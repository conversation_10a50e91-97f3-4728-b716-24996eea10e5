package com.holderzone.holder.saas.aggregation.app.service.feign.trade;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.trade.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR> R
 * @date 2020/12/16 17:20
 * @description
 */
@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = DebtClientService.DebtFallBack.class)
public interface DebtClientService {

    @PostMapping("/debt/unit/page")
    @ApiOperation(value = "分页查询单位列表")
    Page<DebtUnitPageRespDTO> unitPage(@RequestBody DebtUnitPageReqDTO reqDTO);

    @PostMapping("/debt/unit/page_debt_unit_record")
    @ApiOperation(value = "分页查询挂账单位流水")
    Page<DebtUnitRecordPageRespDTO> pageDebtUnitRecord(@Validated @RequestBody DebtUnitRecordPageReqDTO reqDTO);

    @GetMapping("/debt/unit/query_debt_unit_total")
    @ApiOperation(value = "查询挂账单位汇总")
    DebtUnitRecordTotalDTO queryDebtUnitTotal(@RequestParam("unitCode") String unitCode);

    @PostMapping("/debt/unit/update_debt_unit")
    @ApiOperation(value = "挂账管理还款")
    Boolean updateDebtUnit(@RequestBody DebtUnitRecordUpdateReqDTO updateReqDTO);

    @PostMapping("/debt/unit/debt_repayment_record_print")
    @ApiOperation(value = "挂账记录重打印")
    Boolean debtRepaymentRecordPrint(@RequestBody DebtUnitRecordUpdateReqDTO updateReqDTO);

    @Component
    @Slf4j
    class DebtFallBack implements FallbackFactory<DebtClientService> {
        private static final Logger logger = LoggerFactory.getLogger(DebtClientService.DebtFallBack.class);

        @Override
        public DebtClientService create(Throwable throwable) {
            return new DebtClientService() {

                @Override
                public Page<DebtUnitPageRespDTO> unitPage(DebtUnitPageReqDTO reqDTO) {
                    log.error("分页查询单位列表接口FallBack，throwable={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Page<DebtUnitRecordPageRespDTO> pageDebtUnitRecord(DebtUnitRecordPageReqDTO reqDTO) {
                    log.error("分页查询挂账单位流水接口FallBack，throwable={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public DebtUnitRecordTotalDTO queryDebtUnitTotal(String unitCode) {
                    log.error("查询挂账单位汇总接口FallBack，throwable={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean updateDebtUnit(DebtUnitRecordUpdateReqDTO updateReqDTO) {
                    log.error("挂账管理还款接口FallBack，throwable={}", throwable.getMessage());
                    return Boolean.FALSE;
                }

                @Override
                public Boolean debtRepaymentRecordPrint(DebtUnitRecordUpdateReqDTO updateReqDTO) {
                    log.error("挂账还款单打印接口FallBack，throwable={}", throwable.getMessage());
                    return Boolean.FALSE;
                }
            };
        }
    }
}
