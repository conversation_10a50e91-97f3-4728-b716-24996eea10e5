package com.holderzone.holder.saas.aggregation.merchant.controller.erp;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp.SuppliersFeignService;
import com.holderzone.saas.store.dto.erp.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @className SuppliersController
 * @date 2019-05-05 14:08:41
 * @program holder-saas-aggregation-merchant
 */
@Api(tags = "供应商管理")
@RestController
public class SuppliersController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SuppliersController.class);
    private final SuppliersFeignService suppliersFeignService;

    @Autowired
    public SuppliersController(SuppliersFeignService suppliersFeignService) {
        this.suppliersFeignService = suppliersFeignService;
    }

    @ApiOperation(value = "新建供应商")
    @PostMapping("/suppliers/create")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "新建供应商",action = OperatorType.ADD)
    public Result<String> createSuppliers(@RequestBody SuppliersReqDTO reqDTO) {
        LOGGER.info("ERP系统聚合层(新建供应商)-> SuppliersReqDTO:{}", JacksonUtils.writeValueAsString(reqDTO));
        return Result.buildSuccessResult(suppliersFeignService.createSuppliers(reqDTO));
    }

    @ApiOperation(value = "更新供应商")
    @PostMapping("/suppliers/update")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "更新供应商")
    public Result<String> updateSuppliers(@RequestBody SuppliersReqDTO reqDTO) {
        LOGGER.info("ERP系统聚合层(更新供应商)-> SuppliersReqDTO:{}", JacksonUtils.writeValueAsString(reqDTO));
        return Result.buildSuccessResult(suppliersFeignService.updateSuppliers(reqDTO));
    }

    @ApiOperation(value = "查询供应商信息")
    @PostMapping("/suppliers/query/one/{guid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "查询供应商信息",action = OperatorType.SELECT)
    public Result<SuppliersDTO> getSuppliersByGuid(@PathVariable("guid") String guid) {
        LOGGER.info("ERP系统聚合层(查询供应商信息)-> guid:{}", guid);
        return Result.buildSuccessResult(suppliersFeignService.getSuppliersByGuid(guid));
    }

    @ApiOperation(value = "启禁用供应商")
    @PostMapping("/suppliers/enable/{guid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "启禁用供应商")
    public Result<Boolean> enableOrDisableSuppliers(@PathVariable("guid") String guid) {
        LOGGER.info("ERP系统聚合层(启禁用供应商)-> guid:{}", guid);
        return Result.buildSuccessResult(suppliersFeignService.enableOrDisableSuppliers(guid));
    }

    @ApiOperation(value = "删除供应商")
    @PostMapping("/suppliers/delete/{guid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "根据物料GUID列表查询物料信息",action = OperatorType.SELECT)
    public Result<Boolean> deleteSuppliers(@PathVariable("guid") String guid) {
        LOGGER.info("ERP系统聚合层(删除供应商)-> guid:{}", guid);
        return Result.buildSuccessResult(suppliersFeignService.deleteSuppliers(guid));
    }

    @ApiOperation(value = "供应商列表")
    @PostMapping("/suppliers/query/list")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "供应商列表",action = OperatorType.SELECT)
    public Result<Page<SuppliersDTO>> getSuppliersList(@RequestBody SuppliersQueryDTO queryDTO) {
        LOGGER.info("ERP系统聚合层(供应商列表)-> guid:{}", JacksonUtils.writeValueAsString(queryDTO));
        return Result.buildSuccessResult(suppliersFeignService.getSuppliersList(queryDTO));
    }

    @ApiOperation(value = "供应商下拉列表")
    @PostMapping("/suppliers/query/all")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "供应商下拉列表",action = OperatorType.SELECT)
    public Result<List<SuppliersDTO>> getAllOfSuppliersList(@RequestBody SuppliersQueryDTO queryDTO) {
        LOGGER.info("ERP系统聚合层(供应商下拉列表)-> guid:{}", JacksonUtils.writeValueAsString(queryDTO));
        return Result.buildSuccessResult(suppliersFeignService.getAllOfSuppliersList(queryDTO));
    }

}
