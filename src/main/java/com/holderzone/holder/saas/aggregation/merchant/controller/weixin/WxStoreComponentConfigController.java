package com.holderzone.holder.saas.aggregation.merchant.controller.weixin;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin.WxStoreComponentConfigClientService;
import com.holderzone.saas.store.dto.weixin.open.WxOpenAuthDTO;
import com.holderzone.saas.store.dto.weixin.req.WxCommonReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPreCodReqDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxThirdOpenController
 * @date 2019/02/26 11:35
 * @description 微信开放平台聚合层controller
 * @program holder-saas-store-weixin
 */
@RestController
@RequestMapping("/wx_open")
@Api(description = "微信开放平台聚合层controller")
@Slf4j
public class WxStoreComponentConfigController {

    @Autowired
    WxStoreComponentConfigClientService wxStoreComponentConfigClientService;

    @PostMapping("/receive_ticket")
    @ApiOperation(value = "接收微信方推送的component_verify_ticket，验证后直接返回success字符串")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "接收微信方推送的component_verify_ticket，验证后直接返回success字符串")
    public String receiveVerifyTicket(WxCommonReqDTO wxCommonReqDTO, @RequestBody String requestBody) {
        log.info("wxCommonReqDTO:{}", wxCommonReqDTO);
        log.info("requestBody:{}", requestBody);
        wxCommonReqDTO.setXml(requestBody);
        return wxStoreComponentConfigClientService.receiveTicket(wxCommonReqDTO);
    }

    @GetMapping("/query_auth")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "微信聚合层：已收到公众号授权")
    public String callBack(WxOpenAuthDTO wxOpenAuthDTO) {
        log.info("微信聚合层：已收到公众号授权微信回调参数：{}", wxOpenAuthDTO);
        return wxStoreComponentConfigClientService.queryAuth(wxOpenAuthDTO);
    }

    @PostMapping("/get_pre_code")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "接收到微信预授权")
    public Result<String> getPreCode(@RequestBody WxPreCodReqDTO wxPreCodReqDTO) {
        log.info("接收到微信预授权请求参数：{}", JacksonUtils.writeValueAsString(wxPreCodReqDTO));
        return Result.buildSuccessResult(wxStoreComponentConfigClientService.getPreCode(wxPreCodReqDTO));
    }

//    private WxCommonReqDTO requestParam2Obj(Map<String, String[]> param) {
//        if (ObjectUtils.isEmpty(param))
//            return null;
//        WxCommonReqDTO wxCommonReqDTO = new WxCommonReqDTO();
//        wxCommonReqDTO.setTimeStamp(ObjectUtils.isEmpty(param.get("timestamp")) ? null : param.get("timestamp")[0]);
//        wxCommonReqDTO.setEncType(ObjectUtils.isEmpty(param.get("encrypt_type")) ? null : param.get("encrypt_type")[0]);
//        wxCommonReqDTO.setEchoStr(ObjectUtils.isEmpty(param.get("echostr")) ? null : param.get("echostr")[0]);
//        wxCommonReqDTO.setMsgSignature(ObjectUtils.isEmpty(param.get("msg_signature"))
//                ? null : param.get("msg_signature")[0]);
//        wxCommonReqDTO.setNonce(ObjectUtils.isEmpty(param.get("nonce")) ? null : param.get("nonce")[0]);
//        wxCommonReqDTO.setSignature(ObjectUtils.isEmpty(param.get("signature")) ? null : param.get("signature")[0]);
//        return wxCommonReqDTO;
//    }
}
