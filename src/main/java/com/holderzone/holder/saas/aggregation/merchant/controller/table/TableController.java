package com.holderzone.holder.saas.aggregation.merchant.controller.table;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.TableService;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.table.TableBatchCreateDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableController
 * @date 2019/01/07 10:15
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Api(description = "桌台服务Api")
@RestController
@RequestMapping("/table")
public class TableController {

    private static final Logger logger = LoggerFactory.getLogger(TableController.class);

    private final TableService tableService;

    @Autowired
    public TableController(TableService tableService) {
        this.tableService = tableService;
    }

    @ApiOperation("创建桌台")
    @PostMapping("/add")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "创建桌台")
    public Result add(@RequestBody TableBasicDTO tableBasicDTO) {
        logger.info("创建桌台 tableBasicDTO={}", JacksonUtils.writeValueAsString(tableBasicDTO));
        String result = tableService.add(tableBasicDTO);
        if ("success".equalsIgnoreCase(result)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.CREATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.CREATION_FAILED));
    }

    @ApiOperation("更新桌台")
    @PostMapping("/update")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "更新桌台")
    public Result update(@RequestBody TableBasicDTO tableBasicDTO) {
        logger.info("更新桌台信息 tableBasicDTO={}", JacksonUtils.writeValueAsString(tableBasicDTO));
        String result = tableService.updateTable(tableBasicDTO);
        if ("success".equalsIgnoreCase(result)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.UPDATE_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.UPDATE_FAILED));
    }

    @ApiOperation("批量删除桌台，返回空集合表示删除成功，返回有值得集合表示该桌台无法被删除")
    @PostMapping("/deleteAll")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "批量删除桌台，返回空集合表示删除成功，返回有值得集合表示该桌台无法被删除")
    public Result<List<String>> deleteAll(@RequestBody List<String> guids) {
        logger.info("批量删除桌台 guids={}", JacksonUtils.writeValueAsString(guids));
        List<String> list = tableService.deleteAll(guids);
        return Result.buildSuccessResult(list);
    }

    @ApiOperation("删除单个桌台，返回空集合表示删除成功，返回有值得集合表示该桌台无法被删除")
    @PostMapping("/delete")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "删除单个桌台，返回空集合表示删除成功，返回有值得集合表示该桌台无法被删除")
    public Result<List<String>> delete(@RequestBody List<String> guids) {
        logger.info("批量删除桌台 guids={}", JacksonUtils.writeValueAsString(guids));
        List<String> list = tableService.delete(guids);
        return Result.buildSuccessResult(list);
    }

    @ApiOperation("查询桌台")
    @PostMapping("/web/query")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "查询桌台")
    public Result<List<TableBasicDTO>> queryByWeb(@RequestBody TableBasicQueryDTO tableBasicQueryDTO) {
        logger.info("web前端查询桌位信息 tableBasicQueryDTO={}", JacksonUtils.writeValueAsString(tableBasicQueryDTO));
        List<TableBasicDTO> query = tableService.query(tableBasicQueryDTO);
        return Result.buildSuccessResult(query);
    }


    @ApiOperation("批量创建桌台")
    @PostMapping("/batch/create")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "批量创建桌台")
    public Result create(@RequestBody TableBatchCreateDTO tableBatchCreateDTO) {
        logger.info("批量生成桌位 tableBatchCreateDTO={}", JacksonUtils.writeValueAsString(tableBatchCreateDTO));
        String result = tableService.batchCreateTable(tableBatchCreateDTO);
        if ("success".equalsIgnoreCase(result)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.CREATION_SUCCESSFUL));
        } else if (LocaleUtil.getMessage(Constants.TABLE_ALREADY_EXISTS).equals(result)){
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.TABLE_ALREADY_EXISTS));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.CREATION_FAILED));
    }
}
