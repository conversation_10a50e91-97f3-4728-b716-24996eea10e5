package com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.order;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.report.query.ReportExportDTO;
import com.holderzone.saas.store.dto.report.resp.ExportRespDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PayAndDiscountClientService
 * @date 2018/10/11 9:08
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-store-report",fallbackFactory = ReportExportClientService.ReportExportClientFallBack.class)
public interface ReportExportClientService {

    @PostMapping("file/export")
    ExportRespDTO export(@RequestBody ReportExportDTO reportExportDTO);

    @Component
    class ReportExportClientFallBack implements FallbackFactory<ReportExportClientService> {

        private static final Logger logger = LoggerFactory.getLogger(ReportExportClientFallBack.class);
        @Override
        public ReportExportClientService create(Throwable cause) {
            return reportExportDTO -> {
                logger.error("导出报表异常 e={}", cause.getMessage());
                cause.printStackTrace();
                throw new BusinessException("导出报表异常！");
            };
        }
    }
}
