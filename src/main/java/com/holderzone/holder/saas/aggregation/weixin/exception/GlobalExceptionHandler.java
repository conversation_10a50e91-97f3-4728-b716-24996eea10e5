package com.holderzone.holder.saas.aggregation.weixin.exception;

import com.holderzone.feign.spring.boot.core.RawJsonException;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.response.ResultEnum;
import com.holderzone.holder.dingding.handler.DingWarningHandler;
import com.holderzone.holder.saas.aggregation.weixin.utils.StringMessageUtils;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.netflix.hystrix.exception.HystrixRuntimeException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GlobalExceptionHandler
 * @date 2018/09/14 16:01
 * @description 全局异常拦截
 * @program holder-saas-aggregation-app
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    @Resource
    private DingWarningHandler dingWarningHandler;

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);


    @ExceptionHandler(value = BusinessException.class)
    public Result interceptBusinessException(BusinessException e) {
        logger.warn("BusinessException_异常", e);
        Result<Map<String, String>> warnMsg = Result.buildSuccessResult(
                Collections.singletonMap("warnMsg", e.getMessage())
        );
        warnMsg.setCode(809);
        return warnMsg;
    }


    @ExceptionHandler(value = Exception.class)
    public Result<String> exception(Exception e) {
        String message = null;

        if (e instanceof HystrixRuntimeException) {
            Throwable cause = e.getCause();
            if (cause != null) {
                message = cause.getMessage();
                //message = hanldReminderMessage(message);
                logger.warn("服务调用异常,{}", message, cause);
            }
        } else {
            message = e.getMessage();
            logger.warn("系统异常,{}", message, e);
        }
        String checkChines = null;
        assert message != null;
        if (message.length() > 20) {
            checkChines = message.substring(0, 20);
        } else {
            checkChines = message;
        }
        boolean containChinese = StringMessageUtils.isContainChinese(checkChines);
        if (containChinese) {
            String hanldReminderMessage = hanldReminderMessage(message);
            return Result.buildIllegalArgumentResult(hanldReminderMessage);
        }
        return Result.buildIllegalArgumentResult(BusinessName.GLOBAL_DEFAULT_ERROR_MSG);

    }

    private String hanldReminderMessage(String message) {
        if (message.contains("商家短信不足")) {
            return "商家短信不足,请联系商家处理";
        }
        return message;
    }


    @ExceptionHandler(value = RawJsonException.class)
    protected Result interceptRawJsonException(RawJsonException e) {
        String message = e.getMessage();
        if (message.length() >= 10
                && "406".equals(message.substring(7, 10))
                && message.contains("设备已解绑，请重新绑定门店")) {
            return Result.buildFailResult(
                    ResultEnum.NO_SUCH_APPLICATION_EXISTS.getResultCode(),
                    "设备已解绑，请重新绑定门店"
            );
        }
        return Result.buildIllegalArgumentResult(BusinessName.GLOBAL_DEFAULT_ERROR_MSG);
    }


}
