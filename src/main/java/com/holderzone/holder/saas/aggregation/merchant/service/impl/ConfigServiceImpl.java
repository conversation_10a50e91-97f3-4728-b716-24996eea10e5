package com.holderzone.holder.saas.aggregation.merchant.service.impl;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.aggregation.merchant.service.ConfigService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.config.ConfigClientService;
import com.holderzone.saas.store.dto.config.req.*;
import com.holderzone.saas.store.dto.config.resp.ConfigRespDTO;
import com.holderzone.saas.store.dto.config.resp.EstimateConfigRespDTO;
import com.holderzone.saas.store.enums.common.ConfigEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ConfigServiceImpl
 * @date 2019/05/15 18:06
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Service
public class ConfigServiceImpl implements ConfigService {

    @Autowired
    ConfigClientService configClientService;

    @Override
    public Integer saveEstimateResetTime(EstimateConfigReqDTO request) {
        String guid = Optional.ofNullable(request).map(EstimateConfigReqDTO::getGuid).isPresent() ? request.getGuid() : null;
        if (!Optional.ofNullable(request).map(EstimateConfigReqDTO::getResetTime).isPresent()) {
            request.setResetTime("05:00");
        }
        ConfigReqDTO configReqDTO = ConfigReqDTO.builder()
                .dicCode(ConfigEnum.ESTIMATE_RECOVERY_TIME.getCode())
                .dicName(ConfigEnum.ESTIMATE_RECOVERY_TIME.getDesc())
                .dictValue(request.getResetTime())
                .storeGuid(request.getStoreGuid())
                .guid(guid).build();
        return configClientService.saveEstimateResetTime(configReqDTO);
    }

    @Override
    public EstimateConfigRespDTO selectEstimateResetTime(ConfigReqQueryDTO request) {
        request.setDicCode(ConfigEnum.ESTIMATE_RECOVERY_TIME.getCode());
        ConfigRespDTO configRespDTO = configClientService.selectEstimateResetTime(request);
        if (ObjectUtils.isEmpty(configRespDTO)) {
            return EstimateConfigRespDTO.builder()
                    .enterpriseGuid(UserContextUtils.getEnterpriseGuid())
                    .storeGuid(request.getStoreGuid()).build();
        } else {
            return EstimateConfigRespDTO.builder()
                    .enterpriseGuid(configRespDTO.getEnterpriseGuid())
                    .guid(configRespDTO.getGuid())
                    .resetTime(configRespDTO.getDictValue())
                    .storeGuid(configRespDTO.getStoreGuid()).build();
        }

    }

    @Override
    public void saveReservePhone(ReservePhoneReqDTO request) {
        if (!StringUtils.hasText(request.getPhone())) {
            configClientService.deleteConfig(ConfigReqDTO.builder()
                    .dicCode(ConfigEnum.RESERVE_MERCHANT_PHONE.getCode())
                    .storeGuid(request.getStoreGuid()).build());
            return;
        }
        ConfigReverseQueryDTO configReverseQueryDTO = new ConfigReverseQueryDTO();
        configReverseQueryDTO.setDicCode(ConfigEnum.RESERVE_MERCHANT_PHONE.getCode());
        configReverseQueryDTO.setDicValue(request.getPhone());
        ConfigRespDTO reservePhone = configClientService.getConfigByCodeValue(configReverseQueryDTO);
        if (reservePhone != null && !reservePhone.getStoreGuid().equalsIgnoreCase(request.getStoreGuid())) {
            throw new BusinessException("云呼预订电话号码已被其他商户使用！");
        }
        ConfigReqQueryDTO configReqQueryDTO = new ConfigReqQueryDTO();
        configReqQueryDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        configReqQueryDTO.setStoreGuid(request.getStoreGuid());
        configReqQueryDTO.setDicCode(ConfigEnum.RESERVE_MERCHANT_PHONE.getCode());
        reservePhone = configClientService.getConfigByCode(configReqQueryDTO);
        ConfigReqDTO configReqDTO = ConfigReqDTO.builder()
                .dicCode(ConfigEnum.RESERVE_MERCHANT_PHONE.getCode())
                .dicName(ConfigEnum.RESERVE_MERCHANT_PHONE.getDesc())
                .dictValue(request.getPhone())
                .storeGuid(request.getStoreGuid())
                .guid(Optional.ofNullable(reservePhone)
                        .map(ConfigRespDTO::getGuid).orElse(null))
                .build();
        configClientService.saveConfig(configReqDTO);
    }

    @Override
    public String queryReservePhone(ReservePhoneReqDTO reqDTO) {
        ConfigReqQueryDTO configReqQueryDTO = new ConfigReqQueryDTO();
        configReqQueryDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        configReqQueryDTO.setStoreGuid(reqDTO.getStoreGuid());
        configReqQueryDTO.setDicCode(ConfigEnum.RESERVE_MERCHANT_PHONE.getCode());
        ConfigRespDTO reservePhone = configClientService.getConfigByCode(configReqQueryDTO);
        return Optional.ofNullable(reservePhone).map(ConfigRespDTO::getDictValue).orElse(null);
    }
}
