package com.holderzone.holder.saas.aggregation.app.controller.print;


import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;

import com.holderzone.holder.saas.aggregation.app.manage.PrintManager;

import com.holderzone.holder.saas.aggregation.app.service.feign.trade.TradeItemClientService;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.print.ListPrintItemReq;
import com.holderzone.saas.store.dto.print.PrintLabelReq;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 打印相关接口
 */
@RestController
@RequestMapping("/print")
@Slf4j
@Validated
public class PrintController {
    private final PrintManager printManager;
    private final TradeItemClientService tradeItemClient;

    @Autowired
    public PrintController(PrintManager printManager, TradeItemClientService tradeItemClient) {
        this.printManager = printManager;
        this.tradeItemClient = tradeItemClient;
    }


    /**
     * 查询指定订单已绑定标签商品
     *
     * @param printItemsReq 请求
     * @return 打印商品列表
     */
    @PostMapping("/list_print_items_by_orderid")
    public Result<DineinOrderDetailRespDTO> listPrintItemsByOrderGuid(@RequestBody @Validated ListPrintItemReq printItemsReq) {
        log.info("查询指定订单已绑定标签商品请求参数,printItemsReq:{}", JacksonUtils.writeValueAsString(printItemsReq));
        return Result.buildSuccessResult(printManager.listPrintItemsByOrderGuid(printItemsReq));
    }


    /**
     * 重打印标签
     *
     * @param printLabelReq 请求
     */
    @ApiOperation(value = "重打印标签")
    @PostMapping("/label")
    public Result<Void> printLabel(@RequestBody @Validated PrintLabelReq printLabelReq) {
        log.info("重打印标签请求参数,printLabelReq:{}", JacksonUtils.writeValueAsString(printLabelReq));
        tradeItemClient.reprintLabel(printLabelReq);
        return Result.buildEmptySuccess();
    }
}