package com.holderzone.holder.saas.aggregation.merchant.controller.config;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.ConfigService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.config.ConfigClientService;
import com.holderzone.saas.store.dto.config.req.ConfigReqDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReqQueryDTO;
import com.holderzone.saas.store.dto.config.req.EstimateConfigReqDTO;
import com.holderzone.saas.store.dto.config.req.ReservePhoneReqDTO;
import com.holderzone.saas.store.dto.config.resp.EstimateConfigRespDTO;
import com.holderzone.saas.store.enums.common.ConfigEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ConfigController
 * @date 2019/05/14 14:53
 * @description //TODO
 * @program holder-saas-aggregation-merchant
 */
@RestController
@RequestMapping("/config")
@Api(description = "门店配置接口")
@Slf4j
public class ConfigController {

    @Autowired
    ConfigService configService;

    @Autowired
    ConfigClientService clientService;

    /**
     * 商户后台菜品设置估清
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "门店配置估清置满时间")
    @PostMapping("/save_estimate_reset_time")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_CONFIG, description = "门店配置估清置满时间")
    public Result saveEstimateResetTime(@RequestBody @Valid EstimateConfigReqDTO request) {
        log.info("估清新增or更新接口入参,request={}", JacksonUtils.writeValueAsString(request));
        Integer num = configService.saveEstimateResetTime(request);
        return Integer.valueOf(1).equals(num) ? Result.buildEmptySuccess() : Result.buildOpFailedResult("保存/更新失败");
    }

    /**
     * 商户后台菜品设置估清
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "查询门店配置估清置满时间")
    @PostMapping("/select_estimate_reset_time")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_CONFIG, description = "查询门店配置估清置满时间")
    public Result<EstimateConfigRespDTO> selectEstimateResetTime(@RequestBody @Valid ConfigReqQueryDTO request) {
        log.info("查询门店估清置满时间接口入参,request={}", JacksonUtils.writeValueAsString(request));
        request.setDicCode(ConfigEnum.ESTIMATE_RECOVERY_TIME.getCode());
        EstimateConfigRespDTO estimateConfigRespDTO = configService.selectEstimateResetTime(request);
        return Result.buildSuccessResult(estimateConfigRespDTO);
    }

    @PostMapping("/test")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_CONFIG)
    public void test(@RequestBody @Valid ConfigReqDTO request) {
        log.info("result  = {}", JacksonUtils.writeValueAsString(clientService.getConfig(request)));
    }

    @ApiOperation(value = "保存云呼电话号码")
    @PostMapping("/save_reserve_phone")
    @EFKOperationLogAop(
            PLATFORM = Platform.MERCHANTBACK,
            moduleName = ModuleNameType.HOLDER_SAAS_STORE_CONFIG,
            description = "保存云呼电话号码"
    )
    public Result saveReservePhone(@RequestBody @Valid ReservePhoneReqDTO request) {
        log.info("保存云呼电话号码，接口入参：{}", JacksonUtils.writeValueAsString(request));
        configService.saveReservePhone(request);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "查询云呼电话号码")
    @PostMapping("/query_reserve_phone")
    @EFKOperationLogAop(
            PLATFORM = Platform.MERCHANTBACK,
            moduleName = ModuleNameType.HOLDER_SAAS_STORE_CONFIG,
            description = "查询云呼电话号码"
    )
    public Result<String> queryReservePhone(@RequestBody @Valid ReservePhoneReqDTO request) {
        log.info("查询云呼电话号码，接口入参：{}", JacksonUtils.writeValueAsString(request));
        return Result.buildSuccessResult(configService.queryReservePhone(request));
    }
}
