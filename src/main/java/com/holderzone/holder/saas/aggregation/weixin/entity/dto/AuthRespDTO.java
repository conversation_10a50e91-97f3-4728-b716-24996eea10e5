package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("msgkey获取")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AuthRespDTO {

    @ApiModelProperty(value = "token",required = true)
    String token;
}
