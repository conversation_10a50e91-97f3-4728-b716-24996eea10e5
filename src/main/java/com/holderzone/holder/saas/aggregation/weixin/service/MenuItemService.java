package com.holderzone.holder.saas.aggregation.weixin.service;

import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.WxSearchItemDto;
import com.holderzone.saas.store.dto.weixin.PricePairDTO;
import com.holderzone.saas.store.dto.weixin.deal.*;
import com.holderzone.saas.store.dto.weixin.req.ClearCartItemReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.StoreActivityRespDTO;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MenuItemService
 * @date 2019/11/7
 * 首页及确认页业务逻辑
 */
public interface MenuItemService {

    /**
     * 查询扫码点餐菜谱变动情况
     *
     * @param pricePlanChangeRequestDTO 变动请求
     * @return PricePlanChangeResponseDTO
     */
    PricePlanChangeResponseDTO pricePlanChangeInfo(PricePlanChangeRequestDTO pricePlanChangeRequestDTO);

    /**
     * 查询门店配置，商品
     *
     * @return 门店配置，商品
     */
    MenuInfoAllDTO getMenuInfoAll(WxMemberSessionDTO wxMemberSessionDTO, Integer wxPermission,
                                  Integer wxAddItemFlag, Integer h5flag, Integer nthFlag) throws ExecutionException, InterruptedException;

    /**
     * 查询门店配置
     */
    MenuInfoAllDTO getStoreConfig(Integer wxAddItemFlag, Integer isLogin);

    /**
     * 赚餐扫描点餐关键字搜索门店商品
     *
     * @return 关键字
     */
    Page<ItemInfoDTO> searchItems(WxSearchItemDto dto) throws ExecutionException, InterruptedException;

    /**
     * 购物车加菜
     *
     * @param shopCartItemReqDTO 商品项
     * @return 购物车
     */
    ShopCartItemAddRespDTO itemModify(ShopCartItemReqDTO shopCartItemReqDTO);

    /**
     * 查询购物车
     *
     * @return dto
    //     */
    ShopCartRespDTO getMenuItemList(Integer h5flag);

    /**
     * 查询购物车 直接返回缓存数据
     */
    List<ShopCartItemReqDTO> getCartItemList();

    void calculateMemberDiscount(ShopCartRespDTO shopCartRespDTO,
                                 List<ShopCartItemReqDTO> cartItemList);

    /**
     * 商品价格计算
     */
    PricePairDTO itemPrice(ItemInfoDTO itemInfoDTO);

    List<UserMemberCardCacheDTO> cardList();

    Boolean switchCard(String memberInfoCardGuid);

    Boolean clearShopCart();

    PreOrderRespDTO preOrderList(Integer wxAddItemFlag);

    /**
     * @param openid 用户id
     * @return 删除用户预订单
     */
    PreOrderRespDTO delUserPreOrder(String openid);

    /**
     * @return 购物车校验
     */
    SubmitShopCartRespDTO submitShopCart();

    void sendShopCartMessage();

    void saveQrCodeTypeByToken(String wxToken, Integer qrCodeType);

    /**
     * 根据商品guid批量清除购物车里的指定商品
     *
     * @param request 商品guidList
     * @return boolean
     */
    Boolean clearItemsOnShopCart(ClearCartItemReqDTO request);

    /**
     * 根据商品guid查询商品详情
     *
     * @param itemSingleDTO 商品guid放在data里就行
     * @return 商品详情
     */
    ItemInfoDTO selectItemInfo(ItemSingleDTO itemSingleDTO);

    /**
     * 获取点餐人数
     */
    Integer queryGuestCount();

    List<StoreActivityRespDTO> queryStoreActivity();

    /**
     * 下单前置处理
     */
    OrderSubmitRespDTO preSubmit(OrderSubmitReqDTO orderSubmitReqDTO);

    /**
     * 校验是否必点分类商品
     */
    CheckMustItemRespDTO checkMustItem(OrderMustPointCheckDTO orderMustPointCheckDTO);
}
