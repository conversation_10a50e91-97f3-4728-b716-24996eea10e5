package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@ApiModel("预订下单返回")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ReserveSubmitRespDTO {

	@ApiModelProperty("1:成功，0：失败")
	private Integer code = 0;

	@ApiModelProperty("失败原因")
	private String errorMsg;
}
