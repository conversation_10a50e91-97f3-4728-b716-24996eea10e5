package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.order.inside.OrderGuidsDTO;
import com.holderzone.saas.store.dto.order.request.dinein.DineInOrderListReqDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.trade.OrderDetailPushMqDTO;
import com.holderzone.saas.store.dto.trade.OrderInfoRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = TradeOrderService.TradeOrderFallBack.class)
public interface TradeOrderService {

    @ApiOperation(value = "检查是否存在未结账的订单，true存在，false不存在", notes = "检查是否存在未结账的订单")
    @GetMapping("/dine_in_order/checkOrder/pending/{memberInfoGuid}")
    boolean checkOrderPending(@PathVariable("memberInfoGuid") String memberInfoGuid);

    @ApiOperation(value = "获取订单详情", notes = "获取订单详情")
    @GetMapping("/order_detail/find_by_order_guid")
    OrderDTO findByOrderGuid(@RequestParam("orderGuid") String orderGuid);

    @ApiOperation(value = "微信小程序获取订单列表", notes = "微信小程序获取订单列表")
    @PostMapping("/order_detail/wx/order_list")
    Page<OrderInfoRespDTO> pageAppletOrderInfo(@RequestBody DineInOrderListReqDTO reqDTO);

    @ApiOperation(value = "获取微信订单详情", notes = "获取微信订单详情")
    @PostMapping("/order_detail/find_by_wx_order_guid")
    OrderDetailPushMqDTO findByAppletOrderGuid(@RequestBody OrderGuidsDTO orderGuidsDTO);

    @Component
    @Slf4j
    class TradeOrderFallBack implements FallbackFactory<TradeOrderService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public TradeOrderService create(Throwable throwable) {
            return new TradeOrderService() {
                @Override
                public boolean checkOrderPending(String memberInfoGuid) {
                    log.error("checkOrderPending 异常,memberInfoGuid:{},error:{}", memberInfoGuid, throwable.getMessage());
                    throw new BusinessException("获取用户未结账订单错误");
                    //return false;
                }

                @Override
                public OrderDTO findByOrderGuid(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "findByOrderGuid", orderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<OrderInfoRespDTO> pageAppletOrderInfo(DineInOrderListReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "pageAppletOrderInfo", reqDTO,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public OrderDetailPushMqDTO findByAppletOrderGuid(OrderGuidsDTO orderGuidsDTO) {
                    log.error(HYSTRIX_PATTERN, "findByAppletOrderGuid", orderGuidsDTO,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
