package com.holderzone.holder.saas.aggregation.merchant.controller.journaling;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.controller.report.ReportExportController;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.journaling.ReportClientService;
import com.holderzone.holder.saas.aggregation.merchant.util.DownloadExcelUtils;
import com.holderzone.saas.store.dto.journaling.eum.JournalExportTypeEnum;
import com.holderzone.saas.store.dto.journaling.req.OrderDetailReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.OrderDetailRespDTO;
import com.holderzone.saas.store.dto.report.query.ReportExportDTO;
import com.holderzone.saas.store.dto.report.resp.ExportPayRespDTO;
import com.holderzone.saas.store.dto.report.resp.ExportRespDTO;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.holderzone.holder.saas.aggregation.merchant.util.ReportUtils.getParamString;

/**
 * <AUTHOR>
 * @version 1.0
 * @className JournalingExportController
 * @date 2019/06/06 9:48
 * @description 商户后台1.2报表导出
 * @program holder-saas-aggregation-merchant
 */
@Controller
@RequestMapping("/journal")
public class JournalingExportController {

    private static final Logger logger = LoggerFactory.getLogger(ReportExportController.class);
    private ReportClientService reportClientService;

    @Autowired
    public JournalingExportController(ReportClientService reportClientService) {
        this.reportClientService = reportClientService;
    }

    @ApiOperation(value = "exportType类型对应不同的导出接口 详见文档")
    @PostMapping("/export/{exportType}")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT, description = "exportType类型对应不同的导出接口")
    public void export(@PathVariable("exportType") Integer exportType, HttpServletRequest request, HttpServletResponse response) {
        try {
            String paramString = getParamString(request);
            logger.info("报表导出 exportType={},paramString={}", exportType, paramString);
            if (Objects.equals(6, exportType)) {
                ExportPayRespDTO exportPayRespDTO = reportClientService.payExport(new ReportExportDTO(exportType, paramString));
                DownloadExcelUtils.fullPayExcel(response, exportPayRespDTO);
            } else if (Objects.equals(1, exportType)) {
                OrderDetailReqDTO orderDetailReqDTO = (OrderDetailReqDTO) JacksonUtils.toObject(JournalExportTypeEnum.ORDER_DETAIL_TYPE.getQueryClzz(), paramString);
                Map<String, String> paymentMap = reportClientService.getPaymentAll(orderDetailReqDTO);
                orderDetailReqDTO.setPageSize(Integer.MAX_VALUE);
                orderDetailReqDTO.setCurrentPage(1);
                List<OrderDetailRespDTO> respDTOPage = reportClientService.exportOrderDetail(orderDetailReqDTO);
                DownloadExcelUtils.orderDetailExcel(response, JournalExportTypeEnum.ORDER_DETAIL_TYPE.getName(), JournalExportTypeEnum.ORDER_DETAIL_TYPE.getHead(), respDTOPage, paymentMap);
            } else {
                ExportRespDTO export = reportClientService.export(new ReportExportDTO(exportType, paramString));
                logger.info("export={}", JacksonUtils.writeValueAsString(export));
                DownloadExcelUtils.fullExcel(response, export.getExcelName(), export.getHead(), export.getList(), export.getClzz(), export.getHeight());
            }
        } catch (Exception e) {
            logger.error("报表导出异常", e);
        }
    }

//    private ExportPayRespDTO createDefaultResp() {
//        ExportPayRespDTO<PaySerialStatisticsRespDTO> respDTO = new ExportPayRespDTO<>();
//
//        PaySerialStatisticsRespDTO paySerialStatisticsRespDTO = new PaySerialStatisticsRespDTO();
//        paySerialStatisticsRespDTO.setPayWay("asdasd");
//        paySerialStatisticsRespDTO.setConsumeAmount(new BigDecimal("12"));
//        paySerialStatisticsRespDTO.setConsumeCount(15);
//        paySerialStatisticsRespDTO.setStoredAmount(new BigDecimal("12"));
//        paySerialStatisticsRespDTO.setStoredCount(15);
//        paySerialStatisticsRespDTO.setPreOrderAmount(new BigDecimal("12"));
//        paySerialStatisticsRespDTO.setPreOrderCount(15);
//        paySerialStatisticsRespDTO.setRefundAmount(new BigDecimal("12"));
//        paySerialStatisticsRespDTO.setRefundCount(15);
//        paySerialStatisticsRespDTO.setActualInOut(new BigDecimal("12"));
//
//        ExportRespDTO<PaySerialStatisticsRespDTO> exportRespDTO = new ExportRespDTO<>();
//        exportRespDTO.setClzz(PaySerialStatisticsRespDTO.class);
//        exportRespDTO.setList(Collections.singletonList(paySerialStatisticsRespDTO));
//        exportRespDTO.setHead(JournalExportTypeEnum.getHeadByType(6));
//        exportRespDTO.setExcelName(JournalExportTypeEnum.PAY_SERIAL_TYPE.getName());
//
//        respDTO.setExportRespDTO(exportRespDTO);
//        respDTO.setConsumeSubtotal("消费金额小计");
//        respDTO.setStoredAmountSubtotal("储值金额小计");
//        respDTO.setPreOrderAmountSubtotal("预定金额小计");
//        respDTO.setRefundSubtotal("退款金额小计");
//        respDTO.setInoutSubtotal("收支金额小计");
//        respDTO.setCspTotal("消费、储值、预定金额总计");
//        respDTO.setRefundTotal("退款金额总计");
//        respDTO.setInoutTotal("收支金额总计");
//
//        return respDTO;
//    }
}
