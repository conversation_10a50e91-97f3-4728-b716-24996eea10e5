package com.holderzone.holder.saas.aggregation.merchant.service.rpc.manage;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.business.manage.*;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(name = "holder-saas-store-business", fallbackFactory = SurchargeClientService.AdditionalFallBack.class)
public interface SurchargeClientService {

    @PostMapping("/surcharge/create")
    String create(@RequestBody @Validated SurchargeCreateDTO surchargeCreateDTO);

    @PostMapping("/surcharge/query")
    SurchargeDTO query(@RequestBody @Validated SurchargeQueryDTO surchargeQueryDTO);

    @PostMapping("/surcharge/update")
    void update(@RequestBody @Validated SurchargeUpdateDTO surchargeUpdateDTO);

    @PostMapping("/surcharge/enable")
    void enable(@RequestBody @Validated SurchargeEnableDTO surchargeEnableDTO);

    @PostMapping("/surcharge/delete")
    void delete(@RequestBody @Validated SurchargeDeleteDTO surchargeDeleteDTO);

    @PostMapping("/surcharge/batch_enable")
    void batchEnable(@RequestBody @Validated SurchargeBatchEnableDTO surchargeBatchEnableDTO);

    @PostMapping("/surcharge/batch_delete")
    void batchDelete(@RequestBody @Validated SurchargeBatchDeleteDTO surchargeBatchDeleteDTO);

    @PostMapping("/surcharge/list_by_type")
    Page<SurchargeDTO> listByType(@RequestBody @Validated SurchargeListDTO surchargeListDTO);

    @Slf4j
    @Component
    class AdditionalFallBack implements FallbackFactory<SurchargeClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";
        private static final Logger logger = LoggerFactory.getLogger(SurchargeClientService.AdditionalFallBack.class);

        @Override
        public SurchargeClientService create(Throwable cause) {
            return new SurchargeClientService() {

                @Override
                public String create(SurchargeCreateDTO surchargeCreateDTO) {
                    log.error(HYSTRIX_PATTERN, "create", JacksonUtils.writeValueAsString(surchargeCreateDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public SurchargeDTO query(SurchargeQueryDTO surchargeQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "single", JacksonUtils.writeValueAsString(surchargeQueryDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void enable(SurchargeEnableDTO surchargeEnableDTO) {
                    log.error(HYSTRIX_PATTERN, "enable", JacksonUtils.writeValueAsString(surchargeEnableDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void update(SurchargeUpdateDTO surchargeUpdateDTO) {
                    log.error(HYSTRIX_PATTERN, "update", JacksonUtils.writeValueAsString(surchargeUpdateDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void delete(SurchargeDeleteDTO surchargeDeleteDTO) {
                    log.error(HYSTRIX_PATTERN, "delete", JacksonUtils.writeValueAsString(surchargeDeleteDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void batchEnable(SurchargeBatchEnableDTO surchargeBatchEnableDTO) {
                    log.error(HYSTRIX_PATTERN, "batchEnable", JacksonUtils.writeValueAsString(surchargeBatchEnableDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void batchDelete(SurchargeBatchDeleteDTO surchargeBatchDeleteDTO) {
                    log.error(HYSTRIX_PATTERN, "batchDelete", JacksonUtils.writeValueAsString(surchargeBatchDeleteDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Page<SurchargeDTO> listByType(SurchargeListDTO surchargeListDTO) {
                    log.error(HYSTRIX_PATTERN, "listByFeeType", JacksonUtils.writeValueAsString(surchargeListDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

            };
        }
    }

}
