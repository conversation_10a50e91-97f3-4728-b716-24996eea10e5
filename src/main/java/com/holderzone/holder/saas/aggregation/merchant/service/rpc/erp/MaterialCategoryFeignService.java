package com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/05 11:17
 */
@FeignClient("holder-saas-store-erp")
public interface MaterialCategoryFeignService {
    /**
     * 添加物料分类
     */
    @PostMapping("materialCategory")
    Boolean add(@RequestBody MaterialCategoryDTO materialCategoryDTO);

    /**
     * 添加物料分类
     */
    @PutMapping("materialCategory")
    Boolean update(@RequestBody MaterialCategoryDTO materialCategoryDTO);

    /**
     * 根据GUID查询分类信息
     */
    @GetMapping("materialCategory/{guid}")
    MaterialCategoryDTO findByGuid(@PathVariable("guid") String guid);

    /**
     * 条件查询分类信息列表,分页
     */
    @PostMapping("materialCategory/findByCondition")
    Page<MaterialCategoryDTO> findByCondition(@RequestBody MaterialCategoryQueryDTO queryDTO);

    /**
     * 查询所有的分类信息,不包括物料
     */
    @PostMapping("materialCategory/list")
    List<MaterialCategoryDTO> findList(@RequestBody CategoryListQueryDTO categoryListQueryDTO);

    /**
     * 统计某分类下的物料使用数量
     */
    @GetMapping("materialCategory/countCategory/{guid}")
    Long countCategory(@PathVariable("guid") String guid);

    /**
     * 查询所有的分类物料信息
     */
    @PostMapping("materialCategory/listCategory")
    List<CategoryDTO> findCategoryAndMaterial(@RequestBody CategoryListQueryDTO categoryListQueryDTO);

    /**
     * 删除物料分类
     */
    @DeleteMapping("materialCategory/{guid}")
    Boolean delete(@PathVariable("guid") String guid);
}
