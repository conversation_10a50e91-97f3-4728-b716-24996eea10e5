package com.holderzone.holder.saas.aggregation.merchant.service.rpc.report;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.store.store.ChildOrganization;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreInfoClientService
 * @date 2018/10/16 18:09
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-store-report", fallbackFactory = StoreInfoClientService.StoreInfoFallBack.class)
public interface StoreInfoClientService {

    @PostMapping("/store/info")
    List<ChildOrganization> getStoreInfo();

    @Component
    class StoreInfoFallBack implements FallbackFactory<StoreInfoClientService> {

        private static final Logger LOGGER = LoggerFactory.getLogger(StoreInfoFallBack.class);

        @Override
        public StoreInfoClientService create(Throwable throwable) {
            return new StoreInfoClientService() {
                @Override
                public List<ChildOrganization> getStoreInfo() {
                    LOGGER.error("门店信息熔断userGuid");
                    throw new BusinessException("门店信息熔断！！");
                }
            };
        }
    }

}
