package com.holderzone.holder.saas.aggregation.merchant.service.rpc.report;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.report.openapi.MemberFundingDetailLimitRespDTO;
import com.holderzone.saas.store.dto.report.openapi.MemberFundingDetailQueryDTO;
import com.holderzone.saas.store.dto.report.openapi.SaleDetailLimitRespDTO;
import com.holderzone.saas.store.dto.report.openapi.SaleDetailQueryDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@Component
@FeignClient(name = "holder-saas-store-report", fallbackFactory = OpenReportClientService.ReportClientFallBack.class)
public interface OpenReportClientService {

    @ApiOperation(value = "开放接口 查询销售明细")
    @PostMapping("/openapi/sale_detail")
    SaleDetailLimitRespDTO querySaleDetail(@RequestBody SaleDetailQueryDTO query);

    @ApiOperation(value = "开放接口 查询会员资金变动明细")
    @PostMapping("/openapi/member/funding_detail")
    MemberFundingDetailLimitRespDTO queryMemberFundingDetail(@RequestBody MemberFundingDetailQueryDTO query);

    @Slf4j
    @Component
    class ReportClientFallBack implements FallbackFactory<OpenReportClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public OpenReportClientService create(Throwable throwable) {

            return new OpenReportClientService() {

                @Override
                public SaleDetailLimitRespDTO querySaleDetail(SaleDetailQueryDTO query) {
                    log.error(HYSTRIX_PATTERN, "querySaleDetail", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public MemberFundingDetailLimitRespDTO queryMemberFundingDetail(MemberFundingDetailQueryDTO query) {
                    log.error(HYSTRIX_PATTERN, "queryMemberFundingDetail", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

            };
        }
    }

}
