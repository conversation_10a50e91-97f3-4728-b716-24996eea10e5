package com.holderzone.holder.saas.aggregation.app.service.feign.trade;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.order.request.groupon.GrouponReqDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GrouponClientService
 * @date 2018/09/06 15:02
 * @description 正餐订单远程调用
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = GrouponClientService.FastFoodFallBack.class)
public interface GrouponClientService {

    @PostMapping("/groupon/verify")
    List<GrouponListRespDTO> verify(GrouponReqDTO grouponReqDTO);

    @PostMapping("/groupon/undo")
    void undo(GrouponReqDTO grouponReqDTO);

    @GetMapping("/groupon/list")
    List<GrouponListRespDTO> list(@RequestParam("orderGuid") String orderGuid,
                                  @RequestParam(value = "grouponType", required = false) Integer grouponType);

    @Component
    @Slf4j
    class FastFoodFallBack implements FallbackFactory<GrouponClientService> {

        @Override
        public GrouponClientService create(Throwable throwable) {
            return new GrouponClientService() {

                @Override
                public void undo(GrouponReqDTO grouponReqDTO) {
                    log.error("撤销验券FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("撤销验券失败!" + throwable.getMessage());
                }

                @Override
                public List<GrouponListRespDTO> verify(GrouponReqDTO grouponReqDTO) {
                    log.error("验券FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("验券失败!" + throwable.getMessage());
                }

                @Override
                public List<GrouponListRespDTO> list(String orderGuid, Integer grouponType) {
                    log.error("查询已验券列表FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("查询已验券列表失败!" + throwable.getMessage());
                }

            };
        }
    }
}
