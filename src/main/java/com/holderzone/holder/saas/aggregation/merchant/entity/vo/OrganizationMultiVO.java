package com.holderzone.holder.saas.aggregation.merchant.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/7/8 10:47
 * @description 运营主体VO
 */
@Data
public class OrganizationMultiVO {
    @ApiModelProperty("运营主体guid")
    private String multiMemberGuid;
    @ApiModelProperty("运营主体名称")
    private String multiMemberName;
    @ApiModelProperty("是否为联盟企业")
    private Boolean isManagementModel;
}
