package com.holderzone.holder.saas.aggregation.merchant.aop;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.StoreParserService;
import com.holderzone.saas.store.dto.organization.StoreParserBaseDTO;
import com.holderzone.saas.store.dto.organization.StoreParserBasePageDTO;
import com.holderzone.saas.store.dto.organization.StoreParserDTO;
import com.holderzone.saas.store.dto.organization.StoreParserPageDTO;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ControllerAspect
 * @date 2018/10/16 上午10:56
 * @description //controller切面
 * @program holder-saas-aggregation-merchant
 */
@Aspect
@Component
@Order(2)
public class StoreParserAspect {

    private Logger logger = LoggerFactory.getLogger(StoreParserAspect.class);

    private final StoreParserService storeParserService;

    @Autowired
    public StoreParserAspect(StoreParserService storeParserService) {
        this.storeParserService = storeParserService;
    }

    @Pointcut("execution(* com.holderzone.holder.saas.aggregation.merchant.controller.*.*.*(..))")
    public void pointCut() {
    }

    @Before("pointCut()")
    public void doBefore(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        if (args != null && args.length > 0) {
            for (Object arg : args) {
                List<String> storeGuidList = new ArrayList<>();
                if (arg instanceof StoreParserDTO) {
                    StoreParserDTO storeParserDTO = (StoreParserDTO) arg;
                    storeParserService.parseByCondition(storeParserDTO);
                    storeGuidList.addAll(storeParserDTO.getStoreGuidList());
                } else if (arg instanceof StoreParserBaseDTO) {
                    StoreParserBaseDTO storeParserBaseDTO = (StoreParserBaseDTO) arg;
                    storeParserService.parseByCondition(storeParserBaseDTO);
                    storeGuidList.addAll(storeParserBaseDTO.getStoreGuidList());
                } else if (arg instanceof StoreParserPageDTO) {
                    StoreParserPageDTO storeParserPageDTO = (StoreParserPageDTO) arg;
                    storeParserService.parseByCondition(storeParserPageDTO);
                    storeGuidList.addAll(storeParserPageDTO.getStoreGuidList());
                } else if (arg instanceof StoreParserBasePageDTO) {
                    StoreParserBasePageDTO storeParserBasePageDTO = (StoreParserBasePageDTO) arg;
                    storeParserService.parseByCondition(storeParserBasePageDTO);
                    storeGuidList.addAll(storeParserBasePageDTO.getStoreGuidList());
                }
                if (!CollectionUtils.isEmpty(storeGuidList)) {
                    logger.info("已解析门店列表：" + JacksonUtils.writeValueAsString(storeGuidList));
                }
            }
        }
    }
}
