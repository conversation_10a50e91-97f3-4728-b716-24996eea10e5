package com.holderzone.holder.saas.aggregation.merchant.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2024-09-03
 * @description
 */
@Getter
@AllArgsConstructor
public enum InOutDocumentEnum {
    UNKNOWN(-1,"未知"),
    /**
     * 采购入库
     */
    IN_PURCHASE(0,"采购入库"),
    /**
     * 其他入库
     */
    IN_OTHER(1,"其他入库"),
    /**
     * 盘盈入库
     */
    IN_CHECK_PROFIT(2,"盘盈入库"),
    /**
     * 反结账入库
     */
    IN_RECOBERY_ORDER(3,"反结账入库"),
    /**
     * 销售出库
     */
    OUT_PURCHASE(10,"销售出库"),
    /**
     * 退货出库
     */
    OUT_RETURN(11,"退货出库"),
    /**
     * 其他出库
     */
    OUT_OTHER(12,"其他出库"),
    /**
     * 盘亏出库
     */
    OUT_CHECK_DEFICIT(13,"盘亏出库");

    private final Integer code;

    private final String desc;

    public static String getNameByCode(Integer code){
        for(InOutDocumentEnum inOut : InOutDocumentEnum.values()){
            if(code.equals(inOut.getCode())){
                return inOut.getDesc();
            }
        }
        return UNKNOWN.getDesc();
    }
}
