package com.holderzone.holder.saas.aggregation.merchant.controller.rights;


import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.baseresource.HsmStoreService;
import com.holderzone.holder.saas.member.dto.baseresource.request.HsmStoreReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.request.ShopQueryReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.response.HsmStoreRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 组织表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-16
 */
@RestController
@RequestMapping("/hsm/member/store")
@Api(description = "门店")
public class HsmMemberStoreController {

    @Resource
    private HsmStoreService hsmStoreService;

    /**
     * 根据分页进行查询
     *
     * @param shopQueryReqDTO 条件
     * @return 分页的结果
     */
    @ApiOperation(value = "根据条件进行分页", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, response = HsmStoreRespDTO.class)
    @PostMapping("/query/page")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "根据条件进行分页")
    public Result findPageByCondition(
        @ApiParam(value = "商品对象列表", required = true) @RequestBody ShopQueryReqDTO shopQueryReqDTO) {
        return Result
            .buildSuccessResult(hsmStoreService.findPageByCondition(shopQueryReqDTO));
    }

    /**
     * 根据查询条件进行列表查询
     *
     * @param shopQueryReqDTO 条件
     * @return 全列表
     */
    @ApiOperation(value = "根据查询条件进行列表查询", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, response = HsmStoreRespDTO.class)
    @PostMapping("/query/list")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "根据查询条件进行列表查询")
    public Result findListByCondition(
        @ApiParam(value = "商品对象列表", required = true) @RequestBody ShopQueryReqDTO shopQueryReqDTO) {
        return Result
            .buildSuccessResult(hsmStoreService.findListByCondition(shopQueryReqDTO));
    }

    /**
     * 根据品牌GUID进行列表查询
     *
     * @return java.util.List<com.holderzone.holder.saas.member.dto.baseresource.response.HsmStoreRespDTO>
     * @Param [brandGuid]
     */
    @ApiOperation("根据品牌GUID进行列表查询,删除和禁用的也要查")
    @GetMapping("/findListByBrandGuid")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "根据品牌GUID进行列表查询,删除和禁用的也要查")
    public Result<List<HsmStoreRespDTO>> findListByBrandGuid(@RequestParam(value = "brandGuid", required = false) String brandGuid) {
        return Result.buildSuccessResult(hsmStoreService.findListByBrandGuid(brandGuid));
    }

    /**
     * 同步门店
     *
     * @param hsmStoreReqDTOS 列表
     */
    @ApiOperation(value = "同步门店", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, response = HsmStoreRespDTO.class)
    @PostMapping("/syc/list")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "同步门店")
    public Result syc(
        @ApiParam(value = "商品对象列表", required = true) @RequestBody List<HsmStoreReqDTO> hsmStoreReqDTOS) {
        return Result.buildSuccessResult(hsmStoreService.syc(hsmStoreReqDTOS));
    }
    /**
     * 同步门店
     *
     * @param hsmStoreReqDTO
     */
    @ApiOperation("同步门店")
    @PostMapping("/syc")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "同步门店")
    public  Result syc(@RequestBody  HsmStoreReqDTO hsmStoreReqDTO) {
        return Result.buildSuccessResult(hsmStoreService.syc(hsmStoreReqDTO));
    }
    /**
     * 保存门店
     * @param hsmStoreReqDTO
     * @return
     */
    @ApiOperation("保存门店")
    @PostMapping("/save")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "保存门店")
    public Result saveHsmStore(@RequestBody @Validated HsmStoreReqDTO hsmStoreReqDTO){
        return Result.buildSuccessResult(hsmStoreService.saveHsmStore(hsmStoreReqDTO));
    }

    /**
     * 修改门店
     * @param hsmStoreReqDTO
     * @return
     */
    @ApiOperation("修改门店")
    @PostMapping("/modify")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "修改门店")
    public Result modifyHsmStore(@RequestBody @Validated HsmStoreReqDTO hsmStoreReqDTO){
        return Result.buildSuccessResult(hsmStoreService.modifyHsmStore(hsmStoreReqDTO));
    }

    /**
     *
     * @param storeKey
     * @param allianceid
     * @return
     */
    @ApiOperation(value = "根据外部门店主键为空和联盟ID删除门店", notes = "根据外部门店主键为空和联盟ID删除门店")
    @DeleteMapping("/{storeKey}/{allianceid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "根据外部门店主键为空和联盟ID删除门店")
    public Result removeHsmStore(@ApiParam("storeKey") @PathVariable("storeKey") String storeKey,
                                  @ApiParam("allianceid") @PathVariable("allianceid") String allianceid) {
        return Result.buildSuccessResult(hsmStoreService.removeHsmStore(storeKey,allianceid));
    }

}
