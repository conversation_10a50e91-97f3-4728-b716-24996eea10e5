package com.holderzone.holder.saas.aggregation.app.entity.auth;

import com.holderzone.holder.saas.aggregation.app.anno.DataAuthFieldControl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/10/30
 * @description 优惠总额明细
 */
@Data
@ApiModel
@Accessors(chain = true)
public class DiscountAmountItemDTO implements Serializable {

    private static final long serialVersionUID = 7209713844750261841L;

    @ApiModelProperty(value = "编码")
    private Integer code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "金额")
    @DataAuthFieldControl("overview_sale_discount_amount")
    private String amount;

    @ApiModelProperty(value = "商家预计应得金额")
    private String amountStr;

    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountAmount;

    @ApiModelProperty(value = "数量")
    private Integer count;

    @ApiModelProperty(value = "超出金额，第三方活动使用")
    @DataAuthFieldControl("overview_sale_discount_amount")
    private String excessAmount;

    @ApiModelProperty(value = "顺序")
    private Integer sort;

    @ApiModelProperty(value = "使用明细")
    private List<InnerDetails> innerDetails;

    @ApiModelProperty(value = "商家预计应得金额")
    @DataAuthFieldControl("overview_sale_estimated_amount")
    private String estimatedAmount;

    @ApiModelProperty(value = "订单笔数")
    private String orderCount;

    @Data
    public static class InnerDetails {
        @ApiModelProperty("明细")
        private String name;

        @ApiModelProperty("统计金额")
        private BigDecimal amount;

        @ApiModelProperty("使用数量")
        private Integer count;

        @ApiModelProperty(value = "使用明细")
        private List<InnerDetails> subInnerDetails;
    }


    public DiscountAmountItemDTO() {

    }

    public DiscountAmountItemDTO(Integer code, String name, String amount, String excessAmount) {
        this.code = code;
        this.name = name;
        this.amount = amount;
        this.excessAmount = excessAmount;
    }
}