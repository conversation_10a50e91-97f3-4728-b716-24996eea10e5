package com.holderzone.holder.saas.aggregation.app.utils;

import com.holderzone.framework.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class VersionUtils {
    private static int[] getVersionIntArray(String version){
        if(StringUtils.isEmpty(version)){
            return new int[]{-1};
        }
        String[] versionArray = version.split(".");
        int[] versionIntArray = new int[versionArray.length];
        try {
            for (int i = 0; i < versionArray.length&& i<3; i++) {
                if(StringUtils.isEmpty(version)){
                    versionIntArray[i]=0;
                     continue;
                }
                versionIntArray[i] = Integer.parseInt(versionArray[i]);
            }
        }catch (Exception ex){
            log.error("版本号转化失败 ",ex);
            return  new int[]{-1};
        }
        return versionIntArray;
    }
    private static int compareVersion(int[] version1,int[] version2){
        if(version1.length!=version2.length){
            return  -2;
        }
        for (int i = 0; i < version1.length; i++) {
            if(version1[i]==version2[i]){
                continue;
            }
            if(version1[i]>version2[i]){
                return 1;
            }else{
                return -1;
            }
        }
        return 0;
    }
    public static  int compareVersion(String  version1,String  version2){
         return compareVersion(getVersionIntArray(version1),getVersionIntArray(version2));
    }


}
