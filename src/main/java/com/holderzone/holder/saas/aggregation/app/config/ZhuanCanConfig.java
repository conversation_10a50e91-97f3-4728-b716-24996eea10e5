package com.holderzone.holder.saas.aggregation.app.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @date 2024/4/19
 * @description 赚餐配置类
 */
@Data
@RefreshScope
@Configuration
public class ZhuanCanConfig {

    @Value("${zhuancan.sendCallMessage}")
    private String sendCallMessage;

    @Value("${zhuancan.batchSendCallMessage}")
    private String batchSendCallMessage;

    @Value("${zhuancan.memberRedeem}")
    private String memberRedeem;

    @Value("${zhuancan.updateVolumeRelevance}")
    private String updateVolumeRelevance;

}