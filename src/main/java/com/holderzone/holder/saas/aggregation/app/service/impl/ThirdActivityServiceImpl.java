package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.ThirdActivityService;
import com.holderzone.holder.saas.aggregation.app.service.feign.activity.ThirdActivityClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInOrderClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.GrouponClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.TradeThirdActivityClientService;
import com.holderzone.holder.saas.aggregation.app.utils.MapstructUtil;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityRespDTO;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityTypeDTO;
import com.holderzone.resource.common.util.LoginSource;
import com.holderzone.saas.store.dto.member.activity.ThirdActivityInfoAndRecordRespDTO;
import com.holderzone.saas.store.dto.member.activity.ThirdActivityTypeEnum;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.dto.trade.req.ThirdActivityRecordDTO;
import com.holderzone.saas.store.enums.order.RuleTypeEnum;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ThirdActivityServiceImpl implements ThirdActivityService {

    private final GrouponClientService grouponClientService;

    private final ThirdActivityClientService thirdActivityClientService;

    private final TradeThirdActivityClientService tradeThirdActivityClientService;

    private final DineInOrderClientService dineInOrderClientService;


    @Override
    public List<ThirdActivityInfoAndRecordRespDTO> listInfoAndRecord(String orderGuid, Integer groupBuyType) {
        // 查询活动列表
        List<ThirdActivityRespDTO> activities = thirdActivityClientService.list();
        if (Objects.nonNull(groupBuyType)) {
            // 按平台查询
            activities = activities.stream()
                    .filter(e -> e.getThirdType().equals(ThirdActivityTypeEnum.transferThirdType(groupBuyType)))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(activities)) {
            return Lists.newArrayList();
        }
        List<ThirdActivityInfoAndRecordRespDTO> resultList = MapstructUtil.INSTANCE.thirdActivityDTOList2InfoAndRecordDTOList(activities);
        List<GrouponListRespDTO> grouponList = grouponClientService.list(orderGuid, null);
        calculateThirdActivityFee(orderGuid, resultList, grouponList);
        return resultList;
    }

    @Override
    public List<ThirdActivityTypeDTO> queryThirdType(String orderGuid) {
        DineinOrderDetailRespDTO dine = dineInOrderClientService.getOrderDetail(orderGuid);
        if (!ObjectUtils.isEmpty(dine) && Objects.equals(TradeModeEnum.FAST.getCode(), dine.getTradeMode())) {
            List<GrouponListRespDTO> grouponList = grouponClientService.list(orderGuid, null);
            return buildFastTypes(grouponList);
        }
        List<ThirdActivityRespDTO> activities = thirdActivityClientService.list();
        if (CollectionUtils.isEmpty(activities)) {
            return buildDefaultTypes();
        }
        List<ThirdActivityInfoAndRecordRespDTO> activityRecords = MapstructUtil.INSTANCE.thirdActivityDTOList2InfoAndRecordDTOList(activities);
        List<GrouponListRespDTO> grouponList = grouponClientService.list(orderGuid, null);
        calculateThirdActivityFee(orderGuid, activityRecords, grouponList);
        log.info("查询第三方活动列表,recordDTOList:{},grouponList:{}", JacksonUtils.writeValueAsString(activityRecords),
                JacksonUtils.writeValueAsString(grouponList));
        // 按平台分组
        Map<String, List<ThirdActivityInfoAndRecordRespDTO>> byThirdTypeMap = activityRecords.stream()
                .collect(Collectors.groupingBy(ThirdActivityInfoAndRecordRespDTO::getThirdType));
        Set<String> thirdTypeSet = new HashSet<>(byThirdTypeMap.keySet());
        // 默认包含 美团团购、抖音团购
        thirdTypeSet.addAll(ThirdActivityTypeEnum.DOCK_COMPLETED_LIST.stream().map(ThirdActivityTypeEnum::getThirdType).collect(Collectors.toSet()));
        // 暂时先排除pos端的支付宝验券
        log.info("当前登录来源:{}", UserContextUtils.getSource());
        if (LoginSource.POS.code().equals(UserContextUtils.getSource()) || LoginSource.M.code().equals(UserContextUtils.getSource())) {
            thirdTypeSet.remove(ThirdActivityTypeEnum.ABC.getThirdType());
        }
        // transfer
        List<ThirdActivityTypeDTO> thirdActivityTypes = thirdTypeSet.stream().map(thirdType -> {
            ThirdActivityTypeEnum type = ThirdActivityTypeEnum.getNameByType(thirdType);
            ThirdActivityTypeDTO thirdActivityType = new ThirdActivityTypeDTO();
            thirdActivityType.setThirdType(thirdType);
            thirdActivityType.setThirdName(type.getThirdName());
            thirdActivityType.setSort(type.getSort());
            thirdActivityType.setGroupBuyType(ThirdActivityTypeEnum.transferGroupBuyType(thirdType));
            thirdActivityType.setJoinFee(calculateJoinFee(thirdType, byThirdTypeMap.get(thirdType), grouponList));
            return thirdActivityType;
        }).collect(Collectors.toList());
        //  sort
        return thirdActivityTypes.stream().sorted(Comparator.comparing(ThirdActivityTypeDTO::getSort)).collect(Collectors.toList());
    }


    /**
     * 计算 活动参与抵扣金额
     */
    private BigDecimal calculateJoinFee(String thirdType, List<ThirdActivityInfoAndRecordRespDTO> byThirdTypeList, List<GrouponListRespDTO> grouponList) {
        if (CollectionUtils.isEmpty(byThirdTypeList)) {
            byThirdTypeList = Lists.newArrayList();
        }
        if (ThirdActivityTypeEnum.dockCompleted(thirdType)) {
            List<String> activityGuids = byThirdTypeList.stream().map(ThirdActivityInfoAndRecordRespDTO::getGuid).collect(Collectors.toList());
            // 美团、抖音
            BigDecimal totalJoinFee = BigDecimal.ZERO;
            totalJoinFee = totalJoinFee.add(byThirdTypeList.stream()
                    .map(ThirdActivityInfoAndRecordRespDTO::getDeductionAmount)
                    .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            // 单纯使用美团、抖音验券的
            List<GrouponListRespDTO> grouponListByThirdType = grouponList.stream()
                    .filter(e -> Objects.equals(ThirdActivityTypeEnum.transferGroupBuyType(thirdType), e.getGrouponType()))
                    .filter(e -> StringUtils.isEmpty(e.getActivityGuid()))
                    .collect(Collectors.toList());
            totalJoinFee = totalJoinFee.add(grouponListByThirdType.stream()
                    .map(GrouponListRespDTO::getDeductionAmount)
                    .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            // 查询已使用第三方活动的团购验券，但是目前活动是下架状态的
            List<GrouponListRespDTO> grouponListByNoneThirdType = grouponList.stream()
                    .filter(e -> Objects.equals(ThirdActivityTypeEnum.transferGroupBuyType(thirdType), e.getGrouponType()))
                    .filter(e -> StringUtils.isNotBlank(e.getActivityGuid()) && !activityGuids.contains(e.getActivityGuid()))
                    .collect(Collectors.toList());
            totalJoinFee = totalJoinFee.add(grouponListByNoneThirdType.stream()
                    .map(GrouponListRespDTO::getDeductionAmount)
                    .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            return totalJoinFee;
        }
        return byThirdTypeList.stream()
                .map(ThirdActivityInfoAndRecordRespDTO::getDeductionAmount)
                .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算每个第三方活动的抵扣金额，参与活动金额，不参与活动金额
     */
    private void calculateThirdActivityFee(String orderGuid, List<ThirdActivityInfoAndRecordRespDTO> recordDTOList, List<GrouponListRespDTO> grouponList) {
        // 查询订单已参与的第三方活动记录
        List<ThirdActivityRecordDTO> activityList = tradeThirdActivityClientService.listThirdActivityByOrderGuid(orderGuid);
        if (CollectionUtils.isEmpty(activityList)) {
            return;
        }
        // 查询团购验券的
        Map<String, List<GrouponListRespDTO>> grouponMap = grouponList.stream().filter(e -> StringUtils.isNotBlank(e.getActivityGuid()))
                .collect(Collectors.groupingBy(GrouponListRespDTO::getActivityGuid));
        Map<String, ThirdActivityRecordDTO> activityMap = activityList.stream()
                .collect(Collectors.toMap(ThirdActivityRecordDTO::getActivityGuid, Function.identity(), (key1, key2) -> key2));
        for (ThirdActivityInfoAndRecordRespDTO info : recordDTOList) {
            ThirdActivityRecordDTO thirdActivity = activityMap.get(info.getGuid());
            if (ObjectUtils.isEmpty(thirdActivity)) {
                log.error("未匹配到活动使用记录 activityGuid={}", info.getGuid());
                continue;
            }
            if (Objects.equals(RuleTypeEnum.AMOUNT_DEDUCTION.getCode(), thirdActivity.getRuleType())) {
                calculateAmountByDeduction(info, thirdActivity, grouponMap);
            } else {
                // 买单优惠的抵扣金额 = 参与活动金额 + 不参与活动金额
                info.setDeductionAmount(thirdActivity.getJoinFee().add(thirdActivity.getNotJoinFee()));
                info.setJoinFee(thirdActivity.getJoinFee());
                info.setNotJoinFee(thirdActivity.getNotJoinFee());
            }
        }
    }


    /**
     * 计算金额扣减类型的第三方活动金额
     */
    private void calculateAmountByDeduction(ThirdActivityInfoAndRecordRespDTO info, ThirdActivityRecordDTO thirdActivity,
                                            Map<String, List<GrouponListRespDTO>> grouponMap) {
        info.setThirdActivityCodeList(thirdActivity.getThirdActivityCodeList());
        info.setJoinFee(BigDecimal.ZERO);
        info.setNotJoinFee(BigDecimal.ZERO);
        if (Objects.nonNull(info.getCouponFee())) {
            // 老的美团、抖音活动 和所有大众、赚餐、其他平台的
            info.setDeductionAmount(info.getCouponFee().multiply(BigDecimal.valueOf(info.getThirdActivityCodeList().size())));
        }
        List<GrouponListRespDTO> grouponListRespDTO = grouponMap.get(thirdActivity.getActivityGuid());
        if (ThirdActivityTypeEnum.dockCompleted(info.getThirdType()) && CollectionUtils.isNotEmpty(grouponListRespDTO)) {
            info.setCouponFee(grouponListRespDTO.get(0).getAmount());
            info.setDeductionAmount(grouponListRespDTO.stream().map(GrouponListRespDTO::getDeductionAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        if (Objects.isNull(info.getCouponFee())) {
            info.setCouponFee(thirdActivity.getCouponFee().divide(BigDecimal.valueOf(thirdActivity.getThirdActivityCodeList().size()), 2, RoundingMode.DOWN));
        }
        if (Objects.isNull(info.getDeductionAmount())) {
            info.setDeductionAmount(thirdActivity.getCouponFee());
        }
    }


    /**
     * 构建默认的第三方活动
     */
    private List<ThirdActivityTypeDTO> buildDefaultTypes() {
        return ThirdActivityTypeEnum.DOCK_COMPLETED_LIST.stream().map(e -> {
            ThirdActivityTypeDTO activity = new ThirdActivityTypeDTO();
            activity.setThirdType(e.getThirdType());
            activity.setThirdName(e.getThirdName());
            activity.setSort(e.getSort());
            activity.setGroupBuyType(ThirdActivityTypeEnum.transferGroupBuyType(e.getThirdType()));
            activity.setJoinFee(BigDecimal.ZERO);
            return activity;
        }).collect(Collectors.toList());
    }

    /**
     * 构建快餐返回的信息
     * 快餐没有第三方活动
     */
    private List<ThirdActivityTypeDTO> buildFastTypes(List<GrouponListRespDTO> grouponList) {
        Map<Integer, List<GrouponListRespDTO>> grouponMap = grouponList.stream()
                .collect(Collectors.groupingBy(GrouponListRespDTO::getGrouponType));
        return ThirdActivityTypeEnum.DOCK_COMPLETED_LIST.stream().map(e -> {
            ThirdActivityTypeDTO activity = new ThirdActivityTypeDTO();
            activity.setThirdType(e.getThirdType());
            activity.setThirdName(e.getThirdName());
            activity.setSort(e.getSort());
            activity.setGroupBuyType(ThirdActivityTypeEnum.transferGroupBuyType(e.getThirdType()));
            List<GrouponListRespDTO> grouponTypeList = grouponMap.get(ThirdActivityTypeEnum.transferGroupBuyType(e.getThirdType()));
            BigDecimal joinFee = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(grouponTypeList)) {
                joinFee = grouponTypeList.stream()
                        .map(GrouponListRespDTO::getDeductionAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            activity.setJoinFee(joinFee);
            return activity;
        }).collect(Collectors.toList());
    }
}