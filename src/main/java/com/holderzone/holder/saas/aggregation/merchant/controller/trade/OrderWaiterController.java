package com.holderzone.holder.saas.aggregation.merchant.controller.trade;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.trade.OrderWaiterClientService;
import com.holderzone.saas.store.dto.order.request.waiter.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> R
 * @date 2020/12/1 14:46
 * @description
 */
@RestController
@RequestMapping("/order_waiter")
@Api(description = "订单服务员")
@Slf4j
public class OrderWaiterController {

    @Autowired
    private OrderWaiterClientService orderWaiterClientService;

    @ApiOperation(value = "分页查询订单服务员明细接口", notes = "分页查询订单服务员明细接口")
    @PostMapping("/page_order_waiter_details")
    public Result<Page<OrderWaiterPageDetailsRespDTO>> pageOrderWaiterDetails(@RequestBody OrderWaiterPageDetailsReqDTO orderWaiterPageDetailsReqDTO) {
        return Result.buildSuccessResult(orderWaiterClientService.pageOrderWaiterDetails(orderWaiterPageDetailsReqDTO));
    }

    @ApiOperation(value = "分页查询订单服务员汇总接口", notes = "分页查询订单服务员汇总接口")
    @PostMapping("/page_order_waiter_total")
    public Result<Page<OrderWaiterPageTotalRespDTO>> pageOrderWaiterTotal(@RequestBody OrderWaiterPageDetailsReqDTO orderWaiterPageDetailsReqDTO) {
        return Result.buildSuccessResult(orderWaiterClientService.pageOrderWaiterTotal(orderWaiterPageDetailsReqDTO));
    }

    @ApiOperation(value = "分页查询订单服务员接口---后台补录", notes = "分页查询订单服务员接口---后台补录")
    @PostMapping("/page_order_waiter_make_up")
    public Result<Page<OrderWaiterPageMakeUpRespDTO>> pageOrderWaiterMakeUp(@RequestBody OrderWaiterMakeUpPageReqDTO reqDTO) {
        return Result.buildSuccessResult(orderWaiterClientService.pageOrderWaiterMakeUp(reqDTO));
    }

    @ApiOperation(value = "补录订单服务员接口---后台补录", notes = "补录订单服务员接口---后台补录")
    @PostMapping("/order_waiter_make_up")
    public Result<Boolean> orderWaiterMakeUp(@RequestBody OrderWaiterMakeUpReqDTO makeUpReqDTO) {
        return Result.buildSuccessResult(orderWaiterClientService.orderWaiterMakeUp(makeUpReqDTO));
    }
}
