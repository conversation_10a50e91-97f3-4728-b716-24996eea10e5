package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dynamic.datasource.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.weixin.entity.dto.WxMemberSessionPlusDTO;
import com.holderzone.holder.saas.aggregation.weixin.service.WxStoreTradeOrderService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.EnterpriseClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxStoreMenuDetailsClientService;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.dto.weixin.MultiMemberDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAuthorizerInfoDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.req.WxOrderNumberQuery;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreMenuReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxMenuDetailsDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxMenuController
 * @date 2019/2/23 14:30
 * @description 微信点餐门店菜品信息Controller
 * @package com.holderzone.holder.saas.aggregation.weixin.controller
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/wx-store-menu")
@Api("微信端门店：商品列表")
@Slf4j
public class WxMenuController {

    private final WxStoreMenuDetailsClientService wxStoreMenuDetailsClientService;

    private final EnterpriseClientService enterpriseClientService;

    private final WxStoreTradeOrderService wxStoreTradeOrderService;

    private final RedisUtils redisUtils;

    private final RedisTemplate<String, Integer> redisTemplate;

    @ApiOperation("获取门店菜单详情接口")
    @ApiImplicitParam(name = "baseDTO", value = "基本aaa菜品信息")
    @PostMapping("/details")
    public Result<WxMenuDetailsDTO> wxMenuDetails(@RequestBody WxStoreMenuReqDTO wxStoreMenuReqDTO) {
        log.info("获取门店菜单详情接口入参,wxConsumerReqDTO={}", JacksonUtils.writeValueAsString(wxStoreMenuReqDTO));
        return Result.buildSuccessResult(wxStoreMenuDetailsClientService.getWxMenuDetails(wxStoreMenuReqDTO));
    }

    @ApiOperation("获取consumer信息,")
    @ApiImplicitParam(name = "msgKey", value = "换取consumer信息的key，")
    @PostMapping("/get_consumer_info")
    @Deprecated
    public Result<WxStoreConsumerDTO> getConsumerInfo(@RequestBody WxPortalReqDTO wxPortalReqDTO) {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        log.info("WeixinUserThreadLocal获取的会员信息{}", JacksonUtils.writeValueAsString(wxMemberSessionDTO));
        WxStoreConsumerDTO consumer = null;
        if (wxMemberSessionDTO != null) {
            if (wxMemberSessionDTO.getCode() != 0) {
                log.error("获取用户信息失败,wxMemberSessionDTO:{}", wxMemberSessionDTO);
                return Result.buildFailResult(wxMemberSessionDTO.getCode(), wxMemberSessionDTO.getMessage());
            }
            consumer = build(wxMemberSessionDTO);
        } else {
            UserContextUtils.putErp(wxPortalReqDTO.getEnterpriseGuid());
            EnterpriseIdentifier.setEnterpriseGuid(wxPortalReqDTO.getEnterpriseGuid());
            consumer = wxStoreMenuDetailsClientService.getConsumerInfo(wxPortalReqDTO);
        }
        if (Objects.nonNull(consumer)) {
            // 点餐人数
            consumer.setGuestCount(wxStoreTradeOrderService.getFastGuestCount(consumer.getDiningTableGuid(), consumer.getOpenId()));
        }
        return Result.buildSuccessResult(consumer);
    }


    private WxStoreConsumerDTO build(WxMemberSessionDTO wxMemberSessionDTO) {
        WxStoreConsumerDTO wxStoreConsumerDTO = new WxStoreConsumerDTO();
        wxStoreConsumerDTO.setAreaGuid(wxMemberSessionDTO.getAreaGuid());
        wxStoreConsumerDTO.setAreaName(wxMemberSessionDTO.getAreaName());
        wxStoreConsumerDTO.setBrandGuid(wxMemberSessionDTO.getBrandGuid());
        wxStoreConsumerDTO.setBrandLogo(wxMemberSessionDTO.getLogUrl());
        wxStoreConsumerDTO.setBrandName(wxMemberSessionDTO.getBrandName());
        //wxStoreConsumerDTO.setCity(wxMemberSessionDTO.getWxUserInfoDTO().get);
        //wxStoreConsumerDTO.setConsumerGuid(wxMemberSessionDTO.getWxUserInfoDTO().get);
        wxStoreConsumerDTO.setDiningTableGuid(wxMemberSessionDTO.getDiningTableGuid());
        wxStoreConsumerDTO.setDiningTableName(wxMemberSessionDTO.getDiningTableCode());
        wxStoreConsumerDTO.setEnterpriseGuid(wxMemberSessionDTO.getEnterpriseGuid());
        wxStoreConsumerDTO.setEnterpriseName(wxMemberSessionDTO.getEnterpriseName());
        wxStoreConsumerDTO.setOpenId(wxMemberSessionDTO.getWxUserInfoDTO().getOpenId());
        wxStoreConsumerDTO.setHeadImgUrl(wxMemberSessionDTO.getWxUserInfoDTO().getHeadImgUrl());
        wxStoreConsumerDTO.setIsLogin(wxMemberSessionDTO.getWxUserInfoDTO().getIsLogin());
        wxStoreConsumerDTO.setNickName(wxMemberSessionDTO.getWxUserInfoDTO().getNickname());
        wxStoreConsumerDTO.setPhoneNum(wxMemberSessionDTO.getPhoneNum());
        wxStoreConsumerDTO.setSex(wxMemberSessionDTO.getWxUserInfoDTO().getSex());
        wxStoreConsumerDTO.setWorkName(wxMemberSessionDTO.getWxUserInfoDTO().getWorkName());
        wxStoreConsumerDTO.setContactAddress(wxMemberSessionDTO.getWxUserInfoDTO().getContactAddress());
        wxStoreConsumerDTO.setStoreGuid(wxMemberSessionDTO.getStoreGuid());
        wxStoreConsumerDTO.setStoreName(wxMemberSessionDTO.getStoreName());
        wxStoreConsumerDTO.setTableCode(wxMemberSessionDTO.getDiningTableCode());

        if (Objects.nonNull(wxMemberSessionDTO.getBrandGuid())) {
            WxStoreAuthorizerInfoDTO wxAuthInfo = wxStoreMenuDetailsClientService.getWxAuthInfo(wxMemberSessionDTO.getBrandGuid());
            log.info("getConsumerInfo品牌绑定的相关运营主体信息{}", JacksonUtils.writeValueAsString(wxAuthInfo));
            //运营主体是否为联盟
            Boolean isAlliance = false;
            //运营主体状态
            Boolean multiMemberStatus = false;
            //运营主体guid
            String operSubjectGuid = "";
            if (Objects.nonNull(wxAuthInfo)) {
                isAlliance = wxAuthInfo.getIsAlliance();
                operSubjectGuid = wxAuthInfo.getOperSubjectGuid();
            }
            if (!StringUtils.isEmpty(operSubjectGuid)) {
                List<MultiMemberDTO> multiMemberDTOS = enterpriseClientService.list(new HashSet<>(Collections.singleton(operSubjectGuid)));
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(multiMemberDTOS)) {
                    multiMemberStatus = multiMemberDTOS.get(0).getEnabled();
                }
            }
            wxStoreConsumerDTO.setIsAlliance(isAlliance);
            wxStoreConsumerDTO.setMultiMemberStatus(multiMemberStatus);
            wxStoreConsumerDTO.setOperSubjectGuid(operSubjectGuid);
        }


        //wxStoreConsumerDTO.setCity(city);
        return wxStoreConsumerDTO;
    }

    @ApiOperation("获取session中的consumer信息")
    @GetMapping("/get_consumer")
    public Result<WxMemberSessionDTO> getConsumer() {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        if (wxMemberSessionDTO == null) {
            return Result.buildOpFailedResult("用户信息不存在");
        }
        if (wxMemberSessionDTO.getCode() != 0) {
            log.error("获取用户信息失败,wxMemberSessionDTO:{}", wxMemberSessionDTO);
            return Result.buildFailResult(wxMemberSessionDTO.getCode(), wxMemberSessionDTO.getMessage());
        }
        return Result.buildSuccessResult(wxMemberSessionDTO);
    }

    /**
     * 缓存微信点餐人数
     *
     * @param query 桌位信息和人数
     * @return Boolean
     */
    @ApiOperation("缓存微信点餐人数")
    @PostMapping("/set_order_number")
    public Result<Boolean> setOrderNumber(@RequestBody WxOrderNumberQuery query) {
        log.info("缓存微信点餐人数 in={}", query);
        Integer orderNumber = query.getOrderNumber();
        if (Objects.isNull(orderNumber)) {
            return Result.buildOpFailedResult("没有传入微信点餐人数");
        }
        query.setOrderNumber(null);
        String searchKey = JacksonUtils.writeValueAsString(query);
        redisTemplate.opsForValue().set(searchKey, orderNumber, 12, TimeUnit.HOURS);
        return Result.buildSuccessResult(Boolean.TRUE);
    }

    /**
     * 获取缓存的微信点餐人数
     *
     * @param query 桌位信息
     * @return session中的consumer信息和点餐人数
     */
    @ApiOperation("获取缓存的微信点餐人数")
    @PostMapping("/get_order_number")
    public Result<WxMemberSessionPlusDTO> getOrderNumber(@RequestBody WxOrderNumberQuery query) {
        log.info("获取缓存的微信点餐人数 in={}", query);
        WxMemberSessionPlusDTO plusDTO = new WxMemberSessionPlusDTO();
        return Result.buildSuccessResult(plusDTO);
    }

    /**
     * 删除缓存的微信点餐人数
     *
     * @param query 桌位信息
     * @return Boolean
     */
    @ApiOperation("删除缓存的微信点餐人数")
    @PostMapping("/delete_order_number")
    public Result<Boolean> deleteOrderNumber(@RequestBody WxOrderNumberQuery query) {
        log.info("删除缓存的微信点餐人数 in={}", query);
        String searchKey = JacksonUtils.writeValueAsString(query);
        return Result.buildSuccessResult(redisTemplate.delete(searchKey));
    }
}
