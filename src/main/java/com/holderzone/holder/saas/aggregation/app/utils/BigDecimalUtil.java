package com.holderzone.holder.saas.aggregation.app.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @description BigDecimal工具类
 * @date 2022/7/12 17:03
 * @className: BigDecimalUtil
 */
public final class BigDecimalUtil {

    private BigDecimalUtil() {

    }

    /**
     * 取负数
     *
     * @param num
     * @return
     */
    public static BigDecimal negative(BigDecimal num) {
        return num.multiply(new BigDecimal(-1));
    }

    /**
     * 判断num大于0
     *
     * @param num
     * @return num大于0返回true
     */
    public static boolean greaterThanZero(BigDecimal num) {
        return null != num && BigDecimal.ZERO.compareTo(num) < 0;
    }

    /**
     * 判断num等于0
     *
     * @param num
     * @return num等于0返回true
     */
    public static boolean equelZero(BigDecimal num) {
        return null != num && BigDecimal.ZERO.compareTo(num) == 0;
    }

    /**
     * 判断num小于0
     *
     * @param num
     * @return num大于0返回true
     */
    public static boolean lessThanZero(BigDecimal num) {
        return null != num && BigDecimal.ZERO.compareTo(num) > 0;
    }

    /**
     * 保留两位小数
     *
     * @param num
     * @return 保留两位小数
     */
    public static BigDecimal setScale2(BigDecimal num) {
        return num.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public static String quantityTrimmed(BigDecimal quantity) {
        BigDecimal quantityScale0 = quantity.setScale(0, RoundingMode.HALF_UP);
        if (quantity.compareTo(quantityScale0) == 0) {
            return quantityScale0.toString();
        }
        return quantity.setScale(2, RoundingMode.HALF_UP).toString();
    }

    /**
     * 判断num1是否小于num2
     *
     * @param num1
     * @param num2
     * @return num1小于num2返回true
     */
    public static boolean lessThan(BigDecimal num1, BigDecimal num2) {
        return num1.compareTo(num2) == -1;
    }

    /**
     * 判断num1是否小于等于num2
     *
     * @param num1
     * @param num2
     * @return num1小于或者等于num2返回true
     */
    public static boolean lessEqual(BigDecimal num1, BigDecimal num2) {
        return (num1.compareTo(num2) == -1) || (num1.compareTo(num2) == 0);
    }

    /**
     * 判断num1是否大于num2
     *
     * @param num1
     * @param num2
     * @return num1大于num2返回true
     */
    public static boolean greaterThan(BigDecimal num1, BigDecimal num2) {
        return num1.compareTo(num2) == 1;
    }

    /**
     * 判断num1是否大于等于num2
     *
     * @param num1
     * @param num2
     * @return num1大于或者等于num2返回true
     */
    public static boolean greaterEqual(BigDecimal num1, BigDecimal num2) {
        return (num1.compareTo(num2) == 1) || (num1.compareTo(num2) == 0);
    }

    /**
     * 判断num1是否等于num2
     *
     * @param num1
     * @param num2
     * @return num1等于num2返回true
     */
    public static boolean equal(BigDecimal num1, BigDecimal num2) {
        return num1.compareTo(num2) == 0;
    }

    public static BigDecimal getRealDiscount(BigDecimal memberDiscount) {
        if (lessEqual(BigDecimal.ZERO, memberDiscount)) {
            return BigDecimal.ONE.subtract(memberDiscount.divide(BigDecimal.TEN));
        }
        return BigDecimal.ONE;
    }

    public static BigDecimal nonNullValue(BigDecimal value) {
        return value != null ? value : BigDecimal.ZERO;
    }

    /**
     * 除法
     */
    public static BigDecimal divide(BigDecimal d1, BigDecimal... dArr) {

        for (BigDecimal d2 : dArr) {
            d1 = d1.divide(d2, 4, BigDecimal.ROUND_HALF_UP);
        }
        return d1.setScale(4, RoundingMode.HALF_UP);
    }

    /**
     * 保留两位小数 ---乘法
     *
     * @param v1
     * @param v2
     * @return
     */
    public static BigDecimal multiply2(BigDecimal v1, BigDecimal v2) {
        return v1.multiply(v2).divide(new BigDecimal(1), 2, BigDecimal.ROUND_HALF_UP);
    }
}
