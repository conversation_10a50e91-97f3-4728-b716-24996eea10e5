package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.rights;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.dto.rights.request.HsmGrowthSetReqDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @description 成长值设置
 * @date 2019/5/16 16:17
 */
@Component
@FeignClient(name = "holder-saas-member-account",fallbackFactory = SystemManagementGrowthSetClientService.SystemManagementGrowthSetClientServiceFallBack.class)
public interface SystemManagementGrowthSetClientService {
    /**
     *  成长值设置
     * @param hsmGrowthSetDTO
     * @return
     */
    @PostMapping(value = "/hsm-system-management-growth-set/create", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    String createGrowthRule(HsmGrowthSetReqDTO hsmGrowthSetDTO);
    /**
     *  成长值设置 --update
     * @param hsmGrowthSetDTO
     * @return
     */
    @PostMapping(value = "/hsm-system-management-growth-set/update", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    boolean updateGrowthRule(HsmGrowthSetReqDTO hsmGrowthSetDTO);

    @Slf4j
    @Component
    class SystemManagementGrowthSetClientServiceFallBack  implements FallbackFactory<SystemManagementGrowthSetClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";
        @Override
        public  SystemManagementGrowthSetClientService create(Throwable throwable) {
            return new SystemManagementGrowthSetClientService(){

                @Override
                public String createGrowthRule(HsmGrowthSetReqDTO hsmGrowthSetDTO) {
                    log.error(HYSTRIX_PATTERN, "createGrowthRule", JacksonUtils.writeValueAsString(hsmGrowthSetDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("成长值设置失败");
                }

                @Override
                public boolean updateGrowthRule(HsmGrowthSetReqDTO hsmGrowthSetDTO) {
                        log.error(HYSTRIX_PATTERN, "updateGrowthRule", JacksonUtils.writeValueAsString(hsmGrowthSetDTO), ThrowableUtils.asString(throwable));
                        throw new BusinessException("成长值更新失败");
                }
            };
        }

    }
}
