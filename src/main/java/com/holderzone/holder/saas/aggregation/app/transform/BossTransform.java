package com.holderzone.holder.saas.aggregation.app.transform;

import com.holderzone.saas.store.dto.boss.resp.BossOrderDetailRespDTO;
import com.holderzone.saas.store.dto.boss.resp.BossTableRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.table.TableOrderDTO;
import com.holderzone.saas.store.reserve.api.dto.TableOrderReserveDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/22
 * @description 老板助手转换类
 */
@Mapper
public interface BossTransform {

    BossTransform INSTANCE = Mappers.getMapper(BossTransform.class);

    default BossTableRespDTO tableOrderReserveDTO2BossTableRespDTO(TableOrderReserveDTO tableOrderReserveDTO) {
        if (tableOrderReserveDTO == null) {
            return null;
        }
        BossTableRespDTO tableRespDTO = new BossTableRespDTO();
        tableRespDTO.setTableGuid(tableOrderReserveDTO.getTableGuid());
        tableRespDTO.setTableCode(tableOrderReserveDTO.getTableCode());
        tableRespDTO.setAreaGuid(tableOrderReserveDTO.getAreaGuid());
        tableRespDTO.setAreaName(tableOrderReserveDTO.getAreaName());
        tableRespDTO.setOrderAmount(tableOrderReserveDTO.getOrderAmount());
        tableRespDTO.setMainOrderGuid(tableOrderReserveDTO.getMainOrderGuid());
        tableRespDTO.setOrderGuid(tableOrderReserveDTO.getOrderGuid());
        tableRespDTO.setSeats(tableOrderReserveDTO.getSeats());
        tableRespDTO.setActualGuestsNo(tableOrderReserveDTO.getActualGuestsNo());
        tableRespDTO.setStatus(tableOrderReserveDTO.getStatus());
        tableRespDTO.setSort(tableOrderReserveDTO.getSort());
        tableRespDTO.setSubStatus(tableOrderReserveDTO.getSubStatus());
        tableRespDTO.setOpenTableTime(tableOrderReserveDTO.getOpenTableTime());
        tableRespDTO.setCurrentTime(tableOrderReserveDTO.getCurrentTime());
        tableRespDTO.setStoreGuid(tableOrderReserveDTO.getStoreGuid());
        tableRespDTO.setStoreName(tableOrderReserveDTO.getStoreName());
        return tableRespDTO;
    }

    default List<BossTableRespDTO> tableOrderReserveDTOList2BossTableRespDTOList(List<TableOrderReserveDTO> tableOrderReserveDTOS) {
        if (tableOrderReserveDTOS == null) {
            return Collections.emptyList();
        }

        List<BossTableRespDTO> list = new ArrayList<>(tableOrderReserveDTOS.size());
        for (TableOrderReserveDTO dto : tableOrderReserveDTOS) {
            list.add(tableOrderReserveDTO2BossTableRespDTO(dto));
        }

        return list;
    }

    default BossTableRespDTO tableOrderDTO2BossTableRespDTO(TableOrderDTO tableOrder) {
        if (tableOrder == null) {
            return null;
        }
        BossTableRespDTO respDTO = new BossTableRespDTO();
        respDTO.setTableGuid(tableOrder.getTableGuid());
        respDTO.setTableCode(tableOrder.getTableCode());
        respDTO.setAreaGuid(tableOrder.getAreaGuid());
        respDTO.setAreaName(tableOrder.getAreaName());
        respDTO.setOrderAmount(tableOrder.getOrderAmount());
        respDTO.setMainOrderGuid(tableOrder.getMainOrderGuid());
        respDTO.setOrderGuid(tableOrder.getOrderGuid());
        respDTO.setSeats(tableOrder.getSeats());
        respDTO.setActualGuestsNo(tableOrder.getActualGuestsNo());
        respDTO.setStatus(tableOrder.getStatus());
        respDTO.setSort(tableOrder.getSort());
        respDTO.setSubStatus(tableOrder.getSubStatus());
        respDTO.setOpenTableTime(tableOrder.getOpenTableTime());
        respDTO.setCurrentTime(tableOrder.getCurrentTime());
        respDTO.setStoreGuid(tableOrder.getStoreGuid());
        respDTO.setStoreName(tableOrder.getStoreName());
        respDTO.setAssociatedTimes(tableOrder.getAssociatedTimes());
        respDTO.setCombineTimes(tableOrder.getCombineTimes());
        return respDTO;
    }

    default List<BossTableRespDTO> tableOrderDTOList2BossTableRespDTOList(List<TableOrderDTO> tableOrders) {
        if (tableOrders == null) {
            return Collections.emptyList();
        }

        List<BossTableRespDTO> list = new ArrayList<>(tableOrders.size());
        for (TableOrderDTO dto : tableOrders) {
            list.add(tableOrderDTO2BossTableRespDTO(dto));
        }

        return list;
    }

    default BossOrderDetailRespDTO dineOrderDetail2BossOrderDetail(DineinOrderDetailRespDTO dine) {
        if (null == dine) {
            return null;
        }
        BossOrderDetailRespDTO respDTO = new BossOrderDetailRespDTO();
        respDTO.setOrderGuid(dine.getGuid());
        respDTO.setOrderNo(dine.getOrderNo());
        respDTO.setStoreGuid(dine.getStoreGuid());
        respDTO.setStoreName(dine.getStoreName());
        respDTO.setUpperState(dine.getUpperState());
        respDTO.setMainOrderNo(dine.getMainOrderNo());
        respDTO.setMainOrderGuid(dine.getMainOrderGuid());
        respDTO.setReturnItemDTOS(dine.getReturnItemDTOS());
        respDTO.setDineInItemDTOS(dine.getDineInItemDTOS());
        respDTO.setRecoveryId(dine.getRecoveryId());
        respDTO.setRecoveryType(dine.getRecoveryType());
        respDTO.setOrderFee(dine.getOrderFee());
        respDTO.setAppendFee(dine.getAppendFee());
        respDTO.setActuallyPayFee(dine.getActuallyPayFee());
        respDTO.setChangeFee(dine.getChangeFee());
        respDTO.setDiscountFee(dine.getDiscountFee());
        respDTO.setReserveFee(dine.getReserveFee());
        respDTO.setReserveRefundAmount(dine.getReserveRefundAmount());
        respDTO.setReserveGuid(dine.getReserveGuid());
        respDTO.setReserveRemark(dine.getReserveRemark());
        respDTO.setGrouponFee(dine.getGrouponFee());
        respDTO.setGmtCreate(dine.getGmtCreate());
        respDTO.setRemark(dine.getRemark());
        respDTO.setDiningTableGuid(dine.getDiningTableGuid());
        respDTO.setDiningTableName(dine.getDiningTableName());
        respDTO.setVirtualTable(dine.getVirtualTable());
        respDTO.setGuestCount(dine.getGuestCount());
        respDTO.setActivityGuid(dine.getActivityGuid());
        respDTO.setMemberCardGuid(dine.getMemberCardGuid());
        respDTO.setRefundAbleFee(dine.getRefundAbleFee());
        respDTO.setGrouponRefundAbleFee(dine.getGrouponRefundAbleFee());
        respDTO.setUseWholeGrouponFlag(dine.getUseWholeGrouponFlag());

        return respDTO;
    }
}
