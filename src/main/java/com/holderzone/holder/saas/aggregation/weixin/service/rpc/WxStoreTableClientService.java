package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 调用桌台服务，开台，生成空订单
 * @className WxStoreTableOpenClientService
 * @date 2019/3/6
 */
@Component
@FeignClient(name = "holder-saas-store-table"
        , fallbackFactory = WxStoreTableClientService.WxStoreTableOpenFallBack.class)
public interface WxStoreTableClientService {
    /***
     * 根据条件查询桌台（可查门店下所有）
     * @param tableBasicQueryDTO 请求参数
     * @return
     */
    @PostMapping("/table/web/query")
    List<TableBasicDTO> queryTableByWeb(@RequestBody TableBasicQueryDTO tableBasicQueryDTO);

    @PostMapping("/table/getOrderGuid/{tableGuid}")
    String getOrderGuidByTableGuid(@PathVariable("tableGuid") String tableGuid);

	@Slf4j
    @Component
    class WxStoreTableOpenFallBack implements FallbackFactory<WxStoreTableClientService> {
        @Override
        public WxStoreTableClientService create(Throwable throwable) {
            return new WxStoreTableClientService() {

                @Override
                public List<TableBasicDTO> queryTableByWeb(TableBasicQueryDTO tableBasicQueryDTO) {
                    log.error("queryTableByWeb失败，msg={}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public String getOrderGuidByTableGuid(String tableGuid) {
                    log.error("getOrderGuidByTableGuid失败，msg={}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }
            };

        }
    }
}
