package com.holderzone.holder.saas.aggregation.merchant.controller.journaling;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.journaling.ReportClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.pay.StorePayClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.trade.TradeClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.user.UserFeignService;
import com.holderzone.saas.store.dto.journaling.req.BusinessSituationReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.BusinessDataRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.BusinessHisTrendRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.RetailBusinessDataRespDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.pay.QuickPayStatisticsReqDTO;
import com.holderzone.saas.store.dto.pay.QuickPayStatisticsRespDTO;
import com.holderzone.saas.store.dto.user.UserSpinnerDTO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessSituationController
 * @date 2019/05/29 16:49
 * @description 营业概况controller
 * @program holder-saas-aggregation-merchant
 */
@RestController
@Api(value = "报表-营业概况", description = "报表-营业概况")
@RequestMapping("/busSituation")
@Slf4j
public class BusinessSituationController {

    @Autowired
    ReportClientService reportClientService;
    @Autowired
    UserFeignService userFeignService;

    @Autowired
    TradeClientService tradeClientService;

    @Resource
    private StorePayClientService storePayClientService;

    @PostMapping("/businessData")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT)
    public Result<BusinessDataRespDTO> businessData(@RequestBody BusinessSituationReqDTO businessSituationReqDTO) {
        //获取有权限的门店信息
        UserSpinnerDTO userSpinnerDTO = userFeignService.queryStoreSpinner();
        List<String> storeGuids = userSpinnerDTO.getArrayOfStoreDTO().stream().map(StoreDTO::getGuid).collect(Collectors.toList());
        if (storeGuids.size() == 0) {
            throw new BusinessException("该账号所在商户未绑定门店信息！");
        }
        businessSituationReqDTO.setStoreGuidList(storeGuids);
        //时间兼容
        setReqDateTime(businessSituationReqDTO);
        //商户后台调用 获取营业数据（实收金额）
        businessSituationReqDTO.setDataSource("0");
        BusinessDataRespDTO orderStatistics = tradeClientService.getOrderStatistics(businessSituationReqDTO);
        BigDecimal businessFee = BigDecimal.ZERO;
        BigDecimal actuallyPayFee = BigDecimal.ZERO;
        Integer orderCount = 0;
        Integer guestCount = 0;
        if (orderStatistics != null) {
            businessFee = orderStatistics.getActuallyPayFee();
            actuallyPayFee = orderStatistics.getActuallyPayFee();
            orderCount = orderStatistics.getOrderCount();
            guestCount = orderStatistics.getGuestCount();
        } else {
            orderStatistics = new BusinessDataRespDTO();
        }

        // 处理快速收款数据
        BigDecimal quickPayAmountTotal = BigDecimal.ZERO;
        Integer quickPayOrderCount = 0;
        Integer quickPayGuestCount = 0;
        QuickPayStatisticsReqDTO quickPaReqDTO = new QuickPayStatisticsReqDTO();
        quickPaReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        quickPaReqDTO.setStartDateTime(businessSituationReqDTO.getBusinessStartDateTime());
        quickPaReqDTO.setEndDateTime(businessSituationReqDTO.getBusinessEndDateTime());
        quickPaReqDTO.setStoreGuidList(storeGuids);
        List<QuickPayStatisticsRespDTO> quickPayList = storePayClientService.queryQuickPayStatistics(quickPaReqDTO);
        if (!CollectionUtils.isEmpty(quickPayList)) {
            quickPayAmountTotal = quickPayList.stream()
                    .map(QuickPayStatisticsRespDTO::getAmountTotal)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            quickPayOrderCount = quickPayList.stream()
                    .map(QuickPayStatisticsRespDTO::getOrderCount)
                    .filter(Objects::nonNull)
                    .reduce(0, Integer::sum);
            quickPayGuestCount = quickPayList.stream()
                    .map(QuickPayStatisticsRespDTO::getGuestCount)
                    .filter(Objects::nonNull)
                    .reduce(0, Integer::sum);
        }
        businessFee = businessFee.add(quickPayAmountTotal);
        actuallyPayFee = actuallyPayFee.add(quickPayAmountTotal);
        orderCount = orderCount + quickPayOrderCount;
        guestCount = guestCount + quickPayGuestCount;

        orderStatistics.setBusinessFee(businessFee);
        orderStatistics.setActuallyPayFee(actuallyPayFee);
        orderStatistics.setOrderCount(orderCount);
        orderStatistics.setGuestCount(guestCount);
        return Result.buildSuccessResult(orderStatistics);
    }

    @PostMapping("/busiHisTrend")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT)
    public Result<BusinessHisTrendRespDTO> hisTrend(@RequestBody BusinessSituationReqDTO businessSituationReqDTO) {
        //获取有权限的门店信息
        UserSpinnerDTO userSpinnerDTO = userFeignService.queryStoreSpinner();
        List<String> storeGuids = userSpinnerDTO.getArrayOfStoreDTO().stream().map(StoreDTO::getGuid).collect(Collectors.toList());
        if (storeGuids.size() == 0) {
            throw new BusinessException("该账号所在商户未绑定门店信息！");
        }
        businessSituationReqDTO.setStoreGuidList(storeGuids);
        //时间兼容
        setReqDateTime(businessSituationReqDTO);
        //商户后台调用 获取营业数据（实收金额）
        businessSituationReqDTO.setDataSource("0");
        return Result.buildSuccessResult(tradeClientService.getBusinessHisTrend(businessSituationReqDTO));
    }

    @PostMapping("/single_business_data")
    public Result<RetailBusinessDataRespDTO> singleBusinessData(@RequestBody BusinessSituationReqDTO businessSituationReqDTO) {
        log.info("商超-营业概况请求入参：{}", JacksonUtils.writeValueAsString(businessSituationReqDTO));
        //时间兼容
        setReqDateTime(businessSituationReqDTO);
        return Result.buildSuccessResult(reportClientService.singleBusinessData(businessSituationReqDTO));
    }

    @PostMapping("/single_his_trend")
    public Result<BusinessHisTrendRespDTO> singleHisTrend(@RequestBody BusinessSituationReqDTO businessSituationReqDTO) {
        log.info("商超-营业概况请求入参：{}", JacksonUtils.writeValueAsString(businessSituationReqDTO));
        //时间兼容
        setReqDateTime(businessSituationReqDTO);
        return Result.buildSuccessResult(reportClientService.singleHisTrend(businessSituationReqDTO));
    }

    private static void setReqDateTime(BusinessSituationReqDTO businessSituationReqDTO) {
        if (businessSituationReqDTO.getBusinessEndDateTime() == null) {
            businessSituationReqDTO.setBusinessEndDateTime(businessSituationReqDTO.getEndDate().atTime(LocalTime.MAX));
        }
        if (businessSituationReqDTO.getBusinessStartDateTime() == null) {
            businessSituationReqDTO.setBusinessStartDateTime(businessSituationReqDTO.getStartDate().atTime(LocalTime.MIN));
        }
    }

}
