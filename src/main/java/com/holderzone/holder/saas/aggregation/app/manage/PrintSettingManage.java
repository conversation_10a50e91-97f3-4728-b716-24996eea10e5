package com.holderzone.holder.saas.aggregation.app.manage;

import com.holderzone.holder.saas.aggregation.app.service.feign.PrintClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.business.StoreConfigClientService;
import com.holderzone.saas.store.dto.business.manage.StoreConfigQueryDTO;
import com.holderzone.saas.store.dto.config.req.PrintItemOrderConfigReq;
import com.holderzone.saas.store.dto.print.PrintSettingDTO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class PrintSettingManage {

    private final StoreConfigClientService storeConfigClientService;

    private final PrintClientService printClientService;

    public PrintSettingDTO getPrintSetting(StoreConfigQueryDTO configQueryDTO) {
        PrintSettingDTO printSettingDTO = new PrintSettingDTO();
        //查询商品打印排序
        printSettingDTO.setPrintItemOrder(storeConfigClientService.queryPrintItemOrderConfig(configQueryDTO));

        //查询单据是否自动打印
        PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid(configQueryDTO.getStoreGuid());
        printerDTO.setDeviceId(configQueryDTO.getDeviceId());
        printSettingDTO.setAutoPrint(printClientService.autoPrintQuery(printerDTO));

        return printSettingDTO;
    }

    public boolean printSet(PrintItemOrderConfigReq req){
        //设置商品打印顺序
        storeConfigClientService.updatePrintItemOrderConfig(req);
        //设置自动打印
        PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid(req.getStoreGuid());
        printerDTO.setDeviceId(req.getDeviceId());
        printClientService.autoPrintSet(req.getAutoPrint(), printerDTO);
        return true;
    }
}
