package com.holderzone.holder.saas.aggregation.app.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;


@Data
@Component
@ConfigurationProperties(prefix = "api.version")
public class ApiVersionControlConfig {


    private List<InnerControl> control;


    @Data
    public static class InnerControl {

        /**
         * 最小版本
         */
        private Integer min;

        /**
         * 控制uri
         */
        private String uri;

    }

}
