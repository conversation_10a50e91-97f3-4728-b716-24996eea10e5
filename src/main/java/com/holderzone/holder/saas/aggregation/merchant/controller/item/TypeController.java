package com.holderzone.holder.saas.aggregation.merchant.controller.item;

import cn.hutool.core.collection.CollectionUtil;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.item.ItemClientService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.TypeReqDTO;
import com.holderzone.saas.store.dto.item.resp.TypeWebRespDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TypeController
 * @date 2018/09/11 上午9:21
 * @description //TODO
 * @program holder-saas-store-dish
 */
@RestController
@RequestMapping("/type")
@Api(description = "商品分类接口")
@ResponseBody
public class TypeController {
    private static final Logger logger = LoggerFactory.getLogger(TypeController.class);
    private final ItemClientService itemClientService;

    @Autowired
    public TypeController(ItemClientService itemClientService) {
        this.itemClientService = itemClientService;
    }

    @ApiOperation(value = "根据门店信息查询商品分类列表接口", notes = "必填参数：dataList,内容：选择门店的GUID集合")
    @PostMapping("/query_type_by_stores")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "根据门店信息查询商品分类列表接口", action = OperatorType.SELECT)
    public Result<List<TypeWebRespDTO>> queryTypeByStoreGuidList(@RequestBody ItemStringListDTO itemStringListDTO) {
        logger.info("根据门店集合查询商品分类列表接口入参,itemStringListDTO={}", JacksonUtils.writeValueAsString(itemStringListDTO));
        itemStringListDTO.setFrom(0);
        List<TypeWebRespDTO> typeWebRespDTOList = itemClientService.queryTypeByStoreGuidList(itemStringListDTO);
        if (CollectionUtil.isNotEmpty(typeWebRespDTOList)) {
            typeWebRespDTOList.forEach(t -> {
                if (t.getName().equals(Constants.BANQUET_PACKAGES)) {
                    t.setName(LocaleUtil.getMessage("BANQUET_PACKAGES"));
                }
                if (t.getName().equals(Constants.DEFAULT_CATEGORY)) {
                    t.setName(LocaleUtil.getMessage("DEFAULT_CATEGORY"));
                }
            });
        }
        return Result.buildSuccessResult(typeWebRespDTOList);
    }

    @ApiOperation(value = "门店分类保存接口")
    @PostMapping("/store/save")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "门店分类保存接口", action = OperatorType.ADD)
    public Result saveFromStore(@RequestBody @Valid TypeReqDTO typeReqDTO) {
        logger.info("门店分类新增接口入参,typeReqDTO={}", JacksonUtils.writeValueAsString(typeReqDTO));
        typeReqDTO.setFrom(0);
        Integer num = itemClientService.save(typeReqDTO);
        if (num == 1) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.CATEGORY_CREATION_FAILED));
        }
    }

    @ApiOperation(value = "品牌库入口分类保存接口")
    @PostMapping("/brand/save")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "品牌库入口分类保存接口", action = OperatorType.ADD)
    public Result saveFromBrand(@RequestBody @Valid TypeReqDTO typeReqDTO) {
        logger.info("品牌分类新增接口入参,typeReqDTO={}", JacksonUtils.writeValueAsString(typeReqDTO));
        typeReqDTO.setFrom(1);
        Integer num = itemClientService.save(typeReqDTO);
        if (num == 1) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.CATEGORY_CREATION_FAILED));
        }
    }

    @ApiOperation(value = "门店分类快速新建接口")
    @PostMapping("/store/quick_save")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "门店分类快速新建接口", action = OperatorType.ADD)
    public Result quickSave(@RequestBody TypeReqDTO typeReqDTO) {
        logger.info("门店分类快速新建接口,typeReqDTO={}", JacksonUtils.writeValueAsString(typeReqDTO));
        typeReqDTO.setFrom(0);
        Integer num = itemClientService.quickSave(typeReqDTO);
        if (num == 1) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.QUICK_CATEGORY_CREATION_FAILED));
        }
    }

    @ApiOperation(value = "品牌分类快速新建接口")
    @PostMapping("/brand/quick_save")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "品牌分类快速新建接口", action = OperatorType.ADD)
    public Result quickSaveFromBrand(@RequestBody TypeReqDTO typeReqDTO) {
        logger.info("品牌分类快速新建接口,typeReqDTO={}", JacksonUtils.writeValueAsString(typeReqDTO));
        typeReqDTO.setFrom(1);
        Integer num = itemClientService.quickSave(typeReqDTO);
        if (num == 1) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.QUICK_CATEGORY_CREATION_FAILED));
        }
    }

    @ApiOperation(value = "门店查询分类接口", notes = "data字段内容为门店GUID")
    @PostMapping("/store/query_type")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "门店查询分类接口", action = OperatorType.SELECT)
    public Result<List<TypeWebRespDTO>> queryType(@RequestBody @Valid ItemSingleDTO itemSingleDTO) {
        logger.info("门店分类查询接口入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(0);
        List<TypeWebRespDTO> typeWebRespDTOS = itemClientService.queryType(itemSingleDTO);
        return Result.buildSuccessResult(typeWebRespDTOS);
    }

    @ApiOperation(value = "品牌查询分类接口", notes = "data字段内容为门店GUID")
    @PostMapping("/brand/query_type")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "品牌查询分类接口", action = OperatorType.SELECT)
    public Result<List<TypeWebRespDTO>> queryTypeFromBrand(@RequestBody @Valid ItemSingleDTO itemSingleDTO) {
        logger.info("品牌分类查询接口入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(1);
        List<TypeWebRespDTO> typeWebRespDTOS = itemClientService.queryType(itemSingleDTO);
        return Result.buildSuccessResult(typeWebRespDTOS);
    }

    @ApiOperation(value = "门店分类获取排序接口", notes = "data字段内容为门店GUID")
    @PostMapping("/store/get_sort")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "门店分类获取排序接口", action = OperatorType.SELECT)
    public Result<Integer> getSort(@RequestBody ItemSingleDTO itemSingleDTO) {
        logger.info("门店分类获取排序接口入参,dishSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(0);
        Integer sort = itemClientService.getSort(itemSingleDTO);
        if (sort == null || sort < 0) {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SORTING_RETRIEVAL_FAILED));
        } else {
            return Result.buildSuccessResult(sort);
        }
    }

    @ApiOperation(value = "品牌分类获取排序接口", notes = "data字段内容为门店GUID")
    @PostMapping("/brand/get_sort")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "品牌分类获取排序接口", action = OperatorType.SELECT)
    public Result<Integer> getSortFromBrand(@RequestBody ItemSingleDTO itemSingleDTO) {
        logger.info("品牌分类获取排序接口入参,dishSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(1);
        Integer sort = itemClientService.getSort(itemSingleDTO);
        if (sort == null || sort < 0) {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SORTING_RETRIEVAL_FAILED));
        } else {
            return Result.buildSuccessResult(sort);
        }
    }

    @ApiOperation(value = "门店入口分类修改接口")
    @PostMapping("/store/update")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "门店入口分类修改接口")
    public Result updateFromStore(@RequestBody @Valid TypeReqDTO typeReqDTO) {
        logger.info("分类修改接口入参,typeReqDTO={}", JacksonUtils.writeValueAsString(typeReqDTO));
        typeReqDTO.setFrom(0);
        Integer update = itemClientService.update(typeReqDTO);
        if (update != 1) {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.CATEGORY_MODIFICATION_FAILED));
        } else {
            return Result.buildEmptySuccess();
        }
    }

    @ApiOperation(value = "商品库入口分类修改接口")
    @PostMapping("/brand/update")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "商品库入口分类修改接口")
    public Result updateFromBrand(@RequestBody @Valid TypeReqDTO typeReqDTO) {
        logger.info("分类修改接口入参,typeReqDTO={}", JacksonUtils.writeValueAsString(typeReqDTO));
        typeReqDTO.setFrom(1);
        Integer update = itemClientService.update(typeReqDTO);
        if (update != 1) {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.CATEGORY_MODIFICATION_FAILED));
        } else {
            return Result.buildEmptySuccess();
        }
    }

    @ApiOperation(value = "门店商品库入口分类删除接口")
    @PostMapping("/store/delete")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "门店商品库入口分类删除接口", action = OperatorType.DELETE)
    public Result deleteFromStore(@RequestBody ItemSingleDTO itemSingleDTO) {
        logger.info("门店分类删除接口入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(0);
        Integer delete = itemClientService.delete(itemSingleDTO);
        if (delete != 1) {
            return Result.buildOpFailedResult("分类删除失败");
        } else {
            return Result.buildEmptySuccess();
        }
    }

    @ApiOperation(value = "品牌商品库入口分类删除接口")
    @PostMapping("/brand/delete")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "品牌商品库入口分类删除接口", action = OperatorType.DELETE)
    public Result deleteFromBrand(@RequestBody ItemSingleDTO itemSingleDTO) {
        logger.info("品牌分类删除接口入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(1);
        Integer delete = itemClientService.delete(itemSingleDTO);
        if (delete != 1) {
            return Result.buildOpFailedResult("分类删除失败");
        } else {
            return Result.buildEmptySuccess();
        }
    }

    @ApiOperation(value = "判断商品类型下面是否存在商品  存在1/不存在2")
    @PostMapping("/verify_item_exists_for_type")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "判断商品类型下面是否存在商品接口", action = OperatorType.SELECT)
    public Result verifyThatitemExistsForType(@RequestBody @Valid SingleDataDTO request) {
        logger.info("判断商品类型下面是否存在商品,request = {}", JacksonUtils.writeValueAsString(request));
        return Result.buildEmptySuccess().setTData(itemClientService.verifyThatitemExistsForType(request));
    }


    @ApiOperation(value = "查询当前团餐功能是否开启 data: 门店storeGuid")
    @PostMapping("/select_group_meal_status")
    public Result selectGroupMealStatus(@RequestBody SingleDataDTO request) {
        logger.info("查询团餐当前状态，入参 request = {}", JacksonUtils.writeValueAsString(request));
        return Result.buildEmptySuccess().setTData(itemClientService.selectGroupMealStatus(request));
    }

    @ApiOperation(value = "修改团餐功能当前状态 data: 0:关闭  1：开启, 门店storeGuid")
    @PostMapping("/set_group_meal_status")
    public Result setGroupMealStatus(@RequestBody SingleDataDTO request) {
        logger.info("设置团餐当前状态, 入参 request = {}", JacksonUtils.writeValueAsString(request));
        return Result.buildEmptySuccess().setTData(itemClientService.setGroupMealStatus(request));
    }

    /**
     * 批量修改分类顺序
     *
     * @param typeReqDTOList 里面只有sort和typeGuid
     * @return 1
     */
    @ApiOperation(value = "批量修改分类顺序")
    @PostMapping("/batch_modify_sort")
    public Result<Integer> batchModifySort(@RequestBody List<TypeReqDTO> typeReqDTOList) {
        logger.info("批量修改分类顺序,typeReqDTO={}", JacksonUtils.writeValueAsString(typeReqDTOList));
        return Result.buildSuccessResult(itemClientService.batchModifySort(typeReqDTOList));
    }

    @ApiOperation(value = "查询品牌列表下的所有分类")
    @PostMapping("/query_type_by_brand")
    public List<TypeWebRespDTO> queryTypeByBrand(@RequestBody ItemStringListDTO query) {
        logger.info("[查询品牌列表下的所有分类]query={}", JacksonUtils.writeValueAsString(query));
        return itemClientService.queryTypeByBrand(query);
    }

}
