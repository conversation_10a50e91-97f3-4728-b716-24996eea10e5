package com.holderzone.holder.saas.aggregation.merchant.util;

import com.holderzone.saas.store.dto.report.resp.KeyValueForDate;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/11 17:05
 * @description 日期拆分多个时间段工具类
 */
@Slf4j
public class KeyValueForDateUtil {

    /***
     *  一周的时间戳
     */
    private final static Long weekTime = 604800000L;

    /**
     * 根据一段时间区间，按7天时间拆分成多个时间段
     * @param startDate 开始日期
     * @param endDate  结束日期
     * @return
     */
    @SuppressWarnings("deprecation")
    public static List<KeyValueForDate> getKeyValueForDate(Long startDate, Long endDate) {
        List<KeyValueForDate> list = null;
        try {
            list = new ArrayList<KeyValueForDate>();
            long space =  endDate - startDate;
            Boolean result = true;
            if (space <= weekTime){
                KeyValueForDate keyValueForDate = new KeyValueForDate();
                keyValueForDate.setStartDate(LocalDateTime.ofEpochSecond(startDate/1000,0, ZoneOffset.ofHours(8)));
                keyValueForDate.setEndDate(LocalDateTime.ofEpochSecond(endDate/1000,0, ZoneOffset.ofHours(8)));
                list.add(keyValueForDate);
                return list;
            }else {
                Long endDateNew = startDate;
                while (result){
                   if (endDateNew<endDate){
                       endDateNew +=weekTime;
                       KeyValueForDate keyValueForDate = new KeyValueForDate();
                       if (endDateNew >= endDate){
                           keyValueForDate.setStartDate(LocalDateTime.ofEpochSecond((endDateNew-weekTime)/1000,0, ZoneOffset.ofHours(8)));
                           keyValueForDate.setEndDate(LocalDateTime.ofEpochSecond(endDate/1000,0, ZoneOffset.ofHours(8)));
                           result = false;
                       }else {
                           keyValueForDate.setStartDate(LocalDateTime.ofEpochSecond(startDate/1000,0, ZoneOffset.ofHours(8)));
                           keyValueForDate.setEndDate(LocalDateTime.ofEpochSecond((endDateNew/1000)-1,0, ZoneOffset.ofHours(8)));
                       }
                       list.add(keyValueForDate);
                       startDate = endDateNew;
                   }
                }
            }
        } catch (Exception e) {
            log.error("日期拆分异常",e);
        }

        return list;
    }
}
