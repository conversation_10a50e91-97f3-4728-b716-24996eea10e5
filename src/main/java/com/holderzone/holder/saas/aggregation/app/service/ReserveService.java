package com.holderzone.holder.saas.aggregation.app.service;

import com.holderzone.saas.store.reserve.api.dto.ReserveRecordDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordDetailDTO;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveService
 * @date 2019/05/28 16:21
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public interface ReserveService {

    ReserveRecordDetailDTO launch(ReserveRecordDTO reserveRecordDTO);
}