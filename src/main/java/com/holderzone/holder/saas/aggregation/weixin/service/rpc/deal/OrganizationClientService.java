package com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.QueryStoreDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.organization.StoreParserDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrganizationClientService
 * @date 2019/02/18 10:30
 * @description //TODO
 * @program ${MODULE_NAME}
 */
@Component
@FeignClient(name = "holder-saas-store-organization", fallbackFactory = OrganizationClientService.OrganizationFullback.class)
public interface OrganizationClientService {
    @PostMapping("/store/query_store_by_condition")
    Page<StoreDTO> queryStoreByCondition(QueryStoreDTO queryStoreDTO);

    @PostMapping("/store/query_store_by_guid")
    StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid);

    @PostMapping("/brand/query_list")
    List<BrandDTO> queryBrandList();

    @PostMapping("/brand/query_brand_by_guid")
    BrandDTO queryBrandByGuid(@RequestParam("brandGuid") String brandGuid);

    @PostMapping("/store/query_store_by_city_and_brand")
    List<StoreDTO> queryStoreByCityAndBrand(@RequestBody StoreDTO storeDTO);

    @PostMapping("/store/query_store_by_idlist")
    List<StoreDTO> queryStoreByIdList(List<String> storeGuidList);

	@PostMapping("/store/parse_by_condition")
	List<String> parseByCondition(@RequestBody StoreParserDTO storeParserDTO);

	@ApiOperation(value = "根据门店guid查询门店关联的品牌信息", notes = "若门店未关联到品牌则返回为null，后期一个门店可关联多个品牌")
	@PostMapping("/store/query_brand_by_storeguid")
	BrandDTO queryBrandByStoreGuid(@RequestParam(value = "storeGuid") String storeGuid);

	/**
	 * 根据门店guid查询主机deviceId
	 *
	 * @param storeGuid 门店guid
	 * @return 门店下的主机信息（一体机）
	 */
	@GetMapping("/device/get_master_device_by_storeguid/{storeGuid}")
	StoreDeviceDTO findMasterDevice(@PathVariable("storeGuid") String storeGuid);


    @PostMapping("/store/query_by_condition_no_page")
    List<StoreDTO> queryStoreByConditionNoPage(@RequestBody StoreParserDTO storeParserDTO);

	@Component
    @Slf4j
    class OrganizationFullback implements FallbackFactory<OrganizationClientService> {
        @Override
        public OrganizationClientService create(Throwable throwable) {
            return new OrganizationClientService() {
                @Override
                public Page<StoreDTO> queryStoreByCondition(QueryStoreDTO queryStoreDTO) {
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public StoreDTO queryStoreByGuid(String storeGuid) {
                    throw new RuntimeException("通过门店guid查询门店失败,e:{}" + throwable.getMessage());
                }

                @Override
                public List<BrandDTO> queryBrandList() {
                    throw new RuntimeException("获取品牌列表失败,e:{}" + throwable.getMessage());
                }

                @Override
                public BrandDTO queryBrandByGuid(String brandGuid) {
                    throw new RuntimeException("获取品牌列表失败,e:{}" + throwable.getMessage());
                }

                @Override
                public List<StoreDTO> queryStoreByCityAndBrand(StoreDTO storeDTO) {
                    throw new RuntimeException("通过品牌和城市查询门店列表失败,e:{}" + throwable.getMessage());
                }

                @Override
                public List<StoreDTO> queryStoreByIdList(List<String> storeGuidList) {
                    throw new RuntimeException("通过guidList查询门店信息失败,e:{}" + throwable.getMessage());
                }

				@Override
				public List<String> parseByCondition(StoreParserDTO storeParserDTO) {
					throw new RuntimeException("转换门店信息失败,e:{}" + throwable.getMessage());
				}

				@Override
				public BrandDTO queryBrandByStoreGuid(String storeGuid) {
					throw new RuntimeException("查询品牌失败,e:{}" + throwable.getMessage());
				}

				@Override
				public StoreDeviceDTO findMasterDevice(String storeGuid) {
					throw new RuntimeException("查询品牌失败,e:{}" + throwable.getMessage());
				}

                @Override
                public List<StoreDTO> queryStoreByConditionNoPage(StoreParserDTO storeParserDTO) {
                    throw new RuntimeException("查询门店失败,e:{}" + throwable.getMessage());
                }
            };
        }
    }
}
