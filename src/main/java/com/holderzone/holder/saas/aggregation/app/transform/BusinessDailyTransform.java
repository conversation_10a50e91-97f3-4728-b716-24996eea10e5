package com.holderzone.holder.saas.aggregation.app.transform;


import com.google.common.collect.Lists;
import com.holderzone.holder.saas.aggregation.app.entity.auth.AmountItemDTO;
import com.holderzone.holder.saas.aggregation.app.entity.auth.*;
import com.holderzone.holder.saas.aggregation.app.utils.BigDecimalUtil;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponseConsumpStatis;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponseRechargeStatis;
import com.holderzone.saas.store.dto.order.response.daily.*;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Mapper
public interface BusinessDailyTransform {

    BusinessDailyTransform INSTANCE = Mappers.getMapper(BusinessDailyTransform.class);

    default GoodsSaleDTO itemRespDTO2GoodsSaleDTO(ItemRespDTO itemRespDTO) {
        if (itemRespDTO == null) {
            return null;
        }
        GoodsSaleDTO goodsSaleDTO = new GoodsSaleDTO();
        goodsSaleDTO.setItemType(itemRespDTO.getItemType());
        goodsSaleDTO.setGuid(itemRespDTO.getGuid());
        goodsSaleDTO.setName(itemRespDTO.getName());
        if (Objects.nonNull(itemRespDTO.getUnitPrice())) {
            goodsSaleDTO.setUnitPrice(itemRespDTO.getUnitPrice().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(itemRespDTO.getQuantum())) {
            goodsSaleDTO.setQuantum(itemRespDTO.getQuantum().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(itemRespDTO.getAmount())) {
            goodsSaleDTO.setAmount(itemRespDTO.getAmount().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(itemRespDTO.getDiscountAmount())) {
            goodsSaleDTO.setDiscountAmount(itemRespDTO.getDiscountAmount().stripTrailingZeros().toPlainString());
        }
        goodsSaleDTO.setSkuName(itemRespDTO.getSkuName());
        if (Objects.nonNull(itemRespDTO.getDinnerQuantum())) {
            goodsSaleDTO.setDinnerQuantum(itemRespDTO.getDinnerQuantum().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(itemRespDTO.getDinnerAmount())) {
            goodsSaleDTO.setDinnerAmount(itemRespDTO.getDinnerAmount().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(itemRespDTO.getDinnerDiscountAmount())) {
            goodsSaleDTO.setDinnerDiscountAmount(itemRespDTO.getDinnerDiscountAmount().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(itemRespDTO.getTakeoutQuantum())) {
            goodsSaleDTO.setTakeoutQuantum(itemRespDTO.getTakeoutQuantum().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(itemRespDTO.getTakeoutAmount())) {
            goodsSaleDTO.setTakeoutAmount(itemRespDTO.getTakeoutAmount().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(itemRespDTO.getTakeoutUnitPrice())) {
            goodsSaleDTO.setTakeoutUnitPrice(itemRespDTO.getTakeoutUnitPrice().stripTrailingZeros().toPlainString());
        }
        goodsSaleDTO.setIsTotal(itemRespDTO.getIsTotal());
        goodsSaleDTO.setHasSubInfo(itemRespDTO.getHasSubInfo());
        List<ItemRespDTO> list = itemRespDTO.getSubs();
        goodsSaleDTO.setSubs(itemRespDTO2GoodsSaleDTO(list));
        return goodsSaleDTO;
    }

    List<GoodsSaleDTO> itemRespDTO2GoodsSaleDTO(List<ItemRespDTO> itemRespDTOList);

    default CategorySaleDTO itemRespDTO2CategorySaleDTO(ItemRespDTO itemRespDTO) {
        if (itemRespDTO == null) {
            return null;
        }
        CategorySaleDTO categorySaleDTO = new CategorySaleDTO();
        categorySaleDTO.setIsTotal(itemRespDTO.getIsTotal());
        categorySaleDTO.setName(itemRespDTO.getName());
        if (Objects.nonNull(itemRespDTO.getQuantum())) {
            categorySaleDTO.setQuantum(itemRespDTO.getQuantum().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(itemRespDTO.getAmount())) {
            categorySaleDTO.setAmount(itemRespDTO.getAmount().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(itemRespDTO.getDiscountAmount())) {
            categorySaleDTO.setDiscountAmount(itemRespDTO.getDiscountAmount().stripTrailingZeros().toPlainString());
        }
        return categorySaleDTO;
    }


    List<CategorySaleDTO> itemRespDTO2CategorySaleDTO(List<ItemRespDTO> itemRespDTOList);

    default PropStatsSaleDTO propStatsDTO2PropStatsSaleDTO(PropStatsDTO attrResp) {
        if (attrResp == null) {
            return null;
        }
        PropStatsSaleDTO propStatsSaleDTO = new PropStatsSaleDTO();
        if (Objects.nonNull(attrResp.getTotalMoney())) {
            propStatsSaleDTO.setTotalMoney(attrResp.getTotalMoney().stripTrailingZeros().toPlainString());
        }
        propStatsSaleDTO.setTotalQuantity(String.valueOf(attrResp.getTotalQuantity()));
        List<PropStatsDTO.PropGroup> propGroupList = attrResp.getPropGroupList();
        if (CollectionUtils.isEmpty(propGroupList)) {
            propStatsSaleDTO.setPropGroupList(Lists.newArrayList());
            return propStatsSaleDTO;
        }
        List<PropStatsSaleDTO.PropGroup> propGroups = propGroupList.stream().map(e -> {
            PropStatsSaleDTO.PropGroup propGroup = new PropStatsSaleDTO.PropGroup();
            propGroup.setName(e.getName());
            if (Objects.nonNull(e.getGroupMoney())) {
                propGroup.setGroupMoney(e.getGroupMoney().stripTrailingZeros().toPlainString());
            }
            propGroup.setGroupQuantity(String.valueOf(e.getGroupQuantity()));
            List<PropStatsDTO.PropItem> propList = e.getPropList();
            if (CollectionUtils.isEmpty(propList)) {
                propGroup.setPropList(Lists.newArrayList());
                return propGroup;
            }
            propGroup.setPropList(propItemList2PropSaleItemList(propList));
            return propGroup;
        }).collect(Collectors.toList());
        propStatsSaleDTO.setPropGroupList(propGroups);
        return propStatsSaleDTO;
    }

    default List<PropStatsSaleDTO.PropItem> propItemList2PropSaleItemList(List<PropStatsDTO.PropItem> propList) {
        return propList.stream().map(pi -> {
            PropStatsSaleDTO.PropItem propItem = new PropStatsSaleDTO.PropItem();
            propItem.setName(pi.getName());
            propItem.setQuantity(String.valueOf(pi.getQuantity()));
            if (Objects.nonNull(pi.getUnitPrice())) {
                propItem.setUnitPrice(pi.getUnitPrice().stripTrailingZeros().toPlainString());
            }
            if (Objects.nonNull(pi.getMoney())) {
                propItem.setMoney(pi.getMoney().stripTrailingZeros().toPlainString());
            }
            if (CollectionUtils.isNotEmpty(pi.getPropList())) {
                propItem.setPropList(propItemList2PropSaleItemList(pi.getPropList()));
            }
            return propItem;
        }).collect(Collectors.toList());
    }

    default ReturnSaleDTO itemRespDTO2ReturnSaleDTO(ItemRespDTO itemRespDTO) {
        if (itemRespDTO == null) {
            return null;
        }
        ReturnSaleDTO returnSaleDTO = new ReturnSaleDTO();
        returnSaleDTO.setItemType(itemRespDTO.getItemType());
        returnSaleDTO.setName(itemRespDTO.getName());
        returnSaleDTO.setSkuName(itemRespDTO.getSkuName());
        if (Objects.nonNull(itemRespDTO.getUnitPrice())) {
            returnSaleDTO.setUnitPrice(itemRespDTO.getUnitPrice().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(itemRespDTO.getQuantum())) {
            returnSaleDTO.setQuantum(itemRespDTO.getQuantum().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(itemRespDTO.getAmount())) {
            returnSaleDTO.setAmount(itemRespDTO.getAmount().stripTrailingZeros().toPlainString());
        }
        returnSaleDTO.setIsTotal(itemRespDTO.getIsTotal());
        returnSaleDTO.setHasSubInfo(itemRespDTO.getHasSubInfo());
        List<ItemRespDTO> list = itemRespDTO.getSubs();
        returnSaleDTO.setSubs(itemRespDTO2ReturnSaleDTO(list));
        return returnSaleDTO;
    }

    List<ReturnSaleDTO> itemRespDTO2ReturnSaleDTO(List<ItemRespDTO> itemRespDTOList);


    default GiftSaleDTO itemRespDTO2GiftSaleDTO(ItemRespDTO itemRespDTO) {
        if (itemRespDTO == null) {
            return null;
        }
        GiftSaleDTO giftSaleDTO = new GiftSaleDTO();
        giftSaleDTO.setItemType(itemRespDTO.getItemType());
        giftSaleDTO.setName(itemRespDTO.getName());
        giftSaleDTO.setSkuName(itemRespDTO.getSkuName());
        if (Objects.nonNull(itemRespDTO.getUnitPrice())) {
            giftSaleDTO.setUnitPrice(itemRespDTO.getUnitPrice().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(itemRespDTO.getQuantum())) {
            giftSaleDTO.setQuantum(itemRespDTO.getQuantum().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(itemRespDTO.getAmount())) {
            giftSaleDTO.setAmount(itemRespDTO.getAmount().stripTrailingZeros().toPlainString());
        }
        giftSaleDTO.setIsTotal(itemRespDTO.getIsTotal());
        giftSaleDTO.setHasSubInfo(itemRespDTO.getHasSubInfo());
        List<ItemRespDTO> list = itemRespDTO.getSubs();
        giftSaleDTO.setSubs(itemRespDTO2GiftSaleDTO(list));
        return giftSaleDTO;
    }

    List<GiftSaleDTO> itemRespDTO2GiftSaleDTO(List<ItemRespDTO> itemRespDTOList);

    default MemberConsumeSaleDTO responseConsumpStatis2MemberConsumeSaleDTO(ResponseConsumpStatis responseConsumpStatis) {
        if (responseConsumpStatis == null) {
            return null;
        }
        MemberConsumeSaleDTO memberConsumeSaleDTO = new MemberConsumeSaleDTO();
        if (Objects.nonNull(responseConsumpStatis.getConsumptionNum())) {
            memberConsumeSaleDTO.setConsumptionNum(String.valueOf(responseConsumpStatis.getConsumptionNum()));
        }
        if (Objects.nonNull(responseConsumpStatis.getConsumptionMemberNum())) {
            memberConsumeSaleDTO.setConsumptionMemberNum(String.valueOf(responseConsumpStatis.getConsumptionMemberNum()));
        }
        if (Objects.nonNull(responseConsumpStatis.getConsumptionAmount())) {
            memberConsumeSaleDTO.setConsumptionAmount(responseConsumpStatis.getConsumptionAmount().stripTrailingZeros().toPlainString());
        }
        memberConsumeSaleDTO.setPayWayDetailList(responseConsumpStatis.getPayWayDetailList());
        return memberConsumeSaleDTO;
    }

    default MemberRechargeSaleDTO responseRechargeStatis2MemberRechargeSaleDTO(ResponseRechargeStatis responseRechargeStatis) {
        if (responseRechargeStatis == null) {
            return null;
        }
        MemberRechargeSaleDTO memberRechargeSaleDTO = new MemberRechargeSaleDTO();
        if (Objects.nonNull(responseRechargeStatis.getRechargeNum())) {
            memberRechargeSaleDTO.setRechargeNum(String.valueOf(responseRechargeStatis.getRechargeNum()));
        }
        if (Objects.nonNull(responseRechargeStatis.getRechargeMemberNum())) {
            memberRechargeSaleDTO.setRechargeMemberNum(String.valueOf(responseRechargeStatis.getRechargeMemberNum()));
        }
        if (Objects.nonNull(responseRechargeStatis.getRechargeAmount())) {
            memberRechargeSaleDTO.setRechargeAmount(responseRechargeStatis.getRechargeAmount().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(responseRechargeStatis.getPresentAmount())) {
            memberRechargeSaleDTO.setPresentAmount(responseRechargeStatis.getPresentAmount().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(responseRechargeStatis.getIncomeAmount())) {
            memberRechargeSaleDTO.setIncomeAmount(responseRechargeStatis.getIncomeAmount().stripTrailingZeros().toPlainString());
        }
        memberRechargeSaleDTO.setPayWayDetailList(responseRechargeStatis.getPayWayDetailList());
        return memberRechargeSaleDTO;
    }


    default OverviewSaleDTO overview2OverviewSaleDTO(OverviewRespDTO overview) {
        if (overview == null) {
            return null;
        }
        OverviewSaleDTO overviewSaleDTO = new OverviewSaleDTO();
        if (Objects.nonNull(overview.getOrderCount())) {
            overviewSaleDTO.setOrderCount(String.valueOf(overview.getOrderCount()));
        }
        if (Objects.nonNull(overview.getGuestCount())) {
            overviewSaleDTO.setGuestCount(String.valueOf(overview.getGuestCount()));
        }
        if (Objects.nonNull(overview.getDineInGuestCount())) {
            overviewSaleDTO.setDineInGuestCount(String.valueOf(overview.getDineInGuestCount()));
        }
        if (Objects.nonNull(overview.getOccupancyRatePercent())) {
            overviewSaleDTO.setOccupancyRatePercent(String.valueOf(overview.getOccupancyRatePercent()));
        }
        if (Objects.nonNull(overview.getOpenTableRatePercent())) {
            overviewSaleDTO.setOpenTableRatePercent(String.valueOf(overview.getOpenTableRatePercent()));
        }
        if (Objects.nonNull(overview.getFlipTableRatePercent())) {
            overviewSaleDTO.setFlipTableRatePercent(String.valueOf(overview.getFlipTableRatePercent()));
        }
        if (Objects.nonNull(overview.getAvgDineInTime())) {
            overviewSaleDTO.setAvgDineInTime(String.valueOf(overview.getAvgDineInTime()));
        }
        if (Objects.nonNull(overview.getConsumerAmount())) {
            overviewSaleDTO.setConsumerAmount(overview.getConsumerAmount().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(overview.getGatherAmount())) {
            overviewSaleDTO.setGatherAmount(overview.getGatherAmount().stripTrailingZeros().toPlainString());
        }
        overviewSaleDTO.setGatherItems(gatherItemsDTO2AppGatherItems(overview.getGatherItems()));
        if (Objects.nonNull(overview.getDiscountAmount())) {
            overviewSaleDTO.setDiscountAmount(overview.getDiscountAmount().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(overview.getEstimatedAmount())) {
            overviewSaleDTO.setEstimatedAmount(overview.getEstimatedAmount().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(overview.getRechargeMoney())) {
            overviewSaleDTO.setRechargeMoney(overview.getRechargeMoney().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(overview.getRefundAmount())) {
            overviewSaleDTO.setRefundAmount(overview.getRefundAmount().stripTrailingZeros().toPlainString());
        }
        overviewSaleDTO.setDiscountItems(gatherItemsDTO2DiscountItemsDTO(overview.getDiscountItems()));
        overviewSaleDTO.setCheckoutStaffs(overview.getCheckoutStaffs());
        return overviewSaleDTO;
    }

    default DiscountAmountItemDTO gatherItemDTO2DiscountItemDTO(com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO gatherItem) {
        if (ObjectUtils.isEmpty(gatherItem)) {
            return null;
        }
        DiscountAmountItemDTO discountAmountItemDTO = new DiscountAmountItemDTO();
        discountAmountItemDTO.setCode(gatherItem.getCode());
        discountAmountItemDTO.setName(gatherItem.getName());
        discountAmountItemDTO.setAmount(gatherItem.getAmount().stripTrailingZeros().toPlainString());
        discountAmountItemDTO.setDiscountAmount(gatherItem.getDiscountAmount());
        discountAmountItemDTO.setCount(gatherItem.getCount());
        discountAmountItemDTO.setSort(gatherItem.getSort());
        List<DiscountAmountItemDTO.InnerDetails> innerDetails = getDiscountInnerDetails(gatherItem.getInnerDetails());
        discountAmountItemDTO.setInnerDetails(innerDetails);
        if (BigDecimalUtil.greaterThanZero(gatherItem.getEstimatedAmount())) {
            discountAmountItemDTO.setEstimatedAmount(gatherItem.getEstimatedAmount().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(gatherItem.getOrderCount())) {
            discountAmountItemDTO.setOrderCount(gatherItem.getOrderCount().toString());
        }
        return discountAmountItemDTO;
    }

    default List<DiscountAmountItemDTO.InnerDetails> getDiscountInnerDetails(List<com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO.InnerDetails> innerDetailList) {
        List<DiscountAmountItemDTO.InnerDetails> innerDetails = new ArrayList<>();
        if (org.springframework.util.CollectionUtils.isEmpty(innerDetailList)) {
            return innerDetails;
        }
        innerDetailList.forEach(innerDetails1 -> {
            DiscountAmountItemDTO.InnerDetails innerDetails2 = new DiscountAmountItemDTO.InnerDetails();
            innerDetails2.setName(innerDetails1.getName());
            innerDetails2.setAmount(innerDetails1.getAmount());
            innerDetails2.setCount(innerDetails1.getCount());
            if (!org.springframework.util.CollectionUtils.isEmpty(innerDetails1.getInnerDetails())) {
                innerDetails2.setSubInnerDetails(getDiscountInnerDetails(innerDetails1.getInnerDetails()));
            }
            innerDetails.add(innerDetails2);
        });
        return innerDetails;
    }

    default List<DiscountAmountItemDTO> gatherItemsDTO2DiscountItemsDTO(List<com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO> discountItems) {
        if (org.springframework.util.CollectionUtils.isEmpty(discountItems)) {
            return null;
        }

        List<DiscountAmountItemDTO> list = new ArrayList<DiscountAmountItemDTO>(discountItems.size());
        discountItems.forEach(gatherItem -> {
            list.add(gatherItemDTO2DiscountItemDTO(gatherItem));
        });

        return list;
    }

    default AmountItemDTO gatherItemDTO2AppGatherItem(com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO gatherItem) {
        if (ObjectUtils.isEmpty(gatherItem)) {
            return null;
        }
        AmountItemDTO amountItemDTO = new AmountItemDTO();
        amountItemDTO.setCode(gatherItem.getCode());
        amountItemDTO.setName(gatherItem.getName());
        amountItemDTO.setAmount(gatherItem.getAmount().stripTrailingZeros().toPlainString());
        amountItemDTO.setDiscountAmount(gatherItem.getDiscountAmount());
        amountItemDTO.setCount(gatherItem.getCount());
        if (BigDecimalUtil.greaterThanZero(gatherItem.getExcessAmount())) {
            amountItemDTO.setExcessAmount(gatherItem.getExcessAmount().stripTrailingZeros().toPlainString());
        }
        amountItemDTO.setSort(gatherItem.getSort());
        List<AmountItemDTO.InnerDetails> innerDetails = getInnerDetails(gatherItem.getInnerDetails());
        amountItemDTO.setInnerDetails(innerDetails);
        if (BigDecimalUtil.greaterThanZero(gatherItem.getEstimatedAmount())) {
            amountItemDTO.setEstimatedAmount(gatherItem.getEstimatedAmount().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(gatherItem.getOrderCount())) {
            amountItemDTO.setOrderCount(gatherItem.getOrderCount().toString());
        }
        if (Objects.nonNull(gatherItem.getGrouponCount())) {
            amountItemDTO.setGrouponCount(gatherItem.getGrouponCount().toString());
        }
        amountItemDTO.setIsGroupon(gatherItem.getIsGroupon());
        return amountItemDTO;
    }

    static @NotNull List<AmountItemDTO.InnerDetails> getInnerDetails(List<com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO.InnerDetails> innerDetailList) {
        List<AmountItemDTO.InnerDetails> innerDetails = new ArrayList<>();
        if (org.springframework.util.CollectionUtils.isEmpty(innerDetailList)) {
            return innerDetails;
        }
        innerDetailList.forEach(innerDetails1 -> {
            AmountItemDTO.InnerDetails innerDetails2 = new AmountItemDTO.InnerDetails();
            innerDetails2.setName(innerDetails1.getName());
            innerDetails2.setAmount(innerDetails1.getAmount());
            innerDetails2.setDiscountAmount(innerDetails1.getDiscountAmount());
            innerDetails2.setCount(innerDetails1.getCount());
            if (!org.springframework.util.CollectionUtils.isEmpty(innerDetails1.getInnerDetails())) {
                innerDetails2.setSubInnerDetails(getInnerDetails(innerDetails1.getInnerDetails()));
            }
            if (Objects.nonNull(innerDetails1.getOrderCount())) {
                innerDetails2.setOrderCount(innerDetails1.getOrderCount().toString());
            }
            if (Objects.nonNull(innerDetails1.getGrouponCount())) {
                innerDetails2.setGrouponCount(innerDetails1.getGrouponCount().toString());
            }
            innerDetails2.setIsGroupon(innerDetails1.getIsGroupon());
            innerDetails.add(innerDetails2);
        });
        return innerDetails;
    }

    default List<AmountItemDTO> gatherItemsDTO2AppGatherItems(List<com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO> gatherItems) {
        if (org.springframework.util.CollectionUtils.isEmpty(gatherItems)) {
            return null;
        }

        List<AmountItemDTO> list = new ArrayList<AmountItemDTO>(gatherItems.size());
        gatherItems.forEach(gatherItem -> {
            list.add(gatherItemDTO2AppGatherItem(gatherItem));
        });

        return list;
    }


    default DiningTypeSaleDTO diningTypeRespList2DiningTypeSaleDTO(DiningTypeRespDTO diningTypeRespDTO) {
        if (diningTypeRespDTO == null) {
            return null;
        }
        DiningTypeSaleDTO diningTypeSaleDTO = new DiningTypeSaleDTO();
        if (Objects.nonNull(diningTypeRespDTO.getTypeCode())) {
            diningTypeSaleDTO.setTypeCode(String.valueOf(diningTypeRespDTO.getTypeCode()));
        }
        diningTypeSaleDTO.setTypeName(diningTypeRespDTO.getTypeName());
        if (Objects.nonNull(diningTypeRespDTO.getOrderCount())) {
            diningTypeSaleDTO.setOrderCount(String.valueOf(diningTypeRespDTO.getOrderCount()));
        }
        if (Objects.nonNull(diningTypeRespDTO.getGuestCount())) {
            diningTypeSaleDTO.setGuestCount(String.valueOf(diningTypeRespDTO.getGuestCount()));
        }
        if (Objects.nonNull(diningTypeRespDTO.getAmount())) {
            diningTypeSaleDTO.setAmount(diningTypeRespDTO.getAmount().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(diningTypeRespDTO.getOrderPrice())) {
            diningTypeSaleDTO.setOrderPrice(diningTypeRespDTO.getOrderPrice().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(diningTypeRespDTO.getGuestPrice())) {
            diningTypeSaleDTO.setGuestPrice(diningTypeRespDTO.getGuestPrice().stripTrailingZeros().toPlainString());
        }
        diningTypeSaleDTO.setSubDiningTypes(diningTypeRespList2DiningTypeSaleDTO(diningTypeRespDTO.getSubDiningTypes()));
        diningTypeSaleDTO.setIsTotal(diningTypeRespDTO.getIsTotal());
        return diningTypeSaleDTO;
    }

    List<DiningTypeSaleDTO> diningTypeRespList2DiningTypeSaleDTO(List<DiningTypeRespDTO> diningTypeRespList);

    default GatherSaleDTO gatherRespDTOList2GatherSaleDTO(GatherRespDTO gatherRespDTO) {
        if (gatherRespDTO == null) {
            return null;
        }
        GatherSaleDTO gatherSaleDTO = new GatherSaleDTO();
        gatherSaleDTO.setGatherCode(gatherRespDTO.getGatherCode());
        gatherSaleDTO.setGatherName(gatherRespDTO.getGatherName());
        if (Objects.nonNull(gatherRespDTO.getConsumerAmount())) {
            gatherSaleDTO.setConsumerAmount(gatherRespDTO.getConsumerAmount().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(gatherRespDTO.getExcessAmount())) {
            gatherSaleDTO.setExcessAmount(gatherRespDTO.getExcessAmount().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(gatherRespDTO.getPrepaidAmount())) {
            gatherSaleDTO.setPrepaidAmount(gatherRespDTO.getPrepaidAmount().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(gatherRespDTO.getReserveAmount())) {
            gatherSaleDTO.setReserveAmount(gatherRespDTO.getReserveAmount().stripTrailingZeros().toPlainString());
        }
        gatherSaleDTO.setIsTotal(gatherRespDTO.getIsTotal());
        return gatherSaleDTO;
    }

    default List<GatherSaleDTO> gatherRespDTOList2GatherSaleDTO(List<GatherRespDTO> gatherRespDTOList) {
        if (gatherRespDTOList == null) {
            return null;
        }
        List<GatherSaleDTO> result = Lists.newArrayList();
        if (gatherRespDTOList.isEmpty()) {
            return result;
        }

        for (GatherRespDTO gatherRespDTO : gatherRespDTOList) {
            GatherSaleDTO gatherSaleDTO = new GatherSaleDTO();
            gatherSaleDTO.setGatherCode(gatherRespDTO.getGatherCode());
            gatherSaleDTO.setGatherName(gatherRespDTO.getGatherName());
            if (Objects.nonNull(gatherRespDTO.getConsumerAmount())) {
                gatherSaleDTO.setConsumerAmount(gatherRespDTO.getConsumerAmount().stripTrailingZeros().toPlainString());
            }
            if (Objects.nonNull(gatherRespDTO.getExcessAmount())) {
                gatherSaleDTO.setExcessAmount(gatherRespDTO.getExcessAmount().stripTrailingZeros().toPlainString());
            }
            if (Objects.nonNull(gatherRespDTO.getPrepaidAmount())) {
                gatherSaleDTO.setPrepaidAmount(gatherRespDTO.getPrepaidAmount().stripTrailingZeros().toPlainString());
            }
            if (Objects.nonNull(gatherRespDTO.getReserveAmount())) {
                gatherSaleDTO.setReserveAmount(gatherRespDTO.getReserveAmount().stripTrailingZeros().toPlainString());
            }
            gatherSaleDTO.setIsTotal(gatherRespDTO.getIsTotal());
            gatherSaleDTO.setInnerDetails(gatherRespDTO.getInnerDetails());
            result.add(gatherSaleDTO);
        }
        return result;
    }

    List<RefundSaleAmountDTO> refundAmountDTOList2RefundSaleAmountDTOList(List<RefundAmountDTO> refundAmountDTOList);

    default RefundSaleDTO refundRespDTO2RefundSaleDTO(RefundRespDTO refundRespDTO) {
        if (refundRespDTO == null) {
            return null;
        }
        RefundSaleDTO refundSaleDTO = new RefundSaleDTO();
        if (Objects.nonNull(refundRespDTO.getCheckoutStaffs())) {
            refundSaleDTO.setCheckoutStaffs(refundRespDTO.getCheckoutStaffs());
        }
        if (Objects.nonNull(refundRespDTO.getRefundAmounts())) {
            refundSaleDTO.setRefundAmounts(refundAmountDTOList2RefundSaleAmountDTOList(refundRespDTO.getRefundAmounts()));
        }
        return refundSaleDTO;
    }
}
