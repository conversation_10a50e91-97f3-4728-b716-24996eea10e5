package com.holderzone.holder.saas.aggregation.merchant.controller.erp;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp.MaterialCategoryFeignService;
import com.holderzone.saas.store.dto.erp.*;
import com.holderzone.saas.store.dto.erp.util.Add;
import com.holderzone.saas.store.dto.erp.util.Update;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/05 11:15
 */
@RestController
@RequestMapping("materialCategory")
@Api(tags = "物料分类Api")
public class MaterialCategoryController {
    private static final Logger LOGGER = LoggerFactory.getLogger(MaterialCategoryController.class);
    @Autowired
    private MaterialCategoryFeignService materialCategoryFeignService;

    @PostMapping("add")
    @ApiOperation("添加物料分类")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "添加物料分类")
    public Result<Object> add(@RequestBody @Validated(Add.class) MaterialCategoryDTO materialCategoryDTO) {
        LOGGER.info("聚合层添加物料分类入参:->{}", JacksonUtils.writeValueAsString(materialCategoryDTO));
        materialCategoryFeignService.add(materialCategoryDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("update")
    @ApiOperation("修改物料分类")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "修改物料分类")
    public Result<Object> update(@RequestBody @Validated(Update.class) MaterialCategoryDTO materialCategoryDTO) {
        LOGGER.info("聚合层修改物料分类入参:->{}", JacksonUtils.writeValueAsString(materialCategoryDTO));
        materialCategoryFeignService.update(materialCategoryDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("findByGuid")
    @ApiOperation("根据分类GUID查询分类信息")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "根据分类GUID查询分类信息")
    public Result<MaterialCategoryDTO> findByGuid(@RequestBody MaterialCategoryDTO materialCategoryDTO) {
        LOGGER.info("聚合层根据分类GUID查询分类信息入参:->{}", JacksonUtils.writeValueAsString(materialCategoryDTO));
        if (!StringUtils.isEmpty(materialCategoryDTO.getGuid())) {
            return Result.buildSuccessResult(materialCategoryFeignService.findByGuid(materialCategoryDTO.getGuid()));
        }
        return Result.buildEmptySuccess();
    }

    @PostMapping("findByCondition")
    @ApiOperation("条件查询分类信息列表,分页")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "条件查询分类信息列表,分页")
    public Result<Page<MaterialCategoryDTO>> findByCondition(@RequestBody MaterialCategoryQueryDTO queryDTO) {
        LOGGER.info("条件查询分类信息列表入参:->{}", JacksonUtils.writeValueAsString(queryDTO));
        return Result.buildSuccessResult(materialCategoryFeignService.findByCondition(queryDTO));
    }

    @PostMapping("list")
    @ApiOperation("查询所有的分类信息,不包括物料")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "查询所有的分类信息,不包括物料")
    public Result<List<MaterialCategoryDTO>> findList(@RequestBody CategoryListQueryDTO categoryListQueryDTO) {
        return Result.buildSuccessResult(materialCategoryFeignService.findList(categoryListQueryDTO));
    }

    @PostMapping("countCategory")
    @ApiOperation("根据分类GUID统计某分类下的物料使用数量")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "根据分类GUID统计某分类下的物料使用数量")
    public Result<Long> countCategory(@RequestBody MaterialCategoryDTO materialCategoryDTO) {
        LOGGER.info("聚合层根据分类GUID统计某分类下的物料使用数量入参:->{}", JacksonUtils.writeValueAsString(materialCategoryDTO));
        if (!StringUtils.isEmpty(materialCategoryDTO.getGuid())) {
            return Result.buildSuccessResult(materialCategoryFeignService.countCategory(materialCategoryDTO.getGuid()));
        }
        return Result.buildEmptySuccess();
    }

    @PostMapping("listCategory")
    @ApiOperation("查询所有的分类物料信息,包括物料")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "查询所有的分类物料信息,包括物料")
    public Result<List<CategoryDTO>> findCategoryAndMaterial(@RequestBody CategoryListQueryDTO categoryListQueryDTO) {
        return Result.buildSuccessResult(materialCategoryFeignService.findCategoryAndMaterial(categoryListQueryDTO));
    }

    @PostMapping("delete")
    @ApiOperation("删除物料分类")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "删除物料分类")
    public Result<Object> delete(@RequestBody MaterialCategoryDTO materialCategoryDTO) {
        LOGGER.info("聚合层删除物料分类入参:->{}", JacksonUtils.writeValueAsString(materialCategoryDTO));
        if (!StringUtils.isEmpty(materialCategoryDTO.getGuid())) {
            materialCategoryFeignService.delete(materialCategoryDTO.getGuid());
        }
        return Result.buildEmptySuccess();
    }
}
