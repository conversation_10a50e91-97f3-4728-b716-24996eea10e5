package com.holderzone.holder.saas.aggregation.merchant.service.rpc.business;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.business.SystemDiscountReqDTO;
import com.holderzone.saas.store.dto.trade.SystemDiscountDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SystemDiscountClientService
 * @date 2018/09/12 17:43
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-store-business", fallbackFactory = SystemDiscountClientService.SystemDiscountFallBack.class)
public interface SystemDiscountClientService {

    @PostMapping("/system/add")
    String addSystemDiscount(@RequestBody SystemDiscountDTO systemDiscountDTO);

    @PostMapping("/system/getAll/{storeGuid}")
    List<SystemDiscountDTO> queryAllSystemDis(@PathVariable("storeGuid") String storeGuid);

    @PostMapping("/system/update")
    String update(@RequestBody SystemDiscountDTO systemDiscountDTO);

    @PostMapping("/system/delete/{storeGuid}/{systemDiscountGuid}")
    String delete(@PathVariable("storeGuid") String storeGuid, @PathVariable("systemDiscountGuid") String systemDiscountGuid);

    @PostMapping("/system/get_sys_discount")
    SystemDiscountDTO getByStoreGuid(@RequestBody SystemDiscountReqDTO systemDiscountDTO);

    @PostMapping("/system/save_or_update")
    SystemDiscountDTO saveOrUpdate(@RequestBody SystemDiscountReqDTO systemDiscountDTO);

    @Component
    class SystemDiscountFallBack implements FallbackFactory<SystemDiscountClientService> {

        private static final Logger logger = LoggerFactory.getLogger(SystemDiscountFallBack.class);

        @Override
        public SystemDiscountClientService create(Throwable throwable) {
            return new SystemDiscountClientService() {
                @Override
                public String addSystemDiscount(SystemDiscountDTO systemDiscountDTO) {
                    logger.error("新增系统折扣异常 e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("新增系统折扣异常 e=" + throwable.getMessage());
                }

                @Override
                public List<SystemDiscountDTO> queryAllSystemDis(String storeGuid) {
                    logger.error("查询所有 e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("查询所有 e=" + throwable.getMessage());
                }

                @Override
                public String update(SystemDiscountDTO systemDiscountDTO) {
                    logger.error("更新异常 e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("更新异常 e=" + throwable.getMessage());
                }

                @Override
                public String delete(String storeGuid, String systemDiscountGuid) {
                    logger.error("删除系统折扣异常 e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("删除系统折扣异常 e=" + throwable.getMessage());
                }

                @Override
                public SystemDiscountDTO getByStoreGuid(SystemDiscountReqDTO systemDiscountDTO) {
                    logger.error("商超-获取系统省零异常 e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("商超-获取系统省零异常 e=" + throwable.getMessage());
                }

                @Override
                public SystemDiscountDTO saveOrUpdate(SystemDiscountReqDTO systemDiscountDTO) {
                    logger.error("商超-保存系统省零异常 e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("商超-保存系统省零异常 e=" + throwable.getMessage());
                }
            };
        }
    }


}
