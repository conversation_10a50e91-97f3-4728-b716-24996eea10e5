package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.weixin.menu.WxMenuUrlDTO;
import com.holderzone.saas.store.dto.weixin.req.*;
import com.holderzone.saas.store.dto.weixin.resp.WxConfigRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxSubjectRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxMpMessageHandlerClientService
 * @date 2019/04/01 15:07
 * @description 微信公众号消息处理Client
 * @program holder-saas-store
 */
@Component
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = WxMpMessageHandlerClientService.MessageHandleFallBack.class)
public interface WxMpMessageHandlerClientService {

    @PostMapping("/wx_mp/verify")
    String verify(WxCommonReqDTO wxCommonReqDTO);

    @PostMapping("/wx_mp/get_user_info")
    String getUserInfo(WxAuthorizeReqDTO wxAuthorizeReqDTO);

    @PostMapping("/wx_handler/shop_list")
    String shopList(WxPreCodReqDTO wxPreCodReqDTO);

    @PostMapping("/wx_handler/wx_config")
    WxConfigRespDTO getWxConfig(WxPortalReqDTO wxPortalReqDTO);

    @PostMapping("/wx_handler/get_wx_subject")
    WxSubjectRespDTO getWxSubject(@RequestBody WxSubjectReqDTO wxSubjectReqDTO);

    @GetMapping("/wx_table_stick/qr_redirect")
    String qrRedirect(@RequestParam("enterpriseTable") String enterpriseTable);

    @PostMapping("/wx_handler/menu_authorize_url/boss")
    String getBossAuthorizeUrl(@RequestBody WxMenuUrlDTO wxMenuUrlDTO);

    @PostMapping("/wx_handler/menu_redirect_url/boss")
    String getBossRedirectUrl(@RequestBody WxAuthorizeReqDTO wxAuthorizeReqDTO);

    @PostMapping("/wx_handler/save/boss/token")
    void saveBossAuthToken(@RequestBody WxMenuUrlDTO wxMenuUrlDTO);

    @PostMapping("/wx_handler/clean/boss/token")
    void cleanBossAuthToken(@RequestBody WxMenuUrlDTO wxMenuUrlDTO);

    @Slf4j
    @Component
    class MessageHandleFallBack implements FallbackFactory<WxMpMessageHandlerClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public WxMpMessageHandlerClientService create(Throwable throwable) {
            return new WxMpMessageHandlerClientService() {
                @Override
                public String verify(WxCommonReqDTO wxCommonReqDTO) {
                    log.error(HYSTRIX_PATTERN, "verify", wxCommonReqDTO, throwable);
                    return "验证失败";
                }

                @Override
                public String getUserInfo(WxAuthorizeReqDTO wxAuthorizeReqDTO) {
                    log.error(HYSTRIX_PATTERN, "getUserInfo", wxAuthorizeReqDTO, throwable);
                    return null;
                }

                @Override
                public String shopList(WxPreCodReqDTO wxPreCodReqDTO) {
                    log.error(HYSTRIX_PATTERN, "shopList", wxPreCodReqDTO, throwable);
                    return null;
                }

                @Override
                public WxConfigRespDTO getWxConfig(WxPortalReqDTO wxPortalReqDTO) {
                    return null;
                }

                @Override
                public WxSubjectRespDTO getWxSubject(WxSubjectReqDTO wxSubjectReqDTO) {
                    log.info(HYSTRIX_PATTERN, "wxSubjectReqDTO", JacksonUtils.writeValueAsString(wxSubjectReqDTO), throwable.getCause());
                    throw new BusinessException("获取微信主体配置失败，e:{}", throwable.getCause());
                }

                @Override
                public String qrRedirect(String enterpriseTable) {
                    log.info(HYSTRIX_PATTERN, "enterpriseTable", enterpriseTable, throwable.getCause());
                    throw new BusinessException("扫码二维码异常，e:{}", throwable.getCause());
                }

                @Override
                public String getBossAuthorizeUrl(WxMenuUrlDTO wxMenuUrlDTO) {
                    log.info(HYSTRIX_PATTERN, "getBossAuthorizeUrl", JacksonUtils.writeValueAsString(wxMenuUrlDTO), throwable.getCause());
                    throw new BusinessException("获取老板助手授权地址异常，e:{}", throwable.getCause());
                }

                @Override
                public String getBossRedirectUrl(WxAuthorizeReqDTO wxAuthorizeReqDTO) {
                    log.info(HYSTRIX_PATTERN, "getBossRedirectUrl", JacksonUtils.writeValueAsString(wxAuthorizeReqDTO), throwable.getCause());
                    throw new BusinessException("获取老板助手重定向地址异常，e:{}", throwable.getCause());
                }

                @Override
                public void saveBossAuthToken(WxMenuUrlDTO wxMenuUrlDTO) {
                    log.info(HYSTRIX_PATTERN, "saveBossAuthToken", JacksonUtils.writeValueAsString(wxMenuUrlDTO), throwable.getCause());
                    throw new BusinessException("保存老板助手token异常，e:{}", throwable.getCause());
                }

                @Override
                public void cleanBossAuthToken(WxMenuUrlDTO wxMenuUrlDTO) {
                    log.info(HYSTRIX_PATTERN, "cleanBossAuthToken", JacksonUtils.writeValueAsString(wxMenuUrlDTO), throwable.getCause());
                    throw new BusinessException("清除老板助手token异常，e:{}", throwable.getCause());
                }
            };
        }
    }
}
