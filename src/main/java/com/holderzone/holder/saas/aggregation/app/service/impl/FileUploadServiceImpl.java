package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.framework.base.dto.file.FileDto;
import com.holderzone.framework.security.SecurityManager;
import com.holderzone.holder.saas.aggregation.app.entity.FileUploadDTO;
import com.holderzone.holder.saas.aggregation.app.execption.FileIllegalException;
import com.holderzone.holder.saas.aggregation.app.service.FileUploadService;
import com.holderzone.holder.saas.aggregation.app.service.feign.BaseFeignService;
import com.holderzone.holder.saas.aggregation.app.utils.UploadValidateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/26 11:44
 * @className: FileUploadServiceImpl
 */
@Slf4j
@Service
@AllArgsConstructor
public class FileUploadServiceImpl implements FileUploadService {

    private final BaseFeignService baseService;

    public static String getRandomFileName() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 5);
    }

    private static double getAccuracy(long size) {
        double accuracy;
        if (size < 900) {
            accuracy = 0.85;
        } else if (size < 2047) {
            accuracy = 0.6;
        } else if (size < 3275) {
            accuracy = 0.44;
        } else {
            accuracy = 0.4;
        }
        return accuracy;
    }

    @Override
    public String upload(FileUploadDTO fileUploadDTO, MultipartFile file) {
        String fileName = file.getOriginalFilename();
        String type = fileName != null && fileName.contains(".") ?
                fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length()) : null;
        if (UploadValidateUtil.typeNotValidate(type)) {
            throw new FileIllegalException("文件格式必须为 jpg/jpeg/bmp/gif/png 格式！！！");
        }
        try {
//            byte[] bytes = checkFile(file, weight, height, size);
            FileDto fileDto = new FileDto();
            // 0.8-1.0版本过渡决定 不对图片进行压缩处理
            fileDto.setFileContent(SecurityManager.entryptBase64(file.getBytes()));
            fileDto.setFileName(getRandomFileName() + "." + type);
            String upload = baseService.upload(fileDto);
            log.info("图片上传下载路径 upload={}", upload);
            return Optional.ofNullable(upload).orElse(null);
        } catch (IOException e) {
            log.error("文件上传失败 file={}", file.getOriginalFilename());
            e.printStackTrace();
        }
        return null;
    }

    /**
     * @param file
     * @return
     * @throws IOException
     */
    private byte[] checkFile(MultipartFile file, Integer weight, Integer height, Long size) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        if (null != size && file.getSize() > size) {
            byte[] bytes = compressPicForScale(file.getBytes(), size);
            if (null != weight && null != height) {
                Thumbnails.of(new ByteArrayInputStream(bytes))
                        .forceSize(weight, height)
                        .outputQuality(1)
                        .toOutputStream(byteArrayOutputStream);
                return byteArrayOutputStream.toByteArray();
            }
            return bytes;
        } else {
            if (null != weight && null != height) {
                Thumbnails.of(file.getInputStream())
                        .forceSize(weight, height)
                        .outputQuality(1)
                        .toOutputStream(byteArrayOutputStream);
                return byteArrayOutputStream.toByteArray();
            }
            return file.getBytes();
        }
    }

    public byte[] compressPicForScale(byte[] imageBytes, long desFileSize) {
        if (imageBytes == null || imageBytes.length <= 0 || imageBytes.length < desFileSize) {
            return imageBytes;
        }
        try {
            while (imageBytes.length > desFileSize) {
                double accuracy = getAccuracy(imageBytes.length);
                ByteArrayInputStream inputStream = new ByteArrayInputStream(imageBytes);
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream(imageBytes.length);
                Thumbnails.of(inputStream)
                        .scale(accuracy)
                        .outputQuality(accuracy)
                        .toOutputStream(outputStream);
                imageBytes = outputStream.toByteArray();
            }
        } catch (Exception e) {
            log.error("压缩图片异常！e={}", e.getMessage());
        }
        return imageBytes;
    }
}
