package com.holderzone.holder.saas.aggregation.merchant.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class ThreadPoolConfig {

	@Bean
	public ExecutorService executorService() {
		return new ThreadPoolExecutor(5, 8, 10L, TimeUnit.SECONDS,
				new ArrayBlockingQueue<>(500), new ThreadFactoryBuilder().setNameFormat("report-mapping-pool-%d").build());
	}

	@Bean(name = "executorTakeoutService")
	public ExecutorService executorTakeoutService() {
		return new ThreadPoolExecutor(5, 8, 10L, TimeUnit.SECONDS,
				new ArrayBlockingQueue<>(500), new ThreadFactoryBuilder().setNameFormat("takeout-pool-%d").build());
	}
}