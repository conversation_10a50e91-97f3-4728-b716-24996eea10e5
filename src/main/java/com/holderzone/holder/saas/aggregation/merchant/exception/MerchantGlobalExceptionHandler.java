package com.holderzone.holder.saas.aggregation.merchant.exception;

import com.holderzone.feign.spring.boot.core.GenericException;
import com.holderzone.feign.spring.boot.exception.ResultExceptionHandlerAdapter;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.saas.store.dto.exception.GroupBuyException;
import com.holderzone.saas.store.enums.locale.LocaleMessageEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GlobalExceptionHandler
 * @date 2018/09/14 16:01
 * @description 全局异常拦截
 * @program holder-saas-aggregation-merchant
 */
@RestControllerAdvice
public class MerchantGlobalExceptionHandler extends ResultExceptionHandlerAdapter {

    private static final Logger logger = LoggerFactory.getLogger(MerchantGlobalExceptionHandler.class);

    @ExceptionHandler(value = FileIllegalException.class)
    public Result fileIllegalException(FileIllegalException e) {
        logger.error("图片上传校验失败：{}", nonNullDetailMessage(e));
        return Result.buildIllegalArgumentResult(nonNullMessage(e.getMessage()));
    }

    @ExceptionHandler(value = IOException.class)
    public Result ioException(IOException e) {
        String message = e.getMessage();
        logger.error("IOException:" + e);
        if (message.contains("Connection reset by peer")) {
            message = "业务繁忙，请稍后再试";
        }
        return Result.buildOpFailedResult(message);
    }

    @ExceptionHandler(value = GroupBuyException.class)
    public Result<?> groupBuyException(GroupBuyException e) {
        return Result.buildFailResult(500,e.getMessage());
    }

    @Override
    @ExceptionHandler(value = GenericException.class)
    public Result<?> genericException(GenericException e) {
        logger.warn("本地业务异常：", e);
        return Result.buildOpFailedResult(LocaleMessageEnum.getLocale(e.getMessage()));
    }

    @ConditionalOnClass({BusinessException.class})
    @ExceptionHandler({BusinessException.class})
    @Override
    public Result<?> businessException(BusinessException e) {
        logger.warn("本地业务异常：", e);
        return Result.buildOpFailedResult(LocaleMessageEnum.getLocale(e.getMessage()));
    }

}
