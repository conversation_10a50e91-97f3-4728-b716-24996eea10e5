package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerShoppingCartDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreShoppingCartDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreConsumerCartItemReqDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

import javax.validation.Valid;
/**
 * @description 调用微信服务层业务
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreShoppingCartClientService
 * @date 2019/3/6
 */
@Component
@FeignClient(name = "holder-saas-store-weixin",fallbackFactory = WxStoreShoppingCartClientService.WxStoreShoppingCartFallBack.class)
public interface WxStoreShoppingCartClientService {

	@PostMapping("/wx-store-shopping-cart-provide/get")
	WxStoreShoppingCartDTO getWxStoreShoppingCart(@Valid WxStoreConsumerDTO wxStoreConsumerDTO);

	@PostMapping("/wx-store-shopping-cart-provide/update")
	boolean changeWxStoreShoppingCart(@Valid WxStoreConsumerShoppingCartDTO wxStoreConsumerShoppingCartDTO);

	@PostMapping("/wx-store-shopping-cart-provide/empty")
	boolean emptyWxStoreShoppingCart(@Valid WxStoreConsumerDTO wxStoreConsumerDTO);

	@PostMapping("/wx-store-shopping-cart-provide/create-item")
	boolean createCartItem(@Valid WxStoreConsumerCartItemReqDTO wxStoreConsumerCartItemReqDTO);

	@PostMapping("/wx-store-shopping-cart-provide/update-item")
	boolean updateCartItem(@Valid WxStoreConsumerCartItemReqDTO wxStoreConsumerCartItemReqDTO);

	@PostMapping("/wx-store-shopping-cart-provide/del-item")
	boolean delCartItem(@Valid WxStoreConsumerCartItemReqDTO wxStoreConsumerCartItemReqDTO);

	@Slf4j
	@Component
	class WxStoreShoppingCartFallBack implements FallbackFactory<WxStoreShoppingCartClientService> {

		@Override
		public WxStoreShoppingCartClientService create(Throwable throwable) {
			return new WxStoreShoppingCartClientService() {

				@Override
				public WxStoreShoppingCartDTO getWxStoreShoppingCart(@Valid WxStoreConsumerDTO wxStoreConsumerDTO) {
					log.error("远程调用服务失败，msg={}", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public boolean changeWxStoreShoppingCart(@Valid WxStoreConsumerShoppingCartDTO wxStoreConsumerShoppingCartReqDTO) {
					log.error("远程调用服务失败，msg={}", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public boolean emptyWxStoreShoppingCart(@Valid WxStoreConsumerDTO wxStoreConsumerDTO) {
					log.error("远程调用服务失败，msg={}", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public boolean createCartItem(@Valid WxStoreConsumerCartItemReqDTO wxStoreConsumerCartItemReqDTO) {
					log.error("远程调用服务失败，msg={}", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public boolean updateCartItem(@Valid WxStoreConsumerCartItemReqDTO wxStoreConsumerCartItemReqDTO) {
					log.error("远程调用服务失败，msg={}", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public boolean delCartItem(@Valid WxStoreConsumerCartItemReqDTO wxStoreConsumerCartItemReqDTO) {
					log.error("远程调用服务失败，msg={}", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}
			};
		}
	}
}
