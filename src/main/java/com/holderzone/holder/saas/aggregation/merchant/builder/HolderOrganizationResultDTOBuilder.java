package com.holderzone.holder.saas.aggregation.merchant.builder;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.resource.common.dto.holder.organization.HolderOrganizationResultDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

public class HolderOrganizationResultDTOBuilder {


    public static void relation(HolderOrganizationResultDTO organizationResult, List<Long> organizationGuids) {
        if (Objects.isNull(organizationResult.getParentIds())) {
            organizationResult.setParentIds("");
        }
        organizationResult.setIsRelation(organizationGuids.contains(organizationResult.getId()));
        if (CollectionUtils.isNotEmpty(organizationResult.getChilds())) {
            for (HolderOrganizationResultDTO child : organizationResult.getChilds()) {
                child.setParentId(organizationResult.getId());
                child.setParentIds((StringUtils.isEmpty(organizationResult.getParentIds()) ? "" : organizationResult.getParentIds() + ",") + organizationResult.getId());
                child.setIsRelation(organizationGuids.contains(child.getId()));
                relation(child, organizationGuids);
            }
        }
    }


    public static void findListByIsRelation(HolderOrganizationResultDTO organizationResult, List<HolderOrganizationResultDTO> importOrganizationList) {
        if (organizationResult.getIsRelation()) {
            importOrganizationList.add(organizationResult);
        }
        if (CollectionUtils.isNotEmpty(organizationResult.getChilds())) {
            for (HolderOrganizationResultDTO child : organizationResult.getChilds()) {
                if (organizationResult.getIsRelation()) {
                    importOrganizationList.add(child);
                }
                findListByIsRelation(child, importOrganizationList);
            }
        }
    }

    public static void setParentIds(List<HolderOrganizationResultDTO> organizationResult, String enterpriseGuid) {
        if (CollectionUtils.isEmpty(organizationResult)) {
            throw new BusinessException("导入组织机构不能为空");
        }
        Map<Long, HolderOrganizationResultDTO> organizationMap = organizationResult.stream()
                .collect(Collectors.toMap(HolderOrganizationResultDTO::getId, Function.identity(), (key1, key2) -> key2));
        for (HolderOrganizationResultDTO organizationResultDTO : organizationResult) {
            if (Objects.isNull(organizationResultDTO.getParentId())) {
                organizationResultDTO.setParentIds(enterpriseGuid);
                continue;
            }
            List<String> parentIdList = Lists.newArrayList();
            getParentIds(organizationResultDTO, organizationMap, parentIdList, enterpriseGuid);
            String parentIds = String.join(",", parentIdList);
            organizationResultDTO.setParentIds(parentIds);
        }
    }


    private static void getParentIds(HolderOrganizationResultDTO organization, Map<Long, HolderOrganizationResultDTO> organizationMap,
                                     List<String> parentIdList, String enterpriseGuid) {
        HolderOrganizationResultDTO parentOrganization = organizationMap.get(organization.getParentId());
        if (Objects.nonNull(parentOrganization)) {
            parentIdList.add(0, String.valueOf(parentOrganization.getId()));
            if (Objects.nonNull(parentOrganization.getParentId())) {
                getParentIds(parentOrganization, organizationMap, parentIdList, enterpriseGuid);
            }
        } else {
            parentIdList.add(0, enterpriseGuid);
        }
    }


    public static List<HolderOrganizationResultDTO> searchOrganization(List<HolderOrganizationResultDTO> organizationResultList, String keyword) {
        List<HolderOrganizationResultDTO> searchResultList = Lists.newArrayList();
        getSearchOrganizationList(organizationResultList, keyword, searchResultList);
        return searchResultList;
    }

    private static void getSearchOrganizationList(List<HolderOrganizationResultDTO> organizationResultList, String keyword,
                                                  List<HolderOrganizationResultDTO> searchResultList) {
        for (HolderOrganizationResultDTO resultDTO : organizationResultList) {
            int index = resultDTO.getName().indexOf(keyword);
            if (index != -1) {
                searchResultList.add(resultDTO);
                continue;
            }
            if (CollectionUtils.isNotEmpty(resultDTO.getChilds())) {
                getSearchOrganizationList(resultDTO.getChilds(), keyword, searchResultList);
            }
        }
    }
}
