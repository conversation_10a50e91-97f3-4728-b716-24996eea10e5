package com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.entity.dto.OrganizationNewDTO;
import com.holderzone.holder.saas.aggregation.merchant.entity.vo.EnterpriseDTO;
import com.holderzone.resource.common.dto.enterprise.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EnterpriseClient
 * @date 2019/03/14 11:33
 * @description 云端企业服务
 * @program holder-saas-store-trading-center
 */
@Component
@FeignClient(name = "holder-saas-cloud-enterprise", fallbackFactory = EnterpriseRpcService.EnterClientFallBack.class)
public interface EnterpriseRpcService {

    @ApiOperation(value = "根据手机号查询门店")
    @GetMapping("/organization/store/tel")
    OrganizationDTO getStoreInfoByTel(@RequestParam("tel") String tel);

    /***
     *  根据条件查询组织
     * @param organizationQueryDTO
     * @return
     */
    @PostMapping("/organization/store/platform/page")
    Page<OrganizationNewDTO> pageQueryAllPlatformStore(@RequestBody OrganizationQueryDTO organizationQueryDTO);

    @RequestMapping("/multi/member")
    List<MultiMemberDTO> list(MultiMemberQueryDTO multiMember);


    @ApiOperation(value = "根据主键查询单个企业", notes = "uid、企业guid、企业电话必传其一（其他值无效）")
    @PostMapping("/enterprise/find")
    EnterpriseDTO findEnterprise(@RequestBody EnterpriseQueryDTO query);


    @GetMapping("/multi/member/list")
    List<MultiMemberDTO> list(@RequestBody Set<String> multiMemberGuid);

    @GetMapping("/enterprise/member/info/{organizationGuid}")
    MultiMemberDTO findMemberInfoByOrganizationGuid(@PathVariable("organizationGuid") String organizationGuid);

    @Component
    class EnterClientFallBack implements FallbackFactory<EnterpriseRpcService> {

        private static final Logger logger = LoggerFactory.getLogger(EnterClientFallBack.class);

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public EnterpriseRpcService create(Throwable throwable) {
            return new EnterpriseRpcService() {
                @Override
                public OrganizationDTO getStoreInfoByTel(String tel) {
                    logger.error("根据手机号查询门店，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Page<OrganizationNewDTO> pageQueryAllPlatformStore(OrganizationQueryDTO organizationQueryDTO) {
                    logger.error("根据条件查询组织失败，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<MultiMemberDTO> list(MultiMemberQueryDTO multiMember) {
                    logger.error("根据条件查询组织失败，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public EnterpriseDTO findEnterprise(EnterpriseQueryDTO query) {
                    logger.error("根据主键查询单个企业失败，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<MultiMemberDTO> list(Set<String> multiMemberGuid) {
                    logger.error("批量查询运营主体信息失败，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public MultiMemberDTO findMemberInfoByOrganizationGuid(String organizationGuid) {
                    logger.info(HYSTRIX_PATTERN, "findMemberInfoByOrganizationGuid", organizationGuid,
                            throwable.getCause());
                    throw new ServerException();
                }
            };
        }
    }

}
