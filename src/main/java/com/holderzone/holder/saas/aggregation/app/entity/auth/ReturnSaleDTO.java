package com.holderzone.holder.saas.aggregation.app.entity.auth;

import com.holderzone.holder.saas.aggregation.app.anno.DataAuthFieldControl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * 退菜统计
 */
@Data
@ApiModel
public class ReturnSaleDTO {

    /**
     * 商品名称
     */
    @DataAuthFieldControl("return_sale_item_name")
    private String name;

    /**
     * 规格名称
     */
    @DataAuthFieldControl("return_sale_item_name")
    private String skuName;

    /**
     * 单价
     */
    @DataAuthFieldControl("return_sale_unit_price")
    private String unitPrice;

    /**
     * 退菜数量
     */
    @DataAuthFieldControl("return_sale_count")
    private String quantum;

    /**
     * 金额
     */
    @DataAuthFieldControl("return_sale_amount")
    private String amount;

    @ApiModelProperty(value = "商品类型")
    private Integer itemType;

    @ApiModelProperty(value = "是否合计项,0否 1是")
    private int isTotal = 0;

    @ApiModelProperty(value = "是否含有子项,0否 1是")
    private int hasSubInfo = 0;

    @ApiModelProperty(value = "子项")
    @DataAuthFieldControl
    private List<ReturnSaleDTO> subs;


}