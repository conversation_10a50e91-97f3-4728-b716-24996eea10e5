package com.holderzone.holder.saas.aggregation.merchant.util;

import cn.hutool.core.collection.CollectionUtil;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.aggregation.merchant.entity.enums.MerchantMenuLocaleEnum;
import com.holderzone.saas.store.dto.user.MenuDTO;
import com.holderzone.saas.store.dto.user.MenuSourceDTO;
import com.holderzone.saas.store.dto.user.SourceDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 * @create 2023-07-19
 * @description
 */
public class MenuLocaleUtil {

    private MenuLocaleUtil() {
        throw new UnsupportedOperationException();
    }

    public static void transferMerchantMenu(List<MenuDTO> merchantMenuList){
        if(CollectionUtil.isEmpty(merchantMenuList)){
            return;
        }
        //若本地是简体中文直接返回
        if(LocaleContextHolder.getLocale() == Locale.SIMPLIFIED_CHINESE){
            return;
        }
        //遍历菜单信息
        recursiveTransfer(merchantMenuList);
    }

    private static void recursiveTransfer(List<MenuDTO> merchantMenuList) {
        for(MenuDTO merchantMenu : merchantMenuList){
            merchantMenu.setMenuName(MerchantMenuLocaleEnum.getLocale(merchantMenu.getMenuName()));
            if(CollectionUtil.isNotEmpty(merchantMenu.getSourceDTOList())){
                transferSource(merchantMenu.getSourceDTOList());
            }
            if(CollectionUtil.isNotEmpty(merchantMenu.getMenus())){
                recursiveTransfer(merchantMenu.getMenus());
            }
        }
    }

    private final static String SOURCE_LOCALE_PREFIX = "SOURCE_";

    private static void transferSource(List<SourceDTO> sourceList) {
        sourceList.forEach(s -> s.setSourceName(LocaleUtil.getMessage(SOURCE_LOCALE_PREFIX + s.getSourceCode().toUpperCase())));
    }

    public static void transferSourceMenu(List<MenuSourceDTO> sourceList) {
        if(CollectionUtil.isEmpty(sourceList)){
            return;
        }
        sourceList.forEach(s -> s.setSourceName(LocaleUtil.getMessage(SOURCE_LOCALE_PREFIX + s.getSourceCode().toUpperCase())));
    }
}
