package com.holderzone.holder.saas.aggregation.app.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @description 字符串工具
 * @date 2021/8/6 10:06
 */
public class StringMessageUtils {

    static Pattern chinesePattern = Pattern.compile("[\u4e00-\u9fa5]");

    private StringMessageUtils() {
    }

    /**
     * 判断字符串中是否包含中文
     *
     * @param str 待校验字符串
     * @return 是否为中文
     * @warn 不能校验是否为中文标点符号
     */
    public static boolean isContainChinese(String str) {
        Matcher m = chinesePattern.matcher(str);
        return m.find();
    }

}