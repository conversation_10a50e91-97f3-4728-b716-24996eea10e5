package com.holderzone.holder.saas.aggregation.weixin.controller.memberCenter;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.UpdateUserLoginClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.memberCenter.WxMemberOverviewClientService;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseAccountStatus;
import com.holderzone.saas.store.dto.weixin.WxMemberOverviewRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/wx_member_center_overview")
@Api(description = "会员中心：主页")
@Slf4j
public class WxMemberCenterOverviewController {

    private final WxMemberOverviewClientService wxMemberOverviewClientService;

//    private final WxHsmMemberBasicService wxHsmMemberBasicService;

    private final HsaBaseClientService hsaBaseClientService;

    private final UpdateUserLoginClientService updateUserLoginClientService;


    @Autowired
    public WxMemberCenterOverviewController(WxMemberOverviewClientService wxMemberOverviewClientService, HsaBaseClientService hsaBaseClientService, UpdateUserLoginClientService updateUserLoginClientService) {
        this.wxMemberOverviewClientService = wxMemberOverviewClientService;
//        this.wxHsmMemberBasicService = wxHsmMemberBasicService;
        this.hsaBaseClientService = hsaBaseClientService;
        this.updateUserLoginClientService = updateUserLoginClientService;
    }

    @ApiOperation("查询所有模块")
    @PostMapping(value = "/all_model")
    public Result<WxMemberOverviewRespDTO> allModel(@RequestBody WxStoreConsumerDTO wxStoreConsumerDTO) {
        log.info("查询所有模块入参:{}", JacksonUtils.writeValueAsString(wxStoreConsumerDTO));
        ResponseAccountStatus memberState = hsaBaseClientService.getMemberState(wxStoreConsumerDTO.getOpenId(),
                wxStoreConsumerDTO.getEnterpriseGuid(), wxStoreConsumerDTO.getOperSubjectGuid()).getData();
        log.info("获取用户状态:{}", JSON.toJSONString(memberState));
        if (!ObjectUtils.isEmpty(memberState) && 1 == memberState.getAccountState()) {
            //如果会员的状态被禁用  移除登录状态
            wxStoreConsumerDTO.setIsLogin(false);
            boolean updateUserLogin = updateUserLoginClientService.updateUserLogin(wxStoreConsumerDTO);
            log.info("移除登录状态：{}====》会员openId:{}", updateUserLogin, wxStoreConsumerDTO.getOpenId());
//            return Result.buildFailResult(-10, "您的帐号已被禁用，请联系商家");
        }
        return Result.buildSuccessResult(wxMemberOverviewClientService.allModel(wxStoreConsumerDTO));
    }

}
