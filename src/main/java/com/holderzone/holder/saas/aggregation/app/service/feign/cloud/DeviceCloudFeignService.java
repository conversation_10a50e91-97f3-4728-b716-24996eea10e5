package com.holderzone.holder.saas.aggregation.app.service.feign.cloud;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.resource.common.dto.device.VersionDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(value = "holder-saas-cloud-device", fallbackFactory = DeviceCloudFeignService.VersionServiceFallback.class)
public interface DeviceCloudFeignService {

    @PostMapping("/version/add")
    void add(@RequestBody VersionDTO versionDTO);

    @GetMapping("/version/get")
    VersionDTO get(@RequestParam("product") Integer product);

    @PostMapping("/version/update")
    void update(@RequestBody VersionDTO versionDTO);

    @GetMapping("/version/delete")
    void delete(@RequestParam("id") Long id);

    @Slf4j
    @Component
    class VersionServiceFallback implements FallbackFactory<DeviceCloudFeignService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public DeviceCloudFeignService create(Throwable throwable) {
            return new DeviceCloudFeignService() {
                @Override
                public void add(VersionDTO versionDTO) {
                    log.error(HYSTRIX_PATTERN, "add", JacksonUtils.writeValueAsString(versionDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public VersionDTO get(Integer product) {
                    log.error(HYSTRIX_PATTERN, "get", JacksonUtils.writeValueAsString(product),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void update(VersionDTO versionDTO) {
                    log.error(HYSTRIX_PATTERN, "update", JacksonUtils.writeValueAsString(versionDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void delete(Long id) {
                    log.error(HYSTRIX_PATTERN, "delete", id, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}