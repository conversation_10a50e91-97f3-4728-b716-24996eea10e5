package com.holderzone.holder.saas.aggregation.merchant.controller.weixin;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin.WxStickOrderClientService;
import com.holderzone.saas.store.dto.weixin.req.WxStickModelOrderDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickOrderRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickOrderController
 * @date 2019/03/15 18:13
 * @description 微信桌贴购买Controller
 * @program holder-saas-store
 */
@RestController
@RequestMapping("/wx_stick_order")
@Api(description = "微信桌贴模板购买controller")
@Slf4j
public class WxStickOrderController {
    @Autowired
    WxStickOrderClientService wxStickOrderClientService;

    @ApiOperation(value = "下单")
    @PostMapping("/order")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "下单")
    public Result<WxStickOrderRespDTO> order(@RequestBody WxStickModelOrderDTO wxStickModelOrderDTO) {
        if (log.isInfoEnabled()) {
            log.info("已收到微信桌贴购买下单请求参数：wxStickModelOrderDTO:{}", wxStickModelOrderDTO);
        }
        return Result.buildSuccessResult(wxStickOrderClientService.order(wxStickModelOrderDTO));
    }

    @ApiOperation(value = "轮询")
    @PostMapping("/polling")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "轮询")
    public Result<WxStickOrderRespDTO> polling(@RequestBody WxStickOrderRespDTO wxStickOrderRespDTO) {
        if (log.isInfoEnabled()) {
            log.info("已收到微信桌贴购买轮询请求参数：wxStickOrderRespDTO:{}", wxStickOrderRespDTO);
        }
        return Result.buildSuccessResult(wxStickOrderClientService.polling(wxStickOrderRespDTO));
    }
}
