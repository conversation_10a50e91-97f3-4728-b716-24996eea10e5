package com.holderzone.holder.saas.aggregation.merchant.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.OverviewRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * holder-saas-aggregation-app
 *
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2023/9/9
 * @since 1.8
 */
@Component
@FeignClient(name = "holder-saas-aggregation-app", fallbackFactory = AggregationAppService.ServiceFallBack.class)
public interface AggregationAppService {
    /**
     * 查询营业概况
     *
     * @param request 请求参数
     */
    @PostMapping("/business_daily/overview")
    Result<OverviewRespDTO> overview(@RequestBody @Valid DailyReqDTO request);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<AggregationAppService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public AggregationAppService create(Throwable cause) {
            return request -> {
                log.error(HYSTRIX_PATTERN, "overview", JacksonUtils.writeValueAsString(request), ThrowableUtils.asString(cause));
                throw new BusinessException("查询营业概况熔断");
            };
        }
    }
}
