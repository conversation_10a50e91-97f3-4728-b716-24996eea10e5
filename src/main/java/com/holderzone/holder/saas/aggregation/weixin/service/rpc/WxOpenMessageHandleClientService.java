package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.saas.store.dto.weixin.open.WxMessageHandleReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxAuthorizeReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxMessageHandleClientService
 * @date 2019/03/26 17:51
 * @description 微信消息处理ClientService
 * @program holder-saas-store
 */
@Component
@FeignClient(name = "holder-saas-store-weixin",
//url="http://127.0.0.1:8920/",
fallbackFactory = WxOpenMessageHandleClientService.MessageHandleFallBack.class)
public interface WxOpenMessageHandleClientService {

    @PostMapping("/wx_handler/handle_message")
    String handleMessage(@RequestBody WxMessageHandleReqDTO wxMessageHandleReqDTO);

    @PostMapping("/wx_handler/get_user_info")
    String getUserInfo(@RequestBody WxAuthorizeReqDTO wxAuthorizeReqDTO);

    @PostMapping("/wx_handler/login")
    String memberLogin(@RequestBody WxPortalReqDTO wxPortalReqDTO);

    @PostMapping("/wx_handler/new_member_login")
    String newMemberLogin(@RequestBody WxPortalReqDTO wxPortalReqDTO);

    @PostMapping("/wx_handler/open_member_login_jump_url")
    String openMemberLogin(@RequestBody WxPortalReqDTO wxPortalReqDTO);

    @PostMapping("/session/build")
    WxMemberSessionDTO buildSession(@RequestBody WxAuthorizeReqDTO wxAuthorizeReqDTO);

    @PostMapping("/session/getToken")
    String getToken(@RequestBody WxMemberSessionDTO wxMemberSessionDTO);

    @Component
    @Slf4j
    class MessageHandleFallBack implements FallbackFactory<WxOpenMessageHandleClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public WxOpenMessageHandleClientService create(Throwable throwable) {
            return new WxOpenMessageHandleClientService() {
                @Override
                public String handleMessage(WxMessageHandleReqDTO wxMessageHandleReqDTO) {
                    log.info(HYSTRIX_PATTERN, "handleMessage", JacksonUtils.writeValueAsString(wxMessageHandleReqDTO), throwable.getCause());
                    throw new BusinessException("调用微信服务失败，e:{}", throwable.getCause());
                }

                @Override
                public String getUserInfo(WxAuthorizeReqDTO wxAuthorizeReqDTO) {
                    log.info(HYSTRIX_PATTERN, "getUserInfo", JacksonUtils.writeValueAsString(wxAuthorizeReqDTO), throwable.getCause());
                    return null;
                }

                @Override
                public String memberLogin(WxPortalReqDTO wxPortalReqDTO) {
                    log.info(HYSTRIX_PATTERN, "getUserInfo", JacksonUtils.writeValueAsString(wxPortalReqDTO), throwable.getCause());
                    throw new BusinessException("调用微信服务失败，e:{}", throwable.getCause());
                }

                @Override
                public String newMemberLogin(WxPortalReqDTO wxPortalReqDTO) {
                    log.info(HYSTRIX_PATTERN, "getUserInfo", JacksonUtils.writeValueAsString(wxPortalReqDTO), throwable.getCause());
                    throw new BusinessException("调用微信服务失败，e:{}", throwable.getCause());
                }

                @Override
                public String openMemberLogin(WxPortalReqDTO wxPortalReqDTO) {
                    log.info(HYSTRIX_PATTERN, "openMemberLogin", JacksonUtils.writeValueAsString(wxPortalReqDTO), throwable.getCause());
                    throw new BusinessException("调用微信服务失败，e:{}", throwable.getCause());
                }

                @Override
                public WxMemberSessionDTO buildSession(WxAuthorizeReqDTO wxAuthorizeReqDTO) {
                	 log.error(HYSTRIX_PATTERN, "buildSession", JacksonUtils.writeValueAsString(wxAuthorizeReqDTO), throwable.getCause());
                	 throw new BusinessException("调用微信服务失败，e:{}", throwable.getCause());
                }

                @Override
                public String getToken(WxMemberSessionDTO wxMemberSessionDTO) {
                    log.error("getToken,参数{}",wxMemberSessionDTO,throwable);
                    return null;
                }
            };
        }
    }

	
}
