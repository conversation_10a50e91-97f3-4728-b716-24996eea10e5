package com.holderzone.holder.saas.aggregation.weixin.assembler;

import com.google.common.collect.Lists;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseClientMarketActivity;
import com.holderzone.saas.store.dto.marketing.nth.NthActivityDetailsVO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityDetailsVO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityItemDTO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityItemVO;
import com.holderzone.saas.store.dto.weixin.resp.MarketingActivityInfoRespDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.member.MarketActivityUnableTipEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/6/28
 * @description 营销活动转换
 */
public class MarketingActivityAssembler {

    private MarketingActivityAssembler() {
    }

    public static List<LimitSpecialsActivityItemVO> getActivityItemVOList(List<LimitSpecialsActivityDetailsVO> specialsActivityList) {
        List<LimitSpecialsActivityItemVO> activityItemVOList = new ArrayList<>();
        specialsActivityList.forEach(activity -> {
            List<LimitSpecialsActivityItemDTO> itemDTOList = activity.getItemDTOList();
            itemDTOList.forEach(itemDTO -> {
                LimitSpecialsActivityItemVO activityItemVO = new LimitSpecialsActivityItemVO();
                activityItemVO.setCommodityId(itemDTO.getCommodityId());
                activityItemVO.setCommodityCode(itemDTO.getCommodityCode());
                activityItemVO.setSpecialsType(itemDTO.getSpecialsType());
                activityItemVO.setSpecialsNumber(itemDTO.getSpecialsNumber());
                activityItemVO.setLimitNumber(itemDTO.getLimitNumber());
                activityItemVO.setItemUseNumber(0);
                activityItemVO.setActivityGuid(activity.getGuid());
                activityItemVO.setActivityName(activity.getName());
                activityItemVO.setStartTime(activity.getStartTime());
                activityItemVO.setEndTime(activity.getEndTime());
                activityItemVO.setIsLimitPeriod(activity.getIsLimitPeriod());
                activityItemVO.setLimitPeriodType(activity.getIsLimitPeriod());
                activityItemVO.setLimitPeriodJson(activity.getEquitiesTimeLimitedJson());
                activityItemVO.setRelationRule(activity.getRelationRule());
                activityItemVO.setApplyBusiness(activity.getApplyBusiness());
                activityItemVOList.add(activityItemVO);
            });
        });
        return activityItemVOList;
    }

    public static List<MarketingActivityInfoRespDTO> respClientActivityList2activityInfoList(
            List<ResponseClientMarketActivity> activitiesList,
            LimitSpecialsActivityDetailsVO specialsActivitySelectDetailsVO,
            NthActivityDetailsVO nthActivityDetailsVO) {
        if (CollectionUtils.isEmpty(activitiesList)) {
            return Lists.newArrayList();
        }
        List<MarketingActivityInfoRespDTO> respDTOList = new ArrayList<>();
        activitiesList.forEach(activity -> {
            MarketingActivityInfoRespDTO infoRespDTO = new MarketingActivityInfoRespDTO();
            infoRespDTO.setActivityGuid(activity.getGuid());
            infoRespDTO.setActivityName(activity.getActivityTitle());
            infoRespDTO.setActivityType(DiscountTypeEnum.ACTIVITY.getCode());
            infoRespDTO.setActivityRule(activity.getActivityRule());
            infoRespDTO.setUseAble(activity.isUseAble());
            infoRespDTO.setUnUseReason(activity.getUnUseReason());
            infoRespDTO.setDiscountPrice(activity.getDiscountPrice().setScale(2, RoundingMode.HALF_UP));
            // 限时特价活动存在且互斥的，需要处理满减满折活动
            if (!ObjectUtils.isEmpty(specialsActivitySelectDetailsVO) &&
                    Objects.equals(specialsActivitySelectDetailsVO.getRelationRule(), BooleanEnum.FALSE.getCode())) {
                infoRespDTO.setUseAble(false);
                infoRespDTO.setUnUseReason(MarketActivityUnableTipEnum.OTHER_DISCOUNT.getView());
            }
            // 第N份优惠活动存在且互斥的，需要处理满减满折活动
            if (!ObjectUtils.isEmpty(nthActivityDetailsVO) &&
                    Objects.equals(nthActivityDetailsVO.getRelationRule(), BooleanEnum.FALSE.getCode())) {
                infoRespDTO.setUseAble(false);
                infoRespDTO.setUnUseReason(MarketActivityUnableTipEnum.OTHER_DISCOUNT.getView());
            }
            // 选择了第N份优惠，满减满折互斥
            if (!ObjectUtils.isEmpty(nthActivityDetailsVO) &&
                    Objects.equals(activity.getIsShare(), BooleanEnum.TRUE.getCode())) {
                infoRespDTO.setUseAble(false);
                infoRespDTO.setUnUseReason(MarketActivityUnableTipEnum.OTHER_DISCOUNT.getView());
            }
            // 选择了限时特价，满减满折互斥
            if (!ObjectUtils.isEmpty(specialsActivitySelectDetailsVO) &&
                    Objects.equals(activity.getIsShare(), BooleanEnum.TRUE.getCode())) {
                infoRespDTO.setUseAble(false);
                infoRespDTO.setUnUseReason(MarketActivityUnableTipEnum.OTHER_DISCOUNT.getView());
            }

            respDTOList.add(infoRespDTO);
        });

        return respDTOList;
    }

    public static List<LimitSpecialsActivityItemVO> getLimitSpecialsActivityItemVOS(LimitSpecialsActivityDetailsVO activity,
                                                                                    List<String> itemGuidList) {
        List<LimitSpecialsActivityItemVO> activityItemVOList = new ArrayList<>();
        activity.getItemDTOList().forEach(activityItem -> {
            if (itemGuidList.contains(activityItem.getCommodityId())) {
                LimitSpecialsActivityItemVO activityItemVO = new LimitSpecialsActivityItemVO();
                activityItemVO.setCommodityId(activityItem.getCommodityId());
                activityItemVO.setCommodityCode(activityItem.getCommodityCode());
                activityItemVO.setSpecialsType(activityItem.getSpecialsType());
                activityItemVO.setSpecialsNumber(activityItem.getSpecialsNumber());
                activityItemVO.setLimitNumber(activityItem.getLimitNumber());
                activityItemVO.setItemUseNumber(0);
                activityItemVO.setActivityGuid(activity.getGuid());
                activityItemVO.setActivityName(activity.getName());
                activityItemVO.setStartTime(activity.getStartTime());
                activityItemVO.setEndTime(activity.getEndTime());
                activityItemVO.setIsLimitPeriod(activity.getIsLimitPeriod());
                activityItemVO.setLimitPeriodType(activity.getIsLimitPeriod());
                activityItemVO.setLimitPeriodJson(activity.getEquitiesTimeLimitedJson());
                activityItemVO.setRelationRule(activity.getRelationRule());
                activityItemVO.setApplyBusiness(activity.getApplyBusiness());
                activityItemVOList.add(activityItemVO);
            }
        });
        return activityItemVOList;
    }

    public static LimitSpecialsActivityItemVO getSpecialsActivityItemVO(LimitSpecialsActivityDetailsVO activityDetailsVO,
                                                                        LimitSpecialsActivityItemDTO activityDetails) {
        LimitSpecialsActivityItemVO activityItemVO = new LimitSpecialsActivityItemVO();
        activityItemVO.setCommodityId(activityDetails.getCommodityId());
        activityItemVO.setCommodityCode(activityDetails.getCommodityCode());
        activityItemVO.setSpecialsType(activityDetails.getSpecialsType());
        activityItemVO.setSpecialsNumber(activityDetails.getSpecialsNumber());
        activityItemVO.setLimitNumber(activityDetails.getLimitNumber());
        activityItemVO.setItemUseNumber(0);
        activityItemVO.setActivityGuid(activityDetailsVO.getGuid());
        activityItemVO.setActivityName(activityDetailsVO.getName());
        activityItemVO.setStartTime(activityDetailsVO.getStartTime());
        activityItemVO.setEndTime(activityDetailsVO.getEndTime());
        activityItemVO.setState(activityDetailsVO.getApplyBusiness());
        activityItemVO.setIsLimitPeriod(activityDetailsVO.getIsLimitPeriod());
        activityItemVO.setLimitPeriodType(activityDetailsVO.getEquitiesTimeLimitedType());
        activityItemVO.setLimitPeriodJson(activityDetailsVO.getEquitiesTimeLimitedJson());
        activityItemVO.setRelationRule(activityDetailsVO.getRelationRule());
        activityItemVO.setApplyBusiness(activityDetailsVO.getApplyBusiness());
        return activityItemVO;
    }

    public static List<LimitSpecialsActivityItemVO> getSpecialsActivityItemVOList(LimitSpecialsActivityDetailsVO activityDetailsVO,
                                                                                  List<LimitSpecialsActivityItemDTO> itemDTOList) {
        if (CollectionUtils.isEmpty(itemDTOList)) {
            return new ArrayList<>();
        }
        List<LimitSpecialsActivityItemVO> response = new ArrayList<>();
        itemDTOList.forEach(item -> {
            LimitSpecialsActivityItemVO activityItemVO = getSpecialsActivityItemVO(activityDetailsVO, item);
            response.add(activityItemVO);
        });
        return response;
    }
}
