package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dynamic.datasource.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.weixin.config.WeCatConfig;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxOpenMessageHandleClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxStoreMenuDetailsClientService;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WxAuthorizeReqUtils;
import com.holderzone.saas.store.dto.weixin.WxStoreAuthorizerInfoDTO;
import com.holderzone.saas.store.dto.weixin.open.WxMessageHandleReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxAuthorizeReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxCommonReqDTO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxOpenMessageHandler
 * @date 2019/03/25 14:15
 * @description 微信第三方平台消息处理Controller
 * @program holder-saas-store
 */
@RestController
@Api(value = "微信第三方平台消息处理Controller", description = "用于处理微信服务器发送的消息")
@RequestMapping("/wx_handler/{APPID}")
@Slf4j
public class WxOpenMessageHandler {
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private WxStoreMenuDetailsClientService wxStoreMenuDetailsClientService;


    @Autowired
    private WxOpenMessageHandleClientService wxMessageHandleClientService;

    @Autowired
    private WeCatConfig weCatConfig;

    @PostMapping("/call_back")
    public String callBack(@PathVariable("APPID") String appId, @RequestBody(required = false) String message, WxCommonReqDTO wxCommonReqDTO, HttpServletRequest request) {
        log.info("接收到微信端推送的消息，appid:{}", appId);
        log.info("接收到微信端推送的消息，wxCommonReqDTO:{}", JacksonUtils.writeValueAsString(wxCommonReqDTO));
        log.info("接收到微信端推送的消息，wxMpXmlMessage:\n{}", message);
        return wxMessageHandleClientService.handleMessage(new WxMessageHandleReqDTO(message, appId, wxCommonReqDTO));
    }

    //frontend:
    //ordering-index-page
    @GetMapping("/redirect")
    public void redirect(@PathVariable("APPID") String appId, WxAuthorizeReqDTO wxAuthorizeReqDTO, HttpServletResponse response) throws IOException {
        log.info("地址跳转，appid:{}, wxAuthorizeReqDTO:{}", appId, wxAuthorizeReqDTO);
        wxAuthorizeReqDTO.setAppId(appId);
        WxAuthorizeReqUtils.handEventKey(wxAuthorizeReqDTO);
        String enterpriseGuid = wxAuthorizeReqDTO.getEnterpriseGuid();
        String operSubjectGuid = null;
        if (!StringUtils.isEmpty(appId)) {
            UserContextUtils.putErp(enterpriseGuid);
            WxStoreAuthorizerInfoDTO wxAuthInfo = wxStoreMenuDetailsClientService.findWxAuthInfo(appId);
            if (Objects.nonNull(wxAuthInfo)) {
                operSubjectGuid = wxAuthInfo.getOperSubjectGuid();
            }
        }
        if (enterpriseGuid != null) {
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        }
        if (wxAuthorizeReqDTO.getEventKey().startsWith(BusinessName.ORDER_CONFIG)
                || wxAuthorizeReqDTO.getEventKey().startsWith(BusinessName.ORDER_CONFIG_2)
        ) {
            Long currentTime = wxAuthorizeReqDTO.getCurrentTime();
            //有效
            boolean validTime = weCatConfig.getOrderingMessageExpire() > 0
                    && currentTime != null
                    && (currentTime + 1000 * 60 * weCatConfig.getOrderingMessageExpire()) >= System.currentTimeMillis();
            validTime = validTime || currentTime == null;
            log.info("currentTime={},validTime={},orderingMessageExpire={}"
                    , currentTime, validTime, weCatConfig.getOrderingMessageExpire());
            if (weCatConfig.getOrderingMessageExpire() > 0 && !validTime) {
                //无效
                response.setContentType("text/html;charset=utf-8");
                response.getWriter().print("<!DOCTYPE html><html><head><meta charset=\"utf-8\" /></head>\n" +
                        "<script  type=\"text/javascript\">alert('桌码链接已失效，请重新扫桌台二维码点餐！');</script></html>");
                return;
            }
            //微信点餐服务
            String weixinToken = /*enterpriseGuid+"_"+*/redisUtils.generateGuid("xweixintoken");
            redisUtils.setEx("redirct:" + weixinToken, wxAuthorizeReqDTO, 5, TimeUnit.MINUTES);
            wxAuthorizeReqDTO.setWeixinToken(weixinToken);
            String redirectUrl = String.format(weCatConfig.getOrderingIndexPage(), weixinToken, enterpriseGuid);
            try {
                //response.getWriter().println(wxAuthorizeReqDTO.getCode());
                //response.getWriter().println("redirectUrl="+redirectUrl);
                WxMemberSessionDTO session = wxMessageHandleClientService.buildSession(wxAuthorizeReqDTO);
                if (session.getCode() != 0) {
                    throw new BusinessException("微信重定向失败，错误编码" + session.getCode());
                }
                redirectUrl = redirectUrl + "&storeGuid=" + session.getStoreGuid();
                if (!StringUtils.isEmpty(operSubjectGuid)) {
                    redirectUrl = redirectUrl + "&operSubjectGuid=" + operSubjectGuid;
                }
                   /*if(developMode){
                       response.getWriter().println("code="+wxAuthorizeReqDTO.getCode());
                       response.getWriter().println();
                       response.getWriter().println("redirectUrl="+redirectUrl);
                       response.getWriter().println("需要本地创建session，接口http://192.168.102.42:8920/session/build,参数"+
                               JSON.toJSONString(wxAuthorizeReqDTO));
                   }else{
                       response.sendRedirect(redirectUrl);
                   }*/
                log.info("redirectUrl:{}", redirectUrl);
                response.sendRedirect(redirectUrl);
            } catch (IOException e) {
                log.error("微信重定向失败", e);
                throw new BusinessException("微信重定向失败，e: " + e);
            }
            //throw new BusinessException("微信重定向失败， ");
            //异步创建缓存

            return;
        }
        //其他逻辑
        try {
            String redirectUrl = wxMessageHandleClientService.getUserInfo(wxAuthorizeReqDTO);

            response.sendRedirect(redirectUrl);
        } catch (IOException e) {
            log.error("微信重定向失败", e);
            throw new BusinessException("微信重定向失败，e: " + e);
        }
    }

}
