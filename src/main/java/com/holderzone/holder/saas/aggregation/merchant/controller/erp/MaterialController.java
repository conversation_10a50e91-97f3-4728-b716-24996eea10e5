package com.holderzone.holder.saas.aggregation.merchant.controller.erp;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.merchant.entity.vo.MaterialStockVO;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp.MaterialFeignService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp.MaterialUnitFeignService;
import com.holderzone.holder.saas.aggregation.merchant.util.MaterialExcelUtil;
import com.holderzone.saas.store.dto.erp.*;
import com.holderzone.saas.store.dto.erp.util.Add;
import com.holderzone.saas.store.dto.erp.util.Update;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/05 9:55
 */
@RestController
@RequestMapping("material")
@Api(tags = "物料相关Api")
public class MaterialController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MaterialController.class);

    @Autowired
    private MaterialFeignService materialFeignService;

    @Autowired
    private MaterialUnitFeignService materialUnitFeignService;

    @PostMapping("add")
    @ApiOperation("添加物料信息")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "添加物料信息", action = OperatorType.ADD)
    public Result<Object> add(@RequestBody @Validated(Add.class) MaterialDTO materialDTO) {
        LOGGER.info("聚合层添加物料信息入参:->{}", JacksonUtils.writeValueAsString(materialDTO));
        materialFeignService.add(materialDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("delete")
    @ApiOperation("根据GUID删除物料信息")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "根据GUID删除物料信息", action = OperatorType.DELETE)
    public Result<Object> delete(@RequestBody MaterialDTO materialDTO) {
        LOGGER.info("聚合层根据GUID删除物料信息入参:->{}", JacksonUtils.writeValueAsString(materialDTO));
        if (!StringUtils.isEmpty(materialDTO.getGuid())) {
            materialFeignService.delete(materialDTO.getGuid());
        }
        return Result.buildEmptySuccess();
    }

    @PostMapping("update")
    @ApiOperation("修改物料信息")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "修改物料信息")
    public Result<Object> update(@RequestBody @Validated(Update.class) MaterialDTO materialDTO) {
        LOGGER.info("聚合层修改物料信息入参:->{}", JacksonUtils.writeValueAsString(materialDTO));
        materialFeignService.update(materialDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("changeStatus")
    @ApiOperation("启用禁用物料信息")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "启用禁用物料信息", action = OperatorType.SELECT)
    public Result changeStatus(@RequestBody MaterialDTO materialDTO) {
        LOGGER.info("聚合层启用禁用物料信息:->{}", JacksonUtils.writeValueAsString(materialDTO));
        materialFeignService.changeStatus(materialDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("findByCondition")
    @ApiOperation("条件查询物料信息")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "条件查询物料信息", action = OperatorType.SELECT)
    public Result<Page<MaterialDTO>> findPage(@RequestBody @Validated MaterialQueryDTO queryDTO) {
        LOGGER.info("聚合层条件查询物料信息入参:->{}", JacksonUtils.writeValueAsString(queryDTO));
        return Result.buildSuccessResult(materialFeignService.findByCondition(queryDTO));
    }

    @PostMapping("findByGuid")
    @ApiOperation("根据GUID查询物料信息")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "根据GUID查询物料信息", action = OperatorType.SELECT)
    public Result<MaterialDTO> findByGuid(@RequestBody MaterialDTO materialDTO) {
        LOGGER.info("聚合层根据GUID查询物料信息入参:->{}", JacksonUtils.writeValueAsString(materialDTO));
        if (!StringUtils.isEmpty(materialDTO.getGuid())) {
            return Result.buildSuccessResult(materialFeignService.findByGuid(materialDTO.getGuid()));
        }
        return Result.buildEmptySuccess();
    }

    @PostMapping("countBom")
    @ApiOperation("根据GUID查询物料配置的bom数量")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "根据GUID查询物料配置的bom数量", action = OperatorType.SELECT)
    public Result<Long> countMaterialBom(@RequestBody MaterialDTO materialDTO) {
        LOGGER.info("聚合层根据GUID查询物料配置的bom数量入参:->{}", JacksonUtils.writeValueAsString(materialDTO));
        if (!StringUtils.isEmpty(materialDTO.getGuid())) {
            return Result.buildSuccessResult(materialFeignService.countMaterialBom(materialDTO.getGuid()));
        }
        return Result.buildEmptySuccess();
    }

    @PostMapping("findByGuidList")
    @ApiOperation("根据物料GUID列表查询物料信息")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "根据物料GUID列表查询物料信息", action = OperatorType.SELECT)
    public Result<List<MaterialDTO>> findList(@RequestBody MaterialListQuery materialListQuery) {
        LOGGER.info("聚合层根据物料GUID列表查询物料信息入参:->{}", JacksonUtils.writeValueAsString(materialListQuery));
        return Result.buildSuccessResult(materialFeignService.findList(materialListQuery));
    }

    @PostMapping("import")
    @ApiOperation("导入物料信息")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "导入物料信息", action = OperatorType.ADD)
    public Result<List<MaterialDTO>> importMaterial(@RequestParam("storeGuid") String storeGuid, @RequestParam("file") MultipartFile multipartFile) throws IOException {
        LOGGER.info("聚合层导入物料信息入参:->{}", storeGuid);
        List<MaterialUnitDTO> unitDTOList = materialUnitFeignService.findList();
        List<MaterialDTO> materialDataByFile = MaterialExcelUtil.getMaterialDataByFile(multipartFile, unitDTOList, storeGuid);
        if (!materialDataByFile.isEmpty()) {
            materialFeignService.batchAdd(materialDataByFile);
        }
        return Result.buildEmptySuccess();
    }

    @PostMapping("template")
    @ApiOperation("下载物料模板")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "下载物料模板", action = OperatorType.SELECT)
    public void downloadTemplate() throws Exception {
        HttpServletResponse resp = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        XSSFWorkbook xssfWorkbook = MaterialExcelUtil.getDownloadWorkBook("downloadExcel/downloadMaterial.xlsx");
        MaterialExcelUtil.exportFile(resp, xssfWorkbook, "物料模板");
    }

    @PostMapping("export")
    @ApiOperation("导出物料信息")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "导出物料信息", action = OperatorType.SELECT)
    public void exportMaterial(@RequestBody MaterialQueryDTO materialQueryDTO) throws Exception {
        LOGGER.info("聚合层导出物料信息入参:->{}", JacksonUtils.writeValueAsString(materialQueryDTO));
        List<MaterialDTO> materialDTOList = materialFeignService.findListByCondition(materialQueryDTO);
        HttpServletResponse resp = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        XSSFWorkbook xssfWorkbook = MaterialExcelUtil.exportMaterial(materialDTOList);
        MaterialExcelUtil.exportFile(resp, xssfWorkbook, null);
    }

    @PostMapping("generatorCode")
    @ApiOperation("生成物料code")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "生成物料code", action = OperatorType.SELECT)
    public Result generatorCode() {
        return Result.buildSuccessResult(materialFeignService.generatorCode());
    }

    @PostMapping("findStockByCondition")
    @ApiOperation("条件查询库存列表")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "条件查询库存列表", action = OperatorType.SELECT)
    public Result findStockByCondition(@RequestBody StockQueryDTO stockQueryDTO) {
        LOGGER.info("聚合层条件查询库存列表入参:->{}", JacksonUtils.writeValueAsString(stockQueryDTO));
        Page stockByCondition = materialFeignService.findStockByCondition(stockQueryDTO);
        return Result.buildSuccessResult(fillStockVO(stockByCondition));
    }

    @PostMapping("export_stock")
    @ApiOperation("导出库存列表")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "导出库存列表", action = OperatorType.SELECT)
    public void exportStock(@RequestBody StockQueryDTO stockQueryDTO) {
        LOGGER.info("聚合层[导出库存列表]入参:->{}", JacksonUtils.writeValueAsString(stockQueryDTO));
        List<MaterialDTO> materialDTOS = materialFeignService.listByCondition(stockQueryDTO);
        try {
            HttpServletResponse resp = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
            XSSFWorkbook xssfWorkbook = MaterialExcelUtil.exportStock(materialDTOS);
            String fileName = "库存查询数据" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            MaterialExcelUtil.exportFile(resp, xssfWorkbook, fileName);
        } catch (Exception e) {
            LOGGER.error("导出库存列表失败",e);
            throw new BusinessException("导出库存列表失败");
        }
    }

    @PostMapping("materialConsumeSum")
    @ApiOperation("物料耗用汇总")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "物料耗用汇总", action = OperatorType.SELECT)
    public Result<Page<MaterialConsumeRespDTO>> materialConsumeSum(@RequestBody MaterialConsumeReqDTO materialConsumeReqDTO) {
        LOGGER.info("物料耗用汇总入参:->{}", JacksonUtils.writeValueAsString(materialConsumeReqDTO));
        return Result.buildSuccessResult(materialFeignService.materialConsumeSum(materialConsumeReqDTO));
    }

    private Page fillStockVO(Page<MaterialDTO> page) {
        List dataList = new ArrayList<>(page.getData().size());
        page.getData().forEach(materialDTO -> {
            MaterialStockVO materialStockVO = new MaterialStockVO();
            BeanUtils.copyProperties(materialDTO, materialStockVO);
            if (materialDTO.getLowestStock().compareTo(BigDecimal.ZERO) == 0 || materialDTO.getStock().compareTo(materialDTO.getLowestStock()) >= 0) {
                materialStockVO.setStockState("正常");
            } else if (materialDTO.getStock().compareTo(materialDTO.getLowestStock()) < 0) {
                materialStockVO.setStockState("过低");
            }
            dataList.add(materialStockVO);
        });
        page.setData(dataList);
        return page;
    }
}
