package com.holderzone.holder.saas.aggregation.app.controller.item;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.ItemService;
import com.holderzone.holder.saas.aggregation.app.service.feign.item.ItemClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.TradeItemClientService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.EstimateForManualReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemBarCodeReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemReqDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.print.PrintItemReqDto;
import io.seata.spring.annotation.GlobalTransactional;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @version 1.0
 * @className DishController
 * @date 2018/09/07 下午4:27
 * @description 商品接口
 * @program holder-saas-store-dish
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/item")
@Api(tags = "商品接口")
@Slf4j
public class ItemController {

    private final ItemClientService itemClientService;

    private final TradeItemClientService tradeItemClientService;

    private final ItemService itemService;

    @ApiOperation(value = "商品同步接口,必填参数：storeGuid")
    @PostMapping("/query_for_synchronize")
    public Result<ItemAndTypeForAndroidRespDTO> selectItemAndTypeForSyn(@RequestBody BaseDTO baseDTO) {
        log.info("query_for_synchronize入参,request={}", JacksonUtils.writeValueAsString(baseDTO));
        return Result.buildSuccessResult(itemService.selectItemAndTypeForSyn(baseDTO));
    }

    @ApiOperation(value = "获取打印商品集合接口,必填参数：storeGuid")
    @PostMapping("/selectTypeItemList")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "获取打印商品集合接口",action = OperatorType.SELECT)
    public Result<List<TypeItemListDTO>> selectTypeItemList(@RequestBody @Valid PrintItemReqDto printItemReqDto) {
        List<TypeItemListDTO> typeItemListDTOS = itemClientService.selectTypeItemList(printItemReqDto);
        return Result.buildSuccessResult(typeItemListDTOS);
    }

    @ApiOperation(value = "安卓同步菜品估清列表，必传门店：storeGuid")
    @PostMapping("/query_estimate_for_synchronize")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "安卓同步菜品估清列表",action = OperatorType.SELECT)
    public Result<List<ItemEstimateForAndroidRespDTO>> queryEstimateForSyn(@RequestBody @Valid BaseDTO baseDTO) {
        log.info("query_estimate_for_synchronize入参,request={}", JacksonUtils.writeValueAsString(baseDTO));
        List<ItemEstimateForAndroidRespDTO> itemEstimateForAndroidRespDTOS = itemClientService.queryEstimateForSyn(baseDTO);
        return Result.buildSuccessResult(itemEstimateForAndroidRespDTOS);
    }

    @ApiOperation(value = "门店手动配置商品sku估清")
    @PostMapping("/save_sold_out_status")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "门店手动配置商品sku估清", action = OperatorType.SELECT)
    public Result<Void> saveSoldOutStatus(@RequestBody @Valid EstimateForManualReqDTO request) {
        log.info("估清新增or更新接口入参,request={}", JacksonUtils.writeValueAsString(request));
        Integer num = itemClientService.saveSoldOutStatus(request);
        return Integer.valueOf(1).equals(num) ? Result.buildEmptySuccess() : Result.buildOpFailedResult("保存/更新失败");
    }

    @ApiOperation(value = "安卓同步获取近三天销售模板执行时间列表")
    @PostMapping("/item_template_execute_time_synchronize")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "安卓同步获取近三天销售模板执行时间列表",action = OperatorType.SELECT)
    public Result<Map<String, Object>> queryTimeAndTypeForSyn(@RequestBody @Valid SingleDataDTO request) {
        log.info(" 安卓同步获取近三天销售模板执行时间列表接口入参,request={}", JacksonUtils.writeValueAsString(request));
        List<Long> times = itemClientService.queryTimeAndTypeForSyn(request);
        Map<String, Object> result = new HashMap<>();
        result.put("times", times);
        return Result.buildSuccessResult(result);
    }

    @ApiOperation(value = "新建临时菜")
    @PostMapping("/save_temporary_item")
    @Transactional
    @GlobalTransactional
    public Result<ItemInfoRespDTO> saveTemporaryItem(@RequestBody ItemReqDTO itemSaveReqDTO) {
        log.info("新建临时菜接口入参,itemSaveReqDTO={}", JacksonUtils.writeValueAsString(itemSaveReqDTO));
        return Result.buildSuccessResult(itemService.getItemInfoRespDTOResult(itemSaveReqDTO));
    }

    /**
     * Pad商品查询接口
     *
     * @param baseDTO 门店guid
     * @return Pad分类、商品
     */
    @ApiOperation(value = "Pad商品查询接口,必填参数：storeGuid")
    @PostMapping("/query_item_list_to_pad")
    public Result<PadTypeItemRespDTO> queryItemListToPad(@RequestBody BaseDTO baseDTO) {
        log.info("Pad商品查询接口 入参,baseDTO={}", JacksonUtils.writeValueAsString(baseDTO));
        return Result.buildSuccessResult(itemService.queryItemListToPad(baseDTO));
    }

    @ApiOperation(value = "打印商品标签单", notes = "打印商品标签单")
    @PostMapping("/print/barCode")
    public Result<Void> printItemBarCode(@RequestBody @Valid ItemBarCodeReqDTO itemBarCodeReqDTO) {
        log.info("商品条码打印,入参:{}", JacksonUtils.writeValueAsString(itemBarCodeReqDTO));
        tradeItemClientService.printItemBarCode(itemBarCodeReqDTO);
        return Result.buildEmptySuccess();
    }
}
