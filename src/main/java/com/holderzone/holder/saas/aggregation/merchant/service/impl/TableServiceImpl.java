package com.holderzone.holder.saas.aggregation.merchant.service.impl;

import com.holderzone.holder.saas.aggregation.merchant.service.TableService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.table.TableServiceClient;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.table.TableBatchCreateDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableServiceImpl
 * @date 2019/01/07 10:17
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Service
public class TableServiceImpl implements TableService {

    private final TableServiceClient tableServiceClient;

    @Autowired
    public TableServiceImpl(TableServiceClient tableServiceClient) {
        this.tableServiceClient = tableServiceClient;
    }

    @Override
    public String add(TableBasicDTO tableBasicDTO) {
        return tableServiceClient.addTable(tableBasicDTO);
    }

    @Override
    public String updateTable(TableBasicDTO tableBasicDTO) {
        return tableServiceClient.updateTable(tableBasicDTO);
    }

    @Override
    public List<String> deleteAll(List<String> guids) {
        return tableServiceClient.deleteAllTable(guids);
    }

    @Override
    public List<String> delete(List<String> guids) {
        return tableServiceClient.deleteTable(guids);
    }

    @Override
    public List<TableBasicDTO> query(TableBasicQueryDTO tableBasicQueryDTO) {
        return tableServiceClient.queryTableByWeb(tableBasicQueryDTO);
    }

    @Override
    public String batchCreateTable(TableBatchCreateDTO tableBatchCreateDTO) {
        return tableServiceClient.createTable(tableBatchCreateDTO);
    }
}
