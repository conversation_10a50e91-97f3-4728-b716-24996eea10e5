package com.holderzone.holder.saas.aggregation.app.service.feign.report;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.journaling.req.DailySaleDetailReqDTO;
import com.holderzone.saas.store.dto.journaling.req.JournalStoreAppBaseReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.RetailBusinessDataRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.TotalPaymentRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.TotalSaleRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReportClientService
 * @date 2019/10/29 16:02
 * @description 报表feignClient
 * @program holder-saas-store
 */
@Component
@FeignClient(name = "holder-saas-store-report", fallbackFactory = ReportClientService.ServiceFallBack.class)
public interface ReportClientService {
    @PostMapping("/daily/total")
    RetailBusinessDataRespDTO businessDaily(@RequestBody JournalStoreAppBaseReqDTO journalStoreAppBaseReqDTO);

    @PostMapping("/daily/totalPayment")
    TotalPaymentRespDTO totalPayment(@RequestBody JournalStoreAppBaseReqDTO journalStoreAppBaseReqDTO);

    @PostMapping("/daily/sale_detail")
    TotalSaleRespDTO saleDetail(@RequestBody DailySaleDetailReqDTO dailySaleDetailReqDTO);

    @Component
    @Slf4j
    class ServiceFallBack implements FallbackFactory<ReportClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ReportClientService create(Throwable throwable) {
            return new ReportClientService() {

                @Override
                public RetailBusinessDataRespDTO businessDaily(JournalStoreAppBaseReqDTO journalStoreAppBaseReqDTO) {
                    log.error(HYSTRIX_PATTERN, "businessDaily", JacksonUtils.writeValueAsString(journalStoreAppBaseReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public TotalPaymentRespDTO totalPayment(JournalStoreAppBaseReqDTO journalStoreAppBaseReqDTO) {
                    log.error(HYSTRIX_PATTERN, "upload", JacksonUtils.writeValueAsString(journalStoreAppBaseReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public TotalSaleRespDTO saleDetail(DailySaleDetailReqDTO dailySaleDetailReqDTO) {
                    log.error(HYSTRIX_PATTERN, "upload", JacksonUtils.writeValueAsString(dailySaleDetailReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
