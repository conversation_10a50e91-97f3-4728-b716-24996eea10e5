package com.holderzone.holder.saas.aggregation.app.controller.erp;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.erp.ErpRpcService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.erpretail.RepertorySumDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.*;
import com.holderzone.saas.store.dto.erp.erpretail.resp.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/repertory")
@Api(description = "库存")
public class ErpController {

    @Autowired
    private ErpRpcService erpRpcService;

    @Autowired
    public ErpController(ErpRpcService erpRpcService) {
        this.erpRpcService = erpRpcService;
    }

    @ApiOperation("查询商品库存总数，动销，data 传 storeGuid")
    @PostMapping("/query_repertory_sum")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "查询商品库存总数，动销", action = OperatorType.SELECT)
    public Result<RepertorySumDTO> queryRepertorySum(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("查询商品信息入参：{}", singleDataDTO);
        return Result.buildSuccessResult(erpRpcService.queryRepertorySum(singleDataDTO));
    }

    @ApiOperation("新建库存单")
    @PostMapping("/insert_repertory")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "新建库存单", action = OperatorType.ADD)
    public Result<Boolean> insertRepertory(@RequestBody @Validated CreateRepertoryReqDTO createRepertoryReqDTO) {
        log.info("新建库存单入参：{}", JacksonUtils.writeValueAsString(createRepertoryReqDTO));
        return Result.buildSuccessResult(erpRpcService.insertRepertory(createRepertoryReqDTO));
    }

    @ApiOperation("销售出库")
    @PostMapping("/sale_out_repertory")
    public Result<Boolean> saleOutRepertory(@RequestBody SubstractRepertoryForTradeReqDTO substractRepertoryForTradeReqDTO) {
        log.info("销售出库入参：{}", JacksonUtils.writeValueAsString(substractRepertoryForTradeReqDTO));
        return Result.buildSuccessResult(erpRpcService.saleOutRepertory(substractRepertoryForTradeReqDTO));
    }

    @ApiOperation("查询商品库存汇总信息")
    @PostMapping("/query_goods_repertory_sum_info")
    public Result<Page<GoodsSumInfoRespDTO>> queryGoodsRepertorySumInfo(@RequestBody @Validated QueryGoodsSumInfoReqDTO queryGoodsSumInfoReqDTO) {
        log.info("查询商品库存汇总信息入参：{}", JacksonUtils.writeValueAsString(queryGoodsSumInfoReqDTO));
        return Result.buildSuccessResult(erpRpcService.queryGoodsRepertorySumInfo(queryGoodsSumInfoReqDTO));
    }

    @ApiOperation("查询商品出入库流水")
    @PostMapping("/query_goods_serial")
    public Result<Page<GoodsSerialRespDTO>> queryGoodsSerial(@RequestBody @Validated QueryGoodsSerialReqDTO queryGoodsSerialReqDTO) {
        log.info("查询商品出入库流水：{}", JacksonUtils.writeValueAsString(queryGoodsSerialReqDTO));
        return Result.buildSuccessResult(erpRpcService.queryGoodsSerial(queryGoodsSerialReqDTO));
    }

    @ApiOperation("查询出入库单据及其明细(仅查看时使用),data 传 guid")
    @PostMapping("/query_repertory_detail")
    public Result<RepertoryDetailInfoRespDTO> queryRepertoryDetail(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("查询出入库单据及其明细(仅查看时使用)入参：{}", JacksonUtils.writeValueAsString(singleDataDTO));
        if (StringUtils.isEmpty(singleDataDTO.getData())) {
            throw new ParameterException("单据guid不能为空");
        }
        return Result.buildSuccessResult(erpRpcService.queryRepertoryDetail(singleDataDTO));
    }

    @ApiOperation("查询出入库列表(分页)")
    @PostMapping("/query_in_out_repertory_list")
    public Result<Page<RepertoryManageRespDTO>> queryInOutRepertoryList(@RequestBody @Validated QueryRepertoryManageReqDTO queryRepertoryManageReqDTO) {
        log.info("查询出入库列表(分页)入参: {}", JacksonUtils.writeValueAsString(queryRepertoryManageReqDTO));
        return Result.buildSuccessResult(erpRpcService.queryInOutRepertoryList(queryRepertoryManageReqDTO));
    }

    @ApiOperation("作废库存单，data 传 guid")
    @PostMapping("/invalid_repertory")
    public Result<Boolean> invalidRepertory(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("作废库存单入参: {}", JacksonUtils.writeValueAsString(singleDataDTO));
        return Result.buildSuccessResult(erpRpcService.invalidRepertory(singleDataDTO));
    }

    @ApiOperation("查询商品列表，仅包含开启库存的商品")
    @PostMapping("/query_goods_list")
    public Result<List<GoodsClassifyAndItemRespDTO>> queryGoodsList(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("查询商品列表入参：{}", JacksonUtils.writeValueAsString(singleDataDTO));
        return Result.buildSuccessResult(erpRpcService.queryGoodsList(singleDataDTO));
    }
}
