package com.holderzone.holder.saas.aggregation.app.entity.auth;

import com.holderzone.holder.saas.aggregation.app.anno.DataAuthFieldControl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/14
 * @since 1.8
 */
@Data
@ApiModel
public class RefundSaleAmountDTO {

    @ApiModelProperty(value = "退款方式编码 1 正餐部分退款 2 正餐反结账 3 快销部分退款 4 快销反结账")
    private String refundCode;

    @ApiModelProperty(value = "退款方式名称")
    private String refundName;

    @ApiModelProperty(value = "退款笔数")
    @DataAuthFieldControl("refund_sale_order_count")
    private String refundOrderCount;

    @ApiModelProperty(value = "退款总金额")
    @DataAuthFieldControl("refund_sale_amount")
    private String refundAmount;

    @ApiModelProperty(value = "是否合计项,0否 1是")
    private int isTotal = 0;
}
