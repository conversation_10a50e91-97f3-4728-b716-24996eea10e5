package com.holderzone.holder.saas.aggregation.weixin.utils.map;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.weixin.deal.ItemInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ShopCardItemMAP {

    ShopCardItemMAP INSTANCE = Mappers.getMapper(ShopCardItemMAP.class);

    @Mappings({
            @Mapping(target = "itemPrice", source = "originalPrice")
    })
    DineInItemDTO itemInfoDTO2DineInItemDTO(ItemInfoDTO itemInfoDTO);

    List<DineInItemDTO> itemInfoDTOList2DineInItemDTOList(List<ItemInfoDTO> shopCartItemList);
}
