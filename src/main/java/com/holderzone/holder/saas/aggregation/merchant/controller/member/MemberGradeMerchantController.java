package com.holderzone.holder.saas.aggregation.merchant.controller.member;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.exception.ParamException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.MemberGradeClientService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.member.grade.AccountValidityDTO;
import com.holderzone.saas.store.dto.member.grade.MemberGradeDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberMerchantController
 * @date 2018/09/16 下午9:22
 * @description web端会员接口
 * @program holder-saas-aggregation-merchant
 */
@RestController
@RequestMapping("/grade")
@Api(description = "web端会员等级接口")
public class MemberGradeMerchantController {

    private static final Logger logger = LoggerFactory.getLogger(MemberGradeMerchantController.class);

    @Autowired
    private MemberGradeClientService memberGradeClientService;

    @ApiOperation(value = "账号有效期设置", notes = "账号有效期设置")
    @ApiImplicitParam(name = "accountValidityDTO", value = "账号有效期设置实体accountValidityDTO", required = true, dataType =
            "AccountValidityDTO")
    @PostMapping(value = "/update_account_validity", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "账号有效期设置")
    public Result updateAccountValidity(@RequestBody @Validated AccountValidityDTO accountValidityDTO) throws
            ParamException {
        if (logger.isInfoEnabled()) {
            logger.info("账号有效期调整，accountValidityDTO={}", JacksonUtils.writeValueAsString(accountValidityDTO));
        }
        if (memberGradeClientService.updateAccountValidity(accountValidityDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.OPERATION_FAILED));
    }

    @ApiOperation(value = "查询当前账号有效期", notes = "查询当前账号有效期")
    @ApiImplicitParam(name = "baseDTO", value = "查询当前账号有效期实体baseDTO", dataType = "BaseDTO")
    @PostMapping(value = "/get_account_validity", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "查询当前账号有效期")
    public Result<AccountValidityDTO> getAccountValidity(@RequestBody BaseDTO baseDTO) throws ParamException {
        return Result.buildSuccessResult(memberGradeClientService.getAccountValidity());
    }

    @ApiOperation(value = "增加会员等级", notes = "增加会员等级")
    @ApiImplicitParam(name = "memberGradeDTO", value = "增加会员等级实体memberGradeDTO", required = true, dataType =
            "MemberGradeDTO")
    @PostMapping(value = "/add_member_grade", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "增加会员等级")
    public Result addMemberGrade(@RequestBody @Validated MemberGradeDTO memberGradeDTO) throws ParamException {
        if (logger.isInfoEnabled()) {
            logger.info("增加会员等级，memberGradeDTO={}", JacksonUtils.writeValueAsString(memberGradeDTO));
        }
        if (memberGradeClientService.addMemberGrade(memberGradeDTO)) {
            return Result.buildSuccessMsg("新增成功");
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.OPERATION_FAILED));
    }

    @ApiOperation(value = "修改会员等级", notes = "修改会员等级")
    @ApiImplicitParam(name = "memberGradeDTO", value = "修改会员等级实体memberGradeDTO", required = true, dataType =
            "MemberGradeDTO")
    @PostMapping(value = "/update_member_grade", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "修改会员等级")
    public Result updateMemberGrade(@RequestBody @Validated MemberGradeDTO memberGradeDTO) throws ParamException {
        if (logger.isInfoEnabled()) {
            logger.info("修改会员等级，memberGradeDTO={}", JacksonUtils.writeValueAsString(memberGradeDTO));

        }
        if (memberGradeClientService.updateMemberGrade(memberGradeDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.UPDATE_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.OPERATION_FAILED));
    }

    @ApiOperation(value = "会员等级列表", notes = "会员等级列表")
    @ApiImplicitParam(name = "baseDTO", value = "会员等级列表实体baseDTO", dataType =
            "BaseDTO")
    @PostMapping(value = "/member_grade_list", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "会员等级列表")
    public Result<List<MemberGradeDTO>> memberGradeList(@RequestBody BaseDTO baseDTO) throws ParamException {
        logger.info("会员等级列表入参,BaseDTO={}",baseDTO);
        return Result.buildSuccessResult(memberGradeClientService.memberGradeList(baseDTO));
    }

    @ApiOperation(value = "会员等级详情", notes = "会员等级详情")
    @ApiImplicitParam(name = "memberGradeDTO", value = "会员等级详情实体memberGradeDTO", dataType =
            "MemberGradeDTO")
    @PostMapping(value = "/member_grade_detail", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "会员等级详情")
    public Result<MemberGradeDTO> memberGradeDetail(@RequestBody MemberGradeDTO memberGradeDTO) throws ParamException {
        logger.info("会员等级详情入参,BaseDTO={}",memberGradeDTO);
        return Result.buildSuccessResult(memberGradeClientService.memberGradeDetail(memberGradeDTO));
    }

    @ApiOperation(value = "删除等级", notes = "删除等级")
    @ApiImplicitParam(name = "memberGradeDTO", value = "删除等级实体memberGradeDTO", required = true, dataType =
            "MemberGradeDTO")
    @PostMapping(value = "/delete_member_grade", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "删除等级")
    public Result deleteMemberGrade(@RequestBody MemberGradeDTO memberGradeDTO) throws ParamException {
        if (logger.isInfoEnabled()) {
            logger.info("删除等级，memberGradeDTO={}", JacksonUtils.writeValueAsString(memberGradeDTO));
        }
        if (memberGradeClientService.deleteMemberGrade(memberGradeDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.DELETION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.OPERATION_FAILED));
    }
}
