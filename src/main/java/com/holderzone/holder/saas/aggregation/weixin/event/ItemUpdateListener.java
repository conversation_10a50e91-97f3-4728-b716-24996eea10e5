package com.holderzone.holder.saas.aggregation.weixin.event;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import com.holderzone.framework.dynamic.datasource.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.holder.saas.aggregation.weixin.config.RocketMqConfig;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.ItemClientService;
import com.holderzone.holder.saas.weixin.common.CacheName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.item.resp.ItemAndTypeForAndroidRespDTO;
import com.holderzone.saas.store.dto.weixin.deal.mq.ItemUpdateMQDTO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RocketListenerHandler(
		topic = RocketMqConfig.ITEM_UPDATE_TOPIC,
		tags = RocketMqConfig.ITEM_UPDATE_TAG,
		consumerGroup = RocketMqConfig.ITEM_UPDATE_CONSUME_GROUP
)
public class ItemUpdateListener extends AbstractRocketMqConsumer<RocketMqTopic, ItemUpdateMQDTO> {

	private final ItemClientService itemClientService;

	private final RedisUtils redisUtils;

	@Autowired
	public ItemUpdateListener(ItemClientService itemClientService, RedisUtils redisUtils) {
		this.itemClientService = itemClientService;
		this.redisUtils = redisUtils;
	}

	@Override
	public boolean consumeMsg(ItemUpdateMQDTO itemUpdateMQDTO, MessageExt messageExt) {
		log.info("接收商品信息变更:{}", itemUpdateMQDTO);
		String enterpriseGuid = itemUpdateMQDTO.getEnterpriseGuid();
		List<String> storeGuidList = itemUpdateMQDTO.getStoreGuidList();
		if (StringUtils.isEmpty(enterpriseGuid) || ObjectUtils.isEmpty(storeGuidList)) {
			throw new RuntimeException("接收商品信息变更失败：" + enterpriseGuid + "," + storeGuidList);
		}
		EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
		for (String storeGuid : storeGuidList) {
			BaseDTO baseDTO = new BaseDTO();
			baseDTO.setStoreGuid(storeGuid);
			redisUtils.hDelete(CacheName.ITEM_DETAILS+storeGuid);
			ItemAndTypeForAndroidRespDTO itemsForWeixin = itemClientService.getItemsForWeixin(baseDTO);
			log.info("捕获商品变更，拉取最新商品:{}",itemsForWeixin);
			if (!ObjectUtils.isEmpty(itemsForWeixin)) {
				redisUtils.set(CacheName.ITEM_DETAILS+storeGuid,itemsForWeixin);
			}
		}
		return false;
	}
}
