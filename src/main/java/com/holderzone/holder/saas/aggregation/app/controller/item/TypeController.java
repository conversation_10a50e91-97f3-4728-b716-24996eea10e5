package com.holderzone.holder.saas.aggregation.app.controller.item;

import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.app.service.feign.item.ItemClientService;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.resp.SaleTypeRespDTO;
import com.holderzone.saas.store.dto.item.resp.TypeSynRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/10/29
 * @description 商品分类接口
 */
@Slf4j
@ResponseBody
@RestController
@RequestMapping("/type")
@Api(tags = "商品分类接口")
public class TypeController {

    private final ItemClientService itemClientService;

    @Autowired
    public TypeController(ItemClientService itemClientService) {
        this.itemClientService = itemClientService;
    }

    /**
     * 通过门店查询门店商品在品牌库所属的分类
     */
    @ApiOperation(value = "通过门店查询门店商品在品牌库所属的分类")
    @PostMapping("/query_brand_type_by_store")
    public Result<List<SaleTypeRespDTO>> queryBrandTypeByStore(@RequestBody ItemSingleDTO query) {
        log.info("[通过门店查询门店商品在品牌库所属的分类],query={}", query);
        return Result.buildSuccessResult(itemClientService.queryBrandTypeByStore(query));
    }

}
