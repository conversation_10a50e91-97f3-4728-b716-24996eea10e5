package com.holderzone.holder.saas.aggregation.app.execption;

import com.holderzone.framework.util.StringUtils;
import io.seata.core.context.RootContext;
import io.seata.core.exception.TransactionException;
import io.seata.tm.api.GlobalTransactionContext;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class GlobalTransactionalHandler {


    public static void afterReturning() {
        try {
            if (!StringUtils.isEmpty(RootContext.getXID())) {
                log.info("分布式事务Id:{}", RootContext.getXID());
                GlobalTransactionContext.reload(RootContext.getXID()).rollback();
            }
        } catch (TransactionException e) {
            e.printStackTrace();
            log.error("分布式事务回滚失败");
        }
    }

}
