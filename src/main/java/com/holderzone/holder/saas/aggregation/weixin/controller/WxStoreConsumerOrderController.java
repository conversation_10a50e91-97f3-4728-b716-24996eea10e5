package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxStoreConsumerOrderClientService;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDineInOrderDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @description 微信门店订单管理
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreConsumerOrderController
 * @date 2019/3/15
 */
@RestController
@RequestMapping(value="/wx-store-order")
@Api(value="微信门店订单管理,废弃")
@Slf4j
@Deprecated
public class WxStoreConsumerOrderController {

	private final WxStoreConsumerOrderClientService wxStoreConsumerOrderClientService;

	@Autowired
	public WxStoreConsumerOrderController(WxStoreConsumerOrderClientService wxStoreConsumerOrderClientService) {
		this.wxStoreConsumerOrderClientService = wxStoreConsumerOrderClientService;
	}

	@ApiOperation("选好了")
	@PostMapping("/order/list")
	public Result<List<WxStoreConsumerDineInOrderDTO>> getCurrentTableOrderForm(@RequestBody WxStoreConsumerDineInOrderDTO wxStoreConsumerDineInOrderDTO){
		log.info("查询入参{}", JacksonUtils.writeValueAsString(wxStoreConsumerDineInOrderDTO));
		return Result.buildSuccessResult(wxStoreConsumerOrderClientService.getCurrentTableOrderForm(wxStoreConsumerDineInOrderDTO));
	}

	@ApiOperation("修改商品/加菜")
	@PostMapping("/order/sig")
	public Result<WxStoreConsumerDineInOrderDTO> updateDineInOrder(@RequestBody WxStoreConsumerDTO wxStoreConsumerDTO){
		log.info("查询入参{}", JacksonUtils.writeValueAsString(wxStoreConsumerDTO));
		return Result.buildSuccessResult(wxStoreConsumerOrderClientService.updateDineInOrder(wxStoreConsumerDTO));
	}

	@ApiOperation("删除用户预订单")
	@PostMapping(value="/order/del")
	public Result<String> delDineInOrder(@RequestBody WxStoreConsumerDTO wxStoreConsumerDTO) {
		log.info("查询入参{}", JacksonUtils.writeValueAsString(wxStoreConsumerDTO));
		return wxStoreConsumerOrderClientService.delDineInOrder(wxStoreConsumerDTO) ? Result.buildSuccessMsg("操作成功") : Result.buildSuccessMsg("操作失败");
	}

	@ApiOperation("订单确认/加菜")
	@PostMapping("/submit")
	public Result<DineinOrderDetailRespDTO> submitOrder(@RequestBody WxStoreConsumerDineInOrderDTO consumerDineInOrderDTO) {
		log.info("查询入参{}", JacksonUtils.writeValueAsString(consumerDineInOrderDTO));
		return Result.buildSuccessResult(wxStoreConsumerOrderClientService.submitOrder(consumerDineInOrderDTO));
	}

	@ApiOperation(value = "修改整单备注", notes = "修改整单备注")
	@PostMapping("/update_remark")
	public Result<String> updateRemark(@RequestBody CreateDineInOrderReqDTO createDineInOrderReqDTO) {
		return wxStoreConsumerOrderClientService.updateRemark(createDineInOrderReqDTO) ? Result.buildSuccessMsg("操作成功") : Result.buildSuccessMsg("操作失败");
	}

	@ApiOperation(value = "修改就餐人数", notes = "修改就餐人数")
	@PostMapping("/update_guest_count")
	public Result<String> updateGuestCount(@RequestBody CreateDineInOrderReqDTO createDineInOrderReqDTO) {
		return wxStoreConsumerOrderClientService.updateGuestCount(createDineInOrderReqDTO) ? Result.buildSuccessMsg("操作成功") : Result.buildSuccessMsg("操作失败");
	}
}
