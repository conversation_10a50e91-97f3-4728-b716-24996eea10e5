package com.holderzone.holder.saas.aggregation.weixin.service.impl;

import com.holderzone.holder.saas.aggregation.weixin.service.ItemService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.ItemClientService;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商品服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ItemServiceImpl implements ItemService {


    private final ItemClientService itemClientService;

    @Override
    public void setParentDishSkuInfo(List<RequestDishInfo> dishInfoList) {
        if (CollectionUtils.isEmpty(dishInfoList)) {
            return;
        }
        List<String> dishGuidList = dishInfoList.stream().map(RequestDishInfo::getDishGuid).collect(Collectors.toList());
        ItemStringListDTO listDTO = new ItemStringListDTO().setDataList(dishGuidList);
        List<ItemInfoRespDTO> itemInfoRespDTOList = itemClientService.selectItemInfoList(listDTO);
        Map<String, ItemInfoRespDTO> itemInfoRespMap = itemInfoRespDTOList.stream()
                .collect(Collectors.toMap(ItemInfoRespDTO::getItemGuid, a -> a, (k1, k2) -> k1));
        for (RequestDishInfo dishInfo : dishInfoList) {
            String dishGuid = dishInfo.getDishGuid();
            String dishSpecification = dishInfo.getDishSpecification();
            ItemInfoRespDTO itemInfoRespDTO = itemInfoRespMap.get(dishGuid);
            dishInfo.setParentDishGuid(itemInfoRespDTO.getParentGuid());
            List<SkuInfoRespDTO> skuList = itemInfoRespDTO.getSkuList();
            for (SkuInfoRespDTO skuInfoRespDTO : skuList) {
                String skuGuid = skuInfoRespDTO.getSkuGuid();
                if (dishSpecification.equals(skuGuid)) {
                    dishInfo.setParentDishSpecification(skuInfoRespDTO.getParentGuid());
                    break;
                }
            }
        }
    }
}
