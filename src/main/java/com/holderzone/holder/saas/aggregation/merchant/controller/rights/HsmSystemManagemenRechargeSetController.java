package com.holderzone.holder.saas.aggregation.merchant.controller.rights;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.rights.SystemManagemenRechargeSetClientService;
import com.holderzone.holder.saas.member.dto.activity.common.VolumeInfoCommonRespDTO;
import com.holderzone.holder.saas.member.dto.rights.common.SelectStoreInfoDTO;
import com.holderzone.holder.saas.member.dto.rights.request.*;
import com.holderzone.holder.saas.member.enums.rights.RechargeRuleIsAllEnum;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @description 充值规则设置 controller
 * @date 2019/5/18 9:51
 */
@RestController
@RequestMapping("/hsm-system-management-recharge-set")
@Api(description = "权益体系充值规则设置")
public class HsmSystemManagemenRechargeSetController {
    /**
     * 规则尺寸
     */
    public static final int rule_size = 10;
    @Autowired
    private SystemManagemenRechargeSetClientService systemManagemenRechargeSetClientService;

    /**
     * @param hsmRechargeSetReqDTO
     * @return com.holderzone.framework.response.Result
     * @Description 充值规则创建
     * <AUTHOR>
     * @Date 2019/5/18 10:48
     */
    @ApiOperation(value = "创建充值规则", notes = "创建充值规则")
    @PostMapping(value = "/create", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "创建充值规则")
    public Result createRechargeRule(@RequestBody @Validated HsmRechargeSetReqDTO hsmRechargeSetReqDTO) {
        checkOut(hsmRechargeSetReqDTO);
        return Result.buildSuccessResult(systemManagemenRechargeSetClientService.createRechargeRule(hsmRechargeSetReqDTO));
    }

    /**
     * 校验
     *
     * @param hsmRechargeSetReqDTO
     */
    private void checkOut(HsmRechargeSetReqDTO hsmRechargeSetReqDTO) {
        Integer isAll = hsmRechargeSetReqDTO.getIsAll();
        if (isAll == RechargeRuleIsAllEnum.RECHARGE_RULE_IS_ALL_NO.getCode() && CollectionUtils.isEmpty(hsmRechargeSetReqDTO.getHsmRechargeRuleStoreReqDTOList())) {
            throw new ParameterException("使用门店选择了部分后,需选择对应的门店");
        }
    }

    /**
     * @param hsmRechargeSetReqDTO
     * @return com.holderzone.framework.response.Result
     * @Description 充值规则---更新
     * <AUTHOR>
     * @Date 2019/5/18 10:48
     */
    @ApiOperation(value = "更新充值规则", notes = "更新充值规则")
    @PostMapping(value = "/update", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "更新充值规则")
    public Result updateRechargeRule(@RequestBody @Validated HsmRechargeSetReqDTO hsmRechargeSetReqDTO) {
        checkOut(hsmRechargeSetReqDTO);
        if (systemManagemenRechargeSetClientService.updateRechargeRule(hsmRechargeSetReqDTO)) {
            return Result.buildEmptySuccess();
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.UPDATE_FAILED));
    }

    /**
     * @param
     * @return com.holderzone.framework.response.Result
     * @Description 查询门店信息
     * <AUTHOR>
     * @Date 2019/5/20 15:17
     */
    @ApiOperation(value = "查询选择门店信息", notes = "查询选择门店信息,返回对象为数组", response = SelectStoreInfoDTO.class)
    @GetMapping(value = "/querySelectStoreInfo", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "查询选择门店信息")
    public Result querySelectStoreInfo(@ApiParam(value = "sytemMangementGuid 体系ID", required = true) String sytemMangementGuid, @ApiParam(value = "门店名称") String name,
                                       @RequestParam(value = "cardLevelGuid", required = false) String cardLevelGuid,
                                       @RequestParam(value = "relationGuid", required = false) String relationGuid,
                                       @RequestParam(value = "rightTypeCode", required = false) Integer rightTypeCode) {

        return Result.buildSuccessResult(systemManagemenRechargeSetClientService.querySelectStoreInfo(sytemMangementGuid, name, cardLevelGuid, relationGuid, rightTypeCode));
    }

    /**
     * @param
     * @return com.holderzone.framework.response.Result
     * @Description 查询优惠劵信息
     * <AUTHOR>
     * @Date 2019/5/20 15:17
     */
    @ApiOperation(value = "查询优惠劵信息", notes = "查询优惠劵信息,返回对象为page", response = VolumeInfoCommonRespDTO.class)
    @GetMapping(value = "/queryVolumeInfo", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "查询优惠劵信息")
    public Result queryVolumeInfo(QueryVolumeDTO queryVolumeDTO) {

        return Result.buildSuccessResult(systemManagemenRechargeSetClientService.queryVolumeInfo(queryVolumeDTO));
    }
}
