package com.holderzone.holder.saas.aggregation.merchant.controller.member.account;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.HsmMemberLevelDetailClientService;
import com.holderzone.holder.saas.member.dto.account.request.MemberLevelDetailReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberLevelDetailRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HsmMemberLevelDetailController
 * @date 2019/08/21 15:06
 * @description 会员等级明细
 * @program holder-saas-member-account
 */
@RestController
@Api(value = "会员等级明细相关操作")
@RequestMapping(value = "/hsm_member_level_detail")
public class HsmMemberLevelDetailController {

    @Resource
    private HsmMemberLevelDetailClientService hsmMemberLevelDetailClientService;

    /**
     * 通过条件分页查询
     *
     * @param queryReqDTO 查询条件
     * @return 查询结果
     */
    @PostMapping(value = "/listByCondition", produces = "application/json;charset=utf-8")
    @ApiOperation("通过条件分页查询等级明细")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "通过条件分页查询等级明细")
    public Result<Page<MemberLevelDetailRespDTO>> listByCondition(@RequestBody MemberLevelDetailReqDTO queryReqDTO) {
        return Result.buildSuccessResult(hsmMemberLevelDetailClientService.listByCondition(queryReqDTO));
    }
}
