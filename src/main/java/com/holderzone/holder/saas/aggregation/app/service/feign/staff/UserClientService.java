package com.holderzone.holder.saas.aggregation.app.service.feign.staff;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.user.*;
import com.holderzone.saas.store.dto.user.req.AuthorizationReqDTO;
import com.holderzone.saas.store.dto.user.req.UserFaceInputReqDTO;
import com.holderzone.saas.store.dto.user.resp.PermissionsRespDTO;
import com.holderzone.saas.store.dto.user.resp.UserAuthorityBriefDTO;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import com.holderzone.saas.store.dto.user.resp.UserFaceDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className MenuService
 * @date 19-2-11 下午2:01
 * @description 商户后台获取菜单
 * @program holder-saas-store-staff
 */
@Component
@FeignClient(name = "holder-saas-store-staff", fallbackFactory = UserClientService.ServiceFallBack.class)
public interface UserClientService {

    @PostMapping("/menu/get_source_by_user")
    List<MenuSourceDTO> getSourceByUser(@RequestParam("terminalCode") String terminalCode);

    @PostMapping("/menu/get_module_source_by_user")
    List<MenuSourceDTO> getModuleSourceByUser(@RequestParam("terminalCode") String terminalCode);

    @PostMapping("/user_data/query_data_threshold")
    UserDTO queryUserDataThreshold();

    @PostMapping("/user_data/query_aio_users")
    List<UserDTO> queryAIOUsers(BaseDTO baseDTO);

    @PostMapping("/user_data/local_user_info")
    List<LocalUserInfoDTO> queryAllUserToLocal(@RequestBody LocalUserReqDTO localUserReqDTO);

    /***
     * 分页查询员工信息
     * @param userQueryDTO 请求参数
     * @return 返回参数
     */
    @PostMapping(value = "/user/page_query")
    Page<UserDTO> pageQuery(@RequestBody UserQueryDTO userQueryDTO);

    @ApiOperation(value = "门店查询员工信息")
    @PostMapping(value = "/user/find_by_store_guid")
    List<UserDTO> findByStoreGuid(@Validated @RequestBody UserQueryDTO userQueryDTO);

    /**
     * 根据授权码和资源code校验信息，并记录
     *
     * @param reqDTO 授权码，资源code
     * @return Boolean
     */
    @ApiOperation(value = "aio授权")
    @PostMapping(value = "/user_authority/authorize")
    Boolean authorize(@RequestBody AuthorizationReqDTO reqDTO);

    @PostMapping(value = "/user_data/query_store_spinner")
    UserSpinnerDTO queryStoreSpinner();

    @ApiOperation(value = "外部获取员工权限")
    @GetMapping("/menu/get_source_code_on_out")
    List<String> queryUserSourceOnOut(@NotNull(message = "终端code不能为空") @RequestParam("terminalCode") Integer terminalCode,
                                      @NotNull(message = "用户或手机号不能为空") @RequestParam("userOrPhone") String userOrPhone,
                                      @NotNull(message = "企业guid不能为空") @RequestParam("enterpriseGuid") String enterpriseGuid);

    @ApiOperation(value = "录入人脸")
    @PostMapping(value = "/user/inputFace")
    void inputFace(@RequestBody UserFaceInputReqDTO reqDTO);

    @ApiOperation(value = "人脸授权")
    @PostMapping(value = "/user_authority/authorize_face")
    UserFaceDTO authorizeFace(@RequestBody @Validated AuthorizationReqDTO reqDTO);

    @ApiOperation(value = "查询门店有权限的员工信息")
    @PostMapping(value = "/user/store_users")
    List<UserBriefDTO> storeUsers(@RequestParam("storeGuid") String storeGuid);

    @ApiOperation(value = "查询门店下是否有员工勾选自己设置的权限")
    @PostMapping(value = "/user_authority/query_authority/any_match")
    List<PermissionsRespDTO> queryAuthorityAnyMatch(UserAuthorityQueryDTO userAuthorityQueryDTO);

    @ApiOperation(value = "aio授权并返回授权人信息")
    @PostMapping(value = "/user_authority/authorize/user")
    UserAuthorityBriefDTO authorizeUser(@RequestBody AuthorizationReqDTO reqDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<UserClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public UserClientService create(Throwable cause) {
            return new UserClientService() {
                @Override
                public List<MenuSourceDTO> getSourceByUser(String terminalCode) {
                    log.error(HYSTRIX_PATTERN, "getSourceByUser", "终端Code为：" + terminalCode, ThrowableUtils.asString(cause));
                    throw new BusinessException("获取用户资源接口熔断");
                }

                @Override
                public List<MenuSourceDTO> getModuleSourceByUser(String terminalCode) {
                    log.error(HYSTRIX_PATTERN, "getModuleSourceByUser", "终端Code为：" + terminalCode, ThrowableUtils.asString(cause));
                    throw new BusinessException("获取用户模块资源接口熔断");
                }

                @Override
                public UserDTO queryUserDataThreshold() {
                    log.error(HYSTRIX_PATTERN, "queryUserDataThreshold", "无", ThrowableUtils.asString(cause));
                    throw new BusinessException("获取用户数据阈值接口熔断");
                }

                @Override
                public List<UserDTO> queryAIOUsers(BaseDTO baseDTO) {
                    log.error(HYSTRIX_PATTERN, "queryAIOUsers", JacksonUtils.writeValueAsString(baseDTO), ThrowableUtils.asString(cause));
                    throw new BusinessException("获取用户数据阈值接口熔断");
                }

                @Override
                public List<LocalUserInfoDTO> queryAllUserToLocal(LocalUserReqDTO localUserReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryAllUserToLocal", JacksonUtils.writeValueAsString(localUserReqDTO), ThrowableUtils.asString(cause));
                    throw new BusinessException("获取本地化用户数据接口熔断");
                }

                @Override
                public Page<UserDTO> pageQuery(UserQueryDTO userQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "pageQuery", JacksonUtils.writeValueAsString(userQueryDTO), ThrowableUtils.asString(cause));
                    throw new BusinessException("获取员工信息熔断");
                }

                @Override
                public List<UserDTO> findByStoreGuid(UserQueryDTO userQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "findByStoreGuid", JacksonUtils.writeValueAsString(userQueryDTO), ThrowableUtils.asString(cause));
                    throw new BusinessException("获取员工信息熔断");
                }

                @Override
                public Boolean authorize(AuthorizationReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "authorize", JacksonUtils.writeValueAsString(reqDTO)
                            , ThrowableUtils.asString(cause));
                    throw new BusinessException("aio授权接口熔断");
                }

                @Override
                public UserSpinnerDTO queryStoreSpinner() {
                    log.error(HYSTRIX_PATTERN, "queryStoreSpinner", "无", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<String> queryUserSourceOnOut(Integer terminalCode, String userOrPhone, String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN, "queryUserSourceOnOut", terminalCode + "-" + userOrPhone + "-" + enterpriseGuid,
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void inputFace(UserFaceInputReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "inputFace", JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public UserFaceDTO authorizeFace(AuthorizationReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "authorizeFace", JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<UserBriefDTO> storeUsers(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "authorizeFace", storeGuid,
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<PermissionsRespDTO> queryAuthorityAnyMatch(UserAuthorityQueryDTO userAuthorityQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "authorizeFace", userAuthorityQueryDTO,
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public UserAuthorityBriefDTO authorizeUser(AuthorizationReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "authorizeUser", reqDTO,
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }
    }
}
