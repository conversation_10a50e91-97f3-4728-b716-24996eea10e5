package com.holderzone.holder.saas.aggregation.app.aop;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ControllerAspect
 * @date 2018/10/16 上午10:56
 * @description //controller切面
 * @program holder-saas-aggregation-merchant
 */
@Aspect
@Component
public class ControllerAspect {
    private Logger logger = LoggerFactory.getLogger(ControllerAspect.class);

    @Pointcut("execution(* com.holderzone.holder.saas.aggregation.app.controller.*.*.*(..))")
    public void pointCut() {
    }

    @Before("pointCut()")
    public void doBefore(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        if (args != null && args.length > 0) {
            for (Object arg : args) {
                if (arg instanceof BaseDTO) {
                    if (UserContextUtils.getEnterpriseGuid() != null) {
                        ((BaseDTO) arg).setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
                    }
                    if (UserContextUtils.getUserGuid() != null) {
                        ((BaseDTO) arg).setUserGuid(UserContextUtils.getUserGuid());
                    }
                    if (UserContextUtils.getUserName() != null) {
                        ((BaseDTO) arg).setUserName(UserContextUtils.getUserName());
                    }
                    if (UserContextUtils.getUserAccount() != null) {
                        ((BaseDTO) arg).setAccount(UserContextUtils.getUserAccount());
                    }
                    if (UserContextUtils.getStoreGuid() != null) {
                        ((BaseDTO) arg).setStoreGuid(UserContextUtils.getStoreGuid());
                    }
                }
            }
        }
    }
}
