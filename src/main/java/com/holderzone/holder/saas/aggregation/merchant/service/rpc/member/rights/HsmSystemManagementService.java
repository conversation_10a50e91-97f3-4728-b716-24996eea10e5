package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.rights;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.dto.rights.request.CardReqDTO;
import com.holderzone.holder.saas.member.dto.rights.request.HsmSystemManagementReqDTO;
import com.holderzone.holder.saas.member.dto.rights.request.QuerySystemDTO;
import com.holderzone.holder.saas.member.dto.rights.response.SystemRightDetailRespDTO;
import com.holderzone.holder.saas.member.dto.rights.response.SystemRightRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HsmSystemManagementService
 * @date 2019/05/31 16:44
 * @description 体系管理
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-member-account", fallbackFactory = HsmSystemManagementService.HsmSystemManagementServiceFallBack.class)
public interface HsmSystemManagementService {

    /**
     * 删除体系
     *
     * @param systemGuid guid
     * @return true-成功，false-失败
     */
    @ApiOperation("删除体系")
    @DeleteMapping(value = "/hsm/system/management/{systemGuid}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    boolean deleteSystem(@PathVariable("systemGuid") String systemGuid);


    @ApiOperation("设置体系卡发放状态")
    @GetMapping("/hsm/system/management/change/system/{systemGuid}")
    boolean changeSystemCardGrant(@ApiParam(value = "systemGuid",name = "体系guid")@PathVariable("systemGuid") String systemGuid,
                                         @ApiParam(value = "state",name = "状态 0继续发放 1停止发放")@RequestParam("state") Integer state);


    /**
     * 根据体系Guid检查该体系下是否有会员
     * @param systemGuid
     * @return
     */
    @ApiOperation("体系下是否有会员")
    @GetMapping("/hsm/system/management/check/hasmember/{systemGuid}")
   boolean  checkHasMember(@ApiParam(value = "systemGuid", name = "体系guid") @PathVariable("systemGuid") String systemGuid);

    /**
     * 查询体系的列表
     *
     * @param querySystemDTO 查询体系列表
     * @return 体系列表的信息
     */
    @ApiOperation("查询体系的列表")
    @PostMapping(value = "/hsm/system/management/query", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Page<SystemRightRespDTO> querySystem(@RequestBody QuerySystemDTO querySystemDTO);

    /**
     * 获取体系内的详情
     *
     * @param systemGuid 体系Guid
     * @return 体系列表的信息
     */
    @ApiOperation("获取体系会员权益的详情")
    @GetMapping(value = "/hsm/system/management/{systemGuid}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    SystemRightDetailRespDTO getDetailOne(@PathVariable("systemGuid") String systemGuid);

    /**
     * 修改名称
     *
     * @param hsmSystemManagementReqDTO 体系对象
     * @return true-成功，false-失败
     */
    @ApiOperation("修改名称")
    @PostMapping(value = "/hsm/system/management/modify/name", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    boolean modifyName(@RequestBody HsmSystemManagementReqDTO hsmSystemManagementReqDTO);

    /**
     * 修改门店列表
     *
     * @param hsmSystemManagementReqDTO 体系对象
     * @return true-成功，false-失败
     */
    @ApiOperation("修改门店列表")
    @PostMapping(value = "/hsm/system/management/modify/shops", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    boolean modifyShop(@RequestBody HsmSystemManagementReqDTO hsmSystemManagementReqDTO);

    /**
     * 校验并且保存门店
     *
     * @param hsmSystemManagementReqDTO 体系对象
     * @return true-成功，false-失败
     */
    @ApiOperation("校验并且保存门店")
    @PostMapping(value = "/hsm/system/management/check/modify/shops", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    boolean checkAndModifyShop(@RequestBody HsmSystemManagementReqDTO hsmSystemManagementReqDTO);

    /**
     * 校验信息（门店重复、名称）并且保存
     *
     * @param hsmSystemManagementReqDTO 对象
     * @return 返回体系Guid
     */
    @ApiOperation("校验并且保存体系基本信息")
    @PostMapping(value = "/hsm/system/management/check/save", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    HsmSystemManagementReqDTO checkAndSaveBaseInfo(
        @RequestBody HsmSystemManagementReqDTO hsmSystemManagementReqDTO);


    /**
     * 保存体系的基本信息
     *
     * @param hsmSystemManagementReqDTO 体系的基本信息
     * @return 返回体系的Guid
     */
    @ApiOperation("保存体系基本信息")
    @PostMapping(value = "/hsm/system/management/save", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    HsmSystemManagementReqDTO saveBaseInfo(
        @RequestBody HsmSystemManagementReqDTO hsmSystemManagementReqDTO);

    /**
     * 跳过体系中主卡设置
     *
     * @param cardReqDTO 体系会员卡对象
     * @return 返回体系的Guid
     */
    @ApiOperation("保存主卡信息")
    @PostMapping(value = "/hsm/system/management/save/card", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    String saveCard(@RequestBody CardReqDTO cardReqDTO);

    /**
     * 通过会员卡获取体系Guid
     *
     * @return 体系Guid
     */
    @ApiOperation("通过会员卡获取体系Guid")
    @GetMapping("/hsm/system/management/guid/{cardGuid}")
    String findSystemGuidByCardGuid(@PathVariable("cardGuid") String cardGuid);

    @Slf4j
    @Component
    class HsmSystemManagementServiceFallBack implements
        FallbackFactory<HsmSystemManagementService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public HsmSystemManagementService create(Throwable cause) {
            return new HsmSystemManagementService() {
                @Override
                public boolean deleteSystem(String systemGuid) {
                    log.error(HYSTRIX_PATTERN, "deleteSystem",
                        systemGuid, ThrowableUtils
                            .asString(cause));
                    throw new BusinessException("删除体系失败");
                }

                @Override
                public boolean changeSystemCardGrant(String systemGuid, Integer state) {
                    log.error(HYSTRIX_PATTERN, "changeSystemCardGrant",
                            "systemGuid="+systemGuid+"  state="+state, ThrowableUtils
                                    .asString(cause));
                    throw new BusinessException("修改体系卡发放状态失败");
                }

                @Override
                public boolean checkHasMember(String systemGuid) {
                    log.error(HYSTRIX_PATTERN, "checkHasMember",
                            "systemGuid="+systemGuid, ThrowableUtils
                                    .asString(cause));
                    throw new BusinessException("检查体系下是否有会员失败");
                }

                @Override
                public Page<SystemRightRespDTO> querySystem(QuerySystemDTO querySystemDTO) {
                    log.error(HYSTRIX_PATTERN, "querySystem",
                        JSONObject.toJSON(querySystemDTO), ThrowableUtils
                            .asString(cause));
                    throw new BusinessException("体系分页查询失败");
                }

                @Override
                public SystemRightDetailRespDTO getDetailOne(String systemGuid) {
                    log.error(HYSTRIX_PATTERN, "getDetailOne",
                        systemGuid, ThrowableUtils
                            .asString(cause));
                    throw new BusinessException("体系详情失败");
                }

                @Override
                public boolean modifyName(HsmSystemManagementReqDTO hsmSystemManagementReqDTO) {
                    log.error(HYSTRIX_PATTERN, "modifyName",
                        JSONObject.toJSON(hsmSystemManagementReqDTO), ThrowableUtils
                            .asString(cause));
                    throw new BusinessException("修改体系名称失败");
                }

                @Override
                public boolean modifyShop(HsmSystemManagementReqDTO hsmSystemManagementReqDTO) {
                    log.error(HYSTRIX_PATTERN, "modifyShop",
                        JSONObject.toJSON(hsmSystemManagementReqDTO),
                        ThrowableUtils
                            .asString(cause));
                    throw new BusinessException("体系修改门店失败");
                }

                @Override
                public boolean checkAndModifyShop(
                    HsmSystemManagementReqDTO hsmSystemManagementReqDTO) {
                    log.error(HYSTRIX_PATTERN, "checkAndModifyShop",
                        JSONObject.toJSON(hsmSystemManagementReqDTO),
                        ThrowableUtils
                            .asString(cause));
                    throw new BusinessException("体系校验并修改门店失败");
                }

                @Override
                public HsmSystemManagementReqDTO checkAndSaveBaseInfo(
                    HsmSystemManagementReqDTO hsmSystemManagementReqDTO) {
                    log.error(HYSTRIX_PATTERN, "checkAndSaveBaseInfo",
                        JSONObject.toJSON(hsmSystemManagementReqDTO), ThrowableUtils
                            .asString(cause));
                    throw new BusinessException("校验并保存基本信息失败");
                }

                @Override
                public HsmSystemManagementReqDTO saveBaseInfo(
                    HsmSystemManagementReqDTO hsmSystemManagementReqDTO) {
                    log.error(HYSTRIX_PATTERN, "saveBaseInfo",
                        JSONObject.toJSON(hsmSystemManagementReqDTO), ThrowableUtils
                            .asString(cause));
                    throw new BusinessException("保存基本信息失败");
                }

                @Override
                public String saveCard(CardReqDTO cardReqDTO) {
                    log.error(HYSTRIX_PATTERN, "saveCard",
                        JSONObject.toJSON(cardReqDTO), ThrowableUtils
                            .asString(cause));
                    throw new BusinessException("保存主卡信息失败");
                }

                @Override
                public String findSystemGuidByCardGuid(String cardGuid) {
                    log.error(HYSTRIX_PATTERN, "findSystemGuidByCardGuid",
                        cardGuid, ThrowableUtils
                            .asString(cause));
                    throw new BusinessException("通过会员卡获取体系Guid失败");
                }
            };
        }
    }

}
