package com.holderzone.holder.saas.aggregation.merchant.service.rpc.user;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.user.*;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className RoleService
 * @date 19-1-15 下午5:08
 * @description
 * @program holder-saas-store-staff
 */
@Component
@FeignClient(name = "holder-saas-store-staff", fallbackFactory = RoleFeignService.ServiceFallBack.class)
public interface RoleFeignService {
    @PostMapping(value = "/role/create")
    boolean createRole(@RequestBody RoleDTO roleDTO);

    @PostMapping(value = "/role/update")
    boolean updateRole(@RequestBody RoleDTO roleDTO);

    @PostMapping(value = "/role/query_page_by_name")
    Page<RoleDTO> queryPageByName(@RequestBody RoleQueryDTO roleQueryDTO);

    @PostMapping(value = "/role/delete")
    boolean deleteRole(@RequestParam("roleGuid") String roleGuid);

    @PostMapping(value = "/role/query_exist_user")
    boolean queryExistUser(@RequestParam("roleGuid") String roleGuid);

    @PostMapping(value = "/role/copy_role")
    boolean copyRole(@RequestParam("roleGuid") String roleGuid);

    @PostMapping(value = "/role_data/query_role_terminal")
    List<TerminalDTO> queryRoleTerminal(@RequestParam("roleGuid") String roleGuid);

    @PostMapping(value = "/role_data/query_role_terminal_data")
    DefaultMenuChecked queryRoleTerminalData(@RequestParam("roleGuid") String roleGuid, @RequestParam("terminalGuid") String terminalGuid);

    @PostMapping(value = "/role_data/save")
    boolean saveRoleData(@RequestBody TerminalSourceDTO terminalSourceDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<RoleFeignService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public RoleFeignService create(Throwable cause) {
            return new RoleFeignService() {
                @Override
                public boolean createRole(RoleDTO roleDTO) {
                    log.error(HYSTRIX_PATTERN, "createRole", JacksonUtils.writeValueAsString(roleDTO), ThrowableUtils.asString(cause));
                    return false;
                }

                @Override
                public boolean updateRole(RoleDTO roleDTO) {
                    log.error(HYSTRIX_PATTERN, "updateRole", JacksonUtils.writeValueAsString(roleDTO), ThrowableUtils.asString(cause));
                    return false;
                }

                @Override
                public Page<RoleDTO> queryPageByName(RoleQueryDTO roleQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "queryPageByName", JacksonUtils.writeValueAsString(roleQueryDTO), ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public boolean deleteRole(String roleGuid) {
                    log.error(HYSTRIX_PATTERN, "queryPageByName", "入参roleGuid为：" + roleGuid, ThrowableUtils.asString(cause));
                    return false;
                }

                @Override
                public boolean queryExistUser(String roleGuid) {
                    log.error(HYSTRIX_PATTERN, "queryExistUser", "入参roleGuid为：" + roleGuid, ThrowableUtils.asString(cause));
                    return false;
                }

                @Override
                public boolean copyRole(String roleGuid) {
                    log.error(HYSTRIX_PATTERN, "copyRole", "入参roleGuid为：" + roleGuid, ThrowableUtils.asString(cause));
                    throw new BusinessException("复制角色失败");
                }

                @Override
                public List<TerminalDTO>  queryRoleTerminal(String roleGuid) {
                    log.error(HYSTRIX_PATTERN, "queryRoleTerminal", "入参roleGuid为：" + roleGuid, ThrowableUtils.asString(cause));
                    throw new BusinessException("查询所有终端失败");
                }

                @Override
                public DefaultMenuChecked queryRoleTerminalData(String roleGuid, String terminalGuid) {
                    log.error(HYSTRIX_PATTERN, "queryRoleTerminalData", "入参roleGuid为：" + roleGuid + "终端guid为：" + terminalGuid, ThrowableUtils.asString(cause));
                    throw new BusinessException("查询角色权限失败");
                }

                @Override
                public boolean saveRoleData(TerminalSourceDTO terminalSourceDTO) {
                    log.error(HYSTRIX_PATTERN, "saveRoleData", JacksonUtils.writeValueAsString(terminalSourceDTO), ThrowableUtils.asString(cause));
                    throw new BusinessException("保存角色权限失败");
                }
            };
        }
    }
}
