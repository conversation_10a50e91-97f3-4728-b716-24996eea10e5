package com.holderzone.holder.saas.aggregation.app.controller.print;

import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.app.service.feign.PrintClientService;
import com.holderzone.saas.store.dto.print.PrintOrderDTO;
import com.holderzone.saas.store.dto.print.PrintRecordDTO;
import com.holderzone.saas.store.dto.print.PrintRecordReqDTO;
import com.holderzone.saas.store.dto.print.deprecate.PrintContentDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/09/20 11:14
 */
@RestController
@RequestMapping(value = "/print_record")
@Api("打印内容接口")
public class PrintContentController {

    private final PrintClientService printClientService;

    @Autowired
    public PrintContentController(PrintClientService printClientService) {
        this.printClientService = printClientService;
    }

    @PostMapping("/get_content")
    @ApiOperation("打印内容获取接口")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT,description = "打印内容获取接口")
    public Result<List<PrintContentDTO>> getContent(@RequestBody PrintRecordReqDTO printRecordReqDTO) {
        return Result.buildSuccessResult(printClientService.getRecord(printRecordReqDTO));
    }

    @PostMapping("/get_order")
    @ApiOperation("打印内容获取接口")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT,description = "打印内容获取接口")
    public Result<List<PrintOrderDTO>> getOrder(@RequestBody PrintRecordReqDTO printRecordReqDTO) {
        return Result.buildSuccessResult(printClientService.getOrder(printRecordReqDTO));
    }

    @PostMapping("/get_test_order")
    @ApiOperation(value = "测试单据格式")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT,description = "测试单据格式")
    public Result<PrintOrderDTO> getTestPrinterOrder(@RequestBody String formatDTO) {
        return Result.buildSuccessResult(printClientService.getTestPrintOrder(formatDTO));
    }

    @PostMapping("/get_test_orders")
    @ApiOperation(value = "测试单据格式")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT,description = "测试单据格式")
    public Result<List<PrintOrderDTO>> getTestPrinterOrders(@RequestBody String formatDTO) {
        return Result.buildSuccessResult(printClientService.getTestPrintOrders(formatDTO));
    }

    @PostMapping("/update_status")
    @ApiOperation("打印结果更新：1=成功; 2=失败")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT,description = "打印结果更新")
    public Result updateStatus(@RequestBody PrintRecordReqDTO printRecordReqDTO) {
        printClientService.updateStatus(printRecordReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/list")
    @ApiOperation("获取相应打印结果的列表，status：0=打印中, 1=成功, 2=失败")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT,description = "获取相应打印结果的列表")
    public Result<List<PrintRecordDTO>> getRecordList(@RequestBody PrintRecordReqDTO printRecordReqDTO) {
        return Result.buildSuccessResult(printClientService.listRecord(printRecordReqDTO));
    }

    @PostMapping("/delete")
    @ApiOperation("删除单条打印记录")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT,description = "删除单条打印记录")
    public Result deleteRecord(@RequestBody PrintRecordReqDTO printRecordReqDTO) {
        printClientService.deleteRecord(printRecordReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/batch_delete")
    @ApiOperation("删除打印记录列表")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT,description = "删除打印记录列表")
    public Result deleteRecordList(@RequestBody PrintRecordReqDTO printRecordReqDTO) {
        printClientService.batchDeleteRecord(printRecordReqDTO);
        return Result.buildEmptySuccess();
    }
}
