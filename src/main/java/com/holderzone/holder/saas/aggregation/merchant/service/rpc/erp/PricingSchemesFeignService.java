package com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp;

import com.holderzone.saas.store.dto.erp.PricingReqDTO;
import com.holderzone.saas.store.dto.erp.PricingSchemesDTO;
import com.holderzone.saas.store.dto.erp.PricingSchemesQueryDTO;
import feign.hystrix.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @className PricingSchemesFeignService
 * @date 2019-05-05 15:27:21
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-store-erp", fallbackFactory = PricingSchemesFeignService.PricingSchemesFeignServiceFallback.class)
public interface PricingSchemesFeignService {

    /**
     * 保存报价方案
     */
    @PostMapping("/pricing")
    Boolean savePricingSchemes(@RequestBody PricingReqDTO pricingReqDTO);

    /**
     * 报价方案列表(含停止供应)
     */
    @GetMapping("/pricing/{suppliersGuid}")
    List<PricingSchemesDTO> getPricingSchemesList(@PathVariable("suppliersGuid") String suppliersGuid);

    /**
     * 保存报价方案
     */
    @DeleteMapping("/pricing/{guid}")
    Boolean deletePricingSchemes(@PathVariable("guid") String guid);

    /**
     * 启禁用物料报价
     */
    @PutMapping("/pricing/{guid}")
    Boolean enableOrDisablePricingSchemes(@PathVariable("guid") String guid);

    /**
     * 批量查询物料协议单价
     */
    @PostMapping("/pricing/batch")
    List<PricingSchemesDTO> batchQueryPricingSchemesList(@RequestBody PricingSchemesQueryDTO queryDTO);

    @Component
    class PricingSchemesFeignServiceFallback implements FallbackFactory<PricingSchemesFeignService> {

        @Override
        public PricingSchemesFeignService create(Throwable throwable) {
            return new PricingSchemesFeignService() {
                @Override
                public Boolean savePricingSchemes(PricingReqDTO pricingReqDTO) {
                    return null;
                }

                @Override
                public List<PricingSchemesDTO> getPricingSchemesList(String suppliersGuid) {
                    return null;
                }

                @Override
                public Boolean deletePricingSchemes(String guid) {
                    return null;
                }

                @Override
                public Boolean enableOrDisablePricingSchemes(String guid) {
                    return null;
                }

                @Override
                public List<PricingSchemesDTO> batchQueryPricingSchemesList(PricingSchemesQueryDTO queryDTO) {
                    return null;
                }
            };
        }
    }
}
