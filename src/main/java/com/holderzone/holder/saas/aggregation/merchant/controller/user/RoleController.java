package com.holderzone.holder.saas.aggregation.merchant.controller.user;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.entity.enums.CommonLocaleEnum;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.user.RoleFeignService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.user.RoleDTO;
import com.holderzone.saas.store.dto.user.RoleQueryDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className RoleController
 * @date 19-1-15 下午5:07
 * @description
 * @program holder-saas-store-staff
 */
@RestController
@RequestMapping("/role")
@Api(tags = "角色相关接口", description = "商户后台二期角色相关服务接口")
public class RoleController {
    private final RoleFeignService roleFeignService;

    @Autowired
    public RoleController(RoleFeignService roleFeignService) {
        this.roleFeignService = roleFeignService;
    }

    @ApiOperation(value = "创建角色", notes = "创建角色")
    @PostMapping(value = "/create")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF,description = "创建角色")
    public Result createRole(@RequestBody @Validated(RoleDTO.Create.class) RoleDTO roleDTO) {
        if (roleFeignService.createRole(roleDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.CREATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.CREATION_FAILED));
    }

    @ApiOperation(value = "更新角色", notes = "更新角色，角色guid必传")
    @PostMapping(value = "/update")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF,description = "更新角色")
    public Result updateRole(@RequestBody @Validated(RoleDTO.Update.class) RoleDTO roleDTO) {
        if (roleFeignService.updateRole(roleDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.RESULT_MODIFICATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.RESULT_MODIFICATION_FAILED));
    }

    @ApiOperation(value = "根据角色名称分页查询角色列表", notes = "根据角色名称分页查询角色列表，不传角色名称或角色名称为空则默认查询所有角色信息")
    @PostMapping(value = "/query_page_by_name")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF,description = "根据角色名称分页查询角色列表")
    public Result<Page<RoleDTO>> queryPageByName(@RequestBody RoleQueryDTO roleQueryDTO) {
        Page<RoleDTO> page = roleFeignService.queryPageByName(roleQueryDTO);
        if(page != null && CollectionUtil.isNotEmpty(page.getData())){
            page.getData().forEach(role -> {
                if(CollectionUtil.isNotEmpty(role.getEnableLoginSourceList())){
                    List<String> localeSourceList = Lists.newArrayList();
                    role.getEnableLoginSourceList().forEach(e -> localeSourceList.add(CommonLocaleEnum.getLocale(e)));
                    role.setEnableLoginSourceList(localeSourceList);
                }
            });
        }
        return Result.buildSuccessResult(page);
    }

    @ApiOperation(value = "删除角色", notes = "根据角色guid删除角色")
    @PostMapping(value = "/delete")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF,description = "删除角色")
    public Result deleteRole(@ApiParam("实体中的业务请求参数为角色guid") @RequestBody SingleDataDTO singleDataDTO) {
        if (roleFeignService.deleteRole(singleDataDTO.getData())) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.DELETION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.DELETION_FAILED));
    }

    @ApiOperation(value = "判断该角色下是否存在帐号", notes = "根据角色guid判断该角色下是否存在帐号，true-存在，false-不存在")
    @PostMapping(value = "/query_exist_user")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF,description = "判断该角色下是否存在帐号")
    public Result queryExistUser(@ApiParam("实体中的业务请求参数为角色guid") @RequestBody SingleDataDTO singleDataDTO) {
        return Result.buildSuccessResult(roleFeignService.queryExistUser(singleDataDTO.getData()));
    }

    @ApiOperation(value = "复制角色", notes = "根据角色guid复制角色（包括角色权限）")
    @PostMapping(value = "/copy_role")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF,description = "复制角色")
    public Result copyRole(@ApiParam("实体中的业务请求参数为角色guid") @RequestBody SingleDataDTO singleDataDTO) {
        return Result.buildSuccessResult(roleFeignService.copyRole(singleDataDTO.getData()));
    }
}