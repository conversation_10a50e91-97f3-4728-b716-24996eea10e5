package com.holderzone.holder.saas.aggregation.weixin.service.rpc.account;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseAccountStatus;
import com.holderzone.holder.saas.member.wechat.enums.MemberStateEnum;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.TimeUnit;


@Slf4j
@Component
@RequiredArgsConstructor
public class WxHsmMemberBasicBufferService {

    public final HsaBaseClientService hsaBaseClientService;

    private final RedisUtils redisUtils;

    public static final String MEMBER_ACCOUNT_KEY = "WX:MEMBER_ACCOUNT:%s";
    public static final int MEMBER_ACCOUNT_TIMEOUT = 300;

    /**
     * 查询会员账户状态
     */
    public Integer getWxMemberState(String phoneNumOrOpenid, String enterpriseGuid, String operSubjectGuid) {
        log.info("获取会员账号状态信息, phoneNumOrOpenid:{},enterpriseGuid:{},operSubjectGuid:{}",
                phoneNumOrOpenid, enterpriseGuid, operSubjectGuid);
        ResponseAccountStatus memberAccount = getMemberAccount(phoneNumOrOpenid, enterpriseGuid, operSubjectGuid);
        if (Objects.isNull(memberAccount)) {
            return MemberStateEnum.FREEZE.getState();
        }
        return memberAccount.getAccountState();
    }

    /**
     * 查询会员账户信息
     * 优先从缓存中查询
     */
    public ResponseAccountStatus getMemberAccount(String phoneNumOrOpenid, String enterpriseGuid, String operSubjectGuid) {
        log.info("获取会员账号信息, phoneNumOrOpenid:{},enterpriseGuid:{},operSubjectGuid:{}",
                phoneNumOrOpenid, enterpriseGuid, operSubjectGuid);
        String key = String.format(MEMBER_ACCOUNT_KEY, phoneNumOrOpenid + enterpriseGuid);
        Object accountRedis = redisUtils.get(key);
        if (accountRedis == null) {
            ResponseAccountStatus account = queryMemberAccount(phoneNumOrOpenid, enterpriseGuid, operSubjectGuid);
            if (Objects.nonNull(account)) {
                redisUtils.setEx(key, JacksonUtils.writeValueAsString(account), MEMBER_ACCOUNT_TIMEOUT, TimeUnit.SECONDS);
                return account;
            }
            return null;
        }
        ResponseAccountStatus account = JacksonUtils.toObject(ResponseAccountStatus.class, accountRedis.toString());
        // 比较缓存中的运营主体和需要查询的运营主体是否一致
        if (!StringUtils.isEmpty(operSubjectGuid)
                && !operSubjectGuid.equals(account.getOperSubjectGuid())
                && compareOperSubjectGuid(operSubjectGuid, account)) {
            account = queryMemberAccount(phoneNumOrOpenid, enterpriseGuid, operSubjectGuid);
            if (Objects.nonNull(account)) {
                redisUtils.setEx(key, JacksonUtils.writeValueAsString(account), MEMBER_ACCOUNT_TIMEOUT, TimeUnit.SECONDS);
            }
        }
        return account;
    }

    /**
     * 查询会员账户信息
     */
    private ResponseAccountStatus queryMemberAccount(String phoneNumOrOpenid, String enterpriseGuid, String operSubjectGuid) {
        ResponseModel<ResponseAccountStatus> memberAccount = hsaBaseClientService.getMemberState(phoneNumOrOpenid,
                enterpriseGuid, operSubjectGuid);
        if (memberAccount.getCode() == 0) {
            return memberAccount.getData();
        }
        return null;
    }

    public void removeByOpenId(String openId, String enterpriseGuid) {
        String key = openId + enterpriseGuid;
        redisUtils.delete(key);
        String wxMemberStateKey = String.format(MEMBER_ACCOUNT_KEY, openId + enterpriseGuid);
        redisUtils.delete(wxMemberStateKey);
        log.info("清除会员状态缓存,key:{}, wxMemberStateKey:{}", key, wxMemberStateKey);
    }

    private boolean compareOperSubjectGuid(String operSubjectGuid, ResponseAccountStatus account) {
        if (StringUtils.isEmpty(operSubjectGuid) || StringUtils.isEmpty(account.getOperSubjectGuid())) {
            return true;
        }
        if (!Boolean.TRUE.equals(account.getCenterMemberFlag())) {
            return true;
        }
        // 因为会员中台的运营主体和餐饮云的运营主体不一致，所以需要比较运营主体是否一致
        String transferBeforeOperSubjectGuid = account.getTransferBeforeOperSubjectGuid();
        return !operSubjectGuid.equals(transferBeforeOperSubjectGuid);
    }
}
