package com.holderzone.holder.saas.aggregation.weixin.service.chain;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.context.DiscountContext;
import com.holderzone.holder.saas.aggregation.weixin.helper.DineInItemHelper;
import com.holderzone.holder.saas.aggregation.weixin.helper.PriceCalculateHelper;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.HsmTerminalServiceClient;
import com.holderzone.holder.saas.aggregation.weixin.utils.map.DiscountMAP;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.member.terminal.dto.order.RequestDiscount;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseDiscount;
import com.holderzone.holder.saas.weixin.utils.BigDecimalUtil;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.weixin.MinPriceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 会员折扣
 */
@Component
@Slf4j
@AllArgsConstructor
public class MemberDiscountHandler extends DiscountHandler {

    private final HsmTerminalServiceClient terminalServiceClient;

    private final PriceCalculateHelper priceCalculateHelper;

    @Override
    void dealDiscount(DiscountContext context) {
        if (context.isRejectDiscount()) {
            return;
        }

        // 会员折扣
        DiscountFeeDetailDTO member = DiscountMAP.INSTANCE.discountDO2DiscountFeeDetailDTO(context.getDiscountTypeMap().get(type()));
        if (!context.isHasMember() || Boolean.FALSE.equals(context.getUseMemberDiscountFlag())) {
            member.setDiscountFee(BigDecimal.ZERO);
            context.getDiscountFeeDetailDTOS().add(member);
            return;
        }

        // B:代金券+会员折扣
        ResponseDiscount pureDiscount = calculateDiscountByPure(context);

        // A:限时特价+代金券
        DiscountFeeDetailDTO limitSpecialsActivity = context.getDiscountFeeDetailDTOS().stream()
                .filter(d -> Objects.equals(d.getDiscountType(), DiscountTypeEnum.LIMIT_SPECIALS_ACTIVITY.getCode()))
                .findFirst()
                .orElse(null);
        if (!ObjectUtils.isEmpty(limitSpecialsActivity) &&
                BigDecimalUtil.greaterThanZero(limitSpecialsActivity.getDiscountFee())) {
            log.info("[限时特价+代金券]specialsActivityFee={}", limitSpecialsActivity.getDiscountFee());

            // 共享：限时特价+代金券+会员折扣
            ResponseDiscount discountShare = calculateDiscountByShare(context);

            priceCalculateHelper.calculateSpecialsActivityByMemberDiscount(context, member, pureDiscount, limitSpecialsActivity, discountShare);
            log.info("[限时特价[会员折扣]计算后订单剩余金额：{}，优惠金额：{}，菜品优惠：{}", context.getCalculateOrderRespDTO().getOrderSurplusFee(),
                    member.getDiscountFee(), JacksonUtils.writeValueAsString(context.getAllItems()));
            context.getDiscountFeeDetailDTOS().add(member);
            return;
        }

        noSpecialsMemberDiscountHandle(context, pureDiscount, member);
    }

    private ResponseDiscount calculateDiscountByShare(DiscountContext context) {
        RequestDiscount discountShareReqDTO = buildDiscountReqDTO(context,
                context.getCalculateOrderRespDTO().getOrderSurplusFee(), context.getAllItems());
        log.info("[限时特价+代金券+会员折扣]会员折扣请求：{}", JacksonUtils.writeValueAsString(discountShareReqDTO));
        ResponseDiscount discountShare = terminalServiceClient.calculateDiscount(discountShareReqDTO);
        log.info("[限时特价+代金券+会员折扣]会员折扣返回：{}", JacksonUtils.writeValueAsString(discountShare));
        return discountShare;
    }

    private ResponseDiscount calculateDiscountByPure(DiscountContext context) {
        RequestDiscount discountReqDTO = buildDiscountReqDTO(context,
                context.getCalculateOrderRespDTO().getOrderSurplusFeeBySkipSpecials(),
                context.getBeforeSpecialsItems());
        log.info("[代金券+会员折扣]会员折扣请求：{}", JacksonUtils.writeValueAsString(discountReqDTO));
        ResponseDiscount pureDiscount = terminalServiceClient.calculateDiscount(discountReqDTO);
        log.info("[代金券+会员折扣]会员折扣返回：{}", JacksonUtils.writeValueAsString(pureDiscount));
        return pureDiscount;
    }

    private void noSpecialsMemberDiscountHandle(DiscountContext context,
                                                ResponseDiscount discount,
                                                DiscountFeeDetailDTO member) {
        // 优惠金额处理
        handleDiscountTotalPrice(context, discount.getRequestDishInfoList());
        // 商品最低价处理
        setMinPrice(context, discount);
        // 会员折扣
        memberDiscount(context.getDineInItemDTOMap(), member, discount);
        // 订单剩余金额更新
        context.getCalculateOrderRespDTO().setOrderSurplusFee(context.getCalculateOrderRespDTO().getOrderSurplusFee()
                .subtract(member.getDiscountFee()));
        context.getDiscountFeeDetailDTOS().add(member);
        log.info("会员折扣计算后订单剩余金额：{}，优惠金额：{}，菜品优惠：{}", context.getCalculateOrderRespDTO().getOrderSurplusFee(),
                member.getDiscountFee(), JacksonUtils.writeValueAsString(context.getAllItems()));
    }

    private void setMinPrice(DiscountContext context, ResponseDiscount discount) {
        Map<String, RequestDishInfo> dishInfoMap = discount.getRequestDishInfoList().stream()
                .collect(Collectors.toMap(RequestDishInfo::getOrderItemGuid, Function.identity(), (v1, v2) -> v1));
        context.getAllItems().forEach(dineInItemDTO -> {
            RequestDishInfo dishInfo = dishInfoMap.get(dineInItemDTO.getGuid());
            if (!ObjectUtils.isEmpty(dishInfo)) {
                dineInItemDTO.setMinPriceType(MinPriceTypeEnum.MEMBER_DISCOUNT.getCode());
                dineInItemDTO.setMinPrice(BigDecimalUtil.setScale2(dineInItemDTO.getOriginalPrice()
                        .multiply(dineInItemDTO.getCurrentCount())
                        .subtract(BigDecimalUtil.nonNullValue(dishInfo.getDiscountMoney()))));
            }
        });
    }

    /**
     * 构建查询会员折扣请求
     */
    private RequestDiscount buildDiscountReqDTO(DiscountContext context,
                                                BigDecimal orderSurplusFee,
                                                List<DineInItemDTO> allItems) {
        RequestDiscount discountReqDTO = new RequestDiscount();
        discountReqDTO.setMemberDiscount(context.getDiscountRuleBO().getMemberDiscount());
        discountReqDTO.setVolumeCodeList(context.getCalculateOrderDTO().getVolumeCodes());
        discountReqDTO.setMemberInfoCardGuid(context.getUserMemberSession().getMemberInfoCardGuid());
        BigDecimal payMoney = orderSurplusFee.subtract(context.getCalculateOrderRespDTO().getAppendFee());
        // 此时订单应支付的金额
        discountReqDTO.setPayMoney(BigDecimalUtil.greaterThanZero(payMoney) ? payMoney : BigDecimal.ZERO);
        discountReqDTO.setTotalMoney(context.getCalculateOrderRespDTO().getOrderFee());
        discountReqDTO.setStoreGuid(UserContextUtils.getStoreGuid());
        discountReqDTO.setRequestDishInfoList(DineInItemHelper.dineInItem2DishList(allItems));
        return discountReqDTO;
    }

    /**
     * 会员折扣
     * 更新每个商品上的优惠金额
     */
    private void memberDiscount(Map<String, DineInItemDTO> dineInItemMap, DiscountFeeDetailDTO member,
                                ResponseDiscount discount) {
        for (RequestDishInfo dishInfoDTO : discount.getRequestDishInfoList()) {
            if (dishInfoDTO.getDiscountMoney() != null) {
                DineInItemDTO dineInItemDTO = dineInItemMap.get(dishInfoDTO.getOrderItemGuid());
                if (dineInItemDTO != null && dishInfoDTO.getDiscountMoney() != null) {
                    dineInItemDTO.setTotalDiscountFee(dineInItemDTO.getTotalDiscountFee().add(dishInfoDTO.getDiscountMoney()));
                }
            }
        }
        member.setDiscountFee(BigDecimalUtil.setScale2(discount.getDeductionMoney()));
    }

    @Override
    Integer type() {
        return DiscountTypeEnum.MEMBER.getCode();
    }
}
