package com.holderzone.holder.saas.aggregation.merchant.service.rpc.mdm;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.mdm.MDMResult;
import com.holderzone.saas.store.dto.user.UserDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(value = "holder-saas-store-mdm", fallbackFactory = MdmServiceClient.MdmServiceClientFallBack.class)
public interface MdmServiceClient {

    /**
     * 查询员工
     */
    @GetMapping("/sync_user/find")
    MDMResult<UserDTO> findUser(@RequestParam("userGuid") String userGuid);

    /**
     * 同步员工
     */
    @PostMapping("/sync_user/create")
    MDMResult<Void> createUser(@RequestBody List<UserDTO> userDTOList);

    @Slf4j
    @Component
    class MdmServiceClientFallBack implements FallbackFactory<MdmServiceClient> {

        @Override
        public MdmServiceClient create(Throwable throwable) {
            return new MdmServiceClient() {
                @Override
                public MDMResult<UserDTO> findUser(String userGuid) {
                    log.error("查询mdm员工信息失败:{}", userGuid, throwable);
                    throw new BusinessException("查询mdm员工信息失败");
                }

                @Override
                public MDMResult<Void> createUser(List<UserDTO> userDTOList) {
                    log.error("同步员工信息失败:{}", JacksonUtils.writeValueAsString(JacksonUtils.writeValueAsString(userDTOList)),
                            throwable);
                    throw new BusinessException("同步员工信息失败");
                }

            };
        }
    }
}
