package com.holderzone.holder.saas.aggregation.app.entity.auth;

import com.holderzone.holder.saas.aggregation.app.anno.DataAuthFieldControl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * 商品销售统计
 */
@Data
@ApiModel
public class GoodsSaleDTO {

    /**
     * 商品名称
     */
    @DataAuthFieldControl("goods_sale_item_name")
    private String name;

    @DataAuthFieldControl("goods_sale_item_name")
    private String skuName;

    /**
     * 堂食单价
     */
    @DataAuthFieldControl("goods_sale_trade_unit_price")
    private String unitPrice;

    /**
     * 堂食销售数量
     */
    @DataAuthFieldControl("goods_sale_trade_count")
    private String dinnerQuantum;

    /**
     * 堂食销售金额
     */
    @DataAuthFieldControl("goods_sale_trade_sale_amount")
    private String dinnerAmount;

    /**
     * 堂食实付金额
     */
    @DataAuthFieldControl("goods_sale_trade_actually_amount")
    private String dinnerDiscountAmount;

    /**
     * 外卖单价
     */
    @DataAuthFieldControl("goods_sale_takeaway_unit_price")
    private String takeoutUnitPrice;

    /**
     * 外卖销售数量
     */
    @DataAuthFieldControl("goods_sale_takeaway_count")
    private String takeoutQuantum;

    /**
     * 外卖销售金额
     */
    @DataAuthFieldControl("goods_sale_takeaway_sale_amount")
    private String takeoutAmount;

    /**
     * 销售数量合计
     */
    @DataAuthFieldControl("goods_sale_count_total")
    private String quantum;

    /**
     * 销售金额合计
     */
    @DataAuthFieldControl("goods_sale_sale_amount_total")
    private String amount;

    /**
     * 实付金额合计
     */
    @DataAuthFieldControl("goods_sale_actually_amount_total")
    private String discountAmount;

    @ApiModelProperty(value = "唯一标识")
    private String guid;

    @ApiModelProperty(value = "商品类型")
    private Integer itemType;

    @ApiModelProperty(value = "是否合计项,0否 1是")
    private int isTotal = 0;

    @ApiModelProperty(value = "是否含有子项,0否 1是")
    private int hasSubInfo = 0;

    @ApiModelProperty(value = "子项")
    @DataAuthFieldControl
    private List<GoodsSaleDTO> subs;

}