package com.holderzone.holder.saas.aggregation.weixin.utils.map;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface UserMemberSessionMAP {

    UserMemberSessionMAP INSTANCE = Mappers.getMapper(UserMemberSessionMAP.class);

//	UserMemberSessionCardItemDTO toSessionCard(MemberCardListOwnedRespDTO memberCardListOwnedRespDTO);

//	List<UserMemberSessionCardItemDTO> toSessionCardList(List<MemberCardListOwnedRespDTO> memberCardListOwnedRespDTOS);
}
