package com.holderzone.holder.saas.aggregation.phoneapp.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.journaling.req.BusinessSituationReqDTO;
import com.holderzone.saas.store.dto.journaling.req.JournalAppBaseReqDTO;
import com.holderzone.saas.store.dto.journaling.req.RecoveryOrderReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.DiscountSituationDTO;
import com.holderzone.saas.store.dto.journaling.resp.RecoveryOrderRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.TotalPaymentRespDTO;
import com.holderzone.saas.store.dto.journaling.req.SaleCountReqDTO;
import com.holderzone.saas.store.dto.journaling.req.StoreStatisticsAppReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className JournalingRpcService
 * @date 2019/06/03 18:46
 * @description 报表服务FeignService
 * @program holder-saas-store
 */
@FeignClient(name = "holder-saas-store-report", fallbackFactory = JournalRpcService.FallBackClass.class)
@Service
public interface JournalRpcService {

    @PostMapping("/recovery/list")
    List<RecoveryOrderRespDTO> listRecoveryOrder(@RequestBody RecoveryOrderReqDTO recoveryOrderReqDTO);

    @PostMapping("/sale_count/sale")
    @ApiOperation("销售报表")
    SaleRespDTO saleCount(SaleCountReqDTO saleCountReqDTO);

    @PostMapping("/busSituation/businessData")
    BusinessDataRespDTO businessData(@RequestBody BusinessSituationReqDTO businessSituationReqDTO);

    @PostMapping("/busSituation/busiHisTrend")
    BusinessHisTrendRespDTO hisTrend(@RequestBody BusinessSituationReqDTO businessSituationReqDTO);

    @PostMapping("/busSituation/todayBusinessSituation")
    TodayBusinessSituationRespDTO todayBusinessSituation(@RequestBody BusinessSituationReqDTO businessSituationReqDTO);

    @PostMapping("/appStoreStatistics/list")
    StoreStatisticsAppRespDTO appStoreStatistics(@RequestBody StoreStatisticsAppReqDTO storeStatisticsAppReqDTO);

    @PostMapping("/discount/list")
    DiscountSituationDTO listDiscountSituation(@RequestBody BusinessSituationReqDTO businessSituationReqDTO);

    @PostMapping("/payment/totalPayment")
    TotalPaymentRespDTO totalPayment(@RequestBody JournalAppBaseReqDTO journalAppBaseReqDTO);

    @Component
    @Slf4j
    class FallBackClass implements FallbackFactory<JournalRpcService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public JournalRpcService create(Throwable throwable) {
            return new JournalRpcService() {
                @Override
                public List<RecoveryOrderRespDTO> listRecoveryOrder(RecoveryOrderReqDTO recoveryOrderReqDTO) {
                    log.error(HYSTRIX_PATTERN, "listRecoveryOrder", JacksonUtils.writeValueAsString(recoveryOrderReqDTO), throwable.getCause());
                    throw new BusinessException("反结账记录统计查询异常");
                }

                @Override
                public DiscountSituationDTO listDiscountSituation(BusinessSituationReqDTO businessSituationReqDTO) {
                    log.error(HYSTRIX_PATTERN, "listDiscountSituation", JacksonUtils.writeValueAsString(businessSituationReqDTO), throwable.getCause());
                    throw new BusinessException("优惠信息统计查询异常");
                }

                @Override
                public TotalPaymentRespDTO totalPayment(JournalAppBaseReqDTO journalAppBaseReqDTO) {
                    log.error(HYSTRIX_PATTERN, "totalPayment", JacksonUtils.writeValueAsString(journalAppBaseReqDTO), throwable.getCause());
                    throw new BusinessException("支付记录统计查询异常");
                }

                @Override
                public SaleRespDTO saleCount(SaleCountReqDTO saleCountReqDTO) {
                    log.error(HYSTRIX_PATTERN, "saleCount", JacksonUtils.writeValueAsString(saleCountReqDTO), throwable.getCause());
                    throw new BusinessException("查询报表，销售统计报表信息异常");
                }

                @Override
                public BusinessDataRespDTO businessData(BusinessSituationReqDTO businessSituationReqDTO) {
                    log.error(HYSTRIX_PATTERN, "listRecoveryOrder", JacksonUtils.writeValueAsString(businessSituationReqDTO), throwable.getCause());
                    throw new BusinessException("报表服务-营业数据调用异常");
                }

                @Override
                public BusinessHisTrendRespDTO hisTrend(BusinessSituationReqDTO businessSituationReqDTO) {
                    log.error(HYSTRIX_PATTERN, "listRecoveryOrder", JacksonUtils.writeValueAsString(businessSituationReqDTO), throwable.getCause());
                    throw new BusinessException("报表服务-历史趋势调用异常");
                }

                @Override
                public TodayBusinessSituationRespDTO todayBusinessSituation(BusinessSituationReqDTO businessSituationReqDTO) {
                    log.error(HYSTRIX_PATTERN, "listRecoveryOrder", JacksonUtils.writeValueAsString(businessSituationReqDTO), throwable.getCause());
                    throw new BusinessException("报表服务-今日概况调用异常");
                }

                @Override
                public StoreStatisticsAppRespDTO appStoreStatistics(StoreStatisticsAppReqDTO storeStatisticsAppReqDTO) {
                    log.error(HYSTRIX_PATTERN, "appStoreStatistics", JacksonUtils.writeValueAsString(storeStatisticsAppReqDTO), throwable.getCause());
                    throw new BusinessException("app报表服务-门店统计调用异常");
                }
            };
        }
    }
}
