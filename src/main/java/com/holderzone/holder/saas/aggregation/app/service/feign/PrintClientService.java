package com.holderzone.holder.saas.aggregation.app.service.feign;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.print.*;
import com.holderzone.saas.store.dto.print.cloud.CloudPrinterDTO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.deprecate.PrintContentDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.raw.PrinterRawAggDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(name = "holder-saas-store-print", fallbackFactory = PrintClientService.FallbackFactoryImpl.class)
public interface PrintClientService {

    @PostMapping("/printer/backups_printer")
    boolean backupsPrinter(@RequestBody SingleDataDTO singleDataDTO);

    @PostMapping("/printer/restore_printer")
    boolean restorePrinter(@RequestBody SingleDataDTO singleDataDTO);

    @PostMapping("/printer/backups_printer_time")
    String backupsPrinterTime(@RequestBody SingleDataDTO singleDataDTO);

    @PostMapping("/printer/add")
    String addPrinter(@RequestBody PrinterDTO printerDTO);

    @PostMapping("/printer/add_cloud")
    @ApiOperation(value = "添加云打印机")
    void addCloud(@RequestBody CloudPrinterDTO cloudPrinterDTO);

    @PostMapping("/printer/delete")
    void deletePrinter(@RequestBody PrinterDTO printerDTO);

    @PostMapping("/printer/update")
    void updatePrinter(@RequestBody PrinterDTO printerDTO);

    @PostMapping("/printer/batch_update")
    @ApiOperation(value = "批量修改打印机信息/与菜品的绑定/与区域绑定")
    void batchUpdatePrinter(@RequestBody PrinterDTO printerDTO);

    @PostMapping("/printer/update_cloud")
    @ApiOperation(value = "修改云打印机信息")
    void updateCloudPrinter(@RequestBody CloudPrinterDTO cloudPrinterDTO);

    @PostMapping("/printer/query")
    PrinterDTO getPrinter(@RequestBody PrinterDTO printerDTO);

    @PostMapping("/printer/list")
    @ApiOperation(value = "批量获取打印机信息, 包括与菜品/区域绑定关系")
    List<PrinterDTO> listPrinter(@RequestBody SingleDataDTO request);

    @PostMapping("/printer/list_by_biz")
    List<PrinterDTO> listPrintersByBiz(@RequestBody PrinterDTO printerDTO);

    @PostMapping("/printer/list_cloud_printers")
    @ApiOperation(value = "通过门店guid获取该门店的云打印机信息列表(不含菜品/区域绑定关系)")
    List<PrinterDTO> listCloudPrinters(@RequestBody PrinterDTO printerDTO);

    /**
     * 根据打印Id和商品Id查询商品信息列表
     */
    @PostMapping("/printer/listPrinterByPrinterIdAndItemId")
    List<String> listPrinterByPrinterIdAndItemId(@RequestBody PrinterDTO printerDTO);

    /**
     * 根据条件查询打印机
     */
    @PostMapping("/printer/query_by_condition")
    List<PrinterDTO> queryByCondition(@RequestBody PrinterQueryDTO queryDTO);

    @PostMapping("/printer/list_by_device")
    List<PrinterDTO> listPrinterByDevice(@RequestBody PrinterDTO printerDTO);

    @PostMapping("/print_record/get_content")
    List<PrintContentDTO> getRecord(@RequestBody PrintRecordReqDTO printRecordReqDTO);

    @PostMapping("/print_record/get_order")
    List<PrintOrderDTO> getOrder(@RequestBody PrintRecordReqDTO printRecordReqDTO);

    @PostMapping("/print_record/get_test_order")
    PrintOrderDTO getTestPrintOrder(@RequestBody String formatDTO);

    @PostMapping("/print_record/get_test_orders")
    List<PrintOrderDTO> getTestPrintOrders(@RequestBody String formatDTO);

    @PostMapping("/print_record/update_status")
    void updateStatus(@RequestBody PrintRecordReqDTO printRecordReqDTO);

    @PostMapping("/print_record/list")
    List<PrintRecordDTO> listRecord(@RequestBody PrintRecordReqDTO printRecordReqDTO);

    @PostMapping("/print_record/delete")
    void deleteRecord(@RequestBody PrintRecordReqDTO printRecordReqDTO);

    @PostMapping("/print_record/batch_delete")
    void batchDeleteRecord(@RequestBody PrintRecordReqDTO printRecordReqDTO);

    @PostMapping("/print_record/send")
    String printTask(@RequestBody @Validated PrintDTO printDto);

    @PostMapping("/format/add")
    void addFormat(@RequestBody String formatDTO);

    @PostMapping("/format/list")
    String listFormat(@RequestBody FormatDTO formatDTO);

    @PostMapping("/format/delete")
    void deleteFormat(@RequestBody FormatDTO formatDTO);

    @PostMapping("/format/enable")
    void enableFormat(@RequestBody FormatDTO formatDTO);

    @PostMapping("/format/reset")
    String resetFormat(@RequestBody FormatDTO formatDTO);

    @PostMapping("/format/urls")
    List<String> getInvoiceUrls(@RequestBody FormatDTO formatDTO);

    @PostMapping("/print_table/sync")
    PrinterRawAggDTO printTableSync(@RequestBody BaseDTO baseDTO);

    @PostMapping("/printer/test_print")
    @ApiOperation(value = "测试打印")
    void testPrint(@RequestBody CloudPrinterDTO cloudPrinterDTO);

    @ApiOperation(value = "校验飞蛾打印机")
    @PostMapping("/printer/check_cloud")
    void checkCloudPrinter(@RequestBody CloudPrinterDTO cloudPrinterDTO);

    @PostMapping("/printer/auto_print/{status}")
    void autoPrintSet(@PathVariable("status") Integer status, @RequestBody PrinterDTO printerDTO);

    @PostMapping("/printer/auto_print/query")
    Integer autoPrintQuery(@RequestBody PrinterDTO printerDTO);

    @Slf4j
    @Component
    class FallbackFactoryImpl implements FallbackFactory<PrintClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public PrintClientService create(Throwable throwable) {

            return new PrintClientService() {
                @Override
                public List<PrinterDTO> queryByCondition(PrinterQueryDTO queryDTO) {
                    log.error("queryByCondition，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public List<String> listPrinterByPrinterIdAndItemId(PrinterDTO printerDTO) {
                    log.error("listPrinterByPrinterIdAndItemId，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public List<PrinterDTO> listPrinterByDevice(PrinterDTO printerDTO) {
                    log.error("listPrinterByStoreGuid，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public boolean backupsPrinter(SingleDataDTO singleDataDTO) {
                    log.error("backupsPrinter，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public boolean restorePrinter(SingleDataDTO singleDataDTO) {
                    log.error("restorePrinter，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public String backupsPrinterTime(SingleDataDTO singleDataDTO) {
                    log.error("backupsPrinterTime，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public String addPrinter(PrinterDTO printerDTO) {
                    log.error("addPrinter失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public void addCloud(CloudPrinterDTO cloudPrinterDTO) {
                    log.error(HYSTRIX_PATTERN, "addCloud", JacksonUtils.writeValueAsString(cloudPrinterDTO),
                            throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public void deletePrinter(PrinterDTO printerDTO) {
                    log.error("deletePrinterByGuid失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public void updatePrinter(PrinterDTO printerDTO) {
                    log.error("updatePrinter失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public void batchUpdatePrinter(PrinterDTO printerDTO) {
                    log.error(HYSTRIX_PATTERN, "batchUpdatePrinter", JacksonUtils.writeValueAsString(printerDTO),
                            throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public void updateCloudPrinter(CloudPrinterDTO cloudPrinterDTO) {
                    log.error(HYSTRIX_PATTERN, "updateCloudPrinter", JacksonUtils.writeValueAsString(cloudPrinterDTO),
                            throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public PrinterDTO getPrinter(PrinterDTO printerDTO) {
                    log.error("getPrinter失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public List<PrinterDTO> listPrinter(SingleDataDTO request) {
                    log.error(HYSTRIX_PATTERN, "listPrinter", JacksonUtils.writeValueAsString(request),
                            throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public List<PrinterDTO> listPrintersByBiz(PrinterDTO printerDTO) {
                    log.error("listPrintersByQuery失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public List<PrinterDTO> listCloudPrinters(PrinterDTO printerDTO) {
                    log.error(HYSTRIX_PATTERN, "listCloudPrinters", JacksonUtils.writeValueAsString(printerDTO),
                            throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public List<PrintContentDTO> getRecord(PrintRecordReqDTO printRecordReqDTO) {
                    log.error("getContent失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }


                @Override
                public List<PrintOrderDTO> getOrder(PrintRecordReqDTO printRecordReqDTO) {
                    log.error("getOrder失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public PrintOrderDTO getTestPrintOrder(String formatDTO) {
                    log.error("getTestPrintOrder失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public List<PrintOrderDTO> getTestPrintOrders(String formatDTO) {
                    log.error("getTestPrintOrders失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public void updateStatus(PrintRecordReqDTO printRecordReqDTO) {
                    log.error("handlerResult失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public List<PrintRecordDTO> listRecord(PrintRecordReqDTO printRecordReqDTO) {
                    log.error("getRecordList失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public void deleteRecord(PrintRecordReqDTO printRecordReqDTO) {
                    log.error("deleteRecord，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public void batchDeleteRecord(PrintRecordReqDTO printRecordReqDTO) {
                    log.error("deleteRecordList，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public String printTask(PrintDTO printDto) {
                    log.error("deleteRecordList，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public void addFormat(String formatDTO) {
                    log.error("createFormat，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public String listFormat(FormatDTO formatDTO) {
                    log.error("queryFormat，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public void deleteFormat(FormatDTO formatDTO) {
                    log.error("deleteFormat，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public void enableFormat(FormatDTO formatDTO) {
                    log.error("enableFormat，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public String resetFormat(FormatDTO formatDTO) {
                    log.error("resetFormat，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public List<String> getInvoiceUrls(FormatDTO formatDTO) {
                    log.error("getInvoiceUrls，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public PrinterRawAggDTO printTableSync(BaseDTO baseDTO) {
                    log.error("printTableSync，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public void testPrint(CloudPrinterDTO cloudPrinterDTO) {
                    log.error(HYSTRIX_PATTERN, "testPrint", JacksonUtils.writeValueAsString(cloudPrinterDTO),
                            throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public void checkCloudPrinter(CloudPrinterDTO cloudPrinterDTO) {
                    log.error(HYSTRIX_PATTERN, "checkCloudPrinter", JacksonUtils.writeValueAsString(cloudPrinterDTO),
                            throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public void autoPrintSet(Integer status, PrinterDTO printerDTO) {
                    log.error(HYSTRIX_PATTERN, "autoPrintSet", JacksonUtils.writeValueAsString(printerDTO),
                            throwable.getMessage());
                    throw new BusinessException("自动打印设置失败!!" + throwable.getMessage());
                }

                @Override
                public Integer autoPrintQuery(PrinterDTO printerDTO) {
                    log.error(HYSTRIX_PATTERN, "autoPrintQuery", JacksonUtils.writeValueAsString(printerDTO),
                            throwable.getMessage());
                    return 1;
                }
            };
        }
    }
}
