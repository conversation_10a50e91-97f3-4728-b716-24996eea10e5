package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.weixin.WxPositionDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreListDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreCityListRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreListCilentService
 * @date 2019/05/17 19:54
 * @description 微信门店功能及信息ClientServic
 * @program holder-saas-store
 */
@Service
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = WxStoreListClientService.FallBackClass.class)
public interface WxStoreListClientService {

    @PostMapping("/wx_store/list_store_config")
    WxStoreListDTO listStoreConfig(WxPositionDTO wxPositionDTO);


    @PostMapping("/wx_store/list_store_city")
    WxStoreCityListRespDTO listStoreCity(WxPositionDTO wxPositionDTO);

    /**
     * 查询门店配置
     *
     * @param storeGuid 门店guid
     * @return 门店配置
     */
    @ApiOperation("查询门店配置")
    @GetMapping("/wx-store-menu-provide/store_config")
    WxOrderConfigDTO getStoreConfig(@RequestParam("storeGuid") String storeGuid);

    @Component
    @Slf4j
    class FallBackClass implements FallbackFactory<WxStoreListClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public WxStoreListClientService create(Throwable throwable) {
            return new WxStoreListClientService() {
                @Override
                public WxStoreListDTO listStoreConfig(WxPositionDTO wxPositionDTO) {
                    return null;
                }

                @Override
                public WxStoreCityListRespDTO listStoreCity(WxPositionDTO wxPositionDTO) {
                    return null;
                }

                @Override
                public WxOrderConfigDTO getStoreConfig(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "getStoreConfig", storeGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
