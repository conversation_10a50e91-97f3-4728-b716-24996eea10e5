package com.holderzone.holder.saas.aggregation.merchant.service.rpc.cloud;


import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.enterprise.MultiMemberQueryDTO;
import com.holderzone.resource.common.dto.enterprise.OrganizationDTO;
import com.holderzone.resource.common.dto.extension.BaseDictionaryDTO;
import com.holderzone.resource.common.dto.holder.organization.HolderOrganizationResultDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description 服务间调用-云端商户服务
 * @program holder-saas-aggregation-merchant
 * @date 2021/3/11 11:20
 */
@Component
@FeignClient(value = "holder-saas-cloud-enterprise", fallbackFactory = CloudEnterpriseFeignClient.ServiceFallBack.class)
public interface CloudEnterpriseFeignClient {


    /**
     * 根据企业id查询企业信息
     */
    @GetMapping("/enterprise/find/{enterpriseGuid}")
    EnterpriseDTO findEnterprise(@PathVariable(value = "enterpriseGuid") String enterpriseGuid);

    /**
     * 根据门店GUID、员工GUID查询营业日起始时间
     *
     * @return
     */
    @GetMapping("/enterprise/hasEnterprise")
    Boolean hasEnterprise(@RequestParam("enterpriseGuid") String enterpriseGuid);

    /**
     * 根据企业guid查询企业经营模式
     *
     * @param enterpriseGuid 企业guid
     * @return SINGLE, 单店 CHAIN,连锁 PLATFORM平台
     */
    @GetMapping("/enterprise/management_model/{enterpriseGuid}")
    String queryManagementModel(@PathVariable(value = "enterpriseGuid") String enterpriseGuid);

    /**
     * 查询运营主体下的门店列表
     *
     * @param queryDTO 关联企业guid，运营主体guid
     * @return 门店列表
     */
    @ApiOperation(value = "查询运营主体下的门店列表", notes = "必传 关联企业guid，运营主体guid")
    @PostMapping("/organization/store_list_multiMemberGuid")
    List<OrganizationDTO> getStoreByMultiMemberGuid(@RequestBody MultiMemberQueryDTO queryDTO);

    /**
     * 根据企业guid查询企业经营类型
     *
     * @param enterpriseGuid 企业guid
     * @return 数据字典实体
     */
    @ApiOperation("根据企业guid查询企业经营类型")
    @GetMapping("/enterprise/management_type/{enterpriseGuid}")
    BaseDictionaryDTO queryManagementType(@PathVariable(value = "enterpriseGuid") String enterpriseGuid);

    /**
     * 查询该企业绑定的holder组织机构(返回树形结构)
     */
    @GetMapping("/organization/holder/list/{enterpriseGuid}")
    HolderOrganizationResultDTO listTreeOrganizationByHolder(@PathVariable(value = "enterpriseGuid") String enterpriseGuid);

    /**
     * 查询该企业绑定的holder组织机构(返回列表结构，不分页)
     */
    @GetMapping("/organization/holder/list/all/{enterpriseGuid}")
    List<HolderOrganizationResultDTO> listAllOrganizationByHolder(@PathVariable(value = "enterpriseGuid") String enterpriseGuid);

    /**
     * 同步holder组织机构名称
     */
    @PutMapping("/organization/holder/sync/{enterpriseGuid}")
    void syncHolderOrganization(@PathVariable("enterpriseGuid") String enterpriseGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<CloudEnterpriseFeignClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}";

        @Override
        public CloudEnterpriseFeignClient create(Throwable throwable) {
            return new CloudEnterpriseFeignClient() {

                @Override
                public EnterpriseDTO findEnterprise(String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN, "findEnterprise", enterpriseGuid, ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询企业信息失败");
                }

                /**
                 * 根据门店GUID、员工GUID查询营业日起始时间
                 *
                 * @param enterpriseGuid
                 * @return
                 */
                @Override
                public Boolean hasEnterprise(String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN, "/enterprise/hasEnterprise", enterpriseGuid, throwable);
                    return Boolean.TRUE;
                }

                @Override
                public String queryManagementModel(String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN, "/management_model/{enterpriseGuid}", enterpriseGuid, throwable);
                    return null;
                }

                @Override
                public List<OrganizationDTO> getStoreByMultiMemberGuid(MultiMemberQueryDTO queryDTO) {
                    log.error(HYSTRIX_PATTERN, "getStoreByMultiMemberGuid", JacksonUtils.writeValueAsString(queryDTO),
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询运营主体下的门店列表 熔断");
                }

                @Override
                public BaseDictionaryDTO queryManagementType(String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN, "/management_type/{enterpriseGuid}", enterpriseGuid, throwable);
                    return null;
                }

                @Override
                public HolderOrganizationResultDTO listTreeOrganizationByHolder(String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN, "listTreeOrganizationByHolder", enterpriseGuid, throwable);
                    return null;
                }

                @Override
                public List<HolderOrganizationResultDTO> listAllOrganizationByHolder(String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN, "listAllOrganizationByHolder", enterpriseGuid, throwable);
                    return null;
                }

                @Override
                public void syncHolderOrganization(String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN, "syncHolderOrganization", enterpriseGuid, throwable);
                }
            };
        }
    }
}