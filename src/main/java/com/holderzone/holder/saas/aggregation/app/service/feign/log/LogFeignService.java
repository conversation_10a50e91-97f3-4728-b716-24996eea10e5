package com.holderzone.holder.saas.aggregation.app.service.feign.log;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.BaseFeignService;
import com.holderzone.saas.store.dto.log.request.client.LogClientErrorReportDTO;
import com.holderzone.saas.store.dto.log.request.client.LogClientOperatReportDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * @Athor forewei
 * @Email <EMAIL>
 * @Date 2019/12/9
 */
@Component
@FeignClient(value = "holder-saas-cloud-log", fallbackFactory = LogFeignService.ServiceFallBack.class)
public interface LogFeignService {

    @RequestMapping(method = RequestMethod.POST, value = "log/client/report/error")
    void clientReportErrorLog(@Validated @RequestBody LogClientErrorReportDTO logClientErrorReportDTO);


    @RequestMapping(method = RequestMethod.POST, value = "log/client/report/operat")
    void clientReportOperatLog(@Validated @RequestBody LogClientOperatReportDTO logClientOperatReportDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<LogFeignService> {


        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public LogFeignService create(Throwable cause) {
            return new LogFeignService() {
                @Override
                public void clientReportErrorLog(LogClientErrorReportDTO logClientErrorReportDTO) {
                    log.error(HYSTRIX_PATTERN, "clientReportErrorLog", "logClientErrorReportDTO=" + JacksonUtils.writeValueAsString(logClientErrorReportDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void clientReportOperatLog(LogClientOperatReportDTO logClientOperatReportDTO) {
                    log.error(HYSTRIX_PATTERN, "clientReportOperatLog", "logClientOperatReportDTO=" + JacksonUtils.writeValueAsString(logClientOperatReportDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }
    }
}
