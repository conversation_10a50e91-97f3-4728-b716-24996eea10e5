package com.holderzone.holder.saas.aggregation.merchant.service.rpc.media;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.media.dto.entity.*;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableServiceClient
 * @date 2019/01/07 10:22
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(value = "holder-saas-store-media", fallbackFactory = MediaServiceClient.MediaServiceClientFallBack.class)
public interface MediaServiceClient {

    @PostMapping("/file/batch/create_file")
    List<FileCreateRespDTO> batchCreateFile(@RequestBody List<MediaDTO> hsmMediaList);

    @PostMapping("/file/create_file")
    FileCreateRespDTO createFile(@RequestBody MediaDTO mediaDTO);


    @PostMapping("/file/delete")
    void delete(@RequestBody List<FileDeleteDTO> fileDeleteDTOList);


    @PostMapping("/file/queryMediaByName")
    Page<MediaDTO> queryMediaByName(@RequestBody PageMedia pageMedia);


    @PostMapping("/file/queryMediaList")
    Page<MediaDTO> queryMediaList(@RequestBody PageMedia pageMedia);


    @PostMapping("/file/move")
    Boolean moveFile(@RequestBody FileMoveDTO fileMoveDTO);


    @PostMapping("/file/rename")
    Boolean renameFile(@RequestBody FileRenameDTO fileRenameDTO);

    @Slf4j
    @Component
    class MediaServiceClientFallBack implements FallbackFactory<MediaServiceClient> {
        @Override
        public MediaServiceClient create(Throwable throwable) {
            return new MediaServiceClient() {

                @Override
                public List<FileCreateRespDTO> batchCreateFile(List<MediaDTO> hsmMediaList) {
                    log.error("批量创建文件异常 e={} hsmMediaList={}", throwable.getMessage(), JacksonUtils.writeValueAsString(hsmMediaList));
                    throw new BusinessException("批量创建文件发生异常");
                }

                @Override
                public FileCreateRespDTO createFile(MediaDTO mediaDTO) {
                    log.error("创建文件异常 e={} mediaDTO={}", throwable.getMessage(), JacksonUtils.writeValueAsString(mediaDTO));
                    throw new BusinessException("创建文件发生异常");
                }

                @Override
                public void delete(List<FileDeleteDTO> fileDeleteDTOList) {
                    log.error("删除文件异常 e={} fileDeleteDTO={}", throwable.getMessage(), JacksonUtils.writeValueAsString(fileDeleteDTOList));
                    throw new BusinessException("删除文件发生异常");
                }

                @Override
                public Page<MediaDTO> queryMediaByName(PageMedia pageMedia) {
                    log.error("查询文件异常 e={} pageMedia={}", throwable.getMessage(), JacksonUtils.writeValueAsString(pageMedia));
                    throw new BusinessException("查询文件发生异常");
                }

                @Override
                public Page<MediaDTO> queryMediaList(PageMedia pageMedia) {
                    log.error("加载文件异常 e={} pageMedia={}", throwable.getMessage(), JacksonUtils.writeValueAsString(pageMedia));
                    throw new BusinessException("加载文件发生异常");
                }

                @Override
                public Boolean moveFile(FileMoveDTO fileMoveDTO) {
                    log.error("移动文件异常 e={} fileMoveDTO={}", throwable.getMessage(), JacksonUtils.writeValueAsString(fileMoveDTO));
                    throw new BusinessException("移动文件发生异常");
                }

                @Override
                public Boolean renameFile(FileRenameDTO fileRenameDTO) {
                    log.error("重命名文件异常 e={} fileRenameDTO={}", throwable.getMessage(), JacksonUtils.writeValueAsString(fileRenameDTO));
                    throw new BusinessException("重命名文件发生异常");
                }
            };
        }
    }
}
