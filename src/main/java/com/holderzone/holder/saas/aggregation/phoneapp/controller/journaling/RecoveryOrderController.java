package com.holderzone.holder.saas.aggregation.phoneapp.controller.journaling;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.phoneapp.service.rpc.JournalRpcService;
import com.holderzone.saas.store.dto.journaling.req.RecoveryOrderReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.RecoveryOrderRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RecoveryOrderController
 * @date 2019/06/03 11:30
 * @description 反结账订单统计Controller
 * @program holder-saas-store
 */
@Api(description = "反结账订单统计Controller")
@RequestMapping("/recovery")
@RestController
@Slf4j
public class RecoveryOrderController {

    @Autowired
    JournalRpcService journalRpcService;

    @PostMapping("/list")
    @ApiOperation(value = "查询反结账订单统计列表")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT,description = "查询反结账订单统计列表")
    public Result<List<RecoveryOrderRespDTO>> listRecoveryOrder(@RequestBody RecoveryOrderReqDTO recoveryOrderReqDTO) {
        log.info("反结账订单统计列表查入参：{}", JacksonUtils.writeValueAsString(recoveryOrderReqDTO));
        return Result.buildSuccessResult(journalRpcService.listRecoveryOrder(recoveryOrderReqDTO));
    }
}
