package com.holderzone.holder.saas.aggregation.merchant.entity.vo;

import com.holderzone.resource.common.dto.enterprise.EnterpriseOpenApiConfigDTO;
import com.holderzone.resource.common.dto.enterprise.EnterprisePayInfoDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseSupplyChainConfigDTO;
import com.holderzone.resource.common.dto.enterprise.OrganizationDTO;
import de.pentabyte.springfox.ApiEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>  commrvice有一个同名dto 无法引入正确的dto版本 故 引入此版本
 * @date 2018/7/10 15:59
 */
@Data
public class EnterpriseDTO implements Serializable {

    private static final long serialVersionUID = -3928504779306522229L;
    /**
     * 主键
     */
    private Long id;

    /**
     * 非0开头，8位纯数字作为商户编号，唯一
     */
    private String uid;

    /**
     * 企业的guid
     */
    private String enterpriseGuid;

    /**
     * 名字
     */
    private String name;

    /**
     * 名字
     */
    private Integer dbServer;

    /**
     * 注册电话
     */
    private String regTel;

    /**
     * 注册邮箱
     */
    private String regEmail;

    /**
     * 注册时间
     */
    private long regTime;

    /**
     * 注册方式
     */
    private String regType;

    /**
     * 有效起始时间
     */
    private long validStartTime;

    /**
     * 有效截止时间
     */
    private long validEndTime;

    /**
     * 地址
     */
    private String address;

    /**
     * 是否删除
     */
    private Integer deleted;

    /**
     * 是否可用，默认可用
     */
    private Integer enabled;

    /**
     * 法人名字
     */
    private String corporationName;

    /**
     * 法人身份证号码
     */
    private String corporationIDCard;

    /**
     * 法人身份证正面照片
     */
    private String corporationIDCardFaceImg;

    /**
     * 法人身份证反面照片
     */
    private String corporationIDCardBackImg;

    /**
     * 营业执照编号
     */
    private String businessLicence;

    /**
     * 营业执照照片
     */
    private String businessLicenceImg;

    /**
     * 企业名称（以营业执照为准）
     */
    private String enterpriseName;

//    /**
//     * 企业类型（以营业执照为准）
//     */
//    private String enterpriseType;

    /**
     * 企业地址（以营业执照为准）
     */
    private String enterpriseAddr;

    /**
     * 经营期限（以营业执照为准）
     */
    private long businessTimeStart;
    private long businessTimeEnd;

    /**
     * 状态
     */
    private int state;

    /**
     * 软件版本
     */
    private String dictionaryKey;
    private String productName;

    /**
     * 软件规格
     */
    private String dictionaryStandard;

    /**
     * 经营范围
     */
    private String businessScope;

    /**
     * 软件对照企业类型
     */
    private String type;
    private Integer category;
    private EnterprisePayInfoDTO enterprisePayInfo;
    private int storeCount;
    private int staffCount;
    private String linkGuid;
    private String commercialActivities;
    private List<OrganizationDTO> organizationList;
    private Integer orgCount;
    private int messageCount;

    /**
     * 联系人姓名
     */
    private String contactName;
    /**
     * 联系人性别
     */
    private Gender contactGender;
    /**
     * 联系人电话
     */
    private String contactPhone;
    /**
     * 联系地址编码
     */
    private String contactAddressCode;
    /**
     * 联系地址
     */
    private String contactAddress;
    /**
     * 经营模式
     */
    private ManagementModel managementModel;
    /**
     * 推广人(员工,合作商)
     */
    private String promoterRelateGuid;
    /**
     * 推广类型
     */
    private PromoterType promoterType;
    /**
     * 推广人姓名
     */
    private String promoterName;
    /**
     * 推广人电话
     */
    private String promoterTel;
    /**
     * 企业类型
     */
    private EnterpriseType enterpriseType;

    /**
     * 是否支持麓豆支付
     */
    private Boolean supportLudouPayFlag;

    /**
     * 是否支付威海供应链
     */
    private Boolean supportWeiHaiSupplyChain;

    /**
     * 供应链配置
     */
    private EnterpriseSupplyChainConfigDTO supplyChainConfig;

    /**
     * 是否支持开放接口
     */
    private Boolean supportOpenApi;

    /**
     * 开放接口配置
     */
    private EnterpriseOpenApiConfigDTO openApiConfig;

    public enum Gender {
        /**
         * 未知
         */
        UNKNOWN,
        /**
         * 男
         */
        MAN,
        /**
         * 女
         */
        WOMAN
    }

    public enum EnterpriseType {
        /**
         * 企业
         */
        @ApiEnum("企业")
        ENTERPRISE,
        /**
         * 个体户
         */
        @ApiEnum("个体户")
        SELF,
        /**
         * 小微商户
         */
        @ApiEnum("小微商户")
        SMALL_SHOP
    }

    public enum PromoterType {
        /**
         * 员工
         */
        @ApiEnum("员工")
        STAFF,
        /**
         * 合作商
         */
        @ApiEnum("合作商")
        PARTNER
    }

    public enum ManagementModel {
        /**
         * 单店
         */
        @ApiEnum("单店")
        SINGLE,
        /**
         * 连锁
         */
        @ApiEnum("连锁")
        CHAIN,
        /**
         * 平台
         */
        @ApiEnum("平台")
        PLATFORM
    }

}
