package com.holderzone.holder.saas.aggregation.merchant;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, HibernateJpaAutoConfiguration.class})
@EnableFeignClients(basePackages = "com.holderzone")
@EnableSwagger2
@EnableEurekaClient
@EnableApolloConfig
@EnableAsync
@ComponentScan(basePackages = "com.holderzone")
public class HolderSaasStoreAggregationMerchantApplication {

    public static void main(String[] args) {

        SpringApplication.run(HolderSaasStoreAggregationMerchantApplication.class, args);
    }
}
