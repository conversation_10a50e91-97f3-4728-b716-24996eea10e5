package com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.weixin.req.WxOrderConfigUpdateBatchReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxOrderConfigClientService
 * @date 2019/02/14 14:19
 * @description //TODO
 * @program ${MODULE_NAME}
 */
@Component
@FeignClient(value = "holder-saas-store-weixin", fallbackFactory = WxOrderConfigClientService.FallBackService.class)
public interface WxOrderConfigClientService {

    @PostMapping("/wx_store_order_config/list_order_config")
    Page<WxOrderConfigDTO> pageOrderConfig(WxStorePageReqDTO wxStorePageReqDTO);

    @PostMapping("/wx_store_order_config/get_detail_config")
    WxOrderConfigDTO getDetailConfig(WxStoreReqDTO wxStoreReqDTO);

    @PostMapping("/wx_store_order_config/update_store_config")
    Boolean updateStoreConfig(WxOrderConfigDTO wxOrderConfigDTO);

    @PostMapping("/wx_store_order_config/update_batch_store_config")
    Boolean updateStoreConfigBatch(WxOrderConfigUpdateBatchReqDTO wxOrderConfigUpdateBatchReqDTO);


    @Slf4j
    @Component
    class FallBackService implements FallbackFactory<WxOrderConfigClientService> {
        @Override
        public WxOrderConfigClientService create(Throwable throwable) {
            return new WxOrderConfigClientService() {
                @Override
                public Page<WxOrderConfigDTO> pageOrderConfig(WxStorePageReqDTO wxStorePageReqDTO) {
                    log.error("获取微信点餐门店配置列表失败，e{}", throwable.getMessage());
                    throw new BusinessException("获取微信点餐门店配置列表失败");
                }

                @Override
                public WxOrderConfigDTO getDetailConfig(WxStoreReqDTO wxStoreReqDTO) {
                    log.error("获取微信点餐门店详细配置信息失败，e{}", throwable.getMessage());
                    throw new BusinessException("获取微信点餐门店详细配置信息失败");
                }

                @Override
                public Boolean updateStoreConfig(WxOrderConfigDTO wxOrderConfigDTO) {
                    log.error("修改微信点餐门店配置信息失败，e{}", throwable.getMessage());
                    throw new BusinessException("修改微信点餐门店详细配置信息失败");
                }

                @Override
                public Boolean updateStoreConfigBatch(WxOrderConfigUpdateBatchReqDTO wxOrderConfigUpdateBatchReqDTO) {
                    log.error("批量修改微信点餐门店配置信息失败，e{}", throwable.getMessage());
                    throw new BusinessException("批量修改微信点餐门店配置信息失败");
                }


            };
        }
    }
}
