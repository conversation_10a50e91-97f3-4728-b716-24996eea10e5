package com.holderzone.holder.saas.aggregation.app.converter;

import com.holderzone.holder.saas.member.terminal.dto.volume.ResponseCheckSingleVolumeRedeem;
import com.holderzone.holder.saas.member.terminal.dto.volume.ResponseCheckVolumeRedeem;

public class VolumeConverter {

    private VolumeConverter(){

    }


    public static void responseCheckVolumeRedeemConverter(ResponseCheckVolumeRedeem responseCheckVolumeRedeem ,ResponseCheckSingleVolumeRedeem obj) {
        responseCheckVolumeRedeem.setBelongsMemberInfoGuid(obj.getBelongsMemberInfoGuid());
        responseCheckVolumeRedeem.setVolumeName(obj.getVolumeName());
        responseCheckVolumeRedeem.setVolumeMoney(obj.getVolumeMoney());
        responseCheckVolumeRedeem.setCanNum(obj.getCanNum());
        responseCheckVolumeRedeem.setReceiveTime(obj.getReceiveTime());
        responseCheckVolumeRedeem.setVolumeCode(obj.getVolumeCode());
        responseCheckVolumeRedeem.setStartValidityPeriod(obj.getStartValidityPeriod());
        responseCheckVolumeRedeem.setEndValidityPeriod(obj.getEndValidityPeriod());
        responseCheckVolumeRedeem.setUseThreshold(obj.getUseThreshold());
        responseCheckVolumeRedeem.setUseThresholdFull(obj.getUseThresholdFull());
        responseCheckVolumeRedeem.setRuleTip(obj.getRuleTip());
        responseCheckVolumeRedeem.setVolumeType(obj.getVolumeType());
    }
}
