package com.holderzone.holder.saas.aggregation.merchant.service.rpc.journaling;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.store.client.entity.dto.ReportDetailDTO;
import com.holderzone.holder.saas.store.client.entity.dto.StoreGuidAndStoreNameDTO;
import com.holderzone.saas.store.dto.journaling.req.*;
import com.holderzone.saas.store.dto.journaling.resp.*;
import com.holderzone.saas.store.dto.report.query.ReportExportDTO;
import com.holderzone.saas.store.dto.report.resp.ExportPayRespDTO;
import com.holderzone.saas.store.dto.report.resp.ExportRespDTO;
import com.holderzone.saas.store.dto.report.resp.PageExt;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReportClientService
 * @date 2019/05/29 10:29
 * @description 报表feignClientService
 * @program holder-saas-store
 */
@FeignClient(name = "holder-saas-store-report", fallbackFactory = ReportClientService.FallBackClass.class)
@Service
public interface ReportClientService {

    @PostMapping("/order_detail/page")
    Page<OrderDetailRespDTO> pageOrderDetail(@RequestBody OrderDetailReqDTO orderDetailReqDTO);

    @PostMapping("/storeGather/storeGatherList")
    StoreGatherTotalRespDTO storeGatherList(@RequestBody StoreGatherReportReqDTO storeGatherReportReqDTO);

    @PostMapping("/busSituation/businessData")
    BusinessDataRespDTO businessData(@RequestBody BusinessSituationReqDTO businessSituationReqDTO);

    @PostMapping("/busSituation/busiHisTrend")
    BusinessHisTrendRespDTO hisTrend(@RequestBody BusinessSituationReqDTO businessSituationReqDTO);

    @PostMapping("/journal/export")
    ExportRespDTO export(ReportExportDTO reportExportDTO);

    @PostMapping("/journal/export_pay")
    ExportPayRespDTO payExport(ReportExportDTO reportExportDTO);

    @PostMapping("/busSituation/screen_business_data")
    ScreenBusinessDataRespDTO getScreenData(@RequestBody BusinessSituationReqDTO businessSituationReqDTO);

    @PostMapping("/sale_count/screen_sale")
    SaleRespDTO saleCount(SaleCountReqDTO saleCountReqDTO);

    @PostMapping("/appStoreStatistics/list")
    StoreStatisticsAppRespDTO appStoreStatistics(@RequestBody StoreStatisticsAppReqDTO storeStatisticsAppReqDTO);

    @PostMapping("/sale_by_hour/statistics")
    SaleStatisticsByHoursRespDTO doStatisticsByHours(@RequestBody SaleStatisticsByHoursReqDTO saleStatisticsByHoursReqDTO);

    @PostMapping("/busSituation/single_business_data")
    RetailBusinessDataRespDTO singleBusinessData(@RequestBody BusinessSituationReqDTO businessSituationReqDTO);

    @PostMapping("/busSituation/single_his_trend")
    BusinessHisTrendRespDTO singleHisTrend(@RequestBody BusinessSituationReqDTO businessSituationReqDTO);

    @PostMapping("/order_detail/single_page")
    Page<OrderDetailReportRespDTO> singlePageOrderDetail(@RequestBody OrderDetailReqDTO orderDetailReqDTO);

    @PostMapping("/sales_volume/page")
    Page<SalesVolumeRespDTO> pageSalesVolumeRespDTO(@RequestBody SalesVolumeReqDTO salesVolumeReqDTO);

    @PostMapping("/pay_serial/page")
    PageExt<PaySerialStatisticsRespDTO> pagePaySerial(@RequestBody PaySerialStatisticsReqDTO paySerialStatisticsReqDTO);

    @PostMapping("/sale_detail_new/sale_detail_export_oss")
    void saleDetailExportOss(@RequestBody SaleDetailReportReqDTO saleDetailReportReqDTO);

    @PostMapping("/sale_detail_new/page_sale_detail_all")
    List<SaleDetailReportRespDTO> pageSaleDetailAll(@RequestBody SaleDetailReportReqDTO saleDetailReportReqDTO);

    @PostMapping("/order_detail/getPaymentAll")
    Map<String, String> getPaymentAll(@RequestBody OrderDetailReqDTO orderDetailReqDTO);

    @GetMapping("/sale_detail_new/get_order_details_oss_url")
    String getOrderDetailsOssUrl(@RequestParam("key") String key);

    @PostMapping("/order_detail/export_order_detail")
    List<OrderDetailRespDTO> exportOrderDetail(@RequestBody OrderDetailReqDTO orderDetailReqDTO);

    @PostMapping("/journal/getExportToken")
    String findSpecialSaleDetail(@RequestBody ReportDetailDTO reportDetailDTO);

    @GetMapping("/journal/getUrl")
    String getRedisDetailData(@RequestParam("token") String token);

    @GetMapping("/journal/getStoreGuidAndStoreName")
    List<StoreGuidAndStoreNameDTO> getStoreGuidAndStoreName(@RequestParam("keyword") String keyword);

    @Component
    @Slf4j
    class FallBackClass implements FallbackFactory<ReportClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ReportClientService create(Throwable throwable) {
            return new ReportClientService() {
                @Override
                public Page<OrderDetailRespDTO> pageOrderDetail(OrderDetailReqDTO orderDetailReqDTO) {
                    log.error(HYSTRIX_PATTERN, "storeGatherList", JacksonUtils.writeValueAsString(orderDetailReqDTO), ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("查询报表-订单详情发生异常");
                }

                @Override
                public StoreGatherTotalRespDTO storeGatherList(StoreGatherReportReqDTO storeGatherReportReqDTO) {
                    log.error(HYSTRIX_PATTERN, "storeGatherList", JacksonUtils.writeValueAsString(storeGatherReportReqDTO), ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("查询报表-门店汇总信息发生异常");
                }

                @Override
                public BusinessDataRespDTO businessData(BusinessSituationReqDTO businessSituationReqDTO) {
                    log.error(HYSTRIX_PATTERN, "storeGatherList", JacksonUtils.writeValueAsString(businessSituationReqDTO), ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("查询报表-营业概况-营业数据信息发生异常");
                }

                @Override
                public BusinessHisTrendRespDTO hisTrend(BusinessSituationReqDTO businessSituationReqDTO) {
                    log.error(HYSTRIX_PATTERN, "storeGatherList", JacksonUtils.writeValueAsString(businessSituationReqDTO), ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("查询报表-营业概况-历史趋势信息发生异常");
                }

                @Override
                public ExportRespDTO export(ReportExportDTO reportExportDTO) {
                    log.error(HYSTRIX_PATTERN, "export", JacksonUtils.writeValueAsString(reportExportDTO), ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("报表导出获取数据发生异常");
                }

                @Override
                public ExportPayRespDTO payExport(ReportExportDTO reportExportDTO) {
                    log.error(HYSTRIX_PATTERN, "payExport", JacksonUtils.writeValueAsString(reportExportDTO), ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("支付统计报表导出获取数据发生异常");
                }

                @Override
                public ScreenBusinessDataRespDTO getScreenData(BusinessSituationReqDTO businessSituationReqDTO) {
                    log.error(HYSTRIX_PATTERN, "getScreenData", JacksonUtils.writeValueAsString(businessSituationReqDTO), ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("查询大屏营业额发生异常");
                }

                @Override
                public SaleRespDTO saleCount(SaleCountReqDTO saleCountReqDTO) {
                    log.error(HYSTRIX_PATTERN, "saleCount", JacksonUtils.writeValueAsString(saleCountReqDTO), ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("查询商品/分类销售额发生异常");
                }

                @Override
                public StoreStatisticsAppRespDTO appStoreStatistics(StoreStatisticsAppReqDTO storeStatisticsAppReqDTO) {
                    log.error(HYSTRIX_PATTERN, "appStoreStatistics", JacksonUtils.writeValueAsString(storeStatisticsAppReqDTO), ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("查询门店销售排行发生异常");
                }

                @Override
                public SaleStatisticsByHoursRespDTO doStatisticsByHours(SaleStatisticsByHoursReqDTO saleStatisticsByHoursReqDTO) {
                    log.error(HYSTRIX_PATTERN, "doStatisticsByHours", JacksonUtils.writeValueAsString(saleStatisticsByHoursReqDTO), ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("查询时间点销售额发生异常");
                }

                @Override
                public RetailBusinessDataRespDTO singleBusinessData(BusinessSituationReqDTO businessSituationReqDTO) {
                    log.error(HYSTRIX_PATTERN, "singleBusinessData", JacksonUtils.writeValueAsString(businessSituationReqDTO), ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("商超-营业概况数据查询异常");
                }

                @Override
                public BusinessHisTrendRespDTO singleHisTrend(BusinessSituationReqDTO businessSituationReqDTO) {
                    log.error(HYSTRIX_PATTERN, "singleHisTrend", JacksonUtils.writeValueAsString(businessSituationReqDTO), ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("商超-历史趋势查询异常");
                }

                @Override
                public Page<OrderDetailReportRespDTO> singlePageOrderDetail(OrderDetailReqDTO orderDetailReqDTO) {
                    log.error(HYSTRIX_PATTERN, "singlePageOrderDetail", JacksonUtils.writeValueAsString(orderDetailReqDTO), ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("商超-支付统计查询异常");
                }

                @Override
                public Page<SalesVolumeRespDTO> pageSalesVolumeRespDTO(SalesVolumeReqDTO salesVolumeReqDTO) {
                    log.error(HYSTRIX_PATTERN, "pageSalesVolumeRespDTO", JacksonUtils.writeValueAsString(salesVolumeReqDTO), ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("商品销量统计查询异常");
                }

                @Override
                public PageExt<PaySerialStatisticsRespDTO> pagePaySerial(PaySerialStatisticsReqDTO paySerialStatisticsReqDTO) {
                    log.error(HYSTRIX_PATTERN, "pageSalesVolumeRespDTO", JacksonUtils.writeValueAsString(paySerialStatisticsReqDTO), ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("支付流水");
                }

                @Override
                public void saleDetailExportOss(SaleDetailReportReqDTO saleDetailReportReqDTO) {
                    log.error(HYSTRIX_PATTERN, "orderExportReqDTO", JacksonUtils.writeValueAsString(saleDetailReportReqDTO), ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("销售明细导出异常");
                }

                @Override
                public List<SaleDetailReportRespDTO> pageSaleDetailAll(SaleDetailReportReqDTO saleDetailReportReqDTO) {
                    log.error(HYSTRIX_PATTERN, "orderExportReqDTO", JacksonUtils.writeValueAsString(saleDetailReportReqDTO), ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("销售明细查询异常");
                }

                @Override
                public Map<String, String> getPaymentAll(OrderDetailReqDTO orderDetailReqDTO) {
                    log.error(HYSTRIX_PATTERN, "getPaymentAll", JacksonUtils.writeValueAsString(orderDetailReqDTO), ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("门店收款方式查询异常");
                }

                @Override
                public String getOrderDetailsOssUrl(String key) {
                    log.error(HYSTRIX_PATTERN, "getOrderDetailsOssUrl", JacksonUtils.writeValueAsString(key), ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("获取oss地址异常");
                }

                @Override
                public List<OrderDetailRespDTO> exportOrderDetail(OrderDetailReqDTO orderDetailReqDTO) {
                    log.error(HYSTRIX_PATTERN, "exportOrderDetail", JacksonUtils.writeValueAsString(orderDetailReqDTO), ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("查询订单明细数据异常");
                }

                @Override
                public String findSpecialSaleDetail(ReportDetailDTO reportDetailDTO) {
                    log.error(HYSTRIX_PATTERN, ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("销售明细查询异常");
                }

                @Override
                public String getRedisDetailData(String token) {
                    log.error(HYSTRIX_PATTERN, ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("获取url异常");
                }

                @Override
                public List<StoreGuidAndStoreNameDTO> getStoreGuidAndStoreName(String keyword) {
                    log.error(HYSTRIX_PATTERN, ThrowableUtils.asString(throwable.getCause()));
                    throw new BusinessException("获取getStoreGuidAndStoreName异常");
                }
            };
        }
    }

}
