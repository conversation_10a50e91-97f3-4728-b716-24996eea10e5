package com.holderzone.holder.saas.aggregation.app.service.feign;

import com.holderzone.framework.base.dto.file.FileDto;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(name = "base-service", fallbackFactory = BaseFeignService.ServiceFallBack.class)
public interface BaseFeignService {

    @PostMapping("/file")
    String upload(@RequestBody FileDto fileDto);

    @DeleteMapping("/file")
    void delete(@RequestParam("fileUrl") String fileUrl);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<BaseFeignService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public BaseFeignService create(Throwable cause) {
            return new BaseFeignService() {

                @Override
                public String upload(FileDto fileDto) {
                    log.error(HYSTRIX_PATTERN, "upload", JacksonUtils.writeValueAsString(fileDto),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void delete(String fileUrl) {
                    log.error(HYSTRIX_PATTERN, "delete", "fileUrl=" + fileUrl,
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }
    }
}
