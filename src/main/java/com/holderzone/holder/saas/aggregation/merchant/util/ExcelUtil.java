package com.holderzone.holder.saas.aggregation.merchant.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.ObjectUtils;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ExcelUtil
 * @date 2019/1/28 15:31
 * @description 利用开源组件POI动态导出EXCEL文档工具类
 * @program holder-saas-store-business
 */
@Slf4j
public class ExcelUtil<T> {

    /**
     * 限制最大行数 6w
     */
    private static final int MAX_ROW_LIMIT = 60000;
    /**
     * 每张sheet最大行数 默认60000
     */
    private int maxRow = 60000;

    /**
     * 默认列宽
     */
    private static final short DEFAULT_COL_W = 15;
    /**
     * 默认行高
     */
    private static final short DEFAULT_ROW_H = 20;

    private HSSFWorkbook workbook;

    /**
     * 标题默认样式
     */
    private CellStyle defaultTitleStyle;

    /**
     * 内容默认样式
     */
    private CellStyle defaultContentStyle;

    /**
     * 数字正则表达式
     */
    private static final Pattern ALL_NUMBER_PATTERN = Pattern.compile("^//d+(//.//d+)?$");

    private void init() {
        // 声明工作簿
        workbook = new HSSFWorkbook();

        defaultTitleStyle = workbook.createCellStyle();
        defaultContentStyle = workbook.createCellStyle();

        HSSFFont font = workbook.createFont();
        font.setColor(HSSFFont.COLOR_RED);
        font.setBold(true);
        defaultTitleStyle.setFont(font);
        defaultTitleStyle.setAlignment(HorizontalAlignment.CENTER);
        defaultTitleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        defaultContentStyle.setAlignment(HorizontalAlignment.CENTER);
        defaultContentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
    }


    /**
     * 导出到excel
     *
     * @param headers      表格表头
     * @param collData     数据
     * @param outputStream outputStream 没有关闭 需自己手工关闭
     */
    public void exportExcel(String[] headers, Collection<T> collData, OutputStream outputStream) {
        this.exportExcel("sheet", headers, collData, outputStream);
    }


    /**
     * 创建一个有标题栏的默认样式的sheet
     *
     * @param sheetTitle sheetTitle
     * @param headers    表头
     * @return HSSFSheet
     */
    private HSSFSheet createDefaultSheet(String sheetTitle, String[] headers) {
        // 生成一个表格
        HSSFSheet sheet = workbook.createSheet(sheetTitle);
        // 默认列宽
        sheet.setDefaultColumnWidth(DEFAULT_COL_W);
        // 默认行高
        sheet.setDefaultRowHeightInPoints(DEFAULT_ROW_H);

        // 标题行
        HSSFRow titleRow = sheet.createRow(0);
        for (int i = 0; i < headers.length; i++) {
            HSSFCell cell = titleRow.createCell(i);
            HSSFRichTextString text = new HSSFRichTextString(headers[i]);
            cell.setCellStyle(defaultTitleStyle);
            cell.setCellValue(text);
        }
        return sheet;
    }

    /**
     * 导出到excel
     *
     * @param sheetTitle   工作簿title
     * @param headers      表格表头
     * @param collData     数据
     * @param outputStream outputStream 没有关闭 需自己手工关闭
     */
    public void exportExcel(String sheetTitle, String[] headers, Collection<T> collData, OutputStream outputStream) {
        init();

        // sheet编号
        int sheetNum = 1;

        // 生成一个表格
        HSSFSheet sheet = createDefaultSheet(sheetTitle + sheetNum, headers);

        if (!ObjectUtils.isEmpty(collData)) {

            HSSFRow contentRow = null;
            Iterator<T> it = collData.iterator();
            int index = 0;
            while (it.hasNext()) {
                index++;
                // 超过每张sheet最大行数
                if (index > maxRow) {
                    index = 1;
                    sheet = createDefaultSheet(sheetTitle + ++sheetNum, headers);
                }
                contentRow = sheet.createRow(index);
                T t = it.next();
                // 利用反射，根据javabean属性的先后顺序，动态调用getXxx()方法得到属性值
                Field[] fields = t.getClass().getDeclaredFields();
                for (int i = 0; i < fields.length; i++) {
                    HSSFCell cell = contentRow.createCell(i);
                    cell.setCellStyle(defaultContentStyle);
                    Field field = fields[i];
                    String fieldName = field.getName();
                    String getterMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                    try {
                        Class clazz = t.getClass();
                        Method getMethod = clazz.getMethod(getterMethodName);
                        Object value = getMethod.invoke(t);
                        handleValue(cell, value);
                    } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                        log.error("找不到方法" + getterMethodName + "()", e);
                    }
                }
            }
        }
        try {
            workbook.write(outputStream);
        } catch (IOException e) {
            log.error("excel导出失败", e);
        }
    }

    /**
     * 处理value格式
     *
     * @param cell  单元格
     * @param value value
     */
    private void handleValue(HSSFCell cell, Object value) {
        if (ObjectUtils.isEmpty(value)) {
            return;
        }

        String textValue = null;
        if (value instanceof Boolean) {
            textValue = (boolean) value ? "true" : "false";

        } else if (value instanceof Date) {
            textValue = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(value);

        } else if (value instanceof LocalDate) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            textValue = df.format((LocalDate) value);

        } else if (value instanceof LocalDateTime) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            textValue = df.format((LocalDateTime) value);

        } else if (value instanceof BigDecimal) {
            // 默认保留两位小数
            textValue = String.format("%.2f", value);
        } else {
            textValue = value.toString();
        }

        if (textValue != null) {

            Matcher matcher = ALL_NUMBER_PATTERN.matcher(textValue);
            if (matcher.matches()) {
                // 是数字当作double处理
                cell.setCellValue(Double.parseDouble(textValue));
            } else {

                HSSFRichTextString richTextString = new HSSFRichTextString(textValue);
                cell.setCellValue(richTextString);
            }
        }
    }

    /**
     * 设置每张sheet最大行数
     *
     * @param maxRow int
     */
    public void setMaxRow(int maxRow) {
        if (maxRow > MAX_ROW_LIMIT) {
            throw new RuntimeException("行数最多为" + MAX_ROW_LIMIT);
        }
        this.maxRow = maxRow;
    }

    /**
     * 给单元格添加下拉选择框，其数据只能从选择框中来
     *
     * @param workbook  workbook
     * @param tarSheet  目标sheet
     * @param menuItems 备选项值
     * @param firstRow  从哪一行开始添加
     * @param lastRow   从哪一行结束
     * @param column    添加到哪一列
     */
    public static void addDropDownList(HSSFWorkbook workbook, HSSFSheet tarSheet, String[] menuItems, int firstRow, int lastRow, int column) {
        if (ObjectUtils.isEmpty(workbook)) {
            throw new RuntimeException("workbook is null");
        }
        if (ObjectUtils.isEmpty(tarSheet)) {
            throw new RuntimeException("待添加菜单的sheet为null");
        }
        // 必须以字母开头，最长为31位
        String hiddenSheetName = "a" + UUID.randomUUID().toString().replace("-", "").substring(1, 31);

        //excel中的"名称"，用于标记隐藏sheet中的用作菜单下拉项的所有单元格
        String formulaId = "form" + UUID.randomUUID().toString().replace("-", "");

        // 用于存储 下拉菜单数据
        HSSFSheet hiddenSheet = workbook.createSheet(hiddenSheetName);
        //存储下拉菜单项的sheet页不显示
        workbook.setSheetHidden(workbook.getSheetIndex(hiddenSheet), true);
        HSSFRow row;
        HSSFCell cell;
        // 隐藏sheet中添加菜单数据
        for (int i = 0; i < menuItems.length; i++) {
            row = hiddenSheet.createRow(i);

            // 隐藏表的数据列必须和添加下拉菜单的列序号相同，否则不能显示下拉菜单
            cell = row.createCell(column);
            cell.setCellValue(menuItems[i]);
        }

        // 创建"名称"标签，用于链接
        HSSFName namedCell = workbook.createName();
        namedCell.setNameName(formulaId);
        namedCell.setRefersToFormula(hiddenSheetName + "!A$1:A$" + menuItems.length);
        HSSFDataValidationHelper dvHelper = new HSSFDataValidationHelper(tarSheet);
        DataValidationConstraint dvConstraint = dvHelper.createFormulaListConstraint(formulaId);

        CellRangeAddressList addressList = new CellRangeAddressList(firstRow, lastRow, column, column);

        // 添加菜单(将单元格与"名称"建立关联)
        HSSFDataValidation validation = (HSSFDataValidation) dvHelper.createValidation(dvConstraint, addressList);
        tarSheet.addValidationData(validation);
    }

    /**
     * 获取sheet的数据
     *
     * @param sheet             sheet
     * @param firstDataRowIndex 数据的第一行index 从0开始
     * @param firstDataColIndex 数据的第一列index 从0开始
     * @return 两层list的String集合，第一层为行，第二层为列
     */
    public static List<List<String>> getSheetData(Sheet sheet, int firstDataRowIndex, int firstDataColIndex) {
        return getSheetData(sheet, firstDataRowIndex, -1, firstDataColIndex, -1);
    }

    /**
     * 获取sheet的数据
     *
     * @param sheet             sheet
     * @param firstDataRowIndex 数据的第一行index 从0开始
     * @param lastDataRowIndex  数据的最后一行index，传负则取最后一行
     * @param firstDataColIndex 数据的第一列index 从0开始
     * @param lastDataColIndex  数据的最后一列index，传负则取最后一列
     * @return 两层list的String集合，第一层为行，第二层为列
     */
    public static List<List<String>> getSheetData(Sheet sheet, int firstDataRowIndex, int lastDataRowIndex, int firstDataColIndex, int lastDataColIndex) {
        lastDataRowIndex = getLastDataRowIndex(sheet, lastDataRowIndex, firstDataColIndex);
        List<List<String>> sheetData = new ArrayList<>(lastDataRowIndex - firstDataColIndex);
        for (int rowIndex = firstDataRowIndex; rowIndex <= lastDataRowIndex; rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (ObjectUtils.isEmpty(row)) {
                break;
            }
            // 传负则取最后一列
            if (lastDataColIndex < 0) {
                lastDataColIndex = row.getLastCellNum();
            }
            List<String> rowData = new ArrayList<>(lastDataColIndex - firstDataColIndex);
            for (int cellIndex = firstDataColIndex; cellIndex < lastDataColIndex; cellIndex++) {
                Cell cell = row.getCell(cellIndex);
                if (ObjectUtils.isEmpty(cell)) {
                    rowData.add(null);
                } else {
                    rowData.add(cell.toString());
                }
            }
            sheetData.add(rowData);
        }
        return sheetData;
    }

    /**
     * 格式化获取sheet的数据
     *
     * @param sheet             sheet
     * @param firstDataRowIndex 数据的第一行index 从0开始
     * @param lastDataRowIndex  数据的最后一行index，传负则取最后一行
     * @param firstDataColIndex 数据的第一列index 从0开始
     * @param lastDataColIndex  数据的最后一列index，传负则取最后一列
     * @return 两层list的String集合，第一层为行，第二层为列
     */
    public static List<List<String>> getSheetDataWithDataFormatter(Sheet sheet, int firstDataRowIndex, int lastDataRowIndex, int firstDataColIndex, int lastDataColIndex) {
        lastDataRowIndex = getLastDataRowIndex(sheet, lastDataRowIndex, firstDataColIndex);
        List<List<String>> sheetData = new ArrayList<>(lastDataRowIndex - firstDataColIndex);
        DataFormatter dataFormatter = new DataFormatter();
        for (int rowIndex = firstDataRowIndex; rowIndex <= lastDataRowIndex; rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (ObjectUtils.isEmpty(row)) {
                break;
            }
            // 传负则取最后一列
            if (lastDataColIndex < 0) {
                lastDataColIndex = row.getLastCellNum();
            }
            List<String> rowData = new ArrayList<>(lastDataColIndex - firstDataColIndex);
            for (int cellIndex = firstDataColIndex; cellIndex < lastDataColIndex; cellIndex++) {
                Cell cell = row.getCell(cellIndex);
                if (ObjectUtils.isEmpty(cell)) {
                    rowData.add(null);
                } else {
                    rowData.add(dataFormatter.formatCellValue(cell));
                }
            }
            sheetData.add(rowData);
        }
        return sheetData;
    }

    private static int getLastDataRowIndex(Sheet sheet, int lastDataRowIndex, int firstDataColIndex) {
        if (ObjectUtils.isEmpty(sheet)) {
            throw new RuntimeException("sheet is null");
        }
        log.info("第一行数据" + firstDataColIndex);
        // 传负则取最后一行
        if (lastDataRowIndex < 0) {
            lastDataRowIndex = sheet.getLastRowNum();
        }
        log.info("最后一行数据" + lastDataRowIndex);
        return lastDataRowIndex;
    }

    /**
     * 根据给定的行号创建一行单元格
     *
     * @param sheet
     * @param rowIndex
     * @return
     */
    public static XSSFRow createXSSFRow(XSSFSheet sheet, int rowIndex) {
        XSSFRow row = sheet.createRow(rowIndex);
        return row;
    }

    public static void saveExcel(XSSFWorkbook wb) {
        FileOutputStream fileOut;
        try {
            fileOut = new FileOutputStream("");
            wb.write(fileOut);
            fileOut.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }

    }
}
