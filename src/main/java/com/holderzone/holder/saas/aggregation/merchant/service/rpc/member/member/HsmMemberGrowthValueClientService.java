package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.HsmMemberGrowthValueClientService.MemberGrowthValueClientServiceFallback;
import com.holderzone.holder.saas.member.dto.account.request.MemberChangeGrowthValueReqDTO;
import com.holderzone.holder.saas.member.dto.account.request.MemberGrowthValueQueryReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberGrowthValueListRespDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberSourceTypeRespDTO;
import feign.hystrix.FallbackFactory;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberGrowthValueClientService
 * @date 2019/05/30 14:30
 * @description 会员成长值
 * @program holder-saas-member-account
 */
@Component
@FeignClient(name = "holder-saas-member-account", fallbackFactory = MemberGrowthValueClientServiceFallback.class)
public interface HsmMemberGrowthValueClientService {

    /**
     * 改变成长值
     *
     * @param growthValueReqDTO 需要改变成长值
     * @return 改变结果
     */
    @RequestMapping(value = "/hsm_member_growth_value/changeGrowthValue", produces = "application/json;charset=utf-8",
            method = RequestMethod.POST)
    boolean changeGrowthValue(@RequestBody MemberChangeGrowthValueReqDTO growthValueReqDTO);


    /**
     * 来源类型
     *
     * @return 集合
     */
    @RequestMapping(value = "/hsm_member_growth_value/listSourceType", produces = "application/json;charset=utf-8",
            method = RequestMethod.GET)
    List<MemberSourceTypeRespDTO> listSourceType();


    /**
     * 通过条件分页查询
     *
     * @param queryReqDTO 查询条件
     * @return 查询结果
     */
    @RequestMapping(value = "/hsm_member_growth_value/listByCondition", produces = "application/json;charset=utf-8",
            method = RequestMethod.POST)
    Page<MemberGrowthValueListRespDTO> listByCondition(
        @RequestBody MemberGrowthValueQueryReqDTO queryReqDTO);

    @Component
    class MemberGrowthValueClientServiceFallback implements
            FallbackFactory<HsmMemberGrowthValueClientService> {

        private static final Logger LOGGER = LoggerFactory
                .getLogger(MemberGrowthValueClientServiceFallback.class);

        @Override
        public HsmMemberGrowthValueClientService create(Throwable throwable) {
            return new HsmMemberGrowthValueClientService() {
                @Override
                public boolean changeGrowthValue(MemberChangeGrowthValueReqDTO growthValueReqDTO) {
                    LOGGER.error("修改会员成长值错误:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<MemberSourceTypeRespDTO> listSourceType() {
                    LOGGER.error("查询修改会员成长值来源错误:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public Page<MemberGrowthValueListRespDTO> listByCondition(
                        MemberGrowthValueQueryReqDTO queryReqDTO) {
                    LOGGER.error("分页查询修改会员成长值记录错误:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }
}
