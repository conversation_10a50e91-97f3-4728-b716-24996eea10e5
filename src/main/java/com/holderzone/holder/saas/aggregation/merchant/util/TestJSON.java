package com.holderzone.holder.saas.aggregation.merchant.util;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.member.request.MemberListReqDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TestJSON
 * @date 2018/10/19 13:52
 * @description //TODO
 * @program holder-saas-store-member
 */
public class TestJSON {
    public static void main(String[] args) {
        MemberListReqDTO memberListReqDTO =new MemberListReqDTO();
        memberListReqDTO.setMemberGradeGuid("-1");
        memberListReqDTO.setSex(-1);
        memberListReqDTO.setSearchType(1);
        String s = JacksonUtils.writeValueAsString(memberListReqDTO);
        System.out.println(s);
    }
}
