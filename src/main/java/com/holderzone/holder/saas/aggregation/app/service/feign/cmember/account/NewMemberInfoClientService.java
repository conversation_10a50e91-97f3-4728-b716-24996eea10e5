package com.holderzone.holder.saas.aggregation.app.service.feign.cmember.account;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.terminal.dto.card.RequestMemberCardRecharge;
import com.holderzone.holder.saas.member.terminal.dto.card.ResponseBaseCardInfo;
import com.holderzone.holder.saas.member.terminal.dto.card.ResponseRecharge;
import com.holderzone.holder.saas.member.terminal.dto.member.request.*;
import com.holderzone.holder.saas.member.terminal.dto.member.response.MemberPortrayalDetailsDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseQueryStorCardRespDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.SimpleMemberInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.order.RequestConfirmPay;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseQuickPay;
import com.holderzone.holder.saas.member.terminal.dto.volume.RequestUpdateVolumeRelevance;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestMemberBasic;
import com.holderzone.saas.store.dto.common.BaseRespDTO;
import com.holderzone.saas.store.dto.common.RedisReqDTO;
import com.holderzone.saas.store.dto.marketing.portrayal.MemberPortrayalDetailsVO;
import com.holderzone.saas.store.dto.member.common.LoginByPwdDTO;
import com.holderzone.saas.store.dto.member.request.MemberBindWeChatRespDTO;
import com.holderzone.saas.store.dto.member.response.BindAndLoginRespDTO;
import com.holderzone.saas.store.dto.member.response.PadLoginMemberRespDTO;
import com.holderzone.saas.store.dto.weixin.auth.WeChatUserInfoDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description 会员客户端
 * @date 2020年7月16日16:33:25
 */
@Component
@FeignClient(name = "HOLDER-SAAS-MEMBER-TERMINAL", fallbackFactory = NewMemberInfoClientService.MemberInfoClientServiceFallBack.class)
public interface NewMemberInfoClientService {

    /**
     * 会员新增
     *
     * @param saveCMemberDTO
     * @return
     */
    @PostMapping(value = "/hsmca/member/add", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    String add(@RequestBody RequestSaveCMemberDTO saveCMemberDTO);

    /**
     * 权益卡充值
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/hsmca/card/memberCardRechargeForEquityCard", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    ResponseRecharge memberCardRechargeForEquityCard(RequestMemberCardRecharge request);

    /**
     * 在某门店查找会员及卡信息
     *
     * @param queryStoreAndMemberAndCardReqDTO
     * @return
     */
    @PostMapping(value = "/hsmca/member/getMemberInfoAndCard", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    ResponseMemberAndCardInfoDTO getMemberInfoAndCard(@RequestBody RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO);

    /**
     * 查询门店所有会有会员卡信息
     *
     * @param queryStorCardReqDTO
     * @return
     */
    @GetMapping("/hsmca/member/systemManagementCard")
    List<ResponseQueryStorCardRespDTO> getAllSystemManagementCardByStore(@RequestBody RequestQueryStorCardReqDTO queryStorCardReqDTO);

    /**
     * 根据 会员信息guid 和 门店查询会员基本信息和 卡开通情况
     *
     * @return
     */
    @GetMapping(value = "/hsmca/member/getMemberInfoAndCardByMemberInfoGuid", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    ResponseMemberAndCardInfoDTO getMemberInfoAndCardByMemberInfoGuid(@RequestParam("memberInfoGuid") String memberInfoGuid);

    /**
     * 发送修改密码的验证码(忘记原密码)
     *
     * @param phoneNum
     * @return
     */
    @PostMapping(value = "/hsmca/member/{phoneNum}/pwdCode", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    boolean sendUpdatePasswordCode(@PathVariable("phoneNum") String phoneNum);

    /**
     * 修改支付密码
     */
    @PostMapping(value = "/hsmca/member/updatePassword", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    void updatePassword(RequestUpdatePassword updatePasswordReqDto);

    @GetMapping(value = "/hsmca/member/{memberInfoGuid}/payPwd/checkout")
    boolean payPwdCheckOut(@PathVariable("memberInfoGuid") String memberInfoGuid, @RequestParam("payPwd") String payPwd,
                           @RequestParam(value = "checkExpireGuid", required = false) String checkExpireGuid);

    /**
     * 修改会员基本信息
     *
     * @param updateMemberInfoDTO
     * @return
     */
    @PutMapping(value = "/hsmca/member/{memberInfoGuid}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    void updateMemberInfo(@PathVariable("memberInfoGuid") String memberInfoGuid, RequestUpdateMemberDTO updateMemberInfoDTO);

    /**
     * 校验手机号对应的会员的登录密码
     *
     * @param login 手机号,登录密码
     * @return 0密码错误 1登录成功 2手机号未注册
     */
    @ApiOperation(value = "会员登录（手机号和密码）")
    @PostMapping(value = "/hsmca/member/login_by_pwd")
    PadLoginMemberRespDTO loginByPwd(@RequestBody LoginByPwdDTO login);

    /**
     * pad修改会员密码
     *
     * @param requestUpdatePassword 手机号码,原密码,新密码,短信验证码,验证类型
     * @return 0成功，1验证码失效，2验证码错误，3当前手机号未注册
     */
    @ApiOperation(value = "pad修改会员密码", notes = "pad修改会员密码")
    @PostMapping(value = "/hsmca/member/update_pad_password", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Integer updatePadPassword(@Validated @RequestBody RequestUpdatePassword requestUpdatePassword);


    /**
     * 短信验证码登录
     *
     * @param memberBasic 会员基本信息请求体
     * @return PadLoginMemberRespDTO
     */
    @ApiOperation(value = "短信验证码登录")
    @PostMapping("/hsmca/member/login_by_sms_code")
    PadLoginMemberRespDTO loginBySmsCode(@RequestBody RequestMemberBasic memberBasic);

    /**
     * 通过unionId查询会员信息
     *
     * @param weChatUserInfoDTO 微信用户信息
     * @return 登录返回实体
     */
    @ApiOperation(value = "通过unionId查询会员信息")
    @PostMapping(value = "/hsmca/member/get_member_info_by_union_id")
    PadLoginMemberRespDTO getMemberInfoByUnionId(@RequestBody WeChatUserInfoDTO weChatUserInfoDTO);

    /**
     * 发送微信绑定会员手机号的验证码
     *
     * @param phoneNum 手机号
     * @return 结果
     */
    @ApiOperation(value = "发送微信绑定会员手机号的验证码")
    @GetMapping(value = "/hsmca/member/send_wechat_bind_code", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    boolean sendWechatBindCode(@RequestParam("phoneNum") String phoneNum);

    /**
     * 会员绑定微信
     *
     * @param memberBindWeChat 会员绑定微信实体
     * @return 返回结果
     */
    @ApiOperation("会员绑定微信")
    @PostMapping(value = "/hsmca/member/bind_member_and_we_chat")
    BindAndLoginRespDTO bindMemberAndWeChat(@RequestBody MemberBindWeChatRespDTO memberBindWeChat);

    /**
     * 微信用户信息存缓存
     *
     * @param redisReqDTO redis请求实体
     * @return Boolean
     */
    @ApiOperation("微信用户信息存缓存")
    @PostMapping(value = "/hsmca/member/put_we_chat_user_info_to_redis")
    Boolean putWeChatUserInfoToRedis(@RequestBody RedisReqDTO redisReqDTO);

    /**
     * 根据主体查询抵扣规则
     *
     * @param operSubjectGuid 运营主体
     * @return 结果
     */
    @GetMapping("/hsmca/order/query_deduction_rule")
    @ApiOperation(value = "根据主体查询抵扣规则")
    BaseRespDTO queryDeductionRule(@RequestParam("operSubjectGuid") String operSubjectGuid);

    /**
     * 校验手机号是否注册
     *
     * @param phoneNum 手机号
     * @return 校验结果 1未注册 2已注册
     */
    @ApiOperation("校验手机号是否注册")
    @GetMapping(value = "/hsmca/member/check_register_by_phone")
    Integer checkRegisterByPhone(@RequestParam("phoneNum") String phoneNum);

    /**
     * 根据手机号查询会员名称
     *
     * @param phoneNum 手机号
     * @return userName
     */
    @ApiOperation("根据手机号查询会员名称")
    @GetMapping(value = "/hsmca/member/query_name_by_phone")
    String queryNameByPhone(@RequestParam("phoneNum") String phoneNum);


    @GetMapping(value = "/hsa/base/oldBusinessDayHandle")
    void oldBusinessDayHandle();

    @ApiOperation("根据手机号后四位查询会员")
    @GetMapping(value = "/hsmca/member/query_member_by_phone_tail")
    List<SimpleMemberInfoDTO> queryMemberByPhoneTail(@RequestParam("phoneTail") String phoneTail);

    @PostMapping(value = "/hsmca/order/quick_pay")
    ResponseQuickPay quickPayOrder(@RequestBody RequestConfirmPay requestConfirmPay);

    @PutMapping(value = "/hsmca/order/quick_pay/refund/{orderGuid}")
    ResponseQuickPay refundQuickPayOrder(@PathVariable("orderGuid") String orderGuid);

    @ApiOperation("获取会员卡基本信息")
    @GetMapping("/hsmca/card/getCardInfo")
    ResponseBaseCardInfo getCardInfo();

    /**
     * 切换券关联关系
     * @param request request
     */
    @PostMapping("/hsmca/volume/updateVolumeRelevance")
    @ApiOperation(value = "切换券关联关系", response = Boolean.class)
    void updateVolumeRelevance(@RequestBody RequestUpdateVolumeRelevance request);

    @ApiOperation("查询会员画像信息")
    @GetMapping("/hsmca/member/query_member_portrayal")
    MemberPortrayalDetailsDTO queryMemberPortrayal(@RequestParam("memberGuid") String memberGuid);

    @ApiOperation("根据运营主体查询会员画像")
    @GetMapping("/marketing/member_portrayal/query_apply_setting")
    MemberPortrayalDetailsVO queryApplySetting(@RequestParam("operSubjectGuid") String operSubjectGuid);

    @Slf4j
    @Component
    class MemberInfoClientServiceFallBack implements FallbackFactory<NewMemberInfoClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public NewMemberInfoClientService create(Throwable throwable) {
            return new NewMemberInfoClientService() {
                @Override
                public String add(RequestSaveCMemberDTO saveCMemberDTO) {
                    log.error(HYSTRIX_PATTERN, "add", JacksonUtils.writeValueAsString(saveCMemberDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseRecharge memberCardRechargeForEquityCard(RequestMemberCardRecharge request) {
                    throw new BusinessException("权益卡充值异常");
                }

                @Override
                public List<ResponseQueryStorCardRespDTO> getAllSystemManagementCardByStore(RequestQueryStorCardReqDTO queryStorCardReqDTO) {
                    log.error(HYSTRIX_PATTERN, "get", JacksonUtils.writeValueAsString(queryStorCardReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询失败");
                }

                @Override
                public ResponseMemberAndCardInfoDTO getMemberInfoAndCard(RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO) {
                    log.error(HYSTRIX_PATTERN, "getMemberInfoAndCard", JacksonUtils.writeValueAsString(queryStoreAndMemberAndCardReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("在某门店查找会员及卡信息失败");
                }

                @Override
                public ResponseMemberAndCardInfoDTO getMemberInfoAndCardByMemberInfoGuid(String memberInfoGuid) {
                    log.error(HYSTRIX_PATTERN, "getMemberInfoAndCardByGuid", "memberInfoGuid" + memberInfoGuid, ThrowableUtils.asString(throwable));
                    throw new BusinessException("根据 会员信息guid 和 门店查询会员基本信息和 卡开通情况失败");
                }

                @Override
                public boolean sendUpdatePasswordCode(String phoneNum) {
                    log.error(HYSTRIX_PATTERN, "sendUpdatePasswordCode", phoneNum, ThrowableUtils.asString(throwable));
                    throw new BusinessException("发送修改密码的验证码(忘记原密码)失败");
                }

                @Override
                public void updatePassword(RequestUpdatePassword updatePasswordReqDto) {
                    log.error(HYSTRIX_PATTERN, "updatePassword", JacksonUtils.writeValueAsString(updatePasswordReqDto), ThrowableUtils.asString(throwable));
                    throw new BusinessException("修改支付密码成功");
                }

                @Override
                public boolean payPwdCheckOut(String memberInfoGuid, String payPwd, String checkExpireGuid) {
                    log.error(HYSTRIX_PATTERN, "updatePassword", "memberInfoGuid" + memberInfoGuid + "payPwd" + payPwd +
                            "checkExpireGuid" + checkExpireGuid, ThrowableUtils.asString(throwable));
                    throw new BusinessException("密码校验");
                }

                @Override
                public void updateMemberInfo(String memberInfoGuid, RequestUpdateMemberDTO updateMemberInfoDTO) {
                    log.error(HYSTRIX_PATTERN, "updateMemberInfo", "memberInfoGuid" + memberInfoGuid + JacksonUtils.writeValueAsString(updateMemberInfoDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("修改会员基本信息失败");
                }

                @Override
                public PadLoginMemberRespDTO loginByPwd(LoginByPwdDTO login) {
                    log.error(HYSTRIX_PATTERN, "loginByPwd", JacksonUtils.writeValueAsString(login), ThrowableUtils.asString(throwable));
                    throw new BusinessException("会员登录（手机号和密码）失败");
                }

                @Override
                public Integer updatePadPassword(RequestUpdatePassword requestUpdatePassword) {
                    log.error(HYSTRIX_PATTERN, "updatePadPassword", JacksonUtils.writeValueAsString(requestUpdatePassword),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PadLoginMemberRespDTO loginBySmsCode(RequestMemberBasic memberBasic) {
                    log.error(HYSTRIX_PATTERN, "loginBySmsCode", JacksonUtils.writeValueAsString(memberBasic),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PadLoginMemberRespDTO getMemberInfoByUnionId(WeChatUserInfoDTO weChatUserInfoDTO) {
                    log.error(HYSTRIX_PATTERN, "getMemberInfoByUnionId", JacksonUtils.writeValueAsString(weChatUserInfoDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public boolean sendWechatBindCode(String phoneNum) {
                    log.error(HYSTRIX_PATTERN, "sendWechatBindCode", phoneNum, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public BindAndLoginRespDTO bindMemberAndWeChat(MemberBindWeChatRespDTO memberBindWeChat) {
                    log.error(HYSTRIX_PATTERN, "bindMemberAndWeChat", JacksonUtils.writeValueAsString(memberBindWeChat),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Boolean putWeChatUserInfoToRedis(RedisReqDTO redisReqDTO) {
                    log.error(HYSTRIX_PATTERN, "putWeChatUserInfoToRedis", JacksonUtils.writeValueAsString(redisReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public BaseRespDTO queryDeductionRule(String operSubjectGuid) {
                    log.error(HYSTRIX_PATTERN, "queryDeductionRule", operSubjectGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Integer checkRegisterByPhone(String phoneNum) {
                    log.error(HYSTRIX_PATTERN, "checkRegisterByPhone", phoneNum,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public String queryNameByPhone(String phoneNum) {
                    log.error(HYSTRIX_PATTERN, "quer" +
                                    "yNameByPhone", phoneNum,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void oldBusinessDayHandle() {
                    log.error(HYSTRIX_PATTERN, "oldBusinessDayHandle", null,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<SimpleMemberInfoDTO> queryMemberByPhoneTail(String phoneTail) {
                    log.error(HYSTRIX_PATTERN, "queryMemberByPhoneTail", phoneTail,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ResponseQuickPay quickPayOrder(RequestConfirmPay requestConfirmPay) {
                    log.error(HYSTRIX_PATTERN, "quickPayOrder", requestConfirmPay,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ResponseQuickPay refundQuickPayOrder(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "refundQuickPayOrder", orderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ResponseBaseCardInfo getCardInfo() {
                    log.error(HYSTRIX_PATTERN, "getCardInfo", "",
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void updateVolumeRelevance(RequestUpdateVolumeRelevance request) {
                    log.error(HYSTRIX_PATTERN, "updateVolumeRelevance", request,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public MemberPortrayalDetailsDTO queryMemberPortrayal(String memberGuid) {
                    log.error(HYSTRIX_PATTERN, "queryMemberPortrayal", memberGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public MemberPortrayalDetailsVO queryApplySetting(String operSubjectGuid) {
                    log.error(HYSTRIX_PATTERN, "queryApplySetting", operSubjectGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

            };
        }
    }
}
