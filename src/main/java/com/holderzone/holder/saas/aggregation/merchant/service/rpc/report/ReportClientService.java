package com.holderzone.holder.saas.aggregation.merchant.service.rpc.report;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.journaling.req.SalesVolumeReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.SalesVolumeRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.SalesVolumeStoreRespDTO;
import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.openapi.SaleDetailLimitRespDTO;
import com.holderzone.saas.store.dto.report.openapi.SaleDetailQueryDTO;
import com.holderzone.saas.store.dto.report.query.*;
import com.holderzone.saas.store.dto.report.resp.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;


@Component
@FeignClient(name = "holder-saas-store-report", fallbackFactory = ReportClientService.ReportClientFallBack.class)
public interface ReportClientService {

    @PostMapping("/trade/detail/groupon")
    Page<GrouponTradeDetailRespDTO> pageGroupon(@RequestBody TradeDetailQueryDTO query);

    @PostMapping("/trade/detail/takeaway")
    Page<TakeawayTradeDetailRespDTO> pageTakeaway(@RequestBody TradeDetailQueryDTO query);

    @PostMapping("/trade/detail/groupon/export")
    String exportGroupon(@RequestBody TradeDetailQueryDTO query);

    @PostMapping("/trade/detail/takeaway/export")
    String exportTakeaway(@RequestBody TradeDetailQueryDTO query);

    @PostMapping("/trade/order/item/types")
    List<OrderItemTypeRespDTO> queryOrderItemTypes(@RequestBody ReportQueryVO query);

    @PostMapping("/trade/order/item/categories")
    List<String> queryOrderItemCategories(@RequestBody ReportQueryVO query);

    @PostMapping("/trade/return/list")
    Message<ReturnItemDTO> queryTradeOrderReturn(@RequestBody ReportQueryVO query);

    @PostMapping("/trade/free/list")
    Message<FreeItemDTO> queryTradeOrderFree(@RequestBody ReportQueryVO query);

    @PostMapping("/trade/return/list_detail")
    Message<ReturnDetailItemDTO> listDetail(@RequestBody ReportQueryVO query);

    @PostMapping("/trade/return/detail_export")
    String detailExport(@RequestBody ReportQueryVO query);

    @PostMapping("/trade/refund/list")
    Message<RefundDetailDTO> queryTradeOrderRefund(@RequestBody ReportQueryVO query);

    @PostMapping("/trade/refund/export")
    String tradeOrderRefundExport(@RequestBody ReportQueryVO query);

    @PostMapping("/trade/change/list")
    Message<ChangeDetailDTO> queryTradeOrderItemChange(@RequestBody ReportQueryVO query);

    @PostMapping("/trade/change/export")
    String tradeOrderItemChangeExport(@RequestBody ReportQueryVO query);

    @PostMapping("/trade/debt/list")
    Message<DebtUnitRecordDTO> queryTradeDebt(@RequestBody ReportQueryVO query);

    @PostMapping("/trade/debt/export")
    String tradeDebtExport(@RequestBody ReportQueryVO query);

    @PostMapping("/trade/free/export")
    String export(@RequestBody ReportQueryVO query);

    @ApiOperation(value = "商品分类统计")
    @PostMapping("/trade/item/type/statistics")
    GoodsSalesStatisticDTO queryItemTypeStatistics(@RequestBody @Valid GoodsSalesVO query);

    @ApiOperation(value = "套餐销量统计")
    @PostMapping("/trade/item/group/sale/statistics")
    GoodsSalesStatisticDTO queryGroupItemSaleStatistics(@RequestBody @Valid GoodsSalesVO query);

    @ApiOperation(value = "门店商品销量")
    @PostMapping("/trade/item/store/sale/statistics")
    Page<SalesVolumeRespDTO> pageStoreSaleStatistics(@RequestBody SalesVolumeReqDTO query);

    @ApiOperation(value = "门店商品销量 - 按门店统计")
    @PostMapping("/trade/item/group_by_store/sale/statistics")
    Page<SalesVolumeStoreRespDTO> pageGroupByStoreSaleStatistics(@RequestBody SalesVolumeReqDTO query);

    @ApiOperation(value = "门店商品销量合计")
    @PostMapping("/trade/item/store/sale/statistics/total")
    GoodsSalesTotalDTO storeSaleStatisticsTotal(@RequestBody SalesVolumeReqDTO query);

    @ApiOperation(value = "门店商品销量分类查询")
    @PostMapping("/trade/item/store/sale/statistics/type")
    List<String> pageStoreSaleStatisticsType(@RequestBody SalesVolumeReqDTO query);

    @ApiOperation(value = "门店商品销量套餐查询")
    @PostMapping("/trade/item/store/sale/statistics/group")
    List<GoodsSalesDTO> pageStoreSaleStatisticsGroup(@RequestBody SalesVolumeReqDTO query);

    @ApiOperation(value = "商品分类统计导出")
    @PostMapping("/trade/item/type/statistics/export")
    String exportItemTypeStatistics(@RequestBody @Valid GoodsSalesVO query);

    @ApiOperation(value = "套餐销量统计导出")
    @PostMapping("/trade/item/group/sale/statistics/export")
    String exportGroupItemSaleStatistics(@RequestBody @Valid GoodsSalesVO query);

    @ApiOperation(value = "收款构成")
    @PostMapping("/trade/detail/payment/constitute")
    Page<PaymentConstituteRespDTO> paymentConstitute(@RequestBody @Valid PaymentConstituteQueryDTO query);

    @ApiOperation(value = "收款构成导出")
    @PostMapping("/trade/detail/payment/constitute/export")
    String paymentConstituteExport(@RequestBody @Valid PaymentConstituteQueryDTO query);

    @ApiOperation(value = "销售明细")
    @PostMapping("/trade/item/sale/detail/page")
    Page<SalesDetailRespDTO> pageSaleDetail(@RequestBody @Valid SalesDetailQO query);


    @ApiOperation(value = "销售明细导出")
    @PostMapping("/trade/item/sale/detail/export")
    String exportSaleDetail(@RequestBody @Valid SalesDetailQO query);

    @ApiOperation(value = "开放接口 查询销售明细")
    @PostMapping("/openapi/sale_detail")
    SaleDetailLimitRespDTO querySaleDetail(@RequestBody SaleDetailQueryDTO query);

    @ApiOperation(value = "云收款统计")
    @PostMapping("/trade/detail/cloud/pay/constitute")
    CloudPayConstituteRespDTO cloudPayConstitute(@RequestBody CloudPayConstituteQueryDTO query);

    /**
     * 查询预定明细
     */
    @PostMapping("/reserve/list")
    Page<ReserveRespDTO> pageReserve(@RequestBody ReserveReportQueryVO query);

    /**
     * 导出预定明细
     */
    @PostMapping("/reserve/export")
    String exportReserve(@RequestBody ReserveReportQueryVO query);

    @ApiOperation(value = "聚合支付手续费")
    @PostMapping("/trade/detail/query_agg_pay_service_charge")
    BigDecimal queryAggPayServiceCharge(@RequestBody AggPayServiceChargeQueryDTO query);

    @Slf4j
    @Component
    class ReportClientFallBack implements FallbackFactory<ReportClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ReportClientService create(Throwable throwable) {

            return new ReportClientService() {
                @Override
                public Page<GrouponTradeDetailRespDTO> pageGroupon(TradeDetailQueryDTO query) {
                    log.error("团购结算列表熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("团购结算列表熔断！！");
                }

                @Override
                public Page<TakeawayTradeDetailRespDTO> pageTakeaway(TradeDetailQueryDTO query) {
                    log.error("外卖结算列表熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("外卖结算列表熔断！！");
                }

                @Override
                public String exportGroupon(TradeDetailQueryDTO query) {
                    log.error("团购结算列表导出熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("团购结算列表导出熔断！！");
                }

                @Override
                public String exportTakeaway(TradeDetailQueryDTO query) {
                    log.error("外卖结算列表导出熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("外卖结算列表导出熔断！！");
                }

                @Override
                public List<OrderItemTypeRespDTO> queryOrderItemTypes(ReportQueryVO query) {
                    log.error("查询订单商品类型熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("查询订单商品类型熔断！！");
                }

                @Override
                public List<String> queryOrderItemCategories(ReportQueryVO query) {
                    log.error("查询订单商品分类熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("查询订单商品分类熔断！！");
                }

                @Override
                public Message<ReturnItemDTO> queryTradeOrderReturn(ReportQueryVO query) {
                    log.error("查询订单退菜熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("查询订单退菜熔断！！");
                }

                @Override
                public Message<FreeItemDTO> queryTradeOrderFree(ReportQueryVO query) {
                    log.error("查询订单赠菜熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("查询订单赠菜熔断！！");
                }

                @Override
                public Message<ReturnDetailItemDTO> listDetail(ReportQueryVO query) {
                    log.error("查询退菜明细熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("查询退菜明细熔断！！");
                }

                @Override
                public String detailExport(ReportQueryVO query) {
                    log.error("退菜明细导出熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("退菜明细导出熔断！！");
                }

                @Override
                public Message<RefundDetailDTO> queryTradeOrderRefund(ReportQueryVO query) {
                    log.error("查询退款明细熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("查询退款明细熔断！！");
                }

                @Override
                public String tradeOrderRefundExport(ReportQueryVO query) {
                    log.error("退款明细导出熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("退款明细导出熔断！！");
                }

                @Override
                public Message<ChangeDetailDTO> queryTradeOrderItemChange(ReportQueryVO query) {
                    log.error("查询套餐换菜明细熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("查询套餐换菜明细熔断！！");
                }

                @Override
                public String tradeOrderItemChangeExport(ReportQueryVO query) {
                    log.error("套餐换菜明细导出熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("套餐换菜明细导出熔断！！");
                }

                @Override
                public Message<DebtUnitRecordDTO> queryTradeDebt(ReportQueryVO query) {
                    log.error("查询挂账明细熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("查询挂账还款明细熔断！！");
                }

                @Override
                public String tradeDebtExport(ReportQueryVO query) {
                    log.error("挂账明细导出熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("挂账明细导出熔断！！");
                }

                @Override
                public String export(ReportQueryVO query) {
                    log.error("赠菜列表导出熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("赠菜列表导出熔断！！");
                }

                @Override
                public GoodsSalesStatisticDTO queryItemTypeStatistics(GoodsSalesVO query) {
                    log.error("商品分类统计熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("商品分类统计熔断！！");
                }

                @Override
                public GoodsSalesStatisticDTO queryGroupItemSaleStatistics(GoodsSalesVO query) {
                    log.error("套餐销量统计熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("套餐销量统计熔断！！");
                }

                @Override
                public Page<SalesVolumeRespDTO> pageStoreSaleStatistics(SalesVolumeReqDTO query) {
                    log.error("门店商品销量熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("门店商品销量熔断！！");
                }

                @Override
                public Page<SalesVolumeStoreRespDTO> pageGroupByStoreSaleStatistics(SalesVolumeReqDTO query) {
                    log.error("门店商品销量熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("门店商品销量熔断！！");
                }

                @Override
                public GoodsSalesTotalDTO storeSaleStatisticsTotal(SalesVolumeReqDTO query) {
                    log.error("门店商品销量合计熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("门店商品销量合计熔断！！");
                }

                @Override
                public List<String> pageStoreSaleStatisticsType(SalesVolumeReqDTO query) {
                    log.error("门店商品销量分类查询熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("门店商品销量分类查询熔断！！");
                }

                @Override
                public List<GoodsSalesDTO> pageStoreSaleStatisticsGroup(SalesVolumeReqDTO query) {
                    log.error(HYSTRIX_PATTERN, "pageStoreSaleStatisticsGroup",
                            JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public String exportItemTypeStatistics(@Valid GoodsSalesVO query) {
                    log.error("商品分类统计导出熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("商品分类统计导出！！");
                }

                @Override
                public String exportGroupItemSaleStatistics(@Valid GoodsSalesVO query) {
                    log.error("套餐销量统计导出熔断，入参:{}", JacksonUtils.writeValueAsString(query));
                    throw new BusinessException("套餐销量统计导出！！");
                }

                @Override
                public Page<PaymentConstituteRespDTO> paymentConstitute(PaymentConstituteQueryDTO query) {
                    log.error(HYSTRIX_PATTERN, "paymentConstitute", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public String paymentConstituteExport(PaymentConstituteQueryDTO query) {
                    log.error(HYSTRIX_PATTERN, "paymentConstituteExport", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<SalesDetailRespDTO> pageSaleDetail(SalesDetailQO query) {
                    log.error(HYSTRIX_PATTERN, "pageSaleDetail", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public String exportSaleDetail(SalesDetailQO query) {
                    log.error(HYSTRIX_PATTERN, "exportSaleDetail", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public SaleDetailLimitRespDTO querySaleDetail(SaleDetailQueryDTO query) {
                    log.error(HYSTRIX_PATTERN, "querySaleDetail", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CloudPayConstituteRespDTO cloudPayConstitute(CloudPayConstituteQueryDTO query) {
                    log.error(HYSTRIX_PATTERN, "cloudPayConstitute", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<ReserveRespDTO> pageReserve(ReserveReportQueryVO query) {
                    log.error(HYSTRIX_PATTERN, "pageReserve", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public String exportReserve(ReserveReportQueryVO query) {
                    log.error(HYSTRIX_PATTERN, "exportReserve", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public BigDecimal queryAggPayServiceCharge(AggPayServiceChargeQueryDTO query) {
                    log.error(HYSTRIX_PATTERN, "queryAggPayServiceCharge", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }

}
