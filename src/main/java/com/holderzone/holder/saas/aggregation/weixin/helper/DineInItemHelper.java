package com.holderzone.holder.saas.aggregation.weixin.helper;

import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.weixin.deal.*;
import com.holderzone.saas.store.util.BigDecimalUtil;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

public class DineInItemHelper {

    private DineInItemHelper() {

    }

    public static List<RequestDishInfo> dineInItem2DishList(List<DineInItemDTO> dineInItemList) {
        return dineInItemList.stream().map(dineInItemDTO -> {
            RequestDishInfo dishInfo = new RequestDishInfo();
            dishInfo.setOrderItemGuid(dineInItemDTO.getGuid());
            dishInfo.setDishGuid(dineInItemDTO.getItemGuid());
            dishInfo.setDishName(dineInItemDTO.getItemName());
            dishInfo.setDishSpecification(dineInItemDTO.getSkuGuid());
            dishInfo.setDishUnit(dineInItemDTO.getUnit());
            dishInfo.setDishNum(dineInItemDTO.getCurrentCount());
            dishInfo.setGiftDishNum(BigDecimal.ZERO);
            dishInfo.setMainGoodGuid(null);
            dishInfo.setIsMainGood(1);
            dishInfo.setSurcharge(BigDecimal.ZERO);
            dishInfo.setDishType(dineInItemDTO.getItemType());
            dishInfo.setSubtotal(dineInItemDTO.getItemPrice());
            dishInfo.setDishOriginalUnitPrice(dineInItemDTO.getOriginalPrice());
            dishInfo.setDishSellUnitPrice(dineInItemDTO.getPrice());
            dishInfo.setPayPrice(dineInItemDTO.getItemPrice());
            if (Objects.isNull(dineInItemDTO.getTotalDiscountFee())) {
                dineInItemDTO.setTotalDiscountFee(BigDecimal.ZERO);
            }
            if (BigDecimalUtil.greaterEqual(dineInItemDTO.getItemPrice(), dineInItemDTO.getTotalDiscountFee())) {
                dishInfo.setPayPrice(dineInItemDTO.getItemPrice().subtract(dineInItemDTO.getTotalDiscountFee()));
            } else {
                dishInfo.setPayPrice(BigDecimal.ZERO);
            }
            dishInfo.setDishMemberPrice(dineInItemDTO.getMemberPrice());
            return dishInfo;
        }).collect(Collectors.toList());
    }

    public static void shopCartSetData(ShopCartRespDTO shopCartRespDTO,
                                       List<ItemInfoDTO> itemInfoDTOS,
                                       boolean usedMemberRights) {
        // 购物车排序
        itemInfoDTOS.sort(Comparator.nullsLast(Comparator.comparing(ItemInfoDTO::getIsSoldOut).reversed()));
        shopCartRespDTO.setItemInfoDTOS(itemInfoDTOS);
        BigDecimal pieceCount = itemInfoDTOS.stream()
                .map(ItemInfoDTO::getCurrentCount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        shopCartRespDTO.setPieceCount(pieceCount);
        shopCartRespDTO.setSpeciesCount(itemInfoDTOS.size());

        // 原价总价
        BigDecimal originPriceTotal = itemInfoDTOS.stream()
                .map(ItemInfoDTO::getOriginalPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        shopCartRespDTO.setOriginPrice(originPriceTotal);

        // 会员价/折扣 总价
        BigDecimal memberPriceTotal = itemInfoDTOS.stream()
                .map(ItemInfoDTO::getMinPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        shopCartRespDTO.setPreferentialPrice(memberPriceTotal);
        shopCartRespDTO.setDiscountPrice(originPriceTotal.subtract(memberPriceTotal));
        shopCartRespDTO.setEnablePreferentialPrice(false);
        // 用了会员权益
        if (usedMemberRights) {
            shopCartRespDTO.setEnablePreferentialPrice(true);
        }
    }

    /**
     * @param itemInfoDTOS 商品集合
     * @return 订单超重判断
     */
    public static String orderUpperLimit(List<ItemInfoDTO> itemInfoDTOS) {
        String reStr = null;
        if (!ObjectUtils.isEmpty(itemInfoDTOS)) {
            reStr = getReStr(itemInfoDTOS, reStr);
        }
        return reStr;
    }

    public static String getReStr(List<ItemInfoDTO> itemInfoDTOS, String reStr) {
        boolean isWeight = itemInfoDTOS.stream().anyMatch(x -> x.getItemType() == 3);
        if (isWeight) {
            BigDecimal bigDecimal = itemInfoDTOS.stream().map(x -> x.getItemType() == 3 ? BigDecimal.ZERO
                    : x.getCurrentCount()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            if (bigDecimal.intValue() > 9999.999) {
                reStr = "单次下单数量不可超过9999.999";
            }
        } else {
            BigDecimal bigDecimal = itemInfoDTOS.stream().map(x -> x.getItemType() == 3 ? BigDecimal.ZERO
                    : x.getCurrentCount()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            if (bigDecimal.intValue() > 9999) {
                reStr = "单次下单数量不可超过9999";
            }
        }
        return reStr;
    }

    /**
     * 预订单项转换
     *
     * @param shopCartItemReqDTOS 商品集合
     * @return 单项集合
     */
    public static List<PreOrderItemDTO> transformPreOrderItem(List<ShopCartItemReqDTO> shopCartItemReqDTOS, boolean enablePreferentialPrice) {
        boolean isLogin = WeixinUserThreadLocal.getIsLogin();
        return shopCartItemReqDTOS.stream().map(x -> {
            ItemInfoDTO itemInfoDTO = x.getItemInfoDTO();
            ItemInfoSkuDTO uckSku = PriceCalculateHelper.getUckSku(itemInfoDTO);
            BigDecimal showMemberPrice = itemInfoDTO.getShowMemberPrice();
            return new PreOrderItemDTO()
                    .setItemName(itemInfoDTO.getName())
                    .setItemCount(itemInfoDTO.getCurrentCount())
                    .setItemType(itemInfoDTO.getItemType())
                    .setSkuName(uckSku.getName())
                    .setUnit(uckSku.getUnit())
                    .setPictureUrl(itemInfoDTO.getPictureUrl())
                    .setItemPrice(itemInfoDTO.getShowPrice())
                    .setEnablePreferentialPrice(isLogin && enablePreferentialPrice && showMemberPrice != null && showMemberPrice.compareTo(BigDecimal.ZERO) > 0)
                    .setItemPreferentialPrice(showMemberPrice)
                    .setPreOrderSubItemDTOS(transformPreOrderSubItem(itemInfoDTO))
                    .setPreOrderItemAttrDTOList(transformPreOrderItemAttr(itemInfoDTO.getAttrGroupList()));
        }).collect(Collectors.toList());
    }

    /**
     * @param itemInfoDTO 商品集合
     * @return 预订单属性转换
     */
    public static List<PreOrderSubItemDTO> transformPreOrderSubItem(ItemInfoDTO itemInfoDTO) {
        ArrayList<PreOrderSubItemDTO> preOrderSubItemDTOS = new ArrayList<>();
        if ((itemInfoDTO.getItemType() == 1 || itemInfoDTO.getItemType() == 5) && !ObjectUtils.isEmpty(itemInfoDTO.getSubgroupList())) {
            List<ItemInfoSubgroupDTO> subgroupList = itemInfoDTO.getSubgroupList();
            for (ItemInfoSubgroupDTO subgroupDTO : subgroupList) {
                List<ItemInfoSubSkuDTO> subItemSkuList = subgroupDTO.getSubItemSkuList();
                for (ItemInfoSubSkuDTO skuDTO : subItemSkuList) {
                    if (subgroupDTO.getPickNum() == 0 || skuDTO.getDefaultNum() > 0) {
                        PreOrderSubItemDTO preOrderSubItemDTO = new PreOrderSubItemDTO();
                        preOrderSubItemDTO.setItemName(skuDTO.getItemName());
                        int currentCount = skuDTO.getDefaultNum() * skuDTO.getItemNum().intValue();
                        preOrderSubItemDTO.setItemAddPrice(skuDTO.getAddPrice());
                        preOrderSubItemDTO.setCurrentCount(currentCount);
                        preOrderSubItemDTO.setPreOrderItemAttrDTOList(transformPreOrderItemAttr(skuDTO.getAttrGroupList()));
                        preOrderSubItemDTOS.add(preOrderSubItemDTO);
                    }
                }
            }
        }
        return preOrderSubItemDTOS;
    }

    /**
     * @param itemInfoAttrGroupDTOS 属性组集合
     * @return 预订单属性转换
     */
    public static List<PreOrderItemAttrDTO> transformPreOrderItemAttr(List<ItemInfoAttrGroupDTO> itemInfoAttrGroupDTOS) {
        return ObjectUtils.isEmpty(itemInfoAttrGroupDTOS)
                ? Collections.emptyList()
                : itemInfoAttrGroupDTOS.stream()
                .flatMap(x -> x.getAttrList().stream())
                .filter(x -> x.getUck() == 1)
                .map(x -> new PreOrderItemAttrDTO()
                        .setAttrName(x.getName())
                        .setAttrPrice(x.getPrice())).collect(Collectors.toList());
    }

}
