package com.holderzone.holder.saas.aggregation.merchant.controller.member.account;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.HsmMemberGrowthValueClientService;
import com.holderzone.holder.saas.member.dto.account.request.MemberChangeGrowthValueReqDTO;
import com.holderzone.holder.saas.member.dto.account.request.MemberGrowthValueQueryReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberGrowthValueListRespDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberSourceTypeRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberGrowthValueController
 * @date 2019/05/30 15:06
 * @description 会员成长值
 * @program holder-saas-member-account
 */
@RestController
@Api(description = "会员成长值相关操作")
@RequestMapping(value = "/hsm_member_growth_value")
public class HsmMemberGrowthValueController {

    @Resource
    private HsmMemberGrowthValueClientService hsmMemberGrowthValueClientService;


    /**
     * 改变成长值
     *
     * @param growthValueReqDTO 需要改变成长值
     * @return 改变结果
     */
    @PostMapping(value = "/changeGrowthValue", produces = "application/json;charset=utf-8")
    @ApiOperation("改变成长值")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "改变成长值")
    public Result<Boolean> changeGrowthValue(
            @RequestBody MemberChangeGrowthValueReqDTO growthValueReqDTO) {
        return Result.buildSuccessResult(
                hsmMemberGrowthValueClientService.changeGrowthValue(growthValueReqDTO));
    }


    /**
     * 来源类型
     *
     * @return 集合
     */
    @GetMapping(value = "/listSourceType", produces = "application/json;charset=utf-8")
    @ApiOperation("改变成长值")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "改变成长值")
    public Result<List<MemberSourceTypeRespDTO>> listSourceType() {
        return Result.buildSuccessResult(hsmMemberGrowthValueClientService.listSourceType());
    }


    /**
     * 通过条件分页查询
     *
     * @param queryReqDTO 查询条件
     * @return 查询结果
     */
    @PostMapping(value = "/listByCondition", produces = "application/json;charset=utf-8")
    @ApiOperation("通过条件分页查询成长值记录")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "通过条件分页查询成长值记录")
    public Result<Page<MemberGrowthValueListRespDTO>> listByCondition(
            @RequestBody MemberGrowthValueQueryReqDTO queryReqDTO) {
        return Result
                .buildSuccessResult(hsmMemberGrowthValueClientService.listByCondition(queryReqDTO));
    }


}

