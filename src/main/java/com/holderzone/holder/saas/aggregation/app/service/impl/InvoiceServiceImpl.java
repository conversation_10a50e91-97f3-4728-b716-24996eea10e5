package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.app.config.IPassInvoiceConfig;
import com.holderzone.holder.saas.aggregation.app.helper.RedisHelper;
import com.holderzone.holder.saas.aggregation.app.service.InvoiceService;
import com.holderzone.holder.saas.aggregation.app.service.feign.staff.ValidateClientService;
import com.holderzone.holder.saas.aggregation.app.utils.HttpsClientUtils;
import com.holderzone.holder.saas.common.enums.BooleanEnum;
import com.holderzone.saas.store.dto.invoice.*;
import com.holderzone.saas.store.dto.invoice.ipass.*;
import com.holderzone.saas.store.enums.trade.TaxRateTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 开票实现类
 * @className: invoiceServiceImpl
 */
@Slf4j
@Service
@AllArgsConstructor
public class InvoiceServiceImpl implements InvoiceService {

    @Resource
    private IPassInvoiceConfig iPassInvoiceConfig;

    @Resource
    private RedisHelper redisHelper;

    @Resource
    private ValidateClientService validateClientService;

    private static final String ELECTRONIC_INVOICE_KEY = "ELECTRONIC_INVOICE_KEY:";

    @Override
    public ResponseVerifyInvoiceDTO receiptValidate(RequestValidateDTO requestValidateDTO) {
        ResponseVerifyInvoiceDTO invoiceResultDTO = new ResponseVerifyInvoiceDTO();
        invoiceResultDTO.setVerifyCode(BooleanEnum.FALSE.getCode());
        UserContext userContext = UserContextUtils.get();
        log.info("userContext={}", JSON.toJSONString(userContext));
        String storeGuid = requestValidateDTO.getStoreGuid();

        //校验后台是否配置注册人
        UserAuthorityInvoiceDTO userAuthorityDTO;
        String object = redisHelper.get(ELECTRONIC_INVOICE_KEY + requestValidateDTO.getUserGuid());
        log.info("缓存后台配置授权信息={}", JacksonUtils.writeValueAsString(object));
        if (Objects.isNull(object)) {
            userAuthorityDTO = validateClientService.queryUserInvoice(requestValidateDTO.getUserGuid());
            log.info("DB后台配置授权信息={}", JacksonUtils.writeValueAsString(userAuthorityDTO));
            if (Objects.isNull(userAuthorityDTO)) {
                log.info("未配置授权数电发票开票信息，请在管理后台配置数电发票开票信息");
                invoiceResultDTO.setMessageCode(IPassInvoiceConfig.NOT_CONFIGURED_ERROR_CODE);
                return invoiceResultDTO;
            }
        } else {
            String str = (String) JSON.parse(object);
            userAuthorityDTO = JSON.parseObject(str, UserAuthorityInvoiceDTO.class);
        }

        log.info("userAuthorityInvoiceDTO={}", JSON.toJSONString(userAuthorityDTO));
        invoiceResultDTO.setElectronicTaxpayerName(userAuthorityDTO.getElectronicTaxpayerName());
        invoiceResultDTO.setElectronicTaxpayerPhone(userAuthorityDTO.getElectronicTaxpayerPhone());

        //Ipass校验
        IpassVerifyInvoiceQueryDTO ipassVerifyInvoiceQueryDTO = new IpassVerifyInvoiceQueryDTO();
        ipassVerifyInvoiceQueryDTO.setAccount(userAuthorityDTO.getElectronicTaxpayerPhone());
        ipassVerifyInvoiceQueryDTO.setStoreId(storeGuid);

        log.info("Ipass校验开票条件参数={}", JSON.toJSONString(ipassVerifyInvoiceQueryDTO));
        try {
            //刷新额度
            informResidueLimit(requestValidateDTO, storeGuid, userAuthorityDTO);

            String response = HttpsClientUtils.doPost(iPassInvoiceConfig.getReceiptValidate(), JacksonUtils.writeValueAsString(ipassVerifyInvoiceQueryDTO));

            log.info("Ipass校验开票返回返参：{}", response);

            JSONObject jsonObject = JSON.parseObject(response);

            String code = jsonObject.get(IPassInvoiceConfig.CODE).toString();
            if (!code.equals(IPassInvoiceConfig.SUCCESS_CODE)) {
                String message = jsonObject.get(IPassInvoiceConfig.MESSAGE).toString();
                log.info("Ipass校验开票返回异常={}", message);
                invoiceResultDTO.setMessageCode(Integer.parseInt(code));
                return invoiceResultDTO;
            }

            String data = jsonObject.get(IPassInvoiceConfig.DATA).toString();

            IpassVerifyInvoiceResultDTO verifyInvoiceResultDTO = JSON.parseObject(data, IpassVerifyInvoiceResultDTO.class);

            log.info("Ipass校验开票条件结果：{}", verifyInvoiceResultDTO);

            if (verifyInvoiceResultDTO.getIsTaxLogin() == BooleanEnum.FALSE.getCode()) {
                log.info("未登录");
                invoiceResultDTO.setMessageCode(IPassInvoiceConfig.NOT_LOGIN_ERROR_CODE);
                return invoiceResultDTO;
            }

            if (verifyInvoiceResultDTO.getIsTaxAuth() == BooleanEnum.FALSE.getCode()) {
                log.info("未认证");
                invoiceResultDTO.setMessageCode(IPassInvoiceConfig.NOT_AUTHORIZED_ERROR_CODE);
                return invoiceResultDTO;
            }

            if (Objects.nonNull(verifyInvoiceResultDTO.getSysxed())
                    && requestValidateDTO.getOrderAmount().compareTo(new BigDecimal(verifyInvoiceResultDTO.getSysxed())) > 0) {
                log.info("开票金额已达上限");
                invoiceResultDTO.setMessageCode(IPassInvoiceConfig.AMOUNT_LIMIT_ERROR_CODE);
                return invoiceResultDTO;
            }
        } catch (Exception e) {
            log.info("Ipass校验开票条件调用异常={}", e.getMessage());
        }
        invoiceResultDTO.setVerifyCode(BooleanEnum.TRUE.getCode());
        return invoiceResultDTO;
    }

    private void informResidueLimit(RequestValidateDTO requestValidateDTO, String storeGuid, UserAuthorityInvoiceDTO userAuthorityDTO) {
        RequestAuthenticationDTO requestAuthenticationDTO = new RequestAuthenticationDTO();
        requestAuthenticationDTO.setStoreGuid(storeGuid);
        requestAuthenticationDTO.setAccount(userAuthorityDTO.getElectronicTaxpayerPhone());
        requestAuthenticationDTO.setOrderNo(requestValidateDTO.getOrderNo());
        queryResidueLimit(requestAuthenticationDTO);
    }

    @Override
    public Boolean noteLogin(RequestValidateDTO requestValidateDTO) {
        IpassNoteLoginDTO ipassNoteLoginDTO = new IpassNoteLoginDTO();
        ipassNoteLoginDTO.setAccount(requestValidateDTO.getAccount());
        ipassNoteLoginDTO.setStoreId(requestValidateDTO.getStoreGuid());
        ipassNoteLoginDTO.setSms(requestValidateDTO.getSms());
        try {
            log.info("Ipass获取短信验证码参数={}", JSON.toJSONString(ipassNoteLoginDTO));
            //获取短信验证码
            String response = HttpsClientUtils.doPost(iPassInvoiceConfig.getNoteLogin(), JacksonUtils.writeValueAsString(ipassNoteLoginDTO));

            log.info("ipass获取短信验证码返回参数={}", response);

            JSONObject jsonObject = JSON.parseObject(response);

            String code = jsonObject.get(IPassInvoiceConfig.CODE).toString();

            if (code.equals(IPassInvoiceConfig.SMS_SUCCESS_CODE)) {
                log.info("此账号已发送短信验证码={}", requestValidateDTO.getAccount());
                return false;
            } else if (code.equals(IPassInvoiceConfig.SUCCESS_CODE)) {
                log.info("此账号已登录={}", requestValidateDTO.getAccount());
                return true;
            } else {
                String renson = jsonObject.get(IPassInvoiceConfig.MESSAGE).toString();
                log.info("Ipass获取短信验证码异常={}", renson);
                throw new BusinessException(renson);
            }
        } catch (Exception e) {
            log.info("Ipass获取短信验证码返回异常={}", e.getMessage());
            throw new BusinessException(e.getMessage());
        }
    }

    @Override
    public ResponseAuthenticationUrlDTO authenticationUrl(RequestAuthenticationDTO requestAuthenticationDTO) {
        ResponseAuthenticationUrlDTO responseAuthenticationUrlDTO = new ResponseAuthenticationUrlDTO();
        try {
            IpassAuthenticationUrlDTO ipassAuthenticationUrlDTO = new IpassAuthenticationUrlDTO();
            ipassAuthenticationUrlDTO.setAccount(requestAuthenticationDTO.getAccount());
            ipassAuthenticationUrlDTO.setEwmlx(String.valueOf(requestAuthenticationDTO.getTaxType()));
            ipassAuthenticationUrlDTO.setOrderNo(requestAuthenticationDTO.getOrderNo());
            ipassAuthenticationUrlDTO.setStoreId(requestAuthenticationDTO.getStoreGuid());
            log.info("生成认证二维码参数={}", JSON.toJSONString(ipassAuthenticationUrlDTO));
            //生成认证二维码
            String response = HttpsClientUtils.doPost(iPassInvoiceConfig.getAuthenticationUrl(), JacksonUtils.writeValueAsString(ipassAuthenticationUrlDTO));

            log.info("生成认证二维码返回参数={}", response);

            JSONObject jsonObject = JSON.parseObject(response);

            String code = jsonObject.get(IPassInvoiceConfig.CODE).toString();
            if (!code.equals(IPassInvoiceConfig.SUCCESS_CODE)) {
                String renson = jsonObject.get(IPassInvoiceConfig.MESSAGE).toString();
                log.info("生成认证二维码异常={}", renson);
                responseAuthenticationUrlDTO.setErrorMessage(renson);
                responseAuthenticationUrlDTO.setStatus(IPassInvoiceConfig.FAIL_CODE);
                return responseAuthenticationUrlDTO;
            }

            String data = jsonObject.get(IPassInvoiceConfig.DATA).toString();

            IpassAuthenticationResultUrlDTO ipassAuthenticationResultUrlDTO = JSON.parseObject(data, IpassAuthenticationResultUrlDTO.class);

            responseAuthenticationUrlDTO.setStatus(ipassAuthenticationResultUrlDTO.getStatus());
            responseAuthenticationUrlDTO.setCode(ipassAuthenticationResultUrlDTO.getEwm());
            responseAuthenticationUrlDTO.setStoreId(ipassAuthenticationResultUrlDTO.getStoreId());
            return responseAuthenticationUrlDTO;
        } catch (Exception e) {
            log.info("生成认证二维码异常={}", e.getMessage());
            responseAuthenticationUrlDTO.setStatus(IPassInvoiceConfig.FAIL_CODE);
            responseAuthenticationUrlDTO.setErrorMessage("第三方接口调用超时");
        }
        return responseAuthenticationUrlDTO;
    }

    @Override
    public ResponseQueryAuthDTO queryAuthResult(RequestAuthenticationDTO requestAuthenticationDTO) {
        ResponseQueryAuthDTO responseQueryAuthDTO = new ResponseQueryAuthDTO();
        try {
            IpassQueryAuthResultDTO ipassQueryAuthResultDTO = new IpassQueryAuthResultDTO();
            ipassQueryAuthResultDTO.setStoreId(requestAuthenticationDTO.getStoreGuid());
            ipassQueryAuthResultDTO.setOrderNo(requestAuthenticationDTO.getOrderNo());
            ipassQueryAuthResultDTO.setAccount(requestAuthenticationDTO.getAccount());
            log.info("通知认证查询参数={}", JSON.toJSONString(ipassQueryAuthResultDTO));
            //生成认证二维码
            String response = HttpsClientUtils.doPost(iPassInvoiceConfig.getAuthResult(), JacksonUtils.writeValueAsString(ipassQueryAuthResultDTO));

            log.info("通知认证查询参数={}", response);

            JSONObject jsonObject = JSON.parseObject(response);

            String code = jsonObject.get(IPassInvoiceConfig.CODE).toString();
            if (!code.equals(IPassInvoiceConfig.SUCCESS_CODE)) {
                String renson = jsonObject.get(IPassInvoiceConfig.MESSAGE).toString();
                log.info("通知认证查询异常={}", renson);
                responseQueryAuthDTO.setStatus(IPassInvoiceConfig.FAIL_CODE);
                responseQueryAuthDTO.setErrorMessage(renson);
                return responseQueryAuthDTO;
            }

            //查询认证结果
            responseQueryAuthDTO = getQueryAuthDTO(requestAuthenticationDTO);
        } catch (Exception e) {
            log.info("查询认证调用异常={}", e.getMessage());
        }
        return responseQueryAuthDTO;
    }

    @Override
    public String generateOrderInvoice(RequestValidateDTO requestAuthenticationDTO) {
        log.info("调用生成订单发票二维码入参={}", JacksonUtils.writeValueAsString(requestAuthenticationDTO));

        if (StringUtils.isEmpty(requestAuthenticationDTO.getAccount())) {
            return null;
        }
        try {
            IpassGenerateOrderInvoiceDTO generateOrderInvoiceDTO = getIpassGenerateOrderInvoiceDTO(requestAuthenticationDTO);

            log.info("生成订单发票二维码参数={}", JSON.toJSONString(generateOrderInvoiceDTO));
            //生成认证二维码
            String response = HttpsClientUtils.doPost(iPassInvoiceConfig.getOrderInvoiceUrl(), JacksonUtils.writeValueAsString(generateOrderInvoiceDTO));

            log.info("生成订单发票二维码返回参数={}", response);

            JSONObject jsonObject = JSON.parseObject(response);

            String code = jsonObject.get(IPassInvoiceConfig.CODE).toString();
            if (!code.equals(IPassInvoiceConfig.SUCCESS_CODE)) {
                String message = jsonObject.get(IPassInvoiceConfig.MESSAGE).toString();
                log.info("生成订单发票二维码异常={}", message);
            } else {
                String data = jsonObject.get(IPassInvoiceConfig.DATA).toString();
                log.info("订单号={} 发票二维码={}", requestAuthenticationDTO.getOrderNo(), data);
                return data;
            }
        } catch (Exception e) {
            log.info("生成订单发票二维码调用异常={}", e.getMessage());
        }
        return null;
    }

    private static IpassGenerateOrderInvoiceDTO getIpassGenerateOrderInvoiceDTO(RequestValidateDTO requestAuthenticationDTO) {
        IpassGenerateOrderInvoiceDTO generateOrderInvoiceDTO = new IpassGenerateOrderInvoiceDTO();
        generateOrderInvoiceDTO.setStoreId(requestAuthenticationDTO.getStoreGuid());
        generateOrderInvoiceDTO.setAccount(requestAuthenticationDTO.getAccount());
        generateOrderInvoiceDTO.setOrderNo(requestAuthenticationDTO.getOrderNo());

        generateOrderInvoiceDTO.setKpr(requestAuthenticationDTO.getAccountName());
        generateOrderInvoiceDTO.setSqr(requestAuthenticationDTO.getAccountName());

        generateOrderInvoiceDTO.setJshj(String.valueOf(requestAuthenticationDTO.getOrderAmount()));
        generateOrderInvoiceDTO.setFplxdm("02");

        List<IpassOrderItemsDTO> ipassOrderItemsDTOS = getIpassOrderItemsDTOS(requestAuthenticationDTO);

        generateOrderInvoiceDTO.setOrderItems(ipassOrderItemsDTOS);
        return generateOrderInvoiceDTO;
    }

    private static List<IpassOrderItemsDTO> getIpassOrderItemsDTOS(RequestValidateDTO requestAuthenticationDTO) {
        List<IpassOrderItemsDTO> ipassOrderItemsDTOS = new ArrayList<>();
        IpassOrderItemsDTO ipassOrderItemsDTO = new IpassOrderItemsDTO();

        ipassOrderItemsDTO.setDj(String.valueOf(requestAuthenticationDTO.getOrderAmount()));
        ipassOrderItemsDTO.setJe(String.valueOf(requestAuthenticationDTO.getOrderAmount()));
        ipassOrderItemsDTO.setLslbs(String.valueOf(TaxRateTypeEnum.NORMAL_TAX_RATE.getCode()));
        ipassOrderItemsDTO.setSl("1");
        ipassOrderItemsDTO.setSpbm(IPassInvoiceConfig.CATERING_CODE);
        ipassOrderItemsDTO.setSpmc(IPassInvoiceConfig.CATERING_NAME);
        ipassOrderItemsDTOS.add(ipassOrderItemsDTO);
        return ipassOrderItemsDTOS;
    }

    private void queryResidueLimit(RequestAuthenticationDTO requestAuthenticationDTO) {
        log.info("查询剩余额度开始={}", JacksonUtils.writeValueAsString(requestAuthenticationDTO));
        try {
            IpassQueryAuthResultDTO ipassQueryAuthResultDTO = new IpassQueryAuthResultDTO();
            ipassQueryAuthResultDTO.setStoreId(requestAuthenticationDTO.getStoreGuid());
            ipassQueryAuthResultDTO.setOrderNo(String.valueOf(System.currentTimeMillis()));
            ipassQueryAuthResultDTO.setAccount(requestAuthenticationDTO.getAccount());

            log.info("查询剩余额度参数={}", JSON.toJSONString(ipassQueryAuthResultDTO));
            String response = HttpsClientUtils.doPost(iPassInvoiceConfig.getQueryResidueLimit(), JacksonUtils.writeValueAsString(ipassQueryAuthResultDTO));

            log.info("查询剩余额度返参数={}", response);

            JSONObject jsonObject = JSON.parseObject(response);

            String code = jsonObject.get(IPassInvoiceConfig.CODE).toString();
            if (!code.equals(IPassInvoiceConfig.SUCCESS_CODE)) {
                String renson = jsonObject.get(IPassInvoiceConfig.MESSAGE).toString();
                log.info("查询剩余额度异常={}", renson);
            }
        } catch (Exception e) {
            log.info("查询剩余额度调用异常={}", e.getMessage());
        }
    }

    /**
     * 查询认证结果
     */
    private ResponseQueryAuthDTO getQueryAuthDTO(RequestAuthenticationDTO requestAuthenticationDTO) {
        ResponseQueryAuthDTO responseQueryAuthDTO = new ResponseQueryAuthDTO();

        IpassVerifyInvoiceQueryDTO ipassVerifyInvoiceQueryDTO = new IpassVerifyInvoiceQueryDTO();
        ipassVerifyInvoiceQueryDTO.setAccount(requestAuthenticationDTO.getAccount());
        ipassVerifyInvoiceQueryDTO.setStoreId(requestAuthenticationDTO.getStoreGuid());

        log.info("查询认证结果参数={}", JSON.toJSONString(ipassVerifyInvoiceQueryDTO));
        String verifyResponse = HttpsClientUtils.doPost(iPassInvoiceConfig.getReceiptValidate(), JacksonUtils.writeValueAsString(ipassVerifyInvoiceQueryDTO));

        log.info("查询认证结果返参：{}", verifyResponse);


        JSONObject verifyObject = JSON.parseObject(verifyResponse);

        String verifyCode = verifyObject.get(IPassInvoiceConfig.CODE).toString();
        if (!verifyCode.equals(IPassInvoiceConfig.SUCCESS_CODE)) {
            String message = verifyObject.get(IPassInvoiceConfig.MESSAGE).toString();
            log.info("查询认证结果返回异常={}", message);
            responseQueryAuthDTO.setStatus(IPassInvoiceConfig.FAIL_CODE);
            responseQueryAuthDTO.setErrorMessage(message);
            return responseQueryAuthDTO;
        }

        String data = verifyObject.get(IPassInvoiceConfig.DATA).toString();

        IpassVerifyInvoiceResultDTO verifyInvoiceResultDTO = JSON.parseObject(data, IpassVerifyInvoiceResultDTO.class);

        log.info("Ipass校验开票条件结果：{}", verifyInvoiceResultDTO);


        if (verifyInvoiceResultDTO.getIsTaxAuth() == BooleanEnum.FALSE.getCode()) {
            responseQueryAuthDTO.setStatus(IPassInvoiceConfig.FAIL_CODE);
        } else {
            responseQueryAuthDTO.setStatus(IPassInvoiceConfig.SUCCESS_CODE);
        }
        return responseQueryAuthDTO;
    }
}
