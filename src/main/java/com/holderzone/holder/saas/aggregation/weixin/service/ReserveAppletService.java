package com.holderzone.holder.saas.aggregation.weixin.service;


import com.holderzone.saas.store.reserve.api.dto.ReserveAvailableStoreConfigDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveAvailableStoreDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveAppletQueryDTO;

import java.util.List;

/**
 * 小程序
 * 预订
 */
public interface ReserveAppletService {

    /**
     * 查询可预订的门店列表
     */
    List<ReserveAvailableStoreDTO> getAvailableStoreList(ReserveAppletQueryDTO queryDTO);

    /**
     * 查询可预订的门店详情
     */
    ReserveAvailableStoreConfigDTO getAvailableStore(ReserveAppletQueryDTO queryDTO);

    /**
     * 发起预订
     */
    String launch(ReserveRecordDTO reserveRecordDTO);
}
