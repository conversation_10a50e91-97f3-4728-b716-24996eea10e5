package com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.weixin.WxCategoryDTO;
import com.holderzone.saas.store.dto.weixin.WxTableStickDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickDownloadReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickIsModelDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickModelReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickDownloadRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickModelRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxTableStickClientService
 * @date 2019/03/06 10:38
 * @description 微信桌贴client
 * @program holder-saas-store
 */
@Component
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = WxTableStickClientService.FallBackService.class)
public interface WxTableStickClientService {

    @PostMapping("/wx_table_stick/list_model_and_stick")
    List<WxTableStickDTO> listModelAndTicket(WxStickIsModelDTO wxStickIsModelDTO);

    @PostMapping("/wx_table_stick/save_or_update_stick")
    Boolean saveOrUpdateStick(WxTableStickDTO wxTableStickDTO);

    @PostMapping("/wx_table_stick/find_by_guid")
    WxTableStickDTO findByGuid(String guid);

    @PostMapping("/wx_table_stick/update_stick")
    Boolean updateTableStick(WxTableStickDTO wxTableStickDTO);

    @PostMapping("/wx_table_stick/list_stick_model")
    List<WxStickModelRespDTO> listStickModel(WxStickModelReqDTO wxStickModelReqDTO);

    @PostMapping("/wx_table_stick/list_stick_category")
    List<WxCategoryDTO> listStickCategory();

    @PostMapping("/wx_table_stick/download_stick_zip")
    WxStickDownloadRespDTO downloadStickZip(String downloadKey);

    @PostMapping("/wx_table_stick/create_stick_zip")
    String createStickZip(WxStickDownloadReqDTO wxStickDownloadReqDTO);

    @PostMapping("/wx_table_stick/delete_my_stick")
    Boolean deleteMyStick(WxStickIsModelDTO wxStickIsModelDTO);

    @Component
    @Slf4j
    class FallBackService implements FallbackFactory<WxTableStickClientService> {
        @Override
        public WxTableStickClientService create(Throwable throwable) {
            return new WxTableStickClientService() {
                @Override
                public List<WxTableStickDTO> listModelAndTicket(WxStickIsModelDTO wxStickIsModelDTO) {
                    log.info("商户聚合层：微信桌贴/模板查询出现异常，message:{}", throwable.getMessage());
                    throw new BusinessException("微信桌贴/模板查询出现异常,{}", throwable);
                }

                @Override
                public Boolean saveOrUpdateStick(WxTableStickDTO wxTableStickDTO) {
                    log.info("商户聚合层：创建微信桌贴时出现异常，message:{}", throwable.getMessage());
                    throw new BusinessException("创建微信桌贴时出现异常,{}", throwable);
                }

                @Override
                public WxTableStickDTO findByGuid(String guid) {
                    log.info("商户聚合层：调用查询桌贴接口出现异常，message:{}", throwable.getMessage());
                    throw new BusinessException("调用查询桌贴接口出现异常,{}", throwable);
                }

                @Override
                public Boolean updateTableStick(WxTableStickDTO wxTableStickDTO) {
                    log.info("商户聚合层：调用更新桌贴接口出现异常，message:{}", throwable.getMessage());
                    throw new BusinessException("调用更新桌贴接口出现异常,{}", throwable);
                }

                @Override
                public List<WxStickModelRespDTO> listStickModel(WxStickModelReqDTO wxStickModelReqDTO) {
                    log.info("商户聚合层：调用查询模板库接口出现异常，message:{}", throwable.getMessage());
                    throw new BusinessException("调用查询模板库接口出现异常,{}", throwable);
                }

                @Override
                public List<WxCategoryDTO> listStickCategory() {
                    log.info("商户聚合层：调用查询模板分类接口出现异常，message:{}", throwable.getMessage());
                    throw new BusinessException("调用查询模板分类接口出现异常,{}", throwable);
                }

                @Override
                public WxStickDownloadRespDTO downloadStickZip(String downloadKey) {
                    log.info("商户聚合层：调用桌贴下载接口出现异常，message:{}", throwable.getMessage());
                    throw new BusinessException("调用桌贴下载接口出现异常,{}", throwable);
                }

                @Override
                public String createStickZip(WxStickDownloadReqDTO wxStickDownloadReqDTO) {
                    log.info("商户聚合层：调用桌贴创建接口出现异常，message:{}", throwable.getMessage());
                    throw new BusinessException("调用桌贴创建接口出现异常,{}", throwable);
                }

                @Override
                public Boolean deleteMyStick(WxStickIsModelDTO wxStickIsModelDTO) {
                    log.info("商户聚合层：调用桌贴删除接口出现异常，message:{}", throwable.getMessage());
                    throw new BusinessException("调用桌贴删除接口出现异常,{}", throwable);
                }
            };
        }
    }
}
