package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.StorePayClientService;
import com.holderzone.saas.store.dto.pay.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/agg")
@Api(tags = "聚合支付统一轮询入口")
public class WxAggPayController {

    private final StorePayClientService storePayClientService;

    @PostMapping("/h5/polling")
    @ApiOperation("轮询接口：看实体中paySt的描述，付款中则需要继续轮询")
    public Result<AggPayPollingRespDTO> polling(@RequestBody SaasPollingDTO saasPollingDTO) {
        log.info("轮询接口 saasPollingDTO:{}", JacksonUtils.writeValueAsString(saasPollingDTO));
        AggPayPollingRespDTO pollingRespDTO = storePayClientService.h5Polling(saasPollingDTO);
        return Result.buildSuccessResult(pollingRespDTO);
    }
}
