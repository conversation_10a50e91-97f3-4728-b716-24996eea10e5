package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.baseresource;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.dto.baseresource.request.HsmBrandReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.response.HsmBrandRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HsmBrandService
 * @date 2019/05/31 14:09
 * @description 品牌信息表
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-member-account", fallbackFactory = HsmBrandService.HsmBrandServiceFallBack.class)
public interface HsmBrandService {

    /**
     * 同步品牌
     *
     * @param hsmBrandReqDTO 列表
     */
    @ApiOperation("同步品牌")
    @PostMapping(value = "/hsm/brand/syc")
    HsmBrandRespDTO syc(@RequestBody HsmBrandReqDTO hsmBrandReqDTO);

    /**
     * 同步品牌
     *
     * @param hsmBrandReqDTOS 列表
     */
    @ApiOperation("同步品牌")
    @PostMapping(value = "/hsm/brand/syc/list")
    List<HsmBrandRespDTO> syc(@RequestBody List<HsmBrandReqDTO> hsmBrandReqDTOS);


    /**
     * 通过企业Guid获取品牌列表
     *
     * @param enterpriseGuid 企业Guid
     * @return 品牌列表
     */
    @ApiOperation("通过企业Guid获取品牌列表")
    @PostMapping(value = "/hsm/brand/enterprise/{enterpriseGuid}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    List<HsmBrandRespDTO> findByEnterpriseGuid(
        @PathVariable("enterpriseGuid") String enterpriseGuid);

    /**
     * 通过企业Guid获取品牌列表,不要参数
     *
     * @return 品牌列表
     */
    @ApiOperation("通过企业Guid获取品牌列表，不要参数")
    @PostMapping("/hsm/brand/enterprise")
    List<HsmBrandRespDTO> findByEnterpriseGuid();

    /**
     * <AUTHOR>
     *  根据brandKey和allianceid删除品牌
     * @param brandKey 外部商家对应的品牌主键
     * @param allianceid 联盟ID
     * @return
     */
    @DeleteMapping("/hsm/brand//{brandKey}/{allianceid}")
    boolean removeHsmBrand(@PathVariable("brandKey") String brandKey,@PathVariable("allianceid") String allianceid);

    /**
     * <AUTHOR>
     * 保存品牌
     * @param hsmBrandReqDTOS
     * @return
     */
    @PostMapping("/hsm/brand//save")
    boolean saveHsmBrand(@RequestBody  HsmBrandReqDTO hsmBrandReqDTOS) ;

    /**
     * <AUTHOR>
     * 修改品牌
     * @param hsmBrandReqDTOS
     * @return
     */
    @PostMapping("/hsm/brand//modify")
    boolean modifyHsmBrand(@RequestBody  HsmBrandReqDTO hsmBrandReqDTOS);

    @Slf4j
    @Component
    class HsmBrandServiceFallBack implements FallbackFactory<HsmBrandService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public HsmBrandService create(Throwable throwable) {
            return new HsmBrandService() {
                @Override
                public HsmBrandRespDTO syc(HsmBrandReqDTO hsmBrandReqDTO) {
                    log.error(HYSTRIX_PATTERN, "syc",
                        JSONObject.toJSON(hsmBrandReqDTO), ThrowableUtils
                            .asString(throwable));
                    throw new BusinessException("同步品牌数据失败");
                }

                @Override
                public List<HsmBrandRespDTO> syc(List<HsmBrandReqDTO> hsmBrandReqDTOS) {
                    log.error(HYSTRIX_PATTERN, "syc",
                        JSONObject.toJSON(hsmBrandReqDTOS), ThrowableUtils
                            .asString(throwable));
                    throw new BusinessException("同步品牌数据失败");

                }

                @Override
                public List<HsmBrandRespDTO> findByEnterpriseGuid(String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN, "findByEnterpriseGuid",
                        enterpriseGuid, ThrowableUtils
                            .asString(throwable));
                    throw new BusinessException("通过企业Guid获取品牌列表失败");
                }

                @Override
                public List<HsmBrandRespDTO> findByEnterpriseGuid() {
                    log.error(HYSTRIX_PATTERN, "findByEnterpriseGuid",
                            "", ThrowableUtils
                                    .asString(throwable));
                    throw new BusinessException("不要参数获取品牌列表失败");
                }

                @Override
                public boolean removeHsmBrand(String brandKey, String allianceid) {
                    log.error(HYSTRIX_PATTERN, "removeHsmBrand",
                            brandKey+","+allianceid, ThrowableUtils
                                    .asString(throwable));
                    throw new BusinessException("删除品牌失败");
                }

                @Override
                public boolean saveHsmBrand(HsmBrandReqDTO hsmBrandReqDTOS) {
                    log.error(HYSTRIX_PATTERN, "saveHsmBrand",
                            JSONObject.toJSON(hsmBrandReqDTOS), ThrowableUtils
                                    .asString(throwable));
                    throw new BusinessException("保存品牌数据失败");
                }

                @Override
                public boolean modifyHsmBrand(HsmBrandReqDTO hsmBrandReqDTOS) {
                    log.error(HYSTRIX_PATTERN, "modifyHsmBrand",
                            JSONObject.toJSON(hsmBrandReqDTOS), ThrowableUtils
                                    .asString(throwable));
                    throw new BusinessException("修改品牌数据失败");
                }
            };
        }
    }

}
