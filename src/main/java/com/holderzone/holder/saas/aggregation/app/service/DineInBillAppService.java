package com.holderzone.holder.saas.aggregation.app.service;

import com.holderzone.saas.store.dto.order.request.bill.BilMemberCardCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;

public interface DineInBillAppService {

    DineinOrderDetailRespDTO getMemberCardAndCalculate(BilMemberCardCalculateReqDTO reqDTO);

    DineinOrderDetailRespDTO switchoverMemberAndCalculate(BilMemberCardCalculateReqDTO reqDTO);

    DineinOrderDetailRespDTO calculate(BillCalculateReqDTO billCalculateReqDTO);
}
