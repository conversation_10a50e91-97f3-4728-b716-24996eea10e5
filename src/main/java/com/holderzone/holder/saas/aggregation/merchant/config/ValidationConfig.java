package com.holderzone.holder.saas.aggregation.merchant.config;

import org.hibernate.validator.HibernateValidator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ValidationConfig
 * @date 2018/11/05 下午4:45
 * @description //TODO
 * @program holder-saas-aggregation-merchant
 */
@Configuration
public class ValidationConfig {

    @Bean
    public Validator validator(){
        ValidatorFactory validatorFactory = Validation.byProvider(HibernateValidator.class).configure().failFast(true).buildValidatorFactory();
        return validatorFactory.getValidator();
    }
}
