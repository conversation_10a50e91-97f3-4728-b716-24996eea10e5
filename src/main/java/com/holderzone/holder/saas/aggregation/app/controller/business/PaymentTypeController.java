package com.holderzone.holder.saas.aggregation.app.controller.business;

import cn.hutool.core.collection.CollectionUtil;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.business.PaymentTypeClientService;
import com.holderzone.saas.store.dto.trade.PaymentTypeDTO;
import com.holderzone.saas.store.dto.trade.PaymentTypeQueryDTO;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentTypeController
 * @date 2018/09/10 16:08
 * @description
 * @program holder-saas-aggregation-app
 */
@Api(value = "支付方式接口")
@RestController
@RequestMapping("/pay/type")
public class PaymentTypeController {

    private static final Logger logger = LoggerFactory.getLogger(PaymentTypeController.class);

    private final PaymentTypeClientService paymentTypeClientService;

    @Autowired
    public PaymentTypeController(PaymentTypeClientService paymentTypeClientService) {
        this.paymentTypeClientService = paymentTypeClientService;
    }

    @ApiOperation(value = "如果新增其他支付方式，sorting自动自增1,type自增+1")
    @PostMapping("/add")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "新增支付方式",action = OperatorType.ADD)
    public Result<String> addPaymentType(@RequestBody PaymentTypeDTO paymentTypeDTO) {
        logger.info("新增支付方式：paymentTypeDTO={}", JacksonUtils.writeValueAsString(paymentTypeDTO));
        return Result.buildSuccessResult(paymentTypeClientService.add(paymentTypeDTO));
    }

    @ApiOperation(value = "更新支付方式")
    @PostMapping("/update")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "更新支付方式",action = OperatorType.UPDATE)
    public Result<String> updatePaymentType(@RequestBody PaymentTypeDTO paymentTypeDTO) {
        logger.info("更新支付方式：paymentTypeDTO={}", JacksonUtils.writeValueAsString(paymentTypeDTO));
        return Result.buildSuccessResult(paymentTypeClientService.update(paymentTypeDTO));
    }

    @ApiOperation(value = "支付方式排序", notes = "将整个表单提交")
    @PostMapping("/sort")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "支付方式排序",action = OperatorType.UPDATE)
    public Result<String> sorting(@RequestBody List<PaymentTypeDTO> paymentTypeDTOS) {
        logger.info("更新排序，paymentTypeDTOS={}", JacksonUtils.writeValueAsString(paymentTypeDTOS));
        return Result.buildSuccessResult(paymentTypeClientService.sort(paymentTypeDTOS));
    }


    @ApiOperation(value = "删除支付方式,传入guid，paymentType，storeGuid", notes = "返回success表示成功")
    @PostMapping("/delete")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "删除支付方式",action = OperatorType.DELETE)
    public Result<String> deletePaymentType(@RequestBody PaymentTypeDTO paymentTypeDTO) {
        logger.info("查询所有支付方式paymentTypeDTO={}", JacksonUtils.writeValueAsString(paymentTypeDTO));
        String result = paymentTypeClientService.deletePaymentType(paymentTypeDTO);
        return Result.buildSuccessResult(result);
    }

    @ApiOperation(value = "查询所有支付方式")
    @PostMapping("/getAll")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "查询所有支付方式",action = OperatorType.SELECT)
    public Result<List<PaymentTypeDTO>> getAll(@RequestBody PaymentTypeQueryDTO paymentTypeQueryDTO) {
        logger.info("查询所有支付方式 paymentTypeQueryDTO={}", JacksonUtils.writeValueAsString(paymentTypeQueryDTO));
        List<PaymentTypeDTO> all = paymentTypeClientService.getAll(paymentTypeQueryDTO);
        if(CollectionUtil.isNotEmpty(all)){
            all.forEach(p ->{
                if(p.getState() == 1){
                    return;
                }
                String localeName = PaymentTypeEnum.PaymentType.getLocaleNameById(p.getPaymentType());
                if (StringUtils.isEmpty(localeName)) {
                    return;
                }
                p.setPaymentTypeName(localeName);
            });
        }
        // 快餐显示麓豆支付
        if (!Objects.equals(TradeModeEnum.FAST.getCode(), paymentTypeQueryDTO.getTradeMode())) {
            all.removeIf(e -> PaymentTypeEnum.LUDOU_MEMBER_PAY.getCode() == e.getPaymentType());
        }
        return Result.buildSuccessResult(all);
    }

    @ApiOperation(value = "查询所有支付方式:修改了原接口返回值格式")
    @PostMapping("/list")
    public Result<List<PaymentTypeDTO>> getAllNew(@RequestBody PaymentTypeQueryDTO paymentTypeQueryDTO) {
        logger.info("查询所有支付方式 paymentTypeQueryDTO={}", JacksonUtils.writeValueAsString(paymentTypeQueryDTO));
        List<PaymentTypeDTO> all = paymentTypeClientService.getAll(paymentTypeQueryDTO);
        if(!CollectionUtils.isEmpty(all)){
            PaymentTypeDTO paymentTypeOfCanteen = null;
            List<PaymentTypeDTO> paymentTypeOfCanteenChildren = new ArrayList<>(2);
            for (PaymentTypeDTO paymentTypeDTO : all) {
                //父
                if(paymentTypeDTO.getPaymentType() == 9){
                    paymentTypeOfCanteen = paymentTypeDTO;
                }
                //子
                if(paymentTypeDTO.getPaymentType() == PaymentTypeEnum.CANTEEN_ELECTRONIC_CARD.getCode() ||
                        paymentTypeDTO.getPaymentType() == PaymentTypeEnum.CANTEEN_PHYSICAL_CARD.getCode()){
                    paymentTypeOfCanteenChildren.add(paymentTypeDTO);
                }
            }
            Optional.ofNullable(paymentTypeOfCanteen).ifPresent(parent -> {
                parent.setChildren(paymentTypeOfCanteenChildren);
                all.removeAll(paymentTypeOfCanteenChildren);
            });
        }
        return Result.buildSuccessResult(all);
    }
}
