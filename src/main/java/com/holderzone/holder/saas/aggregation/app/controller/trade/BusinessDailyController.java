package com.holderzone.holder.saas.aggregation.app.controller.trade;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.entity.OrderLocaleEnum;
import com.holderzone.holder.saas.aggregation.app.entity.auth.DiningTypeSaleDTO;
import com.holderzone.holder.saas.aggregation.app.entity.auth.GatherSaleDTO;
import com.holderzone.holder.saas.aggregation.app.entity.auth.OverviewSaleDTO;
import com.holderzone.holder.saas.aggregation.app.entity.auth.RefundSaleDTO;
import com.holderzone.holder.saas.aggregation.app.service.BusinessDailyPrintService;
import com.holderzone.holder.saas.aggregation.app.service.BusinessDailyService;
import com.holderzone.saas.store.dto.order.request.daily.DailyPrintReqDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.*;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessDailyController
 * @date 2019/02/15 10:04
 * @description
 * @program holder-saas-aggregation-app
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/business_daily")
@Api(description = "营业日报接口")
@Slf4j
public class BusinessDailyController {

    private final BusinessDailyService businessDailyService;

    private final BusinessDailyPrintService businessDailyPrintService;

    @ApiOperation(value = "营业日报打印", notes = "营业日报打印")
    @PostMapping("print")
    public Result<String> print(@RequestBody @Valid DailyPrintReqDTO request) {
        log.info("营业日报打印入参：{}", JacksonUtils.writeValueAsString(request));
        return businessDailyPrintService.print(request);
    }

    /**
     * old
     */
    @ApiOperation(value = "营业概况", notes = "营业概况")
    @PostMapping("overview")
    public Result<OverviewRespDTO> overview(@RequestBody @Valid DailyReqDTO request) {
        if (log.isInfoEnabled()) {
            log.info("营业概况入参：{}", JacksonUtils.writeValueAsString(request));
        }
        OverviewRespDTO overview = businessDailyService.overview(request);
        if (overview == null) {
            return Result.buildSuccessResult(null);
        }
        if (CollectionUtils.isNotEmpty(overview.getDiscountItems())) {
            overview.getDiscountItems().forEach(d -> d.setName(DiscountTypeEnum.getLocaleDescByName(d.getName())));
        }
        if (CollectionUtils.isNotEmpty(overview.getGatherItems())) {
            overview.getGatherItems().forEach(d -> d.setName(PaymentTypeEnum.PaymentType.getLocaleName(d.getName())));
        }
        return Result.buildSuccessResult(overview);
    }

    /**
     * new 受权限控制
     */
    @ApiOperation(value = "营业概况", notes = "营业概况")
    @PostMapping("/overview_sale")
    public Result<OverviewSaleDTO> overviewSale(@RequestBody @Valid DailyReqDTO request) {
        if (log.isInfoEnabled()) {
            log.info("营业概况入参：{}", JacksonUtils.writeValueAsString(request));
        }
        OverviewSaleDTO overviewSaleDTO = businessDailyService.overviewSale(request);
        if (overviewSaleDTO == null) {
            return Result.buildSuccessResult(null);
        }
        if (CollectionUtils.isNotEmpty(overviewSaleDTO.getDiscountItems())) {
            overviewSaleDTO.getDiscountItems().forEach(d -> d.setName(DiscountTypeEnum.getLocaleDescByName(d.getName())));
        }
        if (CollectionUtils.isNotEmpty(overviewSaleDTO.getGatherItems())) {
            overviewSaleDTO.getGatherItems().forEach(d -> d.setName(PaymentTypeEnum.PaymentType.getLocaleName(d.getName())));
        }
        return Result.buildSuccessResult(overviewSaleDTO);
    }


    /**
     * old
     */
    @ApiOperation(value = "收款统计", notes = "收款统计")
    @PostMapping("gather")
    public Result<List<GatherRespDTO>> gather(@RequestBody @Valid DailyReqDTO request) {
        if (log.isInfoEnabled()) {
            log.info("收款统计入参：{}", JacksonUtils.writeValueAsString(request));
        }
        List<GatherRespDTO> gather = businessDailyService.gather(request);
        if (CollectionUtils.isNotEmpty(gather)) {
            gather.forEach(g -> {
                String localeName = PaymentTypeEnum.PaymentType.getLocaleName(g.getGatherName());
                if (StringUtils.isNotEmpty(localeName)) {
                    g.setGatherName(localeName);
                }
            });
        }
        return Result.buildSuccessResult(gather);
    }

    /**
     * new 受权限控制
     */
    @ApiOperation(value = "收款统计", notes = "收款统计")
    @PostMapping("gather_sale")
    public Result<List<GatherSaleDTO>> gatherSale(@RequestBody @Valid DailyReqDTO request) {
        if (log.isInfoEnabled()) {
            log.info("收款统计入参：{}", JacksonUtils.writeValueAsString(request));
        }
        List<GatherSaleDTO> gatherSaleDTOList = businessDailyService.gatherSale(request);
        if (CollectionUtils.isNotEmpty(gatherSaleDTOList)) {
            gatherSaleDTOList.forEach(g -> {
                String localeName = PaymentTypeEnum.PaymentType.getLocaleName(g.getGatherName());
                if (StringUtils.isNotEmpty(localeName)) {
                    g.setGatherName(localeName);
                }
            });
        }
        return Result.buildSuccessResult(gatherSaleDTOList);
    }


    /**
     * old
     */
    @ApiOperation(value = "用餐类型统计", notes = "用餐类型统计")
    @PostMapping("dining_type")
    public Result<List<DiningTypeRespDTO>> diningType(@RequestBody @Valid DailyReqDTO request) {
        if (log.isInfoEnabled()) {
            log.info("用餐类型统计入参：{}", JacksonUtils.writeValueAsString(request));
        }
        List<DiningTypeRespDTO> list = businessDailyService.diningType(request);
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(a -> {
                a.setTypeName(OrderLocaleEnum.getTradeModeLocale(a.getTypeName()));
                if (CollectionUtils.isNotEmpty(a.getSubDiningTypes())) {
                    a.getSubDiningTypes().forEach(s -> s.setTypeName(OrderLocaleEnum.getTradeModeLocale(s.getTypeName())));
                }
            });
        }
        return Result.buildSuccessResult(list);
    }

    /**
     * new 受权限控制
     */
    @ApiOperation(value = "用餐类型统计", notes = "用餐类型统计")
    @PostMapping("/dining_type_sale")
    public Result<List<DiningTypeSaleDTO>> diningTypeSale(@RequestBody @Valid DailyReqDTO request) {
        if (log.isInfoEnabled()) {
            log.info("用餐类型统计入参：{}", JacksonUtils.writeValueAsString(request));
        }
        List<DiningTypeSaleDTO> list = businessDailyService.diningTypeSale(request);
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(a -> {
                a.setTypeName(OrderLocaleEnum.getTradeModeLocale(a.getTypeName()));
                if (CollectionUtils.isNotEmpty(a.getSubDiningTypes())) {
                    a.getSubDiningTypes().forEach(s -> s.setTypeName(OrderLocaleEnum.getTradeModeLocale(s.getTypeName())));
                }
            });
        }
        return Result.buildSuccessResult(list);
    }

    /**
     * old
     */
    @ApiOperation(value = "退款统计报表", notes = "退款统计报表")
    @PostMapping("refund")
    public Result<RefundRespDTO> refund(@RequestBody @Valid DailyReqDTO request) {
        if (log.isInfoEnabled()) {
            log.info("退款统计报表入参：{}", JacksonUtils.writeValueAsString(request));
        }
        RefundRespDTO result = businessDailyService.refund(request);
        if (CollectionUtils.isNotEmpty(result.getRefundAmounts())) {
            result.getRefundAmounts().forEach(refund -> refund.setRefundName(OrderLocaleEnum.getRefundTypeLocale(refund.getRefundName())));
        }
        return Result.buildSuccessResult(result);
    }

    /**
     * new
     * 受权限控制
     */
    @ApiOperation(value = "退款统计报表", notes = "退款统计报表")
    @PostMapping("refund_sale")
    public Result<RefundSaleDTO> refundSale(@RequestBody @Valid DailyReqDTO request) {
        if (log.isInfoEnabled()) {
            log.info("退款统计报表入参：{}", JacksonUtils.writeValueAsString(request));
        }
        RefundSaleDTO result = businessDailyService.refundSale(request);
        if (CollectionUtils.isNotEmpty(result.getRefundAmounts())) {
            result.getRefundAmounts().forEach(refund -> refund.setRefundName(OrderLocaleEnum.getRefundTypeLocale(refund.getRefundName())));
        }
        return Result.buildSuccessResult(result);
    }

    @ApiOperation(value = "查询当前门店有权限的员工信息", notes = "查询当前门店有权限的员工信息")
    @PostMapping("users")
    public Result<List<UserBriefDTO>> users() {
        if (log.isInfoEnabled()) {
            log.info("查询当前门店有权限的员工信息：{}", JacksonUtils.writeValueAsString(UserContextUtils.getStoreGuid()));
        }
        return Result.buildSuccessResult(businessDailyService.users());
    }
}