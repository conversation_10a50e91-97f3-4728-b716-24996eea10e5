package com.holderzone.holder.saas.aggregation.merchant.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.holder.saas.aggregation.merchant.service.ReportService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.OpenReportClientService;
import com.holderzone.holder.saas.aggregation.merchant.transform.ReportTransform;
import com.holderzone.saas.store.dto.report.openapi.*;
import com.holderzone.saas.store.dto.report.openapi.shiyuanhui.SaleDetailShiYuanHuiRespDTO;
import com.holderzone.saas.store.dto.report.openapi.shiyuanhui.SalePayDetailShiYuanHuiRespDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.report.openapi.OrderSourceTypeEnum;
import com.holderzone.saas.store.enums.report.openapi.RequestSourceEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
public class ReportServiceImpl implements ReportService {

    private final OpenReportClientService openReportClientService;

    @Override
    public SaleDetailLimitOpenRespDTO<?> querySaleDetail(SaleDetailQueryDTO query) {
        SaleDetailLimitRespDTO respDTO = openReportClientService.querySaleDetail(query);
        if (CollectionUtils.isEmpty(respDTO.getList())) {
            return transferEmpty(respDTO);
        }
        return transfer(query, respDTO);
    }

    @Override
    public MemberFundingDetailLimitOpenRespDTO<?> queryMemberFundingDetail(MemberFundingDetailQueryDTO query) {
        MemberFundingDetailLimitRespDTO respDTO = openReportClientService.queryMemberFundingDetail(query);
        if (CollectionUtils.isEmpty(respDTO.getList())) {
            return transferEmpty(respDTO);
        }
        return transfer(respDTO);
    }

    private SaleDetailLimitOpenRespDTO<?> transferEmpty(SaleDetailLimitRespDTO respDTO) {
        SaleDetailLimitOpenRespDTO<?> openResp = new SaleDetailLimitOpenRespDTO<>();
        openResp.setNextCursor(respDTO.getNextCursor());
        openResp.setList(Lists.newArrayList());
        return openResp;
    }

    private MemberFundingDetailLimitOpenRespDTO<?> transferEmpty(MemberFundingDetailLimitRespDTO respDTO) {
        MemberFundingDetailLimitOpenRespDTO<?> openResp = new MemberFundingDetailLimitOpenRespDTO<>();
        openResp.setNextCursor(respDTO.getNextCursor());
        openResp.setList(Lists.newArrayList());
        return openResp;
    }

    private SaleDetailLimitOpenRespDTO<?> transfer(SaleDetailQueryDTO query, SaleDetailLimitRespDTO respDTO) {
        RequestSourceEnum requestSource = RequestSourceEnum.getByCode(query.getRequestSource());
        switch (requestSource) {
            case SHI_YUAN_HUI:
                return transferShiYuanHuiSaleDetails(respDTO);
            case KU_BAN:
                return transferKuBanSaleDetails(respDTO);
            default:
                return transferCommonSaleDetails(respDTO);
        }
    }

    private SaleDetailLimitOpenRespDTO<SaleDetailShiYuanHuiRespDTO> transferShiYuanHuiSaleDetails(SaleDetailLimitRespDTO respDTO) {
        SaleDetailLimitOpenRespDTO<SaleDetailShiYuanHuiRespDTO> openResp = new SaleDetailLimitOpenRespDTO<>();
        openResp.setNextCursor(respDTO.getNextCursor());
        openResp.setList(ReportTransform.INSTANCE.saleDetailRespDTO2SaleDetailShiYuanHuiRespDTO(respDTO.getList()));
        List<SaleDetailShiYuanHuiRespDTO> list = openResp.getList();
        for (SaleDetailShiYuanHuiRespDTO saleDetailShiYuanHuiRespDTO : list) {
            List<SalePayDetailShiYuanHuiRespDTO> payDetails = saleDetailShiYuanHuiRespDTO.getPayDetails();
            if (CollectionUtils.isNotEmpty(payDetails)) {
                for (SalePayDetailShiYuanHuiRespDTO payDetail : payDetails) {
                    // 设置支付方式编码
                    if (PaymentTypeEnum.CASH.getCode() == payDetail.getPaymentType()) {
                        payDetail.setPayWayCode("x1001");
                    }
                }
            }
        }
        return openResp;
    }

    private SaleDetailLimitOpenRespDTO<SaleDetailOpenRespDTO> transferKuBanSaleDetails(SaleDetailLimitRespDTO respDTO) {
        SaleDetailLimitOpenRespDTO<SaleDetailOpenRespDTO> openResp = new SaleDetailLimitOpenRespDTO<>();
        openResp.setNextCursor(respDTO.getNextCursor());
        openResp.setList(ReportTransform.INSTANCE.saleDetailRespDTO2SaleDetailOpenRespDTO(respDTO.getList()));
        // 订单来源转换
        for (SaleDetailOpenRespDTO saleDetailOpenRespDTO : openResp.getList()) {
            Integer deviceType = saleDetailOpenRespDTO.getDeviceType();
            List<Integer> onlinePays = Lists.newArrayList(BaseDeviceTypeEnum.ALI.getCode(), BaseDeviceTypeEnum.TCD.getCode(),
                    BaseDeviceTypeEnum.WECHAT_MINI.getCode(), BaseDeviceTypeEnum.WECHAT.getCode(), BaseDeviceTypeEnum.WECHAT_UNUSED.getCode());
            if (onlinePays.contains(deviceType)) {
                saleDetailOpenRespDTO.setSourceType(OrderSourceTypeEnum.ONLINE.getCode());
            } else {
                saleDetailOpenRespDTO.setSourceType(OrderSourceTypeEnum.OFFLINE.getCode());
            }
        }
        return openResp;
    }

    private SaleDetailLimitOpenRespDTO<SaleDetailOpenRespDTO> transferCommonSaleDetails(SaleDetailLimitRespDTO respDTO) {
        SaleDetailLimitOpenRespDTO<SaleDetailOpenRespDTO> openResp = new SaleDetailLimitOpenRespDTO<>();
        openResp.setNextCursor(respDTO.getNextCursor());
        openResp.setList(ReportTransform.INSTANCE.saleDetailRespDTO2SaleDetailOpenRespDTO(respDTO.getList()));
        return openResp;
    }

    private MemberFundingDetailLimitOpenRespDTO<?> transfer(MemberFundingDetailLimitRespDTO respDTO) {
        MemberFundingDetailLimitOpenRespDTO<MemberFundingDetailRespDTO> openResp = new MemberFundingDetailLimitOpenRespDTO<>();
        openResp.setNextCursor(respDTO.getNextCursor());
        openResp.setList(respDTO.getList());
        return openResp;
    }
}
