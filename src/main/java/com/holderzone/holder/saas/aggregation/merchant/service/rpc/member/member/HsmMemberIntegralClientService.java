package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.HsmMemberIntegralClientService.MemberIntegralClientServiceFallback;
import com.holderzone.holder.saas.member.dto.account.request.MemberIntegralChangeReqDTO;
import com.holderzone.holder.saas.member.dto.account.request.MemberIntegralListQueryReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberIntegralConversionRespDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberIntegralRecordListRespDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberSourceTypeRespDTO;
import feign.hystrix.FallbackFactory;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberIntegralClientService
 * @date 2019/05/30 14:36
 * @description 会员积分
 * @program holder-saas-member-account
 */
@Component
@FeignClient(name = "holder-saas-member-account", fallbackFactory = MemberIntegralClientServiceFallback.class)
public interface HsmMemberIntegralClientService {

    /**
     * 改变积分
     *
     * @param changeReqDTO 改变请求
     * @return 操作结果
     */
    @PostMapping(value = "/hsm_member_integral/changeIntegral", produces = "application/json;charset=utf-8")
    boolean changeIntegral(@RequestBody MemberIntegralChangeReqDTO changeReqDTO);


    /**
     * 来源类型
     *
     * @return 集合
     */
    @PostMapping(value = "/hsm_member_integral/listSourceType", produces = "application/json;charset=utf-8")
    List<MemberSourceTypeRespDTO> listSourceType();

    /**
     * 根据条件分页查询积分记录
     *
     * @param queryReqDTO 查询条件
     * @return 查询结果
     */
    @PostMapping(value = "/hsm_member_integral/listByCondition", produces = "application/json;charset=utf-8")
    Page<MemberIntegralRecordListRespDTO> listByCondition(
        @RequestBody MemberIntegralListQueryReqDTO queryReqDTO);

    /**
     * 统计可兑换的积分
     *
     * @param memberCardGuid 会员持卡guid
     * @return 可兑换的积分
     */
    @RequestMapping(value = "/hsm_member_integral/statisticsConvertibleIntegral", produces = "application/json;charset=utf-8", method = RequestMethod.GET)
    MemberIntegralConversionRespDTO statisticsConvertibleIntegral(
        @RequestParam(value = "memberCardGuid") String memberCardGuid);

    @Component
    class MemberIntegralClientServiceFallback implements
            FallbackFactory<HsmMemberIntegralClientService> {

        private static final Logger LOGGER = LoggerFactory
                .getLogger(MemberIntegralClientServiceFallback.class);

        @Override
        public HsmMemberIntegralClientService create(Throwable throwable) {
            return new HsmMemberIntegralClientService() {
                @Override
                public boolean changeIntegral(MemberIntegralChangeReqDTO changeReqDTO) {
                    LOGGER.error("修改会员积分错误：{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<MemberSourceTypeRespDTO> listSourceType() {
                    LOGGER.error("查询修改会员积分来源类型错误：{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public Page<MemberIntegralRecordListRespDTO> listByCondition(
                        MemberIntegralListQueryReqDTO queryReqDTO) {
                    LOGGER.error("分页查询会员修改积分记录错误：{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public MemberIntegralConversionRespDTO statisticsConvertibleIntegral(
                        String memberCardGuid) {
                    LOGGER.error("统计会员积分错误：{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }
}
