package com.holderzone.holder.saas.aggregation.merchant.controller.member.account;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.HsmMemberInfoVolumeClientService;
import com.holderzone.holder.saas.member.dto.account.request.MemberInfoVolumeEnQueryReqDTO;
import com.holderzone.holder.saas.member.dto.account.request.MemberInfoVolumeSaveReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberInfoVolumeEnListRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 会员持卷表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019/5/31
 */
@Api(description = "会员持有优惠券模块")
@RestController
@RequestMapping("/hsm-member-info-volume")
public class HsmMemberInfoVolumeController {

    @Resource
    private HsmMemberInfoVolumeClientService memberInfoVolumeClientService;

    @ApiOperation("后台同时给多个用户赠送多个优惠券")
    @PostMapping("/platform-save")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "后台同时给多个用户赠送多个优惠券")
    private Result<List<String>> saveMemberInfoVolume(@RequestBody MemberInfoVolumeSaveReqDTO saveReqDTO) {
        return Result.buildSuccessResult(memberInfoVolumeClientService.addMemberInfoVolume(saveReqDTO));
    }

    @ApiOperation("精准推送功能企业查询自己赠送的优惠券，按时间批次返回")
    @PostMapping("/platform-list")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "精准推送功能企业查询自己赠送的优惠券，按时间批次返回")
    private Result<Page<MemberInfoVolumeEnListRespDTO>> listBySendEnterpriseGuid(@RequestBody MemberInfoVolumeEnQueryReqDTO enQueryReqDTO) {
        return Result.buildSuccessResult(memberInfoVolumeClientService.listBySendEnterpriseGuid(enQueryReqDTO));
    }

    @ApiOperation("精准推送功能企业按批次撤销赠送的优惠券")
    @DeleteMapping("/platform-withdraw")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "精准推送功能企业按批次撤销赠送的优惠券")
    public Result<String> withdrawMemberInfoVolumeBatch(@RequestParam(value = "batchNum") String batchNum) {
        boolean res = memberInfoVolumeClientService.withdrawMemberInfoVolumeBatch(batchNum);
        if (res) {
            return Result.buildEmptySuccess();
        }
        return Result.buildSystemErrResult("精准推送功能企业按批次撤销赠送的优惠券-失败");
    }
}
