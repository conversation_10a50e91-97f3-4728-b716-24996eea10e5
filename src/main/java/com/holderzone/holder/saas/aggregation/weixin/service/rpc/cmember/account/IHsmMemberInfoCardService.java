//package com.holderzone.holder.saas.aggregation.weixin.service.rpc.cmember.account;
//
//
//import com.holderzone.framework.exception.unchecked.BusinessException;
//import com.holderzone.framework.util.ThrowableUtils;
//import com.holderzone.holder.saas.cmember.app.dto.account.request.RechargeReqDTO;
//import com.holderzone.holder.saas.cmember.app.dto.account.response.RechargeRespDTO;
//import com.holderzone.holder.saas.cmember.wechat.dto.account.request.MemberCardOpenReqDTO;
//import com.holderzone.holder.saas.cmember.wechat.dto.account.request.MemberCardSummaryQueryReqDTO;
//import com.holderzone.holder.saas.cmember.wechat.dto.account.response.MemberCardSummaryRespDTO;
//import com.holderzone.holder.saas.cmember.wechat.dto.account.response.TravelDetailsRespDTO;
//import com.holderzone.holder.saas.cmember.wechat.dto.systemrights.request.CardRechargeRuleReqDTO;
//import com.holderzone.holder.saas.cmember.wechat.dto.systemrights.response.*;
//import feign.hystrix.FallbackFactory;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.stereotype.Component;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestParam;
//
//
///**
// * <p>
// * 会员持卡表 服务类
// * </p>
// *
// * <AUTHOR>
// * @since 2019-06-14
// */
//@Component
//@FeignClient(name = "holder-saas-cmember-wechat-base-service", fallbackFactory = IHsmMemberInfoCardService.IHsmMemberInfoCardFallback.class)
//public interface IHsmMemberInfoCardService {
//
//    /**
//     * 新的查询会员卡权益等级概要信息
//     * @param memberCardSummaryQueryReqDTO
//     * @return
//     */
//    @PostMapping("/hsmcw/card/getNewCardLevelSummaryInfo")
//    CardLevelSummaryNewRespDTO getNewCardLevelSummaryInfo(@RequestBody MemberCardSummaryQueryReqDTO memberCardSummaryQueryReqDTO);
//
////    /**
////     * 未开通卡的等级及所有权益
////     * @param cardSummaryQueryReqDTO
////     * @return
////     */
////    @PostMapping("/hsmcw/card/getNotOpenCardLevelRights")
////    NotOpenCardLevelRightsRespDto getNotOpenCardLevelRights(@RequestBody @Validated MemberCardSummaryQueryReqDTO cardSummaryQueryReqDTO);
//
////    /**
////     * 根据卡Guid查询卡等级即每个等级权益信息
////     * @param cardGuid
////     * @return
////     */
////    @GetMapping("/hsmcw/card/getCardLevelAndRightInfo")
////    List<CardLevelListDTO> getCardLevelAndRightInfo(@RequestParam("cardGuid")String cardGuid);
//
//    @PostMapping("/hsmcw/card/rechargeMemberCard")
//    RechargeRespDTO rechargeMemberCard(@RequestBody RechargeReqDTO rechargeReqDTO);
//
////    @PostMapping(value = "/hsmcw/card/getMemberCard")
////    MemberCardRespDTO getMemberCard(@RequestBody MemberCardListQueryReqDTO memberCardListQueryReqDTO);
//
//    @PostMapping(value = "/hsmcw/card/getMemberCardSummaryInfo")
//    MemberCardSummaryRespDTO getMemberCardSummaryInfo(@RequestBody MemberCardSummaryQueryReqDTO memberCardSummaryQueryReqDTO);
//
//    @PostMapping(value = "/hsmcw/card/openMemberCard")
//    boolean openMemberCard(@RequestBody MemberCardOpenReqDTO memberCardOpenReqDTO);
//
//    @GetMapping(value = "/hsmcw/card/getFundingTravelDetails")
//    TravelDetailsRespDTO getFundingTravelDetails(@RequestParam("memberInfoCardGuid") String memberInfoCardGuid);
//
//    @GetMapping(value = "/hsmcw/card/getGrowthValueDetails")
//    TravelDetailsRespDTO getGrowthValueDetails(@RequestParam("memberInfoCardGuid") String memberInfoCardGuid);
//
//    @GetMapping(value = "/hsmcw/card/getIntegralTravelDetails")
//    TravelDetailsRespDTO getIntegralTravelDetails(@RequestParam("memberInfoCardGuid") String memberInfoCardGuid);
//
//    @PostMapping(value = "/hsmcw/card/getCardLevelSummaryInfo")
//    CardLevelSummaryRespDTO getCardLevelSummaryInfo(@RequestBody MemberCardSummaryQueryReqDTO cardSummaryQueryReqDTO);
//
////    @PostMapping(value = "/hsmcw/card/getCardRightDetails")
////    List<CardRightDetailsRespDTO> getCardRightDetails(@RequestBody CardRightDetailsReqDTO cardRightDetailsReqDTO);
//
//    @PostMapping(value = "/hsmcw/card/getCardSimpleRechargeRule")
//    CardRechargeRuleSummaryRespDTO getCardSimpleRechargeRule(@RequestBody CardRechargeRuleReqDTO cardRechargeRuleReqDTO);
//
//    @GetMapping(value = "/hsmcw/card/getFundingRule")
//    FundingRuleRespDTO getFundingRule(@RequestParam("systemManagementGuid") String systemManagementGuid,
//                                      @RequestParam("cardGuid") String cardGuid);
//
//    @GetMapping(value = "/hsmcw/card/getGrowthValueRule")
//    GrowthValueRuleRespDTO getGrowthValueRule(@RequestParam("systemManagementGuid") String systemManagementGuid,
//                                              @RequestParam("cardGuid") String cardGuid);
//
//    @GetMapping(value = "/hsmcw/card/getIntegralRule")
//    IntegralRuleRespDTO getIntegralRule(@RequestParam("systemManagementGuid") String systemManagementGuid,
//                                        @RequestParam("cardGuid") String cardGuid);
//
//    @Component
//    class IHsmMemberInfoCardFallback implements
//            FallbackFactory<IHsmMemberInfoCardService> {
//
//        private static final Logger LOGGER = LoggerFactory
//                .getLogger(IHsmMemberInfoCardFallback.class);
//
//        @Override
//        public IHsmMemberInfoCardService create(Throwable throwable) {
//            return new IHsmMemberInfoCardService() {
//
//                @Override
//                public CardLevelSummaryNewRespDTO getNewCardLevelSummaryInfo(MemberCardSummaryQueryReqDTO memberCardSummaryQueryReqDTO) {
//                    LOGGER.error("新的查询会员卡权益等级概要信息错误:{}", ThrowableUtils.asString(throwable));
//                    throw new BusinessException(throwable.getMessage());
//                }
//
////                @Override
////                public NotOpenCardLevelRightsRespDto getNotOpenCardLevelRights(MemberCardSummaryQueryReqDTO cardSummaryQueryReqDTO) {
////                    LOGGER.error("查询未开通卡的等级及所有权益信息错误:{}", ThrowableUtils.asString(throwable));
////                    throw new BusinessException(throwable.getMessage());
////                }
//
////                @Override
////                public List<CardLevelListDTO> getCardLevelAndRightInfo(String cardGuid) {
////                    LOGGER.error("根据卡Guid查询卡等级即每个等级权益信息错误:{}", ThrowableUtils.asString(throwable));
////                    throw new BusinessException(throwable.getMessage());
////                }
//
//                @Override
//                public RechargeRespDTO rechargeMemberCard(RechargeReqDTO rechargeReqDTO) {
//                    LOGGER.error("会员卡充值信息错误:{}", ThrowableUtils.asString(throwable));
//                    throw new BusinessException(throwable.getMessage());
//                }
//
////                @Override
////                public MemberCardRespDTO getMemberCard(MemberCardListQueryReqDTO memberCardListQueryReqDTO) {
////                    LOGGER.error("查找会员卡及未开通个卡信息错误:{}", ThrowableUtils.asString(throwable));
////                    throw new BusinessException(throwable.getMessage());
////                }
//
//                @Override
//                public MemberCardSummaryRespDTO getMemberCardSummaryInfo(MemberCardSummaryQueryReqDTO memberCardSummaryQueryReqDTO) {
//                    LOGGER.error("查询会员卡概要信息错误:{}", ThrowableUtils.asString(throwable));
//                    throw new BusinessException(throwable.getMessage());
//                }
//
//                @Override
//                public boolean openMemberCard(MemberCardOpenReqDTO memberCardOpenReqDTO) {
//                    LOGGER.error("会员卡开通错误:{}", ThrowableUtils.asString(throwable));
//                    if (throwable.getMessage().contains("已经拥有此卡")) {
//                        return true;
//                    } else {
//                        throw new BusinessException(throwable.getMessage());
//                    }
//                }
//
//                @Override
//                public TravelDetailsRespDTO getFundingTravelDetails(String memberInfoCardGuid) {
//                    LOGGER.error("查看会员卡资金明细错误:{}", ThrowableUtils.asString(throwable));
//                    throw new BusinessException(throwable.getMessage());
//                }
//
//                @Override
//                public TravelDetailsRespDTO getGrowthValueDetails(String memberInfoCardGuid) {
//                    LOGGER.error("查看会员卡成长值明细错误:{}", ThrowableUtils.asString(throwable));
//                    throw new BusinessException(throwable.getMessage());
//                }
//
//                @Override
//                public TravelDetailsRespDTO getIntegralTravelDetails(String memberInfoCardGuid) {
//                    LOGGER.error("查看会员卡积分明细错误:{}", ThrowableUtils.asString(throwable));
//                    throw new BusinessException(throwable.getMessage());
//                }
//
//                @Override
//                public CardLevelSummaryRespDTO getCardLevelSummaryInfo(MemberCardSummaryQueryReqDTO cardSummaryQueryReqDTO) {
//                    LOGGER.error("查看会员卡等级详情错误:{}", ThrowableUtils.asString(throwable));
//                    throw new BusinessException(throwable.getMessage());
//                }
//
////                @Override
////                public List<CardRightDetailsRespDTO> getCardRightDetails(CardRightDetailsReqDTO cardRightDetailsReqDTO) {
////                    LOGGER.error("查寻会员卡权益详情错误:{}", ThrowableUtils.asString(throwable));
////                    throw new BusinessException(throwable.getMessage());
////                }
//
//                @Override
//                public CardRechargeRuleSummaryRespDTO getCardSimpleRechargeRule(CardRechargeRuleReqDTO cardRechargeRuleReqDTO) {
//                    LOGGER.error("会员卡充值金额规则错误:{}", ThrowableUtils.asString(throwable));
//                    throw new BusinessException(throwable.getMessage());
//                }
//
//                @Override
//                public FundingRuleRespDTO getFundingRule(String systemManagementGuid, String cardGuid) {
//                    LOGGER.error("查看会员卡余额配置规则错误:{}", ThrowableUtils.asString(throwable));
//                    throw new BusinessException(throwable.getMessage());
//                }
//
//                @Override
//                public GrowthValueRuleRespDTO getGrowthValueRule(String systemManagementGuid, String cardGuid) {
//                    LOGGER.error("查看会员卡成长值配置规则错误:{}", ThrowableUtils.asString(throwable));
//                    throw new BusinessException(throwable.getMessage());
//                }
//
//                @Override
//                public IntegralRuleRespDTO getIntegralRule(String systemManagementGuid, String cardGuid) {
//                    LOGGER.error("查看会员卡积分配置规则错误:{}", ThrowableUtils.asString(throwable));
//                    throw new BusinessException(throwable.getMessage());
//                }
//            };
//        }
//    }
//}
