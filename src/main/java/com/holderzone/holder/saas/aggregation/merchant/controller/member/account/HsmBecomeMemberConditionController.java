package com.holderzone.holder.saas.aggregation.merchant.controller.member.account;


import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.BecomeMemberConditionClientService;
import com.holderzone.holder.saas.member.dto.account.request.HsmBecomeMemberConditionReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.HsmBecomeMemberConditionRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 成为会员门槛表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-21
 */
@RestController
@RequestMapping("/hsm-become-member-condition")
@Api(description = "会员门槛 controller")
public class HsmBecomeMemberConditionController {
    @Autowired
    private BecomeMemberConditionClientService becomeMemberConditionClientService;



    /**
     * @Description 查询门店信息
     * <AUTHOR>
     * @Date  2019/5/20 15:17
     * @param
     * @return com.holderzone.framework.response.Result
     */
    @ApiOperation(value = "更新会员渠道", notes = "更新会员渠道")
    @PostMapping(value = "/update", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "更新会员渠道")
    public Result update(@RequestBody @Validated HsmBecomeMemberConditionReqDTO hsmBecomeMemberConditionReqDTO){
        return Result.buildSuccessResult(becomeMemberConditionClientService.update(hsmBecomeMemberConditionReqDTO));
    }
    /**
     * @Description 获取会员渠道配置
     * <AUTHOR>
     * @Date  2019/6/1 16:50
     * @param
     * @return com.holderzone.framework.response.Result
     */
    @ApiOperation(value = "获取会员渠道", notes = "获取会员渠道",response = HsmBecomeMemberConditionRespDTO.class)
    @GetMapping(value = "/getByEnterpriseGuid", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "获取会员渠道")
    public Result getByEnterpriseGuid(){
        return Result.buildSuccessResult(becomeMemberConditionClientService.getByEnterpriseGuid());
    }
}
