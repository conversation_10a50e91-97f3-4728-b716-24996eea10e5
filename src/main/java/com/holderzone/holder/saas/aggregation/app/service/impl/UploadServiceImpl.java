package com.holderzone.holder.saas.aggregation.app.service.impl;


import com.holderzone.framework.base.dto.file.FileDto;
import com.holderzone.holder.saas.aggregation.app.service.UploadService;
import com.holderzone.holder.saas.aggregation.app.service.feign.BaseFeignService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * 上传接口
 * 调用framework-base-service的上传
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UploadServiceImpl implements UploadService {

    private final BaseFeignService baseService;

    @Override
    public String upload(String picture) {
        if (StringUtils.isBlank(picture)) {
            return Strings.EMPTY;
        }
        FileDto fileDto = new FileDto();
        fileDto.setFileContent(picture);
        fileDto.setFileName(UUID.randomUUID().toString().replace("-", ""));
        return baseService.upload(fileDto);
    }
}
