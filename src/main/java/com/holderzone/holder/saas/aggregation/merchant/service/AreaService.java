package com.holderzone.holder.saas.aggregation.merchant.service;

import com.holderzone.framework.response.Result;
import com.holderzone.saas.store.dto.table.AreaDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AreaService
 * @date 2019/01/07 10:47
 * @description
 * @program holder-saas-aggregation-merchant
 */
public interface AreaService {

    String addArea(AreaDTO areaDTO);

    String updateArea(AreaDTO areaDTO);

    String delete(String guid);

    List<AreaDTO> queryAll(String storeGuid);
}
