package com.holderzone.holder.saas.aggregation.merchant.service.rpc.takeout;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.jd.BusinessOrderDTO;
import com.holderzone.saas.store.dto.takeaway.jd.CallBackDTO;
import com.holderzone.saas.store.dto.takeaway.request.*;
import com.holderzone.saas.store.dto.takeaway.response.*;
import eleme.openapi.sdk.api.entity.other.OMessage;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeawayProducersClientService
 * @date 2018/09/12 18:20
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-takeaway-producer", fallbackFactory = TakeoutProducerService.ServiceFallBack.class)
public interface TakeoutProducerService {

    @PostMapping("/takeout/shop_binding_url")
    TakeoutShopBindRespDTO shopBindingUrl(@RequestBody TakeoutShopBindReqDTO takeoutShopBindReqDTO);

    @PostMapping("/takeout/do_shop_unbinding")
    TakeoutOwnRespDTO doShopUnBinding(@RequestBody TakeoutShopOwnUnBindReqDTO takeoutShopOwnUnBindReqDTO);

    @PostMapping("/own/callback/order")
    OwnCallbackResponse orderCallback(@RequestBody SalesOrderDTO salesOrderDTO);

    @PostMapping("/takeout/do_shop_binding")
    TakeoutOwnRespDTO doShopBinding(@RequestBody TakeoutShopOwnBindReqDTO takeoutShopOwnBindReqDTO);

    @PostMapping("/takeout/do_shop_binding_tcd")
    TcdCommonRespDTO doShopBindingTcd(@RequestBody TCDBindReqDTO tcdBindReqDTO);

    @PostMapping("/takeout/do_shop_unbinding_tcd")
    TcdCommonRespDTO doShopUnBindingTcd(@RequestBody TCDBindReqDTO tcdBindReqDTO);

    @PostMapping("/tiktok/store/bind")
    void doShopBindingTikTok(@RequestBody StoreAuthByStoreReqDTO storeAuthByStoreReqDTO);

    @PostMapping("/tiktok/store/unbind")
    void doShopUnBindingTikTok(@RequestBody StoreAuthByStoreReqDTO storeAuthByStoreReqDTO);

    @PostMapping("/takeout/group_buy_binding_url")
    TakeoutShopBindRespDTO groupBuyBindingUrl(@RequestBody GroupBuyShopBindReqDTO groupBuyShopBindReqDTO);

    @PostMapping("/takeout/item_binding_url")
    TakeoutItemBindRespDTO itemBindingUrl(@RequestBody TakeoutItemBindReqDTO takeoutItemBindReqDTO);

    @PostMapping("/ele/callback/order")
    EleCallbackResponse eleOrderCallback(@RequestBody OMessage omessage);

    @GetMapping("/ele/callback/order")
    EleCallbackResponse testEleOrderCallback();

    @GetMapping("/ele/callback/bind")
    EleCallbackResponse eleBindCallback(@RequestBody EleCallbackBindDTO eleCallbackBindDTO);

    @PostMapping("/mt/callback/order/{path}")
    MtCallbackResponse mtOrderCallback(@RequestBody MtCallbackDTO mtCallbackDTO, @PathVariable("path") String path);

    @PostMapping("/mt/callback/settlement/order")
    MtCallbackResponse mtOrderSettlementCallback(@RequestBody MtCallbackSettlementDTO mtCallbackDTO);

    @GetMapping("/mt/callback/order/{path}")
    MtCallbackResponse testMtOrderCallback(@PathVariable("path") String path);

    @PostMapping("/mt/callback/privacy_degrade")
    MtCallbackResponse mtPrivacyDegradeCallback(@RequestBody MtCallbackDTO mtCallbackDTO);

    @GetMapping("/mt/callback/privacy_degrade")
    MtCallbackResponse testMtPrivacyDegradeCallback();

    @PostMapping("/mt/callback/bind")
    MtCallbackResponse mtBindCallback(@RequestBody MtCallbackDTO mtCallbackDTO);

    @GetMapping("/mt/callback/bind")
    MtCallbackResponse testMtBindCallback();

    @PostMapping("/mt/callback/unbind")
    MtCallbackResponse mtUnbindCallback(@RequestBody MtCallbackDTO mtCallbackDTO);

    @GetMapping("/mt/callback/unbind")
    MtCallbackResponse testMtUnbindCallback();

    @PostMapping("/mt/callback/heartbeat")
    MtCallbackResponse mtHeartbeatCallback();

    @GetMapping("/mt/callback/heartbeat")
    MtCallbackResponse testMtHeartbeatCallback();

    @PostMapping("/tcd/callback/order")
    TcdCallbackResponse tcdOrderCallback(@RequestBody TakeoutTcdOrderReqDTO takeoutTcdOrderReqDTO);

    @PostMapping("/takeout/bind_stock_store")
    StockStoreBindResqDTO bindStockStore(@RequestBody StockStoreBindReqDTO req);

    @GetMapping("/takeout/get_bind_stock_store")
    StockStoreBindResqDTO getBindStockStore(@RequestParam("storeGuid") String storeGuid);

    @PostMapping("/item_mapping/bind")
    void bindItem(@RequestBody @Validated UnItemBindUnbindReq unItemBindUnbindReq);

    @PostMapping("/mt/callback/authorization")
    MtCallbackResponse authorizationCallback(@RequestBody MtCallbackAuthorizationDTO mtCallbackDTO);

    @PostMapping("/mt/callback/authorization/unbind")
    MtCallbackResponse authorizationUnbindCallback(@RequestBody MtCallbackUnbindAuthorizationDTO mtCallbackDTO);

    @PostMapping("/mt/callback/member/consume")
    MtCallbackResponse memberConsumeCallback(@RequestBody MtCallbackConsumeDTO mtCallbackDTO);

    @PostMapping("/mt/callback/member/new")
    MtCallbackResponse memberNewCallback(@RequestBody MtCallbackMemberDTO mtCallbackDTO);

    @PostMapping(value = "/takeout/get_mt_auth_url")
    String getMtAuthUrl(@RequestBody MtAuthBindUrlDTO authBindUrl);

    @GetMapping("/takeout/get_mt_bind_auth/{data}")
    List<MtAuthBindUrlDTO> getMtBindAuth(@PathVariable("data") String data);

    @PostMapping(value = "/takeout/get_auth_stores")
    List<String> getAuthStores(@RequestBody UnItemQueryReq unItemQueryReq);

    @PostMapping(value = "/takeout/notify_alipay_auth")
    @ApiOperation(value = "通知支付宝授权变更", notes = "通知支付宝授权变更")
    void notifyAliPayAuth(@RequestBody NotifyAliPayAuthReqDTO notifyDTO);

    @PostMapping(value = "/jd/token")
    void callback(@RequestBody CallBackDTO callBackDTO);

    @PostMapping(value = "/jd/order_callback")
    void jdOrderCallback(@RequestBody BusinessOrderDTO callBackDTO);

    @PostMapping("/jd/store/bind")
    void doShopBindingJd(@RequestBody StoreAuthByStoreReqDTO storeAuthByStoreReqDTO);

    @PostMapping("/jd/store/unbind")
    void doShopUnBindingJd(@RequestBody StoreAuthByStoreReqDTO storeAuthByStoreReqDTO);

    @PostMapping("/jd/auth_brand")
    List<String> authBrand(@RequestBody List<String> brandGuidList);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<TakeoutProducerService> {

        private static final String HYSTRIX_PATTERN = "调用外卖Producer服务熔断，方法：{}，入参：{}，异常：{}";

        @Override
        public TakeoutProducerService create(Throwable cause) {
            return new TakeoutProducerService() {

                @Override
                public TakeoutShopBindRespDTO shopBindingUrl(TakeoutShopBindReqDTO takeoutShopBindReqDTO) {
                    log.error(HYSTRIX_PATTERN, "shopBindingUrl",
                            JacksonUtils.writeValueAsString(takeoutShopBindReqDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public OwnCallbackResponse orderCallback(SalesOrderDTO salesOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "orderCallback",
                            JacksonUtils.writeValueAsString(salesOrderDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TakeoutOwnRespDTO doShopUnBinding(TakeoutShopOwnUnBindReqDTO takeoutShopOwnUnBindReqDTO) {
                    log.error(HYSTRIX_PATTERN, "doShopUnBinding",
                            JacksonUtils.writeValueAsString(takeoutShopOwnUnBindReqDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TakeoutOwnRespDTO doShopBinding(TakeoutShopOwnBindReqDTO takeoutShopOwnBindReqDTO) {
                    log.error(HYSTRIX_PATTERN, "doShopBinding",
                            JacksonUtils.writeValueAsString(takeoutShopOwnBindReqDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TakeoutShopBindRespDTO groupBuyBindingUrl(GroupBuyShopBindReqDTO groupBuyShopBindReqDTO) {
                    log.error(HYSTRIX_PATTERN, "groupBuyBindingUrl",
                            JacksonUtils.writeValueAsString(groupBuyShopBindReqDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TakeoutItemBindRespDTO itemBindingUrl(TakeoutItemBindReqDTO takeoutItemBindReqDTO) {
                    log.error(HYSTRIX_PATTERN, "itemBindingUrl",
                            JacksonUtils.writeValueAsString(takeoutItemBindReqDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public EleCallbackResponse eleOrderCallback(OMessage omessage) {
                    log.error(HYSTRIX_PATTERN, "eleOrderCallback",
                            JacksonUtils.writeValueAsString(omessage), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public EleCallbackResponse testEleOrderCallback() {
                    log.error(HYSTRIX_PATTERN, "testEleOrderCallback", "", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public EleCallbackResponse eleBindCallback(EleCallbackBindDTO eleCallbackBindDTO) {
                    log.error(HYSTRIX_PATTERN, "eleBindCallback",
                            JacksonUtils.writeValueAsString(eleCallbackBindDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public MtCallbackResponse mtOrderCallback(MtCallbackDTO mtCallbackDTO, String path) {
                    log.error(HYSTRIX_PATTERN, "mtOrderCallback",
                            JacksonUtils.writeValueAsString(mtCallbackDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public MtCallbackResponse mtOrderSettlementCallback(MtCallbackSettlementDTO mtCallbackDTO) {
                    log.error(HYSTRIX_PATTERN, "mtOrderSettlementCallback",
                            JacksonUtils.writeValueAsString(mtCallbackDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public MtCallbackResponse testMtOrderCallback(String path) {
                    log.error(HYSTRIX_PATTERN, "testMtOrderCallback", path, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public MtCallbackResponse mtPrivacyDegradeCallback(MtCallbackDTO mtCallbackDTO) {
                    log.error(HYSTRIX_PATTERN, "mtPrivacyDegradeCallback",
                            JacksonUtils.writeValueAsString(mtCallbackDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public MtCallbackResponse testMtPrivacyDegradeCallback() {
                    log.error(HYSTRIX_PATTERN, "testMtPrivacyDegradeCallback", "", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public MtCallbackResponse mtBindCallback(MtCallbackDTO mtCallbackDTO) {
                    log.error(HYSTRIX_PATTERN, "mtBindCallback",
                            JacksonUtils.writeValueAsString(mtCallbackDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public MtCallbackResponse testMtBindCallback() {
                    log.error(HYSTRIX_PATTERN, "testMtBindCallback", "", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public MtCallbackResponse mtUnbindCallback(MtCallbackDTO mtCallbackDTO) {
                    log.error(HYSTRIX_PATTERN, "mtUnbindCallback",
                            JacksonUtils.writeValueAsString(mtCallbackDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public MtCallbackResponse testMtUnbindCallback() {
                    log.error(HYSTRIX_PATTERN, "testMtUnbindCallback", "", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public MtCallbackResponse mtHeartbeatCallback() {
                    log.error(HYSTRIX_PATTERN, "mtHeartbeatCallback", "", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public MtCallbackResponse testMtHeartbeatCallback() {
                    log.error(HYSTRIX_PATTERN, "testMtHeartbeatCallback", "", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TcdCallbackResponse tcdOrderCallback(TakeoutTcdOrderReqDTO takeoutTcdOrderReqDTO) {
                    log.error(HYSTRIX_PATTERN, "tcdOrderCallback", "", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public StockStoreBindResqDTO bindStockStore(StockStoreBindReqDTO req) {
                    log.error(HYSTRIX_PATTERN, "bindStockStore", "", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public StockStoreBindResqDTO getBindStockStore(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "getBindStockStore", "", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void bindItem(UnItemBindUnbindReq unItemBindUnbindReq) {
                    log.error(HYSTRIX_PATTERN, "bindItem", JacksonUtils.writeValueAsString(unItemBindUnbindReq), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public MtCallbackResponse authorizationCallback(MtCallbackAuthorizationDTO mtCallbackDTO) {
                    log.error(HYSTRIX_PATTERN, "authorizationCallback", JacksonUtils.writeValueAsString(mtCallbackDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public MtCallbackResponse authorizationUnbindCallback(MtCallbackUnbindAuthorizationDTO mtCallbackDTO) {
                    log.error(HYSTRIX_PATTERN, "authorizationUnbindCallback", JacksonUtils.writeValueAsString(mtCallbackDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public MtCallbackResponse memberConsumeCallback(MtCallbackConsumeDTO mtCallbackDTO) {
                    log.error(HYSTRIX_PATTERN, "memberConsumeCallback", JacksonUtils.writeValueAsString(mtCallbackDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public MtCallbackResponse memberNewCallback(MtCallbackMemberDTO mtCallbackDTO) {
                    log.error(HYSTRIX_PATTERN, "memberNewCallback", JacksonUtils.writeValueAsString(mtCallbackDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public String getMtAuthUrl(MtAuthBindUrlDTO authBindUrl) {
                    log.error(HYSTRIX_PATTERN, "getMtAuthUrl", JacksonUtils.writeValueAsString(authBindUrl), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<MtAuthBindUrlDTO> getMtBindAuth(String data) {
                    log.error(HYSTRIX_PATTERN, "getMtBindAuth", data, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<String> getAuthStores(UnItemQueryReq unItemQueryReq) {
                    log.error(HYSTRIX_PATTERN, "getAuthStores", JacksonUtils.writeValueAsString(unItemQueryReq), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void notifyAliPayAuth(NotifyAliPayAuthReqDTO notifyDTO) {
                    log.error(HYSTRIX_PATTERN, "notifyAliPayAuth", JacksonUtils.writeValueAsString(notifyDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TcdCommonRespDTO doShopBindingTcd(TCDBindReqDTO tcdBindReqDTO) {
                    log.error(HYSTRIX_PATTERN, "doShopBindingTcd", "", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TcdCommonRespDTO doShopUnBindingTcd(TCDBindReqDTO tcdBindReqDTO) {
                    log.error(HYSTRIX_PATTERN, "doShopUnBindingTcd", "", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void doShopBindingTikTok(StoreAuthByStoreReqDTO storeAuthByStoreReqDTO) {
                    log.error(HYSTRIX_PATTERN, "doShopBindingTikTok", JacksonUtils.writeValueAsString(storeAuthByStoreReqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void doShopUnBindingTikTok(StoreAuthByStoreReqDTO storeAuthByStoreReqDTO) {
                    log.error(HYSTRIX_PATTERN, "doShopUnBindingTikTok", JacksonUtils.writeValueAsString(storeAuthByStoreReqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void callback(CallBackDTO callBackDTO) {
                    log.error(HYSTRIX_PATTERN, "doShopUnBindingTikTok", JacksonUtils.writeValueAsString(callBackDTO),
                            ThrowableUtils.asString(cause));
                }

                @Override
                public void doShopBindingJd(StoreAuthByStoreReqDTO storeAuthByStoreReqDTO) {
                    log.error(HYSTRIX_PATTERN, "doShopBindingJd", JacksonUtils.writeValueAsString(storeAuthByStoreReqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void doShopUnBindingJd(StoreAuthByStoreReqDTO storeAuthByStoreReqDTO) {
                    log.error(HYSTRIX_PATTERN, "doShopUnBindingJd", JacksonUtils.writeValueAsString(storeAuthByStoreReqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<String> authBrand(List<String> brandGuidList) {
                    log.error(HYSTRIX_PATTERN, "authBrand", JacksonUtils.writeValueAsString(brandGuidList),
                            ThrowableUtils.asString(cause));
                    return Collections.emptyList();
                }

                @Override
                public void jdOrderCallback(BusinessOrderDTO callBackDTO) {
                    log.error(HYSTRIX_PATTERN, "jdOrderCallback", JacksonUtils.writeValueAsString(callBackDTO),
                            ThrowableUtils.asString(cause));
                }
            };
        }
    }
}
