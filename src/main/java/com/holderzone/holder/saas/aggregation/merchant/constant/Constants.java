package com.holderzone.holder.saas.aggregation.merchant.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className Constants
 * @date 2018/09/27 下午1:01
 * @description //TODO
 * @program holder-saas-aggregation-merchant
 */
public final class Constants {
    public static final String BRAND_ITEM_MODEL ="品牌商品库";
    public static final String STORE_ITEM_MODEL ="门店商品库";
    public static final String DISTRIBUTE_FAIL ="注意：保存成功，分配售卖门店失败";

    public final static int MAX_EXPORT_SIZE = 20000;

    public final static String DEVICE_SORTING_FAILED = "DEVICE_SORTING_FAILED";

    public final static String DEVICE_UNBINDING_FAILED = "DEVICE_UNBINDING_FAILED";

    public final static String GUID_AND_SKU_EMPTY = "GUID_AND_SKU_EMPTY";

    public final static String SAVE_FAILED = "SAVE_FAILED";

    public final static String DELETION_FAILED = "DELETION_FAILED";

    public final static String UPDATE_FAILED = "UPDATE_FAILED";

    public final static String FAILED_TO_ASSIGN_SELLING_STORES = "FAILED_TO_ASSIGN_SELLING_STORES";

    public final static String PRODUCT_LISTING_FAILED = "PRODUCT_LISTING_FAILED";

    public final static String PRODUCT_DELISTING_FAILED = "PRODUCT_DELISTING_FAILED";

    public final static String BATCH_PRODUCT_LISTING_FAILED = "BATCH_PRODUCT_LISTING_FAILED";

    public final static String BATCH_PRODUCT_DELISTING_FAILED = "BATCH_PRODUCT_DELISTING_FAILED";

    public final static String BATCH_PARTICIPATION_IN_OVERALL_ORDER_DISCOUNT_FAILED = "BATCH_PARTICIPATION_IN_OVERALL_ORDER_DISCOUNT_FAILED";

    public final static String BATCH_EXCLUSION_FROM_OVERALL_ORDER_DISCOUNT_FAILED = "BATCH_EXCLUSION_FROM_OVERALL_ORDER_DISCOUNT_FAILED";
    public final static String IMPORT_FAILED = "IMPORT_FAILED";

    public final static String SAVE_UPDATE_DELETE_FAILED = "SAVE_UPDATE_DELETE_FAILED";
    public final static String DEACTIVATION_FAILED = "DEACTIVATION_FAILED";
    public final static String SAVING_IN_PROGRESS = "SAVING_IN_PROGRESS";

    public final static String SORTING_UPDATE_FAILED = "SORTING_UPDATE_FAILED";
    public final static String CATEGORY_CREATION_FAILED = "CATEGORY_CREATION_FAILED";
    public final static String QUICK_CATEGORY_CREATION_FAILED = "QUICK_CATEGORY_CREATION_FAILED";
    public final static String SORTING_RETRIEVAL_FAILED = "SORTING_RETRIEVAL_FAILED";
    public final static String CATEGORY_MODIFICATION_FAILED = "CATEGORY_MODIFICATION_FAILED";
    public final static String RULE_UPDATE_FAILED = "RULE_UPDATE_FAILED";
    public final static String RULE_SAVING_FAILED = "RULE_SAVING_FAILED";
    public final static String RULE_DELETION_FAILED = "RULE_DELETION_FAILED";
    public final static String OPERATION_FAILED = "OPERATION_FAILED";
    public final static String CREATION_FAILED = "CREATION_FAILED";
    public final static String SALES_MODE_CHANGE_FAILED = "SALES_MODE_CHANGE_FAILED";
    public final static String TABLE_ALREADY_EXISTS = "TABLE_ALREADY_EXISTS";
    public final static String UPDATE_SUCCESSFUL = "UPDATE_SUCCESSFUL";
    public final static String DELETION_SUCCESSFUL = "DELETION_SUCCESSFUL";
    public final static String CREATION_SUCCESSFUL = "CREATION_SUCCESSFUL";
    public final static String OPERATION_SUCCESSFUL = "OPERATION_SUCCESSFUL";
    public final static String SALES_MODE_CHANGE_SUCCESSFUL = "SALES_MODE_CHANGE_SUCCESSFUL";
    public final static String STORE_SETTLEMENT_SETTINGS_SUCCESSFUL = "STORE_SETTLEMENT_SETTINGS_SUCCESSFUL";
    public final static String DEVICE_UNBINDING_SUCCESSFUL = "DEVICE_UNBINDING_SUCCESSFUL";

    public final static String WE_CHAT_STORE_CONFIGURATION_MODIFIED_FAILED = "WE_CHAT_STORE_CONFIGURATION_MODIFIED_FAILED";
    public final static String WE_CHAT_STORE_CONFIGURATION_MODIFIED_SUCCESSFULLY = "WE_CHAT_STORE_CONFIGURATION_MODIFIED_SUCCESSFULLY";
    public final static String WE_CHAT_STORE_CONFIGURATION_BATCH_MODIFIED_SUCCESSFULLY = "WE_CHAT_STORE_CONFIGURATION_BATCH_MODIFIED_SUCCESSFULLY";
    public final static String WE_CHAT_STORE_CONFIGURATION_BATCH_MODIFIED_FAILED = "WE_CHAT_STORE_CONFIGURATION_BATCH_MODIFIED_FAILED";
    public final static String IMPORTED_PRODUCTS = "IMPORTED_PRODUCTS";
    public final static String EXPORT_LABEL = "$";

    public final static String NEW_CREATION_FAILED="新增失败:";

    public final static String BANQUET_PACKAGES="宴会套餐";

    public final static String DEFAULT_CATEGORY="默认分类";

    public final static String RESULT_MODIFICATION_SUCCESSFUL="RESULT_MODIFICATION_SUCCESSFUL";
    public final static String RESULT_MODIFICATION_FAILED="RESULT_MODIFICATION_FAILED";

    public static final String DEFAULT_TEMPLATE = "默认模板";


}
