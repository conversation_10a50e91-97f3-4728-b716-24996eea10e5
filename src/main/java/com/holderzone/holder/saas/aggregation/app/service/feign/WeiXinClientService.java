package com.holderzone.holder.saas.aggregation.app.service.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOperationDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.open.WxMpTemplateDTO;
import com.holderzone.saas.store.dto.weixin.open.WxSendMessageReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxOperateReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreMerchantOrderRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreMerchantOrderClientService
 * @date 2019/4/10
 */
@Component
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = WeiXinClientService.WxStoreMerchantOrder.class)
public interface WeiXinClientService {

    String URL_PREFIX = "/wx_store_merchant_order";

    @ApiOperation("获取当前门店所有订单")
    @PostMapping(URL_PREFIX + "/get_pend")
    WxStoreMerchantOrderRespDTO getWxStoreMerchantOrderResp(WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO);

    @ApiOperation("商户处理订单")
    @PostMapping(URL_PREFIX + "/operate")
    WxStoreMerchantOperationDTO operationMerchantOrder(WxOperateReqDTO wxOperateReqDTO);

    @ApiOperation("获取订单详情")
    @PostMapping(URL_PREFIX + "/get_detail_pend")
    WxStoreMerchantOrderDTO getDetailPend(WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO);

    @ApiOperation("获取下单手机号")
    @PostMapping(value = "/wx_store_order_record/get_merchant_order_phone")
    WxStoreMerchantOrderDTO getMerchantOrderPhone(@RequestParam(value = "orderGuid") String orderGuid);

    @ApiOperation("快餐订单查微信订单")
    @PostMapping(value = "/wx_store_order_record/list_by_order_guid")
    List<WxStoreMerchantOrderDTO> listByOrderGuid(@RequestBody SingleDataDTO query);

    @PostMapping("/wx_handler/send_call_message")
    @ApiOperation("发送商家出餐通知")
    void sendCallMessage(@RequestBody WxSendMessageReqDTO sendMessageReqDTO);

    @PostMapping("/wx_handler/add_msg_template")
    @ApiOperation("手动添加消息模版")
    void addMsgTemplate(@RequestBody WxMpTemplateDTO wxMpTemplateDTO);

    @Slf4j
    @Component
    class WxStoreMerchantOrder implements FallbackFactory<WeiXinClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public WeiXinClientService create(Throwable throwable) {

            return new WeiXinClientService() {

                @Override
                public WxStoreMerchantOrderRespDTO getWxStoreMerchantOrderResp(WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO) {
                    log.error(HYSTRIX_PATTERN, "getWxStoreMerchantOrderResp", JacksonUtils.writeValueAsString(wxStoreMerchantOrderReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public WxStoreMerchantOperationDTO operationMerchantOrder(WxOperateReqDTO wxOperateReqDTO) {
                    log.error(HYSTRIX_PATTERN, "operationMerchantOrder", JacksonUtils.writeValueAsString(wxOperateReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public WxStoreMerchantOrderDTO getDetailPend(WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO) {
                    log.error(HYSTRIX_PATTERN, "getDetailPend", JacksonUtils.writeValueAsString(wxStoreMerchantOrderReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public WxStoreMerchantOrderDTO getMerchantOrderPhone(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "getMerchantOrderPhone", orderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<WxStoreMerchantOrderDTO> listByOrderGuid(SingleDataDTO query) {
                    log.error(HYSTRIX_PATTERN, "listByOrderGuid", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void sendCallMessage(WxSendMessageReqDTO sendMessageReqDTO) {
                    log.error(HYSTRIX_PATTERN, "sendCallMessage", JacksonUtils.writeValueAsString(sendMessageReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void addMsgTemplate(WxMpTemplateDTO wxMpTemplateDTO) {
                    log.error(HYSTRIX_PATTERN, "addMsgTemplate", JacksonUtils.writeValueAsString(wxMpTemplateDTO),
                            throwable.getMessage());
                    throw new ServerException();
                }
            };
        }
    }
}
