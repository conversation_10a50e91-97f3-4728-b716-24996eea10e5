package com.holderzone.holder.saas.aggregation.app.controller.business;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.manage.PrintSettingManage;
import com.holderzone.holder.saas.aggregation.app.service.feign.business.StoreConfigClientService;
import com.holderzone.saas.store.dto.business.manage.StoreConfigQueryDTO;
import com.holderzone.saas.store.dto.config.req.CashSettingReqDTO;
import com.holderzone.saas.store.dto.config.req.DineFoodSettingReqDTO;
import com.holderzone.saas.store.dto.config.req.PrintItemOrderConfigReq;
import com.holderzone.saas.store.dto.config.req.StoreFinishFoodConfigReq;
import com.holderzone.saas.store.dto.config.resp.CashSettingRespDTO;
import com.holderzone.saas.store.dto.config.resp.DineFoodSettingRespDTO;
import com.holderzone.saas.store.dto.config.resp.FinishFoodRespDTO;
import com.holderzone.saas.store.dto.order.request.AutoMarkReqDTO;
import com.holderzone.saas.store.dto.order.response.AutoMarkRespDTO;
import com.holderzone.saas.store.dto.print.PrintSettingDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreConfigAppController
 * @date 2018/09/04 11:26
 * @description app聚合层订单门店配置接口
 * @program holder-saas-aggregation-app
 */
@Slf4j
@RestController
@RequestMapping("/config")
@Api(description = "门店配置接口")
public class StoreConfigAppController {

    private final StoreConfigClientService storeConfigClientService;

    private final PrintSettingManage printSettingManage;

    @Autowired
    public StoreConfigAppController(StoreConfigClientService storeConfigClientService, PrintSettingManage printSettingManage) {
        this.storeConfigClientService = storeConfigClientService;
        this.printSettingManage = printSettingManage;
    }

    @ApiOperation(value = "更新自动号牌状态", notes = "更新自动号牌状态")
    @ApiImplicitParam(name = "autoMarkReqDTO", value = "autoMarkReqDTO", required = true, dataType =
            "AutoMarkReqDTO")
    @PostMapping("/update_auto_mark")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "更新自动号牌状态", action = OperatorType.UPDATE)
    public Result updateAutoMark(@RequestBody AutoMarkReqDTO autoMarkReqDTO) {
        log.info("更新自动号牌状态：{}", JacksonUtils.writeValueAsString(autoMarkReqDTO));
        return Result.buildSuccessResult(storeConfigClientService.updateAutoMark(autoMarkReqDTO));
    }

    @ApiOperation(value = "查询自动号牌状态", notes = "查询自动号牌状态")
    @ApiImplicitParam(name = "autoMarkReqDTO", value = "autoMarkReqDTO", required = true, dataType =
            "AutoMarkReqDTO")
    @PostMapping("/query_auto_mark")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "查询自动号牌状态",action = OperatorType.SELECT)
    public Result<Integer> queryAutoMark(@RequestBody AutoMarkReqDTO autoMarkReqDTO) {
        log.info("查询自动号牌状态：{}", JacksonUtils.writeValueAsString(autoMarkReqDTO));
        return Result.buildSuccessResult(storeConfigClientService.queryAutoMark(autoMarkReqDTO));
    }

    @ApiOperation(value = "查询自动号牌设置", notes = "查询自动号牌设置")
    @ApiImplicitParam(name = "autoMarkReqDTO", value = "autoMarkReqDTO", required = true, dataType = "AutoMarkReqDTO")
    @PostMapping("/auto_mark/query")
    public Result<AutoMarkRespDTO> queryAutoMarkResp(@RequestBody AutoMarkReqDTO autoMarkReqDTO) {
        log.info("查询自动号牌设置：{}", JacksonUtils.writeValueAsString(autoMarkReqDTO));
        return Result.buildSuccessResult(storeConfigClientService.queryAutoMarkResp(autoMarkReqDTO));
    }

    @ApiOperation(value = "更新出餐设置", notes = "更新出餐设置")
    @PostMapping("/update_finish_food")
    public Result<Boolean> updateFinishFood(@RequestBody @Validated StoreFinishFoodConfigReq finishFoodConfigReq) {
        log.info("[更新出餐设置]finishFoodConfigReq={}", JacksonUtils.writeValueAsString(finishFoodConfigReq));
        return Result.buildSuccessResult(storeConfigClientService.updateFinishFood(finishFoodConfigReq));
    }

    @ApiOperation(value = "查询出餐设置", notes = "查询出餐设置")
    @ApiImplicitParam(name = "configQueryDTO", value = "configQueryDTO", required = true, dataType = "StoreConfigQueryDTO")
    @PostMapping("/finish_food/query")
    public Result<FinishFoodRespDTO> queryFinishFood(@RequestBody @Validated StoreConfigQueryDTO configQueryDTO) {
        log.info("[查询出餐设置]configQueryDTO={}", JacksonUtils.writeValueAsString(configQueryDTO));
        return Result.buildSuccessResult(storeConfigClientService.queryFinishFood(configQueryDTO));
    }

    @ApiOperation(value = "查询商品打印顺序配置", notes = "查询商品打印顺序配置")
    @PostMapping("/print_item_order/query")
    public Result<PrintSettingDTO> queryPrintItemOrderConfig(@RequestBody @Validated StoreConfigQueryDTO configQueryDTO) {
        log.info("[查询商品打印顺序配置]configQueryDTO={}", JacksonUtils.writeValueAsString(configQueryDTO));
        return Result.buildSuccessResult(printSettingManage.getPrintSetting(configQueryDTO));
    }

    @ApiOperation(value = "修改商品打印顺序配置", notes = "修改商品打印顺序配置")
    @PostMapping("/print_item_order/update")
    public Result<Boolean> updatePrintItemOrderConfig(@RequestBody @Validated PrintItemOrderConfigReq configReq) {
        log.info("[修改商品打印顺序配置]configQueryDTO={}", JacksonUtils.writeValueAsString(configReq));
        return Result.buildSuccessResult(printSettingManage.printSet(configReq));
    }

    @ApiOperation(value = "查询正餐设置")
    @PostMapping("/dine_food_setting/query")
    public Result<DineFoodSettingRespDTO> queryDineFoodSetting(@RequestBody @Validated StoreConfigQueryDTO configQueryDTO) {
        log.info("[查询正餐设置]configQueryDTO={}", JacksonUtils.writeValueAsString(configQueryDTO));
        return Result.buildSuccessResult(storeConfigClientService.queryDineFoodSetting(configQueryDTO));
    }

    @ApiOperation(value = "更新正餐设置")
    @PostMapping("/dine_food_setting/update")
    public Result<Void> updateDineFoodSetting(@RequestBody @Validated DineFoodSettingReqDTO settingReqDTO) {
        log.info("[更新正餐设置]settingReqDTO={}", JacksonUtils.writeValueAsString(settingReqDTO));
        storeConfigClientService.updateDineFoodSetting(settingReqDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "收银设置", notes = "查询收银设置")
    @PostMapping("/cash_setting/query")
    public Result<CashSettingRespDTO> queryCashSetting(@RequestBody @Validated CashSettingReqDTO cashSettingReqDTO) {
        log.info("[查询收银设置cashSettingReqDTO]={}", JacksonUtils.writeValueAsString(cashSettingReqDTO));
        return Result.buildSuccessResult(storeConfigClientService.queryCashSetting(cashSettingReqDTO));
    }
}
