package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/7/30 9:55
 * @description 兼容jar包依赖冲突
 */
@Data
public class BasePage {
    @ApiModelProperty("每页查询数量")
    public Integer pageSize;
    @ApiModelProperty("当前页码")
    public Integer pageIndex;
    public Integer sortType;
    public String orderBy;

    public Integer getBeginIndex() {
        return this.pageIndex == null ? 0 : this.pageSize == null ? 0 : (this.pageIndex - 1) * this.pageSize;
    }
}
