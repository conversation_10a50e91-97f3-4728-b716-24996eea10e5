package com.holderzone.holder.saas.aggregation.app.controller.staff;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.organization.OrgFeignClient;
import com.holderzone.holder.saas.aggregation.app.service.feign.staff.ProductClientService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.organization.StoreBusinessDateDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.user.ThemeReqDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
/**
 * <AUTHOR>
 * @version 1.0
 * @className MessageController
 * @date 2018/09/25 11:05
 * @description
 * @program holder-saas-aggregation-app
 */
@Slf4j
@RestController
@Api(value = "主题")
@RequestMapping("/product")
public class ThemeController {

    private final ProductClientService productClientService;

    private final OrgFeignClient orgFeignClient;

    @Autowired
    public ThemeController(ProductClientService productClientService, OrgFeignClient orgFeignClient) {
        this.productClientService = productClientService;
        this.orgFeignClient = orgFeignClient;
    }

    @PostMapping("/query_theme")
//    @EFKOperationLogAop(
//            PLATFORM = Platform.MERCHANTBACK,
//            moduleName = ModuleNameType.HOLDER_SAAS_STORE_MESSAGE,
//            description = "查询产品主题"
//    )
    @ApiOperation(value = "查询产品主题：标准版=standard；专业版=professional，连锁版=chain")
    public Result<String> queryThemeCode(@RequestBody ThemeReqDTO themeReqDTO) {
        log.info("查询产品主题 themeReqDTO={}", JacksonUtils.writeValueAsString(themeReqDTO));
        return Result.buildSuccessResult(productClientService.queryThemeCode(themeReqDTO));
    }


    @PostMapping("/query_business_time")
    @ApiOperation(value = "查询营业时间")
    public Result<StoreDTO> queryBusinessTime(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("查询营业时间");
        List<String> guidList = new ArrayList<>();
        if (StringUtils.isEmpty(singleDataDTO.getStoreGuid())) {
            return Result.buildEmptySuccess();
        } else {
            log.info("查询营业时间入参：{}", singleDataDTO.getStoreGuid());
            guidList.add(singleDataDTO.getStoreGuid());
            List<StoreDTO> storeDTOS = orgFeignClient.queryStoreByGuidList(guidList);
            if (!ObjectUtils.isEmpty(storeDTOS)) {
                return Result.buildSuccessResult(storeDTOS.get(0));
            } else {
                return Result.buildEmptySuccess();
            }
        }
    }

    @PostMapping("/query_business_date_time")
    @ApiOperation(value = "查询营业时间段（带日期）")
    public Result<StoreBusinessDateDTO> queryBusinessDateTime(@RequestBody BaseDTO baseDTO) {
        log.info("查询营业时间，门店：{}，storeGuid：{}", baseDTO.getStoreName(), baseDTO.getStoreGuid());
        if (StringUtils.isEmpty(baseDTO.getStoreGuid())) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildSuccessResult(orgFeignClient.queryBusinessDate(baseDTO.getStoreGuid()));
        }
    }

}
