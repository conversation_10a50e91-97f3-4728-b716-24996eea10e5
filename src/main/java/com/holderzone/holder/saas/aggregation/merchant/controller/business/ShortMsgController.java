package com.holderzone.holder.saas.aggregation.merchant.controller.business;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.manage.ShortMsgClientService;
import com.holderzone.saas.store.dto.business.manage.ChargeDTO;
import com.holderzone.saas.store.dto.business.manage.ProductOrderDTO;
import com.holderzone.saas.store.dto.business.manage.ShortMsgRespDTO;
import com.holderzone.saas.store.dto.trade.ShortMsgConfigDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ShortMsgController
 * @date 2018/09/17 18:17
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Api(value = "短信充值")
@RestController
@RequestMapping("/shortMsg")
public class ShortMsgController {

    private static final Logger logger = LoggerFactory.getLogger(ShortMsgController.class);

    private final ShortMsgClientService shortMsgClientService;

    @Autowired
    public ShortMsgController(ShortMsgClientService shortMsgClientService) {
        this.shortMsgClientService = shortMsgClientService;
    }

    @ApiOperation(value = "首页")
    @PostMapping("/index")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "首页")
    public Result<List<ChargeDTO>> index() {
        logger.info("短信充值首页");
        List<ChargeDTO> all = shortMsgClientService.getAll();
        return Result.buildSuccessResult(all);
    }

    @ApiOperation(value = "充值")
    @PostMapping("/charge")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "充值")
    public Result<ShortMsgRespDTO> charge(@RequestBody ProductOrderDTO productOrderDTO) {
        logger.info("短信充值 productOrderDTO={}", JacksonUtils.writeValueAsString(productOrderDTO));
        ShortMsgRespDTO charge = shortMsgClientService.charge(productOrderDTO);
        return Result.buildSuccessResult(charge);
    }

    @ApiOperation(value = "轮询接口，返回codeUrl是二维码链接地址，paySt支付状态")
    @PostMapping("/polling")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "轮询接口，返回codeUrl是二维码链接地址，paySt支付状态")
    public Result<ShortMsgRespDTO> hasPaid(@RequestBody ShortMsgRespDTO shortMsgRespDTO) {
        logger.info("轮询接口 shortMsgRespDTO={}", JacksonUtils.writeValueAsString(shortMsgRespDTO));
        ShortMsgRespDTO respDTO = shortMsgClientService.polling(shortMsgRespDTO);
        return Result.buildSuccessResult(respDTO);
    }

    @ApiOperation(value = "查询短信条数接口")
    @PostMapping("/query")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "查询短信条数接口")
    public Result<ShortMsgConfigDTO> query() {
        logger.info("查询短信条数接口 shortMsgRespDTO={}");
        ShortMsgConfigDTO query = shortMsgClientService.query();
        return Result.buildSuccessResult(query);
    }

    @ApiOperation(value = "更新")
    @PostMapping("/update")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "更新")
    public Result<Boolean> update(@RequestBody ShortMsgConfigDTO shortMsgConfigDTO) {
        logger.info("更新短信发送配置 shortMsgConfigDTO={}", JacksonUtils.writeValueAsString(shortMsgConfigDTO));
        Boolean result = shortMsgClientService.update(shortMsgConfigDTO);
        if (result) {
            return Result.buildSuccessResult(result);
        }
        return Result.buildFailResult(1, "更新失败！！");
    }

}
