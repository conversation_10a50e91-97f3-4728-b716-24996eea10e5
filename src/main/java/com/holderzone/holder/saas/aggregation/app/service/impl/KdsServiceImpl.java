package com.holderzone.holder.saas.aggregation.app.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.config.ZhuanCanConfig;
import com.holderzone.holder.saas.aggregation.app.service.KdsService;
import com.holderzone.holder.saas.aggregation.app.service.feign.MessageClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.WeiXinClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.business.StoreConfigClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.kds.KitchenItemRpcService;
import com.holderzone.holder.saas.aggregation.app.service.feign.takeout.TakeoutClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.OrderItemClientService;
import com.holderzone.holder.saas.aggregation.app.utils.HttpsClientUtils;
import com.holderzone.saas.store.bo.FoodFinishBarCodeBO;
import com.holderzone.saas.store.constant.RedisKeyConstant;
import com.holderzone.saas.store.dto.business.manage.StoreConfigQueryDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.config.resp.FinishFoodRespDTO;
import com.holderzone.saas.store.dto.kds.req.ItemStateTransReqDTO;
import com.holderzone.saas.store.dto.kds.req.PrdDstItemQueryDTO;
import com.holderzone.saas.store.dto.kds.req.ScanFinishFoodReqDTO;
import com.holderzone.saas.store.dto.kds.resp.DistributeItemDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemTableDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderDTO;
import com.holderzone.saas.store.dto.weixin.open.WxOrderItemReqDTO;
import com.holderzone.saas.store.dto.weixin.open.WxOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.open.WxSendMessageReqDTO;
import com.holderzone.saas.store.dto.zhuancan.ZhuanCanCallMessageDTO;
import com.holderzone.saas.store.dto.zhuancan.ZhuanCanOrderItemMessageDTO;
import com.holderzone.saas.store.dto.zhuancan.ZhuanCanOrderMessageDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.kds.ScanFinishFoodTypeEnum;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/20
 * @description kds聚合层业务实现
 */
@Slf4j
@Service
@AllArgsConstructor
public class KdsServiceImpl implements KdsService {

    private final MessageClientService msgClientService;

    private final OrderItemClientService orderItemClientService;

    private final KitchenItemRpcService kitchenItemRpcService;

    private final StoreConfigClientService storeConfigClientService;

    private final TakeoutClientService takeoutClientService;

    private final WeiXinClientService weiXinClientService;

    private final ZhuanCanConfig zhuanCanConfig;

    private final RedisTemplate<String, String> stringRedisTemplate;

    @Override
    public void scanFinishFood(ScanFinishFoodReqDTO request) {
        // 查询订单信息
        Integer difference = ScanFinishFoodTypeEnum.DINNER.getCode();
        String foodFinishBarCode = request.getOrderNo();
        // 从缓存中获取打印记录，7天有效
        String foodFinishBarCodeKey = RedisKeyConstant.FOOD_FINISH_BAR_CODE + foodFinishBarCode;
        String barCodeBOJson = stringRedisTemplate.opsForValue().get(foodFinishBarCodeKey);
        log.info("barCodeBOJson={}", barCodeBOJson);
        if (StringUtils.isEmpty(barCodeBOJson)) {
            log.warn("[scanFinishFood]出餐码无效");
            return;
        }
        FoodFinishBarCodeBO barCodeBO = JacksonUtils.toObject(FoodFinishBarCodeBO.class, barCodeBOJson);
        String orderNo = barCodeBO.getOrderNo();

        // 订单查询
        OrderDTO orderDTO = orderItemClientService.findByOrderNoAndStoreGuid(orderNo, request.getStoreGuid());
        String orderDesc = "";
        if (!ObjectUtils.isEmpty(orderDTO)) {
            orderDesc = orderDTO.getMark();
            if (Objects.equals(TradeModeEnum.FAST.getCode(), orderDTO.getTradeMode())) {
                difference = ScanFinishFoodTypeEnum.FAST.getCode();
            }
        }
        if (ObjectUtils.isEmpty(orderDTO)) {
            log.warn("[scanFinishFood]未查询到订单信息");
            // 外卖订单
            TakeoutOrderDTO takeoutOrderDetail = takeoutClientService.getOrderByOrderNo(orderNo, request.getStoreGuid());
            if (ObjectUtils.isEmpty(takeoutOrderDetail)) {
                log.warn("[scanFinishFood]未查询到外卖订单信息");
                return;
            }
            difference = ScanFinishFoodTypeEnum.TAKEAWAY.getCode();
            orderDesc = takeoutOrderDetail.getOrderDaySn();
        }

        // 查询未出堂商品
        PrdDstItemQueryDTO queryDTO = new PrdDstItemQueryDTO();
        queryDTO.setOrderItemGuidList(barCodeBO.getOrderItemGuidList());
        List<PrdDstItemDTO> prdDstItemDTOList = kitchenItemRpcService.queryByOrder(queryDTO);
        log.info("[scanFinishFood][queryByOrder]prdDstItemDTOList={}", JacksonUtils.writeValueAsString(prdDstItemDTOList));
        // 批量出堂商品
        batchDistribute(request, prdDstItemDTOList, difference, orderDesc);

        // 查询出餐设置
        StoreConfigQueryDTO configQueryDTO = new StoreConfigQueryDTO();
        configQueryDTO.setStoreGuid(request.getStoreGuid());
        FinishFoodRespDTO finishFoodConfig = storeConfigClientService.queryFinishFood(configQueryDTO);
        if (Objects.equals(BooleanEnum.TRUE.getCode(), finishFoodConfig.getFinishFoodVoiceSwitch())
                && Objects.nonNull(orderDTO) && TradeModeEnum.FAST.getCode() == orderDTO.getTradeMode()) {
            // 向一体机推送语音消息
            finishFoodVoiceMsg(request.getStoreGuid(), request.getStoreName(), request.getDeviceId(), orderDesc);
        }

        // 调用赚餐推送小程序消息给用户
        sendCallMessage(orderDTO);

        if (CollectionUtils.isEmpty(prdDstItemDTOList)) {
            log.warn("[scanFinishFood]没有出餐商品则不用通知");
            return;
        }

        // h5发送消息
        WxStoreMerchantOrderDTO merchantOrderPhone = weiXinClientService.getMerchantOrderPhone(orderDTO.getGuid());
        if (ObjectUtils.isEmpty(merchantOrderPhone)) {
            log.warn("[scanFinishFood]未查询到微信订单信息");
            return;
        }
        wxSendCallMessage(prdDstItemDTOList, Lists.newArrayList(merchantOrderPhone));
    }

    private void batchDistribute(ScanFinishFoodReqDTO request,
                                 List<PrdDstItemDTO> prdDstItemDTOList,
                                 Integer difference,
                                 String orderDesc) {
        if (!CollectionUtils.isEmpty(prdDstItemDTOList)) {
            // 根据商品sku查询对应出堂设备id
            SingleDataDTO singleDataDTO = new SingleDataDTO();
            singleDataDTO.setData(request.getStoreGuid());
            List<String> skuGuidList = prdDstItemDTOList.stream()
                    .map(PrdDstItemDTO::getSkuGuid)
                    .filter(StringUtils::hasText)
                    .distinct()
                    .collect(Collectors.toList());
            singleDataDTO.setDatas(skuGuidList);
            List<DistributeItemDTO> itemDTOList = kitchenItemRpcService.queryDistributeItemBySku(singleDataDTO);
            log.info("[根据sku查询出堂商品配置],itemDTOList={}", JacksonUtils.writeValueAsString(itemDTOList));
            if (!CollectionUtils.isEmpty(itemDTOList)) {
                Map<String, List<DistributeItemDTO>> deviceIdItemDTOMap = itemDTOList.stream()
                        .collect(Collectors.groupingBy(DistributeItemDTO::getDeviceId));
                // 调用出堂接口
                List<ItemStateTransReqDTO> reqDistributeList = new ArrayList<>();
                for (Map.Entry<String, List<DistributeItemDTO>> entry : deviceIdItemDTOMap.entrySet()) {
                    String deviceId = entry.getKey();
                    List<DistributeItemDTO> itemList = entry.getValue();
                    ItemStateTransReqDTO reqDistributeDTO = getReqDistributeDTO(request, prdDstItemDTOList, itemList);
                    reqDistributeDTO.setOrderDifference(difference);
                    reqDistributeDTO.setOrderDesc(orderDesc);
                    reqDistributeDTO.setDeviceId(deviceId);
                    reqDistributeList.add(reqDistributeDTO);
                }
                kitchenItemRpcService.batchDistribute(reqDistributeList);
            }
        }
    }

    /**
     * 调用赚餐推送小程序消息给用户
     *
     * @param orderDTO 订单信息
     */
    @Override
    public void sendCallMessage(OrderDTO orderDTO) {
        if (!ObjectUtils.isEmpty(orderDTO) && TradeModeEnum.FAST.getCode() == orderDTO.getTradeMode()) {
            String orderGuid = orderDTO.getGuid();
            WxStoreMerchantOrderDTO merchantOrderPhone = weiXinClientService.getMerchantOrderPhone(orderGuid);
            log.info("[扫码出餐]查询微信订单信息={},请求订单Guid为={}", JacksonUtils.writeValueAsString(merchantOrderPhone), orderGuid);
            if (ObjectUtil.isNotNull(merchantOrderPhone)) {
                // 赚餐推送小程序消息
                Map<String, Object> params = new HashMap<>();
                String orderRecordGuid = merchantOrderPhone.getOrderRecordGuid();
                params.put("orderGuid", orderRecordGuid);
                String zcResponse = HttpsClientUtils.doGet(zhuanCanConfig.getSendCallMessage(), params);
                log.info("[扫码出餐][赚餐推送小程序消息]返回={}", zcResponse);
            }
        }
    }

    /**
     * 批量发送叫号通知
     *
     * @param prdDstItemList 出堂商品
     */
    @Override
    public void distributeBatchSendCallMessage(List<PrdDstItemDTO> prdDstItemList) {
        SingleDataDTO dataDTO = new SingleDataDTO();
        List<String> fastOrderGuidList = getFastOrderGuidList(prdDstItemList);
        dataDTO.setDatas(fastOrderGuidList);
        List<WxStoreMerchantOrderDTO> merchantOrderDTOList = weiXinClientService.listByOrderGuid(dataDTO);
        log.info("[批量发送叫号通知][微信订单]merchantOrderDTOList={}", JacksonUtils.writeValueAsString(merchantOrderDTOList));
        if (CollectionUtils.isEmpty(merchantOrderDTOList)) {
            log.warn("出堂商品非小程序点餐");
            return;
        }
        // 赚餐发送消息
        zcSendCallMessage(prdDstItemList, merchantOrderDTOList);

        // h5发送消息
        wxSendCallMessage(prdDstItemList, merchantOrderDTOList);
    }

    /**
     * h5发送消息
     */
    private void wxSendCallMessage(List<PrdDstItemDTO> prdDstItemList,
                                   List<WxStoreMerchantOrderDTO> merchantOrderDTOList) {
        Map<String, List<WxOrderItemReqDTO>> wxOrderItemMap = buildWxOrderItemMap(prdDstItemList);
        WxSendMessageReqDTO wxSendMessageReqDTO = new WxSendMessageReqDTO();
        List<WxOrderReqDTO> orderList = new ArrayList<>();
        List<String> wxOrderGuidList = merchantOrderDTOList.stream()
                .map(WxStoreMerchantOrderDTO::getOrderGuid)
                .distinct()
                .collect(Collectors.toList());
        wxOrderGuidList.forEach(wxOrderGuid -> {
            List<WxOrderItemReqDTO> wxOrderItemList = wxOrderItemMap.get(wxOrderGuid);
            if (CollectionUtils.isEmpty(wxOrderItemList)) {
                wxOrderItemList = new ArrayList<>();
            }
            WxOrderReqDTO orderReqDTO = new WxOrderReqDTO();
            orderReqDTO.setOrderGuid(wxOrderGuid);
            orderReqDTO.setOrderItemList(wxOrderItemList);
            orderList.add(orderReqDTO);
        });
        wxSendMessageReqDTO.setOrderList(orderList);
        weiXinClientService.sendCallMessage(wxSendMessageReqDTO);
        log.info("h5发送消息完成");
    }

    /**
     * 赚餐发送消息
     */
    private void zcSendCallMessage(List<PrdDstItemDTO> prdDstItemList, List<WxStoreMerchantOrderDTO> merchantOrderDTOList) {
        Map<String, String> wxOrderMap = merchantOrderDTOList.stream()
                .collect(Collectors.toMap(WxStoreMerchantOrderDTO::getOrderRecordGuid, WxStoreMerchantOrderDTO::getOrderGuid, (v1, v2) -> v1));
        Map<String, List<ZhuanCanOrderItemMessageDTO>> orderItemMap = buildOrderItemMap(prdDstItemList);
        List<ZhuanCanOrderMessageDTO> zhuanCanOrderList = new ArrayList<>();
        wxOrderMap.forEach((zhuanCanOrderGuid, orderGuid) -> {
            ZhuanCanOrderMessageDTO messageDTO = new ZhuanCanOrderMessageDTO();
            messageDTO.setZhuanCanOrderSn(zhuanCanOrderGuid);
            List<ZhuanCanOrderItemMessageDTO> itemMessageDTOList = orderItemMap.get(orderGuid);
            if (CollectionUtils.isEmpty(itemMessageDTOList)) {
                itemMessageDTOList = new ArrayList<>();
            }
            messageDTO.setOrderItemList(itemMessageDTOList);
            zhuanCanOrderList.add(messageDTO);
        });
        ZhuanCanCallMessageDTO zhuanCan = new ZhuanCanCallMessageDTO();
        zhuanCan.setZhuanCanOrderList(zhuanCanOrderList);
        String zcResponse = HttpsClientUtils.doPost(zhuanCanConfig.getBatchSendCallMessage(), JacksonUtils.writeValueAsString(zhuanCan));
        log.info("[赚餐推送消息][批量发送叫号通知]返回={}", zcResponse);
    }

    private Map<String, List<WxOrderItemReqDTO>> buildWxOrderItemMap(List<PrdDstItemDTO> prdDstItemList) {
        Map<String, List<WxOrderItemReqDTO>> wxOrderItemMap = new HashMap<>();
        for (PrdDstItemDTO dstItem : prdDstItemList) {
            List<PrdDstItemTableDTO> kitchenItemList = dstItem.getKitchenItemList();
            if (!CollectionUtils.isEmpty(kitchenItemList)) {
                Map<String, List<PrdDstItemTableDTO>> orderItemMap = kitchenItemList.stream()
                        .collect(Collectors.groupingBy(PrdDstItemTableDTO::getOrderGuid));

                orderItemMap.forEach((orderGuid, dstItemList) -> {
                    List<WxOrderItemReqDTO> itemList = wxOrderItemMap.get(orderGuid);
                    WxOrderItemReqDTO wxOrderItemReqDTO = new WxOrderItemReqDTO();
                    wxOrderItemReqDTO.setItemGuid(dstItem.getItemGuid());
                    wxOrderItemReqDTO.setItemName(dstItem.getItemName());
                    wxOrderItemReqDTO.setSkuGuid(dstItem.getSkuGuid());
                    wxOrderItemReqDTO.setSkuName(dstItem.getSkuName());
                    wxOrderItemReqDTO.setNumber(dstItemList.size());

                    if (CollectionUtils.isEmpty(itemList)) {
                        List<WxOrderItemReqDTO> newSkuGuidSet = new ArrayList<>();
                        newSkuGuidSet.add(wxOrderItemReqDTO);
                        wxOrderItemMap.put(orderGuid, newSkuGuidSet);
                        return;
                    }
                    itemList.add(wxOrderItemReqDTO);
                });
            }
        }
        return wxOrderItemMap;
    }

    @NotNull
    private List<String> getFastOrderGuidList(List<PrdDstItemDTO> prdDstItemList) {
        List<PrdDstItemTableDTO> dstItemTableDTOList = prdDstItemList.stream()
                .filter(prd -> !CollectionUtils.isEmpty(prd.getKitchenItemList()))
                .flatMap(prd -> prd.getKitchenItemList().stream()).collect(Collectors.toList());

        Map<String, List<PrdDstItemTableDTO>> orderMap = dstItemTableDTOList.stream()
                .collect(Collectors.groupingBy(PrdDstItemTableDTO::getOrderGuid));

        SingleDataDTO dto = new SingleDataDTO();
        Set<String> orderGuidSet = orderMap.keySet();
        dto.setDatas(Lists.newArrayList(orderGuidSet));
        List<OrderDTO> orderDTOList = orderItemClientService.listByOrderGuid(dto);
        List<OrderDTO> fastOrderList = orderDTOList.stream()
                .filter(orderDTO -> !ObjectUtils.isEmpty(orderDTO) && TradeModeEnum.FAST.getCode() == orderDTO.getTradeMode())
                .collect(Collectors.toList());
        return fastOrderList.stream()
                .map(OrderDTO::getGuid)
                .distinct()
                .collect(Collectors.toList());
    }

    @NotNull
    private Map<String, List<ZhuanCanOrderItemMessageDTO>> buildOrderItemMap(List<PrdDstItemDTO> prdDstItemList) {
        Map<String, List<ZhuanCanOrderItemMessageDTO>> orderSkuMap = new HashMap<>();
        for (PrdDstItemDTO dstItem : prdDstItemList) {
            List<PrdDstItemTableDTO> kitchenItemList = dstItem.getKitchenItemList();
            if (!CollectionUtils.isEmpty(kitchenItemList)) {
                Map<String, List<PrdDstItemTableDTO>> orderItemMap = kitchenItemList.stream()
                        .collect(Collectors.groupingBy(PrdDstItemTableDTO::getOrderGuid));

                orderItemMap.forEach((orderGuid, dstItemList) -> {
                    List<ZhuanCanOrderItemMessageDTO> inSkuGuidSet = orderSkuMap.get(orderGuid);
                    ZhuanCanOrderItemMessageDTO messageItemDTO = getOrderItemMessageDTO(dstItem, orderGuid, dstItemList);

                    if (CollectionUtils.isEmpty(inSkuGuidSet)) {
                        List<ZhuanCanOrderItemMessageDTO> newSkuGuidSet = new ArrayList<>();
                        newSkuGuidSet.add(messageItemDTO);
                        orderSkuMap.put(orderGuid, newSkuGuidSet);
                        return;
                    }
                    inSkuGuidSet.add(messageItemDTO);
                });
            }
        }
        return orderSkuMap;
    }

    @NotNull
    private ZhuanCanOrderItemMessageDTO getOrderItemMessageDTO(PrdDstItemDTO dstItem,
                                                               String orderGuid,
                                                               List<PrdDstItemTableDTO> dstItemList) {
        ZhuanCanOrderItemMessageDTO messageItemDTO = new ZhuanCanOrderItemMessageDTO();
        messageItemDTO.setItemGuid(dstItem.getItemGuid());
        messageItemDTO.setItemName(dstItem.getItemName());
        messageItemDTO.setSkuGuid(dstItem.getSkuGuid());
        messageItemDTO.setSkuName(dstItem.getSkuName());
        messageItemDTO.setOrderGuid(orderGuid);
        // 如果以后扫码点餐可以点称重商品，这里就会有问题
        messageItemDTO.setNumber(dstItemList.size());
        return messageItemDTO;
    }

    private ItemStateTransReqDTO getReqDistributeDTO(ScanFinishFoodReqDTO request,
                                                     List<PrdDstItemDTO> prdDstItemDTOList,
                                                     List<DistributeItemDTO> itemList) {
        ItemStateTransReqDTO reqDistributeDTO = new ItemStateTransReqDTO();
        List<String> skuGuidList = itemList.stream()
                .map(DistributeItemDTO::getSkuGuid)
                .collect(Collectors.toList());
        List<PrdDstItemDTO> prdDstItemList = prdDstItemDTOList.stream()
                .filter(prd -> skuGuidList.contains(prd.getSkuGuid()))
                .collect(Collectors.toList());
        reqDistributeDTO.setPrdDstItemList(prdDstItemList);
        reqDistributeDTO.setDeviceType(BaseDeviceTypeEnum.KDS.getCode());
        reqDistributeDTO.setEnterpriseGuid(request.getEnterpriseGuid());
        reqDistributeDTO.setEnterpriseName(request.getEnterpriseName());
        reqDistributeDTO.setStoreGuid(request.getStoreGuid());
        reqDistributeDTO.setStoreName(request.getStoreName());
        reqDistributeDTO.setUserGuid(request.getUserGuid());
        reqDistributeDTO.setUserName(request.getUserName());
        reqDistributeDTO.setAccount(request.getAccount());
        reqDistributeDTO.setRequestTimestamp(request.getRequestTimestamp());
        return reqDistributeDTO;
    }

    /**
     * 发送出餐语音消息
     */
    private void finishFoodVoiceMsg(String storeGuid, String storeName, String deviceId, String orderDesc) {
        BusinessMessageDTO build = BusinessMessageDTO.builder()
                .storeGuid(storeGuid)
                .storeName(storeName)
                .messageType(1)
                .detailMessageType(BusinessMsgTypeEnum.MEAL_VOICE_REMINDERS.getId())
                .platform("2")
                .subject("请" + orderDesc + "号顾客前来取餐")
                .content(deviceId)
                .build();
        log.info("[出餐语音消息],build={}", JacksonUtils.writeValueAsString(build));
        msgClientService.msg(build);
    }
}
