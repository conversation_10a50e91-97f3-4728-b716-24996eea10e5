package com.holderzone.holder.saas.aggregation.merchant.util;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Chinese2PinyinUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.erp.InOutDocumentDetailSelectDTO;
import com.holderzone.saas.store.dto.erp.InOutDocumentMaterialDetailImportDTO;
import com.holderzone.saas.store.dto.erp.MaterialDTO;
import com.holderzone.saas.store.dto.erp.MaterialUnitDTO;
import com.holderzone.saas.store.dto.takeaway.UnMappedItem;
import com.holderzone.saas.store.enums.takeaway.TakeawayQueryTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/05/08 16:57
 */
@Slf4j
public class MaterialExcelUtil {
    public static List<MaterialDTO> getMaterialDataByFile(MultipartFile multipartFile, List<MaterialUnitDTO> materialUnitDTOList, String storeGuid) throws IOException {
        InputStream inputStream = multipartFile.getInputStream();
        String originalFilename = multipartFile.getOriginalFilename();
        if (!StringUtils.isEmpty(originalFilename) && !originalFilename.matches("^.+\\.(?i)(xls)$") && !originalFilename.matches("^.+\\.(?i)(xlsx)$")) {
            throw new BusinessException("上传文件格式不正确");
        }
        int firstDataRowIndex = 2;
        Sheet sheet;
        try (Workbook workbook = WorkbookFactory.create(inputStream)) {
            sheet = workbook.getSheetAt(0);
            //解析获取excel数据
            List<List<String>> sheetData = ExcelUtil.getSheetData(sheet, firstDataRowIndex, 0);
            if (sheetData.isEmpty()) {
                return Collections.emptyList();
            }
            //根据数据构建MaterialDTO
            return buildMaterialList(sheetData, materialUnitDTOList, storeGuid, firstDataRowIndex);
        } catch (Exception cause) {
            log.error("物料导入失败", cause);
            throw new BusinessException(cause.getMessage());
        }
    }

    private static List<MaterialDTO> buildMaterialList(List<List<String>> dataList, List<MaterialUnitDTO> materialUnitDTOList, String storeGuid, int firstDataRowIndex) {
        List<MaterialDTO> materialDTOList = new ArrayList<>(dataList.size());
        for (int i = 0; i < dataList.size(); i++) {
            List<String> strings = dataList.get(i);
            //校验数据合法性
            validateMaterial(materialDTOList, strings, firstDataRowIndex + i + 1);
            MaterialDTO materialDTO = new MaterialDTO();
            materialDTO.setName(strings.get(0).trim());
            //设置简码
            materialDTO.setSimpleName(Chinese2PinyinUtils.initials(strings.get(0).trim()));
            List<MaterialUnitDTO> collect = materialUnitDTOList.stream().filter(unit -> unit.getName().equalsIgnoreCase(strings.get(2))).collect(Collectors.toList());
            if (collect.isEmpty()) {
                throw new BusinessException("第" + (firstDataRowIndex + i + 1) + "行的单位非法");
            }
            materialDTO.setUnit(collect.get(0).getGuid());
            materialDTO.setSpecs(strings.size() > 3 ? strings.get(3) : null);
            materialDTO.setType("原料".equals(strings.get(1)) ? "1" : "0");
            //条码
            String barCode = strings.size() > 4 ? strings.get(4) : null;
            if (!StringUtils.isEmpty(barCode)) {
                int index = barCode.indexOf(".");
                barCode = index > 0 ? barCode.substring(0, index) : barCode;
                materialDTO.setBarCode(barCode);
            }
            //设置门店GUID
            materialDTO.setStoreGuid(storeGuid);
            materialDTOList.add(materialDTO);
        }
        return materialDTOList;
    }

    /**
     * 校验数据合法性
     *
     * @param materialDTOS
     * @param dataSheet
     * @param rowIndex
     */
    private static void validateMaterial(List<MaterialDTO> materialDTOS, List<String> dataSheet, int rowIndex) {
        String name = dataSheet.size() > 0 ? dataSheet.get(0) : "";
        String type = dataSheet.size() > 1 ? dataSheet.get(1) : "";
        String unit = dataSheet.size() > 2 ? dataSheet.get(2) : "";
        String spec = dataSheet.size() > 3 ? dataSheet.get(3) : "";
        String barCode = dataSheet.size() > 4 ? dataSheet.get(4) : "";
        String regex = "^[A-Za-z0-9]{4,30}+(.0)?$";
        Pattern pattern = Pattern.compile(regex);
        if (StringUtils.isEmpty(name) || (!StringUtils.isEmpty(name) && name.trim().length() > 40)) {
            throw new BusinessException("第" + rowIndex + "行物料名称非法");
        }
        if (StringUtils.isEmpty(type) || (!StringUtils.isEmpty(type) && !("原料".equals(type) || "物资".equals(type)))) {
            throw new BusinessException("第" + rowIndex + "行类型非法");
        }
        if (StringUtils.isEmpty(unit)) {
            throw new BusinessException("第" + rowIndex + "行单位不能为空");
        }
        if (!StringUtils.isEmpty(spec) && spec.trim().length() > 10) {
            throw new BusinessException("第" + rowIndex + "行规格信息不能超过10个长度");
        }
        if (!StringUtils.isEmpty(barCode) && !pattern.matcher(barCode).matches()) {
            throw new BusinessException("第" + rowIndex + "行条码信息不符合规范");
        }
        Optional<MaterialDTO> any = materialDTOS.stream().filter(materialDTO -> materialDTO.getName().equals(name)).findAny();
        if (any.isPresent()) {
            throw new BusinessException("第" + rowIndex + "行物料信息重复:" + name);
        }
    }

    /**
     * 获取模板对应的XSSFWorkbook
     *
     * @return
     * @throws IOException
     */
    public static XSSFWorkbook getDownloadWorkBook(String downloadExcel) throws IOException {
        XSSFWorkbook wb = null;
        FileInputStream fis = null;
        Resource resource = new ClassPathResource(downloadExcel);
        InputStream inputStream = resource.getInputStream();
        try {
            if (inputStream != null) {
                wb = new XSSFWorkbook(inputStream);
            }
        } finally {
            if (fis != null) {
                fis.close();
            }
        }
        return wb;
    }

    /**
     * 导出物料信息
     *
     * @param materialDTOList
     * @throws IOException
     */
    public static XSSFWorkbook exportMaterial(List<MaterialDTO> materialDTOList) throws IOException {
        XSSFWorkbook workBook = getDownloadWorkBook("downloadExcel/downloadMaterial.xlsx");
        XSSFSheet sheet = workBook.getSheetAt(0);
        int rowNum = sheet.getLastRowNum();
        for (MaterialDTO materialDTO : materialDTOList) {
            XSSFRow xssfRow = ExcelUtil.createXSSFRow(sheet, rowNum + 1);
            buildMaterialCell(materialDTO, xssfRow);
            rowNum++;
        }
        //将构建好的单元格数据返回
        return workBook;
    }

    /**
     * 按照excel模板构建cell数据
     *
     * @param materialDTO
     * @param xssfRow
     */
    private static void buildMaterialCell(MaterialDTO materialDTO, XSSFRow xssfRow) {
        XSSFCell nameCell = xssfRow.createCell(0);
        nameCell.setCellValue(materialDTO.getName());
        XSSFCell typeCell = xssfRow.createCell(1);
        typeCell.setCellValue("0".equals(materialDTO.getType()) ? "物资" : "原料");
        XSSFCell unitCell = xssfRow.createCell(2);
        unitCell.setCellValue(materialDTO.getUnitName());
        XSSFCell specCell = xssfRow.createCell(3);
        specCell.setCellValue(materialDTO.getSpecs());
        XSSFCell barCell = xssfRow.createCell(4);
        barCell.setCellValue(materialDTO.getBarCode());
    }

    /**
     * 构建物料导出的文件名字,null则为导出物料信息,否则按指定的物料名称导出
     *
     * @param fileName
     * @return
     * @throws UnsupportedEncodingException
     */
    private static String createFileName(String fileName) throws UnsupportedEncodingException {
        if (StringUtils.isEmpty(fileName)) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String format = simpleDateFormat.format(new Date());
            String name = URLEncoder.encode("物料信息" + "_" + format + ".xlsx", "UTF-8");
            return name;
        }
        String name = URLEncoder.encode(fileName + ".xlsx", "UTF-8");
        return name;
    }

    /**
     * 构建物料导出文件信息
     *
     * @param httpServletResponse
     * @param xssfWorkbook
     * @param fileName
     * @throws Exception
     */
    public static void exportFile(HttpServletResponse httpServletResponse, XSSFWorkbook xssfWorkbook, String fileName) throws Exception {
        httpServletResponse.setHeader("Content-Disposition", "attachment;filename=" + createFileName(fileName));
        httpServletResponse.setContentType("application/octet-stream;charset=UTF-8");
        xssfWorkbook.write(httpServletResponse.getOutputStream());
    }

    public static void main(String[] args) {
        String regex = "^[A-Za-z0-9]{4,30}+(.0)?$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher("1234561.0");
        System.out.println(matcher.matches());
        String str = "123456.1";
        System.out.println(str.indexOf("."));
        System.out.println(str.substring(0, str.indexOf(".")));
    }

    public static XSSFWorkbook exportStock(List<MaterialDTO> stockList) throws IOException {
        XSSFWorkbook workBook = getDownloadWorkBook("downloadExcel/downloadStock.xlsx");
        if (Objects.nonNull(workBook)) {
            XSSFSheet sheet = workBook.getSheetAt(0);
            int rowNum = sheet.getLastRowNum();
            for (int i = 0; i < stockList.size(); i++) {
                MaterialDTO stockDTO = stockList.get(i);
                XSSFRow xssfRow = ExcelUtil.createXSSFRow(sheet, rowNum + 1);
                buildStockCell(stockDTO, xssfRow, i + 1);
                rowNum++;
            }
        }
        return workBook;
    }

    private static void buildStockCell(MaterialDTO stockDTO, XSSFRow xssfRow, int order) {
        XSSFCell orderCell = xssfRow.createCell(0);
        log.info("order={}", order);
        orderCell.setCellValue(String.valueOf(order));

        XSSFCell codeCell = xssfRow.createCell(1);
        codeCell.setCellValue(stockDTO.getCode());

        XSSFCell nameCell = xssfRow.createCell(2);
        nameCell.setCellValue(stockDTO.getName());

        XSSFCell categoryCell = xssfRow.createCell(3);
        categoryCell.setCellValue(stockDTO.getCategoryName());

        XSSFCell simpleNameCell = xssfRow.createCell(4);
        simpleNameCell.setCellValue(stockDTO.getSimpleName());

        XSSFCell typeCell = xssfRow.createCell(5);
        typeCell.setCellValue("0".equals(stockDTO.getType()) ? "物资" : "原料");

        XSSFCell stockCell = xssfRow.createCell(6);
        stockCell.setCellValue(stockDTO.getStock().stripTrailingZeros().toPlainString());

        XSSFCell unitNameCell = xssfRow.createCell(7);
        unitNameCell.setCellValue(stockDTO.getUnitName());

        XSSFCell inUnitPriceCell = xssfRow.createCell(8);
        inUnitPriceCell.setCellValue(stockDTO.getInUnitPrice().stripTrailingZeros().toPlainString());

        XSSFCell stockStateCell = xssfRow.createCell(9);
        String stockState = "正常";
        if (stockDTO.getStock().compareTo(stockDTO.getLowestStock()) < 0) {
            stockState = "过低";
        }
        stockStateCell.setCellValue(stockState);
    }

    public static XSSFWorkbook exportTakeawayItems(List<UnMappedItem> mappedItemList, Integer takeoutType) throws IOException {
        XSSFWorkbook workBook = getDownloadWorkBook("downloadExcel/downloadTakeawayItems.xlsx");
        if (Objects.nonNull(workBook)) {
            XSSFSheet sheet = workBook.getSheetAt(0);
            int rowNum = sheet.getLastRowNum();
            for (UnMappedItem unMappedItem : mappedItemList) {
                XSSFRow xssfRow = ExcelUtil.createXSSFRow(sheet, rowNum + 1);
                buildTakeawayItemsCell(unMappedItem, xssfRow, takeoutType);
                rowNum++;
            }
        }
        return workBook;
    }

    public static List<InOutDocumentMaterialDetailImportDTO> getInOutDocumentMaterialDataByFile(MultipartFile multipartFile) throws IOException {
        InputStream inputStream = multipartFile.getInputStream();
        String originalFilename = multipartFile.getOriginalFilename();
        if (!StringUtils.isEmpty(originalFilename) && !originalFilename.matches("^.+\\.(?i)(xls)$") && !originalFilename.matches("^.+\\.(?i)(xlsx)$")) {
            throw new BusinessException("上传文件格式不正确");
        }
        int firstDataRowIndex = 2;
        Sheet sheet;
        try (Workbook workbook = WorkbookFactory.create(inputStream)) {
            sheet = workbook.getSheetAt(0);
            //解析获取excel数据
            List<List<String>> sheetData = ExcelUtil
                    .getSheetDataWithDataFormatter(sheet, firstDataRowIndex, -1, 0, -1);
            if (sheetData.isEmpty()) {
                return Collections.emptyList();
            }
            return getInOutDocumentMaterialDetailImportDTOS(sheetData);
        } catch (Exception cause) {
            log.error("新建出入库单据时批量导入物料失败", cause);
            throw new BusinessException(cause.getMessage());
        }
    }

    private static List<InOutDocumentMaterialDetailImportDTO> getInOutDocumentMaterialDetailImportDTOS(List<List<String>> sheetData) {
        List<InOutDocumentMaterialDetailImportDTO> inOutDocumentMaterialDetailImportDTOList = new ArrayList<>(sheetData.size());
        //根据数据构建MaterialDTO
        for (List<String> data : sheetData) {
            if (CollectionUtils.isEmpty(data)) {
                continue;
            }
            InOutDocumentMaterialDetailImportDTO dto = new InOutDocumentMaterialDetailImportDTO();
            dto.setMaterialCode(getSheetData(data, 0));
            dto.setMaterialName(getSheetData(data, 1));
            dto.setCount(getSheetData(data, 2));
            dto.setUnitName(getSheetData(data, 3));
            dto.setUnitPrice(getSheetData(data, 4));
            inOutDocumentMaterialDetailImportDTOList.add(dto);
        }
        return inOutDocumentMaterialDetailImportDTOList;
    }

    private static String getSheetData(List<String> data, int columnIndex) {
        return columnIndex < data.size() && Objects.nonNull(data.get(columnIndex)) ? data.get(columnIndex).trim() : null;
    }

    private static void buildTakeawayItemsCell(UnMappedItem unMappedItem, XSSFRow xssfRow, int takeoutType) {
        XSSFCell belongBrandNameCell = xssfRow.createCell(0);
        belongBrandNameCell.setCellValue(unMappedItem.getBelongBrandName());

        // 0：美团，1：饿了么,2:自营,3:赚餐
        XSSFCell takeoutTypeCell = xssfRow.createCell(1);
        takeoutTypeCell.setCellValue(TakeawayQueryTypeEnum.getDescByCode(takeoutType));

        XSSFCell erpStoreNameCell = xssfRow.createCell(2);
        erpStoreNameCell.setCellValue(unMappedItem.getErpStoreName());

        XSSFCell unItemTypeNameCell = xssfRow.createCell(3);
        unItemTypeNameCell.setCellValue(unMappedItem.getUnItemTypeName());

        XSSFCell unItemNameWithSkuCell = xssfRow.createCell(4);
        unItemNameWithSkuCell.setCellValue(unMappedItem.getUnItemNameWithSku());

        XSSFCell unItemSkuIdCell = xssfRow.createCell(5);
        unItemSkuIdCell.setCellValue(unMappedItem.getUnItemSkuId());

        XSSFCell erpItemNameWithSkuCell = xssfRow.createCell(6);
        erpItemNameWithSkuCell.setCellValue(Objects.isNull(unMappedItem.getErpItemNameWithSku()) ? "" : unMappedItem.getErpItemNameWithSku());

        XSSFCell erpItemSkuIdCell = xssfRow.createCell(7);
        erpItemSkuIdCell.setCellValue(Objects.isNull(unMappedItem.getErpItemSkuId()) ? "" : unMappedItem.getErpItemSkuId());

        XSSFCell unItemCountMapperCell = xssfRow.createCell(8);
        unItemCountMapperCell.setCellValue(Objects.isNull(unMappedItem.getUnItemCountMapper()) ? "" : unMappedItem.getUnItemCountMapper().toString());

        XSSFCell bindStateCell = xssfRow.createCell(9);
        bindStateCell.setCellValue(Objects.isNull(unMappedItem.getErpItemSkuId()) ? "未绑定" : "已绑定");

        XSSFCell erpItemIsRackCell = xssfRow.createCell(10);
        String erpItemIsRack = "";
        if (Objects.nonNull(unMappedItem.getErpItemIsRack())) {
            erpItemIsRack = unMappedItem.getErpItemIsRack() == 1 ? "上架" : "下架";
        }
        erpItemIsRackCell.setCellValue(erpItemIsRack);
    }
}
