package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.baseresource;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.dto.baseresource.request.HsmStoreReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.request.ShopQueryReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.response.HsmStoreRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HsmStoreService
 * @date 2019/05/31 15:34
 * @description 门店
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-member-account", fallbackFactory = HsmStoreService.HsmStoreServiceFallBack.class)
public interface HsmStoreService {

    /**
     * 根据分页进行查询
     *
     * @param shopQueryReqDTO 条件
     * @return 分页的结果
     */
    @ApiOperation("根据条件进行分页")
    @PostMapping(value = "/hsm/store/query/page", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Page<HsmStoreRespDTO> findPageByCondition(@RequestBody ShopQueryReqDTO shopQueryReqDTO);

    /**
     * 根据查询条件进行列表查询
     *
     * @param shopQueryReqDTO 条件
     * @return 全列表
     */
    @ApiOperation("根据查询条件进行列表查询")
    @PostMapping(value = "/hsm/store/query/list", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    List<HsmStoreRespDTO> findListByCondition(@RequestBody ShopQueryReqDTO shopQueryReqDTO);

    /**
     * 根据品牌GUID进行列表查询
     *
     * @return java.util.List<com.holderzone.holder.saas.member.dto.baseresource.response.HsmStoreRespDTO>
     * @Param [brandGuid]
     */
    @ApiOperation("根据品牌GUID进行列表查询,删除和禁用的也要查")
    @GetMapping("/hsm/store/findListByBrandGuid")
    public List<HsmStoreRespDTO> findListByBrandGuid(@RequestParam(value = "brandGuid") String brandGuid);

    /**
     * 同步门店
     *
     * @param hsmStoreReqDTOS 列表
     */
    @ApiOperation("同步门店")
    @PostMapping("/hsm/store/syc/list")
    List<HsmStoreRespDTO> syc(@RequestBody List<HsmStoreReqDTO> hsmStoreReqDTOS);

    /**
     * 同步门店
     *
     * @param hsmStoreReqDTO
     */
    @ApiOperation("同步门店")
    @PostMapping("/hsm/store/syc")
    HsmStoreRespDTO syc(@RequestBody  HsmStoreReqDTO hsmStoreReqDTO);
    /**
     * 保存门店
     * @param hsmStoreReqDTO
     * @return
     */
    @ApiOperation("保存门店")
    @PostMapping("/hsm/store/save")
    boolean saveHsmStore(@RequestBody @Validated HsmStoreReqDTO hsmStoreReqDTO);

    /**
     * 修改门店
     * @param hsmStoreReqDTO
     * @return
     */
    @ApiOperation("修改门店")
    @PostMapping("/hsm/store/modify")
    boolean modifyHsmStore(@RequestBody @Validated HsmStoreReqDTO hsmStoreReqDTO);

    /**
     *
     * @param storeKey
     * @param allianceid
     * @return
     */
    @ApiOperation(value = "根据外部门店主键为空和联盟ID删除门店", notes = "根据外部门店主键为空和联盟ID删除门店")
    @DeleteMapping("/hsm/store/{storeKey}/{allianceid}")
    boolean removeHsmStore(@ApiParam("storeKey") @PathVariable("storeKey") String storeKey,
                                  @ApiParam("allianceid") @PathVariable("allianceid") String allianceid);

    @Component
    @Slf4j
    class HsmStoreServiceFallBack implements FallbackFactory<HsmStoreService> {


        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public HsmStoreService create(Throwable cause) {
            return new HsmStoreService() {
                @Override
                public Page<HsmStoreRespDTO> findPageByCondition(ShopQueryReqDTO shopQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "findPageByCondition",
                        JSONObject.toJSON(shopQueryReqDTO), ThrowableUtils
                            .asString(cause));
                    throw new BusinessException("门店分页查询失败");
                }

                @Override
                public List<HsmStoreRespDTO> findListByCondition(ShopQueryReqDTO shopQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "findListByCondition",
                        JSONObject.toJSON(shopQueryReqDTO), ThrowableUtils
                            .asString(cause));
                    throw new BusinessException("门店列表查询失败");
                }

                @Override
                public List<HsmStoreRespDTO> findListByBrandGuid(String brandGuid) {
                    log.error(HYSTRIX_PATTERN, "findListByBrandGuid",
                            JSONObject.toJSON(brandGuid), ThrowableUtils
                                    .asString(cause));
                    throw new BusinessException("根据品牌GUID进行列表查询失败");
                }

                @Override
                public List<HsmStoreRespDTO> syc(List<HsmStoreReqDTO> hsmStoreReqDTOS) {
                    log.error(HYSTRIX_PATTERN, "syc",
                        JSONObject.toJSON(hsmStoreReqDTOS), ThrowableUtils
                            .asString(cause));
                    throw new BusinessException("同步门店列表数据失败");
                }

                @Override
                public HsmStoreRespDTO syc(HsmStoreReqDTO hsmStoreReqDTO) {
                    log.error(HYSTRIX_PATTERN, "syc",
                            JSONObject.toJSON(hsmStoreReqDTO), ThrowableUtils
                                    .asString(cause));
                    throw new BusinessException("同步门店数据失败");
                }

                @Override
                public boolean saveHsmStore(HsmStoreReqDTO hsmStoreReqDTO) {
                    log.error(HYSTRIX_PATTERN, "saveHsmStore",
                            JSONObject.toJSON(hsmStoreReqDTO), ThrowableUtils
                                    .asString(cause));
                    throw new BusinessException("保存门店数据失败");
                }

                @Override
                public boolean modifyHsmStore(HsmStoreReqDTO hsmStoreReqDTO) {
                    log.error(HYSTRIX_PATTERN, "modifyHsmStore",
                            JSONObject.toJSON(hsmStoreReqDTO), ThrowableUtils
                                    .asString(cause));
                    throw new BusinessException("修改门店数据失败");
                }

                @Override
                public boolean removeHsmStore(String storeKey, String allianceid) {
                    log.error(HYSTRIX_PATTERN, "removeHsmStore",
                            JSONObject.toJSON(storeKey+","+allianceid), ThrowableUtils
                                    .asString(cause));
                    throw new BusinessException("删除门店数据失败");
                }
            };
        }
    }

}
