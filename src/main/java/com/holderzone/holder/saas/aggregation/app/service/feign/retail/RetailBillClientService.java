package com.holderzone.holder.saas.aggregation.app.service.feign.retail;


import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.retail.bill.request.*;
import com.holderzone.saas.store.dto.retail.bill.response.RetailAggPayRespDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderDetailRespDTO;
import com.holderzone.saas.store.dto.retail.pay.SaasNotifyDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInBillClientService
 * @date 2018/09/06 15:02
 * @description 正餐订单远程调用
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-store-retail", fallbackFactory = RetailBillClientService.FastFoodFallBack.class)
public interface RetailBillClientService {

    @PostMapping("/retail_bill/calculate")
    RetailOrderDetailRespDTO calculate(@RequestBody RetailCalculateReqDTO retailCalculateReqDTO);

    @PostMapping("/retail_bill/pay")
    Boolean pay(@RequestBody RetailPayReqDTO billPayReqDTO);

    @PostMapping("/retail_bill/agg_pay")
    RetailAggPayRespDTO aggPay(@RequestBody RetailAggPayReqDTO retailAggPayReqDTO);

    @PostMapping("/retail_bill/callback")
    String aggCallBack(@RequestBody SaasNotifyDTO saasNotifyDTO);

    @PostMapping("/retail_bill/refund")
    Boolean refund(RefundReqDTO refundReqDTO);

    @PostMapping("/retail_bill/validat_agg_refund")
    Boolean validatAggRefund(ValidatAggReturnReqDTO validatAggReturnReqDTO);

    @Component
    @Slf4j
    class FastFoodFallBack implements FallbackFactory<RetailBillClientService> {

        @Override
        public RetailBillClientService create(Throwable throwable) {
            return new RetailBillClientService() {

                @Override
                public RetailOrderDetailRespDTO calculate(RetailCalculateReqDTO retailCalculateReqDTO) {
                    log.error("计算金额FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("计算金额失败!" + throwable.getMessage());
                }

                @Override
                public Boolean pay(RetailPayReqDTO billPayReqDTO) {
                    log.error("支付FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("支付失败!" + throwable.getMessage());
                }

                @Override
                public RetailAggPayRespDTO aggPay(RetailAggPayReqDTO retailAggPayReqDTO) {
                    log.error("聚合支付FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("聚合支付失败!" + throwable.getMessage());
                }

                @Override
                public String aggCallBack(SaasNotifyDTO saasNotifyDTO) {
                    log.error("聚合支付回调FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("聚合支付回调失败!" + throwable.getMessage());
                }

                @Override
                public Boolean refund(RefundReqDTO refundReqDTO) {
                    log.error("退款FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("退款失败!" + throwable.getMessage());
                }

                @Override
                public Boolean validatAggRefund(ValidatAggReturnReqDTO validatAggReturnReqDTO) {
                    log.error("校验退款FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("校验退款失败!" + throwable.getMessage());
                }

            };
        }
    }
}
