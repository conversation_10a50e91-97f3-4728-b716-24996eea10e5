package com.holderzone.holder.saas.aggregation.weixin.controller.cmember.account;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dynamic.datasource.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxUserRecordClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.WxHsmMemberBasicBufferService;
import com.holderzone.holder.saas.aggregation.weixin.utils.StringMessageUtils;
import com.holderzone.holder.saas.member.wechat.dto.base.RequestMemberPasswore;
import com.holderzone.holder.saas.member.wechat.dto.base.RequestMemberPhoneSmsCode;
import com.holderzone.holder.saas.member.wechat.dto.member.*;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WxMemberSessionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @describe 会员基础操作控制层
 * @date 2019/6/13 11:51
 */
@RestController
@Api(description = "会员基础操作控制层")
@RequestMapping(value = "/hsmcw/member")
@Slf4j
public class WxHsmMemberBasicController {


    private final static String SUCCESS_MSG = "操作成功";
    private final static String FAIL_MSG = "操作失败";
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private WxUserRecordClientService wxUserRecordClientService;
    @Resource
    WxHsmMemberBasicBufferService wxHsmMemberBasicBufferService;
    @Resource
    HsaBaseClientService hsaBaseClientService;


    /**
     * 登录页面-> Icon  根据品牌去查询,如果没有则需要前端默认显示
     *
     * @param
     * @return
     */
    @ApiOperation(value = "发送注册验证码")
    @GetMapping("/sendRegSmsCode")
    public Result<Object> sendRegSmsCode(@ApiParam(name = "phoneNum", value = "会员电话号码", required = true) @RequestParam(value = "phoneNum", required = true) String phoneNum
            , @ApiParam(name = "enterpriseGuid", value = "企业Guid", required = true) @RequestParam(value = "enterpriseGuid", required = true) String enterpriseGuid) {
        RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setPhoneNum(phoneNum);
        ResponseMemberInfo memberInfo = hsaBaseClientService.getMemberInfo(requestQueryMemberInfo).getData();
        if (memberInfo != null) {
            return Result.buildOpFailedResult("该手机号已注册");
        }
        ResponseModel<Boolean> booleanResponseModel = hsaBaseClientService.sendLoginSmsCode(phoneNum);
        if (booleanResponseModel.getCode() == 0) {
            return Result.buildSuccessMsg(SUCCESS_MSG);
        } else {
            return Result.buildOpFailedResult(booleanResponseModel.getMessage());
        }
    }


    @ApiOperation(value = "发送登录验证码")
    @GetMapping("/sendLoginSmsCode")
    public Result<Integer> sendLoginSmsCode(@ApiParam(name = "phoneNum", value = "会员电话号码", required = true) @RequestParam(value = "phoneNum", required = true) String phoneNum
            , @ApiParam(name = "enterpriseGuid", value = "企业Guid", required = true) @RequestParam(value = "enterpriseGuid", required = true) String enterpriseGuid) {
        try {
            UserContextUtils.putErp(enterpriseGuid);
            ResponseModel<Boolean> booleanResponseModel = hsaBaseClientService.sendLoginSmsCode(phoneNum);
            if (booleanResponseModel.getCode() == 0) {
                return Result.buildSuccessResult(0).setMessage(SUCCESS_MSG);
            } else {
                return Result.buildSuccessResult(1).setMessage(booleanResponseModel.getMessage());
            }
        } catch (Exception e) {
            boolean containChinese = StringMessageUtils.isContainChinese(e.getMessage());
            if (containChinese) {
                return Result.buildSuccessResult(1).setMessage(e.getMessage());
            }
        }
        return Result.buildSuccessResult(1).setMessage("发送登录验证码失败");
    }

    @ApiOperation(value = "手机登陆或注册会员")
    @PostMapping("/registerOrLoginMemberInfo")
    public Result<Integer> registerOrLoginMemberInfo(@ApiParam(value = "手机登陆或注册会员参数", required = true) @RequestBody RequestMemberBasic requestMemberBasic) {
        if (StringUtils.isEmpty(requestMemberBasic.getMemberEnterpriseGuid())) {
            requestMemberBasic.setMemberEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        }
        UserContextUtils.putErp(requestMemberBasic.getMemberEnterpriseGuid());
        EnterpriseIdentifier.setEnterpriseGuid(requestMemberBasic.getMemberEnterpriseGuid());
        boolean success = false;
        try {
            ResponseModel<Boolean> booleanResponseModel = hsaBaseClientService.registerOrLoginMemberInfo(requestMemberBasic);
            //0 是成功
            if (booleanResponseModel.getCode() != 0) {
                return Result.buildSuccessResult(1).setMessage(booleanResponseModel.getMessage());
            } else {
                success = true;
            }
        } catch (Exception e) {
            boolean containChinese = StringMessageUtils.isContainChinese(e.getMessage());
            if (containChinese) {
                return Result.buildSuccessResult(1).setMessage(e.getMessage());
            }
        }
        if (success) {
            String phoneNum = requestMemberBasic.getPhoneNum();
            String openId = requestMemberBasic.getOpenid();
            log.info("openid:{},phoneNum:{} 登录", openId, phoneNum);
            wxHsmMemberBasicBufferService.removeByOpenId(openId, requestMemberBasic.getMemberEnterpriseGuid());
            wxUserRecordClientService.loginWxUserRecordAndFlushCard(requestMemberBasic.getMemberEnterpriseGuid(), phoneNum, openId);
            WxMemberSessionDTO memberByOpenId = WxMemberSessionUtil.getMemberByOpenId(redisUtils, openId);
            if (memberByOpenId != null) {
                memberByOpenId.getWxUserInfoDTO().setIsLogin(true);
                memberByOpenId.setPhoneNum(phoneNum);
                WxMemberSessionUtil.initMemberSessionWithOpenId(redisUtils, memberByOpenId);
            }
        }
        return success ? Result.buildSuccessResult(0).setMessage(SUCCESS_MSG) : Result.buildSuccessResult(1).setMessage(FAIL_MSG);
    }

    @ApiOperation(value = "查询会员是否被禁用")
    @GetMapping("/getMemberState")
    public Result<ResponseAccountStatus> getMemberState(@ApiParam(name = "phoneNumOrOpenid", value = "会员电话号码或微信OpenId", required = true) @RequestParam(value = "phoneNumOrOpenid", required = true) String phoneNumOrOpenid,
                                                        @ApiParam(name = "enterpriseGuid", value = "企业Guid", required = true) @RequestParam(value = "enterpriseGuid", required = true) String enterpriseGuid) {
        UserContextUtils.putErp(enterpriseGuid);
        UserContext userContext = UserContextUtils.get();
        String operSubjectGuid = userContext.getOperSubjectGuid();
        return Result.buildSuccessResult(hsaBaseClientService.getMemberState(phoneNumOrOpenid, enterpriseGuid, operSubjectGuid).getData());
    }


    @ApiOperation(value = "查询会员信息")
    @GetMapping("/getMemberInfo")
    public Result<ResponseMemberInfo> getMemberInfo(@ApiParam(name = "phoneNumOrOpenid", value = "会员电话号码或微信OpenId", required = true) @RequestParam(value = "phoneNumOrOpenid", required = true) String phoneNumOrOpenid,
                                                    @ApiParam(name = "enterpriseGuid", value = "企业Guid", required = true) @RequestParam(value = "enterpriseGuid", required = true) String enterpriseGuid) {
        ResponseMemberInfo memberInfo = null;
        try {
            RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
            //手机号 0r openId
            requestQueryMemberInfo.setPhoneNum(phoneNumOrOpenid);
            memberInfo = hsaBaseClientService.getMemberInfo(requestQueryMemberInfo).getData();
        } catch (Exception e) {
            memberInfo = new ResponseMemberInfo();
            log.error("查询会员失败", e);
        }
        return Result.buildSuccessResult(memberInfo);
    }

    @ApiOperation(value = "获取二维码")
    @GetMapping("/getQrcode")
    public Result<String> getMemberQrcode(@ApiParam(name = "key", value = "会员二维码获取KEY", required = true) @RequestParam(value = "key", required = true) String key) {
        return Result.buildSuccessResult(hsaBaseClientService.getMemberQrcode(key).getData());
    }

    @ApiOperation(value = "查询会员卡二维码")
    @GetMapping("/queryMemberMainCardQRCode")
    public Result<String> queryMemberMainCardQRCode(@RequestParam(value = "phoneNum") String phoneNum) {
        return Result.buildSuccessResult(hsaBaseClientService.queryMemberMainCardQRCode(phoneNum).getData());
    }

    @ApiOperation(value = "修改会员信息")
    @PostMapping("/updateMemberInfo")
    public Result<Object> updateMemberInfo(@ApiParam(value = "修改会员传输参数对象", required = true) @Valid @RequestBody RequestUpdateMemberInfo requestUpdateMemberInfo) {
        log.info("修改会员传输参数对象{}", JacksonUtils.writeValueAsString(requestUpdateMemberInfo));
        return (hsaBaseClientService.updateMemberInfo(requestUpdateMemberInfo).getData()) ? Result.buildSuccessMsg(SUCCESS_MSG) : Result.buildOpFailedResult(FAIL_MSG);

    }


    @ApiOperation(value = "更换主账号发送手机验证码")
    @GetMapping("/sendChangeSmsCode")
    public Result<Object> sendChangeSmsCode(@ApiParam(value = "旧手机号", required = true) @RequestParam(value = "oldPhoneNum", required = true) String oldPhoneNum,
                                            @ApiParam(value = "新手机号", required = true) @RequestParam(value = "newPhoneNum", required = true) String newPhoneNum,
                                            @ApiParam(name = "enterpriseGuid", value = "企业Guid", required = true) @RequestParam(value = "enterpriseGuid", required = true) String enterpriseGuid) {
        if (oldPhoneNum.equals(newPhoneNum)) {
            return Result.buildOpFailedResult("新手机号与原手机号相同，请重新输入");
        }
        UserContextUtils.putErp(enterpriseGuid);
        ResponseModel<Boolean> booleanResponseModel = hsaBaseClientService.sendChangeSmsCode(oldPhoneNum, newPhoneNum, enterpriseGuid);
        if (booleanResponseModel.getCode() != 0) {
            return Result.buildOpFailedResult(booleanResponseModel.getMessage());
        }
        return (BooleanUtils.isTrue(booleanResponseModel.getData())) ? Result.buildSuccessMsg(SUCCESS_MSG) : Result.buildOpFailedResult(FAIL_MSG);
    }

    @ApiOperation(value = "会员更换手机号码（重新绑定新账号）")
    @PostMapping("/bindChangeMemberPhone")
    public Result<Object> bindChangeMemberPhone(@ApiParam(value = "会员更换手机号码参数对象", required = true) @RequestBody RequestMemberPhoneSmsCode hsaMemberPhoneSmsCodeReqDTO) {
        ResponseModel<Boolean> booleanResponseModel = hsaBaseClientService.bindChangeMemberPhone(hsaMemberPhoneSmsCodeReqDTO);
        if (booleanResponseModel.getCode() != 0) {
            return Result.buildOpFailedResult(booleanResponseModel.getMessage());
        }
        String phoneNum = hsaMemberPhoneSmsCodeReqDTO.getNewPhoneNum();
        String openId = hsaMemberPhoneSmsCodeReqDTO.getOpenId();
        log.info("openid:{},phoneNum:{} 登录", openId, phoneNum);
        UserContextUtils.putErp(hsaMemberPhoneSmsCodeReqDTO.getEnterpriseGuid());
        EnterpriseIdentifier.setEnterpriseGuid(hsaMemberPhoneSmsCodeReqDTO.getEnterpriseGuid());
        WxMemberSessionDTO memberByOpenId = WxMemberSessionUtil.getMemberByOpenId(redisUtils, openId);
        if (memberByOpenId != null) {
            memberByOpenId.getWxUserInfoDTO().setIsLogin(true);
            memberByOpenId.setPhoneNum(phoneNum);
            WxMemberSessionUtil.initMemberSessionWithOpenId(redisUtils, memberByOpenId);
        }

        return Result.buildSuccessMsg(SUCCESS_MSG);
    }

    @ApiOperation(value = "绑定主账号发送手机验证码")
    @GetMapping("/sendBindSmsCode")
    public Result<Object> sendBindSmsCode(@ApiParam(value = "手机号", required = true) @RequestParam(value = "phoneNum", required = true) String phoneNum,
                                          @ApiParam(value = "企业Guid", required = true) @RequestParam(value = "enterpriseGuid", required = true) String enterpriseGuid) {
        UserContextUtils.putErp(enterpriseGuid);
        ResponseModel<Boolean> booleanResponseModel = hsaBaseClientService.sendBindSmsCode(phoneNum, enterpriseGuid);
        boolean success = false;
        if (booleanResponseModel.getCode() == 0) {
            success = true;
        } else {
            return Result.buildOpFailedResult(booleanResponseModel.getMessage());
        }
        return success ? Result.buildSuccessMsg(SUCCESS_MSG) : Result.buildOpFailedResult(FAIL_MSG);
    }

    @ApiOperation(value = "会员手机号码绑定,废弃20200628")
    @PostMapping("/bindMemberPhone")
    public Result<Object> bindMemberPhone(@ApiParam(value = "会员手机号和短信验证码参数", required = true) @RequestBody RequestBindMemberPhone hsmMemberPhoneSmsCodeReqDTO) {
        log.info("手机号绑定入参:{}", JacksonUtils.writeValueAsString(hsmMemberPhoneSmsCodeReqDTO));
        String enterpriseGuid = hsmMemberPhoneSmsCodeReqDTO.getEnterpriseGuid();
        ResponseModel<Boolean> booleanResponseModel = hsaBaseClientService.bindMemberPhone(hsmMemberPhoneSmsCodeReqDTO);
        if (booleanResponseModel.getCode() != 0) {
            return Result.buildOpFailedResult(booleanResponseModel.getMessage());
        }
        EnterpriseIdentifier.setEnterpriseGuid(hsmMemberPhoneSmsCodeReqDTO.getEnterpriseGuid());
        UserContextUtils.putErp(hsmMemberPhoneSmsCodeReqDTO.getEnterpriseGuid());
        String phoneNum = hsmMemberPhoneSmsCodeReqDTO.getOldPhoneNum();
        String openId = hsmMemberPhoneSmsCodeReqDTO.getOpenId();
        log.info("openid:{},phoneNum:{} 绑定并登录", openId, phoneNum);
        wxUserRecordClientService.loginWxUserRecordAndFlushCard(enterpriseGuid, phoneNum, openId);
        WxMemberSessionDTO memberByOpenId = WxMemberSessionUtil.getMemberByOpenId(redisUtils, openId);
        if (memberByOpenId != null) {
            memberByOpenId.getWxUserInfoDTO().setIsLogin(true);
            memberByOpenId.setPhoneNum(phoneNum);
            WxMemberSessionUtil.initMemberSessionWithOpenId(redisUtils, memberByOpenId);
        }
        return Result.buildSuccessMsg(SUCCESS_MSG);
    }

    @ApiOperation(value = "修改会员支付密码")
    @PostMapping("/updateMemberPassword")
    public Result<Object> updateMemberPassword(@RequestBody @Valid RequestMemberPasswore hsaMemberPassworeReqDTO) {
        ResponseModel<Boolean> booleanResponseModel = hsaBaseClientService.updateMemberPassword(hsaMemberPassworeReqDTO);
        return (booleanResponseModel.getCode()) == 0 ? Result.buildSuccessMsg(SUCCESS_MSG) : Result.buildOpFailedResult(booleanResponseModel.getMessage());
    }


    @ApiOperation(value = "查询openId绑定的账号(兼容以前只有openId的账号)")
    @GetMapping("/queryMemberByOpenId")
    public Result<List<ResponseMemberAccount>> queryMemberByOpenId(@ApiParam(name = "openId", value = "微信openId") @RequestParam(value = "openId") String openId) {
        return Result.buildSuccessResult(hsaBaseClientService.queryMemberByOpenId(openId).getData());
    }


    @ApiOperation(value = "通过手机号登录")
    @PostMapping("/loginByPhone")
    public Result<ResponseLoginResult> loginByPhone(@ApiParam(value = "通过手机号登录参数", required = true) @RequestBody RequestMemberLogin request) {
        return Result.buildSuccessResult(hsaBaseClientService.loginByPhone(request).getData());
    }


    @ApiOperation(value = "通过手机号注册并登录会员")
    @PostMapping("/registerAndLogin")
    public Result<ResponseLoginResult> registerAndLogin(@ApiParam(value = "通过手机号注册并登录会员参数", required = true) @RequestBody RequestMemberBasic request) {
        return Result.buildSuccessResult(hsaBaseClientService.registerAndLogin(request).getData());
    }

    @ApiOperation(value = "查询会员是否被禁用")
    @GetMapping("/isNeedRegister")
    public Result<Boolean> getMemberState(@RequestParam("channel") Integer channel) {
        return Result.buildSuccessResult(hsaBaseClientService.isNeedRegister(channel).getData());
    }
}