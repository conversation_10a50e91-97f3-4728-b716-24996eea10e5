package com.holderzone.holder.saas.aggregation.merchant.service.impl;

import com.holderzone.holder.saas.aggregation.merchant.service.AreaService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.table.TableServiceClient;
import com.holderzone.saas.store.dto.table.AreaDTO;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AreaServiceImpl
 * @date 2019/01/07 10:47
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Service
public class AreaServiceImpl implements AreaService {

    private final TableServiceClient tableServiceClient;

    @Autowired
    public AreaServiceImpl(TableServiceClient tableServiceClient) {
        this.tableServiceClient = tableServiceClient;
    }

    @Override
    public String addArea(AreaDTO areaDTO) {
        return tableServiceClient.addArea(areaDTO);
    }

    @Override
    public String updateArea(AreaDTO areaDTO) {
        return tableServiceClient.updateArea(areaDTO);
    }

    @Override
    public String delete(String guid) {
        return tableServiceClient.deleteArea(guid);
    }

    @Override
    public List<AreaDTO> queryAll(String storeGuid) {
        return tableServiceClient.queryAllArea(storeGuid);
    }
}
