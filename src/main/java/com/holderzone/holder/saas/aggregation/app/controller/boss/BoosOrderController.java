package com.holderzone.holder.saas.aggregation.app.controller.boss;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.app.service.OrderDetailsService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.OrderDetailsClientService;
import com.holderzone.saas.store.dto.boss.req.BossOrderItemQueryDTO;
import com.holderzone.saas.store.dto.boss.resp.BossOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.request.dinein.DineInOrderListReqDTO;
import com.holderzone.saas.store.dto.trade.OrderInfoRespDTO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 老板助手 - 订单管理
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/boss/order")
public class BoosOrderController {

    private final OrderDetailsClientService orderDetailsClientService;

    private final OrderDetailsService orderDetailsService;

    @PostMapping("/page")
    public Result<Page<OrderInfoRespDTO>> pageOrderInfo(@RequestBody DineInOrderListReqDTO reqDTO) {
        log.info("老板助手查询订单管理列表入参:{}", JacksonUtils.writeValueAsString(reqDTO));
        Page<OrderInfoRespDTO> page = orderDetailsClientService.pageOrderInfo(reqDTO);
        List<OrderInfoRespDTO> data = page.getData();
        if (CollectionUtils.isEmpty(data)) {
            return Result.buildSuccessResult(page);
        }
        // 返回值脱敏
        data.forEach(orderDetailsService::sensitization);
        return Result.buildSuccessResult(page);
    }

    @ApiOperation(value = "查询订单商品详情")
    @PostMapping("/query_order_item_info")
    public Result<BossOrderDetailRespDTO> queryOrderItemInfo(@RequestBody BossOrderItemQueryDTO queryDTO) {
        log.info("[老板助手][查询订单商品详情]入参,queryDTO={}", JacksonUtils.writeValueAsString(queryDTO));
        return Result.buildSuccessResult(orderDetailsService.queryOrderItemInfo(queryDTO));
    }

}
