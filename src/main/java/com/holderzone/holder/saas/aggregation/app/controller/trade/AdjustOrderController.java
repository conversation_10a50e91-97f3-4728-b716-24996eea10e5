package com.holderzone.holder.saas.aggregation.app.controller.trade;

import cn.hutool.core.collection.CollectionUtil;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.app.builder.AdjustOrderReqDTOBuilder;
import com.holderzone.holder.saas.aggregation.app.entity.OrderLocaleEnum;
import com.holderzone.holder.saas.aggregation.app.manage.AdjustOrderManage;
import com.holderzone.holder.saas.aggregation.app.service.feign.takeout.TakeoutClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInOrderClientService;
import com.holderzone.holder.saas.aggregation.app.utils.ValidateUtil;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderItemQuery;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderListQuery;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustOrderQueryDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustOrderReqDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.*;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.print.TradeModeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/adjust_order")
@Api(tags = "调整订单接口")
@AllArgsConstructor
public class AdjustOrderController {

    private final DineInOrderClientService dineInOrderClientService;

    private final TakeoutClientService takeoutClientService;

    private final AdjustOrderManage adjustOrderManage;

    private static final String ADJUST_TIME_ERROR = "超过时间限制，无法操作";

    /**
     * 调整单-正餐/快餐订单列表
     * 正餐、快餐展示近30天已结账的订单数据
     *
     * @param query 关键字和门店guid
     * @return 正餐订单信息列表
     */
    @ApiOperation(value = "调整单-正餐/快餐订单列表")
    @PostMapping("/page_dine_and_fast_order")
    public Result<Page<AdjustByDineAndFastOrderRespDTO>> pageDineAndFastOrder(@RequestBody AdjustByOrderListQuery query) {
        log.info("调整单-正餐/快餐订单列表 入参 query={}", JacksonUtils.writeValueAsString(query));
        Page<AdjustByDineAndFastOrderRespDTO> page = dineInOrderClientService.pageDineAndFastOrder(query);
        if (page != null && CollectionUtil.isNotEmpty(page.getData())) {
            page.getData().forEach(d -> d.setStateName(OrderLocaleEnum.getOrderStatusLocale(d.getStateName())));
        }
        return Result.buildSuccessResult(page);
    }

    /**
     * 调整单-外卖订单列表
     *
     * @param query 关键字，门店guid，分页数据
     * @return 外卖订单列表
     */
    @ApiOperation(value = "调整单-外卖订单列表")
    @PostMapping(value = "/page_order_by_adjust")
    public Result<Page<AdjustByTakeawayOrderRespDTO>> pageOrderByAdjust(@RequestBody AdjustByOrderListQuery query) {
        log.info("调整单-外卖订单列表 入参：{}", JacksonUtils.writeValueAsString(query));
        return Result.buildSuccessResult(takeoutClientService.pageOrderByAdjust(query));
    }

    /**
     * 调整单-查询订单商品
     * 标记商品是否调整过
     * tradeMode为2的时候查外卖，其他情况包括空都查询正餐、快餐
     *
     * @param query 订单guid，门店，交易模式
     * @return 订单商品信息列表
     */
    @ApiOperation(value = "调整单-查询订单商品")
    @PostMapping("/list_order_item")
    public Result<AdjustByOrderRespDTO> listOrderItem(@RequestBody AdjustByOrderItemQuery query) {
        log.info("调整单-查询订单商品 入参 query={}", JacksonUtils.writeValueAsString(query));
        AdjustByOrderRespDTO adjust;
        if (!ObjectUtils.isEmpty(query.getTradeMode()) && Objects.equals(2, query.getTradeMode())) {
            adjust = takeoutClientService.listOrderItem(query);
        } else {
            adjust = dineInOrderClientService.listOrderItem(query);
        }
        // 判断操作时间，是否可以操作
        if (!ValidateUtil.checkOrderAdjust(adjust.getOperateTime())) {
            return Result.buildOpFailedResult(ADJUST_TIME_ERROR);
        }
        if (adjust != null && CollectionUtil.isNotEmpty(adjust.getPayDetailList())) {
            adjust.getPayDetailList().forEach(p -> {
                String localeName = PaymentTypeEnum.PaymentType.getLocaleName(p.getPaymentTypeName());
                if (StringUtils.isNotEmpty(localeName)) {
                    p.setPaymentTypeName(localeName);
                }
            });
        }
        return Result.buildSuccessResult(adjust);
    }


    @ApiOperation(value = "调整单-创建调整单")
    @PostMapping(value = "/create")
    public Result<Long> createAdjustOrder(@RequestBody @Valid AdjustOrderReqDTO reqDTO) {
        log.info("调整单-创建调整单 入参 req={}", JacksonUtils.writeValueAsString(reqDTO));
        if (!TradeModeEnum.TAKEOUT.getMode().equals(reqDTO.getTradeMode())) {
            // 正餐快餐请求DTO预处理
            DineinOrderDetailRespDTO orderRespDTO = dineInOrderClientService.getOrderDetail(String.valueOf(reqDTO.getOrderGuid()));
            // 判断操作时间，是否可以操作
            if (!ValidateUtil.checkOrderAdjust(orderRespDTO.getCheckoutTime())) {
                return Result.buildOpFailedResult(ADJUST_TIME_ERROR);
            }
            AdjustOrderReqDTOBuilder.preHandleReqDTO(reqDTO, orderRespDTO);
        } else {
            // 外卖请求DTO预处理
            TakeoutOrderDTO takeoutOrderDetail = takeoutClientService.getOrderDetailMapping(String.valueOf(reqDTO.getOrderGuid()));
            // 判断操作时间，是否可以操作
            if (!ValidateUtil.checkOrderAdjust(takeoutOrderDetail.getAcceptTime())) {
                return Result.buildOpFailedResult(ADJUST_TIME_ERROR);
            }
            AdjustOrderReqDTOBuilder.preHandleReqDTO(reqDTO, takeoutOrderDetail);
        }
        log.info("调整单-创建调整单 预处理完成请求 req={}", JacksonUtils.writeValueAsString(reqDTO));
        return Result.buildSuccessResult(adjustOrderManage.create(reqDTO));
    }

    @ApiOperation(value = "调整单-查询调整单详情")
    @PostMapping("/query")
    public Result<AdjustByOrderRespDTO> queryAdjustOrder(@RequestBody AdjustOrderQueryDTO queryDTO) {
        AdjustByOrderRespDTO resp = adjustOrderManage.query(queryDTO);
        if (resp != null) {
            resp.setCreateStaffName(BaseDeviceTypeEnum.getLocaleDesc(resp.getCreateStaffName()));
            if (CollectionUtil.isNotEmpty(resp.getPayDetailList())) {
                resp.getPayDetailList().forEach(e -> e.setPaymentTypeName(PaymentTypeEnum.PaymentType.getLocaleName(e.getPaymentTypeName())));
            }
        }
        return Result.buildSuccessResult(resp);
    }

    /**
     * 调整单-分页列表
     *
     * @param queryDTO 门店guid
     * @return 调整单列表
     */
    @ApiOperation(value = "调整单-分页列表")
    @PostMapping("/page_adjust_order")
    public Result<Page<AdjustOrderRespDTO>> pageAdjustOrder(@RequestBody AdjustOrderQueryDTO queryDTO) {
        log.info("调整单列表-分页 入参 basePageDTO={}", JacksonUtils.writeValueAsString(queryDTO));
        Page<AdjustOrderRespDTO> adjustOrderPage = dineInOrderClientService.pageAdjustOrder(queryDTO);
        if (adjustOrderPage != null && CollectionUtil.isNotEmpty(adjustOrderPage.getData())) {
            adjustOrderPage.getData().forEach(a -> {
                if (a.getCreateStaffName().equals(OrderLocaleEnum.TAKEOUT_DEFAULT_ADMINISTRATOR.getMessage())) {
                    a.setCreateStaffName(OrderLocaleEnum.TAKEOUT_DEFAULT_ADMINISTRATOR.name());
                }
                a.setCreateStaffName(BaseDeviceTypeEnum.getLocaleDesc(a.getCreateStaffName()));
            });
        }
        return Result.buildSuccessResult(adjustOrderPage);
    }
}
