package com.holderzone.holder.saas.aggregation.app.manage;

import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.OrganizationClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.print.PrinterClient;
import com.holderzone.holder.saas.aggregation.app.service.feign.takeout.TakeoutClientService;
import com.holderzone.saas.store.dto.print.PrintOrderDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 * 外卖 业务层
 */
@Slf4j
@Component
public class TakeoutManage {

    @Autowired
    private TakeoutClientService takeoutClientService;

    @Autowired
    private PrinterClient printerClient;

    @Autowired
    private OrganizationClientService organizationClientService;

    /**
     * 轮询查询自动接单漏单订单
     */
    public List<PrintOrderDTO> delayAutoAcceptOrder(TakeoutOrderDTO takeoutOrderDTO) {
        if (StringUtils.isEmpty(UserContextUtils.getEnterpriseGuid())) {
            return Collections.emptyList();
        }
        // 获取访问设备是否门店主机
        // 查询重连门店主机
        StoreDeviceDTO masterDevice = organizationClientService.getMasterDeviceByStoreGuid(takeoutOrderDTO.getStoreGuid());
        if (Objects.isNull(masterDevice)) {
            return Lists.newArrayList();
        }
        // 判断当前重连设备是否为主机
        if (!masterDevice.getDeviceGuid().equals(takeoutOrderDTO.getDeviceId())) {
            return Lists.newArrayList();
        }
        // 漏单订单自动接单
        takeoutClientService.delayAutoAcceptOrder(takeoutOrderDTO);
        // 将打印失败的小票重新返回打印
        return printerClient.reprintTakeawayPrintOrderList(takeoutOrderDTO.getStoreGuid());
    }

}
