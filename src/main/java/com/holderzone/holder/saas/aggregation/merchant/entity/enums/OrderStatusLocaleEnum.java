package com.holderzone.holder.saas.aggregation.merchant.entity.enums;

import com.holderzone.saas.store.util.LocaleUtil;
import lombok.Getter;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

/**
 * <AUTHOR>
 * @create 2023-08-11
 * @description
 */
@Getter
public enum OrderStatusLocaleEnum {

    ORDER_NOT_SETTLED("未结账"),
    ORDER_CANCELLED("已作废"),
    ORDER_REFUNDED("已退款"),
    ORDER_REVERSED_SETTLEMENT("反结账"),
    ORDER_PENDING_DELIVERY("待配送"),
    ORDER_COMPLETED("已完成"),
    ORDER_SETTLED("已结账");

    private final String message;

    OrderStatusLocaleEnum(String message){
        this.message = message;
    }
    public static String getOrderStatusLocale(String message){
        if( LocaleContextHolder.getLocale() == Locale.SIMPLIFIED_CHINESE){
            return message;
        }
        //若存在多语言则通过中文寻找对应语言
        for(OrderStatusLocaleEnum localeMessageEnum :OrderStatusLocaleEnum.values()){
            //若完全匹配
            if(localeMessageEnum.getMessage().equals(message)){
                return LocaleUtil.getMessage(localeMessageEnum.name());
            }
        }
        return message;
    }


}
