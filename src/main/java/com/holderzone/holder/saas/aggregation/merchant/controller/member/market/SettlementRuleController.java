package com.holderzone.holder.saas.aggregation.merchant.controller.member.market;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.market.SettlementRuleClientService;
import com.holderzone.holder.saas.member.dto.marketing.response.SettlementRuleDetailsRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SettlementRuleController
 * @date 2019/05/30 14:49
 * @description 结算规则
 * @program holder-saas-member-account
 */
@RestController
@Api(description = "结算规则")
@RequestMapping("/hsm_settlement_rule")
public class SettlementRuleController {

    @Resource
    private SettlementRuleClientService settlementRuleService;

    /**
     * 获取模板规则详情
     *
     * @return 模板规则
     */
    @GetMapping(value = "/getDetails",produces= MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation("获取模板规则详情")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "获取模板规则详情")
    public Result<SettlementRuleDetailsRespDTO> getDetails() {
        return Result.buildSuccessResult(settlementRuleService.getDetails());
    }
}

