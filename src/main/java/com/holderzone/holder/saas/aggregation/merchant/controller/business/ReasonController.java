package com.holderzone.holder.saas.aggregation.merchant.controller.business;

import cn.hutool.core.collection.CollectionUtil;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.manage.ReasonClientService;
import com.holderzone.saas.store.dto.business.reason.ReasonCopyReqDTO;
import com.holderzone.saas.store.dto.business.reason.ReasonDTO;
import com.holderzone.saas.store.dto.business.reason.ReasonTypeDTO;
import com.holderzone.saas.store.enums.locale.ReasonLocaleEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReasonController
 * @date 2019/08/21 10:57
 * @description //TODO
 * @program holder-saas-store-business
 */
@Slf4j
@Api(value = "原因业务接口")
@RestController
@RequestMapping("/reason")
public class ReasonController {

    private final ReasonClientService reasonClientService;

    @Autowired
    public ReasonController(ReasonClientService reasonClientService) {
        this.reasonClientService = reasonClientService;
    }

    @PostMapping(value = "/findReason")
    @ApiOperation(value = "查询'原因列表',传入商家guid,原因类别guid", notes = "查询'原因列表',传入商家guid,类别guid")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "查询原因列表")
    public Result<List<ReasonDTO>> findReason(@RequestBody ReasonDTO reasonDTO) {
        log.info("查询'原因列表': reasonDTO={}", JacksonUtils.writeValueAsString(reasonDTO));
        List<ReasonDTO> reasonList = reasonClientService.findReason(reasonDTO);
        if(CollectionUtil.isNotEmpty(reasonList)){
            reasonList.forEach(e -> e.setReason(ReasonLocaleEnum.getLocale(e.getReason())));
        }
        return Result.buildSuccessResult(reasonList);
    }

    @PostMapping(value = "/insertReason")
    @ApiOperation(value = "新增'原因列表',传入商家guid,类别guid,原因内容", notes = "新增'原因列表',传入商家guid,类别guid,原因内容")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "新增原因列表")
    public Result<Void> insertReason(@RequestBody ReasonDTO reasonDTO) {
        log.info("新增'原因列表': reasonDTO={}", JacksonUtils.writeValueAsString(reasonDTO));
        reasonClientService.insertReason(reasonDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping(value = "/updateReason")
    @ApiOperation(value = "修改'原因列表',传入'原因列表'的guid,修改的原因内容", notes = "修改'原因列表',传入'原因列表'的guid,修改的原因内容")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "修改原因列表")
    public Result<Void> updateReason(@RequestBody ReasonDTO reasonDTO) {
        log.info("修改'原因列表': reasonDTO={}", JacksonUtils.writeValueAsString(reasonDTO));
        reasonClientService.updateReason(reasonDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping(value = "/deleteReason")
    @ApiOperation(value = "删除'原因列表',传入'原因列表'的guid", notes = "删除'原因列表',传入'原因列表'的guid")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "删除原因列表")
    public Result<Void> deleteReason(@RequestBody ReasonDTO reasonDTO) {
        reasonClientService.deleteReason(reasonDTO);
        return Result.buildEmptySuccess();
    }


    @PostMapping(value = "/findReasonType")
    @ApiOperation(value = "查询原因类型", notes = "查询原因类型")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "查询原因类型")
    public Result<List<ReasonTypeDTO>> findReasonType() {
        List<ReasonTypeDTO> reasonTypeList = reasonClientService.findReasonType();
        if(CollectionUtil.isNotEmpty(reasonTypeList)){
            reasonTypeList.forEach(e -> e.setReasonType(ReasonLocaleEnum.getLocale(e.getReasonType())));
        }
        return Result.buildSuccessResult(reasonTypeList);
    }

    @PostMapping(value = "/findReasonTypeSuperMarket")
    @ApiOperation(value = "商超版查询原因类型", notes = "查询原因类型")
    public Result<List<ReasonTypeDTO>> findReasonTypeSuperMarket() {
        return Result.buildSuccessResult(reasonClientService.findReasonTypeSuperMarket());
    }

    @PostMapping(value = "/copy_reason")
    @ApiOperation(value = "复制'原因列表',传入原因实体（类别code,原因内容）,门店列表")
    public Result<Boolean> copyReason(@RequestBody ReasonCopyReqDTO copyReqDTO) {
        log.info("复制'原因列表': reasonDTO={}", JacksonUtils.writeValueAsString(copyReqDTO));
        return Result.buildSuccessResult(reasonClientService.copyReason(copyReqDTO));
    }
}