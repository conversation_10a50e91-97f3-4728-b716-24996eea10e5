package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxQueueClientService;
import com.holderzone.saas.store.dto.queue.QueueWechatDTO;
import com.holderzone.saas.store.dto.weixin.WxInQueueReqDTO;
import com.holderzone.saas.store.dto.weixin.WxQueueDetailDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxInQueueRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxTotalQueueDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQueueController
 * @date 2019/05/16 10:12
 * @description 微信排队Controller
 * @program holder-saas-store
 */
@RestController
@Slf4j
@Api("微信排队相关接口")
@RequestMapping("/wx_queue")
public class WxQueueController {

    @Autowired
    WxQueueClientService wxQueueClientService;

    @PostMapping("/detail")
    @ApiOperation("排队详情")
    public Result<WxQueueDetailDTO> getQueueDetail(@RequestBody WxPortalReqDTO wxPortalReqDTO) {
        log.info("微信排队获取详情请求入参：{}", wxPortalReqDTO);
        WxQueueDetailDTO detail = wxQueueClientService.getDetail(wxPortalReqDTO);
        log.info("排队详情：{}", JacksonUtils.writeValueAsString(detail));
        return Result.buildSuccessResult(detail);
    }

    @PostMapping("/in_queue")
    public Result<WxInQueueRespDTO> inQueue(@RequestBody WxInQueueReqDTO wxInQueueReqDTO) {
        WxInQueueRespDTO wxPortalReqDTO = wxQueueClientService.inQueue(wxInQueueReqDTO);
        return Result.buildSuccessResult(wxPortalReqDTO);
    }

    @PostMapping("/total_detail")
    @ApiOperation("获取当前门店排队桌位配置")
    public Result<WxTotalQueueDTO> getTotalDetail(@RequestBody WxPortalReqDTO wxPortalReqDTO) {
        log.info("微信排队获取门店桌位排队详情请求入参：{}", JacksonUtils.writeValueAsString(wxPortalReqDTO));
        WxTotalQueueDTO totalDetail = wxQueueClientService.getTotalDetail(wxPortalReqDTO);
        return ObjectUtils.isEmpty(totalDetail) ? Result.buildOpFailedResult("当前门店暂未配置排队功能") : Result.buildSuccessResult(totalDetail);
    }

    @PostMapping("/groupingByStatus")
    @ApiOperation(value = "排队分组",notes = "状态 0:队列中,1:过号,2:叫号中,3:已就餐,4:已取消")
    public Result<Map<Integer, Integer>> queryQueueStatusNum(QueueWechatDTO queueWechatDTO){
        log.info("排队分组请求入参：{}", JacksonUtils.writeValueAsString(queueWechatDTO));
        return Result.buildSuccessResult(wxQueueClientService.queryQueueStatusNum(queueWechatDTO));
    }
}
