package com.holderzone.holder.saas.aggregation.weixin.config;

import java.util.ArrayList;
import java.util.List;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * <AUTHOR>
 * @version 1.0
 * @className Swagger2
 * @date 2018/08/01 上午8:57
 * @description //TODO
 * @program holder-saas-store-dish
 */
@Configuration
@EnableSwagger2
public class Swagger2 {
    private static final String HEADER = "header";
	private static final String STRING = "string";
	//swagger2的配置文件，这里可以配置swagger2的一些基本的内容，比如扫描的包等等
    @Bean
    public Docket createRestApi() {
    	 List<Parameter> pars = new ArrayList<>();  
    	ParameterBuilder ticketPar = new ParameterBuilder();
    	ticketPar.name("wxtoken").description("用户身份token,就是msgKey的值")
    	.modelRef(new ModelRef(STRING)).parameterType(HEADER) 
    	.required(false).build(); //header中的ticket参数非必填，传空也可以
    	pars.add(ticketPar.build());    //根据每个方法名也知道当前方法在设置什么参数
    	
    	ParameterBuilder openidPar = new ParameterBuilder();
    	openidPar.name("openId").description("微信openId")
    	.modelRef(new ModelRef(STRING)).parameterType(HEADER) 
    	.required(false).build(); //满足消息推送点击进入
    	pars.add(openidPar.build());  
    	
    	ParameterBuilder enterpriseGuidParam = new ParameterBuilder();
    	enterpriseGuidParam.name("enterpriseGuid").description("企业guid")
    	.modelRef(new ModelRef(STRING)).parameterType(HEADER) 
    	.required(false).build(); //header中的ticket参数非必填，传空也可以
    	pars.add(enterpriseGuidParam.build());    //根据每个方法名也知道当前方法在设置什么参数
    	
        return new Docket(DocumentationType.SWAGGER_2)
                .select()
                //为当前包路径
                .apis(RequestHandlerSelectors.basePackage("com.holderzone.holder.saas.aggregation.weixin.controller"))
                .paths(PathSelectors.any())
                .build() .globalOperationParameters(pars) .apiInfo(apiInfo()) ;
    }
    //构建 api文档的详细信息函数,注意这里的注解引用的是哪个
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                //页面标题
                .title("商户移动终端 聚合层 API")
                //版本号
                .version("1.0")
                //描述
                .description("API 描述")
                .build();
    }
}
