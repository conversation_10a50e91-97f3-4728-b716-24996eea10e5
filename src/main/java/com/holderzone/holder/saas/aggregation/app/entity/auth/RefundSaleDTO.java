package com.holderzone.holder.saas.aggregation.app.entity.auth;

import com.holderzone.holder.saas.aggregation.app.anno.DataAuthFieldControl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/14
 * @since 1.8
 */
@Data
@ApiModel
public class RefundSaleDTO {

    @ApiModelProperty(value = "收银员")
    private List<String> checkoutStaffs;

    @ApiModelProperty(value = "退款统计")
    @DataAuthFieldControl
    private List<RefundSaleAmountDTO> refundAmounts;
}
