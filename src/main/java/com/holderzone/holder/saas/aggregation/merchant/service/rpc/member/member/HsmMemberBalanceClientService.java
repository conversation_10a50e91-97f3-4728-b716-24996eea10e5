package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.HsmMemberBalanceClientService.MemberBalanceClientServiceFallback;
import com.holderzone.holder.saas.member.dto.account.request.MemberBalanceChangeReqDTO;
import com.holderzone.holder.saas.member.dto.account.request.MemberBalanceRecordQueryReqDTO;
import com.holderzone.holder.saas.member.dto.account.request.MemberBalanceStatisticalReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberBalanceRecordListRespDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberSourceTypeRespDTO;
import feign.hystrix.FallbackFactory;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberBalanceClientService
 * @date 2019/05/30 14:04
 * @description 会员余额相关操作
 * @program holder-saas-member-account
 */
@Component
@FeignClient(name = "holder-saas-member-account", fallbackFactory = MemberBalanceClientServiceFallback.class)
public interface HsmMemberBalanceClientService {

    /**
     * 改变会员余额
     *
     * @param memberBalanceChangeReqDTO 会员余额改变请求参数
     * @return 操作结果
     */
    @RequestMapping(value = "/hsm_member_balance/changeBalance", produces = "application/json;charset=utf-8")
    boolean changeBalance(MemberBalanceChangeReqDTO memberBalanceChangeReqDTO);


    /**
     * 来源类型
     *
     * @return 集合
     */
    @RequestMapping(value = "/hsm_member_balance/listSourceType", produces = "application/json;charset=utf-8")
    List<MemberSourceTypeRespDTO> listSourceType();


    /**
     * 通过条件分页查询
     *
     * @param queryReqDTO 查询条件
     * @return 查询结果
     */
    @RequestMapping(value = "/hsm_member_balance/listByCondition", produces = "application/json;charset=utf-8")
    Page<MemberBalanceRecordListRespDTO> listByCondition(
        @RequestBody MemberBalanceRecordQueryReqDTO queryReqDTO);


    /**
     * 统计会员持有的卡下的余额
     *
     * @param memberCardGuid 会员卡guid
     * @return 统计结果
     */
    @RequestMapping(value = "/hsm_member_balance/statistical", produces = "application/json;charset=utf-8")
    MemberBalanceStatisticalReqDTO statistical(
        @RequestParam("memberCardGuid") String memberCardGuid);


    @Component
    class MemberBalanceClientServiceFallback implements
            FallbackFactory<HsmMemberBalanceClientService> {

        private static final Logger LOGGER = LoggerFactory
                .getLogger(MemberBalanceClientServiceFallback.class);

        @Override
        public HsmMemberBalanceClientService create(Throwable throwable) {

            return new HsmMemberBalanceClientService() {
                @Override
                public boolean changeBalance(MemberBalanceChangeReqDTO memberBalanceChangeReqDTO) {
                    LOGGER.error("改变会员余额出错:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<MemberSourceTypeRespDTO> listSourceType() {
                    LOGGER.error("查询会员余额变化来源错误:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public Page<MemberBalanceRecordListRespDTO> listByCondition(
                        MemberBalanceRecordQueryReqDTO queryReqDTO) {
                    LOGGER.error("根据条件查询会员余额记录错误:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public MemberBalanceStatisticalReqDTO statistical(String memberCardGuid) {
                    LOGGER.error("会员余额统计出错:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }
}
