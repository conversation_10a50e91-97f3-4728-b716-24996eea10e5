package com.holderzone.holder.saas.aggregation.merchant.service;

import com.holderzone.saas.store.dto.config.req.ConfigReqQueryDTO;
import com.holderzone.saas.store.dto.config.req.EstimateConfigReqDTO;
import com.holderzone.saas.store.dto.config.req.ReservePhoneReqDTO;
import com.holderzone.saas.store.dto.config.resp.EstimateConfigRespDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ConfigService
 * @date 2019/05/15 18:05
 * @description //TODO 门店配置service
 * @program holder-saas-aggregation-app
 */
public interface ConfigService {


    /**
     * 保存 、 更新 估清重置时间
     * @param request
     * @return
     */
    Integer saveEstimateResetTime(EstimateConfigReqDTO request);


    /**
     * 查询门店配置估清置满时间
     * @param request
     * @return
     */
    EstimateConfigRespDTO selectEstimateResetTime(ConfigReqQueryDTO request);

    void saveReservePhone(ReservePhoneReqDTO reqDTO);

    String queryReservePhone(ReservePhoneReqDTO reqDTO);
}
