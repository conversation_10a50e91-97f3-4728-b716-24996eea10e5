package com.holderzone.holder.saas.aggregation.weixin.config;

import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ClassUtils;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.cache.support.SimpleCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.net.URI;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 本地缓存配置
 */
@EnableCaching
@Component
@Slf4j
public class LocalCacheConfig extends CachingConfigurerSupport {

    public Integer maxSize = 1000;

    public interface CacheExpires {
        String SECOND_1 = "second1";
        String SECOND_5 = "second5";
        String SECOND_10 = "second10";
        String MINUTES_1 = "minutes1";
        String MINUTES_10 = "minutes10";
        String HOUR_1 = "hour1";
        String HOUR_24 = "hour24";
    }

    public enum Caches{
        SECOND_1(CacheExpires.SECOND_1, 1),
        SECOND_5(CacheExpires.SECOND_5, 5),
        SECOND_10(CacheExpires.SECOND_10, 10),
        MINUTES_1(CacheExpires.MINUTES_1, 60),
        MINUTES_10(CacheExpires.MINUTES_10, 600),
        HOUR_1(CacheExpires.HOUR_1, 3600),
        HOUR_24(CacheExpires.HOUR_24, 86400);

        private final String name;
        private final int ttl;


        Caches(String name, int ttl) {
            this.name = name;
            this.ttl = ttl;
        }

        public String getName() {
            return name;
        }

        public int getTtl() {
            return ttl;
        }
    }

    /**
     * - 创建基于Caffeine的Cache Manager
     */
    @Bean
    @Primary
    public CacheManager caffeineCacheManager() {
        SimpleCacheManager cacheManager = new SimpleCacheManager();

        ArrayList<CaffeineCache> caches = new ArrayList<>();
        for(Caches c : Caches.values()){
            caches.add(new CaffeineCache(c.getName(),
                    Caffeine.newBuilder().recordStats()
                            .expireAfterWrite(c.getTtl(), TimeUnit.SECONDS)
                            .maximumSize(maxSize)
                            .build())
            );
        }

        cacheManager.setCaches(caches);
        return cacheManager;
    }

    @Bean("weixinCacheKeyGenerator")
    public KeyGenerator weixinCacheKeyGenerator() {
        return new BiKeyGenerator();
    }

    public static class BiKeyGenerator implements KeyGenerator {

        private static final String PREFIX = "myapp-caching";
        private static final int NO_PARAM_KEY = 0;

        private boolean isSimpleValueType(Class<?> clazz) {
            return (ClassUtils.isPrimitiveOrWrapper(clazz) || clazz.isEnum() || CharSequence.class.isAssignableFrom(clazz)
                    || Number.class.isAssignableFrom(clazz) || Date.class.isAssignableFrom(clazz) || URI.class == clazz
                    || URL.class == clazz || Locale.class == clazz || Class.class == clazz);
        }

        @Override
        public Object generate(Object target, Method method, Object... params) {
            char sp = ':';
            StringBuilder strBuilder = new StringBuilder(30);
            strBuilder.append(PREFIX);
            strBuilder.append(sp);
            // 类名
            strBuilder.append(target.getClass().getSimpleName());
            strBuilder.append(sp);
            // 方法名
            strBuilder.append(method.getName());
            strBuilder.append(sp);
            log.info("本地缓存key参数：{}", params);
            if (params.length > 0) {
                // 参数值
                for (Object object : params) {
                    if(null == object) {
                        continue;
                    }
                    if (isSimpleValueType(object.getClass())) {
                        strBuilder.append(object);
                    } else {
                        strBuilder.append(DigestUtil.md5Hex(JSON.toJSONString(object)));
                    }
                }
            } else {
                strBuilder.append(NO_PARAM_KEY);
            }
            log.info("本地缓存key：{}", strBuilder);
            return strBuilder.toString();
        }

    }

    @Bean
    public RedisTemplate redisTemplate(RedisConnectionFactory factory) {
        RedisTemplate redisTemplate = new RedisTemplate();
        GenericJackson2JsonRedisSerializer jackson2JsonRedisSerializer = new GenericJackson2JsonRedisSerializer();
        // 设置值（value）的序列化采用FastJsonRedisSerializer。
        redisTemplate.setValueSerializer(jackson2JsonRedisSerializer);
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(jackson2JsonRedisSerializer);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setConnectionFactory(factory);
        return redisTemplate;
    }

}

