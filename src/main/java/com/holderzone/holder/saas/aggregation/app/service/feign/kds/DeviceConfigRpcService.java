package com.holderzone.holder.saas.aggregation.app.service.feign.kds;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.kds.req.DeviceCreateReqDTO;
import com.holderzone.saas.store.dto.kds.req.DeviceQueryReqDTO;
import com.holderzone.saas.store.dto.kds.resp.DeviceStatusRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(name = "holder-saas-store-kds", fallbackFactory = DeviceConfigRpcService.FallbackFactoryImpl.class)
public interface DeviceConfigRpcService {

    @PostMapping("/device/create")
    DeviceStatusRespDTO create(@RequestBody DeviceCreateReqDTO deviceCreateReqDTO);

    @PostMapping("/device/query")
    DeviceStatusRespDTO query(@RequestBody DeviceQueryReqDTO deviceQueryReqDTO);

    @PostMapping("/device/initialize")
    void initialize(@RequestBody DeviceQueryReqDTO deviceQueryReqDTO);

    @Slf4j
    @Component
    class FallbackFactoryImpl implements FallbackFactory<DeviceConfigRpcService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public DeviceConfigRpcService create(Throwable throwable) {

            return new DeviceConfigRpcService() {

                @Override
                public DeviceStatusRespDTO create(DeviceCreateReqDTO deviceCreateReqDTO) {
                    log.error(HYSTRIX_PATTERN, "create",
                            JacksonUtils.writeValueAsString(deviceCreateReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public DeviceStatusRespDTO query(DeviceQueryReqDTO deviceQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "query",
                            JacksonUtils.writeValueAsString(deviceQueryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void initialize(DeviceQueryReqDTO deviceQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "initialize",
                            JacksonUtils.writeValueAsString(deviceQueryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
