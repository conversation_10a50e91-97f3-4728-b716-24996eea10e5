package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.rights;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.dto.rights.request.BirthRightReqDTO;
import com.holderzone.holder.saas.member.dto.rights.request.CardDetailReqDTO;
import com.holderzone.holder.saas.member.dto.rights.request.DiscountRightReqDTO;
import com.holderzone.holder.saas.member.dto.rights.request.UpgradeRightReqDTO;
import com.holderzone.holder.saas.member.dto.rights.response.BaseRightRespDTO;
import com.holderzone.holder.saas.member.dto.rights.response.BirthRightRespDTO;
import com.holderzone.holder.saas.member.dto.rights.response.CardDetailInfoRespDTO;
import com.holderzone.holder.saas.member.dto.rights.response.DiscountRightRespDTO;
import com.holderzone.holder.saas.member.dto.rights.response.UpgradeRightRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import java.util.List;

import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HsmRCardLevelRightsService
 * @date 2019/05/31 17:52
 * @description 卡等级权益
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-member-account", fallbackFactory = HsmRCardLevelRightsService.HsmRCardLevelRightsServiceFallBack.class)
public interface HsmRCardLevelRightsService {

    /**
     * 根据关系Guid和权益类型删除会员权益
     *
     * @param relationGuid 关系Guid
     * @param typeCode 权益类型
     */
    @ApiOperation("删除权益")
    @DeleteMapping(value = "/hsm/card/level/rights/{relationGuid}/{typeCode}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    void delRight(@PathVariable("relationGuid") String relationGuid,
        @PathVariable("typeCode") String typeCode);


    /**
     * 根据关系Guid和权益类型获取权益详情
     *
     * @param relationGuid 关系Guid
     * @param typeCode 权益类型
     * @return 权益类型
     */
    @ApiOperation("获取权益的详情值")
    @GetMapping(value = "/hsm/card/level/rights/{relationGuid}/{typeCode}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    BaseRightRespDTO findRightDetail(@PathVariable("relationGuid") String relationGuid,
        @PathVariable("typeCode") String typeCode);

    /**
     * 更新或者保存升级权益
     *
     * @param upgradeRightReqDTO 升级权益
     */
    @ApiOperation("保存/更新升级权益")
    @PostMapping("/hsm/card/level/rights/upgrade")
    void saveOrUpdateUpgradeRight(
        @RequestBody UpgradeRightReqDTO upgradeRightReqDTO);

    /**
     * 更新或者保存折扣权益
     *
     * @param discountRightReqDTO 折扣权益
     */
    @ApiOperation("保存/更新折扣权益")
    @PostMapping("/hsm/card/level/rights/discount")
    void saveOrUpdateDiscountRight(
        @RequestBody DiscountRightReqDTO discountRightReqDTO);

    /**
     * 更新或者保存生日权益
     *
     * @param birthRightReqDTO 生日权益
     */
    @ApiOperation("保存/更新生日权益")
    @PostMapping("/hsm/card/level/rights/birth")
    void saveOrUpdateBirthRight(
        @RequestBody BirthRightReqDTO birthRightReqDTO);


    @ApiOperation("更新成长值取值的勾选项")
    @PostMapping("/hsm/system/management/card/range")
    void updateRange(@RequestParam("cardGuid") String cardGuid,
        @RequestBody List<String> growthRangeList);


    @ApiOperation("更新会员卡的信息")
    @PostMapping("/hsm/system/management/card/card")
    void updateCard(@RequestBody CardDetailReqDTO cardDetailReqDTO);

    /**
     * 根据关系Guid和权益类型获取权益详情
     *
     * @param relationGuid 关系Guid
     * @return 权益类型
     */
    @ApiOperation("获取折扣权益的详情值")
    @GetMapping("/hsm/card/level/rights/discount/{relationGuid}")
    DiscountRightRespDTO findDiscountRightDetail(
        @PathVariable("relationGuid") String relationGuid);


    /**
     * 根据关系Guid和权益类型获取权益详情
     *
     * @param relationGuid 关系Guid
     * @return 权益类型
     */
    @ApiOperation("获取升级权益的详情值")
    @GetMapping("/hsm/card/level/rights/upgrade/{relationGuid}")
    UpgradeRightRespDTO findUpgradeRightDetail(@PathVariable("relationGuid") String relationGuid);


    /**
     * 根据关系Guid和权益类型获取权益详情
     *
     * @param relationGuid 关系Guid
     * @return 权益类型
     */
    @ApiOperation("获取生日权益的详情值")
    @GetMapping("/hsm/card/level/rights/birth/{relationGuid}")
    BirthRightRespDTO findBirthRightDetail(
        @PathVariable("relationGuid") String relationGuid);

    /**
     * 查询会员卡的列表
     *
     * @return 会员卡的信息
     */
    @ApiOperation("会员卡的列表")
    @GetMapping("/hsm/system/management/card/query")
    List<CardDetailInfoRespDTO> queryCard(@ApiParam("memberGuid") @RequestParam(value = "memberGuid",required = false) String memberInfoGuid);


    @Slf4j
    @Component
    class HsmRCardLevelRightsServiceFallBack implements
        FallbackFactory<HsmRCardLevelRightsService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public HsmRCardLevelRightsService create(Throwable cause) {
            return new HsmRCardLevelRightsService() {
                @Override
                public void delRight(String relationGuid, String typeCode) {
                    log.error(HYSTRIX_PATTERN, "delRight",
                        "relationGuid->" + relationGuid + " typeCode->" + typeCode, ThrowableUtils
                            .asString(cause));
                    throw new BusinessException("删除体系");
                }

                @Override
                public BaseRightRespDTO findRightDetail(String relationGuid, String typeCode) {
                    log.error(HYSTRIX_PATTERN, "findRightDetail",
                        "relationGuid->" + relationGuid + " typeCode->" + typeCode, ThrowableUtils
                            .asString(cause));
                    throw new BusinessException("删除体系");
                }

                @Override
                public void saveOrUpdateUpgradeRight(UpgradeRightReqDTO upgradeRightReqDTO) {
                    log.error(HYSTRIX_PATTERN, "saveOrUpdateUpgradeRight",
                        JSONObject.toJSONString(upgradeRightReqDTO), ThrowableUtils
                            .asString(cause));
                    throw new BusinessException("保存/更新升级权益失败");
                }

                @Override
                public void saveOrUpdateDiscountRight(DiscountRightReqDTO discountRightReqDTO) {
                    log.error(HYSTRIX_PATTERN, "saveOrUpdateDiscountRight",
                        JSONObject.toJSONString(discountRightReqDTO), ThrowableUtils
                            .asString(cause));
                    throw new BusinessException("保存/更新折扣权益失败");
                }

                @Override
                public void saveOrUpdateBirthRight(BirthRightReqDTO birthRightReqDTO) {
                    log.error(HYSTRIX_PATTERN, "saveOrUpdateBirthRight",
                        JSONObject.toJSONString(birthRightReqDTO), ThrowableUtils
                            .asString(cause));
                    throw new BusinessException("保存/更新生日权益失败");
                }

                @Override
                public void updateRange(String cardGuid, List<String> growthRangeList) {
                    log.error(HYSTRIX_PATTERN, "updateRange",
                        "cardGuid->" + cardGuid + " " + JSONObject.toJSONString(growthRangeList),
                        ThrowableUtils
                            .asString(cause));
                    throw new BusinessException("更新成长值取值的勾选项失败");
                }

                @Override
                public void updateCard(CardDetailReqDTO cardDetailReqDTO) {
                    log.error(HYSTRIX_PATTERN, "updateCard",
                        JSONObject.toJSONString(cardDetailReqDTO), ThrowableUtils.asString(cause));
                    throw new BusinessException("更新会员卡的信息失败");
                }

                @Override
                public DiscountRightRespDTO findDiscountRightDetail(String relationGuid) {
                    log.error(HYSTRIX_PATTERN, "findDiscountRightDetail",
                        JSONObject.toJSONString(relationGuid), ThrowableUtils.asString(cause));
                    throw new BusinessException("获取折扣权益失败");
                }

                @Override
                public UpgradeRightRespDTO findUpgradeRightDetail(String relationGuid) {
                    log.error(HYSTRIX_PATTERN, "findUpgradeRightDetail",
                        JSONObject.toJSONString(relationGuid), ThrowableUtils.asString(cause));
                    throw new BusinessException("获取升级权益失败");
                }

                @Override
                public BirthRightRespDTO findBirthRightDetail(String relationGuid) {
                    log.error(HYSTRIX_PATTERN, "findBirthRightDetail",
                        JSONObject.toJSONString(relationGuid), ThrowableUtils.asString(cause));
                    throw new BusinessException("获取生日权益失败");
                }

                @Override
                public List<CardDetailInfoRespDTO> queryCard(String memberInfoGuid) {
                    log.error(HYSTRIX_PATTERN, "queryCard",
                            memberInfoGuid, ThrowableUtils.asString(cause));
                    throw new BusinessException("获取列表");
                }
            };
        }
    }

}
