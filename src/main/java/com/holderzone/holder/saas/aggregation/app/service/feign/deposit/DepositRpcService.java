package com.holderzone.holder.saas.aggregation.app.service.feign.deposit;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.deposit.req.*;
import com.holderzone.saas.store.dto.deposit.resp.*;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(name = "holder-saas-store-deposit", fallbackFactory = DepositRpcService.FallbackFactoryImpl.class)
public interface DepositRpcService {

    @PostMapping("/deposit/create_deposit_item")
    Boolean createDepositItem(@RequestBody DepositCreateReqDTO depositCreateDTO);

    @PostMapping("/deposit/query_deposit_item")
    Page<DepositQueryRespDTO> queryDepositItem(@RequestBody DepositQueryReqDTO depositQueryReqDTO);

    @PostMapping("/deposit/query_deposit_detail")
    List<GoodsRespDTO> queryDepositDetail(@RequestBody QueryDepositDetailReqDTO depositQueryReqDTO);

    @PostMapping("/deposit/get_deposit_goods")
    boolean getDepositGoods(@RequestBody DepositGetReqDTO depositGetReqDTO);

    @PostMapping("/deposit/query_operation_history")
    Page<OperationQueryRespDTO> queryOperationHistory(@RequestBody OperationHistoryQueryReqDTO operationHistoryQueryReqDTO);

    @PostMapping("/deposit/query_goods_summary")
    Page<GoodsSummaryRespDTO> queryGoodsSummary(@RequestBody DepositQueryReqDTO depositQueryReqDTO);

    @PostMapping("/deposit/query_deposit_detail_for_pos")
    DepositDetailForPosRespDTO queryDepositDetailForPos(@RequestBody QueryDepositDetailReqDTO depositQueryReqDTO);

    @Slf4j
    @Component
    class FallbackFactoryImpl implements FallbackFactory<DepositRpcService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public DepositRpcService create(Throwable throwable) {

            return new DepositRpcService() {

                @Override
                public Boolean createDepositItem(DepositCreateReqDTO depositCreateDTO) {
                    log.error(HYSTRIX_PATTERN, "createDepositItem",
                            JacksonUtils.writeValueAsString(depositCreateDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<DepositQueryRespDTO> queryDepositItem(DepositQueryReqDTO depositQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryDepositItem",
                            JacksonUtils.writeValueAsString(depositQueryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<GoodsRespDTO> queryDepositDetail(QueryDepositDetailReqDTO depositQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryDepositDetail",
                            JacksonUtils.writeValueAsString(depositQueryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public boolean getDepositGoods(DepositGetReqDTO depositGetReqDTO) {
                    log.error(HYSTRIX_PATTERN, "getDepositGoods",
                            JacksonUtils.writeValueAsString(depositGetReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<OperationQueryRespDTO> queryOperationHistory(OperationHistoryQueryReqDTO operationHistoryQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryOperationHistory",
                            JacksonUtils.writeValueAsString(operationHistoryQueryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<GoodsSummaryRespDTO> queryGoodsSummary(DepositQueryReqDTO depositQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryGoodsSummary",
                            JacksonUtils.writeValueAsString(depositQueryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public DepositDetailForPosRespDTO queryDepositDetailForPos(QueryDepositDetailReqDTO depositQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryDepositDetailForPos",
                            JacksonUtils.writeValueAsString(depositQueryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}