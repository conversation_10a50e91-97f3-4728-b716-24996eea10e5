package com.holderzone.holder.saas.aggregation.merchant.controller.weixin;

import com.google.common.collect.Lists;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.entity.dto.OrganizationNewDTO;
import com.holderzone.holder.saas.aggregation.merchant.entity.vo.EnterpriseDTO;
import com.holderzone.holder.saas.aggregation.merchant.entity.vo.OrganizationMultiVO;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp.EnterpriseRpcService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.organization.OrganizationService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin.WxBrandAuthClientService;
import com.holderzone.resource.common.dto.enterprise.EnterpriseQueryDTO;
import com.holderzone.resource.common.dto.enterprise.MultiMemberDTO;
import com.holderzone.resource.common.dto.enterprise.OrganizationQueryDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.weixin.WxBaseDTO;
import com.holderzone.saas.store.dto.weixin.req.WxOperSubjectBrandReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxSendShortMsgReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxUnBandReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxAuthRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxBrandAuthRespDTO;
import com.holderzone.saas.store.enums.weixin.WxUnBandRespEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxBrandAuthController
 * @date 2019/03/01 17:58
 * @description 微信品牌公众号授权接口
 * @program holder-saas-store
 */
@RestController
@RequestMapping("/wx_brand_auth")
@Component
@Api(description = "微信品牌公众号授权接口")
@Slf4j
public class WxBrandAuthController {

    @Autowired
    WxBrandAuthClientService wxBrandAuthClientService;

    @Autowired
    OrganizationService organizationService;

    @Autowired
    EnterpriseRpcService enterpriseRpcService;

    @PostMapping("/list_brand_auth")
    @ApiOperation("获取微信列表")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "获取微信列表")
    public Result<WxAuthRespDTO> getBrandList() {
        List<WxBrandAuthRespDTO> respList = wxBrandAuthClientService.listWxBrand();
        //组装运营主体绑定关系
        try {
            List<String> collect = respList.stream().map(WxBrandAuthRespDTO::getMultiMemberGuid).filter(Objects::nonNull).collect(Collectors.toList());
            HashSet<String> strings = new HashSet<>(collect);
            List<MultiMemberDTO> list = enterpriseRpcService.list(strings);
            if (CollectionUtils.isNotEmpty(list)){
                Map<String, String> multiMap = list.stream().collect(Collectors.toMap(MultiMemberDTO::getMultiMemberGuid, MultiMemberDTO::getMultiMemberName));
                respList.forEach(e->{
                    if (Objects.nonNull(multiMap.get(e.getMultiMemberGuid()))){
                        e.setMultiMemberName(multiMap.get(e.getMultiMemberGuid()));
                    }
                });
            }
        }catch (Exception e){
            log.error("查询运营主体信息异常！",e);
        }
        WxAuthRespDTO wxAuthRespDTO = new WxAuthRespDTO();
        wxAuthRespDTO.setWxBrandAuthRespDTOList(respList);
        return Result.buildSuccessResult(wxAuthRespDTO);
    }

    @PostMapping("/send_message")
    @ApiOperation(value = "解绑公众号前，发送验证码", notes = "传入当前用户guid，向当前用户手机发送短息验证码")
    @ApiImplicitParam(name = "用户guid")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "解绑公众号前，发送验证码")
    public Result<String> sendShortMessage(@RequestBody WxSendShortMsgReqDTO wxSendShortMsgReqDTO) {
        String result = wxBrandAuthClientService.sendShortMessage(wxSendShortMsgReqDTO);
        return StringUtils.isEmpty(result) ? Result.buildOpFailedResult("短息验证码发送失败") : Result.buildSuccessResult(result);
    }

    @PostMapping("/un_band_brand")
    @ApiOperation("通过品牌guid解绑公众号")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "通过品牌guid解绑公众号")
    public Result<String> unBandBrand(@RequestBody WxUnBandReqDTO wxUnBandReqDTO) {
        Integer respCode = wxBrandAuthClientService.unBandBrand(wxUnBandReqDTO);
        return Result.buildFailResult(respCode, WxUnBandRespEnum.getDescByCode(respCode));
    }

    @PostMapping("/get_by_brand_guid")
    @ApiOperation("通过品牌guid获取品牌公众号绑定信息")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "通过品牌guid获取品牌公众号绑定信息")
    public Result<WxBrandAuthRespDTO> getAuthorizeByBrandGuid(@RequestBody WxBaseDTO wxBaseDTO) {
        WxBrandAuthRespDTO wxBrandAuthRespDTO = wxBrandAuthClientService.getByBrandGuid(wxBaseDTO.getBrandGuid());
        return ObjectUtils.isEmpty(wxBrandAuthRespDTO) ? Result.buildOpFailedResult("公众号绑定失败") : Result.buildSuccessResult(wxBrandAuthRespDTO);
    }

    @PostMapping("/query_by_store_guid")
    @ApiOperation("通过门店guid获取门店信息，包含品牌信息")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "通过门店guid获取门店信息，包含品牌信息")
    public Result<BrandDTO> getStoreDTOByGuid(@RequestBody BaseDTO baseDTO) {
        StoreDTO storeDTO = organizationService.queryStoreByGuid(baseDTO.getStoreGuid());
        if (storeDTO.getBelongBrandGuid() == null) {
            throw new BusinessException("当前门店暂未绑定品牌");
        }
        BrandDTO brandDTO = organizationService.queryBrandByGuid(storeDTO.getBelongBrandGuid());
        return Result.buildSuccessResult(brandDTO);
    }

    @ApiOperation(value = "根据企业GuId查询运营主体")
    @PostMapping(value = "/find_by_enterprise_organization_multi")
    public Result<List<OrganizationMultiVO>> findByEnterpriseOrganizationMulti() {
        List<OrganizationMultiVO> multiVOS = Lists.newArrayList();
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        //
        EnterpriseQueryDTO enterpriseQueryDTO = new EnterpriseQueryDTO();
        enterpriseQueryDTO.setEnterpriseGuid(enterpriseGuid);
        EnterpriseDTO enterprise = enterpriseRpcService.findEnterprise(enterpriseQueryDTO);
        log.info("findByEnterpriseOrganizationMulti 企业基本信息数据{}", JacksonUtils.writeValueAsString(enterprise));
        //查询该企业下所有组织及运营主体（多会员体系）
        OrganizationQueryDTO queryDTO = new OrganizationQueryDTO();
        queryDTO.setEnterpriseGuid(enterpriseGuid);
        queryDTO.setPageSize(Integer.MAX_VALUE);
        Page<OrganizationNewDTO> platformDTOPage = enterpriseRpcService.pageQueryAllPlatformStore(queryDTO);
        if (CollectionUtils.isEmpty(platformDTOPage.getData())){
            log.info("无组织运营主体数据！");
            return Result.buildSuccessResult(multiVOS);
        }
        log.info("findByEnterpriseOrganizationMulti 查出的组织运营主体数据{}", JacksonUtils.writeValueAsString(platformDTOPage));
        try {
            List<OrganizationMultiVO> multiVOList = Lists.newArrayList();
            List<OrganizationNewDTO> platformDTOList = platformDTOPage.getData();
                if (CollectionUtils.isNotEmpty(platformDTOList)){
                    platformDTOList.stream().forEach(e->{
                        OrganizationMultiVO organizationMultiVO = new OrganizationMultiVO();
                        organizationMultiVO.setMultiMemberGuid(e.getMultiMemberGuid());
                        organizationMultiVO.setMultiMemberName(e.getMultiMemberName());
                        //平台类型的企业（是否是联盟） 需特殊处理
                        Boolean isManagementModel = false;
                        if (Objects.nonNull(enterprise)){
                            EnterpriseDTO.ManagementModel managementModel = enterprise.getManagementModel();
                            if ("PLATFORM".equals(managementModel)){
                                isManagementModel = true;
                            }
                        }
                        organizationMultiVO.setIsManagementModel(isManagementModel);
                        multiVOList.add(organizationMultiVO);
                    });
                }
            //过滤到重复的运营主体
            multiVOS = multiVOList.stream().collect(
                    collectingAndThen(
                            toCollection(() -> new TreeSet<>(Comparator.comparing(OrganizationMultiVO::getMultiMemberGuid))), ArrayList::new)
            );
        }catch (Exception e){
            log.error("查询运营主体异常",e);
        }
        log.info("findByEnterpriseOrganizationMulti 最终返回出的数据{}",JacksonUtils.writeValueAsString(multiVOS));
        return Result.buildSuccessResult(multiVOS);
    }

    @PostMapping("/bind_subject")
    @ApiOperation("通过品牌guid绑定运营主体与微信公众号信息")
    public Result<Boolean> bindSubject(@RequestBody WxOperSubjectBrandReqDTO wxOperSubjectBrandReqDTO) {
        return Result.buildSuccessResult(wxBrandAuthClientService.bindSubject(wxOperSubjectBrandReqDTO));
    }
}

