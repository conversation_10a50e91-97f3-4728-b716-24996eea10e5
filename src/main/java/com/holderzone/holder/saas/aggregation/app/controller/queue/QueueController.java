package com.holderzone.holder.saas.aggregation.app.controller.queue;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.app.service.QueueTableService;
import com.holderzone.holder.saas.aggregation.app.service.feign.queue.QueueClientService;
import com.holderzone.saas.store.dto.queue.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueController
 * @date 2019/03/27 16:59
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Api("排队 - 队列")
@RestController
@Slf4j
public class QueueController {
    @Autowired
    private QueueClientService queueClientService;
    @Autowired
    private QueueTableService queueTableService;

    @GetMapping("/queue/list")
    @ApiOperation("list")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "商家查询",action = OperatorType.SELECT)
    public Result<StoreQueueDTO> list() {
        log.info("商家查询");
        return Result.buildSuccessResult(queueClientService.list());
    }

    @PostMapping("/queue/removeAll")
    @ApiOperation("removeAll")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "removeAll",action = OperatorType.DELETE)
    public Result<Boolean> removeAll() {
        return Result.buildSuccessResult(queueClientService.removeAll());
    }

    @PostMapping("/queue/table/obtain")
    @ApiOperation("查询队列对应桌台")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "查询队列对应桌台",action = OperatorType.SELECT)
    public Result<QueueTableDTO> obtain(@RequestBody QueueGuidDTO queueGuidDTO) {
        return Result.buildSuccessResult(queueClientService.obtain(queueGuidDTO));
    }

    @PostMapping("/queue/table/merged")
    @ApiOperation("查询队列对应桌台")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "查询队列对应桌台",action = OperatorType.SELECT)
    public Result<QueueTableDTO> merged(@RequestBody QueueGuidDTO queueGuidDTO) {
        return Result.buildSuccessResult(queueTableService.mergeTables(queueGuidDTO));
    }

    @PostMapping("/queue/table")
    @ApiOperation("查询队列对应桌台")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "查询队列对应桌台",action = OperatorType.SELECT)
    public Result<List<QueueTableDTO>> obtain(@RequestBody StoreGuidDTO queueGuidDTO){
        return Result.buildSuccessResult(queueClientService.obtain(queueGuidDTO));
    }
}