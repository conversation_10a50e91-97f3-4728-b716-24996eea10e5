package com.holderzone.holder.saas.aggregation.merchant.service.rpc.manage;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.business.reason.ReasonCopyReqDTO;
import com.holderzone.saas.store.dto.business.reason.ReasonDTO;
import com.holderzone.saas.store.dto.business.reason.ReasonTypeDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReasonClientService
 * @date 2019/08/21 10:00
 * @description //TODO
 * @program holder-saas-store-business
 */
@Component
@FeignClient(name = "holder-saas-store-business", fallbackFactory = ReasonClientService.ReasonFallBack.class)
public interface ReasonClientService {

    @PostMapping(value = "/reason/findReason")
    List<ReasonDTO> findReason(@RequestBody ReasonDTO reasonDTO);

    @PostMapping(value = "/reason/insertReason")
    void insertReason(@RequestBody @Validated ReasonDTO reasonDTO);

    @PostMapping(value = "/reason/updateReason")
    void updateReason(@RequestBody @Validated ReasonDTO reasonDTO);

    @PostMapping(value = "/reason/deleteReason")
    void deleteReason(@RequestBody @Validated ReasonDTO reasonDTO);

    @PostMapping(value = "/reason/findReasonType")
    List<ReasonTypeDTO> findReasonType();

    @PostMapping(value = "/reason/findReasonTypeSuperMarket")
    List<ReasonTypeDTO> findReasonTypeSuperMarket();

    @PostMapping(value = "/reason/copy_reason")
    Boolean copyReason(@RequestBody @Validated ReasonCopyReqDTO copyReqDTO);

    @Slf4j
    @Component
    class ReasonFallBack implements FallbackFactory<ReasonClientService>{

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ReasonClientService create(Throwable cause) {
            return new ReasonClientService() {
                @Override
                public List<ReasonDTO> findReason(ReasonDTO reasonDTO) {
                    log.error(HYSTRIX_PATTERN,"findReason",JacksonUtils.writeValueAsString(reasonDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void insertReason(ReasonDTO reasonDTO) {
                    log.error(HYSTRIX_PATTERN,"insertReason",JacksonUtils.writeValueAsString(reasonDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void updateReason(ReasonDTO reasonDTO) {
                    log.error(HYSTRIX_PATTERN,"updateReason",JacksonUtils.writeValueAsString(reasonDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void deleteReason(ReasonDTO reasonDTO) {
                    log.error(HYSTRIX_PATTERN,"deleteReason",JacksonUtils.writeValueAsString(reasonDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<ReasonTypeDTO> findReasonType() {
                    log.error(HYSTRIX_PATTERN,"findReasonType",ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<ReasonTypeDTO> findReasonTypeSuperMarket() {
                    log.error(HYSTRIX_PATTERN,"findReasonTypeSuperMarket",ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Boolean copyReason(ReasonCopyReqDTO copyReqDTO) {
                    log.error(HYSTRIX_PATTERN,"copyReason",ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }
    }
}
