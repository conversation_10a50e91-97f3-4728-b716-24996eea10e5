package com.holderzone.holder.saas.aggregation.weixin;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, HibernateJpaAutoConfiguration.class})
@EnableDiscoveryClient
@EnableFeignClients
@EnableSwagger2
@EnableEurekaClient
@EnableAsync
@EnableScheduling
@EnableApolloConfig
@ComponentScan(basePackages = {"com.holderzone.holder.saas.aggregation.weixin", "com.holderzone.holder.saas.weixin"
})
public class HolderSaasAggregationWeixinApplication {

    public static void main(String[] args) {
        SpringApplication.run(HolderSaasAggregationWeixinApplication.class, args);

    }

}

