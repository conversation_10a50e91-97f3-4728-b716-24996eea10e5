package com.holderzone.holder.saas.aggregation.phoneapp.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.organization.*;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className OrganizationService
 * @date 19-1-8 下午4:02
 * @description 服务间调用-组织相关服务
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-store-organization", fallbackFactory = OrganizationService.ServiceFallBack.class)
public interface OrganizationService {
    @PostMapping("/brand/create")
    BrandDTO createBrand(@RequestBody BrandDTO brandDTO);

    @PostMapping("/brand/update")
    boolean updateBrand(@RequestBody BrandDTO brandDTO);

    @PostMapping("/brand/delete")
    boolean deleteBrand(@RequestParam("brandGuid") String brandGuid);

    @PostMapping("/brand/query_list")
    List<BrandDTO> queryBrandList();

    @PostMapping("/brand/query_exist_store_account")
    boolean queryExistStoreAccount(@RequestParam("storeGuid") String storeGuid);

    @PostMapping("/store/create")
    boolean createStore(StoreDTO storeDTO);

    @PostMapping("/store/update")
    boolean updateStore(StoreDTO storeDTO);

    @PostMapping("/store/enable")
    boolean enableOrDisableStore(@RequestParam("storeGuid") String storeGuid);

    @PostMapping("/store/delete")
    boolean deleteStore(@RequestParam("storeGuid") String storeGuid);

    @PostMapping("/store/query_by_condition")
    Page<StoreDTO> queryStoreByCondition(@RequestBody QueryStoreDTO queryStoreDTO);

    @PostMapping("/store/query_by_condition_no_page")
    List<StoreDTO> queryStoreByConditionNoPage(@RequestBody StoreParserDTO storeParserDTO);

    @PostMapping("/store/query_all_store")
    List<StoreDTO> queryAllStore();

    @PostMapping("/organization/create")
    OrganizationDTO createOrganization(@RequestBody OrganizationDTO organizationDTO);

    @PostMapping("/organization/update")
    boolean updateOrganization(@RequestBody OrganizationDTO organizationDTO);

    @PostMapping("/organization/delete")
    boolean deleteOrganization(@RequestParam("organizationGuid") String organizationGuid);

    @PostMapping("/organization/query_exist_organization_or_store")
    boolean queryExistOrganizationOrStore(@RequestParam("organizationGuid") String organizationGuid);

    @PostMapping("/organization/get_optional_organization")
    List<OrganizationDTO> getOptionalOrganization(@RequestParam("organizationGuid") String organizationGuid);

    @PostMapping("/organization/query_enterprise_and_organization")
    List<OrganizationDTO> queryEnterpriseAndOrganization();

    @PostMapping("/organization/query_all_organization")
    List<OrgGeneralDTO> queryAllOrganization();

    @PostMapping("/organization/query_exist_account")
    boolean queryExistAccount(@RequestParam("organizationGuid") String organizationGuid);

    @PostMapping("/store/parse_by_condition")
    List<String> parseByCondition(@RequestBody StoreParserDTO storeParserDTO);

    @PostMapping("/store/parse_by_condition_not_union")
    List<String> parseByConditionNotUnion(@RequestBody StoreParserDTO storeParserDTO);

    @PostMapping("/store/query_store_by_guid")
    StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid);

    @PostMapping("/brand/query_brand_by_guid")
    BrandDTO queryBrandByGuid(@RequestParam("brandGuid") String brandGuid);

    @PostMapping("/brand/query_store_guid_list_by_brand_guid")
    List<String> queryStoreGuidListByBrandGuid(@RequestBody String brandGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<OrganizationService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public OrganizationService create(Throwable cause) {
            return new OrganizationService() {
                @Override
                public BrandDTO createBrand(BrandDTO brandDTO) {
                    log.error(HYSTRIX_PATTERN, "createBrand", JacksonUtils.writeValueAsString(brandDTO), ThrowableUtils.asString(cause));
                    throw new BusinessException("创建品牌失败");
                }

                @Override
                public boolean updateBrand(BrandDTO brandDTO) {
                    log.error(HYSTRIX_PATTERN, "updateBrand", JacksonUtils.writeValueAsString(brandDTO), ThrowableUtils.asString(cause));
                    return false;
                }

                @Override
                public boolean deleteBrand(String brandGuid) {
                    log.error(HYSTRIX_PATTERN, "deleteBrand", "入参brandGuid为：" + brandGuid, ThrowableUtils.asString(cause));
                    return false;
                }

                @Override
                public List<BrandDTO> queryBrandList() {
                    log.error(HYSTRIX_PATTERN, "queryBrandList", null, ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public boolean queryExistStoreAccount(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "门店guid为： " + storeGuid, ThrowableUtils.asString(cause));
                    return false;
                }

                @Override
                public boolean createStore(StoreDTO storeDTO) {
                    log.error(HYSTRIX_PATTERN, "createStore", JacksonUtils.writeValueAsString(storeDTO), ThrowableUtils.asString(cause));
                    return false;
                }

                @Override
                public boolean updateStore(StoreDTO storeDTO) {
                    log.error(HYSTRIX_PATTERN, "updateStore", JacksonUtils.writeValueAsString(storeDTO), ThrowableUtils.asString(cause));
                    return false;
                }

                @Override
                public boolean enableOrDisableStore(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "enableOrDisableStore", "入参storeGuid为：" + storeGuid, ThrowableUtils.asString(cause));
                    return false;
                }

                @Override
                public boolean deleteStore(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "deleteStore", "入参storeGuid为：" + storeGuid, ThrowableUtils.asString(cause));
                    return false;
                }

                @Override
                public Page<StoreDTO> queryStoreByCondition(QueryStoreDTO queryStoreDTO) {
                    log.error(HYSTRIX_PATTERN, "queryStoreByCondition", JacksonUtils.writeValueAsString(queryStoreDTO), ThrowableUtils.asString(cause));
                    throw new BusinessException("查询门店失败");
                }

                @Override
                public List<StoreDTO> queryStoreByConditionNoPage(StoreParserDTO storeParserDTO) {
                    log.error(HYSTRIX_PATTERN, "queryStoreByConditionNoPage", JacksonUtils.writeValueAsString(storeParserDTO), ThrowableUtils.asString(cause));
                    throw new BusinessException("查询门店失败");
                }

                @Override
                public List<StoreDTO> queryAllStore() {
                    throw new BusinessException("查询门店失败");
                }

                @Override
                public OrganizationDTO createOrganization(OrganizationDTO organizationDTO) {
                    log.error(HYSTRIX_PATTERN, "createOrganization", JacksonUtils.writeValueAsString(organizationDTO), ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public boolean updateOrganization(OrganizationDTO organizationDTO) {
                    log.error(HYSTRIX_PATTERN, "updateOrganization", JacksonUtils.writeValueAsString(organizationDTO), ThrowableUtils.asString(cause));
                    return false;
                }

                @Override
                public boolean deleteOrganization(String organizationGuid) {
                    log.error(HYSTRIX_PATTERN, "deleteOrganization", "入参organizationGuid为：" + organizationGuid, ThrowableUtils.asString(cause));
                    return false;
                }

                @Override
                public boolean queryExistOrganizationOrStore(String organizationGuid) {
                    log.error(HYSTRIX_PATTERN, "queryExistOrganizationOrStore", "入参organizationGuid为：" + organizationGuid, ThrowableUtils.asString(cause));
                    throw new BusinessException("查询失败");
                }

                @Override
                public List<OrganizationDTO> getOptionalOrganization(String organizationGuid) {
                    log.error(HYSTRIX_PATTERN, "getOptionalOrganization", "入参organizationGuid为：" + organizationGuid, ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public List<OrganizationDTO> queryEnterpriseAndOrganization() {
                    log.error(HYSTRIX_PATTERN, "queryEnterpriseAndOrganization", null, ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public List<OrgGeneralDTO> queryAllOrganization() {
                    log.error(HYSTRIX_PATTERN, "queryAllOrganization", null, ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public boolean queryExistAccount(String organizationGuid) {
                    log.error(HYSTRIX_PATTERN, "queryExistAccount", "门店guid为：" + organizationGuid, ThrowableUtils.asString(cause));
                    return false;
                }

                @Override
                public List<String> parseByCondition(StoreParserDTO storeParserDTO) {
                    log.error(HYSTRIX_PATTERN, "parseByCondition", JacksonUtils.writeValueAsString(storeParserDTO), ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public List<String> parseByConditionNotUnion(StoreParserDTO storeParserDTO) {
                    log.error(HYSTRIX_PATTERN, "parseByConditionNotUnion", JacksonUtils.writeValueAsString(storeParserDTO), ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public StoreDTO queryStoreByGuid(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "queryStoreByGuid", storeGuid, ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public BrandDTO queryBrandByGuid(String brandGuid) {
                    log.error(HYSTRIX_PATTERN, "queryBrandByGuid", brandGuid, ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public List<String> queryStoreGuidListByBrandGuid(String brandGuid) {
                    log.error(HYSTRIX_PATTERN, "queryStoreGuidListByBrandGuid", brandGuid, ThrowableUtils.asString(cause));
                    return null;
                }
            };
        }
    }
}