package com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.order;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.report.OrderRecoveryDetailDTO;
import com.holderzone.saas.store.dto.report.query.OrderDetailQueryDTO;
import com.holderzone.saas.store.dto.report.query.OrderTransRecordQueryDTO;
import com.holderzone.saas.store.dto.report.resp.OrderTransRecordRespDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderTranceClientService
 * @date 2018/10/08 18:22
 * @description 订单交易报表clientService
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(value = "holder-saas-store-report",fallbackFactory = OrderTranceClientService.ServiceFallBack.class)
public interface OrderTranceClientService {

    @PostMapping("/orderTrans/orderList")
    Page<OrderTransRecordRespDTO> orderList( @RequestBody   OrderTransRecordQueryDTO transRecordQueryDTO);
    @PostMapping("/orderTrans/orderDetail")
    OrderRecoveryDetailDTO orderDetail(@RequestBody OrderDetailQueryDTO queryDTO);
    @PostMapping(value = "/orderTrans/export")
    void export(OrderTransRecordQueryDTO transRecordQueryDTO);

    @Component
    class ServiceFallBack implements FallbackFactory<OrderTranceClientService> {

        private static final Logger logger = LoggerFactory.getLogger(OrderTranceClientService.ServiceFallBack.class);

        @Override
        public OrderTranceClientService create(Throwable throwable) {
            return new OrderTranceClientService(){
                @Override
                public Page<OrderTransRecordRespDTO> orderList(OrderTransRecordQueryDTO transRecordQueryDTO) {
                    logger.error("查询订单交易记录列表失败，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public OrderRecoveryDetailDTO orderDetail(@RequestBody OrderDetailQueryDTO queryDTO) {
                    logger.error("查询交易订单详情失败，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public void export(OrderTransRecordQueryDTO transRecordQueryDTO) {
                    logger.error("导出交易订单列表失败，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }
            };
        }
    }

}
