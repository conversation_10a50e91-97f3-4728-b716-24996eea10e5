package com.holderzone.holder.saas.aggregation.merchant.controller.queue;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.queue.QueueClientService;
import com.holderzone.saas.store.dto.queue.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueController
 * @date 2019/03/27 16:59
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Api("排队 - 队列")
@RestController
public class QueueController {
    @Autowired
    private QueueClientService queueClientService;

    @PostMapping("/queue/table/save")
    @ApiOperation("保存桌台")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "保存桌台")
    public Result<QueueTableDTO> saveTable(@RequestBody QueueTableDTO tableDTO) {
        try {
            return Result.buildSuccessResult(queueClientService.saveTable(tableDTO));
        }catch (Exception e){
            if(e.getMessage().equals("replicate_tables")){
                return Result.buildFailResult(1002,"桌台被占用");
            }
            throw e;
        }
    }
    @GetMapping("/queue/table/all")
    @ApiOperation("查询可用桌台")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "查询可用桌台")
    public Result<List<TreeTableDTO>> allTables(@RequestParam("storeGuid") String storeGuid) {
        return Result.buildSuccessResult(queueClientService.allTables(storeGuid));
    }
    @PostMapping("/queue/save")
    @ApiOperation("修改/保存")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "修改/保存")
    public Result<String> save(@Valid @RequestBody HolderQueueDTO dto) {
        try {
            return Result.buildSuccessResult(queueClientService.save(dto));
        }catch (Exception e){
            if(e.getMessage().equals("had_deleted")){
                return Result.buildFailResult(1003,"队列已删除");
            }
            throw e;
        }
    }

    @PostMapping("/queue/enable")
    @ApiOperation("禁用/启用")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "禁用/启用")
    public Result<Boolean> enable(@RequestParam("guid") String guid) {
        return Result.buildSuccessResult(queueClientService.enable(guid));
    }

    @DeleteMapping("/queue/delete")
    @ApiOperation("删除")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "删除")
    public Result<Boolean> delete(@RequestParam("guid") String guid) {
        return Result.buildSuccessResult(queueClientService.delete(guid));
    }
    @PostMapping("/queue/obtain")
    @ApiOperation("obtain")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "obtain")
    public Result<HolderQueueDTO> fetchOne(@RequestBody QueueGuidDTO dto){
        return Result.buildSuccessResult(queueClientService.fetchOne(dto));
    }
    @GetMapping("/queue/query")
    @ApiOperation("query")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "query")
    public Result<List<QueueDetailDTO>> query(@RequestParam("storeGuid") String storeGuid) {
        return Result.buildSuccessResult(queueClientService.query(storeGuid));
    }

}