package com.holderzone.holder.saas.aggregation.merchant.service.rpc.manage;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceQueryDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceSortDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceUnbindDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className StoreDeviceService
 * @date 18-9-13 下午4:07
 * @description 服务间调用-设备/门店服务
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-store-organization", fallbackFactory = StoreDeviceService.ServiceFallBack.class)
public interface StoreDeviceService {

    @PostMapping(value = "/device/create")
    boolean create(@RequestBody StoreDeviceDTO storeDeviceDTO);

    @PostMapping(value = "/device/find_store_device")
    List<StoreDeviceDTO> findStoreDevice(@RequestBody @Validated StoreDeviceQueryDTO storeDeviceQueryDTO);

    @PostMapping(value = "/device/sort")
    boolean sort(@RequestBody @Validated List<StoreDeviceSortDTO> storeDeviceSortDTOS);

    @PostMapping(value = "/device/unbind")
    boolean unbind(@RequestBody @Validated StoreDeviceUnbindDTO storeDeviceUnbindDTO);

    @GetMapping(value = "/device/find_device_status/{deviceNo}")
    StoreDeviceDTO findDeviceStatus(@ApiParam(value = "厂商设备编号") @PathVariable("deviceNo") String deviceNo);

    @PostMapping(value = "/device/set_master/{deviceGuid}")
    void setMasterDevice(@PathVariable("deviceGuid") String deviceGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<StoreDeviceService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public StoreDeviceService create(Throwable cause) {
            return new StoreDeviceService() {

                @Override
                public boolean create(StoreDeviceDTO storeDeviceDTO) {
                    log.error(HYSTRIX_PATTERN, "create", JacksonUtils.writeValueAsString(storeDeviceDTO),
                              ThrowableUtils.asString(cause));
                    return false;
                }

                @Override
                public List<StoreDeviceDTO> findStoreDevice(StoreDeviceQueryDTO storeDeviceQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "findStoreDevice", JacksonUtils.writeValueAsString(storeDeviceQueryDTO),
                              ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public boolean sort(List<StoreDeviceSortDTO> storeDeviceSortDTOS) {
                    log.error(HYSTRIX_PATTERN, "sort", JacksonUtils.writeValueAsString(storeDeviceSortDTOS),
                              ThrowableUtils.asString(cause));
                    return false;
                }

                @Override
                public boolean unbind(StoreDeviceUnbindDTO storeDeviceUnbindDTO) {
                    log.error(HYSTRIX_PATTERN, "unbind", JacksonUtils.writeValueAsString(storeDeviceUnbindDTO),
                              ThrowableUtils.asString(cause));
                    return false;
                }

                @Override
                public StoreDeviceDTO findDeviceStatus(String deviceId) {
                    log.error(HYSTRIX_PATTERN, "findDeviceStatus", "设备编号：" + deviceId, ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public void setMasterDevice(String deviceGuid) {
                    log.error(HYSTRIX_PATTERN, "setMasterDevice", "设备编号：" + deviceGuid, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }
    }
}
