package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.dto.pay.SaasPollingDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className updateUserLogin
 * @date 2019/09/12 10:39
 * @description //TODO
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = UpdateUserLoginClientService.UpdateUserLoginFallBack.class)
public interface UpdateUserLoginClientService {

    @PostMapping("/wx_open/update_login")
    @ApiOperation(value = "修改登录状态")
    Boolean updateUserLogin(@RequestBody WxStoreConsumerDTO wxStoreConsumerDTO);


    @Slf4j
    @Component
    class UpdateUserLoginFallBack implements FallbackFactory<UpdateUserLoginClientService> {


        @Override
        public UpdateUserLoginClientService create(Throwable throwable) {
            return new UpdateUserLoginClientService() {
                @Override
                public Boolean updateUserLogin(WxStoreConsumerDTO wxStoreConsumerDTO) {
                    log.error("远程调用信息失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }
            };
        }
    }

}
