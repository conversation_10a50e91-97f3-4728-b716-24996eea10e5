package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.util.MemberResult;
import com.holderzone.holder.saas.member.merchant.dto.statistics.RequestMemberAge;
import com.holderzone.holder.saas.member.merchant.dto.statistics.ResponseAgeDistribution;
import com.holderzone.holder.saas.member.merchant.dto.statistics.ResponseMemberGrowth;
import com.holderzone.holder.saas.member.merchant.dto.statistics.ResponseMemberSexAndConsume;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.dto.report.openapi.MemberRegisterReqDTO;
import com.holderzone.saas.store.dto.report.openapi.MemberRegisterRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HsmMemberDataCenterClientService
 * @date 2019/08/29 15:35
 * @description
 * @program holder-saas-store
 */
@Component
@FeignClient(name = "holder-saas-member-merchant", fallbackFactory = HsmMemberDataCenterClientService.FallBackClass.class)
public interface HsmMemberDataCenterClientService {

    @PostMapping("/hsm-member-statistics/ageDistribution")
    MemberResult<List<ResponseAgeDistribution>> ageDistribution(@RequestBody RequestMemberAge body);

    @GetMapping("/hsm-member-statistics/memberGrowth")
    MemberResult<List<ResponseMemberGrowth>> memberGrowth(@RequestParam("number") String number);

    @GetMapping("/hsm-member-statistics/querySexAndConsume")
    MemberResult<ResponseMemberSexAndConsume> querySexAndConsume();

    @ApiOperation(value = "查询会员余额支付成分")
    @PostMapping(value = "/hsmdc/store/business/query_member_pay_constitute")
    MemberResult<List<AmountItemDTO>> queryMemberPayConstitute(@RequestBody DailyReqDTO query);

    @ApiOperation(value = "注册会员and开通卡")
    @PostMapping(value = "/hsa-member/registerMemberAndCardOpen")
    MemberResult<MemberRegisterRespDTO> registerMemberAndCardOpen(@RequestBody MemberRegisterReqDTO reqDTO);

    @Component
    @Slf4j
    class FallBackClass implements FallbackFactory<HsmMemberDataCenterClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public HsmMemberDataCenterClientService create(Throwable throwable) {
            return new HsmMemberDataCenterClientService() {
                @Override
                public MemberResult<List<ResponseAgeDistribution>> ageDistribution(RequestMemberAge body) {
                    log.error(HYSTRIX_PATTERN, "ageDistribution", JacksonUtils.writeValueAsString(body), throwable);
                    throw new RuntimeException("查询会员年龄分布发生异常");
                }

                @Override
                public MemberResult<List<ResponseMemberGrowth>> memberGrowth(String number) {
                    log.error(HYSTRIX_PATTERN, "memberGrowth", number, throwable);
                    throw new RuntimeException("查询会员增长情况发生异常");
                }

                @Override
                public MemberResult<ResponseMemberSexAndConsume> querySexAndConsume() {
                    log.error(HYSTRIX_PATTERN, "memberGrowth", null, throwable);
                    throw new RuntimeException("查询门店消费者性别构成发生异常");
                }

                @Override
                public MemberResult<List<AmountItemDTO>> queryMemberPayConstitute(DailyReqDTO query) {
                    log.error(HYSTRIX_PATTERN, "queryMemberPayConstitute", JacksonUtils.writeValueAsString(query), throwable);
                    throw new BusinessException("查询会员余额支付成分异常");
                }

                @Override
                public MemberResult<MemberRegisterRespDTO> registerMemberAndCardOpen(MemberRegisterReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "registerMemberAndCardOpen", JacksonUtils.writeValueAsString(reqDTO), throwable);
                    throw new BusinessException("注册会员异常");
                }
            };
        }
    }
}
