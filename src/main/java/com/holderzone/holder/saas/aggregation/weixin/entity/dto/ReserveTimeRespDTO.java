package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalTime;

@ApiModel("预订可使用时间范围")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
public class ReserveTimeRespDTO {

	@ApiModelProperty("开始时间")
	private LocalTime reserveStartTime;
	@ApiModelProperty("结束时间")
	private LocalTime reserveEndTime;
}
