package com.holderzone.holder.saas.aggregation.app.controller.log;


import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.device.DeviceService;
import com.holderzone.holder.saas.aggregation.app.service.feign.log.LogFeignService;
import com.holderzone.resource.common.dto.device.DeviceRequestLogDTO;
import com.holderzone.saas.store.dto.log.request.client.LogClientErrorReportDTO;
import com.holderzone.saas.store.dto.log.request.client.LogClientOperatReportDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Athor forewei
 * @Email <EMAIL>
 * @Date 2019/12/09
 */
@Api(tags = "holder-saas-cloud-log", description = "日志服务相关的api")
@RestController
@RequestMapping(value = "log", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@Slf4j
public class LogFeignController {

    @Autowired
    private LogFeignService logFeignService;

    @Autowired
    private DeviceService deviceService;

    @ApiOperation(value = "客户端上报操作日志")
    @PostMapping("client/report/operat")
    public void clientReportOperatLog(@Validated @RequestBody LogClientOperatReportDTO logClientOperatReportDTO) {
        logFeignService.clientReportOperatLog(logClientOperatReportDTO);
    }

    @ApiOperation(value = "客户端上报错误日志")
    @PostMapping("client/report/error")
    public void clientReportErrorLog(@Validated @RequestBody LogClientErrorReportDTO logClientErrorReportDTO) {
        logFeignService.clientReportErrorLog(logClientErrorReportDTO);
    }

    @ApiOperation(value = "添加请求记录")
    @PostMapping("/operate")
    public Result addRequestLog(@RequestBody DeviceRequestLogDTO requestLogDTO) {
        log.info("添加设备请求记录:{}", JacksonUtils.writeValueAsString(requestLogDTO));
        deviceService.addRequestLog(requestLogDTO);
        return Result.buildEmptySuccess();
    }
}
