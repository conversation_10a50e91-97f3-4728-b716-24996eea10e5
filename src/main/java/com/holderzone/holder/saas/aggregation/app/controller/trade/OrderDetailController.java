package com.holderzone.holder.saas.aggregation.app.controller.trade;

import com.holderzone.holder.saas.aggregation.app.service.feign.trade.OrderItemClientService;
import com.holderzone.saas.store.dto.order.inside.OrderGuidsDTO;
import com.holderzone.saas.store.dto.trade.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/order_detail")
@Api(tags = "订单信息接口")
@Slf4j
public class OrderDetailController {

    private final OrderItemClientService orderItemClientService;

    @ApiOperation(value = "获取订单商品", notes = "获取订单商品")
    @PostMapping("/get_order_items")
    public List<OrderItemDTO> getOrderItem(@RequestBody OrderGuidsDTO orderGuidsDTO) {
        return orderItemClientService.getOrderItems(orderGuidsDTO);
    }

    @ApiOperation(value = "获取微信订单详情", notes = "获取微信订单详情")
    @PostMapping("/find_by_wx_order_guid")
    public OrderDetailPushMqDTO findByWxOrderGuid(@RequestBody OrderGuidsDTO orderGuidsDTO) {
        return orderItemClientService.findByWxOrderGuid(orderGuidsDTO);
    }

}
