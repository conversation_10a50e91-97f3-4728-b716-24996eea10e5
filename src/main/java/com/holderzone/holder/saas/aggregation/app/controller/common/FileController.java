package com.holderzone.holder.saas.aggregation.app.controller.common;

import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.app.entity.FileUploadDTO;
import com.holderzone.holder.saas.aggregation.app.execption.FileIllegalException;
import com.holderzone.holder.saas.aggregation.app.service.FileUploadService;
import com.holderzone.holder.saas.aggregation.app.utils.UploadValidateUtil;
import com.holderzone.saas.store.enums.PictureEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 文件接口
 * @date 2022/8/26 11:28
 * @className: FileController
 */
@Slf4j
@RestController
@RequestMapping("/file")
@Api(tags = "文件接口")
@AllArgsConstructor
public class FileController {

    private final FileUploadService fileUploadService;

    @ApiOperation(value = "文件上传接口,文件上传name 必须为file, 不传入type或者为空表示不作处理",
            notes = "单个图片大小不能超过20M，上传成功返回图片的地址，失败返回空，w*h type=1表示1080*1920,大小为20M," +
                    "type=3表示1024*600,大小为20M,type=4表示635*600,大小为20M," +
                    "type=2表示800*800,大小为0.5M,type=5表示1920*1080,大小为20M," +
                    "type=6表示650*690，大小为20M")
    @PostMapping("/upload")
    public Result<String> upload(@RequestParam(value = "file") MultipartFile file, @RequestParam(value = "type", required = false)
            Integer type) {
        log.info("文件上传， fileName={},type={}", file.getOriginalFilename(), type);
        FileUploadDTO fileUploadDTO = checkFile(file, type);
        return Result.buildSuccessResult(fileUploadService.upload(fileUploadDTO, file));
    }

    @ApiOperation(value = "文件上传接口,文件上传name 必须为file",
            notes = "单个图片大小不能超过3M，上传成功返回图片的地址，失败返回空")
    @PostMapping("/batch/upload")
    public Result<List<String>> upload(@RequestParam(value = "fileList") List<MultipartFile> fileList,
                                       @RequestParam(value = "type", required = false) Integer type) {
        log.info("文件批量上传，type={}", type);
        List<String> urls = fileList.stream()
                .map(file -> {
                    log.info("文件批量上传,fileName={}", file.getOriginalFilename());
                    FileUploadDTO fileUploadDTO = checkFile(file, type);
                    log.info("1111");
                    return fileUploadService.upload(fileUploadDTO, file);
                })
                .collect(Collectors.toList());
        return Result.buildSuccessResult(urls);
    }

    @PostMapping("/more_upload")
    @ResponseBody
    public Result<List<String>> handleFileUpload(HttpServletRequest request) {
        List<MultipartFile> fileList = ((MultipartHttpServletRequest) request).getFiles("file");
        List<String> urls = new ArrayList<>();
        for (int i = 0; i < fileList.size(); i++) {
            MultipartFile file = fileList.get(i);
            if (!file.isEmpty()) {
                log.info("文件批量上传,fileName={}", file.getOriginalFilename());
                FileUploadDTO fileUploadDTO = checkFile(file, 2);
                String url = fileUploadService.upload(fileUploadDTO, file);
                urls.add(url);
            } else {
                return Result.buildOpFailedResult(file.getOriginalFilename() + "上传失败");
            }
        }
        return Result.buildSuccessResult(urls);
    }

    /**
     * 校验并处理文件
     *
     * @param file 图片
     * @param type 图片类型
     * @return 图片地址
     */
    private FileUploadDTO checkFile(MultipartFile file, Integer type) {
        FileUploadDTO fileUploadDTO = null;
        // 仅校验商品图片
        String fileName = file.getOriginalFilename();
        String fileType = fileName != null && fileName.contains(".") ?
                fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length()) : null;
        if (UploadValidateUtil.typeNotValidate(fileType)) {
            log.info("当前文件类型为：{}", fileType);
            throw new FileIllegalException("文件格式必须为 jpg/png/bmp/jpeg/gif 格式！！！");
        }
        if (!UploadValidateUtil.whetherImage(file)) {
            throw new FileIllegalException("请上传正确的图片");
        }
        if (null != type) {
            PictureEnum byType = PictureEnum.getByType(type);//根据type查找图片大小标准
            if (null != byType) {
                Integer height = byType.getHeight();
                Integer weight = byType.getWeight();
                Long size = byType.getSize();
                fileUploadDTO = new FileUploadDTO();
                fileUploadDTO.setHeight(height);
                fileUploadDTO.setWeight(weight);
                fileUploadDTO.setSize(size);
                // 仅修改商品上传图片大小限制
                if (null != size && file.getSize() > size) { // 判断图片是否超过限制
                    throw new FileIllegalException("文件大小超过限制");
                }
            }
        }
        return fileUploadDTO;
    }
}
