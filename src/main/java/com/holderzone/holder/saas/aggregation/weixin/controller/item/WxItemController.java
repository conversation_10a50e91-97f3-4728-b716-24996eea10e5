package com.holderzone.holder.saas.aggregation.weixin.controller.item;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.weixin.entity.cmember.HistorySearchDTO;
import com.holderzone.holder.saas.aggregation.weixin.helper.HistorySearchHelper;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxItemClientService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.item.resp.ItemAndTypeForAndroidRespDTO;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @className WxItemController
 * @date 2019/01/23 14:13
 * @description 微信商品接口
 * @program holder-saas-store-weixin
 */
@RestController
@RequestMapping("/wxItem")
@AllArgsConstructor
public class WxItemController {

    private final WxItemClientService wxItemClientService;

    private final HistorySearchHelper historySearchHelper;


    @ApiOperation(value = "获取当前门店商品列表")
    @PostMapping("/getItems")
    public ItemAndTypeForAndroidRespDTO getItems(BaseDTO baseDTO){
        return wxItemClientService.getItemsForWeixin(baseDTO);
    }

    @PostMapping("/history/put")
    public Result<Void> historyPut(@RequestBody @Validated HistorySearchDTO historySearch){
        historySearchHelper.putHistorySearchRecord(historySearch);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/history/list")
    public Result<List<String>> historyList(@RequestBody @Validated HistorySearchDTO historySearch){
        return Result.buildSuccessResult(historySearchHelper.getHistorySearchRecord(historySearch));
    }

    @PostMapping("/history/clear")
    public Result<List<String>> historyClear(@RequestBody @Validated HistorySearchDTO historySearch){
        historySearchHelper.removeHistorySearchRecord(historySearch);
        return Result.buildEmptySuccess();
    }
}
