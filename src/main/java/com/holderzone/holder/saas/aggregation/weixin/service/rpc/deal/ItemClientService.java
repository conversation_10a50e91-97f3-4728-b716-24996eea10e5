package com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.common.TypeSingleDTO;
import com.holderzone.saas.store.dto.item.req.ItemQueryListReq;
import com.holderzone.saas.store.dto.item.req.ItemRedisReqDTO;
import com.holderzone.saas.store.dto.item.req.WxSearchItemDto;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.weixin.deal.PricePlanChangeRequestDTO;
import com.holderzone.saas.store.dto.weixin.deal.PricePlanChangeResponseDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxItemClientService
 * @date 2019/01/23 14:27
 * @description //TODO
 * @program ${MODULE_NAME}
 */
@Component
@FeignClient(name = "holder-saas-store-item", fallbackFactory = ItemClientService.WxItemFallBack.class)
public interface ItemClientService {

    /**
     * 获取分类排序  区分销售模式
     *
     * @param typeSingleDTO
     * @return
     */
    @ApiOperation(value = "根据商品guid获取门店商品详情列表（区分销售模式）")
    @PostMapping("/item/query_store_item_type")
    List<ItemInfoTypeRespDTO> queryStoreItemType(@RequestBody TypeSingleDTO typeSingleDTO);

    /**
     * 查询扫码点餐菜谱变动情况
     *
     * @param pricePlanChangeRequestDTO pricePlanChangeRequestDTO
     * @return PricePlanChangeResponseDTO
     */
    @PostMapping("/plan/pricePlanChangeInfo")
    PricePlanChangeResponseDTO pricePlanChangeInfo(@RequestBody PricePlanChangeRequestDTO pricePlanChangeRequestDTO);

    @PostMapping("/item/query_for_synchronize")
    ItemAndTypeForAndroidRespDTO getItemsForWeixin(@RequestBody BaseDTO baseDTO);

    @PostMapping("/item/list_sku_info/by_mode")
    List<SkuInfoRespDTO> listSkuInfoByRecipeMode(@RequestBody ItemStringListDTO itemStringListDTO);

    /**
     * 赚餐扫描点餐搜索商品
     *
     * @return 商品分页数据
     */
    @PostMapping("/item/wx_search")
    Page<ItemSynRespDTO> wxSearchItems(@RequestBody WxSearchItemDto dto);

    @PostMapping(value = "/estimate/query_estimate_for_synchronize")
    List<ItemEstimateForAndroidRespDTO> queryEstimateForSyn(@RequestBody BaseDTO baseDTO);

    @PostMapping(value = "/estimate/dinein_fail")
    Boolean dineinFail(@RequestBody List<DineInItemDTO> request);

    @ApiOperation("存入item服务的redis")
    @PostMapping("/item/put_item_redis")
    Boolean putItemRedis(@RequestBody ItemRedisReqDTO redisReqDTO);

    /**
     * 根据商品guid查询商品详情
     *
     * @param itemSingleDTO 商品guid放在data里就行
     * @return 商品详情
     */
    @ApiOperation(value = "商品详情", notes = "商品详情")
    @PostMapping("/item/get_item_info")
    ItemInfoRespDTO getItemInfo(@RequestBody ItemSingleDTO itemSingleDTO);

    @PostMapping("/item/get_item_info_estimate")
    ItemInfoEstimateSingleDTO getItemInfoEstimate(@RequestBody ItemQueryListReq itemSingleDTO);

    @ApiOperation(value = "获取商品详情列表", notes = "获取商品详情列表")
    @PostMapping("/item/get_item_info_list")
    List<ItemInfoRespDTO> selectItemInfoList(@RequestBody ItemStringListDTO itemStringListDTO);

    @ApiOperation(value = "获取商品列表 - 已删除商品也需查询", notes = "获取商品列表 - 已删除商品也需查询")
    @PostMapping("/item/get_items")
    List<ItemInfoRespDTO> selectItems(@RequestBody ItemStringListDTO itemStringListDTO);

    @ApiOperation(value = "获取商品信息", notes = "获取商品信息")
    @PostMapping("/item/getItemInfoList3")
    List<String> getItemInfoList3(@RequestBody ItemStringListDTO stringListDTO);

    /**
     * 查询推荐商品
     */
    @ApiOperation(value = "查询推荐商品")
    @PostMapping("/item/query_recommend_item")
    List<ItemSynRespDTO> queryRecommendItem(@RequestBody ItemSingleDTO query);

    @Slf4j
    @Component
    class WxItemFallBack implements FallbackFactory<ItemClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ItemClientService create(Throwable throwable) {
            return new ItemClientService() {

                @Override
                public List<ItemInfoTypeRespDTO> queryStoreItemType(TypeSingleDTO typeSingleDTO) {
                    log.error("queryStoreItemType失败:{}", typeSingleDTO);
                    return Collections.emptyList();
                }

                @Override
                public PricePlanChangeResponseDTO pricePlanChangeInfo(PricePlanChangeRequestDTO pricePlanChangeRequestDTO) {
                    log.error("查询扫码点餐菜谱变动情况失败：{}", JacksonUtils.writeValueAsString(pricePlanChangeRequestDTO));
                    return null;
                }

                @Override
                public ItemAndTypeForAndroidRespDTO getItemsForWeixin(BaseDTO baseDTO) {
                    log.error("获取商品信息失败", throwable.getMessage());
                    ItemAndTypeForAndroidRespDTO itemAndTypeForAndroidRespDTO = new ItemAndTypeForAndroidRespDTO();
                    itemAndTypeForAndroidRespDTO.setItemList(Collections.emptyList());
                    itemAndTypeForAndroidRespDTO.setTypeList(Collections.emptyList());
                    return itemAndTypeForAndroidRespDTO;
                }

                @Override
                public List<SkuInfoRespDTO> listSkuInfoByRecipeMode(ItemStringListDTO itemStringListDTO) {
                    log.error("查询商品失败:{}", itemStringListDTO);
                    return Collections.emptyList();
                }

                @Override
                public Page<ItemSynRespDTO> wxSearchItems(WxSearchItemDto dto) {
                    log.error("赚餐扫码点餐搜索商品失败：storeGuid:{} keywords:{}", dto.getStoreGuid(), dto.getKeywords());
                    return new Page<>();
                }

                @Override
                public List<ItemEstimateForAndroidRespDTO> queryEstimateForSyn(BaseDTO baseDTO) {
                    log.error("查询估清失败:{}", baseDTO);
                    return Collections.emptyList();
                }

                @Override
                public Boolean dineinFail(List<DineInItemDTO> request) {
                    log.error("退还库存失败:{}", request);
                    return false;
                }

                @Override
                public Boolean putItemRedis(ItemRedisReqDTO redisReqDTO) {
                    log.error("存入item服务的redis 失败:{}", JacksonUtils.writeValueAsString(redisReqDTO));
                    return false;
                }

                @Override
                public ItemInfoRespDTO getItemInfo(ItemSingleDTO itemSingleDTO) {
                    log.error("查询item服务的商品详情 失败:{}", JacksonUtils.writeValueAsString(itemSingleDTO));
                    return null;
                }

                @Override
                public ItemInfoEstimateSingleDTO getItemInfoEstimate(ItemQueryListReq itemSingleDTO) {
                    log.error("查询item服务的商品详情 失败:{}", JacksonUtils.writeValueAsString(itemSingleDTO));
                    return null;
                }

                @Override
                public List<ItemInfoRespDTO> selectItemInfoList(ItemStringListDTO itemStringListDTO) {
                    log.error("查询商品详情失败:{}", JacksonUtils.writeValueAsString(itemStringListDTO));
                    return Collections.emptyList();
                }

                @Override
                public List<ItemInfoRespDTO> selectItems(ItemStringListDTO itemStringListDTO) {
                    log.error("查询商品详情失败:{}", JacksonUtils.writeValueAsString(itemStringListDTO));
                    return Collections.emptyList();
                }

                @Override
                public List<String> getItemInfoList3(ItemStringListDTO stringListDTO) {
                    log.error("查询获取商品信息失败" + throwable.getMessage());
                    throw new ServerException("查询获取商品信息失败" + throwable.getMessage());
                }

                @Override
                public List<ItemSynRespDTO> queryRecommendItem(ItemSingleDTO query) {
                    log.error(HYSTRIX_PATTERN, "queryRecommendItem", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
