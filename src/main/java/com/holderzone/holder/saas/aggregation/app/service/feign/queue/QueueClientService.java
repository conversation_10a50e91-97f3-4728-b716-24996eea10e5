package com.holderzone.holder.saas.aggregation.app.service.feign.queue;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.aggregation.app.service.feign.MessageClientService;
import com.holderzone.saas.store.dto.queue.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueController
 * @date 2019/03/27 16:59
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Api("排队 - 队列")
@Component
@FeignClient(name = "holder-saas-store-queue", fallbackFactory = QueueClientService.ServiceFallBack.class)
public interface QueueClientService {
    @GetMapping("/queue/list")
    @ApiOperation("list")
    StoreQueueDTO list();
    @PostMapping("/queue/removeAll")
    Boolean removeAll();
    @PostMapping("/queue/table")
    public List<QueueTableDTO> obtain(@RequestBody StoreGuidDTO queueGuidDTO);
    @PostMapping("/queue/table/obtain")
    @ApiOperation("查询队列对应桌台")
    public QueueTableDTO obtain(@RequestBody QueueGuidDTO queueGuidDTO);
    @Component
    class ServiceFallBack implements FallbackFactory<QueueClientService>{
        private static final Logger logger = LoggerFactory.getLogger(QueueClientService.class);
        @Override
        public QueueClientService create(Throwable throwable) {
            return new QueueClientService() {
                @Override
                public List<QueueTableDTO> obtain(StoreGuidDTO queueGuidDTO) {
                    logger.error("查询队列桌台信息异常 ，e={}", throwable);
                    throw new BusinessException("查询队列桌台信息异常!!" + throwable.getMessage());
                }

                @Override
                public StoreQueueDTO list() {
                    logger.error("查询排队信息异常 ，e={}", throwable);
                    throw new BusinessException("查询排队信息异常!!" + throwable.getMessage());
                }

                @Override
                public QueueTableDTO obtain(QueueGuidDTO queueGuidDTO) {
                    logger.error("查询队列对应桌台异常 ，e={}", throwable);
                    throw new BusinessException("查询队列对应桌台异常!!" + throwable.getMessage());
                }

                @Override
                public Boolean removeAll() {
                    logger.error("清除排队信息异常 ，e={}", throwable);
                    throw new BusinessException("清除排队信息异常!!" + throwable.getMessage());
                }
            };
        }
    }
}