package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.pay.*;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStorePayClientService
 * @date 2019/4/3
 */
@Component
@FeignClient(name = "holder-saas-store-pay", fallbackFactory = StorePayClientService.WxStorePayFallBack.class)
public interface StorePayClientService {

    @PostMapping("/agg/wechat/public/polling")
    AggPayPollingRespDTO pollingWeChatPublic(@RequestBody SaasPollingDTO saasPollingDTO);

    @PostMapping("agg/pay")
    AggPayRespDTO pay(SaasAggPayDTO saasAggPayDTO);

    @PostMapping("agg/h5/polling")
    AggPayPollingRespDTO h5Polling(@RequestBody SaasPollingDTO saasPollingDTO);

    @PostMapping("agg/wechat/public")
    String weChatPublic(SaasAggWeChatPublicAccountPayDTO publicAccountPayDTO);

    @PostMapping("agg/query")
    AggPayPollingRespDTO query(SaasPollingDTO saasPollingDTO);

    @Slf4j
    @Component
    class WxStorePayFallBack implements FallbackFactory<StorePayClientService> {

        @Override
        public StorePayClientService create(Throwable throwable) {
            return new StorePayClientService() {

                @Override
                public AggPayPollingRespDTO pollingWeChatPublic(SaasPollingDTO saasPollingDTO) {
                    log.error("远程调用信息失败，msg={}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public AggPayRespDTO pay(SaasAggPayDTO saasAggPayDTO) {
                    throw new BusinessException("调用交易中心异常");
                }

                @Override
                public AggPayPollingRespDTO h5Polling(SaasPollingDTO saasPollingDTO) {
                    log.error("远程调用信息失败，msg={}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public String weChatPublic(SaasAggWeChatPublicAccountPayDTO publicAccountPayDTO) {
                    throw new BusinessException("调用交易中心异常");
                }

                @Override
                public AggPayPollingRespDTO query(SaasPollingDTO saasPollingDTO) {
                    return AggPayPollingRespDTO.errorResp("10005", "查询交易中心异常");
                }
            };
        }
    }
}
