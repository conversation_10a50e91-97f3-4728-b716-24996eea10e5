package com.holderzone.holder.saas.aggregation.merchant.aop.item;

import com.holderzone.framework.log.busines.Platform;
import com.holderzone.saas.store.dto.item.common.ItemLogDTO;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import static com.holderzone.holder.saas.aggregation.merchant.constant.Constants.*;
import static com.holderzone.holder.saas.aggregation.merchant.util.CurrentHttpRequestUtils.getRequestUri;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ControllerAspect
 * @date 2018/10/16 上午10:56
 * @description //controller切面
 * @program holder-saas-aggregation-merchant
 */
@Aspect
@Component
@Order(2)
public class ItemControllerAspect {

    @Pointcut("execution(* com.holderzone.holder.saas.aggregation.merchant.controller.item.*.*(..))")
    public void pointCut() {
    }

    @Before("pointCut()")
    public void doBefore(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        if (args != null && args.length > 0) {
            for (Object arg : args) {
                if (arg instanceof ItemLogDTO){
                    ((ItemLogDTO)arg).setRequestUri(getRequestUri());
                    ((ItemLogDTO)arg).setPlatform(Platform.MERCHANTBACK);
                    if (Integer.valueOf(1).equals(((ItemLogDTO)arg).getFrom())){
                        ((ItemLogDTO)arg).setModule(BRAND_ITEM_MODEL);
                    }else if (Integer.valueOf(2).equals(((ItemLogDTO)arg).getFrom())){
                        ((ItemLogDTO)arg).setModule(STORE_ITEM_MODEL);
                    }
                }
            }
        }
    }
}
