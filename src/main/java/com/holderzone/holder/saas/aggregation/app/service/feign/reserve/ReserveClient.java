package com.holderzone.holder.saas.aggregation.app.service.feign.reserve;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.GatherRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(name = "holder-saas-store-reserve", fallbackFactory = ReserveClient.ServiceFallBack.class)
public interface ReserveClient {

    @PostMapping("/reserve/gather")
    List<GatherRespDTO> gather(DailyReqDTO dailyReqDTO);

    @ApiOperation("根据订单打印预付金信息")
    @PostMapping({"/reserve/printByOrderGuid"})
    void printByOrderGuid(@RequestBody SingleDataDTO singleDataDTO);

    @Component
    @Slf4j
    class ServiceFallBack implements FallbackFactory<ReserveClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ReserveClient create(Throwable throwable) {
            return new ReserveClient() {
                @Override
                public List<GatherRespDTO> gather(DailyReqDTO dailyReqDTO) {
                    log.error(HYSTRIX_PATTERN, "gather", JacksonUtils.writeValueAsString(dailyReqDTO),
                            ThrowableUtils.asString(throwable));
                    return null;
                }

                @Override
                public void printByOrderGuid(SingleDataDTO singleDataDTO) {
                    log.error(HYSTRIX_PATTERN, "printByOrderGuid", JacksonUtils.writeValueAsString(singleDataDTO),
                            ThrowableUtils.asString(throwable));
                }
            };
        }
    }
}
