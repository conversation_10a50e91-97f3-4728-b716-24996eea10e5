package com.holderzone.holder.saas.aggregation.merchant.controller.baseresource;


import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.baseresource.StoreClientService;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.holder.saas.member.dto.baseresource.QueryStoreDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 门店 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-16
 */
@RestController
@RequestMapping("/hsm-store")
@Api(description ="门店 controller" )
public class HsmStoreController {
    @Autowired
    private StoreClientService storeClientService;
    /**
     * @Description  查询门店信息
     * <AUTHOR>
     * @Date  2019/5/24 9:36
     * @return com.holderzone.framework.response.Result
     */
    @ApiOperation(value = "查询门店信息", notes = "查询门店信息")
    @GetMapping(value = "/queryOptionStoreInfo",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "查询门店信息")
    public Result queryOptionStoreInfo(QueryStoreDTO queryStoreDTO ){
        queryStoreDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        return Result.buildSuccessResult(storeClientService.queryOptionStoreInfo(queryStoreDTO));
    }
}
