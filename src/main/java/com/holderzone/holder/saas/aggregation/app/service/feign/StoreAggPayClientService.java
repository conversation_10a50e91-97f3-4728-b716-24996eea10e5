package com.holderzone.holder.saas.aggregation.app.service.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import com.holderzone.saas.store.dto.pay.*;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreAggPayClient
 * @date 2019/03/20 14:37
 * @description
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(value = "holder-saas-store-pay", fallbackFactory = StoreAggPayClientService.StoreAggPayClientFallBack.class)
public interface StoreAggPayClientService {

    @PostMapping("agg/pay")
    AggPayRespDTO pay(SaasAggPayDTO saasAggPayDTO);

    @PostMapping("agg/polling")
    AggPayPollingRespDTO polling(SaasPollingDTO saasPollingDTO);

    @PostMapping("agg/query")
    AggPayPollingRespDTO query(SaasPollingDTO saasPollingDTO);

    @PostMapping("agg/wechat/public/polling")
    AggPayPollingRespDTO pollingWeChatPublic(SaasPollingDTO saasPollingDTO);

    @PostMapping("agg/wechat/public/query")
    AggPayPollingRespDTO queryWeChatPublic(SaasPollingDTO saasPollingDTO);

    @PostMapping("agg/refund")
    AggRefundRespDTO refund(SaasAggRefundDTO saasAggRefundDTO);

    @PostMapping("agg/refund/polling")
    AggRefundPollingRespDTO refundPolling(SaasPollingDTO saasPollingDTO);

    @PostMapping("agg/record")
    Page<AggPayRecordDTO> queryPayRecord(BasePageDTO basePageDTO);

    @PostMapping("agg/record/statistics")
    AggPayStatisticsDTO queryPayStatistics(BasePageDTO basePageDTO);

    @PostMapping("agg/callback")
    String callback(AggPayCallbackDTO aggPayCallbackDTO);


    @Slf4j
    @Component
    class StoreAggPayClientFallBack implements FallbackFactory<StoreAggPayClientService> {
        @Override
        public StoreAggPayClientService create(Throwable throwable) {
            return new StoreAggPayClientService() {

                @Override
                public AggPayRespDTO pay(SaasAggPayDTO saasAggPayDTO) {
                    log.error("聚合支付预下单异常 e={}", throwable.getMessage());
                    return AggPayRespDTO.errorPaymentResp("10007", throwable.getMessage());
                }

                @Override
                public AggRefundRespDTO refund(SaasAggRefundDTO saasAggRefundDTO) {
                    log.error("聚合支付退款异常 e={}", throwable.getMessage());
                    return AggRefundRespDTO.errorPaymentResp("10007", throwable.getMessage());
                }

                @Override
                public AggRefundPollingRespDTO refundPolling(SaasPollingDTO saasPollingDTO) {
                    log.error("聚合支付轮询异常 e={}", throwable.getMessage());
                    return AggRefundPollingRespDTO.errorResp("10007", throwable.getMessage());
                }

                @Override
                public AggPayPollingRespDTO polling(SaasPollingDTO saasPollingDTO) {
                    log.error("聚合支付轮询异常 e={}", throwable.getMessage());
                    return AggPayPollingRespDTO.errorResp("10007", throwable.getMessage());
                }

                @Override
                public AggPayPollingRespDTO query(SaasPollingDTO saasPollingDTO) {
                    log.error("聚合支付轮询异常 e={}", throwable.getMessage());
                    return AggPayPollingRespDTO.errorResp("10007", throwable.getMessage());
                }

                @Override
                public AggPayPollingRespDTO pollingWeChatPublic(SaasPollingDTO saasPollingDTO) {
                    log.error("聚合支付轮询异常 e={}", throwable.getMessage());
                    return AggPayPollingRespDTO.errorResp("10007", throwable.getMessage());
                }

                @Override
                public AggPayPollingRespDTO queryWeChatPublic(SaasPollingDTO saasPollingDTO) {
                    log.error("聚合支付轮询异常 e={}", throwable.getMessage());
                    return AggPayPollingRespDTO.errorResp("10007", throwable.getMessage());
                }

                @Override
                public Page<AggPayRecordDTO> queryPayRecord(BasePageDTO basePageDTO) {
                    log.error("聚合支付查询支付记录异常 e={}", throwable.getMessage());
                    throw new ServerException();
                }
                @Override
                public AggPayStatisticsDTO queryPayStatistics(BasePageDTO basePageDTO) {
                    log.error("聚合支付查询支付记录异常 e={}", throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public String callback(AggPayCallbackDTO aggPayCallbackDTO) {
                    log.error("聚合支付回调异常 e={}", throwable.getMessage());
                    throw new ServerException();
                }
            };
        }
    }

}
