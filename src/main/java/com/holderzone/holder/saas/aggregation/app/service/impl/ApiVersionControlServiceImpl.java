package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.holder.saas.aggregation.app.anno.ApiVersionControl;
import com.holderzone.holder.saas.aggregation.app.service.ApiVersionControlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ApiVersionControlServiceImpl implements ApiVersionControlService {


    @Override
    @ApiVersionControl
    public void changeGroupItem() {
        log.info("套餐换菜版本控制");
    }
}
