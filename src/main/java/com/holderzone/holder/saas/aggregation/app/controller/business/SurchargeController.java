package com.holderzone.holder.saas.aggregation.app.controller.business;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.business.SurchargeClientService;
import com.holderzone.saas.store.dto.business.manage.SurchargeConditionQuery;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeAggDTO;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeSyncDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreConfigController
 * @date 2018/07/29 下午4:25
 * @description 附加费Controller
 * @program holder-saas-store-business-center
 */
@Slf4j
@RestController
@RequestMapping("/surcharge")
@Api(description = "附加费相关Api")
public class SurchargeController {

    private final SurchargeClientService surchargeClientService;

    @Autowired
    public SurchargeController(SurchargeClientService surchargeClientService) {
        this.surchargeClientService = surchargeClientService;
    }

    @PostMapping("/sync")
    @ApiOperation(value = "附加费用同步接口", notes = "附加费用同步接口")
    public Result<SurchargeAggDTO> sync(@RequestBody SurchargeSyncDTO surchargeDTO) {
        return Result.buildSuccessResult(surchargeClientService.sync(surchargeDTO));
    }

    @PostMapping("/list_by_condition")
    @ApiOperation(value = "根据条件查询收费规则")
    public Result<List<SurchargeLinkDTO>> listByCondition(@RequestBody SurchargeConditionQuery query) {
        log.info("[根据条件查询收费规则]入参,query={}", JacksonUtils.writeValueAsString(query));
        return Result.buildSuccessResult(surchargeClientService.listByCondition(query));
    }
}
