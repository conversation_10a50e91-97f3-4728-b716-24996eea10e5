package com.holderzone.holder.saas.aggregation.merchant.entity.enums;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Maps;
import com.holderzone.saas.store.dto.order.SettlementRulesDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import lombok.Getter;
import org.springframework.context.i18n.LocaleContextHolder;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023-07-19
 * @description
 */
@Getter
public enum SettlementConfigLocaleEnum {


    SETTLEMENT_COUPON_MEAL(1,"SETTLEMENT_COUPON_MEAL_NAME","SETTLEMENT_COUPON_MEAL_DESC"),
    SETTLEMENT_GIFT(2,"SETTLEMENT_GIFT_NAME","——"),
    SETTLEMENT_MERCHANDISE_COUPON(3,"SETTLEMENT_MERCHANDISE_COUPON_NAME","SETTLEMENT_MERCHANDISE_COUPON_DESC"),
    SETTLEMENT_MEMBERSHIP_PRICE(4,"SETTLEMENT_MEMBERSHIP_PRICE_NAME","——"),
    SETTLEMENT_CHANGE_PRICE(5,"SETTLEMENT_CHANGE_PRICE_NAME","SETTLEMENT_CHANGE_PRICE_DESC"),
    SETTLEMENT_ITEM_DISCOUNT(6,"SETTLEMENT_ITEM_DISCOUNT_NAME","——"),
    SETTLEMENT_NOT_COUPON_MEAL(7,"SETTLEMENT_NOT_COUPON_MEAL_NAME","SETTLEMENT_COUPON_MEAL_DESC"),
    SETTLEMENT_CASH_COUPON(8,"SETTLEMENT_CASH_COUPON_NAME","SETTLEMENT_CASH_COUPON_DESC"),
    SETTLEMENT_MEMBER_DISCOUNT(9,"SETTLEMENT_MEMBER_DISCOUNT_NAME","——"),
    SETTLEMENT_MAX_OUT(10,"SETTLEMENT_MAX_OUT_NAME","——"),
    SETTLEMENT_FULL_DISCOUNT(11,"SETTLEMENT_FULL_DISCOUNT_NAME","——"),
    SETTLEMENT_PACKAGE_DISCOUNT(12,"SOURCE_SNACK_DISCOUNT","——"),
    SETTLEMENT_POINT_DEDUCTION(13,"SETTLEMENT_POINT_DEDUCTION_NAME","——"),
    SETTLEMENT_SYSTEM_ZERO(14,"SETTLEMENT_SYSTEM_ZERO_NAME","——"),
    SETTLEMENT_PRICE_CONCESSION(15,"SOURCE_SNACK_ALLOWANCE","——"),
    SETTLEMENT_RED_ENVELOPE(16,"SETTLEMENT_RED_ENVELOPE_NAME","——"),
    ;


    private final String offerName;

    private final int number;

    private final String offerDesc;

    public static final String DEFAULT_DESC = "——";

    SettlementConfigLocaleEnum(int number,String offerName,String offerDesc){
        this.number = number;
        this.offerName = offerName;
        this.offerDesc = offerDesc;
    }

    private final static Map<Integer, SettlementConfigLocaleEnum> LOCALE_MAP;

    static {
        LOCALE_MAP = initMap();

    }

    private static Map<Integer, SettlementConfigLocaleEnum> initMap(){
        Map<Integer, SettlementConfigLocaleEnum> localeMap = Maps.newHashMap();
        for (SettlementConfigLocaleEnum localeEnum : SettlementConfigLocaleEnum.values()){
            localeMap.put(localeEnum.getNumber(),localeEnum);
        }
        return localeMap;
    }

    public static void transferLocale(List<SettlementRulesDTO> settlementRulesList){
        if(CollectionUtil.isEmpty(settlementRulesList)){
            return;
        }
        if(LocaleContextHolder.getLocale() == Locale.SIMPLIFIED_CHINESE){
            return;
        }
        settlementRulesList.forEach(s ->{
            SettlementConfigLocaleEnum localeEnum = LOCALE_MAP.get(s.getSequenceNumber());
            s.setOfferName(LocaleUtil.getMessage(localeEnum.getOfferName()));
            if(s.getOfferDesc().equals(DEFAULT_DESC)){
                return;
            }
            s.setOfferDesc(LocaleUtil.getMessage(localeEnum.getOfferDesc()));
        });
    }
}
