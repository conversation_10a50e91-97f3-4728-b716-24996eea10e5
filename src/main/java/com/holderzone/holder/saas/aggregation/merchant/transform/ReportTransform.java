package com.holderzone.holder.saas.aggregation.merchant.transform;


import com.holderzone.saas.store.dto.report.openapi.*;
import com.holderzone.saas.store.dto.report.openapi.shiyuanhui.SaleDetailShiYuanHuiRespDTO;
import com.holderzone.saas.store.dto.report.openapi.shiyuanhui.SalePayDetailShiYuanHuiRespDTO;
import com.holderzone.saas.store.dto.report.openapi.shiyuanhui.SaleProductDetailShiYuanHuiRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper
public interface ReportTransform {

    ReportTransform INSTANCE = Mappers.getMapper(ReportTransform.class);

    SaleDetailOpenRespDTO saleDetailRespDTO2SaleDetailOpenRespDTO(SaleDetailRespDTO saleDetailRespDTO);

    SalePayDetailOpenRespDTO salePayDetailRespDTO2SalePayDetailOpenRespDTO(SalePayDetailRespDTO salePayDetailRespDTO);

    SaleProductDetailOpenRespDTO saleProductDetailRespDTO2SaleProductDetailOpenRespDTO(SaleProductDetailRespDTO saleProductDetailRespDTO);

    List<SaleDetailOpenRespDTO> saleDetailRespDTO2SaleDetailOpenRespDTO(List<SaleDetailRespDTO> saleDetailRespDTO);

    List<SaleProductDetailOpenRespDTO> salePayDetailRespDTO2SaleProductDetailOpenRespDTO(List<SaleProductDetailRespDTO> saleProductDetailRespDTO);

    List<SalePayDetailOpenRespDTO> saleProductDetailRespDTO2SalePayDetailOpenRespDTO(List<SalePayDetailRespDTO> salePayDetailRespDTO);


    SaleDetailShiYuanHuiRespDTO saleDetailRespDTO2SaleDetailShiYuanHuiRespDTO(SaleDetailRespDTO saleDetailRespDTO);

    SalePayDetailShiYuanHuiRespDTO salePayDetailRespDTO2SalePayDetailShiYuanHuiRespDTO(SalePayDetailRespDTO salePayDetailRespDTO);

    SaleProductDetailShiYuanHuiRespDTO saleProductDetailRespDTO2SaleProductDetailShiYuanHuiRespDTO(SaleProductDetailRespDTO saleProductDetailRespDTO);

    List<SaleDetailShiYuanHuiRespDTO> saleDetailRespDTO2SaleDetailShiYuanHuiRespDTO(List<SaleDetailRespDTO> saleDetailRespDTO);

    List<SalePayDetailShiYuanHuiRespDTO> salePayDetailRespDTO2SalePayDetailShiYuanHuiRespDTO(List<SalePayDetailRespDTO> salePayDetailRespDTO);

    List<SaleProductDetailShiYuanHuiRespDTO> saleProductDetailRespDTO2SaleProductDetailShiYuanHuiRespDTO(List<SaleProductDetailRespDTO> saleProductDetailRespDTO);

}
