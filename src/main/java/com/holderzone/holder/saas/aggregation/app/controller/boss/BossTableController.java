package com.holderzone.holder.saas.aggregation.app.controller.boss;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.TableService;
import com.holderzone.saas.store.dto.boss.req.BossTableQueryDTO;
import com.holderzone.saas.store.dto.boss.resp.BossTableRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/21
 * @description
 */
@Slf4j
@RestController
@CrossOrigin
@Api(tags = "老板助手-桌台接口")
@RequestMapping("/boss/table")
public class BossTableController {

    private final TableService tableService;

    public BossTableController(TableService tableService) {
        this.tableService = tableService;
    }

    @ApiOperation(value = "桌台列表")
    @PostMapping("/list_table")
    public Result<List<BossTableRespDTO>> listTable(@RequestBody @Valid BossTableQueryDTO queryDTO) {
        log.info("[老板助手][桌台列表]入参,queryDTO={}", JacksonUtils.writeValueAsString(queryDTO));
        return Result.buildSuccessResult(tableService.listTable(queryDTO));
    }

}
