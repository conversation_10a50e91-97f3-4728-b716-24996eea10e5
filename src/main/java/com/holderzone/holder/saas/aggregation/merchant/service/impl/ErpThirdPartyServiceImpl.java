package com.holderzone.holder.saas.aggregation.merchant.service.impl;

import com.google.common.collect.Maps;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.merchant.entity.vo.EnterpriseDTO;
import com.holderzone.holder.saas.aggregation.merchant.service.ErpThirdPartyService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp.EnterpriseRpcService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.mdm.MdmServiceClient;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.user.UserFeignService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.weihai.WeihaiErpService;
import com.holderzone.holder.saas.aggregation.merchant.util.RedisCacheUtil;
import com.holderzone.resource.common.dto.enterprise.EnterpriseQueryDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseSupplyChainConfigDTO;
import com.holderzone.saas.store.dto.mdm.MDMResult;
import com.holderzone.saas.store.dto.user.UserDTO;
import com.holderzone.saas.store.dto.weihai.WeihaiAuthCodeReqDTO;
import com.holderzone.saas.store.dto.weihai.WeihaiCommonRespDTO;
import com.holderzone.saas.store.dto.weihai.WeihaiTokenReqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ErpThirdPartyServiceImpl implements ErpThirdPartyService {

    private final MdmServiceClient mdmServiceClient;
    private final EnterpriseRpcService enterpriseRpcService;
    private final WeihaiErpService weihaiErpService;
    private final RedisCacheUtil redisCacheUtil;
    private final UserFeignService userFeignService;

    private static final String ACCESS_TOKEN_CACHE_KEY = "weihai:erp:access_token:%s";
    private static final String AUTH_CODE_CACHE_KEY = "weihai:erp:auth_code:%s:%s";

    private static final String ALGORITHM_STR = "AES/ECB/PKCS5Padding";
    private static final long ACCESS_TOKEN_EXPIRE_TIME = 110;
    private static final long AUTH_CODE_EXPIRE_TIME = 300;

    @Value("${weihai.erp.sso-login-url}")
    private String weihaiLoginUrl;

    @Override
    public String getLoginUrl() {
        // 查询当期企业信息
        EnterpriseQueryDTO enterpriseQueryDTO = new EnterpriseQueryDTO();
        enterpriseQueryDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        EnterpriseDTO enterprise = enterpriseRpcService.findEnterprise(enterpriseQueryDTO);
        log.info("当前企业信息, enterprise:{}", JacksonUtils.writeValueAsString(enterprise));
        Boolean supportWeiHaiSupplyChain = enterprise.getSupportWeiHaiSupplyChain();
        if (!Boolean.TRUE.equals(supportWeiHaiSupplyChain)) {
            return Strings.EMPTY;
        }
        EnterpriseSupplyChainConfigDTO supplyChainConfig = enterprise.getSupplyChainConfig();
        if (Objects.isNull(supplyChainConfig)) {
            log.error("微海供应链未配置,enterpriseGuid:{}", UserContextUtils.getEnterpriseGuid());
            throw new BusinessException("微海供应链未配置");
        }
        // 查询当前用户的微海id
        String userGuid = UserContextUtils.getUserGuid();
        MDMResult<UserDTO> userResp = mdmServiceClient.findUser(userGuid);
        log.warn("查询mdm用户返回:{}", userResp);
        if (Objects.isNull(userResp.getData())) {
            log.warn("当前用户未同步到mdm, userGuid:{}", userGuid);
            userFeignService.updateTime(userGuid);
            throw new BusinessException("当前用户未同步到微海供应链，请稍后再试！");
        }
        String weihaiId = userResp.getData().getWeihaiId();
        if (StringUtils.isEmpty(weihaiId)) {
            log.warn("当前用户未同步到微海, userGuid:{}", userGuid);
            userFeignService.updateTime(userGuid);
            throw new BusinessException("当前用户未同步到微海供应链，请稍后再试！");
        }
        String accessToken = getAccessToken(supplyChainConfig);
        if (StringUtils.isEmpty(accessToken)) {
            throw new BusinessException("获取微海accessToken失败");
        }
        String authCode = getAuthCode(supplyChainConfig, accessToken, weihaiId);
        return buildWeihaiLoginUrl(supplyChainConfig, authCode);
    }

    private String buildWeihaiLoginUrl(EnterpriseSupplyChainConfigDTO supplyChainConfig, String authCode) {
        long timestamp = System.currentTimeMillis();
        String corpId = supplyChainConfig.getWeihaiCorpId();
        String secret = supplyChainConfig.getWeihaiSecret();
        String weihaiAesKey = supplyChainConfig.getWeihaiAesKey();

        // 加密authCode
        authCode = aesAuthCode(authCode, weihaiAesKey);

        Map<String, Object> map = Maps.newHashMap();
        map.put("corpId", corpId);
        map.put("authCode", authCode);
        map.put("timestamp", timestamp);

        // 有序字段map
        TreeMap<String, Object> filteredSortedMap = sortMap(map);
        // 参数拼接
        String joinedParam = filteredSortedMap.entrySet().stream()
                .map(entry -> String.format("%s=%s", entry.getKey(), entry.getValue()))
                .collect(Collectors.joining("&"));
        log.info("待签名字符串： {}", joinedParam);
        String joinedParamWithKey = joinedParam + secret;
        String sign = DigestUtils.md5Hex(joinedParamWithKey.getBytes());

        String finalUrl = String.format(weihaiLoginUrl, corpId, authCode, timestamp, sign);
        log.info("免密登录链接:{}", finalUrl);
        return finalUrl;
    }

    private String aesAuthCode(String authCode, String aesKey) {
        try {
            KeyGenerator keygen = KeyGenerator.getInstance("AES");
            keygen.init(128, new SecureRandom("AES".getBytes()));
            SecretKey key = new SecretKeySpec(aesKey.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance(ALGORITHM_STR);
            cipher.init(Cipher.ENCRYPT_MODE, key);
            byte[] byteEncode = authCode.getBytes(StandardCharsets.UTF_8);
            byte[] byteAes = cipher.doFinal(byteEncode);
            return Hex.encodeHexString(byteAes);
        } catch (Exception e) {
            log.error("aes authCode fail ,e:", e);
            return authCode;
        }
    }

    /**
     * 获取AccessToken，优先从Redis缓存获取，不存在则调用接口获取并缓存
     */
    private String getAccessToken(EnterpriseSupplyChainConfigDTO supplyChainConfig) {
        String cacheKey = String.format(ACCESS_TOKEN_CACHE_KEY, supplyChainConfig.getWeihaiCorpId());
        return redisCacheUtil.getOrSet(cacheKey,
                () -> {
                    WeihaiTokenReqDTO tokenReqDTO = new WeihaiTokenReqDTO();
                    tokenReqDTO.setAgent(supplyChainConfig.getWeihaiAgent());
                    tokenReqDTO.setCorpId(supplyChainConfig.getWeihaiCorpId());
                    tokenReqDTO.setSecret(supplyChainConfig.getWeihaiSecret());
                    WeihaiCommonRespDTO<String> accessTokenResp = weihaiErpService.getAccessToken(tokenReqDTO);
                    log.info("微海getAccessToken返回:{}", JacksonUtils.writeValueAsString(accessTokenResp));
                    return accessTokenResp.getResult();
                },
                ACCESS_TOKEN_EXPIRE_TIME,
                TimeUnit.MINUTES);
    }

    /**
     * 获取AuthCode，优先从Redis缓存获取，不存在则调用接口获取并缓存
     */
    private String getAuthCode(EnterpriseSupplyChainConfigDTO supplyChainConfig,
                               String accessToken, String weihaiId) {
        WeihaiAuthCodeReqDTO authCodeReqDTO = new WeihaiAuthCodeReqDTO();
        authCodeReqDTO.setCorpId(supplyChainConfig.getWeihaiCorpId());
        authCodeReqDTO.setUserId(weihaiId);
        WeihaiCommonRespDTO<String> authCodeResp = weihaiErpService.getAuthCode(accessToken, authCodeReqDTO);
        log.info("微海getAuthCode返回:{}", JacksonUtils.writeValueAsString(authCodeResp));
        return authCodeResp.getResult();
    }

    private TreeMap<String, Object> sortMap(Map<String, Object> map) {
        return map.entrySet().stream()
                .filter(stringObjectEntry -> validate(stringObjectEntry.getValue()))
                .sorted(Map.Entry.comparingByKey())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                        (u, v) -> {
                            throw new IllegalStateException(String.format("Duplicate key %s", u));
                        }, TreeMap::new)
                );
    }

    private boolean validate(Object object) {
        return object != null && (!(object instanceof String) || StringUtils.hasText((String) object));
    }
}
