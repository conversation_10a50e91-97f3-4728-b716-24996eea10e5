package com.holderzone.holder.saas.aggregation.merchant.controller.item;

import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.item.ItemClientService;
import com.holderzone.saas.store.dto.item.req.BatchImportGetItemsReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemExcelTemplateReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemBatchImportTempRespDTO;
import com.holderzone.saas.store.enums.item.ItemTypeEnum;
import com.holderzone.saas.store.util.LocaleUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemHelper
 * @date 2019/07/30 14:46
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Slf4j
@Component
public class ItemHelper {

    @Autowired
    private ItemClientService itemClientService;

    //新版本 批量导入验证 flag  0:门店  1：品牌   type  0：餐饮版  1：零售版
    public List<ItemBatchImportTempRespDTO>
    itemBatchVerify(List<ItemExcelTemplateReqDTO> itemExcelTemplateReqDTOList, String storeGuid, Integer flag, Integer type) {
        List<ItemBatchImportTempRespDTO> result = new ArrayList<>();
        //拿到excel里面的商品名称 排除名称为空的商品
        List<String> itemNames = itemExcelTemplateReqDTOList.stream().filter(a -> Objects.nonNull(a.getItemName())).map(ItemExcelTemplateReqDTO::getItemName).collect(Collectors.toList());
        //获取到已存在的商品
        List<ItemBatchImportTempRespDTO> existsItmes = itemClientService.getItemsBeforeImport(BatchImportGetItemsReqDTO
                .builder()
                .itemNames(itemNames)
                .flag(flag)
                .guid(storeGuid).build());
        //获取到门店已存在的商品名称集合
        List<String> existsItemNames = existsItmes.stream().map(ItemBatchImportTempRespDTO::getItemName).collect(Collectors.toList());
        List<String> existsItemCodes = existsItmes.stream().map(ItemBatchImportTempRespDTO::getCode).collect(Collectors.toList());
        List<String> existsItemUpcs = existsItmes.stream().map(ItemBatchImportTempRespDTO::getUpc).collect(Collectors.toList());
        //根据 itemName 分组看是否重复  map  的 value 大于1的存在重复  排除名称为空的商品
        Map<String, List<ItemExcelTemplateReqDTO>> collect = itemExcelTemplateReqDTOList.stream().filter(a -> Objects.nonNull(a.getItemName())).collect(Collectors.groupingBy(ItemExcelTemplateReqDTO::getItemName));
        Map<String, List<ItemExcelTemplateReqDTO>> collectCode = itemExcelTemplateReqDTOList.stream().filter(a -> Objects.nonNull(a.getCode())).collect(Collectors.groupingBy(ItemExcelTemplateReqDTO::getCode));
        Map<String, List<ItemExcelTemplateReqDTO>> collectUpc = itemExcelTemplateReqDTOList.stream().filter(a -> Objects.nonNull(a.getUpc())).collect(Collectors.groupingBy(ItemExcelTemplateReqDTO::getUpc));
        itemExcelTemplateReqDTOList.forEach(s -> {
            String errorMsg = "";
            //先判断商品名称是否为空
            if (Objects.isNull(s.getItemName())) {
                errorMsg = errorMsg.concat(errorMsgLabel(errorMsg,LocaleUtil.getMessage("EXPORT_PRODUCT_NAME_REQUIRED")));
            }
            String tagMsg = type.equals(0) ? LocaleUtil.getMessage("EXPORT_NORMAL_WEIGHING") : LocaleUtil.getMessage("EXPORT_COUNTING_WEIGHING");
            //在判断商品是否重复
            if (Objects.nonNull(s.getItemName())) {
                if (CollectionUtils.isNotEmpty(existsItemNames) && existsItemNames.contains(s.getItemName())) {
                    errorMsg = errorMsg.concat(errorMsgLabel(errorMsg,LocaleUtil.getMessage("EXPORT_PRODUCT_ALREADY_EXISTS_LIBRARY")));
                } else if (collect.get(s.getItemName()).size() > 1) {
                    errorMsg = errorMsg.concat(errorMsgLabel(errorMsg, LocaleUtil.getMessage("EXPORT_DUPLICATE_PRODUCT")));
                }
            }

            if ((Objects.nonNull(s.getItemName()) && s.getItemName().length() < 1) || (Objects.nonNull(s.getItemName()) && s.getItemName().length() > 40)) {
                errorMsg = errorMsg.concat(errorMsgLabel(errorMsg,LocaleUtil.getMessage("EXPORT_INVALID_PRODUCT_NAME")));
            }
            if (Objects.isNull(s.getTypeName()) || s.getTypeName().length() < 1 || s.getTypeName().length() > 20) {
                errorMsg = errorMsg.concat(errorMsgLabel(errorMsg,LocaleUtil.getMessage("EXPORT_CATEGORY_NAME_REQUIRED")));
            }

            if (Objects.isNull(s.getItemType()) || Optional.ofNullable(s.getItemType()).isPresent() && (!"4".equals(s.getItemType().trim()) && !"3".equals(s.getItemType().trim()))) {

                errorMsg = errorMsg.concat(errorMsgLabel(errorMsg,String.format(LocaleUtil.getMessage("EXPORT_INCORRECT_PRODUCT"),tagMsg)));
            }


            if (Objects.nonNull(s.getItemType()) && "3".equals(s.getItemType().trim()) && Objects.nonNull(s.getUnit())) {
                if ("kg".equalsIgnoreCase(s.getUnit().trim())) {
                    s.setUnit("kg");
                } else if("斤".equals(s.getUnit().trim())) {
                    s.setUnit("斤");
                } else {
                    errorMsg = errorMsg.concat(errorMsgLabel(errorMsg,LocaleUtil.getMessage("EXPORT_TYPE_DOES_NOT_MATCH")));
                }
            }
            if (Objects.isNull(s.getSalePrice())) {
                errorMsg = errorMsg.concat(errorMsgLabel(errorMsg,LocaleUtil.getMessage("EXPORT_SALES_PRICE_REQUIRED")));
            } else {
                BigDecimal minSalePrice = new BigDecimal("0.01");
                BigDecimal maxSalePrice = new BigDecimal("99999.99");
                if(type.equals(1)) {
                    minSalePrice = BigDecimal.ZERO;
                }
                if (s.getSalePrice().scale() > 2) {
                    errorMsg = errorMsg.concat(errorMsgLabel(errorMsg,LocaleUtil.getMessage("EXPORT_SALES_PRICE_TWO_DECIMAL_PLACES")));
                } else if (s.getSalePrice().compareTo(minSalePrice) < 0 || s.getSalePrice().compareTo(maxSalePrice) > 0) {
                    errorMsg = errorMsg.concat(errorMsgLabel(errorMsg,LocaleUtil.getMessage("EXPORT_USE_ENGLISH_INPUT"))+minSalePrice+"-"+maxSalePrice);
                }
            }
            if(Objects.nonNull(s.getCostPrice())) {
                if (s.getCostPrice().scale() > 2) {
                    errorMsg = errorMsg.concat(errorMsgLabel(errorMsg,LocaleUtil.getMessage("EXPORT_COST_PRICE_PLACES")));
                }else if (s.getCostPrice().compareTo(BigDecimal.ZERO) < 0 || s.getSalePrice().compareTo(new BigDecimal(99999.99)) > 0) {
                    errorMsg = errorMsg.concat(errorMsgLabel(errorMsg,LocaleUtil.getMessage("EXPORT_COST_PRICE_RANGE")));
                }
            }
            if(type == 0 ) { //todo  餐饮版判断
                if(Objects.nonNull(s.getMemberPrice())) {
                    if (s.getMemberPrice().scale() > 2) {
                        errorMsg = errorMsg.concat(StringUtils.isEmpty(errorMsg) ? "会员价最多保留两位小数" : "$会员价最多保留两位小数");
                    }else if (s.getMemberPrice().compareTo(BigDecimal.ZERO) < 0 || s.getSalePrice().compareTo(new BigDecimal(99999.99)) > 0) {
                        errorMsg = errorMsg.concat(StringUtils.isEmpty(errorMsg) ? "会员价；范围0-99990.99" : "$会员价；范围0-99990.99");
                    }
                }
                if(Objects.nonNull(s.getMemberPrice()) && Objects.nonNull(s.getSalePrice()) && s.getSalePrice().compareTo(s.getMemberPrice()) == -1) {
                    errorMsg = errorMsg.concat(StringUtils.isEmpty(errorMsg) ? "会员价不得高于销售价" : "$会员价不得高于销售价");
                }
                if (Objects.isNull(s.getUnit()) || s.getUnit().length() < 1 || s.getUnit().length() > 10) {
                    errorMsg = errorMsg.concat(StringUtils.isEmpty(errorMsg) ? "单位，必填，1-10字" : "$单位，必填，1-10字");
                }
                // SKU简码 餐饮：1-16位数字  不可重复
                if (Objects.nonNull(s.getCode())) {
                    // 称重商品sku简码为1-5
                    if (String.valueOf(ItemTypeEnum.WEIGH.getCode()).equals(s.getItemType())) {
                        String regex = "^[0-9]{1,5}$";
                        if (!s.getCode().matches(regex)) {
                            log.warn("SKU简码格式错误");
                            errorMsg = errorMsg.concat("称重商品SKU简码请输入1-5位数字");
                        }
                    } else {
                        String regex = "^[0-9]{1,16}$";
                        if (!s.getCode().matches(regex)) {
                            log.warn("SKU简码格式错误");
                            errorMsg = errorMsg.concat("SKU简码请输入1-16位数字");
                        }
                    }
                    if (CollectionUtils.isNotEmpty(existsItemCodes) && existsItemCodes.contains(s.getCode())) {
                        errorMsg = errorMsg.concat(StringUtils.isEmpty(errorMsg) ? "商品库该SKU简码已存在" : "$商品库该SKU简码已存在");
                    } else if (collectCode.get(s.getCode()).size() > 1) {
                        errorMsg = errorMsg.concat(StringUtils.isEmpty(errorMsg) ? "excelSKU简码已重复" : "$excelSKU简码已重复");
                    }
                }
            }
            if(type == 1) {  //todo  零售版判断
                //在判断商品code是否重复 零售
                if (Objects.nonNull(s.getCode())) {
                    if (CollectionUtils.isNotEmpty(existsItemCodes) && existsItemCodes.contains(s.getCode())) {
                        errorMsg = errorMsg.concat(StringUtils.isEmpty(errorMsg) ? "商品库该货号已存在" : "$商品库该货号已存在");
                    } else if (collectCode.get(s.getCode()).size() > 1) {
                        errorMsg = errorMsg.concat(StringUtils.isEmpty(errorMsg) ? "excel货号已重复" : "$excel货号已重复");
                    }
                }
                //在判断商品是否重复
                if (Objects.nonNull(s.getUpc())) {
                    if (CollectionUtils.isNotEmpty(existsItemUpcs) && existsItemUpcs.contains(s.getUpc())) {
                        errorMsg = errorMsg.concat(StringUtils.isEmpty(errorMsg) ? "商品库该商品条码已存在" : "$商品库该商品条码已存在");
                    } else if (collectUpc.get(s.getUpc()).size() > 1) {
                        errorMsg = errorMsg.concat(StringUtils.isEmpty(errorMsg) ? "excel商品条码已重复" : "$excel商品条码已重复");
                    }
                }
                if (Objects.isNull(s.getUnit()) || s.getUnit().length() < 1 || s.getUnit().length() > 5) {
                    errorMsg = errorMsg.concat(StringUtils.isEmpty(errorMsg) ? "单位，必填，1-5字" : "$单位，必填，1-5字");
                }
            }
                result.add(type.equals(0) ? ItemBatchImportTempRespDTO
                        .builder()
                        .itemName(s.getItemName())
                        .itemType(s.getItemType())
                        .salePrice(s.getSalePrice())
                        .typeName(s.getTypeName())
                        .costPrice(s.getCostPrice())
                        .code(s.getCode())
                        .isWholeDiscount(s.getIsWholeDiscount())
                        .memberPrice(s.getMemberPrice())
                        .isRack(s.getIsRack())
                        .isJoinWechat(s.getIsJoinWechat())
                        .isJoinBuffet(s.getIsJoinBuffet())
                        .flag(errorMsg.trim().length() > 0 ? 1 : 0)
                        .errorMsg(errorMsg)
                        .unit(s.getUnit())
                        .minOrderNum(s.getMinOrderNum())
                        .pictureUrl(s.getPictureUrl())
                        .build():
                                ItemBatchImportTempRespDTO
                                        .builder()
                                        .itemName(s.getItemName())
                                        .itemType(s.getItemType())
                                        .salePrice(s.getSalePrice())
                                        .typeName(s.getTypeName())
                                        .flag(errorMsg.trim().length() > 0 ? 1 : 0)
                                        .errorMsg(errorMsg)
                                        .unit(s.getUnit())
                                        .minOrderNum(s.getMinOrderNum())
                                        .safeStock(s.getSafeStock())
                                        .totalStock(s.getTotalStock())
                                        .costPrice(s.getCostPrice())
                                        .isWholeDiscount(s.getIsWholeDiscount())
                                        .code(s.getCode())
                                        .upc(s.getUpc())
                                        .isOpenStock(s.getIsOpenStock())
                                        .build()
                        );

        });
        if (Objects.nonNull(result)) {
            result.sort((ItemBatchImportTempRespDTO c1, ItemBatchImportTempRespDTO c2) -> c2.getFlag().compareTo(c1.getFlag()));
        }
        return result;
    }

    //商品优化导入数据转换  type  0：餐饮版  1：零售版
    public List<ItemExcelTemplateReqDTO> itemBatchImportConvert(List<ItemExcelTemplateReqDTO> itemExcelTemplateReqDTOS,Integer type) {
        itemExcelTemplateReqDTOS.forEach(o -> {
            o.setItemType(type.equals(0) ? o.getItemType().equals("4") ? "普通" : "称重" : o.getItemType().equals("4") ? "计数" : "计重");
        });
        return itemExcelTemplateReqDTOS;
    }

    private static String errorMsgLabel(String errorMsg,String localeMessage){
        return StringUtils.isEmpty(errorMsg) ? localeMessage : Constants.EXPORT_LABEL + localeMessage;
    }
}
