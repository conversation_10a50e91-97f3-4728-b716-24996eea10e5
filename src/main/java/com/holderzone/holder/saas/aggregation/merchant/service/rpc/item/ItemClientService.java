package com.holderzone.holder.saas.aggregation.merchant.service.rpc.item;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.aggregation.merchant.mapper.EstimateItemResidueMemchantRespFixDTO;
import com.holderzone.holder.saas.aggregation.merchant.mapper.EstimateMerchantConfigRespFixDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemDoubleParamDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.common.PrintItemTypeDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.req.price.*;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.item.resp.price.ItemSkuAndPlanPriceDTO;
import com.holderzone.saas.store.dto.item.resp.price.PlanPriceAllItemSkuRespDTO;
import com.holderzone.saas.store.dto.item.resp.price.PlanPriceAllTypeRespDTO;
import com.holderzone.saas.store.dto.item.resp.price.PlanPriceEditDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.takeaway.ErpMappingType;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemClientService
 * @date 2018/09/07 下午4:29
 * @description //TODO
 * @program holder-saas-store-item
 */
@Component
@FeignClient(name = "holder-saas-store-item", fallbackFactory = ItemClientService.ItemFallBack.class)
public interface ItemClientService {


    /**
     * Content type 'text/plain;charset=UTF-8' not supported
     * 批量导入之前根据flag获取商品集合
     *
     * @param batchImportGetItemsReqDTO
     * @return
     */
    @PostMapping("/item/get_items_before_import")
    List<ItemBatchImportTempRespDTO> getItemsBeforeImport(@RequestBody BatchImportGetItemsReqDTO batchImportGetItemsReqDTO);

    /**
     * 保存和更新估清商品
     *
     * @param estimateReqDTO
     * @return 保存更新行数
     */
    @PostMapping("/estimate/save")
    Integer saveOrUpdateEstimate(@RequestBody EstimateReqDTO estimateReqDTO);


    /**
     * 更新属性组
     *
     * @param attrGroupUpdateReqDTO attrGroupUpdateReqDTO
     * @return Integer
     */
    @PostMapping("/attr/set_attr_group")
    boolean setAttrGroup(@RequestBody AttrGroupUpdateReqDTO attrGroupUpdateReqDTO);

    /**
     * 新增属性组
     *
     * @param attrGroupReqDTO attrGroupReqDTO
     * @return boolean
     */
    @PostMapping("/attr/save_attr_group")
    boolean saveAttrGroup(@RequestBody AttrGroupReqDTO attrGroupReqDTO);

    /**
     * 新增属性值
     *
     * @param attrReqDTO attrReqDTO
     * @return boolean
     */
    @PostMapping("/attr/save_attr")
    boolean saveAttrValue(@RequestBody AttrReqDTO attrReqDTO);

    @PostMapping("/attr/list_attr_group")
    List<AttrGroupAttrRespDTO> listAttrGroup(@RequestBody ItemSingleDTO itemSingleDTO);

    @PostMapping("/item/code")
    String getCode(@RequestBody ItemSingleDTO dataDTO);

    @PostMapping("/type/save")
    Integer save(@RequestBody TypeReqDTO typeReqDTO);

    @PostMapping("/type/quick_save")
    Integer quickSave(@RequestBody TypeReqDTO typeReqDTO);

    @PostMapping("/type/query_type")
    List<TypeWebRespDTO> queryType(@RequestBody ItemSingleDTO itemSingleDTO);

    @PostMapping("/type/update")
    Integer update(@RequestBody TypeReqDTO typeReqDTO);

    @PostMapping("/type/delete")
    Integer delete(@RequestBody ItemSingleDTO itemSingleDTO);

    /**
     * 正餐获取分类排序接口
     *
     * @param itemSingleDTO
     * @return
     */
    @PostMapping("/type/get_sort")
    Integer getSort(@RequestBody ItemSingleDTO itemSingleDTO);

    /**
     * 属性值列表
     *
     * @param itemSingleDTO 属性组guid
     * @return 属性值list
     */
    @PostMapping("/attr/list_attr_by_group")
    List<AttrRespDTO> listAttrByGroup(@RequestBody ItemSingleDTO itemSingleDTO);

    @PostMapping("/item/save_item")
    String saveItem(@RequestBody ItemReqDTO itemSaveReqDTO);

    @PostMapping("/item/update_item")
    Integer updateItem(@RequestBody ItemReqDTO itemUpdateReqDTO);

    /**
     * 更新商品pad图片
     *
     * @param padPictureDTO 属性值保存DTO
     * @return 操作结果
     */
    @PostMapping("/item/editPadPicture")
    Integer editPadPicture(@RequestBody PadPictureDTO padPictureDTO);

    @PostMapping("/item/delete_item")
    Integer deleteItem(@RequestBody ItemSingleDTO itemSingleDTO);

    @PostMapping("/item/select_item_for_web")
    Page<ItemWebRespDTO> selectItemForWeb(@RequestBody ItemQueryReqDTO itemQueryReqDTO);

    @PostMapping("/item/queryPadPicture")
    Page<PadPictureRespDTO> queryPadPicture(@RequestBody PadPictureDTO itemQueryReqDTO);

    @PostMapping("/retail/item/select_item_for_web")
    Page<ItemWebRespDTO> selectRetailItemForWeb(@RequestBody ItemQueryReqDTO itemQueryReqDTO);

    @PostMapping("/item/get_export_item_list")
    List<ItemExportRespDTO> getExportItemList(@RequestBody ItemExportReqDTO itemExportReqDTO);

    @PostMapping("/item/get_item_info")
    ItemInfoRespDTO getItemInfo(@RequestBody ItemSingleDTO itemSingleDTO);

    @PostMapping("/item_pkg/select_sku_list")
    List<TypeSkuRespDTO> selectSkuListForPkg(@RequestBody ItemSingleDTO itemSingleDTO);

    /**
     * 外卖批量绑定查询品牌库商品
     */
    @PostMapping("/item_pkg/select_list")
    List<TypeSkuRespDTO> selectList(@RequestBody ItemSingleDTO itemSingleDTO);

    @PostMapping("/item_pkg/save_pkg")
    Integer savePkg(@RequestBody ItemPkgSaveReqDTO itemPkgSaveReqDTO);

    @PostMapping("/item_pkg/update_pkg")
    Integer updatePkg(@RequestBody ItemPkgSaveReqDTO itemPkgSaveReqDTO);

    /**
     * 新增套餐时的属性列表
     *
     * @param itemSingleDTO 门店guid or 品牌guid
     * @return 属性组list（包括属性值）
     */
    @PostMapping("/attr/list_attr_for_save_item")
    List<AttrGroupAttrRespDTO> listAttrForSaveItem(@RequestBody ItemSingleDTO itemSingleDTO);

    @PostMapping("/item/parent_sku")
    List<SkuInfoRespDTO> findParentSKUS(@RequestBody List<String> skuGuids);

    /**
     * 根据门店当前模式查询规格商品信息
     */
    @PostMapping("/item/list_sku_info/by_mode")
    List<SkuInfoRespDTO> listSkuInfoByRecipeMode(@RequestBody ItemStringListDTO itemStringListDTO);

    /**
     * 导出商品数据
     *
     * @param itemSingleDTO 门店guid
     * @return list
     */
    @PostMapping("/item/download_data")
    List<ItemExcelTemplateRespDTO> getDownloadItemData(@RequestBody ItemSingleDTO itemSingleDTO);

    /**
     * 查询当前品牌是否有绑定的商品分类或商品
     *
     * @param brandGuid 当前品牌GUID
     * @return true:count>0,即品牌下有商品分类或商品;false:count=0，即品牌下无商品分类或商品
     */
    @PostMapping("/item/count_type_or_item")
    boolean countTypeOrItem(@RequestParam("brandGuid") String brandGuid);

    /**
     * 更新菜品排序
     *
     * @param itemSortUpdateReqDTO itemSortUpdateReqDTO
     * @return boolean
     */
    @PostMapping("/item/update_item_sort")
    boolean updateItemSort(ItemSortUpdateReqDTO itemSortUpdateReqDTO);

    @PostMapping("/item/push_item")
    Integer pushItem(@RequestBody PushItemReqDTO pushItemReqDTO);

    /**
     * 修改属性值
     *
     * @param attrSaveReqDTO attrSaveReqDTO
     * @return boolean
     */
    @PostMapping("/attr/update_attr")
    boolean updateAttrValue(@RequestBody AttrSaveReqDTO attrSaveReqDTO);

    /**
     * 删除属性值
     *
     * @param itemSingleDTO 属性值guid
     * @return boolean
     */
    @PostMapping("/attr/delete_attr")
    boolean deleteAttrByGuid(@RequestBody ItemSingleDTO itemSingleDTO);

    /**
     * 删除属性组
     *
     * @param itemSingleDTO 属性组guid
     * @return boolean
     */
    @PostMapping("/attr/delete_attr_group")
    boolean deleteAttrGroupByGuid(ItemSingleDTO itemSingleDTO);

    /**
     * 批量导入商品
     *
     * @param itemDoubleParamDTO 第一个是data，第二个是storeGuid or brandGuid
     * @return boolean
     */
    @PostMapping("/item/batch_import")
    int batchImportItem(ItemDoubleParamDTO<List<ItemExcelTemplateReqDTO>, String> itemDoubleParamDTO);

    @PostMapping("/item/rack_item")
    Integer rackItem(@RequestBody ItemRackDTO itemRackDTO);

    @PostMapping("/item/batch_delete")
    Integer batchDelete(@RequestBody ItemStringListDTO itemStringListDTO);

    @PostMapping("/item/mapping")
    List<MappingRespDTO> mapping(@RequestBody ItemSingleDTO itemSingleDTO);

    @PostMapping("/type/query_type_by_stores")
    List<TypeWebRespDTO> queryTypeByStoreGuidList(@RequestBody ItemStringListDTO itemStringListDTO);

    @PostMapping("erp/list_type_of_store")
    /**获取分类列表*/
    List<ErpTypeDTO> listTypeStore(@RequestBody SingleDataDTO singleDataDTO);

    @PostMapping("/erp/page_item_of_type")
    /**获取分类下商品分页列表*/
    Page<ErpItemDTO> listItemStore(@RequestBody SingleDataPageDTO erpTypeItemQueryDTO);

    @PostMapping("/erp/page_item_of_store")
    /**获取门店下商品分页列表*/
    Page<ErpItemDTO> listAllItemStore(@RequestBody SingleDataPageDTO erpTypeItemQueryDTO);

    @PostMapping("/erp/page_item_by_name")
    /**根据名称获取门店下商品分页列表*/
    Page<ErpItemDTO> listItemByName(@RequestBody DoubleDataPageDTO doubleDataPageDTO);

    @PostMapping("/estimate/get_estimate_list_store")
    /**根据条件获取估清商品分页列表*/
    Page<EstimateMerchantConfigRespFixDTO> selectItemEstimates(EstimateMerchantReqDTO request);

    @PostMapping("/estimate/get_estimate_item_residue_store")
    /**根据条件获取估清商品剩余可售数量分页列表*/
    Page<EstimateItemResidueMemchantRespFixDTO> selectEstimateItemResidueStore(EstimateMerchantReqDTO request);

    @PostMapping("/item_template/get_store_item_templates")
    /**根据条件获取门店商品销售模板分页列表*/
    ItemTemplatesRespDTO selectStoreItemTemplates(ItemTemplateSearchReqDTO request);

    @PostMapping("/item_template/save")
    /**更新和保存和逻辑删除门店销售模板*/
    Integer save(ItemTemplateReqDTO request);

    @PostMapping("/item_template/get_item_template_menus")
    /**获取销售模板下菜单列表*/
    List<ItemTemplateMenusRespDTO> selectItemTemplateMenus(SingleDataDTO request);

    @PostMapping("/item_template/save_menu")
    /**新增销售模板*/
    Integer saveItemMenu(ItemTemplateMenuSubitemReqDTO request);

    @PostMapping("/item/get_sku_item_list")
    /**按分类、名称获取sku商品列表*/
    Page<ItemTemplateSubItemRespDTO> selectSkuItemList(ItemTemplateMenuAllSubItemReqDTO request);

    @PostMapping("/item_template/get_item_template_menu_detail")
    /**按guid查询模板菜单详情*/
    ItemTemplateMenuDetailRespDTO selectItemTemplateMenuDetail(ItemTemplateMenuDetailsReqDTO request);

    @PostMapping("/item_template/batch_remove")
    /**（批量）移除菜单下菜品*/
    Integer menuSubItemBatchRemove(SingleDataDTO request);

    @PostMapping("/retail/item/get_item_sort_list")
    /**商超排序分类获取商品列表接口*/
    Page<ItemSortRespDTO> selectItemSortList(ItemQueryReqDTO request);

    @PostMapping("/retail/item/retail_update_item_sort")
    /**商超商品更新排序*/
    int retailUpdateItemSort(ItemRetailSortReqDTO request);

    /**
     * 判断商品类型下面是否存在商品
     */
    @PostMapping("/type/verify_item_exists_for_type")
    Integer verifyThatitemExistsForType(SingleDataDTO request);

    /**
     * 商超获取分类排序和分类下商品集合
     */
    @PostMapping("/retail/item/get_sort_type_and_item")
    ItemRetailSortRespDTO getSortTypeAndItems(ItemSingleDTO request);

    /**
     * 获取团餐详情
     */
    @PostMapping("/item/select_group_meal_detail")
    GroupMealItemDetailRespDTO selectGroupMealDetail(SingleDataDTO request);

    /**
     * 保存/更新团餐
     */
    @PostMapping("/item_pkg/save_group_meal_pkg")
    Integer saveOrUpdateGroupMealPkg(ItemGroupMealSaveReqDTO request);

    /**
     * 查询当前套餐功能是否开启
     */
    @PostMapping("/type/select_group_meal_status")
    String selectGroupMealStatus(SingleDataDTO data);

    /**
     * 查询当前套餐功能是否开启
     */
    @PostMapping(value = "/type/set_group_meal_status")
    String setGroupMealStatus(SingleDataDTO data);

    /**
     * 商品批量整单折扣修改
     */
    @PostMapping("/item/whole_discount_item")
    Integer wholeDiscountItem(ItemWholeDiscountDTO itemWholeDiscountDTO);

    /**
     * 价格方案保存
     *
     * @param reqDTO PricePlanReqDTO
     * @return 价格方案guid
     */
    @PostMapping("/plan/save")
    String savePlan(PricePlanReqDTO reqDTO);

    /**
     * 价格方案列表查询
     *
     * @param reqDTO PricePlanPageReqDTO
     * @return Page<PricePlanRespDTO>
     */
    @PostMapping("/plan/list")
    Page<PricePlanRespDTO> planList(PricePlanPageReqDTO reqDTO);

    /**
     * 价格方案编辑查询
     *
     * @param planGuid 方案guid
     * @return PricePlanRespDTO
     */
    @GetMapping("/plan/getOne")
    PricePlanRespDTO getPlan(@RequestParam("planGuid") String planGuid);

    /**
     * 价格方案商品导入查询可导入列表
     *
     * @param request ItemTemplateMenuAllSubItemReqDTO
     * @return Page<PricePlanItemAddQueryRespDTO>
     */
    @PostMapping("/plan/get_brand_item_list")
    Page<PricePlanItemAddQueryRespDTO> selectBrandSkuItemList(PricePlanItemAddQueryReqDTO request);

    /**
     * 价格方案商品保存（导入）
     *
     * @param reqDTO PricePlanItemAddReqDTO
     * @return 结果
     */
    @PostMapping("/plan/saveItem")
    Boolean savePlanItem(PricePlanItemAddReqDTO reqDTO);

    /**
     * 价格方案关联商品列表查询
     *
     * @param reqDTO PricePlanItemPageReqDTO
     * @return Page<PricePlanItemPageRespDTO>
     */
    @PostMapping("/plan/itemList")
    Page<PricePlanItemPageRespDTO> itemList(PricePlanItemPageReqDTO reqDTO);

    /**
     * 价格方案移除关联商品
     *
     * @param reqDTO PricePlanItemRemoveReqDTO
     * @return 结果
     */
    @PostMapping("/plan/removeItems")
    Boolean removeItems(PricePlanItemRemoveReqDTO reqDTO);

    /**
     * 价格方案更新关联商品
     *
     * @param reqDTO PricePlanItemUpdateReqDTO
     * @return 结果
     */
    @PostMapping("/plan/updateItems")
    Boolean updateItems(@RequestBody PricePlanItemUpdateReqDTO reqDTO);

    /**
     * 价格方案推送
     *
     * @param reqDTO PricePlanPushReqDTO
     * @return 结果
     */
    @PostMapping("/plan/push")
    Boolean pushPlan(PricePlanPushReqDTO reqDTO);

    /**
     * 价格方案删除
     *
     * @param planGuid 方案guid
     * @return 结果
     */
    @GetMapping("/plan/delete")
    Boolean deletePlan(@RequestParam("planGuid") String planGuid);

    /**
     * 永久停用方案
     *
     * @param planGuid 方案id
     * @return Boolean
     */
    @ApiOperation(value = "永久停用方案")
    @GetMapping("/plan/permanently_deactivate")
    Boolean permanentlyDeactivate(@RequestParam("planGuid") String planGuid);

    /**
     * 门店控制列表
     *
     * @param reqDTO
     * @return
     */
    @PostMapping("/plan/store_control_list")
    List<PricePlanStoreRespDTO> storeControlList(PricePlanStoreReqDTO reqDTO);

    /**
     * 绑定门店与方案关系
     *
     * @param reqDTO
     * @return
     */
    @PostMapping("/plan/store_plan_bind")
    Boolean bingPlanStore(PricePlanStoreReqDTO reqDTO);

    @ApiOperation(value = "查询价格方案分类列表", notes = "查询品牌/门店分类列表")
    @GetMapping("/plan/query_type_by_price_plan_guid")
    List<TypeWebRespDTO> queryTypeByPricePlanGuid(@RequestParam("pricePlanGuid") String pricePlanGuid);

    /**
     * 获取品牌下所有商品及分类
     *
     * @param itemSingleDTO
     * @return
     */
    @PostMapping("/item/query_item_by_brand_list")
    List<TypeItemListDTO> queryStoreByBrandList(ItemSingleDTO itemSingleDTO);

    /**
     * 批量修改分类顺序
     *
     * @param typeReqDTOList 里面只有sort和typeGuid
     * @return 1
     */
    @ApiOperation(value = "批量修改分类顺序")
    @PostMapping("/type/batch_modify_sort")
    Integer batchModifySort(@RequestBody List<TypeReqDTO> typeReqDTOList);

    /**
     * 批量移动商品:选中一个分类下的商品，批量移动到其它分类下
     *
     * @param typeSortReqDTO 商品guidList，目标分类guid
     * @return boolean
     */
    @ApiOperation(value = "批量移动商品")
    @PostMapping("/item/batch_move_item")
    Boolean batchMoveItem(@RequestBody TypeSortReqDTO typeSortReqDTO);

    @ApiOperation(value = "根据门店查询已下架和已推送的商品")
    @PostMapping("/item/query_filter_item")
    List<String> queryFilterItem(@RequestBody ItemSingleDTO itemSingleDTO);

    @ApiOperation(value = "门店绑定菜谱方案校验")
    @PostMapping("/plan/check_store_price_plan_rule")
    List<PricePlanStoreCheckRespDTO> checkStorePricePlanRule(PricePlanReqDTO reqDTO);

    @ApiOperation(value = "商品批量设置上下架")
    @PostMapping("/plan/batch_operating_item")
    Boolean batchOperatingItem(@RequestBody PricePlanItemReqDTO reqDTO);

    @ApiOperation(value = "菜谱方案草稿删除")
    @PostMapping("/plan/delete_plan_draft")
    Boolean deletePlanDraft(@RequestBody PricePlanDraftReqDTO reqDTO);

    @ApiOperation(value = "价格方案草稿查询")
    @GetMapping("/plan/get_price_plan_cache_data")
    PricePlanReqDTO getPricePlanCacheData(@RequestParam("userGuid") String userGuid,
                                          @RequestParam("brandGuid") String brandGuid);

    /**
     * 通过门店guid查询所有菜谱方案
     *
     * @param storeGuid 门店guid
     * @return 所有菜谱方案
     */
    @ApiOperation(value = "通过门店guid查询所有菜谱方案")
    @GetMapping("/plan/query_plans_by_store_guid")
    List<PricePlanRespDTO> queryPlansByStoreGuid(@RequestParam("storeGuid") String storeGuid);


    @ApiOperation(value = "校验价格方案分类是否可以删除", notes = "校验价格方案分类是否可以删除")
    @PostMapping("/plan/check_type_price_plan")
    Boolean checkTypePricePlan(@RequestBody ItemQueryReqDTO itemQueryReqDTO);

    @ApiOperation(value = "菜谱方案可导入菜品查询")
    @PostMapping("/plan/find_price_plan_item_list")
    List<TypeItemRespDTO> findPricePlanItemList(@RequestBody ItemSingleDTO itemSingleDTO);

    @ApiOperation(value = "erp商品售罄回调 售完下架")
    @PostMapping("/item/erp/callback")
    Boolean sellOutRackItem(ItemErpReqDto itemErpReqDto);

    /**
     * 根据菜谱方案查询菜谱方案绑定菜品
     *
     * @param itemQueryReqDTO ItemQueryReqDTO
     * @return 商品列表
     */
    @ApiOperation(value = "根据菜谱方案查询菜谱方案绑定菜品")
    @PostMapping("/plan/query_plan_items_by_plan")
    Page<ItemWebRespDTO> queryPlanItemsByPlan(@RequestBody ItemQueryReqDTO itemQueryReqDTO);

    /**
     * 根据方案guid查询绑定门店信息
     *
     * @param itemSingleDTO 菜谱方案Guid
     * @return List<StoreDTO>
     */
    @ApiOperation(value = "根据方案guid查询绑定门店信息")
    @PostMapping("/plan/query_plan_store_list_by_plan")
    List<StoreDTO> queryPlanStoreListByPlan(@RequestBody ItemSingleDTO itemSingleDTO);

    /**
     * 通过门店guid查询所属菜谱方案
     *
     * @param storeGuid 门店guid
     * @return 所属菜谱方案，如果没有则返回空list
     */
    @ApiOperation(value = "通过门店guid查询所属菜谱方案")
    @GetMapping("/plan/query_belong_plan_by_store_guid")
    List<PricePlanRespDTO> queryBelongPlansByStoreGuid(@RequestParam("storeGuid") String storeGuid);

    /**
     * 根据门店GUID查询所有商品详情已经分类信息（区分销售模式）
     *
     * @param itemSingleDTO storeGuid
     * @return 分类和商品详情
     */
    @ApiOperation(value = "根据门店GUID查询所有商品详情已经分类信息（区分销售模式）")
    @PostMapping("/item/query_store_item_By_sales_model")
    ItemInfoAndTypeRespDTO queryStoreItemBySalesModel(@RequestBody ItemSingleDTO itemSingleDTO);

    /**
     * 根据门店guidList查询所有商品并过滤
     * 只返回传入的商品GUID对应的商品的详情
     *
     * @param itemStringListDTO 门店guid，商品guid
     * @return 过滤后的商品信息
     */
    @ApiOperation(value = "根据门店guidList查询所有商品并过滤")
    @PostMapping("/item/query_store_item_and_filter")
    ItemInfoAndTypeRespDTO queryStoreItemAndFilter(@RequestBody ItemStringListDTO itemStringListDTO);

    /**
     * 通过菜谱guid预览菜谱
     *
     * @param reqDTO 菜谱guid,浏览模式
     * @return 预览的菜谱方案信息
     */
    @ApiOperation(value = "通过菜谱guid预览菜谱")
    @PostMapping("/plan/preview_plan_by_guid")
    PreviewPlanRespDTO previewPlansByGuid(@RequestBody PreviewPlanReqDTO reqDTO);

    /**
     * 二维码过期校验
     *
     * @param reqDTO 二维码id和方案id
     * @return 该二维码是否过期，过期时间为3天
     */
    @ApiOperation(value = "二维码过期校验，过期时间为3天")
    @PostMapping("/plan/check_qr_code")
    CheckQrCodeRespDTO checkQrCode(@RequestBody CheckQrCodeDTO reqDTO);

    @ApiOperation(value = "菜谱复制")
    @GetMapping("/plan/copy_plan")
    Boolean copyPlan(@RequestParam("planGuid") String planGuid);

    /**
     * 根据门店guid查询已上架的商品
     * 区分普通模式和菜谱模式
     *
     * @param itemQueryListReq 门店guid
     * @return 已上架的商品
     */
    @PostMapping("/item/query_for_synchronize")
    @ApiOperation(value = "查询门店全部商品", notes = "查询门店全部商品")
    ItemAndTypeForAndroidRespDTO selectItemAndTypeForSyn(@RequestBody ItemQueryListReq itemQueryListReq);

    /**
     * 批量编辑方案商品的商品列表
     * v1.18.5 版本优化：取消分页，增加分类
     * 查询所有为已启用、即将启用、暂不启用菜谱的所有商品（去重）
     *
     * @param req 请求参数
     * @return 商品列表
     */
    @ApiOperation(value = "查询所有为已启用、即将启用、暂不启用菜谱的所有商品（去重）")
    @PostMapping("/plan/list_all_plan_price_item")
    List<PlanPriceAllTypeRespDTO> listAllPlanPriceItem(@RequestBody @Validated PlanPriceAllItemReqDTO req);

    /**
     * 批量编辑方案分类的分类列表
     * 查询所有为已启用、即将启用、暂不启用菜谱的所有分类 按名称去重
     */
    @ApiOperation(value = "查询所有为已启用、即将启用、暂不启用菜谱的所有分类 按名称去重")
    @PostMapping("/plan/list_all_plan_price_item_type")
    List<TypeRespDTO> listAllPlanPriceItemType(@RequestBody @Validated PlanPriceAllItemReqDTO req);

    /**
     * 查询分类名称所存在的菜谱
     * 查询所有为已启用、即将启用、暂不启用菜谱 按名称去重
     */
    @ApiOperation(value = "批量编辑方案分类的分类列表 查询分类名称所存在的菜谱")
    @GetMapping("/plan/list_item_type_and_plan_price")
    List<PlanPriceEditDTO> listItemTypeAndPlanPrice(@RequestParam("typeName") String typeName);

    /**
     * 批量编辑保存菜谱分类信息
     */
    @ApiOperation(value = "批量编辑保存菜谱分类信息")
    @PostMapping("/plan/save_batch_edit_plan_price_type")
    void saveBatchEditPlanPriceType(@RequestBody PlanPriceEditReqDTO req);

    @GetMapping("/plan/list_item_sku_and_plan_price")
    ItemSkuAndPlanPriceDTO listItemSkuAndPlanPrice(@RequestParam("itemGuid") String itemGuid);

    @PostMapping("/plan/save_batch_edit_plan_price")
    void saveBatchEditPlanPrice(@RequestBody PlanPriceEditReqDTO req);

    @PostMapping("/plan/list_available_plan_price")
    List<PlanPriceEditDTO> listAvailablePlanPrice(@RequestBody PlanPriceAvailableReqDTO req);

    @PostMapping("/plan/save_batch_add_plan_price")
    void saveBatchAddPlanPrice(PlanPriceAddReqDTO req);

    @GetMapping("/plan/list_available_plan_item_type")
    List<ItemTypeRespDTO> listAvailablePlanItemType(@RequestParam("planGuid") String planGuid);

    /**
     * 批量下架的商品列表
     * 查询所有为已启用、即将启用、暂不启用菜谱的所有商品规格（去重）
     *
     * @param req 品牌必传
     * @return 商品列表
     */
    @ApiOperation(value = "批量下架商品列表")
    @PostMapping("/plan/list_all_plan_price_item_sku")
    Page<PlanPriceAllItemSkuRespDTO> listAllPlanPriceItemSku(@RequestBody @Validated PlanPriceAllItemSkuReqDTO req);

    /**
     * 批量下架保存商品菜谱信息
     *
     * @param request 批量下架保存入参实体
     */
    @ApiOperation(value = "批量下架保存商品菜谱信息")
    @PostMapping("/plan/save_batch_sold_out_plan_item")
    Boolean saveBatchSoldOutPlanItem(@RequestBody PlanPriceSoldOutReqDTO request);

    /**
     * 查询外卖绑定的erp商品
     * 菜谱和普通模式都查询
     *
     * @param itemSingleDTO 门店guid
     * @return erp商品
     */
    @ApiOperation(value = "查询外卖绑定的erp商品")
    @PostMapping("/item/query_erp_item")
    List<ErpMappingType> queryErpItem(@RequestBody ItemSingleDTO itemSingleDTO);

    /**
     * 查询所有门店外卖绑定的erp商品
     *
     * @param itemStringListDTO 门店guid列表
     * @return 所有门店外卖绑定的erp商品
     */
    @ApiOperation(value = "查询外卖绑定的erp商品")
    @PostMapping("/item/query_erp_item/by_stores")
    List<StoreItemListRespDTO> queryErpItemByStoreGuids(@RequestBody ItemStringListDTO itemStringListDTO);

    /**
     * 获取品牌下所有分类及商品
     *
     * @param itemSingleDTO 品牌guid，关键字
     * @return 分类及商品
     */
    @ApiOperation(value = "获取品牌下所有分类及商品")
    @PostMapping("/item/list_type_and_item_by_brand")
    List<TypeItemRespDTO> listTypeAndItemByBrand(@RequestBody @Valid ItemSingleDTO itemSingleDTO);

    /**
     * 获取门店分类以及指定商品
     *
     * @param itemTypeDTO guid
     * @return 分类以及指定商品
     */
    @ApiOperation(value = "获取品牌下所有分类及商品")
    @PostMapping("/item/select_print_item_type")
    public PrintSortRespDTO selectPrintItemType(@RequestBody PrintItemTypeDTO itemTypeDTO);

    @ApiOperation(value = "查询品牌列表下的所有分类")
    @PostMapping("/type/query_type_by_brand")
    List<TypeWebRespDTO> queryTypeByBrand(@RequestBody ItemStringListDTO query);

    /**
     * 查询品牌下所有商品
     */
    @ApiOperation(value = "查询品牌下所有商品")
    @PostMapping("/item/query_item_by_brand")
    Page<ItemWebRespDTO> queryItemByBrand(@RequestBody ItemQueryReqDTO queryReqDTO);

    /**
     * 根据商品id查询商品信息
     */
    @ApiOperation(value = "根据商品id查询商品信息")
    @PostMapping("/item/query_item_by_guid")
    List<ItemWebRespDTO> queryItemByGuid(@RequestBody ItemStringListDTO query);

    /**
     * 查询推荐商品
     */
    @ApiOperation(value = "查询推荐商品")
    @PostMapping("/item/query_recommend_item")
    List<ItemSynRespDTO> queryRecommendItem(@RequestBody ItemSingleDTO query);

    @Component
    @Slf4j
    class ItemFallBack implements FallbackFactory<ItemClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ItemClientService create(Throwable throwable) {
            return new ItemClientService() {

                @Override
                public List<ItemBatchImportTempRespDTO> getItemsBeforeImport(BatchImportGetItemsReqDTO batchImportGetItemsReqDTO) {
                    log.error("批量导入商品之前获取商品库商品异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Integer saveOrUpdateEstimate(EstimateReqDTO estimateReqDTO) {
                    log.error("设置sku估清异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public boolean setAttrGroup(AttrGroupUpdateReqDTO attrGroupUpdateReqDTO) {
                    log.error("设置属性组异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public boolean saveAttrGroup(AttrGroupReqDTO attrGroupReqDTO) {
                    log.error("新增属性组异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public boolean saveAttrValue(AttrReqDTO attrReqDTO) {
                    log.error("新增属性异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<AttrGroupAttrRespDTO> listAttrGroup(ItemSingleDTO itemSingleDTO) {
                    log.error("获取属性组和属性异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public String getCode(ItemSingleDTO dataDTO) {
                    log.error("获取编码异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Integer save(TypeReqDTO typeReqDTO) {
                    log.error("新增分类异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Integer quickSave(TypeReqDTO typeReqDTO) {
                    log.error("快速新建分类异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<TypeWebRespDTO> queryType(ItemSingleDTO itemSingleDTO) {
                    log.error("获取分类列表数据异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Integer update(TypeReqDTO typeReqDTO) {
                    log.error("更新分类异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Integer delete(ItemSingleDTO itemSingleDTO) {
                    log.error("删除分类数据异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Integer getSort(ItemSingleDTO itemSingleDTO) {
                    log.error("获取分类的排序异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<AttrRespDTO> listAttrByGroup(ItemSingleDTO itemSingleDTO) {
                    log.error("获取指定属性组的属性异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public String saveItem(ItemReqDTO itemSaveReqDTO) {
                    log.error("新增商品异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Integer updateItem(ItemReqDTO itemUpdateReqDTO) {
                    log.error("更新商品数据异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Integer editPadPicture(PadPictureDTO padPictureDTO) {
                    log.error("更新pad图片异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Integer deleteItem(ItemSingleDTO itemSingleDTO) {
                    log.error("删除商品数据异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Page<ItemWebRespDTO> selectItemForWeb(ItemQueryReqDTO itemQueryReqDTO) {
                    log.error("网页端商品数据查询异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Page<PadPictureRespDTO> queryPadPicture(PadPictureDTO itemQueryReqDTO) {
                    log.error("pad图片查询异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }


                @Override
                public Page<ItemWebRespDTO> selectRetailItemForWeb(ItemQueryReqDTO itemQueryReqDTO) {
                    log.error("网页端零售商品数据查询异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<ItemExportRespDTO> getExportItemList(ItemExportReqDTO itemExportReqDTO) {
                    log.error("网页端零售商品数据查询异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public ItemInfoRespDTO getItemInfo(ItemSingleDTO itemSingleDTO) {
                    log.error("获取商品详情数据异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<TypeSkuRespDTO> selectSkuListForPkg(ItemSingleDTO itemSingleDTO) {
                    log.error("获取套餐子商品的规格集合数据异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<TypeSkuRespDTO> selectList(ItemSingleDTO itemSingleDTO) {
                    log.error(HYSTRIX_PATTERN, "selectList", JacksonUtils.writeValueAsString(itemSingleDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Integer savePkg(ItemPkgSaveReqDTO itemPkgSaveReqDTO) {
                    log.error("新增套餐异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Integer updatePkg(ItemPkgSaveReqDTO itemPkgSaveReqDTO) {
                    log.error("修改套餐异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<AttrGroupAttrRespDTO> listAttrForSaveItem(ItemSingleDTO itemSingleDTO) {
                    log.error("新增商品时获取属性组及属性异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<SkuInfoRespDTO> findParentSKUS(List<String> skuGuids) {
                    log.error("获取商品skuGuid父级id异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<SkuInfoRespDTO> listSkuInfoByRecipeMode(ItemStringListDTO itemStringListDTO) {
                    log.error("根据门店当前模式查询规格商品信息异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<ItemExcelTemplateRespDTO> getDownloadItemData(ItemSingleDTO itemSingleDTO) {
                    log.error("获取下载商品数据异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public boolean countTypeOrItem(String brandGuid) {
                    log.error("查询品牌下的分类或商品操作异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public boolean updateItemSort(ItemSortUpdateReqDTO itemSortUpdateReqDTO) {
                    log.error("更新菜品排序操作异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Integer pushItem(PushItemReqDTO pushItemReqDTO) {
                    log.error("推送商品操作异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public boolean updateAttrValue(AttrSaveReqDTO attrSaveReqDTO) {
                    log.error("修改属性值异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getCause());
                }

                @Override
                public boolean deleteAttrByGuid(ItemSingleDTO itemSingleDTO) {
                    log.error("删除属性值异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public boolean deleteAttrGroupByGuid(ItemSingleDTO itemSingleDTO) {
                    log.error("删除属性组异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public int batchImportItem(ItemDoubleParamDTO<List<ItemExcelTemplateReqDTO>, String> itemDoubleParamDTO) {
                    log.error("批量导入商品异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Integer rackItem(ItemRackDTO itemRackDTO) {
                    log.error("上下架商品异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Integer batchDelete(ItemStringListDTO itemStringListDTO) {
                    log.error("批量删除商品异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<MappingRespDTO> mapping(ItemSingleDTO itemSingleDTO) {
                    log.error("美团获取商品映射接口异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<TypeWebRespDTO> queryTypeByStoreGuidList(ItemStringListDTO itemStringListDTO) {
                    log.error("根据门店集合查询分类接口异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<ErpTypeDTO> listTypeStore(SingleDataDTO erpTypeItemQueryDTO) {
                    log.error("ERP查询item接口异常：{}", throwable.getMessage());
                    throw new RuntimeException("ERP查询item接口异常，msg={}" + throwable.getMessage());
                }

                @Override
                public Page<ErpItemDTO> listItemStore(SingleDataPageDTO erpTypeItemQueryDTO) {
                    log.error("ERP查询item接口异常：{}", throwable.getMessage());
                    throw new RuntimeException("ERP查询item接口异常，msg={}" + throwable.getMessage());
                }

                @Override
                public Page<ErpItemDTO> listAllItemStore(SingleDataPageDTO erpTypeItemQueryDTO) {
                    log.error("ERP查询item接口异常：{}", throwable.getMessage());
                    return null;
                }

                @Override
                public Page<ErpItemDTO> listItemByName(DoubleDataPageDTO doubleDataPageDTO) {
                    log.error("ERP查询item接口异常：{}", throwable.getMessage());
                    return null;
                }

                @Override
                public Page<EstimateMerchantConfigRespFixDTO> selectItemEstimates(EstimateMerchantReqDTO request) {
                    log.error("查询门店sku估清列表异常：{}", throwable.getMessage());
                    throw new RuntimeException("查询门店sku估清列表异常，msg={}" + throwable.getMessage());
                }

                @Override
                public Page<EstimateItemResidueMemchantRespFixDTO> selectEstimateItemResidueStore(EstimateMerchantReqDTO request) {
                    log.error("查询门店sku估清剩余可售数量列表异常：{}", throwable.getMessage());
                    throw new RuntimeException("查询门店sku估清剩余可售数量列表异常，msg={}" + throwable.getMessage());
                }

                @Override
                public ItemTemplatesRespDTO selectStoreItemTemplates(ItemTemplateSearchReqDTO request) {
                    log.error("查询门店商品销售模板列表异常：{}", throwable.getMessage());
                    throw new RuntimeException("查询门店商品销售模板列表异常，msg={}" + throwable.getMessage());
                }

                @Override
                public Integer save(ItemTemplateReqDTO request) {
                    log.error("保存/更新门店商品模板异常：{}", throwable.getMessage());
                    throw new RuntimeException("保存/更新门店商品模板异常异常，msg={}" + throwable.getMessage());
                }

                @Override
                public List<ItemTemplateMenusRespDTO> selectItemTemplateMenus(SingleDataDTO request) {
                    log.error("查询模板菜单列表接口异常：{}", throwable.getMessage());
                    throw new RuntimeException("查询模板菜单列表接口异常，msg={}" + throwable.getMessage());
                }

                @Override
                public Integer saveItemMenu(ItemTemplateMenuSubitemReqDTO request) {
                    log.error("新增销售模板异常：{}", throwable.getMessage());
                    throw new RuntimeException(" 新增销售模板异常，msg={}" + throwable.getMessage());
                }

                @Override
                public Page<ItemTemplateSubItemRespDTO> selectSkuItemList(ItemTemplateMenuAllSubItemReqDTO request) {
                    log.error(" 按分类、名称获取sku商品列表 异常：{}", throwable.getMessage());
                    throw new RuntimeException("  按分类、名称获取sku商品列表 异常，msg={}" + throwable.getMessage());
                }

                @Override
                public ItemTemplateMenuDetailRespDTO selectItemTemplateMenuDetail(ItemTemplateMenuDetailsReqDTO request) {
                    log.error(" 获取商品模板菜单详情接口 异常：{}", throwable.getMessage());
                    throw new RuntimeException("获取商品模板菜单详情接口 异常，msg={}" + throwable.getMessage());
                }

                @Override
                public Integer menuSubItemBatchRemove(SingleDataDTO request) {
                    log.error("（批量）移除菜单下菜品接口 异常：{}", throwable.getMessage());
                    throw new RuntimeException("（批量）移除菜单下菜品接口 异常，msg={}" + throwable.getMessage());
                }

                @Override
                public Page<ItemSortRespDTO> selectItemSortList(ItemQueryReqDTO request) {
                    log.error("（商超排序分类获取商品集合接口 异常：{}", throwable.getMessage());
                    throw new RuntimeException("（商超排序分类获取商品集合接口 异常，msg={}" + throwable.getMessage());
                }

                @Override
                public int retailUpdateItemSort(ItemRetailSortReqDTO request) {
                    log.error("（商超商品更新排序接口 异常：{}", throwable.getMessage());
                    throw new RuntimeException("（商超商品更新排序接口 异常，msg={}" + throwable.getMessage());
                }

                @Override
                public Integer verifyThatitemExistsForType(SingleDataDTO request) {
                    log.error("（判断商品类型下面是否存在商品接口 异常：{}", throwable.getMessage());
                    throw new RuntimeException("（判断商品类型下面是否存在商品接口 异常，msg={}" + throwable.getMessage());
                }

                @Override
                public ItemRetailSortRespDTO getSortTypeAndItems(ItemSingleDTO request) {
                    log.error("（商超获取分类排序和分类下商品集合 异常：{}", throwable.getMessage());
                    throw new RuntimeException("（商超获取分类排序和分类下商品集合 异常，msg={}" + throwable.getMessage());
                }

                @Override
                public GroupMealItemDetailRespDTO selectGroupMealDetail(SingleDataDTO request) {
                    log.error("获取团餐详情 异常：{}", throwable.getMessage());
                    throw new RuntimeException("（获取团餐详情 异常，msg={}" + throwable.getMessage());
                }

                @Override
                public Integer saveOrUpdateGroupMealPkg(ItemGroupMealSaveReqDTO request) {
                    log.error("保存/更新团餐 异常：{}", throwable.getMessage());
                    throw new RuntimeException("（保存/更新团餐 异常，msg={}" + throwable.getMessage());
                }

                @Override
                public String selectGroupMealStatus(SingleDataDTO data) {
                    log.error("查询当前套餐功能是否开启 异常：{}", throwable.getMessage());
                    throw new RuntimeException("（查询当前套餐功能是否开启 异常，msg={}" + throwable.getMessage());
                }

                @Override
                public String setGroupMealStatus(SingleDataDTO data) {
                    log.error("修改团餐功能当前状态 异常：{}", throwable.getMessage());
                    throw new RuntimeException("（修改团餐功能当前状态 异常，msg={}" + throwable.getMessage());
                }

                @Override
                public Integer wholeDiscountItem(ItemWholeDiscountDTO itemWholeDiscountDTO) {
                    log.error("商品批量整单折扣修改 异常：{}", throwable.getMessage());
                    throw new RuntimeException("（商品批量整单折扣修改 异常，msg={}" + throwable.getMessage());
                }

                @Override
                public String savePlan(PricePlanReqDTO reqDTO) {
                    log.error("价格方案保存异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Page<PricePlanRespDTO> planList(PricePlanPageReqDTO reqDTO) {
                    log.error("价格方案列表查询异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public PricePlanRespDTO getPlan(String planGuid) {
                    log.error("价格方案编辑查询异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Page<PricePlanItemAddQueryRespDTO> selectBrandSkuItemList(PricePlanItemAddQueryReqDTO request) {
                    log.error("价格方案可导入商品列表查询异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean savePlanItem(PricePlanItemAddReqDTO reqDTO) {
                    log.error("价格方案商品保存异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Page<PricePlanItemPageRespDTO> itemList(PricePlanItemPageReqDTO reqDTO) {
                    log.error("价格方案商品列表查询异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean removeItems(PricePlanItemRemoveReqDTO reqDTO) {
                    log.error("价格方案商品移除异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean updateItems(PricePlanItemUpdateReqDTO reqDTO) {
                    log.error("价格方案商品更新异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean pushPlan(PricePlanPushReqDTO reqDTO) {
                    log.error("价格方案推送异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean deletePlan(String planGuid) {
                    log.error("价格方案删除异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean permanentlyDeactivate(String planGuid) {
                    log.error(HYSTRIX_PATTERN, "permanentlyDeactivate", planGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PricePlanStoreRespDTO> storeControlList(PricePlanStoreReqDTO reqDTO) {
                    log.error("门店控制列表 异常：{}", throwable.getMessage());
                    throw new RuntimeException("（门店控制列表 异常，msg={}" + throwable.getMessage());
                }

                @Override
                public Boolean bingPlanStore(PricePlanStoreReqDTO reqDTO) {
                    log.error("绑定门店与方案关系 异常：{}", throwable.getMessage());
                    throw new RuntimeException("（绑定门店与方案关系 异常，msg={}" + throwable.getMessage());
                }

                @Override
                public List<TypeWebRespDTO> queryTypeByPricePlanGuid(String pricePlanGuid) {
                    log.error("根据价格方案查询商品类型异常：{}", throwable.getMessage());
                    return new ArrayList<>();
                }

                @Override
                public List<TypeItemListDTO> queryStoreByBrandList(ItemSingleDTO itemSingleDTO) {
                    log.error("获取品牌下所有商品及分类异常：{}", throwable.getMessage());
                    return new ArrayList<>();
                }

                @Override
                public Integer batchModifySort(List<TypeReqDTO> typeReqDTOList) {
                    log.error("批量修改分类顺序异常：{}", throwable.getMessage());
                    return null;
                }

                @Override
                public Boolean batchMoveItem(TypeSortReqDTO typeSortReqDTO) {
                    log.error("批量移动商品异常：{}", throwable.getMessage());
                    return null;
                }

                @Override
                public List<String> queryFilterItem(ItemSingleDTO itemSingleDTO) {
                    log.error("根据门店查询已下架和已推送的商品异常：{}", throwable.getMessage());
                    return null;
                }

                @Override
                public List<PricePlanStoreCheckRespDTO> checkStorePricePlanRule(PricePlanReqDTO reqDTO) {
                    log.error("门店绑定菜谱方案校验商品异常：{}", throwable.getMessage());
                    return null;
                }

                @Override
                public Boolean batchOperatingItem(PricePlanItemReqDTO reqDTO) {
                    log.error("商品批量设置上下架异常：{}", throwable.getMessage());
                    return null;
                }

                @Override
                public Boolean deletePlanDraft(PricePlanDraftReqDTO reqDTO) {
                    log.error("菜谱方案草稿删除异常：{}", throwable.getMessage());
                    return null;
                }

                @Override
                public PricePlanReqDTO getPricePlanCacheData(String userGuid, String brandGuid) {
                    log.error(HYSTRIX_PATTERN, "getPricePlanCacheData", userGuid + "-" + brandGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }


                @Override
                public List<PricePlanRespDTO> queryPlansByStoreGuid(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "queryPlansByStoreGuid", storeGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Boolean checkTypePricePlan(ItemQueryReqDTO itemQueryReqDTO) {
                    log.error("校验价格方案分类是否可以删除异常：{}", throwable.getMessage());
                    return null;
                }

                @Override
                public List<TypeItemRespDTO> findPricePlanItemList(ItemSingleDTO itemSingleDTO) {
                    log.error("菜谱方案可导入菜品查询异常：{}", throwable.getMessage());
                    return null;
                }

                @Override
                public Boolean sellOutRackItem(ItemErpReqDto itemErpReqDto) {
                    log.error("erp商品售罄回调异常：{}", throwable.getMessage());
                    return null;
                }

                @Override
                public Page<ItemWebRespDTO> queryPlanItemsByPlan(ItemQueryReqDTO itemQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryPlanItemsByPlan", JacksonUtils.writeValueAsString(itemQueryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<StoreDTO> queryPlanStoreListByPlan(ItemSingleDTO itemSingleDTO) {
                    log.error(HYSTRIX_PATTERN, "queryPlanStoreListByPlan", JacksonUtils.writeValueAsString(itemSingleDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PricePlanRespDTO> queryBelongPlansByStoreGuid(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "queryBelongPlansByStoreGuid", storeGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ItemInfoAndTypeRespDTO queryStoreItemBySalesModel(ItemSingleDTO itemSingleDTO) {
                    log.error(HYSTRIX_PATTERN, "queryStoreItemBySalesModel", itemSingleDTO,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ItemInfoAndTypeRespDTO queryStoreItemAndFilter(ItemStringListDTO itemStringListDTO) {
                    log.error(HYSTRIX_PATTERN, "queryStoreItemAndFilter", itemStringListDTO,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PreviewPlanRespDTO previewPlansByGuid(PreviewPlanReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "previewPlansByGuid", JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CheckQrCodeRespDTO checkQrCode(CheckQrCodeDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "checkQrCode", JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ItemAndTypeForAndroidRespDTO selectItemAndTypeForSyn(ItemQueryListReq itemQueryListReq) {
                    log.error(HYSTRIX_PATTERN, "selectItemAndTypeForSyn", JacksonUtils.writeValueAsString(itemQueryListReq),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PlanPriceAllTypeRespDTO> listAllPlanPriceItem(PlanPriceAllItemReqDTO req) {
                    log.error(HYSTRIX_PATTERN, "listAllPlanPriceItem", JacksonUtils.writeValueAsString(req),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<TypeRespDTO> listAllPlanPriceItemType(PlanPriceAllItemReqDTO req) {
                    log.error(HYSTRIX_PATTERN, "listAllPlanPriceItemType", JacksonUtils.writeValueAsString(req),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PlanPriceEditDTO> listItemTypeAndPlanPrice(String typeName) {
                    log.error(HYSTRIX_PATTERN, "listItemTypeAndPlanPrice", typeName, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void saveBatchEditPlanPriceType(PlanPriceEditReqDTO req) {
                    log.error(HYSTRIX_PATTERN, "saveBatchEditPlanPriceType", JacksonUtils.writeValueAsString(req),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ItemSkuAndPlanPriceDTO listItemSkuAndPlanPrice(String itemGuid) {
                    log.error(HYSTRIX_PATTERN, "listItemSkuAndPlanPrice", JacksonUtils.writeValueAsString(itemGuid),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void saveBatchEditPlanPrice(PlanPriceEditReqDTO req) {
                    log.error(HYSTRIX_PATTERN, "saveBatchEditPlanPrice", JacksonUtils.writeValueAsString(req),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PlanPriceEditDTO> listAvailablePlanPrice(PlanPriceAvailableReqDTO req) {
                    log.error(HYSTRIX_PATTERN, "listAvailablePlanPrice", JacksonUtils.writeValueAsString(req),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void saveBatchAddPlanPrice(PlanPriceAddReqDTO req) {
                    log.error(HYSTRIX_PATTERN, "saveBatchAddPlanPrice", JacksonUtils.writeValueAsString(req),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<ItemTypeRespDTO> listAvailablePlanItemType(String planGuid) {
                    log.error(HYSTRIX_PATTERN, "listAvailablePlanItemType", JacksonUtils.writeValueAsString(planGuid),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<PlanPriceAllItemSkuRespDTO> listAllPlanPriceItemSku(PlanPriceAllItemSkuReqDTO req) {
                    log.error(HYSTRIX_PATTERN, "listAllPlanPriceItemSku", JacksonUtils.writeValueAsString(req),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Boolean saveBatchSoldOutPlanItem(PlanPriceSoldOutReqDTO request) {
                    log.error(HYSTRIX_PATTERN, "saveBatchSoldOutPlanItem", JacksonUtils.writeValueAsString(request),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<ErpMappingType> queryErpItem(ItemSingleDTO itemSingleDTO) {
                    log.error(HYSTRIX_PATTERN, "queryErpItem", JacksonUtils.writeValueAsString(itemSingleDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Boolean copyPlan(String planGuid) {
                    log.error(HYSTRIX_PATTERN, "copyPlan", planGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<StoreItemListRespDTO> queryErpItemByStoreGuids(ItemStringListDTO itemStringListDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "queryErpItemByStoreGuids",
                                JacksonUtils.writeValueAsString(itemStringListDTO), ThrowableUtils.asString(throwable));
                    }
                    throw new ServerException();
                }

                @Override
                public List<TypeItemRespDTO> listTypeAndItemByBrand(@Valid ItemSingleDTO itemSingleDTO) {
                    log.error(HYSTRIX_PATTERN, "listTypeAndItemByBrand", JacksonUtils.writeValueAsString(itemSingleDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PrintSortRespDTO selectPrintItemType(PrintItemTypeDTO selectPrintItemType) {
                    log.error(HYSTRIX_PATTERN, "selectPrintItemType", JacksonUtils.writeValueAsString(selectPrintItemType),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<TypeWebRespDTO> queryTypeByBrand(ItemStringListDTO query) {
                    log.error(HYSTRIX_PATTERN, "queryTypeByBrand", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<ItemWebRespDTO> queryItemByBrand(ItemQueryReqDTO queryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryItemByBrand", JacksonUtils.writeValueAsString(queryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<ItemWebRespDTO> queryItemByGuid(ItemStringListDTO query) {
                    log.error(HYSTRIX_PATTERN, "queryItemByGuid", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<ItemSynRespDTO> queryRecommendItem(ItemSingleDTO query) {
                    log.error(HYSTRIX_PATTERN, "queryRecommendItem", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }

}
