package com.holderzone.holder.saas.aggregation.merchant.controller.report;

import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.ReportClientService;
import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.ChangeDetailDTO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


/**
 * 套餐换菜明细报表
 */
@Slf4j
@RestController
@RequestMapping("/report/trade/change")
@RequiredArgsConstructor
public class TradeChangeController {

    private final ReportClientService reportClientService;

    @ApiOperation(value = "查询套餐换菜明细报表")
    @PostMapping("/list")
    public Result<Message<ChangeDetailDTO>> list(@RequestBody @Valid ReportQueryVO query) {
        return Result.buildSuccessResult(reportClientService.queryTradeOrderItemChange(query));
    }

    @PostMapping("/export")
    public Result<String> export(@RequestBody @Valid ReportQueryVO query) {
        return Result.buildSuccessResult(reportClientService.tradeOrderItemChangeExport(query));
    }
}
