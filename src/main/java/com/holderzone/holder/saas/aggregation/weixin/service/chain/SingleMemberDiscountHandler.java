package com.holderzone.holder.saas.aggregation.weixin.service.chain;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.context.DiscountContext;
import com.holderzone.holder.saas.aggregation.weixin.utils.map.DiscountMAP;
import com.holderzone.holder.saas.weixin.utils.BigDecimalUtil;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.weixin.MinPriceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 会员优惠价计算
 */
@Component
@Slf4j
public class SingleMemberDiscountHandler extends DiscountHandler {


    @Override
    void dealDiscount(DiscountContext context) {
        if (context.isRejectDiscount()) {
            return;
        }
        if (context.isHasMember() && context.getUseMemberPriceFlag() && !context.isIntegralStore()) {
            DiscountFeeDetailDTO singleMemberCard = DiscountMAP.INSTANCE.discountDO2DiscountFeeDetailDTO(context.getDiscountTypeMap().get(type()));
            // 计算优惠金额
            BigDecimal singleMemberDiscountFee = dealWithDiscountFee(context.getCalculateOrderRespDTO().getOrderSurplusFee(),
                    getSingleMemberDiscountFee(context.getAllItems(), context.getDineInItemDTOMap()));
            // 会员优惠总金额
            singleMemberCard.setDiscountFee(singleMemberDiscountFee);
            // 订单剩余金额
            context.getCalculateOrderRespDTO().setOrderSurplusFee(context.getCalculateOrderRespDTO().getOrderSurplusFee().subtract(singleMemberDiscountFee));
            // 优惠明细
            context.getDiscountFeeDetailDTOS().add(singleMemberCard);
            log.info("会员单品价优惠：{}，订单剩余金额：{}，菜品优惠：{}", singleMemberCard.getDiscountFee(), context.getCalculateOrderRespDTO()
                    .getOrderSurplusFee(), JacksonUtils.writeValueAsString(context.getAllItems()));
        }
    }

    /**
     * 获取订单所有商品的优惠总额
     */
    private BigDecimal getSingleMemberDiscountFee(List<DineInItemDTO> dineInItemList, Map<String, DineInItemDTO> dineInItemMap) {
        BigDecimal freeDiscount = BigDecimal.ZERO;
        for (DineInItemDTO dineInItemDTO : dineInItemList) {
            // 会员价大于等于原价，则会员价不生效
            if (dineInItemDTO.getMemberPrice() == null ||
                    BigDecimalUtil.greaterEqual(dineInItemDTO.getMemberPrice(), dineInItemDTO.getOriginalPrice())) {
                continue;
            }
            // 是否用会员价格计算了
            dineInItemDTO.setIsCaculatByMemberPrice(1);
            dineInItemDTO.setMinPriceType(MinPriceTypeEnum.MEMBER_PRICE.getCode());
            dineInItemDTO.setMinPrice(BigDecimalUtil.setScale2(dineInItemDTO.getMemberPrice().multiply(dineInItemDTO.getCurrentCount())));
            // 用了会员价后的差价
            BigDecimal disparityPrice = dineInItemDTO.getOriginalPrice().subtract(dineInItemDTO.getMemberPrice());
            // 是否参与了单品券的数量
            if (dineInItemDTO.getIsGoodsReduceDiscount() != null) {
                // 商品数量需要减去被商品券抵扣的数量
                BigDecimal finalDisparityPrice = disparityPrice.multiply(dineInItemDTO.getCurrentCount()
                        .subtract(dineInItemDTO.getIsGoodsReduceDiscount()));
                // 最终商品优惠总价
                dineInItemDTO.setDiscountTotalPrice(dineInItemDTO.getDiscountTotalPrice().subtract(finalDisparityPrice));
                freeDiscount = freeDiscount.add(finalDisparityPrice);
                // 商品小计 = 商品小计 - 优惠金额
                dineInItemDTO.setItemPrice(BigDecimalUtil.setScale2(dineInItemDTO.getItemPrice()
                        .subtract(finalDisparityPrice)));
            } else {
                BigDecimal discountFee = disparityPrice.multiply(dineInItemDTO.getCurrentCount());
                BigDecimal itemNowFee = dineInItemDTO.getItemPrice().subtract(dineInItemDTO.getTotalDiscountFee());
                BigDecimal nowMemberFee = itemNowFee.compareTo(discountFee) > 0 ? discountFee : itemNowFee;
                freeDiscount = freeDiscount.add(nowMemberFee);
                // 最终商品优惠总价
                dineInItemDTO.setDiscountTotalPrice(dineInItemDTO.getDiscountTotalPrice().subtract(discountFee));
                // 商品小计 = 商品小计 - 优惠金额
                dineInItemDTO.setItemPrice(BigDecimalUtil.setScale2(dineInItemDTO.getItemPrice().subtract(discountFee)));
                // 设置商品会员优惠
                setMemberPreferential(dineInItemDTO, dineInItemMap, discountFee);
            }

        }
        if (BigDecimalUtil.lessThanZero(freeDiscount)) {
            freeDiscount = BigDecimal.ZERO;
        }
        return BigDecimalUtil.setScale2(freeDiscount);
    }

    /**
     * 设置商品会员优惠
     */
    private void setMemberPreferential(DineInItemDTO dineInItemDTO, Map<String, DineInItemDTO> dineInItemMap, BigDecimal discountFee) {
        if (dineInItemMap.containsKey(dineInItemDTO.getGuid())) {
            DineInItemDTO item = dineInItemMap.get(dineInItemDTO.getGuid());
            item.setMemberPreferential(discountFee);
            item.setDiscountPrice(dineInItemDTO.getDiscountPrice());
            log.info("会员价优惠金额：{}", discountFee);
        }
    }

    @Override
    Integer type() {
        return DiscountTypeEnum.SINGLE_MEMBER.getCode();
    }
}
