package com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.dto.trade.req.RecordThirdActivityInfoReqDTO;
import com.holderzone.saas.store.dto.trade.req.ThirdActivityRecordDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = TradeThirdActivityClientService.FastFoodFallBack.class)
public interface TradeThirdActivityClientService {

    @ApiOperation(value = "保存单个第三方活动使用信息")
    @PostMapping("/third_activity_record/save_third_activity_info")
    void saveThirdActivityInfo(@RequestBody RecordThirdActivityInfoReqDTO reqDTO);

    /**
     * 查询订单下使用的活动
     */
    @ApiOperation(value = "查询订单下使用的活动")
    @GetMapping("/third_activity_record/list_third_activity_by_order_guid")
    List<ThirdActivityRecordDTO> listThirdActivityByOrderGuid(@RequestParam("orderGuid") String orderGuid);


    @GetMapping("/third_activity_record/couponCodes/query_activity")
    List<GrouponListRespDTO> grouponCodesByActivityGuid(@RequestParam("orderGuid") String orderGuid,
                                                        @RequestParam("activityGuid") String activityGuid);

    @GetMapping("/third_activity_record/revoke_third_activity_record")
    void revokeThirdActivityRecord(@RequestParam("orderGuid") String orderGuid,
                                   @RequestParam("grouponType") Integer grouponType);

    @Component
    @Slf4j
    class FastFoodFallBack implements FallbackFactory<TradeThirdActivityClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public TradeThirdActivityClientService create(Throwable throwable) {
            return new TradeThirdActivityClientService() {

                @Override
                public void saveThirdActivityInfo(RecordThirdActivityInfoReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "saveThirdActivityInfo", JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<ThirdActivityRecordDTO> listThirdActivityByOrderGuid(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "listThirdActivityByOrderGuid", orderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }


                @Override
                public List<GrouponListRespDTO> grouponCodesByActivityGuid(String orderGuid, String activityGuid) {
                    log.error(HYSTRIX_PATTERN, "grouponCodesByActivityGuid", orderGuid + "," + activityGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void revokeThirdActivityRecord(String orderGuid, Integer grouponType) {
                    log.error(HYSTRIX_PATTERN, "revokeThirdActivityRecord", orderGuid + "," + grouponType,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
