package com.holderzone.holder.saas.aggregation.weixin.aop;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.dingding.handler.DingWarningHandler;
import io.undertow.servlet.spec.HttpServletRequestImpl;
import io.undertow.servlet.spec.HttpServletResponseImpl;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
@Component
@Slf4j
public class AopLogger {
    @Value(value = "${log.enable.controller:true}")
    private Boolean logEnable;

    @Resource
    DingWarningHandler dingWarningHandler;

    public Object around(ProceedingJoinPoint point,boolean isFeignClient) throws Throwable {
        if (logEnable) {
            List<Object> argObjs = new ArrayList<>();
            try {
                Object[] args = point.getArgs();
                for (Object obj : args) {
                    boolean isHttp = obj instanceof HttpServletResponseImpl || obj instanceof HttpServletRequestImpl;
                    if (!isHttp) {
                        argObjs.add(obj);
                    }
                }
                log.info("{},入参:{}", point.getSignature(), argObjs);
            } catch (Exception e) {
                log.error("logger日志输出异常,{}", e.getMessage());
            }
        }
        Object result = null;
        long beginTime = System.currentTimeMillis();
        try {
            result = point.proceed();
        } catch (Throwable e) {
            dingWarningHandler.sendDingDingGroupMsg(e);
            throw e;
        }
        if (!logEnable) {
            return result;
        }
        try {
            long time = System.currentTimeMillis() - beginTime;
            log.info("{}{},耗时:{}ms,return:{}",isFeignClient?"feignClient,":"", point.getSignature(), time,JacksonUtils.writeValueAsString(result));
        } catch (Throwable e) {
            log.error("logger日志输出异常,{}", e.getMessage());
        }
        return result;
    }
}
