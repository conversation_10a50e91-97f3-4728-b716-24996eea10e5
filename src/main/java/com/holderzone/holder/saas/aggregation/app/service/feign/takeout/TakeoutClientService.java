package com.holderzone.holder.saas.aggregation.app.service.feign.takeout;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.aggregation.app.execption.GlobalTransactionalHandler;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.SalesUpdateDTO;
import com.holderzone.saas.store.dto.takeaway.response.OwnDistributionDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutOrderStatisticsRespDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderItemQuery;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderListQuery;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustTakeoutOrderReqDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByTakeawayOrderRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeawayClientService
 * @date 2018/09/08 11:15
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-takeaway-consumer", fallbackFactory = TakeoutClientService.ServiceFallBack.class)
public interface TakeoutClientService {

    @PostMapping("/takeout/accept_order")
    void acceptOrder(@RequestBody TakeoutOrderDTO takeawayOrderDTO);

    @PostMapping("/takeout/cancel_order")
    void cancelOrder(@RequestBody TakeoutOrderDTO takeawayOrderDTO);

    @PostMapping("/takeout/agree_cancel_req")
    String agreeCancelReq(@RequestBody TakeoutOrderDTO takeawayOrderDTO);

    @PostMapping("/takeout/disagree_cancel_req")
    String disagreeCancelReq(@RequestBody TakeoutOrderDTO takeawayOrderDTO);

    @PostMapping("/takeout/agree_refund_req")
    String agreeRefundReq(@RequestBody TakeoutOrderDTO takeawayOrderDTO);

    @PostMapping("/takeout/disagree_refund_req")
    String disagreeRefundReq(@RequestBody TakeoutOrderDTO takeawayOrderDTO);

    @PostMapping("/takeout/reply_remind_order")
    void replyRemindOrder(@RequestBody TakeoutOrderDTO takeawayOrderDTO);

    @PostMapping("/takeout/list_order")
    List<TakeoutOrderListDTO> listOrder(@RequestBody TakeoutOrderDTO takeoutOrderDTO);

    @PostMapping("/takeout/count_order")
    TakeoutOrderStatisticsRespDTO countOrder(@RequestBody TakeoutOrderDTO takeoutOrderDTO);

    @PostMapping("/takeout/get_order")
    TakeoutOrderDTO getOrderDetail(@RequestBody TakeoutOrderDTO takeoutOrderDTO);

    @GetMapping("/takeout/get_order/mapping/{orderGuid}")
    TakeoutOrderDTO getOrderDetailMapping(@PathVariable String orderGuid);

    @ApiOperation(value = "订单编号查询外卖订单详情", notes = "查询外卖订单详情")
    @GetMapping("/takeout/get_order_by_orderNo")
    TakeoutOrderDTO getOrderByOrderNo(@RequestParam("orderNo") String orderNo,
                                      @RequestParam("storeGuid") String storeGuid);

    @PostMapping("/takeout/print_bill")
    String printBill(@RequestBody TakeoutOrderDTO takeawayOrderDTO);

    @PostMapping("/takeout/print_kitchen")
    String printKitchen(@RequestBody TakeoutOrderDTO takeawayOrderDTO);

    @PostMapping("/takeout/query_auto_receive")
    TakeoutAutoRcvDTO queryAutoReceive(@RequestBody TakeoutAutoRcvDTO takeoutAutoRcvDTO);

    @PostMapping("/takeout/get_distribution")
    List<OwnDistributionDTO> getDistribution(@RequestBody BaseDTO baseDTO);

    @PostMapping("/takeout/order_update")
    OwnCallbackResponse orderUpdate(@RequestBody SalesUpdateDTO salesUpdateDTO);

    @PostMapping("/takeout/go_shipping")
    OwnCallbackResponse goShipping(@RequestBody TakeoutOrderDTO takeoutOrderDTO);

    @PostMapping("/takeout/done_shipping")
    OwnCallbackResponse doneShipping(@RequestBody TakeoutOrderDTO takeoutOrderDTO);

    @PostMapping("/takeout/cancel_shipping")
    OwnCallbackResponse cancelShipping(@RequestBody TakeoutOrderDTO takeoutOrderDTO);

    @PostMapping("/takeout/set_auto_receive")
    Boolean setAutoReceive(@RequestBody TakeoutAutoRcvDTO takeoutAutoRcvDTO);

    @PostMapping("/takeout/set_config")
    Boolean setConfig(@RequestBody TakeoutConfigDTO takeoutConfigDTO);

    @PostMapping("/takeout/query_config")
    TakeoutConfigDTO queryConfig(@RequestBody TakeoutConfigDTO takeoutConfigDTO);

    @PostMapping("/takeout/delivery_change")
    void deliveryChange(@RequestBody TakeoutDeliveryChange takeoutDeliveryChange);

    @PostMapping("/takeout/delivery_location")
    void deliveryLocation(TakeoutDeliveryChange takeoutDeliveryChange);

    /**
     * 调整单-分页查询外卖订单列表
     * 外卖展示近30天 待接单、配送中、已完成的订单数据
     *
     * @param query 关键字，门店guid，分页数据
     * @return 外卖订单列表
     */
    @PostMapping(value = "/takeout/page_order_by_adjust")
    Page<AdjustByTakeawayOrderRespDTO> pageOrderByAdjust(@RequestBody AdjustByOrderListQuery query);

    /**
     * 调整单-查询外卖订单商品
     * 标记商品是否调整过
     *
     * @param query 订单guid，门店，交易模式
     * @return 订单商品信息列表
     */
    @PostMapping("/takeout/list_order_item")
    AdjustByOrderRespDTO listOrderItem(@RequestBody AdjustByOrderItemQuery query);

    /**
     * 调整外卖单-编辑外卖单和外卖订单明细为已调整
     */
    @PostMapping("/takeout/adjust_order")
    void adjustOrder(@RequestBody AdjustTakeoutOrderReqDTO reqDTO);

    /**
     * 轮询查询自动接单漏单订单
     */
    @PostMapping("/takeout/delay/auto_accept_order")
    void delayAutoAcceptOrder(@RequestBody TakeoutOrderDTO reqDTO);

    /**
     * 订单出餐
     * @param takeoutOrderDTO 入参
     */
    @PostMapping("/takeout/prepared")
    void orderPrepared(@RequestBody TakeoutOrderDTO takeoutOrderDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<TakeoutClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public TakeoutClientService create(Throwable cause) {
            return new TakeoutClientService() {

                @Override
                public void acceptOrder(TakeoutOrderDTO takeawayOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "acceptOrder", JacksonUtils.writeValueAsString(takeawayOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void cancelOrder(TakeoutOrderDTO takeawayOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "cancelOrder", JacksonUtils.writeValueAsString(takeawayOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public String agreeCancelReq(TakeoutOrderDTO takeawayOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "agreeCancelReq", JacksonUtils.writeValueAsString(takeawayOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public String disagreeCancelReq(TakeoutOrderDTO takeawayOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "disagreeCancelReq", JacksonUtils.writeValueAsString(takeawayOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }


                @Override
                public List<OwnDistributionDTO> getDistribution(BaseDTO baseDTO) {
                    log.error(HYSTRIX_PATTERN, "getDistribution", JacksonUtils.writeValueAsString(baseDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public OwnCallbackResponse orderUpdate(SalesUpdateDTO salesUpdateDTO) {
                    log.error(HYSTRIX_PATTERN, "orderUpdate", JacksonUtils.writeValueAsString(salesUpdateDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public OwnCallbackResponse goShipping(TakeoutOrderDTO takeoutOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "goShipping", JacksonUtils.writeValueAsString(takeoutOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public OwnCallbackResponse doneShipping(TakeoutOrderDTO takeoutOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "doneShipping", JacksonUtils.writeValueAsString(takeoutOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public OwnCallbackResponse cancelShipping(TakeoutOrderDTO takeoutOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "cancelShipping", JacksonUtils.writeValueAsString(takeoutOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public String agreeRefundReq(TakeoutOrderDTO takeawayOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "agreeRefundReq", JacksonUtils.writeValueAsString(takeawayOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public String disagreeRefundReq(TakeoutOrderDTO takeawayOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "disagreeRefundReq", JacksonUtils.writeValueAsString(takeawayOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void replyRemindOrder(TakeoutOrderDTO takeawayOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "replyRemindOrder", JacksonUtils.writeValueAsString(takeawayOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<TakeoutOrderListDTO> listOrder(TakeoutOrderDTO takeoutOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "listOrder", JacksonUtils.writeValueAsString(takeoutOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TakeoutOrderStatisticsRespDTO countOrder(TakeoutOrderDTO takeoutOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "countOrder", JacksonUtils.writeValueAsString(takeoutOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TakeoutOrderDTO getOrderDetail(TakeoutOrderDTO takeoutOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "getOrderDetail", JacksonUtils.writeValueAsString(takeoutOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TakeoutOrderDTO getOrderDetailMapping(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "getOrderDetailMapping", orderGuid, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TakeoutOrderDTO getOrderByOrderNo(String orderNo, String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "getOrderByOrderNo", orderNo + "-" + storeGuid,
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public String printBill(TakeoutOrderDTO takeawayOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "printBill", JacksonUtils.writeValueAsString(takeawayOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public String printKitchen(TakeoutOrderDTO takeawayOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "printKitchen", JacksonUtils.writeValueAsString(takeawayOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TakeoutAutoRcvDTO queryAutoReceive(TakeoutAutoRcvDTO takeoutAutoRcvDTO) {
                    log.error(HYSTRIX_PATTERN, "queryAutoReceive", JacksonUtils.writeValueAsString(takeoutAutoRcvDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Boolean setAutoReceive(TakeoutAutoRcvDTO takeoutAutoRcvDTO) {
                    log.error(HYSTRIX_PATTERN, "setAutoReceive", JacksonUtils.writeValueAsString(takeoutAutoRcvDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Boolean setConfig(TakeoutConfigDTO takeoutConfigDTO) {
                    log.error(HYSTRIX_PATTERN, "setConfig", JacksonUtils.writeValueAsString(takeoutConfigDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TakeoutConfigDTO queryConfig(TakeoutConfigDTO takeoutConfigDTO) {
                    log.error(HYSTRIX_PATTERN, "queryConfig", JacksonUtils.writeValueAsString(takeoutConfigDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void deliveryChange(TakeoutDeliveryChange takeoutDeliveryChange) {
                    log.error(HYSTRIX_PATTERN, "deliveryChange", JacksonUtils.writeValueAsString(takeoutDeliveryChange),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void deliveryLocation(TakeoutDeliveryChange takeoutDeliveryChange) {
                    log.error(HYSTRIX_PATTERN, "deliveryLocation", JacksonUtils.writeValueAsString(takeoutDeliveryChange),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Page<AdjustByTakeawayOrderRespDTO> pageOrderByAdjust(AdjustByOrderListQuery query) {
                    log.error(HYSTRIX_PATTERN, "pageOrderByAdjust", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public AdjustByOrderRespDTO listOrderItem(AdjustByOrderItemQuery query) {
                    log.error(HYSTRIX_PATTERN, "listOrderItem", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void adjustOrder(AdjustTakeoutOrderReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "adjustOrder", JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(cause));
                    GlobalTransactionalHandler.afterReturning();
                    throw new BusinessException("新增调整单失败" + cause.getMessage());
                }

                @Override
                public void delayAutoAcceptOrder(TakeoutOrderDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "delayAutoAcceptOrder", JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(cause));
                    GlobalTransactionalHandler.afterReturning();
                    throw new BusinessException("轮询查询自动接单漏单订单" + cause.getMessage());
                }

                @Override
                public void orderPrepared(TakeoutOrderDTO takeoutOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "orderPrepared", JacksonUtils.writeValueAsString(takeoutOrderDTO),
                            ThrowableUtils.asString(cause));
                    GlobalTransactionalHandler.afterReturning();
                    throw new BusinessException("外卖订单出餐" + cause.getMessage());
                }
            };
        }
    }
}
