package com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.*;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/09 下午 15:12
 * @description
 */
@FeignClient(name = "holder-saas-store-erp", fallbackFactory = CheckoutDocumentFeignFallback.class)
public interface CheckoutDocumentFeignService {

    /**
     * 添加或者更新盘点单
     *
     * @param addOrUpdateDTO
     * @return
     */
    @PostMapping("/checkoutDocument/addOrUpdateCheckoutDocument")
    String addOrUpdateCheckoutDocument(@RequestBody CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO);

    /**
     * 查询物料信息(新增物料时使用)
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/checkoutDocument/selectDocumentDetailForAdd")
    List<CheckoutDocumentDetailSelectDTO> selectDocumentDetailForAdd(@RequestBody CheckoutDocumentDetailQueryDTO queryDTO);


    /**
     * 删除盘点单
     *
     * @param documentGuid
     */
    @PostMapping("/checkoutDocument/deleteDocument")
    void deleteCheckoutDocumentAndDetail(@RequestParam("documentGuid") String documentGuid);

    /**
     * 提交盘点单
     *
     * @param addOrUpdateDTO
     * @return
     */
    @PostMapping("/checkoutDocument/submitCheckoutDocument")
    String submitCheckoutDocument(@RequestBody CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO);

    /**
     * 分页查询盘点单列表
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/checkoutDocument/selectCheckoutDocumentForPage")
    Page<CheckoutDocumentSelectDTO> selectCheckoutDocumentForPage(@RequestBody CheckoutDocumentQueryDTO queryDTO);

    /**
     * 查询盘点单及其明细(编辑时使用)
     *
     * @param documentGuid
     * @return
     */
    @PostMapping("/checkoutDocument/selectDocumentAndDetailForUpdate")
    CheckoutDocumentSelectDTO selectDocumentAndDetailForUpdate(@RequestParam("documentGuid") String documentGuid);

    /**
     * 查询盘点单及其明细(查看时使用)
     *
     * @param documentGuid
     * @return
     */
    @PostMapping("/checkoutDocument/selectDocumentAndDetailForSelect")
    CheckoutDocumentSelectDTO selectDocumentAndDetailForSelect(@RequestParam("documentGuid")String documentGuid);
}

@Component
class CheckoutDocumentFeignFallback implements FallbackFactory<CheckoutDocumentFeignService> {

    private static final Logger log = LoggerFactory.getLogger(CheckoutDocumentFeignFallback.class);
    private static final String HYSTRIX_RESULT = "调用进销存服务熔断！";

    @Override
    public CheckoutDocumentFeignService create(Throwable throwable) {
        return new CheckoutDocumentFeignService() {
            @Override
            public String addOrUpdateCheckoutDocument(CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO) {
                log.error(HYSTRIX_RESULT + "错误原因 throwable:{}", throwable.getMessage());
                return null;
            }

            @Override
            public List<CheckoutDocumentDetailSelectDTO> selectDocumentDetailForAdd(CheckoutDocumentDetailQueryDTO queryDTO) {
                log.error(HYSTRIX_RESULT + "错误原因 throwable:{}", throwable.getMessage());
                return Collections.emptyList();
            }


            @Override
            public void deleteCheckoutDocumentAndDetail(String documentGuid) {
                log.error(HYSTRIX_RESULT + "错误原因 throwable:{}", throwable.getMessage());
            }

            @Override
            public String submitCheckoutDocument(CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO) {
                log.error(HYSTRIX_RESULT + "错误原因 throwable:{}", throwable.getMessage());
                return null;
            }

            @Override
            public Page<CheckoutDocumentSelectDTO> selectCheckoutDocumentForPage(CheckoutDocumentQueryDTO queryDTO) {
                log.error(HYSTRIX_RESULT + "错误原因 throwable:{}", throwable.getMessage());
                return null;
            }

            @Override
            public CheckoutDocumentSelectDTO selectDocumentAndDetailForUpdate(String documentGuid) {
                log.error(HYSTRIX_RESULT + "错误原因 throwable:{}", throwable.getMessage());
                return null;
            }

            @Override
            public CheckoutDocumentSelectDTO selectDocumentAndDetailForSelect(String documentGuid) {
                log.error(HYSTRIX_RESULT + "错误原因 throwable:{}", throwable.getMessage());
                return null;
            }
        };
    }
}
