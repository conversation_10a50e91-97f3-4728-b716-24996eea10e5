package com.holderzone.holder.saas.aggregation.merchant.controller.report;

import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.ReportClientService;
import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.ReturnDetailItemDTO;
import com.holderzone.saas.store.dto.report.resp.ReturnItemDTO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.Valid;


/**
 * 订单退菜报表
 */
@Slf4j
@RestController
@RequestMapping("/report/trade/return")
@RequiredArgsConstructor
public class TradeReturnController {

    private final ReportClientService reportClientService;

    @ApiOperation(value = "查询订单退菜及合计列表")
    @PostMapping("/list")
    public Result<Message<ReturnItemDTO>> queryTradeOrderReturn(@RequestBody @Valid ReportQueryVO query) {
        return Result.buildSuccessResult(reportClientService.queryTradeOrderReturn(query));
    }

    @PostMapping("/list_detail")
    public Result<Message<ReturnDetailItemDTO>> queryTradeOrderDetailReturn(@RequestBody @Valid ReportQueryVO query) {
        return Result.buildSuccessResult(reportClientService.listDetail(query));
    }

    @PostMapping("/detail_export")
    public Result<String> detailExport(@RequestBody @Valid ReportQueryVO query) {
        return Result.buildSuccessResult(reportClientService.detailExport(query));
    }

}
