package com.holderzone.holder.saas.aggregation.app.controller.report;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.print.PrinterClient;
import com.holderzone.holder.saas.aggregation.app.service.feign.report.ReportClientService;
import com.holderzone.holder.saas.aggregation.app.utils.MapstructUtil;
import com.holderzone.saas.store.dto.journaling.req.DailyPrintReqDTO;
import com.holderzone.saas.store.dto.journaling.req.DailySaleDetailReqDTO;
import com.holderzone.saas.store.dto.journaling.req.JournalStoreAppBaseReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.BusinessDataRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.RetailBusinessDataRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.TotalPaymentRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.TotalSaleRespDTO;
import com.holderzone.saas.store.dto.print.content.PrintItemStatsDTO;
import com.holderzone.saas.store.dto.print.content.PrintTypeStatsDTO;
import com.holderzone.saas.store.dto.print.content.nested.InOutRecord;
import com.holderzone.saas.store.dto.print.content.nested.PayDetailRecord;
import com.holderzone.saas.store.dto.print.content.retail.PrintRetailOpStatsDTO;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.enums.print.PrintSourceEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReportDailyController
 * @date 2019/10/25 14:19
 * @description 新营业日报
 * @program holder-saas-store
 */
@Api("营业日报接口")
@RestController
@RequestMapping("/daily")
@Slf4j
public class ReportDailyController {

    private final ReportClientService reportClientService;

    private final PrinterClient printerClient;

    @Autowired
    public ReportDailyController(ReportClientService reportClientService,
                                 PrinterClient printerClient) {
        this.reportClientService = reportClientService;
        this.printerClient = printerClient;
    }

    @PostMapping("/total")
    @ApiOperation("营业汇总")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT, description = "营业汇总")
    public Result<BusinessDataRespDTO> businessDaily(@RequestBody JournalStoreAppBaseReqDTO journalStoreAppBaseReqDTO) {
        return Result.buildSuccessResult(reportClientService.businessDaily(journalStoreAppBaseReqDTO));
    }

    @ApiOperation(value = "支付汇总")
    @PostMapping("/total_payment")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT, description = "支付汇总")
    public Result<TotalPaymentRespDTO> totalPayment(@RequestBody JournalStoreAppBaseReqDTO journalAppBaseReqDTO) {
        return Result.buildSuccessResult(reportClientService.totalPayment(journalAppBaseReqDTO));
    }

    @ApiOperation(value = "销售相关统计")
    @PostMapping("/sale_detail")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT, description = "销售相关统计")
    public Result<TotalSaleRespDTO> saleDetail(@RequestBody DailySaleDetailReqDTO dailySaleDetailReqDTO) {
        return Result.buildSuccessResult(reportClientService.saleDetail(dailySaleDetailReqDTO));
    }

    @ApiOperation(value = "营业日报打印")
    @PostMapping("/print")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT, description = "营业日报打印")
    public Result print(@RequestBody DailyPrintReqDTO request) {
        Integer type = request.getType();
        DailySaleDetailReqDTO dailySaleDetailReqDTO = MapstructUtil.INSTANCE.dailyPrintReqDTO2DailySaleDetailReqDTO(request);
        TotalSaleRespDTO totalSaleRespDTO = null;  //销售统计
        RetailBusinessDataRespDTO businessDataRespDTO = null; //营业概况统计 明细
        TotalPaymentRespDTO totalPaymentRespDTO = null; //营业概况统计 支付明细
        // 排除 5：营业概况打印
        if (!Objects.equals(type, 5)) {
            totalSaleRespDTO = reportClientService.saleDetail(dailySaleDetailReqDTO);
        } else {
            dailySaleDetailReqDTO.setReqType(null);
            businessDataRespDTO = reportClientService.businessDaily(dailySaleDetailReqDTO);
            totalPaymentRespDTO = reportClientService.totalPayment(dailySaleDetailReqDTO);
            log.warn("report return totalPaymentRespDTO = {} ", JacksonUtils.writeValueAsString(totalPaymentRespDTO));

        }
        switch (type) {
            case 1:
                //分类销售统计打印
                List<PrintTypeStatsDTO.ItemType> itemTypeList = MapstructUtil.INSTANCE.itemCountDetailList2itemTypeList(totalSaleRespDTO.getItemCountDetailDTOS());
                PrintTypeStatsDTO build = new PrintTypeStatsDTO();
                build.setItemTypeList(itemTypeList);
                build.setCreateTime(DateTimeUtils.nowMillis());
                build.setInvoiceType(InvoiceTypeEnum.RETAIL_TYPE_STATS.getType());
                build.setOperatorStaffName(UserContextUtils.getUserName());
                build.setPrintSourceEnum(PrintSourceEnum.AIO);
                build.setEndTime(request.getEndDate().isEqual(LocalDate.now()) ? DateTimeUtils.nowMillis() :DateTimeUtils.localDateTime2Mills(request.getEndDate().atTime(LocalTime.MAX)));
                build.setStartTime(DateTimeUtils.parseStrDate(DateTimeUtils.localDate2String(request.getStartDate(), DateTimeUtils.PATTERN_DAY) + " 00:00:00", DateTimeUtils.PATTERN_SECONDS));
                build.setPrintUid(UserContextUtils.getEnterpriseGuid());
                build.setStoreName(UserContextUtils.getStoreName());
                build.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
                build.setDeviceId(request.getDeviceId());
                build.setOperatorStaffGuid(request.getCreateStaffGuid());
                build.setStoreGuid(UserContextUtils.getStoreGuid());
                build.setOperatorStaffGuid(request.getCreateStaffGuid());
                printerClient.printClassifySales(build);
                break;
            case 5:
                //营业概况打印
                PrintRetailOpStatsDTO printRetailOpStatsDTO = new PrintRetailOpStatsDTO();
                List<InOutRecord> inOutRecordList = Arrays.asList(InOutRecord
                                .builder()
                                .name("销售统计")
                                .number(businessDataRespDTO.getOrderCount().longValue())
                                .money(businessDataRespDTO.getBusinessFee())
                                .build(),
                        InOutRecord.
                                builder()
                                .name("退货统计")
                                .number(businessDataRespDTO.getRefundOrderCount().longValue())
                                .money(businessDataRespDTO.getRefundAmount())
                                .build());
                List<PayDetailRecord> payDetailRecordList = MapstructUtil.INSTANCE.paymentReportList2payDetailRecordList(totalPaymentRespDTO.getPaymentReportList());
                log.warn(" payDetailRecordList = {} ", JacksonUtils.writeValueAsString(payDetailRecordList));
                printRetailOpStatsDTO.setInOutRecordList(inOutRecordList);
                printRetailOpStatsDTO.setPayDetailRecordList(payDetailRecordList);
                printRetailOpStatsDTO.setCreateTime(DateTimeUtils.nowMillis());
                printRetailOpStatsDTO.setInvoiceType(InvoiceTypeEnum.RETAIL_OP_STATS.getType());
                printRetailOpStatsDTO.setOperatorStaffName(UserContextUtils.getUserName());
                printRetailOpStatsDTO.setPrintSourceEnum(PrintSourceEnum.AIO);
                printRetailOpStatsDTO.setEndTime(request.getEndDate().isEqual(LocalDate.now()) ? DateTimeUtils.nowMillis() :DateTimeUtils.localDateTime2Mills(request.getEndDate().atTime(LocalTime.MAX)));
                printRetailOpStatsDTO.setStartTime(DateTimeUtils.parseStrDate(DateTimeUtils.localDate2String(request.getStartDate(), DateTimeUtils.PATTERN_DAY) + " 00:00:00", DateTimeUtils.PATTERN_SECONDS));
                printRetailOpStatsDTO.setPrintUid(UserContextUtils.getEnterpriseGuid());
                printRetailOpStatsDTO.setStoreName(UserContextUtils.getStoreName());
                printRetailOpStatsDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
                printRetailOpStatsDTO.setDeviceId(request.getDeviceId());
                printRetailOpStatsDTO.setOperatorStaffGuid(request.getCreateStaffGuid());
                printRetailOpStatsDTO.setStoreGuid(UserContextUtils.getStoreGuid());
                printRetailOpStatsDTO.setOperatorStaffGuid(request.getCreateStaffGuid());
                printerClient.printRetailOpStats(printRetailOpStatsDTO);
                break;
            default:
                //商品 、退货 、赠货打印
                printerClient.printItemSales(dailyItemPrintHelper(totalSaleRespDTO, request));
                break;
        }
        return Result.buildEmptySuccess();
    }


    public PrintItemStatsDTO dailyItemPrintHelper(TotalSaleRespDTO totalSaleRespDTO, DailyPrintReqDTO request) {
        List<PrintItemStatsDTO.Item> itemStatsList = MapstructUtil.INSTANCE.itemCountDetailList2itemStatsList(totalSaleRespDTO.getItemCountDetailDTOS());
        PrintItemStatsDTO item = new PrintItemStatsDTO();
        item.setItemList(itemStatsList);
        item.setCreateTime(DateTimeUtils.nowMillis());
        item.setInvoiceType(request.getType().equals(2)
                ? InvoiceTypeEnum.RETAIL_ITEM_STATS.getType()
                : request.getType().equals(3)
                ? InvoiceTypeEnum.RETAIL_ITEM_REFUND_STATS.getType()
                : InvoiceTypeEnum.RETAIL_ITEM_GIFT_STATS.getType());
        item.setOperatorStaffName(UserContextUtils.getUserName());
        item.setPrintSourceEnum(PrintSourceEnum.AIO);
        item.setEndTime(request.getEndDate().isEqual(LocalDate.now()) ? DateTimeUtils.nowMillis() :DateTimeUtils.localDateTime2Mills(request.getEndDate().atTime(LocalTime.MAX)));
        item.setStartTime(DateTimeUtils.parseStrDate(DateTimeUtils.localDate2String(request.getStartDate(), DateTimeUtils.PATTERN_DAY) + " 00:00:00", DateTimeUtils.PATTERN_SECONDS));
        item.setPrintUid(UserContextUtils.getEnterpriseGuid());
        item.setStoreName(UserContextUtils.getStoreName());
        item.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        item.setDeviceId(request.getDeviceId());
        item.setOperatorStaffGuid(request.getCreateStaffGuid());
        item.setStoreGuid(UserContextUtils.getStoreGuid());
        item.setOperatorStaffGuid(request.getCreateStaffGuid());
        return item;

    }


}
