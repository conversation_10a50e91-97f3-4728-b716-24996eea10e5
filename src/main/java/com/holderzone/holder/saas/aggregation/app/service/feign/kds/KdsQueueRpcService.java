package com.holderzone.holder.saas.aggregation.app.service.feign.kds;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.kds.req.QueueConfigDTO;
import com.holderzone.saas.store.dto.kds.req.QueueQueryReqDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsQueueRespDTO;
import com.holderzone.saas.store.dto.kds.resp.KitchenItemDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(name = "holder-saas-store-kds", fallbackFactory = KdsQueueRpcService.FallbackFactoryImpl.class)
public interface KdsQueueRpcService {

    @PostMapping("/kds_queue/config/query")
    QueueConfigDTO queryConfig(QueueConfigDTO queueConfigDTO);

    @PostMapping("/kds_queue/config/update")
    void updateConfig(QueueConfigDTO queueConfigDTO);

    @PostMapping("/kds_queue/item/query")
    KdsQueueRespDTO queryQueueItem(@RequestBody QueueQueryReqDTO queueQueryReqDTO);

    @PostMapping("/kds_queue/item/call_for_meal")
    void dstCallForMealAgain(KitchenItemDTO kitchenItemDTO);

    @Slf4j
    @Component
    class FallbackFactoryImpl implements FallbackFactory<KdsQueueRpcService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public KdsQueueRpcService create(Throwable throwable) {

            return new KdsQueueRpcService() {

                @Override
                public QueueConfigDTO queryConfig(QueueConfigDTO queueConfigDTO) {
                    log.error(HYSTRIX_PATTERN, "queryConfig",
                            JacksonUtils.writeValueAsString(queueConfigDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void updateConfig(QueueConfigDTO queueConfigDTO) {
                    log.error(HYSTRIX_PATTERN, "updateConfig",
                            JacksonUtils.writeValueAsString(queueConfigDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public KdsQueueRespDTO queryQueueItem(QueueQueryReqDTO queueQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "query",
                            JacksonUtils.writeValueAsString(queueQueryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void dstCallForMealAgain(KitchenItemDTO kitchenItemDTO) {
                    log.error(HYSTRIX_PATTERN, "dstCallForMealAgain",
                            JacksonUtils.writeValueAsString(kitchenItemDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
