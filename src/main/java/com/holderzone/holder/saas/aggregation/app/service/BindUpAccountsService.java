package com.holderzone.holder.saas.aggregation.app.service;

import com.holderzone.saas.store.dto.store.store.BindupAccountsTips;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableService
 * @date 2019/01/07 11:23
 * @description
 * @program holder-saas-aggregation-app
 */
public interface BindUpAccountsService {

    BindupAccountsTips loginAutoBuAccounts(TableBasicQueryDTO tableBasicQueryDTO);

    /**
     * 不提示构建返回对象
     * @return
     */
    BindupAccountsTips noTipsBindUpAccount(String storeGuid);

    /**
     * 当前时间的营业日
     * @param storeGuid
     * @return
     */
    LocalDate currentTimeDay(String storeGuid, LocalDateTime localDateTime);


    /**
     * 检查是否最后一台桌台，并判断昨日是否扎帐
     * @param storeGuid
     */
    void checkTableStatus(String storeGuid);
}
