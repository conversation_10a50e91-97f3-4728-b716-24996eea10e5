package com.holderzone.holder.saas.aggregation.weixin.service.chain;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.assembler.MarketingActivityAssembler;
import com.holderzone.holder.saas.aggregation.weixin.context.DiscountContext;
import com.holderzone.holder.saas.aggregation.weixin.helper.DineInItemHelper;
import com.holderzone.holder.saas.aggregation.weixin.helper.MarketingActivityHelper;
import com.holderzone.holder.saas.aggregation.weixin.service.ItemService;
import com.holderzone.holder.saas.aggregation.weixin.utils.map.DiscountMAP;
import com.holderzone.holder.saas.aggregation.weixin.utils.map.MemberDishInfoMAP;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.member.wechat.dto.activitie.RequestMarketActivityUse;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseClientMarketActivity;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseMarketActivityUse;
import com.holderzone.saas.store.dto.marketing.nth.NthActivityDetailsVO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityDetailsVO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountRuleDTO;
import com.holderzone.saas.store.dto.trade.DiscountDTO;
import com.holderzone.saas.store.dto.weixin.deal.ActivitySelectDTO;
import com.holderzone.saas.store.dto.weixin.resp.MarketingActivityInfoRespDTO;
import com.holderzone.saas.store.enums.member.MarketActivityUnableTipEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;


@Component
@Slf4j
@AllArgsConstructor
public class ActivityDiscountHandler extends DiscountHandler {

    private final ItemService itemService;

    private final MarketingActivityHelper marketingActivityHelper;

    @Override
    void dealDiscount(DiscountContext context) {
        if (context.isRejectDiscount()) {
            return;
        }
        if (Boolean.FALSE.equals(context.getNeedFullMarketActivityList())) {
            return;
        }
        DiscountDTO activityDO = context.getDiscountTypeMap().get(type());
        DiscountFeeDetailDTO marketActivity = DiscountMAP.INSTANCE.discountDO2DiscountFeeDetailDTO(activityDO);
        // 满减满折计算
        ResponseMarketActivityUse activityUseRespDTO = dealMarketActivity(context);
        log.info("营销活动返回：{}", JacksonUtils.writeValueAsString(activityUseRespDTO));
        marketActivity.setContent(activityUseRespDTO.getActivityName());
        // 商品优惠金额计算
        singleItemDiscount(context.getDineInItemDTOMap(), marketActivity,
                MemberDishInfoMAP.INSTANCE.toMemberTerminalDishDTOList(activityUseRespDTO.getDishInfoDTOList()));
        // 处理商品优惠金额
        handleDiscountTotalPrice(context, MemberDishInfoMAP.INSTANCE.toMemberTerminalDishDTOList(activityUseRespDTO.getDishInfoDTOList()));
        // 订单剩余金额
        context.getCalculateOrderRespDTO().setOrderSurplusFee(context.getCalculateOrderRespDTO().getOrderSurplusFee().subtract(marketActivity
                .getDiscountFee()));
        // 活动列表
        // 替换不可使用原因， 前端显示
        replaceActivityUnUseReason(activityUseRespDTO.getActivitiesList());
        LimitSpecialsActivityDetailsVO specialsActivitySelectDetailsVO = context.getDiscountRuleBO().getSpecialsActivityDetailsVO();
        NthActivityDetailsVO nthActivityDetailsVO = context.getDiscountRuleBO().getNthActivityDetailsVO();
        List<MarketingActivityInfoRespDTO> activityInfoList = MarketingActivityAssembler.respClientActivityList2activityInfoList(
                activityUseRespDTO.getActivitiesList(), specialsActivitySelectDetailsVO, nthActivityDetailsVO);
        context.getCalculateOrderRespDTO().getActivityInfoList().addAll(activityInfoList);
        if (com.holderzone.framework.util.StringUtils.hasText(activityUseRespDTO.getActivityGuid())) {
            ActivitySelectDTO selectDTO = new ActivitySelectDTO();
            selectDTO.setActivityGuid(activityUseRespDTO.getActivityGuid());
            selectDTO.setActivityType(DiscountTypeEnum.ACTIVITY.getCode());
            // 活动信息
            DiscountRuleDTO discountRuleDTO = new DiscountRuleDTO();
            discountRuleDTO.setActivityGuid(activityUseRespDTO.getActivityGuid());
            discountRuleDTO.setActivityName(activityUseRespDTO.getActivityName());
            marketActivity.setRule(JacksonUtils.writeValueAsString(discountRuleDTO));
            context.getCalculateOrderRespDTO().getActivitySelectList().add(selectDTO);
        }
        // 是否与会员优惠共享
        context.getDiscountRuleBO().setActivityMemberShareFlag(queryMemberShareFlag(context, activityUseRespDTO));
        context.getDiscountFeeDetailDTOS().add(marketActivity);
        log.info("优惠活动计算后订单剩余金额：{}，优惠金额：{}，菜品优惠：{}", context.getCalculateOrderRespDTO().getOrderSurplusFee(), marketActivity
                .getDiscountFee(), JacksonUtils.writeValueAsString(context.getAllItems()));
    }

    @Override
    Integer type() {
        return DiscountTypeEnum.ACTIVITY.getCode();
    }


    /**
     * 处理营销活动 满减满折
     */
    public ResponseMarketActivityUse dealMarketActivity(DiscountContext context) {
        RequestMarketActivityUse req = new RequestMarketActivityUse();
        String activityGuid = context.getCalculateOrderDTO().getActivitySelectList().stream()
                .filter(s -> Objects.equals(DiscountTypeEnum.ACTIVITY.getCode(), s.getActivityType()))
                .map(ActivitySelectDTO::getActivityGuid)
                .findFirst()
                .orElse(null);
        req.setActivityGuid(activityGuid);
        req.setMemberInfoCardGuid(context.getUserMemberSession().getMemberInfoCardGuid());
        List<RequestDishInfo> dishInfoList = DineInItemHelper.dineInItem2DishList(context.getAllItems());
        // 设置商品父级信息
        itemService.setParentDishSkuInfo(dishInfoList);
        req.setDishInfoDTOList(MemberDishInfoMAP.INSTANCE.toMemberWechatDishDTOList(dishInfoList));
        req.setVolumeGuid(context.getVolumeGuid());
        req.setIsMemberDiscount(context.getUseMemberDiscountFlag());
        return marketingActivityHelper.calculateFullDiscountAndReduction(req);
    }

    /**
     * 设置满减满折活动是否与会员优惠共享
     */
    private boolean queryMemberShareFlag(DiscountContext context, ResponseMarketActivityUse activityUseRespDTO) {
        List<ResponseClientMarketActivity> activitiesList = activityUseRespDTO.getActivitiesList();
        if (CollectionUtils.isEmpty(activitiesList) || StringUtils.isEmpty(activityUseRespDTO.getActivityGuid())) {
            return true;
        }
        if (CollectionUtils.isEmpty(context.getCalculateOrderDTO().getActivitySelectList())) {
            log.warn("[没有选中活动信息]calculateOrderDTO={}", JacksonUtils.writeValueAsString(context.getCalculateOrderDTO()));
            return true;
        }
        String activityGuid = context.getCalculateOrderDTO().getActivitySelectList().stream()
                .filter(s -> Objects.equals(DiscountTypeEnum.ACTIVITY.getCode(), s.getActivityType()))
                .map(ActivitySelectDTO::getActivityGuid)
                .findFirst()
                .orElse(null);
        ResponseClientMarketActivity unShareActivity = activitiesList.stream()
                .filter(e -> e.getGuid().equals(activityGuid)
                        && e.getIsShare() == 1).findFirst().orElse(null);
        return Objects.isNull(unShareActivity);
    }

    /**
     * 替换不可使用原因
     */
    private void replaceActivityUnUseReason(List<ResponseClientMarketActivity> activitiesList) {
        for (ResponseClientMarketActivity activity : activitiesList) {
            if (StringUtils.isEmpty(activity.getUnUseReason())) {
                continue;
            }
            activity.setUnUseReason(MarketActivityUnableTipEnum.getView(activity.getUnUseReason()));
        }
    }
}
