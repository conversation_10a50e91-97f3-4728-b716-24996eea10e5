package com.holderzone.holder.saas.aggregation.merchant.controller.business;

import cn.hutool.core.collection.CollectionUtil;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.config.RedisLock;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.business.PaymentTypeClientService;
import com.holderzone.saas.store.dto.trade.*;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.locale.LocaleMessageEnum;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentTypeController
 * @date 2018/09/12 19:21
 * @description
 * @program holder-saas-aggregation-merchant
 */
@RestController
@RequestMapping("/payType")
@Slf4j
public class PaymentTypeController {

//    private static final log log = logFactory.getlog(PaymentTypeController.class);

    private final PaymentTypeClientService paymentTypeClientService;

    private final RedisLock redisLock;

    @Autowired
    public PaymentTypeController(PaymentTypeClientService paymentTypeClientService, RedisLock redisLock) {
        this.paymentTypeClientService = paymentTypeClientService;
        this.redisLock = redisLock;
    }

    @ApiOperation(value = "如果新增其他支付方式，sorting自动自增1", notes = "返回success 成功")
    @PostMapping("/add")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "新增支付方式")
    public Result<String> addPaymentType(@RequestBody PaymentTypeDTO paymentTypeDTO) {
        log.info("新增支付方式 paymentTypeDTO={}", JacksonUtils.writeValueAsString(paymentTypeDTO));
        String result = paymentTypeClientService.addPaymentType(paymentTypeDTO);
        if ("success".equals(result)) {
            return Result.buildSuccessResult(result);
        }
        return Result.buildFailResult(1, LocaleMessageEnum.getLocale(result));
    }

    @ApiOperation(value = "查看聚合支付方式详情")
    @PostMapping("/detail")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "查看聚合支付方式详情")
    public Result<PaymentTypeDTO> detail(@RequestBody PaymentTypeDTO paymentTypeDTO) {
        log.info("查询详情 paymentTypeDTO={}", JacksonUtils.writeValueAsString(paymentTypeDTO));
        PaymentTypeDTO typeDTO = paymentTypeClientService.getOne(paymentTypeDTO);
        return Result.buildSuccessResult(typeDTO);
    }

    @ApiOperation(value = "聚合支付配置")
    @PostMapping("/config")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "聚合支付配置")
    public Result<String> config(@RequestBody JHConfigDTO jhConfigDTO) {
        log.info("查询所有支付方式 jhConfigDTO={}", JacksonUtils.writeValueAsString(jhConfigDTO));
        String result = paymentTypeClientService.config(jhConfigDTO);
        if ("success".equals(result)) {
            return Result.buildSuccessResult(result);
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage("INCORRECT_PAYMENT_MERCHANT_KEY"));
    }

    @ApiOperation(value = "聚合支付解绑")
    @PostMapping("/unbind")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "聚合支付解绑")
    public Result<String> unbind(@RequestBody JHReqDTO jhReqDTO) {
        log.info("聚合支付解绑 jhConfigDTO={}", JacksonUtils.writeValueAsString(jhReqDTO));
        String result = paymentTypeClientService.unbind(jhReqDTO);
        if ("success".equals(result)) {
            return Result.buildSuccessResult(result);
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage("AGGREGATION_PAYMENT_UNTYING_FAILED"));
    }

    @ApiOperation(value = "查询所有支付方式")
    @PostMapping("/getAll")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "查询所有支付方式")
    public Result<List<PaymentTypeDTO>> getAll(@RequestBody PaymentTypeQueryDTO paymentTypeQueryDTO) {
        log.info("查询所有支付方式 paymentTypeQueryDTO={}", JacksonUtils.writeValueAsString(paymentTypeQueryDTO));
        List<PaymentTypeDTO> all = paymentTypeClientService.getAll(paymentTypeQueryDTO);
        if (CollectionUtil.isNotEmpty(all)) {
            all.forEach(p -> {
                String localeName = PaymentTypeEnum.PaymentType.getLocaleNameById(p.getPaymentType());
                if (StringUtils.isEmpty(localeName)) {
                    return;
                }
                p.setPaymentTypeName(localeName);
            });
        }
        return Result.buildSuccessResult(all);
    }

    @ApiOperation(value = "删除支付方式,传入guid，paymentType，storeGuid", notes = "返回success表示成功")
    @PostMapping("/delete")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "删除支付方式")
    public Result<String> deletePaymentType(@RequestBody PaymentTypeDTO paymentTypeDTO) {
        log.info("查询所有支付方式paymentTypeDTO={}", JacksonUtils.writeValueAsString(paymentTypeDTO));
        String result = paymentTypeClientService.deletePaymentType(paymentTypeDTO);
        if ("success".equals(result)) {
            return Result.buildSuccessResult(result);
        }
        return Result.buildFailResult(1, LocaleUtil.getMessage(Constants.DELETION_FAILED));
    }

    @ApiOperation(value = "支付方式排序", notes = "返回success表示成功，需要提交整个表单")
    @PostMapping("/sort")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "支付方式排序")
    public Result<String> sorting(@RequestBody List<PaymentTypeDTO> paymentTypeDTOS) {
        log.info("更新排序，paymentTypeDTOS={}", JacksonUtils.writeValueAsString(paymentTypeDTOS));
        String result = paymentTypeClientService.sorting(paymentTypeDTOS);
        if ("success".equals(result)) {
            return Result.buildSuccessResult(result);
        }
        return Result.buildOpFailedResult(result);
    }

    @ApiOperation(value = "支付方式更新", notes = "返回success表示成功")
    @PostMapping("/update")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "支付方式更新")
    public Result<String> updatePaymentType(@RequestBody PaymentTypeDTO paymentTypeDTO) {
        log.info("更新支付方式：paymentTypeDTO={}", JacksonUtils.writeValueAsString(paymentTypeDTO));
        String result = paymentTypeClientService.updatePaymentType(paymentTypeDTO);
        if ("success".equals(result)) {
            return Result.buildSuccessResult(result);
        }
        return Result.buildFailResult(1, LocaleMessageEnum.getLocale(result));
    }

    @ApiOperation(value = "根据门店Guid更新默认支付方式")
    @PostMapping("/init_payment_type/{storeGuid}")
    public Result<Boolean> initPaymentType(@PathVariable("storeGuid") String storeGuid) {
        return Result.buildSuccessResult(paymentTypeClientService.initPaymentType(storeGuid));
    }

    @ApiOperation(value = "查询系统默认支付方式枚举转换")
    @PostMapping("/get_payment_type_list")
    public Result<List<PaymentTypeEnumDTO>> getPaymentTypeList() {
        List<PaymentTypeEnumDTO> paymentTypeList = paymentTypeClientService.getPaymentTypeList();
        if (CollectionUtil.isNotEmpty(paymentTypeList)) {
            paymentTypeList.forEach(p -> p.setPaymentTypeName(PaymentTypeEnum.PaymentType.getLocaleNameById(p.getPaymentType())));
        }
        return Result.buildSuccessResult(paymentTypeList);
    }

    @ApiOperation(value = "修改食堂卡/会员卡的支付模式")
    @PostMapping("/edit_canteen_payment_type_mode")
    public Result<Boolean> editPaymentTypeMode(@RequestBody PaymentTypeModeDTO modeDTO) {
        return Result.buildSuccessResult(paymentTypeClientService.editPaymentTypeMode(modeDTO));
    }

    @PostMapping("/list/{storeGuid}")
    @ApiOperation(value = "根据门店guid查询支付方式列表")
    public Result<List<PaymentTypeDTO>> getAllTypeByStoreGuid(@PathVariable("storeGuid") String storeGuid) {
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid("6506431195651982337");
        UserContextUtils.put(userContext);
        return Result.buildSuccessResult(paymentTypeClientService.getAllTypeByStoreGuid(storeGuid));
    }

    /**
     * 聚合支付账户设置
     *
     * @param request 入参
     * @return boolean
     */
    @ApiOperation(value = "聚合支付账户设置")
    @PostMapping("/payment_account_save")
    public Result<Boolean> paymentAccountSave(@RequestBody PaymentAccountReqDTO request) {
        log.info("聚合支付账户设置 request={}", JacksonUtils.writeValueAsString(request));
        // 防重复提交
        boolean getLock;
        Boolean save = Boolean.FALSE;
        try {
            // 判断是否获取了锁
            getLock = redisLock.lock(JacksonUtils.writeValueAsString(request));
            if (getLock) {
                save = paymentTypeClientService.paymentAccountSave(request);
            }
        } finally {
            redisLock.delete(JacksonUtils.writeValueAsString(request));
        }
        if (getLock) {
            return Result.buildSuccessResult(save);
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVING_IN_PROGRESS));
        }
    }
}
