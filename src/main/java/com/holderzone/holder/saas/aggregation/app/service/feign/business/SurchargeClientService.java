package com.holderzone.holder.saas.aggregation.app.service.feign.business;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.business.manage.SurchargeConditionQuery;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeAggDTO;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeSyncDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReasonClientService
 * @date 2019/08/21 10:00
 * @description //TODO
 * @program holder-saas-store-business
 */
@Component
@FeignClient(name = "holder-saas-store-business", fallbackFactory = SurchargeClientService.DefaultFallBackFactory.class)
public interface SurchargeClientService {

    @PostMapping(value = "/surcharge/sync")
    SurchargeAggDTO sync(@RequestBody SurchargeSyncDTO surchargeDTO);

    @PostMapping("/surcharge/list_by_condition")
    @ApiOperation(value = "根据条件查询收费规则")
    List<SurchargeLinkDTO> listByCondition(@RequestBody SurchargeConditionQuery query);

    @Slf4j
    @Component
    class DefaultFallBackFactory implements FallbackFactory<SurchargeClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public SurchargeClientService create(Throwable throwable) {
            return new SurchargeClientService() {
                @Override
                public SurchargeAggDTO sync(SurchargeSyncDTO surchargeDTO) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public List<SurchargeLinkDTO> listByCondition(SurchargeConditionQuery query) {
                    log.error(HYSTRIX_PATTERN, "listByCondition", JacksonUtils.writeValueAsString(query),
                            throwable.getMessage());
                    throw new ServerException();
                }
            };
        }
    }
}
