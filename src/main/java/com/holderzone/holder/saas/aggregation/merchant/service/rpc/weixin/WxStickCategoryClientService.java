package com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.weixin.WxCategoryDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickModelClientService
 * @date 2019/03/13 15:30
 * @description 桌贴模板库clientService
 * @program holder-saas-store
 */
@Component
@FeignClient(name = "holder-saas-cloud-wechat", fallbackFactory = WxStickCategoryClientService.FallBackClass.class)
public interface WxStickCategoryClientService {

    @GetMapping("/category/merchantList")
    List<WxCategoryDTO> listCategory();

    @Component
    @Slf4j
    class FallBackClass implements FallbackFactory<WxStickCategoryClientService> {
        @Override
        public WxStickCategoryClientService create(Throwable throwable) {
            return new WxStickCategoryClientService() {
                private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";
                @Override
                public List<WxCategoryDTO> listCategory() {
                    if (log.isWarnEnabled()) {
                        log.warn(HYSTRIX_PATTERN, "order", null, ThrowableUtils.asString(throwable));
                    }
                    throwable.printStackTrace();
                    throw new BusinessException("调用云端异常！");
                }
            };
        }
    }
}
