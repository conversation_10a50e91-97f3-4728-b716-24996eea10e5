package com.holderzone.holder.saas.aggregation.merchant.entity.pojo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrganizeBO
 * @date 2018/10/17 下午7:57
 * @description //TODO
 * @program holder-saas-store-dish
 */
@Data
public class OrganizeBO implements Serializable {
    private String guid;
    private String name;
    // 组织类型。0=企业 1=品牌，2=区域，3=门店
    private Integer organizationType;
    private OrganizeBO brandOrEnterprise;
}
