package com.holderzone.holder.saas.aggregation.app.controller.print;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.takeout.TakeoutClientService;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeawayPrintController
 * @date 2018/10/16 15:05
 * @description
 * @program holder-saas-store-takeaway
 */
@RestController
@RequestMapping("/takeaway/print")
@Api(description = "外卖打印相关接口")
public class TakeoutPrintController {
    private static final Logger logger = LoggerFactory.getLogger(TakeoutPrintController.class);

    @Autowired
    private TakeoutClientService takeoutClientService;


    @ApiOperation(value = "打印账单", notes = "打印账单")
    @PostMapping(value = "/printBill", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY,description = "打印账单",action = OperatorType.SELECT)
    public Result print(@RequestBody @Validated TakeoutOrderDTO takeawayOrderDTO){
        logger.info("打印账单入参：{}", JacksonUtils.writeValueAsString(takeawayOrderDTO));
        String result = takeoutClientService.printBill(takeawayOrderDTO);
        if ("SUCCESS".equals(result)){
            return Result.buildEmptySuccess();
        }else
            return Result.buildOpFailedResult(result);

    }

    @ApiOperation(value = "打印后厨菜单", notes = "打印后厨菜单")
    @PostMapping(value = "/printKitchen", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY,description = "打印后厨菜单",action = OperatorType.SELECT)
    public Result printKitchen(@RequestBody @Validated TakeoutOrderDTO takeawayOrderDTO){
        logger.info("打印账单入参：{}", JacksonUtils.writeValueAsString(takeawayOrderDTO));
        String result = takeoutClientService.printKitchen(takeawayOrderDTO);
        if ("SUCCESS".equals(result)){
            return Result.buildEmptySuccess();
        }else
            return Result.buildOpFailedResult(result);
    }
}
