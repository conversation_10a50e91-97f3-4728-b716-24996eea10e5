package com.holderzone.holder.saas.aggregation.merchant.controller.report;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.AggregationAppService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.HsmMemberDataCenterClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.pay.StorePayClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.ReportClientService;
import com.holderzone.holder.saas.aggregation.merchant.util.MemberResult;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.dto.order.response.daily.OverviewRespDTO;
import com.holderzone.saas.store.dto.pay.QuickPayStatisticsReqDTO;
import com.holderzone.saas.store.dto.pay.QuickPayStatisticsRespDTO;
import com.holderzone.saas.store.dto.report.query.AggPayServiceChargeQueryDTO;
import com.holderzone.saas.store.dto.report.query.CloudPayConstituteQueryDTO;
import com.holderzone.saas.store.dto.report.query.PaymentConstituteQueryDTO;
import com.holderzone.saas.store.dto.report.query.TradeDetailQueryDTO;
import com.holderzone.saas.store.dto.report.resp.CloudPayConstituteRespDTO;
import com.holderzone.saas.store.dto.report.resp.GrouponTradeDetailRespDTO;
import com.holderzone.saas.store.dto.report.resp.PaymentConstituteRespDTO;
import com.holderzone.saas.store.dto.report.resp.TakeawayTradeDetailRespDTO;
import com.holderzone.saas.store.dto.trade.constant.PayPowerId;
import com.holderzone.saas.store.util.BigDecimalUtil;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 结算明细报表
 */
@Slf4j
@RestController
@RequestMapping("/report/trade/detail")
@RequiredArgsConstructor
public class TradeDetailController {

    private final ReportClientService reportClientService;
    private final AggregationAppService aggregationAppService;

    private final HsmMemberDataCenterClientService memberDataCenterClientService;

    private final StorePayClientService storePayClientService;

    @ApiOperation(value = "查询团购结算明细")
    @PostMapping("/groupon")
    public Result<Page<GrouponTradeDetailRespDTO>> pageGroupon(@RequestBody @Valid TradeDetailQueryDTO query) {
        return Result.buildSuccessResult(reportClientService.pageGroupon(query));
    }

    @ApiOperation(value = "查询外卖结算明细")
    @PostMapping("/takeaway")
    public Result<Page<TakeawayTradeDetailRespDTO>> pageTakeaway(@RequestBody @Valid TradeDetailQueryDTO query) {
        return Result.buildSuccessResult(reportClientService.pageTakeaway(query));
    }

    @ApiOperation(value = "导出团购结算明细")
    @PostMapping("/groupon/export")
    public Result<String> exportGroupon(@RequestBody @Valid TradeDetailQueryDTO query) {
        return Result.buildSuccessResult(reportClientService.exportGroupon(query));
    }

    @ApiOperation(value = "查询外卖结算明细")
    @PostMapping("/takeaway/export")
    public Result<String> exportTakeaway(@RequestBody @Valid TradeDetailQueryDTO query) {
        return Result.buildSuccessResult(reportClientService.exportTakeaway(query));
    }

    @ApiOperation(value = "收款统计")
    @PostMapping("/paymentStatistics")
    public Result<OverviewRespDTO> paymentStatistics(@RequestBody @Valid DailyReqDTO query) {
        if (log.isInfoEnabled()) {
            log.info("收款统计入参：{}", JacksonUtils.writeValueAsString(query));
        }
        Result<OverviewRespDTO> overviewRespDTOResult = aggregationAppService.overview(query);
        // 根据条件查询会员余额支付成分
        handleMemberConstitute(query, overviewRespDTOResult);
        // 云收款统计
        handleCloudPayConstitute(query, overviewRespDTOResult);
        // 处理快速收款数据
        handleQuickPayStatistics(query, overviewRespDTOResult);
        // 处理聚合支付手续费
        handleAggPayServiceCharge(query, overviewRespDTOResult);
        return overviewRespDTOResult;
    }

    /**
     * 处理聚合支付手续费
     */
    private void handleAggPayServiceCharge(DailyReqDTO query,
                                           Result<OverviewRespDTO> overviewRespDTOResult) {
        AggPayServiceChargeQueryDTO queryDTO = new AggPayServiceChargeQueryDTO();
        queryDTO.setStartTime(DateTimeUtils.string2LocalDateTime(query.getBeginTime()));
        queryDTO.setEndTime(DateTimeUtils.string2LocalDateTime(query.getEndTime()));
        queryDTO.setStoreGuidList(query.getStoreGuids());
        BigDecimal aggPayServiceCharge = reportClientService.queryAggPayServiceCharge(queryDTO);
        if (BigDecimalUtil.greaterThanZero(aggPayServiceCharge)) {
            OverviewRespDTO overviewRespDTO = overviewRespDTOResult.getTData();
            if (!ObjectUtils.isEmpty(overviewRespDTO)) {
                AmountItemDTO aggPayAmountItemDTO = overviewRespDTO.getGatherItems().stream()
                        .filter(g -> 2 == g.getCode())
                        .findFirst()
                        .orElse(null);
                if (!ObjectUtils.isEmpty(aggPayAmountItemDTO)) {
                    aggPayAmountItemDTO.setEstimatedAmount(aggPayAmountItemDTO.getEstimatedAmount().subtract(aggPayServiceCharge));
                    overviewRespDTO.setEstimatedAmount(overviewRespDTO.getEstimatedAmount().subtract(aggPayServiceCharge));
                }
            }
        }
    }

    private void handleQuickPayStatistics(DailyReqDTO query, Result<OverviewRespDTO> overviewRespDTOResult) {
        OverviewRespDTO overviewRespDTO = overviewRespDTOResult.getTData();
        QuickPayStatisticsReqDTO quickPaReqDTO = new QuickPayStatisticsReqDTO();
        quickPaReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        quickPaReqDTO.setStartDateTime(DateTimeUtils.string2LocalDateTime(query.getBeginTime()));
        quickPaReqDTO.setEndDateTime(DateTimeUtils.string2LocalDateTime(query.getEndTime()));
        quickPaReqDTO.setStoreGuidList(query.getStoreGuids());
        List<QuickPayStatisticsRespDTO> quickPayList = storePayClientService.queryQuickPayStatistics(quickPaReqDTO);
        if (!CollectionUtils.isEmpty(quickPayList)) {
            BigDecimal quickPayAmountTotal = quickPayList.stream()
                    .map(QuickPayStatisticsRespDTO::getAmountTotal)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            overviewRespDTO.setConsumerAmount(overviewRespDTO.getConsumerAmount().add(quickPayAmountTotal));
            overviewRespDTO.setEstimatedAmount(overviewRespDTO.getEstimatedAmount().add(quickPayAmountTotal));
            overviewRespDTO.setGatherAmount(overviewRespDTO.getGatherAmount().add(quickPayAmountTotal));
            Map<Integer, List<QuickPayStatisticsRespDTO>> payTypeQuickMap = quickPayList.stream()
                    .collect(Collectors.groupingBy(q -> {
                        if (Objects.equals(q.getPayPowerId(), PayPowerId.MEMBER_PAY.getId())) {
                            return 4;
                        }
                        return 2;
                    }));
            if (CollectionUtils.isEmpty(overviewRespDTO.getGatherItems())) {
                emptyAddQuickPay(payTypeQuickMap, overviewRespDTO);
            } else {
                Map<Integer, AmountItemDTO> amountItemDTOMap = overviewRespDTO.getGatherItems().stream()
                        .collect(Collectors.toMap(AmountItemDTO::getCode, Function.identity(), (v1, v2) -> v1));
                payTypeQuickMap.forEach((code, payTypeQuick) -> {
                    AmountItemDTO amountItemDTO = amountItemDTOMap.get(code);
                    if (ObjectUtils.isEmpty(amountItemDTO)) {
                        addNewQuickAmountItemDTO(code, payTypeQuick, overviewRespDTO);
                        return;
                    }
                    BigDecimal quickPayAmount = payTypeQuick.stream()
                            .map(QuickPayStatisticsRespDTO::getAmountTotal)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    amountItemDTO.setAmount(BigDecimalUtil.nonNullValue(amountItemDTO.getAmount()).add(quickPayAmount));
                    amountItemDTO.setEstimatedAmount(BigDecimalUtil.nonNullValue(amountItemDTO.getEstimatedAmount()).add(quickPayAmount));
                });
            }

            Integer quickPayOrderCount = quickPayList.stream()
                    .map(QuickPayStatisticsRespDTO::getOrderCount)
                    .filter(Objects::nonNull)
                    .reduce(0, Integer::sum);
            overviewRespDTO.setOrderCount(overviewRespDTO.getOrderCount() + quickPayOrderCount);

            Integer quickPayGuestCount = quickPayList.stream()
                    .map(QuickPayStatisticsRespDTO::getGuestCount)
                    .filter(Objects::nonNull)
                    .reduce(0, Integer::sum);
            overviewRespDTO.setGuestCount(overviewRespDTO.getGuestCount() + quickPayGuestCount);
        }
    }

    private void addNewQuickAmountItemDTO(Integer code,
                                          List<QuickPayStatisticsRespDTO> payTypeQuick,
                                          OverviewRespDTO overviewRespDTO) {
        AmountItemDTO itemDTO = new AmountItemDTO();
        itemDTO.setCode(code);
        if (code == 4) {
            itemDTO.setName("会员充值余额支付");
        } else {
            itemDTO.setName("聚合支付");
        }
        BigDecimal quickPayAmount = payTypeQuick.stream()
                .map(QuickPayStatisticsRespDTO::getAmountTotal)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        itemDTO.setAmount(quickPayAmount);
        itemDTO.setEstimatedAmount(quickPayAmount);
        overviewRespDTO.getGatherItems().add(itemDTO);
    }

    private void emptyAddQuickPay(Map<Integer, List<QuickPayStatisticsRespDTO>> payTypeQuickMap,
                                  OverviewRespDTO overviewRespDTO) {
        if (!CollectionUtils.isEmpty(payTypeQuickMap)) {
            payTypeQuickMap.forEach((code, payTypeQuick) -> {
                addNewQuickAmountItemDTO(code, payTypeQuick, overviewRespDTO);
            });
        }
    }

    /**
     * 云收款统计
     */
    private void handleCloudPayConstitute(DailyReqDTO query, Result<OverviewRespDTO> overviewRespDTOResult) {
        CloudPayConstituteQueryDTO queryDTO = new CloudPayConstituteQueryDTO();
        queryDTO.setStartTime(DateTimeUtils.string2LocalDateTime(query.getBeginTime()));
        queryDTO.setEndTime(DateTimeUtils.string2LocalDateTime(query.getEndTime()));
        queryDTO.setStoreGuidList(query.getStoreGuids());
        CloudPayConstituteRespDTO cloudedPay = reportClientService.cloudPayConstitute(queryDTO);
        if (!ObjectUtils.isEmpty(cloudedPay) && BigDecimalUtil.greaterThanZero(cloudedPay.getTotalAmount())) {
            OverviewRespDTO overviewRespDTO = overviewRespDTOResult.getTData();
            if (!ObjectUtils.isEmpty(overviewRespDTO)) {
                AmountItemDTO itemDTO = new AmountItemDTO();
                itemDTO.setCode(cloudedPay.getPayCode());
                itemDTO.setName(cloudedPay.getPayMethod());
                itemDTO.setAmount(cloudedPay.getTotalAmount());
                // 商家预计应得金额不做处理
                overviewRespDTO.getGatherItems().add(itemDTO);
                overviewRespDTO.setGatherAmount(overviewRespDTO.getGatherAmount().add(cloudedPay.getTotalAmount()));
                overviewRespDTO.setConsumerAmount(overviewRespDTO.getConsumerAmount().add(cloudedPay.getTotalAmount()));
            }
        }
    }

    /**
     * 根据条件查询会员余额支付成分
     */
    private void handleMemberConstitute(DailyReqDTO query, Result<OverviewRespDTO> overviewRespDTOResult) {
        MemberResult<List<AmountItemDTO>> memberResult = memberDataCenterClientService.queryMemberPayConstitute(query);
        List<AmountItemDTO> memberPayConstitute = memberResult.getTData();
        log.info("[根据条件查询会员余额支付成分]memberPayConstitute={}", JacksonUtils.writeValueAsString(memberPayConstitute));
        if (!CollectionUtils.isEmpty(memberPayConstitute)) {
            memberPayConstitute.removeIf(c -> BigDecimalUtil.equelZero(c.getAmount()));
            OverviewRespDTO overviewRespDTO = overviewRespDTOResult.getTData();
            if (!ObjectUtils.isEmpty(overviewRespDTO)) {
                overviewRespDTO.getGatherItems().removeIf(g -> BigDecimalUtil.greaterThanZero(g.getAmount()) && g.getCode() == 4);
                overviewRespDTO.getGatherItems().addAll(memberPayConstitute);
            }
        }
    }

    @ApiOperation(value = "收款构成")
    @PostMapping("/payment/constitute")
    public Result<Page<PaymentConstituteRespDTO>> paymentConstitute(@RequestBody @Valid PaymentConstituteQueryDTO query) {
        return Result.buildSuccessResult(reportClientService.paymentConstitute(query));
    }

    @ApiOperation(value = "收款构成导出")
    @PostMapping("/payment/constitute/export")
    public Result<String> paymentConstituteExport(@RequestBody @Valid PaymentConstituteQueryDTO query) {
        return Result.buildSuccessResult(reportClientService.paymentConstituteExport(query));
    }
}
