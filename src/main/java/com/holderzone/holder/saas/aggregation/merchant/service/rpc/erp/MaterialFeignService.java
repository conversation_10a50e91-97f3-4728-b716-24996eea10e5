package com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/05 10:20
 */
@Component
@FeignClient("holder-saas-store-erp")
public interface MaterialFeignService {
    /**
     * 添加物料信息
     */
    @PostMapping("material")
    Boolean add(@RequestBody @Validated MaterialDTO materialDTO);

    /**
     * 修改物料信息
     */
    @PutMapping("material")
    Boolean update(@RequestBody MaterialDTO materialDTO);

    /**
     * 条件查询物料信息
     */
    @PostMapping("material/findByCondition")
    Page<MaterialDTO> findByCondition(@RequestBody MaterialQueryDTO queryDTO);

    /**
     * 根据GUID查询物料信息
     */
    @GetMapping("material/{guid}")
    MaterialDTO findByGuid(@PathVariable("guid") String guid);

    /**
     * 根据GUID查询物料配置的bom数量
     */
    @GetMapping("material/countBom/{materialGuid}")
    Long countMaterialBom(@PathVariable("materialGuid") String materialGuid);

    /**
     * 根据GUID删除物料信息
     */
    @DeleteMapping("material/{guid}")
    Boolean delete(@PathVariable("guid") String guid);

    @PutMapping("material/changeStatus")
    /**启用禁用物料信息*/
    Boolean changeStatus(@RequestBody MaterialDTO materialDTO);

    /**
     * 根据物料GUID列表查询物料信息
     *
     * @param materialListQuery
     * @return
     */
    @PostMapping("material/findByGuidList")
    List<MaterialDTO> findList(MaterialListQuery materialListQuery);

    @PostMapping("material/findListByCondition")
    /**条件查询物料信息不分页*/
    List<MaterialDTO> findListByCondition(@RequestBody MaterialQueryDTO queryDTO);

    @GetMapping("material/generatorCode")
    /**生成物料code*/
    String generatorCode();

    @PostMapping("material/batch")
    /**批量添加物料信息*/
    Boolean batchAdd(@RequestBody List<MaterialDTO> materialDTOList);

    /**
     * 根据条件查询库存列表
     */
    @PostMapping("stock/findByCondition")
    Page<MaterialDTO> findStockByCondition(@RequestBody StockQueryDTO stockQueryDTO);

    @PostMapping("stock/listByCondition")
    @ApiOperation("条件查询库存列表")
    List<MaterialDTO> listByCondition(@RequestBody StockQueryDTO stockQueryDTO);

    /**
     * 物料消耗汇总
     */
    @PostMapping("material/materialConsumeSum")
    Page<MaterialConsumeRespDTO> materialConsumeSum(@RequestBody MaterialConsumeReqDTO materialConsumeReqDTO);

}
