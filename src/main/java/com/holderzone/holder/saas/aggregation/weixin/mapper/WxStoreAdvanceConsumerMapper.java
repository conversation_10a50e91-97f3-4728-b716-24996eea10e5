package com.holderzone.holder.saas.aggregation.weixin.mapper;

import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

@Component
@Mapper(componentModel = "spring")
public interface WxStoreAdvanceConsumerMapper {

	@Mappings({
			@Mapping(target = "orderModel",defaultValue = "0")
	})
	WxStoreAdvanceConsumerReqDTO getWxStoreAdvanceConsumerReq(WxStoreConsumerDTO wxStoreConsumerDTO);
}
