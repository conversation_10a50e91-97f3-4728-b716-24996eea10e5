package com.holderzone.holder.saas.aggregation.weixin.controller.deal;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dynamic.datasource.starter.utils.JacksonUtil;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.weixin.assembler.WxOrderInfoRespAssembler;
import com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel;
import com.holderzone.holder.saas.aggregation.weixin.service.WxStoreTradeOrderService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.*;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.aggregation.weixin.utils.UserMemberSessionUtils;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ConsumptionGiftDetailDTO;
import com.holderzone.holder.saas.weixin.entry.dto.OrderDetailDTO;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.entry.dto.req.WxOrderDetailReqDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.holder.saas.weixin.utils.WxMemberSessionUtil;
import com.holderzone.saas.store.dto.order.inside.OrderGuidsDTO;
import com.holderzone.saas.store.dto.order.request.dinein.DineInOrderListReqDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.trade.OrderDetailPushMqDTO;
import com.holderzone.saas.store.dto.trade.OrderInfoRespDTO;
import com.holderzone.saas.store.dto.weixin.WxOrderInfoRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderDTO;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberSessionDTO;
import com.holderzone.saas.store.dto.weixin.deal.WechatOrderInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Api(value = "微信订单详情")
@Slf4j
@RestController
@RequestMapping("/deal/order")
public class OrderingDetailController {
    @Resource
    WxStoreMenuDetailsClientService wxStoreMenuDetailsClientService;
    @Resource
    WxOrderRecordClientService wxOrderRecordClientService;

    @Resource
    private WxQueueClientService wxQueueClientService;

    //    @Resource
//    WeChatClientService weChatClientService;
//    @Resource
//    MemberBaseClientService memberBaseClientService;
    @Resource
    UserMemberSessionUtils userMemberSessionUtils;
    @Resource
    HsaBaseClientService hsaBaseClientService;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private TradeOrderService tradeOrderService;
    @Resource
    private WxStoreMerchantOrderClientService merchantOrderClientService;
    @Resource
    private MemberMarketingClientService memberMarketingClientService;
    @Resource
    private WxStoreTradeOrderService wxStoreTradeOrderService;

    @ApiOperation(value = "根据订单guid获取订单信息，订单未支付时不包含结算信息", notes = "订单详情")
    @GetMapping("/detail/{guid}")
    public Result<OrderDetailDTO> detail(@PathVariable("guid") String guid) {
        if (StringUtils.isEmpty(guid) || "null".equals(guid)) {
            return Result.buildOpFailedResult("查询的订单编号错误");
        }
        WxOrderDetailReqDTO orderDetailDTO = new WxOrderDetailReqDTO();
        orderDetailDTO.setGuid(guid);
        log.info("线程中的缓存数据：{}", JacksonUtil.writeValueAsString(WeixinUserThreadLocal.get()));
        WxMemberSessionDTO memberByOpenId = WeixinUserThreadLocal.getOpenId() == null ? null : WxMemberSessionUtil.getMemberByOpenId(redisUtils, WeixinUserThreadLocal.getOpenId());
        boolean isLogin = memberByOpenId == null ? WeixinUserThreadLocal.getIsLogin() : memberByOpenId.getWxUserInfoDTO().getIsLogin();
        if (isLogin) {
            setMemberInfo(orderDetailDTO);
        }
        OrderDetailDTO orderDetail = wxOrderRecordClientService.getOrderDetail(orderDetailDTO);
        if (orderDetail == null) {
            return Result.buildOpFailedResult("数据已过期");
        }
        log.info("根据订单guid获取订单信息返回参数:{}", JacksonUtil.writeValueAsString(orderDetail));
        return Result.buildSuccessResult(orderDetail);
    }


    @GetMapping("/get/order/gift")
    public Result<ConsumptionGiftDetailDTO> getOrderGift(@RequestParam("orderNumber") String orderNumber, @RequestParam("orderHolderNo") String orderHolderNo) {
        log.info("根据订单编号获取订单消费赠送orderNumber={}orderHolderNo={},", orderNumber, orderHolderNo);
        ConsumptionGiftDetailDTO consumptionGiftDetailDTO = null;
        if (!StringUtils.isEmpty(orderNumber)) {
            consumptionGiftDetailDTO = memberMarketingClientService.getOrderGift(orderNumber);
        }
        log.info("根据订单编号获取订单消费赠送返回参数:{}", JacksonUtil.writeValueAsString(consumptionGiftDetailDTO));
        return Result.buildSuccessResult(consumptionGiftDetailDTO);
    }


    private void setMemberInfo(WxOrderDetailReqDTO orderDetailDTO) {
        UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
        if (Objects.isNull(userMemberSession)) {
            return;
        }
        String memberInfoCardGuid = userMemberSession.getMemberInfoCardGuid();
        if (memberInfoCardGuid != null && memberInfoCardGuid.length() > 5) {
            try {
                UserContext userContext = UserContextUtils.get();
                if (StringUtils.isEmpty(userContext.getOperSubjectGuid())) {
                    WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
                    userContext.setOperSubjectGuid(wxMemberSessionDTO.getOperSubjectGuid());
                }
                ResponseModel<Boolean> responseModel = hsaBaseClientService.hasMemberPrice(memberInfoCardGuid);
                boolean hasMemberPrice = responseModel != null && responseModel.getData() != null && responseModel.getData();
                log.info("查询会员价返回参数:{}", hasMemberPrice);
                orderDetailDTO.setEnableMemberPrice(hasMemberPrice);
                orderDetailDTO.setMemberInfoCardGuid(memberInfoCardGuid);
                if (userMemberSession.getMemberIntegral() != null) {
                    orderDetailDTO.setMemberIntegral(userMemberSession.getMemberIntegral().equals(1) ? 1 : 2);
                }
            } catch (Exception e) {
                log.error("memberBaseClientService.hasMemberPrice异常", e);
            }
        }
        if (orderDetailDTO.getEnableMemberPrice() == null) {
            orderDetailDTO.setEnableMemberPrice(false);
        }
    }

//    @Resource
//    WxHsmMemberBasicService wxHsmMemberBasicService;

    @ApiOperation(value = "根据订单guid获取订单信息，包含结算信息", notes = "买单支付页调用，已支付的订单无法调用")
    @GetMapping("/detail/settlement/{guid}")
    public Result<OrderDetailDTO> detailSettement(@PathVariable("guid") String guid) {
        WxOrderDetailReqDTO orderDetailDTO = new WxOrderDetailReqDTO();
        orderDetailDTO.setGuid(guid);
        boolean isLogin = WeixinUserThreadLocal.getIsLogin();
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        log.info("结算页面获取登录信息 wxMemberSessionDTO={} isLogin={}", JacksonUtils.writeValueAsString(wxMemberSessionDTO), isLogin);
        String memberInfoCardGuid = null;
        String volumeCode = null;
        if (isLogin) {
            UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
            //优惠券code,-1：不使用，0：请选择，
            //会员持卡GUID,-1:不使用，0：请选择
            if (userMemberSession != null) {
                volumeCode = userMemberSession.getVolumeCode();
                memberInfoCardGuid = userMemberSession.getMemberInfoCardGuid();
            }
            if (memberInfoCardGuid != null && memberInfoCardGuid.length() > 5) {
                boolean hasMemberPrice = false;
                try {
//                    hasMemberPrice = memberBaseClientService.hasMemberPrice(memberInfoCardGuid);
                    hasMemberPrice = hsaBaseClientService.hasMemberPrice(memberInfoCardGuid).getData();
                    log.info("是否支持会员价{}", hasMemberPrice);
                    orderDetailDTO.setEnableMemberPrice(hasMemberPrice);
                    orderDetailDTO.setMemberInfoCardGuid(memberInfoCardGuid);
                    if (userMemberSession.getMemberIntegral() != null) {
                        orderDetailDTO.setMemberIntegral(userMemberSession.getMemberIntegral().equals(1) ? 1 : 2);
                    }
                } catch (Exception e) {
                    log.error("订单guid获取订单信息异常", e);
                }

            }
            if (volumeCode != null && volumeCode.length() > 5) {
                orderDetailDTO.setVolumeCode(volumeCode);
            }
            orderDetailDTO.setMemberLogin(1);
        } else {
            orderDetailDTO.setMemberLogin(2);
        }
        if ("-1".equals(volumeCode)) {
            orderDetailDTO.setVolumeCode(null);
        }
        if ("-1".equals(memberInfoCardGuid)) {
            orderDetailDTO.setMemberInfoCardGuid(null);
        }
        Result<OrderDetailDTO> orderDetailResult = wxOrderRecordClientService.detailCalculate(orderDetailDTO);
        if (orderDetailResult.getCode() != 0) {
            return orderDetailResult;
        }
        OrderDetailDTO orderDetail = orderDetailResult.getTData();
        if (orderDetail == null) {
            return Result.buildOpFailedResult("该订单不存在");
        }
        ////优惠券code,-1：不使用，0：请选择，
        ////会员持卡GUID,-1:不使用，0：请选择

        orderDetail.setMemberCardSelect(isLogin && memberInfoCardGuid != null && memberInfoCardGuid.length() > 5);
        if (orderDetail.getMemberCardSelect()) {
            orderDetail.setMemberCardGuid(memberInfoCardGuid);
        }
        if (orderDetail.getCouponSelect() == null) {
            if (orderDetail.getCouponFee() != null && orderDetail.getCouponFee().compareTo(BigDecimal.ZERO) > 0) {
                orderDetail.setCouponSelect(true);
            } else {
                orderDetail.setCouponSelect(isLogin && volumeCode != null && volumeCode.length() > 5);
            }
        }
        if (orderDetail.getCouponSelect()) {
            if (volumeCode == null || volumeCode.length() < 5) {
                orderDetail.setCouponId("1");
            } else {
                orderDetail.setCouponId(volumeCode);
            }
        }
        return orderDetailResult;
    }


    @ApiOperation("进入买单订单状态检查,code=51001存在待处理批次")
    @GetMapping(value = "/settlement/check/{guid}")
    public Result detailCalculateCheck(@PathVariable("guid") String guid) {
        WxOrderDetailReqDTO orderDetailDTO = new WxOrderDetailReqDTO();
        orderDetailDTO.setGuid(guid);
        //订单批次状态检查 正餐
        return wxOrderRecordClientService.detailCalculateCheck(orderDetailDTO);

    }


	/*@ApiOperation("支付时订单状态检查,code=51001存在待处理批次")
	@GetMapping(value = "/prepay/check/{guid}")
	public Result detailPayCheck(@PathVariable("guid")String guid)  {
		WxOrderDetailReqDTO orderDetailDTO = new WxOrderDetailReqDTO();
		orderDetailDTO.setGuid(guid);
		//订单批次状态检查 正餐
		return wxOrderRecordClientService.detailCalculateCheck(orderDetailDTO);

	}*/

    /**
     * 获取订单详情
     *
     * @param orderGuid 订单Guid
     * @return 订单详情
     */
    @ApiOperation(value = "获取订单详情", notes = "获取订单详情")
    @GetMapping("/find_by_order_guid")
    public Result<OrderDTO> findByOrderGuid(@RequestParam("orderGuid") String orderGuid) {
        log.info("获取订单详情 orderGuid={}", orderGuid);
        return Result.buildSuccessResult(tradeOrderService.findByOrderGuid(orderGuid));
    }

    /**
     * 根据订单记录id获取订单详情
     *
     * @param orderRecordGuid 微信订单记录guid
     * @return 订单详情
     */
    @ApiOperation("根据订单记录id获取订单详情")
    @GetMapping("/get_detail_by_order_record_guid")
    public Result<List<WxStoreMerchantOrderDTO>> getDetailByOrderRecordGuid(@RequestParam("orderRecordGuid") String orderRecordGuid) {
        log.info("根据订单记录id获取订单详情，orderRecordGuid={}", orderRecordGuid);
        return Result.buildSuccessResult(merchantOrderClientService.getDetailByOrderRecordGuid(orderRecordGuid));
    }

    /**
     * 根据订单Guid查询微信订单金额（接单&未接单）
     *
     * @param orderGuid 订单guid
     * @return 微信订单金额（接单&未接单）
     */
    @ApiOperation(value = "根据订单查询订单金额")
    @GetMapping("/get_wechat_order_fee_by_order_guid")
    public Result<BigDecimal> getWechatOrderFeeByOrderGuid(@RequestParam("orderGuid") String orderGuid) {
        log.info("根据订单查询订单金额，orderGuid={}", orderGuid);
        return Result.buildSuccessResult(merchantOrderClientService.getWechatOrderFeeByOrderGuid(orderGuid));
    }

    /**
     * 根据订单Guid查询微信订单信息
     *
     * @param orderGuid 订单guid
     * @return 微信订单信息
     */
    @ApiOperation(value = "根据订单Guid查询微信订单信息")
    @GetMapping("/get_wechat_order_info_by_order_guid")
    public Result<WechatOrderInfoDTO> getWechatOrderInfoByOrderGuid(@RequestParam("orderGuid") String orderGuid) {
        log.info("根据订单Guid查询微信订单信息，orderGuid={}", orderGuid);
        return Result.buildSuccessResult(merchantOrderClientService.getWechatOrderInfoByOrderGuid(orderGuid));
    }

    /**
     * 根据Guid查询微信订单信息
     *
     * @param guid 订单guid/订单记录guid
     * @return 微信订单信息
     */
    @ApiOperation(value = "根据Guid查询微信订单信息")
    @GetMapping("/get_wechat_order_info_by_guid")
    public Result<WechatOrderInfoDTO> getWechatOrderInfoByGuid(@RequestParam("guid") String guid) {
        log.info("根据Guid查询微信订单信息，guid={}", guid);
        return Result.buildSuccessResult(merchantOrderClientService.getWechatOrderInfoByGuid(guid));
    }

    /**
     * 小程序获取订单列表
     */
    @ApiOperation(value = "小程序获取订单列表")
    @PostMapping("/list")
    public Result<Page<WxOrderInfoRespDTO>> pageAppletOrderInfo(@RequestBody DineInOrderListReqDTO reqDTO) {
        log.info("小程序获取订单列表入参:{}", JacksonUtils.writeValueAsString(reqDTO));
        Page<OrderInfoRespDTO> page = tradeOrderService.pageAppletOrderInfo(reqDTO);
        List<OrderInfoRespDTO> data = page.getData();
        if (CollectionUtils.isEmpty(data)) {
            return Result.buildSuccessResult(new Page<>());
        }
        return Result.buildSuccessResult(new Page<>(reqDTO.getCurrentPage(), reqDTO.getPageSize(), page.getTotalCount(),
                WxOrderInfoRespAssembler.copyOrderInfoResp(data)));
    }


    /**
     * 小程序获取订单详情
     */
    @ApiOperation(value = "小程序获取订单详情", notes = "小程序获取订单详情")
    @PostMapping("/get")
    public Result<OrderDetailPushMqDTO> findByAppletOrderGuid(@RequestBody OrderGuidsDTO orderGuidsDTO) {
        return Result.buildSuccessResult(wxStoreTradeOrderService.findByAppletOrderGuid(orderGuidsDTO));
    }
}
