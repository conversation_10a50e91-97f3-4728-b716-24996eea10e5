package com.holderzone.holder.saas.aggregation.app.controller.business;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.business.ManageClientService;
import com.holderzone.saas.store.dto.business.manage.ScreenAppRespDTO;
import com.holderzone.saas.store.dto.business.manage.ScreenPicConfigReqDTO;
import com.holderzone.saas.store.dto.business.manage.ScreenPicRespDTO;
import com.holderzone.saas.store.dto.business.manage.ScreenPictureConfigDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreeanPicController
 * @date 2018/11/19 14:10
 * @description
 * @program holder-saas-aggregation-app
 */
@Api("副屏图片接口")
@RestController
@RequestMapping("/pic")
public class ScreenPicController {

    private static final Logger logger = LoggerFactory.getLogger(ScreenPicController.class);

    private final ManageClientService manageClientService;

    @Autowired
    public ScreenPicController(ManageClientService manageClientService) {
        this.manageClientService = manageClientService;
    }

    @ApiOperation(value = "查询门店附屏图片")
    @PostMapping("/{storeGuid}")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "查询门店附屏图片",action = OperatorType.SELECT)
    public Result<List<ScreenAppRespDTO>> query(@PathVariable("storeGuid") String storeGuid) {
        logger.info("查询门店附屏图片 storeGuid={}", storeGuid);
        return Result.buildSuccessResult(manageClientService.query(storeGuid));
    }

    @ApiOperation(value = "查询门店附屏配置")
    @PostMapping("/query_store_config")
    public Result<ScreenPictureConfigDTO> queryStoreConfig(@RequestBody ScreenPicConfigReqDTO query) {
        logger.info("[查询门店附屏配置]query={}", JacksonUtils.writeValueAsString(query));
        return Result.buildSuccessResult(manageClientService.queryStoreConfig(query));
    }

}
