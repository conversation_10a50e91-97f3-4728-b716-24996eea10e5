package com.holderzone.holder.saas.aggregation.merchant.controller.media;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.FileUploadService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.media.CloudMediaServiceClient;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.media.MediaServiceClient;
import com.holderzone.holder.saas.aggregation.merchant.util.UploadValidateUtil;
import com.holderzone.holder.saas.store.media.dto.entity.*;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@Api(value = "多媒体", description = "多媒体")
@RequestMapping("/media")
@Slf4j
public class MediaController {

    private final MediaServiceClient mediaServiceClient;

    private final FileUploadService fileUploadService;

    private final CloudMediaServiceClient cloudMediaServiceClient;

    public MediaController(MediaServiceClient mediaServiceClient, FileUploadService fileUploadService,
                           CloudMediaServiceClient cloudMediaServiceClient) {
        this.mediaServiceClient = mediaServiceClient;
        this.fileUploadService = fileUploadService;
        this.cloudMediaServiceClient = cloudMediaServiceClient;
    }

    @ApiOperation(value = "批量新建接口")
    @PostMapping("batch/create_file")
    public Result<List<FileCreateRespDTO>> batchCreateFile(@RequestBody List<MediaDTO> hsmMediaList) {
        log.info("批量新建接口入参：{}", JacksonUtils.writeValueAsString(hsmMediaList));
        List<FileCreateRespDTO> data = mediaServiceClient.batchCreateFile(hsmMediaList);
        if (ObjectUtils.isEmpty(data)) {
            return Result.buildFailResult(-1, "批量新建失败");
        } else {
            return Result.buildSuccessResult(data);
        }
    }

    @ApiOperation(value = "新建接口")
    @PostMapping("/create_file")
    public Result<FileCreateRespDTO> createFile(@RequestBody MediaDTO mediaDTO) {
        log.info("新建接口入参：{}", JacksonUtils.writeValueAsString(mediaDTO));
        FileCreateRespDTO data = mediaServiceClient.createFile(mediaDTO);
        if (ObjectUtils.isEmpty(data)) {
            return Result.buildFailResult(-1, "新建失败");
        } else {
            return Result.buildSuccessResult(data);
        }
    }

    @ApiOperation(value = "文件上传接口,文件上传name 必须为file, 不传入type或者为空表示不作处理",
            notes = "单个图片大小不能超过20M，上传成功返回图片的地址，失败返回空，w*h type=1表示1080*1920,大小为20M," +
                    "type=3表示1024*600,大小为20M,type=4表示635*600,大小为20M," +
                    "type=2表示800*800,大小为0.5M,type=5表示1920*1080,大小为20M," +
                    "type=6表示650*690，大小为20M")
    @PostMapping("/upload")
    public Result<MediaDTO> upload(@RequestParam(value = "file") MultipartFile file, @RequestParam(value = "type", required = false)
            Integer type) {
        log.info("文件上传， fileName={},type={}", file.getOriginalFilename(), type);
        // 仅校验商品图片
        String fileName = file.getOriginalFilename();
        String fileType = fileName != null && fileName.contains(".") ?
                fileName.substring(fileName.lastIndexOf(".") + 1) : null;
        if (UploadValidateUtil.typeNotValidate(fileType)) {
            log.info("当前文件类型为：{}", fileType);
            throw new BusinessException("文件格式必须为 jpg/png/bmp/jpeg/gif 格式！！！");
        }

        if (file.getSize() / 1024 >= 500) { // 判断图片是否超过限制
            throw new BusinessException("文件大小超过限制");
        }
        MediaDTO data = fileUploadService.uploadForMedia(file);
        if (data == null) {
            return Result.buildFailResult(-1, "上传失败");
        } else {
            return Result.buildSuccessResult(data);
        }
    }

    @ApiOperation(value = "文件上传接口,文件上传name 必须为file",
            notes = "单个图片大小不能超过3M，上传成功返回图片的地址，失败返回空")
    @PostMapping("/batch/upload")
    public Result<List<MediaDTO>> upload(@RequestParam(value = "file") List<MultipartFile> fileList) {
        log.info("文件批量上传");
        List<MediaDTO> urls = fileList.stream()
                .map(file -> fileUploadService.uploadForMedia(file))
                .collect(Collectors.toList());
        return Result.buildSuccessResult(urls);
    }

    @ApiOperation(value = "删除文件")
    @PostMapping("/delete")
    public Result delete(@RequestBody List<FileDeleteDTO> fileDeleteDTOList) {
        log.info("删除文件入参:{}", JacksonUtils.writeValueAsString(fileDeleteDTOList));
        mediaServiceClient.delete(fileDeleteDTOList);
        return Result.buildSuccessResult(LocaleUtil.getMessage(Constants.DELETION_SUCCESSFUL));
    }

    @ApiOperation(value = "根据名称查询文件或文件夹")
    @PostMapping("/queryMediaByName")
    public Result<Page<MediaDTO>> queryMediaByName(@RequestBody PageMedia pageMedia) {
        log.info("根据名称查询文件入参：{}", JacksonUtils.writeValueAsString(pageMedia));
        return Result.buildSuccessResult(mediaServiceClient.queryMediaByName(pageMedia));
    }

    @ApiOperation(value = "查询根目录文件和文件夹")
    @PostMapping("/queryMediaList")
    public Result<Page<MediaDTO>> queryMediaList(@RequestBody PageMedia pageMedia) {
        log.info("查询根目录文件入参：{}", JacksonUtils.writeValueAsString(pageMedia));
        return Result.buildSuccessResult(mediaServiceClient.queryMediaList(pageMedia));
    }

    @ApiOperation(value = "移动文件")
    @PostMapping("/move")
    public Result<Boolean> moveFile(@RequestBody FileMoveDTO fileMoveDTO) {
        log.info("移动文件入参：{}", JacksonUtils.writeValueAsString(fileMoveDTO));
        return Result.buildSuccessResult(mediaServiceClient.moveFile(fileMoveDTO));
    }

    @ApiOperation(value = "文件重命名")
    @PostMapping("/rename")
    public Result<Boolean> renameFile(@RequestBody FileRenameDTO fileRenameDTO) {
        log.info("文件重命名入参：{}", JacksonUtils.writeValueAsString(fileRenameDTO));
        if (mediaServiceClient.renameFile(fileRenameDTO)) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildFailResult(500, "已存在同名文件或文件夹");
        }
    }

    @ApiOperation(value = "查询云图库图片")
    @PostMapping("/cloud/queryMediaList")
    public Result<Page<MediaDTO>> queryCloudMediaList(@RequestBody PageMedia pageMedia) {
        log.info("查询云图库图片入参：{}", JacksonUtils.writeValueAsString(pageMedia));
        return Result.buildSuccessResult(cloudMediaServiceClient.queryMediaList(pageMedia));
    }

    @ApiOperation(value = "根据名称查询云图库图片")
    @PostMapping("/cloud/queryMediaByName")
    public Result<Page<MediaDTO>> queryCloudMediaByName(@RequestBody PageMedia pageMedia) {
        log.info("根据名称查询云图库图片入参：{}", JacksonUtils.writeValueAsString(pageMedia));
        return Result.buildSuccessResult(cloudMediaServiceClient.queryMediaByName(pageMedia));
    }

}
