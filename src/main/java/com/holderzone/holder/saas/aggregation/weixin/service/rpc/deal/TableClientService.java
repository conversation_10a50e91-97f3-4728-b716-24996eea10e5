package com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.item.UpdateOrderItemInfoDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.table.*;
import com.holderzone.saas.store.dto.weixin.WxStoreTableCombineDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Service
@FeignClient(name = "holder-saas-store-table", fallbackFactory = TableClientService.WxStoreTableOpenFallBack.class)
public interface TableClientService {

	@PostMapping("/table/open")
	String openTable(OpenTableDTO openTableDTO);

	@PostMapping("/area/query/all/{storeGuid}")
	List<AreaDTO> queryArea(@PathVariable("storeGuid") String storeGuid);

	@PostMapping("/table/whether/open")
	boolean whetherBeOpened(OpenTableDTO openTableDTO);

	@PostMapping("/table/combine")
	List<String> combine(TableCombineDTO tableCombineDTO);

	@PostMapping("/table/compensation")
	void compensationTableStatus(List<CompensationTableDTO> list);

	@PostMapping("/table/close")
	boolean closeTable(CancelOrderReqDTO cancelOrderReqDTO);

	@PostMapping("/table/separate")
	boolean separateTable(TableOrderCombineDTO tableOrderCombineDTO);

	@PostMapping("/table/turn")
	boolean turnTale(TurnTableDTO turnTableDTO);

	@ApiOperation("获取当前桌台guid")
	@PostMapping("/table/getOrderGuid/{tableGuid}")
	String getOrderGuid(@PathVariable("tableGuid") String tableGuid);

	@PostMapping("/table/try_open")
	String tryOpen(OpenTableDTO openTableDTO);

	@ApiOperation("校验桌台是否能开台接口，处于空闲状态，未被业务锁定")
	@PostMapping("/table/whether/open")
	boolean checkStatus(OpenTableDTO openTableDTO);

	@ApiModelProperty("根据当前桌台，查出所有并桌")
	@PostMapping("/table/table_list/{tableGuid}")
	List<WxStoreTableCombineDTO> tableList(@PathVariable("tableGuid") String tableGuid);

	@PostMapping("/table/android/query")
	List<TableOrderDTO> listByAndroid(@RequestBody TableBasicQueryDTO tableBasicQueryDTO);

	@ApiModelProperty("根据桌台guid，查桌台详情")
	@PostMapping("/table/details/{tableGuid}")
	TableDTO getTableByGuid(@PathVariable("tableGuid") String tableGuid);

	@Slf4j
	@Component
	class WxStoreTableOpenFallBack implements FallbackFactory<TableClientService> {
		@Override
		public TableClientService create(Throwable throwable) {
			return new TableClientService() {

				@Override
				public boolean turnTale(TurnTableDTO turnTableDTO) {
					log.error("转台接口异常 e={} turnTableDTO={}", throwable.getMessage(), JacksonUtils.writeValueAsString(turnTableDTO));
					return false;
				}

				@Override
				public String getOrderGuid(String tableGuid) {
					log.error("远程调用失败，throwable={}", throwable.getMessage());
					throw new BusinessException("失败!!" + throwable.getMessage());
				}

				@Override
				public boolean separateTable(TableOrderCombineDTO tableOrderCombineDTO) {
					log.error("拆台失败 e={} tableOrderCombineDTO={}", throwable.getMessage(), JacksonUtils.writeValueAsString(tableOrderCombineDTO));
					return false;
				}

				@Override
				public boolean closeTable(CancelOrderReqDTO cancelOrderReqDTO) {
					log.error("关台接口异常 e={} cancelOrderReqDTO={}", throwable.getMessage(), JacksonUtils.writeValueAsString(cancelOrderReqDTO));
					return false;
				}

				@Override
				public void compensationTableStatus(List<CompensationTableDTO> list) {
					log.error("补偿桌台状态异常 e={} list={}", throwable.getMessage(), JacksonUtils.writeValueAsString(list));
				}

				@Override
				public List<String> combine(TableCombineDTO tableCombineDTO) {
					log.error("并桌异常 e={} tableCombineDTO={}", throwable.getMessage(), JacksonUtils.writeValueAsString(tableCombineDTO));
					return null;
				}

				@Override
				public String openTable(OpenTableDTO openTableDTO) {
					log.error("开台异常 e={} openTableDTO={}", throwable.getMessage(), JacksonUtils.writeValueAsString(openTableDTO));
					throw new BusinessException("开台失败");
				}

				@Override
				public boolean whetherBeOpened(OpenTableDTO openTableDTO) {
					log.error("桌台状态校验异常 e={} openTableDTO={}", throwable.getMessage(), JacksonUtils.writeValueAsString(openTableDTO));
					return false;
				}


				@Override
				public List<AreaDTO> queryArea(String storeGuid) {
					log.error("查询门店区域异常 e={} storeGuid={}", throwable.getMessage(), storeGuid);
					throw new BusinessException("查询门店区域异常");
				}

				@Override
				public String tryOpen(OpenTableDTO openTableDTO) {
					log.error("尝试开台接口熔断 e={} openTableDTO={}", throwable.getMessage(), openTableDTO);
					throw new BusinessException("尝试开台出现异常");
				}

				@Override
				public boolean checkStatus(OpenTableDTO openTableDTO) {
					log.error("尝试开台接口熔断 e={} openTableDTO={}", throwable.getMessage(), openTableDTO);
					throw new BusinessException("尝试开台出现异常");
				}

				@Override
				public List<TableOrderDTO> listByAndroid(TableBasicQueryDTO tableBasicQueryDTO) {
					log.error("获取桌台状态列表信息失败， e={} tableBasicQueryDTO={}", throwable.getMessage(), tableBasicQueryDTO);
					throw new BusinessException("获取桌台状态列表信息失败");
				}

				@Override
				public TableDTO getTableByGuid(String tableGuid) {
					log.error("获取桌台状态列表信息失败， e={} tableBasicQueryDTO={}", throwable.getMessage(), tableGuid);
					throw new BusinessException("获取桌台状态列表信息失败");
				}

				@Override
				public List<WxStoreTableCombineDTO> tableList(String tableGuid) {
					log.error("尝试获取所有并桌失败:{},桌台id:{}", throwable.getMessage(), tableGuid);
					throw new BusinessException("尝试开台出现异常");
				}
			};

		}
	}
}

