package com.holderzone.holder.saas.aggregation.merchant.controller.report;

import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.StoreInfoClientService;
import com.holderzone.saas.store.dto.store.store.ChildOrganization;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreInfoController
 * @date 2018/10/16 18:07
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Api("报表获取门店信息接口")
@RestController
@RequestMapping("/report")
@Deprecated
public class StoreInfoController {

    private static final Logger logger = LoggerFactory.getLogger(StoreInfoController.class);

    private final StoreInfoClientService storeInfoClientService;

    @Autowired
    public StoreInfoController(StoreInfoClientService storeInfoClientService) {
        this.storeInfoClientService = storeInfoClientService;
    }

    @ApiOperation(value = "查询门店信息接口")
    @PostMapping("/store")
    @Deprecated
    public Result<List<ChildOrganization>> getStoreInfo() {
        logger.info("查询门店信息接口");
        List<ChildOrganization> childOrganization = storeInfoClientService.getStoreInfo();
        return Result.buildSuccessResult(childOrganization);
    }

}
