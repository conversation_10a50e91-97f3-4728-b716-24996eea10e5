package com.holderzone.holder.saas.aggregation.merchant.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2023-07-10
 * @description
 */
@Data
public class BusinessOrderStatisticsExportRespDTO {

    private String storeName;

    private String tradeMode;

    private String orderNo;

    private String orderState;

    private String orderSource;

    private LocalDateTime createTime;

    private LocalDateTime checkoutTime;

    private String diningTableName;

    private String orderFeeForCombine;

    private String actuallyPayFee;

    private String payWay;

    private String cashier;

    /**
     * 开票结果 1成功 0失败
     */
    private String isInvoice;
}
