package com.holderzone.holder.saas.aggregation.app.controller.trade;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.app.anno.ApiVersionControl;
import com.holderzone.holder.saas.aggregation.app.constant.Constant;
import com.holderzone.holder.saas.aggregation.app.helper.RedisHelper;
import com.holderzone.holder.saas.aggregation.app.service.ApiVersionControlService;
import com.holderzone.holder.saas.aggregation.app.service.UploadService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.OrderItemClientService;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelFreeItemReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.PriceChangeItemReqDTO;
import com.holderzone.saas.store.dto.order.request.item.*;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.enums.common.ResultCodeEnum;
import com.holderzone.saas.store.enums.locale.LocaleMessageEnum;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderItemAppController
 * @date 2018/09/04 11:26
 * @description app聚合层订单商品接口
 * @program holder-saas-aggregation-app
 */
@Slf4j
@RestController
@RequestMapping("/order_item")
@Api(description = "订单商品接口")
public class OrderItemAppController {

    private final OrderItemClientService orderItemClientService;

    private final RedisHelper redisHelper;

    private final UploadService uploadService;

    private final ApiVersionControlService apiVersionControlService;

    @Autowired
    public OrderItemAppController(OrderItemClientService orderItemClientService, RedisHelper redisHelper,
                                  UploadService uploadService, ApiVersionControlService apiVersionControlService) {
        this.orderItemClientService = orderItemClientService;
        this.redisHelper = redisHelper;
        this.uploadService = uploadService;
        this.apiVersionControlService = apiVersionControlService;
    }

    @ApiOperation(value = "商品新增接口", notes = "商品新增接口")
    @PostMapping("/add_item")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE,
            description = "商品新增接口", action = OperatorType.ADD)
    public Result addItem(@RequestBody CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        String uuid = createDineInOrderReqDTO.getAddGoodsBatchUUID();
        if (!StringUtils.isEmpty(uuid)) {
            log.info("防重复提交 uuid:{}", uuid);
            boolean b = redisHelper.setNxEx(uuid, "1", 200);
            if (!b) {
                log.info("重复提交");
                return Result.buildOpFailedResult("已提交，正在处理中，请勿重复操作");
            }
        }
        log.info("商品新增接口：{}", JacksonUtils.writeValueAsString(createDineInOrderReqDTO));
        List<DineInItemDTO> dineInItemList = createDineInOrderReqDTO.getDineInItemDTOS();
        if (CollectionUtils.isNotEmpty(dineInItemList)) {
            long changeGroupItemCount = dineInItemList.stream().filter(e -> CollectionUtils.isNotEmpty(e.getPackageSubgroupDTOS()))
                    .flatMap(e -> e.getPackageSubgroupDTOS().stream())
                    .flatMap(e -> e.getSubDineInItemDTOS().stream())
                    .filter(e -> Objects.nonNull(e.getOriginalItem()))
                    .count();
            if (changeGroupItemCount > 0) {
                apiVersionControlService.changeGroupItem();
            }
        }
        EstimateItemRespDTO estimateItemRespDTO = orderItemClientService.batchAddItems(createDineInOrderReqDTO);
        if (estimateItemRespDTO.getEstimate() != null && estimateItemRespDTO.getEstimate()) {
            redisHelper.delete(uuid);
            return Result.buildFailResult(ResultCodeEnum.ITEM_ESTIMATE_EXCEPTION.getCode(), LocaleMessageEnum.getLocale(estimateItemRespDTO.getEstimateInfo()))
                    .setTData(estimateItemRespDTO.getEstimateSkuGuids());
        }
        if (estimateItemRespDTO.getResult()) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        redisHelper.delete(uuid);
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "批量划菜", notes = "批量划菜")
    @PostMapping("/serve_item")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "批量划菜", action = OperatorType.SELECT)

    public Result serveItem(@RequestBody ServeItemReqDTO serveItemReqDTO) {
        if (orderItemClientService.batchServeItem(serveItemReqDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "批量催菜", notes = "批量催菜")
    @PostMapping("/urge")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "批量催菜", action = OperatorType.SELECT)

    public Result urge(@RequestBody UpdateItemStateReqDTO updateItemStateReqDTO) {
        if (orderItemClientService.batchUrgeItem(updateItemStateReqDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "批量叫起", notes = "批量叫起")
    @PostMapping("/call_up")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "批量叫起", action = OperatorType.SELECT)

    public Result callUp(@RequestBody UpdateItemStateReqDTO updateItemStateReqDTO) {
        if (orderItemClientService.callUp(updateItemStateReqDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "批量退货接口", notes = "批量退菜接口")
    @PostMapping("/return")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "批量退货接口", action = OperatorType.SELECT)

    public Result<BatchItemReturnOrFreeReqDTO> returnItem(@RequestBody BatchItemReturnOrFreeReqDTO
                                                                  batchItemReturnOrFreeReqDTO) {
        log.info("退货接口入参：{}", JacksonUtils.writeValueAsString(batchItemReturnOrFreeReqDTO));
        // 上传授权图片
        batchItemReturnOrFreeReqDTO.setAuthStaffPicture(uploadService.upload(batchItemReturnOrFreeReqDTO.getAuthStaffPicture()));
        return Result.buildSuccessResult(orderItemClientService.returnItem(batchItemReturnOrFreeReqDTO));

    }

    @ApiOperation(value = "批量赠送接口", notes = "批量赠送接口")
    @PostMapping("/free")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "批量赠送接口", action = OperatorType.SELECT)

    public Result<BatchItemReturnOrFreeReqDTO> freeItem(@RequestBody BatchItemReturnOrFreeReqDTO
                                                                batchItemReturnOrFreeReqDTO) {
        log.info("赠送接口入参：{}", JacksonUtils.writeValueAsString(batchItemReturnOrFreeReqDTO));
        return Result.buildSuccessResult(orderItemClientService.freeItem(batchItemReturnOrFreeReqDTO));

    }

    @ApiOperation(value = "取消赠送接口", notes = "取消赠送接口")
    @PostMapping("/cancel_free")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "取消赠送接口", action = OperatorType.SELECT)

    public Result cancelFree(@RequestBody CancelFreeItemReqDTO cancelFreeItemReqDTO) {
        log.info("取消赠送接口入参：{}", JacksonUtils.writeValueAsString(cancelFreeItemReqDTO));
        if (orderItemClientService.cancelFree(cancelFreeItemReqDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "改价折扣接口", notes = "批量取消赠送接口")
    @PostMapping("/change_price")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "改价折扣接口", action = OperatorType.SELECT)

    public Result changePrice(@RequestBody PriceChangeItemReqDTO priceChangeItemReqDTO) {
        log.info("改价折扣接口入参：{}", JacksonUtils.writeValueAsString(priceChangeItemReqDTO));
        return Result.buildSuccessResult(orderItemClientService.changePrice(priceChangeItemReqDTO));
    }

    @ApiOperation(value = "套餐子菜换菜", notes = "套餐子菜换菜")
    @PostMapping("/change_item")
    @ApiVersionControl
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "套餐子菜换菜接口", action = OperatorType.UPDATE)
    public Result changeGroupItem(@RequestBody ChangeGroupItemReqDTO changeGroupItemReqDTO) {
        log.info("套餐子菜换菜入参：{}", JacksonUtils.writeValueAsString(changeGroupItemReqDTO));
        EstimateItemRespDTO estimateItemRespDTO = orderItemClientService.changeGroupItem(changeGroupItemReqDTO);
        if (estimateItemRespDTO.getEstimate() != null && estimateItemRespDTO.getEstimate()) {
            return Result.buildFailResult(ResultCodeEnum.ITEM_ESTIMATE_EXCEPTION.getCode(), LocaleMessageEnum.getLocale(estimateItemRespDTO.getEstimateInfo()))
                    .setTData(estimateItemRespDTO.getEstimateSkuGuids());
        }
        if (Boolean.TRUE.equals(estimateItemRespDTO.getResult())) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "撤销套餐子菜换菜")
    @PostMapping("/cancel_change_item")
    @ApiVersionControl
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "撤销套餐子菜换菜", action = OperatorType.UPDATE)
    public Result cancelChangeGroupItem(@RequestBody ChangeGroupItemReqDTO changeGroupItemReqDTO) {
        log.info("撤销套餐子菜换菜,changeGroupItemReqDTO:{}", JacksonUtils.writeValueAsString(changeGroupItemReqDTO));
        EstimateItemRespDTO estimateItemRespDTO = orderItemClientService.cancelChangeGroupItem(changeGroupItemReqDTO);
        if (estimateItemRespDTO.getEstimate() != null && estimateItemRespDTO.getEstimate()) {
            return Result.buildFailResult(ResultCodeEnum.ITEM_ESTIMATE_EXCEPTION.getCode(), LocaleMessageEnum.getLocale(estimateItemRespDTO.getEstimateInfo()))
                    .setTData(estimateItemRespDTO.getEstimateSkuGuids());
        }
        if (Boolean.TRUE.equals(estimateItemRespDTO.getResult())) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiVersionControl
    @ApiOperation(value = "转菜")
    @PostMapping("/transfer_item")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "转菜", action = OperatorType.UPDATE)
    public Result<Void> transferItem(@RequestBody TransferItemReqDTO transferReq) {
        log.info("[转菜]req={}", transferReq);
        orderItemClientService.transferItem(transferReq);
        return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
    }
}
