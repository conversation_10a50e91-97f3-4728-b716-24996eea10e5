package com.holderzone.holder.saas.aggregation.merchant.controller.user;

import cn.hutool.core.collection.CollectionUtil;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.entity.enums.CommonLocaleEnum;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.user.UserAuthorityFeignService;
import com.holderzone.saas.store.dto.user.UserAuthorityDTO;
import com.holderzone.saas.store.dto.user.UserAuthorityQueryDTO;
import com.holderzone.saas.store.dto.user.UserAuthoritySaveDTO;
import com.holderzone.saas.store.dto.user.UserAuthorityUpdateDTO;
import com.holderzone.saas.store.dto.user.req.AuthorizationReqDTO;
import com.holderzone.saas.store.dto.user.resp.PermissionsRespDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/user_authority")
@Api(description = "员工数据权限管理接口")
public class UserAuthorityController {

    private final UserAuthorityFeignService userAuthorityFeignService;


    @Autowired
    public UserAuthorityController(UserAuthorityFeignService userAuthorityFeignService) {
        this.userAuthorityFeignService = userAuthorityFeignService;
    }

    @ApiOperation(value = "保存用户权限", notes = "保存用户权限")
    @PostMapping(value = "/save_user_authority")
    public Result<Boolean> save(@RequestBody UserAuthoritySaveDTO userAuthoritySaveDTO) {
        log.info("保存用户权限入参:{}", JacksonUtils.writeValueAsString(userAuthoritySaveDTO));
        userAuthorityFeignService.saveUserAuthority(userAuthoritySaveDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "录入人脸")
    @PostMapping(value = "/inputFace")
    public Result<Void> inputFace(@RequestBody UserAuthoritySaveDTO userAuthoritySaveDTO) {
        log.info("[录入人脸]入参={}", JacksonUtils.writeValueAsString(userAuthoritySaveDTO));
        userAuthorityFeignService.inputFace(userAuthoritySaveDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "查询用户权限", notes = "查询用户权限")
    @PostMapping(value = "/query_user_authority")
    public Result<List<UserAuthorityDTO>> query(@RequestBody UserAuthorityQueryDTO userAuthorityQueryDTO) {
        log.info("查询用户权限入参:{}", JacksonUtils.writeValueAsString(userAuthorityQueryDTO));
        List<UserAuthorityDTO> data = userAuthorityFeignService.queryUserAuthority(userAuthorityQueryDTO);
        return Result.buildSuccessResult(data);
    }

    @ApiOperation(value = "更新用户权限", notes = "更新用户权限")
    @PostMapping(value = "/update")
    public Result<String> update(@RequestBody UserAuthorityUpdateDTO userAuthorityUpdateDTO) {
        log.info("更新用户权限入参:{}", JacksonUtils.writeValueAsString(userAuthorityUpdateDTO));
        userAuthorityFeignService.updateUserData(userAuthorityUpdateDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "删除员工账号权限")
    @PostMapping(value = "/delete")
    public Result<Boolean> deleteAuthority(@RequestBody AuthorizationReqDTO deleteDTO) {
        log.info("删除员工账号权限：{}", JacksonUtils.writeValueAsString(deleteDTO));
        Boolean deleteAuthority = userAuthorityFeignService.deleteAuthority(deleteDTO);
        return Result.buildSuccessResult(LocaleUtil.getMessage(Constants.DELETION_SUCCESSFUL), deleteAuthority);
    }

    @ApiOperation(value = "查询所有可升级权限")
    @PostMapping(value = "/query_authority")
    public Result<List<PermissionsRespDTO>> queryAuthority() {
        List<PermissionsRespDTO> permissionsResp = userAuthorityFeignService.queryAuthority();
        if(CollectionUtil.isNotEmpty(permissionsResp)){
            permissionsResp.forEach(p -> p.setSourceName(CommonLocaleEnum.getLocale(p.getSourceName())));
        }
        return Result.buildSuccessResult(permissionsResp);
    }
}
