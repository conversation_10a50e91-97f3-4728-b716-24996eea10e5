package com.holderzone.holder.saas.aggregation.weixin.helper;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.aggregation.weixin.utils.UserMemberSessionUtils;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseProductDiscount;
import com.holderzone.holder.saas.member.wechat.dto.enums.MemberRightsType;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.holder.saas.weixin.utils.WxMemberSessionUtil;
import com.holderzone.saas.store.constant.RedisKeyConstant;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberSessionDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 会员权益帮助类
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MemberRightHelper {

    private final RedisUtils redisUtils;

    private final UserMemberSessionUtils userMemberSessionUtils;

    private final HsaBaseClientService wechatClientService;


    /**
     * 查询会员权益
     *
     * @param wxToken 微信Token
     * @return 查询会员权益
     */
    public ResponseProductDiscount queryMemberRights(String wxToken) {
        String redisKey = String.format(RedisKeyConstant.MEMBER_RIGHTS, wxToken);
        ResponseProductDiscount productDiscountRespDTO = (ResponseProductDiscount) redisUtils.get(redisKey);
        log.info("[查询会员权益]memberRights={}", JacksonUtils.writeValueAsString(productDiscountRespDTO));
        if (ObjectUtils.isEmpty(productDiscountRespDTO)) {
            productDiscountRespDTO = saveMemberRights(wxToken);
        }
        return productDiscountRespDTO;
    }


    /**
     * 保存会员权益
     */
    public ResponseProductDiscount saveMemberRights(String wxToken) {
        String redisKey = String.format(RedisKeyConstant.MEMBER_RIGHTS, wxToken);
        ResponseProductDiscount productDiscountRespDTO = enableMemberPrice(isMemberLogin());
        redisUtils.setEx(redisKey, productDiscountRespDTO, 30, TimeUnit.MINUTES);
        return productDiscountRespDTO;
    }

    /**
     * 删除会员权益缓存
     */
    public void removeMemberRights(String wxToken) {
        String redisKey = String.format(RedisKeyConstant.MEMBER_RIGHTS, wxToken);
        redisUtils.delete(redisKey);
    }

    private boolean isMemberLogin() {
        WxMemberSessionDTO memberByOpenId = WxMemberSessionUtil.getMemberByOpenId(redisUtils, WeixinUserThreadLocal.getOpenId());
        return (memberByOpenId == null ? WeixinUserThreadLocal.getIsLogin() : memberByOpenId.getWxUserInfoDTO().getIsLogin());
    }


    /**
     * @return 会员价与匹配规则
     */
    private ResponseProductDiscount enableMemberPrice(Boolean isLogin) {
        ResponseProductDiscount productDiscountRespDTO = null;
        UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
        log.info("查询出的redis中的会员卡信息:{}", JacksonUtils.writeValueAsString(userMemberSession));
        String memberInfoCardGuid = userMemberSession.getMemberInfoCardGuid();
        if (!StringUtils.isEmpty(memberInfoCardGuid) && memberInfoCardGuid.length() > 5 && Boolean.TRUE.equals(isLogin)) {
            WxMemberSessionDTO memberByOpenId = WxMemberSessionUtil.getMemberByOpenId(redisUtils, WeixinUserThreadLocal.getOpenId());
            Integer tradeMode = Optional.ofNullable(memberByOpenId).orElse(new WxMemberSessionDTO()).getOrderState();
            productDiscountRespDTO = wechatClientService.getDiscountProducts(memberInfoCardGuid,
                    WeixinUserThreadLocal.getStoreGuid(), tradeMode).getData();
            log.info("门店首页：会员匹配规则：{}", productDiscountRespDTO);
        }
        if (productDiscountRespDTO == null) {
            productDiscountRespDTO = new ResponseProductDiscount();
            productDiscountRespDTO.setMemberPrice(false);
            productDiscountRespDTO.setMemberRightsType(MemberRightsType.NOT.getCode());
        }
        userMemberSession.setWhetherSupportMemberPrice(productDiscountRespDTO.isMemberPrice());
        userMemberSessionUtils.addUserMemberSession(userMemberSession);
        return productDiscountRespDTO;
    }

}
