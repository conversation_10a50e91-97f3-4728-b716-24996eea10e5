package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.rights;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.dto.activity.common.VolumeInfoCommonRespDTO;
import com.holderzone.holder.saas.member.dto.rights.common.SelectStoreInfoDTO;
import com.holderzone.holder.saas.member.dto.rights.request.HsmRechargeSetReqDTO;
import com.holderzone.holder.saas.member.dto.rights.request.QueryVolumeDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2019/5/18 10:52
 */
@Component
@FeignClient(name = "holder-saas-member-account",fallbackFactory = SystemManagemenRechargeSetClientService.SystemManagemenRechargeSetClientServiceFallBack.class)
public interface SystemManagemenRechargeSetClientService {

    /**
     * 体系下的充值配置
     * @param hsmRechargeSetReqDTO
     * @return
     */
    @PostMapping(value = "/hsm-system-management-recharge-set/create", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    String createRechargeRule(@Validated HsmRechargeSetReqDTO hsmRechargeSetReqDTO);
    /**
     *  体系下的充值配置 --update
     * @param hsmRechargeSetReqDTO
     * @return
     */
    @PostMapping(value = "/hsm-system-management-recharge-set/update", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    boolean updateRechargeRule(HsmRechargeSetReqDTO hsmRechargeSetReqDTO);

    /**
     * 查询选择门店信息
     * @return
     */
    @GetMapping(value = "/hsm-system-management-recharge-set/querySelectStoreInfo", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    List<SelectStoreInfoDTO> querySelectStoreInfo(@RequestParam("sytemMangementGuid") String sytemMangementGuid,@RequestParam("name")  String name,
                                                  @RequestParam(value = "cardLevelGuid",required = false) String cardLevelGuid,
                                                  @RequestParam(value = "relationGuid",required = false) String relationGuid,
                                                  @RequestParam(value = "rightTypeCode",required = false) Integer rightTypeCode);

    /**
     * 查询优惠劵信息
     * @param queryVolumeDTO
     * @return
     */
    @GetMapping(value = "/hsm-volume-info/queryVolumeInfo", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Page<VolumeInfoCommonRespDTO> queryVolumeInfo(QueryVolumeDTO queryVolumeDTO);
    @Slf4j
    @Component
    class SystemManagemenRechargeSetClientServiceFallBack  implements FallbackFactory<SystemManagemenRechargeSetClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";
        @Override
        public  SystemManagemenRechargeSetClientService create(Throwable throwable) {
            return new SystemManagemenRechargeSetClientService(){

                @Override
                public String createRechargeRule(HsmRechargeSetReqDTO hsmRechargeSetReqDTO) {
                    log.error(HYSTRIX_PATTERN, "createRechargeRule", JacksonUtils.writeValueAsString(hsmRechargeSetReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("充值设置失败");
                }

                @Override
                public boolean updateRechargeRule(HsmRechargeSetReqDTO hsmRechargeSetReqDTO) {
                    log.error(HYSTRIX_PATTERN, "updateRechargeRule", JacksonUtils.writeValueAsString(hsmRechargeSetReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("充值更新失败");
                }

                @Override
                public List<SelectStoreInfoDTO> querySelectStoreInfo(String sytemMangementGuid,String name,
                                                                     String cardLevelGuid,
                                                                     String relationGuid,
                                                                     Integer rightTypeCode) {
                    log.error(HYSTRIX_PATTERN, "querySelectStoreInfo", sytemMangementGuid+name, ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询选择的门店信息失败");
                }

                @Override
                public Page<VolumeInfoCommonRespDTO> queryVolumeInfo(QueryVolumeDTO queryVolumeDTO) {
                    log.error(HYSTRIX_PATTERN, "queryVolumeInfo", JacksonUtils.writeValueAsString(queryVolumeDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询优惠劵信息失败");
                }
            };
        }

    }
}
