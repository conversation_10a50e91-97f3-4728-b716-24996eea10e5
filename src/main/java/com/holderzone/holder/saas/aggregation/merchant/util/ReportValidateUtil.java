package com.holderzone.holder.saas.aggregation.merchant.util;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.journaling.req.JournalWebBaseReqDTO;
import com.holderzone.saas.store.enums.locale.LocaleMessageEnum;
import com.holderzone.saas.store.util.LocaleUtil;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class ReportValidateUtil {

    public static void reportCompatibilityCheck(JournalWebBaseReqDTO journalWebBaseReqDTO){
        List<String> storeGuids = journalWebBaseReqDTO.getStoreGuids();
        if(storeGuids==null){
            storeGuids = new ArrayList<>();
            journalWebBaseReqDTO.setStoreGuids(storeGuids);
        }
        if (CollectionUtils.isEmpty(storeGuids)&&journalWebBaseReqDTO.getStoreGuid()!=null) {
            storeGuids.add(journalWebBaseReqDTO.getStoreGuid());
        }
        if (CollectionUtils.isEmpty(storeGuids)){
            throw  new BusinessException(LocaleUtil.getMessage(LocaleMessageEnum.NO_ACCESS_TO_STORE_DATA));
        }
    }
}
