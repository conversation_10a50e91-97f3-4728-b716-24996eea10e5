package com.holderzone.holder.saas.aggregation.merchant.service.rpc.cloud;

import com.holderzone.framework.exception.unchecked.BusinessException;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LoginClientService
 * @date 2018/10/12 17:38
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "authorization-service", fallbackFactory = CloudAggClientService.LoginFallBack.class)
public interface CloudAggClientService {

    @PostMapping("/login")
    // String login(LoginDTO loginDTO);

    @GetMapping("/logout")
    boolean logout(@RequestParam("token") String token, @RequestParam("source") String source);

    @Component
    class LoginFallBack implements FallbackFactory<CloudAggClientService> {

        private static final Logger logger = LoggerFactory.getLogger(LoginFallBack.class);

        @Override
        public CloudAggClientService create(Throwable throwable) {
            return new CloudAggClientService() {
               /* @Override
                public String login(LoginDTO loginDTO) {
                    logger.error("登录失败，e{}", throwable.getMessage());
                    throw new BusinessException("登录失败");
                }*/

                @Override
                public boolean logout(String token, String source) {
                    logger.error("登出失败，e{}", throwable.getMessage());
                    throw new BusinessException("登出失败");
                }
            };
        }
    }
}
