package com.holderzone.holder.saas.aggregation.merchant.controller.report.orderdish;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.orderdish.OrderDishServer;
import com.holderzone.saas.store.dto.report.OrderDishDTO;
import com.holderzone.saas.store.dto.report.OrderDishDetailDTO;
import com.holderzone.saas.store.dto.report.OrderDishTypeInfoDTO;
import com.holderzone.saas.store.dto.report.query.OrderDishDetailQueryDTO;
import com.holderzone.saas.store.dto.report.query.OrderDishQueryDTO;
import com.holderzone.saas.store.dto.report.query.OrderDishTypeInfoQueryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2018/09/27 下午 14:52
 * @description
 */
@Api(tags = "订单菜品相关接口",hidden = true)
@RestController
@RequestMapping("/orderDish")
@Deprecated
public class OrderDishController {

    private static Logger log = LoggerFactory.getLogger(OrderDishController.class);

    @Autowired
    private OrderDishServer orderDishService;

    @PostMapping("/saleDish")
    @ApiOperation(value = "销售菜品或者赠送菜品报表")
    @Deprecated
    public Result<Page<OrderDishDTO>> saleDish(@RequestBody OrderDishQueryDTO orderDishQuery) {
        orderDishQuery.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        log.info("查询菜品销售或者赠送报表入参： {}", JacksonUtils.writeValueAsString(orderDishQuery));
        return Result.buildSuccessResult(orderDishService.saleDish(orderDishQuery));
    }


    @PostMapping("/saleDishDetail")
    @ApiOperation(value = "销售菜品或者赠送菜品明细报表")
    @Deprecated
    public Result<Page<OrderDishDetailDTO>> saleDishDetail(@RequestBody OrderDishDetailQueryDTO orderDishDetailQuery) {
        orderDishDetailQuery.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        log.info("查询菜品销售或者赠送明细报表入参: {}", JacksonUtils.writeValueAsString(orderDishDetailQuery));
        return Result.buildSuccessResult(orderDishService.saleDishDetail(orderDishDetailQuery));
    }

    @PostMapping("/saleDishTypeInfo")
    @ApiOperation(value = "订单菜品分类统计")
    @Deprecated
    public Result<Page<OrderDishTypeInfoDTO>> orderDishTypeInfo(@RequestBody OrderDishTypeInfoQueryDTO orderDishTypeInfoQuery) {
        orderDishTypeInfoQuery.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        log.info("订单菜品分类统计入参：{}", JacksonUtils.writeValueAsString(orderDishTypeInfoQuery));
        return Result.buildSuccessResult(orderDishService.orderDishTypeInfo(orderDishTypeInfoQuery));
    }

    @PostMapping("/saleDishReturn")
    @ApiOperation(value = "退菜统计")
    @Deprecated
    public Result<Page<OrderDishDTO>> orderDishReturn(@RequestBody OrderDishQueryDTO orderDishQuery) {
        orderDishQuery.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        log.info("退菜统计入参：{}", JacksonUtils.writeValueAsString(orderDishQuery));
        return Result.buildSuccessResult(orderDishService.orderDishReturn(orderDishQuery));
    }

    @PostMapping("/saleDishReturnDetail")
    @ApiOperation(value = "退菜明细")
    @Deprecated
    public Result<Page<OrderDishDetailDTO>> orderDishReturnDetail(@RequestBody OrderDishDetailQueryDTO orderDishDetailQuery) {
        orderDishDetailQuery.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        log.info("退菜明细入参：{}", JacksonUtils.writeValueAsString(orderDishDetailQuery));
        return Result.buildSuccessResult(orderDishService.orderDishReturnDetail(orderDishDetailQuery));
    }
}
