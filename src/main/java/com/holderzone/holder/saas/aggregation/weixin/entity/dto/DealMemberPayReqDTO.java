package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors
@ApiModel("会员支付入参")
public class DealMemberPayReqDTO {

    @ApiModelProperty(value = "订单id", required = true)
    @NotBlank(message = "订单id必传")
    private String orderGuid;

    @ApiModelProperty(value = "加密后会员密码", required = true)
    @NotBlank(message = "会员密码必传")
    private String memberPassWord;

    @ApiModelProperty(value = "设备类型")
    private Integer deviceType;

    /**
     * @see com.holderzone.saas.store.enums.weixin.WxAppletMemberPayTypeEnum
     */
    @ApiModelProperty(value = "支付方式 6:储值金额 4:收益余额")
    private Integer payType;

    /**
     * @see com.holderzone.saas.store.enums.order.TradeModeEnum
     */
    private Integer tradeMode;

    @ApiModelProperty(value = "是否使用会员优惠")
    private Boolean useMemberDiscountFlag;

    @ApiModelProperty(value = "支付金额", required = true)
    private BigDecimal payAmount;

    @ApiModelProperty(value = "是否二次支付(第一次支付取消或者失败，在订单详情页面支付)")
    private Boolean secondPayFlag;

    /**
     * 会员卡guid
     */
    @ApiModelProperty(value = "会员卡guid")
    private String memberInfoCardGuid;

}
