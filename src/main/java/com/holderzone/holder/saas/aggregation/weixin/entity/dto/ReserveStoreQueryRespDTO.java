package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("新增预订时，桌台与时间查询")
public class ReserveStoreQueryRespDTO {

	private List<ReserveTimeInfoRespDTO> reserveTimeInfoRespDTOS= Collections.emptyList();

	private List<ReserveTimeRespDTO> reserveTimeRespDTOS= Collections.emptyList();

	private List<ReserveAreaRespDTO> reserveAreaRespDTOS= Collections.emptyList();

	@ApiModelProperty(value = "用户昵称")
	private String nickName;

	@ApiModelProperty(value = "0：女，1：男")
	private Integer gender=0;

	@ApiModelProperty(value = "电话，可能为空")
	private String phone= StringUtils.EMPTY;
}
