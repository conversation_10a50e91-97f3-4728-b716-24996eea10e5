package com.holderzone.holder.saas.aggregation.merchant.controller.rights;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.rights.SystemManagementIntegralSetClientService;
import com.holderzone.holder.saas.member.dto.rights.request.HsmIntegralSetReqDTO;
import com.holderzone.holder.saas.member.dto.rights.request.IntegralDeductionDTO;
import com.holderzone.holder.saas.member.dto.rights.request.IntegralGiveDTO;
import com.holderzone.holder.saas.member.enums.rights.EnableTypeEnum;
import com.holderzone.holder.saas.member.enums.rights.IntegralValidityPeriodTypeEnum;
import com.holderzone.holder.saas.member.enums.rights.UnitTimeEnum;
import com.holderzone.holder.saas.member.util.validation.ValidationUtils;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description  积分创建controller
 * @date 2019/5/17 11:15
 */
@RestController
@RequestMapping("/hsm-system-management-integral-set")
public class HsmSystemManagementIntegralSetController {
    /**
     * 二月
     */
    private static final  int month2=2;
    private static final  int month2end=28;
    @Autowired
    private SystemManagementIntegralSetClientService systemManagementIntegralSetClientService;

    /**
     * @Description 积分规则创建
     * <AUTHOR>
     * @Date  2019/5/17 13:55
     * @param hsmIntegralSetReqDTO
     * @return com.holderzone.framework.response.Result
     */
    @ApiOperation(value = "创建消费积分规则", notes = "创建消费积分规则")
    @PostMapping(value = "/create", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "创建消费积分规则")
    public Result createIntegralRule(@RequestBody  HsmIntegralSetReqDTO hsmIntegralSetReqDTO)  {
        checkout(hsmIntegralSetReqDTO);

        return Result.buildSuccessResult(systemManagementIntegralSetClientService.createIntegralRule(hsmIntegralSetReqDTO));
    }

    /**
     * 校验
     * @param hsmIntegralSetReqDTO
     */
    private void checkout(HsmIntegralSetReqDTO hsmIntegralSetReqDTO) {
        IntegralGiveDTO integralGiveDTO = hsmIntegralSetReqDTO.getIntegralGiveDTO();

        if (integralGiveDTO.getRuleState() == EnableTypeEnum.ENABLE_TYPE_YES.getCode()) {
            ValidationUtils.validate(hsmIntegralSetReqDTO.getIntegralGiveDTO());
            int maxDeductionIntegral = integralGiveDTO.getMaxUnitTimeIntegral() == null ? 0 : integralGiveDTO.getMaxUnitTimeIntegral();
            if (integralGiveDTO.getUnitTime() != UnitTimeEnum.UNIT_TIME_NO_LIMIt.getCode() && maxDeductionIntegral <= 0) {
                throw new ParameterException("最高获取积分必须大于0");
            }

        }
        IntegralDeductionDTO integralDeductionDTO = hsmIntegralSetReqDTO.getIntegralDeductionDTO();
        if (integralDeductionDTO.getRuleState() == EnableTypeEnum.ENABLE_TYPE_YES.getCode()) {
            ValidationUtils.validate(hsmIntegralSetReqDTO.getIntegralDeductionDTO());
            BigDecimal maxDeductionIntegral = integralDeductionDTO.getMaxDeduction() == null ?new BigDecimal(0) : integralDeductionDTO.getMaxDeduction();
            if (integralDeductionDTO.getDeductionUnit() != UnitTimeEnum.UNIT_TIME_NO_LIMIt.getCode() && maxDeductionIntegral.compareTo(new BigDecimal(0))<1) {
                throw new BusinessException("最高可抵扣积分必须大于0");
            }
        }
        if (StringUtils.isEmpty(hsmIntegralSetReqDTO.getSystemManagementGuid())) {
            throw new ParameterException("权益体系guid不能为null");
        }
         Integer smsNotification =hsmIntegralSetReqDTO.getSmsNotification()==null?0:hsmIntegralSetReqDTO.getSmsNotification();
         Integer wechatNotification =hsmIntegralSetReqDTO.getWechatNotification()==null?0:hsmIntegralSetReqDTO.getWechatNotification();
        if (EnableTypeEnum.ENABLE_TYPE_YES.getCode()== smsNotification || EnableTypeEnum.ENABLE_TYPE_YES.getCode()== wechatNotification) {
            if (hsmIntegralSetReqDTO.getIntegralPeriodTip() <= 0 || hsmIntegralSetReqDTO.getTimeUnit() == null || hsmIntegralSetReqDTO.getAdvanceWarn() <= 0) {
                throw new ParameterException("提醒方式已勾选后，对应的信息也要填写");
            }
        }

        if(IntegralValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_YEAR.getCode()==hsmIntegralSetReqDTO.getValidityPeriodType()){
            Integer validityPeriodDay =hsmIntegralSetReqDTO.getValidityPeriodDay();
            Integer validityPeriodYear = hsmIntegralSetReqDTO.getValidityPeriodYear();
            Integer validityPeriodMonty = hsmIntegralSetReqDTO.getValidityPeriodMonty();
            if(month2==hsmIntegralSetReqDTO.getValidityPeriodMonty()){
                if(validityPeriodDay>month2end){
                    throw new BusinessException("月份为2月时,天数最多为28");
                }
            }else{
                if(validityPeriodDay<1||validityPeriodDay>31){
                    throw new BusinessException("天数在1~30之间");
                }
            }
            if (validityPeriodYear == null || validityPeriodYear == 0) {
                throw new BusinessException("有效期的年参数不合法");
            }
            if (validityPeriodMonty < 1 || validityPeriodMonty > 12) {
                throw new BusinessException("有效期的月份应在1~12");
            }

        }

    }

    /**
     * @Description 积分规则修改
     * <AUTHOR>
     * @Date  2019/5/17 13:55
     * @param hsmIntegralSetReqDTO
     * @return com.holderzone.framework.response.Result
     */
    @ApiOperation(value = "积分规则修改", notes = "积分规则修改")
    @PostMapping(value = "/update", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "积分规则修改")
    public Result updateIntegralRule(@RequestBody  HsmIntegralSetReqDTO hsmIntegralSetReqDTO)  {
        checkout(hsmIntegralSetReqDTO);

        if(systemManagementIntegralSetClientService.updateIntegralRule(hsmIntegralSetReqDTO)){
            return   Result.buildEmptySuccess();
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.UPDATE_FAILED));
    }
}
