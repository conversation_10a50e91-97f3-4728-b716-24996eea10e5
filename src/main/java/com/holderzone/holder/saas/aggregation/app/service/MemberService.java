package com.holderzone.holder.saas.aggregation.app.service;

import com.holderzone.saas.store.dto.common.BaseRespDTO;

/**
 * <AUTHOR>
 * @description 聚合层会员接口
 * @date 2021/9/22 16:11
 * @className: MemberService
 */
public interface MemberService {

    /**
     * 查询会员主体能否使用积分
     *
     * @param operSubjectGuid 运营主体Guid
     * @return 返回公共实体
     */
    BaseRespDTO queryMemberUseIntegral(String operSubjectGuid);
}
