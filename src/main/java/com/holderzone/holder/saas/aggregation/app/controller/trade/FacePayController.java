package com.holderzone.holder.saas.aggregation.app.controller.trade;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.constant.Constant;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInOrderClientService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.request.face.FacePayCompensateReqDTO;
import com.holderzone.saas.store.dto.order.request.face.FacePayEstimateReqDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.enums.common.ResultCodeEnum;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FacePayController
 * @date 2019/01/04 8:53
 * @description 人脸支付接口
 * @program holder-saas-aggregation-app
 */
@RestController
@RequestMapping("/face_pay")
@Api(description = "人脸支付")
@Slf4j
public class FacePayController {

    private final DineInOrderClientService dineInOrderClientService;

    @Autowired
    public FacePayController(DineInOrderClientService dineInOrderClientService) {
        this.dineInOrderClientService = dineInOrderClientService;
    }

    @ApiOperation(value = "人脸支付补偿接口", notes = "人脸支付补偿接口")
    @PostMapping("/compensate")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE,description = "人脸支付补偿接口",action = OperatorType.SELECT)
    public Result compensate(@RequestBody FacePayCompensateReqDTO facePayCompensateReqDTO) {
        log.info("人脸支付补偿接口入参：{}", JacksonUtils.writeValueAsString(facePayCompensateReqDTO));
        if (dineInOrderClientService.compensate(facePayCompensateReqDTO)) {
            return Result.buildEmptySuccess();
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));

    }


    @ApiOperation(value = "人脸校验估清", notes = "人脸校验估清")
    @PostMapping("/estimate")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE,description = "人脸校验估清",action = OperatorType.SELECT)
    public Result payCompensate(@RequestBody FacePayEstimateReqDTO facePayEstimateReqDTO) {
        EstimateItemRespDTO estimateItemRespDTO = dineInOrderClientService.estimate(facePayEstimateReqDTO);
        if (estimateItemRespDTO.getEstimate() != null && estimateItemRespDTO.getEstimate()) {
            return Result.buildFailResult(ResultCodeEnum.ITEM_ESTIMATE_EXCEPTION.getCode(), "菜品估清异常");
        }
        if (estimateItemRespDTO.getResult()) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));

    }

    @ApiOperation(value = "人脸支付退估清", notes = "人脸支付退估清")
    @PostMapping("/return_estimate")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE,description = "人脸支付退估清",action = OperatorType.SELECT)
    public Result returnEstimate(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("人脸支付退估清入参：{}", JacksonUtils.writeValueAsString(singleDataDTO));
        if (dineInOrderClientService.returnEstimate(singleDataDTO)) {
            return Result.buildEmptySuccess();
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));

    }

}
