package com.holderzone.holder.saas.aggregation.app.entity.auth;

import com.holderzone.holder.saas.aggregation.app.anno.DataAuthFieldControl;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponsePayWayDetail;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 会员充值统计
 */
@Data
public class MemberRechargeSaleDTO implements Serializable {

    private static final long serialVersionUID = 6697051794843925951L;

    /**
     * 充值单数
     */
    @DataAuthFieldControl("member_recharge_order_num")
    private String rechargeNum;

    /**
     * 充值人数
     */
    @DataAuthFieldControl("member_charge_number")
    private String rechargeMemberNum;

    /**
     * 充值金额
     */
    @DataAuthFieldControl("member_charge_total_amount")
    private String rechargeAmount;

    /**
     * 充值赠送
     */
    @DataAuthFieldControl("member_charge_gift_amount")
    private String presentAmount;

    /**
     * 充值收入
     */
    @DataAuthFieldControl("member_charge_amount")
    private String incomeAmount;

    /**
     * 充值收入支付方式明细
     */
    @DataAuthFieldControl("member_charge_amount")
    private List<ResponsePayWayDetail> payWayDetailList;

}
