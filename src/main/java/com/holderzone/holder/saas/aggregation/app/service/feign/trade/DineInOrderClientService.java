package com.holderzone.holder.saas.aggregation.app.service.feign.trade;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.aggregation.app.execption.GlobalTransactionalHandler;
import com.holderzone.holder.saas.member.merchant.dto.member.RequestCardRechargePageQO;
import com.holderzone.holder.saas.member.merchant.dto.member.ResponseCardRechargeStatisticsVO;
import com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.OrderLockDTO;
import com.holderzone.saas.store.dto.order.common.BaseOrderDTO;
import com.holderzone.saas.store.dto.order.inside.OrderGuidsDTO;
import com.holderzone.saas.store.dto.order.inside.OrderTableInfoDTO;
import com.holderzone.saas.store.dto.order.request.OrderDetailQueryDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillMultiplePayReqDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillMultipleRefundAggPayReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.*;
import com.holderzone.saas.store.dto.order.request.face.FacePayCompensateReqDTO;
import com.holderzone.saas.store.dto.order.request.face.FacePayEstimateReqDTO;
import com.holderzone.saas.store.dto.order.request.member.MemberCouponListReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.BillAggPayRespDTO;
import com.holderzone.saas.store.dto.order.response.bill.BillRefundAggPayRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineInOrderListRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.trade.LocalDTO;
import com.holderzone.saas.store.dto.trade.LocalQuery;
import com.holderzone.saas.store.dto.trade.SingleQuery;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderItemQuery;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderListQuery;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustOrderQueryDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustOrderReqDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByDineAndFastOrderRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustOrderDetailRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustOrderRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInOrderClientService
 * @date 2018/09/06 15:02
 * @description 正餐订单远程调用
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = DineInOrderClientService.FastFoodFallBack.class)
public interface DineInOrderClientService {

    @ApiOperation("一体机查询充值订单列表")
    @PostMapping("/recharge_order/getMemberCardRechargePage")
    ResponseCardRechargeStatisticsVO getMemberCardRechargePage(@RequestBody RequestCardRechargePageQO request);

    @PostMapping("/dine_in_order/batch_get_table_info")
    List<OrderTableInfoDTO> batchGetTableInfo(OrderGuidsDTO orderGuidsDTO);

    /**
     * 批量查询订单桌台信息
     * 专用于桌台查询所用
     *
     * @param orderGuidsDTO 订单
     * @return 订单信息
     */
    @ApiOperation(value = "批量查询订单桌台信息", notes = "批量查询订单桌台信息")
    @PostMapping("/dine_in_order/batch_get_table_info2")
    List<OrderTableInfoDTO> batchGetTableInfo2(@RequestBody OrderGuidsDTO orderGuidsDTO);

    @PostMapping("/dine_in_order/update_remark")
    Boolean updateRemark(CreateDineInOrderReqDTO createDineInOrderReqDTO);

    @PostMapping("/dine_in_order/update_guest_count")
    Boolean updateGuestCount(CreateDineInOrderReqDTO createDineInOrderReqDTO);

    @PostMapping("/dine_in_order/cancel")
    Boolean cancelOrder(CancelOrderReqDTO cancelOrderReqDTO);

    @PostMapping("/dine_in_order/get_order_detail")
    DineinOrderDetailRespDTO getOrderDetail(OrderDetailQueryDTO orderDetailQueryDTO);

    @PostMapping("/dine_in_order/get_order_detail_for_wx")
    DineinOrderDetailRespDTO getOrderDetailForWx(OrderDetailQueryDTO orderDetailQueryDTO);

    @GetMapping("/dine_in_order/get_order_detail/{orderGuid}")
    DineinOrderDetailRespDTO getOrderDetail(@PathVariable String orderGuid);

    /**
     * 根据订单Guid获取订单商品详情
     *
     * @param orderGuid 订单唯一Id
     * @return 订单和关联商品信息
     */
    @GetMapping("/dine_in_order/get_order_item_detail/{orderGuid}")
    DineinOrderDetailRespDTO getOrderItemDetailByOrderGuid(@PathVariable String orderGuid);

    @ApiOperation(value = "收款方式下拉", notes = "收款方式下拉")
    @GetMapping("/dine_in_order/list_payment_type_name")
    List<String> listPaymentTypeName();

    @PostMapping("/dine_in_order/order_list")
    Page<DineInOrderListRespDTO> orderList(DineInOrderListReqDTO dineInOrderListReqDTO);

    @PostMapping("/dine_in_order/print_item_detail")
    Boolean printItemDetail(SingleDataDTO singleDataDTO);

    @ApiOperation(value = "打印菜品复单", notes = "打印菜品复单")
    @PostMapping("/dine_in_order/print_item_repeat_order")
    boolean printItemRepeatOrder(@RequestBody CreateDineInOrderReqDTO itemRepeatOrderReqDTO);

    @PostMapping("/dine_in_order/print_pre_bill")
    Boolean printPreBill(SingleDataDTO singleDataDTO);

    @PostMapping("/dine_in_order/print_check_out")
    Boolean printCheckOut(SingleDataDTO singleDataDTO);

    @PostMapping("/face_pay/compensate")
    Boolean compensate(FacePayCompensateReqDTO facePayCompensateReqDTO);

    @PostMapping("/face_pay/estimate")
    EstimateItemRespDTO estimate(FacePayEstimateReqDTO facePayEstimateReqDTO);

    @PostMapping("/face_pay/return_estimate")
    Boolean returnEstimate(SingleDataDTO singleDataDTO);

    @PostMapping("/dine_in_order/check/islock")
    Boolean checkOrderIsLock(BaseOrderDTO baseOrderDTO);

    @PostMapping("/dine_in_order/add/version/")
    Boolean addVersion(String orderGuid);

    @PostMapping("/dine_in_order/get/version/")
    String getVersion(String orderGuid);

    @PostMapping("/dine_in_order/order/lock/")
    boolean lockOrder(OrderLockDTO orderLockDto);

    @PostMapping("/dine_in_order/order/unlock/")
    void unlockOrder(OrderLockDTO orderLockDto);

    @PostMapping("/dine_in_order/batch_save_offline_order")
    BatchFastOfflineOrderResp batchSaveFastOrder(BatchCreateFastFoodReqDTO fastFoodReqDTO);

    @PostMapping("/local/get_local/")
    LocalDTO getLocal(LocalQuery localQuery);

    @PostMapping("/local/save_local/")
    Map<String, String> saveLocal(LocalDTO localDTO);

    @PostMapping("/local/get_single/")
    LocalDTO getSingle(SingleQuery singleQuery);

    @GetMapping("/local/get_service/")
    String getService();

    @PostMapping("/dine_in_order/get_order_detail_4_add_item")
    DineinOrderDetailRespDTO getOrderDetail4AddItem(SingleDataDTO singleDataDTO);

    @PostMapping("/member/coupon_list")
    List<ResponseVolumeList> couponList(MemberCouponListReqDTO memberCouponListReqDTO);

    /**
     * 调整单-正餐订单列表
     * 正餐展示近30天已完结的订单数据
     *
     * @param query 关键字和门店guid
     * @return 正餐订单信息列表
     */
    @ApiOperation(value = "调整单-正餐订单列表")
    @PostMapping("/adjust_order/page_dine_and_fast_order")
    Page<AdjustByDineAndFastOrderRespDTO> pageDineAndFastOrder(@RequestBody AdjustByOrderListQuery query);

    /**
     * 调整单-查询订单商品
     * 标记商品是否调整过
     *
     * @param query 订单guid，门店，交易模式
     * @return 订单商品信息列表
     */
    @ApiOperation(value = "调整单-查询订单商品")
    @PostMapping("/adjust_order/list_order_item")
    AdjustByOrderRespDTO listOrderItem(@RequestBody AdjustByOrderItemQuery query);

    /**
     * 创建调整单
     */
    @PostMapping("/adjust_order/create")
    Long createAdjustOrder(@RequestBody AdjustOrderReqDTO reqDTO);

    /**
     * 查询调整单详情
     */
    @PostMapping("/adjust_order/query")
    AdjustOrderDetailRespDTO queryAdjustOrder(@RequestBody AdjustOrderQueryDTO queryDTO);

    /**
     * 调整单-分页列表
     *
     * @param queryDTO 门店guid
     * @return 调整单列表
     */
    @ApiOperation(value = "调整单-分页列表")
    @PostMapping("/adjust_order/page_adjust_order")
    Page<AdjustOrderRespDTO> pageAdjustOrder(@RequestBody AdjustOrderQueryDTO queryDTO);

    /**
     * 随行红包使用设置
     *
     * @param updateOrderReqDTO 订单guid
     * @return 结果
     */
    @ApiOperation(value = "随行红包使用设置")
    @PostMapping("/dine_in_order/set_follow_red_packet")
    Boolean setFollowRedPacket(@RequestBody UpdateOrderReqDTO updateOrderReqDTO);

    /**
     * 根据订单查询订单状态
     *
     * @param orderGuid 订单guid
     * @return 订单状态 (1：待支付 2：支付中 3：支付失败 4：支付成功 5：退款  6：已作废 7: 反结账)
     */
    @ApiOperation(value = "根据订单查询订单状态")
    @GetMapping("/dine_in_order/get_order_state_by_guid")
    Integer getOrderStateByGuid(@RequestParam("orderGuid") String orderGuid);

    /**
     * 根据订单查询订单金额
     *
     * @param orderGuid 订单guid
     * @return 订单金额
     */
    @ApiOperation(value = "根据订单查询订单金额")
    @GetMapping("/dine_in_order/get_order_fee_by_guid")
    BigDecimal getOrderFeeByGuid(@RequestParam("orderGuid") String orderGuid);

    @ApiOperation(value = "多次支付")
    @PostMapping("/dine_in_bill/multiple_pay")
    BillAggPayRespDTO multiplePay(@RequestBody BillMultiplePayReqDTO multiplePayReqDTO);

    @ApiOperation(value = "聚合支付部分退款")
    @PostMapping("/dine_in_bill/multiple_refund_agg_pay")
    BillRefundAggPayRespDTO multipleRefundAggPay(@RequestBody BillMultipleRefundAggPayReqDTO multipleRefundAggPayReqDTO);

    @Component
    @Slf4j
    class FastFoodFallBack implements FallbackFactory<DineInOrderClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public DineInOrderClientService create(Throwable throwable) {
            return new DineInOrderClientService() {

                @Override
                public DineinOrderDetailRespDTO getOrderItemDetailByOrderGuid(String orderGuid) {
                    log.error("getOrderItemDetailByOrderGuid异常，throwable={}", throwable.getMessage());
                    throw new BusinessException("getOrderItemDetailByOrderGuid异常!" + throwable.getMessage());
                }

                @Override
                public ResponseCardRechargeStatisticsVO getMemberCardRechargePage(RequestCardRechargePageQO request) {
                    log.error("getMemberCardRechargePage异常，throwable={}", throwable.getMessage());
                    throw new BusinessException("getMemberCardRechargePage异常!" + throwable.getMessage());
                }

                @Override
                public List<OrderTableInfoDTO> batchGetTableInfo(OrderGuidsDTO orderGuidsDTO) {
                    log.error("获取订单桌位信息FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("获取订单桌位信息失败!" + throwable.getMessage());
                }

                @Override
                public List<OrderTableInfoDTO> batchGetTableInfo2(OrderGuidsDTO orderGuidsDTO) {
                    log.error(HYSTRIX_PATTERN, "batchGetTableInfo2", JacksonUtils.writeValueAsString(orderGuidsDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Boolean updateRemark(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
                    log.error("更新整单备注FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("更新整单备注失败!" + throwable.getMessage());
                }

                @Override
                public Boolean updateGuestCount(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
                    log.error("更新就餐人数FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("更新就餐人数失败!" + throwable.getMessage());
                }

                @Override
                public Boolean cancelOrder(CancelOrderReqDTO cancelOrderReqDTO) {
                    log.error("作废订单FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("作废订单失败!" + throwable.getMessage());
                }

                @Override
                public DineinOrderDetailRespDTO getOrderDetail(OrderDetailQueryDTO orderDetailQueryDTO) {
                    log.error("获取订单详情FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("获取订单详情失败!" + throwable.getMessage());
                }

                @Override
                public DineinOrderDetailRespDTO getOrderDetailForWx(OrderDetailQueryDTO orderDetailQueryDTO) {
                    log.error("获取订单详情失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("获取订单失败!" + throwable.getMessage());
                }

                @Override
                public DineinOrderDetailRespDTO getOrderDetail(String orderGuid) {
                    log.error("获取订单详情FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("获取订单详情失败!" + throwable.getMessage());
                }

                @Override
                public List<String> listPaymentTypeName() {
                    log.error("收款方式下拉FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("收款方式下拉失败!" + throwable.getMessage());
                }

                @Override
                public DineinOrderDetailRespDTO getOrderDetail4AddItem(SingleDataDTO singleDataDTO) {
                    log.error("桌台旁边获取订单详情FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("桌台旁边获取订单详情详情失败!" + throwable.getMessage());
                }

                @Override
                public Boolean printPreBill(SingleDataDTO singleDataDTO) {
                    log.error("打印预结单FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("打印预结单失败!" + throwable.getMessage());
                }

                @Override
                public Page<DineInOrderListRespDTO> orderList(DineInOrderListReqDTO dineInOrderListReqDTO) {
                    log.error("订单列表FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("订单列表失败!" + throwable.getMessage());
                }

                @Override
                public Boolean printItemDetail(SingleDataDTO singleDataDTO) {
                    log.error("打印菜品清单FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("打印菜品清单失败!" + throwable.getMessage());
                }

                @Override
                public boolean printItemRepeatOrder(CreateDineInOrderReqDTO itemRepeatOrderReqDTO) {
                    log.error("打印菜品复单FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("打印菜品复单失败!" + throwable.getMessage());
                }

                @Override
                public Boolean printCheckOut(SingleDataDTO singleDataDTO) {
                    log.error("打印结帐单FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("打印结帐单失败!" + throwable.getMessage());
                }

                @Override
                public Boolean compensate(FacePayCompensateReqDTO facePayCompensateReqDTO) {
                    log.error("人脸支付补偿FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("人脸支付补偿失败!" + throwable.getMessage());
                }

                @Override
                public EstimateItemRespDTO estimate(FacePayEstimateReqDTO facePayEstimateReqDTO) {
                    log.error("人脸支付估清FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("人脸支付估清失败!" + throwable.getMessage());
                }

                @Override
                public Boolean returnEstimate(SingleDataDTO singleDataDTO) {
                    log.error("人脸支付退估清FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("人脸支付退估清失败!" + throwable.getMessage());
                }

                @Override
                public Boolean checkOrderIsLock(BaseOrderDTO baseOrderDTO) {
                    log.error("检查订单锁失败 FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("检查订单锁失败!" + throwable.getMessage());
                }

                @Override
                public Boolean addVersion(String orderGuid) {
                    log.error("增加版本号失败 FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("增加版本号失败!" + throwable.getMessage());
                }

                @Override
                public String getVersion(String orderGuid) {
                    log.error("获取版本号失败 FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("获取版本号失败!" + throwable.getMessage());
                }

                @Override
                public boolean lockOrder(OrderLockDTO orderLockDto) {
                    log.error("订单加锁解败 FallBack,orderGuid -> {}，throwable={}", orderLockDto.getOrderGuid(), throwable
                            .getMessage());
                    throw new BusinessException("订单加锁失败!" + throwable.getMessage());
                }

                @Override
                public void unlockOrder(OrderLockDTO orderLockDto) {
                    log.error("订单解锁解败 FallBack,orderGuid -> {}，throwable={}", orderLockDto.getOrderGuid(), throwable
                            .getMessage());
                    throw new BusinessException("订单解锁失败!" + throwable.getMessage());
                }

                @Override
                public BatchFastOfflineOrderResp batchSaveFastOrder(BatchCreateFastFoodReqDTO fastFoodReqDTO) {
                    log.error("离线订单上传 {}", fastFoodReqDTO);
                    throw new BusinessException("离线订单上传" + throwable.getMessage());
                }

                @Override
                public LocalDTO getLocal(LocalQuery localQuery) {
                    log.error("本地化数据拉取 {}", localQuery);
                    throw new BusinessException("本地化数据拉取失败!" + throwable.getMessage());
                }

                @Override
                public Map<String, String> saveLocal(LocalDTO localDTO) {
                    log.error("本地化数据保存 {}", localDTO);
                    throw new BusinessException("本地化数据保存!" + throwable.getMessage());
                }

                @Override
                public LocalDTO getSingle(SingleQuery singleQuery) {
                    log.error("本地化单条数据拉取 {}", singleQuery);
                    throw new BusinessException("本地化单条数据拉取失败!" + throwable.getMessage());
                }

                @Override
                public String getService() {
                    log.error("校验服务是否可用");
                    throw new BusinessException("校验服务是否可用失败!" + throwable.getMessage());
                }

                @Override
                public List<ResponseVolumeList> couponList(MemberCouponListReqDTO memberCouponListReqDTO) {
                    log.error("会员优惠券列表FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("会员优惠券列表失败!" + throwable.getMessage());
                }

                @Override
                public Page<AdjustByDineAndFastOrderRespDTO> pageDineAndFastOrder(AdjustByOrderListQuery query) {
                    log.error(HYSTRIX_PATTERN, "pageDineAndFastOrder", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public AdjustByOrderRespDTO listOrderItem(AdjustByOrderItemQuery query) {
                    log.error(HYSTRIX_PATTERN, "listOrderItem", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Long createAdjustOrder(AdjustOrderReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "createAdjustOrder", JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(throwable));
                    GlobalTransactionalHandler.afterReturning();
                    throw new BusinessException("创建调整单失败 " + throwable.getMessage());
                }

                @Override
                public AdjustOrderDetailRespDTO queryAdjustOrder(AdjustOrderQueryDTO queryDTO) {
                    log.error(HYSTRIX_PATTERN, "queryAdjustOrder", JacksonUtils.writeValueAsString(queryDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<AdjustOrderRespDTO> pageAdjustOrder(AdjustOrderQueryDTO queryDTO) {
                    log.error(HYSTRIX_PATTERN, "pageAdjustOrder", JacksonUtils.writeValueAsString(queryDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Boolean setFollowRedPacket(UpdateOrderReqDTO updateOrderReqDTO) {
                    log.error(HYSTRIX_PATTERN, "setFollowRedPacket", JacksonUtils.writeValueAsString(updateOrderReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Integer getOrderStateByGuid(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "getOrderStateByGuid", orderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public BigDecimal getOrderFeeByGuid(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "getOrderFeeByGuid", orderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public BillAggPayRespDTO multiplePay(BillMultiplePayReqDTO multiplePayReqDTO) {
                    log.error(HYSTRIX_PATTERN, "multiplePay", JacksonUtils.writeValueAsString(multiplePayReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public BillRefundAggPayRespDTO multipleRefundAggPay(BillMultipleRefundAggPayReqDTO multipleRefundAggPayReqDTO) {
                    log.error(HYSTRIX_PATTERN, "multipleRefundAggPay", JacksonUtils.writeValueAsString(multipleRefundAggPayReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
