package com.holderzone.holder.saas.aggregation.app.service.feign.msg;

import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MsgClientService
 * @date 2018/09/16 下午9:39
 * @description 短消息服务
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "base-service", fallbackFactory = MsgClientService.MsgFallBack.class)
public interface MsgClientService {

    @PostMapping("/message/sendMessage")
    boolean sendMessage(@RequestBody MessageDTO message);

    @Slf4j
    @Component
    class MsgFallBack implements FallbackFactory<MsgClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public MsgClientService create(Throwable throwable) {
            return new MsgClientService() {

                @Override
                public boolean sendMessage(MessageDTO message) {
                    log.error(HYSTRIX_PATTERN, "sendMessage", JacksonUtils.writeValueAsString(message),
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException("推送消息至安卓失败" + throwable.getMessage());
                }
            };
        }
    }

}
