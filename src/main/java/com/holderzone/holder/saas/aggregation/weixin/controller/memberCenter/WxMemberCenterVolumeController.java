package com.holderzone.holder.saas.aggregation.weixin.controller.memberCenter;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.memberCenter.WxMemberCenterVolumeService;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolume;
import com.holderzone.saas.store.dto.weixin.member.WxMemberInfoVolumeDetailsRespDTO;
import com.holderzone.saas.store.dto.weixin.member.WxMemberVolumeInfoListReqDTO;
import com.holderzone.saas.store.dto.weixin.member.WxVolumeDetailReqDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;

@RestController
@RequestMapping(value = "/wx_member_center_volume")
@Slf4j
@Api(description = "会员中心：优惠券")
public class WxMemberCenterVolumeController {

    private final WxMemberCenterVolumeService wxMemberCenterVolumeService;

    @Autowired
    public WxMemberCenterVolumeController(WxMemberCenterVolumeService wxMemberCenterVolumeService) {
        this.wxMemberCenterVolumeService = wxMemberCenterVolumeService;
    }

    @ApiOperation("会员优惠券列表")
    @PostMapping(value = "/volume_list")
    public Result<ResponseMemberInfoVolume> volumeInfoList(@RequestBody WxMemberVolumeInfoListReqDTO wxMemberVolumeInfoListReqDTO) {
        log.info("优惠券列表入参:{}", JacksonUtils.writeValueAsString(wxMemberVolumeInfoListReqDTO));
        return Result.buildSuccessResult(wxMemberCenterVolumeService.volumeInfoList(wxMemberVolumeInfoListReqDTO));
    }

    @ApiOperation("优惠券详情")
    @PostMapping(value = "/volume_details")
    public Result<WxMemberInfoVolumeDetailsRespDTO> volumeCodeDetails(@RequestBody WxVolumeDetailReqDTO wxVolumeDetailReqDTO) {
        log.info("优惠券详情入参:{}", JacksonUtils.writeValueAsString(wxVolumeDetailReqDTO));
        WxMemberInfoVolumeDetailsRespDTO volumeDetail = wxMemberCenterVolumeService.volumeCodeDetails(wxVolumeDetailReqDTO);
        if (volumeDetail != null) {
            ArrayList<String> desc = new ArrayList<>();
            Integer volumeType = volumeDetail.getVolumeType();
            BigDecimal volumeMoney = volumeDetail.getVolumeMoney();
            String volumeDesc = volumeDesc(volumeType, volumeDetail);
            if (!StringUtils.isEmpty(volumeDesc)) {
                desc.add(volumeDesc);
            }
            if (volumeDetail.getUseThreshold() == 0) {
                desc.add("无门槛");
            } else {
                desc.add("满" + volumeDetail.getUseThresholdFull() + "可用");
            }

            if (!StringUtils.isEmpty(volumeDetail.getMayUseNum())) {
                desc.add("每次可用" + volumeDetail.getMayUseNum() + "张");
            }
            log.info("优惠券详情封装:{}", volumeDetail);
            if (volumeDetail.getIsUseAlone() != null && volumeDetail.getIsUseAlone() == 1) {
                desc.add("仅原价购买商品时使用");
            }

            volumeDetail.setVolumeDescList(desc);
        }
        return Result.buildSuccessResult(volumeDetail);
    }

    private String volumeDesc(Integer volumeType, WxMemberInfoVolumeDetailsRespDTO volumeDetail) {
        //兼容非空
        BigDecimal volumeMoney = volumeDetail.getVolumeMoney();
        if (volumeType == 0 && volumeMoney != null) {
//			return "代金券￥" + volumeMoney;
            return "代金券￥" + volumeMoney.doubleValue();
        } else if (volumeType == 3) {
            return "商品券";
//			String volumeInfoName = volumeDetail.getVolumeInfoName();
//			if (!StringUtils.isEmpty(volumeInfoName)) {
//				if (volumeInfoName.endsWith("券")) {
//					return volumeInfoName;
//				}else{
//					return volumeInfoName + "券";
//				}
//			}
        }
        return null;
    }
}
