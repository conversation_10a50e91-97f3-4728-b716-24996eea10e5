package com.holderzone.holder.saas.aggregation.weixin.mapper;

import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreWebSocketUserDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

import javax.websocket.Session;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WebSocketUserMapper
 * @date 2019/3/22
 */
@Mapper(componentModel = "spring")
@Component
public interface WebSocketUserMapper {

	@Mappings({
			@Mapping(target = "wxStoreConsumerDTO",source = "wxStoreConsumerDTO"),
			@Mapping(target = "sid",source = "sid")
	})
	WxStoreWebSocketUserDTO getWxStoreWebSocketUser(WxStoreConsumerDTO wxStoreConsumerDTO, String sid);
}
