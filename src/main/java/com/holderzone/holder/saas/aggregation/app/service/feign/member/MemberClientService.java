package com.holderzone.holder.saas.aggregation.app.service.feign.member;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.member.MemberRechargeDTO;
import com.holderzone.saas.store.dto.member.TransactionRecordDTO;
import com.holderzone.saas.store.dto.member.common.BaseMemberDTO;
import com.holderzone.saas.store.dto.member.hsm.HsmAggPayRespDTO;
import com.holderzone.saas.store.dto.member.hsm.HsmRechargeReqDTO;
import com.holderzone.saas.store.dto.member.request.CreatMemberDTO;
import com.holderzone.saas.store.dto.member.request.ForgetPassWdReqDTO;
import com.holderzone.saas.store.dto.member.request.ModifiPassWdReqDTO;
import com.holderzone.saas.store.dto.member.request.UpdateMemberReqDTO;
import com.holderzone.saas.store.dto.member.response.BaseMemberRespDTO;
import com.holderzone.saas.store.dto.member.response.TransactionRecordRespDTO;
import com.holderzone.saas.store.dto.pay.AggPayRespDTO;
import com.holderzone.saas.store.dto.pay.CardRechargeCashInfoQO;
import com.holderzone.saas.store.dto.pay.SaasPollingDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberClientService
 * @date 2018/09/16 下午9:39
 * @description 会员调用服务接口
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-store-member", fallbackFactory = MemberClientService.MemberFallback.class)
public interface MemberClientService {

    @PostMapping(value = "/hsm_member/print_recharge")
    Boolean printRecharge(@RequestBody CardRechargeCashInfoQO cardRechargeCashInfoQO);

    @PostMapping(value = "/hsm_member/recharge")
    HsmAggPayRespDTO recharge(@RequestBody HsmRechargeReqDTO hsmRechargeReqDTO);

    @PostMapping(value = "/hsm_member/revoke_charge")
    Integer revokeCharge(@RequestBody SaasPollingDTO saasPollingDTO);

    @PostMapping("/member/get_member")
    BaseMemberRespDTO getMember(BaseMemberDTO baseMemberDTO);

    @PostMapping("/member/update_member")
    Boolean updateMember(UpdateMemberReqDTO updateMemberReqDTO);

    @PostMapping("/member/modifi_password")
    Boolean modifiPassWd(ModifiPassWdReqDTO modifiPassWdReqDTO);

    @PostMapping("/member/forget_password")
    Boolean forgetPassWd(ForgetPassWdReqDTO forgetPassWdReqDTO);

    @PostMapping("/member/create")
    Boolean creat(CreatMemberDTO creatMemberDTO);

    @PostMapping("/member/verification_code")
    Boolean verificationCode(BaseMemberDTO baseMemberDTO);

    @PostMapping("/member/transaction_record")
    Page<TransactionRecordRespDTO> queryTransactionRecord(TransactionRecordDTO transactionRecordDTO);

    @PostMapping("/member/checkPrepaidRule")
    String checkPrepaidRule(MemberRechargeDTO memberRechargeDTO);

    @PostMapping("/member/validate")
    Boolean validateMember(ModifiPassWdReqDTO modifiPassWdReqDTO);

    @PostMapping("/member/charge")
    AggPayRespDTO charge(MemberRechargeDTO memberRechargeDTO);

    @Component
    class MemberFallback implements FallbackFactory<MemberClientService> {
        private static final Logger logger = LoggerFactory.getLogger(MemberClientService.MemberFallback.class);

        @Override
        public MemberClientService create(Throwable throwable) {
            return new MemberClientService() {


                @Override
                public Boolean printRecharge(CardRechargeCashInfoQO cardRechargeCashInfoQO) {
                    logger.error("printRecharge异常：{}", throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public HsmAggPayRespDTO recharge(HsmRechargeReqDTO hsmRechargeReqDTO) {
                    logger.error("新会员充值异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Integer revokeCharge(SaasPollingDTO saasPollingDTO) {
                    logger.error("新会员充值撤销发生异常，入参：{}，异常：{}", JacksonUtils.writeValueAsString(saasPollingDTO), throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public AggPayRespDTO charge(MemberRechargeDTO memberRechargeDTO) {
                    logger.error("会员充值异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public BaseMemberRespDTO getMember(BaseMemberDTO baseMemberDTO) {
                    logger.error("获取会员信息异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean updateMember(UpdateMemberReqDTO updateMemberReqDTO) {
                    logger.error("修改会员信息异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean modifiPassWd(ModifiPassWdReqDTO modifiPassWdReqDTO) {
                    logger.error("修改会员密码异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean forgetPassWd(ForgetPassWdReqDTO forgetPassWdReqDTO) {
                    logger.error("会员忘记密码异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean creat(CreatMemberDTO creatMemberDTO) {
                    logger.error("注册会员异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean verificationCode(BaseMemberDTO baseMemberDTO) {
                    logger.error("验证验证码异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Page<TransactionRecordRespDTO> queryTransactionRecord(TransactionRecordDTO transactionRecordDTO) {
                    logger.error("获取会员交易记录异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public String checkPrepaidRule(MemberRechargeDTO memberRechargeDTO) {
                    logger.error("校验充值规则异常：{}", throwable.getMessage());
//                    throw new RuntimeException(throwable.getMessage());
                    return throwable.getMessage();
                }

                @Override
                public Boolean validateMember(ModifiPassWdReqDTO modifiPassWdReqDTO) {
                    logger.error("校验密码：{}", throwable.getMessage());
//                    throw new RuntimeException(throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }
            };
        }
    }
}
