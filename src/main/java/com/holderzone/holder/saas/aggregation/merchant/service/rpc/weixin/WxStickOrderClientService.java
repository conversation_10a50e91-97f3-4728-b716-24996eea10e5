package com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.weixin.req.WxStickModelOrderDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickOrderRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickOrderClientService
 * @date 2019/03/15 18:08
 * @description 微信桌贴购买clientService
 * @program holder-saas-store
 */
@Component
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = WxStickOrderClientService.WxStickOrderFallBack.class)
public interface WxStickOrderClientService {
    @PostMapping("/wx_stick_order/order")
    WxStickOrderRespDTO order(WxStickModelOrderDTO wxStickModelOrderDTO);

    @PostMapping("/wx_stick_order/polling")
    WxStickOrderRespDTO polling(WxStickOrderRespDTO wxStickOrderRespDTO);

    @Component
    @Slf4j
    class WxStickOrderFallBack implements FallbackFactory<WxStickOrderClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public WxStickOrderClientService create(Throwable throwable) {
            return new WxStickOrderClientService() {

                @Override
                public WxStickOrderRespDTO order(WxStickModelOrderDTO wxStickModelOrderDTO) {
                    if (log.isWarnEnabled()) {
                        log.warn(HYSTRIX_PATTERN, "order", wxStickModelOrderDTO, ThrowableUtils.asString(throwable));
                    }
                    throwable.printStackTrace();
                    throw new BusinessException("调用微信服务异常！");
                }

                @Override
                public WxStickOrderRespDTO polling(WxStickOrderRespDTO wxStickOrderRespDTO) {
                    if (log.isWarnEnabled()) {
                        log.warn(HYSTRIX_PATTERN, "polling", wxStickOrderRespDTO, ThrowableUtils.asString(throwable));
                    }
                    throwable.printStackTrace();
                    throw new BusinessException("调用微信服务异常！");
                }
            };
        }
    }
}
