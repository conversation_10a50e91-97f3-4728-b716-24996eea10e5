package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.activity;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.dto.activity.common.VolumeInfoCommonRespDTO;
import com.holderzone.holder.saas.member.dto.activity.request.*;
import com.holderzone.holder.saas.member.dto.activity.response.*;
import com.holderzone.holder.saas.member.dto.rights.request.QueryVolumeDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description  会员客户端
 * @date 2019/5/20 17:50
 */
@Component
@FeignClient(name = "holder-saas-member-account",fallbackFactory =VolumeInfoClientService.VolumeInfoClientServiceFallBack.class )
public interface VolumeInfoClientService {
    /**
     * 查询优惠券信息
     * @param queryVolumeDTO
     * @return
     */
    @GetMapping(value = "/hsm-volume-info/queryVolumeInfo", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Page<VolumeInfoCommonRespDTO> queryOfRechargeSelected(@RequestBody QueryVolumeDTO queryVolumeDTO);

    /**
     *根据guid查询
     * @param guid
     * @return
     */
    @GetMapping(value = "/hsm-volume-info/{guid}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    HsmVolumeInfoupdateRespDTO queryByGuid(@PathVariable("guid") String guid);

    /**
     * 根据guid 查询商品券
     *
     * @param guid
     * @return
     */
    @GetMapping(value = "/hsm-volume-info/product/{guid}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    HsmProductVolumeInfoUpdateRespDTO queryProductByGuid(@PathVariable("guid") String guid);

    /**
     * 查询列表
     * @param hsmVolumeInfoReqDTO
     * @return
     */
    @GetMapping(value = "/hsm-volume-info/queryList", produces = MediaType.APPLICATION_JSON_UTF8_VALUE )
    Page<HsmVolumeInfoRespDTO>queryList(@RequestBody HsmVolumeInfoReqDTO hsmVolumeInfoReqDTO);


    /**
     * 保存
     * @param hsmVolumeInfoUpdateReqDTO
     * @return
     */
    @PostMapping(value = "/hsm-volume-info/save", produces = MediaType.APPLICATION_JSON_UTF8_VALUE )
    boolean save(HsmVolumeInfoUpdateReqDTO hsmVolumeInfoUpdateReqDTO);

    /**
     * 保存商品券
     *
     * @param hsmProductVolumeInfoUpdateReqDTO
     * @return
     */
    @PostMapping(value = "/hsm-volume-info/product/save", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    boolean productSave(HsmProductVolumeInfoUpdateReqDTO hsmProductVolumeInfoUpdateReqDTO);

    /**
     * 更新
     * @param hsmVolumeInfoUpdateRespDTO
     * @return
     */
    @PutMapping(value = "/hsm-volume-info/update", produces = MediaType.APPLICATION_JSON_UTF8_VALUE )
    boolean update(HsmVolumeInfoUpdateReqDTO hsmVolumeInfoUpdateRespDTO);

    /**
     * 更新
     *
     * @param hsmProductVolumeInfoUpdateReqDTO
     * @return
     */
    @PutMapping(value = "/hsm-volume-info/product/update", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    boolean productVolumeUpdate(HsmProductVolumeInfoUpdateReqDTO hsmProductVolumeInfoUpdateReqDTO);

    /**
     * 优惠劵操作 根据操作类型
     * @param guid
     * @param operateType
     * @return
     */
    @PutMapping(value = "/hsm-volume-info/{guid}/{operateType}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE )
    boolean operateByGuidAndOperateType(@PathVariable("guid")String guid, @PathVariable("operateType")Integer operateType);
    @GetMapping(value = "/hsm-volume-info//plus/{guid}")
    boolean volumePlus(@ApiParam("优惠券Guid") @PathVariable("guid") String guid, @ApiParam("优惠券加券数量") @RequestParam("plusNum") Integer plusNum);

    @GetMapping(value ="/hsm-volume-info/volume/detail" ,produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Page<VolumeDetailRespDTO> queryByVolumeDetail(@RequestBody VolumeDetailReqDTO volumeDetailReqDTO);

    @GetMapping(value = "/hsm-volume-info/query/third/validity", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Page<VolumeInfoCommonRespDTO> queryForThirdTime(@RequestBody QueryVolumeDTO queryVolumeDTO);

    @PostMapping(value = "/hsm-volume-info/volume/useDetail", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Page<VolumeUseDetailRespDTO> queryUseDetail(@RequestBody VolumeUseDetailReqDTO volumeUseDetailReqDTO);

    @PostMapping(value = "/hsm-volume-info/volume/useDetailAmount", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    VolumeUseDetailRespDTO sumUseDetailAmount(@RequestBody VolumeUseDetailReqDTO volumeUseDetailReqDTO);

    @PostMapping(value = "/hsm-volume-info/volume/sendDetail", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Page<VolumeSendDetailRespDTO> querySendDetail(@RequestBody VolumeSendDetailReqDTO volumeSendDetailReqDTO);

    @Slf4j
    @Component
    class VolumeInfoClientServiceFallBack implements FallbackFactory<VolumeInfoClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public VolumeInfoClientService create(Throwable throwable) {
            return new VolumeInfoClientService() {
                @Override
                public  Page<VolumeInfoCommonRespDTO> queryOfRechargeSelected(QueryVolumeDTO queryVolumeDTO) {
                    log.error(HYSTRIX_PATTERN, "queryOfRechargeSelected", JacksonUtils.writeValueAsString(queryVolumeDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询充值选项优惠劵信息失败");
                }

                @Override
                public HsmVolumeInfoupdateRespDTO queryByGuid(String guid) {
                    log.error(HYSTRIX_PATTERN, "queryByGuid", guid, ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询优惠劵信息失败");
                }

                @Override
                public HsmProductVolumeInfoUpdateRespDTO queryProductByGuid(String guid) {
                    log.error(HYSTRIX_PATTERN, "queryProductByGuid", guid, ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询商品券信息失败");
                }

                @Override
                public Page<HsmVolumeInfoRespDTO> queryList(HsmVolumeInfoReqDTO hsmVolumeInfoReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryList", JacksonUtils.writeValueAsString(hsmVolumeInfoReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询优惠劵列表信息失败");
                }
                @Override
                public boolean save(HsmVolumeInfoUpdateReqDTO hsmVolumeInfoUpdateRespDTO) {
                    log.error(HYSTRIX_PATTERN, "save", JacksonUtils.writeValueAsString(hsmVolumeInfoUpdateRespDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("优惠劵保存失败");
                }

                @Override
                public boolean productSave(HsmProductVolumeInfoUpdateReqDTO hsmProductVolumeInfoUpdateReqDTO) {
                    log.error(HYSTRIX_PATTERN, "save", JacksonUtils.writeValueAsString(hsmProductVolumeInfoUpdateReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("商品劵保存失败");
                }

                @Override
                public boolean update(HsmVolumeInfoUpdateReqDTO hsmVolumeInfoUpdateRespDTO) {
                    log.error(HYSTRIX_PATTERN, "update", JacksonUtils.writeValueAsString(hsmVolumeInfoUpdateRespDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("优惠劵更新失败");
                }

                @Override
                public boolean productVolumeUpdate(HsmProductVolumeInfoUpdateReqDTO hsmProductVolumeInfoUpdateReqDTO) {
                    log.error(HYSTRIX_PATTERN, "update", JacksonUtils.writeValueAsString(hsmProductVolumeInfoUpdateReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("商品劵更新失败");
                }

                @Override
                public boolean operateByGuidAndOperateType(String guid, Integer operateType) {
                    log.error(HYSTRIX_PATTERN, "operateByGuidAndOperateType", JacksonUtils.writeValueAsString(guid+operateType), ThrowableUtils.asString(throwable));
                    throw new BusinessException("优惠劵操作失败");
                }

                @Override
                public boolean volumePlus(String guid, Integer plusNum) {
                    log.error(HYSTRIX_PATTERN, "volumePlus", JacksonUtils.writeValueAsString(guid+plusNum), ThrowableUtils.asString(throwable));
                    throw new BusinessException("优惠劵加券失败");
                }

                @Override
                public Page<VolumeDetailRespDTO> queryByVolumeDetail(VolumeDetailReqDTO volumeDetailReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryByVolumeDetail", JacksonUtils.writeValueAsString(volumeDetailReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询优惠劵详情失败");
                }

                @Override
                public Page<VolumeInfoCommonRespDTO> queryForThirdTime(
                    QueryVolumeDTO queryVolumeDTO) {
                    log.error(HYSTRIX_PATTERN, "queryForThirdTime",
                        JacksonUtils.writeValueAsString(queryVolumeDTO),
                        ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询第三类有效期的卡券");
                }

                @Override
                public Page<VolumeUseDetailRespDTO> queryUseDetail(VolumeUseDetailReqDTO volumeUseDetailReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryUseDetail", JacksonUtils.writeValueAsString(volumeUseDetailReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询优惠劵核销明细失败");
                }

                @Override
                public VolumeUseDetailRespDTO sumUseDetailAmount(VolumeUseDetailReqDTO volumeUseDetailReqDTO) {
                    log.error(HYSTRIX_PATTERN, "sumUseDetailAmount", JacksonUtils.writeValueAsString(volumeUseDetailReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询优惠劵核销明细金额合计失败");
                }

                @Override
                public Page<VolumeSendDetailRespDTO> querySendDetail(VolumeSendDetailReqDTO volumeSendDetailReqDTO) {
                    log.error(HYSTRIX_PATTERN, "querySendDetail", JacksonUtils.writeValueAsString(volumeSendDetailReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询优惠劵发放明细失败");
                }
            };
        }
    }
}
