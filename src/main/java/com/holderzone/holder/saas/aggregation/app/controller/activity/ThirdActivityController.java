package com.holderzone.holder.saas.aggregation.app.controller.activity;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.ThirdActivityService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.TradeThirdActivityClientService;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityTypeDTO;
import com.holderzone.saas.store.dto.exception.InterfaceDeprecatedException;
import com.holderzone.saas.store.dto.member.activity.ThirdActivityInfoAndRecordRespDTO;
import com.holderzone.saas.store.dto.order.request.groupon.GrouponReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.trade.req.RecordThirdActivityInfoReqDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 第三方平台优惠活动
 */
@Slf4j
@RestController
@Api(tags = "第三方平台优惠活动")
@RequestMapping("/third_activity")
@AllArgsConstructor
public class ThirdActivityController {

    private final ThirdActivityService thirdActivityService;

    private final TradeThirdActivityClientService tradeThirdActivityClientService;


    @ApiOperation("查询进行中的第三方活动平台")
    @GetMapping("/query_third_type")
    public Result<List<ThirdActivityTypeDTO>> queryThirdType(String orderGuid) {
        return Result.buildSuccessResult(thirdActivityService.queryThirdType(orderGuid));
    }

    /**
     * 安卓端活动列表查询
     * 包含活动使用记录
     */
    @ApiOperation("安卓端活动列表查询")
    @GetMapping("/list_info_and_record")
    public Result<List<ThirdActivityInfoAndRecordRespDTO>> listInfoAndRecord(@RequestParam("orderGuid") String orderGuid,
                                                                             @RequestParam(value = "groupBuyType", required = false) Integer groupBuyType) {
        log.info("安卓端活动列表查询 入参：orderGuid={}", orderGuid);
        return Result.buildSuccessResult(thirdActivityService.listInfoAndRecord(orderGuid, groupBuyType));
    }

    /**
     * 保存单个第三方活动使用信息
     */
    @ApiOperation(value = "保存单个第三方活动使用信息")
    @PostMapping("/save_third_activity_info")
    public Result<Boolean> saveThirdActivityInfo(@RequestBody RecordThirdActivityInfoReqDTO reqDTO) {
        log.info("保存单个第三方活动使用信息 入参：reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        tradeThirdActivityClientService.saveThirdActivityInfo(reqDTO);
        return Result.buildEmptySuccess();
    }

    /**
     * 撤销第三方活动记录
     */
    @ApiOperation(value = "撤销第三方活动记录")
    @GetMapping("/revoke_third_activity_record")
    public Result<Boolean> revokeThirdActivityRecord(@RequestParam("orderGuid") String orderGuid, @RequestParam("grouponType") Integer grouponType) {
        log.info("撤销第三方活动记录 入参：orderGuid={},type={}", orderGuid, grouponType);
        tradeThirdActivityClientService.revokeThirdActivityRecord(orderGuid, grouponType);
        return Result.buildSuccessMsg("批量撤销成功");
    }

    /**
     * 批量记录第三方活动使用信息
     */
    @ApiOperation(value = "批量记录第三方活动使用信息")
    @PostMapping("/batch_save_third_activity_info")
    public Result<Boolean> batchSaveThirdActivityInfo(@RequestBody RecordThirdActivityInfoReqDTO reqDTO) {
        log.info("批量记录第三方活动使用信息 入参：reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        throw new InterfaceDeprecatedException();
    }

    /**
     * 撤销第三方活动-美团团购验券
     */
    @ApiOperation(value = "撤销第三方活动-美团团购验券")
    @PostMapping("/revoke_groupon")
    public Result<List<String>> revokeGroupon(@RequestBody GrouponReqDTO reqDTO) {
        log.info("撤销第三方活动-美团团购验券,入参：reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        throw new InterfaceDeprecatedException();
    }

    @ApiOperation(value = "预验券", notes = "预验券")
    @PostMapping(value = "/pre_check", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<MtCouponPreRespDTO> preCheck(@RequestBody CouPonPreReqDTO couPonPreReqDTO) {
        log.info("预验券入参：{}", JacksonUtils.writeValueAsString(couPonPreReqDTO));
        throw new InterfaceDeprecatedException();
    }

    /**
     * 美团团购验券
     */
    @ApiOperation(value = "美团团购验券", notes = "美团团购验券")
    @PostMapping("/verify_code")
    public Result<List<String>> verifyCode(@RequestBody GrouponReqDTO grouponReqDTO) {
        log.info("美团团购验券,入参={}", JacksonUtils.writeValueAsString(grouponReqDTO));
        throw new InterfaceDeprecatedException();
    }

    @PostMapping(value = "/exist_available_third_activity")
    Result<Boolean> existAvailableThirdActivity() {
        throw new InterfaceDeprecatedException();
    }
}
