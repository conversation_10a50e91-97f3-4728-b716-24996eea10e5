package com.holderzone.holder.saas.aggregation.merchant.controller.common;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.base.dto.region.RegionDTO;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.BaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className RegionController
 * @date 18-9-17 下午4:02
 * @description 地区相关接口
 * @program holder-saas-aggregation-merchant
 */
@RestController
@RequestMapping("/region")
@Api(description = "地区相关api")
public class RegionController {

    @Autowired
    private BaseService baseService;

    @GetMapping(value = "/province")
    @ApiOperation(value = "查询所有地区", response = Result.class)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_OTHER,description = "查询所有地区")
    public Result<List<RegionDTO>> listProvince() {
        return Result.buildSuccessResult(baseService.listProvince());
    }

    @GetMapping(value = "/city")
    @ApiOperation(value = "根据地区code查询市")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_OTHER,description = "根据地区code查询市")
    public Result<List<RegionDTO>> listCity(@RequestParam(name = "pCode") String pCode) {
        return Result.buildSuccessResult(baseService.listCity(pCode));
    }

    @GetMapping(value = "/district")
    @ApiOperation(value = "根据市区code查询下级")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_OTHER,description = "根据市区code查询下级")
    public Result<List<RegionDTO>> listDistrict(@RequestParam(name = "pCode") String pCode) {
        return Result.buildSuccessResult(baseService.listDistrict(pCode));
    }
}
