package com.holderzone.holder.saas.aggregation.app.support;

import com.holderzone.holder.saas.aggregation.app.anno.RecordOperate;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.account.NewMemberInfoClientService;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class TerminalMemberSupport {

    private final NewMemberInfoClientService memberInfoClientService;

    @RecordOperate
    public ResponseMemberAndCardInfoDTO loginAfterGetMemberInfo(RequestQueryStoreAndMemberAndCard request) {
        return memberInfoClientService.getMemberInfoAndCard(request);
    }
}
