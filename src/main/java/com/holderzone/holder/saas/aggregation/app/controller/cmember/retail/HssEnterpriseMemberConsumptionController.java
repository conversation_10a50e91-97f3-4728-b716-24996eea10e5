package com.holderzone.holder.saas.aggregation.app.controller.cmember.retail;


import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.member.HssMemberTerminalClientService;
import com.holderzone.holder.saas.member.terminal.dto.retail.RequestRetailPayInfo;
import feign.Param;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 企业会员消费记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-20
 */
@Slf4j
@RestController
@RequestMapping("/hss/member/consumption")
@Api("订单相关")
public class HssEnterpriseMemberConsumptionController {

    @Resource
    private HssMemberTerminalClientService hssMemberTerminalClientService;

    @ApiOperation("会员退款")
    @GetMapping("/refund")
    public Result<Boolean> refund(@Param("orderNum") String orderNum) {
        log.info("[商超]-[消费记录]--用户退款操作，入参orderNum={}", orderNum);
        return Result.buildSuccessResult(hssMemberTerminalClientService.refund(orderNum));
    }

    @ApiOperation("支付结算")
    @PostMapping("/pay")
    public Result<String> payOrder(@ApiParam(value = "支付请求DTO", required = true) @RequestBody RequestRetailPayInfo payReqDTO) {
        log.info("[商超]-[消费记录]-支付结算操作，入参payReqDTO={}", JacksonUtils.writeValueAsString(payReqDTO));
        //从请求头获取企业门店相关信息写入
        BeanUtils.copyProperties(UserContextUtils.get(), payReqDTO.getBaseInfoReqDTO());
        payReqDTO.getBaseInfoReqDTO().setOperatorAccount(UserContextUtils.getUserGuid());
        return Result.buildSuccessResult(hssMemberTerminalClientService.payOrder(payReqDTO));
    }

}
