package com.holderzone.holder.saas.aggregation.merchant.controller.rights;


import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.baseresource.HsmProductService;
import com.holderzone.holder.saas.member.dto.baseresource.request.HsmProductReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.request.ProductQueryReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.response.ProductRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import java.util.List;
import javax.annotation.Resource;

import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 菜品 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-27
 */
@RestController
@RequestMapping("/hsm/member/product")
@Api(description = "商品")
public class HsmMemberProductController {

    @Resource
    private HsmProductService iHsmProductService;

    /**
     * 同步商品
     *
     * @param productReqDTOList 列表
     */
    @ApiOperation(value = "同步商品", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, response = HsmProductReqDTO.class)
    @PostMapping("/syc/list")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "同步商品",action = OperatorType.SELECT)
    public Result syc(
            @ApiParam(value = "商品对象列表", required = true) @RequestBody List<HsmProductReqDTO> productReqDTOList) {
        return Result.buildSuccessResult(iHsmProductService.syc(productReqDTOList));
    }

    /**
     * 同步商品
     *
     * @param productReqDTO 列表
     */
    @ApiOperation(value = "同步商品", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, response = HsmProductReqDTO.class)
    @PostMapping("/syc")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "同步商品",action = OperatorType.SELECT)
    public Result syc(
            @ApiParam(value = "商品对象", required = true) @RequestBody HsmProductReqDTO productReqDTO) {
        return Result.buildSuccessResult(iHsmProductService.syc(productReqDTO));
    }

    /**
     * 查询列表数据
     *
     * @param productQueryReqDTO 查询条件
     * @return 列表数据
     */
    @ApiOperation(value = "查询列表数据", response = ProductRespDTO.class, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/query")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "查询列表数据",action = OperatorType.SELECT)
    public Result findPageByCondition(
            @ApiParam(value = "查询对象", required = true) @RequestBody ProductQueryReqDTO productQueryReqDTO) {
        return Result
                .buildSuccessResult(iHsmProductService.findPageByCondition(productQueryReqDTO));
    }

    /**
     * 删除菜品
     *
     * @param productKey
     * @param allianceid
     * @return
     */
    @ApiOperation(value = "根据外部关联的菜品ID和联盟ID删除菜品", notes = "根据外部关联的菜品ID和联盟ID删除菜品")
    @DeleteMapping("/{productKey}/{allianceid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "根据外部关联的菜品ID和联盟ID删除菜品",action = OperatorType.DELETE)
    public Result removeHsmProduct(@ApiParam("productKey") @PathVariable("productKey") String productKey,
                                   @ApiParam("allianceid") @PathVariable("allianceid") String allianceid) {
        return Result.buildSuccessResult(iHsmProductService.removeHsmProduct(productKey, allianceid));
    }


    @ApiOperation("保存菜品")
    @PostMapping("/save")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "保存菜品",action = OperatorType.ADD)
    public Result save(@RequestBody @Validated HsmProductReqDTO productReqDTO) {
        return Result.buildSuccessResult(iHsmProductService.save(productReqDTO));
    }

    @ApiOperation("修改菜品")
    @PostMapping("/modify")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "修改菜品")
    public Result modify(@RequestBody @Validated HsmProductReqDTO productReqDTO) {
        return Result.buildSuccessResult(iHsmProductService.modify(productReqDTO));
    }
}
