package com.holderzone.holder.saas.aggregation.app.service.feign.trade;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.BaseRespDTO;
import com.holderzone.saas.store.dto.common.RedisReqDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.inside.OrderGuidsDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelFreeItemReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.PriceChangeItemReqDTO;
import com.holderzone.saas.store.dto.order.request.item.*;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.trade.OrderDetailPushMqDTO;
import com.holderzone.saas.store.dto.trade.OrderItemDTO;
import com.holderzone.saas.store.dto.trade.req.PadModifyGuestsNoReqDTO;
import com.holderzone.saas.store.dto.trade.req.PadOrderPlacementReqDTO;
import com.holderzone.saas.store.dto.trade.req.PadPayInfoReqDTO;
import com.holderzone.saas.store.dto.trade.resp.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderItemClientService
 * @date 2018/09/06 15:02
 * @description 正餐订单远程调用
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = OrderItemClientService.FastFoodFallBack.class)
public interface OrderItemClientService {

    @PostMapping("/order_item/add_item")
    EstimateItemRespDTO batchAddItems(CreateDineInOrderReqDTO createDineInOrderReqDTO);

    @PostMapping("/order_item/return")
    BatchItemReturnOrFreeReqDTO returnItem(BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO);

    @PostMapping("/order_item/free")
    BatchItemReturnOrFreeReqDTO freeItem(BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO);

    @PostMapping("/order_item/cancel_free")
    Boolean cancelFree(CancelFreeItemReqDTO cancelFreeItemReqDTO);

    @PostMapping("/order_item/serve_item")
    Boolean batchServeItem(ServeItemReqDTO serveItemReqDTO);

    @PostMapping("/order_item/urge")
    Boolean batchUrgeItem(UpdateItemStateReqDTO updateItemStateReqDTO);

    @PostMapping("/order_item/call_up")
    Boolean callUp(UpdateItemStateReqDTO updateItemStateReqDTO);

    @PostMapping("/order_item/change_price")
    Boolean changePrice(PriceChangeItemReqDTO priceChangeItemReqDTO);

    /**
     * pad下单
     *
     * @param orderPlacementReqDTO pad下单请求实体
     * @return 订单guid
     */
    @ApiOperation("pad下单")
    @PostMapping(value = "/pad_order/order_placement")
    PadOrderPlacementRespDTO orderPlacement(@RequestBody PadOrderPlacementReqDTO orderPlacementReqDTO);

    /**
     * pad购物车价格计算
     *
     * @param orderPlacementReqDTO 购物车商品数据
     * @return 购物车总价
     */
    @ApiOperation("购物车价格计算")
    @PostMapping(value = "/pad_order/calculate_shop_car")
    PadPriceRespDTO calculateShopCar(@RequestBody PadOrderPlacementReqDTO orderPlacementReqDTO);

    /**
     * 查询pad订单详情
     *
     * @param orderGuid 订单guid
     * @return pad订单详情
     */
    @ApiOperation("查询pad订单详情")
    @GetMapping(value = "/pad_order/query_pad_order_info")
    PadOrderInfoRespDTO queryPadOrderInfo(@RequestParam("orderGuid") String orderGuid);

    /**
     * 修改就餐人数
     *
     * @param modifyGuestsNoReqDTO 修改就餐人数请求实体
     * @return 结果
     */
    @ApiOperation("修改就餐人数")
    @PostMapping(value = "/pad_order/modify_guests_no")
    BaseRespDTO modifyGuestsNo(@RequestBody PadModifyGuestsNoReqDTO modifyGuestsNoReqDTO);

    /**
     * 获取pad支付二维码
     *
     * @param orderGuid 订单guid
     * @return pad支付二维码
     */
    @ApiOperation("获取pad支付二维码")
    @PostMapping(value = "/pad_order/get_pad_qr_code")
    PadQrCodeRespDTO getPadQrCode(@RequestParam("orderGuid") String orderGuid);

    /**
     * 保存pad支付信息到缓存
     *
     * @param padPayInfoReqDTO pad支付信息
     * @return Boolean
     */
    @ApiOperation("保存pad支付信息到缓存")
    @PostMapping(value = "/pad_order/save_pad_pay_info_to_redis")
    Boolean savePadPayInfoToRedis(@RequestBody PadPayInfoReqDTO padPayInfoReqDTO);

    /**
     * 获取缓存pad支付信息
     *
     * @param orderGuid 订单guid
     * @return pad支付信息
     */
    @ApiOperation("获取缓存pad支付信息")
    @GetMapping(value = "/pad_order/get_pad_pay_info")
    PadPayInfoReqDTO getPadPayInfo(@RequestParam("orderGuid") String orderGuid);

    /**
     * 获取订单并台的所有下单信息
     *
     * @param orderGuid 订单guid
     * @return pad下单消息
     */
    @ApiOperation("获取pad订单并台的所有下单信息")
    @GetMapping(value = "/pad_order/list_pad_order_info_on_combine")
    List<PadOrderRespDTO> listPadOrderInfoOnCombine(@RequestParam("orderGuid") String orderGuid);

    @ApiOperation(value = "获取订单详情", notes = "获取订单详情")
    @GetMapping("/order_detail/find_by_order_guid")
    OrderDTO findByOrderGuid(@RequestParam("orderGuid") String orderGuid);

    @ApiOperation(value = "获取多个订单详情", notes = "获取多个订单详情")
    @PostMapping("/order_detail/list_by_order_guid")
    List<OrderDTO> listByOrderGuid(@RequestBody SingleDataDTO query);

    @ApiOperation(value = "获取多个订单详情By订单编号", notes = "获取多个订单详情By订单编号")
    @PostMapping("/order_detail/list_by_order_no_and_store_guid")
    List<OrderDTO> listByOrderNoAndStoreGuid(@RequestBody SingleDataDTO query);

    @ApiOperation(value = "获取订单详情By订单编号", notes = "获取订单详情By订单编号")
    @GetMapping("/order_detail/find_by_order_no_and_store_guid")
    OrderDTO findByOrderNoAndStoreGuid(@RequestParam("orderNo") String orderNo,
                                       @RequestParam("storeGuid") String storeGuid);

    @ApiOperation(value = "获取订单商品", notes = "获取订单商品")
    @GetMapping("/order_detail/get_order_item")
    List<OrderItemDTO> getOrderItem(@RequestParam("orderGuid") Long orderGuid,
                                    @RequestParam("enterpriseGuid") String enterpriseGuid);

    @ApiOperation(value = "获取订单商品", notes = "获取订单商品")
    @PostMapping("/order_detail/get_order_items")
    List<OrderItemDTO> getOrderItems(@RequestBody OrderGuidsDTO orderGuidsDTO);

    @ApiOperation(value = "获取微信订单详情", notes = "获取微信订单详情")
    @PostMapping("/order_detail/find_by_wx_order_guid")
    OrderDetailPushMqDTO findByWxOrderGuid(@RequestBody OrderGuidsDTO orderGuidsDTO);

    /**
     * 保存pad转台信息
     *
     * @param redisReqDTO redis请求实体
     * @return 结果
     */
    @ApiOperation("保存pad转台信息")
    @PostMapping(value = "/pad_order/save_pad_turn_info")
    Boolean savePadTurnInfo(@RequestBody RedisReqDTO redisReqDTO);

    /**
     * 获取pad转台信息
     *
     * @param redisKey key
     * @return pad转台信息
     */
    @ApiOperation("获取pad转台信息")
    @GetMapping(value = "/pad_order/get_pad_turn_info")
    String getPadTurnInfo(@RequestParam("redisKey") String redisKey);

    /**
     * 根据桌台查询订单guid和人数
     *
     * @param tableGuid 桌台guid
     * @return 订单guid和人数
     */
    @ApiOperation("根据桌台查询订单guid和人数")
    @GetMapping(value = "/pad_order/query_order_and_guest_by_table")
    PadRebindRespDTO queryOrderAndGuestByTable(@RequestParam("tableGuid") String tableGuid);

    /**
     * pad退出登录处理
     * 撤销验券和删除支付信息
     *
     * @param billCalculateReqDTO 计算接口的入参，用于撤销验券
     */
    @ApiOperation(value = "pad退出登录处理")
    @PostMapping("/pad_order/pad_sign_out")
    Boolean padSignOut(@RequestBody BillCalculateReqDTO billCalculateReqDTO);

    /**
     * 获取所有订单的pad下单数据
     *
     * @param orderGuid 当前订单guid
     * @return pad下单数据
     */
    @ApiOperation("获取所有订单的pad下单数据")
    @GetMapping(value = "/pad_order/list_pad_order_dto_list")
    List<PadOrderRespDTO> listPadOrderDTOList(@RequestParam("orderGuid") String orderGuid);

    /**
     * 根据定单guid查询定单商品
     *
     * @param orderGuid 定单guid
     * @return 定单商品
     */
    @ApiOperation(value = "根据定单guid查询定单商品")
    @GetMapping("/order_item/query_item_by_order_guid")
    List<DineInItemDTO> queryItemByOrderGuid(@RequestParam("orderGuid") String orderGuid);

    /**
     * 根据并台主单guid查询定订单信息列表
     *
     * @param combineOrderGuid 并台主单guid
     * @return 订单信息列表
     */
    @ApiOperation("根据并台主单guid查询定订单信息列表")
    @GetMapping(value = "/order_detail/list_order_by_combine_order_guid")
    List<OrderDTO> listOrderByCombineOrderGuid(@RequestParam("combineOrderGuid") String combineOrderGuid);

    /**
     * 套餐子菜换菜
     */
    @PostMapping("/order_item/change_item")
    EstimateItemRespDTO changeGroupItem(@RequestBody ChangeGroupItemReqDTO changeGroupItemReqDTO);

    @ApiOperation(value = "撤销套餐子菜换菜")
    @PostMapping("/order_item/cancel_change_item")
    EstimateItemRespDTO cancelChangeGroupItem(@RequestBody ChangeGroupItemReqDTO changeGroupItemReqDTO);

    @ApiOperation("转菜")
    @PostMapping("/order_item/transfer_item")
    void transferItem(@RequestBody TransferItemReqDTO transferReq);

    @Component
    @Slf4j
    class FastFoodFallBack implements FallbackFactory<OrderItemClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public OrderItemClientService create(Throwable throwable) {
            return new OrderItemClientService() {

                @Override
                public EstimateItemRespDTO batchAddItems(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
                    log.error("正餐加菜FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("正餐加菜失败!");
                }

                @Override
                public BatchItemReturnOrFreeReqDTO returnItem(BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO) {
                    log.error("退货FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("退货失败!");
                }

                @Override
                public BatchItemReturnOrFreeReqDTO freeItem(BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO) {
                    log.error("赠送FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("赠送失败!");
                }

                @Override
                public Boolean cancelFree(CancelFreeItemReqDTO cancelFreeItemReqDTO) {
                    log.error("取消赠送FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("取消赠送失败!");
                }

                @Override
                public Boolean batchServeItem(ServeItemReqDTO serveItemReqDTO) {
                    log.error("划菜FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("划菜失败!");
                }

                @Override
                public Boolean batchUrgeItem(UpdateItemStateReqDTO updateItemStateReqDTO) {
                    log.error("催菜FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("催菜失败!");
                }

                @Override
                public Boolean callUp(UpdateItemStateReqDTO updateItemStateReqDTO) {
                    log.error("叫起FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("叫起失败!");
                }

                @Override
                public Boolean changePrice(PriceChangeItemReqDTO priceChangeItemReqDTO) {
                    log.error("修改价格失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("修改价格失败!");
                }

                @Override
                public PadOrderPlacementRespDTO orderPlacement(PadOrderPlacementReqDTO orderPlacementReqDTO) {
                    log.error(HYSTRIX_PATTERN, "orderPlacement", JacksonUtils.writeValueAsString(orderPlacementReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PadPriceRespDTO calculateShopCar(PadOrderPlacementReqDTO orderPlacementReqDTO) {
                    log.error(HYSTRIX_PATTERN, "calculateShopCar", JacksonUtils.writeValueAsString(orderPlacementReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PadOrderInfoRespDTO queryPadOrderInfo(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "queryPadOrderInfo", orderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public BaseRespDTO modifyGuestsNo(PadModifyGuestsNoReqDTO modifyGuestsNoReqDTO) {
                    log.error(HYSTRIX_PATTERN, "modifyGuestsNo", JacksonUtils.writeValueAsString(modifyGuestsNoReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PadQrCodeRespDTO getPadQrCode(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "getPadQrCode", orderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Boolean savePadPayInfoToRedis(PadPayInfoReqDTO padPayInfoReqDTO) {
                    log.error(HYSTRIX_PATTERN, "savePadPayInfoToRedis", JacksonUtils.writeValueAsString(padPayInfoReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PadPayInfoReqDTO getPadPayInfo(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "getPadPayInfo", orderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PadOrderRespDTO> listPadOrderInfoOnCombine(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "listPadOrderInfoOnCombine", orderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public OrderDTO findByOrderGuid(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "findByOrderGuid", orderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<OrderDTO> listByOrderGuid(SingleDataDTO query) {
                    log.error(HYSTRIX_PATTERN, "listByOrderGuid", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<OrderDTO> listByOrderNoAndStoreGuid(SingleDataDTO query) {
                    log.error(HYSTRIX_PATTERN, "listByOrderNoAndStoreGuid", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public OrderDTO findByOrderNoAndStoreGuid(String orderNo, String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "findByOrderNoAndStoreGuid", orderNo + "-" + storeGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<OrderItemDTO> getOrderItem(Long orderGuid, String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN, "getOrderItem", orderGuid + "、" + enterpriseGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<OrderItemDTO> getOrderItems(OrderGuidsDTO orderGuidsDTO) {
                    log.error(HYSTRIX_PATTERN, "getOrderItems", JacksonUtils.writeValueAsString(orderGuidsDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public OrderDetailPushMqDTO findByWxOrderGuid(OrderGuidsDTO orderGuidsDTO) {
                    log.error(HYSTRIX_PATTERN, "findByWxOrderGuid", JacksonUtils.writeValueAsString(orderGuidsDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Boolean savePadTurnInfo(RedisReqDTO redisReqDTO) {
                    log.error(HYSTRIX_PATTERN, "savePadTurnInfo", JacksonUtils.writeValueAsString(redisReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public String getPadTurnInfo(String redisKey) {
                    log.error(HYSTRIX_PATTERN, "getPadTurnInfo", redisKey,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PadRebindRespDTO queryOrderAndGuestByTable(String tableGuid) {
                    log.error(HYSTRIX_PATTERN, "queryOrderAndGuestByStoreAndTable", tableGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Boolean padSignOut(BillCalculateReqDTO billCalculateReqDTO) {
                    log.error(HYSTRIX_PATTERN, "padSignOut", JacksonUtils.writeValueAsString(billCalculateReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PadOrderRespDTO> listPadOrderDTOList(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "listPadOrderDTOList", orderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<DineInItemDTO> queryItemByOrderGuid(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "queryItemByOrderGuid", orderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<OrderDTO> listOrderByCombineOrderGuid(String combineOrderGuid) {
                    log.error(HYSTRIX_PATTERN, "listOrderByCombineOrderGuid", combineOrderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public EstimateItemRespDTO changeGroupItem(ChangeGroupItemReqDTO changeGroupItemReqDTO) {
                    log.error(HYSTRIX_PATTERN, "changeGroupItem", JacksonUtils.writeValueAsString(changeGroupItemReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public EstimateItemRespDTO cancelChangeGroupItem(ChangeGroupItemReqDTO changeGroupItemReqDTO) {
                    log.error(HYSTRIX_PATTERN, "cancelChangeGroupItem", JacksonUtils.writeValueAsString(changeGroupItemReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void transferItem(TransferItemReqDTO transferReq) {
                    log.error(HYSTRIX_PATTERN, "transferItem", JacksonUtils.writeValueAsString(transferReq),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

            };
        }
    }
}
