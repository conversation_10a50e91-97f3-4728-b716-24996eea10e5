package com.holderzone.holder.saas.aggregation.app.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @date 2024/06/11
 * @description ipass配置类
 */
@Data
@RefreshScope
@Configuration
public class IPassInvoiceConfig {

    /**
     * 校验发票条件
     */
    @Value("${ipass.receiptValidate}")
    private String receiptValidate;

    /**
     * 查询认证结果
     */
    @Value("${ipass.authResult}")
    private String authResult;

    /**
     * 短信验证登录
     */
    @Value("${ipass.noteLogin}")
    private String noteLogin;

    /**
     * 生成认证url
     */
    @Value("${ipass.authenticationUrl}")
    private String authenticationUrl;

    /**
     * 查询剩余可开额度
     */
    @Value("${ipass.queryResidueLimit}")
    private String queryResidueLimit;

    /**
     * 生成订单发票url
     */
    @Value("${ipass.orderInvoiceUrl}")
    private String orderInvoiceUrl;

    /**
     * apiKey
     */
    @Value("${ipass.apiKey}")
    private String apiKey;

    /**
     * apiSecret
     */
    @Value("${ipass.apiSecret}")
    private String apiSecret;

    /**
     * 请在管理后台配置数电发票开票信息
     */
    public static final Integer NOT_CONFIGURED_ERROR_CODE = 10000;

    /**
     * 请先开通电子发票功能
     */
    public static final Integer NOT_OPEN_ERROR_CODE = 20003;

    /**
     * 未查询到门店映射，请联系管理员
     */
    public static final Integer STORE_MAPPING_ERROR_CODE = 20004;

    /**
     * 开票金额已达上限
     */
    public static final Integer AMOUNT_LIMIT_ERROR_CODE = 30000;

    /**
     * 未登录
     */
    public static final Integer NOT_LOGIN_ERROR_CODE = 40000;

    /**
     * 未认证
     */
    public static final Integer NOT_AUTHORIZED_ERROR_CODE = 50000;

    /**
     * code
     */
    public static final String CODE = "code";

    /**
     * message
     */
    public static final String MESSAGE = "message";

    /**
     * data
     */
    public static final String DATA = "data";

    /**
     * renson
     */
    public static final String RESSION = "ression";

    /**
     * 成功标识
     */
    public static final String SUCCESS_CODE = "0";

    /**
     * 失败标识
     */
    public static final String FAIL_CODE = "-1";

    /**
     * 短信发送成功标识
     */
    public static final String SMS_SUCCESS_CODE = "2001";

    /**
     * 餐饮编码
     */
    public static final String CATERING_CODE = "3070401000000000000";

    /**
     * 餐饮统称
     */
    public static final String CATERING_NAME = "餐饮服务";
}