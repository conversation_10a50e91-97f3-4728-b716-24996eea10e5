package com.holderzone.holder.saas.aggregation.app.controller.boss;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.PrintService;
import com.holderzone.holder.saas.aggregation.app.service.feign.PrintClientService;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.cloud.CloudPrinterDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/26
 * @description 老板助手云打印
 */
@Slf4j
@RestController
@AllArgsConstructor
@Api(tags = "老板助手-云打印")
@RequestMapping("/boss/cloud/print")
public class BoosPrintController {

    private final PrintClientService printClientService;

    private final PrintService printService;

    @ApiOperation(value = "添加打印机")
    @PostMapping("/add")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT, description = "添加打印机")
    public Result<Void> addPrinter(@RequestBody CloudPrinterDTO cloudPrinterDTO) {
        log.info("[云打印][添加打印机]cloudPrinterDTO={}", JacksonUtils.writeValueAsString(cloudPrinterDTO));
        cloudPrinterDTO.setStaffGuid(UserContextUtils.getUserGuid());
        printClientService.addCloud(cloudPrinterDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "校验飞蛾打印机")
    @PostMapping("/check")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT, description = "校验飞蛾打印机")
    public Result<Void> checkPrinter(@RequestBody CloudPrinterDTO cloudPrinterDTO) {
        log.info("[云打印][校验飞蛾打印机]cloudPrinterDTO={}", JacksonUtils.writeValueAsString(cloudPrinterDTO));
        printClientService.checkCloudPrinter(cloudPrinterDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "通过门店guid获取该门店的打印机信息列表(不含菜品/区域绑定关系)")
    @PostMapping("/list")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT, description = "通过门店guid获取该门店的打印机信息列表")
    public Result<List<CloudPrinterDTO>> listCloudPrinters(@RequestBody PrinterDTO printerDTO) {
        log.info("[云打印][通过门店guid获取该门店的打印机信息列表]printerDTO={}", JacksonUtils.writeValueAsString(printerDTO));
        return Result.buildSuccessResult(printService.listCloudPrinters(printerDTO));
    }

    @ApiOperation(value = "获取某台打印机信息, 包括与菜品/区域绑定关系")
    @PostMapping("/query")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT, description = "获取某台打印机信息")
    public Result<CloudPrinterDTO> queryCloudPrinter(@RequestBody CloudPrinterDTO printerDTO) {
        log.info("[云打印][获取某台打印机信息]printerDTO={}", JacksonUtils.writeValueAsString(printerDTO));
        return Result.buildSuccessResult(printService.queryCloudPrinter(printerDTO));
    }

    @ApiOperation(value = "修改打印机信息/与菜品的绑定/与区域绑定")
    @PostMapping("/update")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT, description = "修改打印机信息/与菜品的绑定/与区域绑定")
    public Result<Void> updatePrinter(@RequestBody CloudPrinterDTO cloudPrinterDTO) {
        log.info("[云打印][修改打印机信息]cloudPrinterDTO={}", JacksonUtils.writeValueAsString(cloudPrinterDTO));
        cloudPrinterDTO.setStaffGuid(UserContextUtils.getUserGuid());
        printClientService.updateCloudPrinter(cloudPrinterDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "删除打印机")
    @PostMapping("/delete")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT, description = "删除打印机")
    public Result<Void> deletePrinter(@RequestBody PrinterDTO printerDTO) {
        log.info("[云打印][删除打印机]printerDTO={}", JacksonUtils.writeValueAsString(printerDTO));
        printClientService.deletePrinter(printerDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/test_print")
    @ApiOperation(value = "测试打印")
    public Result<Void> testPrint(@RequestBody CloudPrinterDTO cloudPrinterDTO) {
        log.info("[测试打印]cloudPrinterDTO={}", JacksonUtils.writeValueAsString(cloudPrinterDTO));
        printClientService.testPrint(cloudPrinterDTO);
        return Result.buildEmptySuccess();
    }

}
