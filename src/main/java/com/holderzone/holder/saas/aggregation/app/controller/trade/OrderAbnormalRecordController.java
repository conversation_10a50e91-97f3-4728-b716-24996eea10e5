package com.holderzone.holder.saas.aggregation.app.controller.trade;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.OrderAbnormalRecordClientService;
import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.dto.pay.AggPayReserveResultDTO;
import com.holderzone.saas.store.dto.pay.SaasPollingDTO;
import com.holderzone.saas.store.dto.trade.OrderAbnormalListReqDTO;
import com.holderzone.saas.store.dto.trade.OrderAbnormalRecordReqDTO;
import com.holderzone.saas.store.dto.trade.OrderAbnormalRecordRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/order_abnormal_record")
@Api(value = "异常订单接口")
@Slf4j
public class OrderAbnormalRecordController {

    @Resource
    private OrderAbnormalRecordClientService orderAbnormalRecordClientService;

    @ApiOperation("新增异常订单记录")
    @PostMapping("/save_abnormal_record_order")
    public Result<Integer> saveAbnormalRecordOrder(@RequestBody OrderAbnormalRecordReqDTO orderAbnormalRecordReqDTO) {
        return Result.buildSuccessResult(orderAbnormalRecordClientService.saveAbnormalRecordOrder(orderAbnormalRecordReqDTO));
    }

    @ApiOperation("查询异常订单列表")
    @PostMapping("/abnormal_order_list")
    public Result<Page<OrderAbnormalRecordRespDTO>> listAbnormalOrders(@RequestBody OrderAbnormalListReqDTO orderAbnormalListReqDTO) {
        return Result.buildSuccessResult(orderAbnormalRecordClientService.listAbnormalOrders(orderAbnormalListReqDTO));
    }

    @ApiOperation("查询支付结果")
    @PostMapping("/query_payment_result")
    public Result<AggPayPollingRespDTO> getPaymentResult(@RequestBody SaasPollingDTO saasPollingDTO) {
        return Result.buildSuccessResult(orderAbnormalRecordClientService.getPaymentResult(saasPollingDTO));
    }

    @ApiOperation("取消支付")
    @PostMapping("/cancel_payment")
    public Result<AggPayReserveResultDTO> cancelPayment(@RequestBody SaasPollingDTO saasPollingDTO) {
        return Result.buildSuccessResult(orderAbnormalRecordClientService.cancelPayment(saasPollingDTO));
    }
}
