package com.holderzone.holder.saas.aggregation.app.manage;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.constant.ExceptionConstants;
import com.holderzone.holder.saas.aggregation.app.service.feign.PrintClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.item.ItemClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInOrderClientService;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.print.*;
import com.holderzone.saas.store.enums.item.ItemTypeEnum;
import com.holderzone.saas.store.enums.print.BusinessTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 打印相关管理类
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PrintManager {

    private final DineInOrderClientService orderClient;
    private final PrintClientService printClient;
    private final ItemClientService itemClient;


    /**
     * @param printItemReq 请求
     * @return 返回
     */

    public DineinOrderDetailRespDTO listPrintItemsByOrderGuid(ListPrintItemReq printItemReq) {
        //根据订单Id查询订单商品信息
        String orderGuid = printItemReq.getOrderGuid();
        log.info("调用trade服务获取订单详情请求参数,orderGuid:{}", orderGuid);
        DineinOrderDetailRespDTO orderDetail = orderClient.getOrderDetail(orderGuid);
        log.info("调用trade服务获取订单详情返回参数,orderDetail:{}", JacksonUtils.writeValueAsString(orderDetail));
        if (ObjectUtil.isEmpty(orderDetail) || CollectionUtils.isEmpty(orderDetail.getDineInItemDTOS())) {
            throw new BusinessException(ExceptionConstants.ORDER_IS_NOT_EXISTS);
        }
        // 过滤可重打标签单的商品类型
        filterPrinterOrderItemType(orderDetail);
        // 获取所有可打印标签单的商品itemGuid
        List<String> printItemGuidList = Lists.newArrayList();
        appendPrinterItemGuids(printItemGuidList, orderDetail);
        printItemGuidList = printItemGuidList.stream().distinct().collect(Collectors.toList());
        log.info("订单中商品列表Id信息,itemGuidList:{}", printItemGuidList);
        if (CollectionUtils.isEmpty(printItemGuidList)) {
            throw new BusinessException(ExceptionConstants.UN_BIND_PRINTER_IN_ORDER);
        }
        //通过商品guid和门店guid查询对应spu的所有商品Guid
        Map<String, String> allItemGuidMap = getAllItemGuidList(printItemGuidList);
        log.info("所有商品列表Id信息,allItemGuidMap:{}", allItemGuidMap);
        List<String> allBindItemGuidList = new ArrayList<>(allItemGuidMap.values());
        // 查询标签打印机配置商品 并过滤可打印的商品
        filterPrinterItemGuid(printItemReq, allBindItemGuidList);
        log.info("查询已绑定的商品,过滤得到allBindItemGuidList:{}", allBindItemGuidList);
        // 过滤商品
        filterPrinterItem(orderDetail, allBindItemGuidList, allItemGuidMap);
        log.info("最终可打印的订单详情:{}", orderDetail);
        return orderDetail;
    }

    /**
     * 过滤可重打标签单的商品类型
     */
    private void filterPrinterOrderItemType(DineinOrderDetailRespDTO orderDetail) {
        List<DineInItemDTO> orderItemList = orderDetail.getDineInItemDTOS();
        // 标签打印机需要过滤套餐和称重商品
        List<Integer> availablePrintItemTypeList = Lists.newArrayList(ItemTypeEnum.MULTI_SKU.getCode(),
                ItemTypeEnum.SINGLE_UNWEIGH.getCode(), ItemTypeEnum.WEIGH.getCode(), ItemTypeEnum.PKG.getCode());
        orderItemList.removeIf(e -> !availablePrintItemTypeList.contains(e.getItemType()));
        List<DineinOrderDetailRespDTO> subOrderDetails = orderDetail.getSubOrderDetails();
        if (CollectionUtils.isNotEmpty(subOrderDetails)) {
            // 并单
            subOrderDetails.forEach(this::filterPrinterOrderItemType);
        }
        List<DineinOrderDetailRespDTO> otherOrderDetails = orderDetail.getOtherOrderDetails();
        if (CollectionUtils.isNotEmpty(otherOrderDetails)) {
            // 多单结账
            otherOrderDetails.forEach(this::filterPrinterOrderItemType);
        }
    }

    /**
     * 获取所有可打印标签单的商品itemGuid
     */
    private void appendPrinterItemGuids(List<String> printItemGuidList, DineinOrderDetailRespDTO orderDetail) {
        List<DineInItemDTO> orderItemList = orderDetail.getDineInItemDTOS();
        if (CollectionUtils.isEmpty(orderItemList)) {
            return;
        }
        List<String> orderItemGuids = orderItemList.stream()
                .map(DineInItemDTO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        printItemGuidList.addAll(orderItemGuids);
        // 套餐子商品
        List<String> allSubDineInItemGuidList = orderItemList.stream()
                .filter(e -> CollectionUtils.isNotEmpty(e.getPackageSubgroupDTOS()))
                .flatMap(e -> e.getPackageSubgroupDTOS().stream())
                .filter(e -> CollectionUtils.isNotEmpty(e.getSubDineInItemDTOS()))
                .flatMap(e -> e.getSubDineInItemDTOS().stream())
                .map(SubDineInItemDTO::getItemGuid)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(allSubDineInItemGuidList)) {
            printItemGuidList.addAll(allSubDineInItemGuidList);
        }
        List<DineinOrderDetailRespDTO> subOrderDetails = orderDetail.getSubOrderDetails();
        if (CollectionUtils.isNotEmpty(subOrderDetails)) {
            subOrderDetails.forEach(e -> appendPrinterItemGuids(printItemGuidList, e));
        }
        List<DineinOrderDetailRespDTO> otherOrderDetails = orderDetail.getOtherOrderDetails();
        if (CollectionUtils.isNotEmpty(otherOrderDetails)) {
            otherOrderDetails.forEach(e -> appendPrinterItemGuids(printItemGuidList, e));
        }
    }


    /**
     * 查询标签打印机配置商品 并过滤可打印的商品ItemGuids
     */
    private void filterPrinterItemGuid(ListPrintItemReq printItemReq, List<String> itemGuidList) {
        // 查询标签打印机配置商品 并过滤可打印的商品
        PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setStoreGuid(printItemReq.getStoreGuid());
        printerDTO.setDeviceId(printItemReq.getDeviceId());
        printerDTO.setBusinessType(BusinessTypeEnum.LABEL_PRINTER.getType());
        printerDTO.setArrayOfItemGuid(itemGuidList);
        log.info("查询打印机对应的绑定商品请求参数:{}", JacksonUtils.writeValueAsString(printerDTO));
        List<String> printerBindItemGuids = printClient.listPrinterByPrinterIdAndItemId(printerDTO);
        log.info("查询到的已绑定商品Id,bindItemGuidList:{}", printerBindItemGuids);
        if (CollUtil.isEmpty(printerBindItemGuids)) {
            throw new BusinessException(ExceptionConstants.UN_BIND_PRINTER_IN_ORDER);
        }
        itemGuidList.removeIf(e -> !printerBindItemGuids.contains(e));
    }

    /**
     * 过滤商品
     */
    private void filterPrinterItem(DineinOrderDetailRespDTO orderDetail,
                                   List<String> allBindItemGuidList,
                                   Map<String, String> allItemGuidMap) {
        List<DineInItemDTO> orderItemList = orderDetail.getDineInItemDTOS();
        // 过滤未绑定的非套餐商品
        orderItemList.removeIf(e -> ItemTypeEnum.PKG.getCode() != e.getItemType()
                && !allBindItemGuidList.contains(e.getItemGuid())
                && !allBindItemGuidList.contains(allItemGuidMap.getOrDefault(e.getItemGuid(), e.getItemGuid())));
        // 过滤未绑定的套餐商品
        Iterator<DineInItemDTO> iterator = orderItemList.iterator();
        while (iterator.hasNext()) {
            DineInItemDTO dineInItemDTO = iterator.next();
            if (ItemTypeEnum.PKG.getCode() != dineInItemDTO.getItemType()) {
                continue;
            }
            List<PackageSubgroupDTO> packageSubgroupList = dineInItemDTO.getPackageSubgroupDTOS();
            Iterator<PackageSubgroupDTO> packageSubgroupDTOIterator = packageSubgroupList.iterator();
            while (packageSubgroupDTOIterator.hasNext()) {
                PackageSubgroupDTO packageSubgroupDTO = packageSubgroupDTOIterator.next();
                List<SubDineInItemDTO> subDineInItemDTOS = packageSubgroupDTO.getSubDineInItemDTOS();
                subDineInItemDTOS.removeIf(e -> !allBindItemGuidList.contains(e.getItemGuid())
                        && !allBindItemGuidList.contains(allItemGuidMap.getOrDefault(e.getItemGuid(), e.getItemGuid())));
                if (CollectionUtils.isEmpty(subDineInItemDTOS)) {
                    packageSubgroupDTOIterator.remove();
                }
            }
            if (CollectionUtils.isEmpty(packageSubgroupList)) {
                iterator.remove();
            }
        }
        List<DineinOrderDetailRespDTO> subOrderDetails = orderDetail.getSubOrderDetails();
        if (CollectionUtils.isNotEmpty(subOrderDetails)) {
            subOrderDetails.forEach(e -> filterPrinterItem(e, allBindItemGuidList, allItemGuidMap));
        }
        List<DineinOrderDetailRespDTO> otherOrderDetails = orderDetail.getOtherOrderDetails();
        if (CollectionUtils.isNotEmpty(otherOrderDetails)) {
            otherOrderDetails.forEach(e -> filterPrinterItem(e, allBindItemGuidList, allItemGuidMap));
        }
    }


    private Map<String, String> getAllItemGuidList(List<String> itemGuidList) {
        ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(itemGuidList);
        log.info("调用item服务查询父级ItemGuid请求参数，itemStringListDTO：{}", JacksonUtils.writeValueAsString(itemStringListDTO));
        Map<String, String> itemMap = itemClient.queryParentItemGuidByItem(itemStringListDTO);
        log.info("调用item服务查询父级ItemGuid返回参数，itemMap：{}", JacksonUtils.writeValueAsString(itemMap));
        return itemMap;
    }

}

































