package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dynamic.datasource.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.constant.RedisConstants;
import com.holderzone.holder.saas.aggregation.weixin.service.HsmMemberService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.StorePayClientService;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.member.hsm.HsmRechargeReqDTO;
import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.dto.pay.SaasNotifyDTO;
import com.holderzone.saas.store.dto.pay.SaasPollingDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * Created by zhaohongyang on 2018/8/21.
 */
@Slf4j
@RestController
@RequestMapping("/hsm_member")
@Api(value = "新会员接口")
public class HsmMemberController {

    private final StorePayClientService storePayClientService;

    private final HsmMemberService hsmMemberService;

    private final RedisUtils redisUtils;

    @Autowired
    public HsmMemberController(StorePayClientService storePayClientService, HsmMemberService hsmMemberService, RedisUtils redisUtils) {
        this.storePayClientService = storePayClientService;
        this.hsmMemberService = hsmMemberService;
        this.redisUtils = redisUtils;
    }

    @ApiOperation(value = "会员充值接口（公众号）", notes = "会员充值接口（公众号）")
    @PostMapping(value = "/wx_recharge")
    public Result<WxPayRespDTO> wxRecharge(@RequestBody HsmRechargeReqDTO hsmRechargeReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("会员充值接口（公众号）入参：{}", JacksonUtils.writeValueAsString(hsmRechargeReqDTO));
        }
        try {
            payBeforeHandler(hsmRechargeReqDTO);
            WxPayRespDTO wxPayRespDTO = hsmMemberService.wechatRecharge(hsmRechargeReqDTO);
            return Result.buildSuccessResult(wxPayRespDTO);
        } catch (Exception e) {
            log.error("支付异常, e", e);
            return Result.buildFailResult(-1, "未开启充值，请联系商家");
        }
    }


    private void payBeforeHandler(HsmRechargeReqDTO hsmRechargeReqDTO) {
        UserContext userContext = UserContextUtils.get();
        userContext.setEnterpriseGuid(hsmRechargeReqDTO.getEnterpriseGuid());
        userContext.setStoreGuid(hsmRechargeReqDTO.getStoreGuid());
        userContext.setStoreName(hsmRechargeReqDTO.getStoreName());
        UserContextUtils.put(JacksonUtils.writeValueAsString(userContext));
        EnterpriseIdentifier.setEnterpriseGuid(hsmRechargeReqDTO.getEnterpriseGuid());
        log.info("会员充值，当前请求头信息：{}", UserContextUtils.getJsonStr());
        String msgKey = hsmRechargeReqDTO.getMsgKey();
        if (StringUtils.isEmpty(msgKey)) {
            return;
        }
        String cacheKey = String.format(RedisConstants.H5_PERSONAL_WECHAT_THIRD_OPENID_APP_ID_KEY, msgKey);
        Object cacheObj = redisUtils.get(cacheKey);
        if (Objects.nonNull(cacheObj)) {
            String[] split = cacheObj.toString().split(",");
            hsmRechargeReqDTO.setThirdAppId(split[0]);
            hsmRechargeReqDTO.setThirdOpenId(split[1]);
        }
    }

    @ApiOperation(value = "会员充值回调接口", notes = "会员充值回调接口", response = Boolean.class)
    @PostMapping(value = "/callback", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String callback(@RequestBody SaasNotifyDTO saasNotifyDTO) {
        if (log.isInfoEnabled()) {
            log.info("会员充值接口入参：{}", JacksonUtils.writeValueAsString(saasNotifyDTO));
        }
        return hsmMemberService.callback(saasNotifyDTO);
    }

    @PostMapping("/wechat/public/polling")
    @ApiOperation(value = "微信公众号支付轮询接口")
    public Result<AggPayPollingRespDTO> pollingWeChatPublic(@RequestBody SaasPollingDTO saasPollingDTO) {
        log.info("微信公众号支付轮询入参：{}", JacksonUtils.writeValueAsString(saasPollingDTO));
        return Result.buildSuccessResult(storePayClientService.pollingWeChatPublic(saasPollingDTO));
    }


}
