package com.holderzone.holder.saas.aggregation.app.service;

import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityTypeDTO;
import com.holderzone.saas.store.dto.member.activity.ThirdActivityInfoAndRecordRespDTO;

import java.util.List;

public interface ThirdActivityService {

    List<ThirdActivityInfoAndRecordRespDTO> listInfoAndRecord(String orderGuid, Integer groupBuyType);

    List<ThirdActivityTypeDTO> queryThirdType(String orderGuid);
}
