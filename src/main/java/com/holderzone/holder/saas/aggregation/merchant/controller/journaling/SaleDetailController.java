package com.holderzone.holder.saas.aggregation.merchant.controller.journaling;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.SyncExecutor;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.jou.SaleClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.journaling.ReportClientService;
import com.holderzone.holder.saas.aggregation.merchant.util.ReportValidateUtil;
import com.holderzone.saas.store.dto.journaling.req.ItemTypeDTO;
import com.holderzone.saas.store.dto.journaling.req.SaleDetailReportReqDTO;
import com.holderzone.saas.store.dto.journaling.req.SingleSaleDetailReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.ItemTypeRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.SaleDetailReportRespDTO;
import com.holderzone.saas.store.enums.locale.LocaleMessageEnum;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.time.LocalTime;
import java.util.Map;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderDetailController
 * @date 2019/05/28 17:52
 * @description 报表-订单明细controller
 * @program holder-saas-store
 */

@Api(description = "报表-销售明细controller")
@RestController
@RequestMapping("/sale_detail")
@Slf4j
public class SaleDetailController {


    private final SaleClientService saleClientService;
    private ReportClientService reportClientService;

    private final RedisTemplate redisTemplate;

    private final SyncExecutor syncExecutor;

    private final ExecutorService executorService;

    @Autowired
    public SaleDetailController(SaleClientService saleClientService, ReportClientService reportClientService, RedisTemplate redisTemplate, SyncExecutor syncExecutor,  ExecutorService executorService) {
        this.saleClientService = saleClientService;
        this.reportClientService = reportClientService;
        this.redisTemplate = redisTemplate;
        this.syncExecutor = syncExecutor;
        this.executorService = executorService;
    }

    @PostMapping("/page")
//    @Deprecated
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT, description = "页查询销售明细报表")
    public Result<Page<SaleDetailReportRespDTO>> pageSaleDetail(@RequestBody SaleDetailReportReqDTO saleDetailReportReqDTO) {
//        log.info("分页查询销售明细报表请求参数：{}", JacksonUtils.writeValueAsString(saleDetailReportReqDTO));
        if (CollectionUtils.isEmpty(saleDetailReportReqDTO.getStoreGuid())){
            throw  new BusinessException(LocaleUtil.getMessage(LocaleMessageEnum.NO_ACCESS_TO_STORE_DATA));
        }
//        //时间兼容
        setReqDateTime(saleDetailReportReqDTO);
//        List<SaleDetailReportRespDTO> saleDetailReportRespDTOS = saleClientService.pageSaleDetailAll(saleDetailReportReqDTO);
//        Comparator<SaleDetailReportRespDTO> comparator;
//        Comparator<SaleDetailReportRespDTO> ascComparator;
//        ascComparator = Comparator.comparing(SaleDetailReportRespDTO::getCreateTime);
//        comparator = 0 == 0 ? ascComparator.reversed() : ascComparator;
//        int skipCount = ((int) saleDetailReportReqDTO.getCurrentPage() - 1) * (int) saleDetailReportReqDTO.getPageSize();
//        List<SaleDetailReportRespDTO> collect = saleDetailReportRespDTOS.stream().sorted(comparator).skip(skipCount).limit((int) saleDetailReportReqDTO.getPageSize()).collect(Collectors.toList());
//
//        return Result.buildSuccessResult(new Page<>(saleDetailReportReqDTO.getCurrentPage(), saleDetailReportReqDTO.getPageSize(), saleDetailReportRespDTOS.size(), collect));
        Page<SaleDetailReportRespDTO> saleDetailReportRespDTOPage = saleClientService.pageEsSaleDetailAll(saleDetailReportReqDTO);
        return  Result.buildSuccessResult(saleDetailReportRespDTOPage);
    }

    @PostMapping("/item_type")
    @Deprecated
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT, description = "销售明细报表")
    public Result<Map<String, ItemTypeRespDTO>> itemType(@RequestBody ItemTypeDTO itemTypeDTO) {
        log.info("销售明细报表请求参数：{}", JacksonUtils.writeValueAsString(itemTypeDTO));
        Map<String, ItemTypeRespDTO> map = saleClientService.itemSearch(itemTypeDTO);
        return Result.buildSuccessResult(map);
    }

    @PostMapping("/single_page")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT, description = "分页查询销售明细报表")
    public Result<Page<SaleDetailReportRespDTO>> singlePageSaleDetail(@RequestBody SingleSaleDetailReqDTO singleSaleDetailReqDTO) {
        //门店兼容
        ReportValidateUtil.reportCompatibilityCheck(singleSaleDetailReqDTO);
        //时间兼容
        if(singleSaleDetailReqDTO.getBusinessEndDateTime()==null){
            singleSaleDetailReqDTO.setBusinessEndDateTime(singleSaleDetailReqDTO.getEndDate().atTime(LocalTime.MAX));
        }
        if(singleSaleDetailReqDTO.getBusinessStartDateTime()==null){
            singleSaleDetailReqDTO.setBusinessStartDateTime(singleSaleDetailReqDTO.getStartDate().atTime(LocalTime.MIN));
        }
        return Result.buildSuccessResult(saleClientService.singlePageSaleDetail(singleSaleDetailReqDTO));
    }

    @ApiOperation(value = "销售明细导出请求接口")
    @PostMapping("/sale_detail_export_oss")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT)
    public Result<Boolean> saleDetailExportOss(@RequestBody SaleDetailReportReqDTO saleDetailReportReqDTO) {
//        saleDetailReportReqDTO.setBusinessEndDateTime(LocalDateTime.of(saleDetailReportReqDTO.getEndDate(), LocalTime.MAX));
//        saleDetailReportReqDTO.setBusinessStartDateTime(LocalDateTime.of(saleDetailReportReqDTO.getStartDate(), LocalTime.MIN));
        log.info("销售明细导出请参数{},操作人id{}", JacksonUtils.writeValueAsString(saleDetailReportReqDTO), UserContextUtils.get().getUserGuid());
        //时间兼容
        setReqDateTime(saleDetailReportReqDTO);
        syncExecutor.syncUploadOssData(reportClientService, saleDetailReportReqDTO, UserContextUtils.getJsonStr());
        return Result.buildSuccessResult(Boolean.TRUE);
    }

    @ApiOperation(value = "销售明细导出获取OSS下载链接接口")
    @GetMapping("/get_order_details_oss_url")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT)
    public Result<String> getOrderDetailsOssUrl(@RequestParam("key") String key) {
        log.info("销售明细导出oss下载地址获取请求key{}", key);
        String result = reportClientService.getOrderDetailsOssUrl(key);
        log.info("返回url{}", result);
        return Result.buildSuccessResult(result);
    }

    private static void setReqDateTime(SaleDetailReportReqDTO businessSituationReqDTO) {
        if (businessSituationReqDTO.getBusinessEndDateTime() == null) {
            businessSituationReqDTO.setBusinessEndDateTime(businessSituationReqDTO.getEndDate().atTime(LocalTime.MAX));
        }
        if (businessSituationReqDTO.getBusinessStartDateTime() == null) {
            businessSituationReqDTO.setBusinessStartDateTime(businessSituationReqDTO.getStartDate().atTime(LocalTime.MIN));
        }
    }
}
