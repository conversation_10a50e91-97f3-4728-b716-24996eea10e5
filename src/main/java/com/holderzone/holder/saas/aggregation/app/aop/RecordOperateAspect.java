package com.holderzone.holder.saas.aggregation.app.aop;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.app.constant.Constant;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.OperateRecordClientService;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.saas.store.dto.trade.req.record.MemberOperateRecordReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

@Slf4j
@Aspect
@Component
public class RecordOperateAspect {

    @Autowired
    private OperateRecordClientService operateRecordClientService;

    @Pointcut("@annotation(com.holderzone.holder.saas.aggregation.app.anno.RecordOperate)")
    public void memberOperateRecord() {

    }

    /**
     * 会员登录操作记录
     */

    @Around("memberOperateRecord()")
    public Object memberOperateRecordAround(ProceedingJoinPoint point) {

        Object result;
        try {
            result = point.proceed();
            log.info("操作返回 result={}", JacksonUtils.writeValueAsString(result));
        } catch (Throwable throwable) {
            log.error("[会员登录]记录失败");
            throw new BusinessException(throwable.getMessage());
        }
        if (ObjectUtils.isEmpty(result)) {
            log.warn("[会员登录]记录失败：返回值为空");
            return result;
        }

        ResponseMemberAndCardInfoDTO responseMember = (ResponseMemberAndCardInfoDTO) result;
        if (responseMember.getMemberInfoDTO() == null) {
            log.warn("[会员登录]记录失败：会员未登录成功");
            return result;
        }
        RequestQueryStoreAndMemberAndCard parameter = (RequestQueryStoreAndMemberAndCard) point.getArgs()[0];
        log.info("记录终端会员登录方式：{}",JacksonUtils.writeValueAsString(parameter));
        MemberOperateRecordReqDTO reqDTO = new MemberOperateRecordReqDTO();
        String userGuid = UserContextUtils.getUserGuid();
        if (StringUtils.isEmpty(userGuid)) {
            return result;
        }
        reqDTO.setDeviceType(Integer.valueOf(UserContextUtils.getSource()));
        reqDTO.setLoginType(parameter.getLoginType());
        reqDTO.setModuleType(parameter.getModuleType());
        reqDTO.setTradeMode(parameter.getTradeMode());
        reqDTO.setOperatorGuid(UserContextUtils.getUserGuid());
        reqDTO.setPhoneNum(parameter.getPhoneNumOrCardNum());
        reqDTO.setOperatorName(UserContextUtils.getUserName());
        reqDTO.setOrderGuid(parameter.getOrderGuid());
        reqDTO.setStoreGuid(UserContextUtils.getStoreGuid());
        reqDTO.setStoreName(UserContextUtils.getStoreName());
        operateRecordClientService.saveRecord(reqDTO);
        return result;
    }

}