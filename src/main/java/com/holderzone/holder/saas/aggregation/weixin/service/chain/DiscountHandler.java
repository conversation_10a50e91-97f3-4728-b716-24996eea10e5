package com.holderzone.holder.saas.aggregation.weixin.service.chain;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.context.DiscountContext;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.weixin.utils.BigDecimalUtil;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 优惠处理器
 */
@Slf4j
public abstract class DiscountHandler {

    private DiscountHandler nextDiscountHandler;

    void setNextDiscountHandler(DiscountHandler discountHandler) {
        nextDiscountHandler = discountHandler;
    }

    public void doDiscount(DiscountContext context) {
        log.info("[{}]context={}", DiscountTypeEnum.getDesc(type()), JacksonUtils.writeValueAsString(context));
        dealDiscount(context);
        if (nextDiscountHandler != null) {
            nextDiscountHandler.doDiscount(context);
        }
    }

    public BigDecimal dealWithDiscountFee(BigDecimal orderSurplusFee, BigDecimal discountFee) {
        if (discountFee.compareTo(orderSurplusFee) > 0) {
            discountFee = BigDecimalUtil.greaterThanZero(orderSurplusFee) ?
                    orderSurplusFee : BigDecimal.ZERO;
        }
        return discountFee;
    }

    public void singleItemDiscount(Map<String, DineInItemDTO> dineInItemMap, DiscountFeeDetailDTO memberGroupon,
                                   List<RequestDishInfo> dishInfoDTOList) {
        BigDecimal discountFee = BigDecimal.ZERO;
        for (RequestDishInfo dishInfoDTO : dishInfoDTOList) {
            if (Objects.isNull(dishInfoDTO.getDiscountMoney())) {
                continue;
            }
            discountFee = discountFee.add(dishInfoDTO.getDiscountMoney());
            DineInItemDTO dineInItemDTO = dineInItemMap.get(dishInfoDTO.getOrderItemGuid());
            if (dineInItemDTO != null && dishInfoDTO.getDiscountMoney() != null) {
                dineInItemDTO.setTotalDiscountFee(dineInItemDTO.getTotalDiscountFee().add(dishInfoDTO
                        .getDiscountMoney()));
                // 设置活动商品优惠金额
                dineInItemDTO.setDiscountPreferential(dishInfoDTO.getDiscountMoney());
                log.info("优惠金额,{}", dishInfoDTO.getDiscountMoney());
            }
        }
        memberGroupon.setDiscountFee(BigDecimalUtil.setScale2(discountFee));
    }


    /**
     * 优惠金额处理
     * 更新每个商品上的最终优惠价
     */
    public void handleDiscountTotalPrice(DiscountContext context, List<RequestDishInfo> dishInfoList) {
        if (CollectionUtils.isEmpty(dishInfoList)) {
            return;
        }
        Map<String, DineInItemDTO> itemMap = context.getAllItems().stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (v1, v2) -> v1));
        dishInfoList.forEach(dish -> {
            if (dish.getDiscountMoney() != null) {
                DineInItemDTO dineInItemDTO = itemMap.get(dish.getOrderItemGuid());
                if (!ObjectUtils.isEmpty(dineInItemDTO)) {
                    dineInItemDTO.setDiscountTotalPrice(dineInItemDTO.getDiscountTotalPrice().subtract(dish.getDiscountMoney()));
                }
            }
        });
    }

    abstract void dealDiscount(DiscountContext context);

    abstract Integer type();
}
