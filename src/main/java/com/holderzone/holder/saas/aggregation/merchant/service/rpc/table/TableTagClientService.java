package com.holderzone.holder.saas.aggregation.merchant.service.rpc.table;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import com.holderzone.saas.store.dto.table.TableTagDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableTagClientService
 * @date 2019/12/06 9:37
 * @description //TODO
 * @program IdeaProjects
 */
@Component
@FeignClient(value = "holder-saas-store-table", fallbackFactory = TableTagClientService.TableTagServiceClientFallBack.class)
public interface TableTagClientService {

    @PostMapping("/tag/delete")
    Boolean deleteTag(TableTagDTO tableTagDTO);

    @PostMapping("/tag/update")
    Boolean updateTag(TableTagDTO tableTagDTO);

    @PostMapping("/tag/list")
    Page<TableTagDTO> listTag(BasePageDTO basePageDTO);

    @PostMapping("/tag/add")
    Boolean createTag(TableTagDTO tableTagDTO);

    @Slf4j
    @Component
    class TableTagServiceClientFallBack implements FallbackFactory<TableTagClientService> {

        public static final String HYSTRIX_PATTERN = "远程调用出错";

        @Override
        public TableTagClientService create(Throwable cause) {
            return new TableTagClientService() {

                @Override
                public Boolean deleteTag(TableTagDTO tableTagDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "删除标签",
                                JacksonUtils.writeValueAsString(tableTagDTO));
                    }
                    throw new BusinessException("删除标签异常");
                }

                @Override
                public Boolean updateTag(TableTagDTO tableTagDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "更新标签异常",
                                JacksonUtils.writeValueAsString(tableTagDTO));
                    }
                    throw new BusinessException("更新标签异常");
                }

                @Override
                public Page<TableTagDTO> listTag(BasePageDTO basePageDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "查询标签列表异常",
                                JacksonUtils.writeValueAsString(basePageDTO));
                    }
                    throw new BusinessException("查询标签列表异常");
                }

                @Override
                public Boolean createTag(TableTagDTO tableTagDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "创建标签异常",
                                JacksonUtils.writeValueAsString(tableTagDTO));
                    }
                    throw new BusinessException("创建标签异常");
                }
            };
        }
    }
}
