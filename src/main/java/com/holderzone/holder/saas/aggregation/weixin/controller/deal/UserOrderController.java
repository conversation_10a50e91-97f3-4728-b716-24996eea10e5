package com.holderzone.holder.saas.aggregation.weixin.controller.deal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.WxClientService;
import com.holderzone.saas.store.dto.weixin.deal.WxStoreUserOrderItemDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/deal/user_order")
@Slf4j
@Api(description = "用户订单")
public class UserOrderController {

	private final WxClientService wxClientService;

	@Autowired
	public UserOrderController(WxClientService wxClientService) {
		this.wxClientService = wxClientService;
	}

	@ApiOperation("我的订单")
	@GetMapping(value = "/store")
	public Result<List<WxStoreUserOrderItemDTO>> getStoreUserOrderList(){
		return Result.buildSuccessResult(wxClientService.userStoreList());
	}

	@ApiOperation("查询我的或者桌台没有完结的订单")
	@GetMapping(value = "/un_finish_order_list")
	public Result<List<WxStoreUserOrderItemDTO>> getUnFinishStoreUserOrderList() {
		return Result.buildSuccessResult(wxClientService.getUnFinishStoreUserOrderList());
	}

	@ApiOperation("门店用户订单")
	@PostMapping(value = "/store_page")
	public Result<IPage<WxStoreUserOrderItemDTO>> userStorePage(Integer pageNo, Integer pageSize){
		return Result.buildSuccessResult(wxClientService.userStorePage(pageNo, pageSize));
	}

}
