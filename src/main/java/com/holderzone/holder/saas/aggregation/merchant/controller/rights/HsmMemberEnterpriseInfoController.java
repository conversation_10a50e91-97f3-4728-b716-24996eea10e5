package com.holderzone.holder.saas.aggregation.merchant.controller.rights;


import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.baseresource.HsmEnterpriseInfoService;
import com.holderzone.holder.saas.member.dto.baseresource.request.HsmEnterpriseReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.response.HsmEnterpriseRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 商家(企业)信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-27
 */
@RestController
@RequestMapping("/hsm/member/enterprise")
@Api(description = "企业")
public class HsmMemberEnterpriseInfoController {

    @Resource
    private HsmEnterpriseInfoService iHsmEnterpriseInfoService;

    /**
     * 同步企业
     *
     * @param hsmEnterpriseReqDTO 列表
     */
    @ApiOperation(value = "同步企业", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, response = HsmEnterpriseRespDTO.class)
    @PostMapping("/syc")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "同步企业")
    public Result syc(@Validated
    @ApiParam(value = "企业对象", required = true) @RequestBody HsmEnterpriseReqDTO hsmEnterpriseReqDTO) {
        return Result.buildSuccessResult(iHsmEnterpriseInfoService.syc(hsmEnterpriseReqDTO));
    }

    /**
     * 同步企业
     *
     * @param hsmEnterpriseReqDTOs 列表
     */
    @ApiOperation("同步企业")
    @PostMapping("/syc/list")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "同步企业")
    public Result sycList(
            @Validated @RequestBody List<HsmEnterpriseReqDTO> hsmEnterpriseReqDTOs) {
       return Result.buildSuccessResult(iHsmEnterpriseInfoService.sycList(hsmEnterpriseReqDTOs));
    }

    /**
     * 保存企业
     *
     * @param hsmEnterpriseReqDTO
     * @return
     */
    @ApiOperation("保存企业")
    @PostMapping("/save")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "保存企业")
    public Result save(
            @Validated @RequestBody HsmEnterpriseReqDTO hsmEnterpriseReqDTO) {
        return Result.buildSuccessResult(iHsmEnterpriseInfoService.save(hsmEnterpriseReqDTO));
    }

    /**
     * 修改企业
     *
     * @param hsmEnterpriseReqDTO
     * @return
     */
    @ApiOperation("修改企业")
    @PostMapping("/modify")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "修改企业")
    public Result modify(
            @Validated @RequestBody HsmEnterpriseReqDTO hsmEnterpriseReqDTO) {
        return Result.buildSuccessResult(iHsmEnterpriseInfoService.modify(hsmEnterpriseReqDTO));
    }


    /**
     * @param enterpriseKey 外部商家主键
     * @param allianceid    联盟ID
     * @return
     * <AUTHOR>
     * 根据Guid删除商家(企业)信息
     */
    @ApiOperation(
            value = "根据Guid删除商家(企业)信息", notes = "根据Guid删除商家(企业)信息"
    )
    @DeleteMapping("/{enterpriseKey}/{allianceid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "根据Guid删除商家(企业)信息")
    public Result removeHsmEnterprise(@ApiParam("enterpriseKey") @PathVariable("enterpriseKey") String enterpriseKey, @ApiParam("allianceid") @PathVariable("allianceid") String allianceid) {
        return Result.buildSuccessResult(iHsmEnterpriseInfoService.removeHsmEnterprise(enterpriseKey, allianceid));
    }

    /**
     * 根据登录用户的企业Guid查询是否关联体系
     * @return
     */
    @GetMapping("/have/system")
    @ApiOperation(value = "根据登录用户的企业Guid查询是否关联体系")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "根据登录用户的企业Guid查询是否关联体系")
    public Result<Boolean> isHaveSystem() {
        return Result.buildSuccessResult(iHsmEnterpriseInfoService.isHaveSystem());
    }

}
