package com.holderzone.holder.saas.aggregation.merchant.controller.user;

import com.google.common.collect.Lists;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.entity.dto.OrganizationNewDTO;
import com.holderzone.holder.saas.aggregation.merchant.entity.vo.EnterpriseDTO;
import com.holderzone.holder.saas.aggregation.merchant.entity.vo.OrganizationMultiVO;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp.EnterpriseRpcService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.user.UserFeignService;
import com.holderzone.resource.common.dto.enterprise.EnterpriseQueryDTO;
import com.holderzone.resource.common.dto.enterprise.OrganizationQueryDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.user.UserDTO;
import com.holderzone.saas.store.dto.user.UserQueryDTO;
import com.holderzone.saas.store.dto.user.UserSpinnerDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

@Slf4j
@RestController
@RequestMapping("/user_data")
@Api(description = "员工数据权限管理接口")
public class UserDataController {

    private final UserFeignService userFeignService;

    private final EnterpriseRpcService enterpriseRpcService;

    @Autowired
    public UserDataController(UserFeignService userFeignService, EnterpriseRpcService enterpriseRpcService) {
        this.userFeignService = userFeignService;
        this.enterpriseRpcService = enterpriseRpcService;
    }

    @ApiOperation(value = "保存用户数据权限", notes = "保存用户数据权限")
    @PostMapping(value = "/save")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "保存用户数据权限")
    public Result save(@RequestBody UserDTO userDTO) {
        userFeignService.saveUserData(userDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "查询用户数据权限", notes = "查询用户数据权限")
    @PostMapping(value = "/query")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "查询用户数据权限")
    public Result<UserDTO> query(@RequestBody UserDTO userDTO) {
        return Result.buildSuccessResult(userFeignService.queryUserData(userDTO));
    }

    @ApiOperation(value = "更新用户数据权限", notes = "更新用户数据权限")
    @PostMapping(value = "/update")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "更新用户数据权限")
    public Result update(@RequestBody UserDTO userDTO) {
        userFeignService.updateUserData(userDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "查询用户可分配角色", notes = "查询用户可分配角色")
    @PostMapping(value = "/query_roles_distributable")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "查询用户可分配角色")
    public Result<UserDTO> queryUserRolesDistributable() {
        return Result.buildSuccessResult(userFeignService.queryUserRolesDistributable());
    }

    @ApiOperation(value = "查询门店Spinner")
    @PostMapping(value = "/query_store_spinner")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "查询门店Spinner")
    public Result<UserSpinnerDTO> queryStoreSpinner() {
        return Result.buildSuccessResult(userFeignService.queryStoreSpinner());
    }

    @ApiOperation(value = "通过品牌guid查询门店Spinner")
    @PostMapping(value = "/query_store_spinner_by_brand_guid")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "通过品牌guid查询门店Spinner")
    public Result<UserSpinnerDTO> queryStoreSpinnerByBrandGuid(@RequestBody SingleDataDTO singleDataDTO) {
        return Result.buildSuccessResult(userFeignService.queryStoreSpinnerByBrandGuid(singleDataDTO));
    }

    @ApiOperation(value = "查询组织Spinner")
    @PostMapping(value = "/query_org_spinner")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "查询组织Spinner")
    public Result<UserSpinnerDTO> queryOrgSpinner() {
        return Result.buildSuccessResult(userFeignService.queryOrgSpinner());
    }

    @ApiOperation(value = "查询品牌Spinner")
    @PostMapping(value = "/query_brand_spinner")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "查询品牌Spinner")
    public Result<UserSpinnerDTO> queryBrandSpinner() {
        return Result.buildSuccessResult(userFeignService.queryBrandSpinner());
    }

    @ApiOperation(value = "查询条件匹配Spinner")
    @PostMapping(value = "/query_condition_spinner")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "查询条件匹配Spinner")
    public Result<UserSpinnerDTO> queryConditionSpinner() {
        return Result.buildSuccessResult(userFeignService.queryConditionSpinner());
    }

    @ApiOperation(value = "查询门店和条件匹配Spinner")
    @PostMapping(value = "/query_store_and_cond_spinner")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "查询门店和条件匹配Spinner")
    public Result<UserSpinnerDTO> queryStoreAndCondSpinner() {
        return Result.buildSuccessResult(userFeignService.queryStoreAndCondSpinner());
    }

    @ApiOperation(value = "查询广义组织Spinner")
    @PostMapping(value = "/query_generic_org_spinner")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "查询广义组织Spinner")
    public Result<UserSpinnerDTO> queryGenericOrgSpinner() {
        return Result.buildSuccessResult(userFeignService.queryGenericOrgSpinner());
    }


    @ApiOperation(value = "操作人查询运营主体")
    @PostMapping(value = "/query_organization_multi")
    public Result<List<OrganizationMultiVO>> queryOrganizationMulti() {
        List<OrganizationMultiVO> multiVOS = Lists.newArrayList();
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        //根据操作人查询对应所拥有的门店
        SingleDataDTO singleDataDTO = new SingleDataDTO();
        singleDataDTO.setData("");
        UserSpinnerDTO userSpinnerDTO = userFeignService.queryStoreSpinnerByBrandGuid(singleDataDTO);
        if (CollectionUtils.isEmpty(userSpinnerDTO.getArrayOfStoreDTO())){
            log.info("无门店数据！");
            return Result.buildSuccessResult(multiVOS);
        }

        //
        EnterpriseQueryDTO enterpriseQueryDTO = new EnterpriseQueryDTO();
        enterpriseQueryDTO.setEnterpriseGuid(enterpriseGuid);
        EnterpriseDTO enterprise = enterpriseRpcService.findEnterprise(enterpriseQueryDTO);
        log.info("queryStoreSpinnerByBrandGuid 企业基本信息数据{}", JacksonUtils.writeValueAsString(enterprise));
        log.info("queryStoreSpinnerByBrandGuid 查询出的门店数据{}", JacksonUtils.writeValueAsString(userSpinnerDTO));
        List<StoreDTO> storeDTOList = userSpinnerDTO.getArrayOfStoreDTO();
        //查询该企业下所有组织及运营主体（多会员体系）
        OrganizationQueryDTO queryDTO = new OrganizationQueryDTO();
        queryDTO.setEnterpriseGuid(enterpriseGuid);
        Page<OrganizationNewDTO> platformDTOPage = enterpriseRpcService.pageQueryAllPlatformStore(queryDTO);
        if (CollectionUtils.isEmpty(platformDTOPage.getData())){
            log.info("无组织运营主体数据！");
            return Result.buildSuccessResult(multiVOS);
        }
        log.info("queryStoreSpinnerByBrandGuid 查出的组织运营主体数据{}", JacksonUtils.writeValueAsString(platformDTOPage));
        try {
            List<OrganizationMultiVO> multiVOList = Lists.newArrayList();
            List<OrganizationNewDTO> platformDTOList = platformDTOPage.getData();
            Map<String, List<OrganizationNewDTO>> platformDTOMap = platformDTOList.stream().collect(Collectors.groupingBy(OrganizationNewDTO::getOrganizationGuid));
            for (StoreDTO storeDTO : storeDTOList) {
                List<OrganizationNewDTO> organizationNewDTOS = platformDTOMap.get(storeDTO.getGuid());
                if (CollectionUtils.isNotEmpty(organizationNewDTOS)){
                    organizationNewDTOS.stream().forEach(e->{
                        //运营主体被禁用过滤掉
                        if (e.getEnabled()){
                            OrganizationMultiVO organizationMultiVO = new OrganizationMultiVO();
                            organizationMultiVO.setMultiMemberGuid(e.getMultiMemberGuid());
                            organizationMultiVO.setMultiMemberName(e.getMultiMemberName());
                            //平台类型的企业 需特殊处理
                            Boolean isManagementModel = false;
                            if (Objects.nonNull(enterprise)){
                                EnterpriseDTO.ManagementModel managementModel = enterprise.getManagementModel();
                                if (managementModel.equals("PLATFORM")){
                                    isManagementModel = true;
                                }
                            }

                            organizationMultiVO.setIsManagementModel(isManagementModel);
                            multiVOList.add(organizationMultiVO);
                        }
                    });
                }
            }
            //过滤到重复的运营主体
            multiVOS = multiVOList.stream().collect(
                    collectingAndThen(
                            toCollection(() -> new TreeSet<>(Comparator.comparing(OrganizationMultiVO::getMultiMemberGuid))), ArrayList::new)
            );
        }catch (Exception e){
            log.error("查询运营主体异常",e);
        }
        log.info("queryStoreSpinnerByBrandGuid 最终返回出的数据{}",JacksonUtils.writeValueAsString(multiVOS));
        return Result.buildSuccessResult(multiVOS);
    }

    @ApiOperation(value = "门店查询员工信息")
    @PostMapping(value = "/query_user_by_store")
    public Result<List<UserDTO>> pageQuery(@Validated @RequestBody UserQueryDTO userQueryDTO) {
        if (log.isInfoEnabled()) {
            log.info("门店查询员工信息入参：{}", JacksonUtils.writeValueAsString(userQueryDTO));
        }
        return Result.buildSuccessResult(userFeignService.findByStoreGuid(userQueryDTO));
    }
}
