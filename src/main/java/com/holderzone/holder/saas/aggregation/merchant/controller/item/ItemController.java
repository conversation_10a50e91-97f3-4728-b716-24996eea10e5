package com.holderzone.holder.saas.aggregation.merchant.controller.item;

import cn.hutool.core.collection.CollectionUtil;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Chinese2PinyinUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.entity.enums.DisplayRuleTypeEnum;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.item.ItemClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.kds.KdsClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.organization.OrganizationService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.takeout.TakeoutConsumerService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemDoubleParamDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.common.PrintItemTypeDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleQueryDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayItemRespDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.takeaway.ErpMappingType;
import com.holderzone.saas.store.enums.item.ModuleEntranceEnum;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.holderzone.holder.saas.aggregation.merchant.constant.Constants.DISTRIBUTE_FAIL;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemController
 * @date 2018/09/12 上午9:53
 * @description //TODO
 * @program holder-saas-store-item
 */
@RestController
@AllArgsConstructor
@RequestMapping("/item")
@Api(tags = "商品接口")
@Slf4j
public class ItemController {

    private final ItemClientService itemClientService;

    private final ItemHelper itemHelper;

    private final KdsClientService kdsClientService;

    private final OrganizationService organizationService;

    private final TakeoutConsumerService takeoutConsumerService;

    @ApiOperation(value = "门店入口保存商品接口")
    @PostMapping("/store/save")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "门店入口保存商品接口")
    public Result saveItem(@RequestBody ItemReqDTO itemSaveReqDTO) {
        log.info("门店商品保存接口查询入参,itemSaveReqDTO={}", itemSaveReqDTO);
        // 21年3月17需求变更：门店可以自建了
        itemSaveReqDTO.setFrom(0);
        itemClientService.saveItem(itemSaveReqDTO);
        return Result.buildEmptySuccess();
    }

    /**
     * 带推送功能的返回
     *
     * @param num
     * @return
     */
    private Result dishtributeReturn(Integer num) {
        if (Integer.valueOf(3).equals(num)) {
            return Result.buildSuccessMsg(DISTRIBUTE_FAIL);
        }
        if (Integer.valueOf(1).equals(num)) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVE_FAILED));
        }
    }

    @ApiOperation(value = "品牌入口保存商品接口")
    @PostMapping("/brand/save")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "品牌入口保存商品接口")
    public Result saveItemFromBrand(@RequestBody ItemReqDTO itemSaveReqDTO) {
        log.info("品牌商品保存接口查询入参,itemSaveReqDTO={}", itemSaveReqDTO);
        itemSaveReqDTO.setFrom(1);
        itemClientService.saveItem(itemSaveReqDTO);
        return Result.buildEmptySuccess();
    }


    @ApiOperation(value = "门店更新商品接口")
    @PostMapping("/store/update_item")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "门店更新商品接口")
    public Result updateItem(@RequestBody ItemReqDTO itemUpdateReqDTO) {
        log.info("门店更新商品接口查询入参,itemUpdateReqDTO={}", itemUpdateReqDTO);
        itemUpdateReqDTO.setFrom(0);
        Integer num = itemClientService.updateItem(itemUpdateReqDTO);
        if (num == 1) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.UPDATE_FAILED));
        }
    }

    @ApiOperation(value = "更新商品pad图片")
    @PostMapping("/editPadPicture")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "门店更新商品接口")
    public Result updateItem(@RequestBody @Valid PadPictureDTO padPictureDTO) {
        log.info("更新商品pad图片参数入口,padPictureDTO={}", padPictureDTO);
        Integer num = itemClientService.editPadPicture(padPictureDTO);
        if (num == 1) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.UPDATE_FAILED));
        }
    }

    @ApiOperation(value = "品牌更新商品接口")
    @PostMapping("/brand/update_item")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "品牌更新商品接口")
    public Result updateItemFromBrand(@RequestBody ItemReqDTO itemUpdateReqDTO) {
        log.info("品牌更新商品接口查询入参,itemUpdateReqDTO={}", itemUpdateReqDTO);
        // todo 测试含属性的商品的修改
        itemUpdateReqDTO.setFrom(1);
        Integer num = itemClientService.updateItem(itemUpdateReqDTO);
        return dishtributeReturn(num);
    }

    @ApiOperation(value = "门店删除商品接口")
    @PostMapping("/store/delete_item")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "门店删除商品接口")
    public Result deleteItem(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("门店删除商品接口查询入参,itemSingleDTO={}", itemSingleDTO);
        itemSingleDTO.setFrom(0);
        return Result.buildSuccessResult(itemClientService.deleteItem(itemSingleDTO));
    }

    @ApiOperation(value = "品牌删除商品接口")
    @PostMapping("/brand/delete_item")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "品牌删除商品接口")
    public Result deleteItemFromBrand(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("门店删除商品接口查询入参,itemSingleDTO={}", itemSingleDTO);
        itemSingleDTO.setFrom(1);
        return Result.buildSuccessResult(itemClientService.deleteItem(itemSingleDTO));
    }

    @ApiOperation(value = "门店入口商品列表接口")
    @PostMapping("/store/select_item_for_web")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "门店入口商品列表接口")
    public Result<Page<ItemWebRespDTO>> selectItemForWeb(@RequestBody ItemQueryReqDTO itemQueryReqDTO) {
        log.info("商品列表接口查询入参,itemQueryReqDTO={}", itemQueryReqDTO);
        Page<ItemWebRespDTO> itemWebRespDTOS = itemClientService.selectItemForWeb(itemQueryReqDTO);
        return Result.buildSuccessResult(itemWebRespDTOS);
    }

    @ApiOperation(value = "门店入口商品列表接口")
    @PostMapping("/store/queryPadPicture")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "门店入口商品列表接口")
    public Result<Page<PadPictureRespDTO>> queryPadPicture(@RequestBody PadPictureDTO itemQueryReqDTO) {
        log.info("商品列表接口查询入参,itemQueryReqDTO={}", itemQueryReqDTO);
        Page<PadPictureRespDTO> padPictureRespDTOs = itemClientService.queryPadPicture(itemQueryReqDTO);
        return Result.buildSuccessResult(padPictureRespDTOs);
    }


    @ApiOperation(value = "品牌入口商品列表接口")
    @PostMapping("/brand/select_item_for_web")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "品牌入口商品列表接口")
    public Result<Page<ItemWebRespDTO>> selectItemForWebFromBrand(@RequestBody ItemQueryReqDTO itemQueryReqDTO) {
        log.info("商品列表接口查询入参,itemQueryReqDTO={}", itemQueryReqDTO);
        Page<ItemWebRespDTO> itemWebRespDTOS = itemClientService.selectItemForWeb(itemQueryReqDTO);
        return Result.buildSuccessResult(itemWebRespDTOS);
    }

    @ApiOperation(value = "根据汉字转拼音首字母", notes = "必填参数：data")
    @PostMapping("/getFirstChar")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "根据汉字转拼音首字母")
    public Result<String> obtainFirstChar(@RequestBody ItemSingleDTO chinese) {
        log.info("根据汉字转拼音首字母接口入参,ItemSingleDTO={}", chinese.getData());
        String initials = Chinese2PinyinUtils.initials(chinese.getData());
        return Result.buildSuccessResult(initials);
    }

    @ApiOperation(value = "获取一个商品详情", notes = "必填参数：data，商品GUID")
    @PostMapping("/get_item_info")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "获取一个商品详情")
    public Result<ItemInfoRespDTO> getItemInfo(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("获取一个商品详情接口入参,ItemSingleDTO={}", itemSingleDTO.getData());
        ItemInfoRespDTO itemInfo = itemClientService.getItemInfo(itemSingleDTO);
        return Result.buildSuccessResult(itemInfo);
    }

    @ApiOperation("更新门店菜品排序")
    @PostMapping("/store/update_item_sort")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "更新门店菜品排序")
    public Result<Boolean> updateItemSort(@RequestBody ItemSortUpdateReqDTO itemSortUpdateReqDTO) {
        log.info("更新门店菜品排序入参:{}", JacksonUtils.writeValueAsString(itemSortUpdateReqDTO));
        itemSortUpdateReqDTO.setFrom(ModuleEntranceEnum.STORE.code());
        boolean flag = itemClientService.updateItemSort(itemSortUpdateReqDTO);
        return flag ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVE_FAILED));
    }

    @ApiOperation("更新品牌菜品排序")
    @PostMapping("/brand/update_item_sort")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "更新品牌菜品排序")
    public Result<Boolean> updateBrandItemSort(@RequestBody ItemSortUpdateReqDTO itemSortUpdateReqDTO) {
        log.info("更新品牌菜品排序入参:{}", JacksonUtils.writeValueAsString(itemSortUpdateReqDTO));
        itemSortUpdateReqDTO.setFrom(ModuleEntranceEnum.BRAND.code());
        boolean flag = itemClientService.updateItemSort(itemSortUpdateReqDTO);
        return flag ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVE_FAILED));
    }

    @ApiOperation("分配售卖门店")
    @PostMapping("/brand/push_item")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "分配售卖门店")
    public Result<Boolean> pushItem(@RequestBody PushItemReqDTO pushItemReqDTO) {
        log.info("推送商品入参:{}", JacksonUtils.writeValueAsString(pushItemReqDTO));
        Integer flag = itemClientService.pushItem(pushItemReqDTO);
        return flag == 1 ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.FAILED_TO_ASSIGN_SELLING_STORES));
    }

    @ApiOperation(value = "分配售卖门店获取商品列表接口", notes = "必填参数：data:当前新增套餐所属门店或品牌GUID")
    @PostMapping("/select_sku_list_from_push")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "分配售卖门店获取商品列表接口")
    public Result<List<TypeSkuRespDTO>> selectSkuListForPkgFromPush(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("分配售卖门店获取商品列表接口查询入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(ModuleEntranceEnum.PUSH.code());
        List<TypeSkuRespDTO> typeSkuRespDTOList = itemClientService.selectSkuListForPkg(itemSingleDTO);
        return Result.buildSuccessResult(typeSkuRespDTOList);
    }

    @ApiOperation(value = "门店导入品牌库获取商品列表接口", notes = "必填参数：data:门店guid")
    @PostMapping("/select_list_from_push_by_store_guid")
    public Result<List<TypeSkuRespDTO>> selectListForPkgFromPushByStoreGuid(@RequestBody ItemSingleDTO itemSingleDTO) {
        String storeGuid = itemSingleDTO.getData();
        log.info("门店导入品牌库获取商品列表接口查询入参,门店guid={}", storeGuid);
        // 通过门店guid查询品牌guid
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeGuid);
        if (ObjectUtils.isEmpty(brandDTO)) {
            throw new BusinessException("未查询到品牌！");
        }
        itemSingleDTO.setData(brandDTO.getGuid());
        itemSingleDTO.setFrom(ModuleEntranceEnum.PUSH.code());
        // 品牌下所有的商品
        List<TypeSkuRespDTO> typeSkuRespDTOList = itemClientService.selectSkuListForPkg(itemSingleDTO);
        // 过滤已上架和已推送的商品
        itemSingleDTO.setData(storeGuid);
        List<String> parentSkuList = itemClientService.queryFilterItem(itemSingleDTO);
        if (!CollectionUtil.isEmpty(parentSkuList) && !CollectionUtil.isEmpty(typeSkuRespDTOList)) {
            typeSkuRespDTOList.forEach(
                    typeSku -> typeSku.getSkuNameRespDTOList().removeIf(s -> parentSkuList.contains(s.getSkuGuid()))
            );
            typeSkuRespDTOList.removeIf(t -> CollectionUtils.isEmpty(t.getSkuNameRespDTOList()));
        }
        return Result.buildSuccessResult(typeSkuRespDTOList);
    }

    @ApiOperation(value = "品牌库入口的上架接口")
    @PostMapping("/brand/rack")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "品牌库入口的上架接口")
    public Result brandRackItem(@RequestBody ItemRackDTO itemRackDTO) {
        log.info("上下架商品入参,itemRackDTO={}", JacksonUtils.writeValueAsString(itemRackDTO));
        itemRackDTO.setFrom(1);
        itemRackDTO.setRackState(1);
        Integer rackItem = itemClientService.rackItem(itemRackDTO);
        return Integer.valueOf(1).equals(rackItem) ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.PRODUCT_LISTING_FAILED));
    }

    @ApiOperation(value = "品牌库入口的下架接口")
    @PostMapping("/brand/un_rack")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "品牌库入口的下架接口")
    public Result brandUnRackItem(@RequestBody ItemRackDTO itemRackDTO) {
        log.info("上下架商品入参,itemRackDTO={}", JacksonUtils.writeValueAsString(itemRackDTO));
        itemRackDTO.setFrom(1);
        itemRackDTO.setRackState(0);
        Integer rackItem = itemClientService.rackItem(itemRackDTO);
        return Integer.valueOf(1).equals(rackItem) ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.PRODUCT_DELISTING_FAILED));
    }

    @ApiOperation(value = "门店库入口的上架接口")
    @PostMapping("/store/rack")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "门店库入口的上架接口")
    public Result storeRackItem(@RequestBody ItemRackDTO itemRackDTO) {
        log.info("上下架商品入参,itemRackDTO={}", JacksonUtils.writeValueAsString(itemRackDTO));
        itemRackDTO.setFrom(0);
        itemRackDTO.setRackState(1);
        Integer rackItem = itemClientService.rackItem(itemRackDTO);
        return Integer.valueOf(1).equals(rackItem) ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.PRODUCT_LISTING_FAILED));
    }

    @ApiOperation(value = "门店库入口的下架接口")
    @PostMapping("/store/un_rack")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "门店库入口的下架接口")
    public Result rackItem(@RequestBody ItemRackDTO itemRackDTO) {
        log.info("上下架商品入参,itemRackDTO={}", JacksonUtils.writeValueAsString(itemRackDTO));
        itemRackDTO.setFrom(0);
        itemRackDTO.setRackState(0);
        Integer rackItem = itemClientService.rackItem(itemRackDTO);
        return Integer.valueOf(1).equals(rackItem) ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.PRODUCT_DELISTING_FAILED));
    }

    @ApiOperation(value = "品牌库入口的批量上架接口")
    @PostMapping("/brand/batch_rack")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "品牌库入口的批量上架接口")
    public Result brandBatchRackItem(@RequestBody ItemRackDTO itemRackDTO) {
        log.info("批量上下架商品入参,itemRackDTO={}", JacksonUtils.writeValueAsString(itemRackDTO));
        itemRackDTO.setRackState(1);
        itemRackDTO.setFrom(1);
        Integer rackItem = itemClientService.rackItem(itemRackDTO);
        return Integer.valueOf(1).equals(rackItem) ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.BATCH_PRODUCT_LISTING_FAILED));
    }

    @ApiOperation(value = "品牌库入口的批量下架接口")
    @PostMapping("/brand/batch_un_rack")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "品牌库入口的批量下架接口")
    public Result brandBatchUnRackItem(@RequestBody ItemRackDTO itemRackDTO) {
        log.info("批量上下架商品入参,itemRackDTO={}", JacksonUtils.writeValueAsString(itemRackDTO));
        itemRackDTO.setRackState(0);
        itemRackDTO.setFrom(1);
        Integer rackItem = itemClientService.rackItem(itemRackDTO);
        return Integer.valueOf(1).equals(rackItem) ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.BATCH_PRODUCT_DELISTING_FAILED));
    }

    @ApiOperation(value = "门店库入口的批量上架接口")
    @PostMapping("/store/batch_rack")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "门店库入口的批量上架接口")
    public Result storeBatchRackItem(@RequestBody ItemRackDTO itemRackDTO) {
        log.info("批量上下架商品入参,itemRackDTO={}", JacksonUtils.writeValueAsString(itemRackDTO));
        itemRackDTO.setRackState(1);
        itemRackDTO.setFrom(0);
        Integer rackItem = itemClientService.rackItem(itemRackDTO);
        return Integer.valueOf(1).equals(rackItem) ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.BATCH_PRODUCT_LISTING_FAILED));
    }

    @ApiOperation(value = "门店库入口的批量下架接口")
    @PostMapping("/store/batch_un_rack")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "门店库入口的批量下架接口")
    public Result storeBatchUnRackItem(@RequestBody ItemRackDTO itemRackDTO) {
        log.info("批量上下架商品入参,itemRackDTO={}", JacksonUtils.writeValueAsString(itemRackDTO));
        itemRackDTO.setRackState(0);
        itemRackDTO.setFrom(0);
        Integer rackItem = itemClientService.rackItem(itemRackDTO);
        return Integer.valueOf(1).equals(rackItem) ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.BATCH_PRODUCT_DELISTING_FAILED));
    }

    @ApiOperation(value = "门店批量删除商品接口")
    @PostMapping("/store/batch_delete")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "门店批量删除商品接口")
    public Result storeBatchDeleteItem(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("门店批量删除商品接口查询入参,itemStringListDTO={}", itemStringListDTO);
        itemStringListDTO.setFrom(0);
        Integer num = itemClientService.batchDelete(itemStringListDTO);
        if (num == 1) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.DELETION_FAILED));
        }
    }

    @ApiOperation(value = "品牌批量删除商品接口")
    @PostMapping("/brand/batch_delete")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "品牌批量删除商品接口")
    public Result brandBatchDeleteItem(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("品牌批量删除商品接口查询入参,itemStringListDTO={}", itemStringListDTO);
        itemStringListDTO.setFrom(1);
        Integer num = itemClientService.batchDelete(itemStringListDTO);
        if (num == 1) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.DELETION_FAILED));
        }
    }

    @PostMapping("/mapping")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM)
    public Result<List<MappingRespDTO>> mapping(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("美团获取映射商品接口查询入参,itemSingleDTO={}", itemSingleDTO);
        List<MappingRespDTO> mappingRespDTOS = itemClientService.mapping(itemSingleDTO);
        return Result.buildSuccessResult(mappingRespDTOS);
    }


    @ApiOperation(value = "按分类、名称获取sku商品列表")
    @PostMapping("/select_sku_item_list")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "按分类、名称获取sku商品列表")
    public Result<Page<ItemTemplateSubItemRespDTO>> selectSkuItemList(@RequestBody ItemTemplateMenuAllSubItemReqDTO request) {
        log.info("按分类、名称获取sku商品列表查询入参,itemStringListDTO={}", request);
        Page<ItemTemplateSubItemRespDTO> skuItems = itemClientService.selectSkuItemList(request);
        if (skuItems != null && CollectionUtil.isNotEmpty(skuItems.getData())) {
            skuItems.getData().forEach(s -> {
                if (s.getTypeName().equals(Constants.BANQUET_PACKAGES)) {
                    s.setTypeName(LocaleUtil.getMessage("BANQUET_PACKAGES"));
                }
                if (s.getTypeName().equals(Constants.DEFAULT_CATEGORY)) {
                    s.setTypeName(LocaleUtil.getMessage("DEFAULT_CATEGORY"));
                }
            });
        }
        return Result.buildSuccessResult(skuItems);
    }


    @ApiOperation(value = "Q3阶段：流程优化—>批量导入门店商品 p2:列表验证保存")
    @PostMapping("/store/batch_import_item")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "Q3阶段：流程优化—>批量导入门店商品 p2:列表验证保存")
    public Result<BatchImportItemRespDTO> storeBatchImportItem(@RequestBody ItemBatchImportReqDTO itemBatchImportReqDTO) {
        log.info("Q3阶段：流程优化—>批量导入门店商品 p2:列表验证保存入参,request={}", JacksonUtils.writeValueAsString(itemBatchImportReqDTO));
        return batchItemHelper(itemBatchImportReqDTO, 0, 0);

    }


    @ApiOperation(value = "Q3阶段：流程优化—>批量导入品牌商品 p2:列表验证保存")
    @PostMapping("/brand/batch_import_item")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "Q3阶段：流程优化—>批量导入品牌商品 p2:列表验证保存")
    public Result<BatchImportItemRespDTO> brandBatchImportItem(@RequestBody ItemBatchImportReqDTO itemBatchImportReqDTO) {
        log.info("Q3阶段：流程优化—>批量导入品牌商品 p2:列表验证保存入参,request={}", JacksonUtils.writeValueAsString(itemBatchImportReqDTO));
        return batchItemHelper(itemBatchImportReqDTO, 1, 0);

    }


    @ApiOperation(value = "零售版：—>批量导入门店商品 p2:列表验证保存")
    @PostMapping("/retail/store/batch_import_item")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "零售版：—>批量导入门店商品 p2:列表验证保存")
    public Result<BatchImportItemRespDTO> retailStoreBatchImportItem(@RequestBody ItemBatchImportReqDTO itemBatchImportReqDTO) {
        log.info("零售版：—>批量导入门店商品 p2:列表验证保存入参,request={}", JacksonUtils.writeValueAsString(itemBatchImportReqDTO));
        return batchItemHelper(itemBatchImportReqDTO, 0, 1);
    }


    /**
     * 获取团餐详情
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "获取团餐详情 data:团餐商品guid")
    @PostMapping("/select_group_meal_detail")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "获取团餐详情")
    public Result<GroupMealItemDetailRespDTO> selectGroupMealDetail(@RequestBody @Valid SingleDataDTO request) {
        log.info("获取团餐详情 入参， request = {}", JacksonUtils.writeValueAsString(request));
        GroupMealItemDetailRespDTO groupMealItemDetailRespDTO = itemClientService.selectGroupMealDetail(request);
        return Result.buildSuccessResult(groupMealItemDetailRespDTO);
    }


    @ApiOperation(value = "品牌库入口的批量参与整单折扣接口")
    @PostMapping("/brand/batch_whole_discount")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "品牌库入口的批量参与整单折扣接口")
    public Result brandBatchWholeDiscountItem(@RequestBody ItemWholeDiscountDTO itemWholeDiscountDTO) {
        log.info("品牌库入口的批量参与整单折扣接口入参 ,itemWholeDiscountDTO={}", JacksonUtils.writeValueAsString(itemWholeDiscountDTO));
        itemWholeDiscountDTO.setWholeDiscountState(1);
        itemWholeDiscountDTO.setFrom(1);
        Integer wholeDiscountItem = itemClientService.wholeDiscountItem(itemWholeDiscountDTO);
        return Integer.valueOf(1).equals(wholeDiscountItem) ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.BATCH_PARTICIPATION_IN_OVERALL_ORDER_DISCOUNT_FAILED));
    }

    @ApiOperation(value = "品牌库入口的批量不参与整单折扣接口")
    @PostMapping("/brand/batch_un_whole_discount")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "品牌库入口的批量不参与整单折扣接口")
    public Result brandBatchUnWholeDiscounItem(@RequestBody ItemWholeDiscountDTO itemWholeDiscountDTO) {
        log.info("品牌库入口的批量不参与整单折扣接口入参,itemWholeDiscountDTO={}", JacksonUtils.writeValueAsString(itemWholeDiscountDTO));
        itemWholeDiscountDTO.setWholeDiscountState(0);
        itemWholeDiscountDTO.setFrom(1);
        Integer wholeDiscountItem = itemClientService.wholeDiscountItem(itemWholeDiscountDTO);
        return Integer.valueOf(1).equals(wholeDiscountItem) ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.BATCH_EXCLUSION_FROM_OVERALL_ORDER_DISCOUNT_FAILED));
    }

    @ApiOperation(value = "门店库入口的批量参与整单折扣接口")
    @PostMapping("/store/batch_whole_discount")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "门店库入口的批量参与整单折扣接口")
    public Result storeBatchWholeDiscounItem(@RequestBody ItemWholeDiscountDTO itemWholeDiscountDTO) {
        log.info("门店库入口的批量参与整单折扣接口入参,itemWholeDiscountDTO={}", JacksonUtils.writeValueAsString(itemWholeDiscountDTO));
        itemWholeDiscountDTO.setWholeDiscountState(1);
        itemWholeDiscountDTO.setFrom(0);
        Integer wholeDiscountItem = itemClientService.wholeDiscountItem(itemWholeDiscountDTO);
        return Integer.valueOf(1).equals(wholeDiscountItem) ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.BATCH_PARTICIPATION_IN_OVERALL_ORDER_DISCOUNT_FAILED));
    }

    @ApiOperation(value = "门店库入口的批量不参与整单折扣接口")
    @PostMapping("/store/batch_un_whole_discount")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "门店库入口的批量不参与整单折扣接口")
    public Result storeBatchUnWholeDiscounItem(@RequestBody ItemWholeDiscountDTO itemWholeDiscountDTO) {
        log.info("门店库入口的批量参与整单折扣接口入参,itemWholeDiscountDTO={}", JacksonUtils.writeValueAsString(itemWholeDiscountDTO));
        itemWholeDiscountDTO.setWholeDiscountState(0);
        itemWholeDiscountDTO.setFrom(0);
        Integer wholeDiscountItem = itemClientService.wholeDiscountItem(itemWholeDiscountDTO);
        return Integer.valueOf(1).equals(wholeDiscountItem) ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.BATCH_EXCLUSION_FROM_OVERALL_ORDER_DISCOUNT_FAILED));
    }


    /**
     * @param itemBatchImportReqDTO
     * @param flag                  0:门店  1：品牌
     * @param type                  0:餐饮版  1：零售版
     * @return
     */
    private Result<BatchImportItemRespDTO> batchItemHelper(ItemBatchImportReqDTO itemBatchImportReqDTO, Integer flag, Integer type) {
        List<ItemBatchImportTempRespDTO> itemBatchImportTempRespDTOS = itemHelper.itemBatchVerify(itemBatchImportReqDTO.getItemExcelTemplateReqDTOS(), itemBatchImportReqDTO.getGuid(), flag, type);
        if (itemBatchImportTempRespDTOS.stream().anyMatch(a -> a.getFlag().equals(1))) {
            return Result.buildSuccessResult(BatchImportItemRespDTO.builder().flag(2).list(itemBatchImportTempRespDTOS).build());
        }//全部验证通过 保存入库
        else {
            ItemDoubleParamDTO<List<ItemExcelTemplateReqDTO>, String> itemDoubleParamDTO = new ItemDoubleParamDTO<>();
            itemDoubleParamDTO.setFirstData(itemHelper.itemBatchImportConvert(itemBatchImportReqDTO.getItemExcelTemplateReqDTOS(), 0));
            itemDoubleParamDTO.setSecondData(itemBatchImportReqDTO.getGuid());
            itemDoubleParamDTO.setFrom(flag.equals(0) ? ModuleEntranceEnum.STORE.code() : ModuleEntranceEnum.BRAND.code());
            int importItemSize = itemClientService.batchImportItem(itemDoubleParamDTO);
            if (importItemSize == 0) {
                return Result.buildSuccessResult(BatchImportItemRespDTO.builder().flag(1).list(itemBatchImportTempRespDTOS).build());
            } else {
                return Result.buildSuccessResult(BatchImportItemRespDTO.builder().flag(0).list(null).build());
            }
        }
    }

    @ApiOperation(value = "kds显示获取品牌下所有商品及分类")
    @PostMapping("/query_item_by_brand_list")
    public Result<List<TypeItemListDTO>> queryStoreByBrandList(@RequestBody @Valid ItemSingleDTO itemSingleDTO) {
        log.info("获取品牌下所有商品及分类接口入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        List<TypeItemListDTO> itemListDTOList = itemClientService.queryStoreByBrandList(itemSingleDTO);
        if (CollectionUtils.isEmpty(itemListDTOList)) {
            log.warn("品牌下商品为空");
            return Result.buildEmptySuccess();
        }
        if (DisplayRuleTypeEnum.DISPLAY_BATCH.getCode().equals(itemSingleDTO.getRuleType())) {

            DisplayRuleQueryDTO queryDTO = new DisplayRuleQueryDTO();
            queryDTO.setRuleType(itemSingleDTO.getRuleType());
            if (!ObjectUtils.isEmpty(itemSingleDTO.getRuleGuid())) {
                queryDTO.setRuleGuid(itemSingleDTO.getRuleGuid());
            }
            List<DisplayItemRespDTO> displayItemDTOList = kdsClientService.listItem(queryDTO);
            log.info("查询kds商品列表结果, displayItemDTOList={}", JacksonUtils.writeValueAsString(displayItemDTOList));
            List<String> itemGuidList = displayItemDTOList.stream()
                    .map(DisplayItemRespDTO::getItemGuid)
                    .collect(Collectors.toList());
            Iterator<TypeItemListDTO> iterator = itemListDTOList.iterator();
            while (iterator.hasNext()) {
                List<TypeItemListDTO> itemList = iterator.next().getItemList();
                if (CollectionUtils.isEmpty(itemList)) {
                    iterator.remove();
                    break;
                }
                itemList.removeIf(item -> itemGuidList.contains(item.getGuid()));
                if (CollectionUtils.isEmpty(itemList)) {
                    iterator.remove();
                }
            }
        }
        return Result.buildSuccessResult(itemListDTOList);
    }

    /**
     * 批量移动商品:选中一个分类下的商品，批量移动到其它分类下
     *
     * @param typeSortReqDTO 商品guidList，目标分类guid
     * @return boolean
     */
    @ApiOperation(value = "批量移动商品")
    @PostMapping("/batch_move_item")
    public Result<Boolean> batchMoveItem(@RequestBody TypeSortReqDTO typeSortReqDTO) {
        log.info("批量移动商品入参,typeSortReqDTO={}", JacksonUtils.writeValueAsString(typeSortReqDTO));
        return Result.buildSuccessResult(itemClientService.batchMoveItem(typeSortReqDTO));
    }

    @ApiOperation(value = "erp库存为空回调 售完下架")
    @PostMapping("/erp/callback")
    public Result<Boolean> erpCallback(@RequestBody ItemErpReqDto itemErpReqDto) {
        log.info("通过门店guid查询所有菜谱方案入参：{}", JacksonUtils.writeValueAsString(itemErpReqDto));
        return Result.buildSuccessResult(itemClientService.sellOutRackItem(itemErpReqDto));
    }

    /**
     * 根据门店GUID查询所有商品详情已经分类信息（区分销售模式）
     *
     * @param itemSingleDTO storeGuid
     * @return 分类和商品详情
     */
    @ApiOperation(value = "根据门店GUID查询所有商品详情已经分类信息（区分销售模式）")
    @PostMapping("/query_store_item_By_sales_model")
    public Result<ItemInfoAndTypeRespDTO> queryStoreItemBySalesModel(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("根据门店GUID查询所有商品详情已经分类信息（区分销售模式） 入参,storeGuid={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return Result.buildSuccessResult(itemClientService.queryStoreItemBySalesModel(itemSingleDTO));
    }

    /**
     * 根据门店guidList查询所有商品并过滤
     * 只返回传入的商品GUID对应的商品的详情
     *
     * @param itemStringListDTO 门店guid，商品guid
     * @return 过滤后的商品信息
     */
    @ApiOperation(value = "根据门店guidList查询所有商品并过滤")
    @PostMapping("/query_store_item_and_filter")
    public Result<ItemInfoAndTypeRespDTO> queryStoreItemAndFilter(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("根据门店guidList查询所有商品并过滤 入参,storeGuid={}", JacksonUtils.writeValueAsString(itemStringListDTO));
        return Result.buildSuccessResult(itemClientService.queryStoreItemAndFilter(itemStringListDTO));
    }

    /**
     * 根据门店guid查询已上架的商品
     * 区分普通模式和菜谱模式
     *
     * @param itemQueryListReq 门店guid
     * @return 已上架的商品
     */
    @PostMapping("/query_for_synchronize")
    @ApiOperation(value = "查询门店全部商品", notes = "查询门店全部商品")
    public Result<ItemAndTypeForAndroidRespDTO> selectItemAndTypeForSyn(@RequestBody ItemQueryListReq itemQueryListReq) {
        log.info("正餐商品同步接口入参={}", JacksonUtils.writeValueAsString(itemQueryListReq));
        if (StringUtils.isEmpty(itemQueryListReq.getStoreGuid())) {
            throw new ParameterException("门店唯一标识不能为空");
        }
        ItemAndTypeForAndroidRespDTO respDTO = itemClientService.selectItemAndTypeForSyn(itemQueryListReq);

        // 兑换商品只能显示普通商品，不显示套餐和称重商品 18667
        respDTO.getItemList().removeIf(item -> Objects.equals(1, item.getItemType()) ||
                Objects.equals(3, item.getItemType()) || Objects.equals(5, item.getItemType()));
        return Result.buildSuccessResult(respDTO);
    }


    @ApiOperation(value = "通过sku查询parentSku", notes = "查询parentSku")
    @PostMapping("/parent_sku")
    public List<SkuInfoRespDTO> findParentSKUS(@RequestBody @Valid List<String> skuGuids) {
        log.info("通过sku查询parentSku接口入参,skuGuids={}", JacksonUtils.writeValueAsString(skuGuids));
        return itemClientService.findParentSKUS(skuGuids);
    }


    @ApiOperation(value = "根据门店当前模式查询规格商品信息", notes = "根据门店当前模式查询规格商品信息")
    @PostMapping("/list_sku_info/by_mode")
    public List<SkuInfoRespDTO> listSkuInfoByRecipeMode(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("根据门店当前模式查询规格商品信息入参,{}", JacksonUtils.writeValueAsString(itemStringListDTO));
        return itemClientService.listSkuInfoByRecipeMode(itemStringListDTO);
    }

    /**
     * 查询外卖绑定的erp商品
     * 菜谱和普通模式都查询
     *
     * @param itemSingleDTO 门店guid
     * @return erp商品
     */
    @ApiOperation(value = "查询外卖绑定的erp商品")
    @PostMapping("/query_erp_item")
    public Result<List<ErpMappingType>> queryErpItem(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("查询外卖绑定的erp商品 入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return Result.buildSuccessResult(itemClientService.queryErpItem(itemSingleDTO));
    }

    /**
     * 查询所有门店外卖绑定的erp商品
     *
     * @param itemStringListDTO 门店guid列表
     * @return 所有门店外卖绑定的erp商品
     */
    @ApiOperation(value = "查询所有门店外卖绑定的erp商品")
    @PostMapping("/query_erp_item/by_stores")
    public Result<List<StoreItemListRespDTO>> queryErpItemByStoreGuids(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("查询外卖绑定的erp商品 入参,itemStringListDTO={}", JacksonUtils.writeValueAsString(itemStringListDTO));
        if (Objects.nonNull(itemStringListDTO.getRecordId())) {
            // 查询详情里的门店列表
            List<String> storeGuids = takeoutConsumerService.queryStoreGuids(itemStringListDTO.getRecordId());
            if (!CollectionUtils.isEmpty(storeGuids)) {
                itemStringListDTO.setDataList(storeGuids);
            }
        }
        return Result.buildSuccessResult(itemClientService.queryErpItemByStoreGuids(itemStringListDTO));
    }

    /**
     * 获取品牌下所有分类及商品
     *
     * @param itemSingleDTO 品牌guid，关键字
     * @return 分类及商品
     */
    @ApiOperation(value = "获取品牌下所有分类及商品")
    @PostMapping("/list_type_and_item_by_brand")
    public Result<List<TypeItemRespDTO>> listTypeAndItemByBrand(@RequestBody @Valid ItemSingleDTO itemSingleDTO) {
        log.info("获取品牌下所有分类及商品 入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return Result.buildSuccessResult(itemClientService.listTypeAndItemByBrand(itemSingleDTO));
    }

    /**
     * 获取门店分类以及指定商品
     *
     * @param itemTypeDTO guid
     * @return 分类以及指定商品
     */
    @ApiOperation(value = "获取品牌下所有分类及商品")
    @PostMapping("/select_print_item_type")
    public Result<PrintSortRespDTO> selectPrintItemType(@RequestBody PrintItemTypeDTO itemTypeDTO) {
        log.info("获取品牌下所有分类及商品 入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemTypeDTO));
        return Result.buildSuccessResult(itemClientService.selectPrintItemType(itemTypeDTO));
    }

    @ApiOperation(value = "外卖批量绑定查询品牌库商品")
    @PostMapping("/brand/select_list")
    public Result<List<TypeSkuRespDTO>> selectList(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("[外卖批量绑定查询品牌库商品]入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(ModuleEntranceEnum.BRAND.code());
        List<TypeSkuRespDTO> typeSkuRespDTOList = itemClientService.selectList(itemSingleDTO);
        return Result.buildSuccessResult(typeSkuRespDTOList);
    }

    /**
     * 查询品牌下所有商品
     */
    @ApiOperation(value = "查询品牌下所有商品")
    @PostMapping("/query_item_by_brand")
    public Page<ItemWebRespDTO> queryItemByBrand(@RequestBody ItemQueryReqDTO queryReqDTO) {
        log.info("[查询品牌下所有商品]queryReqDTO={}", JacksonUtils.writeValueAsString(queryReqDTO));
        return itemClientService.queryItemByBrand(queryReqDTO);
    }

    /**
     * 根据商品id查询商品信息
     */
    @ApiOperation(value = "根据商品id查询商品信息")
    @PostMapping("/query_item_by_guid")
    public Result<List<ItemWebRespDTO>> queryItemByGuid(@RequestBody ItemStringListDTO query) {
        log.info("[根据商品id查询商品信息]query={}", JacksonUtils.writeValueAsString(query));
        return Result.buildSuccessResult(itemClientService.queryItemByGuid(query));
    }

    /**
     * 查询推荐商品
     */
    @ApiOperation(value = "查询推荐商品")
    @PostMapping("/query_recommend_item")
    public Result<List<ItemSynRespDTO>> queryRecommendItem(@RequestBody ItemSingleDTO query) {
        log.info("[查询推荐商品]query={}", JacksonUtils.writeValueAsString(query));
        return Result.buildSuccessResult(itemClientService.queryRecommendItem(query));
    }

}
