package com.holderzone.holder.saas.aggregation.app.entity;

import com.google.common.collect.Lists;
import com.holderzone.saas.store.util.LocaleUtil;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @create 2023-07-19
 * @description
 */
@Getter
public enum OrderLocaleEnum {


    TAKEOUT_IMMEDIATE_DELIVERY("立即送达"),
    TAKEOUT_NAME("姓名"),
    TAKEOUT_CONTACT_PHONE_NUMBER("联系电话"),
    TAKEOUT_DELIVERY_ADDRESS("送餐地址"),
    TAKEOUT_PICK_UP_IN_STORE("到店自取"),
    TAKEOUT_DELIVERY_TIME("送餐时间"),
    TAKEOUT_NOTE("备注"),
    TAKEOUT_PICK_UP_AT_STORE("到店取餐"),
    TAKEOUT_ORDER_NUMBER("订单号"),
    TAKEOUT_ORDER_PLACEMENT_TIME("下单时间"),
    TAKEOUT_ORDER_SOURCE("订单来源"),
    TAKEOUT_ORDER_AMOUNT("订单金额"),
    TAKEOUT_PAID_AMOUNT("实付金额"),
    TAKEOUT_ACCEPTANCE_TIME("接单时间"),
    TAKEOUT_ACCEPTANCE_OPERATOR("接单操作员"),
    TAKEOUT_TONGCHIDAOSHANGJIA_EDITION("通吃岛商家版"),
    TAKEOUT_EARN_MEAL_SELF_OPERATED_TAKEAWAY("赚餐自营外卖"),
    TAKEOUT_ELE_ME("饿了么"),
    TAKEOUT_JD("京东"),
    TAKEOUT_DEFAULT_ADMINISTRATOR("默认管理员"),
    TAKEOUT_COMPLETION_TIME("完成时间"),
    TAKEOUT_DELIVERY_STATUS("配送状态"),
    TAKEOUT_DELIVERY_CONFIRMED("已确认送达"),
    TAKEOUT_CANCELLATION_TIME("取消时间"),
    TAKEOUT_CANCELLATION_REASON("取消原因"),
    TAKEOUT_CANCELLATION_OPERATOR("取消操作员"),
    TAKEOUT_ORDERING_USER("下单用户"),
    TAKEOUT_AUTOMATICALLY_CANCELED("下单超时未确认，系统自动取消"),
    TAKEOUT_MEITUAN_SYSTEM("美团系统"),
    TAKEOUT_MEITUAN("美团"),
    TAKEOUT_REJECTION_TIME("拒单时间"),
    TAKEOUT_REJECTION_REASON("拒单原因"),
    TAKEOUT_UNABLE_DELIVER("无法配送"),
    TAKEOUT_REJECTION_OPERATOR("拒单操作员"),
    TAKEOUT_ALL_IN_ONE_MACHINE("一体机"),
    TAKEOUT_PROVIDING_MEALS("依据餐量提供餐具"),
    TAKEOUT_PRODUCT_SOLD_OUT("商品售罄"),
    TAKEOUT_RETURN_REQUEST_TIME("退单申请时间"),
    TAKEOUT_REFUND_ITEMS("退款菜品"),
    TAKEOUT_REASON_FOR_REFUND("退款原因"),
    TAKEOUT_REFUND_AMOUNT("退款金额"),
    ;
    @Getter
    public enum Title{
        TAKEOUT_DELIVERY_INFORMATION("送餐信息"),

        TAKEOUT_ORDER_INFORMATION("订单信息"),

        TAKEOUT_ACCEPTANCE_INFORMATION("接单信息"),

        TAKEOUT_CANCELLATION_INFORMATION("取消信息"),

        TAKEOUT_REFUND_REQUEST("退款/退单申请"),

        TAKEOUT_COMPLETION_INFORMATION("完成信息");

        private final String message;

        Title(String message){
            this.message = message;
        }
    }

    @Getter
    public enum OrderStatus{
        ORDER_NOT_SETTLED("未结账"),
        ORDER_CANCELLED("已作废"),
        ORDER_REFUNDED("已退款"),
        ORDER_REVERSED_SETTLEMENT("反结账"),
        ORDER_PENDING_DELIVERY("待配送"),
        ORDER_COMPLETED("已完成"),
        ORDER_SETTLED("已结账");

        private final String message;

        OrderStatus(String message){
            this.message = message;
        }
    }

    @Getter
    public enum TradeMode{
        TRADE_MODE_DINEIN("正餐"),
        TRADE_MODE_FAST("快餐"),
        TRADE_MODE_TAKEOUT("外卖"),
        TRADE_MODE_TAKEOUT_ZC("赚餐外卖"),
        TRADE_MODE_TAKEOUT_MT("美团外卖"),
        TRADE_MODE_TAKEOUT_ELE("饿了么外卖"),
        TRADE_MODE_TAKEOUT_JD("京东外卖");

        private final String message;

        TradeMode(String message){
            this.message = message;
        }
    }

    @Getter
    public enum RefundType {
        REFUND_TYPE_DINEIN_PART_REFUND("正餐部分退款"),
        REFUND_TYPE_DINEIN_RECOVERY("正餐反结账"),
        REFUND_TYPE_FAST_PART_REFUND("快销部分退款"),
        REFUND_TYPE_FAST_RECOVERY("快销反结账"),
        REFUND_TYPE_TOTAL("合计");

        private final String message;

        RefundType(String message) {
            this.message = message;
        }
    }

    private final String message;

    private final static List<OrderLocaleEnum> ORDER_INFO_LIST;

    static {
        ORDER_INFO_LIST = Lists.newArrayList();
        ORDER_INFO_LIST.addAll(Arrays.asList(OrderLocaleEnum.values()));
    }

    OrderLocaleEnum(String message){
        this.message = message;
    }
    public static String replaceLocaleBody(String body) {
        if (translate(body)) {
            return body;
        }
        AtomicReference<String> replaceBody = new AtomicReference<>(body);
        //替换
        ORDER_INFO_LIST.forEach(o -> replaceBody.set(replaceBody.get().replace(o.getMessage(), LocaleUtil.getMessage(o.name()))));
        return replaceBody.get();
    }

    public static String getTitleLocale(String message){
        if(translate(message)){
            return message;
        }
        //若存在多语言则通过中文寻找对应语言
        for(Title localeMessageEnum : OrderLocaleEnum.Title.values()){
            //若完全匹配
            if(localeMessageEnum.getMessage().equals(message)){
                return LocaleUtil.getMessage(localeMessageEnum.name());
            }
        }
        return message;
    }

    public static String getTradeModeLocale(String message){
        if(translate(message)){
            return message;
        }
        //若存在多语言则通过中文寻找对应语言
        for(TradeMode localeMessageEnum : OrderLocaleEnum.TradeMode.values()){
            //若完全匹配
            if(localeMessageEnum.getMessage().equals(message)){
                return LocaleUtil.getMessage(localeMessageEnum.name());
            }
        }
        return message;
    }

    public static String getOrderStatusLocale(String message){
        if(translate(message)){
            return message;
        }
        //若存在多语言则通过中文寻找对应语言
        for(OrderStatus localeMessageEnum : OrderLocaleEnum.OrderStatus.values()){
            //若完全匹配
            if(localeMessageEnum.getMessage().equals(message)){
                return LocaleUtil.getMessage(localeMessageEnum.name());
            }
        }
        return message;
    }

    public static String getRefundTypeLocale(String message) {
        if (translate(message)) {
            return message;
        }
        //若存在多语言则通过中文寻找对应语言
        for (RefundType localeMessageEnum : OrderLocaleEnum.RefundType.values()) {
            //若完全匹配
            if (localeMessageEnum.getMessage().equals(message)) {
                return LocaleUtil.getMessage(localeMessageEnum.name());
            }
        }
        return message;
    }

    static boolean translate(String message){
        if(StringUtils.isEmpty(message)){
            return true;
        }
        //若本地是简体中文直接返回
        return LocaleContextHolder.getLocale() == Locale.SIMPLIFIED_CHINESE;
    }
}
