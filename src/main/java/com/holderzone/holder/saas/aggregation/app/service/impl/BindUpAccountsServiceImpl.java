package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.BindUpAccountsService;
import com.holderzone.holder.saas.aggregation.app.service.TableService;
import com.holderzone.holder.saas.aggregation.app.service.feign.OrganizationClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.business.BindupAccountsClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DinnerDailyClientService;
import com.holderzone.saas.store.dto.organization.BusinessDateReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.store.store.BindupAccountsDTO;
import com.holderzone.saas.store.dto.store.store.BindupAccountsTips;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.reserve.api.dto.TableOrderReserveDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableServiceImpl
 * @date 2019/01/07 11:24
 * @description
 * @program holder-saas-aggregation-app
 */
@Slf4j
@Service
public class BindUpAccountsServiceImpl implements BindUpAccountsService {

    @Autowired
    private OrganizationClientService organizationClientService;

    @Autowired
    @Lazy
    private TableService tableService;

    @Resource
    private BindupAccountsClientService bindupAccountsClientService;

    @Resource
    private DinnerDailyClientService tradingClient;

    @Override
    public BindupAccountsTips loginAutoBuAccounts(TableBasicQueryDTO tableBasicQueryDTO) {
        /**
         *  是否强制扎帐 0 不用  1 可以
         */
        BindupAccountsTips bindupAccountsTips = new BindupAccountsTips();
        String storeGuid = tableBasicQueryDTO.getStoreGuid();
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(storeGuid);
        Integer integer = tradingClient.queryOrderNumForStoreGuid(storeGuid);
        //是否展示现金
        Integer isShowCash = storeDTO.getIsShowCash();
        bindupAccountsTips.setIsShowCash(isShowCash != null && isShowCash != 0);
        Integer isBuAccounts = storeDTO.getIsBuAccounts();
        //是否强制扎帐
        if ( isBuAccounts == 0 || integer == 0){
            bindupAccountsTips = this.noTipsBindUpAccount(storeGuid);
            bindupAccountsTips.setIsShowCash(isShowCash != null && isShowCash != 0);
            bindupAccountsTips.setCanOpenTable(true);
            return  bindupAccountsTips;
        }else {
            //是否能够开台
            Integer canOpenTable = storeDTO.getCanOpenTable();
            bindupAccountsTips.setCanOpenTable(canOpenTable != null && canOpenTable != 0);
        }
        BindupAccountsDTO bindupAccountsDo = bindupAccountsClientService.queryBindUpAccountsLast(tableBasicQueryDTO.getStoreGuid());
        log.info("门店最早数据信息：{}", JacksonUtils.writeValueAsString(bindupAccountsDo));
        //当前时间营业日
        LocalDate localDate = this.currentTimeDay(storeGuid,null);
        if (bindupAccountsDo == null || bindupAccountsDo.getBindupAccounts() == null){
            //判断最早订单是否是今日，如果是今日则不需要提示扎帐信息
            LocalDateTime localDateTime = tradingClient.getfristorderForStoreGuid(storeGuid);
            LocalDate storeFirstOrder = this.currentTimeDay(storeGuid, localDateTime);
            log.info("当前门店的guid数据第一步订单时间：{}",storeFirstOrder);
            log.info("当前时间的guid比较：{}",localDate);
            log.info("最早订单数据时间：{}",JacksonUtils.writeValueAsString(localDateTime));
            if (localDate.compareTo(storeFirstOrder) != 0) {
                //之前从未扎帐
                bindupAccountsTips.setTips(1);
                bindupAccountsTips.setCurrTime(localDate);
                return bindupAccountsTips;
            }else {
                bindupAccountsTips.setTips(0);
                bindupAccountsTips.setCanOpenTable(true);
                bindupAccountsTips.setCurrTime(localDate);
                return bindupAccountsTips;
            }
        }
        // 系统扎帐最新时间
        LocalDateTime bindupAccounts = bindupAccountsDo.getBindupAccounts();
        LocalDate currlocalData = bindupAccounts.toLocalDate();
        LocalDate yesterdayTime  = localDate.plusDays(-1);
        if (currlocalData.compareTo(yesterdayTime) < 0 ) {
            // 昨日未扎帐
            bindupAccountsTips.setTips(1);
            bindupAccountsTips.setCurrTime(localDate);
            return bindupAccountsTips;
        }
        if (currlocalData.compareTo(yesterdayTime) ==0) {
            // 昨日扎帐，需要判断扎帐之后有无新开桌台
            StopWatch stopWatch = new StopWatch();
            List<TableOrderReserveDTO> tableOrderReserveDTOS = tableService.queryTable(tableBasicQueryDTO, stopWatch);
            stopWatch.stop();
            //判断当前桌台是否存在占用情况  拿到桌台判断是否是
            List<TableOrderReserveDTO> collect = tableOrderReserveDTOS.stream().filter(tableOrderReserveDTO -> tableOrderReserveDTO.getStatus() == 1).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect) ) {
                for (TableOrderReserveDTO tord : collect) {
                    LocalDateTime openTableTime = tord.getOpenTableTime();
                    //拿到这个时间判断是否门店扎帐时间
                    LocalDate history = this.currentTimeDay(storeGuid,openTableTime);
                    if (yesterdayTime.compareTo(history) == 0) {
                        //昨日扎帐后 桌台又有未关闭桌台
                        bindupAccountsTips.setTips(1);
                        bindupAccountsTips.setCurrTime(localDate);
                        return bindupAccountsTips;
                    }
                }
            }
        }
        //判断当前卓天昨日是否扎帐，昨日扎帐时间要在所有未管关台之前
        bindupAccountsTips.setTips(0);
        bindupAccountsTips.setCanOpenTable(true);
        bindupAccountsTips.setCurrTime(localDate);
        return bindupAccountsTips;
    }

    @Override
    public BindupAccountsTips noTipsBindUpAccount(String storeGuid) {
        //获取当前时间营业日期
        LocalDate localDate = currentTimeDay(storeGuid,null);
        //构建对象
        BindupAccountsTips bindupAccountsTips = new BindupAccountsTips();
        bindupAccountsTips.setTips(0);
        bindupAccountsTips.setCurrTime(localDate);
        return bindupAccountsTips;
    }

    /**
     * 当前时间的营业日
     * @return
     */
    @Override
    public LocalDate currentTimeDay(String storeGuid,LocalDateTime localDateTime){
        BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        ArrayList<String> str = new ArrayList<>();
        str.add(storeGuid);
        businessDateReqDTO.setStoreGuidList(str);
        if (localDateTime != null) {
            businessDateReqDTO.setQueryDateTime(localDateTime);
        }
        return organizationClientService.queryBusinessDay(businessDateReqDTO);
    }

    @Override
    public void checkTableStatus(String storeGuid) {
        /**
         * 昨日是否已扎帐
         *          桌台状态信息，是否已扎帐
         */
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(storeGuid);
        log.info("获取门店信息：{}",JacksonUtils.writeValueAsString(storeDTO));
        Integer isBuAccounts = storeDTO.getIsBuAccounts();
        //是否强制扎帐
        if ( isBuAccounts == 0 ){
            return;
        }
        //最新扎帐日期
        BindupAccountsDTO lastBuAccounts = bindupAccountsClientService.queryBindUpAccountsLast(storeGuid);
        log.info("获取最新扎帐日期信息：{}",JacksonUtils.writeValueAsString(lastBuAccounts));
        //从未扎帐
        if (lastBuAccounts == null || lastBuAccounts.getBindupAccounts() == null) {
            return;
        }
        LocalDate localDate = this.currentTimeDay(storeGuid,null);
        LocalDate yesterdayTime  = localDate.plusDays(-1);
        LocalDateTime bindupAccounts = lastBuAccounts.getBindupAccounts();
        LocalDate storeLastBuAccounts = bindupAccounts.toLocalDate();
        log.info("扎帐时间对比storeLastBuAccounts：{}",JacksonUtils.writeValueAsString(storeLastBuAccounts));
        log.info("扎帐时间对比yesterdayTime：{}",JacksonUtils.writeValueAsString(yesterdayTime));
        // 昨日未扎帐
        if (storeLastBuAccounts.compareTo(yesterdayTime) < 0) {
            return;
        }
        // 昨日已扎帐，需要判断扎帐之后有无新开桌台
        StopWatch stopWatch = new StopWatch();
        TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid(storeGuid);
        List<TableOrderReserveDTO> tableOrderReserveDTOS = tableService.queryTable(tableBasicQueryDTO, stopWatch);
        stopWatch.stop();
        //判断当前桌台是否存在占用情况  拿到桌台判断是否是
        List<TableOrderReserveDTO> collect = tableOrderReserveDTOS.stream().filter(tableOrderReserveDTO -> tableOrderReserveDTO.getStatus() == 1).collect(Collectors.toList());
        log.info("门店是否关台列表：{}",JacksonUtils.writeValueAsString(collect));
        for (TableOrderReserveDTO tableStatus : collect) {
            // 桌台开台时间
            LocalDateTime openTableTime = tableStatus.getOpenTableTime();
            LocalDate tableOpenTime = this.currentTimeDay(storeGuid, openTableTime);
            //存在有昨日未关台桌台
            if (tableOpenTime.compareTo(localDate) < 0) {
                return;
            }
        }
        log.info("关台发送mq信息内容：{}",JacksonUtils.writeValueAsString(storeGuid));
        //不存在昨日桌台,发送mq信息给一体机
        bindupAccountsClientService.sendMqForCanOpenTalbe(storeGuid);
    }
}
