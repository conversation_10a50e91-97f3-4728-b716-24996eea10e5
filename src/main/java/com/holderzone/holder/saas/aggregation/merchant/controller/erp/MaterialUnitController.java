package com.holderzone.holder.saas.aggregation.merchant.controller.erp;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp.MaterialUnitFeignService;
import com.holderzone.saas.store.dto.erp.MaterialUnitDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/05 17:30
 */
@Api(tags = "物料单位Api")
@RestController
@RequestMapping("materialUnit")
public class MaterialUnitController {
    private static final Logger LOGGER = LoggerFactory.getLogger(MaterialUnitController.class);
    @Autowired
    MaterialUnitFeignService materialUnitService;

    @PostMapping
    @ApiOperation("增加单位")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "增加单位",action = OperatorType.ADD)
    public Result<Object> add(@RequestBody MaterialUnitDTO materialUnitDTO) {
        LOGGER.info("添加单位入参:->{}", JacksonUtils.writeValueAsString(materialUnitDTO));
        materialUnitService.add(materialUnitDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("list")
    @ApiOperation("获取单位列表")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "获取单位列表",action = OperatorType.SELECT)
    public Result<List<MaterialUnitDTO>> findList() {
        return Result.buildSuccessResult(materialUnitService.findList());
    }
}
