package com.holderzone.holder.saas.aggregation.merchant.controller.erp;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp.RepertoryRpcService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.erpretail.*;
import com.holderzone.saas.store.dto.erp.erpretail.req.*;
import com.holderzone.saas.store.dto.erp.erpretail.resp.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @className WarehouseController
 * @date 2019-10-28 18:08:44
 * @description
 */
@Slf4j
@Api(tags = "库存信息")
@RestController
@RequestMapping("/repertory")
public class RepertoryController {

    @Autowired
    private RepertoryRpcService repertoryRpcService;

    @ApiOperation("新建库存单")
    @PostMapping("/insert_repertory")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "新建库存单",action = OperatorType.ADD)
    public Result<Boolean> insertRepertory(@RequestBody @Validated CreateRepertoryReqDTO createRepertoryReqDTO) {
        return Result.buildSuccessResult(repertoryRpcService.insertRepertory(createRepertoryReqDTO));
    }

    @ApiOperation("查询商品库存汇总信息")
    @PostMapping("/query_goods_repertory_sum_info")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "查询商品库存汇总信息",action = OperatorType.SELECT)
    public Result<Page<GoodsSumInfoRespDTO>> queryGoodsRepertorySumInfo(@RequestBody @Validated QueryGoodsSumInfoReqDTO queryGoodsSumInfoReqDTO) {
        return Result.buildSuccessResult(repertoryRpcService.queryGoodsRepertorySumInfo(queryGoodsSumInfoReqDTO));
    }

    @ApiOperation("查询商品出入库流水")
    @PostMapping("/query_goods_serial")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "查询商品出入库流水",action = OperatorType.SELECT)
    public Result<Page<GoodsSerialRespDTO>> queryGoodsInOutRepertoryRecord(@RequestBody @Validated QueryGoodsSerialReqDTO queryGoodsSerialReqDTO) {
        log.info("查询商品出入库流水：{}", JacksonUtils.writeValueAsString(queryGoodsSerialReqDTO));
        return Result.buildSuccessResult(repertoryRpcService.queryGoodsSerial(queryGoodsSerialReqDTO));
    }

    @ApiOperation("查询出入库单据及其明细(仅查看时使用)")
    @PostMapping("/query_repertory_detail")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "查询出入库单据及其明细(仅查看时使用)",action = OperatorType.SELECT)
    public Result<RepertoryDetailInfoRespDTO> queryRepertoryDetail(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("查询出入库单据及其明细(仅查看时使用)入参：{}", JacksonUtils.writeValueAsString(singleDataDTO));
        if (StringUtils.isEmpty(singleDataDTO.getData())) {
            throw new ParameterException("单据guid不能为空");
        }
        return Result.buildSuccessResult(repertoryRpcService.queryRepertoryDetail(singleDataDTO));
    }

    @ApiOperation("查询出入库列表(分页)")
    @PostMapping("/query_in_out_repertory_list")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "查询出入库列表(分页)",action = OperatorType.SELECT)
    public Result<Page<RepertoryManageRespDTO>> queryInOutRepertoryList(@RequestBody @Validated QueryRepertoryManageReqDTO queryRepertoryManageReqDTO) {
        log.info("查询出入库列表(分页)入参: {}", JacksonUtils.writeValueAsString(queryRepertoryManageReqDTO));
        return Result.buildSuccessResult(repertoryRpcService.queryInOutRepertoryList(queryRepertoryManageReqDTO));
    }

    @ApiOperation("作废库存单")
    @PostMapping("/invalid_repertory")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "作废库存单",action = OperatorType.SELECT)
    public Result<Boolean> invalidRepertory(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("作废库存单入参: {}", JacksonUtils.writeValueAsString(singleDataDTO));
        return Result.buildSuccessResult(repertoryRpcService.invalidRepertory(singleDataDTO));
    }

    @ApiOperation("查询商品列表")
    @PostMapping("/query_goods_list")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "查询商品列表",action = OperatorType.SELECT)
    public Result<List<GoodsClassifyAndItemRespDTO>> queryGoodsList(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("查询商品列表");
        return Result.buildSuccessResult(repertoryRpcService.queryGoodsList(singleDataDTO));
    }

}
