package com.holderzone.holder.saas.aggregation.app.aop;

import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.app.anno.DataAuthFieldControl;
import com.holderzone.holder.saas.aggregation.app.service.feign.staff.UserClientService;
import com.holderzone.holder.saas.member.terminal.enums.TerminalTypeEnum;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.user.MenuSourceDTO;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 字段权限 控制
 */
@Slf4j
@Aspect
@Component
public class DataAuthControlAspect {

    @Autowired
    private UserClientService userClientService;

    @AfterReturning(pointcut = "@annotation(com.holderzone.holder.saas.aggregation.app.anno.DataAuthMethodControl)", returning = "result")
    public void afterReturn(Object result) throws Exception {
        // 查询权限code
        List<MenuSourceDTO> sourceList = userClientService.getSourceByUser(String.valueOf(TerminalTypeEnum.TERMINAL_AIO.getCode()));
        List<String> sourceCodeList = sourceList.stream()
                .map(MenuSourceDTO::getSourceCode)
                .distinct()
                .collect(Collectors.toList());
        encryption(result, sourceCodeList);
    }


    private void encryption(Object result, List<String> sourceCodeList) throws Exception {
        if (Objects.isNull(result)) {
            return;
        }
        if (result instanceof List<?>) {
            List<?> list = (List<?>) result;
            boolean isAllFieldEncryption = false;
            for (Object object : list) {
                // 使用反射获取字段
                isAllFieldEncryption = encryptionObj(object, sourceCodeList);
            }
            DataAuthFieldControl annotation = result.getClass().getAnnotation(DataAuthFieldControl.class);
            if (Objects.nonNull(annotation)) {
                boolean isListClear = annotation.isListClear();
                if (isListClear && isAllFieldEncryption) {
                    list.clear();
                }
            }
        } else {
            encryptionObj(result, sourceCodeList);
        }
    }

    private boolean encryptionObj(Object object, List<String> sourceCodeList) throws Exception {
        int encryptionAllFieldSize = 0;
        int unEncryptionFieldSize = 0;
        // 使用反射获取字段
        for (Field field : object.getClass().getDeclaredFields()) {
            DataAuthFieldControl annotation = field.getAnnotation(DataAuthFieldControl.class);
            if (Objects.isNull(annotation)) {
                continue;
            }
            encryptionAllFieldSize = encryptionAllFieldSize + 1;
            String sourceCode = annotation.value();
            if (!StringUtils.isEmpty(sourceCode) && !sourceCodeList.contains(sourceCode)) {
                unEncryptionFieldSize = unEncryptionFieldSize + 1;
                // 无权限
                ReflectionUtils.makeAccessible(field);
                if (List.class.isAssignableFrom(field.getType())) {
                    ReflectionUtils.setField(field, object, null);
                } else {
                    ReflectionUtils.setField(field, object, Constant.ENCRYPTION_SYMBOL);
                }
                continue;
            }
            if (List.class.isAssignableFrom(field.getType())) {
                ReflectionUtils.makeAccessible(field);
                encryption(field.get(object), sourceCodeList);
            }
        }
        return encryptionAllFieldSize > 0 && encryptionAllFieldSize == unEncryptionFieldSize;
    }

}