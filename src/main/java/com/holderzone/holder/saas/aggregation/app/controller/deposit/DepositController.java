package com.holderzone.holder.saas.aggregation.app.controller.deposit;

import cn.hutool.core.collection.CollectionUtil;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.app.constant.Constant;
import com.holderzone.holder.saas.aggregation.app.service.feign.deposit.DepositRpcService;
import com.holderzone.saas.store.dto.deposit.req.*;
import com.holderzone.saas.store.dto.deposit.resp.*;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/deposit")
@Api(description = "寄存接口")
public class DepositController {

    private final DepositRpcService depositRpcService;

    @Autowired
    public DepositController(DepositRpcService depositRpcService) {
        this.depositRpcService = depositRpcService;
    }

    @PostMapping("/create_deposit_item")
    @ApiOperation(value = "新建寄存记录")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "新建寄存记录",action = OperatorType.ADD)
    public Result<Boolean> createDepositItem(@RequestBody @Validated DepositCreateReqDTO depositCreateDTO) {
        if (log.isInfoEnabled()) {
            log.info("新建寄存记录入参:{}", JacksonUtils.writeValueAsString(depositCreateDTO));
        }
        return Result.buildSuccessResult(depositRpcService.createDepositItem(depositCreateDTO));
    }

    @PostMapping("/query_deposit_item")
    @ApiOperation(value = "查询寄存记录")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "查询寄存记录",action = OperatorType.SELECT)
    public Result<Page<DepositQueryRespDTO>> queryDepositItem(@RequestBody @Validated DepositQueryReqDTO depositQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询寄存记录入参:{}", JacksonUtils.writeValueAsString(depositQueryReqDTO));
        }
        Page<DepositQueryRespDTO> depositQueryRespDTOPage = depositRpcService.queryDepositItem(depositQueryReqDTO);
        if(depositQueryRespDTOPage!= null && CollectionUtil.isNotEmpty(depositQueryRespDTOPage.getData())){
            depositQueryRespDTOPage.getData().forEach(d ->{
                if (d.getExpireTime().equals(Constant.PERMANENT_VALIDITY)){
                    d.setExpireTime(LocaleUtil.getMessage("PERMANENT_VALIDITY"));
                }
            });
        }
        return Result.buildSuccessResult(depositQueryRespDTOPage);
    }

    @PostMapping("/query_deposit_detail")
    @ApiOperation(value = "查询寄存记录详情")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "查询寄存记录详情",action = OperatorType.SELECT)
    public Result<List<GoodsRespDTO>> queryDepositDetail(@RequestBody @Validated QueryDepositDetailReqDTO depositQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询寄存记详情入参:{}", JacksonUtils.writeValueAsString(depositQueryReqDTO));
        }
        List<GoodsRespDTO> list = depositRpcService.queryDepositDetail(depositQueryReqDTO);
        if(CollectionUtil.isNotEmpty(list)){
            list.forEach( g ->{
                if (g.getExpireTime().equals(Constant.PERMANENT_VALIDITY)){
                    g.setExpireTime(LocaleUtil.getMessage("PERMANENT_VALIDITY"));
                }
            });
        }
        return Result.buildSuccessResult(list);
    }

    @PostMapping("/get_deposit_goods")
    @ApiOperation(value = "取出寄存商品")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "取出寄存商品",action = OperatorType.SELECT)
    public Result<Boolean> getDepositGoods(@RequestBody @Validated DepositGetReqDTO depositGetReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("取出寄存商品:{}", JacksonUtils.writeValueAsString(depositGetReqDTO));
        }
        return Result.buildSuccessResult(depositRpcService.getDepositGoods(depositGetReqDTO));
    }

    @PostMapping("/query_operation_history")
    @ApiOperation(value = "查询操作历史记录")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "查询操作历史记录",action = OperatorType.SELECT)
    public Result<Page<OperationQueryRespDTO>> queryOperationHistory(@RequestBody @Validated OperationHistoryQueryReqDTO operationHistoryQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询操作历史记录:{}", JacksonUtils.writeValueAsString(operationHistoryQueryReqDTO));
        }
        return Result.buildSuccessResult(depositRpcService.queryOperationHistory(operationHistoryQueryReqDTO));
    }

    @PostMapping("/query_goods_summary")
    @ApiOperation(value = "商品寄存汇总")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "商品寄存汇总",action = OperatorType.SELECT)
    public Result<Page<GoodsSummaryRespDTO>> queryGoodsSummary(@RequestBody @Validated DepositQueryReqDTO depositQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询商品寄存汇总入参:{}", JacksonUtils.writeValueAsString(depositQueryReqDTO));
        }
        return Result.buildSuccessResult(depositRpcService.queryGoodsSummary(depositQueryReqDTO));
    }

    @PostMapping("/query_deposit_detail_for_pos")
    @ApiOperation(value = "查询寄存记录详情")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "查询寄存记录详情_POS",action = OperatorType.SELECT)
    public Result<DepositDetailForPosRespDTO> queryDepositDetailForPos(@RequestBody @Validated QueryDepositDetailReqDTO depositQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询寄存记详情入参:{}", JacksonUtils.writeValueAsString(depositQueryReqDTO));
        }
        return Result.buildSuccessResult(depositRpcService.queryDepositDetailForPos(depositQueryReqDTO));
    }
}
