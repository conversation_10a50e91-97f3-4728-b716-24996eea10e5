package com.holderzone.holder.saas.aggregation.app.service.feign.member;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.account.NewMemberInfoClientService;
import com.holderzone.holder.saas.member.terminal.dto.member.response.SimpleMemberInfoDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/8
 */
@Component
@FeignClient(name = "holder-saas-store-member", fallbackFactory = StoreMemberClientService.StoreMemberClientServiceFallBack.class)
public interface StoreMemberClientService {

    @ApiOperation("根据手机号后四位查询会员")
    @GetMapping(value = "/query_member_by_phone_tail")
    List<SimpleMemberInfoDTO> queryMemberByPhoneTail(@RequestParam("phoneTail") String phoneTail);

    @Slf4j
    @Component
    class StoreMemberClientServiceFallBack implements FallbackFactory<StoreMemberClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public StoreMemberClientService create(Throwable throwable) {
            return new StoreMemberClientService() {
                @Override
                public List<SimpleMemberInfoDTO> queryMemberByPhoneTail(String phoneTail) {
                    log.error(HYSTRIX_PATTERN, "根据手机号后四位查询会员", phoneTail, ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }
}
