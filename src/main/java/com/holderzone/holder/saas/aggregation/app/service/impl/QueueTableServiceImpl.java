package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.holder.saas.aggregation.app.service.QueueTableService;
import com.holderzone.holder.saas.aggregation.app.service.feign.queue.QueueClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.table.TableClientService;
import com.holderzone.saas.store.dto.queue.QueueGuidDTO;
import com.holderzone.saas.store.dto.queue.QueueTableDTO;
import com.holderzone.saas.store.dto.queue.TableDTO;
import com.holderzone.saas.store.dto.table.AreaDTO;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.table.TableOrderDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueTableServiceImpl
 * @date 2019/05/14 16:37
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Service
public class QueueTableServiceImpl implements QueueTableService {
    @Autowired
    private TableClientService tableClientService;
    @Autowired
    private QueueClientService queueClientService;

    @Override
    public QueueTableDTO mergeTables(QueueGuidDTO queueGuidDTO) {
        QueueTableDTO queueTableDTO = queueClientService.obtain(queueGuidDTO);
        TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid(queueGuidDTO.getStoreGuid());
        List<AreaDTO> areaDTOS = tableClientService.queryArea(queueGuidDTO.getStoreGuid());
        Map<String, String> areaNameRef = areaDTOS.stream().collect(Collectors.toMap(AreaDTO::getGuid, AreaDTO::getAreaName));
        List<TableOrderDTO> tableBasicDTOS = tableClientService.queryTable(tableBasicQueryDTO);
        List<TableDTO> all = tableBasicDTOS.stream()
                .filter((e) -> e.getStatus() == 0)
//                .filter((e) -> e.getSubStatus() == null || !e.getSubStatus().contains(20))
                .map((e) -> {
                    TableDTO tableDTO = new TableDTO();
                    tableDTO.setAreaGuid(e.getAreaGuid());
                    tableDTO.setAreaName(areaNameRef.get(e.getAreaGuid()));
                    tableDTO.setTableGuid(e.getTableGuid());
                    tableDTO.setSeats(e.getSeats());
                    tableDTO.setTableName(e.getTableCode());
                    return tableDTO;
                }).collect(Collectors.toList());
        Map<String, TableDTO> allTableRef = all.stream()
                .collect(Collectors.toMap(TableDTO::getTableGuid, Function.identity()));
        List<TableDTO> tables = new ArrayList<>();
        Optional.ofNullable(queueTableDTO.getTables()).ifPresent(e -> {
            e.forEach((s) ->
                    Optional.ofNullable(allTableRef.get(s.getTableGuid())).ifPresent((d) -> {
                        tables.add(d);
                        all.remove(d);
                    })
            );
            all.sort(Comparator.comparing(TableDTO::getTableName));
            tables.addAll(all);
            queueTableDTO.setTables(tables);
        });
        return queueTableDTO;
    }
}