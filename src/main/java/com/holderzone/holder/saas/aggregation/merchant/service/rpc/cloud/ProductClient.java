package com.holderzone.holder.saas.aggregation.merchant.service.rpc.cloud;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.resource.common.dto.product.MenuDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className MenuClient
 * @date 18-10-12 下午6:57
 * @description
 * @program holder-saas-store-staff
 */
@Component
@FeignClient(name = "product-service", fallbackFactory = ProductClient.FallBackService.class)
public interface ProductClient {
    @GetMapping("/menu/list/{terminalGuid}")
    List<MenuDTO> getMenusByTerminalGuid(@PathVariable("terminalGuid") String terminalGuid);

    @Slf4j
    @Component
    class FallBackService implements FallbackFactory<ProductClient> {
        @Override
        public ProductClient create(Throwable cause) {
            return terminalGuid -> {
                log.error("登陆后查询菜单列表失败，e{}", cause.getMessage());
                throw new BusinessException("登陆后查询菜单列表失败");
            };
        }
    }
}
