package com.holderzone.holder.saas.aggregation.merchant.service;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.journaling.ReportClientService;
import com.holderzone.saas.store.dto.journaling.req.SaleDetailReportReqDTO;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/5/11 18:08
 * @description 异步方法
 */
@Component
public class SyncExecutor {

    @Async
    public void syncUploadOssData(ReportClientService reportClientService, SaleDetailReportReqDTO saleDetailReportReqDTO,String json) {
        UserContextUtils.put(json);
        reportClientService.saleDetailExportOss(saleDetailReportReqDTO);
    }
}
