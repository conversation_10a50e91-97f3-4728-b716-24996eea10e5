package com.holderzone.holder.saas.aggregation.app.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.app.service.TradeService;
import com.holderzone.holder.saas.aggregation.app.service.feign.MessageClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.msg.MsgClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.organization.OrgFeignClient;
import com.holderzone.holder.saas.aggregation.app.service.feign.pay.AggPayClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInBillClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.OrderItemClientService;
import com.holderzone.holder.saas.aggregation.app.utils.BigDecimalUtil;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillAggPayReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.BillAggPayRespDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.organization.PadOrderTypeReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.dto.pay.SaasPollingDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.trade.req.PadPayInfoReqDTO;
import com.holderzone.saas.store.dto.trade.resp.PadOrderRespDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.constant.Constant.NUMBER_ZERO;
import static com.holderzone.saas.store.constant.Constant.TRUE;

/**
 * <AUTHOR>
 * @description 聚合层交易服务实现
 * @date 2021/9/13 18:15
 * @className: TradeServiceImpl
 */
@Slf4j
@Service
public class TradeServiceImpl implements TradeService {

    private final AggPayClientService aggPayClientService;

    private final DineInBillClientService dineInBillClientService;

    private final OrderItemClientService orderItemClientService;

    private final MessageClientService messageClientService;

    private final MsgClientService msgClientService;

    private final OrgFeignClient orgFeignClient;

    public TradeServiceImpl(AggPayClientService aggPayClientService, DineInBillClientService dineInBillClientService,
                            OrderItemClientService orderItemClientService, MessageClientService messageClientService, MsgClientService msgClientService, OrgFeignClient orgFeignClient) {
        this.aggPayClientService = aggPayClientService;
        this.dineInBillClientService = dineInBillClientService;
        this.orderItemClientService = orderItemClientService;
        this.messageClientService = messageClientService;
        this.msgClientService = msgClientService;
        this.orgFeignClient = orgFeignClient;
    }

    /**
     * 发起预支付
     *
     * @param orderGuid  订单guid
     * @param payPowerId 支付功能的id
     * @return 二维码链接
     */
    @Override
    public String padPay(String orderGuid, String payPowerId) throws InterruptedException {
        OrderDTO orderDTO = orderItemClientService.findByOrderGuid(orderGuid);
        //判断订单状态
        if(isFinishOrder(orderDTO.getState())){
            throw new BusinessException("当前订单状态无法支付");
        }
        // 获取缓存pad支付信息
        PadPayInfoReqDTO padPayInfo = orderItemClientService.getPadPayInfo(orderGuid);
        if (ObjectUtils.isEmpty(padPayInfo) && Objects.equals(2, orderDTO.getUpperState())) {
            padPayInfo = orderItemClientService.getPadPayInfo(orderDTO.getMainOrderGuid());
        }
        if (ObjectUtils.isEmpty(padPayInfo)) {
            throw new BusinessException("支付信息为空或已完成");
        }
        //支付金额为0直接返回
        if(padPayInfo.getActuallyPayFee() == null || padPayInfo.getActuallyPayFee().compareTo(BigDecimal.ZERO) == 0){
            throw new BusinessException("支付金额为0，请到前台结算");
        }
        if (Objects.equals(2, orderDTO.getUpperState())) {
            orderGuid = orderDTO.getMainOrderGuid();
        }

        // 后续支付头部数据完善
        UserContext userContext = UserContextUtils.get();
        userContext.setOperSubjectGuid(padPayInfo.getOperSubjectGuid());
        userContext.setStoreGuid(padPayInfo.getStoreGuid());
        userContext.setStoreName(padPayInfo.getStoreName());
        userContext.setUserName(Optional.ofNullable(padPayInfo.getNickName()).orElse("未登录用户"));
        userContext.setUserGuid(Optional.ofNullable(padPayInfo.getMemberInfoGuid()).orElse("0"));
        UserContextUtils.put(userContext);

        // 推送消息到安卓表示扫码成功-指定设备 topic: 6506431195651982337/6619160595813892096/20:PFSNU18416106187
        pushMesToAndroid(padPayInfo);

        // 发起预支付
        BillAggPayReqDTO aggPayReqDTO = getBillAggPayReqDTO(orderGuid, payPowerId, padPayInfo);
        log.info("发起预支付 aggPayReqDTO={}", JacksonUtils.writeValueAsString(aggPayReqDTO));
        BillAggPayRespDTO aggPayRespDTO = dineInBillClientService.aggPay(aggPayReqDTO);
        log.info("发起预支付 aggPayRespDTO={}", JacksonUtils.writeValueAsString(aggPayRespDTO));
        if (aggPayRespDTO.getEstimate() != null && aggPayRespDTO.getEstimate()) {
            throw new BusinessException(aggPayRespDTO.getEstimateInfo());
        }

        // 预支付成功推送消息到安卓
        pushPayMesToAndroid(padPayInfo, aggPayRespDTO.getPayGuid());

        SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setOrderGuid(orderGuid);
        saasPollingDTO.setPayGuid(aggPayRespDTO.getPayGuid());
        saasPollingDTO.setEnterpriseGuid(padPayInfo.getEnterpriseGuid());
        saasPollingDTO.setStoreGuid(padPayInfo.getStoreGuid());
        log.info("聚合支付查询支付结果 saasPollingDTO={}", JacksonUtils.writeValueAsString(saasPollingDTO));
        long starTime = System.currentTimeMillis();
        String codeUrl = polling(saasPollingDTO, starTime);
        if (StringUtils.isEmpty(codeUrl)) {
            throw new BusinessException("支付链接已失效");
        }
        return codeUrl;
    }

    private boolean isFinishOrder(Integer state) {
        if (state == null) {
            return Boolean.FALSE;
        }
        if(state == 1 || state == 2 || state == 3){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 轮询预支付结果
     *
     * @param saasPollingDTO 预支付请求
     * @return 支付地址
     * @throws InterruptedException 线程异常
     */
    private String polling(SaasPollingDTO saasPollingDTO, long starTime) throws InterruptedException {
        AggPayPollingRespDTO query = aggPayClientService.query(saasPollingDTO);
        log.info("聚合支付查询支付结果 query={}", JacksonUtils.writeValueAsString(query));
        String codeUrl = query.getCodeUrl();
        log.info("codeUrl1={}", codeUrl);
        long payTime = 30000;

        // 没得法子，聚合支付就是这么返回的 “轮询接口：看实体中paySt的描述，付款中则需要继续轮询”
        if (StringUtils.isEmpty(codeUrl) && Objects.equals("支付中，请稍等！", query.getMsg())) {
            long endTime = System.currentTimeMillis();
            long now = endTime - starTime;
            log.info("pad扫码支付轮询 starTime={} endTime={} now={}", starTime, endTime, now);
            if (payTime > now) {
                Thread.sleep(2000);
                codeUrl = polling(saasPollingDTO, starTime);
            }
        }
        log.info("codeUrl2={}", codeUrl);
        return codeUrl;
    }

    public String cyclicResult(SaasPollingDTO saasPollingDTO) {
        int pay = 30;
        try {
            while (true) {
                AggPayPollingRespDTO query = aggPayClientService.query(saasPollingDTO);
                log.info("聚合支付查询支付结果 query={}", JacksonUtils.writeValueAsString(query));
                String codeUrl = query.getCodeUrl();
                pay--;
                Thread.sleep(1000);

                if (ObjectUtil.isNotNull(codeUrl)) {
                    return codeUrl;
                }
                if (pay <= 0) {
                    return null;
                }
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 并台发送结账成功推送消息
     *
     * @param orderGuid 订单guid
     * @return 结果
     */
    @Override
    public Boolean sendMsgWhenCombine(String orderGuid) {
        // 支付成功删除缓存-不删也行，还方便排查问题，如有需要后续删

        // 获取订单并台的所有下单信息
        List<PadOrderRespDTO> padOrderRespDTOS = orderItemClientService.listPadOrderInfoOnCombine(orderGuid);
        if (CollectionUtils.isEmpty(padOrderRespDTOS)) {
            log.warn("pad下单信息为空 orderGuid={}", orderGuid);
            return Boolean.FALSE;
        }
        String storeGuid = padOrderRespDTOS.get(0).getStoreGuid();
        List<String> tableGuidList = padOrderRespDTOS.stream()
                .map(PadOrderRespDTO::getDiningTableGuid)
                .distinct()
                .collect(Collectors.toList());

        BusinessMessageDTO payMessageDTO = new BusinessMessageDTO();
        payMessageDTO.setSubject(BusinessMsgTypeEnum.MERGE_TABLE_PAY.getName());

        // 查询订单金额以供其他设备展示
        OrderDTO orderDTO = orderItemClientService.findByOrderGuid(orderGuid);
        BigDecimal combineFee = BigDecimal.ONE;
        if (Objects.equals(2, orderDTO.getUpperState())) {
            List<OrderDTO> subOrderDTOList = orderItemClientService.listOrderByCombineOrderGuid(orderDTO.getMainOrderGuid());
            subOrderDTOList.add(orderItemClientService.findByOrderGuid(orderDTO.getMainOrderGuid()));
            combineFee = subOrderDTOList.stream()
                    .map(OrderDTO::getActuallyPayFee)
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);
        } else if (Objects.equals(1, orderDTO.getUpperState())) {
            List<OrderDTO> subOrderDTOList = orderItemClientService.listOrderByCombineOrderGuid(orderGuid);
            subOrderDTOList.add(orderDTO);
            combineFee = subOrderDTOList.stream()
                    .map(OrderDTO::getActuallyPayFee)
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);
        } else {
            combineFee = orderDTO.getActuallyPayFee();
        }

        payMessageDTO.setContent(String.valueOf(combineFee));
        payMessageDTO.setMessageType(BusinessMsgTypeEnum.PAD_MESSAGE.getId());
        payMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.MERGE_TABLE_PAY.getId());
        payMessageDTO.setPlatform("2");
        payMessageDTO.setStoreGuid(storeGuid);

        StoreDTO storeDTO = orgFeignClient.queryStoreByGuid(storeGuid);
        payMessageDTO.setStoreName(storeDTO.getName());

        PadOrderTypeReqDTO reqDTO = new PadOrderTypeReqDTO();
        reqDTO.setStoreGuid(storeGuid);
        reqDTO.setTableGuidList(tableGuidList);
        List<StoreDeviceDTO> storeDeviceDTOList = orgFeignClient.listDeviceByStoreTable(reqDTO);
        if (!CollectionUtils.isEmpty(storeDeviceDTOList)) {
            storeDeviceDTOList.forEach(device -> {
                payMessageDTO.setMessageTypeStr(BusinessMsgTypeEnum.PAD_MESSAGE.getId() + ":" + device.getDeviceNo());
                log.info("combineMessageDTO={}", JacksonUtils.writeValueAsString(payMessageDTO));
                messageClientService.msg(payMessageDTO);
            });
        }
        return Boolean.TRUE;
    }

    /**
     * 预支付成功推送消息到安卓
     *
     * @param padPayInfo pad支付信息
     * @param payGuid    支付id
     */
    private void pushPayMesToAndroid(PadPayInfoReqDTO padPayInfo, String payGuid) {
        BusinessMessageDTO payMessageDTO = new BusinessMessageDTO();
        payMessageDTO.setSubject(BusinessMsgTypeEnum.PAD_PREPAYMENT_IS_SUCCESSFUL.getName());
        payMessageDTO.setContent(payGuid);
        payMessageDTO.setMessageType(BusinessMsgTypeEnum.PAD_MESSAGE.getId());
        payMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.PAD_PREPAYMENT_IS_SUCCESSFUL.getId());
        payMessageDTO.setPlatform("2");
        payMessageDTO.setMessageTypeStr(BusinessMsgTypeEnum.PAD_MESSAGE.getId() + ":" + padPayInfo.getDeviceId());
        payMessageDTO.setStoreGuid(padPayInfo.getStoreGuid());
        payMessageDTO.setStoreName(padPayInfo.getStoreName());
        messageClientService.msg(payMessageDTO);
    }

    /**
     * 聚合支付预支付参数准备
     *
     * @param orderGuid  订单guid
     * @param payPowerId 支付功能的id
     * @param padPayInfo pad支付信息
     * @return 聚合支付预支付入参
     */
    private BillAggPayReqDTO getBillAggPayReqDTO(String orderGuid, String payPowerId, PadPayInfoReqDTO padPayInfo) {
        BillAggPayReqDTO aggPayReqDTO = new BillAggPayReqDTO();
        aggPayReqDTO.setOrderGuid(orderGuid);
        aggPayReqDTO.setAmount(padPayInfo.getActuallyPayFee());
        aggPayReqDTO.setLast(Boolean.TRUE);
        aggPayReqDTO.setPayPowerId(payPowerId);
        aggPayReqDTO.setOrderFee(padPayInfo.getOrderTotalFee());
        aggPayReqDTO.setAppendFee(padPayInfo.getAppendFee());
        aggPayReqDTO.setActuallyPayFee(padPayInfo.getActuallyPayFee());
        aggPayReqDTO.setDiscountFee(padPayInfo.getMemberDiscountFee().add(BigDecimalUtil.nonNullValue(padPayInfo.getVolumeFee())));
        if(padPayInfo.getUseIntegral() != null && padPayInfo.getUseIntegral() > 1){
            aggPayReqDTO.setUseIntegral(padPayInfo.getUseIntegral());
        }
        if (Objects.equals(TRUE, padPayInfo.getUseIntegral())) {
            Integer useIntegral = NUMBER_ZERO;
            for (DiscountFeeDetailDTO discount : padPayInfo.getDiscountFeeDetailDTOS()) {
                if (Objects.equals(DiscountTypeEnum.POINTS_DEDUCTION.getCode(), discount.getDiscountType())) {
                    useIntegral = discount.getIntegral();
                }
            }
            aggPayReqDTO.setUseIntegral(useIntegral);
        }
        aggPayReqDTO.setMemberIntegralStore(BooleanEnum.FALSE.getCode());
        aggPayReqDTO.setIntegralDiscountMoney(padPayInfo.getIntegralDeductedAmount());
        aggPayReqDTO.setDiscountFeeDetailDTOS(padPayInfo.getDiscountFeeDetailDTOS());
        aggPayReqDTO.setVersion(padPayInfo.getVersion());
        aggPayReqDTO.setDeviceType(padPayInfo.getDeviceType());
        aggPayReqDTO.setEnterpriseGuid(padPayInfo.getEnterpriseGuid());
        aggPayReqDTO.setStoreGuid(padPayInfo.getStoreGuid());
        aggPayReqDTO.setStoreName(padPayInfo.getStoreName());
        aggPayReqDTO.setUserGuid(Optional.ofNullable(padPayInfo.getMemberInfoGuid()).orElse("0"));
        aggPayReqDTO.setUserName(Optional.ofNullable(padPayInfo.getNickName()).orElse("未登录用户"));
        aggPayReqDTO.setMemberInfoGuid(padPayInfo.getMemberInfoGuid());
        aggPayReqDTO.setMemberInfoCardGuid(padPayInfo.getMemberInfoCardGuid());
        if (!ObjectUtils.isEmpty(padPayInfo.getOperSubjectGuid())) {
            aggPayReqDTO.setOperSubjectGuid(padPayInfo.getOperSubjectGuid());
        }
        aggPayReqDTO.setActiveScan(1);
        return aggPayReqDTO;
    }

    /**
     * 推送消息到安卓
     *
     * @param padPayInfo pad支付信息
     */
    private void pushMesToAndroid(PadPayInfoReqDTO padPayInfo) {
        BusinessMessageDTO inMessageDTO = new BusinessMessageDTO();
        inMessageDTO.setSubject(BusinessMsgTypeEnum.PAD_PAY_MESSAGE.getName());
        inMessageDTO.setContent("PAD扫码成功");
        inMessageDTO.setMessageType(BusinessMsgTypeEnum.PAD_MESSAGE.getId());
        inMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.PAD_PAY_MESSAGE.getId());
        inMessageDTO.setPlatform("2");
        inMessageDTO.setMessageTypeStr(BusinessMsgTypeEnum.PAD_MESSAGE.getId() + ":" + padPayInfo.getDeviceId());
        inMessageDTO.setStoreGuid(padPayInfo.getStoreGuid());
        inMessageDTO.setStoreName(padPayInfo.getStoreName());
        messageClientService.msg(inMessageDTO);
    }
}
