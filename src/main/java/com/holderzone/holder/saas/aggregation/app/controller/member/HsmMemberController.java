package com.holderzone.holder.saas.aggregation.app.controller.member;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.constant.Constant;
import com.holderzone.holder.saas.aggregation.app.service.MemberService;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.account.NewMemberInfoClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.member.HssMemberMerchantClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.member.MemberClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.OrderItemClientService;
import com.holderzone.holder.saas.aggregation.app.utils.BigDecimalUtil;
import com.holderzone.holder.saas.member.merchant.dto.member.RequestQueryMemberConsumption;
import com.holderzone.holder.saas.member.merchant.dto.member.ResponseMemberConsumption;
import com.holderzone.holder.saas.member.terminal.dto.card.ResponseBaseCardInfo;
import com.holderzone.holder.saas.member.terminal.dto.member.response.MemberPortrayalDetailsDTO;
import com.holderzone.holder.saas.member.terminal.dto.order.RequestConfirmPay;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseQuickPay;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.marketing.portrayal.MemberPortrayalDetailsVO;
import com.holderzone.saas.store.dto.member.common.PageDTO;
import com.holderzone.saas.store.dto.member.hsm.HsmAggPayRespDTO;
import com.holderzone.saas.store.dto.member.hsm.HsmRechargeReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineInOrderListRespDTO;
import com.holderzone.saas.store.dto.pay.CardRechargeCashInfoQO;
import com.holderzone.saas.store.dto.pay.SaasPollingDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by zhaohongyang on 2018/8/21.
 */
@Slf4j
@RestController
@RequestMapping("/hsm_member")
@Api(description = "新会员接口")
public class HsmMemberController {

    private final MemberClientService memberClientService;

    private final NewMemberInfoClientService newMemberInfoClientService;

    private final HssMemberMerchantClientService memberMerchantClientService;

    private final MemberService memberService;

    private final OrderItemClientService orderItemClientService;

    @Autowired
    public HsmMemberController(MemberClientService memberClientService,
                               NewMemberInfoClientService newMemberInfoClientService,
                               HssMemberMerchantClientService memberMerchantClientService,
                               MemberService memberService,
                               OrderItemClientService orderItemClientService) {
        this.memberClientService = memberClientService;
        this.newMemberInfoClientService = newMemberInfoClientService;
        this.memberMerchantClientService = memberMerchantClientService;
        this.memberService = memberService;
        this.orderItemClientService = orderItemClientService;
    }

    @ApiOperation(value = "新会员充值接口", notes = "新会员充值接口")
    @PostMapping(value = "/recharge", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "新会员充值接口", action = OperatorType.ADD)
    public Result<HsmAggPayRespDTO> recharge(@RequestBody HsmRechargeReqDTO hsmRechargeReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("新会员充值接口入参：{}", JacksonUtils.writeValueAsString(hsmRechargeReqDTO));
        }
        if (BigDecimalUtil.equelZero(hsmRechargeReqDTO.getRechargeMoney())) {
            hsmRechargeReqDTO.setPayWay(PaymentTypeEnum.PaymentType.CASH_PAY.getId());
            hsmRechargeReqDTO.setPayName(PaymentTypeEnum.PaymentType.CASH_PAY.getName());
        }
        return Result.buildSuccessResult(memberClientService.recharge(hsmRechargeReqDTO));
    }

    @ApiOperation(value = "充值重打印接口", notes = "充值重打印接口")
    @PostMapping(value = "/print_recharge", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<String> printRecharge(@RequestBody CardRechargeCashInfoQO cardRechargeCashInfoQO) {
        log.info("充值重打印接口入参：{}", cardRechargeCashInfoQO);
        Boolean result = memberClientService.printRecharge(cardRechargeCashInfoQO);
        if (Boolean.TRUE.equals(result)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "会员充值撤销接口", notes = "会员充值撤销接口")
    @PostMapping(value = "/revoke_charge", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "新会员充值撤销接口", action = OperatorType.ADD)
    public Result<Integer> revokeCharge(@RequestBody @Validated SaasPollingDTO saasPollingDTO) {
        if (log.isInfoEnabled()) {
            log.info("新会员充值撤销接口入参：{}", JacksonUtils.writeValueAsString(saasPollingDTO));
        }
        return Result.buildSuccessResult(memberClientService.revokeCharge(saasPollingDTO));
    }

    /**
     * 根据手机号查询会员名称
     *
     * @param phoneNum 手机号
     * @return userName
     */
    @ApiOperation("根据手机号查询会员名称")
    @GetMapping(value = "/query_name_by_phone")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,
            description = "根据手机号查询会员名称接口", action = OperatorType.SELECT)
    public Result<String> queryNameByPhone(@RequestParam("phoneNum") String phoneNum) {
        log.info("根据手机号查询会员名称 phoneNum={}", phoneNum);
        return Result.buildSuccessResult(newMemberInfoClientService.queryNameByPhone(phoneNum));
    }

    @GetMapping(value = "/oldBusinessDayHandle")
    void oldBusinessDayHandle() {
        newMemberInfoClientService.oldBusinessDayHandle();
    }


    @ApiOperation(value = "会员快速收款", notes = "会员快速收款")
    @PostMapping(value = "/quick_pay", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.VIP, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "会员快速收款", action = OperatorType.ADD)
    public Result<ResponseQuickPay> quickPay(@RequestBody RequestConfirmPay confirmPay) {
        if (log.isInfoEnabled()) {
            log.info("会员快速收款入参：{}", JacksonUtils.writeValueAsString(confirmPay));
        }
        return Result.buildSuccessResult(newMemberInfoClientService.quickPayOrder(confirmPay));
    }

    @ApiOperation(value = "会员快速收款退款", notes = "会员快速收款退款")
    @PutMapping(value = "/quick_pay/refund/{orderGuid}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.VIP, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "会员快速收款退款", action = OperatorType.UPDATE)
    public Result<ResponseQuickPay> refundQuickPay(@PathVariable String orderGuid) {
        if (log.isInfoEnabled()) {
            log.info("会员快速收款退款入参：{}", orderGuid);
        }
        return Result.buildSuccessResult(newMemberInfoClientService.refundQuickPayOrder(orderGuid));
    }

    @ApiOperation("获取会员卡基本信息")
    @GetMapping("/getCardInfo")
    @EFKOperationLogAop(PLATFORM = Platform.VIP, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "获取会员卡基本信息", action = OperatorType.UPDATE)
    public Result<ResponseBaseCardInfo> getCardInfo() {
        return Result.buildSuccessResult(newMemberInfoClientService.getCardInfo());
    }

    /**
     * 查询会员消费记录
     *
     * @param request 查询参数
     * @return 响应结果
     */
    @PostMapping("/getMemberConsumptionPage")
    public Result<PageDTO<ResponseMemberConsumption>> getMemberConsumptionPage(@RequestBody RequestQueryMemberConsumption request) {
        log.info("查询会员消费记录入参：{}", JacksonUtils.writeValueAsString(request));

        Result<PageDTO<ResponseMemberConsumption>> memberConsumptionResult = memberMerchantClientService.getMemberConsumptionPage(request);
        PageDTO<ResponseMemberConsumption> consumptionPageDTO = Optional.ofNullable(memberConsumptionResult)
                .map(Result::getTData)
                .orElse(new PageDTO<>());

        // 填充orderGuid
        setOrderInfo(request, consumptionPageDTO);

        return Result.buildSuccessResult(consumptionPageDTO);
    }

    /**
     * 填充orderGuid
     *
     * @param request          查询参数
     * @param consumptionPageDTO 响应结果
     */
    private void setOrderInfo(RequestQueryMemberConsumption request, PageDTO<ResponseMemberConsumption> consumptionPageDTO) {
        List<ResponseMemberConsumption> records = consumptionPageDTO.getRecords();
        String storeGuid = request.getStoreGuid();

        if (CollectionUtils.isNotEmpty(records) && StringUtils.isNotBlank(storeGuid)) {
            // 提取非空订单号并去重
            List<String> orderNumberList = records.stream()
                    .filter(Objects::nonNull)
                    .map(ResponseMemberConsumption::getOrderNumber)
                    .filter(StringUtils::isNotEmpty)
                    .distinct()
                    .collect(Collectors.toList());

            if (!orderNumberList.isEmpty()) {
                SingleDataDTO singleDataDTO = new SingleDataDTO();
                singleDataDTO.setStoreGuid(storeGuid);
                singleDataDTO.setDatas(orderNumberList);

                List<OrderDTO> orderDTOs = orderItemClientService.listByOrderNoAndStoreGuid(singleDataDTO);

                Map<String, OrderDTO> orderNoMap = Optional.ofNullable(orderDTOs)
                        .orElse(Collections.emptyList()).stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(OrderDTO::getOrderNo, Function.identity(), (existing, replacement) -> existing));

                records.forEach(consumption -> {
                    OrderDTO orderDTO = orderNoMap.get(consumption.getOrderNumber());
                    if (Objects.nonNull(orderDTO)) {
                        consumption.setOrderGuid(orderDTO.getGuid());
                        consumption.setOrderTradeMode(orderDTO.getTradeMode());
                        consumption.setOrderUpperState(orderDTO.getUpperState());
                        setState(consumption, orderDTO.getState());
                    }
                });
            }
        }
    }

    private void setState(ResponseMemberConsumption responseMemberConsumption, Integer state) {
        responseMemberConsumption.setOrderState(state);
        if (StateEnum.READY.getCode() <= state && state <= StateEnum.FAILURE.getCode()) {
            responseMemberConsumption.setOrderState(1);
        }
        if (StateEnum.SUCCESS.getCode() == state) {
            responseMemberConsumption.setOrderState(2);
        }
        if (StateEnum.REFUNDED.getCode() == state) {
            responseMemberConsumption.setOrderState(3);
        }
        if (StateEnum.CANCEL.getCode() == state) {
            responseMemberConsumption.setOrderState(4);
        }
        if (StateEnum.SUB_SUCCESS.getCode() == state) {
            responseMemberConsumption.setOrderState(StateEnum.SUB_SUCCESS.getCode());
        }
    }

    @ApiOperation("查询会员画像信息")
    @GetMapping("/query_member_portrayal")
    public Result<MemberPortrayalDetailsDTO> queryMemberPortrayal(@RequestParam("memberGuid") String memberGuid) {
        log.info("[查询会员画像信息]入参：memberGuid={}", memberGuid);
        return Result.buildSuccessResult(newMemberInfoClientService.queryMemberPortrayal(memberGuid));
    }

    @ApiOperation("查询会员画像配置")
    @GetMapping("/query_member_portrayal_setting")
    public Result<MemberPortrayalDetailsVO> queryMemberPortrayalSetting(@RequestParam("operSubjectGuid") String operSubjectGuid) {
        log.info("[查询会员画像配置]入参：operSubjectGuid={}", operSubjectGuid);
        return Result.buildSuccessResult(newMemberInfoClientService.queryApplySetting(operSubjectGuid));
    }

}
