package com.holderzone.holder.saas.aggregation.weixin.controller;

import cn.hutool.core.util.ObjectUtil;
import com.holderzone.framework.dynamic.datasource.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.response.ResultEnum;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.entity.dto.AuthRespDTO;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxOpenMessageHandleClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxUserRecordClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.TableClientService;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/auth")
@Api(value = "用户授权接口")
public class AuthController {

    @Autowired
    RedisUtils redisUtils;
    @Autowired
    WxUserRecordClientService wxUserRecordClientService;
    @Autowired
    TableClientService tableClientService;
    @Autowired
    WxOpenMessageHandleClientService wxOpenMessageHandleClientService;


    @ApiOperation(value = "手动获取用户token", notes = "手动获取用户token,token有效期24小时")
    @PostMapping(value = "/getToken")
    public Result<AuthRespDTO> getToken(WxMemberSessionDTO wxMemberSessionDTO){
        log.info("手动获取用户token请求参数{}", JacksonUtils.writeValueAsString(wxMemberSessionDTO));
        if(ObjectUtil.isNull(wxMemberSessionDTO)){
            return Result.buildFailResult(ResultEnum.INVALID_PARAMETER.getResultCode(),"请求参数无效");
        }
        if(EnterpriseIdentifier.getEnterpriseGuid()==null){
            EnterpriseIdentifier.setEnterpriseGuid(wxMemberSessionDTO.getEnterpriseGuid());
        }
        String token = wxOpenMessageHandleClientService.getToken(wxMemberSessionDTO);
        if(token==null){
            return Result.buildOpFailedResult("获取token失败");
        }
        return Result.buildSuccessResult(new AuthRespDTO(token));
    }

}
