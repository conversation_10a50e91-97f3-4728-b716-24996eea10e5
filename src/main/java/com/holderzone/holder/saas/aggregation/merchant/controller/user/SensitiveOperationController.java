package com.holderzone.holder.saas.aggregation.merchant.controller.user;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.manage.BusinessClientService;
import com.holderzone.saas.store.dto.business.brand.BrandConfigDTO;
import com.holderzone.saas.store.dto.business.brand.BrandConfigSaveOrUpdateDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * desc
 *
 * <AUTHOR>
 * @since 2025/7/1
 */
@Slf4j
@RestController
@Api(value = "敏感操作限制")
@RequestMapping("/sensitive_operation_restrictions")
public class SensitiveOperationController {

    private final BusinessClientService businessClientService;

    @Autowired
    public SensitiveOperationController(BusinessClientService businessClientService) {
        this.businessClientService = businessClientService;
    }

    @ApiOperation(value = "保存或更新敏感操作限制", notes = "如果敏感操作限制已存在则更新，不存在则创建")
    @PostMapping("/save_or_update")
    public Result<Long> saveOrUpdate(@RequestBody @Valid BrandConfigSaveOrUpdateDTO saveOrUpdateDTO) {
        log.info("保存或更新敏感操作限制，参数：{}", JacksonUtils.writeValueAsString(saveOrUpdateDTO));
        Long guid = businessClientService.saveOrUpdateBrandConfig(saveOrUpdateDTO);
        return Result.buildSuccessResult(guid);
    }

    @ApiOperation(value = "根据品牌GUID查询敏感操作限制")
    @GetMapping("/query/{brandGuid}")
    public Result<BrandConfigDTO> getByBrandGuid(@PathVariable("brandGuid") String brandGuid) {
        log.info("根据品牌GUID查询敏感操作限制，brandGuid：{}", brandGuid);
        BrandConfigDTO brandConfigDTO = businessClientService.getBrandConfigByBrandGuid(brandGuid);
        return Result.buildSuccessResult(brandConfigDTO);
    }
}
