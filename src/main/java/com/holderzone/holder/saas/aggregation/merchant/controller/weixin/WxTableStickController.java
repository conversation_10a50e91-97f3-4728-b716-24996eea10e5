package com.holderzone.holder.saas.aggregation.merchant.controller.weixin;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.config.DownTickConfig;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin.WxStickCategoryClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin.WxTableStickClientService;
import com.holderzone.saas.store.dto.weixin.WxCategoryDTO;
import com.holderzone.saas.store.dto.weixin.WxTableStickDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickDownloadReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickIsModelDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickModelReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickDownloadRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickModelRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxTableStickController
 * @date 2019/03/06 10:20
 * @description 微信桌贴相关controller
 * @program holder-saas-store
 */
@RestController
@Slf4j
@RequestMapping("/wx_table_stick")
@Api(description = "微信桌贴相关controller")
public class WxTableStickController {
    @Autowired
    WxTableStickClientService wxTableStickClientService;

    @Autowired
    WxStickCategoryClientService wxStickCategoryClientService;



    @Autowired
    private DownTickConfig downTickConfig;


    /***
     * 赚餐桌贴二维码 静态常量
     */
    private static final int QR_CODE_TYPE = 2;

    @PostMapping("/list_stick_model")
    @ApiOperation("查询桌贴模板库列表")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "查询桌贴模板库列表")
    public Result<List<WxStickModelRespDTO>> listWxTableStick(@RequestBody WxStickModelReqDTO wxStickModelReqDTO) {
        return Result.buildSuccessResult(wxTableStickClientService.listStickModel(wxStickModelReqDTO));
    }

    @ApiOperation("查询我的模板列表，结果为当前企业下所有模板")
    @PostMapping("/list_model_and_ticket")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "查询我的模板列表，结果为当前企业下所有模板")
    public Result<List<WxTableStickDTO>> listMyModel(@RequestBody WxStickIsModelDTO wxStickIsModelDTO) {
        log.info("------开始查询我的模板列表------");
        return Result.buildSuccessResult(wxTableStickClientService.listModelAndTicket(wxStickIsModelDTO));
    }

    @ApiOperation(value = "删除我的桌贴")
    @PostMapping("/delete_my_stick")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "删除我的桌贴")
    public Result deleteMyStick(@RequestBody WxStickIsModelDTO wxStickIsModelDTO) {
        log.info("-------开始删除我的桌贴--------");
        wxStickIsModelDTO.setIsModel(0);
        Boolean deleteFlag = wxTableStickClientService.deleteMyStick(wxStickIsModelDTO);
        return deleteFlag ? Result.buildEmptySuccess() : Result.buildOpFailedResult("桌贴删除失败，请稍后重试");
    }

    @ApiOperation(value = "删除我的模板")
    @PostMapping("/delete_my_model")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "删除我的模板")
    public Result deleteMyModel(@RequestBody WxStickIsModelDTO wxStickIsModelDTO) {
        log.info("-------开始删除我的模板--------");
        wxStickIsModelDTO.setIsModel(1);
        Boolean deleteFlag = wxTableStickClientService.deleteMyStick(wxStickIsModelDTO);
        return deleteFlag ? Result.buildEmptySuccess() : Result.buildOpFailedResult("模板删除失败，请稍后重试");
    }

    @ApiOperation("修改我的桌贴")
    @PostMapping("/update_my_stick")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "修改我的桌贴")
    public Result<String> updateMyStick(@RequestBody WxTableStickDTO wxTableStickDTO) {
        Boolean createFlag = wxTableStickClientService.saveOrUpdateStick(wxTableStickDTO);
        return createFlag ? Result.buildEmptySuccess() : Result.buildOpFailedResult("微信桌贴创建失败");
    }

    @ApiOperation("创建我的桌贴")
    @PostMapping("/save_my_stick")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "创建我的桌贴")
    public Result<String> saveMyStick(@RequestBody WxTableStickDTO wxTableStickDTO) {
        Boolean createFlag = wxTableStickClientService.saveOrUpdateStick(wxTableStickDTO);
        return createFlag ? Result.buildEmptySuccess() : Result.buildOpFailedResult("微信桌贴创建失败");
    }

    @ApiOperation("通过guid查询桌贴详情，我的桌贴模板也是通过该接口调用")
    @PostMapping("/find_by_guid")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "通过guid查询桌贴详情，我的桌贴模板也是通过该接口调用")
    public Result<WxTableStickDTO> findByGuid(@RequestBody WxStickIsModelDTO wxStickIsModelDTO) {
        WxTableStickDTO wxTableStickDTO = wxTableStickClientService.findByGuid(wxStickIsModelDTO.getGuid());
        return Result.buildSuccessResult(wxTableStickDTO);
    }

    @ApiOperation("/获取当前桌贴分类")
    @PostMapping("/list_stick_category")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "获取当前桌贴分类")
    public Result<List<WxCategoryDTO>> listStickCategory() {
        return Result.buildSuccessResult(wxStickCategoryClientService.listCategory());
    }

    @ApiOperation("下载桌贴")
    @PostMapping("/download_stick_zip")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "下载桌贴")
    public void downloadStickZip(@RequestBody WxStickDownloadReqDTO wxStickDownloadReqDTO, HttpServletResponse response) {
        log.info("已收到桌贴下载请求参数：wxStickDownloadReqDTO：{}", wxStickDownloadReqDTO);
        try {
            WxStickDownloadRespDTO wxStickDownloadRespDTO = wxTableStickClientService.downloadStickZip(wxStickDownloadReqDTO.getDownloadKey());
            log.info("查询到下载信息，wxStickDownloadRespDTO：{}", wxStickDownloadRespDTO);
            response.reset();
            if (!ObjectUtils.isEmpty(wxStickDownloadRespDTO)) {
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(wxStickDownloadRespDTO.getFileName(), "UTF-8"));
                response.setHeader("content-type", ContentType.APPLICATION_OCTET_STREAM.getMimeType());
                response.setHeader("isSuccess", "1");
                IOUtils.write(wxStickDownloadRespDTO.getFileByte(), response.getOutputStream());
                log.info("桌贴下载生成成功：fileName：{}", wxStickDownloadRespDTO.getFileName());
            } else {
                response.setHeader("isSuccess", "0");
            }
        } catch (IOException e) {
            log.error("桌贴下载时出现异常，e：{}", e.getMessage());
            throw new RuntimeException("数据传输错误，请稍后重试");
        }
    }

    @ApiOperation("创建桌贴")
    @PostMapping("/create_stick_zip")
    public Result<String> createStickZip(@RequestBody WxStickDownloadReqDTO wxStickDownloadReqDTO) {
        log.info("商户聚合层，已收到生成桌贴请求参数，wxStickDownloadReqDTO：{}", wxStickDownloadReqDTO);
        /*if(wxStickDownloadReqDTO.getQrCodeType()== QR_CODE_TYPE){
            String response = null;
            try{
                response = doPost(downTickConfig.getDownTick(), wxStickDownloadReqDTO);
                if(response==null){
                    return Result.buildOpFailedResult("赚餐二维码下载出错");
                }
                net.sf.json.JSONObject jsonObject = JacksonUtils.toJSONObject(response);
                Object code = jsonObject.get("code");
                if(code!=null&&code.toString().equals("0")){
                    //success
                    return Result.buildSuccessResult(jsonObject.getString("content"));
                }
                Object message = jsonObject.get("message");
                if(message!=null){
                    return Result.buildOpFailedResult(message.toString());
                }
                log.error("下载赚餐二维码返回{}",response);
                return Result.buildSuccessResult("赚餐二维码下载出错");
            }catch (Exception e){
                log.error("赚餐二维码下载出错,response:{}",response,e);
                throw new BusinessException(e.getMessage());
            }
        }*/
        return Result.buildSuccessResult(wxTableStickClientService.createStickZip(wxStickDownloadReqDTO));
    }


}
