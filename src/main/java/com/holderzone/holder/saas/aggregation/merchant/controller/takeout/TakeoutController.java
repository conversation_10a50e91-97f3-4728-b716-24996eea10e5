package com.holderzone.holder.saas.aggregation.merchant.controller.takeout;

import cn.hutool.core.collection.CollectionUtil;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.config.RedisLock;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.business.GroupBuyClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.organization.OrganizationService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.takeout.TakeoutConsumerService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.takeout.TakeoutProducerService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.user.UserFeignService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.*;
import com.holderzone.saas.store.dto.takeaway.response.*;
import com.holderzone.saas.store.dto.trade.ItemPageQuery;
import com.holderzone.saas.store.dto.user.UserSpinnerDTO;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * todo 修改url，并告知前端修改
 *
 * <AUTHOR>
 * @version 1.0
 * @className TakeawayController
 * @date 2018/09/20 15:35
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Slf4j
@RestController
@RequestMapping("/takeout")
@Api(tags = "外卖服务接口")
public class TakeoutController {

    private final TakeoutProducerService takeoutProducerService;

    private final TakeoutConsumerService takeoutConsumerService;

    private final GroupBuyClientService groupBuyClientService;

    private final OrganizationService organizationService;

    private final RedisLock redisLock;

    @Resource
    private UserFeignService userFeignService;

    @Value("${small.ENTERPRISE_FOR_HESHI}")
    private String enterpriseGuidForHeShi;

    @Autowired
    public TakeoutController(TakeoutProducerService takeoutProducerService, TakeoutConsumerService takeoutConsumerService,
                             GroupBuyClientService groupBuyClientService, OrganizationService organizationService, RedisLock redisLock) {
        this.takeoutProducerService = takeoutProducerService;
        this.takeoutConsumerService = takeoutConsumerService;
        this.groupBuyClientService = groupBuyClientService;
        this.organizationService = organizationService;
        this.redisLock = redisLock;
    }

    @ApiOperation(value = "门店授权页面url", notes = "门店授权页面url")
    @PostMapping(value = "/shop_binding_url", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "门店授权页面url")
    public Result<TakeoutShopBindRespDTO> shopBindingURL(@RequestBody TakeoutShopBindReqDTO takeoutShopBindReqDTO) {
        log.info("门店授权页面url入参：{}", JacksonUtils.writeValueAsString(takeoutShopBindReqDTO));
        return Result.buildSuccessResult(takeoutProducerService.shopBindingUrl(takeoutShopBindReqDTO));
    }

    @ApiOperation(value = "自营外卖平台绑定", notes = " 自营外卖平台绑定")
    @PostMapping(value = "/do_shop_binding", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "门店授权页面url")
    public Result<TakeoutOwnRespDTO> doShopBinding(@RequestBody TakeoutShopOwnBindReqDTO takeoutShopOwnBindReqDTO) {
        log.info("【自营外卖平台】绑定入参：{}", JacksonUtils.writeValueAsString(takeoutShopOwnBindReqDTO));
        return Result.buildSuccessResult(takeoutProducerService.doShopBinding(takeoutShopOwnBindReqDTO));
    }

    @ApiOperation(value = "自营外卖平台解绑", notes = "自营外卖平台解绑")
    @PostMapping(value = "/do_shop_unbinding", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "门店授权页面url")
    public Result<TakeoutOwnRespDTO> doTakeOutUnBinding(@RequestBody TakeoutShopOwnUnBindReqDTO takeoutShopOwnUnBindReqDTO) {
        log.info("【自营外卖平台】解绑入参：{}", JacksonUtils.writeValueAsString(takeoutShopOwnUnBindReqDTO));
        return Result.buildSuccessResult(takeoutProducerService.doShopUnBinding(takeoutShopOwnUnBindReqDTO));
    }

    @ApiOperation(value = "赚餐外卖平台门店绑定", notes = " 赚餐外卖平台门店绑定")
    @PostMapping(value = "/do_shop_binding_tcd", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "门店授权页面url")
    public Result<Boolean> doShopBindingTcd(@RequestBody TCDBindReqDTO tcdBindReqDTO) {
        log.info("【赚餐外卖平台】门店绑定入参：{}", JacksonUtils.writeValueAsString(tcdBindReqDTO));
        TcdCommonRespDTO tcdCommonRespDTO = takeoutProducerService.doShopBindingTcd(tcdBindReqDTO);
        log.info("【赚餐外卖平台】门店绑定返回：{}", JacksonUtils.writeValueAsString(tcdCommonRespDTO));
        if (tcdCommonRespDTO.getCode() == 200) {
            return Result.buildSuccessResult(Boolean.TRUE);
        } else {
            return Result.buildFailResult(tcdCommonRespDTO.getCode(), tcdCommonRespDTO.getMessage());
        }
    }

    @ApiOperation(value = "赚餐外卖平台门店解绑", notes = "赚餐外卖平台门店解绑")
    @PostMapping(value = "/do_shop_unbinding_tcd", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "门店授权页面url")
    public Result<Boolean> doUnBindingTcd(@RequestBody TCDBindReqDTO tcdBindReqDTO) {
        log.info("【赚餐外卖平台】门店解绑入参：{}", JacksonUtils.writeValueAsString(tcdBindReqDTO));
        TcdCommonRespDTO tcdCommonRespDTO = takeoutProducerService.doShopUnBindingTcd(tcdBindReqDTO);
        log.info("【赚餐外卖平台】门店解绑返回：{}", JacksonUtils.writeValueAsString(tcdCommonRespDTO));
        if (tcdCommonRespDTO.getCode() == 200) {
            return Result.buildSuccessResult(Boolean.TRUE);
        } else {
            return Result.buildFailResult(tcdCommonRespDTO.getCode(), tcdCommonRespDTO.getMessage());
        }
    }

    @ApiOperation(value = "抖音外卖平台门店绑定", notes = " 抖音外卖平台门店绑定")
    @PostMapping(value = "/do_shop_binding_tiktok", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "抖音外卖平台门店绑定")
    public Result<Void> doShopBindingTikTok(@RequestBody StoreAuthByStoreReqDTO storeAuthByStoreReqDTO) {
        log.info("抖音外卖平台门店绑定入参：{}", JacksonUtils.writeValueAsString(storeAuthByStoreReqDTO));
        takeoutProducerService.doShopBindingTikTok(storeAuthByStoreReqDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "抖音外卖平台门店解绑", notes = "抖音外卖平台门店解绑")
    @PostMapping(value = "/do_shop_unbinding_tiktok", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "抖音外卖平台门店解绑")
    public Result<Void> doTakeOutUnBindingTikTok(@RequestBody StoreAuthByStoreReqDTO storeAuthByStoreReqDTO) {
        log.info("抖音外卖平台门店解绑入参：{}", JacksonUtils.writeValueAsString(storeAuthByStoreReqDTO));
        // 防重复提交
        takeoutProducerService.doShopUnBindingTikTok(storeAuthByStoreReqDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "绑定库存门店", notes = "绑定库存门店")
    @PostMapping(value = "/bind_stock_store", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<StockStoreBindResqDTO> bindStockStore(@RequestBody @Validated StockStoreBindReqDTO req) {
        if (log.isInfoEnabled()) {
            log.info("绑定库存门店入参：{}", JacksonUtils.writeValueAsString(req));
        }
        return Result.buildSuccessResult(takeoutProducerService.bindStockStore(req));
    }

    @ApiOperation(value = "查询绑定库存门店", notes = "查询绑定库存门店")
    @GetMapping(value = "/get_bind_stock_store", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<StockStoreBindResqDTO> getBindStockStore(@RequestParam("storeGuid") String storeGuid) {
        if (log.isInfoEnabled()) {
            log.info("查询绑定库存门店：{}", storeGuid);
        }
        return Result.buildSuccessResult(takeoutProducerService.getBindStockStore(storeGuid));
    }

    @ApiOperation(value = "团购门店授权页面url", notes = "门店授权页面url")
    @PostMapping(value = "/group_buy_binding_url", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "团购门店授权页面url")
    public Result<TakeoutShopBindRespDTO> groupBuyBindingUrl(@RequestBody GroupBuyShopBindReqDTO groupBuyShopBindReqDTO) {
        log.info("门店授权页面url入参：{}", JacksonUtils.writeValueAsString(groupBuyShopBindReqDTO));
        return Result.buildSuccessResult(takeoutProducerService.groupBuyBindingUrl(groupBuyShopBindReqDTO));
    }

    @Deprecated
    @ApiOperation(value = "门店菜品绑定页面url", notes = "门店菜品绑定页面url")
    @PostMapping(value = "/item_binding_url", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "门店菜品绑定页面url")
    public Result<TakeoutItemBindRespDTO> itemBindingUrl(@RequestBody TakeoutItemBindReqDTO takeoutItemBindReqDTO) {
        log.info("门店菜品绑定页面url入参：{}", JacksonUtils.writeValueAsString(takeoutItemBindReqDTO));
        return Result.buildSuccessResult(takeoutProducerService.itemBindingUrl(takeoutItemBindReqDTO));
    }

    @ApiOperation(value = "查询门店授权列表（根据平台类型）", notes = "查询门店授权列表")
    @PostMapping(value = "/query_store_auth", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "查询门店授权列表（根据平台类型）")
    public Result<List<StoreAuthDTO>> queryStoreAuth(
            @RequestBody StoreAuthByTypeReqDTO storeAuthByTypeReqDTO) {
        log.info("查询门店授权列表入参：{}", JacksonUtils.writeValueAsString(storeAuthByTypeReqDTO));
        return Result.buildSuccessResult(takeoutConsumerService.queryAuthByType(storeAuthByTypeReqDTO));
    }

    @ApiOperation(value = "查询门店授权列表（根据门店）", notes = "查询门店授权列表")
    @PostMapping(value = "/query_takeout_auth_by_store", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "查询门店授权列表（根据门店）")
    public Result<List<StoreAuthDTO>> queryTakeoutAuthByStore(@RequestBody StoreAuthByStoreReqDTO storeAuthByStoreReqDTO) {
        log.info("查询门店授权列表入参：{}", JacksonUtils.writeValueAsString(storeAuthByStoreReqDTO));
        List<StoreAuthDTO> list = takeoutConsumerService.queryTakeoutAuthByStore(storeAuthByStoreReqDTO);
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(e -> e.setPlatformName(LocaleUtil.getMessage(OrderType.TakeoutSubType.ofType(e.getTakeoutType()).name())));
        }
        return Result.buildSuccessResult(list);
    }

    @ApiOperation(value = "查询门店授权列表（根据门店）", notes = "查询门店授权列表")
    @PostMapping(value = "/query_tuangou_auth_by_store", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "查询门店授权列表（根据门店）")
    public Result<List<StoreAuthDTO>> queryTuanGouAuthByStore(
            @RequestBody StoreAuthByStoreReqDTO storeAuthByStoreReqDTO) {
        log.info("查询门店授权列表入参：{}", JacksonUtils.writeValueAsString(storeAuthByStoreReqDTO));
        List<StoreAuthDTO> groupList = groupBuyClientService.query(storeAuthByStoreReqDTO.getStoreGuid());
        if (CollectionUtil.isNotEmpty(groupList)) {
            groupList.forEach(g -> g.setPlatformName(LocaleUtil.getMessage(g.getTakeoutType() == 1 ? GroupBuyTypeEnum.MEI_TUAN.name() : GroupBuyTypeEnum.groupBuyType(g.getTakeoutType()).name())));
        }
        return Result.buildSuccessResult(groupList);
    }

    /**
     * 暂未使用
     *
     * @param queryDishReqDTO
     * @return
     */
    @Deprecated
    @ApiOperation(value = "菜品映射（查询erp菜品）", notes = "菜品映射（查询erp菜品）")
    @PostMapping(value = "/query_dish", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "菜品映射（查询erp菜品）")
    public String queryDish(@RequestBody QueryDishReqDTO queryDishReqDTO) {
        log.info("菜品映射（查询erp菜品）入参：{}", JacksonUtils.writeValueAsString(queryDishReqDTO));
        return takeoutConsumerService.queryDish(queryDishReqDTO);
    }

    @PostMapping(value = "/update_delivery")
    @ApiOperation(value = "修改门店配送方式", notes = "修改门店配送方式")
    public Result<String> deliveryChange(@RequestBody StoreAuthDTO storeAuthDTO) {
        log.info("修改门店配送方式：入参{}", JacksonUtils.writeValueAsString(storeAuthDTO));
        Boolean result = takeoutConsumerService.updateDelivery(storeAuthDTO);
        if (result) {
            return Result.buildSuccessResult(LocaleUtil.getMessage(Constants.RESULT_MODIFICATION_SUCCESSFUL));
        } else {
            return Result.buildFailResult(-1, LocaleUtil.getMessage(Constants.RESULT_MODIFICATION_FAILED));
        }
    }


    @PostMapping(value = "/callback/delivery_change")
    @ApiOperation(value = "一城飞客订单状态发生变更", notes = "一城飞客订单状态发生变更")
    public Result<String> deliveryChange(@RequestBody TakeoutDeliveryChange takeoutDeliveryChange) {
        String enterpriseGuid = enterpriseGuidForHeShi;
        log.info("一城飞客订单状态发生变更({}),enterpriseGuid={},入参={}", takeoutDeliveryChange.getOrderId(),
                enterpriseGuidForHeShi, JacksonUtils.writeValueAsString(takeoutDeliveryChange));
        UserContextUtils.put(JacksonUtils.writeValueAsString(UserInfoDTO.builder().enterpriseGuid(enterpriseGuid)
                .build()));
        takeoutConsumerService.deliveryChange(takeoutDeliveryChange);
        return Result.buildSuccessResult("变更成功");
    }

    @PostMapping(value = "/callback/delivery_location")
    @ApiOperation(value = "一城飞客骑手位置信息", notes = "一城飞客骑手位置信息")
    public Result<String> deliveryLocation(@RequestBody TakeoutDeliveryChange takeoutDeliveryChange) {
        String enterpriseGuid = enterpriseGuidForHeShi;
        log.info("一城飞客骑手定位({}),enterpriseGuid={},入参={}", takeoutDeliveryChange.getOrderId(),
                enterpriseGuidForHeShi, JacksonUtils.writeValueAsString(takeoutDeliveryChange));
        UserContextUtils.put(JacksonUtils.writeValueAsString(UserInfoDTO.builder().enterpriseGuid(enterpriseGuid)
                .build()));
        takeoutConsumerService.deliveryLocation(takeoutDeliveryChange);
        return Result.buildSuccessResult("推送成功");
    }


    @ApiOperation(value = "分页查询订单商品明细", notes = "分页查询订单商品明细")
    @PostMapping(value = "/page_order_item_problem")
    public Result<Page<TakeoutOrderItemProblemDTO>> pageOrderItemProblem(@RequestBody ItemPageQuery query) {
        Page<TakeoutOrderItemProblemDTO> takeoutOrderItemProblemDTOPage = takeoutConsumerService.pageOrderItemProblem(query);
        return Result.buildSuccessResult(takeoutOrderItemProblemDTOPage);
    }

    @ApiOperation(value = "批量修改订单商品明细", notes = "批量修改订单商品明细")
    @PostMapping(value = "/update_batch_item_problem")
    public Result<String> updateBatchItem(@RequestBody List<TakeoutOrderItemProblemDTO> itemDOList) {
        takeoutConsumerService.updateBatchItem(itemDOList);
        return Result.buildSuccessResult(LocaleUtil.getMessage(Constants.RESULT_MODIFICATION_SUCCESSFUL));
    }

    /**
     * 外卖异常数据列表
     *
     * @param reqDTO 外卖异常数据列表请求
     * @return 外卖异常数据列表
     */
    @ApiOperation(value = "外卖异常数据列表")
    @PostMapping("/abnormal_data_page")
    public Result<Page<TakeoutItemAbnormalDataRespDTO>> pageAbnormalData(@RequestBody TakeoutItemAbnormalDataReqDTO reqDTO) {
        log.info("外卖异常数据列表,入参,reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        return Result.buildSuccessResult(takeoutConsumerService.page(reqDTO));
    }

    /**
     * 数据修复列表
     *
     * @param reqDTO 数据修复列表请求
     * @return 数据修复列表
     */
    @ApiOperation(value = "数据修复列表")
    @PostMapping("/data_fix_list")
    public Result<List<TakeoutItemDataFixRespDTO>> listDataFix(@RequestBody TakeoutItemDataFixReqDTO reqDTO) {
        log.info("数据修复列表,入参,reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        return Result.buildSuccessResult(takeoutConsumerService.listDataFix2(reqDTO));
    }

    /**
     * 外卖商品迁移异常数据
     */
    @ApiOperation(value = "外卖商品迁移异常数据")
    @PostMapping("/move")
    public void move(@RequestBody MoveDTO moveDTO) {
        log.info("外卖商品迁移异常数据 moveDTO={}", JacksonUtils.writeValueAsString(moveDTO));
        takeoutConsumerService.move(moveDTO);
    }

    @ApiOperation(value = "第三方业务授权地址")
    @PostMapping("/mt_auth_url")
    public Result<String> mtAuthUrl(@RequestBody @Validated MtAuthBindUrlDTO authBindUrl) {
        log.info("第三方业务授权地址:{}", JacksonUtils.writeValueAsString(authBindUrl));
        return Result.buildSuccessResult(takeoutProducerService.getMtAuthUrl(authBindUrl));
    }

    @ApiOperation(value = "查询业务授权绑定信息", notes = "第三方业务授权地址")
    @GetMapping(value = "/get_mt_bind_auth/{data}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<MtAuthBindUrlDTO>> getMtBindAuth(@PathVariable("data") String data) {
        return Result.buildSuccessResult(takeoutProducerService.getMtBindAuth(data));
    }

    @GetMapping("/available_stores/{brandGuid}/{type}")
    @ApiOperation(value = "查询已授权的门店列表", notes = "查询已授权的门店列表")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "查询已授权的门店列表")
    public Result<List<StoreDTO>> getAvailableStores(@PathVariable("brandGuid") String brandGuid, @PathVariable("type") Integer type) {
        if (log.isInfoEnabled()) {
            log.info("查询已授权的门店列表：入参:brandGuid:{}, type:{}", brandGuid, type);
        }
        // 查询当前账号权限范围内的门店列表
        SingleDataDTO singleDataDTO = new SingleDataDTO();
        singleDataDTO.setData(brandGuid);
        UserSpinnerDTO userSpinnerDTO = userFeignService.queryStoreSpinnerByBrandGuid(singleDataDTO);
        if (Objects.isNull(userSpinnerDTO) || CollectionUtils.isEmpty(userSpinnerDTO.getArrayOfStoreDTO())) {
            return Result.buildSuccessResult(Collections.emptyList());
        }
        List<StoreDTO> arrayOfStoreList = userSpinnerDTO.getArrayOfStoreDTO();
        List<String> storeGuids = arrayOfStoreList.stream().map(StoreDTO::getGuid).collect(Collectors.toList());
        // 查询授权的门店列表
        UnItemQueryReq unItemQueryReq = new UnItemQueryReq();
        unItemQueryReq.setStoreGuids(storeGuids);
        unItemQueryReq.setTakeoutType(type);
        List<String> authStoreGuids = takeoutProducerService.getAuthStores(unItemQueryReq);
        log.info("已授权的门店guids:{}", JacksonUtils.writeValueAsString(authStoreGuids));
        // 返回已授权的门店列表
        arrayOfStoreList.removeIf(e -> !authStoreGuids.contains(e.getGuid()));
        return Result.buildSuccessResult(arrayOfStoreList);
    }

    @PostMapping(value = "/notify_alipay_auth")
    @ApiOperation(value = "通知支付宝授权变更", notes = "通知支付宝授权变更")
    public void notifyAliPayAuth(@RequestBody @Validated NotifyAliPayAuthReqDTO notifyDTO) {
        log.info("[通知支付宝授权变更],入参{}", JacksonUtils.writeValueAsString(notifyDTO));
        takeoutProducerService.notifyAliPayAuth(notifyDTO);
    }

    /**
     * 京东外卖平台门店绑定
     * @param storeAuthByStoreReqDTO 请求参数
     */
    @PostMapping(value = "/do_shop_binding_jd", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Void> doShopBindingJd(@RequestBody StoreAuthByStoreReqDTO storeAuthByStoreReqDTO) {
        log.info("京东外卖平台门店绑定入参：{}", JacksonUtils.writeValueAsString(storeAuthByStoreReqDTO));
        //需先根据门店查询到对应品牌是否授权
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeAuthByStoreReqDTO.getStoreGuid());
        storeAuthByStoreReqDTO.setBrandGuid(brandDTO.getGuid());
        takeoutProducerService.doShopBindingJd(storeAuthByStoreReqDTO);
        return Result.buildEmptySuccess();
    }

    /**
     * 京东外卖平台门店绑定
     * @param storeAuthByStoreReqDTO 请求参数
     */
    @PostMapping(value = "/do_shop_unbinding_jd", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Void> doTakeOutUnBindingJd(@RequestBody StoreAuthByStoreReqDTO storeAuthByStoreReqDTO) {
        log.info("京东外卖平台门店解绑入参：{}", JacksonUtils.writeValueAsString(storeAuthByStoreReqDTO));
        //需先根据门店查询到对应品牌是否授权
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeAuthByStoreReqDTO.getStoreGuid());
        storeAuthByStoreReqDTO.setBrandGuid(brandDTO.getGuid());
        takeoutProducerService.doShopUnBindingJd(storeAuthByStoreReqDTO);
        return Result.buildEmptySuccess();
    }

}
