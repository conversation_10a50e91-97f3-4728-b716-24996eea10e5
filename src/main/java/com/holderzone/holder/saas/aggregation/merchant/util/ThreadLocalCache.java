package com.holderzone.holder.saas.aggregation.merchant.util;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import org.springframework.lang.Nullable;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ThreadLocalCache
 * @date 2018/09/13 16:17
 * @description 线程级别的缓存userInfo，用于动态数据源切库，获取当前用户信息，适当的时候，清除，防止内存泄漏
 * @program holder-saas-aggregation-merchant-member
 */
public final class ThreadLocalCache {

    private static final ThreadLocal<String> THREAD_LOCAL = new ThreadLocal<>();

    public static void putJsonStr(String str) {
        THREAD_LOCAL.set(str);
    }

    @Nullable
    public static String getJsonStr() {
        return THREAD_LOCAL.get();
    }

    @Nullable
    public static UserInfoDTO get() {
        return JacksonUtils.toObject(UserInfoDTO.class, getJsonStr());
    }

    @Nullable
    public static String getEnterpriseGuid() {
        return Optional.ofNullable(get()).map(UserInfoDTO::getEnterpriseGuid).orElse(null);
    }

    @Nullable
    public static String getEnterpriseName() {
        return Optional.ofNullable(get()).map(UserInfoDTO::getEnterpriseName).orElse(null);
    }

    @Nullable
    public static String getEnterpriseNo() {
        return Optional.ofNullable(get()).map(UserInfoDTO::getEnterpriseNo).orElse(null);
    }

    @Nullable
    public static String getUserGuid() {
        return Optional.ofNullable(get()).map(UserInfoDTO::getUserGuid).orElse(null);
    }

    @Nullable
    public static String getUserName() {
        return Optional.ofNullable(get()).map(UserInfoDTO::getUserName).orElse(null);
    }

    @Nullable
    public static String getAccount() {
        return Optional.ofNullable(get()).map(UserInfoDTO::getAccount).orElse(null);
    }

    @Nullable
    public static String getStoreGuid() {
        return Optional.ofNullable(get()).map(UserInfoDTO::getStoreGuid).orElse(null);
    }

    @Nullable
    public static String getStoreName() {
        return Optional.ofNullable(get()).map(UserInfoDTO::getStoreName).orElse(null);
    }

    @Nullable
    public static String getStoreNo() {
        return Optional.ofNullable(get()).map(UserInfoDTO::getStoreNo).orElse(null);
    }

    @Nullable
    public static String getTel() {
        return Optional.ofNullable(get()).map(UserInfoDTO::getTel).orElse(null);
    }

    public static void remove() {
        THREAD_LOCAL.remove();
    }
}
