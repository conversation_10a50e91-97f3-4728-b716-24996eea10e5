package com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.weixin.deal.*;
import com.holderzone.saas.store.dto.weixin.member.WxMemberInfoVolumeDetailsRespDTO;
import com.holderzone.saas.store.dto.weixin.member.WxVolumeDetailReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxQueryThirdPartUserInfoReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxReserveConfigDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxThirdPartUserInfoReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxThirdPartUserInfoRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

@Service
@FeignClient(name = "holder-saas-store-weixin"
        //,url = "http://192.168.102.42:8920"
        , fallbackFactory = WxClientService.WxClientServiceFallBack.class)
public interface WxClientService {

    @ApiModelProperty(value = "优惠券详情")
    @PostMapping(value = "/wx_member_center_volume/volume_details")
    WxMemberInfoVolumeDetailsRespDTO volumeCodeDetails(@RequestBody WxVolumeDetailReqDTO wxVolumeDetailReqDTO);

    @ApiOperation("下单")
    @PostMapping(value = "/deal/order_item/submit")
    OrderSubmitRespDTO submit(@RequestBody(required = false) OrderSubmitReqDTO orderSubmitReqDTO);

    @ApiOperation("查询门店配置重构")
    @GetMapping("/wx_store_order_config/store_config")
    WxOrderConfigDTO getStoreConfig(@RequestParam("storeGuid") String storeGuid);

    @ApiOperation("用户门店订单：重构")
    @GetMapping("/wx_store_order_record/store_list")
    List<WxStoreUserOrderItemDTO> userStoreList();

    @ApiOperation("查询我的或者桌台没有完结的订单")
    @GetMapping("/wx_store_order_record/un_finish_order_list")
    List<WxStoreUserOrderItemDTO> getUnFinishStoreUserOrderList();

    @ApiOperation("获取一体机订单id")
    @PostMapping("/wx_store_order_record/order_guid")
    String getMerchantOrderGuid(@RequestParam(value = "orderGuid") String orderRecordGuid);

    @ApiOperation("微信、支付宝小程序支付/微信公众号支付")
    @PostMapping("/wx_store_pay/wechat_pay")
    WxPayRespDTO aggPay(@RequestBody WeChatH5PayReqDTO weChatH5PayReqDTO);

    @ApiOperation("门店用户订单分页")
    @PostMapping(value = "/wx_store_order_record/store_page")
    IPage<WxStoreUserOrderItemDTO> userStorePage(@RequestParam("pageNo") Integer pageNo, @RequestParam("pageSize") Integer pageSize);

    @ApiModelProperty(value = "是否启用输入人数键盘")
    @GetMapping(value = "/deal/order_item/keyboard")
    Boolean enableKeyboard();

    @ApiOperation("正餐订单初始化")
    @GetMapping(value = "/wx_store_order_record/dine_initial")
    String initialDineOrder();

    @ApiOperation("会员支付回调更新订单")
    @PostMapping(value = "/wx_store_order_record/member_back")
    void payBackOrder(@RequestBody PayBackOrderRecordDTO payBackOrderRecordDTO);

    @ApiOperation("非会员推送消息")
    @GetMapping(value = "/wx_store_order_record/sendUnMemberMessage")
    void sendUnMemberMessage(UnMemberMessageDTO unMemberMessageDTO);

    @PostMapping("/wx_reserve/get_config")
    WxReserveConfigDTO getConfig(@RequestBody SingleDataDTO singleDataDTO);


    @PostMapping("/wx_reserve/store")
    WxReserveConfigDTO obtainByStore(@RequestParam("storeGuid") String storeGuid);

    @PostMapping("/wx_reserve/store_list")
    List<WxReserveConfigDTO> storeList(@RequestParam("storeGuidList") List<String> storeGuidList);


    @PostMapping("/wx_third_part/save_or_update_third_part_user_info")
    @ApiOperation(value = "新增或更新第三方会员信息")
    Boolean saveOrUpdateThirdPartUserInfo(@RequestBody WxThirdPartUserInfoReqDTO wxThirdPartUserInfoReqDTO);


    @PostMapping("/wx_third_part/check_third_part_user_info")
    @ApiOperation(value = "校验第三方会员信息")
    WxThirdPartUserInfoRespDTO checkThirdPartUserInfo(@RequestBody WxQueryThirdPartUserInfoReqDTO wxQueryThirdPartUserInfoReqDTO);

    @PostMapping("/wx_third_part/query_third_part_user_info")
    @ApiOperation(value = "查询第三方会员信息")
    WxThirdPartUserInfoRespDTO queryThirdPartUserInfo(@RequestBody WxQueryThirdPartUserInfoReqDTO wxQueryThirdPartUserInfoReqDTO);

    @GetMapping("/deal/order_item/get_item_list_by_order_guid")
    List<DineInItemDTO> getItemListByOrderGuid(@RequestParam("orderGuid") String orderGuid);

    @GetMapping("/deal/tcd/order/async/{orderGuid}")
    void asyncTcdOrder(@PathVariable("orderGuid") String orderGuid);

    /**
     * 获取门店配置详细信息
     * 包含微信点餐及PAD端用户点餐买单设置
     *
     * @param wxStoreReqDTO
     * @return
     */
    @PostMapping("/wx_store_order_config/get_detail_config")
    @ApiOperation(value = "获取微信点餐门店配置详细信息")
    WxOrderConfigDTO getDetailConfig(@RequestBody WxStoreReqDTO wxStoreReqDTO);

    @ApiOperation("正餐下单")
    @PostMapping(value = "/deal/order_item/submitDine")
    OrderSubmitRespDTO submitDine(@RequestBody(required = false) OrderSubmitReqDTO orderSubmitReqDTO);

    @ApiOperation("快餐下单")
    @PostMapping(value = "/deal/order_item/submitFast")
    OrderSubmitRespDTO submitFast(@RequestBody(required = false) OrderSubmitReqDTO orderSubmitReqDTO);

    @Slf4j
    @Component
    class WxClientServiceFallBack implements FallbackFactory<WxClientService> {
        @Override
        public WxClientService create(Throwable throwable) {
            return new WxClientService() {

                @Override
                public WxOrderConfigDTO getStoreConfig(String storeGuid) {
                    log.error("查询门店配置失败：{}", storeGuid);
                    return null;
                }

                @Override
                public List<WxStoreUserOrderItemDTO> userStoreList() {
                    log.error("查询用户订单失败:{}", WeixinUserThreadLocal.get());
                    return Collections.emptyList();
                }

                @Override
                public List<WxStoreUserOrderItemDTO> getUnFinishStoreUserOrderList() {
                    log.error("查询我的或者桌台没有完结的订单失败:{}", WeixinUserThreadLocal.get());
                    return Collections.emptyList();
                }

                @Override
                public String getMerchantOrderGuid(String orderRecordGuid) {
                    log.error("获取商户guid失败:{}", orderRecordGuid, throwable);
                    return null;
                }

                @Override
                public WxPayRespDTO aggPay(WeChatH5PayReqDTO weChatH5PayReqDTO) {
                    log.error("h5支付失败:{}", weChatH5PayReqDTO);
                    return null;
                }

                @Override
                public IPage<WxStoreUserOrderItemDTO> userStorePage(Integer pageNo, Integer pageSize) {
                    log.error("分页查询我的订单失败:{}", pageNo);
                    Page<WxStoreUserOrderItemDTO> page = new Page<>();
                    page.setRecords(Collections.emptyList());
                    return page;
                }

                @Override
                public Boolean enableKeyboard() {
                    log.error("查询是否启用键盘失败:{}", WeixinUserThreadLocal.get());
                    return true;
                }

                @Override
                public String initialDineOrder() {
                    log.error("正餐订单初始化失败");
                    return "";
                }

                @Override
                public void payBackOrder(PayBackOrderRecordDTO payBackOrderRecordDTO) {
                    log.error("会员支付更新订单失败:{}", payBackOrderRecordDTO);
                }

                @Override
                public void sendUnMemberMessage(UnMemberMessageDTO unMemberMessageDTO) {
                    log.error("非会员推送消息失败:{}", unMemberMessageDTO);
                }

                @Override
                public WxReserveConfigDTO getConfig(SingleDataDTO singleDataDTO) {
                    log.error("查询门店预订失败:{}", singleDataDTO);
                    WxReserveConfigDTO wxReserveConfigDTO = new WxReserveConfigDTO();
                    wxReserveConfigDTO.setStatus(false);
                    return wxReserveConfigDTO;
                }

                @Override
                public WxReserveConfigDTO obtainByStore(String storeGuid) {
                    log.error("根据门店id:{}查询预订配置失败");
                    return null;
                }

                @Override
                public List<WxReserveConfigDTO> storeList(List<String> storeGuidList) {
                    log.error("根据门店查询预订配置失败:{}", storeGuidList);
                    return Collections.emptyList();
                }

                @Override
                public Boolean saveOrUpdateThirdPartUserInfo(WxThirdPartUserInfoReqDTO wxThirdPartUserInfoReqDTO) {
                    log.error("新增或更新第三方会员信息失败！");
                    return null;
                }

                @Override
                public WxThirdPartUserInfoRespDTO checkThirdPartUserInfo(WxQueryThirdPartUserInfoReqDTO wxQueryThirdPartUserInfoReqDTO) {
                    log.error("校验第三方会员信息失败！");
                    return null;
                }

                @Override
                public WxThirdPartUserInfoRespDTO queryThirdPartUserInfo(WxQueryThirdPartUserInfoReqDTO wxQueryThirdPartUserInfoReqDTO) {
                    log.error("查询第三方会员信息失败！");
                    return null;
                }

                @Override
                public List<DineInItemDTO> getItemListByOrderGuid(String orderGuid) {
                    log.error("查询微信订单明细失败", throwable);
                    return Lists.newArrayList();
                }

                @Override
                public void asyncTcdOrder(String orderGuid) {
                    log.error("同步小程序订单到赚餐失败， e={} orderGuid={}", throwable.getMessage(), orderGuid);
                    throw new BusinessException("同步小程序订单到赚餐失败");
                }

                @Override
                public WxOrderConfigDTO getDetailConfig(WxStoreReqDTO wxStoreReqDTO) {
                    log.error("获取微信点餐门店配置详细信息失败， e={} orderGuid={}", throwable.getMessage(),
                            JacksonUtils.writeValueAsString(wxStoreReqDTO));
                    throw new BusinessException("获取微信点餐门店配置详细信息失败");
                }

                @Override
                public OrderSubmitRespDTO submitDine(OrderSubmitReqDTO orderSubmitReqDTO) {
                    log.error("正餐下单失败， e={} orderGuid={}", throwable.getMessage(),
                            JacksonUtils.writeValueAsString(orderSubmitReqDTO));
                    return OrderSubmitRespDTO.submitFailed();
                }

                @Override
                public OrderSubmitRespDTO submitFast(OrderSubmitReqDTO orderSubmitReqDTO) {
                    log.error("快餐下单失败， e={} orderGuid={}", throwable.getMessage(),
                            JacksonUtils.writeValueAsString(orderSubmitReqDTO));
                    return OrderSubmitRespDTO.submitFailed();
                }

                @Override
                public WxMemberInfoVolumeDetailsRespDTO volumeCodeDetails(WxVolumeDetailReqDTO wxVolumeDetailReqDTO) {
                    log.error("查询优惠券详情失败:{}", wxVolumeDetailReqDTO);
                    return null;
                }

                @Override
                public OrderSubmitRespDTO submit(OrderSubmitReqDTO orderSubmitReqDTO) {
                    log.error("下单失败", throwable);
                    return new OrderSubmitRespDTO().setErrorMsg("下单失败");
                }
            };

        }
    }

}
