package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOperationDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.deal.WechatOrderInfoDTO;
import com.holderzone.saas.store.dto.weixin.req.WxOperateReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreMerchantOrderRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreMerchantOrderClientService
 * @date 2019/4/10
 */
@Component
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = WxStoreMerchantOrderClientService.WxStoreMerchantOrder.class)
public interface WxStoreMerchantOrderClientService {

    String URL_PREFIX = "/wx_store_merchant_order";

    @ApiOperation("获取当前门店所有订单")
    @PostMapping(URL_PREFIX + "/get_pend")
    WxStoreMerchantOrderRespDTO getWxStoreMerchantOrderResp(WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO);

    @ApiOperation("商户处理订单")
    @PostMapping(URL_PREFIX + "/operate")
    WxStoreMerchantOperationDTO operationMerchantOrder(WxOperateReqDTO wxOperateReqDTO);

    @ApiOperation("获取订单详情")
    @PostMapping(URL_PREFIX + "/get_detail_pend")
    WxStoreMerchantOrderDTO getDetailPend(WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO);

    /**
     * 根据订单记录id获取订单详情
     *
     * @param orderRecordGuid 微信订单记录guid
     * @return 订单详情
     */
    @ApiOperation("根据订单记录id获取订单详情")
    @GetMapping(URL_PREFIX + "/get_detail_by_order_record_guid")
    List<WxStoreMerchantOrderDTO> getDetailByOrderRecordGuid(@RequestParam("orderRecordGuid") String orderRecordGuid);

    /**
     * 根据订单Guid查询微信订单金额（接单&未接单）
     *
     * @param orderGuid 订单guid
     * @return 微信订单金额（接单&未接单）
     */
    @ApiOperation(value = "根据订单查询订单金额")
    @GetMapping(URL_PREFIX + "/get_wechat_order_fee_by_order_guid")
    BigDecimal getWechatOrderFeeByOrderGuid(@RequestParam("orderGuid") String orderGuid);

    /**
     * 根据订单Guid查询微信订单信息
     *
     * @param orderGuid 订单guid
     * @return 微信订单信息
     */
    @ApiOperation(value = "根据订单Guid查询微信订单信息")
    @GetMapping(URL_PREFIX + "/get_wechat_order_info_by_order_guid")
    WechatOrderInfoDTO getWechatOrderInfoByOrderGuid(@RequestParam("orderGuid") String orderGuid);

    /**
     * 根据Guid查询微信订单信息
     *
     * @param guid 订单guid/订单记录guid
     * @return 微信订单信息
     */
    @ApiOperation(value = "根据Guid查询微信订单信息")
    @GetMapping(URL_PREFIX + "/get_wechat_order_info_by_guid")
    WechatOrderInfoDTO getWechatOrderInfoByGuid(@RequestParam("guid") String guid);

    @Component
    class WxStoreMerchantOrder implements FallbackFactory<WxStoreMerchantOrderClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        private static final Logger logger =
                LoggerFactory.getLogger(WxStoreMerchantOrderClientService.WxStoreMerchantOrder.class);

        @Override
        public WxStoreMerchantOrderClientService create(Throwable throwable) {
            return new WxStoreMerchantOrderClientService() {

                @Override
                public WxStoreMerchantOrderRespDTO getWxStoreMerchantOrderResp(WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO) {
                    logger.error("远程调用服务失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public WxStoreMerchantOperationDTO operationMerchantOrder(WxOperateReqDTO wxOperateReqDTO) {
                    logger.error("远程调用服务失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public WxStoreMerchantOrderDTO getDetailPend(WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO) {
                    logger.error("远程调用服务失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<WxStoreMerchantOrderDTO> getDetailByOrderRecordGuid(String orderRecordGuid) {
                    logger.error("远程调用服务失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public BigDecimal getWechatOrderFeeByOrderGuid(String orderGuid) {
                    logger.error("远程调用服务失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public WechatOrderInfoDTO getWechatOrderInfoByOrderGuid(String orderGuid) {
                    logger.error(HYSTRIX_PATTERN, "getWechatOrderInfoByOrderGuid", orderGuid, throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public WechatOrderInfoDTO getWechatOrderInfoByGuid(String guid) {
                    logger.error(HYSTRIX_PATTERN, "getWechatOrderInfoByGuid", guid, throwable.getMessage());
                    throw new ServerException();
                }
            };
        }
    }
}
