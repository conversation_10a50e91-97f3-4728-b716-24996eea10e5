package com.holderzone.holder.saas.aggregation.app.service;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.item.req.ItemReqDTO;
import com.holderzone.saas.store.dto.boss.req.BossItemQueryDTO;
import com.holderzone.saas.store.dto.item.resp.ItemAndTypeForAndroidRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO;
import com.holderzone.saas.store.dto.item.resp.PadTypeItemRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 商品app聚合层接口
 * @date 2021/10/22 16:17
 * @className: ItemService
 */
public interface ItemService {

    /**
     * Pad商品查询接口
     *
     * @param baseDTO 门店guid
     * @return Pad分类、商品
     */
    PadTypeItemRespDTO queryItemListToPad(BaseDTO baseDTO);

    ItemAndTypeForAndroidRespDTO selectItemAndTypeForSyn(BaseDTO baseDTO);

    /**
     * 新建临时菜具处理逻辑
     *
     * @param itemSaveReqDTO ItemReqDTO
     * @return ItemInfoRespDTO
     */
    ItemInfoRespDTO getItemInfoRespDTOResult(ItemReqDTO itemSaveReqDTO);

    /**
     * 老板助手门店商品列表接口
     *
     * @param queryDTO 列表查询参数
     * @return 商品列表（不分页）
     */
    List<ItemWebRespDTO> listItemForBoss(BossItemQueryDTO queryDTO);

    /**
     * 老板助手保存商品接口
     *
     * @param itemSaveReqDTO 保存入参
     * @return EmptySuccess
     */
    void saveItemFromBoss(ItemReqDTO itemSaveReqDTO);

    /**
     * 老板助手更新商品接口
     *
     * @param itemUpdateReqDTO 更新商品接口入参
     * @return EmptySuccess
     */
    Integer updateItemFromBoss(ItemReqDTO itemUpdateReqDTO);
}
