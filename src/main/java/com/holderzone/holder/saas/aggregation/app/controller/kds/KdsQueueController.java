package com.holderzone.holder.saas.aggregation.app.controller.kds;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.kds.KdsQueueRpcService;
import com.holderzone.saas.store.dto.kds.req.QueueConfigDTO;
import com.holderzone.saas.store.dto.kds.req.QueueQueryReqDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsQueueRespDTO;
import com.holderzone.saas.store.dto.kds.resp.KitchenItemDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Api("KDS队列接口")
@RequestMapping("/kds_queue")
public class KdsQueueController {

    private final KdsQueueRpcService kdsQueueRpcService;

    @Autowired
    public KdsQueueController(KdsQueueRpcService kdsQueueRpcService) {
        this.kdsQueueRpcService = kdsQueueRpcService;
    }

    @PostMapping("/config/query")
    @ApiOperation(value = "查询取餐屏")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "查询取餐屏")
    public Result<QueueConfigDTO> queryConfig(@RequestBody QueueConfigDTO queueConfigDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询取餐屏入参:{}", JacksonUtils.writeValueAsString(queueConfigDTO));
        }
        return Result.buildSuccessResult(kdsQueueRpcService.queryConfig(queueConfigDTO));
    }

    @PostMapping("/config/update")
    @ApiOperation(value = "更新取餐屏")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "更新取餐屏")
    public Result updateConfig(@RequestBody QueueConfigDTO queueConfigDTO) {
        if (log.isInfoEnabled()) {
            log.info("更新取餐屏入参:{}", JacksonUtils.writeValueAsString(queueConfigDTO));
        }
        kdsQueueRpcService.updateConfig(queueConfigDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/item/query")
    @ApiOperation(value = "查询KDS队列元素")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "查询KDS队列元素")
    public Result<KdsQueueRespDTO> queryQueueItem(@RequestBody QueueQueryReqDTO queueQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询KDS队列元素入参:{}", JacksonUtils.writeValueAsString(queueQueryReqDTO));
        }
        return Result.buildSuccessResult(kdsQueueRpcService.queryQueueItem(queueQueryReqDTO));
    }

    @PostMapping("/item/call_for_meal")
    @ApiOperation(value = "出堂口呼叫取餐")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "出堂口呼叫取餐")
    public Result call(@RequestBody KitchenItemDTO kitchenItemDTO) {
        if (log.isInfoEnabled()) {
            log.info("出堂口呼叫取餐入参:{}", JacksonUtils.writeValueAsString(kitchenItemDTO));
        }
        kdsQueueRpcService.dstCallForMealAgain(kitchenItemDTO);
        return Result.buildEmptySuccess();
    }
}

