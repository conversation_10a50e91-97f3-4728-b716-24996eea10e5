package com.holderzone.holder.saas.aggregation.app.service.feign.trade;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.*;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SnackOrderDailyService
 * @date 2019/02/21 14:49
 * @description 快餐（order项目）营业日报接口
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-store-order", fallbackFactory = SnackOrderDailyClientService.SnackOrderFallBack.class)
public interface SnackOrderDailyClientService {
    @PostMapping("/daily/classify")
    List<ItemRespDTO> classify(DailyReqDTO request);

    @PostMapping("/daily/goods")
    List<ItemRespDTO> goods(DailyReqDTO request);

    @PostMapping("/daily/attr")
    List<AttrItemRespDTO> attr(DailyReqDTO request);

    @PostMapping("/daily/return_vegetables")
    List<ItemRespDTO> returnVegetables(DailyReqDTO request);

    @PostMapping("/daily/dish_giving")
    List<ItemRespDTO> dishGiving(DailyReqDTO request);

    @PostMapping("/daily/dining_type")
    DiningTypeRespDTO diningType(DailyReqDTO request);

    @Component
    class SnackOrderFallBack implements FallbackFactory<SnackOrderDailyClientService> {
        private static final Logger logger = LoggerFactory.getLogger(SnackOrderDailyClientService.SnackOrderFallBack.class);

        @Override
        public SnackOrderDailyClientService create(Throwable throwable) {
            return new SnackOrderDailyClientService() {
                @Override
                public List<ItemRespDTO> classify(DailyReqDTO request) {
                    logger.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public List<ItemRespDTO> goods(DailyReqDTO request) {
                    logger.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public List<AttrItemRespDTO> attr(DailyReqDTO request) {
                    logger.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public List<ItemRespDTO> returnVegetables(DailyReqDTO request) {
                    logger.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public List<ItemRespDTO> dishGiving(DailyReqDTO request) {
                    logger.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public DiningTypeRespDTO diningType(DailyReqDTO request) {
                    logger.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }
            };
        }
    }
}
