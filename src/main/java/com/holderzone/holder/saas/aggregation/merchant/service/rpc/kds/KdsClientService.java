package com.holderzone.holder.saas.aggregation.merchant.service.rpc.kds;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.DisplayItemRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayRepeatItemRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayRuleRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayStoreRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemClientService
 * @date 2018/09/07 下午4:29
 * @description //TODO
 * @program holder-saas-store-item
 */
@Component
@FeignClient(name = "holder-saas-store-kds", fallbackFactory = KdsClientService.KdsFallBack.class)
public interface KdsClientService {

    /**
     * 保存规则
     *
     * @param reqDTO DisplayRuleReqDTO
     * @return Boolean
     */
    @ApiOperation("保存规则")
    @PostMapping("/display_rule/save_rule")
    Boolean saveRule(@RequestBody DisplayRuleSaveOrUpdateDTO reqDTO);

    /**
     * 显示批次列表
     *
     * @param reqDTO 当前页数,每页显示条数，必传规则类型 0显示批次 1菜品汇总
     * @return Page<DisplayRuleRespDTO>
     */
    @ApiOperation("显示批次列表")
    @PostMapping("/display_rule/rule_list")
    Page<DisplayRuleRespDTO> batchList(@RequestBody DisplayRuleQueryDTO reqDTO);

    /**
     * 根据ruleGuid删除单个规则
     *
     * @param reqDTO ruleGuid
     * @return Boolean
     */
    @ApiOperation("删除规则")
    @PostMapping("/display_rule/delete_rule")
    Boolean deleteRule(@RequestBody DisplayRuleQueryDTO reqDTO);

    /**
     * 根据ruleGuid查询单个规则信息
     *
     * @param reqDTO ruleGuid
     * @return DisplayRuleRespDTO
     */
    @ApiOperation("查询规则")
    @PostMapping("/display_rule/get_rule")
    DisplayRuleRespDTO getRule(@RequestBody DisplayRuleQueryDTO reqDTO);

    /**
     * 更新规则
     *
     * @param reqDTO DisplayRuleReqDTO
     * @return Boolean
     */
    @ApiOperation("更新规则")
    @PostMapping("/display_rule/update_rule")
    Boolean updateRule(@RequestBody DisplayRuleSaveOrUpdateDTO reqDTO);

    /**
     * 查询kds商品列表
     *
     * @param reqDTO DisplayRuleQueryDTO
     * @return List<DisplayItemRespDTO>
     */
    @ApiOperation(value = "kds商品列表")
    @PostMapping("/display_item/item_list")
    List<DisplayItemRespDTO> listItem(@RequestBody DisplayRuleQueryDTO reqDTO);

    /**
     * 查询kds门店列表
     *
     * @param reqDTO DisplayRuleQueryDTO
     * @return List<DisplayStoreRespDTO>
     */
    @ApiOperation(value = "kds门店列表")
    @PostMapping("/display_store/item_store")
    List<DisplayStoreRespDTO> listStore(@RequestBody DisplayRuleQueryDTO reqDTO);

    /**
     * 查询有无选择全部门店 true:有选择 false：没有
     *
     * @param reqDTO DisplayRuleQueryDTO
     * @return Boolean
     */
    @ApiOperation("查询有无选择全部门店 true:有选择 false：没有")
    @PostMapping("/display_rule/query_has_all_store")
    Boolean queryHasAllStore(@RequestBody DisplayRuleQueryDTO reqDTO);

    /**
     * 更新菜品显示顺序配置
     */
    @ApiOperation("更新菜品显示顺序配置")
    @PostMapping("/display_item_sort/save_or_update")
    void saveOrUpdateItemSortRule(@RequestBody DisplayRuleItemSortDTO reqDTO);

    /**
     * 显示批次列表
     */
    @ApiOperation("查询菜品显示顺序配置")
    @PostMapping("/display_item_sort/query")
    DisplayRuleItemSortDTO queryItemSortRule(@RequestBody DisplayRuleItemSortQueryDTO reqDTO);

    /**
     * 保存可重复绑定的门店列表
     */
    @PostMapping("/display_repeat_item/save")
    void saveOrUpdateRepeatItem(@RequestBody DisplayRepeatItemStoreReqDTO reqDTO);

    @GetMapping("/display_repeat_item/query")
    DisplayRepeatItemRespDTO queryRepeatItem(@RequestParam("brandGuid") String brandGuid);

    @Component
    @Slf4j
    class KdsFallBack implements FallbackFactory<KdsClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public KdsClientService create(Throwable throwable) {
            return new KdsClientService() {

                @Override
                public Boolean saveRule(DisplayRuleSaveOrUpdateDTO reqDTO) {
                    log.error("保存规则异常：{}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public Page<DisplayRuleRespDTO> batchList(DisplayRuleQueryDTO reqDTO) {
                    log.error("显示批次列表异常：{}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public Boolean deleteRule(DisplayRuleQueryDTO reqDTO) {
                    log.error("删除规则异常：{}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public DisplayRuleRespDTO getRule(DisplayRuleQueryDTO reqDTO) {
                    log.error("查询规则异常：{}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public Boolean updateRule(DisplayRuleSaveOrUpdateDTO reqDTO) {
                    log.error("更新规则异常：{}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<DisplayItemRespDTO> listItem(DisplayRuleQueryDTO reqDTO) {
                    log.error("查询kds商品列表异常：{}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<DisplayStoreRespDTO> listStore(DisplayRuleQueryDTO reqDTO) {
                    log.error("查询kds门店列表异常：{}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public Boolean queryHasAllStore(DisplayRuleQueryDTO reqDTO) {
                    log.error("查询有无选择全部门店异常：{}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public void saveOrUpdateItemSortRule(DisplayRuleItemSortDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "saveOrUpdateItemSortRule",
                            JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public DisplayRuleItemSortDTO queryItemSortRule(DisplayRuleItemSortQueryDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryItemSortRule",
                            JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void saveOrUpdateRepeatItem(DisplayRepeatItemStoreReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "saveOrUpdateRepeatItem",
                            JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public DisplayRepeatItemRespDTO queryRepeatItem(String brandGuid) {
                    log.error(HYSTRIX_PATTERN, "queryRepeatItem",
                            brandGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }

}
