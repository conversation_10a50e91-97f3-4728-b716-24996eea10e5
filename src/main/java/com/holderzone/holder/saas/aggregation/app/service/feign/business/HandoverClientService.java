package com.holderzone.holder.saas.aggregation.app.service.feign.business;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.business.manage.*;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className HandoverService
 * @date 18-9-17 下午2:56
 * @description 服务间调用-交接班相关服务
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-store-business", fallbackFactory = HandoverClientService.ServiceFallBack.class)
public interface HandoverClientService {

    @PostMapping(value = "/handover/confirm")
    void confirm(@RequestBody HandoverRecordConfirmDTO handoverRecordConfirmDTO);

    @PostMapping(value = "/handover/confirmNew")
    void confirmNew(@RequestBody HandoverRecordConfirmDTO handoverRecordConfirmDTO);

    @PostMapping("/handover/settle")
    HandoverPayDTO settle(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO);
    @PostMapping("/handover/settleNew")
    HandoverPayNewDTO settleNew(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO);

    @PostMapping("/handover/query")
    HandoverRecordDTO query(@RequestBody HandoverRecordQuerySingleDTO handoverRecordQuerySingleDTO);
    @PostMapping("/handover/queryNew")
    HandoverPayNewDTO queryNew(@RequestBody HandoverRecordQuerySingleDTO handoverRecordQuerySingleDTO);

    @PostMapping("/handover/queryByPage")
    Page<HandoverRecordDTO> queryByPage(@RequestBody HandoverRecordQueryAllDTO handoverRecordQueryAllDTO);

    @PostMapping("/handover/isAllConfirmed")
    Integer isAllConfirmed(@RequestBody HandoverRecordIsAllConfirmedDTO handoverRecordIsAllConfirmedDTO);

    @PostMapping("/handover/printHandOverRecord/{handoverRecordGuid}/{deviceId}")
    void printHandOverRecord(@PathVariable("handoverRecordGuid") String handoverRecordGuid,
                             @PathVariable("deviceId") String deviceId);

    @PostMapping("/handover/printHandOverRecordNew/{handoverRecordGuid}/{deviceId}")
    void printHandOverRecordNew(@PathVariable("handoverRecordGuid") String handoverRecordGuid,
                             @PathVariable("deviceId") String deviceId);

    @PostMapping("/handover/prePrintHandOverRecord")
    void prePrintHandOverRecord(@RequestBody HandoverPayNewDTO handoverPayNewDTO);

    @PostMapping("/handover/retail/printHandOverRecord/{handoverRecordGuid}/{deviceId}")
    void printRetailHandOverRecord(@PathVariable("handoverRecordGuid") String handoverRecordGuid,
                             @PathVariable("deviceId") String deviceId);

    @PostMapping("/handover/queryByUserGuid")
    HandoverRecordDTO queryByUserGuid(@RequestBody HandoverRecordConfirmDTO handoverRecordConfirmDTO);

    @PostMapping("/handover/retail/settle")
    HandoverPayDTO retailSettle(HandoverPayQueryDTO handoverPayQueryDTO);

    @PostMapping("/handover/retail/confirm")
    void retailConfirm(HandoverRecordConfirmDTO handoverRecordConfirmDTO);

    @PostMapping("/handover/retail/queryByPage")
    Page<HandoverRecordDTO> retailQueryByPage(HandoverRecordQueryAllDTO handoverRecordQueryAllDTO);

    @PostMapping("/handover/retail/query")
    HandoverRecordDTO retailQuery(@RequestBody HandoverRecordQuerySingleDTO handoverRecordQuerySingleDTO);


    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<HandoverClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public HandoverClientService create(Throwable cause) {
            return new HandoverClientService() {
                @Override
                public void confirm(HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
                    log.error(HYSTRIX_PATTERN, "confirm", JacksonUtils.writeValueAsString(handoverRecordConfirmDTO),
                              ThrowableUtils.asString(cause));
                }

                @Override
                public void confirmNew(HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
                    log.error(HYSTRIX_PATTERN, "confirmNew", JacksonUtils.writeValueAsString(handoverRecordConfirmDTO),
                            ThrowableUtils.asString(cause));
                }

                @Override
                public HandoverPayDTO settle(HandoverPayQueryDTO handoverPayQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "settle", JacksonUtils.writeValueAsString(handoverPayQueryDTO),
                            ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public HandoverPayNewDTO settleNew(HandoverPayQueryDTO handoverPayQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "settleNew", JacksonUtils.writeValueAsString(handoverPayQueryDTO),
                            ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public HandoverRecordDTO query(HandoverRecordQuerySingleDTO handoverRecordQuerySingleDTO) {
                    log.error(HYSTRIX_PATTERN, "query", JacksonUtils.writeValueAsString(handoverRecordQuerySingleDTO),
                              ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public HandoverPayNewDTO queryNew(HandoverRecordQuerySingleDTO handoverRecordQuerySingleDTO) {
                    log.error(HYSTRIX_PATTERN, "queryNew", JacksonUtils.writeValueAsString(handoverRecordQuerySingleDTO),
                            ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public Page<HandoverRecordDTO> queryByPage(HandoverRecordQueryAllDTO handoverRecordQueryAllDTO) {
                    log.error(HYSTRIX_PATTERN, "queryAll", JacksonUtils.writeValueAsString(handoverRecordQueryAllDTO),
                              ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public Integer isAllConfirmed(HandoverRecordIsAllConfirmedDTO handoverRecordIsAllConfirmedDTO) {
                    log.error(HYSTRIX_PATTERN, "isAllConfirmed", JacksonUtils.writeValueAsString(handoverRecordIsAllConfirmedDTO),
                            ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public void printHandOverRecord(String handoverRecordGuid, String deviceId) {
                    log.error(HYSTRIX_PATTERN, "printHandOverRecord", "handoverRecordGuid为：" + handoverRecordGuid + "deviceId为：" + deviceId,
                              ThrowableUtils.asString(cause));
                }

                @Override
                public void printHandOverRecordNew(String handoverRecordGuid, String deviceId) {
                    log.error(HYSTRIX_PATTERN, "printHandOverRecordNew", "handoverRecordGuid为：" + handoverRecordGuid + "deviceId为：" + deviceId,
                            ThrowableUtils.asString(cause));
                }

                @Override
                public void prePrintHandOverRecord(HandoverPayNewDTO handoverPayNewDTO) {
                    log.error(HYSTRIX_PATTERN, "prePrintHandOverRecord", JacksonUtils.writeValueAsString(handoverPayNewDTO), ThrowableUtils.asString(cause));
                }

                @Override
                public void printRetailHandOverRecord(String handoverRecordGuid, String deviceId) {
                    log.error(HYSTRIX_PATTERN, "printRetailHandOverRecord", "handoverRecordGuid为：" + handoverRecordGuid + "deviceId为：" + deviceId,
                            ThrowableUtils.asString(cause));
                }

                @Override
                public HandoverRecordDTO queryByUserGuid(HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
                    log.error(HYSTRIX_PATTERN, "入参：", JacksonUtils.writeValueAsString(handoverRecordConfirmDTO), ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public HandoverPayDTO retailSettle(HandoverPayQueryDTO handoverPayQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "入参：", JacksonUtils.writeValueAsString(handoverPayQueryDTO), ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public void retailConfirm(HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
                    log.error(HYSTRIX_PATTERN, "入参：", JacksonUtils.writeValueAsString(handoverRecordConfirmDTO), ThrowableUtils.asString(cause));
                }

                @Override
                public Page<HandoverRecordDTO> retailQueryByPage(HandoverRecordQueryAllDTO handoverRecordQueryAllDTO) {
                    log.error(HYSTRIX_PATTERN, "入参：", JacksonUtils.writeValueAsString(handoverRecordQueryAllDTO), ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public HandoverRecordDTO retailQuery(HandoverRecordQuerySingleDTO handoverRecordQuerySingleDTO) {
                    log.error(HYSTRIX_PATTERN, "入参：", JacksonUtils.writeValueAsString(handoverRecordQuerySingleDTO), ThrowableUtils.asString(cause));
                    return  null;
                }
            };
        }
    }
}
