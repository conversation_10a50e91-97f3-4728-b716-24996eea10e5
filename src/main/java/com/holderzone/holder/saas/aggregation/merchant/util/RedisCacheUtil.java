package com.holderzone.holder.saas.aggregation.merchant.util;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Slf4j
@Component
@RequiredArgsConstructor
public class RedisCacheUtil {

    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * 获取缓存数据，如果不存在则通过supplier获取并缓存
     *
     * @param key 缓存key
     * @param supplier 数据提供者
     * @param expireTime 过期时间
     * @param timeUnit 时间单位
     * @return 缓存数据
     */
    public <T> T getOrSet(String key, Supplier<T> supplier, long expireTime, TimeUnit timeUnit) {
        // 尝试从缓存获取
        Object value = redisTemplate.opsForValue().get(key);
        if (value != null) {
            return (T) value;
        }

        // 缓存不存在，通过supplier获取
        T newValue = supplier.get();
        if (newValue != null) {
            // 设置缓存
            redisTemplate.opsForValue().set(key, newValue, expireTime, timeUnit);
        }
        return newValue;
    }

    /**
     * 删除缓存
     *
     * @param key 缓存key
     */
    public void delete(String key) {
        redisTemplate.delete(key);
    }
} 