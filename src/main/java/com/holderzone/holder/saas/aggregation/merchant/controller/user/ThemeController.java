package com.holderzone.holder.saas.aggregation.merchant.controller.user;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.user.ProductClientService;
import com.holderzone.saas.store.dto.user.ThemeReqDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MessageController
 * @date 2018/09/25 11:05
 * @description
 * @program holder-saas-aggregation-app
 */
@Slf4j
@RestController
@Api(value = "主题")
@RequestMapping("/product")
public class ThemeController {

    private final ProductClientService productClientService;

    @Autowired
    public ThemeController(ProductClientService productClientService) {
        this.productClientService = productClientService;
    }

    @ApiOperation(value = "查询产品主题")
    @PostMapping("/query_theme")
    @EFKOperationLogAop(
            PLATFORM = Platform.MERCHANTBACK,
            moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF,
            description = "查询产品主题"
    )
    public Result<String> queryThemeCode(@RequestBody ThemeReqDTO themeReqDTO) {
        log.info("查询产品主题 themeReqDTO={}", JacksonUtils.writeValueAsString(themeReqDTO));
        return Result.buildSuccessResult(productClientService.queryThemeCode(themeReqDTO));
    }
}
