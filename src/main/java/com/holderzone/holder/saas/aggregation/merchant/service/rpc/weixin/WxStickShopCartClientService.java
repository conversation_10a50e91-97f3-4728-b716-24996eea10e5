package com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.weixin.WxStickShopCartDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickShopCartRemoveDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickShopCartClientService
 * @date 2019/03/12 11:44
 * @description 微信桌贴购物车clientService
 * @program holder-saas-store
 */
@Component
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = WxStickShopCartClientService.FallBackService.class)
public interface WxStickShopCartClientService {

    @PostMapping("/wx_stick_shop_cart/list_shop_cart")
    List<WxStickShopCartDTO> listShopCart();

    @PostMapping("/wx_stick_shop_cart/add_models")
    void addModels(List<WxStickShopCartDTO> wxStickShopCartDTOList);

    @PostMapping("/wx_stick_shop_cart/remove_models")
    void removeModels(WxStickShopCartRemoveDTO wxStickShopCartRemoveDTO);

    @Component
    @Slf4j
    class FallBackService implements FallbackFactory<WxStickShopCartClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";
        @Override
        public WxStickShopCartClientService create(Throwable throwable) {
            return new WxStickShopCartClientService() {
                @Override
                public List<WxStickShopCartDTO> listShopCart() {
                    if (log.isWarnEnabled()) {
                        log.warn(HYSTRIX_PATTERN, "order", null, ThrowableUtils.asString(throwable));
                    }
                    throwable.printStackTrace();
                    throw new BusinessException("调用微信服务异常！");
                }

                @Override
                public void addModels(List<WxStickShopCartDTO> wxStickShopCartDTOList) {
                    if (log.isWarnEnabled()) {
                        log.warn(HYSTRIX_PATTERN, "order", JacksonUtils.writeValueAsString(wxStickShopCartDTOList), ThrowableUtils.asString(throwable));
                    }
                    throwable.printStackTrace();
                    throw new BusinessException("调用微信服务异常！");
                }

                @Override
                public void removeModels(WxStickShopCartRemoveDTO wxStickShopCartRemoveDTO) {
                    if (log.isWarnEnabled()) {
                        log.warn(HYSTRIX_PATTERN, "order", JacksonUtils.writeValueAsString(wxStickShopCartRemoveDTO), ThrowableUtils.asString(throwable));
                    }
                    throwable.printStackTrace();
                    throw new BusinessException("调用微信服务异常！");
                }
            };
        }
    }
}
