package com.holderzone.holder.saas.aggregation.app.aop;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.app.helper.RedisHelper;
import com.holderzone.holder.saas.aggregation.app.service.feign.table.TableClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInOrderClientService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.table.anno.LockField;
import com.holderzone.saas.store.dto.table.exception.TableBusinessLockException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableAop
 * @date 2019/01/17 10:08
 * @description
 * @program holder-saas-aggregation-app
 */
@Aspect
@Component
@Slf4j
public class TableValidatorAspect {

    @Autowired
    private TableClientService tableServiceClient;

    @Autowired
    private DineInOrderClientService dineInOrderClientService;

    @Autowired
    private RedisHelper redisHelper;

    @Pointcut("@annotation(com.holderzone.holder.saas.aggregation.app.anno.RequireBizAndRdsLock)")
    public void tableValidatorCut() {

    }

    @Pointcut("@annotation(com.holderzone.holder.saas.aggregation.app.anno.RequireRdsLock)")
    public void lockTableInRedissonCut() {

    }



    private static final String LOCK_STR = "lockStr";

    private static final String DEVICE_ID = "deviceId";

    private static final String FAST_FOOD = "fastFood";

    private static final String TABLE_GUID = "tableGuid";

//    @Around("tableWhetherLocked()")
//    public Object whetherLocked(ProceedingJoinPoint point) {
//        Object parameter = point.getArgs()[0];
//        Map<String, String> lockMap = getLockStr(parameter);
//        String tableGuid = lockMap.get(LOCK_STR);
//        String deviceId = lockMap.get(DEVICE_ID);
//        boolean fastFood = getFastFood(parameter);
//        if (!fastFood && (StringUtils.isEmpty(tableGuid) || StringUtils.isEmpty(deviceId))) {
//            throw new BusinessException("桌台号不能为空");
//        }
//        try {
//            if (!fastFood) {
//                boolean result = tableServiceClient.checkTableIsLocked(deviceId, tableGuid);
//                if (result) {
//                    throw new TableBusinessLockException("该桌台已被锁定");
//                }
//            }
//
//            return point.proceed();
//        } catch (Throwable throwable) {
//            log.error("Aspect校验业务锁异常 e={}", throwable.getMessage());
//            throwable.printStackTrace();
//            if (throwable instanceof TableBusinessLockException) {
//                throw new TableBusinessLockException(throwable.getMessage());
//            }
//            throw new BusinessException(throwable.getMessage());
//        }
//    }

    @Around("lockTableInRedissonCut()")
    public Object lockInRedisson(ProceedingJoinPoint point) {
        Object parameter = point.getArgs()[0];
        Map<String, String> lockMap = getLockStr(parameter);
        String tableGuid = lockMap.get(LOCK_STR);
        if (StringUtils.isEmpty(tableGuid)) {
            throw new BusinessException("缺少需要被锁定的桌台信息");
        }
        try {
            boolean lockResult = RedissonLockUtil.tryLock(tableGuid, 0, 10);
            if (!lockResult) {
                throw new BusinessException("该桌台正在被占用");
            }
            return point.proceed();
        } catch (Throwable throwable) {
            throwable.printStackTrace();
            log.error("Aspect分布式锁异常 e={}", throwable.getMessage());
            throw new BusinessException(throwable.getMessage());
        } finally {
            try {
                RedissonLockUtil.unlock(tableGuid);
            } catch (IllegalMonitorStateException e) {
                log.info("并发解锁异常 e={}", e.getCause());
                e.printStackTrace();
            }
        }
    }

    @Around("tableValidatorCut()")
    public Object validator(ProceedingJoinPoint point) {
        Object parameter = point.getArgs()[0];
        Map<String, String> lockMap = getLockStr(parameter);
        String tableGuid = lockMap.get(LOCK_STR);
        String deviceId = lockMap.get(DEVICE_ID);
        if (StringUtils.isEmpty(tableGuid) || StringUtils.isEmpty(deviceId)) {
            throw new BusinessException("缺少需要被锁定的桌台信息");
        }
        boolean lockResult = RedissonLockUtil.tryLock(tableGuid, 0, 10);
        if (!lockResult) {
            throw new BusinessException("该桌台正在被占用");
        }
        try {
            boolean result = tableServiceClient.checkTableIsLocked(deviceId, tableGuid);
            if (result) {
                throw new TableBusinessLockException("该桌台已被锁定");
            }
            return point.proceed();
        } catch (Throwable throwable) {
            log.error("Aspect校验异常 e={}", throwable.getMessage());
            throwable.printStackTrace();
            if (throwable instanceof TableBusinessLockException) {
                throw new TableBusinessLockException(throwable.getMessage());
            }
            throw new BusinessException(throwable.getMessage());
        } finally {
            try {
                RedissonLockUtil.unlock(tableGuid);
            } catch (IllegalMonitorStateException e) {
                log.info("并发解锁异常 e={}", e.getCause());
                e.printStackTrace();
            }
        }
    }

    private Map<String, String> getLockStr(Object parameter) {
        String lockStr = null;
        String deviceId = null;
        Map<String, String> map = new HashMap<>();
        if (parameter instanceof BaseDTO) {
            deviceId = ((BaseDTO) parameter).getDeviceId();
        }
        Class<?> aClass = parameter.getClass();
        for (Class<?> clazz = aClass; clazz != Object.class; clazz = clazz.getSuperclass()) {
            for (Field field : clazz.getDeclaredFields()) {
                LockField lockField = field.getAnnotation(LockField.class);
                if (null != lockField) {
                    try {
                        // 暂时只支持一个字段锁定
                        PropertyDescriptor pd = new PropertyDescriptor(field.getName(), aClass);
                        Method readMethod = pd.getReadMethod();
                        lockStr = (String) readMethod.invoke(parameter);
                        break;
                    } catch (IntrospectionException | InvocationTargetException | IllegalAccessException e) {
                        e.printStackTrace();
                    }
                } else if (field.getName().equals(TABLE_GUID) ) {
                    try {
                        PropertyDescriptor pd = new PropertyDescriptor(field.getName(), aClass);
                        Method readMethod = pd.getReadMethod();
                        lockStr = (String) readMethod.invoke(parameter);
                    } catch (IntrospectionException | IllegalAccessException | InvocationTargetException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        map.put(LOCK_STR, lockStr);
        map.put(DEVICE_ID, deviceId);
        return map;
    }

    private boolean getFastFood(Object parameter) {
        boolean fastFood = false;
        Class<?> aClass = parameter.getClass();
        for (Class<?> clazz = aClass; clazz != Object.class; clazz = clazz.getSuperclass()) {
            for (Field field : clazz.getDeclaredFields()) {
                if (field.getName().equals(FAST_FOOD)) {
                    try {
                        PropertyDescriptor pd = new PropertyDescriptor(field.getName(), aClass);
                        Method readMethod = pd.getReadMethod();
                        fastFood = (boolean) readMethod.invoke(parameter);
                    } catch (IntrospectionException | IllegalAccessException | InvocationTargetException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return fastFood;
    }


}
