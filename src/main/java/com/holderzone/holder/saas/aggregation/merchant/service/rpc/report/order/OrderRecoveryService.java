package com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.order;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.report.OrderRecoveryDTO;
import com.holderzone.saas.store.dto.report.OrderRecoveryDetailDTO;
import com.holderzone.saas.store.dto.report.OrderRecoveryDetailQueryDTO;
import com.holderzone.saas.store.dto.report.query.OrderRecoveryQueryDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.IOException;
import java.io.OutputStream;

@Component
@FeignClient(value = "holder-saas-store-report")
public interface OrderRecoveryService {

    /**
     * 查询反结账订单列表
     * @param query
     * @return
     */
    @PostMapping("/orderRecovery/orderRecoveryList")
    Page<OrderRecoveryDTO> orderRecoveryList(@RequestBody OrderRecoveryQueryDTO query);

    /**
     * 查询反结账详情
     * @return
     */
    @PostMapping("/orderRecovery/queryDetailByOrderNo")
    OrderRecoveryDetailDTO queryDetailByOrderNo(@RequestBody OrderRecoveryDetailQueryDTO query);

    /**
     * 导出反结账订单列表
     * @param query
     * @return
     */
    @GetMapping(value = "/orderRecovery/exportOrderRecovery")
    OutputStream exportOrderRecovery(OrderRecoveryQueryDTO query) throws IOException;
}
