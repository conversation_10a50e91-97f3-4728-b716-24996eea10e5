package com.holderzone.holder.saas.aggregation.merchant.controller.business;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.manage.DataSettingClientService;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingDTO;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingQueryDTO;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingSaveDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/7
 */
@Slf4j
@Api("数据取值设置")
@RestController
@RequestMapping("/data_setting")
public class DataSettingController {

    private final DataSettingClientService dataSettingClientService;

    @Autowired
    public DataSettingController(DataSettingClientService dataSettingClientService) {
        this.dataSettingClientService = dataSettingClientService;
    }

    @PostMapping(value = "/find_data_setting")
    @ApiOperation("查询数据取值设置列表")
    public Result<List<DataSettingDTO>> findDataSetting(@RequestBody @Valid DataSettingQueryDTO dataSettingQueryDTO) {
        log.info("查询'数据取值设置列表': reasonDTO={}", JacksonUtils.writeValueAsString(dataSettingQueryDTO));
        List<DataSettingDTO> dataSettingDTOS = dataSettingClientService.findDataSetting(dataSettingQueryDTO);
        return Result.buildSuccessResult(dataSettingDTOS);
    }

    @PostMapping(value = "/save_data_setting")
    @ApiOperation("批量保存数据取值设置")
    public Result saveDataSetting(@RequestBody @Valid DataSettingSaveDTO dataSettingSaveDTO) {
        log.info("批量更新'数据取值设置': dataSettingDTO={}", JacksonUtils.writeValueAsString(dataSettingSaveDTO));
        Boolean result = dataSettingClientService.saveDataSetting(dataSettingSaveDTO);
        if (result) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.UPDATE_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.UPDATE_FAILED));
    }
}
