package com.holderzone.holder.saas.aggregation.app.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Chinese2PinyinUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.app.service.ItemService;
import com.holderzone.holder.saas.aggregation.app.service.feign.PrintClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.cloud.EnterpriseCloudService;
import com.holderzone.holder.saas.aggregation.app.service.feign.item.ItemClientService;
import com.holderzone.holder.saas.aggregation.app.utils.MapstructUtil;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.boss.req.BossItemQueryDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.ItemQueryReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemReqDTO;
import com.holderzone.saas.store.dto.item.req.PadPictureDTO;
import com.holderzone.saas.store.dto.item.req.SkuSaveReqDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.print.PrinterAreaDTO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.PrinterItemDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.item.ModuleEntranceEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.holderzone.holder.saas.aggregation.app.constant.Constant.NUMBER_ONE;
import static com.holderzone.holder.saas.aggregation.app.constant.Constant.NUMBER_ZERO;

/**
 * <AUTHOR>
 * @description 商品app聚合层实现类
 * @date 2021/10/22 16:17
 * @className: ItemServiceImpl
 */
@Slf4j
@Service
@AllArgsConstructor
public class ItemServiceImpl implements ItemService {

    private final ItemClientService itemClientService;

    private final PrintClientService printClientService;

    private final EnterpriseCloudService enterpriseCloudService;

    /**
     * Pad商品查询接口
     *
     * @param baseDTO 门店guid
     * @return Pad分类、商品
     */
    @Override
    public PadTypeItemRespDTO queryItemListToPad(BaseDTO baseDTO) {
        if (ObjectUtils.isEmpty(baseDTO.getStoreGuid())) {
            throw new BusinessException("门店guid不能为空");
        }
        ItemAndTypeForAndroidRespDTO itemAndType = itemClientService.selectItemAndTypeForSyn(baseDTO);
        PadTypeItemRespDTO respDTO = new PadTypeItemRespDTO();
        if (ObjectUtils.isEmpty(itemAndType) || CollectionUtils.isEmpty(itemAndType.getTypeList())) {
            log.warn("query_for_synchronize查询为空");
            return respDTO;
        }
        // 构造pad商品
        constructionItem(baseDTO, itemAndType, respDTO);
        return respDTO;
    }

    /**
     * 构造pad商品
     *
     * @param baseDTO     入参
     * @param itemAndType 商品分类查询结果
     * @param respDTO     pad返回商品
     */
    private void constructionItem(BaseDTO baseDTO, ItemAndTypeForAndroidRespDTO itemAndType, PadTypeItemRespDTO respDTO) {
        String storeGuid = baseDTO.getStoreGuid();
        // pad只显示4普通，2多规格，1套餐
        ArrayList<Integer> typeList = new ArrayList<>();
        typeList.add(1);
        typeList.add(2);
        typeList.add(4);
        List<ItemSynRespDTO> itemSynRespDTOList = itemAndType.getItemList().stream()
                .filter(item -> typeList.contains(item.getItemType()))
                .collect(Collectors.toList());

        // 估清的商品不展示
        estimateFilter(baseDTO, itemSynRespDTOList);

        // 复用原来商品查询逻辑，处理数据多余字段
        respDTO.setTypeList(MapstructUtil.INSTANCE.typeSynList2PadTypeList(itemAndType.getTypeList()));
        List<PadItemRespDTO> padItemRespDTOS = MapstructUtil.INSTANCE.itemSynList2PadItemList(itemSynRespDTOList);
        Map<String, List<PadItemRespDTO>> padItemMap = padItemRespDTOS.stream()
                .collect(Collectors.groupingBy(PadItemRespDTO::getTypeGuid));

        // pad点餐图片查询
        Page<PadPictureRespDTO> padPicture = getPadPictureRespDTOPage(storeGuid, itemAndType);
        if (null == padPicture) {
            log.warn("pad点餐图片查询为空");
            return;
        }
        Map<String, PadPictureRespDTO> padPictureMap = padPicture.getData().stream()
                .collect(Collectors.toMap(PadPictureRespDTO::getItemGuid, type -> type));

        // pad图片设置
        respDTO.getTypeList().forEach(type -> {
            List<PadItemRespDTO> padItem = padItemMap.get(type.getTypeGuid());
            if (CollectionUtils.isEmpty(padItem)) {
                log.warn("分类下商品为空 typeGuid={}", type.getTypeGuid());
                return;
            }
            padItem.forEach(item -> {
                PadPictureRespDTO picture = padPictureMap.get(item.getItemGuid());
                if (ObjectUtils.isEmpty(picture)) {
                    log.warn("商品未设置图片信息 itemGuid={}", item.getItemGuid());
                    return;
                }
                List<PadSkuRespDTO> skuList = item.getSkuList();
                if (CollectionUtil.isNotEmpty(skuList)) {
                    List<PadSkuRespDTO> skuReturnList = new ArrayList<>();
                    for (PadSkuRespDTO sku : skuList) {
                        Integer isOpenStock = sku.getIsOpenStock();
                        Integer stock = sku.getStock();
                        boolean stockNotEnough = ObjectUtil.equal(isOpenStock, BooleanEnum.TRUE.getCode())
                                && ObjectUtil.equal(stock, NUMBER_ZERO);
                        //如果限购并且数量为0则pad不展示
                        if (!stockNotEnough) {
                            skuReturnList.add(sku);
                        }

                    }
                    log.info("结算估清后的商品信息：itemGuid:{},skuList:{}", item.getItemGuid(), JacksonUtils.writeValueAsString(skuReturnList));
                    item.setSkuList(skuReturnList);
                }
                if (ObjectUtils.isEmpty(picture.getSmallPicture())) {
                    item.setSmallPicture(Constant.EMPTY);
                } else {
                    item.setSmallPicture(Arrays.asList(picture.getSmallPicture().split(",")).get(0));
                }
                if (ObjectUtils.isEmpty(picture.getBigPicture())) {
                    item.setBigPicture(Constant.EMPTY);
                } else {
                    item.setBigPicture(Arrays.asList(picture.getBigPicture().split(",")).get(0));
                }
                if (ObjectUtils.isEmpty(picture.getVerticalPicture())) {
                    item.setVerticalPicture(Constant.EMPTY);
                } else {
                    item.setVerticalPicture(Arrays.asList(picture.getVerticalPicture().split(",")).get(0));
                }
                String detailPictureStr = picture.getDetailPicture();
                if (ObjectUtils.isEmpty(detailPictureStr)) {
                    item.setDetailPictureList(new ArrayList<>());
                } else {
                    item.setDetailPictureList(Arrays.asList(detailPictureStr.split(",")));
                }
            });
            //过滤商品sku为空的商品信息
            padItem.removeIf(item -> CollectionUtils.isEmpty(item.getSkuList()));
            type.setItemList(padItem);
        });

        // 过滤商品为空的分类
        respDTO.getTypeList().removeIf(type -> CollectionUtils.isEmpty(type.getItemList()));
    }

    /**
     * 估清商品过滤
     *
     * @param baseDTO            入参
     * @param itemSynRespDTOList 商品列表
     */
    private void estimateFilter(BaseDTO baseDTO, List<ItemSynRespDTO> itemSynRespDTOList) {
        List<ItemEstimateForAndroidRespDTO> estimateForSyn = itemClientService.queryEstimateForSyn(baseDTO);
        List<String> estimateSkuGuid = estimateForSyn.stream()
                .filter(estimate -> Objects.equals(2, estimate.getIsSoldOut()))
                .map(ItemEstimateForAndroidRespDTO::getSkuGuid)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(estimateSkuGuid)) {
            return;
        }
        itemSynRespDTOList.forEach(item -> {
            item.getSkuList().removeIf(sku -> estimateSkuGuid.contains(sku.getSkuGuid()));
        });
        List<String> toDeleteItemGuidList = new ArrayList<>();

        // 套餐商品处理
        for (ItemSynRespDTO itemSynRespDTO : itemSynRespDTOList) {
            String packageItemGuid = itemSynRespDTO.getItemGuid();
            if (Objects.equals(1, itemSynRespDTO.getItemType())) {
                List<SubgroupSynRespDTO> subgroupList = itemSynRespDTO.getSubgroupList();
                if (CollectionUtils.isEmpty(subgroupList)) {
                    log.warn("套餐商品无分组 itemGuid={}", packageItemGuid);
                    continue;
                }
                // 只有一个组并且是固定套餐
                if (1 == subgroupList.size()) {
                    SubgroupSynRespDTO subgroupSynRespDTO = subgroupList.get(0);
                    if (0 == subgroupSynRespDTO.getPickNum()) {
                        for (SubItemSkuSynRespDTO sub : subgroupSynRespDTO.getSubItemSkuList()) {
                            if (estimateSkuGuid.contains(sub.getSkuGuid())) {
                                toDeleteItemGuidList.add(packageItemGuid);
                                break;
                            }
                        }
                    } else {
                        subgroupSynRespDTO.getSubItemSkuList().removeIf(sub -> estimateSkuGuid.contains(sub.getSkuGuid()));
                        if (CollectionUtils.isEmpty(subgroupSynRespDTO.getSubItemSkuList())) {
                            toDeleteItemGuidList.add(packageItemGuid);
                        }
                    }
                } else {
                    subgroupList.removeIf(subGroup -> {
                        if (0 == subGroup.getPickNum()) {
                            for (SubItemSkuSynRespDTO sub : subGroup.getSubItemSkuList()) {
                                if (estimateSkuGuid.contains(sub.getSkuGuid())) {
                                    return true;
                                }
                            }
                        } else {
                            subGroup.getSubItemSkuList().removeIf(sub -> estimateSkuGuid.contains(sub.getSkuGuid()));
                            return CollectionUtils.isEmpty(subGroup.getSubItemSkuList());
                        }
                        return false;
                    });
                }
                if (CollectionUtils.isEmpty(subgroupList)) {
                    toDeleteItemGuidList.add(packageItemGuid);
                }
            }
        }
        if (!CollectionUtils.isEmpty(toDeleteItemGuidList)) {
            itemSynRespDTOList.removeIf(item -> toDeleteItemGuidList.contains(item.getItemGuid()));
        }

        // 删除规格数为零、套餐子商品为零的商品
        itemSynRespDTOList.removeIf(item -> CollectionUtils.isEmpty(item.getSkuList()));
    }

    /**
     * 查询pad点餐图片信息
     *
     * @param storeGuid   门店guid
     * @param itemAndType 分类和商品信息
     * @return pad点餐图片返回DTO
     */
    private Page<PadPictureRespDTO> getPadPictureRespDTOPage(String storeGuid, ItemAndTypeForAndroidRespDTO itemAndType) {
        List<String> typeGuidList = itemAndType.getTypeList().stream()
                .map(TypeSynRespDTO::getTypeGuid)
                .distinct()
                .collect(Collectors.toList());
        PadPictureDTO query = new PadPictureDTO();
        query.setCurrentPage(1);
        query.setPageSize(9999);
        query.setStoreGuid(storeGuid);
        query.setTypeGuidList(typeGuidList);
        if (!ObjectUtils.isEmpty(itemAndType.getPricePlanGuid())) {
            query.setPlanGuid(itemAndType.getPricePlanGuid());
        }
        Page<PadPictureRespDTO> padPicture = itemClientService.queryPadPicture(query);
        log.info("pad点餐图片查询 padPicture={}", JacksonUtils.writeValueAsString(padPicture));
        if (ObjectUtils.isEmpty(padPicture)) {
            log.warn("未查询到pad点餐图片 query={}", JacksonUtils.writeValueAsString(query));
            return null;
        }
        return padPicture;
    }

    @Override
    public ItemAndTypeForAndroidRespDTO selectItemAndTypeForSyn(BaseDTO baseDTO) {
        ItemAndTypeForAndroidRespDTO itemAndType = itemClientService.selectItemAndTypeForSyn(baseDTO);

        //商户后台之前提交的图片都是单张，现在有可能出现多张，用逗号分隔。这里做适配，截取第一张给取餐屏、pad
        List<ItemSynRespDTO> itemLists = itemAndType.getItemList();
        if (!ObjectUtils.isEmpty(itemLists)) {
            itemLists.forEach(itemSynRespDTO -> {
                String picUrl = itemSynRespDTO.getPictureUrl();
                String[] pics = null;
                if (!StringUtils.isEmpty(picUrl)) {
                    pics = picUrl.split(",");
                }
                if (!ObjectUtils.isEmpty(pics)) {
                    itemSynRespDTO.setPictureUrl(pics[0]);
                }
            });
            itemAndType.setItemList(itemLists);
        }

        // 老pad查询新pad图片模式
        if (Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), baseDTO.getDeviceType())) {
            handlePadPicture(baseDTO, itemAndType);
        }
        return itemAndType;
    }

    /**
     * 处理pad图片
     *
     * @param baseDTO     门店guid
     * @param itemAndType 分类和商品信息
     */
    private void handlePadPicture(BaseDTO baseDTO, ItemAndTypeForAndroidRespDTO itemAndType) {
        Page<PadPictureRespDTO> padPicture = getPadPictureRespDTOPage(baseDTO.getStoreGuid(), itemAndType);
        if (null == padPicture) {
            return;
        }
        Map<String, PadPictureRespDTO> padPictureMap = padPicture.getData().stream()
                .collect(Collectors.toMap(PadPictureRespDTO::getItemGuid, type -> type, (oldValue, newValue) -> newValue));

        // pad图片设置
        itemAndType.getItemList().forEach(item -> {
            PadPictureRespDTO picture = padPictureMap.get(item.getItemGuid());
            if (ObjectUtils.isEmpty(picture)) {
                log.warn("商品未设置图片信息 itemGuid={}", item.getItemGuid());
                return;
            }
            if (!ObjectUtils.isEmpty(picture.getSmallPicture())) {
                item.setSmallPicture(Optional.ofNullable(Arrays.asList(picture.getSmallPicture().split(",")).get(0))
                        .orElse(Constant.EMPTY));
            }
            if (!ObjectUtils.isEmpty(picture.getBigPicture())) {
                item.setBigPicture(Optional.ofNullable(Arrays.asList(picture.getBigPicture().split(",")).get(0))
                        .orElse(Constant.EMPTY));
            }

            if (ObjectUtils.isEmpty(picture.getVerticalPicture())) {
                item.setVerticalPicture(Constant.EMPTY);
            } else {
                item.setVerticalPicture(Arrays.asList(picture.getVerticalPicture().split(",")).get(0));
            }
            // 老pad没得详情页面
            item.setDetailPicture(Constant.EMPTY);
        });
    }

    /**
     * 新建临时菜具处理逻辑
     *
     * @param itemSaveReqDTO ItemReqDTO
     * @return ItemInfoRespDTO
     */
    @Override
    public ItemInfoRespDTO getItemInfoRespDTOResult(ItemReqDTO itemSaveReqDTO) {
        //item服务：新建门店级菜品
        //入参补全
        itemSaveReqDTO.setFrom(NUMBER_ZERO);
        itemSaveReqDTO.setIsNew(NUMBER_ZERO);
        itemSaveReqDTO.setIsBestseller(NUMBER_ZERO);
        itemSaveReqDTO.setIsSign(NUMBER_ZERO);
        itemSaveReqDTO.setDescription("");
        itemSaveReqDTO.setPictureUrl("");
        //拼音简码
        String initials = Chinese2PinyinUtils.initials(itemSaveReqDTO.getName());
        itemSaveReqDTO.setPinyin(initials);
        List<@Valid SkuSaveReqDTO> skuList = itemSaveReqDTO.getSkuList();
        skuList.forEach(
                sku -> {
                    sku.setName("");
                    sku.setMinOrderNum(BigDecimal.ONE);
                    //只上架一体机，不上架POS机和pad
                    sku.setIsRack(NUMBER_ONE);
                    sku.setIsJoinAio(NUMBER_ONE);
                    sku.setIsJoinPos(NUMBER_ZERO);
                    sku.setIsJoinPad(NUMBER_ZERO);
                    //防止意外设置值
                    sku.setIsJoinBuffet(NUMBER_ZERO);
                    sku.setIsJoinWeChat(NUMBER_ZERO);
                    //临时菜默认参加整单折扣
                    sku.setIsWholeDiscount(NUMBER_ONE);
                }
        );

        String itemGuid = itemClientService.saveItem(itemSaveReqDTO);
        //获取商品信息
        ItemSingleDTO singleDTO = new ItemSingleDTO();
        singleDTO.setData(itemGuid);
        ItemInfoRespDTO itemInfo = itemClientService.getItemInfo(singleDTO);
        itemInfo.setTypeName(itemSaveReqDTO.getTypeName());
        //kds服务：绑定商品--暂时不做
        //打印服务：获取打印机信息，传了打印机guid才去绑定，如果没传就不绑定
        if (ObjectUtils.isEmpty(itemSaveReqDTO.getPrinterGuid())) {
            if (CollectionUtils.isEmpty(itemSaveReqDTO.getPrinterGuidList())) {
                return itemInfo;
            }
            batchBindPrinter(itemSaveReqDTO, itemInfo, itemGuid);
            return itemInfo;
        }
        singleBindPrinter(itemSaveReqDTO, itemInfo, itemGuid);
        return itemInfo;
    }

    private void singleBindPrinter(ItemReqDTO itemSaveReqDTO,
                                   ItemInfoRespDTO itemInfo,
                                   String itemGuid) {
        PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setPrinterGuid(itemSaveReqDTO.getPrinterGuid());
        PrinterDTO printer = printClientService.getPrinter(printerDTO);

        buildPrinter(itemSaveReqDTO, printer, itemInfo, itemGuid);
        printClientService.updatePrinter(printer);
    }

    private void batchBindPrinter(ItemReqDTO itemSaveReqDTO,
                                  ItemInfoRespDTO itemInfo,
                                  String itemGuid) {
        SingleDataDTO request = new SingleDataDTO();
        request.setDatas(itemSaveReqDTO.getPrinterGuidList());
        List<PrinterDTO> printerDTOList = printClientService.listPrinter(request);
        printerDTOList.forEach(printerDTO1 -> {
            //绑定商品，原来的商品也要加上
            buildPrinter(itemSaveReqDTO, printerDTO1, itemInfo, itemGuid);
        });
        PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setPrinterDTOList(printerDTOList);
        printClientService.batchUpdatePrinter(printerDTO);
    }

    private void buildPrinter(ItemReqDTO itemSaveReqDTO,
                              PrinterDTO printer,
                              ItemInfoRespDTO itemInfo,
                              String itemGuid) {
        //绑定商品，原来的商品也要加上
        List<PrinterItemDTO> arrayOfItemDTO = printer.getArrayOfItemDTO();
        List<String> arrayOfItemGuid = new ArrayList<>();
        //原绑定商品处理
        arrayOfItemDTO.forEach(
                item -> arrayOfItemGuid.add(item.getItemName() + "," + item.getItemGuid())
        );
        arrayOfItemGuid.add(itemInfo.getName() + "," + itemGuid);
        printer.setArrayOfItemGuid(arrayOfItemGuid);

        //原绑定区域处理
        List<PrinterAreaDTO> arrayOfAreaDTO = printer.getArrayOfAreaDTO();
        List<String> arrayOfAreaGuid = new ArrayList<>();
        arrayOfAreaDTO.forEach(
                area -> arrayOfAreaGuid.add(area.getAreaName() + "," + area.getAreaGuid())
        );
        printer.setArrayOfAreaGuid(arrayOfAreaGuid);

        printer.setStaffGuid(itemSaveReqDTO.getUserGuid());
    }

    /**
     * 老板助手门店商品列表接口
     *
     * @param queryDTO 列表查询参数
     * @return 商品列表（不分页）
     */
    @Override
    public List<ItemWebRespDTO> listItemForBoss(BossItemQueryDTO queryDTO) {
        ItemQueryReqDTO queryReqDTO = new ItemQueryReqDTO();
        queryReqDTO.setPageSize(999L);
        queryReqDTO.setIsRack(queryDTO.getIsRack());
        queryReqDTO.setTypeGuid(queryDTO.getTypeGuid());
        queryReqDTO.setStoreGuid(queryDTO.getStoreGuid());
        Page<ItemWebRespDTO> itemForWebPage = itemClientService.selectItemForWeb(queryReqDTO);
        return itemForWebPage.getData();
    }

    /**
     * 老板助手保存商品接口
     *
     * @param itemSaveReqDTO 保存入参
     * @return EmptySuccess
     */
    @Override
    public void saveItemFromBoss(ItemReqDTO itemSaveReqDTO) {
//        handleParam(itemSaveReqDTO);
        itemSaveReqDTO.setFrom(ModuleEntranceEnum.STORE.code());
        itemClientService.saveItem(itemSaveReqDTO);
    }

    /**
     * 获取企业经营模式，排除平台类型
     *
     * @return 企业经营模式
     */
    private String getManagementModel() {
        if (StringUtils.isEmpty(UserContextUtils.getEnterpriseGuid())) {
            throw new BusinessException("企业guid为空");
        }
        String managementModel = enterpriseCloudService.queryManagementModel(UserContextUtils.getEnterpriseGuid());
        if (Objects.equals("PLATFORM", managementModel)) {
            throw new BusinessException("企业经营模式不支持");
        }
        return managementModel;
    }

    /**
     * 老板助手更新商品接口
     *
     * @param itemUpdateReqDTO 更新商品接口入参
     * @return EmptySuccess
     */
    @Override
    public Integer updateItemFromBoss(ItemReqDTO itemUpdateReqDTO) {
//        handleParam(itemUpdateReqDTO);
        itemUpdateReqDTO.setFrom(ModuleEntranceEnum.STORE.code());
        return itemClientService.updateItem(itemUpdateReqDTO);
    }

    /**
     * 处理不同企业经营类型
     *
     * @param itemUpdateReqDTO 参数
     */
    private void handleParam(ItemReqDTO itemUpdateReqDTO) {
        String managementModel = getManagementModel();
        if (Objects.equals("CHAIN", managementModel)) {
            itemUpdateReqDTO.setFrom(ModuleEntranceEnum.BRAND.code());
            if (CollectionUtils.isEmpty(itemUpdateReqDTO.getDataList())) {
                throw new BusinessException("商品所属门店不能为空");
            }
        }
        if (Objects.equals("SINGLE", managementModel)) {
            itemUpdateReqDTO.setFrom(ModuleEntranceEnum.STORE.code());
        }
    }
}
