package com.holderzone.holder.saas.aggregation.app.entity.auth;

import com.holderzone.holder.saas.aggregation.app.anno.DataAuthFieldControl;
import com.holderzone.saas.store.dto.order.response.daily.GatherRespDTO;
import com.holderzone.saas.store.util.EncryptionSymbolUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 收款方式统计
 */
@Data
public class GatherSaleDTO implements Serializable {

    private static final long serialVersionUID = -7454114775577506227L;

    /**
     * 收款方式
     */
    private Integer gatherCode;

    /**
     * 收款方式
     */
    @DataAuthFieldControl("gather_sale_type_name")
    private String gatherName;

    /**
     * 销售净额
     */
    @DataAuthFieldControl("gather_sale_actually_amount")
    private String consumerAmount;

    /**
     * 超出金额，销售收入内
     */
    @DataAuthFieldControl("gather_sale_actually_amount")
    private String excessAmount;

    /**
     * 充值收入
     */
    @DataAuthFieldControl("gather_sale_recharge_amount")
    private String prepaidAmount;

    /**
     * 预付金
     */
    @DataAuthFieldControl("gather_sale_reserve_amount")
    private String reserveAmount;

    /**
     * 是否合计 0否 1是
     */
    private int isTotal = 0;

    @ApiModelProperty(value = "明细")
    @DataAuthFieldControl(value = "gather_sale_type_name", isListClear = true)
    private List<GatherRespDTO.InnerDetails> innerDetails;

    public String getTotalAmount() {
        BigDecimal innerTotalAmount = BigDecimal.ZERO;
        if (EncryptionSymbolUtil.isNormal(consumerAmount)) {
            innerTotalAmount = innerTotalAmount.add(new BigDecimal(consumerAmount));
        }
        if (EncryptionSymbolUtil.isNormal(prepaidAmount)) {
            innerTotalAmount = innerTotalAmount.add(new BigDecimal(prepaidAmount));
        }
        if (EncryptionSymbolUtil.isNormal(reserveAmount)) {
            innerTotalAmount = innerTotalAmount.add(new BigDecimal(reserveAmount));
        }
        return innerTotalAmount.stripTrailingZeros().toPlainString();

    }
}