package com.holderzone.holder.saas.aggregation.weixin.service.rpc.cmember;


import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberClientService
 * @date 2018/08/06 11:22
 * @description
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-cmember-base-service", fallbackFactory = MemberBaseClientService
        .MemberClientFallback.class)
public interface MemberBaseClientService {

//    @GetMapping("/hsmca/member/{memberInfoCardGuid}/fullInfo")
//    CardFullInfoRespDTO fullInfo(@PathVariable("memberInfoCardGuid") String memberInfoCardGuid);

//    @GetMapping("/hsmca/member/getMemberInfoAndCardTwo")
//    MemberAndCardInfoRespDTO getMemberInfoAndCardTwo(@RequestBody QueryStoreAndMemberAndCardReqDTO queryStoreAndMemberAndCardReqDTO);

//    @GetMapping("/hsmca/card/{memberInfoCardGuid}/hasMemberPrice")
//    boolean hasMemberPrice(@PathVariable("memberInfoCardGuid") String memberInfoCardGuid);

    @Component
    class MemberClientFallback implements FallbackFactory<MemberBaseClientService> {

        private static final Logger logger = LoggerFactory.getLogger(MemberClientFallback.class);

        @Override
        public MemberBaseClientService create(Throwable throwable) {
            return new MemberBaseClientService() {
                /*@Override
                public CardFullInfoRespDTO fullInfo(@PathVariable("memberInfoCardGuid") String memberInfoCardGuid) {
                    logger.error("获取会员信息调用异常e={}", throwable.getMessage());
                    throw new ParameterException("获取会员信息调用异常");
                }*/

                /*@Override
                public MemberAndCardInfoRespDTO getMemberInfoAndCardTwo(QueryStoreAndMemberAndCardReqDTO
                                                                             queryStoreAndMemberAndCardReqDTO) {
                    logger.error("获取会员信息调用异常e={}", throwable.getMessage());
                    throw new ParameterException("获取会员信息调用异常");
                }*/
//                @Override
//                public boolean hasMemberPrice(String memberInfoCardGuid) {
//                    logger.error("获取会员是否能使用会员价 e={}", throwable.getMessage());
//                    return false;
//                    //throw new ParameterException("获取会员是否能使用会员价");
//                }
            };
        }
    }

}
