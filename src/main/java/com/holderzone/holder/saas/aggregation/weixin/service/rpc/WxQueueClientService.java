package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.saas.store.dto.queue.*;
import com.holderzone.saas.store.dto.weixin.WxInQueueReqDTO;
import com.holderzone.saas.store.dto.weixin.WxQueueDetailDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxInQueueRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxTotalQueueDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQueueClientService
 * @date 2019/05/16 10:34
 * @description 微信排队ClientService
 * @program holder-saas-store
 */
@Service
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = WxQueueClientService.FallBackClass.class)
public interface WxQueueClientService {

    @GetMapping(value = "/wx_store_order_record/get_by_order_holder_no")
    String getByOrderHolderNo(@RequestParam(value = "orderHolderNo") String orderHolderNo);

    @PostMapping("/wx_queue/groupingByQueueStatus")
    @ApiOperation("统计各排队分组数量")
    Map<Integer, Integer> queryQueueStatusNum(QueueWechatDTO queueWechatDTO);

    @PostMapping("/wx_queue/query_by_guid")
    @ApiOperation(value = "用户所有门店的排队信息",notes = " 排队状态 0:队列中,1:过号,2:叫号中,3:已就餐,4:已取消")
    List<WxQueueListDTO> queryByGuid(@RequestBody @Valid QueueWechatDTO queueWechatDTO);

    @PostMapping("/wx_queue/query_detail")
    @ApiOperation(value = "排队详情",notes = " 排队状态 0:队列中,1:过号,2:叫号中,3:已就餐,4:已取消")
    HolderQueueQueueRecordDTO getQueueDetail(@RequestBody ItemGuidDTO dto);

    @GetMapping("/wx_queue/update_cancel_queue")
    @ApiOperation(value = "取消排队")
    Boolean cancelQueue(@RequestParam("queueGuid") String queueGuid, @RequestParam("enterpriseGuid") String enterpriseGuid);


    @PostMapping("/wx_queue/detail")
    WxQueueDetailDTO getDetail(WxPortalReqDTO wxPortalReqDTO);

    @PostMapping("/wx_queue/in_queue")
    WxInQueueRespDTO inQueue(WxInQueueReqDTO wxInQueueReqDTO);

    @PostMapping("/wx_queue/total_detail")
    WxTotalQueueDTO getTotalDetail(WxPortalReqDTO wxPortalReqDTO);

    @Component
    @Slf4j
    class FallBackClass implements FallbackFactory<WxQueueClientService> {
        @Override
        public WxQueueClientService create(Throwable throwable) {
            return new WxQueueClientService() {

                private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

                @Override
                public String getByOrderHolderNo(String orderHolderNo) {
                    log.error(HYSTRIX_PATTERN,"/wx_store_order_record/get_by_order_holder_no",orderHolderNo,throwable.getMessage());
                    return null;
                }

                @Override
                public Map<Integer, Integer> queryQueueStatusNum(QueueWechatDTO queueWechatDTO) {
                    log.error(HYSTRIX_PATTERN,"/wx_queue/groupingByStatus",queueWechatDTO,throwable.getMessage());
                    throw new RuntimeException(throwable.getCause());
                }

                @Override
                public List<WxQueueListDTO> queryByGuid(QueueWechatDTO queueWechatDTO) {
                    log.error(HYSTRIX_PATTERN,"/wx_queue/query_by_guid",queueWechatDTO,throwable.getMessage());
                    throw new RuntimeException(throwable.getCause());
                }

                @Override
                public HolderQueueQueueRecordDTO getQueueDetail(ItemGuidDTO dto) {
                    log.error(HYSTRIX_PATTERN,"/wx_queue/query_detail",dto,throwable.getMessage());
                    throw new RuntimeException(throwable.getCause());
                }

                @Override
                public Boolean cancelQueue(String guid,String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN,"/wx_queue/update_cancel_queue/{enterpriseGuid}",guid+","+enterpriseGuid,throwable.getMessage());
                    throw new RuntimeException(throwable.getCause());
                }

                @Override
                public WxQueueDetailDTO getDetail(WxPortalReqDTO wxPortalReqDTO) {
                    log.error(HYSTRIX_PATTERN,"/wx_queue/detail",wxPortalReqDTO,throwable.getMessage());
                    throw new RuntimeException(throwable.getCause());
                }

                @Override
                public WxInQueueRespDTO inQueue(WxInQueueReqDTO wxInQueueReqDTO) {
                    log.error(HYSTRIX_PATTERN,"/wx_queue/in_queue",wxInQueueReqDTO,throwable.getMessage());
                    throw new RuntimeException(throwable.getCause());
                }

                @Override
                public WxTotalQueueDTO getTotalDetail(WxPortalReqDTO wxPortalReqDTO) {
                    log.error(HYSTRIX_PATTERN,"/wx_queue/total_detail",wxPortalReqDTO,throwable.getMessage());
                    throw new RuntimeException(throwable.getCause());
                }
            };

        }
    }
}
