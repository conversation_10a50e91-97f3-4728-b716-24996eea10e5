package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberListClientService
 * @date 2018/09/26 15:49
 * @description //TODO
 * @program holder-saas-store-member
 */

import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.order.OrderCancelClientService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.member.common.BaseMemberDTO;
import com.holderzone.saas.store.dto.member.request.*;
import com.holderzone.saas.store.dto.member.response.*;
import com.holderzone.saas.store.dto.report.OrderRecoveryDetailDTO;
import com.holderzone.saas.store.dto.report.query.OrderTransRecordQueryDTO;
import com.holderzone.saas.store.dto.report.resp.OrderTransRecordRespDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


@Component
@FeignClient(name = "holder-saas-store-member",fallbackFactory = MemberListClientService.MemberListServiceFallBack.class)
public interface MemberListClientService {

    @PostMapping("/member_list/getMemberList")
    Page<MemberListRespDTO> memberList(MemberListReqDTO memberListReqDTO);

    @PostMapping("/member_list/memberDetail")
    MemberDetailRespDTO getMemberDetailByGuid(BaseMemberDTO baseMemberDTO);

    @PostMapping("/member_list/memberCards")
    List<MemberCardsRespDTO> getMemberCards(MemberCardsReqDTO memberCardsReqDTO);

    @PostMapping("/member_list/memberGrades")
    List<MemberGradeListDTO> memberGradeList(BaseDTO baseDTO);
    @PostMapping("/member_list/getMemberTransactionRecords")
    Page<MemberPayRecordRespDTO> memberTransactionRecords(MemberPayRecordReqDTO memberPayRecordReqDTO);

    @PostMapping("/member_list/getMemberIntegralType")
    List<MemberIntegralTypeRespDTO> getMemberIntegralType(BaseDTO baseDTO);

    @PostMapping("/member_list/getMemberIntegralRecords")
    Page<MemberIntegralRecordRespDTO> getmemberIntegralRecords(MemberIntegralReqDTO memberIntegrals);

    @PostMapping("/member_list/getMemberListwithNoPage")
    List<MemberListRespDTO> AllmemberList(MemberListReqDTO memberListReqDTO);

    @Component
    class MemberListServiceFallBack implements FallbackFactory<MemberListClientService> {

        private static final Logger logger = LoggerFactory.getLogger(MemberListServiceFallBack.class);

        @Override
        public MemberListClientService create(Throwable throwable) {
            return new MemberListClientService(){

                @PostMapping("/member_list/getMemberList")
                @Override
                public Page<MemberListRespDTO> memberList(MemberListReqDTO memberListReqDTO) {
                    logger.error("会员列表失败，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public MemberDetailRespDTO getMemberDetailByGuid(BaseMemberDTO baseMemberDTO) {
                    logger.error("会员详情失败，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<MemberCardsRespDTO> getMemberCards(MemberCardsReqDTO memberCardsReqDTO) {
                    logger.error("会员卡列表失败，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<MemberGradeListDTO> memberGradeList(BaseDTO baseDTO) {
                    logger.error("会员中心会员等级列表失败，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Page<MemberPayRecordRespDTO> memberTransactionRecords(MemberPayRecordReqDTO memberPayRecordReqDTO) {
                    logger.error("会员支付充值失败，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<MemberIntegralTypeRespDTO> getMemberIntegralType(BaseDTO baseDTO) {
                    logger.error("会员列积分规则，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Page<MemberIntegralRecordRespDTO> getmemberIntegralRecords(MemberIntegralReqDTO memberIntegrals) {
                    logger.error("会员积分记录，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }
                @Override
                public List<MemberListRespDTO> AllmemberList(MemberListReqDTO memberListReqDTO) {
                    logger.error("查询所有会员不分页，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }
            };
        }
    }
}
