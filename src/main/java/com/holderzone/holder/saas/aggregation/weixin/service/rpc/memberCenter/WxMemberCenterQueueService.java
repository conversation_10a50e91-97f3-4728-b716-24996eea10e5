package com.holderzone.holder.saas.aggregation.weixin.service.rpc.memberCenter;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.queue.QueueWechatDTO;
import com.holderzone.saas.store.dto.queue.WxQueueListDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Service
@FeignClient(name = "holder-saas-store-queue", fallbackFactory = WxMemberCenterQueueService.WxMemberCenterQueueServiceFallBack.class)
public interface WxMemberCenterQueueService {

	@PostMapping("/queue/queryByUser")
	 List<WxQueueListDTO> queryByUser(@RequestBody QueueWechatDTO queueWechatDTO);

	@PostMapping("/queue/cancel/{enterpriseGuid}")
	 Boolean cancel(@RequestParam("queueGuid") String queueGuid, @PathVariable("enterpriseGuid") String enterpriseGuid);

	@Slf4j
	@Component
	class WxMemberCenterQueueServiceFallBack implements FallbackFactory<WxMemberCenterQueueService> {
		@Override
		public WxMemberCenterQueueService create(Throwable throwable) {
			return new WxMemberCenterQueueService() {

				@Override
				public List<WxQueueListDTO> queryByUser(QueueWechatDTO queueWechatDTO) {
					log.error("查询排队记录列表失败:{}", queueWechatDTO);
					throw new BusinessException(throwable.getMessage());
				}

				@Override
				public Boolean cancel(String queueGuid,String enterpriseGuid) {
					log.error("取消失败:{}", queueGuid);
					throw new BusinessException(throwable.getMessage());
				}

			};
		}
	}
}
