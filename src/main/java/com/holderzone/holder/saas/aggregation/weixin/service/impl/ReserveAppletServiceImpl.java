package com.holderzone.holder.saas.aggregation.weixin.service.impl;

import com.holderzone.holder.saas.aggregation.weixin.service.ReserveAppletService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.ReserveClientService;
import com.holderzone.saas.store.reserve.api.dto.ReserveAvailableStoreConfigDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveAvailableStoreDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveAppletQueryDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
public class ReserveAppletServiceImpl implements ReserveAppletService {

    private final ReserveClientService reserveClientService;

    @Override
    public List<ReserveAvailableStoreDTO> getAvailableStoreList(ReserveAppletQueryDTO queryDTO) {
        return reserveClientService.getAvailableStoreList(queryDTO);
    }

    @Override
    public ReserveAvailableStoreConfigDTO getAvailableStore(ReserveAppletQueryDTO queryDTO) {
        return reserveClientService.getAvailableStore(queryDTO);
    }

    @Override
    public String launch(ReserveRecordDTO reserveRecordDTO) {
        return reserveClientService.launch(reserveRecordDTO);
    }
}
