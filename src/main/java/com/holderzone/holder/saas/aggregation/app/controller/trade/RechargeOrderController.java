package com.holderzone.holder.saas.aggregation.app.controller.trade;

import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInOrderClientService;
import com.holderzone.holder.saas.member.merchant.dto.member.RequestCardRechargePageQO;
import com.holderzone.holder.saas.member.merchant.dto.member.ResponseCardRechargeStatisticsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.holderzone.framework.response.Result;

@Slf4j
@RestController
@RequestMapping("/recharge_order")
@Api(tags = "充值订单接口")
@AllArgsConstructor
public class RechargeOrderController {

    private final DineInOrderClientService dineInOrderClientService;

    /**
     * 一体机查询充值订单列表
     *
     * @param request request model
     * @return page ResponseMemberConsumption
     */
    @ApiOperation("一体机查询充值订单列表")
    @PostMapping("/getMemberCardRechargePage")
    Result<ResponseCardRechargeStatisticsVO> getMemberCardRechargePage(@RequestBody RequestCardRechargePageQO request) {
        return Result.buildSuccessResult(dineInOrderClientService.getMemberCardRechargePage(request));
    }
}
