package com.holderzone.holder.saas.aggregation.merchant.service.rpc.user;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.user.UserAuthorityDTO;
import com.holderzone.saas.store.dto.user.UserAuthorityQueryDTO;
import com.holderzone.saas.store.dto.user.UserAuthoritySaveDTO;
import com.holderzone.saas.store.dto.user.UserAuthorityUpdateDTO;
import com.holderzone.saas.store.dto.user.req.AuthorizationReqDTO;
import com.holderzone.saas.store.dto.user.resp.PermissionsRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className UserService
 * @date 18-9-17 上午10:09
 * @description 服务间调用-员工/权限服务
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-store-staff", fallbackFactory = UserAuthorityFeignService.ServiceFallBack.class)
public interface UserAuthorityFeignService {

    @ApiOperation(value = "保存员工账号授权")
    @PostMapping(value = "/user_authority/save_user_authority")
    void saveUserAuthority(@RequestBody UserAuthoritySaveDTO userAuthoritySaveDTO);

    @ApiOperation(value = "查询员工账号权限")
    @PostMapping(value = "/user_authority/query_user_authority")
    List<UserAuthorityDTO> queryUserAuthority(@RequestBody UserAuthorityQueryDTO userAuthorityQueryDTO);

    @PostMapping(value = "/user_authority/update")
    void updateUserData(@RequestBody UserAuthorityUpdateDTO userAuthorityUpdateDTO);

    @ApiOperation(value = "删除员工账号权限")
    @PostMapping(value = "/user_authority/delete")
    Boolean deleteAuthority(@RequestBody AuthorizationReqDTO deleteDTO);

    @ApiOperation(value = "查询所有可升级权限")
    @PostMapping(value = "/user_authority/query_authority")
    List<PermissionsRespDTO> queryAuthority();

    @ApiOperation(value = "录入人脸")
    @PostMapping(value = "/user_authority/inputFace")
    void inputFace(@RequestBody UserAuthoritySaveDTO userAuthoritySaveDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<UserAuthorityFeignService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public UserAuthorityFeignService create(Throwable cause) {
            return new UserAuthorityFeignService() {

                @Override
                public void saveUserAuthority(UserAuthoritySaveDTO userAuthoritySaveDTO) {
                    log.error(HYSTRIX_PATTERN, "saveUserData", "无", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<UserAuthorityDTO> queryUserAuthority(UserAuthorityQueryDTO userAuthorityQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "saveUserData", "无", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void updateUserData(UserAuthorityUpdateDTO userAuthorityUpdateDTO) {
                    log.error(HYSTRIX_PATTERN, "saveUserData", "无", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Boolean deleteAuthority(AuthorizationReqDTO deleteDTO) {
                    log.error(HYSTRIX_PATTERN, "deleteAuthority", "无", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<PermissionsRespDTO> queryAuthority() {
                    log.error(HYSTRIX_PATTERN, "queryAuthority", "无", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void inputFace(UserAuthoritySaveDTO userAuthoritySaveDTO) {
                    log.error(HYSTRIX_PATTERN, "inputFace", JacksonUtils.writeValueAsString(userAuthoritySaveDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }
    }
}
