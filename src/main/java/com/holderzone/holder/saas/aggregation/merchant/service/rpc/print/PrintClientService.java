package com.holderzone.holder.saas.aggregation.merchant.service.rpc.print;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.SingleDataPageDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.type.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(name = "holder-saas-store-print", fallbackFactory = PrintClientService.FallbackFactoryImpl.class)
public interface PrintClientService {

    @PostMapping("/format/add")
    void addFormat(@RequestBody String formatDTO);

    @PostMapping("/format/list")
    String listFormat(@RequestBody FormatDTO formatDTO);

    @PostMapping("/format/delete")
    void deleteFormat(@RequestBody FormatDTO formatDTO);

    @PostMapping("/format/enable")
    void enableFormat(@RequestBody FormatDTO formatDTO);

    /**
     * 创建打印分类模版
     */
    @PostMapping("/print_type_template/create")
    @ApiOperation(value = "创建打印分类模版")
    void create(@RequestBody @Validated PrintTypeTemplateDTO createDTO);

    /**
     * 查询打印分类模版列表
     */
    @PostMapping("/print_type_template/query_page")
    @ApiOperation(value = "查询打印分类模版列表", notes = "查询打印分类模版列表")
    Page<PrintTypeTemplateVO> queryPage(@RequestBody SingleDataPageDTO query);

    /**
     * 查询打印分类模版详情
     */
    @PostMapping("/print_type_template/query_detail")
    @ApiOperation(value = "查询打印分类模版详情", notes = "查询打印分类模版详情")
    PrintTypeTemplateDetailDTO queryDetail(@RequestBody @Validated SingleDataDTO query);

    /**
     * 更新打印分类模版
     */
    @PostMapping("/print_type_template/modify")
    @ApiOperation(value = "更新打印分类模版", notes = "更新打印分类模版")
    void modify(@RequestBody @Validated PrintTypeTemplateDTO modifyDTO);

    /**
     * 启用/禁用打印分类模版
     */
    @PostMapping("/print_type_template/enable")
    @ApiOperation(value = "启用/禁用打印分类模版", notes = "启用/禁用打印分类模版")
    boolean enable(@RequestBody @Validated PrintTypeTemplateEnableDTO enableDTO);

    /**
     * 删除打印分类模版
     */
    @PostMapping("/print_type_template/delete")
    @ApiOperation(value = "删除打印分类模版", notes = "删除打印分类模版")
    boolean delete(@RequestBody @Validated SingleDataDTO deleteDTO);

    /**
     * 查询品牌模版已配置的门店
     */
    @PostMapping("/print_type_template/query_store_by_brand")
    @ApiOperation(value = "查询品牌模版已配置的门店", notes = "查询品牌模版已配置的门店")
    List<String> queryStoreByBrand(@RequestBody TemplateStoreQO query);

    /**
     * 校验模版名称
     */
    @PostMapping("/print_type_template/check_template_name")
    @ApiOperation(value = "校验模版名称", notes = "校验模版名称")
    Boolean checkTemplateName(@RequestBody PrintTypeTemplateDTO query);

    @Slf4j
    @Component
    class FallbackFactoryImpl implements FallbackFactory<PrintClientService> {

        @Override
        public PrintClientService create(Throwable throwable) {

            return new PrintClientService() {

                private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

                @Override
                public void addFormat(String formatDTO) {
                    log.error("createFormat，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public String listFormat(FormatDTO formatDTO) {
                    log.error("queryFormat，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public void deleteFormat(FormatDTO formatDTO) {
                    log.error("deleteFormat，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public void enableFormat(FormatDTO formatDTO) {
                    log.error("enableFormat，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public void create(PrintTypeTemplateDTO createDTO) {
                    log.error(HYSTRIX_PATTERN, "create", JacksonUtils.writeValueAsString(createDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<PrintTypeTemplateVO> queryPage(SingleDataPageDTO query) {
                    log.error(HYSTRIX_PATTERN, "queryPage", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PrintTypeTemplateDetailDTO queryDetail(SingleDataDTO query) {
                    log.error(HYSTRIX_PATTERN, "queryDetail", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void modify(PrintTypeTemplateDTO modifyDTO) {
                    log.error(HYSTRIX_PATTERN, "modify", JacksonUtils.writeValueAsString(modifyDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public boolean enable(PrintTypeTemplateEnableDTO enableDTO) {
                    log.error(HYSTRIX_PATTERN, "enable", JacksonUtils.writeValueAsString(enableDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public boolean delete(SingleDataDTO deleteDTO) {
                    log.error(HYSTRIX_PATTERN, "delete", JacksonUtils.writeValueAsString(deleteDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<String> queryStoreByBrand(TemplateStoreQO query) {
                    log.error(HYSTRIX_PATTERN, "queryStoreByBrand", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Boolean checkTemplateName(PrintTypeTemplateDTO query) {
                    log.error(HYSTRIX_PATTERN, "checkTemplateName", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
