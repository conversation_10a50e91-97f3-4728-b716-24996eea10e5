package com.holderzone.holder.saas.aggregation.merchant.controller.queue;


import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.queue.QueueConfigClientService;
import com.holderzone.saas.store.dto.queue.StoreConfigDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueConfigController
 * @date 2019/05/09 18:40
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Api("排队 - 队列")
@RestController
public class QueueConfigController {
    @Autowired
    private QueueConfigClientService configClientService;
    @ApiOperation("保存排队设置")
    @PostMapping("/queue/config")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "保存排队设置")
    public Result<StoreConfigDTO> config(@RequestBody StoreConfigDTO dto){
        return Result.buildSuccessResult(configClientService.config(dto));
    }

    @ApiOperation("获取队列配置")
    @GetMapping("/queue/config")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "获取队列配置")
    public Result<StoreConfigDTO> query(@RequestParam("storeGuid") String storeGuid){
        return Result.buildSuccessResult(configClientService.query(storeGuid));
    }
}