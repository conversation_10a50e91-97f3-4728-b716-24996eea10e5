package com.holderzone.holder.saas.aggregation.app.service.feign.business;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.trade.SystemDiscountDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SystemDiscountClientService
 * @date 2018/09/10 16:15
 * @description
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-store-business", fallbackFactory = SystemDiscountClientService.SystemDiscountFallBack.class)
public interface SystemDiscountClientService {

    @PostMapping("/system/add")
    String addSystemDiscount(@RequestBody SystemDiscountDTO systemDiscountDTO);

    @PostMapping("/system/update")
    String update(@RequestBody SystemDiscountDTO systemDiscountDTO);

    @PostMapping("/system/delete/{storeGuid}/{systemDiscountGuid}")
    String delete(@PathVariable("storeGuid") String storeGuid, @PathVariable("systemDiscountGuid") String systemDiscountGuid);

    @PostMapping("/system/getAll/{storeGuid}")
    List<SystemDiscountDTO> queryAll(@PathVariable("storeGuid") String storeGuid);

    @Component
    class SystemDiscountFallBack implements FallbackFactory<SystemDiscountClientService> {

        private static final Logger logger = LoggerFactory.getLogger(SystemDiscountFallBack.class);

        @Override
        public SystemDiscountClientService create(Throwable throwable) {
            return new SystemDiscountClientService() {
                @Override
                public String addSystemDiscount(SystemDiscountDTO systemDiscountDTO) {
                    logger.error("新增系统折扣异常！！e={}", throwable.getMessage());
                    throw new BusinessException("新增系统折扣异常" + throwable.getMessage());
                }

                @Override
                public String update(SystemDiscountDTO systemDiscountDTO) {
                    logger.error("修改系统折扣异常！！e={}", throwable.getMessage());
                    throw new BusinessException("修改系统折扣异常" + throwable.getMessage());
                }

                @Override
                public String delete(String storeGuid, String systemDiscountGuid) {
                    logger.error("删除系统折扣异常！！e={}", throwable.getMessage());
                    throw new BusinessException("删除系统折扣异常" + throwable.getMessage());
                }

                @Override
                public List<SystemDiscountDTO> queryAll(String storeGuid) {
                    logger.error("查询所有系统折扣异常！！e={}", throwable.getMessage());
                    throw new BusinessException("查询所有系统折扣异常" + throwable.getMessage());
                }
            };
        }
    }

}
