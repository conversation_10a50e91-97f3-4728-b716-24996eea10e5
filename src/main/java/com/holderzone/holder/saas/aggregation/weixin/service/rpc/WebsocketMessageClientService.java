package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.holder.saas.weixin.entry.dto.req.MqttMessageReqDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = WebsocketMessageClientService.WebsocketMessageClientFallBack.class)
public interface WebsocketMessageClientService {
    @PostMapping("/mqtt/sendDinnerEmqMessage")
    public boolean sendDinnerEmqMessage(@RequestBody MqttMessageReqDTO mqttMessageReqDTO);

    @PostMapping("/mqtt/sendOrderEmqMessage")
    public void sendOrderEmqMessage(@RequestBody MqttMessageReqDTO mqttMessageReqDTO);

    /**
     * tableGuid，openids必填
     * @param mqttMessageReqDTO
     */
    @PostMapping("/mqtt/sendShopcartEmqMessage")
    public void sendShopcartEmqMessage(@RequestBody MqttMessageReqDTO mqttMessageReqDTO);

    @PostMapping("/mqtt/sendTableEmqMessage")
    public void sendTableEmqMessage(@RequestBody MqttMessageReqDTO mqttMessageReqDTO);

    @Slf4j
    @Component
    class WebsocketMessageClientFallBack implements FallbackFactory<WebsocketMessageClientService> {

        @Override
        public WebsocketMessageClientService create(Throwable throwable) {
            return new WebsocketMessageClientService() {

                @Override
                public boolean sendDinnerEmqMessage(MqttMessageReqDTO mqttMessageReqDTO) {
                    log.error("sendDinnerEmqMessage远程调用失败,mqttMessageReqDTO：{}",mqttMessageReqDTO, throwable.getMessage());
                    return false;
                }

                @Override
                public void sendOrderEmqMessage(MqttMessageReqDTO mqttMessageReqDTO) {
                    log.error("sendOrderEmqMessage远程调用失败,mqttMessageReqDTO：{}",mqttMessageReqDTO, throwable.getMessage());
                }

                @Override
                public void sendShopcartEmqMessage(MqttMessageReqDTO mqttMessageReqDTO) {
                    log.error("sendShopcartEmqMessage远程调用失败,mqttMessageReqDTO：{}",mqttMessageReqDTO, throwable.getMessage());
                }

                @Override
                public void sendTableEmqMessage(MqttMessageReqDTO mqttMessageReqDTO) {
                    log.error("sendTableEmqMessage远程调用失败,mqttMessageReqDTO：{}",mqttMessageReqDTO, throwable.getMessage());
                }
            };
        }

    }
}



