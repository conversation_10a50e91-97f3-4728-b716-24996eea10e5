package com.holderzone.holder.saas.aggregation.merchant.controller.journaling;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.journaling.ReportClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.HsmMemberDataCenterClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.trade.TradeClientService;
import com.holderzone.holder.saas.member.merchant.dto.statistics.RequestMemberAge;
import com.holderzone.holder.saas.member.merchant.dto.statistics.ResponseAgeDistribution;
import com.holderzone.holder.saas.member.merchant.dto.statistics.ResponseMemberGrowth;
import com.holderzone.holder.saas.member.merchant.dto.statistics.ResponseMemberSexAndConsume;
import com.holderzone.saas.store.dto.journaling.req.BusinessSituationReqDTO;
import com.holderzone.saas.store.dto.journaling.req.SaleCountReqDTO;
import com.holderzone.saas.store.dto.journaling.req.SaleStatisticsByHoursReqDTO;
import com.holderzone.saas.store.dto.journaling.req.StoreStatisticsAppReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.SaleRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.SaleStatisticsByHoursRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.ScreenBusinessDataRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.StoreStatisticsAppRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenDataController
 * @date 2019/08/29 15:32
 * @description 大屏数据controller
 * @program holder-saas-store
 */
@RestController
@RequestMapping("/screen")
@Slf4j
@Api(tags = "大屏相关接口（包括会员等）")
public class ScreenDataController {

    @Value("${queryES.enterpriseGuidList}")
    private String queryESEnterpriseGuidList;

    private final ReportClientService reportClientService;

    private final TradeClientService tradeClientService;

    private final HsmMemberDataCenterClientService hsmMemberDataCenterClientService;

    public ScreenDataController(ReportClientService reportClientService, TradeClientService tradeClientService,
                                HsmMemberDataCenterClientService hsmMemberDataCenterClientService) {
        this.reportClientService = reportClientService;
        this.tradeClientService = tradeClientService;
        this.hsmMemberDataCenterClientService = hsmMemberDataCenterClientService;
    }

    @PostMapping("/business_data")
    @ApiOperation("大屏营业统计数据接口")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT, description = "大屏营业统计数据接口")
    public Result<ScreenBusinessDataRespDTO> getScreenData(@RequestBody BusinessSituationReqDTO businessSituationReqDTO) {
        //门店兼容
        if (CollectionUtils.isEmpty(businessSituationReqDTO.getStoreGuidList())) {
            List<String> storeGuids = businessSituationReqDTO.getStoreGuidList();
            storeGuids.add(businessSituationReqDTO.getStoreGuid());
            businessSituationReqDTO.setStoreGuidList(storeGuids);
        }
        //时间兼容
        if (businessSituationReqDTO.getBusinessEndDateTime() == null) {
            businessSituationReqDTO.setBusinessEndDateTime(businessSituationReqDTO.getEndDate().atTime(LocalTime.MAX));
        }
        if (businessSituationReqDTO.getBusinessStartDateTime() == null) {
            businessSituationReqDTO.setBusinessStartDateTime(businessSituationReqDTO.getStartDate().atTime(LocalTime.MIN));
        }
        // 何师走原来逻辑，其他企业直接查mysql
        ScreenBusinessDataRespDTO screenData;
        if (queryESEnterpriseGuidList.contains(UserContextUtils.getEnterpriseGuid())) {
            screenData = reportClientService.getScreenData(businessSituationReqDTO);
        } else {
            screenData = tradeClientService.screenBusinessData(businessSituationReqDTO);
        }
        return Result.buildSuccessResult(screenData);
    }

    @PostMapping("/sale_count")
    @ApiOperation("销售报表")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT, description = "销售报表")
    public Result<SaleRespDTO> saleCountByEnterprise(@Valid @RequestBody SaleCountReqDTO saleCountReqDTO) {
        log.info("销售报表入参,{}", JacksonUtils.writeValueAsString(saleCountReqDTO));
        //时间兼容
        if (saleCountReqDTO.getBusinessEndDateTime() == null) {
            saleCountReqDTO.setBusinessEndDateTime(saleCountReqDTO.getEndDate().atTime(LocalTime.MAX));
        }
        if (saleCountReqDTO.getBusinessStartDateTime() == null) {
            saleCountReqDTO.setBusinessStartDateTime(saleCountReqDTO.getStartDate().atTime(LocalTime.MIN));
        }
        // 区分查询
        SaleRespDTO saleRespDTO;
        if (queryESEnterpriseGuidList.contains(UserContextUtils.getEnterpriseGuid())) {
            saleRespDTO = reportClientService.saleCount(saleCountReqDTO);
        } else {
            saleRespDTO = tradeClientService.getSaleCount(saleCountReqDTO);
        }
        return Result.buildSuccessResult(saleRespDTO);
    }

    @PostMapping("/store")
    @ApiOperation(value = "门店统计列表")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT, description = "门店统计列表")
    public Result<StoreStatisticsAppRespDTO> list(@RequestBody StoreStatisticsAppReqDTO storeStatisticsAppReqDTO) {
        storeStatisticsAppReqDTO.setIsMch(true);
        log.info("app-门店统计，入参：storeStatisticsAppReqDTO={}", JacksonUtils.writeValueAsString(storeStatisticsAppReqDTO));
        //门店兼容
        if (CollectionUtils.isEmpty(storeStatisticsAppReqDTO.getStoreGuidList())) {
            List<String> storeGuids = storeStatisticsAppReqDTO.getStoreGuidList();
            storeGuids.add(storeStatisticsAppReqDTO.getStoreGuid());
            storeStatisticsAppReqDTO.setStoreGuidList(storeGuids);
        }
        //时间兼容
        if (storeStatisticsAppReqDTO.getBusinessEndDateTime() == null) {
            storeStatisticsAppReqDTO.setBusinessEndDateTime(storeStatisticsAppReqDTO.getEndDate().atTime(LocalTime.MAX));
        }
        if (storeStatisticsAppReqDTO.getBusinessStartDateTime() == null) {
            storeStatisticsAppReqDTO.setBusinessStartDateTime(storeStatisticsAppReqDTO.getStartDate().atTime(LocalTime.MIN));
        }
        // 区分查询
        StoreStatisticsAppRespDTO storeStatistics;
        if (queryESEnterpriseGuidList.contains(UserContextUtils.getEnterpriseGuid())) {
            storeStatistics = reportClientService.appStoreStatistics(storeStatisticsAppReqDTO);
        } else {
            storeStatistics = tradeClientService.saleStoreStatistics(storeStatisticsAppReqDTO);
        }
        return Result.buildSuccessResult(storeStatistics);
    }

    @PostMapping("/sale_by_hour/statistics")
    @ApiOperation("销售额按小时分段统计")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT, description = "销售额按小时分段统计")
    public Result<SaleStatisticsByHoursRespDTO> doStatisticsByHours(@RequestBody SaleStatisticsByHoursReqDTO saleStatisticsByHoursReqDTO) {
        log.info("销售额按小时分段统计请求入参：{}", JacksonUtils.writeValueAsString(saleStatisticsByHoursReqDTO));
        //门店兼容
        if (CollectionUtils.isEmpty(saleStatisticsByHoursReqDTO.getStoreGuidList())) {
            List<String> storeGuids = saleStatisticsByHoursReqDTO.getStoreGuidList();
            storeGuids.add(saleStatisticsByHoursReqDTO.getStoreGuid());
            saleStatisticsByHoursReqDTO.setStoreGuidList(storeGuids);
        }
        //不用时间兼容
        // 区分查询
        SaleStatisticsByHoursRespDTO respDTO;
        if (queryESEnterpriseGuidList.contains(UserContextUtils.getEnterpriseGuid())) {
            respDTO = reportClientService.doStatisticsByHours(saleStatisticsByHoursReqDTO);
        } else {
            respDTO = tradeClientService.saleByHoursStatistics(saleStatisticsByHoursReqDTO);
        }
        return Result.buildSuccessResult(respDTO);
    }


    @PostMapping("/hsm-member-statistics/ageDistribution")
    @ApiOperation("年龄分布")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT, description = "年龄分布")
    public Result<List<ResponseAgeDistribution>> ageDistribution(
            @ApiParam("把年龄分布用单个阿拉伯数字传输，{0,17,18,30,31,50,50},按照这种方式传参,最后的那个50代表的就是50岁以上")
            @RequestBody RequestMemberAge body) {
        log.info("查询-年龄分布，入参：{}", JacksonUtils.writeValueAsString(body));
        return hsmMemberDataCenterClientService.ageDistribution(body);
    }

    @GetMapping("/hsm-member-statistics/memberGrowth")
    @ApiOperation("近7日会员增长")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT, description = "近7日会员增长")
    public Result<List<ResponseMemberGrowth>> memberGrowth(@ApiParam("是查看最近几天的会员增长数据，如果是7天则传7，如果是30天则传30") String number) {
        log.info("查询-会员增长情况，入参：{}", number);
        return hsmMemberDataCenterClientService.memberGrowth(number);
    }

    @GetMapping("/hsm-member-statistics/querySexAndConsume")
    @ApiOperation("查询会员数，性别构成，周消费频次")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT, description = "查询会员数")
    public Result<ResponseMemberSexAndConsume> querySexAndConsume() {
        return hsmMemberDataCenterClientService.querySexAndConsume();
    }
}
