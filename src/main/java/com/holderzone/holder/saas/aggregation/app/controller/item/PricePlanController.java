package com.holderzone.holder.saas.aggregation.app.controller.item;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.PrintClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.item.ItemClientService;
import com.holderzone.saas.store.dto.item.req.PreOrderValidateReq;
import com.holderzone.saas.store.dto.item.resp.PreOrderValidateRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @version 1.0
 * @className PricePlanController
 * @date 2021/3/24 下午4:27
 * @description //TODO
 * @program holder-saas-store-dish
 */
@RestController
@RequestMapping("/plan")
@Api(tags = "菜谱接口")
@Slf4j
public class PricePlanController {
    private final ItemClientService itemClientService;
    private final PrintClientService printClientService;

    @Autowired
    public PricePlanController(ItemClientService itemClientService, PrintClientService printClientService) {
        this.itemClientService = itemClientService;
        this.printClientService = printClientService;
    }

    @ApiOperation(value = "一体机预点餐菜谱验证")
    @PostMapping("/preOrderVerification")
    public Result<PreOrderValidateRespDTO> preOrderVerification(@RequestBody PreOrderValidateReq preOrderValidateReq) {
        log.info("query_for_preOrderVerification入参,request={}", JacksonUtils.writeValueAsString(preOrderValidateReq));
        PreOrderValidateRespDTO respDTO = itemClientService.preOrderVerification(preOrderValidateReq);
        return Result.buildSuccessResult(respDTO);
    }

}
