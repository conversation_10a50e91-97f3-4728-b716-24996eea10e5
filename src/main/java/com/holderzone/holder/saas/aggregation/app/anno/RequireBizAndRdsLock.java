package com.holderzone.holder.saas.aggregation.app.anno;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LockAndValidatorTable
 * @date 2019/01/14 15:05
 * @description Redisson中锁住桌台，而且校验桌台是否被业务锁，锁住 需要将带有锁定字段的实体，作为函数的第一入参
 * 默认是tableGuid字段，如果在字段上打了@LockFiled注解会锁住该字段
 * @program holder-saas-store-table
 */

@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireBizAndRdsLock {
}
