package com.holderzone.holder.saas.aggregation.merchant.controller.takeout;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.takeout.TakeoutConsumerService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.takeout.TakeoutProducerService;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.SalesOrderDTO;
import com.holderzone.saas.store.dto.takeaway.request.SalesUpdateDTO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/takeout/callback/own")
public class OwnController {

    private final TakeoutProducerService takeoutProducerService;

    private final TakeoutConsumerService takeoutConsumerService;

    @Autowired
    public OwnController(TakeoutProducerService takeoutProducerService, TakeoutConsumerService takeoutConsumerService) {
        this.takeoutProducerService = takeoutProducerService;
        this.takeoutConsumerService = takeoutConsumerService;
    }

    @PostMapping("/order/save")
    @ApiOperation(value = "掌控者订单接收", notes = "掌控者订单接收")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "美团订单回调")
    public OwnCallbackResponse callbackOrder(@RequestBody @Validated SalesOrderDTO salesOrderDTO) {
        if (log.isInfoEnabled()) {
            log.info("掌控者订单接收：{}", JacksonUtils.writeValueAsString(salesOrderDTO));
        }
        return takeoutProducerService.orderCallback(salesOrderDTO);
    }

    @PostMapping("/order/update")
    @ApiOperation(value = "掌控者订单状态变更", notes = "掌控者订单状态变更")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "美团订单回调")
    public OwnCallbackResponse orderUpdate(@RequestBody @Validated SalesUpdateDTO salesUpdateDTO) {
        if (log.isInfoEnabled()) {
            log.info("掌控者订单状态变更,入参：{}", JacksonUtils.writeValueAsString(salesUpdateDTO));
        }
        return takeoutConsumerService.orderUpdate(salesUpdateDTO);
    }
}
