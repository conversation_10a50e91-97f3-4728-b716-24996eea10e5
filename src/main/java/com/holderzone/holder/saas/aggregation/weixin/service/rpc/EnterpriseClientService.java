package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.weixin.entity.dto.OrganizationNewDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.enterprise.OrganizationQueryDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.weixin.MultiMemberDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EnterpriseClientService
 * @date 2019/04/11 10:00
 * @description 企业信息ClientService
 * @program holder-saas-store
 */
@Component
@FeignClient(name = "holder-saas-cloud-enterprise", fallbackFactory = EnterpriseClientService.EnterpriseFallBack.class)
public interface EnterpriseClientService {

    @PostMapping("/enterprise/find")
    EnterpriseDTO findEnterprise(BaseDTO baseDTO);

    @GetMapping("/multi/member/list")
    List<MultiMemberDTO> list(@RequestBody Set<String> multiMemberGuid);

    /***
     *  根据条件查询组织
     * @param organizationQueryDTO
     * @return
     */
    @PostMapping("/organization/store/platform/page")
    Page<OrganizationNewDTO> pageQueryAllPlatformStore(@RequestBody OrganizationQueryDTO organizationQueryDTO);

    @Component
    @Slf4j
    class EnterpriseFallBack implements FallbackFactory<EnterpriseClientService> {
        private static final Logger logger = LoggerFactory.getLogger(EnterpriseFallBack.class);
        @Override
        public EnterpriseClientService create(Throwable throwable) {
            return new EnterpriseClientService() {
                @Override
                public EnterpriseDTO findEnterprise(BaseDTO baseDTO) {
                    return null;
                }

                @Override
                public List<MultiMemberDTO> list(Set<String> multiMemberGuid) {
                    logger.error("查询运营主体失败，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Page<OrganizationNewDTO> pageQueryAllPlatformStore(OrganizationQueryDTO organizationQueryDTO) {
                    logger.error("根据条件查询组织失败，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }
            };
        }
    }

}
