package com.holderzone.holder.saas.aggregation.weixin.service;

import com.holderzone.saas.store.dto.member.hsm.HsmRechargeReqDTO;
import com.holderzone.saas.store.dto.pay.SaasNotifyDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;

/**
 * <AUTHOR>
 * @date 2020/8/13 15:18
 * @description
 */
public interface HsmMemberService {
    String callback(SaasNotifyDTO saasNotifyDTO);
    WxPayRespDTO wechatRecharge(HsmRechargeReqDTO hsmRechargeReqDTO);
}
