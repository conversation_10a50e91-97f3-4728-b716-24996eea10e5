package com.holderzone.holder.saas.aggregation.merchant.controller.member.activity;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.activity.VolumeInfoClientService;
import com.holderzone.holder.saas.aggregation.merchant.util.DateUtil;
import com.holderzone.holder.saas.member.dto.activity.common.VolumeInfoCommonRespDTO;
import com.holderzone.holder.saas.member.dto.activity.request.*;
import com.holderzone.holder.saas.member.dto.activity.response.*;
import com.holderzone.holder.saas.member.dto.rights.request.QueryVolumeDTO;
import com.holderzone.holder.saas.member.enums.common.IsDelTypeEnum;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;

/**
 * <AUTHOR>
 * @description  优惠劵
 * @date 2019/5/20 17:34
 */
@RestController
@RequestMapping("/hsm-volume-info")
@Api(description = "优惠劵")
public class HsmVolumeInfoController {
    @Autowired
    private VolumeInfoClientService volumeInfoClientService;
    /**
     * @Description 查询充值下的选项优惠劵
     * <AUTHOR>
     * @Date  2019/5/20 17:43
     * @param
     * @return com.holderzone.framework.response.Result
     */
    @ApiOperation(value = "查询充值下的选项优惠劵", notes = "查询充值下的选项优惠劵",response= VolumeInfoCommonRespDTO.class)
    @GetMapping(value = "/queryOfRechargeSelected", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "查询充值下的选项优惠劵")
    public Result queryOfRechargeSelected(QueryVolumeDTO queryVolumeDTO){

         return Result.buildSuccessResult(volumeInfoClientService.queryOfRechargeSelected(queryVolumeDTO));
    }

    /**
     * @return com.holderzone.framework.response.Result
     * @Description 查询有效期为第三类优惠券
     * <AUTHOR>
     * @Date 2019/5/20 17:43
     */
    @ApiOperation(value = "查询有效期为第三类优惠券", notes = "查询有效期为第三类优惠券", response = VolumeInfoCommonRespDTO.class)
    @GetMapping(value = "/query/third/validity", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "查询有效期为第三类优惠券")
    public Result queryForThirdTime(QueryVolumeDTO queryVolumeDTO) {

        return Result.buildSuccessResult(volumeInfoClientService.queryForThirdTime(queryVolumeDTO));
    }
    /**
     * @Description 根据guid查询
     * <AUTHOR>
     * @Date  2019/5/20 17:43
     * @param
     * @return com.holderzone.framework.response.Result
     */
    @ApiOperation(value = "根据guid查询", notes = "根据guid查询",response = HsmVolumeInfoupdateRespDTO.class)
    @GetMapping(value = "/{guid}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "guid", value = "guid", required = true, dataType = "String")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "根据guid查询")
    public Result queryByGuid(@PathVariable("guid") String guid){

        return Result.buildSuccessResult(volumeInfoClientService.queryByGuid(guid));
    }

    /**
     * 根据guid 查询商品券
     *
     * @return com.holderzone.holder.saas.member.dto.activity.response.HsmVolumeInfoupdateRespDTO
     * @Param [guid]
     */
    @ApiOperation(value = "根据guid查询商品券", notes = "根据guid查询商品券")
    @GetMapping(value = "product/{guid}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "guid", value = "guid", required = true, dataType = "String")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "根据guid查询商品券")
    public Result<HsmProductVolumeInfoUpdateRespDTO> queryProductByGuid(@PathVariable("guid") String guid) {
        return Result.buildSuccessResult(volumeInfoClientService.queryProductByGuid(guid));
    }

    /**
     * @Description 查询优惠劵列表
     * <AUTHOR>
     * @Date  2019/5/22 10:26
     * @param hsmVolumeInfoReqDTO
     * @return com.holderzone.framework.response.Result
     */
    @ApiOperation(value = "优惠劵列表", notes = "优惠劵列表,返回为分页page",response=HsmVolumeInfoRespDTO.class)
    @GetMapping(value = "/queryList", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "优惠劵列表")
    public Result queryList(HsmVolumeInfoReqDTO hsmVolumeInfoReqDTO){
        hsmVolumeInfoReqDTO.setCurrDateStr(DateUtil.format(new Date(),DateUtil.DATE_FORMAT));
        hsmVolumeInfoReqDTO.setIsDel(IsDelTypeEnum.IS_DEL_TYPE_NO.getCode());
        return Result.buildSuccessResult(volumeInfoClientService.queryList(hsmVolumeInfoReqDTO));
    }
    /**
     * @Description  优惠劵新增
     * <AUTHOR>
     * @Date  2019/5/24 9:36
     * @param hsmVolumeInfoUpdateRespDTO
     * @return com.holderzone.framework.response.Result
     */
    @ApiOperation(value = "优惠劵新增", notes = "优惠劵新增")
    @PostMapping(value = "/save",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "优惠劵新增")
    public Result save(@Validated @RequestBody HsmVolumeInfoUpdateReqDTO hsmVolumeInfoUpdateRespDTO){

        return Result.buildSuccessResult(volumeInfoClientService.save(hsmVolumeInfoUpdateRespDTO));
    }

    /**
     * @param hsmProductVolumeInfoUpdateReqDTO
     * @return boolean
     * @Description 保存商品券
     * <AUTHOR>
     * @Date 2019/5/24 9:40
     */
    @ApiOperation(value = "商品券保存", notes = "商品券保存")
    @PostMapping(value = "/product/save", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "商品券保存")
    public Result productSave(@Validated @RequestBody HsmProductVolumeInfoUpdateReqDTO hsmProductVolumeInfoUpdateReqDTO) {

        return Result.buildSuccessResult(volumeInfoClientService.productSave(hsmProductVolumeInfoUpdateReqDTO));
    }

    /**
     * @Description  优惠劵新增或更新
     * <AUTHOR>
     * @Date  2019/5/24 9:36
     * @param hsmVolumeInfoUpdateReqDTO
     * @return com.holderzone.framework.response.Result
     */
    @ApiOperation(value = "优惠劵更新", notes = "优惠劵更新")
    @PutMapping(value = "/update",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "优惠劵更新")
    public Result update(@Validated({HsmVolumeInfoUpdateReqDTO.update.class, HsmVolumeStoreDTO.update.class})@RequestBody HsmVolumeInfoUpdateReqDTO hsmVolumeInfoUpdateReqDTO){

        return Result.buildSuccessResult(volumeInfoClientService.update(hsmVolumeInfoUpdateReqDTO));
    }

    /**
     * 更新商品券
     *
     * @return boolean
     * @Param [hsmVolumeInfoUpdateRespDTO]
     */
    @ApiOperation(value = "商品券更新", notes = "商品券更新")
    @PutMapping(value = "product/update", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "商品券更新")
    public Result productUpdate(@RequestBody @Validated({HsmProductVolumeInfoUpdateReqDTO.update.class}) HsmProductVolumeInfoUpdateReqDTO hsmProductVolumeInfoUpdateReqDTO) {

        return Result.buildSuccessResult(volumeInfoClientService.productVolumeUpdate(hsmProductVolumeInfoUpdateReqDTO));
    }

    /**
     * @Description  优惠劵操作 操作类型 1 启动发放 2 停止发放 3 作废 4 删除
     * <AUTHOR>
     * @Date  2019/5/24 9:36
     * @return com.holderzone.framework.response.Result
     */
    @ApiOperation(value = "优惠劵操作", notes = "优惠劵操作 操作类型 1 启动发放 2 停止发放 3 作废 4 删除")
    @PutMapping(value = "/{guid}/{operateType}",produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParams({@ApiImplicitParam(name = "guid", value = "guid", required = true, dataType = "String"),
            @ApiImplicitParam(name = "operateType", value = "1 启动发放 2 停止发放 3 作废 4 删除", required = true, dataType = "String")
    })
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "优惠劵操作")
    public Result operateByGuidAndOperateType(@PathVariable("guid") String guid,@PathVariable("operateType") Integer operateType ){

        return Result.buildSuccessResult(volumeInfoClientService.operateByGuidAndOperateType(guid,operateType));
    }

    @ApiOperation(value = "优惠券加券")
    @GetMapping("/plus/{guid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "优惠券加券")
    public Result<Boolean> volumePlus(@ApiParam("优惠券Guid") @PathVariable("guid") String guid, @ApiParam("优惠券加券数量") @RequestParam("plusNum") Integer plusNum){
        return Result.buildSuccessResult(volumeInfoClientService.volumePlus(guid,plusNum));
    }
    /**
     * @Description 优惠劵详情
     * <AUTHOR>
     * @Date  2019/5/27 9:57
     * @param volumeDetailReqDTO
     * @return com.holderzone.framework.response.Result
     */
    @ApiOperation(value = "优惠劵详情", notes = "优惠劵详情",response = VolumeDetailRespDTO.class)
    @GetMapping(value ="/volume/detail" ,produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "优惠劵详情")
    public Result queryByVolumeDetail(@ApiParam(value ="volumeDetailReqDTO" ) @Valid VolumeDetailReqDTO volumeDetailReqDTO){
        return Result.buildSuccessResult(volumeInfoClientService.queryByVolumeDetail(volumeDetailReqDTO));
    }

    /**
     * @Description 优惠券核销明细
     * <AUTHOR>
     * @Date 2019/8/14
     * @param volumeUseDetailReqDTO
     * @return com.holderzone.framework.response.Result
     */
    @ApiOperation(value = "优惠券核销明细", notes = "优惠券核销明细", response = VolumeUseDetailRespDTO.class)
    @PostMapping(value = "/volume/useDetail", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "优惠券核销明细")
    public Result queryUseDetail(@ApiParam(value = "volumeUseDetailReqDTO") @RequestBody @Valid VolumeUseDetailReqDTO volumeUseDetailReqDTO) {
        return Result.buildSuccessResult(volumeInfoClientService.queryUseDetail(volumeUseDetailReqDTO));
    }

    /**
     * @Description 优惠券核销明细金额合计
     * <AUTHOR>
     * @Date 2019/8/14
     * @param volumeUseDetailReqDTO
     * @return com.holderzone.framework.response.Result
     */
    @ApiOperation(value = "优惠券核销明细金额合计", notes = "优惠券核销明细金额合计", response = VolumeUseDetailRespDTO.class)
    @PostMapping(value = "/volume/useDetailAmount", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "优惠券核销明细金额合计")
    public Result sumUseDetailAmount(@ApiParam(value = "volumeUseDetailReqDTO") @RequestBody @Valid VolumeUseDetailReqDTO volumeUseDetailReqDTO) {
        return Result.buildSuccessResult(volumeInfoClientService.sumUseDetailAmount(volumeUseDetailReqDTO));
    }

    /**
     * @Description 优惠券发放明细
     * <AUTHOR>
     * @Date 2019/8/14
     * @param volumeSendDetailReqDTO
     * @return
     */
    @ApiOperation(value = "优惠券发放明细", notes = "优惠券发放明细", response = VolumeSendDetailRespDTO.class)
    @PostMapping(value = "/volume/sendDetail", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "优惠券发放明细")
    public Result querySendDetail(@ApiParam(value = "volumeSendDetailReqDTO") @RequestBody @Valid VolumeSendDetailReqDTO volumeSendDetailReqDTO) {
        return Result.buildSuccessResult(volumeInfoClientService.querySendDetail(volumeSendDetailReqDTO));
    }
}
