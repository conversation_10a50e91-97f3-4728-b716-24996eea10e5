package com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.order;


import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.report.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(value = "holder-saas-store-report")
public interface IndexService {

    /**
     * 收入概览
     * @param query
     * @return
     */
    @PostMapping("/index/queryInComeOverview")
    IndexInComeOverviewDTO queryInComeOverview(@RequestBody IndexQuery query);

    /**
     * 顾客概览
     * @param query
     * @return
     */
    @PostMapping("/index/queryCustomerOverview")
    IndexCustomerDTO queryCustomerOverview(@RequestBody IndexQuery query);

    /**
     * 门店收入排名
     * @param query
     * @return
     */
    @PostMapping("/index/queryStoreSore")
    Page<IndexStoreSortDTO> queryStoreSore(@RequestBody IndexQuery query);

    /**
     *订单概览
     * @param indexQuery
     * @return
     */
    @PostMapping(value = "/index/orderOverview")
    IndexOrderOverviewDto orderOverview(@RequestBody IndexQuery indexQuery);
}
