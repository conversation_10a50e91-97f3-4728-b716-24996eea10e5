package com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin;

import com.holderzone.saas.store.dto.weixin.open.WxOpenAuthDTO;
import com.holderzone.saas.store.dto.weixin.req.WxCommonReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPreCodReqDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreComponentConfigClientService
 * @date 2019/02/26 11:37
 * @description //TODO
 * @program ${MODULE_NAME}
 */
@FeignClient(value = "holder-saas-store-weixin", fallbackFactory = WxStoreComponentConfigClientService.FallBackService.class)
public interface WxStoreComponentConfigClientService {
    @PostMapping("/wx_open/receive_ticket")
    String receiveTicket(WxCommonReqDTO wxCommonReqDTO);

    @PostMapping("/wx_open/get_pre_code")
    String getPreCode(WxPreCodReqDTO wxPreCodReqDTO);

    @PostMapping("/wx_open/query_auth")
    String queryAuth(WxOpenAuthDTO wxOpenAuthDTO);

    @Component
    @Slf4j
    class FallBackService implements FallbackFactory<WxStoreComponentConfigClientService> {

        @Override
        public WxStoreComponentConfigClientService create(Throwable throwable) {
            return new WxStoreComponentConfigClientService() {
                @Override
                public String receiveTicket(WxCommonReqDTO wxCommonReqDTO) {
                    log.error("微信聚合层熔断，处理component_verify_ticket异常，exception：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public String getPreCode(WxPreCodReqDTO wxPreCodReqDTO) {
                    log.error("微信聚合层熔断，处理pre_code异常，exception：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public String queryAuth(WxOpenAuthDTO wxOpenAuthDTO) {
                    log.error("微信聚合层熔断，处理pre_code异常，exception：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }
            };
        }
    }
}
