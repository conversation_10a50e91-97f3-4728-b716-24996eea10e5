package com.holderzone.holder.saas.aggregation.merchant.controller.rights;


import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.rights.HsmRCardLevelRightsService;
import com.holderzone.holder.saas.member.dto.rights.request.BirthRightReqDTO;
import com.holderzone.holder.saas.member.dto.rights.request.DiscountRightReqDTO;
import com.holderzone.holder.saas.member.dto.rights.request.UpgradeRightReqDTO;
import com.holderzone.holder.saas.member.dto.rights.response.BaseRightRespDTO;
import com.holderzone.holder.saas.member.enums.rights.RightsTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 卡等级权益关联表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-17
 */
@RestController
@RequestMapping("/hsm/card/level/rights")
@Api(description = "会员权益")
public class HsmRCardLevelRightsController {


    @Resource
    private HsmRCardLevelRightsService iHsmRCardLevelRightsService;

    /**
     * 根据关系Guid和权益类型删除会员权益
     *
     * @param relationGuid 关系Guid
     * @param typeCode 权益类型
     */
    @ApiOperation(value = "删除权益", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @DeleteMapping("/{relationGuid}/{typeCode}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "删除权益")
    public Result delRight(
        @ApiParam(value = "关系ID", required = true) @PathVariable("relationGuid") String relationGuid,
        @ApiParam(value = "权益类型", required = true) @PathVariable("typeCode") String typeCode) {
        iHsmRCardLevelRightsService.delRight(relationGuid, typeCode);
        return Result.buildEmptySuccess();
    }


    /**
     * 根据关系Guid和权益类型获取权益详情
     *
     * @param relationGuid 关系Guid
     * @param typeCode 权益类型
     * @return 权益类型
     */
    @ApiOperation(value = "获取权益的详情值", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, response = BaseRightRespDTO.class)
    @GetMapping("/{relationGuid}/{typeCode}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "获取权益的详情值")
    public Result findRightDetail(
        @ApiParam(value = "关系ID", required = true) @PathVariable("relationGuid") String relationGuid,
        @ApiParam(value = "权益类型", required = true) @PathVariable("typeCode") String typeCode) {
        RightsTypeEnum rightsTypeEnum = RightsTypeEnum.findByCodeStr(typeCode);
        if (rightsTypeEnum == null) {
            return Result.buildFailResult(-1, "错误的权益类型");
        }
        switch (rightsTypeEnum) {
            case DISCOUNT:
                return Result
                    .buildSuccessResult(
                        iHsmRCardLevelRightsService.findDiscountRightDetail(relationGuid));
            case MEMBERSHIP_UPGRADE_COUPON_DELIVERY:
                return Result
                    .buildSuccessResult(
                        iHsmRCardLevelRightsService.findUpgradeRightDetail(relationGuid));
            case BIRTHDAY_PRIVILEGE:
                return Result
                    .buildSuccessResult(
                        iHsmRCardLevelRightsService.findBirthRightDetail(relationGuid));
            default:
                return Result.buildFailResult(-1, "错误的权益类型");
        }

    }

    /**
     * 更新或者保存升级权益
     *
     * @param upgradeRightReqDTO 升级权益
     */
    @ApiOperation(value = "保存/更新升级权益", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/upgrade")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "保存/更新升级权益")
    public Result saveOrUpdateUpgradeRight(
        @ApiParam(value = "升级权益", required = true) @Validated @RequestBody UpgradeRightReqDTO upgradeRightReqDTO) {
        iHsmRCardLevelRightsService.saveOrUpdateUpgradeRight(upgradeRightReqDTO);
        return Result.buildEmptySuccess();
    }

    /**
     * 更新或者保存折扣权益
     *
     * @param discountRightReqDTO 折扣权益
     */
    @ApiOperation(value = "保存/更新折扣权益", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/discount")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "保存/更新折扣权益")
    public Result saveOrUpdateDiscountRight(
        @ApiParam(value = "折扣权益", required = true) @Validated @RequestBody DiscountRightReqDTO discountRightReqDTO) {
        iHsmRCardLevelRightsService.saveOrUpdateDiscountRight(discountRightReqDTO);
        return Result.buildEmptySuccess();
    }

    /**
     * 更新或者保存生日权益
     *
     * @param birthRightReqDTO 生日权益
     */
    @ApiOperation(value = "保存/更新生日权益", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/birth")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "保存/更新生日权益")
    public Result saveOrUpdateBirthRight(
        @ApiParam(value = "生日权益", required = true) @Validated @RequestBody BirthRightReqDTO birthRightReqDTO) {
        iHsmRCardLevelRightsService.saveOrUpdateBirthRight(birthRightReqDTO);
        return Result.buildEmptySuccess();
    }


}
