package com.holderzone.holder.saas.aggregation.app.controller.business;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.business.SystemDiscountClientService;
import com.holderzone.saas.store.dto.trade.SystemDiscountDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SystemDiscountController
 * @date 2018/09/10 16:20
 * @description
 * @program holder-saas-aggregation-app
 */
@Api(value = "系统折扣接口")
@RestController
@RequestMapping("/system")
public class SystemDiscountController {

    private static final Logger logger = LoggerFactory.getLogger(SystemDiscountController.class);

    private final SystemDiscountClientService systemDiscountClientService;

    @Autowired
    public SystemDiscountController(SystemDiscountClientService systemDiscountClientService) {
        this.systemDiscountClientService = systemDiscountClientService;
    }

    @ApiOperation(value = "新增系统折扣")
    @PostMapping("/add")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "新增系统折扣",action = OperatorType.ADD)
    public Result<String> addSystemDiscount(@RequestBody SystemDiscountDTO systemDiscountDTO) {
        logger.info("新增系统省零配置，systemDiscountDTO={}", JacksonUtils.writeValueAsString(systemDiscountDTO));
        return Result.buildSuccessResult(systemDiscountClientService.addSystemDiscount(systemDiscountDTO));
    }

    @ApiOperation(value = "查询系统折扣")
    @PostMapping("/getAll/{storeGuid}")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "查询系统折扣",action = OperatorType.SELECT)
    public Result<List<SystemDiscountDTO>> queryAll(@PathVariable("storeGuid") String storeGuid) {
        logger.info("查询系统省零，storeGuid={}", storeGuid);
        return Result.buildSuccessResult(systemDiscountClientService.queryAll(storeGuid));
    }

    @ApiOperation(value = "更新")
    @PostMapping("/update")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "更新系统省零配置",action = OperatorType.UPDATE)
    public Result<String> update(@RequestBody SystemDiscountDTO systemDiscountDTO) {
        logger.info("更新系统省零配置失败，systemDiscountDTO={}", JacksonUtils.writeValueAsString(systemDiscountDTO));
        return Result.buildSuccessResult(systemDiscountClientService.update(systemDiscountDTO));
    }

    @ApiOperation(value = "删除")
    @PostMapping("/delete/{storeGuid}/{systemDiscountGuid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "删除系统省零规则",action = OperatorType.DELETE)
    public Result<String> delete(@PathVariable("storeGuid") String storeGuid, @PathVariable("systemDiscountGuid") String systemDiscountGuid) {
        logger.info("删除系统省零规则，storeGuid={}，systemDiscountGuid={}", storeGuid, systemDiscountGuid);
        return Result.buildSuccessResult(systemDiscountClientService.delete(storeGuid, systemDiscountGuid));
    }

}
