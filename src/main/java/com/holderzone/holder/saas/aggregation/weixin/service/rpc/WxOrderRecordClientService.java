package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.weixin.entry.dto.OrderDetailDTO;
import com.holderzone.holder.saas.weixin.entry.dto.req.WxOrderDetailReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreCallbackNotifyDTO;
import com.holderzone.saas.store.dto.weixin.req.WxBrandUserOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxBrandUserOrderDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreTradeOrderDetailsRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreUserOrderDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxOrderRecordClientService
 * @date 2019/4/8
 */
@Component
@FeignClient(name = "holder-saas-store-weixin"
		//,url = "http://**************:8920"
		,fallbackFactory = WxOrderRecordClientService.WxOrderRecordFallBack.class)
public interface WxOrderRecordClientService {

	String URL_PREFIX = "/wx_store_order_record";

	@ApiOperation("/更新用户订单记录")
	@PostMapping(value =URL_PREFIX+ "/update")
	boolean updateWxOrderRecord(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	@ApiOperation("删除用户订单记录")
	@PostMapping(value=URL_PREFIX+"/del")
	void delWxOrderRecord(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	@PostMapping(value=	URL_PREFIX+"/user_order")
	WxStoreUserOrderDTO getWxStoreUserOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	@PostMapping(value="/wx_store_pay/result_operation")
	WxStoreTradeOrderDetailsRespDTO operation(WxStoreCallbackNotifyDTO wxStoreCallbackNotifyDTO);

	@PostMapping(value = URL_PREFIX+"/brand_user_order")
	WxBrandUserOrderDTO getWxBrandUserOrder(@RequestBody WxBrandUserOrderReqDTO wxBrandUserOrderReqDTO);
	
	@PostMapping(value = URL_PREFIX+"/detail")
	OrderDetailDTO getOrderDetail(@RequestBody WxOrderDetailReqDTO wxOrderRecordDTO);

	@PostMapping(value = URL_PREFIX+"/detail/calculate")
	Result<OrderDetailDTO> detailCalculate(@RequestBody WxOrderDetailReqDTO wxOrderRecordDTO);



	@PostMapping(value = URL_PREFIX+"/calculate/check")
    Result detailCalculateCheck(@RequestBody WxOrderDetailReqDTO orderDetailDTO);


    @Component
	@Slf4j
	class WxOrderRecordFallBack implements FallbackFactory<WxOrderRecordClientService> {


		@Override
		public WxOrderRecordClientService create(Throwable throwable) {
			return new WxOrderRecordClientService() {

				@Override
				public boolean updateWxOrderRecord(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
					log.error("远程调用服务失败，msg={}", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public void delWxOrderRecord(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
					log.error("远程调用服务失败，msg={}", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public WxStoreUserOrderDTO getWxStoreUserOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
					log.error("远程调用服务失败，msg={}", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public WxStoreTradeOrderDetailsRespDTO operation(WxStoreCallbackNotifyDTO wxStoreCallbackNotifyDTO) {
					log.error("远程调用服务失败，msg={}", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public WxBrandUserOrderDTO getWxBrandUserOrder(WxBrandUserOrderReqDTO wxBrandUserOrderReqDTO) {
					log.error("会员中心查询用户就餐列表失败:{}", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public OrderDetailDTO getOrderDetail(WxOrderDetailReqDTO wxOrderRecordDTO) {
					log.error("远程调用服务失败", throwable);
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public Result<OrderDetailDTO> detailCalculate(WxOrderDetailReqDTO wxOrderRecordDTO) {
					log.error("检验订单结算信息失败，Calculate error,message:{}", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());

				}

				@Override
				public Result detailCalculateCheck(WxOrderDetailReqDTO orderDetailDTO) {
					log.error("detailCalculateCheck结算状态失败:{}", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

			};
		}
	}
}
