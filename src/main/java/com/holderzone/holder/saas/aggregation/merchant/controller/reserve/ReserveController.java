package com.holderzone.holder.saas.aggregation.merchant.controller.reserve;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.aop.annotation.SecuretKeyValidate;
import com.holderzone.holder.saas.aggregation.merchant.service.ReserveService;
import com.holderzone.saas.store.dto.reserve.HWDTO;
import com.holderzone.saas.store.dto.reserve.HWReserveRecordDTO;
import com.holderzone.saas.store.dto.reserve.MerchantPhoneDTO;
import com.holderzone.saas.store.reserve.api.ReserveConfigApi;
import com.holderzone.saas.store.reserve.api.dto.ReserveConfigDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveConfigSyncDTO;
import com.holderzone.saas.store.reserve.api.dto.StoreGuidDTO;
import com.holderzone.saas.store.reserve.api.dto.StoreRoomDTO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveController
 * @date 2019/05/09 10:00
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Slf4j
@RestController
public class ReserveController {

    @Autowired
    ReserveConfigApi reserveConfigApi;

    @Autowired
    ReserveService reserveService;

    @ApiOperation("保存配置")
    @PostMapping("/reserve/config/insert")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_RESERVE,description = "保存配置")
    Result<ReserveConfigDTO> insert(@RequestBody ReserveConfigDTO dto) {
        log.info("保存配置,入参{}", dto);
        return Result.buildSuccessResult(reserveConfigApi.insert(dto));
    }

    @ApiOperation("修改配置")
    @PostMapping("/reserve/config/update")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_RESERVE,description = "修改配置")
    Result<ReserveConfigDTO> update(@RequestBody ReserveConfigDTO dto) {
        log.info("修改配置,入参{}", dto);
        return Result.buildSuccessResult(reserveConfigApi.update(dto));
    }

    @ApiOperation("查询配置")
    @PostMapping("/reserve/config")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_RESERVE,description = "查询配置")
    Result<ReserveConfigDTO> query(@RequestBody StoreGuidDTO storeGuid) {
        log.info("查询配置,入参{}", storeGuid);
        return Result.buildSuccessResult(reserveConfigApi.query(storeGuid));
    }

    @ApiOperation("同步门店配置")
    @PostMapping("/reserve/config/sync")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_RESERVE, description = "同步门店配置")
    Result<ReserveConfigDTO> sync(@RequestBody ReserveConfigSyncDTO reserveConfigSyncDTO) {
        log.info("同步门店配置,入参{}", JacksonUtils.writeValueAsString(reserveConfigSyncDTO));
        reserveConfigApi.syncConfig(reserveConfigSyncDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation("发起预定")
    @SecuretKeyValidate
    @PostMapping("/reserve/launch")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_RESERVE,description = "发起预定")
    public com.holderzone.saas.store.dto.reserve.Result launch(@RequestBody HWDTO<HWReserveRecordDTO> hwReserveRecordDTO) {
        try {
            reserveService.launch(hwReserveRecordDTO.getParams());
        } catch (Exception e) {
            return new com.holderzone.saas.store.dto.reserve.Result(1, e.getMessage());
        }
        return new com.holderzone.saas.store.dto.reserve.Result(0, "成功");
    }

    @ApiOperation("发起预定")
    @SecuretKeyValidate
    @PostMapping("/reserve/merchant/validate")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_RESERVE,description = "发起预定")
    public com.holderzone.saas.store.dto.reserve.Result validate(@RequestBody HWDTO<MerchantPhoneDTO> hwReserveRecordDTO) {
        try {
            reserveService.validate(hwReserveRecordDTO.getParams());
        } catch (Exception e) {
            return new com.holderzone.saas.store.dto.reserve.Result(1, e.getMessage());
        }
        return new com.holderzone.saas.store.dto.reserve.Result(0, "成功");

    }

    @ApiOperation("查询包间")
    @PostMapping("/reserve/room/query")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_RESERVE,description = "查询包间")
    public Result<List<String>> queryRoom(@RequestBody StoreGuidDTO storeGuid) {
        return Result.buildSuccessResult(reserveConfigApi.queryRoom(storeGuid));
    }

    @ApiOperation("保存包间")
    @PostMapping("/reserve/room/save")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_RESERVE,description = "保存包间")
    public Result saveRoom(@RequestBody StoreRoomDTO storeRoom) {
        reserveConfigApi.saveRoom(storeRoom);
        return Result.buildEmptySuccess();
    }
}