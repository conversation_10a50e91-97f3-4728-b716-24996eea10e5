package com.holderzone.holder.saas.aggregation.app.service.feign.business;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.trade.PaymentTypeDTO;
import com.holderzone.saas.store.dto.trade.PaymentTypeQueryDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentTypeClientService
 * @date 2018/09/10 16:09
 * @description
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-store-business", fallbackFactory = PaymentTypeClientService.PaymentTypeFallBack.class)
public interface PaymentTypeClientService {

    @PostMapping("pay/type/add")
    String add(@RequestBody PaymentTypeDTO paymentTypeDTO);

    @PostMapping("pay/type/update")
    String update(@RequestBody PaymentTypeDTO paymentTypeDTO);

    @PostMapping("pay/type/sort")
    String sort(@RequestBody List<PaymentTypeDTO> paymentTypeDTOS);

    @PostMapping("pay/type/delete")
    String deletePaymentType(@RequestBody PaymentTypeDTO paymentTypeDTO);

    @PostMapping("pay/type/getAll/android")
    List<PaymentTypeDTO> getAll(@RequestBody PaymentTypeQueryDTO paymentTypeQueryDTO);

    @Component
    class PaymentTypeFallBack implements FallbackFactory<PaymentTypeClientService> {

        private static final Logger logger = LoggerFactory.getLogger(PaymentTypeFallBack.class);

        @Override
        public PaymentTypeClientService create(Throwable throwable) {
            return new PaymentTypeClientService() {
                @Override
                public String add(PaymentTypeDTO paymentTypeDTO) {
                    logger.error("新增支付方式异常 e={}", throwable.getMessage());
                    throw new BusinessException("新增支付方式异常" + throwable.getMessage());
                }

                @Override
                public String update(PaymentTypeDTO paymentTypeDTO) {
                    logger.error("修改支付方式异常 e={}", throwable.getMessage());
                    throw new BusinessException("修改支付方式异常" + throwable.getMessage());
                }

                @Override
                public String sort(List<PaymentTypeDTO> paymentTypeDTOS) {
                    logger.error("支付方式排序异常 e={}", throwable.getMessage());
                    throw new BusinessException("支付方式排序异常" + throwable.getMessage());
                }

                @Override
                public String deletePaymentType(PaymentTypeDTO paymentTypeDTO) {
                    logger.error("删除支付方式异常 e={}", throwable.getMessage());
                    throw new BusinessException("删除支付方式异常" + throwable.getMessage());
                }

                @Override
                public List<PaymentTypeDTO> getAll(PaymentTypeQueryDTO paymentTypeQueryDTO) {
                    logger.error("查询所有支付方式异常 e={}", throwable.getMessage());
                    throw new BusinessException("查询所有支付方式异常" + throwable.getMessage());
                }
            };
        }
    }

}
