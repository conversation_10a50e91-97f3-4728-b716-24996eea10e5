package com.holderzone.holder.saas.aggregation.merchant.util;

import com.holderzone.framework.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * 签名工具
 */
@Slf4j
public class SignatureUtil {

    private SignatureUtil() {
    }

    private static final String KEY_API_KEY = "apiKey";
    private static final String KEY_APP_SECRET = "apiSecret";

    public static final String KEY_SIGNATURE = "signature";

    public static final String ENTERPRISE_GUID = "enterpriseGuid";

    public static final String KEY_NOW = "now";

    public static String getSignature(String apiKey, String apiSecret, Map<String, Object> map) {
        if (null != map) {
            map.remove(KEY_SIGNATURE);
        }
        if (map != null) {
            Map<String, Object> filteredSortedMap = map.entrySet().stream()
                    .filter(stringObjectEntry -> validate(stringObjectEntry.getValue()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                            (u, v) -> {
                                throw new IllegalStateException(String.format("Duplicate key %s", u));
                            }, TreeMap::new)
                    );
            // 参数拼接
            String joinedParam = filteredSortedMap.entrySet().stream()
                    .map(stringObjectEntry -> String.format("%s=%s", stringObjectEntry.getKey(), stringObjectEntry.getValue()))
                    .collect(Collectors.joining("&"));
            // appSecret拼接
            String joinedParamWithKey = String.format("%s&%s=%s&%s=%s", joinedParam, KEY_API_KEY, apiKey, KEY_APP_SECRET, apiSecret);
            // 我方计算signature
            return DigestUtils.sha1Hex(joinedParamWithKey.getBytes());
        }
        return "";
    }

    private static boolean validate(Object object) {
        return object != null && (!(object instanceof String) || StringUtils.hasText((String) object));
    }
}
