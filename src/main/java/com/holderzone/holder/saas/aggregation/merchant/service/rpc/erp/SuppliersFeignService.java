package com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.SuppliersDTO;
import com.holderzone.saas.store.dto.erp.SuppliersQueryDTO;
import com.holderzone.saas.store.dto.erp.SuppliersReqDTO;
import feign.hystrix.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @className SuppliersFeignService
 * @date 2019-05-05 14:09:27
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-store-erp", fallbackFactory = SuppliersFeignService.SuppliersFeignServiceFallback.class)
public interface SuppliersFeignService {

    /**
     * 新建供应商
     */
    @PostMapping("/suppliers")
    String createSuppliers(@RequestBody SuppliersReqDTO reqDTO);

    /**
     * 更新供应商
     */
    @PutMapping("/suppliers")
    String updateSuppliers(@RequestBody SuppliersReqDTO reqDTO);

    /**
     * 查询供应商信息
     */
    @GetMapping("/suppliers/{guid}")
    SuppliersDTO getSuppliersByGuid(@PathVariable("guid") String guid);

    /**
     * 启禁用供应商
     */
    @PutMapping("/suppliers/enable/{guid}")
    Boolean enableOrDisableSuppliers(@PathVariable("guid") String guid);

    /**
     * 删除供应商
     */
    @DeleteMapping("/suppliers/{guid}")
    Boolean deleteSuppliers(@PathVariable("guid") String guid);

    /**
     * 供应商列表
     */
    @PostMapping("/suppliers/query/list")
    Page<SuppliersDTO> getSuppliersList(@RequestBody SuppliersQueryDTO queryDTO);

    /**
     * 供应商下拉列表
     */
    @PostMapping("/suppliers/query/all")
    List<SuppliersDTO> getAllOfSuppliersList(@RequestBody SuppliersQueryDTO queryDTO);

    @Component
    class SuppliersFeignServiceFallback implements FallbackFactory<SuppliersFeignService> {

        @Override
        public SuppliersFeignService create(Throwable throwable) {
            return new SuppliersFeignService() {
                @Override
                public String createSuppliers(SuppliersReqDTO reqDTO) {
                    return null;
                }

                @Override
                public String updateSuppliers(SuppliersReqDTO reqDTO) {
                    return null;
                }

                @Override
                public SuppliersDTO getSuppliersByGuid(String guid) {
                    return null;
                }

                @Override
                public Boolean enableOrDisableSuppliers(String guid) {
                    return null;
                }

                @Override
                public Boolean deleteSuppliers(String guid) {
                    return null;
                }

                @Override
                public Page<SuppliersDTO> getSuppliersList(SuppliersQueryDTO queryDTO) {
                    return null;
                }

                @Override
                public List<SuppliersDTO> getAllOfSuppliersList(SuppliersQueryDTO queryDTO) {
                    return null;
                }
            };
        }
    }
}
