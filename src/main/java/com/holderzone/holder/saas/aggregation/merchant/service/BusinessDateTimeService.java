package com.holderzone.holder.saas.aggregation.merchant.service;

import com.holderzone.saas.store.dto.journaling.req.BusinessDateTimeReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.BusinessDateTimeRespDTO;

public interface BusinessDateTimeService {

    /**
     * 根据 企业guid 获取营业日 今天，近七天，近30天 的开始和结束 日期时间
     * @param businessDateTimeReqDTO
     * @return
     */
    BusinessDateTimeRespDTO getBusinessDateTimeByStoreGuids(BusinessDateTimeReqDTO businessDateTimeReqDTO);

    /**
     * 根据 日期类型 获取营业日的 开始和结束 日期时间
     * @param businessDateTimeReqDTO
     * @return
     */
    BusinessDateTimeRespDTO getBusinessDateTime(BusinessDateTimeReqDTO businessDateTimeReqDTO);
}
