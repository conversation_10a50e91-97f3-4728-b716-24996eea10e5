package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.holder.saas.aggregation.app.service.MemberService;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.account.NewMemberInfoClientService;
import com.holderzone.saas.store.dto.common.BaseRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 聚合层会员实现类
 * @date 2021/9/22 16:12
 * @className: MemberServiceImpl
 */
@Slf4j
@Service
public class MemberServiceImpl implements MemberService {

    /**
     * holder-saas-member-terminal
     */
    private final NewMemberInfoClientService memberInfoClientService;

    public MemberServiceImpl(NewMemberInfoClientService memberInfoClientService) {
        this.memberInfoClientService = memberInfoClientService;
    }

    /**
     * 查询会员主体能否使用积分
     *
     * @param operSubjectGuid 运营主体Guid
     * @return 返回公共实体
     */
    @Override
    public BaseRespDTO queryMemberUseIntegral(String operSubjectGuid) {
        return memberInfoClientService.queryDeductionRule(operSubjectGuid);
    }
}
