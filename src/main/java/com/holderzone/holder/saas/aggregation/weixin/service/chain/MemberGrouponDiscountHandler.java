package com.holderzone.holder.saas.aggregation.weixin.service.chain;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.context.DiscountContext;
import com.holderzone.holder.saas.aggregation.weixin.helper.PriceCalculationUtils;
import com.holderzone.holder.saas.aggregation.weixin.utils.map.DiscountMAP;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.member.terminal.enums.VolumeTypeEnum;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.trade.DiscountDTO;
import com.holderzone.saas.store.enums.member.VolumeVerifyEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.util.BigDecimalUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 会员代金券
 */
@Component
@Slf4j
@AllArgsConstructor
public class MemberGrouponDiscountHandler extends DiscountHandler {

    private final PriceCalculationUtils priceCalculationUtils;

    @Override
    void dealDiscount(DiscountContext context) {
        if (context.isRejectDiscount()) {
            return;
        }
        // 代金券（营销活动）
        if (context.getVolumeCodeType() == VolumeTypeEnum.CASH_COUPON.getCcCode()) {
            // 会员价共享场景加回specialsMemberPrice
            if (context.isHasMember() && Boolean.TRUE.equals(context.getUseMemberDiscountFlag())) {
                context.getAllItems().forEach(itemDTO -> {
                    BigDecimal specialsMemberPrice = BigDecimalUtil.nonNullValue(itemDTO.getSpecialsMemberPrice());
                    itemDTO.setDiscountTotalPrice(itemDTO.getDiscountTotalPrice().add(specialsMemberPrice));
                });
            }
            // 返回可使用代金券的最终商品
            List<RequestDishInfo> dishInfoList = priceCalculationUtils.dealWithVolume(context, context.getAllItems());
            DiscountDTO memberGrouponDTO = context.getDiscountTypeMap().get(type());
            DiscountFeeDetailDTO memberGroupon = DiscountMAP.INSTANCE.discountDO2DiscountFeeDetailDTO(memberGrouponDTO);
            if (CollectionUtils.isNotEmpty(dishInfoList)) {
                log.info("会员优惠券最终菜品：{}", JacksonUtils.writeValueAsString(dishInfoList));
                // 设置每个商品的优惠金额
                priceCalculationUtils.dealwithVolumeByDiscount(context.getAllItems(), dishInfoList);
                singleItemDiscount(context.getDineInItemDTOMap(), memberGroupon, dishInfoList);
            } else {
                memberGroupon.setDiscountFee(BigDecimal.ZERO);
            }
            context.getDiscountFeeDetailDTOS().add(memberGroupon);
            BigDecimal orderSurplusFee = context.getCalculateOrderRespDTO().getOrderSurplusFee();
            context.getCalculateOrderRespDTO().setOrderSurplusFee(orderSurplusFee.subtract(memberGroupon
                    .getDiscountFee()));
            log.info("会员优惠券计算后订单剩余金额：{}，优惠金额：{}，会员优惠券计算后菜品：{}", orderSurplusFee,
                    memberGroupon.getDiscountFee(), JacksonUtils.writeValueAsString(context.getAllItems()));

            // 计算非限时特价场景优惠价格
            context.getDiscountRuleBO().setVerify(VolumeVerifyEnum.QUERY.getCode());
            List<RequestDishInfo> pureDishInfoList = priceCalculationUtils.dealWithVolume(context, context.getBeforeSpecialsItems());
            priceCalculationUtils.dealwithVolumeByDiscount(context.getBeforeSpecialsItems(), pureDishInfoList);
            Map<String, DineInItemDTO> dineInItemMap = context.getBeforeSpecialsItems().stream()
                    .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (key1, key2) -> key1));
            DiscountFeeDetailDTO pureMemberGroupon = new DiscountFeeDetailDTO();
            singleItemDiscount(dineInItemMap, pureMemberGroupon, dishInfoList);
            context.setMemberGrouponDiscount(pureMemberGroupon.getDiscountFee());
            context.getCalculateOrderRespDTO().setOrderSurplusFeeBySkipSpecials(orderSurplusFee
                    .subtract(pureMemberGroupon.getDiscountFee()));
            log.info("[非限时特价场景]计算后订单剩余金额：{}，代金券优惠价格：{}，会员优惠券计算后菜品：{}",
                    context.getCalculateOrderRespDTO().getOrderSurplusFeeBySkipSpecials(), pureMemberGroupon.getDiscountFee(),
                    JacksonUtils.writeValueAsString(context.getBeforeSpecialsItems()));
        }
    }

    @Override
    Integer type() {
        return DiscountTypeEnum.MEMBER_GROUPON.getCode();
    }
}
