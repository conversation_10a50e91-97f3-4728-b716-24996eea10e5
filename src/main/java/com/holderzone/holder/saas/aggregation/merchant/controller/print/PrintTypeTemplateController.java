package com.holderzone.holder.saas.aggregation.merchant.controller.print;


import com.google.common.collect.Lists;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.organization.OrganizationService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.print.PrintClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.user.UserFeignService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.SingleDataPageDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.print.type.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 打印分类模版聚合层
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Slf4j
@Api("打印分类模版聚合层")
@RestController
@AllArgsConstructor
@RequestMapping("/print_type_template")
public class PrintTypeTemplateController {

    private final PrintClientService printClientService;

    private final OrganizationService organizationService;

    /**
     * 创建打印分类模版
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建打印分类模版")
    public Result<Void> create(@RequestBody @Validated PrintTypeTemplateDTO createDTO) {
        log.info("[创建打印分类模版]入参,createDTO={}", JacksonUtils.writeValueAsString(createDTO));
        printClientService.create(createDTO);
        return Result.buildEmptySuccess();
    }

    /**
     * 查询打印分类模版列表
     */
    @PostMapping("/query_page")
    @ApiOperation(value = "查询打印分类模版列表", notes = "查询打印分类模版列表")
    public Result<Page<PrintTypeTemplateVO>> queryPage(@RequestBody SingleDataPageDTO query) {
        log.info("[查询打印分类模版列表]入参,query={}", JacksonUtils.writeValueAsString(query));
        return Result.buildSuccessResult(printClientService.queryPage(query));
    }

    /**
     * 查询打印分类模版详情
     */
    @PostMapping("/query_detail")
    @ApiOperation(value = "查询打印分类模版详情", notes = "查询打印分类模版详情")
    public Result<PrintTypeTemplateDetailDTO> queryDetail(@RequestBody @Validated SingleDataDTO query) {
        log.info("[查询打印分类模版详情]入参,query={}", JacksonUtils.writeValueAsString(query));
        return Result.buildSuccessResult(printClientService.queryDetail(query));
    }

    /**
     * 更新打印分类模版
     */
    @PostMapping("/modify")
    @ApiOperation(value = "更新打印分类模版", notes = "更新打印分类模版")
    public Result<Void> modify(@RequestBody @Validated PrintTypeTemplateDTO modifyDTO) {
        log.info("[更新打印分类模版]入参,modifyDTO={}", JacksonUtils.writeValueAsString(modifyDTO));
        printClientService.modify(modifyDTO);
        return Result.buildEmptySuccess();
    }

    /**
     * 启用/禁用打印分类模版
     */
    @PostMapping("/enable")
    @ApiOperation(value = "启用/禁用打印分类模版", notes = "启用/禁用打印分类模版")
    public Result<Boolean> enable(@RequestBody @Validated PrintTypeTemplateEnableDTO enableDTO) {
        log.info("[启用/禁用打印分类模版]入参,enableDTO={}", JacksonUtils.writeValueAsString(enableDTO));
        return Result.buildSuccessResult(printClientService.enable(enableDTO));
    }

    /**
     * 删除打印分类模版
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除打印分类模版", notes = "删除打印分类模版")
    public Result<Boolean> delete(@RequestBody @Validated SingleDataDTO deleteDTO) {
        log.info("[删除打印分类模版]入参,enableDTO={}", JacksonUtils.writeValueAsString(deleteDTO));
        return Result.buildSuccessResult(printClientService.delete(deleteDTO));
    }

    @PostMapping("/query_spinner_store_by_brand")
    @ApiOperation(value = "查询打印模版能用的门店")
    public Result<List<StoreDTO>> getAvailableStores(@RequestBody @Validated TemplateStoreQO query) {
        log.info("[查询打印模版能用的门店]入参,query={}", JacksonUtils.writeValueAsString(query));
        List<StoreDTO> storeDTOList = organizationService.queryStoreByBrandList(Lists.newArrayList(query.getBrandGuid()));

        // 查询品牌下已经配置的门店
        List<String> setGuidList = printClientService.queryStoreByBrand(query);
        log.info("[查询打印模版能用的门店]setGuidList={}", setGuidList);
        if (CollectionUtils.isNotEmpty(setGuidList)) {
            storeDTOList.removeIf(e -> setGuidList.contains(e.getGuid()));
        }

        // 关键字搜索
        if (CollectionUtils.isNotEmpty(storeDTOList) && StringUtils.hasText(query.getKeywords())) {
            storeDTOList.removeIf(s -> !s.getName().contains(query.getKeywords()));
        }
        return Result.buildSuccessResult(storeDTOList);
    }

    /**
     * 校验模版名称
     */
    @PostMapping("/check_template_name")
    @ApiOperation(value = "校验模版名称", notes = "校验模版名称")
    public Result<Boolean> checkTemplateName(@RequestBody PrintTypeTemplateDTO query) {
        log.info("[校验模版名称]入参,query={}", JacksonUtils.writeValueAsString(query));
        return Result.buildSuccessResult(printClientService.checkTemplateName(query));
    }

}
