package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.config.WeChatConfig;
import com.holderzone.holder.saas.aggregation.app.service.WeChatService;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.account.NewMemberInfoClientService;
import com.holderzone.holder.saas.aggregation.app.utils.HttpsClientUtils;
import com.holderzone.saas.store.dto.common.RedisReqDTO;
import com.holderzone.saas.store.dto.member.response.PadLoginMemberRespDTO;
import com.holderzone.saas.store.dto.weixin.auth.WeChatAuthParamDTO;
import com.holderzone.saas.store.dto.weixin.auth.WeChatUserInfoDTO;
import com.holderzone.saas.store.enums.member.LoginStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.holderzone.holder.saas.aggregation.app.constant.OrderRedisConstant.WE_CHAT_USER_INFO;
import static com.holderzone.saas.store.constant.Constant.NUMBER_ONE;

/**
 * <AUTHOR>
 * @description 微信实现类
 * @date 2021/9/6 18:13
 * @className: WeChatServiceImpl
 */
@Slf4j
@Service
public class WeChatServiceImpl implements WeChatService {

    @Resource
    private WeChatConfig weChatConfig;

    /**
     * holder-saas-member-terminal
     */
    private final NewMemberInfoClientService memberInfoClientService;

    public WeChatServiceImpl(NewMemberInfoClientService memberInfoClientService) {
        this.memberInfoClientService = memberInfoClientService;
    }

    /**
     * 获取微信授权参数
     * 不需要参数
     *
     * @return 微信授权所需参数
     */
    @Override
    public WeChatAuthParamDTO getWeChatAuthParam() {

        WeChatAuthParamDTO respDTO = new WeChatAuthParamDTO();

        // 获取access_token
        HashMap<String, Object> accessTokenParams = new HashMap<>();
        accessTokenParams.put("grant_type", "client_credential");
        accessTokenParams.put("appid", weChatConfig.getAppId());
        accessTokenParams.put("secret", weChatConfig.getSecret());
        String accessTokenStr = HttpsClientUtils.doGet(weChatConfig.getAccessTokenUrl(), accessTokenParams);
        log.info("获取access_token accessTokenStr={}", accessTokenStr);
        JSONObject accessTokenJson = JSONObject.parseObject(accessTokenStr);
        String accessToken = accessTokenJson.getString("access_token");

        // 获取sdk_ticket
        HashMap<String, Object> tickeParams = new HashMap<>();
        tickeParams.put("access_token", accessToken);
        tickeParams.put("type", 2);
        String sdkTicketStr = HttpsClientUtils.doGet(weChatConfig.getTicketUrl(), tickeParams);
        log.info("获取sdk_ticket sdkTicketStr={}", sdkTicketStr);
        JSONObject sdkTicketJson = JSONObject.parseObject(sdkTicketStr);
        String ticket = sdkTicketJson.getString("ticket");

        final String nonceStr = RandomStringUtils.randomAlphanumeric(32);
        final int timestamp = Math.toIntExact((System.currentTimeMillis() / 1000));

        // 获取signature
        String preString = "appid=" + weChatConfig.getAppId() + "&noncestr=" + nonceStr + "&sdk_ticket=" + ticket +
                "&timestamp=" + timestamp;
        String signature = DigestUtils.sha1Hex(preString);

        respDTO.setAppId(weChatConfig.getAppId());
        respDTO.setNoncestr(nonceStr);
        respDTO.setSignature(signature);
        respDTO.setScope(weChatConfig.getScope());
        respDTO.setTimestamp(String.valueOf(timestamp));
        return respDTO;
    }

    /**
     * 获取微信用户信息
     *
     * @param authCode 授权码
     * @return 登录结果
     */
    @Override
    public PadLoginMemberRespDTO getWechatUserInfo(String authCode) {
        // 获取新的token及openid
        HashMap<String, Object> accessToken2Params = new HashMap<>();
        accessToken2Params.put("appid", weChatConfig.getAppId());
        accessToken2Params.put("secret", weChatConfig.getSecret());
        accessToken2Params.put("code", authCode);
        accessToken2Params.put("grant_type", "authorization_code");
        String accessToken2Str = HttpsClientUtils.doGet(weChatConfig.getAccessTokenUrl2(), accessToken2Params);
        log.info("获取新的token及openid accessToken2Str={}", accessToken2Str);
        JSONObject accessTokenJson = JSONObject.parseObject(accessToken2Str);

        PadLoginMemberRespDTO respDTO = new PadLoginMemberRespDTO();
        String accessToken = "";
        String openid = "";
        try {
            accessToken = accessTokenJson.getString("access_token");
            openid = accessTokenJson.getString("openid");
        } catch (Exception e) {
            int errorCode = accessTokenJson.getInteger("errorCode");
            String errorMsg = accessTokenJson.getString("errorMsg");
            log.error("获取网页授权失败errorCode=" + errorCode + ",errorMsg=" + errorMsg);
            respDTO.setLoginState(LoginStateEnum.WECHAT_AUTHORIZATION_FAILED.getCode());
            return respDTO;
        }

        // 获取微信用户信息
        HashMap<String, Object> userInfoParams = new HashMap<>();
        userInfoParams.put("access_token", accessToken);
        userInfoParams.put("openid", openid);
        String userInfoStr = HttpsClientUtils.doGet(weChatConfig.getUserInfoUrl(), userInfoParams);
        log.info("获取微信用户信息 userInfoStr={}", userInfoStr);
        JSONObject userInfoJson = JSONObject.parseObject(userInfoStr);
        WeChatUserInfoDTO weChatUserInfoDTO = JSONObject.toJavaObject(userInfoJson, WeChatUserInfoDTO.class);
        log.info("weChatUserInfoDTO={}", JacksonUtils.writeValueAsString(weChatUserInfoDTO));

        // 通过unionid查询会员信息
        PadLoginMemberRespDTO memberRespDTO = memberInfoClientService.getMemberInfoByUnionId(weChatUserInfoDTO);
        if (Objects.equals(LoginStateEnum.WECHAT_NOT_BOUND_PHONE_NUMBER.getCode(), memberRespDTO.getLoginState())) {
            // 存redis
            String redisKey = WE_CHAT_USER_INFO + authCode;
            RedisReqDTO redisReqDTO = new RedisReqDTO();
            redisReqDTO.setRedisValue(JacksonUtils.writeValueAsString(weChatUserInfoDTO));
            redisReqDTO.setRedisKey(redisKey);
            redisReqDTO.setRedisTime(Long.valueOf(NUMBER_ONE));
            redisReqDTO.setTimeUnit(TimeUnit.DAYS);

            memberInfoClientService.putWeChatUserInfoToRedis(redisReqDTO);
        }
        return memberRespDTO;
    }
}
