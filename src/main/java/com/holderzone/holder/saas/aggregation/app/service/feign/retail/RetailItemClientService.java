package com.holderzone.holder.saas.aggregation.app.service.feign.retail;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.retail.bill.request.ReturnItemReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.CancelFreeItemReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailAddGoodsReqDTO;
import com.holderzone.saas.store.dto.retail.item.BatchItemReturnOrFreeReqDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderItemClientService
 * @date 2018/09/06 15:02
 * @description 正餐订单远程调用
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-store-retail", fallbackFactory = RetailItemClientService.FastFoodFallBack.class)
public interface RetailItemClientService {

    @PostMapping("/order_item/retail_add_item")
    String retailAddItem(@RequestBody RetailAddGoodsReqDTO retailAddGoodsReqDTO);

    @PostMapping("/order_item/free")
    BatchItemReturnOrFreeReqDTO freeItem(@RequestBody BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO);

    @PostMapping("/order_item/cancel_free")
    Boolean cancelFree(@RequestBody CancelFreeItemReqDTO cancelFreeItemReqDTO);

    @PostMapping("/order_item/return_items")
    public Boolean returnItems(@RequestBody ReturnItemReqDTO returnItemReqDTO);

    @PostMapping("/order_item/return_order")
    public Boolean returnOrder(@RequestBody ReturnItemReqDTO returnItemReqDTO);

    @Component
    @Slf4j
    class FastFoodFallBack implements FallbackFactory<RetailItemClientService> {

        @Override
        public RetailItemClientService create(Throwable throwable) {
            return new RetailItemClientService() {

                @Override
                public String retailAddItem(RetailAddGoodsReqDTO retailAddGoodsReqDTO) {
                    log.error("添加商品FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("添加商品失败!");
                }

                @Override
                public Boolean cancelFree(CancelFreeItemReqDTO cancelFreeItemReqDTO) {
                    log.error("批量取消赠送FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("批量取消赠送失败!");
                }

                @Override
                public Boolean returnItems(ReturnItemReqDTO returnItemReqDTO) {
                    log.error("退货FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("退货失败!");
                }

                @Override
                public Boolean returnOrder(ReturnItemReqDTO returnItemReqDTO) {
                    log.error("退单失败 FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("退单失败!");
                }

                @Override
                public BatchItemReturnOrFreeReqDTO freeItem(BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO) {
                    log.error("赠送FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("赠送失败!");
                }

            };
        }
    }
}
