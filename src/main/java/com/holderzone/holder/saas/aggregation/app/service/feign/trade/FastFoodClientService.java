package com.holderzone.holder.saas.aggregation.app.service.feign.trade;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.order.common.HangOrderDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateFastFoodReqDTO;
import com.holderzone.saas.store.dto.order.request.tcd.TcdAddOrderReqDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FastFoodClientService
 * @date 2018/09/06 15:02
 * @description 正餐订单远程调用
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = FastFoodClientService.FastFoodFallBack.class)
public interface FastFoodClientService {

    @PostMapping("/fast_food/hang_order_list")
    Map<String, String> hangOrderList(HangOrderDTO hangOrderDTO);

    @PostMapping("/fast_food/gain_order")
    Boolean gainOrder(HangOrderDTO hangOrderDTO);

    @PostMapping("/fast_food/hang_order")
    String hangOrder(HangOrderDTO hangOrderDTO);

    @PostMapping("/fast_food/add_item")
    EstimateItemRespDTO addItem(CreateFastFoodReqDTO createFastFoodReqDTO);

    @PostMapping("/tcd/add_order")
    String tcdAddOrder(TcdAddOrderReqDTO tcdAddOrderReqDTO);


    @Component
    @Slf4j
    class FastFoodFallBack implements FallbackFactory<FastFoodClientService> {

        @Override
        public FastFoodClientService create(Throwable throwable) {
            return new FastFoodClientService() {

                @Override
                public Map<String, String> hangOrderList(HangOrderDTO hangOrderDTO) {
                    log.error("挂单列表FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("挂单列表失败!" + throwable.getMessage());
                }

                @Override
                public Boolean gainOrder(HangOrderDTO hangOrderDTO) {
                    log.error("取单FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("取单失败!" + throwable.getMessage());
                }

                @Override
                public String hangOrder(HangOrderDTO hangOrderDTO) {
                    log.error("挂起订单失败FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("挂起订单失败失败!" + throwable.getMessage());
                }

                @Override
                public EstimateItemRespDTO addItem(CreateFastFoodReqDTO createFastFoodReqDTO) {
                    log.error("快餐下单FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("快餐下单失败!" + throwable.getMessage());
                }

                @Override
                public String tcdAddOrder(TcdAddOrderReqDTO tcdAddOrderReqDTO) {
                    log.error("通吃岛订单落库FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("通吃岛订单落库失败!" + throwable.getMessage());
                }
            };
        }
    }
}
