package com.holderzone.holder.saas.aggregation.app.controller.trade;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.FastFoodClientService;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.order.request.tcd.TcdAddOrderReqDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TongchidaoOrderController
 * @date 2019/01/04 8:53
 * @description 通吃岛订单接口
 * @program holder-saas-store-trade
 */
@RestController
@RequestMapping("/tcd")
@Api(description = "通吃岛订单接口")
@Slf4j
public class TongchidaoOrderAppController {

    private final FastFoodClientService fastFoodClientService;

    @Autowired
    public TongchidaoOrderAppController(FastFoodClientService fastFoodClientService) {
        this.fastFoodClientService = fastFoodClientService;
    }

    @ApiOperation(value = "通吃岛订单落库并打印结账单", notes = "通吃岛订单落库并打印结账单")
    @PostMapping("/add_order")
    public Result<String> tcdAddOrder(@RequestBody TcdAddOrderReqDTO tcdAddOrderReqDTO) {
        log.info("通吃岛订单落库并打印结账单入参{}", JacksonUtils.writeValueAsString(tcdAddOrderReqDTO));
        String enterpriseGuid = tcdAddOrderReqDTO.getEnterpriseGuid();
        UserContextUtils.put(JacksonUtils.writeValueAsString(UserInfoDTO.builder().enterpriseGuid(enterpriseGuid)
                .build()));
        return Result.buildSuccessResult(fastFoodClientService.tcdAddOrder(tcdAddOrderReqDTO));
    }

}
