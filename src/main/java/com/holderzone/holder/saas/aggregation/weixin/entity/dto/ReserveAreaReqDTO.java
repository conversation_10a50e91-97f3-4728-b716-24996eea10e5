package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("根据时间查询当前区域")
public class ReserveAreaReqDTO {

	@ApiModelProperty(value = "选中时间",required = true)
	@NotNull(message = "选中时间不能为空")
	private LocalDateTime currentTime;

	@NotNull(message = "门店id不能为空")
	@ApiModelProperty(value = "门店id",required = true)
	private String storeGuid;
}
