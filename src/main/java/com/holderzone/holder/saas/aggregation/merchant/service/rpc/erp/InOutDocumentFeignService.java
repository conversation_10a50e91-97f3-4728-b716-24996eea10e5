package com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.erp.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/07 下午 17:38
 * @description
 */
@FeignClient(name = "holder-saas-store-erp", fallbackFactory = InOutDocumentFeignServiceFallback.class)
public interface InOutDocumentFeignService {


    /**
     * 插入或者更新入库单据及其明细
     *
     * @param inOutDocumentDTO
     * @return
     */
    @PostMapping("/inOutDocument/insertOrUpdate")
    String insertOrUpdateInOutDocument(@RequestBody InOutDocumentAddOrUpdateDTO inOutDocumentDTO);

    /**
     * 查询出入库单据中的物料信息(新添物料使用)
     *
     * @param materialQueryDTO
     * @return
     */
    @PostMapping("/inOutDocument/selectMaterialListForAdd")
    List<InOutDocumentDetailSelectDTO> selectMaterialListForAdd(@RequestBody InOutDocumentDetailQueryDTO materialQueryDTO);

    /**
     * 查询对应入库单的退货数量小于入库数量的物料明细(新增退货出库单并有关联单据时使用)
     *
     * @param contactDocumentGuid
     * @return
     */
    @PostMapping("/inOutDocument/selectMaterialListForReturn")
    List<InOutDocumentDetailSelectDTO> selectMaterialListForReturn(@RequestParam("contactDocumentGuid") String contactDocumentGuid);

    /**
     * 提交出入库单
     *
     * @param inOutDocumentDTO
     */
    @PostMapping("/inOutDocument/submitInOutDocument")
    void submitInOutDocument(@RequestBody InOutDocumentAddOrUpdateDTO inOutDocumentDTO);

    /**
     * 删除出入库单
     *
     * @param documentGuid
     */
    @PostMapping("/inOutDocument/deleteDocument")
    void deleteDocument(@RequestParam("documentGuid") String documentGuid);

    /**
     * 查询关联单据
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/inOutDocument/selectDocumentGuidList")
    List<String> selectDocumentGuidList(@RequestBody InOutContactDocumentQueryDTO queryDTO);

    /**
     * 查询出入库单据及其明细(编辑时使用)
     *
     * @param documentGuid
     * @return
     */
    @PostMapping("/inOutDocument/selectDocumentAndDetailForUpdate")
    InOutDocumentSelectDTO selectDocumentAndDetailForUpdate(@RequestParam("documentGuid") String documentGuid);

    /**
     * 查询出入库单据及其明细(仅查看时使用)
     *
     * @param documentGuid
     * @return
     */
    @PostMapping("/inOutDocument/selectDocumentAndDetailForSelect")
    InOutDocumentSelectDTO selectDocumentAndDetailForSelect(@RequestParam("documentGuid") String documentGuid);

    /**
     * 查询出入库列表(分页)
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/inOutDocument/selectDocumentListForPage")
    Page<InOutDocumentSelectDTO> selectDocumentListForPage(@RequestBody InOutDocumentQueryDTO queryDTO);

    /**
     * 供应商对账表(分页)
     */
    @PostMapping("/inOutDocument/reconciliation")
    Page<InOutDocumentSelectDTO> selectSuppliersReconciliation(@RequestBody SuppliersReconciliationQueryDTO queryDTO);

    /**
     * 结算总金额
     */
    @PostMapping("/inOutDocument/reconciliation/total")
    BigDecimal selectSuppliersReconciliationTotalAmount(@RequestBody SuppliersReconciliationQueryDTO queryDTO);

    /**
     * 结算
     */
    @PostMapping("/inOutDocument/reconciliation/settle")
    Boolean settleSuppliersReconciliation(@RequestBody List<String> list);

    /**
     * 查询出入库流水明细(分页)
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/inOutDocument/selectFlowDetailListForPage")
    Page<InOutDocumentFolwDetailSelectDTO> selectFlowDetailListForPage(@RequestBody InOutDocumentFlowDetailQueryDTO queryDTO);

    @ApiOperation("查询出入库单详情列表")
    @PostMapping("/inOutDocument/query_document_detail_list")
    List<InOutDocumentSelectDTO> queryDocumentDetailList(@RequestBody DocumentDetailListQueryDTO queryDTO);

    @ApiOperation("新建出入库单据时批量导入物料")
    @PostMapping("/inOutDocument/importMaterialList")
    List<InOutDocumentDetailSelectDTO> importMaterialList(@RequestBody InOutDocumentMaterialImportDTO inOutDocumentMaterialImportDTO);
}

@Component
class InOutDocumentFeignServiceFallback implements FallbackFactory<InOutDocumentFeignService> {

    private static final Logger log = LoggerFactory.getLogger(InOutDocumentFeignServiceFallback.class);
    private static final String HYSTRIX_RESULT = "调用进销存服务熔断！";

    private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

    @Override
    public InOutDocumentFeignService create(Throwable throwable) {
        return new InOutDocumentFeignService() {

            @Override
            public String insertOrUpdateInOutDocument(InOutDocumentAddOrUpdateDTO inOutDocumentDTO) {
                log.error(HYSTRIX_RESULT + "错误原因 throwable:{}", throwable.getMessage());
                return "";
            }

            @Override
            public List<InOutDocumentDetailSelectDTO> selectMaterialListForAdd(InOutDocumentDetailQueryDTO materialQueryDTO) {
                log.error(HYSTRIX_RESULT + "错误原因 throwable:{}", throwable.getMessage());
                return Collections.emptyList();
            }

            @Override
            public List<InOutDocumentDetailSelectDTO> selectMaterialListForReturn(String contactDocumentGuid) {
                log.error(HYSTRIX_RESULT + "错误原因 throwable:{}", throwable.getMessage());
                return Collections.emptyList();
            }

            @Override
            public void submitInOutDocument(InOutDocumentAddOrUpdateDTO inOutDocumentDTO) {
                log.error(HYSTRIX_RESULT + "错误原因 throwable:{}", throwable.getMessage());
            }

            @Override
            public void deleteDocument(String documentGuid) {
                log.error(HYSTRIX_RESULT + "错误原因 throwable:{}", throwable.getMessage());
            }

            @Override
            public List<String> selectDocumentGuidList(InOutContactDocumentQueryDTO queryDTO) {
                log.error(HYSTRIX_RESULT + "错误原因 throwable:{}", throwable.getMessage());
                return Collections.emptyList();
            }

            @Override
            public InOutDocumentSelectDTO selectDocumentAndDetailForUpdate(String documentGuid) {
                log.error(HYSTRIX_RESULT + "错误原因 throwable:{}", throwable.getMessage());
                return null;
            }

            @Override
            public InOutDocumentSelectDTO selectDocumentAndDetailForSelect(String documentGuid) {
                log.error(HYSTRIX_RESULT + "错误原因 throwable:{}", throwable.getMessage());
                return null;
            }

            @Override
            public Page<InOutDocumentSelectDTO> selectDocumentListForPage(InOutDocumentQueryDTO queryDTO) {
                log.error(HYSTRIX_RESULT + "错误原因 throwable:{}", throwable.getMessage());
                return null;
            }

            @Override
            public Page<InOutDocumentSelectDTO> selectSuppliersReconciliation(SuppliersReconciliationQueryDTO queryDTO) {
                log.error(HYSTRIX_RESULT + "错误原因 throwable:{}", throwable.getMessage());
                return null;
            }

            @Override
            public BigDecimal selectSuppliersReconciliationTotalAmount(SuppliersReconciliationQueryDTO queryDTO) {
                log.error(HYSTRIX_RESULT + "错误原因 throwable:{}", throwable.getMessage());
                return null;
            }

            @Override
            public Boolean settleSuppliersReconciliation(List<String> list) {
                log.error(HYSTRIX_RESULT + "错误原因 throwable:{}", throwable.getMessage());
                return null;
            }

            @Override
            public Page<InOutDocumentFolwDetailSelectDTO> selectFlowDetailListForPage(InOutDocumentFlowDetailQueryDTO queryDTO) {
                log.error(HYSTRIX_RESULT + "错误原因 throwable:{}", throwable.getMessage());
                return null;
            }

            @Override
            public List<InOutDocumentSelectDTO> queryDocumentDetailList(DocumentDetailListQueryDTO queryDTO) {
                log.error(HYSTRIX_PATTERN, "queryDocumentDetailList", JacksonUtils.writeValueAsString(queryDTO),
                        ThrowableUtils.asString(throwable));
                throw new ServerException();
            }

            @Override
            public List<InOutDocumentDetailSelectDTO> importMaterialList(InOutDocumentMaterialImportDTO inOutDocumentMaterialImportDTO) {
                log.error(HYSTRIX_PATTERN, "importMaterialList", JacksonUtils.writeValueAsString(inOutDocumentMaterialImportDTO),
                        ThrowableUtils.asString(throwable));
                throw new ServerException();
            }
        };
    }
}

