package com.holderzone.holder.saas.aggregation.merchant.service.rpc.organization;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.organization.*;
import com.holderzone.saas.store.dto.store.store.BindupAccountsSaveDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className OrganizationService
 * @date 19-1-8 下午4:02
 * @description 服务间调用-组织相关服务
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-store-organization", fallbackFactory = OrganizationService.ServiceFallBack.class)
public interface OrganizationService {
    @PostMapping("/brand/create")
    BrandDTO createBrand(@RequestBody BrandDTO brandDTO);

    @PostMapping("/brand/update")
    boolean updateBrand(@RequestBody BrandDTO brandDTO);

    @PostMapping("/brand/delete")
    boolean deleteBrand(@RequestParam("brandGuid") String brandGuid);

    @PostMapping("/brand/query_list")
    List<BrandDTO> queryBrandList(@RequestBody QueryBrandDTO queryBrandDTO);

    @PostMapping("/brand/query_exist_store_account/{brandGuid}")
    boolean queryExistStoreAccount(@PathVariable("brandGuid") String brandGuid);

    @PostMapping("/store/create")
    boolean createStore(StoreDTO storeDTO);

    @PostMapping("/store/update")
    boolean updateStore(StoreDTO storeDTO);

    @PostMapping("/store/enable")
    boolean enableOrDisableStore(@RequestParam("storeGuid") String storeGuid);

    @PostMapping("/store/delete")
    boolean deleteStore(@RequestParam("storeGuid") String storeGuid);

    @PostMapping("/store/query_by_condition")
    Page<StoreDTO> queryStoreByCondition(@RequestBody QueryStoreDTO queryStoreDTO);

    @PostMapping("/store/query_by_condition_no_page")
    List<StoreDTO> queryStoreByConditionNoPage(@RequestBody StoreParserDTO storeParserDTO);

    /**
     * 根据传入的guid集合查询门店：仅包含Guid、Name
     *
     * @param guidList
     * @return
     */
    @PostMapping("/store/query_store_by_idlist")
    List<StoreDTO> queryStoreByGuidList(@RequestBody List<String> guidList);

    @PostMapping("/store/query_store_and_brand_by_id_list")
    List<StoreDTO> queryStoreAndBrandByIdList(@RequestBody List<String> storeGuidList);

    @PostMapping("/store/query_all_store")
    List<StoreDTO> queryAllStore();

    @PostMapping("/organization/create")
    OrganizationDTO createOrganization(@RequestBody OrganizationDTO organizationDTO);

    @PostMapping("/organization/update")
    boolean updateOrganization(@RequestBody OrganizationDTO organizationDTO);

    @PostMapping("/organization/delete")
    boolean deleteOrganization(@RequestParam("organizationGuid") String organizationGuid);

    @PostMapping("/organization/query_exist_organization_or_store")
    boolean queryExistOrganizationOrStore(@RequestParam("organizationGuid") String organizationGuid);

    @PostMapping("/organization/get_optional_organization")
    List<OrganizationDTO> getOptionalOrganization(@RequestParam("organizationGuid") String organizationGuid);

    @PostMapping("/organization/query_enterprise_and_organization")
    List<OrganizationDTO> queryEnterpriseAndOrganization();

    @PostMapping("/organization/query_all_organization")
    List<OrgGeneralDTO> queryAllOrganization();

    @PostMapping("/organization/query_erp_org_store")
    List<OrgGeneralDTO> queryErpOrgStore(@RequestParam(value = "queryErp") Integer queryErp,
                                         @RequestParam(value = "queryStore") Integer queryStore);

    @PostMapping("/organization/query_organization/list")
    List<OrganizationDTO> queryOrganizationList();

    @PostMapping("/organization/query_exist_account")
    boolean queryExistAccount(@RequestParam("organizationGuid") String organizationGuid);

    @PostMapping("/store/parse_by_condition")
    List<String> parseByCondition(@RequestBody StoreParserDTO storeParserDTO);

    @PostMapping("/store/query_store_by_guid")
    StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid);

    /**
     * 根据品牌guid查询品牌信息
     *
     * @param brandGuid 品牌guid
     * @return 品牌信息
     */
    @ApiOperation("根据品牌guid查询品牌信息")
    @PostMapping("/brand/query_brand_by_guid")
    BrandDTO queryBrandByGuid(@RequestParam("brandGuid") String brandGuid);

    @PostMapping("/brand/query_store_guid_list_by_brand_guid")
    List<String> queryStoreGuidListByBrandGuid(@RequestBody String brandGuid);

    /**
     * 根据品牌列表查询品牌列表下的所有门店
     *
     * @param brandGuidList 品牌guid数组
     * @return 门店列表
     */
    @ApiOperation("根据品牌列表查询品牌列表下的所有门店")
    @PostMapping("/store/query_store_by_brandlist")
    List<StoreDTO> queryStoreByBrandList(@RequestBody List<String> brandGuidList);

    /**
     * 根据传入的guid数组查询门店列表（返回扁平结构、只包含guid和name两列）
     *
     * @param singleDataDTO 门店guid集合及品牌guid
     * @return 门店列表（扁平结构、只包含guid和name两列）
     */
    @ApiOperation("根据传入的guid数组查询组织列表")
    @PostMapping("/store/query_store_by_idlist_and_brand")
    List<StoreDTO> queryStoreByIdList(@RequestBody SingleDataDTO singleDataDTO);

    /**
     * 根据门店guid查询门店关联的品牌信息
     *
     * @param storeGuid 门店guid
     * @return 品牌信息
     */
    @ApiOperation(value = "根据门店guid查询门店关联的品牌信息", notes = "若门店未关联到品牌则返回为null，后期一个门店可关联多个品牌")
    @RequestMapping("/store/query_brand_by_storeguid")
    BrandDTO queryBrandByStoreGuid(@RequestParam("storeGuid") String storeGuid);

    /**
     * 更改销售模式
     *
     * @param brandDTO DTO
     * @return true-成功，false-失败
     */
    @ApiOperation("更改销售模式")
    @PostMapping("/brand/update_sales_model")
    Boolean updateSalesModel(@RequestBody BrandDTO brandDTO);

    /**
     * 批量导入holder组织机构
     */
    @PostMapping("/organization/batch/create")
    void createBatchOrganization(@RequestBody OrganizationBatchCreateDTO createDTO);

    /**
     * 查询该企业下是否存在组织机构
     */
    @GetMapping("/organization/is_exist_organization")
    Boolean isExistOrganization();

    /**
     * 查询门店信息
     */
    @PostMapping("/store/list")
    List<StoreDTO> list(StoreListReq dto);

    /**
     * 更新门店扎帐设置
     *
     * @return
     */
    @PostMapping("/store/update_by_accountcard")
    void updateStoreByAccountAndShowCard(@RequestBody BindupAccountsSaveDTO bindupAccountsSaveDTO);

        @Slf4j
        @Component
        class ServiceFallBack implements FallbackFactory<OrganizationService> {

            private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

            @Override
            public OrganizationService create(Throwable cause) {
                return new OrganizationService() {
                    @Override
                    public BrandDTO createBrand(BrandDTO brandDTO) {
                        log.error(HYSTRIX_PATTERN, "createBrand", JacksonUtils.writeValueAsString(brandDTO), ThrowableUtils.asString(cause));
                        throw new BusinessException("创建品牌失败");
                    }

                    @Override
                    public boolean updateBrand(BrandDTO brandDTO) {
                        log.error(HYSTRIX_PATTERN, "updateBrand", JacksonUtils.writeValueAsString(brandDTO), ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public boolean deleteBrand(String brandGuid) {
                        log.error(HYSTRIX_PATTERN, "deleteBrand", "入参brandGuid为：" + brandGuid, ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public List<BrandDTO> queryBrandList(QueryBrandDTO queryBrandDTO) {
                        log.error(HYSTRIX_PATTERN, "queryBrandList", JacksonUtils.writeValueAsString(queryBrandDTO), ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public boolean queryExistStoreAccount(String brandGuid) {
                        log.error(HYSTRIX_PATTERN, "门店guid为： " + brandGuid, ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public boolean createStore(StoreDTO storeDTO) {
                        log.error(HYSTRIX_PATTERN, "createStore", JacksonUtils.writeValueAsString(storeDTO), ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public boolean updateStore(StoreDTO storeDTO) {
                        log.error(HYSTRIX_PATTERN, "updateStore", JacksonUtils.writeValueAsString(storeDTO), ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public boolean enableOrDisableStore(String storeGuid) {
                        log.error(HYSTRIX_PATTERN, "enableOrDisableStore", "入参storeGuid为：" + storeGuid, ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public boolean deleteStore(String storeGuid) {
                        log.error(HYSTRIX_PATTERN, "deleteStore", "入参storeGuid为：" + storeGuid, ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public Page<StoreDTO> queryStoreByCondition(QueryStoreDTO queryStoreDTO) {
                        log.error(HYSTRIX_PATTERN, "queryStoreByCondition", JacksonUtils.writeValueAsString(queryStoreDTO), ThrowableUtils.asString(cause));
                        throw new BusinessException("查询门店失败");
                    }

                    @Override
                    public List<StoreDTO> queryStoreByConditionNoPage(StoreParserDTO storeParserDTO) {
                        log.error(HYSTRIX_PATTERN, "queryStoreByConditionNoPage", JacksonUtils.writeValueAsString(storeParserDTO), ThrowableUtils.asString(cause));
                        throw new BusinessException("查询门店失败");
                    }

                    @Override
                    public List<StoreDTO> queryStoreByGuidList(List<String> guidList) {
                        log.error(HYSTRIX_PATTERN, "queryStoreByGuidList",
                                JacksonUtils.writeValueAsString(guidList), ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public List<StoreDTO> queryStoreAndBrandByIdList(List<String> storeGuidList) {
                        log.error(HYSTRIX_PATTERN, "queryStoreAndBrandByIdList",
                                JacksonUtils.writeValueAsString(storeGuidList), ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public List<StoreDTO> queryAllStore() {
                        throw new BusinessException("查询门店失败");
                    }

                    @Override
                    public OrganizationDTO createOrganization(OrganizationDTO organizationDTO) {
                        log.error(HYSTRIX_PATTERN, "createOrganization", JacksonUtils.writeValueAsString(organizationDTO), ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public boolean updateOrganization(OrganizationDTO organizationDTO) {
                        log.error(HYSTRIX_PATTERN, "updateOrganization", JacksonUtils.writeValueAsString(organizationDTO), ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public boolean deleteOrganization(String organizationGuid) {
                        log.error(HYSTRIX_PATTERN, "deleteOrganization", "入参organizationGuid为：" + organizationGuid, ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public boolean queryExistOrganizationOrStore(String organizationGuid) {
                        log.error(HYSTRIX_PATTERN, "queryExistOrganizationOrStore", "入参organizationGuid为：" + organizationGuid, ThrowableUtils.asString(cause));
                        throw new BusinessException("查询失败");
                    }

                    @Override
                    public List<OrganizationDTO> getOptionalOrganization(String organizationGuid) {
                        log.error(HYSTRIX_PATTERN, "getOptionalOrganization", "入参organizationGuid为：" + organizationGuid, ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public List<OrganizationDTO> queryEnterpriseAndOrganization() {
                        log.error(HYSTRIX_PATTERN, "queryEnterpriseAndOrganization", null, ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public List<OrgGeneralDTO> queryAllOrganization() {
                        log.error(HYSTRIX_PATTERN, "queryAllOrganization", null, ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public List<OrgGeneralDTO> queryErpOrgStore(Integer queryErp, Integer queryStore) {
                        log.error(HYSTRIX_PATTERN, "queryErpOrgStore", null, ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public List<OrganizationDTO> queryOrganizationList() {
                        log.error(HYSTRIX_PATTERN, "queryOrganizationList", null, ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public boolean queryExistAccount(String organizationGuid) {
                        log.error(HYSTRIX_PATTERN, "queryExistAccount", "门店guid为：" + organizationGuid, ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public List<String> parseByCondition(StoreParserDTO storeParserDTO) {
                        log.error(HYSTRIX_PATTERN, "parseByCondition", JacksonUtils.writeValueAsString(storeParserDTO), ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public StoreDTO queryStoreByGuid(String storeGuid) {
                        log.error(HYSTRIX_PATTERN, "queryStoreByGuid", storeGuid, ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public BrandDTO queryBrandByGuid(String brandGuid) {
                        log.error(HYSTRIX_PATTERN, "queryBrandByGuid", brandGuid, ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public List<String> queryStoreGuidListByBrandGuid(String brandGuid) {
                        log.error(HYSTRIX_PATTERN, "queryStoreGuidListByBrandGuid", brandGuid, ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public List<StoreDTO> queryStoreByBrandList(List<String> brandGuidList) {
                        log.error(HYSTRIX_PATTERN, "queryStoreByBrandList", brandGuidList, ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public List<StoreDTO> queryStoreByIdList(SingleDataDTO singleDataDTO) {
                        log.error(HYSTRIX_PATTERN, "queryStoreByIdList", JacksonUtils.writeValueAsString(singleDataDTO),
                                ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public BrandDTO queryBrandByStoreGuid(String storeGuid) {
                        log.error(HYSTRIX_PATTERN, "queryBrandByStoreGuid", storeGuid, ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public Boolean updateSalesModel(BrandDTO brandDTO) {
                        log.error(HYSTRIX_PATTERN, "updateSalesModel", brandDTO.getGuid(), ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public void createBatchOrganization(OrganizationBatchCreateDTO createDTO) {
                        log.error(HYSTRIX_PATTERN, "createBatchOrganization", createDTO, ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public Boolean isExistOrganization() {
                        log.error(HYSTRIX_PATTERN, "isExistOrganization", null, ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public List<StoreDTO>
                    list(StoreListReq dto) {
                        log.error(HYSTRIX_PATTERN, "list", dto, ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                    @Override
                    public void updateStoreByAccountAndShowCard(BindupAccountsSaveDTO bindupAccountsSaveDTO) {
                        log.error(HYSTRIX_PATTERN, "updateStoreByAccountAndShowCard", null, ThrowableUtils.asString(cause));
                        throw new ServerException();
                    }

                };
        }
    }
}