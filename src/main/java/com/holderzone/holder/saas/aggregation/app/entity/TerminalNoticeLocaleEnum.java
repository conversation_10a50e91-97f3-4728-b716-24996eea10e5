package com.holderzone.holder.saas.aggregation.app.entity;

import com.holderzone.saas.store.util.LocaleUtil;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import java.util.Locale;

/**
 * <AUTHOR>
 * @create 2023-07-19
 * @description
 */
@Getter
public enum TerminalNoticeLocaleEnum {


    SUCCESSFULLY_PAID_YUAN("<>成功支付<>元"),

    PAYMENT_RECEIVED_WE_CHAT("收款成功，微信到账<>元"),

    NEW_FOOD_DELIVERY_ORDER_PROCESSED("您有一个新的外卖订单需要处理。"),
    NEW_WE_CHAT_ORDER_PROCESSED("您有一个新的微信订单需要处理。"),
    NEW_OTHER_ORDER_PROCESSED("您有一个新的其它订单需要处理。"),
    NEW_PAD_ORDER_PROCESSED("您有一个新的PAD订单需要处理。"),
    NEW_MEITUAN_ORDER_HANDLE("您有新的美团订单，请及时处理。"),
    NEW_ELE_ME_ORDER_HANDLE("您有新的饿了么订单，请及时处理。"),
    NEW_JD_ORDER_HANDLE("您有新的京东订单，请及时处理。"),
    NEW_SELF_OPERATED_ORDER_HANDLE("您有新的自营外卖平台订单，请及时处理。"),
    NEW_ORDER_HANDLE("您有新的订单，请及时处理。"),
    MEITUAN_REMINDER_HANDLE("您有新的美团催单，请及时处理。"),
    ELE_ME_REMINDER_HANDLE("您有新的饿了么催单，请及时处理。"),
    JD_REMINDER_HANDLE("您有新的京东催单，请及时处理。"),
    NEW_REMINDER_HANDLE("您有新的催单，请及时处理。"),
    MEITUAN_ORDER_PREPARED_HANDLE("您有美团订单已出餐，请及时处理。"),
    ELE_ME_ORDER_PREPARED_HANDLE("您有饿了么订单已出餐，请及时处理。"),
    JD_ORDER_PREPARED_HANDLE("您有京东订单已出餐，请及时处理。"),
    SELF_OPERATED_ORDER_PREPARED_HANDLE("您有赚餐外卖订单已出餐，请及时处理。"),
    ORDER_PREPARED_HANDLE("您有订单已出餐，请及时处理。"),
    REFUND_REQUEST_HANDLE("您有新的【退单】请求，请及时处理。"),
    MEITUAN_ABNORMAL_ORDER_HANDLE("您有新的美团异常订单，请及时处理。"),
    ELE_ME_ABNORMAL_ORDER_HANDLE("您有新的饿了么异常订单，请及时处理。"),
    JD_ABNORMAL_ORDER_HANDLE("您有新的京东异常订单，请及时处理。"),
    NEW_ABNORMAL_ORDER_HANDLE("您有新的异常订单，请及时处理。"),
    MEITUAN_ORDER_CANCELED("您的<>号美团订单已被取消"),
    ELE_ME_ORDER_CANCELED("您的<>号饿了么订单已被取消"),
    JD_ORDER_CANCELED("您的<>号京东订单已被取消");

    private final String message;



    TerminalNoticeLocaleEnum(String message){
        this.message = message;
    }

    private final static String SPLIT_SEPARATE = "<>";

    public static String getLocale(String message){
        if(StringUtils.isEmpty(message)){
            return message;
        }
        //若本地是简体中文直接返回
        if(LocaleContextHolder.getLocale() == Locale.SIMPLIFIED_CHINESE){
            return message;
        }
        //若存在多语言则通过中文寻找对应语言
        for(TerminalNoticeLocaleEnum localeMessageEnum : TerminalNoticeLocaleEnum.values()){
            //若完全匹配
            if(localeMessageEnum.getMessage().equals(message)){
                return LocaleUtil.getMessage(localeMessageEnum.name());
            }
            //若包含
            if(localeMessageEnum.getMessage().contains(SPLIT_SEPARATE)){
                String[] split = localeMessageEnum.getMessage().split(SPLIT_SEPARATE);
                if(!isContains(split,message)){
                    continue;
                }
                //截取替换元素
                String localeMessage = LocaleUtil.getMessage(localeMessageEnum.name());
                String[] localeSplit = localeMessage.split(SPLIT_SEPARATE);
                return replaceLocale(message,split,localeSplit);
            }
        }
        return message;
    }

    private static String replaceLocale(String message, String[] split, String[] localeSplit) {
        for (int i = 0; i < split.length; i++){
            message = message.replaceFirst(split[i],localeSplit[i]);
        }
        return message;
    }

    private static boolean isContains(String[] split, String message) {
        for (String s : split){
            if(!message.contains(s)){
                return false;
            }
        }
        return true;
    }

}
