package com.holderzone.holder.saas.aggregation.merchant.entity.enums;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holderzone.saas.store.util.LocaleUtil;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023-07-19
 * @description
 */
@Getter
public enum ExportLocaleEnum {

    WEIGHED("称重"),
    REGULAR_ITEM("普通商品"),
    WEIGHED_ITEM("称重商品"),
    REGULAR("普通"),
    YES("是"),
    NO("否"),

    REPAYMENT_COUNT("还款笔数"),
    OUTSTANDING_AMOUNT_FOR_REPAYMENT("还挂账金额"),
    NUMBER_OF_RESERVED_ORDERS("预订订单数"),
    AMOUNT_OF_RESERVATION_DEPOSIT("订金金额"),
    NUMBER_OF_STORED_VALUE_ORDERS("储值订单数"),
    AMOUNT_OF_STORED_VALUE("储值金额"),
    NUMBER_OF_COMPLETED_SALES_ORDERS("销售（已结）订单数"),
    TOTAL_AMOUNT_OF_COMPLETED_SALES("销售（已结）总额"),
    SHIFT_HANDOVER_STATISTICS("交接班统计报表"),
    NET_SALES("销售（已结）净额"),
    CASH_PAY("现金支付"),
    JH_PAY("聚合支付"),
    BANK_CARD_PAY("银联支付"),
    MEMBER_CARD_PAY("会员余额支付"),
    FACE_TO_PAY("人脸支付"),
    DEBT_PAY("挂账支付"),
    CANTEEN_CARD_PAY("食堂卡支付"),
    CANTEEN_ELECTRONIC_CARD("食堂电子卡支付"),
    CANTEEN_PHYSICAL_CARD("食堂实体卡支付"),
    THIRD_ACTIVITY("第三方平台活动"),
    MT_GROUPON_PAYMENT("美团团购"),
    DOUYIN_GROUPON_PAYMENT("抖音团购"),

    ;

    @Getter
    enum Item{
        EXPORT_ITEM_CATEGORY_NAME("分类名称"),
        EXPORT_ITEM_MENU_NAME("菜单名称"),
        EXPORT_ITEM_MENU_ABBREVIATION("菜单简称"),
        EXPORT_ITEM_WEIGHED_ITEM("称重属性"),
        EXPORT_ITEM_SPECIFICATION_NAME("规格名称"),
        EXPORT_ITEM_SKU("SKU"),
        EXPORT_ITEM_SKU_CODE("SKU简码"),
        EXPORT_ITEM_UNIT_OF_MEASUREMENT("计量单位"),
        EXPORT_ITEM_MINIMUM_ORDER_QUANTITY("起卖数"),
        EXPORT_ITEM_SELLING_PRICE("销售价"),
        EXPORT_ITEM_PARTICIPATE_IN_OVERALL_ORDER_DISCOUNT("是否参与整单折扣"),
        EXPORT_ITEM_PRODUCT_IMAGE_URL("商品图片地址");

        private final String name;

        Item(String name){
            this.name = name;
        }
    }

    @Getter
    enum Order{
        EXPORT_ORDER_STORE_NAME("门店名称"),
        EXPORT_ORDER_DINING_TYPE("就餐类型"),
        EXPORT_ORDER_NO("订单号"),
        EXPORT_ORDER_STATE("订单状态"),
        EXPORT_ORDER_SOURCE("订单来源"),
        EXPORT_ORDER_TIME("下单时间"),
        EXPORT_ORDER_CHECKOUT_TIME("结账时间"),
        EXPORT_ORDER_TABLE("桌台"),
        EXPORT_ORDER_AMOUNT("订单金额"),
        EXPORT_ORDER_NET_SALES("销售净额"),
        EXPORT_ORDER_PAYMENT("支付方式"),
        EXPORT_ORDER_CASHIER("收银员"),
        EXPORT_ORDER_INVOICE("开具发票"),
        ;
        private final String name;

        Order(String name){
            this.name = name;
        }
    }

    @Getter
    enum Source{
        M1("点菜宝(M1)"),
        All_IN_ONE("一体机"),

        EARN_MEALS_TAKEOUT("赚餐自营外卖"),

        SCAN_ORDER("扫码点餐"),

        ;
        public final String desc;


        Source(String desc){
            this.desc = desc;
        }
    }


    private final String message;

    ExportLocaleEnum(String message){
        this.message = message;
    }

    private final static Map<String, ExportLocaleEnum> LOCALE_MAP;

    private final static List<Item> ITEM_LIST;

    private final static List<Order> ORDER_LIST;

    static {
        LOCALE_MAP = initMap();
        ITEM_LIST = Lists.newArrayList(Item.values());
        ORDER_LIST = Lists.newArrayList(Order.values());
    }

    private final static String EXPORT_SOURCE = "EXPORT_SOURCE_";

    public static String[] listItemHeader(){
        if(LocaleContextHolder.getLocale() == Locale.SIMPLIFIED_CHINESE){
            return ITEM_LIST.stream().map(Item::getName).toArray(String[]::new);
        }
        List<String> localeHeaders = Lists.newArrayList();
        ITEM_LIST.forEach(e -> localeHeaders.add(LocaleUtil.getMessage(e.name())));
        return localeHeaders.toArray(new String[0]);
    }

    public static String[] listOrderHeader(){
        if(LocaleContextHolder.getLocale() == Locale.SIMPLIFIED_CHINESE){
            return ORDER_LIST.stream().map(Order::getName).toArray(String[]::new);
        }
        List<String> localeHeaders = Lists.newArrayList();
        ORDER_LIST.forEach(e -> localeHeaders.add(LocaleUtil.getMessage(e.name())));
        return localeHeaders.toArray(new String[0]);
    }

    public static String getSourceLocale(String message){
        if(StringUtils.isEmpty(message)){
            return null;
        }
        for (Source source : Source.values()){
            if(source.getDesc().equals(message)){
               return LocaleUtil.getMessage(EXPORT_SOURCE + source.name());
            }
        }
        return message;
    }

    public static String getLocale(String message){
        if(StringUtils.isEmpty(message)){
            return null;
        }
        for (ExportLocaleEnum exportLocaleEnum : ExportLocaleEnum.values()){
            if(exportLocaleEnum.getMessage().equals(message)){
                return LocaleUtil.getMessage(exportLocaleEnum.name());
            }
        }
        return message;
    }

    private static Map<String, ExportLocaleEnum> initMap(){
        Map<String, ExportLocaleEnum> localeMap = Maps.newHashMap();
        for (ExportLocaleEnum exportLocaleEnum : ExportLocaleEnum.values()){
            localeMap.put(exportLocaleEnum.name(),exportLocaleEnum);
        }
        return localeMap;
    }

    public static String transfer2Cn(String data){
        if(StringUtils.isEmpty(data)){
            return data;
        }
        if(LocaleContextHolder.getLocale() == Locale.SIMPLIFIED_CHINESE){
            return data;
        }
        return LOCALE_MAP.get(data.toUpperCase()).getMessage();
    }

}
