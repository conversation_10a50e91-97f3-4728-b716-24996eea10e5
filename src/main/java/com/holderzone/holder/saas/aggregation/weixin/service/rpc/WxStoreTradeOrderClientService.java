package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolumeDetails;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.SubmitReturnDTO;
import com.holderzone.saas.store.dto.weixin.WebSocketMessageDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.member.*;
import com.holderzone.saas.store.dto.weixin.req.WxPaidOrderDetailsReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreTradeOrderDetailsRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreUserOrderDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreTradeOrderClientService
 * @date 2019/3/25
 */
@Service
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = WxStoreTradeOrderClientService.WxStoreTradeOrderClientFallBack.class)
public interface WxStoreTradeOrderClientService {

    String URL_PREFIX = "/wx_store_order_provide";

    @PostMapping(URL_PREFIX + "/submit")
    SubmitReturnDTO submitOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

    @PostMapping(URL_PREFIX + "/table_details")
    WebSocketMessageDTO tableOrderDetails(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

    @PostMapping(URL_PREFIX + "/update_remark")
    Boolean updateRemark(CreateDineInOrderReqDTO createDineInOrderReqDTO);

    @PostMapping(URL_PREFIX + "/update_guest_count")
    Boolean updateGuestCount(CreateDineInOrderReqDTO createDineInOrderReqDTO);

    @PostMapping(URL_PREFIX + "/order_pay")
    WxStoreTradeOrderDetailsRespDTO orderPay(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

    @PostMapping(URL_PREFIX + "/order_defrey")
    WxStoreTradeOrderDetailsRespDTO orderDefrey(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

    @PostMapping(URL_PREFIX + "/settlement_list")
    WxStoreTradeOrderDetailsRespDTO settlementList(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

    @PostMapping(URL_PREFIX + "/user_order_list")
    WxStoreUserOrderDTO getWxStoreDineinOrderDetailsResp(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

    @PostMapping(URL_PREFIX + "/finish_order")
    WxStoreTradeOrderDetailsRespDTO finishOrder(@RequestBody String orderGuid);

    @PostMapping(URL_PREFIX + "/validate_card")
    CardAndVolumeDTO validateCardAndVolume(@RequestBody CardAndVolumeDiscountReqDTO cardAndVolumeDiscountReqDTO);

    @PostMapping(URL_PREFIX + "/validate_card")
    WxMemberCardRespDTO cardList(WxMemberCardListReqDTO wxMemberCardListReqDTO);

    @PostMapping(URL_PREFIX + "/member_card_list")
    WxMemberCardRespDTO cardList2(WxMemberCardListReqDTO wxMemberCardListReqDTO);

    @PostMapping(URL_PREFIX + "/member_card_list")
    WxVolumeCodeRespDTO volumeCodeList(WxVolumeCodeReqDTO wxVolumeCodeReqDTO);

    @PostMapping(URL_PREFIX + "/member_volume_list")
    WxVolumeCodeRespDTO volumeCodeList2(WxVolumeCodeReqDTO wxVolumeCodeReqDTO);

    @PostMapping(URL_PREFIX + "/volume_details")
    ResponseMemberInfoVolumeDetails volumeCodeDetails(WxVolumeCodeDetailsReqDTO wxVolumeCodeDetailsReqDTO);

    @PostMapping(URL_PREFIX + "/paid_order_details")
    WebSocketMessageDTO orderDetails(WxPaidOrderDetailsReqDTO wxPaidOrderDetailsReqDTO);

    @GetMapping(URL_PREFIX + "/exception")
    void exceptionTest();

    @Slf4j
    @Component
    class WxStoreTradeOrderClientFallBack implements FallbackFactory<WxStoreTradeOrderClientService> {
        @Override
        public WxStoreTradeOrderClientService create(Throwable throwable) {
            return new WxStoreTradeOrderClientService() {

                @Override
                public SubmitReturnDTO submitOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
                    log.error("远程调用服务失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public WebSocketMessageDTO tableOrderDetails(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
                    log.error("远程调用服务失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean updateRemark(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
                    log.error("远程调用服务失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean updateGuestCount(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
                    log.error("远程调用服务失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public WxStoreTradeOrderDetailsRespDTO orderPay(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
                    log.error("远程调用服务失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public WxStoreTradeOrderDetailsRespDTO orderDefrey(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
                    log.error("远程调用服务失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public WxStoreTradeOrderDetailsRespDTO settlementList(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
                    log.error("远程调用服务失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public WxStoreUserOrderDTO getWxStoreDineinOrderDetailsResp(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
                    log.error("远程调用服务失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public WxStoreTradeOrderDetailsRespDTO finishOrder(String orderGuid) {
                    log.error("远程调用服务失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public CardAndVolumeDTO validateCardAndVolume(CardAndVolumeDiscountReqDTO cardAndVolumeDiscountReqDTO) {
                    log.error("获取优惠信息失败:{}", cardAndVolumeDiscountReqDTO);
                    throw new RuntimeException("获取优惠信息失败:" + throwable.getMessage());
                }

                @Override
                public WxMemberCardRespDTO cardList(WxMemberCardListReqDTO wxMemberCardListReqDTO) {
                    log.error("获取会员卡列表失败:{}", wxMemberCardListReqDTO);
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public WxMemberCardRespDTO cardList2(WxMemberCardListReqDTO wxMemberCardListReqDTO) {
                    log.error("获取会员卡列表失败:{}", wxMemberCardListReqDTO);
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public WxVolumeCodeRespDTO volumeCodeList(WxVolumeCodeReqDTO wxVolumeCodeReqDTO) {
                    log.error("获取会员优惠券列表失败:{}", wxVolumeCodeReqDTO);
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public WxVolumeCodeRespDTO volumeCodeList2(WxVolumeCodeReqDTO wxVolumeCodeReqDTO) {
                    log.error("获取会员优惠券列表失败:{}", wxVolumeCodeReqDTO);
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public ResponseMemberInfoVolumeDetails volumeCodeDetails(WxVolumeCodeDetailsReqDTO wxVolumeCodeDetailsReqDTO) {
                    log.error("查询优惠券详情失败:{}", wxVolumeCodeDetailsReqDTO);
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public WebSocketMessageDTO orderDetails(WxPaidOrderDetailsReqDTO wxPaidOrderDetailsReqDTO) {
                    log.error("获取优惠信息失败:{}", wxPaidOrderDetailsReqDTO);
                    throw new RuntimeException("获取优惠信息失败:" + throwable.getMessage());
                }

                @Override
                public void exceptionTest() {
                    log.error("远程调用失败:{}");
                    throw new RuntimeException("获取优惠信息失败:" + throwable.getMessage());
                }
            };
        }
    }
}
