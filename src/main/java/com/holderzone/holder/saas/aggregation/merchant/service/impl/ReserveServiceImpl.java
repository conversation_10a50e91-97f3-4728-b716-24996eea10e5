package com.holderzone.holder.saas.aggregation.merchant.service.impl;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.ReserveService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp.EnterpriseRpcService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.table.TableServiceClient;
import com.holderzone.resource.common.dto.enterprise.OrganizationDTO;
import com.holderzone.saas.store.dto.reserve.HWReserveRecordDTO;
import com.holderzone.saas.store.dto.reserve.MerchantPhoneDTO;
import com.holderzone.saas.store.dto.reserve.TableDTO;
import com.holderzone.saas.store.dto.reserve.TableTypeEnum;
import com.holderzone.saas.store.dto.table.AreaDTO;
import com.holderzone.saas.store.reserve.api.ReserveConfigApi;
import com.holderzone.saas.store.reserve.api.ReserveRecordApi;
import com.holderzone.saas.store.reserve.api.ReserveTableApi;
import com.holderzone.saas.store.reserve.api.common.Utils;
import com.holderzone.saas.store.reserve.api.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveServiceImpl
 * @date 2019/06/05 14:24
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Service
public class ReserveServiceImpl implements ReserveService {

    @Autowired
    ReserveRecordApi reserveRecordApi;
    @Autowired
    ReserveTableApi reserveTableApi;
    @Autowired
    ReserveConfigApi reserveConfigApi;
    @Autowired
    TableServiceClient tableServiceClient;
    @Autowired
    EnterpriseRpcService enterpriseRpcService;
    @Override
    public ReserveRecordDetailDTO launch(HWReserveRecordDTO reserveRecordDTO) {
        OrganizationDTO org = fetchOrg(reserveRecordDTO.getMerchantPhone());
        if(org == null){
            throw new BusinessException("商户不存在");
        }
        UserContext userContext = fetchDefaultUser(org);
        UserContextUtils.put(userContext);

        StoreGuidDTO storeGuidDTO = new StoreGuidDTO();
        storeGuidDTO.setStoreGuid(org.getOrganizationGuid());
        ReserveConfigDTO reserveConfigDTO = reserveConfigApi.query(storeGuidDTO);
        if(reserveConfigDTO == null || reserveConfigDTO.getSegments() == null || reserveConfigDTO.getSegments().isEmpty()){
            throw new BusinessException("商户不允许预定");
        }
        TimingSegment timingSegment = Utils.fetchCurrentSeg(reserveRecordDTO.getReserveTime(),reserveConfigDTO.getSegments());
        if(timingSegment == null){
            throw new BusinessException("没有匹配的预定时间段");
        }
        AreaDTO areaDTO = fetchOneArea(org.getOrganizationGuid(),reserveRecordDTO.getTableType());
        TableDTO tableDTO = fetchOneTable(areaDTO.getGuid(),reserveRecordDTO.getReserveTime());
        ReserveRecordDTO recordDTO = new ReserveRecordDTO();
        recordDTO.setGender(Byte.valueOf("1"));
        recordDTO.setName(reserveRecordDTO.getCustomerPhone());
        recordDTO.setPhone(reserveRecordDTO.getCustomerPhone());
        recordDTO.setNumber(reserveRecordDTO.getPeopleTotal());
        recordDTO.setReserveStartTime(reserveRecordDTO.getReserveTime());
        recordDTO.setTables(Arrays.asList(tableDTO));
        recordDTO.setStoreGuid(org.getOrganizationGuid());
        recordDTO.setDeviceId("mechant");
        recordDTO.setDeviceType(0);
        recordDTO.setStoreGuid(org.getOrganizationGuid());
        return reserveRecordApi.launch(recordDTO);
    }

    @Override
    public Boolean validate(MerchantPhoneDTO dto) {
        OrganizationDTO org = fetchOrg(dto.getPhone());
        UserContext userContext = fetchDefaultUser(org);
        UserContextUtils.put(JacksonUtils.writeValueAsString(userContext));
        StoreGuidDTO storeGuidDTO = new StoreGuidDTO();
        storeGuidDTO.setStoreGuid(org.getOrganizationGuid());
        ReserveConfigDTO reserveConfigDTO = reserveConfigApi.query(storeGuidDTO);
        if(reserveConfigDTO == null || reserveConfigDTO.getSegments() == null || reserveConfigDTO.getSegments().isEmpty()){
            throw new BusinessException("商户不允许预定");
        }
        return true;
    }

    public UserContext fetchDefaultUser(OrganizationDTO org){
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid(org.getEnterpriseGuid());
        userContext.setStoreGuid(org.getOrganizationGuid());
        userContext.setStoreName(org.getName());
        userContext.setUserGuid("system");
        userContext.setUserName("system");
        return userContext;
    }
    public OrganizationDTO fetchOrg(String merchantPhone) {
        return enterpriseRpcService.getStoreInfoByTel(merchantPhone);
    }
    public TableDTO fetchOneTable(String areaGuid, LocalDateTime reserveTime) {
        TableQueryDTO tableQueryDTO = new TableQueryDTO();
        tableQueryDTO.setAreaGuid(areaGuid);
        tableQueryDTO.setTime(reserveTime);
        Collection<TableQueryResultDTO> tableQueryResultDTOS =  reserveTableApi.query(tableQueryDTO);
        if (tableQueryResultDTOS == null || tableQueryResultDTOS.isEmpty()) {
            throw new BusinessException("没有可用桌位");
        }
        return tableQueryResultDTOS.stream().findFirst().get();
    }
    public AreaDTO fetchOneArea(String storeGuid, Byte tableType) {
        TableTypeEnum tableTypeEnum = TableTypeEnum.getByValue(tableType);
        List<AreaDTO> areaDTOS = tableServiceClient.queryAllArea(storeGuid);
        if (areaDTOS == null || areaDTOS.isEmpty()) {
            throw new BusinessException("没有区域");
        }
        AreaDTO areaDTO = areaDTOS.stream()
                .filter(
                        e -> e.getAreaName().contains(tableTypeEnum.getName())
                )
                .findFirst()
                .orElseGet(
                        () -> areaDTOS.stream()
                                .sorted(
                                        Comparator.comparing(AreaDTO::getGmtCreate)
                                )
                                .findFirst()
                                .orElse(areaDTOS.get(0))
                );

        return areaDTO;
    }
}