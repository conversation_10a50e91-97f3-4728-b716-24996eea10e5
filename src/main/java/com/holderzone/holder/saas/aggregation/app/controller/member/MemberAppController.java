package com.holderzone.holder.saas.aggregation.app.controller.member;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.exception.ParamException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.app.constant.Constant;
import com.holderzone.holder.saas.aggregation.app.service.feign.member.MemberClientService;
import com.holderzone.saas.store.dto.member.MemberRechargeDTO;
import com.holderzone.saas.store.dto.member.TransactionRecordDTO;
import com.holderzone.saas.store.dto.member.common.BaseMemberDTO;
import com.holderzone.saas.store.dto.member.request.CreatMemberDTO;
import com.holderzone.saas.store.dto.member.request.ForgetPassWdReqDTO;
import com.holderzone.saas.store.dto.member.request.ModifiPassWdReqDTO;
import com.holderzone.saas.store.dto.member.request.UpdateMemberReqDTO;
import com.holderzone.saas.store.dto.member.response.BaseMemberRespDTO;
import com.holderzone.saas.store.dto.member.response.TransactionRecordRespDTO;
import com.holderzone.saas.store.dto.pay.AggPayRespDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

//import com.holderzone.holder.saas.aggregation.app.service.rpc.BillClientService;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberAppController
 * @date 2018/09/16 下午9:22
 * @description app会员接口
 * @program holder-saas-aggregation-app
 */
@RestController
@RequestMapping("/member")
@Api(description = "会员接口")
public class MemberAppController {

    private static final Logger log = LoggerFactory.getLogger(MemberAppController.class);

    @Autowired
    private MemberClientService memberClientService;

//    @Autowired
//    private BillClientService billClientService;

    @ApiOperation(value = "创建会员接口", notes = "创建新会员")
    @ApiImplicitParam(name = "creatMemberDTO", value = "获取会员信息实体creatMemberDTO", required = true, dataType =
            "CreatMemberDTO")
    @PostMapping(value = "/create", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "创建会员接口",action = OperatorType.ADD)
    public Result create(@RequestBody CreatMemberDTO creatMemberDTO) throws ParamException {
        if (log.isInfoEnabled()) {
            log.info("创建会员，memberDTO={}", JacksonUtils.writeValueAsString(creatMemberDTO));
        }

        if (memberClientService.creat(creatMemberDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage("CREATION_SUCCESSFUL"));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "获取会员信息", notes = "根据手机号获取会员信息")
    @ApiImplicitParam(name = "baseMemberDTO", value = "获取会员信息实体baseMemberDTO", required = true, dataType =
            "BaseMemberDTO")
    @PostMapping(value = "/get_member", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "获取会员信息",action = OperatorType.SELECT)
    public Result<BaseMemberRespDTO> getMember(@RequestBody @Validated BaseMemberDTO baseMemberDTO) throws
            ParamException {
        return Result.buildSuccessResult(memberClientService.getMember(baseMemberDTO));

    }

    @ApiOperation(value = "修改会员信息", notes = "根据guid修改会员信息")
    @ApiImplicitParam(name = "updateMemberReqDTO", value = "修改会员信息实体updateMemberReqDTO", required = true, dataType =
            "UpdateMemberReqDTO")
    @PostMapping(value = "/update_member", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "修改会员信息")
    public Result updateMember(@RequestBody @Validated UpdateMemberReqDTO updateMemberReqDTO) throws
            ParamException {
        if (memberClientService.updateMember(updateMemberReqDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.MODIFICATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));

    }

    @ApiOperation(value = "修改会员密码", notes = "修改会员密码")
    @ApiImplicitParam(name = "modifiPassWdReqDTO", value = "修改会员密码modifiPassWdReqDTO", required = true, dataType =
            "ModifiPassWdReqDTO")
    @PostMapping(value = "/modifi_password", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "修改会员密码")
    public Result modifiPassWd(@RequestBody ModifiPassWdReqDTO modifiPassWdReqDTO) throws ParamException {
        if (memberClientService.modifiPassWd(modifiPassWdReqDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.MODIFICATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "校验密码", notes = "校验密码", response = Boolean.class)
    @PostMapping(value = "/validate", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "校验密码",action = OperatorType.SELECT)
    public Result<Boolean> validateMember(@RequestBody ModifiPassWdReqDTO modifiPassWdReqDTO) {
        return Result.buildSuccessResult(memberClientService.validateMember(modifiPassWdReqDTO));
    }

    @ApiOperation(value = "忘记会员密码", notes = "忘记会员密码")
    @ApiImplicitParam(name = "forgetPassWdReqDTO", value = "忘记会员密码forgetPassWdReqDTO", required = true, dataType =
            "ForgetPassWdReqDTO")
    @PostMapping(value = "/forget_password", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "忘记会员密码",action = OperatorType.SELECT)
    public Result forgetPassWd(@RequestBody ForgetPassWdReqDTO forgetPassWdReqDTO) throws ParamException {
        if (memberClientService.forgetPassWd(forgetPassWdReqDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.MODIFICATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "发送验证码", notes = "发送验证码")
    @ApiImplicitParam(name = "baseMemberDTO", value = "发送验证码baseMemberDTO", required = true, dataType =
            "BaseMemberDTO")
    @PostMapping(value = "/verification_code", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "发送验证码",action = OperatorType.SELECT)
    public Result verificationCode(@RequestBody BaseMemberDTO baseMemberDTO) throws ParamException {
        if (StringUtils.isBlank(baseMemberDTO.getPhone())) {
            return Result.buildIllegalArgumentResult("手机号不合法");
        }
        if (memberClientService.verificationCode(baseMemberDTO)) {
            return Result.buildSuccessMsg("验证码已经发送至手机" + baseMemberDTO.getPhone() + "，请注意查收！");
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

//    @ApiOperation(value = "会员充值", notes = "会员充值")
//    @ApiImplicitParam(name = "memberRechargeDTO", value = "会员充值实体memberRechargeDTO", required = true, dataType =
//            "MemberRechargeDTO")
//    @PostMapping("/recharge")
//    public Result<PreTradingRespDTO> memberRechargeByCash(@RequestBody MemberRechargeDTO memberRechargeDTO) {
//        if (log.isInfoEnabled()) {
//            log.info("会员充值，memberRechargeDTO={}", JacksonUtils.writeValueAsString(memberRechargeDTO));
//        }
//        String resultMsg = memberClientService.checkPrepaidRule(memberRechargeDTO);
//        if (!"SUCCESS".equals(resultMsg)){
//            PreTradingRespDTO build = PreTradingRespDTO.Builder
//                    .builder()
//                    .code("10001")
//                    .msg(resultMsg)
//                    .result(resultMsg)
//                    .build();
//            return Result.buildOpFailedResult(resultMsg,build);
//        }
//        PreTradingRespDTO result;
//        if (Objects.equals(memberRechargeDTO.getPaymentType(), PaymentType.JH_PAY.getId())) {
//            result = billClientService.rechargeJh(memberRechargeDTO);
//        } else {
//            result = billClientService.rechargeCash(memberRechargeDTO);
//        }
//        return Result.buildSuccessResult(result);
//    }

    @ApiOperation(value = "会员充值", notes = "会员充值")
    @PostMapping("/recharge")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "会员充值",action = OperatorType.SELECT)
    public Result<AggPayRespDTO> memberRechargeByCash(@RequestBody MemberRechargeDTO memberRechargeDTO) {
        if (log.isInfoEnabled()) {
            log.info("会员充值，memberRechargeDTO={}", JacksonUtils.writeValueAsString(memberRechargeDTO));
        }
        return Result.buildSuccessResult(memberClientService.charge(memberRechargeDTO));
    }

    @ApiOperation(value = "会员消费记录", notes = "会员消费记录")
    @PostMapping(value = "/transaction_record", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "会员消费记录",action = OperatorType.SELECT)
    public Result<Page<TransactionRecordRespDTO>> transactionRecord(@RequestBody TransactionRecordDTO
                                                                                transactionRecordDTO) {
        Page<TransactionRecordRespDTO> transactionRecordRespDTOPage = memberClientService.queryTransactionRecord
                (transactionRecordDTO);

        return Result.buildSuccessResult(transactionRecordRespDTOPage);
    }

}
