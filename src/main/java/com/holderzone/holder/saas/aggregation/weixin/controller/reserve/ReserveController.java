package com.holderzone.holder.saas.aggregation.weixin.controller.reserve;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.ReserveAppletService;
import com.holderzone.saas.store.reserve.api.dto.*;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Slf4j
@RestController("/reserve/applet")
@RequiredArgsConstructor
public class ReserveController {

    private final ReserveAppletService reserveAppletService;

    @ApiOperation("查询可预订的门店列表")
    @PostMapping("/available_store_list")
    public List<ReserveAvailableStoreDTO> getAvailableStoreList(@RequestBody ReserveAppletQueryDTO queryDTO) {
        return reserveAppletService.getAvailableStoreList(queryDTO);
    }

    @ApiOperation("查询可预订的门店详情")
    @PostMapping("/available_store")
    public ReserveAvailableStoreConfigDTO getAvailableStore(@RequestBody ReserveAppletQueryDTO queryDTO) {
        return reserveAppletService.getAvailableStore(queryDTO);
    }

    @ApiOperation("发起预定")
    @PostMapping("/launch")
    public String launch(@RequestBody ReserveRecordDTO reserveRecordDTO) {
        log.info("小程序用户发起预订入参:{}", JacksonUtils.writeValueAsString(reserveRecordDTO));
        return reserveAppletService.launch(reserveRecordDTO);
    }
}