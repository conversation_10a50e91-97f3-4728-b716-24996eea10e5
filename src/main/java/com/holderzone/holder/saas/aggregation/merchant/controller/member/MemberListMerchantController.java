package com.holderzone.holder.saas.aggregation.merchant.controller.member;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp.EnterpriseRpcService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.marketing.MemberMarketingClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.MemberConsumeClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.MemberListClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.MemberStoreClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.trade.TradeClientService;
import com.holderzone.holder.saas.aggregation.merchant.util.DownloadExcelUtils;
import com.holderzone.resource.common.dto.enterprise.MultiMemberDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.marketing.portrayal.MemberPortrayalDetailsVO;
import com.holderzone.saas.store.dto.member.common.BaseMemberDTO;
import com.holderzone.saas.store.dto.member.request.*;
import com.holderzone.saas.store.dto.member.response.*;
import com.holderzone.saas.store.dto.store.store.OrganizeDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberMerchantController
 * @date 2018/09/16 下午9:22
 * @description web端会员接口
 * @program holder-saas-aggregation-merchant
 */
@RestController
@RequestMapping("/member")
@Api(description = "web端会员列表数据接口")
public class MemberListMerchantController {

    private static final Logger log = LoggerFactory.getLogger(MemberListMerchantController.class);

    @Autowired
    private MemberListClientService memberListClientService;

    @Autowired
    private MemberConsumeClientService memberConsumeClientService;

    @Autowired
    private TradeClientService tradeClientService;

    @Autowired
    private MemberStoreClientService memberStoreClientService;

    @Autowired
    private MemberMarketingClientService marketingClientService;

    @Autowired
    private EnterpriseRpcService enterpriseRpcService;

    @ApiOperation(value = "查询会员列表", notes = "查询会员列表")
    @ApiImplicitParam(name = "memberListReqDTO", value = "获取会员列表", required = true, dataType =
            "MemberListReqDTO")
    @PostMapping(value = "/member_list", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "查询会员列表")
    public Result<Page<MemberListRespDTO>> memberList(@RequestBody MemberListReqDTO memberListReqDTO) {
        log.info("会员列表入参,MemberListReqDTO={}", memberListReqDTO);
        return Result.buildSuccessResult(memberListClientService.memberList(memberListReqDTO));
    }


    @ApiOperation(value = "获取会员详情", notes = "获取会员详情")
    @ApiImplicitParam(name = "baseMemberDTO", value = "根据GUID获取会员详情", required = true, dataType =
            "BaseMemberDTO")
    @PostMapping(value = "/memberDetail", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "获取会员详情")
    public Result<MemberDetailRespDTO> memberList(@RequestBody BaseMemberDTO baseMemberDTO) {
        log.info("会员详情入参入参,BaseMemberDTO={}", baseMemberDTO);
        return Result.buildSuccessResult(memberListClientService.getMemberDetailByGuid(baseMemberDTO));
    }


    @ApiOperation(value = "查询会员卡", notes = "查询会员卡")
    @ApiImplicitParam(name = "memberCardsReqDTO", value = "根据卡号和Guid查询会员卡", required = true, dataType =
            "MemberCardsReqDTO")
    @PostMapping(value = "/memberCards", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "查询会员卡")
    public Result<List<MemberCardsRespDTO>> memberCards(@RequestBody @Validated MemberCardsReqDTO memberCardsReqDTO) {
        log.info("查询会员卡入参,MemberCardsReqDTO={}", memberCardsReqDTO);
        return Result.buildSuccessResult(memberListClientService.getMemberCards(memberCardsReqDTO));
    }


    @ApiOperation(value = "查询会员等级名称和Guid", notes = "查询会员等级")
    @ApiImplicitParam(name = "baseDTO", value = "所有会员等级", required = true, dataType =
            "BaseDTO")
    @PostMapping(value = "/memberGrades", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "查询会员等级名称和Guid")
    public Result<List<MemberGradeListDTO>> memberGrades(@RequestBody BaseDTO baseDTO) {
        log.info("查询会员等级和名称入参,BaseDTO={}", baseDTO);
        List<MemberGradeListDTO> memberGradeLists = memberListClientService.memberGradeList(baseDTO);
        return Result.buildSuccessResult(memberGradeLists);

    }


    @ApiOperation(value = "查询会员支付记录", notes = "查询会员支付记录")
    @ApiImplicitParam(name = "memberPayRecordReqDTO", value = "查询会员支付记录", required = true, dataType =
            "MemberPayRecordReqDTO")
    @PostMapping(value = "/member_transactions/pay", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "查询会员支付记录")
    public Result<Page<MemberPayRecordRespDTO>> memberListPay(@RequestBody @Validated MemberPayRecordReqDTO memberPayRecordReqDTO) {
        memberPayRecordReqDTO.setType(Byte.valueOf("1"));
        log.info("会员交易记录入参,MemberPayRecordReqDTO={}", memberPayRecordReqDTO);
        Page<MemberPayRecordRespDTO> memberPayRecordRespDTOPage = memberListClientService.memberTransactionRecords
                (memberPayRecordReqDTO);
        return Result.buildSuccessResult(memberPayRecordRespDTOPage);
    }


    @ApiOperation(value = "查询会员充值记录", notes = "查询会员充值记录")
    @ApiImplicitParam(name = "memberPayRecordReqDTO", value = "查询会员充值记录", required = true, dataType =
            "MemberPayRecordReqDTO")
    @PostMapping(value = "/member_transactions/charge", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "查询会员充值记录")
    public Result<Page<MemberPayRecordRespDTO>> memberListCharge(@RequestBody @Validated MemberPayRecordReqDTO memberPayRecordReqDTO) {
        log.info("会员交易记录入参,MemberPayRecordReqDTO={}", memberPayRecordReqDTO);
        memberPayRecordReqDTO.setType(Byte.valueOf("0"));
        Page<MemberPayRecordRespDTO> memberPayRecordRespDTOPage = memberListClientService.memberTransactionRecords
                (memberPayRecordReqDTO);
        return Result.buildSuccessResult(memberPayRecordRespDTOPage);
    }

    @ApiOperation(value = "查询会员消费记录", notes = "查询会员消费记录")
    @ApiImplicitParam(name = "memberConsumeReqDTO", value = "会员消费记录", required = true, dataType =
            "MemberConsumeReqDTO")
    @PostMapping(value = "/memberConsumeRecords", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "查询会员消费记录")
    public Result<Page<MemberConsumeRespDTO>> memberGrades(@RequestBody MemberConsumeReqDTO MemberConsumeRespDTO) {
        log.info("会员消费记录入参,MemberConsumeReqDTO={}", MemberConsumeRespDTO);
        Page<MemberConsumeRespDTO> memberConsumeLists = tradeClientService.memberConsumeRecords
                (MemberConsumeRespDTO);
        return Result.buildSuccessResult(memberConsumeLists);

    }

    @Deprecated
    @ApiOperation(value = "查询会员门店信息", notes = "查询会员门店信息")
    @ApiImplicitParam(name = "baseDTO", value = "所有权限门店", required = true, dataType =
            "BaseDTO")
    @PostMapping(value = "/memberStores", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "查询会员门店信息")
    public Result<List<StoreBaseRespDTO>> memberStores(@RequestBody BaseDTO baseDTO) {
        log.info("查询会员门店信息入参,BaseDTO={}", baseDTO);
        List<OrganizeDTO> organizeDTOS = memberStoreClientService.getStoreList(baseDTO.getUserGuid());
        List<StoreBaseRespDTO> baseRespDTOS = new ArrayList<StoreBaseRespDTO>();
        if (organizeDTOS != null) {
            for (OrganizeDTO oz : organizeDTOS) {
                if (oz.getOrganizationType() == 3) {
                    StoreBaseRespDTO storeBaseRespDTO = new StoreBaseRespDTO();
                    storeBaseRespDTO.setStoreGuid(oz.getOrganizationGuid());
                    storeBaseRespDTO.setStoreName(oz.getName());
                    baseRespDTOS.add(storeBaseRespDTO);
                }

            }
        }
        return Result.buildSuccessResult(baseRespDTOS);
    }

    @ApiOperation(value = "查询会员积分类型", notes = "查询会员积分类型")
    @ApiImplicitParam(name = "baseDTO", value = "会员积分类型", required = true, dataType =
            "BaseDTO")
    @PostMapping(value = "/getMemberIntegralType", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "查询会员积分类型")
    Result<List<MemberIntegralTypeRespDTO>> getMemberType(@RequestBody BaseDTO baseDTO) {
        log.info("查询会员积分类型入参,BaseDTO={}", baseDTO);
        return Result.buildSuccessResult(memberListClientService.getMemberIntegralType(baseDTO));
    }


    @ApiOperation(value = "查询会员积分记录", notes = "查询会员积分记录")
    @ApiImplicitParam(name = "MemberIntegralReqDTO ", value = "获取会员积分记录", required = true, dataType =
            "MemberIntegralReqDTO ")
    @PostMapping(value = "/getMemberIntegralRecords", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "查询会员积分记录")
    public Result<Page<MemberIntegralRecordRespDTO>> memberIntegralRecords(@RequestBody @Validated MemberIntegralReqDTO
                                                                                   memberIntegrals) {

        log.info("查询会员积分记录入参,MemberIntegralReqDTO={}", memberIntegrals);
        return Result.buildSuccessResult(memberListClientService.getmemberIntegralRecords(memberIntegrals));
    }


    @PostMapping(value = "/member_data_download")
    @ApiOperation(value = "查询会员并导出", notes = "查询会员记录并导出")
    @ApiImplicitParam(name = "MemberListReqDTO", value = "获取会员记录并导出", required = true, dataType =
            "MemberListReqDTO")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "查询会员并导出")
    public void dataDownload(@RequestBody MemberListReqDTO memberListReqDTO, HttpServletResponse response) {

        log.info("会员列表传入的参数是:" + JacksonUtils.writeValueAsString(memberListReqDTO));

        List<MemberListRespDTO> memberList = memberListClientService.AllmemberList(memberListReqDTO);
        for (MemberListRespDTO m :
                memberList) {
            if (m.getSex() == 1) {
                m.setSexDesc("男");
            } else if (m.getSex() == 0) {
                m.setSexDesc("女");
            } else {
                m.setSexDesc("未知");
            }
        }
        log.info("会员列表返回的参数是:{}" + JacksonUtils.writeValueAsString(memberList));
        StringBuilder sb = new StringBuilder();
        sb.append("name:会员姓名,").append("phone:手机号,").append("memberGradeDesc:会员等级名称,")
                .append("registerTime:注册时间,").append("sexDesc:性别,").append("birthday:生日,").append("totalConsumeNum:累计消费次数,")
                .append("totalPrepaidNum:累计充值次数,").append("totalConsumeFee:累计消费金额,").append("totalPrepaidFee:累计充值金额");

        try {

            DownloadExcelUtils.fullExcel(response, "会员数据", sb.toString(), memberList, MemberListRespDTO.class, 400);

        } catch (Exception e) {
            log.info(e.getMessage());
        }
    }

    @ApiOperation("查询会员画像配置")
    @GetMapping("/query_member_portrayal_setting")
    public Result<MemberPortrayalDetailsVO> queryMemberPortrayalSettingByStore(@RequestParam("storeGuid") String storeGuid) {
        log.info("[查询会员画像配置]入参：storeGuid={}", storeGuid);
        //通过门店guid查会员主体信息
        MultiMemberDTO multiMemberDTO = enterpriseRpcService.findMemberInfoByOrganizationGuid(storeGuid);
        return Result.buildSuccessResult(marketingClientService.queryApplySetting(multiMemberDTO.getMultiMemberGuid()));
    }

}
