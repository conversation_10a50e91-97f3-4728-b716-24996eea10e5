package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member;


import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.dto.account.request.MemberInfoVolumeEnQueryReqDTO;
import com.holderzone.holder.saas.member.dto.account.request.MemberInfoVolumeSaveReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberInfoVolumeEnListRespDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(name = "holder-saas-member-account", fallbackFactory = HsmMemberInfoVolumeClientService.MemberInfoVolumeClientServiceFallback.class)
public interface HsmMemberInfoVolumeClientService {

    @RequestMapping(value = "/hsm-member-info-volume/platform-save", produces = "application/json;charset=utf-8",
            method = RequestMethod.POST)
    List<String> addMemberInfoVolume(MemberInfoVolumeSaveReqDTO saveReqDTO);

    @RequestMapping(value = "/hsm-member-info-volume/platform-list", produces = "application/json;charset=utf-8",
            method = RequestMethod.POST)
    Page<MemberInfoVolumeEnListRespDTO> listBySendEnterpriseGuid(MemberInfoVolumeEnQueryReqDTO enQueryReqDTO);

    @RequestMapping(value = "/hsm-member-info-volume/platform-withdraw", produces = "application/json;charset=utf-8",
            method = RequestMethod.DELETE)
    boolean withdrawMemberInfoVolumeBatch(@RequestParam(value = "batchNum") String batchNum);

    @Component
    class MemberInfoVolumeClientServiceFallback implements
            FallbackFactory<HsmMemberInfoVolumeClientService> {

        private static final Logger LOGGER = LoggerFactory
                .getLogger(MemberInfoVolumeClientServiceFallback.class);

        @Override
        public HsmMemberInfoVolumeClientService create(Throwable throwable) {
            return new HsmMemberInfoVolumeClientService() {

                @Override
                public List<String> addMemberInfoVolume(MemberInfoVolumeSaveReqDTO saveReqDTO) {
                    LOGGER.error("后台同时给多个用户赠送多个优惠券错误:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public Page<MemberInfoVolumeEnListRespDTO> listBySendEnterpriseGuid(MemberInfoVolumeEnQueryReqDTO enQueryReqDTO) {
                    LOGGER.error("精准推送功能企业查询自己赠送的优惠券错误:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public boolean withdrawMemberInfoVolumeBatch(String batchNum) {
                    LOGGER.error("精准推送功能企业按批次撤销赠送的优惠券错误:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }
}
