package com.holderzone.holder.saas.aggregation.app.execption;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @description 文件异常
 * @date 2022/8/26 11:38
 * @className: FileIllegalException
 */
public class FileIllegalException extends BusinessException {

    public FileIllegalException(String message, Throwable cause) {
        super(message, cause);
    }

    public FileIllegalException(String message) {
        super(message);
    }
}