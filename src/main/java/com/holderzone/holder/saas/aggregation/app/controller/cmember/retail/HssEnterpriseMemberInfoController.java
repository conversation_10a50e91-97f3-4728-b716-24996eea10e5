package com.holderzone.holder.saas.aggregation.app.controller.cmember.retail;


import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.member.HssMemberTerminalClientService;
import com.holderzone.holder.saas.member.terminal.dto.retail.RequestSaveMember;
import com.holderzone.holder.saas.member.terminal.dto.retail.RequestUpdateMemberInfo;
import com.holderzone.holder.saas.member.terminal.dto.retail.ResponseMemberAndCardInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 企业会员信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-20
 */
@Api(description = "会员操作")
@RestController
@RequestMapping(value = "/hss/member")
@Slf4j
public class HssEnterpriseMemberInfoController {

    @Resource
    private HssMemberTerminalClientService hssMemberTerminalClientService;

    @ApiOperation(value = "会员新增接口", notes = "会员新增接口")
    @PostMapping(value = "/add", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Boolean> add(@Validated @RequestBody RequestSaveMember saveCMemberDTO) {
        log.info("********************会员新增接口***********:" + JacksonUtils.writeValueAsString(saveCMemberDTO));
        return Result.buildSuccessResult(hssMemberTerminalClientService.addMember(saveCMemberDTO));
    }

    @ApiOperation(value = "修改会员基本信息", notes = "修改会员基本信息")
    @PostMapping(value = "/{memberInfoGuid}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Boolean> updateMemberInfo(@PathVariable("memberInfoGuid") String memberInfoGuid,
                                            @Validated @RequestBody RequestUpdateMemberInfo updateMemberInfoDTO) {
        if (updateMemberInfoDTO == null) {
            throw new BusinessException("更新会员信息不能为空");
        }
        updateMemberInfoDTO.setMemberInfoGuid(memberInfoGuid);
        log.info("********************修改会员基本信息***********:" + JacksonUtils.writeValueAsString(updateMemberInfoDTO));
        return Result.buildSuccessResult(hssMemberTerminalClientService.updateMemberInfo(memberInfoGuid, updateMemberInfoDTO));
    }

    @ApiOperation("查找会员及卡信息")
    @GetMapping("/getMemberAndCardInfo")
    public Result<ResponseMemberAndCardInfo> getMemberAndCardInfo(@RequestParam("phoneOrCardNum") String phoneOrCardNum) {
        log.info("查询会员信息及卡信息入参 phoneOrCardNum = {}", phoneOrCardNum);
        if (phoneOrCardNum == null || phoneOrCardNum.equals("")) {
            throw new BusinessException("手机号或卡号不能为空");
        }
        ResponseMemberAndCardInfo member = hssMemberTerminalClientService.getMemberAndCardInfo(phoneOrCardNum);
        //如果存在账号，状态为禁用，则提示会员被禁用
        if (!StringUtils.isEmpty(member.getMemberInfo().getMemberAccount()) && member.getMemberInfo().getAccountState() == 1) {
            throw new BusinessException("会员被禁用");
        }
        //已经和会员平台沟通：若不存在会员，则返回一个accountState=1的实体
        if (member.getMemberInfo().getAccountState() == 1) {
            throw new BusinessException("无此会员，请重新输入");
        }
        return Result.buildSuccessResult(hssMemberTerminalClientService.getMemberAndCardInfo(phoneOrCardNum));
    }

}
