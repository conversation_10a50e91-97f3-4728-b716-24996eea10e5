package com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.takeaway;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.report.TakeawayOrderDTO;
import com.holderzone.saas.store.dto.report.TakeawayOrderDateDetail;
import com.holderzone.saas.store.dto.report.query.TakeawayIndexQueryDTO;
import com.holderzone.saas.store.dto.report.resp.TakeawayDishRespDTO;
import com.holderzone.saas.store.dto.report.resp.TakeawayStoreRespDTO;
import com.holderzone.saas.store.dto.store.store.ChildOrganization;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeawayClientService
 * @date 2018/12/06 15:53
 * @description
 * @program holder-saas-aggregation-merchant
 */
@FeignClient(name = "holder-saas-store-report", fallbackFactory = TakeawayClientService.TakeawayFallBack.class)
public interface TakeawayClientService {

    @PostMapping("/takeaway/index/dish/sort")
    Page<TakeawayDishRespDTO> queryDish(TakeawayIndexQueryDTO takeawayIndexQueryDTO);

    @PostMapping("/takeaway/index/store/sort")
    Page<TakeawayStoreRespDTO> queryStore(TakeawayIndexQueryDTO takeawayIndexQueryDTO);

    @PostMapping("/takeaway/index/storeInfo")
    List<StoreDTO> getTakeawayStoreInfo();

    /**
     * 外卖订单5个指标
     *
     * @param takeawayIndexQueryDTO
     * @return
     */
    @PostMapping("/takeaway/index/orderInfo")
    TakeawayOrderDTO selectOrderInfo(@RequestBody TakeawayIndexQueryDTO takeawayIndexQueryDTO);

    /**
     * 外卖订单基于给定时间段统计各个指标接口
     *
     * @param takeawayIndexQueryDTO
     * @return
     */
    @PostMapping("/takeaway/index/orderInfoDetailByDate")
    TakeawayOrderDateDetail selectOrderInfoDetail(@RequestBody TakeawayIndexQueryDTO takeawayIndexQueryDTO);


    @Component
    class TakeawayFallBack implements FallbackFactory<TakeawayClientService> {

        private static final Logger LOGGER = LoggerFactory.getLogger(TakeawayFallBack.class);

        @Override
        public TakeawayClientService create(Throwable throwable) {
            return new TakeawayClientService() {

                @Override
                public Page<TakeawayDishRespDTO> queryDish(TakeawayIndexQueryDTO takeawayIndexQueryDTO) {
                    throwable.printStackTrace();
                    LOGGER.error("外卖首页菜品信息异常");
                    throw new BusinessException("外卖首页菜品信息异常");
                }

                @Override
                public List<StoreDTO> getTakeawayStoreInfo() {
                    throwable.printStackTrace();
                    LOGGER.error("查询外卖首页门店异常");
                    throw new BusinessException("查询外卖首页门店异常");
                }

                @Override
                public TakeawayOrderDTO selectOrderInfo(TakeawayIndexQueryDTO takeawayIndexQueryDTO) {
                    LOGGER.error("外卖订单5个指标异常");
                    throw new BusinessException("外卖订单5个指标异常", throwable);
                }

                @Override
                public TakeawayOrderDateDetail selectOrderInfoDetail(TakeawayIndexQueryDTO takeawayIndexQueryDTO) {
                    LOGGER.error("外卖订单基于给定时间段统计各个指标接口");
                    throw new BusinessException("外卖订单基于给定时间段统计各个指标接口", throwable);
                }

                @Override
                public Page<TakeawayStoreRespDTO> queryStore(TakeawayIndexQueryDTO takeawayIndexQueryDTO) {
                    throwable.printStackTrace();
                    LOGGER.error("外卖首页门店排名异常");
                    throw new BusinessException("外卖首页门店排名异常");
                }
            };
        }
    }

}
