package com.holderzone.holder.saas.aggregation.app.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/26 11:26
 * @className: FileUploadDTO
 */
@Data
@ApiModel
public class FileUploadDTO implements Serializable {

    private static final long serialVersionUID = -4452521830368797908L;

    @ApiModelProperty(value = "像素高度要求")
    private Integer height;

    @ApiModelProperty(value = "像素宽度要求")
    private Integer weight;

    @ApiModelProperty(value = "文件大小要求。超过自动压缩")
    private Long size;

}
