package com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.GoodsBomConfigDTO;
import com.holderzone.saas.store.dto.erp.GoodsBomDTO;
import com.holderzone.saas.store.dto.item.req.ItemQueryReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/06 13:49
 */
@FeignClient("holder-saas-store-erp")
public interface BomFeignService {
    @PostMapping("bom")
    /**添加bom配置*/
    Boolean add(@RequestBody GoodsBomConfigDTO goodsBomDTO);

    @GetMapping("bom/{goodsGuid}/{goodsSku}")
    /**查询商品bom配置*/
    List<GoodsBomDTO> findBomByGoods(@PathVariable("goodsGuid") String goodsGuid, @PathVariable("goodsSku") String goodsSku);

    @GetMapping("bom/findGoodsList")
    Page<ItemWebRespDTO> findGoodsList(@RequestBody ItemQueryReqDTO itemQueryReqDTO);

    @PostMapping("bom/findGoodsBomCount")
    /**查询商品bom配置的种类数量*/
    List<GoodsBomDTO> findBomCountBySku(@RequestBody List<String> goodsBomDTOList);
}
