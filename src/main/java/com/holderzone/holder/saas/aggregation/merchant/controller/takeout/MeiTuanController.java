package com.holderzone.holder.saas.aggregation.merchant.controller.takeout;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.takeout.TakeoutProducerService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.trade.TradeClientService;
import com.holderzone.saas.store.dto.takeaway.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MeiTuanController
 * @date 2018/09/12 19:32
 * @description
 * @program holder-saas-aggregation-app
 */
@Slf4j
@RestController
@RequestMapping("/takeout/callback/mt")
@Api(description = "美团外卖回调接口")
public class MeiTuanController {

    private final TakeoutProducerService takeoutProducerService;

    private final TradeClientService tradeClientService;

    @Autowired
    public MeiTuanController(TakeoutProducerService takeoutProducerService, TradeClientService tradeClientService) {
        this.takeoutProducerService = takeoutProducerService;
        this.tradeClientService = tradeClientService;
    }

    @PostMapping("/order/{path}")
    @ApiOperation(value = "美团订单回调", notes = "回调推送")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "美团订单回调")
    public MtCallbackResponse orderCreatedCallback(MtCallbackDTO mtCallbackDTO, @PathVariable("path") String path) {
        if (log.isInfoEnabled()) {
            log.info("美团订单回调入参：{}", JacksonUtils.writeValueAsString(mtCallbackDTO));
        }
        return takeoutProducerService.mtOrderCallback(mtCallbackDTO, path);
    }

    @PostMapping("/privacy_degrade")
    @ApiOperation(value = "拉取真实手机号回调", notes = "拉取真实手机号回调")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "拉取真实手机号回调")
    public MtCallbackResponse privacyDegradeCallback(MtCallbackDTO mtCallbackDTO) {
        if (log.isInfoEnabled()) {
            log.info("隐私号降级回调入参：{}", JacksonUtils.writeValueAsString(mtCallbackDTO));
        }
        return takeoutProducerService.mtPrivacyDegradeCallback(mtCallbackDTO);
    }

    @PostMapping("/bind")
    @ApiOperation(value = "门店映射回调", notes = "门店映射回调")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "门店映射回调")
    public MtCallbackResponse bindCallback(MtCallbackDTO mtCallbackDTO) {
        if (log.isInfoEnabled()) {
            log.info("美团绑定回调入参：{}", JacksonUtils.writeValueAsString(mtCallbackDTO));
        }
        return takeoutProducerService.mtBindCallback(mtCallbackDTO);
    }

    @PostMapping("/unbind")
    @ApiOperation(value = "门店自助解绑回调", notes = "门店自助解绑回调")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "门店自助解绑回调")
    public MtCallbackResponse unbindCallback(MtCallbackDTO mtCallbackDTO) {
        if (log.isInfoEnabled()) {
            log.info("美团解绑回调入参：{}", JacksonUtils.writeValueAsString(mtCallbackDTO));
        }
        return takeoutProducerService.mtUnbindCallback(mtCallbackDTO);
    }

    @PostMapping("/settlement/order")
    @ApiOperation(value = "接收订单结算明细回调接口", notes = "回调推送")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "美团接收订单结算明细回调")
    public MtCallbackResponse orderSettlementCallback(MtCallbackSettlementDTO mtCallbackDTO) {
        if (log.isInfoEnabled()) {
            log.info("美团接收订单结算明细回调入参：{}", JacksonUtils.writeValueAsString(mtCallbackDTO));
        }
        return takeoutProducerService.mtOrderSettlementCallback(mtCallbackDTO);
    }

    @PostMapping("/heartbeat")
    @ApiOperation(value = "云端心跳回调", notes = "云端心跳回调")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "云端心跳回调")
    public MtCallbackResponse heartbeatCallback() {
        return takeoutProducerService.mtHeartbeatCallback();
    }


    @ApiOperation(value = "处理历史数据", notes = "处理历史数据")
    @GetMapping("/handle")
    public void handleTradeDetailHistory(String enterpriseGuid, Long guid) {
        log.info("处理历史数据入参,enterpriseGuid:{}：guid:{}", enterpriseGuid, guid);
        tradeClientService.handleTradeDetailHistory(enterpriseGuid, guid);
    }

    @GetMapping("/authorization")
    @ApiOperation(value = "美团业务授权码回调", notes = "回调推送")
    public MtCallbackResponse authorizationCallback(MtCallbackAuthorizationDTO mtCallbackDTO) {
        if (log.isInfoEnabled()) {
            log.info("美团业务授权码回调：{}", JacksonUtils.writeValueAsString(mtCallbackDTO));
        }
        return takeoutProducerService.authorizationCallback(mtCallbackDTO);
    }

    @PostMapping("/authorization/unbind")
    @ApiOperation(value = "美团解除业务授权回调", notes = "回调推送")
    public MtCallbackResponse authorizationUnbindCallback(MtCallbackUnbindAuthorizationDTO mtCallbackDTO) {
        if (log.isInfoEnabled()) {
            log.info("美团解除业务授权回调：{}", JacksonUtils.writeValueAsString(mtCallbackDTO));
        }
        return takeoutProducerService.authorizationUnbindCallback(mtCallbackDTO);
    }

    @PostMapping("/member/consume")
    @ApiOperation(value = "会员卡积分回调", notes = "回调推送")
    public MtCallbackResponse memberConsumeCallback(MtCallbackConsumeDTO mtCallbackDTO) {
        if (log.isInfoEnabled()) {
            log.info("会员卡积分授权回调：{}", JacksonUtils.writeValueAsString(mtCallbackDTO));
        }
        return takeoutProducerService.memberConsumeCallback(mtCallbackDTO);
    }

    @PostMapping("/member/new")
    @ApiOperation(value = "会员联名卡查询新客", notes = "回调推送")
    public MtCallbackResponse memberNewCallback(MtCallbackMemberDTO mtCallbackDTO) {
        if (log.isInfoEnabled()) {
            log.info("美团会员联名卡查询新客回调：{}", JacksonUtils.writeValueAsString(mtCallbackDTO));
        }
        return takeoutProducerService.memberNewCallback(mtCallbackDTO);
    }
}
