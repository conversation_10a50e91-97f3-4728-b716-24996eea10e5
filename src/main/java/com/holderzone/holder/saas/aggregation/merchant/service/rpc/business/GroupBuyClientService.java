package com.holderzone.holder.saas.aggregation.merchant.service.rpc.business;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.business.group.StoreBindDTO;
import com.holderzone.saas.store.dto.exception.GroupBuyException;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@FeignClient(name = "holder-saas-takeaway-producer", fallbackFactory = GroupBuyClientService.BusinessFallBack.class)
public interface GroupBuyClientService {

    @ApiOperation(value = "团购门店绑定")
    @PostMapping("/group/store/bind")
    Result<?> bindStore(@RequestBody StoreBindDTO storeBindDTO);

    @ApiOperation(value = "团购门店解绑")
    @PostMapping("/group/store/unbind")
    Result<?> unbindStore(@RequestBody StoreBindDTO storeBindDTO);

    @GetMapping("/group/list/{storeGuid}")
    List<StoreAuthDTO> query(@PathVariable("storeGuid") String storeGuid);

    @Component
    class BusinessFallBack implements FallbackFactory<GroupBuyClientService> {

        private static final Logger logger = LoggerFactory.getLogger(BusinessFallBack.class);

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public GroupBuyClientService create(Throwable throwable) {
            return new GroupBuyClientService() {

                @Override
                public Result<?> bindStore(StoreBindDTO storeBindDTO) {
                    logger.error(HYSTRIX_PATTERN, "bindStore", JacksonUtils.writeValueAsString(storeBindDTO),
                            ThrowableUtils.asString(throwable));
                    throw new GroupBuyException(throwable.getMessage());
                }

                @Override
                public Result<?> unbindStore(StoreBindDTO storeBindDTO) {
                    logger.error(HYSTRIX_PATTERN, "unbindStore", JacksonUtils.writeValueAsString(storeBindDTO),
                            ThrowableUtils.asString(throwable));
                    throw new GroupBuyException(throwable.getMessage());
                }

                @Override
                public List<StoreAuthDTO> query(String storeGuid) {
                    logger.error(HYSTRIX_PATTERN, "query", storeGuid,
                            ThrowableUtils.asString(throwable));
                    throw new GroupBuyException(throwable.getMessage());
                }
            };
        }
    }

}
