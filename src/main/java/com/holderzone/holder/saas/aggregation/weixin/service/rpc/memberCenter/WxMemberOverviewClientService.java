package com.holderzone.holder.saas.aggregation.weixin.service.rpc.memberCenter;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.weixin.WxMemberOverviewRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = WxMemberOverviewClientService.WxMemberOverviewClientServiceFallBack.class)
public interface WxMemberOverviewClientService {

	String URL_PREFIX = "/wx_member_center_overview";

	@PostMapping(URL_PREFIX+ "/all_model")
	WxMemberOverviewRespDTO allModel(@RequestBody WxStoreConsumerDTO wxStoreConsumerDTO);

	@Slf4j
	@Component
	class WxMemberOverviewClientServiceFallBack implements FallbackFactory<WxMemberOverviewClientService> {
		@Override
		public WxMemberOverviewClientService create(Throwable throwable) {
			return new WxMemberOverviewClientService() {

				@Override
				public WxMemberOverviewRespDTO allModel(WxStoreConsumerDTO wxStoreConsumerDTO) {
					log.error("查询所有子模块失败:{}", wxStoreConsumerDTO);
					throw new BusinessException(throwable.getMessage());
				}
			};
		}
	}
}
