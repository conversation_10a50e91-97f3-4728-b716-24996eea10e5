package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.dto.account.request.HsmBecomeMemberConditionReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.HsmBecomeMemberConditionRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * <AUTHOR>
 * @description  会员渠道
 * @date 2019/5/21 17:35
 */
@Component
@FeignClient(name = "holder-saas-member-account",fallbackFactory=BecomeMemberConditionClientService.BecomeMemberConditionClientServiceFallBack.class)
public interface BecomeMemberConditionClientService {
    /**
     * 渠道更新
     * @param hsmBecomeMemberConditionReqDTO
     * @return
     */
    @PostMapping(value = "/hsm-become-member-condition/update", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    String update(@RequestBody HsmBecomeMemberConditionReqDTO hsmBecomeMemberConditionReqDTO);
    @GetMapping(value = "/hsm-become-member-condition/getByEnterpriseGuid", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    HsmBecomeMemberConditionRespDTO getByEnterpriseGuid();
    @Slf4j
    @Component
    class BecomeMemberConditionClientServiceFallBack implements FallbackFactory<BecomeMemberConditionClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public BecomeMemberConditionClientService create(Throwable throwable) {
            return new BecomeMemberConditionClientService() {
                @Override
                public String update(HsmBecomeMemberConditionReqDTO hsmBecomeMemberConditionReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryCardLevelBySystemManagementGuid", JacksonUtils.writeValueAsString(hsmBecomeMemberConditionReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("更新会员渠道失败");
                }

                @Override
                public HsmBecomeMemberConditionRespDTO getByEnterpriseGuid() {
                    log.error(HYSTRIX_PATTERN, "getByEnterpriseGuid", "", ThrowableUtils.asString(throwable));
                    throw new BusinessException("获取会员渠道失败");
                }
            };
        }
    }
}
