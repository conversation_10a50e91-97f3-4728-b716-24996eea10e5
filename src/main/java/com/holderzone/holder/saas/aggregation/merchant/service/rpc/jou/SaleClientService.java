package com.holderzone.holder.saas.aggregation.merchant.service.rpc.jou;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.journaling.req.ItemTypeDTO;
import com.holderzone.saas.store.dto.journaling.req.SaleDetailReportReqDTO;
import com.holderzone.saas.store.dto.journaling.req.SingleSaleDetailReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.ItemTypeRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.SaleDetailReportRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

@Component
@FeignClient(name = "holder-saas-store-report", fallbackFactory = SaleClientService.SaleFallBack.class)
public interface SaleClientService {


    @PostMapping("/sale_detail/page")
    Page<SaleDetailReportRespDTO> saleSearch(@RequestBody SaleDetailReportReqDTO saleDetailReportReqDTO);

    @PostMapping("/sale_detail/item_type")
    Map<String, ItemTypeRespDTO> itemSearch(@RequestBody ItemTypeDTO itemTypeDTO);

    @PostMapping("/sale_detail/single_item_type")
    Map<String, ItemTypeRespDTO> singleItemSearch(@RequestBody ItemTypeDTO itemTypeDTO);

    @PostMapping("/sale_detail/single_page")
    Page<SaleDetailReportRespDTO> singlePageSaleDetail(@RequestBody SingleSaleDetailReqDTO singleSaleDetailReqDTO);

    @PostMapping("/sale_detail_new/page_sale_detail_all")
    List<SaleDetailReportRespDTO> pageSaleDetailAll(@RequestBody SaleDetailReportReqDTO saleDetailReportReqDTO);

    @PostMapping("/sale_detail_new/page_sale_detail")
    Page<SaleDetailReportRespDTO> pageSaleDetail(@RequestBody SaleDetailReportReqDTO saleDetailReportReqDTO);

    @PostMapping("/sale_detail_new/page_es_sale_detail_all")
    Page<SaleDetailReportRespDTO> pageEsSaleDetailAll(@RequestBody SaleDetailReportReqDTO saleDetailReportReqDTO);
    @Component
    @Slf4j
    class SaleFallBack implements FallbackFactory<SaleClientService> {

        @Override
        public SaleClientService create(Throwable throwable) {
            return new SaleClientService() {

                @Override
                public Page<SaleDetailReportRespDTO> saleSearch(SaleDetailReportReqDTO saleDetailReportReqDTO) {
                    log.error("查询销售明细报表异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Map<String, ItemTypeRespDTO> itemSearch(ItemTypeDTO itemTypeDTO) {
                    log.error("查询销售明细报表分类加载异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Map<String, ItemTypeRespDTO> singleItemSearch(ItemTypeDTO itemTypeDTO) {
                    log.error("商超-查询销售明细报表分类加载异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Page<SaleDetailReportRespDTO> singlePageSaleDetail(SingleSaleDetailReqDTO singleSaleDetailReqDTO) {
                    log.error("商超-查询销售明细报表异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<SaleDetailReportRespDTO> pageSaleDetailAll(SaleDetailReportReqDTO saleDetailReportReqDTO) {
                    log.error("查询销售明细报表异常 pageSaleDetailAll：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Page<SaleDetailReportRespDTO> pageSaleDetail(SaleDetailReportReqDTO saleDetailReportReqDTO) {
                    log.error("查询销售明细报表异常 pageSaleDetail：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Page<SaleDetailReportRespDTO> pageEsSaleDetailAll(SaleDetailReportReqDTO saleDetailReportReqDTO) {
                    log.error("查询销售明细报表异常 pageSaleDetail：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }
            };
        }


    }


}
