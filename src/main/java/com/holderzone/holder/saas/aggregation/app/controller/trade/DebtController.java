package com.holderzone.holder.saas.aggregation.app.controller.trade;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DebtClientService;
import com.holderzone.saas.store.dto.trade.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * 挂账Controller
 *
 * @since 2020-12-15
 */
@Slf4j
@Api(value = "挂账接口")
@RestController
@RequestMapping(value = "/debt")
public class DebtController {

    private final DebtClientService debtClientService;

    public DebtController(DebtClientService debtUnitService) {
        this.debtClientService = debtUnitService;
    }

    @PostMapping("/unit/page")
    @ApiOperation(value = "分页查询单位列表")
    public Result<Page<DebtUnitPageRespDTO>> unitPage(@RequestBody DebtUnitPageReqDTO reqDTO) {
        log.info("挂账单位分页查询入参：{}", JSON.toJSONString(reqDTO));
        return Result.buildSuccessResult(debtClientService.unitPage(reqDTO));
    }

    @PostMapping("/unit/page_debt_unit_record")
    @ApiOperation(value = "分页查询挂账单位流水")
    public Result<Page<DebtUnitRecordPageRespDTO>> pageDebtUnitRecord(@Validated @RequestBody DebtUnitRecordPageReqDTO reqDTO) {
        log.info("分页查询挂账单位流水接口入参：{}", JSON.toJSONString(reqDTO));
        return Result.buildSuccessResult(debtClientService.pageDebtUnitRecord(reqDTO));
    }

    @GetMapping("/unit/query_debt_unit_total")
    @ApiOperation(value = "查询挂账单位汇总")
    public Result<DebtUnitRecordTotalDTO> queryDebtUnitTotal(@RequestParam("unitCode") String unitCode) {
        log.info("查询挂账单位汇总接口入参：{}", unitCode);
        return Result.buildSuccessResult(debtClientService.queryDebtUnitTotal(unitCode));
    }

    @PostMapping("/unit/update_debt_unit")
    @ApiOperation(value = "挂账管理还款")
    public Result<Boolean> updateDebtUnit(@RequestBody DebtUnitRecordUpdateReqDTO updateReqDTO) {
        log.info("查询挂账单位汇总接口入参：{}", JSON.toJSONString(updateReqDTO));
        return Result.buildSuccessResult(debtClientService.updateDebtUnit(updateReqDTO));
    }

    @PostMapping("/unit/debt_repayment_record_print")
    @ApiOperation(value = "挂账记录重打印")
    public Result<Boolean> debtRepaymentRecordPrint(@RequestBody DebtUnitRecordUpdateReqDTO updateReqDTO) {
        log.info("挂账记录重打印入参：{}", JacksonUtils.writeValueAsString(updateReqDTO));
        return Result.buildSuccessResult(debtClientService.debtRepaymentRecordPrint(updateReqDTO));
    }

}
