package com.holderzone.holder.saas.aggregation.merchant.exception;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FileIllegalException
 * @date 2018/09/13 11:32
 * @description //TODO
 * @program holder-saas-aggregation-merchant
 */
public class FileIllegalException extends BusinessException {

    public FileIllegalException(String message, Throwable cause) {
        super(message, cause);
    }

    public FileIllegalException(String message) {
        super(message);
    }
}
