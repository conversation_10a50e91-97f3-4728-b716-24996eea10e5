package com.holderzone.holder.saas.aggregation.merchant.service.rpc.manage;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.business.brand.BrandConfigDTO;
import com.holderzone.saas.store.dto.business.brand.BrandConfigSaveOrUpdateDTO;
import com.holderzone.saas.store.dto.business.manage.*;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessClientService
 * @date 2018/09/12 15:30
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-store-business", fallbackFactory = BusinessClientService.BusinessFallBack.class)
public interface BusinessClientService {

    @PostMapping("/pic/save_config")
    String saveConfig(@RequestBody ScreenPictureConfigDTO screenPictureConfigDTO);

    @PostMapping("/pic/get_config")
    ScreenPictureConfigDTO getConfig(@RequestBody ScreenPicConfigReqDTO screenPicConfigReqDTO);

    @PostMapping("/pic/save")
    String save(@RequestBody ScreenPictureDTO screenPictureDTO);

    @PostMapping("/pic/query/web")
    List<ScreenAppRespDTO> query(@RequestBody ScreenPicQuery screenPicQuery);

    @PostMapping("/pic/delete/{screenPictureGuid}")
    String delete(@PathVariable("screenPictureGuid") String screenPictureGuid);

    @PostMapping("/pic/time")
    String setTime(ScreenPicTimeDTO screenPicTimeDTO);

    @PostMapping("/pic/get/time")
    List<ScreenPicTimeDTO> getTime(ScreenPicTimeDTO screenPicTimeDTO);

    @PostMapping("/brand_config/save_or_update")
    Long saveOrUpdateBrandConfig(@Valid @RequestBody BrandConfigSaveOrUpdateDTO saveOrUpdateDTO);

    @GetMapping("/brand_config/get_by_brand/{brandGuid}")
    BrandConfigDTO getBrandConfigByBrandGuid(@PathVariable("brandGuid") String brandGuid);

    @Component
    class BusinessFallBack implements FallbackFactory<BusinessClientService> {

        private static final Logger logger = LoggerFactory.getLogger(BusinessFallBack.class);

        @Override
        public BusinessClientService create(Throwable throwable) {
            return new BusinessClientService() {

                @Override
                public List<ScreenPicTimeDTO> getTime(ScreenPicTimeDTO screenPicTimeDTO) {
                    logger.error("getTime失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("getTime失败，e={}" + throwable.getMessage());
                }

                @Override
                public ScreenPictureConfigDTO getConfig(ScreenPicConfigReqDTO screenPicConfigReqDTO) {
                    logger.error("getConfig，screenPicConfigReqDTO={}, e={}", screenPicConfigReqDTO, throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("saveConfig，e={}" + throwable.getMessage());
                }

                @Override
                public String saveConfig(ScreenPictureConfigDTO screenPictureConfigDTO) {
                    logger.error("saveConfig，screenPictureConfigDTO={}, e={}", screenPictureConfigDTO, throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("saveConfig，e={}" + throwable.getMessage());
                }

                @Override
                public String save(ScreenPictureDTO screenPictureDTO) {
                    logger.error("save接口失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("save接口失败，e={}" + throwable.getMessage());
                }

                @Override
                public String setTime(ScreenPicTimeDTO screenPicTimeDTO) {
                    logger.error("setTime接口失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("save接口失败，e={}" + throwable.getMessage());
                }

                @Override
                public List<ScreenAppRespDTO> query(ScreenPicQuery screenPicQuery) {
                    logger.error("query失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("save接口失败，e={}" + throwable.getMessage());
                }

                @Override
                public String delete(String screenPictureGuid) {
                    logger.error("delete，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("save接口失败，e={}" + throwable.getMessage());
                }

                @Override
                public Long saveOrUpdateBrandConfig(BrandConfigSaveOrUpdateDTO saveOrUpdateDTO) {
                    logger.error("saveOrUpdateBrandConfig失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("saveOrUpdateBrandConfig失败，e={}" + throwable.getMessage());
                }

                @Override
                public BrandConfigDTO getBrandConfigByBrandGuid(String brandGuid) {
                    logger.error("getBrandConfigByBrandGuid失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("getBrandConfigByBrandGuid失败，e={}" + throwable.getMessage());
                }
            };
        }
    }

}
