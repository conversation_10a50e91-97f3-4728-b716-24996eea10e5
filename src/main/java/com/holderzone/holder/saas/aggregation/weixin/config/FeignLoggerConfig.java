package com.holderzone.holder.saas.aggregation.weixin.config;

import feign.Logger;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date ：Created in 2019/6/30  20:00
 * @description ：
 * @version: 1.0
 */

/**
 * 代表当前的类是一个配置类
 * ApplicationContext.xml
 */
@Configuration
public class FeignLoggerConfig {
    /**
     * Bean相当于把当前对象注入到Spring容器当中
     * <Bean  id =""  class=""></>
     * @return
     */
    @Bean
    public Logger.Level feignLoggerLevel(){

        return Logger.Level.FULL;
    }
    /**
     * FULL Log the headers, body, and metadata for both requests and responses.
     * 全部：请求和响应的头、体、元数据
     */

    /**
     * Bean 把当前对象注入到spring容器中
     * <bean id= class=></>
     * @return
     */
    /**
     *  NONE No logging.不做日志
     */
    /**
     * BASIC Log only the request method and URL and the response status code and execution time.
     * 仅仅是只记录请求方法，URL地址，响应状态码和执行时间
     */
    /**
     * HEADERS Log the basic information along with request and response headers.
     * headers：记录基本信息，请求和响应的头
     */
}

