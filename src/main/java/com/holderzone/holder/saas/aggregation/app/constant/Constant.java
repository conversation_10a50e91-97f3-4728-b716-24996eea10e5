package com.holderzone.holder.saas.aggregation.app.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className Constant
 * @date 2018/09/10 16:28
 * @description
 * @program holder-saas-aggregation-app
 */
public interface Constant {

    String ENTERPRISE_GUID = "enterpriseGuid";

    String API_VERSION = "version";

    String API_VERSION_ERROR = "请升级到最新版本";

    Integer NUMBER_ZERO = 0;

    Integer NUMBER_ONE = 1;

    Integer NUMBER_TWO = 2;

    Integer NUMBER_FOUR = 4;

    String LOGIN_INPUT_PARAMETER_IS_EMPTY = "登录入参为空";

    String WRONG_LOGIN_METHOD = "登录方式错误";

    String WECHAT_AUTHORIZATION_CODE_IS_EMPTY = "微信授权码为空";

    String OPERATION_SUBJECT_GUID_IS_EMPTY = "运营主体guid为空";

    String PERMANENT_VALIDITY = "永久有效";

    String RESUME_SALES = "恢复售卖";

    String PAYMENT_FAILED = "支付失败";

    String PAYMENT_SUCCESSFUL = "支付成功";

    String DEFAULT_TEMPLATE = "默认模板";

    String OPERATION_FAILED = "OPERATION_FAILED";

    String MODIFICATION_SUCCESSFUL = "MODIFICATION_SUCCESSFUL";

    String OPERATION_SUCCESSFUL = "OPERATION_SUCCESSFUL";

    String ORDER_PREPARATION_SUCCESSFUL = "ORDER_PREPARATION_SUCCESSFUL";

    String DEFAULT_LEVEL = "默认等级";

    String REFUND_INTERFACE_VERSION = "2.0";

    String RESERVE_PAYING = "预订支付中...";

}
