package com.holderzone.holder.saas.aggregation.merchant.controller.openapi;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.ReportService;
import com.holderzone.saas.store.dto.report.openapi.SaleDetailLimitOpenRespDTO;
import com.holderzone.saas.store.dto.report.openapi.SaleDetailQueryDTO;
import com.holderzone.saas.store.enums.report.openapi.RequestSourceEnum;
import com.holderzone.sdk.annotation.RateLimit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 销售明细
 */
@Slf4j
@RestController
@RequestMapping("/report/openapi")
@RequiredArgsConstructor
public class OpenSaleDetailController {

    private final ReportService reportService;

    /**
     * 接口升级 使用下面的 此接口指定为世园会查询 固定写死了requestSource
     */
    @PostMapping("/sale_detail")
    @RateLimit(limitType = "openapiReport", limitCount = 100)
    public Result<SaleDetailLimitOpenRespDTO<?>> queryShiYuanHui(@RequestBody SaleDetailQueryDTO saleDetailQueryDTO) {
        log.info("销售明细报表请求入参：{}", JacksonUtils.writeValueAsString(saleDetailQueryDTO));
        try {
            saleDetailQueryDTO.setRequestSource(RequestSourceEnum.SHI_YUAN_HUI.getCode());
            return Result.buildSuccessResult(reportService.querySaleDetail(saleDetailQueryDTO));
        } catch (Exception e) {
            throw new BusinessException("系统繁忙稍后再试");
        }
    }

    @PostMapping("/sale_details")
    @RateLimit(limitType = "openapiReport", limitCount = 100)
    public Result<SaleDetailLimitOpenRespDTO<?>> query(@RequestBody SaleDetailQueryDTO saleDetailQueryDTO) {
        log.info("销售明细报表请求入参：{}", JacksonUtils.writeValueAsString(saleDetailQueryDTO));
        try {
            return Result.buildSuccessResult(reportService.querySaleDetail(saleDetailQueryDTO));
        } catch (Exception e) {
            throw new BusinessException("系统繁忙稍后再试");
        }
    }
}
