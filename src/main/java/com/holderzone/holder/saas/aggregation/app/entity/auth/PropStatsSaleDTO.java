package com.holderzone.holder.saas.aggregation.app.entity.auth;

import com.holderzone.holder.saas.aggregation.app.anno.DataAuthFieldControl;
import lombok.Data;

import java.util.List;

/**
 * 属性销售统计
 */
@Data
public class PropStatsSaleDTO {

    /**
     * 属性组列表
     */
    @DataAuthFieldControl
    private List<PropGroup> propGroupList;

    /**
     * 合计：数量
     */
    @DataAuthFieldControl("attr_sale_count")
    private String totalQuantity;

    /**
     * 合计：金额
     */
    @DataAuthFieldControl("attr_sale_amount")
    private String totalMoney;

    @Data
    public static class PropGroup {

        /**
         * 属性组名称
         */
        @DataAuthFieldControl("attr_sale_group_name")
        private String name;

        /**
         * 合计：属性组数量
         */
        @DataAuthFieldControl("attr_sale_count")
        private String groupQuantity;

        /**
         * 合计：属性组金额
         */
        @DataAuthFieldControl("attr_sale_amount")
        private String groupMoney;

        /**
         * 属性列表
         */
        @DataAuthFieldControl
        private List<PropItem> propList;

    }

    @Data
    public static class PropItem {

        /**
         * 属性名称
         */
        @DataAuthFieldControl("attr_sale_name")
        private String name;

        /**
         * 属性销售数量
         */
        @DataAuthFieldControl("attr_sale_count")
        private String quantity;

        /**
         * 单价
         */
        @DataAuthFieldControl("attr_sale_unit_price")
        private String unitPrice;

        /**
         * 属性销售金额
         */
        @DataAuthFieldControl("attr_sale_amount")
        private String money;

        /**
         * 属性列表
         */
        @DataAuthFieldControl
        private List<PropItem> propList;
    }
}
