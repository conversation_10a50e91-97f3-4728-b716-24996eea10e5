package com.holderzone.holder.saas.aggregation.merchant.service.rpc.trade;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.journaling.req.*;
import com.holderzone.saas.store.dto.journaling.resp.*;
import com.holderzone.saas.store.dto.member.request.MemberConsumeReqDTO;
import com.holderzone.saas.store.dto.member.response.MemberConsumeRespDTO;
import com.holderzone.saas.store.dto.order.SettlementRulesDTO;
import com.holderzone.saas.store.dto.order.response.OrderUploadRespDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsCombinedRespDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsDetailRespDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsQueryDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsRespDTO;
import com.holderzone.saas.store.dto.trade.req.FixGrouponTransactionRecordReqDTO;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberConsumeClientService
 * @date 2018/09/29 14:19
 * @description //TODO
 * @program holder-saas-store-member
 */

@Component
@FeignClient(name = "holder-saas-store-trade")
public interface TradeClientService {

    @PostMapping("/dine_in_bill/member_consume_records")
    Page<MemberConsumeRespDTO> memberConsumeRecords(@RequestBody MemberConsumeReqDTO memberConsumeRespDTO);

    @GetMapping("/hst_settlement_rules/get_settlement_rules")
    List<SettlementRulesDTO> getSettlementRules();

    @PostMapping("/order_report/orderStatistics/page")
    Page<BusinessOrderStatisticsRespDTO> getOrderStatisticsPage(@RequestBody @Validated BusinessOrderStatisticsQueryDTO queryDTO);

    @PostMapping("/order_report/orderStatistics/combined")
    BusinessOrderStatisticsCombinedRespDTO getOrderStatisticsCombined(@RequestBody @Validated BusinessOrderStatisticsQueryDTO queryDTO);

    @GetMapping("/order_report/orderStatistics/detail")
    BusinessOrderStatisticsDetailRespDTO getOrderStatisticsDetail(@RequestParam("orderGuid") String orderGuid);

    @GetMapping("/order_report/orderStatistics/refundInfo")
    BusinessOrderStatisticsDetailRespDTO getOrderStatisticsRefundInfo(@RequestParam("orderGuid") String orderGuid);

    @GetMapping("/order_detail/copyOrders")
    OrderUploadRespDTO copyOrders(@RequestParam("fileUrl") String fileUrl);

    /**
     * 根据订单查询订单状态
     *
     * @param orderGuid 订单guid
     * @return 订单状态 (-1：未查询到订单 1：未结账， 2：已结账， 3：已退款，4：已作废)
     */
    @ApiOperation(value = "根据订单查询订单状态")
    @GetMapping("/dine_in_order/get_order_state_by_guid")
    Integer getOrderStateByGuid(@RequestParam("orderGuid") String orderGuid);

    /**
     * 根据订单查询订单金额
     *
     * @param orderGuid 订单guid
     * @return 订单金额
     */
    @ApiOperation(value = "根据订单查询订单金额")
    @GetMapping("/dine_in_order/get_order_fee_by_guid")
    BigDecimal getOrderFeeByGuid(@RequestParam("orderGuid") String orderGuid);

    @ApiOperation(value = "收款方式下拉")
    @GetMapping("/dine_in_order/list_payment_type_name")
    List<String> listPaymentTypeName();

    /**
     * 处理历史团购结算明细
     */
    @GetMapping("/groupon/handle")
    void handleTradeDetailHistory(@RequestParam("enterpriseGuid") String enterpriseGuid, @RequestParam("guid") Long guid);

    @PostMapping("/order_report/orderStatistics/businessData")
    BusinessDataRespDTO getOrderStatistics(@RequestBody JournalAppBaseReqDTO baseReq);

    @PostMapping("/order_report/orderStatistics/businessHisTrend")
    BusinessHisTrendRespDTO getBusinessHisTrend(@RequestBody JournalAppBaseReqDTO baseReq);

    @PostMapping("/order_report/orderStatistics/storeGatherBusiness")
    StoreGatherTotalRespDTO listStoreGatherBusiness(@RequestBody StoreGatherReportReqDTO baseReq);

    @PostMapping("/order_report/orderStatistics/screen_business_data")
    ScreenBusinessDataRespDTO screenBusinessData(@RequestBody JournalAppBaseReqDTO baseReq);

    @ApiOperation(value = "大屏销售报表")
    @PostMapping("/order_report/orderStatistics/screen_sale")
    SaleRespDTO getSaleCount(@RequestBody SaleCountReqDTO saleCountReqDTO);

    @ApiOperation(value = "门店销售收入TOP5")
    @PostMapping("/order_report/orderStatistics/screen_sale_store_statistics")
    StoreStatisticsAppRespDTO saleStoreStatistics(@RequestBody StoreStatisticsAppReqDTO storeStatisticsAppReqDTO);

    @ApiOperation(value = "销售额按小时分段统计")
    @PostMapping("/order_report/orderStatistics/screen_sale_by_hours_statistics")
    SaleStatisticsByHoursRespDTO saleByHoursStatistics(@RequestBody SaleStatisticsByHoursReqDTO statisticsByHoursReqDTO);

    @ApiOperation(value = "查询券退款信息", notes = "查询券退款信息")
    @PostMapping("/groupon/list_refund_by_request")
    List<AmountItemDTO> listRefundByRequest(@RequestBody StoreGatherReportReqDTO request);

    @ApiOperation(value = "订单统计查询收银员信息")
    @PostMapping("/order_report/orderStatistics/get_checkout_staff")
    List<UserBriefDTO> getCheckoutStaffs(@RequestBody BusinessOrderStatisticsQueryDTO businessOrderStatisticsQueryDTO);

    @ApiOperation(value = "修复团购支付记录丢失问题")
    @PostMapping("/order_detail/fix_groupon_transaction_record")
    void fixGrouponTransactionRecord(@RequestBody FixGrouponTransactionRecordReqDTO reqDTO);
}
