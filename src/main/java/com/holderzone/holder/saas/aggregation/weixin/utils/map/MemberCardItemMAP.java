package com.holderzone.holder.saas.aggregation.weixin.utils.map;

import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberCard;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberCardListOwned;
import com.holderzone.saas.store.dto.weixin.deal.MemberCardItemDTO;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberCardCacheDTO;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberSessionCardItemDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MemberCardItemMAP {

    MemberCardItemMAP INSTANCE = Mappers.getMapper(MemberCardItemMAP.class);

//    MemberCardItemDTO fromMemberCardOwn(MemberCardListOwnedRespDTO memberCardListOwnedRespDTO);

    MemberCardItemDTO fromResponseMemberCardOwn(ResponseMemberCardListOwned responseMemberCardListOwned);

//    List<MemberCardItemDTO> fromMemberCardOwnList(List<MemberCardListOwnedRespDTO> memberCardListOwnedRespDTOS);

//    MemberCardListItemDTO toMemberCardListItemDTO(MemberCardListOwnedRespDTO memberCardListOwnedRespDTO);

//    List<MemberCardListItemDTO> toMemberCardListItemDTOList(List<MemberCardListOwnedRespDTO> memberCardListOwnedRespDTOS);

    MemberCardItemDTO fromUserMemberSessionCardItem(UserMemberSessionCardItemDTO userMemberSessionCardItemDTO);

    List<MemberCardItemDTO> fromUserMemberSessionCardItemList(List<UserMemberSessionCardItemDTO> userMemberSessionCardItemDTOS);

    List<MemberCardItemDTO> fromResponseMemberCardList(List<ResponseMemberCardListOwned> responseMemberCardListOwneds);

    UserMemberCardCacheDTO toUserMemberCache(UserMemberSessionCardItemDTO userMemberSessionCardItemDTO);

    List<UserMemberCardCacheDTO> toUserMemberCacheList(List<UserMemberSessionCardItemDTO> userMemberSessionCardItemDTOS);

    List<UserMemberCardCacheDTO> newToUserMemberCacheList(List<ResponseMemberCard> userMemberSessionCardItemDTOS);

    UserMemberCardCacheDTO toUserMemberCacheNew(ResponseMemberCardListOwned responseMemberCardListOwned);

    List<UserMemberCardCacheDTO> toUserMemberCacheListNew(List<ResponseMemberCardListOwned> responseMemberCardListOwneds);
}
