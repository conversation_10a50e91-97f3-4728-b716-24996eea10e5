package com.holderzone.holder.saas.aggregation.merchant.service.rpc.queue;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.queue.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueItemController
 * @date 2019/03/27 17:06
 * @description //TODO
 * @program holder-saas-store-queue
 */
@FeignClient(name = "holder-saas-store-queue", fallbackFactory = QueueConfigClientService.ServiceFallBack.class)
@Api("排队操作")
public interface QueueConfigClientService {

    @ApiOperation("保存排队设置")
    @PostMapping("/queue/config")
     StoreConfigDTO config(@RequestBody StoreConfigDTO dto);

    @ApiOperation("获取队列配置")
    @GetMapping("/queue/config")
    StoreConfigDTO query(@RequestParam("storeGuid") String storeGuid);

    @Component
    class ServiceFallBack implements FallbackFactory<QueueConfigClientService> {
        private static final Logger logger = LoggerFactory.getLogger(QueueConfigClientService.class);
        @Override
        public QueueConfigClientService create(Throwable throwable) {
            return new QueueConfigClientService() {
                @Override
                public StoreConfigDTO config(StoreConfigDTO dto) {
                    logger.error("保存排队设置异常 ，e={}", throwable);
                    throw new BusinessException("保存排队设置异常!!" + throwable.getMessage());
                }

                @Override
                public StoreConfigDTO query(String storeGuid) {
                    logger.error("获取队列配置异常 ，e={}", throwable);
                    throw new BusinessException("获取队列配置异常!!" + throwable.getMessage());
                }
            };
        }
    }
}