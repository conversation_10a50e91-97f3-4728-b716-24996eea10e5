package com.holderzone.holder.saas.aggregation.merchant.controller.rights;


import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.rights.HsmSystemManagementService;
import com.holderzone.holder.saas.member.dto.rights.request.CardReqDTO;
import com.holderzone.holder.saas.member.dto.rights.request.HsmSystemManagementReqDTO;
import com.holderzone.holder.saas.member.dto.rights.request.QuerySystemDTO;
import com.holderzone.holder.saas.member.dto.rights.response.SystemRightDetailRespDTO;
import com.holderzone.holder.saas.member.dto.rights.response.SystemRightRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 体系管理表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-16
 */
@RestController
@RequestMapping("/hsm/system/management")
@Api(description = "会员体系")
@Slf4j
public class HsmSystemManagementController {


    @Resource
    private HsmSystemManagementService iHsmSystemManagementService;


    /**
     * 删除体系
     *
     * @param systemGuid guid
     * @return true-成功，false-失败
     */
    @ApiOperation(value = "删除体系", response = Boolean.class, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @DeleteMapping("/{systemGuid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "删除体系")
    public Result deleteSystem(
        @ApiParam(value = "体系ID", required = true) @PathVariable("systemGuid") String systemGuid) {
        return Result
            .buildSuccessResult(iHsmSystemManagementService.deleteSystem(systemGuid));
    }


    @ApiOperation("设置体系卡发放状态")
    @GetMapping("/change/system/{systemGuid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "设置体系卡发放状态")
    public Result<Boolean> changeSystemCardGrant(@ApiParam(value = "systemGuid",name = "体系guid")@PathVariable("systemGuid") String systemGuid,
                                                 @ApiParam(value = "state",name = "状态 0继续发放 1停止发放") @RequestParam("state") Integer state){
        log.info("[体系管理]-[改变发放状态]-[传入参数 systemGuid={},state={}]",systemGuid,state);
        return Result.buildSuccessResult(iHsmSystemManagementService.changeSystemCardGrant(systemGuid,state));
    }


    /**
     * 根据体系Guid检查该体系下是否有会员
     * @param systemGuid
     * @return
     */
    @ApiOperation("体系下是否有会员")
    @GetMapping("/check/hasmember/{systemGuid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "体系下是否有会员")
    public Result<Boolean> checkHasMember(@ApiParam(value = "systemGuid", name = "体系guid") @PathVariable("systemGuid") String systemGuid){
        log.info("[体系管理]-[体系下是否有会员]-[传入参数 systemGuid={}]",systemGuid);
        return Result.buildSuccessResult(iHsmSystemManagementService.checkHasMember(systemGuid));
    }
    /**
     * 查询体系的列表
     *
     * @param querySystemDTO 查询体系列表
     * @return 体系列表的信息
     */
    @ApiOperation(value = "查询体系的列表", response = SystemRightRespDTO.class, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/query")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "查询体系的列表")
    public Result querySystem(
        @ApiParam(value = "体系查询条件", required = true) @RequestBody QuerySystemDTO querySystemDTO) {
        return Result
            .buildSuccessResult(iHsmSystemManagementService.querySystem(querySystemDTO));
    }

    /**
     * 获取体系内的详情
     *
     * @param systemGuid 体系Guid
     * @return 体系列表的信息
     */
    @ApiOperation(value = "获取体系会员权益的详情", response = SystemRightDetailRespDTO.class, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/{systemGuid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "获取体系会员权益的详情")
    public Result getDetailOne(
        @ApiParam(value = "体系ID", required = true) @PathVariable("systemGuid") String systemGuid) {
        return Result
            .buildSuccessResult(iHsmSystemManagementService.getDetailOne(systemGuid));
    }

    /**
     * 修改名称
     *
     * @param hsmSystemManagementReqDTO 体系对象
     * @return true-成功，false-失败
     */
    @ApiOperation(value = "修改名称", response = Boolean.class, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/modify/name")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "修改名称")
    public Result modifyName(
        @ApiParam(value = "体系对象", required = true) @RequestBody HsmSystemManagementReqDTO hsmSystemManagementReqDTO) {
        return Result
            .buildSuccessResult(iHsmSystemManagementService
                .modifyName(hsmSystemManagementReqDTO));
    }

    /**
     * 修改门店列表
     *
     * @param hsmSystemManagementReqDTO 体系对象
     * @return true-成功，false-失败
     */
    @ApiOperation(value = "修改门店列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, response = Boolean.class)
    @PostMapping("/modify/shops")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "修改门店列表")
    public Result modifyShop(
        @ApiParam(value = "体系对象", required = true) @RequestBody HsmSystemManagementReqDTO hsmSystemManagementReqDTO) {
        return Result
            .buildSuccessResult(iHsmSystemManagementService
                .modifyShop(hsmSystemManagementReqDTO));
    }

    /**
     * 校验并且保存门店
     *
     * @param hsmSystemManagementReqDTO 体系对象
     * @return true-成功，false-失败
     */
    @ApiOperation(value = "校验并且保存门店", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, response = Boolean.class)
    @PostMapping("/check/modify/shops")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "校验并且保存门店")
    public Result checkAndModifyShop(
        @ApiParam(value = "体系对象", required = true) @RequestBody HsmSystemManagementReqDTO hsmSystemManagementReqDTO) {
        return Result
            .buildSuccessResult(iHsmSystemManagementService
                .checkAndModifyShop(hsmSystemManagementReqDTO));
    }

    /**
     * 校验信息（门店重复、名称）并且保存
     *
     * @param hsmSystemManagementReqDTO 对象
     * @return 返回体系Guid
     */
    @ApiOperation(value = "校验并且保存体系基本信息", response = HsmSystemManagementReqDTO.class, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/check/save")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "校验并且保存体系基本信息")
    public Result checkAndSaveBaseInfo(
        @ApiParam(value = "体系对象", required = true) @RequestBody HsmSystemManagementReqDTO hsmSystemManagementReqDTO) {
        return Result
            .buildSuccessResult(
                iHsmSystemManagementService.checkAndSaveBaseInfo(hsmSystemManagementReqDTO));
    }


    /**
     * 保存体系的基本信息
     *
     * @param hsmSystemManagementReqDTO 体系的基本信息
     * @return 返回体系的Guid
     */
    @ApiOperation(value = "保存体系基本信息", response = HsmSystemManagementReqDTO.class, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/save")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "保存体系基本信息")
    public Result saveBaseInfo(
        @ApiParam(value = "体系对象", required = true) @RequestBody @Validated HsmSystemManagementReqDTO hsmSystemManagementReqDTO) {
        return Result
            .buildSuccessResult(
                iHsmSystemManagementService.saveBaseInfo(hsmSystemManagementReqDTO));
    }

    /**
     * 跳过体系中主卡设置
     *
     * @param cardReqDTO 体系会员卡对象
     * @return 返回体系的Guid
     */
    @ApiOperation(value = "保存主卡信息", response = String.class, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/save/card")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "保存主卡信息")
    public Result saveCard(
        @Validated @ApiParam(value = "主卡信息", required = true) @RequestBody CardReqDTO cardReqDTO) {
        return Result
            .buildSuccessResult(iHsmSystemManagementService.saveCard(cardReqDTO));
    }

    /**
     * 通过会员卡Guid获取体系的Guid
     *
     * @return 会员卡的信息
     */
    @ApiOperation(value = "通过会员卡Guid获取体系的Guid", response = String.class, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/guid/{cardGuid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "通过会员卡Guid获取体系的Guid")
    public Result findSystemGuidByCardGuid(@PathVariable("cardGuid") String cardGuid) {
        return Result
            .buildSuccessResult(iHsmSystemManagementService.findSystemGuidByCardGuid(cardGuid));
    }

}
