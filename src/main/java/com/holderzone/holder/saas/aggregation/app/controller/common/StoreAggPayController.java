package com.holderzone.holder.saas.aggregation.app.controller.common;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.app.constant.Constant;
import com.holderzone.holder.saas.aggregation.app.helper.RedisHelper;
import com.holderzone.holder.saas.aggregation.app.service.feign.StoreAggPayClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInBillClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.OrderItemClientService;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.pay.*;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreAggPayController
 * @date 2019/03/20 14:35
 * @description
 * @program holder-saas-aggregation-app
 */
@Slf4j
@RestController
@RequestMapping("/agg")
@Api(tags = "聚合支付统一轮询入口")
public class StoreAggPayController {

    private final StoreAggPayClientService storeAggPayClientService;

    private final DineInBillClientService dineInBillClientService;

    private final OrderItemClientService orderItemClientService;

    private final RedisHelper redisHelper;

    @Autowired
    public StoreAggPayController(StoreAggPayClientService storeAggPayClientService, DineInBillClientService
            dineInBillClientService, OrderItemClientService orderItemClientService, RedisHelper redisHelper) {
        this.storeAggPayClientService = storeAggPayClientService;
        this.dineInBillClientService = dineInBillClientService;
        this.orderItemClientService = orderItemClientService;
        this.redisHelper = redisHelper;
    }

    @PostMapping("/pay")
    @ApiOperation("支付接口：快速收款模式(isQuickReceipt=true, enterpriseGuid, storeGuid, amount, authCode, terminalId 必传)")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PAY,
            description = "支付接口：快速收款模式", action = OperatorType.SELECT)
    public Result<AggPayRespDTO> pay(@RequestBody SaasAggPayDTO saasAggPayDTO) {
        log.info("支付接口 saasAggPayDTO:{}", JacksonUtils.writeValueAsString(saasAggPayDTO));
        String deviceId = saasAggPayDTO.getDeviceId();
        if (StringUtils.isEmpty(deviceId)) {
            AggPayRespDTO aggPayRespDTO = storeAggPayClientService.pay(saasAggPayDTO);
            return Result.buildSuccessResult(aggPayRespDTO);
        } else {
            String lockKey = "quick:agg:pay:";
            boolean lockSuccess = false;
            try {
                lockKey += deviceId;
                lockSuccess = redisHelper.setNxEx(lockKey, "1", 30);
                if (!lockSuccess) {
                    log.info("快速收款重复调用");
                    return null;
                }
                AggPayRespDTO aggPayRespDTO = storeAggPayClientService.pay(saasAggPayDTO);
                return Result.buildSuccessResult(aggPayRespDTO);
            }finally {
                if (lockSuccess) {
                    redisHelper.delete(lockKey);
                }
            }
        }
    }

    @PostMapping("/polling")
    @ApiOperation("轮询接口：看实体中paySt的描述，付款中则需要继续轮询")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PAY,
            description = "轮询接口：看实体中paySt的描述，付款中则需要继续轮询", action = OperatorType.SELECT)
    public Result<AggPayPollingRespDTO> polling(@RequestBody SaasPollingDTO saasPollingDTO) {
        log.info("轮询接口 saasPollingDTO:{}", JacksonUtils.writeValueAsString(saasPollingDTO));
        AggPayPollingRespDTO pollingRespDTO = storeAggPayClientService.polling(saasPollingDTO);
        if(pollingRespDTO != null){
            if(pollingRespDTO.getMsg().equals(Constant.PAYMENT_FAILED)){
                pollingRespDTO.setMsg(LocaleUtil.getMessage("PAYMENT_FAILED"));
            }
            if(pollingRespDTO.getMsg().equals(Constant.PAYMENT_SUCCESSFUL)){
                pollingRespDTO.setMsg(LocaleUtil.getMessage("PAYMENT_SUCCESSFUL"));
            }
        }
        return Result.buildSuccessResult(pollingRespDTO);
    }

    @PostMapping("/pad/polling")
    @ApiOperation("pad轮询接口：看实体中paySt的描述，付款中则需要继续轮询")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PAY,
            description = "pad轮询接口：看实体中paySt的描述，付款中则需要继续轮询", action = OperatorType.SELECT)
    public Result<AggPayPollingRespDTO> padPolling(@RequestBody SaasPollingDTO saasPollingDTO) {
        log.info("pad轮询接口 saasPollingDTO:{}", JacksonUtils.writeValueAsString(saasPollingDTO));
        OrderDTO orderDTO = orderItemClientService.findByOrderGuid(saasPollingDTO.getOrderGuid());
        if (Objects.equals(2, orderDTO.getUpperState())) {
            saasPollingDTO.setOrderGuid(orderDTO.getMainOrderGuid());
            log.info("拨乱反正 以子还主 saasPollingDTO={}", JacksonUtils.writeValueAsString(saasPollingDTO));
        }

        AggPayPollingRespDTO pollingRespDTO = storeAggPayClientService.polling(saasPollingDTO);
        return Result.buildSuccessResult(pollingRespDTO);
    }

    @PostMapping("/cancel")
    @ApiOperation("撤销聚合支付")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PAY,
            description = "撤销聚合支付", action = OperatorType.UPDATE)
    public Result<Integer> cancel(@RequestBody SaasPollingDTO saasPollingDTO) {
        Boolean result = dineInBillClientService.cancel(saasPollingDTO);
        if (result) {
            //撤销成功
            return Result.buildSuccessResult(1);
        }
        //已支付成功
        return Result.buildSuccessResult(2);
    }

    @ApiOperation("查询接口")
    @PostMapping("/query")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PAY,
            description = "查询接口", action = OperatorType.SELECT)
    public Result<AggPayPollingRespDTO> query(@RequestBody SaasPollingDTO saasPollingDTO) {
        return Result.buildSuccessResult(storeAggPayClientService.query(saasPollingDTO));
    }

    @ApiOperation("微信公众号支付轮询接口")
    @PostMapping("/wechat/public/polling")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PAY,
            description = "微信公众号支付轮询接口", action = OperatorType.SELECT)
    public Result<AggPayPollingRespDTO> pollingWeChatPublic(@RequestBody SaasPollingDTO saasPollingDTO) {
        return Result.buildSuccessResult(storeAggPayClientService.pollingWeChatPublic(saasPollingDTO));
    }

    @ApiOperation("查询接口")
    @PostMapping("/wechat/public/query")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PAY,
            description = "查询接口", action = OperatorType.SELECT)
    public Result<AggPayPollingRespDTO> queryWeChatPublic(@RequestBody SaasPollingDTO saasPollingDTO) {
        return Result.buildSuccessResult(storeAggPayClientService.queryWeChatPublic(saasPollingDTO));
    }

    @PostMapping("/refund")
    @ApiOperation("退款接口：\n" +
            "快速收款模式(isQuickReceipt=true, enterpriseGuid, storeGuid, payGUID, orderGUID, refundFee(元) 必传)\n" +
            "code=10000||20045 && state=1 表示退款成功，否则都是失败")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PAY,
            description = "退款接口", action = OperatorType.SELECT)
    public Result<AggRefundRespDTO> pay(@RequestBody SaasAggRefundDTO saasAggRefundDTO) {
        AggRefundRespDTO aggPayRespDTO = storeAggPayClientService.refund(saasAggRefundDTO);
        return Result.buildSuccessResult(aggPayRespDTO);
    }

    @ApiOperation("退款轮询接口，如果退款接口返回状态是state=3，那么需要轮询拿到结果")
    @PostMapping("/refund/polling")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PAY,
            description = "退款轮询接口", action = OperatorType.SELECT)
    public Result<AggRefundPollingRespDTO> refundPolling(@RequestBody SaasPollingDTO saasPollingDTO) {
        return Result.buildSuccessResult(storeAggPayClientService.refundPolling(saasPollingDTO));
    }

    @PostMapping("/record")
    @ApiOperation(value = "支付记录接口")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PAY,
// description = "支付记录接口",action = OperatorType.SELECT)
    public Result<Page<AggPayRecordDTO>> queryPayRecord(@RequestBody BasePageDTO basePageDTO) {
        return Result.buildSuccessResult(storeAggPayClientService.queryPayRecord(basePageDTO));
    }
    @PostMapping("/record/statistics")
    @ApiOperation(value = "支付记录接口 包含支付记录和统计总金额")
    public Result<AggPayStatisticsDTO> queryPayStatistics(@RequestBody BasePageDTO basePageDTO) {
        return Result.buildSuccessResult(storeAggPayClientService.queryPayStatistics(basePageDTO));
    }

    @PostMapping("/callback")
    @ApiOperation(value = "支付回调接口")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PAY,
            description = "支付回调接口", action = OperatorType.SELECT)
    public String callback(@RequestBody AggPayCallbackDTO aggPayCallbackDTO) {
        return storeAggPayClientService.callback(aggPayCallbackDTO);
    }
}
