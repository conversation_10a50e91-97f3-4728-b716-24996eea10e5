package com.holderzone.holder.saas.aggregation.merchant.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-07-13
 * @description
 */
@Data
public class StoreGatherExportDTO {

    private String date;

    private String brandName;

    private String storeName;

    private BigDecimal orderFee;

    private BigDecimal discountFee;

    private BigDecimal actuallyPayFee;

    private Integer orderCount;

    private BigDecimal orderAverageFee;

    private Integer guestCount;

    private BigDecimal guestAverageFee;

    private Integer orderAverageTime;
}
