package com.holderzone.holder.saas.aggregation.merchant.controller.rights;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.rights.SystemManagementGrowthSetClientService;
import com.holderzone.holder.saas.member.dto.rights.request.HsmGrowthSetReqDTO;
import com.holderzone.holder.saas.member.enums.rights.EnableTypeEnum;
import com.holderzone.holder.saas.member.enums.rights.GrowthValidityPeriodTypeEnum;
import com.holderzone.holder.saas.member.util.validation.ValidationUtils;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description  体系下的成长值设置管理
 * @date 2019/5/16 15:02
 */
@RestController
@RequestMapping("/hsm-system-management-growth-set")
@Api(description="体系下的成长值设置管理")
public class HsmSystemManagementGrowthSetController {
    /**
     * 二月
     */
    private static final  int month2=2;
    private static final  int month2end=28;
    @Autowired
    private SystemManagementGrowthSetClientService systemManagementGrowthSetClientService;

    /**
     * @Description   创建消费送成长值规则
     * <AUTHOR>
     * @Date   16:47
     * @param hsmGrowthSetReqDTO
     * @return com.holderzone.framework.response.Result
     */
    @ApiOperation(value = "创建消费送成长值规则", notes = "创建消费送成长值规则")
    @PostMapping(value = "/create", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "创建消费送成长值规则")
    public Result createGrowthRule(@RequestBody  @Validated HsmGrowthSetReqDTO hsmGrowthSetReqDTO)  {
        checkout(hsmGrowthSetReqDTO);

        return Result.buildSuccessResult(systemManagementGrowthSetClientService.createGrowthRule(hsmGrowthSetReqDTO));
    }

    private void checkout(HsmGrowthSetReqDTO hsmGrowthSetReqDTO){
        if(hsmGrowthSetReqDTO.getGrowthRuleReqDTO().getRuleState()== EnableTypeEnum.ENABLE_TYPE_YES.getCode()){
            ValidationUtils.validate(hsmGrowthSetReqDTO.getGrowthRuleReqDTO());
        }
        if(StringUtils.isEmpty(hsmGrowthSetReqDTO.getGrowthRuleReqDTO().getSystemManagementGuid())){
            throw new ParameterException("权益体系的guid不能为null");
        }
        if(GrowthValidityPeriodTypeEnum.VALIDITY_PERIOD_TYPE_YEAR.getCode()==hsmGrowthSetReqDTO.getValidityPeriodType()){
            Integer validityPeriodDay =hsmGrowthSetReqDTO.getValidityPeriodDay();
            Integer validityPeriodYear = hsmGrowthSetReqDTO.getValidityPeriodYear();
            Integer validityPeriodMonty = hsmGrowthSetReqDTO.getValidityPeriodMonty();

            if(month2==hsmGrowthSetReqDTO.getValidityPeriodMonty()){
                if(validityPeriodDay>month2end){
                    throw new BusinessException("月份为2月时,天数最多为28");
                }
            }else{
                if(validityPeriodDay<1||validityPeriodDay>31){
                    throw new BusinessException("天数在1~30之间");
                }
            }
            if (validityPeriodYear == null || validityPeriodYear == 0) {
                throw new BusinessException("有效期的年参数不合法");
            }
            if (validityPeriodMonty < 1 || validityPeriodMonty > 12) {
                throw new BusinessException("有效期的月份应在1~12");
            }

        }
    }

    /**
     * @Description   修改消费送成长值规则
     * <AUTHOR>
     * @Date   16:47
     * @param hsmGrowthSetReqDTO
     * @return com.holderzone.framework.response.Result
     */
    @ApiOperation(value = "修改消费送成长值规则", notes = "修改消费送成长值规则")
    @PostMapping(value = "/update", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "修改消费送成长值规则")
    public Result updateGrowthRule(@RequestBody @Validated(HsmGrowthSetReqDTO.update.class) HsmGrowthSetReqDTO hsmGrowthSetReqDTO)  {
        checkout(hsmGrowthSetReqDTO);
        if(systemManagementGrowthSetClientService.updateGrowthRule(hsmGrowthSetReqDTO)){
            return   Result.buildEmptySuccess();
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.CREATION_FAILED));
    }
}
