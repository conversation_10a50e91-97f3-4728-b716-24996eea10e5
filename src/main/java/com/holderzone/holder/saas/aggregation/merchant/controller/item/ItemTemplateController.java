package com.holderzone.holder.saas.aggregation.merchant.controller.item;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.item.ItemClientService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.ItemTemplateMenuDetailsReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemTemplateMenuSubitemReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemTemplateReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemTemplateSearchReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemTemplateMenuDetailRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemTemplateMenusRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemTemplatesRespDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemTemplateController
 * @date 2019/05/30 13:49
 * @description //TODO 商品销售模板相关接口
 * @program holder-saas-aggregation-app
 */
@RestController
@RequestMapping("/item_template")
@Api(description = "商品销售模板相关接口")
@Slf4j
@ResponseBody
public class ItemTemplateController {

    @Autowired
    ItemClientService itemClientService;

    @ApiOperation(value = "条件获取门店商品销售模板列表")
    @PostMapping("/select_store_item_templates")
    @ResponseBody@EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "条件获取门店商品销售模板列表")
    public Result<ItemTemplatesRespDTO> selectStoreItemTemplates(@RequestBody @Valid ItemTemplateSearchReqDTO request){
        log.info("条件查询门店商品模板接口入参,request={}", JacksonUtils.writeValueAsString(request));
       ItemTemplatesRespDTO itemTemplatesRespDTO = itemClientService.selectStoreItemTemplates(request);
        return Result.buildSuccessResult(itemTemplatesRespDTO);
    }


    @ApiOperation(value = "保存/更新/删除商品模板接口入参")
    @PostMapping("/save")
    @ResponseBody@EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "保存/更新/删除商品模板接口入参")
    public Result save(@RequestBody @Valid ItemTemplateReqDTO request){
        log.info("保存/更新/删除商品模板接口入参,request={}", JacksonUtils.writeValueAsString(request));
        Integer num = itemClientService.save(request);
        return Integer.valueOf(1).equals(num) ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVE_UPDATE_DELETE_FAILED));
    }


    @ApiOperation(value = "查询模板菜单列表接口入参")
    @PostMapping("/select_item_template_menus")
    @ResponseBody@EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "查询模板菜单列表接口入参")
    public Result<List<ItemTemplateMenusRespDTO>> selectItemTemplateMenus( @RequestBody @Valid SingleDataDTO request){
        log.info("查询模板菜单列表接口入参,request={}", JacksonUtils.writeValueAsString(request));
        List<ItemTemplateMenusRespDTO> menus  = itemClientService.selectItemTemplateMenus(request);
        return Result.buildSuccessResult(menus);
    }


    @ApiOperation(value = "新增模板—菜单")
    @PostMapping("/save_menu")
    @ResponseBody@EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "新增模板—菜单")
    public Result saveItemMenu( @RequestBody @Valid ItemTemplateMenuSubitemReqDTO request){
        log.info("查询模板菜单列表接口入参,request={}", JacksonUtils.writeValueAsString(request));
        Integer  num = itemClientService.saveItemMenu(request);
        return Integer.valueOf(1).equals(num) ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVE_FAILED));
    }


    @ApiOperation(value = "查询模板—菜单详情")
    @PostMapping("/select_item_template_menu_detail")
    @ResponseBody@EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "查询模板—菜单详情")
    public Result<ItemTemplateMenuDetailRespDTO> selectItemTemplateMenuDetail(@RequestBody @Valid ItemTemplateMenuDetailsReqDTO request){
        log.info("获取商品模板菜单详情接口入参,request={}", JacksonUtils.writeValueAsString(request));
        ItemTemplateMenuDetailRespDTO  details  = itemClientService.selectItemTemplateMenuDetail(request);
        return Result.buildSuccessResult(details);
    }

    @ApiOperation(value = "（批量）移除菜单下菜品")
    @PostMapping("/batch_remove")
    @ResponseBody@EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "（批量）移除菜单下菜品")
    public  Result menuSubItemBatchRemove(@RequestBody @Valid SingleDataDTO request){
        log.info("（批量）移除菜单下菜品接口入参,request={}", JacksonUtils.writeValueAsString(request));
        Integer num = itemClientService.menuSubItemBatchRemove(request);
        return Integer.valueOf(1).equals(num) ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.DELETION_FAILED));

    }
}
