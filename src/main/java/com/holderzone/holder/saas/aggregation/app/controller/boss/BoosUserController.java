package com.holderzone.holder.saas.aggregation.app.controller.boss;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.app.service.feign.staff.UserClientService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 老板助手 - 用户
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/boss/user")
public class BoosUserController {

    private final UserClientService userClientService;

    @ApiOperation(value = "获取用户权限")
    @GetMapping("/get_source_code")
    public Result<List<String>> queryUserSourceOnOut() {
        UserContext userContext = UserContextUtils.get();
        return Result.buildSuccessResult(userClientService.queryUserSourceOnOut(4, userContext.getUserGuid(), userContext.getEnterpriseGuid()));
    }
}
