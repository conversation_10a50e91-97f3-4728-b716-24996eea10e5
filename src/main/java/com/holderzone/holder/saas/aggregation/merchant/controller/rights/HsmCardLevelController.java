package com.holderzone.holder.saas.aggregation.merchant.controller.rights;


import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.rights.CardLevelClientService;
import com.holderzone.holder.saas.member.dto.rights.request.GrowthLevelReqDTO;
import com.holderzone.holder.saas.member.dto.rights.response.GrowthLevelRespDTO;
import com.holderzone.holder.saas.member.dto.rights.response.MemberRightRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 卡等级表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-21
 */
@RestController
@RequestMapping("/hsm-card-level")
@Api(description = "卡等级")
public class HsmCardLevelController {

    @Autowired
    private CardLevelClientService cardLevelClientService;

    /**
     * @return com.holderzone.framework.response.Result
     * @Description 查询门店信息
     * <AUTHOR>
     * @Date 2019/5/20 15:17
     */
    @ApiOperation(value = "查询体系下的卡等级", notes = "查询体系下的卡等级,返回对象为数组", response = GrowthLevelRespDTO.class)
    @GetMapping(value = "/queryCardLevelBySystemManagementGuid", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "查询体系下的卡等级")
    public Result queryCardLevelBySystemManagementGuid(
        @ApiParam(value = "体系ID", required = true) String sytemMangementGuid) {

        return Result.buildSuccessResult(
            cardLevelClientService.queryCardLevelBySystemManagementGuid(sytemMangementGuid));
    }

    /**
     * 保存/更新会员等级
     *
     * @param growthLevelReqDTO 会员等级对象
     */
    @ApiOperation(value = "保存/更新会员等级", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/save")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "保存/更新会员等级")
    public Result saveOrUpdateLevel(
        @ApiParam(value = "会员等级", required = true) @Validated @RequestBody GrowthLevelReqDTO growthLevelReqDTO) {
        cardLevelClientService.saveOrUpdateLevel(growthLevelReqDTO);
        return Result.buildEmptySuccess();
    }

    /**
     * 通过会员等级Guid进行删除（默认的等级不可以进行删除）
     *
     * @param levelGuid 会员等级Guid
     */
    @ApiOperation(value = "删除会员等级", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @DeleteMapping("/{levelGuid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "删除会员等级")
    public Result delLevel(
        @ApiParam(value = "等级Guid", required = true) @PathVariable("levelGuid") String levelGuid) {
        cardLevelClientService.delLevel(levelGuid);
        return Result.buildEmptySuccess();
    }

    /**
     * 复制会员权益
     *
     * @param levelGuid 会员等级Guid
     */
    @ApiOperation(value = "复制权益", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/copy/{levelGuid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "复制权益")
    public Result copyRight(
        @ApiParam(value = "等级Guid", required = true) @PathVariable("levelGuid") String levelGuid) {
        cardLevelClientService.copyRight(levelGuid);
        return Result.buildEmptySuccess();
    }

    /**
     * 获取权益列表
     *
     * @param levelGuid 等级Guid
     * @return 权益列表
     */
    @ApiOperation(value = "获取权益列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, response = MemberRightRespDTO.class)
    @GetMapping("/right/{levelGuid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "获取权益列表")
    public Result findRight(
        @ApiParam(value = "等级Guid", required = true) @PathVariable("levelGuid") String levelGuid) {
        return Result.buildSuccessResult(cardLevelClientService.findRight(levelGuid));
    }

    /**
     * 获取会员等级
     *
     * @param cardGuid 会员卡Guid
     * @return 会员等级
     */
    @ApiOperation(value = "获取会员等级", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, response = GrowthLevelRespDTO.class)
    @GetMapping(value = "/list/{cardGuid}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "获取会员等级")
    public Result findListByCardGuid(@PathVariable("cardGuid") String cardGuid) {
        return Result.buildSuccessResult(cardLevelClientService.findListByCardGuid(cardGuid));
    }
}
