package com.holderzone.holder.saas.aggregation.weixin.service.rpc.memberCenter;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolume;
import com.holderzone.saas.store.dto.weixin.member.WxMemberInfoVolumeDetailsRespDTO;
import com.holderzone.saas.store.dto.weixin.member.WxMemberVolumeInfoListReqDTO;
import com.holderzone.saas.store.dto.weixin.member.WxVolumeDetailReqDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;

@Service
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = WxMemberCenterVolumeService.WxMemberCenterVolumeServiceFallBack.class)
public interface WxMemberCenterVolumeService {

    String URL_PREFIX = "/wx_member_center_volume";

    @PostMapping(URL_PREFIX + "/volume_list")
    ResponseMemberInfoVolume volumeInfoList(WxMemberVolumeInfoListReqDTO wxMemberVolumeInfoListReqDTO);

    @PostMapping(URL_PREFIX + "/volume_details")
    WxMemberInfoVolumeDetailsRespDTO volumeCodeDetails(WxVolumeDetailReqDTO wxVolumeDetailReqDTO);


    @Slf4j
    @Component
    class WxMemberCenterVolumeServiceFallBack implements FallbackFactory<WxMemberCenterVolumeService> {
        @Override
        public WxMemberCenterVolumeService create(Throwable throwable) {
            return new WxMemberCenterVolumeService() {


                @Override
                public ResponseMemberInfoVolume volumeInfoList(WxMemberVolumeInfoListReqDTO wxMemberVolumeInfoListReqDTO) {
                    log.error("查询优惠券列表失败:{}", wxMemberVolumeInfoListReqDTO);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public WxMemberInfoVolumeDetailsRespDTO volumeCodeDetails(WxVolumeDetailReqDTO wxVolumeDetailReqDTO) {
                    log.error("查询优惠券详情失败:{}", wxVolumeDetailReqDTO);
                    throw new BusinessException(throwable.getMessage());
                }

            };
        }
    }
}
