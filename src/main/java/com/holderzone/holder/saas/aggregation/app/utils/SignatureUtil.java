package com.holderzone.holder.saas.aggregation.app.utils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;


/**
 * <AUTHOR>
 * @date 2024/12/10
 * @description ipass签名
 */
public class SignatureUtil {

    private SignatureUtil() {
    }

    /**
     * 签名算法
     */
    public static final String HMAC_SHA_256 = "HmacSHA256";

    /**
     * 生成签名
     *
     * @param data   待签名数据
     * @param secret 密钥
     * @return 签名
     */
    public static String generateSignature(String data, String secret) throws NoSuchAlgorithmException, InvalidKeyException {
        Mac sha256Hmac = Mac.getInstance(HMAC_SHA_256);
        SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), HMAC_SHA_256);
        sha256Hmac.init(secretKey);
        byte[] hash = sha256Hmac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hash);
    }

    /**
     * 校验签名
     *
     * @param data      待验签数据
     * @param secret    密钥
     * @param signature 签名
     */
    public static boolean valid(String data, String secret, String signature) throws NoSuchAlgorithmException, InvalidKeyException {
        return signature != null && secret != null && signature.equals(generateSignature(data, secret));
    }

}