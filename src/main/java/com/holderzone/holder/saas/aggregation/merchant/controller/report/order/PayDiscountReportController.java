package com.holderzone.holder.saas.aggregation.merchant.controller.report.order;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.order.PayClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.order.ReportExportClientService;
import com.holderzone.saas.store.dto.report.query.DiscountQueryDTO;
import com.holderzone.saas.store.dto.report.query.HandOverQueryDTO;
import com.holderzone.saas.store.dto.report.query.PayTypeCountQueryDTO;
import com.holderzone.saas.store.dto.report.resp.*;
import com.holderzone.saas.store.dto.trade.PaymentTypeDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PayDiscountReportController
 * @date 2018/10/11 9:10
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Api("折扣，支付，交接班报表")
@RestController
@RequestMapping("/pay/report")
@Deprecated
public class PayDiscountReportController {

    private final PayClientService payClientService;

    private static final Logger logger = LoggerFactory.getLogger(PayDiscountReportController.class);

    @Autowired
    public PayDiscountReportController(PayClientService payClientService) {
        this.payClientService = payClientService;
    }

    @ApiOperation(value = "支付方式报表统计")
    @PostMapping("/type")
    @Deprecated
    public Result<PaymentTypeRespDTO> type(@RequestBody PayTypeCountQueryDTO payTypeCountQueryDTO) {
        logger.info("查询支付方式报表 payTypeCountQueryDTO={}", JacksonUtils.writeValueAsString(payTypeCountQueryDTO));
        PaymentTypeRespDTO paymentTypeRespDTO = payClientService.queryPayType(payTypeCountQueryDTO);
        return Result.buildSuccessResult(paymentTypeRespDTO);
    }

    @ApiOperation(value = "优惠方式报表统计")
    @PostMapping("/discount")
    @Deprecated
    public Result<DiscountRespDTO> discount(@RequestBody DiscountQueryDTO discountQueryDTO) {
        logger.info("优惠方式报表统计 discountQueryDTO={}", JacksonUtils.writeValueAsString(discountQueryDTO));
        DiscountRespDTO discountRespDTO = payClientService.queryDiscount(discountQueryDTO);
        return Result.buildSuccessResult(discountRespDTO);
    }

    @ApiOperation(value = "支付方式详情报表")
    @PostMapping("/type/detail")
    @Deprecated
    public Result<Page<PayDetailRespDTO>> typeDetail(@RequestBody PayTypeCountQueryDTO payTypeCountQueryDTO) {
        logger.info("支付方式详情报表 payTypeCountQueryDTO={}", JacksonUtils.writeValueAsString(payTypeCountQueryDTO));
        Page<PayDetailRespDTO> payDetailRespDTOPage = payClientService.queryPayDeatil(payTypeCountQueryDTO);
        return Result.buildSuccessResult(payDetailRespDTOPage);
    }

    @ApiOperation(value = "折扣详情报表接口")
    @PostMapping("/discount/detail")
    @Deprecated
    public Result<Page<DiscountDetailRespDTO>> discountDetail(@RequestBody DiscountQueryDTO discountQueryDTO) {
        logger.info("折扣详情报表接口 discountQueryDTO={}", JacksonUtils.writeValueAsString(discountQueryDTO));
        Page<DiscountDetailRespDTO> discountDetailRespDTOPage = payClientService.queryDiscountDetail(discountQueryDTO);
        return Result.buildSuccessResult(discountDetailRespDTOPage);
    }


    @ApiOperation(value = "查询支付方式")
    @PostMapping("/type/name")
    @Deprecated
    public Result<Set<String>> getPayTypeName(@RequestBody List<String> storeGuid) {
        logger.info("查询支付方式 storeGuids={}", storeGuid);
        Set<String> paymentTypeDTOS = payClientService.queryPayTypeName(storeGuid);
        return Result.buildSuccessResult(paymentTypeDTOS);
    }

    @ApiOperation(value = "交接班记录报表查询接口")
    @PostMapping("/hand/query")
    @Deprecated
    public Result<Page<HandoverRespDTO>> query(@RequestBody HandOverQueryDTO handOverQueryDTO) {
        logger.info("交接班记录报表查询接口 handOverQueryDTO={}", JacksonUtils.writeValueAsString(handOverQueryDTO));
        Page<HandoverRespDTO> handoverRespDTOPage = payClientService.handoverQuery(handOverQueryDTO);
        return Result.buildSuccessResult(handoverRespDTOPage);
    }

    @ApiOperation(value = "查询交接班详情记录")
    @PostMapping("/hand/query/detail/{handoverRecordGuid}")
    @Deprecated
    public Result<HandoverDetailRespDTO> detail(@PathVariable("handoverRecordGuid") String handoverRecordGuid) {
        logger.info("查询交接班详情记录 handoverRecordGuid={}", handoverRecordGuid);
        HandoverDetailRespDTO handoverDetailRespDTO = payClientService.handoverDetail(handoverRecordGuid);
        return Result.buildSuccessResult(handoverDetailRespDTO);
    }


}
