package com.holderzone.holder.saas.aggregation.merchant.controller.report.takeaway;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.takeaway.TakeawayClientService;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.report.TakeawayOrderDTO;
import com.holderzone.saas.store.dto.report.TakeawayOrderDateDetail;
import com.holderzone.saas.store.dto.report.query.TakeawayIndexQueryDTO;
import com.holderzone.saas.store.dto.report.resp.TakeawayDishRespDTO;
import com.holderzone.saas.store.dto.report.resp.TakeawayStoreRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeawayController
 * @date 2018/12/06 15:51
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Api(tags = "外卖首页",hidden = true)
@RestController
@RequestMapping("/takeaway/index")
@Deprecated
public class TakeawayController {

    private static final Logger logger = LoggerFactory.getLogger(TakeawayController.class);

    private final TakeawayClientService takeawayClientService;

    @Autowired
    public TakeawayController(TakeawayClientService takeawayClientService) {
        this.takeawayClientService = takeawayClientService;
    }

    /**
     * 菜品销售排序
     *
     * @param takeawayIndexQueryDTO
     * @return
     */
    @ApiOperation(value = "菜品销售排序")
    @PostMapping("/dish/sort")
    @Deprecated
    public Result<Page<TakeawayDishRespDTO>> dishSort(@RequestBody TakeawayIndexQueryDTO takeawayIndexQueryDTO) {
        logger.info("查询外卖订单菜品排名:{}", JacksonUtils.writeValueAsString(takeawayIndexQueryDTO));
        Page<TakeawayDishRespDTO> takeawayDishRespDTOPage = takeawayClientService.queryDish(takeawayIndexQueryDTO);
        return Result.buildSuccessResult(takeawayDishRespDTOPage);
    }

    /**
     * 门店销售排序
     *
     * @param takeawayIndexQueryDTO
     * @return
     */
    @ApiOperation(value = "门店销售排序")
    @PostMapping("/store/sort")
    @Deprecated
    public Result<Page<TakeawayStoreRespDTO>> storeSort(@RequestBody TakeawayIndexQueryDTO takeawayIndexQueryDTO) {
        logger.info("查询外卖订单门店排名:{}", JacksonUtils.writeValueAsString(takeawayIndexQueryDTO));
        Page<TakeawayStoreRespDTO> page = takeawayClientService.queryStore(takeawayIndexQueryDTO);
        return Result.buildSuccessResult(page);
    }

    @ApiOperation(value = "外卖门店查询")
    @PostMapping("/storeInfo")
    @Deprecated
    public Result<List<StoreDTO>> storeInfo() {
        logger.info("查询外卖订单门店排名:{}", JacksonUtils.writeValueAsString(UserContextUtils.get()));
        List<StoreDTO> takeawayStoreInfo = takeawayClientService.getTakeawayStoreInfo();
        return Result.buildSuccessResult(takeawayStoreInfo);
    }

    @ApiOperation("外卖订单5个指标")
    @PostMapping("/orderInfo")
    @Deprecated
    public Result<TakeawayOrderDTO> orderInfo(@RequestBody TakeawayIndexQueryDTO takeawayIndexQueryDTO) {
        logger.info("查询外卖订单5个指标入参:{}", JacksonUtils.writeValueAsString(takeawayIndexQueryDTO));
        return Result.buildSuccessResult(takeawayClientService.selectOrderInfo(takeawayIndexQueryDTO));
    }

    @ApiOperation("外卖订单基于给定时间段统计各个指标接口")
    @PostMapping("/orderInfoDetailByDate")
    @Deprecated
    public Result<TakeawayOrderDateDetail> orderInfoDetailByDate(@RequestBody TakeawayIndexQueryDTO takeawayIndexQueryDTO) {
        return Result.buildSuccessResult(takeawayClientService.selectOrderInfoDetail(takeawayIndexQueryDTO));
    }

}
