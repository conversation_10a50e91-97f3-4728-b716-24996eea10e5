package com.holderzone.holder.saas.aggregation.merchant.controller.table;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.AreaService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.table.AreaDTO;
import com.holderzone.saas.store.dto.trade.StoreDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AreaController
 * @date 2019/01/07 10:45
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Api(description = "区域服务Api")
@RestController
@RequestMapping("/area")
public class AreaController {

    private static final Logger logger = LoggerFactory.getLogger(AreaController.class);

    private final AreaService areaService;

    @Autowired
    public AreaController(AreaService areaService) {
        this.areaService = areaService;
    }

    @ApiOperation(value = "新增区域")
    @PostMapping("/add")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "新增区域")
    public Result<String> addArea(@RequestBody AreaDTO areaDTO) {
        logger.info("新增区域 areaDTO={}", JacksonUtils.writeValueAsString(areaDTO));
        String result = areaService.addArea(areaDTO);
        if ("success".equalsIgnoreCase(result)) {
            return Result.buildSuccessResult(result);
        }
        return Result.buildOpFailedResult(result);
    }

    @ApiOperation(value = "更新区域")
    @PostMapping("/update")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "更新区域")
    public Result<String> updateArea(@RequestBody AreaDTO areaDTO) {
        logger.info("更新区域 areaDTO={}", JacksonUtils.writeValueAsString(areaDTO));
        String result = areaService.updateArea(areaDTO);
        if ("success".equalsIgnoreCase(result)) {
            return Result.buildSuccessResult(result);
        }
        return Result.buildOpFailedResult(result);
    }

    @ApiOperation(value = "删除区域,传入业务主键进行删除")
    @PostMapping("/delete")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "删除区域,传入业务主键进行删除")
    public Result<String> delete(@RequestBody AreaDTO areaDTO) {
        logger.info("删除区域 areaDTO={}", JacksonUtils.writeValueAsString(areaDTO));
        String result = areaService.delete(areaDTO.getGuid());
        if ("success".equalsIgnoreCase(result)) {
            return Result.buildSuccessResult(result);
        }
        return Result.buildOpFailedResult(result);
    }

    @ApiOperation(value = "查询区域")
    @PostMapping("/query/all")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "查询区域")
    public Result<List<AreaDTO>> query(@RequestBody StoreDTO storeDTO) {
        logger.info("查询当前门店区域信息 storeDTO={}", JacksonUtils.writeValueAsString(storeDTO));
        List<AreaDTO> areaDTOS = areaService.queryAll(storeDTO.getStoreGuid());
        return Result.buildSuccessResult(areaDTOS);
    }

}
