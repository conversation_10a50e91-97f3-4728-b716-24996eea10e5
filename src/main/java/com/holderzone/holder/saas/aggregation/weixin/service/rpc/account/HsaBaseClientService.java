package com.holderzone.holder.saas.aggregation.weixin.service.rpc.account;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.weixin.config.FeignLoggerConfig;
import com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel;
import com.holderzone.holder.saas.aggregation.weixin.entity.dto.RequestTravelDetail;
import com.holderzone.holder.saas.member.wechat.dto.activitie.RequestMarketActivityUse;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseClientMarketActivity;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseMarketActivitieRule;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseMarketActivityUse;
import com.holderzone.holder.saas.member.wechat.dto.base.RequestMemberPasswore;
import com.holderzone.holder.saas.member.wechat.dto.base.RequestMemberPhoneSmsCode;
import com.holderzone.holder.saas.member.wechat.dto.card.*;
import com.holderzone.holder.saas.member.wechat.dto.coupon.ResponseVolumeInfo;
import com.holderzone.holder.saas.member.wechat.dto.coupon.ResponseVolumeList;
import com.holderzone.holder.saas.member.wechat.dto.label.RequestManualLabel;
import com.holderzone.holder.saas.member.wechat.dto.label.ResponseMemberLabel;
import com.holderzone.holder.saas.member.wechat.dto.member.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 微信端会员基础操作FeignClient
 *
 * <AUTHOR>
 * @date 2019/6/25 15:54
 */
@Component
@FeignClient(name = "holder-saas-member-wechat",
        configuration = FeignLoggerConfig.class,
        fallbackFactory = HsaBaseClientService.HsaBaseClientServiceFallBack.class)
public interface HsaBaseClientService {

    @ApiOperation(value = "发送登录验证码")
    @GetMapping("/hsa-base/sendLoginSmsCode")
    ResponseModel<Boolean> sendLoginSmsCode(@ApiParam(name = "phoneNum", value = "会员电话号码", required = true) @RequestParam(value = "phoneNum", required = true) String phoneNum);

    @ApiOperation(value = "手机登陆或注册会员")
    @PostMapping("/hsa-base/registerOrLoginMemberInfo")
    ResponseModel<Boolean> registerOrLoginMemberInfo(@ApiParam(value = "手机登陆或注册会员参数", required = true) @RequestBody RequestMemberBasic requestMemberBasic);

    @ApiOperation(value = "修改会员信息")
    @PostMapping("/hsa-member/updateMemberInfo")
    ResponseModel<Boolean> updateMemberInfo(@ApiParam(value = "修改会员信息对象", required = true) @Valid @RequestBody RequestUpdateMemberInfo requestUpdateMemberInfo);

    @ApiOperation(value = "查询会员信息")
    @GetMapping("/hsa-member/getMemberInfo")
    ResponseModel<ResponseMemberInfo> getMemberInfo(@RequestBody RequestQueryMemberInfo requestQueryMemberInfo);


    @ApiOperation(value = "通吃岛.赚餐|MIN-APP-->会员主卡充值", notes = "通吃岛.赚餐|MIN-APP-->会员主卡充值")
    @PostMapping(value = "/hsa-member/memberCardRecharge", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    ResponseModel<ResponseRecharge> memberCardRecharge(@Validated @RequestBody RequestMemberCardRecharge request);

    @ApiOperation(value = "查询会员是否被禁用")
    @GetMapping("/hsa-member/getMemberState")
    ResponseModel<ResponseAccountStatus> getMemberState(@ApiParam(name = "phoneNumOrOpenid", value = "会员电话号码或微信OpenId", required = true) @RequestParam(value = "phoneNumOrOpenid", required = true) String phoneNumOrOpenid,
                                                        @ApiParam(name = "enterpriseGuid", value = "企业Guid", required = true) @RequestParam(value = "enterpriseGuid", required = true) String enterpriseGuid,
                                                        @ApiParam(name = "operSubjectGuid", value = "运营主体guid", required = true) @RequestParam(value = "operSubjectGuid", required = true) String operSubjectGuid);

    @ApiOperation(value = "查看会员卡资金明细")
    @GetMapping(value = "/hsa-member/getFundingTravelDetails")
    ResponseModel<ResponseTravelDetails> getFundingTravelDetails(@RequestBody RequestTravelDetail requestTravelDetail);

    @ApiOperation(value = "查看会员卡成长值明细")
    @GetMapping(value = "/hsa-member/getGrowthValueDetails")
    ResponseModel<ResponseTravelDetails> getGrowthValueDetails(@RequestBody RequestTravelDetail requestTravelDetail);

    @ApiOperation(value = "查看会员卡积分明细")
    @GetMapping(value = "/hsa-member/getIntegralTravelDetails")
    ResponseModel<ResponseTravelDetails> getIntegralTravelDetails(@RequestBody RequestTravelDetail requestTravelDetail);

    @ApiOperation(value = "修改会员支付密码")
    @PostMapping("/hsa-member/updateMemberPassword")
    ResponseModel<Boolean> updateMemberPassword(@RequestBody @Valid RequestMemberPasswore hsaMemberPassworeReqDTO);

    @ApiOperation(value = "更换主账号发送手机验证码")
    @GetMapping("/hsa-member/sendChangeSmsCode")
    ResponseModel<Boolean> sendChangeSmsCode(
            @ApiParam(value = "旧手机号", required = true) @RequestParam(value = "oldPhoneNum", required = true) String oldPhoneNum,
            @ApiParam(value = "新手机号", required = true) @RequestParam(value = "newPhoneNum", required = true) String newPhoneNum,
            @ApiParam(name = "enterpriseGuid", value = "企业Guid", required = true) @RequestParam(value = "enterpriseGuid", required = true) String enterpriseGuid);

    @ApiOperation(value = "会员更换手机号码（重新绑定新账号）")
    @PostMapping("/hsa-member/bindChangeMemberPhone")
    ResponseModel<Boolean> bindChangeMemberPhone(@ApiParam(value = "会员更换手机号码参数对象", required = true) @RequestBody RequestMemberPhoneSmsCode hsmMemberPhoneSmsCodeReqDTO);

    @ApiOperation(value = "获取二维码")
    @GetMapping("/hsa-member/getQrcode")
    ResponseModel<String> getMemberQrcode(@ApiParam(name = "key", value = "会员二维码获取KEY", required = true) @RequestParam(value = "key", required = true) String key);

    @ApiOperation(value = "查询会员卡二维码")
    @GetMapping(value = "/hsa-member/queryMemberMainCardQRCode")
    ResponseModel<String> queryMemberMainCardQRCode(@ApiParam(name = "phoneNum", value = "会员电话号码", required = true)
                                                    @RequestParam("phoneNum") String phoneNum);

    @ApiOperation(value = "会员卡概要信息")
    @PostMapping(value = "/hsa-member/getMemberCardSummaryInfo")
    ResponseModel<ResponseMemberCardSummary> getMemberCardSummaryInfo(@RequestBody RequestMemberCardSummaryQuery requestMemberCardSummaryQuery);


    @ApiOperation("查看会员卡积分配置规则")
    @GetMapping(value = "/hsa-member/getIntegralRule")
    ResponseModel<ResponseIntegralRule> getIntegralRule(@RequestParam(value = "systemManagementGuid", required = false) String systemManagementGuid,
                                                        @RequestParam("cardGuid") String cardGuid);

    @ApiOperation("查看会员卡余额配置规则")
    @GetMapping(value = "/hsa-member/getFundingRule")
    ResponseModel<ResponseFundingRule> getFundingRule(@RequestParam("memberInfoCardGuid") String memberInfoCardGuid,
                                                      @RequestParam("cardGuid") String cardGuid);

    @ApiOperation("新的查询会员卡权益等级概要信息")
    @PostMapping("/hsa-member/getNewCardLevelSummaryInfo")
    ResponseModel<ResponseCardLevelSummaryNew> getNewCardLevelSummaryInfo(@RequestBody RequestMemberCardSummaryQuery memberCardSummaryQueryReqDTO);

    @ApiOperation("查看会员卡成长值配置规则")
    @GetMapping(value = "/hsa-member/getGrowthValueRule")
    ResponseModel<ResponseGrowthValueRule> getGrowthValueRule(@RequestParam(value = "systemManagementGuid", required = false) String systemManagementGuid,
                                                              @RequestParam("cardGuid") String cardGuid);

    @ApiOperation("会员卡充值金额规则")
    @PostMapping(value = "/hsa-member/getCardSimpleRechargeRule")
    ResponseModel<ResponseCardRechargeRuleSummary> getCardSimpleRechargeRule(@RequestBody RequestCardRechargeRule cardRechargeRuleReqDTO);

    @ApiOperation("查询会员优惠券列表")
    @PostMapping(value = "/hsa-member-volume/getMemberVolume")
    ResponseModel<ResponseMemberInfoVolume> getMemberVolume(RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO);

    @ApiOperation("查看优惠券详情")
    @RequestMapping(value = "/hsa-member-volume/getMemberVolumeDetails")
    ResponseModel<ResponseMemberInfoVolumeDetails> getMemberVolumeDetails(@RequestParam("memberVolumeGuid") String memberVolumeGuid);

    @ApiOperation("获取优惠券类型")
    @GetMapping("/hsa-member-volume/getVolumeTypes")
    ResponseModel<List<ResponseMemberSourceType>> getVolumeTypes();

    @ApiOperation("查找会员卡及未开通卡信息")
    @PostMapping(value = "/hsa-card/getMemberCard")
    ResponseModel<ResponseMemberCardAll> getMemberCard(@RequestBody RequestQueryMemberCardList requestQueryMemberCardList);

    @ApiOperation("查询会员主卡信息")
    @GetMapping(value = "/hsa-member/queryMemberMainCard")
    ResponseModel<ResponseMemberAndCardInfo> getMemberMainCardByMemberInfoGuid(@RequestParam("memberInfoGuid") String memberInfoGuid);

    @ApiOperation("分页查找会员卡及未开通个卡信息")
    @PostMapping("/hsa-card/getMemberCardByPage")
    ResponseModel<Page<ResponseMemberCardListOwned>> getMemberCardByPage(@RequestBody @Validated RequestCardOwnedPage memberCardsOwnedPageReqDTO);


    @ApiOperation("根据卡Guid查询卡等级即每个等级权益信息")
    @GetMapping("/hsa-card/getCardLevelAndRightInfo")
    ResponseModel<List<CardLevelList>> getCardLevelAndRightInfo(@RequestParam("cardGuid") String cardGuid);

    @ApiOperation(value = "查询会员卡是否有会员价权益/是否冻结")
    @GetMapping("/hsa-member/{memberInfoCardGuid}/hasMemberPrice")
    ResponseModel<Boolean> hasMemberPrice(@PathVariable("memberInfoCardGuid") String memberInfoCardGuid);


    @ApiOperation("查询会员卡权益详情")
    @PostMapping("/hsa-card/getCardRightDetails")
    ResponseModel<List<ResponseCardRight>> getCardRightDetails(@RequestBody @Validated RequestCardRightDetails requestCardRightDetails);

    @GetMapping("/hsa-member-volume/consumeVolumeList")
    @ApiOperation(value = "优惠劵的已校验列表---返回集合", response = ResponseVolumeList.class)
    ResponseModel<List<ResponseVolumeList>> consumeVolumeList(@ApiParam("外部订单guid") @RequestParam("orderNumber") String orderNumber);

    @ApiOperation("未开通卡的等级及所有权益")
    @PostMapping("/hsa-card/getNotOpenCardLevelRights")
    ResponseModel<ResponseNotOpenCardLevelRights> getNotOpenCardLevelRights(@RequestBody @Validated RequestMemberCardSummaryQuery requestMemberCardSummaryQuery);

    @ApiOperation(value = "查询openId绑定的账号(兼容以前只有openId的账号)")
    @GetMapping("/hsa-base/queryMemberByOpenId")
    ResponseModel<List<ResponseMemberAccount>> queryMemberByOpenId(@ApiParam(name = "openId", value = "微信openId") @RequestParam(value = "openId") String openId);

    @ApiOperation(value = "通过手机号登录")
    @PostMapping("/hsa-base/loginByPhone")
    ResponseModel<ResponseLoginResult> loginByPhone(@ApiParam(value = "通过手机号登录参数", required = true) @RequestBody RequestMemberLogin request);

    @ApiOperation(value = "通过手机号注册并登录会员")
    @PostMapping("/hsa-base/registerAndLogin")
    ResponseModel<ResponseLoginResult> registerAndLogin(@ApiParam(value = "通过手机号注册并登录会员参数", required = true) @RequestBody RequestMemberBasic request);

    @ApiOperation("开通会员卡（权益卡）")
    @PostMapping("/hsa-member/openMemberCard")
    ResponseModel<Boolean> openMemberCard(@Validated @RequestBody RequestMemberCardOpen requestMemberCardOpen);

    @ApiOperation(value = "绑定主账号发送手机验证码")
    @GetMapping("/hsa-base/sendBindSmsCode")
    ResponseModel<Boolean> sendBindSmsCode(@ApiParam(value = "手机号", required = true) @RequestParam(value = "phoneNum", required = true) String phoneNum, @ApiParam(value = "企业Guid", required = true) @RequestParam(value = "enterpriseGuid", required = true) String enterpriseGuid);

    @ApiOperation(value = "设置openId对应的登陆状态(openId对应多个账号时的业务处理)")
    @PostMapping("/hsa-base/setOpenIdLoginStatus")
    ResponseModel<Boolean> setOpenIdLoginStatus(@ApiParam(name = "openId", value = "微信openId") @RequestParam(value = "openId") String openId,
                                                @ApiParam(name = "memberInfoGuid", value = "会员账号guid") @RequestParam(value = "openId") String memberInfoGuid);

    @ApiOperation(value = "WeChat-->会员主卡充值", notes = "会员主卡充值")
    @PostMapping(value = "/hsa-member/memberCardRechargeForWechat", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    ResponseModel<ResponseRecharge> memberCardRechargeForWechat(@Validated @RequestBody RequestMemberCardRechargeForWechat request);

    @ApiOperation("查看会员卡支持的商品")
    @GetMapping("/hsa-card/getDiscountProducts")
    ResponseModel<ResponseProductDiscount> getDiscountProducts(@RequestParam("memberInfoCardGuid") String memberInfoCardGuid,
                                                               @RequestParam("storeGuid") String storeGuid,
                                                               @RequestParam(value = "tradeMode", required = false) Integer tradeMode);

    @ApiOperation(value = "是否需要注册会员")
    @PostMapping("/hsa-member/isNeedRegister")
    @ApiImplicitParam(name = "channel", value = "使用场景(1:微信扫码点餐;2:微信预定;3:微信排队)", required = true)
    ResponseModel<Boolean> isNeedRegister(@RequestParam("channel") Integer channel);


    @ApiOperation(value = "会员手机号码绑定")
    @PostMapping("/hsa-base/bindMemberPhone")
    ResponseModel<Boolean> bindMemberPhone(@ApiParam(value = "会员手机号和短信验证码参数", required = true) @RequestBody RequestBindMemberPhone requestBindMemberPhone);


    @ApiOperation(value = "查询小程序满减满折活动")
    @GetMapping("/hsm-activitie/getMarketActivitieByType")
    ResponseModel<ResponseMarketActivitieRule> getMarketActivityByType(@RequestParam("isEnable") Boolean isEnable);


    @ApiOperation(value = "小程序计算满减满折优惠")
    @PostMapping("/hsm-activitie/discount/calculate")
    ResponseModel<ResponseMarketActivityUse> calculateDiscount(@RequestBody RequestMarketActivityUse marketplaceActivityUse);

    @ApiOperation("根据活动guid查询满减满折活动")
    @GetMapping("/hsm-activitie/get/activity")
    ResponseModel<ResponseClientMarketActivity> getActivityInfo(@RequestParam("activityGuid") String activityGuid);

    @ApiOperation("默认卡详细")
    @GetMapping("/hsa-card/default")
    ResponseModel<ResponseMemberCardListOwned> getDefaultCard(@RequestParam("memberInfoGuid") String memberInfoGuid);

    @ApiOperation("获取会员标签")
    @GetMapping("/hsa-card/get_memberInfo_label")
    ResponseModel<List<ResponseMemberLabel>> getMemberInfoLabel(@RequestParam("memberInfoGuid") String memberInfoGuid);

    @ApiOperation("查询满减满折活动详情")
    @PostMapping("/hsm-activitie/query_market")
    ResponseModel<List<ResponseClientMarketActivity>> queryMarket(@RequestBody RequestMarketActivityUse marketplaceActivityUse);

    @ApiOperation(value = "查询会员优惠券适用商品和适用门店", notes = "查询会员优惠券详情")
    @GetMapping(value = "/hsa-volume/queryUseConditions", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    ResponseModel<ResponseVolumeInfo> queryUseConditions(@RequestParam("volumeInfoGuid") String volumeInfoGuid);

    @ApiOperation("会员批量打手动标签")
    @PostMapping("/label/batch_add_manual_label")
    void batchAddManualLabel(@Validated @RequestBody RequestManualLabel req);

    @Slf4j
    @Component
    class HsaBaseClientServiceFallBack implements FallbackFactory<HsaBaseClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public HsaBaseClientService create(Throwable throwable) {
            return new HsaBaseClientService() {

                @Override
                public ResponseModel<Boolean> sendLoginSmsCode(String phoneNum) {
                    log.error("发送登录验证码失败:phoneNum={},msg={}", phoneNum, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<Boolean> registerOrLoginMemberInfo(RequestMemberBasic requestMemberBasic) {
                    log.error("手机登陆或注册会员失败:{}", requestMemberBasic);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<Boolean> updateMemberInfo(@Valid RequestUpdateMemberInfo requestUpdateMemberInfo) {
                    log.error("修改会员信息失败:{}", requestUpdateMemberInfo);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseMemberInfo> getMemberInfo(RequestQueryMemberInfo requestQueryMemberInfo) {
                    log.error("查询会员信息失败:{}", requestQueryMemberInfo);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseRecharge> memberCardRecharge(RequestMemberCardRecharge request) {
                    log.error("会员主卡充值失败:{}", request);
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseAccountStatus> getMemberState(String phoneNumOrOpenid, String enterpriseGuid, String operSubjectGuid) {
                    log.error("查询会员是否被禁用失败:phoneNum={},enterpriseGuid={},msg={}", phoneNumOrOpenid, enterpriseGuid, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseTravelDetails> getFundingTravelDetails(RequestTravelDetail requestTravelDetail) {
                    log.error("查看会员卡资金明细失败:requestTravelDetail={},msg={}", requestTravelDetail, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseTravelDetails> getGrowthValueDetails(RequestTravelDetail requestTravelDetail) {
                    log.error("查看会员卡成长值明细失败:requestTravelDetail={},msg={}", requestTravelDetail, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseTravelDetails> getIntegralTravelDetails(RequestTravelDetail requestTravelDetail) {
                    log.error("查看会员卡积分明细失败:requestTravelDetail={},msg={}", requestTravelDetail, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<Boolean> updateMemberPassword(@Valid RequestMemberPasswore hsaMemberPassworeReqDTO) {
                    log.error("修改会员支付密码失败:{},msg={}", hsaMemberPassworeReqDTO, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<Boolean> sendChangeSmsCode(String oldPhoneNum, String newPhoneNum, String enterpriseGuid) {
                    log.error("更换主账号发送手机验证码失败:oldPhoneNum={},newPhoneNum={},enterpriseGuid={},msg={}", oldPhoneNum, newPhoneNum, enterpriseGuid, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<Boolean> bindChangeMemberPhone(RequestMemberPhoneSmsCode hsmMemberPhoneSmsCodeReqDTO) {
                    log.error("会员更换手机号码（重新绑定新账号）失败:{},msg={}", hsmMemberPhoneSmsCodeReqDTO, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<String> getMemberQrcode(String key) {
                    log.error("获取二维码失败:{},msg={}", key, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<String> queryMemberMainCardQRCode(String phoneNum) {
                    log.error("查询会员卡二维码失败:phoneNum={},msg={}", phoneNum, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseMemberCardSummary> getMemberCardSummaryInfo(RequestMemberCardSummaryQuery memberCardSummaryQueryReqDTO) {
                    log.error("会员卡概要信息失败:{},msg={}", memberCardSummaryQueryReqDTO, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseIntegralRule> getIntegralRule(String systemManagementGuid, String cardGuid) {
                    log.error("查看会员卡积分配置规则失败:systemManagementGuid={},cardGuid={},msg={}", systemManagementGuid, cardGuid, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseFundingRule> getFundingRule(String memberInfoCardGuid, String cardGuid) {
                    log.error("查看会员卡余额配置规则失败:memberInfoCardGuid={},cardGuid={},msg={}", memberInfoCardGuid, cardGuid, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseCardLevelSummaryNew> getNewCardLevelSummaryInfo(RequestMemberCardSummaryQuery memberCardSummaryQueryReqDTO) {
                    log.error("新的查询会员卡权益等级概要信息失败:{},msg={}", memberCardSummaryQueryReqDTO, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseGrowthValueRule> getGrowthValueRule(String systemManagementGuid, String cardGuid) {
                    log.error("查看会员卡成长值配置规则失败:systemManagementGuid={},cardGuid={},msg={}", systemManagementGuid, cardGuid, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseCardRechargeRuleSummary> getCardSimpleRechargeRule(RequestCardRechargeRule cardRechargeRuleReqDTO) {
                    log.error("会员卡充值金额规则失败:{},msg={}", cardRechargeRuleReqDTO, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseMemberInfoVolume> getMemberVolume(RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO) {
                    log.error("查询会员优惠券列表失败:{},msg={}", memberInfoVolumeQueryReqDTO, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseMemberInfoVolumeDetails> getMemberVolumeDetails(String memberVolumeGuid) {
                    log.error("查询会员优惠券详情失败:memberVolumeGuid={},msg={}", memberVolumeGuid, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<List<ResponseMemberSourceType>> getVolumeTypes() {
                    log.error("获取优惠券类型失败:msg={}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseMemberCardAll> getMemberCard(RequestQueryMemberCardList memberCardListQueryReqDTO) {
                    log.error("查找会员卡及未开通卡信息失败:{},msg={}", memberCardListQueryReqDTO, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseMemberAndCardInfo> getMemberMainCardByMemberInfoGuid(String memberInfoGuid) {
                    log.error("查找会员主卡信息失败:{},msg={}", memberInfoGuid, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<Page<ResponseMemberCardListOwned>> getMemberCardByPage(RequestCardOwnedPage memberCardsOwnedPageReqDTO) {
                    log.error("分页查找会员卡及未开通个卡信息失败:{},msg={}", memberCardsOwnedPageReqDTO, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<List<CardLevelList>> getCardLevelAndRightInfo(String cardGuid) {
                    log.error("根据卡Guid查询卡等级即每个等级权益信息失败:cardGuid={},msg={}", cardGuid, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<Boolean> hasMemberPrice(String memberInfoCardGuid) {
                    log.error("查询会员卡是否有会员价权益失败:memberInfoCardGuid={},msg={}", memberInfoCardGuid, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<List<ResponseCardRight>> getCardRightDetails(RequestCardRightDetails requestCardRightDetails) {
                    log.error("查询会员卡权益详情失败:requestCardRightDetails={},msg={}", requestCardRightDetails, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<List<ResponseVolumeList>> consumeVolumeList(String orderNumber) {
                    log.error("优惠劵的已校验列表失败:orderNumber={},msg={}", orderNumber, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseNotOpenCardLevelRights> getNotOpenCardLevelRights(RequestMemberCardSummaryQuery cardSummaryQueryReqDTO) {
                    log.error("未开通卡的等级及所有权益失败:cardSummaryQueryReqDTO={},msg={}", cardSummaryQueryReqDTO, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<List<ResponseMemberAccount>> queryMemberByOpenId(String openId) {
                    log.error("查询openId绑定的账号(兼容以前只有openId的账号)失败:openId={},msg={}", openId, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseLoginResult> loginByPhone(RequestMemberLogin request) {
                    log.error("通过手机号登录失败:request={},msg={}", request, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseLoginResult> registerAndLogin(RequestMemberBasic request) {
                    log.error("通过手机号注册并登录会员失败:request={},msg={}", request, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<Boolean> openMemberCard(RequestMemberCardOpen requestMemberCardOpen) {
                    log.error("开通会员卡（权益卡）失败:requestMemberCardOpen={},msg={}", requestMemberCardOpen, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<Boolean> sendBindSmsCode(String phoneNum, String enterpriseGuid) {
                    log.error("绑定主账号发送手机验证码失败:phoneNum={},enterpriseGuid:{}msg={}", phoneNum, enterpriseGuid, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<Boolean> setOpenIdLoginStatus(String openId, String memberInfoGuid) {
                    log.error("设置openId对应的登陆状态(openId对应多个账号时的业务处理)失败:openId={},memberInfoGuid:{}msg={}", openId, memberInfoGuid, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseRecharge> memberCardRechargeForWechat(RequestMemberCardRechargeForWechat request) {
                    log.error("WeChat-->会员主卡充值失败:request={}msg={}", request, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseProductDiscount> getDiscountProducts(String memberInfoCardGuid, String storeGuid, Integer tradeMode) {
                    log.error("WeChat-->查询会员卡支持商品失败:memberInfoCardGuid={},storeGuid={},tradeMode:{},msg={}",
                            memberInfoCardGuid, storeGuid, tradeMode, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<Boolean> isNeedRegister(Integer channel) {
                    log.error("WeChat-->是否需要注册会员失败:channel={},msg={}", channel, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<Boolean> bindMemberPhone(RequestBindMemberPhone requestBindMemberPhone) {
                    log.error("获取会员手机号码绑定失败:requestBindMemberPhone={},msg={}", requestBindMemberPhone, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseMarketActivitieRule> getMarketActivityByType(Boolean isEnable) {
                    log.error("查询小程序满减满折活动失败:getMarketActivityByType={},msg={}", isEnable, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseMarketActivityUse> calculateDiscount(RequestMarketActivityUse marketplaceActivityUse) {
                    log.error("小程序计算满减满折优惠失败:calculateDiscount={},msg={}", marketplaceActivityUse, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseClientMarketActivity> getActivityInfo(String activityGuid) {
                    log.error("根据活动guid查询满减满折活动失败:getActivityInfo={},activityGuid={}", activityGuid, throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseModel<ResponseMemberCardListOwned> getDefaultCard(String memberInfoGuid) {
                    log.info(HYSTRIX_PATTERN, "getDefaultCard", memberInfoGuid,
                            throwable.getCause());
                    throw new ServerException();
                }

                @Override
                public ResponseModel<List<ResponseMemberLabel>> getMemberInfoLabel(String memberInfoGuid) {
                    log.info(HYSTRIX_PATTERN, "getMemberInfoLabel", memberInfoGuid,
                            throwable.getCause());
                    throw new ServerException();
                }

                @Override
                public ResponseModel<List<ResponseClientMarketActivity>> queryMarket(RequestMarketActivityUse marketplaceActivityUse) {
                    log.info(HYSTRIX_PATTERN, "queryMarket", JacksonUtils.writeValueAsString(marketplaceActivityUse),
                            throwable.getCause());
                    throw new ServerException();
                }

                @Override
                public ResponseModel<ResponseVolumeInfo> queryUseConditions(String volumeInfoGuid) {
                    log.info(HYSTRIX_PATTERN, "queryUseConditions", volumeInfoGuid,
                            throwable.getCause());
                    throw new ServerException();
                }

                @Override
                public void batchAddManualLabel(RequestManualLabel req) {
                    log.info(HYSTRIX_PATTERN, "batchAddManualLabel", req,
                            throwable.getCause());
                    throw new ServerException();
                }
            };
        }
    }
}
