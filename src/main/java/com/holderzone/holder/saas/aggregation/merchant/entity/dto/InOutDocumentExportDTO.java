package com.holderzone.holder.saas.aggregation.merchant.entity.dto;

import com.google.common.collect.Lists;
import com.holderzone.holder.saas.aggregation.merchant.entity.enums.DocumentStatus;
import com.holderzone.holder.saas.aggregation.merchant.entity.enums.InOutDocumentEnum;
import com.holderzone.holder.saas.aggregation.merchant.util.DateUtil;
import com.holderzone.saas.store.dto.erp.InOutDocumentDetailSelectDTO;
import com.holderzone.saas.store.dto.erp.InOutDocumentSelectDTO;
import com.holderzone.saas.store.util.BigDecimalUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-09-03
 * @description 导出出入库列表
 */
@Data
public class InOutDocumentExportDTO {

    @ApiModelProperty("序号")
    private String sort;

    @ApiModelProperty("入库单编码")
    private String guid;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("供应商")
    private String supplierName;

    @ApiModelProperty("入库仓库")
    private String warehouseName;

    @ApiModelProperty("入库类型")
    private String type;

    @ApiModelProperty("日期")
    private String documentDate;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("纸质单编号")
    private String code;

    @ApiModelProperty("商品编号")
    private String materialGuid;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("入库单位")
    private String unitName;

    @ApiModelProperty("入库数量")
    private String count;

    @ApiModelProperty("入库单价")
    private String unitPrice;

    @ApiModelProperty("入库总金额（元）")
    private BigDecimal totalAmount;

    public static List<InOutDocumentExportDTO> build(InOutDocumentSelectDTO inOutDocument, int sort) {
        List<InOutDocumentExportDTO> exportList = Lists.newArrayList();
        List<InOutDocumentDetailSelectDTO> detailList = inOutDocument.getDetailList();
        for (int i = 0; i < detailList.size(); i++) {
            InOutDocumentDetailSelectDTO detail = detailList.get(i);
            InOutDocumentExportDTO inOutDocumentExportDTO = new InOutDocumentExportDTO();
            inOutDocumentExportDTO.setSort(String.valueOf(sort + i));
            inOutDocumentExportDTO.setGuid(inOutDocument.getGuid());
            inOutDocumentExportDTO.setStatus(DocumentStatus.getDescByStatus(inOutDocument.getStatus()));
            inOutDocumentExportDTO.setSupplierName(inOutDocument.getSupplierName());
            inOutDocumentExportDTO.setWarehouseName(inOutDocument.getWarehouseName());
            inOutDocumentExportDTO.setType(InOutDocumentEnum.getNameByCode(inOutDocument.getType()));
            inOutDocumentExportDTO.setDocumentDate(DateUtil.format(inOutDocument.getDocumentDate(), "yyyy-MM-dd"));
            inOutDocumentExportDTO.setRemark(inOutDocument.getRemark());
            inOutDocumentExportDTO.setCode(inOutDocument.getCode());

            // 商品
            inOutDocumentExportDTO.setMaterialGuid(detail.getMaterialGuid());
            inOutDocumentExportDTO.setMaterialName(detail.getMaterialName());
            inOutDocumentExportDTO.setUnitName(detail.getUnitName());
            inOutDocumentExportDTO.setCount(detail.getCount().stripTrailingZeros().toPlainString());
            inOutDocumentExportDTO.setUnitPrice(detail.getUnitPrice().stripTrailingZeros().toPlainString());
            inOutDocumentExportDTO.setTotalAmount(detail.getCount().multiply(detail.getUnitPrice()));
            exportList.add(inOutDocumentExportDTO);
        }
        return exportList;
    }
}
