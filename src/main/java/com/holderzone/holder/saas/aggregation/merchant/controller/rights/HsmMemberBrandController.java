package com.holderzone.holder.saas.aggregation.merchant.controller.rights;


import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.baseresource.HsmBrandService;
import com.holderzone.holder.saas.member.dto.baseresource.request.HsmBrandReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.response.HsmBrandRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 品牌信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-27
 */
@RestController
@RequestMapping("/hsm/member/brand")
@Api(description = "会员的品牌")
public class HsmMemberBrandController {

    @Resource
    private HsmBrandService iHsmBrandService;


    /**
     * 同步品牌
     *
     * @param hsmBrandReqDTO 列表
     */
    @ApiOperation(value = "同步品牌", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, response = HsmBrandRespDTO.class)
    @PostMapping("/syc")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "同步品牌")
    public Result syc(
        @ApiParam(value = "品牌对象", required = true) @RequestBody HsmBrandReqDTO hsmBrandReqDTO) {
        return Result.buildSuccessResult(iHsmBrandService.syc(hsmBrandReqDTO));
    }

    /**
     * 同步品牌
     *
     * @param hsmBrandReqDTOS 列表
     */
    @ApiOperation(value = "同步品牌", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, response = HsmBrandRespDTO.class)
    @PostMapping("/syc/list")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "同步品牌")
    public Result syc(
        @ApiParam(value = "品牌对象列表", required = true) @RequestBody List<HsmBrandReqDTO> hsmBrandReqDTOS) {
        return Result.buildSuccessResult(iHsmBrandService.syc(hsmBrandReqDTOS));
    }


    /**
     * 通过企业Guid获取品牌列表
     *
     * @param enterpriseGuid 企业Guid
     * @return 品牌列表
     */
    @ApiOperation(value = "通过企业Guid获取品牌列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, response = HsmBrandRespDTO.class)
    @PostMapping("/enterprise/{enterpriseGuid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "通过企业Guid获取品牌列表")
    public Result findByEnterpriseGuid(
        @ApiParam(value = "企业ID", required = true) @PathVariable("enterpriseGuid") String enterpriseGuid) {
        return Result.buildSuccessResult(iHsmBrandService.findByEnterpriseGuid(enterpriseGuid));
    }

    /**
     * 通过企业Guid获取品牌列表,不要参数
     *
     * @return 品牌列表
     */
    @ApiOperation("通过企业Guid获取品牌列表，不要参数,删除和禁用的也要查")
    @PostMapping("/enterprise")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "通过企业Guid获取品牌列表，不要参数,删除和禁用的也要查")
    public Result<List<HsmBrandRespDTO>> findByEnterpriseGuid() {
        return Result.buildSuccessResult(iHsmBrandService.findByEnterpriseGuid());
    }


    /**
     * <AUTHOR>
     *  根据brandKey和allianceid删除品牌
     * @param brandKey 外部商家对应的品牌主键
     * @param allianceid 联盟ID
     * @return
     */
    @DeleteMapping("/{brandKey}/{allianceid}")
    @ApiOperation(value = "根据外部商家对应的品牌主键和联盟ID删除品牌", notes = "根据外部商家对应的品牌主键和联盟ID删除品牌")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "根据外部商家对应的品牌主键和联盟ID删除品牌")
    public Result removeHsmBrand(@PathVariable("brandKey") String brandKey,@PathVariable("allianceid") String allianceid){
        return Result.buildSuccessMsg(iHsmBrandService.removeHsmBrand(brandKey,allianceid)==true?"success":"fail");
    }

    /**
     * <AUTHOR>
     * 保存品牌
     * @param hsmBrandReqDTOS
     * @return
     */
    @PostMapping("/save")
    @ApiOperation("保存品牌")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "保存品牌")
    public Result saveHsmBrand(@RequestBody  HsmBrandReqDTO hsmBrandReqDTOS){
        return Result.buildSuccessMsg(iHsmBrandService.saveHsmBrand(hsmBrandReqDTOS)==true?"success":"fail");
    }

    /**
     * <AUTHOR>
     * 修改品牌
     * @param hsmBrandReqDTOS
     * @return
     */
    @ApiOperation("修改品牌")
    @PostMapping("/modify")
   @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "修改品牌")
    public Result modifyHsmBrand(@RequestBody  HsmBrandReqDTO hsmBrandReqDTOS){
        return Result.buildSuccessMsg(iHsmBrandService.modifyHsmBrand(hsmBrandReqDTOS)==true?"success":"fail");
    }
}
