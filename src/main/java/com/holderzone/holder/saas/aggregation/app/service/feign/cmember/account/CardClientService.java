package com.holderzone.holder.saas.aggregation.app.service.feign.cmember.account;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.terminal.dto.card.RequestBaseCardInfo;
import com.holderzone.holder.saas.member.terminal.dto.card.RequestMemberCardRecharge;
import com.holderzone.holder.saas.member.terminal.dto.card.ResponseCardRechargeRule;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description
 * @date 2019/6/19 16:21
 */
@Component
@FeignClient(name = "HOLDER-SAAS-MEMBER-TERMINAL", fallbackFactory = CardClientService.CardClientServiceFallBack.class)
public interface CardClientService {
    /**
     * 给某卡充值
     *
     * @param memberInfoCardGuid
     * @param rechargeReqDTO
     * @return
     */
    @PostMapping("/hsmca/card/{memberInfoCardGuid}/recharge")
    boolean cardCharge(@PathVariable("memberInfoCardGuid") String memberInfoCardGuid, @RequestBody RequestMemberCardRecharge rechargeReqDTO);

    /**
     * 通过持卡人guid 查询当前卡所对应的充值规则
     *
     * @param memberInfoCardGuid
     * @return
     */
    @GetMapping("/hsmca/card/{memberInfoCardGuid}/rechargeRule")
    String getRechargeRuleByMemberInfoCardGuid(@PathVariable("memberInfoCardGuid") String memberInfoCardGuid);

    /**
     * 通过持卡人guid查询当前卡等级对应的在某门店充值规则
     *
     * @param memberInfoCardGuid 持卡人guid
     * @return 充值规则
     */
    @ApiOperation(value = "通过持卡人guid查询当前卡等级对应的在某门店充值规则", notes = "通过持卡人guid查询当前卡等级对应的在某门店充值规则")
    @GetMapping(value = "/hsmca/card/cardRechargeRule", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    ResponseCardRechargeRule memberCardRechargeRule(@RequestParam("memberInfoCardGuid") String memberInfoCardGuid);

    @PostMapping("/hsmca/card/open")
    void open(RequestBaseCardInfo memberCardOpenReqDTO);

    @PostMapping("/hsmca/card/openEquityCard")
    void openEquityCard(String guid,
                        @ApiParam(name = "equityCardGuid", value = "权益卡GUID", required = true)
                        @RequestParam("equityCardGuid") String equityCardGuid,
                        @ApiParam(name = "equityCardExpirationGuid", value = "权益卡规格GUID", required = true)
                        @RequestParam(value = "equityCardExpirationGuid") String equityCardExpirationGuid,
                        @ApiParam(name = "sourceType", value = "终端类型 (1:后台;2:一体机;3:微信公众号;4:POS)", required = true)
                        @RequestParam(value = "sourceType") Integer sourceType);

    @Slf4j
    @Component
    class CardClientServiceFallBack implements FallbackFactory<CardClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public CardClientService create(Throwable throwable) {
            return new CardClientService() {
                @Override
                public boolean cardCharge(String memberConsumptionGuid, RequestMemberCardRecharge rechargeReqDTO) {
                    log.error(HYSTRIX_PATTERN, "getCouponInfoByMemberConsumptionGuid", "memberConsumptionGuid:" + memberConsumptionGuid + "|" + JacksonUtils.writeValueAsString(rechargeReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("通过消费记录guid查询优惠信息");
                }

                @Override
                public String getRechargeRuleByMemberInfoCardGuid(String memberInfoCardGuid) {
                    log.error(HYSTRIX_PATTERN, "getCouponInfoByMemberConsumptionGuid", "memberInfoCardGuid:" + memberInfoCardGuid, ThrowableUtils.asString(throwable));
                    throw new BusinessException("通过消费记录guid查询优惠信息");
                }

                @Override
                public ResponseCardRechargeRule memberCardRechargeRule(String memberInfoCardGuid) {
                    log.error(HYSTRIX_PATTERN, "memberCardRechargeRule", memberInfoCardGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void open(RequestBaseCardInfo memberCardOpenReqDTO) {
                    log.error("会员卡开通错误:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public void openEquityCard(String guid,
                                           @ApiParam(name = "equityCardGuid", value = "权益卡GUID", required = true)
                                           @RequestParam("equityCardGuid") String equityCardGuid,
                                           @ApiParam(name = "equityCardExpirationGuid", value = "权益卡规格GUID", required = true)
                                           @RequestParam(value = "equityCardExpirationGuid") String equityCardExpirationGuid,
                                           @ApiParam(name = "sourceType", value = "终端类型 (1:后台;2:一体机;3:微信公众号;4:POS)", required = true)
                                           @RequestParam(value = "sourceType") Integer sourceType) {
                    log.error("权卡开通错误:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }
}
