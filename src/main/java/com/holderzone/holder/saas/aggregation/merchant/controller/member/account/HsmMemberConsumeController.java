package com.holderzone.holder.saas.aggregation.merchant.controller.member.account;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.HsmMemberConsumeClientService;
import com.holderzone.holder.saas.member.dto.common.request.MemberConsumeOrderSyncSaveReqDTO;
import com.holderzone.holder.saas.member.dto.account.request.MemberConsumeRecordQueryReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberConsumeBaseStatisticsRespDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberConsumeRecordListRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberConsumeController
 * @date 2019/05/30 14:54
 * @description 会员消费
 * @program holder-saas-member-account
 */
@RestController
@Api(description = "会员消费")
@RequestMapping(value = "/hsm_member_consume")
public class HsmMemberConsumeController {

	@Resource
	private HsmMemberConsumeClientService hsmMemberConsumeClientService;

	/**
	 * 根据条件查询会员消费记录
	 *
	 * @param queryReqDTO 查询条件
	 * @return 查询结果
	 */
	@PostMapping(value = "/listByCondition", produces = "application/json;charset=utf-8")
	@ApiOperation("根据条件查询会员消费记录")
	@EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "根据条件查询会员消费记录")
	public Result<Page<MemberConsumeRecordListRespDTO>> listByCondition(
			@RequestBody MemberConsumeRecordQueryReqDTO queryReqDTO) {
		return Result.buildSuccessResult(hsmMemberConsumeClientService.listByCondition(queryReqDTO));
	}

	/**
	 * 同步消费记录
	 *
	 * @param syncSaveReqDTO 保存消费记录
	 * @return 保存结果 true成功 false失败
	 */
	@PostMapping(value = "/syncConsumeRecord", produces = "application/json;charset=utf-8")
	@ApiOperation("同步消费记录")
	@EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "同步消费记录")
	public Result<Boolean> syncConsumeRecord(@RequestBody MemberConsumeOrderSyncSaveReqDTO syncSaveReqDTO) {
		return Result.buildSuccessResult(hsmMemberConsumeClientService.syncConsumeRecord(syncSaveReqDTO));
	}

	/**
	 * 通过会员guid统计会员消费数据
	 *
	 * @param memberGuid 会员guid
	 * @return 会员消费数据
	 */
	@GetMapping(value = "/statisticsConsumeBaseData", produces = "application/json;charset=utf-8")
	@ApiOperation("通过会员guid统计会员消费数据")
	@EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "通过会员guid统计会员消费数据")
	public Result<MemberConsumeBaseStatisticsRespDTO> statisticsConsumeBaseData(
			@RequestParam(value = "memberGuid") String memberGuid) {
		return Result.buildSuccessResult(hsmMemberConsumeClientService.statisticsConsumeBaseData(memberGuid));
	}

}