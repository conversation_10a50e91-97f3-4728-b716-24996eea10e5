package com.holderzone.holder.saas.aggregation.app.service.feign.trade;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.OrderMultiMemberDTO;
import com.holderzone.saas.store.dto.order.request.bill.*;
import com.holderzone.saas.store.dto.order.response.bill.AppendFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.BillMemberCardCalculateRespDTO;
import com.holderzone.saas.store.dto.order.response.bill.BillAggPayRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.pay.SaasPollingDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInBillClientService
 * @date 2018/09/06 15:02
 * @description 正餐订单远程调用
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = DineInBillClientService.FastFoodFallBack.class)
public interface DineInBillClientService {

    @PostMapping("/dine_in_bill/calculate")
    DineinOrderDetailRespDTO calculate(BillCalculateReqDTO billCalculateReqDTO);

    @PostMapping("/dine_in_bill/pay")
    EstimateItemRespDTO pay(BillPayReqDTO billPayReqDTO);

    @PostMapping("/dine_in_bill/agg_pay")
    BillAggPayRespDTO aggPay(BillAggPayReqDTO billPayReqDTO);

    @PostMapping("/dine_in_bill/checkout/success")
    void orderCheckoutSuccess(@RequestBody BillPayReqDTO billPayReqDTO);

    @PostMapping("/dine_in_bill/recovery")
    String recovery(RecoveryReqDTO recoveryReqDTO);

    @PostMapping("/dine_in_bill/refund")
    Boolean refund(RefundReqDTO refundReqDTO);

    @PostMapping("/dine_in_bill/adjust")
    String adjust(AdjustOrderReqDTO adjustOrderReqDTO);

    @PostMapping("/dine_in_bill/validat_agg_refund")
    Boolean validatAggRefund(ValidatAggReturnReqDTO validatAggReturnReqDTO);

    @PostMapping("/dine_in_bill/append_fee_list")
    List<AppendFeeDetailDTO> appendFeeList(SingleDataDTO singleDataDTO);

    @PostMapping("/dine_in_bill/cancel")
    Boolean cancel(SaasPollingDTO saasPollingDTO);

    @ApiOperation("设置结账是否更新优惠券关系")
    @GetMapping(value = "/order_detail/updateOrderIsUpdatedEs")
    void updateOrderIsUpdatedEs(@RequestParam("orderGuid") String orderGuid);

    @ApiOperation(value = "麓豆会员登录", notes = "麓豆会员登录")
    @PostMapping("/dine_in_bill/ludou_member_login")
    BillMemberCardCalculateRespDTO ludouMemberLogin(@RequestBody BilMemberCardCalculateReqDTO cardCalculateReqDTO);

    @ApiOperation(value = "麓豆会员登出", notes = "麓豆会员登出")
    @PostMapping("/dine_in_bill/ludou_member_logout")
    void ludouMemberLogout(@RequestBody BilMemberCardCalculateReqDTO cardCalculateReqDTO);

    @ApiOperation(value = "订单关联多卡", notes = "订单关联多卡")
    @PostMapping("/dine_in_bill/relation_multi_member")
    void relationOrderMultiMember(@RequestBody OrderMultiMemberDTO reqDTO);

    @ApiOperation(value = "查询订单关联多卡", notes = "查询订单关联多卡")
    @GetMapping("/dine_in_bill/query_relation_multi_member")
    List<OrderMultiMemberDTO> queryRelationOrderMultiMember(@RequestParam("orderGuid") String orderGuid);

    @ApiOperation(value = "订单反结账是否超时", notes = "订单反结账是否超时")
    @PostMapping("/dine_in_bill/recovery/time_limit")
    Boolean recoveryTimeLimit(RecoveryReqDTO recoveryReqDTO);

    @Component
    @Slf4j
    class FastFoodFallBack implements FallbackFactory<DineInBillClientService> {

        @Override
        public DineInBillClientService create(Throwable throwable) {
            return new DineInBillClientService() {

                @Override
                public DineinOrderDetailRespDTO calculate(BillCalculateReqDTO billCalculateReqDTO) {
                    log.error("计算金额FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("计算金额失败!" + throwable.getMessage());
                }

                @Override
                public EstimateItemRespDTO pay(BillPayReqDTO billPayReqDTO) {
                    log.error("支付FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("支付失败!" + throwable.getMessage());
                }

                @Override
                public BillAggPayRespDTO aggPay(BillAggPayReqDTO billPayReqDTO) {
                    log.error("聚合支付FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("聚合支付失败!" + throwable.getMessage());
                }

                @Override
                public void orderCheckoutSuccess(BillPayReqDTO billPayReqDTO) {
                    log.error("订单结账完成FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("订单结账完成失败!" + throwable.getMessage());
                }

                @Override
                public String recovery(RecoveryReqDTO recoveryReqDTO) {
                    log.error("反结账FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("反结账失败!" + throwable.getMessage());
                }

                @Override
                public String adjust(AdjustOrderReqDTO adjustOrderReqDTO) {
                    log.error("调整订单商品FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("调整订单商品失败!" + throwable.getMessage());
                }


                @Override
                public Boolean refund(RefundReqDTO refundReqDTO) {
                    log.error("退款FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("退款失败!" + throwable.getMessage());
                }

                @Override
                public Boolean validatAggRefund(ValidatAggReturnReqDTO validatAggReturnReqDTO) {
                    log.error("校验退款FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("校验退款失败!" + throwable.getMessage());
                }

                @Override
                public List<AppendFeeDetailDTO> appendFeeList(SingleDataDTO singleDataDTO) {
                    log.error("附加费列表FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("附加费列表失败!" + throwable.getMessage());
                }

                @Override
                public Boolean cancel(SaasPollingDTO saasPollingDTO) {
                    log.error("撤销聚合支付FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("撤销聚合支付失败!" + throwable.getMessage());
                }

                @Override
                public void updateOrderIsUpdatedEs(String orderGuid) {
                    log.error("updateOrderIsUpdatedEs，throwable={}", throwable.getMessage());
                    throw new BusinessException("updateOrderIsUpdatedEs失败!" + throwable.getMessage());
                }

                @Override
                public BillMemberCardCalculateRespDTO ludouMemberLogin(BilMemberCardCalculateReqDTO cardCalculateReqDTO) {
                    log.error("ludouMemberLogin，throwable={}", throwable.getMessage());
                    throw new BusinessException("ludouMemberLogin失败!" + throwable.getMessage());
                }

                @Override
                public void ludouMemberLogout(BilMemberCardCalculateReqDTO cardCalculateReqDTO) {
                    log.error("ludouMemberLogout，throwable={}", throwable.getMessage());
                    throw new BusinessException("ludouMemberLogout失败!" + throwable.getMessage());
                }

                @Override
                public void relationOrderMultiMember(OrderMultiMemberDTO reqDTO) {
                    log.error("relationOrderMultiMember，throwable={}", throwable.getMessage());
                    throw new BusinessException("relationOrderMultiMember失败!" + throwable.getMessage());
                }

                @Override
                public List<OrderMultiMemberDTO> queryRelationOrderMultiMember(String orderGuid) {
                    log.error("queryRelationOrderMultiMember，throwable={}", throwable.getMessage());
                    throw new BusinessException("queryRelationOrderMultiMember失败!" + throwable.getMessage());
                }

                @Override
                public Boolean recoveryTimeLimit(RecoveryReqDTO recoveryReqDTO) {
                    log.error("recoveryTimeLimit，throwable={}", throwable.getMessage());
                    throw new BusinessException("recoveryTimeLimit!" + throwable.getMessage());
                }


            };
        }
    }
}
