package com.holderzone.holder.saas.aggregation.weixin.service.rpc.memberCenter;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.weixin.WxMemberCenterCardRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = WxMemberCenterCardService.WxMemberCenterCardServiceFallBack.class)
public interface WxMemberCenterCardService {

	String URL_PREFIX = "/wx_member_card";

	@PostMapping(URL_PREFIX+"/card_list")
	WxMemberCenterCardRespDTO cardList(@RequestBody WxStoreConsumerDTO wxStoreConsumerDTO);

	@Slf4j
	@Component
	class WxMemberCenterCardServiceFallBack implements FallbackFactory<WxMemberCenterCardService> {
		@Override
		public WxMemberCenterCardService create(Throwable throwable) {
			return new WxMemberCenterCardService() {

				@Override
				public WxMemberCenterCardRespDTO cardList(WxStoreConsumerDTO wxStoreConsumerDTO) {
					log.error("查询会员卡列表失败:{}", wxStoreConsumerDTO);
					throw new BusinessException(throwable.getMessage());
				}
			};
		}
	}
}
