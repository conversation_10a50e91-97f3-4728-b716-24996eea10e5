package com.holderzone.holder.saas.aggregation.app.service.feign.trade;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.order.request.bill.OrderRefundReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * 订单退款 远程调用
 */
@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = OrderRefundClientService.FastFoodFallBack.class)
public interface OrderRefundClientService {

    /**
     * 订单发起退款
     */
    @PostMapping("/dine_in_bill/order/refund")
    String orderRefund(@RequestBody OrderRefundReqDTO orderRefundReqDTO);

    /**
     * 查询可退订单商品
     */
    @GetMapping("/dine_in_bill/order/refund/available/{orderGuid}")
    DineinOrderDetailRespDTO getAvailableRefundDetail(@PathVariable String orderGuid);

    /**
     * 订单退款时间限制
     */
    @ApiOperation(value = "订单部分退款是否超时", notes = "订单部分退款是否超时")
    @PostMapping("/dine_in_bill/order/refund/time_limit")
    Boolean refundTimeLimit(@RequestBody OrderRefundReqDTO orderRefundReqDTO);

    @Component
    @Slf4j
    class FastFoodFallBack implements FallbackFactory<OrderRefundClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public OrderRefundClientService create(Throwable throwable) {
            return new OrderRefundClientService() {

                @Override
                public String orderRefund(OrderRefundReqDTO orderRefundReqDTO) {
                    log.error(HYSTRIX_PATTERN, "orderRefund", JacksonUtils.writeValueAsString(orderRefundReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public DineinOrderDetailRespDTO getAvailableRefundDetail(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "getAvailableRefundDetail", orderGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Boolean refundTimeLimit(OrderRefundReqDTO orderRefundReqDTO) {
                    log.error(HYSTRIX_PATTERN, "refundTimeLimit", orderRefundReqDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

            };
        }
    }
}
