package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.member.request.MemberConsumeReqDTO;
import com.holderzone.saas.store.dto.member.response.MemberConsumeRespDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberConsumeClientService
 * @date 2018/09/29 14:19
 * @description //TODO
 * @program holder-saas-store-member
 */

@Component
@FeignClient(name = "holder-saas-store-trading-center")
public interface  MemberConsumeClientService {

    @PostMapping("/bill/member/consume")
   Page<MemberConsumeRespDTO> memberConsumeRecords(@RequestBody MemberConsumeReqDTO memberConsumeRespDTO);



}
