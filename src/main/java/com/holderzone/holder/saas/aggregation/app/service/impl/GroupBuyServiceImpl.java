package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.GroupBuyService;
import com.holderzone.holder.saas.aggregation.app.service.feign.activity.ThirdActivityClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.groupbuy.GroupClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.item.ItemClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInOrderClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.GrouponClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.TradeThirdActivityClientService;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityRespDTO;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.member.activity.ThirdActivityTypeEnum;
import com.holderzone.saas.store.dto.order.inside.OrderGuidsDTO;
import com.holderzone.saas.store.dto.order.inside.OrderTableInfoDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.trade.req.ThirdActivityRecordDTO;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.enums.order.GrouponReceiptChannelEnum;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import com.holderzone.saas.store.enums.trade.CouponTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class GroupBuyServiceImpl implements GroupBuyService {

    private final GroupClientService groupClientService;

    private final GrouponClientService grouponClientService;

    private final ThirdActivityClientService thirdActivityClientService;

    private final TradeThirdActivityClientService tradeThirdActivityClientService;

    private final DineInOrderClientService dineInOrderClientService;

    private final ItemClientService itemClientService;


    /**
     * 结账页面预验券
     */
    @Override
    public List<MtCouponPreRespDTO> preCheck(CouPonPreReqDTO couPonPreReqDTO) {
        if (StringUtils.isEmpty(couPonPreReqDTO.getErpOrderId())) {
            throw new BusinessException("第三方erp订单号不能为空");
        }
        // 查询券信息
        List<MtCouponPreRespDTO> couponPreResp = couponPrepare(couPonPreReqDTO);
        MtCouponPreRespDTO preRespDTO = couponPreResp.get(0);
        // 查询是否绑定第三方活动
        ThirdActivityRespDTO thirdActivity = null;
        try {
            // 正餐匹配第三方活动，快餐也要匹配
            thirdActivity = queryThirdActivity(couPonPreReqDTO, preRespDTO);
        } catch (Exception e) {
            log.warn("无匹配第三方活动,券返回信息:{},错误信息:{}", JacksonUtils.writeValueAsString(preRespDTO), e.getMessage());
        }
        // 校验第三方活动规则
        preCheckParamVerify(couPonPreReqDTO.getErpOrderId(), thirdActivity);
        return buildRespDTOList(couponPreResp, thirdActivity, couPonPreReqDTO);
    }

    @Override
    public List<MtCouponPreRespDTO> preCheckQueryItem(CouPonPreReqDTO couPonPreReqDTO) {
        // 查询券信息
        List<MtCouponPreRespDTO> couponPreResp = couponPrepare(couPonPreReqDTO);
        // 验券加商品 只能使用套餐券
        MtCouponPreRespDTO preRespDTO = couponPreResp.get(0);
        if (preRespDTO.getCouponType() != CouponTypeEnum.ITEM.getCode()) {
            throw new BusinessException(Constant.COUPON_CODE_VOUCHER_INVALID);
        }
        if (Objects.equals(GrouponReceiptChannelEnum.MAITON.getCode(), preRespDTO.getReceiptChannel())) {
            throw new BusinessException(Constant.COUPON_CODE_MAITON_INVALID);
        }
        ThirdActivityRespDTO thirdActivity;
        try {
            // 根据活动编号查询第三方活动
            thirdActivity = queryThirdActivity(couPonPreReqDTO, preRespDTO);
        } catch (Exception e) {
            log.error("查询第三方活动异常,券返回信息:{},错误信息:{}", JacksonUtils.writeValueAsString(preRespDTO), e.getMessage());
            throw new BusinessException(Constant.ACTIVITY_WRONG);
        }
        if (ObjectUtils.isEmpty(thirdActivity) || 0 != thirdActivity.getRuleType()) {
            throw new BusinessException(Constant.ACTIVITY_WRONG);
        }
        log.info("匹配到第三方活动信息:{}", JacksonUtils.writeValueAsString(thirdActivity));
        //替换品牌库的商品guid为门店库
        thirdActivity.setItemGuid(replaceItemGuid(thirdActivity.getItemGuid()));
        // 校验第三方活动规则, 快餐第一次验券加购时不传orderGuid，此时还未创建订单，跳过校验
        if (StringUtils.isNotEmpty(couPonPreReqDTO.getErpOrderId())) {
            preCheckParamVerify(couPonPreReqDTO.getErpOrderId(), thirdActivity);
        }
        // 构建返回值
        return buildRespDTOList(couponPreResp, thirdActivity, couPonPreReqDTO);
    }

    private String replaceItemGuid(String itemGuid) {
        if (StringUtils.isEmpty(itemGuid)) {
            return itemGuid;
        }
        String subItemGuid = itemClientService.getSubItemGuid(itemGuid);
        log.info("品牌库商品对应的门店商品guid：{}", subItemGuid);
        if (StringUtils.isEmpty(subItemGuid)) {
            return itemGuid;
        }
        return subItemGuid;
    }


    /**
     * 查询团购券信息
     */
    private List<MtCouponPreRespDTO> couponPrepare(CouPonPreReqDTO couPonPreReqDTO) {
        List<MtCouponPreRespDTO> couponPreResp;
        try {
            couponPreResp = groupClientService.couponPrepare(couPonPreReqDTO);
        } catch (Exception e) {
            log.error("预验券失败,请求参数:{},失败原因:{}", JacksonUtils.writeValueAsString(couPonPreReqDTO), e.getMessage());
            throw new BusinessException(e.getMessage());
        }
        if (CollectionUtils.isEmpty(couponPreResp)) {
            log.error("预验券失败,返回为空，入参:{}", JacksonUtils.writeValueAsString(couPonPreReqDTO));
            throw new BusinessException(Constant.COUPON_CODE_INVALID);
        }
        log.info("验券返回信息:{}", JacksonUtils.writeValueAsString(couponPreResp));
        return couponPreResp;
    }


    /**
     * 查询第三方活动
     */
    private ThirdActivityRespDTO queryThirdActivity(CouPonPreReqDTO couPonPreReqDTO, MtCouponPreRespDTO respDTO) {
        // 活动编号
        String thirdCode = String.valueOf(respDTO.getDealId());
        if (GroupBuyTypeEnum.DOU_YIN.getCode() == couPonPreReqDTO.getGroupBuyType()) {
            thirdCode = respDTO.getSkuId();
        }
        if (GroupBuyTypeEnum.ALIPAY.getCode() == couPonPreReqDTO.getGroupBuyType()) {
            thirdCode = respDTO.getItemId();
        }
        String activityName = respDTO.getDealTitle();
        String thirdType = ThirdActivityTypeEnum.transferThirdType(couPonPreReqDTO.getGroupBuyType());
        return thirdActivityClientService.getByThirdCode(thirdCode, thirdType, activityName);
    }

    /**
     * 验券 规则校验
     */
    private void preCheckParamVerify(String orderGuid, ThirdActivityRespDTO thirdActivity) {
        // 无论先使用第三方活动的券 还是 直接使用团购验券 都要校验订单上已验的券
        // 查询订单上已使用的第三方活动
        List<ThirdActivityRecordDTO> thirdActivityRecordList = tradeThirdActivityClientService.listThirdActivityByOrderGuid(orderGuid);
        log.info("查询订单上已使用的第三方活动信息,orderGuid:{},活动明细:{}", orderGuid, JacksonUtils.writeValueAsString(thirdActivityRecordList));
        if (Objects.nonNull(thirdActivity)) {
            checkThirdActivityShareRule(orderGuid, thirdActivity, thirdActivityRecordList);
        } else {
            // 当前使用的是团购验券
            List<ThirdActivityRecordDTO> unShareList = thirdActivityRecordList.stream()
                    .filter(e -> e.getIsThirdShare() == 0)
                    .collect(Collectors.toList());
            // 校验订单上已有的第三方活动是否共享
            if (CollectionUtils.isNotEmpty(unShareList)) {
                throw new BusinessException(Constant.IS_THIRD_SHARE_TIPS);
            }
        }
    }


    /**
     * 校验第三方活动共享规则
     */
    private void checkThirdActivityShareRule(String orderGuid, ThirdActivityRespDTO thirdActivity,
                                             List<ThirdActivityRecordDTO> thirdActivityRecords) {
        if (CollectionUtils.isNotEmpty(thirdActivityRecords)) {
            // 判断订单上已使用的第三方活动使用规则
            List<ThirdActivityRecordDTO> unShareList = thirdActivityRecords.stream()
                    .filter(e -> !e.getActivityGuid().equals(thirdActivity.getGuid()))
                    .filter(e -> e.getIsThirdShare() == 0).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(unShareList)) {
                throw new BusinessException(Constant.IS_THIRD_SHARE_TIPS);
            }
            if (thirdActivity.getIsThirdShare() == 0) {
                List<ThirdActivityRecordDTO> otherActivityList = thirdActivityRecords.stream()
                        .filter(e -> !e.getActivityGuid().equals(thirdActivity.getGuid())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(otherActivityList)) {
                    throw new BusinessException(Constant.IS_THIRD_SHARE_TIPS);
                }
            }
        } else {
            if (thirdActivity.getIsThirdShare() == 0) {
                // 如果没有使用其他第三方活动，则需要去查询是否有团购验券
                List<GrouponListRespDTO> grouponList = grouponClientService.list(orderGuid, null);
                if (CollectionUtils.isNotEmpty(grouponList)) {
                    throw new BusinessException(Constant.IS_THIRD_SHARE_TIPS);
                }
            }
        }
    }


    /**
     * 构建返回值
     */
    private List<MtCouponPreRespDTO> buildRespDTOList(List<MtCouponPreRespDTO> couponPreResp, ThirdActivityRespDTO finalMtActivity,
                                                      CouPonPreReqDTO couPonPreReqDTO) {
        Byte defaultShare = 0;
        Byte maitonShare = 1;
        Integer defaultUseLimit = 9999;
        couponPreResp.forEach(e -> {
            // 默认不共享
            e.setIsThirdShare(defaultShare);
            e.setIsActivityShare(defaultShare);
            if (Objects.equals(GrouponReceiptChannelEnum.MAITON.getCode(), e.getReceiptChannel())) {
                // 美团买单 默认共享
                e.setIsThirdShare(maitonShare);
                e.setIsActivityShare(maitonShare);
            }
            e.setUseLimit(defaultUseLimit);
            if (Objects.nonNull(finalMtActivity)) {
                e.setItemGuid(finalMtActivity.getItemGuid());
                e.setSkuGuid(finalMtActivity.getSkuGuid());
                e.setActivityGuid(finalMtActivity.getGuid());
                e.setNewActivityGuid(finalMtActivity.getGuid());
                e.setIsThirdShare(finalMtActivity.getIsThirdShare());
                e.setIsActivityShare(finalMtActivity.getIsActivityShare());
                e.setGroupBuyType(couPonPreReqDTO.getGroupBuyType());
                e.setUseLimit(finalMtActivity.getUseLimit());
            }
        });
        return couponPreResp;
    }
}
