package com.holderzone.holder.saas.aggregation.app.controller.cmember.order;


import com.alibaba.fastjson.JSON;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.app.service.VolumeService;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.order.VolumeClientService;
import com.holderzone.holder.saas.common.exception.MemberTerminalException;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.member.terminal.dto.volume.*;
import com.holderzone.saas.store.dto.order.request.member.RequestQueryVolumeDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @description
 * @date 2019/6/18 18:49
 */
@Api(description = "优惠劵controller")
@RestController
@RequestMapping("/hsmca/volume")
public class VolumeController {
    @Autowired
    private VolumeClientService volumeClientService;

    @Resource
    private VolumeService volumeService;

    /**
     * @param volumeCancelReqDTO
     * @return boolean
     * @Description 取消优惠劵的使用
     * <AUTHOR>
     * @Date 2019/6/18 19:16
     */
    @PostMapping("/{volumeCode}/cancel")
    @ApiOperation(value = "取消某个优惠劵的使用", response = Boolean.class)
    @ApiImplicitParam(value = "优惠劵guid", required = true, dataType = "String", paramType = "path")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "取消某个优惠劵的使用")
    public Result<List<RequestDishInfo>> cancelByVolumeGuid(@PathVariable("volumeCode") String volumeCode, @RequestBody @Validated RequestVolumeCancel volumeCancelReqDTO) {
        return Result.buildSuccessResult(volumeClientService.cancelByVolumeCode(volumeCode, volumeCancelReqDTO));
    }

    /**
     * @param volumeCheckOutInfoReqDTO
     * @return com.holderzone.framework.response.Result
     * @Description 校验
     * <AUTHOR>
     * @Date 2019/6/21 14:09
     */
    @PostMapping("/{volumeCode}/checkout")
    @ApiOperation(value = "劵码校验")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "劵码校验",action = OperatorType.SELECT)
    public Result<ResponseVolumeCheckOut> checkoutByVolumeCode(@PathVariable(value = "volumeCode") String volumeCode,
                                                               @RequestBody @Validated RequestVolumeCheckOut volumeCheckOutInfoReqDTO) throws MemberTerminalException {

        return Result.buildSuccessResult(volumeClientService.checkoutByVolumeCode(volumeCode, volumeCheckOutInfoReqDTO));

    }

    /**
     * @param volumeConsumeReqDTO
     * @return com.holderzone.framework.response.Result
     * @Description 优惠劵的消费
     * <AUTHOR>
     * @Date 2019/6/24 10:49
     */
    @PostMapping("/{volumeCode}/consume")
    @ApiOperation("优惠劵的消费")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "优惠劵的消费",action = OperatorType.SELECT)
    public Result<List<RequestDishInfo>> consume(@PathVariable("volumeCode") String volumeCode,
                                                 @RequestBody @Validated RequestVolumeConsume volumeConsumeReqDTO) throws MemberTerminalException {
        return Result.buildSuccessResult(volumeClientService.consume(volumeCode, volumeConsumeReqDTO));
    }

    @GetMapping("/consumeVolumeList")
    @ApiOperation(value = "优惠劵的已校验列表")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "优惠劵的已校验列表",action = OperatorType.SELECT)
    public Result<List<ResponseVolumeList>> consumeVolumeList(@RequestParam("orderNumber") String orderNumber) {
        return Result.buildSuccessResult(volumeClientService.consumeVolumeList(orderNumber));
    }

    @GetMapping("/memberVolumeList")
    @ApiOperation(value = "可用优惠劵列表")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "可用优惠劵列表",action = OperatorType.SELECT)
    public Result<List<ResponseVolumeList>> memberVolumeList(@RequestParam("memberInfoGuid") String memberInfoGuid) {
        return Result.buildSuccessResult(volumeClientService.memberVolumeList(memberInfoGuid));
    }

    @PostMapping("/queryRelevanceVolumeList")
    @ApiOperation(value = "可用优惠劵整合列表")
    public Result<ResponseQueryVolumeDTO> queryMemberVolumeList(@RequestBody RequestQueryVolumeDTO requestQueryVolumeDTO) {
        return Result.buildSuccessResult(volumeService.queryMemberVolumeList(requestQueryVolumeDTO));
    }


    @PostMapping("/checkVolumeRedeem")
    @ApiOperation(value = "优惠券通用兑换")
    public Result<ResponseCheckVolumeRedeem> checkVolumeRedeem(@RequestBody RequestCheckVolumeRedeem requestCheckVolumeRedeem) {
        return Result.buildSuccessResult(volumeService.checkVolumeRedeem(requestCheckVolumeRedeem));
    }
}
