package com.holderzone.holder.saas.aggregation.app.controller.common;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.cloud.DeviceCloudFeignService;
import com.holderzone.resource.common.dto.device.VersionDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023年02月15日 下午3:22
 * @description 版本信息控制器
 */
@Slf4j
@RestController
@RequestMapping(value = "/version")
public class VersionController {

    @Autowired
    private DeviceCloudFeignService deviceCloudFeignService;

    @PostMapping("/add")
    public Result<Void> add(@RequestBody VersionDTO versionDTO) {
        log.info("增加版本信息，versionDTO={}", JacksonUtils.writeValueAsString(versionDTO));
        deviceCloudFeignService.add(versionDTO);
        return Result.buildEmptySuccess();
    }

    @GetMapping("/get")
    public Result<VersionDTO> get(@RequestParam("product") Integer product) {
        log.info("获取版本信息，product={}", product);
        return Result.buildSuccessResult(deviceCloudFeignService.get(product));
    }

    @PostMapping("/update")
    public Result<Void> update(@RequestBody VersionDTO versionDTO) {
        log.info("更新版本信息，versionDTO={}", JacksonUtils.writeValueAsString(versionDTO));
        deviceCloudFeignService.update(versionDTO);
        return Result.buildEmptySuccess();
    }

    @GetMapping("/delete")
    public Result<Void> delete(@RequestParam("id") Long id) {
        log.info("删除版本信息，id={}", id);
        deviceCloudFeignService.delete(id);
        return Result.buildEmptySuccess();
    }
}
