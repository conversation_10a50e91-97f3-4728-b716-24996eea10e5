package com.holderzone.holder.saas.aggregation.app.controller.member;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.member.HssMemberMerchantClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.member.MemberClientService;
import com.holderzone.holder.saas.aggregation.app.utils.MemberResult;
import com.holderzone.holder.saas.member.merchant.dto.card.RequestCanteenCard;
import com.holderzone.holder.saas.member.merchant.dto.card.ResponseCanteenCard;
import com.holderzone.holder.saas.member.merchant.dto.member.ResponseOperationMemberInfo;
import com.holderzone.holder.saas.member.merchant.dto.physical.*;
import com.holderzone.saas.store.dto.member.request.CanteenBasicSetupReqDTO;
import com.holderzone.saas.store.dto.member.request.PhysicalCardCheckDTO;
import com.holderzone.saas.store.dto.member.response.CanteenBasicSetupRespDTO;
import com.holderzone.saas.store.dto.member.response.CheckResultDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 食堂卡相关接口
 * @date 2021/6/16 10:41
 */
@RestController
@Api(tags = "食堂卡相关接口", description = "食堂卡相关接口")
@Slf4j
@AllArgsConstructor
public class HsaCanteenCardController {

    private final HssMemberMerchantClientService memberMerchantClientService;

    private final MemberClientService memberClientService;

    /**
     * 校验卡号是否存在
     *
     * @param physicalCardNum 实体卡号
     * @return Boolean
     */
    @ApiOperation("校验卡号是否存在")
    @GetMapping(value = "/hsa_physical_card/checkPhysicalCardNum")
    public Result<String> checkPhysicalCardNum(@RequestParam("physicalCardNum") String physicalCardNum) {
        return memberMerchantClientService.checkPhysicalCardNum(physicalCardNum);
    }


    @ApiOperation("校验卡号和手机号是否正确")
    @PostMapping(value = "/hsa_physical_card/checkPhysicalCardNumAndPhoneNum")
    public Result<CheckResultDTO> checkPhysicalCardNumAndPhoneNum(@RequestBody PhysicalCardCheckDTO request) {
        CheckResultDTO resultDTO = new CheckResultDTO();
        MemberResult<String> cardNumResult = memberMerchantClientService.checkPhysicalCardNum(request.getPhysicalCardNum());
        if (Objects.nonNull(cardNumResult.getTData())) {
            resultDTO.setCheckResult(false);
            resultDTO.setAbnormalPrompt(cardNumResult.getTData());
            return Result.buildSuccessResult(resultDTO);
        }
        MemberResult<Boolean> phoneNumResult = memberMerchantClientService.checkPhysicalCardPhoneNum(request.getPhysicalCardPhoneNum());
        if (Objects.isNull(phoneNumResult.getTData())) {
            resultDTO.setCheckResult(false);
            resultDTO.setAbnormalPrompt(phoneNumResult.getMessage());
            return Result.buildSuccessResult(resultDTO);
        }
        resultDTO.setCheckResult(phoneNumResult.getTData());
        return Result.buildSuccessResult(resultDTO);
    }


    /**
     * 校验卡号是否存在
     *
     * @param request 校验的实体
     * @return Boolean
     */
    @ApiOperation("校验卡号存在、错误、重复、是否匹配绑定码")
    @PostMapping(value = "/hsa_physical_card/checkPhysicalCardNumPairBindingNum")
    public Result<CheckResultDTO> checkPhysicalCardNumPairBindingNum(@RequestBody PhysicalCardCode request) {
        log.info("校验卡号存在、错误、重复、是否匹配绑定码 入参：{}", JacksonUtils.writeValueAsString(request));
        CheckResultDTO resultDTO = new CheckResultDTO();
        List<PhysicalCardCode> reqList = new ArrayList<>();
        reqList.add(request);
        MemberResult<Boolean> result = memberMerchantClientService.checkPhysicalCardNumPairBindingNum(reqList);
        if (Objects.isNull(result.getTData())) {
            resultDTO.setCheckResult(false);
            resultDTO.setAbnormalPrompt(result.getMessage());
            return Result.buildSuccessResult(resultDTO);
        }
        resultDTO.setCheckResult(result.getTData());
        return Result.buildSuccessResult(resultDTO);
    }


    /**
     * 实体卡绑定会员
     *
     * @param memberInfoPhysicalCardGuid 会员guid
     * @param phone                      电话
     * @param userName                   userName
     * @return ResponseMemberInfoPhysicalCardDto
     */
    @ApiOperation(value = "实体卡绑定会员")
    @PostMapping("/hsa_physical_card/binding/physical_card_member")
    public Result<CheckResultDTO> physicalCardBindingMember(@ApiParam("实体卡持卡GUID") @RequestParam("memberInfoPhysicalCardGuid") String memberInfoPhysicalCardGuid,
                                                            @ApiParam("手机号") @RequestParam("phone") String phone,
                                                            @ApiParam("姓名") @RequestParam("userName") String userName) {
        CheckResultDTO resultDTO = new CheckResultDTO();
        MemberResult<Boolean> result = memberMerchantClientService.physicalCardBindingMember(memberInfoPhysicalCardGuid, phone, userName);
        if (Objects.isNull(result.getTData())) {
            resultDTO.setCheckResult(false);
            resultDTO.setAbnormalPrompt(result.getMessage());
            return Result.buildSuccessResult(resultDTO);
        }
        MemberResult<ResponseOperationMemberInfo> info = memberMerchantClientService.getMemberInfoByPhone(phone);
        if (ObjectUtil.isNotNull(info.getTData())) {
            resultDTO.setMemberInfoGuid(info.getTData().getGuid());
        }
        resultDTO.setCheckResult(result.getTData());
        return Result.buildSuccessResult(resultDTO);
    }

    /**
     * 批量开卡或单独开卡绑定账户信息
     *
     * @param dto RequestMemberInfoPhysicalCardDto
     * @return ResponseMemberInfoPhysicalCardDto
     */
    @ApiOperation(value = "保存或编辑开卡信息")
    @PostMapping("/hsa_physical_card/save_or_update/physical_card")
    public Result<Boolean> saveOrUpdatePhysicalCardInfo(@RequestBody RequestSavePhysicalCardDto dto) {
        log.info("保存或编辑开卡信息入参:{}", JacksonUtils.writeValueAsString(dto));
        String source = UserContextUtils.getSource();
        log.info("source={}", source);
        dto.setSource(Integer.valueOf(source));
        MemberResult<Boolean> result = memberMerchantClientService.saveOrUpdatePhysicalCardInfo(dto);
        if (0 == result.getCode()) {
            return Result.buildSuccessResult(result.getMessage(), true);
        }
        return Result.buildSuccessResult(result.getMessage(), false);
    }

    /**
     * 开卡押金策略查询接口
     *
     * @param query 基础设置公共查询入参
     * @return 企业下所有的开卡押金策略
     */
    @ApiOperation(value = "查询企业下所有的开卡押金策略")
    @PostMapping("/hsa_basic_setup/list_deposit_strategy")
    public Result<List<ResponseDepositStrategy>> listDepositStrategy(@RequestBody(required = false) BasicSetupQuery query) {
        log.info("查询企业下所有的开卡押金策略 入参：{}", JacksonUtils.writeValueAsString(query));
        return memberMerchantClientService.listDepositStrategy(query);
    }

    @ApiOperation(value = "获取食堂卡信息")
    @PostMapping("/hsa_canteen_pay/get_canteen_card")
    public Result<ResponseCanteenCard> getCanteenCard(@RequestBody @Validated RequestCanteenCard requestCanteenCard) {
        log.info("【获取食堂卡信息：{}】", requestCanteenCard);
        MemberResult<ResponseCanteenCard> canteenCard = memberMerchantClientService.getCanteenCard(requestCanteenCard);
        if (Objects.isNull(canteenCard.getTData())) {
            return Result.buildFailResult(canteenCard.getCode(), canteenCard.getMessage());
        }
        return Result.buildSuccessResult(canteenCard.getTData());
    }

    /**
     * 更改实体卡状态
     *
     * @param memberInfoPhysicalCardGuid MemberInfoPhysicalCardGuid
     * @param physicalCardState          physicalCardState
     * @return ResponseMemberInfoPhysicalCardDto
     */
    @ApiOperation(value = "更改实体卡状态")
    @PostMapping("/update/physical_card_state")
    public Result<Boolean> updatePhysicalCardState(@ApiParam("实体卡持卡GUID") @RequestParam("memberInfoPhysicalCardGuid") String memberInfoPhysicalCardGuid,
                                                   @ApiParam("卡状态：0 未激活  1 正常 2 已挂失") @RequestParam("physicalCardState") Integer physicalCardState) {
        MemberResult<Boolean> result = memberMerchantClientService.updatePhysicalCardState(memberInfoPhysicalCardGuid, physicalCardState);
        return Result.buildSuccessResult(result.getMessage(), result.getTData());
    }

    /**
     * 批量激活实体卡
     *
     * @param request MemberInfoPhysicalCardGuid
     * @return Boolean
     */
    @ApiOperation(value = "批量激活实体卡,只需传activateCardList")
    @PostMapping("/hsa_physical_card/batch/activate_physical_card")
    public Result<CheckResultDTO> activationPhysicalCard(@RequestBody PhysicalCardCheckDTO request) {
        log.info("批量激活实体卡,只需传activateCardList 入参：{}", JacksonUtils.writeValueAsString(request));
        CheckResultDTO resultDTO = new CheckResultDTO();
        MemberResult<Boolean> result = memberMerchantClientService.activationPhysicalCard(request.getActivateCardList());
        if (Objects.isNull(result.getTData())) {
            resultDTO.setCheckResult(false);
            resultDTO.setAbnormalPrompt(result.getMessage());
            return Result.buildSuccessResult(resultDTO);
        }
        resultDTO.setCheckResult(result.getTData());
        return Result.buildSuccessResult(resultDTO);
    }

    @ApiOperation(value = "一体机基本设置保存和编辑接口")
    @PostMapping("/save_or_update_basic_setup")
    public Result<Boolean> saveOrUpdateBasicSetup(@RequestBody CanteenBasicSetupReqDTO reqDTO) {
        log.info("一体机余额规则保存和编辑接口 入参：{}", JSON.toJSONString(reqDTO));
        String operSubjectGuid = reqDTO.getOperSubjectGuid();
        String enterpriseGuid = reqDTO.getEnterpriseGuid();

        // 设置运营主体
        setOperSubjectGuid(operSubjectGuid);

        RequestRechargeRule rechargeRule = new RequestRechargeRule();
        rechargeRule.setIsEnableCombinationPayment(reqDTO.getIsEnableCombinationPayment());
        rechargeRule.setOperSubjectGuid(operSubjectGuid);
        rechargeRule.setEnterpriseGuid(enterpriseGuid);
        memberMerchantClientService.saveOrUpdateBalanceUsageRule(rechargeRule);

        RequestBalanceRule balanceRule = new RequestBalanceRule();
        if (Objects.isNull(reqDTO.getBalanceRuleGuid())) {
            balanceRule.setBalanceRuleGuid(null);
        } else {
            balanceRule.setBalanceRuleGuid(reqDTO.getBalanceRuleGuid());
        }
        balanceRule.setDeductionBalanceOrder(reqDTO.getDeductionBalanceOrder());
        memberMerchantClientService.saveOrUpdateBalanceRule(balanceRule);

        return Result.buildSuccessResult(Boolean.TRUE);
    }

    /**
     * 一体机基本设置查询接口
     *
     * @param query 基础设置公共查询入参
     * @return 一体机基本设置
     */
    @ApiOperation(value = "一体机基本设置查询接口")
    @PostMapping("/get_basic_setup")
    public Result<CanteenBasicSetupRespDTO> getBasicSetup(@RequestBody(required = false) BasicSetupQuery query) {
        log.info("一体机基本设置查询接口 入参：{}", JacksonUtils.writeValueAsString(query));

        // 设置运营主体
        if (Objects.nonNull(query.getOperSubjectGuid())) {
            setOperSubjectGuid(query.getOperSubjectGuid());
        }

        CanteenBasicSetupRespDTO respDTO = new CanteenBasicSetupRespDTO();
        // 充值规则
        ResponseRechargeRule rechargeRule = memberMerchantClientService.getRechargeRule(query).getTData();
        if (Objects.isNull(rechargeRule)) {
            log.warn("充值规则为空 入参={}", JacksonUtils.writeValueAsString(query));
            return Result.buildSuccessResult(respDTO);
        }
        respDTO.setIsEnableCombinationPayment(rechargeRule.getIsEnableCombinationPayment());

        // 余额规则
        ResponseBalanceRule balanceRule = memberMerchantClientService.getBalanceRule(query).getTData();
        if (Objects.isNull(balanceRule)) {
            log.warn("余额规则为空 入参={}", JacksonUtils.writeValueAsString(query));
            return Result.buildSuccessResult(respDTO);
        }
        respDTO.setDeductionBalanceOrder(balanceRule.getDeductionBalanceOrder());
        respDTO.setBalanceRuleGuid(balanceRule.getBalanceRuleGuid());

        return Result.buildSuccessResult(respDTO);
    }

    /**
     * 批量生成卡密
     *
     * @param request request
     * @return Boolean
     */
    @ApiOperation(value = "批量生成卡密")
    @PostMapping("/hsa_make_physical_card_record/save/physical_card_dense")
    Result<ResponseMakePhysicalCardRecordDto> batchGenerationPhysicalCard(@RequestBody RequestMakePhysicalCardRecordDto request) {
        request.setMakePhysicalCardNum(1);
        request.setMakePhysicalCardType(1);
        log.info("一体机生成卡密接口 入参：{}", JSON.toJSONString(request));
        MemberResult<ResponseMakePhysicalCardRecordDto> makePhysicalCardRecordDto = memberMerchantClientService.batchGenerationPhysicalCard(request);
        if (Objects.nonNull(makePhysicalCardRecordDto.getTData())) {
            return Result.buildSuccessResult(makePhysicalCardRecordDto.getTData());
        }
        return Result.buildSuccessResult(new ResponseMakePhysicalCardRecordDto());
    }


    /**
     * 记录一体机写卡结果
     *
     * @param requestUpdateMakePhysicalCardType requestUpdateMakePhysicalCardType
     */
    @ApiOperation(value = "记录一体机写卡结果")
    @PostMapping("/hsa_make_physical_card_record/updateMakePhysicalCardType")
    Result<Boolean> updateMakePhysicalCardType(@RequestBody RequestUpdateMakePhysicalCardType requestUpdateMakePhysicalCardType) {
        MemberResult<Boolean> booleanMemberResult = memberMerchantClientService.updateMakePhysicalCardType(requestUpdateMakePhysicalCardType);
        if (!booleanMemberResult.getTData()) {
            return Result.buildSuccessResult(booleanMemberResult.getMessage(), false);
        } else {
            return Result.buildSuccessResult(booleanMemberResult.getMessage(), true);
        }

    }

    /**
     * 查询完善信息
     *
     * @param memberInfoGuid memberInfoGuid
     * @return Integer
     */
    @ApiOperation(value = "查询完善信息")
    @GetMapping("/hsa-member/checkMemberInfo")
    public Result<Integer> checkMemberInfo(@RequestParam("memberInfoGuid") String memberInfoGuid) {
        MemberResult<Integer> booleanMemberResult = memberMerchantClientService.checkMemberInfo(memberInfoGuid);
        return Result.buildSuccessResult(booleanMemberResult.getTData());
    }

    private void setOperSubjectGuid(String operSubjectGuid) {
        UserContext userContext = UserContextUtils.get();
        userContext.setOperSubjectGuid(operSubjectGuid);
        UserContextUtils.put(userContext);
    }
}
