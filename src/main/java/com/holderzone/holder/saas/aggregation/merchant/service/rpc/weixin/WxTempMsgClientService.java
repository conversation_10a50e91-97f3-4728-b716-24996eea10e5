package com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.weixin.open.WxMpTemplateDTO;
import com.holderzone.saas.store.dto.weixin.req.TempMsgCreateDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * <AUTHOR>
 * @date 2024/5/13
 * @description 消息模版
 */
@Service
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = WxTempMsgClientService.FallBackClass.class)
public interface WxTempMsgClientService {

    @PostMapping("/wx_handler/create_msg_temp")
    void createMsgTemp(TempMsgCreateDTO tempMsgCreateDTO);

    @PostMapping("/wx_handler/add_msg_template")
    @ApiOperation("手动添加消息模版")
    void addMsgTemplate(@RequestBody WxMpTemplateDTO wxMpTemplateDTO);

    @Component
    @Slf4j
    class FallBackClass implements FallbackFactory<WxTempMsgClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public WxTempMsgClientService create(Throwable throwable) {
            return new WxTempMsgClientService() {

                @Override
                public void createMsgTemp(TempMsgCreateDTO tempMsgCreateDTO) {
                    log.error(HYSTRIX_PATTERN, "createMsgTemp", JacksonUtils.writeValueAsString(tempMsgCreateDTO),
                            throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public void addMsgTemplate(WxMpTemplateDTO wxMpTemplateDTO) {
                    log.error(HYSTRIX_PATTERN, "addMsgTemplate", JacksonUtils.writeValueAsString(wxMpTemplateDTO),
                            throwable.getMessage());
                    throw new ServerException();
                }
            };
        }
    }
}
