package com.holderzone.holder.saas.aggregation.merchant.controller.report;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.ReportClientService;
import com.holderzone.saas.store.dto.report.query.ReserveReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.ReserveRespDTO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


/**
 * 预定明细报表
 */
@Slf4j
@RestController
@RequestMapping("/report/reserve")
@RequiredArgsConstructor
public class ReserveReportController {

    private final ReportClientService reportClientService;

    @ApiOperation(value = "查询预定明细")
    @PostMapping("/list")
    public Result<Page<ReserveRespDTO>> list(@RequestBody @Valid ReserveReportQueryVO query) {
        return Result.buildSuccessResult(reportClientService.pageReserve(query));
    }

    @ApiOperation(value = "预定明细导出")
    @PostMapping("/export")
    public Result<String> export(@RequestBody @Valid ReserveReportQueryVO query) {
        return Result.buildSuccessResult(reportClientService.exportReserve(query));
    }
}
