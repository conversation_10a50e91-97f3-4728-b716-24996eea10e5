package com.holderzone.holder.saas.aggregation.weixin.entity.bo;

import com.holderzone.holder.saas.member.wechat.dto.card.ResponseProductDiscount;
import com.holderzone.saas.store.dto.marketing.nth.NthActivityDetailsVO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityDetailsVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DiscountRuleBO {

    @ApiModelProperty(value = "优惠券guid")
    private String volumeGuid;

    @ApiModelProperty(value = "优惠券类型")
    private Integer volumeCodeType;

    @ApiModelProperty(value = "1：验券，2：撤销，3：查询（不验券，只给微信用）")
    private Integer verify;

    @ApiModelProperty(value = "优惠券是否与其他共享")
    private Boolean volumeShareFlag;

    @ApiModelProperty(value = "满减满折活动是否与会员优惠共享")
    private Boolean activityMemberShareFlag;

    /**
     * 是否有会员价
     */
    private Boolean hasMemberPrice;

    /**
     * 是否有会员折扣
     */
    private Boolean hasMemberDiscount;

    @ApiModelProperty(value = "会员折扣")
    private BigDecimal memberDiscount;

    @ApiModelProperty(value = "满减满折活动是否与其他共享")
    private Boolean fullShareFlag;

    /**
     * 限时特价活动详情
     * 为空表示没有限时特价
     */
    @ApiModelProperty(value = "限时特价活动详情")
    private LimitSpecialsActivityDetailsVO specialsActivityDetailsVO;

    /**
     * 第N份优惠活动详情
     * 为空表示没有第N份优惠
     */
    @ApiModelProperty(value = "第N份优惠活动详情")
    private NthActivityDetailsVO nthActivityDetailsVO;

    /**
     * 会员权益信息
     */
    private ResponseProductDiscount responseProductDiscount;
}
