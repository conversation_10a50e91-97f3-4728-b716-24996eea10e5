package com.holderzone.holder.saas.aggregation.weixin.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class ThreadPoolConfig {

	@Bean
	public ExecutorService executorService() {
		return new ThreadPoolExecutor(5, 10, 30L, TimeUnit.SECONDS,
				new ArrayBlockingQueue<>(500), new ThreadFactoryBuilder().setNameFormat("takeout-mapping-pool-%d").build());
	}

	@Bean(value = "menuItemExecutor")
	public ExecutorService menuItemExecutor() {
		return new ThreadPoolExecutor(10, 100, 5L, TimeUnit.SECONDS,
				new ArrayBlockingQueue<>(50), new ThreadFactoryBuilder().setNameFormat("menu-item-pool-%d").build());
	}
}