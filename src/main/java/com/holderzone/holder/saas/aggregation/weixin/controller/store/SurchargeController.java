package com.holderzone.holder.saas.aggregation.weixin.controller.store;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.constant.RedisConstants;
import com.holderzone.holder.saas.aggregation.weixin.service.WxStoreTradeOrderService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.BusinessClientService;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeCalculateDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 附加费相关Api
 * @date 2022/3/25 16:58
 * @className: SurchargeController
 */
@Slf4j
@RestController
@RequestMapping("/surcharge")
@Api(tags = "附加费相关Api")
public class SurchargeController {

    private final BusinessClientService businessClientService;

    private final WxStoreTradeOrderService wxStoreTradeOrderService;

    public SurchargeController(BusinessClientService businessClientService,
                               WxStoreTradeOrderService wxStoreTradeOrderService) {
        this.businessClientService = businessClientService;
        this.wxStoreTradeOrderService = wxStoreTradeOrderService;
    }

    /**
     * 通过门店和桌台查询附加费信息
     *
     * @param surchargeDTO 入参
     * @return 附加费信息
     */
    @PostMapping("/calculate_surcharge")
    @ApiOperation(value = "通过门店和桌台以及人数计算附加费")
    public Result<List<SurchargeLinkDTO>> calculateSurcharge(@RequestBody SurchargeCalculateDTO surchargeDTO) {
        log.info("通过门店和桌台以及人数计算附加费入参 surchargeDTO={}", JacksonUtils.writeValueAsString(surchargeDTO));
        return Result.buildSuccessResult(businessClientService.calculateSurcharge(surchargeDTO));
    }

    /**
     * 通过门店和桌台查询附加费信息*新
     * 会过滤快餐附加费时效
     */
    @PostMapping("/calculate_surcharge_new")
    @ApiOperation(value = "通过门店和桌台以及人数计算附加费*新")
    public Result<List<SurchargeLinkDTO>> calculateSurchargeNew(@RequestBody SurchargeCalculateDTO surchargeDTO) {
        log.info("[通过门店和桌台以及人数计算附加费]*新,入参 surchargeDTO={}", JacksonUtils.writeValueAsString(surchargeDTO));
//        wxStoreTradeOrderService.querySurchargeList(surchargeDTO.getTradeMode(), surchargeDTO.getAreaGuid(), surchargeDTO.getTableGuid())
        return Result.buildSuccessResult(wxStoreTradeOrderService.querySurchargeListByRedis(surchargeDTO.getTableGuid()));
    }

}
