package com.holderzone.holder.saas.aggregation.merchant.thread;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.jou.SaleClientService;
import com.holderzone.saas.store.dto.journaling.req.SaleDetailReportReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.SaleDetailReportRespDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.Callable;

/**
 * <AUTHOR> Yu <PERSON>
 * @date 2020/5/15 14:35
 * @description
 */
@Slf4j
public class SaleDetailRunnable implements Callable<List<SaleDetailReportRespDTO>> {

    private SaleClientService saleClientService;

    private SaleDetailReportReqDTO saleDetailReportReqDTO;

    public SaleDetailRunnable(SaleClientService saleClientService, SaleDetailReportReqDTO saleDetailReportReqDTO) {
        this.saleClientService = saleClientService;
        this.saleDetailReportReqDTO = saleDetailReportReqDTO;
    }

    @Override
    public List<SaleDetailReportRespDTO> call() throws Exception {
        List<SaleDetailReportRespDTO> saleDetailReportRespDTOS = Lists.newArrayList();
        try {
            log.info("查询销售订单 线程开始 参数:{}", JSONObject.toJSONString(saleDetailReportReqDTO));
             saleDetailReportRespDTOS = saleClientService.pageSaleDetailAll(saleDetailReportReqDTO);
        }catch (Exception e){
            log.error("查询销售订单数据线程失败，异常：{}", e);
        }
        return saleDetailReportRespDTOS;
    }
}
