package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.saas.store.dto.member.hsm.HsmRechargeReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberClientService
 * @date 2018/09/16 下午9:39
 * @description 会员调用服务接口
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-store-member", fallbackFactory = MemberClientService.MemberFallback.class)
public interface MemberClientService {

    @PostMapping(value = "/hsm_member/wx_recharge")
    WxPayRespDTO wechatRecharge(@RequestBody HsmRechargeReqDTO hsmRechargeReqDTO);

    @Component
    class MemberFallback implements FallbackFactory<MemberClientService> {
        private static final Logger logger = LoggerFactory.getLogger(MemberClientService.MemberFallback.class);

        @Override
        public MemberClientService create(Throwable throwable) {
            return new MemberClientService() {

                @Override
                public WxPayRespDTO wechatRecharge(HsmRechargeReqDTO hsmRechargeReqDTO) {
                    logger.error("新会员公众号充值异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }
            };
        }
    }
}
