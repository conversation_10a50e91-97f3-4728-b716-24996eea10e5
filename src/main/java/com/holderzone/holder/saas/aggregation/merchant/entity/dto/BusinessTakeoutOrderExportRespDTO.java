package com.holderzone.holder.saas.aggregation.merchant.entity.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2023-07-11
 * @description
 */
@Data
public class BusinessTakeoutOrderExportRespDTO {

    private String orderNo;

    private String storeName;

    private String orderState;

    private String orderSource;

    private LocalDateTime acceptTime;

    private String acceptStaffName;

    private String customerName;

    private String customerPhone;

    private BigDecimal itemCount;

    private BigDecimal total;

    private BigDecimal customerActualPay;
}
