package com.holderzone.holder.saas.aggregation.app.controller.table;

import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.app.service.feign.table.TableClientService;
import com.holderzone.saas.store.dto.table.LocalizeTableOrderReqDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableLocalizeController
 * @date 2019/11/13 15:37
 * @description //TODO
 * @program IdeaProjects
 */
@RestController
@RequestMapping
@Api(description = "桌台本地化数据接口")
public class TableLocalizeController {

    @Autowired
    TableClientService tableClientService;

    @ApiOperation("本地化数据上传")
    @PostMapping("/localize/upload/tableOrder")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE,description = "本地化数据上传")
    public Result uploadTableOrderStatus(@RequestBody LocalizeTableOrderReqDTO localizeTableOrderReqDTO) {
        boolean result = tableClientService.uploadTableOrderStatus(localizeTableOrderReqDTO);
        if (result) {
            return Result.buildSuccessResult(Boolean.TRUE);
        }
        return Result.buildOpFailedResult("上传失败");
    }

}