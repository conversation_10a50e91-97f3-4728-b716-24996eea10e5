package com.holderzone.holder.saas.aggregation.merchant.controller.item;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.item.ItemClientService;
import com.holderzone.holder.saas.aggregation.merchant.util.DownloadExcelUtils;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.ItemExportReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemQueryReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemRetailSortReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemExportRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemRetailSortRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemSortRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RetailItemController
 * @date 2019/10/26 下午2:51
 * @description //零售商品Controller
 * @program holder
 */

@RestController
@RequestMapping("/retail/item")
@Api(description = "零售商品接口")
public class RetailItemController {

    private static final Logger logger = LoggerFactory.getLogger(ItemController.class);

    @Autowired
    private ItemClientService itemClientService;

    @ApiOperation(value = "零售门店入口商品列表接口")
    @PostMapping("/store/select_item_for_web")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "零售门店入口商品列表接口", action = OperatorType.SELECT)
    public Result<Page<ItemWebRespDTO>> selectRetailItemForWeb(@RequestBody ItemQueryReqDTO itemQueryReqDTO) {
        logger.info("零售商品列表接口查询入参,itemQueryReqDTO={}", itemQueryReqDTO);
        Page<ItemWebRespDTO> itemWebRespDTOS = itemClientService.selectRetailItemForWeb(itemQueryReqDTO);
        return Result.buildSuccessResult(itemWebRespDTOS);
    }

    @ApiOperation(value = "零售门店入口商品列表数据导出")
    @PostMapping("/store/export_item_for_web")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "零售门店入口商品列表数据导出接口", action = OperatorType.SELECT)
    public void exportRetailItemForWeb(@RequestBody ItemExportReqDTO itemExportReqDTO, HttpServletResponse response) {
        logger.info("零售门店入口商品列表数据导出,itemExportReqDTO={}", itemExportReqDTO);
        List<ItemExportRespDTO> respDTOList = itemClientService.getExportItemList(itemExportReqDTO);
        try {
            StringBuilder head = new StringBuilder();
            head.append("typeName:分类名称,").append("name:商品名称,").append("itemType:商品类型,")
                    .append("skuUnit:商品单位,").append("skuSalePrice:售价,").append("isWholeDiscount:是否整单折扣,")
                    .append("skuCode:货号,")
                    .append("skuUpc:条码,").append("isOpenStock:是否开启库存,").append("safeNum:安全库存,").append("remainRepertoryNum:库存数量");
            DownloadExcelUtils.fullExcel(response, "商品资料", head.toString(), respDTOList, ItemExportRespDTO.class, 400);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("报表导出异常 e={}", e.getMessage());
        }
    }

    /**
     * 商超根据分类guid获取商品列表
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "商超排序分类获取商品列表接口")
    @PostMapping("/get_item_sort_list")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "商超排序分类获取商品列表接口")
    public Result<Page<ItemSortRespDTO>> getItemSortList(@RequestBody @Valid ItemQueryReqDTO request) {
        logger.info("商超排序分类获取商品集合,request = {}", JacksonUtils.writeValueAsString(request));
        return Result.buildSuccessResult(itemClientService.selectItemSortList(request));
    }


    /**
     * 商超商品更新排序
     *
     * @param request
     * @return
     */
    @PostMapping("/retail_update_item_sort")
    @ApiOperation(value = "商超分类商品更新排序")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "商超商品更新排序接口")
    public Result retailUpdateItemSort(@RequestBody @Valid ItemRetailSortReqDTO request) {
        logger.info("商超商品更新排序,request = {}", JacksonUtils.writeValueAsString(request));
        int num = itemClientService.retailUpdateItemSort(request);
        return Integer.valueOf(1).equals(num) ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SORTING_UPDATE_FAILED));
    }


    /**
     * 商超获取分类排序和分类下商品集合
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "商超获取分类排序和分类下商品集合")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "商超获取分类排序和分类下商品集合")
    @PostMapping("/get_sort_type_and_item")
    public Result<ItemRetailSortRespDTO> getSortTypeAndItems(@RequestBody @Valid ItemSingleDTO request) {
        logger.info("商超获取分类排序和分类下商品集合,request = {}", JacksonUtils.writeValueAsString(request));
        return Result.buildSuccessResult(itemClientService.getSortTypeAndItems(request));
    }


}
