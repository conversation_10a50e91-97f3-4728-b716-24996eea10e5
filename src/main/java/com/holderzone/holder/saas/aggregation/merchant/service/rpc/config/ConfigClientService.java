package com.holderzone.holder.saas.aggregation.merchant.service.rpc.config;

import com.holderzone.saas.store.dto.config.req.ConfigReqDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReqQueryDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReverseQueryDTO;
import com.holderzone.saas.store.dto.config.resp.ConfigRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemClientService
 * @date 2018/09/07 下午4:29
 * @description //TODO
 * @program holder-saas-store-item
 */
@Component
@FeignClient(name = "holder-saas-store-config", fallbackFactory = ConfigClientService.ConfigFallBack.class)
public interface ConfigClientService {

    /**
     * 保存、更新门店置满时间
     *
     * @param configReqDTO
     * @return 保存更新行数
     */
    @PostMapping("/config/save_config")
    Integer saveEstimateResetTime(@RequestBody ConfigReqDTO configReqDTO);

    /**
     * 查询门店估清置满时间
     *
     * @param configReqQueryDTO
     * @return 查询门店估清置满时间
     */
    @PostMapping("/config/get_config_by_code")
    ConfigRespDTO selectEstimateResetTime(@RequestBody ConfigReqQueryDTO configReqQueryDTO);

    @PostMapping("/config/save_config")
    Integer saveConfig(@RequestBody ConfigReqDTO configReqDTO);

    @PostMapping("/config/get_config")
    List<ConfigRespDTO> getConfig(@RequestBody ConfigReqDTO configReqDTO);

    @PostMapping("/config/get_config_by_code")
    ConfigRespDTO getConfigByCode(@RequestBody ConfigReqQueryDTO configReqQueryDTO);

    @PostMapping("/config/get_reverse_config")
    ConfigRespDTO getConfigByCodeValue(@RequestBody ConfigReverseQueryDTO configReqQueryDTO);

    @PostMapping("/config/delete_config")
    void deleteConfig(@RequestBody ConfigReqDTO configReqDTO);

    @Component
    @Slf4j
    class ConfigFallBack implements FallbackFactory<ConfigClientService> {

        @Override
        public ConfigClientService create(Throwable throwable) {
            return new ConfigClientService() {

                @Override
                public Integer saveEstimateResetTime(ConfigReqDTO configReqDTO) {
                    log.error("设置估清重置时间异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public ConfigRespDTO selectEstimateResetTime(ConfigReqQueryDTO configReqQueryDTO) {
                    log.error("查询门店估清置满时间异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Integer saveConfig(ConfigReqDTO configReqDTO) {
                    log.error("保存门店配置异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<ConfigRespDTO> getConfig(ConfigReqDTO configReqDTO) {
                    log.error("查询门店sku估清剩余可售数量列表异常：{}", throwable.getMessage());
                    throw new RuntimeException("查询门店sku估清剩余可售数量列表异常，msg={}" + throwable.getMessage());
                }

                @Override
                public ConfigRespDTO getConfigByCode(ConfigReqQueryDTO configReqQueryDTO) {
                    log.error("查询门店配置异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public ConfigRespDTO getConfigByCodeValue(ConfigReverseQueryDTO configReqQueryDTO) {
                    log.error("获取门店配置异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public void deleteConfig(ConfigReqDTO configReqDTO) {
                    log.error("删除们门店配置异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }
            };
        }
    }

}
