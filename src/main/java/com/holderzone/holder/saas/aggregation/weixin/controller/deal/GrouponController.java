package com.holderzone.holder.saas.aggregation.weixin.controller.deal;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.GroupBuyService;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 团购验券
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class GrouponController {

    private final GroupBuyService groupBuyService;

    /**
     * 验券加商品
     */
    @ApiOperation(value = "预验券查询商品", notes = "预验券查询商品")
    @PostMapping(value = "/third_activity/pre_check/query_item", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<MtCouponPreRespDTO>> preCheckQueryItem(@RequestBody CouPonPreReqDTO couPonPreReqDTO) {
        log.info("预验券查询商品入参：{}", JacksonUtils.writeValueAsString(couPonPreReqDTO));
        return Result.buildSuccessResult(groupBuyService.preCheckQueryItem(couPonPreReqDTO));
    }
}
