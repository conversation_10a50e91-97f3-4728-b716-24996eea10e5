package com.holderzone.holder.saas.aggregation.merchant.service.rpc.pay;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.pay.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/11/6
 * @description holder-saas-store-pay
 */
@Component
@FeignClient(name = "holder-saas-store-pay", fallbackFactory = StorePayClientService.MerchantStorePayFallBack.class)
public interface StorePayClientService {

    @PostMapping("agg/pay")
    AggPayRespDTO pay(SaasAggPayDTO saasAggPayDTO);

    @PostMapping("agg/query")
    AggPayPollingRespDTO query(SaasPollingDTO saasPollingDTO);

    @PostMapping("/agg/record/quickPay/statistics")
    @ApiOperation(value = "快速收款统计")
    List<QuickPayStatisticsRespDTO> queryQuickPayStatistics(@RequestBody QuickPayStatisticsReqDTO request);

    @Slf4j
    @Component
    class MerchantStorePayFallBack implements FallbackFactory<StorePayClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public StorePayClientService create(Throwable throwable) {
            return new StorePayClientService() {

                @Override
                public AggPayRespDTO pay(SaasAggPayDTO saasAggPayDTO) {
                    log.error(HYSTRIX_PATTERN, "pay", JacksonUtils.writeValueAsString(saasAggPayDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public AggPayPollingRespDTO query(SaasPollingDTO saasPollingDTO) {
                    log.error(HYSTRIX_PATTERN, "query", JacksonUtils.writeValueAsString(saasPollingDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<QuickPayStatisticsRespDTO> queryQuickPayStatistics(QuickPayStatisticsReqDTO request) {
                    log.error(HYSTRIX_PATTERN, "queryQuickPayStatistics", JacksonUtils.writeValueAsString(request),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

            };
        }
    }
}
