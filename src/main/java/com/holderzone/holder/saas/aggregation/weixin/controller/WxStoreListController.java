package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxStoreListClientService;
import com.holderzone.saas.store.dto.weixin.WxPositionDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreListDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreCityListRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreListController
 * @date 2019/05/17 15:31
 * @description 微信门店功能及信息列表Controller
 * @program holder-saas-store
 */
@RestController
@Slf4j
@RequestMapping("/wx_store")
@Api("微信门店功能及信息列表Controller")
public class WxStoreListController {

    @Autowired
    WxStoreListClientService wxStoreListClientService;

    @ApiOperation(value = "获取门店列表", notes = "品牌guid和企业guid必传")
    @PostMapping("/list_store_config")
    public Result<WxStoreListDTO> listStoreConfig(@RequestBody WxPositionDTO wxPositionDTO) {
        log.info("获取门店微信功能列表请求参数：{}", JacksonUtils.writeValueAsString(wxPositionDTO));
        return Result.buildSuccessResult(wxStoreListClientService.listStoreConfig(wxPositionDTO));
    }

    @ApiOperation(value = "获取城市列表")
    @PostMapping("/list_store_city")
    public Result<WxStoreCityListRespDTO> listStoreCity(@RequestBody WxPositionDTO wxPositionDTO) {
        log.info("获取城市列表请求参数：{}", JacksonUtils.writeValueAsString(wxPositionDTO));
        return Result.buildSuccessResult(wxStoreListClientService.listStoreCity(wxPositionDTO));
    }

    /**
     * 查询门店配置
     *
     * @param storeGuid 门店guid
     * @return 门店配置
     */
    @ApiOperation("查询门店配置")
    @GetMapping("/store_config")
    public Result<WxOrderConfigDTO> getStoreConfig(@RequestParam("storeGuid") String storeGuid) {
        log.info("查询门店配置入参:{}", storeGuid);
        return Result.buildSuccessResult(wxStoreListClientService.getStoreConfig(storeGuid));
    }
}
