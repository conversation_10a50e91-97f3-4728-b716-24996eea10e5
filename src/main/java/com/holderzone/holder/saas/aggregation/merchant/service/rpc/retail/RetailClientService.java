package com.holderzone.holder.saas.aggregation.merchant.service.rpc.retail;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.member.request.MemberConsumeReqDTO;
import com.holderzone.saas.store.dto.member.response.MemberConsumeRespDTO;
import com.holderzone.saas.store.dto.order.SettlementRulesDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberConsumeClientService
 * @date 2018/09/29 14:19
 * @description //TODO
 * @program holder-saas-store-member
 */

@Component
@FeignClient(name = "holder-saas-store-retail")
public interface RetailClientService {

    @PostMapping("/dine_in_bill/member_consume_records")
   Page<MemberConsumeRespDTO> memberConsumeRecords(@RequestBody MemberConsumeReqDTO memberConsumeRespDTO);


    @GetMapping("/hst_settlement_rules/get_settlement_rules")
    List<SettlementRulesDTO> getSettlementRules();

}
