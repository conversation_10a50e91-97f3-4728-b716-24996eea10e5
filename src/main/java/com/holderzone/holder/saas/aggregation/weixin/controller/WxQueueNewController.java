package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxQueueClientService;
import com.holderzone.saas.store.dto.queue.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQueueNewController
 * @date 2019/09/17 15:40
 * @description
 * @program holder-saas-aggregation-merchant
 */
@RestController
@Slf4j
@Api("微信排队相关接口")
@RequestMapping("/wx_queue")
public class WxQueueNewController {

    @Autowired
    WxQueueClientService wxQueueClientService;

    @PostMapping("/query_by_guid")
    @ApiOperation(value = "用户所有门店的排队信息",notes = " 排队状态 0:队列中,1:过号,2:叫号中,3:已就餐,4:已取消")
    public Result<List<WxQueueListDTO>> queryByGuid(@RequestBody @Valid QueueWechatDTO queueWechatDTO) {
        log.info("获取用户排队信息请求入参：{}", JacksonUtils.writeValueAsString(queueWechatDTO));
        List<WxQueueListDTO> wxQueueListDTOS = wxQueueClientService.queryByGuid(queueWechatDTO);
        return Result.buildSuccessResult(wxQueueListDTOS);
    }

    @PostMapping("/query_detail")
    @ApiOperation(value = "排队详情",notes = " 排队状态 0:队列中,1:过号,2:叫号中,3:已就餐,4:已取消")
    public Result<HolderQueueQueueRecordDTO> getQueueDetail(@RequestBody ItemGuidDTO dto) {
        log.info("微信排队获取详情请求入参：{}", dto);
        HolderQueueQueueRecordDTO queueDetail = wxQueueClientService.getQueueDetail(dto);
        if(queueDetail==null) {
        	return Result.buildIllegalArgumentResult("当前排队信息已经被重置,请重新排队");
        }
        log.info("排队详情：{}", JacksonUtils.writeValueAsString(queueDetail));
        return Result.buildSuccessResult(queueDetail);
    }

    @GetMapping("/update_cancel_queue")
    @ApiOperation(value = "取消排队")
    public Result<String> cancelQueue(@RequestParam("queueGuid") String queueGuid, @RequestParam("enterpriseGuid") String enterpriseGuid) {
        log.info("取消排队请求入参：{}", JacksonUtils.writeValueAsString(queueGuid));
        return wxQueueClientService.cancelQueue(queueGuid,enterpriseGuid)?Result.buildSuccessResult("操作成功"):Result.buildOpFailedResult("操作失败");
    }
}