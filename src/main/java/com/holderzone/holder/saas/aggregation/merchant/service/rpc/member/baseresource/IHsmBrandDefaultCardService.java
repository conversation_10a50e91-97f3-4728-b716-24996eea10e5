package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.baseresource;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.dto.baseresource.request.HsmBrandDefaultCardReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.request.HsmBrandDefaultCardUpdateReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.response.HsmBrandDefaultCardRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * <p>
 * 品牌默认卡表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-12
 */
@Component
@FeignClient(name = "holder-saas-member-account", fallbackFactory = IHsmBrandDefaultCardService.HsmBrandDefaultCardServiceFallBack.class)
public interface IHsmBrandDefaultCardService {

    @ApiOperation("根据条件查询企业所有品牌的默认卡")
    @PostMapping("hsm-brand-default-card/query_by_condition")
    Page<HsmBrandDefaultCardRespDTO> queryByCondition(@RequestBody HsmBrandDefaultCardReqDTO brandDefaultCardReqDTO);

    @ApiOperation("保存或修改品牌默认卡")
    @PostMapping("hsm-brand-default-card/saveOrUpdateBrandDefaultCard")
    Boolean saveOrUpdateBrandDefaultCard(@RequestBody HsmBrandDefaultCardUpdateReqDTO brandDefaultCardUpdateReqDTO);

    @Component
    class HsmBrandDefaultCardServiceFallBack implements FallbackFactory<IHsmBrandDefaultCardService> {

        private static final Logger LOGGER = LoggerFactory
                .getLogger(IHsmBrandDefaultCardService.HsmBrandDefaultCardServiceFallBack.class);

        @Override
        public IHsmBrandDefaultCardService create(Throwable throwable) {
            return new IHsmBrandDefaultCardService() {

                @Override
                public Page<HsmBrandDefaultCardRespDTO> queryByCondition(HsmBrandDefaultCardReqDTO brandDefaultCardReqDTO) {
                    LOGGER.error("根据条件查询企业所有品牌的默认卡错误:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public Boolean saveOrUpdateBrandDefaultCard(HsmBrandDefaultCardUpdateReqDTO brandDefaultCardUpdateReqDTO) {
                    LOGGER.error("保存或修改品牌默认卡错误:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }
}
