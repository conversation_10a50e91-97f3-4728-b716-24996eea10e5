package com.holderzone.holder.saas.aggregation.merchant.controller.member.account;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.HsmMemberDetailsClientService;
import com.holderzone.holder.saas.member.dto.account.request.MemberGiftCardReqDTO;
import com.holderzone.holder.saas.member.dto.account.request.MemberInfoUpdateReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberBaseInfoRespDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberBaseStatisticsRespDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberSystemCardListRespDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberSystemListRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberDetailsController
 * @date 2019/05/30 14:58
 * @description 会员详情
 * @program holder-saas-member-account
 */
@RestController
@Api(description = "会员操作")
@RequestMapping(value = "/hsm_member")
public class HsmMemberDetailsController {


    @Resource
    private HsmMemberDetailsClientService hsmMemberDetailsClientService;

    /**
     * 获取会员基础信息
     *
     * @param memberGuid 会员guid
     * @return 会员基础信息
     */
    @GetMapping(value = "/getOne", produces = "application/json;charset=utf-8")
    @ApiOperation("获取会员基础信息")
    @ApiImplicitParams({@ApiImplicitParam(name = "memberGuid", value = "会员guid", required = true, dataType = "String")})
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "获取会员基础信息")
    public Result<MemberBaseInfoRespDTO> getOne(
            @RequestParam(value = "memberGuid") String memberGuid) {
        return Result.buildSuccessResult(hsmMemberDetailsClientService.getOne(memberGuid));
    }

    /**
     * 统计基础会员的优惠券数量和消费总额 等数据
     *
     * @param memberGuid 会员guid
     * @return 查询结果
     */
    @GetMapping(value = "/statisticsBaseData", produces = "application/json;charset=utf-8")
    @ApiOperation("统计基础会员的优惠券数量和消费总额")
    @ApiImplicitParams({@ApiImplicitParam(name = "memberGuid", value = "会员guid", required = true, dataType = "String")})
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "统计基础会员的优惠券数量和消费总额")
    public Result<MemberBaseStatisticsRespDTO> statisticsBaseData(
            @RequestParam(value = "memberGuid") String memberGuid) {
        return Result
                .buildSuccessResult(hsmMemberDetailsClientService.statisticsBaseData(memberGuid));
    }


    /**
     * 通过会员guid查询会员体系
     *
     * @return 会员体系
     */
    @GetMapping(value = "/listMemberSystemByMemberGuid", produces = "application/json;charset=utf-8")
    @ApiOperation("通过会员guid查询会员体系")
    @ApiImplicitParams({@ApiImplicitParam(name = "memberGuid", value = "会员guid", required = true, dataType = "String")})
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "通过会员guid查询会员体系")
    public Result<List<MemberSystemListRespDTO>> listMemberSystemByMemberGuid(
            @RequestParam(value = "memberGuid") String memberGuid) {
        return Result.buildSuccessResult(
                hsmMemberDetailsClientService.listMemberSystemByMemberGuid(memberGuid));
    }


    /**
     * 通过会员体系guid查询会员体系下卡的信息
     *
     * @param memberSystemGuid 会员体系guid
     * @return 查询结果
     */
    @GetMapping(value = "/listCardBySystemGuid", produces = "application/json;charset=utf-8")
    @ApiOperation("通过会员体系guid查询会员体系下卡的信息")
    @ApiImplicitParams({@ApiImplicitParam(name = "memberSystemGuid", value = "会员体系guid", required = true, dataType = "String")})
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "通过会员体系guid查询会员体系下卡的信息")
    public Result<List<MemberSystemCardListRespDTO>> listCardBySystemGuid(
            @RequestParam("memberSystemGuid")
                    String memberSystemGuid) {
        return Result.buildSuccessResult(
                hsmMemberDetailsClientService.listCardBySystemGuid(memberSystemGuid));
    }


    /**
     * 赠送卡
     *
     * @param giftCardReqDTO 送卡请求参数
     * @return 操作结果
     */
    @GetMapping(value = "/giftCard", produces = "application/json;charset=utf-8")
    @ApiOperation("赠送卡")
    @ApiImplicitParams({@ApiImplicitParam(name = "giftCardReqDTO", value = "送卡请求参数", required = true, dataType = "memberGiftCardReqDTO")})
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "赠送卡")
    public Result<Boolean> giftCard(MemberGiftCardReqDTO giftCardReqDTO) {
        return Result.buildSuccessResult(hsmMemberDetailsClientService.giftCard(giftCardReqDTO));
    }

    /**
     * 更新会员信息
     *
     * @param updateReqDTO 会员信息
     * @return 更新结果
     */
    @PostMapping(value = "/update", produces = "application/json;charset=utf-8")
    @ApiOperation("更新会员信息")
    @ApiImplicitParams({@ApiImplicitParam(name = "MemberInfoUpdateReqDTO", value = "会员信息", required = true, dataType = "MemberInfoUpdateReqDTO")})
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "更新会员信息")
    public Result<Boolean> update(@RequestBody MemberInfoUpdateReqDTO updateReqDTO) {
        return Result.buildSuccessResult(hsmMemberDetailsClientService.update(updateReqDTO));
    }

    @GetMapping(value = "/getCardNameByMember", produces = "application/json;charset=utf-8")
    @ApiOperation("根据会员Guid查询该会员在当前企业的所有卡名称和Guid")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "根据会员Guid查询该会员在当前企业的所有卡名称和Guid")
    public Result<List<MemberSystemCardListRespDTO>> getCardNameByMember(@RequestParam(value = "memberInfoGuid") String memberInfoGuid) {
        return Result.buildSuccessResult(hsmMemberDetailsClientService.getCardNameByMember(memberInfoGuid));
    }

    /**
     * 重置密码
     */
    @GetMapping(value = "/resetPassword", produces = "application/json;charset=utf-8")
    @ApiOperation("重置密码")
    @ApiImplicitParams({@ApiImplicitParam(name = "memberGuid", value = "会员guid", required = true, dataType = "String")})
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "重置密码")
    public Result<Boolean> resetPassword(String memberGuid) {
        return Result.buildSuccessResult(hsmMemberDetailsClientService.resetPassword(memberGuid));
    }

}