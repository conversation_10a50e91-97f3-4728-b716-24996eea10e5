package com.holderzone.holder.saas.aggregation.weixin.entity.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/2/19
 * @description 商品零碎对象
 */
@Data
public class ItemPiecemealBO {

    @ApiModelProperty(value = "商品guid")
    private String itemGuid;

    @ApiModelProperty(value = "数量")
    private BigDecimal currentCount;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;
}
