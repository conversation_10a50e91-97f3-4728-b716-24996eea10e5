package com.holderzone.holder.saas.aggregation.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.app.config.ZhuanCanConfig;
import com.holderzone.holder.saas.aggregation.app.service.DineInBillAppService;
import com.holderzone.holder.saas.aggregation.app.service.TableService;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.account.NewMemberInfoClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.order.VolumeClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInBillClientService;
import com.holderzone.holder.saas.aggregation.app.support.TerminalMemberSupport;
import com.holderzone.holder.saas.aggregation.app.utils.HttpsClientUtils;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberCard;
import com.holderzone.holder.saas.member.terminal.dto.volume.RequestUpdateVolumeRelevance;
import com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList;
import com.holderzone.saas.store.dto.order.request.bill.BilMemberCardCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.member.RequestUpdateCouponDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.table.TableRefreShDTO;
import com.holderzone.saas.store.enums.member.MemberLoginEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class DineInBillAppServiceImpl implements DineInBillAppService {

    private final NewMemberInfoClientService memberInfoClientService;

    private final DineInBillClientService dineInBillClientService;

    private final TableService tableService;

    private final ZhuanCanConfig zhuanCanConfig;

    private final VolumeClientService volumeClientService;

    private final TerminalMemberSupport terminalMemberSupport;

    /**
     * 获取金额卡信息并计算账单优惠
     * @param reqDTO 请求实体
     * @return 计算结果
     */
    @Override
    public DineinOrderDetailRespDTO getMemberCardAndCalculate(BilMemberCardCalculateReqDTO reqDTO) {
        log.info("查询会员卡入参:{}", JacksonUtils.writeValueAsString(reqDTO));

        UserContext userContext = UserContextUtils.get();

        RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO = new RequestQueryStoreAndMemberAndCard();

        BeanUtils.copyProperties(reqDTO, queryStoreAndMemberAndCardReqDTO);

        //获取会员卡列表
        ResponseMemberAndCardInfoDTO memberInfoAndCard = terminalMemberSupport.loginAfterGetMemberInfo(queryStoreAndMemberAndCardReqDTO);

        if (Objects.isNull(memberInfoAndCard) || Objects.isNull(memberInfoAndCard.getMemberInfoDTO())) {
            throw new BusinessException("未注册");
        }

        //获取会员开通卡信息
        List<ResponseMemberCard> memberCardListRespDTOs = memberInfoAndCard.getMemberCardListRespDTOs();
        log.info("已开通会员卡信息:{}", JacksonUtils.writeValueAsString(memberCardListRespDTOs));
        if (CollUtil.isEmpty(memberCardListRespDTOs)) {
            throw new BusinessException("暂未开通会员卡");
        }

        //获取金额最大的卡信息
        ResponseMemberCard responseMemberCard = memberCardListRespDTOs
                .stream()
                .sorted(Comparator.comparing(ResponseMemberCard::getCardMoney).reversed())
                .collect(Collectors.toList())
                .get(0);

        log.info("排序后会员卡信息:{}", JacksonUtils.writeValueAsString(responseMemberCard));

        //计算账单优惠
        BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        BeanUtils.copyProperties(reqDTO, billCalculateReqDTO);
        billCalculateReqDTO.setMemberInfoCardGuid(responseMemberCard.getMemberInfoCardGuid());
        billCalculateReqDTO.setMemberCardNum(responseMemberCard.getSystemManagementCardNum());
        billCalculateReqDTO.setLoginType(reqDTO.getLoginType());

        DineinOrderDetailRespDTO dineinOrderDetailRespDTO = getDineinOrderDetailRespDTO(billCalculateReqDTO);

        //优惠券订单关联切换为会员关联
        updateVolumeRelevance(reqDTO, dineinOrderDetailRespDTO, userContext);

        // 登录会员后刷新桌台
        TableRefreShDTO refreShDTO = new TableRefreShDTO();
        refreShDTO.setOrderGuid(reqDTO.getOrderGuid());
        refreShDTO.setStoreGuid(reqDTO.getStoreGuid());
        refreShDTO.setStoreName(reqDTO.getStoreName());
        tableService.refresh(refreShDTO);
        return dineinOrderDetailRespDTO;
    }

    private void updateVolumeRelevance(BilMemberCardCalculateReqDTO reqDTO, DineinOrderDetailRespDTO dineinOrderDetailRespDTO, UserContext userContext) {
        if (Objects.nonNull(dineinOrderDetailRespDTO)
                && !StringUtils.isEmpty(dineinOrderDetailRespDTO.getMemberGuid())) {
            try {
                RequestUpdateVolumeRelevance request = new RequestUpdateVolumeRelevance();
                request.setMemberInfoGuid(dineinOrderDetailRespDTO.getMemberGuid());
                request.setOrderGuid(reqDTO.getOrderGuid());
                request.setOperSubjectGuid(reqDTO.getOperSubjectGuid());

                RequestUpdateCouponDTO redeemCodeApplyDTO = new RequestUpdateCouponDTO();
                //获取订单已验证的券信息
                List<ResponseVolumeList> checkedVolumeLists = volumeClientService.consumeVolumeList(reqDTO.getOrderGuid());
                log.info("checkedVolumeLists：{}", JacksonUtils.writeValueAsString(checkedVolumeLists));
                if (CollUtil.isNotEmpty(checkedVolumeLists)) {
                    List<String> volumeCode = checkedVolumeLists
                            .stream()
                            .map(ResponseVolumeList::getVolumeCode)
                            .collect(Collectors.toList());

                    request.setVolumeCode(volumeCode);

                    redeemCodeApplyDTO.setVolumeCode(volumeCode);
                }

                log.info("登录门店优惠券关联修改入参：{}", JacksonUtils.writeValueAsString(request));
                //门店优惠券关联关系切换
                memberInfoClientService.updateVolumeRelevance(request);

                zhuancanVolumeRelevance(reqDTO.getMemberPhone(),
                        dineinOrderDetailRespDTO.getMemberGuid(),
                        reqDTO.getOrderGuid(),
                        userContext,
                        redeemCodeApplyDTO);
            } catch (Exception e) {
                log.info("一体机优惠券关联修改失败：{}", e.getMessage());
            }
        }
    }

    /**
     * 赚餐券关联
     */
    private void zhuancanVolumeRelevance(String memberPhone,
                                         String memberGuid,
                                         String orderGuid,
                                         UserContext userContext,
                                         RequestUpdateCouponDTO redeemCodeApplyDTO) {
        String memberName = memberInfoClientService.queryNameByPhone(memberPhone);
        //赚餐优惠券关联关系切换
        redeemCodeApplyDTO.setSource(Integer.valueOf(userContext.getSource()));
        redeemCodeApplyDTO.setStoreGuid(userContext.getStoreGuid());
        redeemCodeApplyDTO.setStoreName(userContext.getStoreName());
        redeemCodeApplyDTO.setPhoneNum(memberPhone);
        redeemCodeApplyDTO.setMemberName(memberName);
        redeemCodeApplyDTO.setOrderGuid(orderGuid);
        redeemCodeApplyDTO.setMemberInfoGuid(memberGuid);
        redeemCodeApplyDTO.setEnterpriseGuid(userContext.getEnterpriseGuid());
        redeemCodeApplyDTO.setOperSubjectGuid(userContext.getOperSubjectGuid());
        log.info("赚餐优惠券关联修改入参：{}", JacksonUtils.writeValueAsString(redeemCodeApplyDTO));
        HttpsClientUtils.doPost(zhuanCanConfig.getUpdateVolumeRelevance(), JacksonUtils.writeValueAsString(redeemCodeApplyDTO));
    }

    /**
     * 计算账单优惠
     */
    @Nullable
    private DineinOrderDetailRespDTO getDineinOrderDetailRespDTO(BillCalculateReqDTO billCalculateReqDTO) {
        log.info("计算账单优惠入参:{}", JacksonUtils.writeValueAsString(billCalculateReqDTO));
        DineinOrderDetailRespDTO calculate = dineInBillClientService.calculate(billCalculateReqDTO);
        if (calculate != null && CollUtil.isNotEmpty(calculate.getDiscountFeeDetailDTOS())) {
            calculate.getDiscountFeeDetailDTOS().forEach(c -> c.setDiscountName(DiscountTypeEnum.getLocaleDescByName(c.getDiscountName())));
        }
        return calculate;
    }

    @Override
    public DineinOrderDetailRespDTO switchoverMemberAndCalculate(BilMemberCardCalculateReqDTO reqDTO) {
        //退出登录
        BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setMemberLogin(2);
        billCalculateReqDTO.setMemberPhone(reqDTO.getMemberPhone());
        BeanUtils.copyProperties(reqDTO, billCalculateReqDTO);
        getDineinOrderDetailRespDTO(billCalculateReqDTO);

        //再次登录
        reqDTO.setMemberLogin(1);
        reqDTO.setPhoneNumOrCardNum(reqDTO.getPhoneNumOrCardNum());
        reqDTO.setMemberPhone(reqDTO.getPhoneNumOrCardNum());
        return getMemberCardAndCalculate(reqDTO);
    }

    @Override
    public DineinOrderDetailRespDTO calculate(BillCalculateReqDTO billCalculateReqDTO) {
        UserContext userContext = UserContextUtils.get();
        updateVolumeRelevance(billCalculateReqDTO, userContext);
        DineinOrderDetailRespDTO calculate = dineInBillClientService.calculate(billCalculateReqDTO);
        // 退出会员刷新桌台
        if (MemberLoginEnum.LOGOUT.getType() == billCalculateReqDTO.getMemberLogin()) {
            TableRefreShDTO refreShDTO = new TableRefreShDTO();
            refreShDTO.setOrderGuid(billCalculateReqDTO.getOrderGuid());
            refreShDTO.setStoreGuid(billCalculateReqDTO.getStoreGuid());
            refreShDTO.setStoreName(billCalculateReqDTO.getStoreName());
            tableService.refresh(refreShDTO);
        }
        return calculate;
    }

    private void updateVolumeRelevance(BillCalculateReqDTO billCalculateReqDTO, UserContext userContext) {
        try {

            List<String> volumeCodes = Lists.newArrayList();
            if (!StringUtils.isEmpty(billCalculateReqDTO.getVolumeCode())) {
                volumeCodes.add(billCalculateReqDTO.getVolumeCode());
            }

            if (CollUtil.isNotEmpty(billCalculateReqDTO.getVolumeCodes())) {
                volumeCodes.addAll(billCalculateReqDTO.getVolumeCodes());
            }

            if (CollUtil.isNotEmpty(volumeCodes) && !StringUtils.isEmpty(billCalculateReqDTO.getMemberInfoGuid())) {
                //门店关联关系切换
                RequestUpdateVolumeRelevance request = new RequestUpdateVolumeRelevance();
                request.setMemberInfoGuid(billCalculateReqDTO.getMemberInfoGuid());
                request.setOrderGuid(billCalculateReqDTO.getOrderGuid());
                request.setOperSubjectGuid(userContext.getOperSubjectGuid());
                request.setVolumeCode(volumeCodes);
                log.info("验证门店优惠券关联修改入参：{}", JacksonUtils.writeValueAsString(request));
                //门店优惠券关联关系切换
                memberInfoClientService.updateVolumeRelevance(request);

                //赚餐关联关系切换
                RequestUpdateCouponDTO redeemCodeApplyDTO = new RequestUpdateCouponDTO();
                redeemCodeApplyDTO.setVolumeCode(volumeCodes);
                zhuancanVolumeRelevance(billCalculateReqDTO.getMemberPhone(),
                        billCalculateReqDTO.getMemberInfoGuid(),
                        billCalculateReqDTO.getOrderGuid(),
                        userContext,
                        redeemCodeApplyDTO);
            }
        } catch (Exception e) {
            log.info("验证优惠券关联修改失败：{}", e.getMessage());
        }
    }
}
