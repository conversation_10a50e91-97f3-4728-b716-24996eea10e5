package com.holderzone.holder.saas.aggregation.weixin.service.impl;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.weixin.config.WeCatConfig;
import com.holderzone.holder.saas.aggregation.weixin.entity.ErrorResult;
import com.holderzone.holder.saas.aggregation.weixin.service.HsmMemberService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.EntServiceClient;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.MemberTransactionPrintService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.StoreOrganizationClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.StorePayClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestMemberCardRechargeForWechat;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseRecharge;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.member.hsm.HsmRechargeReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.pay.*;
import com.holderzone.saas.store.dto.print.content.PrintStoredCashDTO;
import com.holderzone.saas.store.dto.trade.BaseInfo;
import com.holderzone.saas.store.dto.trade.constant.PayPowerId;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.print.PrintSourceEnum;
import com.holderzone.saas.store.util.BigDecimalUtil;
import com.netflix.hystrix.exception.HystrixBadRequestException;
import com.netflix.hystrix.exception.HystrixRuntimeException;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

import static org.apache.rocketmq.client.Validators.PATTERN;

/**
 * <AUTHOR> Yu Ren
 * @date 2020/8/13 15:19
 * @description
 */
@Slf4j
@Service
public class HsmMemberServiceImpl implements HsmMemberService {

    @Autowired
    private HsaBaseClientService hsaBaseClientService;
    @Autowired
    private StorePayClientService storePayClientService;
    @Autowired
    private StoreOrganizationClientService storeOrganizationClientService;
    @Autowired
    private EntServiceClient entServiceClient;
    @Autowired
    private WeCatConfig weChatPublic;
    @Autowired
    private MemberTransactionPrintService memberTransactionPrintService;

    public static final String RECHARGE_TYPE = "会员充值";

    @Override
    public String callback(SaasNotifyDTO saasNotifyDTO) {
        log.info("callback===》微信回调充值入参{}", JacksonUtils.writeValueAsString(saasNotifyDTO));
        AggPayPollingRespDTO aggPayPollingRespDTO = saasNotifyDTO.getAggPayPollingRespDTO();
        // erpGuid, storeGuid, deviceType, deviceId可用
        BaseInfo baseInfo = saasNotifyDTO.getBaseInfo();
        // todo 回调成功时 是否需要判断该订单是否处于撤销状态或已撤销，
        if ("10000".equals(aggPayPollingRespDTO.getCode())) {
            if ("2".equals(aggPayPollingRespDTO.getPaySt())) {
                log.info("支付成功，payGuid={}", aggPayPollingRespDTO.getPayGUID());
                RequestMemberCardRechargeForWechat rechargeReqDTO = new RequestMemberCardRechargeForWechat();
                String payPowerId = aggPayPollingRespDTO.getPayPowerId();
                log.info("payPowerId: {}", payPowerId);
//                rechargeReqDTO.setPayWay(getPayWay(payPowerId));
                rechargeReqDTO.setPayWay(1);
//                rechargeReqDTO.setPayGuid(aggPayPollingRespDTO.getPayGUID());
                rechargeReqDTO.setOrderGuid(aggPayPollingRespDTO.getOrderGUID());
                int orderSource = (8 == baseInfo.getDeviceType() || 12 == baseInfo.getDeviceType())
                        ? 0 : 3 == baseInfo.getDeviceType() ? 1 : 4 == baseInfo.getDeviceType() ? 2 : 3;
                rechargeReqDTO.setOrderSource(orderSource);
                rechargeReqDTO.setPayGuid(aggPayPollingRespDTO.getPayGUID());
                log.info("支付回调 ，调用会员充值请求入参：{}", JacksonUtils.writeValueAsString(rechargeReqDTO));
                UserContext userContext = UserContextUtils.get();
                userContext.setAllianceId("1fb529b8da78459ca64187f94dc3ae3e");
                UserContextUtils.put(userContext);
//                RechargeRespDTO rechargeRespDTO = hsaBaseClientService.memberCardRechargeForWechat(rechargeReqDTO).getData();
                ResponseRecharge rechargeRespDTO = hsaBaseClientService.memberCardRechargeForWechat(rechargeReqDTO).getData();
                log.info("支付回调 ，调用会员充值返回参数：{}", JacksonUtils.writeValueAsString(rechargeRespDTO));
                if (PayPowerId.BEST_BAR_CODE.getId().equals(payPowerId) || PayPowerId.BEST_QR_CODE.getId().equals(payPowerId)) {
                    rechargeRespDTO.setPayWay(PayPowerId.BEST_BAR_CODE.getName());
                }
                if (rechargeRespDTO.getPayChannel() == null || 2 != rechargeRespDTO.getPayChannel()) {
                    boolean isRepeatCallback = Optional.ofNullable(rechargeRespDTO.getIsRepeatCallback()).orElse(false);
                    if (!isRepeatCallback) {
                        log.info("baseInfo参数：{}", JacksonUtils.writeValueAsString(baseInfo));
                        print(baseInfo, rechargeRespDTO);
                    }
                }
            } else {
                log.error("支付失败：paySt={}", aggPayPollingRespDTO.getPaySt());
            }
        } else {
            log.error("支付失败：code={}", aggPayPollingRespDTO.getCode());
        }
        return "SUCCESS";
    }

    @Override
    public WxPayRespDTO wechatRecharge(HsmRechargeReqDTO hsmRechargeReqDTO) {
        UserContext userContext = UserContextUtils.get();
        if (userContext == null || !StringUtils.hasText(userContext.getAllianceId())) {
            throw new BusinessException("当前线程没有用户信息");
        }
        RequestMemberCardRechargeForWechat requestMemberCardRecharge = new RequestMemberCardRechargeForWechat();
        requestMemberCardRecharge.setMemberInfoCardGuid(hsmRechargeReqDTO.getMemberInfoCardGuid());
        requestMemberCardRecharge.setOrderGuid(hsmRechargeReqDTO.getOrderGuid());
        //会员那边要求这样处理Could not autowire. No beans of 'WxStorePayConfig' type found.
        requestMemberCardRecharge.setOrderNumber(hsmRechargeReqDTO.getOrderGuid());
        requestMemberCardRecharge.setOrderSource(hsmRechargeReqDTO.getOrderSource());
        requestMemberCardRecharge.setOrderTime(hsmRechargeReqDTO.getOrderTime());
        requestMemberCardRecharge.setPayCode(hsmRechargeReqDTO.getPayCode());
        if (Objects.equals(hsmRechargeReqDTO.getPayWay(), PaymentTypeEnum.PaymentType.CASH_PAY.getId())
                && BigDecimalUtil.equelZero(hsmRechargeReqDTO.getRechargeMoney())) {
            requestMemberCardRecharge.setPayName(hsmRechargeReqDTO.getPayName());
            requestMemberCardRecharge.setPayWay(hsmRechargeReqDTO.getPayWay());
        } else {
            requestMemberCardRecharge.setPayWay(5);
            requestMemberCardRecharge.setPayName("自定义");
        }
        requestMemberCardRecharge.setRechargeMoney(hsmRechargeReqDTO.getRechargeMoney());

        StoreDTO storeDTO = storeOrganizationClientService.queryStoreByGuid(hsmRechargeReqDTO.getStoreGuid());
        if (storeDTO == null) {
            throw new BusinessException("门店" + hsmRechargeReqDTO.getStoreGuid() + "]不存在");
        }
        log.info("会员充值调用会员服务请求入参：{}", JacksonUtils.writeValueAsString(requestMemberCardRecharge));
        ResponseRecharge responseRecharge = hsaBaseClientService.memberCardRechargeForWechat(requestMemberCardRecharge).getData();
        String payGuid = responseRecharge.getPayGuid();
        String orderGuid = responseRecharge.getOrderGuid();
        log.info("微信充值调用支付服务请求入参hsmRechargeReqDTO:{}====>payGuid{}=====>orderGuid{}", JacksonUtils.writeValueAsString(hsmRechargeReqDTO), payGuid, orderGuid);
        return wechatPay(hsmRechargeReqDTO, payGuid, orderGuid);
    }

    private WxPayRespDTO wechatPay(HsmRechargeReqDTO hsmRechargeReqDTO, String payGuid, String orderGuid) {
        WxPayRespDTO wxPayRespDTO = new WxPayRespDTO();
        if (hsmRechargeReqDTO.getRechargeMoney().compareTo(BigDecimal.ZERO) <= 0) {
            // 充值金额小于等于0 直接返回
            wxPayRespDTO.setCouldPay(1);
            return wxPayRespDTO;
        }
        if (!StringUtils.isEmpty(hsmRechargeReqDTO.getThirdAppId())) {
            // 使用微信小程序支付
            SaasAggPayDTO saasAggPayDTO = buildSaasAggPayDTO(hsmRechargeReqDTO, payGuid, orderGuid);
            log.info("支付下单:{}", JacksonUtils.writeValueAsString(saasAggPayDTO));
            AggPayRespDTO payResp = storePayClientService.pay(saasAggPayDTO);
            log.info("支付下单结果:{}", JacksonUtils.writeValueAsString(payResp));
            wxPayRespDTO.setResult(payResp);
            wxPayRespDTO.setCouldPay(1);
            return wxPayRespDTO;
        }
        SaasAggWeChatPublicAccountPayDTO saasAggWeChatPublicAccountPay = new SaasAggWeChatPublicAccountPayDTO();

        saasAggWeChatPublicAccountPay.setDeviceType(12);
        saasAggWeChatPublicAccountPay.setSaasCallBackUrl("http://holder-saas-aggregation-weixin/hsm_member/callback");
        AggWeChatPublicAccountPayDTO aggWeChatPublicAccountPayDTO = new AggWeChatPublicAccountPayDTO();
        //重定向到微信订单详情页面
//        aggWeChatPublicAccountPayDTO.setOutNotifyUrl(hsmRechargeReqDTO.getOutNotifyUrl());
        aggWeChatPublicAccountPayDTO.setRedirectUrl(hsmRechargeReqDTO.getOutNotifyUrl());
        //微信回调买单人的openid
        aggWeChatPublicAccountPayDTO.setAppId(weChatPublic.getAppId());
        aggWeChatPublicAccountPayDTO.setMchntName(weChatPublic.getMchntName());
        aggWeChatPublicAccountPayDTO.setAppSecret(weChatPublic.getAppSecret());
        aggWeChatPublicAccountPayDTO.setStoreName(hsmRechargeReqDTO.getStoreName());
        aggWeChatPublicAccountPayDTO.setGoodsName(RECHARGE_TYPE);
        aggWeChatPublicAccountPayDTO.setBody(RECHARGE_TYPE);
        EnterpriseDTO enterprise = findEnterprise(hsmRechargeReqDTO.getEnterpriseGuid());
        log.info("查询出的企业信息{}", JacksonUtils.writeValueAsString(enterprise));
        aggWeChatPublicAccountPayDTO.setEnterpriseName(enterprise.getName());
        aggWeChatPublicAccountPayDTO.setStoreName(hsmRechargeReqDTO.getStoreName());
        aggWeChatPublicAccountPayDTO.setAmount(hsmRechargeReqDTO.getRechargeMoney());
        aggWeChatPublicAccountPayDTO.setOrderGUID(orderGuid);
        aggWeChatPublicAccountPayDTO.setPayGUID(payGuid);
        saasAggWeChatPublicAccountPay.setPublicAccountPayDTO(aggWeChatPublicAccountPayDTO);
        //企业 门店 guid
        saasAggWeChatPublicAccountPay.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        saasAggWeChatPublicAccountPay.setEnterpriseName(enterprise.getName());
        saasAggWeChatPublicAccountPay.setStoreGuid(hsmRechargeReqDTO.getStoreGuid());

        try {
            log.info("微信公众号支付轮询请求入参saasAggWeChatPublicAccountPay:{}", JacksonUtils.writeValueAsString(saasAggWeChatPublicAccountPay));
            String payUrl = storePayClientService.weChatPublic(saasAggWeChatPublicAccountPay);
            wxPayRespDTO.setPayUrl(payUrl);
            wxPayRespDTO.setCouldPay(1);
        } catch (Exception e) {
            e.printStackTrace();
            wxPayRespDTO.setCouldPay(0);
            String message = getAggPayErrorMsg(e);
            wxPayRespDTO.setErrorMsg(message);
        }
        return wxPayRespDTO;
    }

    private SaasAggPayDTO buildSaasAggPayDTO(HsmRechargeReqDTO hsmRechargeReqDTO, String payGuid, String orderGuid) {
        String callBackUrl = "http://holder-saas-aggregation-weixin/hsm_member/callback";
        String appId = hsmRechargeReqDTO.getThirdAppId();
        String openId = hsmRechargeReqDTO.getThirdOpenId();
        log.info("使用外部传入的appId和openId, appId:{}, openId:{}", appId, openId);

        AggPayPreTradingReqDTO aggWeChatPublicAccountPayDTO = new AggPayPreTradingReqDTO();
        aggWeChatPublicAccountPayDTO.setStoreName(hsmRechargeReqDTO.getStoreName());
        aggWeChatPublicAccountPayDTO.setGoodsName(RECHARGE_TYPE);
        aggWeChatPublicAccountPayDTO.setBody(RECHARGE_TYPE);
        aggWeChatPublicAccountPayDTO.setPayPowerId(PayPowerId.WX_MINI_PROGRAM_ONLINE.getId());
        aggWeChatPublicAccountPayDTO.setTerminalId(payGuid);

        EnterpriseDTO enterprise = findEnterprise(hsmRechargeReqDTO.getEnterpriseGuid());
        log.info("查询出的企业信息{}", JacksonUtils.writeValueAsString(enterprise));
        aggWeChatPublicAccountPayDTO.setEnterpriseName(enterprise.getName());
        aggWeChatPublicAccountPayDTO.setAmount(hsmRechargeReqDTO.getRechargeMoney());
        aggWeChatPublicAccountPayDTO.setOrderGUID(orderGuid);
        aggWeChatPublicAccountPayDTO.setPayGUID(payGuid);
        aggWeChatPublicAccountPayDTO.setSubAppId(appId);
        aggWeChatPublicAccountPayDTO.setSubOpenId(openId);

        SaasAggPayDTO saasAggPayDTO = new SaasAggPayDTO();
        saasAggPayDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        saasAggPayDTO.setStoreGuid(hsmRechargeReqDTO.getStoreGuid());
        // 操作设备类型
        saasAggPayDTO.setDeviceType(BaseDeviceTypeEnum.WECHAT.getCode());
        saasAggPayDTO.setIsLast(true);
        saasAggPayDTO.setSaasCallBackUrl(callBackUrl);
        saasAggPayDTO.setReqDTO(aggWeChatPublicAccountPayDTO);
        // 是否结账不清台
        saasAggPayDTO.setCloseTableFlag(BooleanEnum.FALSE.getCode());
        return saasAggPayDTO;
    }

    private EnterpriseDTO findEnterprise(String enterpriseGuid) {
        BaseDTO baseDTO = new BaseDTO();
        baseDTO.setEnterpriseGuid(enterpriseGuid);
        return entServiceClient.findEnterprise(baseDTO);
    }

    /**
     * 特定于判断聚合支付错误时是否是门店未配置的问题导致的
     *
     * @param e
     * @return
     */
    private String getAggPayErrorMsg(Exception e) {
        String message;
        if (e instanceof HystrixRuntimeException || e instanceof HystrixBadRequestException) {
            log.info("会员充值的异常类型是HystrixRuntimeException");
            message = e.getCause().getMessage();
        } else if (e instanceof FeignException) {
            log.info("会员充值的异常类型是FeignException");
            message = e.getMessage();
        } else {
            log.info("无法识别到异常类型");
            return "聚合支付发生错误";
        }
        message = cutMsg(message);
        log.info("会员充值异常message：{}", message);
        if (!PATTERN.matcher(message).find()) {
            return "该门店未配置收款账户，无法支付";
        }
        return "聚合支付发生错误";
    }

    private String cutMsg(String message) {
        if (StringUtils.isEmpty(message)) {
            return null;
        } else {
            String content = "; content:\n";
            int contentLength = content.length();
            int contentIndex = message.indexOf(content);
            String substring = message.substring(contentIndex + contentLength);
            log.info("substring:{}", substring);
            ErrorResult errorResult = JacksonUtils.toObject(ErrorResult.class, substring);
            log.info("errorResult:{}", errorResult);
            return errorResult.getMessage();
        }
    }


    private void print(BaseDTO baseDTO, ResponseRecharge rechargeRespDTO) {
        log.info("rechargeRespDTO -----------------------  = {}", JacksonUtils.writeValueAsString(rechargeRespDTO));
        PrintStoredCashDTO printStoredCashDTO = new PrintStoredCashDTO();
        //交易流水号
        printStoredCashDTO.setSerialNumber(rechargeRespDTO.getSerialNumber());
        //充值金额
        printStoredCashDTO.setRecharge(rechargeRespDTO.getRecharge());
        //赠送金额
        printStoredCashDTO.setPresented(rechargeRespDTO.getPresented());
        //赠送积分
        // fixme 升级memberDto依赖，放开此处
        printStoredCashDTO.setPresentedIntegral(rechargeRespDTO.getPresentedIntegral());
        //到账金额
        printStoredCashDTO.setArrival(rechargeRespDTO.getArrival());
        //充值方式
//        printStoredCashDTO.setPayWay(StringUtils.hasText(rechargeRespDTO.getPayName())
//                ? rechargeRespDTO.getPayName() : rechargeRespDTO.getPayWay());
        printStoredCashDTO.setPayWay(rechargeRespDTO.getPayWay());
        //充值卡号
        printStoredCashDTO.setCardNo(rechargeRespDTO.getCardNo());
        //当前余额
        printStoredCashDTO.setCurrentCash(rechargeRespDTO.getCurrentCash());
        //当前积分
        printStoredCashDTO.setIntegration(rechargeRespDTO.getIntegration());
        //充值时间
        printStoredCashDTO.setRechargeTime(DateTimeUtils.nowMillis());
        //企业信息
        printStoredCashDTO.setEnterpriseGuid(baseDTO.getEnterpriseGuid());
        //门店信息
        printStoredCashDTO.setStoreGuid(baseDTO.getStoreGuid());
        printStoredCashDTO.setStoreName(baseDTO.getStoreName());
//        printStoredCashDTO.setStoreAddress(rechargeRespDTO.getStoreAddress());
//        printStoredCashDTO.setTel(rechargeRespDTO.getTel());
        //单据类型
        printStoredCashDTO.setInvoiceType(10);
        //打印唯一标识
        printStoredCashDTO.setPrintUid(rechargeRespDTO.getOrderGuid());
        //操作人
        printStoredCashDTO.setOperatorStaffGuid(baseDTO.getUserGuid());
        printStoredCashDTO.setOperatorStaffName(baseDTO.getUserName());
        //打印时间
        printStoredCashDTO.setCreateTime(DateTimeUtils.nowMillis());
        //设备id
        printStoredCashDTO.setDeviceId(baseDTO.getDeviceId());
        //打印来源
        printStoredCashDTO.setPrintSourceEnum(PrintSourceEnum.getPrintSourceByDeviceType(baseDTO.getDeviceType()));
        //会员信息
        printStoredCashDTO.setMemberName(rechargeRespDTO.getMemberName());
        printStoredCashDTO.setMemberPhone(rechargeRespDTO.getMemberPhone());
        String transactionLog = memberTransactionPrintService.printMemberTransactionLog(printStoredCashDTO);
        log.info("打印结果：{}", transactionLog);
    }
}
