package com.holderzone.holder.saas.aggregation.weixin.service.chain;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.weixin.context.DiscountContext;
import com.holderzone.holder.saas.aggregation.weixin.entity.bo.DiscountRuleBO;
import com.holderzone.holder.saas.aggregation.weixin.helper.MarketingActivityHelper;
import com.holderzone.holder.saas.aggregation.weixin.helper.PriceCalculateHelper;
import com.holderzone.holder.saas.aggregation.weixin.utils.map.DiscountMAP;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.marketing.nth.NthActivityDetailsVO;
import com.holderzone.saas.store.dto.marketing.nth.NthActivityItemDTO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityDetailsVO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountRuleDTO;
import com.holderzone.saas.store.dto.weixin.deal.ActivitySelectDTO;
import com.holderzone.saas.store.dto.weixin.resp.MarketingActivityInfoRespDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.member.MarketActivityUnableTipEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.util.BigDecimalUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 第N份优惠活动
 */
@Component
@Slf4j
@AllArgsConstructor
public class NthActivityDiscountHandler extends DiscountHandler {

    private final MarketingActivityHelper marketingActivityHelper;

    private final PriceCalculateHelper priceCalculateHelper;

    @Override
    void dealDiscount(DiscountContext context) {
        if (context.isRejectDiscount()) {
            return;
        }
        DiscountFeeDetailDTO nthActivityDiscountDTO =
                DiscountMAP.INSTANCE.discountDO2DiscountFeeDetailDTO(context.getDiscountTypeMap().get(type()));

        // 查询第N份优惠
        List<NthActivityDetailsVO> nthActivityList = marketingActivityHelper.queryNthActivityList(WeixinUserThreadLocal.get());
        log.info("[第N份优惠]nthActivityList={}", JacksonUtils.writeValueAsString(nthActivityList));

        // 商品校验
        List<DineInItemDTO> allItems = context.getAllItems();
        List<String> itemGuidList = allItems.stream()
                .map(DineInItemDTO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        List<String> parentGuidList = priceCalculateHelper.queryParentGuid(itemGuidList);
        itemGuidList.addAll(parentGuidList);
        MarketingActivityHelper.nthFilterItem(itemGuidList, nthActivityList, 1);

        // 选中的活动信息
        NthActivityDetailsVO activityDetailsVO = getSelectNthActivityDetailsVO(context, nthActivityList);

        // 营销活动列表
        addNthActivityInfoList(context, nthActivityList, allItems);

        // 第N份优惠活动计算
        handleNthActivity(context, activityDetailsVO, nthActivityDiscountDTO);

        context.getDiscountFeeDetailDTOS().add(nthActivityDiscountDTO);
        log.info("[第N份优惠]计算后订单剩余金额：{}，优惠金额：{}，菜品优惠：{}", context.getCalculateOrderRespDTO().getOrderSurplusFee(),
                nthActivityDiscountDTO.getDiscountFee(), JacksonUtils.writeValueAsString(context.getAllItems()));
    }

    private NthActivityDetailsVO getSelectNthActivityDetailsVO(DiscountContext context,
                                                               List<NthActivityDetailsVO> nthActivityList) {
        String selectActivityGuid = context.getCalculateOrderDTO().getActivitySelectList().stream()
                .filter(s -> Objects.equals(DiscountTypeEnum.NTH_ACTIVITY.getCode(), s.getActivityType()))
                .map(ActivitySelectDTO::getActivityGuid)
                .findFirst()
                .orElse(null);
        NthActivityDetailsVO nthActivityDetailsVO = nthActivityList.stream()
                .filter(a -> Objects.equals(selectActivityGuid, a.getGuid()))
                .findFirst()
                .orElse(null);
        if (!ObjectUtils.isEmpty(nthActivityDetailsVO)
                && BooleanEnum.FALSE.getCode() == nthActivityDetailsVO.getRelationRule()
                && Boolean.TRUE.equals(context.getUseMemberDiscountFlag())) {
            log.warn("[第N份优惠]选了第N份优惠再选会员折扣");
            nthActivityDetailsVO = null;
            context.getCalculateOrderDTO().getActivitySelectList().removeIf(s ->
                    Objects.equals(DiscountTypeEnum.NTH_ACTIVITY.getCode(), s.getActivityType()));
        }
        return nthActivityDetailsVO;
    }

    private void addNthActivityInfoList(DiscountContext context,
                                        List<NthActivityDetailsVO> nthActivityList,
                                        List<DineInItemDTO> allItems) {
        BigDecimal orderSurplusFee = context.getCalculateOrderRespDTO().getOrderSurplusFee();
        LimitSpecialsActivityDetailsVO specialsActivitySelectDetailsVO = context.getDiscountRuleBO().getSpecialsActivityDetailsVO();
        String activityGuid = context.getCalculateOrderDTO().getActivitySelectList().stream()
                .filter(s -> Objects.equals(DiscountTypeEnum.ACTIVITY.getCode(), s.getActivityType()))
                .map(ActivitySelectDTO::getActivityGuid)
                .findFirst()
                .orElse(null);
        List<MarketingActivityInfoRespDTO> activityInfoList = new ArrayList<>();
        nthActivityList.forEach(nthActivity -> {
            MarketingActivityInfoRespDTO infoRespDTO = new MarketingActivityInfoRespDTO();
            infoRespDTO.setActivityGuid(nthActivity.getGuid());
            infoRespDTO.setActivityName(nthActivity.getName());
            infoRespDTO.setActivityType(DiscountTypeEnum.NTH_ACTIVITY.getCode());
            infoRespDTO.setUseAble(true);
            infoRespDTO.setDiscountPrice(BigDecimal.ZERO);

            checkOrderSurplusFee(orderSurplusFee, infoRespDTO);

            // 根据选中活动满足的商品进行过滤
            checkNthActivityItem(allItems, nthActivity, infoRespDTO);

            // 选择了优惠券且该优惠券互斥/选择了优满减满折且该活动互斥
            DiscountRuleBO discountRuleBO = context.getDiscountRuleBO();
            if (Boolean.TRUE.equals(!discountRuleBO.getVolumeShareFlag())
                    || Boolean.TRUE.equals(!discountRuleBO.getFullShareFlag())) {
                log.warn("[第N份优惠][互斥处理][选择了优惠券且该优惠券互斥/选择了优满减满折且该活动互斥]volumeShareFlag={},fullShareFlag={}",
                        discountRuleBO.getVolumeShareFlag(), discountRuleBO.getFullShareFlag());
                infoRespDTO.setUseAble(false);
                infoRespDTO.setUnUseReason(Constant.OTHER_DISCOUNT_SHARE);
            }

            // 限时特价活动存在且互斥
            if (!ObjectUtils.isEmpty(specialsActivitySelectDetailsVO) &&
                    Objects.equals(specialsActivitySelectDetailsVO.getRelationRule(), BooleanEnum.FALSE.getCode())) {
                log.warn("[第N份优惠][互斥处理][限时特价活动存在且互斥]");
                infoRespDTO.setUseAble(false);
                infoRespDTO.setUnUseReason(MarketActivityUnableTipEnum.OTHER_DISCOUNT.getView());
            }

            // 有会员折扣且自己互斥
            checkMemberDiscount(context, nthActivity, infoRespDTO);

            // 选择了限时特价，但自己互斥
            if (!ObjectUtils.isEmpty(specialsActivitySelectDetailsVO) &&
                    Objects.equals(BooleanEnum.FALSE.getCode(), nthActivity.getRelationRule())) {
                log.warn("[第N份优惠][互斥处理][选择了限时特价，但自己互斥]");
                infoRespDTO.setUseAble(false);
                infoRespDTO.setUnUseReason(MarketActivityUnableTipEnum.OTHER_DISCOUNT.getView());
            }
            // 选择了优惠券，但自己互斥
            if (StringUtils.hasText(discountRuleBO.getVolumeGuid()) &&
                    Objects.equals(BooleanEnum.FALSE.getCode(), nthActivity.getRelationRule())) {
                log.warn("[第N份优惠][互斥处理][选择了优惠券，但自己互斥]");
                infoRespDTO.setUseAble(false);
                infoRespDTO.setUnUseReason(MarketActivityUnableTipEnum.OTHER_DISCOUNT.getView());
            }
            // 选择了满减满折，但自己互斥
            if (StringUtils.hasText(activityGuid) &&
                    Objects.equals(BooleanEnum.FALSE.getCode(), nthActivity.getRelationRule())) {
                log.warn("[第N份优惠][互斥处理][选择了满减满折，但自己互斥]");
                infoRespDTO.setUseAble(false);
                infoRespDTO.setUnUseReason(MarketActivityUnableTipEnum.OTHER_DISCOUNT.getView());
            }
            activityInfoList.add(infoRespDTO);
        });
        context.getCalculateOrderRespDTO().getActivityInfoList().addAll(activityInfoList);
    }

    private void checkMemberDiscount(DiscountContext context,
                                     NthActivityDetailsVO nthActivity,
                                     MarketingActivityInfoRespDTO infoRespDTO) {
        if (Boolean.TRUE.equals(context.getUseMemberDiscountFlag()) &&
                Objects.equals(BooleanEnum.FALSE.getCode(), nthActivity.getRelationRule())) {
            log.warn("[第N份优惠][互斥处理][有会员折扣且自己互斥]");
            infoRespDTO.setUseAble(false);
            infoRespDTO.setUnUseReason(MarketActivityUnableTipEnum.OTHER_DISCOUNT.getView());
        }
    }

    private void checkNthActivityItem(List<DineInItemDTO> allItems,
                                      NthActivityDetailsVO nthActivity,
                                      MarketingActivityInfoRespDTO infoRespDTO) {
        List<NthActivityItemDTO> nthActivityItemDTOList = nthActivity.getItemDTOList();
        List<String> commodityIdList = nthActivityItemDTOList.stream()
                .map(NthActivityItemDTO::getCommodityId)
                .distinct()
                .collect(Collectors.toList());
        List<DineInItemDTO> itemDTOList = allItems.stream()
                .filter(itemDTO -> commodityIdList.contains(itemDTO.getItemGuid())
                        || commodityIdList.contains(itemDTO.getParentGuid()))
                .collect(Collectors.toList());
        BigDecimal itemCount = priceCalculateHelper.getItemCount(itemDTOList);
        if (itemCount.intValue() < nthActivity.getPerCount()) {
            log.warn("[第N份优惠][互斥处理][商品数量不满足]itemCount={},perCount={}", itemCount, nthActivity.getPerCount());
            infoRespDTO.setUseAble(false);
            infoRespDTO.setUnUseReason(Constant.UN_FULL_CONDITION);
        }
    }

    private void checkOrderSurplusFee(BigDecimal orderSurplusFee,
                                      MarketingActivityInfoRespDTO infoRespDTO) {
        if (!BigDecimalUtil.greaterThanZero(orderSurplusFee)) {
            log.warn("[第N份优惠][互斥处理][金额不足]orderSurplusFee={}", orderSurplusFee);
            infoRespDTO.setUseAble(false);
            infoRespDTO.setUnUseReason(Constant.UN_FULL_CONDITION);
        }
    }

    private void handleNthActivity(DiscountContext context,
                                   NthActivityDetailsVO activityDetailsVO,
                                   DiscountFeeDetailDTO nthActivityDiscountDTO) {
        if (ObjectUtils.isEmpty(activityDetailsVO)) {
            return;
        }
        BigDecimal totalActivityDiscountPrice = priceCalculateHelper.calculateNthActivity(context, activityDetailsVO);
        if (BigDecimalUtil.greaterThanZero(totalActivityDiscountPrice)) {
            DiscountRuleDTO ruleDTO = new DiscountRuleDTO();
            ruleDTO.setActivityGuid(activityDetailsVO.getGuid());
            ruleDTO.setActivityName(activityDetailsVO.getName());
            nthActivityDiscountDTO.setRule(JacksonUtils.writeValueAsString(ruleDTO));
            nthActivityDiscountDTO.setDiscountFee(BigDecimalUtil.setScale2(totalActivityDiscountPrice));

            // 选中活动返回
            ActivitySelectDTO selectDTO = new ActivitySelectDTO();
            selectDTO.setActivityGuid(activityDetailsVO.getGuid());
            selectDTO.setActivityType(DiscountTypeEnum.NTH_ACTIVITY.getCode());
            context.getCalculateOrderRespDTO().getActivitySelectList().add(selectDTO);

            // 更新活动金额
            context.getCalculateOrderRespDTO().getActivityInfoList().forEach(activityInfo -> {
                if (Objects.equals(activityInfo.getActivityGuid(), activityDetailsVO.getGuid())) {
                    activityInfo.setDiscountPrice(BigDecimalUtil.setScale2(totalActivityDiscountPrice));
                }
            });

            // 订单剩余金额
            BigDecimal orderSurplusFee = context.getCalculateOrderRespDTO().getOrderSurplusFee()
                    .subtract(nthActivityDiscountDTO.getDiscountFee());
            context.getCalculateOrderRespDTO().setOrderSurplusFee(orderSurplusFee);
        }
    }

    @Override
    Integer type() {
        return DiscountTypeEnum.NTH_ACTIVITY.getCode();
    }
}
