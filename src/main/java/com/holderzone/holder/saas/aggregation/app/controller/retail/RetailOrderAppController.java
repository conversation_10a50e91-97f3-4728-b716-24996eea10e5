package com.holderzone.holder.saas.aggregation.app.controller.retail;


import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.app.constant.Constant;
import com.holderzone.holder.saas.aggregation.app.service.feign.retail.RetailOrderClientService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.retail.HangOrderDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderDetailRespDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderListReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderListRespDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailRemarkReqDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInOrderAppController
 * @date 2018/09/04 11:26
 * @description app聚合层订单接口
 * @program holder-saas-aggregation-app
 */
@RestController
@RequestMapping("/retail_order")
@Api(description = "零售订单接口")
@Slf4j
public class RetailOrderAppController {

    private final RetailOrderClientService retailOrderClientService;

    @Autowired
    public RetailOrderAppController(RetailOrderClientService retailOrderClientService) {
        this.retailOrderClientService = retailOrderClientService;
    }

    @ApiOperation(value = "订单详情", notes = "订单详情")
    @PostMapping(value = "/get_order_detail", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "订单详情")
    public Result<RetailOrderDetailRespDTO> getOrderDetail(@RequestBody @Validated SingleDataDTO singleDataDTO) {
        log.info("订单详情入参：SingleDataDTO={}", singleDataDTO);
        return Result.buildSuccessResult(retailOrderClientService.getOrderDetail(singleDataDTO));
    }

    @ApiOperation(value = "订单列表", notes = "订单列表")
    @PostMapping("/order_list")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "订单列表")
    public Result<Page<RetailOrderListRespDTO>> orderList(@RequestBody RetailOrderListReqDTO retailOrderListReqDTO) {
        return Result.buildSuccessResult(retailOrderClientService.orderList(retailOrderListReqDTO));
    }

    @ApiOperation(value = "修改整单备注", notes = "修改整单备注")
    @PostMapping("/update_remark")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "修改整单备注")
    public Result updateRemark(@RequestBody RetailRemarkReqDTO retailRemarkReqDTO) {
        if (retailOrderClientService.updateRemark(retailRemarkReqDTO)) {
            return Result.buildEmptySuccess();
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "挂起订单", notes = "挂起订单")
    @PostMapping("/hang_order")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "修改整单备注")
    public Result hangOrder(@RequestBody HangOrderDTO hangOrderDTO) {
        String result = retailOrderClientService.hangOrder(hangOrderDTO);
        if (!StringUtils.isEmpty(result)) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
        }
    }


    @ApiOperation(value = "挂起订单列表", notes = "挂起订单列表")
    @PostMapping("/hang_list")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "修改整单备注")
    public Result<Map<String, String>> hangList(@RequestBody HangOrderDTO hangOrderDTO) {
        Map<String, String> map = retailOrderClientService.hangOrderList(hangOrderDTO);
        if (!StringUtils.isEmpty(map)) {
            return Result.buildSuccessResult(map);
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
        }
    }

    @ApiOperation(value = "删除挂起订单", notes = "删除挂起订单")
    @PostMapping("/gain_order")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "修改整单备注")
    public Result gainOrder(@RequestBody HangOrderDTO hangOrderDTO) {
        boolean result = retailOrderClientService.gainOrder(hangOrderDTO);
        if (result) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
        }
    }

    @ApiOperation(value = "打印结账单", notes = "打印结账单")
    @PostMapping("/print_check_out")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE,description = "打印结账单")
    public Result printCheckOut(@RequestBody SingleDataDTO singleDataDTO) {
        if (retailOrderClientService.printCheckOut(singleDataDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));

    }

}
