package com.holderzone.holder.saas.aggregation.merchant.service.impl;

import com.holderzone.holder.saas.aggregation.merchant.service.StoreParserService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.organization.OrganizationService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.user.UserFeignService;
import com.holderzone.saas.store.dto.organization.StoreParserBaseDTO;
import com.holderzone.saas.store.dto.organization.StoreParserBasePageDTO;
import com.holderzone.saas.store.dto.organization.StoreParserDTO;
import com.holderzone.saas.store.dto.organization.StoreParserPageDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class StoreParserServiceImpl implements StoreParserService {

    private final OrganizationService organizationService;

    private final UserFeignService userFeignService;

    @Autowired
    public StoreParserServiceImpl(OrganizationService organizationService, UserFeignService userFeignService) {
        this.organizationService = organizationService;
        this.userFeignService = userFeignService;
    }

    @Override
    public void parseByCondition(StoreParserDTO storeParserDTO) {
        List<String> storeGuidList = parseByCondition(
                storeParserDTO.getStoreGuidList(),
                storeParserDTO.getBrandGuidList(),
                storeParserDTO.getOrganizationGuidList(),
                storeParserDTO.getRegionCodeList()
        );
        storeParserDTO.setStoreGuidList(storeGuidList);
    }

    @Override
    public void parseByCondition(StoreParserBaseDTO storeParserBaseDTO) {
        List<String> storeGuidList = parseByCondition(
                storeParserBaseDTO.getStoreGuidList(),
                storeParserBaseDTO.getBrandGuidList(),
                storeParserBaseDTO.getOrganizationGuidList(),
                storeParserBaseDTO.getRegionCodeList()
        );
        storeParserBaseDTO.setStoreGuidList(storeGuidList);
    }

    @Override
    public void parseByCondition(StoreParserPageDTO storeParserPageDTO) {
        List<String> storeGuidList = parseByCondition(
                storeParserPageDTO.getStoreGuidList(),
                storeParserPageDTO.getBrandGuidList(),
                storeParserPageDTO.getOrganizationGuidList(),
                storeParserPageDTO.getRegionCodeList()
        );
        storeParserPageDTO.setStoreGuidList(storeGuidList);
    }

    @Override
    public void parseByCondition(StoreParserBasePageDTO storeParserBasePageDTO) {
        List<String> storeGuidList = parseByCondition(
                storeParserBasePageDTO.getStoreGuidList(),
                storeParserBasePageDTO.getBrandGuidList(),
                storeParserBasePageDTO.getOrganizationGuidList(),
                storeParserBasePageDTO.getRegionCodeList()
        );
        storeParserBasePageDTO.setStoreGuidList(storeGuidList);
    }

    private List<String> parseByCondition(List<String> storeGuidList,
                                          List<String> brandGuidList,
                                          List<String> organizationGuidList,
                                          List<String> regionCodeList) {
        List<String> allStoreGuid = userFeignService.queryAllStoreGuid();
        if (CollectionUtils.isEmpty(allStoreGuid)) return Collections.emptyList();
        StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setStoreGuidList(storeGuidList);
        storeParserDTO.setBrandGuidList(brandGuidList);
        storeParserDTO.setOrganizationGuidList(organizationGuidList);
        storeParserDTO.setRegionCodeList(regionCodeList);
        storeParserDTO.setAllStoreGuidList(allStoreGuid);
        return organizationService.parseByCondition(storeParserDTO);
    }
}
