package com.holderzone.holder.saas.aggregation.weixin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.weixin.constant.RedisConstants;
import com.holderzone.holder.saas.aggregation.weixin.service.WxStoreTradeOrderService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.BusinessClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.StoreOrganizationClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.TradeOrderService;
import com.holderzone.holder.saas.weixin.common.CacheName;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.dto.business.manage.SurchargeConditionQuery;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.order.inside.OrderGuidsDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.trade.OrderDetailPushMqDTO;
import com.holderzone.saas.store.dto.trade.OrderItemDTO;
import com.holderzone.saas.store.enums.order.OrderStateEnum;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class WxStoreTradeOrderServiceImpl implements WxStoreTradeOrderService {

    private final RedisUtils redisUtils;

    private final BusinessClientService businessClientService;

    private final StoreOrganizationClientService storeOrganizationClientService;

    private final TradeOrderService tradeOrderService;

    @Override
    public OrderDetailPushMqDTO findByAppletOrderGuid(OrderGuidsDTO orderGuidsDTO) {
        orderGuidsDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        OrderDetailPushMqDTO orderDetailPushMqDTO = tradeOrderService.findByAppletOrderGuid(orderGuidsDTO);
        if (Objects.isNull(orderDetailPushMqDTO)) {
            throw new BusinessException("订单不存在,请刷新后重试");
        }
        OrderDTO orderDTO = orderDetailPushMqDTO.getOrderDTO();
        // 查询门店信息
        StoreDTO storeDTO = storeOrganizationClientService.queryStoreByGuid(orderDTO.getStoreGuid());
        orderDTO.setStoreContactTel(storeDTO.getContactTel());
        if (TradeModeEnum.FAST.getCode() == orderDTO.getTradeMode() && OrderStateEnum.ACCEPT_ORDER.getCode() == orderDTO.getState()) {
            // 快餐待支付订单，需要计算支付倒计时
            LocalDateTime maxTime = orderDTO.getGmtCreate().plusMinutes(15);
            long secondsBetween = ChronoUnit.SECONDS.between(LocalDateTime.now(), maxTime);
            orderDetailPushMqDTO.setSecondsBetween(secondsBetween);
        }
        // 商品明细处理
        wxOrderItemAfterHandler(orderDetailPushMqDTO.getOrderItemDTOList());
        return orderDetailPushMqDTO;
    }


    /**
     * 微信订单商品列表后置处理
     */
    private void wxOrderItemAfterHandler(List<OrderItemDTO> orderItemDTOList) {
        orderItemDTOList.removeIf(e -> Objects.nonNull(e.getParentItemGuid()) && 0 != e.getParentItemGuid());
        orderItemDTOList.forEach(e -> e.setOrderItemGuid(String.valueOf(e.getGuid())));
    }


    @Override
    public void updateFastGuestCount(Integer count) {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        log.info("修改点餐人数,当前会话信息：{}", JacksonUtils.writeValueAsString(wxMemberSessionDTO));
        Assert.notNull(WeixinUserThreadLocal.getDiningTableGuid(), "无法获取扫码信息");
        Assert.notNull(WeixinUserThreadLocal.getOpenId(), "无法获取用户信息");

        // 修改缓存
        redisUtils.setEx(CacheName.USER_COUNT + ":" + WeixinUserThreadLocal.getDiningTableGuid()
                + ":" + WeixinUserThreadLocal.getOpenId(), count, 48, TimeUnit.HOURS);
    }

    @Override
    public void removeFastGuestCount(String diningTableGuid, String openId) {
        redisUtils.delete(CacheName.USER_COUNT + ":" + diningTableGuid + ":" + openId);
    }

    @Override
    public Integer getFastGuestCount(String diningTableGuid, String openId) {
        return (Integer) redisUtils.get(CacheName.USER_COUNT + ":" + diningTableGuid + ":" + openId);
    }

    @Override
    public List<SurchargeLinkDTO> filterTimeLimitTableSurchargeList(String tableGuid, List<SurchargeLinkDTO> surchargeLinkList) {
        if (CollectionUtils.isEmpty(surchargeLinkList)) {
            return Collections.emptyList();
        }
        return surchargeLinkList.stream()
                .filter(e -> !redisUtils.hasKey(String.format(CacheName.TABLE_SURCHARGE_KEY, tableGuid, e.getSurchargeGuid())))
                .collect(Collectors.toList());
    }

    @Override
    public List<SurchargeLinkDTO> querySurchargeList(Integer orderModel, String areaGuid, String diningTableGuid) {
        try {
            SurchargeConditionQuery surchargeQuery = new SurchargeConditionQuery();
            surchargeQuery.setStoreGuid(WeixinUserThreadLocal.getStoreGuid());
            surchargeQuery.setTradeMode(orderModel);
            surchargeQuery.setAreaGuid(areaGuid);
            List<SurchargeLinkDTO> surchargeLinkList = businessClientService.listSurchargeByCondition(surchargeQuery);
            log.info("微信扫码点餐查询附加费明细返回:{}", JacksonUtils.writeValueAsString(surchargeLinkList));
            // 查询是否需要过滤
            if (TradeModeEnum.FAST.getCode() == orderModel) {
                surchargeLinkList = filterTimeLimitTableSurchargeList(diningTableGuid, surchargeLinkList);
            }
            log.info("微信扫码点餐查询附加费明细过滤之后:{}", JacksonUtils.writeValueAsString(surchargeLinkList));
            return surchargeLinkList;
        } catch (Exception e) {
            log.error("微信扫码点餐查询附加费明细异常，e:{}", e.getMessage());
            return Collections.emptyList();
        }
    }

    @Override
    public void transformSurchargeCache(String orderGuid) {
        // 查询订单附加费缓存
        String orderSurchargeStr = (String) redisUtils.get(CacheName.ORDER_SURCHARGE_KEY + orderGuid);
        if (StringUtils.isEmpty(orderSurchargeStr)) {
            return;
        }
        List<SurchargeLinkDTO> surchargeLinkList = JacksonUtils.toObjectList(SurchargeLinkDTO.class, orderSurchargeStr);
        for (SurchargeLinkDTO surchargeLink : surchargeLinkList) {
            if (Objects.isNull(surchargeLink.getEffectiveTime()) || surchargeLink.getEffectiveTime() == 0) {
                continue;
            }
            redisUtils.setEx(String.format(CacheName.TABLE_SURCHARGE_KEY, WeixinUserThreadLocal.getDiningTableGuid(), surchargeLink.getSurchargeGuid()),
                    JacksonUtils.writeValueAsString(surchargeLink), surchargeLink.getEffectiveTime(), TimeUnit.MINUTES);
        }
    }

    @Override
    public void saveAddItemBeforeSurcharges(List<SurchargeLinkDTO> surchargeLinkList) {
        redisUtils.setEx(String.format(RedisConstants.MEMBER_LAST_CARD_GUID, WeixinUserThreadLocal.getDiningTableGuid(), WeixinUserThreadLocal.getOpenId()),
                JacksonUtils.writeValueAsString(surchargeLinkList), 12, TimeUnit.HOURS);
    }

    @Override
    public List<SurchargeLinkDTO> queryAddItemBeforeSurcharges() {
        String surchargeStr = (String) redisUtils.get(String.format(CacheName.TABLE_ADD_ITEM_SURCHARGE_KEY,
                WeixinUserThreadLocal.getDiningTableGuid(), WeixinUserThreadLocal.getOpenId()));
        log.info("查询到加菜前附加费明细缓存数据, surchargeStr:{}", surchargeStr);
        if (StringUtils.isEmpty(surchargeStr)) {
            // 没有则返回null
            return null;
        }
        // 有则返回数组
        return JacksonUtils.toObjectList(SurchargeLinkDTO.class, surchargeStr);
    }

    @Override
    public List<SurchargeLinkDTO> querySurchargeListByRedis(String tableGuid) {
        String surchargeString = (String) redisUtils.get(String.format(RedisConstants.FAST_TABLE_SURCHARGE_KEY, WeixinUserThreadLocal.getDiningTableGuid(),
                WeixinUserThreadLocal.getOpenId()));
        if (StringUtils.isEmpty(surchargeString)) {
            return Lists.newArrayList();
        }
        return JacksonUtils.toObjectList(SurchargeLinkDTO.class, surchargeString);
    }
}
