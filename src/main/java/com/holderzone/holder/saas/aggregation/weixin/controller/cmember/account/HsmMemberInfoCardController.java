package com.holderzone.holder.saas.aggregation.weixin.controller.cmember.account;


import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel;
import com.holderzone.holder.saas.aggregation.weixin.entity.dto.RequestTravelDetail;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.common.enums.CardTypeEnum;
import com.holderzone.holder.saas.member.wechat.dto.card.RequestCardRightDetails;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseCardRight;
import com.holderzone.holder.saas.member.wechat.dto.member.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 会员持卡表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-06-14
 */
@Api(value = "会员卡模块接口")
@RestController
@RequestMapping("/hsmcw/card")
@Slf4j
public class HsmMemberInfoCardController {

    //    @Resource
//    private IHsmMemberInfoCardService hsmMemberInfoCardService;
    @Resource
    private HsaBaseClientService hsaBaseClientService;

    @ApiOperation("查找会员卡及未开通个卡信息")
    @PostMapping("/getMemberCard")
    public Result<ResponseMemberCardAll> getMemberCard(@RequestBody @Validated RequestQueryMemberCardList memberCardListQueryReqDTO) {
        return Result.buildSuccessResult(hsaBaseClientService.getMemberCard(memberCardListQueryReqDTO).getData());
    }

//    @ApiOperation("会员卡充值")
//    @PostMapping("/rechargeMemberCard")
//    public Result<RechargeRespDTO> rechargeMemberCard(@RequestBody @Validated RechargeReqDTO rechargeReqDTO) {
//        return Result.buildSuccessResult(hsmMemberInfoCardService.rechargeMemberCard(rechargeReqDTO));
//    }
//    @ApiOperation("会员卡概要信息")
//    @PostMapping("/getMemberCardSummaryInfo")
//    public Result<MemberCardSummaryRespDTO> getMemberCardSummaryInfo(@RequestBody @Validated MemberCardSummaryQueryReqDTO memberCardSummaryQueryReqDTO) {
//        MemberCardSummaryRespDTO memberCardSummaryInfo = hsmMemberInfoCardService.getMemberCardSummaryInfo(memberCardSummaryQueryReqDTO);
//        //过滤会员价权益
//        List<CardLevelRightDTO> currentLevelCardRightList = memberCardSummaryInfo.getCurrentLevelCardRightList();
//        List<CardLevelRightDTO> collect = currentLevelCardRightList.stream().filter(x -> x.getRightsTypeCode() != 3).collect(Collectors.toList());
//        memberCardSummaryInfo.setCurrentLevelCardRightList(collect);
//        return Result.buildSuccessResult(memberCardSummaryInfo);
//    }

    @ApiOperation("会员卡概要信息")
    @PostMapping("/getMemberCardSummaryInfo")
    public Result<ResponseMemberCardSummary> getMemberCardSummaryInfo(@RequestBody @Validated RequestMemberCardSummaryQuery memberCardSummaryQueryReqDTO) {
        ResponseMemberCardSummary memberCardSummaryInfo = hsaBaseClientService.getMemberCardSummaryInfo(memberCardSummaryQueryReqDTO).getData();
        //权益卡无法获取积分和成长值，应不展示积分和等级相关字段
//        if (memberCardSummaryInfo.getCardType().equals(CardTypeEnum.CARD_TYPE_EQUITY.getCode())) {
//            throw new BusinessException("权益卡积分与成长值已汇总至会员主卡");
//        }
        //过滤会员价权益
        /*if (CollectionUtils.isNotEmpty(memberCardSummaryInfo.getCurrentLevelCardRightList())){
            List<CardLevelRight> collect = memberCardSummaryInfo.getCurrentLevelCardRightList().stream().filter(x -> x.getRightsTypeCode() != 3).collect(Collectors.toList());
            memberCardSummaryInfo.setCurrentLevelCardRightList(collect);
        }*/
        return Result.buildSuccessResult(memberCardSummaryInfo);
    }

    @ApiOperation("会员卡开通")
    @PostMapping("/openMemberCard")
    public Result openMemberCard(@RequestBody @Validated RequestMemberCardOpen requestMemberCardOpen) {
//        boolean flag = hsmMemberInfoCardService.openMemberCard(memberCardOpenReqDTO);
        boolean flag = hsaBaseClientService.openMemberCard(requestMemberCardOpen).getData();
        if (!flag) {
            return Result.buildOpFailedResult("该会员已拥有此卡，请进入主页查看。");
        }

        return Result.buildEmptySuccess();
    }

    @ApiOperation("查看会员卡资金明细")
    @GetMapping("/getFundingTravelDetails")
    public Result getFundingTravelDetails(@NotNull @RequestParam("memberInfoCardGuid") String memberInfoCardGuid) {
//        return Result.buildSuccessResult(hsmMemberInfoCardService.getFundingTravelDetails(memberInfoCardGuid));
//        RequestTravelDetail requestTravelDetail = new RequestTravelDetail();
//        requestTravelDetail.setMemberInfoCardGuid(memberInfoCardGuid);
        RequestTravelDetail requestTravelDetail = new RequestTravelDetail();
        requestTravelDetail.setMemberInfoCardGuid(memberInfoCardGuid);
        return Result.buildSuccessResult(hsaBaseClientService.getFundingTravelDetails(requestTravelDetail).getData());
    }

    @ApiOperation("查看会员卡成长值明细")
    @GetMapping("/getGrowthValueDetails")
    public Result getGrowthValueDetails(@NotNull @RequestParam("memberInfoCardGuid") String memberInfoCardGuid) {
//        return Result.buildSuccessResult(hsmMemberInfoCardService.getGrowthValueDetails(memberInfoCardGuid));
        RequestTravelDetail requestTravelDetail = new RequestTravelDetail();
        requestTravelDetail.setMemberInfoCardGuid(memberInfoCardGuid);
        return Result.buildSuccessResult(hsaBaseClientService.getGrowthValueDetails(requestTravelDetail).getData());
    }

    @ApiOperation("查看会员卡积分明细")
    @GetMapping("/getIntegralTravelDetails")
    public Result getIntegralTravelDetails(@NotNull @RequestParam("memberInfoCardGuid") String memberInfoCardGuid,
                                           @NotNull @RequestParam("cardGuid") String cardGuid) {
//        return Result.buildSuccessResult(hsmMemberInfoCardService.getIntegralTravelDetails(memberInfoCardGuid));
        RequestMemberCardSummaryQuery memberCardSummaryQuery = new RequestMemberCardSummaryQuery();
        memberCardSummaryQuery.setMemberInfoCardGuid(memberInfoCardGuid);
        memberCardSummaryQuery.setCardGuid(cardGuid);
        ResponseMemberCardSummary cardSummary = hsaBaseClientService.getMemberCardSummaryInfo(memberCardSummaryQuery).getData();
        if (Objects.nonNull(cardSummary.getCardType()) && CardTypeEnum.CARD_TYPE_EQUITY.getCode() == cardSummary.getCardType()) {
            throw new BusinessException("权益卡积分与成长值已汇总至会员主卡");
        }

        RequestTravelDetail requestTravelDetail = new RequestTravelDetail();
        requestTravelDetail.setMemberInfoCardGuid(memberInfoCardGuid);
        return Result.buildSuccessResult(hsaBaseClientService.getIntegralTravelDetails(requestTravelDetail).getData());
    }

    //TODO 在微信c端未发现调用 且dto改造受影响  暂时注释掉
//    @ApiOperation("会员卡等级详情（包括等级的权益）")
//    @PostMapping("/getCardLevelSummaryInfo")
//    public Result<CardLevelSummaryRespDTO> getCardLevelSummaryInfo(@RequestBody @Validated MemberCardSummaryQueryReqDTO cardSummaryQueryReqDTO) {
//        return Result.buildSuccessResult(hsmMemberInfoCardService.getCardLevelSummaryInfo(cardSummaryQueryReqDTO));
//    }

    @ApiOperation("查寻会员卡权益详情")
    @PostMapping("/getCardRightDetails")
    public Result<List<ResponseCardRight>> getCardRightDetails(@RequestBody @Validated RequestCardRightDetails cardRightDetailsReqDTO) {
//        List<CardRightDetailsRespDTO> cardRightDetails = hsmMemberInfoCardService.getCardRightDetails(cardRightDetailsReqDTO);
        List<ResponseCardRight> cardRightDetails = hsaBaseClientService.getCardRightDetails(cardRightDetailsReqDTO).getData();
        return Result.buildSuccessResult(cardRightDetails);
    }

    @ApiOperation("会员卡充值金额规则")
    @PostMapping("/getCardSimpleRechargeRule")
    public Result<ResponseCardRechargeRuleSummary> getCardSimpleRechargeRule(@RequestBody @Validated RequestCardRechargeRule cardRechargeRuleReqDTO) {
//		CardRechargeRuleSummaryRespDTO cardSimpleRechargeRule = hsmMemberInfoCardService.getCardSimpleRechargeRule(cardRechargeRuleReqDTO);
        ResponseModel<ResponseCardRechargeRuleSummary> cardSimpleRechargeRule = hsaBaseClientService.getCardSimpleRechargeRule(cardRechargeRuleReqDTO);
        log.info("会员卡充值金额规则返回结果:{}", cardSimpleRechargeRule);
        if (cardSimpleRechargeRule.getCode() == 0){
            return Result.buildSuccessResult(cardSimpleRechargeRule.getData());
        }else {
            return Result.buildOpFailedResult(cardSimpleRechargeRule.getMessage());
        }
    }

    @ApiOperation("查看会员卡余额配置规则")
    @GetMapping("/getFundingRule")
    public Result<ResponseFundingRule> getFundingRule(@RequestParam("memberInfoCardGuid") String memberInfoCardGuid,
                                                      @NotNull @RequestParam("cardGuid") String cardGuid) {
        return Result.buildSuccessResult(hsaBaseClientService.getFundingRule(memberInfoCardGuid, cardGuid).getData());
    }

    @ApiOperation("查看会员卡成长值配置规则")
    @GetMapping("/getGrowthValueRule")
    public Result<ResponseGrowthValueRule> getGrowthValueRule(@RequestParam(value = "systemManagementGuid", required = false) String systemManagementGuid,
                                                              @NotNull @RequestParam("cardGuid") String cardGuid) {
        return Result.buildSuccessResult(hsaBaseClientService.getGrowthValueRule(systemManagementGuid, cardGuid).getData());
    }

    @ApiOperation("查看会员卡积分配置规则")
    @GetMapping("/getIntegralRule")
    public Result<ResponseIntegralRule> getIntegralRule(@RequestParam(value = "systemManagementGuid", required = false) String systemManagementGuid,
                                                        @NotNull @RequestParam("cardGuid") String cardGuid) {
        return Result.buildSuccessResult(hsaBaseClientService.getIntegralRule(systemManagementGuid, cardGuid).getData());
    }
}
