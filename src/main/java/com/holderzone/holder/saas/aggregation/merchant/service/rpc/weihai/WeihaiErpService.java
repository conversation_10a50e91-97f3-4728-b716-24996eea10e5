package com.holderzone.holder.saas.aggregation.merchant.service.rpc.weihai;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.weihai.WeihaiAuthCodeReqDTO;
import com.holderzone.saas.store.dto.weihai.WeihaiCommonRespDTO;
import com.holderzone.saas.store.dto.weihai.WeihaiTokenReqDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * weihai供应链 外部接口
 */
@Component
@FeignClient(name = "weihai-erp-service", fallbackFactory = WeihaiErpService.ServiceFallBack.class, url = "${weihai.erp.sso-host}")
public interface WeihaiErpService {

    @PostMapping("/token")
    WeihaiCommonRespDTO<String> getAccessToken(@RequestBody WeihaiTokenReqDTO tokenReqDTO);

    @PostMapping("/openapi/joyhr/web/xlcapi/getAuthCode")
    WeihaiCommonRespDTO<String> getAuthCode(@RequestHeader("access_token") String accessToken,
                                            @RequestBody WeihaiAuthCodeReqDTO authCodeReqDTO);

    @Component
    @Slf4j
    class ServiceFallBack implements FallbackFactory<WeihaiErpService> {
        private static final String HYSTRIX_PATTERN = "Service call failed {} with params {}, exception {}";

        @Override
        public WeihaiErpService create(Throwable throwable) {
            return new WeihaiErpService() {
                @Override
                public WeihaiCommonRespDTO<String> getAccessToken(WeihaiTokenReqDTO tokenReqDTO) {
                    log.error(HYSTRIX_PATTERN, "getAccessToken",
                            JacksonUtils.writeValueAsString(tokenReqDTO),
                            throwable.getMessage());
                    throw new BusinessException("Failed to get access token");
                }

                @Override
                public WeihaiCommonRespDTO<String> getAuthCode(String accessToken, WeihaiAuthCodeReqDTO authCodeReqDTO) {
                    log.error(HYSTRIX_PATTERN, "getAuthCode",
                            JacksonUtils.writeValueAsString(authCodeReqDTO), throwable.getMessage());
                    throw new BusinessException("Failed to get auth code");
                }
            };
        }
    }
} 