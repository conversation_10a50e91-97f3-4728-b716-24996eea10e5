package com.holderzone.holder.saas.aggregation.weixin.controller.debt;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.DebtClientService;
import com.holderzone.saas.store.dto.trade.DebtRecordH5RespDTO;
import com.holderzone.saas.store.dto.trade.DebtUnitLoginH5ReqDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 挂账Controller
 *
 * @since 2020-12-15
 */
@Slf4j
@Api(value = "挂账接口")
@RestController
@RequestMapping(value = "/debt")
public class DebtController {

    private final DebtClientService debtClientService;

    public DebtController(DebtClientService debtUnitService) {
        this.debtClientService = debtUnitService;
    }

    @PostMapping("/record/h5query")
    @ApiOperation(value = "H5查询挂账还款记录")
    public Result<DebtRecordH5RespDTO> queryDebtRecordH5(@Validated @RequestBody DebtUnitLoginH5ReqDTO reqDTO) {
        log.info("H5查询挂账还款记录入参：{}", JSON.toJSONString(reqDTO));
        return Result.buildSuccessResult(debtClientService.queryDebtRecordH5(reqDTO));
    }
}
