package com.holderzone.holder.saas.aggregation.phoneapp.controller.journaling;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.phoneapp.service.rpc.JournalRpcService;
import com.holderzone.saas.store.dto.journaling.req.JournalAppBaseReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.TotalPaymentRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentStatisticsController
 * @date 2019/06/03 11:03
 * @description 支付统计Controller
 * @program holder-saas-store
 */
@Api(description = "支付统计Controller")
@RequestMapping("/payment")
@RestController
@Slf4j
public class PaymentStatisticsController {
    @Autowired
    JournalRpcService journalRpcService;

    @ApiOperation(value = "支付统计")
    @PostMapping("/totalPayment")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT,description = "支付统计")
    public Result<TotalPaymentRespDTO> totalPayment(@RequestBody JournalAppBaseReqDTO journalAppBaseReqDTO) {
        log.info("支付统计查询请求入参:{}", JacksonUtils.writeValueAsString(journalAppBaseReqDTO));
        return Result.buildSuccessResult(journalRpcService.totalPayment(journalAppBaseReqDTO));
    }
}
