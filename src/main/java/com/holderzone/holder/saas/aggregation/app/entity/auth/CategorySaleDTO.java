package com.holderzone.holder.saas.aggregation.app.entity.auth;

import com.holderzone.holder.saas.aggregation.app.anno.DataAuthFieldControl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 分类销售统计
 */
@Data
@ApiModel
public class CategorySaleDTO {

    /**
     * 商品分类
     */
    @DataAuthFieldControl("category_sale_name")
    private String name;

    /**
     * 销售数量
     */
    @DataAuthFieldControl("category_sale_count")
    private String quantum;

    /**
     * 销售金额
     */
    @DataAuthFieldControl("category_sale_amount")
    private String amount;

    /**
     * 实付金额
     */
    @DataAuthFieldControl("category_sale_actually_amount")
    private String discountAmount;

    @ApiModelProperty(value = "是否合计项,0否 1是")
    private int isTotal = 0;
}