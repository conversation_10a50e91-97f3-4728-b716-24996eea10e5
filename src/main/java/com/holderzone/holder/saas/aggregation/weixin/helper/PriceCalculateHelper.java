package com.holderzone.holder.saas.aggregation.weixin.helper;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.assembler.MarketingActivityAssembler;
import com.holderzone.holder.saas.aggregation.weixin.context.DiscountContext;
import com.holderzone.holder.saas.aggregation.weixin.entity.bo.ItemPiecemealBO;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.ItemClientService;
import com.holderzone.holder.saas.common.enums.ActivitieTypeEnum;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseDiscount;
import com.holderzone.holder.saas.member.terminal.enums.DiscountStoreTypeEnum;
import com.holderzone.holder.saas.member.terminal.enums.FullReductionTypeEnum;
import com.holderzone.holder.saas.member.wechat.dto.activitie.RequestActivityFullDiscountRule;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseActivitieProduct;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseClientMarketActivity;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseMarketActivityUse;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseProductDiscount;
import com.holderzone.holder.saas.member.wechat.dto.enums.MemberRightsType;
import com.holderzone.saas.store.bo.weixin.SpecialsActivityAmountBO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.marketing.nth.NthActivityDetailsVO;
import com.holderzone.saas.store.dto.marketing.nth.NthActivityItemDTO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityDetailsVO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityItemDTO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityItemVO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.weixin.deal.ItemInfoDTO;
import com.holderzone.saas.store.dto.weixin.deal.ItemInfoSkuDTO;
import com.holderzone.saas.store.dto.weixin.deal.SpecialsActivityAmountDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.item.ItemTypeEnum;
import com.holderzone.saas.store.enums.marketing.SpecialsTypeEnum;
import com.holderzone.saas.store.enums.member.MarketActivityUnableTipEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.weixin.MinPriceTypeEnum;
import com.holderzone.saas.store.util.BigDecimalUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/19
 * @description 价格计算辅助类
 */
@Slf4j
@Component
@AllArgsConstructor
public class PriceCalculateHelper {

    public static final String SPECIALS_PRICE_TYPE_ERROR = "特价类型错误";

    private final ItemClientService itemClientService;

    public static BigDecimal getSpecialsPrice(ItemInfoDTO itemInfoDTO, LimitSpecialsActivityItemVO activityItemVO, boolean isTotal) {
        // 活动规则
        BigDecimal specialsNumber = activityItemVO.getSpecialsNumber();
        // 优惠限购为空表示不限制
        boolean isLimit = !ObjectUtils.isEmpty(activityItemVO.getLimitNumber());
        BigDecimal limitNumber = BigDecimal.ZERO;
        if (isLimit) {
            limitNumber = BigDecimal.valueOf(activityItemVO.getLimitNumber().longValue() - activityItemVO.getItemUseNumber().longValue());
        }

        // 参与优惠的商品单价
        BigDecimal originalPrice;
        BigDecimal minPrice;
        BigDecimal currentCount = itemInfoDTO.getCurrentCount();
        if (isTotal) {
            originalPrice = itemInfoDTO.getOriginalPrice().divide(currentCount, 2, RoundingMode.HALF_UP);
            minPrice = itemInfoDTO.getMinPrice().divide(currentCount, 2, RoundingMode.HALF_UP);
        } else {
            originalPrice = itemInfoDTO.getOriginalPrice();
            minPrice = itemInfoDTO.getMinPrice();
            currentCount = BigDecimal.ONE;
        }

        return calculateMinPrice(activityItemVO.getSpecialsType(), currentCount, originalPrice,
                specialsNumber, isLimit, limitNumber, minPrice);
    }

    @NotNull
    private static BigDecimal calculateMinPrice(Integer specialsType,
                                                BigDecimal currentCount,
                                                BigDecimal originalPrice,
                                                BigDecimal specialsNumber,
                                                boolean isLimit,
                                                BigDecimal limitNumber,
                                                BigDecimal minPrice) {
        SpecialsTypeEnum specialsTypeEnum = SpecialsTypeEnum.getEnum(specialsType);
        BigDecimal specialsPrice;
        switch (Objects.requireNonNull(specialsTypeEnum)) {
            case DISCOUNT:
                // 打折
                specialsPrice = calculateDiscount(currentCount, originalPrice, specialsNumber, isLimit, limitNumber, minPrice);
                break;
            case SALE:
                // 减价
                specialsPrice = calculateSale(currentCount, originalPrice, specialsNumber, isLimit, limitNumber, minPrice);
                break;
            case SPECIFY_PRICE:
                // 指定价格：如指定价格≥商品当时售价，则指定价格不生效
                specialsPrice = calculateSpecifyPrice(currentCount, specialsNumber, isLimit, limitNumber, minPrice);
                break;
            default:
                throw new BusinessException(SPECIALS_PRICE_TYPE_ERROR);
        }
        return specialsPrice;
    }

    @NotNull
    private static BigDecimal calculateDiscount(BigDecimal currentCount,
                                                BigDecimal originalPrice,
                                                BigDecimal specialsNumber,
                                                boolean isLimit,
                                                BigDecimal limitNumber,
                                                BigDecimal minPrice) {
        BigDecimal specialsPrice;
        BigDecimal specialsAmount = originalPrice.multiply(specialsNumber)
                .divide(BigDecimal.TEN, 2, RoundingMode.HALF_UP);
        if (!isLimit || BigDecimalUtil.greaterEqual(limitNumber, currentCount)) {
            specialsPrice = specialsAmount.multiply(currentCount).setScale(2, RoundingMode.HALF_UP);
        } else {
            specialsPrice = getOverSpecialsPrice(currentCount, limitNumber, specialsAmount, minPrice);
        }
        return specialsPrice;
    }

    @NotNull
    private static BigDecimal calculateSale(BigDecimal currentCount,
                                            BigDecimal originalPrice,
                                            BigDecimal specialsNumber,
                                            boolean isLimit,
                                            BigDecimal limitNumber,
                                            BigDecimal minPrice) {
        BigDecimal specialsPrice;
        BigDecimal subtracted = originalPrice.subtract(specialsNumber);
        if (BigDecimalUtil.lessThanZero(subtracted)) {
            subtracted = BigDecimal.ZERO;
        }
        if (!isLimit || BigDecimalUtil.greaterEqual(limitNumber, currentCount)) {
            specialsPrice = subtracted.multiply(currentCount);
        } else {
            specialsPrice = getOverSpecialsPrice(currentCount, limitNumber, subtracted, minPrice);
        }
        return specialsPrice;
    }

    @NotNull
    private static BigDecimal calculateSpecifyPrice(BigDecimal currentCount,
                                                    BigDecimal specialsNumber,
                                                    boolean isLimit,
                                                    BigDecimal limitNumber,
                                                    BigDecimal minPrice) {
        BigDecimal specialsPrice;
        if (!isLimit || BigDecimalUtil.greaterEqual(limitNumber, currentCount)) {
            specialsPrice = specialsNumber.multiply(currentCount);
        } else {
            specialsPrice = getOverSpecialsPrice(currentCount, limitNumber, specialsNumber, minPrice);
        }
        return specialsPrice;
    }

    @NotNull
    private static BigDecimal getOverSpecialsPrice(BigDecimal currentCount,
                                                   BigDecimal limitNumber,
                                                   BigDecimal specialsAmount,
                                                   BigDecimal minPrice) {
        BigDecimal residue = currentCount.subtract(limitNumber);
        BigDecimal specialsPrice = specialsAmount.multiply(limitNumber);
        specialsPrice = specialsPrice.add(minPrice.multiply(residue).setScale(2, RoundingMode.HALF_UP));
        return specialsPrice;
    }

    /**
     * 计算单个商品在活动下的优惠金额
     * 单个商品优惠金额 = 商品售价*商品数量 - 商品折扣价合计
     * 都是基于商品售价去计算的
     * 2024.06.24群里有聊天记录
     *
     * @param itemInfoDTO    商品
     * @param activityItemVO 限时特价活动
     * @return 单个商品优惠金额
     */
    public BigDecimal calculateSpecialsActivityDiscountPrice(DineInItemDTO itemInfoDTO,
                                                             LimitSpecialsActivityItemVO activityItemVO) {
        log.info("[计算限时特价]itemInfoDTO={},activityItemVO={}", JacksonUtils.writeValueAsString(itemInfoDTO),
                JacksonUtils.writeValueAsString(activityItemVO));
        // 活动规则
        BigDecimal specialsNumber = activityItemVO.getSpecialsNumber();
        // 优惠限购为空表示不限制
        boolean isLimit = !ObjectUtils.isEmpty(activityItemVO.getLimitNumber());
        BigDecimal limitNumber = BigDecimal.ZERO;
        if (isLimit) {
            limitNumber = BigDecimal.valueOf(activityItemVO.getLimitNumber().longValue() - activityItemVO.getItemUseNumber().longValue());
        }

        // 参与优惠的商品单价
        // 字段originalPrice为前端除了数量的值
        BigDecimal originalPrice = itemInfoDTO.getOriginalPrice();
        BigDecimal goodsReduceDiscount = itemInfoDTO.getIsGoodsReduceDiscount();
        if (ObjectUtils.isEmpty(goodsReduceDiscount)) {
            goodsReduceDiscount = BigDecimal.ZERO;
        }
        BigDecimal currentCount = itemInfoDTO.getCurrentCount().subtract(goodsReduceDiscount);
        if (isLimit && BigDecimalUtil.greaterEqual(currentCount, limitNumber)) {
            currentCount = limitNumber;
        }

        BigDecimal specialsPrice;
        SpecialsTypeEnum specialsTypeEnum = SpecialsTypeEnum.getEnum(activityItemVO.getSpecialsType());
        switch (Objects.requireNonNull(specialsTypeEnum)) {
            case DISCOUNT:
                // 打折
                specialsPrice = calculateDiscount(currentCount, originalPrice, specialsNumber);
                break;
            case SALE:
                // 减价
                specialsPrice = calculateSale(currentCount, originalPrice, specialsNumber);
                break;
            case SPECIFY_PRICE:
                // 指定价格：如指定价格≥商品当时售价，则指定价格不生效
                if (BigDecimalUtil.greaterEqual(specialsNumber, originalPrice)) {
                    return BigDecimal.ZERO;
                }
                specialsPrice = calculateSpecifyPrice(currentCount, specialsNumber);
                break;
            default:
                throw new BusinessException(SPECIALS_PRICE_TYPE_ERROR);
        }
        BigDecimal originalTotalPrice = originalPrice.multiply(currentCount);
        BigDecimal singleActivityDiscountPrice = originalTotalPrice.subtract(specialsPrice);
        if (BigDecimalUtil.lessThanZero(singleActivityDiscountPrice)) {
            singleActivityDiscountPrice = BigDecimal.ZERO;
        }
        // 限时特价和会员优惠比较，如果
        activityItemVO.setItemUseNumber(currentCount.intValue());
        return singleActivityDiscountPrice;
    }

    private static BigDecimal calculateSpecifyPrice(BigDecimal currentCount,
                                                    BigDecimal specialsNumber) {
        return specialsNumber.multiply(currentCount);
    }

    private static BigDecimal calculateSale(BigDecimal currentCount,
                                            BigDecimal originalPrice,
                                            BigDecimal specialsNumber) {
        BigDecimal subtracted = originalPrice.subtract(specialsNumber);
        if (BigDecimalUtil.lessThanZero(subtracted)) {
            subtracted = BigDecimal.ZERO;
        }
        return subtracted.multiply(currentCount);
    }

    private static BigDecimal calculateDiscount(BigDecimal currentCount,
                                                BigDecimal originalPrice,
                                                BigDecimal specialsNumber) {
        BigDecimal specialsAmount = originalPrice.multiply(specialsNumber)
                .divide(BigDecimal.TEN, 2, RoundingMode.HALF_UP);
        return specialsAmount.multiply(currentCount).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 限时特价活动计算
     * 举例：商品a原价15，会员价12，限时特价10（限购2）
     * 互斥情况下：分别计算对比取值更优惠：
     * 限时特价：商品a应付金额=10*2+15=35元，优惠金额=15*3-35=10
     * 会员价：商品a应付金额=12*3=36元，优惠金额=15*3-36=9元
     * 共享情况下：商品a应付金额=10*2+12=32元，优惠金额=15*3-32=13元
     *
     * @param context                 计算综合容器
     * @param activityDetailsVO       选中的活动
     * @return 优惠的金额
     */
    public BigDecimal calculateSpecialsActivityByMemberPrice(DiscountContext context,
                                                             LimitSpecialsActivityDetailsVO activityDetailsVO) {

        // 参与活动的商品
        List<DineInItemDTO> allItems = context.getAllItems();
        calculateItemSkuSpecialsPrice(context, activityDetailsVO, allItems);

        // 会员总优惠价
        BigDecimal totalMemberDiscountPrice = allItems.stream()
                .filter(i -> !ObjectUtils.isEmpty(i.getMemberPrice()))
                .map(i -> i.getOriginalPrice().subtract(i.getMemberPrice())
                        .multiply(i.getCurrentCount().subtract(ObjectUtils.isEmpty(i.getIsGoodsReduceDiscount()) ?
                                BigDecimal.ZERO : i.getIsGoodsReduceDiscount())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 被限时特价覆盖的价格
        BigDecimal totalLimitDiscountPrice = allItems.stream()
                .map(DineInItemDTO::getLimitPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 比较会员价（该阶段只有会员价，会员折扣在后面）
        DiscountFeeDetailDTO singleMemberCard = context.getDiscountFeeDetailDTOS().stream()
                .filter(d -> Objects.equals(d.getDiscountType(), DiscountTypeEnum.SINGLE_MEMBER.getCode()))
                .findFirst()
                .orElse(null);
        // 没有会员价的情况
        if (ObjectUtils.isEmpty(singleMemberCard)) {
            log.info("[限时特价计算]没有会员价的情况");
            // 更新每个商品上的最终优惠价
            updateItemDiscountList(allItems, context);
            // 限时特价总优惠价
            return getTotalSpecialsDiscountPrice(allItems);
        }

        // 会员价优惠小于限时特价优惠，处理限时特价
        boolean isShare = Objects.equals(BooleanEnum.TRUE.getCode(), activityDetailsVO.getRelationRule());
        if (isShare) {
            // 勾选共享。商品同时存在限时特价和会员价，取最低的一个价格计算，记录到对应优惠上；如果有两个商品分别存在限时特价和会员价，勾选其中一个，另一个参与计算
            // 更新每个商品上的最终优惠价
            calculateAllItemShareMemberPrice(context, allItems, singleMemberCard, totalMemberDiscountPrice, totalLimitDiscountPrice);
            return getTotalSpecialsDiscountPrice(allItems);
        } else {
            // 勾选互斥。商品同时存在限时特价和会员价，取最低的一个价格计算，记录到对应优惠上；如果有两个商品分别存在限时特价和会员价，勾选其中最优惠的一个，另一个不参与计算，
            // 如果勾选另一个，原来那个取消勾选则并不参与计算
            BigDecimal totalSpecialsDiscountPrice = allItems.stream()
                    .map(DineInItemDTO::getSpecialsDiscountPrice)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            return handleUnShare(context, singleMemberCard, totalSpecialsDiscountPrice, allItems);
        }
    }

    private void calculateItemSkuSpecialsPrice(DiscountContext context,
                                               LimitSpecialsActivityDetailsVO activityDetailsVO,
                                               List<DineInItemDTO> allItems) {
        // 商品总优惠价
        List<LimitSpecialsActivityItemVO> activityItemVOList = MarketingActivityAssembler.getSpecialsActivityItemVOList(
                activityDetailsVO, activityDetailsVO.getItemDTOList());
        Map<String, LimitSpecialsActivityItemVO> activityItemVOMap = activityItemVOList.stream()
                .collect(Collectors.toMap(LimitSpecialsActivityItemVO::getCommodityId, Function.identity(), (v1, v2) -> v1));

        // 计算前数据净化
        allItems.forEach(item -> {
            item.setSpecialsDiscountPrice(BigDecimal.ZERO);
            item.setSpecialsMemberPrice(BigDecimal.ZERO);
        });
        Map<String, List<DineInItemDTO>> itemMap = allItems.stream()
                .collect(Collectors.groupingBy(i -> ObjectUtils.isEmpty(i.getParentGuid()) ? i.getItemGuid() : i.getParentGuid()));

        ResponseProductDiscount responseProductDiscount = context.getDiscountRuleBO().getResponseProductDiscount();

        // 根据各SKU商品售价计算优惠金额
        for (Map.Entry<String, List<DineInItemDTO>> entry : itemMap.entrySet()) {
            String itemGuid = entry.getKey();
            List<DineInItemDTO> skuItemList = entry.getValue();
            LimitSpecialsActivityItemVO activityItemVO = activityItemVOMap.get(itemGuid);
            if (ObjectUtils.isEmpty(activityItemVO)) {
                log.warn("该商品没有匹配的活动,itemGuid={}", itemGuid);
                continue;
            }

            // 计算每个规格的优惠
            calculateEverySkuDiscount(activityDetailsVO, skuItemList, activityItemVO, responseProductDiscount, context);
            // 排序后重计算
            calculateAgainBySort(activityDetailsVO, skuItemList, activityItemVO, responseProductDiscount, context);

        }
        log.info("[限时特价计算后商品结果]allItems={}", JacksonUtils.writeValueAsString(allItems));
    }

    private void calculateAllItemShareMemberPrice(DiscountContext context,
                                                  List<DineInItemDTO> allItems,
                                                  DiscountFeeDetailDTO singleMemberCard,
                                                  BigDecimal totalMemberDiscountPrice,
                                                  BigDecimal totalLimitDiscountPrice) {
        Boolean useMemberPriceFlag = context.getUseMemberPriceFlag();
        for (DineInItemDTO dineInItem : allItems) {
            // 限时特价优惠的金额
            BigDecimal specialsDiscountPrice = BigDecimalUtil.nonNullValue(dineInItem.getSpecialsDiscountPrice());
            // 限时特价总金额中的会员优惠金额
            BigDecimal specialsMemberPrice = BigDecimalUtil.nonNullValue(dineInItem.getSpecialsMemberPrice());
            // 商品券
            BigDecimal ticketPreferential = BigDecimalUtil.nonNullValue(dineInItem.getTicketPreferential());
            // 代金券
            BigDecimal discountPreferential = BigDecimalUtil.nonNullValue(dineInItem.getDiscountPreferential());

            // 使用会员价
            if (Boolean.TRUE.equals(useMemberPriceFlag)) {
                // 限时特价优惠 vs 会员优惠，相同：优先取会员优惠
                BigDecimal memberPreferential = dineInItem.getMemberPreferential();
                if (ObjectUtils.isEmpty(memberPreferential) || BigDecimalUtil.lessThanZero(memberPreferential)) {
                    memberPreferential = BigDecimal.ZERO;
                }
                if (BigDecimalUtil.greaterEqual(memberPreferential, specialsDiscountPrice.add(specialsMemberPrice))) {
                    dineInItem.setTotalDiscountFee(ticketPreferential);
                    dineInItem.setSpecialsDiscountPrice(BigDecimal.ZERO);
                    continue;
                }
                if (BigDecimalUtil.greaterThanZero(dineInItem.getMemberPrice())) {
                    dineInItem.setMemberPreferential(dineInItem.getOriginalPrice()
                            .subtract(dineInItem.getMemberPrice())
                            .multiply(dineInItem.getCurrentCount())
                            .subtract(ticketPreferential)
                            .subtract(BigDecimalUtil.nonNullValue(dineInItem.getLimitPrice())));
                }
            }
            handleItemBySpecials(dineInItem, specialsDiscountPrice, ticketPreferential, discountPreferential);
        }
        singleMemberCard.setDiscountFee(BigDecimalUtil.setScale2(totalMemberDiscountPrice.subtract(totalLimitDiscountPrice)));
    }

    @NotNull
    private BigDecimal getTotalSpecialsDiscountPrice(List<DineInItemDTO> allItems) {
        return allItems.stream()
                .filter(i -> Objects.nonNull(i) && i.getMinPriceType().equals(MinPriceTypeEnum.LIMITED_SPECIAL_ACTIVITY.getCode()))
                .map(DineInItemDTO::getSpecialsDiscountPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算每个规格的优惠
     */
    private void calculateEverySkuDiscount(LimitSpecialsActivityDetailsVO activityDetailsVO,
                                           List<DineInItemDTO> skuItemList,
                                           LimitSpecialsActivityItemVO activityItemVO,
                                           ResponseProductDiscount responseProductDiscount,
                                           DiscountContext context) {
        activityItemVO.setItemUseNumber(0);
        for (DineInItemDTO skuItem : skuItemList) {
            SpecialsActivityAmountBO amountBO = getSpecialsActivityAmountBO(activityDetailsVO, skuItem, responseProductDiscount);
            amountBO.setIsFirst(context.getIsFirst());
            amountBO.setIsReplace(context.getIsReplace());
            SpecialsActivityAmountDTO amountDTO = calculateMinPrice(skuItem, activityItemVO, amountBO);
            log.info("[限时特价][商品规格计算结果]amountDTO={}", JacksonUtils.writeValueAsString(amountDTO));
            skuItem.setSpecialsDiscountPrice(amountDTO.getDiscountPrice());
            skuItem.setSpecialsMemberPrice(amountDTO.getMemberPrice());
            skuItem.setSpecialsPrice(amountDTO.getSpecialsPrice());
            skuItem.setSpecialsTotalPrice(amountDTO.getTotalPrice());
            skuItem.setLimitPrice(amountDTO.getLimitPrice());
            skuItem.setJoinSpecialsCount(amountDTO.getJoinSpecialsCount());
            skuItem.setSpecialsSingleDistinctPrice(amountDTO.getSpecialsSingleDistinctPrice());
        }
    }

    /**
     * 排序后重计算
     */
    public void calculateAgainBySort(LimitSpecialsActivityDetailsVO activityDetailsVO,
                                     List<DineInItemDTO> skuItemList,
                                     LimitSpecialsActivityItemVO activityItemVO,
                                     ResponseProductDiscount responseProductDiscount,
                                     DiscountContext context) {
        // 从大到小排列优先命中更优商品
        skuItemList.sort(Comparator.comparing(DineInItemDTO::getSpecialsSingleDistinctPrice).reversed());
        calculateEverySkuDiscount(activityDetailsVO, skuItemList, activityItemVO, responseProductDiscount, context);
    }

    private BigDecimal handleUnShare(DiscountContext context,
                                     DiscountFeeDetailDTO singleMemberCard,
                                     BigDecimal totalSpecialsDiscountPrice,
                                     List<DineInItemDTO> toUpdateItemList) {
        if (BigDecimalUtil.lessThan(singleMemberCard.getDiscountFee(), totalSpecialsDiscountPrice)) {
            // 更新每个商品上的最终优惠价
            updateItemList(toUpdateItemList);
            // 会员价需要重新加回去
            singleMemberCard.setDiscountFee(BigDecimal.ZERO);
        } else {
            // 会员优惠更佳，禁用限时特价活动
            context.getCalculateOrderRespDTO().getActivityInfoList().forEach(activity -> {
                activity.setUseAble(false);
                activity.setUnUseReason(MarketActivityUnableTipEnum.OTHER_DISCOUNT.getView());
            });
            totalSpecialsDiscountPrice = BigDecimal.ZERO;
        }
        return totalSpecialsDiscountPrice;
    }

    private void updateItemList(List<DineInItemDTO> toUpdateItemList) {
        toUpdateItemList.forEach(dineInItem -> {
            BigDecimal specialsDiscountPrice = dineInItem.getSpecialsDiscountPrice();
            if (ObjectUtils.isEmpty(specialsDiscountPrice)) {
                specialsDiscountPrice = BigDecimal.ZERO;
            }
            BigDecimal goodsReduceDiscount = dineInItem.getIsGoodsReduceDiscount();
            if (ObjectUtils.isEmpty(goodsReduceDiscount)) {
                goodsReduceDiscount = BigDecimal.ZERO;
            }
            BigDecimal discountTotalPrice = dineInItem.getOriginalPrice()
                    .multiply(dineInItem.getCurrentCount().subtract(goodsReduceDiscount))
                    .subtract(specialsDiscountPrice);
            dineInItem.setDiscountTotalPrice(discountTotalPrice);
            // 商品券
            BigDecimal ticketPreferential = dineInItem.getTicketPreferential();
            if (ObjectUtils.isEmpty(ticketPreferential)) {
                ticketPreferential = BigDecimal.ZERO;
            }
            // 代金券
            BigDecimal discountPreferential = dineInItem.getDiscountPreferential();
            if (ObjectUtils.isEmpty(discountPreferential)) {
                discountPreferential = BigDecimal.ZERO;
            }
            dineInItem.setTotalDiscountFee(specialsDiscountPrice.add(ticketPreferential).add(discountPreferential));
            if (BigDecimalUtil.greaterThanZero(specialsDiscountPrice)) {
                // 商品小计 = 商品小计 - 优惠金额
                dineInItem.setItemPrice(dineInItem.getPrice());
                dineInItem.setMinPrice(BigDecimalUtil.setScale2(dineInItem.getPrice().subtract(specialsDiscountPrice)));
                dineInItem.setMinPriceType(MinPriceTypeEnum.LIMITED_SPECIAL_ACTIVITY.getCode());
            }
        });
    }

    /**.
     * 更新每个商品上的最终优惠价
     */
    private void updateItemDiscountList(List<DineInItemDTO> toUpdateItemList, DiscountContext context) {
        Boolean useMemberPriceFlag = context.getUseMemberPriceFlag();
        toUpdateItemList.forEach(dineInItem -> {
            BigDecimal specialsDiscountPrice = dineInItem.getSpecialsDiscountPrice();
            if (ObjectUtils.isEmpty(specialsDiscountPrice) || BigDecimalUtil.lessThanZero(specialsDiscountPrice)) {
                specialsDiscountPrice = BigDecimal.ZERO;
            }
            // 商品券
            BigDecimal ticketPreferential = BigDecimalUtil.nonNullValue(dineInItem.getTicketPreferential());
            // 代金券
            BigDecimal discountPreferential = BigDecimalUtil.nonNullValue(dineInItem.getDiscountPreferential());

            // 使用会员价
            if (Boolean.TRUE.equals(useMemberPriceFlag)) {
                // 限时特价优惠 vs 会员优惠，相同：优先取会员优惠
                BigDecimal memberPreferential = BigDecimalUtil.nonNullValue(dineInItem.getMemberPreferential());
                if (BigDecimalUtil.greaterEqual(memberPreferential, specialsDiscountPrice)) {
                    // 会员价更优
                    if (Boolean.TRUE.equals(context.getIsFirst())) {
                        dineInItem.setTotalDiscountFee(memberPreferential.add(ticketPreferential).add(discountPreferential));
                        return;
                    }
                    handleItemBySpecials(dineInItem, specialsDiscountPrice, ticketPreferential, discountPreferential);
                    return;
                }
            }
            handleItemBySpecialsNoMember(dineInItem, specialsDiscountPrice, ticketPreferential, discountPreferential);
        });
    }

    private void handleItemBySpecialsNoMember(DineInItemDTO dineInItem,
                                              BigDecimal specialsDiscountPrice,
                                              BigDecimal ticketPreferential,
                                              BigDecimal discountPreferential) {
        BigDecimal goodsReduceDiscount = BigDecimalUtil.nonNullValue(dineInItem.getIsGoodsReduceDiscount());
        BigDecimal discountTotalPrice = dineInItem.getOriginalPrice()
                .multiply(dineInItem.getCurrentCount().subtract(goodsReduceDiscount))
                .subtract(specialsDiscountPrice);
        dineInItem.setDiscountTotalPrice(discountTotalPrice);
        dineInItem.setTotalDiscountFee(specialsDiscountPrice.add(ticketPreferential).add(discountPreferential));
        if (BigDecimalUtil.greaterThanZero(specialsDiscountPrice)) {
            // 商品小计 = 商品小计 - 优惠金额
            dineInItem.setItemPrice(dineInItem.getPrice()
                    .subtract(BigDecimalUtil.nonNullValue(dineInItem.getMemberPreferential())));
            dineInItem.setMinPrice(BigDecimalUtil.setScale2(dineInItem.getOriginalPrice()
                    .multiply(dineInItem.getCurrentCount())
                    .subtract(specialsDiscountPrice)
            ));
            dineInItem.setMinPriceType(MinPriceTypeEnum.LIMITED_SPECIAL_ACTIVITY.getCode());
        }
    }

    private void handleItemBySpecials(DineInItemDTO dineInItem,
                                      BigDecimal specialsDiscountPrice,
                                      BigDecimal ticketPreferential,
                                      BigDecimal discountPreferential) {
        BigDecimal specialsMemberPrice = dineInItem.getSpecialsMemberPrice();
        if (ObjectUtils.isEmpty(specialsMemberPrice) || BigDecimalUtil.lessThanZero(specialsMemberPrice)) {
            specialsMemberPrice = BigDecimal.ZERO;
        }
        BigDecimal goodsReduceDiscount = BigDecimalUtil.nonNullValue(dineInItem.getIsGoodsReduceDiscount());
        BigDecimal discountTotalPrice = dineInItem.getOriginalPrice()
                .multiply(dineInItem.getCurrentCount().subtract(goodsReduceDiscount))
                .subtract(specialsDiscountPrice)
                .subtract(specialsMemberPrice);
        dineInItem.setDiscountTotalPrice(discountTotalPrice);
        dineInItem.setTotalDiscountFee(specialsDiscountPrice.add(ticketPreferential).add(discountPreferential));
        if (BigDecimalUtil.greaterThanZero(specialsDiscountPrice)) {
            // 商品小计 = 商品小计 - 优惠金额
            dineInItem.setItemPrice(dineInItem.getPrice()
                    .subtract(BigDecimalUtil.nonNullValue(dineInItem.getMemberPreferential())));
            dineInItem.setMinPrice(BigDecimalUtil.setScale2(dineInItem.getOriginalPrice()
                    .multiply(dineInItem.getCurrentCount())
                    .subtract(specialsDiscountPrice)
                    .subtract(specialsMemberPrice)));
            dineInItem.setMinPriceType(MinPriceTypeEnum.LIMITED_SPECIAL_ACTIVITY.getCode());
        }
    }

    public SpecialsActivityAmountBO getSpecialsActivityAmountBO(LimitSpecialsActivityDetailsVO activityDetailsVO,
                                                                DineInItemDTO dineInItemDTO) {
        boolean isShare = Objects.equals(BooleanEnum.TRUE.getCode(), activityDetailsVO.getRelationRule());
        SpecialsActivityAmountBO amountBO = new SpecialsActivityAmountBO();
        amountBO.setActivityAmountDTO(new SpecialsActivityAmountDTO());
        if (isShare) {
            if (BigDecimalUtil.greaterThanZero(dineInItemDTO.getMemberPrice())) {
                // 共享 计算限时特价+会员价
                amountBO.setMinPrice(dineInItemDTO.getMemberPrice());
                amountBO.setMinPriceType(MinPriceTypeEnum.MEMBER_PRICE.getCode());
            } else {
                // 没有会员价时设置原价
                amountBO.setMinPrice(dineInItemDTO.getOriginalPrice());
                amountBO.setMinPriceType(MinPriceTypeEnum.ORIGINAL_PRICE.getCode());
            }
        } else {
            // 互斥 计算限时特价+原价
            amountBO.setMinPrice(dineInItemDTO.getOriginalPrice());
            amountBO.setMinPriceType(MinPriceTypeEnum.ORIGINAL_PRICE.getCode());
        }
        return amountBO;
    }

    public SpecialsActivityAmountDTO calculateMinPrice(DineInItemDTO dineInItemDTO,
                                                        LimitSpecialsActivityItemVO activityItemVO,
                                                        SpecialsActivityAmountBO amountBO) {
        log.info("[限时特价][计算金额]dineInItemDTO={},activityItemVO={},amountBO={}",
                JacksonUtils.writeValueAsString(dineInItemDTO),
                JacksonUtils.writeValueAsString(activityItemVO),
                JacksonUtils.writeValueAsString(amountBO));
        // 活动规则
        BigDecimal specialsNumber = activityItemVO.getSpecialsNumber();
        amountBO.setSpecialsNumber(specialsNumber);
        // 优惠限购为空表示不限制
        boolean isLimit = !ObjectUtils.isEmpty(activityItemVO.getLimitNumber());
        amountBO.setLimit(isLimit);
        BigDecimal limitNumber = BigDecimal.ZERO;
        if (isLimit) {
            limitNumber = BigDecimal.valueOf(activityItemVO.getLimitNumber().longValue() - activityItemVO.getItemUseNumber().longValue());
        }
        amountBO.setLimitNumber(limitNumber);

        // 参与优惠的商品单价
        BigDecimal originalPrice = dineInItemDTO.getOriginalPrice();
        amountBO.setOriginalPrice(originalPrice);

        // 参与限时特价的数量
        BigDecimal goodsReduceDiscount = dineInItemDTO.getIsGoodsReduceDiscount();
        if (ObjectUtils.isEmpty(goodsReduceDiscount)) {
            goodsReduceDiscount = BigDecimal.ZERO;
        }
        BigDecimal currentCount = dineInItemDTO.getCurrentCount().subtract(goodsReduceDiscount);
        amountBO.setCurrentCount(currentCount);

        SpecialsTypeEnum specialsTypeEnum = SpecialsTypeEnum.getEnum(activityItemVO.getSpecialsType());
        SpecialsActivityAmountDTO amountDTO = new SpecialsActivityAmountDTO();
        switch (Objects.requireNonNull(specialsTypeEnum)) {
            case DISCOUNT:
                // 打折
                calculateDiscountByAmountBO(amountBO, amountDTO);
                break;
            case SALE:
                // 减价
                calculateSaleByAmountBO(amountBO, amountDTO);
                break;
            case SPECIFY_PRICE:
                // 指定价格：如指定价格≥商品当时售价，则指定价格不生效
                calculateSpecifyPriceByAmountBO(amountBO, amountDTO);
                break;
            default:
                throw new BusinessException(SPECIALS_PRICE_TYPE_ERROR);
        }

        // 限时特价优惠的金额
        BigDecimal memberPrice = amountDTO.getMemberPrice();
        if (ObjectUtils.isEmpty(memberPrice)) {
            memberPrice = BigDecimal.ZERO;
        }
        BigDecimal discountPrice = dineInItemDTO.getOriginalPrice()
                .multiply(dineInItemDTO.getCurrentCount().subtract(goodsReduceDiscount))
                .subtract(amountDTO.getTotalPrice())
                .subtract(memberPrice);
        amountDTO.setDiscountPrice(discountPrice.setScale(2, RoundingMode.HALF_UP));
        activityItemVO.setItemUseNumber(activityItemVO.getItemUseNumber() + amountDTO.getJoinSpecialsCount().intValue());
        return amountDTO;
    }

    private void calculateSpecifyPriceByAmountBO(SpecialsActivityAmountBO amountBO, SpecialsActivityAmountDTO amountDTO) {
        // 如指定价格≥商品当时售价，则指定价格不生效
        if (BigDecimalUtil.greaterEqual(amountBO.getSpecialsNumber(), amountBO.getOriginalPrice())) {
            amountDTO.setSpecialsSingleDistinctPrice(BigDecimal.ZERO);
            amountDTO.setJoinSpecialsCount(BigDecimal.ZERO);
            amountDTO.setLimitPrice(BigDecimal.ZERO);
            amountDTO.setMemberPrice(BigDecimal.ZERO);
            BigDecimal specialsPrice = amountBO.getOriginalPrice().multiply(amountBO.getCurrentCount());
            amountDTO.setSpecialsPrice(specialsPrice.setScale(2, RoundingMode.HALF_UP));
            amountDTO.setTotalPrice(specialsPrice.setScale(2, RoundingMode.HALF_UP));
            return;
        }

        BigDecimal specialsPrice;
        amountDTO.setSpecialsSingleDistinctPrice(amountBO.getOriginalPrice().subtract(amountBO.getSpecialsNumber()));
        if (!amountBO.isLimit() || BigDecimalUtil.greaterEqual(amountBO.getLimitNumber(), amountBO.getCurrentCount())) {
            specialsPrice = amountBO.getSpecialsNumber().multiply(amountBO.getCurrentCount());
            amountDTO.setJoinSpecialsCount(amountBO.getCurrentCount());
            amountDTO.setSpecialsPrice(specialsPrice.setScale(2, RoundingMode.HALF_UP));
            amountDTO.setMemberPrice(BigDecimal.ZERO);
            calculateLimitPrice(amountBO, amountDTO);
        } else {
            specialsPrice = getSummaryPrice(amountBO, amountBO.getSpecialsNumber(), amountDTO);
        }
        amountDTO.setTotalPrice(specialsPrice.setScale(2, RoundingMode.HALF_UP));
    }

    private void calculateLimitPrice(SpecialsActivityAmountBO amountBO, SpecialsActivityAmountDTO amountDTO) {
        if (Objects.equals(MinPriceTypeEnum.MEMBER_PRICE.getCode(), amountBO.getMinPriceType()) ||
                Objects.equals(MinPriceTypeEnum.MEMBER_DISCOUNT.getCode(), amountBO.getMinPriceType())) {
            // 限时特价优惠 vs 会员优惠，相同：优先取会员优惠
            if (BigDecimalUtil.lessEqual(amountBO.getMinPrice().multiply(amountBO.getCurrentCount()), amountDTO.getSpecialsPrice())) {
                amountDTO.setLimitPrice(BigDecimal.ZERO);
                return;
            }
            BigDecimal difference = amountBO.getOriginalPrice().subtract(amountBO.getMinPrice());
            BigDecimal limitPrice = difference.multiply(amountBO.getCurrentCount());
            amountDTO.setLimitPrice(limitPrice.setScale(2, RoundingMode.HALF_UP));
        }
    }

    private void calculateSaleByAmountBO(SpecialsActivityAmountBO amountBO, SpecialsActivityAmountDTO amountDTO) {
        BigDecimal specialsPrice;
        BigDecimal subtracted = amountBO.getOriginalPrice().subtract(amountBO.getSpecialsNumber());
        if (BigDecimalUtil.lessThanZero(subtracted)) {
            subtracted = BigDecimal.ZERO;
        }
        amountDTO.setSpecialsSingleDistinctPrice(amountBO.getOriginalPrice().subtract(subtracted));
        if (!amountBO.isLimit() || BigDecimalUtil.greaterEqual(amountBO.getLimitNumber(), amountBO.getCurrentCount())) {
            specialsPrice = subtracted.multiply(amountBO.getCurrentCount());
            amountDTO.setJoinSpecialsCount(amountBO.getCurrentCount());
            amountDTO.setSpecialsPrice(specialsPrice.setScale(2, RoundingMode.HALF_UP));
            amountDTO.setMemberPrice(BigDecimal.ZERO);
            calculateLimitPrice(amountBO, amountDTO);
        } else {
            specialsPrice = getSummaryPrice(amountBO, subtracted, amountDTO);
        }
        amountDTO.setTotalPrice(specialsPrice.setScale(2, RoundingMode.HALF_UP));
    }

    private void calculateDiscountByAmountBO(SpecialsActivityAmountBO amountBO,
                                             SpecialsActivityAmountDTO amountDTO) {
        BigDecimal specialsPrice;
        BigDecimal specialsAmount = amountBO.getOriginalPrice().multiply(amountBO.getSpecialsNumber())
                .divide(BigDecimal.TEN, 2, RoundingMode.HALF_UP);
        amountDTO.setSpecialsSingleDistinctPrice(amountBO.getOriginalPrice().subtract(specialsAmount));
        if (!amountBO.isLimit() || BigDecimalUtil.greaterEqual(amountBO.getLimitNumber(), amountBO.getCurrentCount())) {
            amountDTO.setJoinSpecialsCount(amountBO.getCurrentCount());
            specialsPrice = specialsAmount.multiply(amountBO.getCurrentCount());
            amountDTO.setSpecialsPrice(specialsPrice.setScale(2, RoundingMode.HALF_UP));
            amountDTO.setMemberPrice(BigDecimal.ZERO);
            calculateLimitPrice(amountBO, amountDTO);
        } else {
            specialsPrice = getSummaryPrice(amountBO, specialsAmount, amountDTO);
        }
        amountDTO.setTotalPrice(specialsPrice.setScale(2, RoundingMode.HALF_UP));
    }

    @NotNull
    private BigDecimal getSummaryPrice(SpecialsActivityAmountBO amountBO,
                                       BigDecimal specialsAmount,
                                       SpecialsActivityAmountDTO amountDTO) {
        amountDTO.setJoinSpecialsCount(amountBO.getLimitNumber());
        BigDecimal specialsPrice;
        // 多余的没有参与的数量
        BigDecimal residue = amountBO.getCurrentCount().subtract(amountBO.getLimitNumber());
        specialsPrice = specialsAmount.multiply(amountBO.getLimitNumber());
        amountDTO.setSpecialsPrice(specialsPrice.setScale(2, RoundingMode.HALF_UP));
        BigDecimal residuePrice = amountBO.getMinPrice().multiply(residue);
        BigDecimal memberPrice = BigDecimal.ZERO;
        BigDecimal limitPrice = BigDecimal.ZERO;
        if (Objects.equals(MinPriceTypeEnum.MEMBER_PRICE.getCode(), amountBO.getMinPriceType()) ||
                Objects.equals(MinPriceTypeEnum.MEMBER_DISCOUNT.getCode(), amountBO.getMinPriceType())) {
            // 差价
            BigDecimal difference = amountBO.getOriginalPrice().subtract(amountBO.getMinPrice());
            memberPrice = difference.multiply(residue);
            limitPrice = difference.multiply(amountBO.getLimitNumber());
            // 限时特价优惠 vs 会员优惠，相同：优先取会员优惠
            if (BigDecimalUtil.lessEqual(amountBO.getMinPrice().multiply(amountBO.getCurrentCount()),
                    amountDTO.getSpecialsPrice().add(amountBO.getMinPrice().multiply(residue)))) {
                memberPrice = BigDecimal.ZERO;
                limitPrice = BigDecimal.ZERO;
            }
        }
        amountDTO.setLimitPrice(limitPrice.setScale(2, RoundingMode.HALF_UP));
        amountDTO.setMemberPrice(memberPrice.setScale(2, RoundingMode.HALF_UP));
        specialsPrice = specialsPrice.add(residuePrice);
        return specialsPrice;
    }

    /**
     * 会员折扣计算限时特价
     */
    public void calculateSpecialsActivityByMemberDiscount(DiscountContext context,
                                                          DiscountFeeDetailDTO member,
                                                          ResponseDiscount pureDiscount,
                                                          DiscountFeeDetailDTO limitSpecialsActivity,
                                                          ResponseDiscount discountShare) {
        List<DineInItemDTO> allItems = context.getAllItems();
        calculateItemSkuSpecialsPrice(context, context.getDiscountRuleBO().getSpecialsActivityDetailsVO(), context.getBeforeSpecialsItems());

        // 选中的活动
        LimitSpecialsActivityDetailsVO specialsActivitySelectDetailsVO = context.getDiscountRuleBO().getSpecialsActivityDetailsVO();
        if (ObjectUtils.isEmpty(specialsActivitySelectDetailsVO)) {
            member.setDiscountFee(BigDecimal.ZERO);
            log.warn("[会员折扣计算]限时特价活动不存在");
            return;
        }
        boolean isShare = Objects.equals(BooleanEnum.TRUE.getCode(), specialsActivitySelectDetailsVO.getRelationRule());
        if (isShare) {
            // 共享：限时特价+代金券+会员折扣
            handleShare(context, member, discountShare, allItems, pureDiscount, limitSpecialsActivity);
            // 订单剩余金额更新
            context.getCalculateOrderRespDTO().setOrderSurplusFee(context.getCalculateOrderRespDTO().getOrderSurplusFee()
                    .subtract(member.getDiscountFee()));
            return;
        }
        // A与B比较
        if (BigDecimalUtil.greaterEqual(pureDiscount.getDeductionMoney(), limitSpecialsActivity.getDiscountFee())) {
            List<RequestDishInfo> dishInfoList = pureDiscount.getRequestDishInfoList();
            if (!CollectionUtils.isEmpty(dishInfoList)) {
                handleDishInfoList(context, dishInfoList);
            }
            member.setDiscountFee(BigDecimalUtil.setScale2(pureDiscount.getDeductionMoney()));

            // 取消默认选中
            context.getCalculateOrderRespDTO().getActivitySelectList().removeIf(a ->
                    Objects.equals(a.getActivityType(), DiscountTypeEnum.LIMIT_SPECIALS_ACTIVITY.getCode()));
            // 重置限时特价金额
            limitSpecialsActivity.setDiscountFee(BigDecimal.ZERO);

            // 订单剩余金额更新
            context.getCalculateOrderRespDTO().setOrderSurplusFee(context.getCalculateOrderRespDTO().getOrderSurplusFeeBySkipSpecials()
                    .subtract(member.getDiscountFee()));
            log.info("[互斥]会员折扣优惠 > 限时特价优惠");
            return;
        }
        // 更新每个商品上的最终优惠价
        handleAllItems(allItems);
        member.setDiscountFee(BigDecimal.ZERO);

        // 订单剩余金额更新
        context.getCalculateOrderRespDTO().setOrderSurplusFee(context.getCalculateOrderRespDTO().getOrderSurplusFee()
                .subtract(member.getDiscountFee()));
    }

    private void handleShare(DiscountContext context,
                             DiscountFeeDetailDTO member,
                             ResponseDiscount discountShare,
                             List<DineInItemDTO> allItems,
                             ResponseDiscount pureDiscount,
                             DiscountFeeDetailDTO limitSpecialsActivity) {
        // 更新每个商品上的最终优惠价
        updateItemDiscountList(allItems, context);

        // 参与限时特价后多余数量的折扣优惠 + 没有参加限时特价活动商品的折扣优惠
        BigDecimal totalMemberDiscountPrice = calculateTotalMemberDiscountPrice(allItems, discountShare, pureDiscount);
        member.setDiscountFee(BigDecimalUtil.setScale2(totalMemberDiscountPrice));

        BigDecimal totalSpecialsDiscountPrice = getTotalSpecialsDiscountPrice(allItems);

        // 取消默认选中
        if (!BigDecimalUtil.greaterThanZero(totalSpecialsDiscountPrice)) {
            context.getCalculateOrderRespDTO().getActivitySelectList().removeIf(a ->
                    Objects.equals(a.getActivityType(), DiscountTypeEnum.LIMIT_SPECIALS_ACTIVITY.getCode()));
        }
        // 返回活动信息处理
        respActivityHandle(context, allItems);
        limitSpecialsActivity.setDiscountFee(BigDecimalUtil.setScale2(totalSpecialsDiscountPrice));
    }

    public void respActivityHandle(DiscountContext context, List<DineInItemDTO> allItems) {
        Map<String, List<DineInItemDTO>> activityItemMap = allItems.stream()
                .filter(i -> StringUtils.hasText(i.getSpecialsActivityGuid()) &&
                        Objects.equals(i.getMinPriceType(), MinPriceTypeEnum.LIMITED_SPECIAL_ACTIVITY.getCode()))
                .collect(Collectors.groupingBy(DineInItemDTO::getSpecialsActivityGuid));
        if (CollectionUtils.isEmpty(activityItemMap)) {
            return;
        }

        context.getCalculateOrderRespDTO().getActivityInfoList().forEach(activity -> {
            // 只处理的商品列表对应的活动列表；不能直接置零，要用扣减的方式，判断为0才这么处理
            // 场景1：只有这个商品，直接处理活动
            // 场景2：有多个该活动商品。要扣除这个商品涉及的金额
            // 场景3：有多个活动商品，要在该商品的活动上扣除金额
            // 总结：商品里要有活动id，以是否是限时特价来判断？
            List<DineInItemDTO> inItemDTOList = activityItemMap.get(activity.getActivityGuid());
            if (!CollectionUtils.isEmpty(inItemDTOList)) {
                BigDecimal specialsPrice = inItemDTOList.stream()
                        .map(DineInItemDTO::getSpecialsDiscountPrice)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                activity.setDiscountPrice(specialsPrice.setScale(2, RoundingMode.HALF_UP));
            } else {
                activity.setDiscountPrice(BigDecimal.ZERO);
            }

            if (activity.isUseAble() && !BigDecimalUtil.greaterThanZero(activity.getDiscountPrice())) {
                activity.setUseAble(false);
                activity.setUnUseReason(MarketActivityUnableTipEnum.OTHER_DISCOUNT.getView());
            }
        });
    }

    private void handleAllItems(List<DineInItemDTO> allItems) {
        allItems.forEach(dineInItem -> {
            BigDecimal specialsDiscountPrice = dineInItem.getSpecialsDiscountPrice();
            if (ObjectUtils.isEmpty(specialsDiscountPrice)) {
                specialsDiscountPrice = BigDecimal.ZERO;
            }
            BigDecimal goodsReduceDiscount = dineInItem.getIsGoodsReduceDiscount();
            if (ObjectUtils.isEmpty(goodsReduceDiscount)) {
                goodsReduceDiscount = BigDecimal.ZERO;
            }
            // 商品小计 = 商品小计 - 优惠金额
            dineInItem.setItemPrice(dineInItem.getPrice());
            dineInItem.setMinPrice(BigDecimalUtil.setScale2(dineInItem.getOriginalPrice()
                    .multiply(dineInItem.getCurrentCount())
                    .subtract(specialsDiscountPrice)));
            dineInItem.setMinPriceType(MinPriceTypeEnum.LIMITED_SPECIAL_ACTIVITY.getCode());
            BigDecimal discountTotalPrice = dineInItem.getOriginalPrice()
                    .multiply(dineInItem.getCurrentCount().subtract(goodsReduceDiscount))
                    .subtract(specialsDiscountPrice);
            dineInItem.setDiscountTotalPrice(discountTotalPrice);
            // 商品券
            BigDecimal ticketPreferential = dineInItem.getTicketPreferential();
            if (ObjectUtils.isEmpty(ticketPreferential)) {
                ticketPreferential = BigDecimal.ZERO;
            }
            // 代金券
            BigDecimal discountPreferential = dineInItem.getDiscountPreferential();
            if (ObjectUtils.isEmpty(discountPreferential)) {
                discountPreferential = BigDecimal.ZERO;
            }
            dineInItem.setTotalDiscountFee(specialsDiscountPrice.add(ticketPreferential).add(discountPreferential));
        });
    }

    private void handleDishInfoList(DiscountContext context,
                                    List<RequestDishInfo> dishInfoList) {
        Map<String, DineInItemDTO> itemMap = context.getAllItems().stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (v1, v2) -> v1));
        dishInfoList.forEach(dish -> {
            if (dish.getDiscountMoney() != null) {
                DineInItemDTO inItemDTO = itemMap.get(dish.getOrderItemGuid());
                setMinPrice(dish, inItemDTO);

                setTotalDiscountFee(context, dish);
            }
        });
    }

    private void setTotalDiscountFee(DiscountContext context, RequestDishInfo dish) {
        DineInItemDTO dineInItemDTO = context.getDineInItemDTOMap().get(dish.getOrderItemGuid());
        if (dineInItemDTO != null && dish.getDiscountMoney() != null) {
            BigDecimal totalDiscountFee = dineInItemDTO.getTotalDiscountFee();
            if (ObjectUtils.isEmpty(totalDiscountFee)) {
                totalDiscountFee = BigDecimal.ZERO;
            }
            dineInItemDTO.setTotalDiscountFee(totalDiscountFee.add(dish.getDiscountMoney()));
        }
    }

    private void setMinPrice(RequestDishInfo dish, DineInItemDTO inItemDTO) {
        if (!ObjectUtils.isEmpty(inItemDTO)) {
            BigDecimal goodsReduceDiscount = inItemDTO.getIsGoodsReduceDiscount();
            if (ObjectUtils.isEmpty(goodsReduceDiscount)) {
                goodsReduceDiscount = BigDecimal.ZERO;
            }
            inItemDTO.setDiscountTotalPrice(inItemDTO.getOriginalPrice()
                    .multiply(inItemDTO.getCurrentCount().subtract(goodsReduceDiscount))
                    .subtract(dish.getDiscountMoney()));
            // 商品小计 = 商品小计 - 优惠金额
            inItemDTO.setItemPrice(inItemDTO.getPrice());
            inItemDTO.setMinPrice(BigDecimalUtil.setScale2(inItemDTO.getOriginalPrice()
                    .multiply(inItemDTO.getCurrentCount())
                    .subtract(dish.getDiscountMoney())));
            inItemDTO.setMinPriceType(MinPriceTypeEnum.MEMBER_DISCOUNT.getCode());
        }
    }

    @NotNull
    private BigDecimal calculateTotalMemberDiscountPrice(List<DineInItemDTO> allItems,
                                                         ResponseDiscount discountShare,
                                                         ResponseDiscount pureDiscount) {
        List<RequestDishInfo> dishInfoShareList = discountShare.getRequestDishInfoList();
        Map<String, RequestDishInfo> dishSpecShareInfoMap = dishInfoShareList.stream()
                .collect(Collectors.toMap(RequestDishInfo::getDishSpecification, Function.identity(), (v1, v2) -> v1));
        List<RequestDishInfo> dishInfoPureList = pureDiscount.getRequestDishInfoList();
        Map<String, RequestDishInfo> dishSpecPureInfoMap = dishInfoPureList.stream()
                .collect(Collectors.toMap(RequestDishInfo::getDishSpecification, Function.identity(), (v1, v2) -> v1));
        return allItems.stream()
                .map(itemDTO -> {
                    RequestDishInfo dishSpecPureInfo = dishSpecPureInfoMap.get(itemDTO.getSkuGuid());
                    if (Objects.isNull(dishSpecPureInfo)) {
                        return BigDecimal.ZERO;
                    }
                    // 限时特价的优惠
                    BigDecimal specialsDiscountPrice = itemDTO.getSpecialsDiscountPrice();
                    if (ObjectUtils.isEmpty(specialsDiscountPrice) || BigDecimalUtil.lessThanZero(specialsDiscountPrice)) {
                        specialsDiscountPrice = BigDecimal.ZERO;
                    }
                    // 会员折扣的优惠
                    BigDecimal discountMoney = dishSpecPureInfo.getDiscountMoney();
                    // 商品券
                    BigDecimal ticketPreferential = BigDecimalUtil.nonNullValue(itemDTO.getTicketPreferential());
                    // 代金券
                    BigDecimal discountPreferential = BigDecimalUtil.nonNullValue(itemDTO.getDiscountPreferential());
                    // 限时特价总金额中的会员优惠金额
                    BigDecimal specialsMemberPrice = BigDecimalUtil.nonNullValue(itemDTO.getSpecialsMemberPrice());
                    // 限时特价优惠 vs 会员优惠，相同：优先取会员优惠
                    if (BigDecimalUtil.greaterEqual(discountMoney, specialsDiscountPrice.add(specialsMemberPrice))) {
                        // 会员优惠更优,discountMoney已包含代金券处理-纯
                        BigDecimal discountTotalPrice = itemDTO.getPrice()
                                .subtract(discountMoney)
                                .subtract(ticketPreferential)
                                .subtract(discountPreferential);
                        itemDTO.setMinPrice(BigDecimalUtil.setScale2(itemDTO.getPrice()
                                .subtract(discountMoney)
                        ));
                        itemDTO.setMinPriceType(MinPriceTypeEnum.MEMBER_DISCOUNT.getCode());
                        itemDTO.setDiscountTotalPrice(discountTotalPrice);
                        itemDTO.setTotalDiscountFee(itemDTO.getTotalDiscountFee().add(discountMoney));
                        return discountMoney;
                    }

                    // 限时特价更优-共享
                    BigDecimal discountTotalPrice = itemDTO.getPrice()
                            .subtract(specialsDiscountPrice)
                            .subtract(ticketPreferential)
                            .subtract(discountPreferential);
                    itemDTO.setDiscountTotalPrice(discountTotalPrice);
                    itemDTO.setMinPrice(BigDecimalUtil.setScale2(itemDTO.getPrice()
                            .subtract(specialsDiscountPrice)
                            .subtract(specialsMemberPrice)
                    ));
                    itemDTO.setMinPriceType(MinPriceTypeEnum.LIMITED_SPECIAL_ACTIVITY.getCode());
                    itemDTO.setTotalDiscountFee(specialsDiscountPrice);
                    RequestDishInfo dishSpecShareInfo = dishSpecShareInfoMap.get(itemDTO.getSkuGuid());
                    if (Objects.isNull(dishSpecShareInfo)) {
                        return specialsMemberPrice;
                    }
                    return specialsMemberPrice.min(dishSpecShareInfo.getDiscountMoney());
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @NotNull
    private BigDecimal getGrouponDiscountFee(DiscountContext context) {
        // 会员代金券
        DiscountFeeDetailDTO memberGroupon = context.getDiscountFeeDetailDTOS().stream()
                .filter(d -> Objects.equals(d.getDiscountType(), DiscountTypeEnum.MEMBER_GROUPON.getCode()))
                .findFirst()
                .orElse(null);
        // 会员商品券
        DiscountFeeDetailDTO goodsGroupon = context.getDiscountFeeDetailDTOS().stream()
                .filter(d -> Objects.equals(d.getDiscountType(), DiscountTypeEnum.GOODS_GROUPON.getCode()))
                .findFirst()
                .orElse(null);
        BigDecimal grouponDiscountFee = BigDecimal.ZERO;
        if (!ObjectUtils.isEmpty(memberGroupon) && BigDecimalUtil.greaterThanZero(memberGroupon.getDiscountFee())) {
            grouponDiscountFee = grouponDiscountFee.add(memberGroupon.getDiscountFee());
        }
        if (!ObjectUtils.isEmpty(goodsGroupon) && !ObjectUtils.isEmpty(goodsGroupon.getDiscountFee())) {
            grouponDiscountFee = grouponDiscountFee.add(goodsGroupon.getDiscountFee());
        }
        return grouponDiscountFee;
    }

    private SpecialsActivityAmountBO getSpecialsActivityAmountBO(LimitSpecialsActivityDetailsVO activityDetailsVO,
                                                                 DineInItemDTO dineInItemDTO,
                                                                 ResponseProductDiscount responseProductDiscount) {
        boolean isShare = Objects.equals(BooleanEnum.TRUE.getCode(), activityDetailsVO.getRelationRule());
        SpecialsActivityAmountBO amountBO = new SpecialsActivityAmountBO();
        amountBO.setActivityAmountDTO(new SpecialsActivityAmountDTO());
        if (isShare) {
            // 共享 计算限时特价+会员价/会员折扣
            Integer memberRightsType = responseProductDiscount.getMemberRightsType();
            MemberRightsType rightsType = MemberRightsType.getEnum(memberRightsType);
            switch (Objects.requireNonNull(rightsType)) {
                case MEMBER_PRICE:
                    if (BigDecimalUtil.greaterThanZero(dineInItemDTO.getMemberPrice())) {
                        amountBO.setMinPrice(dineInItemDTO.getMemberPrice());
                        amountBO.setMinPriceType(MinPriceTypeEnum.MEMBER_PRICE.getCode());
                    } else {
                        amountBO.setMinPrice(dineInItemDTO.getOriginalPrice());
                        amountBO.setMinPriceType(MinPriceTypeEnum.ORIGINAL_PRICE.getCode());
                    }
                    break;
                case MEMBER_DISCOUNT:
                    amountBO.setMinPrice(dineInItemDTO.getOriginalPrice()
                            .subtract(dineInItemDTO.getTotalDiscountFee())
                            .multiply(responseProductDiscount.getDiscountValue())
                            .divide(BigDecimal.TEN, 2, RoundingMode.HALF_UP));
                    amountBO.setMinPriceType(MinPriceTypeEnum.MEMBER_DISCOUNT.getCode());
                    break;
                case NOT:
                    amountBO.setMinPrice(dineInItemDTO.getOriginalPrice());
                    amountBO.setMinPriceType(MinPriceTypeEnum.ORIGINAL_PRICE.getCode());
                    break;
                default:
                    log.warn("未知权益类型");
                    break;
            }
        } else {
            // 互斥 计算限时特价+原价
            amountBO.setMinPrice(dineInItemDTO.getOriginalPrice());
            amountBO.setMinPriceType(MinPriceTypeEnum.ORIGINAL_PRICE.getCode());
        }
        return amountBO;
    }

    /**
     * 获取活动支持商品的总价
     */
    private BigDecimal getTotalPrice(List<com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo> dishInfoList,
                                     ResponseClientMarketActivity marketActivity) {
        BigDecimal totalPrice = BigDecimal.ZERO;
        if (CollectionUtils.isEmpty(dishInfoList)) {
            return totalPrice;
        }
        if (marketActivity.getAllType().equals(DiscountStoreTypeEnum.Discount_ALL_TYPE_ALL.getCode())) {
            // 支持全部商品，直接计算所有商品总价
            totalPrice = dishInfoList.stream()
                    .map(com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo::getPayPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            return totalPrice;
        }
        if (marketActivity.getAllType().equals(DiscountStoreTypeEnum.Discount_ALL_TYPE_PRODUCT.getCode())) {
            // 部分商品
            return getPartProductPrice(dishInfoList, marketActivity);
        }
        return totalPrice;
    }

    /**
     * 获取部分商品的总价
     */
    private BigDecimal getPartProductPrice(List<com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo> dishInfoDTOList,
                                           ResponseClientMarketActivity marketActivity) {
        if (CollectionUtils.isEmpty(dishInfoDTOList)) {
            return BigDecimal.ZERO;
        }
        BigDecimal totalPrice;
        List<String> productGuidList = getProductGuidList(marketActivity);
        ItemStringListDTO listDTO = new ItemStringListDTO();
        listDTO.setDataList(productGuidList);
        List<String> finalProductGuidList = itemClientService.getItemInfoList3(listDTO);
        totalPrice = dishInfoDTOList.stream()
                .map(v -> {
                    if (productGuidList.contains(v.getDishGuid())) {
                        return v.getPayPrice();
                    }
                    return BigDecimal.ZERO;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (BigDecimal.ZERO.compareTo(totalPrice) == 0) {
            totalPrice = dishInfoDTOList.stream().map(v -> {
                if (productGuidList.contains(v.getParentDishGuid())) {
                    return v.getPayPrice();
                }
                return BigDecimal.ZERO;
            }).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        if (BigDecimal.ZERO.compareTo(totalPrice) == 0 && !CollectionUtils.isEmpty(finalProductGuidList)) {
            totalPrice = dishInfoDTOList.stream().map(v -> {
                if (finalProductGuidList.contains(v.getParentDishGuid()) || finalProductGuidList.contains(v.getDishGuid())) {
                    return v.getPayPrice();
                }
                return BigDecimal.ZERO;
            }).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return totalPrice;
    }

    /**
     * 营销活动仅支持部分商品时，获取这部分商品guid集合
     */
    private List<String> getProductGuidList(ResponseClientMarketActivity marketActivity) {
        List<ResponseActivitieProduct> productList = marketActivity.getProductList();
        return productList.stream()
                .map(ResponseActivitieProduct::getProductGuid).collect(Collectors.toList());
    }


    /**
     * 计算优惠价
     */
    public void calculatePreferentialPrice(ResponseClientMarketActivity activity,
                                           ResponseMarketActivityUse respDTO) {
        // 商品总金额
        BigDecimal totalPrice = getTotalPrice(respDTO.getDishInfoDTOList(), activity);

        // 计算最终优惠价
        if (Objects.equals(activity.getActivitieType(), ActivitieTypeEnum.DISCOUNT.getCode())) {
            // 满折计算
            fullDiscountCalculation(activity, totalPrice);
        } else if (Objects.equals(activity.getActivitieType(), ActivitieTypeEnum.SUBTRACTION.getCode())) {
            // 满减计算
            fullMinusCalculation(activity, totalPrice);
        }

        // 如果活动优惠金额为0，说明不满足活动的条件
        if (!BigDecimalUtil.greaterThanZero(activity.getDiscountPrice())) {
            activity.setUseAble(false);
            activity.setUnUseReason(MarketActivityUnableTipEnum.UN_FULL_AMOUNT_CONDITION.getTips());
        }
    }

    /**
     * 满折计算
     */
    private void fullDiscountCalculation(ResponseClientMarketActivity activity,
                                         BigDecimal totalPrice) {
        List<RequestActivityFullDiscountRule> fullDiscountRuleList = activity.getFullDiscountRuleList();
        if (CollectionUtils.isEmpty(fullDiscountRuleList)) {
            log.error("[满折活动规则不存在]activityGuid={}", activity.getGuid());
            return;
        }

        RequestActivityFullDiscountRule discountRate = getActivityFullDiscountRule(totalPrice, fullDiscountRuleList);
        BigDecimal totalDiscount = BigDecimal.ZERO;
        if (Objects.nonNull(discountRate)) {
            // 总优惠金额
            totalDiscount = BigDecimalUtil.multiply2(totalPrice,
                    BigDecimal.ONE.subtract(discountRate.getDiscount().divide(BigDecimal.TEN, 5, RoundingMode.DOWN)));
        }
        // 设置活动总优惠金额
        activity.setDiscountPrice(totalDiscount);
    }

    /**
     * 满减计算
     */
    private void fullMinusCalculation(ResponseClientMarketActivity activity,
                                      BigDecimal totalPrice) {
        BigDecimal fullReduceMoney = BigDecimal.ZERO;
        if (Objects.equals(activity.getPromotionType(), FullReductionTypeEnum.LADDER.getCode())) {
            // 阶梯满减
            List<RequestActivityFullDiscountRule> fullReductionLadderRuleList = activity.getFullReductionLadderRuleList();

            RequestActivityFullDiscountRule reduction = getActivityFullDiscountRule(totalPrice, fullReductionLadderRuleList);

            if (Objects.nonNull(reduction)) {
                fullReduceMoney = reduction.getDiscount();
            }
        } else if (Objects.equals(activity.getPromotionType(), FullReductionTypeEnum.LOOP.getCode())) {
            // 循环满减
            RequestActivityFullDiscountRule fullReductionLoopRule = activity.getFullReductionLoopRule();
            BigDecimal fullMoney = fullReductionLoopRule.getFullMoney();
            BigDecimal reduceMoney = fullReductionLoopRule.getDiscount();
            BigDecimal times = totalPrice.divide(fullMoney, 0, RoundingMode.DOWN);
            fullReduceMoney = reduceMoney.multiply(times);
        }
        // 设置活动总优惠金额
        activity.setDiscountPrice(fullReduceMoney);
    }

    @Nullable
    private RequestActivityFullDiscountRule getActivityFullDiscountRule(BigDecimal totalPrice,
                                                                        List<RequestActivityFullDiscountRule> fullReductionLadderRuleList) {
        // 折扣排序，由大到小
        fullReductionLadderRuleList = fullReductionLadderRuleList.stream()
                .sorted(Comparator.comparing(RequestActivityFullDiscountRule::getFullMoney).reversed())
                .collect(Collectors.toList());

        // 筛选出应该使用的折扣
        return fullReductionLadderRuleList.stream()
                .filter(v -> totalPrice.compareTo(v.getFullMoney()) >= 0)
                .findFirst()
                .orElse(null);
    }

    /**
     * 计算商品最终优惠价
     */
    public void calculateItemDiscountPrice(ResponseClientMarketActivity activity, ResponseMarketActivityUse respDTO) {
        // 商品总金额
        BigDecimal totalPrice = getTotalPrice(respDTO.getDishInfoDTOList(), activity);

        if (activity.getActivitieType().equals(ActivitieTypeEnum.DISCOUNT.getCode())) {
            // 满折计算
            calculateDiscount(activity, respDTO, totalPrice);
        } else if (activity.getActivitieType().equals(ActivitieTypeEnum.SUBTRACTION.getCode())) {
            // 满减计算
            calculateReductionRuleAmount(activity, respDTO, totalPrice);
        }
    }

    /**
     * 计算满减活动优惠
     */
    private void calculateReductionRuleAmount(ResponseClientMarketActivity activity,
                                              ResponseMarketActivityUse respDTO,
                                              BigDecimal totalPrice) {
        if (activity.getPromotionType().equals(FullReductionTypeEnum.LADDER.getCode())) {
            List<RequestActivityFullDiscountRule> fullReductionLadderRuleList = activity.getFullReductionLadderRuleList();
            RequestActivityFullDiscountRule reduction = getActivityFullDiscountRule(totalPrice, fullReductionLadderRuleList);
            if (Objects.nonNull(reduction)) {
                // 设置每个商品的优惠金额
                calculateItemReduceAmount(activity, respDTO.getDishInfoDTOList(), reduction.getDiscount(), totalPrice);
                // 设置营销活动总优惠金额
                respDTO.setDiscountMoney(reduction.getDiscount());
            } else {
                // 没有可用的规则，则取消选择活动
                respDTO.setActivityGuid(null);
            }
        } else if (activity.getPromotionType().equals(FullReductionTypeEnum.LOOP.getCode())) {
            // 循环满减
            RequestActivityFullDiscountRule fullReductionLoopRule = activity.getFullReductionLoopRule();
            BigDecimal fullMoney = fullReductionLoopRule.getFullMoney();
            BigDecimal reduceMoney = fullReductionLoopRule.getDiscount();
            int times = totalPrice.divide(fullMoney, 0, RoundingMode.DOWN).intValue();
            BigDecimal fullReduceMoney = reduceMoney.multiply(new BigDecimal(times));
            if (fullReduceMoney.compareTo(BigDecimal.ZERO) > 0) {
                // 设置每个商品的优惠金额
                calculateItemReduceAmount(activity, respDTO.getDishInfoDTOList(), fullReduceMoney, totalPrice);
                // 设置营销活动总优惠金额
                respDTO.setDiscountMoney(fullReduceMoney);
            } else {
                // 满减金额为0，则活动商品总价不满足满减规则，取消选择活动
                respDTO.setActivityGuid(null);
            }
        }
    }

    private void calculateDiscount(ResponseClientMarketActivity activity,
                                   ResponseMarketActivityUse respDTO,
                                   BigDecimal totalPrice) {
        List<RequestActivityFullDiscountRule> fullDiscountRuleList = activity.getFullDiscountRuleList();
        if (CollectionUtils.isEmpty(fullDiscountRuleList)) {
            log.error("[满折活动规则不存在]activityGuid={}", activity.getGuid());
            return;
        }

        RequestActivityFullDiscountRule discountRule = getActivityFullDiscountRule(totalPrice, fullDiscountRuleList);
        if (!ObjectUtils.isEmpty(discountRule)) {
            // 总优惠金额
            BigDecimal totalDiscount = BigDecimalUtil.multiply2(totalPrice,
                    BigDecimal.ONE.subtract(discountRule.getDiscount().divide(BigDecimal.TEN, 5, RoundingMode.DOWN)));
            // 设置总优惠金额
            respDTO.setDiscountMoney(totalDiscount);
            // 设置每个商品的优惠金额
            calculateDiscountRuleAmount(activity, discountRule, respDTO, totalPrice);
        } else {
            // 没有可用的规则，则取消选择活动
            respDTO.setActivityGuid(null);
        }
    }

    /**
     * 满减时计算每件商品的优惠值
     */
    private void calculateItemReduceAmount(ResponseClientMarketActivity activity,
                                           List<com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo> dishInfoDTOList,
                                           BigDecimal reduceAmount, BigDecimal totalPrice) {
        // 定义最后一个商品的优惠值，每次计算一个商品的优惠值，从总优惠中减去
        BigDecimal lastReduceAmount = reduceAmount;
        if (activity.getAllType().equals(DiscountStoreTypeEnum.Discount_ALL_TYPE_ALL.getCode())) {
            // 支持全部商品，将优惠金额按商品价格占总价比例分摊
            for (int i = 0; i < dishInfoDTOList.size(); i++) {
                com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo dishInfo = dishInfoDTOList.get(i);
                if (i == dishInfoDTOList.size() - 1) {
                    // 设置最后一个的满减值
                    dishInfo.setDiscountMoney(Optional.ofNullable(dishInfo.getDiscountMoney())
                            .orElse(BigDecimal.ZERO).add(lastReduceAmount));
                    dishInfo.setPayPrice(dishInfo.getPayPrice().subtract(lastReduceAmount));
                } else {
                    // 单个商品优惠金额
                    BigDecimal discountMoney = BigDecimalUtil.multiply2(reduceAmount, dishInfo.getPayPrice().
                            divide(totalPrice, 2, RoundingMode.HALF_DOWN));
                    dishInfo.setDiscountMoney(Optional.ofNullable(dishInfo.getDiscountMoney())
                            .orElse(BigDecimal.ZERO).add(discountMoney));
                    dishInfo.setPayPrice(dishInfo.getPayPrice().subtract(discountMoney));
                    lastReduceAmount = lastReduceAmount.subtract(discountMoney);
                }
            }
        } else if (activity.getAllType().equals(DiscountStoreTypeEnum.Discount_ALL_TYPE_PRODUCT.getCode())) {
            // 支持部分商品
            List<String> productGuidList = getProductGuidList(activity);
            ItemStringListDTO listDTO = new ItemStringListDTO();
            listDTO.setDataList(productGuidList);
            List<String> productGuidList2 = itemClientService.getItemInfoList3(listDTO);
            BigDecimal reduceAmountAdd = BigDecimal.ZERO;
            for (com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo dishInfo : dishInfoDTOList) {
                if (productGuidList.contains(dishInfo.getDishGuid()) || productGuidList.contains(dishInfo.getParentDishGuid())
                        || productGuidList2.contains(dishInfo.getDishGuid()) || productGuidList2.contains(dishInfo.getParentDishGuid())) {
                    BigDecimal bigDecimal = Optional.ofNullable(dishInfo.getDiscountMoney()).orElse(BigDecimal.ZERO);
                    // 总优惠 * (单个菜支付 / 总支付) = 单个菜优惠
                    BigDecimal divide = dishInfo.getPayPrice().divide(totalPrice, 2, RoundingMode.HALF_DOWN);
                    BigDecimal discountMoney = BigDecimalUtil.multiply2(reduceAmount, divide);
                    reduceAmountAdd = reduceAmountAdd.add(discountMoney);
                    dishInfo.setDiscountMoney(bigDecimal.add(discountMoney));
                    dishInfo.setPayPrice(dishInfo.getPayPrice().subtract(discountMoney));
                }
            }
        }
    }

    /**
     * 计算每件商品的满折金额
     */
    private void calculateDiscountRuleAmount(ResponseClientMarketActivity activity,
                                             RequestActivityFullDiscountRule discountRate,
                                             ResponseMarketActivityUse respDTO,
                                             BigDecimal totalPrice) {
        if (activity.getAllType().equals(DiscountStoreTypeEnum.Discount_ALL_TYPE_ALL.getCode())) {
            // 活动适用全部商品
            calculateDiscountRuleAmountByAll(discountRate, respDTO, totalPrice);
        } else if (activity.getAllType().equals(DiscountStoreTypeEnum.Discount_ALL_TYPE_PRODUCT.getCode())) {
            calculateDiscountRuleAmountByPart(activity, discountRate, respDTO);
        }
    }

    /**
     * 计算每件商品的满折金额
     * 活动适用部分商品
     */
    private void calculateDiscountRuleAmountByPart(ResponseClientMarketActivity activity,
                                                   RequestActivityFullDiscountRule discountRate,
                                                   ResponseMarketActivityUse respDTO) {
        // 支持部分商品，仅这部分商品计算折扣
        List<String> productGuidList = getProductGuidList(activity);
        ItemStringListDTO listDTO = new ItemStringListDTO();
        listDTO.setDataList(productGuidList);
        List<String> finalProductGuidList = itemClientService.getItemInfoList3(listDTO);

        //每次叠加优惠价（将每次优惠价加起来看与总优惠是否有差异）
        BigDecimal discountMoneyAdd = BigDecimal.ZERO;
        for (com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo dishInfoDTO : respDTO.getDishInfoDTOList()) {
            if (productGuidList.contains(dishInfoDTO.getDishGuid()) || productGuidList.contains(dishInfoDTO.getParentDishGuid())) {
                //计算折扣后的支付金额
                BigDecimal payPrice = BigDecimalUtil.multiply2(
                        dishInfoDTO.getPayPrice(), discountRate.getDiscount().divide(BigDecimal.TEN, 5, RoundingMode.DOWN));
                //折扣优惠金额
                BigDecimal discountMoney = dishInfoDTO.getPayPrice().subtract(payPrice);
                discountMoneyAdd = discountMoneyAdd.add(discountMoney);
                //设置优惠金额
                dishInfoDTO.setDiscountMoney(Optional.ofNullable(dishInfoDTO.getDiscountMoney()).orElse(BigDecimal.ZERO)
                        .add(discountMoney));
                //设置支付金额
                dishInfoDTO.setPayPrice(payPrice);
            } else {
                if (finalProductGuidList.contains(dishInfoDTO.getDishGuid()) || finalProductGuidList.contains(dishInfoDTO.getParentDishGuid())) {
                    //计算折扣后的支付金额
                    BigDecimal payPrice = BigDecimalUtil.multiply2(
                            dishInfoDTO.getPayPrice(), discountRate.getDiscount().divide(BigDecimal.TEN, 5, RoundingMode.DOWN));
                    //折扣优惠金额
                    BigDecimal discountMoney = dishInfoDTO.getPayPrice().subtract(payPrice);
                    discountMoneyAdd = discountMoneyAdd.add(discountMoney);
                    //设置支付金额
                    dishInfoDTO.setPayPrice(payPrice);
                    //设置优惠金额
                    dishInfoDTO.setDiscountMoney(Optional.ofNullable(dishInfoDTO.getDiscountMoney()).orElse(BigDecimal.ZERO)
                            .add(discountMoney));
                }
            }
        }
    }

    /**
     * 计算每件商品的满折金额
     * 活动适用全部商品
     */
    private void calculateDiscountRuleAmountByAll(RequestActivityFullDiscountRule discountRate,
                                                  ResponseMarketActivityUse respDTO,
                                                  BigDecimal totalPrice) {
        // 设置最后一个商品支付金额，每次遍历减去每个商品的支付金额
        BigDecimal lastPayPrice = totalPrice;
        // 设置最后一个商品优惠金额，每次遍历减去每个商品的优惠金额
        BigDecimal lastDiscountMoney = respDTO.getDiscountMoney();
        List<com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo> dishInfoDTOList = respDTO.getDishInfoDTOList();
        // 支持全部商品，所有商品乘以折扣，最后一个用总折扣减去其余折扣
        for (int i = 0; i < dishInfoDTOList.size(); i++) {
            com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo dishInfoDTO = dishInfoDTOList.get(i);
            if (i == dishInfoDTOList.size() - 1) {
                // 最后一个商品直接取减去其它商品总价的值
                dishInfoDTO.setDiscountMoney(Optional.ofNullable(dishInfoDTO.getDiscountMoney()).orElse(BigDecimal.ZERO)
                        .add(lastDiscountMoney));
                dishInfoDTO.setPayPrice(lastPayPrice);
            } else {
                // 计算折扣后的支付金额
                BigDecimal payPrice = BigDecimalUtil.multiply2(
                        dishInfoDTO.getPayPrice(), discountRate.getDiscount().divide(BigDecimal.TEN, 5, RoundingMode.DOWN));
                // 折扣优惠金额
                BigDecimal discountMoney = dishInfoDTO.getPayPrice().subtract(payPrice);
                // 设置优惠金额
                dishInfoDTO.setDiscountMoney(Optional.ofNullable(dishInfoDTO.getDiscountMoney()).orElse(BigDecimal.ZERO)
                        .add(discountMoney));
                // 设置支付金额
                dishInfoDTO.setPayPrice(payPrice);
                // 设置最后支付金额
                lastPayPrice = lastPayPrice.subtract(payPrice);
                // 设置最后优惠金额
                lastDiscountMoney = lastDiscountMoney.subtract(discountMoney);
            }
        }
    }

    /**
     * 第N份优惠活动计算
     *
     * @param context           计算综合容器
     * @param activityDetailsVO 选中的活动
     * @return 第N份优惠的金额
     */
    public BigDecimal calculateNthActivity(DiscountContext context,
                                           NthActivityDetailsVO activityDetailsVO) {
        BigDecimal orderSurplusFee = context.getCalculateOrderRespDTO().getOrderSurplusFee();
        if (!BigDecimalUtil.greaterThanZero(orderSurplusFee)) {
            return BigDecimal.ZERO;
        }
        List<DineInItemDTO> allItems = context.getAllItems();

        // 根据选中活动满足的商品进行过滤
        List<NthActivityItemDTO> nthActivityItemDTOList = activityDetailsVO.getItemDTOList();
        List<String> commodityIdList = nthActivityItemDTOList.stream()
                .map(NthActivityItemDTO::getCommodityId)
                .distinct()
                .collect(Collectors.toList());
        List<DineInItemDTO> itemDTOList = allItems.stream()
                .filter(itemDTO -> commodityIdList.contains(itemDTO.getItemGuid())
                        || commodityIdList.contains(itemDTO.getParentGuid()))
                .collect(Collectors.toList());

        LimitSpecialsActivityDetailsVO specialsActivitySelectDetailsVO = context.getDiscountRuleBO().getSpecialsActivityDetailsVO();

        // 商品拆分为单个
        List<ItemPiecemealBO> itemPiecemealBOList = new ArrayList<>();
        itemDTOList.forEach(itemDTO -> {
            BigDecimal actualCount = itemDTO.getCurrentCount().subtract(BigDecimalUtil.nonNullValue(itemDTO.getIsGoodsReduceDiscount()));
            actualCount = handleSpecialsActivityCount(itemDTO, specialsActivitySelectDetailsVO, actualCount, itemPiecemealBOList);
            BigDecimal price = itemDTO.getDiscountTotalPrice().subtract(BigDecimalUtil.nonNullValue(itemDTO.getSpecialsDiscountPrice()));
            calculateAddPiecemealBO(itemDTO, actualCount, itemPiecemealBOList, price);
        });
        itemPiecemealBOList.sort(Comparator.comparing(ItemPiecemealBO::getPrice));
        log.info("[拆分商品]itemPiecemealBOList={}", JacksonUtils.writeValueAsString(itemPiecemealBOList));

        // 计算商品数
        BigDecimal itemCount = getItemCount(itemDTOList);

        // 计算触发次数
        int joinCount = itemCount.intValue() / activityDetailsVO.getPerCount();

        // 折扣值
        BigDecimal discountValue = activityDetailsVO.getPerDiscount().divide(BigDecimal.TEN, 2, RoundingMode.HALF_UP);
        List<ItemPiecemealBO> joinItemBOList = itemPiecemealBOList.subList(0, joinCount);

        BigDecimal totalPrice = joinItemBOList.stream()
                .map(ItemPiecemealBO::getPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return totalPrice.multiply(BigDecimal.ONE.subtract(discountValue));
    }

    private void calculateAddPiecemealBO(DineInItemDTO itemDTO,
                                         BigDecimal actualCount,
                                         List<ItemPiecemealBO> itemPiecemealBOList,
                                         BigDecimal price) {
        if (ItemTypeEnum.WEIGH.getCode() == itemDTO.getItemType()) {
            ItemPiecemealBO piecemealBO = new ItemPiecemealBO();
            piecemealBO.setItemGuid(itemDTO.getItemGuid());
            piecemealBO.setCurrentCount(actualCount);
            piecemealBO.setPrice(price);
            itemPiecemealBOList.add(piecemealBO);
            return;
        }
        for (int i = 0; i < actualCount.intValue(); i++) {
            ItemPiecemealBO piecemealBO = new ItemPiecemealBO();
            piecemealBO.setItemGuid(itemDTO.getItemGuid());
            piecemealBO.setCurrentCount(BigDecimal.ONE);
            BigDecimal singlePrice = BigDecimalUtil.divide(price, actualCount);
            piecemealBO.setPrice(singlePrice);
            itemPiecemealBOList.add(piecemealBO);
        }
    }

    private BigDecimal handleSpecialsActivityCount(DineInItemDTO itemDTO,
                                                   LimitSpecialsActivityDetailsVO specialsActivitySelectDetailsVO,
                                                   BigDecimal actualCount,
                                                   List<ItemPiecemealBO> itemPiecemealBOList) {
        if (ObjectUtils.isEmpty(specialsActivitySelectDetailsVO)) {
            return actualCount;
        }
        List<LimitSpecialsActivityItemDTO> detailsVOItemDTOList = specialsActivitySelectDetailsVO.getItemDTOList();
        if (CollectionUtils.isEmpty(detailsVOItemDTOList)) {
            return actualCount;
        }
        Map<String, LimitSpecialsActivityItemDTO> activityItemDTOMap = detailsVOItemDTOList.stream()
                .collect(Collectors.toMap(LimitSpecialsActivityItemDTO::getCommodityId, Function.identity(), (key1, key2) -> key2));
        if (activityItemDTOMap.containsKey(itemDTO.getItemGuid())
                || activityItemDTOMap.containsKey(itemDTO.getParentGuid())) {
            String itemMapKey = StringUtils.hasText(itemDTO.getParentGuid()) ? itemDTO.getParentGuid() : itemDTO.getItemGuid();
            LimitSpecialsActivityItemDTO activityItemDTO = activityItemDTOMap.get(itemMapKey);
            if (ObjectUtils.isEmpty(activityItemDTO) || activityItemDTO.getLimitNumber() >= actualCount.intValue()) {
                calculateAddPiecemealBO(itemDTO, actualCount, itemPiecemealBOList, itemDTO.getSpecialsPrice());
                return actualCount.subtract(BigDecimal.valueOf(activityItemDTO.getLimitNumber()));
            }
            for (int i = 0; i < activityItemDTO.getLimitNumber(); i++) {
                ItemPiecemealBO piecemealBO = new ItemPiecemealBO();
                piecemealBO.setItemGuid(itemDTO.getItemGuid());
                piecemealBO.setCurrentCount(BigDecimal.ONE);
                BigDecimal singlePrice = BigDecimalUtil.divide(itemDTO.getSpecialsPrice(),
                        BigDecimal.valueOf(activityItemDTO.getLimitNumber()));
                piecemealBO.setPrice(singlePrice);
                itemPiecemealBOList.add(piecemealBO);
            }
            actualCount = actualCount.subtract(BigDecimal.valueOf(activityItemDTO.getLimitNumber()));
        }
        return actualCount;
    }

    /**
     * 计算商品数
     */
    public BigDecimal getItemCount(List<DineInItemDTO> itemDTOList) {
        return itemDTOList.stream()
                .map(itemDTO -> {
                    if (ItemTypeEnum.WEIGH.getCode() == itemDTO.getItemType()) {
                        return BigDecimal.ONE;
                    }
                    return itemDTO.getCurrentCount().subtract(BigDecimalUtil.nonNullValue(itemDTO.getIsGoodsReduceDiscount()));
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * @param itemInfoDTO 商品
     * @return 规格必选
     */
    public static ItemInfoSkuDTO getUckSku(ItemInfoDTO itemInfoDTO) {
        List<ItemInfoSkuDTO> skuList = itemInfoDTO.getSkuList();
        Assert.isTrue(!ObjectUtils.isEmpty(skuList), "规格不能为空");
        if (skuList.size() == 1) {
            return skuList.get(0);
        }
        Optional<ItemInfoSkuDTO> first = skuList.stream().filter(x -> x.getUck() == 1).findFirst();
        if (first.isPresent()) {
            return first.get();
        }
        log.error("商品id:{},规格:{}", itemInfoDTO.getItemGuid(), skuList);
        throw new BusinessException("商品:" + itemInfoDTO.getName() + "必须选择规格");
    }

    public List<String> queryParentGuid(List<String> itemGuidList) {
        ItemStringListDTO listDTO = new ItemStringListDTO();
        listDTO.setDataList(itemGuidList);
        return itemClientService.getItemInfoList3(listDTO);
    }
}
