package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberStoreService
 * @date 2018/10/18 11:51
 * @description //TODO
 * @program holder-saas-store-member
 */
@Component
@FeignClient(name = "holder-saas-store-organization", fallbackFactory = StoreOrganizationClientService.FallBack.class)
public interface StoreOrganizationClientService {

    @PostMapping("/store/query_store_by_guid")
    StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid);


    /**
     * 根据门店guid查询该门店下的master一体机
     */
    @GetMapping("/device/get_master_device/{enterpriseGuid}/{storeGuid}")
    StoreDeviceDTO getMasterDeviceByStoreGuid(@PathVariable("enterpriseGuid") String enterpriseGuid,
                                              @PathVariable("storeGuid") String storeGuid);

    @Component
    class FallBack implements FallbackFactory<StoreOrganizationClientService> {
        private static final Logger logger = LoggerFactory.getLogger(FallBack.class);

        @Override
        public StoreOrganizationClientService create(Throwable throwable) {
            return new StoreOrganizationClientService() {
                @Override
                public StoreDTO queryStoreByGuid(String storeGuid) {
                    logger.error("获取门店详情失败,msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public StoreDeviceDTO getMasterDeviceByStoreGuid(String enterpriseGuid, String storeGuid) {
                    logger.error("根据门店guid查询该门店下的master一体机失败,msg={}", throwable.getMessage());
                    throw new ServerException();
                }
            };
        }
    }
}