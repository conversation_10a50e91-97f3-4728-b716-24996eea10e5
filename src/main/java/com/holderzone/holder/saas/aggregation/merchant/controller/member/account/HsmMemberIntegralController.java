package com.holderzone.holder.saas.aggregation.merchant.controller.member.account;

import java.util.List;

import javax.annotation.Resource;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.HsmMemberIntegralClientService;
import com.holderzone.holder.saas.member.dto.account.request.MemberIntegralChangeReqDTO;
import com.holderzone.holder.saas.member.dto.account.request.MemberIntegralListQueryReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberIntegralConversionRespDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberIntegralRecordListRespDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberSourceTypeRespDTO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberIntegralController
 * @date 2019/05/30 15:08
 * @description 会员积分
 * @program holder-saas-member-account
 */
@RestController
@RequestMapping(value = "/hsm_member_integral")
@Api(description = "会员积分相关接口")
public class HsmMemberIntegralController {

    @Resource
    private HsmMemberIntegralClientService hsmMemberIntegralClientService;
    

    /**
     * 改变积分
     *
     * @param changeReqDTO 改变请求
     * @return 操作结果
     */
    @PostMapping(value = "/changeIntegral", produces = "application/json;charset=utf-8")
    @ApiOperation(value = "改变积分", notes = "改变积分")
    @ApiImplicitParams({
		@ApiImplicitParam(name = "memberIntegralChangeReqDTO", value = "会员积分改变请求参数dto", required = true, dataType = "memberIntegralChangeReqDTO") })
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "改变积分")
    public Result<Boolean> changeIntegral(@RequestBody MemberIntegralChangeReqDTO changeReqDTO) {
        return Result
                .buildSuccessResult(hsmMemberIntegralClientService.changeIntegral(changeReqDTO));
    }


    /**
     * 来源类型
     *
     * @return 集合
     */
    @PostMapping(value = "/listSourceType", produces = "application/json;charset=utf-8")
    @ApiOperation("获取积分来源类型")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "获取积分来源类型")
    public Result<List<MemberSourceTypeRespDTO>> listSourceType() {
        return Result.buildSuccessResult(hsmMemberIntegralClientService.listSourceType());
    }

    /**
     * 根据条件分页查询积分记录
     *
     * @param queryReqDTO 查询条件
     * @return 查询结果
     */
    @PostMapping(value = "/listByCondition", produces = "application/json;charset=utf-8")
    @ApiOperation(value = "根据条件分页查询积分记录", notes = "根据条件分页查询积分记录")
    @ApiImplicitParams({
		@ApiImplicitParam(name = "memberIntegralListQueryReqDTO", value = "memberIntegralListQueryReqDTO", required = true, dataType = "memberIntegralListQueryReqDTO") })
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "根据条件分页查询积分记录")
    public Result<Page<MemberIntegralRecordListRespDTO>> listByCondition(
            @RequestBody MemberIntegralListQueryReqDTO queryReqDTO) {
        return Result
                .buildSuccessResult(hsmMemberIntegralClientService.listByCondition(queryReqDTO));
    }

    /**
     * 统计可兑换的积分
     *
     * @param memberCardGuid 会员持卡guid
     * @return 可兑换的积分
     */
    @GetMapping(value = "/statisticsConvertibleIntegral", produces = "application/json;charset=utf-8")
    @ApiOperation("统计可兑换的积分")
    @ApiImplicitParam(name = "memberCardGuid", value = "会员持卡guid", required = true, dataType = "string")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "统计可兑换的积分")
    public Result<MemberIntegralConversionRespDTO> statisticsConvertibleIntegral(
            @RequestParam(value = "memberCardGuid") String memberCardGuid) {
        return Result.buildSuccessResult(
                hsmMemberIntegralClientService.statisticsConvertibleIntegral(memberCardGuid));
    }
    

}