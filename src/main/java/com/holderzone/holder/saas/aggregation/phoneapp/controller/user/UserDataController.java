package com.holderzone.holder.saas.aggregation.phoneapp.controller.user;

import com.google.common.collect.Lists;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.phoneapp.entity.constant.Constants;
import com.holderzone.holder.saas.aggregation.phoneapp.service.StoreParserService;
import com.holderzone.holder.saas.aggregation.phoneapp.service.rpc.UserFeignService;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.organization.OrgGeneralDTO;
import com.holderzone.saas.store.dto.organization.StoreParserDTO;
import com.holderzone.saas.store.dto.user.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UserDataController
 * @date 2019/05/31 14:26
 * @description 用户权限Controller
 * @program holder-saas-store
 */
@Slf4j
@RestController
@RequestMapping("/user_data")
@Api(description = "员工数据权限管理接口")
public class UserDataController {

    @Autowired
    UserFeignService userFeignService;

    @Autowired
    StoreParserService storeParserService;

    @ApiOperation(value = "查询门店Spinner")
    @PostMapping(value = "/query_store_spinner")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF,description = "查询门店Spinner")
    public Result<UserSpinnerDTO> queryStoreSpinner() {
        UserSpinnerDTO userSpinner = userFeignService.queryStoreSpinner();
        log.info("phoneapp聚合层请求storeSpinner, 返回数据:{}", JacksonUtils.writeValueAsString(userSpinner));
        return Result.buildSuccessResult(userSpinner);
    }

    @ApiOperation(value = "查询组织Spinner")
    @PostMapping(value = "/query_org_spinner")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF,description = "查询组织Spinner")
    public Result<UserSpinnerDTO> queryOrgSpinner() {
        UserSpinnerDTO userSpinnerDTO = userFeignService.queryOrgSpinner();
        List<OrgGeneralDTO> arrayOfOrgDTO = userSpinnerDTO.getArrayOfOrgDTO();
        List<OrgGeneralDTO> collect = arrayOfOrgDTO.stream()
                .flatMap(orgGeneralDTO -> {
                    if (UserContextUtils.getEnterpriseGuid().equals(orgGeneralDTO.getGuid())) {
                        if (CollectionUtils.isEmpty(orgGeneralDTO.getChildren()))
                            return Stream.empty();
                        return orgGeneralDTO.getChildren().stream();
                    }
                    return Stream.of(orgGeneralDTO);
                })
                .collect(Collectors.toList());
        userSpinnerDTO.setArrayOfOrgDTO(collect);
        log.info("查询组织Spinner, 返回数据:{}", userSpinnerDTO);
        return Result.buildSuccessResult(userSpinnerDTO);
    }

    @ApiOperation(value = "查询品牌Spinner")
    @PostMapping(value = "/query_brand_spinner")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF,description = "查询品牌Spinner")
    public Result<UserSpinnerDTO> queryBrandSpinner() {
        UserSpinnerDTO userSpinnerDTO = userFeignService.queryBrandSpinner();
        log.info("查询品牌Spinner，返回数据：{}", JacksonUtils.writeValueAsString(userSpinnerDTO));
        return Result.buildSuccessResult(userSpinnerDTO);
    }

    @ApiOperation(value = "通过品牌guid和组织guid查询门店")
    @PostMapping(value = "/query_store_by_brand_and_org")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF,description = "通过品牌guid和组织guid查询门店")
    public Result<List<String>> queryStoreSpinnerByBrandAndOrg(@RequestBody SpinnerReqDTO spinnerReqDTO) {
        StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setBrandGuidList(StringUtils.hasText(spinnerReqDTO.getBrandGuid()) ? Lists.newArrayList(spinnerReqDTO.getBrandGuid()) : null);
        storeParserDTO.setOrganizationGuidList(StringUtils.hasText(spinnerReqDTO.getOrgGuid()) ? Lists.newArrayList(spinnerReqDTO.getOrgGuid()) : null);
        storeParserService.parseByConditionNotUnion(storeParserDTO);
        log.info("通过品牌和组织查询门店，请求入参：{}\n 返回参数：{}",
                JacksonUtils.writeValueAsString(spinnerReqDTO),
                JacksonUtils.writeValueAsString(storeParserDTO.getStoreGuidList()));
        return Result.buildSuccessResult(storeParserDTO.getStoreGuidList());
    }

    @ApiOperation(value = "查询门店Spinner")
    @PostMapping(value = "/query_store_spinner_and_user")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF,description = "查询门店Spinner")
    public Result<UserInfoSpinnerDTO> queryStoreSpinnerAndUser() {
        UserContext userContext = UserContextUtils.get();
        UserSpinnerDTO userSpinnerDTO = userFeignService.queryStoreSpinner();
        UserDTO userDTO;
        if (Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {// 默认管理员没有存角色信息，直接封装UserDTO
            userDTO = new UserDTO().setAccount(userContext.getAccount())
                    .setName(userContext.getUserName()).setPhone(userContext.getTel());
            RoleDTO roleDTO = new RoleDTO().setName("默认管理员");
            userDTO.setUserRoles(Arrays.asList(roleDTO));
        } else {
            UserDTO queryDTO = new UserDTO().setGuid(userContext.getUserGuid());
            userDTO = userFeignService.queryUser(queryDTO);
        }
        UserInfoSpinnerDTO userInfoSpinnerDTO = UserInfoSpinnerDTO.builder()
                .userSpinnerDTO(userSpinnerDTO)
                .userInfoDTO(JacksonUtils.toObject(UserInfoDTO.class, JacksonUtils.writeValueAsString(userContext)))
                .roleDTOList(userDTO.getUserRoles())
                .build();
        log.info("phoneApp聚合层请求storeSpinnerAndUser，UserInfoDTO:{}\n 返回数据：{}",
                JacksonUtils.writeValueAsString(userContext),
                JacksonUtils.writeValueAsString(userInfoSpinnerDTO));
        return Result.buildSuccessResult(userInfoSpinnerDTO);
    }
}
