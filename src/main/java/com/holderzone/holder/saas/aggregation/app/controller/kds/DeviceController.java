package com.holderzone.holder.saas.aggregation.app.controller.kds;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.kds.DeviceConfigRpcService;
import com.holderzone.saas.store.dto.kds.req.DeviceCreateReqDTO;
import com.holderzone.saas.store.dto.kds.req.DeviceQueryReqDTO;
import com.holderzone.saas.store.dto.kds.resp.DeviceStatusRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Api("KDS设备接口")
@RequestMapping("/device")
public class DeviceController {

    private final DeviceConfigRpcService deviceConfigRpcService;

    @Autowired
    public DeviceController(DeviceConfigRpcService deviceConfigRpcService) {
        this.deviceConfigRpcService = deviceConfigRpcService;
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建KDS设备")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS, description = "创建KDS设备",action = OperatorType.ADD)
    public Result<DeviceStatusRespDTO> create(@RequestBody DeviceCreateReqDTO deviceCreateReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("创建KDS设备入参:{}", JacksonUtils.writeValueAsString(deviceCreateReqDTO));
        }
        return Result.buildSuccessResult(deviceConfigRpcService.create(deviceCreateReqDTO));
    }

    @PostMapping("/query")
    @ApiOperation(value = "查询KDS设备")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS, description = "查询KDS设备",action = OperatorType.SELECT)
    public Result<DeviceStatusRespDTO> query(@RequestBody DeviceQueryReqDTO deviceQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询KDS设备入参:{}", JacksonUtils.writeValueAsString(deviceQueryReqDTO));
        }
        return Result.buildSuccessResult(deviceConfigRpcService.query(deviceQueryReqDTO));
    }

    @PostMapping("/initialize")
    @ApiOperation(value = "初始化KDS设备")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS, description = "初始化KDS设备",action = OperatorType.SELECT)
    public Result initialize(@RequestBody DeviceQueryReqDTO deviceQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("初始化KDS设备入参:{}", JacksonUtils.writeValueAsString(deviceQueryReqDTO));
        }
        deviceConfigRpcService.initialize(deviceQueryReqDTO);
        return Result.buildEmptySuccess();
    }
}

