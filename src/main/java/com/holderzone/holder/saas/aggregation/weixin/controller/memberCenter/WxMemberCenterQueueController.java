package com.holderzone.holder.saas.aggregation.weixin.controller.memberCenter;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.memberCenter.WxMemberCenterQueueService;
import com.holderzone.saas.store.dto.queue.QueueWechatDTO;
import com.holderzone.saas.store.dto.queue.WxQueueListDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(value = "/wx_member_center_queue")
@Api(description = "会员中心：排队")
@Slf4j
public class WxMemberCenterQueueController {

	private final WxMemberCenterQueueService wxMemberCenterQueueService;

	@Autowired
	public WxMemberCenterQueueController(WxMemberCenterQueueService wxMemberCenterQueueService) {
		this.wxMemberCenterQueueService = wxMemberCenterQueueService;
	}

	@PostMapping("/queryByUser")
	@ApiOperation("根据用户查询排队信息")
	public Result<List<WxQueueListDTO>> queryByUser(@RequestBody QueueWechatDTO queueWechatDTO) {
		log.info("微信端查询用户排队门店信息列表：入参{}", JacksonUtils.writeValueAsString(queueWechatDTO));
		return Result.buildSuccessResult(wxMemberCenterQueueService.queryByUser(queueWechatDTO));
	}

	@PostMapping("/cancel")
	@ApiOperation("取消排队")
	public Result<String> cancel(@RequestParam("queueGuid") String queueGuid,@RequestParam("enterpriseGuid") String enterpriseGuid) {
		return wxMemberCenterQueueService.cancel(queueGuid,enterpriseGuid) ? Result.buildSuccessMsg(	"取消排队成功") : Result.buildSuccessMsg("取消排队失败");
	}
}
