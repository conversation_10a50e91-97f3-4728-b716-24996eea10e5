package com.holderzone.holder.saas.aggregation.merchant.controller.kds;

import com.google.common.collect.Lists;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.kds.KdsClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.organization.OrganizationService;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.DisplayRepeatItemRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayRuleRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayStoreRespDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @description 显示规则接口
 * @date 2021/1/27 16:46
 */
@Slf4j
@AllArgsConstructor
@RestController
@Api(description = "kds-显示规则接口")
@RequestMapping("/display_rule")
public class DisplayRuleController {

    private final KdsClientService kdsClientService;

    private final OrganizationService organizationService;


    @ApiOperation("保存规则")
    @PostMapping("/save_rule")
    public Result<Boolean> saveRule(@RequestBody DisplayRuleSaveOrUpdateDTO reqDTO) {
        if (!ObjectUtils.isEmpty(reqDTO.getRuleGuid())) {
            log.info("更新规则入参：{}", JacksonUtils.writeValueAsString(reqDTO));
            if (kdsClientService.updateRule(reqDTO)) {
                return Result.buildSuccessMsg("更新规则成功");
            } else {
                return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.RULE_UPDATE_FAILED));
            }
        }
        log.info("保存规则入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        if (kdsClientService.saveRule(reqDTO)) {
            return Result.buildSuccessMsg("保存规则成功");
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.RULE_SAVING_FAILED));
        }
    }

    /**
     * 显示批次列表
     *
     * @param reqDTO 当前页数,每页显示条数，必传规则类型 0显示批次 1菜品汇总,品牌guid
     * @return Page<DisplayRuleRespDTO>
     */
    @ApiOperation(value = "显示批次列表", notes = "可传当前页数,每页显示条数，必传规则类型 0显示批次 1菜品汇总")
    @PostMapping("/rule_list")
    public Result<Page<DisplayRuleRespDTO>> batchList(@RequestBody DisplayRuleQueryDTO reqDTO) {
        log.info("显示批次列表入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        return Result.buildSuccessResult(kdsClientService.batchList(reqDTO));
    }

    /**
     * 根据ruleGuid删除单个规则
     *
     * @param reqDTO DisplayRuleReqDTO
     * @return Boolean
     */
    @ApiOperation("删除规则")
    @PostMapping("/delete_rule")
    public Result<Boolean> deleteRule(@RequestBody DisplayRuleQueryDTO reqDTO) {
        log.info("删除规则入参：ruleGuid={}", JacksonUtils.writeValueAsString(reqDTO));
        if (kdsClientService.deleteRule(reqDTO)) {
            return Result.buildSuccessMsg("删除规则成功");
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.RULE_DELETION_FAILED));
        }
    }

    /**
     * 根据ruleGuid查询单个规则信息
     *
     * @param reqDTO DisplayRuleReqDTO
     * @return DisplayRuleRespDTO
     */
    @ApiOperation("查询规则")
    @PostMapping("/get_rule")
    public Result<DisplayRuleRespDTO> getRule(@RequestBody DisplayRuleQueryDTO reqDTO) {
        log.info("查询规则入参：ruleGuid={}", JacksonUtils.writeValueAsString(reqDTO));
        return Result.buildSuccessResult(kdsClientService.getRule(reqDTO));
    }

    /**
     * 更新规则
     *
     * @param reqDTO DisplayRuleReqDTO
     * @return Boolean
     */
    @ApiOperation("更新规则")
    @PostMapping("/update_rule")
    public Result<Boolean> updateRule(@RequestBody DisplayRuleSaveOrUpdateDTO reqDTO) {
        log.info("更新规则入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        if (kdsClientService.updateRule(reqDTO)) {
            return Result.buildSuccessMsg("更新规则成功");
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.RULE_UPDATE_FAILED));
        }
    }

    /**
     * 查询有无选择全部门店 true:有选择 false：没有
     *
     * @param reqDTO DisplayRuleQueryDTO
     * @return Boolean
     */
    @ApiOperation("查询有无选择全部门店 true:有选择 false：没有")
    @PostMapping("/query_has_all_store")
    public Result<Boolean> queryHasAllStore(@RequestBody DisplayRuleQueryDTO reqDTO) {
        log.info("查询有无选择全部门店入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        boolean result;
        Boolean hasAllStore = kdsClientService.queryHasAllStore(reqDTO);
        List<DisplayStoreRespDTO> storeRespDTOList = kdsClientService.listStore(reqDTO);
        result = !hasAllStore && CollectionUtils.isEmpty(storeRespDTOList);
        return Result.buildSuccessResult(result);
    }

    /**
     * 更新菜品显示顺序配置
     */
    @ApiOperation("更新菜品显示顺序配置")
    @PostMapping("/save_or_update_item_sort_rule")
    public Result<Void> saveOrUpdateItemSortRule(@RequestBody DisplayRuleItemSortDTO reqDTO) {
        log.info("[更新菜品显示顺序配置]入参={}", JacksonUtils.writeValueAsString(reqDTO));
        kdsClientService.saveOrUpdateItemSortRule(reqDTO);
        return Result.buildEmptySuccess();
    }

    /**
     * 显示批次列表
     */
    @ApiOperation("查询菜品显示顺序配置")
    @PostMapping("/query_item_sort_rule")
    public Result<DisplayRuleItemSortDTO> queryItemSortRule(@RequestBody DisplayRuleItemSortQueryDTO reqDTO) {
        log.info("[查询菜品显示顺序配置]入参={}", JacksonUtils.writeValueAsString(reqDTO));
        return Result.buildSuccessResult(kdsClientService.queryItemSortRule(reqDTO));
    }


    /**
     * 查询菜品绑定配置
     */
    @ApiOperation("查询菜品绑定配置")
    @GetMapping("/query_repeat_item")
    public Result<DisplayRepeatItemRespDTO> queryRepeatItem(String brandGuid) {
        DisplayRepeatItemRespDTO displayRepeatItemRespDTO = kdsClientService.queryRepeatItem(brandGuid);
        displayRepeatItemRespDTO.setStoreList(Lists.newArrayList());
        if (Boolean.FALSE.equals(displayRepeatItemRespDTO.getAllowRepeatFlag()) || Boolean.TRUE.equals(displayRepeatItemRespDTO.getAllStoreFlag())) {
            return Result.buildSuccessResult(displayRepeatItemRespDTO);
        }
        // 查询门店信息
        List<StoreDTO> storeDTOList = organizationService.queryStoreByBrandList(Lists.newArrayList(brandGuid));
        if (CollectionUtils.isEmpty(storeDTOList)) {
            return Result.buildEmptySuccess();
        }
        List<String> repeatItemStoreList = displayRepeatItemRespDTO.getStoreGuids();
        log.info("查询品牌{}可重复绑定的门店列表返回:{}", brandGuid, JacksonUtils.writeValueAsString(repeatItemStoreList));
        storeDTOList.removeIf(e -> !repeatItemStoreList.contains(e.getGuid()));
        displayRepeatItemRespDTO.setStoreList(storeDTOList);
        return Result.buildSuccessResult(displayRepeatItemRespDTO);
    }

    /**
     * 保存菜品绑定配置
     */
    @ApiOperation(value = "保存菜品绑定配置")
    @PostMapping("/save_or_update_repeat_item")
    public Result<Void> saveOrUpdateRepeatItem(@RequestBody @Validated DisplayRepeatItemStoreReqDTO reqDTO) {
        log.info("保存可重复绑定的门店列表入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        kdsClientService.saveOrUpdateRepeatItem(reqDTO);
        return Result.buildEmptySuccess();
    }

}
