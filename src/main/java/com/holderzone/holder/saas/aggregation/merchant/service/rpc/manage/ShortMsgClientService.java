package com.holderzone.holder.saas.aggregation.merchant.service.rpc.manage;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.business.manage.ChargeDTO;
import com.holderzone.saas.store.dto.business.manage.ProductOrderDTO;
import com.holderzone.saas.store.dto.business.manage.ShortMsgRespDTO;
import com.holderzone.saas.store.dto.trade.ShortMsgConfigDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ShortMsgClientService
 * @date 2018/09/17 18:19
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(value = "holder-saas-store-business", fallbackFactory = ShortMsgClientService.ShortMsgFallBack.class)
public interface ShortMsgClientService {

    @PostMapping("/shortMsg/index")
    List<ChargeDTO> getAll();

    @PostMapping("/shortMsg/charge")
    ShortMsgRespDTO charge(@RequestBody ProductOrderDTO productOrderDTO);

    @PostMapping("/shortMsg/polling")
    ShortMsgRespDTO polling(@RequestBody ShortMsgRespDTO shortMsgRespDTO);

    @PostMapping("/shortMsg/query")
    ShortMsgConfigDTO query();

    @PostMapping("/shortMsg/update")
    Boolean update(@RequestBody ShortMsgConfigDTO shortMsgConfigDTO);

    @Component
    class ShortMsgFallBack implements FallbackFactory<ShortMsgClientService> {

        private static final Logger logger = LoggerFactory.getLogger(ShortMsgFallBack.class);

        @Override
        public ShortMsgClientService create(Throwable throwable) {
            return new ShortMsgClientService() {
                @Override
                public List<ChargeDTO> getAll() {
                    logger.error("异常fallBack={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("fallBack 异常 e" + throwable.getMessage());
                }

                @Override
                public ShortMsgRespDTO charge(ProductOrderDTO productOrderDTO) {
                    logger.error("异常fallBack={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("fallBack 异常 e" + throwable.getMessage());
                }

                @Override
                public ShortMsgRespDTO polling(ShortMsgRespDTO shortMsgRespDTO) {
                    logger.error("异常fallBack={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("fallBack 异常 e" + throwable.getMessage());
                }

                @Override
                public ShortMsgConfigDTO query() {
                    logger.error("异常fallBack={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("fallBack 异常 e" + throwable.getMessage());
                }

                @Override
                public Boolean update(ShortMsgConfigDTO shortMsgConfigDTO) {
                    logger.error("异常fallBack={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("fallBack 异常 e" + throwable.getMessage());
                }
            };
        }
    }

}
