package com.holderzone.holder.saas.aggregation.merchant.controller.item;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.item.ItemClientService;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.AttrGroupReqDTO;
import com.holderzone.saas.store.dto.item.req.AttrGroupUpdateReqDTO;
import com.holderzone.saas.store.dto.item.req.AttrReqDTO;
import com.holderzone.saas.store.dto.item.req.AttrSaveReqDTO;
import com.holderzone.saas.store.dto.item.resp.AttrGroupAttrRespDTO;
import com.holderzone.saas.store.dto.item.resp.AttrRespDTO;
import com.holderzone.saas.store.enums.item.ModuleEntranceEnum;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PropertyController
 * @date 2018/09/12 上午9:53
 * @description 属性相关接口
 * @program holder-saas-store-dish
 */
@RestController
@RequestMapping("/attr")
@Api(description = "属性接口", tags = "属性接口")
public class AttrController {
    private static final Logger logger = LoggerFactory.getLogger(AttrController.class);

    @Autowired
    private ItemClientService itemClientService;


    /**
     * 属性组保存接口
     */
    @ApiOperation(value = "门店属性组保存接口")
    @PostMapping("/store/save_attr_group")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "门店属性组保存接口",action = OperatorType.ADD)
    public Result saveStoreAttrGroup(@RequestBody @Valid AttrGroupReqDTO attrGroupReqDTO) {
        logger.info("属性保存接口查询入参,attrGroupReqDTO={}", attrGroupReqDTO);
        attrGroupReqDTO.setFrom(ModuleEntranceEnum.STORE.code());
        boolean flag = itemClientService.saveAttrGroup(attrGroupReqDTO);
        return flag ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVE_FAILED));
    }

    @ApiOperation(value = "品牌属性组保存接口}")
    @PostMapping("/brand/save_attr_group")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "品牌属性组保存接口",action = OperatorType.ADD)
    public Result saveBrandAttrGroup(@RequestBody @Valid AttrGroupReqDTO attrGroupReqDTO) {
        logger.info("品牌属性组保存入参:{}", JacksonUtils.writeValueAsString(attrGroupReqDTO));
        attrGroupReqDTO.setFrom(ModuleEntranceEnum.BRAND.code());
        boolean flag = itemClientService.saveAttrGroup(attrGroupReqDTO);
        return flag ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVE_FAILED));
    }

    /**
     * 属性组修改接口
     */

    @ApiOperation(value = "门店属性组修改接口")
    @PostMapping("/store/set_attr_group")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "门店属性组修改接口")
    public Result setAttrGroup(@RequestBody @Valid AttrGroupUpdateReqDTO attrGroupUpdateReqDTO) {
        logger.info("门店属性组修改入参：{}", attrGroupUpdateReqDTO);
        attrGroupUpdateReqDTO.setFrom(ModuleEntranceEnum.STORE.code());
        boolean flag = itemClientService.setAttrGroup(attrGroupUpdateReqDTO);
        return flag ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVE_FAILED));
    }

    @ApiOperation(value = "品牌属性组修改接口")
    @PostMapping("/brand/set_attr_group")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "品牌属性组修改接口")
    public Result setBrandAttrGroup(@RequestBody @Valid AttrGroupUpdateReqDTO attrGroupUpdateReqDTO) {
        logger.info("品牌属性组修改入参：{}", JacksonUtils.writeValueAsString(attrGroupUpdateReqDTO));
        attrGroupUpdateReqDTO.setFrom(ModuleEntranceEnum.BRAND.code());
        boolean flag = itemClientService.setAttrGroup(attrGroupUpdateReqDTO);
        return flag ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVE_FAILED));
    }


    /**
     * 品牌属性组列表
     */

    @ApiOperation(value = "获取门店属性组列表", notes = " 必填参数：data:值：门店GUID  keywords: 0:查询自建属性  2：推送属性")
    @PostMapping("/store/list_attr_group")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "获取门店属性组列表",action = OperatorType.SELECT)
    public Result<List<AttrGroupAttrRespDTO>> listStoreAttrGroup(@RequestBody ItemSingleDTO itemSingleDTO) {
        logger.info("属性列表接口查询入参,itemSingleDTO={}", itemSingleDTO);
        itemSingleDTO.setFrom(ModuleEntranceEnum.STORE.code());
        List<AttrGroupAttrRespDTO> attrList = itemClientService.listAttrGroup(itemSingleDTO);
        return Result.buildSuccessResult(attrList);
    }

    @ApiOperation(value = "获取品牌属性组列表", notes = "必填参数：data：品牌guid")
    @PostMapping("/brand/list_attr_group")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "获取品牌属性组列表",action = OperatorType.SELECT)
    public Result<List<AttrGroupAttrRespDTO>> listBrandAttrGroup(@RequestBody ItemSingleDTO itemSingleDTO) {
        logger.info("品牌属性组列表入参 brandGuid:{}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(ModuleEntranceEnum.BRAND.code());
        List<AttrGroupAttrRespDTO> attrGroupAttrRespDTOS = itemClientService.listAttrGroup(itemSingleDTO);
        return Result.buildSuccessResult(attrGroupAttrRespDTOS);
    }

    /**
     * 删除属性组
     */
    @ApiOperation(value = "删除门店属性组")
    @PostMapping("/store/delete_attr_group")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "删除门店属性组",action = OperatorType.DELETE)
    public Result deleteStoreAttrGroup(@RequestBody ItemSingleDTO itemSingleDTO) {
        logger.info("删除门店属性组入参：{}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(ModuleEntranceEnum.STORE.code());
        boolean flag = itemClientService.deleteAttrGroupByGuid(itemSingleDTO);
        return flag ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.DELETION_FAILED));
    }

    @ApiOperation(value = "删除品牌属性组")
    @PostMapping("/brand/delete_attr_group")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "删除品牌属性组",action = OperatorType.DELETE)
    public Result deleteBrandAttrGroup(@RequestBody ItemSingleDTO itemSingleDTO) {
        logger.info("删除品牌属性组入参：{}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(ModuleEntranceEnum.STORE.code());
        boolean flag = itemClientService.deleteAttrGroupByGuid(itemSingleDTO);
        return flag ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.DELETION_FAILED));
    }

    /**
     * 获取新增套餐时的属性列表
     */

    @ApiOperation(value = "门店获取新增套餐时的属性列表")
    @PostMapping("/store/select_attr_list_for_save_item")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "门店获取新增套餐时的属性列表",action = OperatorType.SELECT)
    public Result<List<AttrGroupAttrRespDTO>> selectStoreAttrGroupAttrForSaveItem(@RequestBody ItemSingleDTO itemSingleDTO) {
        logger.info("门店获取新增套餐时的属性列表,itemSingleDTO={}", itemSingleDTO);
        itemSingleDTO.setFrom(ModuleEntranceEnum.STORE.code());
        List<AttrGroupAttrRespDTO> attrGroupAttrRespDTOList = itemClientService.listAttrForSaveItem(itemSingleDTO);
        return Result.buildSuccessResult(attrGroupAttrRespDTOList);
    }

    @ApiOperation(value = "品牌获取新增套餐时的属性列表")
    @PostMapping("/brand/select_attr_list_for_save_item")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "品牌获取新增套餐时的属性列表",action = OperatorType.SELECT)
    public Result<List<AttrGroupAttrRespDTO>> selectBrandAttrGroupAttrForSaveItem(@RequestBody ItemSingleDTO itemSingleDTO) {
        logger.info("品牌获取新增套餐时的属性列表，入参：{}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(ModuleEntranceEnum.BRAND.code());
        List<AttrGroupAttrRespDTO> attrGroupAttrRespDTOS = itemClientService.listAttrForSaveItem(itemSingleDTO);
        return Result.buildSuccessResult(attrGroupAttrRespDTOS);
    }

    /**
     * 属性详情保存接口
     */
    @ApiOperation(value = "门店属性详情保存接口")
    @PostMapping("/store/save_attr")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "门店属性详情保存接口",action = OperatorType.ADD)
    public Result savePropertyValue(@RequestBody @Valid AttrReqDTO attrReqDTO) {
        logger.info("门店属性详情保存入参：{}", attrReqDTO);
        attrReqDTO.setFrom(ModuleEntranceEnum.STORE.code());
        boolean flag = itemClientService.saveAttrValue(attrReqDTO);
        return flag ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVE_FAILED));
    }

    @ApiOperation(value = "品牌属性详情保存接口")
    @PostMapping("/brand/save_attr")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "品牌属性详情保存接口",action = OperatorType.ADD)
    public Result saveBrandAttr(@RequestBody @Valid AttrReqDTO attrReqDTO) {
        logger.info("品牌属性详情保存入参:{}", JacksonUtils.writeValueAsString(attrReqDTO));
        attrReqDTO.setFrom(ModuleEntranceEnum.BRAND.code());
        boolean flag = itemClientService.saveAttrValue(attrReqDTO);
        return flag ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVE_FAILED));
    }


    /**
     * 属性组下的属性列表
     */

    @ApiOperation(value = "门店属性组下的属性列表", notes = "参数为data, 值为attrGroupGuid")
    @PostMapping("/store/list_attr_by_group")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "门店属性组下的属性列表",action = OperatorType.SELECT)
    public Result<List<AttrRespDTO>> listStoreAttrByGroup(@RequestBody ItemSingleDTO itemSingleDTO) {
        logger.info("门店属性组下的属性列表入参,attrGroupGuid={}", itemSingleDTO);
        itemSingleDTO.setFrom(ModuleEntranceEnum.STORE.code());
        List<AttrRespDTO> attrList = itemClientService.listAttrByGroup(itemSingleDTO);
        return Result.buildSuccessResult(attrList);
    }

    @ApiOperation(value = "品牌属性组下的属性列表", notes = "参数为data, 值为attrGroupGuid")
    @PostMapping("/brand/list_attr_by_group")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "品牌属性组下的属性列表",action = OperatorType.SELECT)
    public Result<List<AttrRespDTO>> listBrandAttrByGroup(@RequestBody ItemSingleDTO itemSingleDTO) {
        logger.info("获取品牌属性组下的属性列表");
        itemSingleDTO.setFrom(ModuleEntranceEnum.BRAND.code());
        List<AttrRespDTO> attrRespDTOS = itemClientService.listAttrByGroup(itemSingleDTO);
        return Result.buildSuccessResult(attrRespDTOS);
    }

    /**
     * 属性值修改
     */

    @ApiOperation(value = "修改门店属性值")
    @PostMapping("/store/update_attr")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "修改门店属性值")
    public Result updateStoreAttr(@RequestBody AttrSaveReqDTO attrSaveReqDTO) {
        logger.info("修改门店属性值入参:{}", JacksonUtils.writeValueAsString(attrSaveReqDTO));
        attrSaveReqDTO.setFrom(ModuleEntranceEnum.STORE.code());
        boolean flag = itemClientService.updateAttrValue(attrSaveReqDTO);
        return flag ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVE_FAILED));
    }

    @ApiOperation(value = "修改品牌属性值")
    @PostMapping("/brand/update_attr")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "修改品牌属性值")
    public Result updateBrandAttr(@RequestBody AttrSaveReqDTO attrSaveReqDTO) {
        logger.info("修改品牌属性值入参:{}", JacksonUtils.writeValueAsString(attrSaveReqDTO));
        attrSaveReqDTO.setFrom(ModuleEntranceEnum.BRAND.code());
        boolean flag = itemClientService.updateAttrValue(attrSaveReqDTO);
        return flag ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVE_FAILED));
    }

    /**
     * 属性值删除
     */

    @ApiOperation(value = "门店属性值删除", notes = "参数为data，值为属性值guid")
    @PostMapping("/store/delete_attr")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "门店属性值删除",action = OperatorType.DELETE)
    public Result deleteStoreAttr(@RequestBody ItemSingleDTO itemSingleDTO) {
        logger.info("门店属性值删除入参：{}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(ModuleEntranceEnum.STORE.code());
        boolean flag = itemClientService.deleteAttrByGuid(itemSingleDTO);
        return flag ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.DELETION_FAILED));
    }

    @ApiOperation(value = "品牌属性值删除", notes = "参数为data，值为属性值guid")
    @PostMapping("/brand/delete_attr")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "品牌属性值删除",action = OperatorType.DELETE)
    public Result deleteBrandAttr(@RequestBody ItemSingleDTO itemSingleDTO) {
        logger.info("品牌属性值删除入参：{}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(ModuleEntranceEnum.BRAND.code());
        boolean flag = itemClientService.deleteAttrByGuid(itemSingleDTO);
        return flag ? Result.buildEmptySuccess() : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.DELETION_FAILED));
    }
}
