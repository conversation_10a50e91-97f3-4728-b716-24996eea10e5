package com.holderzone.holder.saas.aggregation.app.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description 微信配置类
 * @date 2021/9/6 18:13
 * @className: WeChatConfig
 */
@Data
@RefreshScope
@Configuration
public class WeChatConfig {

    @Value("${weChat.accessTokenUrl}")
    private String accessTokenUrl;

    @Value("${weChat.accessTokenUrl2}")
    private String accessTokenUrl2;

    @Value("${weChat.appId}")
    private String appId;

    @Value("${weChat.secret}")
    private String secret;

    @Value("${weChat.ticketUrl}")
    private String ticketUrl;

    @Value("${weChat.scope}")
    private String scope;

    @Value("${weChat.userInfoUrl}")
    private String userInfoUrl;
}