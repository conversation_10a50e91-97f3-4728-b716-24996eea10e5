package com.holderzone.holder.saas.aggregation.app.controller.business;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.business.ReasonClientService;
import com.holderzone.saas.store.dto.business.reason.ReasonDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReasonController
 * @date 2019/08/21 10:57
 * @description //TODO
 * @program holder-saas-store-business
 */
@Slf4j
@Api(value = "原因业务接口")
@RestController
@RequestMapping("/reason")
public class ReasonController {

    private final ReasonClientService reasonClientService;

    @Autowired
    public ReasonController(ReasonClientService reasonClientService) {
        this.reasonClientService = reasonClientService;
    }

    @PostMapping(value = "/findReason")
    @ApiOperation(value = "查询'原因列表',传参storeGuid", notes =
            "0     反结账原因" +
                    "1     作废订单原因" +
                    "2     取消预定原因" +
                    "3     外卖拒单原因" +
                    "4     外卖退单原因" +
                    "5     外卖拒绝退款原因" +
                    "6     退菜原因" +
                    "7     赠菜原因")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "查询原因列表",action = OperatorType.SELECT)
    public Result<List<ReasonDTO>> findReason(@RequestBody ReasonDTO reasonDTO) {
        log.info("查询'原因列表': reasonDTO={}", JacksonUtils.writeValueAsString(reasonDTO));
        return Result.buildSuccessResult(reasonClientService.findReason(reasonDTO));
    }

//    @PostMapping(value = "/insertReason")
//    @ApiOperation(value = "新增'原因列表',传入商家guid,类别guid,原因内容", notes = "新增'原因列表',传入商家guid,类别guid,原因内容")
//    public Result<Void> insertReason(@RequestBody ReasonDTO reasonDTO) {
//        log.info("新增'原因列表': reasonDTO={}",JacksonUtils.writeValueAsString(reasonDTO));
//        reasonClientService.insertReason(reasonDTO);
//        return Result.buildEmptySuccess();
//    }
//
//    @PostMapping(value = "/updateReason")
//    @ApiOperation(value = "修改'原因列表',传入'原因列表'的guid,修改的原因内容", notes = "修改'原因列表',传入'原因列表'的guid,修改的原因内容")
//    public Result<Void> updateReason(@RequestBody ReasonDTO reasonDTO) {
//        log.info("修改'原因列表': reasonDTO={}", JacksonUtils.writeValueAsString(reasonDTO));
//        reasonClientService.updateReason(reasonDTO);
//        return Result.buildEmptySuccess();
//    }
//
//    @PostMapping(value = "/deleteReason")
//    @ApiOperation(value = "删除'原因列表',传入'原因列表'的guid", notes = "删除'原因列表',传入'原因列表'的guid")
//    public Result<Void> deleteReason(@RequestBody ReasonDTO reasonDTO) {
//        reasonClientService.deleteReason(reasonDTO);
//        return Result.buildEmptySuccess();
//    }
//
//
//
//    @PostMapping(value = "/findReasonType")
//    @ApiOperation(value = "查询原因类型", notes = "查询原因类型")
//    public Result<List<ReasonTypeDTO>> findReasonType() {
//        return Result.buildSuccessResult(reasonClientService.findReasonType());
//    }

}