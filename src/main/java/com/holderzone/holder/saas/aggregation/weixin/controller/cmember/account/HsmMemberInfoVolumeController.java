package com.holderzone.holder.saas.aggregation.weixin.controller.cmember.account;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dynamic.datasource.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.weixin.entity.dto.OrganizationNewDTO;
import com.holderzone.holder.saas.aggregation.weixin.entity.dto.WxStoreDTO;
import com.holderzone.holder.saas.aggregation.weixin.entity.dto.WxStoreListReqDTO;
import com.holderzone.holder.saas.aggregation.weixin.entity.dto.WxStoreListRespDTO;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.EnterpriseClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.OrganizationClientService;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestMemberInfoVolumeQuery;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolume;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolumeDetails;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberSourceType;
import com.holderzone.resource.common.dto.enterprise.OrganizationQueryDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.organization.StoreParserDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 会员持卷表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-06-20
 */
@RestController
@RequestMapping("/hsmcw/volume")
@Api(value = "优惠券模块接口")
@Slf4j
public class HsmMemberInfoVolumeController {

//    @Resource
//    private IHsmMemberInfoVolumeService memberInfoVolumeService;

    @Resource
    private HsaBaseClientService hsaBaseClientService;

    @Resource
    private OrganizationClientService organizationClientService;

    @Resource
    private EnterpriseClientService enterpriseClientService;

    @ApiOperation("查询会员优惠券列表")
    @PostMapping("/getMemberVolume")
    public Result<ResponseMemberInfoVolume> getMemberVolume(@RequestBody @Validated RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO) {
//        return Result.buildSuccessResult(memberInfoVolumeService.getMemberVolume(memberInfoVolumeQueryReqDTO));
        return Result.buildSuccessResult(hsaBaseClientService.getMemberVolume(memberInfoVolumeQueryReqDTO).getData());
    }

    @ApiOperation("查看优惠券详情")
    @GetMapping("/getMemberVolumeDetails")
    public Result<ResponseMemberInfoVolumeDetails> getMemberVolumeDetails(@NotNull @RequestParam("memberVolumeGuid") String memberVolumeGuid) {
//        return Result.buildSuccessResult(memberInfoVolumeService.getMemberVolumeDetails(memberVolumeGuid));
        return Result.buildSuccessResult(hsaBaseClientService.getMemberVolumeDetails(memberVolumeGuid).getData());
    }

//    @ApiOperation("查询某商家门店列表")
//    @PostMapping("/getEnterpriseStoreList")
//    public Result<StoreListRespDTO> getEnterpriseStoreList(@RequestBody @Validated StoreListReqDTO storeListReqDTO) {
//    	log.info("storeListReqDTO:{}",JSON.toJSONString(storeListReqDTO));
//        return Result.buildSuccessResult(memberInfoVolumeService.getEnterpriseStoreList(storeListReqDTO));
//    }

    @ApiOperation("查询某商家门店列表")
    @PostMapping("/getEnterpriseStoreList")
    public Result<WxStoreListRespDTO> getEnterpriseStoreList(@RequestBody @Validated WxStoreListReqDTO storeListReqDTO) {
        log.info("storeListReqDTO:{}", JSON.toJSONString(storeListReqDTO));
        WxStoreListRespDTO storeListRespDTO = new WxStoreListRespDTO();
        UserContextUtils.putErp(storeListReqDTO.getEnterpriseGuid());
        UserContext userContext = UserContextUtils.get();
        String operSubjectGuid = userContext.getOperSubjectGuid();
        EnterpriseIdentifier.setEnterpriseGuid(storeListReqDTO.getEnterpriseGuid());
        StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setBrandGuidList(Lists.newArrayList(storeListReqDTO.getBrandGuid()));
        List<StoreDTO> storeDTOS = organizationClientService.queryStoreByConditionNoPage(storeParserDTO);
        if (CollectionUtils.isEmpty(storeDTOS)) {
            log.info("getEnterpriseStoreList 无门店数据！");
            return Result.buildSuccessResult(storeListRespDTO);
        }
        OrganizationQueryDTO organizationQueryDTO = new OrganizationQueryDTO();
        organizationQueryDTO.setEnterpriseGuid(storeListReqDTO.getEnterpriseGuid());
        organizationQueryDTO.setPageSize(Integer.MAX_VALUE);
        Page<OrganizationNewDTO> platformDTOPage = enterpriseClientService.pageQueryAllPlatformStore(organizationQueryDTO);
        if (CollectionUtils.isEmpty(platformDTOPage.getData())){
            log.info("无组织运营主体数据！");
        }
        List<OrganizationNewDTO> platformDTOList = platformDTOPage.getData();
        Map<String, List<OrganizationNewDTO>> platformDTOMap = platformDTOList.stream().collect(Collectors.groupingBy(OrganizationNewDTO::getOrganizationGuid));

        List<WxStoreDTO> wxStoreDTOS = Lists.newArrayList();
        storeDTOS.stream().forEach(e -> {
            List<OrganizationNewDTO> organizationNewDTOS = platformDTOMap.get(e.getGuid());
            if (CollectionUtils.isNotEmpty(organizationNewDTOS)){
                organizationNewDTOS.stream().forEach(a->{
                    if(operSubjectGuid.equals(a.getMultiMemberGuid())){
                        WxStoreDTO wxStoreDTO = new WxStoreDTO();
                        wxStoreDTO.setStoreGuid(e.getGuid());
                        //为了兼容老的方式 key和guid都是storeGuid
                        wxStoreDTO.setStoreKey(e.getGuid());
                        wxStoreDTO.setStoreName(e.getName());
                        wxStoreDTOS.add(wxStoreDTO);
                    }
                });
            }
        });
        storeListRespDTO.setStoreList(wxStoreDTOS);
        log.info("查询某商家门店列表返回参数{}", JacksonUtils.writeValueAsString(storeListRespDTO));
        return Result.buildSuccessResult(storeListRespDTO);
    }

    @ApiOperation("获取优惠券类型")
    @GetMapping("/getVolumeTypes")
    public Result<List<ResponseMemberSourceType>> getVolumeTypes() {
//        return Result.buildSuccessResult(memberInfoVolumeService.getVolumeTypes());
        return Result.buildSuccessResult(hsaBaseClientService.getVolumeTypes().getData());
    }
}
