package com.holderzone.holder.saas.aggregation.merchant.controller.member.account;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.HsmMemberDataStatisticsClientService;
import com.holderzone.holder.saas.member.dto.account.request.MemberDataStatisticsQueryReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberDataStatisticsRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 会员数据统计表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019/5/31
 */
@Api(description = "会员精准营销接口")
@RestController
@RequestMapping("/hsm-member-data-statistics")
public class HsmMemberDataStatisticsController {

    @Resource
    private HsmMemberDataStatisticsClientService memberDataStatisticsClientService;

    @ApiOperation("根据条件精准查询某企业下所有体系的会员（如果是某会员有多体系，仍然展示一条会员数据）")
    @PostMapping("/query_by_condition")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "根据条件精准查询某企业下所有体系的会员")
    public Result<Page<MemberDataStatisticsRespDTO>> queryByCondition(
            @RequestBody @Validated MemberDataStatisticsQueryReqDTO memberDataStatisticsQueryReqDTO) {
        return Result.buildSuccessResult(memberDataStatisticsClientService.queryMemberDataByCondition(memberDataStatisticsQueryReqDTO));
    }
}
