package com.holderzone.holder.saas.aggregation.app.controller.boss;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Chinese2PinyinUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.ItemService;
import com.holderzone.holder.saas.aggregation.app.service.feign.item.ItemClientService;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.ItemRackDTO;
import com.holderzone.saas.store.dto.item.req.ItemReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemSortUpdateReqDTO;
import com.holderzone.saas.store.dto.item.req.TypeSortReqDTO;
import com.holderzone.saas.store.dto.boss.req.BossItemQueryDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO;
import com.holderzone.saas.store.enums.item.ModuleEntranceEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;


/**
 * <AUTHOR>
 * @description 老板助手-商品接口
 * @date 2022/8/16 12:06
 * @className: ItemController
 */
@Slf4j
@RestController
@AllArgsConstructor
@Api(tags = "老板助手-商品接口")
@RequestMapping("/boss/item")
public class BossItemController {

    private final ItemClientService itemClientService;

    private final ItemService itemService;

    /**
     * 老板助手-门店商品列表接口
     * 暂不考虑连锁
     *
     * @param queryDTO 列表查询参数
     * @return 商品列表（不分页）
     */
    @ApiOperation(value = "老板助手-门店商品列表接口")
    @PostMapping("/list_store_item")
    public Result<List<ItemWebRespDTO>> listStoreItem(@RequestBody @Valid BossItemQueryDTO queryDTO) {
        log.info("老板助手-门店商品列表接口入参,queryDTO={}", JacksonUtils.writeValueAsString(queryDTO));
        return Result.buildSuccessResult(itemService.listItemForBoss(queryDTO));
    }

    /**
     * 老板助手-保存商品接口
     * 暂不考虑连锁
     *
     * @param itemSaveReqDTO 保存入参
     * @return EmptySuccess
     */
    @ApiOperation(value = "老板助手-保存商品接口")
    @PostMapping("/save")
    public Result<Void> save(@RequestBody ItemReqDTO itemSaveReqDTO) {
        log.info("老板助手-保存商品接口入参,itemSaveReqDTO={}", JacksonUtils.writeValueAsString(itemSaveReqDTO));
        itemService.saveItemFromBoss(itemSaveReqDTO);
        return Result.buildEmptySuccess();
    }

    /**
     * 老板助手-更新商品接口
     * 暂不考虑连锁
     *
     * @param itemUpdateReqDTO 更新商品接口入参
     * @return EmptySuccess
     */
    @ApiOperation(value = "老板助手更新商品接口")
    @PostMapping("/update")
    public Result<Void> update(@RequestBody ItemReqDTO itemUpdateReqDTO) {
        log.info("老板助手-更新商品接口入参,itemUpdateReqDTO={}", JacksonUtils.writeValueAsString(itemUpdateReqDTO));
        if (1 == itemService.updateItemFromBoss(itemUpdateReqDTO)) {
            return Result.buildEmptySuccess();
        }
        return Result.buildOpFailedResult("更新失败");
    }

    /**
     * 老板助手-获取商品详情
     *
     * @param itemSingleDTO data：商品GUID
     * @return 商品详情
     */
    @ApiOperation(value = "老板助手-获取商品详情", notes = "必填参数：data，商品GUID")
    @PostMapping("/get_item_info")
    public Result<ItemInfoRespDTO> getItemInfo(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("老板助手-获取商品详情接口入参,ItemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return Result.buildSuccessResult(itemClientService.getItemInfo(itemSingleDTO));
    }

    @ApiOperation(value = "根据汉字转拼音首字母", notes = "必填参数：data")
    @PostMapping("/get_first_char")
    public Result<String> getFirstChar(@RequestBody ItemSingleDTO chinese) {
        log.info("根据汉字转拼音首字母接口入参,ItemSingleDTO={}", JacksonUtils.writeValueAsString(chinese));
        String initials = Chinese2PinyinUtils.initials(chinese.getData());
        return Result.buildSuccessResult(initials);
    }

    /**
     * 老板助手-批量上架接口
     *
     * @param itemRackDTO 入参
     * @return Void
     */
    @ApiOperation(value = "老板助手-批量上架接口")
    @PostMapping("/batch_rack")
    public Result<Void> batchRack(@RequestBody ItemRackDTO itemRackDTO) {
        log.info("老板助手-批量上架接口入参,itemRackDTO={}", JacksonUtils.writeValueAsString(itemRackDTO));
        itemRackDTO.setRackState(1);
        itemRackDTO.setFrom(ModuleEntranceEnum.STORE.code());
        Integer rackItem = itemClientService.rackItem(itemRackDTO);
        return Integer.valueOf(1).equals(rackItem) ? Result.buildEmptySuccess() : Result.buildOpFailedResult("商品批量上架失败");
    }

    /**
     * 老板助手-批量下架接口
     *
     * @param itemRackDTO 入参
     * @return Void
     */
    @ApiOperation(value = "老板助手-批量下架接口")
    @PostMapping("/batch_un_rack")
    public Result<Void> batchUnRack(@RequestBody ItemRackDTO itemRackDTO) {
        log.info("老板助手-批量下架接口入参,itemRackDTO={}", JacksonUtils.writeValueAsString(itemRackDTO));
        itemRackDTO.setRackState(0);
        itemRackDTO.setFrom(ModuleEntranceEnum.STORE.code());
        Integer rackItem = itemClientService.rackItem(itemRackDTO);
        return Integer.valueOf(1).equals(rackItem) ? Result.buildEmptySuccess() : Result.buildOpFailedResult("商品批量下架失败");
    }

    /**
     * 老板助手-批量删除商品接口
     *
     * @param itemStringListDTO 入参
     * @return Void
     */
    @ApiOperation(value = "老板助手-批量删除商品接口")
    @PostMapping("/batch_delete")
    public Result<Void> batchDelete(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("老板助手-批量删除商品接口入参,itemStringListDTO={}", JacksonUtils.writeValueAsString(itemStringListDTO));
        itemStringListDTO.setFrom(ModuleEntranceEnum.STORE.code());
        Integer num = itemClientService.batchDelete(itemStringListDTO);
        if (num == 1) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult("删除失败");
        }
    }

    /**
     * 老板助手-菜品排序
     *
     * @param itemSortUpdateReqDTO 入参
     * @return Boolean
     */
    @ApiOperation("老板助手-菜品排序")
    @PostMapping("/sort")
    public Result<Boolean> sort(@RequestBody ItemSortUpdateReqDTO itemSortUpdateReqDTO) {
        log.info("老板助手-菜品排序入参:{}", JacksonUtils.writeValueAsString(itemSortUpdateReqDTO));
        itemSortUpdateReqDTO.setFrom(ModuleEntranceEnum.STORE.code());
        boolean flag = itemClientService.updateItemSort(itemSortUpdateReqDTO);
        return flag ? Result.buildEmptySuccess() : Result.buildOpFailedResult("保存失败");
    }

    /**
     * 老板助手-批量移动商品
     *
     * @param typeSortReqDTO 商品guidList，目标分类guid
     * @return boolean
     */
    @ApiOperation(value = "老板助手-批量移动商品")
    @PostMapping("/batch_move")
    public Result<Boolean> batchMove(@RequestBody TypeSortReqDTO typeSortReqDTO) {
        log.info("老板助手-批量移动商品入参,typeSortReqDTO={}", JacksonUtils.writeValueAsString(typeSortReqDTO));
        return Result.buildSuccessResult(itemClientService.batchMoveItem(typeSortReqDTO));
    }
}
