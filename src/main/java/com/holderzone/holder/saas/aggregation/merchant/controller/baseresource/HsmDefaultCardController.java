package com.holderzone.holder.saas.aggregation.merchant.controller.baseresource;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.baseresource.IHsmBrandDefaultCardService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.baseresource.IHsmStoreDefaultCardService;
import com.holderzone.holder.saas.member.dto.baseresource.request.HsmBrandDefaultCardReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.request.HsmBrandDefaultCardUpdateReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.request.HsmStoreDefaultCardReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.request.HsmStoreDefaultCardUpdateReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.response.HsmBrandDefaultCardRespDTO;
import com.holderzone.holder.saas.member.dto.baseresource.response.HsmStoreDefaultCardRespDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/hsm-default-card")
@Api(description = "默认卡相关controller")
public class HsmDefaultCardController {

    @Resource
    private IHsmBrandDefaultCardService hsmBrandDefaultCardService;

    @Resource
    private IHsmStoreDefaultCardService hsmStoreDefaultCardService;

    @ApiOperation("根据条件查询企业所有品牌的默认卡")
    @PostMapping("/brand/query_by_condition")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "根据条件查询企业所有品牌的默认卡")
    public Result<Page<HsmBrandDefaultCardRespDTO>> queryByCondition(@RequestBody @Validated HsmBrandDefaultCardReqDTO brandDefaultCardReqDTO) {
        return Result.buildSuccessResult(hsmBrandDefaultCardService.queryByCondition(brandDefaultCardReqDTO));
    }

    @ApiOperation("保存或修改品牌默认卡")
    @PostMapping("/brand/saveOrUpdateBrandDefaultCard")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "保存或修改品牌默认卡")
    public Result saveOrUpdateBrandDefaultCard(@RequestBody @Validated HsmBrandDefaultCardUpdateReqDTO brandDefaultCardUpdateReqDTO) {
        if (hsmBrandDefaultCardService.saveOrUpdateBrandDefaultCard(brandDefaultCardUpdateReqDTO)) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.RESULT_MODIFICATION_FAILED));
        }
    }

    @ApiOperation("根据条件查询企业所有门店的默认卡")
    @PostMapping("/store/query_by_condition")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "根据条件查询企业所有门店的默认卡")
    public Result<Page<HsmStoreDefaultCardRespDTO>> queryByCondition(@RequestBody @Validated HsmStoreDefaultCardReqDTO storeDefaultCardReqDTO) {
        return Result.buildSuccessResult(hsmStoreDefaultCardService.queryByCondition(storeDefaultCardReqDTO));
    }

    @ApiOperation("保存或修改门店默认卡")
    @PostMapping("/store/saveOrUpdateStoreDefaultCard")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "保存或修改门店默认卡")
    public Result saveOrUpdateStoreDefaultCard(@RequestBody @Validated HsmStoreDefaultCardUpdateReqDTO storeDefaultCardUpdateReqDTO) {
        if (hsmStoreDefaultCardService.saveOrUpdateStoreDefaultCard(storeDefaultCardUpdateReqDTO)) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.RESULT_MODIFICATION_FAILED));
        }
    }
}
