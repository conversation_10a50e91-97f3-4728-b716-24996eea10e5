package com.holderzone.holder.saas.aggregation.merchant.controller.erp;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp.CheckoutDocumentFeignService;
import com.holderzone.saas.store.dto.erp.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/07 下午 18:39
 * @description
 */
@Api(tags = "盘点单相关接口")
@RestController
@RequestMapping("/checkoutDocument")
public class CheckoutDocumentController {

    private static final Logger log = LoggerFactory.getLogger(CheckoutDocumentController.class);

    @Autowired
    private CheckoutDocumentFeignService checkoutDocumentService;

    @ApiOperation("添加或者更新盘点单")
    @PostMapping("/addOrUpdateCheckoutDocument")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "添加或者更新盘点单")
    public Result<String> addOrUpdateCheckoutDocument(@RequestBody CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO) {
        log.info("添加或者更新盘点单入参: {}", JacksonUtils.writeValueAsString(addOrUpdateDTO));
        return Result.buildSuccessResult(checkoutDocumentService.addOrUpdateCheckoutDocument(addOrUpdateDTO));
    }

    @ApiOperation("查询物料信息(新增物料时使用)")
    @PostMapping("/selectDocumentDetailForAdd")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "查询物料信息(新增物料时使用)")
    public Result<List<CheckoutDocumentDetailSelectDTO>> selectDocumentDetailForAdd(@RequestBody CheckoutDocumentDetailQueryDTO queryDTO) {
        log.info("查询盘点单物料信息(新增物料时使用)入参: {}", JacksonUtils.writeValueAsString(queryDTO));
        return Result.buildSuccessResult(checkoutDocumentService.selectDocumentDetailForAdd(queryDTO));
    }

    @ApiOperation("查询盘点单及其明细(编辑时使用)")
    @PostMapping("/selectDocumentAndDetailForUpdate")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "查询盘点单及其明细(编辑时使用)")
    public Result<CheckoutDocumentSelectDTO> selectDocumentAndDetailForUpdate(String documentGuid) {
        log.info("查询盘点单及其明细(编辑和查看时使用)入参: {}", documentGuid);
        return Result.buildSuccessResult(checkoutDocumentService.selectDocumentAndDetailForUpdate(documentGuid));
    }

    @ApiOperation("查询盘点单及其明细(查看时使用)")
    @PostMapping("/selectDocumentAndDetailForSelect")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "查询盘点单及其明细(查看时使用)")
    public Result<CheckoutDocumentSelectDTO> selectDocumentAndDetailForSelect(String documentGuid) {
        log.info("查询盘点单及其明细(查看时使用)入参: {}", documentGuid);
        return Result.buildSuccessResult(checkoutDocumentService.selectDocumentAndDetailForSelect(documentGuid));
    }

    @ApiOperation("删除盘点单")
    @PostMapping("/deleteDocument")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "删除盘点单")
    public Result deleteDocument(String documentGuid) {
        log.info("删除盘点单入参: {}", documentGuid);
        checkoutDocumentService.deleteCheckoutDocumentAndDetail(documentGuid);
        return Result.buildEmptySuccess();
    }

    @ApiOperation("提交盘点单")
    @PostMapping("/submitCheckoutDocument")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "提交盘点单")
    public Result<String> submitCheckoutDocument(@RequestBody CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO) {
        log.info("提交盘点单入参: {}", JacksonUtils.writeValueAsString(addOrUpdateDTO));
        return Result.buildSuccessResult(checkoutDocumentService.submitCheckoutDocument(addOrUpdateDTO));
    }

    @ApiOperation("分页查询盘点单列表")
    @PostMapping("/selectCheckoutDocumentForPage")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "分页查询盘点单列表")
    public Result<Page<CheckoutDocumentSelectDTO>> selectCheckoutDocumentForPage(@RequestBody CheckoutDocumentQueryDTO queryDTO) {
        log.info("分页查询盘点单列表入参: {}", JacksonUtils.writeValueAsString(queryDTO));
        return Result.buildSuccessResult(checkoutDocumentService.selectCheckoutDocumentForPage(queryDTO));
    }
}
