package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020/7/17 17:36
 * @description
 */
@ApiModel("查询所有门店请求参数")
@Accessors(chain = true)
@Data
public class WxStoreListReqDTO {
    @ApiModelProperty("企业GUID")
    @NotNull(
            message = "企业GUID不能为空"
    )
    private String enterpriseGuid;
    @ApiModelProperty("门店类型，0全部门店，1仅支持充值的门店，目前所有门店都支持充值，可为空（暂未使用）")
    private Integer storeType;
    @ApiModelProperty("品牌GUID，精确到品牌就需要传，只要企业级别可为空")
    @NotNull(
            message = "品牌GUID不能为空"
    )
    private String brandGuid;
    @ApiModelProperty("体系GUID，过滤当前体系，充值门店需要（20200717废弃）")
    private String systemManagementGuid;
}
