package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceEstimateDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceOrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceOrderPriceDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 预订单服务调用
 * @className WxStoreAdvanceOrderClientService
 * @date 2019/3/19
 */
@Component
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = WxStoreAdvanceOrderClientService.WxStoreAdvanceOrderFallBack.class)
public interface WxStoreAdvanceOrderClientService {

    String URL_PREFIX = "/wx_store_advance_order_provide";

    @PostMapping(value = URL_PREFIX + "/add")
    WxStoreAdvanceEstimateDTO createAdvanceOrder(WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO);

    @PostMapping(value = URL_PREFIX + "/del")
    Boolean delAdvanceOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

    @PostMapping(value = URL_PREFIX + "/advance")
    WxStoreAdvanceOrderPriceDTO getPersonWxStoreAdvanceOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

    @PostMapping(value = URL_PREFIX + "/table")
    WxStoreAdvanceOrderPriceDTO getTableWxStoreAdvanceOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

    @PostMapping(value = URL_PREFIX + "/update_remark")
    void updateAdvanceOrderRemark(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

    @ApiOperation("释放桌台锁")
    @PostMapping(value = URL_PREFIX + "/release_table_lock")
    void releaseTableLock(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);


    @Slf4j
    @Component
    class WxStoreAdvanceOrderFallBack implements FallbackFactory<WxStoreAdvanceOrderClientService> {
        @Override
        public WxStoreAdvanceOrderClientService create(Throwable throwable) {
            return new WxStoreAdvanceOrderClientService() {

                @Override
                public WxStoreAdvanceEstimateDTO createAdvanceOrder(WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO) {
                    log.error("远程调用服务失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean delAdvanceOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
                    log.error("远程调用服务失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public WxStoreAdvanceOrderPriceDTO getPersonWxStoreAdvanceOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
                    log.error("远程调用服务失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }


                @Override
                public WxStoreAdvanceOrderPriceDTO getTableWxStoreAdvanceOrder(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
                    log.error("远程调用服务失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public void updateAdvanceOrderRemark(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
                    throwable.printStackTrace();
                    log.error("远程调用服务失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public void releaseTableLock(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
                    throwable.printStackTrace();
                    log.error("远程调用服务失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

            };
        }
    }
}
