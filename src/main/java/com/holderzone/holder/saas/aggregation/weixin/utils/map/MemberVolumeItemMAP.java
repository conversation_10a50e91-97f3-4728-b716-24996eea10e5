package com.holderzone.holder.saas.aggregation.weixin.utils.map;

import com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList;
import com.holderzone.saas.store.dto.weixin.deal.MemberVolumeItemDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MemberVolumeItemMAP {

    MemberVolumeItemMAP INSTANCE = Mappers.getMapper(MemberVolumeItemMAP.class);

    default Integer whetherOriginPrice(boolean useAlone) {
        return useAlone ? 1 : 0;
    }

//	@Mapping(target = "whetherOriginPrice", source = "isUseAlone")
//	MemberVolumeItemDTO fromMemberInfoVolume(MemberInfoVolumeDTO memberInfoVolumeDTO);

//	List<MemberVolumeItemDTO> fromMemberInfoVolumeList(List<MemberInfoVolumeDTO> memberInfoVolumeDTOS);

	@Mappings({
			@Mapping(target = "whetherOriginPrice", expression = "java(whetherOriginPrice(volumeListRespDTO.isUseAlone()))"),
			@Mapping(target = "enable", source = "useable")
	})
	MemberVolumeItemDTO fromVolumeListRespDTO(ResponseVolumeList volumeListRespDTO);

	List<MemberVolumeItemDTO> fromVolumeList(List<ResponseVolumeList> volumeListRespDTOS);
}
