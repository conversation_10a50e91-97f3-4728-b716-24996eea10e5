package com.holderzone.holder.saas.aggregation.merchant.controller.journaling;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Lists;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.entity.dto.StoreGatherExportDTO;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.pay.StorePayClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.trade.TradeClientService;
import com.holderzone.holder.saas.aggregation.merchant.util.ExcelUtil;
import com.holderzone.holder.saas.aggregation.merchant.util.ReportValidateUtil;
import com.holderzone.saas.store.dto.journaling.req.JournalAppBaseReqDTO;
import com.holderzone.saas.store.dto.journaling.req.StoreGatherReportReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.BusinessDataRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.StoreGatherReportRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.StoreGatherTotalRespDTO;
import com.holderzone.saas.store.dto.pay.QuickPayStatisticsReqDTO;
import com.holderzone.saas.store.dto.pay.QuickPayStatisticsRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreGatherReportController
 * @date 2019/05/29 14:23
 * @description 报表-门店汇总controller
 * @program holder-saas-aggregation-merchant
 */
@Slf4j
@RestController
@RequestMapping("/storeGather")
@Api(value = "报表-门店汇总", tags = "报表-门店汇总")
public class StoreGatherReportController {

    @Resource
    private TradeClientService tradeClientService;

    @Resource
    private StorePayClientService storePayClientService;

    @PostMapping(value = "storeGatherList")
    @ApiOperation(value = "门店汇总列表")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT, description = "门店汇总列表")
    public Result<StoreGatherTotalRespDTO> storeGatherList(@RequestBody StoreGatherReportReqDTO storeGatherReportReqDTO) {
        log.info("[门店汇总列表]入参={}", JacksonUtils.writeValueAsString(storeGatherReportReqDTO));
        //门店兼容
        ReportValidateUtil.reportCompatibilityCheck(storeGatherReportReqDTO);
        StoreGatherTotalRespDTO storeGatherTotalResp = tradeClientService.listStoreGatherBusiness(storeGatherReportReqDTO);
        log.info("[门店汇总列表]storeGatherTotalResp={}", JacksonUtils.writeValueAsString(storeGatherTotalResp));
        //配置门店汇总总计
        assemblyRecordTotal(storeGatherTotalResp, storeGatherReportReqDTO);
        return Result.buildSuccessResult(storeGatherTotalResp);
    }

    private static final String[] STORE_GATHER_HEADERS = {"日期", "品牌", "门店名称", "应收（元）", "优惠（元）", "实收（元）", "订单数", "单均（元）", "客流量", "客均（元）", "平均用餐时长（分）"};

    @PostMapping("/storeGatherList/export")
    public void storeGatherListExport(@RequestBody StoreGatherReportReqDTO queryDTO, HttpServletResponse response) {
        ReportValidateUtil.reportCompatibilityCheck(queryDTO);

        queryDTO.setCurrentPage(1);
        queryDTO.setPageSize(Constants.MAX_EXPORT_SIZE);
        StoreGatherTotalRespDTO storeGatherTotalResp = tradeClientService.listStoreGatherBusiness(queryDTO);

        List<StoreGatherReportRespDTO> data = storeGatherTotalResp.getRespDataPage().getData();
        ExcelUtil<StoreGatherExportDTO> excelUtil = new ExcelUtil<>();
        List<StoreGatherExportDTO> exportList = Lists.newArrayList();
        data.forEach(d -> {
            StoreGatherExportDTO export = new StoreGatherExportDTO();
            BeanUtil.copyProperties(d, export);
            exportList.add(export);
        });
        // 增加汇总数据
        addSummarize(exportList);
        try {
            response.setContentType("application/msexcel;charset=UTF-8");
            String substring = System.currentTimeMillis() + "_门店汇总" + ".xls";
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(substring, "UTF-8"));
            ServletOutputStream outputStream = response.getOutputStream();
            excelUtil.exportExcel(STORE_GATHER_HEADERS, exportList, outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void addSummarize(List<StoreGatherExportDTO> exportList) {
        if (CollectionUtils.isEmpty(exportList)) {
            return;
        }
        StoreGatherExportDTO summarize = new StoreGatherExportDTO();
        BigDecimal orderFee = exportList.stream()
                .map(StoreGatherExportDTO::getOrderFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
        summarize.setOrderFee(orderFee);
        BigDecimal discountFee = exportList.stream()
                .map(StoreGatherExportDTO::getDiscountFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
        summarize.setDiscountFee(discountFee);
        BigDecimal actuallyPayFee = exportList.stream()
                .map(StoreGatherExportDTO::getActuallyPayFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
        summarize.setActuallyPayFee(actuallyPayFee);
        Integer orderCount = exportList.stream()
                .map(StoreGatherExportDTO::getOrderCount)
                .filter(Objects::nonNull)
                .reduce(0, Integer::sum);
        summarize.setOrderCount(orderCount);

        Integer guestCount = exportList.stream()
                .map(StoreGatherExportDTO::getGuestCount)
                .filter(Objects::nonNull)
                .reduce(0, Integer::sum);
        summarize.setGuestCount(guestCount);

        exportList.add(summarize);
    }

    private void assemblyRecordTotal(StoreGatherTotalRespDTO storeGatherTotalResp, StoreGatherReportReqDTO baseReq) {
        JournalAppBaseReqDTO journalAppBaseReq = new JournalAppBaseReqDTO();
        journalAppBaseReq.setBusinessStartDateTime(baseReq.getBusinessStartDateTime());
        journalAppBaseReq.setBusinessEndDateTime(baseReq.getBusinessEndDateTime());
        journalAppBaseReq.setStoreGuidList(baseReq.getStoreGuids());
        BusinessDataRespDTO businessData = tradeClientService.getOrderStatistics(journalAppBaseReq);
        log.info("[门店汇总列表]businessData={}", JacksonUtils.writeValueAsString(businessData));
        if (businessData == null) {
            // 处理快速收款数据
            setNullDateQuickPay(storeGatherTotalResp, baseReq);
            return;
        }

        // 处理快速收款数据
        BigDecimal quickPayAmountTotal = BigDecimal.ZERO;
        Integer quickPayOrderCount = 0;
        Integer quickPayGuestCount = 0;
        QuickPayStatisticsReqDTO quickPaReqDTO = new QuickPayStatisticsReqDTO();
        quickPaReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        quickPaReqDTO.setStartDateTime(baseReq.getBusinessStartDateTime());
        quickPaReqDTO.setEndDateTime(baseReq.getBusinessEndDateTime());
        quickPaReqDTO.setStoreGuidList(baseReq.getStoreGuids());
        List<QuickPayStatisticsRespDTO> quickPayList = storePayClientService.queryQuickPayStatistics(quickPaReqDTO);
        if (!CollectionUtils.isEmpty(quickPayList)) {
            quickPayAmountTotal = quickPayList.stream()
                    .map(QuickPayStatisticsRespDTO::getAmountTotal)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            quickPayOrderCount = quickPayList.stream()
                    .map(QuickPayStatisticsRespDTO::getOrderCount)
                    .filter(Objects::nonNull)
                    .reduce(0, Integer::sum);
            quickPayGuestCount = quickPayList.stream()
                    .map(QuickPayStatisticsRespDTO::getGuestCount)
                    .filter(Objects::nonNull)
                    .reduce(0, Integer::sum);
        }

        storeGatherTotalResp.setActuallyPayFeeTotal(businessData.getActuallyPayFee() == null ?
                BigDecimal.ZERO.add(quickPayAmountTotal) : businessData.getActuallyPayFee().add(quickPayAmountTotal));
        storeGatherTotalResp.setOrderFeeTotal(businessData.getBusinessFee() == null ?
                BigDecimal.ZERO.add(quickPayAmountTotal) : businessData.getBusinessFee().add(quickPayAmountTotal));
        storeGatherTotalResp.setOrderCountTotal(businessData.getOrderCount() == null ?
                quickPayOrderCount : businessData.getOrderCount() + quickPayOrderCount);
        storeGatherTotalResp.setGuestCountTotal(businessData.getGuestCount() == null ?
                quickPayGuestCount : businessData.getGuestCount() + quickPayGuestCount);
        storeGatherTotalResp.setOrderAverageFeeTotal(storeGatherTotalResp.getActuallyPayFeeTotal()
                .divide(BigDecimal.valueOf(storeGatherTotalResp.getOrderCountTotal()), 2, RoundingMode.HALF_UP));
        storeGatherTotalResp.setGuestAverageFeeTotal(storeGatherTotalResp.getActuallyPayFeeTotal()
                .divide(BigDecimal.valueOf(storeGatherTotalResp.getGuestCountTotal()), 2, RoundingMode.HALF_UP));
        storeGatherTotalResp.setOrderAverageTimeTotal(businessData.getOrderAverageTimeTotal() == null ? 0 : businessData.getOrderAverageTimeTotal());
        // 此处因bug【39174】涉及，不再查询团购券去减
//        List<AmountItemDTO> amountItemDTOList = tradeClientService.listRefundByRequest(baseReq);
//        BigDecimal groupAmount = BigDecimal.ZERO;
//        if (!CollectionUtils.isEmpty(amountItemDTOList)) {
//            groupAmount = amountItemDTOList.stream().map(AmountItemDTO::getDiscountAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//        }
        storeGatherTotalResp.setDiscountFeeTotal(storeGatherTotalResp.getOrderFeeTotal().subtract(storeGatherTotalResp.getActuallyPayFeeTotal()));
    }

    private void setNullDateQuickPay(StoreGatherTotalRespDTO storeGatherTotalResp,
                                     StoreGatherReportReqDTO baseReq) {
        QuickPayStatisticsReqDTO quickPaReqDTO = new QuickPayStatisticsReqDTO();
        quickPaReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        quickPaReqDTO.setStartDateTime(baseReq.getBusinessStartDateTime());
        quickPaReqDTO.setEndDateTime(baseReq.getBusinessEndDateTime());
        quickPaReqDTO.setStoreGuidList(baseReq.getStoreGuids());
        List<QuickPayStatisticsRespDTO> quickPayList = storePayClientService.queryQuickPayStatistics(quickPaReqDTO);
        if (CollectionUtils.isEmpty(quickPayList)) {
            log.warn("[setNullDateQuickPay]快速收款数据为空");
            return;
        }
        BigDecimal quickPayAmountTotal = quickPayList.stream()
                .map(QuickPayStatisticsRespDTO::getAmountTotal)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        Integer quickPayOrderCount = quickPayList.stream()
                .map(QuickPayStatisticsRespDTO::getOrderCount)
                .filter(Objects::nonNull)
                .reduce(0, Integer::sum);
        Integer quickPayGuestCount = quickPayList.stream()
                .map(QuickPayStatisticsRespDTO::getGuestCount)
                .filter(Objects::nonNull)
                .reduce(0, Integer::sum);
        storeGatherTotalResp.setActuallyPayFeeTotal(quickPayAmountTotal);
        storeGatherTotalResp.setOrderFeeTotal(quickPayAmountTotal);
        storeGatherTotalResp.setOrderCountTotal(quickPayOrderCount);
        storeGatherTotalResp.setGuestCountTotal(quickPayGuestCount);
        storeGatherTotalResp.setOrderAverageFeeTotal(storeGatherTotalResp.getActuallyPayFeeTotal()
                .divide(BigDecimal.valueOf(storeGatherTotalResp.getOrderCountTotal()), 2, RoundingMode.HALF_UP));
        storeGatherTotalResp.setGuestAverageFeeTotal(storeGatherTotalResp.getActuallyPayFeeTotal()
                .divide(BigDecimal.valueOf(storeGatherTotalResp.getGuestCountTotal()), 2, RoundingMode.HALF_UP));
        storeGatherTotalResp.setDiscountFeeTotal(storeGatherTotalResp.getOrderFeeTotal().subtract(storeGatherTotalResp.getActuallyPayFeeTotal()));
    }

}
