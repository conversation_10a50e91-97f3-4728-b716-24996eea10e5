package com.holderzone.holder.saas.aggregation.merchant.controller.erp;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.entity.vo.BomItemVO;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp.BomFeignService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.item.ItemClientService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.GoodsBomConfigDTO;
import com.holderzone.saas.store.dto.erp.GoodsBomDTO;
import com.holderzone.saas.store.dto.item.req.DoubleDataPageDTO;
import com.holderzone.saas.store.dto.item.req.SingleDataPageDTO;
import com.holderzone.saas.store.dto.item.resp.ErpItemDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/05/06 13:47
 */
@RestController
@RequestMapping("bom")
@Api(tags = "菜品bom配置Api")
public class BomController {
    private static final Logger LOGGER = LoggerFactory.getLogger(BomController.class);

    @Autowired
    BomFeignService bomFeignService;
    @Autowired
    ItemClientService itemClientService;

    @PostMapping("add")
    @ApiOperation("添加bom配置")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "添加bom配置")
    public Result add(@RequestBody GoodsBomConfigDTO goodsBomDTO) {
        LOGGER.info("聚合层bom配置入参:->{}", JacksonUtils.writeValueAsString(goodsBomDTO));
        bomFeignService.add(goodsBomDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("findByGoods")
    @ApiOperation("查询商品bom配置")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "查询商品bom配置")
    public Result findBomByGoods(@RequestBody GoodsBomDTO goodsBomDTO) {
        LOGGER.info("聚合层查询商品bom配置入参:->{}", JacksonUtils.writeValueAsString(goodsBomDTO));
        if (!StringUtils.isEmpty(goodsBomDTO.getGoodsGuid()) && !StringUtils.isEmpty(goodsBomDTO.getGoodsSku())) {
            return Result.buildSuccessResult(bomFeignService.findBomByGoods(goodsBomDTO.getGoodsGuid(), goodsBomDTO.getGoodsSku()));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.GUID_AND_SKU_EMPTY));
    }

    @PostMapping("findTypeByStore")
    @ApiOperation("查询商品分类列表")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "查询商品分类列表")
    public Result findBomGoodsType(@RequestBody SingleDataDTO singleDataDTO) {
        LOGGER.info("聚合层查询ERP商品分类列表入参:->{}", JacksonUtils.writeValueAsString(singleDataDTO));
        if (StringUtils.isEmpty(singleDataDTO.getData())) {
            throw new ParameterException("请先创建门店");
        }
        return Result.buildSuccessResult(itemClientService.listTypeStore(singleDataDTO));
    }

    @PostMapping("findGoodsListByType")
    @ApiOperation(value = "根据分类查询商品列表", notes = "data1:门店GUID;data2:分类GUID")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "根据分类查询商品列表")
    public Result findGoodsList(@RequestBody DoubleDataPageDTO doubleDataPageDTO) {
        LOGGER.info("聚合层根据分类查询ERP商品列表入参:->{}", JacksonUtils.writeValueAsString(doubleDataPageDTO));
        Page<ErpItemDTO> page;
        if (StringUtils.isEmpty(doubleDataPageDTO.getData2())) {
            //查询所有
            SingleDataPageDTO singleDataPageDTO = buildSingleDataPage(doubleDataPageDTO);
            page = itemClientService.listAllItemStore(singleDataPageDTO);
        } else {
            //查询指定分类
            SingleDataPageDTO singleDataPageDTO = buildSingleDataPage(doubleDataPageDTO);
            page = itemClientService.listItemStore(singleDataPageDTO);
        }
        return page == null ? null : Result.buildSuccessResult(fillGoodsBomMaterial(page));
    }

    @PostMapping("findGoodsListByName")
    @ApiOperation(value = "根据名称查询商品列表", notes = "data1:门店GUID;data2:查询条件(名称)")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "根据名称查询商品列表")
    public Result findGoodsListByName(@RequestBody DoubleDataPageDTO doubleDataPageDTO) {
        LOGGER.info("聚合层ERP根据名称查询商品列表入参:->{}", JacksonUtils.writeValueAsString(doubleDataPageDTO));
        Page<ErpItemDTO> page;
        if (StringUtils.isEmpty(doubleDataPageDTO.getData1())) {
            return Result.buildEmptySuccess();
        } else {
            //查询指定名称的商品
            page = itemClientService.listItemByName(doubleDataPageDTO);
        }
        return page == null ? null : Result.buildSuccessResult(fillGoodsBomMaterial(page));
    }

    private Page fillGoodsBomMaterial(Page page) {
        List<ErpItemDTO> data = page.getData();
        List<String> skuList = data.stream().map(ErpItemDTO::getSkuId).collect(Collectors.toList());
        List<GoodsBomDTO> bomCountBySku = bomFeignService.findBomCountBySku(skuList);
        List<BomItemVO> dataList = new ArrayList<>(data.size());
        for (ErpItemDTO itemDTO : data) {
            BomItemVO bomItemVO = new BomItemVO();
            BeanUtils.copyProperties(itemDTO, bomItemVO);
            List<GoodsBomDTO> bomDTOS = bomCountBySku.stream().filter(goodsBomDTO -> goodsBomDTO.getGoodsSku().equals(itemDTO.getSkuId())).collect(Collectors.toList());
            if (!bomDTOS.isEmpty()) {
                GoodsBomDTO goodsBomDTO = bomDTOS.get(0);
                bomItemVO.setMaterialType(goodsBomDTO.getMaterialCategoryCount());
            }
            dataList.add(bomItemVO);
        }
        page.setData(dataList);
        return page;
    }

    private SingleDataPageDTO buildSingleDataPage(DoubleDataPageDTO doubleDataPageDTO) {
        SingleDataPageDTO singleDataPageDTO = new SingleDataPageDTO();
        //data2不为空代表查询指定分类,为空代表全部分类只根据门店GUID查询所有就行
        singleDataPageDTO.setData(StringUtils.isEmpty(doubleDataPageDTO.getData2()) ? doubleDataPageDTO.getData1() : doubleDataPageDTO.getData2());
        singleDataPageDTO.setCurrentPage(doubleDataPageDTO.getCurrentPage());
        singleDataPageDTO.setPageSize(doubleDataPageDTO.getPageSize());
        return singleDataPageDTO;
    }
}
