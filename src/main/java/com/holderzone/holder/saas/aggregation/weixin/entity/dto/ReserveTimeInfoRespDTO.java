package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;

@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("时间与周期相关")
@Accessors(chain = true)
public class ReserveTimeInfoRespDTO {
	@ApiModelProperty("日期")
	private LocalDate localDate;

	@ApiModelProperty("星期")
	private String msg;
}
