package com.holderzone.holder.saas.aggregation.app.service;

/**
 * <AUTHOR>
 * @description 聚合层交易服务接口
 * @date 2021/9/13 18:14
 * @className: TradeService
 */
public interface TradeService {

    /**
     * 发起预支付
     * payPowerId 支付宝31 微信51
     *
     * @param orderGuid  订单guid
     * @param payPowerId 支付功能的id
     * @return 二维码链接
     */
    String padPay(String orderGuid, String payPowerId) throws InterruptedException ;

    /**
     * 并台发送结账成功推送消息
     *
     * @param orderGuid 订单guid
     * @return 结果
     */
    Boolean sendMsgWhenCombine(String orderGuid);
}
