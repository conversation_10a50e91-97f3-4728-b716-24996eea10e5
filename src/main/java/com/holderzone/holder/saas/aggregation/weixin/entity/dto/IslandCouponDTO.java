package com.holderzone.holder.saas.aggregation.weixin.entity.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


@ApiModel(description = "优惠券")
@Data
public class IslandCouponDTO implements Serializable {


    @ApiModelProperty(value = "guid")
    private String guid;

    @NotNull
    @ApiModelProperty(value = "优惠券名称")
    private String name;

    @ApiModelProperty(value = "num")
    private Integer num;

    /**
     * 赠送类型 0 金额 1 积分 2 会员卡 3 成长值 4 优惠券
     */
    private Integer giftType;
}
