package com.holderzone.holder.saas.aggregation.app.controller.boss;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.item.ItemClientService;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.AttrGroupReqDTO;
import com.holderzone.saas.store.dto.item.req.AttrGroupUpdateReqDTO;
import com.holderzone.saas.store.dto.item.req.AttrReqDTO;
import com.holderzone.saas.store.dto.item.req.AttrSaveReqDTO;
import com.holderzone.saas.store.dto.item.resp.AttrGroupAttrRespDTO;
import com.holderzone.saas.store.dto.item.resp.AttrRespDTO;
import com.holderzone.saas.store.enums.item.ModuleEntranceEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 老板助手-属性接口
 * @date 2022/8/17 9:49
 * @className: BossAttrController
 */
@Slf4j
@RestController
@AllArgsConstructor
@Api(tags = "老板助手-属性接口")
@RequestMapping("/boss/attr")
public class BossAttrController {

    private final ItemClientService itemClientService;

    /**
     * 老板助手-新增商品时的属性列表
     *
     * @param itemSingleDTO 门店guid
     * @return 新增商品时的属性列表
     */
    @ApiOperation(value = "老板助手-新增商品时的属性列表")
    @PostMapping("/list_for_save_item")
    public Result<List<AttrGroupAttrRespDTO>> listForSaveItem(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("老板助手-新增商品时的属性列表入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(ModuleEntranceEnum.STORE.code());
        return Result.buildSuccessResult(itemClientService.listAttrForSaveItem(itemSingleDTO));
    }

    /**
     * 老板助手-获取门店属性组列表
     *
     * @param itemSingleDTO 必填参数：data:值：门店GUID
     * @return 门店属性组列表
     */
    @ApiOperation(value = "老板助手-获取门店属性组列表", notes = " 必填参数：data:值：门店GUID")
    @PostMapping("/list_attr_group")
    public Result<List<AttrGroupAttrRespDTO>> listAttrGroup(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("老板助手-获取门店属性组列表入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setKeywords("0");
        itemSingleDTO.setFrom(ModuleEntranceEnum.STORE.code());
        return Result.buildSuccessResult(itemClientService.listAttrGroup(itemSingleDTO));
    }

    /**
     * 老板助手-保存属性组接口
     *
     * @param attrGroupReqDTO 入参
     * @return Void
     */
    @ApiOperation(value = "老板助手-保存属性组接口")
    @PostMapping("/save_attr_group")
    public Result<Void> saveAttrGroup(@RequestBody @Valid AttrGroupReqDTO attrGroupReqDTO) {
        log.info("老板助手-保存属性组接口入参,attrGroupReqDTO={}", JacksonUtils.writeValueAsString(attrGroupReqDTO));
        attrGroupReqDTO.setFrom(ModuleEntranceEnum.STORE.code());
        boolean flag = itemClientService.saveAttrGroup(attrGroupReqDTO);
        return flag ? Result.buildEmptySuccess() : Result.buildOpFailedResult("保存失败");
    }


    /**
     * 老板助手-修改属性组接口
     *
     * @param attrGroupUpdateReqDTO 入参
     * @return Void
     */
    @ApiOperation(value = "老板助手-修改属性组接口")
    @PostMapping("/update_attr_group")
    public Result<Void> updateAttrGroup(@RequestBody @Valid AttrGroupUpdateReqDTO attrGroupUpdateReqDTO) {
        log.info("老板助手-修改属性组接口入参,attrGroupUpdateReqDTO={}", JacksonUtils.writeValueAsString(attrGroupUpdateReqDTO));
        attrGroupUpdateReqDTO.setFrom(ModuleEntranceEnum.STORE.code());
        boolean flag = itemClientService.setAttrGroup(attrGroupUpdateReqDTO);
        return flag ? Result.buildEmptySuccess() : Result.buildOpFailedResult("修改失败");
    }

    /**
     * 老板助手-删除属性组接口
     *
     * @param itemSingleDTO data：属性组guid
     * @return Void
     */
    @ApiOperation(value = "老板助手-删除属性组接口")
    @PostMapping("/delete_attr_group")
    public Result<Void> deleteStoreAttrGroup(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("老板助手-删除属性组接口入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(ModuleEntranceEnum.STORE.code());
        boolean flag = itemClientService.deleteAttrGroupByGuid(itemSingleDTO);
        return flag ? Result.buildEmptySuccess() : Result.buildOpFailedResult("删除失败");
    }

    /**
     * 老板助手-属性组下的属性列表
     *
     * @param itemSingleDTO 参数为data, 值为attrGroupGuid
     * @return 属性组下的属性列表
     */
    @ApiOperation(value = "老板助手-属性组下的属性列表", notes = "参数为data, 值为attrGroupGuid")
    @PostMapping("/list_by_group")
    public Result<List<AttrRespDTO>> listByGroup(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("老板助手-属性组下的属性列表入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(ModuleEntranceEnum.STORE.code());
        List<AttrRespDTO> attrList = itemClientService.listAttrByGroup(itemSingleDTO);
        return Result.buildSuccessResult(attrList);
    }

    /**
     * 老板助手-属性详情保存接口
     *
     * @param attrReqDTO 入参
     * @return Void
     */
    @ApiOperation(value = "老板助手-属性详情保存接口")
    @PostMapping("/save")
    public Result<Void> savePropertyValue(@RequestBody @Valid AttrReqDTO attrReqDTO) {
        log.info("老板助手-属性详情保存接口入参,attrReqDTO={}", JacksonUtils.writeValueAsString(attrReqDTO));
        attrReqDTO.setFrom(ModuleEntranceEnum.STORE.code());
        boolean flag = itemClientService.saveAttrValue(attrReqDTO);
        return flag ? Result.buildEmptySuccess() : Result.buildOpFailedResult("保存失败");
    }

    /**
     * 老板助手-修改属性值
     *
     * @param attrSaveReqDTO 入参
     * @return Void
     */
    @ApiOperation(value = "老板助手-修改属性值")
    @PostMapping("/update")
    public Result<Void> updateStoreAttr(@RequestBody AttrSaveReqDTO attrSaveReqDTO) {
        log.info("老板助手-修改属性值入参，attrSaveReqDTO={}", JacksonUtils.writeValueAsString(attrSaveReqDTO));
        attrSaveReqDTO.setFrom(ModuleEntranceEnum.STORE.code());
        attrSaveReqDTO.setPrice(ObjectUtils.isEmpty(attrSaveReqDTO.getPrice()) ? BigDecimal.ZERO : attrSaveReqDTO.getPrice());
        boolean flag = itemClientService.updateAttrValue(attrSaveReqDTO);
        return flag ? Result.buildEmptySuccess() : Result.buildOpFailedResult("保存失败");
    }

    /**
     * 老板助手-属性值删除
     *
     * @param itemSingleDTO 参数为data，值为属性值guid
     * @return Void
     */
    @ApiOperation(value = "老板助手-属性值删除", notes = "参数为data，值为属性值guid")
    @PostMapping("/delete")
    public Result<Void> deleteStoreAttr(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("老板助手-属性值删除入参，itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(ModuleEntranceEnum.STORE.code());
        boolean flag = itemClientService.deleteAttrByGuid(itemSingleDTO);
        return flag ? Result.buildEmptySuccess() : Result.buildOpFailedResult("删除失败");
    }

    /**
     * 检查属性是否被使用
     *
     * @param attrGuid 属性guid
     * @return Boolean
     */
    @ApiOperation(value = "检查属性是否被使用")
    @GetMapping("/check_attr_used")
    public Result<Boolean> checkAttrUsed(@RequestParam("attrGuid") String attrGuid) {
        log.info("检查属性是否被使用入参,attrGuid={}", attrGuid);
        return Result.buildSuccessResult(itemClientService.checkAttrUsed(attrGuid));
    }
}
