package com.holderzone.holder.saas.aggregation.weixin.context;

import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.entity.bo.DiscountRuleBO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.trade.DiscountDTO;
import com.holderzone.saas.store.dto.weixin.deal.CalculateOrderDTO;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberSessionDTO;
import com.holderzone.saas.store.dto.weixin.resp.CalculateOrderRespDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
@Slf4j
public class DiscountContext {

    private CalculateOrderDTO calculateOrderDTO;

    private CalculateOrderRespDTO calculateOrderRespDTO;

    private List<DineInItemDTO> allItems;

    /**
     * 限时特价前的商品备份
     */
    private List<DineInItemDTO> beforeSpecialsItems;

    /**
     * 无限时特价情况下代金券优惠金额
     */
    private BigDecimal memberGrouponDiscount;

    private DiscountRuleBO discountRuleBO;

    private Map<Integer, DiscountDTO> discountTypeMap;

    private List<DiscountFeeDetailDTO> discountFeeDetailDTOS;

    private Map<String, DineInItemDTO> dineInItemDTOMap;

    private UserMemberSessionDTO userMemberSession;

    private String volumeGuid;

    private Integer volumeCodeType;

    private boolean integralStore;

    private boolean rejectDiscount;

    /**
     * 是否登录会员
     */
    private boolean hasMember;

    /**
     * 是否使用会员价
     */
    private Boolean useMemberPriceFlag;

    /**
     * 是否使用会员折扣
     */
    private Boolean useMemberDiscountFlag;

    /**
     * 是否需要满减满折活动列表
     */
    private Boolean needFullMarketActivityList;

    /**
     * 是否需要限时活动活动列表
     */
    private Boolean needLimitSpecialsMarketActivityList;

    private boolean isCalculateMinPrice;

    /**
     * 是否首次进入
     */
    @ApiModelProperty(value = "是否首次进入")
    private Boolean isFirst;

    @ApiModelProperty(value = "是否替换")
    private Boolean isReplace;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty("订单记录guid")
    private String memberConsumptionGuid;

    public static DiscountContext init(CalculateOrderDTO calculateDTO, CalculateOrderRespDTO calculateOrderRespDTO,
                                       DiscountRuleBO discountRuleBO, UserMemberSessionDTO userMemberSession) {
        DiscountContext discountContext = new DiscountContext();
        discountContext.setCalculateOrderDTO(calculateDTO);
        calculateOrderRespDTO.setActivitySelectList(Lists.newArrayList());
        calculateOrderRespDTO.setActivityInfoList(Lists.newArrayList());
        discountContext.setRejectDiscount(false);
        discountContext.setCalculateOrderRespDTO(calculateOrderRespDTO);
        discountContext.setDiscountRuleBO(discountRuleBO);
        discountContext.setOrderGuid(calculateDTO.getOrderGuid());
        // 是否首次进入
        discountContext.setIsFirst(ObjectUtils.isEmpty(calculateDTO.getIsFirst()) ? Boolean.FALSE : calculateDTO.getIsFirst());
        discountContext.setIsReplace(ObjectUtils.isEmpty(calculateDTO.getIsReplace()) ? Boolean.FALSE : calculateDTO.getIsReplace());
        // 默认认为是登录了会员的
        discountContext.setHasMember(true);
        if (Objects.equals(BaseDeviceTypeEnum.WECHAT.getCode(), calculateDTO.getDeviceType())
                && Boolean.FALSE.equals(userMemberSession.getIsLogin())) {
            discountContext.setHasMember(false);
            discountContext.setRejectDiscount(true);
        }
        // 是否需要查询满减满折活动列表
        discountContext.setNeedFullMarketActivityList(!Objects.equals(BaseDeviceTypeEnum.WECHAT.getCode(), calculateDTO.getDeviceType()));
        // 是否需要查询限时折扣活动列表
        discountContext.setNeedLimitSpecialsMarketActivityList(!Objects.equals(BaseDeviceTypeEnum.WECHAT.getCode(), calculateDTO.getDeviceType()));
        // 是否使用会员价
        discountContext.setUseMemberPriceFlag(discountRuleBO.getHasMemberPrice() && calculateDTO.getUseMemberDiscountFlag());
        // 是否使用会员折扣
        discountContext.setUseMemberDiscountFlag(discountRuleBO.getHasMemberDiscount() && calculateDTO.getUseMemberDiscountFlag());
        // 是否使用积分
        discountContext.setIntegralStore(calculateDTO.getMemberIntegralStore());
        // 剩余可优惠金额
        calculateOrderRespDTO.setOrderSurplusFee(calculateOrderRespDTO.getOrderFee());
        calculateOrderRespDTO.setOrderSurplusFeeBySkipSpecials(calculateOrderRespDTO.getOrderFee());
        // 初始化折扣优惠列表
        List<DiscountDTO> discountList = initDiscountList(discountRuleBO);
        Map<Integer, DiscountDTO> discountTypeMap = discountList.stream()
                .collect(Collectors.toMap(DiscountDTO::getDiscountType, Function.identity(), (key1, key2) -> key1));
        discountContext.setDiscountFeeDetailDTOS(Lists.newArrayList());
        discountContext.setDiscountTypeMap(discountTypeMap);
        // 所有菜品
        List<DineInItemDTO> allItems = Lists.newArrayList(calculateDTO.getDineInItemList());
        allItems.forEach(i -> {
            i.setDiscountTotalPrice(i.getItemPrice());
            i.setTotalDiscountFee(BigDecimal.ZERO);
        });
        log.info("所有菜品：{}", JacksonUtils.writeValueAsString(allItems));
        discountContext.setAllItems(allItems);
        // 所有菜品map用于修改单个菜品优惠金额
        Map<String, DineInItemDTO> dineInItemMap = allItems.stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (key1, key2) -> key1));
        discountContext.setDineInItemDTOMap(dineInItemMap);

        // 优惠券类型
        discountContext.setVolumeGuid(discountRuleBO.getVolumeGuid());
        discountContext.setVolumeCodeType(discountRuleBO.getVolumeCodeType());
        // 会员信息
        discountContext.setUserMemberSession(userMemberSession);
        return discountContext;
    }

    private static List<DiscountDTO> initDiscountList(DiscountRuleBO discountRuleBO) {
        List<DiscountDTO> initDiscountList = buildInitDiscountDOList();
        return buildDiscountList(initDiscountList, discountRuleBO);
    }

    private static List<DiscountDTO> buildInitDiscountDOList() {
        List<DiscountDTO> discountList = Lists.newArrayList();
        for (DiscountTypeEnum discountTypeEnum : DiscountTypeEnum.values()) {
            if (!discountTypeEnum.equals(DiscountTypeEnum.OTHER)) {
                DiscountDTO discountDTO = new DiscountDTO();
                discountDTO.setDiscountType(discountTypeEnum.getCode());
                discountDTO.setDiscountName(discountTypeEnum.getDesc());
                discountDTO.setDiscountFee(BigDecimal.ZERO);
                discountList.add(discountDTO);
            }
        }
        return discountList;
    }

    private static List<DiscountDTO> buildDiscountList(List<DiscountDTO> discountList, DiscountRuleBO discountRuleBO) {
        for (DiscountDTO discountDTO : discountList) {
            if (DiscountTypeEnum.get(discountDTO.getDiscountType()) == DiscountTypeEnum.MEMBER) {
                if (discountRuleBO.getMemberDiscount() == null) {
                    discountRuleBO.setMemberDiscount(discountDTO.getDiscount());
                } else {
                    discountDTO.setDiscount(discountRuleBO.getMemberDiscount());
                }
            }
        }
        return discountList;

    }

}
