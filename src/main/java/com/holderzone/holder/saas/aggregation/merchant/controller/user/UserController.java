package com.holderzone.holder.saas.aggregation.merchant.controller.user;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dynamic.datasource.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.cloud.CloudEnterpriseFeignClient;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.cloud.CloudUserFeignClient;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.user.UserFeignService;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.user.HolderAdjustUserDTO;
import com.holderzone.resource.common.dto.holder.HolderUserDTO;
import com.holderzone.resource.common.enums.RegTypeEnum;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.user.*;
import com.holderzone.saas.store.dto.user.resp.UserFaceDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@RestController
@RequestMapping("/user")
@Api(description = "员工管理接口")
public class UserController {

    private final UserFeignService userFeignService;

    private final CloudEnterpriseFeignClient cloudEnterpriseFeignClient;

    private final CloudUserFeignClient cloudUserFeignClient;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    public UserController(UserFeignService userFeignService, CloudEnterpriseFeignClient cloudEnterpriseFeignClient,
                          CloudUserFeignClient cloudUserFeignClient) {
        this.userFeignService = userFeignService;
        this.cloudEnterpriseFeignClient = cloudEnterpriseFeignClient;
        this.cloudUserFeignClient = cloudUserFeignClient;
    }

    @ApiOperation(value = "自动生成帐号", notes = "自动生成六位数字的帐号")
    @PostMapping(value = "/new_account")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "自动生成帐号")
    public Result<String> newAccount() {
        return Result.buildSuccessResult(userFeignService.newAccount());
    }

    @ApiOperation(value = "自动生成授权码", notes = "自动生成六位数字的授权码")
    @PostMapping(value = "/new_auth_code")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "自动生成授权码")
    public Result<String> newAuthCode() {
        return Result.buildSuccessResult(userFeignService.newAuthCode());
    }

    @ApiOperation(value = "新建职位", notes = "新建职位")
    @PostMapping(value = "/new_user_office")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "新建职位")
    public Result<UserOfficeDTO> newUserOffice(@RequestBody UserOfficeDTO userOfficeDTO) {
        return Result.buildSuccessResult(userFeignService.newUserOffice(userOfficeDTO));
    }

    @ApiOperation(value = "查询职位列表", notes = "查询职位列表")
    @PostMapping(value = "/list_user_office")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "查询职位列表")
    public Result<List<UserOfficeDTO>> listUserOffice() {
        return Result.buildSuccessResult(userFeignService.listUserOffice());
    }

    @ApiOperation(value = "新建员工", notes = "执行成功返回值为新建的员工的guid")
    @PostMapping(value = "/create")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "新建员工")
    public Result<String> create(@RequestBody @Validated(UserDTO.Create.class) UserDTO userDTO) {
        if (log.isInfoEnabled()) {
            log.info("新建员工接口入参：{}", JacksonUtils.writeValueAsString(userDTO));
        }
        return Result.buildSuccessResult(userFeignService.createUser(userDTO));
    }

    @ApiOperation(value = "更新员工信息")
    @PostMapping(value = "/update")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "更新员工信息")
    public Result<String> update(@Validated(UserDTO.Update.class) @RequestBody UserDTO userDTO) {
        if (log.isInfoEnabled()) {
            log.info("更新员工信息接口入参：{}，", JacksonUtils.writeValueAsString(userDTO));
        }
        return Result.buildSuccessResult(userFeignService.updateUser(userDTO));
    }

    @ApiOperation(value = "查询用户信息")
    @PostMapping(value = "/query")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "查询用户信息")
    public Result<UserDTO> query(@Validated(UserDTO.Query.class) @RequestBody UserDTO userDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询用户信息接口入参：{}，", JacksonUtils.writeValueAsString(userDTO));
        }
        return Result.buildSuccessResult(userFeignService.queryUser(userDTO));
    }

    @ApiOperation(value = "查询用户信息")
    @PostMapping(value = "/queryInfo")
    public Result<com.holderzone.resource.common.dto.user.UserDTO> queryInfo(@Validated(UserDTO.Query.class) @RequestBody UserDTO userDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询用户信息接口入参：{}，", JacksonUtils.writeValueAsString(userDTO));
        }
        return Result.buildSuccessResult(cloudUserFeignClient.findUserById(userDTO.getGuid()));
    }

    @ApiOperation(value = "查询用户人脸信息")
    @PostMapping(value = "/getUserFaceInfo")
    public Result<UserFaceDTO> getUserFaceInfo(@RequestBody SingleDataDTO dto) {
        log.info("查询用户人脸信息,dto={}", JacksonUtils.writeValueAsString(dto));
        return Result.buildSuccessResult(userFeignService.getUserFaceInfo(dto));
    }

    @ApiOperation(value = "启用员工")
    @PostMapping(value = "/enable")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "启用员工")
    public Result enable(@Validated(UserDTO.Enable.class) @RequestBody UserDTO userDTO) {
        if (log.isInfoEnabled()) {
            log.info("启用员工入参：{}", JacksonUtils.writeValueAsString(userDTO));
        }
        userFeignService.enableOrDisableUser(userDTO.setIsEnable(true));
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "禁用员工")
    @PostMapping(value = "/disable")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "禁用员工")
    public Result disable(@Validated(UserDTO.Disable.class) @RequestBody UserDTO userDTO) {
        if (log.isInfoEnabled()) {
            log.info("禁用员工入参：{}", JacksonUtils.writeValueAsString(userDTO));
        }
        userFeignService.enableOrDisableUser(userDTO.setIsEnable(false));
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "删除员工")
    @PostMapping(value = "/delete")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "删除员工")
    public Result delete(@Validated(UserDTO.Delete.class) @RequestBody UserDTO userDTO) {
        if (log.isInfoEnabled()) {
            log.info("删除员工入参：{}", JacksonUtils.writeValueAsString(userDTO));
        }
        userFeignService.deleteUser(userDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "分页查询员工信息")
    @PostMapping(value = "/page_query")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "分页查询员工信息")
    public Result<Page<UserDTO>> pageQuery(@Validated @RequestBody UserQueryDTO userQueryDTO) {
        if (log.isInfoEnabled()) {
            log.info("分页查询员工信息入参：{}", JacksonUtils.writeValueAsString(userQueryDTO));
        }
        return Result.buildSuccessResult(userFeignService.pageQuery(userQueryDTO));
    }

    @ApiOperation(value = "修改密码（用户自行输入密码）")
    @PostMapping(value = "/update_pwd")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "改密码（用户自行输入密码）")
    public Result updatePwd(@Validated @RequestBody UpdatePwdDTO updatePwdDTO) {
        if (log.isInfoEnabled()) {
            log.info("修改密码入参：{}", JacksonUtils.writeValueAsString(updatePwdDTO));
        }
        userFeignService.updatePwd(updatePwdDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "用户自行修改密码")
    @PostMapping(value = "/self_update_pwd")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "用户自行修改密码")
    public Result selfModifyPwd(@Validated @RequestBody UpdatePwdDTO updatePwdDTO) {
        if (log.isInfoEnabled()) {
            log.info("修改密码入参：{}", JacksonUtils.writeValueAsString(updatePwdDTO));
        }
        userFeignService.selfModifyPwd(updatePwdDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "重置密码（生成随机密码并发送短信）")
    @PostMapping(value = "/reset_pwd")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "重置密码（生成随机密码并发送短信）")
    public Result resetPwd(@RequestBody(required = false) ResetPwdDTO resetPwdDTO) {
        if (log.isInfoEnabled()) {
            log.info("重置密码请求入参：{}", Optional.ofNullable(resetPwdDTO)
                    .map(JacksonUtils::writeValueAsString).orElse("null"));
        }
        userFeignService.resetPwd(resetPwdDTO);
        return Result.buildEmptySuccess();
    }

    private final static String DEFAULT_ADMINISTRATOR ="默认管理员";

    @ApiOperation(value = "获取用户基础信息")
    @PostMapping(value = "/get_user_base_info")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "获取用户基础信息")
    public Result<UserBaseDTO> getUserBaseInfo() {
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        String managementModel = cloudEnterpriseFeignClient.queryManagementModel(enterpriseGuid);
        EnterpriseDTO enterprise = cloudEnterpriseFeignClient.findEnterprise(enterpriseGuid);
        return Result.buildSuccessResult(new UserBaseDTO().setEnterpriseName(enterprise.getName())
                .setEnterpriseNo(UserContextUtils.getEnterpriseNo())
                .setUserName(UserContextUtils.getUserName().equals(DEFAULT_ADMINISTRATOR) ? LocaleUtil.getMessage("DEFAULT_ADMINISTRATOR") : UserContextUtils.getUserName())
                .setUserGuid(UserContextUtils.getUserGuid())
                .setEnterpriseGuid(enterpriseGuid)
                .setTel(UserContextUtils.getUserTel())
                .setManagementModel(managementModel)
                .setRegType(enterprise.getRegType())
                .setIntegrateFlag(enterprise.getIntegrateFlag())
                .setBaseDictionaryDTO(cloudEnterpriseFeignClient.queryManagementType(enterpriseGuid))
        );
    }

    @ApiOperation(value = "门店版用户创建")
    @PostMapping(value = "/create_store_user")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "获取用户基础信息")
    public Result<String> createStoreUser(@RequestBody UserDTO userDTO) {
        String guid = userDTO.getGuid();
        if (StringUtils.isEmpty(userDTO.getGuid())) {
            guid = userFeignService.createUser(userDTO);
            userDTO.setGuid(guid);
        } else {
            userFeignService.updateUser(userDTO);
        }
        String storeGuid = userDTO.getStoreGuid();
        UserDataDTO userDataDTO = new UserDataDTO();
        userDataDTO.setGuid(storeGuid);
        List<UserDataDTO> list = Collections.singletonList(userDataDTO);
        UserDataStoreRuleDTO userDataStoreRuleDTO = new UserDataStoreRuleDTO();
        userDataStoreRuleDTO.setUserStoreData(list);
        userDTO.setUserDataStoreRule(userDataStoreRuleDTO);
        userFeignService.saveUserData(userDTO);
        return Result.buildSuccessResult(guid);
    }


    @ApiOperation(value = "holder同步修改员工信息")
    @PutMapping(value = "/holder/sync")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "holder同步修改员工信息")
    public Result syncHolderUser(@RequestBody HolderUserDTO userDTO) {
        if (log.isInfoEnabled()) {
            log.info("holder同步修改员工信息接口入参：{}，", JacksonUtils.writeValueAsString(userDTO));
        }
        cloudUserFeignClient.syncHolderUserPwd(userDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "holder同步修改企业负责人")
    @PutMapping(value = "/holder/admin/sync")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "holder同步修改企业负责人")
    public Result syncHolderAdminUser(@RequestBody HolderAdjustUserDTO userDTO) {
        if (log.isInfoEnabled()) {
            log.info("holder同步修改企业负责人接口入参：{}，", JacksonUtils.writeValueAsString(userDTO));
        }
        if (Objects.isNull(userDTO.getSourceUserId()) || Objects.isNull(userDTO.getTargetUserId())) {
            log.error("修改负责人参数不完整");
            return Result.buildEmptySuccess();
        }
        String enterpriseGuid = request.getHeader("enterpriseGuid");
        EnterpriseDTO enterprise = cloudEnterpriseFeignClient.findEnterprise(enterpriseGuid);
        if (Objects.isNull(enterprise)) {
            log.error("未查询到企业信息,enterpriseGuid:{}", enterpriseGuid);
            return Result.buildEmptySuccess();
        }
        if (Objects.isNull(enterprise.getRegType())
                || (!RegTypeEnum.HOLDER_CLIENT.getType().equals(enterprise.getRegType())
                && !RegTypeEnum.HOLDER_PLATFORM.getType().equals(enterprise.getRegType()))) {
            log.error("该企业未绑定holder,不能同步修改,enterprise:{}", JacksonUtils.writeValueAsString(enterprise));
            return Result.buildEmptySuccess();
        }
        userDTO.setEnterpriseGuid(enterpriseGuid);
        userDTO.setEnterpriseNo(enterprise.getUid());
        userDTO.setRegType(enterprise.getRegType());
        // holder同步修改企业负责人
        cloudUserFeignClient.syncHolderAdminUser(userDTO);
        return Result.buildEmptySuccess();
    }

}
