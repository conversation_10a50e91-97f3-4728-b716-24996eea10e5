package com.holderzone.holder.saas.aggregation.merchant.entity.enums;

import com.google.common.collect.Maps;
import lombok.Getter;
import org.springframework.context.i18n.LocaleContextHolder;
import java.util.Map;
import java.util.ResourceBundle;

/**
 * <AUTHOR>
 * @create 2023-07-19
 * @description
 */
@Getter
public enum MerchantMenuLocaleEnum {

    MENU_HOME("首页"),

    MENU_STORES_AND_STAFF("门店及员工"),

    MENU_EMPLOYEE_MANAGEMENT("员工管理"),

    MENU_EMPLOYEE_LIST("员工列表"),

    MENU_CREATE_ACCOUNT("新建账号"),

    MENU_EDIT_ACCOUNT("编辑账号"),

    MENU_ROLE_AUTHORIZATION("角色授权"),

    MENU_ROLE_LIST("角色列表"),

    MENU_STORE_MANAGEMENT("门店管理"),

    MENU_STORE_LIST("门店列表"),

    MENU_TABLE_SEATING("门店桌位"),
    MENU_SETTLEMENT_RULES("结算规则"),
    MENU_AD_SPACE_MANAGEMENT("广告位管理"),
    MENU_DEVICE_BINDING("设备绑定"),
    MENU_STORE_ACCOUNTING("门店扎帐"),
    MENU_PAYMENT_METHODS("收款方式"),
    MENU_DISCOUNT_RULES("省零规则"),
    MENU_QUEUE_SETTINGS("排队设置"),
    MENU_RESERVATION_SETTINGS("预定设置"),
    MENU_ADDITIONAL_CHARGES("附加费"),
    MENU_GROUP_BUYING_MANAGEMENT("团购管理"),
    MENU_TAKEAWAY_MANAGEMENT("外卖管理"),
    MENU_TAKEAWAY_BATCH_BIND("外卖批量绑定"),
    MENU_REASON_MANAGEMENT("原因管理"),
    MENU_STORAGE_MANAGEMENT("寄存管理"),
    MENU_PRINTING_SETTINGS("打印设置"),
    MENU_PRINTING_CLASS_TEMPLATE("打印分类模版"),
    MENU_CREATE_NEW_STORE("新建门店"),
    MENU_EDIT_STORE("编辑门店"),
    MENU_TAG_MANAGEMENT("标签管理"),
    MENU_AGGREGATED_PAYMENT_ACCOUNT_SETTINGS("聚合支付账户设置"),
    MENU_ASSOCIATED_PRODUCTS_MEITUAN("关联商品(美团)"),
    MENU_ASSOCIATED_PRODUCTS_ELEME("关联商品(饿了么)"),
    MENU_ASSOCIATED_PRODUCTS_SELF_OWNED_STORE("关联商品（自营门店）"),
    MENU_CORPORATE_BRAND("企业品牌"),
    MENU_ORGANIZATIONAL_STRUCTURE("组织机构"),
    MENU_WE_CHAT_OFFICIAL_ACCOUNT("微信公众号"),
    MENU_AUTH_JUMP("Authjump"),
    MENU_OFFICIAL_ACCOUNT_AUTHORIZATION("公众号授权"),
    MENU_WE_CHAT_STORE("微信门店"),
    MENU_BUSINESS_CONFIGURATION("业务配置"),
    MENU_BULK_ORDER_SETTINGS("点餐批量设置"),
    MENU_EDIT_ORDER_CONFIGURATION("编辑点餐配置"),
    MENU_TABLE_STICKER_SETTINGS("桌贴设置"),
    MENU_EDIT_QUEUE_CONFIGURATION("编辑排队配置"),
    MENU_BULK_EDIT_QUEUE_SETTINGS("批量编辑排队设置"),
    MENU_PIECEWORK_MANAGEMENT("计件管理"),
    MENU_OPERATOR_SUPPLEMENT("补录操作员"),
    MENU_ACCOUNT_CREDIT_MANAGEMENT("挂账管理"),
    MENU_ACCOUNT_CREDIT_REPAYMENT("挂账还款"),
    MENU_ACCOUNT_CREDIT_UNIT("挂账单位"),
    MENU_PRODUCT_SALES("商品销售"),
    MENU_BRAND_LIBRARY("品牌库"),
    MENU_PRODUCT_LIBRARY("商品库"),
    MENU_CREATE_NEW_PRODUCT("新建单品"),
    MENU_CREATE_NEW_COMBO("新建套餐"),
    MENU_EDIT_SELF_PRODUCT("编辑单品"),
    MENU_EDIT_COMBO("编辑套餐"),
    MENU_ATTRIBUTE_LIBRARY("属性库"),
    MENU_BRAND_GALLERY("品牌图库"),
    MENU_PRODUCT_SALES_MODES("商品销售模式"),
    MENU_MENU_SOLUTIONS("菜谱方案"),
    MENU_EDIT_SOLUTION_PRODUCTS("方案商品编辑"),
    MENU_KDS_PRODUCT_DISPLAY("KDS商品显示"),
    MENU_CREATE_EDIT_DISPLAY_RULES("新建/编辑显示规则"),
    MENU_CREATE_EDIT_MENU_SUMMARY("新建/编辑菜品汇总"),
    MENU_BULK_EDIT_MENU("批量编辑菜谱"),
    MENU_STORE_PRODUCTS("门店商品"),
    MENU_PRODUCT_LIST("商品列表"),
    MENU_ATTRIBUTE_LIST("属性列表"),
    MENU_STORE_GALLERY("门店图库"),
    MENU_BANQUET_PACKAGES("宴会套餐"),
    MENU_PRODUCT_INVENTORY("商品估清"),
    MENU_SALES_TEMPLATES("销售模板"),
    MENU_TEMPLATE_MENUS("模板菜单"),
    MENU_ADD_PRODUCT("添加商品"),
    MENU_EDIT_PRODUCT("编辑商品"),
    MENU_CREATE_BANQUET_PACKAGE("创建宴会套餐"),
    MENU_EDIT_BANQUET_PACKAGE("编辑宴会套餐"),
    MENU_EDIT_MENU("编辑菜单"),
    MENU_MEMBER_MARKETING("会员营销"),
    MENU_CUSTOMER_MANAGEMENT("顾客管理"),
    MENU_CUSTOMER_LIST("顾客列表"),
    MENU_CUSTOMER_SEGMENTS("顾客人群"),
    MENU_ADD_SEGMENT_TAG("新增(标签)人群"),
    MENU_EDIT_SEGMENT_TAG("编辑(标签)人群"),
    MENU_CUSTOMER_DETAILS("顾客详情"),
    MENU_DETAILS_TAG("(标签)详情"),
    MENU_DELETE_SEGMENT_TAG("删除（标签）人群"),
    MENU_POINTS_DETAILS("积分明细"),
    MENU_GROWTH_POINTS_DETAILS("成长值明细"),
    MENU_MEMBERSHIP_CARD_MANAGEMENT("会员卡管理"),
    MENU_BASIC_SETTINGS("基本设置"),
    MENU_LEVELS_AND_PRIVILEGES("等级与权益"),
    MENU_GROWTH_POINTS_RULES("成长值规则"),
    MENU_RECHARGE_RULES("充值规则"),
    MENU_ADD_LEVEL_AND_PRIVILEGES("新增等级与权益"),
    MENU_LEVEL_AND_PRIVILEGES_DETAILS("等级与权益详情"),
    MENU_EDIT_LEVEL_AND_PRIVILEGES("编辑等级与权益"),
    MENU_COPY_LEVEL_AND_PRIVILEGES("复制等级与权益"),
    MENU_PRIVILEGE_CARD_MANAGEMENT("权益卡管理"),
    MENU_PRIVILEGE_CARD_DETAILS("权益卡详情"),
    MENU_CREATE_NEW_PRIVILEGE_CARD_PAGE("新增权益卡（页面）"),
    MENU_EDIT_PRIVILEGE_CARD_PAGE("编辑权益卡（页面）"),
    MENU_POINTS_MANAGEMENT("积分管理"),
    MENU_EARNING_RULES("获取规则"),
    MENU_REDEMPTION_RULES("消耗规则"),
    MENU_EXPIRY_RULES("有效期规则"),
    MENU_RULES_EXPLANATION("规则说明"),
    MENU_POINTS_ACTIVITY_RECORDS("积分活动记录"),
    MENU_MARKETING_CENTER("营销中心"),
    MENU_RANDOM_RED_ENVELOPES("随行红包"),
    MENU_CERTIFICATION_REWARDS("认证有礼"),
    MENU_COUPON_LIST("优惠券列表"),
    MENU_MARKETING_ACTIVITIES("营销活动"),
    MENU_PRECISION_MARKETING("精准营销"),
    MENU_THIRD_PARTY_PLATFORM_ACTIVITIES("第三方平台活动"),
    MENU_FULL_REDUCTION_AND_DISCOUNTS("满减满折"),
    MENU_RECHARGE_GIFTS("充值赠送"),
    MENU_SUBSIDY_ACTIVITIES("补贴活动"),
    MENU_ADD_COUPON("添加优惠券"),
    MENU_ADD_PRODUCT_COUPON("添加商品券"),
    MENU_CREATE_NEW_ACTIVITY("新建活动"),
    MENU_COUPON_STATISTICS("优惠券统计"),
    MENU_VIEW_COUPON("查看优惠券"),
    MENU_EDIT_ACTIVITY("编辑活动"),
    MENU_EDIT_COUPON("编辑优惠券"),
    MENU_ACTIVITY_DETAILS("活动详情"),
    MENU_VIEW_PRODUCT_COUPON("查看商品券"),
    MENU_ACTIVITY_STATISTICS("活动统计"),
    MENU_EDIT_PRODUCT_COUPON("编辑商品券"),
    MENU_GIFTING_RECORDS("赠送记录"),
    MENU_COUPON_EXCLUSION_SETTINGS("优惠券互斥设置"),
    MENU_RECHARGE_GIFT_ACTIVITY_DETAILS("充值赠送活动详情"),
    MENU_CREATE_NEW_RECHARGE_GIFT_ACTIVITY("新增充值赠送活动"),
    MENU_EDIT_RECHARGE_GIFT_ACTIVITY("编辑充值赠送活动"),
    MENU_CREATE_NEW_SUBSIDY_ACTIVITY("新建补贴活动"),
    MENU_CREATE_NEW_THIRD_PARTY_PLATFORM_ACTIVITY("新建第三方平台活动"),
    MENU_EDIT_THIRD_PARTY_PLATFORM_ACTIVITY("编辑第三方平台活动"),
    MENU_VIEW_THIRD_PARTY_PLATFORM_ACTIVITY("查看第三方平台活动"),
    MENU_THIRD_PARTY_PLATFORM_ACTIVITY_STATISTICS("第三方平台活动统计"),
    MENU_DATA_REPORT_NEW("数据报表-新"),
    MENU_BUSINESS_OVERVIEW("营业概况"),
    MENU_STORE_SUMMARY("门店汇总"),
    MENU_DATA_DASHBOARD("数据大屏"),
    MENU_PRODUCT_REPORTS("商品报表"),
    MENU_PRODUCT_SALES_STATISTICS("商品销量统计"),
    MENU_PRODUCT_CATEGORY_STATISTICS("商品分类统计"),
    MENU_COMBO_SALES_STATISTICS("套餐销量统计"),
    MENU_RETURN_REPORTS("退货报表"),
    MENU_DISH_RETURN_DETAILS("退菜明细"),
    MENU_PRODUCT_SALES_DETAILS("商品销量明细"),
    MENU_GIFT_REPORTS("赠送报表"),
    MENU_GIFT_DETAILS("赠送明细"),
    MENU_STORE_PRODUCT_SALES("门店商品销量"),
    MENU_FINANCIAL_REPORTS("财务报表"),
    MENU_TAKEAWAY_SETTLEMENT_DETAILS("外卖结算明细"),
    MENU_GROUP_BUYING_SETTLEMENT_DETAILS("团购结算明细"),
    MENU_PAYMENT_COMPOSITION("收款构成"),
    MENU_ORDER_SUMMARY("订单汇总"),
    MENU_ORDER_REPORTS("订单报表"),
    MENU_ORDER_STATISTICS("订单统计"),
    MENU_ORDER_DETAILS("订单明细"),
    MENU_TAKEAWAY_ORDER_DETAILS("外卖订单明细"),
    MENU_DOWNLOAD_CENTER("下载中心"),
    MENU_OPERATOR_REPORTS("操作员报表"),
    MENU_SERVICE_DETAILS_REPORT("服务明细报表"),
    MENU_SERVICE_SUMMARY_REPORT("服务汇总报表"),
    MENU_TAKEAWAY_ABNORMAL_DATA("外卖异常数据"),
    MENU_ABNORMAL_DATA("异常数据"),
    MENU_REPAIR_AUDIT("修复审核"),
    MENU_DATA_REPAIR("数据修复"),
    MENU_AUDIT_DETAILS("审核详情"),
    MENU_SHIFT_HANDOVER_STATISTICS("交接班统计"),
    MENU_MORE("更多"),
    MENU_SMS_RECHARGE("短信充值"),
    MENU_DATA_REPORT_OLD("数据报表-旧"),
    MENU_SALES_DETAILS("销售明细"),
    MENU_DAILY_EXPORT("每日导数"),
    MENU_PAYMENT_METHOD_STATISTICS("支付方式统计"),
    MENU_INVENTORY_MANAGEMENT("库存管理"),
    MENU_CREATE_MATERIAL("建资料"),
    MENU_CREATE_NEW_MATERIAL("新建物料"),
    MENU_EDIT_MATERIAL("编辑物料"),
    MENU_MATERIAL_LIST("物料列表"),
    MENU_MATERIAL_CATEGORY("物料分类"),
    MENU_CONFIGURE_PRODUCTS("配置商品"),
    MENU_PRODUCT_MATERIAL_RATIO("商品物料配比"),
    MENU_SUPPLIER_LIST("供应商列表"),
    MENU_CREATE_SUPPLIER("新建供应商"),
    MENU_EDIT_SUPPLIER("编辑供应商"),
    MENU_SUPPLIER_PRICING_SCHEME("供应商报价方案"),
    MENU_INBOUND_MANAGEMENT("入库管理"),
    MENU_INBOUND_LIST("入库列表"),
    MENU_CREATE_INBOUND_ORDER("新建入库单"),
    MENU_EDIT_INBOUND_ORDER("编辑入库单"),
    MENU_VIEW_INBOUND_ORDER("查看入库单"),
    MENU_OUTBOUND_MANAGEMENT("出库管理"),
    MENU_OUTBOUND_LIST("出库列表"),
    MENU_CREATE_OUTBOUND_ORDER("新建出库单"),
    MENU_EDIT_OUTBOUND_ORDER("编辑出库单"),
    MENU_VIEW_OUTBOUND_ORDER("查看出库单"),
    MENU_STOCKTAKING_MANAGEMENT("盘点管理"),
    MENU_STOCKTAKING_LIST("盘点单列表"),
    MENU_CREATE_STOCKTAKING_ORDER("新建盘点单"),
    MENU_VIEW_STOCKTAKING_ORDER("查看盘点单"),
    MENU_EDIT_STOCKTAKING_ORDER("编辑盘点单"),
    MENU_WAREHOUSE_MANAGEMENT("仓库管理(企业)"),
    MENU_WAREHOUSE_LIST("仓库列表"),
    MENU_STOCK_INQUIRY("库存查询"),
    MENU_IN_OUT_FLOW_DETAILS("出入库流水明细"),
    MENU_SUPPLIER_RECONCILIATION("供应商对账表"),
    MENU_MATERIAL_CONSUMPTION_SUMMARY("物料耗用汇总"),
    MENU_SETTINGS("设置"),
    MENU_CASHIER_SETTINGS("收银设置"),
    MENU_CASHIER_SETTINGS_HOMEPAGE("收银设置主页"),
    MENU_PRINTING_SETTINGS_HOMEPAGE("打印设置主页"),
    MENU_FRONT_DESK_PRINTER("前台打印机"),
    MENU_KITCHEN_PRINTER("后厨打印机"),
    MENU_LABEL_PRINTER("标签打印机"),
    MENU_ONE_CLICK_BACKUP_RESTORE("一键备份/还原"),
    MENU_OPERATION_SETTINGS("操作设置"),
    MENU_SETTINGS_HOMEPAGE("设置主页"),
    MENU_MEMBERS("会员"),
    MENU_ORDERS("订单"),
    MENU_DINE_IN_ORDERS("正餐订单"),
    MENU_TAKEAWAY_ORDERS("外卖订单"),
    MENU_ORDERS_HOMEPAGE("订单主页"),
    MENU_FAST_FOOD_ORDERS("快餐订单"),
    MENU_TAKEAWAY("外卖"),
    MENU_DINE_IN("正餐"),
    MENU_STORAGE("寄存"),
    MENU_REPORTS("报表"),
    MENU_SHIFT_HANDOVER("交接班"),
    MENU_SELF_CHECK("自检"),
    MENU_CANTEEN_CARD("食堂卡"),
    MENU_ADJUSTMENT_ORDER("调整单"),
    MENU_FAST_FOOD("快餐"),
    MENU_GUIDED_PROCESS("引导流程"),
    MENU_PRODUCTION_POINT("制作点位"),
    MENU_ORDER_INFORMATION("订单信息"),
    MENU_HISTORY_RECORDS("历史记录"),
    MENU_PRODUCTION_POINT_OUTLET_AND_DISH_CONFIGURATION("制作点堂口及菜品配置"),
    MENU_PRODUCTION_POINT_DISPLAY_MODE("制作点显示模式"),
    MENU_PRODUCTION_POINT_ADVANCED_CONFIGURATION("制作点高级配置"),
    MENU_PRODUCTION_POINT_PRINTER_CONFIGURATION("制作点打印机配置"),
    MENU_ABOUT_KDS_KITCHEN_DISPLAY_SYSTEM("制作点关于KDS"),
    MENU_OUTLET("出堂口"),
    MENU_DISH_CONFIGURATION("菜品配置"),
    MENU_DISPLAY_MODE("显示模式"),
    MENU_ADVANCED_CONFIGURATION("高级配置"),
    MENU_PRINTER_CONFIGURATION("打印机配置"),
    MENU_ABOUT_KDS("关于KDS"),
    MENU_QUICK_CHECKOUT("快速收银"),
    MENU_ORDER_ACCEPTANCE("接单"),
    MENU_QUEUE("排队"),
    MENU_RESERVATION("预定"),
    MENU_SAVE_INTEGRAL_RULE("积分规则"),
    MENU_DATA_SETTING("数据取值设置"),
    MENU_SENSITIVE_OPERATION_RESTRICTIONS("敏感操作限制"),
    ;


    private final String message;

    MerchantMenuLocaleEnum(String message){
        this.message = message;
    }

    private final static Map<String,MerchantMenuLocaleEnum> LOCALE_MAP;

    static {
        LOCALE_MAP = initMap();

    }

    private static Map<String,MerchantMenuLocaleEnum> initMap(){
        Map<String,MerchantMenuLocaleEnum> localeMap = Maps.newHashMap();
        for (MerchantMenuLocaleEnum merchantMenuLocaleEnum : MerchantMenuLocaleEnum.values()){
            localeMap.put(merchantMenuLocaleEnum.message,merchantMenuLocaleEnum);
        }
        return localeMap;
    }

    public static String getLocale(String message){
        MerchantMenuLocaleEnum merchantMenuLocaleEnum = LOCALE_MAP.get(message);
        return merchantMenuLocaleEnum == null ? message : getLocaleMessage(merchantMenuLocaleEnum);
    }

    private static String getLocaleMessage(MerchantMenuLocaleEnum merchantMenuLocaleEnum) {
        try {
            ResourceBundle bundle = ResourceBundle.getBundle("i18n/messages", LocaleContextHolder.getLocale());
            return bundle.getString(merchantMenuLocaleEnum.name());
        } catch (Exception e) {
            return merchantMenuLocaleEnum.getMessage();
        }
    }
}
