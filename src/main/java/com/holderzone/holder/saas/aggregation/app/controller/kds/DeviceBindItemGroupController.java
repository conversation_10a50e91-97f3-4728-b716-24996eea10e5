package com.holderzone.holder.saas.aggregation.app.controller.kds;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.kds.DeviceBindItemGroupRpcService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.kds.req.DstItemQueryReqDTO;
import com.holderzone.saas.store.dto.kds.req.ItemGroupReqDTO;
import com.holderzone.saas.store.dto.kds.req.PrdPointItemQueryReqDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayRepeatItemRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DstTypeBindRespDTO;
import com.holderzone.saas.store.dto.kds.resp.ItemGroupRespDTO;
import com.holderzone.saas.store.dto.kds.resp.PointTypeBindRespDTO;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * KDS菜品绑定分组
 */
@Slf4j
@RestController
@RequestMapping("/kds_device_bind_item_group")
@AllArgsConstructor
public class DeviceBindItemGroupController {

    private final DeviceBindItemGroupRpcService deviceBindItemGroupRpcService;

    @PostMapping("/query/allow_repeat_flag")
    @ApiOperation(value = "查询KDS菜品绑定配置")
    public Result<DisplayRepeatItemRespDTO> queryRepeatItemByStore(@RequestBody BaseDTO baseDTO) {
        return Result.buildSuccessResult(deviceBindItemGroupRpcService.queryRepeatItemByStore(baseDTO.getStoreGuid()));
    }

    @ApiOperation(value = "查询KDS制作口绑定商品列表")
    @PostMapping("/prd/item/list")
    public Result<List<PointTypeBindRespDTO>> queryPrdBindItem(@RequestBody PrdPointItemQueryReqDTO prdPointItemQueryReqDTO) {
        return Result.buildSuccessResult(deviceBindItemGroupRpcService.queryPrdBindItem(prdPointItemQueryReqDTO));
    }

    @ApiOperation(value = "查询KDS出堂口绑定商品列表")
    @PostMapping("/dst/item/list")
    public Result<List<DstTypeBindRespDTO>> queryDstBindItem(@RequestBody DstItemQueryReqDTO dstItemQueryReqDTO) {
        return Result.buildSuccessResult(deviceBindItemGroupRpcService.queryDstBindItem(dstItemQueryReqDTO));
    }

    @ApiOperation(value = "菜品分组列表")
    @PostMapping("/group/list")
    public Result<List<ItemGroupRespDTO>> queryGroupList(@RequestBody PrdPointItemQueryReqDTO queryReqDTO) {
        return Result.buildSuccessResult(deviceBindItemGroupRpcService.queryGroupList(queryReqDTO));
    }

    @ApiOperation(value = "菜品分组列表(当前设备已绑定)")
    @PostMapping("/group/myself/list")
    public Result<List<ItemGroupRespDTO>> queryMyselfGroupList(@RequestBody PrdPointItemQueryReqDTO queryReqDTO) {
        return Result.buildSuccessResult(deviceBindItemGroupRpcService.queryMyselfGroupList(queryReqDTO));
    }

    @ApiOperation(value = "新增菜品分组")
    @PostMapping("/group/save")
    public Result<Void> saveGroup(@RequestBody @Validated ItemGroupReqDTO reqDTO) {
        log.info("新增菜品分组入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        String lockKey = String.format("kds_bind_item_save_group:%s", UserContextUtils.getStoreGuid());
        try {
            boolean lockResult = RedissonLockUtil.tryLock(lockKey, 3, 10);
            if (!lockResult) {
                throw new BusinessException("新增菜品分组中,请稍后再试");
            }
            deviceBindItemGroupRpcService.saveGroup(reqDTO);
            return Result.buildEmptySuccess();
        } catch (Throwable throwable) {
            log.error("新增菜品分组异常 e:{}", throwable.getMessage());
            throw new BusinessException(throwable.getMessage());
        } finally {
            try {
                RedissonLockUtil.unlock(lockKey);
            } catch (Exception e) {
                log.info("并发解锁异常 e:", e);
            }
        }
    }

    @ApiOperation(value = "编辑菜品分组(菜品新增)")
    @PostMapping("/group/update")
    public Result<Void> updateGroup(@RequestBody ItemGroupReqDTO reqDTO) {
        log.info("编辑菜品分组入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        String lockKey = String.format("kds_bind_item_update_group:%s", reqDTO.getGroupGuid());
        try {
            boolean lockResult = RedissonLockUtil.tryLock(lockKey, 3, 10);
            if (!lockResult) {
                throw new BusinessException("编辑菜品分组中,请稍后再试");
            }
            deviceBindItemGroupRpcService.updateGroup(reqDTO);
            return Result.buildEmptySuccess();
        } catch (Throwable throwable) {
            log.error("编辑菜品分组异常 e:{}", throwable.getMessage());
            throw new BusinessException(throwable.getMessage());
        } finally {
            try {
                RedissonLockUtil.unlock(lockKey);
            } catch (Exception e) {
                log.info("并发解锁异常 e:", e);
            }
        }
    }

    @ApiOperation(value = "删除菜品分组")
    @DeleteMapping("/group/remove/{groupGuid}")
    public Result<Void> removeGroup(@PathVariable("groupGuid") String groupGuid) {
        log.info("删除菜品分组入参：{}", groupGuid);
        deviceBindItemGroupRpcService.removeGroup(groupGuid);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "删除菜品分组下绑定商品")
    @DeleteMapping("/group/remove/{groupGuid}/{skuGuid}")
    public Result<Void> removeGroupItem(@PathVariable("groupGuid") String groupGuid,
                                        @PathVariable("skuGuid") String skuGuid) {
        log.info("删除菜品分组下绑定商品入参：groupGuid:{}, skuGuid:{}", groupGuid, skuGuid);
        deviceBindItemGroupRpcService.removeGroupItem(groupGuid, skuGuid);
        return Result.buildEmptySuccess();
    }

}
