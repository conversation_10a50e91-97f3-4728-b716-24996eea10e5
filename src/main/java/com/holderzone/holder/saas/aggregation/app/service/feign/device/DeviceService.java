package com.holderzone.holder.saas.aggregation.app.service.feign.device;

import com.holderzone.resource.common.dto.device.DeviceRequestLogDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2021/5/7 17:18
 */
@Component
@FeignClient(name = "holder-saas-cloud-device", fallbackFactory = DeviceService.FallbackFactoryImpl.class)
public interface DeviceService {


    @PostMapping("/device/request/log")
    String addRequestLog(@RequestBody DeviceRequestLogDTO requestLogDTO);

    @Slf4j
    @Component
    class FallbackFactoryImpl implements FallbackFactory<DeviceService> {
        private static final String HYSTRIX_PATTERN = "调用Device服务熔断";

        @Override
        public DeviceService create(Throwable throwable) {
            return new DeviceService() {
                @Override
                public String addRequestLog(DeviceRequestLogDTO requestLogDTO) {
                    log.error(HYSTRIX_PATTERN + "错误原因 throwable:{}", throwable.getMessage());
                    return null;
                }
            };

        }
    }
}
