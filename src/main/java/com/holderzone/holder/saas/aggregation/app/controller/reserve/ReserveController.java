package com.holderzone.holder.saas.aggregation.app.controller.reserve;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.app.anno.ApiVersionControl;
import com.holderzone.holder.saas.aggregation.app.constant.Constant;
import com.holderzone.holder.saas.aggregation.app.service.ReserveService;
import com.holderzone.holder.saas.aggregation.app.service.feign.reserve.ReserveClient;
import com.holderzone.holder.saas.aggregation.app.service.feign.table.TableClientService;
import com.holderzone.holder.saas.aggregation.app.utils.BigDecimalUtil;
import com.holderzone.holder.saas.aggregation.app.utils.RedisLock;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.reserve.ReserveRecordGuidDTO;
import com.holderzone.saas.store.dto.reserve.TableDTO;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.reserve.api.ReserveConfigApi;
import com.holderzone.saas.store.reserve.api.ReservePaymentApi;
import com.holderzone.saas.store.reserve.api.ReserveRecordApi;
import com.holderzone.saas.store.reserve.api.ReserveTableApi;
import com.holderzone.saas.store.reserve.api.dto.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.maven.surefire.shared.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveController
 * @date 2019/05/09 10:00
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Slf4j
@RestController
public class ReserveController {

    @Autowired
    ReserveRecordApi reserveRecordApi;

    @Autowired
    ReserveTableApi reserveTableApi;

    @Autowired
    ReserveConfigApi reserveConfigApi;

    @Autowired
    ReserveService reserveService;

    @Autowired
    ReservePaymentApi reservePaymentApi;

    @Autowired
    TableClientService tableClientService;

    @Autowired
    private RedisLock redisLock;

    @ApiOperation("查询预定列表")
    @PostMapping("/reserve/query")
    public Result<Collection<ReserveRecordLessDTO>> query(@RequestBody ReserveRecordQueryDTO queryDTO) {
        Collection<ReserveRecordLessDTO> lessDTOS = reserveRecordApi.query(queryDTO);
        lessDTOS.forEach(less -> {
            if (BigDecimalUtil.greaterThanZero(less.getReserveRefundAmount())) {
                less.setReserveAmount(less.getReserveAmount().add(less.getReserveRefundAmount()));
            }
        });
        return Result.buildSuccessResult(lessDTOS);
    }

    @ApiOperation("发起预定")
    @PostMapping("/reserve/launch")
    @ApiVersionControl
    public Result<ReserveRecordDTO> launch(@RequestBody ReserveRecordDTO reserveRecordDTO) {
        log.info("一体机发起预定入参:{}", JacksonUtils.writeValueAsString(reserveRecordDTO));
        return Result.buildSuccessResult(reserveService.launch(reserveRecordDTO));
    }

    @ApiOperation("修改预定")
    @PostMapping("/reserve/modify")
    @ApiVersionControl
    public Result<ReserveRecordDetailDTO> modify(@RequestBody ReserveRecordDTO reserveRecordDTO) {
        log.info("一体机修改预定入参:{}", JacksonUtils.writeValueAsString(reserveRecordDTO));
        return Result.buildSuccessResult(reserveRecordApi.modify(reserveRecordDTO));
    }

    @ApiOperation("审核通过")
    @PostMapping("/reserve/pass")
    @ApiVersionControl
    public Result<ReserveRecordDetailDTO> pass(@RequestBody ReserveRecordGuidDTO guidDTO) {
        log.info("一体机预定审核通过入参:{}", JacksonUtils.writeValueAsString(guidDTO));
        return Result.buildSuccessResult(reserveRecordApi.pass(guidDTO));
    }

    @ApiOperation("查询指定预订记录详细信息")
    @PostMapping("/reserve/obtain")
    @ApiVersionControl
    public Result<ReserveRecordDetailDTO> obtain(@RequestBody ReserveRecordGuidDTO guidDTO) {
        ReserveRecordDetailDTO detailDTO = reserveRecordApi.obtain(guidDTO);
        if (BigDecimalUtil.greaterThanZero(detailDTO.getReserveRefundAmount())) {
            detailDTO.setReserveAmount(detailDTO.getReserveAmount().add(detailDTO.getReserveRefundAmount()));
        }
        return Result.buildSuccessResult(detailDTO);
    }

    @ApiOperation("查询预定记录详细信息(包含关联赚餐门店信息)")
    @GetMapping("/reserve/obtain/applet")
    public Result<ReserveRecordDetailDTO> obtainApplet(@RequestParam("guid") String guid) {
        ReserveRecordDetailDTO detailDTO = reserveRecordApi.obtainApplet(guid);
        if (BigDecimalUtil.greaterThanZero(detailDTO.getReserveRefundAmount())) {
            detailDTO.setReserveAmount(detailDTO.getReserveAmount().add(detailDTO.getReserveRefundAmount()));
        }
        return Result.buildSuccessResult(detailDTO);
    }

    @ApiOperation("取消预定")
    @PostMapping("/reserve/cancle")
    @ApiVersionControl
    public Result<ReserveRecordDetailDTO> cancle(@RequestBody ReserveRecordGuidDTO guidDTO) {
        log.info("一体机取消预定入参:{}", JacksonUtils.writeValueAsString(guidDTO));
        if (StringUtils.hasText(guidDTO.getMainOrderGuid())) {
            guidDTO.setOrderGuid(guidDTO.getMainOrderGuid());
            SingleDataDTO dto = new SingleDataDTO();
            dto.setData(guidDTO.getMainOrderGuid());
            List<TableBasicDTO> tableBasicDTOList = tableClientService.queryCombineListByMainOrder(dto);
            if (!CollectionUtils.isEmpty(tableBasicDTOList)) {
                List<TableDTO> tables = Lists.newArrayList();
                tableBasicDTOList.forEach(t -> {
                    TableDTO tableDTO = new TableDTO();
                    tableDTO.setGuid(t.getGuid());
                    tableDTO.setName(t.getTableCode());
                    tableDTO.setAreaGuid(t.getAreaGuid());
                    tableDTO.setAreaName(t.getAreaName());
                    tables.add(tableDTO);
                });
                guidDTO.setTables(tables);
            }
        }
        return Result.buildSuccessResult(reserveRecordApi.cancle(guidDTO));
    }

    @ApiOperation("到店开台")
    @PostMapping("/reserve/open")
    public Result<ReserveRecordDetailDTO> open(@RequestBody ReserveRecordGuidDTO guidDTO) {
        log.info("一体机预定到店开台入参:{}", JacksonUtils.writeValueAsString(guidDTO));
        return Result.buildSuccessResult(reserveRecordApi.open(guidDTO));
    }

    @ApiOperation("到店选台")
    @PostMapping("/reserve/compensate")
    public Result<ReserveRecordDetailDTO> compensate(@RequestBody CompensateDTO guidDTO) {
        log.info("一体机预定到店选台入参:{}", JacksonUtils.writeValueAsString(guidDTO));
        return Result.buildSuccessResult(reserveRecordApi.compensate(guidDTO));
    }

    @ApiOperation("查询对应桌台")
    @PostMapping("/reserve/table/query")
    public Result<Collection<TableQueryResultDTO>> query(@RequestBody TableQueryDTO queryDTO) {
        return Result.buildSuccessResult(reserveTableApi.query(queryDTO));
    }

    @ApiOperation("查询对应门店")
    @PostMapping("/reserve/config")
    public Result<List<TimingSegmentDTO>> config(@RequestBody StoreGuidDTO storeGuid) {
        List<TimingSegmentDTO> sub = reserveConfigApi.query(storeGuid).getSub();
        sub = sub.stream().distinct().collect(Collectors.toList());
        return Result.buildSuccessResult(sub);
    }

    @ApiOperation("支付reserver")
    @PostMapping("/reserve/pay")
    public Result<ReservePayRespDTO> pay(@RequestBody ReservePayReqDTO reservePayReqDTO) {
        log.info("一体机预定支付入参:{}", JacksonUtils.writeValueAsString(reservePayReqDTO));
        // 防重复提交
        boolean getLock;
        ReservePayRespDTO payRespDTO = new ReservePayRespDTO();
        try {
            // 判断是否获取了锁
            getLock = redisLock.lock(JacksonUtils.writeValueAsString(reservePayReqDTO));
            if (getLock) {
                handleReserveParam(reservePayReqDTO);
                payRespDTO = reservePaymentApi.pay(reservePayReqDTO);
            }
        } finally {
            redisLock.delete(JacksonUtils.writeValueAsString(reservePayReqDTO));
        }
        if (getLock) {
            return Result.buildSuccessResult(payRespDTO);
        } else {
            return Result.buildOpFailedResult(Constant.RESERVE_PAYING);
        }
    }

    private void handleReserveParam(ReservePayReqDTO reservePayReqDTO) {
        if (StringUtils.hasText(reservePayReqDTO.getMainOrderGuid())) {
            reservePayReqDTO.setOrderGuid(reservePayReqDTO.getMainOrderGuid());
            SingleDataDTO dto = new SingleDataDTO();
            dto.setData(reservePayReqDTO.getMainOrderGuid());
            List<TableBasicDTO> tableBasicDTOList = tableClientService.queryCombineListByMainOrder(dto);
            if (!CollectionUtils.isEmpty(tableBasicDTOList)) {
                List<TableDTO> tables = Lists.newArrayList();
                tableBasicDTOList.forEach(t -> {
                    TableDTO tableDTO = new TableDTO();
                    tableDTO.setGuid(t.getGuid());
                    tableDTO.setName(t.getTableCode());
                    tableDTO.setAreaGuid(t.getAreaGuid());
                    tableDTO.setAreaName(t.getAreaName());
                    tables.add(tableDTO);
                });
                reservePayReqDTO.setTables(tables);
            }
        }
    }


    @ApiOperation("预点餐商品打印")
    @PostMapping("/reserve/pre_ordering/print")
    public Result<String> printPreOrdering(@RequestBody ReservePrintDTO reservePrintDTO) {
        log.info("预点餐商品打印入参:{}", JacksonUtils.writeValueAsString(reservePrintDTO));
        reserveTableApi.printPreOrdering(reservePrintDTO);
        return Result.buildSuccessResult("打印成功");
    }

    @ApiOperation("根据订单打印预付金信息")
    @PostMapping("/reserve/printByOrderGuid")
    public Result<String> printByOrderGuid(@RequestBody @Validated SingleDataDTO singleDataDTO) {
        log.info("根据订单打印预付金信息入参:{}", JacksonUtils.writeValueAsString(singleDataDTO));
        reserveRecordApi.printByOrderGuid(singleDataDTO);
        return Result.buildSuccessResult("打印成功");
    }
}
