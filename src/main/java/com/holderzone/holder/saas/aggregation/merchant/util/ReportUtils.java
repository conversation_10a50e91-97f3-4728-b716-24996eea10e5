package com.holderzone.holder.saas.aggregation.merchant.util;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.framework.util.JacksonUtils;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReportUtils
 * @date 2018/10/17 19:08
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Slf4j
public final class ReportUtils {

    public ReportUtils() {

    }

    public static String getParamString(HttpServletRequest request) {
        String param = null;
        BufferedReader streamReader = null;
        try {
            streamReader = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));
            StringBuilder responseStrBuilder = new StringBuilder();
            String inputStr;
            while ((inputStr = streamReader.readLine()) != null) {
                responseStrBuilder.append(inputStr);
            }
            JSONObject jsonObject = JSONObject.parseObject(responseStrBuilder.toString());
            if (jsonObject != null) {
                param = jsonObject.toJSONString();
            }
        } catch (Exception e) {
            log.info("发生错误：{}", e.getMessage());
        } finally {
            if (streamReader != null) {
                try {
                    streamReader.close();
                } catch (IOException e) {
                    log.info("发生错误：{}", e.getMessage());
                }
            }
        }
        return param;
    }
}
