package com.holderzone.holder.saas.aggregation.merchant.service.rpc.baseresource;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.dto.baseresource.QueryStoreDTO;
import com.holderzone.holder.saas.member.dto.baseresource.request.HsmStoreReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.request.ShopQueryReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.response.HsmStoreRespDTO;
import com.holderzone.holder.saas.member.dto.common.response.StoreOptionRespDTO;

import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @description
 * @date 2019/5/24 16:45
 */
@Component
@FeignClient(name = "holder-saas-member-account")
public interface StoreClientService {
    /**
     * 查询门店选项
     * @param queryStoreDTO
     * @return
     */
    @GetMapping(value = "/hsm-store/queryOptionStoreInfo", produces = MediaType.APPLICATION_JSON_UTF8_VALUE )
    Page<StoreOptionRespDTO>queryOptionStoreInfo(QueryStoreDTO queryStoreDTO);
    /**
     * 根据分页进行查询
     *
     * @param shopQueryReqDTO 条件
     * @return 分页的结果
     */
    @PostMapping(value="/hsm/store/query/page", produces = MediaType.APPLICATION_JSON_UTF8_VALUE )
    Page<HsmStoreRespDTO> findPageByCondition(@RequestBody ShopQueryReqDTO shopQueryReqDTO);
    /**
     * 根据查询条件进行列表查询
     *
     * @param shopQueryReqDTO 条件
     * @return 全列表
     */
    @PostMapping(value="/hsm/store/query/list", produces = MediaType.APPLICATION_JSON_UTF8_VALUE )
    List<HsmStoreRespDTO> findListByCondition(@RequestBody ShopQueryReqDTO shopQueryReqDTO);

    /**
     * 同步门店
     *
     * @param hsmStoreReqDTOS 列表
     */
    @PostMapping(value="/hsm/store/syc/list", produces = MediaType.APPLICATION_JSON_UTF8_VALUE )
    List<HsmStoreRespDTO> sycList(@RequestBody List<HsmStoreReqDTO> hsmStoreReqDTOS);

    /**
     * 同步门店
     *
     * @param hsmStoreReqDTO
     */
    @PostMapping(value="/hsm/store/syc", produces = MediaType.APPLICATION_JSON_UTF8_VALUE )
    HsmStoreRespDTO syc(@RequestBody  HsmStoreReqDTO hsmStoreReqDTO);


    /**
     * 保存门店
     * @param hsmStoreReqDTO
     * @return
     */
    @PostMapping(value="/hsm/store/save", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    boolean saveHsmStore(@RequestBody HsmStoreReqDTO hsmStoreReqDTO);

    /**
     * 修改门店
     * @param hsmStoreReqDTO
     * @return
     */
    @PostMapping(value="/hsm/store/modify", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    boolean modifyHsmStore(@RequestBody @Validated HsmStoreReqDTO hsmStoreReqDTO);

    /**
     * 删除门店
     * @param storeKey
     * @param allianceid
     * @return
     */
    @DeleteMapping("/hsm/store/{storeKey}/{allianceid}")
    boolean removeHsmStore(@ApiParam("storeKey") @PathVariable("storeKey") String storeKey,
                                  @ApiParam("allianceid") @PathVariable("allianceid") String allianceid);

    @Slf4j
    @Component
    class StoreClientServiceServiceFallBack  implements FallbackFactory<StoreClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";
        @Override
        public  StoreClientService create(Throwable throwable) {
            return new StoreClientService(){
                @Override
                public Page<StoreOptionRespDTO> queryOptionStoreInfo(QueryStoreDTO queryStoreDTO) {
                    log.error(HYSTRIX_PATTERN, "queryOptionStoreInfo", JacksonUtils.writeValueAsString(queryStoreDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询门店选项失败");
                }

                @Override
                public Page<HsmStoreRespDTO> findPageByCondition(ShopQueryReqDTO shopQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "findPageByCondition", JacksonUtils.writeValueAsString(shopQueryReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("根据分页进行门店查询");
                }

                @Override
                public List<HsmStoreRespDTO> findListByCondition(ShopQueryReqDTO shopQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "findListByCondition", JacksonUtils.writeValueAsString(shopQueryReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("根据查询条件进行门店列表查询");
                }

                @Override
                public List<HsmStoreRespDTO> sycList(List<HsmStoreReqDTO> hsmStoreReqDTOS) {
                    log.error(HYSTRIX_PATTERN, "sycList", JacksonUtils.writeValueAsString(hsmStoreReqDTOS), ThrowableUtils.asString(throwable));
                    throw new BusinessException("批量同步门店失败");
                }

                @Override
                public HsmStoreRespDTO syc(HsmStoreReqDTO hsmStoreReqDTO) {
                    log.error(HYSTRIX_PATTERN, "syc", JacksonUtils.writeValueAsString(hsmStoreReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("同步门店失败");
                }

                @Override
                public boolean saveHsmStore(HsmStoreReqDTO hsmStoreReqDTO) {
                    log.error(HYSTRIX_PATTERN, "saveHsmStore", JacksonUtils.writeValueAsString(hsmStoreReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("保存门店失败");
                }

                @Override
                public boolean modifyHsmStore(HsmStoreReqDTO hsmStoreReqDTO) {
                    log.error(HYSTRIX_PATTERN, "modifyHsmStore", JacksonUtils.writeValueAsString(hsmStoreReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("修改门店失败");
                }

                @Override
                public boolean removeHsmStore(String storeKey, String allianceid) {
                    log.error(HYSTRIX_PATTERN, "removeHsmStore", JacksonUtils.writeValueAsString(storeKey+" "+allianceid), ThrowableUtils.asString(throwable));
                    throw new BusinessException("删除门店失败");
                }
            };
        }

    }
}
