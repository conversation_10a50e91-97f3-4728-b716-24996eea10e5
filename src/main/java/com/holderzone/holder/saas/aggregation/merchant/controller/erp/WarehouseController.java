package com.holderzone.holder.saas.aggregation.merchant.controller.erp;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp.WarehouseFeignService;
import com.holderzone.saas.store.dto.erp.WarehouseDTO;
import com.holderzone.saas.store.dto.erp.WarehouseQueryDTO;
import com.holderzone.saas.store.dto.erp.WarehouseReqDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @className WarehouseController
 * @date 2019-04-30 16:08:44
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Api(tags = "仓库管理")
@RestController
public class WarehouseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(WarehouseController.class);
    private final WarehouseFeignService warehouseFeignService;

    @Autowired
    public WarehouseController(WarehouseFeignService warehouseFeignService) {
        this.warehouseFeignService = warehouseFeignService;
    }

    @ApiOperation(value = "创建仓库")
    @PostMapping("/warehouse/create")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "创建仓库")
    public Result<String> createWarehouse(@RequestBody WarehouseReqDTO reqDTO) {
        LOGGER.info("ERP系统聚合层(创建仓库)-> WarehouseReqDTO:{}", JacksonUtils.writeValueAsString(reqDTO));
        return Result.buildSuccessResult(warehouseFeignService.createWarehouse(reqDTO));
    }

    @ApiOperation(value = "更新仓库")
    @PostMapping("/warehouse/update")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "更新仓库")
    public Result<String> updateWarehouse(@RequestBody WarehouseReqDTO reqDTO) {
        LOGGER.info("ERP系统聚合层(更新仓库)-> WarehouseReqDTO:{}", JacksonUtils.writeValueAsString(reqDTO));
        return Result.buildSuccessResult(warehouseFeignService.updateWarehouse(reqDTO));
    }

    @ApiOperation(value = "查询仓库信息")
    @PostMapping("/warehouse/query/one/{guid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "查询仓库信息")
    public Result<WarehouseDTO> getWarehouseByGuid(@PathVariable("guid") String guid) {
        LOGGER.info("ERP系统聚合层(查询仓库信息)-> guid:{}", guid);
        return Result.buildSuccessResult(warehouseFeignService.getWarehouseByGuid(guid));
    }

    @ApiOperation(value = "查询仓库列表")
    @PostMapping("/warehouse/query/list")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "查询仓库列表")
    public Result<Page<WarehouseDTO>> getWarehouseList(@RequestBody WarehouseQueryDTO queryDTO) {
        LOGGER.info("ERP系统聚合层(查询仓库列表)-> WarehouseQueryDTO:{}", JacksonUtils.writeValueAsString(queryDTO));
        return Result.buildSuccessResult(warehouseFeignService.getWarehouseList(queryDTO));
    }

    @ApiOperation(value = "仓库下拉列表")
    @PostMapping("/warehouse/name")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "仓库下拉列表")
    public Result<List<WarehouseDTO>> getWarehouseListByName(@RequestBody WarehouseQueryDTO queryDTO) {
        LOGGER.info("ERP系统聚合层(仓库下拉列表)-> name:{}", JacksonUtils.writeValueAsString(queryDTO));
        List<WarehouseDTO> list = warehouseFeignService.getWarehouseListByName(queryDTO);
        WarehouseDTO warehouseDTO = new WarehouseDTO();
        warehouseDTO.setName("全部仓库");
        warehouseDTO.setGuid("all");
        if (list == null) {
            list = new ArrayList<>();
        }
        list.add(0, warehouseDTO);
        return Result.buildSuccessResult(list);
    }

    @ApiOperation(value = "启禁用仓库")
    @PostMapping("/warehouse/enable/{guid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "启禁用仓库")
    public Result<Boolean> enableOrDisableWarehouse(@PathVariable("guid") String guid) {
        LOGGER.info("ERP系统聚合层(启禁用仓库)-> guid:{}", guid);
        return Result.buildSuccessResult(warehouseFeignService.enableOrDisableWarehouse(guid));
    }

    @ApiOperation(value = "仓库解锁或锁定")
    @PostMapping("/warehouse/lock/{guid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "仓库解锁或锁定")
    public Result<Boolean> lockOrUnlockWarehouse(@PathVariable("guid") String guid) {
        LOGGER.info("ERP系统聚合层(仓库解锁或锁定)-> guid:{}", guid);
        return Result.buildSuccessResult(warehouseFeignService.lockOrUnlockWarehouse(guid));
    }

    @ApiOperation(value = "删除仓库", notes = "此方法暂不对外开放")
    @PostMapping("/warehouse/delete/{guid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "删除仓库")
    public Result<Boolean> deleteWarehouse(@PathVariable("guid") String guid) {
        LOGGER.info("ERP系统聚合层(删除仓库)-> guid:{}", guid);
        return Result.buildSuccessResult(warehouseFeignService.deleteWarehouse(guid));
    }

    @ApiOperation(value = "生产仓库编号")
    @PostMapping("/warehouse/code")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "生产仓库编号")
    public Result<String> code() {
        return Result.buildSuccessResult(warehouseFeignService.warehouseCode());
    }
}
