package com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.WarehouseDTO;
import com.holderzone.saas.store.dto.erp.WarehouseQueryDTO;
import com.holderzone.saas.store.dto.erp.WarehouseReqDTO;
import feign.hystrix.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @className WarehouseFeignService
 * @date 2019-05-05 10:17:50
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-store-erp", fallbackFactory = WarehouseFeignService.WarehouseFeignServiceFallback.class)
public interface WarehouseFeignService {

    /**
     * 创建仓库
     */
    @PostMapping("/warehouse")
    String createWarehouse(@RequestBody WarehouseReqDTO reqDTO);

    /**
     * 更新仓库
     */
    @PutMapping("/warehouse")
    String updateWarehouse(@RequestBody WarehouseReqDTO reqDTO);

    /**
     * 查询仓库信息
     */
    @GetMapping("/warehouse")
    WarehouseDTO getWarehouseByGuid(@RequestParam("guid") String guid);

    /**
     * 查询仓库列表
     */
    @PostMapping("/warehouse/list")
    Page<WarehouseDTO> getWarehouseList(@RequestBody WarehouseQueryDTO queryDTO);

    /**
     * 仓库下拉列表
     */
    @PostMapping("/warehouse/name")
    List<WarehouseDTO> getWarehouseListByName(@RequestBody WarehouseQueryDTO queryDTO);

    /**
     * 启禁用仓库
     */
    @PutMapping("/warehouse/enable/{guid}")
    Boolean enableOrDisableWarehouse(@PathVariable("guid") String guid);

    /**
     * 仓库解锁或锁定
     */
    @PutMapping("/warehouse/lock/{guid}")
    Boolean lockOrUnlockWarehouse(@PathVariable("guid") String guid);

    /**
     * 删除仓库
     */
    @DeleteMapping("/warehouse/delete/{guid}")
    Boolean deleteWarehouse(@PathVariable("guid") String guid);

    /**
     * 生产仓库编号
     */
    @GetMapping("/warehouse/code")
    String warehouseCode();

    @Component
    class WarehouseFeignServiceFallback implements FallbackFactory<WarehouseFeignService> {

        @Override
        public WarehouseFeignService create(Throwable throwable) {
            return new WarehouseFeignService() {
                @Override
                public String createWarehouse(WarehouseReqDTO reqDTO) {
                    return null;
                }

                @Override
                public String updateWarehouse(WarehouseReqDTO reqDTO) {
                    return null;
                }

                @Override
                public WarehouseDTO getWarehouseByGuid(String guid) {
                    return null;
                }

                @Override
                public Page<WarehouseDTO> getWarehouseList(WarehouseQueryDTO queryDTO) {
                    return null;
                }

                @Override
                public List<WarehouseDTO> getWarehouseListByName(WarehouseQueryDTO queryDTO) {
                    return null;
                }

                @Override
                public Boolean enableOrDisableWarehouse(String guid) {
                    return null;
                }

                @Override
                public Boolean lockOrUnlockWarehouse(String guid) {
                    return null;
                }

                @Override
                public Boolean deleteWarehouse(String guid) {
                    return null;
                }

                @Override
                public String warehouseCode() {
                    return null;
                }
            };
        }
    }
}
