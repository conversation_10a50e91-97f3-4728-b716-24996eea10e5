package com.holderzone.holder.saas.aggregation.app.controller.trade;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.constant.Constant;
import com.holderzone.holder.saas.aggregation.app.service.UploadService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.OrderRefundClientService;
import com.holderzone.saas.store.dto.order.request.bill.*;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;


/**
 * 订单退款 前端控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/dine_in_bill/order/refund")
public class OrderRefundController {

    private final OrderRefundClientService orderRefundClientService;

    private final UploadService uploadService;

    @ApiOperation(value = "查询可退款的订单详情")
    @GetMapping("/available/{orderGuid}")
    public Result<DineinOrderDetailRespDTO> getAvailableRefundDetail(@PathVariable String orderGuid) {
        log.info("查询可退款的订单详情入参：{}", JacksonUtils.writeValueAsString(orderGuid));
        return Result.buildSuccessResult(orderRefundClientService.getAvailableRefundDetail(orderGuid));
    }

    @ApiOperation(value = "订单退款")
    @PostMapping
    public Result<String> orderRefund(@RequestBody OrderRefundReqDTO orderRefundReqDTO) {
        log.info("订单退款入参：{}", JacksonUtils.writeValueAsString(orderRefundReqDTO));
        // 校验接口版本号
        validateVersion(orderRefundReqDTO.getVersion());
        // 上传图片
        orderRefundReqDTO.setPicture(uploadService.upload(orderRefundReqDTO.getPicture()));
        if (StringUtils.isEmpty(orderRefundReqDTO.getPicture())) {
            if (StringUtils.isEmpty(orderRefundReqDTO.getAuthStaffGuid())) {
                orderRefundReqDTO.setAuthStaffGuid("");
            }
            if (StringUtils.isEmpty(orderRefundReqDTO.getAuthStaffName())) {
                orderRefundReqDTO.setAuthStaffName("");
            }
        }
        return Result.buildSuccessResult(orderRefundClientService.orderRefund(orderRefundReqDTO));
    }

    @ApiOperation(value = "订单部分退款是否超时", notes = "订单部分退款是否超时")
    @PostMapping("/time_limit")
    public Result<Boolean> refundTimeLimit(@RequestBody OrderRefundReqDTO orderRefundReqDTO) {
        log.info("订单部分退款是否超时入参：{}", JacksonUtils.writeValueAsString(orderRefundReqDTO));
        return Result.buildSuccessResult(orderRefundClientService.refundTimeLimit(orderRefundReqDTO));
    }

    /**
     * 校验一体机版本
     */
    private void validateVersion(String version) {
        if (StringUtils.isEmpty(version) || !Constant.REFUND_INTERFACE_VERSION.equals(version)) {
            throw new BusinessException("一体机请更新到最新版本");
        }
    }

}
