package com.holderzone.holder.saas.aggregation.merchant.controller.openapi;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.ReportService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.HsmMemberDataCenterClientService;
import com.holderzone.holder.saas.aggregation.merchant.util.MemberResult;
import com.holderzone.saas.store.dto.report.openapi.MemberFundingDetailLimitOpenRespDTO;
import com.holderzone.saas.store.dto.report.openapi.MemberFundingDetailQueryDTO;
import com.holderzone.saas.store.dto.report.openapi.MemberRegisterReqDTO;
import com.holderzone.saas.store.dto.report.openapi.MemberRegisterRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


/**
 * 会员开放接口
 */
@Slf4j
@RestController
@RequestMapping("/openapi/member")
@RequiredArgsConstructor
public class OpenMemberController {

    private final HsmMemberDataCenterClientService memberDataCenterClientService;

    private final ReportService reportService;

    @PostMapping(value = "/register")
    public Result<MemberRegisterRespDTO> registerMemberAndCardOpen(@RequestBody @Valid MemberRegisterReqDTO memberRegisterReqDTO) {
        log.info("注册会员and开通卡入参：{}", JacksonUtils.writeValueAsString(memberRegisterReqDTO));
        UserContext userContext = UserContextUtils.get();
        userContext.setEnterpriseGuid(memberRegisterReqDTO.getEnterpriseGuid());
        userContext.setOperSubjectGuid(memberRegisterReqDTO.getOperSubjectGuid());
        UserContextUtils.put(userContext);
        MemberResult<MemberRegisterRespDTO> result = memberDataCenterClientService
                .registerMemberAndCardOpen(memberRegisterReqDTO);
        return Result.buildSuccessResult(result.getTData());
    }

    @PostMapping(value = "/report/funding_detail")
    public Result<MemberFundingDetailLimitOpenRespDTO<?>> queryFundingDetail(@RequestBody @Valid MemberFundingDetailQueryDTO memberFundingDetailQueryDTO) {
        log.info("查询会员资金变动明细入参：{}", JacksonUtils.writeValueAsString(memberFundingDetailQueryDTO));
        UserContext userContext = UserContextUtils.get();
        userContext.setEnterpriseGuid(memberFundingDetailQueryDTO.getEnterpriseGuid());
        userContext.setOperSubjectGuid(memberFundingDetailQueryDTO.getOperSubjectGuid());
        UserContextUtils.put(userContext);
        return Result.buildSuccessResult(reportService.queryMemberFundingDetail(memberFundingDetailQueryDTO));
    }
}
