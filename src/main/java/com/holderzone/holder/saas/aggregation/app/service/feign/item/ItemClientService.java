package com.holderzone.holder.saas.aggregation.app.service.feign.item;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemPadCalculateDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.req.estimate.EstimateForAndroidReqDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateForAndroidRespDTO;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.print.PrintItemReqDto;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemClientService
 * @date 2018/09/07 下午4:29
 * @description
 * @program holder-saas-store-item
 */
@Component
@FeignClient(name = "holder-saas-store-item", fallbackFactory = ItemClientService.ItemFallBack.class)
public interface ItemClientService {

    /**
     * 一体机预点餐
     *
     * @param preOrderValidateReq preOrderValidateReq
     * @return PreOrderValidateRespDTO
     */
    @PostMapping("/plan/preOrderVerification")
    PreOrderValidateRespDTO preOrderVerification(@RequestBody PreOrderValidateReq preOrderValidateReq);

    @PostMapping("/item/query_for_synchronize")
    ItemAndTypeForAndroidRespDTO selectItemAndTypeForSyn(@RequestBody BaseDTO baseDTO);

    @PostMapping("/item/selectTypeItemList")
    List<TypeItemListDTO> selectTypeItemList(@RequestBody PrintItemReqDto printItemReqDto);

    /**
     * 安卓一体机同步菜品估清
     */
    @PostMapping("/estimate/query_estimate_for_synchronize")
    List<ItemEstimateForAndroidRespDTO> queryEstimateForSyn(BaseDTO baseDTO);

    /**
     * 一体机修改商品估清状态
     */
    @PostMapping("/estimate/save_sold_out_status")
    Integer saveSoldOutStatus(EstimateForManualReqDTO request);

    /**
     * 一安卓同步获取近三天销售模板执行时间
     */
    @PostMapping("/item_template/get_item_template_execute_time")
    List<Long> queryTimeAndTypeForSyn(SingleDataDTO request);

    /**
     * 新建商品接口
     *
     * @param itemSaveReqDTO itemInfo
     * @return itemGuid
     */
    @PostMapping("/item/save_item")
    String saveItem(@RequestBody ItemReqDTO itemSaveReqDTO);

    /**
     * 商品详情
     *
     * @param itemSingleDTO itemGuid
     * @return itemInfo
     */
    @PostMapping("/item/get_item_info")
    ItemInfoRespDTO getItemInfo(@RequestBody ItemSingleDTO itemSingleDTO);

    /**
     * 删除商品
     *
     * @param itemSingleDTO
     * @return
     */
    @PostMapping("/item/delete_item")
    Integer deleteItem(@RequestBody @Valid ItemSingleDTO itemSingleDTO);

    @PostMapping("/item/batch_delete")
    Integer batchDelete(@RequestBody ItemStringListDTO itemStringListDTO);

    @PostMapping("/item/rack_item")
    Integer rackItem(@RequestBody ItemRackDTO itemRackDTO);

    /**
     * 更新菜品排序
     *
     * @param itemSortUpdateReqDTO itemSortUpdateReqDTO
     * @return boolean
     */
    @PostMapping("/item/update_item_sort")
    boolean updateItemSort(ItemSortUpdateReqDTO itemSortUpdateReqDTO);

    /**
     * 批量移动商品:选中一个分类下的商品，批量移动到其它分类下
     *
     * @param typeSortReqDTO 商品guidList，目标分类guid
     * @return boolean
     */
    @ApiOperation(value = "批量移动商品")
    @PostMapping("/item/batch_move_item")
    Boolean batchMoveItem(@RequestBody TypeSortReqDTO typeSortReqDTO);

    /**
     * pad点餐图片查询
     *
     * @param padPictureDTO 门店guid
     * @return pad点餐图片
     */
    @ApiOperation(value = "商品列表")
    @PostMapping("/item/queryPadPicture")
    Page<PadPictureRespDTO> queryPadPicture(@RequestBody @Valid PadPictureDTO padPictureDTO);

    /**
     * 判断当前是否有不可下单的商品
     * 不可下单：商品下架、商品售罄、库存不足
     *
     * @param orderItemReqDTO 需要检查的规格guid
     * @return 不可下单的商品
     */
    @ApiOperation(value = "判断当前是否有不可下单的商品")
    @PostMapping(value = "/item_sku/check_order_placement_item")
    List<ItemPadCalculateDTO> checkOrderPlacementItem(@RequestBody OrderItemReqDTO orderItemReqDTO);

    @PostMapping("/item/parent_sku")
    List<SkuInfoRespDTO> findParentSKUS(@RequestBody List<String> skuGuids);

    /**
     * 新的一体机估清接口
     * 老估清接口也在使用
     *
     * @param request 入参
     * @return Boolean
     */
    @ApiOperation(value = "一体机设置估清*新")
    @PostMapping("/estimate/save_sold_out")
    Boolean saveSoldOut(@RequestBody @Valid EstimateForAndroidReqDTO request);

    /**
     * 一体机估清商品列表
     *
     * @param storeGuid 门店guid
     * @return 一体机估清商品列表
     */
    @ApiOperation(value = "一体机估清商品列表")
    @GetMapping("/estimate/list_estimate")
    EstimateForAndroidRespDTO listEstimate(@RequestParam(value = "storeGuid") String storeGuid);

    /**
     * 批量取消估清
     *
     * @param request 规格Guid列表
     * @return Boolean
     */
    @ApiOperation(value = "批量取消估清")
    @PostMapping("/estimate/batch_cancel_estimate")
    Boolean batchCancelEstimate(@RequestBody SingleDataDTO request);

    /**
     * 批量停售
     *
     * @param request 规格Guid列表
     * @return Boolean
     */
    @ApiOperation(value = "批量停售")
    @PostMapping("/estimate/batch_stop_sell")
    Boolean batchStopSell(@RequestBody SingleDataDTO request);

    /**
     * 根据商品guid查询估清商品详情
     *
     * @param request 商品Guid
     * @return 估清商品详情
     */
    @ApiOperation(value = "根据商品guid查询估清商品详情")
    @PostMapping("/estimate/list_estimate_by_item")
    EstimateItemRespDTO listEstimateByItem(@RequestBody SingleDataDTO request);

    @PostMapping("/item/select_item_for_web")
    Page<ItemWebRespDTO> selectItemForWeb(@RequestBody ItemQueryReqDTO itemQueryReqDTO);

    @PostMapping("/item/update_item")
    Integer updateItem(@RequestBody ItemReqDTO itemUpdateReqDTO);

    @PostMapping("/type/query_type")
    List<TypeWebRespDTO> queryType(@RequestBody ItemSingleDTO itemSingleDTO);

    @PostMapping("/type/save")
    Integer save(@RequestBody TypeReqDTO typeReqDTO);

    @PostMapping("/type/update")
    Integer update(@RequestBody TypeReqDTO typeReqDTO);

    @PostMapping("/type/delete")
    Integer delete(@RequestBody ItemSingleDTO itemSingleDTO);

    /**
     * 批量修改分类顺序
     *
     * @param typeReqDTOList 里面只有sort和typeGuid
     * @return 1
     */
    @ApiOperation(value = "批量修改分类顺序")
    @PostMapping("/type/batch_modify_sort")
    Integer batchModifySort(@RequestBody List<TypeReqDTO> typeReqDTOList);

    /**
     * 新增商品时的属性列表
     *
     * @param itemSingleDTO 门店guid or 品牌guid
     * @return 属性组list（包括属性值）
     */
    @PostMapping("/attr/list_attr_for_save_item")
    List<AttrGroupAttrRespDTO> listAttrForSaveItem(@RequestBody ItemSingleDTO itemSingleDTO);

    @PostMapping("/attr/list_attr_group")
    List<AttrGroupAttrRespDTO> listAttrGroup(@RequestBody ItemSingleDTO itemSingleDTO);

    /**
     * 新增属性组
     *
     * @param attrGroupReqDTO attrGroupReqDTO
     * @return boolean
     */
    @PostMapping("/attr/save_attr_group")
    boolean saveAttrGroup(@RequestBody AttrGroupReqDTO attrGroupReqDTO);

    /**
     * 更新属性组
     *
     * @param attrGroupUpdateReqDTO attrGroupUpdateReqDTO
     * @return Integer
     */
    @PostMapping("/attr/set_attr_group")
    boolean setAttrGroup(@RequestBody AttrGroupUpdateReqDTO attrGroupUpdateReqDTO);

    /**
     * 删除属性组
     *
     * @param itemSingleDTO 属性组guid
     * @return boolean
     */
    @PostMapping("/attr/delete_attr_group")
    boolean deleteAttrGroupByGuid(ItemSingleDTO itemSingleDTO);

    /**
     * 属性值列表
     *
     * @param itemSingleDTO 属性组guid
     * @return 属性值list
     */
    @PostMapping("/attr/list_attr_by_group")
    List<AttrRespDTO> listAttrByGroup(@RequestBody ItemSingleDTO itemSingleDTO);

    /**
     * 新增属性值
     *
     * @param attrReqDTO attrReqDTO
     * @return boolean
     */
    @PostMapping("/attr/save_attr")
    boolean saveAttrValue(@RequestBody AttrReqDTO attrReqDTO);

    /**
     * 修改属性值
     *
     * @param attrSaveReqDTO attrSaveReqDTO
     * @return boolean
     */
    @PostMapping("/attr/update_attr")
    boolean updateAttrValue(@RequestBody AttrSaveReqDTO attrSaveReqDTO);

    /**
     * 删除属性值
     *
     * @param itemSingleDTO 属性值guid
     * @return boolean
     */
    @PostMapping("/attr/delete_attr")
    boolean deleteAttrByGuid(@RequestBody ItemSingleDTO itemSingleDTO);

    /**
     * 检查属性是否被使用
     *
     * @param attrGuid 属性guid
     * @return Boolean
     */
    @ApiOperation(value = "检查属性是否被使用")
    @GetMapping("/attr/check_attr_used")
    Boolean checkAttrUsed(@RequestParam("attrGuid") String attrGuid);

    @GetMapping("/item/query_parent_item_guid")
    String getSubItemGuid(@RequestParam("parentItemGuid") String parentItemGuid);

    /**
     * 通过item查询父级ItemGuid
     * 如果已经是父级，返回本身ItemGuid
     */
    @PostMapping("/item/query_parent_item_guid_by_item")
    Map<String, String> queryParentItemGuidByItem(@RequestBody ItemStringListDTO query);

    /**
     * 通过商品guid和门店guid查询对应spu的所有商品Guid
     */
    @PostMapping("/item/selectSpuItems")
    List<String> selectSpuItems(@RequestBody ItemSpuReqDTO itemSpuReqDTO);

    /**
     * 通过门店查询门店商品在品牌库所属的分类
     */
    @ApiOperation(value = "通过门店查询门店商品在品牌库所属的分类")
    @PostMapping("/type/query_brand_type_by_store")
    List<SaleTypeRespDTO> queryBrandTypeByStore(@RequestBody ItemSingleDTO query);

    @Component
    @Slf4j
    class ItemFallBack implements FallbackFactory<ItemClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ItemClientService create(Throwable throwable) {
            return new ItemClientService() {
                @Override
                public Map<String, String> queryParentItemGuidByItem(ItemStringListDTO query) {
                    log.error("queryParentItemGuidByItem，msg={}", throwable.getMessage());
                    throw new RuntimeException("queryParentItemGuidByItem异常，msg={}" + throwable.getMessage());
                }

                @Override
                public List<String> selectSpuItems(ItemSpuReqDTO itemSpuReqDTO) {
                    log.error("通过商品guid和门店guid查询对应spu的所有商品Guid异常，msg={}", throwable.getMessage());
                    throw new RuntimeException("通过商品guid和门店guid查询对应spu的所有商品Guid异常，msg={}" + throwable.getMessage());
                }

                @Override
                public PreOrderValidateRespDTO preOrderVerification(PreOrderValidateReq preOrderValidateReq) {
                    log.error("一体机预点餐菜谱验证异常，p'rimsg={}", throwable.getMessage());
                    throw new RuntimeException("一体机预点餐菜谱验证异常，msg={}" + throwable.getMessage());
                }

                @Override
                public ItemAndTypeForAndroidRespDTO selectItemAndTypeForSyn(BaseDTO baseDTO) {
                    log.error("商品安卓端同步异常，msg={}", throwable.getMessage());
                    throw new RuntimeException("商品安卓端同步异常，msg={}" + throwable.getMessage());
                }

                @Override
                public List<TypeItemListDTO> selectTypeItemList(PrintItemReqDto printItemReqDto) {
                    log.error("获取打印商品列表异常，msg={}", throwable.getMessage());
                    throw new RuntimeException("获取打印商品列表异常，msg={}" + throwable.getMessage());
                }

                @Override
                public List<ItemEstimateForAndroidRespDTO> queryEstimateForSyn(BaseDTO baseDTO) {
                    log.error("安卓同步查询门店sku估清列表异常：{}", throwable.getMessage());
                    throw new RuntimeException("安卓同步查询门店sku估清列表异常，msg={}" + throwable.getMessage());
                }

                @Override
                public Integer saveSoldOutStatus(EstimateForManualReqDTO request) {
                    log.error("安卓配置商品估清估清列表异常：{}", throwable.getMessage());
                    throw new RuntimeException("安卓配置商品估清估清列表异常，msg={}" + throwable.getMessage());
                }

                @Override
                public List<Long> queryTimeAndTypeForSyn(SingleDataDTO request) {
                    log.error("安卓同步获取近三天销售模板执行时间列表异常：{}", throwable.getMessage());
                    throw new RuntimeException("安卓同步获取近三天销售模板执行时间列表异常，msg={}" + throwable.getMessage());
                }

                @Override
                public String saveItem(ItemReqDTO itemSaveReqDTO) {
                    log.error("新增商品异常：{}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ItemInfoRespDTO getItemInfo(ItemSingleDTO itemSingleDTO) {
                    log.error("获取商品详情数据异常：{}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public Integer deleteItem(@Valid ItemSingleDTO itemSingleDTO) {
                    log.error("删除商品详情数据异常：{}", throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public Integer batchDelete(ItemStringListDTO itemStringListDTO) {
                    log.error(HYSTRIX_PATTERN, "batchDelete", JacksonUtils.writeValueAsString(itemStringListDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Integer rackItem(ItemRackDTO itemRackDTO) {
                    log.error(HYSTRIX_PATTERN, "rackItem", JacksonUtils.writeValueAsString(itemRackDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public boolean updateItemSort(ItemSortUpdateReqDTO itemSortUpdateReqDTO) {
                    log.error(HYSTRIX_PATTERN, "updateItemSort", JacksonUtils.writeValueAsString(itemSortUpdateReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Boolean batchMoveItem(TypeSortReqDTO typeSortReqDTO) {
                    log.error(HYSTRIX_PATTERN, "batchMoveItem", JacksonUtils.writeValueAsString(typeSortReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<PadPictureRespDTO> queryPadPicture(@Valid PadPictureDTO padPictureDTO) {
                    log.error(HYSTRIX_PATTERN, "queryPadPicture", JacksonUtils.writeValueAsString(padPictureDTO),
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<ItemPadCalculateDTO> checkOrderPlacementItem(OrderItemReqDTO orderItemReqDTO) {
                    log.error(HYSTRIX_PATTERN, "checkOrderPlacementItem", JacksonUtils.writeValueAsString(orderItemReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<SkuInfoRespDTO> findParentSKUS(List<String> skuGuids) {
                    log.error(HYSTRIX_PATTERN, "findParentSKUS", JacksonUtils.writeValueAsString(skuGuids),
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public Boolean saveSoldOut(@Valid EstimateForAndroidReqDTO request) {
                    log.error(HYSTRIX_PATTERN, "saveSoldOut", JacksonUtils.writeValueAsString(request),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public EstimateForAndroidRespDTO listEstimate(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "listEstimate", storeGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Boolean batchCancelEstimate(SingleDataDTO request) {
                    log.error(HYSTRIX_PATTERN, "batchCancelEstimate", JacksonUtils.writeValueAsString(request),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Boolean batchStopSell(SingleDataDTO request) {
                    log.error(HYSTRIX_PATTERN, "batchStopSell", JacksonUtils.writeValueAsString(request),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public EstimateItemRespDTO listEstimateByItem(SingleDataDTO request) {
                    log.error(HYSTRIX_PATTERN, "listEstimateByItem", JacksonUtils.writeValueAsString(request),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<ItemWebRespDTO> selectItemForWeb(ItemQueryReqDTO itemQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "selectItemForWeb", JacksonUtils.writeValueAsString(itemQueryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Integer updateItem(ItemReqDTO itemUpdateReqDTO) {
                    log.error(HYSTRIX_PATTERN, "updateItem", JacksonUtils.writeValueAsString(itemUpdateReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<TypeWebRespDTO> queryType(ItemSingleDTO itemSingleDTO) {
                    log.error("获取分类列表数据异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Integer save(TypeReqDTO typeReqDTO) {
                    log.error(HYSTRIX_PATTERN, "save", JacksonUtils.writeValueAsString(typeReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Integer update(TypeReqDTO typeReqDTO) {
                    log.error(HYSTRIX_PATTERN, "update", JacksonUtils.writeValueAsString(typeReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Integer delete(ItemSingleDTO itemSingleDTO) {
                    log.error(HYSTRIX_PATTERN, "delete", JacksonUtils.writeValueAsString(itemSingleDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Integer batchModifySort(List<TypeReqDTO> typeReqDTOList) {
                    log.error(HYSTRIX_PATTERN, "batchModifySort", JacksonUtils.writeValueAsString(typeReqDTOList),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<AttrGroupAttrRespDTO> listAttrForSaveItem(ItemSingleDTO itemSingleDTO) {
                    log.error(HYSTRIX_PATTERN, "listAttrForSaveItem", JacksonUtils.writeValueAsString(itemSingleDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<AttrGroupAttrRespDTO> listAttrGroup(ItemSingleDTO itemSingleDTO) {
                    log.error(HYSTRIX_PATTERN, "listAttrGroup", JacksonUtils.writeValueAsString(itemSingleDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public boolean saveAttrGroup(AttrGroupReqDTO attrGroupReqDTO) {
                    log.error(HYSTRIX_PATTERN, "saveAttrGroup", JacksonUtils.writeValueAsString(attrGroupReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public boolean setAttrGroup(AttrGroupUpdateReqDTO attrGroupUpdateReqDTO) {
                    log.error(HYSTRIX_PATTERN, "setAttrGroup", JacksonUtils.writeValueAsString(attrGroupUpdateReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public boolean deleteAttrGroupByGuid(ItemSingleDTO itemSingleDTO) {
                    log.error(HYSTRIX_PATTERN, "deleteAttrGroupByGuid", JacksonUtils.writeValueAsString(itemSingleDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<AttrRespDTO> listAttrByGroup(ItemSingleDTO itemSingleDTO) {
                    log.error(HYSTRIX_PATTERN, "listAttrByGroup", JacksonUtils.writeValueAsString(itemSingleDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public boolean saveAttrValue(AttrReqDTO attrReqDTO) {
                    log.error(HYSTRIX_PATTERN, "saveAttrValue", JacksonUtils.writeValueAsString(attrReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public boolean updateAttrValue(AttrSaveReqDTO attrSaveReqDTO) {
                    log.error(HYSTRIX_PATTERN, "updateAttrValue", JacksonUtils.writeValueAsString(attrSaveReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public boolean deleteAttrByGuid(ItemSingleDTO itemSingleDTO) {
                    log.error(HYSTRIX_PATTERN, "deleteAttrByGuid", JacksonUtils.writeValueAsString(itemSingleDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Boolean checkAttrUsed(String attrGuid) {
                    log.error(HYSTRIX_PATTERN, "checkAttrUsed", attrGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public String getSubItemGuid(String parentItemGuid) {
                    log.error(HYSTRIX_PATTERN, "getSubItemGuid", parentItemGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<SaleTypeRespDTO> queryBrandTypeByStore(ItemSingleDTO query) {
                    log.error(HYSTRIX_PATTERN, "queryBrandTypeByStore", query,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
