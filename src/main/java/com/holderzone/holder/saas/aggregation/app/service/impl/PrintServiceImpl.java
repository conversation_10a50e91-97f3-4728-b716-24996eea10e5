package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.PrintService;
import com.holderzone.holder.saas.aggregation.app.service.feign.PrintClientService;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.PrinterItemDTO;
import com.holderzone.saas.store.dto.print.cloud.CloudPrinterDTO;
import com.holderzone.saas.store.dto.print.cloud.DeviceUseDTO;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.enums.print.UseInvoiceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * 打印服务实现类
 */
@Slf4j
@Service
@AllArgsConstructor
public class PrintServiceImpl implements PrintService {

    private final PrintClientService printClientService;

    @Override
    public CloudPrinterDTO queryCloudPrinter(CloudPrinterDTO printerDTO) {
        PrinterDTO printer = printClientService.getPrinter(printerDTO);
        CloudPrinterDTO cloudPrinterDTO = new CloudPrinterDTO();
        BeanUtils.copyProperties(printer, cloudPrinterDTO);
        List<DeviceUseDTO> deviceUse = new ArrayList<>();
        cloudPrinterDTO.getArrayOfInvoiceDTO().forEach(invoiceDTO -> {
            DeviceUseDTO deviceUseDTO = new DeviceUseDTO();
            deviceUseDTO.setPrintCount(invoiceDTO.getPrintCount());
            Integer invoiceType = invoiceDTO.getInvoiceType();
            InvoiceTypeEnum typeEnum = InvoiceTypeEnum.ofType(invoiceType);
            switch (typeEnum) {
                case ORDER_ITEM:
                    deviceUseDTO.setInvoiceType(UseInvoiceTypeEnum.POINT_MENU.getCode());
                    break;
                case CHECKOUT:
                    deviceUseDTO.setInvoiceType(UseInvoiceTypeEnum.CHECK_OUT.getCode());
                    break;
                default:
                    return;
            }
            // 云打印机目前只有点菜单有菜
            if (Objects.equals(InvoiceTypeEnum.ORDER_ITEM.getType(), invoiceType)) {
                List<PrinterItemDTO> arrayOfItemDTO = printer.getArrayOfItemDTO();
                deviceUseDTO.setArrayOfItemDTO(Lists.newArrayList(arrayOfItemDTO));
            }

            deviceUse.add(deviceUseDTO);
        });
        cloudPrinterDTO.setDeviceUse(deviceUse);
        return cloudPrinterDTO;
    }

    @Override
    public List<CloudPrinterDTO> listCloudPrinters(PrinterDTO printerDTO) {
        List<PrinterDTO> printerDTOList = printClientService.listCloudPrinters(printerDTO);
        if (CollectionUtils.isEmpty(printerDTOList)) {
            return Lists.newArrayList();
        }
        List<CloudPrinterDTO> cloudPrinterDTOList = new ArrayList<>();
        printerDTOList.forEach(printer -> {
            CloudPrinterDTO cloudPrinterDTO = new CloudPrinterDTO();
            BeanUtils.copyProperties(printer, cloudPrinterDTO);
            List<DeviceUseDTO> deviceUse = new ArrayList<>();
            cloudPrinterDTO.getArrayOfInvoiceDTO().forEach(invoiceDTO -> {
                DeviceUseDTO deviceUseDTO = new DeviceUseDTO();
                deviceUseDTO.setPrintCount(invoiceDTO.getPrintCount());
                InvoiceTypeEnum typeEnum = InvoiceTypeEnum.ofType(invoiceDTO.getInvoiceType());
                switch (typeEnum) {
                    case ORDER_ITEM:
                        deviceUseDTO.setInvoiceType(UseInvoiceTypeEnum.POINT_MENU.getCode());
                        break;
                    case CHECKOUT:
                        deviceUseDTO.setInvoiceType(UseInvoiceTypeEnum.CHECK_OUT.getCode());
                        break;
                    default:
                        return;
                }

                deviceUse.add(deviceUseDTO);
            });
            cloudPrinterDTO.setDeviceUse(deviceUse);
            cloudPrinterDTOList.add(cloudPrinterDTO);
        });
        return cloudPrinterDTOList;
    }
}