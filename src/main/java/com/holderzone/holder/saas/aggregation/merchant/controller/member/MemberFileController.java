package com.holderzone.holder.saas.aggregation.merchant.controller.member;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.base.dto.file.FileDto;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.security.SecurityManager;
import com.holderzone.holder.saas.aggregation.merchant.exception.FileIllegalException;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.BaseService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.MemberListClientNewService;
import com.holderzone.holder.saas.member.dto.account.response.HsmMemberUploadExcelDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.poifs.filesystem.FileMagic;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FileController
 * @date 2018/09/13 10:46
 * @description
 * @program holder-saas-aggregation-merchant
 */
@RestController
@RequestMapping("/file/member")
@Api(description = "会员上传文件接口")
public class MemberFileController {

    private static final Logger logger = LoggerFactory.getLogger(MemberFileController.class);

    private static final List<String> correctTypeList = Arrays.asList("xls", "xlsx", "XLS", "XLSX");

    @Autowired
    private BaseService baseService;

    @Autowired
    private MemberListClientNewService memberListClientNewService;

    @ApiOperation(value = "会员导入接口,文件上传name 必须为file", notes = "单个文件不能超过5000条，上传成功返回文件的地址，失败返回空")
    @PostMapping("/memberUploadExcel")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "会员导入接口,文件上传name 必须为file")
    public Result<String> memberUploadExcel(@RequestParam(value = "file") MultipartFile file) {
        logger.info("文件上传， fileName={}", file.getOriginalFilename());
        // 仅校验商品图片
        String fileName = file.getOriginalFilename();
        String fileType = fileName != null && fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".") + 1) : null;
        if (!correctTypeList.contains(fileType)) {
            logger.info("当前文件类型为：{}", fileType);
            throw new FileIllegalException("文件格式必须为 xls/xlsx/XLS/XLSX 格式！！！");
        }
        try {
            FileMagic fileMagic = FileMagic.valueOf(file.getInputStream());
            if (!(Objects.equals(fileMagic, FileMagic.OLE2) || Objects.equals(fileMagic, FileMagic.OOXML))) {
                throw new FileIllegalException("不是有效的Excel文件");
            }
        } catch (IOException e) {
            logger.error("获取文件流失败--->>>>>>> {}", e.getMessage());
            throw new FileIllegalException("获取文件失败");
        }
        String upload = null;
        try {
            FileDto fileDto = new FileDto();
            fileDto.setFileContent(SecurityManager.entryptBase64(file.getBytes()));
            fileDto.setFileName(UUID.randomUUID().toString().replace("-", "").substring(0, 5) + "." + fileType);
            upload = baseService.upload(fileDto);
            logger.info("图片上传下载路径->>>>>{}", upload);
            upload = Optional.ofNullable(upload).orElse(null);
        } catch (IOException e) {
            logger.error("上传文件失败");
            e.printStackTrace();
        }
        return Result.buildSuccessResult(upload);
    }

    @ApiOperation(value = "会员导入接口,文件url路径 必须为fileUrl")
    @GetMapping("/memberUploadExcelUrl")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "会员导入接口")
    public Result<HsmMemberUploadExcelDTO> memberUploadExcelUrl(@RequestParam("fileUrl") String fileUrl) {
        logger.info("文件url上传， fileUrl={}", fileUrl);
        if (!StringUtils.hasText(fileUrl)) {
            throw new FileIllegalException("文件路径不能为空");
        }
        return Result.buildSuccessResult(memberListClientNewService.memberUploadExcelUrl(fileUrl));
    }

    @ApiOperation(value = "会员模板下载,返回下载地址")
    @GetMapping("/downloadExcelUrl")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "会员模板下载,返回下载地址")
    public Result<String> downloadExcelUrl() {
        return Result.buildSuccessResult(memberListClientNewService.downloadExcelUrl());
    }

}