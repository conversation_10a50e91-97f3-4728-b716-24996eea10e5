package com.holderzone.holder.saas.aggregation.app.utils;

import com.holderzone.holder.saas.common.exception.enums.ExceptionEnums;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 前端数据返回基类
 * @date 2021/8/6 10:05
 */
@Data
public class ResponseModel<T> implements Serializable {

    private static final long serialVersionUID = 3644322851500370651L;

    private Integer code = 0;

    private String message = "ok";

    private T data;

    public ResponseModel() {
    }

    public ResponseModel(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public ResponseModel(String message) {
        this.code = 0;
        this.message = message;
    }

    public ResponseModel(ExceptionEnums exceptionEnums) {
        this.code = exceptionEnums.getCode();
        this.message = exceptionEnums.getMessage();
    }

    public ResponseModel(T data) {
        this.data = data;
    }

}
