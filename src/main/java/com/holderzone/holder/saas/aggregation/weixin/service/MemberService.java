package com.holderzone.holder.saas.aggregation.weixin.service;

import com.holderzone.holder.saas.aggregation.weixin.entity.dto.CheckVerifyVolumeRespDTO;
import com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList;
import com.holderzone.holder.saas.member.wechat.dto.card.RequestCardRightDetails;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseCardRight;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseCardRights;
import com.holderzone.saas.store.dto.weixin.deal.*;
import com.holderzone.saas.store.dto.weixin.member.WxMemberInfoVolumeDetailsRespDTO;
import com.holderzone.saas.store.dto.weixin.member.WxPrepayConfirmRespDTO;
import com.holderzone.saas.store.dto.weixin.member.WxVolumeDetailReqDTO;

import java.util.List;

public interface MemberService {
    List<MemberCardItemDTO> cardPage();

    WxMemberInfoVolumeDetailsRespDTO volumeDetail(WxVolumeDetailReqDTO wxVolumeDetailReqDTO);

    List<ResponseCardRight> cardRight(RequestCardRightDetails requestCardRightDetails);

    WxPrepayConfirmRespDTO confirm(MemberConfirmReqDTO memberConfirmReqDTO);

    ConcessionTotalRespDTO discount(ConcessionTotalReqDTO concessionTotalReqDTO);

    List<MemberVolumeItemDTO> volumeList(VolumePageReqDTO volumePageReqDTO);

    List<ResponseVolumeList> queryVolumeList(VolumeOrderQueryDTO queryDTO);

    CheckVerifyVolumeRespDTO checkVolume(VolumePageReqDTO volumePageReqDTO);

    /**
     * 验证会员账户是否存在
     */
    void verifyMemberExist(String operSubjectGuid, String memberInfoGuid);
}