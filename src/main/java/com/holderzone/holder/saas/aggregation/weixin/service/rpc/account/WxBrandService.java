package com.holderzone.holder.saas.aggregation.weixin.service.rpc.account;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestQueryMemberCardList;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxBrandService
 * @date 2019/10/11 14:58
 * @description //TODO
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = WxBrandService.wxBrandFallBack.class)
public interface WxBrandService {

    @PostMapping("/wx_brand/get_by_brand_guid")
    @ApiOperation(value = "根据品牌guid得到品牌信息")
    BrandDTO getBrandDetail(@RequestBody RequestQueryMemberCardList memberCardListQueryReqDTO);

    @Slf4j
    @Component
    class wxBrandFallBack implements FallbackFactory<WxBrandService>{

        @Override
        public WxBrandService create(Throwable throwable) {
            return new WxBrandService() {
                @Override
                public BrandDTO getBrandDetail(RequestQueryMemberCardList memberCardListQueryReqDTO) {
                    log.error("[/wx_brand/get_by_brand_guid]远程调用服务失败，param={},msg={}", memberCardListQueryReqDTO.getBrandGuid(), throwable.getMessage());
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }
}