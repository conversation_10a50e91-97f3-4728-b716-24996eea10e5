package com.holderzone.holder.saas.aggregation.app.service.feign.kds;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.PointBindDetailsRespDTO;
import com.holderzone.saas.store.dto.kds.resp.PointTypeBindRespDTO;
import com.holderzone.saas.store.dto.kds.resp.ProductionPointRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(name = "holder-saas-store-kds", fallbackFactory = ProductionRpcService.FallbackFactoryImpl.class)
public interface ProductionRpcService {

    @PostMapping("/production/create_point")
    String createPoint(@RequestBody PrdPointCreateReqDTO prdPointCreateReqDTO);

    @PostMapping("/production/update_point")
    void updatePoint(@RequestBody PrdPointUpdateReqDTO prdPointUpdateReqDTO);

    @PostMapping("/production/delete_point")
    void deletePoint(@RequestBody PrdPointDelReqDTO prdPointDelReqDTO);

    @PostMapping("/production/list_point")
    List<ProductionPointRespDTO> listPoint(@RequestBody PrdPointListReqDTO prdPointListReqDTO);

    @PostMapping("/production/query_all_point_item")
    List<PointTypeBindRespDTO> queryAllPointItem(@RequestBody PrdPointItemQueryReqDTO prdPointItemQueryReqDTO);

    @PostMapping("/production/query_binding_details")
    PointBindDetailsRespDTO queryBindingDetails(@RequestBody PrdPointItemQueryReqDTO prdPointItemQueryReqDTO);

    @PostMapping("/production/query_bound_point_item")
    List<PointTypeBindRespDTO> queryBoundPointItem(@RequestBody PrdPointItemQueryReqDTO prdPointItemQueryReqDTO);

    @PostMapping("/production/update_point_item_config")
    void updatePointItem(@RequestBody ItemConfBatchUpdateReqDTO itemConfBatchUpdateReqDTO);

    @PostMapping("/production/bind_point_item")
    void bindPointItem(@RequestBody PrdPointItemBindReqDTO prdPointItemBindReqDTO);

    @PostMapping("/production/unbind_point_item")
    void unbindPointItem(@RequestBody PrdPointItemBindReqDTO prdPointItemBindReqDTO);

    @PostMapping("/production/update_basic_config")
    void updatePrdBasicConfig(@RequestBody DeviceBasicConfUpdateReqDTO deviceBasicConfUpdateReqDTO);

    @PostMapping("/production/update_advanced_config")
    void updatePrdAdvancedConfig(@RequestBody DeviceAdvanConfUpdateReqDTO deviceAdvanConfUpdateReqDTO);

    @Slf4j
    @Component
    class FallbackFactoryImpl implements FallbackFactory<ProductionRpcService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ProductionRpcService create(Throwable throwable) {

            return new ProductionRpcService() {

                @Override
                public String createPoint(PrdPointCreateReqDTO prdPointCreateReqDTO) {
                    log.error(HYSTRIX_PATTERN, "createPoint",
                            JacksonUtils.writeValueAsString(prdPointCreateReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void updatePoint(PrdPointUpdateReqDTO prdPointUpdateReqDTO) {
                    log.error(HYSTRIX_PATTERN, "updatePoint",
                            JacksonUtils.writeValueAsString(prdPointUpdateReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void deletePoint(PrdPointDelReqDTO prdPointDelReqDTO) {
                    log.error(HYSTRIX_PATTERN, "deletePoint",
                            JacksonUtils.writeValueAsString(prdPointDelReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<ProductionPointRespDTO> listPoint(PrdPointListReqDTO prdPointListReqDTO) {
                    log.error(HYSTRIX_PATTERN, "listPoint",
                            JacksonUtils.writeValueAsString(prdPointListReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PointTypeBindRespDTO> queryAllPointItem(PrdPointItemQueryReqDTO prdPointItemQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryAllPointItem",
                            JacksonUtils.writeValueAsString(prdPointItemQueryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PointBindDetailsRespDTO queryBindingDetails(PrdPointItemQueryReqDTO prdPointItemQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryBindingDetails",
                            JacksonUtils.writeValueAsString(prdPointItemQueryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PointTypeBindRespDTO> queryBoundPointItem(PrdPointItemQueryReqDTO prdPointItemQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryBoundPointItem",
                            JacksonUtils.writeValueAsString(prdPointItemQueryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void updatePointItem(ItemConfBatchUpdateReqDTO itemConfBatchUpdateReqDTO) {
                    log.error(HYSTRIX_PATTERN, "updatePointItem",
                            JacksonUtils.writeValueAsString(itemConfBatchUpdateReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void bindPointItem(PrdPointItemBindReqDTO prdPointItemBindReqDTO) {
                    log.error(HYSTRIX_PATTERN, "bindPointItem",
                            JacksonUtils.writeValueAsString(prdPointItemBindReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void unbindPointItem(PrdPointItemBindReqDTO prdPointItemBindReqDTO) {
                    log.error(HYSTRIX_PATTERN, "unbindPointItem",
                            JacksonUtils.writeValueAsString(prdPointItemBindReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void updatePrdBasicConfig(DeviceBasicConfUpdateReqDTO deviceBasicConfUpdateReqDTO) {
                    log.error(HYSTRIX_PATTERN, "updatePrdBasicConfig",
                            JacksonUtils.writeValueAsString(deviceBasicConfUpdateReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void updatePrdAdvancedConfig(DeviceAdvanConfUpdateReqDTO deviceAdvanConfUpdateReqDTO) {
                    log.error(HYSTRIX_PATTERN, "updatePrdAdvancedConfig",
                            JacksonUtils.writeValueAsString(deviceAdvanConfUpdateReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
