package com.holderzone.holder.saas.aggregation.merchant.controller.common;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.MenuService;
import com.holderzone.holder.saas.aggregation.merchant.util.MenuLocaleUtil;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.user.MenuDTO;
import com.holderzone.saas.store.dto.user.MenuSourceDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className MenuController
 * @date 19-2-11 下午5:58
 * @description
 * @program holder-saas-store-staff
 */
@Api(tags = "商户后台菜单相关接口",description = "商户后台菜单、菜单下的资源相关")
@RestController
@RequestMapping("/menu")
public class MenuController {
    private final MenuService menuService;

    @Autowired
    public MenuController(MenuService menuService) {
        this.menuService = menuService;
    }

    @ApiOperation(value = "获取当前登陆用户与指定终端code下的菜单信息")
    @PostMapping("/get_merchant_menu")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF,description = "获取当前登陆用户与指定终端code下的菜单信息")
    public Result<List<MenuDTO>> getMerchantMenu(@ApiParam("业务请求参数为终端code") @RequestBody SingleDataDTO singleDataDTO) {
        List<MenuDTO> merchantMenu = menuService.getMerchantMenu(UserContextUtils.getUserGuid(), singleDataDTO.getData());
        MenuLocaleUtil.transferMerchantMenu(merchantMenu);
        return Result.buildSuccessResult(merchantMenu);
    }

    @ApiOperation(value = "根据菜单guid获取该菜单下的资源信息")
    @PostMapping("/get_source_by_menu")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF,description = "根据菜单guid获取该菜单下的资源信息")
    public Result<List<MenuSourceDTO>> getSourceByMenu(@ApiParam("业务请求参数为菜单guid") @RequestBody SingleDataDTO singleDataDTO) {
        return Result.buildSuccessResult(menuService.getSourceByMenu(singleDataDTO.getData()));
    }

    @ApiOperation(value = "根据菜单pageUrl获取该菜单下的资源信息")
    @PostMapping("/get_page_Url_by_menu")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF,description = "根据菜单guid获取该菜单下的资源信息")
    public Result<List<MenuSourceDTO>> getPageUrlByMenu(@ApiParam("业务请求参数为菜单pageUrl,前缀*") @RequestBody SingleDataDTO singleDataDTO) {
        return Result.buildSuccessResult(menuService.getPageUrlByMenu(singleDataDTO.getData()));
    }

    @Deprecated
    @ApiOperation(value = "根据用户guid获取该菜单下的资源信息")
    @PostMapping("/get_source_by_user")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF,description = "根据用户guid获取该菜单下的资源信息")
    public Result<List<MenuSourceDTO>> getSourceByUser(@ApiParam("业务请求参数为终端Code") @RequestBody SingleDataDTO singleDataDTO) {
        return Result.buildSuccessResult(menuService.getSourceByUser(singleDataDTO.getData()));
    }
}
