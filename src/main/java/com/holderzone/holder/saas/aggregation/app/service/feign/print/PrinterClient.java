package com.holderzone.holder.saas.aggregation.app.service.feign.print;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.print.PrintOrderDTO;
import com.holderzone.saas.store.dto.print.content.PrintItemStatsDTO;
import com.holderzone.saas.store.dto.print.content.PrintTypeStatsDTO;
import com.holderzone.saas.store.dto.print.content.retail.PrintRetailOpStatsDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className PrinterClient
 * @date 18-10-20 上午9:42
 * @description
 * @program holder-saas-store-staff
 */
@Component
@FeignClient(value = "holder-saas-store-print", fallbackFactory = PrinterClient.ServiceFallBack.class)
public interface PrinterClient {


    /**
     * 零售版 -> 分类销售统计打印
     *
     * @param request
     */
    @PostMapping("/print_record/send")
    String printClassifySales(PrintTypeStatsDTO request);

    /**
     * 零售版 -> 商品销售统计打印
     *
     * @param request
     */
    @PostMapping("/print_record/send")
    String printItemSales(PrintItemStatsDTO request);

    /**
     * 零售版 -> 营业概况打印
     *
     * @param request
     */
    @PostMapping("/print_record/send")
    String printRetailOpStats(PrintRetailOpStatsDTO request);


    /**
     * 获取外卖自动接单漏单小票打印
     */
    @GetMapping("/print_record/takeaway_timeout/reprint")
    List<PrintOrderDTO> reprintTakeawayPrintOrderList(@RequestParam("storeGuid") String storeGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<PrinterClient> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public PrinterClient create(Throwable cause) {
            return new PrinterClient() {


                @Override
                public String printClassifySales(PrintTypeStatsDTO request) {
                    log.error(HYSTRIX_PATTERN, "queryByAndroid", JacksonUtils.writeValueAsString(request),
                            ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public String printItemSales(PrintItemStatsDTO request) {
                    log.error(HYSTRIX_PATTERN, "queryByAndroid", JacksonUtils.writeValueAsString(request),
                            ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public String printRetailOpStats(PrintRetailOpStatsDTO request) {
                    log.error(HYSTRIX_PATTERN, "queryByAndroid", JacksonUtils.writeValueAsString(request),
                            ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public List<PrintOrderDTO> reprintTakeawayPrintOrderList(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "reprintTakeawayPrintOrderList", storeGuid, ThrowableUtils.asString(cause));
                    return null;
                }
            };
        }

    }
}