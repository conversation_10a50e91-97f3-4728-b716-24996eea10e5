package com.holderzone.holder.saas.aggregation.merchant.entity.vo;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**库存列表VO
 * <AUTHOR>
 * @date 2019/05/10 13:44
 */
public class MaterialStockVO {
    @ApiModelProperty("GUID")
    private String guid;
    @ApiModelProperty("企业GUID")
    private String enterpriseGuid;
    @ApiModelProperty("门店GUID")
    private String storeGuid;
    @ApiModelProperty("仓库GUID")
    private String warehouseGuid;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("简称")
    private String simpleName;
    @ApiModelProperty("单位")
    private String unit;
    @ApiModelProperty("单位名称")
    private String unitName;
    @ApiModelProperty("最低库存")
    private BigDecimal lowestStock;
    @ApiModelProperty("实时库存")
    private BigDecimal stock;
    @ApiModelProperty("所属分类GUID")
    private String category;
    @ApiModelProperty("类型 0:物资,1/原料")
    private String type;
    @ApiModelProperty("编码")
    private String code;
    @ApiModelProperty("库存状态")
    private String stockState;

    @ApiModelProperty(value = "入库单价")
    private BigDecimal inUnitPrice;

    public BigDecimal getInUnitPrice() {
        return inUnitPrice;
    }

    public void setInUnitPrice(BigDecimal inUnitPrice) {
        this.inUnitPrice = inUnitPrice;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getEnterpriseGuid() {
        return enterpriseGuid;
    }

    public void setEnterpriseGuid(String enterpriseGuid) {
        this.enterpriseGuid = enterpriseGuid;
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
    }

    public String getWarehouseGuid() {
        return warehouseGuid;
    }

    public void setWarehouseGuid(String warehouseGuid) {
        this.warehouseGuid = warehouseGuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSimpleName() {
        return simpleName;
    }

    public void setSimpleName(String simpleName) {
        this.simpleName = simpleName;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getLowestStock() {
        return lowestStock;
    }

    public void setLowestStock(BigDecimal lowestStock) {
        this.lowestStock = lowestStock;
    }

    public BigDecimal getStock() {
        return stock;
    }

    public void setStock(BigDecimal stock) {
        this.stock = stock;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getStockState() {
        return stockState;
    }

    public void setStockState(String stockState) {
        this.stockState = stockState;
    }
}
