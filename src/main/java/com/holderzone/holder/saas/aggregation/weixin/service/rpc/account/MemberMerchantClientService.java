package com.holderzone.holder.saas.aggregation.weixin.service.rpc.account;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.utils.MemberResult;
import com.holderzone.holder.saas.member.merchant.dto.label.RequestManualLabel;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/8/6
 * @description member-merchant
 */
@Component
@FeignClient(value = "holder-saas-member-merchant", fallbackFactory = MemberMerchantClientService.MemberMerchantFallback.class)
public interface MemberMerchantClientService {

    @ApiOperation("会员批量打手动标签-后台设置的接口")
    @PostMapping("/hsa-LabelSetting/batchMemberManualLabel")
    void batchMemberManualLabel(@Validated @RequestBody RequestManualLabel req);

    @ApiOperation(value = "查询会员余额支付成分")
    @PostMapping(value = "/hsmdc/store/business/query_member_pay_constitute")
    MemberResult<List<AmountItemDTO>> queryMemberPayConstitute(@RequestBody DailyReqDTO query);

    @Slf4j
    @Component
    class MemberMerchantFallback implements FallbackFactory<MemberMerchantClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public MemberMerchantClientService create(Throwable throwable) {
            return new MemberMerchantClientService() {

                @Override
                public void batchMemberManualLabel(RequestManualLabel req) {
                    log.info(HYSTRIX_PATTERN, "batchMemberManualLabel", JacksonUtils.writeValueAsString(req),
                            throwable.getCause());
                    throw new ServerException();
                }

                @Override
                public MemberResult<List<AmountItemDTO>> queryMemberPayConstitute(DailyReqDTO query) {
                    log.info(HYSTRIX_PATTERN, "queryMemberPayConstitute", JacksonUtils.writeValueAsString(query),
                            throwable.getCause());
                    throw new ServerException();
                }
            };
        }
    }

}
