package com.holderzone.holder.saas.aggregation.weixin.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2020/7/14 11:17
 * @description
 */
@Data
@RefreshScope
@Configuration
public class WeCatConfig {

    @Value("${wxStorePay.appId}")
    private String appId;

    @Value("${wxStorePay.mchntName}")
    private String mchntName;

    @Value("${wxStorePay.appSecret}")
    private String appSecret;

    @Value("${frontend.ordering-index-page}")
    private String orderingIndexPage;
    /*
    分钟
     */
    @Value("${frontend.ordering-message-expire}")
    private Integer orderingMessageExpire;
}
