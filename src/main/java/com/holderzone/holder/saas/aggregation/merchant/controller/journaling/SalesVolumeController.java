package com.holderzone.holder.saas.aggregation.merchant.controller.journaling;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.journaling.ReportClientService;
import com.holderzone.holder.saas.aggregation.merchant.util.ReportValidateUtil;
import com.holderzone.saas.store.dto.journaling.req.SaleDetailReportReqDTO;
import com.holderzone.saas.store.dto.journaling.req.SalesVolumeReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.SalesVolumeRespDTO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SalesVolumeController
 * @date 2019/12/26 13:37
 * @description 商品销量接口
 * @program holder-saas-store
 */
@RestController
@RequestMapping("/sales_volume")
@Slf4j
@Api("商品销量接口")
public class SalesVolumeController {

    private final ReportClientService reportClientService;

    @Autowired
    public SalesVolumeController(ReportClientService reportClientService) {
        this.reportClientService = reportClientService;
    }

    @PostMapping("/page")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT, description = "分页查询商品销量统计")
    public Result<Page<SalesVolumeRespDTO>> pageSalesVolumeRespDTO(@RequestBody SalesVolumeReqDTO salesVolumeReqDTO) {
        log.info("分页查询商品销量统计请求入参：{}", JacksonUtils.writeValueAsString(salesVolumeReqDTO));
        Page<SalesVolumeRespDTO> data = reportClientService.pageSalesVolumeRespDTO(salesVolumeReqDTO);
        log.info("商品销量统计查询结果");
        //门店兼容
        ReportValidateUtil.reportCompatibilityCheck(salesVolumeReqDTO);
        //时间兼容
        setReqDateTime(salesVolumeReqDTO);
        return Result.buildSuccessResult(data);
    }

    private static void setReqDateTime(SalesVolumeReqDTO businessSituationReqDTO) {
        if (businessSituationReqDTO.getBusinessEndDateTime() == null) {
            businessSituationReqDTO.setBusinessEndDateTime(businessSituationReqDTO.getEndDate().atTime(LocalTime.MAX));
        }
        if (businessSituationReqDTO.getBusinessStartDateTime() == null) {
            businessSituationReqDTO.setBusinessStartDateTime(businessSituationReqDTO.getStartDate().atTime(LocalTime.MIN));
        }
    }
}
