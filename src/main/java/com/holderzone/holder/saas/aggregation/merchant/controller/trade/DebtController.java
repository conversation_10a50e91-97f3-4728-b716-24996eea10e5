package com.holderzone.holder.saas.aggregation.merchant.controller.trade;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.trade.DebtClientService;
import com.holderzone.saas.store.dto.trade.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 挂账Controller
 *
 * @since 2020-12-15
 */
@Slf4j
@Api(value = "挂账接口")
@RestController
@RequestMapping(value = "/debt")
public class DebtController {

    private final DebtClientService debtClientService;

    public DebtController(DebtClientService debtUnitService) {
        this.debtClientService = debtUnitService;
    }

    @PostMapping("/unit/save")
    @ApiOperation(value = "新建挂账单位")
    public Result<Boolean> saveUnit(@Validated @RequestBody DebtUnitSaveReqDTO reqDTO) {
        log.info("新建挂账单位接口入参：{}", JSON.toJSONString(reqDTO));
        return Result.buildSuccessResult(debtClientService.saveUnit(reqDTO));
    }

    @PostMapping("/unit/page")
    @ApiOperation(value = "分页查询单位列表")
    public Result<Page<DebtUnitPageRespDTO>> unitPage(@RequestBody DebtUnitPageReqDTO reqDTO) {
        log.info("挂账单位分页查询入参：{}", JSON.toJSONString(reqDTO));
        return Result.buildSuccessResult(debtClientService.unitPage(reqDTO));
    }

    @GetMapping("/unit/delete")
    @ApiOperation(value = "删除挂账单位")
    public Result<Boolean> unitDelete(@RequestParam("unitGuid") String unitGuid) {
        log.info("挂账单位删除单位guid：{}", unitGuid);
        return Result.buildSuccessResult(debtClientService.unitDelete(unitGuid));
    }

    @PostMapping("/unit/page_debt_unit_record")
    @ApiOperation(value = "分页查询挂账单位流水")
    public Result<Page<DebtUnitRecordPageRespDTO>> pageDebtUnitRecord(@Validated @RequestBody DebtUnitRecordPageReqDTO reqDTO) {
        log.info("分页查询挂账单位流水接口入参：{}", JSON.toJSONString(reqDTO));
        return Result.buildSuccessResult(debtClientService.pageDebtUnitRecord(reqDTO));
    }

    @GetMapping("/unit/query_debt_unit_total")
    @ApiOperation(value = "查询挂账单位汇总")
    public Result<DebtUnitRecordTotalDTO> queryDebtUnitTotal(@RequestParam("unitCode") String unitCode) {
        log.info("查询挂账单位汇总接口入参：{}", unitCode);
        return Result.buildSuccessResult(debtClientService.queryDebtUnitTotal(unitCode));
    }

    @PostMapping("/unit/update_debt_unit")
    @ApiOperation(value = "挂账管理还款")
    public Result<Boolean> updateDebtUnit(@RequestBody DebtUnitRecordUpdateReqDTO updateReqDTO) {
        log.info("查询挂账单位汇总接口入参：{}", JSON.toJSONString(updateReqDTO));
        return Result.buildSuccessResult(debtClientService.updateDebtUnit(updateReqDTO));
    }

    @GetMapping("/unit/query_debt_unit_list")
    @ApiOperation(value = "查询挂账单位")
    public Result<List<DebtUnitDropdownListDTO>> queryDebtUnitList() {
        return Result.buildSuccessResult(debtClientService.queryDebtUnitList());
    }

    @GetMapping("/unit/query_debt_unit_total_by_guid")
    @ApiOperation(value = "根据单位Guid查询挂账单位汇总")
    public Result<DebtUnitRecordTotalDTO> queryDebtUnitTotalByUnitGuid(@RequestParam("unitGuid") String unitGuid) {
        log.info("查询挂账单位汇总接口入参：{}", unitGuid);
        return Result.buildSuccessResult(debtClientService.queryDebtUnitTotalByUnitGuid(unitGuid));
    }

    @PostMapping("/unit/h5login")
    @ApiOperation(value = "h5挂账页面登录")
    public Result<Boolean> h5Login(@Validated @RequestBody DebtUnitLoginH5ReqDTO reqDTO) {
        log.info("H5页面查询登录入参：{}", JSON.toJSONString(reqDTO));
        return Result.buildSuccessResult(debtClientService.h5Login(reqDTO));
    }

    @PostMapping("/record/h5query")
    @ApiOperation(value = "H5查询挂账还款记录")
    public Result<DebtRecordH5RespDTO> queryDebtRecordH5(@Validated @RequestBody DebtUnitLoginH5ReqDTO reqDTO) {
        log.info("H5查询挂账还款记录入参：{}", JSON.toJSONString(reqDTO));
        return Result.buildSuccessResult(debtClientService.queryDebtRecordH5(reqDTO));
    }
}
