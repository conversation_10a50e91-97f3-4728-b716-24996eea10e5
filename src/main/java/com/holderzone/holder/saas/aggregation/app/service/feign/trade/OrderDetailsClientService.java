package com.holderzone.holder.saas.aggregation.app.service.feign.trade;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.order.request.dinein.DineInOrderListReqDTO;
import com.holderzone.saas.store.dto.trade.OrderInfoRespDTO;
import com.holderzone.saas.store.dto.trade.req.UpdateOrderInvoiceStateReqDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * 订单详情
 */
@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = OrderDetailsClientService.FastFoodFallBack.class)
public interface OrderDetailsClientService {

    @ApiOperation(value = "订单列表", notes = "订单列表")
    @PostMapping("/order_detail/order_list")
    Page<OrderInfoRespDTO> pageOrderInfo(@RequestBody DineInOrderListReqDTO reqDTO);

    @ApiOperation(value = "更新订单开票状态")
    @PostMapping("/order_detail/update_order_invoice_state")
    void updateOrderInvoiceState(@RequestBody UpdateOrderInvoiceStateReqDTO reqDTO);

    @Component
    @Slf4j
    class FastFoodFallBack implements FallbackFactory<OrderDetailsClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public OrderDetailsClientService create(Throwable throwable) {
            return new OrderDetailsClientService() {

                @Override
                public Page<OrderInfoRespDTO> pageOrderInfo(DineInOrderListReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "pageOrderInfo", JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void updateOrderInvoiceState(UpdateOrderInvoiceStateReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "updateOrderInvoiceState", JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
