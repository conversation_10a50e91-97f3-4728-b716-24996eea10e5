package com.holderzone.holder.saas.aggregation.merchant.controller.item;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.config.RedisLock;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.item.ItemClientService;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.req.price.*;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.item.resp.price.ItemSkuAndPlanPriceDTO;
import com.holderzone.saas.store.dto.item.resp.price.PlanPriceAllItemSkuRespDTO;
import com.holderzone.saas.store.dto.item.resp.price.PlanPriceAllTypeRespDTO;
import com.holderzone.saas.store.dto.item.resp.price.PlanPriceEditDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/plan")
@Api(tags = "菜谱方案接口")
@AllArgsConstructor
public class PricePlanController {

    private final ItemClientService itemClientService;

    private final RedisLock redisLock;

    @ApiOperation(value = "价格方案保存")
    @PostMapping("/save")
    public Result<String> savePlan(@RequestBody PricePlanReqDTO reqDTO) {
        return Result.buildSuccessResult(itemClientService.savePlan(reqDTO));
    }

    @ApiOperation(value = "方案列表查询")
    @PostMapping("/list")
    public Result<Page<PricePlanRespDTO>> planList(@RequestBody PricePlanPageReqDTO reqDTO) {
        return Result.buildSuccessResult(itemClientService.planList(reqDTO));
    }

    @ApiOperation(value = "方案编辑查询")
    @GetMapping("/getOne")
    public Result<PricePlanRespDTO> getPlan(@RequestParam("planGuid") String planGuid) {
        return Result.buildSuccessResult(itemClientService.getPlan(planGuid));
    }

    @ApiOperation(value = "获取方案可导入菜品")
    @PostMapping("/get_brand_item_list")
    public Result<Page<PricePlanItemAddQueryRespDTO>> selectBrandSkuItemList(@RequestBody PricePlanItemAddQueryReqDTO request) {
        return Result.buildSuccessResult(itemClientService.selectBrandSkuItemList(request));
    }

    @ApiOperation(value = "方案菜品导入时保存")
    @PostMapping("/saveItem")
    public Result savePlanItem(@RequestBody PricePlanItemAddReqDTO reqDTO) {
        if (itemClientService.savePlanItem(reqDTO)) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVE_FAILED));
        }
    }

    @ApiOperation(value = "方案菜品导入后查询")
    @PostMapping("/itemList")
    public Result<Page<PricePlanItemPageRespDTO>> itemList(@RequestBody PricePlanItemPageReqDTO reqDTO) {
        return Result.buildSuccessResult(itemClientService.itemList(reqDTO));
    }

    @ApiOperation(value = "方案菜品移除")
    @PostMapping("/removeItems")
    public Result removeItems(@RequestBody PricePlanItemRemoveReqDTO reqDTO) {
        if (itemClientService.removeItems(reqDTO)) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVE_FAILED));
        }
    }

    @ApiOperation(value = "方案菜品更新")
    @PostMapping("/updateItems")
    public Result updateItems(@RequestBody PricePlanItemUpdateReqDTO reqDTO) {
        if (itemClientService.updateItems(reqDTO)) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVE_FAILED));
        }
    }

    @ApiOperation(value = "方案推送")
    @PostMapping("/push")
    public Result pushPlan(@RequestBody PricePlanPushReqDTO reqDTO) {
        if (itemClientService.pushPlan(reqDTO)) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVE_FAILED));
        }
    }

    @ApiOperation(value = "方案删除")
    @GetMapping("/delete")
    public Result deletePlan(@RequestParam("planGuid") String planGuid) {
        if (itemClientService.deletePlan(planGuid)) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVE_FAILED));
        }
    }

    /**
     * 永久停用方案
     *
     * @param planGuid 方案id
     * @return Boolean
     */
    @ApiOperation(value = "永久停用方案")
    @GetMapping("/permanently_deactivate")
    public Result<Boolean> permanentlyDeactivate(@RequestParam("planGuid") String planGuid) {
        log.info("方案永久停用 入参：{}", planGuid);
        if (itemClientService.permanentlyDeactivate(planGuid)) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.DEACTIVATION_FAILED));
        }
    }

    @ApiOperation(value = "门店控制列表")
    @PostMapping("/store_control_list")
    public Result<List<PricePlanStoreRespDTO>> storeControlList(@RequestBody PricePlanStoreReqDTO reqDTO) {
        log.info("门店控制列表入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        return Result.buildSuccessResult(itemClientService.storeControlList(reqDTO));
    }

    @ApiOperation(value = "绑定门店与方案关系")
    @PostMapping("/store_plan_bind")
    public Result bingPlanStore(@RequestBody PricePlanStoreReqDTO reqDTO) {
        log.info("绑定门店与方案关系入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        if (itemClientService.bingPlanStore(reqDTO)) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVE_FAILED));
        }
    }

    @ApiOperation(value = "查询价格方案分类列表", notes = "查询品牌/门店分类列表")
    @GetMapping("/query_type_by_price_plan_guid")
    public Result<List<TypeWebRespDTO>> queryTypeByPricePlanGuid(@RequestParam String pricePlanGuid) {
        log.info("查询价格方案分类列表入参,pricePlanGuid={}", pricePlanGuid);
        return Result.buildSuccessResult(itemClientService.queryTypeByPricePlanGuid(pricePlanGuid));
    }

    @ApiOperation(value = "门店绑定菜谱方案校验")
    @PostMapping("/check_store_price_plan_rule")
    public Result<List<PricePlanStoreCheckRespDTO>> checkStorePricePlanRule(@RequestBody PricePlanReqDTO reqDTO) {
        log.info("门店绑定菜谱方案校验入参：{}", JSON.toJSONString(reqDTO));
        return Result.buildSuccessResult(itemClientService.checkStorePricePlanRule(reqDTO));
    }

    @ApiOperation(value = "商品批量设置上下架")
    @PostMapping("/batch_operating_item")
    public Result<Boolean> batchOperatingItem(@RequestBody PricePlanItemReqDTO reqDTO) {
        log.info("商品批量设置上下架入参：{}", JSON.toJSONString(reqDTO));
        return Result.buildSuccessResult(itemClientService.batchOperatingItem(reqDTO));
    }

    @ApiOperation(value = "菜谱方案草稿删除")
    @PostMapping("/delete_plan_draft")
    public Result<Boolean> deletePlanDraft(@RequestBody PricePlanDraftReqDTO reqDTO) {
        log.info("菜谱方案草稿删除入参：{}", JSON.toJSONString(reqDTO));
        return Result.buildSuccessResult(itemClientService.deletePlanDraft(reqDTO));
    }

    @ApiOperation(value = "价格方案草稿查询")
    @GetMapping("/get_price_plan_cache_data")
    public Result<PricePlanReqDTO> getPricePlanCacheData(@RequestParam("userGuid") String userGuid,
                                                         @RequestParam("brandGuid") String brandGuid) {
        log.info("价格方案草稿查询,userGuid={},brandGuid={}", userGuid, brandGuid);
        return Result.buildSuccessResult(itemClientService.getPricePlanCacheData(userGuid, brandGuid));
    }

    /**
     * 通过门店guid查询所有菜谱方案
     *
     * @param storeGuid 门店guid
     * @return 所有菜谱方案
     */
    @ApiOperation(value = "通过门店guid查询所有菜谱方案")
    @GetMapping("/query_plans_by_store_guid")
    public Result<List<PricePlanRespDTO>> queryPlansByStoreGuid(@RequestParam("storeGuid") String storeGuid) {
        log.info("通过门店guid查询所有菜谱方案入参：{}", storeGuid);
        return Result.buildSuccessResult(itemClientService.queryPlansByStoreGuid(storeGuid));
    }

    @ApiOperation(value = "校验价格方案分类是否可以删除", notes = "校验价格方案分类是否可以删除")
    @PostMapping("/check_type_price_plan")
    public Result<Boolean> checkTypePricePlan(@RequestBody ItemQueryReqDTO itemQueryReqDTO) {
        log.info("校验价格方案分类是否可以删除入参,typeGuid={}", JacksonUtils.writeValueAsString(itemQueryReqDTO));
        return Result.buildSuccessResult(itemClientService.checkTypePricePlan(itemQueryReqDTO));
    }

    @ApiOperation(value = "菜谱方案可导入菜品查询")
    @PostMapping("/find_price_plan_item_list")
    public Result<List<TypeItemRespDTO>> findPricePlanItemList(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("菜谱方案可导入菜品查询入参,request={}", JSON.toJSONString(itemSingleDTO));
        return Result.buildSuccessResult(itemClientService.findPricePlanItemList(itemSingleDTO));
    }

    /**
     * 根据菜谱方案查询菜谱方案绑定菜品
     *
     * @param itemQueryReqDTO ItemQueryReqDTO
     * @return 商品列表
     */
    @ApiOperation(value = "根据菜谱方案查询菜谱方案绑定菜品")
    @PostMapping("/query_plan_items_by_plan")
    public Result<Page<ItemWebRespDTO>> queryPlanItemsByPlan(@RequestBody ItemQueryReqDTO itemQueryReqDTO) {
        log.info("根据菜谱方案查询菜谱方案绑定菜品入参：{}", JacksonUtils.writeValueAsString(itemQueryReqDTO));
        Page<ItemWebRespDTO> itemWebRespDTOPage = itemClientService.queryPlanItemsByPlan(itemQueryReqDTO);
        log.info("根据菜谱方案查询菜谱方案绑定菜品返回：{}",JacksonUtils.writeValueAsString(itemWebRespDTOPage));
        return Result.buildSuccessResult(itemWebRespDTOPage);
    }

    /**
     * 根据方案guid查询绑定门店信息
     *
     * @param itemSingleDTO 菜谱方案Guid
     * @return List<StoreDTO>
     */
    @ApiOperation(value = "根据方案guid查询绑定门店信息")
    @PostMapping("/query_plan_store_list_by_plan")
    public Result<List<StoreDTO>> queryPlanStoreListByPlan(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("根据方案guid查询绑定门店信息 入参,request={}", JSON.toJSONString(itemSingleDTO));
        return Result.buildSuccessResult(itemClientService.queryPlanStoreListByPlan(itemSingleDTO));
    }

    /**
     * 通过门店guid查询所属菜谱方案
     *
     * @param storeGuid 门店guid
     * @return 所属菜谱方案，如果没有则返回空list
     */
    @ApiOperation(value = "通过门店guid查询所属菜谱方案")
    @GetMapping("/query_belong_plan_by_store_guid")
    public Result<List<PricePlanRespDTO>> queryBelongPlansByStoreGuid(@RequestParam("storeGuid") String storeGuid) {
        log.info("通过门店guid查询所属菜谱方案 入参：{}", storeGuid);
        return Result.buildSuccessResult(itemClientService.queryBelongPlansByStoreGuid(storeGuid));
    }

    /**
     * 通过菜谱guid预览菜谱
     *
     * @param reqDTO 菜谱guid,浏览模式
     * @return 预览的菜谱方案信息
     */
    @ApiOperation(value = "通过菜谱guid预览菜谱")
    @PostMapping("/preview_plan_by_guid")
    public Result<PreviewPlanRespDTO> previewPlansByGuid(@RequestBody PreviewPlanReqDTO reqDTO) {
        log.info("通过菜谱guid预览菜谱 入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        return Result.buildSuccessResult(itemClientService.previewPlansByGuid(reqDTO));
    }

    /**
     * 二维码过期校验
     *
     * @param reqDTO 二维码id和方案id
     * @return 该二维码是否过期，过期时间为3天
     */
    @ApiOperation(value = "二维码过期校验，过期时间为3天")
    @PostMapping("/check_qr_code")
    public Result<CheckQrCodeRespDTO> checkQrCode(@RequestBody CheckQrCodeDTO reqDTO) {
        log.info("二维码过期校验 入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        return Result.buildSuccessResult(itemClientService.checkQrCode(reqDTO));
    }

    @ApiOperation(value = "复制菜谱方案")
    @GetMapping("/copy_plan")
    public Result<Boolean> copyPlan(String planGuid) {
        log.info("菜谱复制入参：{}，", planGuid);
        return Result.buildSuccessResult(itemClientService.copyPlan(planGuid));
    }

    /**
     * 批量编辑方案商品的商品列表
     * v1.18.5 版本优化：取消分页，增加分类
     * 查询所有为已启用、即将启用、暂不启用菜谱的所有商品（去重）
     *
     * @param req 请求参数
     * @return 商品列表
     */
    @ApiOperation(value = "批量编辑方案商品的商品列表")
    @PostMapping("/list_all_plan_price_item")
    public Result<List<PlanPriceAllTypeRespDTO>> listAllPlanPriceItem(@RequestBody @Validated PlanPriceAllItemReqDTO req) {
        log.info("批量编辑方案商品的商品列表 req={}", JacksonUtils.writeValueAsString(req));
        return Result.buildSuccessResult(itemClientService.listAllPlanPriceItem(req));
    }

    @ApiOperation(value = "编辑商品得商品规格信息和价格方案列表信息")
    @GetMapping("/list_item_sku_and_plan_price")
    public Result<ItemSkuAndPlanPriceDTO> listItemSkuAndPlanPrice(@RequestParam("itemGuid") String itemGuid) {
        log.info("批量编辑菜谱入参：{}，", itemGuid);
        return Result.buildSuccessResult(itemClientService.listItemSkuAndPlanPrice(itemGuid));
    }

    @ApiOperation(value = "批量编辑保存商品菜谱信息")
    @PostMapping("/save_batch_edit_plan_price")
    public Result<Void> saveBatchEditPlanPrice(@RequestBody PlanPriceEditReqDTO req) {
        log.info("批量编辑保存商品菜谱信息 req={}", JacksonUtils.writeValueAsString(req));
        // 防重复提交
        boolean getLock;
        try {
            // 判断是否获取了锁
            getLock = redisLock.lock(JacksonUtils.writeValueAsString(req));
            if (getLock) {
                itemClientService.saveBatchEditPlanPrice(req);
            }
        } finally {
            redisLock.delete(JacksonUtils.writeValueAsString(req));
        }
        if (getLock) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVING_IN_PROGRESS));
        }
    }

    @ApiOperation(value = "批量新增保存商品菜谱信息")
    @PostMapping("/save_batch_add_plan_price")
    public Result<Void> saveBatchAddPlanPrice(@RequestBody PlanPriceAddReqDTO req) {
        log.info("批量新增保存商品菜谱信息 req={}", JacksonUtils.writeValueAsString(req));
        // 防重复提交
        boolean getLock;
        try {
            // 判断是否获取了锁
            getLock = redisLock.lock(JacksonUtils.writeValueAsString(req));
            if (getLock) {
                itemClientService.saveBatchAddPlanPrice(req);
            }
        } finally {
            redisLock.delete(JacksonUtils.writeValueAsString(req));
        }
        if (getLock) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVING_IN_PROGRESS));
        }
    }

    @ApiOperation(value = "查询可用的价格方案")
    @PostMapping("/list_available_plan_price")
    public Result<List<PlanPriceEditDTO>> listAvailablePlanPrice(@RequestBody PlanPriceAvailableReqDTO req) {
        log.info("查询可用的价格方案 req={}", JacksonUtils.writeValueAsString(req));
        return Result.buildSuccessResult(itemClientService.listAvailablePlanPrice(req));
    }

    @ApiOperation(value = "查询价格方案可用的商品分类")
    @GetMapping("/list_available_plan_item_type")
    public Result<List<ItemTypeRespDTO>> listAvailablePlanItemType(@RequestParam("planGuid") String planGuid) {
        log.info("查询价格方案可用的商品分类 planGuid={}", planGuid);
        return Result.buildSuccessResult(itemClientService.listAvailablePlanItemType(planGuid));
    }

    /**
     * 批量下架的商品列表
     * 查询所有为已启用、即将启用、暂不启用菜谱的所有商品规格（去重）
     *
     * @param req 品牌必传
     * @return 商品列表
     */
    @ApiOperation(value = "批量下架商品列表")
    @PostMapping("/list_all_plan_price_item_sku")
    public Result<Page<PlanPriceAllItemSkuRespDTO>> listAllPlanPriceItemSku(@RequestBody @Validated PlanPriceAllItemSkuReqDTO req) {
        log.info("批量下架商品列表 eqr={}，", JacksonUtils.writeValueAsString(req));
        return Result.buildSuccessResult(itemClientService.listAllPlanPriceItemSku(req));
    }

    /**
     * 批量下架保存商品菜谱信息
     *
     * @param request 批量下架保存入参实体
     */
    @ApiOperation(value = "批量下架保存商品菜谱信息")
    @PostMapping("/save_batch_sold_out_plan_item")
    public Result<Boolean> saveBatchSoldOutPlanItem(@RequestBody PlanPriceSoldOutReqDTO request) {
        log.info("批量下架保存商品菜谱信息 request={}", JacksonUtils.writeValueAsString(request));
        // 防重复提交
        boolean getLock;
        try {
            // 判断是否获取了锁
            getLock = redisLock.lock(JacksonUtils.writeValueAsString(request));
            if (getLock) {
                itemClientService.saveBatchSoldOutPlanItem(request);
            }
        } finally {
            redisLock.delete(JacksonUtils.writeValueAsString(request));
        }
        if (getLock) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVING_IN_PROGRESS));
        }
    }

    /**
     * 批量编辑方案分类的分类列表
     * 查询所有为已启用、即将启用、暂不启用菜谱的所有分类 按名称去重
     */
    @ApiOperation(value = "批量编辑方案分类的分类列表")
    @PostMapping("/list_all_plan_price_item_type")
    public Result<List<TypeRespDTO>> listAllPlanPriceItemType(@RequestBody @Validated PlanPriceAllItemReqDTO req) {
        log.info("批量编辑方案分类的分类列表 req={}", JacksonUtils.writeValueAsString(req));
        return Result.buildSuccessResult(itemClientService.listAllPlanPriceItemType(req));
    }

    /**
     * 查询分类名称所存在的菜谱
     * 查询所有为已启用、即将启用、暂不启用菜谱 按名称去重
     */
    @ApiOperation(value = "批量编辑方案分类的分类列表 查询分类名称所存在的菜谱")
    @GetMapping("/list_item_type_and_plan_price")
    public Result<List<PlanPriceEditDTO>> listItemTypeAndPlanPrice(@RequestParam("typeName") String typeName) {
        log.info("查询分类名称所存在的菜谱 req={}", typeName);
        return Result.buildSuccessResult(itemClientService.listItemTypeAndPlanPrice(typeName));
    }


    /**
     * 批量编辑保存菜谱分类信息
     */
    @ApiOperation(value = "批量编辑保存菜谱分类信息")
    @PostMapping("/save_batch_edit_plan_price_type")
    public Result<Void> saveBatchEditPlanPriceType(@RequestBody PlanPriceEditReqDTO req) {
        log.info("批量编辑保存菜谱分类信息 req={}", JacksonUtils.writeValueAsString(req));
        // 防重复提交
        boolean getLock;
        try {
            // 判断是否获取了锁
            getLock = redisLock.lock(JacksonUtils.writeValueAsString(req));
            if (getLock) {
                itemClientService.saveBatchEditPlanPriceType(req);
            }
        } finally {
            redisLock.delete(JacksonUtils.writeValueAsString(req));
        }
        if (getLock) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVING_IN_PROGRESS));
        }
    }

}
