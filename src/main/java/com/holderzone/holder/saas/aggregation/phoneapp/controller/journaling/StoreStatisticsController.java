package com.holderzone.holder.saas.aggregation.phoneapp.controller.journaling;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.phoneapp.service.rpc.JournalRpcService;
import com.holderzone.saas.store.dto.journaling.req.StoreStatisticsAppReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.StoreStatisticsAppRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreStatisticsController
 * @date 2019/06/03 10:22
 * @description 门店统计
 * @program holder-saas-aggregation-phoneapp
 */
@RestController
@RequestMapping("/appStoreStatistics")
@Api(value = "app报表-门店统计",description = "app报表-门店统计")
@Slf4j
public class StoreStatisticsController {

    @Autowired
    JournalRpcService journalRpcService;

    @ApiOperation(value = "门店统计列表")
    @PostMapping("/list")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT,description = "门店统计列表")
    public Result<StoreStatisticsAppRespDTO> list(@RequestBody StoreStatisticsAppReqDTO storeStatisticsAppReqDTO){
        log.info("app-门店统计，入参：storeStatisticsAppReqDTO={}", JacksonUtils.writeValueAsString(storeStatisticsAppReqDTO));
        return Result.buildSuccessResult(journalRpcService.appStoreStatistics(storeStatisticsAppReqDTO));
    }



}
