package com.holderzone.holder.saas.aggregation.weixin.service.impl;


import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.WxCpService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.MemberMarketingClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxMpMessageHandlerClientService;
import com.holderzone.holder.saas.member.terminal.dto.feign.FeignModel;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.weixin.cp.StoreQrCodeDTO;
import com.holderzone.saas.store.enums.member.MemberFeignModelEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;


@Slf4j
@Service
@RequiredArgsConstructor
public class WxCpServiceImpl implements WxCpService {

    private final MemberMarketingClientService memberMarketingClientService;

    private final WxMpMessageHandlerClientService wxMpMessageHandlerClientService;

    @Override
    public String queryWxCpAuthorizeUrl(String t) {
        // 查询二维码参数
        String redirectParams = wxMpMessageHandlerClientService.qrRedirect(t);
        log.info("二维码参数:{}", redirectParams);
        if (StringUtils.isEmpty(redirectParams)) {
            throw new BusinessException(Constant.WX_CP_QR_CODE_PARAMS_ERROR);
        }
        StoreQrCodeDTO storeQrCodeDTO = JacksonUtils.toObject(StoreQrCodeDTO.class, redirectParams);
        FeignModel<String> wxCpAuthorizeUrlModel = memberMarketingClientService.queryWxCpAuthorizeUrl(storeQrCodeDTO.getOperSubjectGuid(), t);
        if (Objects.isNull(wxCpAuthorizeUrlModel)) {
            throw new BusinessException(Constant.WX_CP_AUTHORIZE_ERROR);
        }
        log.info("企微-查询用户授权地址, wxCpAuthorizeUrlModel:{}", JacksonUtils.writeValueAsString(wxCpAuthorizeUrlModel));
        if (wxCpAuthorizeUrlModel.getCode() != MemberFeignModelEnum.SUCCESS.getCode()) {
            throw new BusinessException(Constant.WX_CP_AUTHORIZE_ERROR);
        }
        return wxCpAuthorizeUrlModel.getData();
    }
}
