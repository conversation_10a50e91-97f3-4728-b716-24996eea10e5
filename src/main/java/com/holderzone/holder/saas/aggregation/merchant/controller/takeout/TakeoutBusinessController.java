package com.holderzone.holder.saas.aggregation.merchant.controller.takeout;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.entity.dto.BusinessOrderStatisticsExportRespDTO;
import com.holderzone.holder.saas.aggregation.merchant.entity.dto.BusinessTakeoutOrderExportRespDTO;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.takeout.TakeoutConsumerService;
import com.holderzone.holder.saas.aggregation.merchant.util.ExcelUtil;
import com.holderzone.saas.store.dto.takeaway.request.BusinessTakeoutOrderReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.BusinessTakeoutOrderDetailRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.BusinessTakeoutOrderRespDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsQueryDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/11/04
 */
@Api(description = "外卖服务B端接口")
@RestController
@RequestMapping("/takeout/business")
public class TakeoutBusinessController {

    private final TakeoutConsumerService takeoutConsumerService;

    @Autowired
    public TakeoutBusinessController(TakeoutConsumerService takeoutConsumerService) {
        this.takeoutConsumerService = takeoutConsumerService;
    }

    @ApiOperation(value = "获取订单统计分页数据")
    @PostMapping("/takeoutOrder/page")
    public Result<Page<BusinessTakeoutOrderRespDTO>> getTakeoutOrderPage(@RequestBody @Validated BusinessTakeoutOrderReqDTO reqDTO) {
        return Result.buildSuccessResult(takeoutConsumerService.getTakeoutOrderPage(reqDTO));
    }

    private final static String[] TAKEOUT_ORDER_HEADERS = {"订单号", "门店名称", "订单状态", "订单来源", "接单时间", "接单员", "姓名", "联系电话", "商品数量", "订单金额", "实付金额"};

    @PostMapping("/takeoutOrder/export")
    public void takeoutOrderExport(@RequestBody BusinessTakeoutOrderReqDTO reqDTO, HttpServletResponse response){
        reqDTO.setCurrentPage(1);
        reqDTO.setPageSize(Constants.MAX_EXPORT_SIZE);
        Page<BusinessTakeoutOrderRespDTO> orderStatisticsPage = takeoutConsumerService.getTakeoutOrderPage(reqDTO);
        if(orderStatisticsPage.getTotalCount() > Constants.MAX_EXPORT_SIZE){
            throw new BusinessException("最多只支持2万条数据导出");
        }
        List<BusinessTakeoutOrderRespDTO> data = orderStatisticsPage.getData();
        ExcelUtil<BusinessTakeoutOrderExportRespDTO> excelUtil = new ExcelUtil<>();
        List<BusinessTakeoutOrderExportRespDTO> exportList = Lists.newArrayList();
        data.forEach(d ->{
            BusinessTakeoutOrderExportRespDTO export = new BusinessTakeoutOrderExportRespDTO();
            BeanUtil.copyProperties(d, export);
            exportList.add(export);
        });
        try {
            response.setContentType("application/msexcel;charset=UTF-8");
            String substring = System.currentTimeMillis() + "_外卖订单统计" + ".xls";
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(substring,"UTF-8"));
            ServletOutputStream outputStream = response.getOutputStream();
            excelUtil.exportExcel(TAKEOUT_ORDER_HEADERS, exportList, outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "统计订单 订单明细")
    @GetMapping("/takeoutOrder/detail")
    public Result<BusinessTakeoutOrderDetailRespDTO> getTakeoutOrderDetail(@RequestParam("orderGuid") String orderGuid) {
        return Result.buildSuccessResult(takeoutConsumerService.getTakeoutOrderDetail(orderGuid));
    }
}
