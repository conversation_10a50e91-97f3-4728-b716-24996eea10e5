package com.holderzone.holder.saas.aggregation.merchant.util;

import com.holderzone.framework.exception.unchecked.ParameterException;
import org.springframework.util.StringUtils;

import java.util.Collection;

/**
 * <AUTHOR>
 * @version 1.0
 * @className Asserts
 * @date 2018/7/10 17:49
 * @description check-检查正确的范围/表达式
 * @program framework-resource-service
 */
public class Asserts {
    private static Asserts asserts;
    private Asserts(){
    }

    public static Asserts assets() {
        if (null != asserts) {
            return asserts;
        }
        return new Asserts();
    }
    public Asserts check(final boolean expression, final String message) {
        if (!expression) {
            throw new ParameterException(message);
        }
        return this;
    }

    public Asserts check(final boolean expression, final String message, final Object... args) {
        if (!expression) {
            throw new ParameterException(String.format(message, args));
        }
        return this;
    }

    public Asserts check(final boolean expression, final String message, final Object arg) {
        if (!expression) {
            throw new ParameterException(String.format(message, arg));
        }
        return this;
    }

    public Asserts notNull(final Object object, final String name) {
        if (object == null) {
            throw new ParameterException(name + " is null");
        }
        return this;
    }

    public Asserts notEmpty(final CharSequence s, final String name) {
        if (StringUtils.isEmpty(s)) {
            throw new ParameterException(name + " is empty");
        }
        return this;
    }

    public Asserts notEmpty(Collection<?> collection, String fieldName) {
        if (null == collection || collection.isEmpty()) {
            throw new ParameterException(fieldName + " is empty");
        }

        return this;
    }

    public Asserts lessLength(final CharSequence s, final int len, final String name) {
        if (StringUtils.isEmpty(s)) {
            throw new ParameterException(name + " is empty");
        }
        if (s.length() > len) {
            throw new ParameterException(name + "长度大于" + len);
        }
        return this;
    }

}
