package com.holderzone.holder.saas.aggregation.merchant.controller.erp;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp.RepertoryRpcService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.*;
import com.holderzone.saas.store.dto.erp.erpretail.resp.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @className WarehouseController
 * @date 2019-10-28 18:08:44
 * @description
 */
@Slf4j
@Api(tags = "盘点信息")
@RestController
@RequestMapping("/inventory")
public class InventoryController {

    @Autowired
    private RepertoryRpcService repertoryRpcService;

    @ApiOperation("新建盘点单")
    @PostMapping("/create_inventory")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "新建盘点单")
    public Result<String> createInventory(@RequestBody CreateInventoryReqDTO inventoryDTO) {
        log.info("新建盘点单入参：{}", JacksonUtils.writeValueAsString(inventoryDTO));
        return Result.buildSuccessResult(repertoryRpcService.createInventory(inventoryDTO));
    }

    @ApiOperation("查询盘点单")
    @PostMapping("/query_inventory_detail")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "查询盘点单")
    public Result<InventoryDetailRespDTO> queryInventoryDetail(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("查询盘点单入参：{}", JacksonUtils.writeValueAsString(singleDataDTO));
        if (StringUtils.isEmpty(singleDataDTO.getData())) {
            throw new BusinessException("盘点记录Guid不得为空");
        }
        return Result.buildSuccessResult(repertoryRpcService.queryInventoryDetail(singleDataDTO));
    }

    @ApiOperation("查询盘点单概览")
    @PostMapping("/query_inventory_overview")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "查询盘点单概览")
    public Result<Page<InventoryManageRespDTO>> queryInventoryOverView(@RequestBody @Validated InventoryOverviewReqDTO inventoryOverviewReqDTO) {
        log.info("查询盘点单概览：{}", JacksonUtils.writeValueAsString(inventoryOverviewReqDTO));
        return Result.buildSuccessResult(repertoryRpcService.queryInventoryOverView(inventoryOverviewReqDTO));
    }

    @ApiOperation("作废盘点单")
    @PostMapping("/invalid_inventory")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "作废盘点单")
    public Result<Boolean> invalidInventory(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("查询盘点单概览：{}", JacksonUtils.writeValueAsString(singleDataDTO));
        if (StringUtils.isEmpty(singleDataDTO.getData())) {
            throw new BusinessException("为找到对应的盘点单据信息");
        }
        return Result.buildSuccessResult(repertoryRpcService.invalidInventory(singleDataDTO));
    }
}
