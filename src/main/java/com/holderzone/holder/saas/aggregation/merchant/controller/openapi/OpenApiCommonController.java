package com.holderzone.holder.saas.aggregation.merchant.controller.openapi;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.OpenApiService;
import com.holderzone.saas.store.dto.report.openapi.TokenOpenReqDTO;
import com.holderzone.saas.store.dto.report.openapi.TokenOpenRespDTO;
import com.holderzone.sdk.annotation.RateLimit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 开放接口 -公共
 */
@Slf4j
@RestController
@RequestMapping("/openapi")
@RequiredArgsConstructor
public class OpenApiCommonController {

    private final OpenApiService openApiService;

    /**
     * 获取接口访问token
     */
    @PostMapping(value = "/token")
    @RateLimit(limitType = "token", limitCount = 100)
    public Result<TokenOpenRespDTO> getAccessToken(@RequestBody TokenOpenReqDTO reqDTO) {
        log.info("获取接口访问token入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        if (StringUtils.isEmpty(reqDTO.getEnterpriseGuid())) {
            throw new BusinessException("企业id不能为空");
        }
        try {
            return Result.buildSuccessResult(openApiService.getAccessToken(reqDTO.getEnterpriseGuid()));
        } catch (Exception e) {
            log.error("getAccessToken error", e);
            throw new BusinessException("系统繁忙稍后再试");
        }
    }
}
