package com.holderzone.holder.saas.aggregation.merchant.service.rpc.manage;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingDTO;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingQueryDTO;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingSaveDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/8
 * @since 1.8
 */
@Component
@FeignClient(name = "holder-saas-store-business", fallbackFactory = DataSettingClientService.DataSettingFallBack.class)
public interface DataSettingClientService {

    @PostMapping(value = "/data_setting/find_data_setting")
    List<DataSettingDTO> findDataSetting(@RequestBody @Valid DataSettingQueryDTO dataSettingQueryDTO);

    @PostMapping(value = "/data_setting/save_data_setting")
    Boolean saveDataSetting(@RequestBody @Valid DataSettingSaveDTO dataSettingSaveDTO);

    @Slf4j
    @Component
    class DataSettingFallBack implements FallbackFactory<DataSettingClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public DataSettingClientService create(Throwable throwable) {
            return new DataSettingClientService() {

                @Override
                public List<DataSettingDTO> findDataSetting(DataSettingQueryDTO dataSettingQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "findDataSetting", JacksonUtils.writeValueAsString(dataSettingQueryDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询数据取值设置异常");
                }

                @Override
                public Boolean saveDataSetting(DataSettingSaveDTO dataSettingSaveDTO) {
                    log.error(HYSTRIX_PATTERN, "findDataSetting", JacksonUtils.writeValueAsString(dataSettingSaveDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("保存数据取值设置异常");
                }
            };
        }
    }
}
