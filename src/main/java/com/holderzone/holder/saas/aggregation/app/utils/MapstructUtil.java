package com.holderzone.holder.saas.aggregation.app.utils;

import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemSynRespDTO;
import com.holderzone.saas.store.dto.item.resp.PadItemRespDTO;
import com.holderzone.saas.store.dto.item.resp.PadTypeRespDTO;
import com.holderzone.saas.store.dto.item.resp.TypeSynRespDTO;
import com.holderzone.saas.store.dto.journaling.req.DailyPrintReqDTO;
import com.holderzone.saas.store.dto.journaling.req.DailySaleDetailReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.ItemCountDetailDTO;
import com.holderzone.saas.store.dto.journaling.resp.PaymentReportRespDTO;
import com.holderzone.saas.store.dto.member.activity.ThirdActivityInfoAndRecordRespDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.print.content.PrintItemStatsDTO;
import com.holderzone.saas.store.dto.print.content.PrintTypeStatsDTO;
import com.holderzone.saas.store.dto.print.content.nested.PayDetailRecord;
import com.holderzone.saas.store.dto.table.OpenTableDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper
public interface MapstructUtil {

    MapstructUtil INSTANCE = Mappers.getMapper(MapstructUtil.class);


    CreateDineInOrderReqDTO openTableDTO2CreateDineInOrderReqDTO(OpenTableDTO openTableDTO);


    @Mapping(target = "reqType", source = "type")
    DailySaleDetailReqDTO dailyPrintReqDTO2DailySaleDetailReqDTO(DailyPrintReqDTO request);

    List<PrintTypeStatsDTO.ItemType> itemCountDetailList2itemTypeList(List<ItemCountDetailDTO> request);

    @Mappings({
            @Mapping(target = "quantity", source = "count"),
            @Mapping(target = "money", source = "amount")
    })
    PrintTypeStatsDTO.ItemType itemCountDetail2itemType(ItemCountDetailDTO request);

    List<PrintItemStatsDTO.Item> itemCountDetailList2itemStatsList(List<ItemCountDetailDTO> request);

    @Mappings({
            @Mapping(target = "quantity", source = "count"),
            @Mapping(target = "money", source = "amount"),
            @Mapping(target = "isWeight", expression = "java(java.util.Objects.equals(false,true))"),
            @Mapping(target = "isPackage", expression = "java(java.util.Objects.equals(false,true))")
    })
    PrintItemStatsDTO.Item itemCountDetailitemStats(ItemCountDetailDTO request);


    List<PayDetailRecord> paymentReportList2payDetailRecordList(List<PaymentReportRespDTO> request);

    @Mappings({
            @Mapping(target = "payName", source = "payWayName"),
            @Mapping(target = "salesAmount", source = "payFee", defaultValue = "0"),
            @Mapping(target = "refundAmount", source = "refundFee", defaultValue = "0"),
            @Mapping(target = "actuallyAmount", source = "actuallyFee", defaultValue = "0")
    })
    PayDetailRecord paymentReport2payDetailRecord(PaymentReportRespDTO request);

    PadTypeRespDTO typeSyn2PadType(TypeSynRespDTO typeSynRespDTO);

    List<PadTypeRespDTO> typeSynList2PadTypeList(List<TypeSynRespDTO> typeList);

    @Mappings({
            @Mapping(target = "skuList", source = "skuList"),
            @Mapping(target = "itemTypeName", source = "typeName")
    })
    PadItemRespDTO itemSyn2PadItem(ItemSynRespDTO itemSynRespDTO);

    List<PadItemRespDTO> itemSynList2PadItemList(List<ItemSynRespDTO> itemSynRespDTOList);

    @Mappings({
            @Mapping(target = "thirdActivityCodeList", ignore = true),
            @Mapping(target = "joinFee", ignore = true),
            @Mapping(target = "notJoinFee", ignore = true)
    })
    ThirdActivityInfoAndRecordRespDTO thirdActivityDTO2InfoAndRecordDTO(ThirdActivityRespDTO activityRespDTO);

    List<ThirdActivityInfoAndRecordRespDTO> thirdActivityDTOList2InfoAndRecordDTOList(List<ThirdActivityRespDTO> list);
}
