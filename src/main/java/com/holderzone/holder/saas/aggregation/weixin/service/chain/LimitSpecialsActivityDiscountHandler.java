package com.holderzone.holder.saas.aggregation.weixin.service.chain;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.weixin.assembler.MarketingActivityAssembler;
import com.holderzone.holder.saas.aggregation.weixin.context.DiscountContext;
import com.holderzone.holder.saas.aggregation.weixin.entity.bo.DiscountRuleBO;
import com.holderzone.holder.saas.aggregation.weixin.helper.MarketingActivityHelper;
import com.holderzone.holder.saas.aggregation.weixin.helper.PriceCalculateHelper;
import com.holderzone.holder.saas.aggregation.weixin.utils.map.DiscountMAP;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityDetailsVO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityItemVO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountRuleDTO;
import com.holderzone.saas.store.dto.trade.DiscountDTO;
import com.holderzone.saas.store.dto.weixin.deal.ActivitySelectDTO;
import com.holderzone.saas.store.dto.weixin.resp.MarketingActivityInfoRespDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.member.MarketActivityUnableTipEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.util.BigDecimalUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/26
 * @description 限时特价活动
 */
@Slf4j
@Component
@AllArgsConstructor
public class LimitSpecialsActivityDiscountHandler extends DiscountHandler {

    private final MarketingActivityHelper marketingActivityHelper;

    private final PriceCalculateHelper priceCalculateHelper;

    @Override
    void dealDiscount(DiscountContext context) {
        if (context.isRejectDiscount()) {
            return;
        }

        // 所有商品，备份商品数据
        List<DineInItemDTO> allItems = context.getAllItems();
        String allItemsJson = JacksonUtils.writeValueAsString(allItems);
        context.setBeforeSpecialsItems(JacksonUtils.toObjectList(DineInItemDTO.class, allItemsJson));
        context.getCalculateOrderRespDTO().setOrderSurplusFeeBySkipSpecials(context.getCalculateOrderRespDTO().getOrderSurplusFee());

        if (Boolean.FALSE.equals(context.getNeedLimitSpecialsMarketActivityList())) {
            return;
        }
        DiscountDTO memberGoodsGrouponDO = context.getDiscountTypeMap().get(type());
        DiscountFeeDetailDTO specialsActivity = DiscountMAP.INSTANCE.discountDO2DiscountFeeDetailDTO(memberGoodsGrouponDO);

        // 查询限时特价活动
        List<LimitSpecialsActivityDetailsVO> specialsActivityList = marketingActivityHelper.querySpecialsActivityList(WeixinUserThreadLocal.get());
        log.info("[限时特价活动]specialsActivityList={}", JacksonUtils.writeValueAsString(specialsActivityList));

        // 选中的活动信息
        String selectActivityGuid = context.getCalculateOrderDTO().getActivitySelectList().stream()
                .filter(s -> Objects.equals(DiscountTypeEnum.LIMIT_SPECIALS_ACTIVITY.getCode(), s.getActivityType()))
                .map(ActivitySelectDTO::getActivityGuid)
                .findFirst()
                .orElse(null);
        LimitSpecialsActivityDetailsVO activityDetailsVO = specialsActivityList.stream()
                .filter(a -> Objects.equals(selectActivityGuid, a.getGuid()))
                .findFirst()
                .orElse(null);
        boolean isShare = true;
        if (!ObjectUtils.isEmpty(activityDetailsVO)) {
            isShare = Objects.equals(BooleanEnum.TRUE.getCode(), activityDetailsVO.getRelationRule());
        }

        // 计算每个活动的优惠金额
        calculateActivityDiscountPrice(context, specialsActivityList, allItems, isShare, selectActivityGuid);

        // 进入结算页面时取优惠力度最大的活动默认选中
        activityDetailsVO = handleDefaultSelect(context, specialsActivityList, activityDetailsVO);
        log.info("[计算][最终确定的活动]activityDetailsVO={}", JacksonUtils.writeValueAsString(activityDetailsVO));

        // 限时特价活动计算
        calculateSpecialsActivity(context, activityDetailsVO, specialsActivity, allItems);

        context.getDiscountFeeDetailDTOS().add(specialsActivity);
        log.info("[限时特价活动]计算后订单剩余金额：{}，优惠金额：{}，菜品优惠：{}", context.getCalculateOrderRespDTO().getOrderSurplusFee(),
                specialsActivity.getDiscountFee(), JacksonUtils.writeValueAsString(context.getAllItems()));
    }

    private void calculateSpecialsActivity(DiscountContext context,
                                           LimitSpecialsActivityDetailsVO activityDetailsVO,
                                           DiscountFeeDetailDTO specialsActivity,
                                           List<DineInItemDTO> allItems) {
        DiscountRuleDTO ruleDTO = new DiscountRuleDTO();
        if (!ObjectUtils.isEmpty(activityDetailsVO)) {
            BigDecimal totalActivityDiscountPrice = priceCalculateHelper.calculateSpecialsActivityByMemberPrice(context, activityDetailsVO);
            if (BigDecimalUtil.greaterThanZero(totalActivityDiscountPrice)) {
                specialsActivity.setDiscountFee(BigDecimalUtil.setScale2(totalActivityDiscountPrice));
                ruleDTO.setActivityGuid(activityDetailsVO.getGuid());
                ruleDTO.setActivityName(activityDetailsVO.getName());
                ruleDTO.setLabelGuidList(activityDetailsVO.getLabelGuidList());

                // 选中活动返回
                ActivitySelectDTO selectDTO = new ActivitySelectDTO();
                selectDTO.setActivityGuid(activityDetailsVO.getGuid());
                selectDTO.setActivityType(DiscountTypeEnum.LIMIT_SPECIALS_ACTIVITY.getCode());
                context.getCalculateOrderRespDTO().getActivitySelectList().add(selectDTO);

                // 被限时特价覆盖的价格
                BigDecimal totalLimitDiscountPrice = BigDecimal.ZERO;
                if (context.isHasMember() && Boolean.TRUE.equals(context.getUseMemberPriceFlag())) {
                    totalLimitDiscountPrice = allItems.stream()
                        .map(DineInItemDTO::getLimitPrice)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                }

                // 订单剩余金额
                BigDecimal orderSurplusFee = context.getCalculateOrderRespDTO().getOrderSurplusFee()
                        .subtract(specialsActivity.getDiscountFee()).add(totalLimitDiscountPrice);
                context.getCalculateOrderRespDTO().setOrderSurplusFee(orderSurplusFee);
            } else {
                if (Boolean.TRUE.equals(context.getIsFirst())) {
                    // 去掉限时特价
                    specialsActivity.setDiscountFee(BigDecimal.ZERO);
                    // 取消默认选中
                    context.getCalculateOrderRespDTO().getActivitySelectList().removeIf(a ->
                            Objects.equals(a.getActivityType(), DiscountTypeEnum.LIMIT_SPECIALS_ACTIVITY.getCode()));
                }
            }
        }
        specialsActivity.setRule(JacksonUtils.writeValueAsString(ruleDTO));
    }

    /**
     * 进入结算页面时取优惠力度最大的活动默认选中
     */
    private LimitSpecialsActivityDetailsVO handleDefaultSelect(DiscountContext context,
                                                               List<LimitSpecialsActivityDetailsVO> specialsActivityList,
                                                               LimitSpecialsActivityDetailsVO activityDetailsVO) {
        log.info("[probe][handleDefaultSelect]activityDetailsVO={}", JacksonUtils.writeValueAsString(activityDetailsVO));
        if (ObjectUtils.isEmpty(activityDetailsVO) &&
                (!CollectionUtils.isEmpty(context.getCalculateOrderRespDTO().getActivityInfoList()) &&
                        Boolean.TRUE.equals(context.getIsFirst()))) {
            // 只从可用的活动里选择
            MarketingActivityInfoRespDTO activityInfoDTO = context.getCalculateOrderRespDTO().getActivityInfoList().stream()
                    .filter(MarketingActivityInfoRespDTO::isUseAble)
                    .max(Comparator.comparing(MarketingActivityInfoRespDTO::getDiscountPrice))
                    .orElse(null);
            log.info("[probe][handleDefaultSelect]activityInfoDTO={}", JacksonUtils.writeValueAsString(activityInfoDTO));
            if (!ObjectUtils.isEmpty(activityInfoDTO)) {
                LimitSpecialsActivityDetailsVO defaultActivityDetailsVO = specialsActivityList.stream()
                        .filter(a -> Objects.equals(activityInfoDTO.getActivityGuid(), a.getGuid()))
                        .findFirst()
                        .orElse(null);
                log.info("[probe][handleDefaultSelect]defaultActivityDetailsVO={}", JacksonUtils.writeValueAsString(defaultActivityDetailsVO));
                if (!ObjectUtils.isEmpty(defaultActivityDetailsVO)) {
                    activityDetailsVO = defaultActivityDetailsVO;
                    context.getDiscountRuleBO().setSpecialsActivityDetailsVO(defaultActivityDetailsVO);

                    Map<String, LimitSpecialsActivityDetailsVO> activityDetailsVOMap = specialsActivityList.stream()
                            .collect(Collectors.toMap(LimitSpecialsActivityDetailsVO::getGuid, Function.identity(), (v1, v2) -> v1));
                    context.getCalculateOrderRespDTO().getActivityInfoList().forEach(activity ->
                            setActivityListUseAble(activity, defaultActivityDetailsVO, activityDetailsVOMap));
                }
            }

        }
        return activityDetailsVO;
    }

    private void setActivityListUseAble(MarketingActivityInfoRespDTO activity,
                                        LimitSpecialsActivityDetailsVO defaultActivityDetailsVO,
                                        Map<String, LimitSpecialsActivityDetailsVO> activityDetailsVOMap) {
        // 默认选中的活动设置为可用
        if (Objects.equals(defaultActivityDetailsVO.getGuid(), activity.getActivityGuid())) {
            activity.setUseAble(true);
            activity.setUnUseReason(null);
        } else {
            // 除开默认选中活动之外的其他活动根据情况设置状态
            LimitSpecialsActivityDetailsVO detailsVO = activityDetailsVOMap.get(activity.getActivityGuid());
            if (!ObjectUtils.isEmpty(detailsVO)) {
                if (Objects.equals(BooleanEnum.TRUE.getCode(), defaultActivityDetailsVO.getRelationRule())) {
                    if (Objects.equals(BooleanEnum.TRUE.getCode(), detailsVO.getRelationRule())) {
                        activity.setUseAble(true);
                        activity.setUnUseReason(null);
                    } else {
                        activity.setUseAble(false);
                        activity.setUnUseReason(Constant.OTHER_DISCOUNT_SHARE);
                    }
                } else {
                    activity.setUseAble(false);
                    activity.setUnUseReason(Constant.OTHER_DISCOUNT_SHARE);
                }
            }
        }
    }

    /**
     * 计算每个活动的优惠金额
     */
    private void calculateActivityDiscountPrice(DiscountContext context,
                                                List<LimitSpecialsActivityDetailsVO> specialsActivityList,
                                                List<DineInItemDTO> allItems,
                                                boolean isShare,
                                                String selectActivityGuid) {
        List<String> itemGuidList = allItems.stream()
                .map(DineInItemDTO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        List<String> parentGuidList = priceCalculateHelper.queryParentGuid(itemGuidList);
        itemGuidList.addAll(parentGuidList);
        List<MarketingActivityInfoRespDTO> activityInfoList = new ArrayList<>();
        specialsActivityList.forEach(activity -> {
            // 命中的商品
            List<LimitSpecialsActivityItemVO> activityItemVOList =
                    MarketingActivityAssembler.getLimitSpecialsActivityItemVOS(activity, itemGuidList);
            log.info("[命中的商品]activityItemVOList={}", JacksonUtils.writeValueAsString(activityItemVOList));
            if (CollectionUtils.isEmpty(activityItemVOList)) {
                log.warn("订单中没有该活动的商品,activity={},name={}", activity.getGuid(), activity.getName());
                return;
            }
            Map<String, LimitSpecialsActivityItemVO> activityItemVOMap = activityItemVOList.stream()
                    .collect(Collectors.toMap(LimitSpecialsActivityItemVO::getCommodityId, Function.identity(), (v1, v2) -> v1));

            Map<String, BigDecimal> skuDiscountPriceMap = getSpecialsTotalPrice(allItems, activityItemVOMap);

            // 构建返回的活动列表
            MarketingActivityInfoRespDTO infoRespDTO = getActivityInfoRespDTO(context, activity,
                    skuDiscountPriceMap, isShare, selectActivityGuid);
            activityInfoList.add(infoRespDTO);
        });
        log.info("[限时特价]返回活动列表={}", JacksonUtils.writeValueAsString(activityInfoList));
        context.getCalculateOrderRespDTO().getActivityInfoList().addAll(activityInfoList);
    }

    @NotNull
    private Map<String, BigDecimal> getSpecialsTotalPrice(List<DineInItemDTO> allItems,
                                             Map<String, LimitSpecialsActivityItemVO> activityItemVOMap) {
        List<DineInItemDTO> joinActivityItemList = allItems.stream()
                .filter(i -> activityItemVOMap.containsKey(i.getItemGuid())
                        || activityItemVOMap.containsKey(i.getParentGuid()))
                .collect(Collectors.toList());
        Map<String, BigDecimal> skuDiscountPriceMap = new HashMap<>();
        for (DineInItemDTO itemInfoDTO : joinActivityItemList) {
            String itemMapKey = StringUtils.hasText(itemInfoDTO.getParentGuid()) ?
                    itemInfoDTO.getParentGuid() : itemInfoDTO.getItemGuid();
            LimitSpecialsActivityItemVO activityItemVO = activityItemVOMap.get(itemMapKey);
            if (ObjectUtils.isEmpty(activityItemVO)) {
                log.warn("该商品没有匹配的活动,itemGuid={}", itemInfoDTO.getItemGuid());
                continue;
            }
            itemInfoDTO.setSpecialsActivityGuid(activityItemVO.getActivityGuid());
            BigDecimal singleActivityDiscountPrice = priceCalculateHelper.calculateSpecialsActivityDiscountPrice(itemInfoDTO, activityItemVO);
            BigDecimal memberPreferential = BigDecimalUtil.nonNullValue(itemInfoDTO.getMemberPreferential());
            if (BigDecimalUtil.greaterThan(memberPreferential, singleActivityDiscountPrice)) {
                singleActivityDiscountPrice = BigDecimal.ZERO;
            }
            skuDiscountPriceMap.put(itemInfoDTO.getSkuGuid(), singleActivityDiscountPrice);
        }
        return skuDiscountPriceMap;
    }

    @NotNull
    private MarketingActivityInfoRespDTO getActivityInfoRespDTO(DiscountContext context,
                                                                LimitSpecialsActivityDetailsVO activity,
                                                                Map<String, BigDecimal> skuDiscountPriceMap,
                                                                boolean isShare,
                                                                String selectActivityGuid) {
        BigDecimal specialsTotalPrice = skuDiscountPriceMap.values().stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
        MarketingActivityInfoRespDTO infoRespDTO = new MarketingActivityInfoRespDTO();
        infoRespDTO.setActivityGuid(activity.getGuid());
        infoRespDTO.setActivityName(activity.getName());
        infoRespDTO.setActivityType(DiscountTypeEnum.LIMIT_SPECIALS_ACTIVITY.getCode());
        infoRespDTO.setActivityRule(String.format("省%s元", specialsTotalPrice));
        infoRespDTO.setDiscountPrice(specialsTotalPrice.setScale(2, RoundingMode.HALF_UP));
        infoRespDTO.setUseAble(true);
        infoRespDTO.setSkuDiscountPriceMap(skuDiscountPriceMap);

        // 如果金额为0说明没有优惠，即没有参与活动
        if (!BigDecimalUtil.greaterThanZero(specialsTotalPrice)) {
            log.warn("[限时特价][互斥处理][活动优惠金额小于等于0]specialsTotalPrice={}", specialsTotalPrice);
            infoRespDTO.setUseAble(false);
            infoRespDTO.setUnUseReason(Constant.UN_FULL_CONDITION);
            return infoRespDTO;
        }

        // 选择了优惠券且该优惠券互斥/选择了优满减满折且该活动互斥
        DiscountRuleBO discountRuleBO = context.getDiscountRuleBO();
        if (Boolean.TRUE.equals(!discountRuleBO.getVolumeShareFlag())
                || Boolean.TRUE.equals(!discountRuleBO.getFullShareFlag())
                || (!ObjectUtils.isEmpty(discountRuleBO.getNthActivityDetailsVO())
                && Objects.equals(discountRuleBO.getNthActivityDetailsVO().getRelationRule(), BooleanEnum.FALSE.getCode()))) {
            log.warn("[限时特价][互斥处理][选择了优惠券且该优惠券互斥/选择了优满减满折且该活动互斥]volumeShareFlag={},fullShareFlag={}",
                    discountRuleBO.getVolumeShareFlag(), discountRuleBO.getFullShareFlag());
            infoRespDTO.setUseAble(false);
            infoRespDTO.setUnUseReason(Constant.OTHER_DISCOUNT_SHARE);
            return infoRespDTO;
        }

        // 非首次没选活动/选中了该活动
        if (Boolean.TRUE.equals(!context.getIsFirst()) && (ObjectUtils.isEmpty(selectActivityGuid) || Objects.equals(selectActivityGuid, activity.getGuid()))) {
            return checkMemberDiscountUseAble(context, specialsTotalPrice, isShare, infoRespDTO);
        }
        if (isShare) {
            if (Objects.equals(BooleanEnum.FALSE.getCode(), activity.getRelationRule())) {
                // 会员价与限时特价活动生效最低价，两种价格取优惠后更低的价格生效
                compareMarketingActivityInfoRespDTO(context, infoRespDTO, specialsTotalPrice);
            }
        } else {
            log.warn("[限时特价][互斥处理][活动互斥]");
            infoRespDTO.setUseAble(false);
            infoRespDTO.setUnUseReason(Constant.OTHER_DISCOUNT_SHARE);
        }
        return infoRespDTO;
    }

    private void compareMarketingActivityInfoRespDTO(DiscountContext context,
                                                     MarketingActivityInfoRespDTO infoRespDTO,
                                                     BigDecimal specialsTotalPrice) {
        // 比较会员价（该阶段只有会员价，会员折扣在后面）
        DiscountFeeDetailDTO singleMemberCard = context.getDiscountFeeDetailDTOS().stream()
                .filter(d -> Objects.equals(d.getDiscountType(), DiscountTypeEnum.SINGLE_MEMBER.getCode()))
                .findFirst()
                .orElse(null);
        // 没有会员价的情况
        if (ObjectUtils.isEmpty(singleMemberCard)) {
            log.warn("[限时特价][互斥处理][会员价不存在]");
            return;
        }

        // 会员优惠更佳，禁用限时特价活动
        if (BigDecimalUtil.greaterThanZero(singleMemberCard.getDiscountFee()) &&
                BigDecimalUtil.greaterThan(singleMemberCard.getDiscountFee(), specialsTotalPrice)) {
            log.warn("[限时特价][互斥处理][会员优惠更佳，禁用限时特价活动]memberDiscountFee={},specialsTotalPrice={}",
                    singleMemberCard.getDiscountFee(), specialsTotalPrice);
            infoRespDTO.setUseAble(false);
            infoRespDTO.setUnUseReason(Constant.OTHER_DISCOUNT_SHARE);
        }
    }

    private MarketingActivityInfoRespDTO checkMemberDiscountUseAble(DiscountContext context,
                                                                    BigDecimal specialsTotalPrice,
                                                                    boolean isShare,
                                                                    MarketingActivityInfoRespDTO infoRespDTO) {
        // 比较会员价（该阶段只有会员价，会员折扣在后面）
        DiscountFeeDetailDTO singleMemberCard = context.getDiscountFeeDetailDTOS().stream()
                .filter(d -> Objects.equals(d.getDiscountType(), DiscountTypeEnum.SINGLE_MEMBER.getCode()))
                .findFirst()
                .orElse(null);
        // 没有会员价的情况
        if (ObjectUtils.isEmpty(singleMemberCard)) {
            log.warn("[限时特价][互斥处理][会员价不存在]");
            return infoRespDTO;
        }

        // 会员优惠更佳，禁用限时特价活动
        if (!isShare) {
            if (BigDecimalUtil.greaterThanZero(singleMemberCard.getDiscountFee()) &&
                    BigDecimalUtil.greaterThan(singleMemberCard.getDiscountFee(), specialsTotalPrice)) {
                log.warn("[限时特价][互斥处理][会员优惠更佳]specialsTotalPrice={}", specialsTotalPrice);
                infoRespDTO.setUseAble(false);
                infoRespDTO.setUnUseReason(MarketActivityUnableTipEnum.OTHER_DISCOUNT.getView());
                return infoRespDTO;
            }
            log.warn("[限时特价][互斥处理][活动互斥]");
            infoRespDTO.setUseAble(false);
            infoRespDTO.setUnUseReason(Constant.OTHER_DISCOUNT_SHARE);
        }
        return infoRespDTO;
    }

    @Override
    Integer type() {
        return DiscountTypeEnum.LIMIT_SPECIALS_ACTIVITY.getCode();
    }

}
