package com.holderzone.holder.saas.aggregation.merchant.controller.report;

import com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.order.ReportExportClientService;
import com.holderzone.holder.saas.aggregation.merchant.util.DownloadExcelUtils;
import com.holderzone.saas.store.dto.report.query.ReportExportDTO;
import com.holderzone.saas.store.dto.report.resp.ExportRespDTO;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static com.holderzone.holder.saas.aggregation.merchant.util.ReportUtils.getParamString;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReportExportController
 * @date 2018/10/11 9:59
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Controller
@RequestMapping("/file")
@Deprecated
public class ReportExportController {

    private final ReportExportClientService reportExportClientService;

    private static final Logger logger = LoggerFactory.getLogger(ReportExportController.class);


    @Autowired
    public ReportExportController(ReportExportClientService reportExportClientService) {
        this.reportExportClientService = reportExportClientService;
    }

    @ApiOperation(value = "exportType类型对应不同的导出接口 详见文档")
    @PostMapping("/export/{exportType}")
    @Deprecated
    public void export(@PathVariable("exportType") Integer exportType, HttpServletRequest request, HttpServletResponse response) {
        String paramString = getParamString(request);
        logger.info("报表导出 exportType={},paramString={}", exportType, paramString);
        ExportRespDTO export = reportExportClientService.export(new ReportExportDTO(exportType, paramString));
        try {
            DownloadExcelUtils.fullExcel(response, export.getExcelName(), export.getHead(), export.getList(), export.getClzz(), export.getHeight());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("报表导出异常 e={}", e.getMessage());
        }
    }


}
