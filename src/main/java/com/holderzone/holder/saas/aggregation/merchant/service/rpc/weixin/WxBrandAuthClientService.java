package com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin;

import com.holderzone.saas.store.dto.weixin.req.WxOperSubjectBrandReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxSendShortMsgReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxUnBandReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxBrandAuthRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxBrandAuthClientService
 * @date 2019/03/01 18:00
 * @description 微信品牌公众号绑定ClientService
 * @program holder-saas-store
 */
@Component
@FeignClient(value = "holder-saas-store-weixin", fallbackFactory = WxBrandAuthClientService.FallBackService.class)
public interface WxBrandAuthClientService {

    @PostMapping("/wx_brand_auth/list_brand_auth")
    List<WxBrandAuthRespDTO> listWxBrand();

    @PostMapping("/wx_brand_auth/un_band_brand")
    Integer unBandBrand(WxUnBandReqDTO wxUnBandReqDTO);

    @PostMapping("/wx_brand_auth/get_by_brand_guid")
    WxBrandAuthRespDTO getByBrandGuid(String brandGuid);

    @PostMapping("/wx_brand_auth/send_message")
    String sendShortMessage(WxSendShortMsgReqDTO wxSendShortMsgReqDTO);

    @PostMapping("/wx_brand_auth/bind_subject")
    @ApiOperation("通过品牌guid绑定运营主体与微信公众号信息")
    Boolean bindSubject(@RequestBody WxOperSubjectBrandReqDTO wxOperSubjectBrandReqDTO);
    @Component
    @Slf4j
    class FallBackService implements FallbackFactory<WxBrandAuthClientService> {
        @Override
        public WxBrandAuthClientService create(Throwable throwable) {
            return new WxBrandAuthClientService() {
                @Override
                public List<WxBrandAuthRespDTO> listWxBrand() {
                    log.error("获取微信品牌公众号绑定列表失败，e{}", throwable.getMessage());
                    throw new RuntimeException("获取微信品牌公众号绑定列表失败");
                }

                @Override
                public Integer unBandBrand(WxUnBandReqDTO wxUnBandReqDTO) {
                    log.error("微信品牌公众号解绑失败，e{}", throwable.getMessage());
                    throw new RuntimeException("微信品牌公众号解绑失败");
                }

                @Override
                public WxBrandAuthRespDTO getByBrandGuid(String brandGuid) {
                    log.error("获取微信品牌公众号绑定信息失败，e{}", throwable.getMessage());
                    throw new RuntimeException("获取微信品牌公众号绑定信息失败");
                }

                @Override
                public String sendShortMessage(WxSendShortMsgReqDTO wxSendShortMsgReqDTO) {
                    log.error("获取微信品牌公众号解绑短信验证码失败，e{}", throwable.getMessage());
                    throw new RuntimeException("获取微信品牌公众号解绑短信验证码失败");
                }

                @Override
                public Boolean bindSubject(WxOperSubjectBrandReqDTO wxOperSubjectBrandReqDTO) {
                    log.error("品牌guid绑定运营主体与微信公众号信息失败，e{}", throwable.getMessage());
                    throw new RuntimeException("品牌guid绑定运营主体与微信公众号信息失败");
                }
            };
        }
    }
}
