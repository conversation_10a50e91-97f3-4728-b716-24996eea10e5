package com.holderzone.holder.saas.aggregation.weixin.utils;

import org.apache.logging.log4j.util.Strings;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringMessageUtils {


    static Pattern chinesePattern = Pattern.compile("[\u4e00-\u9fa5]");
    /**
     * 判断字符串中是否包含中文
     * @param str
     * 待校验字符串
     * @return 是否为中文
     * @warn 不能校验是否为中文标点符号
     */
    public static boolean isContainChinese(String str) {

        Matcher m = chinesePattern.matcher(str);
        return m.find();
    }


    /**
     * 获取字符串中的第一个数字
     */
    public static String getFirstNumber(String str1) {
        // 匹配数字的正则表达式
        Pattern pattern = Pattern.compile("\\d+");
        // 匹配字符串
        Matcher matcher1 = pattern.matcher(str1);
        if (matcher1.find()) {
            return matcher1.group();
        }
        return Strings.EMPTY;
    }


}
