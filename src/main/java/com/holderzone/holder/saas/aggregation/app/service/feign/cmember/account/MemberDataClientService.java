package com.holderzone.holder.saas.aggregation.app.service.feign.cmember.account;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.holder.saas.member.terminal.dto.statistics.RequestMemberDaily;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponseConsumpStatis;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponseRechargeStatis;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@Component
@FeignClient(name = "holder-saas-member-terminal", fallbackFactory = MemberDataClientService.MemberDataClientServiceFallback.class)
public interface MemberDataClientService {

    @PostMapping("/hsmdc/store/business/memberConsumptionDaily")
    ResponseConsumpStatis queryOnlyConsumption(@RequestBody RequestMemberDaily reqDTO);

    @PostMapping("hsmdc/store/business/memberRechargeDaily")
    ResponseRechargeStatis queryOnlyRecharge(@RequestBody RequestMemberDaily reqDTO);

    @Component
    class MemberDataClientServiceFallback implements FallbackFactory<MemberDataClientService> {

        private static final Logger logger = LoggerFactory.getLogger(MemberDataClientService.MemberDataClientServiceFallback.class);

        @Override
        public MemberDataClientService create(Throwable throwable) {

            return new MemberDataClientService() {

                @Override
                public ResponseConsumpStatis queryOnlyConsumption(RequestMemberDaily reqDTO) {
                    logger.error("获取会员消费日报调用异常e={}", throwable.getMessage());
                    throw new ParameterException("获取会员消费日报调用异常");
                }

                @Override
                public ResponseRechargeStatis queryOnlyRecharge(RequestMemberDaily reqDTO) {
                    logger.error("获取会员充值日报调用异常e={}", throwable.getMessage());
                    throw new ParameterException("获取会员充值日报调用异常");
                }
            };
        }
    }
}