package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member;

import com.fasterxml.jackson.databind.ser.Serializers;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.member.common.BaseMemberDTO;
import com.holderzone.saas.store.dto.member.grade.AccountValidityDTO;
import com.holderzone.saas.store.dto.member.grade.MemberGradeDTO;
import com.holderzone.saas.store.dto.member.request.MemberCardsReqDTO;
import com.holderzone.saas.store.dto.member.request.MemberIntegralReqDTO;
import com.holderzone.saas.store.dto.member.request.MemberListReqDTO;
import com.holderzone.saas.store.dto.member.request.MemberPayRecordReqDTO;
import com.holderzone.saas.store.dto.member.response.*;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberClientService
 * @date 2018/09/20 9:50
 * @description //TODO
 * @program holder-saas-store-order
 */
@Component
@FeignClient(name = "holder-saas-store-member",fallbackFactory =MemberGradeClientService.MemberGradeClientServiceFallBack.class)
public interface MemberGradeClientService {

    @PostMapping("/grade/update_account_validity")
    Boolean updateAccountValidity(AccountValidityDTO accountValidityDTO);

    @PostMapping("/grade/get_account_validity")
    AccountValidityDTO getAccountValidity();

    @PostMapping("/grade/add_member_grade")
    Boolean addMemberGrade(MemberGradeDTO memberGradeDTO);

    @PostMapping("/grade/update_member_grade")
    Boolean updateMemberGrade(MemberGradeDTO memberGradeDTO);

    @PostMapping("/grade/member_grade_list")
    List<MemberGradeDTO> memberGradeList(BaseDTO baseDTO);

    @PostMapping("/grade/delete_member_grade")
    Boolean deleteMemberGrade(MemberGradeDTO memberGradeDTO);

    @PostMapping("/grade/member_grade_detail")
    MemberGradeDTO memberGradeDetail(MemberGradeDTO memberGradeDTO);

    @Component
    class MemberGradeClientServiceFallBack implements FallbackFactory<MemberGradeClientService> {

        private static final Logger logger = LoggerFactory.getLogger(MemberListClientService.MemberListServiceFallBack.class);

        @Override
        public MemberGradeClientService create(Throwable throwable) {
            return new MemberGradeClientService(){


                @Override
                public Boolean updateAccountValidity(AccountValidityDTO accountValidityDTO) {
                    logger.error("会员有效期设置失败，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public AccountValidityDTO getAccountValidity() {
                    logger.error("会员有效期失败，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean addMemberGrade(MemberGradeDTO memberGradeDTO) {
                    logger.error("新增会员等级失败，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean updateMemberGrade(MemberGradeDTO memberGradeDTO) {
                    logger.error("修改会员等级失败，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<MemberGradeDTO> memberGradeList(BaseDTO baseDTO) {
                    logger.error("会员等级列表失败，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean deleteMemberGrade(MemberGradeDTO memberGradeDTO) {
                    logger.error("删除会员等级失败，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public MemberGradeDTO memberGradeDetail(MemberGradeDTO memberGradeDTO) {
                    logger.error("获取会员等级详情失败，msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }
            };
        }
    }
}
