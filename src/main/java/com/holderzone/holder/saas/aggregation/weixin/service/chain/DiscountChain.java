package com.holderzone.holder.saas.aggregation.weixin.service.chain;

import com.holderzone.holder.saas.aggregation.weixin.context.DiscountContext;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 优惠计算 责任链
 */
@Component
@AllArgsConstructor
public class DiscountChain {

    private final MemberDiscountHandler memberDiscountHandler;

    private final MemberGrouponDiscountHandler memberGrouponDiscountHandler;

    private final GoodsGrouponDiscountHandler goodsGrouponDiscountHandler;

    private final SingleMemberDiscountHandler singleMemberDiscountHandler;

    private final ActivityDiscountHandler activityDiscountHandler;

    private final LimitSpecialsActivityDiscountHandler specialsActivityDiscountHandler;

    private final NthActivityDiscountHandler nthActivityDiscountHandler;

    @PostConstruct
    public void init() {
        // 商品券
        goodsGrouponDiscountHandler.setNextDiscountHandler(singleMemberDiscountHandler);
        // 会员价
        singleMemberDiscountHandler.setNextDiscountHandler(specialsActivityDiscountHandler);
        // 限时特价
        specialsActivityDiscountHandler.setNextDiscountHandler(memberGrouponDiscountHandler);
        // 代金券
        memberGrouponDiscountHandler.setNextDiscountHandler(memberDiscountHandler);
        // 会员折扣
        memberDiscountHandler.setNextDiscountHandler(nthActivityDiscountHandler);
        // 第N份优惠
        nthActivityDiscountHandler.setNextDiscountHandler(activityDiscountHandler);
        // 满减满折
    }

    public void doDiscount(DiscountContext context) {
        goodsGrouponDiscountHandler.doDiscount(context);
    }
}
