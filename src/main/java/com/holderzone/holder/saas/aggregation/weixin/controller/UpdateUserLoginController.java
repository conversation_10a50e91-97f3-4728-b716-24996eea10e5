package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.UpdateUserLoginClientService;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.resp.UpdateMemberLoginRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UpdateUserLoginController
 * @date 2019/09/12 10:57
 * @description //TODO
 * @program holder-saas-aggregation-merchant
 */
@Slf4j
@RestController
@RequestMapping("/wx_open")
@Api(description = "修改登录状态接口")
public class UpdateUserLoginController {

    @Autowired
    UpdateUserLoginClientService updateUserLoginClientService;

    @PostMapping("/update_login")
    @ApiOperation(value = "修改登录状态",notes = "false:未登录  true：登录")
    public Result<UpdateMemberLoginRespDTO> updateUserLogin(@RequestBody WxStoreConsumerDTO wxStoreConsumerDTO){
        log.info("查询入参{}", JacksonUtils.writeValueAsString(wxStoreConsumerDTO));
        if(wxStoreConsumerDTO.getIsLogin()==null) {
        	wxStoreConsumerDTO.setIsLogin(false);
        }
        boolean updateUserLogin = updateUserLoginClientService.updateUserLogin(wxStoreConsumerDTO);
        if(!updateUserLogin) {
        	return Result.buildOpFailedResult((wxStoreConsumerDTO.getIsLogin()?"登录":"退出")+"失败,请稍后再试!");
        }
        return Result.buildSuccessResult(new UpdateMemberLoginRespDTO(wxStoreConsumerDTO.getIsLogin()));
    }
}