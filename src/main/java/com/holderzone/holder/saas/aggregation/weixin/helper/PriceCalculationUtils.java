package com.holderzone.holder.saas.aggregation.weixin.helper;

import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.context.DiscountContext;
import com.holderzone.holder.saas.aggregation.weixin.entity.bo.DiscountRuleBO;
import com.holderzone.holder.saas.aggregation.weixin.service.ItemService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.HsmTerminalServiceClient;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.member.terminal.dto.volume.*;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberSessionDTO;
import com.holderzone.saas.store.enums.member.VolumeVerifyEnum;
import com.holderzone.saas.store.util.BigDecimalUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 价格计算工具类
 */
@Component
@Slf4j
@AllArgsConstructor
public class PriceCalculationUtils {

    private final HsmTerminalServiceClient terminalServiceClient;

    private final ItemService itemService;


    /**
     * 会员优惠券计算
     */
    public List<RequestDishInfo> dealWithVolume(DiscountContext context, List<DineInItemDTO> allItems) {
        List<RequestDishInfo> dishInfoList = DineInItemHelper.dineInItem2DishList(allItems);
        // 设置商品父级信息
        itemService.setParentDishSkuInfo(dishInfoList);
        // 处理会员价
        dealWithRequestDishInfoByLadder(dishInfoList);
        List<String> volumeCodes = context.getCalculateOrderDTO().getVolumeCodes();
        if (volumeCodes == null) {
            volumeCodes = new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(volumeCodes)) {
            return Lists.newArrayList();
        }

        DiscountRuleBO discountRuleBO = context.getDiscountRuleBO();
        ResponseVolumeCalculate result;
        if (Objects.equals(discountRuleBO.getVerify(), VolumeVerifyEnum.QUERY.getCode())) {
            // 走查询的接口，但是不验券
            RequestVolumeCalculate volumeCalculateReq = new RequestVolumeCalculate();
            volumeCalculateReq.setVolumeCodeList(volumeCodes);
            volumeCalculateReq.setStoreGuid(UserContextUtils.getStoreGuid());
            volumeCalculateReq.setDishInfoDTOList(dishInfoList);
            volumeCalculateReq.setHasMemberPrice(context.getUseMemberPriceFlag());
            log.info("[会员优惠券][查询],volumeCalculateReq={}", JacksonUtils.writeValueAsString(volumeCalculateReq));
            result = terminalServiceClient.calculateDiscount(volumeCalculateReq);
            log.info("[会员优惠券][查询]返回结果={}", JacksonUtils.writeValueAsString(result));
            // 将错误原因放入返回值中
            context.getCalculateOrderRespDTO().setTip(result.getTip());
            return result.getDishInfoDTOList();
        }

        if (Objects.equals(discountRuleBO.getVerify(), VolumeVerifyEnum.VERIFY.getCode())) {
            // 验券
            RequestVolumeConsume volumeConsumeReqDTO = new RequestVolumeConsume();
            volumeConsumeReqDTO.setStoreGuid(UserContextUtils.getStoreGuid());
            volumeConsumeReqDTO.setHasMemberPrice(discountRuleBO.getHasMemberPrice());
            // 此时消费id一定为空
            volumeConsumeReqDTO.setMemberConsumptionGuid(null);
            volumeConsumeReqDTO.setVolumeCodes(volumeCodes);
            UserMemberSessionDTO memberSession = context.getUserMemberSession();
            volumeConsumeReqDTO.setMemberInfoGuid(memberSession.getMemberInfoGuid());
            volumeConsumeReqDTO.setMemberName(memberSession.getNickName());
            volumeConsumeReqDTO.setOrderNumber(context.getOrderGuid());
            volumeConsumeReqDTO.setRequestDishInfoList(dishInfoList);
            log.info("[会员优惠券][验券],volumeConsumeReqDTO={}", JacksonUtils.writeValueAsString(volumeConsumeReqDTO));
            ResponseVolumeConsume consume = terminalServiceClient.consume(volumeConsumeReqDTO);
            log.warn("[会员优惠券][验券]返回结果={}", JacksonUtils.writeValueAsString(consume));
            context.setMemberConsumptionGuid(consume.getMemberConsumptionGuid());
            return consume.getRequestDishInfoList();
        }

        // 撤销
        if (Objects.equals(discountRuleBO.getVerify(), VolumeVerifyEnum.CANCEL.getCode())) {
            // 撤销验券
            RequestVolumeCancel cancelReqDTO = new RequestVolumeCancel();
            String volumeCode = context.getCalculateOrderDTO().getVolumeCodes().get(0);
            cancelReqDTO.setVolumeCode(volumeCode);
            cancelReqDTO.setStoreGuid(UserContextUtils.getStoreGuid());
            cancelReqDTO.setMemberConsumptionGuid(context.getMemberConsumptionGuid());
            cancelReqDTO.setNum(1);
            cancelReqDTO.setRequestDishInfoList(dishInfoList);
            log.warn("[会员优惠券][撤销验券]，volumeCode={},cancelReqDTO={}", volumeCode, JacksonUtils.writeValueAsString(cancelReqDTO));
            List<RequestDishInfo> cancel = terminalServiceClient.cancel(volumeCode, cancelReqDTO);
            log.warn("[会员优惠券][撤销验券]返回结果={}", JacksonUtils.writeValueAsString(cancel));
            return Lists.newArrayList();
        }

        return Lists.newArrayList();
    }

    public void dealWithRequestDishInfoByLadder(List<RequestDishInfo> requestDishInfos) {
        // 订单总价
        BigDecimal totalPrice = requestDishInfos.stream()
                .map(dish -> dish.getDishOriginalUnitPrice().multiply(dish.getDishNum().subtract(dish.getGiftDishNum())
                        .compareTo(BigDecimal.ZERO) > 0 ? dish.getDishNum().subtract(dish.getGiftDishNum()) : BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        for (RequestDishInfo dishInfo : requestDishInfos) {
            if (ObjectUtils.isEmpty(dishInfo.getDishMemberPrice())) {
                log.warn("商品会员价为空");
                continue;
            }
            // 支付原价
            BigDecimal payPrice = dishInfo.getDishOriginalUnitPrice().multiply(dishInfo.getDishNum());
            // 会员价总优惠金额
            BigDecimal gapFee = dishInfo.getDishOriginalUnitPrice().subtract(dishInfo.getDishMemberPrice()).multiply(dishInfo.getDishNum());
            log.warn("payPrice={},totalPrice={}", payPrice, totalPrice);
            if (payPrice.compareTo(gapFee) > 0) {
                dishInfo.setDishMemberPrice(payPrice.subtract(gapFee));
            } else {
                dishInfo.setDishMemberPrice(BigDecimal.ZERO);
            }
        }
    }

    public void dealwithVolumeByDiscount(List<DineInItemDTO> allItems, List<RequestDishInfo> dishInfoDTOList) {
        if (CollectionUtils.isNotEmpty(dishInfoDTOList)) {
            Map<String, DineInItemDTO> orderItemDTOAllMap = allItems.stream()
                    .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (entity1, entity2) -> entity1));
            dishInfoDTOList.forEach(dish -> {
                if (dish.getDiscountMoney() != null) {
                    DineInItemDTO dineInItemDTO = orderItemDTOAllMap.get(dish.getOrderItemGuid());
                    if (dineInItemDTO != null && dish.getDiscountMoney() != null && dish.getDiscountMoney()
                            .floatValue() > 0) {
                        dineInItemDTO.setDiscountTotalPrice(dineInItemDTO.getDiscountTotalPrice().subtract(dish.getDiscountMoney()));
                    }
                }
            });
        }
    }
}
