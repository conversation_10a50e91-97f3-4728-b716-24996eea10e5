package com.holderzone.holder.saas.aggregation.merchant.controller.report.order;


import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.order.OrderRecoveryService;
import com.holderzone.saas.store.dto.report.OrderRecoveryDTO;
import com.holderzone.saas.store.dto.report.OrderRecoveryDetailDTO;
import com.holderzone.saas.store.dto.report.OrderRecoveryDetailQueryDTO;
import com.holderzone.saas.store.dto.report.query.OrderRecoveryQueryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/orderRecovery")
@Api(value = "orderRecovery",description = "聚合层订单报表-反结账报表",hidden = true)
@Deprecated
public class OrderRecoveryController {

    private static final Logger logger = LoggerFactory.getLogger(OrderRecoveryController.class);

    @Autowired
    private OrderRecoveryService orderRecoveryService;

    @RequestMapping(value = "/orderRecoveryList", method = RequestMethod.POST)
    @ApiOperation(value = "根据条件查询反结账列表")
    @Deprecated
    public Result<Page<OrderRecoveryDTO>> orderRecoveryList(@RequestBody OrderRecoveryQueryDTO query) {

        Page<OrderRecoveryDTO> page = orderRecoveryService.orderRecoveryList(query);

       return Result.buildSuccessResult(page);

    }

    @RequestMapping(value = "/queryDetailByOrderNo", method = RequestMethod.POST)
    @ApiOperation(value = "查询反结账详情")
    @Deprecated
    public Result<OrderRecoveryDetailDTO> queryDetailByOrderNo(@RequestBody OrderRecoveryDetailQueryDTO query) {

        OrderRecoveryDetailDTO orderRecoveryDetailDTO = orderRecoveryService
                .queryDetailByOrderNo(query);

        return Result.buildSuccessResult(orderRecoveryDetailDTO);
    }
}
