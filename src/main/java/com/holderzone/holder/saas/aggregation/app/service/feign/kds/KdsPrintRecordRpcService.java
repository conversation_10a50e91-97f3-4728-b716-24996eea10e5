package com.holderzone.holder.saas.aggregation.app.service.feign.kds;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.kds.req.KdsPrintRecordReqDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsPrintOrderDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsPrintRecordDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(name = "holder-saas-store-kds", fallbackFactory = KdsPrintRecordRpcService.FallbackFactoryImpl.class)
public interface KdsPrintRecordRpcService {

    @PostMapping("/kds_print_record/get_order")
    List<KdsPrintOrderDTO> getPrintOrder(@RequestBody KdsPrintRecordReqDTO printRecordReqDTO);

    @PostMapping("/kds_print_record/list")
    List<KdsPrintRecordDTO> listRecord(@RequestBody KdsPrintRecordReqDTO printRecordReqDTO);

    @PostMapping("/kds_print_record/update_status")
    void updateStatus(@RequestBody KdsPrintRecordReqDTO printRecordReqDTO);

    @PostMapping("/kds_print_record/delete")
    void deleteRecord(@RequestBody KdsPrintRecordReqDTO printRecordReqDTO);

    @PostMapping("/kds_print_record/batch_delete")
    void batchDeleteRecord(@RequestBody KdsPrintRecordReqDTO printRecordReqDTO);

    @Slf4j
    @Component
    class FallbackFactoryImpl implements FallbackFactory<KdsPrintRecordRpcService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public KdsPrintRecordRpcService create(Throwable throwable) {

            return new KdsPrintRecordRpcService() {

                @Override
                public List<KdsPrintOrderDTO> getPrintOrder(KdsPrintRecordReqDTO printRecordReqDTO) {
                    log.error(HYSTRIX_PATTERN, "getPrinterOrder",
                            JacksonUtils.writeValueAsString(printRecordReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<KdsPrintRecordDTO> listRecord(KdsPrintRecordReqDTO printRecordReqDTO) {
                    log.error(HYSTRIX_PATTERN, "listRecord",
                            JacksonUtils.writeValueAsString(printRecordReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void updateStatus(KdsPrintRecordReqDTO printRecordReqDTO) {
                    log.error(HYSTRIX_PATTERN, "updateStatus",
                            JacksonUtils.writeValueAsString(printRecordReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void deleteRecord(KdsPrintRecordReqDTO printRecordReqDTO) {
                    log.error(HYSTRIX_PATTERN, "deleteRecord",
                            JacksonUtils.writeValueAsString(printRecordReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void batchDeleteRecord(KdsPrintRecordReqDTO printRecordReqDTO) {
                    log.error(HYSTRIX_PATTERN, "batchDeleteRecord",
                            JacksonUtils.writeValueAsString(printRecordReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
