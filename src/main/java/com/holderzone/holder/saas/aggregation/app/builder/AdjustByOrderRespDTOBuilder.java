package com.holderzone.holder.saas.aggregation.app.builder;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderItemRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustOrderDetailRespDTO;
import org.apache.commons.collections.CollectionUtils;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class AdjustByOrderRespDTOBuilder {


    /**
     * 构建返回信息
     */
    public static void build(AdjustByOrderRespDTO respDTO, AdjustOrderDetailRespDTO detailRespDTO) {
        // 追加调整单信息
        respDTO.setAdjustNo(detailRespDTO.getAdjustNo());
        respDTO.setAdjustPrice(detailRespDTO.getAdjustPrice());
        respDTO.setReason(detailRespDTO.getReason());
        respDTO.setCreateStaffName(detailRespDTO.getCreateStaffName());
        respDTO.setGmtCreate(detailRespDTO.getGmtCreate());

        // 组装调整单商品明细到订单明细中
        List<DineInItemDTO> adjustItemList = detailRespDTO.getAdjustItemList();
        Map<String, List<DineInItemDTO>> itemByItemGuidMap = adjustItemList.stream().collect(Collectors.groupingBy(DineInItemDTO::getGuid));

        List<AdjustByOrderItemRespDTO> orderItemList = respDTO.getOrderItemList();
        Iterator<AdjustByOrderItemRespDTO> iterator = orderItemList.iterator();
        while (iterator.hasNext()) {
            AdjustByOrderItemRespDTO item = iterator.next();
            List<DineInItemDTO> adjustItemByOrderItemGuidList = itemByItemGuidMap.get(item.getOrderItemGuid());
            if (CollectionUtils.isEmpty(adjustItemByOrderItemGuidList)) {
                iterator.remove();
                continue;
            }
            item.setAdjustItemList(adjustItemByOrderItemGuidList);
            item.setAdjustType(adjustItemByOrderItemGuidList.get(0).getAdjustType());
        }
    }

}
