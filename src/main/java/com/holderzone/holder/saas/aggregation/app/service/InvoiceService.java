package com.holderzone.holder.saas.aggregation.app.service;

import com.holderzone.saas.store.dto.invoice.*;

public interface InvoiceService {

    /**
     * 校验开票条件
     */
    ResponseVerifyInvoiceDTO receiptValidate(RequestValidateDTO requestValidateDTO);

    /**
     * 短信登录
     * @return boolean
     */
    Boolean noteLogin(RequestValidateDTO requestValidateDTO);

    /**
     * 生成认证二维码
     * @param requestAuthenticationDTO requestAuthenticationDTO
     */
    ResponseAuthenticationUrlDTO authenticationUrl(RequestAuthenticationDTO requestAuthenticationDTO);

    /**
     * 通知认证结果
     * @param requestAuthenticationDTO requestAuthenticationDTO
     * @return IpassQueryAuthResultDTO
     */
    ResponseQueryAuthDTO queryAuthResult(RequestAuthenticationDTO requestAuthenticationDTO);

    /**
     * 生成订单发票二维码
     * @param requestAuthenticationDTO requestAuthenticationDTO
     */
    String generateOrderInvoice(RequestValidateDTO requestAuthenticationDTO);
}
