package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.rights;

/**
 * <AUTHOR>
 * @description 卡等级
 * @date 2019/5/21 16:31
 */

import com.alibaba.fastjson.JSONObject;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.dto.rights.request.GrowthLevelReqDTO;
import com.holderzone.holder.saas.member.dto.rights.response.GrowthLevelRespDTO;
import com.holderzone.holder.saas.member.dto.rights.response.MemberRightRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

@Component
@FeignClient(name = "holder-saas-member-account", fallbackFactory = CardLevelClientService.CardLevelClientServiceFallBack.class)
public interface CardLevelClientService {

    /**
     * 查询体系下的卡等级
     */
    @GetMapping(value = "/hsm/card/level/queryCardLevelBySystemManagementGuid", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    List<GrowthLevelRespDTO> queryCardLevelBySystemManagementGuid(
        @RequestParam("sytemMangementGuid") String sytemMangementGuid);


    /**
     * 保存/更新会员等级
     *
     * @param growthLevelReqDTO 会员等级对象
     */
    @ApiOperation("保存/更新会员等级")
    @PostMapping("/hsm/card/level/save")
    void saveOrUpdateLevel(@RequestBody GrowthLevelReqDTO growthLevelReqDTO);

    /**
     * 通过会员等级Guid进行删除（默认的等级不可以进行删除）
     *
     * @param levelGuid 会员等级Guid
     */
    @ApiOperation("删除会员等级")
    @DeleteMapping("/hsm/card/level/{levelGuid}")
    void delLevel(@PathVariable("levelGuid") String levelGuid);

    /**
     * 复制会员权益
     *
     * @param levelGuid 会员等级Guid
     */
    @ApiOperation("复制权益")
    @PostMapping("/hsm/card/level/copy/{levelGuid}")
    void copyRight(@PathVariable("levelGuid") String levelGuid);

    /**
     * 获取权益列表
     *
     * @param levelGuid 等级Guid
     * @return 权益列表
     */
    @ApiOperation("获取权益列表")
    @GetMapping(value = "/hsm/card/level/right/{levelGuid}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    List<MemberRightRespDTO> findRight(@PathVariable("levelGuid") String levelGuid);

    /**
     * 获取会员等级
     *
     * @param cardGuid 会员卡Guid
     * @return 会员等级
     */
    @ApiOperation("获取会员等级")
    @GetMapping(value = "/hsm/card/level/list/{cardGuid}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    List<GrowthLevelRespDTO> findListByCardGuid(@PathVariable("cardGuid") String cardGuid);


    @Slf4j
    @Component
    class CardLevelClientServiceFallBack implements FallbackFactory<CardLevelClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public CardLevelClientService create(Throwable throwable) {
            return new CardLevelClientService() {
                @Override
                public List<GrowthLevelRespDTO> queryCardLevelBySystemManagementGuid(
                    String sytemMangementGuid) {
                    log.error(HYSTRIX_PATTERN, "queryCardLevelBySystemManagementGuid",
                        sytemMangementGuid, ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询体系下卡等级失败");
                }

                @Override
                public void saveOrUpdateLevel(GrowthLevelReqDTO growthLevelReqDTO) {
                    log.error(HYSTRIX_PATTERN, "saveOrUpdateLevel",
                        JSONObject.toJSONString(growthLevelReqDTO),
                        ThrowableUtils.asString(throwable));
                    throw new BusinessException("新增或更新会员等级失败");
                }

                @Override
                public void delLevel(String levelGuid) {
                    log.error(HYSTRIX_PATTERN, "delLevel",
                        levelGuid, ThrowableUtils.asString(throwable));
                    throw new BusinessException("删除会员等级失败");
                }

                @Override
                public void copyRight(String levelGuid) {
                    log.error(HYSTRIX_PATTERN, "copyRight",
                        levelGuid, ThrowableUtils.asString(throwable));
                    throw new BusinessException("复制会员权益失败");
                }

                @Override
                public List<MemberRightRespDTO> findRight(String levelGuid) {
                    log.error(HYSTRIX_PATTERN, "findRight",
                        levelGuid, ThrowableUtils.asString(throwable));
                    throw new BusinessException("查找权益失败");
                }

                @Override
                public List<GrowthLevelRespDTO> findListByCardGuid(String cardGuid) {
                    log.error(HYSTRIX_PATTERN, "findListByCardGuid",
                        cardGuid, ThrowableUtils.asString(throwable));
                    throw new BusinessException("获取会员等级");
                }
            };
        }
    }

}
