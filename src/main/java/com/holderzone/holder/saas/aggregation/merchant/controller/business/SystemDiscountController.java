package com.holderzone.holder.saas.aggregation.merchant.controller.business;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.business.SystemDiscountClientService;
import com.holderzone.holder.saas.aggregation.merchant.util.ResultLocaleUtil;
import com.holderzone.saas.store.dto.business.SystemDiscountReqDTO;
import com.holderzone.saas.store.dto.trade.SystemDiscountDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SystemDiscountController
 * @date 2018/09/12 17:40
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Api(value = "系统省零配置")
@RestController
@RequestMapping("system")
public class SystemDiscountController {

    private static final Logger logger = LoggerFactory.getLogger(SystemDiscountController.class);

    private final SystemDiscountClientService systemDiscountClientService;

    @Autowired
    public SystemDiscountController(SystemDiscountClientService systemDiscountClientService) {
        this.systemDiscountClientService = systemDiscountClientService;
    }

    @ApiOperation(value = "新增系统省零配置", notes = "返回success成功其余失败")
    @PostMapping("/add")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "新增系统省零配置")
    public Result<String> addSystemDiscount(@RequestBody SystemDiscountDTO systemDiscountDTO) {
        logger.info("新增系统省零配置，systemDiscountDTO={}", JacksonUtils.writeValueAsString(systemDiscountDTO));
        String result = systemDiscountClientService.addSystemDiscount(systemDiscountDTO);
        if ("success".equals(result)) {
            return Result.buildSuccessResult(result);
        }
        return Result.buildOpFailedResult(ResultLocaleUtil.replacePrefixLocale(result, Constants.NEW_CREATION_FAILED));
    }

    @ApiOperation(value = "查询")
    @PostMapping("/getAll/{storeGuid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "查询")
    public Result<List<SystemDiscountDTO>> queryAll(@PathVariable("storeGuid") String storeGuid) {
        logger.info("查询系统省零，storeGuid={}", storeGuid);
        return Result.buildSuccessResult(systemDiscountClientService.queryAllSystemDis(storeGuid));
    }

    @ApiOperation(value = "更新", notes = "返回success 成功")
    @PostMapping("/update")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "更新")
    public Result<String> update(@RequestBody SystemDiscountDTO systemDiscountDTO) {
        logger.info("更新系统省零配置，systemDiscountDTO={}", JacksonUtils.writeValueAsString(systemDiscountDTO));
        String result = systemDiscountClientService.update(systemDiscountDTO);
        if ("success".equals(result)) {
            return Result.buildSuccessResult(result);
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.UPDATE_FAILED));
    }

    @ApiOperation(value = "启用", notes = "返回success 成功")
    @PostMapping("/enable")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "启用")
    public Result<String> enable(@RequestBody SystemDiscountDTO systemDiscountDTO) {
        systemDiscountDTO.setState(0);
        logger.info("更新系统省零配置，systemDiscountDTO={}", JacksonUtils.writeValueAsString(systemDiscountDTO));
        String result = systemDiscountClientService.update(systemDiscountDTO);
        if ("success".equals(result)) {
            return Result.buildSuccessResult(result);
        }
        return Result.buildOpFailedResult(result);
    }

    @ApiOperation(value = "禁用", notes = "返回success 成功")
    @PostMapping("/disable")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "禁用")
    public Result<String> disable(@RequestBody SystemDiscountDTO systemDiscountDTO) {
        systemDiscountDTO.setState(1);
        logger.info("更新系统省零配置，systemDiscountDTO={}", JacksonUtils.writeValueAsString(systemDiscountDTO));
        String result = systemDiscountClientService.update(systemDiscountDTO);
        if ("success".equals(result)) {
            return Result.buildSuccessResult(result);
        }
        return Result.buildOpFailedResult(result);
    }

    @ApiOperation(value = "删除系统省零规则", notes = "返回success 成功")
    @PostMapping("/delete/{storeGuid}/{systemDiscountGuid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "删除系统省零规则")
    public Result<String> delete(@PathVariable("storeGuid") String storeGuid, @PathVariable("systemDiscountGuid") String systemDiscountGuid) {
        logger.info("删除系统省零规则，storeGuid={}，systemDiscountGuid={}", storeGuid, systemDiscountGuid);
        String result = systemDiscountClientService.delete(storeGuid, systemDiscountGuid);
        if ("success".equals(result)) {
            return Result.buildSuccessResult(result);
        }
        return Result.buildOpFailedResult(result);
    }

    @PostMapping("/get_sys_discount")
    public Result<SystemDiscountDTO> getByStoreGuid(@RequestBody SystemDiscountReqDTO systemDiscountDTO) {
        return Result.buildSuccessResult(systemDiscountClientService.getByStoreGuid(systemDiscountDTO));
    }

    @PostMapping("/save_or_update")
    public Result<SystemDiscountDTO> saveOrUpdate(@RequestBody SystemDiscountReqDTO systemDiscountDTO) {
        return Result.buildSuccessResult(systemDiscountClientService.saveOrUpdate(systemDiscountDTO));
    }
}
