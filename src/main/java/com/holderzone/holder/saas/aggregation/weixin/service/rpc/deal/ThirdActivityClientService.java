package com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 第三方优惠活动
 */
@Component
@FeignClient(name = "holder-saas-member-terminal", fallbackFactory = ThirdActivityClientService.MemberFallback.class)
public interface ThirdActivityClientService {

    @ApiModelProperty("活动列表")
    @GetMapping("/hsm-third-activity/list")
    List<ThirdActivityRespDTO> list();

    @ApiOperation("通过活动guidList查询活动列表")
    @GetMapping("/hsm-third-activity/list/by_guid_list")
    List<ThirdActivityRespDTO> listByGuid(@RequestParam("guidList") List<String> guidList);

    @ApiOperation("通过活动thirdCode查询活动")
    @GetMapping("/hsm-third-activity/get/by_third_code")
    ThirdActivityRespDTO getByThirdCode(@RequestParam("thirdCode") String thirdCode,
                                        @RequestParam("thirdType") String thirdType,
                                        @RequestParam("activityName") String activityName);

    @Slf4j
    @Component
    class MemberFallback implements FallbackFactory<ThirdActivityClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ThirdActivityClientService create(Throwable cause) {
            return new ThirdActivityClientService() {
                @Override
                public List<ThirdActivityRespDTO> list() {
                    log.error(HYSTRIX_PATTERN, "list", null, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }


                @Override
                public List<ThirdActivityRespDTO> listByGuid(List<String> guidList) {
                    log.error(HYSTRIX_PATTERN, "listByGuid", guidList,
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public ThirdActivityRespDTO getByThirdCode(String thirdCode, String thirdType, String thirdName) {
                    log.error(HYSTRIX_PATTERN, "getByThirdCode", thirdCode + "-" + thirdType + "-" + thirdName,
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }
    }
}