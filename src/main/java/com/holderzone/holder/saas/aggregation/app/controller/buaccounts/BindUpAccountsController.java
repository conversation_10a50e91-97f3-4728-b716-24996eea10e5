package com.holderzone.holder.saas.aggregation.app.controller.buaccounts;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.app.service.BindUpAccountsService;
import com.holderzone.holder.saas.aggregation.app.service.TableService;
import com.holderzone.holder.saas.aggregation.app.service.feign.organization.OrgFeignClient;
import com.holderzone.saas.store.dto.store.store.BindupAccountsStatusDTO;
import com.holderzone.saas.store.dto.store.store.BindupAccountsTips;
import com.holderzone.saas.store.dto.store.table.TableBuAccountsDTO;
import com.holderzone.saas.store.dto.store.table.TableStatusDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.reserve.api.dto.TableOrderReserveDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * 扎帐业务信息
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping
@Api(tags = "扎帐代码Api")
public class BindUpAccountsController {

    @Resource
    private TableService tableService;

    @Resource
    private BindUpAccountsService bindUpAccountsService;

    @Resource
    private OrgFeignClient orgFeignClient;


    @ApiOperation("扎帐接口")
    @PostMapping("/bindupaccounts/table/buaccounts")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "扎帐接口", action = OperatorType.SELECT)
    public Result<BindupAccountsStatusDTO> bindUpAccounts(@RequestBody TableBasicQueryDTO tableBasicQueryDTO){
        Long requestTimestamp = tableBasicQueryDTO.getRequestTimestamp();
        if (requestTimestamp != null) {
            long currentTimeMillis = System.currentTimeMillis();
            log.warn("企业：" + UserContextUtils.getEnterpriseName() + "门店：" + UserContextUtils.getStoreName() +
                    "请求发起时间：" + requestTimestamp + "收到请求时间：" + currentTimeMillis + "请求过程耗时：" + (currentTimeMillis -
                    requestTimestamp));
        }
        StopWatch stopWatch = new StopWatch();
        List<TableOrderReserveDTO> tableOrderReserveDTOS = tableService.queryTable(tableBasicQueryDTO, stopWatch);
        stopWatch.stop();
        //判断当前桌台是否存在占用情况
        BindupAccountsStatusDTO bindupAccountsDTO = new BindupAccountsStatusDTO();
        List<TableOrderReserveDTO> collect = tableOrderReserveDTOS.stream().filter(tableOrderReserveDTO -> tableOrderReserveDTO.getStatus() == 1).collect(Collectors.toList());
        List<TableStatusDTO> tableStatusDTOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(collect)) {
            for (TableOrderReserveDTO e : collect) {
                TableStatusDTO tableStatusDTO = new TableStatusDTO();
                tableStatusDTO.setStatus(e.getStatus());
                tableStatusDTO.setOpenTableTime(e.getOpenTableTime());
                tableStatusDTOS.add(tableStatusDTO);
            }
        }
        TableBuAccountsDTO tbDTO = new TableBuAccountsDTO();
        tbDTO.setStoreGuid(tableBasicQueryDTO.getStoreGuid());
        tbDTO.setUserGuid(tableBasicQueryDTO.getUserGuid());
        tbDTO.setUserName(tableBasicQueryDTO.getUserName());
        tbDTO.setTableStatusDTOS(tableStatusDTOS);
        HashMap<String, String> stringObjectHashMap = orgFeignClient.autoBindupAccounts(tbDTO);
        int status = Integer.parseInt(stringObjectHashMap.get("status"));
        if (status == 0) {
            bindupAccountsDTO.setCode(1L);
            return Result.buildSuccessResult(bindupAccountsDTO);
        }
        String buAccounts = stringObjectHashMap.get("data");
        bindupAccountsDTO.setCode(0L);
        bindupAccountsDTO.setBuAccounts(buAccounts);
        return Result.buildSuccessResult(bindupAccountsDTO);
    }




    @ApiOperation("门店登录扎帐验证")
    @PostMapping("/bindupaccounts/autologin/buaccounts")
    public Result<BindupAccountsTips> loginAutoBuAccounts(@RequestBody TableBasicQueryDTO tableBasicQueryDTO){
        BindupAccountsTips bindupAccountsTips = bindUpAccountsService.loginAutoBuAccounts(tableBasicQueryDTO);
        return  Result.buildSuccessResult(bindupAccountsTips);
    }
}
