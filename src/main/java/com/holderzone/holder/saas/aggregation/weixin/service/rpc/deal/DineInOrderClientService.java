package com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.OrderLockDTO;
import com.holderzone.saas.store.dto.order.inside.OrderGuidsDTO;
import com.holderzone.saas.store.dto.order.inside.OrderTableInfoDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.DineInOrderListReqDTO;
import com.holderzone.saas.store.dto.order.request.face.WeChatPayLockReqDTO;
import com.holderzone.saas.store.dto.order.request.face.WeChatPayReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineInOrderListRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = DineInOrderClientService.WxStoreDineInOrderFullback.class)
public interface DineInOrderClientService {
    String URL_FAST_PREFIX = "/fast_food";

    @PostMapping("/dine_in_order/create")
    CreateDineInOrderReqDTO creatOrder(CreateDineInOrderReqDTO creatOrderReqDTO);

    @PostMapping("/dine_in_order/batch_get_table_info")
    List<OrderTableInfoDTO> batchGetTableInfo(OrderGuidsDTO orderGuidsDTO);

    @PostMapping("/dine_in_order/update_remark")
    Boolean updateRemark(CreateDineInOrderReqDTO createDineInOrderReqDTO);

    @PostMapping("/dine_in_order/update_guest_count")
    Boolean updateGuestCount(CreateDineInOrderReqDTO createDineInOrderReqDTO);

    @PostMapping("/dine_in_order/cancel")
    Boolean cancelOrder(CancelOrderReqDTO cancelOrderReqDTO);

    @PostMapping("/dine_in_order/get_order_detail")
    DineinOrderDetailRespDTO getOrderDetail(SingleDataDTO singleDataDTO);

    @PostMapping("/dine_in_order/print_pre_bill")
    Boolean printPreBill(SingleDataDTO singleDataDTO);

    @PostMapping("/dine_in_order/order_list")
    Page<DineInOrderListRespDTO> orderList(DineInOrderListReqDTO dineInOrderListReqDTO);

    @PostMapping("/we_chat/pay")
    Boolean pay(WeChatPayReqDTO weChatPayReqDTO);

    @PostMapping("/we_chat/prepay")
    Boolean prePay(@RequestBody WeChatPayLockReqDTO WeChatPayLockReqDTO);

    @PostMapping("/dine_in_order/order/lock")
    boolean lockOrder(@RequestBody OrderLockDTO orderLockDto);

    @ApiOperation(value = "去掉订单锁", notes = "去掉订单锁")
    @PostMapping("/dine_in_order/order/unlock")
    void unLockOrder(@RequestBody OrderLockDTO orderLockDto);

    /**
     * 查询是否有商品
     */
    @PostMapping("/we_chat/has_item")
    boolean hasItem(@RequestBody Long orderGuid);

    @PostMapping("/order_item/add_item")
    EstimateItemRespDTO batchAddItems(CreateDineInOrderReqDTO createDineInOrderReqDTO);

    @Component
    @Slf4j
    class WxStoreDineInOrderFullback implements FallbackFactory<DineInOrderClientService> {
        @Override
        public DineInOrderClientService create(Throwable throwable) {
            return new DineInOrderClientService() {

                @Override
                public CreateDineInOrderReqDTO creatOrder(CreateDineInOrderReqDTO creatOrderReqDTO) {
                    throwable.printStackTrace();
                    log.error("远程创建订单失败", JacksonUtils.writeValueAsString(throwable),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<OrderTableInfoDTO> batchGetTableInfo(OrderGuidsDTO orderGuidsDTO) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Boolean updateRemark(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Boolean updateGuestCount(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Boolean cancelOrder(CancelOrderReqDTO cancelOrderReqDTO) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public DineinOrderDetailRespDTO getOrderDetail(SingleDataDTO singleDataDTO) {
                    log.error("查询订单详情失败，singleDataDTO:{}", singleDataDTO);
                    return null;
                }

                @Override
                public Boolean printPreBill(SingleDataDTO singleDataDTO) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Page<DineInOrderListRespDTO> orderList(DineInOrderListReqDTO dineInOrderListReqDTO) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Boolean pay(WeChatPayReqDTO weChatPayReqDTO) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public Boolean prePay(WeChatPayLockReqDTO WeChatPayLockReqDTO) {
                    log.error("支付前校验失败:{}", WeChatPayLockReqDTO);
                    throw new BusinessException("支付前校验失败");
                }

                @Override
                public boolean lockOrder(OrderLockDTO orderLockDto) {
                    log.error("加锁失败:{}", orderLockDto);
                    throw new BusinessException("加锁失败");
                }

                @Override
                public void unLockOrder(OrderLockDTO orderLockDto) {
                    log.error("解锁失败:{}", orderLockDto);
                    throw new BusinessException("解锁失败");
                }

                @Override
                public boolean hasItem(Long orderGuid) {
                    log.error("查询是否有商品失败:{}", orderGuid);
                    return false;
                }

                @Override
                public EstimateItemRespDTO batchAddItems(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }
            };
        }
    }
}
