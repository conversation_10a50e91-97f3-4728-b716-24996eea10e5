package com.holderzone.holder.saas.aggregation.merchant.service.rpc.queue;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.queue.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueController
 * @date 2019/03/27 16:59
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Api("排队 - 队列")
@Component
@FeignClient(name = "holder-saas-store-queue", fallbackFactory = QueueClientService.ServiceFallBack.class)
public interface QueueClientService {
    @PostMapping("/queue/table/save")
    QueueTableDTO saveTable(@RequestBody QueueTableDTO tableDTO);

    @PostMapping("/queue/save")
    String save(@Valid @RequestBody HolderQueueDTO dto);

    @PostMapping("/queue/enable")
    Boolean enable(@RequestParam("guid") String guid);

    @DeleteMapping("/queue/delete")
    Boolean delete(@RequestParam("guid") String guid);

    @PostMapping("/queue/obtain")
    public HolderQueueDTO fetchOne(@RequestBody QueueGuidDTO dto);

    @GetMapping("/queue/query")
    public List<QueueDetailDTO> query(@RequestParam("storeGuid")String storeGuid);

    @GetMapping("/queue/table/all")
    public List<TreeTableDTO> allTables(@RequestParam("storeGuid") String storeGuid);
    @Component
    class ServiceFallBack implements FallbackFactory<QueueClientService> {
        private static final Logger logger = LoggerFactory.getLogger(QueueClientService.class);

        @Override
        public QueueClientService create(Throwable throwable) {
            return new QueueClientService() {
                @Override
                public HolderQueueDTO fetchOne(QueueGuidDTO dto) {
                    logger.error("查询指定队列异常 ，e={}", throwable);
                    throw new BusinessException("查询指定队列异常!!" + throwable.getMessage());
                }

                @Override
                public List<TreeTableDTO> allTables(String storeGuid) {
                    logger.error("查询可用桌台异常 ，e={}", throwable);
                    throw new BusinessException("查询可用桌台异常!!" + throwable.getMessage());
                }

                @Override
                public List<QueueDetailDTO> query(String storeGuid) {
                    logger.error("查询队列异常 ，e={}", throwable);
                    throw new BusinessException("查询队列异常!!" + throwable.getMessage());
                }

                @Override
                public QueueTableDTO saveTable(QueueTableDTO tableDTO) {
                    logger.error("保存队列异常 ，e={}", throwable);
                    throw new BusinessException("保存队列异常!!" + throwable.getMessage());
                }

                @Override
                public String save(@Valid HolderQueueDTO dto) {
                    logger.error("修改/保存信息异常 ，e={}", throwable);
                    throw new BusinessException("修改/保存信息异常!!" + throwable.getMessage());
                }

                @Override
                public Boolean enable(String guid) {
                    logger.error("禁用/启用信息异常 ，e={}", throwable);
                    throw new BusinessException("禁用/启用信息异常!!" + throwable.getMessage());
                }

                @Override
                public Boolean delete(String guid) {
                    logger.error("删除异常 ，e={}", throwable);
                    throw new BusinessException("删除异常!!" + throwable.getMessage());
                }
            };
        }
    }
}