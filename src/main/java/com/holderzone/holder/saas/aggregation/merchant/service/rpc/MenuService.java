package com.holderzone.holder.saas.aggregation.merchant.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.user.MenuDTO;
import com.holderzone.saas.store.dto.user.MenuSourceDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className MenuService
 * @date 19-2-11 下午2:01
 * @description 商户后台获取菜单
 * @program holder-saas-store-staff
 */
@Component
@FeignClient(name = "holder-saas-store-staff", fallbackFactory = MenuService.ServiceFallBack.class)
public interface MenuService {
    @PostMapping("/menu/get_merchant_menu")
    List<MenuDTO> getMerchantMenu(@RequestParam("userGuid") String userGuid, @RequestParam("terminalCode") String terminalCode);

    @PostMapping("/menu/get_source_by_menu")
    List<MenuSourceDTO> getSourceByMenu(@RequestParam("menuGuid") String menuGuid);

    @PostMapping("/menu/get_page_Url_by_menu")
    List<MenuSourceDTO> getPageUrlByMenu(@RequestParam("pageUrl") String pageUrl);

    @Deprecated
    @PostMapping("/menu/get_source_by_user")
    List<MenuSourceDTO> getSourceByUser(@RequestParam("terminalCode") String terminalCode);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<MenuService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public MenuService create(Throwable cause) {
            return new MenuService() {
                @Override
                public List<MenuDTO> getMerchantMenu(String userGuid, String terminalCode) {
                    log.error(HYSTRIX_PATTERN, "getMerchantMenu", "用户guid为：" + userGuid
                            + "终端code为：" + terminalCode, ThrowableUtils.asString(cause));
                    throw new BusinessException("商户后台获取菜单熔断");
                }

                @Override
                public List<MenuSourceDTO> getSourceByMenu(String menuGuid) {
                    log.error(HYSTRIX_PATTERN, "getSourceByMenu", "菜单guid为：" + menuGuid, ThrowableUtils.asString(cause));
                    throw new BusinessException("商户后台根据菜单获取菜单下资源接口熔断");
                }

                @Override
                public List<MenuSourceDTO> getPageUrlByMenu(String pageUrl) {
                    log.error(HYSTRIX_PATTERN, "getPageUrlByMenu", "pageUrl为：" + pageUrl, ThrowableUtils.asString(cause));
                    throw new BusinessException("商户后台根据菜单url获取菜单下资源接口熔断");
                }

                @Override
                public List<MenuSourceDTO> getSourceByUser(String terminalCode) {
                    log.error(HYSTRIX_PATTERN, "getSourceByUser", "终端Code为：" + terminalCode, ThrowableUtils.asString(cause));
                    throw new BusinessException("商户后台根据终端获取终端下资源接口熔断");
                }
            };
        }
    }
}
