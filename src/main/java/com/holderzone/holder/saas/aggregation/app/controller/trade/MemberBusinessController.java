package com.holderzone.holder.saas.aggregation.app.controller.trade;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.entity.auth.*;
import com.holderzone.holder.saas.aggregation.app.service.BusinessDailyService;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponseConsumpStatis;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponsePayWayDetail;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponseRechargeStatis;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.*;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;


/**
 * 营业日报接口
 * 会员报表
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/business_daily")
@Slf4j
public class MemberBusinessController {

    private final BusinessDailyService businessDailyService;

    /**
     * old
     */
    @ApiOperation(value = "会员消费统计", notes = "会员消费统计")
    @PostMapping("member_consumer")
    public Result<MemberConsumeRespDTO> memberConsume(@RequestBody @Valid DailyReqDTO request) {
        if (log.isInfoEnabled()) {
            log.info("会员消费统计入参：{}", JacksonUtils.writeValueAsString(request));
        }
        return Result.buildSuccessResult(businessDailyService.memberConsume(request));
    }

    /**
     * old
     */
    @ApiOperation(value = "会员消费统计")
    @PostMapping("member_consume_only")
    public Result<ResponseConsumpStatis> memberConsumeDaily(@RequestBody DailyReqDTO reqDTO) {
        if (log.isInfoEnabled()) {
            log.info("会员消费统计入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        }
        ResponseConsumpStatis responseConsumpStatis = businessDailyService.memberConsumeDaily(reqDTO);
        if (responseConsumpStatis != null && !CollectionUtils.isEmpty(responseConsumpStatis.getPayWayDetailList())) {
            payWayDetailTransferLocaleName(responseConsumpStatis.getPayWayDetailList());
        }
        return Result.buildSuccessResult(responseConsumpStatis);
    }

    /**
     * new 受权限控制
     */
    @ApiOperation(value = "会员消费统计")
    @PostMapping("/member_consume_sale")
    public Result<MemberConsumeSaleDTO> memberConsumeSale(@RequestBody DailyReqDTO reqDTO) {
        if (log.isInfoEnabled()) {
            log.info("会员消费统计报表入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        }
        MemberConsumeSaleDTO memberConsumeSaleDTO = businessDailyService.memberConsumeSale(reqDTO);
        if (memberConsumeSaleDTO != null && !CollectionUtils.isEmpty(memberConsumeSaleDTO.getPayWayDetailList())) {
            payWayDetailTransferLocaleName(memberConsumeSaleDTO.getPayWayDetailList());
        }
        return Result.buildSuccessResult(memberConsumeSaleDTO);
    }


    /**
     * old
     */
    @ApiOperation(value = "会员充值统计")
    @PostMapping("member_recharge")
    public Result<ResponseRechargeStatis> memberRechargeDaily(@RequestBody DailyReqDTO reqDTO) {
        if (log.isInfoEnabled()) {
            log.info("会员充值统计入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        }
        ResponseRechargeStatis responseRechargeStatis = businessDailyService.memberRechargeDaily(reqDTO);
        if (responseRechargeStatis != null && !CollectionUtils.isEmpty(responseRechargeStatis.getPayWayDetailList())) {
            payWayDetailTransferLocaleName(responseRechargeStatis.getPayWayDetailList());
        }
        return Result.buildSuccessResult(responseRechargeStatis);
    }

    /**
     * new 受权限控制
     */
    @ApiOperation(value = "会员充值统计")
    @PostMapping("/member_recharge_sale")
    public Result<MemberRechargeSaleDTO> memberRechargeSale(@RequestBody DailyReqDTO reqDTO) {
        if (log.isInfoEnabled()) {
            log.info("会员充值统计入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        }
        MemberRechargeSaleDTO memberRechargeSaleDTO = businessDailyService.memberRechargeSale(reqDTO);
        if (memberRechargeSaleDTO != null && !CollectionUtils.isEmpty(memberRechargeSaleDTO.getPayWayDetailList())) {
            payWayDetailTransferLocaleName(memberRechargeSaleDTO.getPayWayDetailList());
        }
        return Result.buildSuccessResult(memberRechargeSaleDTO);
    }

    /**
     * 支付方式 国际化处理
     */
    private void payWayDetailTransferLocaleName(List<ResponsePayWayDetail> payWayDetails) {
        if (CollectionUtils.isEmpty(payWayDetails)) {
            return;
        }
        payWayDetails.forEach(r -> {
            String localeName = PaymentTypeEnum.PaymentType.getLocaleName(r.getPayName());
            if (StringUtils.isNotEmpty(localeName)) {
                r.setPayName(localeName);
            }
        });
    }
}