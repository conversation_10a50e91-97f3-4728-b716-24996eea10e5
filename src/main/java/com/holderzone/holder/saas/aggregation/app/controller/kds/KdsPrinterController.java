package com.holderzone.holder.saas.aggregation.app.controller.kds;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.app.service.feign.kds.KdsPrinterRpcService;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.KdsPrinterRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Api("KDS设备接口")
@RequestMapping("/kds_printer")
public class KdsPrinterController {

    private final KdsPrinterRpcService kdsPrinterRpcService;

    @Autowired
    public KdsPrinterController(KdsPrinterRpcService kdsPrinterRpcService) {
        this.kdsPrinterRpcService = kdsPrinterRpcService;
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建门店打印机")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "创建门店打印机")
    public Result create(@RequestBody KdsPrinterCreateReqDTO kdsPrinterCreateReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("创建门店打印机入参:{}", JacksonUtils.writeValueAsString(kdsPrinterCreateReqDTO));
        }
        kdsPrinterRpcService.create(kdsPrinterCreateReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改门店打印机")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "修改门店打印机")
    public Result update(@RequestBody KdsPrinterUpdateReqDTO kdsPrinterUpdateReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("修改门店打印机入参:{}", JacksonUtils.writeValueAsString(kdsPrinterUpdateReqDTO));
        }
        kdsPrinterRpcService.update(kdsPrinterUpdateReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除门店打印机")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "删除门店打印机")
    public Result delete(@RequestBody KdsPrinterDeleteReqDTO kdsPrinterDeleteReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("删除门店打印机入参:{}", JacksonUtils.writeValueAsString(kdsPrinterDeleteReqDTO));
        }
        kdsPrinterRpcService.delete(kdsPrinterDeleteReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/page")
    @ApiOperation(value = "查询门店门店打印机列表")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "查询门店门店打印机列表")
    public Result<Page<KdsPrinterRespDTO>> page(@RequestBody KdsPrinterPageReqDTO kdsPrinterPageReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询门店门店打印机列表入参:{}", JacksonUtils.writeValueAsString(kdsPrinterPageReqDTO));
        }
        return Result.buildSuccessResult(kdsPrinterRpcService.page(kdsPrinterPageReqDTO));
    }

    @PostMapping("/bind")
    @ApiOperation(value = "KDS设备绑定打印机")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "KDS设备绑定打印机")
    public Result bind(@RequestBody KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("KDS设备绑定打印机入参:{}", JacksonUtils.writeValueAsString(kdsPrinterBindUnbindReqDTO));
        }
        kdsPrinterRpcService.bind(kdsPrinterBindUnbindReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/rebind")
    @ApiOperation(value = "KDS设备重新绑定打印机")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "KDS设备重新绑定打印机")
    public Result rebind(@RequestBody KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("KDS设备重新绑定打印机入参:{}", JacksonUtils.writeValueAsString(kdsPrinterBindUnbindReqDTO));
        }
        kdsPrinterRpcService.rebind(kdsPrinterBindUnbindReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/unbind")
    @ApiOperation(value = "KDS设备解绑打印机")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "KDS设备解绑打印机")
    public Result unbind(@RequestBody KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("KDS设备解绑打印机入参:{}", JacksonUtils.writeValueAsString(kdsPrinterBindUnbindReqDTO));
        }
        kdsPrinterRpcService.unbind(kdsPrinterBindUnbindReqDTO);
        return Result.buildEmptySuccess();
    }
}

