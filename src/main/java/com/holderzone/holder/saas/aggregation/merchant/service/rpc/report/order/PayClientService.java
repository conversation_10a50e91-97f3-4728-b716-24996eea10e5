package com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.order;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.report.query.DiscountQueryDTO;
import com.holderzone.saas.store.dto.report.query.HandOverQueryDTO;
import com.holderzone.saas.store.dto.report.query.PayTypeCountQueryDTO;
import com.holderzone.saas.store.dto.report.resp.*;
import com.holderzone.saas.store.dto.trade.PaymentTypeDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PayClientService
 * @date 2018/10/16 9:13
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-store-report", fallbackFactory = PayClientService.PayClientFallBack.class)
public interface PayClientService {

    @PostMapping("/pay/report/type")
    PaymentTypeRespDTO queryPayType(PayTypeCountQueryDTO payTypeCountQueryDTO);

    @PostMapping("/pay/report/discount")
    DiscountRespDTO queryDiscount(DiscountQueryDTO discountQueryDTO);

    @PostMapping("/pay/report/type/detail")
    Page<PayDetailRespDTO> queryPayDeatil(PayTypeCountQueryDTO payTypeCountQueryDTO);

    @PostMapping("/pay/report/discount/detail")
    Page<DiscountDetailRespDTO> queryDiscountDetail(DiscountQueryDTO discountQueryDTO);

    @PostMapping("/pay/report/type/name")
    Set<String> queryPayTypeName(List<String> storeGuids);

    @PostMapping("/hand/query")
    Page<HandoverRespDTO> handoverQuery(HandOverQueryDTO handOverQueryDTO);

    @PostMapping("/hand/query/detail/{handoverRecordGuid}")
    HandoverDetailRespDTO handoverDetail(@PathVariable("handoverRecordGuid") String handoverRecordGuid);

    @Component
    class PayClientFallBack implements FallbackFactory<PayClientService> {

        private static final Logger logger = LoggerFactory.getLogger(PayClientFallBack.class);

        @Override
        public PayClientService create(Throwable throwable) {
            return new PayClientService() {

                @Override
                public PaymentTypeRespDTO queryPayType(PayTypeCountQueryDTO payTypeCountQueryDTO) {
                    logger.error("查询报表支付方式异常 e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("查询报表支付方式异常");
                }

                @Override
                public DiscountRespDTO queryDiscount(DiscountQueryDTO discountQueryDTO) {
                    logger.error("查询折扣type异常 e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("查询报表支付方式异常");
                }

                @Override
                public Page<PayDetailRespDTO> queryPayDeatil(PayTypeCountQueryDTO payTypeCountQueryDTO) {
                    logger.error("支付方式详情异常 e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("查询报表支付方式异常");
                }

                @Override
                public Page<DiscountDetailRespDTO> queryDiscountDetail(DiscountQueryDTO discountQueryDTO) {
                    logger.error("查询折扣详情异常 e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("查询报表支付方式异常");
                }

                @Override
                public Set<String> queryPayTypeName(List<String> storeGuids) {
                    logger.error("查询支付方式异常 e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("查询报表支付方式异常");
                }

                @Override
                public Page<HandoverRespDTO> handoverQuery(HandOverQueryDTO handOverQueryDTO) {
                    logger.error("交接班记录详情 e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("查询报表支付方式异常");
                }

                @Override
                public HandoverDetailRespDTO handoverDetail(String handoverRecordGuid) {
                    logger.error("交接班详情异常 e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("查询报表支付方式异常");
                }
            };
        }
    }

}
