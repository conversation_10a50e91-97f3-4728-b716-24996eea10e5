package com.holderzone.holder.saas.aggregation.merchant.controller.report;

import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.ReportClientService;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.OrderItemTypeRespDTO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;


/**
 * 订单相关报表
 */
@Slf4j
@RestController
@RequestMapping("/report/trade/order")
@RequiredArgsConstructor
public class TradeOrderController {

    private final ReportClientService reportClientService;

    @ApiOperation(value = "查询订单商品类型列表")
    @PostMapping("/item/types")
    public Result<List<OrderItemTypeRespDTO>> queryOrderItemTypes(@RequestBody @Valid ReportQueryVO query) {
        return Result.buildSuccessResult(reportClientService.queryOrderItemTypes(query));
    }

    @ApiOperation(value = "查询订单商品分类列表")
    @PostMapping("/item/categories")
    public Result<List<String>> queryOrderItemCategories(@RequestBody @Valid ReportQueryVO query) {
        return Result.buildSuccessResult(reportClientService.queryOrderItemCategories(query));
    }
}
