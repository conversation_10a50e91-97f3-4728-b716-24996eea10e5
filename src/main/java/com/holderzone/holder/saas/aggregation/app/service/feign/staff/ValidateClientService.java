package com.holderzone.holder.saas.aggregation.app.service.feign.staff;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.invoice.UserAuthorityInvoiceDTO;
import com.holderzone.saas.store.dto.user.UserAuthorityDTO;
import com.holderzone.saas.store.dto.user.ValidateDTO;
import com.holderzone.saas.store.dto.user.ValidateRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className UserService
 * @date 18-9-17 上午11:48
 * @description 服务间调用-登陆验证服务
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-store-staff", fallbackFactory = ValidateClientService.ServiceFallBack.class)
public interface ValidateClientService {
    @PostMapping(value = "/user/validate")
    ValidateRespDTO validate(@RequestBody ValidateDTO validateDTO);

    @GetMapping(value = "/user_authority/query_user_invoice")
    UserAuthorityInvoiceDTO queryUserInvoice(@RequestParam String userGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<ValidateClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ValidateClientService create(Throwable cause) {
            return new ValidateClientService() {
                @Override
                public ValidateRespDTO validate(ValidateDTO validateDTO) {
                    log.error(HYSTRIX_PATTERN, "validate", JacksonUtils.writeValueAsString(validateDTO),
                              ThrowableUtils.asString(cause));
                    throw new BusinessException("验证失败");
                }

                @Override
                public UserAuthorityInvoiceDTO queryUserInvoice(String userGuid) {
                    log.error(HYSTRIX_PATTERN, "queryUserInvoice", JacksonUtils.writeValueAsString(userGuid),
                            ThrowableUtils.asString(cause));
                    throw new BusinessException("调用失败");
                }
            };
        }
    }
}
