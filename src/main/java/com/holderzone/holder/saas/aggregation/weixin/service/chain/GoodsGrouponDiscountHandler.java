package com.holderzone.holder.saas.aggregation.weixin.service.chain;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.context.DiscountContext;
import com.holderzone.holder.saas.aggregation.weixin.helper.PriceCalculationUtils;
import com.holderzone.holder.saas.aggregation.weixin.utils.map.DiscountMAP;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.member.terminal.enums.VolumeTypeEnum;
import com.holderzone.holder.saas.weixin.utils.BigDecimalUtil;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.trade.DiscountDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 商品券 优惠计算
 */
@Component
@Slf4j
@AllArgsConstructor
public class GoodsGrouponDiscountHandler extends DiscountHandler {

    private final PriceCalculationUtils priceCalculationUtils;

    @Override
    void dealDiscount(DiscountContext context) {
        if (context.isRejectDiscount()) {
            return;
        }
        Integer volumeCodeType = context.getVolumeCodeType();
        if (volumeCodeType != VolumeTypeEnum.PRODUCT_COUPON.getCcCode()) {
            return;
        }
        // 查询商品券抵扣金额
        List<RequestDishInfo> dishInfoDTOList = priceCalculationUtils.dealWithVolume(context, context.getAllItems());
        // 处理商品优惠金额
        handleDiscountTotalPrice(context, dishInfoDTOList);
        DiscountDTO memberGoodsGrouponDO = context.getDiscountTypeMap().get(type());
        DiscountFeeDetailDTO memberGoodsGroupon = DiscountMAP.INSTANCE.discountDO2DiscountFeeDetailDTO(memberGoodsGrouponDO);
        if (CollectionUtils.isNotEmpty(dishInfoDTOList)) {
            singleItemReduceDiscount(context.getDineInItemDTOMap(), memberGoodsGroupon, dishInfoDTOList);
            log.info("会员单品券最终菜品：{}", JacksonUtils.writeValueAsString(memberGoodsGroupon));
        } else {
            memberGoodsGroupon.setDiscountFee(BigDecimal.ZERO);
        }
        log.info("会员商品券计算后订单剩余金额：{}，优惠金额：{}，会员商品券计算后菜品：{}", context.getCalculateOrderRespDTO().getOrderSurplusFee(),
                memberGoodsGroupon.getDiscountFee(), JacksonUtils.writeValueAsString(context.getAllItems()));
        // 订单剩余金额
        context.getCalculateOrderRespDTO().setOrderSurplusFee(context.getCalculateOrderRespDTO().getOrderSurplusFee()
                .subtract(memberGoodsGroupon.getDiscountFee()));
        // 优惠明细加上 会员商品券
        context.getDiscountFeeDetailDTOS().add(memberGoodsGroupon);
    }

    /**
     * 设置商品使用商品券数量 及优惠金额
     */
    private void singleItemReduceDiscount(Map<String, DineInItemDTO> dineInItemMap, DiscountFeeDetailDTO memberGroupon,
                                          List<RequestDishInfo> dishInfoDTOList) {
        BigDecimal discountFee = BigDecimal.ZERO;
        for (RequestDishInfo dishInfoDTO : dishInfoDTOList) {
            if (dishInfoDTO.getDiscountMoney() != null) {
                discountFee = discountFee.add(dishInfoDTO.getDiscountMoney());
                DineInItemDTO dineInItemDTO = dineInItemMap.get(dishInfoDTO.getOrderItemGuid());
                if (dineInItemDTO != null && dishInfoDTO.getDiscountMoney() != null && dishInfoDTO.getDiscountMoney().floatValue() > 0) {
                    dineInItemDTO.setTotalDiscountFee(dineInItemDTO.getTotalDiscountFee().add(dishInfoDTO.getDiscountMoney()));
                    // 参与了单品券的数量
                    dineInItemDTO.setIsGoodsReduceDiscount(dishInfoDTO.getProductItemNum());
                    // 商品券抵扣优惠金额
                    dineInItemDTO.setTicketPreferential(dishInfoDTO.getDiscountMoney());
                }
            }
        }
        memberGroupon.setDiscountFee(BigDecimalUtil.setScale2(discountFee));
    }


    @Override
    Integer type() {
        return DiscountTypeEnum.GOODS_GROUPON.getCode();
    }
}
