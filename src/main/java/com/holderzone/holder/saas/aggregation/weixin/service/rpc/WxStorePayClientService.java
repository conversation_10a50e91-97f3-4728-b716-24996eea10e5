package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.saas.store.dto.weixin.WxPrepayRespDTO;
import com.holderzone.saas.store.dto.weixin.WxZeroPayReqDTO;
import com.holderzone.saas.store.dto.weixin.member.*;
import com.holderzone.saas.store.dto.weixin.req.WxH5PayReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxMemberTradeNotifyReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPrepayReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxStorePayClientService
 * @date 2019/4/3
 */
@Component
@FeignClient(name = "holder-saas-store-weixin",fallbackFactory = WxStorePayClientService.WxStorePayFallBack.class)
public interface WxStorePayClientService {

	String URL_PREFIX = "/wx_store_pay";

	@PostMapping(URL_PREFIX+"/wechat/public")
	WxPayRespDTO weChatPublic(WxH5PayReqDTO wxH5PayReqDTO);

	@PostMapping(URL_PREFIX+"/member_pay")
	WxStorePayResultDTO memberPay(WxMemberPayDTO wxMemberPayDTO);

	@PostMapping(URL_PREFIX+"/pay_way")
	WxPayWayRespDTO getAllPayWay(WxStorePayReqDTO wxStorePayReqDTO);

	@PostMapping(URL_PREFIX+"/prepay")
	WxPrepayRespDTO prepay(WxPrepayReqDTO wxPrepayReqDTO);

	@PostMapping(URL_PREFIX+ "/validate_order")
	WxPrepayRespDTO validateOrder(WxStorePayReqDTO wxStorePayReqDTO);

	@PostMapping(URL_PREFIX+"/prepay_confirm")
	WxPrepayConfirmRespDTO memberConfirm(WxPrepayConfirmReqDTO wxPrepayConfirmReqDTO);

	@PostMapping(URL_PREFIX+"/zero_pay")
	WxStorePayResultDTO zeroPay(WxZeroPayReqDTO wxZeroPayReqDTO);

	@ApiOperation(value = "支付完成模板消息推送", notes = "支付完成模板消息推送")
	@PostMapping(URL_PREFIX+"/sendWeixinNotifyMessage")
	public boolean sendWeixinNotifyMessage(@RequestBody WxMemberTradeNotifyReqDTO wxMemberTradeNotifyReqDTO);

	@Slf4j
	@Component
	class WxStorePayFallBack implements FallbackFactory<WxStorePayClientService> {

		@Override
		public WxStorePayClientService create(Throwable throwable) {
			return new WxStorePayClientService() {
				@Override
				public WxPayRespDTO weChatPublic(WxH5PayReqDTO wxH5PayReqDTO) {
					log.error("远程调用信息失败，msg={}", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public WxStorePayResultDTO memberPay(WxMemberPayDTO wxMemberPayDTO) {
					log.error("会员支付失败:{}",wxMemberPayDTO);
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public WxPayWayRespDTO getAllPayWay(WxStorePayReqDTO wxStorePayReqDTO) {
					log.error("查询支付方式失败:{}",wxStorePayReqDTO);
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public WxPrepayRespDTO prepay(WxPrepayReqDTO wxStorePayReqDTO) {
					log.error("支付失败:{}",wxStorePayReqDTO);
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public WxPrepayRespDTO validateOrder(WxStorePayReqDTO wxStorePayReqDTO) {
					log.error("订单验证失败:{}",wxStorePayReqDTO);
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public WxPrepayConfirmRespDTO memberConfirm(WxPrepayConfirmReqDTO wxPrepayConfirmReqDTO) {
					log.error("会员与优惠券验证:{}",wxPrepayConfirmReqDTO);
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public WxStorePayResultDTO zeroPay(WxZeroPayReqDTO wxZeroPayReqDTO) {
					log.error("0元支付失败:{}",wxZeroPayReqDTO);
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public boolean sendWeixinNotifyMessage(WxMemberTradeNotifyReqDTO wxMemberTradeNotifyReqDTO) {
					log.error("微信通知失败,入参：{}",wxMemberTradeNotifyReqDTO,throwable);
					return false;
				}
			};
		}
	}
}
