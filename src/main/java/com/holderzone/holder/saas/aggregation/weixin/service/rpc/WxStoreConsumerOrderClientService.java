package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDineInOrderDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * @description 微信门店订单调用层
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreConsumerOrderClientService
 * @date 2019/3/15
 */
@Component
@FeignClient(name = "holder-saas-store-weixin",fallbackFactory = WxStoreConsumerOrderClientService.WxStoreConsumerOrderFallBack.class)
public interface WxStoreConsumerOrderClientService {

	String PATH = "/wx-store-order-provide";

	@PostMapping(PATH+"/order/list")
	List<WxStoreConsumerDineInOrderDTO> getCurrentTableOrderForm(WxStoreConsumerDineInOrderDTO wxStoreConsumerDineInOrderDTO);

	@PostMapping(PATH+"/order/sig")
	WxStoreConsumerDineInOrderDTO updateDineInOrder(WxStoreConsumerDTO wxStoreConsumerDTO);

	@PostMapping(PATH+"/submit")
	DineinOrderDetailRespDTO submitOrder(WxStoreConsumerDineInOrderDTO consumerDineInOrderDTO);

	@PostMapping(PATH+"/update_remark")
	Boolean updateRemark(CreateDineInOrderReqDTO createDineInOrderReqDTO);

	@PostMapping(PATH+"/update_guest_count")
	Boolean updateGuestCount(CreateDineInOrderReqDTO createDineInOrderReqDTO);

	@PostMapping(value="/order/del")
	Boolean delDineInOrder(WxStoreConsumerDTO wxStoreConsumerDTO);


	@Slf4j
	@Component
	class WxStoreConsumerOrderFallBack implements FallbackFactory<WxStoreConsumerOrderClientService> {

		@Override
		public WxStoreConsumerOrderClientService create(Throwable throwable) {
			return new WxStoreConsumerOrderClientService() {

				@Override
				public List<WxStoreConsumerDineInOrderDTO> getCurrentTableOrderForm(WxStoreConsumerDineInOrderDTO wxStoreConsumerDineInOrderDTO) {
					log.error("远程调用服务失败，msg={}", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public WxStoreConsumerDineInOrderDTO updateDineInOrder(WxStoreConsumerDTO wxStoreConsumerDTO) {
					log.error("远程调用服务失败，msg={}", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public DineinOrderDetailRespDTO submitOrder(WxStoreConsumerDineInOrderDTO consumerDineInOrderDTO) {
					log.error("远程调用服务失败，msg={}", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public Boolean updateRemark(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
					log.error("远程调用服务失败，msg={}", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public Boolean updateGuestCount(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
					log.error("远程调用服务失败，msg={}", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public Boolean delDineInOrder(WxStoreConsumerDTO wxStoreConsumerDTO) {
					log.error("远程调用服务失败，msg={}", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}
			};
		}
	}
}
