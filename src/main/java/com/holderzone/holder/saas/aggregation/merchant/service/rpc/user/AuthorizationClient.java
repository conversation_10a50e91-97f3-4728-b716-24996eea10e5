package com.holderzone.holder.saas.aggregation.merchant.service.rpc.user;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.resource.common.dto.authorization.ModuleTerminalDto;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className IAuthorizationFeignService
 * @date 2018/8/24 9:53
 * @description authorization-service feign
 * @program framework-resource-service
 */
@Component
@FeignClient(value = "authorization-service", fallbackFactory = AuthorizationClient.FallBackService.class)
public interface AuthorizationClient {
    /**
     * 查询用户的所有权限资源
     */
    @GetMapping("/poll/user/{userGuid}")
    List<ModuleTerminalDto> findUserAllResource(@PathVariable("userGuid") String userGuid);

    @Slf4j
    @Component
    class FallBackService implements FallbackFactory<AuthorizationClient> {
        @Override
        public AuthorizationClient create(Throwable cause) {
            return userGuid -> {
                log.error("登陆后查询菜单列表失败，e{}", cause.getMessage());
                throw new BusinessException("登陆后查询菜单列表失败");
            };
        }
    }
}
