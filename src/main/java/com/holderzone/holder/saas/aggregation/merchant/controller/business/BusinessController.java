package com.holderzone.holder.saas.aggregation.merchant.controller.business;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.manage.BusinessClientService;
import com.holderzone.saas.store.dto.business.manage.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessController
 * @date 2018/09/12 15:30
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Api(value = "附屏图片接口")
@RestController
@RequestMapping("/business")
public class BusinessController {

    private static final Logger logger = LoggerFactory.getLogger(BusinessController.class);

    private final BusinessClientService businessClientService;

    @Autowired
    public BusinessController(BusinessClientService businessClientService) {
        this.businessClientService = businessClientService;
    }

    @ApiOperation(value = "保存副屏图片设置")
    @PostMapping("/save_config")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "保存副屏图片设置")
    public Result<String> saveConfig(@RequestBody ScreenPictureConfigDTO screenPictureConfigDTO) {
        logger.info("保存附屏图片 screenPictureDTO={}", JacksonUtils.writeValueAsString(screenPictureConfigDTO));
        String result = businessClientService.saveConfig(screenPictureConfigDTO);
        if ("success".equals(result)) {
            return Result.buildSuccessResult(result);
        }
        return Result.buildFailResult(1, "保存附屏图片失败");
    }

    @ApiOperation(value = "获取当前的副屏图片配置")
    @PostMapping("/get_config")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "获取当前的副屏图片配置")
    public Result<ScreenPictureConfigDTO> getConfig(@RequestBody ScreenPicConfigReqDTO screenPicConfigReqDTO) {
        logger.info("获取当前的副屏图片配置 screenPicConfigReqDTO={}", JacksonUtils.writeValueAsString(screenPicConfigReqDTO));
        return Result.buildSuccessResult(businessClientService.getConfig(screenPicConfigReqDTO));
    }


    @ApiOperation(value = "保存附屏图片")
    @PostMapping("/save")
    @Deprecated
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "保存附屏图片")
    public Result<String> save(@RequestBody ScreenPictureDTO screenPictureDTO) {
        logger.info("保存附屏图片 screenPictureDTO={}", JacksonUtils.writeValueAsString(screenPictureDTO));
        String result = businessClientService.save(screenPictureDTO);
        if ("success".equals(result)) {
            return Result.buildSuccessResult(result);
        }
        return Result.buildFailResult(1, "保存附屏图片失败");
    }

    @ApiOperation(value = "时间设置，必须传入storeGuid和changeMills")
    @PostMapping("/time")
    @Deprecated
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "时间设置，必须传入storeGuid和changeMills")
    public Result<String> setTime(@RequestBody ScreenPicTimeDTO screenPicTimeDTO) {
        logger.info("设置切换时间 screenPictureDTO={}", JacksonUtils.writeValueAsString(screenPicTimeDTO));
        String result = businessClientService.setTime(screenPicTimeDTO);
        if ("success".equals(result)) {
            return Result.buildSuccessResult(result);
        }
        return Result.buildOpFailedResult(result);
    }

    @ApiOperation(value = "获取时间，必须传入storeGuid和changeMills")
    @PostMapping("/get/time")
    @Deprecated
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "获取时间，必须传入storeGuid和changeMills")
    public Result<List<ScreenPicTimeDTO>> getTime(@RequestBody ScreenPicTimeDTO screenPicTimeDTO) {
        logger.info("获取切换时间 storeGuid={}", JacksonUtils.writeValueAsString(screenPicTimeDTO));
        List<ScreenPicTimeDTO> list = businessClientService.getTime(screenPicTimeDTO);
        return Result.buildSuccessResult(list);
    }


    @ApiOperation(value = "查询门店附屏图片")
    @PostMapping("/query/web")
    @Deprecated
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "查询门店附屏图片")
    public Result<List<ScreenAppRespDTO>> query(@RequestBody ScreenPicQuery screenPicQuery) {
        logger.info("web查询门店附屏图片 screenPicQuery={}", JacksonUtils.writeValueAsString(screenPicQuery));

        return Result.buildSuccessResult(businessClientService.query(screenPicQuery));
    }

    @ApiOperation(value = "删除图片")
    @PostMapping("/delete/{screenPictureGuid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "删除图片")
    public Result<String> delete(@PathVariable("screenPictureGuid") String screenPictureGuid) {
        logger.info("删除附屏图片 screenPictureGuid={}", screenPictureGuid);
        String result = businessClientService.delete(screenPictureGuid);
        if ("success".equals(result)) {
            return Result.buildSuccessResult(result);
        }
        return Result.buildOpFailedResult(result);
    }
}
