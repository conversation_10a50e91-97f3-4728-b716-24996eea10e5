package com.holderzone.holder.saas.aggregation.app.controller.auth;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.response.ResultEnum;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.MemberMarketingClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.OrganizationClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.business.ManageClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.cloud.EnterpriseCloudService;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.account.NewMemberInfoClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.staff.ValidateClientService;
import com.holderzone.holder.saas.member.terminal.dto.card.ResponseBaseCardInfo;
import com.holderzone.resource.common.dto.enterprise.MultiMemberDTO;
import com.holderzone.saas.store.dto.business.manage.ScreenPicConfigReqDTO;
import com.holderzone.saas.store.dto.business.manage.ScreenPictureConfigDTO;
import com.holderzone.saas.store.dto.marketing.portrayal.MemberPortrayalDetailsVO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceQueryDTO;
import com.holderzone.saas.store.dto.user.ValidateDTO;
import com.holderzone.saas.store.dto.user.ValidateRespDTO;
import com.holderzone.saas.store.enums.marketing.portrayal.ApplySettingEnum;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className ValidateController
 * @date 18-9-17 上午11:45
 * @description 登陆验证相关接口
 * @program holder-saas-aggregation-app
 */
@Api(description = "app查询设备注册-绑定状态、登陆验证接口")
@RestController
@Slf4j
public class ValidateController {

    private final ValidateClientService validateClientService;

    private final OrganizationClientService organizationClientService;

    @Resource
    private EnterpriseCloudService enterpriseCloudService;

    private final NewMemberInfoClientService newMemberInfoClientService;

    private final MemberMarketingClientService marketingClientService;

    private final ManageClientService manageClientService;

    @Autowired
    public ValidateController(ValidateClientService validateClientService,
                              OrganizationClientService organizationClientService,
                              NewMemberInfoClientService newMemberInfoClientService,
                              MemberMarketingClientService marketingClientService,
                              ManageClientService manageClientService) {
        this.validateClientService = validateClientService;
        this.organizationClientService = organizationClientService;
        this.newMemberInfoClientService = newMemberInfoClientService;
        this.marketingClientService = marketingClientService;
        this.manageClientService = manageClientService;
    }

    private final static String DEFAULT_ADMINISTRATOR ="默认管理员";

    @ApiOperation(value = "app登陆验证接口，验证成功返回相关信息")
    @PostMapping(value = "/user/validate")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "登陆验证",action = OperatorType.LOGIN)
    public Result<ValidateRespDTO> validate(@RequestBody ValidateDTO validateDTO) {
        log.info("android登陆验证接口入参为：" + JacksonUtils.writeValueAsString(validateDTO));
        ValidateRespDTO dto = validateClientService.validate(validateDTO);
        if (dto == null) {
            return Result.buildFailResult(ResultEnum.SERVICE_TEMPORARILY_UNAVAILABLE.getResultCode(), LocaleUtil.getMessage("INTERFACE_EXCEPTION"));
        }
        if(dto.getUserName().equals(DEFAULT_ADMINISTRATOR)){
            dto.setUserName(LocaleUtil.getMessage("DEFAULT_ADMINISTRATOR"));
        }
        //通过门店guid查会员主体信息
        MultiMemberDTO multiMemberDTO = enterpriseCloudService.findMemberInfoByOrganizationGuid(validateDTO.getStoreGuid());
        if (Objects.isNull(multiMemberDTO) || Objects.isNull(multiMemberDTO.getMultiMemberGuid())) {
//            throw new BusinessException("会员主体未配置！");
            dto.setMultiMemberGuid("-1");
            dto.setMultiMemberName("-1");
            dto.setMultiMemberStatus(Boolean.FALSE);
            return Result.buildSuccessResult(dto);
        }
        dto.setMultiMemberGuid(multiMemberDTO.getMultiMemberGuid());
        dto.setMultiMemberName(multiMemberDTO.getMultiMemberName());
        dto.setMultiMemberStatus(multiMemberDTO.getEnabled());

        // 余额使用校验
        UserContext userContext = UserContextUtils.get();
        userContext.setOperSubjectGuid(multiMemberDTO.getMultiMemberGuid());
        UserContextUtils.put(userContext);
        ResponseBaseCardInfo cardInfo = newMemberInfoClientService.getCardInfo();
        dto.setMemberLoginType(cardInfo.getMemberLoginType());
        dto.setMemberLoginCheckType(cardInfo.getMemberLoginCheckType());

        // 是否开启就餐会员画像
        queryApplySetting(dto, multiMemberDTO);

        // 查询门店附屏配置
        queryStoreConfig(validateDTO, dto);
        return Result.buildSuccessResult(dto);
    }

    private void queryApplySetting(ValidateRespDTO dto, MultiMemberDTO multiMemberDTO) {
        try {
            dto.setMemberPortrayal(Boolean.FALSE);
            log.info("[根据运营主体查询会员画像]operSubjectGuid={}", multiMemberDTO.getMultiMemberGuid());
            MemberPortrayalDetailsVO portrayalDetailsVO = marketingClientService.queryApplySetting(multiMemberDTO.getMultiMemberGuid());
            log.info("[根据运营主体查询会员画像]portrayalDetailsVO={}", JacksonUtils.writeValueAsString(portrayalDetailsVO));
            if (!ObjectUtils.isEmpty(portrayalDetailsVO) && !CollectionUtils.isEmpty(portrayalDetailsVO.getApplySetting())) {
                dto.setMemberPortrayal(portrayalDetailsVO.getApplySetting().contains(ApplySettingEnum.REPAST_AIO_DIN.getCode()));
            }
        } catch (Exception e) {
            log.warn("获取会员画像异常=》", e);
        }
    }

    private void queryStoreConfig(ValidateDTO validateDTO, ValidateRespDTO dto) {
        try {
            ScreenPicConfigReqDTO configReqDTO = new ScreenPicConfigReqDTO();
            configReqDTO.setStoreGuid(dto.getStoreGuid());
            configReqDTO.setDeviceType(validateDTO.getDeviceTypeCode());
            log.info("[查询门店附屏配置]configReqDTO={}", JacksonUtils.writeValueAsString(configReqDTO));
            ScreenPictureConfigDTO pictureConfigDTO = manageClientService.queryStoreConfig(configReqDTO);
            log.info("[查询门店附屏配置]pictureConfigDTO={}", JacksonUtils.writeValueAsString(pictureConfigDTO));
            dto.setDisplayType(2);
            if (!ObjectUtils.isEmpty(pictureConfigDTO) && !ObjectUtils.isEmpty(pictureConfigDTO.getDisplayType())) {
                dto.setDisplayType(pictureConfigDTO.getDisplayType());
            }
        } catch (Exception e) {
            log.warn("查询门店附屏配置=》", e);
        }
    }

    @ApiOperation(value = "根据厂商设备编号查询设备的注册、绑定状态", notes = "分为未注册、已注册未绑定、已注册已绑定三种情况")
    @GetMapping(value = "/device/find_device_status/{deviceNo}")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT,description = "设备状态查询",action = OperatorType.SELECT)
    public Result<StoreDeviceDTO> findDeviceStatus(@ApiParam(value = "厂商设备编号") @PathVariable("deviceNo") String deviceNo) {
        log.info("设备状态查询请求入参：{}", deviceNo);
        StoreDeviceDTO result = organizationClientService.findDeviceStatus(deviceNo);
        if (result == null) {
            return Result.buildFailResult(ResultEnum.SERVICE_TEMPORARILY_UNAVAILABLE.getResultCode(), "接口异常，请稍后重试");
        }
        log.info("查询到设备状态，result:{}", JacksonUtils.writeValueAsString(result));
        return Result.buildSuccessResult(result);
    }

    @ApiOperation(value = "根据门店guid查询该门店下的master一体机")
    @GetMapping("/device/get_master_device_by_storeguid/{storeGuid}")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT,description = "根据门店guid查询该门店下的master一体机",action = OperatorType.SELECT)
    public Result<StoreDeviceDTO> getMasterDeviceByStoreGuid(@PathVariable("storeGuid") String storeGuid) {
        return Result.buildSuccessResult(organizationClientService.getMasterDeviceByStoreGuid(storeGuid));
    }

    @ApiOperation(value = "根据门店guid或设备类型查询设备列表")
    @PostMapping(value = "/device/find_store_device")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT,description = "根据门店guid或设备类型查询设备列表",action = OperatorType.SELECT)
    public Result<List<StoreDeviceDTO>> findStoreDevice(@RequestBody StoreDeviceQueryDTO storeDeviceQueryDTO) {
        log.info("查询门店设备请求入参：{}", JacksonUtils.writeValueAsString(storeDeviceQueryDTO));
        return Result.buildSuccessResult(organizationClientService.findStoreDevice(storeDeviceQueryDTO));
    }
}
