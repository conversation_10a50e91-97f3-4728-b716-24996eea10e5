package com.holderzone.holder.saas.aggregation.weixin.controller.deal;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.weixin.entity.dto.PayOnlineDTO;
import com.holderzone.holder.saas.aggregation.weixin.service.MenuItemService;
import com.holderzone.holder.saas.aggregation.weixin.service.WxStoreTradeOrderService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.ItemClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.WxClientService;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.WxSearchItemDto;
import com.holderzone.saas.store.dto.item.resp.ItemSynRespDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateFastFoodReqDTO;
import com.holderzone.saas.store.dto.weixin.common.WxStoreInfoDTO;
import com.holderzone.saas.store.dto.weixin.deal.*;
import com.holderzone.saas.store.dto.weixin.req.ClearCartItemReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.StoreActivityRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.sdk.annotation.RateLimit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.concurrent.ExecutionException;

@Api(value = "门店配置与商品")
@Slf4j
@RestController
@RequestMapping("/deal/menu")
@Validated
public class MenuItemController {

    private final MenuItemService menuItemService;

    private final WxClientService wxClientService;

    private final WxStoreTradeOrderService wxStoreTradeOrderService;

    private final ItemClientService itemClientService;

    private static final String ITEM_MESSAGE = "该商品已售罄";

    @Autowired
    public MenuItemController(MenuItemService menuItemService,
                              WxClientService wxClientService,
                              WxStoreTradeOrderService wxStoreTradeOrderService,
                              ItemClientService itemClientService) {
        this.menuItemService = menuItemService;
        this.wxClientService = wxClientService;
        this.wxStoreTradeOrderService = wxStoreTradeOrderService;
        this.itemClientService = itemClientService;
    }

    @ApiOperation("/查询扫码点餐菜谱变动情况")
    @PostMapping(value = "/pricePlanChangeInfo")
    @ApiImplicitParam(name = "pricePlanChangeInfo", value = "查询扫码点餐菜谱变动情况", dataType = "Integer")
    public Result<PricePlanChangeResponseDTO> pricePlanChangeInfo(@RequestBody PricePlanChangeRequestDTO pricePlanChangeRequestDTO) {
        log.info("查询扫码点餐菜谱变动情况，入参:{}", JacksonUtils.writeValueAsString(pricePlanChangeRequestDTO));

        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        String storeGuid = wxMemberSessionDTO.getStoreGuid();
        pricePlanChangeRequestDTO.setStoreGuid(storeGuid);

        PricePlanChangeResponseDTO pricePlanChangeResponseDTO = menuItemService.pricePlanChangeInfo(pricePlanChangeRequestDTO);

        return Result.buildSuccessResult(pricePlanChangeResponseDTO);
    }

    @ApiOperation("/查询门店商品，分类，配置")
    @GetMapping(value = "/item_config")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "wxPermission", value = "1：表示扫码第一次进入", dataType = "Integer"),
            @ApiImplicitParam(name = "wxAddItemFlag", value = "1：表示加菜进入 0:表示非加菜进入", dataType = "Integer")
    })
    @RateLimit(limitType = "weixinItemConfig")
    public Result<MenuInfoAllDTO> getMenuInfoAll(@RequestParam("wxPermission") Integer wxPermission,
                                                 @RequestParam(value = "wxAddItemFlag", required = false, defaultValue = "0") Integer wxAddItemFlag,
                                                 @RequestParam(value = "h5flag", required = false, defaultValue = "0") Integer h5flag,
                                                 @RequestParam(value = "nthFlag", required = false, defaultValue = "0") Integer nthFlag)
            throws ExecutionException, InterruptedException {
        log.info("查询门店商品，分类，配置入参:xPermission:{}, wxAddItemFlag:{}, H5flag:{}, nthFlag:{}", wxPermission, wxAddItemFlag, h5flag, nthFlag);
        return Result.buildSuccessResult(menuItemService.getMenuInfoAll(WeixinUserThreadLocal.get(), wxPermission, wxAddItemFlag, h5flag, nthFlag));
    }

    @ApiOperation("/查询门店配置")
    @GetMapping(value = "/store_config")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "wxPermission", value = "1：表示扫码第一次进入", dataType = "Integer"),
            @ApiImplicitParam(name = "wxAddItemFlag", value = "1：表示加菜进入 0:表示非加菜进入", dataType = "Integer")
    })
    public Result<MenuInfoAllDTO> getStoreConfig(@RequestParam("wxPermission") Integer wxPermission,
                                                 @RequestParam(value = "wxAddItemFlag", required = false, defaultValue = "0") Integer wxAddItemFlag,
                                                 @RequestParam(value = "isLogin", required = false) Integer isLogin) {
        log.info("查询门店配置入参:wxPermission:{}, wxAddItemFlag:{}, isLogin:{}", wxPermission, wxAddItemFlag, isLogin);
        return Result.buildSuccessResult(menuItemService.getStoreConfig(wxAddItemFlag, isLogin));
    }

    /**
     * 2024.06优化之后扫码点餐不用该接口，改为前端搜索
     */
    @ApiOperation("/赚餐扫描点餐-关键字搜索商品")
    @PostMapping(value = "/item_search")
    public Result<Page<ItemInfoDTO>> searchItems(@RequestBody WxSearchItemDto dto) throws ExecutionException, InterruptedException {
        log.info("赚餐扫描点餐-关键字搜索商品入参:{}", JacksonUtils.writeValueAsString(dto));
        return Result.buildSuccessResult(menuItemService.searchItems(dto));
    }

    @ApiOperation("加减菜")
    @PostMapping(value = "/item_modify")
    @RateLimit(limitType = "weixinItemModify")
    public Result<ShopCartItemAddRespDTO> itemModify(@Valid @RequestBody ShopCartItemReqDTO shopCartItemReqDTO) {
        log.info("加菜入参:{}", JacksonUtils.writeValueAsString(shopCartItemReqDTO));
        return Result.buildSuccessResult(menuItemService.itemModify(shopCartItemReqDTO));
    }

    @ApiOperation("清空购物车")
    @GetMapping(value = "/clear_shop_cart")
    public Result<Void> clearShopCart() {
        menuItemService.clearShopCart();
        return Result.buildEmptySuccess();
    }

    @ApiOperation("购物车查询接口")
    @GetMapping(value = "/shop_cart")
    @RateLimit(limitType = "weixinGetMenuItem")
    public Result<ShopCartRespDTO> getMenuItem(@RequestParam(value = "h5flag", required = false, defaultValue = "0") Integer h5flag) {
        log.info("查询购物车请求头参数:{}, h5flag:{}", JacksonUtils.writeValueAsString(UserContextUtils.get()), h5flag);
        return Result.buildSuccessResult(menuItemService.getMenuItemList(h5flag));
    }

    @ApiOperation("会员卡列表")
    @GetMapping(value = "/member_card_list")
    public Result<List<UserMemberCardCacheDTO>> getMemberCardList() {
        return Result.buildSuccessResult(menuItemService.cardList());
    }

    @ApiOperation("切换会员卡")
    @PostMapping(value = "/switch_card")
    public Result<Void> switchCard(@RequestBody SwitchCardReqDTO switchCardReqDTO) {
        return Boolean.TRUE.equals(menuItemService.switchCard(switchCardReqDTO.getMemberInfoCardGuid())) ?
                Result.buildEmptySuccess() : Result.buildFailResult(400, "操作失败");
    }

    @ApiOperation("提交购物车")
    @GetMapping(value = "/submit_cart")
    @RateLimit(limitType = "weixinSubmitCart", limitCount = 200)
    public Result<SubmitShopCartRespDTO> submitShopCart() {
        return Result.buildSuccessResult(menuItemService.submitShopCart());
    }

    @ApiOperation("订单确认列表")
    @GetMapping(value = "/pre_order_list")
    @ApiImplicitParam(name = "wxAddItemFlag", value = "1：表示加菜进入 0:表示非加菜进入", dataType = "Integer")
    public Result<PreOrderRespDTO> preOrderList(@RequestParam(value = "wxAddItemFlag", required = false, defaultValue = "0") Integer wxAddItemFlag) {
        log.info("订单确认头部信息:{}, wxAddItemFlag:{}", JacksonUtils.writeValueAsString(UserContextUtils.get()), wxAddItemFlag);
        return Result.buildSuccessResult(menuItemService.preOrderList(wxAddItemFlag));
    }

    @ApiOperation("删除用户订单")
    @PostMapping(value = "/del_user_order")
    public Result<PreOrderRespDTO> delUserPreOrder(@RequestParam String openid) {
        return Result.buildSuccessResult(menuItemService.delUserPreOrder(openid));
    }

    @ApiOperation("下单")
    @PostMapping(value = "/submit_order")
    @RateLimit(limitType = "weixinSubmitOrder", limitCount = 200)
    public Result<OrderSubmitRespDTO> submit(@RequestBody(required = false) OrderSubmitReqDTO orderSubmitReqDTO) {
        log.info("下单入参:{},session:{}", JacksonUtils.writeValueAsString(orderSubmitReqDTO), JacksonUtils.writeValueAsString(WeixinUserThreadLocal.get()));
        if (orderSubmitReqDTO == null) {
            orderSubmitReqDTO = new OrderSubmitReqDTO();
            orderSubmitReqDTO.setUserCount(4);
        }
        OrderSubmitRespDTO submit = menuItemService.preSubmit(orderSubmitReqDTO);
        menuItemService.sendShopCartMessage();
        log.info("下单参数返回:{}", JacksonUtils.writeValueAsString(submit));
        return Result.buildSuccessResult(submit);
    }

    @ApiOperation("是否在线支付")
    @PostMapping(value = "/pay_online")
    public Result<PayOnlineDTO> payOnline(@RequestBody WxStoreInfoDTO wxStoreInfoDTO) {
        WxOrderConfigDTO wxOrderConfigDTO = wxClientService.getStoreConfig(wxStoreInfoDTO.getStoreGuid());
        PayOnlineDTO payOnlineDTO = new PayOnlineDTO();
        if (ObjectUtils.isEmpty(wxOrderConfigDTO) || wxOrderConfigDTO.getIsOnlinePayed() == 0) {
            payOnlineDTO.setIsOnlinePayed(false);
            return Result.buildSuccessResult(payOnlineDTO);
        }
        payOnlineDTO.setIsOnlinePayed(true);
        return Result.buildSuccessResult(payOnlineDTO);
    }

    /**
     * 根据商品guid查询商品详情
     *
     * @param itemSingleDTO 商品guid放在data里就行
     * @return 商品详情
     */
    @ApiOperation(value = "根据商品guid查询商品详情", notes = "商品guid放在data里就行")
    @PostMapping("/get_item_info")
    public Result<ItemInfoDTO> selectItemInfo(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("获取一个商品详情接口入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        ItemInfoDTO itemInfoDTO = menuItemService.selectItemInfo(itemSingleDTO);
        log.info("当前商品详情信息=" + itemInfoDTO);
        Integer isSoldOut = itemInfoDTO.getIsSoldOut();
        if (2 == isSoldOut) { //2:表示商品已经售罄
            return Result.buildFailResult(1, ITEM_MESSAGE);
        }
        return Result.buildSuccessResult(itemInfoDTO);
    }

    /**
     * 根据商品批量清除购物车里的指定商品
     *
     * @param request 商品List
     * @return boolean
     */
    @ApiOperation("清除购物车里的指定商品")
    @PostMapping(value = "/clear_items_on_shop_cart")
    public Result<Boolean> clearItemsOnShopCart(@RequestBody ClearCartItemReqDTO request) {
        log.info("清除购物车里的指定商品 入参:{}", JacksonUtils.writeValueAsString(request));
        return Result.buildSuccessResult(menuItemService.clearItemsOnShopCart(request));
    }

    @ApiOperation("加菜时保存当前所需附加费明细")
    @PostMapping(value = "/add_item_before_surcharges")
    public Result<Void> saveAddItemBeforeSurcharges(@RequestBody CreateFastFoodReqDTO createFastFoodReqDTO) {
        log.info("加菜时保存当前所需附加费明细入参:{}", JacksonUtils.writeValueAsString(createFastFoodReqDTO));
        wxStoreTradeOrderService.saveAddItemBeforeSurcharges(createFastFoodReqDTO.getSurchargeLinkList());
        return Result.buildEmptySuccess();
    }

    @ApiOperation("获取快餐点餐人数")
    @GetMapping("/get_fast_guest_count")
    public Result<Integer> getFastGuestCount(@RequestParam("diningTableGuid") String diningTableGuid,
                                             @RequestParam("openId") String openId) {
        log.info("[获取快餐点餐人数],diningTableGuid={},openId={}", diningTableGuid, openId);
        return Result.buildSuccessResult(wxStoreTradeOrderService.getFastGuestCount(diningTableGuid, openId));
    }

    @ApiOperation("删除快餐点餐人数")
    @GetMapping("/remove_fast_guest_count")
    public Result<Void> removeFastGuestCount(@RequestParam("diningTableGuid") String diningTableGuid,
                                             @RequestParam("openId") String openId) {
        log.info("[删除快餐点餐人数],diningTableGuid={},openId={}", diningTableGuid, openId);
        wxStoreTradeOrderService.removeFastGuestCount(diningTableGuid, openId);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "门店活动列表")
    @GetMapping("/query_store_activity")
    public Result<List<StoreActivityRespDTO>> queryStoreActivity() {
        return Result.buildSuccessResult(menuItemService.queryStoreActivity());
    }

    /**
     * 查询推荐商品
     */
    @ApiOperation(value = "查询推荐商品")
    @PostMapping("/query_recommend_item")
    public Result<List<ItemSynRespDTO>> queryRecommendItem(@RequestBody ItemSingleDTO query) {
        log.info("[查询推荐商品]query={}", JacksonUtils.writeValueAsString(query));
        return Result.buildSuccessResult(itemClientService.queryRecommendItem(query));
    }

    /**
     * 校验是否必点分类商品(只校验普通模式)
     */
    @ApiOperation(value = "校验是否必点分类商品")
    @PostMapping("/check_must_item")
    public Result<CheckMustItemRespDTO> checkMustItem(@RequestBody OrderMustPointCheckDTO orderMustPointCheckDTO) {
        return Result.buildSuccessResult(menuItemService.checkMustItem(orderMustPointCheckDTO));
    }

}
