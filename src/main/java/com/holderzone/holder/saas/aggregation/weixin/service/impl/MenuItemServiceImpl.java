package com.holderzone.holder.saas.aggregation.weixin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dynamic.datasource.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.dynamic.datasource.starter.utils.JacksonUtil;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.weixin.assembler.MarketingActivityAssembler;
import com.holderzone.holder.saas.aggregation.weixin.config.LocalCacheConfig;
import com.holderzone.holder.saas.aggregation.weixin.constant.RedisConstants;
import com.holderzone.holder.saas.aggregation.weixin.event.TagEnum;
import com.holderzone.holder.saas.aggregation.weixin.helper.DineInItemHelper;
import com.holderzone.holder.saas.aggregation.weixin.helper.MarketingActivityHelper;
import com.holderzone.holder.saas.aggregation.weixin.helper.MemberRightHelper;
import com.holderzone.holder.saas.aggregation.weixin.helper.PriceCalculateHelper;
import com.holderzone.holder.saas.aggregation.weixin.service.CalculateService;
import com.holderzone.holder.saas.aggregation.weixin.service.MenuItemService;
import com.holderzone.holder.saas.aggregation.weixin.service.WxStoreTradeOrderService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.*;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.WxHsmMemberBasicBufferService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.*;
import com.holderzone.holder.saas.aggregation.weixin.utils.StringMessageUtils;
import com.holderzone.holder.saas.aggregation.weixin.utils.UserMemberSessionUtils;
import com.holderzone.holder.saas.aggregation.weixin.utils.map.BigDecimalFormatUtil;
import com.holderzone.holder.saas.aggregation.weixin.utils.map.MemberCardItemMAP;
import com.holderzone.holder.saas.aggregation.weixin.utils.map.ShopCardItemMAP;
import com.holderzone.holder.saas.member.wechat.dto.activitie.RequestMarketActivityUse;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseMarketActivitieOneRule;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseMarketActivitieRule;
import com.holderzone.holder.saas.member.wechat.dto.card.RequestCardOwnedPage;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseDiscount;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseProductDiscount;
import com.holderzone.holder.saas.member.wechat.dto.enums.MemberRightsType;
import com.holderzone.holder.saas.member.wechat.dto.label.ResponseMemberLabel;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberCardListOwned;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.common.CacheName;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.entry.dto.WxUserInfoDTO;
import com.holderzone.holder.saas.weixin.entry.dto.req.MqttMessageReqDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WebsocketUtils;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.holder.saas.weixin.utils.WxMemberSessionUtil;
import com.holderzone.resource.common.util.LoginSource;
import com.holderzone.saas.store.bo.weixin.AssembleMenuItemBO;
import com.holderzone.saas.store.bo.weixin.SpecialsActivityAmountBO;
import com.holderzone.saas.store.constant.RedisKeyConstant;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.TypeSingleDTO;
import com.holderzone.saas.store.dto.item.req.ItemQueryListReq;
import com.holderzone.saas.store.dto.item.req.ItemRedisReqDTO;
import com.holderzone.saas.store.dto.item.req.WxSearchItemDto;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.marketing.nth.NthActivityDetailsVO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityDetailsVO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityItemQO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityItemVO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.ItemAttrDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.weixin.PricePairDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxUserRecordDTO;
import com.holderzone.saas.store.dto.weixin.deal.*;
import com.holderzone.saas.store.dto.weixin.req.ClearCartItemReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.StoreActivityRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.item.ItemTypeEnum;
import com.holderzone.saas.store.enums.member.VolumeVerifyEnum;
import com.holderzone.saas.store.enums.order.AppendFeeTypeEnum;
import com.holderzone.saas.store.enums.order.OrderTradeModeEnum;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import com.holderzone.saas.store.enums.weixin.ActivityTypeEnum;
import com.holderzone.saas.store.enums.weixin.MinPriceTypeEnum;
import com.holderzone.saas.store.enums.weixin.PreferentialTypeEnum;
import com.holderzone.saas.store.enums.weixin.WxMealsTypeEnum;
import com.holderzone.saas.store.util.BigDecimalUtil;
import com.holderzone.saas.store.util.StringHandlerUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.holder.saas.weixin.common.BusinessName.WX_STORE_CONFIG;

@Slf4j
@Service
public class MenuItemServiceImpl implements MenuItemService {

    public static final String ITEM_INFO_DTOS = "itemInfoDTOS={}";
    private final DineInOrderClientService dineInOrderClientService;
    private final TableClientService tableClientService;
    private final WxClientService wxClientService;
    private final ItemClientService itemClientService;
    private final RedisUtils redisUtils;
    private final UserMemberSessionUtils userMemberSessionUtils;
    private final ExecutorService menuItemExecutor;
    private final OrganizationClientService organizationClientService;
    private final HsaBaseClientService hsaBaseClientService;

    private final MarketingActivityHelper marketingActivityHelper;

    private final PriceCalculateHelper priceCalculateHelper;

    @Lazy
    @Resource
    private CalculateService calculateService;

    @Resource
    private WebsocketMessageClientService websocketMessageClientService;
    @Resource
    private WxStoreTradeOrderService wxStoreTradeOrderService;

    @Resource
    private WebsocketUtils websocketUtils;
    @Resource
    private WxUserRecordClientService wxUserRecordClientService;
    @Resource
    private WxHsmMemberBasicBufferService wxHsmMemberBasicBufferService;
    @Resource
    private BusinessClientService businessClientService;
    @Value("${brandGuidFilter}")
    private String brandGuidFilter;

    @Resource
    private UpdateUserLoginClientService updateUserLoginClientService;

    @Resource
    private WxStoreTableClientService wxStoreTableClientService;

    @Resource
    private MemberRightHelper memberRightHelper;

    @Autowired
    public MenuItemServiceImpl(DineInOrderClientService dineInOrderClientService,
                               TableClientService tableClientService,
                               WxClientService wxClientService,
                               ItemClientService itemClientService,
                               RedisUtils redisUtils,
                               UserMemberSessionUtils userMemberSessionUtils,
                               ExecutorService menuItemExecutor,
                               OrganizationClientService organizationClientService,
                               HsaBaseClientService hsaBaseClientService,
                               MarketingActivityHelper marketingActivityHelper,
                               PriceCalculateHelper priceCalculateHelper) {
        this.dineInOrderClientService = dineInOrderClientService;
        this.tableClientService = tableClientService;
        this.wxClientService = wxClientService;
        this.itemClientService = itemClientService;
        this.redisUtils = redisUtils;
        this.userMemberSessionUtils = userMemberSessionUtils;
        this.menuItemExecutor = menuItemExecutor;
        this.organizationClientService = organizationClientService;
        this.hsaBaseClientService = hsaBaseClientService;
        this.marketingActivityHelper = marketingActivityHelper;
        this.priceCalculateHelper = priceCalculateHelper;
    }

    /**
     * 查询扫码点餐菜谱变动情况
     *
     * @param pricePlanChangeRequestDTO 变动请求
     * @return PricePlanChangeResponseDTO
     */
    @Override
    public PricePlanChangeResponseDTO pricePlanChangeInfo(PricePlanChangeRequestDTO pricePlanChangeRequestDTO) {
        return itemClientService.pricePlanChangeInfo(pricePlanChangeRequestDTO);
    }

    /**
     * 查询门店配置，商品
     *
     * @return 门店配置，商品
     */
    @Override
    @Cacheable(cacheNames = {LocalCacheConfig.CacheExpires.SECOND_10}, keyGenerator = "weixinCacheKeyGenerator",
            unless = "#result == null || #result.itemInfoDTOS.size() == 0")
    public MenuInfoAllDTO getMenuInfoAll(WxMemberSessionDTO wxMemberSessionDTO, Integer wxPermission,
                                         Integer wxAddItemFlag, Integer h5flag, Integer nthFlag) {
        log.info("当前会话信息：{}", JacksonUtils.writeValueAsString(wxMemberSessionDTO));
        Assert.notNull(wxMemberSessionDTO, "无法获取当前会话");
        if (StringUtils.isEmpty(wxMemberSessionDTO.getStoreGuid())) {
            throw new BusinessException("无法获取当前会话门店id");
        }
        UserContext userInfoDTO = UserContextUtils.get();
        userInfoDTO.setSource(h5flag == 0 ? LoginSource.MINAPP.code() : LoginSource.WECHAT.code());
        UserContextUtils.put(userInfoDTO);

        // 商品
        CompletableFuture<ItemAndTypeForAndroidRespDTO> itemFuture = getItemFuture(wxMemberSessionDTO, userInfoDTO);

        // 门店配置
        CompletableFuture<WxOrderConfigDTO> storeConfigFuture = getStoreConfigFuture(wxMemberSessionDTO, userInfoDTO);

        // 估清
        CompletableFuture<List<ItemEstimateForAndroidRespDTO>> estimateSkuListFuture = getEstimateSkuListFuture(wxMemberSessionDTO, userInfoDTO);

        // 满减满折
        CompletableFuture<ResponseMarketActivitieRule> marketActivityFuture = getMarketActivityFuture(wxMemberSessionDTO, userInfoDTO);

        // 限时特价
        CompletableFuture<List<LimitSpecialsActivityDetailsVO>> specialsActivityFuture = getSpecialsActivityFuture(wxMemberSessionDTO, userInfoDTO);

        // 第N份优惠活动
        CompletableFuture<List<NthActivityDetailsVO>> nthActivityFuture = getNthActivityListFuture(wxMemberSessionDTO, userInfoDTO, nthFlag);

        CompletableFuture<Void> all = CompletableFuture.allOf(itemFuture, storeConfigFuture, estimateSkuListFuture,
                marketActivityFuture, specialsActivityFuture, nthActivityFuture);
        try {
            all.get();

            AssembleMenuItemBO itemBO = new AssembleMenuItemBO();
            ItemAndTypeForAndroidRespDTO itemRespDTO = itemFuture.get();
            itemBO.setItem(itemRespDTO);
            itemBO.setStoreConfig(storeConfigFuture.get());
            itemBO.setEstimateSkuList(estimateSkuListFuture.get());
            itemBO.setResponseMarketActivitieRule(marketActivityFuture.get());
            itemBO.setWxPermission(wxPermission);
            itemBO.setWxAddItemFlag(wxAddItemFlag);
            List<String> itemGuidList = getItemGuidList(wxMemberSessionDTO.getStoreGuid(), itemRespDTO);
            List<LimitSpecialsActivityItemVO> activityItemVOList = queryFilterSpecialsActivity(itemGuidList,
                    specialsActivityFuture.get());
            itemBO.setActivityItemVOList(activityItemVOList);

            List<NthActivityDetailsVO> nthActivityList = nthActivityFuture.get();
            // 商品校验
            MarketingActivityHelper.nthFilterItem(itemGuidList, nthActivityList, nthFlag);
            itemBO.setNthActivityList(nthActivityList);
            itemBO.setNthFlag(nthFlag);

            return assembleMenuItem(itemBO, wxMemberSessionDTO);
        } catch (Exception e) {
            log.error("查询商品列表失败", e);
            Thread.currentThread().interrupt();
            throw new BusinessException("系统繁忙稍后再试");
        }
    }

    private CompletableFuture<List<LimitSpecialsActivityDetailsVO>> getSpecialsActivityFuture(WxMemberSessionDTO wxMemberSessionDTO,
                                                                                              UserContext userInfoDTO) {
        return CompletableFuture.supplyAsync(() -> {
            EnterpriseIdentifier.setEnterpriseGuid(userInfoDTO.getEnterpriseGuid());
            UserContextUtils.put(userInfoDTO);
            // h5不查询限时活动
            if (LoginSource.WECHAT.code().equals(userInfoDTO.getSource())) {
                return new ArrayList<LimitSpecialsActivityDetailsVO>();
            }
            return marketingActivityHelper.querySpecialsActivityList(wxMemberSessionDTO);
        }, menuItemExecutor).exceptionally(throwable -> {
            log.error("查询限时特价活动失败", throwable);
            return null;
        });
    }

    private CompletableFuture<ResponseMarketActivitieRule> getMarketActivityFuture(WxMemberSessionDTO wxMemberSessionDTO,
                                                                                   UserContext userInfoDTO) {
        return CompletableFuture.supplyAsync(() -> {
            EnterpriseIdentifier.setEnterpriseGuid(userInfoDTO.getEnterpriseGuid());
            UserContextUtils.put(userInfoDTO);
            // h5不查询满减满折
            if (LoginSource.WECHAT.code().equals(userInfoDTO.getSource())) {
                return new ResponseMarketActivitieRule();
            }
            if (Objects.isNull(wxMemberSessionDTO.getOrderState()) || TradeModeEnum.DINEIN.getCode() == wxMemberSessionDTO.getOrderState()) {
                return new ResponseMarketActivitieRule();
            }
            // 确保点餐和结算活动一致，此处查询并缓存一份
            UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(wxMemberSessionDTO.getWxUserInfoDTO().getOpenId());
            log.info("[getMarketActivityFuture]userMemberSession={}", JacksonUtils.writeValueAsString(userMemberSession));
            RequestMarketActivityUse req = new RequestMarketActivityUse();
            req.setMemberInfoCardGuid(userMemberSession.getMemberInfoCardGuid());
            marketingActivityHelper.queryFullDiscountAndReductionActivityList(req, wxMemberSessionDTO);
            return queryMarketActivityList(wxMemberSessionDTO);
        }, menuItemExecutor).exceptionally(throwable -> {
            log.error("查询小程序满减满折活动失败", throwable);
            return null;
        });
    }

    private CompletableFuture<List<ItemEstimateForAndroidRespDTO>> getEstimateSkuListFuture(WxMemberSessionDTO wxMemberSessionDTO,
                                                                                            UserContext userInfoDTO) {
        return CompletableFuture.supplyAsync(() -> {
            BaseDTO baseDTO = new BaseDTO();
            baseDTO.setStoreGuid(wxMemberSessionDTO.getStoreGuid());
            EnterpriseIdentifier.setEnterpriseGuid(userInfoDTO.getEnterpriseGuid());
            UserContextUtils.put(userInfoDTO);
            return itemClientService.queryEstimateForSyn(baseDTO);
        }, menuItemExecutor).exceptionally(throwable -> {
            log.error("查询门店商品估清列表", throwable);
            return null;
        });
    }

    private CompletableFuture<WxOrderConfigDTO> getStoreConfigFuture(WxMemberSessionDTO wxMemberSessionDTO,
                                                                     UserContext userInfoDTO) {
        return CompletableFuture.supplyAsync(() -> {
            EnterpriseIdentifier.setEnterpriseGuid(userInfoDTO.getEnterpriseGuid());
            UserContextUtils.put(userInfoDTO);
            return wxClientService.getStoreConfig(wxMemberSessionDTO.getStoreGuid());
        }, menuItemExecutor).exceptionally(throwable -> {
            log.error("查询门店配置失败", throwable);
            throw new BusinessException("无法获取当前门店配置");
        });
    }

    private CompletableFuture<ItemAndTypeForAndroidRespDTO> getItemFuture(WxMemberSessionDTO wxMemberSessionDTO,
                                                                          UserContext userInfoDTO) {
        return CompletableFuture.supplyAsync(() -> {
            EnterpriseIdentifier.setEnterpriseGuid(userInfoDTO.getEnterpriseGuid());
            UserContextUtils.put(userInfoDTO);
            return getItem(wxMemberSessionDTO.getStoreGuid(), wxMemberSessionDTO.getOrderState());
        }, menuItemExecutor).exceptionally(throwable -> {
            log.error("查询门店商品列表失败", throwable);
            return null;
        });
    }

    @NotNull
    private List<String> getItemGuidList(String storeGuid,
                                         ItemAndTypeForAndroidRespDTO itemRespDTO) {
        List<String> itemGuidList = new ArrayList<>();
        if (!org.springframework.util.CollectionUtils.isEmpty(itemRespDTO.getItemList())) {
            itemGuidList = itemRespDTO.getItemList().stream()
                    .map(ItemSynRespDTO::getItemGuid)
                    .distinct()
                    .collect(Collectors.toList());
            List<String> itemParentGuidList = itemRespDTO.getItemList().stream()
                    .map(ItemSynRespDTO::getParentGuid)
                    .filter(com.holderzone.framework.util.StringUtils::hasText)
                    .distinct()
                    .collect(Collectors.toList());
            itemGuidList.addAll(itemParentGuidList);
        }
        log.warn("itemGuidList={}", itemGuidList);

        // 每次调用该接口说明是重新扫码进入，缓存商品id
        String redisKey = String.format(RedisKeyConstant.SCAN_ORDER_ITEM_GUID, storeGuid);
        redisUtils.setEx(redisKey, itemGuidList, 30, TimeUnit.MINUTES);
        return itemGuidList;
    }

    /**
     * 限时特价活动
     */
    private List<LimitSpecialsActivityItemVO> queryFilterSpecialsActivity(List<String> itemGuidList,
                                                                          List<LimitSpecialsActivityDetailsVO> specialsActivityList) {
        // 商品校验
        marketingActivityHelper.checkItem(itemGuidList, specialsActivityList);
        if (org.springframework.util.CollectionUtils.isEmpty(specialsActivityList)) {
            log.warn("[商品列表][商品校验]过滤为空");
            return new ArrayList<>();
        }
        return MarketingActivityAssembler.getActivityItemVOList(specialsActivityList);
    }

    @NotNull
    private LimitSpecialsActivityItemQO getActivityItemQO(WxMemberSessionDTO wxMemberSessionDTO, List<String> itemGuidList) {
        LimitSpecialsActivityItemQO activityItemQO = new LimitSpecialsActivityItemQO();
        activityItemQO.setOperSubjectGuid(wxMemberSessionDTO.getOperSubjectGuid());
        activityItemQO.setItemGuidList(itemGuidList);
        activityItemQO.setStoreGuid(wxMemberSessionDTO.getStoreGuid());
        activityItemQO.setApplyBusiness(wxMemberSessionDTO.getOrderState());

        activityItemQO.setMemberInfoGuid(wxMemberSessionDTO.getMemberInfoGuid());
        activityItemQO.setIsRegister(org.springframework.util.StringUtils.hasText(wxMemberSessionDTO.getMemberInfoGuid())
                ? BooleanEnum.TRUE.getCode() : BooleanEnum.FALSE.getCode());

        if (org.springframework.util.StringUtils.hasText(wxMemberSessionDTO.getMemberInfoGuid())) {

            // 会员等级,产品说只看处理主卡
            ResponseMemberCardListOwned defaultCard = hsaBaseClientService.getDefaultCard(wxMemberSessionDTO.getMemberInfoGuid()).getData();
            if (!ObjectUtils.isEmpty(defaultCard)) {
                activityItemQO.setGradeGuidListStr(defaultCard.getCardLevelGuid());
            }

            // 会员标签
            List<ResponseMemberLabel> labelList = hsaBaseClientService.getMemberInfoLabel(wxMemberSessionDTO.getMemberInfoGuid()).getData();
            if (!org.springframework.util.CollectionUtils.isEmpty(labelList)) {
                List<String> labelGuidList = labelList.stream()
                        .map(ResponseMemberLabel::getLabelGuid)
                        .distinct()
                        .collect(Collectors.toList());
                String labelGuidListStr = StringHandlerUtil.toMysqlRegStr(labelGuidList);
                activityItemQO.setLabelGuidListStr(labelGuidListStr);
            }
        }
        return activityItemQO;
    }

    private MenuInfoAllDTO assembleMenuItem(AssembleMenuItemBO itemBO, WxMemberSessionDTO wxMemberSessionDTO) {
        Integer wxPermission = itemBO.getWxPermission();
        WxOrderConfigDTO storeConfig = itemBO.getStoreConfig();
        log.info("getMenuInfoAll 微信门店相关配置{}", JacksonUtils.writeValueAsString(storeConfig));
        boolean jumpState = wxPermission == 1 && 0 == storeConfig.getOrderModel() && checkStatus(wxMemberSessionDTO.getDiningTableGuid());
        List<ItemEstimateForAndroidRespDTO> estimateSkuList = itemBO.getEstimateSkuList();
        if (!CollectionUtils.isEmpty(estimateSkuList)) {
            addItemEstimate(wxMemberSessionDTO.getStoreGuid(), estimateSkuList);
        }
        // 会员权益缓存，每次调用商品列表都更新一下
        memberRightHelper.saveMemberRights(wxMemberSessionDTO.getWxtoken());

        // 商品处理
        List<UserMemberCardCacheDTO> cardList = getCardList();
        ItemAndTypeForAndroidRespDTO item = itemBO.getItem();
        MenuInfoAllDTO menuInfoAllDTO = transFormItem(itemBO, jumpState, cardList);

        // 查询是否需要附加费
        Integer wxAddItemFlag = itemBO.getWxAddItemFlag();
        menuInfoAllDTO.setSurchargeFlag(getIsExistSurcharge(wxAddItemFlag, menuInfoAllDTO.getMenuInfoConfigDTO()));
        if (storeConfig.getOrderModel() == 0) {
            String openId = WeixinUserThreadLocal.getOpenId();
            boolean firstAdd = websocketUtils.addOpenIdInRedis(wxMemberSessionDTO.getDiningTableGuid(), openId);
            log.info("增加桌台的opneid,tableGuid:{},openid:{},firstAdd:{}", wxMemberSessionDTO.getDiningTableGuid(), openId, firstAdd);
            if (firstAdd) {
                sendDinnerMessage();
            }
        }

        menuInfoAllDTO.setStoreName(getStoreDetail(wxMemberSessionDTO.getStoreGuid()).getName());
        menuInfoAllDTO.setBrandName(getBrandDetail(wxMemberSessionDTO.getBrandGuid()).getName());
        Integer qrCodeTypeByToken = getQrCodeTypeByToken(WeixinUserThreadLocal.getWxtoken());
        menuInfoAllDTO.setQrCodeType(qrCodeTypeByToken);

        // 设置售卖模式
        if (item == null) {
            menuInfoAllDTO.setSalesModel(1);
        } else {
            menuInfoAllDTO.setSalesModel(item.getSalesModel());
            menuInfoAllDTO.setPricePlanGuid(item.getPricePlanGuid());
        }

        // 存入redis以便商品服务使用
        saveCache2Item(wxMemberSessionDTO, menuInfoAllDTO);

        log.info("menuInfoAllDTO={}", JacksonUtils.writeValueAsString(menuInfoAllDTO));
        return menuInfoAllDTO;
    }

    private void saveCache2Item(WxMemberSessionDTO wxMemberSessionDTO, MenuInfoAllDTO menuInfoAllDTO) {
        String cacheKey = "itemConfig:" + wxMemberSessionDTO.getEnterpriseGuid() + ":" + wxMemberSessionDTO.getWxtoken();
        ItemRedisReqDTO reqDTO = new ItemRedisReqDTO();
        reqDTO.setRedisKey(cacheKey);
        reqDTO.setRedisValue(JacksonUtils.writeValueAsString(menuInfoAllDTO));
        reqDTO.setRedisTime(20L);
        reqDTO.setTimeUnit(TimeUnit.MINUTES);
        itemClientService.putItemRedis(reqDTO);
    }


    @Override
    public MenuInfoAllDTO getStoreConfig(Integer wxAddItemFlag, Integer isLogin) {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        log.info("获取门店配置当前会话信息：{}", JacksonUtils.writeValueAsString(wxMemberSessionDTO));
        Assert.notNull(wxMemberSessionDTO, "获取门店配置无法获取当前会话");
        if (StringUtils.isEmpty(wxMemberSessionDTO.getStoreGuid())) {
            throw new BusinessException("获取门店配置无法获取当前会话门店id");
        }
        WxOrderConfigDTO storeConfig = wxClientService.getStoreConfig(wxMemberSessionDTO.getStoreGuid());
        Assert.notNull(storeConfig, "无法获取当前门店配置");
        log.info("getStoreConfig微信门店相关配置{}", JacksonUtils.writeValueAsString(storeConfig));
        MenuInfoAllDTO menuInfoAllDTO = new MenuInfoAllDTO();
        menuInfoAllDTO.setMenuInfoConfigDTO(transformStoreConfig(storeConfig));
        // 查询是否需要附加费
        menuInfoAllDTO.setSurchargeFlag(getIsExistSurcharge(wxAddItemFlag, menuInfoAllDTO.getMenuInfoConfigDTO()));
        // 查询快餐点餐人数
        if (Objects.equals(OrderTradeModeEnum.快餐.getCode(), storeConfig.getOrderModel())
                && !StringUtils.isEmpty(wxMemberSessionDTO.getDiningTableGuid())) {
            menuInfoAllDTO.setGuestCount(wxStoreTradeOrderService.getFastGuestCount(wxMemberSessionDTO.getDiningTableGuid(),
                    WeixinUserThreadLocal.getOpenId()));
        }
        // 修改登录状态
        if (Objects.nonNull(isLogin)) {
            WxStoreConsumerDTO wxStoreConsumerDTO = new WxStoreConsumerDTO();
            wxStoreConsumerDTO.setEnterpriseGuid(wxMemberSessionDTO.getEnterpriseGuid());
            wxStoreConsumerDTO.setOpenId(WeixinUserThreadLocal.getOpenId());
            wxStoreConsumerDTO.setIsLogin(true);
            updateUserLoginClientService.updateUserLogin(wxStoreConsumerDTO);
        }
        return menuInfoAllDTO;
    }

    @Override
    public Page<ItemInfoDTO> searchItems(WxSearchItemDto dto) {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        log.info("当前会话信息：{}", JacksonUtils.writeValueAsString(wxMemberSessionDTO));
        Assert.notNull(wxMemberSessionDTO, "无法获取当前会话");
        if (StringUtils.isEmpty(wxMemberSessionDTO.getStoreGuid())) {
            throw new BusinessException("无法获取当前会话门店id");
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("扫描点餐关键字查询商品");
        dto.setStoreGuid(wxMemberSessionDTO.getStoreGuid());
        Page<ItemSynRespDTO> page = itemClientService.wxSearchItems(dto);
        log.info("扫描点餐关键字{}查询商品耗时{}", dto.getKeywords(), stopWatch.prettyPrint());
        BaseDTO baseDTO = new BaseDTO();
        baseDTO.setStoreGuid(wxMemberSessionDTO.getStoreGuid());
        List<ItemEstimateForAndroidRespDTO> estimateSkuList = itemClientService.queryEstimateForSyn(baseDTO);
        List<ItemInfoDTO> itemInfoDTOS = transformDish(page.getData(), estimateSkuList);
        log.info(ITEM_INFO_DTOS, JacksonUtils.writeValueAsString(itemInfoDTOS));
        if (ObjectUtils.isEmpty(itemInfoDTOS)) {
            log.warn("itemInfoDTOS转换为空");
        }
        itemListEstimate(itemInfoDTOS, estimateSkuList);
        itemInfoDTOS = itemEstimateFilter(itemInfoDTOS);
        if (ObjectUtils.isEmpty(itemInfoDTOS)) {
            log.warn("itemInfoDTOS估清为空");
        }
        // 添加商品满减满折活动信息
        appendItemMarketActivityList(itemInfoDTOS);

        // 限时特价活动
        appendLimitSpecialsActivity(itemInfoDTOS, wxMemberSessionDTO);

        // 活动互斥展示
        itemActivityShareHandler(itemInfoDTOS);

        Page<ItemInfoDTO> infoDTOPage = new Page<>();
        infoDTOPage.setTotalCount(page.getTotalCount());
        infoDTOPage.setPageSize(page.getPageSize());
        infoDTOPage.setData(itemInfoDTOS);
        infoDTOPage.setCurrentPage(page.getCurrentPage());
        return infoDTOPage;
    }

    private void appendLimitSpecialsActivity(List<ItemInfoDTO> itemInfoDTOS,
                                             WxMemberSessionDTO wxMemberSessionDTO) {
        if (!org.springframework.util.CollectionUtils.isEmpty(itemInfoDTOS)) {
            List<String> itemGuidList = itemInfoDTOS.stream()
                    .map(ItemInfoDTO::getItemGuid)
                    .distinct()
                    .collect(Collectors.toList());
            List<LimitSpecialsActivityDetailsVO> specialsActivityList = marketingActivityHelper.querySpecialsActivityList(wxMemberSessionDTO);
            List<LimitSpecialsActivityItemVO> activityItemVOList = queryFilterSpecialsActivity(itemGuidList, specialsActivityList);
            // 限时特价活动
            MarketingActivityHelper.buildLimitSpecialsActivity(activityItemVOList, itemInfoDTOS);
        }
    }

    @Override
    public ShopCartItemAddRespDTO itemModify(ShopCartItemReqDTO shopCartItemReqDTO) {
        handlerUckItem(shopCartItemReqDTO);  //筛选uck选中的sku商品
        ShopCartItemAddRespDTO shopCartItemAddRespDTO = Boolean.TRUE.equals(shopCartItemReqDTO.getChangeType()) ?
                shopCartModify(shopCartItemReqDTO) : shopCartUpdate(shopCartItemReqDTO);
        if (shopCartItemAddRespDTO.getCode().equals(0)) {
            //成功
            sendShopCartMessage();
        }
        return shopCartItemAddRespDTO;
    }

    /**
     * 筛选uck选中的sku商品
     *
     * @param shopCartItemReqDTO 提交的参数信息
     */
    private void handlerUckItem(ShopCartItemReqDTO shopCartItemReqDTO) {
        //提交的商品sku
        ItemInfoDTO itemInfo = shopCartItemReqDTO.getItemInfoDTO();
        List<ItemInfoSkuDTO> skuList = itemInfo.getSkuList();
        if (ObjectUtils.isEmpty(skuList) || skuList.isEmpty()) {
            return;
        }

        List<ItemInfoSkuDTO> itemSkuList = skuList.stream().filter(x -> x.getUck() == 1).collect(Collectors.toList());
        shopCartItemReqDTO.getItemInfoDTO().setSkuList(itemSkuList);
        log.info("筛选后，skuList:{}", itemSkuList);
    }

    /**
     * @param itemInfoDTO 商品
     * @param modifyNum   数量
     * @return 购物车超重不处理
     */
    private String overWeight(ItemInfoDTO itemInfoDTO, BigDecimal modifyNum) {
        Integer itemType = itemInfoDTO.getItemType();
        if (itemType == 3) {
            if (modifyNum.compareTo(new BigDecimal("999.999")) > 0) {
                log.info("商品超重不处理:{}", itemInfoDTO);
                return "单次下单不能超过999.999";
            }
        } else {
            if (modifyNum.compareTo(new BigDecimal("999")) > 0) {
                return "单次下单不能超过999";
            }
        }
        return null;
    }

    /**
     * @param shopCartItemReqDTO 购物车项
     * @return 直接修改购物车项数量
     */
    private ShopCartItemAddRespDTO shopCartUpdate(ShopCartItemReqDTO shopCartItemReqDTO) {
        BigDecimal modifyNum = shopCartItemReqDTO.getModifyNum();
        Assert.isTrue(modifyNum.compareTo(BigDecimal.ZERO) >= 0, "直接修改购物车项，数量必须大于等于0");
        ItemInfoDTO itemInfoDTO = shopCartItemReqDTO.getItemInfoDTO();
        String storeGuid = WeixinUserThreadLocal.getStoreGuid();
        Assert.notNull(storeGuid, "加菜时门店guid不能为空");

        String uniqueKey = ItemInfoDTO.getUniqueKey(itemInfoDTO);
        WxOrderConfigDTO storeConfig = getStoreConfig(storeGuid);
        Assert.notNull(storeConfig, "门店配置异常，不能加菜");
        ShopCartItemReqDTO cartItem = getCartItem(uniqueKey, storeConfig.getOrderModel());
        if (cartItem == null) {
            ItemInfoSkuDTO uckSku = PriceCalculateHelper.getUckSku(itemInfoDTO);
            if (Objects.nonNull(itemInfoDTO.getCouponPreRespDTO()) || modifyNum.compareTo(uckSku.getMinOrderNum()) >= 0) {
                WxUserInfoDTO wxUserInfoDTO = WeixinUserThreadLocal.getWxUserInfoDTO();
                Assert.notNull(wxUserInfoDTO, "无法查询到微信用户,不能加菜");
                String overWeight = overWeight(itemInfoDTO, modifyNum);
                if (!StringUtils.isEmpty(overWeight)) {
                    return ShopCartItemAddRespDTO.addFailed(overWeight);
                }
                itemInfoDTO.setCurrentCount(modifyNum);

                shopCartItemReqDTO.setOpenid(wxUserInfoDTO.getOpenId());
                shopCartItemReqDTO.setNickName(wxUserInfoDTO.getNickname());
                shopCartItemReqDTO.setHeadImgUrl(wxUserInfoDTO.getHeadImgUrl());
                shopCartItemReqDTO.setGmtCreate(LocalDateTime.now());
                setCartItem(uniqueKey, shopCartItemReqDTO, storeConfig.getOrderModel());
            }
        } else {
            ItemInfoDTO itemInfo = cartItem.getItemInfoDTO();
            ItemInfoSkuDTO uckSku = PriceCalculateHelper.getUckSku(itemInfo);
            if (modifyNum.compareTo(uckSku.getMinOrderNum()) < 0) {
                delCartItem(uniqueKey, storeConfig.getOrderModel());
            } else {
                String overWeight = overWeight(itemInfo, modifyNum);
                if (!StringUtils.isEmpty(overWeight)) {
                    return ShopCartItemAddRespDTO.addFailed(overWeight);
                }
                itemInfo.setCurrentCount(modifyNum);

                cartItem.setItemInfoDTO(itemInfo);
                setCartItem(uniqueKey, cartItem, storeConfig.getOrderModel());
            }
        }
        return new ShopCartItemAddRespDTO().setCode(0);
    }


    /**
     * @param shopCartItemReqDTO 购物车项
     * @return 在原本数量基础上修改
     */
    private ShopCartItemAddRespDTO shopCartModify(ShopCartItemReqDTO shopCartItemReqDTO) {
        BigDecimal modifyNum = shopCartItemReqDTO.getModifyNum();
        Assert.isTrue(modifyNum.compareTo(new BigDecimal(-1)) >= 0, "增减购物车项数量，必须大于等于-1");
        ItemInfoDTO itemInfoDTO = shopCartItemReqDTO.getItemInfoDTO();
        String storeGuid = WeixinUserThreadLocal.getStoreGuid();
        Assert.notNull(storeGuid, "加菜时门店guid不能为空");

        String uniqueKey = ItemInfoDTO.getUniqueKey(itemInfoDTO);
        WxOrderConfigDTO storeConfig = getStoreConfig(storeGuid);
        Assert.notNull(storeConfig, "门店配置异常，不能加菜");
        ShopCartItemReqDTO cartItem = getCartItem(uniqueKey, storeConfig.getOrderModel());
        //没有购物车 新建
        if (cartItem == null) {
            if (modifyNum.compareTo(BigDecimal.ZERO) >= 0) {
                ItemInfoSkuDTO uckSku = PriceCalculateHelper.getUckSku(itemInfoDTO);
                BigDecimal minOrderNum = Optional.ofNullable(uckSku.getMinOrderNum()).orElse(BigDecimal.ZERO);
                BigDecimal newItemCount = modifyNum.compareTo(minOrderNum) < 0 ? minOrderNum : modifyNum;
                WxUserInfoDTO wxUserInfoDTO = WeixinUserThreadLocal.getWxUserInfoDTO();
                Assert.notNull(wxUserInfoDTO, "无法查询到微信用户,不能加菜");
                itemInfoDTO.setCurrentCount(newItemCount);
                String overWeight = overWeight(itemInfoDTO, newItemCount);
                if (!StringUtils.isEmpty(overWeight)) {
                    return ShopCartItemAddRespDTO.addFailed(overWeight);
                }

                shopCartItemReqDTO.setItemInfoDTO(itemInfoDTO);
                shopCartItemReqDTO.setOpenid(wxUserInfoDTO.getOpenId());
                shopCartItemReqDTO.setNickName(wxUserInfoDTO.getNickname());
                shopCartItemReqDTO.setHeadImgUrl(wxUserInfoDTO.getHeadImgUrl());
                shopCartItemReqDTO.setGmtCreate(LocalDateTime.now());
                setCartItem(uniqueKey, shopCartItemReqDTO, storeConfig.getOrderModel());
            }
        } else {
            return getShopCartItemAddRespDTO(cartItem, modifyNum, uniqueKey, storeConfig);
        }
        return new ShopCartItemAddRespDTO().setCode(0);
    }

    private ShopCartItemAddRespDTO getShopCartItemAddRespDTO(ShopCartItemReqDTO cartItem,
                                                             BigDecimal modifyNum,
                                                             String uniqueKey,
                                                             WxOrderConfigDTO storeConfig) {
        ItemInfoDTO itemInfo = cartItem.getItemInfoDTO();
        BigDecimal newItemCount = itemInfo.getCurrentCount().add(modifyNum);
        if (modifyNum.compareTo(new BigDecimal(-1)) == 0) {
            ItemInfoSkuDTO uckSku = PriceCalculateHelper.getUckSku(itemInfo);
            BigDecimal minOrderNum = Optional.ofNullable(uckSku.getMinOrderNum()).orElse(BigDecimal.ZERO);
            if (newItemCount.compareTo(minOrderNum) < 0) {
                delCartItem(uniqueKey, storeConfig.getOrderModel());
                return new ShopCartItemAddRespDTO().setCode(0);
            }
        }
        ItemInfoSkuDTO uckSku = PriceCalculateHelper.getUckSku(itemInfo);
        if (newItemCount.compareTo(uckSku.getMinOrderNum()) < 0) {
            delCartItem(uniqueKey, storeConfig.getOrderModel());
            return new ShopCartItemAddRespDTO().setCode(0);
        }
        String overWeight = overWeight(itemInfo, newItemCount);
        if (!StringUtils.isEmpty(overWeight)) {
            return ShopCartItemAddRespDTO.addFailed(overWeight);
        }
        itemInfo.setCurrentCount(newItemCount);
        cartItem.setItemInfoDTO(itemInfo);
        setCartItem(uniqueKey, cartItem, storeConfig.getOrderModel());
        return new ShopCartItemAddRespDTO().setCode(0);
    }


    /**
     * 查询购物车
     *
     * @return 购物车
     */
    @Override
    public ShopCartRespDTO getMenuItemList(Integer h5flag) {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        ShopCartRespDTO shopCartRespDTO = new ShopCartRespDTO();
        boolean isLogin = isMemberLogin();
        shopCartRespDTO.setIsLogin(isLogin);
        TableDTO tableDetail = getTableDetail(WeixinUserThreadLocal.getDiningTableGuid());
        shopCartRespDTO.setTableCode(tableDetail.getCode());
        shopCartRespDTO.setAreaName(wxMemberSessionDTO.getAreaName());
        List<ShopCartItemReqDTO> cartItemList = getCartItemList();
        log.info("购物车信息：{}", JacksonUtils.writeValueAsString(cartItemList));
        if (CollectionUtils.isEmpty(cartItemList)) {
            // 如果购物车为空
            shopCartRespDTO.setItemInfoDTOS(Lists.newArrayList());
            shopCartRespDTO.setOriginPrice(BigDecimal.ZERO);
            shopCartRespDTO.setPieceCount(BigDecimal.ZERO);
            shopCartRespDTO.setSpeciesCount(0);
            log.info("购物车返回参数：{}", JacksonUtils.writeValueAsString(shopCartRespDTO));
            return shopCartRespDTO;
        }

        // 查询当前会员信息
        UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
        // 重新放入用户上下文
        rePutUserContext(userMemberSession);

        // 计算会员优惠
        calculateMemberDiscount(shopCartRespDTO, cartItemList);

        // 计算限时特价优惠
        if (h5flag == 0) {
            calculateSpecialsActivityDiscount(shopCartRespDTO);
        }
        // 复用加入购物车传递的活动，不再查询商品满减满折活动
        List<ItemInfoDTO> itemInfoDTOList = shopCartRespDTO.getItemInfoDTOS();
        itemInfoDTOList.forEach(itemInfo -> {
            List<ItemInfoSkuDTO> skuList = itemInfo.getSkuList();
            itemInfo.setActivityRuleDescList(skuList.get(0).getActivityRuleDescList());
        });

        return shopCartRespDTO;
    }

    /**
     * 计算限时特价优惠
     *
     * @param shopCartRespDTO 购物车返回数据
     */
    private void calculateSpecialsActivityDiscount(ShopCartRespDTO shopCartRespDTO) {
        List<ItemInfoDTO> itemInfoDTOList = shopCartRespDTO.getItemInfoDTOS();
        if (CollectionUtils.isEmpty(itemInfoDTOList)) {
            log.warn("购物车无商品不做处理");
            return;
        }
        List<String> itemGuidList = itemInfoDTOList.stream()
                .map(ItemInfoDTO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        List<String> parentGuidList = priceCalculateHelper.queryParentGuid(itemGuidList);
        itemGuidList.addAll(parentGuidList);
        List<LimitSpecialsActivityDetailsVO> specialsActivityList = marketingActivityHelper.querySpecialsActivityList(WeixinUserThreadLocal.get());
        List<LimitSpecialsActivityItemVO> activityItemVOList = queryFilterSpecialsActivity(itemGuidList, specialsActivityList);
        if (CollectionUtils.isEmpty(activityItemVOList)) {
            log.warn("没有限时特价活动商品");
            return;
        }
        Map<String, List<LimitSpecialsActivityItemVO>> activity2ItemVOMap = activityItemVOList.stream()
                .collect(Collectors.groupingBy(LimitSpecialsActivityItemVO::getActivityGuid));

        // 计算每个活动的优惠金额
        calculateEachActivityDiscountPrice(specialsActivityList, itemInfoDTOList, activity2ItemVOMap);

        // 根据已加购商品，默认生效最优惠的一个活动
        LimitSpecialsActivityDetailsVO activityMaxDetailsVO = handleDefaultSelect(specialsActivityList);
        log.info("[购物车][最终确定的活动]activityMaxDetailsVO={}", JacksonUtils.writeValueAsString(activityMaxDetailsVO));
        if (ObjectUtils.isEmpty(activityMaxDetailsVO)) {
            log.warn("没有最优惠限时特价活动");
            return;
        }

        // 限时特价优惠 vs 会员优惠，相同：优先取会员优惠
        if (activityMaxDetailsVO.getRelationRule().equals(BooleanEnum.FALSE.getCode()) &&
                Boolean.TRUE.equals(shopCartRespDTO.getEnablePreferentialPrice()) &&
                BigDecimalUtil.greaterEqual(shopCartRespDTO.getDiscountPrice(), activityMaxDetailsVO.getDiscountPrice())) {
            log.warn("会员优惠更优");
            return;
        }

        // 限时特价更优
        List<LimitSpecialsActivityItemVO> specialsActivityItemVOList =
                MarketingActivityAssembler.getLimitSpecialsActivityItemVOS(activityMaxDetailsVO, itemGuidList);
        Map<String, LimitSpecialsActivityItemVO> specialsActivityItemVOMap = specialsActivityItemVOList.stream()
                .collect(Collectors.toMap(LimitSpecialsActivityItemVO::getCommodityId, Function.identity(), (v1, v2) -> v1));

        // 计算前数据净化
        itemInfoDTOList.forEach(item -> {
            item.setSpecialsDiscountPrice(BigDecimal.ZERO);
            item.setSpecialsMemberPrice(BigDecimal.ZERO);
        });
        Map<String, List<ItemInfoDTO>> itemMap = itemInfoDTOList.stream()
                .collect(Collectors.groupingBy(i -> com.holderzone.framework.util.StringUtils.hasText(i.getParentGuid())
                        ? i.getParentGuid() : i.getItemGuid()));

        // 根据各SKU商品售价计算优惠金额
        calculateSingleSpecialsPrice(itemMap, specialsActivityItemVOMap, activityMaxDetailsVO);

        // 处理商品最小金额
        handleShopCartMinPrice(shopCartRespDTO, activityMaxDetailsVO);

        // 处理总价优惠展示
        handleShowPreferential(shopCartRespDTO);

    }

    private void calculateSingleSpecialsPrice(Map<String, List<ItemInfoDTO>> itemMap,
                                              Map<String, LimitSpecialsActivityItemVO> specialsActivityItemVOMap,
                                              LimitSpecialsActivityDetailsVO activityMaxDetailsVO) {
        // 根据各SKU商品售价计算优惠金额
        for (Map.Entry<String, List<ItemInfoDTO>> entry : itemMap.entrySet()) {
            String itemGuid = entry.getKey();
            List<ItemInfoDTO> skuItemList = entry.getValue();
            LimitSpecialsActivityItemVO activityItemVO = specialsActivityItemVOMap.get(itemGuid);
            if (ObjectUtils.isEmpty(activityItemVO)) {
                log.warn("该商品没有匹配的活动,itemGuid={}", itemGuid);
                continue;
            }

            // 计算每个规格的优惠
            calculateEverySkuDiscount(activityMaxDetailsVO, skuItemList, activityItemVO);
            // 包含数量处理
            handleContainNumber(skuItemList, activityItemVO);
            // 排序后重计算
            calculateEverySkuDiscount(activityMaxDetailsVO, skuItemList, activityItemVO);
        }
    }

    private void calculateEverySkuDiscount(LimitSpecialsActivityDetailsVO activityMaxDetailsVO,
                                           List<ItemInfoDTO> skuItemList,
                                           LimitSpecialsActivityItemVO activityItemVO) {
        activityItemVO.setItemUseNumber(0);
        for (ItemInfoDTO skuItem : skuItemList) {
            DineInItemDTO dineInItemDTO = ShopCardItemMAP.INSTANCE.itemInfoDTO2DineInItemDTO(skuItem);
            dineInItemDTO.setOriginalPrice(BigDecimalUtil.divide(dineInItemDTO.getOriginalPrice(), dineInItemDTO.getCurrentCount()));
            if (BigDecimalUtil.greaterThanZero(dineInItemDTO.getMemberPrice())) {
                dineInItemDTO.setMemberPrice(BigDecimalUtil.divide(dineInItemDTO.getMemberPrice(), dineInItemDTO.getCurrentCount()));
            }

            SpecialsActivityAmountBO amountBO = priceCalculateHelper.getSpecialsActivityAmountBO(activityMaxDetailsVO, dineInItemDTO);
            SpecialsActivityAmountDTO amountDTO = priceCalculateHelper.calculateMinPrice(dineInItemDTO, activityItemVO, amountBO);
            skuItem.setSpecialsDiscountPrice(amountDTO.getDiscountPrice());
            skuItem.setSpecialsMemberPrice(amountDTO.getMemberPrice());
            skuItem.setSpecialsSingleDistinctPrice(amountDTO.getSpecialsSingleDistinctPrice());
        }
    }

    private void handleShopCartMinPrice(ShopCartRespDTO shopCartRespDTO,
                                        LimitSpecialsActivityDetailsVO activityMaxDetailsVO) {
        boolean isShare = Objects.equals(BooleanEnum.TRUE.getCode(), activityMaxDetailsVO.getRelationRule());
        if (isShare) {
            // 共享
            isShareHandleItem(shopCartRespDTO);
        } else {
            // 互斥
            unShareHandleItem(shopCartRespDTO);
        }
    }

    private void unShareHandleItem(ShopCartRespDTO shopCartRespDTO) {
        for (ItemInfoDTO itemInfoDTO : shopCartRespDTO.getItemInfoDTOS()) {
            BigDecimal specialsDiscountPrice = itemInfoDTO.getSpecialsDiscountPrice();
            if (ObjectUtils.isEmpty(specialsDiscountPrice)) {
                specialsDiscountPrice = BigDecimal.ZERO;
            }
            BigDecimal specialsPrice = itemInfoDTO.getOriginalPrice().subtract(specialsDiscountPrice);
            if (BigDecimalUtil.lessThan(specialsPrice, itemInfoDTO.getMinPrice())) {
                itemInfoDTO.setMinPrice(specialsPrice.min(itemInfoDTO.getMinPrice()));
                itemInfoDTO.setMinPriceType(MinPriceTypeEnum.LIMITED_SPECIAL_ACTIVITY.getCode());
                shopCartRespDTO.setEnablePreferentialPrice(true);
            } else {
                itemInfoDTO.setMinPrice(itemInfoDTO.getOriginalPrice());
                itemInfoDTO.setMinPriceType(MinPriceTypeEnum.ORIGINAL_PRICE.getCode());
            }
        }
    }

    private void isShareHandleItem(ShopCartRespDTO shopCartRespDTO) {
        for (ItemInfoDTO itemInfoDTO : shopCartRespDTO.getItemInfoDTOS()) {
            BigDecimal specialsPrice = itemInfoDTO.getOriginalPrice()
                    .subtract(itemInfoDTO.getSpecialsDiscountPrice())
                    .subtract(itemInfoDTO.getSpecialsMemberPrice());
            if (BigDecimalUtil.lessThan(specialsPrice, itemInfoDTO.getMinPrice())) {
                itemInfoDTO.setMinPrice(specialsPrice);
                itemInfoDTO.setMinPriceType(MinPriceTypeEnum.LIMITED_SPECIAL_ACTIVITY.getCode());
                shopCartRespDTO.setEnablePreferentialPrice(true);
            }
        }
    }

    /**
     * 根据已加购商品，默认生效最优惠的一个活动
     */
    private LimitSpecialsActivityDetailsVO handleDefaultSelect(List<LimitSpecialsActivityDetailsVO> specialsActivityList) {
        if (CollectionUtils.isEmpty(specialsActivityList)) {
            return null;
        }
        return specialsActivityList.stream()
                .filter(activity -> BigDecimalUtil.greaterThanZero(activity.getDiscountPrice()))
                .max(Comparator.comparing(LimitSpecialsActivityDetailsVO::getDiscountPrice))
                .orElse(null);
    }

    /**
     * 计算每个活动的优惠金额
     */
    private void calculateEachActivityDiscountPrice(List<LimitSpecialsActivityDetailsVO> specialsActivityList,
                                                    List<ItemInfoDTO> itemInfoDTOList,
                                                    Map<String, List<LimitSpecialsActivityItemVO>> activity2ItemVOMap) {
        specialsActivityList.forEach(specialsActivity -> {
            List<LimitSpecialsActivityItemVO> activityItemVOList = activity2ItemVOMap.get(specialsActivity.getGuid());
            BigDecimal specialsTotalPrice = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(activityItemVOList)) {
                Map<String, LimitSpecialsActivityItemVO> activityItemVOMap = activityItemVOList.stream()
                        .collect(Collectors.toMap(LimitSpecialsActivityItemVO::getCommodityId, Function.identity(), (v1, v2) -> v1));
                List<ItemInfoDTO> joinActivityItemList = itemInfoDTOList.stream()
                        .filter(i -> activityItemVOMap.containsKey(i.getItemGuid())
                                || activityItemVOMap.containsKey(i.getParentGuid()))
                        .collect(Collectors.toList());
                // 区分规格
                Map<String, List<ItemInfoDTO>> itemSkuMap = joinActivityItemList.stream()
                        .collect(Collectors.groupingBy(i -> com.holderzone.framework.util.StringUtils.hasText(i.getParentGuid())
                                ? i.getParentGuid() : i.getItemGuid()));

                // 根据各SKU商品售价计算优惠金额
                calculateItemSkuDiscountPrice(activityItemVOMap, itemSkuMap);

                specialsTotalPrice = joinActivityItemList.stream()
                        .map(ItemInfoDTO::getSpecialsDiscountPrice)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

            }
            specialsActivity.setDiscountPrice(specialsTotalPrice);
        });
    }

    /**
     * 计算商品优惠金额
     */
    private void calculateItemSkuDiscountPrice(Map<String, LimitSpecialsActivityItemVO> activityItemVOMap,
                                               Map<String, List<ItemInfoDTO>> itemMap) {
        for (Map.Entry<String, List<ItemInfoDTO>> entry : itemMap.entrySet()) {
            String itemGuid = entry.getKey();
            List<ItemInfoDTO> skuItemList = entry.getValue();
            LimitSpecialsActivityItemVO activityItemVO = activityItemVOMap.get(itemGuid);
            if (ObjectUtils.isEmpty(activityItemVO)) {
                log.warn("该商品没有匹配的活动,itemGuid={}", itemGuid);
                continue;
            }

            // 计算每个规格的优惠
            for (ItemInfoDTO skuItem : skuItemList) {
                DineInItemDTO dineInItemDTO = ShopCardItemMAP.INSTANCE.itemInfoDTO2DineInItemDTO(skuItem);
                dineInItemDTO.setOriginalPrice(BigDecimalUtil.divide(dineInItemDTO.getOriginalPrice(), dineInItemDTO.getCurrentCount()));
                BigDecimal specialsDiscountPrice = priceCalculateHelper.calculateSpecialsActivityDiscountPrice(dineInItemDTO, activityItemVO);
                skuItem.setSpecialsDiscountPrice(specialsDiscountPrice);
            }
        }
    }

    private void handleContainNumber(List<ItemInfoDTO> skuItemList,
                                     LimitSpecialsActivityItemVO activityItemVO) {
        // 从大到小排列优先命中更优商品
        skuItemList.sort(Comparator.comparing(ItemInfoDTO::getSpecialsSingleDistinctPrice).reversed());
        // 已扣除数量
        BigDecimal deductionNumber = BigDecimal.ZERO;

        // 查询会员权益
        ResponseProductDiscount responseProductDiscount = marketingActivityHelper.queryMemberRights(WeixinUserThreadLocal.getWxtoken());
        log.info("会员权益缓存:{}", JacksonUtils.writeValueAsString(responseProductDiscount));

        for (ItemInfoDTO skuItem : skuItemList) {
            // 限时特价优惠 vs 会员优惠，相同：优先取会员优惠（即不做处理）
            BigDecimal memberSingleDiscountPrice = getMemberSingleDiscountPrice(skuItem, responseProductDiscount);
            if (BigDecimalUtil.greaterEqual(memberSingleDiscountPrice, skuItem.getSpecialsSingleDistinctPrice())) {
                log.warn("优先取会员优惠");
                continue;
            }

            // 优惠限购为空表示不限制
            boolean isLimit = !ObjectUtils.isEmpty(activityItemVO.getLimitNumber());
            if (isLimit) {
                BigDecimal limitNumber = BigDecimal.valueOf(activityItemVO.getLimitNumber()).subtract(deductionNumber);
                if (BigDecimalUtil.greaterThan(skuItem.getCurrentCount(), limitNumber)) {
                    skuItem.setContainNumber(limitNumber.intValue());
                } else {
                    skuItem.setContainNumber(skuItem.getCurrentCount().intValue());
                    deductionNumber = deductionNumber.add(skuItem.getCurrentCount());
                }
            } else {
                skuItem.setContainNumber(skuItem.getCurrentCount().intValue());
            }
        }
    }

    @NotNull
    private BigDecimal getMemberSingleDiscountPrice(ItemInfoDTO skuItem, ResponseProductDiscount responseProductDiscount) {
        BigDecimal memberSingleDiscountPrice = BigDecimal.ZERO;
        if (responseProductDiscount.getMemberRightsType().equals(MemberRightsType.MEMBER_PRICE.getCode()) &&
                (BigDecimalUtil.greaterThanZero(skuItem.getMemberPrice()))) {
            BigDecimal memberDiscountPrice = skuItem.getOriginalPrice().subtract(skuItem.getMemberPrice());
            memberSingleDiscountPrice = BigDecimalUtil.divide(memberDiscountPrice, skuItem.getCurrentCount());

        }
        if (responseProductDiscount.getMemberRightsType().equals(MemberRightsType.MEMBER_DISCOUNT.getCode())) {
            memberSingleDiscountPrice = BigDecimalUtil.divide(skuItem.getOriginalPrice(), skuItem.getCurrentCount())
                    .multiply(responseProductDiscount.getDiscountValue())
                    .divide(BigDecimal.TEN, 2, RoundingMode.HALF_UP);
        }
        return memberSingleDiscountPrice;
    }

    /**
     * 处理总价优惠展示
     */
    private void handleShowPreferential(ShopCartRespDTO shopCartRespDTO) {
        if (Boolean.TRUE.equals(shopCartRespDTO.getEnablePreferentialPrice())) {
            BigDecimal preferentialPrice = shopCartRespDTO.getItemInfoDTOS().stream()
                    .map(ItemInfoDTO::getMinPrice)
                    .filter(price -> !ObjectUtils.isEmpty(price))
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);
            shopCartRespDTO.setPreferentialPrice(preferentialPrice);
            shopCartRespDTO.setDiscountPrice(shopCartRespDTO.getOriginPrice().subtract(preferentialPrice));
        }
    }

    @Override
    public List<UserMemberCardCacheDTO> cardList() {
        return getCardList();
    }


    @Override
    public Boolean switchCard(String memberInfoCardGuid) {
        UserMemberSessionDTO userMemberSessionDTO = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
        userMemberSessionDTO.setMemberInfoCardGuid(StringUtils.isEmpty(memberInfoCardGuid) ? "-1" : memberInfoCardGuid);
        userMemberSessionDTO.setMemberIntegral(1);
        userMemberSessionUtils.addUserMemberSession(userMemberSessionDTO);
        // 更新卡列表缓存
        userMemberSessionUtils.delCardList(WeixinUserThreadLocal.getStoreGuid(), WeixinUserThreadLocal.getOpenId());
        return true;
    }

    @Override
    public Boolean clearShopCart() {
        WxOrderConfigDTO storeConfig = getStoreConfig(WeixinUserThreadLocal.getStoreGuid());
        Boolean delSuccess = storeConfig.getOrderModel() == 0
                ? redisUtils.delete(CacheName.SHOP_CART_ITEM + ":" + WeixinUserThreadLocal.getStoreGuid() + ":" +
                WeixinUserThreadLocal.getDiningTableGuid())
                : redisUtils.delete(CacheName.SHOP_CART_ITEM + ":" + WeixinUserThreadLocal.getStoreGuid() + ":" +
                WeixinUserThreadLocal.getOpenId());
        if (delSuccess != null && delSuccess) {
            sendShopCartMessage();
        }
        return delSuccess;
    }

    @Override
    public PreOrderRespDTO preOrderList(Integer wxAddItemFlag) {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        WxOrderConfigDTO storeConfig = getStoreConfig(wxMemberSessionDTO.getStoreGuid());
        List<ShopCartItemReqDTO> cartItemList = getCartItemList(storeConfig.getOrderModel());
        return preOrderList(cartItemList, storeConfig.getOrderModel(), wxAddItemFlag);
    }

    private PreOrderRespDTO preOrderList(List<ShopCartItemReqDTO> shopCartItemReqDTOS, Integer orderModel, Integer wxAddItemFlag) {
        boolean isLogin = WeixinUserThreadLocal.getIsLogin();
        log.info("确认页：登录状态:{}", isLogin);
        if (CollectionUtils.isEmpty(shopCartItemReqDTOS)) {
            // 如果为空， 直接返回
            sendShopCartMessage();
            return buildEmptyItemRespDTO(orderModel);
        }

        BigDecimal totalPrice = BigDecimal.ZERO;
        boolean totalMemberEnable = false;
        boolean enablePreferentialPrice = false;
        boolean enableMemberPrice = false;
        if (isLogin) {
            UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
            enableMemberPrice = hasMemberPrice(userMemberSession.getMemberInfoCardGuid());
        }
        final boolean hasMemberPrice = enableMemberPrice;
        boolean loginAndHasMemberPrice = isLogin && hasMemberPrice;

        List<ItemInfoDTO> collect = shopCartItemReqDTOS.stream()
                .map(ShopCartItemReqDTO::getItemInfoDTO)
                .collect(Collectors.toList());
        collect.forEach(x -> {
            PricePairDTO pricePairDTO = itemPrice(x);
            BigDecimal itemMemberPrice = pricePairDTO.getMemberPrice();
            x.setShowPrice(pricePairDTO.getOriginPrice().setScale(2, RoundingMode.HALF_UP));
            x.setEnablePreferentialPrice(false);
            x.setShowMemberPrice(BigDecimal.ZERO);
            if (loginAndHasMemberPrice && itemMemberPrice != null && itemMemberPrice.compareTo(BigDecimal.ZERO) > 0) {
                x.setEnablePreferentialPrice(true);
                x.setShowMemberPrice(itemMemberPrice.setScale(2, RoundingMode.HALF_UP));
            }
        });
        PricePairDTO pricePairDTO = orderPrice(collect);
        BigDecimal originOrderPrice = pricePairDTO.getOriginPrice().setScale(2, RoundingMode.HALF_UP).stripTrailingZeros();
        if (isLogin && enableMemberPrice && pricePairDTO.getMemberPrice().compareTo(BigDecimal.ZERO) > 0) {
            enablePreferentialPrice = true;
        }
        for (ItemInfoDTO itemInfoDTO : collect) {
            BigDecimal showMemberPrice = itemInfoDTO.getShowMemberPrice();
            if (Boolean.TRUE.equals(itemInfoDTO.getEnablePreferentialPrice() && showMemberPrice != null)
                    && showMemberPrice.compareTo(BigDecimal.ZERO) > 0) {
                totalMemberEnable = true;
                totalPrice = totalPrice.add(showMemberPrice);
            } else {
                totalPrice = totalPrice.add(itemInfoDTO.getShowPrice());
            }
        }
        Collection<List<ShopCartItemReqDTO>> values = shopCartItemReqDTOS.stream()
                .collect(Collectors.groupingBy(ShopCartItemReqDTO::getOpenid)).values();
        List<PreOrderPersonDTO> preOrderPersonDTOS = transformPreOrderPerson(values, enablePreferentialPrice, wxAddItemFlag);
        sendShopCartMessage();
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        log.info("当前wxMemberSessionDTO：{}", JacksonUtils.writeValueAsString(wxMemberSessionDTO));
        return new PreOrderRespDTO().setStoreName(wxMemberSessionDTO.getStoreName())
                .setEnableKeyboard(isEnableKeyboard(orderModel, wxMemberSessionDTO))
                .setAreaName(wxMemberSessionDTO.getAreaName())
                .setTableCode(wxMemberSessionDTO.getDiningTableCode())
                .setBrandName(wxMemberSessionDTO.getBrandName())
                .setIsLogin(isLogin)
                .setGuestCount(queryGuestCount())
                .setPreOrderRemark(null)
                .setPreOrderPersonDTOS(preOrderPersonDTOS)
                .setOriginPreOrderPrice(originOrderPrice)
                .setPreOrderRemark("")
                .setEnablePreferentialPrice(totalMemberEnable)
                .setAppendFee(calculateAppendFee(preOrderPersonDTOS))
                .setPreferentialPreOrderPrice(totalPrice);
    }

    /**
     * 是否需要启用键盘输入人数
     */
    private boolean isEnableKeyboard(Integer orderModel, WxMemberSessionDTO wxMemberSessionDTO) {
        boolean dineEnableKeyboard = orderModel == 1 || userCount() == null;
        if (orderModel == 0 && userCount() == null) {
            String orderGuidByTableGuid = wxStoreTableClientService.getOrderGuidByTableGuid(wxMemberSessionDTO.getDiningTableGuid());
            log.info("查询当前桌台是否有订单：{}", orderGuidByTableGuid);
            dineEnableKeyboard = StringUtils.isEmpty(orderGuidByTableGuid);
        }
        return dineEnableKeyboard;
    }


    /**
     * 构建一个商品为空的预订单对象
     */
    private PreOrderRespDTO buildEmptyItemRespDTO(Integer orderModel) {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        return new PreOrderRespDTO().setStoreName(wxMemberSessionDTO.getStoreName())
                .setAreaName(wxMemberSessionDTO.getAreaName())
                .setTableCode(wxMemberSessionDTO.getDiningTableCode())
                .setBrandName(wxMemberSessionDTO.getBrandName())
                .setEnableKeyboard(orderModel == 1 || userCount() == null)
                .setIsLogin(WeixinUserThreadLocal.getIsLogin())
                .setGuestCount(queryGuestCount())
                .setPreOrderRemark(null)
                .setPreOrderPersonDTOS(Collections.emptyList())
                .setOriginPreOrderPrice(BigDecimal.ZERO)
                .setPreOrderRemark("")
                .setEnablePreferentialPrice(false)
                .setAppendFee(calculateAppendFee(Collections.emptyList()))
                .setPreferentialPreOrderPrice(BigDecimal.ZERO);
    }

    private List<PreOrderPersonDTO> transformPreOrderPerson(Collection<List<ShopCartItemReqDTO>> values, boolean enablePreferentialPrice,
                                                            Integer wxAddItemFlag) {
        UserContext userContext = UserContextUtils.get();
        String enterpriseGuid = WeixinUserThreadLocal.getEnterpriseGuid();
        String operSubjectGuid = userContext.getOperSubjectGuid();
        boolean isLogin = WeixinUserThreadLocal.getIsLogin();
        String currentOpenId = WeixinUserThreadLocal.getOpenId();
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        return values.parallelStream().map(x -> {
            dynamicDatasource(userContext, enterpriseGuid);
            WeixinUserThreadLocal.set(wxMemberSessionDTO);
            ShopCartItemReqDTO shopCartItemReqDTO = x.get(0);
            String itemOpenid = shopCartItemReqDTO.getOpenid();
            boolean isLoginThis = false;
            if (currentOpenId.equals(itemOpenid)) {
                isLoginThis = isLogin;
            } else {

                WxUserRecordDTO userByOpenId = wxUserRecordClientService.getUserByOpenId(itemOpenid);
                if (Objects.nonNull(userByOpenId)) {
                    isLoginThis = userByOpenId.getIsLogin();
                } else {
                    isLoginThis = false;
                }

            }
            if (isLoginThis) {
                Integer memberState = wxHsmMemberBasicBufferService.getWxMemberState(itemOpenid, enterpriseGuid, operSubjectGuid);
                isLoginThis = memberState != null && memberState == 0;
            }
            return new PreOrderPersonDTO()
                    .setHeadImgUrl(shopCartItemReqDTO.getHeadImgUrl())
                    .setNickName(shopCartItemReqDTO.getNickName())
                    .setOpenid(shopCartItemReqDTO.getOpenid())
                    .setLoginMember(isLoginThis)
                    .setPreOrderItemDTOS(DineInItemHelper.transformPreOrderItem(x, enablePreferentialPrice))
                    .setPreOrderSurchargeList(transformPreOrderSurcharge(wxAddItemFlag));
        }).collect(Collectors.toList());
    }

    /**
     * 查询附加费明细
     */
    private List<PreOrderSurchargeDTO> transformPreOrderSurcharge(Integer wxAddItemFlag) {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        wxMemberSessionDTO.setOrderState(Objects.isNull(wxMemberSessionDTO.getOrderState()) ? TradeModeEnum.FAST.getCode() : wxMemberSessionDTO.getOrderState());
        // 如果是加菜返回的订单确认页面， 则需要查询加菜之前的附加费
        List<SurchargeLinkDTO> surchargeLinkList = null;
        if (wxAddItemFlag == 1) {
            surchargeLinkList = wxStoreTradeOrderService.queryAddItemBeforeSurcharges();
        }
        if (Objects.isNull(surchargeLinkList)) {
            surchargeLinkList = wxStoreTradeOrderService.querySurchargeList(wxMemberSessionDTO.getOrderState(),
                    WeixinUserThreadLocal.getAreaGuid(), WeixinUserThreadLocal.getDiningTableGuid());
        }
        if (CollectionUtils.isEmpty(surchargeLinkList)) {
            return Collections.emptyList();
        }
        // 查询点餐人数
        Integer count = queryGuestCount();
        log.info("查询附加费明细, tableGuid:{},openId:{},人数:{}", WeixinUserThreadLocal.getDiningTableGuid(), WeixinUserThreadLocal.getOpenId(), count);
        if (Objects.isNull(count) || count == 0) {
            surchargeLinkList = surchargeLinkList.stream().filter(e -> AppendFeeTypeEnum.BY_TABLE.getCode() == e.getType()).collect(Collectors.toList());
        }
        return surchargeLinkList.stream().map(e -> {
            PreOrderSurchargeDTO preOrderSurchargeDTO = new PreOrderSurchargeDTO();
            preOrderSurchargeDTO.setSurchargeGuid(e.getSurchargeGuid());
            preOrderSurchargeDTO.setAreaGuid(e.getAreaGuid());
            preOrderSurchargeDTO.setName(e.getName());
            if (AppendFeeTypeEnum.BY_NUM.getCode() == e.getType()) {
                preOrderSurchargeDTO.setCount(count);
            } else {
                preOrderSurchargeDTO.setCount(1);
            }
            preOrderSurchargeDTO.setAmount(e.getAmount());
            preOrderSurchargeDTO.setTotalAmount(e.getAmount().multiply(new BigDecimal(preOrderSurchargeDTO.getCount())));
            preOrderSurchargeDTO.setType(e.getType());
            preOrderSurchargeDTO.setTradeMode(e.getTradeMode());
            preOrderSurchargeDTO.setEffectiveTime(e.getEffectiveTime());
            return preOrderSurchargeDTO;
        }).collect(Collectors.toList());
    }


    /**
     * 查询点餐人数
     */
    @Override
    public Integer queryGuestCount() {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        wxMemberSessionDTO.setOrderState(Objects.isNull(wxMemberSessionDTO.getOrderState()) ? TradeModeEnum.FAST.getCode() : wxMemberSessionDTO.getOrderState());
        // 查询点餐人数
        return TradeModeEnum.DINEIN.getCode() == wxMemberSessionDTO.getOrderState() ?
                userCount() : wxStoreTradeOrderService.getFastGuestCount(WeixinUserThreadLocal.getDiningTableGuid(), WeixinUserThreadLocal.getOpenId());
    }

    @Override
    public List<StoreActivityRespDTO> queryStoreActivity() {
        List<StoreActivityRespDTO> storeActivityList = new ArrayList<>();
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        UserContext userContext = UserContextUtils.get();
        userContext.setSource(LoginSource.MINAPP.code());
        UserContextUtils.put(userContext);

        // 满减满折活动
        CompletableFuture<ResponseMarketActivitieRule> marketActivityFuture = getMarketActivityFuture(wxMemberSessionDTO, userContext);

        // 限时特价活动
        CompletableFuture<List<LimitSpecialsActivityDetailsVO>> specialsActivityFuture = getSpecialsActivityFuture(wxMemberSessionDTO, userContext);

        // 第N份优惠活动
        CompletableFuture<List<NthActivityDetailsVO>> nthActivityFuture = getNthActivityListFuture(wxMemberSessionDTO, userContext, 1);

        CompletableFuture<Void> all = CompletableFuture.allOf(marketActivityFuture, specialsActivityFuture);
        try {
            all.get();

            // 满减满折
            handleMarketActivity(marketActivityFuture.get(), storeActivityList);

            // 限时特价
            handleSpecialsActivity(specialsActivityFuture.get(), storeActivityList);

            // 第N份优惠活动
            handleNthActivity(nthActivityFuture.get(), storeActivityList);

        } catch (Exception e) {
            log.error("查询门店活动列表失败", e);
            Thread.currentThread().interrupt();
            throw new BusinessException("系统繁忙稍后再试");
        }
        return storeActivityList;
    }

    private void handleNthActivity(List<NthActivityDetailsVO> nthActivityList,
                                   List<StoreActivityRespDTO> storeActivityList) {
        List<String> itemGuidList = getItemGuidList();

        // 商品校验
        MarketingActivityHelper.nthFilterItem(itemGuidList, nthActivityList, 1);
        MarketingActivityHelper.addNthActivityList(nthActivityList, storeActivityList);
    }

    @NotNull
    private List<String> getItemGuidList() {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        String redisKey = String.format(RedisKeyConstant.SCAN_ORDER_ITEM_GUID, wxMemberSessionDTO.getStoreGuid());
        Object redisValue = redisUtils.get(redisKey);
        log.info("[扫码查询到的商品id]门店:{},缓存={}", wxMemberSessionDTO.getStoreGuid(), redisValue);
        List<String> itemGuidList = new ArrayList<>();
        if (redisValue == null) {
            ItemAndTypeForAndroidRespDTO item = getItem(wxMemberSessionDTO.getStoreGuid(), wxMemberSessionDTO.getOrderState());
            if (!ObjectUtils.isEmpty(item)) {
                itemGuidList = item.getItemList().stream()
                        .map(ItemSynRespDTO::getItemGuid)
                        .distinct()
                        .collect(Collectors.toList());
                List<String> itemParentGuidList = item.getItemList().stream()
                        .map(ItemSynRespDTO::getParentGuid)
                        .filter(com.holderzone.framework.util.StringUtils::hasText)
                        .distinct()
                        .collect(Collectors.toList());
                itemGuidList.addAll(itemParentGuidList);
                redisUtils.setEx(redisKey, itemGuidList, 30, TimeUnit.MINUTES);
            }
        } else {
            itemGuidList = (List<String>) redisValue;
        }
        log.info("[最终的商品id]itemGuidList={}", itemGuidList);
        return itemGuidList;
    }

    /**
     * 第N份优惠活动
     */
    private CompletableFuture<List<NthActivityDetailsVO>> getNthActivityListFuture(WxMemberSessionDTO wxMemberSessionDTO,
                                                                                   UserContext userInfoDTO,
                                                                                   Integer nthFlag) {
        return CompletableFuture.supplyAsync(() -> {
            EnterpriseIdentifier.setEnterpriseGuid(userInfoDTO.getEnterpriseGuid());
            UserContextUtils.put(userInfoDTO);
            // h5不查询第N份优惠活动
            if (BooleanEnum.FALSE.getCode() == nthFlag) {
                return new ArrayList<NthActivityDetailsVO>();
            }
            return marketingActivityHelper.queryNthActivityList(wxMemberSessionDTO);
        }, menuItemExecutor).exceptionally(throwable -> {
            log.error("查询[第N份优惠活动]失败", throwable);
            return null;
        });
    }

    @Override
    public OrderSubmitRespDTO preSubmit(OrderSubmitReqDTO orderSubmitReqDTO) {
        WxOrderConfigDTO detailConfig = wxClientService.getDetailConfig(new WxStoreReqDTO(WeixinUserThreadLocal.getStoreGuid()));
        log.info("[查询门店配置返回]={}", JacksonUtils.writeValueAsString(detailConfig));
        if (ObjectUtils.isEmpty(detailConfig) || ObjectUtils.isEmpty(detailConfig.getOrderModel())) {
            return OrderSubmitRespDTO.unSet();
        }
        if (Objects.equals(detailConfig.getOrderModel(), TradeModeEnum.DINEIN.getCode())) {
            return wxClientService.submitDine(orderSubmitReqDTO);
        }

        return submitFast(orderSubmitReqDTO);
    }

    @Override
    public CheckMustItemRespDTO checkMustItem(OrderMustPointCheckDTO orderMustPointCheckDTO) {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        log.info("[checkMustItem.wxMemberSessionDTO]={}", JacksonUtils.writeValueAsString(wxMemberSessionDTO));
        log.info("[checkMustItem.orderMustPointCheckDTO]={}", JacksonUtils.writeValueAsString(orderMustPointCheckDTO));
        if (CollectionUtils.isEmpty(orderMustPointCheckDTO.getShopCartItemList())) {
            throw new BusinessException("请选择商品");
        }
        CheckMustItemRespDTO checkMustItemRespDTO = new CheckMustItemRespDTO();
        checkMustItemRespDTO.setIsCheckMust(true);

        List<WxStoreUserOrderItemDTO> winOrderItems = wxClientService.getUnFinishStoreUserOrderList();
        log.info("[getUnFinishStoreUserOrderList返参]={}", JacksonUtils.writeValueAsString(winOrderItems));
        if (CollUtil.isNotEmpty(winOrderItems)) {
            return checkMustItemRespDTO;
        }

        TypeSingleDTO typeSingleDTO = new TypeSingleDTO();
        typeSingleDTO.setIsModel(1);
        typeSingleDTO.setData(orderMustPointCheckDTO.getStoreGuid());
        typeSingleDTO.setStoreGuid(orderMustPointCheckDTO.getStoreGuid());
        log.info("[queryStoreItemType入参]={}", JacksonUtils.writeValueAsString(typeSingleDTO));
        List<ItemInfoTypeRespDTO> itemInfoTypeRespDTOS = itemClientService.queryStoreItemType(typeSingleDTO);
        log.info("[queryStoreItemType返参]={}", JacksonUtils.writeValueAsString(itemInfoTypeRespDTOS));

        List<ItemInfoTypeRespDTO> infoTypeRespDTOS = itemInfoTypeRespDTOS.stream().filter(e -> e.getIsMustPoint() == 1)
                .collect(Collectors.toList());

        //判断是否下单必点商品
        if (CollUtil.isNotEmpty(infoTypeRespDTOS)) {
            log.info("[必点商品类型]={}", JacksonUtils.writeValueAsString(infoTypeRespDTOS));
            String typeGuid = infoTypeRespDTOS.get(0).getTypeGuid();
            Set<String> typeGuidSet = orderMustPointCheckDTO.getShopCartItemList()
                    .stream().map(ItemInfoDTO::getTypeGuid)
                    .collect(Collectors.toSet());
            checkMustItemRespDTO.setTypeGuid(typeGuid);
            checkMustItemRespDTO.setIsCheckMust(typeGuidSet.contains(typeGuid));
        }
        return checkMustItemRespDTO;
    }

    private OrderSubmitRespDTO submitFast(OrderSubmitReqDTO orderSubmitReqDTO) {
        if (orderSubmitReqDTO == null || orderSubmitReqDTO.getUserCount() == null) {
            log.error("快餐下单人数异常:{}", orderSubmitReqDTO);
            return OrderSubmitRespDTO.submitFailed();
        }

        UserContext userContext = UserContextUtils.get();
        userContext.setOperSubjectGuid(WeixinUserThreadLocal.get().getOperSubjectGuid());
        userContext.setEnterpriseGuid(WeixinUserThreadLocal.getEnterpriseGuid());
        userContext.setStoreGuid(WeixinUserThreadLocal.getStoreGuid());
        UserContextUtils.put(userContext);

        String shopCartItemKey = CacheName.SHOP_CART_ITEM + ":" + WeixinUserThreadLocal.getStoreGuid() + ":" + WeixinUserThreadLocal.getOpenId();
        List<ShopCartItemReqDTO> shopCartItemReqDTOS = redisUtils.hValues(shopCartItemKey);
        Assert.notEmpty(shopCartItemReqDTOS, "未选择商品，无法下单");
        // 排序
        shopCartItemReqDTOS.sort(Comparator.comparing(ShopCartItemReqDTO::getGmtCreate).reversed());
        List<ItemInfoDTO> shopCartItemList = shopCartItemReqDTOS.stream()
                .map(ShopCartItemReqDTO::getItemInfoDTO)
                .collect(Collectors.toList());
        subFilter(shopCartItemList);
        attrFilter(shopCartItemList);
        String msg = DineInItemHelper.orderUpperLimit(shopCartItemList);
        if (!StringUtils.isEmpty(msg)) {
            return OrderSubmitRespDTO.upperLimit(msg);
        }

        BaseDTO baseDTO = new BaseDTO();
        baseDTO.setStoreGuid(WeixinUserThreadLocal.getStoreGuid());
        baseDTO.setEnterpriseGuid(WeixinUserThreadLocal.getEnterpriseGuid());
        List<ItemEstimateForAndroidRespDTO> estimate = itemClientService.queryEstimateForSyn(baseDTO);
        List<ItemInfoEstimateVerifyDTO> verifyDTOS = itemEstimateVerify(shopCartItemList, estimate);
        if (!com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(verifyDTOS)) {
            // 商品被估清提示信息
            return OrderSubmitRespDTO.estimateFaild(buildItemEstimateTips(shopCartItemList, verifyDTOS));
        }

        // 下单处理订单商品
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        String redisKey = String.format(RedisKeyConstant.CALCULATE_ITEM_INFO, wxMemberSessionDTO.getStoreGuid(),
                wxMemberSessionDTO.getWxtoken());
        String calculateItemInfoJson = (String) redisUtils.get(redisKey);
        if (com.holderzone.framework.util.StringUtils.hasText(calculateItemInfoJson)) {
            redisUtils.delete(redisKey);
            List<DineInItemDTO> allItems = JacksonUtils.toObjectList(DineInItemDTO.class, calculateItemInfoJson);
            orderSubmitReqDTO.setCalculateItemResultList(allItems);
        }

        orderSubmitReqDTO.setShopCartItemReqDTOS(shopCartItemReqDTOS);
        orderSubmitReqDTO.setShopCartItemList(shopCartItemList);
        OrderSubmitRespDTO submitRespDTO = wxClientService.submitFast(orderSubmitReqDTO);

        // 下单后处理优惠券
        handleVolume(orderSubmitReqDTO, shopCartItemList, submitRespDTO);
        return submitRespDTO;
    }

    private void handleVolume(OrderSubmitReqDTO orderSubmitReqDTO,
                              List<ItemInfoDTO> shopCartItemList,
                              OrderSubmitRespDTO submitRespDTO) {
        if (CollectionUtils.isEmpty(orderSubmitReqDTO.getVolumeCodes())) {
            log.warn("没有使用优惠券，直接返回");
            return;
        }
        CalculateOrderDTO calculateQuery = getCalculateQuery(orderSubmitReqDTO, shopCartItemList, submitRespDTO.getOrderGuid());
        try {
            log.info("小程序下单验券,calculateQuery={}", JacksonUtils.writeValueAsString(calculateQuery));
            calculateService.calculate(calculateQuery);
        } catch (Exception e) {
            String message = e.getMessage();
            if (message.contains("优惠券正在使用中")) {
                List<String> volumeCodes = orderSubmitReqDTO.getVolumeCodes();
                volumeCodes.forEach(volumeCode -> {
                    if (!StringUtils.isEmpty(volumeCode) && volumeCode.length() > 5) {
                        calculateQuery.setVerify(2);
                        calculateService.calculate(calculateQuery);
                    }
                });
            }
            log.error("[下单计算异常]参数calculateQuery={}，异常=", JacksonUtils.writeValueAsString(calculateQuery), e);
        }
    }

    @NotNull
    private CalculateOrderDTO getCalculateQuery(OrderSubmitReqDTO orderSubmitReqDTO,
                                                List<ItemInfoDTO> shopCartItemList,
                                                String orderGuid) {
        CalculateOrderDTO calculateQuery = new CalculateOrderDTO();
        // 目前不使用积分
        calculateQuery.setMemberIntegralStore(Boolean.FALSE);
        calculateQuery.setUseMemberDiscountFlag(orderSubmitReqDTO.getUseMemberDiscountFlag());
        calculateQuery.setVolumeCodes(orderSubmitReqDTO.getVolumeCodes());
        calculateQuery.setActivitySelectList(orderSubmitReqDTO.getActivitySelectList());
        calculateQuery.setDineInItemList(transformFastItem(shopCartItemList));
        calculateQuery.setIsFirst(Boolean.FALSE);
        calculateQuery.setOrderGuid(orderGuid);
        calculateQuery.setVerify(VolumeVerifyEnum.VERIFY.getCode());
        return calculateQuery;
    }

    /**
     * 快餐商品转换
     *
     * @param itemInfoDTOS 购物车源商品集合
     * @return 商品集合
     */
    private List<DineInItemDTO> transformFastItem(List<ItemInfoDTO> itemInfoDTOS) {
        return ObjectUtils.isEmpty(itemInfoDTOS)
                ? Collections.emptyList()
                : itemInfoDTOS.stream().map(x -> {
            DineInItemDTO dineInItemDTO = new DineInItemDTO();
            dineInItemDTO.setUserWxPublicOpenId(WeixinUserThreadLocal.getOpenId());
            dineInItemDTO.setItemGuid(x.getItemGuid());
            dineInItemDTO.setItemName(x.getName());
            ItemInfoSkuDTO uckSku = PriceCalculateHelper.getUckSku(x);
            dineInItemDTO.setCode(uckSku.getCode());
            Integer itemType = x.getItemType();
            if (x.getItemType() == ItemTypeEnum.GROUP.getCode()) {
                itemType = ItemTypeEnum.PKG.getCode();
            }
            dineInItemDTO.setItemType(itemType);
            dineInItemDTO.setItemState(1);
            dineInItemDTO.setItemTypeGuid(x.getTypeGuid());
            dineInItemDTO.setItemTypeName(x.getTypeName());
            dineInItemDTO.setSkuGuid(uckSku.getSkuGuid());
            dineInItemDTO.setSkuName(uckSku.getName());
            dineInItemDTO.setPrice(uckSku.getSalePrice());
            dineInItemDTO.setItemPrice(itemPrice(x).getOriginPrice());
            dineInItemDTO.setOriginalPrice(uckSku.getSalePrice().multiply(x.getCurrentCount()));
            BigDecimal memberPrice = uckSku.getMemberPrice();
            if (Objects.nonNull(memberPrice) && memberPrice.compareTo(BigDecimal.ZERO) <= 0) {
                memberPrice = null;
            }
            if (Objects.nonNull(memberPrice)) {
                dineInItemDTO.setMemberPrice(memberPrice.multiply(x.getCurrentCount()));
            }
            dineInItemDTO.setCurrentCount(x.getCurrentCount());
            dineInItemDTO.setFreeCount(BigDecimal.ZERO);
            dineInItemDTO.setReturnCount(BigDecimal.ZERO);
            dineInItemDTO.setUnit(uckSku.getUnit());
            dineInItemDTO.setIsPay(0);
            dineInItemDTO.setPriceChangeType(0);
            dineInItemDTO.setSingleItemAttrTotal(getAttrTotalPrice(x.getAttrGroupList()));
            dineInItemDTO.setIsMemberDiscount(uckSku.getIsMemberDiscount());
            dineInItemDTO.setIsWholeDiscount(uckSku.getIsWholeDiscount());
            dineInItemDTO.setMinOrderNum(uckSku.getMinOrderNum());
            dineInItemDTO.setFreeItemDTOS(Collections.emptyList());
            dineInItemDTO.setItemAttrDTOS(transformItemAttrGroup(x.getAttrGroupList()));
            dineInItemDTO.setPackageSubgroupDTOS(transformSubgroup(x.getSubgroupList()));
            return dineInItemDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 套餐子项转换
     *
     * @param subItemSkuList 子项
     * @return 转换
     */
    private List<SubDineInItemDTO> transformSubDineInItem(List<ItemInfoSubSkuDTO> subItemSkuList, Integer pickNum) {
        LinkedList<SubDineInItemDTO> subDineInItemDTOS = new LinkedList<>();
        for (ItemInfoSubSkuDTO itemInfoSubSkuDTO : subItemSkuList) {
            log.info("商品转换:套餐子商品:{}", itemInfoSubSkuDTO);
            if (pickNum == 0 || itemInfoSubSkuDTO.getDefaultNum() > 0) {
                SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
                subDineInItemDTO.setItemGuid(itemInfoSubSkuDTO.getItemGuid());
                subDineInItemDTO.setItemName(itemInfoSubSkuDTO.getItemName());
                subDineInItemDTO.setCode(itemInfoSubSkuDTO.getCode());
                subDineInItemDTO.setItemType(itemInfoSubSkuDTO.getItemType());
                subDineInItemDTO.setItemTypeGuid(itemInfoSubSkuDTO.getItemTypeGuid());
                subDineInItemDTO.setItemTypeName(itemInfoSubSkuDTO.getItemTypeName());
                subDineInItemDTO.setSkuGuid(itemInfoSubSkuDTO.getSkuGuid());
                subDineInItemDTO.setSkuName(itemInfoSubSkuDTO.getSkuName());
                subDineInItemDTO.setUnit(itemInfoSubSkuDTO.getUnit());
                subDineInItemDTO.setPrice(BigDecimal.ZERO);
                subDineInItemDTO.setCurrentCount(new BigDecimal(itemInfoSubSkuDTO.getDefaultNum()));
                subDineInItemDTO.setPackageDefaultCount(itemInfoSubSkuDTO.getItemNum());
                subDineInItemDTO.setSingleItemAttrTotal(getAttrTotalPrice(itemInfoSubSkuDTO.getAttrGroupList()));
                subDineInItemDTO.setAddPrice(itemInfoSubSkuDTO.getAddPrice());
                subDineInItemDTO.setItemAttrDTOS(transformItemAttrGroup(itemInfoSubSkuDTO.getAttrGroupList()));
                subDineInItemDTOS.add(subDineInItemDTO);
            }
        }
        return subDineInItemDTOS;
    }

    /**
     * 套餐分组转换
     *
     * @param itemInfoSubgroupDTOS 分组集合
     * @return 转换
     */
    private List<PackageSubgroupDTO> transformSubgroup(List<ItemInfoSubgroupDTO> itemInfoSubgroupDTOS) {
        if (ObjectUtils.isEmpty(itemInfoSubgroupDTOS)) {
            return Collections.emptyList();
        }
        LinkedList<PackageSubgroupDTO> packageSubgroupDTOS = new LinkedList<>();
        for (ItemInfoSubgroupDTO itemInfoSubgroupDTO : itemInfoSubgroupDTOS) {
            PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
            packageSubgroupDTO.setSubgroupGuid(itemInfoSubgroupDTO.getSubgroupGuid());
            packageSubgroupDTO.setSubgroupName(itemInfoSubgroupDTO.getName());
            packageSubgroupDTO.setSubDineInItemDTOS(transformSubDineInItem(itemInfoSubgroupDTO.getSubItemSkuList(), itemInfoSubgroupDTO.getPickNum()));
            packageSubgroupDTOS.add(packageSubgroupDTO);
        }
        return packageSubgroupDTOS;
    }

    /**
     * 属性合并
     *
     * @param itemInfoAttrGroupDTOS 属性组
     * @return 属性集合
     */
    public static List<ItemAttrDTO> transformItemAttrGroup(List<ItemInfoAttrGroupDTO> itemInfoAttrGroupDTOS) {
        if (ObjectUtils.isEmpty(itemInfoAttrGroupDTOS)) {
            return Collections.emptyList();
        }
        LinkedList<ItemAttrDTO> itemAttrDTOS = new LinkedList<>();
        for (ItemInfoAttrGroupDTO itemInfoAttrGroupDTO : itemInfoAttrGroupDTOS) {
            List<ItemInfoAttrDTO> attrList = itemInfoAttrGroupDTO.getAttrList();
            if (ObjectUtils.isEmpty(attrList)) {
                continue;
            }
            for (ItemInfoAttrDTO itemInfoAttrDTO : attrList) {
                ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
                itemAttrDTO.setGuid(itemInfoAttrDTO.getAttrGuid());
                itemAttrDTO.setAttrGuid(itemInfoAttrDTO.getAttrGuid());
                itemAttrDTO.setAttrName(itemInfoAttrDTO.getName());
                itemAttrDTO.setAttrPrice(itemInfoAttrDTO.getPrice());
                itemAttrDTO.setAttrGroupGuid(itemInfoAttrGroupDTO.getAttrGroupGuid());
                itemAttrDTO.setAttrGroupName(itemInfoAttrGroupDTO.getName());
                itemAttrDTO.setNum(1);
                itemAttrDTOS.add(itemAttrDTO);
            }
        }
        return itemAttrDTOS;
    }

    /**
     * 构建返回商品估清提示信息
     */
    private String buildItemEstimateTips(List<ItemInfoDTO> collect, List<ItemInfoEstimateVerifyDTO> verifyDTOS) {
        Map<String, ItemInfoDTO> itemInfoDTOMap = collect.stream()
                .collect(Collectors.toMap(i -> i.getSkuList().get(0).getSkuGuid(), i -> i));
        verifyDTOS.forEach(i -> {
            ItemInfoDTO itemInfoDTO = itemInfoDTOMap.get(i.getSkuGuid());
            if (!ObjectUtils.isEmpty(itemInfoDTO)) {
                delCartItem(itemInfoDTO);
            }
        });

        StringBuilder dineErrorMsg = new StringBuilder("以下商品库存不足不可下单：");
        verifyDTOS.forEach(e -> {
            String productName = e.getItemName();
            if (StringUtils.isNotBlank(e.getSkuName())) {
                productName = productName + "-" + e.getSkuName();
            }
            BigDecimal remainNum = Optional.ofNullable(e.getRemainNum()).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP);
            dineErrorMsg.append(productName).append(" (").append(BigDecimalFormatUtil.format(remainNum)).append(e.getUnit()).append(")、");
        });
        return dineErrorMsg.substring(0, dineErrorMsg.length() - 1);
    }

    private void attrFilter(List<ItemInfoDTO> itemInfoDTOS) {
        if (ObjectUtils.isEmpty(itemInfoDTOS)) {
            return;
        }
        for (ItemInfoDTO itemInfoDTO : itemInfoDTOS) {
            List<ItemInfoAttrGroupDTO> attrGroupList = itemInfoDTO.getAttrGroupList();
            if (!ObjectUtils.isEmpty(attrGroupList)) {
                for (ItemInfoAttrGroupDTO itemInfoAttrGroupDTO : attrGroupList) {
                    List<ItemInfoAttrDTO> attrList = itemInfoAttrGroupDTO.getAttrList();
                    if (!ObjectUtils.isEmpty(attrList)) {
                        attrList = attrList.stream().filter(x -> x.getUck() == 1).collect(Collectors.toList());
                        itemInfoAttrGroupDTO.setAttrList(attrList);
                    }
                }
            }
        }
    }

    private void subFilter(List<ItemInfoDTO> itemInfoDTOS) {
        if (ObjectUtils.isEmpty(itemInfoDTOS)) {
            return;
        }
        for (ItemInfoDTO itemInfoDTO : itemInfoDTOS) {
            List<ItemInfoSubgroupDTO> subgroupList = itemInfoDTO.getSubgroupList();
            if (!ObjectUtils.isEmpty(subgroupList)) {
                for (ItemInfoSubgroupDTO itemInfoSubgroupDTO : subgroupList) {
                    List<ItemInfoSubSkuDTO> subItemSkuList = getItemInfoSubSkuDTOList(itemInfoSubgroupDTO);

                    itemInfoSubgroupDTO.setSubItemSkuList(subItemSkuList);
                }
            }
        }
    }

    @NotNull
    private List<ItemInfoSubSkuDTO> getItemInfoSubSkuDTOList(ItemInfoSubgroupDTO itemInfoSubgroupDTO) {
        Integer pickNum = itemInfoSubgroupDTO.getPickNum();
        List<ItemInfoSubSkuDTO> subItemSkuList = itemInfoSubgroupDTO.getSubItemSkuList();
        subItemSkuList = subItemSkuList.stream().filter(x -> pickNum == 0 || x.getDefaultNum() > 0).collect(Collectors.toList());

        for (ItemInfoSubSkuDTO itemInfoSubSkuDTO : subItemSkuList) {
            List<ItemInfoAttrGroupDTO> attrGroupList = itemInfoSubSkuDTO.getAttrGroupList();
            if (!ObjectUtils.isEmpty(attrGroupList)) {
                for (ItemInfoAttrGroupDTO itemInfoAttrGroupDTO : attrGroupList) {
                    List<ItemInfoAttrDTO> attrList = itemInfoAttrGroupDTO.getAttrList();
                    if (!ObjectUtils.isEmpty(attrList)) {
                        attrList = attrList.stream().filter(x -> x.getUck() == 1).collect(Collectors.toList());
                        itemInfoAttrGroupDTO.setAttrList(attrList);
                    }
                }
            }
        }
        return subItemSkuList;
    }

    private void handleSpecialsActivity(List<LimitSpecialsActivityDetailsVO> specialsActivityList,
                                        List<StoreActivityRespDTO> storeActivityList) {
        List<String> itemGuidList = getItemGuidList();

        // 商品校验
        marketingActivityHelper.checkItem(itemGuidList, specialsActivityList);
        if (org.springframework.util.CollectionUtils.isEmpty(specialsActivityList)) {
            log.warn("[活动列表][商品校验]过滤为空");
            return;
        }
        StoreActivityRespDTO activityRespDTO = new StoreActivityRespDTO();
        activityRespDTO.setLabel("限");
        activityRespDTO.setName("限时特价");
        List<String> activityDescList = specialsActivityList.stream()
                .map(LimitSpecialsActivityDetailsVO::getName)
                .distinct()
                .collect(Collectors.toList());
        activityRespDTO.setActivityDescList(activityDescList);
        storeActivityList.add(activityRespDTO);
    }

    private void handleMarketActivity(ResponseMarketActivitieRule responseMarketActivitieRule,
                                      List<StoreActivityRespDTO> storeActivityList) {
        if (Objects.nonNull(responseMarketActivitieRule) &&
                CollectionUtils.isNotEmpty(responseMarketActivitieRule.getMarketActivitieResponseDTOList())) {
            List<ResponseMarketActivitieOneRule> activitieOneRuleList = responseMarketActivitieRule.getMarketActivitieResponseDTOList();
            List<String> fullDiscountStrList = new ArrayList<>();
            List<String> fullReductionStrList = new ArrayList<>();
            activitieOneRuleList.forEach(activity -> {
                if (!org.springframework.util.CollectionUtils.isEmpty(activity.getFullDiscountStrList())) {
                    fullDiscountStrList.addAll(activity.getFullDiscountStrList());
                }
                if (!org.springframework.util.CollectionUtils.isEmpty(activity.getFullReductionStrList())) {
                    fullReductionStrList.addAll(activity.getFullReductionStrList());
                }
            });
            if (!org.springframework.util.CollectionUtils.isEmpty(fullDiscountStrList)) {
                StoreActivityRespDTO activityRespDTO = new StoreActivityRespDTO();
                activityRespDTO.setLabel("折");
                activityRespDTO.setName("满折活动");
                activityRespDTO.setActivityDescList(fullDiscountStrList);
                storeActivityList.add(activityRespDTO);
            }
            if (!org.springframework.util.CollectionUtils.isEmpty(fullReductionStrList)) {
                StoreActivityRespDTO activityRespDTO = new StoreActivityRespDTO();
                activityRespDTO.setLabel("减");
                activityRespDTO.setName("满减活动");
                activityRespDTO.setActivityDescList(fullReductionStrList);
                storeActivityList.add(activityRespDTO);
            }
        }
    }


    /**
     * 计算附加费合计
     */
    private BigDecimal calculateAppendFee(List<PreOrderPersonDTO> preOrderPersonList) {
        BigDecimal appendFee = BigDecimal.ZERO;
        if (CollectionUtils.isEmpty(preOrderPersonList)) {
            return appendFee;
        }
        return preOrderPersonList.stream()
                .flatMap(e -> e.getPreOrderSurchargeList().stream())
                .map(PreOrderSurchargeDTO::getTotalAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public PreOrderRespDTO delUserPreOrder(String openid) {
        WxOrderConfigDTO storeConfig = getStoreConfig(WeixinUserThreadLocal.getStoreGuid());
        List<ShopCartItemReqDTO> cartItemList = getCartItemList(storeConfig.getOrderModel());
        if (!ObjectUtils.isEmpty(cartItemList)) {
            cartItemList.removeIf(next -> next.getOpenid().equals(openid) && delUserShopCart(next.getItemInfoDTO(), storeConfig.getOrderModel()));
            sendShopCartMessage();
        }
        return preOrderList(cartItemList, storeConfig.getOrderModel(), 0);
    }

    private List<ItemInfoEstimateVerifyDTO> itemEstimateVerify(List<ItemInfoDTO> itemInfoDTOS, List<ItemEstimateForAndroidRespDTO> estimateSkuList) {
        log.info("估清校验:{}", estimateSkuList);
        List<ItemInfoEstimateVerifyDTO> verifyDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(itemInfoDTOS) || CollectionUtils.isEmpty(estimateSkuList)) {
            return verifyDTOS;
        }
        handleEstimateVerify(itemInfoDTOS, estimateSkuList, verifyDTOS);
        return verifyDTOS;
    }

    private void handleEstimateVerify(List<ItemInfoDTO> itemInfoDTOS,
                                      List<ItemEstimateForAndroidRespDTO> estimateSkuList,
                                      List<ItemInfoEstimateVerifyDTO> verifyDTOS) {
        for (ItemEstimateForAndroidRespDTO estimate : estimateSkuList) {
            for (ItemInfoDTO itemInfoDTO : itemInfoDTOS) {
                if (ItemTypeEnum.PKG.getCode() == itemInfoDTO.getItemType()) {
                    // 套餐不再校验估清逻辑，即使此时已经估清，需要用户自己去换菜
                    return;
                }
                ItemInfoSkuDTO uckSku = PriceCalculateHelper.getUckSku(itemInfoDTO);
                if (estimate.getSkuGuid().equals(uckSku.getSkuGuid())) {
                    log.info("下单：估清匹配商品:{},规格:{}", itemInfoDTO, estimate);
                    if (estimate.getIsSoldOut() == 1 && estimate.getResidueQuantity().compareTo(itemInfoDTO.getCurrentCount()) < 0) {
                        ItemInfoEstimateVerifyDTO itemInfoEstimateVerifyDTO = new ItemInfoEstimateVerifyDTO(itemInfoDTO.getName(), uckSku.getSkuGuid()
                                , uckSku.getName(), uckSku.getUnit(), false, estimate.getResidueQuantity());
                        log.info("余量匹配:{}", itemInfoEstimateVerifyDTO);
                        verifyDTOS.add(itemInfoEstimateVerifyDTO);
                    } else if (estimate.getIsSoldOut() == 2) {
                        ItemInfoEstimateVerifyDTO itemInfoEstimateVerifyDTO = new ItemInfoEstimateVerifyDTO(itemInfoDTO.getName(), uckSku.getSkuGuid()
                                , uckSku.getName(), uckSku.getUnit(), true, null);
                        log.info("售尽匹配:{}", itemInfoEstimateVerifyDTO);
                        verifyDTOS.add(itemInfoEstimateVerifyDTO);
                    }
                }
                // 所有套餐不再校验，由用户自己去换菜
            }
        }
    }
    // 删除套餐子项估清逻辑，点此恢复

    @Override
    public SubmitShopCartRespDTO submitShopCart() {
        String storeGuid = WeixinUserThreadLocal.getStoreGuid();
        WxOrderConfigDTO storeConfig = getStoreConfig(storeGuid);
        log.info("门店配置:{}", storeConfig);
        if (storeConfig == null || storeConfig.getIsOrderOpen() != null && storeConfig.getIsOrderOpen() == 0) {
            return SubmitShopCartRespDTO.closeStore();
        }

        List<ShopCartItemReqDTO> cartItemList = getCartItemList(storeConfig.getOrderModel());
        log.info("购物车:{}", cartItemList);
        if (ObjectUtils.isEmpty(cartItemList)) {
            return SubmitShopCartRespDTO.noItem();
        }

        List<ItemInfoDTO> itemInfoDTOS = cartItemList.stream().map(ShopCartItemReqDTO::getItemInfoDTO).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(itemInfoDTOS)) {
            return SubmitShopCartRespDTO.noItem();
        }
        String s = DineInItemHelper.orderUpperLimit(itemInfoDTOS);
        if (!StringUtils.isEmpty(s)) {
            return SubmitShopCartRespDTO.estimate(s);
        }


        BaseDTO baseDTO = new BaseDTO();
        baseDTO.setStoreGuid(storeGuid);
        List<ItemEstimateForAndroidRespDTO> estimateSkuList = itemClientService.queryEstimateForSyn(baseDTO);
        addItemEstimate(storeGuid, estimateSkuList);


        List<ItemInfoEstimateVerifyDTO> verifyDTOS = itemEstimateVerify(itemInfoDTOS, estimateSkuList);
        if (!CollectionUtils.isEmpty(verifyDTOS)) {
            Map<String, ItemInfoDTO> itemInfoDTOMap = itemInfoDTOS.stream().collect(Collectors.toMap(i -> i.getSkuList().get(0).getSkuGuid(), i -> i));
            verifyDTOS.forEach(i -> {
                ItemInfoDTO itemInfoDTO = itemInfoDTOMap.get(i.getSkuGuid());
                if (itemInfoDTO == null)
                    log.info("{}-{}", JSON.toJSONString(i), JSON.toJSONString(itemInfoDTOMap));
                else
                    delCartItem(itemInfoDTO, storeGuid);
            });

            StringBuilder errorMsg = new StringBuilder("以下商品库存不足不可下单：");
            verifyDTOS.forEach(e -> {
                String productName = e.getItemName();
                if (StringUtils.isNotBlank(e.getSkuName())) {
                    productName = productName + "-" + e.getSkuName();
                }
                BigDecimal remainNum = Optional.ofNullable(e.getRemainNum()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP);
                errorMsg.append(productName).append(" (").append(BigDecimalFormatUtil.format(remainNum)).append(e.getUnit()).append(")、");
            });
            return SubmitShopCartRespDTO.estimate(errorMsg.substring(0, errorMsg.length() - 1));
        }
        sendShopCartMessage();
        return new SubmitShopCartRespDTO();
    }

    /**
     * 获取购物车
     *
     * @return 购物车项
     */
    @Override
    public List<ShopCartItemReqDTO> getCartItemList() {
        WxOrderConfigDTO storeConfig = getStoreConfig(WeixinUserThreadLocal.getStoreGuid());
        Assert.notNull(storeConfig, "无法获取当前会员的门店配置");
        return getCartItemList(storeConfig.getOrderModel());
    }

    /**
     * 获取购物车
     *
     * @return 购物车项
     */
    private List<ShopCartItemReqDTO> getCartItemList(Integer orderModel) {
        return orderModel == 0
                ? redisUtils.hValues(CacheName.SHOP_CART_ITEM + ":" + WeixinUserThreadLocal.getStoreGuid() + ":" + WeixinUserThreadLocal.getDiningTableGuid())
                : redisUtils.hValues(CacheName.SHOP_CART_ITEM + ":" + WeixinUserThreadLocal.getStoreGuid() + ":" + WeixinUserThreadLocal.getOpenId());
    }

    /**
     * 获取购物车项
     *
     * @param itemUck 项key
     * @return 项
     */
    private ShopCartItemReqDTO getCartItem(String itemUck, Integer orderModel) {
        return (ShopCartItemReqDTO) (orderModel == 0
                ? redisUtils.hGet(CacheName.SHOP_CART_ITEM + ":" + WeixinUserThreadLocal.getStoreGuid() + ":" + WeixinUserThreadLocal.getDiningTableGuid(), itemUck)
                : redisUtils.hGet(CacheName.SHOP_CART_ITEM + ":" + WeixinUserThreadLocal.getStoreGuid() + ":" + WeixinUserThreadLocal.getOpenId(), itemUck));
    }

    /**
     * 设置购物车项
     *
     * @param itemUck            项key
     * @param shopCartItemReqDTO 项
     */
    private void setCartItem(String itemUck, ShopCartItemReqDTO shopCartItemReqDTO, Integer orderModel) {
        if (orderModel == 0) {
            redisUtils.hPut15(CacheName.SHOP_CART_ITEM + ":" + WeixinUserThreadLocal.getStoreGuid() + ":" +
                    WeixinUserThreadLocal.getDiningTableGuid(), itemUck, shopCartItemReqDTO);
        } else {
            redisUtils.hPut15(CacheName.SHOP_CART_ITEM + ":" + WeixinUserThreadLocal.getStoreGuid() + ":" +
                    WeixinUserThreadLocal.getOpenId(), itemUck, shopCartItemReqDTO);
        }
    }

    private void delCartItem(ItemInfoDTO itemInfoDTO, String storeGuid) {
        log.info("移除购物车商品[{}-{}]", itemInfoDTO.getItemGuid(), itemInfoDTO.getName());
        WxOrderConfigDTO storeConfig = getStoreConfig(storeGuid);
        String uniqueKey = ItemInfoDTO.getUniqueKey(itemInfoDTO);
        delCartItem(uniqueKey, storeConfig.getOrderModel());
    }

    private void delCartItem(ItemInfoDTO itemInfoDTO) {
        log.info("移除购物车商品[{}-{}]", itemInfoDTO.getItemGuid(), itemInfoDTO.getName());
        String uniqueKey = ItemInfoDTO.getUniqueKey(itemInfoDTO);
        delCartItem(uniqueKey, TradeModeEnum.DINEIN.getCode());
    }

    private void delCartItem(String itemUck, Integer orderModel) {
        if (orderModel == 0) {
            redisUtils.hDelete(CacheName.SHOP_CART_ITEM + ":" + WeixinUserThreadLocal.getStoreGuid() + ":" + WeixinUserThreadLocal.getDiningTableGuid(), itemUck);
        } else {
            redisUtils.hDelete(CacheName.SHOP_CART_ITEM + ":" + WeixinUserThreadLocal.getStoreGuid() + ":" + WeixinUserThreadLocal.getOpenId(), itemUck);
        }
    }

    /**
     * 商品转换
     * itemBO      商品
     * itemBO      估清
     * itemBO      配置
     *
     * @param cardList 会员卡
     * @return 商品
     */
    private MenuInfoAllDTO transFormItem(AssembleMenuItemBO itemBO,
                                         boolean jumpState,
                                         List<UserMemberCardCacheDTO> cardList) {
        MenuInfoAllDTO menuInfoAllDTO = new MenuInfoAllDTO();
        ItemAndTypeForAndroidRespDTO itemForWx = itemBO.getItem();
        if (itemForWx == null) {
            menuInfoAllDTO.setSalesModel(1);
            menuInfoAllDTO.setItemInfoDTOS(Collections.emptyList());
            log.info("itemForWx传递为空");
            return menuInfoAllDTO;
        }

        // 设置售卖模式
        menuInfoAllDTO.setSalesModel(itemForWx.getSalesModel());
        menuInfoAllDTO.setPricePlanGuid(itemForWx.getPricePlanGuid());

        // 门店配置
        WxOrderConfigDTO storeConfig = itemBO.getStoreConfig();
        if (jumpState && storeConfig.getOrderModel() == 0) {
            menuInfoAllDTO.setIsJump(1);
            menuInfoAllDTO.setOrderGuid(wxClientService.initialDineOrder());
        }
        menuInfoAllDTO.setCardList(cardList);
        menuInfoAllDTO.setIsLogin(isMemberLogin());
        menuInfoAllDTO.setMenuInfoConfigDTO(transformStoreConfig(storeConfig));
        List<TypeSynRespDTO> typeList = itemForWx.getTypeList();
        menuInfoAllDTO.setItemTypeDTOS(transformType(typeList, storeConfig));

        // 商品列表
        List<ItemEstimateForAndroidRespDTO> estimateSkuList = itemBO.getEstimateSkuList();
        menuInfoAllDTO.setItemInfoDTOS(listItemInfo(itemForWx.getItemList(), estimateSkuList));

        // 限时特价活动
        MarketingActivityHelper.buildLimitSpecialsActivity(itemBO.getActivityItemVOList(), menuInfoAllDTO.getItemInfoDTOS());

        // 第N份优惠
        MarketingActivityHelper.buildNthActivity(itemBO.getNthActivityList(), menuInfoAllDTO.getItemInfoDTOS());

        // 商品满减满折活动
        MarketingActivityHelper.buildItemMarketActivity(menuInfoAllDTO.getItemInfoDTOS(), itemBO.getResponseMarketActivitieRule());

        // 活动互斥展示
        itemActivityShareHandler(menuInfoAllDTO.getItemInfoDTOS());

        return menuInfoAllDTO;
    }


    /**
     * 商品活动 互斥处理
     * 限时特价 和 满减满折的活动互斥展示
     * 第N份优惠
     */
    private void itemActivityShareHandler(List<ItemInfoDTO> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        for (ItemInfoDTO item : itemList) {
            // 限时折扣 和 满减满折之间的互斥
            fullAndLimitSpecialActivityShareActivity(item);
            // 会员折扣 和 满减满折之间的互斥
            memberDiscountAndFullActivityShareActivity(item);
            // 满减满折活动名称合并
            reMergeFullMarketActivity(item);
        }
    }


    /**
     * 判断是否存在互斥
     * 互斥做一些删除
     * 限时折扣 和 满减满折之间的互斥
     */
    private void fullAndLimitSpecialActivityShareActivity(ItemInfoDTO item) {
        // spu activity
        List<ActivityRuleDescDTO> activityRuleDescList = item.getActivityRuleDescList();
        // 限时折扣活动列表
        List<ActivityRuleDescDTO> limitSpecialsActivityList = activityRuleDescList.stream()
                .filter(e -> ActivityTypeEnum.LIMIT_SPECIALS_ACTIVITY.getCode() == e.getActivityType())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(limitSpecialsActivityList)) {
            // 没有限时折扣 默认返回共享
            return;
        }
        boolean limitSpecialsActivityShareFlag = limitSpecialsActivityList.stream()
                .anyMatch(e -> Objects.equals(BooleanEnum.TRUE.getCode(), e.getRelationRule()));
        if (!limitSpecialsActivityShareFlag) {
            // 不共享
            activityRuleDescList.removeIf(e -> ActivityTypeEnum.FULL_MINUS.getCode() == e.getActivityType()
                    || ActivityTypeEnum.FULL_DISCOUNT.getCode() == e.getActivityType()
                    || ActivityTypeEnum.NTH_ACTIVITY.getCode() == e.getActivityType());
            for (ItemInfoSkuDTO skuDTO : item.getSkuList()) {
                List<ActivityRuleDescDTO> skuActivityRuleDescList = skuDTO.getActivityRuleDescList();
                skuActivityRuleDescList.removeIf(e -> ActivityTypeEnum.FULL_MINUS.getCode() == e.getActivityType()
                        || ActivityTypeEnum.FULL_DISCOUNT.getCode() == e.getActivityType()
                        || ActivityTypeEnum.NTH_ACTIVITY.getCode() == e.getActivityType());
            }
            return;
        }

        // 第N份优惠活动列表
        MarketingActivityHelper.filterNthActivityList(activityRuleDescList, item);

        // 满减满折活动列表
        List<ActivityRuleDescDTO> marketActivityList = activityRuleDescList.stream()
                .filter(e -> ActivityTypeEnum.FULL_MINUS.getCode() == e.getActivityType()
                        || ActivityTypeEnum.FULL_DISCOUNT.getCode() == e.getActivityType())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(marketActivityList)) {
            // 没有满减满折 默认返回共享
            return;
        }
        // 删除不共享的满减满折
        activityRuleDescList.removeIf(this::fullActivityExclusion);
        for (ItemInfoSkuDTO skuDTO : item.getSkuList()) {
            List<ActivityRuleDescDTO> skuActivityRuleDescList = skuDTO.getActivityRuleDescList();
            skuActivityRuleDescList.removeIf(this::fullActivityExclusion);
        }
    }

    /**
     * 判断是否存在互斥
     * 互斥做一些删除
     * 会员折扣 和 满减满折之间的互斥
     */
    private void memberDiscountAndFullActivityShareActivity(ItemInfoDTO item) {
        // spu activity
        List<ActivityRuleDescDTO> activityRuleDescList = item.getActivityRuleDescList();
        // 满减满折活动列表
        List<ActivityRuleDescDTO> fullActivityList = activityRuleDescList.stream()
                .filter(e -> ActivityTypeEnum.FULL_MINUS.getCode() == e.getActivityType()
                        || ActivityTypeEnum.FULL_DISCOUNT.getCode() == e.getActivityType())
                .collect(Collectors.toList());
        if (Objects.equals(MinPriceTypeEnum.MEMBER_DISCOUNT.getCode(), item.getMinPriceType())
                && CollectionUtils.isNotEmpty(fullActivityList)) {
            // 删除不共享的满减满折
            activityRuleDescList.removeIf(this::fullActivityExclusion);
            for (ItemInfoSkuDTO skuDTO : item.getSkuList()) {
                List<ActivityRuleDescDTO> skuActivityRuleDescList = skuDTO.getActivityRuleDescList();
                skuActivityRuleDescList.removeIf(this::fullActivityExclusion);
            }
        }
    }


    /**
     * 满减满折活动互斥判断
     */
    private boolean fullActivityExclusion(ActivityRuleDescDTO activityRuleDescDTO) {
        return (ActivityTypeEnum.FULL_MINUS.getCode() == activityRuleDescDTO.getActivityType()
                || ActivityTypeEnum.FULL_DISCOUNT.getCode() == activityRuleDescDTO.getActivityType())
                && Objects.equals(BooleanEnum.FALSE.getCode(), activityRuleDescDTO.getRelationRule());
    }


    /**
     * 重新合并满减满折活动名称
     */
    private void reMergeFullMarketActivity(ItemInfoDTO item) {
        List<ActivityRuleDescDTO> activityRuleDescList = item.getActivityRuleDescList();
        if (CollectionUtils.isNotEmpty(activityRuleDescList)) {
            item.setActivityRuleDescList(resetActivityRuleDescList(activityRuleDescList));
        }
        for (ItemInfoSkuDTO skuDTO : item.getSkuList()) {
            skuDTO.setActivityRuleDescList(resetActivityRuleDescList(activityRuleDescList));
        }
    }


    /**
     * 重构活动列表
     */
    private List<ActivityRuleDescDTO> resetActivityRuleDescList(List<ActivityRuleDescDTO> activityRuleDescList) {
        List<ActivityRuleDescDTO> fullMinusActivityRuleDescList = activityRuleDescList.stream()
                .filter(e -> ActivityTypeEnum.FULL_MINUS.getCode() == e.getActivityType())
                .collect(Collectors.toList());
        List<ActivityRuleDescDTO> fullDiscountActivityRuleDescList = activityRuleDescList.stream()
                .filter(e -> ActivityTypeEnum.FULL_DISCOUNT.getCode() == e.getActivityType())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fullMinusActivityRuleDescList) && CollectionUtils.isEmpty(fullDiscountActivityRuleDescList)) {
            return activityRuleDescList;
        }
        List<ActivityRuleDescDTO> resetList = Lists.newArrayList();
        List<String> fullMinusActivityRuleDescStrList = fullMinusActivityRuleDescList.stream()
                .map(ActivityRuleDescDTO::getActivityRuleDesc)
                .collect(Collectors.toList());
        List<String> resetFullMinusActivityRuleDescStrList = mergeMarketActivity(fullMinusActivityRuleDescStrList);
        resetFullMinusActivityRuleDescStrList.forEach(r -> {
            ActivityRuleDescDTO descDTO = new ActivityRuleDescDTO();
            descDTO.setActivityType(ActivityTypeEnum.FULL_MINUS.getCode());
            descDTO.setActivityRuleDesc(r);
            resetList.add(descDTO);
        });
        List<String> fullDiscountActivityRuleDescStrList = fullDiscountActivityRuleDescList.stream()
                .map(ActivityRuleDescDTO::getActivityRuleDesc)
                .collect(Collectors.toList());
        List<String> resetFullDiscountActivityRuleDescStrList = mergeMarketActivity(fullDiscountActivityRuleDescStrList);
        resetFullDiscountActivityRuleDescStrList.forEach(r -> {
            ActivityRuleDescDTO descDTO = new ActivityRuleDescDTO();
            descDTO.setActivityType(ActivityTypeEnum.FULL_DISCOUNT.getCode());
            descDTO.setActivityRuleDesc(r);
            resetList.add(descDTO);
        });
        activityRuleDescList.removeIf(e -> ActivityTypeEnum.FULL_DISCOUNT.getCode() == e.getActivityType()
                || ActivityTypeEnum.FULL_MINUS.getCode() == e.getActivityType());
        activityRuleDescList.addAll(resetList);
        return activityRuleDescList;
    }


    /**
     * 合并商品满减满折活动
     */
    private List<String> mergeMarketActivity(List<String> fullStrList) {
        if (CollectionUtils.isEmpty(fullStrList)) {
            return fullStrList;
        }
        // 去重
        fullStrList = fullStrList.stream().distinct().collect(Collectors.toList());
        // 排序
        Map<BigDecimal, String> map = Maps.newHashMap();
        for (String fullStr : fullStrList) {
            String firstNumber = StringMessageUtils.getFirstNumber(fullStr);
            if (StringUtils.isNotEmpty(firstNumber)) {
                map.put(new BigDecimal(firstNumber), fullStr);
            }
        }
        List<BigDecimal> numberSortedList = map.keySet()
                .stream()
                .sorted(Comparator.comparing(BigDecimal::doubleValue))
                .collect(Collectors.toList());
        return numberSortedList.stream().map(map::get).collect(Collectors.toList());
    }

    private List<ItemInfoDTO> listItemInfo(List<ItemSynRespDTO> itemList, List<ItemEstimateForAndroidRespDTO> estimateSkuList) {
        List<ItemSynRespDTO> filterItemList = filterItemList(itemList);
        log.info("filterItemList={}", filterItemList);
        List<ItemInfoDTO> itemInfoDTOS = transformDish(filterItemList, estimateSkuList);
        log.info(ITEM_INFO_DTOS, JacksonUtils.writeValueAsString(itemInfoDTOS));
        if (ObjectUtils.isEmpty(itemInfoDTOS)) {
            log.warn("itemInfoDTOS转换为空");
        }

        itemListEstimate(itemInfoDTOS, estimateSkuList);
        itemInfoDTOS = itemEstimateFilter(itemInfoDTOS);
        if (ObjectUtils.isEmpty(itemInfoDTOS)) {
            log.warn("itemInfoDTOS估清为空");
        }
        setDefaultUck(itemInfoDTOS);
        log.info(ITEM_INFO_DTOS, JacksonUtils.writeValueAsString(itemInfoDTOS));
        return itemInfoDTOS;
    }

    private void setDefaultUck(List<ItemInfoDTO> itemInfoDTOS) {
        itemInfoDTOS.forEach(itemInfoDTO -> {
            List<ItemInfoSkuDTO> skuList = itemInfoDTO.getSkuList();
            if (!org.springframework.util.CollectionUtils.isEmpty(skuList)) {
                Optional<ItemInfoSkuDTO> first = skuList.stream().filter(x -> x.getUck() == 1).findFirst();
                if (!first.isPresent()) {
                    skuList.get(0).setUck(1);
                }
            }
        });
    }

    private boolean isMemberLogin() {
        WxMemberSessionDTO memberByOpenId = WxMemberSessionUtil.getMemberByOpenId(redisUtils, WeixinUserThreadLocal.getOpenId());
        return (memberByOpenId == null ? WeixinUserThreadLocal.getIsLogin() : memberByOpenId.getWxUserInfoDTO().getIsLogin());
    }

    /**
     * 何师售罄不显示
     * 新需求：所有小程序的估清商品不展示（之前只有何师的不展示）
     *
     * @param itemInfoDTOS
     */
    private List<ItemInfoDTO> itemEstimateFilter(List<ItemInfoDTO> itemInfoDTOS) {
        log.info("brandGuidFilter:{}", brandGuidFilter);
        String brandGuid = WeixinUserThreadLocal.getBrandGuid();
        log.info("当前需要过滤的品牌:{}", brandGuid);
        if (!CollectionUtils.isEmpty(itemInfoDTOS)) {
            for (ItemInfoDTO itemInfoDTO : itemInfoDTOS) {
                List<ItemInfoSkuDTO> skuList = itemInfoDTO.getSkuList();
                if (ObjectUtils.isEmpty(skuList) || skuList.isEmpty()) {
                    continue;
                }
                itemInfoDTO.setExistEstimateFlag(false);
                //过滤已经售罄的sku
                List<ItemInfoSkuDTO> skuDTOList = skuList.stream()
                        .filter(x -> !ObjectUtils.isEmpty(x.getIsSoldOut()) && !Objects.equals(2, x.getIsSoldOut()))
                        .collect(Collectors.toList());
                itemInfoDTO.setSkuList(skuDTOList);
                itemInfoDTO.setExistEstimateFlag(skuDTOList.size() != skuList.size());

                BigDecimal minOrderNum = returnSkuMinOrderNum(skuDTOList, itemInfoDTO.getMinOrderNum(), itemInfoDTO.getItemType());
                itemInfoDTO.setMinOrderNum(minOrderNum);
            }
            return itemInfoDTOS.stream()
                    .filter(x -> !ObjectUtils.isEmpty(x.getIsSoldOut()) && !Objects.equals(2, x.getIsSoldOut()))
                    .collect(Collectors.toList());
        }
        return itemInfoDTOS;
    }

    /**
     * 如果是多规格的商品
     * 估清后只剩下一个sku可用售卖，
     * 那么返回这个sku的minOrderNum作为Item的minOrderNum
     *
     * @param skuDTOList  sku集合
     * @param minOrderNum 商品minOrderNum的值
     * @param itemType    "商品类型:1.套餐，2.多规格，3.称重,4,单品,5,固定套餐"
     * @return sku的minOrderNum
     */
    private BigDecimal returnSkuMinOrderNum(List<ItemInfoSkuDTO> skuDTOList, BigDecimal minOrderNum, Integer itemType) {
        if (CollectionUtils.isEmpty(skuDTOList) || 2 != itemType || 1 != skuDTOList.size()) {
            return minOrderNum;
        }
        //只有多规格商品，并且估清后只剩下一个sku时才返回当前sku的minOrderNum值
        BigDecimal skuMinOrderNum = skuDTOList.stream()
                .findFirst().map(ItemInfoSkuDTO::getMinOrderNum)
                .orElse(minOrderNum);
        log.info("更新商品minOrderNum值为：" + skuMinOrderNum);
        return skuMinOrderNum;
    }

    /**
     * 类型转换
     *
     * @param typeList    类型
     * @param storeConfig 配置
     */
    private List<ItemTypeDTO> transformType(List<TypeSynRespDTO> typeList, WxOrderConfigDTO storeConfig) {
        if (ObjectUtils.isEmpty(typeList)) {
            return Collections.emptyList();
        }
        List<ItemTypeDTO> itemTypeDTOS = new ArrayList<>();
        transformTag(storeConfig, itemTypeDTOS);
        itemTypeDTOS = itemTypeDTOS.stream().sorted(Comparator.comparing(ItemTypeDTO::getSort)).collect(Collectors.toList());
        transformType(typeList, itemTypeDTOS);
        return itemTypeDTOS;
    }

    /**
     * 类型转换
     *
     * @param typeList     类型
     * @param itemTypeDTOS 商品
     */
    private void transformType(List<TypeSynRespDTO> typeList, List<ItemTypeDTO> itemTypeDTOS) {
        for (TypeSynRespDTO typeSynRespDTO : typeList) {
            itemTypeDTOS.add(
                    new ItemTypeDTO()
                            .setTypeGuid(typeSynRespDTO.getTypeGuid())
                            .setIsType(1)
                            .setDescription(typeSynRespDTO.getDescription())
                            .setIconUrl(typeSynRespDTO.getIconUrl())
                            .setName(typeSynRespDTO.getName())
                            .setSort(typeSynRespDTO.getSort())
                            .setIsMustPoint(typeSynRespDTO.getIsMustPoint()));
        }
    }

    /**
     * 标签转换
     *
     * @param storeConfig  配置
     * @param itemTypeDTOS 标签
     */
    private void transformTag(WxOrderConfigDTO storeConfig, List<ItemTypeDTO> itemTypeDTOS) {
        String[] tags = getTags(storeConfig);
        if (!ObjectUtils.isEmpty(tags)) {
            for (int i = 0; i < tags.length; i++) {
                itemTypeDTOS.add(
                        new ItemTypeDTO()
                                .setTypeGuid(tags[i])
                                .setName(tags[i])
                                .setIsType(0)
                                .setDescription("")
                                .setIconUrl("")
                                .setSort(TagEnum.getCode(tags[i]))
                );
            }
        }
    }


    /**
     * 标签获取
     *
     * @param storeConfig 配置
     * @return 标签列表
     */
    private String[] getTags(WxOrderConfigDTO storeConfig) {
        if (!ObjectUtils.isEmpty(storeConfig)) {
            String tagNames = storeConfig.getTagNames();
            if (StringUtils.isNoneBlank(tagNames)) {
                return tagNames.split(",");
            }
        }
        return new String[0];
    }

    /**
     * 商品转换
     *
     * @param itemList 商品
     */
    private List<ItemInfoDTO> transformDish(List<ItemSynRespDTO> itemList, List<ItemEstimateForAndroidRespDTO> estimateSkuList) {
        ResponseProductDiscount productDiscountRespDTO = marketingActivityHelper.queryMemberRights(WeixinUserThreadLocal.getWxtoken());

        // 微信点餐过滤
        return ObjectUtils.isEmpty(itemList)
                ? Collections.emptyList()
                : itemList.stream().map(x -> {
            ItemInfoDTO itemInfoDTO = new ItemInfoDTO();
            itemInfoDTO.setItemGuid(x.getItemGuid());
            itemInfoDTO.setParentGuid(x.getParentGuid());
            itemInfoDTO.setTypeGuid(x.getTypeGuid());
            itemInfoDTO.setTypeName(x.getTypeName());
            itemInfoDTO.setItemType(x.getItemType());
            itemInfoDTO.setName(x.getName());
            itemInfoDTO.setPinyin(x.getPinyin());
            itemInfoDTO.setNameAbbr(x.getNameAbbr());
            if (StringUtils.isNotEmpty(x.getPictureUrl())) {
                itemInfoDTO.setPictureUrl(x.getPictureUrl());
            }
            if (StringUtils.isNotEmpty(x.getBigPictureUrl())) {
                itemInfoDTO.setBigPictureUrl(x.getBigPictureUrl());
            }
            if (StringUtils.isNotEmpty(x.getDetailBigPictureUrl())) {
                itemInfoDTO.setDetailBigPictureUrl(x.getDetailBigPictureUrl());
            }
            itemInfoDTO.setSort(x.getSort());
            itemInfoDTO.setDescription(x.getDescription());
            itemInfoDTO.setEnglishBrief(x.getEnglishBrief());
            itemInfoDTO.setEnglishIngredientsDesc(x.getEnglishIngredientsDesc());
            itemInfoDTO.setTagList(transformTag(x.getTagList()));
            itemInfoDTO.setIsBestseller(x.getIsBestseller());
            itemInfoDTO.setIsNew(x.getIsNew());
            itemInfoDTO.setIsSign(x.getIsSign());
            List<ItemInfoSkuDTO> skuDTOs = transformSku(x.getSkuList(), estimateSkuList, productDiscountRespDTO.isMemberPrice());
            itemInfoDTO.setSkuList(skuDTOs);
            if (skuDTOs.stream().allMatch(i -> i.getIsSoldOut() == 2)) {  //如果所有sku都估清了那么不展示商品
                itemInfoDTO.setIsSoldOut(2);
            }
            itemInfoDTO.setAttrGroupList(transformAttrGroup(x.getAttrGroupList()));
            itemInfoDTO.setSubgroupList(transformSubgroup(x));
            itemInfoDTO.setActivityRuleDescList(Lists.newArrayList());

            //展示价格与会员规则
            showPrice(itemInfoDTO, productDiscountRespDTO);
            itemInfoDTO.setTypeList(x.getTypeList());
            return itemInfoDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 查询是否需要按人收的附加费， 前端需要弹窗选择人数
     */
    private boolean getIsExistSurcharge(Integer wxAddItemFlag, MenuInfoConfigDTO menuInfoConfigDTO) {
        List<SurchargeLinkDTO> surchargeLinkList = null;
        // 如果是加菜，则需要查询加菜前的附加费
        if (wxAddItemFlag == 1) {
            surchargeLinkList = wxStoreTradeOrderService.queryAddItemBeforeSurcharges();
        }
        if (Objects.isNull(surchargeLinkList)) {
            // 查询最新附加费
            surchargeLinkList = wxStoreTradeOrderService.querySurchargeList(menuInfoConfigDTO.getOrderModel(),
                    WeixinUserThreadLocal.getAreaGuid(), WeixinUserThreadLocal.getDiningTableGuid());
            redisUtils.setEx(String.format(RedisConstants.FAST_TABLE_SURCHARGE_KEY, WeixinUserThreadLocal.getDiningTableGuid(),
                    WeixinUserThreadLocal.getOpenId()), JacksonUtils.writeValueAsString(surchargeLinkList), 12, TimeUnit.HOURS);
        }
        surchargeLinkList = surchargeLinkList.stream()
                .filter(e -> AppendFeeTypeEnum.BY_TABLE.getCode() != e.getType())
                .collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(surchargeLinkList);
    }

    /**
     * 展示最必选的价格
     */
    private void showPrice(ItemInfoDTO itemInfoDTO, ResponseProductDiscount productDiscountRespDTO) {
        boolean isLogin = isMemberLogin();
        cheapUnit(itemInfoDTO, productDiscountRespDTO, isLogin);
        if (isLogin) {
            // 2024.06之后不用这个，为避免影响未知业务，不删除
            integralRule(itemInfoDTO, productDiscountRespDTO);
        }
        List<ItemInfoAttrGroupDTO> attrGroupList = itemInfoDTO.getAttrGroupList();
        List<ItemInfoSubgroupDTO> subgroupList = itemInfoDTO.getSubgroupList();
        BigDecimal showPrice = itemInfoDTO.getShowPrice();
        BigDecimal showMemberPrice = itemInfoDTO.getShowMemberPrice();
        BigDecimal originalPrice = showPrice.add(cheapAttr(attrGroupList)).add(cheapSubgroup(subgroupList));
        itemInfoDTO.setShowPrice(originalPrice);
        itemInfoDTO.setOriginalPrice(originalPrice);
        if (ObjectUtils.isEmpty(itemInfoDTO.getMinPrice())) {
            itemInfoDTO.setMinPrice(originalPrice);
        }
        // 会员折扣
        if (isLogin && Objects.equals(MemberRightsType.MEMBER_DISCOUNT.getCode(), productDiscountRespDTO.getMemberRightsType())) {
            BigDecimal memberDiscountPrice = itemInfoDTO.getOriginalPrice().multiply(productDiscountRespDTO.getDiscountValue())
                    .divide(BigDecimal.TEN, 2, RoundingMode.HALF_UP);
            itemInfoDTO.setMinPrice(memberDiscountPrice);
            itemInfoDTO.setMinPriceType(MinPriceTypeEnum.MEMBER_DISCOUNT.getCode());
            itemInfoDTO.setDiscountValue(productDiscountRespDTO.getDiscountValue());
            itemInfoDTO.getPreferentialTypeList().add(PreferentialTypeEnum.MEMBER_DISCOUNTS.getCode());
        }

        if (showMemberPrice != null && showMemberPrice.compareTo(BigDecimal.ZERO) > 0) {
            itemInfoDTO.setShowMemberPrice(showMemberPrice.add(cheapAttr(attrGroupList)).add(cheapSubgroup(subgroupList)));
            itemInfoDTO.setEnablePreferentialPrice(true);
        }

    }

    /**
     * @param subgroupList 套餐分组
     * @return 必选分组下所有子商品
     */
    private BigDecimal cheapSubgroup(List<ItemInfoSubgroupDTO> subgroupList) {
        return ObjectUtils.isEmpty(subgroupList)
                ? BigDecimal.ZERO
                : subAttrFee(subgroupList.stream().filter(x -> x.getPickNum() == 0).flatMap(x -> x.getSubItemSkuList().stream()).collect(Collectors.toList()));
    }

    /**
     * @param attrGroupList 属性组
     * @return 必选属性价格
     */
    private BigDecimal cheapAttr(List<ItemInfoAttrGroupDTO> attrGroupList) {
        return ObjectUtils.isEmpty(attrGroupList)
                ? BigDecimal.ZERO
                : attrGroupList.stream().flatMap(x -> x.getAttrList().stream())
                .filter(x -> x.getUck() == 1)
                .map(ItemInfoAttrDTO::getPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    /**
     * 显示最便宜的价格
     *
     * @param itemInfoDTO            商品
     * @param productDiscountRespDTO 规则
     */
    private void cheapUnit(ItemInfoDTO itemInfoDTO, ResponseProductDiscount productDiscountRespDTO, boolean isLogin) {
        List<ItemInfoSkuDTO> skuList = itemInfoDTO.getSkuList();
        ItemInfoSkuDTO itemInfoSkuDTO = Collections.min(skuList, Comparator.comparing(ItemInfoSkuDTO::getSalePrice));
        itemInfoDTO.setShowUnit(itemInfoSkuDTO.getUnit());
        itemInfoDTO.setShowPrice(itemInfoSkuDTO.getSalePrice());
        itemInfoDTO.setCurrentCount(itemInfoSkuDTO.getMinOrderNum());
        itemInfoDTO.setMinOrderNum(itemInfoSkuDTO.getMinOrderNum());
        itemInfoDTO.setShowLinePrice(itemInfoSkuDTO.getLinePrice());
        itemInfoDTO.setLinePrice(itemInfoSkuDTO.getLinePrice());
        itemInfoDTO.setMinPriceType(MinPriceTypeEnum.ORIGINAL_PRICE.getCode());

        List<ItemInfoAttrGroupDTO> attrGroupList = itemInfoDTO.getAttrGroupList();
        List<ItemInfoSubgroupDTO> subgroupList = itemInfoDTO.getSubgroupList();

        List<ItemInfoSkuDTO> collect = skuList.stream().filter(x -> {
            BigDecimal memberPrice = x.getMemberPrice();
            return memberPrice != null && memberPrice.compareTo(BigDecimal.ZERO) > 0;
        }).collect(Collectors.toList());

        if (!ObjectUtils.isEmpty(collect)) {
            ItemInfoSkuDTO min = Collections.min(collect, Comparator.comparing(ItemInfoSkuDTO::getMemberPrice));
            itemInfoDTO.setShowUnit(min.getUnit());
            itemInfoDTO.setEnablePreferentialPrice(isLogin && productDiscountRespDTO.isMemberPrice());
            itemInfoDTO.setShowMemberPrice(min.getMemberPrice());
            if (isLogin && Objects.equals(MemberRightsType.MEMBER_PRICE.getCode(), productDiscountRespDTO.getMemberRightsType())) {
                itemInfoDTO.setMinPrice(min.getMemberPrice().add(cheapAttr(attrGroupList)).add(cheapSubgroup(subgroupList)));
                itemInfoDTO.setMinPriceType(MinPriceTypeEnum.MEMBER_PRICE.getCode());
                itemInfoDTO.getPreferentialTypeList().add(PreferentialTypeEnum.MEMBER_PRICE.getCode());
            }
            itemInfoDTO.setMinOrderNum(min.getMinOrderNum());
            itemInfoDTO.setCurrentCount(min.getMinOrderNum());
        }

    }

    /**
     * 会员折扣匹配规则
     *
     * @param itemInfoDTO            商品
     * @param productDiscountRespDTO 规则
     */
    private void integralRule(ItemInfoDTO itemInfoDTO, ResponseProductDiscount productDiscountRespDTO) {
        List<ResponseDiscount> discountRespDTOList = productDiscountRespDTO.getDiscountRespDTOList();
        if (!ObjectUtils.isEmpty(discountRespDTOList)) {
            for (ResponseDiscount discountRespDTO : discountRespDTOList) {
                boolean allProducts = discountRespDTO.isAllProducts();
                List<String> productKeys = discountRespDTO.getProductKeys();
                if (allProducts || (!ObjectUtils.isEmpty(productKeys)) && productKeys.stream().anyMatch(x -> x.equals(itemInfoDTO.getItemGuid()))) {
                    String join = StringUtils.join(discountRespDTO.getProductDiscounts(), ";");
                    String showMemberRule = itemInfoDTO.getShowMemberRule();
                    if (StringUtils.isEmpty(showMemberRule)) {
                        itemInfoDTO.setShowMemberRule(join);
                    } else {
                        itemInfoDTO.setShowMemberRule(showMemberRule + ";" + join);
                    }
                }
            }

        }

    }


    /**
     * 套餐转换
     *
     * @param itemSynRespDTO 商品
     * @return 套餐
     */
    private List<ItemInfoSubgroupDTO> transformSubgroup(ItemSynRespDTO itemSynRespDTO) {
        Integer itemType = itemSynRespDTO.getItemType();
        List<SubgroupSynRespDTO> subgroupList = itemSynRespDTO.getSubgroupList();
        Integer isFixPkg = itemSynRespDTO.getIsFixPkg();
        //isFixPkg是否是固定套餐，0：否，1：是
        return 1 != itemType || ObjectUtils.isEmpty(subgroupList)
                ? Collections.emptyList()
                : subgroupList.stream().map(x -> {
            ItemInfoSubgroupDTO itemInfoSubgroupDTO = new ItemInfoSubgroupDTO();
            itemInfoSubgroupDTO.setSubgroupGuid(x.getSubgroupGuid());
            itemInfoSubgroupDTO.setName(x.getName());
            Integer pickNum = 0;
            if (1 != isFixPkg) {
                pickNum = x.getPickNum();
            }
            itemInfoSubgroupDTO.setPickNum(pickNum);
            itemInfoSubgroupDTO.setSort(x.getSort());
            itemInfoSubgroupDTO.setSubItemSkuList(transformSubItemSku(x.getSubItemSkuList()));
            return itemInfoSubgroupDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 套餐商品转换
     *
     * @param subItemSkuList 商品
     * @return 商品
     */
    private List<ItemInfoSubSkuDTO> transformSubItemSku(List<SubItemSkuSynRespDTO> subItemSkuList) {
        return ObjectUtils.isEmpty(subItemSkuList)
                ? Collections.emptyList()
                : subItemSkuList.stream().map(x -> {
            ItemInfoSubSkuDTO itemInfoSubSkuDTO = new ItemInfoSubSkuDTO();
            itemInfoSubSkuDTO.setItemNum(x.getItemNum());
            itemInfoSubSkuDTO.setItemGuid(x.getItemGuid());
            itemInfoSubSkuDTO.setItemName(x.getItemName());
            itemInfoSubSkuDTO.setCode(x.getCode());
            itemInfoSubSkuDTO.setItemType(x.getItemType());
            itemInfoSubSkuDTO.setItemTypeGuid(x.getTypeGuid());
            itemInfoSubSkuDTO.setItemTypeName(x.getTypeName());
            itemInfoSubSkuDTO.setUnit(x.getUnit());
            itemInfoSubSkuDTO.setSkuGuid(x.getSkuGuid());
            itemInfoSubSkuDTO.setSkuName(x.getSkuName());
            itemInfoSubSkuDTO.setAddPrice(x.getAddPrice());
            itemInfoSubSkuDTO.setIsRepeat(x.getIsRepeat());
            itemInfoSubSkuDTO.setDefaultNum(x.getDefaultNum());
            itemInfoSubSkuDTO.setPictureUrl(x.getPictureUrl());
            itemInfoSubSkuDTO.setIsSoldOut(1);
            itemInfoSubSkuDTO.setSort(x.getSort());
            itemInfoSubSkuDTO.setSalePrice(x.getSalePrice());
            itemInfoSubSkuDTO.setAttrGroupList(transformAttrGroup(x.getAttrGroupList()));
            return itemInfoSubSkuDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 属性转换
     *
     * @param attrGroupList 属性集合
     * @return 属性集合
     */
    private List<ItemInfoAttrGroupDTO> transformAttrGroup(List<AttrGroupSynRespDTO> attrGroupList) {
        return ObjectUtils.isEmpty(attrGroupList)
                ? Collections.emptyList()
                : attrGroupList.stream().map(x -> {
            ItemInfoAttrGroupDTO itemInfoAttrGroupDTO = new ItemInfoAttrGroupDTO();
            itemInfoAttrGroupDTO.setAttrGroupGuid(x.getAttrGroupGuid());
            itemInfoAttrGroupDTO.setName(x.getName());
            itemInfoAttrGroupDTO.setIsMultiChoice(x.getIsMultiChoice());
            itemInfoAttrGroupDTO.setIsRequired(x.getIsRequired());
            itemInfoAttrGroupDTO.setShowPrice(x.getShowPrice());
            itemInfoAttrGroupDTO.setAttrList(transformAttr(x));
            return itemInfoAttrGroupDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 属性转换
     *
     * @param attrGroupSynRespDTO 属性
     * @return 属性
     */
    private List<ItemInfoAttrDTO> transformAttr(AttrGroupSynRespDTO attrGroupSynRespDTO) {
        List<AttrSynRespDTO> attrList = attrGroupSynRespDTO.getAttrList();
        if (ObjectUtils.isEmpty(attrList)) {
            return Collections.emptyList();
        }
        List<ItemInfoAttrDTO> collect = attrList.stream().map(x -> {
            ItemInfoAttrDTO itemInfoAttrDTO = new ItemInfoAttrDTO();
            itemInfoAttrDTO.setAttrGuid(x.getAttrGuid());
            itemInfoAttrDTO.setName(x.getName());
            itemInfoAttrDTO.setPrice(x.getPrice());
            itemInfoAttrDTO.setUck(x.getIsDefault());
            itemInfoAttrDTO.setAttrGroupGuid(x.getAttrGroupGuid());
            return itemInfoAttrDTO;
        }).collect(Collectors.toList());
        if (attrGroupSynRespDTO.getIsRequired() == 1 && collect.stream().noneMatch(x -> x.getUck() == 1)) {
            collect.get(0).setUck(1);
        }
        return collect;
    }

    /**
     * 规格转换
     *
     * @param skuList 规格集合
     * @return 规格集合
     */
    private List<ItemInfoSkuDTO> transformSku(List<SkuSynRespDTO> skuList, List<ItemEstimateForAndroidRespDTO> estimateSkuList,
                                              Boolean enableMemberPrice) {
        if (ObjectUtils.isEmpty(skuList)) {
            return Collections.emptyList();
        }
        List<ItemInfoSkuDTO> collect = skuList.stream().map(x -> {
            ItemInfoSkuDTO itemInfoSkuDTO = new ItemInfoSkuDTO();
            itemInfoSkuDTO.setSkuGuid(x.getSkuGuid());
            itemInfoSkuDTO.setParentGuid(x.getParentGuid());
            itemInfoSkuDTO.setName(x.getName());
            itemInfoSkuDTO.setUnit(x.getUnit());
            itemInfoSkuDTO.setCode(x.getCode());
            itemInfoSkuDTO.setSalePrice(x.getSalePrice());
            BigDecimal memberPrice = x.getMemberPrice();
            itemInfoSkuDTO.setMemberPrice(memberPrice);
            itemInfoSkuDTO.setLinePrice(x.getLinePrice());
            itemInfoSkuDTO.setAccountingPrice(x.getAccountingPrice());
            itemInfoSkuDTO.setEnablePreferentialPrice(enableMemberPrice && memberPrice != null && memberPrice.compareTo(BigDecimal.ZERO) > 0);
            itemInfoSkuDTO.setMinOrderNum(x.getMinOrderNum());
            itemInfoSkuDTO.setIsWholeDiscount(x.getIsWholeDiscount());
            itemInfoSkuDTO.setIsMemberDiscount(x.getIsMemberDiscount());
            itemInfoSkuDTO.setActivityRuleDescList(Lists.newArrayList());
            setEstimateSku(itemInfoSkuDTO, estimateSkuList);
            return itemInfoSkuDTO;
        }).collect(Collectors.toList());
        collect.get(0).setUck(1);
        return collect;
    }

    /**
     * 商品集合估清
     *
     * @param itemInfoDTOS 商品集合
     * @param estimateDTOS 估清集合
     */
    private void itemListEstimate(List<ItemInfoDTO> itemInfoDTOS, List<ItemEstimateForAndroidRespDTO> estimateDTOS) {
        if (CollectionUtils.isEmpty(estimateDTOS) || CollectionUtils.isEmpty(itemInfoDTOS)) {
            return;
        }
        for (ItemEstimateForAndroidRespDTO estimate : estimateDTOS) {
            for (ItemInfoDTO itemInfoDTO : itemInfoDTOS) {
                switch (itemInfoDTO.getItemType()) {
                    case 1:
                    case 5:
                        subGroupEstimate(itemInfoDTO, estimate);
                        break;
                    case 2:
                        multiEstimate(itemInfoDTO, estimate);
                        break;
                    case 3:
                    case 4:
                        singleEstimate(itemInfoDTO, estimate);
                        break;
                    default:
                        break;
                }
            }
        }
    }

    /**
     * 套餐估清设置
     *
     * @param itemInfoDTO 商品
     * @param estimate    估清
     */
    private void subGroupEstimate(ItemInfoDTO itemInfoDTO, ItemEstimateForAndroidRespDTO estimate) {
        singleEstimate(itemInfoDTO, estimate);
        List<ItemInfoSubgroupDTO> subgroupList = itemInfoDTO.getSubgroupList();
        if (CollectionUtils.isEmpty(subgroupList)) {
            return;
        }
        for (ItemInfoSubgroupDTO itemInfoSubgroupDTO : subgroupList) {
            List<ItemInfoSubSkuDTO> subItemSkuList = itemInfoSubgroupDTO.getSubItemSkuList();
            if (CollectionUtils.isEmpty(subItemSkuList)) {
                continue;
            }
            if (handleItemEstimateSubgroup(itemInfoDTO, estimate, itemInfoSubgroupDTO, subItemSkuList)) return;
        }
        if (subgroupList.stream().allMatch(ItemInfoSubgroupDTO::getAllSoldOut)) {
            itemInfoDTO.setIsSoldOut(2);
            itemInfoDTO.getSkuList().get(0).setIsSoldOut(2);
        }
    }

    private boolean handleItemEstimateSubgroup(ItemInfoDTO itemInfoDTO,
                                               ItemEstimateForAndroidRespDTO estimate,
                                               ItemInfoSubgroupDTO itemInfoSubgroupDTO,
                                               List<ItemInfoSubSkuDTO> subItemSkuList) {
        for (ItemInfoSubSkuDTO itemInfoSubSkuDTO : subItemSkuList) {
            if (!estimate.getSkuGuid().equals(itemInfoSubSkuDTO.getSkuGuid())) {
                continue;
            }
            if (handleItemEstimateForSubgroup(itemInfoDTO, estimate, itemInfoSubgroupDTO, itemInfoSubSkuDTO))
                return true;
        }
        if (subItemSkuList.stream().allMatch(x -> x.getIsSoldOut() != null && x.getIsSoldOut() == 2)) {
            itemInfoSubgroupDTO.setAllSoldOut(true);
        }
        return false;
    }

    private boolean handleItemEstimateForSubgroup(ItemInfoDTO itemInfoDTO,
                                                  ItemEstimateForAndroidRespDTO estimate,
                                                  ItemInfoSubgroupDTO itemInfoSubgroupDTO,
                                                  ItemInfoSubSkuDTO itemInfoSubSkuDTO) {
        if (estimate.getIsSoldOut() == 1) {
            Integer defaultNum = itemInfoSubSkuDTO.getDefaultNum();
            BigDecimal itemNum = itemInfoSubSkuDTO.getItemNum();
            defaultNum = Optional.ofNullable(defaultNum).orElse(0);
            itemNum = Optional.ofNullable(itemNum).orElse(BigDecimal.ZERO);
            BigDecimal cartNum = itemNum.multiply(new BigDecimal(defaultNum));//99
            //固定套餐 份数99  < 剩余30
            if (cartNum.compareTo(estimate.getResidueQuantity()) > 0) {
                //当前分组中商品的可选择数量（商品可重复被选）：0：顾客不可选，其余值：最大可选商品次数
                if (itemInfoSubgroupDTO.getPickNum() == 0) {
                    //固定套餐 100%
                    itemInfoDTO.setIsSoldOut(2);
                    itemInfoDTO.getSkuList().get(0).setIsSoldOut(2);
                    return true;
                } else {
                    //可选套餐100%
                    itemInfoSubSkuDTO.setIsSoldOut(2);
                    itemInfoSubSkuDTO.setDefaultNum(0);
                }
            } else {
                itemInfoSubSkuDTO.setResidueQuantity(estimate.getResidueQuantity());
            }
        } else if (estimate.getIsSoldOut() == 2 && (itemInfoSubgroupDTO.getPickNum() != 0)) {
            // 固定套餐正常可选
            itemInfoSubSkuDTO.setIsSoldOut(2);
            itemInfoSubSkuDTO.setDefaultNum(0);
        }
        return false;
    }

    /**
     * 单规格估清设置
     *
     * @param itemInfoDTO 商品
     * @param estimate    估清
     */
    private void singleEstimate(ItemInfoDTO itemInfoDTO, ItemEstimateForAndroidRespDTO estimate) {
        ItemInfoSkuDTO uckSku = PriceCalculateHelper.getUckSku(itemInfoDTO);
        if (estimate.getSkuGuid().equals(uckSku.getSkuGuid())) {
            if (estimate.getIsSoldOut() == 1) {
                if (uckSku.getMinOrderNum().compareTo(estimate.getResidueQuantity()) <= 0) {
                    uckSku.setResidueQuantity(estimate.getResidueQuantity());
                    itemInfoDTO.setResidueQuantity(estimate.getResidueQuantity());
                } else if (uckSku.getMinOrderNum().compareTo(estimate.getResidueQuantity()) > 0) {
                    uckSku.setIsSoldOut(2);
                    itemInfoDTO.setIsSoldOut(2);
                }
            } else if (estimate.getIsSoldOut() == 2) {
                uckSku.setIsSoldOut(2);
                itemInfoDTO.setIsSoldOut(2);
            }
        }
    }

    /**
     * 多规格估清设置
     *
     * @param itemInfoDTO 商品
     * @param estimate    估清
     */
    private void multiEstimate(ItemInfoDTO itemInfoDTO, ItemEstimateForAndroidRespDTO estimate) {
        List<ItemInfoSkuDTO> skuList = itemInfoDTO.getSkuList();
        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }
        for (ItemInfoSkuDTO itemInfoSkuDTO : skuList) {
            if (!estimate.getSkuGuid().equals(itemInfoSkuDTO.getSkuGuid())) {
                continue;
            }
            if (estimate.getIsSoldOut() == 1) {
                if (itemInfoSkuDTO.getMinOrderNum().compareTo(estimate.getResidueQuantity()) > 0) {
                    itemInfoSkuDTO.setIsSoldOut(2);
                } else if (itemInfoSkuDTO.getMinOrderNum().compareTo(estimate.getResidueQuantity()) >= 0) {
                    itemInfoSkuDTO.setResidueQuantity(estimate.getResidueQuantity());
                }
            } else if (estimate.getIsSoldOut() == 2) {
                itemInfoSkuDTO.setIsSoldOut(2);
            }
        }
        if (skuList.stream().allMatch(x -> x.getIsSoldOut() == 2)) {
            itemInfoDTO.setIsSoldOut(2);
        }
        Optional<ItemInfoSkuDTO> first = skuList.stream().filter(x -> x.getIsSoldOut() == 2).findFirst();
        if (Boolean.TRUE.equals(first.isPresent())) { //如果当前商品中有估清的sku,那么商品展示为估清的最低sku价格
            Optional<ItemInfoSkuDTO> itemSku = skuList.stream()
                    .filter(x -> !ObjectUtils.isEmpty(x.getSalePrice()) && x.getIsSoldOut() != 2)
                    .min(Comparator.comparing(ItemInfoSkuDTO::getSalePrice));
            BigDecimal salePrice = itemSku.map(ItemInfoSkuDTO::getSalePrice).orElse(BigDecimal.ZERO);
            BigDecimal memberPrice = itemSku.map(ItemInfoSkuDTO::getMemberPrice).orElse(BigDecimal.ZERO);
            itemInfoDTO.setShowPrice(salePrice);
            itemInfoDTO.setShowMemberPrice(memberPrice);
        }
    }

    /**
     * 标签转换
     *
     * @param tagList 标签
     * @return 标签集合
     */
    private List<ItemInfoTagDTO> transformTag(List<TagRespDTO> tagList) {
        return ObjectUtils.isEmpty(tagList)
                ? Collections.emptyList()
                : tagList.stream().map(x -> new ItemInfoTagDTO(x.getId(), x.getId())).collect(Collectors.toList());
    }

    /**
     * 沽清剩余数量设置
     *
     * @param itemInfoSkuDTO 返回值
     */
    private void setEstimateSku(ItemInfoSkuDTO itemInfoSkuDTO, List<ItemEstimateForAndroidRespDTO> estimateSkuList) {
        if (ObjectUtils.isEmpty(estimateSkuList)) {
            return;
        }
        String skuGuid = itemInfoSkuDTO.getSkuGuid();
        Optional<ItemEstimateForAndroidRespDTO> first = estimateSkuList.stream()
                .filter(x -> x.getSkuGuid().equals(skuGuid)).findFirst();
        if (first.isPresent()) {
            ItemEstimateForAndroidRespDTO itemEstimateForAndroidRespDTO = first.get();
            itemInfoSkuDTO.setIsSoldOut(itemEstimateForAndroidRespDTO.getIsSoldOut());
            itemInfoSkuDTO.setResidueQuantity(itemEstimateForAndroidRespDTO.getResidueQuantity());
        }
    }

    /**
     * 配置转换
     *
     * @param storeConfig 配置
     * @return 配置
     */
    private MenuInfoConfigDTO transformStoreConfig(WxOrderConfigDTO storeConfig) {
        MenuInfoConfigDTO menuInfoConfigDTO = new MenuInfoConfigDTO();
        if (ObjectUtils.isEmpty(storeConfig) || ObjectUtils.isEmpty(storeConfig.getIsOrderOpen())) {
            menuInfoConfigDTO.setIsOrderOpen(0);
            menuInfoConfigDTO.setIsOpened(0);
            return menuInfoConfigDTO;
        }
        menuInfoConfigDTO.setIsOrderOpen(storeConfig.getIsOrderOpen());
        menuInfoConfigDTO.setOrderModel(storeConfig.getOrderModel());
        menuInfoConfigDTO.setIsWeighingOrdered(storeConfig.getIsWeighingOrdered());
        menuInfoConfigDTO.setMenuType(storeConfig.getMenuType());
        menuInfoConfigDTO.setIsOpened(Boolean.TRUE.equals(storeConfig.getIsOpened()) ? 1 : 0);
        menuInfoConfigDTO.setGuestFlag(Objects.equals(BooleanEnum.TRUE.getCode(), storeConfig.getGuestFlag())
                ? BooleanEnum.TRUE.getCode() : BooleanEnum.FALSE.getCode());
        menuInfoConfigDTO.setSupportGrouponTypes(storeConfig.getSupportGrouponTypes());
        menuInfoConfigDTO.setStyleColor(storeConfig.getStyleColor());
        menuInfoConfigDTO.setAutoInMemberInfoFlag(storeConfig.getAutoInMemberInfoFlag());
        return menuInfoConfigDTO;
    }

    /**
     * 校验是否开台且下单
     *
     * @param tableGuid tableGuid
     * @return bool
     */
    private boolean checkStatus(String tableGuid) {
        if (StringUtils.isEmpty(tableGuid)) {
            return false;
        }
        String orderGuid = tableClientService.getOrderGuid(tableGuid);
        if (StringUtils.isEmpty(orderGuid)) {
            return false;
        }
        SingleDataDTO singleDataDTO = new SingleDataDTO();
        singleDataDTO.setData(orderGuid);
        DineinOrderDetailRespDTO orderDetail = dineInOrderClientService.getOrderDetail(singleDataDTO);
        if (orderDetail != null && orderDetail.getState() == 1) {
            if (itemIsEmpty(orderDetail)) return true;

            Integer upperState = orderDetail.getUpperState();
            String mainOrderGuid = orderDetail.getMainOrderGuid();
            if (upperState != null && upperState == 2 && !StringUtils.isEmpty(mainOrderGuid) && mainOrderGuid.length() > 5) {
                return mainOrderIsNull(singleDataDTO, mainOrderGuid);
            }

        }
        return false;
    }

    private boolean mainOrderIsNull(SingleDataDTO singleDataDTO, String mainOrderGuid) {
        singleDataDTO.setData(mainOrderGuid);
        DineinOrderDetailRespDTO mainOrder = dineInOrderClientService.getOrderDetail(singleDataDTO);
        if (mainOrder == null) {
            return false;
        }
        return itemIsEmpty(mainOrder);
    }

    private boolean itemIsEmpty(DineinOrderDetailRespDTO orderDetail) {
        if (!CollectionUtils.isEmpty(orderDetail.getDineInItemDTOS()) || Objects.equals(4, orderDetail.getUpperState())) {
            return true;
        }
        List<DineinOrderDetailRespDTO> subFirst = orderDetail.getSubOrderDetails();
        if (!CollectionUtils.isEmpty(subFirst)) {
            for (DineinOrderDetailRespDTO subDetail : subFirst) {
                if (!CollectionUtils.isEmpty(subDetail.getDineInItemDTOS())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 属性加价计算
     *
     * @param itemInfoAttrGroupDTOS 属性集合
     * @return 价格
     */
    private BigDecimal getAttrTotalPrice(List<ItemInfoAttrGroupDTO> itemInfoAttrGroupDTOS) {
        return ObjectUtils.isEmpty(itemInfoAttrGroupDTOS)
                ? BigDecimal.ZERO
                : itemInfoAttrGroupDTOS.stream().map(x -> {
            List<ItemInfoAttrDTO> attrList = x.getAttrList();
            if (!ObjectUtils.isEmpty(attrList)) {
                return attrList.stream().filter(k -> k.getUck() == 1).map(k ->
                                Optional.ofNullable(k.getPrice()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            }
            return BigDecimal.ZERO;
        }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    /**
     * @param itemInfoDTOS 商品集合
     * @return 订单价格
     */
    private PricePairDTO orderPrice(List<ItemInfoDTO> itemInfoDTOS) {
        return ObjectUtils.isEmpty(itemInfoDTOS)
                ? new PricePairDTO(BigDecimal.ZERO, BigDecimal.ZERO)
                : new PricePairDTO(itemInfoDTOS.stream().map(x -> itemPrice(x).getOriginPrice()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO)
                , itemInfoDTOS.stream().map(x -> itemPrice(x).getMemberPrice()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
    }

    /**
     * 商品价格
     *
     * @param itemInfoDTO 商品
     * @return 价格
     */
    @Override
    public PricePairDTO itemPrice(ItemInfoDTO itemInfoDTO) {
        Integer itemType = itemInfoDTO.getItemType();
        switch (itemType) {
            case 1:
            case 5:
                return packageItemPrice(itemInfoDTO);
            case 3:
                return weightItemPrice(itemInfoDTO);
            case 2:
            case 4:
                return nonWeightItemPrice(itemInfoDTO);
            default:
        }
        return new PricePairDTO(BigDecimal.ZERO, BigDecimal.ZERO);
    }


    /**
     * 非称重商品计算
     *
     * @param itemInfoDTO 商品
     * @return 价格
     */
    private PricePairDTO nonWeightItemPrice(ItemInfoDTO itemInfoDTO) {
        ItemInfoSkuDTO uckSku = PriceCalculateHelper.getUckSku(itemInfoDTO);
        BigDecimal memberPrice = uckSku.getMemberPrice() == null ? BigDecimal.ZERO : uckSku.getMemberPrice();
        BigDecimal salePrice = uckSku.getSalePrice() == null ? BigDecimal.ZERO : uckSku.getSalePrice();

        if (memberPrice.compareTo(BigDecimal.ZERO) > 0) {
            memberPrice = itemInfoDTO.getCurrentCount().multiply(memberPrice.add(getAttrTotalPrice(itemInfoDTO.getAttrGroupList())));
        } else {
            memberPrice = BigDecimal.ZERO;
        }

        return new PricePairDTO(itemInfoDTO.getCurrentCount().multiply(salePrice.add(getAttrTotalPrice(itemInfoDTO.getAttrGroupList())))
                , memberPrice);
    }

    /**
     * 称重商品计算
     *
     * @param itemInfoDTO 商品
     * @return 价格
     */
    private PricePairDTO weightItemPrice(ItemInfoDTO itemInfoDTO) {
        ItemInfoSkuDTO uckSku = PriceCalculateHelper.getUckSku(itemInfoDTO);
        BigDecimal memberPrice = uckSku.getMemberPrice();
        BigDecimal salePrice = uckSku.getSalePrice();
        if (memberPrice != null && memberPrice.compareTo(BigDecimal.ZERO) > 0) {
            memberPrice = itemInfoDTO.getCurrentCount().multiply(memberPrice).add(getAttrTotalPrice(itemInfoDTO.getAttrGroupList()));
        } else {
            memberPrice = BigDecimal.ZERO;
        }
        return new PricePairDTO(itemInfoDTO.getCurrentCount().multiply(salePrice).add(getAttrTotalPrice(itemInfoDTO.getAttrGroupList()))
                , memberPrice);
    }

    /**
     * 套餐计算
     *
     * @param itemInfoDTO 商品
     * @return 价格
     */
    private PricePairDTO packageItemPrice(ItemInfoDTO itemInfoDTO) {
        ItemInfoSkuDTO uckSku = PriceCalculateHelper.getUckSku(itemInfoDTO);
        BigDecimal memberPrice = uckSku.getMemberPrice();
        BigDecimal salePrice = uckSku.getSalePrice();

        List<ItemInfoSubgroupDTO> subgroupList = itemInfoDTO.getSubgroupList();
        List<ItemInfoSubSkuDTO> collect = subgroupList.stream().flatMap(x -> x.getSubItemSkuList().stream()).collect(Collectors.toList());
        BigDecimal bigDecimal = subAttrFee(collect);

        if (memberPrice != null && memberPrice.compareTo(BigDecimal.ZERO) > 0) {
            memberPrice = itemInfoDTO.getCurrentCount().multiply(memberPrice.add(bigDecimal));
        } else {
            memberPrice = BigDecimal.ZERO;
        }


        return new PricePairDTO(itemInfoDTO.getCurrentCount().multiply(salePrice.add(bigDecimal))
                , memberPrice);
    }

    /**
     * 套餐子项属性加价
     *
     * @param itemInfoSubSkuDTOS 套餐子项商品
     * @return 价格
     */
    private BigDecimal subAttrFee(List<ItemInfoSubSkuDTO> itemInfoSubSkuDTOS) {
        log.info("subAttrFee skuList:{}", itemInfoSubSkuDTOS);
        return ObjectUtils.isEmpty(itemInfoSubSkuDTOS)
                ? BigDecimal.ZERO
                : itemInfoSubSkuDTOS.stream().map(x -> {
            BigDecimal itemNum = getItemNum(x);
            int defaultNum = getDefaultNum(x);
            BigDecimal attrTotalPrice = getAttrTotalPrice(getAttrTotalPrice(x.getAttrGroupList()));
            BigDecimal addPrice = getAttrTotalPrice(x.getAddPrice());
            return attrTotalPrice.multiply(new BigDecimal(defaultNum)).multiply(itemNum)
                    .add(addPrice.multiply(new BigDecimal(defaultNum)));
        }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    @NotNull
    private BigDecimal getAttrTotalPrice(BigDecimal x) {
        BigDecimal attrTotalPrice = x;
        if (null == attrTotalPrice) {
            attrTotalPrice = BigDecimal.ZERO;
        }
        return attrTotalPrice;
    }

    private int getDefaultNum(ItemInfoSubSkuDTO x) {
        int defaultNum = 0;
        if (x.getDefaultNum() != null) {
            defaultNum = x.getDefaultNum();
        }
        return defaultNum;
    }

    @NotNull
    private BigDecimal getItemNum(ItemInfoSubSkuDTO x) {
        BigDecimal itemNum = BigDecimal.ZERO;
        if (x.getItemNum() != null) {
            itemNum = x.getItemNum();
        }
        if (x.getItemType() == 3) {
            itemNum = BigDecimal.ONE;
        }
        return itemNum;
    }



    /**
     * 切换数据源
     *
     * @param userContext    用户token
     * @param enterpriseGuid 企业id
     */
    private void dynamicDatasource(UserContext userContext, String enterpriseGuid) {
        if (null == userContext) {
            userContext = new UserContext();
            userContext.setEnterpriseGuid(enterpriseGuid);
        }
        UserContextUtils.put(userContext);
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
    }


    /**
     * 删除购物车项
     *
     * @param itemInfoDTO 商品
     * @param orderModel  类型
     * @return bool
     */
    private Boolean delUserShopCart(ItemInfoDTO itemInfoDTO, Integer orderModel) {
        String uniqueKey = ItemInfoDTO.getUniqueKey(itemInfoDTO);
        return orderModel == 0
                ? redisUtils.hDelete(CacheName.SHOP_CART_ITEM + ":" + WeixinUserThreadLocal.getStoreGuid() + ":" + WeixinUserThreadLocal.getDiningTableGuid(), uniqueKey) > 0
                : redisUtils.hDelete(CacheName.SHOP_CART_ITEM + ":" + WeixinUserThreadLocal.getStoreGuid() + ":" + WeixinUserThreadLocal.getOpenId(), uniqueKey) > 0;
    }

    /**
     * 会员卡查询
     *
     * @return 会员卡列表
     */
    private List<UserMemberCardCacheDTO> getCardList() {
        WxMemberSessionDTO memberByOpenId = WxMemberSessionUtil.getMemberByOpenId(redisUtils, WeixinUserThreadLocal.getOpenId());
        boolean isLogin = memberByOpenId == null ? WeixinUserThreadLocal.getIsLogin() : memberByOpenId.getWxUserInfoDTO().getIsLogin();
        String openId = WeixinUserThreadLocal.getOpenId();
        String enterpriseGuid = WeixinUserThreadLocal.getEnterpriseGuid();
        String storeGuid = WeixinUserThreadLocal.getStoreGuid();
        if (!isLogin) {
            UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(openId);
            userMemberSession.setMemberInfoCardGuid("-1");
            return Collections.emptyList();
        }
        List<UserMemberCardCacheDTO> cardList = userMemberSessionUtils.getCardList(storeGuid, openId);
        UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(openId);
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        RequestCardOwnedPage memberCardsOwnedPageReqDTO = new RequestCardOwnedPage();
        memberCardsOwnedPageReqDTO.setEnterpriseGuid(wxMemberSessionDTO.getEnterpriseGuid());
        memberCardsOwnedPageReqDTO.setStoreGuid(wxMemberSessionDTO.getStoreGuid());
        memberCardsOwnedPageReqDTO.setBrandGuid(wxMemberSessionDTO.getBrandGuid());
        memberCardsOwnedPageReqDTO.setOpenId(openId);
        memberCardsOwnedPageReqDTO.setPage(1);
        memberCardsOwnedPageReqDTO.setPageSize(100);
        if (ObjectUtils.isEmpty(cardList)) {
            try {
                Page<ResponseMemberCardListOwned> memberCardByPage = hsaBaseClientService.getMemberCardByPage(memberCardsOwnedPageReqDTO).getData();
                log.info("getCardList查询出的会员卡:{}", JacksonUtils.writeValueAsString(memberCardByPage));
                if (CollectionUtils.isNotEmpty(memberCardByPage.getData())) {
                    ResponseProductDiscount productDiscountRespDTO = memberRightHelper.saveMemberRights(WeixinUserThreadLocal.getWxtoken());
                    log.info("getCardList会员价与匹配规则:{}", JacksonUtils.writeValueAsString(productDiscountRespDTO));
                    cardList = userMemberSessionUtils.memberCardUck(memberCardByPage.getData(), userMemberSession
                            , productDiscountRespDTO.isMemberPrice());
                } else {
                    log.info("查询会员卡失败:{}", wxMemberSessionDTO);
                    return Collections.emptyList();
                }
            } catch (Exception e) {
                log.error("查询会员卡失败,wxMemberSessionDTO:{}", wxMemberSessionDTO, e);
            }
        } else {
            //刷新会员卡列表 1分钟刷新一次
            refreshMemberCard(openId, enterpriseGuid, memberCardsOwnedPageReqDTO, storeGuid);
            //刷新会员卡列表 end
            userMemberSessionUtils.reSetMemberSessionCardInfo(cardList, userMemberSession);
            ResponseProductDiscount productDiscountRespDTO = memberRightHelper.saveMemberRights(WeixinUserThreadLocal.getWxtoken());
            userMemberSession.setIsLogin(true);
            userMemberSession.setWhetherSupportMemberPrice(productDiscountRespDTO.isMemberPrice());
            log.info("getCardList 更新会员卡信息:{}", JacksonUtils.writeValueAsString(userMemberSession));
            userMemberSessionUtils.addUserMemberSession(userMemberSession);
        }
        if (ObjectUtils.isEmpty(cardList)) {
            log.info("门店首页，会员卡为空:{}", cardList);
            return Collections.emptyList();
        }
        if (StringUtils.isEmpty(userMemberSession.getMemberInfoCardGuid()) || userMemberSession.getMemberInfoCardGuid().length() < 5) {
            return cardList;
        }
        for (UserMemberCardCacheDTO userMemberCardCacheDTO : cardList) {
            if (userMemberCardCacheDTO.getMemberInfoCardGuid().equals(userMemberSession.getMemberInfoCardGuid())) {
                userMemberCardCacheDTO.setUck(1);
                break;
            }
        }
        return cardList;
    }

    private void refreshMemberCard(String openId,
                                   String enterpriseGuid,
                                   RequestCardOwnedPage memberCardsOwnedPageReqDTO,
                                   String storeGuid) {
        UserContext userContext = UserContextUtils.get();
        menuItemExecutor.submit(() -> {
            boolean lockOk = redisUtils.setNx("addCardList:lock:" + openId, "1", 60);
            if (lockOk) {
                EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
                UserContextUtils.put(userContext);
                Page<ResponseMemberCardListOwned> memberCardByPage = hsaBaseClientService.getMemberCardByPage(memberCardsOwnedPageReqDTO).getData();
                List<UserMemberCardCacheDTO> userMemberCardCacheDTOS = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(memberCardByPage.getData())) {
                    userMemberCardCacheDTOS = MemberCardItemMAP.INSTANCE.toUserMemberCacheListNew(memberCardByPage.getData());
                }
                userMemberSessionUtils.addCardList(storeGuid, openId, userMemberCardCacheDTOS);
            }
        });
    }

    /**
     * @param storeGuid  门店id
     * @param orderState 用餐类型:0正餐，1快餐
     * @return 异步获取商品
     */
    private ItemAndTypeForAndroidRespDTO getItem(String storeGuid, Integer orderState) {
        log.info("获取商品:{}", storeGuid);
        if (StringUtils.isEmpty(storeGuid)) {
            log.info("无法获取当前会话的门店id:{}", storeGuid);
            return null;
        }
        BaseDTO baseDTO = new BaseDTO();
        baseDTO.setStoreGuid(storeGuid);
        ItemAndTypeForAndroidRespDTO item = itemClientService.getItemsForWeixin(baseDTO);
        if (ObjectUtils.isEmpty(item)) {
            log.info("获取商品失败:{}", storeGuid);
            return null;
        }
        log.info("商品数据: {}", JacksonUtils.writeValueAsString(item));
        // 过滤商品列表（同前端判断逻辑，前端不愿意修改搜索，因此后端处理）
        if (LoginSource.MINAPP.code().equals(UserContextUtils.getSource())) {
            List<ItemSynRespDTO> respDTOList = item.getItemList().stream()
                    .filter(i -> (!Objects.equals(ItemTypeEnum.PKG.getCode(), i.getItemType())
                            && !Objects.equals(ItemTypeEnum.GROUP.getCode(), i.getItemType())) ||
                            (Objects.equals(ItemTypeEnum.PKG.getCode(), i.getItemType())
                                    && Objects.equals(WxMealsTypeEnum.FAST_FOOD.getCode(), orderState))
                    )
                    .collect(Collectors.toList());
            item.setItemList(respDTOList);
        }
        return item;
    }

    private void addItemEstimate(String storeGuid, List<ItemEstimateForAndroidRespDTO> estimateSkuList) {
        redisUtils.setEx(CacheName.ESTIMATE_ITEM + ":" + storeGuid, estimateSkuList, 10, TimeUnit.MINUTES);
    }

    /**
     * @return 就餐人数
     */
    private Integer userCount() {
        return (Integer) redisUtils.get(CacheName.USER_COUNT + ":" + WeixinUserThreadLocal.getDiningTableGuid());
    }

    private BrandDTO getBrandDetail(String brandGuid) {
        BrandDTO brandDTO = (BrandDTO) redisUtils.get(BusinessName.BRAND_DETAIL + ":" + brandGuid);
        if (ObjectUtils.isEmpty(brandDTO)) {
            brandDTO = organizationClientService.queryBrandByGuid(brandGuid);
            if (ObjectUtils.isEmpty(brandDTO)) {
                throw new BusinessException("无法查询到品牌信息:" + brandGuid);
            }
            redisUtils.set(BusinessName.BRAND_DETAIL + ":" + brandGuid, brandDTO);
        }
        return brandDTO;
    }

    private StoreDTO getStoreDetail(String storeGuid) {
        StoreDTO storeDTO = null;
        try {

            storeDTO = (StoreDTO) redisUtils.get(BusinessName.STORE_DETAIL + ":" + storeGuid + "VIP");
        } catch (Exception e) {
            log.error("门店获取异常=", e);
        }
        if (storeDTO == null) {
            storeDTO = organizationClientService.queryStoreByGuid(storeGuid);
            if (storeDTO == null) {
                throw new BusinessException("无法查询到门店信息:" + storeGuid);
            }
            redisUtils.set(BusinessName.STORE_DETAIL + ":" + storeGuid + "VIP", storeDTO);
        }
        return storeDTO;
    }


    /**
     * 微信上下架
     *
     * @param itemSynRespDTOList item
     * @return item
     */
    private List<ItemSynRespDTO> filterItemList(List<ItemSynRespDTO> itemSynRespDTOList) {
        if (CollectionUtils.isEmpty(itemSynRespDTOList)) {
            return itemSynRespDTOList;
        }
        List<ItemSynRespDTO> needFilterItemList = Lists.newArrayList();
        itemSynRespDTOList.forEach(itemSynRespDTO -> handleItemSyn(itemSynRespDTO, needFilterItemList));
        itemSynRespDTOList.removeAll(needFilterItemList);
        return itemSynRespDTOList;
    }

    private void handleItemSyn(ItemSynRespDTO itemSynRespDTO, List<ItemSynRespDTO> needFilterItemList) {
        List<SkuSynRespDTO> skuList = itemSynRespDTO.getSkuList();
        if (CollectionUtils.isEmpty(skuList)) {
            needFilterItemList.add(itemSynRespDTO);
        }
        // 判断单品是否微信上架
        List<SkuSynRespDTO> newSkuList = Lists.newArrayList();
        skuList.forEach(skuSynRespDTO -> { // 遍历商品sku  取出微信上架的skuList
            Integer isJoinWeChat = skuSynRespDTO.getIsJoinWeChat();
            if (Objects.equals(1, isJoinWeChat)) {
                newSkuList.add(skuSynRespDTO);
            }

        });
        if (CollectionUtils.isNotEmpty(newSkuList)) {
            itemSynRespDTO.setSkuList(newSkuList);
        } else {
            needFilterItemList.add(itemSynRespDTO);
        }
    }

    private void handleItemSubGroup(ItemSynRespDTO itemSynRespDTO,
                                    List<ItemSynRespDTO> needFilterItemList,
                                    SubgroupSynRespDTO subgroupSynRespDTO,
                                    List<SubgroupSynRespDTO> newSubGroupList) {
        List<SubItemSkuSynRespDTO> subItemSkuList = subgroupSynRespDTO.getSubItemSkuList();
        if (!CollectionUtils.isEmpty(subItemSkuList)) {
            if (Objects.equals(0, subgroupSynRespDTO.getPickNum())) {
                //必选项，用户不可更改。当前分组下只要有一个sku未在微信上架，则这个套餐不上架
                List<SubItemSkuSynRespDTO> collect = subItemSkuList.stream()
                        .filter(subItemSkuSynRespDTO -> Objects.equals(0, subItemSkuSynRespDTO.getIsJoinWeChat()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) { // 当前套餐必选项有未在微信上架的sku， 过滤掉该套餐
                    needFilterItemList.add(itemSynRespDTO);
                } else {
                    newSubGroupList.add(subgroupSynRespDTO);
                }
            } else {
                // 【赶山牧野】 有部分套餐里面包含的单品，不想在扫码点餐上面展示，但是隐藏了之后，套餐里面也不展示相应的单品了
                // 希望能在套餐展示隐藏的单品，所以这里的逻辑去掉
                newSubGroupList.add(subgroupSynRespDTO);
            }
        }
    }

    private TableDTO getTableDetail(String tableGuid) {
        TableDTO tableDTO = null;
        try {
            tableDTO = (TableDTO) redisUtils.get(BusinessName.TABLE_DETAIL + ":" + tableGuid);
        } catch (Exception e) {
            log.error("获取缓存中的桌台信息失败！", e);
        }
        if (ObjectUtils.isEmpty(tableDTO)) {
            tableDTO = tableClientService.getTableByGuid(tableGuid);
            if (ObjectUtils.isEmpty(tableDTO)) {
                throw new BusinessException("无法查询到桌台信息:" + tableGuid);
            }
            redisUtils.set(BusinessName.TABLE_DETAIL + ":" + tableGuid, tableDTO);
        }
        return tableDTO;
    }

    private WxOrderConfigDTO getStoreConfig(String storeGuid) {
        WxOrderConfigDTO wxOrderConfigDTO = (WxOrderConfigDTO) redisUtils.get(redisUtils.keyGenerate(WX_STORE_CONFIG, storeGuid));
        if (wxOrderConfigDTO == null) {
            return wxClientService.getStoreConfig(storeGuid);
        }
        return wxOrderConfigDTO;
    }

    @Override
    public void sendShopCartMessage() {
        WxMemberSessionDTO memberSessionDTO = WeixinUserThreadLocal.get();
        UserContext userContext = UserContextUtils.get();
        try {
            menuItemExecutor.execute(() -> {
                UserContextUtils.put(userContext);
                EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
                WxUserInfoDTO wxUserInfoDTO = memberSessionDTO.getWxUserInfoDTO();
                String diningTableGuid = memberSessionDTO.getDiningTableGuid();
                String openId = wxUserInfoDTO.getOpenId();
                Set<String> openIds = websocketUtils.getOpenIdsByTableGuid(diningTableGuid);
                if (!CollectionUtils.isEmpty(openIds)) {
                    openIds.remove(openId);
                }
                if (!CollectionUtils.isEmpty(openIds)) {
                    MqttMessageReqDTO mqttMessageReqDTO = new MqttMessageReqDTO();
                    mqttMessageReqDTO.setOpenIds(new ArrayList<>(openIds));
                    mqttMessageReqDTO.setTableGuid(diningTableGuid);
                    websocketMessageClientService.sendShopcartEmqMessage(mqttMessageReqDTO);
                }
            });
        } catch (Exception e) {
            log.error("购物车", e);
        }
    }


    static String qrCodeTypeKey = "qrCodeType:";

    private Integer getQrCodeTypeByToken(String wxToken) {
        return Optional.ofNullable(redisUtils.get(qrCodeTypeKey + wxToken))
                .map(o -> Integer.valueOf(o.toString())).orElse(1);
    }

    @Override
    public void saveQrCodeTypeByToken(String wxToken, Integer qrCodeType) {
        redisUtils.setEx(qrCodeTypeKey + wxToken, qrCodeType, 1, TimeUnit.DAYS);
    }

    /**
     * 根据商品guid批量清除购物车里的指定商品
     *
     * @param request 商品guidList
     * @return boolean
     */
    @Override
    public Boolean clearItemsOnShopCart(ClearCartItemReqDTO request) {
        if (CollectionUtils.isEmpty(request.getInfoDTOList())) {
            log.warn("入参为空");
            return Boolean.FALSE;
        }
        request.getInfoDTOList().forEach(item -> delCartItem(item, WeixinUserThreadLocal.getStoreGuid()));
        return Boolean.TRUE;
    }

    /**
     * 根据商品guid查询商品详情
     *
     * @param itemSingleDTO 商品guid放在data里就行
     * @return 商品详情
     */
    @Override
    public ItemInfoDTO selectItemInfo(ItemSingleDTO itemSingleDTO) {
        if (Objects.isNull(itemSingleDTO.getData())) {
            throw new BusinessException("商品Guid不能为空");
        }
        ItemQueryListReq itemReq = new ItemQueryListReq();
        itemReq.setItemGuid(itemSingleDTO.getData());
        itemReq.setStoreGuid(WeixinUserThreadLocal.getStoreGuid());
        ItemInfoEstimateSingleDTO itemInfoEstimate = itemClientService.getItemInfoEstimate(itemReq);
        ItemInfoDTO itemInfoDTO = new ItemInfoDTO();
        if (Objects.isNull(itemInfoEstimate) || Objects.isNull(itemInfoEstimate.getItemInfo())) {
            itemInfoDTO.setIsSoldOut(2);
            log.warn("未查询到商品");
            return null;
        }
        List<ItemInfoDTO> infoDTOList = listItemInfo(Lists.newArrayList(itemInfoEstimate.getItemInfo()),
                itemInfoEstimate.getItemEstimateList());
        if (CollectionUtils.isEmpty(infoDTOList)) {
            log.warn("未查询到商品");
            itemInfoDTO.setIsSoldOut(2);
            return itemInfoDTO;
        }
        boolean isExist = false;  //判断当前查询商品是否存在或者售罄
        for (ItemInfoDTO item : infoDTOList) {
            if (Objects.equals(itemSingleDTO.getData(), item.getItemGuid())) {
                log.info("查询到商品：{}", item.getItemGuid());
                itemInfoDTO = item;
                isExist = true;
            }
        }
        if (Boolean.FALSE.equals(isExist)) {
            itemInfoDTO.setIsSoldOut(2);
        }
        if (itemSingleDTO.getH5flag() == BooleanEnum.FALSE.getCode()) {
            // 限时特价活动
            appendLimitSpecialsActivity(itemInfoDTO);
        }
        if (itemSingleDTO.getNthFlag() == BooleanEnum.TRUE.getCode()) {
            // 第N份优惠
            appendNthActivity(itemInfoDTO);
        }

        // 添加商品满减满折活动信息
        appendItemMarketActivityList(Lists.newArrayList(itemInfoDTO));

        // 活动互斥展示
        itemActivityShareHandler(Lists.newArrayList(itemInfoDTO));
        return itemInfoDTO;
    }

    private void appendNthActivity(ItemInfoDTO itemInfoDTO) {
        if (ObjectUtils.isEmpty(itemInfoDTO)) {
            return;
        }
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        List<NthActivityDetailsVO> nthActivityList = marketingActivityHelper.queryNthActivityList(wxMemberSessionDTO);
        MarketingActivityHelper.buildNthActivity(nthActivityList, Lists.newArrayList(itemInfoDTO));
    }

    private void appendLimitSpecialsActivity(ItemInfoDTO itemInfoDTO) {
        if (!ObjectUtils.isEmpty(itemInfoDTO)) {
            WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
            List<LimitSpecialsActivityDetailsVO> specialsActivityList = marketingActivityHelper.querySpecialsActivityList(wxMemberSessionDTO);
            List<LimitSpecialsActivityItemVO> activityItemVOList = queryFilterSpecialsActivity(
                    Lists.newArrayList(com.holderzone.framework.util.StringUtils.hasText(itemInfoDTO.getParentGuid()) ?
                            itemInfoDTO.getParentGuid() : itemInfoDTO.getItemGuid()), specialsActivityList);
            // 限时特价活动
            MarketingActivityHelper.buildLimitSpecialsActivity(activityItemVOList, Lists.newArrayList(itemInfoDTO));
        }
    }


    /**
     * 添加商品满减满折活动信息
     */
    private void appendItemMarketActivityList(List<ItemInfoDTO> itemInfoList) {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        if (CollectionUtils.isEmpty(itemInfoList)) {
            return;
        }
        // 判断当前门店模式
        if (Objects.isNull(wxMemberSessionDTO.getOrderState()) || TradeModeEnum.FAST.getCode() != wxMemberSessionDTO.getOrderState()) {
            return;
        }
        ResponseMarketActivitieRule responseMarketActivitieRule = queryMarketActivityList(wxMemberSessionDTO);
        // 构建商品满减满折活动
        MarketingActivityHelper.buildItemMarketActivity(itemInfoList, responseMarketActivitieRule);
    }

    private void sendDinnerMessage() {
        WxMemberSessionDTO memberSessionDTO = WeixinUserThreadLocal.get();
        try {
            menuItemExecutor.execute(() -> {
                WxUserInfoDTO wxUserInfoDTO = memberSessionDTO.getWxUserInfoDTO();
                String diningTableGuid = memberSessionDTO.getDiningTableGuid();
                String openId = wxUserInfoDTO.getOpenId();
                log.info("当前人:{}", openId);
                Set<String> openIds = websocketUtils.getOpenIdsByTableGuid(diningTableGuid);
                if (!CollectionUtils.isEmpty(openIds)) {
                    openIds.remove(openId);
                }
                if (!CollectionUtils.isEmpty(openIds)) {
                    MqttMessageReqDTO mqttMessageReqDTO = new MqttMessageReqDTO();
                    mqttMessageReqDTO.setTableGuid(diningTableGuid);
                    mqttMessageReqDTO.setOpenIds(new ArrayList<>(openIds));
                    mqttMessageReqDTO.setHeadImgUrl(wxUserInfoDTO.getHeadImgUrl());
                    mqttMessageReqDTO.setNickname(wxUserInfoDTO.getNickname());
                    websocketMessageClientService.sendDinnerEmqMessage(mqttMessageReqDTO);
                }
            });
        } catch (Exception e) {
            log.error("点餐消息发送:", e);
        }
    }


    /**
     * @param memberInfoCardGuid 会员卡id
     * @return 查询会员价支持
     */
    private Boolean hasMemberPrice(String memberInfoCardGuid) {
        log.info("查询会员价支持:{}", memberInfoCardGuid);
        Boolean hasMemberPrice = false;
        if (!StringUtils.isEmpty(memberInfoCardGuid) && memberInfoCardGuid.length() > 5) {
            hasMemberPrice = (Boolean) redisUtils.get("MemberInfoCardSupport:" + memberInfoCardGuid);
            log.info("会员价缓存:{}", hasMemberPrice);
            if (hasMemberPrice == null) {
                hasMemberPrice = hsaBaseClientService.hasMemberPrice(memberInfoCardGuid).getData();
                redisUtils.setEx("MemberInfoCardSupport:" + memberInfoCardGuid, hasMemberPrice, 30, TimeUnit.MINUTES);
            }
        }
        return hasMemberPrice;
    }


    /**
     * 查询满减满折活动
     * 用户扫码之后存入缓存
     */
    private ResponseMarketActivitieRule queryMarketActivityList(WxMemberSessionDTO wxMemberSessionDTO) {
        String redisKey = String.format(RedisKeyConstant.MEMBER_MARKET_ACTIVITY, wxMemberSessionDTO.getStoreGuid(), wxMemberSessionDTO.getWxtoken());
        ResponseMarketActivitieRule responseMarketActivitieRule = (ResponseMarketActivitieRule) redisUtils.get(redisKey);
        log.info("查询满减满折活动,门店:{},缓存:{}", wxMemberSessionDTO.getStoreGuid(), responseMarketActivitieRule);
        if (responseMarketActivitieRule == null) {
            responseMarketActivitieRule = hsaBaseClientService.getMarketActivityByType(true).getData();
            redisUtils.setEx(redisKey, responseMarketActivitieRule, 30, TimeUnit.MINUTES);
        }

        // 活动时段过滤
        if (ObjectUtils.isEmpty(responseMarketActivitieRule)) {
            return responseMarketActivitieRule;
        }
        LocalDateTime currentDateTime = LocalDateTime.now();
        List<ResponseMarketActivitieOneRule> activitieOneRuleNewList = Lists.newArrayList();
        List<ResponseMarketActivitieOneRule> activitieOneRuleList = responseMarketActivitieRule.getMarketActivitieResponseDTOList();
        if (CollUtil.isEmpty(activitieOneRuleList)) {
            return responseMarketActivitieRule;
        }
        for (ResponseMarketActivitieOneRule marketActivitie : activitieOneRuleList) {
            if (marketingActivityHelper.periodFilter(marketActivitie, currentDateTime)) {
                activitieOneRuleNewList.add(marketActivitie);
            }
        }
        responseMarketActivitieRule.setMarketActivitieResponseDTOList(activitieOneRuleNewList);
        return responseMarketActivitieRule;
    }

    /**
     * 重新放入用户上下文
     */
    private void rePutUserContext(UserMemberSessionDTO userMemberSession) {
        //header头中没有运营主体时  尝试到缓存中去取
        if (Objects.nonNull(userMemberSession) && StringUtils.isNotBlank(userMemberSession.getOperSubjectGuid())) {
            UserContext userContext = UserContextUtils.get();
            if (StringUtils.isBlank(userContext.getOperSubjectGuid())) {
                log.info("getMenuItemList缓存中的会员信息:{}", JacksonUtil.writeValueAsString(userMemberSession));
                userContext.setOperSubjectGuid(userMemberSession.getOperSubjectGuid());
                UserContextUtils.put(userContext);
            }
        }
    }

    /**
     * 计算会员优惠
     */
    @Override
    public void calculateMemberDiscount(ShopCartRespDTO shopCartRespDTO,
                                        List<ShopCartItemReqDTO> cartItemList) {
        // 查询会员权益
        ResponseProductDiscount responseProductDiscount = marketingActivityHelper.queryMemberRights(WeixinUserThreadLocal.getWxtoken());
        log.info("会员权益缓存:{}", JacksonUtils.writeValueAsString(responseProductDiscount));
        Integer memberRightsType = responseProductDiscount.getMemberRightsType();

        List<ItemInfoDTO> itemInfoDTOS = new ArrayList<>();
        boolean usedMemberRights = false;

        // 购物车排序
        cartItemList.sort(Comparator.comparing(ShopCartItemReqDTO::getGmtCreate).reversed());
        for (ShopCartItemReqDTO shopCartItem : cartItemList) {
            ItemInfoDTO itemInfoDTO = shopCartItem.getItemInfoDTO();

            // 商品价格计算
            PricePairDTO pricePairDTO = itemPrice(itemInfoDTO);
            BigDecimal originalPrice = pricePairDTO.getOriginPrice().setScale(2, RoundingMode.HALF_UP);
            itemInfoDTO.setOriginalPrice(originalPrice);
            itemInfoDTO.setMinPrice(originalPrice);
            itemInfoDTO.setMinPriceType(MinPriceTypeEnum.ORIGINAL_PRICE.getCode());

            // 团购验券商品
            MtCouponPreRespDTO couponPreRespDTO = itemInfoDTO.getCouponPreRespDTO();
            if (Objects.nonNull(couponPreRespDTO)) {
                itemInfoDTO.setMinPrice(BigDecimal.ZERO);
                itemInfoDTO.setMemberPrice(BigDecimal.ZERO);
                itemInfoDTO.setMinPriceType(MinPriceTypeEnum.GROUPON_PRICE.getCode());
                itemInfoDTOS.add(itemInfoDTO);
                continue;
            }
            MemberRightsType rightsType = MemberRightsType.getEnum(memberRightsType);
            switch (Objects.requireNonNull(rightsType)) {
                case NOT:
                    break;
                case MEMBER_PRICE:
                    BigDecimal memberPrice = pricePairDTO.getMemberPrice().setScale(2, RoundingMode.HALF_UP);
                    if (BigDecimalUtil.greaterThanZero(memberPrice)) {
                        itemInfoDTO.setMinPrice(memberPrice);
                        itemInfoDTO.setMemberPrice(memberPrice);
                        itemInfoDTO.setMinPriceType(MinPriceTypeEnum.MEMBER_PRICE.getCode());
                        usedMemberRights = true;
                    }
                    break;
                case MEMBER_DISCOUNT:
                    itemInfoDTO.setMinPrice(originalPrice.multiply(responseProductDiscount.getDiscountValue())
                            .divide(BigDecimal.TEN, 2, RoundingMode.HALF_UP));
                    itemInfoDTO.setMemberPrice(itemInfoDTO.getMinPrice());
                    itemInfoDTO.setMinPriceType(MinPriceTypeEnum.MEMBER_DISCOUNT.getCode());
                    usedMemberRights = true;
                    break;
                default:
                    log.warn("未知权益类型");
                    break;
            }
            itemInfoDTOS.add(itemInfoDTO);
        }
        DineInItemHelper.shopCartSetData(shopCartRespDTO, itemInfoDTOS, usedMemberRights);
    }

}
