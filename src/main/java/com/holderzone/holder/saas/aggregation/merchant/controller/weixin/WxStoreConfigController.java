package com.holderzone.holder.saas.aggregation.merchant.controller.weixin;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.StoreParserService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin.WxOrderConfigClientService;
import com.holderzone.saas.store.dto.weixin.req.WxOrderConfigUpdateBatchReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreConfigController
 * @date 2019/02/14 13:57
 * @description 微信端门店点餐配置Controller
 * @program holder-saas-store-weixin
 */
@RestController
@RequestMapping("/wx_store_order_config")
@Api(description = "微信门店点餐配置接口")
@Slf4j
public class WxStoreConfigController {

    @Autowired
    StoreParserService storeParserService;

    @Autowired
    WxOrderConfigClientService wxOrderConfigClientService;

    @PostMapping("/list_order_config")
    @ApiOperation(value = "获取微信点餐门店配置列表")
    @ApiImplicitParam(name = "微信点餐门店配置列表请求参数", value = "wxStorePageReqDTO")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "获取微信点餐门店配置列表")
    public Result<Page<WxOrderConfigDTO>> getOrderConfigList(@RequestBody WxStorePageReqDTO wxStorePageReqDTO) {
        log.info("merchant聚合层：获取到微信门店配置信息列表查询参数：{}", JacksonUtils.writeValueAsString(wxStorePageReqDTO));
        return Result.buildSuccessResult(wxOrderConfigClientService.pageOrderConfig(wxStorePageReqDTO));
    }

    @PostMapping("/get_detail_config")
    @ApiOperation(value = "获取微信点餐门店配置详细信息")
    @ApiImplicitParam(name = "微信点餐门店配置详细信息请求参数", value = "wxStoreReqDTO")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "获取微信点餐门店配置详细信息")
    public Result<WxOrderConfigDTO> getDetailConfig(@RequestBody WxStoreReqDTO wxStoreReqDTO) {
        log.info("merchant聚合层：获取到微信门店配置详细信息查询参数：{}", JacksonUtils.writeValueAsString(wxStoreReqDTO));
        return Result.buildSuccessResult(wxOrderConfigClientService.getDetailConfig(wxStoreReqDTO));
    }

    @PostMapping("/update_store_config")
    @ApiOperation(value = "修改微信点餐门店配置")
    @ApiImplicitParam(name = "微信门店配置修改参数", value = "wxStoreUpdateReqDTO")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "修改微信点餐门店配置")
    public Result<String> updateStoreConfig(@RequestBody WxOrderConfigDTO wxOrderConfigDTO) {
        log.info("merchant聚合层：获取到微信门店配置修改参数：{}", JacksonUtils.writeValueAsString(wxOrderConfigDTO));
        Boolean flag = wxOrderConfigClientService.updateStoreConfig(wxOrderConfigDTO);
        return flag ? Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.WE_CHAT_STORE_CONFIGURATION_MODIFIED_SUCCESSFULLY))
                : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.WE_CHAT_STORE_CONFIGURATION_MODIFIED_FAILED));
    }

    @PostMapping("/update_batch_store_config")
    @ApiImplicitParam(name = "微信门店配置批量修改入参", value = "wxOrderConfigUpdateBatchReqDTO")
    @ApiOperation(value = "批量修改门店配置")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "微信门店配置批量修改入参")
    public Result<String> updateStoreConfigBatch(@RequestBody WxOrderConfigUpdateBatchReqDTO wxOrderConfigUpdateBatchReqDTO) {
        log.info("merchant聚合层：获取到微信门店配置批量修改参数：{}", JacksonUtils.writeValueAsString(wxOrderConfigUpdateBatchReqDTO));
        return wxOrderConfigClientService.updateStoreConfigBatch(wxOrderConfigUpdateBatchReqDTO)
                ? Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.WE_CHAT_STORE_CONFIGURATION_BATCH_MODIFIED_SUCCESSFULLY)) : Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.WE_CHAT_STORE_CONFIGURATION_BATCH_MODIFIED_FAILED));
    }


}
