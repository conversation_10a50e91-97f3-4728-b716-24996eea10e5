package com.holderzone.holder.saas.aggregation.weixin.service.impl;

import com.beust.jcommander.internal.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.context.DiscountContext;
import com.holderzone.holder.saas.aggregation.weixin.entity.bo.DiscountRuleBO;
import com.holderzone.holder.saas.aggregation.weixin.helper.MarketingActivityHelper;
import com.holderzone.holder.saas.aggregation.weixin.helper.MemberRightHelper;
import com.holderzone.holder.saas.aggregation.weixin.service.CalculateService;
import com.holderzone.holder.saas.aggregation.weixin.service.MemberService;
import com.holderzone.holder.saas.aggregation.weixin.service.MenuItemService;
import com.holderzone.holder.saas.aggregation.weixin.service.WxStoreTradeOrderService;
import com.holderzone.holder.saas.aggregation.weixin.service.chain.DiscountChain;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.HsmTerminalServiceClient;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.ItemClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.TradeClientService;
import com.holderzone.holder.saas.aggregation.weixin.utils.UserMemberSessionUtils;
import com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList;
import com.holderzone.holder.saas.member.terminal.enums.VolumeTypeEnum;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseClientMarketActivity;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseCardRights;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseProductDiscount;
import com.holderzone.holder.saas.member.wechat.dto.enums.MemberRightsType;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.utils.BigDecimalUtil;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.constant.RedisKeyConstant;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.marketing.nth.NthActivityDetailsVO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityDetailsVO;
import com.holderzone.saas.store.dto.order.OrderDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.weixin.PricePairDTO;
import com.holderzone.saas.store.dto.weixin.deal.*;
import com.holderzone.saas.store.dto.weixin.resp.CalculateOrderRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.MarketingActivityInfoRespDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.member.VolumeVerifyEnum;
import com.holderzone.saas.store.enums.order.AppendFeeTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.weixin.MinPriceTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CalculateServiceImpl implements CalculateService {

    private final UserMemberSessionUtils userMemberSessionUtils;

    private final WxStoreTradeOrderService wxStoreTradeOrderService;

    private final MenuItemService menuItemService;

    private final HsmTerminalServiceClient terminalServiceClient;

    private final DiscountChain discountChain;

    private final MemberService memberService;

    private final MarketingActivityHelper marketingActivityHelper;

    private final HsaBaseClientService wechatClientService;

    private final TradeClientService tradeClientService;

    private final RedisUtils redisUtils;

    private final ItemClientService itemClientService;

    private final MemberRightHelper memberRightHelper;

    @Override
    public CalculateOrderRespDTO calculate(CalculateOrderDTO calculateDTO) {
        // 默认值
        initCalculateDTO(calculateDTO);
        // 金额计算
        return calculateAmountChain(calculateDTO);
    }

    /**
     * 设置请求的默认值
     */
    private void initCalculateDTO(CalculateOrderDTO calculateDTO) {
        // 默认值
        if (Objects.isNull(calculateDTO.getUseMemberDiscountFlag())) {
            calculateDTO.setUseMemberDiscountFlag(true);
        }
        if (Objects.isNull(calculateDTO.getMemberIntegralStore())) {
            calculateDTO.setMemberIntegralStore(false);
        }

        List<String> itemGuidList = calculateDTO.getDineInItemList().stream()
                .map(DineInItemDTO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        ItemStringListDTO listDTO = new ItemStringListDTO();
        listDTO.setDataList(itemGuidList);
        List<ItemInfoRespDTO> itemInfoRespDTOList = itemClientService.selectItems(listDTO);
        Map<String, ItemInfoRespDTO> itemInfoRespDTOMap = itemInfoRespDTOList.stream()
                .collect(Collectors.toMap(ItemInfoRespDTO::getItemGuid, a -> a, (k1, k2) -> k1));

        // 默认orderItemGuid
        for (int i = 0; i < calculateDTO.getDineInItemList().size(); i++) {
            DineInItemDTO dineInItemDTO = calculateDTO.getDineInItemList().get(i);
            dineInItemDTO.setGuid(String.valueOf(i + 1));
            dineInItemDTO.setMinPrice(BigDecimalUtil.setScale2(dineInItemDTO.getOriginalPrice()));
            dineInItemDTO.setMinPriceType(MinPriceTypeEnum.ORIGINAL_PRICE.getCode());
            dineInItemDTO.setTotalPrice(dineInItemDTO.getOriginalPrice());
            dineInItemDTO.setOriginalPrice(dineInItemDTO.getOriginalPrice()
                    .divide(dineInItemDTO.getCurrentCount(), BigDecimal.ROUND_HALF_UP, RoundingMode.HALF_UP));
            if (!ObjectUtils.isEmpty(dineInItemDTO.getMemberPrice())) {
                dineInItemDTO.setMemberPrice(dineInItemDTO.getMemberPrice()
                        .divide(dineInItemDTO.getCurrentCount(), BigDecimal.ROUND_HALF_UP, RoundingMode.HALF_UP));
            }
            ItemInfoRespDTO itemInfoRespDTO = itemInfoRespDTOMap.get(dineInItemDTO.getItemGuid());
            if (!ObjectUtils.isEmpty(itemInfoRespDTO)) {
                dineInItemDTO.setParentGuid(itemInfoRespDTO.getParentGuid());
            }
        }
    }

    /**
     * 计算订单金额 (未下单)
     */
    private CalculateOrderRespDTO calculateAmountChain(CalculateOrderDTO calculateDTO) {
        CalculateOrderRespDTO calculateOrderRespDTO = new CalculateOrderRespDTO();
        // 查询会员信息
        UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
        log.info("查询当前会员信息,userMemberSession:{}", JacksonUtils.writeValueAsString(userMemberSession));
        // ============================== 优惠计算开始 ==============================
        DiscountRuleBO discountRuleBO = getDiscountRuleBO(calculateDTO, userMemberSession);
        log.info("优惠计算bo：{}", JacksonUtils.writeValueAsString(discountRuleBO));
        // 计算附加费
        calculateAppendFee(calculateOrderRespDTO);
        // 计算订单总金额
        calculateOrderRespDTO.setOrderFee(getOrderFee(calculateDTO.getDineInItemList(), calculateOrderRespDTO.getAppendFee()));
        // init discountContext
        DiscountContext discountContext = DiscountContext.init(calculateDTO, calculateOrderRespDTO, discountRuleBO, userMemberSession);
        log.info("优惠计算规则：{}", JacksonUtils.writeValueAsString(discountContext));
        discountChain.doDiscount(discountContext);
        // ============================== 优惠计算结束 ==============================
        // 计算相关金额返回给前端
        calculateFinalAmount(calculateOrderRespDTO, discountContext);
        // 计算规则返回给前端
        appendDiscountRuleBO(calculateOrderRespDTO, calculateDTO, discountRuleBO);
        // 优惠信息处理
        orderMemberInfoHandler(discountContext);
        return calculateOrderRespDTO;
    }

    /**
     * 计算订单附加费
     */
    private void calculateAppendFee(CalculateOrderRespDTO calculateOrderRespDTO) {
        calculateOrderRespDTO.setAppendFeeDetailDTOS(Lists.newArrayList());
        calculateOrderRespDTO.setAppendFee(BigDecimal.ZERO);
        List<SurchargeLinkDTO> surchargeLinkList = wxStoreTradeOrderService.querySurchargeListByRedis(WeixinUserThreadLocal.getDiningTableGuid());
        if (CollectionUtils.isEmpty(surchargeLinkList)) {
            return;
        }
        log.info("桌台附加费明细:{}", JacksonUtils.writeValueAsString(surchargeLinkList));
        calculateOrderRespDTO.setAppendFeeDetailDTOS(surchargeLinkList);
        // 获取人数
        Integer guestsCount = menuItemService.queryGuestCount();
        // 计算订单附加费
        for (SurchargeLinkDTO e : surchargeLinkList) {
            int count = 1;
            if (Objects.equals(AppendFeeTypeEnum.BY_NUM.getCode(), e.getType())) {
                count = guestsCount;
            }
            calculateOrderRespDTO.setAppendFee(calculateOrderRespDTO.getAppendFee().add(e.getAmount().multiply(new BigDecimal(count))));
        }
    }

    /**
     * 获取订单总金额
     */
    private BigDecimal getOrderFee(List<DineInItemDTO> dineInItemList, BigDecimal appendFee) {
        BigDecimal orderFee = BigDecimal.ZERO;
        for (DineInItemDTO dineInItemDTO : dineInItemList) {
            if (BigDecimalUtil.greaterThanZero(dineInItemDTO.getCurrentCount())) {
                orderFee = orderFee.add(dineInItemDTO.getItemPrice());
            }
        }
        return orderFee.add(appendFee);
    }

    /**
     * 处理金额为负数的情况
     */
    private void handleZero(CalculateOrderRespDTO calculateOrderRespDTO) {
        if (BigDecimalUtil.lessThanZero(calculateOrderRespDTO.getOrderSurplusFee())) {
            calculateOrderRespDTO.setOrderSurplusFee(BigDecimal.ZERO);
        }
        if (BigDecimalUtil.lessThanZero(calculateOrderRespDTO.getActuallyPayFee())) {
            calculateOrderRespDTO.setActuallyPayFee(BigDecimal.ZERO);
        }
    }


    /**
     * 构建优惠BO
     */
    private DiscountRuleBO getDiscountRuleBO(CalculateOrderDTO calculateDTO, UserMemberSessionDTO userMemberSession) {
        DiscountRuleBO discountRuleBO = new DiscountRuleBO();
        // 设置优惠券信息
        setVolumeInfo(discountRuleBO, calculateDTO);
        // 可能存在userMemberSession中没有memberCardGuid的情况，此时需要查询会员卡信息
        // 设置会员卡guid
        setMemberInfoCardGuid(calculateDTO, userMemberSession);
        // 设置会员权益
        setMemberCardRights(discountRuleBO, userMemberSession);
        // 设置限时特价活动信息
        setSpecialsActivity(calculateDTO, discountRuleBO);
        // 设置第N份优惠活动计算活动信息
        setNthActivity(calculateDTO, discountRuleBO);
        // 设置满减满折活动信息
        setFullActivity(calculateDTO, discountRuleBO);
        return discountRuleBO;
    }

    /**
     * 设置第N份优惠活动计算活动信息
     */
    private void setNthActivity(CalculateOrderDTO calculateDTO, DiscountRuleBO discountRuleBO) {
        if (org.springframework.util.CollectionUtils.isEmpty(calculateDTO.getActivitySelectList())) {
            return;
        }
        NthActivityDetailsVO nthActivityDetailsVO =
                marketingActivityHelper.querySelectNthActivityDetailsVO(calculateDTO.getActivitySelectList());
        discountRuleBO.setNthActivityDetailsVO(nthActivityDetailsVO);
    }

    /**
     * 设置满减满折活动信息
     */
    private void setFullActivity(CalculateOrderDTO calculateDTO, DiscountRuleBO discountRuleBO) {
        // 如果勾选了营销活动，则需要查询营销活动是否互斥
        discountRuleBO.setFullShareFlag(true);
        if (CollectionUtils.isNotEmpty(calculateDTO.getActivitySelectList())) {
            String selectActivityGuid = calculateDTO.getActivitySelectList().stream()
                    .filter(s -> Objects.equals(DiscountTypeEnum.ACTIVITY.getCode(), s.getActivityType()))
                    .map(ActivitySelectDTO::getActivityGuid)
                    .findFirst()
                    .orElse(null);
            if (StringUtils.isNotEmpty(selectActivityGuid)) {
                ResponseClientMarketActivity marketActivity = wechatClientService.getActivityInfo(selectActivityGuid).getData();
                if (Objects.nonNull(marketActivity)) {
                    discountRuleBO.setFullShareFlag(Objects.equals(marketActivity.getIsShare(), BooleanEnum.FALSE.getCode()));
                }
            }
        }
    }

    /**
     * 设置限时特价活动信息
     */
    private void setSpecialsActivity(CalculateOrderDTO calculateDTO, DiscountRuleBO discountRuleBO) {
        // 【针对非首次进入】查询如果选了限时折扣，查询限时折扣是否共享
        if (!org.springframework.util.CollectionUtils.isEmpty(calculateDTO.getActivitySelectList())) {
            LimitSpecialsActivityDetailsVO activityDetailsVO =
                    marketingActivityHelper.querySelectSpecialsActivityDetailsVO(calculateDTO.getActivitySelectList());
            discountRuleBO.setSpecialsActivityDetailsVO(activityDetailsVO);
        }
    }


    /**
     * 设置优惠券信息
     */
    private void setVolumeInfo(DiscountRuleBO discountRuleBO, CalculateOrderDTO calculateDTO) {
        discountRuleBO.setVolumeCodeType(Constant.VOLUME_CODE_TYPE);
        discountRuleBO.setVolumeShareFlag(true);
        discountRuleBO.setVerify(VolumeVerifyEnum.QUERY.getCode());
        if (!ObjectUtils.isEmpty(calculateDTO.getVerify())) {
            discountRuleBO.setVerify(calculateDTO.getVerify());
        }
        if (CollectionUtils.isEmpty(calculateDTO.getVolumeCodes())) {
            return;
        }
        // 查询券类型
        ResponseVolumeList volumeInfo = terminalServiceClient.getVolumeByCode(calculateDTO.getVolumeCodes().get(0));
        if (Objects.isNull(volumeInfo)) {
            return;
        }
        discountRuleBO.setVolumeCodeType(volumeInfo.getVolumeType());
        discountRuleBO.setVolumeGuid(volumeInfo.getVolumeInfoGuid());
        if (Objects.isNull(volumeInfo.getIsUseAlone())) {
            return;
        }
        discountRuleBO.setVolumeShareFlag(volumeInfo.getIsUseAlone() == 0);
    }


    /**
     * 设置会员卡guid
     */
    private void setMemberInfoCardGuid(CalculateOrderDTO calculateDTO, UserMemberSessionDTO userMemberSession) {
        String cacheMemberInfoCardGuid = userMemberSession.getMemberInfoCardGuid();
        // 可能存在userMemberSession中没有memberCardGuid的情况，此时需要查询会员卡信息
        if (StringUtils.isEmpty(cacheMemberInfoCardGuid) || "-1".equals(cacheMemberInfoCardGuid)) {
            WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
            List<UserMemberCardCacheDTO> cardList = userMemberSessionUtils.getCardList(wxMemberSessionDTO.getStoreGuid(),
                    wxMemberSessionDTO.getWxUserInfoDTO().getOpenId());
            if (CollectionUtils.isNotEmpty(cardList)) {
                log.info("会员卡列表:{}", JacksonUtils.writeValueAsString(cardList));
                UserMemberCardCacheDTO mainCard = cardList.stream().filter(e -> e.getCardType() == 0).findFirst().orElse(null);
                if (Objects.nonNull(mainCard)) {
                    userMemberSession = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
                    userMemberSession.setMemberInfoCardGuid(mainCard.getMemberInfoCardGuid());
                    cacheMemberInfoCardGuid = mainCard.getMemberInfoCardGuid();
                    userMemberSessionUtils.addUserMemberSession(userMemberSession);
                }
            }
        }
        if (StringUtils.isNotEmpty(calculateDTO.getMemberInfoCardGuid())
                && !calculateDTO.getMemberInfoCardGuid().equals(cacheMemberInfoCardGuid)) {
            log.info("切换卡选择, 当前卡:{}, 原卡:{}", calculateDTO.getMemberInfoCardGuid(), cacheMemberInfoCardGuid);
            // 会员指定会员卡
            userMemberSession.setMemberInfoCardGuid(calculateDTO.getMemberInfoCardGuid());
            userMemberSessionUtils.addUserMemberSession(userMemberSession);
            // 删除会员权益缓存
            memberRightHelper.removeMemberRights(WeixinUserThreadLocal.getWxtoken());
            // 重新设置商品相关价格
            resetDineInItemList(calculateDTO.getDineInItemList());
        }
    }

    /**
     * 重新设置商品相关价格
     */
    private void resetDineInItemList(List<DineInItemDTO> dineInItemList) {
        List<ShopCartItemReqDTO> cartItemList = menuItemService.getCartItemList();
        ShopCartRespDTO shopCartRespDTO = new ShopCartRespDTO();
        // 重新计算商品价格
        menuItemService.calculateMemberDiscount(shopCartRespDTO, cartItemList);
        // 设置商品价格
        Map<String, DineInItemDTO> dineInItemSkuMap = dineInItemList.stream()
                .collect(Collectors.toMap(DineInItemDTO::getSkuGuid, Function.identity(), (key1, key2) -> key1));
        List<ItemInfoDTO> cartItemInfoList = cartItemList.stream().map(ShopCartItemReqDTO::getItemInfoDTO).collect(Collectors.toList());
        for (ItemInfoDTO itemInfoDTO : cartItemInfoList) {
            DineInItemDTO dineInItemDTO = dineInItemSkuMap.get(itemInfoDTO.getSkuList().get(0).getSkuGuid());
            if (Objects.isNull(dineInItemDTO)) {
                continue;
            }
            PricePairDTO pricePairDTO = menuItemService.itemPrice(itemInfoDTO);
            dineInItemDTO.setMemberPrice(pricePairDTO.getMemberPrice()
                    .divide(dineInItemDTO.getCurrentCount(), BigDecimal.ROUND_HALF_UP, RoundingMode.HALF_UP));
            if (dineInItemDTO.getMemberPrice().compareTo(BigDecimal.ZERO) <= 0) {
                dineInItemDTO.setMemberPrice(null);
            }
        }
    }

    /**
     * 设置会员卡guid
     */
    private void setMemberCardRights(DiscountRuleBO discountRuleBO, UserMemberSessionDTO userMemberSession) {
        discountRuleBO.setHasMemberPrice(false);
        discountRuleBO.setHasMemberDiscount(false);
        if (StringUtils.isEmpty(userMemberSession.getMemberInfoCardGuid())) {
            return;
        }
        // 查询会员权益
        ResponseProductDiscount responseProductDiscount = memberRightHelper.queryMemberRights(WeixinUserThreadLocal.getWxtoken());
        log.info("查询会员权益:{}", JacksonUtils.writeValueAsString(responseProductDiscount));
        // 是否有会员价
        discountRuleBO.setHasMemberPrice(Objects.nonNull(responseProductDiscount)
                && MemberRightsType.MEMBER_PRICE.getCode() == responseProductDiscount.getMemberRightsType());
        // 是否有会员折扣
        boolean hasMemberDiscount = Objects.nonNull(responseProductDiscount)
                && MemberRightsType.MEMBER_DISCOUNT.getCode() == responseProductDiscount.getMemberRightsType();
        discountRuleBO.setHasMemberDiscount(hasMemberDiscount);
        if (hasMemberDiscount) {
            discountRuleBO.setMemberDiscount(responseProductDiscount.getDiscountValue());
        }
        discountRuleBO.setResponseProductDiscount(responseProductDiscount);
    }

    /**
     * 计算规则返回给前端
     */
    private void appendDiscountRuleBO(CalculateOrderRespDTO calculateOrderRespDTO, CalculateOrderDTO calculateDTO,
                                      DiscountRuleBO discountRuleBO) {
        calculateOrderRespDTO.setHasMemberPrice(discountRuleBO.getHasMemberPrice());
        calculateOrderRespDTO.setHasMemberDiscount(discountRuleBO.getHasMemberDiscount());
        calculateOrderRespDTO.setMemberDiscount(discountRuleBO.getMemberDiscount());
        // 如果最终会员优惠金额 = 0 则不使用会员优惠
        calculateOrderRespDTO.setUseMemberDiscountFlag(false);
        // 是否不可使用会员优惠
        calculateOrderRespDTO.setUnAbleMemberDiscountFlag(isMemberDiscountShare(calculateOrderRespDTO, discountRuleBO));
        if (Boolean.TRUE.equals(calculateOrderRespDTO.getUnAbleMemberDiscountFlag())) {
            calculateOrderRespDTO.setUnAbleMemberDiscountReason(Constant.OTHER_DISCOUNT_SHARE);
        }
        List<DiscountFeeDetailDTO> memberDiscountList = calculateOrderRespDTO.getDiscountFeeDetailDTOS().stream()
                .filter(e -> DiscountTypeEnum.MEMBER.getCode() == e.getDiscountType()
                        || DiscountTypeEnum.SINGLE_MEMBER.getCode() == e.getDiscountType())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(memberDiscountList)) {
            BigDecimal memberDiscountAmount = memberDiscountList.stream()
                    .map(DiscountFeeDetailDTO::getDiscountFee)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (memberDiscountAmount.compareTo(BigDecimal.ZERO) > 0) {
                calculateOrderRespDTO.setUseMemberDiscountFlag(true);
            }
            if (Boolean.FALSE.equals(calculateOrderRespDTO.getUnAbleMemberDiscountFlag()) &&
                    memberDiscountAmount.compareTo(BigDecimal.ZERO) == 0
                    && Boolean.TRUE.equals(calculateDTO.getUseMemberDiscountFlag())) {
                calculateOrderRespDTO.setUnAbleMemberDiscountFlag(true);
                calculateOrderRespDTO.setUnAbleMemberDiscountReason(Strings.EMPTY);
            }
        }
        List<MarketingActivityInfoRespDTO> activityInfoList = calculateOrderRespDTO.getActivityInfoList();
        if (CollectionUtils.isNotEmpty(activityInfoList)) {
            List<MarketingActivityInfoRespDTO> sortedActivityInfoList = activityInfoList.stream()
                    .sorted(Comparator.comparing(MarketingActivityInfoRespDTO::isUseAble)
                            .thenComparing(MarketingActivityInfoRespDTO::getDiscountPrice)
                            .reversed())
                    .collect(Collectors.toList());
            calculateOrderRespDTO.setActivityInfoList(sortedActivityInfoList);
        }
    }


    /**
     * 判断是否禁用选择会员优惠
     */
    private boolean isMemberDiscountShare(CalculateOrderRespDTO calculateOrderRespDTO, DiscountRuleBO discountRuleBO) {
        if (Boolean.TRUE.equals(calculateOrderRespDTO.getUseMemberDiscountFlag())) {
            return false;
        }
        // 如果使用的是会员价优惠
        if (Boolean.TRUE.equals(calculateOrderRespDTO.getHasMemberPrice())) {
            // 如果是代金券，那么和会员价 需要判断是否互斥
            return VolumeTypeEnum.CASH_COUPON.getCcCode() == discountRuleBO.getVolumeCodeType() && !discountRuleBO.getVolumeShareFlag();
        }
        // 如果使用的是会员折扣优惠
        // 判断满减满折活动是否互斥
        if (Boolean.FALSE.equals(discountRuleBO.getActivityMemberShareFlag())) {
            return true;
        }
        // 判断优惠券（代金券、商品券）是否互斥
        return !discountRuleBO.getVolumeShareFlag();
    }


    /**
     * 计算相关金额返回给前端
     */
    private void calculateFinalAmount(CalculateOrderRespDTO calculateOrderRespDTO, DiscountContext discountContext) {
        calculateOrderRespDTO.setDiscountFeeDetailDTOS(discountContext.getDiscountFeeDetailDTOS());
        // 应付金额，优惠金额
        BigDecimal totalDiscountFee = discountContext.getDiscountFeeDetailDTOS().stream()
                .map(DiscountFeeDetailDTO::getDiscountFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("全部优惠：{}", JacksonUtils.writeValueAsString(discountContext.getDiscountFeeDetailDTOS()));
        calculateOrderRespDTO.setDiscountFee(totalDiscountFee);
        calculateOrderRespDTO.setActuallyPayFee(calculateOrderRespDTO.getOrderFee().subtract(totalDiscountFee));
        log.info("优惠金额合计：{},实付金额合计：{}", calculateOrderRespDTO.getDiscountFee(), calculateOrderRespDTO.getActuallyPayFee());
        // 如果金额为负数，则置为0
        handleZero(calculateOrderRespDTO);
        calculateOrderRespDTO.setDineInItemList(discountContext.getAllItems());
        // 商品信息传入缓存，下单时使用
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        String redisKey = String.format(RedisKeyConstant.CALCULATE_ITEM_INFO, wxMemberSessionDTO.getStoreGuid(),
                wxMemberSessionDTO.getWxtoken());
        redisUtils.setEx(redisKey, JacksonUtils.writeValueAsString(discountContext.getAllItems()), 30, TimeUnit.MINUTES);
    }


    /**
     * 订单会员信息处理
     */
    private void orderMemberInfoHandler(DiscountContext discountContext) {
        if (StringUtils.isEmpty(discountContext.getOrderGuid())) {
            return;
        }
        String memberConsumptionGuid = discountContext.getMemberConsumptionGuid();
        if (StringUtils.isNotEmpty(memberConsumptionGuid)) {
            // 持久化订单会员消费guid
            OrderDTO orderDTO = new OrderDTO();
            orderDTO.setOrderGuid(discountContext.getOrderGuid());
            orderDTO.setMemberConsumptionGuid(memberConsumptionGuid);
            log.info("持久化订单会员消费guid, orderDTO:{}", JacksonUtils.writeValueAsString(orderDTO));
            tradeClientService.updateMemberConsumptionGuid(orderDTO);
        }
    }
}
