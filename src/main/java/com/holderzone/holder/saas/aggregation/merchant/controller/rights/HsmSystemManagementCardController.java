package com.holderzone.holder.saas.aggregation.merchant.controller.rights;


import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.rights.HsmRCardLevelRightsService;
import com.holderzone.holder.saas.member.dto.rights.request.CardDetailReqDTO;
import com.holderzone.holder.saas.member.dto.rights.response.CardDetailInfoRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 体系下卡信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-16
 */
@RestController
@RequestMapping("/hsm/system/management/card")
@Api(description = "会员卡")
public class HsmSystemManagementCardController {

    @Resource
    private HsmRCardLevelRightsService iHsmRCardLevelRightsService;

    @ApiOperation(value = "更新成长值取值的勾选项", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/range")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "更新成长值取值的勾选项")
    public Result updateRange(
        @ApiParam(value = "会员卡对象", required = true) @RequestBody CardDetailReqDTO cardDetailReqDTO) {
        iHsmRCardLevelRightsService.updateRange(cardDetailReqDTO.getCardGuid(),
            cardDetailReqDTO.getLevelRangeGrowthList());
        return Result.buildEmptySuccess();
    }


    @ApiOperation(value = "更新会员卡的信息", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @PostMapping("/card")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "更新会员卡的信息")
    public Result updateCard(
        @Validated @ApiParam(value = "会员卡对象", required = true) @RequestBody CardDetailReqDTO cardDetailReqDTO) {
        iHsmRCardLevelRightsService.updateCard(cardDetailReqDTO);
        return Result.buildEmptySuccess();
    }

    /**
     * 查询会员卡的列表
     *
     * @return 会员卡的信息
     */
    @ApiOperation(value = "会员卡的列表",response = CardDetailInfoRespDTO.class, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping("/query")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "会员卡的列表")
    public Result queryCard(@ApiParam("memberGuid") @RequestParam(value = "memberGuid",required = false) String memberInfoGuid) {
        return Result.buildSuccessResult(iHsmRCardLevelRightsService.queryCard(memberInfoGuid));
    }


}
