package com.holderzone.holder.saas.aggregation.weixin.aop;

import com.holderzone.framework.util.JacksonUtils;
import feign.hystrix.FallbackFactory;
import io.undertow.servlet.spec.HttpServletRequestImpl;
import io.undertow.servlet.spec.HttpServletResponseImpl;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Aspect
@Component
@Slf4j
public class FeignAspect {

	@Resource
	AopLogger aopLogger;

	//com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal
	@Pointcut("execution(* com.holderzone.holder.saas.aggregation.weixin.service.rpc..*.*(..))")
    public void pointCut() {
    	
    }

	@Around("pointCut()")
	public Object around(ProceedingJoinPoint point) throws Throwable {
        Object target = point.getTarget();
        Boolean isFallBack = target instanceof FallbackFactory;
	    if(isFallBack){
			return point.proceed();
        }
		return aopLogger.around(point,true);
	}

	public static void main(String[] args) {
		System.out.println(JacksonUtils.writeValueAsString(null));
	}
}
