package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxStoreAdvanceOrderClientService;
import com.holderzone.saas.store.dto.weixin.WebSocketMessageDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceOrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceOrderPriceDTO;
import com.holderzone.saas.store.enums.weixin.WxPageNumEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreAdvanceOrderController
 * @date 2019/3/19
 */
@Slf4j
@RestController
@RequestMapping(value = "/deal/wx_store_advance_order")
@Api("微信门店预订单管理")
public class WxStoreAdvanceOrderController {

	private final WxStoreAdvanceOrderClientService wxStoreAdvanceOrderClientService;

	@Autowired
	public WxStoreAdvanceOrderController(WxStoreAdvanceOrderClientService wxStoreAdvanceOrderClientService) {
		this.wxStoreAdvanceOrderClientService = wxStoreAdvanceOrderClientService;
	}

	@ApiOperation("选好了")
	@PostMapping(value = "/add")
	public Result createAdvanceOrder(@RequestBody WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO) {
		log.error("上传商品:{}",wxStoreAdvanceOrderDTO);
		return Result.buildSuccessResult(wxStoreAdvanceOrderClientService.createAdvanceOrder(wxStoreAdvanceOrderDTO));
	}

	@ApiOperation("删除用户预订单")
	@PostMapping(value = "/del")
	public Result<String> delAdvanceOrder(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		log.error("获取别人的商品：{}",wxStoreAdvanceConsumerReqDTO);
		return wxStoreAdvanceOrderClientService.delAdvanceOrder(wxStoreAdvanceConsumerReqDTO) ? Result.buildSuccessMsg("操作成功") : Result.buildOpFailedResult("操作失败");
	}

	@ApiOperation("获取个人预订单")
	@PostMapping(value = "/advance")
	public Result<WxStoreAdvanceOrderPriceDTO> getPersonWxStoreAdvanceOrder(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		WxStoreAdvanceOrderPriceDTO personWxStoreAdvanceOrder = wxStoreAdvanceOrderClientService.getPersonWxStoreAdvanceOrder(wxStoreAdvanceConsumerReqDTO);
		return Result.buildSuccessResult(personWxStoreAdvanceOrder);
	}

	@ApiOperation("获取桌台订单")
	@PostMapping(value = "/table")
	public Result<WebSocketMessageDTO> getTableWxStoreAdvanceOrder(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		WxStoreAdvanceOrderPriceDTO tableWxStoreAdvanceOrder = wxStoreAdvanceOrderClientService.getTableWxStoreAdvanceOrder(wxStoreAdvanceConsumerReqDTO);
		WebSocketMessageDTO<WxStoreAdvanceOrderPriceDTO> build = WebSocketMessageDTO.<WxStoreAdvanceOrderPriceDTO>builder().isJump(0).type(WxPageNumEnum.ADVANCE.getCode()).content(tableWxStoreAdvanceOrder).build();
		return Result.buildSuccessResult(build);
	}

	@ApiOperation("修改预订单整单备注")
	@PostMapping(value = "/update_remark")
	public Result<WebSocketMessageDTO<WxStoreAdvanceOrderPriceDTO>> updateAdvanceOrderRemark(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
		log.info("入参wxStoreAdvanceConsuerReqDTO:{}", wxStoreAdvanceConsumerReqDTO);
		return Result.buildEmptySuccess();
	}

}
