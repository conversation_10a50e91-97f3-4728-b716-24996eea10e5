package com.holderzone.holder.saas.aggregation.app.service.feign.trade;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.trade.req.record.MemberOperateRecordReqDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * <AUTHOR>
 * @date 2023/10/17
 * @description 操作记录远程调用
 */
@Component
@FeignClient(name = "holder-saas-store-business", fallbackFactory = OperateRecordClientService.FastFoodFallBack.class)
public interface OperateRecordClientService {

    @ApiOperation(value = "保存记录")
    @PostMapping("/member_operate_record/save_record")
    void saveRecord(@RequestBody MemberOperateRecordReqDTO reqDTO);

    @Component
    @Slf4j
    class FastFoodFallBack implements FallbackFactory<OperateRecordClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public OperateRecordClientService create(Throwable throwable) {
            return reqDTO -> {
                log.error(HYSTRIX_PATTERN, "saveRecord", JacksonUtils.writeValueAsString(reqDTO),
                        ThrowableUtils.asString(throwable));
                throw new ServerException();
            };
        }
    }
}
