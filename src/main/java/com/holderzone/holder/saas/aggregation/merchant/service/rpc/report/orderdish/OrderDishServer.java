package com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.orderdish;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.report.OrderDishDTO;
import com.holderzone.saas.store.dto.report.OrderDishDetailDTO;
import com.holderzone.saas.store.dto.report.OrderDishTypeInfoDTO;
import com.holderzone.saas.store.dto.report.query.OrderDishDetailQueryDTO;
import com.holderzone.saas.store.dto.report.query.OrderDishQueryDTO;
import com.holderzone.saas.store.dto.report.query.OrderDishTypeInfoQueryDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2018/10/15 下午 15:39
 * @description
 */
@FeignClient(value = "holder-saas-store-report", fallbackFactory = OrderDishServer.ServiceFallBack.class)
public interface OrderDishServer {

    Logger log = LoggerFactory.getLogger(OrderDishServer.class);

    /**
     * 销售菜品或者赠送菜品报表
     *
     * @param orderDishQuery
     * @return
     */
    @PostMapping("/orderDish/saleDish")
    Page<OrderDishDTO> saleDish(@RequestBody OrderDishQueryDTO orderDishQuery);

    /**
     * 销售菜品或者赠送菜品明细报表
     *
     * @param orderDishDetailQuery
     * @return
     */
    @PostMapping("/orderDish/saleDishDetail")
    Page<OrderDishDetailDTO> saleDishDetail(@RequestBody OrderDishDetailQueryDTO orderDishDetailQuery);

    /**
     * 订单菜品分类统计
     *
     * @param orderDishTypeInfoQuery
     * @return
     */
    @PostMapping("/orderDish/saleDishTypeInfo")
    Page<OrderDishTypeInfoDTO> orderDishTypeInfo(@RequestBody OrderDishTypeInfoQueryDTO orderDishTypeInfoQuery);

    /**
     * 退菜统计
     *
     * @param orderDishQuery
     * @return
     */
    @PostMapping("/orderDish/saleDishReturn")
    Page<OrderDishDTO> orderDishReturn(@RequestBody OrderDishQueryDTO orderDishQuery);

    /**
     * 退菜明细
     *
     * @param orderDishDetailQuery
     * @return
     */
    @PostMapping("/orderDish/saleDishReturnDetail")
    Page<OrderDishDetailDTO> orderDishReturnDetail(@RequestBody OrderDishDetailQueryDTO orderDishDetailQuery);

    @Component
    class ServiceFallBack implements FallbackFactory<OrderDishServer> {


        @Override
        public OrderDishServer create(Throwable throwable) {
            return new OrderDishServer() {
                @Override
                public Page<OrderDishDTO> saleDish(OrderDishQueryDTO orderDishQuery) {
                    log.error("销售菜品或者赠送菜品报表异常", throwable.getCause());
                    return null;
                }

                @Override
                public Page<OrderDishDetailDTO> saleDishDetail(OrderDishDetailQueryDTO orderDishDetailQuery) {
                    log.error("销售菜品或者赠送菜品明细报表", throwable.getCause());
                    return null;
                }

                @Override
                public Page<OrderDishTypeInfoDTO> orderDishTypeInfo(OrderDishTypeInfoQueryDTO orderDishTypeInfoQuery) {
                    log.error("订单菜品分类统计", throwable.getCause());
                    return null;
                }

                @Override
                public Page<OrderDishDTO> orderDishReturn(OrderDishQueryDTO orderDishQuery) {
                    log.error("退菜统计", throwable.getCause());
                    return null;
                }

                @Override
                public Page<OrderDishDetailDTO> orderDishReturnDetail(OrderDishDetailQueryDTO orderDishDetailQuery) {
                    log.error("退菜明细", throwable.getCause());
                    return null;
                }
            };
        }
    }
}
