package com.holderzone.holder.saas.aggregation.app.controller.emq;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.manage.EmqReconnectManage;
import com.holderzone.saas.store.dto.emq.EmqReconnectDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * emq客户端重连后置操作
 */
@Slf4j
@RestController
@RequestMapping("/emq/reconnect")
@Api(tags = "emq客户端重连操作相关接口")
public class EmqReconnectController {

    @Autowired
    private EmqReconnectManage emqReconnectManage;

    /**
     * 暂时不用
     */
    @ApiOperation(value = "emq客户端重连通知")
    @PostMapping("/notify")
    public Result notifyClient(@RequestBody EmqReconnectDTO emqReconnectDTO) {
        log.info("emq客户端重连通知，入参：{}", JacksonUtils.writeValueAsString(emqReconnectDTO));
        emqReconnectManage.notifyClient(emqReconnectDTO);
        return Result.buildEmptySuccess();
    }

}
