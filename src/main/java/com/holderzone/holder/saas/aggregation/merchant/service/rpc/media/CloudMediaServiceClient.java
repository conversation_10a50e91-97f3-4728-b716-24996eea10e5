package com.holderzone.holder.saas.aggregation.merchant.service.rpc.media;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.media.dto.entity.MediaDTO;
import com.holderzone.holder.saas.store.media.dto.entity.PageMedia;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(value = "holder-saas-cloud-extension", fallbackFactory = CloudMediaServiceClient.CloudMediaServiceClientFallBack.class)
public interface CloudMediaServiceClient {

    @PostMapping("/file/queryMediaList")
    Page<MediaDTO> queryMediaList(@RequestBody PageMedia pageMedia);

    @PostMapping("/file/queryMediaByName")
    Page<MediaDTO> queryMediaByName(@RequestBody PageMedia pageMedia);

    @Slf4j
    @Component
    class CloudMediaServiceClientFallBack implements FallbackFactory<CloudMediaServiceClient> {

        @Override
        public CloudMediaServiceClient create(Throwable throwable) {
            return new CloudMediaServiceClient() {
                @Override
                public Page<MediaDTO> queryMediaList(PageMedia pageMedia) {
                    log.error("加载文件异常 e={} pageMedia={}", throwable.getMessage(), JacksonUtils.writeValueAsString(pageMedia));
                    throw new BusinessException("加载文件发生异常");
                }

                @Override
                public Page<MediaDTO> queryMediaByName(PageMedia pageMedia) {
                    log.error("查询文件异常 e={} pageMedia={}", throwable.getMessage(), JacksonUtils.writeValueAsString(pageMedia));
                    throw new BusinessException("查询文件发生异常");
                }
            };
        }
    }
}
