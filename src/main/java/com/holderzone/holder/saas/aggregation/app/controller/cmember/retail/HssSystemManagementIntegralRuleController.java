package com.holderzone.holder.saas.aggregation.app.controller.cmember.retail;


import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.app.service.feign.member.HssMemberTerminalClientService;
import com.holderzone.holder.saas.member.terminal.dto.retail.ResponseRetailIntegralCompute;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <p>
 * 体系积分规则表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-20
 */
@RestController
@RequestMapping("/hss/rule")
@Slf4j
public class HssSystemManagementIntegralRuleController {

    @Resource
    private HssMemberTerminalClientService hssMemberTerminalClientService;

    @GetMapping("/integral/compute")
    @ApiOperation(value = "积分抵扣计算")
    public Result<ResponseRetailIntegralCompute> integralCompute(
            @ApiParam(value = "memberInfoCardGuid", name = "会员持卡guid", required = true) String memberInfoCardGuid,
            @ApiParam(value = "orderMoney", name = "订单金额", required = true) BigDecimal orderMoney) {
        log.info("[商超]-[积分抵扣]-计算抵扣金额入参,memberInfoCardGuid={},orderMoney={}", memberInfoCardGuid, orderMoney);
        return Result.buildSuccessResult(hssMemberTerminalClientService.integralCompute(memberInfoCardGuid, orderMoney));
    }

}
