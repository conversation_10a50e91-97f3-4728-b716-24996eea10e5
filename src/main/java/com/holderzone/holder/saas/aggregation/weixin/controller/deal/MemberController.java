package com.holderzone.holder.saas.aggregation.weixin.controller.deal;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.entity.dto.CheckVerifyVolumeRespDTO;
import com.holderzone.holder.saas.aggregation.weixin.service.MemberService;
import com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList;
import com.holderzone.holder.saas.member.wechat.dto.card.RequestCardRightDetails;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseCardRight;
import com.holderzone.saas.store.dto.weixin.deal.*;
import com.holderzone.saas.store.dto.weixin.member.WxMemberInfoVolumeDetailsRespDTO;
import com.holderzone.saas.store.dto.weixin.member.WxPrepayConfirmRespDTO;
import com.holderzone.saas.store.dto.weixin.member.WxVolumeDetailReqDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Api(value = "重构会员相关")
@Slf4j
@RestController
@RequestMapping("/deal/member")
@Validated
public class MemberController {

	private final MemberService memberService;

	@Autowired
	public MemberController(MemberService memberService) {
		this.memberService = memberService;
	}

	@ApiOperation("点餐会员卡")
	@PostMapping("/card_page")
	public Result<List<MemberCardItemDTO>> cardPage() {
		return Result.buildSuccessResult(memberService.cardPage());
	}

    /**
     * h5 下单之后查询优惠券列表
     */
    @ApiOperation("优惠券列表")
    @PostMapping("/volume_page")
    public Result<List<MemberVolumeItemDTO>> volumePage(@RequestBody VolumePageReqDTO volumePageReqDTO) {
        log.info("优惠券列表入参:{}", volumePageReqDTO);
        return Result.buildSuccessResult(memberService.volumeList(volumePageReqDTO));
    }

    /**
     * 小程序 下单之前查询优惠券列表
     */
    @ApiOperation("优惠券列表")
    @PostMapping("/volume_list")
    public Result<List<ResponseVolumeList>> volumeList(@RequestBody @Valid VolumeOrderQueryDTO queryDTO) {
        return Result.buildSuccessResult(memberService.queryVolumeList(queryDTO));
    }

    @ApiOperation("优惠券详情")
    @PostMapping(value = "/volume_details")
    public Result<WxMemberInfoVolumeDetailsRespDTO> volumeCodeDetails(@RequestBody WxVolumeDetailReqDTO wxVolumeDetailReqDTO) {
        log.info("优惠券详情入参:{}", JacksonUtils.writeValueAsString(wxVolumeDetailReqDTO));
        wxVolumeDetailReqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        return Result.buildSuccessResult(memberService.volumeDetail(wxVolumeDetailReqDTO));
    }



	@ApiOperation("会员卡权益数组")
	@PostMapping(value = "/card_right")
	public Result<List<ResponseCardRight>> cardRightList(@RequestBody RequestCardRightDetails requestCardRightDetails) {
		log.info("会员卡权益数组入参:{}", JacksonUtils.writeValueAsString(requestCardRightDetails));
		return Result.buildSuccessResult(memberService.cardRight(requestCardRightDetails));
	}

	@ApiOperation("会员卡与优惠券选择确认")
	@PostMapping(value = "/prepay_confirm")
	public Result<WxPrepayConfirmRespDTO> memberConfirm(@RequestBody MemberConfirmReqDTO memberConfirmReqDTO) {
		log.info("会员卡与优惠券选择确认入参:{}",memberConfirmReqDTO);
		return Result.buildSuccessResult(memberService.confirm(memberConfirmReqDTO));
	}

	@ApiOperation("优惠明细")
	@PostMapping(value = "/discount")
	public Result<ConcessionTotalRespDTO> discount(@Valid @RequestBody ConcessionTotalReqDTO concessionTotalReqDTO) {
		log.info("优惠明细计算入参:{}", JacksonUtils.writeValueAsString(concessionTotalReqDTO));
		return Result.buildSuccessResult(memberService.discount(concessionTotalReqDTO));
	}

	@ApiOperation("优惠券点击触发提示")
	@PostMapping(value = "/check")
	public Result<CheckVerifyVolumeRespDTO> checkVolume(@RequestBody VolumePageReqDTO volumePageReqDTO) {
		log.info("优惠券点击触发提示入参:{}",volumePageReqDTO);
		return Result.buildSuccessResult(memberService.checkVolume(volumePageReqDTO));
	}
}
