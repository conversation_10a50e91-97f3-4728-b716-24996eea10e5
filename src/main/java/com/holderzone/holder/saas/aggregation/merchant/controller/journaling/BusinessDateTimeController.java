package com.holderzone.holder.saas.aggregation.merchant.controller.journaling;

import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.BusinessDateTimeService;
import com.holderzone.saas.store.dto.journaling.req.BusinessDateTimeReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.BusinessDateTimeRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/business_datetime")
@Slf4j
@Api("营业日期和时间接口")
public class BusinessDateTimeController {

    @Autowired
    BusinessDateTimeService businessDateTimeService;

    @PostMapping("/datetime")
    @ApiOperation("根据日期类型获取营业日期时间")
    public Result<BusinessDateTimeRespDTO> businessDateTime(@RequestBody BusinessDateTimeReqDTO businessDateTimeReqDTO) {
        return Result.buildSuccessResult(businessDateTimeService.getBusinessDateTime(businessDateTimeReqDTO));
    }
    @PostMapping("/datetime_storeGuid")
    @ApiOperation("根据门店guid集合获取营业日期时间")
    public Result<BusinessDateTimeRespDTO> businessDateTimes(@RequestBody BusinessDateTimeReqDTO businessDateTimeReqDTO) {
        return Result.buildSuccessResult(businessDateTimeService.getBusinessDateTimeByStoreGuids(businessDateTimeReqDTO));
    }

}
