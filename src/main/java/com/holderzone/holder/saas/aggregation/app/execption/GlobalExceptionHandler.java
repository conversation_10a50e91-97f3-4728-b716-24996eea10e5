package com.holderzone.holder.saas.aggregation.app.execption;

import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONTokener;
import com.alibaba.fastjson.JSONObject;
import com.holderzone.feign.spring.boot.core.GenericException;
import com.holderzone.feign.spring.boot.core.OpenFeignException;
import com.holderzone.feign.spring.boot.core.RawJsonException;
import com.holderzone.feign.spring.boot.exception.ResultExceptionHandlerAdapter;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.response.ResultEnum;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.common.exception.MemberTerminalException;
import com.holderzone.saas.store.dto.exception.GroupBuyException;
import com.holderzone.saas.store.dto.exception.InterfaceDeprecatedException;
import com.holderzone.saas.store.dto.table.exception.BindUpAccountsException;
import com.holderzone.saas.store.dto.table.exception.TableBusinessLockException;
import com.holderzone.saas.store.dto.trade.exception.OrderItemTransferVerifyException;
import com.holderzone.saas.store.dto.trade.exception.OrderLockException;
import com.holderzone.saas.store.dto.trade.exception.OrderRefundLockException;
import com.holderzone.saas.store.enums.common.ResultCodeEnum;
import com.holderzone.saas.store.enums.locale.LocaleMessageEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GlobalExceptionHandler
 * @date 2018/09/14 16:01
 * @description 全局异常拦截
 * @program holder-saas-aggregation-app
 */
@RestControllerAdvice
public class GlobalExceptionHandler extends ResultExceptionHandlerAdapter {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(value = InterfaceDeprecatedException.class)
    public Result interfaceDeprecatedException(InterfaceDeprecatedException e) {
        String message = e.getMessage();
        if (logger.isErrorEnabled()) {
            logger.error("业务异常:{}", message);
        }
        return Result.buildOpFailedResult(message);
    }


    /**
     * @param e
     * @return
     */
    @ExceptionHandler(value = MemberTerminalException.class)
    public Result memberException(MemberTerminalException e) {
        String message = e.getMessage();
        if (logger.isErrorEnabled()) {
            logger.error("业务异常:{}", message);
        }
        return (new Result()).setCode(e.getExceptionEnums().getCode()).setMessage(message);
    }

    /**
     * 拦截桌台业务锁定异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(value = TableBusinessLockException.class)
    public Result tableException(TableBusinessLockException e) {
        if (logger.isErrorEnabled()) {
            logger.error("桌台业务锁定异常", e);
        }
        return Result.buildFailResult(ResultCodeEnum.TABLE_BUSINESS_LOCK_EXCEPTION.getCode(), "桌台锁定异常");
    }

    /**
     * 拦截桌台业务锁定异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(value = BindUpAccountsException.class)
    public Result BindUpAccountsException(BindUpAccountsException e) {
        if (logger.isErrorEnabled()) {
            logger.error("桌台清空失败", e);
        }
        return Result.buildFailResult(ResultCodeEnum.BIND_UP_ACCOUNTS_EXCEPTION.getCode(), e.getMessage());
    }

    /**
     * 拦截桌台业务锁定异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(value = OrderLockException.class)
    public Result orderLockException(OrderLockException e) {
        if (logger.isErrorEnabled()) {
            logger.error("订单锁异常", e);
        }
        e.printStackTrace();
        return Result.buildFailResult(2000, e.getMessage());
    }

    @ExceptionHandler(value = GroupBuyException.class)
    public Result<?> groupBuyException(GroupBuyException e) {
        return Result.buildFailResult(500, e.getMessage());
    }


    /**
     * 订单退款锁冲突
     */
    @ExceptionHandler(value = OrderRefundLockException.class)
    public Result orderRefundLockException(OrderRefundLockException e) {
        if (logger.isErrorEnabled()) {
            logger.error("订单退款锁冲突", e);
        }
        return Result.buildFailResult(ResultCodeEnum.ORDER_REFUND_LOCK_EXCEPTION.getCode(), ResultCodeEnum.ORDER_REFUND_LOCK_EXCEPTION.getDesc());
    }

    @ExceptionHandler(value = IOException.class)
    public Result ioException(IOException e) {
        String message = e.getMessage();
        logger.error("IOException:" + e);
        if (message.contains("Connection reset by peer")) {
            message = "业务繁忙，请稍后再试";
        }
        return Result.buildOpFailedResult(message);
    }

    @Override
    protected Result interceptRawJsonException(RawJsonException e) {
        String message = e.getMessage();
        Object json = new JSONTokener(message, new JSONConfig()).nextValue();
        if (json instanceof JSONObject) {
            JSONObject bodyJson = JSONObject.parseObject(message);
            //会员的特殊code处理
            if (bodyJson.containsKey("sourceType")) {
                return Result.buildFailResult(
                        bodyJson.getInteger("errorCode"),
                        bodyJson.getString("message")
                );
            }
        }
        // todo 使用 SpecificException
        if (StringUtils.hasLength(message) && message.startsWith("{")) {
            //自己的特殊code处理
            JSONObject bodyJson = JSONObject.parseObject(message);
            if (bodyJson.containsKey("definedCode")) {
                return Result.buildFailResult(
                        bodyJson.getInteger("definedCode"),
                        bodyJson.getString("definedMessage")
                );
            }
        }
        return super.interceptRawJsonException(e);
    }

    @Override
    protected Result interceptOpenFeignException(OpenFeignException e) {
        String message = e.getMessage();
        // todo 使用 SpecificException
        // 这里判断staff服务中userController的validate方法的异常code与message做特殊处理，后续有更好的解决方法考虑替换
        if (message.length() >= 10 && "406".equals(message.substring(7, 10))
                && message.contains("设备已解绑，请重新绑定门店")) {
            return Result.buildFailResult(
                    ResultEnum.NO_SUCH_APPLICATION_EXISTS.getResultCode(),
                    "设备已解绑，请重新绑定门店"
            );
        }
        return super.interceptOpenFeignException(e);
    }

    @Override
    @ExceptionHandler(value = GenericException.class)
    public Result<?> genericException(GenericException e) {
        logger.error("业务异常",e);
        return Result.buildOpFailedResult(LocaleMessageEnum.getLocale(e.getMessage()));
    }

    @Override
    @ExceptionHandler({BusinessException.class})
    @ConditionalOnClass({BusinessException.class})
    public Result<?> businessException(BusinessException e) {
        logger.error("业务异常",e);
        return Result.buildOpFailedResult(LocaleMessageEnum.getLocale(e.getMessage()));
    }

    /**
     * 订单转菜异常
     */
    @ExceptionHandler(value = OrderItemTransferVerifyException.class)
    public Result<?> orderItemTransferVerifyException(OrderItemTransferVerifyException e) {
        String message = e.getMessage();
        if (logger.isErrorEnabled()) {
            logger.error("订单转菜校验异常:{}", message);
        }
        return Result.buildFailResult(ResultCodeEnum.ORDER_ITEM_TRANSFER_VERIFY_EXCEPTION.getCode(), e.getMessage());
    }

}
