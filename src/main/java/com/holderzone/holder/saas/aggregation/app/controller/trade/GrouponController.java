package com.holderzone.holder.saas.aggregation.app.controller.trade;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.GroupBuyService;
import com.holderzone.holder.saas.aggregation.app.service.feign.takeout.TakeoutProClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.GrouponClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.TradeThirdActivityClientService;
import com.holderzone.saas.store.dto.order.request.groupon.GrouponReqDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GrouponController
 * @date 2018/09/04 11:26
 * @description 团购券接口
 * @program holder-saas-aggregation-app
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class GrouponController {

    private final GroupBuyService groupBuyService;

    private final GrouponClientService grouponClientService;

    private final TakeoutProClientService takeoutProClientService;

    private final TradeThirdActivityClientService tradeThirdActivityClientService;

    /**
     * 验券加商品
     */
    @ApiOperation(value = "预验券查询商品", notes = "预验券查询商品")
    @PostMapping(value = "/third_activity/pre_check/query_item", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<MtCouponPreRespDTO>> preCheckQueryItem(@RequestBody @Validated CouPonPreReqDTO couPonPreReqDTO) {
        log.info("预验券查询商品入参：{}", JacksonUtils.writeValueAsString(couPonPreReqDTO));
        return Result.buildSuccessResult(groupBuyService.preCheckQueryItem(couPonPreReqDTO));
    }

    @ApiOperation(value = "结账页面预验券", notes = "结账页面预验券")
    @PostMapping(value = "/groupon/pre_check", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<List<MtCouponPreRespDTO>> preCheck(@RequestBody @Validated CouPonPreReqDTO couPonPreReqDTO) {
        log.info("结账页面预验券入参：{}", JacksonUtils.writeValueAsString(couPonPreReqDTO));
        return Result.buildSuccessResult(groupBuyService.preCheck(couPonPreReqDTO));
    }

    @ApiOperation(value = "验券", notes = "验券")
    @PostMapping("/groupon/verify")
    public Result<List<GrouponListRespDTO>> verify(@RequestBody GrouponReqDTO grouponReqDTO) {
        log.info("结账页面验券入参：{}", JacksonUtils.writeValueAsString(grouponReqDTO));
        return Result.buildSuccessResult(grouponClientService.verify(grouponReqDTO));
    }

    @ApiOperation(value = "撤销", notes = "撤销")
    @PostMapping("/groupon/undo")
    public Result<Void> undo(@RequestBody GrouponReqDTO grouponReqDTO) {
        log.info("结账页面撤销入参：{}", JacksonUtils.writeValueAsString(grouponReqDTO));
        grouponClientService.undo(grouponReqDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "查询团购验券列表", notes = "查询验券列表")
    @GetMapping("/groupon/list")
    public Result<List<GrouponListRespDTO>> listByOrderGuid(String orderGuid, Integer grouponType) {
        return Result.buildSuccessResult(grouponClientService.list(orderGuid, grouponType));
    }

    @ApiOperation(value = "查询门店授权列表", notes = "查询门店授权列表")
    @GetMapping("/groupon/auth/list/{storeGuid}")
    public Result<List<StoreAuthDTO>> query(@PathVariable("storeGuid") String storeGuid) {
        return Result.buildSuccessResult(takeoutProClientService.queryStoreAuth(storeGuid));
    }

    /**
     * 查询该活动绑定的券码列表
     */
    @ApiOperation(value = "查询该活动绑定的券码列表", notes = "查询该活动绑定的券码列表")
    @GetMapping(value = "/third_activity/couponCodes/query_activity")
    public Result<List<GrouponListRespDTO>> grouponCodesByActivityGuid(String orderGuid, String activityGuid) {
        log.info("查询该活动绑定的券码列表入参：orderGuid:{}, activityGuid:{} ", orderGuid, activityGuid);
        return Result.buildSuccessResult(tradeThirdActivityClientService.grouponCodesByActivityGuid(orderGuid, activityGuid));
    }

}
