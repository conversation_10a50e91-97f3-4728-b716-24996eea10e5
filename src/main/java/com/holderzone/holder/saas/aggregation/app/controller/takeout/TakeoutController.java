package com.holderzone.holder.saas.aggregation.app.controller.takeout;

import cn.hutool.core.collection.CollectionUtil;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.constant.Constant;
import com.holderzone.holder.saas.aggregation.app.entity.OrderLocaleEnum;
import com.holderzone.holder.saas.aggregation.app.manage.TakeoutManage;
import com.holderzone.holder.saas.aggregation.app.service.feign.takeout.TakeoutClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.takeout.TakeoutProClientService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.print.PrintOrderDTO;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.SalesUpdateDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutTCDConfirmTheMealDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutTCDDiningOutDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutTCDPickUpDTO;
import com.holderzone.saas.store.dto.takeaway.response.AcceptOrRefuseOrderRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.OwnDistributionDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutOrderStatisticsRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.TcdCommonRespDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeawayController
 * @date 2018/09/08 9:59
 * @description
 * @program holder-saas-aggregation-app
 */
@Slf4j
@RestController
@RequestMapping("/takeout")
@Api(description = "外卖接口")
public class TakeoutController {

    private final String success = "10000";

    private final TakeoutClientService takeoutClientService;

    private final TakeoutProClientService takeoutProClientService;

    private final TakeoutManage takeoutManage;


    @Autowired
    public TakeoutController(TakeoutClientService takeoutClientService, TakeoutProClientService takeoutProClientService,
                             TakeoutManage takeoutManage) {
        this.takeoutClientService = takeoutClientService;
        this.takeoutProClientService = takeoutProClientService;
        this.takeoutManage = takeoutManage;
    }

    @ApiOperation(value = "商家接单", notes = "商家接单")
    @PostMapping(value = "/accept_order", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "商家接单")
    public Result<AcceptOrRefuseOrderRespDTO> acceptOrder(@RequestBody TakeoutOrderDTO takeawayOrderDTO) {
        log.info("商家接单入参：{}", JacksonUtils.writeValueAsString(takeawayOrderDTO));
        takeoutClientService.acceptOrder(takeawayOrderDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "商家取消订单", notes = "商家取消订单")
    @PostMapping(value = "/cancel_order", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "商家取消订单")
    public Result<AcceptOrRefuseOrderRespDTO> cancelOrder(@RequestBody TakeoutOrderDTO takeawayOrderDTO) {
        log.info("商家取消订单入参：{}", JacksonUtils.writeValueAsString(takeawayOrderDTO));
        takeoutClientService.cancelOrder(takeawayOrderDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "商家同意取消订单", notes = "商家同意取消订单")
    @PostMapping(value = "/agree_cancel_req", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "商家同意取消订单")
    public Result<String> agreeCancelReq(@RequestBody TakeoutOrderDTO takeawayOrderDTO) {
        log.info("商家同意取消订单入参：{}", JacksonUtils.writeValueAsString(takeawayOrderDTO));
        return Result.buildSuccessResult(takeoutClientService.agreeCancelReq(takeawayOrderDTO));
    }

    @ApiOperation(value = "商家不同意取消订单", notes = "商家不同意取消订单")
    @PostMapping(value = "/disagree_cancel_req", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "商家不同意取消订单")
    public Result<String> disagreeCancelReq(@RequestBody TakeoutOrderDTO takeawayOrderDTO) {
        log.info("商家不同意取消订单入参：{}", JacksonUtils.writeValueAsString(takeawayOrderDTO));
        return Result.buildSuccessResult(takeoutClientService.disagreeCancelReq(takeawayOrderDTO));
    }

    @ApiOperation(value = "商家同意退单", notes = "商家同意退单")
    @PostMapping(value = "/agree_refund_req", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "商家同意退单")
    public Result<String> agreeRefundReq(@RequestBody TakeoutOrderDTO takeawayOrderDTO) {
        log.info("商家同意退单入参：{}", JacksonUtils.writeValueAsString(takeawayOrderDTO));
        return Result.buildSuccessResult(takeoutClientService.agreeRefundReq(takeawayOrderDTO));
    }

    @ApiOperation(value = "商家不同意退单", notes = "商家不同意退单")
    @PostMapping(value = "/disagree_refund_req", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "商家不同意退单")
    public Result<String> disagreeRefundReq(@RequestBody TakeoutOrderDTO takeawayOrderDTO) {
        log.info("商家不同意退单入参：{}", JacksonUtils.writeValueAsString(takeawayOrderDTO));
        return Result.buildSuccessResult(takeoutClientService.disagreeRefundReq(takeawayOrderDTO));
    }

    @ApiOperation(value = "商家查询订单", notes = "商家查询订单")
    @PostMapping(value = "/list_order", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "商家查询订单")
    public Result<List<TakeoutOrderListDTO>> listOrder(@RequestBody TakeoutOrderDTO takeoutOrderDTO) {
        log.info("商家查询订单入参：{}", JacksonUtils.writeValueAsString(takeoutOrderDTO));
        List<TakeoutOrderListDTO> takeoutOrderList = takeoutClientService.listOrder(takeoutOrderDTO);
        if (CollectionUtil.isNotEmpty(takeoutOrderList)) {
            takeoutOrderList.forEach(t -> {
                if (StringUtils.isEmpty(t.getEstimateDeliveredTimeString())) {
                    return;
                }
                if (t.getEstimateDeliveredTimeString().equals(OrderLocaleEnum.TAKEOUT_IMMEDIATE_DELIVERY.getMessage())) {
                    t.setEstimateDeliveredTimeString(LocaleUtil.getMessage(OrderLocaleEnum.TAKEOUT_IMMEDIATE_DELIVERY.name()));
                }

            });
        }
        return Result.buildSuccessResult(takeoutOrderList);
    }

    @ApiOperation(value = "查询外卖统计", notes = "查询外卖统计 统计待处理订单数量 + 订单异常状态为非初始状态数量")
    @PostMapping(value = "/count_order")
    public Result<TakeoutOrderStatisticsRespDTO> countOrder(@RequestBody TakeoutOrderDTO takeoutOrderDTO) {
        log.info("查询外卖统计：入参{}", JacksonUtils.writeValueAsString(takeoutOrderDTO));
        return Result.buildSuccessResult(takeoutClientService.countOrder(takeoutOrderDTO));
    }

    @ApiOperation(value = "商家查询订单详情", notes = "商家查询订单详情")
    @PostMapping(value = "/get_order", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "商家查询订单详情")
    public Result<TakeoutOrderDTO> getOrderDetail(@RequestBody TakeoutOrderDTO takeoutOrderDTO) {
        log.info("商家查询订单详情入参：{}", JacksonUtils.writeValueAsString(takeoutOrderDTO));
        TakeoutOrderDTO takeoutOrderDTO1 = takeoutClientService.getOrderDetail(takeoutOrderDTO);
        if (takeoutOrderDTO1 != null && CollectionUtil.isNotEmpty(takeoutOrderDTO1.getArrayOfLog())) {
            takeoutOrderDTO1.getArrayOfLog().forEach(t -> {
                t.setTitle(OrderLocaleEnum.getTitleLocale(t.getTitle()));
                //替换body字段里
                t.setBody(OrderLocaleEnum.replaceLocaleBody(t.getBody()));
            });
        }
        return Result.buildSuccessResult(takeoutOrderDTO1);
    }

    @PostMapping(value = "/set_config", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "商家操作外卖设置", notes = "商家操作外卖设置")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "商家操作外卖设置")
    public Result<String> setStoreConfig(@RequestBody TakeoutConfigDTO takeoutConfigDTO) {
        log.info("商家操作外卖设置：入参{}", JacksonUtils.writeValueAsString(takeoutConfigDTO));
        return Result.buildSuccessResult(takeoutClientService.setConfig(takeoutConfigDTO)
                ? LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL) : LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @PostMapping(value = "/query_config", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "商家查询外卖设置", notes = "商家查询外卖设置")
    public Result<TakeoutConfigDTO> queryConfig(@RequestBody TakeoutConfigDTO takeoutConfigDTO) {
        log.info("商家查询是否自动接单：入参{}", JacksonUtils.writeValueAsString(takeoutConfigDTO));
        return Result.buildSuccessResult(takeoutClientService.queryConfig(takeoutConfigDTO));
    }

    @PostMapping(value = "/set_auto_receive", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "商家操作是否自动接单", notes = "操作商家是否自动接单")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "商家操作是否自动接单")
    public Result<String> setStoreAutomaticallyReceiveOrder(@RequestBody TakeoutAutoRcvDTO takeoutAutoRcvDTO) {
        log.info("商家操作是否自动接单：入参{}", JacksonUtils.writeValueAsString(takeoutAutoRcvDTO));
        return Result.buildSuccessResult(takeoutClientService.setAutoReceive(takeoutAutoRcvDTO) ? LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL) : LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @PostMapping(value = "/query_auto_receive", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "商家查询是否自动接单", notes = "查询商家是否自动接单")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "商家查询是否自动接单")
    public Result<TakeoutAutoRcvDTO> queryAutoReceive(@RequestBody TakeoutAutoRcvDTO takeoutAutoRcvDTO) {
        log.info("商家查询是否自动接单：入参{}", JacksonUtils.writeValueAsString(takeoutAutoRcvDTO));
        return Result.buildSuccessResult(takeoutClientService.queryAutoReceive(takeoutAutoRcvDTO));
    }

    @PostMapping(value = "/query_distribution", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "查询自营配送方式", notes = "查询自营配送方式")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "查询自营外卖配送方式")
    public Result<List<OwnDistributionDTO>> queryDistribution(@RequestBody BaseDTO baseDTO) {
        log.info("查询自营配送方式：入参{}", JacksonUtils.writeValueAsString(baseDTO));
        return Result.buildSuccessResult(takeoutClientService.getDistribution(baseDTO));
    }

    @PostMapping(value = "/order_update", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "自营外卖订单状态变更", notes = "自营外卖订单状态变更")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "自营外卖订单状态变更")
    public Result<OwnCallbackResponse> orderUpdate(@RequestBody SalesUpdateDTO salesUpdateDTO) {
        log.info("自营外卖订单状态变更：入参{}", JacksonUtils.writeValueAsString(salesUpdateDTO));
        return Result.buildSuccessResult(takeoutClientService.orderUpdate(salesUpdateDTO));
    }

    @PostMapping(value = "/go_shipping", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "自营外卖发起配送", notes = "自营外卖发起配送")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "自营外卖发起配送")
    public Result<String> goShipping(@RequestBody TakeoutOrderDTO takeoutOrderDTO) {
        log.info("自营外卖发起配送：入参{}", JacksonUtils.writeValueAsString(takeoutOrderDTO));
        OwnCallbackResponse resp = takeoutProClientService.goShipping(takeoutOrderDTO);
        if (success.equals(String.valueOf(resp.getCode()))) {
            return Result.buildSuccessResult(resp.getMessage());
        } else {
            return Result.buildFailResult(resp.getCode(), resp.getMessage());
        }
    }

    @PostMapping(value = "/done_shipping", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "自营外卖完成配送", notes = "自营外卖完成配送")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "自营外卖完成配送")
    public Result<String> doneShipping(@RequestBody TakeoutOrderDTO takeoutOrderDTO) {
        log.info("自营外卖完成配送：入参{}", JacksonUtils.writeValueAsString(takeoutOrderDTO));
        OwnCallbackResponse resp = takeoutProClientService.doneShipping(takeoutOrderDTO);
        if (success.equals(String.valueOf(resp.getCode()))) {
            return Result.buildSuccessResult(resp.getMessage());
        } else {
            return Result.buildFailResult(resp.getCode(), resp.getMessage());
        }
    }

    @PostMapping(value = "/cancel_shipping", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "自营外卖取消配送", notes = "自营外卖取消配送")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "自营外卖取消配送")
    public Result<String> cancelShipping(@RequestBody TakeoutOrderDTO takeoutOrderDTO) {
        log.info("自营外卖取消配送：入参{}", JacksonUtils.writeValueAsString(takeoutOrderDTO));
        OwnCallbackResponse resp = takeoutProClientService.cancelShipping(takeoutOrderDTO);
        if (success.equals(String.valueOf(resp.getCode()))) {
            return Result.buildSuccessResult(resp.getMessage());
        } else {
            return Result.buildFailResult(resp.getCode(), resp.getMessage());
        }
    }


    @PostMapping(value = "/delivery_change")
    @ApiOperation(value = "一城飞客订单状态发生变更", notes = "一城飞客订单状态发生变更")
    public void deliveryChange(@RequestBody TakeoutDeliveryChange takeoutDeliveryChange) {
        log.info("一城飞客订单状态发生变更：入参{}", JacksonUtils.writeValueAsString(takeoutDeliveryChange));
        takeoutClientService.deliveryChange(takeoutDeliveryChange);
    }

    @PostMapping(value = "/delivery_location")
    @ApiOperation(value = "一城飞客骑手定位", notes = "一城飞客订单状态发生变更")
    public void deliveryLocation(@RequestBody TakeoutDeliveryChange takeoutDeliveryChange) {
        log.info("一城飞客骑手定位：入参{}", JacksonUtils.writeValueAsString(takeoutDeliveryChange));
        takeoutClientService.deliveryLocation(takeoutDeliveryChange);
    }

    @PostMapping(value = "/dining_out_tcd")
    @ApiOperation(value = "赚餐外卖出餐", notes = "赚餐外卖出餐")
    public Result<String> diningOutTcd(@RequestBody TakeoutTCDDiningOutDTO takeoutTCDDiningOutDTO) {
        log.info("赚餐外卖出餐：入参{}", JacksonUtils.writeValueAsString(takeoutTCDDiningOutDTO));
        TcdCommonRespDTO tcdCommonRespDTO = takeoutProClientService.diningOutTcd(takeoutTCDDiningOutDTO);
        if (tcdCommonRespDTO.getCode() == 200) {
            return Result.buildSuccessResult(LocaleUtil.getMessage(Constant.ORDER_PREPARATION_SUCCESSFUL));
        } else {
            return Result.buildFailResult(tcdCommonRespDTO.getCode(), tcdCommonRespDTO.getMessage());
        }
    }

    @PostMapping(value = "/confirm_the_meal_tcd")
    @ApiOperation(value = "赚餐外卖确认取餐", notes = "赚餐外卖确认取餐")
    public Result<String> confirmTheMealTcd(@RequestBody TakeoutTCDConfirmTheMealDTO takeoutTCDConfirmTheMealDTO) {
        log.info("赚餐外卖确认取餐：入参{}", JacksonUtils.writeValueAsString(takeoutTCDConfirmTheMealDTO));
        TcdCommonRespDTO tcdCommonRespDTO = takeoutProClientService.confirmTheMealTcd(takeoutTCDConfirmTheMealDTO);
        if (tcdCommonRespDTO.getCode() == 200) {
            return Result.buildSuccessResult(LocaleUtil.getMessage(Constant.ORDER_PREPARATION_SUCCESSFUL));
        } else {
            return Result.buildFailResult(tcdCommonRespDTO.getCode(), tcdCommonRespDTO.getMessage());
        }
    }

    @PostMapping(value = "/pick_up_tcd")
    @ApiOperation(value = "赚餐外卖到店自提扫码", notes = "赚餐外卖到店自提扫码")
    public Result<String> pickUpTcd(@RequestBody TakeoutTCDPickUpDTO takeoutTCDPickUpDTO) {
        log.info("赚餐外卖到店自提扫码：入参{}", JacksonUtils.writeValueAsString(takeoutTCDPickUpDTO));
        TcdCommonRespDTO tcdCommonRespDTO = takeoutProClientService.pickUpTcd(takeoutTCDPickUpDTO);
        if (tcdCommonRespDTO.getCode() == 200) {
            return Result.buildSuccessResult(LocaleUtil.getMessage(Constant.ORDER_PREPARATION_SUCCESSFUL));
        } else {
            return Result.buildFailResult(tcdCommonRespDTO.getCode(), tcdCommonRespDTO.getMessage());
        }
    }

    @PostMapping(value = "/delay/auto_accept_order")
    @ApiOperation(value = "轮询查询自动接单漏单订单", notes = "轮询查询自动接单漏单订单")
    public Result<List<PrintOrderDTO>> delayAutoAcceptOrder(@RequestBody TakeoutOrderDTO takeoutOrderDTO) {
        return Result.buildSuccessResult(takeoutManage.delayAutoAcceptOrder(takeoutOrderDTO));
    }

    @PostMapping(value = "/order_prepared")
    public Result<Void> orderPrepared(@RequestBody TakeoutOrderDTO takeoutOrderDTO) {
        takeoutClientService.orderPrepared(takeoutOrderDTO);
        return Result.buildEmptySuccess();
    }
}
