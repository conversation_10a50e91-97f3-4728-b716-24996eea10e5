package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description session中的consumer信息 加强版 --我也不想放在服务里，但是store-dto没引入微信的包..
 * @date 2021/7/2 14:40
 */
@Data
@ApiModel("session中的consumer信息 加强版")
public class WxMemberSessionPlusDTO {

    @ApiModelProperty("session中的consumer信息")
    private WxMemberSessionDTO wxMemberSessionDTO;

    @ApiModelProperty("点餐人数")
    private Integer orderNumber;
}
