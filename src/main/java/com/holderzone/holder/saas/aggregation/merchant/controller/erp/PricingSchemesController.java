package com.holderzone.holder.saas.aggregation.merchant.controller.erp;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp.MaterialFeignService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp.PricingSchemesFeignService;
import com.holderzone.saas.store.dto.erp.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @className PricingSchemesController
 * @date 2019-05-05 15:26:18
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Api(tags = "供应商报价方案")
@RestController
public class PricingSchemesController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PricingSchemesController.class);
    private final PricingSchemesFeignService pricingSchemesFeignService;
    private final MaterialFeignService materialFeignService;

    @Autowired
    public PricingSchemesController(PricingSchemesFeignService pricingSchemesFeignService, MaterialFeignService materialFeignService) {
        this.pricingSchemesFeignService = pricingSchemesFeignService;
        this.materialFeignService = materialFeignService;
    }

    @ApiOperation(value = "保存报价方案")
    @PostMapping("/pricing")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "保存报价方案",action = OperatorType.ADD)
    public Result<Boolean> savePricingSchemes(@RequestBody PricingReqDTO pricingReqDTO) {
        LOGGER.info("ERP系统聚合层(保存报价方案)-> PricingReqDTO:{}", JacksonUtils.writeValueAsString(pricingReqDTO));
        return Result.buildSuccessResult(pricingSchemesFeignService.savePricingSchemes(pricingReqDTO));
    }

    @ApiOperation(value = "报价方案列表(含停止供应)")
    @PostMapping("/pricing/query/{suppliersGuid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "报价方案列表(含停止供应)",action = OperatorType.SELECT)
    public Result<List<PricingSchemesDTO>> getPricingSchemesListIncludeDisabled(@PathVariable("suppliersGuid") String suppliersGuid) {
        LOGGER.info("ERP系统聚合层(报价方案列表(含停止供应))-> suppliersGuid:{}", suppliersGuid);
        return Result.buildSuccessResult(pricingSchemesFeignService.getPricingSchemesList(suppliersGuid));
    }

    @ApiOperation(value = "删除物料报价信息")
    @PostMapping("/pricing/delete/{guid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "删除物料报价信息",action = OperatorType.DELETE)
    public Result<Boolean> deletePricingSchemes(@PathVariable("guid") String guid) {
        LOGGER.info("ERP系统聚合层(删除物料报价信息)-> guid:{}", guid);
        return Result.buildSuccessResult(pricingSchemesFeignService.deletePricingSchemes(guid));
    }

    @ApiOperation(value = "启禁用物料报价")
    @PostMapping("/pricing/enable/{guid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "启禁用物料报价",action = OperatorType.ADD)
    public Result<Boolean> enableOrDisablePricingSchemes(@PathVariable("guid") String guid) {
        LOGGER.info("ERP系统聚合层(启禁用物料报价)-> guid:{}", guid);
        return Result.buildSuccessResult(pricingSchemesFeignService.enableOrDisablePricingSchemes(guid));
    }

    @ApiOperation(value = "批量查询物料协议单价")
    @PostMapping("/pricing/batch")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "批量查询物料协议单价",action = OperatorType.SELECT)
    public Result<List<PricingSchemesDTO>> batchQueryPricingSchemesList(@RequestBody PricingSchemesQueryDTO queryDTO) {
        LOGGER.info("ERP系统聚合层(批量查询物料协议单价)-> PricingSchemesQueryDTO:{}", JacksonUtils.writeValueAsString(queryDTO));
        return Result.buildSuccessResult(pricingSchemesFeignService.batchQueryPricingSchemesList(queryDTO));
    }

    @PostMapping("/pricing/findByGuidList")
    @ApiOperation("根据物料GUID列表查询物料信息")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "根据物料GUID列表查询物料信息",action = OperatorType.SELECT)
    public Result<List<MaterialDTO>> findList(@RequestBody MaterialListQuery materialListQuery) {
        LOGGER.info("聚合层根据物料GUID列表查询物料信息入参:->{}", JacksonUtils.writeValueAsString(materialListQuery));
        List<MaterialDTO> materialDTOList = materialFeignService.findList(materialListQuery);
        return Result.buildSuccessResult(materialDTOList);
    }
}
