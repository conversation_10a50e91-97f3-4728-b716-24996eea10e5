package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.WxStoreTradeOrderService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxOrderRecordClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxStorePayClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxStoreTradeOrderClientService;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.*;
import com.holderzone.saas.store.dto.weixin.member.*;
import com.holderzone.saas.store.dto.weixin.req.WxH5PayReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPaidOrderDetailsReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPrepayReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreTradeOrderDetailsRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreUserOrderDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

@Api("微信订单管理")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/wx_store_trade_order")
public class WxStoreTradeOrderController {

    private final WxStoreTradeOrderClientService wxStoreTradeOrderClientService;

    private final WxStorePayClientService wxStorePayClientService;

    private final WxOrderRecordClientService wxOrderRecordClientService;

    private final WxStoreTradeOrderService wxStoreTradeOrderService;

    @ApiOperation("获取订单详情")
    @PostMapping(value = "/table_details")
    public Result<WebSocketMessageDTO> tableOrderDetails(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) throws IOException {
        log.info("订单详情入参{}", JacksonUtils.writeValueAsString(wxStoreAdvanceConsumerReqDTO));
        WebSocketMessageDTO webSocketMessageDTO = wxStoreTradeOrderClientService.tableOrderDetails(wxStoreAdvanceConsumerReqDTO);
        log.info("订单详情数据：{}", webSocketMessageDTO);
        return Result.buildSuccessResult(webSocketMessageDTO);
    }

    @ApiOperation("下单:加菜")
    @PostMapping("/submit")
    public Result<SubmitReturnDTO> submitOrder(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        log.info("wxStoreAdvanceConsumerReqDTO{}:", wxStoreAdvanceConsumerReqDTO);
        return Result.buildSuccessResult(wxStoreTradeOrderClientService.submitOrder(wxStoreAdvanceConsumerReqDTO));
    }

    @ApiOperation(value = "修改整单备注", notes = "修改整单备注")
    @PostMapping("/update_remark")
    public Result<String> updateRemark(@RequestBody CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        return wxStoreTradeOrderClientService.updateRemark(createDineInOrderReqDTO) ? Result.buildSuccessMsg("操作成功") : Result.buildOpFailedResult("操作失败");
    }

    @ApiOperation(value = "修改就餐人数", notes = "修改就餐人数")
    @PostMapping("/update_guest_count")
    public Result<String> updateGuestCount(@RequestBody CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        return wxStoreTradeOrderClientService.updateGuestCount(createDineInOrderReqDTO) ? Result.buildSuccessMsg("操作成功") : Result.buildOpFailedResult("操作失败");
    }

    @ApiOperation("买单")
    @PostMapping(value = "/order_pay")
    public Result<WxStoreTradeOrderDetailsRespDTO> orderPay(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        log.info("买单入参wxStoreAdvanceConsumerReqDTO:{}", wxStoreAdvanceConsumerReqDTO);
        return Result.buildSuccessResult(wxStoreTradeOrderClientService.orderPay(wxStoreAdvanceConsumerReqDTO));
    }

    @ApiOperation("支付")
    @PostMapping(value = "/order_defrey")
    public Result<WxPayRespDTO> orderDefrey(@RequestBody WxH5PayReqDTO wxH5PayReqDTO) {
        log.info("支付入参wxStoreAdvanceConsumerReqDTO:{}", JacksonUtils.writeValueAsString(wxH5PayReqDTO));
        Result<WxPayRespDTO> wxPayRespDTOResult = Result.buildSuccessResult(wxStorePayClientService.weChatPublic(wxH5PayReqDTO));
        log.info("wxPayRespDTOResult:{}", wxPayRespDTOResult);
        return wxPayRespDTOResult;
    }

    @ApiOperation(value = "我的订单记录")
    @PostMapping("/user_order_list")
    public Result<WxStoreUserOrderDTO> getWxStoreUserOrder(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        log.info("入参wxStoreAdvanceConsumerReqDTO:{}", wxStoreAdvanceConsumerReqDTO);
        WxStoreUserOrderDTO wxStoreUserOrder = wxOrderRecordClientService.getWxStoreUserOrder(wxStoreAdvanceConsumerReqDTO);
        return Result.buildSuccessResult(wxStoreUserOrder);
    }

    @ApiOperation("/更新用户订单记录")
    @PostMapping(value = "/update")
    public Result<String> updateWxOrderRecord(@RequestBody WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
        log.info("更新用户订单记录入参:wxStoreAdvanceConsumerReqDTO：{}", wxStoreAdvanceConsumerReqDTO);
        return wxOrderRecordClientService.updateWxOrderRecord(wxStoreAdvanceConsumerReqDTO) ? Result.buildOpFailedResult("操作成功") : Result.buildOpFailedResult("操作失败");
    }

    @ApiOperation("获取会员卡或优惠券优惠")
    @PostMapping(value = "/validate_card")
    public Result<CardAndVolumeDTO> validateCardAndVolume(@RequestBody CardAndVolumeDiscountReqDTO cardAndVolumeDiscountReqDTO) {
        log.info("获取当前会员卡或优惠券的优惠信息:{}", JacksonUtils.writeValueAsString(cardAndVolumeDiscountReqDTO));
        return Result.buildSuccessResult(wxStoreTradeOrderClientService.validateCardAndVolume(cardAndVolumeDiscountReqDTO));
    }


    @ApiOperation("会员卡列表")
    @PostMapping(value = "/member_card_list")
    public Result<WxMemberCardRespDTO> cardList2(@RequestBody WxMemberCardListReqDTO wxMemberCardListReqDTO) {
        log.info("获取用户的会员卡列表:{}", wxMemberCardListReqDTO);
        return Result.buildSuccessResult(wxStoreTradeOrderClientService.cardList2(wxMemberCardListReqDTO));
    }

    @ApiOperation(value = "会员支付", notes = "支付")
    @PostMapping("/member_pay")
    public Result<WxStorePayResultDTO> memberPay(@RequestBody WxMemberPayDTO wxMemberPayDTO) {
        log.info("会员支付入参：{}", JacksonUtils.writeValueAsString(wxMemberPayDTO));
        return Result.buildSuccessResult(wxStorePayClientService.memberPay(wxMemberPayDTO));
    }

    @ApiOperation("获取所有支付方式")
    @PostMapping("/pay_way")
    public Result<WxPayWayRespDTO> getAllPayWay(@RequestBody WxStorePayReqDTO wxStorePayReqDTO) {
        log.info("支付方式入参:{}", JacksonUtils.writeValueAsString(wxStorePayReqDTO));
        return Result.buildSuccessResult(wxStorePayClientService.getAllPayWay(wxStorePayReqDTO));
    }

    @ApiModelProperty(value = "买单支付")
    @PostMapping(value = "/prepay")
    public Result<WxPrepayRespDTO> prepay(@RequestBody WxPrepayReqDTO WxPrepayReqDTO) {
        log.info("买单支付入参:{}", JacksonUtils.writeValueAsString(WxPrepayReqDTO));
        return Result.buildSuccessResult(wxStorePayClientService.prepay(WxPrepayReqDTO));
    }

    @ApiOperation("0元支付")
    @PostMapping("/zero_pay")
    public Result<WxStorePayResultDTO> zeroPay(@RequestBody WxZeroPayReqDTO wxZeroPayReqDTO) {
        log.info("0元支付入参:{}", wxZeroPayReqDTO);
        return Result.buildSuccessResult(wxStorePayClientService.zeroPay(wxZeroPayReqDTO));
    }

    @ApiOperation("优惠券列表2")
    @PostMapping(value = "/member_volume_list")
    public Result<WxVolumeCodeRespDTO> volumeCodeList(@RequestBody WxVolumeCodeReqDTO wxVolumeCodeReqDTO) {
        log.info("优惠券列表:{}", JacksonUtils.writeValueAsString(wxVolumeCodeReqDTO));
        return Result.buildSuccessResult(wxStoreTradeOrderClientService.volumeCodeList2(wxVolumeCodeReqDTO));
    }

    @PostMapping(value = "/validate_order")
    @ApiOperation("订单处理验证")
    public Result<WxPrepayRespDTO> validateOrder(@RequestBody WxStorePayReqDTO wxStorePayReqDTO) {
        log.info("订单验证入参:{}", JacksonUtils.writeValueAsString(wxStorePayReqDTO));
        return Result.buildSuccessResult(wxStorePayClientService.validateOrder(wxStorePayReqDTO));
    }

    @ApiOperation("会员卡与优惠券选择确认")
    @PostMapping(value = "/prepay_confirm")
    public Result<WxPrepayConfirmRespDTO> memberConfirm(@RequestBody WxPrepayConfirmReqDTO wxPrepayConfirmReqDTO) {
        log.info("会员选择确认入参:{}", wxPrepayConfirmReqDTO);
        return Result.buildSuccessResult(wxStorePayClientService.memberConfirm(wxPrepayConfirmReqDTO));
    }

    @ApiOperation("结账后订单详情")
    @PostMapping(value = "/paid_order_details")
    public Result<WebSocketMessageDTO> orderDetails(@RequestBody WxPaidOrderDetailsReqDTO wxPaidOrderDetailsReqDTO) {
        log.info("结账后订单详情入参:{}", JacksonUtils.writeValueAsString(wxPaidOrderDetailsReqDTO));
        return Result.buildSuccessResult(wxStoreTradeOrderClientService.orderDetails(wxPaidOrderDetailsReqDTO));
    }

    @ApiOperation("异常测试")
    @GetMapping(value = "/exception_test")
    public Result<String> exceptionTest() {
        wxStoreTradeOrderClientService.exceptionTest();
        return Result.buildSuccessResult("success");
    }

    @ApiOperation(value = "修改点餐人数", notes = "快餐点餐人数，未下单修改")
    @PutMapping(value = "/update_fast_guest_count/{count}")
    public Result<Void> updateFastGuestCount(@PathVariable Integer count) {
        wxStoreTradeOrderService.updateFastGuestCount(count);
        return Result.buildEmptySuccess();
    }
}

