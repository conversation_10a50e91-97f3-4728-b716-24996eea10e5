package com.holderzone.holder.saas.aggregation.app.controller.trade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.config.ZhuanCanConfig;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInOrderClientService;
import com.holderzone.holder.saas.aggregation.app.utils.HttpsClientUtils;
import com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList;
import com.holderzone.saas.store.dto.order.request.member.IslandRedeemResultDTO;
import com.holderzone.saas.store.dto.order.request.member.MemberCouponListReqDTO;
import com.holderzone.saas.store.dto.order.request.member.RequestRedeemCodeApplyDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberGrouponController
 * @date 2019/01/04 8:53
 * @description 团购接口
 * @program holder-saas-store-trade
 */
@RestController
@RequestMapping("/member")
@Api(tags = "会员优惠券接口")
@Slf4j
public class MemberCouponAppController {

    @Resource
    private DineInOrderClientService dineInOrderClientService;

    @Resource
    private ZhuanCanConfig zhuanCanConfig;

    @ApiOperation(value = "已登陆会员优惠券列表", notes = "已登陆会员优惠券列表")
    @PostMapping("/coupon_list")
    public Result<List<ResponseVolumeList>> couponList(@RequestBody MemberCouponListReqDTO memberCouponListReqDTO) {
        log.info("已登陆会员优惠券列表入参：{}", JacksonUtils.writeValueAsString(memberCouponListReqDTO));

        // pad没有token，手动设置头部信息
        UserContext userContext = UserContextUtils.get();
        userContext.setStoreGuid(memberCouponListReqDTO.getStoreGuid());
        UserContextUtils.put(userContext);

        return Result.buildSuccessResult(dineInOrderClientService.couponList(memberCouponListReqDTO));
    }


    @ApiOperation(value = "一体机优惠券兑换", notes = "一体机优惠券兑换")
    @PostMapping("/coupon_redeem")
    public Result<IslandRedeemResultDTO> couponRedeem(@RequestBody RequestRedeemCodeApplyDTO redeemCodeApplyDTO) {
        IslandRedeemResultDTO islandRedeemResultDTO = new IslandRedeemResultDTO();
        // pad没有token，手动设置头部信息
        UserContext userContext = UserContextUtils.get();
        log.info("userContext={}", JSON.toJSONString(userContext));
        userContext.setEnterpriseGuid(redeemCodeApplyDTO.getEnterpriseGuid());
        userContext.setOperSubjectGuid(redeemCodeApplyDTO.getOperSubjectGuid());
        UserContextUtils.put(userContext);
        redeemCodeApplyDTO.setSource(Integer.valueOf(userContext.getSource()));
        redeemCodeApplyDTO.setStoreGuid(userContext.getStoreGuid());
        redeemCodeApplyDTO.setStoreName(userContext.getStoreName());
        log.info("一体机优惠券兑换入参：{}", JacksonUtils.writeValueAsString(redeemCodeApplyDTO));
        try {
            String zcResponse = HttpsClientUtils.doPost(zhuanCanConfig.getMemberRedeem(), JacksonUtils.writeValueAsString(redeemCodeApplyDTO));

            log.info("一体机优惠券兑换返回：{}", zcResponse);
            if (Objects.isNull(zcResponse)) {
                throw new BusinessException("一体机优惠券兑换调用异常");
            }

            JSONObject jsonObject = JSON.parseObject(zcResponse);
            Object object = jsonObject.get("causeMessage");
            if (Objects.nonNull(object)) {
                islandRedeemResultDTO.setCauseMessage(object.toString());
            }
            String resultState = jsonObject.get("resultState").toString();

            islandRedeemResultDTO.setResultState(resultState);
        } catch (Exception e) {
            log.error("一体机优惠券兑换异常={}", e.getMessage());
        }
        return Result.buildSuccessResult(islandRedeemResultDTO);
    }

}
