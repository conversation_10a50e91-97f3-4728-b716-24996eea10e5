package com.holderzone.holder.saas.aggregation.weixin.service.impl;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.aggregation.weixin.service.WxOpenService;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.constant.OpenApiConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class WxOpenServiceImpl implements WxOpenService {

    private final RedisUtils redisUtils;


    @Override
    public void verifyAccessToken(String enterpriseGuid, String accessToken) {
        String cacheKey = String.format(OpenApiConstant.ACCESS_TOKEN_CACHE_KEY, enterpriseGuid);
        Object accessTokenObj = redisUtils.get(cacheKey);
        if (Objects.isNull(accessTokenObj)) {
            throw new BusinessException("accessToken expire");
        }
        if (!accessToken.equals(accessTokenObj.toString())) {
            throw new BusinessException("accessToken expire");
        }
    }
}
