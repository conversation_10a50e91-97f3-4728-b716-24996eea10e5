package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CheckVerifyVolumeRespDTO
 * @date 2019/12/30
 */
@ApiModel("验证一体机是否已验券")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
public class CheckVerifyVolumeRespDTO {

	@ApiModelProperty("0:无需弹出弱提示，1：需要弹出提示")
	private Integer code=0;

	@ApiModelProperty(value = "提示")
	private String meg;
}
