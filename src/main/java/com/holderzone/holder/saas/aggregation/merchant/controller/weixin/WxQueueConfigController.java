package com.holderzone.holder.saas.aggregation.merchant.controller.weixin;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin.WxQueueConfigClientService;
import com.holderzone.saas.store.dto.weixin.req.WxQueueConfigUpdateBatchReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxQueueConfigDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQueueConfigController
 * @date 2019/05/13 10:33
 * @description 门店微信线上排队配置Controller
 * @program holder-saas-store
 */
@Api(value = "微信线上排队配置", description = "门店微信线上排队配置Controller")
@RestController
@RequestMapping("/wx_queue")
@Slf4j
public class WxQueueConfigController {

    @Autowired
    WxQueueConfigClientService wxQueueConfigClientService;

    @PostMapping("/page_config")
    @ApiOperation(value = "分页查询排队配置")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "分页查询排队配置")
    public Result<Page<WxQueueConfigDTO>> pageQueueConfig(@RequestBody WxStorePageReqDTO wxStorePageReqDTO) {
        log.info("微信排队，分页查询参数：{}", JacksonUtils.writeValueAsString(wxStorePageReqDTO));
        return Result.buildSuccessResult(wxQueueConfigClientService.pageConfig(wxStorePageReqDTO));
    }

    @PostMapping("/get_by_guid")
    @ApiOperation(value = "查询排队配置详情")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "查询排队配置详情")
    public Result<WxQueueConfigDTO> getByGuid(@RequestBody WxQueueConfigDTO wxQueueConfigDTO) {
        log.info("微信排队，通过guid查询参数：{}", wxQueueConfigDTO);
        return Result.buildSuccessResult(wxQueueConfigClientService.getByGuid(wxQueueConfigDTO));
    }

    @PostMapping("/update_by_guid")
    @ApiOperation(value = "通过guid修改排队配置")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "通过guid修改排队配置")
    public Result updateByGuid(@RequestBody WxQueueConfigDTO wxQueueConfigDTO) {
        log.info("微信排队：修改排队配置请求入参：{}", JacksonUtils.writeValueAsString(wxQueueConfigDTO));
        wxQueueConfigClientService.updateByGuid(wxQueueConfigDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/batch_update")
    @ApiOperation(value = "批量修改排队配置")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "批量修改排队配置")
    public Result batchUpdate(@RequestBody WxQueueConfigUpdateBatchReqDTO wxQueueConfigUpdateBatchReqDTO) {
        log.info("微信排队：批量修改排队配置请求入参：{}", JacksonUtils.writeValueAsString(wxQueueConfigUpdateBatchReqDTO));
        wxQueueConfigClientService.batchUpdate(wxQueueConfigUpdateBatchReqDTO);
        return Result.buildEmptySuccess();
    }
}
