package com.holderzone.holder.saas.aggregation.merchant.controller.journaling;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.journaling.ReportClientService;
import com.holderzone.holder.saas.aggregation.merchant.util.ReportValidateUtil;
import com.holderzone.holder.saas.store.client.entity.dto.ReportDetailDTO;
import com.holderzone.saas.store.dto.journaling.req.OrderDetailReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.OrderDetailReportRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.OrderDetailRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderDetailController
 * @date 2019/05/28 17:52
 * @description 报表-订单明细controller
 * @program holder-saas-store
 */

@Api(description = "报表-订单明细controller")
@RestController
@RequestMapping("/order_detail")
@Slf4j
public class OrderDetailController {
    private final ReportClientService reportClientService;

    public OrderDetailController(ReportClientService reportClientService) {
        this.reportClientService = reportClientService;
    }

    @PostMapping("/page")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT, description = "分页查询订单明细报表")
    public Result<Page<OrderDetailRespDTO>> pageOrderDetail(@RequestBody OrderDetailReqDTO orderDetailReqDTO) {
//        orderDetailReqDTO.setBusinessEndDateTime(LocalDateTime.of(orderDetailReqDTO.getEndDate(), LocalTime.MAX));
//        orderDetailReqDTO.setBusinessStartDateTime(LocalDateTime.of(orderDetailReqDTO.getStartDate(), LocalTime.MIN));
        log.info("分页查询订单明细报表请求参数：{}", JacksonUtils.writeValueAsString(orderDetailReqDTO));
        //门店兼容
        ReportValidateUtil.reportCompatibilityCheck(orderDetailReqDTO);
        //时间兼容
        setReqDateTime(orderDetailReqDTO);
        return Result.buildSuccessResult(reportClientService.pageOrderDetail(orderDetailReqDTO));
    }

    @PostMapping("/single_page")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT, description = "分页查询订单明细报表")
    public Result<Page<OrderDetailReportRespDTO>> singlePageOrderDetail(@RequestBody OrderDetailReqDTO orderDetailReqDTO) {
        log.info("分页查询订单明细报表请求参数：{}", JacksonUtils.writeValueAsString(orderDetailReqDTO));
        //门店兼容
        ReportValidateUtil.reportCompatibilityCheck(orderDetailReqDTO);
        //时间兼容
        setReqDateTime(orderDetailReqDTO);
        return Result.buildSuccessResult(reportClientService.singlePageOrderDetail(orderDetailReqDTO));
    }

    @ApiOperation(value = "根据门店guid查询门店历史收款方式")
    @PostMapping("/getPaymentAll")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS, description = "根据门店guid查询门店历史收款方式")
    public Result<Map<String, String>> getPaymentAll(@RequestBody OrderDetailReqDTO orderDetailReqDTO) {
        log.info("分页查询订单明细报表请求参数：{}", JacksonUtils.writeValueAsString(orderDetailReqDTO));
        //门店兼容
        ReportValidateUtil.reportCompatibilityCheck(orderDetailReqDTO);
        //时间兼容
        setReqDateTime(orderDetailReqDTO);
        return Result.buildSuccessResult(reportClientService.getPaymentAll(orderDetailReqDTO));
    }

    private static void setReqDateTime(OrderDetailReqDTO businessSituationReqDTO) {
        if (businessSituationReqDTO.getBusinessEndDateTime() == null) {
            businessSituationReqDTO.setBusinessEndDateTime(businessSituationReqDTO.getEndDate().atTime(LocalTime.MAX));
        }
        if (businessSituationReqDTO.getBusinessStartDateTime() == null) {
            businessSituationReqDTO.setBusinessStartDateTime(businessSituationReqDTO.getStartDate().atTime(LocalTime.MIN));
        }
    }

    @ApiOperation(value = "导出销售、订单、收款明细")
    @PostMapping("/getExportToken")
    public Result exportExcel(@RequestBody ReportDetailDTO reportDetailDTO) throws Exception {
        return Result.buildSuccessResult(reportClientService.findSpecialSaleDetail(reportDetailDTO));
    }


    @ApiOperation(value = "获取销售、订单、收款明细 url")
    @GetMapping("/getUrl")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "token", value = "token", dataType = "String", required = true),
    })
    public Result getRedisDetailData(@RequestParam("token") String token) throws Exception {
        return Result.buildSuccessResult(reportClientService.getRedisDetailData(token));
    }


    @ApiOperation(value = "获取搜索关键字")
    @GetMapping("/getStoreGuidAndStoreName")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keyword", value = "keyword", dataType = "String", required = true),
    })
    public Result getStoreGuidAndStoreName(@RequestParam("keyword") String keyword) throws Exception {
        return Result.buildSuccessResult(reportClientService.getStoreGuidAndStoreName(keyword));

    }
}
