package com.holderzone.holder.saas.aggregation.merchant.controller.takeout;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.takeout.TakeoutProducerService;
import com.holderzone.saas.store.dto.takeaway.EleCallbackBindDTO;
import com.holderzone.saas.store.dto.takeaway.EleCallbackResponse;
import eleme.openapi.sdk.api.entity.other.OMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ElemeController
 * @date 2018/09/12 16:35
 * @description
 * @program holder-saas-aggregation-app
 */
@Slf4j
@RestController
@RequestMapping("/takeout/callback/ele")
@Api(description = "饿了么外卖回调接口")
public class ElemeController {

    private final TakeoutProducerService takeoutProducerService;

    @Autowired
    public ElemeController(TakeoutProducerService takeoutProducerService) {
        this.takeoutProducerService = takeoutProducerService;
    }

    @GetMapping("/bind")
    @ApiOperation(value = "饿了么门店绑定回调", notes = "饿了么门店绑定回调")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY,description = "饿了么门店绑定回调")
    public EleCallbackResponse bindCallback(EleCallbackBindDTO eleCallbackBindDTO) {
        log.info("聚合层饿了么门店绑定");
        if (log.isInfoEnabled()) {
            log.info("饿了么绑定回调入参：{}", JacksonUtils.writeValueAsString(eleCallbackBindDTO));
        }
        return takeoutProducerService.eleBindCallback(eleCallbackBindDTO);
    }

    @PostMapping(value = "/order")
    @ApiOperation(value = "饿了么外卖订单推送", notes = "饿了么外卖订单推送")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY,description = "饿了么外卖订单推送")
    public EleCallbackResponse orderCallback(@RequestBody OMessage omessage) {
        log.info("聚合层饿了么外卖订单");
        if (log.isInfoEnabled()) {
            log.info("饿了么订单回调入参：{}", JacksonUtils.writeValueAsString(omessage));
        }
        return takeoutProducerService.eleOrderCallback(omessage);
    }
}
