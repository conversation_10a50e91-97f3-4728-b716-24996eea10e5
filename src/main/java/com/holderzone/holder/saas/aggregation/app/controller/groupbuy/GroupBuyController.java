package com.holderzone.holder.saas.aggregation.app.controller.groupbuy;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.exception.InterfaceDeprecatedException;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeawayController
 * @date 2018/09/08 9:59
 * @description
 * @program holder-saas-aggregation-app
 */
@Slf4j
@RestController
@RequestMapping("/groupbuy")
@Api(description = "团购接口")
public class GroupBuyController {

    @ApiOperation(value = "结账页面预验券", notes = "结账页面预验券")
    @PostMapping(value = "/pre_check", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<?> preCheck(@RequestBody CouPonPreReqDTO couPonPreReqDTO) {
        log.info("结账页面预验券入参：{}", JacksonUtils.writeValueAsString(couPonPreReqDTO));
        throw new InterfaceDeprecatedException();
    }

}
