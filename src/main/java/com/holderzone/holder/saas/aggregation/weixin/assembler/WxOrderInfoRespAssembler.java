package com.holderzone.holder.saas.aggregation.weixin.assembler;

import com.holderzone.saas.store.dto.trade.OrderInfoRespDTO;
import com.holderzone.saas.store.dto.weixin.WxOrderInfoRespDTO;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

public class WxOrderInfoRespAssembler {

    private WxOrderInfoRespAssembler() {
    }

    public static List<WxOrderInfoRespDTO> copyOrderInfoResp(List<OrderInfoRespDTO> orderInfoRespDTOList) {
        return orderInfoRespDTOList.stream().map(e -> {
            WxOrderInfoRespDTO wxOrderInfoRespDTO = new WxOrderInfoRespDTO();
            BeanUtils.copyProperties(e, wxOrderInfoRespDTO);
            List<WxOrderInfoRespDTO.InnerItem> innerItems = e.getItemList().stream().map(item -> {
                WxOrderInfoRespDTO.InnerItem innerItem = new WxOrderInfoRespDTO.InnerItem();
                BeanUtils.copyProperties(item, innerItem);
                return innerItem;
            }).collect(Collectors.toList());
            wxOrderInfoRespDTO.setOrderItemList(innerItems);
            return wxOrderInfoRespDTO;
        }).collect(Collectors.toList());
    }

}
