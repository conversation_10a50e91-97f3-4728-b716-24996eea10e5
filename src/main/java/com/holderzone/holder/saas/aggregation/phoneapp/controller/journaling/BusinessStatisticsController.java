package com.holderzone.holder.saas.aggregation.phoneapp.controller.journaling;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.phoneapp.service.rpc.JournalRpcService;
import com.holderzone.saas.store.dto.journaling.req.BusinessSituationReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.BusinessDataRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.BusinessHisTrendRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.TodayBusinessSituationRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessStatisticsController
 * @date 2019/06/03 10:55
 * @description 营业统计
 * @program holder-saas-aggregation-phoneapp
 */
@RestController
@Api(value = "app报表-营业统计",description = "app报表-营业统计")
@RequestMapping("/businessStatistics")
@Slf4j
public class BusinessStatisticsController {

    @Autowired
    private JournalRpcService journalRpcService;

    @PostMapping("/businessData")
    @ApiOperation("营业统计-营业统计（企业、门店适用）")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT,description = "营业统计-营业统计（企业、门店适用）")
    public Result<BusinessDataRespDTO> businessData(@RequestBody BusinessSituationReqDTO businessSituationReqDTO){
        log.info("app营业统计-营业统计-营业数据，入参businessSituationReqDTO={}", JacksonUtils.writeValueAsString(businessSituationReqDTO));
        //app调用 获取营业数据（订单金额）
        businessSituationReqDTO.setDataSource("1");
        return Result.buildSuccessResult(journalRpcService.businessData(businessSituationReqDTO));
    }

    @PostMapping("/busiHisTrend")
    @ApiOperation("营业统计-历史趋势（企业、门店适用）")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT,description = "营业统计-历史趋势（企业、门店适用）")
    public Result<BusinessHisTrendRespDTO> hisTrend(@RequestBody BusinessSituationReqDTO businessSituationReqDTO){
        log.info("app营业统计-营业统计-历史趋势，入参businessSituationReqDTO={}", JacksonUtils.writeValueAsString(businessSituationReqDTO));
        //app调用 获取营业数据（订单金额）
        businessSituationReqDTO.setDataSource("1");
        return Result.buildSuccessResult(journalRpcService.hisTrend(businessSituationReqDTO));
    }

    @PostMapping("/todayBusinessSituation")
    @ApiOperation("首页-今日营业概况（企业适用）")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT,description = "首页-今日营业概况（企业适用）")
    public Result<TodayBusinessSituationRespDTO> todayBusinessSituation(@RequestBody BusinessSituationReqDTO businessSituationReqDTO){
        log.info("app首页-今日营业概况，入参businessSituationReqDTO={}", JacksonUtils.writeValueAsString(businessSituationReqDTO));
        return Result.buildSuccessResult(journalRpcService.todayBusinessSituation(businessSituationReqDTO));
    }

}
