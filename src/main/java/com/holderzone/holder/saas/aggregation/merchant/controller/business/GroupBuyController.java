package com.holderzone.holder.saas.aggregation.merchant.controller.business;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.business.GroupBuyClientService;
import com.holderzone.saas.store.dto.business.group.StoreBindDTO;
import com.holderzone.saas.store.enums.locale.LocaleMessageEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 */
@Api(value = "团购管理接口")
@RestController
@RequestMapping("/group")
public class GroupBuyController {

    private static final Logger logger = LoggerFactory.getLogger(GroupBuyController.class);

    private final GroupBuyClientService groupBuyClientService;

    @Autowired
    public GroupBuyController(GroupBuyClientService groupBuyClientService) {
        this.groupBuyClientService = groupBuyClientService;
    }

    @ApiOperation(value = "团购平台门店绑定")
    @PostMapping("/store/bind")
    public Result<?> bindStore(@RequestBody @Validated StoreBindDTO storeBind) {
        logger.info("团购平台门店绑定 storeBind：{},操作人：{}", JacksonUtils.writeValueAsString(storeBind), UserContextUtils.getUserGuid());
        Result<?> result = groupBuyClientService.bindStore(storeBind);
        if(result.getCode() == -1){
            result.setMessage(LocaleMessageEnum.getLocale(result.getMessage()));
        }
        return result;
    }

    @ApiOperation(value = "团购平台门店解绑")
    @PostMapping("/store/unbind")
    public Result<?> unbindStore(@RequestBody @Validated StoreBindDTO storeBind) {
        logger.info("团购平台门店解绑 storeBind：{},操作人：{}", JacksonUtils.writeValueAsString(storeBind), UserContextUtils.getUserGuid());
        Result<?> result = groupBuyClientService.unbindStore(storeBind);
        if(result.getCode() == -1){
            result.setMessage(LocaleMessageEnum.getLocale(result.getMessage()));
        }
        return result;
    }

}
