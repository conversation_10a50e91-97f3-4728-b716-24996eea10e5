package com.holderzone.holder.saas.aggregation.app.controller.common;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.staff.UserClientService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.user.LocalUserInfoDTO;
import com.holderzone.saas.store.dto.user.LocalUserReqDTO;
import com.holderzone.saas.store.dto.user.MenuSourceDTO;
import com.holderzone.saas.store.dto.user.UserAuthorityQueryDTO;
import com.holderzone.saas.store.dto.user.UserDTO;
import com.holderzone.saas.store.dto.user.resp.PermissionsRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className MenuController
 * @date 19-2-11 下午5:58
 * @description
 * @program holder-saas-store-staff
 */
@Slf4j
@RestController
@RequestMapping("/menu")
@Api(tags = "一体机权限相关接口")
public class UserController {

    private final UserClientService userClientService;

    @Autowired
    public UserController(UserClientService userClientService) {
        this.userClientService = userClientService;
    }

    @ApiOperation(value = "根据终端Code获取该终端下的资源信息")
    @PostMapping("/get_source_by_user")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "根据终端Code获取该终端下的资源信息",action = OperatorType.SELECT)
    public Result<List<MenuSourceDTO>> getSourceByUser(@ApiParam("业务请求参数为终端Code") @RequestBody SingleDataDTO singleDataDTO) {
        List<MenuSourceDTO> result = userClientService.getSourceByUser(singleDataDTO.getData());
        log.info("聚合层根据终端code获取终端下的资源信息接口，入参：{}，返回值：{}", "终端code为：" + singleDataDTO.getData(),
                JacksonUtils.writeValueAsString(result));
        return Result.buildSuccessResult(result);
    }

    @ApiOperation(value = "根据终端Code获取该终端下的模块资源信息")
    @PostMapping("/get_module_source_by_user")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "根据终端Code获取该终端下的模块资源信息",action = OperatorType.SELECT)
    public Result<List<MenuSourceDTO>> getModuleSourceByUser(@ApiParam("业务请求参数为终端Code") @RequestBody SingleDataDTO singleDataDTO) {
        List<MenuSourceDTO> result = userClientService.getModuleSourceByUser(singleDataDTO.getData());
        log.info("聚合层根据终端Code获取该终端下的模块资源信息，入参：{}，返回值：{}", "终端code为：" + singleDataDTO.getData(),
                JacksonUtils.writeValueAsString(result));
        return Result.buildSuccessResult(result);
    }

    @ApiOperation(value = "查询用户整单折扣、整单让价阈值", notes = "查询用户整单折扣、整单让价阈值")
    @PostMapping(value = "/query_data_threshold")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "查询用户整单折扣、整单让价阈值",action = OperatorType.SELECT)
    public Result<UserDTO> queryUserDataThreshold() {
        UserDTO data = userClientService.queryUserDataThreshold();
        log.info("用户阈值查询结果：{}", JacksonUtils.writeValueAsString(data));
        return Result.buildSuccessResult(data);
    }

    @ApiOperation(value = "查询AIO用户信息")
    @PostMapping("/query_aio_users")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "聚合层查询AIO用户数据",action = OperatorType.SELECT)
    public Result<List<UserDTO>> queryAIOUsers(@RequestBody BaseDTO baseDTO) {
        log.info("聚合层查询AIO用户数据请求入参：{}", JacksonUtils.writeValueAsString(baseDTO));
        return Result.buildSuccessResult(userClientService.queryAIOUsers(baseDTO));
    }

    @ApiModelProperty(value = "本地化用户数据")
    @PostMapping(value = "/local_user_info")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF, description = "本地化用户数据",action = OperatorType.SELECT)
    public Result<List<LocalUserInfoDTO>> queryAllUserToLocal(@Validated @RequestBody LocalUserReqDTO localUserReqDTO) {
        log.info("聚合层查询本地化用户数据请求入参：{}", JacksonUtils.writeValueAsString(localUserReqDTO));
        return Result.buildSuccessResult(userClientService.queryAllUserToLocal(localUserReqDTO));
    }

    @ApiOperation(value = "外部获取员工权限")
    @GetMapping("/get_source_code_on_out")
    public Result<List<String>> queryUserSourceOnOut(@NotNull(message = "终端code不能为空") @RequestParam("terminalCode") Integer terminalCode,
                                                     @NotNull(message = "用户或手机号不能为空") @RequestParam("userOrPhone") String userOrPhone,
                                                     @NotNull(message = "企业guid不能为空") @RequestParam("enterpriseGuid") String enterpriseGuid) {
        log.info("外部获取员工权限,terminalCode={},userGuid={},enterpriseGuid={}", terminalCode, userOrPhone, enterpriseGuid);
        return Result.buildSuccessResult(userClientService.queryUserSourceOnOut(terminalCode, userOrPhone, enterpriseGuid));
    }

    @ApiOperation(value = "查询门店下是否有员工勾选自己设置的权限")
    @PostMapping(value = "/query_authority/any_match")
    public Result<List<PermissionsRespDTO>> queryAuthorityAnyMatch(@RequestBody @Validated UserAuthorityQueryDTO UserAuthorityQueryDTO) {
        log.info("查询门店下是否有员工勾选自己设置的权限,UserAuthorityQueryDTO={}", JacksonUtils.writeValueAsString(UserAuthorityQueryDTO));
        return Result.buildSuccessResult(userClientService.queryAuthorityAnyMatch(UserAuthorityQueryDTO));
    }
}
