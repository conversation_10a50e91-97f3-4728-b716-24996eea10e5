package com.holderzone.holder.saas.aggregation.phoneapp.controller.journaling;


import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.phoneapp.service.rpc.JournalRpcService;
import com.holderzone.saas.store.dto.journaling.req.SaleCountReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.SaleCountRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.SaleRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@Api("报表 - 销售")
@RestController
@Slf4j
public class SaleCountController {

    @Autowired
    private JournalRpcService journalRpcService;

    @PostMapping("/saleCount")
    @ApiOperation("销售报表")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT,description = "销售报表")
    public Result<SaleRespDTO> saleCountByEnterprise(@Valid @RequestBody SaleCountReqDTO saleCountReqDTO) {
        log.info("销售报表入参,{}", saleCountReqDTO);
        return Result.buildSuccessResult(journalRpcService.saleCount(saleCountReqDTO));
    }

}
