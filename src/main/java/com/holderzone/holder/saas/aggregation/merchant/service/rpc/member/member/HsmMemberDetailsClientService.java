package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.HsmMemberDetailsClientService.MemberDetailsClientServiceFallback;
import com.holderzone.holder.saas.member.dto.account.request.MemberGiftCardReqDTO;
import com.holderzone.holder.saas.member.dto.account.request.MemberInfoUpdateReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberBaseInfoRespDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberBaseStatisticsRespDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberSystemCardListRespDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberSystemListRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberDetailsClientService
 * @date 2019/05/30 14:22
 * @description 会员详情
 * @program holder-saas-member-account
 */
@Component
@FeignClient(name = "holder-saas-member-account", fallbackFactory = MemberDetailsClientServiceFallback.class)
public interface HsmMemberDetailsClientService {


    /**
     * 获取会员基础信息
     *
     * @param memberGuid 会员guid
     * @return 会员基础信息
     */
    @RequestMapping(value = "/hsm_member/getOne", produces = "application/json;charset=utf-8", method = RequestMethod.GET)
    @ApiOperation("获取会员基础信息")
    MemberBaseInfoRespDTO getOne(@RequestParam(value = "memberGuid") String memberGuid);


    /**
     * 统计基础会员的优惠券数量和消费总额 等数据
     *
     * @param memberGuid 会员guid
     * @return 查询结果
     */
    @RequestMapping(value = "/hsm_member/statisticsBaseData", produces = "application/json;charset=utf-8", method = RequestMethod.GET)
    MemberBaseStatisticsRespDTO statisticsBaseData(
            @RequestParam(value = "memberGuid") String memberGuid);


    /**
     * 通过会员guid查询会员体系
     *
     * @return 会员体系
     */
    @RequestMapping(value = "/hsm_member/listMemberSystemByMemberGuid", produces = "application/json;charset=utf-8", method = RequestMethod.GET)
    List<MemberSystemListRespDTO> listMemberSystemByMemberGuid(
            @RequestParam(value = "memberGuid") String memberGuid);


    /**
     * 通过会员体系guid查询会员体系下卡的信息
     *
     * @param memberSystemGuid 会员体系guid
     * @return 查询结果
     */
    @RequestMapping(value = "/hsm_member/listCardBySystemGuid", produces = "application/json;charset=utf-8", method = RequestMethod.GET)
    List<MemberSystemCardListRespDTO> listCardBySystemGuid(@RequestParam("memberSystemGuid")
                                                                   String memberSystemGuid);


    /**
     * 赠送卡
     *
     * @param giftCardReqDTO 送卡请求参数
     * @return 操作结果
     */
    @RequestMapping(value = "/hsm_member/giftCard", produces = "application/json;charset=utf-8", method = RequestMethod.POST)
    boolean giftCard(MemberGiftCardReqDTO giftCardReqDTO);


    /**
     * 更新会员信息
     *
     * @param updateReqDTO 会员信息
     * @return 更新结果
     */
    @PostMapping(value = "/hsm_member/update", produces = "application/json;charset=utf-8")
    boolean update(MemberInfoUpdateReqDTO updateReqDTO);

    /**
     * 通过会员Guid查询会员拥有卡名称
     *
     * @param memberInfoGuid
     * @return
     */
    @GetMapping(value = "/hsm_member/getCardNameByMember", produces = "application/json;charset=utf-8")
    List<MemberSystemCardListRespDTO> getCardNameByMember(@RequestParam(value = "memberInfoGuid") String memberInfoGuid);

    /**
     * 重置密码
     *
     * @param memberGuid 会员id
     */
    @GetMapping(value = "/hsm_member/resetPassword", produces = "application/json;charset=utf-8")
    Boolean resetPassword(@RequestParam("memberGuid") String memberGuid);

    @Component
    class MemberDetailsClientServiceFallback implements
            FallbackFactory<HsmMemberDetailsClientService> {

        private static final Logger LOGGER = LoggerFactory
                .getLogger(MemberDetailsClientServiceFallback.class);

        @Override
        public HsmMemberDetailsClientService create(Throwable throwable) {
            return new HsmMemberDetailsClientService() {
                @Override
                public MemberBaseInfoRespDTO getOne(String memberGuid) {
                    LOGGER.error("获取会员基础信息错误：{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public MemberBaseStatisticsRespDTO statisticsBaseData(String memberGuid) {
                    LOGGER.error("获取会员基础统计错误：{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<MemberSystemListRespDTO> listMemberSystemByMemberGuid(
                        String memberGuid) {
                    LOGGER.error("获取会员拥有体系错误：{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<MemberSystemCardListRespDTO> listCardBySystemGuid(
                        String memberSystemGuid) {
                    LOGGER.error("查询会员体系持卡信息错误：{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public boolean giftCard(MemberGiftCardReqDTO giftCardReqDTO) {
                    LOGGER.error("会员赠送卡错误：{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public boolean update(MemberInfoUpdateReqDTO updateReqDTO) {
                    LOGGER.error("会员更新错误：{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<MemberSystemCardListRespDTO> getCardNameByMember(String memberInfoGuid) {
                    LOGGER.error("查询会员卡名称错误：{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public Boolean resetPassword(String memberGuid) {
                    LOGGER.error("会员充值密码错误：{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }
}
