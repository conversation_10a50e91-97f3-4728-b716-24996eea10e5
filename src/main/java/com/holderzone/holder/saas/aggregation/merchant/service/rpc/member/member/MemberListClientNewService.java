package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member;

import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.member.dto.account.request.MemberGiftCardReqDTO;
import com.holderzone.holder.saas.member.dto.account.request.MemberListQueryReqDTO;
import com.holderzone.holder.saas.member.dto.account.request.MemberSaveReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.*;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberListClientService
 * @date 2018/09/26 15:49
 * @description //TODO
 * @program holder-saas-store-member
 */

@Component
@FeignClient(name = "holder-saas-member-account", fallbackFactory = MemberListClientNewService.MemberListNewServiceFallBack.class)
public interface MemberListClientNewService {

    /**
     * 根据条件分页查询
     *
     * @param queryDTO 查询条件
     * @return 分页查询结果
     */
    @PostMapping(value = "/hsm_member/listByCondition", produces = "application/json;charset=utf-8")
    public Page<MemberListRespDTO> listByCondition(@RequestBody MemberListQueryReqDTO queryDTO);

    /**
     * 保存会员信息
     *
     * @param saveReqDTO 需要保存的会员信息
     * @return 保存结果
     */
    @PostMapping(value = "/hsm_member/save", produces = "application/json;charset=utf-8")
    public boolean save(@RequestBody MemberSaveReqDTO saveReqDTO);

    /**
     * 获取会员基础信息
     *
     * @param memberGuid 会员guid
     * @return 会员基础信息
     */
    @RequestMapping(value = "/hsm_member/getOne", produces = "application/json;charset=utf-8")
    MemberBaseInfoRespDTO getOne(@RequestParam(value = "memberGuid") String memberGuid);

    /**
     * 统计基础会员的优惠券数量和消费总额 等数据
     *
     * @param memberGuid 会员guid
     * @return 查询结果
     */
    @RequestMapping(value = "/hsm_member/statisticsBaseData", produces = "application/json;charset=utf-8")
    MemberBaseStatisticsRespDTO statisticsBaseData(@RequestParam(value = "memberGuid") String memberGuid);

    /**
     * 启用
     *
     * @param memberGuid 会员guid
     * @return true操作成功 false操作失败
     */
    @RequestMapping(value = "/hsm_member/usable", produces = "application/json;charset=utf-8")
    boolean usable(@RequestParam(value = "memberGuid") String memberGuid,
                   @RequestParam(value = "reasonForEnable") String reasonForEnable);

    /**
     * 禁用
     *
     * @param memberGuid 会员guid
     * @param reason     禁用原因
     * @return true操作成功 false操作失败
     */
    @RequestMapping(value = "/hsm_member/disable", produces = "application/json;charset=utf-8")
    boolean disable(@RequestParam(value = "memberGuid") String memberGuid,
                    @RequestParam(value = "reason") String reason);

    /**
     * 删除会员激活的体系
     *
     * @param memberGuid 会员guid
     * @return 删除结果
     */
    @RequestMapping(value = "/hsm_member/delete", produces = "application/json;charset=utf-8")
    boolean delete(@RequestParam(value = "memberGuid") String memberGuid, @RequestParam(value = "reasonForDel") String reasonForDel);

    /**
     * 通过会员guid查询会员体系
     *
     * @return 会员体系
     */
    @RequestMapping(value = "/hsm_member/listMemberSystemByMemberGuid", produces = "application/json;charset=utf-8")
    List<MemberSystemListRespDTO> listMemberSystemByMemberGuid(@RequestParam(value = "memberGuid") String memberGuid);

    /**
     * 通过会员体系guid查询会员体系下卡的信息
     *
     * @param memberSystemGuid 会员体系guid
     * @return 查询结果
     */
    @RequestMapping(value = "/hsm_member/listCardBySystemGuid", produces = "application/json;charset=utf-8")
    List<MemberSystemCardListRespDTO> listCardBySystemGuid(@RequestParam("memberSystemGuid") String memberSystemGuid);

    /**
     * 赠送卡
     *
     * @param giftCardReqDTO 送卡请求参数
     * @return 操作结果
     */
    @RequestMapping(value = "/hsm_member/giftCard", produces = "application/json;charset=utf-8")
    boolean giftCard(MemberGiftCardReqDTO giftCardReqDTO);

    /**
     * 会员导入
     *
     * @param fileUrl excel Url地址
     * @return 操作结果
     */
    @RequestMapping(value = "/hsm_member/memberUploadExcelUrl", produces = "application/json;charset=utf-8")
    HsmMemberUploadExcelDTO memberUploadExcelUrl(@RequestParam("fileUrl") String fileUrl);

    /**
     * 会员模板下载
     */
    @RequestMapping(value = "/hsm_member/downloadExcelUrl", produces = "application/json;charset=utf-8")
    String downloadExcelUrl();

    @Component
    class MemberListNewServiceFallBack implements FallbackFactory<MemberListClientNewService> {

        private static final Logger logger = LoggerFactory.getLogger(MemberListNewServiceFallBack.class);

        @Override
        public MemberListClientNewService create(Throwable throwable) {
            return new MemberListClientNewService() {

                @Override
                public Page<MemberListRespDTO> listByCondition(MemberListQueryReqDTO queryDTO) {
                    logger.error("会员列表失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public boolean save(MemberSaveReqDTO saveReqDTO) {
                    logger.error("会员保存失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public MemberBaseInfoRespDTO getOne(String memberGuid) {
                    logger.error("操作失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public MemberBaseStatisticsRespDTO statisticsBaseData(String memberGuid) {
                    logger.error("操作失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public boolean disable(String memberGuid, String reason) {
                    logger.error("操作失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }


                @Override
                public List<MemberSystemListRespDTO> listMemberSystemByMemberGuid(String memberGuid) {
                    logger.error("操作失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public List<MemberSystemCardListRespDTO> listCardBySystemGuid(String memberSystemGuid) {
                    logger.error("操作失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public boolean giftCard(MemberGiftCardReqDTO giftCardReqDTO) {
                    logger.error("操作失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public boolean usable(String memberGuid, String reasonForEnable) {
                    logger.error("操作失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public boolean delete(String memberGuid, String reasonForDel) {
                    logger.error("操作失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public HsmMemberUploadExcelDTO memberUploadExcelUrl(String fileUrl) {
                    logger.error("操作失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public String downloadExcelUrl() {
                    logger.error("操作失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

            };
        }
    }
}
