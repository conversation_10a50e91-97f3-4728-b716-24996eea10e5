package com.holderzone.holder.saas.aggregation.app.service.feign.kds;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.kds.req.DstItemQueryReqDTO;
import com.holderzone.saas.store.dto.kds.req.ItemGroupReqDTO;
import com.holderzone.saas.store.dto.kds.req.PrdPointItemQueryReqDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayRepeatItemRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DstTypeBindRespDTO;
import com.holderzone.saas.store.dto.kds.resp.ItemGroupRespDTO;
import com.holderzone.saas.store.dto.kds.resp.PointTypeBindRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Component
@FeignClient(name = "holder-saas-store-kds", fallbackFactory = DeviceBindItemGroupRpcService.FallbackFactoryImpl.class)
public interface DeviceBindItemGroupRpcService {

    @GetMapping("/display_repeat_item/query_by_store")
    DisplayRepeatItemRespDTO queryRepeatItemByStore(@RequestParam("storeGuid") String storeGuid);

    /**
     * 菜品分组列表
     */
    @PostMapping("/device_bind_item_group/group/list")
    List<ItemGroupRespDTO> queryGroupList(@RequestBody PrdPointItemQueryReqDTO queryReqDTO);

    /**
     * 菜品分组列表(当前设备已绑定)
     */
    @PostMapping("/device_bind_item_group/group/myself/list")
    List<ItemGroupRespDTO> queryMyselfGroupList(@RequestBody PrdPointItemQueryReqDTO queryReqDTO);

    /**
     * 新增菜品分组
     */
    @PostMapping("/device_bind_item_group/group/save")
    void saveGroup(@RequestBody @Validated ItemGroupReqDTO reqDTO);

    /**
     * 编辑菜品分组(菜品新增)
     */
    @PostMapping("/device_bind_item_group/group/update")
    void updateGroup(@RequestBody @Validated ItemGroupReqDTO reqDTO);

    /**
     * 删除菜品分组
     */
    @DeleteMapping("/device_bind_item_group/group/remove/{groupGuid}")
    void removeGroup(@PathVariable("groupGuid") String groupGuid);

    /**
     * 删除菜品分组下绑定商品
     */
    @DeleteMapping("/device_bind_item_group/group/remove/{groupGuid}/{skuGuid}")
    void removeGroupItem(@PathVariable("groupGuid") String groupGuid, @PathVariable("skuGuid") String skuGuid);

    /**
     * 查询KDS制作口绑定商品列表
     */
    @PostMapping("/device_bind_item_group/prd/item/list")
    List<PointTypeBindRespDTO> queryPrdBindItem(@RequestBody PrdPointItemQueryReqDTO prdPointItemQueryReqDTO);

    /**
     * 查询KDS出堂口绑定商品列表
     */
    @PostMapping("/device_bind_item_group/dst/item/list")
    List<DstTypeBindRespDTO> queryDstBindItem(@RequestBody DstItemQueryReqDTO dstItemQueryReqDTO);


    @Slf4j
    @Component
    class FallbackFactoryImpl implements FallbackFactory<DeviceBindItemGroupRpcService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public DeviceBindItemGroupRpcService create(Throwable throwable) {

            return new DeviceBindItemGroupRpcService() {

                @Override
                public DisplayRepeatItemRespDTO queryRepeatItemByStore(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "queryRepeatItemByStore",
                            storeGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<ItemGroupRespDTO> queryGroupList(@RequestBody PrdPointItemQueryReqDTO queryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryGroupList",
                            JacksonUtils.writeValueAsString(queryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<ItemGroupRespDTO> queryMyselfGroupList(PrdPointItemQueryReqDTO queryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryMyselfGroupList",
                            JacksonUtils.writeValueAsString(queryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void saveGroup(ItemGroupReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "saveGroup",
                            JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void updateGroup(ItemGroupReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "updateGroup",
                            JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void removeGroup(String guid) {
                    log.error(HYSTRIX_PATTERN, "removeGroup",
                            guid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void removeGroupItem(String groupGuid, String skuGuid) {
                    log.error(HYSTRIX_PATTERN, "removeGroupItem",
                            groupGuid + "-" + skuGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PointTypeBindRespDTO> queryPrdBindItem(PrdPointItemQueryReqDTO prdPointItemQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryPrdBindItem",
                            JacksonUtils.writeValueAsString(prdPointItemQueryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<DstTypeBindRespDTO> queryDstBindItem(DstItemQueryReqDTO dstItemQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryDstBindItem",
                            JacksonUtils.writeValueAsString(dstItemQueryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
