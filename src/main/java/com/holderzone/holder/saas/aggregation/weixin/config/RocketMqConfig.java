package com.holderzone.holder.saas.aggregation.weixin.config;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className RocketMqConfig
 * @date 2019/5/16
 */
public interface RocketMqConfig {

	String WX_STORE_SOCKET_MESSAGE = "table-store-socket-message";

	String WX_STORE_SOCKET_MESSAGE_GROUP = "table-store-socket-message-group";

	/**
	 * 商品变更
	 */
	String ITEM_UPDATE_TOPIC = "itemUpdateTopic";

	String ITEM_UPDATE_TAG = "itemUpdateTag";

	String ITEM_UPDATE_CONSUME_GROUP = "itemUpdateConsumeGroup";
}
