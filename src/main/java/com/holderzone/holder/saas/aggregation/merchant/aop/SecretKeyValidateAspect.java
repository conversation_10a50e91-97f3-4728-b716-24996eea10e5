package com.holderzone.holder.saas.aggregation.merchant.aop;

import com.holderzone.saas.store.dto.reserve.HWDTO;
import com.holderzone.saas.store.dto.reserve.KeyDTO;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableAop
 * @date 2019/01/17 10:08
 * @description
 * @program holder-saas-aggregation-app
 */
@Aspect
@Component
@Slf4j
public class SecretKeyValidateAspect {

    @Pointcut("@annotation(com.holderzone.holder.saas.aggregation.merchant.aop.annotation.SecuretKeyValidate)")
    public void securetKeyValidateCut() {

    }
    @Value("${secret.key}")
    private String key;

    @Around("securetKeyValidateCut()")
    public Object whetherLocked(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        if (!Arrays.stream(args).allMatch(e -> e instanceof HWDTO)) {
            return joinPoint.proceed();
        }
        try {
            HWDTO hwdto = (HWDTO) Arrays.stream(args).filter(s -> s instanceof HWDTO).findFirst().get();
            if(!key.equals(Optional.ofNullable(hwdto).map(HWDTO::getParams).map(e-> ((KeyDTO) e).getKey()).orElse(null))){
                return new com.holderzone.saas.store.dto.reserve.Result(1,"验签失败");
            }
            return joinPoint.proceed();
        } catch (Throwable throwable) {
            throw new RuntimeException(throwable);
        }
    }
}
