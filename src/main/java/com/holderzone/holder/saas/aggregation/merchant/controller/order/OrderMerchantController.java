package com.holderzone.holder.saas.aggregation.merchant.controller.order;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
/*import com.holderzone.holder.saas.aggregation.merchant.service.rpc.log.LogFeignService;*/
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.order.OrderClientService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.request.OrderLogListReqDTO;
import com.holderzone.saas.store.dto.order.response.OrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.OrderLogListRespDTO;
import com.holderzone.saas.store.dto.order.response.OrderSourceRespDTO;
import com.holderzone.saas.store.dto.order.response.OrderTradeModeRespDTO;
import com.holderzone.saas.store.dto.orderlog.request.OrderLogDetailReqDTO;
import com.holderzone.saas.store.dto.orderlog.response.OrderLogDetailRespDTO;
import com.holderzone.saas.store.dto.orderlog.response.TakeawayOrderLogDetailRespDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.order.OrderTradeModeEnum;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderMerchantController
 * @date 2018/10/08 15:22
 * @description //TODO
 * @program holder-saas-store-order
 */
@RestController
@RequestMapping("/order")
@Api(description = "web端订单数据接口")
public class OrderMerchantController {

    @Autowired
    private OrderClientService orderClientService;
  /*  @Autowired
    private LogFeignService logFeignService;*/

    @ApiOperation(value = "查询销售类型", notes = "查询销售类型")
    @ApiImplicitParam(name = "baseDTO", value = "查询销售类型", required = true, dataType =
            "BaseDTO")
    @PostMapping(value = "/getMemberIntegralType", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_OTHER,description = "查询销售类型")
    Result<List<OrderTradeModeRespDTO>> getMemberType(@RequestBody BaseDTO baseDTO) {
        List<OrderTradeModeRespDTO> mp = new ArrayList<>();

        for (TradeModeEnum c : TradeModeEnum.values()) {
            OrderTradeModeRespDTO orderTradeModeRespDTO = new OrderTradeModeRespDTO();
            orderTradeModeRespDTO.setCode(c.getCode());
            orderTradeModeRespDTO.setDesc(c.getDesc());
            mp.add(orderTradeModeRespDTO);
        }
        return Result.buildSuccessResult(mp);
    }


    @ApiOperation(value = "查询订单日志列表页", notes = "查询订单日志列表页")
    @ApiImplicitParam(name = "orderLogListReqDTO", value = "查询订单日志列表页", required = true, dataType =
            "OrderLogListReqDTO")
    @PostMapping(value = "/getOrderLog", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_OTHER,description = "查询订单日志列表页")
    Result<Page<OrderLogListRespDTO>> getMemberType(@RequestBody OrderLogListReqDTO orderLogListReqDTO) {
        Page<OrderLogListRespDTO> orderLogList = orderClientService.getOrderLogList(orderLogListReqDTO);
        return Result.buildSuccessResult(orderLogList);
    }

    @ApiOperation(value = "查询普通订单日志详情", notes = "查询普通订单日志详情")
    @ApiImplicitParam(name = "orderLogListReqDTO", value = "查询普通订单日志详情", required = true, dataType =
            "OrderLogDetailReqDTO")
    @PostMapping(value = "/getNormalOrderLogDetail", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_OTHER,description = "查询普通订单日志详情")
    Result<OrderLogDetailRespDTO> orderLogDetailResp(@RequestBody OrderLogDetailReqDTO orderLogDetailReqDTO) {
        return Result.buildSuccessResult(orderClientService.getNormalOrderLogDetail(orderLogDetailReqDTO));
    }

    @ApiOperation(value = "查询外卖订单日志详情", notes = "查询外卖订单日志详情")
    @ApiImplicitParam(name = "orderLogListReqDTO", value = "查询外卖订单日志详情", required = true, dataType =
            "OrderLogDetailReqDTO")
    @PostMapping(value = "/getTakeAwayOrderLogDetail", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_OTHER,description = "查询外卖订单日志详情")
    Result<TakeawayOrderLogDetailRespDTO> takeawayOrderLogDetailResp(@RequestBody OrderLogDetailReqDTO
                                                                             orderLogDetailReqDTO) {
        return Result.buildSuccessResult(orderClientService.getTakeawayOrderLogDetail(orderLogDetailReqDTO));
    }


    @ApiOperation(value = "查询订单来源", notes = "查询订单来源")
    @ApiImplicitParam(name = "baseDTO", value = "查询订单来源", required = true, dataType =
            "BaseDTO")
    @PostMapping(value = "/getOrderSource", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_OTHER,description = "查询订单来源")
    Result<List<OrderSourceRespDTO>> getOrderSource(@RequestBody BaseDTO baseDTO) {
        List<OrderSourceRespDTO> mp = new ArrayList<>();

        for (BaseDeviceTypeEnum d : BaseDeviceTypeEnum.values()) {
            if (d.equals(BaseDeviceTypeEnum.All_IN_ONE)) {
                OrderSourceRespDTO orderSourceRespDTO = new OrderSourceRespDTO();
                orderSourceRespDTO.setCode(d.getCode());
                orderSourceRespDTO.setDesc(d.getDesc());
                mp.add(orderSourceRespDTO);
            }
        }
        return Result.buildSuccessResult(mp);
    }


    @ApiOperation(value = "订单详情", notes = "订单详情")
    @ApiImplicitParam(name = "orderGuid", value = "查询订单详情", required = true, dataType =
            "orderGuid")
    @PostMapping("/order_detail")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_OTHER,description = "订单详情")
    public Result<OrderDetailRespDTO> orderDetail(@RequestBody String orderGuid) {
        return Result.buildSuccessResult(orderClientService.getOrderDetailByorderGuid(orderGuid));
    }


  /*  @ApiOperation(value = "查询普通订单日志详情2.0使用", notes = "查询普通订单日志详情")
    @ApiImplicitParam(name = "orderLogListReqDTO", value = "查询普通订单日志详情", required = true, dataType =
            "OrderLogDetailReqDTO")
    @PostMapping(value = "/normal_order_log_detail", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @Deprecated
    Result<OrderLogDetailRespDTO> NormalOrderLogDetail(@RequestBody OrderLogDetailReqDTO orderLogDetailReqDTO) {
        return Result.buildSuccessResult(logFeignService.NormalOrderLogtail(orderLogDetailReqDTO));
    }*/
}
