package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.queue.TableDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;
import java.time.LocalDateTime;
import java.util.Collection;

@ApiModel("预订下单请求入参")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ReserveSubmitReqDTO {

	@NotEmpty
	@ApiModelProperty(value = "门店guid",required = true)
	private String storeGuid;
	@NotNull
	@Max(value = 99L,message = "请输入正确人数")
	@Min(value = 1L,message = "请输入正确人数")
	@ApiModelProperty(value = "预定人数",required = true)
	private Integer number;
	@ApiModelProperty(hidden = true)
	private String state="COMMIT";
	@ApiModelProperty(value = "预订人姓名",required = true)
	private String name;
	@ApiModelProperty(value = "预订人手机号",required = true)
	@NotEmpty
	@Pattern(regexp = "^1[0-9]{10}$",message = "请输入正确手机号")
	private String phone;
	@ApiModelProperty("桌台信息")
	private Collection<TableDTO> tables;
	@NotNull
	@ApiModelProperty("0. 女,1. 男")
	private Byte gender;
	@ApiModelProperty("预定备注")
	private String remark;
	@ApiModelProperty("预定开始时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	private LocalDateTime reserveStartTime;
	@ApiModelProperty("预定结束时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	private LocalDateTime reservesEndTime;

}
