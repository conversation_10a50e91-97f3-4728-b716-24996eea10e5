package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.app.anno.RequireBizAndRdsLock;
import com.holderzone.holder.saas.aggregation.app.anno.RequireRdsLock;
import com.holderzone.holder.saas.aggregation.app.service.TableService;
import com.holderzone.holder.saas.aggregation.app.service.feign.MessageClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.organization.OrgFeignClient;
import com.holderzone.holder.saas.aggregation.app.service.feign.table.TableClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInOrderClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.OrderItemClientService;
import com.holderzone.holder.saas.aggregation.app.transform.BossTransform;
import com.holderzone.holder.saas.aggregation.app.utils.BigDecimalUtil;
import com.holderzone.saas.store.dto.boss.req.BossTableQueryDTO;
import com.holderzone.saas.store.dto.boss.resp.BossTableRespDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.inside.OrderGuidsDTO;
import com.holderzone.saas.store.dto.order.inside.OrderTableInfoDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.organization.PadOrderTypeReqDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.table.*;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.trade.resp.PadOrderRespDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.enums.order.OrderStateEnum;
import com.holderzone.saas.store.enums.table.TableStatusEnum;
import com.holderzone.saas.store.reserve.api.ReserveTableApi;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordLessDTO;
import com.holderzone.saas.store.reserve.api.dto.TableGuidsDTO;
import com.holderzone.saas.store.reserve.api.dto.TableOrderReserveDTO;
import com.holderzone.saas.store.reserve.api.dto.TableReserveRecordRef;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableServiceImpl
 * @date 2019/01/07 11:24
 * @description
 * @program holder-saas-aggregation-app
 */
@Slf4j
@Service
public class TableServiceImpl implements TableService {

    private final TableClientService tableClientService;

    private final DineInOrderClientService dineInOrderClientService;

    private final ReserveTableApi reserveTableApi;

    private final OrderItemClientService orderItemClientService;

    private final OrgFeignClient orgFeignClient;

    private final MessageClientService messageClientService;


    @Autowired
    public TableServiceImpl(TableClientService tableClientService, DineInOrderClientService dineInOrderClientService,
                            ReserveTableApi reserveTableApi,
                            OrderItemClientService orderItemClientService, OrgFeignClient orgFeignClient, MessageClientService messageClientService) {
        this.tableClientService = tableClientService;
        this.reserveTableApi = reserveTableApi;
        this.dineInOrderClientService = dineInOrderClientService;
        this.orderItemClientService = orderItemClientService;
        this.orgFeignClient = orgFeignClient;
        this.messageClientService = messageClientService;
    }

    @Override
    public List<AreaDTO> queryArea(String storeGuid) {
        return tableClientService.queryArea(storeGuid);
    }

    @Override
    public List<TableOrderReserveDTO> queryTable(TableBasicQueryDTO tableBasicQueryDTO, StopWatch stopWatch) {
        // 查询桌台列表
        stopWatch.start("企业：" + UserContextUtils.getEnterpriseName() + "门店：" + UserContextUtils.getStoreName() +
                "TableGuidList:" + tableBasicQueryDTO.getTableGuidList() + "queryTable耗时");
        List<TableOrderDTO> tableOrders = tableClientService.queryTable(tableBasicQueryDTO);
        log.info("桌台信息：{}",JacksonUtils.writeValueAsString(tableOrders));
        stopWatch.stop();
        stopWatch.start("企业：" + UserContextUtils.getEnterpriseName() + "门店：" + UserContextUtils.getStoreName() +
                "TableGuidList:" + tableBasicQueryDTO.getTableGuidList() + "batchGetTableInfo耗时");
        if (Boolean.TRUE.equals(tableBasicQueryDTO.getQueryTableFlag())) {
            return tableOrders.stream().map(e -> new TableOrderReserveDTO(null, e)).collect(Collectors.toList());
        }
        // 填充桌台关联订单信息
        List<String> orderGuidList = tableOrders.stream()
                .map(TableOrderDTO::getOrderGuid)
                .filter(StringUtils::hasText)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(orderGuidList)) {
            OrderGuidsDTO batchOrderRequest = new OrderGuidsDTO(orderGuidList);
            log.info("[批量查询订单桌台信息][入参]batchOrderRequest={}", JacksonUtils.writeValueAsString(batchOrderRequest));
            List<OrderTableInfoDTO> orderTables = dineInOrderClientService.batchGetTableInfo2(batchOrderRequest);
            log.info("[批量查询订单桌台信息][返回]orderTables={}", JacksonUtils.writeValueAsString(orderTables));
            stopWatch.stop();
            stopWatch.start("企业：" + UserContextUtils.getEnterpriseName() + "门店：" + UserContextUtils.getStoreName() +
                    "TableGuidList:" + tableBasicQueryDTO.getTableGuidList() + "校验和预定相关耗时");
            Map<String, OrderTableInfoDTO> orderTableMap = orderTables.stream()
                    .collect(Collectors.toMap(OrderTableInfoDTO::getGuid, Function.identity()));
            List<CompensationTableDTO> tablesNeedCompensate = new ArrayList<>();
            // 桌台订单信息
            handleTableOrders(tableOrders, orderTableMap, tablesNeedCompensate);
            if (!CollectionUtils.isEmpty(tablesNeedCompensate)) {
                CompensationTableReqDTO tableReqDTO = new CompensationTableReqDTO();
                tableReqDTO.setList(tablesNeedCompensate);
                tableReqDTO.setEnterpriseGuid(null);
                tableClientService.compensationTableStatus(tableReqDTO);
                // 非PAD关台后推送消息给pad
                tablesNeedCompensate.forEach(order -> {
                    OrderDTO orderDTO = orderItemClientService.findByOrderGuid(order.getOrderGuid());
                    if (!Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), tableBasicQueryDTO.getDeviceType()) &&
                            Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), orderDTO.getDeviceType())) {
                        sendCloseMsg(tableBasicQueryDTO.getStoreGuid(), tableBasicQueryDTO.getStoreName(),
                                order.getTableGuid());
                    }
                });
            }
        }
        // 查询桌台预定信息
        Map<String, ReserveRecordLessDTO> reserved = queryReserveInfo(tableOrders);
        log.info("[预订信息]reserved={}", JacksonUtils.writeValueAsString(reserved));
        return tableOrders.stream().map(e -> {
            ReserveRecordLessDTO lessDTO = reserved.get(e.getTableGuid());
            return new TableOrderReserveDTO(lessDTO, e);
        }).collect(Collectors.toList());
    }

    /**
     * 查询桌台预定信息
     */
    private Map<String, ReserveRecordLessDTO> queryReserveInfo(List<TableOrderDTO> tableOrders) {
        Map<String, TableOrderDTO> ref = tableOrders.stream()
                .filter(
                        e -> TableStatusEnum.getReservePayStatusList().contains(e.getStatus())
                                && (e.getSubStatus().contains(TableStatusEnum.RESERVATION_LOCK.getStatus())
                                || e.getSubStatus().contains(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus())
                                || e.getSubStatus().contains(TableStatusEnum.TAKE_UP_WITHOUT_ORDER_DISH.getStatus())
                                || e.getSubStatus().contains(TableStatusEnum.TAKE_UP_EATING.getStatus()))
                ).collect(Collectors.toMap(TableOrderDTO::getTableGuid, Function.identity()));
        Map<String, ReserveRecordLessDTO> reserveRef = Collections.emptyMap();
        if (ref.isEmpty()) {
            return reserveRef;
        }
        TableGuidsDTO tableQuery = new TableGuidsDTO(new ArrayList<>(ref.keySet()));
        List<TableReserveRecordRef> result = reserveTableApi.query(tableQuery);
        log.info("[预订信息]result={},tableQuery={}", JacksonUtils.writeValueAsString(result),
                JacksonUtils.writeValueAsString(tableQuery));
        if (result.isEmpty()) {
            return reserveRef;
        }
        return result.stream()
                .collect(Collectors.toMap(TableReserveRecordRef::getTableGuid, TableReserveRecordRef::getLessDTO, (v1, v2) -> v1));
    }

    /**
     * 非PAD关台后推送消息给pad
     *
     * @param storeGuid 门店guid
     * @param storeName 门店名称
     * @param tableGuid 桌台guid
     */
    private void sendCloseMsg(String storeGuid, String storeName, String tableGuid) {
        BusinessMessageDTO closeMessageDTO = new BusinessMessageDTO();
        closeMessageDTO.setMessageType(BusinessMsgTypeEnum.PAD_MESSAGE.getId());
        closeMessageDTO.setPlatform("2");
        closeMessageDTO.setStoreGuid(storeGuid);
        closeMessageDTO.setStoreName(storeName);

        // 非PAD关台消息
        closeMessageDTO.setSubject(BusinessMsgTypeEnum.NON_PAD_SHUTDOWN.getName());
        closeMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.NON_PAD_SHUTDOWN.getId());

        closeMessageDTO.setContent("当前桌台已清台");

        // 查询门店桌台对应设备信息
        PadOrderTypeReqDTO reqDTO = new PadOrderTypeReqDTO();
        reqDTO.setStoreGuid(storeGuid);
        reqDTO.setTableGuid(tableGuid);
        StoreDeviceDTO storeDeviceDTO = orgFeignClient.queryDeviceByStoreTable(reqDTO);
        if (ObjectUtils.isEmpty(storeDeviceDTO)) {
            log.warn("未查询到门店桌台对应设备信息 reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
            return;
        }
        closeMessageDTO.setMessageTypeStr(BusinessMsgTypeEnum.PAD_MESSAGE.getId() + ":" + storeDeviceDTO.getDeviceNo());

        log.info("非PAD关台 closeMessageDTO={}", JacksonUtils.writeValueAsString(closeMessageDTO));
        messageClientService.msg(closeMessageDTO);
    }

    @Override
    @RequireRdsLock
    public String openTable(OpenTableDTO openTableDTO){
        String result = tableClientService.openTable(openTableDTO);
        if (StringUtils.isEmpty(result)) {
            throw new BusinessException("开台失败!");
        }
        return result;
    }

    @Override
    @RequireRdsLock
    public String associatedOpenTable(OpenAssociatedTableDTO openAssociatedTableDTO) {
        String result = tableClientService.associatedOpenTable(openAssociatedTableDTO);
        if (StringUtils.isEmpty(result)) {
            throw new BusinessException("开台失败!");
        }
        return result;
    }

    @Override
    public String cleanTable(TableStatusChangeDTO tableStatusChangeDTO) {
        String result = tableClientService.cleanTable(tableStatusChangeDTO);
        if (StringUtils.isEmpty(result)) {
            throw new BusinessException("清台失败!");
        }
        return result;
    }

    @Override
    @RequireRdsLock
    public boolean turnTable(TurnTableDTO turnTableDTO) {
        return tableClientService.turnTale(turnTableDTO);
    }

    @Override
    @RequireRdsLock
    public List<String> combine(TableCombineDTO tableCombineDTO) {
        return tableClientService.combine(tableCombineDTO);
    }

    @Override
    public TableCombineVerifyRespDTO verifyCombine(TableCombineDTO tableCombineDTO) {
        return tableClientService.verifyCombine(tableCombineDTO);
    }

    @Override
    @RequireRdsLock
    public TableCombineRespDTO combineV2(TableCombineDTO tableCombineDTO) {
        return tableClientService.combinev2(tableCombineDTO);
    }

    @Override
    @RequireRdsLock
    public boolean separateTable(TableOrderCombineDTO tableOrderCombineDTO) {
        return tableClientService.separateTable(tableOrderCombineDTO);
    }

    @Override
    @RequireBizAndRdsLock
    public boolean closeTable(CancelOrderReqDTO cancelOrderReqDTO) {
        return tableClientService.closeTable(cancelOrderReqDTO);
    }

    @Override
    @RequireBizAndRdsLock
    public boolean payCloseTable(CancelOrderReqDTO cancelOrderReqDTO) {
        return tableClientService.payClose(cancelOrderReqDTO);
    }

    @Override
    @RequireBizAndRdsLock
    public boolean tryLock(TableLockDTO tableLockDTO) {
        return tableClientService.tableLock(tableLockDTO);
    }

    @Override
    @RequireRdsLock
    public boolean releaseLock(TableLockDTO tableLockDTO) {
        return tableClientService.releaseLock(tableLockDTO);
    }

    /**
     * 根据桌台guid，查桌台详情
     * 订单状态:只要接了一个单都是接单，否则没有
     *
     * @param tableGuid 桌台guid
     * @return 桌台详情
     */
    @Override
    public TableDTO queryTableByGuid(String tableGuid) {
        TableDTO tableDTO = tableClientService.getTableByGuid(tableGuid);
        String orderGuid = tableDTO.getOrderGuid();
        boolean isAcceptOrder = false;
        if (!StringUtils.isEmpty(orderGuid)) {
            OrderDTO orderDTO = orderItemClientService.findByOrderGuid(orderGuid);
            if (Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), orderDTO.getDeviceType())) {
                List<PadOrderRespDTO> padOrderRespDTOList = orderItemClientService.listPadOrderDTOList(orderGuid);
                if (!CollectionUtils.isEmpty(padOrderRespDTOList)) {
                    for (PadOrderRespDTO pad : padOrderRespDTOList) {
                        // 只要接了一个单都是接单，否则没有
                        if (Objects.equals(OrderStateEnum.ACCEPT_ORDER.getCode(), pad.getOrderState())) {
                            isAcceptOrder = true;
                            break;
                        }
                    }
                }
            } else {
                List<DineInItemDTO> itemDTOList = orderItemClientService.queryItemByOrderGuid(orderGuid);
                if (!CollectionUtils.isEmpty(itemDTOList)) {
                    isAcceptOrder = true;
                }
            }
        }
        tableDTO.setIsAcceptOrder(isAcceptOrder);
        return tableDTO;
    }

    @Override
    public List<BossTableRespDTO> listTable(BossTableQueryDTO queryDTO) {
        // 查询桌台列表
        List<TableOrderDTO> tableOrders = tableClientService.queryTable(queryDTO);
        log.info("[桌台信息]tableOrders={}", JacksonUtils.writeValueAsString(tableOrders));

        // 设置订单相关信息
        setOrderInfo(tableOrders, queryDTO.getEnterpriseGuid());

        return BossTransform.INSTANCE.tableOrderDTOList2BossTableRespDTOList(tableOrders);
    }

    /**
     * 刷新桌台
     */
    @Override
    public void refresh(TableRefreShDTO refreShDTO) {
        String orderGuid = refreShDTO.getOrderGuid();
        SingleDataDTO dto = new SingleDataDTO();
        dto.setData(orderGuid);
        List<TableBasicDTO> tableBasicDTOList = tableClientService.queryTableByOrderGuid(dto);
        if (CollectionUtils.isEmpty(tableBasicDTOList)) {
            log.info("[刷新桌台]刷新桌台失败，未查询到桌台信息 orderGuid={}", orderGuid);
            return;
        }
        List<String> tableGuidList = tableBasicDTOList.stream()
                .map(TableBasicDTO::getGuid)
                .distinct()
                .collect(Collectors.toList());

        BusinessMessageDTO messageDTO = BusinessMessageDTO.builder()
                .subject("桌位状态变化通知")
                .messageType(BusinessMsgTypeEnum.TABLE_STATE_MSG_TYPE.getId())
                .detailMessageType(BusinessMsgTypeEnum.TABLE_CHANGED.getId())
                .content(JacksonUtils.writeValueAsString(tableGuidList))
                .platform("2")
                .storeGuid(refreShDTO.getStoreGuid())
                .storeName(refreShDTO.getStoreName())
                .build();
        messageClientService.msg(messageDTO);
    }

    private void setOrderInfo(List<TableOrderDTO> tableOrders, String enterpriseGuid) {
        // 填充桌台关联订单信息
        List<String> orderGuidList = tableOrders.stream()
                .map(TableOrderDTO::getOrderGuid)
                .filter(StringUtils::hasText)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderGuidList)) {
            log.warn("桌台无订单");
            return;
        }
        OrderGuidsDTO batchOrderRequest = new OrderGuidsDTO(orderGuidList);
        batchOrderRequest.setEnterpriseGuid(enterpriseGuid);
        List<OrderTableInfoDTO> orderTables = dineInOrderClientService.batchGetTableInfo2(batchOrderRequest);
        Map<String, OrderTableInfoDTO> orderTableMap = orderTables.stream()
                .collect(Collectors.toMap(OrderTableInfoDTO::getGuid, Function.identity()));
        List<CompensationTableDTO> tablesNeedCompensate = new ArrayList<>();
        handleTableOrders(tableOrders, orderTableMap, tablesNeedCompensate);

        if (!CollectionUtils.isEmpty(tablesNeedCompensate)) {
            CompensationTableReqDTO tableReqDTO = new CompensationTableReqDTO();
            tableReqDTO.setList(tablesNeedCompensate);
            tableReqDTO.setEnterpriseGuid(enterpriseGuid);
            tableClientService.compensationTableStatus(tableReqDTO);
        }
    }

    private void handleTableOrders(List<TableOrderDTO> tableOrders,
                                   Map<String, OrderTableInfoDTO> orderTableMap,
                                   List<CompensationTableDTO> tablesNeedCompensate) {
        for (TableOrderDTO tableOrderDTO : tableOrders) {
            OrderTableInfoDTO orderTableInfoDTO = checkOrderTableInfoDTO(tableOrderDTO, orderTableMap, tablesNeedCompensate);
            if (orderTableInfoDTO == null) continue;
            /* BugFix:17696
               只有在upperState、orderFeeForCombine都不为空，且是主桌或者子桌的情况下，
               用orderFeeForCombine这个值在桌台预览页面
            */
            if (!ObjectUtils.isEmpty(orderTableInfoDTO.getUpperState())
                    && (!ObjectUtils.isEmpty(orderTableInfoDTO.getOrderFeeForCombine())
                    || Lists.newArrayList(3, 4).contains(orderTableInfoDTO.getUpperState()))
                    && orderTableInfoDTO.getUpperState() != 0
            ) {
                // 存在主单子单的桌台处理
                existSubOrderTableHandler(tableOrderDTO, orderTableInfoDTO, orderTableMap);
            } else {
                tableOrderDTO.setOrderAmount(BigDecimalUtil.setScale2(BigDecimalUtil.nonNullValue(orderTableInfoDTO.getOrderFee())));
            }

            tableOrderDTO.setActualGuestsNo(orderTableInfoDTO.getGuestCount());
            tableOrderDTO.setPrintPreBillNum(orderTableInfoDTO.getPrintPreBillNum());
        }
    }

    /**
     * 存在主单子单的桌台处理
     */
    private void existSubOrderTableHandler(TableOrderDTO tableOrderDTO, OrderTableInfoDTO orderTableInfoDTO, Map<String, OrderTableInfoDTO> orderTableMap) {
        if (orderTableInfoDTO.getUpperState().equals(1) || orderTableInfoDTO.getUpperState().equals(2)) {
            // 并台
            tableOrderDTO.setOrderAmount(BigDecimalUtil.setScale2(BigDecimalUtil.nonNullValue(orderTableInfoDTO.getOrderFeeForCombine())));
        }
        if (orderTableInfoDTO.getUpperState().equals(3) || orderTableInfoDTO.getUpperState().equals(4)) {
            // 同一桌台多单结账
            BigDecimal orderFeeForCombine = orderTableInfoDTO.getOrderFeeForCombine();
            OrderTableInfoDTO mainOrderTableInfoDTO = orderTableMap.get(orderTableInfoDTO.getMainOrderGuid());
            if (Objects.nonNull(mainOrderTableInfoDTO)) {
                orderFeeForCombine = mainOrderTableInfoDTO.getOrderFeeForCombine();
            }
            tableOrderDTO.setOrderAmount(BigDecimalUtil.setScale2(BigDecimalUtil.nonNullValue(orderFeeForCombine)));
        }
    }

    @Nullable
    private OrderTableInfoDTO checkOrderTableInfoDTO(TableOrderDTO tableOrderDTO,
                                                     Map<String, OrderTableInfoDTO> orderTableMap,
                                                     List<CompensationTableDTO> tablesNeedCompensate) {
        String orderGuid = tableOrderDTO.getOrderGuid();
        if (StringUtils.isEmpty(orderGuid)) {
            return null;
        }
        OrderTableInfoDTO tableInfoDTO = orderTableMap.get(orderGuid);
        if (null == tableInfoDTO) {
            log.warn("桌台服务和订单服务，订单号不一致，tableOrder：{}",
                    JacksonUtils.writeValueAsString(tableOrderDTO));
            return null;
        }
        if (Objects.equals(tableInfoDTO.getState(), 4)) {
            // 可能存在订单服务该笔订单已经支付完成，桌台服务未能及时更新状态
            log.warn("订单服务和桌台服务，支付状态不一致，tableOrder：{}，orderTable：{}",
                    JacksonUtils.writeValueAsString(tableOrderDTO), JacksonUtils.writeValueAsString(tableInfoDTO));
            tablesNeedCompensate.add(new CompensationTableDTO(tableOrderDTO.getTableGuid(), orderGuid));
            return null;
        }
        if (StringUtils.hasText(tableOrderDTO.getMainOrderGuid())) {
            OrderTableInfoDTO mainOrderTableInfoDTO = orderTableMap.get(tableOrderDTO.getMainOrderGuid());
            if (ObjectUtils.isEmpty(mainOrderTableInfoDTO)) {
                log.warn("桌台服务和订单服务，订单号不一致，tableOrder：{}",
                        JacksonUtils.writeValueAsString(tableOrderDTO));
                return null;
            }
            if (Objects.equals(mainOrderTableInfoDTO.getState(), 4)) {
                // 可能存在订单服务该笔订单已经支付完成，桌台服务未能及时更新状态
                log.warn("订单服务和桌台服务，支付状态不一致，tableOrder：{}，mainOrderTable：{}",
                        JacksonUtils.writeValueAsString(tableOrderDTO), JacksonUtils.writeValueAsString(mainOrderTableInfoDTO));
                tablesNeedCompensate.add(new CompensationTableDTO(tableOrderDTO.getTableGuid(), tableOrderDTO.getMainOrderGuid()));
                return null;
            }
            tableOrderDTO.setMemberPhone(mainOrderTableInfoDTO.getMemberPhone());
            tableOrderDTO.setMemberGuid(mainOrderTableInfoDTO.getMemberGuid());
        } else {
            tableOrderDTO.setMemberPhone(tableInfoDTO.getMemberPhone());
            tableOrderDTO.setMemberGuid(tableInfoDTO.getMemberGuid());
        }
        return tableInfoDTO;
    }
}
