package com.holderzone.holder.saas.aggregation.app.service.feign.pay;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.pay.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @description store-pay服务直调
 * @date 2021/9/13 18:59
 * @className: AggPayClientService
 */
@Component
@FeignClient(value = "holder-saas-store-pay", fallbackFactory = AggPayClientService.SaasAggPayClientFallBack.class)
public interface AggPayClientService {

    /**
     * 支付
     *
     * @param saasAggPayDTO SaasAggPayDTO
     * @return AggPayRespDTO
     */
    @PostMapping("agg/pay")
    AggPayRespDTO pay(SaasAggPayDTO saasAggPayDTO);

    /**
     * 退款
     *
     * @param saasAggRefundDTO SaasAggRefundDTO
     * @return AggRefundRespDTO
     */
    @PostMapping("agg/refund")
    AggRefundRespDTO refund(@RequestBody SaasAggRefundDTO saasAggRefundDTO);

    /**
     * 轮询
     *
     * @param saasPollingDTO SaasPollingDTO
     * @return AggPayPollingRespDTO
     */
    @PostMapping("agg/polling")
    AggPayPollingRespDTO polling(SaasPollingDTO saasPollingDTO);

    /**
     * 取消支付
     *
     * @param saasPollingDTO SaasPollingDTO
     * @return AggPayReserveResultDTO
     */
    @PostMapping("agg/reserve")
    AggPayReserveResultDTO reservePay(SaasPollingDTO saasPollingDTO);

    /**
     * 查询支付状态
     *
     * @param saasPollingDTO SaasPollingDTO
     * @return AggPayPollingRespDTO
     */
    @PostMapping("agg/queryPaySt")
    AggPayPollingRespDTO queryPaySt(SaasPollingDTO saasPollingDTO);

    /**
     * 聚合支付查询支付结果接口
     *
     * @param saasPollingDTO SaasPollingDTO
     * @return AggPayPollingRespDTO
     */
    @PostMapping("agg/query")
    @ApiOperation(value = "聚合支付查询支付结果接口")
    AggPayPollingRespDTO query(@RequestBody SaasPollingDTO saasPollingDTO);

    @Slf4j
    @Component
    class SaasAggPayClientFallBack implements FallbackFactory<AggPayClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public AggPayClientService create(Throwable throwable) {
            return new AggPayClientService() {
                @Override
                public AggPayRespDTO pay(SaasAggPayDTO saasAggPayDTO) {
                    throw new BusinessException("调用交易中心异常");
                }

                @Override
                public AggRefundRespDTO refund(SaasAggRefundDTO saasAggRefundDTO) {
                    throw new BusinessException("调用交易中心异常");
                }

                @Override
                public AggPayPollingRespDTO polling(SaasPollingDTO saasPollingDTO) {
                    throw new BusinessException("聚合支付轮询异常");
                }

                @Override
                public AggPayReserveResultDTO reservePay(SaasPollingDTO saasPollingDTO) {
                    throw new BusinessException("聚合支付撤销异常");
                }

                @Override
                public AggPayPollingRespDTO queryPaySt(SaasPollingDTO saasPollingDTO) {
                    throw new BusinessException("聚合支付查询支付状态异常");
                }

                @Override
                public AggPayPollingRespDTO query(SaasPollingDTO saasPollingDTO) {
                    log.error(HYSTRIX_PATTERN, "query", JacksonUtils.writeValueAsString(saasPollingDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }

}
