package com.holderzone.holder.saas.aggregation.weixin.utils.map;

import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.trade.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;


@Mapper
@Component
public interface DiscountMAP {

    DiscountMAP INSTANCE = Mappers.getMapper(DiscountMAP.class);

    DiscountFeeDetailDTO discountDO2DiscountFeeDetailDTO(DiscountDTO discountDTO);
}
