package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxTempMsgClientService;
import com.holderzone.saas.store.dto.weixin.open.WxMpTemplateDTO;
import com.holderzone.saas.store.dto.weixin.req.TempMsgCreateDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxTempMsgController
 * @date 2019/05/15 17:46
 * @description 微信公众号消息模板cotroller
 * @program holder-saas-store
 */
@Api("微信公众号消息模板cotroller")
@RequestMapping("/wx_temp")
@RestController
@Slf4j
public class WxTempMsgController {

    @Autowired
    WxTempMsgClientService wxTempMsgClientService;

    @PostMapping("/create")
    @ApiOperation("替公众号创建指定的消息模板，用于排队消息推送")
    public Result createMsgTemp(TempMsgCreateDTO tempMsgCreateDTO) {
        wxTempMsgClientService.createMsgTemp(tempMsgCreateDTO);
        return Result.buildEmptySuccess();
    }

}
