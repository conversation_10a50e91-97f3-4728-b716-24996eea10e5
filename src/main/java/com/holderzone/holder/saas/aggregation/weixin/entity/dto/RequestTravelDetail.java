package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 获取余额明细(分页查询) 兼容jar包依赖冲突
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "RequestTravelDetail", description = "获取资金明细(分页查询)")
public class RequestTravelDetail extends BasePage implements Serializable {

    @ApiModelProperty(name = "memberInfoCardGuid", value = "会员卡GUID", required = true)
    @NotNull(message = "会员卡GUID不能为空")
    private String memberInfoCardGuid;

}
