package com.holderzone.holder.saas.aggregation.merchant.controller.report.order;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.order.OrderCancelClientService;
import com.holderzone.saas.store.dto.report.OrderRecoveryDetailDTO;
import com.holderzone.saas.store.dto.report.query.OrderDetailQueryDTO;
import com.holderzone.saas.store.dto.report.query.OrderTransRecordQueryDTO;
import com.holderzone.saas.store.dto.report.resp.OrderTransRecordRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderCancelController
 * @date 2018/10/09 11:28
 * @description 订单作废记录报表controller-聚合层
 * @program holder-saas-aggregation-merchant
 */
@RestController
@RequestMapping("/orderCancel")
@Api(value = "OrderCancel",description = "聚合层订单报表-订单作废记录接口",hidden = true)
@Deprecated
public class OrderCancelMerchantController {
    private static final Logger logger = LoggerFactory.getLogger(OrderCancelMerchantController.class);
    @Autowired
    private OrderCancelClientService clientService;

    @PostMapping(value = "/orderList")
    @ApiOperation(value = "根据查询条件获取订单作废记录列表")
    @Deprecated
    public Result<Page<OrderTransRecordRespDTO>> orderList(@RequestBody OrderTransRecordQueryDTO transRecordQueryDTO){
        //已作废订单
        return Result.buildSuccessResult(clientService.orderList(transRecordQueryDTO));
    }

    @PostMapping(value = "/orderDetail")
    @ApiOperation(value = "根据order_guida查询订单详情，包含：基本信息，菜品信息，结算信息，优惠信息，支付信息")
    @Deprecated
    public Result<OrderRecoveryDetailDTO>orderDetail(@RequestBody OrderDetailQueryDTO queryDTO){
        return Result.buildSuccessResult(clientService.orderDetail(queryDTO));
    }
}
