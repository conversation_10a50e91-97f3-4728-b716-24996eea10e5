package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.item.req.ItemRedisReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemAndTypeForAndroidRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemEstimateForAndroidRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxItemClientService
 * @date 2019/01/23 14:27
 * @description //TODO
 * @program ${MODULE_NAME}
 */
@Component
@FeignClient(name = "holder-saas-store-item", fallbackFactory = WxItemClientService.WxItemFallBack.class)
public interface WxItemClientService {

    @PostMapping("/item/query_for_synchronize")
    ItemAndTypeForAndroidRespDTO getItemsForWeixin(@RequestBody BaseDTO baseDTO);

    @PostMapping(value = "/estimate/query_estimate_for_synchronize")
    List<ItemEstimateForAndroidRespDTO> queryEstimateForSyn(@RequestBody BaseDTO baseDTO);

    @PostMapping(value = "/estimate/dinein_fail")
    Boolean dineinFail(@RequestBody List<DineInItemDTO> request);

    @ApiOperation("存入item服务的redis")
    @PostMapping("/item/selectItemList")
    Boolean putItemRedis(@RequestBody ItemRedisReqDTO redisReqDTO);

    @GetMapping("/item/query_parent_item_guid")
    String getSubItemGuid(@RequestParam("parentItemGuid") String parentItemGuid);

    @Slf4j
    @Component
    class WxItemFallBack implements FallbackFactory<WxItemClientService> {
        @Override
        public WxItemClientService create(Throwable throwable) {
            return new WxItemClientService() {
                @Override
                public ItemAndTypeForAndroidRespDTO getItemsForWeixin(BaseDTO baseDTO) {
                    log.error("获取商品信息失败", throwable.getMessage());
                    ItemAndTypeForAndroidRespDTO itemAndTypeForAndroidRespDTO = new ItemAndTypeForAndroidRespDTO();
                    itemAndTypeForAndroidRespDTO.setItemList(Collections.emptyList());
                    itemAndTypeForAndroidRespDTO.setTypeList(Collections.emptyList());
                    return itemAndTypeForAndroidRespDTO;
                }

                @Override
                public List<ItemEstimateForAndroidRespDTO> queryEstimateForSyn(BaseDTO baseDTO) {
                    log.error("查询估清失败:{}", baseDTO);
                    return Collections.emptyList();
                }

                @Override
                public Boolean dineinFail(List<DineInItemDTO> request) {
                    log.error("退还库存失败:{}", request);
                    return false;
                }

                @Override
                public Boolean putItemRedis(ItemRedisReqDTO redisReqDTO) {
                    log.error("存入item服务的redis 失败:{}", JacksonUtils.writeValueAsString(redisReqDTO));
                    return false;
                }

                @Override
                public String getSubItemGuid(String parentItemGuid) {
                    log.error("getSubItemGuid 失败:{}, e:{}", parentItemGuid, JacksonUtils.writeValueAsString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
