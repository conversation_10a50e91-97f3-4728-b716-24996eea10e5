package com.holderzone.holder.saas.aggregation.app.controller.business;

import cn.hutool.core.collection.CollectionUtil;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.app.service.feign.business.HandoverClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.business.PayClientService;
import com.holderzone.saas.store.dto.business.manage.*;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className HandOverController
 * @date 18-9-17 下午2:54
 * @description 交接班相关接口
 * @program holder-saas-aggregation-app
 */
@Api(value = "交班接口")
@RestController
@RequestMapping("/handover")
@Slf4j
public class HandoverController {

    private final HandoverClientService handoverClientService;

    private final PayClientService payClientService;

    @Autowired
    public HandoverController(HandoverClientService handoverClientService, PayClientService payClientService) {
        this.handoverClientService = handoverClientService;
        this.payClientService = payClientService;
    }

    @ApiOperation(value = "交班", response = Result.class)
    @PostMapping(value = "/confirm", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "交班",action = OperatorType.UPDATE)
    public Result confirm(@RequestBody HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
        handoverClientService.confirm(handoverRecordConfirmDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "交班（新）", response = Result.class)
    @PostMapping(value = "/confirmNew", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "交班",action = OperatorType.UPDATE)
    public Result confirmNew(@RequestBody HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
        handoverClientService.confirmNew(handoverRecordConfirmDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "根据记录GUID查询该交接班记录")
    @PostMapping(value = "/query", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "根据记录GUID查询该交接班记录",action = OperatorType.SELECT)
    public Result<HandoverRecordDTO> query(@RequestBody HandoverRecordQuerySingleDTO handoverRecordQuerySingleDTO) {
        HandoverRecordDTO dto = handoverClientService.query(handoverRecordQuerySingleDTO);
        if (dto == null) {
            return Result.buildOpFailedResult("接口异常，请稍后重试", null);
        }
        return Result.buildSuccessResult(dto).setNow(DateTimeUtils.localDateTime2Mills(DateTimeUtils.now()));
    }

    @ApiOperation(value = "根据记录GUID查询该交接班记录（新）")
    @PostMapping(value = "/queryNew", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "根据记录GUID查询该交接班记录",action = OperatorType.SELECT)
    public Result<HandoverPayNewDTO> queryNew(@RequestBody HandoverRecordQuerySingleDTO handoverRecordQuerySingleDTO) {
        HandoverPayNewDTO dto = handoverClientService.queryNew(handoverRecordQuerySingleDTO);
        if (dto == null) {
            return Result.buildOpFailedResult("接口异常，请稍后重试", null);
        }
        return Result.buildSuccessResult(dto).setNow(DateTimeUtils.localDateTime2Mills(DateTimeUtils.now()));
    }

    @PostMapping(value = "/queryAll", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "根据(storeGuid&terminalId&status)查询交接班记录列表")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "查询交接班记录列表",action = OperatorType.SELECT)
    public Result<Page<HandoverRecordDTO>> queryAll(@RequestBody HandoverRecordQueryAllDTO handoverRecordQueryAllDTO) {
        Page<HandoverRecordDTO> result = handoverClientService.queryByPage(handoverRecordQueryAllDTO);
        if (result == null || result.getData() == null || result.getData().isEmpty()) {
            return Result.buildSuccessMsg("无交接班记录");
        }
        return Result.buildSuccessResult(result).setNow(DateTimeUtils.localDateTime2Mills(DateTimeUtils.now()));
    }

    @PostMapping(value = "/isAllConfirmed", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "根据storeGuid查询该门店是否全部设备均已交班（返回值1代表已交班）")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "根据storeGuid查询该门店是否全部设备均已交班",action = OperatorType.SELECT)
    public Result<Integer> isAllConfirmed(@RequestBody HandoverRecordIsAllConfirmedDTO handoverRecordIsAllConfirmedDTO) {
        return Result.buildSuccessResult(handoverClientService.isAllConfirmed(handoverRecordIsAllConfirmedDTO))
                     .setNow(DateTimeUtils.localDateTime2Mills(DateTimeUtils.now()));
    }

    @ApiOperation(value = "交接班查询")
    @PostMapping
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "交接班查询",action = OperatorType.SELECT)
    public Result<HandoverPayDTO> settle(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO) {
        HandoverPayDTO handoverPayDTO = handoverClientService.settle(handoverPayQueryDTO);
        return Result.buildSuccessResult(handoverPayDTO).setNow(DateTimeUtils.localDateTime2Mills(DateTimeUtils.now()));
    }

    @ApiOperation(value = "交接班查询(新)", notes = "增加预订金统计，更改返回数据格式，拆分为销售、储值、预订")
    @PostMapping(value = "/settleNew", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "交接班查询",action = OperatorType.SELECT)
    public Result<?> settleNew(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO) {
        HandoverPayNewDTO handoverPayDTO = handoverClientService.settleNew(handoverPayQueryDTO);
        log.info("交接班查询(新)返回参数：{}", JacksonUtils.writeValueAsString(handoverPayDTO));
        if(handoverPayDTO == null){
            return Result.buildSuccessResult(null).setNow(DateTimeUtils.localDateTime2Mills(DateTimeUtils.now()));
        }
        handoverPayLocaleTransfer(handoverPayDTO.getIncomingDetailList());
        handoverPayLocaleTransfer(handoverPayDTO.getChargeDetailList());
        handoverPayLocaleTransfer(handoverPayDTO.getRepaymentList());
        handoverPayLocaleTransfer(handoverPayDTO.getReservePayDetailList());
        handoverDiscountLocaleTransfer(handoverPayDTO.getDiscountDetailList());
        return Result.buildSuccessResult(handoverPayDTO).setNow(DateTimeUtils.localDateTime2Mills(DateTimeUtils.now()));
    }

    private void handoverPayLocaleTransfer(List<AmountItemDTO> amountList){
        if(CollectionUtil.isEmpty(amountList)){
            return;
        }
        amountList.forEach(e -> e.setName(PaymentTypeEnum.PaymentType.getLocaleName(e.getName())));
    }

    private void handoverDiscountLocaleTransfer(List<AmountItemDTO> amountList){
        if(CollectionUtil.isEmpty(amountList)){
            return;
        }
        amountList.forEach(e -> e.setName(DiscountTypeEnum.getLocaleDescByName(e.getName())));
    }

    @ApiOperation("根据员工guid查询该员工当前的班次详情")
    @PostMapping("/queryByUserGuid")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "根据员工guid查询该员工当前的班次详情",action = OperatorType.SELECT)
    public Result<HandoverRecordDTO> queryByUserGuid(@RequestBody HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
        HandoverRecordDTO handoverRecordDTO = handoverClientService.queryByUserGuid(handoverRecordConfirmDTO);
        return Result.buildSuccessResult(handoverRecordDTO).setNow(DateTimeUtils.localDateTime2Mills(DateTimeUtils.now()));
    }

    @ApiOperation(value = "根据交接班guid重打印交接单", notes = "根据交接班guid重打印交接单")
    @PostMapping("/printHandOverRecord/{handoverRecordGuid}/{deviceId}")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "根据交接班guid重打印交接单",action = OperatorType.SELECT)
    public Result printHandOverRecord(@PathVariable("handoverRecordGuid") String handoverRecordGuid,
                                      @PathVariable("deviceId") String deviceId) {
        handoverClientService.printHandOverRecord(handoverRecordGuid, deviceId);
        return Result.buildEmptySuccess().setNow(DateTimeUtils.localDateTime2Mills(DateTimeUtils.now()));
    }

    @ApiOperation(value = "根据交接班guid重打印交接单（新）", notes = "根据交接班guid重打印交接单")
    @PostMapping("/printHandOverRecordNew/{handoverRecordGuid}/{deviceId}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "根据交接班guid重打印交接单",action = OperatorType.SELECT)
    public Result printHandOverRecordNew(@PathVariable("handoverRecordGuid") String handoverRecordGuid,
                                      @PathVariable("deviceId") String deviceId) {
        handoverClientService.printHandOverRecordNew(handoverRecordGuid, deviceId);
        return Result.buildEmptySuccess().setNow(DateTimeUtils.localDateTime2Mills(DateTimeUtils.now()));
    }

    @ApiOperation(value = "预打印交接单", notes = "预打印交接单")
    @PostMapping("/prePrintHandOverRecord")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "根据交接班guid重打印交接单",action = OperatorType.SELECT)
    public Result prePrintHandOverRecord(@RequestBody HandoverPayNewDTO handoverPayNewDTO) {
        log.info("预打印交接单：{}", JacksonUtils.writeValueAsString(handoverPayNewDTO));
        handoverClientService.prePrintHandOverRecord(handoverPayNewDTO);
        return Result.buildEmptySuccess().setNow(DateTimeUtils.localDateTime2Mills(DateTimeUtils.now()));
    }


    @ApiOperation(value = "零售版交接班查询")
    @PostMapping("/retail/settle")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "零售版交接班查询",action = OperatorType.SELECT)
    public Result<HandoverPayDTO> retailSettle(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO) {
        HandoverPayDTO handoverPayDTO = handoverClientService.retailSettle(handoverPayQueryDTO);
        return Result.buildSuccessResult(handoverPayDTO).setNow(DateTimeUtils.localDateTime2Mills(DateTimeUtils.now()));
    }



    @ApiOperation(value = "零售版交班", response = Result.class)
    @PostMapping(value = "/retail/confirm", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "零售版交班",action = OperatorType.UPDATE)
    public Result retailConfirm(@RequestBody HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
        handoverClientService.retailConfirm(handoverRecordConfirmDTO);
        return Result.buildEmptySuccess();
    }


    @PostMapping(value = "/retail/queryAll", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "零售版根据(storeGuid&terminalId&status)查询交接班记录列表")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "零售版查询交接班记录列表",action = OperatorType.SELECT)
    public Result<Page<HandoverRecordDTO>> retailQueryAll(@RequestBody HandoverRecordQueryAllDTO handoverRecordQueryAllDTO) {
        Page<HandoverRecordDTO> result = handoverClientService.retailQueryByPage(handoverRecordQueryAllDTO);
        if (result == null || result.getData() == null || result.getData().isEmpty()) {
            return Result.buildSuccessMsg("无交接班记录");
        }
        return Result.buildSuccessResult(result).setNow(DateTimeUtils.localDateTime2Mills(DateTimeUtils.now()));
    }


    @ApiOperation(value = "零售版根据记录GUID查询该交接班记录")
    @PostMapping(value = "/retail/query", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "零售版根据记录GUID查询该交接班记录",action = OperatorType.SELECT)
    public Result<HandoverRecordDTO> retailQuery(@RequestBody HandoverRecordQuerySingleDTO handoverRecordQuerySingleDTO) {
        HandoverRecordDTO dto = handoverClientService.retailQuery(handoverRecordQuerySingleDTO);
        if (dto == null) {
            return Result.buildOpFailedResult("接口异常，请稍后重试", null);
        }
        return Result.buildSuccessResult(dto).setNow(DateTimeUtils.localDateTime2Mills(DateTimeUtils.now()));
    }


    @ApiOperation(value = "零售版根据交接班guid重打印交接单", notes = "零售版根据交接班guid重打印交接单")
    @PostMapping("/retail/printHandOverRecord/{handoverRecordGuid}/{deviceId}")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "零售版根据交接班guid重打印交接单",action = OperatorType.SELECT)
    public Result printRetailHandOverRecord(@PathVariable("handoverRecordGuid") String handoverRecordGuid,
                                      @PathVariable("deviceId") String deviceId) {
        handoverClientService.printRetailHandOverRecord(handoverRecordGuid, deviceId);
        return Result.buildEmptySuccess().setNow(DateTimeUtils.localDateTime2Mills(DateTimeUtils.now()));
    }


}
