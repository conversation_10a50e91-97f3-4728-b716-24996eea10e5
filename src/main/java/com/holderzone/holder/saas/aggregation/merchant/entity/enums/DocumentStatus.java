package com.holderzone.holder.saas.aggregation.merchant.entity.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum DocumentStatus {

    UNKNOWN(-1,"未知"),
    /**
     * 未提交
     */
    UN_SUBMIT(0,"未提交"),

    /**
     * 已提交
     */
    SUBMIT(1,"已提交");

    private final Integer status;

    private final String desc;

    public static String getDescByStatus(Integer status){
        for(DocumentStatus documentStatus : DocumentStatus.values()){
            if(status.equals(documentStatus.getStatus())){
                return documentStatus.getDesc();
            }
        }
        return UNKNOWN.getDesc();
    }

}
