package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.saas.store.dto.weixin.WxStoreAuthorizerInfoDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreMenuReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxMenuDetailsDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreMenuDetailsClientService
 * @date 2019/2/22 14:57
 * @description 微信点餐,菜品，分类，标签获取service
 * @package com.holderzone.holder.saas.aggregation.weixin.service.rpc
 */
@Component
@FeignClient(name = "holder-saas-store-weixin",fallbackFactory = WxStoreMenuDetailsClientService.WxStoreMenuDetailsFallBack.class)
public interface WxStoreMenuDetailsClientService {

    @PostMapping("/wx-store-menu-provide/details")
    WxMenuDetailsDTO getWxMenuDetails(WxStoreMenuReqDTO wxStoreMenuReqDTO);

    @PostMapping("/wx-store-menu-provide/get_consumer_info")
    WxStoreConsumerDTO getConsumerInfo(WxPortalReqDTO wxPortalReqDTO);

	@PostMapping("/wx-store-menu-provide/configuration")
	WxOrderConfigDTO getwxOrderConfig(@RequestBody WxStoreConsumerDTO wxStoreConsumerDTO);

    @PostMapping("/session/get_wx_auth_info")
    WxStoreAuthorizerInfoDTO getWxAuthInfo(@RequestParam("brandGuid")String brandGuid);

    @PostMapping("/session/find_wx_auth_info")
    WxStoreAuthorizerInfoDTO findWxAuthInfo(@RequestParam("appId")String appId);

    @Slf4j
    @Component
    class WxStoreMenuDetailsFallBack implements FallbackFactory<WxStoreMenuDetailsClientService> {

        @Override
        public WxStoreMenuDetailsClientService create(Throwable throwable) {
            return new WxStoreMenuDetailsClientService() {
                @Override
                public WxMenuDetailsDTO getWxMenuDetails(WxStoreMenuReqDTO wxStoreMenuReqDTO) {
                    log.error("获取菜品信息失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public WxStoreConsumerDTO getConsumerInfo(WxPortalReqDTO wxPortalReqDTO) {
                    log.error("获取Consumer信息失败，msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

				@Override
				public WxOrderConfigDTO getwxOrderConfig(WxStoreConsumerDTO wxStoreConsumerDTO) {
					log.error("获取门店配置信息失败，msg={}", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

                @Override
                public WxStoreAuthorizerInfoDTO getWxAuthInfo(String brandGuid) {
                    log.error("getWxAuthInfo获取微信授权信息失败，brandGuid={},msg={}",brandGuid, throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public WxStoreAuthorizerInfoDTO findWxAuthInfo(String appId) {
                    log.error("findWxAuthInfo获取微信授权信息失败，appId={},msg={}",appId, throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }
            };
        }
    }
}
