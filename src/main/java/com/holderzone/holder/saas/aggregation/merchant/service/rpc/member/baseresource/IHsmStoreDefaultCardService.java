package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.baseresource;


import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.dto.baseresource.request.HsmStoreDefaultCardReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.request.HsmStoreDefaultCardUpdateReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.response.HsmStoreDefaultCardRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * 门店默认卡表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-12
 */
@Component
@FeignClient(name = "holder-saas-member-account", fallbackFactory = IHsmStoreDefaultCardService.HsmStoreDefaultCardServiceFallBack.class)
public interface IHsmStoreDefaultCardService {

    @ApiOperation("根据条件查询企业所有门店的默认卡")
    @PostMapping("hsm-store-default-card/query_by_condition")
    Page<HsmStoreDefaultCardRespDTO> queryByCondition(@RequestBody HsmStoreDefaultCardReqDTO storeDefaultCardReqDTO);

    @ApiOperation("保存或修改门店默认卡")
    @PostMapping("hsm-store-default-card/saveOrUpdateStoreDefaultCard")
    Boolean saveOrUpdateStoreDefaultCard(@RequestBody HsmStoreDefaultCardUpdateReqDTO storeDefaultCardUpdateReqDTO);

    @Component
    class HsmStoreDefaultCardServiceFallBack implements FallbackFactory<IHsmStoreDefaultCardService> {

        private static final Logger LOGGER = LoggerFactory
                .getLogger(IHsmStoreDefaultCardService.HsmStoreDefaultCardServiceFallBack.class);

        @Override
        public IHsmStoreDefaultCardService create(Throwable throwable) {
            return new IHsmStoreDefaultCardService() {

                @Override
                public Page<HsmStoreDefaultCardRespDTO> queryByCondition(HsmStoreDefaultCardReqDTO storeDefaultCardReqDTO) {
                    LOGGER.error("根据条件查询企业所有门店的默认卡错误:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public Boolean saveOrUpdateStoreDefaultCard(HsmStoreDefaultCardUpdateReqDTO storeDefaultCardUpdateReqDTO) {
                    LOGGER.error("保存或修改门店默认卡错误:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }
}
