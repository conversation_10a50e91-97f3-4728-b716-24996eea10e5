package com.holderzone.holder.saas.aggregation.merchant.service.rpc.order;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.order.request.OrderLogListReqDTO;
import com.holderzone.saas.store.dto.order.response.OrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.OrderLogListRespDTO;
import com.holderzone.saas.store.dto.orderlog.request.OrderLogDetailReqDTO;
import com.holderzone.saas.store.dto.orderlog.response.OrderLogDetailRespDTO;
import com.holderzone.saas.store.dto.orderlog.response.TakeawayOrderLogDetailRespDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @className orderClientService
 * @date 2018/10/09 9:44
 * @description //TODO
 * @program holder-saas-store-member
 */
@Component
@FeignClient(name = "holder-saas-store-order")
public interface OrderClientService {
    @PostMapping("/order/getOrderLogList")
    Page<OrderLogListRespDTO> getOrderLogList(OrderLogListReqDTO orderLogListReqDTO);

    @PostMapping("/order/getNormalOrderLogDetail")
    OrderLogDetailRespDTO getNormalOrderLogDetail(OrderLogDetailReqDTO orderLogDetailReqDTO);

    @PostMapping("/order/getTakeAwayOrderLogDetail")
    TakeawayOrderLogDetailRespDTO getTakeawayOrderLogDetail(OrderLogDetailReqDTO orderLogDetailReqDTO);

    @PostMapping("/order/order_detail")
    OrderDetailRespDTO getOrderDetailByorderGuid(String orderGuid);

}
