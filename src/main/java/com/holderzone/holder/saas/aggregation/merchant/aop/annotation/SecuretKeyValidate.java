package com.holderzone.holder.saas.aggregation.merchant.aop.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SecuretKeyValidate
 * @date 2019/06/18 15:15
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface SecuretKeyValidate {

}