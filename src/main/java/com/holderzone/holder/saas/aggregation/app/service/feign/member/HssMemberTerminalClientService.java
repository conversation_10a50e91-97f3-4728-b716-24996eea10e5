package com.holderzone.holder.saas.aggregation.app.service.feign.member;

import com.holderzone.holder.saas.member.terminal.dto.retail.*;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberClientService
 * @date 2018/09/16 下午9:39
 * @description 会员调用服务接口
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-member-terminal", fallbackFactory = HssMemberTerminalClientService.MemberFallback.class)
public interface HssMemberTerminalClientService {

    /**
     * 会员退款
     * 注意：这玩意怀疑没调用，要是调用了可能有问题，参数都不一样，参考retail服务的接口
     *
     * @return
     */
    @GetMapping("/hss/member/consumption/refund")
    Boolean refund(@RequestParam("orderNum") String orderNum);

    /**
     * 支付结算
     *
     * @param payReqDTO
     * @return
     */
    @PostMapping("/hss/member/consumption/pay")
    String payOrder(@RequestBody RequestRetailPayInfo payReqDTO);

    @GetMapping("/hss/rule/integral/compute")
    ResponseRetailIntegralCompute integralCompute(@RequestParam("memberInfoCardGuid") String memberInfoCardGuid,
                                                  @RequestParam("orderMoney") BigDecimal orderMoney);

    @PostMapping(value = "/hss/member/add")
    Boolean addMember(@RequestBody RequestSaveMember saveCMemberDTO);

    @PostMapping(value = "/hss/member/{memberInfoGuid}")
    Boolean updateMemberInfo(@PathVariable("memberInfoGuid") String memberInfoGuid,
                             @RequestBody RequestUpdateMemberInfo updateMemberInfoDTO);

    @GetMapping("/hss/member/getMemberAndCardInfo")
    ResponseMemberAndCardInfo getMemberAndCardInfo(@RequestParam("phoneOrCardNum") String phoneOrCardNum);

    @Component
    class MemberFallback implements FallbackFactory<HssMemberTerminalClientService> {
        private static final Logger logger = LoggerFactory.getLogger(HssMemberTerminalClientService.MemberFallback.class);

        @Override
        public HssMemberTerminalClientService create(Throwable throwable) {
            return new HssMemberTerminalClientService() {

                @Override
                public Boolean refund(String orderNum) {
                    logger.error("商超会员异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public String payOrder(RequestRetailPayInfo payReqDTO) {
                    logger.error("商超会员异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public ResponseRetailIntegralCompute integralCompute(String memberInfoCardGuid, BigDecimal orderMoney) {
                    logger.error("商超会员异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean addMember(RequestSaveMember saveCMemberDTO) {
                    logger.error("商超会员异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean updateMemberInfo(String memberInfoGuid, RequestUpdateMemberInfo updateMemberInfoDTO) {
                    logger.error("商超会员异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public ResponseMemberAndCardInfo getMemberAndCardInfo(String phoneOrCardNum) {
                    logger.error("商超会员异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }
            };
        }
    }
}
