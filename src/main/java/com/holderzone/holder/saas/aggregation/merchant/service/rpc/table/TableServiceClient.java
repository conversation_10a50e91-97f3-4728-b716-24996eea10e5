package com.holderzone.holder.saas.aggregation.merchant.service.rpc.table;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.table.*;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableServiceClient
 * @date 2019/01/07 10:22
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(value = "holder-saas-store-table", fallbackFactory = TableServiceClient.TableServiceClientFallBack.class)
public interface TableServiceClient {


    @PostMapping("/table/add")
    String addTable(@RequestBody TableBasicDTO tableBasicDTO);

    @PostMapping("/table/update")
    String updateTable(@RequestBody TableBasicDTO tableBasicDTO);

    @PostMapping("/table/deleteAll")
    List<String> deleteAllTable(@RequestBody List<String> guids);

    @PostMapping("/table/web/query")
    List<TableBasicDTO> queryTableByWeb(@RequestBody TableBasicQueryDTO tableBasicQueryDTO);

    @PostMapping("/table/batch/create")
    String createTable(@RequestBody TableBatchCreateDTO tableBatchCreateDTO);

    @PostMapping("/area/add")
    String addArea(@RequestBody AreaDTO areaDTO);

    @PostMapping("/area/update")
    String updateArea(@RequestBody AreaDTO areaDTO);

    @PostMapping("/area/query/all/{storeGuid}")
    List<AreaDTO> queryAllArea(@PathVariable("storeGuid") String storeGuid);

    @PostMapping("/area/delete/{guid}")
    String deleteArea(@PathVariable("guid") String guid);

    @PostMapping("/table/delete")
    List<String> deleteTable(List<String> guids);

    @Slf4j
    @Component
    class TableServiceClientFallBack implements FallbackFactory<TableServiceClient> {
        @Override
        public TableServiceClient create(Throwable throwable) {
            return new TableServiceClient() {

                private String failResult = "failure";

                @Override
                public String addTable(TableBasicDTO tableBasicDTO) {
                    log.error("新增桌台异常 e={} tableBasicDTO={}", throwable.getMessage(), JacksonUtils.writeValueAsString(tableBasicDTO));
                    return failResult;
                }

                @Override
                public List<String> deleteTable(List<String> guids) {
                    log.error("删除桌台异常  guids={} e={}", JacksonUtils.writeValueAsString(guids), throwable.getMessage());
                    throw new BusinessException("删除桌台异常");
                }

                @Override
                public String updateTable(TableBasicDTO tableBasicDTO) {
                    log.error("更新桌台异常 e={} tableBasicDTO={}", throwable.getMessage(), JacksonUtils.writeValueAsString(tableBasicDTO));
                    return failResult;
                }

                @Override
                public List<String> deleteAllTable(List<String> guids) {
                    log.error("删除桌台异常  guids={} e={}", JacksonUtils.writeValueAsString(guids), throwable.getMessage());
                    throw new BusinessException("删除桌台异常");
                }

                @Override
                public List<TableBasicDTO> queryTableByWeb(TableBasicQueryDTO tableBasicQueryDTO) {
                    log.error("web查询桌台异常  tableBasicQueryDTO={} e={}", JacksonUtils.writeValueAsString(tableBasicQueryDTO), throwable.getMessage());
                    throw new BusinessException("web查询桌台异常");
                }

                @Override
                public String createTable(TableBatchCreateDTO tableBatchCreateDTO) {
                    log.error("批量创建桌台异常 e={}", throwable.getMessage());
                    return failResult;
                }

                @Override
                public String addArea(AreaDTO areaDTO) {
                    log.error("新增区域异常 e={} areaDTO={}", throwable.getMessage(), JacksonUtils.writeValueAsString(areaDTO));
                    return failResult;
                }

                @Override
                public String updateArea(AreaDTO areaDTO) {
                    log.error("更新区域异常 e={} areaDTO={}", throwable.getMessage(), JacksonUtils.writeValueAsString(areaDTO));
                    return failResult;
                }

                @Override
                public List<AreaDTO> queryAllArea(String storeGuid) {
                    log.error("查询区域异常 e={} storeGuid={}", throwable.getMessage(), storeGuid);
                    throw new BusinessException("查询区域异常");
                }

                @Override
                public String deleteArea(String guid) {
                    log.error("删除区域异常 e={} guid={}", throwable.getMessage(), guid);
                    return failResult;
                }
            };
        }
    }
}
