package com.holderzone.holder.saas.aggregation.merchant.controller.trade;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.entity.dto.BusinessOrderStatisticsExportRespDTO;
import com.holderzone.holder.saas.aggregation.merchant.entity.enums.ExportLocaleEnum;
import com.holderzone.holder.saas.aggregation.merchant.entity.enums.OrderStatusLocaleEnum;
import com.holderzone.holder.saas.aggregation.merchant.entity.enums.SettlementConfigLocaleEnum;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.business.PaymentTypeClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.trade.TradeClientService;
import com.holderzone.holder.saas.aggregation.merchant.util.ExcelUtil;
import com.holderzone.saas.store.dto.order.SettlementRulesDTO;
import com.holderzone.saas.store.dto.order.response.OrderUploadRespDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsCombinedRespDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsDetailRespDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsQueryDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsRespDTO;
import com.holderzone.saas.store.dto.trade.PaymentTypeDTO;
import com.holderzone.saas.store.dto.trade.PaymentTypeQueryDTO;
import com.holderzone.saas.store.dto.trade.req.FixGrouponTransactionRecordReqDTO;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TradeMerchantController
 * @date 2019/10/10 17:23
 * @description //TODO
 * @program IdeaProjects
 */
@Slf4j
@RestController
@RequestMapping("/trade")
@Api(description = "trade端相关接口")
public class TradeMerchantController {

    @Autowired
    TradeClientService tradeClientService;

    @Autowired
    private PaymentTypeClientService paymentTypeClientService;

    @ApiOperation(value = "获取所有规则", notes = "订单详情")
    @GetMapping("/hst_settlement_rules/get_settlement_rules")
    public Result<List<SettlementRulesDTO>> getSettlementRules() {
        List<SettlementRulesDTO> settlementRules = tradeClientService.getSettlementRules();
        SettlementConfigLocaleEnum.transferLocale(settlementRules);
        return Result.buildSuccessResult(settlementRules);
    }

    @ApiOperation(value = "获取订单统计分页数据")
    @PostMapping("/orderStatistics/page")
    public Result<Page<BusinessOrderStatisticsRespDTO>> getOrderStatisticsPage(@RequestBody BusinessOrderStatisticsQueryDTO queryDTO) {
        Page<BusinessOrderStatisticsRespDTO> orderStatisticsPage = tradeClientService.getOrderStatisticsPage(queryDTO);
        if (orderStatisticsPage != null && CollectionUtils.isNotEmpty(orderStatisticsPage.getData())) {
            orderStatisticsPage.getData().forEach(o -> {
                o.setTradeMode(TradeModeEnum.getLocaleByDesc(o.getTradeMode()));
                o.setOrderState(OrderStatusLocaleEnum.getOrderStatusLocale(o.getOrderState()));
                o.setOrderSource(ExportLocaleEnum.getSourceLocale(o.getOrderSource()));
                o.setPayWay(transferLocalePayWay(o.getPayWay()));
            });
        }
        return Result.buildSuccessResult(orderStatisticsPage);
    }

    private final static String SPLIT = "、";

    private String transferLocalePayWay(String payWay) {
        if (StringUtils.isEmpty(payWay)) {
            return payWay;
        }
        String[] split = payWay.split(SPLIT);
        List<String> list = Lists.newArrayList();
        for (String s : split) {
            list.add(PaymentTypeEnum.PaymentType.getLocaleName(s));
        }
        return Joiner.on(SPLIT).join(list);
    }

    @PostMapping("/orderStatistics/export")
    public void orderStatisticsDown(@RequestBody BusinessOrderStatisticsQueryDTO queryDTO, HttpServletResponse response) {
        queryDTO.setCurrentPage(1);
        queryDTO.setPageSize(Constants.MAX_EXPORT_SIZE);
        Page<BusinessOrderStatisticsRespDTO> orderStatisticsPage = tradeClientService.getOrderStatisticsPage(queryDTO);
        if (orderStatisticsPage.getTotalCount() > Constants.MAX_EXPORT_SIZE) {
            throw new BusinessException("最多只支持2万条数据导出");
        }
        List<BusinessOrderStatisticsRespDTO> data = orderStatisticsPage.getData();
        ExcelUtil<BusinessOrderStatisticsExportRespDTO> excelUtil = new ExcelUtil<>();
        List<BusinessOrderStatisticsExportRespDTO> exportList = Lists.newArrayList();
        data.forEach(d -> {
            d.setTradeMode(TradeModeEnum.getLocaleByDesc(d.getTradeMode()));
            d.setOrderState(OrderStatusLocaleEnum.getOrderStatusLocale(d.getOrderState()));
            d.setOrderSource(ExportLocaleEnum.getSourceLocale(d.getOrderSource()));
            d.setPayWay(transferLocalePayWay(d.getPayWay()));
            BusinessOrderStatisticsExportRespDTO export = new BusinessOrderStatisticsExportRespDTO();
            BeanUtil.copyProperties(d, export);
            export.setIsInvoice(d.getIsInvoice() != null && d.getIsInvoice() ? "是" : "否");
            exportList.add(export);
        });
        try {
            response.setContentType("application/msexcel;charset=UTF-8");
            String substring = System.currentTimeMillis() + LocaleUtil.getMessage("EXPORT_ORDER_STATISTICS_HEADER") + ".xls";
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(substring, "UTF-8"));
            ServletOutputStream outputStream = response.getOutputStream();
            excelUtil.exportExcel(ExportLocaleEnum.listOrderHeader(), exportList, outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "统计 条件下订单数/实收金额")
    @PostMapping("/orderStatistics/combined")
    public Result<BusinessOrderStatisticsCombinedRespDTO> getOrderStatisticsCombined(@RequestBody BusinessOrderStatisticsQueryDTO queryDTO) {
        return Result.buildSuccessResult(tradeClientService.getOrderStatisticsCombined(queryDTO));
    }

    @ApiOperation(value = "统计订单 订单明细")
    @GetMapping("/orderStatistics/detail")
    public Result<BusinessOrderStatisticsDetailRespDTO> getOrderStatisticsDetail(@RequestParam("orderGuid") String orderGuid) {
        BusinessOrderStatisticsDetailRespDTO orderStatisticsDetail = tradeClientService.getOrderStatisticsDetail(orderGuid);
        if (orderStatisticsDetail != null) {
            orderStatisticsDetail.setOrderSource(ExportLocaleEnum.getSourceLocale(orderStatisticsDetail.getOrderSource()));
            orderStatisticsDetail.setTradeMode(TradeModeEnum.getLocaleByDesc(orderStatisticsDetail.getTradeMode()));
            orderStatisticsDetail.setOrderState(OrderStatusLocaleEnum.getOrderStatusLocale(orderStatisticsDetail.getOrderState()));
            if (CollectionUtils.isNotEmpty(orderStatisticsDetail.getDiscountInfoList())) {
                orderStatisticsDetail.getDiscountInfoList().forEach(d -> d.setDiscountName(DiscountTypeEnum.getLocaleDesc(d.getDiscountType())));
            }
            if (CollectionUtils.isNotEmpty(orderStatisticsDetail.getPayWayDetails())) {
                orderStatisticsDetail.getPayWayDetails().forEach(p -> p.setPaymentTypeName(PaymentTypeEnum.PaymentType.getLocaleName(p.getPaymentTypeName())));
            }
            // 同一笔订单合并相同支付方式金额
//            if (CollectionUtils.isNotEmpty(orderStatisticsDetail.getRefundDetails())) {
//                Map<String, List<RefundTransactionRecordDetailRespDTO>> refundMap = orderStatisticsDetail.getRefundDetails().stream()
//                        .collect(Collectors.groupingBy(RefundTransactionRecordDetailRespDTO::getPaymentTypeName));
//                List<RefundTransactionRecordDetailRespDTO> refundDetails = new ArrayList<>();
//                refundMap.forEach((paymentTypeName, list) -> {
//                    RefundTransactionRecordDetailRespDTO dto = new RefundTransactionRecordDetailRespDTO();
//                    dto.setPaymentTypeName(paymentTypeName);
//                    BigDecimal summarize = list.stream().map(r -> new BigDecimal(r.getAmount())).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    dto.setAmount(summarize.toString());
//                    refundDetails.add(dto);
//                });
//                orderStatisticsDetail.setRefundDetails(refundDetails);
//            }
        }
        return Result.buildSuccessResult(orderStatisticsDetail);
    }

    @ApiOperation(value = "订单退款信息")
    @GetMapping("/orderStatistics/refundInfo")
    public Result<BusinessOrderStatisticsDetailRespDTO> getOrderStatisticsRefundInfo(@RequestParam("orderGuid") String orderGuid) {
        BusinessOrderStatisticsDetailRespDTO orderStatisticsRefundInfo = tradeClientService.getOrderStatisticsRefundInfo(orderGuid);
        if (orderStatisticsRefundInfo != null) {
            orderStatisticsRefundInfo.setOrderSource(ExportLocaleEnum.getSourceLocale(orderStatisticsRefundInfo.getOrderSource()));
            orderStatisticsRefundInfo.setCheckoutSource(ExportLocaleEnum.getSourceLocale(orderStatisticsRefundInfo.getCheckoutSource()));
            orderStatisticsRefundInfo.setTradeMode(TradeModeEnum.getLocaleByDesc(orderStatisticsRefundInfo.getTradeMode()));
            orderStatisticsRefundInfo.setOrderState(OrderStatusLocaleEnum.getOrderStatusLocale(orderStatisticsRefundInfo.getOrderState()));

            if (CollectionUtils.isNotEmpty(orderStatisticsRefundInfo.getRefundDetails())) {
                orderStatisticsRefundInfo.getRefundDetails().forEach(p -> p.setPaymentTypeName(PaymentTypeEnum.PaymentType.getLocaleName(p.getPaymentTypeName())));
            }
        }
        return Result.buildSuccessResult(orderStatisticsRefundInfo);
    }

    @ApiOperation(value = "复制订单")
    @GetMapping(value = "/copyOrders", produces = "application/json;charset=utf-8")
    public Result<OrderUploadRespDTO> copyOrders(@RequestParam("fileUrl") String fileUrl) {
        return Result.buildSuccessResult(tradeClientService.copyOrders(fileUrl));
    }

    /**
     * 根据订单查询订单状态
     *
     * @param orderGuid 订单guid
     * @return 订单状态 (-1：未查询到订单 1：未结账， 2：已结账， 3：已退款，4：已作废)
     */
    @ApiOperation(value = "根据订单查询订单状态")
    @GetMapping("/get_order_state_by_guid")
    public Result<Integer> getOrderStateByGuid(@RequestParam("orderGuid") String orderGuid) {
        return Result.buildSuccessResult(tradeClientService.getOrderStateByGuid(orderGuid));
    }

    @ApiOperation(value = "收款方式下拉", notes = "收款方式下拉")
    @GetMapping("/list_payment_type_name")
    public Result<List<String>> listPaymentTypeName() {
        List<PaymentTypeDTO> paymentTypeDTOS = paymentTypeClientService.getAll(new PaymentTypeQueryDTO());
        Set<String> paymentTypeSet = paymentTypeDTOS.stream()
                .map(PaymentTypeDTO::getPaymentTypeName)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        List<String> paymentTypeList = Arrays.stream(PaymentTypeEnum.values()).map(PaymentTypeEnum::getDesc).collect(Collectors.toList());
        paymentTypeSet.addAll(paymentTypeList);
        List<String> result = paymentTypeSet.stream().sorted().collect(Collectors.toList());
        return Result.buildSuccessResult(result);
    }

    /**
     * 根据订单查询订单金额
     *
     * @param orderGuid 订单guid
     * @return 订单金额
     */
    @ApiOperation(value = "根据订单查询订单金额")
    @GetMapping("/get_order_fee_by_guid")
    public Result<BigDecimal> getOrderFeeByGuid(@RequestParam("orderGuid") String orderGuid) {
        return Result.buildSuccessResult(tradeClientService.getOrderFeeByGuid(orderGuid));
    }

    /**
     * 订单统计查询收银员信息
     *
     * @param businessOrderStatisticsQueryDTO 筛选条件
     * @return 收银员信息列表
     */
    @ApiOperation(value = "订单统计查询收银员信息")
    @PostMapping("/orderStatistics/get_checkout_staff")
    public Result<List<UserBriefDTO>> getCheckoutStaffs(@RequestBody BusinessOrderStatisticsQueryDTO businessOrderStatisticsQueryDTO) {
        return Result.buildSuccessResult(tradeClientService.getCheckoutStaffs(businessOrderStatisticsQueryDTO));
    }


    @ApiOperation(value = "修复团购支付记录丢失问题")
    @PostMapping("/order_detail/fix_groupon_transaction_record")
    public Result<Void> fixGrouponTransactionRecord(@RequestBody FixGrouponTransactionRecordReqDTO reqDTO) {
        log.info("FixGrouponTransactionRecordReqDTO:{}", JacksonUtils.writeValueAsString(reqDTO));
        tradeClientService.fixGrouponTransactionRecord(reqDTO);
        return Result.buildEmptySuccess();
    }
}