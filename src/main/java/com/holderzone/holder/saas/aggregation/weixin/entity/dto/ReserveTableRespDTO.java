package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("预订桌台")
@Accessors(chain = true)
public class ReserveTableRespDTO {

	@ApiModelProperty(value = "桌台id")
	private String tableGuid;

	@ApiModelProperty(value="桌台号")
	private String tableCode;

	@ApiModelProperty(value = "几人桌")
	private Integer seats;

	@ApiModelProperty("桌台标签列表")
	private List<String> tags;
}
