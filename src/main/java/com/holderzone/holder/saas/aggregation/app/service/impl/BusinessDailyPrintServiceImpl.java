package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.app.entity.auth.*;
import com.holderzone.holder.saas.aggregation.app.service.BusinessDailyPrintService;
import com.holderzone.holder.saas.aggregation.app.service.BusinessDailyService;
import com.holderzone.holder.saas.aggregation.app.service.feign.PrintClientService;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponsePayWayDetail;
import com.holderzone.saas.store.dto.order.request.daily.DailyPrintReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.MemberConsumeRespDTO;
import com.holderzone.saas.store.dto.print.content.*;
import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import com.holderzone.saas.store.dto.print.content.nested.PayRecordStats;
import com.holderzone.saas.store.dto.print.content.nested.ReduceRecord;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.enums.print.PrintSourceEnum;
import com.holderzone.saas.store.enums.trade.DiningTypeEnum;
import com.holderzone.saas.store.enums.trade.RefundCodeEnum;
import com.holderzone.saas.store.util.EncryptionSymbolUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BusinessDailyPrintServiceImpl implements BusinessDailyPrintService {

    private final BusinessDailyService businessDailyService;

    private final PrintClientService printClientService;

    @Override
    public Result<String> print(DailyPrintReqDTO request) {
        //用户信息
        UserContext userContext = UserContextUtils.get();
        //时间处理:兼容老版数据
        LocalDateTime start;
        LocalDateTime end;
        if (request.getBeginTime().length() == 10) {
            start = DateTimeUtils.string2LocalDateTime(request.getBeginTime() + " 00:00:00");
            end = DateTimeUtils.string2LocalDateTime(request.getEndTime() + " 23:59:59");
        } else {
            start = DateTimeUtils.string2LocalDateTime(request.getBeginTime());
            end = DateTimeUtils.string2LocalDateTime(request.getEndTime());
        }

        long startTimeMills = DateTimeUtils.localDateTime2Mills(start);
        long endTimeMills = DateTimeUtils.localDateTime2Mills(end);
        PrintDTO printDTO = null;
        //日报类型匹配：1营业概况 2收款统计 3会员消费统计 4用餐类型统计 5分类销售统计 6商品销售统计 7属性销售统计 8退菜统计 9赠菜统计
        // 10 会员充值统计（拆分） 11 会员消费统计（拆分）12 退款统计
        switch (request.getType()) {
            case 1: {
                request.setIsPrint(1);
                //营业概况统计
                OverviewSaleDTO response = businessDailyService.overviewSale(request);
                //数据转换
                printDTO = getOverviewPrintInfo(response, userContext.getStoreName(), startTimeMills, endTimeMills);
                //设置基础数据
                setBasePrintInfo(request, userContext, printDTO, InvoiceTypeEnum.OP_STATS.getType());
                break;
            }
            case 2: {
                //收款统计
                List<GatherSaleDTO> response = businessDailyService.gatherSale(request);
                //数据转换
                printDTO = getGatherPrintInfo(response, userContext.getStoreName(), startTimeMills, endTimeMills);
                //设置基础数据
                setBasePrintInfo(request, userContext, printDTO, InvoiceTypeEnum.RECEIPT_STATS.getType());
                break;
            }
            case 3: {
                //会员消费统计
                MemberConsumeRespDTO response = businessDailyService.memberConsume(request);
                //数据转换
                printDTO = getMemberConsumePrintInfo(response, userContext.getStoreName(), startTimeMills, endTimeMills);
                //设置基础数据
                setBasePrintInfo(request, userContext, printDTO, InvoiceTypeEnum.MEM_STATS.getType());
                break;
            }
            case 4: {
                //用餐类型统计
                List<DiningTypeSaleDTO> response = businessDailyService.diningTypeSale(request);
                //数据转换
                printDTO = getDiningTypePrintInfo(response, userContext.getStoreName(), startTimeMills, endTimeMills);
                //设置基础数据
                setBasePrintInfo(request, userContext, printDTO, InvoiceTypeEnum.TRADE_STATS.getType());
                break;
            }
            case 5: {
                //分类销售统计
                List<CategorySaleDTO> response = businessDailyService.classifySale(request);
                //数据转换
                printDTO = getClassifyPrintInfo(response, userContext.getStoreName(), startTimeMills, endTimeMills);
                if (CollectionUtils.isEmpty(((PrintTypeStatsDTO) printDTO).getItemTypeList())) {
                    return Result.buildOpFailedResult("无分类销售数据，不需要打印");
                }
                //设置基础数据
                setBasePrintInfo(request, userContext, printDTO, InvoiceTypeEnum.TYPE_STATS.getType());
                break;
            }
            case 6: {
                //商品销售统计
                List<GoodsSaleDTO> response = businessDailyService.goodsSale(request);
                //数据转换
                printDTO = getGoodsSalePrintInfo(response, userContext.getStoreName(), startTimeMills, endTimeMills);
                if (CollectionUtils.isEmpty(((PrintItemStatsDTO) printDTO).getItemList())) {
                    return Result.buildOpFailedResult("无商品销售数据，不需要打印");
                }
                //设置基础数据
                setBasePrintInfo(request, userContext, printDTO, InvoiceTypeEnum.ITEM_STATS.getType());
                break;
            }
            case 7: {
                //属性销售统计
                PropStatsSaleDTO response = businessDailyService.attrSale(request);
                //数据转换
                printDTO = getAttrPrintInfo(response, userContext.getStoreName(), startTimeMills, endTimeMills);
                if (CollectionUtils.isEmpty(((PrintPropStatsDTO) printDTO).getPropGroupList())) {
                    return Result.buildOpFailedResult("无属性销售数据，不需要打印");
                }
                //设置基础数据
                setBasePrintInfo(request, userContext, printDTO, InvoiceTypeEnum.PROP_STATS.getType());
                break;
            }
            case 8: {
                //退菜统计
                List<ReturnSaleDTO> response = businessDailyService.returnVegetablesSale(request);
                //数据转换
                printDTO = getCategorySalePrintInfo(response, userContext.getStoreName(), startTimeMills, endTimeMills);
                if (CollectionUtils.isEmpty(((PrintItemStatsDTO) printDTO).getItemList())) {
                    return Result.buildOpFailedResult("无退菜统计数据，不需要打印");
                }
                //设置基础数据
                setBasePrintInfo(request, userContext, printDTO, InvoiceTypeEnum.ITEM_REFUND_STATS.getType());
                break;
            }
            case 9: {
                //赠菜统计
                List<GiftSaleDTO> response = businessDailyService.dishGivingSale(request);
                //数据转换
                printDTO = getGiftSalePrintInfo(response, userContext.getStoreName(), startTimeMills, endTimeMills);
                if (CollectionUtils.isEmpty(((PrintItemStatsDTO) printDTO).getItemList())) {
                    return Result.buildOpFailedResult("无赠菜统计数据，不需要打印");
                }
                //设置基础数据
                setBasePrintInfo(request, userContext, printDTO, InvoiceTypeEnum.ITEM_GIFT_STATS.getType());
                break;
            }
            case 10: {
                //会员充值统计（拆分）
                MemberRechargeSaleDTO response = businessDailyService.memberRechargeSale(request);
                //数据转换
                printDTO = getMemberRechargePrintInfo(response, userContext.getStoreName(), startTimeMills, endTimeMills);
                //设置基础数据
                setBasePrintInfo(request, userContext, printDTO, InvoiceTypeEnum.MEM_RECHAR_STATS.getType());
                break;
            }
            case 11: {
                //会员消费统计（拆分）
                MemberConsumeSaleDTO response = businessDailyService.memberConsumeSale(request);
                //数据转换
                printDTO = getMemberConsumeOnlyPrintInfo(response, userContext.getStoreName(), startTimeMills, endTimeMills);
                //设置基础数据
                setBasePrintInfo(request, userContext, printDTO, InvoiceTypeEnum.MEM_CONSU_STATS.getType());
                break;
            }
            case 12: {
                request.setIsPrint(1);
                //用餐类型统计
                RefundSaleDTO response = businessDailyService.refundSale(request);
                //数据转换
                printDTO = getRefundPrintInfo(response, userContext.getStoreName(), startTimeMills, endTimeMills);
                //设置基础数据
                setBasePrintInfo(request, userContext, printDTO, InvoiceTypeEnum.SALE_REFUND_STATS.getType());
                break;
            }
            default: {
                throw new ParameterException("无匹配的日报类型");
            }
        }
        String printServiceResponse = printClientService.printTask(printDTO);
        if ("SUCCESS".equals(printServiceResponse)) {
            return Result.buildSuccessMsg("打印成功");
        } else {
            return Result.buildOpFailedResult(printServiceResponse);
        }
    }

    private PrintDTO getRefundPrintInfo(RefundSaleDTO response, String storeName, long startTimeMills, long endTimeMills) {
        PrintSaleRefundStatsDTO printDTO = new PrintSaleRefundStatsDTO();
        List<RefundSaleAmountDTO> refundAmounts = response.getRefundAmounts();
        if (CollectionUtils.isNotEmpty(refundAmounts)) {
            for (RefundSaleAmountDTO refundAmount : refundAmounts) {
                if (String.valueOf(RefundCodeEnum.REFUND_TYPE_DINEIN_PART_REFUND.getCode()).equals(refundAmount.getRefundCode())) {
                    printDTO.setDineinPartRefund(buildSaleRefundStats(refundAmount));
                } else if (String.valueOf(RefundCodeEnum.REFUND_TYPE_DINEIN_RECOVERY.getCode()).equals(refundAmount.getRefundCode())) {
                    printDTO.setDineinRecovery(buildSaleRefundStats(refundAmount));
                } else if (String.valueOf(RefundCodeEnum.REFUND_TYPE_FAST_PART_REFUND.getCode()).equals(refundAmount.getRefundCode())) {
                    printDTO.setFastPartRefund(buildSaleRefundStats(refundAmount));
                } else if (String.valueOf(RefundCodeEnum.REFUND_TYPE_FAST_RECOVERY.getCode()).equals(refundAmount.getRefundCode())) {
                    printDTO.setFastRecovery(buildSaleRefundStats(refundAmount));
                } else if (String.valueOf(RefundCodeEnum.REFUND_TYPE_TOTAL.getCode()).equals(refundAmount.getRefundCode())) {
                    printDTO.setTotal(buildSaleRefundStats(refundAmount));
                }
            }
        }
        //收银员 以逗号分隔
        if (CollectionUtils.isNotEmpty(response.getCheckoutStaffs())) {
            StringBuilder staff = new StringBuilder();
            response.getCheckoutStaffs().forEach(s -> staff.append(s).append(","));
            staff.deleteCharAt(staff.length() - 1);
            printDTO.setCheckoutStaffs(staff.toString());
        }
        //门店名称
        printDTO.setStoreName(storeName);
        //开始时间
        printDTO.setStartTime(startTimeMills);
        //结束时间
        printDTO.setEndTime(endTimeMills);
        return printDTO;
    }

    private PrintSaleRefundStatsDTO.SaleRefundStats buildSaleRefundStats(RefundSaleAmountDTO refundAmount) {
        if (Objects.isNull(refundAmount)) {
            return null;
        }
        PrintSaleRefundStatsDTO.SaleRefundStats saleRefundStats = new PrintSaleRefundStatsDTO.SaleRefundStats();
        saleRefundStats.setRefundOrderCount(0);
        saleRefundStats.setRefundName(refundAmount.getRefundName());
        saleRefundStats.setRefundOrderCountStr(refundAmount.getRefundOrderCount());
        if (EncryptionSymbolUtil.isNormal(saleRefundStats.getRefundOrderCountStr())) {
            saleRefundStats.setRefundOrderCount(Integer.valueOf(saleRefundStats.getRefundOrderCountStr()));
        }
        saleRefundStats.setRefundAmount(BigDecimal.ZERO);
        saleRefundStats.setRefundAmountStr(refundAmount.getRefundAmount());
        if (EncryptionSymbolUtil.isNormal(saleRefundStats.getRefundAmountStr())) {
            saleRefundStats.setRefundAmount(new BigDecimal(saleRefundStats.getRefundAmountStr()));
        }
        return saleRefundStats;
    }


    /**
     * 营业概况打印数据组装
     */
    private PrintOpStatsDTO getOverviewPrintInfo(OverviewSaleDTO response, String storeName, long startTimeMills, long endTimeMills) {
        PrintOpStatsDTO printDTO = new PrintOpStatsDTO();
        printDTO.setOrderQuantity(0L);
        printDTO.setOrderQuantityStr(response.getOrderCount());
        if (EncryptionSymbolUtil.isNormal(printDTO.getOrderQuantityStr())) {
            printDTO.setOrderQuantity(Long.valueOf(response.getOrderCount()));
        }
        printDTO.setCustomerNumber(0L);
        printDTO.setCustomerNumberStr(response.getGuestCount());
        if (EncryptionSymbolUtil.isNormal(printDTO.getCustomerNumberStr())) {
            printDTO.setCustomerNumber(Long.valueOf(response.getGuestCount()));
        }
        printDTO.setSalesTotal(BigDecimal.ZERO);
        printDTO.setSalesTotalStr(response.getConsumerAmount());
        if (EncryptionSymbolUtil.isNormal(printDTO.getSalesTotalStr())) {
            printDTO.setSalesTotal(new BigDecimal(response.getConsumerAmount()));
        }
        printDTO.setSalesIncome(BigDecimal.ZERO);
        printDTO.setSalesIncomeStr(response.getGatherAmount());
        if (EncryptionSymbolUtil.isNormal(printDTO.getSalesIncomeStr())) {
            printDTO.setSalesIncome(new BigDecimal(response.getGatherAmount()));
        }
        printDTO.setEstimatedAmount(BigDecimal.ZERO);
        printDTO.setEstimatedAmountStr(response.getEstimatedAmount());
        if (EncryptionSymbolUtil.isNormal(printDTO.getEstimatedAmountStr())) {
            printDTO.setEstimatedAmount(new BigDecimal(response.getEstimatedAmount()));
        }
        List<PayRecord> salesDetail = new ArrayList<>();
        handleGatherItems(response, salesDetail);
        printDTO.setSalesDetail(salesDetail);
        printDTO.setReduceTotal(BigDecimal.ZERO);
        printDTO.setReduceTotalStr(response.getDiscountAmount());
        if (EncryptionSymbolUtil.isNormal(printDTO.getReduceTotalStr())) {
            printDTO.setReduceTotal(new BigDecimal(response.getDiscountAmount()));
        }
        List<ReduceRecord> reduceDetail = new ArrayList<>();
        handleDiscountItems(response, reduceDetail);
        printDTO.setReduceDetail(reduceDetail);
        //收银员 以逗号分隔
        if (CollectionUtils.isNotEmpty(response.getCheckoutStaffs())) {
            StringBuilder staff = new StringBuilder();
            response.getCheckoutStaffs().forEach(s -> staff.append(s).append(","));
            staff.deleteCharAt(staff.length() - 1);
            printDTO.setCheckoutStaffs(staff.toString());
        }
        // 正餐客流量
        printDTO.setDineInCustomerNumber(0L);
        printDTO.setDineInCustomerNumberStr(response.getDineInGuestCount());
        if (EncryptionSymbolUtil.isNormal(printDTO.getDineInCustomerNumberStr())) {
            printDTO.setDineInCustomerNumber(Long.valueOf(response.getDineInGuestCount()));
        }
        // 正餐上座率
        printDTO.setOccupancyRatePercent(response.getOccupancyRatePercent());
        // 正餐开台率
        printDTO.setOpenTableRatePercent(response.getOpenTableRatePercent());
        // 正餐翻台率
        printDTO.setFlipTableRatePercent(response.getFlipTableRatePercent());
        // 正餐平均用餐时长
        printDTO.setAvgDineInTime(0L);
        printDTO.setAvgDineInTimeStr(response.getAvgDineInTime());
        if (EncryptionSymbolUtil.isNormal(printDTO.getAvgDineInTimeStr())) {
            printDTO.setAvgDineInTime(Long.valueOf(response.getAvgDineInTime()));
        }
        // 会员充值
        printDTO.setRechargeMoney(BigDecimal.ZERO);
        printDTO.setRechargeMoneyStr(response.getRechargeMoney());
        if (EncryptionSymbolUtil.isNormal(printDTO.getRechargeMoneyStr())) {
            printDTO.setRechargeMoney(new BigDecimal(response.getRechargeMoney()));
        }
        // 退款总额
        printDTO.setRefundAmount(BigDecimal.ZERO);
        printDTO.setRefundAmountStr(response.getRefundAmount());
        if (EncryptionSymbolUtil.isNormal(printDTO.getRefundAmountStr())) {
            printDTO.setRefundAmount(new BigDecimal(response.getRefundAmount()));
        }
        //门店名称
        printDTO.setStoreName(storeName);
        //开始时间
        printDTO.setStartTime(startTimeMills);
        //结束时间
        printDTO.setEndTime(endTimeMills);
        log.info("营业概况打印信息：{}", JacksonUtils.writeValueAsString(printDTO));
        return printDTO;
    }

    private void handleDiscountItems(OverviewSaleDTO response, List<ReduceRecord> reduceDetail) {
        List<DiscountAmountItemDTO> discounts = response.getDiscountItems();
        if (CollectionUtils.isNotEmpty(discounts)) {
            for (DiscountAmountItemDTO item : discounts) {
                ReduceRecord reduceRecord = new ReduceRecord();
                reduceRecord.setName(DiscountTypeEnum.getLocaleDescByName(item.getName()));
                if (StringUtils.hasText(item.getAmount()) && !EncryptionSymbolUtil.isEncryption(item.getAmount())) {
                    reduceRecord.setAmount(new BigDecimal(item.getAmount()));
                }
                reduceRecord.setOrderCount(0L);
                reduceRecord.setOrderCountStr(item.getOrderCount());
                if (EncryptionSymbolUtil.isNormal(item.getOrderCount())) {
                    reduceRecord.setOrderCount(Long.valueOf(item.getOrderCount()));
                }
                reduceDetail.add(reduceRecord);
            }
        }
    }

    private void handleGatherItems(OverviewSaleDTO response, List<PayRecord> salesDetail) {
        List<AmountItemDTO> gathers = response.getGatherItems();
        if (CollectionUtils.isNotEmpty(gathers)) {
            for (AmountItemDTO item : gathers) {
                PayRecord payRecord = new PayRecord();
                if (StringUtils.hasText(item.getAmount()) &&
                        !EncryptionSymbolUtil.isEncryption(item.getAmount())) {
                    payRecord.setAmount(new BigDecimal(item.getAmount()));
                }
                payRecord.setAmountStr(item.getAmount());
                payRecord.setPayName(PaymentTypeEnum.PaymentType.getLocaleName(item.getName()));
                if (StringUtils.hasText(item.getExcessAmount()) &&
                        !EncryptionSymbolUtil.isEncryption(item.getExcessAmount())) {
                    payRecord.setExcessAmount(new BigDecimal(item.getExcessAmount()));
                }
                if (StringUtils.hasText(item.getEstimatedAmount()) &&
                        !EncryptionSymbolUtil.isEncryption(item.getEstimatedAmount())) {
                    payRecord.setEstimatedAmount(new BigDecimal(item.getEstimatedAmount()));
                }
                payRecord.setEstimatedAmountStr(item.getEstimatedAmount());
                payRecord.setOrderCount(0L);
                payRecord.setOrderCountStr(item.getOrderCount());
                if (EncryptionSymbolUtil.isNormal(item.getOrderCount())) {
                    payRecord.setOrderCount(Long.valueOf(item.getOrderCount()));
                }
                payRecord.setGrouponCount(0L);
                payRecord.setGrouponCountStr(item.getGrouponCount());
                if (EncryptionSymbolUtil.isNormal(item.getGrouponCount())) {
                    payRecord.setGrouponCount(Long.valueOf(item.getGrouponCount()));
                }
                payRecord.setIsGroupon(item.getIsGroupon());
                payRecord.setDiscountAmount(item.getDiscountAmount());
                setInnerDetails(item, payRecord);
                salesDetail.add(payRecord);
            }
        }
    }

    private static void setInnerDetails(AmountItemDTO item, PayRecord payRecord) {
        if (CollectionUtils.isNotEmpty(item.getInnerDetails())) {
            List<com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO.InnerDetails> printInnerDetails
                    = new ArrayList<>(item.getInnerDetails().size());
            for (AmountItemDTO.InnerDetails innerDetail : item.getInnerDetails()) {
                com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO.InnerDetails printInnerDetail
                        = new com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO.InnerDetails();
                printInnerDetail.setName(innerDetail.getName());
                printInnerDetail.setAmount(innerDetail.getAmount());
                printInnerDetail.setDiscountAmount(innerDetail.getDiscountAmount());
                printInnerDetail.setOrderCount(0);
                if (EncryptionSymbolUtil.isNormal(innerDetail.getOrderCount())) {
                    printInnerDetail.setOrderCount(Integer.valueOf(innerDetail.getOrderCount()));
                }
                printInnerDetail.setGrouponCount(0);
                if (EncryptionSymbolUtil.isNormal(innerDetail.getGrouponCount())) {
                    printInnerDetail.setGrouponCount(Integer.valueOf(innerDetail.getGrouponCount()));
                }
                printInnerDetail.setIsGroupon(innerDetail.getIsGroupon());
                printInnerDetails.add(printInnerDetail);
            }
            payRecord.setInnerDetails(printInnerDetails);
        }
    }

    /**
     * 收款统计打印数据组装
     */
    private PrintReceiptStatsDTO getGatherPrintInfo(List<GatherSaleDTO> response, String storeName, long startTimeMills, long endTimeMills) {
        PrintReceiptStatsDTO printDTO = new PrintReceiptStatsDTO();
        List<PayRecord> receiptDetail = new ArrayList<>();
        List<PayRecordStats> receiptStatsDetail = Lists.newArrayList();
        List<GatherSaleDTO> details = response.stream()
                .filter(e -> e.getIsTotal() != BooleanEnum.TRUE.getCode())
                .collect(Collectors.toList());
        for (GatherSaleDTO detail : details) {
            PayRecord payRecord = new PayRecord();
            payRecord.setPayName(PaymentTypeEnum.PaymentType.getLocaleName(detail.getGatherName()));
            payRecord.setAmount(BigDecimal.ZERO);
            payRecord.setAmountStr(detail.getTotalAmount());
            if (EncryptionSymbolUtil.isNormal(payRecord.getAmountStr())) {
                payRecord.setAmount(new BigDecimal(detail.getTotalAmount()));
            }
            payRecord.setExcessAmount(BigDecimal.ZERO);
            payRecord.setExcessAmountStr(detail.getExcessAmount());
            if (EncryptionSymbolUtil.isNormal(payRecord.getExcessAmountStr())) {
                payRecord.setExcessAmount(new BigDecimal(detail.getExcessAmount()));
            }
            receiptDetail.add(payRecord);
            PayRecordStats payRecordStats = new PayRecordStats();
            payRecordStats.setPayName(PaymentTypeEnum.PaymentType.getLocaleName(detail.getGatherName()));
            payRecordStats.setSalesIncome(BigDecimal.ZERO);
            payRecordStats.setSalesIncomeStr(detail.getConsumerAmount());
            if (EncryptionSymbolUtil.isNormal(payRecordStats.getSalesIncomeStr())) {
                payRecordStats.setSalesIncome(new BigDecimal(payRecordStats.getSalesIncomeStr()));
            }
            payRecordStats.setRechargeIncome(BigDecimal.ZERO);
            payRecordStats.setRechargeIncomeStr(detail.getPrepaidAmount());
            if (EncryptionSymbolUtil.isNormal(payRecordStats.getRechargeIncomeStr())) {
                payRecordStats.setRechargeIncome(new BigDecimal(payRecordStats.getRechargeIncomeStr()));
            }
            payRecordStats.setReserveIncome(BigDecimal.ZERO);
            payRecordStats.setReserveIncomeStr(detail.getReserveAmount());
            if (EncryptionSymbolUtil.isNormal(payRecordStats.getReserveIncomeStr())) {
                payRecordStats.setReserveIncome(new BigDecimal(payRecordStats.getReserveIncomeStr()));
            }
            payRecordStats.setExcessAmount(BigDecimal.ZERO);
            payRecordStats.setExcessAmountStr(detail.getExcessAmount());
            if (EncryptionSymbolUtil.isNormal(payRecordStats.getExcessAmountStr())) {
                payRecordStats.setExcessAmount(new BigDecimal(payRecordStats.getExcessAmountStr()));
            }
            payRecordStats.setInnerDetails(detail.getInnerDetails());
            receiptStatsDetail.add(payRecordStats);
        }
        printDTO.setReceiptDetail(receiptDetail);
        printDTO.setReceiptStatsDetail(receiptStatsDetail);
        printDTO.setReceiptTotal(BigDecimal.ZERO);
        GatherSaleDTO totalGatherSaleDTO = response.stream()
                .filter(e -> e.getIsTotal() == BooleanEnum.TRUE.getCode())
                .findFirst()
                .orElse(new GatherSaleDTO());
        printDTO.setReceiptTotal(new BigDecimal(totalGatherSaleDTO.getTotalAmount()));
        //门店名称
        printDTO.setStoreName(storeName);
        //开始时间
        printDTO.setStartTime(startTimeMills);
        //结束时间
        printDTO.setEndTime(endTimeMills);
        return printDTO;
    }

    /**
     * 会员消费统计打印数据组装
     */
    private PrintMemStatsDTO getMemberConsumePrintInfo(MemberConsumeRespDTO response, String storeName, long startTimeMills, long endTimeMills) {
        PrintMemStatsDTO printDTO = new PrintMemStatsDTO();
        printDTO.setConsumeQuantity(Long.valueOf(response.getConsumerCount()));
        printDTO.setConsumeTotal(response.getConsumerAmount());
        printDTO.setRechargeQuantity(response.getPrepaidCount() == null ? 0L : Long.valueOf(response.getPrepaidCount()));
        printDTO.setRechargeTotal(response.getPrepaidAmount() == null ? BigDecimal.ZERO : response.getPrepaidAmount());
        printDTO.setRechargeGift(response.getPrepaidGiveAmount() == null ? BigDecimal.ZERO : response.getPrepaidGiveAmount());
        printDTO.setRechargeIncome(response.getPrepaidTakeInAmount() == null ? BigDecimal.ZERO : response.getPrepaidTakeInAmount());
        List<PayRecord> rechargeDetail = new ArrayList<>();
        List<com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO> prepaidList = response.getPrepaidItems();
        if (CollectionUtils.isNotEmpty(prepaidList)) {
            for (com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO item : prepaidList) {
                PayRecord payRecord = new PayRecord();
                payRecord.setPayName(PaymentTypeEnum.PaymentType.getLocaleName(item.getName()));
                payRecord.setAmount(item.getAmount());
                payRecord.setExcessAmount(item.getExcessAmount());
                rechargeDetail.add(payRecord);
            }
        }
        printDTO.setRechargeDetail(rechargeDetail);
        //门店名称
        printDTO.setStoreName(storeName);
        //开始时间
        printDTO.setStartTime(startTimeMills);
        //结束时间
        printDTO.setEndTime(endTimeMills);
        return printDTO;
    }

    /**
     * 用餐类型统计打印数据组装
     */
    private PrintTradeStatsDTO getDiningTypePrintInfo(List<DiningTypeSaleDTO> response, String storeName, long startTimeMills, long endTimeMills) {
        PrintTradeStatsDTO printDTO = new PrintTradeStatsDTO();
        List<DiningTypeSaleDTO> details = response.stream()
                .filter(e -> e.getIsTotal() != BooleanEnum.TRUE.getCode())
                .collect(Collectors.toList());
        for (DiningTypeSaleDTO item : details) {
            if (String.valueOf(DiningTypeEnum.DINE.getCode()).equals(item.getTypeCode())) {
                // 正餐
                printDTO.setDineMode(buildPrintStatsDineSnackMode(item));
            } else if (String.valueOf(DiningTypeEnum.SNACK.getCode()).equals(item.getTypeCode())) {
                //快餐
                printDTO.setSnackMode(buildPrintStatsDineSnackMode(item));
            } else if (String.valueOf(DiningTypeEnum.TAKEOUT.getCode()).equals(item.getTypeCode())) {
                //外卖
                printDTO.setTakeoutTypeName(item.getTypeName());
                printDTO.setTakeoutMode(buildPrintStatsTakeawayMode(item));
            }
        }
        printDTO.setOrderQuantity(0L);
        printDTO.setCustomerNumber(0L);
        printDTO.setSalesIncome(BigDecimal.ZERO);
        DiningTypeSaleDTO totalDiningTypeSaleDTO = response.stream()
                .filter(e -> e.getIsTotal() == BooleanEnum.TRUE.getCode())
                .findFirst()
                .orElse(null);
        if (totalDiningTypeSaleDTO != null) {
            printDTO.setOrderQuantityStr(totalDiningTypeSaleDTO.getOrderCount());
            if (EncryptionSymbolUtil.isNormal(printDTO.getOrderQuantityStr())) {
                printDTO.setOrderQuantity(Long.valueOf(printDTO.getOrderQuantityStr()));
            }
            printDTO.setSalesIncomeStr(totalDiningTypeSaleDTO.getAmount());
            if (EncryptionSymbolUtil.isNormal(printDTO.getSalesIncomeStr())) {
                printDTO.setSalesIncome(new BigDecimal(printDTO.getSalesIncomeStr()));
            }
            printDTO.setCustomerNumberStr(totalDiningTypeSaleDTO.getGuestCount());
            if (EncryptionSymbolUtil.isNormal(printDTO.getCustomerNumberStr())) {
                printDTO.setCustomerNumber(Long.valueOf(printDTO.getCustomerNumberStr()));
            }
            printDTO.setOrderAvgConsumeStr(totalDiningTypeSaleDTO.getOrderPrice());
        }
        //门店名称
        printDTO.setStoreName(storeName);
        //开始时间
        printDTO.setStartTime(startTimeMills);
        //结束时间
        printDTO.setEndTime(endTimeMills);
        return printDTO;
    }

    /**
     * 构建正餐/快餐用餐方式统计
     */
    private PrintTradeStatsDTO.DineSnackTradeStats buildPrintStatsDineSnackMode(DiningTypeSaleDTO item) {
        PrintTradeStatsDTO.DineSnackTradeStats mode = new PrintTradeStatsDTO.DineSnackTradeStats();
        mode.setOrderQuantity(0L);
        mode.setOrderQuantityStr(item.getOrderCount());
        mode.setTypeName(item.getTypeName());
        if (EncryptionSymbolUtil.isNormal(mode.getOrderQuantityStr())) {
            mode.setOrderQuantity(Long.valueOf(mode.getOrderQuantityStr()));
        }
        mode.setSalesIncome(BigDecimal.ZERO);
        mode.setSalesIncomeStr(item.getAmount());
        if (EncryptionSymbolUtil.isNormal(mode.getSalesIncomeStr())) {
            mode.setSalesIncome(new BigDecimal(mode.getSalesIncomeStr()));
        }
        mode.setOrderAvgConsume(BigDecimal.ZERO);
        mode.setOrderAvgConsumeStr(item.getOrderPrice());
        if (EncryptionSymbolUtil.isNormal(mode.getOrderAvgConsumeStr())) {
            mode.setOrderAvgConsume(new BigDecimal(mode.getOrderAvgConsumeStr()));
        }
        mode.setCustomerNumber(0L);
        mode.setCustomerNumberStr(item.getGuestCount());
        if (EncryptionSymbolUtil.isNormal(mode.getCustomerNumberStr())) {
            mode.setCustomerNumber(Long.valueOf(mode.getCustomerNumberStr()));
        }
        mode.setCustomerAvgConsume(BigDecimal.ZERO);
        mode.setCustomerAvgConsumeStr(item.getGuestPrice());
        if (EncryptionSymbolUtil.isNormal(mode.getCustomerAvgConsumeStr())) {
            mode.setCustomerAvgConsume(new BigDecimal(mode.getCustomerAvgConsumeStr()));
        }
        return mode;
    }

    /**
     * 构建外卖用餐方式统计
     */
    private List<PrintTradeStatsDTO.TakeoutTradeStats> buildPrintStatsTakeawayMode(DiningTypeSaleDTO item) {
        List<DiningTypeSaleDTO> subs = Optional.ofNullable(item.getSubDiningTypes()).orElse(Lists.newArrayList());
        List<PrintTradeStatsDTO.TakeoutTradeStats> takeoutMode = new ArrayList<>();
        for (DiningTypeSaleDTO sub : subs) {
            PrintTradeStatsDTO.TakeoutTradeStats ext2 = new PrintTradeStatsDTO.TakeoutTradeStats();
            ext2.setTypeName(sub.getTypeName());
            ext2.setPlatformName(sub.getTypeName());
            ext2.setOrderQuantity(0L);
            ext2.setOrderQuantityStr(sub.getOrderCount());
            if (EncryptionSymbolUtil.isNormal(ext2.getOrderQuantityStr())) {
                ext2.setOrderQuantity(Long.valueOf(ext2.getOrderQuantityStr()));
            }
            ext2.setSalesIncome(BigDecimal.ZERO);
            ext2.setSalesIncomeStr(sub.getAmount());
            if (EncryptionSymbolUtil.isNormal(ext2.getSalesIncomeStr())) {
                ext2.setSalesIncome(new BigDecimal(ext2.getSalesIncomeStr()));
            }
            ext2.setOrderAvgConsume(BigDecimal.ZERO);
            ext2.setOrderAvgConsumeStr(sub.getOrderPrice());
            if (EncryptionSymbolUtil.isNormal(ext2.getOrderAvgConsumeStr())) {
                ext2.setOrderAvgConsume(new BigDecimal(ext2.getOrderAvgConsumeStr()));
            }
            takeoutMode.add(ext2);
        }
        return takeoutMode;
    }

    /**
     * 分类销售统计打印数据组装
     */
    private PrintTypeStatsDTO getClassifyPrintInfo(List<CategorySaleDTO> response, String storeName, long startTimeMills, long endTimeMills) {
        PrintTypeStatsDTO printDTO = new PrintTypeStatsDTO();
        List<PrintTypeStatsDTO.ItemType> itemTypeList = new ArrayList<>();
        for (CategorySaleDTO item : response) {
            if (item.getIsTotal() == 1) {
                continue;
            }
            PrintTypeStatsDTO.ItemType itemType = new PrintTypeStatsDTO.ItemType();
            itemType.setName(item.getName());
            itemType.setMoney(BigDecimal.ZERO);
            itemType.setMoneyStr(item.getAmount());
            if (EncryptionSymbolUtil.isNormal(item.getAmount())) {
                itemType.setMoney(new BigDecimal(item.getAmount()));
            }
            itemType.setPayMoney(BigDecimal.ZERO);
            itemType.setPayMoneyStr(item.getDiscountAmount());
            if (EncryptionSymbolUtil.isNormal(item.getDiscountAmount())) {
                itemType.setPayMoney(new BigDecimal(item.getDiscountAmount()));
            }
            itemType.setQuantity(BigDecimal.ZERO);
            itemType.setQuantityStr(item.getQuantum());
            if (EncryptionSymbolUtil.isNormal(item.getQuantum())) {
                itemType.setQuantity(new BigDecimal(item.getQuantum()));
            }
            itemTypeList.add(itemType);
        }
        printDTO.setItemTypeList(itemTypeList);
        //门店名称
        printDTO.setStoreName(storeName);
        //开始时间
        printDTO.setStartTime(startTimeMills);
        //结束时间
        printDTO.setEndTime(endTimeMills);
        return printDTO;
    }

    /**
     * 商品销售统计数据组装
     */
    private PrintItemStatsDTO getGoodsSalePrintInfo(List<GoodsSaleDTO> response, String storeName, long startTimeMills, long endTimeMills) {
        List<GoodsSaleDTO> printVOList = buildGoodsSalePrintVOList(response);
        PrintItemStatsDTO printDTO = new PrintItemStatsDTO();
        List<GoodsSaleDTO> filterPrintVoList = printVOList.stream()
                .filter(e -> e.getIsTotal() != 1)
                .collect(Collectors.toList());
        List<PrintItemStatsDTO.Item> itemList = new ArrayList<>();
        for (GoodsSaleDTO goodsSaleDTO : filterPrintVoList) {
            PrintItemStatsDTO.Item printItem = buildPrintItemStatsItemDTO(goodsSaleDTO.getName(), goodsSaleDTO.getAmount(),
                    goodsSaleDTO.getQuantum(), goodsSaleDTO.getItemType());
            itemList.add(printItem);
        }
        printDTO.setItemList(itemList);
        //门店名称
        printDTO.setStoreName(storeName);
        //开始时间
        printDTO.setStartTime(startTimeMills);
        //结束时间
        printDTO.setEndTime(endTimeMills);
        return printDTO;
    }


    /**
     * 构建小票打印明细DTO
     */
    private PrintItemStatsDTO.Item buildPrintItemStatsItemDTO(String name, String amount, String quantum, Integer itemType) {
        PrintItemStatsDTO.Item printItem = new PrintItemStatsDTO.Item();
        printItem.setName(name);
        printItem.setMoneyStr(amount);
        printItem.setMoney(BigDecimal.ZERO);
        if (EncryptionSymbolUtil.isNormal(amount)) {
            printItem.setMoney(new BigDecimal(amount));
        }
        printItem.setQuantityStr(quantum);
        printItem.setQuantity(BigDecimal.ZERO);
        if (EncryptionSymbolUtil.isNormal(quantum)) {
            printItem.setQuantity(new BigDecimal(quantum));
        }
        printItem.setIsPackage(false);
        if (itemType != null && itemType == 1) {
            printItem.setIsPackage(true);
        }
        printItem.setIsWeight(false);
        if (itemType != null && itemType == 3) {
            printItem.setIsWeight(true);
        }
        return printItem;
    }

    /**
     * 构建打印单 商品列表
     */
    private List<GoodsSaleDTO> buildGoodsSalePrintVOList(List<GoodsSaleDTO> itemList) {
        List<GoodsSaleDTO> printVOList = new ArrayList<>();
        itemList.forEach(s -> {
            if (s.getSubs() != null) {
                List<GoodsSaleDTO> subs = s.getSubs();
                subs.forEach(a -> {
                    a.setName(s.getName());
                    if (EncryptionSymbolUtil.isNormal(a.getSkuName())) {
                        a.setName(s.getName() + "(" + a.getSkuName() + ")");
                    }
                });
                printVOList.addAll(subs);
            } else if (EncryptionSymbolUtil.isNormal(s.getSkuName())) {
                s.setName(s.getName() + "(" + s.getSkuName() + ")");
                printVOList.add(s);
            } else {
                printVOList.add(s);
            }
        });
        return printVOList;
    }

    /**
     * 分类销售统计数据组装
     */
    private PrintItemStatsDTO getCategorySalePrintInfo(List<ReturnSaleDTO> response, String storeName, long startTimeMills, long endTimeMills) {
        List<ReturnSaleDTO> printVOList = buildReturnSalePrintVOList(response);
        PrintItemStatsDTO printDTO = new PrintItemStatsDTO();
        List<ReturnSaleDTO> filterPrintVoList = printVOList.stream()
                .filter(e -> e.getIsTotal() != 1)
                .collect(Collectors.toList());
        List<PrintItemStatsDTO.Item> itemList = new ArrayList<>();
        for (ReturnSaleDTO returnSaleDTO : filterPrintVoList) {
            PrintItemStatsDTO.Item printItem = buildPrintItemStatsItemDTO(returnSaleDTO.getName(), returnSaleDTO.getAmount(),
                    returnSaleDTO.getQuantum(), returnSaleDTO.getItemType());
            itemList.add(printItem);
        }
        printDTO.setItemList(itemList);
        //门店名称
        printDTO.setStoreName(storeName);
        //开始时间
        printDTO.setStartTime(startTimeMills);
        //结束时间
        printDTO.setEndTime(endTimeMills);
        return printDTO;
    }

    /**
     * 构建打印单 退菜列表
     */
    private List<ReturnSaleDTO> buildReturnSalePrintVOList(List<ReturnSaleDTO> itemList) {
        List<ReturnSaleDTO> printVOList = new ArrayList<>();
        itemList.forEach(s -> {
            if (s.getSubs() != null) {
                List<ReturnSaleDTO> subs = s.getSubs();
                subs.forEach(a -> {
                    a.setName(s.getName());
                    if (EncryptionSymbolUtil.isNormal(a.getSkuName())) {
                        a.setName(s.getName() + "(" + a.getSkuName() + ")");
                    }
                });
                printVOList.addAll(subs);
            } else if (EncryptionSymbolUtil.isNormal(s.getSkuName())) {
                s.setName(s.getName() + "(" + s.getSkuName() + ")");
                printVOList.add(s);
            } else {
                printVOList.add(s);
            }
        });
        return printVOList;
    }


    /**
     * 构建打印单 赠菜列表
     */
    private PrintItemStatsDTO getGiftSalePrintInfo(List<GiftSaleDTO> response, String storeName, long startTimeMills, long endTimeMills) {
        List<GiftSaleDTO> printVo = buildGiftSalePrintVOList(response);
        PrintItemStatsDTO printDTO = new PrintItemStatsDTO();
        List<PrintItemStatsDTO.Item> itemList = new ArrayList<>();
        List<GiftSaleDTO> filterPrintVoList = printVo.stream()
                .filter(e -> e.getIsTotal() != 1)
                .collect(Collectors.toList());
        for (GiftSaleDTO giftSaleDTO : filterPrintVoList) {
            PrintItemStatsDTO.Item printItem = buildPrintItemStatsItemDTO(giftSaleDTO.getName(), giftSaleDTO.getAmount(),
                    giftSaleDTO.getQuantum(), giftSaleDTO.getItemType());
            itemList.add(printItem);
        }
        printDTO.setItemList(itemList);
        //门店名称
        printDTO.setStoreName(storeName);
        //开始时间
        printDTO.setStartTime(startTimeMills);
        //结束时间
        printDTO.setEndTime(endTimeMills);
        return printDTO;
    }

    /**
     * 构建打印单 赠菜列表
     */
    private List<GiftSaleDTO> buildGiftSalePrintVOList(List<GiftSaleDTO> itemList) {
        List<GiftSaleDTO> printVOList = new ArrayList<>();
        itemList.forEach(s -> {
            if (s.getSubs() != null) {
                List<GiftSaleDTO> subs = s.getSubs();
                for (GiftSaleDTO a : subs) {
                    a.setName(s.getName());
                    if (EncryptionSymbolUtil.isNormal(a.getSkuName())) {
                        a.setName(s.getName() + "(" + a.getSkuName() + ")");
                    }
                }
                printVOList.addAll(subs);
            } else if (EncryptionSymbolUtil.isNormal(s.getSkuName())) {
                s.setName(s.getName() + "(" + s.getSkuName() + ")");
                printVOList.add(s);
            } else {
                printVOList.add(s);
            }
        });
        return printVOList;
    }

    /**
     * 属性销售统计打印数据组装
     */
    private PrintPropStatsDTO getAttrPrintInfo(PropStatsSaleDTO response, String storeName, long startTimeMills, long endTimeMills) {
        PrintPropStatsDTO printDTO = new PrintPropStatsDTO();
        //门店名称
        printDTO.setStoreName(storeName);
        //开始时间
        printDTO.setStartTime(startTimeMills);
        //结束时间
        printDTO.setEndTime(endTimeMills);
        List<PropStatsSaleDTO.PropGroup> propGroupList = response.getPropGroupList();
        if (CollectionUtils.isEmpty(propGroupList)) {
            return printDTO;
        }
        List<PrintPropStatsDTO.PropGroup> printPropGroupList = new ArrayList<>();
        for (PropStatsSaleDTO.PropGroup item : propGroupList) {
            PrintPropStatsDTO.PropGroup pp = new PrintPropStatsDTO.PropGroup();
            pp.setName(item.getName());
            List<PropStatsSaleDTO.PropItem> subs = item.getPropList();
            if (CollectionUtils.isEmpty(subs)) {
                printPropGroupList.add(pp);
                continue;
            }
            List<PrintPropStatsDTO.PropItem> pSubs = new ArrayList<>();
            for (PropStatsSaleDTO.PropItem sub : subs) {
                PrintPropStatsDTO.PropItem ppi = new PrintPropStatsDTO.PropItem();
                ppi.setName(sub.getName());
                ppi.setMoney(BigDecimal.ZERO);
                ppi.setMoneyStr(sub.getMoney());
                if (EncryptionSymbolUtil.isNormal(sub.getMoney())) {
                    ppi.setMoney(new BigDecimal(sub.getMoney()));
                }
                ppi.setQuantity(0L);
                ppi.setQuantityStr(sub.getQuantity());
                if (EncryptionSymbolUtil.isNormal(sub.getQuantity())) {
                    ppi.setQuantity(Long.valueOf(sub.getQuantity()));
                }
                pSubs.add(ppi);
            }
            pp.setPropList(pSubs);
            printPropGroupList.add(pp);
        }
        printDTO.setPropGroupList(printPropGroupList);
        return printDTO;
    }

    /**
     * 会员充值统计打印数据组装（拆分）
     */
    private PrintRechargeStatsDTO getMemberRechargePrintInfo(MemberRechargeSaleDTO response, String storeName, long startTimeMills, long endTimeMills) {
        PrintRechargeStatsDTO printDTO = new PrintRechargeStatsDTO();
        // 充值单数
        printDTO.setRechargeQuantity(0L);
        printDTO.setRechargeQuantityStr(response.getRechargeNum());
        if (EncryptionSymbolUtil.isNormal(printDTO.getRechargeQuantityStr())) {
            printDTO.setRechargeQuantity(Long.valueOf(printDTO.getRechargeQuantityStr()));
        }
        // 充值金额
        printDTO.setRechargeTotal(BigDecimal.ZERO);
        printDTO.setRechargeTotalStr(response.getRechargeAmount());
        if (EncryptionSymbolUtil.isNormal(printDTO.getRechargeTotalStr())) {
            printDTO.setRechargeTotal(new BigDecimal(printDTO.getRechargeTotalStr()));
        }
        // 充值赠送
        printDTO.setRechargeGift(BigDecimal.ZERO);
        printDTO.setRechargeGiftStr(response.getPresentAmount());
        if (EncryptionSymbolUtil.isNormal(printDTO.getRechargeGiftStr())) {
            printDTO.setRechargeGift(new BigDecimal(printDTO.getRechargeGiftStr()));
        }
        // 充值收入
        printDTO.setRechargeIncome(BigDecimal.ZERO);
        printDTO.setRechargeIncomeStr(response.getIncomeAmount());
        if (EncryptionSymbolUtil.isNormal(printDTO.getRechargeIncomeStr())) {
            printDTO.setRechargeIncome(new BigDecimal(printDTO.getRechargeIncomeStr()));
        }
        // 充值人数
        printDTO.setRechargeMemberNum(0);
        printDTO.setRechargeMemberNumStr(response.getRechargeMemberNum());
        if (EncryptionSymbolUtil.isNormal(printDTO.getRechargeMemberNumStr())) {
            printDTO.setRechargeMemberNum(Integer.valueOf(printDTO.getRechargeMemberNumStr()));
        }
        List<PayRecord> rechargeDetail = new ArrayList<>();
        List<ResponsePayWayDetail> payWayList = response.getPayWayDetailList();
        if (CollectionUtils.isNotEmpty(payWayList)) {
            for (ResponsePayWayDetail payWay : payWayList) {
                PayRecord payRecord = new PayRecord();
                payRecord.setPayName(payWay.getPayName());
                payRecord.setAmount(payWay.getPayAmount());
                rechargeDetail.add(payRecord);
            }
        }
        printDTO.setRechargeDetail(rechargeDetail);
        //门店名称
        printDTO.setStoreName(storeName);
        //开始时间
        printDTO.setStartTime(startTimeMills);
        //结束时间
        printDTO.setEndTime(endTimeMills);
        return printDTO;
    }

    /**
     * 会员消费统计打印数据组装（拆分）
     */
    private PrintConsumeStatsDTO getMemberConsumeOnlyPrintInfo(MemberConsumeSaleDTO response, String storeName, long startTimeMills, long endTimeMills) {
        PrintConsumeStatsDTO printDTO = new PrintConsumeStatsDTO();
        // 消费单数
        printDTO.setConsumeQuantity(0L);
        printDTO.setConsumeQuantityStr(response.getConsumptionNum());
        if (EncryptionSymbolUtil.isNormal(printDTO.getConsumeQuantityStr())) {
            printDTO.setConsumeQuantity(Long.valueOf(printDTO.getConsumeQuantityStr()));
        }
        // 消费金额
        printDTO.setConsumeTotal(BigDecimal.ZERO);
        printDTO.setConsumeTotalStr(response.getConsumptionAmount());
        if (EncryptionSymbolUtil.isNormal(printDTO.getConsumeTotalStr())) {
            printDTO.setConsumeTotal(new BigDecimal(printDTO.getConsumeTotalStr()));
        }
        // 消费人数
        printDTO.setConsumeMemberNum(0);
        printDTO.setConsumeMemberNumStr(response.getConsumptionMemberNum());
        if (EncryptionSymbolUtil.isNormal(printDTO.getConsumeMemberNumStr())) {
            printDTO.setConsumeMemberNum(Integer.valueOf(printDTO.getConsumeMemberNumStr()));
        }
        List<PayRecord> consumeDetail = new ArrayList<>();
        List<ResponsePayWayDetail> payWayList = response.getPayWayDetailList();
        if (CollectionUtils.isNotEmpty(payWayList)) {
            for (ResponsePayWayDetail payWay : payWayList) {
                PayRecord payRecord = new PayRecord();
                payRecord.setPayName(payWay.getPayName());
                payRecord.setAmount(payWay.getPayAmount());
                if (PaymentTypeEnum.THIRD_ACTIVITY.getCode() == payWay.getPayWay()) {
                    payRecord.setExcessAmount(payWay.getExcessAmount());
                }
                consumeDetail.add(payRecord);
            }
        }
        printDTO.setConsumeDetail(consumeDetail);
        //门店名称
        printDTO.setStoreName(storeName);
        //开始时间
        printDTO.setStartTime(startTimeMills);
        //结束时间
        printDTO.setEndTime(endTimeMills);
        return printDTO;
    }


    /**
     * 基础打印数据设置
     */
    private void setBasePrintInfo(DailyPrintReqDTO request, UserContext userInfo, PrintDTO printDTO, Integer invoiceType) {
        //打印票据类型
        printDTO.setInvoiceType(invoiceType);
        //设备编号
        printDTO.setDeviceId(request.getDeviceId());
        //打印来源
        printDTO.setPrintSourceEnum(PrintSourceEnum.getPrintSourceByDeviceType(request.getDeviceType()));
        //打印UID
        printDTO.setPrintUid(System.currentTimeMillis() + "");
        printDTO.setEnterpriseGuid(userInfo.getEnterpriseGuid());
        printDTO.setStoreGuid(userInfo.getStoreGuid());
        //打印区域，正餐、微信点餐模式下该值不能为空
        printDTO.setAreaGuid(null);
        printDTO.setOperatorStaffGuid(userInfo.getUserGuid());
        printDTO.setOperatorStaffName(userInfo.getUserName());
        //打印时间
        printDTO.setCreateTime(DateTimeUtils.nowMillis());
    }

}