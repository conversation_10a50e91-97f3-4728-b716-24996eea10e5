package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.HsmMemberConsumeClientService.MemberConsumeClientServiceFallback;
import com.holderzone.holder.saas.member.dto.common.request.MemberConsumeOrderSyncSaveReqDTO;
import com.holderzone.holder.saas.member.dto.account.request.MemberConsumeRecordQueryReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberConsumeBaseStatisticsRespDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberConsumeRecordListRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberConsumeClientService
 * @date 2019/05/30 14:12
 * @description 会员消费
 * @program holder-saas-member-account
 */
@Component
@FeignClient(name = "holder-saas-member-account", fallbackFactory = MemberConsumeClientServiceFallback.class)
public interface HsmMemberConsumeClientService {

    /**
     * 根据条件查询会员消费记录
     *
     * @param queryReqDTO 查询条件
     * @return 查询结果
     */
    @PostMapping(value = "/hsm_member_consume/listByCondition", produces = "application/json;charset=utf-8")
    @ApiOperation("根据条件查询会员消费记录")
    Page<MemberConsumeRecordListRespDTO> listByCondition(
        @RequestBody MemberConsumeRecordQueryReqDTO queryReqDTO);


    /**
     * 同步消费记录
     *
     * @param syncSaveReqDTO 保存消费记录
     * @return 保存结果 true成功 false失败
     */
    @PostMapping(value = "/hsm_member_consume/syncConsumeRecord", produces = "application/json;charset=utf-8")
    @ApiOperation("同步消费记录")
    boolean syncConsumeRecord(@RequestBody MemberConsumeOrderSyncSaveReqDTO syncSaveReqDTO);

    /**
     * 通过会员guid统计会员消费数据
     *
     * @param memberGuid 会员guid
     * @return 会员消费数据
     */
    @RequestMapping(value = "/hsm_member_consume/syncConsumeRecord", produces = "application/json;charset=utf-8")
    MemberConsumeBaseStatisticsRespDTO statisticsConsumeBaseData(
        @RequestParam(value = "memberGuid") String memberGuid);


    @Component
    class MemberConsumeClientServiceFallback implements
            FallbackFactory<HsmMemberConsumeClientService> {

        private static final Logger LOGGER = LoggerFactory
                .getLogger(HsmMemberConsumeClientService.class);

        @Override
        public HsmMemberConsumeClientService create(Throwable throwable) {
            return new HsmMemberConsumeClientService() {
                @Override
                public Page<MemberConsumeRecordListRespDTO> listByCondition(
                        MemberConsumeRecordQueryReqDTO queryReqDTO) {
                    LOGGER.error("查询会员消费记录错误：{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public boolean syncConsumeRecord(MemberConsumeOrderSyncSaveReqDTO syncSaveReqDTO) {
                    LOGGER.error("同步会员消费记录错误：{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public MemberConsumeBaseStatisticsRespDTO statisticsConsumeBaseData(
                        String memberGuid) {
                    LOGGER.error("查询会员消费基础数据统计错误：{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }
}
