package com.holderzone.holder.saas.aggregation.merchant.util;

import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CurrentHttpRequestUtils
 * @date 2018/09/28 下午8:53
 * @description //TODO
 * @program holder-saas-aggregation-merchant
 */
public class CurrentHttpRequestUtils {

    public static String getRequestUri(){
        return getRequest().getRequestURI();
    }

    public static HttpServletRequest getRequest(){
        RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();
        return ((ServletRequestAttributes) requestAttributes).getRequest();
    }

    public static HttpServletResponse getRespnse(){
        RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();
        return ((ServletRequestAttributes) requestAttributes).getResponse();
    }
}
