package com.holderzone.holder.saas.aggregation.app.controller.kds;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.kds.KdsPrintRecordRpcService;
import com.holderzone.saas.store.dto.kds.req.KdsPrintRecordReqDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsPrintOrderDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsPrintRecordDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@Api("KDS设备接口")
@RequestMapping("/kds_print_record")
public class KdsPrintRecordController {

    private final KdsPrintRecordRpcService kdsPrintRecordRpcService;

    @Autowired
    public KdsPrintRecordController(KdsPrintRecordRpcService kdsPrintRecordRpcService) {
        this.kdsPrintRecordRpcService = kdsPrintRecordRpcService;
    }

    @PostMapping("/get_order")
    @ApiOperation("打印内容获取接口")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "打印内容获取接口")
    public Result<List<KdsPrintOrderDTO>> getPrinterOrder(@RequestBody KdsPrintRecordReqDTO printRecordReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("打印内容获取接口入参：{}", JacksonUtils.writeValueAsString(printRecordReqDTO));
        }
        return Result.buildSuccessResult(kdsPrintRecordRpcService.getPrintOrder(printRecordReqDTO));
    }

    @PostMapping("/list")
    @ApiOperation("获取打印结果的列表，入参status: 0=打印中，1=成功，2=失败")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "获取打印结果的列表")
    public Result<List<KdsPrintRecordDTO>> listRecord(@RequestBody KdsPrintRecordReqDTO printRecordReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("获取打印结果的列表入参：{}", JacksonUtils.writeValueAsString(printRecordReqDTO));
        }
        return Result.buildSuccessResult(kdsPrintRecordRpcService.listRecord(printRecordReqDTO));
    }

    @PostMapping("/update_status")
    @ApiOperation("打印结果更新")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "打印结果更新")
    public Result updateStatus(@RequestBody KdsPrintRecordReqDTO printRecordReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("打印结果更新入参：{}", JacksonUtils.writeValueAsString(printRecordReqDTO));
        }
        kdsPrintRecordRpcService.updateStatus(printRecordReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/delete")
    @ApiOperation("删除单条打印记录")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "删除单条打印记录")
    public Result deleteRecord(@RequestBody KdsPrintRecordReqDTO printRecordReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("删除单条打印记录入参：{}", JacksonUtils.writeValueAsString(printRecordReqDTO));
        }
        kdsPrintRecordRpcService.deleteRecord(printRecordReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/batch_delete")
    @ApiOperation("批量删除失败列表")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "批量删除失败列表")
    public Result batchDeleteRecord(@RequestBody KdsPrintRecordReqDTO printRecordReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("批量删除失败列表入参：printStatusDTO={}", JacksonUtils.writeValueAsString(printRecordReqDTO));
        }
        kdsPrintRecordRpcService.batchDeleteRecord(printRecordReqDTO);
        return Result.buildEmptySuccess();
    }
}

