package com.holderzone.holder.saas.aggregation.merchant.entity.enums;

import com.google.common.collect.Maps;
import lombok.Getter;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Map;
import java.util.ResourceBundle;

/**
 * <AUTHOR>
 * @create 2023-07-19
 * @description
 */
@Getter
public enum CommonLocaleEnum {

    NEW_CREATION_FAILED("新增失败:"),

    ALL_IN_ONE_DEVICE("一体机"),

    MERCHANT_BACKEND("商户后台"),

    MOBILE_APP("移动端"),

    DINE_IN_DISH_REFUND("正餐退菜"),

    SNACK_DISH_REFUND("快餐退菜"),

    REPAYMENT_EXCESS("挂账超额"),

    DISH_REFUND_FACE("退菜人脸"),

    REFUND_FACE("退款人脸"),

    ;


    private final String message;

    CommonLocaleEnum(String message){
        this.message = message;
    }

    private final static Map<String, CommonLocaleEnum> LOCALE_MAP;

    static {
        LOCALE_MAP = initMap();

    }

    private static Map<String, CommonLocaleEnum> initMap(){
        Map<String, CommonLocaleEnum> localeMap = Maps.newHashMap();
        for (CommonLocaleEnum commonLocaleEnum : CommonLocaleEnum.values()){
            localeMap.put(commonLocaleEnum.message,commonLocaleEnum);
        }
        return localeMap;
    }

    public static String getLocale(String message){
        CommonLocaleEnum commonLocaleEnum = LOCALE_MAP.get(message);
        return commonLocaleEnum == null ? message : getLocaleMessage(commonLocaleEnum);
    }

    private static String getLocaleMessage(CommonLocaleEnum commonLocaleEnum) {
        try {
            ResourceBundle bundle = ResourceBundle.getBundle("i18n/messages", LocaleContextHolder.getLocale());
            return bundle.getString(commonLocaleEnum.name());
        } catch (Exception e) {
            return commonLocaleEnum.getMessage();
        }
    }
}
