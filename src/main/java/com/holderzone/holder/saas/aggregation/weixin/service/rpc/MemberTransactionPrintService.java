package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.saas.store.dto.print.content.PrintStoredCashDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberTransactionPrintService
 * @date 2018/10/18 9:28
 * @description //TODO
 * @program holder-saas-store-member
 */
@Component
@FeignClient(name = "holder-saas-store-print", fallbackFactory = MemberTransactionPrintService.FallBack.class)
public interface MemberTransactionPrintService {
    @PostMapping("/print_record/send")
    public  String printMemberTransactionLog(PrintStoredCashDTO printStoredCashDto);

    @Component
    class FallBack implements FallbackFactory<MemberTransactionPrintService> {
        private static final Logger logger = LoggerFactory.getLogger(FallBack.class);

        @Override
        public MemberTransactionPrintService create(Throwable throwable) {
            return new MemberTransactionPrintService() {

                @Override
                public String printMemberTransactionLog(PrintStoredCashDTO printStoredCashDto) {
                    logger.error("e={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());

                }
            };
        }
    }
}
