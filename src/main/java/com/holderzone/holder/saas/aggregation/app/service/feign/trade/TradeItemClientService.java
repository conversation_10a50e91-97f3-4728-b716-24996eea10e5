package com.holderzone.holder.saas.aggregation.app.service.feign.trade;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.item.req.ItemBarCodeReqDTO;
import com.holderzone.saas.store.dto.print.PrintLabelReq;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = TradeItemClientService.TradeItemFallBack.class)
public interface TradeItemClientService {

    /**
     * 打印商品标签单
     */
    @PostMapping("/item/print/barCode")
    void printItemBarCode(@RequestBody ItemBarCodeReqDTO itemBarCodeReqDTO);

    /**
     * 商品标签重打
     */
    @PostMapping("/dine_in_order/reprint_label")
    void reprintLabel(@RequestBody PrintLabelReq printLabelReq);


    @Component
    @Slf4j
    class TradeItemFallBack implements FallbackFactory<TradeItemClientService> {
        @Override
        public TradeItemClientService create(Throwable throwable) {
            return new TradeItemClientService() {

                @Override
                public void reprintLabel(PrintLabelReq printLabelReq) {
                    log.error("商品标签重打FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("商品标签重打失败!" + throwable.getMessage());
                }

                @Override
                public void printItemBarCode(ItemBarCodeReqDTO itemBarCodeReqDTO) {
                    log.error("打印商品标签单FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("打印商品标签单失败!" + throwable.getMessage());
                }


            };
        }
    }
}
