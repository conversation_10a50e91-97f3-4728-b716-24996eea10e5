package com.holderzone.holder.saas.aggregation.weixin.service.impl;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.assembler.MarketingActivityAssembler;
import com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel;
import com.holderzone.holder.saas.aggregation.weixin.entity.dto.CheckVerifyVolumeRespDTO;
import com.holderzone.holder.saas.aggregation.weixin.helper.DineInItemHelper;
import com.holderzone.holder.saas.aggregation.weixin.helper.MarketingActivityHelper;
import com.holderzone.holder.saas.aggregation.weixin.helper.MemberRightHelper;
import com.holderzone.holder.saas.aggregation.weixin.helper.PriceCalculateHelper;
import com.holderzone.holder.saas.aggregation.weixin.service.ItemService;
import com.holderzone.holder.saas.aggregation.weixin.service.MemberService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.HsmTerminalServiceClient;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.TradeClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.WxClientService;
import com.holderzone.holder.saas.aggregation.weixin.utils.SortedUtils;
import com.holderzone.holder.saas.aggregation.weixin.utils.UserMemberSessionUtils;
import com.holderzone.holder.saas.aggregation.weixin.utils.map.MemberCardItemMAP;
import com.holderzone.holder.saas.aggregation.weixin.utils.map.MemberDiscountMAP;
import com.holderzone.holder.saas.aggregation.weixin.utils.map.MemberVolumeItemMAP;
import com.holderzone.holder.saas.common.enums.BooleanEnum;
import com.holderzone.holder.saas.common.enums.VolumeTypeEnum;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseIntegralOffset;
import com.holderzone.holder.saas.member.terminal.dto.volume.RequestVolumePageList;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseClientMarketActivity;
import com.holderzone.holder.saas.member.wechat.dto.card.*;
import com.holderzone.holder.saas.member.wechat.dto.coupon.ResponseVolumeList;
import com.holderzone.holder.saas.member.wechat.dto.enums.MemberRightsType;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestQueryMemberInfo;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberCardListOwned;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfo;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.entry.dto.WxUserInfoDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.marketing.nth.NthActivityDetailsVO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityDetailsVO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityItemVO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.member.MemberCouponListReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.weixin.deal.*;
import com.holderzone.saas.store.dto.weixin.member.WxMemberInfoVolumeDetailsRespDTO;
import com.holderzone.saas.store.dto.weixin.member.WxPrepayConfirmRespDTO;
import com.holderzone.saas.store.dto.weixin.member.WxVolumeDetailReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.MarketingActivityInfoRespDTO;
import com.holderzone.saas.store.enums.member.VolumeUnableTipEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.util.BigDecimalUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class MemberServiceImpl implements MemberService {

    private final WxClientService wxClientService;
    private final TradeClientService tradeClientService;
    private final UserMemberSessionUtils userMemberSessionUtils;
    private final HsaBaseClientService hsaBaseClientService;
    private final HsmTerminalServiceClient terminalServiceClient;
    private final ItemService itemService;
    private final RedisUtils redisUtils;
    private final MarketingActivityHelper marketingActivityHelper;
    private final PriceCalculateHelper priceCalculateHelper;
    private final MemberRightHelper memberRightHelper;


    @Override
    public List<MemberCardItemDTO> cardPage() {
        RequestCardOwnedPage pageReqDTO = new RequestCardOwnedPage();
        pageReqDTO.setPage(1);
        pageReqDTO.setPageSize(50);
        pageReqDTO.setBrandGuid(WeixinUserThreadLocal.getBrandGuid());
        pageReqDTO.setStoreGuid(WeixinUserThreadLocal.getStoreGuid());
        pageReqDTO.setEnterpriseGuid(WeixinUserThreadLocal.getEnterpriseGuid());
        pageReqDTO.setOpenId(WeixinUserThreadLocal.getOpenId());
        List<ResponseMemberCardListOwned> cardList = null;
        try {
            cardList = hsaBaseClientService.getMemberCardByPage(pageReqDTO).getData().getData();
        } catch (Exception e) {
            log.error("查询会员卡列表失败,e:", e);
        }
        if (ObjectUtils.isEmpty(cardList)) {
            return Collections.emptyList();
        }
        // 过滤冻结卡
        cardList.removeIf(e -> !e.getIsFreeze());
        if (ObjectUtils.isEmpty(cardList)) {
            return Collections.emptyList();
        }
        List<MemberCardItemDTO> memberCardList = MemberCardItemMAP.INSTANCE.fromResponseMemberCardList(cardList);
        UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
        UserContext userContext = UserContextUtils.get();
        memberCardList = memberCardList.parallelStream().peek(x -> {
            UserContextUtils.put(userContext);
            RequestCardRightDetails requestCardRightDetails = new RequestCardRightDetails();
            requestCardRightDetails.setCardGuid(x.getCardGuid());
            requestCardRightDetails.setCardLevelGuid(x.getCardLevelGuid());
            requestCardRightDetails.setMemberInfoCardGuid(x.getMemberInfoCardGuid());
            List<ResponseCardRight> cardRightDetails = hsaBaseClientService.getCardRightDetails(requestCardRightDetails).getData();
            log.info("权益数组:{}", cardRightDetails);
            x.setCardRights(cardRightDetails);
            if (!ObjectUtils.isEmpty(cardRightDetails)) {
                if (cardRightDetails.stream().anyMatch(k -> !ObjectUtils.isEmpty(k.getList()))) {
                    if (cardRightDetails.size() != 1 || !"会员价".equals(cardRightDetails.get(0).getName())) {
                        x.setUckCardRight(1);
                    }
                }
            }
            if (x.getMemberInfoCardGuid().equals(userMemberSession.getMemberInfoCardGuid())) {
                x.setUck(1);
                x.setUckIntegral(userMemberSession.getMemberIntegral());
            }
        }).collect(Collectors.toList());
        return memberCardList;
    }

    @Override
    public WxMemberInfoVolumeDetailsRespDTO volumeDetail(WxVolumeDetailReqDTO wxVolumeDetailReqDTO) {
        WxMemberInfoVolumeDetailsRespDTO volumeDetail = wxClientService.volumeCodeDetails(wxVolumeDetailReqDTO);
        if (volumeDetail != null) {
            ArrayList<String> desc = new ArrayList<>();
            Integer volumeType = volumeDetail.getVolumeType();
            BigDecimal volumeMoney = volumeDetail.getVolumeMoney();
            String volumeDesc = volumeDesc(volumeType, volumeDetail);
            if (!StringUtils.isEmpty(volumeDesc)) {
                desc.add(volumeDesc);
            }
            if (volumeDetail.getUseThreshold() == 0) {
                desc.add("无门槛");
            } else {
                desc.add("满" + volumeDetail.getUseThresholdFull() + "可用");
            }

            if (!StringUtils.isEmpty(volumeDetail.getMayUseNum())) {
                desc.add("每次可用" + volumeDetail.getMayUseNum() + "张");
            }
            log.info("优惠券详情封装:{}", volumeDetail);
            if (volumeDetail.getIsUseAlone() != null && volumeDetail.getIsUseAlone() == 1) {
                desc.add("仅原价购买商品时使用");
            }

            volumeDetail.setVolumeDescList(desc);
        }
        return volumeDetail;
    }

    private String volumeDesc(Integer volumeType, WxMemberInfoVolumeDetailsRespDTO volumeDetail) {
        //兼容非空
        BigDecimal volumeMoney = volumeDetail.getVolumeMoney();
        if (volumeType == 0 && volumeMoney != null) {
//			return "代金券￥" + volumeMoney;
            return "代金券￥" + volumeMoney.doubleValue();
        } else if (volumeType == 3) {
            return "商品券";
//			String volumeInfoName = volumeDetail.getVolumeInfoName();
//			if (!StringUtils.isEmpty(volumeInfoName)) {
//				if (volumeInfoName.endsWith("券")) {
//					return volumeInfoName;
//				}else{
//					return volumeInfoName + "券";
//				}
//			}
        }
        return null;
    }

    @Override
    public List<ResponseCardRight> cardRight(RequestCardRightDetails requestCardRightDetails) {
        return hsaBaseClientService.getCardRightDetails(requestCardRightDetails).getData();
    }

    @Override
    public WxPrepayConfirmRespDTO confirm(MemberConfirmReqDTO memberConfirmReqDTO) {
        return memberConfirmReqDTO.getType() == 1
                ? confirmCard(memberConfirmReqDTO)
                : confirmVolume(memberConfirmReqDTO);
    }

    /**
     * @param memberConfirmReqDTO 卡券确认
     * @return 券
     */
    private WxPrepayConfirmRespDTO confirmVolume(MemberConfirmReqDTO memberConfirmReqDTO) {

        //Assert.notNull(memberConfirmReqDTO.getVolumeCode(), "优惠券必传");
        if (StringUtils.isEmpty(memberConfirmReqDTO.getVolumeCode())) {
            memberConfirmReqDTO.setMemberInfoCardGuid("-1");
        }
        UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
        userMemberSession.setVolumeCode(StringUtils.isEmpty(memberConfirmReqDTO.getVolumeCode()) ? "-1" : memberConfirmReqDTO.getVolumeCode());
        userMemberSessionUtils.addUserMemberSession(userMemberSession);

        return new WxPrepayConfirmRespDTO(0);
    }

    /**
     * @param memberConfirmReqDTO 卡券确认
     * @return 卡
     */
    private WxPrepayConfirmRespDTO confirmCard(MemberConfirmReqDTO memberConfirmReqDTO) {
        UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
        Assert.notNull(memberConfirmReqDTO.getMemberInfoCardGuid(), "会员卡id必传");
        userMemberSession.setMemberInfoCardGuid(StringUtils.isEmpty(memberConfirmReqDTO.getMemberInfoCardGuid())
                ? "-1" : memberConfirmReqDTO.getMemberInfoCardGuid());
        if (memberConfirmReqDTO.getIntegralUck() != null) {
            userMemberSession.setMemberIntegral(memberConfirmReqDTO.getIntegralUck() ? 1 : 0);
        } else {
            userMemberSession.setMemberIntegral(0);
        }

        userMemberSessionUtils.addUserMemberSession(userMemberSession);
        return new WxPrepayConfirmRespDTO(0);
    }

    @Override
    public ConcessionTotalRespDTO discount(ConcessionTotalReqDTO concessionTotalReqDTO) {
        // 构建计算参数
        BillCalculateReqDTO billCalculateReqDTO = buildBillCalculateReqDTO(concessionTotalReqDTO);
        try {
            return discountVolume(concessionTotalReqDTO, billCalculateReqDTO);
        } catch (Exception e) {
            log.error("优惠计算,e:", e);
            return new ConcessionTotalRespDTO();
        }
    }

    private BillCalculateReqDTO buildBillCalculateReqDTO(ConcessionTotalReqDTO concessionTotalReqDTO) {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        log.info("wxMemberSessionDTO:{}", JacksonUtils.writeValueAsString(wxMemberSessionDTO));
        UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
        log.info("userMemberSession:{}", JacksonUtils.writeValueAsString(userMemberSession));
        String merchantOrderGuid = wxClientService.getMerchantOrderGuid(concessionTotalReqDTO.getOrderGuid());
        //券查询
        BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setOrderGuid(merchantOrderGuid);
        billCalculateReqDTO.setEnterpriseGuid(wxMemberSessionDTO.getEnterpriseGuid());
        billCalculateReqDTO.setStoreGuid(wxMemberSessionDTO.getStoreGuid());
        billCalculateReqDTO.setStoreName(wxMemberSessionDTO.getStoreName());
        billCalculateReqDTO.setUserGuid(WeixinUserThreadLocal.getOpenId());
        billCalculateReqDTO.setUserName(WeixinUserThreadLocal.getNickName());
        billCalculateReqDTO.setDeviceId(WeixinUserThreadLocal.getOpenId());
        billCalculateReqDTO.setMemberLogin(-1);
        billCalculateReqDTO.setDeviceType(12);

        String volumeCode = concessionTotalReqDTO.getVolumeCode();
        String memberInfoCardGuid = concessionTotalReqDTO.getMemberInfoCardGuid();
        Integer enableIntegral = concessionTotalReqDTO.getEnableIntegral();

        volumeCode = StringUtils.isEmpty(volumeCode) ? userMemberSession.getVolumeCode() : volumeCode;
        memberInfoCardGuid = StringUtils.isEmpty(memberInfoCardGuid) ? userMemberSession.getMemberInfoCardGuid() : memberInfoCardGuid;
        enableIntegral = ObjectUtils.isEmpty(enableIntegral) ? userMemberSession.getMemberIntegral() : enableIntegral;

        billCalculateReqDTO.setMemberInfoCardGuid(StringUtils.isEmpty(memberInfoCardGuid) || memberInfoCardGuid.length() < 5 ? null : memberInfoCardGuid);
        billCalculateReqDTO.setMemberLogin(StringUtils.isEmpty(billCalculateReqDTO.getMemberInfoCardGuid()) ? 2 : 1);
        billCalculateReqDTO.setMemberPhone(StringUtils.isEmpty(billCalculateReqDTO.getMemberInfoCardGuid()) ? null : WeixinUserThreadLocal.getOpenId());
        billCalculateReqDTO.setMemberIntegral(enableIntegral != null && enableIntegral == 1 ? 1 : 2);
        billCalculateReqDTO.setVolumeCode(StringUtils.isEmpty(volumeCode) || volumeCode.length() < 5 ? null : volumeCode);
        billCalculateReqDTO.setVerify(StringUtils.isEmpty(volumeCode) ? null : 3);
        if (!StringUtils.isEmpty(volumeCode)) {
            ArrayList<String> checkedCoupon = new ArrayList<>();
            checkedCoupon.add(volumeCode);
            billCalculateReqDTO.setVolumeCodes(checkedCoupon);
        }
        billCalculateReqDTO.setVerify(StringUtils.isEmpty(volumeCode) || volumeCode.length() < 5 ? null : 3);
        return billCalculateReqDTO;
    }

    @Override
    public List<MemberVolumeItemDTO> volumeList(VolumePageReqDTO volumePageReqDTO) {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        String orderRecordGuid = volumePageReqDTO.getOrderGuid();
        WxUserInfoDTO wxUserInfoDTO = WeixinUserThreadLocal.getWxUserInfoDTO();
        RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setOpenId(wxUserInfoDTO.getOpenId());
        ResponseMemberInfo memberInfo = hsaBaseClientService
                .getMemberInfo(requestQueryMemberInfo).getData();
        UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(wxUserInfoDTO.getOpenId());
        if (memberInfo == null) {
            log.error("会员不存在，openid:{},enterpriseGuid:{}", wxUserInfoDTO.getOpenId(), wxMemberSessionDTO.getEnterpriseGuid());
            return Collections.emptyList();
        }
        String merchantOrderGuid = wxClientService.getMerchantOrderGuid(orderRecordGuid);
        if (StringUtils.isEmpty(merchantOrderGuid)) {
            log.error("未查询到商户订单:{},enterpriseGuid:{}", orderRecordGuid, WeixinUserThreadLocal.getEnterpriseGuid());
            return Collections.emptyList();
        }
        MemberCouponListReqDTO memberCouponListReqDTO = new MemberCouponListReqDTO();
        String memberInfoCardGuid = userMemberSession.getMemberInfoCardGuid();
        if (!StringUtils.isEmpty(memberInfoCardGuid) && memberInfoCardGuid.length() > 5) {
            memberCouponListReqDTO.setMemberInfoCardGuid(memberInfoCardGuid);
        }
        memberCouponListReqDTO.setMemberInfoGuid(memberInfo.getMemberInfoGuid());
        memberCouponListReqDTO.setOrderGuid(merchantOrderGuid);
        List<com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList> verifyList = tradeClientService.verify(memberCouponListReqDTO);
        log.info("查询商户优惠券列表:{}", verifyList);
        if (CollectionUtils.isEmpty(verifyList)) {
            return Collections.emptyList();
        }
        String volumeCode = userMemberSession.getVolumeCode();
        //Set<String> couponChecked = userMemberSessionUtils.getCheckedCoupon(WeixinUserThreadLocal.getOpenId(),orderRecordGuid);
        List<Integer> stateList = Arrays.asList(0, 2);
        final List<MemberVolumeItemDTO> collect = new ArrayList<>();
//        List<VolumeListRespDTO> volumeListRespDTOS = memberOrderClientService.verifyVolumeList(merchantOrderGuid);
        List<ResponseVolumeList> responseVolumeLists = hsaBaseClientService.consumeVolumeList(merchantOrderGuid).getData();
        verifyList.stream().forEach((x) -> {
            MemberVolumeItemDTO memberVolumeItemDTO = MemberVolumeItemMAP.INSTANCE.fromVolumeListRespDTO(x);
            if (!stateList.contains(x.getVolumeState())) {
                return;
            }
            memberVolumeItemDTO.setUck(0);
            if (!CollectionUtils.isEmpty(responseVolumeLists)) {
                memberVolumeItemDTO.setEnable(false);
                memberVolumeItemDTO.setTip("请至收银台撤销已选中券~");
            } else {
                //优惠券状态为未使用 且 符合该订单使用  优惠券状态设置为true
                memberVolumeItemDTO.setEnable(x.getVolumeState().equals(0) && x.isUseable());
                if (volumeCode != null && volumeCode.equals(x.getVolumeCode())) {
                    memberVolumeItemDTO.setUck(1);
                }
            }
            collect.add(memberVolumeItemDTO);
        });
        return SortedUtils.sorted(
                collect,
                new SortedUtils.SortedAttribute("enable", SortedUtils.SortedType.DESC),
                new SortedUtils.SortedAttribute("volumeState")
        );
    }

    @Override
    public List<com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList> queryVolumeList(VolumeOrderQueryDTO queryDTO) {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        WxUserInfoDTO wxUserInfoDTO = wxMemberSessionDTO.getWxUserInfoDTO();
        // 查询会员信息
        RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setOpenId(wxUserInfoDTO.getOpenId());
        ResponseMemberInfo memberInfo = hsaBaseClientService.getMemberInfo(requestQueryMemberInfo).getData();
        if (memberInfo == null) {
            log.error("会员不存在，openid:{},enterpriseGuid:{}", wxUserInfoDTO.getOpenId(), wxMemberSessionDTO.getEnterpriseGuid());
            return Collections.emptyList();
        }
        // 商品明细转换
        List<RequestDishInfo> dishInfoList = DineInItemHelper.dineInItem2DishList(queryDTO.getDineInItemList());
        // 查询菜谱和规格的父对象
        itemService.setParentDishSkuInfo(dishInfoList);
        // 查询优惠券
        RequestVolumePageList volumeListReqDTO = buildQueryVolumeReqDTO(memberInfo, dishInfoList, queryDTO);
        log.info("小程序使用优惠券入参:{}", JacksonUtils.writeValueAsString(volumeListReqDTO));
        List<com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList> volumeListRespList =
                terminalServiceClient.queryVolumePage(volumeListReqDTO);
        log.info("小程序使用优惠券入参返回:{}", JacksonUtils.writeValueAsString(volumeListRespList));

        // 限时特价过滤
        filterSpecialsActivity(queryDTO, volumeListRespList);

        // 第N份优惠过滤
        filterNthActivity(queryDTO, volumeListRespList);

        // 暂时先过滤只查询代金券
        // 排序 已生效 > 未使用
        volumeListRespList = volumeListRespList.stream()
                .filter(e -> VolumeTypeEnum.CASH_COUPON.getCcCode() == e.getVolumeType() || VolumeTypeEnum.PRODUCT_COUPON.getCcCode() == e.getVolumeType())
                .sorted(Comparator.comparing(com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList::getVolumeState)
                        .thenComparing(Comparator.comparing(com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList::isUseable).reversed()))
                .collect(Collectors.toList());

        // 处理不可使用原因，替换为前端展示的
        replaceVolumeUnableTips(volumeListRespList);
        return volumeListRespList;
    }

    /**
     * 第N份优惠过滤
     */
    private void filterNthActivity(VolumeOrderQueryDTO queryDTO,
                                   List<com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList> volumeListRespList) {
        NthActivityDetailsVO nthActivityDetailsVO =
                marketingActivityHelper.querySelectNthActivityDetailsVO(queryDTO.getActivitySelectList());

        // 第N份优惠为互斥
        if (!ObjectUtils.isEmpty(nthActivityDetailsVO) &&
                Objects.equals(nthActivityDetailsVO.getRelationRule(), BooleanEnum.FALSE.getCode())) {
            volumeListRespList.forEach(volume -> {
                if (volume.isUseable() && !queryDTO.getVolumeCodes().contains(volume.getVolumeCode())) {
                    volume.setUseable(false);
                    volume.setTip(VolumeUnableTipEnum.OTHER_DISCOUNT_PRICE.getTips());
                }
            });
        }

        // 第N份优惠存在，优惠券本身为互斥
        if (!ObjectUtils.isEmpty(nthActivityDetailsVO)) {
            volumeListRespList.forEach(volume -> {
                if (volume.isUseAlone()) {
                    volume.setUseable(false);
                    volume.setTip(VolumeUnableTipEnum.OTHER_DISCOUNT_PRICE.getTips());
                }
            });
        }
    }

    private void filterSpecialsActivity(VolumeOrderQueryDTO queryDTO,
                                        List<com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList> volumeListRespList) {
        LimitSpecialsActivityDetailsVO activityDetailsVO =
                marketingActivityHelper.querySelectSpecialsActivityDetailsVO(queryDTO.getActivitySelectList());
        boolean isFirst = ObjectUtils.isEmpty(queryDTO.getIsFirst()) ? Boolean.FALSE : queryDTO.getIsFirst();
        // 首次进入查询最优活动
        if (isFirst) {
            // 查询限时特价活动
            List<LimitSpecialsActivityDetailsVO> specialsActivityList =
                    marketingActivityHelper.querySpecialsActivityList(WeixinUserThreadLocal.get());

            // 计算每个活动的优惠金额
            List<MarketingActivityInfoRespDTO> activityInfoList =
                    calculateActivityDiscountPrice(specialsActivityList, queryDTO.getDineInItemList());

            // 进入结算页面时取优惠力度最大的活动默认选中
            activityDetailsVO = handleDefaultSelect(activityInfoList, specialsActivityList);
            log.info("[优惠券列表][最终确定的活动]activityDetailsVO={}", JacksonUtils.writeValueAsString(activityDetailsVO));

        }

        // 限时特价活动为互斥
        if (!ObjectUtils.isEmpty(activityDetailsVO) &&
                Objects.equals(activityDetailsVO.getRelationRule(), BooleanEnum.FALSE.getCode())) {
            volumeListRespList.forEach(volume -> {
                if (volume.isUseable() && !queryDTO.getVolumeCodes().contains(volume.getVolumeCode())) {
                    volume.setUseable(false);
                    volume.setTip(VolumeUnableTipEnum.OTHER_DISCOUNT_PRICE.getTips());
                }
            });
        }

        // 限时特价存在，优惠券本身为互斥
        if (!ObjectUtils.isEmpty(activityDetailsVO)) {
            volumeListRespList.forEach(volume -> {
                if (volume.isUseAlone()) {
                    volume.setUseable(false);
                    volume.setTip(VolumeUnableTipEnum.OTHER_DISCOUNT_PRICE.getTips());
                }
            });
        }
    }

    /**
     * 计算每个活动的优惠金额
     */
    private List<MarketingActivityInfoRespDTO> calculateActivityDiscountPrice(List<LimitSpecialsActivityDetailsVO> specialsActivityList,
                                                                              List<DineInItemDTO> allItems) {
        List<String> itemGuidList = allItems.stream()
                .map(DineInItemDTO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        List<String> parentGuidList = priceCalculateHelper.queryParentGuid(itemGuidList);
        itemGuidList.addAll(parentGuidList);
        List<MarketingActivityInfoRespDTO> activityInfoList = new ArrayList<>();
        specialsActivityList.forEach(activity -> {
            // 命中的商品
            List<LimitSpecialsActivityItemVO> activityItemVOList =
                    MarketingActivityAssembler.getLimitSpecialsActivityItemVOS(activity, itemGuidList);
            log.info("[命中的商品]activityItemVOList={}", JacksonUtils.writeValueAsString(activityItemVOList));
            if (CollectionUtils.isEmpty(activityItemVOList)) {
                log.warn("订单中没有该活动的商品,activity={},name={}", activity.getGuid(), activity.getName());
                return;
            }
            Map<String, LimitSpecialsActivityItemVO> activityItemVOMap = activityItemVOList.stream()
                    .collect(Collectors.toMap(LimitSpecialsActivityItemVO::getCommodityId, Function.identity(), (v1, v2) -> v1));

            BigDecimal specialsTotalPrice = BigDecimal.ZERO;
            List<DineInItemDTO> joinActivityItemList = allItems.stream()
                    .filter(i -> activityItemVOMap.containsKey(i.getItemGuid()))
                    .collect(Collectors.toList());
            for (DineInItemDTO itemInfoDTO : joinActivityItemList) {
                LimitSpecialsActivityItemVO activityItemVO = activityItemVOMap.get(itemInfoDTO.getItemGuid());
                if (ObjectUtils.isEmpty(activityItemVO)) {
                    log.warn("该商品没有匹配的活动,itemGuid={}", itemInfoDTO.getItemGuid());
                    continue;
                }
                BigDecimal singleActivityDiscountPrice = priceCalculateHelper.calculateSpecialsActivityDiscountPrice(itemInfoDTO, activityItemVO);
                specialsTotalPrice = specialsTotalPrice.add(singleActivityDiscountPrice);
            }

            // 构建返回的活动列表
            MarketingActivityInfoRespDTO infoRespDTO = getActivityInfoRespDTO(activity, specialsTotalPrice.setScale(2, RoundingMode.HALF_UP));
            activityInfoList.add(infoRespDTO);
        });
        return activityInfoList;
    }

    @NotNull
    private MarketingActivityInfoRespDTO getActivityInfoRespDTO(LimitSpecialsActivityDetailsVO activity,
                                                                BigDecimal specialsTotalPrice) {
        MarketingActivityInfoRespDTO infoRespDTO = new MarketingActivityInfoRespDTO();
        infoRespDTO.setActivityGuid(activity.getGuid());
        infoRespDTO.setActivityName(activity.getName());
        infoRespDTO.setActivityType(DiscountTypeEnum.LIMIT_SPECIALS_ACTIVITY.getCode());
        infoRespDTO.setActivityRule(String.format("省%s元", specialsTotalPrice));
        infoRespDTO.setDiscountPrice(specialsTotalPrice.setScale(2, RoundingMode.HALF_UP));
        // 如果金额为0说明没有优惠，即没有参与活动
        infoRespDTO.setUseAble(true);
        if (BigDecimalUtil.equelZero(specialsTotalPrice)) {
            infoRespDTO.setUseAble(false);
            infoRespDTO.setUnUseReason(Constant.UN_FULL_CONDITION);
            return infoRespDTO;
        }
        return infoRespDTO;
    }

    /**
     * 进入结算页面时取优惠力度最大的活动默认选中
     */
    private LimitSpecialsActivityDetailsVO handleDefaultSelect(List<MarketingActivityInfoRespDTO> activityInfoList,
                                                               List<LimitSpecialsActivityDetailsVO> specialsActivityList) {
        if (!CollectionUtils.isEmpty(activityInfoList)) {
            // 只从可用的活动里选择
            MarketingActivityInfoRespDTO activityInfoDTO = activityInfoList.stream()
                    .filter(MarketingActivityInfoRespDTO::isUseAble)
                    .max(Comparator.comparing(MarketingActivityInfoRespDTO::getDiscountPrice))
                    .orElse(null);
            if (!ObjectUtils.isEmpty(activityInfoDTO)) {
                return specialsActivityList.stream()
                        .filter(a -> Objects.equals(activityInfoDTO.getActivityGuid(), a.getGuid()))
                        .findFirst()
                        .orElse(null);
            }
        }
        return null;
    }

    /**
     * 替换优惠券 不可使用原因
     */
    private void replaceVolumeUnableTips(List<com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList> volumeListRespList) {
        for (com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList volumeInfo : volumeListRespList) {
            if (StringUtils.isEmpty(volumeInfo.getTip())) {
                continue;
            }
            volumeInfo.setTip(VolumeUnableTipEnum.getView(volumeInfo.getTip()));
        }
    }

    /**
     * 构建查询优惠券列表请求
     */
    private RequestVolumePageList buildQueryVolumeReqDTO(ResponseMemberInfo memberInfo, List<RequestDishInfo> dishInfoList,
                                                         VolumeOrderQueryDTO queryDTO) {
        UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
        RequestVolumePageList volumeListReqDTO = new RequestVolumePageList();
        volumeListReqDTO.setSortFlag(false);
        volumeListReqDTO.setMemberInfoGuid(memberInfo.getMemberInfoGuid());
        volumeListReqDTO.setMemberInfoCardGuid(userMemberSession.getMemberInfoCardGuid());
        // 如果不使用会员优惠，需要把会员价取消
        if (Boolean.FALSE.equals(queryDTO.getUseMemberDiscountFlag())) {
            dishInfoList.forEach(e -> e.setDishMemberPrice(null));
        }
        // 查询会员权益
        ResponseProductDiscount responseProductDiscount = memberRightHelper.queryMemberRights(WeixinUserThreadLocal.getWxtoken());
        if (Objects.nonNull(responseProductDiscount)) {
            // 是否有会员折扣
            boolean hasMemberDiscount = Objects.equals(responseProductDiscount.getMemberRightsType(), MemberRightsType.MEMBER_DISCOUNT.getCode());
            volumeListReqDTO.setHasMemberDiscount(hasMemberDiscount && Boolean.TRUE.equals(queryDTO.getUseMemberDiscountFlag()));
        }
        // 如果勾选了营销活动，则需要查询营销活动是否互斥
        String selectActivityGuid = queryDTO.getActivitySelectList().stream()
                .filter(s -> Objects.equals(DiscountTypeEnum.ACTIVITY.getCode(), s.getActivityType()))
                .map(ActivitySelectDTO::getActivityGuid)
                .findFirst()
                .orElse(null);
        if (StringUtils.isNotEmpty(selectActivityGuid)) {
            ResponseClientMarketActivity marketActivity = hsaBaseClientService.getActivityInfo(selectActivityGuid).getData();
            if (Objects.nonNull(marketActivity)) {
                volumeListReqDTO.setHasOtherDiscount(true);
                volumeListReqDTO.setHasOtherExclusionDiscount(marketActivity.getIsShare() == 1);
            }
        }
        volumeListReqDTO.setRequestDishInfoList(dishInfoList);
        volumeListReqDTO.setVolumeCodes(queryDTO.getVolumeCodes());
        volumeListReqDTO.setPageNo(1);
        volumeListReqDTO.setPageSize(1000);
        return volumeListReqDTO;
    }

    @Override
    public CheckVerifyVolumeRespDTO checkVolume(VolumePageReqDTO volumePageReqDTO) {
        String orderGuid = volumePageReqDTO.getOrderGuid();
        String merchantOrderGuid = wxClientService.getMerchantOrderGuid(orderGuid);
        if (StringUtils.isEmpty(merchantOrderGuid)) {
            log.error("未查询到商户订单:{},enterpriseGuid:{}", orderGuid, WeixinUserThreadLocal.getEnterpriseGuid());
            return new CheckVerifyVolumeRespDTO().setCode(0);
        }
//		List<VolumeListRespDTO> volumeListRespDTOS = memberOrderClientService.verifyVolumeList(merchantOrderGuid);
        List<ResponseVolumeList> responseVolumeLists = hsaBaseClientService.consumeVolumeList(merchantOrderGuid).getData();
        log.info("已验券列表:{}", responseVolumeLists);
        if (!CollectionUtils.isEmpty(responseVolumeLists)) {
            return new CheckVerifyVolumeRespDTO(1, "请至收银台撤销已选中券~");
        }
        return new CheckVerifyVolumeRespDTO().setCode(0);
    }

    @Override
    public void verifyMemberExist(String operSubjectGuid, String memberInfoGuid) {
        RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setOperSubjectGuid(operSubjectGuid);
        requestQueryMemberInfo.setMemberInfoGuid(memberInfoGuid);
        log.info("查询免密登录会员信息入参:{}", JacksonUtils.writeValueAsString(requestQueryMemberInfo));
        ResponseModel<ResponseMemberInfo> memberInfo = hsaBaseClientService.getMemberInfo(requestQueryMemberInfo);
        log.info("查询免密登录会员信息返回:{}", JacksonUtils.writeValueAsString(memberInfo));
        if (Objects.isNull(memberInfo) || Objects.isNull(memberInfo.getData())) {
            throw new BusinessException("会员不存在，请稍后再试");
        }
    }


    /**
     * @param concessionTotalReqDTO 优惠券
     * @return 优惠券优惠
     */
    private ConcessionTotalRespDTO discountVolume(ConcessionTotalReqDTO concessionTotalReqDTO, BillCalculateReqDTO billCalculateReqDTO) {
        log.info("计算优惠明细入参:{}", billCalculateReqDTO);
        DineinOrderDetailRespDTO calculate = tradeClientService.calculate(billCalculateReqDTO);
        log.info("计算优惠明细返回:{}", calculate);
        if (Objects.isNull(calculate)) {
            return new ConcessionTotalRespDTO();
        }
        // 构建返回参数
        ConcessionTotalRespDTO concessionTotalRespDTO = new ConcessionTotalRespDTO();
        concessionTotalRespDTO.setUck(!StringUtils.isEmpty(billCalculateReqDTO.getVolumeCode())
                || !StringUtils.isEmpty(billCalculateReqDTO.getMemberInfoCardGuid()));
        List<DiscountFeeDetailDTO> discountFeeDetailDTOS = calculate.getDiscountFeeDetailDTOS();

        if (concessionTotalReqDTO.getType() == 1 && !ObjectUtils.isEmpty(discountFeeDetailDTOS)) {
            // 选择会员卡优惠
            selectMemberCardDiscount(concessionTotalRespDTO, calculate);
        } else if (!ObjectUtils.isEmpty(discountFeeDetailDTOS)) {
            // 选择会员商品券/代金券优惠
            selectMemberCouponDiscount(concessionTotalRespDTO, calculate);
        } else {
            log.info("---->discountFeeDetailDTOS商品券展示商品数据---->为空！");
        }
        UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
        Integer enableIntegral = concessionTotalReqDTO.getEnableIntegral();
        if (enableIntegral != null) {
            concessionTotalRespDTO.setUckIntegral(enableIntegral == 1 ? 1 : 0);
        } else if (userMemberSession.getMemberIntegral() != null && userMemberSession.getMemberIntegral() == 1) {
            concessionTotalRespDTO.setUckIntegral(1);
        }
        return concessionTotalRespDTO;
    }

    /**
     * 选择会员卡/积优惠
     */
    private void selectMemberCardDiscount(ConcessionTotalRespDTO concessionTotalRespDTO, DineinOrderDetailRespDTO calculate) {
        List<DiscountFeeDetailDTO> discountFeeDetailDTOS = calculate.getDiscountFeeDetailDTOS();
        concessionTotalRespDTO.setMemberDiscountDTOS(ObjectUtils.isEmpty(discountFeeDetailDTOS)
                ? Collections.emptyList()
                : MemberDiscountMAP.INSTANCE.toMemberDiscountList(discountFeeDetailDTOS.stream()
                .filter(x -> Arrays.asList(DiscountTypeEnum.MEMBER.getCode(), DiscountTypeEnum.POINTS_DEDUCTION.getCode()).contains(x.getDiscountType()))
                .collect(Collectors.toList())));
        ResponseIntegralOffset integralOffsetResultRespDTO = calculate.getIntegralOffsetResultRespDTO();
        log.info("积分抵扣:{}", integralOffsetResultRespDTO);
        BigDecimal memberPrice = BigDecimal.ZERO;
        BigDecimal integral = BigDecimal.ZERO;
        for (DiscountFeeDetailDTO feeDetailDTO : discountFeeDetailDTOS) {
            log.info("feeDetailDTO:{}", JacksonUtils.writeValueAsString(feeDetailDTO));
            if (DiscountTypeEnum.MEMBER.getCode() == feeDetailDTO.getDiscountType() && feeDetailDTO.getDiscountFee() != null) {
                memberPrice = feeDetailDTO.getDiscountFee();
            }
            if (DiscountTypeEnum.POINTS_DEDUCTION.getCode() == feeDetailDTO.getDiscountType() && feeDetailDTO.getDiscountFee() != null) {
                integral = feeDetailDTO.getDiscountFee();
            }
        }
        /* 设置可抵扣积分值。如果积分信息为空时，但是却存在抵扣金额，此时用抵扣金额向上取整设置积分值。 */
        boolean nullFlag = integralOffsetResultRespDTO == null;
        integralOffsetResultRespDTO = Optional.ofNullable(integralOffsetResultRespDTO).orElse(new ResponseIntegralOffset());
        Integer useIntegral = integralOffsetResultRespDTO.getUseIntegral();
        useIntegral = Objects.isNull(useIntegral) ? 0 : useIntegral;

        int doubleValue = (int) Math.ceil((integral == null) ? 0 : integral.doubleValue());
        log.info("积分兑换数据---->integral={}，useIntegeral={}，doubleValue={}", integral, useIntegral, doubleValue);
        concessionTotalRespDTO.setIntegral(nullFlag ? doubleValue : useIntegral);
        concessionTotalRespDTO.setEnableIntegral((useIntegral.equals(0) ? 0 : integralOffsetResultRespDTO.getRuleState()));
        /* 是否需要计算带后续测试，目前直接取订单抵扣值。保持与积分展示一致，都来自订单。 */
        concessionTotalRespDTO.setIntegralFee((nullFlag ? BigDecimal.valueOf(0) : integralOffsetResultRespDTO.getDeductionMoney()));
        log.info("积分兑换数据---->Integral={},EnableIntegral={},IntegralFee={}",
                concessionTotalRespDTO.getIntegral()
                , concessionTotalRespDTO.getEnableIntegral()
                , concessionTotalRespDTO.getIntegralFee()
        );
        concessionTotalRespDTO.setDiscountAmount(memberPrice.add(integral));
    }

    /**
     * 选择会员商品券/代金券优惠
     */
    private void selectMemberCouponDiscount(ConcessionTotalRespDTO concessionTotalRespDTO, DineinOrderDetailRespDTO calculate) {
        List<DiscountFeeDetailDTO> discountFeeDetailDTOS = calculate.getDiscountFeeDetailDTOS();
        Optional<DiscountFeeDetailDTO> first = discountFeeDetailDTOS.stream()
                .filter(x -> x.getDiscountType() == 7 || x.getDiscountType() == 11)
                .findFirst();
        if (first.isPresent()) {
            DiscountFeeDetailDTO feeDetailDTO = first.get();

						/*
                        bug-14323，定位，当选择商品券时，需返回对应商品券减掉的金额，微信端只管获取并未参与直接查询。
                        如果再次出现该问题，这问题有两个可能：
                         1-是会员系统未返回对应商品券金额，
                         2-订单接口holder-saas-store-trade/dine_in_bill/calculate未计算类型商品券时的对应金额。
                        */
            concessionTotalRespDTO.setDiscountAmount(Optional.ofNullable(feeDetailDTO.getDiscountFee()).orElse(BigDecimal.ZERO));
            log.info("---->商品券展示商品数据---->DiscountAmount={}", concessionTotalRespDTO.getDiscountAmount());
            concessionTotalRespDTO.setMemberDiscountDTOS(Collections.singletonList(MemberDiscountMAP.INSTANCE.toMemberDiscount(feeDetailDTO)));
        } else {
            log.info("---->DiscountFeeDetailDTO商品券展示商品数据---->为空！");
        }
    }
}
