package com.holderzone.holder.saas.aggregation.app.service.feign.groupbuy;

import com.holderzone.saas.store.dto.exception.GroupBuyException;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


/**
 * <AUTHOR>
 */
@Component
@FeignClient(name = "holder-saas-takeaway-producer", fallbackFactory = GroupClientService.GroupClientServiceFallBack.class)
public interface GroupClientService {

    /**
     * 预验券
     */
    @PostMapping("/group/coupon/prepare")
    List<MtCouponPreRespDTO> couponPrepare(@RequestBody CouPonPreReqDTO couPonPreReqDTO);

    @Component
    class GroupClientServiceFallBack implements FallbackFactory<GroupClientService> {

        private static final Logger logger = LoggerFactory.getLogger(GroupClientServiceFallBack.class);

        @Override
        public GroupClientService create(Throwable throwable) {
            return new GroupClientService() {
                @Override
                public List<MtCouponPreRespDTO> couponPrepare(CouPonPreReqDTO couPonPreReqDTO) {
                    logger.error("预验券 e={}", throwable.getMessage());
                    throw new GroupBuyException(throwable.getMessage());
                }
            };
        }

    }


}
