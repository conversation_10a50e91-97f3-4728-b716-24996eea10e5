package com.holderzone.holder.saas.aggregation.app.service.feign;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.organization.BusinessDateReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceQueryDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className OrganizationClientService
 * @date 18-9-21 下午5:15
 * @description
 * @program holder-saas-store-staff
 */
@Component
@FeignClient(name = "holder-saas-store-organization", fallbackFactory = OrganizationClientService.ServiceFallBack.class)
public interface OrganizationClientService {
    /**
     * 从云端查询状态
     *
     * @param deviceNo 厂商设备编号
     * @return dto
     */
    @GetMapping(value = "/device/find_device_status/{deviceNo}")
    StoreDeviceDTO findDeviceStatus(@PathVariable("deviceNo") String deviceNo);

    @GetMapping("/device/get_master_device_by_storeguid/{storeGuid}")
    StoreDeviceDTO getMasterDeviceByStoreGuid(@PathVariable("storeGuid") String storeGuid);

    @PostMapping(value = "/device/find_store_device")
    List<StoreDeviceDTO> findStoreDevice(@RequestBody StoreDeviceQueryDTO storeDeviceQueryDTO);

    /**
     * 根据门店guid查询门店详细信息
     *
     * @param storeGuid 门店guid
     * @return StoreDTO
     */
    @PostMapping("/store/query_store_by_guid")
    StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid);

    /**
     * 根据门店guid集合及日期时间获取所属营业日期
     * @param businessDateReqDTO
     * @return
     */
    @PostMapping("/store/query_business_day")
    LocalDate queryBusinessDay(@RequestBody BusinessDateReqDTO businessDateReqDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<OrganizationClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";


        @Override
        public OrganizationClientService create(Throwable throwable) {
            return new OrganizationClientService() {
                @Override
                public StoreDeviceDTO findDeviceStatus(String deviceNo) {
                    log.error(HYSTRIX_PATTERN, "findDeviceStatus", "设备编号为：" + deviceNo, ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询接口熔断");
                }

                @Override
                public StoreDeviceDTO getMasterDeviceByStoreGuid(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "getMasterDeviceByStoreGuid", "门店Guid 【" + storeGuid + "】", ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询接口熔断");
                }

                @Override
                public List<StoreDeviceDTO> findStoreDevice(StoreDeviceQueryDTO storeDeviceQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "getMasterDeviceByStoreGuid", JacksonUtils.writeValueAsString(storeDeviceQueryDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询接口熔断");
                }

                @Override
                public StoreDTO queryStoreByGuid(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "queryStoreByGuid", JacksonUtils.writeValueAsString(storeGuid), ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询门店信息");
                }

                @Override
                public LocalDate queryBusinessDay(BusinessDateReqDTO businessDateReqDTO) {
                    return null;
                }
            };
        }
    }
}
