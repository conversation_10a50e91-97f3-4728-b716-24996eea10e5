package com.holderzone.holder.saas.aggregation.merchant.controller.weixin;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin.WxStickShopCartClientService;
import com.holderzone.saas.store.dto.weixin.WxStickShopCartDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickShopCartRemoveDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickShopCartController
 * @date 2019/03/12 11:10
 * @description 微信桌贴购物车controller
 * @program holder-saas-store
 */
@RestController
@RequestMapping("/wx_stick_shop_cart")
@Slf4j
@Api(description = "微信桌贴购物车controller")
public class WxStickShopCartController {
    @Autowired
    WxStickShopCartClientService wxStickShopCartClientService;

    @PostMapping("/list_shop_cart")
    @ApiOperation("查询购物车列表")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "查询购物车列表")
    public Result<List<WxStickShopCartDTO>> listShopCart() {
        return Result.buildSuccessResult(wxStickShopCartClientService.listShopCart());
    }

    @PostMapping("/add_models")
    @ApiOperation("购物车批量添加")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "购物车批量添加")
    public Result addModels(@RequestBody List<WxStickShopCartDTO> shopCartDTOList) {
        wxStickShopCartClientService.addModels(shopCartDTOList);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/remove_models")
    @ApiOperation("购物车批量删除，若不传guidList，则表示清空购物车")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "购物车批量删除，若不传guidList，则表示清空购物车")
    public Result removeModels(@RequestBody WxStickShopCartRemoveDTO wxStickShopCartRemoveDTO) {
        wxStickShopCartClientService.removeModels(wxStickShopCartRemoveDTO);
        return Result.buildEmptySuccess();
    }
}
