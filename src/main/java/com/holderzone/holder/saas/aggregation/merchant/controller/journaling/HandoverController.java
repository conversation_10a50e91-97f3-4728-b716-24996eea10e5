package com.holderzone.holder.saas.aggregation.merchant.controller.journaling;

import cn.hutool.core.collection.CollectionUtil;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.entity.enums.ExportLocaleEnum;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.business.HandoverClientService;
import com.holderzone.saas.store.dto.business.manage.HandoverHistoryHandleDTO;
import com.holderzone.saas.store.dto.report.query.HandOverReportQueryDTO;
import com.holderzone.saas.store.dto.report.resp.HandoverReportRespDTO;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@Api("交接班相关接口")
@RestController
@RequestMapping("/handover")
public class HandoverController {

    private final HandoverClientService handoverClientService;

    @Autowired
    public HandoverController(HandoverClientService handoverClientService) {
        this.handoverClientService = handoverClientService;
    }

    @PostMapping("/report")
    @ApiOperation(value = "交接班报表查询")
    public Result<List<HandoverReportRespDTO>> report(@RequestBody @Valid HandOverReportQueryDTO handOverQueryDTO) {
        log.info("交接班报表查询入参：{}", JacksonUtils.writeValueAsString(handOverQueryDTO));
        List<HandoverReportRespDTO> report = handoverClientService.report(handOverQueryDTO);
        if(CollectionUtil.isNotEmpty(report)){
            report.forEach(r ->{
                r.setStatisticalType(PaymentTypeEnum.PaymentType.getLocaleName(r.getStatisticalType()));
                r.setStatisticalType(ExportLocaleEnum.getLocale(r.getStatisticalType()));
            });
        }
        log.info("交接班报表查询返回参数,report={}", JacksonUtils.writeValueAsString(report));
        return Result.buildSuccessResult(report).setNow(DateTimeUtils.localDateTime2Mills(DateTimeUtils.now()));
    }

    @ApiOperation(value = "处理交接班历史数据")
    @PostMapping("/handle_handover_history")
    public Result<Void> handleHandoverHistory(@RequestBody HandoverHistoryHandleDTO request) {
        log.info("merchant-处理交接班历史数据,request={}", JacksonUtils.writeValueAsString(request));
        handoverClientService.handleHandoverHistory(request);
        return Result.buildEmptySuccess();
    }
}
