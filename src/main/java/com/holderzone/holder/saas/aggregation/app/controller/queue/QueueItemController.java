package com.holderzone.holder.saas.aggregation.app.controller.queue;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.app.service.feign.queue.QueueItemClientService;
import com.holderzone.saas.store.dto.queue.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueItemController
 * @date 2019/03/27 17:06
 * @description //TODO
 * @program holder-saas-store-queue
 */
@RestController
@Api("排队操作")
@Slf4j
public class QueueItemController {
    @Autowired
    private QueueItemClientService queueItemClientService;

    @PostMapping("/queue/item/inQueue")
    @ApiOperation("取号")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "取号",action = OperatorType.SELECT)
    public Result<HolderQueueItemDetailDTO> inQueue(@Valid @RequestBody HolderQueueItemDTO dto){
        log.info("队列服务取号操作入参,{}",dto);
        return Result.buildSuccessResult(queueItemClientService.inQueue(dto));
    }

    @PostMapping("/queue/item/call")
    @ApiOperation("叫号")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "叫号",action = OperatorType.SELECT)
    public Result<HolderQueueItemDetailDTO> call( @RequestBody ItemGuidDTO dto){
        log.info("队列服务叫号操作入参,{}",dto);
        return Result.buildSuccessResult(queueItemClientService.call(dto));
    }

    @PostMapping("/queue/item/pass")
    @ApiOperation("过号")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "过号")
    public Result<HolderQueueItemDetailDTO> pass( @RequestBody ItemGuidDTO dto){
        log.info("队列服务过号操作入参,{}",dto);
        return Result.buildSuccessResult(queueItemClientService.pass(dto));
    }

    @PostMapping("/queue/item/confirm")
    @ApiOperation("就餐")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "就餐",action = OperatorType.SELECT)
    public Result<HolderQueueItemDetailDTO> confirm( @RequestBody ItemGuidDTO dto){
        log.info("队列服务就餐操作入参,{}",dto);
        return Result.buildSuccessResult(queueItemClientService.confirm(dto));
    }
    @PostMapping("/queue/item/recover")
    @ApiOperation("撤销")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "撤销",action = OperatorType.SELECT)
    public Result<HolderQueueItemDetailDTO> recover( @RequestBody ItemGuidDTO dto){
        log.info("队列服务撤销操作入参,{}",dto);
        return Result.buildSuccessResult(queueItemClientService.recover(dto));
    }
    @PostMapping("/queue/item/confirmAndTable")
    @ApiOperation("就餐并开台")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "就餐并开台",action = OperatorType.SELECT)
    public Result<TableQueueItemDetailDTO> confirmAndTable(@RequestBody TableConfirmDTO dto){
        return Result.buildSuccessResult(queueItemClientService.confirmAndTable(dto));
    }
    @PostMapping("/queue/records")
    @ApiOperation("历史记录")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "历史记录",action = OperatorType.SELECT)
    public Result<List<HolderQueueItemDetailDTO>> records(){
        return Result.buildSuccessResult(queueItemClientService.records());
    }
    @PostMapping("/queue/records/page")
    @ApiOperation("分页历史记录")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_QUEUE,description = "分页历史记录",action = OperatorType.SELECT)
    public Result<Page<HolderQueueItemDetailDTO>> page(@RequestBody Page page){
        return Result.buildSuccessResult(queueItemClientService.page(page));
    }
}