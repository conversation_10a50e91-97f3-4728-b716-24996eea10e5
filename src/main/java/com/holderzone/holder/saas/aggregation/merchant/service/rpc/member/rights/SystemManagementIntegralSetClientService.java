package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.rights;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.dto.rights.request.HsmIntegralSetReqDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @description 积分创建
 * @date 2019/5/17 13:48
 */
@Component
@FeignClient(name = "holder-saas-member-account",fallbackFactory =SystemManagementIntegralSetClientService.SystemManagementIntegralSetClientServiceFallBack.class )
public interface SystemManagementIntegralSetClientService {
    /**
     *  积分设置
     * @param hsmIntegralSetReqDTO
     * @return
     */
    @PostMapping(value = "/hsm-system-management-integral-set/create", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    String createIntegralRule(HsmIntegralSetReqDTO hsmIntegralSetReqDTO);
    /**
     *  积分设置
     * @param hsmIntegralSetReqDTO
     * @return
     */
    @PostMapping(value = "/hsm-system-management-integral-set/update", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    boolean updateIntegralRule(HsmIntegralSetReqDTO hsmIntegralSetReqDTO);


    @Slf4j
    @Component
    class SystemManagementIntegralSetClientServiceFallBack  implements FallbackFactory<SystemManagementIntegralSetClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";
        @Override
        public  SystemManagementIntegralSetClientService create(Throwable throwable) {
            return new SystemManagementIntegralSetClientService(){

                @Override
                public String createIntegralRule(HsmIntegralSetReqDTO hsmIntegralSetReqDTO) {
                    log.error(HYSTRIX_PATTERN, "createIntegralRule", JacksonUtils.writeValueAsString(hsmIntegralSetReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("积分设置失败");
                }

                @Override
                public boolean updateIntegralRule(HsmIntegralSetReqDTO hsmIntegralSetReqDTO) {
                    log.error(HYSTRIX_PATTERN, "updateIntegralRule", JacksonUtils.writeValueAsString(hsmIntegralSetReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("积分更新失败");
                }
            };
        }

    }
}
