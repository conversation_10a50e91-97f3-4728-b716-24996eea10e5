package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.card.RequestMemberCardRecharge;
import com.holderzone.holder.saas.member.terminal.dto.card.ResponseRecharge;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.member.terminal.dto.order.RequestActivityUse;
import com.holderzone.holder.saas.member.terminal.dto.order.RequestDiscount;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseActivityUse;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseDiscount;
import com.holderzone.holder.saas.member.terminal.dto.volume.*;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EntServiceClient
 * @date 2018/10/30 18:45
 * @description //TODO
 * @program holder-saas-store-member
 */
@Component
@FeignClient(name = "holder-saas-member-terminal", fallbackFactory = HsmTerminalServiceClient.FallBack.class)
public interface HsmTerminalServiceClient {

    /**
     * 给某卡充值
     *
     * @param memberInfoCardGuid
     * @param rechargeReqDTO
     * @return
     */
    @PostMapping("/hsmca/card/{memberInfoCardGuid}/recharge")
    ResponseRecharge cardCharge(@PathVariable("memberInfoCardGuid") String memberInfoCardGuid,
                                @RequestBody RequestMemberCardRecharge rechargeReqDTO);

    @PostMapping("/hsmca/volume/queryVolumePage")
    List<ResponseVolumeList> queryVolumePage(@RequestBody RequestVolumePageList reqDTO);

    @GetMapping("/hsmca/volume/getVolumeType")
    Integer getVolumeType(@RequestParam("volumeCode") String volumeCode,
                          @RequestParam("consumptionGuid") String consumptionGuid);

    @PostMapping("/hsmca/order/discount/calculate")
    ResponseDiscount calculateDiscount(@RequestBody RequestDiscount discountReqDTO);

    @PostMapping("/hsmca/volume/discount/calculate")
    ResponseVolumeCalculate calculateDiscount(@RequestBody RequestVolumeCalculate requestVolumeCalculate);

    @PostMapping("/hsmca/volume/consume")
    ResponseVolumeConsume consume(@RequestBody RequestVolumeConsume volumeConsumeReqDTO);

    @PostMapping("/hsmca/volume/{volumeCode}/cancel")
    List<RequestDishInfo> cancel(@PathVariable("volumeCode") String volumeCode,
                                 @RequestBody RequestVolumeCancel volumeCancelReqDTO);

    @PostMapping("/hsmca/activity/select")
    ResponseActivityUse activitySelect(@RequestBody RequestActivityUse reqDTO);


    /**
     * 根据优惠券码查询优惠券信息
     */
    @GetMapping("/hsmca/volume/getVolumeByCode")
    ResponseVolumeList getVolumeByCode(@RequestParam("volumeCode") String volumeCode);

    @Component
    class FallBack implements FallbackFactory<HsmTerminalServiceClient> {
        private static final Logger logger = LoggerFactory.getLogger(HsmTerminalServiceClient.FallBack.class);

        @Override
        public HsmTerminalServiceClient create(Throwable throwable) {
            return new HsmTerminalServiceClient() {
                @Override
                public ResponseRecharge cardCharge(String memberInfoCardGuid, RequestMemberCardRecharge rechargeReqDTO) {
                    logger.info("方法：cardCharge， 调用会员服务失败, 请求入参，memberInfoCardGuid:{}, rechargeReqDTO:{}， throwable :{}"
                            , memberInfoCardGuid, JacksonUtils.writeValueAsString(rechargeReqDTO), throwable);
                    throw new BusinessException("123");
                }

                @Override
                public List<ResponseVolumeList> queryVolumePage(RequestVolumePageList reqDTO) {
                    logger.info("方法：queryVolumePage， 调用会员服务失败, 请求入参，reqDTO:{}, throwable :{}"
                            , JacksonUtils.writeValueAsString(reqDTO), throwable);
                    throw new BusinessException("查询优惠券列表失败");
                }

                @Override
                public Integer getVolumeType(String volumeCode, String consumptionGuid) {
                    logger.info("方法：getVolumeType， 调用会员服务失败, 请求入参，volumeCode:{},consumptionGuid:{}, throwable :{}"
                            , volumeCode, consumptionGuid, throwable);
                    throw new BusinessException("查询优惠券列表失败");
                }

                @Override
                public ResponseDiscount calculateDiscount(RequestDiscount discountReqDTO) {
                    logger.info("方法：calculateDiscount， 调用会员服务失败, 请求入参，discountReqDTO:{}, throwable :{}"
                            , discountReqDTO, throwable);
                    throw new BusinessException("查询优惠券列表失败");
                }

                @Override
                public ResponseVolumeCalculate calculateDiscount(RequestVolumeCalculate requestVolumeCalculate) {
                    logger.info("方法：calculateDiscount， 调用会员服务失败, 请求入参，requestVolumeCalculate:{}, throwable :{}"
                            , requestVolumeCalculate, throwable);
                    throw new BusinessException("查询优惠券列表失败");
                }

                @Override
                public ResponseVolumeConsume consume(RequestVolumeConsume volumeConsumeReqDTO) {
                    logger.error("优惠券核销失败，args={}", JSON.toJSONString(volumeConsumeReqDTO));
                    throw new ParameterException("优惠券核销失败");
                }

                @Override
                public List<RequestDishInfo> cancel(String volumeCode, RequestVolumeCancel volumeCancelReqDTO) {
                    logger.error("取消优惠券失败，args={}", JSON.toJSONString(volumeCancelReqDTO));
                    throw new ParameterException("取消优惠券失败");
                }

                @Override
                public ResponseActivityUse activitySelect(RequestActivityUse reqDTO) {
                    logger.info("方法：activitySelect， 调用会员服务失败, 请求入参，RequestActivityUse:{}, throwable :{}"
                            , reqDTO, throwable);
                    throw new BusinessException("查询满减满折活动列表失败");
                }

                @Override
                public ResponseVolumeList getVolumeByCode(String volumeCode) {
                    logger.info("方法：getVolumeByCode， 调用会员服务失败, 请求入参，volumeCode:{}, throwable :{}"
                            , volumeCode, throwable);
                    throw new BusinessException("根据优惠券码查询优惠券信息失败");
                }
            };
        }
    }

}
