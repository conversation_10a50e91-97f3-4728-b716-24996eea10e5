package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/7/17 17:45
 * @description
 */
@ApiModel("门店信息")
@Data
public class WxStoreDTO {
    @ApiModelProperty("门店GUID")
    private String storeGuid;
    @ApiModelProperty("门店key，充值用")
    private String storeKey;
    @ApiModelProperty("门店名称")
    private String storeName;
}
