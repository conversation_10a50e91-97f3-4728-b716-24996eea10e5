package com.holderzone.holder.saas.aggregation.merchant.service;

import com.holderzone.saas.store.dto.reserve.HWReserveRecordDTO;
import com.holderzone.saas.store.dto.reserve.MerchantPhoneDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordDetailDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveService
 * @date 2019/05/28 16:21
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public interface ReserveService {
    ReserveRecordDetailDTO launch(HWReserveRecordDTO reserveRecordDTO);
    Boolean validate(MerchantPhoneDTO dto);
}