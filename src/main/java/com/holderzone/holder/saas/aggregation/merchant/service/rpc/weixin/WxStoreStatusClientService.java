package com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreStatusUpdateReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxCouldEditStoreDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreStatusRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreStatusClientService
 * @date 2019/05/13 10:00
 * @description 门店微信功能状态相关client
 * @program holder-saas-store
 */
@Component
@FeignClient(value = "holder-saas-store-weixin", fallbackFactory = WxStoreStatusClientService.FallBackService.class)
public interface WxStoreStatusClientService {

    @PostMapping("/wx_store_status/page_wx_store_status")
    Page<WxStoreStatusRespDTO> pageWxStoreStatus(WxStorePageReqDTO wxStorePageReqDTO);

    @PostMapping("/wx_store_status/update_status_by_guid")
    Boolean updateStatusByGuid(WxStoreStatusUpdateReqDTO wxStoreStatusUpdateReqDTO);

    @PostMapping("/wx_store_status/list_could_edit_store")
    List<WxCouldEditStoreDTO> listCouldEditStore(WxStoreReqDTO wxStoreReqDTO);

    @Component
    @Slf4j
    class FallBackService implements FallbackFactory<WxStoreStatusClientService> {
        @Override
        public WxStoreStatusClientService create(Throwable throwable) {
            return new WxStoreStatusClientService() {

                @Override
                public Page<WxStoreStatusRespDTO> pageWxStoreStatus(WxStorePageReqDTO wxStorePageReqDTO) {
                    log.error("获取微信门店业务状态失败，e{}", throwable.getMessage());
                    throw new RuntimeException("获取微信门店业务状态失败,e: {}", throwable.getCause());
                }

                @Override
                public Boolean updateStatusByGuid(WxStoreStatusUpdateReqDTO wxStoreStatusUpdateReqDTO) {
                    log.error("修改门店微信功能配置状态失败，e:{}", throwable.getMessage());
                    throw new RuntimeException("修改门店微信功能配置状态失败，微信服务调用失败,e :{}", throwable.getCause());
                }

                @Override
                public List<WxCouldEditStoreDTO> listCouldEditStore(WxStoreReqDTO wxStoreReqDTO) {
                    log.error("获取当前可编辑门店列表失败，e{}", throwable.getMessage());
                    throw new RuntimeException("获取当前可编辑门店列表失败");
                }
            };
        }
    }
}
