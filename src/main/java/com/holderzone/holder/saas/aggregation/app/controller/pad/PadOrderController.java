package com.holderzone.holder.saas.aggregation.app.controller.pad;

import cn.hutool.core.util.ObjectUtil;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.app.controller.organization.OrganizationController;
import com.holderzone.holder.saas.aggregation.app.service.MemberService;
import com.holderzone.holder.saas.aggregation.app.service.TableService;
import com.holderzone.holder.saas.aggregation.app.service.TradeService;
import com.holderzone.holder.saas.aggregation.app.service.WeChatService;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.account.NewMemberInfoClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.item.ItemClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.member.HssMemberWechatClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.table.TableClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInBillClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.OrderItemClientService;
import com.holderzone.holder.saas.aggregation.app.utils.ResponseModel;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestSaveCMemberDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestUpdatePassword;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestMemberBasic;
import com.holderzone.saas.store.dto.common.BaseRespDTO;
import com.holderzone.saas.store.dto.common.RedisReqDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemPadCalculateDTO;
import com.holderzone.saas.store.dto.item.req.OrderItemReqDTO;
import com.holderzone.saas.store.dto.member.common.LoginByPwdDTO;
import com.holderzone.saas.store.dto.member.request.MemberBindWeChatRespDTO;
import com.holderzone.saas.store.dto.member.request.PadLoginMemberReqDTO;
import com.holderzone.saas.store.dto.member.response.BindAndLoginRespDTO;
import com.holderzone.saas.store.dto.member.response.PadLoginMemberRespDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.retail.bill.constant.PayPowerId;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.table.OpenTableDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.trade.req.PadModifyGuestsNoReqDTO;
import com.holderzone.saas.store.dto.trade.req.PadOrderPlacementReqDTO;
import com.holderzone.saas.store.dto.trade.req.PadPayInfoReqDTO;
import com.holderzone.saas.store.dto.trade.resp.*;
import com.holderzone.saas.store.dto.weixin.auth.WeChatAuthParamDTO;
import com.holderzone.saas.store.enums.common.ResultStateEnum;
import com.holderzone.saas.store.enums.member.LoginStateEnum;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

import static com.holderzone.holder.saas.aggregation.app.constant.Constant.*;

/**
 * <AUTHOR>
 * @promise 包装返回：0成功 1代码异常 801服务异常
 * @description pad点餐相关接口 -Pad商品查询接口/query_item_list_to_pad
 * @date 2021/8/4 18:31
 * 其他pad接口
 * @see OrganizationController#padOrderTypeSet(com.holderzone.saas.store.dto.organization.PadOrderTypeReqDTO) 设置pad点餐模式
 */
@RestController
@RequestMapping("/pad")
@Api(tags = "pad点餐相关接口")
@Slf4j
@AllArgsConstructor
public class PadOrderController {

    /**
     * 微信支付
     */
    private static final String WECHAT_PAY = "micromessenger";

    /**
     * 阿里支付
     */
    private static final String ALI_PAY = "alipayclient";

    /**
     * holder-saas-member-terminal
     */
    private final NewMemberInfoClientService memberInfoClientService;

    private final HssMemberWechatClientService memberWechatClientService;

    private final ItemClientService itemClientService;

    private final OrderItemClientService orderItemClientService;

    private final TableService tableService;

    private final WeChatService weChatService;

    private final DineInBillClientService dineInBillClientService;

    private final TradeService tradeService;

    private final MemberService memberService;

    private final TableClientService tableClientService;


    @ApiOperation("pad开台接口")
    @PostMapping("/table/open")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE,
            description = "pad开台接口", action = OperatorType.SELECT)
    public Result<String> openTable(@RequestBody OpenTableDTO openTableDTO) {
        if (log.isInfoEnabled()) {
            log.info("开台入参：{}", JacksonUtils.writeValueAsString(openTableDTO));
        }
        // pad没有token需要手动设置头信息 storeNo,storeGuid,storeName,userGuid,userName
        UserContext userContext = UserContextUtils.get();
        userContext.setStoreNo(openTableDTO.getStoreNo());
        userContext.setStoreGuid(openTableDTO.getStoreGuid());
        userContext.setStoreName(openTableDTO.getStoreName());
        userContext.setUserGuid(openTableDTO.getUserGuid());
        userContext.setUserName(openTableDTO.getUserName());
        UserContextUtils.put(userContext);

        String result = tableService.openTable(openTableDTO);
        return Result.buildSuccessResult(result);
    }

    /**
     * pad会员注册
     *
     * @param saveCMemberDTO c端保存会员DTO
     * @return 0成功，1此手机号已注册
     */
    @PostMapping("/member_register")
    @ApiOperation(value = "pad会员注册")
    @ApiResponses({@ApiResponse(code = 60011, message = "此手机号已注册")})
    public Result<Integer> memberRegister(@Validated @RequestBody RequestSaveCMemberDTO saveCMemberDTO) {
        log.info("pad会员注册 saveCMemberDTO={}", JacksonUtils.writeValueAsString(saveCMemberDTO));
        try {
            memberInfoClientService.add(saveCMemberDTO);
        } catch (Exception e) {
            if (Objects.equals("手机号已存在", e.getMessage())) {
                return Result.buildSuccessResult(1);
            }
            return Result.buildOpFailedResult(e.getMessage());
        }
        return Result.buildSuccessResult(0);
    }

    /**
     * pad忘记原密码,发送修改密码的验证码
     *
     * @param phoneNum 手机号码
     * @return 发送结果
     */
    @ApiOperation(value = "pad忘记原密码,发送修改密码的验证码")
    @ApiImplicitParam(name = "phoneNum", value = "手机号码", required = true, dataType = "String")
    @GetMapping(value = "/pwd_code", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<BaseRespDTO> sendUpdatePasswordCode(@RequestParam("phoneNum") String phoneNum) {
        log.info("pad忘记原密码,发送修改密码的验证码 phoneNum={}", phoneNum);

        // 校验手机号是否注册
        Integer register = memberInfoClientService.checkRegisterByPhone(phoneNum);
        if (Objects.equals(1, register)) {
            return Result.buildSuccessResult(new BaseRespDTO()
                    .setResultState(ResultStateEnum.CURRENT_PHONE_NUMBER_IS_NOT_REGISTERED.getCode()));
        }

        boolean sendCode = memberInfoClientService.sendUpdatePasswordCode(phoneNum);
        if (sendCode) {
            return Result.buildSuccessResult(BaseRespDTO.constSuccess());
        } else {
            return Result.buildSystemErrResult("发送修改密码的验证码(忘记原密码)失败");
        }
    }

    /**
     * pad修改会员密码
     * 登陆密码默认等于支付密码
     *
     * @param updatePasswordReqDto 修改会员支付密码请求DTO
     * @return 0成功，1验证码失效，2验证码错误，3当前手机号未注册
     */
    @ApiOperation(value = "pad修改会员密码")
    @PostMapping(value = "/forget_password", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Integer> updatePassword(@Validated @RequestBody RequestUpdatePassword updatePasswordReqDto) {
        log.info("pad修改会员密码 updatePasswordReqDto={}", JacksonUtils.writeValueAsString(updatePasswordReqDto));
        return Result.buildSuccessResult(memberInfoClientService.updatePadPassword(updatePasswordReqDto));
    }

    /**
     * 发送登录验证码
     *
     * @param phoneNum 手机号码
     * @return 操作是否成功
     */
    @ApiOperation(value = "发送登录验证码")
    @GetMapping("/send_login_sms_code")
    public Result<BaseRespDTO> sendLoginSmsCode(@RequestParam(value = "phoneNum", required = true) String phoneNum) {
        log.info("发送登录验证码 phoneNum={}", phoneNum);

        // 校验手机号是否注册
        Integer register = memberInfoClientService.checkRegisterByPhone(phoneNum);
        if (Objects.equals(1, register)) {
            return Result.buildSuccessResult(new BaseRespDTO()
                    .setResultState(ResultStateEnum.CURRENT_PHONE_NUMBER_IS_NOT_REGISTERED.getCode()));
        }

        ResponseModel<Boolean> responseModel = memberWechatClientService.sendLoginSmsCode(phoneNum);
        if (responseModel.getCode() == 0) {
            return Result.buildSuccessResult(BaseRespDTO.constSuccess());
        } else {
            return Result.buildSystemErrResult(responseModel.getMessage());
        }
    }

    /**
     * 获取微信授权参数
     * 不需要参数
     *
     * @return 微信授权所需参数
     */
    @ApiOperation("获取微信授权参数")
    @GetMapping(value = "/get_we_chat_auth_param")
    public Result<WeChatAuthParamDTO> getWeChatAuthParam() throws IOException {
        return Result.buildSuccessResult(weChatService.getWeChatAuthParam());
    }

    /**
     * 发送微信绑定会员手机号的验证码
     *
     * @param phoneNum 手机号
     * @return 结果
     */
    @ApiOperation(value = "发送微信绑定会员手机号的验证码")
    @GetMapping(value = "/send_wechat_bind_code", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result<Boolean> sendWechatBindCode(@RequestParam("phoneNum") String phoneNum) {
        log.info("发送微信绑定会员手机号的验证码 phoneNum={}", phoneNum);
        return Result.buildSuccessResult(memberInfoClientService.sendWechatBindCode(phoneNum));
    }

    /**
     * 会员绑定微信
     *
     * @param memberBindWeChat 会员绑定微信实体
     * @return 返回结果
     */
    @ApiOperation("会员绑定微信")
    @PostMapping(value = "/bind_member_and_we_chat")
    public Result<BindAndLoginRespDTO> bindMemberAndWeChat(@RequestBody MemberBindWeChatRespDTO memberBindWeChat) {
        log.info("会员绑定微信 memberBindWeChat={}", JacksonUtils.writeValueAsString(memberBindWeChat));
        return Result.buildSuccessResult(memberInfoClientService.bindMemberAndWeChat(memberBindWeChat));
    }

    /**
     * pad登陆会员
     *
     * @param request pad登陆会员参数
     * @return pad登陆会员返回实体
     */
    @ApiOperation(value = "pad登陆会员,头部需要传递运营主体")
    @PostMapping("/pad_login_member")
    public Result<PadLoginMemberRespDTO> registerOrLoginMemberInfo(@RequestBody PadLoginMemberReqDTO request) throws IOException {
        log.info("pad登陆会员 request={}", JacksonUtils.writeValueAsString(request));
        switch (request.getLoginType()) {
            case 1:
                // 短信验证码登录
                return loginBySmsCode(request);
            case 2:
                // 帐号密码登录
                return loginByAccountAndPwd(request);
            case 3:
                // 第三方授权登录
                return loginByWeChatAuth(request);
            default:
                return Result.buildSystemErrResult(WRONG_LOGIN_METHOD);
        }
    }

    /**
     * 微信授权登录
     *
     * @param request pad登陆请求实体
     * @return 登录结果
     */
    private Result<PadLoginMemberRespDTO> loginByWeChatAuth(PadLoginMemberReqDTO request) throws IOException {
        String authCode = request.getAuthCode();
        if (StringUtils.isEmpty(request.getAuthCode())) {
            return Result.buildSystemErrResult(WECHAT_AUTHORIZATION_CODE_IS_EMPTY);
        }
        if (StringUtils.isEmpty(request.getOperSubjectGuid())) {
            return Result.buildSystemErrResult(OPERATION_SUBJECT_GUID_IS_EMPTY);
        }
        UserContext userContext = UserContextUtils.get();
        userContext.setOperSubjectGuid(request.getOperSubjectGuid());
        UserContextUtils.put(userContext);
        return Result.buildSuccessResult(weChatService.getWechatUserInfo(authCode));
    }

    /**
     * 帐号密码登录
     *
     * @param request 请求参数
     * @return 返回结果
     */
    private Result<PadLoginMemberRespDTO> loginByAccountAndPwd(PadLoginMemberReqDTO request) {
        LoginByPwdDTO pwdDTO = request.getLoginByPwdDTO();
        if (ObjectUtil.isNull(pwdDTO)) {
            throw new BusinessException(LOGIN_INPUT_PARAMETER_IS_EMPTY);
        }
        PadLoginMemberRespDTO loginByPwd = memberInfoClientService.loginByPwd(pwdDTO);
        return Result.buildSuccessResult(loginByPwd);
    }

    /**
     * 短信验证码登录
     *
     * @param request 请求参数
     * @return 返回结果
     */
    private Result<PadLoginMemberRespDTO> loginBySmsCode(PadLoginMemberReqDTO request) {
        RequestMemberBasic memberBasic = request.getMemberBasic();
        if (ObjectUtil.isNull(memberBasic)) {
            throw new BusinessException(LOGIN_INPUT_PARAMETER_IS_EMPTY);
        }

        // 短信验证码登录
        PadLoginMemberRespDTO loginMember = memberInfoClientService.loginBySmsCode(memberBasic);

        // 登录成功查询会员信息
        if (Objects.equals(LoginStateEnum.LOGIN_SUCCESSFUL.getCode(), loginMember.getLoginState())) {
            RequestQueryStoreAndMemberAndCard query = new RequestQueryStoreAndMemberAndCard();
            query.setPhoneNumOrCardNum(memberBasic.getPhoneNum());
            ResponseMemberAndCardInfoDTO memberInfoAndCard = memberInfoClientService.getMemberInfoAndCard(query);
            loginMember.setMemberAndCardInfoDTO(memberInfoAndCard);
        }
        return Result.buildSuccessResult(loginMember);
    }

    /**
     * 判断当前是否有不可下单的商品
     * 不可下单：商品下架、商品售罄、库存不足
     *
     * @param orderItemReqDTO 需要检查的规格guid
     * @return 不可下单的商品
     */
    @ApiOperation(value = "判断当前是否有不可下单的商品")
    @PostMapping(value = "/check_order_placement_item")
    public Result<List<ItemPadCalculateDTO>> checkOrderPlacementItem(@RequestBody OrderItemReqDTO orderItemReqDTO) {
        log.info("判断当前是否有不可下单的商品 orderItemReqDTO={}", JacksonUtils.writeValueAsString(orderItemReqDTO));
        return Result.buildSuccessResult(itemClientService.checkOrderPlacementItem(orderItemReqDTO));
    }

    /**
     * pad下单
     *
     * @param orderPlacementReqDTO pad下单请求实体
     * @return 订单guid
     */
    @ApiOperation("pad下单")
    @PostMapping(value = "/order_placement")
    public Result<PadOrderPlacementRespDTO> orderPlacement(@RequestBody PadOrderPlacementReqDTO orderPlacementReqDTO) {
        log.info("pad下单 createDineInOrderDTO={}", JacksonUtils.writeValueAsString(orderPlacementReqDTO));
        PadOrderPlacementRespDTO respDTO = orderItemClientService.orderPlacement(orderPlacementReqDTO);
        if (ObjectUtils.isEmpty(respDTO.getErrorMsg())) {
            return Result.buildSuccessResult(respDTO);
        } else {
            return Result.buildOpFailedResult(respDTO.getErrorMsg());
        }
    }

    /**
     * pad购物车价格计算
     *
     * @param orderPlacementReqDTO 购物车商品数据
     * @return 购物车总价
     */
    @ApiOperation("pad购物车价格计算")
    @PostMapping(value = "/calculate_shop_car")
    public Result<PadPriceRespDTO> calculateShopCar(@RequestBody PadOrderPlacementReqDTO orderPlacementReqDTO) {
        log.info("购物车价格计算 createDineInOrderDTO={}", JacksonUtils.writeValueAsString(orderPlacementReqDTO));
        return Result.buildSuccessResult(orderItemClientService.calculateShopCar(orderPlacementReqDTO));
    }

    /**
     * 查询pad订单详情
     *
     * @param orderGuid 订单guid
     * @return pad订单详情
     */
    @ApiOperation("查询pad订单详情")
    @GetMapping(value = "/query_pad_order_info")
    public Result<PadOrderInfoRespDTO> queryPadOrderInfo(@RequestParam("orderGuid") String orderGuid) {
        log.info("查询pad订单详情 orderGuid={}", orderGuid);
        return Result.buildSuccessResult(orderItemClientService.queryPadOrderInfo(orderGuid));
    }

    /**
     * 修改就餐人数
     *
     * @param modifyGuestsNoReqDTO 修改就餐人数请求实体
     * @return 结果
     */
    @ApiOperation("修改就餐人数")
    @PostMapping(value = "/modify_guests_no")
    public Result<BaseRespDTO> modifyGuestsNo(@RequestBody PadModifyGuestsNoReqDTO modifyGuestsNoReqDTO) {
        log.info("修改就餐人数 modifyGuestsNoReqDTO={}", JacksonUtils.writeValueAsString(modifyGuestsNoReqDTO));
        return Result.buildSuccessResult(orderItemClientService.modifyGuestsNo(modifyGuestsNoReqDTO));
    }

    /**
     * pad计算账单优惠
     * 处理token问题
     *
     * @param billCalculateReqDTO 计算参数
     * @return 计算结果
     */
    @ApiOperation(value = "pad计算账单优惠", notes = "pad计算账单优惠")
    @PostMapping("/calculate")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "计算账单优惠", action = OperatorType.SELECT)
    public Result<DineinOrderDetailRespDTO> calculate(@RequestBody BillCalculateReqDTO billCalculateReqDTO) {
        if (ObjectUtils.isEmpty(billCalculateReqDTO.getStoreGuid())) {
            throw new BusinessException("门店guid不能为空");
        }
        log.info("pad计算账单优惠 入参：{}", JacksonUtils.writeValueAsString(billCalculateReqDTO));
        UserContext userContext = UserContextUtils.get();
        userContext.setStoreGuid(billCalculateReqDTO.getStoreGuid());
        UserContextUtils.put(userContext);
        return Result.buildSuccessResult(dineInBillClientService.calculate(billCalculateReqDTO));
    }

    /**
     * 获取pad支付二维码
     *
     * @param orderGuid 订单guid
     * @return pad支付二维码
     */
    @ApiOperation("获取pad支付二维码")
    @PostMapping(value = "/get_pad_qr_code")
    public Result<PadQrCodeRespDTO> getPadQrCode(@RequestParam("orderGuid") String orderGuid) {
        log.info("获取pad支付二维码 orderGuid={}", orderGuid);
        return Result.buildSuccessResult(orderItemClientService.getPadQrCode(orderGuid));
    }

    /**
     * 保存pad支付信息到缓存
     *
     * @param padPayInfoReqDTO pad支付信息
     * @return Boolean
     */
    @ApiOperation("保存pad支付信息到缓存")
    @PostMapping(value = "/save_pad_pay_info_to_redis")
    public Result<Boolean> savePadPayInfoToRedis(@RequestBody PadPayInfoReqDTO padPayInfoReqDTO) {
        log.info("保存pad支付信息到缓存 padPayInfoReqDTO={}", JacksonUtils.writeValueAsString(padPayInfoReqDTO));
        return Result.buildSuccessResult(orderItemClientService.savePadPayInfoToRedis(padPayInfoReqDTO));
    }

    /**
     * pad支付二维码回调
     *
     * @param orderGuid 订单guid
     */
    @ApiOperation("pad支付二维码回调")
    @GetMapping(value = "/pad_pay_qr_callback")
    public void padPayQrCallback(@RequestParam("orderGuid") String orderGuid,
                                 @RequestParam("enterpriseGuid") String enterpriseGuid,
                                 HttpServletRequest request, HttpServletResponse response) throws IOException, InterruptedException {
        log.info("pad支付二维码回调 orderGuid={} enterpriseGuid={}", orderGuid, enterpriseGuid);
        String agent = request.getHeader("User-Agent").toLowerCase();
        log.info("响应头的类型：" + agent);

        // 获取缓存的头部数据填充
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid(enterpriseGuid);
        UserContextUtils.put(userContext);

        // 根据头部类型调用支付
        String codeUrl = "";
        if (agent.contains(WECHAT_PAY)) {
            log.info("微信扫码支付");
            codeUrl = tradeService.padPay(orderGuid, PayPowerId.YL_WX_SCAN_CODE.getId());
        } else if (agent.contains(ALI_PAY)) {
            log.info("阿里扫码支付");
            codeUrl = tradeService.padPay(orderGuid, PayPowerId.TL_AL_SCAN_CODE.getId());
        } else {
            throw new BusinessException("未知的扫码类型");
        }
        response.sendRedirect(codeUrl);
    }

    /**
     * 查询会员主体能否使用积分
     *
     * @param operSubjectGuid 运营主体Guid
     * @return 返回公共实体
     */
    @ApiOperation("查询会员主体能否使用积分")
    @GetMapping(value = "/query_member_use_integral")
    public Result<BaseRespDTO> queryMemberUseIntegral(@RequestParam("operSubjectGuid") String operSubjectGuid) {
        log.info("查询会员主体能否使用积分 operSubjectGuid={}", operSubjectGuid);
        return Result.buildSuccessResult(memberInfoClientService.queryDeductionRule(operSubjectGuid));
    }

    /**
     * 并台发送结账成功推送消息
     *
     * @param orderGuid 订单guid
     * @return 结果
     */
    @ApiOperation("并台发送结账成功推送消息")
    @GetMapping(value = "/send_msg_when_combine")
    public Result<Boolean> sendMsgWhenCombine(@RequestParam("orderGuid") String orderGuid) {
        log.info("并台发送结账成功推送消息 orderGuid={}", orderGuid);
        return Result.buildSuccessResult(tradeService.sendMsgWhenCombine(orderGuid));
    }

    /**
     * 保存pad转台信息
     *
     * @param redisReqDTO redis请求实体
     * @return 结果
     */
    @ApiOperation("保存pad转台信息")
    @PostMapping(value = "/save_pad_turn_info")
    public Result<Boolean> savePadTurnInfo(@RequestBody RedisReqDTO redisReqDTO) {
        log.info("保存pad转台信息 redisReqDTO={}", JacksonUtils.writeValueAsString(redisReqDTO));
        return Result.buildSuccessResult(orderItemClientService.savePadTurnInfo(redisReqDTO));
    }

    /**
     * 获取pad转台信息
     *
     * @param redisKey key
     * @return pad转台信息
     */
    @ApiOperation("获取pad转台信息")
    @GetMapping(value = "/get_pad_turn_info")
    public Result<String> getPadTurnInfo(@RequestParam("redisKey") String redisKey) {
        log.info("获取pad转台信息 redisKey={}", redisKey);
        return Result.buildSuccessResult(orderItemClientService.getPadTurnInfo(redisKey));
    }

    /**
     * 根据桌台查询订单guid和人数
     *
     * @param tableGuid 桌台guid
     * @return 订单guid和人数
     */
    @ApiOperation("根据桌台查询订单guid和人数")
    @GetMapping(value = "/query_order_and_guest_by_table")
    public Result<PadRebindRespDTO> queryOrderAndGuestByTable(@RequestParam("tableGuid") String tableGuid) {
        log.info("根据桌台查询订单guid和人数 tableGuid={}", tableGuid);
        return Result.buildSuccessResult(orderItemClientService.queryOrderAndGuestByTable(tableGuid));
    }

    /**
     * 获取订单详情
     *
     * @param singleDataDTO 订单Guid
     * @return 订单详情
     */
    @ApiOperation(value = "获取订单详情", notes = "获取订单详情")
    @PostMapping("/query_order_info")
    public Result<OrderDTO> queryOrderInfo(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("获取订单详情 singleDataDTO={}", JacksonUtils.writeValueAsString(singleDataDTO));
        return Result.buildSuccessResult(orderItemClientService.findByOrderGuid(singleDataDTO.getData()));
    }

    /**
     * 根据桌台guid，查桌台详情
     * 订单状态:只要接了一个单都是接单，否则没有
     *
     * @param tableGuid 桌台guid
     * @return 桌台详情
     */
    @ApiModelProperty("根据桌台guid，查桌台详情")
    @PostMapping("/details/{tableGuid}")
    public Result<TableDTO> getTableByGuid(@PathVariable("tableGuid") String tableGuid) {
        log.info("根据桌台guid，查桌台详情:{}", tableGuid);
        return Result.buildSuccessResult(tableService.queryTableByGuid(tableGuid));
    }

    /**
     * pad退出登录处理
     * 撤销验券和删除支付信息
     *
     * @param billCalculateReqDTO 计算接口的入参，用于撤销验券
     */
    @ApiOperation(value = "pad退出登录处理")
    @PostMapping("/pad_sign_out")
    public Result<Boolean> padSignOut(@RequestBody BillCalculateReqDTO billCalculateReqDTO) {
        log.info("pad退出登录处理 入参：{}", JacksonUtils.writeValueAsString(billCalculateReqDTO));
        return Result.buildSuccessResult(orderItemClientService.padSignOut(billCalculateReqDTO));
    }
}