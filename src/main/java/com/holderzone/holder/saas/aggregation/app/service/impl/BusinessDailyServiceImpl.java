package com.holderzone.holder.saas.aggregation.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.app.anno.DataAuthMethodControl;
import com.holderzone.holder.saas.aggregation.app.entity.auth.*;
import com.holderzone.holder.saas.aggregation.app.service.BusinessDailyService;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.account.MemberDataClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.reserve.ReserveClient;
import com.holderzone.holder.saas.aggregation.app.service.feign.staff.UserClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.takeout.TakeoutDailyClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DinnerDailyClientService;
import com.holderzone.holder.saas.aggregation.app.transform.BusinessDailyTransform;
import com.holderzone.holder.saas.aggregation.app.utils.BigDecimalUtil;
import com.holderzone.holder.saas.member.terminal.dto.statistics.RequestMemberDaily;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponseConsumpStatis;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponsePayWayDetail;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponseRechargeStatis;
import com.holderzone.holder.saas.member.terminal.enums.TerminalTypeEnum;
import com.holderzone.saas.store.dto.business.manage.TakeoutStatsDTO;
import com.holderzone.saas.store.dto.business.manage.TakeoutStatsQueryDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.dto.order.response.daily.*;
import com.holderzone.saas.store.dto.takeaway.OrderType;
import com.holderzone.saas.store.dto.user.MenuSourceDTO;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import com.holderzone.saas.store.enums.GroupAdapterEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.order.DailyReqQueryTypeEnum;
import com.holderzone.saas.store.enums.takeaway.OrderSubTypeEnum;
import com.holderzone.saas.store.enums.trade.RefundCodeEnum;
import com.holderzone.saas.store.enums.user.AuthorityReportHideEnum;
import com.holderzone.saas.store.util.LocaleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessDailyServiceImpl
 * @date 2019/02/15 17:45
 * @description
 * @program holder-saas-aggregation-app
 */
@Service
@Slf4j
public class BusinessDailyServiceImpl implements BusinessDailyService {

    private final DinnerDailyClientService dinnerDailyClientService;
    private final TakeoutDailyClientService takeoutDailyClientService;
    private final MemberDataClientService dataClientService;
    private final ReserveClient reserveClient;
    private final UserClientService userClientService;

    private final Executor reportExecutor;

    @Autowired
    public BusinessDailyServiceImpl(DinnerDailyClientService dinnerDailyClientService,
                                    TakeoutDailyClientService takeoutDailyClientService,
                                    MemberDataClientService dataClientService,
                                    ReserveClient reserveClient, UserClientService userClientService, Executor reportExecutor) {
        this.dinnerDailyClientService = dinnerDailyClientService;
        this.takeoutDailyClientService = takeoutDailyClientService;
        this.dataClientService = dataClientService;
        this.reserveClient = reserveClient;
        this.userClientService = userClientService;
        this.reportExecutor = reportExecutor;
    }

    @Override
    public List<DiningTypeRespDTO> diningType(DailyReqDTO request) {
        datePlusTime(request);
        List<DiningTypeRespDTO> resList = Lists.newArrayList();
        List<DiningTypeRespDTO> meals = dinnerDailyClientService.diningType(request);
        if (meals != null && meals.size() > 0) {
            for (int i = 0; i < meals.size(); i++) {
                resList.add(meals.get(i));
            }
        }
        //外卖统计
        DiningTypeRespDTO takeout = new DiningTypeRespDTO();
        takeout.setTypeName("外卖");
        takeout.setTypeCode(3);
        List<DiningTypeRespDTO> subDiningTypes = new ArrayList<>();
        TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid(request.getStoreGuid());
        takeoutStatsQueryDTO.setBeginTime(DateTimeUtils.string2LocalDateTime(request.getBeginTime()));
        takeoutStatsQueryDTO.setEndTime(DateTimeUtils.string2LocalDateTime(request.getEndTime()));
        TakeoutStatsDTO takeoutStatsDTO = takeoutDailyClientService.getTradeStats(takeoutStatsQueryDTO);
        if (takeoutStatsDTO != null) {
            takeout.setOrderCount(takeoutStatsDTO.getOrderCount());
            takeout.setAmount(takeoutStatsDTO.getSalesIncoming() == null ? BigDecimal.ZERO : takeoutStatsDTO.getSalesIncoming());
            if (takeoutStatsDTO.getSalesIncoming() != null) {
                takeout.setOrderPrice(takeoutStatsDTO.getSalesIncoming().divide(new BigDecimal(takeoutStatsDTO.getOrderCount()), 2, RoundingMode.HALF_UP));
            } else {
                takeout.setOrderPrice(BigDecimal.ZERO);
            }
            Map<String, Integer> orderCountDetail = takeoutStatsDTO.getOrderCountDetail();
            Map<String, BigDecimal> salesIncomingDetail = takeoutStatsDTO.getSalesIncomingDetail();
            Map<String, BigDecimal> salesIncomingAvgDetail = takeoutStatsDTO.getSalesIncomingAvgDetail();
            if (orderCountDetail != null) {
                for (String key : orderCountDetail.keySet()) {
                    DiningTypeRespDTO item = new DiningTypeRespDTO();
                    item.setTypeName(key);
                    item.setOrderCount(orderCountDetail.get(key));
                    item.setAmount(BigDecimal.ZERO);
                    item.setOrderPrice(BigDecimal.ZERO);
                    if (salesIncomingDetail != null) {
                        item.setAmount(salesIncomingDetail.get(key));
                    }
                    if (salesIncomingAvgDetail != null) {
                        item.setOrderPrice(salesIncomingAvgDetail.get(key));
                    }
                    subDiningTypes.add(item);
                }
            }
        }
        takeout.setSubDiningTypes(subDiningTypes);
        resList.add(takeout);

        //排序处理
        if (request.getOrderItem() != null) {
            if (request.getOrderItem() == 1) {
                //销售收入
                Collections.sort(resList, new Comparator<DiningTypeRespDTO>() {
                    @Override
                    public int compare(DiningTypeRespDTO o1, DiningTypeRespDTO o2) {
                        return (request.getOrderType() != null && request.getOrderType() == 2) ? (o2.getAmount().compareTo(o1.getAmount())) : (o1.getAmount().compareTo(o2.getAmount()));
                    }
                });
            }
            if (request.getOrderItem() == 2) {
                //单均消费
                Collections.sort(resList, new Comparator<DiningTypeRespDTO>() {
                    @Override
                    public int compare(DiningTypeRespDTO o1, DiningTypeRespDTO o2) {
                        return (request.getOrderType() != null && request.getOrderType() == 2) ? (o2.getOrderPrice().compareTo(o1.getOrderPrice())) : (o1.getOrderPrice().compareTo(o2.getOrderPrice()));
                    }
                });
            }
            if (request.getOrderItem() == 3) {
                //人均消费
                Collections.sort(resList, new Comparator<DiningTypeRespDTO>() {
                    @Override
                    public int compare(DiningTypeRespDTO o1, DiningTypeRespDTO o2) {
                        if (request.getOrderType() != null && request.getOrderType() == 2) {
                            if (o2.getGuestPrice() == null && o1.getGuestPrice() == null) {
                                return 0;
                            }
                            if (o2.getGuestPrice() == null) {
                                return -1;
                            }
                            if (o1.getGuestPrice() == null) {
                                return 1;
                            }
                            return o2.getGuestPrice().compareTo(o1.getGuestPrice());
                        } else {
                            if (o2.getGuestPrice() == null && o1.getGuestPrice() == null) {
                                return 0;
                            }
                            if (o2.getGuestPrice() == null) {
                                return 1;
                            }
                            if (o1.getGuestPrice() == null) {
                                return -1;
                            }
                            return o1.getGuestPrice().compareTo(o2.getGuestPrice());
                        }
                    }
                });
            }
        }
        //计算合计
        DiningTypeRespDTO total = new DiningTypeRespDTO();
        total.setIsTotal(1);
        total.setTypeName("合计");
        //订单数
        total.setOrderCount(resList.stream().mapToInt(item -> {
            if (item != null && item.getOrderCount() != null) {
                return item.getOrderCount();
            } else {
                return 0;
            }
        }).sum());
        //消费人数
        total.setGuestCount(resList.stream().mapToInt(item -> {
            if (item != null && item.getGuestCount() != null) {
                return item.getGuestCount();
            } else {
                return 0;
            }
        }).sum());
        //销售收入
        total.setAmount(resList.stream().map(item -> {
            if (item != null && item.getAmount() != null) {
                return item.getAmount();
            } else {
                return BigDecimal.ZERO;
            }
        }).reduce(BigDecimal.ZERO, BigDecimal::add));
        //单均消费
       /* if (total.getOrderCount() > 0) {
            total.setOrderPrice(total.getAmount().divide(new BigDecimal(total.getOrderCount()), 2, RoundingMode.HALF_UP));
        }*/
        resList.add(total);
        return resList;
    }


    @Override
    @DataAuthMethodControl
    public List<DiningTypeSaleDTO> diningTypeSale(DailyReqDTO request) {
        List<DiningTypeRespDTO> diningTypeRespList = diningType(request);
        if (CollectionUtils.isEmpty(diningTypeRespList)) {
            return Lists.newArrayList();
        }
        return BusinessDailyTransform.INSTANCE.diningTypeRespList2DiningTypeSaleDTO(diningTypeRespList);
    }

    @Override
    public List<ItemRespDTO> classify(DailyReqDTO request) {
        // 正餐统计
        List<ItemRespDTO> dinnerList = Lists.newArrayList();
        if (DailyReqQueryTypeEnum.isDinner(request.getQueryType())) {
            dinnerList = dinnerDailyClientService.classify(request);
        }
        log.info("查询商品分类堂食数据：{}", JacksonUtils.writeValueAsString(dinnerList));
        // 外卖统计
        List<ItemRespDTO> takeoutItemList = Lists.newArrayList();
        if (DailyReqQueryTypeEnum.isTakeaway(request.getQueryType())) {
            List<Integer> hideTakeawaySubTypes = queryAuthorityReportHideTakeawaySubTypes();
            request.setReportHideSubTypes(hideTakeawaySubTypes);
            takeoutItemList = takeoutDailyClientService.listItemSale(request);
        }
        log.info("查询商品分类外卖数据：{}", JacksonUtils.writeValueAsString(takeoutItemList));

        //合并结果
        List<ItemRespDTO> mergeList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dinnerList)) {
            dinnerList.forEach(d -> {
                if (d.getName().contains("SURCHARGE_PER")) {
                    d.setName(LocaleUtil.getMessage(d.getName()));
                }
                d.setDinnerAmount(d.getAmount());
                d.setDinnerQuantum(d.getQuantum());
                d.setDinnerDiscountAmount(d.getDiscountAmount());
            });
            mergeList.addAll(dinnerList);
        }
        if (CollectionUtils.isNotEmpty(takeoutItemList)) {
            takeoutItemList.forEach(t -> {
                t.setAmount(t.getTakeoutAmount());
                t.setQuantum(t.getTakeoutQuantum());
                t.setDiscountAmount(t.getAmount());
            });
            mergeList.addAll(takeoutItemList);
        }
        if (CollectionUtils.isEmpty(mergeList)) {
            return Collections.emptyList();
        }

        List<ItemRespDTO> sortedList = new ArrayList<>(mergeList.stream()
                .collect(Collectors.toMap(ItemRespDTO::getName, record -> record, (o1, o2) -> {
                    ItemRespDTO total = new ItemRespDTO();
                    total.setGuid(o1.getGuid());
                    total.setName(o1.getName());
                    //堂食
                    total.setDinnerQuantum(BigDecimalUtil.nonNullValue(o1.getDinnerQuantum()).add(BigDecimalUtil.nonNullValue(o2.getDinnerQuantum())));
                    total.setDinnerAmount(BigDecimalUtil.nonNullValue(o1.getDinnerAmount()).add(BigDecimalUtil.nonNullValue(o2.getDinnerAmount())));
                    total.setDinnerDiscountAmount(BigDecimalUtil.nonNullValue(o1.getDinnerDiscountAmount()).add(BigDecimalUtil.nonNullValue(o2.getDinnerDiscountAmount())));
                    //外卖
                    total.setTakeoutQuantum(BigDecimalUtil.nonNullValue(o1.getTakeoutQuantum()).add(BigDecimalUtil.nonNullValue(o2.getTakeoutQuantum())));
                    total.setTakeoutDiscountAmount(BigDecimalUtil.nonNullValue(o1.getTakeoutAmount()).add(BigDecimalUtil.nonNullValue(o2.getTakeoutAmount())));
                    total.setTakeoutAmount(BigDecimalUtil.nonNullValue(o1.getTakeoutAmount()).add(BigDecimalUtil.nonNullValue(o2.getTakeoutAmount())));

                    //合计
                    total.setQuantum(total.getDinnerQuantum().add(total.getTakeoutQuantum()));
                    total.setAmount(total.getDinnerAmount().add(total.getTakeoutAmount()).setScale(2, RoundingMode.HALF_UP));
                    total.setDiscountAmount(total.getDinnerDiscountAmount().add(total.getTakeoutDiscountAmount()).setScale(2, RoundingMode.HALF_UP));
                    return total;
                })).values());
        //排序
        sortItemClassifySale(sortedList, request.getOrderItem(), request.getOrderType(), true);

        log.info("查询商品分类数据：{}", JacksonUtils.writeValueAsString(sortedList));
        //将附加费放在最前面
        List<ItemRespDTO> respList = sortedList.stream().sorted(Comparator.comparingInt(i -> i.getGuid() != null && (i.getGuid().equals("0") || i.getGuid().equals("1")) ? 0 : 1)).collect(Collectors.toList());
        respList.add(itemTotal(sortedList));
        return respList;
    }

    @Override
    @DataAuthMethodControl
    public List<CategorySaleDTO> classifySale(DailyReqDTO request) {
        List<ItemRespDTO> classifyRespList = classify(request);
        if (CollectionUtils.isEmpty(classifyRespList)) {
            return Lists.newArrayList();
        }
        return BusinessDailyTransform.INSTANCE.itemRespDTO2CategorySaleDTO(classifyRespList);
    }

    private static void sortItemClassifySale(List<ItemRespDTO> sortedList, Integer orderItem, Integer orderType, boolean classify) {
        if (CollectionUtil.isEmpty(sortedList)) {
            return;
        }
        if (orderItem == null) {
            return;
        }
        //倒叙
        boolean reverse = orderType != null && orderType == 2;

        //数量排序
        Function<ItemRespDTO, BigDecimal> function = classify ? ITEM_CLASSIFY_SORT.get(orderItem) : ITEM_GOODS_SORT.get(orderItem);
        if (function == null) {
            return;
        }
        if (reverse) {
            sortedList.sort(Comparator.comparing(function).reversed());
            return;
        }
        sortedList.sort(Comparator.comparing(function));

    }

    private void sortItemUnitPrice(List<ItemRespDTO> sortedList, Integer orderItem, boolean reverse) {
        //单价排序
        sortedList.sort((o1, o2) -> {
            BigDecimal o1Price = orderItem == 1 ? o1.getUnitPrice() : o1.getTakeoutUnitPrice();
            BigDecimal o2Price = orderItem == 1 ? o2.getUnitPrice() : o2.getTakeoutUnitPrice();
            if (reverse) {
                if (o2Price == null) {
                    return 1;
                }
                if (o1Price == null) {
                    return -1;
                }
                return o2Price.compareTo(o1Price);
            } else {
                if (o2Price == null) {
                    return -1;
                }
                if (o1Price == null) {
                    return 1;
                }
                return o1Price.compareTo(o2Price);
            }
        });
    }

    private static final Map<Integer, Function<ItemRespDTO, BigDecimal>> ITEM_CLASSIFY_SORT;

    private static final Map<Integer, Function<ItemRespDTO, BigDecimal>> ITEM_GOODS_SORT;

    static {
        ITEM_CLASSIFY_SORT = Maps.newHashMap();
        ITEM_CLASSIFY_SORT.put(1, (ItemRespDTO item) -> BigDecimalUtil.nonNullValue(item.getDinnerQuantum()));
        ITEM_CLASSIFY_SORT.put(2, (ItemRespDTO item) -> BigDecimalUtil.nonNullValue(item.getDinnerAmount()));
        ITEM_CLASSIFY_SORT.put(3, (ItemRespDTO item) -> BigDecimalUtil.nonNullValue(item.getTakeoutQuantum()));
        ITEM_CLASSIFY_SORT.put(4, (ItemRespDTO item) -> BigDecimalUtil.nonNullValue(item.getTakeoutAmount()));
        ITEM_CLASSIFY_SORT.put(5, (ItemRespDTO item) -> BigDecimalUtil.nonNullValue(item.getQuantum()));
        ITEM_CLASSIFY_SORT.put(6, (ItemRespDTO item) -> BigDecimalUtil.nonNullValue(item.getAmount()));
        ITEM_CLASSIFY_SORT.put(7, (ItemRespDTO item) -> BigDecimalUtil.nonNullValue(item.getDiscountAmount()));

        ITEM_GOODS_SORT = Maps.newHashMap();
        ITEM_GOODS_SORT.put(1, (ItemRespDTO item) -> BigDecimalUtil.nonNullValue(item.getUnitPrice()));
        ITEM_GOODS_SORT.put(2, (ItemRespDTO item) -> BigDecimalUtil.nonNullValue(item.getDinnerQuantum()));
        ITEM_GOODS_SORT.put(3, (ItemRespDTO item) -> BigDecimalUtil.nonNullValue(item.getDinnerAmount()));
        ITEM_GOODS_SORT.put(4, (ItemRespDTO item) -> BigDecimalUtil.nonNullValue(item.getTakeoutUnitPrice()));
        ITEM_GOODS_SORT.put(5, (ItemRespDTO item) -> BigDecimalUtil.nonNullValue(item.getTakeoutQuantum()));
        ITEM_GOODS_SORT.put(6, (ItemRespDTO item) -> BigDecimalUtil.nonNullValue(item.getTakeoutAmount()));
        ITEM_GOODS_SORT.put(7, (ItemRespDTO item) -> BigDecimalUtil.nonNullValue(item.getQuantum()));
        ITEM_GOODS_SORT.put(8, (ItemRespDTO item) -> BigDecimalUtil.nonNullValue(item.getAmount()));

        ITEM_GOODS_SORT.put(9, (ItemRespDTO item) -> BigDecimalUtil.nonNullValue(item.getDinnerDiscountAmount()));
        ITEM_GOODS_SORT.put(10, (ItemRespDTO item) -> BigDecimalUtil.nonNullValue(item.getDiscountAmount()));
    }

    @Override
    public List<ItemRespDTO> goods(DailyReqDTO request) {
        UserContext userContext = UserContextUtils.get();
        CompletableFuture<List<ItemRespDTO>> dinnerListFuture = CompletableFuture.supplyAsync(() -> {
            UserContextUtils.put(userContext);
            //正餐统计
            List<ItemRespDTO> dinner = dinnerDailyClientService.goods(request);
            //名称相同进行合并
            List<ItemRespDTO> mergeNameList = mergeNameAlikeList(dinner);
            log.info("查询商品堂食数据：{}", JSON.toJSONString(mergeNameList));
            return mergeNameList;
        }, reportExecutor);
        CompletableFuture<List<ItemRespDTO>> takeoutListFuture = CompletableFuture.supplyAsync(() -> {
            UserContextUtils.put(userContext);
            //外卖统计
            List<Integer> hideTakeawaySubTypes = queryAuthorityReportHideTakeawaySubTypes();
            request.setReportHideSubTypes(hideTakeawaySubTypes);
            List<ItemRespDTO> takeout = takeoutDailyClientService.goods(request);
            log.info("查询商品外卖数据：{}", JSON.toJSONString(takeout));
            return takeout;
        }, reportExecutor);
        CompletableFuture<Void> all = CompletableFuture.allOf(dinnerListFuture, takeoutListFuture);
        try {
            all.get();
            //获取结果集
            return goodsHandle(dinnerListFuture.get(), takeoutListFuture.get(), request.getOrderItem(), request.getOrderType());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BusinessException("查询报表中断");
        } catch (Exception e) {
            log.error("查询报表异常", e);
            throw new BusinessException("查询报表有误");
        }

    }

    @Override
    @DataAuthMethodControl
    public List<GoodsSaleDTO> goodsSale(DailyReqDTO request) {
        List<ItemRespDTO> goodsRespList = goods(request);
        if (CollectionUtils.isEmpty(goodsRespList)) {
            return Lists.newArrayList();
        }
        return BusinessDailyTransform.INSTANCE.itemRespDTO2GoodsSaleDTO(goodsRespList);
    }

    private List<ItemRespDTO> mergeNameAlikeList(List<ItemRespDTO> dinner) {
        if (CollUtil.isEmpty(dinner)) {
            return dinner;
        }
        List<ItemRespDTO> list = Lists.newArrayList();
        Map<String, List<ItemRespDTO>> collect = dinner.stream().collect(Collectors.groupingBy(ItemRespDTO::getName, Collectors.toList()));
        collect.forEach((k, v) -> {
            if (v.size() == 1) {
                list.add(v.get(0));
                return;
            }
            ItemRespDTO dto = new ItemRespDTO();
            dto.setName(k);
            dto.setHasSubInfo(1);
            v.forEach(e -> {
                if (CollUtil.isEmpty(dto.getSubs())) {
                    dto.setSubs(Lists.newArrayList());
                }
                //判断相同名称的商品是否存在子商品
                if (CollUtil.isEmpty(e.getSubs())) {
                    e.setName("");
                    dto.getSubs().add(e);
                } else {
                    dto.getSubs().addAll(e.getSubs());
                }

                dto.setAmount(BigDecimalUtil.nonNullValue(dto.getAmount()).add(e.getAmount()));
                dto.setQuantum(BigDecimalUtil.nonNullValue(dto.getQuantum()).add(e.getQuantum()));
                dto.setDiscountAmount(BigDecimalUtil.nonNullValue(dto.getDiscountAmount()).add(e.getDiscountAmount()));
            });
            list.add(dto);
        });
        return list;
    }

    private static List<ItemRespDTO> goodsHandle(List<ItemRespDTO> dinner, List<ItemRespDTO> takeout, Integer orderItem, Integer orderType) {
        //合并结果
        List<ItemRespDTO> merge = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(dinner)) {
            merge.addAll(dinner);
        }
        if (CollectionUtil.isNotEmpty(takeout)) {
            takeout.forEach(e -> {
                e.setDiscountAmount(e.getTakeoutAmount());
                e.setTakeoutDiscountAmount(e.getTakeoutAmount());
            });
            merge.addAll(takeout);
        }
        if (CollectionUtil.isEmpty(merge)) {
            return Collections.emptyList();
        }

        List<ItemRespDTO> list = assemblerMerge(merge);
        log.info("查询商品数据：{}", JSONArray.toJSONString(list));
        //排序
        sortItemClassifySale(list, orderItem, orderType, false);
        //把附加费放在前面
        List<ItemRespDTO> respList = list.stream().sorted(Comparator.comparingInt(i -> i.getItemType() != null && i.getItemType() == 0 ? 0 : 1)).collect(Collectors.toList());
        respList.add(itemTotal(list));
        return respList;
    }

    private static List<ItemRespDTO> assemblerMerge(List<ItemRespDTO> merge) {

        Map<String, ItemRespDTO> map = Maps.newHashMap();
        merge.forEach(m -> {
            String key = m.getName();

            //判断如果是堂食
            if (m.getTakeoutQuantum() == null) {
                m.setDinnerAmount(m.getAmount());
                m.setDinnerQuantum(m.getQuantum());
                m.setDinnerDiscountAmount(m.getDiscountAmount());
            }

            ItemRespDTO dto = map.get(key);
            if (dto == null) {
                //如果是堂食特殊处理
                if (m.getHasSubInfo() == 1 && m.getTakeoutQuantum() == null) {
                    m.getSubs().forEach(s -> {
                        s.setDinnerQuantum(s.getQuantum());
                        s.setDinnerAmount(s.getAmount());
                        s.setDinnerDiscountAmount(s.getDiscountAmount());
                    });
                    List<ItemRespDTO> respList = mergeSubItem(m.getSubs());
                    if (respList.size() == 1) {
                        respList.get(0).setName(m.getName());
                        map.put(key, respList.get(0));
                        return;
                    }
                    m.setSubs(respList);
                }
                map.put(key, m);
                return;
            }
            //若有子商品
            map.put(key, dealSubItem(dto, m));
        });
        return Arrays.asList(map.values().toArray(new ItemRespDTO[]{}));
    }

    private static ItemRespDTO itemTotal(List<ItemRespDTO> list) {
        ItemRespDTO total = new ItemRespDTO();
        total.setName("合计");
        total.setIsTotal(1);

        total.setDinnerQuantum(list.stream().map(item -> BigDecimalUtil.nonNullValue(item.getDinnerQuantum())).reduce(BigDecimal.ZERO, BigDecimal::add));
        total.setDinnerAmount(list.stream().map(item -> BigDecimalUtil.nonNullValue(item.getDinnerAmount())).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        total.setDinnerDiscountAmount(list.stream().map(item -> BigDecimalUtil.nonNullValue(item.getDinnerDiscountAmount())).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));

        total.setTakeoutQuantum(list.stream().map(item -> BigDecimalUtil.nonNullValue(item.getTakeoutQuantum())).reduce(BigDecimal.ZERO, BigDecimal::add));
        total.setTakeoutAmount(list.stream().map(item -> BigDecimalUtil.nonNullValue(item.getTakeoutAmount())).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));

        total.setQuantum(list.stream().map(item -> BigDecimalUtil.nonNullValue(item.getQuantum())).reduce(BigDecimal.ZERO, BigDecimal::add));
        total.setAmount(list.stream().map(item -> BigDecimalUtil.nonNullValue(item.getAmount())).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        total.setDiscountAmount(list.stream().map(item -> BigDecimalUtil.nonNullValue(item.getDiscountAmount())).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        return total;
    }

    private static ItemRespDTO dealSubItem(ItemRespDTO dto, ItemRespDTO m) {

        if (dto.getHasSubInfo() == 1) {
            dto.setDinnerQuantum(BigDecimalUtil.nonNullValue(dto.getDinnerQuantum()).add(BigDecimalUtil.nonNullValue(m.getDinnerQuantum())));
            dto.setDinnerAmount(BigDecimalUtil.nonNullValue(dto.getDinnerAmount()).add(BigDecimalUtil.nonNullValue(m.getDinnerAmount())).setScale(2, RoundingMode.HALF_UP));
            dto.setDinnerDiscountAmount(BigDecimalUtil.nonNullValue(dto.getDinnerDiscountAmount()).add(BigDecimalUtil.nonNullValue(m.getDinnerDiscountAmount())).setScale(2, RoundingMode.HALF_UP));

            dto.setTakeoutQuantum(BigDecimalUtil.nonNullValue(dto.getTakeoutQuantum()).add(BigDecimalUtil.nonNullValue(m.getTakeoutQuantum())));
            dto.setTakeoutDiscountAmount(BigDecimalUtil.nonNullValue(dto.getTakeoutAmount()).add(BigDecimalUtil.nonNullValue(m.getTakeoutAmount())).setScale(2, RoundingMode.HALF_UP));
            dto.setTakeoutAmount(BigDecimalUtil.nonNullValue(dto.getTakeoutAmount()).add(BigDecimalUtil.nonNullValue(m.getTakeoutAmount())).setScale(2, RoundingMode.HALF_UP));

            dto.setQuantum(dto.getDinnerQuantum().add(dto.getTakeoutQuantum()));
            dto.setAmount(dto.getDinnerAmount().add(dto.getTakeoutAmount()));
            dto.setDiscountAmount(dto.getDinnerDiscountAmount().add(dto.getTakeoutDiscountAmount()));
            List<ItemRespDTO> subs = dto.getSubs();
            if (m.getHasSubInfo() == 1) {
                subs.addAll(m.getSubs());
            } else {
                subs.add(m);
            }
            //处理子商品
            dto.setSubs(mergeSubItem(subs));
            return dto;
        }
        List<ItemRespDTO> list = Lists.newArrayList(dto);
        if (m.getHasSubInfo() == 1) {
            list.addAll(m.getSubs());
        } else {
            list.add(m);
        }
        List<ItemRespDTO> respList = mergeSubItem(list);
        if (respList.size() == 1) {
            return respList.get(0);
        }

        ItemRespDTO resp = new ItemRespDTO();
        resp.setGuid(dto.getGuid());
        resp.setName(dto.getName());
        resp.setSubs(respList);
        resp.setHasSubInfo(1);
        resp.setUnitPrice(null);
        resp.setTakeoutUnitPrice(null);

        resp.setDinnerQuantum(BigDecimalUtil.nonNullValue(dto.getDinnerQuantum()).add(BigDecimalUtil.nonNullValue(m.getDinnerQuantum())));
        resp.setDinnerAmount(BigDecimalUtil.nonNullValue(dto.getDinnerAmount()).add(BigDecimalUtil.nonNullValue(m.getDinnerAmount())).setScale(2, RoundingMode.HALF_UP));
        resp.setDinnerDiscountAmount(BigDecimalUtil.nonNullValue(dto.getDinnerDiscountAmount()).add(BigDecimalUtil.nonNullValue(m.getDinnerDiscountAmount())).setScale(2, RoundingMode.HALF_UP));

        resp.setTakeoutQuantum(BigDecimalUtil.nonNullValue(dto.getTakeoutQuantum()).add(BigDecimalUtil.nonNullValue(m.getTakeoutQuantum())));
        resp.setTakeoutAmount(BigDecimalUtil.nonNullValue(dto.getTakeoutAmount()).add(BigDecimalUtil.nonNullValue(m.getTakeoutAmount())).setScale(2, RoundingMode.HALF_UP));
        resp.setTakeoutDiscountAmount(BigDecimalUtil.nonNullValue(dto.getTakeoutAmount()).add(BigDecimalUtil.nonNullValue(m.getTakeoutAmount())).setScale(2, RoundingMode.HALF_UP));

        resp.setQuantum(resp.getDinnerQuantum().add(resp.getTakeoutQuantum()));
        resp.setAmount(resp.getDinnerAmount().add(resp.getTakeoutAmount()).setScale(2, RoundingMode.HALF_UP));
        resp.setDiscountAmount(resp.getDinnerDiscountAmount().add(resp.getTakeoutDiscountAmount()).setScale(2, RoundingMode.HALF_UP));
        return resp;


    }

    private static List<ItemRespDTO> mergeSubItem(List<ItemRespDTO> subs) {

        Map<String, ItemRespDTO> map = Maps.newHashMap();
        //过滤出堂食价格不为空得
        List<ItemRespDTO> collectUnitPrice = subs.stream().filter(e -> e.getUnitPrice() != null).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(collectUnitPrice)) {
            mergePrice(map, collectUnitPrice);
            subs.removeAll(collectUnitPrice);
        }
        if (CollectionUtil.isNotEmpty(subs)) {
            mergePrice(map, subs);
        }
        return Lists.newArrayList(map.values());
    }

    private static void mergePrice(Map<String, ItemRespDTO> map, List<ItemRespDTO> collectUnitPrice) {
        //根据规格名称和单价去重
        collectUnitPrice.forEach(s -> {
            String key = (s.getSkuName() == null ? "" : s.getSkuName()) + "_" + (s.getUnitPrice() == null ?
                    BigDecimalUtil.nonNullValue(s.getTakeoutUnitPrice()).setScale(2, RoundingMode.HALF_UP)
                    : BigDecimalUtil.nonNullValue(s.getUnitPrice()).setScale(2, RoundingMode.HALF_UP));
            ItemRespDTO existItem = map.get(key);
            if (existItem == null) {
                map.put(key, s);
                return;
            }
            //合并
            if (s.getUnitPrice() != null && existItem.getUnitPrice() == null) {
                existItem.setUnitPrice(s.getUnitPrice());
            }
            if (s.getTakeoutUnitPrice() != null && existItem.getTakeoutUnitPrice() == null) {
                existItem.setTakeoutUnitPrice(s.getTakeoutUnitPrice());
            }
            existItem.setDinnerQuantum(BigDecimalUtil.nonNullValue(existItem.getDinnerQuantum()).add(BigDecimalUtil.nonNullValue(s.getDinnerQuantum())));
            existItem.setDinnerAmount(BigDecimalUtil.nonNullValue(existItem.getDinnerAmount()).add(BigDecimalUtil.nonNullValue(s.getDinnerAmount())).setScale(2, RoundingMode.HALF_UP));
            existItem.setDinnerDiscountAmount(BigDecimalUtil.nonNullValue(existItem.getDinnerDiscountAmount()).add(BigDecimalUtil.nonNullValue(s.getDinnerDiscountAmount())).setScale(2, RoundingMode.HALF_UP));

            existItem.setTakeoutQuantum(BigDecimalUtil.nonNullValue(existItem.getTakeoutQuantum()).add(BigDecimalUtil.nonNullValue(s.getTakeoutQuantum())));
            existItem.setTakeoutDiscountAmount(BigDecimalUtil.nonNullValue(existItem.getTakeoutAmount()).add(BigDecimalUtil.nonNullValue(s.getTakeoutAmount())).setScale(2, RoundingMode.HALF_UP));
            existItem.setTakeoutAmount(BigDecimalUtil.nonNullValue(existItem.getTakeoutAmount()).add(BigDecimalUtil.nonNullValue(s.getTakeoutAmount())).setScale(2, RoundingMode.HALF_UP));

            existItem.setQuantum(existItem.getDinnerQuantum().add(existItem.getTakeoutQuantum()));
            existItem.setAmount(existItem.getDinnerAmount().add(existItem.getTakeoutAmount()));
            existItem.setDiscountAmount(existItem.getDinnerDiscountAmount().add(existItem.getTakeoutDiscountAmount()));

        });
    }

    @Override
    public PropStatsDTO attr(DailyReqDTO request) {
        //正餐统计
        List<AttrItemRespDTO> dinner = dinnerDailyClientService.attr(request);
        //合并结果
        List<AttrItemRespDTO> merge = new ArrayList<>();
        if (dinner != null) {
            merge.addAll(dinner);
        }
        Collection<AttrItemRespDTO> resCollection = merge.stream().collect(Collectors.toMap(o -> o.getAttrGroupName() + o.getGuid() + o.getName(), record -> record, (o1, o2) -> {
            AttrItemRespDTO total = new AttrItemRespDTO();
            total.setAttrGroupName(o1.getAttrGroupName());
            total.setGuid(o1.getGuid());
            total.setName(o1.getName());
            total.setQuantum((o1.getQuantum() == null ? BigDecimal.ZERO : o1.getQuantum()).add(o2.getQuantum() == null ? BigDecimal.ZERO : o2.getQuantum()));
            total.setAmount((o1.getAmount() == null ? BigDecimal.ZERO : o1.getAmount()).add(o2.getAmount() == null ? BigDecimal.ZERO : o2.getAmount()));
            return total;
        })).values();


        //合并名称相同的属性组
        List<MaxPricePropGroup> resList = new ArrayList<>();
        resCollection.stream().collect(
                Collectors.toMap(o -> o.getAttrGroupName(),
                        record -> {
                            List<AttrItemRespDTO> temp = new ArrayList();
                            temp.add(record);
                            return temp;
                        },
                        (List<AttrItemRespDTO> oldList, List<AttrItemRespDTO> newList) -> {
                            oldList.addAll(newList);
                            return oldList;
                        }
                )
        ).forEach((key, value) -> {
            AttrItemRespDTO attrItem = value.get(0);
            MaxPricePropGroup group = new MaxPricePropGroup();
            group.setName(attrItem.getAttrGroupName());
            group.setGroupQuantity(value.stream().map(item -> {
                return item.getQuantum() == null ? BigDecimal.ZERO : item.getQuantum();
            }).reduce(BigDecimal.ZERO, BigDecimal::add).longValue());
            group.setGroupMoney(value.stream().map(item -> {
                return item.getAmount() == null ? BigDecimal.ZERO : item.getAmount();
            }).reduce(BigDecimal.ZERO, BigDecimal::add));
            List<PropStatsDTO.PropItem> propList = new ArrayList<>();
            value.stream().map(e -> Optional.ofNullable(e.getAttrs()).orElse(Collections.emptyList())).collect(ArrayList<AttrItemRespDTO>::new, (a, b) -> a.addAll(b), (a, b) -> a.addAll(b))
                    .stream().collect(Collectors.groupingBy((ItemRespDTO::getName)))
                    .forEach((e, a) -> {
                        PropStatsDTO.PropItem item = new PropStatsDTO.PropItem();
                        propList.add(item);
                        BigDecimal quantum = a.get(0).getQuantum();
                        BigDecimal amount = a.get(0).getAmount();
                        if (a.size() > 1) {
                            List<PropStatsDTO.PropItem> leafs = new ArrayList<>();
                            quantum = a.stream().peek((attr) -> {
                                PropStatsDTO.PropItem leaf = new PropStatsDTO.PropItem();
                                group.maxPrice = group.maxPrice.max(attr.getUnitPrice());
                                leaf.setName("");
                                leaf.setQuantity(attr.getQuantum().longValue());
                                leaf.setMoney(attr.getAmount());
                                leaf.setUnitPrice(attr.getUnitPrice());
                                leafs.add(leaf);
                            }).map(ItemRespDTO::getQuantum).reduce(BigDecimal.ZERO, BigDecimal::add);
                            amount = a.stream().map(ItemRespDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                            item.setPropList(leafs);
                        } else {
                            item.setUnitPrice(a.get(0).getUnitPrice());
                            group.maxPrice = group.maxPrice.max(a.get(0).getUnitPrice());
                        }
                        item.setName(e);
                        item.setQuantity(quantum.longValue());
                        item.setMoney(amount);

                    });
            group.setPropList(propList);
            resList.add(group);
        });
        Collections.sort(resList, (o1, o2) -> (
                request.getOrderType() != null && request.getOrderType() == 1) ?
                Optional.ofNullable(o1.maxPrice).orElse(BigDecimal.ZERO).compareTo(Optional.ofNullable(o2.maxPrice).orElse(BigDecimal.ZERO)) :
                Optional.ofNullable(o2.maxPrice).orElse(BigDecimal.ZERO).compareTo(Optional.ofNullable(o1.maxPrice).orElse(BigDecimal.ZERO))
        );
        //排序
        if (request.getOrderItem() != null) {
            if (request.getOrderItem() == 1) {
                //单价排序
                Collections.sort(resList, new Comparator<PropStatsDTO.PropGroup>() {
                    @Override
                    public int compare(PropStatsDTO.PropGroup o1, PropStatsDTO.PropGroup o2) {
                        return 0;
                    }
                });
            } else if (request.getOrderItem() == 2) {
                //数量排序
                Collections.sort(resList, new Comparator<PropStatsDTO.PropGroup>() {
                    @Override
                    public int compare(PropStatsDTO.PropGroup o1, PropStatsDTO.PropGroup o2) {
                        if (request.getOrderType() != null && request.getOrderType() == 2) {
                            if (o1.getGroupQuantity() > o2.getGroupQuantity()) {
                                return 1;
                            }
                            if (o1.getGroupQuantity() < o2.getGroupQuantity()) {
                                return -1;
                            }
                            if (o1.getGroupQuantity() == o2.getGroupQuantity()) {
                                return 0;
                            }
                        } else {
                            if (o1.getGroupQuantity() > o2.getGroupQuantity()) {
                                return -1;
                            }
                            if (o1.getGroupQuantity() < o2.getGroupQuantity()) {
                                return 1;
                            }
                            if (o1.getGroupQuantity() == o2.getGroupQuantity()) {
                                return 0;
                            }
                        }
                        return 0;
                    }
                });
            } else if (request.getOrderItem() == 3) {
                //金额排序
                Collections.sort(resList, new Comparator<PropStatsDTO.PropGroup>() {
                    @Override
                    public int compare(PropStatsDTO.PropGroup o1, PropStatsDTO.PropGroup o2) {
                        return (request.getOrderType() != null && request.getOrderType() == 2) ? (o2.getGroupMoney().compareTo(o1.getGroupMoney())) : (o1.getGroupMoney().compareTo(o2.getGroupMoney()));
                    }
                });
            }
        }

        //组装返回值
        PropStatsDTO response = new PropStatsDTO();
        //属性项
        response.setPropGroupList(new ArrayList<>(resList));
        //金额合计
        response.setTotalMoney(resList.stream().map(item -> {
            return item.getGroupMoney() == null ? BigDecimal.ZERO : item.getGroupMoney();
        }).reduce(BigDecimal.ZERO, BigDecimal::add));
        //数量合计
        response.setTotalQuantity(resList.stream().mapToLong(PropStatsDTO.PropGroup::getGroupQuantity).sum());
        return response;
    }

    @Override
    @DataAuthMethodControl
    public PropStatsSaleDTO attrSale(DailyReqDTO request) {
        PropStatsDTO attrResp = attr(request);
        return BusinessDailyTransform.INSTANCE.propStatsDTO2PropStatsSaleDTO(attrResp);
    }

    static class MaxPricePropGroup extends PropStatsDTO.PropGroup {
        private BigDecimal maxPrice = BigDecimal.ZERO;
    }

    @Override
    public List<ItemRespDTO> returnVegetables(DailyReqDTO request) {
        //正餐统计
        List<ItemRespDTO> dinner = dinnerDailyClientService.returnVegetables(request);
        //获取结果集
        return dishesHandle(dinner, null, request.getOrderItem(), request.getOrderType());
    }

    @Override
    @DataAuthMethodControl
    public List<ReturnSaleDTO> returnVegetablesSale(DailyReqDTO request) {
        List<ItemRespDTO> itemRespList = returnVegetables(request);
        return BusinessDailyTransform.INSTANCE.itemRespDTO2ReturnSaleDTO(itemRespList);
    }

    @Override
    public List<ItemRespDTO> dishGiving(DailyReqDTO request) {
        //正餐统计
        List<ItemRespDTO> dinner = dinnerDailyClientService.dishGiving(request);
        //获取结果集
        return dishesHandle(dinner, null, request.getOrderItem(), request.getOrderType());
    }

    @Override
    @DataAuthMethodControl
    public List<GiftSaleDTO> dishGivingSale(DailyReqDTO request) {
        List<ItemRespDTO> itemRespList = dishGiving(request);
        return BusinessDailyTransform.INSTANCE.itemRespDTO2GiftSaleDTO(itemRespList);
    }

    @Override
    public MemberConsumeRespDTO memberConsume(DailyReqDTO request) {
        //正餐统计（只包含会员正餐消费单数和实际消费总额）
        MemberConsumeRespDTO dinner = dinnerDailyClientService.memberConsume(request);
       /* if (dinner.getConsumerCount() == null) {
            dinner.setConsumerCount(0);
        }
        if (dinner.getConsumerAmount() == null) {
            dinner.setConsumerAmount(BigDecimal.ZERO);
        }
        if (dinner.getPrepaidCount() == null) {
            dinner.setPrepaidCount(0);
        }
        if (dinner.getPrepaidAmount() == null) {
            dinner.setPrepaidAmount(BigDecimal.ZERO);
        }
        if (dinner.getPrepaidGiveAmount() == null) {
            dinner.setPrepaidGiveAmount(BigDecimal.ZERO);
        }
        if (dinner.getPrepaidTakeInAmount() == null) {
            dinner.setPrepaidTakeInAmount(BigDecimal.ZERO);
        }*/
        return dinner;
    }

    @Override
    public List<GatherRespDTO> gather(DailyReqDTO request) {
        datePlusTime(request);
        // 查询隐藏第三方数据的权限
        List<String> reportHideNames = queryAuthorityReportHideNames();
        //正餐销售统计
        List<GatherRespDTO> dinnerList = innerGather(request, reportHideNames);
        log.info("营业日报 销售统计 返回 result = {}", JacksonUtils.writeValueAsString(dinnerList));
        //预订统计
        List<GatherRespDTO> reserveList = reserveClient.gather(request);
        log.info("营业日报 预订统计 返回 result = {}", JacksonUtils.writeValueAsString(reserveList));
        //外卖统计
        List<GatherRespDTO> takeoutList = innerTakeawayGather(request, reportHideNames);
        log.info("营业日报 外卖统计 返回 result = {}", JacksonUtils.writeValueAsString(takeoutList));

        //合并结果
        List<GatherRespDTO> merge = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dinnerList)) {
            merge.addAll(dinnerList);
        }
        if (!CollectionUtils.isEmpty(reserveList)) {
            merge.addAll(reserveList);
        }
        merge.addAll(takeoutList);
        Collection<GatherRespDTO> resCollection = merge.stream().collect(Collectors.toMap(GatherRespDTO::getGatherName, record -> record, (o1, o2) -> {
            GatherRespDTO total = new GatherRespDTO();
            total.setGatherName(o1.getGatherName());
            total.setGatherCode(o1.getGatherCode());
            total.setConsumerAmount((o1.getConsumerAmount() == null ? BigDecimal.ZERO : o1.getConsumerAmount()).add(o2.getConsumerAmount() == null ? BigDecimal.ZERO : o2.getConsumerAmount()));
            total.setPrepaidAmount((o1.getPrepaidAmount() == null ? BigDecimal.ZERO : o1.getPrepaidAmount()).add(o2.getPrepaidAmount() == null ? BigDecimal.ZERO : o2.getPrepaidAmount()));
            total.setReserveAmount((o1.getReserveAmount() == null ? BigDecimal.ZERO : o1.getReserveAmount()).add(o2.getReserveAmount() == null ? BigDecimal.ZERO : o2.getReserveAmount()));
            total.setTotalAmount((o1.getTotalAmount() == null ? BigDecimal.ZERO : o1.getTotalAmount()).add(o2.getTotalAmount() == null ? BigDecimal.ZERO : o2.getTotalAmount()));
            if (CollectionUtils.isNotEmpty(o1.getInnerDetails()) || CollectionUtils.isNotEmpty(o2.getInnerDetails())) {
                List<GatherRespDTO.InnerDetails> innerDetailsList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(o1.getInnerDetails())) {
                    innerDetailsList.addAll(o1.getInnerDetails());
                }
                if (CollectionUtils.isNotEmpty(o2.getInnerDetails())) {
                    innerDetailsList.addAll(o2.getInnerDetails());
                }
                total.setInnerDetails(innerDetailsList);
            }
            return total;
        })).values();

        //排序
        List<GatherRespDTO> resList = new ArrayList<>(resCollection);
        //默认排序
        resList.sort(Comparator.comparing(GatherRespDTO::getGatherCode));
        if (request.getOrderItem() != null) {
            if (request.getOrderItem() == 1) {
                //销售收入
                Collections.sort(resList, new Comparator<GatherRespDTO>() {
                    @Override
                    public int compare(GatherRespDTO o1, GatherRespDTO o2) {
                        BigDecimal o2B = o2.getConsumerAmount() == null ? BigDecimal.ZERO : o2.getConsumerAmount();
                        BigDecimal o1B = o1.getConsumerAmount() == null ? BigDecimal.ZERO : o1.getConsumerAmount();
                        return (request.getOrderType() != null && request.getOrderType() == 2) ? (o2B.compareTo(o1B)) : (o1B.compareTo(o2B));
                    }
                });
            } else if (request.getOrderItem() == 2) {
                //充值收入
                Collections.sort(resList, new Comparator<GatherRespDTO>() {
                    @Override
                    public int compare(GatherRespDTO o1, GatherRespDTO o2) {
                        BigDecimal o2B = o2.getPrepaidAmount() == null ? BigDecimal.ZERO : o2.getPrepaidAmount();
                        BigDecimal o1B = o1.getPrepaidAmount() == null ? BigDecimal.ZERO : o1.getPrepaidAmount();
                        return (request.getOrderType() != null && request.getOrderType() == 2) ? (o2B.compareTo(o1B)) : (o1B.compareTo(o2B));
                    }
                });
            } else if (request.getOrderItem() == 4) {
                //预订收入
                Collections.sort(resList, new Comparator<GatherRespDTO>() {
                    @Override
                    public int compare(GatherRespDTO o1, GatherRespDTO o2) {
                        BigDecimal o2B = o2.getReserveAmount() == null ? BigDecimal.ZERO : o2.getReserveAmount();
                        BigDecimal o1B = o1.getReserveAmount() == null ? BigDecimal.ZERO : o1.getReserveAmount();
                        return (request.getOrderType() != null && request.getOrderType() == 2) ? (o2B.compareTo(o1B)) : (o1B.compareTo(o2B));
                    }
                });
            } else if (request.getOrderItem() == 3) {
                //合计
                Collections.sort(resList, new Comparator<GatherRespDTO>() {
                    @Override
                    public int compare(GatherRespDTO o1, GatherRespDTO o2) {
                        BigDecimal o2B = o2.getTotalAmount() == null ? BigDecimal.ZERO : o2.getTotalAmount();
                        BigDecimal o1B = o1.getTotalAmount() == null ? BigDecimal.ZERO : o1.getTotalAmount();
                        return (request.getOrderType() != null && request.getOrderType() == 2) ? (o2B.compareTo(o1B)) : (o1B.compareTo(o2B));
                    }
                });
            }
        }

        //计算合计
        GatherRespDTO total = new GatherRespDTO();
        total.setGatherName(LocaleUtil.getMessage("PRICE_TOTAL"));
        total.setIsTotal(1);
        total.setConsumerAmount(resList.stream().map(item ->
                item.getConsumerAmount() == null ? BigDecimal.ZERO : item.getConsumerAmount()
        ).reduce(BigDecimal.ZERO, BigDecimal::add));
        total.setPrepaidAmount(resList.stream().map(item ->
                item.getPrepaidAmount() == null ? BigDecimal.ZERO : item.getPrepaidAmount()
        ).reduce(BigDecimal.ZERO, BigDecimal::add));
        total.setReserveAmount(resList.stream().map(item ->
                item.getReserveAmount() == null ? BigDecimal.ZERO : item.getReserveAmount()
        ).reduce(BigDecimal.ZERO, BigDecimal::add));
        total.setTotalAmount(resList.stream().map(item ->
                item.getTotalAmount() == null ? BigDecimal.ZERO : item.getTotalAmount()
        ).reduce(BigDecimal.ZERO, BigDecimal::add));
        resList.add(total);
        resList.removeIf(r -> BigDecimalUtil.equelZero(r.getTotalAmount()));
        return resList;
    }


    private List<GatherRespDTO> innerGather(DailyReqDTO request, List<String> reportHideNames) {
        List<GatherRespDTO> dinnerList = dinnerDailyClientService.gather(request);
        log.info("dinnerList={}", JacksonUtils.writeValueAsString(dinnerList));
        if (CollectionUtils.isEmpty(reportHideNames)) {
            return dinnerList;
        }
        // 预付金支付方式隐藏
        dinnerList.removeIf(e -> Objects.equals(PaymentTypeEnum.RESERVE.getCode(), e.getGatherCode()));
        // 权限控制
        dinnerList.removeIf(e -> reportHideNames.contains(e.getGatherName()));
        return dinnerList;
    }

    private List<GatherRespDTO> innerTakeawayGather(DailyReqDTO request, List<String> reportHideNames) {
        TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid(request.getStoreGuid());
        takeoutStatsQueryDTO.setBeginTime(DateTimeUtils.string2LocalDateTime(request.getBeginTime()));
        takeoutStatsQueryDTO.setEndTime(DateTimeUtils.string2LocalDateTime(request.getEndTime()));
        TakeoutStatsDTO takeout = takeoutDailyClientService.getReceiptStats(takeoutStatsQueryDTO);
        log.info("takeoutStatsDTO :{}", JacksonUtils.writeValueAsString(takeout));

        Map<String, BigDecimal> salesIncomingDetail = takeout.getSalesIncomingDetail();
        if (CollectionUtils.isNotEmpty(reportHideNames)) {
            for (String hideName : reportHideNames) {
                salesIncomingDetail.remove(hideName);
            }
        }
        List<GatherRespDTO> takeoutList = new ArrayList<>();
        for (Map.Entry<String, BigDecimal> entry : salesIncomingDetail.entrySet()) {
            GatherRespDTO item = new GatherRespDTO();
            item.setGatherCode(99); //根据code排序，外卖放在最后
            item.setGatherName(entry.getKey());
            item.setConsumerAmount(entry.getValue());
            item.setPrepaidAmount(BigDecimal.ZERO);
            item.setTotalAmount(entry.getValue());
            takeoutList.add(item);
        }
        return takeoutList;
    }


    @Override
    @DataAuthMethodControl
    public List<GatherSaleDTO> gatherSale(DailyReqDTO request) {
        List<GatherRespDTO> gatherRespList = gather(request);
        return BusinessDailyTransform.INSTANCE.gatherRespDTOList2GatherSaleDTO(gatherRespList);
    }

    @Override
    public OverviewRespDTO overview(DailyReqDTO request) {
        datePlusTime(request);
        // 查询隐藏第三方数据的权限
        List<String> reportHideNames = queryAuthorityReportHideNames();
        //正餐概况
        OverviewRespDTO dinner = innerOverview(request, reportHideNames);
        log.info("filter after dinner={}", JacksonUtils.writeValueAsString(dinner));
        //外卖
        TakeoutStatsDTO takeoutStatsDTO = innerTakeawayGetOpStats(request, reportHideNames);
        log.info("filter after takeoutStats={}", JacksonUtils.writeValueAsString(takeoutStatsDTO));

        //合并结果
        OverviewRespDTO res = new OverviewRespDTO();
        res.setCostAmount(dinner.getCostAmount().add(takeoutStatsDTO.getCostAmount()));
        res.setCheckoutStaffs(dinner.getCheckoutStaffs());
        //订单数
        res.setOrderCount(dinner.getOrderCount() + takeoutStatsDTO.getOrderCount());
        //客人数
        res.setGuestCount(dinner.getGuestCount() == null ? 0 : dinner.getGuestCount());
        // 正餐客流量，上座率，开台率，翻台率，平均用餐时长
        res.setDineInGuestCount(dinner.getDineInGuestCount());
        res.setOccupancyRatePercent(dinner.getOccupancyRatePercent());
        res.setOpenTableRatePercent(dinner.getOpenTableRatePercent());
        res.setFlipTableRatePercent(dinner.getFlipTableRatePercent());
        res.setAvgDineInTime(dinner.getAvgDineInTime());
        res.setRechargeMoney(dinner.getRechargeMoney());

        BigDecimal sumConsumerAmount = null == dinner.getConsumerAmount() ? BigDecimal.ZERO : dinner.getConsumerAmount();
        if (Optional.of(takeoutStatsDTO).map(TakeoutStatsDTO::getSalesIncomingDetail).isPresent()) {
            Set<String> set = takeoutStatsDTO.getSalesIncomingDetail().keySet();
            for (String s : set) {
                sumConsumerAmount = sumConsumerAmount.add(takeoutStatsDTO.getSalesIncomingDetail().get(s));
            }
        }

        BigDecimal sumGatherAmount = null == dinner.getGatherAmount() ? BigDecimal.ZERO : dinner.getGatherAmount();
        if (Optional.of(takeoutStatsDTO).map(TakeoutStatsDTO::getSalesIncomingDetail).isPresent()) {
            Set<String> set = takeoutStatsDTO.getSalesIncomingDetail().keySet();
            for (String s : set) {
                sumGatherAmount = sumGatherAmount.add(takeoutStatsDTO.getSalesIncomingDetail().get(s));
            }
        }

        res.setConsumerAmount(sumConsumerAmount);
        res.setGatherAmount(sumGatherAmount);
        //优惠项
        List<AmountItemDTO> discount = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dinner.getDiscountItems())) {
            discount.addAll(dinner.getDiscountItems());
        }
        if (takeoutStatsDTO.getDiscountAmountDetail() != null) {
            for (String key : takeoutStatsDTO.getDiscountAmountDetail().keySet()) {
                AmountItemDTO item = new AmountItemDTO();
                item.setName(key);
                item.setAmount(takeoutStatsDTO.getDiscountAmountDetail().get(key));
                item.setOrderCount(takeoutStatsDTO.getDiscountOrderCountDetail().get(key));
                item.setCode(99);   //根据code排序，外卖放在最后
                discount.add(item);
            }
        }
        Collection<AmountItemDTO> discountCollection = discount.stream().collect(Collectors.toMap(AmountItemDTO::getName, record -> record, (o1, o2) -> {
            AmountItemDTO total = new AmountItemDTO();
            total.setName(o1.getName());
            total.setAmount((o1.getAmount() == null ? BigDecimal.ZERO : o1.getAmount()).add(o2.getAmount() == null ? BigDecimal.ZERO : o2.getAmount()));
            return total;
        })).values();
        res.setDiscountItems(new ArrayList<>(discountCollection));
        //优惠总额
        res.setDiscountAmount(discountCollection.stream().map(item ->
                item.getAmount() == null ? BigDecimal.ZERO : item.getAmount()
        ).reduce(BigDecimal.ZERO, BigDecimal::add));
        //销售支付明细
        List<AmountItemDTO> sales = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dinner.getGatherItems())) {
            sales.addAll(dinner.getGatherItems());
        }
        if (Optional.of(takeoutStatsDTO).map(TakeoutStatsDTO::getSalesIncomingDetail).isPresent()) {
            for (String s : takeoutStatsDTO.getSalesIncomingDetail().keySet()) {
                AmountItemDTO item = new AmountItemDTO();
                item.setName(s);
                item.setAmount(takeoutStatsDTO.getSalesIncomingDetail().get(s));
                item.setOrderCount(takeoutStatsDTO.getOrderCountDetail().get(s));
                item.setCode(99);   //根据code排序，外卖放在最后
                sales.add(item);
            }
        }
        Collection<AmountItemDTO> saleCollection = sales.stream().collect(Collectors.toMap(AmountItemDTO::getName, record -> record, (o1, o2) -> {
            AmountItemDTO total = new AmountItemDTO();
            total.setCode(o1.getCode());
            total.setName(o1.getName());
            total.setAmount((o1.getAmount() == null ? BigDecimal.ZERO : o1.getAmount()).add(o2.getAmount() == null ? BigDecimal.ZERO : o2.getAmount()));
            total.setExcessAmount(o1.getExcessAmount());
            return total;
        })).values();
        res.setGatherItems(new ArrayList<>(saleCollection));
        //支付明细和优惠明细根据code排序
        res.getGatherItems().sort(Comparator.comparing(AmountItemDTO::getCode));
        res.getDiscountItems().sort(Comparator.comparing(AmountItemDTO::getCode));
        // 退款总额
        BigDecimal refundAmount = Optional.ofNullable(dinner.getRefundAmount()).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(takeoutStatsDTO.getRefundAmount()).orElse(BigDecimal.ZERO));
        res.setRefundAmount(refundAmount);
        // 最后处理下销售额 = 销售收入 + 优惠金额 + 退款总额
        res.setConsumerAmount(res.getGatherAmount().add(res.getDiscountAmount()).add(res.getRefundAmount()));
        //预计应得金额计算
        calculateEstimatedAmount(dinner.getMtGrouponEstimatedAmount(), takeoutStatsDTO.getShopAmountDetail(), res);
        return res;
    }

    /**
     * 查询一体机报表权限
     * 隐藏第三方数据的code
     */
    private List<Integer> queryAuthorityReportHideTakeawaySubTypes() {
        List<String> reportHideNames = queryAuthorityReportHideNames();
        if (CollectionUtils.isEmpty(reportHideNames)) {
            return Lists.newArrayList();
        }
        return reportHideNames.stream().map(e -> {
                    OrderType.TakeoutSubType takeoutSubType = OrderType.TakeoutSubType.ofDesc(e);
                    if (Objects.isNull(takeoutSubType)) {
                        return null;
                    }
                    return takeoutSubType.getType();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    /**
     * 查询一体机报表权限
     * 隐藏第三方数据的code
     */
    private List<Integer> queryAuthorityReportHideMemberPayWay() {
        List<String> reportHideNames = queryAuthorityReportHideNames();
        if (CollectionUtils.isEmpty(reportHideNames)) {
            return Lists.newArrayList();
        }
        List<Integer> paymentTypeIds = reportHideNames.stream()
                .map(PaymentTypeEnum.PaymentType::getIdByName)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return paymentTypeIds.stream()
                .map(this::mapping)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private Integer mapping(Integer payWay) {
        if (payWay == PaymentTypeEnum.OTHER.getCode()) {
            return 5;
        }
        if (payWay == PaymentTypeEnum.DEBT_PAY.getCode()) {
            return 8;
        }
        if (payWay == PaymentTypeEnum.CANTEEN_ELECTRONIC_CARD.getCode() || payWay == PaymentTypeEnum.CANTEEN_PHYSICAL_CARD.getCode()) {
            return payWay;
        }
        if (payWay == PaymentTypeEnum.THIRD_ACTIVITY.getCode()) {
            return payWay;
        }
        return payWay - 1;
    }

    /**
     * 查询一体机报表权限
     * 隐藏第三方数据的code
     */
    private List<String> queryAuthorityReportHideNames() {
        String storeGuid = UserContextUtils.getStoreGuid();
        if (StringUtils.isEmpty(storeGuid)) {
            log.info("门店guid为空，忽略查询权限");
            return Lists.newArrayList();
        }
        // 查询权限code
        List<MenuSourceDTO> sourceList = userClientService.getSourceByUser(String.valueOf(TerminalTypeEnum.TERMINAL_AIO.getCode()));
        if (CollectionUtils.isEmpty(sourceList)) {
            return Lists.newArrayList();
        }
        List<String> sourceCodeList = sourceList.stream()
                .map(MenuSourceDTO::getSourceCode)
                .distinct()
                .collect(Collectors.toList());
        List<String> namesByCodes = AuthorityReportHideEnum.getNamesByCodes(sourceCodeList);
        log.info("需要隐藏的第三方数据:{}", JacksonUtils.writeValueAsString(namesByCodes));
        return namesByCodes;
    }

    private OverviewRespDTO innerOverview(DailyReqDTO request, List<String> reportHideNames) {
        OverviewRespDTO dinner = dinnerDailyClientService.overview(request);
        log.info("dinner={}", JacksonUtils.writeValueAsString(dinner));
        if (CollectionUtils.isEmpty(reportHideNames)) {
            return dinner;
        }
        // 销售总净额
        List<AmountItemDTO> gatherItems = dinner.getGatherItems();
        if (CollectionUtils.isNotEmpty(gatherItems)) {
            List<AmountItemDTO> hideAmountItemDTOList = gatherItems.stream()
                    .filter(e -> reportHideNames.contains(e.getName()))
                    .collect(Collectors.toList());
            // 金额
            BigDecimal hideTotalAmount = hideAmountItemDTOList.stream()
                    .map(AmountItemDTO::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 商家预计应得金额
            BigDecimal hideTotalEstimatedAmount = hideAmountItemDTOList.stream()
                    .map(AmountItemDTO::getEstimatedAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            dinner.setGatherAmount(Optional.ofNullable(dinner.getGatherAmount())
                    .orElse(BigDecimal.ZERO).subtract(hideTotalAmount));
            dinner.setEstimatedAmount(Optional.ofNullable(dinner.getEstimatedAmount())
                    .orElse(BigDecimal.ZERO).subtract(hideTotalEstimatedAmount));
            // 删除隐藏数据
            gatherItems.removeIf(e -> reportHideNames.contains(e.getName()));
        }
        // 优惠总额
        List<AmountItemDTO> discountItems = dinner.getDiscountItems();
        if (CollectionUtils.isNotEmpty(discountItems)) {
            List<AmountItemDTO> hideAmountItemDTOList = discountItems.stream()
                    .filter(e -> reportHideNames.contains(e.getName().replace("优惠", "")))
                    .collect(Collectors.toList());
            // 金额
            BigDecimal hideTotalAmount = hideAmountItemDTOList.stream()
                    .map(AmountItemDTO::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            dinner.setDiscountAmount(Optional.ofNullable(dinner.getDiscountAmount())
                    .orElse(BigDecimal.ZERO).subtract(hideTotalAmount));
            // 删除隐藏数据
            discountItems.removeIf(e -> reportHideNames.contains(e.getName().replace("优惠", "")));
        }
        return dinner;
    }

    private TakeoutStatsDTO innerTakeawayGetOpStats(DailyReqDTO request, List<String> reportHideNames) {
        TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid(request.getStoreGuid());
        takeoutStatsQueryDTO.setBeginTime(DateTimeUtils.string2LocalDateTime(request.getBeginTime()));
        takeoutStatsQueryDTO.setEndTime(DateTimeUtils.string2LocalDateTime(request.getEndTime()));
        takeoutStatsQueryDTO.setStoreGuids(request.getStoreGuids());
        takeoutStatsQueryDTO.setStaffGuids(request.getCheckoutStaffGuids());
        TakeoutStatsDTO takeoutStatsDTO = takeoutDailyClientService.getOpStats(takeoutStatsQueryDTO);
        // 删除外卖优惠中为0的
        Map<String, BigDecimal> discountAmountDetail = takeoutStatsDTO.getDiscountAmountDetail();
        Map<String, Integer> discountOrderCountDetail = takeoutStatsDTO.getDiscountOrderCountDetail();
        Set<String> discountName = Sets.newHashSet(discountAmountDetail.keySet());
        for (String name : discountName) {
            if (discountAmountDetail.get(name).compareTo(BigDecimal.ZERO) == 0) {
                discountAmountDetail.remove(name);
                discountOrderCountDetail.remove(name);
            }
        }
        log.info("takeoutStatsDTO :{}", JacksonUtils.writeValueAsString(takeoutStatsDTO));
        if (CollectionUtils.isEmpty(reportHideNames)) {
            return takeoutStatsDTO;
        }
        // 销售
        Map<String, BigDecimal> salesIncomingDetail = takeoutStatsDTO.getSalesIncomingDetail();
        Map<String, Integer> orderCountDetail = takeoutStatsDTO.getOrderCountDetail();
        if (MapUtils.isNotEmpty(salesIncomingDetail)) {
            for (String reportHideName : reportHideNames) {
                salesIncomingDetail.remove(reportHideName);
            }
        }
        // 销售订单数
        if (MapUtils.isNotEmpty(orderCountDetail)) {
            for (String reportHideName : reportHideNames) {
                orderCountDetail.remove(reportHideName);
            }
        }
        // 优惠项
        if (MapUtils.isNotEmpty(discountAmountDetail)) {
            for (String reportHideName : reportHideNames) {
                discountAmountDetail.remove(reportHideName + "优惠");
            }
        }
        if (MapUtils.isNotEmpty(discountOrderCountDetail)) {
            for (String reportHideName : reportHideNames) {
                discountOrderCountDetail.remove(reportHideName + "优惠");
            }
        }
        // 订单数
        Map<Integer, Long> orderCountMap = Optional.ofNullable(takeoutStatsDTO.getOrderCountMap())
                .orElse(Maps.newHashMap());
        for (String reportHideName : reportHideNames) {
            OrderType.TakeoutSubType takeoutSubType = OrderType.TakeoutSubType.ofDesc(reportHideName);
            if (Objects.nonNull(takeoutSubType)) {
                orderCountMap.remove(takeoutSubType.getType());
            }
        }
        Long orderCount = orderCountMap.values().stream()
                .mapToLong(Long::longValue)
                .sum();
        takeoutStatsDTO.setOrderCount(orderCount.intValue());
        return takeoutStatsDTO;
    }


    @Override
    @DataAuthMethodControl
    public OverviewSaleDTO overviewSale(DailyReqDTO request) {
        OverviewRespDTO overview = overview(request);
        return BusinessDailyTransform.INSTANCE.overview2OverviewSaleDTO(overview);
    }

    /**
     * 预计应得金额计算
     *
     * @param mtGrouponEstimatedAmount 美团团购商家金额
     * @param shopAmountDetail         外卖商家金额
     */
    private void calculateEstimatedAmount(BigDecimal mtGrouponEstimatedAmount, Map<String, BigDecimal> shopAmountDetail, OverviewRespDTO res) {
        BigDecimal estimatedAmount = BigDecimal.ZERO;
        for (AmountItemDTO amountItemDTO : res.getGatherItems()) {
            //外卖
            if (amountItemDTO.getCode() == 99) {
                amountItemDTO.setEstimatedAmount(shopAmountDetail.get(amountItemDTO.getName()));
            }
            //美团团购
            if (amountItemDTO.getCode() == 20) {
                amountItemDTO.setEstimatedAmount(mtGrouponEstimatedAmount);
            }
            if (Objects.isNull(amountItemDTO.getEstimatedAmount())) {
                amountItemDTO.setEstimatedAmount(amountItemDTO.getAmount());
            }
            estimatedAmount = estimatedAmount.add(amountItemDTO.getEstimatedAmount());
        }
        res.setEstimatedAmount(estimatedAmount);
        res.setGrossProfitAmount(estimatedAmount.subtract(res.getCostAmount()));
    }

    /**
     * 菜品结果集处理（合并、分组、排序）
     *
     * @param dinner 正餐
     * @param snack  快餐
     * @return
     */
    private List<ItemRespDTO> dishesHandle(List<ItemRespDTO> dinner, List<ItemRespDTO> snack, Integer orderItem, Integer orderType) {
        //合并结果
        List<ItemRespDTO> merge = new ArrayList<>();
        if (dinner != null) {
            merge.addAll(dinner);
        }
        if (snack != null) {
            merge.addAll(snack);
        }
        Collection<ItemRespDTO> resCollection = merge.stream()
                .collect(Collectors.
                        toMap(o -> o.getGuid() + "_" + o.getName(), record
                                -> record, (o1, o2) -> {
                            ItemRespDTO total = new ItemRespDTO();
                            total.setGuid(o1.getGuid());
                            total.setName(o1.getName());
                            total.setQuantum(BigDecimalUtil.nonNullValue(o1.getQuantum()).add(BigDecimalUtil.nonNullValue(o2.getQuantum())));
                            total.setAmount(BigDecimalUtil.nonNullValue(o1.getAmount()).add(BigDecimalUtil.nonNullValue(o2.getAmount())).setScale(2, RoundingMode.HALF_UP));


                            return total;
                        })).values();

        //合并id和名称相同的菜品
        List<ItemRespDTO> resList = new ArrayList<>();
        resCollection.stream().collect(
                Collectors.toMap(o -> o.getGuid() + "_" + o.getName(),
                        record -> {
                            List<ItemRespDTO> temp = new ArrayList();
                            record.setAmount(record.getAmount().setScale(2, RoundingMode.HALF_UP));
                            temp.add(record);
                            return temp;
                        },
                        (List<ItemRespDTO> oldList, List<ItemRespDTO> newList) -> {
                            oldList.addAll(newList);
                            return oldList;
                        }
                )).forEach((key, value) -> {
            if (value.size() == 1) {
                resList.addAll(value);
            } else {
                String[] keys = key.split("_");
                ItemRespDTO total = new ItemRespDTO();
                total.setGuid(keys[0]);
                total.setName(keys[1]);
                total.setQuantum(value.stream().map(item -> item.getQuantum() == null ? BigDecimal.ZERO : item.getQuantum()).reduce(BigDecimal.ZERO, BigDecimal::add));
                total.setAmount(value.stream().map(item -> item.getAmount() == null ? BigDecimal.ZERO : item.getAmount()).reduce(BigDecimal.ZERO, BigDecimal::add));
                total.setAmount(total.getAmount().setScale(2, RoundingMode.HALF_UP));
                total.setSubs(value);
                resList.add(total);
            }
        });
        List<ItemRespDTO> sameNameList = resList.stream().filter(a -> a.getHasSubInfo() == 1).collect(Collectors.toList());
        Map<String, List<ItemRespDTO>> listMap = resList.stream().collect(Collectors.groupingBy(ItemRespDTO::getName));
        sameNameList.forEach(a -> {
            List<ItemRespDTO> subItem = a.getSubs();
            Map<String, List<ItemRespDTO>> collect = subItem.stream().collect(Collectors.groupingBy(ItemRespDTO::getGuid));
            collect.forEach((k, v) -> {
                List<ItemRespDTO> tempList = v.stream().filter(o -> "".equals(o.getSkuName())).collect(Collectors.toList());
                Set<BigDecimal> tempUnitPrice = v.stream().map(ItemRespDTO::getUnitPrice).collect(Collectors.toSet());
                if (!tempList.isEmpty() && tempUnitPrice.size() == 1) {
                    BigDecimal totalQuantum = tempList.stream().map(ItemRespDTO::getQuantum).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal totalAmount = totalQuantum.multiply(tempList.get(0).getUnitPrice());
                    List<ItemRespDTO> temp = listMap.get(a.getName());
                    temp.get(0).setUnitPrice(tempList.get(0).getUnitPrice());
                    temp.get(0).setQuantum(totalQuantum);
                    temp.get(0).setAmount(totalAmount);
                    temp.get(0).setHasSubInfo(0);
                    temp.get(0).setSubs(null);
                }
            });
        });
        /*resList.clear();
        listMap.forEach((k, v) -> {
            //resList.add(v.get(0));
            v.forEach(o ->  resList.add(o));
        });*/
        //排序
        if (orderItem != null) {
            if (orderItem == 1) {
                //单价排序
                resList.sort((o1, o2) -> {
                    if (orderType != null && orderType == 2) {
                        if (o2.getUnitPrice() == null) {
                            return 1;
                        }
                        if (o1.getUnitPrice() == null) {
                            return -1;
                        }
                        return o2.getUnitPrice().compareTo(o1.getUnitPrice());
                    } else {
                        if (o2.getUnitPrice() == null) {
                            return -1;
                        }
                        if (o1.getUnitPrice() == null) {
                            return 1;
                        }
                        return o1.getUnitPrice().compareTo(o2.getUnitPrice());
                    }
                });
            } else if (orderItem == 2) {
                //数量排序
                resList.sort((o1, o2) -> (orderType != null && orderType == 2) ? (o2.getQuantum().compareTo(o1.getQuantum())) : (o1.getQuantum().compareTo(o2.getQuantum())));
            } else if (orderItem == 3) {
                //金额排序
                resList.sort((o1, o2) -> (orderType != null && orderType == 2) ? (o2.getAmount().compareTo(o1.getAmount())) : (o1.getAmount().compareTo(o2.getAmount())));
            }
        }

        //计算合计
        ItemRespDTO total = new ItemRespDTO();
        total.setName("合计");
        total.setIsTotal(1);
        total.setQuantum(resList.stream().map(item -> item.getQuantum() == null ? BigDecimal.ZERO : item.getQuantum()).reduce(BigDecimal.ZERO, BigDecimal::add));
        total.setAmount(resList.stream().map(item -> item.getAmount() == null ? BigDecimal.ZERO : item.getAmount()).reduce(BigDecimal.ZERO, BigDecimal::add));
        total.setAmount(total.getAmount().setScale(2, RoundingMode.HALF_UP));
        resList.add(total);
        return resList;
    }


    @Override
    public ResponseConsumpStatis memberConsumeDaily(DailyReqDTO reqDTO) {
        queryAuthorityReportHideNames();
        RequestMemberDaily memberDailyReqDTO = new RequestMemberDaily();
        memberDailyReqDTO.setStartDate(DateTimeUtils.string2LocalDateTime(reqDTO.getBeginTime()));
        memberDailyReqDTO.setEndDate(DateTimeUtils.string2LocalDateTime(reqDTO.getEndTime()));
        memberDailyReqDTO.setReportHidePayWays(queryAuthorityReportHideMemberPayWay());
        log.info("查询会员消费数据入参:{}", JacksonUtils.writeValueAsString(memberDailyReqDTO));
        ResponseConsumpStatis respDTO = dataClientService.queryOnlyConsumption(memberDailyReqDTO);
        List<ResponsePayWayDetail> payWayDetailList = respDTO.getPayWayDetailList();
        if (!CollectionUtils.isEmpty(payWayDetailList)) {
            // 特殊处理美团抖音团购到消费金额中
            reqDTO.setIsMember(BooleanEnum.TRUE.getCode());
            List<AmountItemDTO> amountList = dinnerDailyClientService.listByRequest(reqDTO);
            if (!CollectionUtils.isEmpty(amountList)) {
                Map<Integer, AmountItemDTO> amountMap = amountList.stream()
                        .collect(Collectors.toMap(AmountItemDTO::getCode, Function.identity(), (v1, v2) -> v1));
                payWayDetailList.forEach(payWay -> {
                    GroupAdapterEnum adapterEnum = GroupAdapterEnum.getEnumBySecond(payWay.getPayWay());
                    if (null != adapterEnum) {
                        AmountItemDTO amountItemDTO = amountMap.get(adapterEnum.getFirstCode());
                        if (Objects.nonNull(amountItemDTO)) {
                            payWay.setPayAmount(amountItemDTO.getAmount());
                        }
                    }
                });
            }

            payWayDetailList.sort(Comparator.comparing(ResponsePayWayDetail::getPayWay));
        }
        return respDTO;
    }

    @Override
    @DataAuthMethodControl
    public MemberConsumeSaleDTO memberConsumeSale(DailyReqDTO reqDTO) {
        ResponseConsumpStatis responseConsumpStatis = memberConsumeDaily(reqDTO);
        return BusinessDailyTransform.INSTANCE.responseConsumpStatis2MemberConsumeSaleDTO(responseConsumpStatis);
    }

    @Override
    public ResponseRechargeStatis memberRechargeDaily(DailyReqDTO reqDTO) {
        RequestMemberDaily memberDailyReqDTO = new RequestMemberDaily();
        memberDailyReqDTO.setStartDate(DateTimeUtils.string2LocalDateTime(reqDTO.getBeginTime()));
        memberDailyReqDTO.setEndDate(DateTimeUtils.string2LocalDateTime(reqDTO.getEndTime()));
        ResponseRechargeStatis respDTO = dataClientService.queryOnlyRecharge(memberDailyReqDTO);
        if (!CollectionUtils.isEmpty(respDTO.getPayWayDetailList())) {
            respDTO.getPayWayDetailList().sort(Comparator.comparing(ResponsePayWayDetail::getPayWay));
        }
        return respDTO;
    }

    @Override
    @DataAuthMethodControl
    public MemberRechargeSaleDTO memberRechargeSale(DailyReqDTO reqDTO) {
        ResponseRechargeStatis responseRechargeStatis = memberRechargeDaily(reqDTO);
        return BusinessDailyTransform.INSTANCE.responseRechargeStatis2MemberRechargeSaleDTO(responseRechargeStatis);
    }

    @Override
    public List<UserBriefDTO> users() {
        return userClientService.storeUsers(UserContextUtils.getStoreGuid());
    }

    @Override
    public RefundRespDTO refund(DailyReqDTO request) {
        datePlusTime(request);
        RefundRespDTO refundResult = dinnerDailyClientService.refund(request);
        List<RefundAmountDTO> refundList = refundResult.getRefundAmounts();
        //计算合计
        RefundAmountDTO total = new RefundAmountDTO();
        total.setIsTotal(1);
        total.setRefundName("合计");
        total.setRefundCode(RefundCodeEnum.REFUND_TYPE_TOTAL.getCode());
        total.setRefundOrderCount(refundList.stream().mapToInt(item -> {
            if (item != null && item.getRefundOrderCount() != null) {
                return item.getRefundOrderCount();
            } else {
                return 0;
            }
        }).sum());
        total.setRefundAmount(refundList.stream().map(item -> {
            if (item != null && item.getRefundAmount() != null) {
                return item.getRefundAmount();
            } else {
                return BigDecimal.ZERO;
            }
        }).reduce(BigDecimal.ZERO, BigDecimal::add));
        refundList.add(total);
        log.info("查询退款数据返回:{}", JacksonUtils.writeValueAsString(refundResult));
        return refundResult;
    }

    @Override
    @DataAuthMethodControl
    public RefundSaleDTO refundSale(DailyReqDTO request) {
        RefundRespDTO refundRespDTO = refund(request);
        return BusinessDailyTransform.INSTANCE.refundRespDTO2RefundSaleDTO(refundRespDTO);
    }

    /**
     * 日期参数加上时间
     * 一体机1.7.4前端传值改为时间，为了兼容老版本，根据长度判断是否需要加上时间
     * 当前端传的参数只有日期(yyyy-MM-dd,长度10)，则加上时间
     *
     * @param dailyReqDTO
     */
    private void datePlusTime(DailyReqDTO dailyReqDTO) {
        String beginTime = dailyReqDTO.getBeginTime();
        if (beginTime.length() == 10) {
            dailyReqDTO.setBeginTime(beginTime + " 00:00:00");
        }
        String endTime = dailyReqDTO.getEndTime();
        if (endTime.length() == 10) {
            dailyReqDTO.setEndTime(endTime + " 23:59:59");
        }
    }

}