package com.holderzone.holder.saas.aggregation.app.controller.trade;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.OperateRecordClientService;
import com.holderzone.saas.store.dto.trade.req.record.MemberOperateRecordReqDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/10/17
 * @description 会员操作记录接口
 */
@Slf4j
@RestController
@AllArgsConstructor
@Api(tags = "会员操作记录接口")
@RequestMapping("/member_operate_record")
public class MemberOperateRecordController {

    private final OperateRecordClientService operateRecordClientService;

    @ApiOperation(value = "保存记录")
    @PostMapping("/save_record")
    public Result<Void> saveRecord(@RequestBody MemberOperateRecordReqDTO reqDTO) {
        log.info("[会员操作记录]保存记录,reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        operateRecordClientService.saveRecord(reqDTO);
        return Result.buildEmptySuccess();
    }

}