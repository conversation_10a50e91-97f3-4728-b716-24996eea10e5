package com.holderzone.holder.saas.aggregation.app.controller.print;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.PrintClientService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterController
 * @date 2018/7/24 16:39
 * @description 打印机api
 * @program holder-saas-store-print
 */
@Api
@Slf4j
@RestController
@RequestMapping(value = "/printer", produces = {"application/json;charset=UTF-8"})
public class PrinterController {

    private final PrintClientService printClientService;

    @Autowired
    public PrinterController(PrintClientService printClientService) {
        this.printClientService = printClientService;
    }

    @ApiOperation(value = "添加打印机, 返回添加的打印机guid")
    @PostMapping("/add")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT, description = "添加打印机")
    public Result<String> addPrinter(@RequestBody PrinterDTO printerDTO) {
        log.info("添加打印机入参:{}", JacksonUtils.writeValueAsString(printerDTO));
        return Result.buildSuccessResult(printClientService.addPrinter(printerDTO));
    }

    @ApiOperation(value = "获取某台打印机信息, 包括与菜品/区域绑定关系")
    @PostMapping("/query")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT, description = "获取某台打印机信息")
    public Result<PrinterDTO> getPrinter(@RequestBody PrinterDTO printerDTO) {
        log.info("获取某台打印机信息, 包括与菜品/区域绑定关系入参:{}", JacksonUtils.writeValueAsString(printerDTO));
        return Result.buildSuccessResult(printClientService.getPrinter(printerDTO));
    }

    @ApiOperation(value = "通过门店guid获取该门店的打印机信息列表(不含菜品/区域绑定关系)")
    @PostMapping("/list_by_biz")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT, description = "通过门店guid获取该门店的打印机信息列表")
    public Result<List<PrinterDTO>> listPrintersByQuery(@RequestBody PrinterDTO printerDTO) {
        log.info("查询条件入参:{}", JacksonUtils.writeValueAsString(printerDTO));
        return Result.buildSuccessResult(printClientService.listPrintersByBiz(printerDTO));
    }

    @ApiOperation(value = "获取门店下所有的后厨打印机以及该T1设备添加的前台/usb打印机; 有效字段: deviceId, storeGuid")
    @PostMapping("/list_by_device")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT, description = "获取门店下所有的后厨打印机以及该T1设备添加的前台")
    public Result<List<PrinterDTO>> listPrinterByStoreGuid(@RequestBody PrinterDTO printerDTO) {
        log.info("获取门店下所有的后厨打印机以及该T1设备添加的前台/usb打印机入参:{}", JacksonUtils.writeValueAsString(printerDTO));
        return Result.buildSuccessResult(printClientService.listPrinterByDevice(printerDTO));
    }

    @ApiOperation(value = "修改打印机信息/与菜品的绑定/与区域绑定")
    @PostMapping("/update")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT, description = "修改打印机信息/与菜品的绑定/与区域绑定")
    public Result updatePrinter(@RequestBody PrinterDTO printerDTO) {
        log.info("修改打印机信息/与菜品的绑定/与区域绑定入参:{}", JacksonUtils.writeValueAsString(printerDTO));
        printClientService.updatePrinter(printerDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "删除打印机")
    @PostMapping("/delete")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT, description = "删除打印机")
    public Result deletePrinterByGuid(@RequestBody PrinterDTO printerDTO) {
        log.info("删除打印机入参:{}", JacksonUtils.writeValueAsString(printerDTO));
        printClientService.deletePrinter(printerDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/backups_printer")
    @ApiOperation(value = "一键备份打印机,参数传递 deviceId")
    public Result<Boolean> backupsPrinter(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("备份打印机入参:{}", JacksonUtils.writeValueAsString(singleDataDTO));
        return Result.buildSuccessResult(printClientService.backupsPrinter(singleDataDTO));
    }

    @PostMapping("/restore_printer")
    @ApiOperation(value = "一键还原打印机，参数传递 deviceId")
    public Result<Boolean> restorePrinter(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("还原打印机入参:{}", JacksonUtils.writeValueAsString(singleDataDTO));
        return Result.buildSuccessResult(printClientService.restorePrinter(singleDataDTO));
    }

    @PostMapping("/backups_printer_time")
    @ApiOperation(value = "最新备份时间")
    public Result<String> backupsPrinterTime(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("最新备份时间入参:{}", JacksonUtils.writeValueAsString(singleDataDTO));
        return Result.buildSuccessResult(printClientService.backupsPrinterTime(singleDataDTO));
    }

    /**
     * 自动打印设置
     * @param printerDTO 打印设置参数
     * @param status 状态
     */
    @PostMapping("/auto_print/{status}")
    public Result<Void> autoPrintSet(@PathVariable("status") Integer status, @RequestBody PrinterDTO printerDTO) {
        log.info("[自动打印设置]，{}，{}", status,JacksonUtils.writeValueAsString(printerDTO));
        printClientService.autoPrintSet(status,printerDTO);
        return Result.buildEmptySuccess();
    }

}
