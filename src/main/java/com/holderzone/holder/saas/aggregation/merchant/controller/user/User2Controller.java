package com.holderzone.holder.saas.aggregation.merchant.controller.user;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.organization.OrganizationService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.user.UserFeignService;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.dto.user.*;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.holderzone.holder.saas.aggregation.merchant.util.CurrentHttpRequestUtils.getRequestUri;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @className UserController
 * @date 18-9-17 上午9:22
 * @description 员工/权限相关接口
 * @program holder-saas-aggregation-merchant
 */
@RestController
@Deprecated
@RequestMapping("/deprecated")
@Api(description = "员工/权限相关服务接口")
public class User2Controller {

    private final UserFeignService userFeignService;

    private final OrganizationService organizationService;

    @Autowired
    public User2Controller(UserFeignService userFeignService, OrganizationService organizationService) {
        this.userFeignService = userFeignService;
        this.organizationService = organizationService;
    }

    @ApiOperation(value = "自动生成帐号", notes = "自动生成六位数字的帐号")
    @PostMapping(value = "/user/new_account")
    public Result newAccount() {
        return Result.buildSuccessResult(userFeignService.newAccount());
    }

    @ApiOperation(value = "自动生成授权码", notes = "自动生成六位数字的授权码")
    @PostMapping(value = "/user/new_auth_code")
    public Result newAuthCode() {
        return Result.buildSuccessResult(userFeignService.newAuthCode());
    }

    @ApiOperation(value = "新建员工", notes = "若传入的角色数组、门店数组不为空则建立用户与角色、门店的关联关系")
    @PostMapping(value = "/user/create")
    public Result create(@RequestBody UserDTO userDTO) {
        userFeignService.createUser(userDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "更新员工信息", notes = "更新员工信息")
    @PostMapping(value = "/user/update")
    public Result update(@RequestBody UserDTO userDTO) {
//        if (userDTO.getOrganizeDTOS() != null && !userDTO.getOrganizeDTOS().isEmpty()) {
//            userDTO.setStoreList(userDTO.getOrganizeDTOS().stream().map(OrganizeDTO::getOrganizationGuid)
//                    .collect(Collectors.toList()));
//        }
        userFeignService.updateUser(userDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "根据用户guid查询用户相关信息（传入userGuid则查询该userGuid对应的员工信息，否则查询当前登陆用户的员工信息）")
    @PostMapping(value = "/user/query")
    public Result<UserDTO> findUserById(@RequestBody(required = false) UserDTO userDTO) {
        if (userDTO == null) {
            userDTO = new UserDTO();
        }
        if (!StringUtils.hasText(userDTO.getGuid())) {
            userDTO.setGuid(UserContextUtils.getUserGuid());
        }
        return Result.buildSuccessResult(userFeignService.queryUser(userDTO));
//        String jsonStr = JacksonUtils.writeValueAsString(userDTO);
//        UserAllInfoDTO userAllInfoDTO = JacksonUtils.toObject(UserAllInfoDTO.class, jsonStr);
//        // 调用服务获得用户的门店信息
//        if (userAllInfoDTO.getStoreList() != null && !userAllInfoDTO.getStoreList().isEmpty()) {
//            OrganizationListQueryDTO queryDTO = new OrganizationListQueryDTO();
//            queryDTO.setOrganizeGuidList(userAllInfoDTO.getStoreList());
//            List<OrganizeDTO> listResult = organizationService.queryByList(queryDTO);
//            if (listResult != null && !listResult.isEmpty()) {
//                userAllInfoDTO.setOrganizeDTOS(listResult.stream().filter(j -> Objects.equals(UserContextUtils.getEnterpriseGuid(), j.getOrganizationGuid())
//                        || j.getOrganizationType() == 3).collect(Collectors.toList()));
//            }
//        }
//        return Result.buildSuccessResult(userAllInfoDTO);
    }

    @ApiOperation(value = "启用(禁用)员工")
    @PostMapping(value = "/user/enable")
    public Result enable(@RequestBody UserDTO userDTO) {
        userFeignService.enableOrDisableUser(userDTO);
        return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.OPERATION_SUCCESSFUL));
    }

    @ApiOperation(value = "删除员工", notes = "删除员工")
    @PostMapping(value = "/user/delete")
    public Result delete(@RequestBody UserDTO userDTO) {
        userFeignService.deleteUser(userDTO);
        return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.DELETION_SUCCESSFUL));
    }

    @ApiOperation(value = "分页查询员工信息（可查询指定userGuid所创建的员工）")
    @PostMapping(value = "/user/page_query")
    public Result<Page<UserDTO>> findUserByPage(@RequestBody UserQueryDTO userQueryDTO) {
        // 如果不是管理员则只能查看自己创建的员工信息，管理员能查看所有员工信息
        if (!"100000".equals(UserContextUtils.getUserAccount())) {
            userQueryDTO.setCreateStaffGuid(UserContextUtils.getUserGuid());
        }
        Page<UserDTO> userDTOS = userFeignService.pageQuery(userQueryDTO);
        return Result.buildSuccessResult(userDTOS);
        // 转换 UserDTO 为 UserAllInfoDTO
//        List<UserAllInfoDTO> userAllInfoDTOS = new ArrayList<>();
//        userDTOS.getData().forEach(p -> {
//            String jsonStr = JacksonUtils.writeValueAsString(p);
//            userAllInfoDTOS.add(JacksonUtils.toObject(UserAllInfoDTO.class, jsonStr));
//        });
//        // 设置UserAllInfoDTO中的门店List
//        userAllInfoDTOS.forEach(p -> {
//            if (p.getStoreList() != null && !p.getStoreList().isEmpty()) {
//                List<OrganizeDTO> organizeDTOList = new ArrayList<>();
//                p.getStoreList().forEach(k -> {
//                    if (StringUtils.hasText(k)) {
//                        OrganizeQueryAllDTO organizeQueryAllDTO = new OrganizeQueryAllDTO();
//                        organizeQueryAllDTO.setOrganizationGuid(k);
//                        List<OrganizeDTO> dtoList = organizationService.queryAll(organizeQueryAllDTO);
//                        if (dtoList != null && !dtoList.isEmpty()) {
//                            organizeDTOList.add(dtoList.get(0));
//                        }
//                    }
//                });
//                p.setOrganizeDTOS(organizeDTOList);
//            }
//        });
//        // 组装返回的Page对象
//        return Result.buildSuccessResult(new Page<>(userQuery.getPageIndex(), userQuery.getPageSize(), userDTOS.getTotalCount(), userAllInfoDTOS));
    }

    @ApiOperation(value = "修改密码（用户自行输入密码）")
    @PostMapping(value = "/user/update_pwd")
    public Result updatePwd(@RequestBody UpdatePwdDTO updatePwdDTO) {
        userFeignService.updatePwd(updatePwdDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "重置密码（生成随机密码并发送短信）")
    @PostMapping(value = "/user/reset_pwd")
    public Result resetPwd(@RequestBody(required = false) ResetPwdDTO resetPwdDTO) {
        userFeignService.resetPwd(resetPwdDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "新建角色（包括对角色授权）")
    @PostMapping(value = "/role/createRole")
    public Result createRole(@RequestBody GrantRoleDTO grantRoleDTO) {
        userFeignService.createRole(grantRoleDTO, getRequestUri());
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "删除角色")
    @PostMapping(value = "/role/deleteRole/{roleGuid}")
    public Result deleteRole(@ApiParam(value = "角色guid") @PathVariable("roleGuid") String roleGuid) {
        userFeignService.deleteRole(roleGuid);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "启用、禁用角色")
    @PostMapping(value = "/role/enableRole")
    public Result deleteRole(@RequestBody RoleDTO roleDTO) {
        userFeignService.enableRole(roleDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "修改角色信息（包括对角色授权）")
    @PostMapping(value = "/role/updateRole", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result updateRole(@RequestBody GrantRoleDTO grantRoleDTO) {
        userFeignService.updateRole(grantRoleDTO, getRequestUri());
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "根据用户Guid查询用户拥有的角色")
    @PostMapping(value = "/user/role/{userGuid}")
    public Result<List<RoleDTO>> findRoleByUserGuid(@ApiParam(value = "员工guid") @PathVariable("userGuid") String userGuid) {
        return Result.buildSuccessResult(userFeignService.findRoleByUserGuid(userGuid));
    }

    @ApiOperation(value = "根据角色guid查询角色权限")
    @PostMapping(value = "/permission/findPermissionByRoleId/{roleGuid}")
    public Result<List<RolePermissionDTO>> findPermissionById(@ApiParam(value = "角色guid") @PathVariable("roleGuid") String roleGuid) {
        return Result.buildSuccessResult(userFeignService.findPermissionById(roleGuid));
    }

    @ApiOperation(value = "根据userGuid查询该员工拥有的所有权限")
    @PostMapping(value = "/permission/findPermissionByUserId")
    public Result<List<RolePermissionDTO>> findPermissionByUserId() {
        return Result.buildSuccessResult(userFeignService.findPermissionByUserId(UserContextUtils.getUserGuid()));
    }

    @ApiOperation(value = "对角色进行授权")
    @PostMapping(value = "/grantPermission")
    public Result grantPermission(@RequestBody GrantRoleDTO grantRoleDTO) {
        userFeignService.grantPermission(grantRoleDTO);
        return Result.buildEmptySuccess();
    }
}