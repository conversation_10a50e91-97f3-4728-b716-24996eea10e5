package com.holderzone.holder.saas.aggregation.weixin.entity.cmember;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.aggregation.weixin.entity.enums.HistorySearchTypeEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @create 2024-09-12
 * @description 历史搜索
 */
@Data
public class HistorySearchDTO {

    /**
     * 搜索键
     */
    @NotEmpty
    private String key;

    /**
     * 搜索键
     */
    private String content;

    /**
     * 类型
     */
    private int type;

    public String cacheKey(){
        for(HistorySearchTypeEnum historySearchTypeEnum : HistorySearchTypeEnum.values()){
            if (historySearchTypeEnum.getType() == this.type){
                return historySearchTypeEnum.name() + ":" + key;
            }
        }
        throw new BusinessException("搜索类型不能存在");
    }

    public void verifyParams(){
        if(StringUtils.isEmpty(key) || StringUtils.isEmpty(content)){
            throw new BusinessException("搜索参数有误");
        }
    }
}
