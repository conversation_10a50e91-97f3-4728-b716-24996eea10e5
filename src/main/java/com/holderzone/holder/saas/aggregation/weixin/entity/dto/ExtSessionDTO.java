package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.websocket.Session;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className ExtSessionDTO
 * @date 2019/5/8
 */
@Data
@ApiModel("微信websocket会话封装")
public class ExtSessionDTO {
	private Session session;
	private long lastTime;
	private byte resetTime;
	public static final long DEADLINE=5000;
	public static final byte UPPER_LIMIT_TIMES=3;
	public static final byte INITIAL_RESET_TIMES=0;


	public ExtSessionDTO(Session session) {
		this.session=session;
		this.lastTime=System.currentTimeMillis();
		this.resetTime=INITIAL_RESET_TIMES;
	}

	public void refreshTime(){
		this.lastTime=System.currentTimeMillis();
		this.resetTime=INITIAL_RESET_TIMES;
	}

	public boolean checkAndClose(){
		if(resetTime>=UPPER_LIMIT_TIMES){
			return true;
		}else if(System.currentTimeMillis()-lastTime>DEADLINE){
			this.resetTime++;
			lastTime=System.currentTimeMillis();
			return false;
		}
		return false;
	}
}
