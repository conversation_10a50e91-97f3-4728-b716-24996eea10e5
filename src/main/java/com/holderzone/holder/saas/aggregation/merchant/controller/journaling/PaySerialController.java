package com.holderzone.holder.saas.aggregation.merchant.controller.journaling;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.journaling.ReportClientService;
import com.holderzone.holder.saas.aggregation.merchant.util.ReportValidateUtil;
import com.holderzone.saas.store.dto.journaling.req.PaySerialStatisticsReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.PaySerialStatisticsRespDTO;
import com.holderzone.saas.store.dto.report.resp.PageExt;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalTime;
import java.util.List;

@RestController
@RequestMapping("/pay_serial")
@Api(tags = "支付流水")
@Slf4j
public class PaySerialController {

    @Autowired
    ReportClientService reportClientService;

    @PostMapping("/page")
    public Result<PageExt<PaySerialStatisticsRespDTO>> pagePaySerial(@RequestBody PaySerialStatisticsReqDTO paySerialStatisticsReqDTO) {
        log.info("支付流水请求入参：{}", JacksonUtils.writeValueAsString(paySerialStatisticsReqDTO));
        //门店兼容
        ReportValidateUtil.reportCompatibilityCheck(paySerialStatisticsReqDTO);
        //时间兼容
        setReqDateTime(paySerialStatisticsReqDTO);
        return Result.buildSuccessResult(reportClientService.pagePaySerial(paySerialStatisticsReqDTO));
    }

    private static void setReqDateTime(PaySerialStatisticsReqDTO businessSituationReqDTO) {
        if (businessSituationReqDTO.getBusinessEndDateTime() == null) {
            businessSituationReqDTO.setBusinessEndDateTime(businessSituationReqDTO.getEndDate().atTime(LocalTime.MAX));
        }
        if (businessSituationReqDTO.getBusinessStartDateTime() == null) {
            businessSituationReqDTO.setBusinessStartDateTime(businessSituationReqDTO.getStartDate().atTime(LocalTime.MIN));
        }
    }
}
