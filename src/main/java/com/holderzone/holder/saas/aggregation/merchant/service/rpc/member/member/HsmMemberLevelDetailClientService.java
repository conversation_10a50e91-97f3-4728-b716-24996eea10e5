package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.dto.account.request.MemberLevelDetailReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberLevelDetailRespDTO;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.HsmMemberLevelDetailClientService.MemberLevelDetailClientServiceFallback;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HsmMemberLevelDetailClientService
 * @date 2019/08/21 14:30
 * @description 会员等级明细
 * @program holder-saas-member-account
 */

@Component
@FeignClient(name = "holder-saas-member-account", fallbackFactory = MemberLevelDetailClientServiceFallback.class)
public interface HsmMemberLevelDetailClientService {

    /**
     * 通过条件分页查询
     * @param queryReqDTO
     * @return
     */
    @PostMapping(value = "/hsm_member_level_detail/listByCondition", produces = "application/json;charset=utf-8")
    Page<MemberLevelDetailRespDTO> listByCondition(@RequestBody MemberLevelDetailReqDTO queryReqDTO);

    @Component
    class MemberLevelDetailClientServiceFallback implements FallbackFactory<HsmMemberLevelDetailClientService> {

        private static final Logger LOGGER = LoggerFactory
                .getLogger(MemberLevelDetailClientServiceFallback.class);

        @Override
        public HsmMemberLevelDetailClientService create(Throwable throwable) {

            return new HsmMemberLevelDetailClientService() {
                @Override
                public Page<MemberLevelDetailRespDTO> listByCondition(MemberLevelDetailReqDTO queryReqDTO) {
                    LOGGER.error("查询会员等级明细错误:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }
            };

        }
    }
}
