//package com.holderzone.holder.saas.aggregation.weixin.service.rpc.cmember.account;
//
//
//import com.holderzone.framework.exception.unchecked.BusinessException;
//import com.holderzone.framework.util.ThrowableUtils;
//import com.holderzone.holder.saas.cmember.wechat.dto.account.request.MemberInfoVolumeQueryReqDTO;
//import com.holderzone.holder.saas.cmember.wechat.dto.account.response.MemberInfoVolumeDetailsRespDTO;
//import com.holderzone.holder.saas.cmember.wechat.dto.account.response.MemberInfoVolumeRespDTO;
//import com.holderzone.holder.saas.cmember.wechat.dto.baseresource.request.StoreListReqDTO;
//import com.holderzone.holder.saas.cmember.wechat.dto.baseresource.response.StoreListRespDTO;
//import com.holderzone.holder.saas.member.dto.account.response.MemberSourceTypeRespDTO;
//import feign.hystrix.FallbackFactory;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.stereotype.Component;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//
//import java.util.List;
//
///**
// * <p>
// * 会员持卷表 服务类
// * </p>
// *
// * <AUTHOR>
// * @since 2019-06-20
// */
//@Component
//@FeignClient(name = "holder-saas-cmember-wechat-base-service", fallbackFactory = IHsmMemberInfoVolumeService.IHsmMemberInfoVolumeFallback.class)
//public interface IHsmMemberInfoVolumeService {
//
//    @PostMapping(value = "/hsmcw/volume/getMemberVolume")
//    MemberInfoVolumeRespDTO getMemberVolume(MemberInfoVolumeQueryReqDTO memberInfoVolumeQueryReqDTO);
//
//    @RequestMapping(value = "/hsmcw/volume/getMemberVolumeDetails")
//    MemberInfoVolumeDetailsRespDTO getMemberVolumeDetails(@RequestParam("memberVolumeGuid") String memberVolumeGuid);
//
//    @PostMapping(value = "/hsmcw/volume/getEnterpriseStoreList")
//    StoreListRespDTO getEnterpriseStoreList(StoreListReqDTO storeListReqDTO);
//
//    @GetMapping("/hsmcw/volume/getVolumeTypes")
//    List<MemberSourceTypeRespDTO> getVolumeTypes();
//
//    @Component
//    class IHsmMemberInfoVolumeFallback implements
//            FallbackFactory<IHsmMemberInfoVolumeService> {
//
//        private static final Logger LOGGER = LoggerFactory
//                .getLogger(IHsmMemberInfoVolumeFallback.class);
//
//        @Override
//        public IHsmMemberInfoVolumeService create(Throwable throwable) {
//            return new IHsmMemberInfoVolumeService() {
//
//                @Override
//                public MemberInfoVolumeRespDTO getMemberVolume(MemberInfoVolumeQueryReqDTO memberInfoVolumeQueryReqDTO) {
//                    LOGGER.error("查询会员优惠券列表错误:{}", ThrowableUtils.asString(throwable));
//                    throw new BusinessException(throwable.getMessage());
//                }
//
//                @Override
//                public MemberInfoVolumeDetailsRespDTO getMemberVolumeDetails(String memberVolumeGuid) {
//                    LOGGER.error("查看优惠券详情错误:{}", ThrowableUtils.asString(throwable));
//                    throw new BusinessException(throwable.getMessage());
//                }
//
//                @Override
//                public StoreListRespDTO getEnterpriseStoreList(StoreListReqDTO storeListReqDTO) {
//                    LOGGER.error("查询某商家门店列表错误:{}", ThrowableUtils.asString(throwable));
//                    throw new BusinessException(throwable.getMessage());
//                }
//
//                @Override
//                public List<MemberSourceTypeRespDTO> getVolumeTypes() {
//                    LOGGER.error("获取优惠券类型错误:{}", ThrowableUtils.asString(throwable));
//                    throw new BusinessException(throwable.getMessage());
//                }
//            };
//        }
//    }
//}
