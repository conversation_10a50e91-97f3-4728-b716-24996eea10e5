package com.holderzone.holder.saas.aggregation.app.controller.print;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.PrintClientService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.print.raw.PrinterRawAggDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Api("打印数据同步接口")
@RequestMapping(value = "/printer", produces = {"application/json;charset=UTF-8"})
public class PrintSyncController {

    private final PrintClientService printClientService;

    @Autowired
    public PrintSyncController(PrintClientService printClientService) {
        this.printClientService = printClientService;
    }

    @PostMapping("/sync")
    @ApiOperation(value = "打印数据同步接口")
    public Result<PrinterRawAggDTO> sync(@RequestBody BaseDTO baseDTO) {
        if (log.isInfoEnabled()) {
            log.info("打印数据同步接口入参:{}", JacksonUtils.writeValueAsString(baseDTO));
        }
        return Result.buildSuccessResult(printClientService.printTableSync(baseDTO));
    }

}
