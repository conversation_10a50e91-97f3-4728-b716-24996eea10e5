package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.holder.saas.aggregation.app.service.ReserveService;
import com.holderzone.saas.store.reserve.api.ReserveRecordApi;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordDetailDTO;
import com.holderzone.saas.store.dto.reserve.ReserveRecordGuidDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveServiceImpl
 * @date 2019/05/28 16:23
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Service
public class ReserveServiceImpl implements ReserveService {

    @Autowired
    private ReserveRecordApi reserveRecordApi;

    @Override
    public ReserveRecordDetailDTO launch(ReserveRecordDTO reserveRecordDTO) {
        ReserveRecordDetailDTO dto = reserveRecordApi.launch(reserveRecordDTO);
        ReserveRecordGuidDTO reserveRecordGuidDTO = new ReserveRecordGuidDTO();
        reserveRecordGuidDTO.setGuid(dto.getGuid());
        return reserveRecordApi.pass(reserveRecordGuidDTO);
    }
}