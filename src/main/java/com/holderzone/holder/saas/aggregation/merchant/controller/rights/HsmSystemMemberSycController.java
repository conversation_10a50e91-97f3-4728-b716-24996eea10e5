package com.holderzone.holder.saas.aggregation.merchant.controller.rights;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.baseresource.HsmBrandService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.baseresource.HsmEnterpriseInfoService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.baseresource.HsmProductService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.baseresource.HsmStoreService;
import com.holderzone.holder.saas.member.dto.baseresource.request.HsmBrandReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.request.HsmEnterpriseReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.request.HsmProductReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.request.HsmStoreReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.response.HsmBrandRespDTO;
import com.holderzone.holder.saas.member.dto.baseresource.response.HsmEnterpriseRespDTO;
import com.holderzone.holder.saas.member.dto.baseresource.response.HsmStoreRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 商家|门店|品牌|菜品 数据同步
 *
 * <AUTHOR>
 * @date 2019/6/11 16:59
 */
@RestController
@RequestMapping("/hsm/system/member/v1")
@Api(description = "数据同步控制层")
public class HsmSystemMemberSycController {
    @Resource
    private HsmBrandService iHsmBrandService;
    @Resource
    private HsmEnterpriseInfoService iHsmEnterpriseInfoService;
    @Resource
    private HsmProductService iHsmProductService;
    @Resource
    private HsmStoreService hsmStoreService;


    /**
     * 同步品牌
     *
     * @param hsmBrandReqDTO 列表
     */
    @ApiOperation(value = "同步单个品牌", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, response = HsmBrandRespDTO.class)
    @PostMapping("/brand/syc")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "同步单个品牌")
    public Result sycBrand(
            @ApiParam(value = "品牌对象", required = true) @RequestBody HsmBrandReqDTO hsmBrandReqDTO) {
        return Result.buildSuccessResult(iHsmBrandService.syc(hsmBrandReqDTO));
    }

    /**
     * 同步品牌
     *
     * @param hsmBrandReqDTOS 列表
     */
    @ApiOperation(value = "根据Json列表同步多个品牌", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, response = HsmBrandRespDTO.class)
    @PostMapping("/brand/syc/list")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "根据Json列表同步多个品牌")
    public Result sycBrandList(
            @ApiParam(value = "品牌对象列表", required = true) @RequestBody List<HsmBrandReqDTO> hsmBrandReqDTOS) {
        return Result.buildSuccessResult(iHsmBrandService.syc(hsmBrandReqDTOS));
    }



    /**
     * 同步企业
     *
     * @param hsmEnterpriseReqDTO 列表
     */
    @ApiOperation(value = "同步单个企业", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, response = HsmEnterpriseRespDTO.class)
    @PostMapping("/enterprise/syc")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "同步单个企业")
    public Result sycEnterprise(@Valid
                                @ApiParam(value = "企业对象", required = true) @RequestBody HsmEnterpriseReqDTO hsmEnterpriseReqDTO) {
        return Result.buildSuccessResult(iHsmEnterpriseInfoService.syc(hsmEnterpriseReqDTO));
    }

    /**
     * 同步企业
     *
     * @param hsmEnterpriseReqDTOs 列表
     */
    @ApiOperation("根据Json列表同步多个企业")
    @PostMapping("/enterprise/syc/list")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "根据Json列表同步多个企业")
    public Result sycEnterpriseList(
            @Valid @RequestBody List<HsmEnterpriseReqDTO> hsmEnterpriseReqDTOs) {
        return Result.buildSuccessResult(iHsmEnterpriseInfoService.sycList(hsmEnterpriseReqDTOs));
    }


    /**
     * 同步菜品
     *
     * @param productReqDTOList 列表
     */
    @ApiOperation(value = "根据Json列表同步多个菜品", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, response = HsmProductReqDTO.class)
    @PostMapping("/product/syc/list")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "根据Json列表同步多个菜品")
    public Result sycProductList(
            @ApiParam(value = "菜品对象列表", required = true) @RequestBody List<HsmProductReqDTO> productReqDTOList) {
        return Result.buildSuccessResult(iHsmProductService.syc(productReqDTOList));
    }

    /**
     * 同步菜品
     *
     * @param productReqDTO 列表
     */
    @ApiOperation(value = "同步单个菜品", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, response = HsmProductReqDTO.class)
    @PostMapping("/product/syc")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "同步单个菜品")
    public Result sycProduct(
            @ApiParam(value = "菜品对象", required = true) @RequestBody HsmProductReqDTO productReqDTO) {
        return Result.buildSuccessResult(iHsmProductService.syc(productReqDTO));
    }




    /**
     * 同步门店
     *
     * @param hsmStoreReqDTOS 列表
     */
    @ApiOperation(value = "根据Json列表同步多个门店", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, response = HsmStoreRespDTO.class)
    @PostMapping("/store/syc/list")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "根据Json列表同步多个门店")
    public Result sycStoreList(
            @ApiParam(value = "商品对象列表", required = true) @RequestBody List<HsmStoreReqDTO> hsmStoreReqDTOS) {
        return Result.buildSuccessResult(hsmStoreService.syc(hsmStoreReqDTOS));
    }

    /**
     * 同步门店
     *
     * @param hsmStoreReqDTO
     */
    @ApiOperation("同步单个门店")
    @PostMapping("/store/syc")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "同步单个门店")
    public Result sycStore(@RequestBody HsmStoreReqDTO hsmStoreReqDTO) {
        return Result.buildSuccessResult(hsmStoreService.syc(hsmStoreReqDTO));
    }


}
