package com.holderzone.holder.saas.aggregation.weixin.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.GroupBuyService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxItemClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.GroupClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.ThirdActivityClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.TradeClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.TradeThirdActivityClientService;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityRespDTO;
import com.holderzone.holder.saas.weixin.common.CacheName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.member.activity.ThirdActivityTypeEnum;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.trade.req.ThirdActivityRecordDTO;
import com.holderzone.saas.store.dto.weixin.deal.ItemInfoDTO;
import com.holderzone.saas.store.dto.weixin.deal.ShopCartItemReqDTO;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.enums.order.RuleTypeEnum;
import com.holderzone.saas.store.enums.trade.CouponTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class GroupBuyServiceImpl implements GroupBuyService {

    private final GroupClientService groupClientService;

    private final ThirdActivityClientService thirdActivityClientService;

    private final TradeThirdActivityClientService tradeThirdActivityClientService;

    private final WxItemClientService wxItemClientService;

    private final TradeClientService tradeClientService;

    private final RedisUtils redisUtils;

    @Override
    public List<MtCouponPreRespDTO> preCheckQueryItem(CouPonPreReqDTO couPonPreReqDTO) {
        // 查询券信息
        couPonPreReqDTO.setStoreGuid(UserContextUtils.getStoreGuid());
        List<MtCouponPreRespDTO> couponPreResp = couponPrepare(couPonPreReqDTO);
        // 验券加商品 只能使用套餐券
        MtCouponPreRespDTO preRespDTO = couponPreResp.get(0);
        if (preRespDTO.getCouponType() != CouponTypeEnum.ITEM.getCode()) {
            throw new BusinessException(Constant.COUPON_CODE_VOUCHER_INVALID);
        }
        // 查询购物车中商品验券
        List<ShopCartItemReqDTO> shopCartItemList = queryShopCart();
        // 过滤购物车已验的券
        filterMtCouponPreRespList(couponPreResp, shopCartItemList);
        log.info("过滤购物车已验券之后,couponPreResp:{}", JacksonUtils.writeValueAsString(couponPreResp));
        if (CollectionUtils.isEmpty(couponPreResp)) {
            throw new BusinessException(Constant.COUPON_CODE_INVALID);
        }
        ThirdActivityRespDTO thirdActivity;
        try {
            // 根据活动编号查询第三方活动
            thirdActivity = queryThirdActivity(couPonPreReqDTO, preRespDTO);
        } catch (Exception e) {
            log.error("查询第三方活动异常,券返回信息:{},错误信息:{}", JacksonUtils.writeValueAsString(preRespDTO), e.getMessage());
            throw new BusinessException(Constant.ACTIVITY_WRONG);
        }
        if (ObjectUtils.isEmpty(thirdActivity) || 0 != thirdActivity.getRuleType()) {
            throw new BusinessException(Constant.ACTIVITY_WRONG);
        }
        log.info("匹配到第三方活动信息:{}", JacksonUtils.writeValueAsString(thirdActivity));
        //替换品牌库的商品guid为门店库
        thirdActivity.setItemGuid(replaceItemGuid(thirdActivity.getItemGuid()));
        // 校验第三方活动规则
        preCheckParamVerify(couPonPreReqDTO.getErpOrderId(), thirdActivity, shopCartItemList);
        // 构建返回值
        return buildRespDTOList(couponPreResp, thirdActivity, couPonPreReqDTO);
    }

    private String replaceItemGuid(String itemGuid) {
        if (StringUtils.isEmpty(itemGuid)) {
            return itemGuid;
        }
        String subItemGuid = wxItemClientService.getSubItemGuid(itemGuid);
        log.info("品牌库商品对应的门店商品guid：{}", subItemGuid);
        if (StringUtils.isEmpty(subItemGuid)) {
            return itemGuid;
        }
        return subItemGuid;
    }

    /**
     * 过滤购物车已验的券
     */
    private void filterMtCouponPreRespList(List<MtCouponPreRespDTO> couponPreResp,
                                           List<ShopCartItemReqDTO> shopCartItemList) {
        List<MtCouponPreRespDTO> couponPreRespList = shopCartItemList.stream()
                .filter(e -> Objects.nonNull(e.getItemInfoDTO().getCouponPreRespDTO()))
                .map(e -> e.getItemInfoDTO().getCouponPreRespDTO())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(couponPreRespList)) {
            return;
        }
        List<String> couponCodeList = couponPreRespList.stream()
                .map(MtCouponPreRespDTO::getCouponCode)
                .collect(Collectors.toList());
        Iterator<MtCouponPreRespDTO> iterator = couponPreResp.iterator();
        while (iterator.hasNext()) {
            MtCouponPreRespDTO couponPreRespDTO = iterator.next();
            if (couponCodeList.contains(couponPreRespDTO.getCouponCode())) {
                iterator.remove();
                couponCodeList.remove(couponPreRespDTO.getCouponCode());
            }
        }
    }

    /**
     * 查询团购券信息
     */
    private List<MtCouponPreRespDTO> couponPrepare(CouPonPreReqDTO couPonPreReqDTO) {
        List<MtCouponPreRespDTO> couponPreResp;
        try {
            couponPreResp = groupClientService.couponPrepare(couPonPreReqDTO);
        } catch (Exception e) {
            log.error("预验券失败,请求参数:{},失败原因:{}", JacksonUtils.writeValueAsString(couPonPreReqDTO), e.getMessage());
            throw new BusinessException(e.getMessage());
        }
        if (CollectionUtils.isEmpty(couponPreResp)) {
            log.error("预验券失败,返回为空，入参:{}", JacksonUtils.writeValueAsString(couPonPreReqDTO));
            throw new BusinessException(Constant.COUPON_CODE_INVALID);
        }
        log.info("验券返回信息:{}", JacksonUtils.writeValueAsString(couponPreResp));
        return couponPreResp;
    }


    /**
     * 查询第三方活动
     */
    private ThirdActivityRespDTO queryThirdActivity(CouPonPreReqDTO couPonPreReqDTO, MtCouponPreRespDTO respDTO) {
        // 活动编号
        String thirdCode = String.valueOf(respDTO.getDealId());
        if (GroupBuyTypeEnum.DOU_YIN.getCode() == couPonPreReqDTO.getGroupBuyType()) {
            thirdCode = respDTO.getSkuId();
        }
        if (GroupBuyTypeEnum.ALIPAY.getCode() == couPonPreReqDTO.getGroupBuyType()) {
            thirdCode = respDTO.getItemId();
        }
        String activityName = respDTO.getDealTitle();
        String thirdType = ThirdActivityTypeEnum.transferThirdType(couPonPreReqDTO.getGroupBuyType());
        return thirdActivityClientService.getByThirdCode(thirdCode, thirdType, activityName);
    }

    /**
     * 验券 规则校验
     */
    private void preCheckParamVerify(String orderGuid, ThirdActivityRespDTO thirdActivity,
                                     List<ShopCartItemReqDTO> shopCartItemList) {
        List<ThirdActivityRecordDTO> usedThirdActivityRecordList = Lists.newArrayList();
        if (StringUtils.isNotEmpty(orderGuid)) {
            // 无论先使用第三方活动的券 还是 直接使用团购验券 都要校验订单上已验的券
            // 查询订单上已使用的第三方活动
            usedThirdActivityRecordList = tradeThirdActivityClientService.listThirdActivityByOrderGuid(orderGuid);
            log.info("查询订单上已使用的第三方活动信息,orderGuid:{},活动明细:{}", orderGuid, JacksonUtils.writeValueAsString(usedThirdActivityRecordList));
        }
        // 获取购物车中验券商品对应的第三方活动
        List<ThirdActivityRespDTO> shopCartThirdActivityList = queryShopCartThirdActivityList(shopCartItemList);
        // 校验共享互斥规则
        checkThirdActivityShareRule(orderGuid, thirdActivity, shopCartThirdActivityList, usedThirdActivityRecordList);
        // 校验使用上限
        checkThirdActivityLimitRule(thirdActivity, shopCartItemList, usedThirdActivityRecordList);
    }

    /**
     * 获取当前购物车商品列表
     */
    private List<ShopCartItemReqDTO> queryShopCart() {
        String shopCartKey = CacheName.SHOP_CART_ITEM + ":" + WeixinUserThreadLocal.getStoreGuid() + ":" + WeixinUserThreadLocal.getDiningTableGuid();
        List<ShopCartItemReqDTO> itemList = redisUtils.hValues(shopCartKey);
        log.info("查询购物车返回:{}", JacksonUtils.writeValueAsString(itemList));
        return itemList;
    }

    /**
     * 获取购物车中验券商品对应的第三方活动
     */
    private List<ThirdActivityRespDTO> queryShopCartThirdActivityList(List<ShopCartItemReqDTO> shopCartItemList) {
        List<MtCouponPreRespDTO> couponPreRespList = Optional.ofNullable(shopCartItemList).orElse(Lists.newArrayList())
                .stream()
                .map(ShopCartItemReqDTO::getItemInfoDTO)
                .map(ItemInfoDTO::getCouponPreRespDTO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(couponPreRespList)) {
            List<String> activityGuidList = couponPreRespList.stream()
                    .map(MtCouponPreRespDTO::getNewActivityGuid)
                    .distinct()
                    .collect(Collectors.toList());
            return thirdActivityClientService.listByGuid(activityGuidList);
        }
        return Lists.newArrayList();
    }

    /**
     * 校验第三方活动共享规则
     */
    private void checkThirdActivityShareRule(String orderGuid, ThirdActivityRespDTO thirdActivity,
                                             List<ThirdActivityRespDTO> shopCartThirdActivityList,
                                             List<ThirdActivityRecordDTO> thirdActivityRecords) {
        // 当前券共享互斥规则
        if (thirdActivity.getIsThirdShare() == 1) {
            // 共享
            // 判断已验券和加入购物车的验券是否有互斥的
            long usedNotSharedCount = thirdActivityRecords.stream()
                    .filter(e -> !e.getActivityGuid().equals(thirdActivity.getGuid()) && e.getIsThirdShare() == 0)
                    .count();
            long shopCartNotSharedCount = shopCartThirdActivityList.stream()
                    .filter(e -> !e.getGuid().equals(thirdActivity.getGuid()) && e.getIsThirdShare() == 0)
                    .count();
            if (usedNotSharedCount + shopCartNotSharedCount > 0) {
                throw new BusinessException(Constant.IS_THIRD_SHARE_TIPS);
            }
        } else {
            // 互斥
            long otherUsedActivityCount = thirdActivityRecords.stream()
                    .filter(e -> !e.getActivityGuid().equals(thirdActivity.getGuid()))
                    .count();
            long otherShopCartActivityCount = shopCartThirdActivityList.stream()
                    .filter(e -> !e.getGuid().equals(thirdActivity.getGuid()))
                    .count();
            if (otherUsedActivityCount + otherShopCartActivityCount > 0) {
                throw new BusinessException(Constant.IS_THIRD_SHARE_TIPS);
            }
            if (StringUtils.isNotEmpty(orderGuid)) {
                // 如果没有使用其他第三方活动，则需要去查询是否有团购验券
                List<GrouponListRespDTO> grouponList = tradeClientService.useGrouponList(orderGuid, null);
                if (CollectionUtils.isNotEmpty(grouponList)) {
                    throw new BusinessException(Constant.IS_THIRD_SHARE_TIPS);
                }
            }
        }
    }

    /**
     * 校验第三方活动使用上限
     */
    private void checkThirdActivityLimitRule(ThirdActivityRespDTO thirdActivity,
                                             List<ShopCartItemReqDTO> shopCartItemList,
                                             List<ThirdActivityRecordDTO> thirdActivityRecords) {
        // ⑥点击“☑”按钮需要判断该活动的叠加数量是否达上限（判断数量为已展示在上方的券码），达上限提示：最多可叠加x张
        if (RuleTypeEnum.AMOUNT_DEDUCTION.getCode() == thirdActivity.getRuleType()) {
            Integer useLimit = thirdActivity.getUseLimit();
            long usedCount = thirdActivityRecords.stream()
                    .filter(e -> e.getActivityGuid().equals(thirdActivity.getGuid()))
                    .flatMap(e -> e.getThirdActivityCodeList().stream())
                    .distinct()
                    .count();
            long shopCartCount = shopCartItemList.stream()
                    .filter(e -> Objects.nonNull(e.getItemInfoDTO().getCouponPreRespDTO())
                            && Objects.equals(e.getItemInfoDTO().getCouponPreRespDTO().getNewActivityGuid(), thirdActivity.getGuid()))
                    .count();
            if (usedCount + shopCartCount >= useLimit) {
                throw new BusinessException("最多可叠加" + useLimit + "张");
            }
        }
    }


    /**
     * 构建返回值
     */
    private List<MtCouponPreRespDTO> buildRespDTOList(List<MtCouponPreRespDTO> couponPreResp, ThirdActivityRespDTO finalMtActivity,
                                                      CouPonPreReqDTO couPonPreReqDTO) {
        Byte defaultShare = 0;
        Integer defaultUseLimit = 9999;
        String currentTime = DateTimeUtils.localDateTime2String(LocalDateTime.now());
        couponPreResp.forEach(e -> {
            // 默认不共享
            e.setIsThirdShare(defaultShare);
            e.setIsActivityShare(defaultShare);
            e.setUseLimit(defaultUseLimit);
            if (Objects.nonNull(finalMtActivity)) {
                e.setItemGuid(finalMtActivity.getItemGuid());
                e.setSkuGuid(finalMtActivity.getSkuGuid());
                e.setActivityGuid(finalMtActivity.getGuid());
                e.setNewActivityGuid(finalMtActivity.getGuid());
                e.setIsThirdShare(finalMtActivity.getIsThirdShare());
                e.setIsActivityShare(finalMtActivity.getIsActivityShare());
                e.setGroupBuyType(couPonPreReqDTO.getGroupBuyType());
                e.setUseLimit(finalMtActivity.getUseLimit());
                e.setPreQueryTime(currentTime);
            }
        });
        // 一次只能验一张
        return Lists.newArrayList(couponPreResp.get(0));
    }
}
