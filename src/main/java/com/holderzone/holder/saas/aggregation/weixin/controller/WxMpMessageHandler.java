package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.dynamic.datasource.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.config.WeCatConfig;
import com.holderzone.holder.saas.aggregation.weixin.service.MenuItemService;
import com.holderzone.holder.saas.aggregation.weixin.service.WxCpService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.DebtClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxMpMessageHandlerClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxOpenMessageHandleClientService;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WxAuthorizeReqUtils;
import com.holderzone.saas.store.dto.trade.DebtRecordH5RespDTO;
import com.holderzone.saas.store.dto.trade.DebtUnitLoginH5ReqDTO;
import com.holderzone.saas.store.dto.weixin.menu.WxMenuUrlDTO;
import com.holderzone.saas.store.dto.weixin.req.*;
import com.holderzone.saas.store.dto.weixin.resp.WxConfigRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxSubjectRespDTO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxMpMessageHandler
 * @date 2019/04/01 14:47
 * @description
 * @program holder-saas-store
 */
@RequestMapping("/wx_mp")
@Slf4j
@RestController
public class WxMpMessageHandler {

    @Autowired
    WxMpMessageHandlerClientService wxMpMessageHandlerClientService;

    @Autowired
    WxOpenMessageHandleClientService wxMessageHandleClientService;

    @Autowired
    MenuItemService menuItemService;

    @Autowired
    WeCatConfig weCatConfig;

    @Resource
    RedisUtils redisUtils;

    @Autowired
    private DebtClientService debtClientService;

    @Autowired
    private WxCpService wxCpService;

    @GetMapping("/portal")
    public String authGet(WxCommonReqDTO wxCommonReqDTO) {
        log.info("微信聚合层：接收到来自微信服务器的认证消息：[signature: {}" +
                        ", timestamp: {}, nonce: {}, echostr: {}]",
                wxCommonReqDTO.getSignature(), wxCommonReqDTO.getTimestamp(),
                wxCommonReqDTO.getNonce(), wxCommonReqDTO.getEchostr());
        return wxMpMessageHandlerClientService.verify(wxCommonReqDTO);
    }

    @GetMapping("/call_back")
    public void callBack(WxAuthorizeReqDTO wxAuthorizeReqDTO, HttpServletResponse response) {
        try {
//             wxAuthorizeReqDTO.setAppId(appId);
            WxAuthorizeReqUtils.handEventKey(wxAuthorizeReqDTO);
            String enterpriseGuid = wxAuthorizeReqDTO.getEnterpriseGuid();
            log.info("enterpriseGuid:{}", enterpriseGuid);
            if (enterpriseGuid != null) {
                EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            }
            if (wxAuthorizeReqDTO.getEventKey().startsWith(BusinessName.ORDER_CONFIG)
                    || wxAuthorizeReqDTO.getEventKey().startsWith(BusinessName.ORDER_CONFIG_2)
            ) {
                redirectHandler(wxAuthorizeReqDTO, response);
                return;
            }
            String redirectUrl = wxMpMessageHandlerClientService.getUserInfo(wxAuthorizeReqDTO);
            log.info("已获取到二维码对应链接地址：{}，将进行跳转", redirectUrl);
            response.sendRedirect(redirectUrl);
        } catch (IOException e) {
            throw new BusinessException("微信重定向失败，e:" + e);
        }
    }

    private void redirectHandler(WxAuthorizeReqDTO wxAuthorizeReqDTO, HttpServletResponse response) {
        try {
            String enterpriseGuid = wxAuthorizeReqDTO.getEnterpriseGuid();
            Long currentTime = wxAuthorizeReqDTO.getCurrentTime();
            //有效
            boolean validTime = weCatConfig.getOrderingMessageExpire() > 0
                    && currentTime != null
                    && (currentTime + 1000 * 60 * weCatConfig.getOrderingMessageExpire()) >= System.currentTimeMillis();
            validTime = validTime || currentTime == null;
            log.info("currentTime={},validTime={},orderingMessageExpire={}"
                    , currentTime, validTime, weCatConfig.getOrderingMessageExpire());
            if (weCatConfig.getOrderingMessageExpire() > 0 && !validTime) {
                //无效
                response.setContentType("text/html;charset=utf-8");
                response.getWriter().print("<!DOCTYPE html><html><head><meta charset=\"utf-8\" /></head>\n" +
                        "<script  type=\"text/javascript\">alert('桌码已失效，请重新扫桌台二维码点餐！');</script></html>");
                return;
            }
            /**
             *  二维码类型:0普通二维码，1带参数二维码
             Integer qrCodeType;
             */
            //微信点餐服务
            //a结尾
            String weixinToken = redisUtils.generateGuid("xweixintoken");
            menuItemService.saveQrCodeTypeByToken(weixinToken, 0);
            //redisUtils.setEx("qrCodeType:"+weixinToken, 0,1,TimeUnit.DAYS);
            wxAuthorizeReqDTO.setWeixinToken(weixinToken);
            String redirectUrl = String.format(weCatConfig.getOrderingIndexPage(), weixinToken, enterpriseGuid);
            log.info("wx_mp/call_back入参{}", JacksonUtils.writeValueAsString(wxAuthorizeReqDTO));
            WxMemberSessionDTO session = wxMessageHandleClientService.buildSession(wxAuthorizeReqDTO);
            log.info("wx_mp/call_back返回:{}", JacksonUtils.writeValueAsString(session));
            if (session.getCode() == 2 && !StringUtils.isEmpty(session.getMessage())) {
                // 再次重定向
                response.sendRedirect(session.getMessage());
            }
            if (!StringUtils.isEmpty(session.getMessage())) {
                throw new BusinessException("微信重定向失败，" + session.getMessage());
            }
            redirectUrl = redirectUrl + "&storeGuid=" + session.getStoreGuid() + "&operSubjectGuid=" + session.getOperSubjectGuid();
            response.sendRedirect(redirectUrl);
        } catch (IOException e) {
            log.error("微信重定向失败", e);
            throw new BusinessException("微信重定向失败，e: " + e.getMessage());
        }
    }

    @GetMapping("/shop_list")
    public void shopList(WxPreCodReqDTO wxPreCodReqDTO, HttpServletResponse response) {
        try {
            String redirectUrl = wxMpMessageHandlerClientService.shopList(wxPreCodReqDTO);
            response.sendRedirect(redirectUrl);
        } catch (IOException e) {
            throw new BusinessException("微信重定向失败， e:" + e);
        }

    }

    @PostMapping("/get_wx_subject")
    @ApiOperation("获取微信运营主体参数")
    public Result<WxSubjectRespDTO> getWxSubject(@RequestBody WxSubjectReqDTO wxPortalReqDTO) {
        log.info("获取微信运营主体，请求参数：{}", JacksonUtils.writeValueAsString(wxPortalReqDTO));
        WxSubjectRespDTO wxConfig = wxMpMessageHandlerClientService.getWxSubject(wxPortalReqDTO);
        log.info("获取微信返回参数{}", JacksonUtils.writeValueAsString(wxConfig));
        return Result.buildSuccessResult(wxConfig);
    }

    @PostMapping("/login")
    public Result<String> memberLogin(@RequestBody WxPreCodReqDTO wxPreCodReqDTO) {
        log.info("接收到微信会员登录请求参数， wxPreCodeReqDTO: {}", wxPreCodReqDTO);
        String redirectUrl = wxMessageHandleClientService.memberLogin(WxPortalReqDTO.builder()
                .appId(wxPreCodReqDTO.getAppId())
                .brandGuid(wxPreCodReqDTO.getBrandGuid())
                .enterpriseGuid(wxPreCodReqDTO.getEnterpriseGuid())
                .reqType(wxPreCodReqDTO.getReqType())
                .build());
        return Result.buildSuccessResult(redirectUrl);
    }

    /**
     * 菜单地址
     *
     * @param wxPreCodReqDTO
     * @param response
     */
    @GetMapping("/new_member_login")
    public void newMemberLogin(WxPreCodReqDTO wxPreCodReqDTO, HttpServletResponse response) {
        log.info("接收到微信会员登录请求参数， wxPreCodeReqDTO: {}", wxPreCodReqDTO);
        String redirectUrl = wxMessageHandleClientService.newMemberLogin(WxPortalReqDTO.builder()
                .appId(wxPreCodReqDTO.getAppId())
                .brandGuid(wxPreCodReqDTO.getBrandGuid())
                .enterpriseGuid(wxPreCodReqDTO.getEnterpriseGuid())
                .build());
        try {
            log.info("菜单地址跳转,redirectUrl：“{}", redirectUrl);
            response.sendRedirect(redirectUrl);
        } catch (IOException e) {
            log.error("菜单地址跳转 失败,redirectUrl：“{}", redirectUrl, e);
        }
    }

    @PostMapping("/get_wx_config")
    @ApiOperation("获取微信请求jsSdk的参数")
    public Result<WxConfigRespDTO> getWxConfig(@RequestBody WxPortalReqDTO wxPortalReqDTO) {
        log.info("获取微信请求jsSdk的参数方法，请求参数：{}", JacksonUtils.writeValueAsString(wxPortalReqDTO));
        WxConfigRespDTO wxConfig = wxMpMessageHandlerClientService.getWxConfig(wxPortalReqDTO);
        log.info("获取微信返回参数{}", JacksonUtils.writeValueAsString(wxConfig));
        return Result.buildSuccessResult(wxConfig);
    }

    @PostMapping("/unit/h5login")
    @ApiOperation(value = "h5挂账页面登录")
    public Result<Boolean> h5Login(@Validated @RequestBody DebtUnitLoginH5ReqDTO reqDTO) {
        log.info("H5页面查询登录入参：{}", JSON.toJSONString(reqDTO));
        return Result.buildSuccessResult(debtClientService.h5Login(reqDTO));
    }

    @PostMapping("/record/h5query")
    @ApiOperation(value = "H5查询挂账还款记录")
    public Result<DebtRecordH5RespDTO> queryDebtRecordH5(@Validated @RequestBody DebtUnitLoginH5ReqDTO reqDTO) {
        log.info("H5查询挂账还款记录入参：{}", JSON.toJSONString(reqDTO));
        return Result.buildSuccessResult(debtClientService.queryDebtRecordH5(reqDTO));
    }

    @GetMapping("/zhuancan/qr")
    @ApiOperation(value = "扫码赚餐小程序二维码获取参数")
    public String redirectParams(String t) {
        log.info("赚餐小程序二维码参数入参：{}", t);
        String redirectParams = wxMpMessageHandlerClientService.qrRedirect(t);
        log.info("赚餐小程序二维码扫码返回结果：{}", redirectParams);
        return redirectParams;
    }

    @GetMapping("/qr")
    @ApiOperation(value = "扫码默认二维码重定向")
    public void redirect(String t, HttpServletResponse response) throws IOException {
        log.info("扫码默认二维码重定向入参：{}", t);
        String redirectUrl = wxMpMessageHandlerClientService.qrRedirect(t);
        log.info("默认二维码重定向：{}", redirectUrl);
        response.sendRedirect(redirectUrl);
    }

    /**
     * 微信公众号 老板助手菜单跳转地址
     */
    @GetMapping("/menu_url/boss")
    public void redirectBossAuthorizeUrl(WxMenuUrlDTO wxMenuUrlDTO, HttpServletResponse response) {
        log.info("微信公众号: 老板助手菜单跳转请求入参，wxMenuUrlDTO: {}", wxMenuUrlDTO);
        String bossAuthorizeUrl = wxMpMessageHandlerClientService.getBossAuthorizeUrl(wxMenuUrlDTO);
        try {
            log.info("微信公众号：老板助手菜单跳转,redirectUrl：{}", bossAuthorizeUrl);
            response.sendRedirect(bossAuthorizeUrl);
        } catch (Exception e) {
            log.error("微信公众号：老板助手菜单跳转 失败,redirectUrl：{}, e:{}", bossAuthorizeUrl, e.getMessage());
        }
    }

    @GetMapping("/menu_url/boss/call_back")
    public void redirectBossUrl(WxAuthorizeReqDTO wxAuthorizeReqDTO, HttpServletResponse response) {
        log.info("微信公众号: 老板助手菜单授权完成请求入参，wxAuthorizeReqDTO: {}", wxAuthorizeReqDTO);
        String redirectUrl = wxMpMessageHandlerClientService.getBossRedirectUrl(wxAuthorizeReqDTO);
        try {
            log.info("微信公众号：老板助手菜单授权完成跳转,redirectUrl：{}", redirectUrl);
            response.sendRedirect(redirectUrl);
        } catch (Exception e) {
            log.error("微信公众号：老板助手菜单授权完成跳转 失败,redirectUrl：{}, e:{}", redirectUrl, e.getMessage());
        }
    }

    @PostMapping("/save/boss/token")
    public Result<Void> saveBossAuthToken(@RequestBody WxMenuUrlDTO wxMenuUrlDTO) {
        log.info("微信公众号: 保存老板助手token请求入参，wxMenuUrlDTO: {}", wxMenuUrlDTO);
        wxMpMessageHandlerClientService.saveBossAuthToken(wxMenuUrlDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/clean/boss/token")
    public Result<Void> cleanBossAuthToken(@RequestBody WxMenuUrlDTO wxMenuUrlDTO) {
        log.info("微信公众号: 清除老板助手token请求入参，wxMenuUrlDTO: {}", wxMenuUrlDTO);
        wxMpMessageHandlerClientService.cleanBossAuthToken(wxMenuUrlDTO);
        return Result.buildEmptySuccess();
    }

    /**
     * 企微-用户授权
     */
    @GetMapping("/cp/authorize")
    public void wxCpAuthorize(String t, HttpServletResponse response) throws IOException {
        log.info("企微-用户授权请求入参，t: {}", t);
        String redirect = wxCpService.queryWxCpAuthorizeUrl(t);
        response.sendRedirect(redirect);
    }

    /**
     * 企微-用户授权
     */
    @GetMapping("/cp/authorize/url")
    public Result<String> wxCpAuthorizeUrl(String t) {
        log.info("企微-用户授权请求入参，t: {}", t);
        return Result.buildSuccessResult(wxCpService.queryWxCpAuthorizeUrl(t));
    }
}
