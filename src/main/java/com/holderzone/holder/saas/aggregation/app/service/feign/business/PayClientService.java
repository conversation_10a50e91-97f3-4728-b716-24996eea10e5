package com.holderzone.holder.saas.aggregation.app.service.feign.business;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.business.manage.HandoverPayDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PayClientService
 * @date 2018/09/10 15:59
 * @description
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = PayClientService.PayFallBack.class)
public interface PayClientService {

    @PostMapping("/daily/handover")
    HandoverPayDTO queryHandover(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO);


    @Component
    class PayFallBack implements FallbackFactory<PayClientService> {

        private static final Logger logger = LoggerFactory.getLogger(PayFallBack.class);

        @Override
        public PayClientService create(Throwable throwable) {
            return handoverPayQueryDTO -> {
                logger.error("聚合支付查询接口异常 e={}", throwable.getMessage());
                throwable.printStackTrace();
                throw new BusinessException("聚合支付查询接口异常!!" + throwable.getMessage());
            };
        }
    }
}
