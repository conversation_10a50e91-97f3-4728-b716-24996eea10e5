package com.holderzone.holder.saas.aggregation.app.controller.trade;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.base.dto.file.FileDto;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.constant.Constant;
import com.holderzone.holder.saas.aggregation.app.service.feign.BaseFeignService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInBillClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInOrderClientService;
import com.holderzone.holder.saas.aggregation.app.service.DineInBillAppService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.OrderMultiMemberDTO;
import com.holderzone.saas.store.dto.order.request.bill.*;
import com.holderzone.saas.store.dto.order.request.dinein.UpdateOrderReqDTO;
import com.holderzone.saas.store.dto.order.response.OrderTableBillDTO;
import com.holderzone.saas.store.dto.order.response.bill.AppendFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.BillMemberCardCalculateRespDTO;
import com.holderzone.saas.store.dto.order.response.bill.BillAggPayRespDTO;
import com.holderzone.saas.store.dto.order.response.bill.BillRefundAggPayRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.enums.common.ResultCodeEnum;
import com.holderzone.saas.store.enums.locale.LocaleMessageEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInBillAppController
 * @date 2018/09/04 11:26
 * @description app聚合层账单接口
 * @program holder-saas-aggregation-app
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/dine_in_bill")
@Api(tags = "订单结算接口")
@Slf4j
public class DineInBillAppController {

    private final DineInBillClientService dineInBillClientService;

    private final DineInOrderClientService dineInOrderClientService;

    private final BaseFeignService baseService;

    private final DineInBillAppService dineInBillAppService;

    @ApiOperation(value = "计算账单优惠", notes = "计算账单优惠")
    @PostMapping("/calculate")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "计算账单优惠", action = OperatorType.SELECT)
    public Result<DineinOrderDetailRespDTO> calculate(@RequestBody BillCalculateReqDTO billCalculateReqDTO) {
        log.info("计算账单优惠入参：{}", JacksonUtils.writeValueAsString(billCalculateReqDTO));
        DineinOrderDetailRespDTO calculate = dineInBillAppService.calculate(billCalculateReqDTO);
        if (calculate != null && CollectionUtils.isNotEmpty(calculate.getDiscountFeeDetailDTOS())) {
            calculate.getDiscountFeeDetailDTOS().forEach(c -> c.setDiscountName(DiscountTypeEnum.getLocaleDescByName(c.getDiscountName())));
        }
        return Result.buildSuccessResult(calculate);
    }


    /**
     * 获取金额卡信息并计算账单优惠
     */
    @ApiOperation(value = "获取金额卡信息并计算账单优惠", notes = "获取金额卡信息并计算账单优惠")
    @PostMapping(value = "/getMemberCardAndCalculate")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "获取金额卡信息并计算账单优惠", action = OperatorType.SELECT)
    public Result<DineinOrderDetailRespDTO> getMemberCardAndCalculate(@RequestBody BilMemberCardCalculateReqDTO reqDTO) {
        log.info("获取金额卡信息并计算账单优惠入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        return Result.buildSuccessResult(dineInBillAppService.getMemberCardAndCalculate(reqDTO));
    }

    @ApiOperation(value = "切换会员信息并计算账单优惠", notes = "切换会员信息并计算账单优惠")
    @PostMapping(value = "/switchoverMemberAndCalculate")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "切换会员信息并计算账单优惠", action = OperatorType.SELECT)
    public Result<DineinOrderDetailRespDTO> switchoverMemberAndCalculate(@RequestBody BilMemberCardCalculateReqDTO reqDTO) {
        log.info("切换会员信息并计算账单优惠入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        return Result.buildSuccessResult(dineInBillAppService.switchoverMemberAndCalculate(reqDTO));
    }

    @ApiOperation(value = "麓豆会员登录", notes = "麓豆会员登录")
    @PostMapping("/ludou_member_login")
    @EFKOperationLogAop(PLATFORM = Platform.CASHIERSYSTEM, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "麓豆会员登录", action = OperatorType.ADD)
    public Result<BillMemberCardCalculateRespDTO> ludouMemberLogin(@RequestBody BilMemberCardCalculateReqDTO cardCalculateReqDTO) {
        log.info("麓豆会员登录入参:{}", JacksonUtils.writeValueAsString(cardCalculateReqDTO));
        return Result.buildSuccessResult(dineInBillClientService.ludouMemberLogin(cardCalculateReqDTO));
    }

    @ApiOperation(value = "麓豆会员登出", notes = "麓豆会员登出")
    @PostMapping("/ludou_member_logout")
    @EFKOperationLogAop(PLATFORM = Platform.CASHIERSYSTEM, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "麓豆会员登出", action = OperatorType.DELETE)
    public Result<Void> ludouMemberLogout(@RequestBody BilMemberCardCalculateReqDTO cardCalculateReqDTO) {
        log.info("麓豆会员登出入参:{}", JacksonUtils.writeValueAsString(cardCalculateReqDTO));
        dineInBillClientService.ludouMemberLogout(cardCalculateReqDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "订单关联多卡", notes = "订单关联多卡")
    @PostMapping(value = "/relation_multi_member")
    @EFKOperationLogAop(PLATFORM = Platform.CASHIERSYSTEM, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "订单关联多卡", action = OperatorType.ADD)
    public Result<Void> relationOrderMultiMember(@RequestBody OrderMultiMemberDTO reqDTO) {
        log.info("订单关联多卡入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        dineInBillClientService.relationOrderMultiMember(reqDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "查询订单关联多卡", notes = "查询订单关联多卡")
    @GetMapping(value = "/query_relation_multi_member")
    @EFKOperationLogAop(PLATFORM = Platform.CASHIERSYSTEM, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "订单关联多卡", action = OperatorType.ADD)
    public Result<List<OrderMultiMemberDTO>> queryRelationOrderMultiMember(@RequestParam("orderGuid") String orderGuid) {
        log.info("查询订单关联多卡入参：{}", orderGuid);
        return Result.buildSuccessResult(dineInBillClientService.queryRelationOrderMultiMember(orderGuid));
    }

    @ApiOperation(value = "其他支付", notes = "其他支付")
    @PostMapping("/pay")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "其他支付", action = OperatorType.SELECT)
    public Result pay(@RequestBody @Valid BillPayReqDTO billPayReqDTO) {
        log.info("其他支付入参：{}", JacksonUtils.writeValueAsString(billPayReqDTO));
        EstimateItemRespDTO estimateItemRespDTO = dineInBillClientService.pay(billPayReqDTO);
        if (estimateItemRespDTO.getEstimate() != null && estimateItemRespDTO.getEstimate()) {
            return Result.buildFailResult(ResultCodeEnum.ITEM_ESTIMATE_EXCEPTION.getCode(), LocaleMessageEnum.getLocale(estimateItemRespDTO.getEstimateInfo()))
                    .setTData(estimateItemRespDTO.getEstimateSkuGuids());
        }
        if (estimateItemRespDTO.getResult()) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        log.warn("性能测试：订单guid：" + billPayReqDTO.getOrderGuid() + "app返回：" + System.currentTimeMillis());
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "聚合支付", notes = "聚合支付")
    @PostMapping("/agg_pay")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "聚合支付", action = OperatorType.SELECT)
    public Result aggPay(@RequestBody BillAggPayReqDTO billPayReqDTO) {
        log.info("聚合支付入参：{}", JacksonUtils.writeValueAsString(billPayReqDTO));
        BillAggPayRespDTO billAggPayRespDTO = dineInBillClientService.aggPay(billPayReqDTO);
        if (billAggPayRespDTO.getEstimate() != null && billAggPayRespDTO.getEstimate()) {
            return Result.buildFailResult(ResultCodeEnum.ITEM_ESTIMATE_EXCEPTION.getCode(), LocaleMessageEnum.getLocale(billAggPayRespDTO.getEstimateInfo()))
                    .setTData(billAggPayRespDTO);
        }
        if (billAggPayRespDTO.getResult()) {
            return Result.buildSuccessResult(billAggPayRespDTO);
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));

    }


    @ApiOperation(value = "订单结账完成", notes = "订单结账完成")
    @PostMapping("/checkout/success")
    @EFKOperationLogAop(PLATFORM = Platform.CASHIERSYSTEM, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE,
            description = "订单结账完成", action = OperatorType.UPDATE)
    public Result<Void> orderCheckoutSuccess(@RequestBody BillPayReqDTO billPayReqDTO) {
        log.info("订单结账完成入参:{}", JacksonUtils.writeValueAsString(billPayReqDTO));
        dineInBillClientService.orderCheckoutSuccess(billPayReqDTO);
        return Result.buildEmptySuccess();
    }


    @ApiOperation(value = "反结账", notes = "反结账")
    @PostMapping("/recovery")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "反结账", action = OperatorType.SELECT)
    public Result<String> recovery(@RequestBody RecoveryReqDTO recoveryReqDTO) {
        log.info("反结账入参：{}", JacksonUtils.writeValueAsString(recoveryReqDTO));
        // 上传授权图片
        recoveryReqDTO.setAuthStaffPicture(upload(recoveryReqDTO.getAuthStaffPicture()));
        return Result.buildSuccessResult(dineInBillClientService.recovery(recoveryReqDTO));
    }

    @ApiOperation(value = "订单反结账是否超时", notes = "订单反结账是否超时")
    @PostMapping("/recovery/time_limit")
    public Result<Boolean> recoveryTimeLimit(@RequestBody RecoveryReqDTO recoveryReqDTO) {
        log.info("订单反结账是否超时入参：{}", JacksonUtils.writeValueAsString(recoveryReqDTO));
        return Result.buildSuccessResult(dineInBillClientService.recoveryTimeLimit(recoveryReqDTO));
    }

    @ApiOperation(value = "调整订单商品", notes = "调整订单商品")
    @PostMapping("/adjust")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "调整订单商品", action = OperatorType.SELECT)
    public Result<String> adjust(@RequestBody AdjustOrderReqDTO adjustOrderReqDTO) {
        log.info("调整订单商品入参：{}", JacksonUtils.writeValueAsString(adjustOrderReqDTO));
        return Result.buildSuccessResult(dineInBillClientService.adjust(adjustOrderReqDTO));

    }

    @ApiOperation(value = "校验是否有聚合支付退款", notes = "校验是否有聚合支付退款")
    @PostMapping("/validat_agg_refund")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE,description = "校验是否有聚合支付退款",action = OperatorType.SELECT)
    public Result<Boolean> validatAggRefund(@RequestBody ValidatAggReturnReqDTO validatAggReturnReqDTO) {
        log.info("校验是否有聚合支付退款入参：{}", JacksonUtils.writeValueAsString(validatAggReturnReqDTO));
        return Result.buildSuccessResult(dineInBillClientService.validatAggRefund(validatAggReturnReqDTO));
    }

    @ApiOperation(value = "退款", notes = "退款")
    @PostMapping("/refund")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "退款", action = OperatorType.SELECT)
    public Result refund(@RequestBody RefundReqDTO refundReqDTO) {
        log.info("退款入参：{}", JacksonUtils.writeValueAsString(refundReqDTO));
        if (dineInBillClientService.refund(refundReqDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));

    }

    @ApiOperation(value = "附加费列表", notes = "附加费列表")
    @PostMapping("/append_fee_list")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE,description = "附加费列表",action = OperatorType.SELECT)
    public Result<List<AppendFeeDetailDTO>> appendFeeList(@RequestBody SingleDataDTO singleDataDTO) {
        return Result.buildSuccessResult(dineInBillClientService.appendFeeList(singleDataDTO));

    }

    /**
     * 随行红包使用设置
     *
     * @param updateOrderReqDTO 订单guid
     * @return 结果
     */
    @ApiOperation(value = "随行红包使用设置")
    @PostMapping("/set_follow_red_packet")
    public Result<Boolean> setFollowRedPacket(@RequestBody UpdateOrderReqDTO updateOrderReqDTO) {
        log.info("随行红包使用设置 入参={}", JacksonUtils.writeValueAsString(updateOrderReqDTO));
        if (dineInOrderClientService.setFollowRedPacket(updateOrderReqDTO)) {
            return Result.buildEmptySuccess();
        }
        return Result.buildOpFailedResult("随行红包使用设置失败");
    }

    /**
     * 根据订单查询订单状态
     *
     * @param orderGuid 订单guid
     * @return 订单状态 (-1：未查询到订单 1：未结账， 2：已结账， 3：已退款，4：已作废)
     */
    @ApiOperation(value = "根据订单查询订单状态")
    @GetMapping("/get_order_state_by_guid")
    public Integer getOrderStateByGuid(@RequestParam("orderGuid") String orderGuid) {
        return dineInOrderClientService.getOrderStateByGuid(orderGuid);
    }

    /**
     * 根据订单查询订单金额
     *
     * @param orderGuid 订单guid
     * @return 订单金额
     */
    @ApiOperation(value = "根据订单查询订单金额")
    @GetMapping("/get_order_fee_by_guid")
    public BigDecimal getOrderFeeByGuid(@RequestParam("orderGuid") String orderGuid) {
        return dineInOrderClientService.getOrderFeeByGuid(orderGuid);
    }


    private String upload(String recoveryAuthStaffPicture) {
        if (StringUtils.isBlank(recoveryAuthStaffPicture)) {
            return Strings.EMPTY;
        }
        FileDto fileDto = new FileDto();
        fileDto.setFileContent(recoveryAuthStaffPicture);
        fileDto.setFileName(UUID.randomUUID().toString().replace("-", ""));
        return baseService.upload(fileDto);
    }

    @ApiOperation(value = "多次支付")
    @PostMapping("/multiple_pay")
    public Result<BillAggPayRespDTO> multiplePay(@RequestBody BillMultiplePayReqDTO multiplePayReqDTO) {
        log.info("多次支付入参：{}", JacksonUtils.writeValueAsString(multiplePayReqDTO));
        return Result.buildSuccessResult(dineInOrderClientService.multiplePay(multiplePayReqDTO));
    }

    @ApiOperation(value = "聚合支付部分退款")
    @PostMapping("/multiple_refund_agg_pay")
    public Result<BillRefundAggPayRespDTO> multipleRefundAggPay(@RequestBody BillMultipleRefundAggPayReqDTO multipleRefundAggPayReqDTO) {
        log.info("聚合支付部分退款入参：{}", JacksonUtils.writeValueAsString(multipleRefundAggPayReqDTO));
        return Result.buildSuccessResult(dineInOrderClientService.multipleRefundAggPay(multipleRefundAggPayReqDTO));
    }

    @ApiOperation(value = "启动手动清台处理")
    @PostMapping("/deal_enable_handle_close")
    public Result<OrderTableBillDTO> dealEnableHandleClose(@RequestBody BillPayReqDTO billPayReqDTO) {
        log.info("启动手动清台处理入参：{}", JacksonUtils.writeValueAsString(billPayReqDTO));
        OrderTableBillDTO orderTableBillDTO = new OrderTableBillDTO();
        orderTableBillDTO.setOrderGuid(Strings.EMPTY);
        orderTableBillDTO.setTableGuid(Strings.EMPTY);
        orderTableBillDTO.setOrderTableType(0);
        return Result.buildSuccessResult(orderTableBillDTO);
    }

}
