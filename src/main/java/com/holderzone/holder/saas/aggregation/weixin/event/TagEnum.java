package com.holderzone.holder.saas.aggregation.weixin.event;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum TagEnum {
	IS_BAST(-3,"isBestseller"),
	IS_NEW(-2,"isNew"),
	IS_SIGN(-1, "isSign");

	private Integer code;
	private String desc;

	public static int getCode(String desc){
		TagEnum[] values = values();
		for (TagEnum tagEnum : values) {
			if (tagEnum.getDesc().equals(desc)) {
				return tagEnum.getCode();
			}
		}
		return 0;
	}
}
