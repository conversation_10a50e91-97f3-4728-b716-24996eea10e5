package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.dto.account.request.MemberDataStatisticsQueryReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberDataStatisticsRespDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;


@Component
@FeignClient(name = "holder-saas-member-account", fallbackFactory = HsmMemberDataStatisticsClientService.HsmMemberDataStatisticsClientServiceFallback.class)
public interface HsmMemberDataStatisticsClientService {

    @RequestMapping(value = "hsm-member-data-statistics/query_by_condition", produces = "application/json;charset=utf-8",
            method = RequestMethod.POST)
    Page<MemberDataStatisticsRespDTO> queryMemberDataByCondition(MemberDataStatisticsQueryReqDTO memberDataStatisticsQueryReqDTO);

    @Component
    class HsmMemberDataStatisticsClientServiceFallback implements
            FallbackFactory<HsmMemberDataStatisticsClientService> {

        private static final Logger LOGGER = LoggerFactory
                .getLogger(HsmMemberDataStatisticsClientServiceFallback.class);

        @Override
        public HsmMemberDataStatisticsClientService create(Throwable throwable) {
            return new HsmMemberDataStatisticsClientService() {

                @Override
                public Page<MemberDataStatisticsRespDTO> queryMemberDataByCondition(MemberDataStatisticsQueryReqDTO memberDataStatisticsQueryReqDTO) {
                    LOGGER.error("根据条件精准查询某企业下所有体系的会员错误:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }
}
