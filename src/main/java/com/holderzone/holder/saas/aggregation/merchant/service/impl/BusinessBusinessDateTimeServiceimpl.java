package com.holderzone.holder.saas.aggregation.merchant.service.impl;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.aggregation.merchant.service.BusinessDateTimeService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.organization.OrganizationService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.user.UserFeignService;
import com.holderzone.saas.store.dto.journaling.req.BusinessDateTimeReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.BusinessDateTimeRespDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.user.UserSpinnerDTO;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BusinessBusinessDateTimeServiceimpl implements BusinessDateTimeService {

    private final OrganizationService organizationService;

    private final UserFeignService userFeignService;

    public BusinessBusinessDateTimeServiceimpl(OrganizationService organizationService, UserFeignService userFeignService) {
        this.organizationService = organizationService;
        this.userFeignService = userFeignService;
    }

    @Override
    public BusinessDateTimeRespDTO getBusinessDateTime(BusinessDateTimeReqDTO businessDateTimeReqDTO) {
        BusinessDateTimeRespDTO businessDateTimeRespDTO = new BusinessDateTimeRespDTO();
        //获取有权限的门店信息
        UserSpinnerDTO userSpinnerDTO = userFeignService.queryStoreSpinner();
        List<StoreDTO> arrayOfStoreDTO = userSpinnerDTO.getArrayOfStoreDTO();
        List<String> storeGuids = arrayOfStoreDTO.stream().map(StoreDTO::getGuid).collect(Collectors.toList());
        if (storeGuids.size() == 0) {
            throw new BusinessException("该账号所在商户未绑定门店信息！");
        }
        processDateTime(arrayOfStoreDTO,businessDateTimeReqDTO,businessDateTimeRespDTO);
        return businessDateTimeRespDTO;
    }

    @Override
    public BusinessDateTimeRespDTO getBusinessDateTimeByStoreGuids(BusinessDateTimeReqDTO businessDateTimeReqDTO) {
        BusinessDateTimeRespDTO businessDateTimeRespDTO = new BusinessDateTimeRespDTO();
        //根据门店guid集合查询出门店集合
        List<String> storeGuids = businessDateTimeReqDTO.getStoreGuids();
        if (!Optional.ofNullable(storeGuids).isPresent() || storeGuids.size() == 0) {
            throw new BusinessException("传入门店GUID为空！");
        }
        List<StoreDTO> storeDTOs = organizationService.queryStoreByGuidList(storeGuids);
        processDateTime(storeDTOs,businessDateTimeReqDTO,businessDateTimeRespDTO);
        return businessDateTimeRespDTO;
    }

    private static void processDateTime(List<StoreDTO> storeDTOs,BusinessDateTimeReqDTO businessDateTimeReqDTO,BusinessDateTimeRespDTO businessDateTimeRespDTO){
        //获取门店营业时间 fixBug:避免为空
        LocalTime businessStart = Optional.ofNullable(storeDTOs.get(0).getBusinessStart()).orElse(LocalTime.MIN);
        LocalTime businessEnd = Optional.ofNullable(storeDTOs.get(0).getBusinessEnd()).orElse(LocalTime.MAX);
        // 1.如果是单门店，报表按照设置时间默认展示
        // 2.如果是多门店，报表默认时间展示0点-23:59:59
        if (storeDTOs.size() > 1) {
            businessStart = LocalTime.MIN;
            businessEnd = LocalTime.MAX;
        }
        //设置查询日期，约定顺序为（1，7，30）今天，近7天，近30天
        LocalDate startDate =LocalDate.now();
        LocalDate startDate7 = LocalDate.now().minusDays(businessDateTimeReqDTO.getDateTypes().get(1)).plusDays(1);
        LocalDate startDate30 = LocalDate.now().minusDays(businessDateTimeReqDTO.getDateTypes().get(2)).plusDays(1);
        LocalDate endDate = LocalDate.now();

        //拼接日期和营业时间
        LocalDateTime startDateTime = spliceStartDateTime(startDate,businessStart,businessEnd);
        LocalDateTime startDateTime7 = spliceStartDateTime(startDate7,businessStart,businessEnd);
        LocalDateTime startDateTime30 = spliceStartDateTime(startDate30,businessStart,businessEnd);
        //开始日期时间list
        ArrayList<LocalDateTime> startDateTimes = new ArrayList<>();
        startDateTimes.add(startDateTime);
        startDateTimes.add(startDateTime7);
        startDateTimes.add(startDateTime30);
        businessDateTimeRespDTO.setBusinessStartDateTimes(startDateTimes);

        LocalDateTime endDateTime = spliceEndDateTime(endDate,businessStart,businessEnd);
        // 如果今天的开始时间和结束时间相同，则讲结束时间+1天
        if (startDateTime.compareTo(endDateTime) == 0) {
            endDateTime = endDateTime.plusDays(1);
        }
        businessDateTimeRespDTO.setBusinessEndDateTime(endDateTime);
    }

    /**
     * 拼接开始日期时间
     * 跨天处理：开始时间大于结束时间 且现在的时间 小于开始时间，则开始日期减去一天
     * @param startDate 开始日期
     * @param businessStart 营业开始时间
     * @param businessEnd 营业结束时间
     * @return 开始日期时间
     */
    private static LocalDateTime spliceStartDateTime(LocalDate startDate,LocalTime businessStart,LocalTime businessEnd){
        //跨天处理
        if (businessStart.compareTo(businessEnd)>0 && LocalTime.now().compareTo(businessStart)<0){
            return startDate.minusDays(1).atTime(businessStart);
        }
        return startDate.atTime(businessStart);
    }

    /**
     * 拼接开始日期时间
     * 跨天处理：开始时间大于结束时间 且现在的时间 大于开始时间，则结束日期加上一天
     * @param endDate 结束日期
     * @param businessStart 营业开始时间
     * @param businessEnd 营业结束时间
     * @return 结束日期时间
     */
    private static LocalDateTime spliceEndDateTime(LocalDate endDate,LocalTime businessStart,LocalTime businessEnd){
        //跨天处理
        if (businessStart.compareTo(businessEnd)>0 && LocalTime.now().compareTo(businessStart)>0){
            return endDate.plusDays(1).atTime(businessEnd);
        }
        return endDate.atTime(businessEnd);
    }
}
