package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Collections;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@ApiModel("预订区域")
public class ReserveAreaRespDTO {

	@ApiModelProperty(value = "区域id")
	private String areaGuid;

	@ApiModelProperty(value = "区域名称")
	private String areaName;

	@ApiModelProperty(value = "该区域下，桌台列表，可能为空")
	private List<ReserveTableRespDTO> reserveTableRespDTOS=Collections.emptyList();

	@ApiModelProperty(value = "区域下桌台人数列表,没有桌台则为空数组")
	private List<Integer> areaTableSeats= Collections.emptyList();
	@ApiModelProperty(value = "区域下桌台标签列表,没有桌台则为空数组")
	private List<String> areaTableTags = Collections.emptyList();
}
