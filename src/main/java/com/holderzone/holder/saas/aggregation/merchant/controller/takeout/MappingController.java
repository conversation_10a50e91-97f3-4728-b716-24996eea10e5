package com.holderzone.holder.saas.aggregation.merchant.controller.takeout;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.organization.OrganizationService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.takeout.TakeoutConsumerService;
import com.holderzone.holder.saas.aggregation.merchant.util.MaterialExcelUtil;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutItemMappingReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.TakeoutItemMappingRespDTO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.util.Asserts;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@RestController
@RequestMapping("/item_mapping")
public class MappingController {

    private final static Integer CURRENT_PAGE = 1;

    private final static Integer PAGE_SIZE = 99999;

    private final TakeoutConsumerService takeoutConsumerService;

    private final OrganizationService organizationService;

    @Autowired
    public MappingController(TakeoutConsumerService takeoutConsumerService, OrganizationService organizationService) {
        this.takeoutConsumerService = takeoutConsumerService;
        this.organizationService = organizationService;
    }

    @PostMapping("/query")
    @ApiOperation(value = "查询平台商品列表", notes = "查询平台商品列表")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "查询平台商品列表")
    public Result<TakeoutItemMappingRespDTO> getItemBinding(@RequestBody @Validated TakeoutItemMappingReqDTO takeoutItemMappingReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询平台商品列表：入参{}", JacksonUtils.writeValueAsString(takeoutItemMappingReqDTO));
        }
        if (takeoutItemMappingReqDTO.getTakeoutType() == 2) {
            log.info("查询掌控者商品列表");
            return Result.buildSuccessResult(takeoutConsumerService.getOwnItemBinding(takeoutItemMappingReqDTO));
        } else {
            log.info("查询其他平台商品列表");
            return Result.buildSuccessResult(takeoutConsumerService.getItemBinding(takeoutItemMappingReqDTO));
        }
    }

    @PostMapping("/query_items")
    @ApiOperation(value = "批量查询平台商品列表", notes = "批量查询平台商品列表")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "批量查询平台商品列表")
    public Result<TakeawayBatchMappingResult> getItems(@RequestBody @Validated UnItemQueryReq unItemQueryReq) {
        if (log.isInfoEnabled()) {
            log.info("批量查询平台商品列表：入参{}", JacksonUtils.writeValueAsString(unItemQueryReq));
        }
        return Result.buildSuccessResult(getTakeawayBatchMappingResult(unItemQueryReq));
    }

    private TakeawayBatchMappingResult getTakeawayBatchMappingResult(UnItemQueryReq unItemQueryReq) {
        if (CollectionUtils.isEmpty(unItemQueryReq.getStoreGuids())) {
            return TakeawayBatchMappingResult.buildEmptyResult(unItemQueryReq.getCurrentPage(),
                    unItemQueryReq.getPageSize());
        }
        TakeawayBatchMappingResult result = takeoutConsumerService.getItems(unItemQueryReq);
        List<UnMappedItem> itemList = result.getPageInfo().getData();
        if (CollectionUtils.isNotEmpty(itemList)) {
            // 查询门店名称
            Set<String> storeGuids = itemList.stream().map(UnMappedItem::getErpStoreGuid).collect(Collectors.toSet());
            List<StoreDTO> storeList = organizationService.queryStoreAndBrandByIdList(new ArrayList<>(storeGuids));
            Map<String, StoreDTO> storeMap = storeList.stream()
                    .collect(Collectors.toMap(StoreDTO::getGuid, Function.identity(), (key1, key2) -> key2));
            for (UnMappedItem unMappedItem : itemList) {
                unMappedItem.setErpStoreName(storeMap.getOrDefault(unMappedItem.getErpStoreGuid(), new StoreDTO()).getName());
                unMappedItem.setBelongBrandName(storeMap.getOrDefault(unMappedItem.getErpStoreGuid(), new StoreDTO()).getBelongBrandName());
            }
        }
        return result;
    }

    @PostMapping("/export_takeaway_items")
    @ApiOperation("导出批量查询平台商品列表")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "导出批量查询平台商品列表")
    public void exportTakeawayItems(@RequestBody @Validated UnItemQueryReq unItemQueryReq) {
        log.info("[导出批量查询平台商品列表],unItemQueryReq={}", JacksonUtils.writeValueAsString(unItemQueryReq));
        unItemQueryReq.setCurrentPage(CURRENT_PAGE);
        unItemQueryReq.setPageSize(PAGE_SIZE);
        TakeawayBatchMappingResult result = getTakeawayBatchMappingResult(unItemQueryReq);
        List<UnMappedItem> mappedItemList = result.getPageInfo().getData();
        try {
            HttpServletResponse resp = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
            XSSFWorkbook xssfWorkbook = MaterialExcelUtil.exportTakeawayItems(mappedItemList, unItemQueryReq.getTakeoutType());
            String fileName = "外卖商品绑定";
            MaterialExcelUtil.exportFile(resp, xssfWorkbook, fileName);
        } catch (Exception e) {
            log.error("[导出批量查询平台商品列表]失败", e);
            throw new BusinessException("导出批量查询平台商品列表失败");
        }
    }

    @PostMapping("/update-bind-extend-info")
    @ApiOperation(value = "更新商品绑定拓展信息", notes = "更新商品绑定拓展信息")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "更新商品绑定拓展信息")
    public Result<Void> updateBindExtendInfo(@RequestBody JSONObject request) {
        String storeGuid = request.getString("storeGuid");
        String takeoutType = request.getString("takeoutType");
        UnMappedItem item = request.getObject("item", UnMappedItem.class);
        Asserts.notNull(item, "外卖商品信息不能为空");

        log.info("更新商品绑定拓展信息, sid: {}, tt: {}, with data: {}", storeGuid, takeoutType, JacksonUtils.writeValueAsString(item));
        if (item.getExtendInfoId() == null) {
            Asserts.notEmpty(storeGuid, "新建商品绑定拓展信息门店ID不能为空");
            Asserts.notEmpty(takeoutType, "新建商品绑定拓展信息外卖类型不能为空");
            Asserts.notEmpty(item.getErpItemSkuId(), "新建商品绑定拓展信息商品ID不能为空");
        }
        Asserts.notNull(item.getUnItemCountMapper(), "更新商品绑定拓展信息映射减扣数量不能为空");
        takeoutConsumerService.updateBindExtendInfo(storeGuid, takeoutType, item);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/bind")
    @ApiOperation(value = "设置商品映射", notes = "设置商品映射")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "设置商品映射")
    public Result<Void> bindItem(@RequestBody UnItemBindUnbindReq unItemBindUnbindReq) {
        if (log.isInfoEnabled()) {
            log.info("设置商品映射：入参{}", JacksonUtils.writeValueAsString(unItemBindUnbindReq));
        }
        takeoutConsumerService.bindItem(unItemBindUnbindReq);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "批量绑定外卖商品")
    @PostMapping("/batch_bind")
    public Result<Void> batchBind(@RequestBody UnItemBatchBindUnbindReq unItemBatchBindUnbindReq) {
        if (log.isInfoEnabled()) {
            log.info("[批量绑定外卖商品]入参={}", JacksonUtils.writeValueAsString(unItemBatchBindUnbindReq));
        }
        takeoutConsumerService.batchBind(unItemBatchBindUnbindReq);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/unbind")
    @ApiOperation(value = "删除商品映射", notes = "删除商品映射")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "删除商品映射")
    public Result<Void> unbindItem(@RequestBody UnItemBindUnbindReq unItemBindUnbindReq) {
        if (log.isInfoEnabled()) {
            log.info("删除商品映射：入参{}", JacksonUtils.writeValueAsString(unItemBindUnbindReq));
        }
        takeoutConsumerService.unbindItem(unItemBindUnbindReq);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "批量解绑外卖商品")
    @PostMapping("/batch_unbind_out")
    public Result<Void> batchUnbind(@RequestBody UnItemBatchBindUnbindReq batchBindUnbindReq) {
        if (log.isInfoEnabled()) {
            log.info("[批量解绑外卖商品]入参{}", JacksonUtils.writeValueAsString(batchBindUnbindReq));
        }
        takeoutConsumerService.batchUnbind(batchBindUnbindReq);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/batch_unbind")
    @ApiOperation(value = "批量删除商品映射", notes = "批量删除商品映射")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY, description = "批量删除商品映射")
    public Result<Void> batchUnbindItem(@RequestBody UnItemBatchUnbindReq unItemBatchUnbindReq) {
        if (log.isInfoEnabled()) {
            log.info("批量删除商品映射：入参{}", JacksonUtils.writeValueAsString(unItemBatchUnbindReq));
        }
        takeoutConsumerService.batchUnbindItem(unItemBatchUnbindReq);
        return Result.buildEmptySuccess();
    }
}
