package com.holderzone.holder.saas.aggregation.merchant.controller.rights;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.rights.RMemberRightsCouponClientService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description 会员权益优惠券关联表 前端控制器
 * @date 2019/5/20 16:56
 */
@RestController
@RequestMapping("/hsm-rmember-rights-coupon")
@Api(description = "会员权益优惠券关联表")
public class HsmRMemberRightsCouponController {
    @Autowired
    private RMemberRightsCouponClientService rMemberRightsCouponClientService;
    /**
     * @Description  根据guid删除
     * <AUTHOR>
     * @Date  2019/5/20 17:00
     * @param guid
     * @return org.xnio.Result
     */
    @DeleteMapping("/{guid}")
    @ApiOperation(value = "根据guid删除",notes = "根据guid删除")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "根据guid删除")
    public Result delByGuid(@ApiParam("guid") @PathVariable("guid") String guid){

        rMemberRightsCouponClientService.delByGuid(guid);
        return Result.buildEmptySuccess();

    }
}
