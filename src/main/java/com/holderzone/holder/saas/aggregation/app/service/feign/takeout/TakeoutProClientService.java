package com.holderzone.holder.saas.aggregation.app.service.feign.takeout;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.takeaway.OwnCallbackResponse;
import com.holderzone.saas.store.dto.takeaway.TakeoutAutoRcvDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderDTO;
import com.holderzone.saas.store.dto.takeaway.request.SalesUpdateDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutTCDConfirmTheMealDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutTCDDiningOutDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutTCDPickUpDTO;
import com.holderzone.saas.store.dto.takeaway.response.OwnDistributionDTO;
import com.holderzone.saas.store.dto.takeaway.response.StoreAuthDTO;
import com.holderzone.saas.store.dto.takeaway.response.TcdCommonRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeawayClientService
 * @date 2018/09/08 11:15
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-takeaway-producer", fallbackFactory = TakeoutProClientService.ServiceFallBack.class)
public interface TakeoutProClientService {

    @PostMapping("/own/go_shipping")
    OwnCallbackResponse goShipping(@RequestBody TakeoutOrderDTO takeoutOrderDTO);

    @PostMapping("/own/done_shipping")
    OwnCallbackResponse doneShipping(@RequestBody TakeoutOrderDTO takeoutOrderDTO);

    @PostMapping("/own/cancel_shipping")
    OwnCallbackResponse cancelShipping(@RequestBody TakeoutOrderDTO takeoutOrderDTO);

    @PostMapping("/tcd/dining_out_tcd")
    TcdCommonRespDTO diningOutTcd(@RequestBody TakeoutTCDDiningOutDTO takeoutTCDDiningOutDTO);

    @PostMapping("/tcd/confirm_the_meal_tcd")
    TcdCommonRespDTO confirmTheMealTcd(@RequestBody TakeoutTCDConfirmTheMealDTO takeoutTCDConfirmTheMealDTO);

    @PostMapping("/tcd/pick_up_tcd")
    TcdCommonRespDTO pickUpTcd(@RequestBody TakeoutTCDPickUpDTO takeoutTCDPickUpDTO);

    @GetMapping("/group/list/{storeGuid}")
    List<StoreAuthDTO> queryStoreAuth(@PathVariable("storeGuid") String storeGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<TakeoutProClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public TakeoutProClientService create(Throwable cause) {
            return new TakeoutProClientService() {

                @Override
                public OwnCallbackResponse goShipping(TakeoutOrderDTO takeoutOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "goShipping", JacksonUtils.writeValueAsString(takeoutOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public OwnCallbackResponse doneShipping(TakeoutOrderDTO takeoutOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "doneShipping", JacksonUtils.writeValueAsString(takeoutOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public OwnCallbackResponse cancelShipping(TakeoutOrderDTO takeoutOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "cancelShipping", JacksonUtils.writeValueAsString(takeoutOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TcdCommonRespDTO diningOutTcd(TakeoutTCDDiningOutDTO takeoutTCDDiningOutDTO) {
                    log.error(HYSTRIX_PATTERN, "diningOutTcd", JacksonUtils.writeValueAsString(takeoutTCDDiningOutDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TcdCommonRespDTO confirmTheMealTcd(TakeoutTCDConfirmTheMealDTO takeoutTCDConfirmTheMealDTO) {
                    log.error(HYSTRIX_PATTERN, "confirmTheMealTcd", JacksonUtils.writeValueAsString(takeoutTCDConfirmTheMealDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TcdCommonRespDTO pickUpTcd(TakeoutTCDPickUpDTO takeoutTCDPickUpDTO) {
                    log.error(HYSTRIX_PATTERN, "pickUpTcd", JacksonUtils.writeValueAsString(takeoutTCDPickUpDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<StoreAuthDTO> queryStoreAuth(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "query", storeGuid, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }
    }
}
