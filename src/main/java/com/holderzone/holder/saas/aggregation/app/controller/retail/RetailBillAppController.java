package com.holderzone.holder.saas.aggregation.app.controller.retail;


import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.constant.Constant;
import com.holderzone.holder.saas.aggregation.app.service.feign.retail.RetailBillClientService;
import com.holderzone.saas.store.dto.retail.bill.request.*;
import com.holderzone.saas.store.dto.retail.bill.response.RetailAggPayRespDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderDetailRespDTO;
import com.holderzone.saas.store.enums.common.ResultCodeEnum;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInBillAppController
 * @date 2018/09/04 11:26
 * @description app聚合层账单接口
 * @program holder-saas-aggregation-app
 */
@RestController
@RequestMapping("/retail_bill")
@Api(description = "零售订单结算接口")
@Slf4j
public class RetailBillAppController {

    private final RetailBillClientService dineInBillClientService;

    @Autowired
    public RetailBillAppController(RetailBillClientService dineInBillClientService) {
        this.dineInBillClientService = dineInBillClientService;
    }

    @ApiOperation(value = "计算账单优惠", notes = "计算账单优惠")
    @PostMapping("/calculate")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "计算账单优惠")
    public Result<RetailOrderDetailRespDTO> calculate(@RequestBody RetailCalculateReqDTO retailCalculateReqDTO) {
        return Result.buildSuccessResult(dineInBillClientService.calculate(retailCalculateReqDTO));

    }

    @ApiOperation(value = "其他支付", notes = "其他支付")
    @PostMapping("/pay")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "其他支付")
    public Result pay(@RequestBody @Valid RetailPayReqDTO retailPayReqDTO) {
        Boolean falg = dineInBillClientService.pay(retailPayReqDTO);
        if (falg) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        log.warn("性能测试：订单guid：" + retailPayReqDTO.getOrderGuid() + "app返回：" + System.currentTimeMillis());
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "聚合支付", notes = "聚合支付")
    @PostMapping("/agg_pay")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "聚合支付")
    public Result<RetailAggPayRespDTO> aggPay(@RequestBody RetailAggPayReqDTO retailAggPayReqDTO) {
        RetailAggPayRespDTO retailAggPayRespDTO = dineInBillClientService.aggPay(retailAggPayReqDTO);
        /*
        if (retailAggPayRespDTO.getEstimate() != null && retailAggPayRespDTO.getEstimate()) {
            return Result.buildFailResult(ResultCodeEnum.ITEM_ESTIMATE_EXCEPTION.getCode(), "所选商品已达到估清剩余数量");
        }
        */
        if (retailAggPayRespDTO.getResult()) {
            return Result.buildSuccessResult(retailAggPayRespDTO);
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));

    }

    @ApiOperation(value = "校验是否有聚合支付退款", notes = "校验是否有聚合支付退款")
    @PostMapping("/validat_agg_refund")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "校验是否有聚合支付退款")
    public Result<Boolean> validatAggRefund(@RequestBody ValidatAggReturnReqDTO validatAggReturnReqDTO) {
        log.info("校验是否有聚合支付退款入参：{}", JacksonUtils.writeValueAsString(validatAggReturnReqDTO));
        return Result.buildSuccessResult(dineInBillClientService.validatAggRefund(validatAggReturnReqDTO));
    }

    @ApiOperation(value = "退款", notes = "退款")
    @PostMapping("/refund")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "退款")
    public Result refund(@RequestBody RefundReqDTO refundReqDTO) {
        log.info("退款入参：{}", JacksonUtils.writeValueAsString(refundReqDTO));
        if (dineInBillClientService.refund(refundReqDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));

    }

}
