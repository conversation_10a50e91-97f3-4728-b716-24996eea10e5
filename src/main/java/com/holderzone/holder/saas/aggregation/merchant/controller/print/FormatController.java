package com.holderzone.holder.saas.aggregation.merchant.controller.print;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.base.dto.file.FileDto;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.security.SecurityManager;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.BaseService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.print.PrintClientService;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.format.ImagePixelEnum;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@RestController
@Api("模板格式接口")
@RequestMapping(value = "/format")
public class FormatController {

    private final PrintClientService printClientService;

    private final BaseService baseService;

    @Autowired
    public FormatController(PrintClientService printLogoService, BaseService baseService) {
        this.printClientService = printLogoService;
        this.baseService = baseService;
    }

    @ApiOperation("图片规格需要满足：\n" +
            "图片格式：jpg，jpeg，png和bmp格式\n" +
            "图片要求：大小不超过500k，推荐300k，黑白图片\n" +
            "大小建议：不超过576*576，推荐386*386，阈值＞200，质量100\n" +
            "不符合规格要求的图片可以参照以下操作：" +
            "https://www.uupoop.com/ 网页中：图像-图像尺寸、调整-阈值、保存-质量100。" +
            "参考：http://wiki.pospal.cn/index.php?doc-view-440"
    )
    @PostMapping("/upload_image")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT,description = "图片上传")
    public Result<String> uploadImage(@RequestParam("file") MultipartFile multipartFile,
                                      @RequestParam("storeGuid") String storeGuid,
                                      @RequestParam("invoiceType") Integer invoiceType,
                                      @RequestParam("customType") Integer customType,
                                      @RequestParam("customIndex") Integer customIndex) {
        if (log.isInfoEnabled()) {
            log.info("图片上传，storeGuid={}，invoiceType={}，customType={}，customIndex={}",
                    storeGuid, invoiceType, customType, customIndex);
        }
        String suffix = resolveImageSuffix(multipartFile);
        validateImagePixel(multipartFile);
        String url = null;
        try {
            FileDto fileDto = new FileDto();
            fileDto.setFileName(imageKey(storeGuid, invoiceType, customType, customIndex, suffix));
            fileDto.setFileContent(SecurityManager.entryptBase64(multipartFile.getBytes()));
            url = baseService.upload(fileDto);
        } catch (IOException e) {
            log.error("上传logo文件发生异常：{}", ThrowableUtils.asString(e));
        }
        return Result.buildSuccessResult(url);
    }

    @PostMapping("/delete_image")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT,description = "删除图片")
    public Result<Void> deleteImage(@RequestParam("url") String url) {
        if (log.isInfoEnabled()) {
            log.info("删除图片，url={}", url);
        }
        if (StringUtils.isEmpty(url)) {
            throw new BusinessException("图片Url不得为空");
        }
        baseService.delete(url);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/delete_images")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT,description = "删除图片")
    public Result<Void> deleteImage(@RequestBody List<String> urls) {
        if (log.isInfoEnabled()) {
            log.info("删除图片，urls={}", JacksonUtils.writeValueAsString(urls));
        }
        if (CollectionUtils.isEmpty(urls)) {
            throw new BusinessException("图片Url不得为空");
        }
        for (String url : urls) {
            baseService.delete(url);
        }
        return Result.buildEmptySuccess();
    }

    @PostMapping("/add")
    @ApiOperation(value = "添加或修改单据格式")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT,description = "添加或修改单据格式")
    public Result<Void> addFormat(@RequestBody String formatDTO) {
        if (log.isInfoEnabled()) {
            log.info("添加或修改单据格式入参:{}", formatDTO);
        }
        printClientService.addFormat(formatDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/list")
    @ApiOperation(value = "查询单据格式列表")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT, description = "查询单据格式列表")
    public Result<List<FormatDTO>> listFormat(@RequestBody FormatDTO formatDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询单据格式列表入参:{}", JacksonUtils.writeValueAsString(formatDTO));
        }
        List<Object> formatJsonString = JacksonUtils.toObjectList(
                Object.class, printClientService.listFormat(formatDTO));
        return Result.buildSuccessResult(formatJsonString.stream()
                .map(s -> {
                    FormatDTO format = InvoiceTypeEnum.resolveFormatBy(JacksonUtils.writeValueAsString(s));
                    if (format.getName().equals(Constants.DEFAULT_TEMPLATE)) {
                        format.setName(LocaleUtil.getMessage("DEFAULT_TEMPLATE"));
                    }
                    return format;
                })
                .collect(Collectors.toList()));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除单据格式")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT,description = "删除单据格式")
    public Result<Void> deleteFormat(@RequestBody FormatDTO formatDTO) {
        if (log.isInfoEnabled()) {
            log.info("删除单据格式入参:{}", JacksonUtils.writeValueAsString(formatDTO));
        }
        printClientService.deleteFormat(formatDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/enable")
    @ApiOperation(value = "启用单据格式")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT,description = "启用单据格式")
    public Result<Void> enableFormat(@RequestBody FormatDTO formatDTO) {
        if (log.isInfoEnabled()) {
            log.info("启用单据格式入参:{}", JacksonUtils.writeValueAsString(formatDTO));
        }
        printClientService.enableFormat(formatDTO);
        return Result.buildEmptySuccess();
    }

    private String resolveImageSuffix(MultipartFile multipartFile) {
        String fileName = multipartFile.getOriginalFilename();
        String suffix = (fileName != null && fileName.contains("."))
                ? fileName.substring(fileName.lastIndexOf(".") + 1)
                : null;
        if (!Objects.equals(suffix, "jpg")
                && !Objects.equals(suffix, "jpeg")
                && !Objects.equals(suffix, "png")
                && !Objects.equals(suffix, "bmp")) {
            log.error("文件名异常，fileName：{}", fileName);
            throw new BusinessException("文件格式必须为 jpg/jpeg/png/bmp 格式！！！");
        }
        return suffix;
    }

    private void validateImagePixel(MultipartFile multipartFile) {
        BufferedImage bufferedImage;
        try {
            bufferedImage = ImageIO.read(multipartFile.getInputStream());
        } catch (IOException e) {
            log.error("图片读取出错，width：{}", ThrowableUtils.asString(e));
            throw new BusinessException("图片读取出错");
        }
        long size = multipartFile.getSize();
        int width = bufferedImage.getWidth();
        int height = bufferedImage.getHeight();

        ImagePixelEnum maxImagePixel = ImagePixelEnum.MAX_80;

        long maxSize = maxImagePixel.getSize();
        if (size > maxSize) {
            log.error("文件大小异常，size：{}", size);
            throw new BusinessException("文件大小不得超过" + maxSize / 1000 + "k");
        }

        int maxWidth = maxImagePixel.getWidth();
        int maxHeight = maxImagePixel.getHeight();
        if (width > maxWidth || height > maxHeight) {
            log.error("文件尺寸异常，width：{}，height：{}", width, height);
            throw new BusinessException("文件大小不得超过" + width + "*" + height);
        }
    }

    private static String getRandomFileName() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 5);
    }

    private String imageKey(String storeGuid, Integer invoiceType, Integer customType, Integer index, String suffix) {
        String type = customType == 0 ? "header" : "footer";
        long timestamp = System.currentTimeMillis();
        return storeGuid + "_" + invoiceType + "_" + type + "_" + timestamp + "." + suffix;
    }
}
