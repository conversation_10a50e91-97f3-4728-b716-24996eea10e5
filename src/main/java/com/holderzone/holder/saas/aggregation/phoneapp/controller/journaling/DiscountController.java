package com.holderzone.holder.saas.aggregation.phoneapp.controller.journaling;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.phoneapp.service.rpc.JournalRpcService;
import com.holderzone.saas.store.dto.journaling.req.BusinessSituationReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.DiscountSituationDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DiscountController
 * @date 2019/06/03 14:55
 * @description 优惠统计Controller
 * @program holder-saas-store
 */
@RestController
@Slf4j
@RequestMapping("/discount")
@Api("优惠Controller")
public class DiscountController {

    @Autowired
    JournalRpcService journalRpcService;

    @PostMapping("/list")
    @ApiOperation(value = "查询优惠信息")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_REPORT, description = "查询优惠信息")
    public Result<DiscountSituationDTO> listDiscountSituation(@RequestBody BusinessSituationReqDTO businessSituationReqDTO) {
        log.info("优惠信息统计查询入参:{}", JacksonUtils.writeValueAsString(businessSituationReqDTO));
        DiscountSituationDTO data = journalRpcService.listDiscountSituation(businessSituationReqDTO);
        log.info("优惠统计查询结果：{}", JacksonUtils.writeValueAsString(data));
        return Result.buildSuccessResult(data);
    }
}
