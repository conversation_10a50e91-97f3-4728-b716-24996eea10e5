package com.holderzone.holder.saas.aggregation.merchant.domain.user;

import com.holderzone.resource.common.dto.user.UserDTO;
import com.holderzone.saas.store.dto.store.store.OrganizeDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className UserAllInfo
 * @date 18-9-10 下午2:31
 * @description 员工相关信息DTO（包括门店）
 * @program holder-saas-store-staff
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UserAllInfoDTO extends UserDTO {
    @ApiModelProperty("用户创建的门店信息数组")
    private List<OrganizeDTO> organizeDTOS;
}
