package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.app.service.OrderDetailsService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInOrderClientService;
import com.holderzone.holder.saas.aggregation.app.transform.BossTransform;
import com.holderzone.saas.store.dto.boss.req.BossOrderItemQueryDTO;
import com.holderzone.saas.store.dto.boss.resp.BossOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.OrderDetailQueryDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnItemDTO;
import com.holderzone.saas.store.dto.trade.OrderInfoRespDTO;
import com.mos.secure.ext.annotations.Desensitization;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class OrderDetailsServiceImpl implements OrderDetailsService {

    private final DineInOrderClientService dineInOrderClientService;

    public OrderDetailsServiceImpl(DineInOrderClientService dineInOrderClientService) {
        this.dineInOrderClientService = dineInOrderClientService;
    }

    @Desensitization
    public OrderInfoRespDTO sensitization(OrderInfoRespDTO orderInfoRespDTO) {
        return orderInfoRespDTO;
    }

    /**
     * 老板助手
     * 查询订单商品详情
     */
    @Override
    public BossOrderDetailRespDTO queryOrderItemInfo(BossOrderItemQueryDTO queryDTO) {
        OrderDetailQueryDTO dataDTO = new OrderDetailQueryDTO();
        dataDTO.setData(queryDTO.getOrderGuid());
        dataDTO.setEnterpriseGuid(queryDTO.getEnterpriseGuid());
        DineinOrderDetailRespDTO dine = dineInOrderClientService.getOrderDetailForWx(dataDTO);
        // 合并多单结账的商品
        mergeDineOrderDetailRespDTO(dine);
        return BossTransform.INSTANCE.dineOrderDetail2BossOrderDetail(dine);
    }

    private void mergeDineOrderDetailRespDTO(DineinOrderDetailRespDTO dine) {
        List<DineinOrderDetailRespDTO> otherOrderDetails = dine.getOtherOrderDetails();
        if (CollectionUtils.isEmpty(otherOrderDetails)) {
            return;
        }
        List<DineInItemDTO> dineInItemDTOS = Optional.ofNullable(dine.getDineInItemDTOS()).orElse(Lists.newArrayList());
        List<ReturnItemDTO> returnItemDTOS = Optional.ofNullable(dine.getReturnItemDTOS()).orElse(Lists.newArrayList());
        List<String> remarks = Lists.newArrayList();
        if (!StringUtils.isEmpty(dine.getRemark())) {
            remarks.add(dine.getRemark());
        }
        for (DineinOrderDetailRespDTO otherOrderDetail : otherOrderDetails) {
            List<DineInItemDTO> otherDineInItemDTOS = otherOrderDetail.getDineInItemDTOS();
            if (CollectionUtils.isNotEmpty(otherDineInItemDTOS)) {
                dineInItemDTOS.addAll(otherDineInItemDTOS);
            }
            List<ReturnItemDTO> otherReturnItemDTOS = otherOrderDetail.getReturnItemDTOS();
            if (CollectionUtils.isNotEmpty(otherReturnItemDTOS)) {
                returnItemDTOS.addAll(otherReturnItemDTOS);
            }
            if (!StringUtils.isEmpty(otherOrderDetail.getRemark())) {
                remarks.add(otherOrderDetail.getRemark());
            }
        }
        dine.setDineInItemDTOS(dineInItemDTOS);
        dine.setReturnItemDTOS(returnItemDTOS);
        dine.setRemark(org.apache.commons.lang3.StringUtils.join(remarks, "、"));
    }

}
