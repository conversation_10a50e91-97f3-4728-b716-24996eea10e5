package com.holder.saas.print.event;

import com.alibaba.fastjson.JSON;
import com.holder.saas.print.config.RocketMqConfig;
import com.holder.saas.print.service.PrintRecordService;
import com.holder.saas.print.utils.ThrowableExtUtils;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
@RocketListenerHandler(topic = RocketMqConfig.PRINT_RECORD_REMOVE_TOPIC,
        tags = RocketMqConfig.PRINT_RECORD_REMOVE_TAG,
        consumerGroup = RocketMqConfig.PRINT_RECORD_REMOVE_GROUP)
public class PrintRecordRemoveListener extends AbstractRocketMqConsumer<RocketMqTopic, String> {

    @Resource
    private PrintRecordService printRecordService;

    @Override
    public boolean consumeMsg(String recordGuidJson, MessageExt messageExt) {
        try {
            UserContextUtils.put(messageExt.getProperty(RocketMqConfig.MESSAGE_CONTEXT));
            EnterpriseIdentifier.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            List<String> recordGuidList = JSON.parseArray(recordGuidJson, String.class);
            log.info("批量删除打印记录，recordGuidList={}", recordGuidJson);
            printRecordService.batchDeleteRecord(recordGuidList);
        } catch (Exception e) {
            log.error("删除打印记录消息消费异常，throwable={}", ThrowableExtUtils.asStringIfAbsent(e));
        } finally {
            UserContextUtils.remove();
            EnterpriseIdentifier.remove();
        }
        return true;
    }
}
