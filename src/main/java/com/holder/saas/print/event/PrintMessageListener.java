package com.holder.saas.print.event;

import com.holder.saas.print.config.RocketMqConfig;
import com.holder.saas.print.service.PrintRecordService;
import com.holder.saas.print.utils.PrintLogUtils;
import com.holder.saas.print.utils.ThrowableExtUtils;
import com.holder.saas.print.utils.valid.RecordValidUtils;
import com.holderzone.feign.spring.boot.util.MdcContextUtils;
import com.holderzone.feign.spring.boot.util.TraceContextUtils;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/09/18 20:47
 * @description
 * @program holder-saas-store-takeaway
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.PRINT_MESSAGE_TOPIC,
        tags = RocketMqConfig.PRINT_MESSAGE_TAG,
        consumerGroup = RocketMqConfig.PRINT_MESSAGE_GROUP)
public class PrintMessageListener extends AbstractRocketMqConsumer<RocketMqTopic, String> {

    private final PrintRecordService printRecordService;

    @Autowired
    public PrintMessageListener(PrintRecordService printRecordService) {
        this.printRecordService = printRecordService;
    }

    @Override
    public boolean consumeMsg(String json, MessageExt messageExt) {
        try {
            PrintDTO printDTO = InvoiceTypeEnum.resolvePrintBy(json);
            TraceContextUtils.setTraceId(messageExt.getMsgId());
            UserContextUtils.putOrByErpAndStore(
                    messageExt.getProperty(RocketMqConfig.MESSAGE_CONTEXT),
                    printDTO.getEnterpriseGuid(), printDTO.getStoreGuid()
            );
            // 设置国际化字段,如果没传就默认中文
            String localeJson = messageExt.getProperty(RocketMqConfig.MESSAGE_LOCALE);
            log.info("[国际化]localeJson:={}", localeJson);
            Locale locale = Locale.SIMPLIFIED_CHINESE;
            LocaleContextHolder.setLocale(locale);

            MdcContextUtils.fillByPreContext();
            MdcContextUtils.fillByCusContext("searchKey", PrintLogUtils
                    .searchKey(printDTO.getPrintUid(), printDTO.getInvoiceType()));
            log.info("打印消息推送消费，body: {}", json);
            EnterpriseIdentifier.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            try {
                RecordValidUtils.createPrintTaskValidate(printDTO);
            } catch (Exception e) {
                log.warn("打印消息参数异常，throwable={}", ThrowableExtUtils.asStringIfAbsent(e));
                return true;
            }
            log.info("printDTO入参打印：{}", JacksonUtils.writeValueAsString(printDTO));

            printRecordService.insertRecord(printDTO);
        } catch (Exception e) {
            log.warn("打印消息消费异常，throwable={}, e:", ThrowableExtUtils.asStringIfAbsent(e), e);
        } finally {
            TraceContextUtils.remove();
            UserContextUtils.remove();
            EnterpriseIdentifier.remove();
            MdcContextUtils.remove();
        }
        return true;
    }
}
