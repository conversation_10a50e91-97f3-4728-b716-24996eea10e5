package com.holder.saas.print.event;

import com.holder.saas.print.config.RocketMqConfig;
import com.holder.saas.print.service.PrintRecordService;
import com.holder.saas.print.utils.ThrowableExtUtils;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/09/18 20:47
 * @description
 * @program holder-saas-store-takeaway
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.PRINT_RECORD_TIMEOUT_TOPIC,
        tags = RocketMqConfig.PRINT_RECORD_TIMEOUT_TAG,
        consumerGroup = RocketMqConfig.PRINT_RECORD_TIMEOUT_GROUP)
public class PrintRecordTimeoutListener extends AbstractRocketMqConsumer<RocketMqTopic, String> {

    private final PrintRecordService printRecordService;

    @Autowired
    public PrintRecordTimeoutListener(PrintRecordService printRecordService) {
        this.printRecordService = printRecordService;
    }

    @Override
    public boolean consumeMsg(String recordGuid, MessageExt messageExt) {
        try {
            UserContextUtils.put(messageExt.getProperty(RocketMqConfig.MESSAGE_CONTEXT));
            EnterpriseIdentifier.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            printRecordService.updatePendingResult(recordGuid);
        } catch (Exception e) {
            log.error("打印任务超时消息消费异常，throwable={}", ThrowableExtUtils.asStringIfAbsent(e));
        } finally {
            UserContextUtils.remove();
            EnterpriseIdentifier.remove();
        }
        return true;
    }
}
