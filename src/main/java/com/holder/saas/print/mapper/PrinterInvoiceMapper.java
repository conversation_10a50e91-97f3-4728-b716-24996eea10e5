package com.holder.saas.print.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holder.saas.print.entity.domain.PrinterInvoiceDO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import org.apache.ibatis.annotations.Param;

/**
 * 打印机票据 Mapper 接口
 *
 * <AUTHOR>
 * @since 2018-11-26
 */
public interface PrinterInvoiceMapper extends BaseMapper<PrinterInvoiceDO> {

    void updatePrintInvoiceAuto(@Param("status") Integer status, @Param("req")PrinterDTO printerDTO);

    Integer selectInvoiceAutoStatus(@Param("req")PrinterDTO printerDTO);
}
