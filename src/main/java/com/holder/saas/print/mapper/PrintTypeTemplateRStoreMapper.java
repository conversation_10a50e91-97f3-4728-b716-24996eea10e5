package com.holder.saas.print.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holder.saas.print.entity.domain.type.PrintTypeTemplateRStoreDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 打印分类模版关联门店表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
public interface PrintTypeTemplateRStoreMapper extends BaseMapper<PrintTypeTemplateRStoreDO> {


    int queryRepeatTemplateCount(@Param("brandGuid") String brandGuid,
                                 @Param("storeGuidList") List<String> storeGuidList,
                                 @Param("invoiceTypeList") List<String> invoiceTypeList,
                                 @Param("guid") String guid);

    List<PrintTypeTemplateRStoreDO> queryTemplateStoreGuidList(@Param("brandGuid") String brandGuid,
                                                               @Param("invoiceTypeList") List<String> invoiceTypeList,
                                                               @Param("templateGuid") String templateGuid);

    PrintTypeTemplateRStoreDO queryTemplateByInvoiceTypeAndStoreGuid(@Param("invoiceType") String invoiceType,
                                                                     @Param("storeGuid") String storeGuid);
}
