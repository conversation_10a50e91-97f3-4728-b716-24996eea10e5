package com.holder.saas.print.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holder.saas.print.entity.domain.type.PrintTypeTemplateDO;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.item.req.SingleDataPageDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 打印分类模版表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Mapper
@Component
public interface PrintTypeTemplateMapper extends BaseMapper<PrintTypeTemplateDO> {

    /**
     * 查询模版名称数量
     */
    int checkTemplateName(@Param("brandGuid") String brandGuid,
                          @Param("name") String name,
                          @Param("guid") String guid);

    List<PrintTypeTemplateDO> queryPage(@Param("query") SingleDataPageDTO query);


    int queryRepeatTemplateCount(@Param("brandGuid") String brandGuid,
                           @Param("invoiceTypeList") List<String> invoiceTypeList,
                           @Param("guid") String guid);

}
