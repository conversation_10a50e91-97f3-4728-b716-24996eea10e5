package com.holder.saas.print.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holder.saas.print.entity.domain.PrinterDO;
import com.holder.saas.print.entity.query.PrinterQuery;
import com.holder.saas.print.entity.read.PrinterReadDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 打印机 Mapper 接口
 *
 * <AUTHOR>
 * @since 2018-11-26
 */
public interface PrinterMapper extends BaseMapper<PrinterDO> {

    /**
     * 查询完整打印机，包括其单据、区域、商品
     *
     * @param printerDO
     * @return
     */
    PrinterReadDO findPrinterBindings(PrinterDO printerDO);

    /**
     * 查询指定设备的所有可见打印机
     *
     * @param printerDO
     * @return
     */
    List<PrinterReadDO> findPrinterOfTheDevice(PrinterDO printerDO);

    /**
     * 查询指定业务类型的所有打印机
     *
     * @param printerDO
     * @return
     */
    List<PrinterReadDO> findPrinterOfBizType(PrinterDO printerDO);

    /**
     * 通过Query实体查询可用打印机，视查询条件含区域、菜品
     *
     * @param printerQuery
     * @return
     */
    List<PrinterReadDO> findPrinterByQuery(PrinterQuery printerQuery);


    /**
     * 通过Query实体查询转台时可用打印机
     *
     * @param printerQuery
     * @return
     */
    List<PrinterReadDO> findPrinterOfKitchenTable(PrinterQuery printerQuery);

    List<PrinterReadDO> findCloudPrinterByQuery(PrinterQuery printerQuery);

    List<PrinterReadDO> listPrinterBindings(@Param("printerGuidList") List<String> printerGuidList);

    /**
     * 根据printerGuid和商品集合查询绑定商品信息
     * @param printerQuery 查询条件
     * @return 打印商品信息
     */
    List<PrinterReadDO>  listPrinterItemByPrinterIdAndItemList(PrinterQuery printerQuery);

}
