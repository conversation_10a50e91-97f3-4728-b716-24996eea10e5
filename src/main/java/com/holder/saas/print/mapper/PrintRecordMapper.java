package com.holder.saas.print.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holder.saas.print.entity.domain.PrintRecordDO;
import com.holder.saas.print.entity.query.PrintRecordQuery;
import com.holder.saas.print.entity.read.PrintRecordReadDO;
import com.holderzone.saas.store.dto.print.PrintRecordDTO;
import feign.Param;

import java.util.List;

/**
 * 打印记录 Mapper 接口
 *
 * <AUTHOR>
 * @since 2018-11-26
 */
public interface PrintRecordMapper extends BaseMapper<PrintRecordDO> {

    /**
     * 根据打印记录Guid查询该条打印记录，包括其路由的打印机
     *
     * @param printRecordQuery
     * @return
     */
    PrintRecordReadDO queryByRecordGuid(PrintRecordQuery printRecordQuery);

    /**
     * 根据打印记录Guid列表查询多条打印记录，包括其路由的打印机
     *
     * @param printRecordQuery
     * @return
     */
    List<PrintRecordReadDO> queryInRecordGuid(PrintRecordQuery printRecordQuery);

    /**
     * fixme 如果deviceId使用硬件id了，那么该查询中需要加storeGuid作为过滤条件
     *
     * @param printRecordDO
     * @return
     */
    long countByDeviceAndStatus(PrintRecordDO printRecordDO);

    /**
     * fixme 如果deviceId使用硬件id了，那么该查询中需要加storeGuid作为过滤条件
     * <p>
     * 需要联合索引 idx_list_record(printer_guid, device_id, print_status, is_deleted);
     *
     * @param printRecordDO
     * @return
     */
    List<PrintRecordReadDO> listByDeviceAndStatus(PrintRecordDO printRecordDO);

    /**
     * fixme 如果deviceId使用硬件id了，那么该查询中需要加storeGuid作为过滤条件
     * <p>
     * 需要联合索引 idx_list_record(printer_guid, device_id, print_status, is_deleted);
     *
     * @param printRecordDTO
     * @return
     */
    List<PrintRecordReadDO> listByDeviceAndStatusFor3Days(PrintRecordDTO printRecordDTO);


    void deleteHistory(PrintRecordDTO printRecordDTO);
    /**
     * 查询虚拟打印机的打印记录
     *
     * @param printRecordDO
     * @return
     */
    List<PrintRecordReadDO> listByFakePrinterGuid(PrintRecordDO printRecordDO);
}
