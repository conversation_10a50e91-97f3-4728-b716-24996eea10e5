package com.holder.saas.print.controller;

import com.holder.saas.print.service.*;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.print.raw.PrinterFormatRawDTO;
import com.holderzone.saas.store.dto.print.raw.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@Api("打印数据同步接口")
@RequestMapping(value = "/print_table", produces = {"application/json;charset=UTF-8"})
public class PrintSyncController {

    private final PrinterService printerService;

    private final PrinterItemService printerItemService;

    private final PrinterAreaService printerAreaService;

    private final PrinterTableService printerTableService;

    private final PrinterInvoiceService printerInvoiceService;

    private final PrinterFormatService printerFormatService;

    @Autowired
    public PrintSyncController(PrinterService printerService, PrinterItemService printerItemService,
                               PrinterAreaService printerAreaService, PrinterTableService printerTableService,
                               PrinterInvoiceService printerInvoiceService,
                               PrinterFormatService printerFormatService) {
        this.printerService = printerService;
        this.printerItemService = printerItemService;
        this.printerAreaService = printerAreaService;
        this.printerTableService = printerTableService;
        this.printerInvoiceService = printerInvoiceService;
        this.printerFormatService = printerFormatService;
    }

    @PostMapping("/sync")
    @ApiOperation(value = "打印数据同步接口")
    public PrinterRawAggDTO sync(@RequestBody BaseDTO baseDTO) {
        if (log.isInfoEnabled()) {
            log.info("打印数据同步接口入参:{}", JacksonUtils.writeValueAsString(baseDTO));
        }
        List<PrinterRawDTO> printerRawDTOS = printerService.listRaw(baseDTO.getStoreGuid());
        List<PrinterItemRawDTO> printerItemRawDTOS = printerItemService.listRaw(baseDTO.getStoreGuid());
        List<PrinterAreaRawDTO> printerAreaRawDTOS = printerAreaService.listRaw(baseDTO.getStoreGuid());
        List<PrinterTableRawDTO> printerTableRawDTOS = printerTableService.listRaw(baseDTO.getStoreGuid());
        List<PrinterInvoiceRawDTO> printerInvoiceRawDTOS = printerInvoiceService.listRaw(baseDTO.getStoreGuid());
        List<PrinterFormatRawDTO> printerFormatRawDTOS = printerFormatService.listRaw(baseDTO.getStoreGuid());

        PrinterRawAggDTO printerRawAggDTO = new PrinterRawAggDTO();
        printerRawAggDTO.setPrinterList(printerRawDTOS);
        printerRawAggDTO.setPrinterItemList(printerItemRawDTOS);
        printerRawAggDTO.setPrinterAreaList(printerAreaRawDTOS);
        printerRawAggDTO.setPrinterTableList(printerTableRawDTOS);
        printerRawAggDTO.setPrinterInvoiceList(printerInvoiceRawDTOS);
        printerRawAggDTO.setPrinterFormatList(printerFormatRawDTOS);

        return printerRawAggDTO;
    }

}
