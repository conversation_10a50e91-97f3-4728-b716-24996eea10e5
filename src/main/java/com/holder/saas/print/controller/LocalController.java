package com.holder.saas.print.controller;

import com.holder.saas.print.utils.MultiLangUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/local")
public class LocalController {

    @GetMapping("/test")
    public String test() {
        return MultiLangUtils.get("checkout_invoice_header");
    }
}
