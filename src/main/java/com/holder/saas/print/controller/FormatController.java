package com.holder.saas.print.controller;

import com.holder.saas.print.service.PrinterFormatService;
import com.holder.saas.print.utils.valid.HibernateValidUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.format.TakeoutFormatDTO;
import com.holderzone.saas.store.dto.print.format.TurnTableFormatDTO;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@Api("模板格式接口")
@RequestMapping(value = "/format", produces = {"application/json;charset=UTF-8"})
public class FormatController {

    private final PrinterFormatService printerFormatService;

    @Autowired
    public FormatController(PrinterFormatService printerFormatService) {
        this.printerFormatService = printerFormatService;
    }

    @PostMapping("/add")
    @ApiOperation(value = "添加或修改单据格式")
    public void addFormat(@RequestBody FormatDTO formatDTO) {
        if (log.isInfoEnabled()) {
            log.info("添加或修改单据格式入参:{}", JacksonUtils.writeValueAsString(formatDTO));
        }
        HibernateValidUtils.validate(formatDTO);
        if (InvoiceTypeEnum.TURN_TABLE.getType().equals(formatDTO.getInvoiceType())) {
            Assert.notNull(((TurnTableFormatDTO) formatDTO).getStoreName(), "门店名称不能为空");
        }
        printerFormatService.add(formatDTO);
    }

    @PostMapping("/list")
    @ApiOperation(value = "查询单据格式")
    public List<FormatDTO> listFormat(@RequestBody FormatDTO formatDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询单据格式列表入参:{}", JacksonUtils.writeValueAsString(formatDTO));
        }
        return printerFormatService.list(formatDTO);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除单据格式")
    public void deleteFormat(@RequestBody FormatDTO formatDTO) {
        if (log.isInfoEnabled()) {
            log.info("删除单据格式入参:{}", JacksonUtils.writeValueAsString(formatDTO));
        }
        printerFormatService.delete(formatDTO);
    }

    @PostMapping("/enable")
    @ApiOperation(value = "启用单据格式")
    public void enableFormat(@RequestBody FormatDTO formatDTO) {
        if (log.isInfoEnabled()) {
            log.info("启用单据格式入参:{}", JacksonUtils.writeValueAsString(formatDTO));
        }
        printerFormatService.enable(formatDTO);
    }

    @PostMapping("/urls")
    @ApiOperation(value = "获取图片Urls，")
    public List<String> getInvoiceUrls(@RequestBody FormatDTO formatDTO) {
        if (log.isInfoEnabled()) {
            log.info("获取图片Urls入参:{}", JacksonUtils.writeValueAsString(formatDTO));
        }
        return printerFormatService.getInvoiceUrls(Objects.requireNonNull(formatDTO.getStoreGuid(), "门店Guid不得为空"));
    }

    @GetMapping("/judge_pre_qr")
    @ApiOperation(value = "判断是否配置二维码")
    public boolean judgeEnablePreCheckFormat(@RequestParam("storeGuid") String storeGuid) {
        if (log.isInfoEnabled()) {
            log.info("判断是否配置二维码入参:{}", JacksonUtils.writeValueAsString(storeGuid));
        }
        return printerFormatService.judgeEnablePreCheckFormat(storeGuid);
    }

    @GetMapping("/query_takeout")
    @ApiOperation(value = "查询外卖单格式")
    public TakeoutFormatDTO queryTakeout(@RequestParam("storeGuid") String storeGuid) {
        log.info("[查询外卖单格式]storeGuid={}", storeGuid);
        return printerFormatService.query(storeGuid, InvoiceTypeEnum.TAKEOUT.getType());
    }
}
