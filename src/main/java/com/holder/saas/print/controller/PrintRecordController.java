package com.holder.saas.print.controller;

import com.holder.saas.print.config.RocketMqConfig;
import com.holder.saas.print.entity.biz.TraceContext;
import com.holder.saas.print.service.PrintRecordService;
import com.holder.saas.print.utils.PrintLogUtils;
import com.holder.saas.print.utils.SpringContextUtils;
import com.holder.saas.print.utils.ThrowableExtUtils;
import com.holder.saas.print.utils.valid.HibernateValidUtils;
import com.holder.saas.print.utils.valid.RecordValidUtils;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.print.PrintOrderDTO;
import com.holderzone.saas.store.dto.print.PrintRecordDTO;
import com.holderzone.saas.store.dto.print.PrintRecordReqDTO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.util.StopWatch;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/09/20 11:14
 */
@Slf4j
@RestController
@Api("打印内容接口")
@RequestMapping(value = "/print_record")
public class PrintRecordController {

    private final PrintRecordService printRecordService;

    private final DefaultRocketMqProducer defaultRocketMqProducer;

    @Autowired
    public PrintRecordController(PrintRecordService printRecordService, DefaultRocketMqProducer defaultRocketMqProducer) {
        this.printRecordService = printRecordService;
        this.defaultRocketMqProducer = defaultRocketMqProducer;
    }

    @PostMapping(value = "/send")
    @ApiOperation("打印消息推送")
    public String printTask(@RequestBody PrintDTO printDTO) {
        printDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        String context = TraceContext.ids();
        String searchKey = PrintLogUtils.searchKey(printDTO.getPrintUid(), printDTO.getInvoiceType());
        if (log.isInfoEnabled()) {
            // searchKey = printUid_invoiceType;
            log.info("打印消息推送入参，context={}，searchKey={}，body: {}",
                    context, searchKey, JacksonUtils.writeValueAsString(printDTO));
        }
        // 如果当前为开发环境，即激活的dev配置文件
        if (SpringContextUtils.isDevEnv()) {
            try {
                RecordValidUtils.createPrintTaskValidate(printDTO);
            } catch (Exception e) {
                String msg = ThrowableExtUtils.asStringIfAbsent(e);
                log.error("打印消息参数异常：context={}, searchKey={}, throwable={}", context, searchKey, msg);
                return msg;
            }
        }
        Message message = new Message(RocketMqConfig.PRINT_MESSAGE_TOPIC,
                RocketMqConfig.PRINT_MESSAGE_TAG, JacksonUtils.toJsonByte(printDTO));
        message.getProperties().put(RocketMqConfig.MESSAGE_CONTEXT, UserContextUtils.getJsonStr());
        defaultRocketMqProducer.sendMessage(message);

        return "SUCCESS";
    }

    @PostMapping("/get_order")
    @ApiOperation("打印内容获取接口")
    public List<PrintOrderDTO> getPrinterOrder(@RequestBody PrintRecordReqDTO printRecordReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("打印内容获取接口入参：{},lang:{}", JacksonUtils.writeValueAsString(printRecordReqDTO), LocaleContextHolder.getLocale());
        }
        Long startTime = System.currentTimeMillis();
        List<PrintOrderDTO> printOrderRes = printRecordService.getPrintOrder(printRecordReqDTO);
        Long endTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>>>>>>>>>>>>打印内容构造耗时：    " + (endTime - startTime));
        log.info("打印内容返回参数：{}", JacksonUtils.writeValueAsString(printOrderRes));
        return printOrderRes;
    }

    @PostMapping("/get_test_order")
    @ApiOperation(value = "测试单据格式")
    public PrintOrderDTO getTestPrinterOrder(@RequestBody FormatDTO formatDTO) {
        if (log.isInfoEnabled()) {
            log.info("测试单据格式入参:{}", JacksonUtils.writeValueAsString(formatDTO));
        }
        HibernateValidUtils.validate(formatDTO);
        return printRecordService.getTestPrintOrder(formatDTO);
    }

    @PostMapping("/get_test_orders")
    @ApiOperation(value = "测试单据格式")
    public List<PrintOrderDTO> getTestPrinterOrders(@RequestBody FormatDTO formatDTO) {
        if (log.isInfoEnabled()) {
            log.info("测试单据格式入参:{}", JacksonUtils.writeValueAsString(formatDTO));
        }
        HibernateValidUtils.validate(formatDTO);
        return printRecordService.getTestPrintOrders(formatDTO);
    }

    @PostMapping("/list")
    @ApiOperation("获取打印结果的列表，入参status: 0=打印中，1=成功，2=失败")
    public List<PrintRecordDTO> listRecord(
            @Validated(PrintRecordReqDTO.ListRecord.class) @RequestBody PrintRecordReqDTO printRecordReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("获取打印结果的列表入参：{}", JacksonUtils.writeValueAsString(printRecordReqDTO));
        }
        return printRecordService.listRecord(printRecordReqDTO);
    }

    @PostMapping("/update_status")
    @ApiOperation("打印结果更新")
    public void updateStatus(
            @Validated(PrintRecordReqDTO.UpdateRecord.class) @RequestBody PrintRecordReqDTO printRecordReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("打印结果更新入参：{}", JacksonUtils.writeValueAsString(printRecordReqDTO));
        }
        printRecordService.updatePrintResult(printRecordReqDTO);
    }

    @PostMapping("/delete_history_record")
    @ApiOperation("删除打印历史数据")
    public void deleteHistoryRecord() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("企业：" + UserContextUtils.getEnterpriseName() + "删除打印历史数据耗时");
        log.info("开始删除打印历史数据");
        printRecordService.deleteHistoryRecord();
        stopWatch.stop();
        log.info("结束删除打印历史数据");
    }

    @PostMapping("/delete")
    @ApiOperation("删除单条打印记录")
    public void deleteRecord(
            @Validated(PrintRecordReqDTO.DeleteRecord.class) @RequestBody PrintRecordReqDTO printRecordReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("删除单条打印记录入参：{}", JacksonUtils.writeValueAsString(printRecordReqDTO));
        }
        printRecordService.deleteRecord(printRecordReqDTO);
    }

    @PostMapping("/batch_delete")
    @ApiOperation("批量删除失败列表")
    public void batchDeleteRecord(
            @Validated(PrintRecordReqDTO.BatchDeleteRecord.class) @RequestBody PrintRecordReqDTO printRecordReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("批量删除失败列表入参：printStatusDTO={}", JacksonUtils.writeValueAsString(printRecordReqDTO));
        }
        printRecordService.batchDeleteRecord(printRecordReqDTO.getArrayOfRecordGuid());
    }

    /*
    @GetMapping("/takeaway_timeout/reprint")
    @ApiOperation("重新打印外卖自动接单未打印小票")
    public void reprintTakeaway(String storeGuid, String masterDeviceId) {
        if (log.isInfoEnabled()) {
            log.info("获取门店外卖自动接单未打印小票入参：storeGuid:{},masterDeviceId:{}", storeGuid, masterDeviceId);
        }
        printRecordService.reprintTakeaway(storeGuid, masterDeviceId);
    }
    */

    @GetMapping("/takeaway_timeout/reprint")
    @ApiOperation("获取外卖自动接单漏单小票打印")
    public List<PrintOrderDTO> reprintTakeawayPrintOrderList(String storeGuid) {
        /*
        if (log.isInfoEnabled()) {
            log.info("获取外卖自动接单漏单小票打印入参 ：storeGuid:{}", storeGuid);
        }
        */
        return printRecordService.reprintTakeawayPrintOrderList(storeGuid);
    }
}
