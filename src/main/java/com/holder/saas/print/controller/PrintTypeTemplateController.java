package com.holder.saas.print.controller;


import com.holder.saas.print.service.PrintTypeTemplateService;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.SingleDataPageDTO;
import com.holderzone.saas.store.dto.print.type.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 打印分类模版表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Slf4j
@Api("打印分类模版")
@RestController
@AllArgsConstructor
@RequestMapping("/print_type_template")
public class PrintTypeTemplateController {

    private final PrintTypeTemplateService printTypeTemplateService;

    /**
     * 创建打印分类模版
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建打印分类模版")
    public void create(@RequestBody @Validated(PrintTypeTemplateDTO.Create.class) PrintTypeTemplateDTO createDTO) {
        log.info("[创建打印分类模版]入参,createDTO={}", JacksonUtils.writeValueAsString(createDTO));
        printTypeTemplateService.create(createDTO);
    }

    /**
     * 查询打印分类模版列表
     */
    @PostMapping("/query_page")
    @ApiOperation(value = "查询打印分类模版列表", notes = "查询打印分类模版列表")
    public Page<PrintTypeTemplateVO> queryPage(@RequestBody SingleDataPageDTO query) {
        log.info("[查询打印分类模版列表]入参,query={}", JacksonUtils.writeValueAsString(query));
        return printTypeTemplateService.queryPage(query);
    }

    /**
     * 查询打印分类模版详情
     */
    @PostMapping("/query_detail")
    @ApiOperation(value = "查询打印分类模版详情", notes = "查询打印分类模版详情")
    public PrintTypeTemplateDetailDTO queryDetail(@RequestBody SingleDataDTO query) {
        log.info("[查询打印分类模版详情]入参,query={}", JacksonUtils.writeValueAsString(query));
        return printTypeTemplateService.queryDetail(query);
    }

    /**
     * 更新打印分类模版
     */
    @PostMapping("/modify")
    @ApiOperation(value = "更新打印分类模版", notes = "更新打印分类模版")
    public void modify(@RequestBody @Validated(PrintTypeTemplateDTO.Modify.class) PrintTypeTemplateDTO modifyDTO) {
        log.info("[更新打印分类模版]入参,modifyDTO={}", JacksonUtils.writeValueAsString(modifyDTO));
        printTypeTemplateService.modify(modifyDTO);
    }

    /**
     * 启用/禁用打印分类模版
     */
    @PostMapping("/enable")
    @ApiOperation(value = "启用/禁用打印分类模版", notes = "启用/禁用打印分类模版")
    public boolean enable(@RequestBody @Validated PrintTypeTemplateEnableDTO enableDTO) {
        log.info("[启用/禁用打印分类模版]入参,enableDTO={}", JacksonUtils.writeValueAsString(enableDTO));
        return printTypeTemplateService.enable(enableDTO);
    }

    /**
     * 删除打印分类模版
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除打印分类模版", notes = "删除打印分类模版")
    public boolean delete(@RequestBody SingleDataDTO deleteDTO) {
        log.info("[删除打印分类模版]入参,enableDTO={}", JacksonUtils.writeValueAsString(deleteDTO));
        return printTypeTemplateService.delete(deleteDTO);
    }

    /**
     * 查询品牌模版已配置的门店
     */
    @PostMapping("/query_store_by_brand")
    @ApiOperation(value = "查询品牌模版已配置的门店", notes = "查询品牌模版已配置的门店")
    public List<String> queryStoreByBrand(@RequestBody TemplateStoreQO query) {
        log.info("[查询品牌模版已配置的门店]入参,query={}", JacksonUtils.writeValueAsString(query));
        return printTypeTemplateService.queryStoreByBrand(query);
    }

    /**
     * 校验模版名称
     */
    @PostMapping("/check_template_name")
    @ApiOperation(value = "校验模版名称", notes = "校验模版名称")
    public Boolean checkTemplateName(@RequestBody PrintTypeTemplateDTO query) {
        log.info("[校验模版名称]入参,query={}", JacksonUtils.writeValueAsString(query));
        return printTypeTemplateService.checkTemplateName(query);
    }

}
