package com.holder.saas.print.controller;

import com.holder.saas.print.service.PrinterInvoiceService;
import com.holder.saas.print.service.PrinterService;
import com.holder.saas.print.utils.valid.PrinterValidUtils;
import com.holderzone.framework.exception.unchecked.ParameterException;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.PrinterQueryDTO;
import com.holderzone.saas.store.dto.print.cloud.CloudPrinterDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ba3b9b8 修改打印换行Bug 从这个提交开始需要合并（包含这个提交）
 *
 * <AUTHOR>
 * @version 1.0
 * @className PrinterController
 * @date 2018/7/24 16:39
 * @description 打印机api
 * @program holder-saas-store-print
 */
@Slf4j
@RestController
@Api("打印机接口")
@RequestMapping(value = "/printer", produces = {"application/json;charset=UTF-8"})
public class PrinterController {

    private final PrinterService printerService;

    private final PrinterInvoiceService printerInvoiceService;

    @Autowired
    public PrinterController(PrinterService printerService, PrinterInvoiceService printerInvoiceService) {
        this.printerService = printerService;
        this.printerInvoiceService = printerInvoiceService;
    }

    @PostMapping("/add")
    @ApiOperation(value = "添加打印机, 返回添加的打印机guid; 注: 一台T1只能添加一台本地打印机")
    public String addPrinter(@RequestBody PrinterDTO printerDTO) {
        if (log.isInfoEnabled()) {
            log.info("添加打印机入参:{}", JacksonUtils.writeValueAsString(printerDTO));
        }
        PrinterValidUtils.createAddValidate(printerDTO);
        return printerService.addPrinter(printerDTO);
    }

    @PostMapping("/add_cloud")
    @ApiOperation(value = "添加云打印机")
    public void addCloud(@RequestBody CloudPrinterDTO cloudPrinterDTO) {
        log.info("[添加云打印机]cloudPrinterDTO={}", JacksonUtils.writeValueAsString(cloudPrinterDTO));
        PrinterValidUtils.createAddCloudValidate(cloudPrinterDTO);
        printerService.addCloud(cloudPrinterDTO);
    }

    @PostMapping("/query")
    @ApiOperation(value = "获取某台打印机信息, 包括与菜品/区域绑定关系")
    public PrinterDTO queryPrinter(@RequestBody PrinterDTO printerDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询打印机入参：{}", JacksonUtils.writeValueAsString(printerDTO));
        }
        PrinterValidUtils.createQueryValidate(printerDTO);
        return printerService.queryPrinter(printerDTO);
    }

    @PostMapping("/list")
    @ApiOperation(value = "批量获取打印机信息, 包括与菜品/区域绑定关系")
    public List<PrinterDTO> listPrinter(@RequestBody SingleDataDTO request) {
        if (log.isInfoEnabled()) {
            log.info("查询打印机入参：{}", JacksonUtils.writeValueAsString(request));
        }
        PrinterValidUtils.queryListValidate(request);
        return printerService.listPrinter(request);
    }

    @PostMapping("/list_by_biz")
    @ApiOperation(value = "通过门店guid获取该门店的打印机信息列表(不含菜品/区域绑定关系)")
    public List<PrinterDTO> listPrintersByQuery(@RequestBody PrinterDTO printerDTO) {
        if (log.isInfoEnabled()) {
            log.info("条件查询打印机列表入参：{}", JacksonUtils.writeValueAsString(printerDTO));
        }
        PrinterValidUtils.createListByBizValidate(printerDTO);
        return printerService.listPrinterOfBizType(printerDTO);
    }

    @PostMapping("/listPrinterByPrinterIdAndItemId")
    public List<String> listPrinterByItemId(@RequestBody PrinterDTO printerDTO) {
        log.info("[根据商品Id查询已绑定打印商品信息列表],请求参数:printerDTO={}", JacksonUtils.writeValueAsString(printerDTO));
        return printerService.listPrinterByPrinterIdAndItemId(printerDTO);
    }


    @PostMapping("/list_cloud_printers")
    @ApiOperation(value = "通过门店guid获取该门店的云打印机信息列表(不含菜品/区域绑定关系)")
    public List<PrinterDTO> listCloudPrinters(@RequestBody PrinterDTO printerDTO) {
        log.info("[通过门店guid获取该门店的云打印机信息列表]printerDTO={}", JacksonUtils.writeValueAsString(printerDTO));
        PrinterValidUtils.createListCloudPrintersValidate(printerDTO);
        return printerService.listPrinterOfBizType(printerDTO);
    }

    @PostMapping("/list_by_device")
    @ApiOperation(value = "获取门店下所有的后厨打印机以及该一体机设备添加的前台/USB打印机; 有效字段: deviceId, storeGuid")
    public List<PrinterDTO> listPrinterByStoreGuid(@RequestBody PrinterDTO printerDTO) {
        if (log.isInfoEnabled()) {
            log.info("获取门店下所有的后厨打印机以及该一体机设备添加的前台/USB打印机入参：{}",
                    JacksonUtils.writeValueAsString(printerDTO));
        }
        PrinterValidUtils.createListByDeviceValidate(printerDTO);
        return printerService.listPrinterOfTheDevice(printerDTO);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改打印机信息/与菜品的绑定/与区域绑定")
    public void updatePrinter(@RequestBody PrinterDTO printerDTO) {
        if (log.isInfoEnabled()) {
            log.info("修改打印机入参:{}", JacksonUtils.writeValueAsString(printerDTO));
        }
        PrinterValidUtils.updatePrinterValidate(printerDTO);
        printerService.updatePrinter(printerDTO);
    }

    @PostMapping("/batch_update")
    @ApiOperation(value = "批量修改打印机信息/与菜品的绑定/与区域绑定")
    public void batchUpdatePrinter(@RequestBody PrinterDTO printerDTO) {
        if (log.isInfoEnabled()) {
            log.info("修改打印机入参:{}", JacksonUtils.writeValueAsString(printerDTO));
        }
        printerDTO.getPrinterDTOList().forEach(PrinterValidUtils::updatePrinterValidate);
        printerService.batchUpdatePrinter(printerDTO);
    }

    @PostMapping("/update_cloud")
    @ApiOperation(value = "修改云打印机信息")
    public void updateCloudPrinter(@RequestBody CloudPrinterDTO cloudPrinterDTO) {
        log.info("[修改云打印机信息]cloudPrinterDTO={}", JacksonUtils.writeValueAsString(cloudPrinterDTO));
        PrinterValidUtils.updatePrinterValidate(cloudPrinterDTO);
        printerService.updateCloudPrinter(cloudPrinterDTO);
    }

    @PostMapping("/change_master")
    @ApiOperation("修改门店的主机")
    public void changeMasterDevice(@RequestBody PrinterDTO printerDTO) {
        if (log.isInfoEnabled()) {
            log.info("修改门店的主机入参:{}", JacksonUtils.writeValueAsString(printerDTO));
        }
        PrinterValidUtils.createChangeMasterValidate(printerDTO);
        printerService.changeMasterPrinter(printerDTO);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除打印机")
    public void deletePrinter(@RequestBody PrinterDTO printerDTO) {
        if (log.isInfoEnabled()) {
            log.info("删除打印机入参：{}", JacksonUtils.writeValueAsString(printerDTO));
        }
        PrinterValidUtils.createDeleteValidate(printerDTO);
        printerService.deletePrinter(printerDTO);
    }

    @PostMapping("/delete_by_device")
    @ApiOperation(value = "解绑设备时调用此接口删除打印机; ps: 会删除解绑设备添加的打印机(后台打印机除外), 如果门店绑定的设备为0, 则会删除门店所有的打印机; countDevice: 门店剩余的设备数")
    public void deletePrinterByDevice(@RequestParam String deviceId, @RequestParam String storeGuid, @RequestParam int countDevice) {
        log.info("解绑设备时调用接口删除打印机; 入参:device={}, storeGuid={}, countDevice={}", deviceId, storeGuid, countDevice);
        if (countDevice < 0) {
            throw new ParameterException("countDevice参数错误");
        } else if (countDevice == 0) {
            PrinterDTO printerDTO = new PrinterDTO();
            printerDTO.setStoreGuid(storeGuid);
            printerService.deletePrinterOfTheStore(printerDTO);
        } else {
            PrinterDTO printerDTO = new PrinterDTO();
            printerDTO.setStoreGuid(storeGuid);
            printerDTO.setDeviceId(deviceId);
            printerService.deletePrinterOfTheDevice(printerDTO);
        }
    }

    @PostMapping("/backups_printer")
    @ApiOperation(value = "一键备份打印机")
    public boolean backupsPrinter(@RequestBody SingleDataDTO singleDataDTO) {
        return printerService.backupsPrinter(singleDataDTO.getData());
    }

    @PostMapping("/restore_printer")
    @ApiOperation(value = "一键还原打印机")
    public boolean restorePrinter(@RequestBody SingleDataDTO singleDataDTO) {
        return printerService.restorePrinter(singleDataDTO.getData());
    }

    @PostMapping("/backups_printer_time")
    @ApiOperation(value = "最新备份时间")
    public String backupsPrinterTime(@RequestBody SingleDataDTO singleDataDTO) {
        return printerService.backupsPrinterTime(singleDataDTO.getData());
    }

    @PostMapping("/query_by_condition")
    @ApiOperation(value = "根据条件查询打印机")
    public List<PrinterDTO> queryByCondition(@RequestBody PrinterQueryDTO queryDTO) {
        log.info("[根据条件查询打印机]入参：{}", JacksonUtils.writeValueAsString(queryDTO));
        return printerService.queryByCondition(queryDTO);
    }

    @PostMapping("/test_print")
    @ApiOperation(value = "测试打印")
    public void testPrint(@RequestBody CloudPrinterDTO cloudPrinterDTO) {
        log.info("[测试打印]cloudPrinterDTO={}", JacksonUtils.writeValueAsString(cloudPrinterDTO));
        PrinterValidUtils.createQueryValidate(cloudPrinterDTO);
        printerService.testPrint(cloudPrinterDTO);
    }

    @ApiOperation(value = "校验飞蛾打印机")
    @PostMapping("/check_cloud")
    public void checkCloudPrinter(@RequestBody CloudPrinterDTO cloudPrinterDTO) {
        log.info("[云打印][校验飞蛾打印机]cloudPrinterDTO={}", JacksonUtils.writeValueAsString(cloudPrinterDTO));
        PrinterValidUtils.createCheckCloudValidate(cloudPrinterDTO);
        printerService.checkPrinter(cloudPrinterDTO);
    }

    @PostMapping("/auto_print/{status}")
    public void autoPrintSet(@PathVariable("status") Integer status, @RequestBody PrinterDTO printerDTO) {
        log.info("设置是否：{}自动打印：{}", status, JacksonUtils.writeValueAsString(printerDTO));
        PrinterValidUtils.autoPrintValidate(status, printerDTO);
        printerInvoiceService.autoPrintSet(status, printerDTO);
    }

    @PostMapping("/auto_print/query")
    public Integer autoPrintSet(@RequestBody PrinterDTO printerDTO) {
        log.info("查询是否自动打印：{}", JacksonUtils.writeValueAsString(printerDTO));
        PrinterValidUtils.autoPrintQueryValidate(printerDTO);
        return printerInvoiceService.selectInvoiceAutoStatus(printerDTO);
    }

}
