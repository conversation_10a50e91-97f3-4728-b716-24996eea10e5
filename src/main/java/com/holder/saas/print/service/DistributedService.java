package com.holder.saas.print.service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DistributedService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface DistributedService {

    Long rawId(String tag);

    String nextId(String tag);

    List<String> nextBatchId(String tag, long count);

    String nextPrinterGuid();

    List<String> nextBatchPrinterGuid(long count);

    String nextPrinterInvoiceGuid();

    List<String> nextBatchPrinterInvoiceGuid(long count);

    String nextPrinterAreaGuid();

    List<String> nextBatchPrinterAreaGuid(long count);

    String nextPrinterItemGuid();

    List<String> nextBatchPrinterItemGuid(long count);

    String nextPrintRecordGuid();

    List<String> nextBatchPrintRecordGuid(long count);

    String nextInvoiceFormatId();

    String nextPrintTypeTemplateGuid();

    List<String> nextBatchTemplateRTypeGuid(long count);

}
