package com.holder.saas.print.service.impl;

import com.holder.saas.print.entity.read.PrintRecordReadDO;
import com.holder.saas.print.service.ContentCacheService;
import com.holderzone.framework.util.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ContentCacheServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印单缓存实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class ContentCacheServiceImpl implements ContentCacheService {

    private static final long EXPIRE_TIME = 5;

    private static final TimeUnit EXPIRE_UNIT = TimeUnit.MINUTES;

    private final RedisTemplate<String, Object> redisTemplate;

    @Value("${batch-push.enable:false}")
    private boolean batchPushEnable;

    private static final String PRINT_MSG_KEY = "PRINT:MSG_ID:";

    @Autowired
    public ContentCacheServiceImpl(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public void save(List<PrintRecordReadDO> arrayOfPrintRecordReadDO) {
        if (batchPushEnable) {
            List<String> arrayOfRecordGuid = arrayOfPrintRecordReadDO.stream()
                    .map(PrintRecordReadDO::getRecordGuid)
                    .collect(Collectors.toList());
            redisTemplate.opsForValue().set(keyForBatch(arrayOfRecordGuid), arrayOfPrintRecordReadDO, EXPIRE_TIME, EXPIRE_UNIT);
        } else {
            for (PrintRecordReadDO printRecordReadDO : arrayOfPrintRecordReadDO) {
                String recordGuid = printRecordReadDO.getRecordGuid();
                redisTemplate.opsForValue().set(keyForSingle(recordGuid), printRecordReadDO, EXPIRE_TIME, EXPIRE_UNIT);
            }
        }
    }

    @Override
    public PrintRecordReadDO popSingle(String recordGuid) {
        String key = keyForSingle(recordGuid);
        PrintRecordReadDO printRecordReadDO = (PrintRecordReadDO) redisTemplate.opsForValue().get(key);
        redisTemplate.delete(key);
        return printRecordReadDO;
    }

    @Override
    public List<PrintRecordReadDO> popBatch(List<String> arrayOfRecordGuid) {
        String key = keyForBatch(arrayOfRecordGuid);
        List<PrintRecordReadDO> printRecordReadDO = (List<PrintRecordReadDO>) redisTemplate.opsForValue().get(key);
        redisTemplate.delete(key);
        return printRecordReadDO;
    }

    @Override
    public boolean hasMsgId(String msgId, String recordGuid, List<String> arrayOfRecordGuid) {
        if (StringUtils.isEmpty(msgId)) {
            return false;
        }
        if (StringUtils.isEmpty(recordGuid) && CollectionUtils.isEmpty(arrayOfRecordGuid)) {
            return false;
        }
        String key;
        if (StringUtils.isNotEmpty(recordGuid)) {
            key = PRINT_MSG_KEY + msgId + ":" + recordGuid;
        } else {
            Collections.sort(arrayOfRecordGuid);
            key = PRINT_MSG_KEY + msgId + ":" + Objects.hash(arrayOfRecordGuid);
        }
        return Boolean.TRUE.equals(redisTemplate.hasKey(key));
    }

    @Override
    public void saveMsgId(String msgId, String recordGuid, List<String> arrayOfRecordGuid) {
        if (StringUtils.isEmpty(msgId)) {
            return;
        }
        if (StringUtils.isNotEmpty(recordGuid)) {
            redisTemplate.opsForValue().set(PRINT_MSG_KEY + msgId + ":" + recordGuid, recordGuid, 1, TimeUnit.DAYS);
        }
        if (CollectionUtils.isNotEmpty(arrayOfRecordGuid)) {
            Collections.sort(arrayOfRecordGuid);
            redisTemplate.opsForValue().set(PRINT_MSG_KEY + msgId + ":" + Objects.hash(arrayOfRecordGuid),
                    JacksonUtils.writeValueAsString(arrayOfRecordGuid), 1, TimeUnit.DAYS);
        }
    }

    private String keyForSingle(String recordGuid) {
        return "print:content:" + recordGuid;
    }

    private String keyForBatch(List<String> arrayOfRecordGuid) {
        Collections.sort(arrayOfRecordGuid);
        return "print:content:" + Objects.hash(arrayOfRecordGuid);
    }
}
