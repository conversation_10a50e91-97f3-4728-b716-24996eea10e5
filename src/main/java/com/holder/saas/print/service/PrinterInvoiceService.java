package com.holder.saas.print.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.print.entity.domain.PrinterInvoiceDO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.raw.PrinterInvoiceRawDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterInvoiceService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface PrinterInvoiceService extends IService<PrinterInvoiceDO> {

    void bindPrinterInvoice(PrinterDTO printerDTO);

    void deletePrinterInvoice(String printerGuid);

    void batchDeletePrinterInvoice(List<String> arrayOfPrinterGuid);

    void deleteStorePrinterInvoice(String storeGuid);

    List<PrinterInvoiceRawDTO> listRaw(String storeGuid);

    void autoPrintSet(Integer status, PrinterDTO printerDTO);

    Integer selectInvoiceAutoStatus(PrinterDTO printerDTO);
}
