package com.holder.saas.print.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.print.entity.domain.PrinterFormatDO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.raw.PrinterFormatRawDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterFormatService
 * @date 2019/03/29 20:00
 * @description
 * @program holder-saas-store-print
 */
public interface PrinterFormatService extends IService<PrinterFormatDO> {

    void add(FormatDTO formatDTO);

    List<FormatDTO> list(FormatDTO formatDTO);

    boolean judgeEnablePreCheckFormat(String storeGuid);

    void enable(FormatDTO formatDTO);

    void delete(FormatDTO formatDTO);

    <T extends FormatDTO> T query(String storeGuid, Integer invoiceType);

    <T extends FormatDTO> List<T> list(String storeGuid, Integer invoiceType);

    List<String> getInvoiceUrls(String storeGuid);

    List<PrinterFormatRawDTO> listRaw(String storeGuid);
}
