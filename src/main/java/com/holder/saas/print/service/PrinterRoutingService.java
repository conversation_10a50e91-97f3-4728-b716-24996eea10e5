package com.holder.saas.print.service;

import com.holder.saas.print.entity.domain.PrinterDO;
import com.holder.saas.print.entity.read.PrinterReadDO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterRoutingService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface PrinterRoutingService {

    List<PrinterReadDO> findPrinterAvailable(PrintDTO printDto);

    PrinterReadDO findFakePrinterReadDO();

    PrinterDO findFakePrinterDO();

    List<PrinterReadDO> findCloudPrinterAvailable(PrintDTO printDTO);
}
