package com.holder.saas.print.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.print.entity.domain.PrinterTableDO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.PrinterTableDTO;
import com.holderzone.saas.store.dto.print.raw.PrinterTableRawDTO;

import java.util.List;

public interface PrinterTableService extends IService<PrinterTableDO> {

    void bindPrinterAreaTable(PrinterDTO printerDTO);

    List<PrinterTableDTO> listPrinterAreaTable(PrinterDTO printerDTO);

    void deletePrinterAreaTable(String printerGuid);

    void batchDeletePrinterAreaTable(List<String> arrayOfPrinterGuid);

    void deleteStorePrinterAreaTable(String storeGuid);

    List<PrinterTableRawDTO> listRaw(String storeGuid);
}
