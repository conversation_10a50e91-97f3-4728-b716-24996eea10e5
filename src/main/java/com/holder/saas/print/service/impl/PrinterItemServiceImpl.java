package com.holder.saas.print.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.print.entity.domain.PrinterItemDO;
import com.holder.saas.print.mapper.PrinterItemMapper;
import com.holder.saas.print.mapstruct.PrinterMapstruct;
import com.holder.saas.print.mapstruct.PrinterRawMaptstruct;
import com.holder.saas.print.service.DistributedService;
import com.holder.saas.print.service.PrinterItemService;
import com.holderzone.saas.store.dto.print.PrinterItemDTO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.raw.PrinterItemRawDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterItemServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印机商品管理实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class PrinterItemServiceImpl extends ServiceImpl<PrinterItemMapper, PrinterItemDO> implements PrinterItemService {

    private final DistributedService distributedService;

    private final PrinterMapstruct printerMapstruct;

    private final PrinterRawMaptstruct printerRawMaptstruct;

    @Autowired
    public PrinterItemServiceImpl(DistributedService distributedService, PrinterMapstruct printerMapstruct,
                                  PrinterRawMaptstruct printerRawMaptstruct) {
        this.distributedService = distributedService;
        this.printerMapstruct = printerMapstruct;
        this.printerRawMaptstruct = printerRawMaptstruct;
    }

    @Override
    public void bindPrinterItem(PrinterDTO printerDTO) {
        List<PrinterItemDTO> itemDTOList = printerDTO.getArrayOfItemDTO();
        if (CollectionUtils.isEmpty(itemDTOList)) return;
        String storeGuid = printerDTO.getStoreGuid();
        String printerGuid = printerDTO.getPrinterGuid();
        List<String> batchGuid = distributedService.nextBatchPrinterItemGuid(itemDTOList.size());
        List<PrinterItemDO> arrayOfPrinterItemDO = itemDTOList.stream()
                .map(itemDTO -> new PrinterItemDO()
                        .setStoreGuid(storeGuid)
                        .setPrinterGuid(printerGuid)
                        .setItemGuid(itemDTO.getItemGuid())
                        .setItemName(itemDTO.getItemName())
                        .setGuid(batchGuid.remove(batchGuid.size() - 1)))
                .collect(Collectors.toList());
        saveBatch(arrayOfPrinterItemDO);
    }

    @Override
    public List<PrinterItemDTO> listPrinterItem(PrinterDTO printerDTO) {
        List<PrinterItemDO> arrayOfPrinterItemDO = list(new LambdaQueryWrapper<PrinterItemDO>()
                .eq(PrinterItemDO::getPrinterGuid, printerDTO.getPrinterGuid()));
        if (CollectionUtils.isEmpty(arrayOfPrinterItemDO)) return Collections.emptyList();
        return printerMapstruct.toPrinterItemDTO(arrayOfPrinterItemDO);
    }

    @Override
    public void deletePrinterItem(String printerGuid) {
        remove(new LambdaQueryWrapper<PrinterItemDO>()
                .eq(PrinterItemDO::getPrinterGuid, printerGuid));
    }

    @Override
    public void batchDeletePrinterItem(List<String> arrayOfPrinterGuid) {
        if (CollectionUtils.isEmpty(arrayOfPrinterGuid)) return;
        remove(new LambdaQueryWrapper<PrinterItemDO>()
                .in(PrinterItemDO::getPrinterGuid, arrayOfPrinterGuid));
    }

    @Override
    public void deleteStorePrinterItem(String storeGuid) {
        remove(new LambdaQueryWrapper<PrinterItemDO>()
                .eq(PrinterItemDO::getStoreGuid, storeGuid));
    }

    @Override
    public void batchDeleteStorePrinterItem(List<String> arrayOfStoreGuid) {
        if (CollectionUtils.isEmpty(arrayOfStoreGuid)) return;
        remove(new LambdaQueryWrapper<PrinterItemDO>()
                .in(PrinterItemDO::getStoreGuid, arrayOfStoreGuid));
    }

    @Override
    public List<PrinterItemRawDTO> listRaw(String storeGuid) {
        List<PrinterItemDO> list = list(new LambdaQueryWrapper<PrinterItemDO>()
                .eq(PrinterItemDO::getStoreGuid, storeGuid));
        return printerRawMaptstruct.toItemRawDTO(list);
    }
}

