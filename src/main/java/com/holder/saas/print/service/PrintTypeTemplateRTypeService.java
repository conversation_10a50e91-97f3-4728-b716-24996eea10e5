package com.holder.saas.print.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.print.entity.domain.type.PrintTypeTemplateRTypeDO;
import com.holderzone.saas.store.dto.print.type.TemplateTypeVO;

import java.util.List;

/**
 * <p>
 * 打印分类模版关联分类表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
public interface PrintTypeTemplateRTypeService extends IService<PrintTypeTemplateRTypeDO> {

    void removeByTemplateGuid(String templateGuid);

    List<TemplateTypeVO> queryTemplateTypeVOList(String templateGuid, List<String> printItemGuidList);

}
