package com.holder.saas.print.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.print.entity.domain.PrinterInvoiceDO;
import com.holder.saas.print.mapper.PrinterInvoiceMapper;
import com.holder.saas.print.mapstruct.PrinterRawMaptstruct;
import com.holder.saas.print.service.DistributedService;
import com.holder.saas.print.service.PrinterInvoiceService;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.PrinterInvoiceDTO;
import com.holderzone.saas.store.dto.print.raw.PrinterInvoiceRawDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterInvoiceServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印机单据管理实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class PrinterInvoiceServiceImpl extends ServiceImpl<PrinterInvoiceMapper, PrinterInvoiceDO> implements PrinterInvoiceService {

    private final DistributedService distributedService;

    private final PrinterRawMaptstruct printerRawMaptstruct;

    private final PrinterInvoiceMapper printerInvoiceMapper;

    @Autowired
    public PrinterInvoiceServiceImpl(DistributedService distributedService, PrinterRawMaptstruct printerRawMaptstruct, PrinterInvoiceMapper printerInvoiceMapper) {
        this.distributedService = distributedService;
        this.printerRawMaptstruct = printerRawMaptstruct;
        this.printerInvoiceMapper = printerInvoiceMapper;
    }

    @Override
    public void bindPrinterInvoice(PrinterDTO printerDTO) {
        List<PrinterInvoiceDTO> arrayOfInvoiceDTO = printerDTO.getArrayOfInvoiceDTO();
        if (CollectionUtils.isEmpty(arrayOfInvoiceDTO)) {
            return;
        }
        String storeGuid = printerDTO.getStoreGuid();
        String printerGuid = printerDTO.getPrinterGuid();
        // 批量生成出来的ID是从大到小排列的
        List<String> batchGuid = distributedService.nextBatchPrinterInvoiceGuid(arrayOfInvoiceDTO.size());
        List<PrinterInvoiceDO> arrayOfPrinterInvoiceDO = arrayOfInvoiceDTO.stream()
                .map(invoiceDTO -> new PrinterInvoiceDO()
                        .setStoreGuid(storeGuid)
                        .setPrinterGuid(printerGuid)
                        .setInvoiceType(invoiceDTO.getInvoiceType())
                        .setInvoiceName(invoiceDTO.getInvoiceName())
                        .setPrintCount(invoiceDTO.getPrintCount())
                        .setGuid(batchGuid.remove(batchGuid.size() - 1)))
                .collect(Collectors.toList());
        saveBatch(arrayOfPrinterInvoiceDO);
    }

    @Override
    public void deletePrinterInvoice(String printerGuid) {
        remove(new LambdaQueryWrapper<PrinterInvoiceDO>()
                .eq(PrinterInvoiceDO::getPrinterGuid, printerGuid));
    }

    @Override
    public void batchDeletePrinterInvoice(List<String> arrayOfPrinterGuid) {
        if (CollectionUtils.isEmpty(arrayOfPrinterGuid)) {
            return;
        }
        remove(new LambdaQueryWrapper<PrinterInvoiceDO>()
                .in(PrinterInvoiceDO::getPrinterGuid, arrayOfPrinterGuid));
    }

    @Override
    public void deleteStorePrinterInvoice(String storeGuid) {
        remove(new LambdaQueryWrapper<PrinterInvoiceDO>()
                .eq(PrinterInvoiceDO::getStoreGuid, storeGuid));
    }

    @Override
    public List<PrinterInvoiceRawDTO> listRaw(String storeGuid) {
        List<PrinterInvoiceDO> list = list(new LambdaQueryWrapper<PrinterInvoiceDO>()
                .eq(PrinterInvoiceDO::getStoreGuid, storeGuid));
        return printerRawMaptstruct.toInvoiceRawDTO(list);
    }

    @Override
    public void autoPrintSet(Integer status, PrinterDTO printerDTO) {
        printerInvoiceMapper.updatePrintInvoiceAuto(status,printerDTO);
    }

    @Override
    public Integer selectInvoiceAutoStatus(PrinterDTO printerDTO) {
        return printerInvoiceMapper.selectInvoiceAutoStatus(printerDTO);
    }

}
