package com.holder.saas.print.service;

import com.holder.saas.print.entity.read.PrintRecordReadDO;
import com.holderzone.saas.store.dto.print.PrintOrderDTO;
import com.holderzone.saas.store.dto.print.PrintRecordDTO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintRecordRespService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface PrintRecordRespService {

    PrintRecordDTO do2Dto(PrintRecordReadDO printRecordReadDO);

    PrintOrderDTO getOrderByPerDO(PrintRecordReadDO printRecordReadDO);

    PrintOrderDTO getOrderByPerMock(int invoiceType, int pageSize, FormatDTO formatDTO, PrinterDTO printerDTO);
}
