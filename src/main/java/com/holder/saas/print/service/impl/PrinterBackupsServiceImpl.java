package com.holder.saas.print.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.print.entity.domain.PrinterBackupsDO;
import com.holder.saas.print.mapper.PrinterBackupsMapper;
import com.holder.saas.print.service.PrinterBackupsService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印机配置实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
@Transactional
public class PrinterBackupsServiceImpl extends ServiceImpl<PrinterBackupsMapper, PrinterBackupsDO> implements PrinterBackupsService {

    @Override
    public PrinterBackupsDO queryPrinters(String storeGuid, String deviceId) {
        List<PrinterBackupsDO> printerBackupsDOS = list(new LambdaQueryWrapper<PrinterBackupsDO>().eq(PrinterBackupsDO::getStoreGuid, storeGuid)
                .eq(PrinterBackupsDO::getDeviceId, deviceId)
                .orderByDesc(PrinterBackupsDO::getGmtCreate));
        if (!printerBackupsDOS.isEmpty()) {
            return printerBackupsDOS.get(0);
        } else {
            throw new BusinessException("当前没有备份过打印机信息");
        }
    }

    @Override
    @Transactional
    public boolean backupsPrinter(String storeGuid, String deviceId, List<PrinterDTO> printerBackupsDOS) {

        PrinterBackupsDO printerBackupsDO = new PrinterBackupsDO();
        printerBackupsDO.setDeviceId(deviceId);
        printerBackupsDO.setStoreGuid(storeGuid);
        printerBackupsDO.setPrintListJson(JacksonUtils.writeValueAsString(printerBackupsDOS));

        return save(printerBackupsDO);

    }
}

