package com.holder.saas.print.service;

import com.holder.saas.print.entity.domain.PrintRecordDO;
import com.holderzone.saas.store.dto.print.PrintRecordReqDTO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintPushService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface PrintPushService {

    void pushPrintTaskMsg(PrintDTO printDTO, List<String> arrayOfRecordGuid);

    void pushPrintSucceedMsg(PrintRecordReqDTO printRecordReqDTO, PrintRecordDO printRecordDO, int failedCount);

    void pushPrintFailedMsg(PrintRecordReqDTO printRecordReqDTO, PrintRecordDO printRecordDO, int failedCount);
}
