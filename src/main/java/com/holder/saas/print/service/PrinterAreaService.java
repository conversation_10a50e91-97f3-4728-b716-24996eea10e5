package com.holder.saas.print.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.print.entity.domain.PrinterAreaDO;
import com.holderzone.saas.store.dto.print.PrinterAreaDTO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.raw.PrinterAreaRawDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterAreaService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface PrinterAreaService extends IService<PrinterAreaDO> {

    void bindPrinterArea(PrinterDTO printerDTO);

    List<PrinterAreaDTO> listPrinterArea(PrinterDTO printerDTO);

    void deletePrinterArea(String printerGuid);

    void batchDeletePrinterArea(List<String> arrayOfPrinterGuid);

    void deleteStorePrinterArea(String storeGuid);

    void batchDeleteStorePrinterArea(List<String> arrayOfStoreGuid);

    List<PrinterAreaRawDTO> listRaw(String storeGuid);
}
