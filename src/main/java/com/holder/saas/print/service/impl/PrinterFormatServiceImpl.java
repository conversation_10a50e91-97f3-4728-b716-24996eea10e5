package com.holder.saas.print.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.print.entity.domain.PrinterFormatDO;
import com.holder.saas.print.mapper.InvoiceFormatMapper;
import com.holder.saas.print.mapstruct.PrinterMapstruct;
import com.holder.saas.print.mapstruct.PrinterRawMaptstruct;
import com.holder.saas.print.service.DistributedService;
import com.holder.saas.print.service.PrinterFormatService;
import com.holder.saas.print.utils.DeepCloneUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.format.PreCheckoutFormatDTO;
import com.holderzone.saas.store.dto.print.format.metadata.CustomMetadata;
import com.holderzone.saas.store.dto.print.raw.PrinterFormatRawDTO;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterAreaServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印机区域管理实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class PrinterFormatServiceImpl extends ServiceImpl<InvoiceFormatMapper, PrinterFormatDO> implements PrinterFormatService {

    private final DistributedService distributedService;

    private final PrinterMapstruct printerMapstruct;

    private final PrinterRawMaptstruct printerRawMaptstruct;

    @Value("${template.enable:false}")
    private boolean templateEnable;

    @Autowired
    public PrinterFormatServiceImpl(DistributedService distributedService, PrinterMapstruct printerMapstruct,
                                    PrinterRawMaptstruct printerRawMaptstruct) {
        this.distributedService = distributedService;
        this.printerMapstruct = printerMapstruct;
        this.printerRawMaptstruct = printerRawMaptstruct;
    }

    @Override
    public void add(FormatDTO formatDTO) {
        if (count(new LambdaQueryWrapper<PrinterFormatDO>()
                .ne(StringUtils.hasText(formatDTO.getGuid()),
                        PrinterFormatDO::getGuid, formatDTO.getGuid())
                .eq(PrinterFormatDO::getStoreGuid, formatDTO.getStoreGuid())
                .eq(PrinterFormatDO::getInvoiceType, formatDTO.getInvoiceType())) >= 3) {
            throw new BusinessException("最多可创建3个模板");
        }
        formatDTO.setDeviceId(null);
        PrinterFormatDO printerFormatDO = new PrinterFormatDO();
        printerFormatDO.setName(formatDTO.getName());
        FormatDTO objCloned = Objects.requireNonNull((FormatDTO) DeepCloneUtils.cloneObject(formatDTO));
        objCloned.setGuid(null);
        objCloned.setName(null);
        objCloned.setIsEnable(null);
        printerFormatDO.setFormatJsonString(JacksonUtils.writeValueAsString(objCloned));
        if (StringUtils.hasText(formatDTO.getGuid())) {
            update(printerFormatDO, new LambdaQueryWrapper<PrinterFormatDO>()
                    .eq(PrinterFormatDO::getGuid, formatDTO.getGuid())
                    .eq(PrinterFormatDO::getStoreGuid, formatDTO.getStoreGuid())
                    .eq(PrinterFormatDO::getInvoiceType, formatDTO.getInvoiceType()));
            return;
        }
        printerFormatDO.setStoreGuid(formatDTO.getStoreGuid());
        printerFormatDO.setInvoiceType(formatDTO.getInvoiceType());
        printerFormatDO.setIsEnable(false);
        save(printerFormatDO.setGuid(distributedService.nextInvoiceFormatId()));
    }

    @Override
    public List<FormatDTO> list(FormatDTO formatDTO) {
        Assert.notNull(formatDTO.getStoreGuid(), "门店GUID不得为空");
        Assert.notNull(formatDTO.getInvoiceType(), "单据类型不得为空");
        return list(formatDTO.getStoreGuid(), formatDTO.getInvoiceType());
    }

    @Override
    public boolean judgeEnablePreCheckFormat(String storeGuid) {
        if (storeGuid == null) {
            return false;
        }
        List<PrinterFormatDO> printerFormatList = list(new LambdaQueryWrapper<PrinterFormatDO>()
                .eq(PrinterFormatDO::getIsEnable, true)
                .eq(PrinterFormatDO::getStoreGuid, storeGuid)
                .eq(PrinterFormatDO::getInvoiceType, InvoiceTypeEnum.PRE_CHECKOUT.getType()));
        if (CollectionUtil.isEmpty(printerFormatList)) {
            return false;
        }
        PreCheckoutFormatDTO preCheckoutFormat = InvoiceTypeEnum.resolveFormatBy(printerFormatList.get(0).getFormatJsonString());
        log.info("preCheckoutFormat:{}", preCheckoutFormat);
        return preCheckoutFormat.getPayQrCode().isEnable();
    }

    @Override
    public void enable(FormatDTO formatDTO) {
        Assert.notNull(formatDTO.getStoreGuid(), "门店GUID不得为空");
        Assert.notNull(formatDTO.getInvoiceType(), "单据类型不得为空");
        update(new PrinterFormatDO().setIsEnable(false),
                new LambdaQueryWrapper<PrinterFormatDO>()
                        .eq(PrinterFormatDO::getStoreGuid, formatDTO.getStoreGuid())
                        .eq(PrinterFormatDO::getInvoiceType, formatDTO.getInvoiceType())
        );
        if (StringUtils.hasText(formatDTO.getGuid())) {
            update(new PrinterFormatDO().setIsEnable(true),
                    new LambdaQueryWrapper<PrinterFormatDO>().eq(PrinterFormatDO::getGuid, formatDTO.getGuid()));
        }
    }

    @Override
    public void delete(FormatDTO formatDTO) {
        Assert.notNull(formatDTO.getGuid(), "唯一标识不得为空");
        remove(new LambdaQueryWrapper<PrinterFormatDO>().eq(PrinterFormatDO::getGuid, formatDTO.getGuid()));
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T extends FormatDTO> T query(String storeGuid, Integer invoiceType) {
        InvoiceTypeEnum invoiceTypeEnum = InvoiceTypeEnum.ofType(invoiceType);
        invoiceTypeEnum = invoiceTypeEnum.getFormatReference();
        invoiceType = invoiceTypeEnum.getType();
        if (Objects.equals(InvoiceTypeEnum.ITEM_REPEAT_ORDER.getType(), invoiceType)) {
            invoiceType = InvoiceTypeEnum.ITEM_LIST.getType();
        }
        if (invoiceTypeEnum.isFormatSupported() && templateEnable) {
            // 查出可用模板，选第一个模板使用
            List<PrinterFormatDO> printerFormatDO = list(new LambdaQueryWrapper<PrinterFormatDO>()
                    .eq(PrinterFormatDO::getIsEnable, true)
                    .eq(PrinterFormatDO::getStoreGuid, storeGuid)
                    .eq(PrinterFormatDO::getInvoiceType, invoiceType)
                    .orderByDesc(PrinterFormatDO::getGmtModified));
            if (!CollectionUtils.isEmpty(printerFormatDO)) {
                return (T) InvoiceTypeEnum.resolveFormatBy(printerFormatDO.get(0).getFormatJsonString());
            }
        }
        return (T) InvoiceTypeEnum.defaultFormatBy(invoiceType);
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T extends FormatDTO> List<T> list(String storeGuid, Integer invoiceType) {
        List<T> results = new ArrayList<>();
        T defaultFormat = InvoiceTypeEnum.defaultFormatBy(invoiceType);
        defaultFormat.setIsEnable(true);
        results.add(defaultFormat);
        InvoiceTypeEnum invoiceTypeEnum = InvoiceTypeEnum.ofType(invoiceType);
        if (invoiceTypeEnum.isFormatSupported() && templateEnable) {
            List<T> collect = list(new LambdaQueryWrapper<PrinterFormatDO>()
                    .eq(PrinterFormatDO::getStoreGuid, storeGuid)
                    .eq(PrinterFormatDO::getInvoiceType, invoiceType)
                    .orderByAsc(PrinterFormatDO::getGmtCreate)).stream()
                    .map(printerFormatDO -> {
                        String json = printerFormatDO.getFormatJsonString();
                        T format = InvoiceTypeEnum.resolveFormatBy(json);
                        // 修正上一个版本的数据
                        if (!StringUtils.hasText(printerFormatDO.getGuid())) {
                            format.setGuid(distributedService.nextInvoiceFormatId());
                            format.setName("默认模板");
                            format.setIsEnable(false);
                            updateById(new PrinterFormatDO()
                                    .setId(printerFormatDO.getId())
                                    .setGuid(format.getGuid())
                                    .setName(format.getName())
                                    .setIsEnable(format.getIsEnable()));
                        } else {
                            format.setGuid(printerFormatDO.getGuid());
                            format.setName(printerFormatDO.getName());
                            format.setIsEnable(printerFormatDO.getIsEnable());
                        }
                        return format;
                    })
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)) {
                results.addAll(collect);
                defaultFormat.setIsEnable(collect.stream()
                        .map(FormatDTO::getIsEnable)
                        .noneMatch(Boolean.TRUE::equals)
                );
            }
        }
        return results;
    }

    @Override
    public List<String> getInvoiceUrls(String storeGuid) {
        List<PrinterFormatDO> list = list(new LambdaQueryWrapper<PrinterFormatDO>()
                .select(PrinterFormatDO::getFormatJsonString)
                .eq(PrinterFormatDO::getStoreGuid, storeGuid));
        return list.stream()
                .flatMap(printerFormatDO -> {
                    FormatDTO formatDTO = JacksonUtils.toObject(FormatDTO.class, printerFormatDO.getFormatJsonString());
                    List<String> urls = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(formatDTO.getHeaders())) {
                        formatDTO.getHeaders().stream()
                                .filter(customMetadata -> customMetadata.getType() == 1)
                                .map(CustomMetadata::getText).forEach(urls::add);
                    }
                    if (!CollectionUtils.isEmpty(formatDTO.getFooters())) {
                        formatDTO.getFooters().stream()
                                .filter(customMetadata -> customMetadata.getType() == 1)
                                .map(CustomMetadata::getText).forEach(urls::add);
                    }
                    return urls.stream().filter(StringUtils::hasText);
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<PrinterFormatRawDTO> listRaw(String storeGuid) {
        List<PrinterFormatDO> list = list(new LambdaQueryWrapper<PrinterFormatDO>()
                .eq(PrinterFormatDO::getStoreGuid, storeGuid));
        return printerRawMaptstruct.toFormatRawDTO(list);
    }
}

