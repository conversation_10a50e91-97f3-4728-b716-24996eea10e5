package com.holder.saas.print.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.print.entity.domain.PrinterTableDO;
import com.holder.saas.print.mapper.PrinterTableMapper;
import com.holder.saas.print.mapstruct.PrinterMapstruct;
import com.holder.saas.print.mapstruct.PrinterRawMaptstruct;
import com.holder.saas.print.service.DistributedService;
import com.holder.saas.print.service.PrinterTableService;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.PrinterTableDTO;
import com.holderzone.saas.store.dto.print.raw.PrinterTableRawDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 打印机区域桌台管理实现类
 */
@Slf4j
@Service
public class PrinterTableServiceImpl extends ServiceImpl<PrinterTableMapper, PrinterTableDO> implements PrinterTableService {

    private final DistributedService distributedService;

    private final PrinterMapstruct printerMapstruct;

    private final PrinterRawMaptstruct printerRawMaptstruct;

    @Autowired
    public PrinterTableServiceImpl(DistributedService distributedService, PrinterMapstruct printerMapstruct,
                                   PrinterRawMaptstruct printerRawMaptstruct) {
        this.distributedService = distributedService;
        this.printerMapstruct = printerMapstruct;
        this.printerRawMaptstruct = printerRawMaptstruct;
    }

    @Override
    public void bindPrinterAreaTable(PrinterDTO printerDTO) {
        List<PrinterTableDTO> arrayOfTableDTOList = printerDTO.getArrayOfTableDTO();
        if (CollectionUtils.isEmpty(arrayOfTableDTOList)) return;
        String storeGuid = printerDTO.getStoreGuid();
        String printerGuid = printerDTO.getPrinterGuid();
        List<String> batchGuids = distributedService.nextBatchPrinterAreaGuid(arrayOfTableDTOList.size());
        List<PrinterTableDO> arrayOfPrinterTableDO = arrayOfTableDTOList.stream()
                .map(tableDTO -> new PrinterTableDO()
                        .setStoreGuid(storeGuid)
                        .setPrinterGuid(printerGuid)
                        .setTableGuid(tableDTO.getTableGuid())
                        .setTableName(tableDTO.getTableName())
                        .setGuid(batchGuids.remove(batchGuids.size() - 1)))
                .collect(Collectors.toList());
        saveBatch(arrayOfPrinterTableDO);
    }

    @Override
    public List<PrinterTableDTO> listPrinterAreaTable(PrinterDTO printerDTO) {
        List<PrinterTableDO> arrayOfPrinterAreaDO = list(new LambdaQueryWrapper<PrinterTableDO>()
                .eq(PrinterTableDO::getPrinterGuid, printerDTO.getPrinterGuid()));
        if (CollectionUtils.isEmpty(arrayOfPrinterAreaDO)) return Collections.emptyList();
        return printerMapstruct.toPrinterAreaTableDTO(arrayOfPrinterAreaDO);
    }

    @Override
    public void deletePrinterAreaTable(String printerGuid) {
        remove(new LambdaQueryWrapper<PrinterTableDO>()
                .eq(PrinterTableDO::getPrinterGuid, printerGuid));
    }

    @Override
    public void batchDeletePrinterAreaTable(List<String> arrayOfPrinterGuid) {
        if (CollectionUtils.isEmpty(arrayOfPrinterGuid)) return;
        remove(new LambdaQueryWrapper<PrinterTableDO>()
                .in(PrinterTableDO::getPrinterGuid, arrayOfPrinterGuid));
    }

    @Override
    public void deleteStorePrinterAreaTable(String storeGuid) {
        remove(new LambdaQueryWrapper<PrinterTableDO>()
                .eq(PrinterTableDO::getStoreGuid, storeGuid));
    }

    @Override
    public List<PrinterTableRawDTO> listRaw(String storeGuid) {
        List<PrinterTableDO> list = list(new LambdaQueryWrapper<PrinterTableDO>()
                .eq(PrinterTableDO::getStoreGuid, storeGuid));
        return printerRawMaptstruct.toAreaTableRawDTO(list);
    }

}

