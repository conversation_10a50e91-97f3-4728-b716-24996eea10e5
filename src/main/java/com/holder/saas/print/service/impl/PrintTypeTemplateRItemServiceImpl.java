package com.holder.saas.print.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.print.entity.domain.type.PrintTypeTemplateRItemDO;
import com.holder.saas.print.mapper.PrintTypeTemplateRItemMapper;
import com.holder.saas.print.service.PrintTypeTemplateRItemService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 打印分类模版关联商品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Slf4j
@Service
@AllArgsConstructor
public class PrintTypeTemplateRItemServiceImpl extends ServiceImpl<PrintTypeTemplateRItemMapper, PrintTypeTemplateRItemDO>
        implements PrintTypeTemplateRItemService {

    private final PrintTypeTemplateRItemMapper rItemMapper;

    @Override
    public void removeByTemplateGuid(String templateGuid) {
        rItemMapper.delete(new LambdaQueryWrapper<PrintTypeTemplateRItemDO>()
                .eq(PrintTypeTemplateRItemDO::getTemplateGuid, templateGuid));
    }
}
