package com.holder.saas.print.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holder.saas.print.entity.biz.TraceContext;
import com.holder.saas.print.entity.domain.PrinterDO;
import com.holder.saas.print.entity.query.PrinterQuery;
import com.holder.saas.print.entity.read.PrinterReadDO;
import com.holder.saas.print.service.PrinterRoutingService;
import com.holder.saas.print.service.PrinterService;
import com.holder.saas.print.service.feign.ItemClientService;
import com.holder.saas.print.utils.PrintLogUtils;
import com.holder.saas.print.utils.valid.Asserts;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.item.req.ItemSpuReqDTO;
import com.holderzone.saas.store.dto.print.content.PrintBaseItemDTO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.enums.print.BusinessTypeEnum;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.enums.print.PrintSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterRoutingServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印机路由查找实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class PrinterRoutingServiceImpl implements PrinterRoutingService {

    private final PrinterService printerService;

    private final ItemClientService itemClientService;

    @Autowired
    public PrinterRoutingServiceImpl(PrinterService printerService, ItemClientService itemClientService) {
        this.printerService = printerService;
        this.itemClientService = itemClientService;
    }

    @Override
    public List<PrinterReadDO> findPrinterAvailable(PrintDTO printDto) {

        // 自己打印或指定主机打印
        adaptPrintSource(printDto);

        // 有且仅有正餐、微信点餐有区域/桌台ID
        String areaGuid = getCorrectAreaGuid(printDto);
        String tableGuid = getCorrectAreaTableGuid(printDto);

        // 有且仅有前台和标签打印机有设备ID
        String deviceId = getCorrectDeviceId(printDto);

        // 移除数量为0的菜
        removeItemThatNumberIsZeroIfNecessary(printDto);

        // 标签、后厨类型业务需要获取菜品Guid
        List<String> arrayOfItemGuidByBizType = getArrayOfItemGuidByBizType(printDto);
        log.info("arrayOfItemGuidByBizType={}", arrayOfItemGuidByBizType);

        PrinterQuery printerQuery = getPrinterQueryByCorrectParam(printDto, areaGuid, tableGuid, deviceId, arrayOfItemGuidByBizType);

        log.info("打印指定printDto信息:{}", JacksonUtils.writeValueAsString(printDto));

        log.info("printerQuery={}", JacksonUtils.writeValueAsString(printerQuery));
        List<PrinterReadDO> printerReadDOS = findPrinterByQuery(printDto, printerQuery);
        // 数据去重
        printerReadDOS = printerReadDOS.stream().distinct().collect(Collectors.toList());

        log.info("printerReadDOS={}", JacksonUtils.writeValueAsString(printerReadDOS));
        if (!CollectionUtils.isEmpty(printerReadDOS)) {
            return printerReadDOS;
        }

        if (PrintSourceEnum.POS.equals(printDto.getPrintSourceEnum())
                && InvoiceTypeEnum.ofType(printDto.getInvoiceType()).isFrontPrinter()) {
            // POS机未设置打印机，且为前台单据时返回虚拟前台打印机
            if (0 == printerService.count(new LambdaQueryWrapper<PrinterDO>()
                    .eq(PrinterDO::getStoreGuid, printerQuery.getStoreGuid())
                    .eq(PrinterDO::getDeviceId, printerQuery.getDeviceId()))) {
                return Collections.singletonList(findFakePrinterReadDO());
            }
        }

        return Collections.emptyList();
    }


    private List<PrinterReadDO> findPrinterByQuery(PrintDTO printDto, PrinterQuery printerQuery) {
        List<PrinterReadDO> printerReadDOS;
        if (InvoiceTypeEnum.TURN_TABLE_ITEM.getType().equals(printDto.getInvoiceType())) {
            // 如果是厨房转台单，特殊处理。因为现在不考虑转台所影响的菜品，能管理的打印机全部打印
            printerReadDOS = printerService.findPrinterOfKitchenTable(printerQuery);
        } else if (InvoiceTypeEnum.ITEM_LABEL.getType().equals(printDto.getInvoiceType())) {
            // 标签打印机 invoiceType = 100
            printerQuery.setInvoiceType(InvoiceTypeEnum.LABEL.getType());
            printerReadDOS = printerService.findPrinterByQuery(printerQuery);
        } else if (InvoiceTypeEnum.CHANGE_ITEM.getType().equals(printDto.getInvoiceType())) {
            printerQuery.setInvoiceType(null);
            printerQuery.setBusinessType(null);
            printerReadDOS = printerService.findPrinterByQuery(printerQuery);
            printerReadDOS = printerReadDOS.stream()
                    .filter(e -> !BusinessTypeEnum.LABEL_PRINTER.getType().equals(e.getBusinessType()))
                    .collect(Collectors.toList());
        } else if (InvoiceTypeEnum.DEBT_REPAYMENT.getType().equals(printDto.getInvoiceType())
                || InvoiceTypeEnum.TRANSFER_ITEM.getType().equals(printDto.getInvoiceType())
                || InvoiceTypeEnum.RESERVE_PAY_STATS.getType().equals(printDto.getInvoiceType())) {
            printerQuery.setInvoiceType(null);
            printerReadDOS = printerService.findPrinterByQuery(printerQuery);
        } else {
            printerReadDOS = printerService.findPrinterByQuery(printerQuery);
        }
        return printerReadDOS;
    }

    @Override
    public PrinterReadDO findFakePrinterReadDO() {
        PrinterReadDO printerReadDO = new PrinterReadDO();
        // 如需修改printerGuid，请同步修改PrintRecordMapper.xml的listByFakePrinterGuid查询语句
        printerReadDO.setPrinterGuid("000000");
        printerReadDO.setPrintPage("58");
        printerReadDO.setPrintCount(1);
        printerReadDO.setPrinterType(0);
        return printerReadDO;
    }

    @Override
    public PrinterDO findFakePrinterDO() {
        PrinterDO printerDO = new PrinterDO();
        printerDO.setBusinessType(0);
        printerDO.setPrinterType(0);
        printerDO.setPrinterName("本机");
        return printerDO;
    }

    @Override
    public List<PrinterReadDO> findCloudPrinterAvailable(PrintDTO printDTO) {
        // 标签、后厨类型业务需要获取菜品Guid
        List<String> arrayOfItemGuid = getArrayOfItemGuidByBizType(printDTO);
        log.info("[findCloudPrinterAvailable]arrayOfItemGuid={}", arrayOfItemGuid);

        PrinterQuery printerQuery = new PrinterQuery()
                .setStoreGuid(printDTO.getStoreGuid())
                .setInvoiceType(printDTO.getInvoiceType())
                .setArrayOfItemGuid(arrayOfItemGuid);
        log.info("[findCloudPrinterAvailable]printerQuery={}", JacksonUtils.writeValueAsString(printerQuery));
        List<PrinterReadDO> cloudPrinterList = printerService.findCloudPrinterByQuery(printerQuery);
        log.info("[findCloudPrinterAvailable]cloudPrinterList={}", JacksonUtils.writeValueAsString(cloudPrinterList));
        return cloudPrinterList;
    }

    private void adaptPrintSource(PrintDTO printDto) {
        // 打印来源为POS前台，自己打印
        if (PrintSourceEnum.POS == printDto.getPrintSourceEnum()
                && InvoiceTypeEnum.ofType(printDto.getInvoiceType()).isFrontPrinter()) {
            return;
        }
        // 打印来源为SELF前台，自己打印
        if (PrintSourceEnum.SELF == printDto.getPrintSourceEnum()
                && InvoiceTypeEnum.ofType(printDto.getInvoiceType()).isFrontPrinter()) {
            return;
        }
        // 打印来源为AIO，自己打印
        if (PrintSourceEnum.AIO == printDto.getPrintSourceEnum()) {
            return;
        }
        // 打印来源不为POS、AIO，查找主机作为打印机
        try {
            masterPrinterInstead(printDto);
        } catch (Exception e) {
            log.warn("[查询主机]异常={}e=", e.getMessage(), e);
        }
    }

    private void masterPrinterInstead(PrintDTO printDTO) {
        String masterPrinterDeviceId = printerService.findMasterPrinterDeviceIdOrElseThrow(printDTO.getStoreGuid());
        printDTO.setDeviceId(masterPrinterDeviceId);
        String searchKey = PrintLogUtils.searchKey(printDTO.getPrintUid(), printDTO.getInvoiceType());
        log.info("打印消息替打：context={}，searchKey={}，打印记录来源：{}，将使用主机：{} 作为打印设备",
                TraceContext.ids(), searchKey, printDTO.getPrintSourceEnum(), masterPrinterDeviceId);
    }

    private String getCorrectAreaGuid(PrintDTO printDto) {
        // 根据单据类型，返回就餐模式
        switch (InvoiceTypeEnum.getTradeMode(printDto)) {
            case DINE:
                // 预点餐可能没有区域id
                return printDto.getAreaGuid();
            case SNACK:
            case TAKEOUT:
            case NULL:
            default:
                return null;
        }
    }

    private String getCorrectAreaTableGuid(PrintDTO printDto) {
        // 根据单据类型，返回就餐模式
        switch (InvoiceTypeEnum.getTradeMode(printDto)) {
            case DINE:
                return printDto.getTableGuid();
            case SNACK:
            case TAKEOUT:
            case NULL:
            default:
                return null;
        }
    }

    private String getCorrectDeviceId(PrintDTO printDto) {
        // 根据票据类型获取打印业务类型
        switch (InvoiceTypeEnum.ofType(printDto.getInvoiceType()).getBusinessType()) {
            case FRONT_PRINTER:
            case LABEL_PRINTER:
                String deviceId = printDto.getDeviceId();
                Asserts.create().notEmpty(deviceId, "deviceId");
                return deviceId;
            case KITCHEN_PRINTER:
            default:
                return null;
        }
    }

    private void removeItemThatNumberIsZeroIfNecessary(PrintDTO printDTO) {
        if (printDTO instanceof PrintBaseItemDTO) {
            PrintBaseItemDTO basePrintBaseItemDto =
                    (PrintBaseItemDTO) printDTO;
            if (basePrintBaseItemDto.getItemRecordList() != null) {
                basePrintBaseItemDto.getItemRecordList()
                        .removeIf(itemRecord -> itemRecord.getNumber().compareTo(BigDecimal.ZERO) <= 0);
            }
            if (CollectionUtils.isEmpty(basePrintBaseItemDto.getItemRecordList())) {
                // 如果是退款单，允许没有菜品的情况
                if (Objects.equals(printDTO.getInvoiceType(), InvoiceTypeEnum.REFUND_INVOICE.getType())) {
                    return;
                }
                throw new BusinessException("所有菜品数量均为0，无法打印，" +
                        "enterpriseGuid=" + printDTO.getEnterpriseGuid() + "，" +
                        "storeGuid=" + printDTO.getStoreGuid()
                );
            }
        }
    }

    /**
     * 只有后厨和标签打印机需要获取菜品的Guid
     *
     * @param printDto
     * @return
     */
    private List<String> getArrayOfItemGuidByBizType(PrintDTO printDto) {
        InvoiceTypeEnum invoiceType = InvoiceTypeEnum.ofType(printDto.getInvoiceType());
        if (invoiceType == InvoiceTypeEnum.TURN_TABLE_ITEM) {
            // 后厨转台单：二期暂时无关菜品，后期需加入菜品条件判断
            return null;
        }
        BusinessTypeEnum businessType = invoiceType.getBusinessType();
        switch (businessType) {
            case FRONT_PRINTER: {
                return null;
            }
            case COMBINATION:
            case KITCHEN_PRINTER:
            case LABEL_PRINTER: {
                return getPrintItemGuids(printDto);
            }
            default:
                throw new BusinessException("目前仅支持前台、后厨、标签");
        }
    }

    /**
     * 获取打印的商品Guid
     *
     * @param printDto 打印请求model
     * @return 打印的商品Guid
     */
    private List<String> getPrintItemGuids(PrintDTO printDto) {
        PrintBaseItemDTO basePrintBaseItemDto = (PrintBaseItemDTO) printDto;
        log.info("basePrintBaseItemDto={}", JacksonUtils.writeValueAsString(basePrintBaseItemDto));
        List<PrintItemRecord> printItemRecords = basePrintBaseItemDto.getItemRecordList();
        if (CollectionUtils.isEmpty(printItemRecords)) {
            throw new BusinessException("菜品列表为空，无法打印");
        }
        List<String> arrayOfItemGuid = getAllItemGuidIncludeSubItemRecord(printItemRecords);
        if (CollectionUtils.isEmpty(arrayOfItemGuid)) {
            throw new BusinessException("打印的商品信息为空");
        }
        //通过商品guid查询spu相同的商品信息
        ItemSpuReqDTO reqDTO = new ItemSpuReqDTO();
        reqDTO.setStoreGuid(printDto.getStoreGuid());
        reqDTO.setItemGuids(arrayOfItemGuid);
        log.info("reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        arrayOfItemGuid = itemClientService.selectSpuItems(reqDTO);
        if (CollectionUtils.isEmpty(arrayOfItemGuid)) {
            throw new BusinessException("打印的商品信息为空");
        }
        return arrayOfItemGuid;
    }

    /**
     * 获取打印记录所有菜品的Guid
     *
     * @param printItemRecords
     * @return
     */
    private List<String> getAllItemGuidIncludeSubItemRecord(List<PrintItemRecord> printItemRecords) {
        return printItemRecords.stream()
                .map(itemRecord -> {
                    List<PrintItemRecord> subPrintItemRecords = itemRecord.getSubItemRecords();
                    if (CollectionUtils.isEmpty(subPrintItemRecords)) {
                        return Collections.singletonList(itemRecord.getItemGuid());
                    }
                    return subPrintItemRecords.stream()
                            .map(PrintItemRecord::getItemGuid)
                            .collect(Collectors.toList());
                })
                .reduce(new ArrayList<>(), (itemGuids, strings) -> {
                    itemGuids.addAll(strings);
                    return itemGuids;
                });
    }

    private PrinterQuery getPrinterQueryByCorrectParam(PrintDTO printDto, String areaGuid, String tableGuid,
                                                       String deviceId, List<String> arrayOfItemGuid) {
        return new PrinterQuery()
                .setStoreGuid(printDto.getStoreGuid())
                .setInvoiceType(printDto.getInvoiceType())
                .setTradeMode(printDto.getTradeMode())
                .setPartRefundFlag(printDto.getPartRefundFlag())
                .setAreaGuid(areaGuid)
                .setTableGuid(tableGuid)
                .setDeviceId(deviceId)
                .setArrayOfItemGuid(arrayOfItemGuid)
                //设置打印机业务类型（前台/后厨）
                .setBusinessType(InvoiceTypeEnum.ofType(printDto.getInvoiceType()).getBizType());
    }
}
