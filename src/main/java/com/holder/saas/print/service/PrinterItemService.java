package com.holder.saas.print.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.print.entity.domain.PrinterItemDO;
import com.holderzone.saas.store.dto.print.PrinterItemDTO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.raw.PrinterItemRawDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterItemService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface PrinterItemService extends IService<PrinterItemDO> {

    void bindPrinterItem(PrinterDTO printerDTO);

    List<PrinterItemDTO> listPrinterItem(PrinterDTO printerDTO);

    void deletePrinterItem(String printerGuid);

    void batchDeletePrinterItem(List<String> arrayOfPrinterGuid);

    void deleteStorePrinterItem(String storeGuid);

    void batchDeleteStorePrinterItem(List<String> arrayOfStoreGuid);

    List<PrinterItemRawDTO> listRaw(String storeGuid);
}
