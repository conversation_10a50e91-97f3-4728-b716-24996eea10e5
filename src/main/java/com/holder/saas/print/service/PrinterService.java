package com.holder.saas.print.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.print.entity.domain.PrinterDO;
import com.holder.saas.print.entity.query.PrinterQuery;
import com.holder.saas.print.entity.read.PrinterReadDO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.PrinterQueryDTO;
import com.holderzone.saas.store.dto.print.cloud.CloudPrinterDTO;
import com.holderzone.saas.store.dto.print.raw.PrinterRawDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface PrinterService extends IService<PrinterDO> {

    String addPrinter(PrinterDTO printerDTO);

    PrinterDTO queryPrinter(PrinterDTO printerDTO);

    List<PrinterDTO> listPrinterOfTheDevice(PrinterDTO printerDTO);

    List<PrinterDTO> listPrinterOfBizType(PrinterDTO printerDTO);

    void updatePrinter(PrinterDTO printerDTO);

    void deletePrinter(PrinterDTO printerDTO);

    void deletePrinterOfTheStore(PrinterDTO printerDTO);

    void deletePrinterOfTheDevice(PrinterDTO printerDTO);

    void changeMasterPrinter(PrinterDTO printerDTO);

    PrinterDTO findTestOrderPrinter(String storeGuid, String deviceId, Integer invoiceType, Integer pageSize);

    List<PrinterDTO> findTestOrderPrinters(String storeGuid, String deviceId, Integer invoiceType);

    PrinterDO findMasterPrinter(String storeGuid);

    String findMasterPrinterDeviceId(String storeGuid);

    String findMasterPrinterDeviceIdOrElseThrow(String storeGuid);

    boolean isMasterPrinterDevice(String storeGuid, String deviceId);

    List<PrinterReadDO> findPrinterByQuery(PrinterQuery printerQuery);

    List<PrinterReadDO> findPrinterOfKitchenTable(PrinterQuery printerQuery);

    List<PrinterRawDTO> listRaw(String storeGuid);

    boolean backupsPrinter(String deviceId);

    boolean restorePrinter(String deviceId);

    String backupsPrinterTime(String deviceId);

    void deletePrinterList(PrinterDTO printerDTO);

    List<PrinterDTO> queryByCondition(PrinterQueryDTO queryDTO);

    void addCloud(CloudPrinterDTO cloudPrinterDTO);

    void testPrint(CloudPrinterDTO cloudPrinterDTO);

    void updateCloudPrinter(CloudPrinterDTO cloudPrinterDTO);

    void checkPrinter(CloudPrinterDTO cloudPrinterDTO);

    List<PrinterReadDO> findCloudPrinterByQuery(PrinterQuery printerQuery);

    List<PrinterDTO> listPrinter(SingleDataDTO request);

    void batchUpdatePrinter(PrinterDTO printerDTO);

    /**
     * 根据打印Id和商品Id查询商品信息
     *
     * @param printerDTO 请求
     * @return 打印及绑定商品信息
     */
    List<String> listPrinterByPrinterIdAndItemId(PrinterDTO printerDTO);
}
