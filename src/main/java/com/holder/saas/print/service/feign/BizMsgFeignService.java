package com.holder.saas.print.service.feign;

import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BizMsgFeignService
 * @date 2018/02/14 09:00
 * @description 调用业务消息服务
 * @program holder-saas-store-print
 */
@Component
@FeignClient(value = "holder-saas-store-message", fallbackFactory = BizMsgFeignService.DefaultFallbackFactory.class)
public interface BizMsgFeignService {

    /**
     * 接受消息的接口, 返回success，表示推送成功
     * 打印失败推送语音消息走此服务
     *
     * @param businessMessageDTO
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "msg")
    String msg(@RequestBody BusinessMessageDTO businessMessageDTO);

    @Component
    class DefaultFallbackFactory implements FallbackFactory<BizMsgFeignService> {

        private static final Logger LOGGER = LoggerFactory.getLogger(DefaultFallbackFactory.class);

        private static final String HYSTRIX_RESULT = "调用业务消息服务熔断！";

        @Override
        public BizMsgFeignService create(Throwable throwable) {

            return new BizMsgFeignService() {
                @Override
                public String msg(BusinessMessageDTO businessMessageDTO) {
                    LOGGER.error(HYSTRIX_RESULT + "错误原因: throwable=" + throwable.getMessage());
                    return "";
                }
            };
        }
    }

}
