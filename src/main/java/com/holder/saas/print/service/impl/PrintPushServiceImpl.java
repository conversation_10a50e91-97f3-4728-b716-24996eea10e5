package com.holder.saas.print.service.impl;

import com.holder.saas.print.entity.biz.TraceContext;
import com.holder.saas.print.entity.domain.PrintRecordDO;
import com.holder.saas.print.service.PrintPushService;
import com.holder.saas.print.service.feign.PushMsgFeignService;
import com.holder.saas.print.template.base.PrintTemplateAware;
import com.holder.saas.print.template.factory.PrintTemplateFactory;
import com.holder.saas.print.utils.PrintLogUtils;
import com.holder.saas.print.utils.SnowFlakeUtil;
import com.holderzone.framework.base.dto.message.*;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.print.PrintRecordReqDTO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintPushServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印消息推送实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class PrintPushServiceImpl implements PrintPushService {

    private static final String MESSAGE_TOPIC = "print:";

    private final PushMsgFeignService pushMsgFeignService;

    private final PrintTemplateFactory printTemplateFactory;

    @Value("${batch-push.enable:false}")
    private boolean batchPushEnable;

    @Autowired
    public PrintPushServiceImpl(PushMsgFeignService pushMsgFeignService, PrintTemplateFactory printTemplateFactory) {
        this.pushMsgFeignService = pushMsgFeignService;
        this.printTemplateFactory = printTemplateFactory;
    }

    @Async
    @Override
    public void pushPrintTaskMsg(PrintDTO printDTO, List<String> arrayOfRecordGuid) {
        log.info("打印任务消息推送，处理中，arrayOfRecordGuid={}", arrayOfRecordGuid);
        if (batchPushEnable) {
            MessageDTO messageDTO = buildPrintTaskDetail(printDTO.getDeviceId(),
                    printDTO.getStoreGuid(), printDTO.getEnterpriseGuid(), null, arrayOfRecordGuid);
            pushMsgFeignService.sendPrintMessage(messageDTO);
        } else {
            for (String recordGuid : arrayOfRecordGuid) {
                MessageDTO messageDTO = buildPrintTaskDetail(printDTO.getDeviceId(),
                        printDTO.getStoreGuid(), printDTO.getEnterpriseGuid(), recordGuid, null);
                pushMsgFeignService.sendPrintMessage(messageDTO);
            }
        }
        log.info("打印任务消息推送，处理完毕, arrayOfRecordGuid={}", arrayOfRecordGuid);
    }

    @Async
    @Override
    public void pushPrintSucceedMsg(PrintRecordReqDTO printRecordReqDTO, PrintRecordDO printRecordDO, int failedCount) {
        Integer invoiceType = printRecordDO.getInvoiceType();
        String printContent = printRecordDO.getPrintContent();
        PrintDTO printDTO = InvoiceTypeEnum.resolvePrintBy(invoiceType, printContent);
        String context = TraceContext.device(printDTO.getDeviceId());
        String searchKey = PrintLogUtils.searchKey(printDTO.getPrintUid(), printDTO.getInvoiceType());
        log.info("打印成功消息推送，处理中，context={}，searchKey={}，recordGuid={}",
                context, searchKey, printRecordReqDTO.getRecordGuid());
        MessageDTO messageDTO = buildFailedNumberDetail(printRecordReqDTO.getDeviceId(), "", failedCount,
                printDTO.getStoreGuid(), printDTO.getEnterpriseGuid(), "");
        pushMsgFeignService.sendPrintMessage(messageDTO);
        log.info("打印成功消息推送，处理成功，context={}，searchKey={}，推送内容：{}",
                context, searchKey, JacksonUtils.writeValueAsString(messageDTO));
    }

    @Async
    @Override
    public void pushPrintFailedMsg(PrintRecordReqDTO printRecordReqDTO, PrintRecordDO printRecordDO, int failedCount) {
        Integer invoiceType = printRecordDO.getInvoiceType();
        String printContent = printRecordDO.getPrintContent();
        PrintTemplateAware<? extends PrintDTO, ? extends FormatDTO> printTemplate = printTemplateFactory.create(invoiceType, printContent);
        PrintDTO printDTO = printTemplate.getPrintDTO();
        String printFailedMessage = printTemplate.getFailedMsg();
        String context = TraceContext.device(printDTO.getDeviceId());
        String searchKey = PrintLogUtils.searchKey(printDTO.getPrintUid(), printDTO.getInvoiceType());
        log.info("打印失败消息推送，处理中，context={}，searchKey={}，recordGuid={}",
                context, searchKey, printRecordReqDTO.getRecordGuid());
        MessageDTO messageDTO = buildFailedNumberDetail(printRecordReqDTO.getDeviceId(), printFailedMessage, failedCount,
                printDTO.getStoreGuid(), printDTO.getEnterpriseGuid(), printRecordReqDTO.getRecordGuid());
        pushMsgFeignService.sendPrintMessage(messageDTO);
        log.info("打印失败消息推送，处理成功，context={}，searchKey={}，推送内容：{}",
                context, searchKey, JacksonUtils.writeValueAsString(messageDTO));
    }

    private MessageDTO buildPrintTaskDetail(String deviceId, String storeGuid, String enterpriseGuid,
                                            String recordGuid, List<String> arrayOfRecordGuid) {
        Map<String, Object> detailMap = new HashMap<>();
        detailMap.put("recordGuid", recordGuid);
        detailMap.put("arrayOfRecordGuid", arrayOfRecordGuid);
        detailMap.put("msgId", String.valueOf(SnowFlakeUtil.getInstance().nextId()));
        return buildMessage(deviceId, detailMap, storeGuid, enterpriseGuid);
    }

    private MessageDTO buildFailedNumberDetail(String deviceId, String msg, int count,
                                               String storeGuid, String enterpriseGuid, String recordGuid) {
        Map<String, Object> map = new HashMap<>();
        map.put("msg", msg);
        map.put("number", String.valueOf(count));
        map.put("failedKey", recordGuid);
        return buildMessage(deviceId, map, storeGuid, enterpriseGuid);
    }

    private MessageDTO buildMessage(String deviceId, Map<String, Object> detailMap, String storeGuid, String enterpriseGuid) {
        PushMessageDTO pushMessageDTO = new PushMessageDTO();
        pushMessageDTO.setTopicType(TopicType.BUSINESS);
        BusinessMessage businessMessage = new BusinessMessage();
        businessMessage.setBusinessType(MESSAGE_TOPIC + deviceId);
        businessMessage.setStoreGuid(storeGuid);
        businessMessage.setEnterpriseGuid(enterpriseGuid);
        pushMessageDTO.setBusinessMessage(businessMessage);
        pushMessageDTO.setData(JacksonUtils.writeValueAsString(detailMap));
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setMessageType(MessageType.PUSH);
        messageDTO.setPushMessage(pushMessageDTO);
        return messageDTO;
    }
}
