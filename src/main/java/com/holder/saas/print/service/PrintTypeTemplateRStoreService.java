package com.holder.saas.print.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.print.entity.domain.type.PrintTypeTemplateRStoreDO;
import com.holderzone.saas.store.dto.print.type.TemplateStoreVO;

import java.util.List;

/**
 * <p>
 * 打印分类模版关联门店表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
public interface PrintTypeTemplateRStoreService extends IService<PrintTypeTemplateRStoreDO> {

    void removeByTemplateGuid(String templateGuid);

    List<TemplateStoreVO> queryTemplateStoreVOList(Boolean isAllStore, String templateGuid);

    int queryRepeatTemplateCount(String brandGuid, List<String> storeGuidList,
                                 List<String> invoiceTypeList, String guid);

    List<PrintTypeTemplateRStoreDO> queryTemplateStoreGuidList(String brandGuid, List<String> invoiceTypeList, String templateGuid);

    PrintTypeTemplateRStoreDO queryTemplateByInvoiceTypeAndStoreGuid(String invoiceType, String storeGuid);
}
