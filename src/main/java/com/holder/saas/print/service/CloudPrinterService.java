package com.holder.saas.print.service;

import com.holderzone.saas.store.dto.print.cloud.FeieRespDTO;

/**
 * <AUTHOR>
 * @date 2024/3/26
 * @description 云打印机对接
 */
public interface CloudPrinterService {

    void addPrinter(String deviceNo, String deviceKey, String printerName);

    void deletePrinter(String deviceNo);

    FeieRespDTO queryPrinterInfo(String deviceNo);

    void testPrinter(String deviceNo, Integer times);

    void print(String content, String deviceNo, Integer printCount);
}
