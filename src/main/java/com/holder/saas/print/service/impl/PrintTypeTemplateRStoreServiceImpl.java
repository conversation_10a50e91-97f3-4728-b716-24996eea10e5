package com.holder.saas.print.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holder.saas.print.entity.domain.type.PrintTypeTemplateRStoreDO;
import com.holder.saas.print.mapper.PrintTypeTemplateRStoreMapper;
import com.holder.saas.print.service.PrintTypeTemplateRStoreService;
import com.holder.saas.print.service.feign.StoreDeviceFeignService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.print.type.TemplateStoreVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 打印分类模版关联门店表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Slf4j
@Service
@AllArgsConstructor
public class PrintTypeTemplateRStoreServiceImpl extends ServiceImpl<PrintTypeTemplateRStoreMapper, PrintTypeTemplateRStoreDO>
        implements PrintTypeTemplateRStoreService {

    private final PrintTypeTemplateRStoreMapper rStoreMapper;

    private final StoreDeviceFeignService storeDeviceFeignService;

    @Override
    public void removeByTemplateGuid(String templateGuid) {
        rStoreMapper.delete(new LambdaQueryWrapper<PrintTypeTemplateRStoreDO>()
                .eq(PrintTypeTemplateRStoreDO::getTemplateGuid, templateGuid));
    }

    @Override
    public List<TemplateStoreVO> queryTemplateStoreVOList(Boolean isAllStore, String templateGuid) {
        List<TemplateStoreVO> storeList = Lists.newArrayList();
        if (!isAllStore) {
            List<PrintTypeTemplateRStoreDO> rStoreDOList = this.list(
                    new LambdaQueryWrapper<PrintTypeTemplateRStoreDO>()
                            .eq(PrintTypeTemplateRStoreDO::getTemplateGuid, templateGuid)
                            .eq(PrintTypeTemplateRStoreDO::getIsDelete, Boolean.FALSE)
            );
            List<String> storeGuidList = rStoreDOList.stream()
                    .map(PrintTypeTemplateRStoreDO::getStoreGuid)
                    .distinct()
                    .collect(Collectors.toList());
            SingleDataDTO singleDataDTO = new SingleDataDTO();
            singleDataDTO.setDatas(storeGuidList);
            List<StoreDTO> storeDTOList = storeDeviceFeignService.queryStoreByIdList(singleDataDTO);
            Map<String, StoreDTO> storeDTOMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(storeDTOList)) {
                storeDTOMap = storeDTOList.stream()
                        .collect(Collectors.toMap(StoreDTO::getGuid, Function.identity(), (oldValue, newValue) -> oldValue));
            }
            for (String storeGuid : storeGuidList) {
                TemplateStoreVO storeVO = new TemplateStoreVO();
                storeVO.setGuid(storeGuid);
                StoreDTO storeDTO = storeDTOMap.get(storeGuid);
                if (!ObjectUtils.isEmpty(storeDTO)) {
                    storeVO.setName(storeDTO.getName());
                    storeList.add(storeVO);
                }
            }
        }
        return storeList;
    }

    @Override
    public int queryRepeatTemplateCount(String brandGuid, List<String> storeGuidList, List<String> invoiceTypeList, String guid) {
        return rStoreMapper.queryRepeatTemplateCount(brandGuid, storeGuidList, invoiceTypeList, guid);
    }

    @Override
    public List<PrintTypeTemplateRStoreDO> queryTemplateStoreGuidList(String brandGuid, List<String> invoiceTypeList, String templateGuid) {
        return rStoreMapper.queryTemplateStoreGuidList(brandGuid, invoiceTypeList, templateGuid);
    }

    @Override
    public PrintTypeTemplateRStoreDO queryTemplateByInvoiceTypeAndStoreGuid(String invoiceType, String storeGuid) {
        return rStoreMapper.queryTemplateByInvoiceTypeAndStoreGuid(invoiceType, storeGuid);
    }
}
