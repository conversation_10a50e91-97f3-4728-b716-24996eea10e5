package com.holder.saas.print.service.impl;

import com.holder.saas.print.config.PrinterConfig;
import com.holder.saas.print.service.CloudPrinterService;
import com.holder.saas.print.utils.HttpRequestUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.print.cloud.FeieRespDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/3/26
 * @description 飞蛾打印机对接
 */
@Slf4j
@Service
@AllArgsConstructor
public class FeiePrinterServiceImpl implements CloudPrinterService {

    public static final String API_NAME = "apiname";

    private final PrinterConfig printerConfig;

    @Override
    public void addPrinter(String deviceNo, String deviceKey, String printerName) {
        String printerContent = deviceNo + "#" + deviceKey + "#" + printerName;
        String result = requestAddPrinter(printerContent);
        log.info("[addPrinter]result={}", result);
        handleResult(result);
    }

    private String requestAddPrinter(String printerContent) {
        List<NameValuePair> addPair = buildNameValuePairs();
        addPair.add(new BasicNameValuePair(API_NAME, "Open_printerAddlist"));
        addPair.add(new BasicNameValuePair("printerContent", printerContent));
        return HttpRequestUtils.requestPost(printerConfig.getUrl(), addPair);
    }

    @NotNull
    private List<NameValuePair> buildNameValuePairs() {
        List<NameValuePair> pairs = new ArrayList<>();
        pairs.add(new BasicNameValuePair("user", printerConfig.getUser()));
        String nowTime = String.valueOf(System.currentTimeMillis() / 1000);
        pairs.add(new BasicNameValuePair("stime", nowTime));
        pairs.add(new BasicNameValuePair("sig", signature(printerConfig.getUser(), printerConfig.getUKey(), nowTime)));
        return pairs;
    }

    /**
     * 生成签名字符串
     */
    private static String signature(String user, String uKey, String nowTime) {
        return DigestUtils.sha1Hex(user + uKey + nowTime);
    }

    @Override
    public void deletePrinter(String deviceNo) {
        String deleteResult = requestDeletePrinter(deviceNo);
        log.info("[deletePrinter]deleteResult={}", deleteResult);
        handleResult(deleteResult);
    }

    private String requestDeletePrinter(String deviceNo) {
        List<NameValuePair> deletePair = buildNameValuePairs();
        deletePair.add(new BasicNameValuePair(API_NAME, "Open_printerDelList"));
        deletePair.add(new BasicNameValuePair("snlist", deviceNo));
        return HttpRequestUtils.requestPost(printerConfig.getUrl(), deletePair);
    }

    @Override
    public FeieRespDTO queryPrinterInfo(String deviceNo) {
        String queryResult = requestQueryPrinterInfo(deviceNo);
        log.info("[queryPrinterInfo]queryResult={}", queryResult);
        return handleResult(queryResult);
    }

    private String requestQueryPrinterInfo(String deviceNo) {
        List<NameValuePair> queryPair = buildNameValuePairs();
        queryPair.add(new BasicNameValuePair(API_NAME, "Open_printerInfo"));
        queryPair.add(new BasicNameValuePair("sn", deviceNo));
        return HttpRequestUtils.requestPost(printerConfig.getUrl(), queryPair);
    }

    private FeieRespDTO handleResult(String result) {
        if (StringUtils.isEmpty(result)) {
            throw new BusinessException("飞蛾打印机请求异常");
        }
        FeieRespDTO feieRespDTO = JacksonUtils.toObject(FeieRespDTO.class, result);
        if (ObjectUtils.isEmpty(feieRespDTO)) {
            throw new BusinessException("飞蛾打印机请求异常");
        }
        if (!Objects.equals(0, feieRespDTO.getRet())) {
            throw new BusinessException(feieRespDTO.getMsg());
        }
        FeieRespDTO.DataMsg dataMsg = feieRespDTO.getData();
        if (Objects.nonNull(dataMsg) && !CollectionUtils.isEmpty(dataMsg.getNo())) {
            throw new BusinessException("设备编号或设备秘钥错误");
        }
        return feieRespDTO;
    }

    @Override
    public void testPrinter(String deviceNo, Integer times) {
        String content = "<CB>测试打印</CB><BR>";
        String testResult = requestTestPrinter(deviceNo, content, times);
        log.info("[testPrinter]testResult={}", testResult);
    }

    @Override
    public void print(String content, String deviceNo, Integer printCount) {
        String printResult = requestTestPrinter(deviceNo, content, printCount);
        log.info("[print]printResult={}", printResult);
    }

    private String requestTestPrinter(String deviceNo, String content, Integer times) {
        List<NameValuePair> queryPair = buildNameValuePairs();
        queryPair.add(new BasicNameValuePair(API_NAME, "Open_printMsg"));
        queryPair.add(new BasicNameValuePair("sn", deviceNo));
        queryPair.add(new BasicNameValuePair("content", content));
        queryPair.add(new BasicNameValuePair("times", String.valueOf(times)));
        return HttpRequestUtils.requestPost(printerConfig.getUrl(), queryPair);
    }

}
