package com.holder.saas.print.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.print.entity.domain.type.PrintTypeTemplateDO;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.SingleDataPageDTO;
import com.holderzone.saas.store.dto.print.type.*;

import java.util.List;

/**
 * <p>
 * 打印分类模版表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
public interface PrintTypeTemplateService extends IService<PrintTypeTemplateDO> {

    void create(PrintTypeTemplateDTO createDTO);

    Page<PrintTypeTemplateVO> queryPage(SingleDataPageDTO query);

    PrintTypeTemplateDetailDTO queryDetail(SingleDataDTO query);

    void modify(PrintTypeTemplateDTO modifyDTO);

    boolean enable(PrintTypeTemplateEnableDTO enableDTO);

    boolean delete(SingleDataDTO deleteDTO);

    PrintTypeTemplateDetailDTO queryByStoreAndInvoiceType(TemplateDetailQO query);

    List<String> queryStoreByBrand(TemplateStoreQO query);

    Boolean checkTemplateName(PrintTypeTemplateDTO query);
}
