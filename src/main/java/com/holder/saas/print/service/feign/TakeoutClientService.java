package com.holder.saas.print.service.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * <AUTHOR>
 * @date 2024/3/24
 * @description 外卖远程
 */
@Component
@FeignClient(name = "holder-saas-takeaway-consumer", fallbackFactory = TakeoutClientService.ServiceFallBack.class)
public interface TakeoutClientService {

    @GetMapping("/takeout/get_order/mapping/{orderGuid}")
    TakeoutOrderDTO getOrderDetailMapping(@PathVariable String orderGuid);

    @ApiOperation(value = "订单编号查询外卖订单详情", notes = "查询外卖订单详情")
    @GetMapping("/takeout/get_order_by_orderNo")
    TakeoutOrderDTO getOrderByOrderNo(@RequestParam("orderNo") String orderNo,
                                      @RequestParam("storeGuid") String storeGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<TakeoutClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public TakeoutClientService create(Throwable cause) {
            return new TakeoutClientService() {

                @Override
                public TakeoutOrderDTO getOrderDetailMapping(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "getOrderDetailMapping", orderGuid, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TakeoutOrderDTO getOrderByOrderNo(String orderNo, String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "getOrderByOrderNo", orderNo + "-" + storeGuid,
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

            };
        }
    }
}
