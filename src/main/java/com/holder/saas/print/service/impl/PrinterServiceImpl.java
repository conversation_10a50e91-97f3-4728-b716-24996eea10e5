package com.holder.saas.print.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holder.saas.print.entity.domain.PrinterBackupsDO;
import com.holder.saas.print.entity.domain.PrinterDO;
import com.holder.saas.print.entity.enums.PrintCutEnum;
import com.holder.saas.print.entity.query.PrinterQuery;
import com.holder.saas.print.entity.read.PrinterReadDO;
import com.holder.saas.print.mapper.PrinterMapper;
import com.holder.saas.print.mapstruct.PrinterMapstruct;
import com.holder.saas.print.mapstruct.PrinterRawMaptstruct;
import com.holder.saas.print.service.*;
import com.holder.saas.print.service.feign.ItemClientService;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.ItemSpuReqDTO;
import com.holderzone.saas.store.dto.print.*;
import com.holderzone.saas.store.dto.print.cloud.CloudPrinterDTO;
import com.holderzone.saas.store.dto.print.cloud.DeviceUseDTO;
import com.holderzone.saas.store.dto.print.cloud.FeieRespDTO;
import com.holderzone.saas.store.dto.print.raw.PrinterRawDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.print.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.Collections.emptyList;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印机配置实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
@Transactional
public class PrinterServiceImpl extends ServiceImpl<PrinterMapper, PrinterDO> implements PrinterService {

    private final PrinterMapstruct printerMapstruct;

    private final PrinterRawMaptstruct printerRawMaptstruct;

    private final StoreDeviceService storeDeviceService;

    private final PrinterInvoiceService printerInvoiceService;

    private final PrinterItemService printerItemService;

    private final PrinterAreaService printerAreaService;

    private final PrinterTableService printerTableService;

    private final PrinterLogService printerLogService;

    private final DistributedService distributedService;

    private final PrintRecordService printRecordService;

    private final PrinterBackupsService printerBackupsService;

    private final CloudPrinterService cloudPrinterService;

    private final ItemClientService itemClientService;

    @Autowired
    public PrinterServiceImpl(PrinterMapstruct printerMapstruct, PrinterRawMaptstruct printerRawMaptstruct,
                              StoreDeviceService storeDeviceService,
                              PrinterInvoiceService printerInvoiceService, PrinterAreaService printerAreaService,
                              PrinterTableService printerTableService,
                              PrinterItemService printerItemService, PrinterLogService printerLogService,
                              PrinterBackupsService printerBackupsService,
                              DistributedService distributedService, @Lazy PrintRecordService printRecordService,
                              CloudPrinterService cloudPrinterService, ItemClientService itemClientService) {
        this.printerMapstruct = printerMapstruct;
        this.printerRawMaptstruct = printerRawMaptstruct;
        this.storeDeviceService = storeDeviceService;
        this.printerInvoiceService = printerInvoiceService;
        this.printerAreaService = printerAreaService;
        this.printerTableService = printerTableService;
        this.printerItemService = printerItemService;
        this.printerLogService = printerLogService;
        this.distributedService = distributedService;
        this.printRecordService = printRecordService;
        this.printerBackupsService = printerBackupsService;
        this.cloudPrinterService = cloudPrinterService;
        this.itemClientService = itemClientService;
    }

    private static List<PrinterDTO> getPrinterDTOList(List<PrinterReadDO> readDOList) {
        if (CollectionUtils.isEmpty(readDOList)) {
            return emptyList();
        }
        List<PrinterDTO> printerDTOList = new ArrayList<>();
        readDOList.forEach(readDO -> {
            PrinterDTO printerDTO = new PrinterDTO();
            printerDTO.setBusinessType(readDO.getBusinessType());
            printerDTO.setPrinterName(readDO.getPrinterName());
            printerDTO.setPrinterGuid(readDO.getPrinterGuid());
            printerDTO.setPrinterType(readDO.getPrinterType());
            printerDTO.setPrinterPort(readDO.getPrinterPort());
            printerDTO.setArrayOfItemGuid(readDO.getArrayOfItemGuid());
            printerDTOList.add(printerDTO);
        });
        return printerDTOList;
    }

    @Override
    @Transactional
    public String addPrinter(PrinterDTO printerDTO) {
        // 检查打印机名称、IP地址、“单例本机”等规则
        checkPrinterNameAndIp(printerDTO);
        checkLocalPrinterSingleForAdd(printerDTO);

        // 添加打印机
        pretreatmentPrintCut(printerDTO);
        printerDTO.setPrinterGuid(distributedService.nextPrinterGuid());
        PrinterDO printerDO = printerMapstruct.fromPrinterDTO(printerDTO);
        boolean isMaster = storeDeviceService.isMasterDevice(printerDTO.getStoreGuid(), printerDTO.getDeviceId());
        save(printerDO.setIsMaster(isMaster));

        // 添加打印机的票据类型
        pretreatmentPrinterInvoice(printerDTO);
        printerInvoiceService.bindPrinterInvoice(printerDTO);

        // 添加打印机的关联菜品
        pretreatmentPrinterItem(printerDTO);
        printerItemService.bindPrinterItem(printerDTO);

        // 添加打印机的关联区域
        if (Objects.isNull(printerDTO.getAreaType()) || PrinterAreaTypeEnum.AREA.getType().equals(printerDTO.getAreaType())) {
            pretreatmentPrinterArea(printerDTO);
            printerAreaService.bindPrinterArea(printerDTO);
        }

        // 添加打印机的关联桌台
        if (PrinterAreaTypeEnum.TABLE.getType().equals(printerDTO.getAreaType())) {
            pretreatmentPrinterTable(printerDTO);
            printerTableService.bindPrinterAreaTable(printerDTO);
        }

        // 添加日志
        printerLogService.publishAddLog(this, printerDTO);

        return printerDTO.getPrinterGuid();
    }

    @Override
    public PrinterDTO queryPrinter(PrinterDTO printerDTO) {
        checkPrinterGuid(printerDTO);

        PrinterDO printerQuery = printerMapstruct.fromPrinterDTO(printerDTO);
        PrinterReadDO printerReadDO = baseMapper.findPrinterBindings(printerQuery);
        log.info("printerReadDO={}", JacksonUtils.writeValueAsString(printerReadDO));
        return printerMapstruct.toPrinterDTO(printerReadDO);
    }

    @Override
    public List<PrinterDTO> listPrinterOfTheDevice(PrinterDTO printerDTO) {
        PrinterDO printerDO = printerMapstruct.fromPrinterDTO(printerDTO);
        List<PrinterReadDO> printerOfTheDevice = baseMapper.findPrinterOfTheDevice(printerDO);

        if (CollectionUtils.isEmpty(printerOfTheDevice)) {
            return emptyList();
        }

        return printerOfTheDevice.stream().map(printerMapstruct::toPrinterDTO).collect(Collectors.toList());
    }

    @Override
    public List<PrinterDTO> listPrinterOfBizType(PrinterDTO printerDTO) {
        PrinterDO printerDO = printerMapstruct.fromPrinterDTO(printerDTO);
        List<PrinterReadDO> printerOfBizType = baseMapper.findPrinterOfBizType(printerDO);

        if (CollectionUtils.isEmpty(printerOfBizType)) {
            return emptyList();
        }
        List<PrinterDTO> printerDTOList = printerOfBizType.stream()
                .map(printerMapstruct::toPrinterDTO)
                .collect(Collectors.toList());
        // 飞蛾没有批量查询
        printerDTOList.forEach(printer -> {
            if (Objects.equals(ManufacturersTypeEnum.FEIE.getCode(), printer.getManufacturersType())) {
                FeieRespDTO respDTO = null;
                try {
                    respDTO = cloudPrinterService.queryPrinterInfo(printer.getDeviceNo());
                } catch (BusinessException be) {
                    log.warn("[queryPrinterInfo]error={}", be.getMessage());
                }
                if (Objects.nonNull(respDTO)) {
                    FeieRespDTO.DataMsg dataMsg = respDTO.getData();
                    if (Objects.nonNull(dataMsg)) {
                        printer.setDeviceState(dataMsg.getStatus());
                    }
                }
            }
        });
        return printerDTOList;
    }


    /**
     * 根据商品Id查询绑定的商品信息
     *
     * @param printerDTO 请求
     * @return 打印绑定商品信息
     */
    public List<String> listPrinterByPrinterIdAndItemId(PrinterDTO printerDTO) {
        try {
            List<String> arrayOfItemGuidReq = printerDTO.getArrayOfItemGuid();
            ItemSpuReqDTO reqDTO = new ItemSpuReqDTO();
            reqDTO.setStoreGuid(printerDTO.getStoreGuid());
            reqDTO.setItemGuids(arrayOfItemGuidReq);
            log.info("selectSpuItemMaps:{}", JacksonUtils.writeValueAsString(reqDTO));
            Map<String, List<String>> itemMaps = itemClientService.selectSpuItemMaps(reqDTO);
            log.info("selectSpuItemMaps result:{}", JacksonUtils.writeValueAsString(itemMaps));
            List<String> itemGuids = itemMaps.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(itemGuids)) {
                return Lists.newArrayList();
            }
            PrinterQuery printerQuery = buildQueryCondition(printerDTO);
            printerQuery.setArrayOfItemGuid(itemGuids);
            log.info("根据商品Id查询绑定的商品信息执行sql请求参数,printerQuery:{}", JacksonUtils.writeValueAsString(printerQuery));
            List<PrinterReadDO> printItemList = baseMapper.listPrinterItemByPrinterIdAndItemList(printerQuery);
            log.info("根据商品Id查询绑定的商品信息执行sql返回参数,printItemList:{}", JacksonUtils.writeValueAsString(printItemList));
            if (CollUtil.isEmpty(printItemList)) {
                return emptyList();
            }
            List<String> arrayOfItemGuids = printItemList.stream()
                    .flatMap(e -> e.getArrayOfItemGuid().stream())
                    .collect(Collectors.toList());
            List<String> arrayOfItemGuidResp = Lists.newArrayList();
            for (Map.Entry<String, List<String>> entry : itemMaps.entrySet()) {
                if (entry.getValue().stream().anyMatch(arrayOfItemGuids::contains)) {
                    arrayOfItemGuidResp.add(entry.getKey());
                }
            }
            return arrayOfItemGuidResp;
        } catch (Exception e) {
            log.info("根据商品Id查询绑定的商品信息异常:{}", e.getMessage());
            return emptyList();
        }
    }

    @Override
    @Transactional
    public void updatePrinter(PrinterDTO printerDTO) {
        // 检查打印机标识、名称、IP地址
        checkPrinterGuid(printerDTO);
        checkPrinterNameAndIp(printerDTO);

        // 检查“单例本机”规则
        String printerGuid = printerDTO.getPrinterGuid();
        PrinterDO printerQuery = new PrinterDO().setPrinterGuid(printerGuid);
        PrinterReadDO printerReadDO = baseMapper.findPrinterBindings(printerQuery);
        PrinterDTO printerBefore = printerMapstruct.toPrinterDTO(printerReadDO);
        checkLocalPrinterSingleForUpdate(printerDTO, printerBefore);

        // 添加打印机
        pretreatmentPrintCut(printerDTO);
        PrinterDO printerDO = printerMapstruct.fromPrinterDTO(printerDTO);
        printerDO.setIsMaster(storeDeviceService.isMasterDevice(printerDTO.getStoreGuid(), printerDTO.getDeviceId()));

        // 先删除关联的票据、菜品、区域（区域或桌台）
        printerInvoiceService.deletePrinterInvoice(printerGuid);
        printerItemService.deletePrinterItem(printerGuid);
        printerAreaService.deletePrinterArea(printerGuid);
        printerTableService.deletePrinterAreaTable(printerGuid);

        // 后修改打印机基础信息、插入关联的票据、菜品、区域
        updateById(printerDO.setId(printerReadDO.getId()));
        pretreatmentPrinterInvoice(printerDTO);
        printerInvoiceService.bindPrinterInvoice(printerDTO);

        pretreatmentPrinterItem(printerDTO);
        printerItemService.bindPrinterItem(printerDTO);

        // 区域
        if (Objects.isNull(printerDTO.getAreaType()) || PrinterAreaTypeEnum.AREA.getType().equals(printerDTO.getAreaType())) {
            pretreatmentPrinterArea(printerDTO);
            printerAreaService.bindPrinterArea(printerDTO);
        }

        // 添加打印机的关联桌台
        if (PrinterAreaTypeEnum.TABLE.getType().equals(printerDTO.getAreaType())) {
            pretreatmentPrinterTable(printerDTO);
            printerTableService.bindPrinterAreaTable(printerDTO);
        }
        // 添加修改日志
        printerLogService.publishUpdateLog(this, printerBefore, printerDTO);
    }

    @Override
    @Transactional
    public void deletePrinter(PrinterDTO printerDTO) {
        // 检查打印机标识
        checkPrinterGuid(printerDTO);

        // 查询打印机完整信息
        String printerGuid = printerDTO.getPrinterGuid();
        PrinterDO printerQuery = new PrinterDO().setPrinterGuid(printerGuid);
        PrinterReadDO printerReadDO = baseMapper.findPrinterBindings(printerQuery);
        if (Objects.nonNull(printerReadDO) && Objects.equals(PrinterTypeEnum.CLOUD_PRINTER.getType(), printerReadDO.getPrinterType())) {
            cloudPrinterService.deletePrinter(printerReadDO.getDeviceNo());
        }

        // 删除打印机，及其关联的单据、菜品、区域、打印记录
        removeById(printerReadDO.getId());
        printerInvoiceService.deletePrinterInvoice(printerGuid);
        printerItemService.deletePrinterItem(printerGuid);
        printerAreaService.deletePrinterArea(printerGuid);
        printRecordService.deletePrinterRecord(printerGuid);

        // 添加删除日志
        PrinterDTO printerBefore = printerMapstruct.toPrinterDTO(printerReadDO);
        printerLogService.publishDeleteLog(this, printerBefore);
    }

    @Override
    @Transactional
    public void deletePrinterOfTheStore(PrinterDTO printerDTO) {
        String storeGuid = printerDTO.getStoreGuid();

        deleteStorePrinter(storeGuid);
        printerInvoiceService.deleteStorePrinterInvoice(storeGuid);
        printerItemService.deleteStorePrinterItem(storeGuid);
        printerAreaService.deleteStorePrinterArea(storeGuid);
        printerTableService.deleteStorePrinterAreaTable(storeGuid);
        printRecordService.deleteStorePrintRecord(storeGuid);
    }

    @Override
    @Transactional
    public void deletePrinterOfTheDevice(PrinterDTO printerDTO) {
        // 找出设备ID对应的打印机
        LambdaQueryWrapper<PrinterDO> queryWrapper = new LambdaQueryWrapper<PrinterDO>()
                .select(PrinterDO::getPrinterGuid)
                .eq(PrinterDO::getDeviceId, printerDTO.getDeviceId())
                .in(PrinterDO::getBusinessType, bizTypeOfFrontOrLabel());
        List<PrinterDO> arrayOfPrinterDO = list(queryWrapper);

        if (CollectionUtils.isEmpty(arrayOfPrinterDO)) {
            return;
        }

        List<String> printerGuidOwnByTheDevice = arrayOfPrinterDO.parallelStream()
                .map(PrinterDO::getPrinterGuid).collect(Collectors.toList());

        batchDeletePrinter(printerGuidOwnByTheDevice);
        printerItemService.batchDeletePrinterItem(printerGuidOwnByTheDevice);
        printerAreaService.batchDeletePrinterArea(printerGuidOwnByTheDevice);
        printerTableService.batchDeletePrinterAreaTable(printerGuidOwnByTheDevice);
        printRecordService.batchDeletePrinterRecord(printerGuidOwnByTheDevice);
    }

    @Override
    @Transactional
    public void changeMasterPrinter(PrinterDTO printerDTO) {
        update(new PrinterDO().setIsMaster(false), new LambdaQueryWrapper<PrinterDO>()
                .eq(PrinterDO::getStoreGuid, printerDTO.getStoreGuid())
                .eq(PrinterDO::getIsMaster, true));
        update(new PrinterDO().setIsMaster(true), new LambdaQueryWrapper<PrinterDO>()
                .eq(PrinterDO::getDeviceId, printerDTO.getDeviceId()));
    }

    @Override
    public PrinterDTO findTestOrderPrinter(String storeGuid, String deviceId, Integer invoiceType, Integer pageSize) {
        PrinterDTO printerQuery = new PrinterDTO().setStoreGuid(storeGuid).setDeviceId(deviceId);
        printerQuery.setBusinessType(InvoiceTypeEnum.ofType(invoiceType).getBizType());
        List<PrinterDTO> printers = listPrinterOfBizType(printerQuery);
        if (CollectionUtils.isEmpty(printers)) {
            throw new BusinessException("未找到已连接的打印设备");
        }
        printers.sort(Comparator.comparingInt(PrinterDTO::getPrinterType));
        for (PrinterDTO printer : printers) {
            if (printer.getPrintPage().equalsIgnoreCase(String.valueOf(pageSize))) {
                return printer;
            }
        }
        return printers.get(0);
    }

    @Override
    public List<PrinterDTO> findTestOrderPrinters(String storeGuid, String deviceId, Integer invoiceType) {
        PrinterDTO printerQuery = new PrinterDTO().setStoreGuid(storeGuid).setDeviceId(deviceId);
        printerQuery.setBusinessType(InvoiceTypeEnum.ofType(invoiceType).getBizType());
        List<PrinterDTO> printers = listPrinterOfBizType(printerQuery);
        if (CollectionUtils.isEmpty(printers)) {
            throw new BusinessException("未找到已连接的打印设备");
        }
        return printers;
    }

    @Override
    public PrinterDO findMasterPrinter(String storeGuid) {
        return getOne(new LambdaQueryWrapper<PrinterDO>()
                .eq(PrinterDO::getStoreGuid, storeGuid)
                .eq(PrinterDO::getIsMaster, true), false);
    }

    @Override
    public String findMasterPrinterDeviceId(String storeGuid) {
        String masterDeviceId = Optional.ofNullable(findMasterPrinter(storeGuid))
                .map(PrinterDO::getDeviceId).orElse(null);
        if (StringUtils.isEmpty(masterDeviceId)) {
            return storeDeviceService.findMasterDevice(storeGuid);
        }
        return masterDeviceId;
    }

    @Override
    public String findMasterPrinterDeviceIdOrElseThrow(String storeGuid) {
        String masterPrinterDeviceId = findMasterPrinterDeviceId(storeGuid);
        if (!StringUtils.hasText(masterPrinterDeviceId)) {
            throw new BusinessException("该门店下无主机一体机");
        }
        return masterPrinterDeviceId;
    }

    @Override
    public boolean isMasterPrinterDevice(String storeGuid, String deviceId) {
        return deviceId.equalsIgnoreCase(findMasterPrinterDeviceId(storeGuid));
    }

    @Override
    public List<PrinterReadDO> findPrinterByQuery(PrinterQuery printerQuery) {
        return baseMapper.findPrinterByQuery(printerQuery);
    }

    @Override
    public List<PrinterReadDO> findPrinterOfKitchenTable(PrinterQuery printerQuery) {
        return baseMapper.findPrinterOfKitchenTable(printerQuery);
    }

    @Override
    public List<PrinterRawDTO> listRaw(String storeGuid) {
        List<PrinterDO> list = list(new LambdaQueryWrapper<PrinterDO>().eq(PrinterDO::getStoreGuid, storeGuid));
        return printerRawMaptstruct.toPrinterRawDTO(list);
    }

    @Override
    public boolean backupsPrinter(String deviceId) {

        PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setDeviceId(deviceId);
        printerDTO.setStoreGuid(UserContextUtils.getStoreGuid());

        List<PrinterDTO> list = listPrinterOfTheDevice(printerDTO);
        list.stream().map(obj -> {
            PrinterDTO printerDTO1 = queryPrinter(obj);
            obj.setArrayOfInvoiceDTO(printerDTO1.getArrayOfInvoiceDTO());
            obj.setArrayOfItemDTO(printerDTO1.getArrayOfItemDTO());
            obj.setArrayOfAreaDTO(printerDTO1.getArrayOfAreaDTO());
            return obj;
        }).collect(Collectors.toList());

        return printerBackupsService.backupsPrinter(UserContextUtils.getStoreGuid(), deviceId, list);
    }

    @Override
    public String backupsPrinterTime(String deviceId) {
        List<PrinterBackupsDO> printerBackupsDOS = printerBackupsService.list(new LambdaQueryWrapper<PrinterBackupsDO>()
                .eq(PrinterBackupsDO::getStoreGuid, UserContextUtils.getStoreGuid())
                .eq(PrinterBackupsDO::getDeviceId, deviceId)
                .orderByDesc(PrinterBackupsDO::getGmtCreate)
        );
        if (printerBackupsDOS != null && !printerBackupsDOS.isEmpty()) {
            return DateTimeUtils.localDateTime2String(printerBackupsDOS.get(0).getGmtCreate());
        } else {
            return null;
        }
    }

    @Override
    public void deletePrinterList(PrinterDTO printerDTO) {
        remove(new LambdaQueryWrapper<PrinterDO>()
                .eq(PrinterDO::getStoreGuid, printerDTO.getStoreGuid())
                .eq(PrinterDO::getDeviceId, printerDTO.getDeviceId()));
    }

    @Override
    public List<PrinterDTO> queryByCondition(PrinterQueryDTO queryDTO) {
        PrinterQuery query = new PrinterQuery();
        query.setStoreGuid(queryDTO.getStoreGuid());
        query.setInvoiceType(queryDTO.getInvoiceType());
        query.setBusinessType(queryDTO.getBusinessType());
        query.setDeviceId(queryDTO.getDeviceId());
        List<PrinterReadDO> readDOList = this.findPrinterByQuery(query);
        return getPrinterDTOList(readDOList);
    }

    @Override
    public void addCloud(CloudPrinterDTO cloudPrinterDTO) {
        // 检查打印机名称、“单例本机”等规则
        checkCloudPrinterName(cloudPrinterDTO);
        checkLocalPrinterSingleForAdd(cloudPrinterDTO);

        // 校验打印机
        cloudPrinterService.addPrinter(cloudPrinterDTO.getDeviceNo(), cloudPrinterDTO.getDeviceKey(), cloudPrinterDTO.getPrinterName());

        // 添加打印机
        cloudPrinterDTO.setPrinterGuid(distributedService.nextPrinterGuid());
        PrinterDO printerDO = printerMapstruct.fromPrinterDTO(cloudPrinterDTO);
        // 云打印机配置不绑定设备
        printerDO.setDeviceId("0");
        printerDO.setIsMaster(false);
        // 设置默认值1，后续云打印不会用到该字段
        printerDO.setPrintCount(1);
        printerDO.setPrintCut(0);
        this.save(printerDO);

        // 添加打印机的票据类型
        handlePrinterInvoice(cloudPrinterDTO);
        printerInvoiceService.bindPrinterInvoice(cloudPrinterDTO);

        // 添加打印机的关联菜品
        handlePrinterItem(cloudPrinterDTO);
        printerItemService.bindPrinterItem(cloudPrinterDTO);

        // 添加日志
        printerLogService.publishAddLog(this, cloudPrinterDTO);
    }

    @Override
    public void testPrint(CloudPrinterDTO cloudPrinterDTO) {
        checkPrinterGuid(cloudPrinterDTO);

        PrinterDO printerQuery = printerMapstruct.fromPrinterDTO(cloudPrinterDTO);
        PrinterReadDO printerReadDO = baseMapper.findPrinterBindings(printerQuery);
        FeieRespDTO respDTO = cloudPrinterService.queryPrinterInfo(printerReadDO.getDeviceNo());
        if (Objects.isNull(respDTO) || Objects.isNull(respDTO.getData())
                || BooleanEnum.TRUE.getCode() != respDTO.getData().getStatus()) {
            throw new BusinessException("打印失败");
        }
        cloudPrinterService.testPrinter(printerReadDO.getDeviceNo(), 1);
    }

    @Override
    public void updateCloudPrinter(CloudPrinterDTO cloudPrinterDTO) {
        // 检查打印机标识、名称、IP地址
        checkPrinterGuid(cloudPrinterDTO);
        checkCloudPrinterName(cloudPrinterDTO);

        // 检查“单例本机”规则
        String printerGuid = cloudPrinterDTO.getPrinterGuid();
        PrinterDO printerQuery = new PrinterDO().setPrinterGuid(printerGuid);
        PrinterReadDO printerReadDO = baseMapper.findPrinterBindings(printerQuery);
        PrinterDTO printerBefore = printerMapstruct.toPrinterDTO(printerReadDO);

        // 添加打印机
        PrinterDO printerDO = printerMapstruct.fromPrinterDTO(cloudPrinterDTO);
        printerDO.setDeviceId("0");
        printerDO.setIsMaster(false);
        // 设置默认值1，后续云打印不会用到该字段
        printerDO.setPrintCount(1);
        printerDO.setId(printerReadDO.getId());
        updateById(printerDO);

        // 先删除关联的票据、菜品、区域
        printerInvoiceService.deletePrinterInvoice(printerGuid);
        printerItemService.deletePrinterItem(printerGuid);

        // 添加打印机的票据类型
        handlePrinterInvoice(cloudPrinterDTO);
        printerInvoiceService.bindPrinterInvoice(cloudPrinterDTO);

        // 添加打印机的关联菜品
        handlePrinterItem(cloudPrinterDTO);
        printerItemService.bindPrinterItem(cloudPrinterDTO);

        // 添加修改日志
        printerLogService.publishUpdateLog(this, printerBefore, cloudPrinterDTO);
    }

    @Override
    public void checkPrinter(CloudPrinterDTO cloudPrinterDTO) {
        // 校验打印机
        cloudPrinterService.addPrinter(cloudPrinterDTO.getDeviceNo(), cloudPrinterDTO.getDeviceKey(), cloudPrinterDTO.getPrinterName());
        cloudPrinterService.deletePrinter(cloudPrinterDTO.getDeviceNo());
    }

    @Override
    public List<PrinterReadDO> findCloudPrinterByQuery(PrinterQuery printerQuery) {
        return baseMapper.findCloudPrinterByQuery(printerQuery);
    }

    @Override
    public List<PrinterDTO> listPrinter(SingleDataDTO request) {
        List<PrinterReadDO> printerReadDOList = baseMapper.listPrinterBindings(request.getDatas());
        log.info("printerReadDOList={}", JacksonUtils.writeValueAsString(printerReadDOList));
        return printerMapstruct.readToPrinterDTO(printerReadDOList);
    }

    @Override
    @Transactional
    public void batchUpdatePrinter(PrinterDTO printerDTO) {
        printerDTO.getPrinterDTOList().forEach(this::updatePrinter);
    }

    private void handlePrinterItem(CloudPrinterDTO cloudPrinterDTO) {
        List<DeviceUseDTO> useDTOList = cloudPrinterDTO.getDeviceUse();
        List<PrinterItemDTO> arrayOfItemDTO = useDTOList.stream()
                .filter(useDTO -> !CollectionUtils.isEmpty(useDTO.getArrayOfItemDTO()))
                .flatMap(useDTO -> useDTO.getArrayOfItemDTO().stream())
                .collect(Collectors.toList());
        cloudPrinterDTO.setArrayOfItemDTO(arrayOfItemDTO);
    }

    private void handlePrinterInvoice(CloudPrinterDTO cloudPrinterDTO) {
        List<DeviceUseDTO> useDTOList = cloudPrinterDTO.getDeviceUse();
        List<Integer> invoiceTypeList = new ArrayList<>();
        Map<Integer, Integer> printCountMap = new HashMap<>();
        for (DeviceUseDTO useDTO : useDTOList) {
            if (Objects.equals(UseInvoiceTypeEnum.POINT_MENU.getCode(), useDTO.getInvoiceType())) {
                invoiceTypeList.add(InvoiceTypeEnum.ORDER_ITEM.getType());
                printCountMap.put(InvoiceTypeEnum.ORDER_ITEM.getType(), useDTO.getPrintCount());
                invoiceTypeList.add(InvoiceTypeEnum.REFUND_ITEM.getType());
                printCountMap.put(InvoiceTypeEnum.REFUND_ITEM.getType(), useDTO.getPrintCount());
            }
            if (Objects.equals(UseInvoiceTypeEnum.CHECK_OUT.getCode(), useDTO.getInvoiceType())) {
                invoiceTypeList.add(InvoiceTypeEnum.CHECKOUT.getType());
                printCountMap.put(InvoiceTypeEnum.CHECKOUT.getType(), useDTO.getPrintCount());
                invoiceTypeList.add(InvoiceTypeEnum.CHECKOUT_TABLES.getType());
                printCountMap.put(InvoiceTypeEnum.CHECKOUT_TABLES.getType(), useDTO.getPrintCount());
            }
        }
        List<PrinterInvoiceDTO> arrayOfInvoiceDTO = new ArrayList<>();
        invoiceTypeList.forEach(invoiceType -> {
            PrinterInvoiceDTO invoiceDTO = new PrinterInvoiceDTO();
            invoiceDTO.setInvoiceType(invoiceType);
            invoiceDTO.setInvoiceName(InvoiceTypeEnum.getInvoiceName(invoiceType));
            Integer printCount = printCountMap.get(invoiceType);
            if (Objects.isNull(printCount)) {
                printCount = 1;
            }
            invoiceDTO.setPrintCount(printCount);
            arrayOfInvoiceDTO.add(invoiceDTO);
        });
        cloudPrinterDTO.setArrayOfInvoiceDTO(arrayOfInvoiceDTO);
    }

    private void checkCloudPrinterName(CloudPrinterDTO cloudPrinterDTO) {
        LambdaQueryWrapper<PrinterDO> wrapper = new LambdaQueryWrapper<PrinterDO>()
                .eq(PrinterDO::getStoreGuid, cloudPrinterDTO.getStoreGuid())
                .eq(PrinterDO::getPrinterName, cloudPrinterDTO.getPrinterName());


        if (!StringUtils.isEmpty(cloudPrinterDTO.getPrinterGuid())) {
            wrapper.ne(PrinterDO::getPrinterGuid, cloudPrinterDTO.getPrinterGuid());
        }

        if (count(wrapper) > 0) {
            throw new BusinessException("该门店已存在此名称的打印机");
        }
    }

    @Override
    @Transactional
    public boolean restorePrinter(String deviceId) {
        PrinterBackupsDO printerBackupsDOS = printerBackupsService.queryPrinters(UserContextUtils.getStoreGuid(), deviceId);
        PrinterDTO printerDTO = new PrinterDTO();
        printerDTO.setDeviceId(deviceId);
        printerDTO.setStoreGuid(UserContextUtils.getStoreGuid());
        List<PrinterDTO> list = listPrinterOfTheDevice(printerDTO);

        // 删除该设备绑定的所有打印机
        list.forEach(obj -> {
            PrinterDTO deletePrinterDTO = new PrinterDTO();
            deletePrinterDTO.setStoreGuid(obj.getStoreGuid());
            deletePrinterDTO.setStoreName(obj.getStoreName());
            deletePrinterDTO.setPrinterGuid(obj.getPrinterGuid());
            deletePrinterDTO.setBusinessType(obj.getBusinessType());
            deletePrinter(deletePrinterDTO);
        });

        List<PrinterDTO> printerDTOS = JacksonUtils.toObjectList(PrinterDTO.class, printerBackupsDOS.getPrintListJson());
        printerDTOS.forEach(obj -> {
            // 还原打印机，使用备份数据更新打印机原表数据
            addPrinter(obj);
        });
        return true;
    }

    /**
     * 检验该PrinterGuid对应的打印机是否存在
     *
     * @param printerDTO
     */
    private void checkPrinterGuid(PrinterDTO printerDTO) {
        if (0 == count(wrapperByPrinterGuid(printerDTO.getPrinterGuid()))) {
            log.warn("打印机(printerGuid:" + printerDTO.getPrinterGuid() + ")不存在");
            throw new BusinessException("打印机不存在，请重新选择打印机！");
        }
    }

    /**
     * 同种业务类型下：打印机名字、IP不得重复
     *
     * @param printerDTO
     */
    private void checkPrinterNameAndIp(PrinterDTO printerDTO) {
        LambdaQueryWrapper<PrinterDO> wrapper = new LambdaQueryWrapper<PrinterDO>()
                .eq(PrinterDO::getStoreGuid, printerDTO.getStoreGuid())
                .eq(PrinterDO::getBusinessType, printerDTO.getBusinessType())
                .and(j -> j
                        .eq(PrinterDO::getPrinterIp, printerDTO.getPrinterIp())
                        .or()
                        .eq(PrinterDO::getPrinterName, printerDTO.getPrinterName())
                );

        BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.ofType(printerDTO.getBusinessType());
        if (businessTypeEnum == BusinessTypeEnum.FRONT_PRINTER
                || businessTypeEnum == BusinessTypeEnum.LABEL_PRINTER) {
            wrapper.eq(PrinterDO::getDeviceId, printerDTO.getDeviceId());
        }

        if (!StringUtils.isEmpty(printerDTO.getPrinterGuid())) {
            wrapper.ne(PrinterDO::getPrinterGuid, printerDTO.getPrinterGuid());
        }

        if (count(wrapper) > 0) {
            throw new BusinessException("该门店已存在此名称或ip相同的打印机");
        }
    }

    /**
     * 创建打印机时检查“一个设备一种业务类型至多只能创建一个本地打印机”的规则
     *
     * @param printerDTO
     */
    private void checkLocalPrinterSingleForAdd(PrinterDTO printerDTO) {
        // 本机打印机一台T1每一个业务类型只能添加一个, 添加其他的打印机前必须添加master T1设备的本机打印机
        if (PrinterTypeEnum.LOCAL_PRINTER.getType().equals(printerDTO.getPrinterType())) {
            if (existLocalPrinterByDevice(printerDTO)) {
                throw new BusinessException("该设备已存在本地打印机");
            }
        }
    }

    /**
     * 更新打印机时检查“一个设备一种业务类型至多只能创建一个本地打印机”的规则
     *
     * @param printerDTO
     * @param printerBefore
     */
    private void checkLocalPrinterSingleForUpdate(PrinterDTO printerDTO, PrinterDTO printerBefore) {
        // 本机打印机一台T1每一个业务类型只能添加一个, 添加其他的打印机前必须添加master T1设备的本机打印机
        if (printerDTO.getPrinterType().equals(PrinterTypeEnum.LOCAL_PRINTER.getType())) {
            // 已存在该T1设备、该业务类型的本机打印机,
            boolean existLocalPrinterByDevice = existLocalPrinterByDevice(printerDTO);
            // 是否为更新自己
            boolean updateSelf = printerBefore.getDeviceId().equals(printerDTO.getDeviceId());
            if (existLocalPrinterByDevice && !updateSelf) {
                // 修改打印机为本机打印机, 且本设备已经有指定业务类型下的本机打印机, 且不是更新自己
                throw new BusinessException("该设备已存在本地打印机");
            }
        }
    }

    /**
     * 预处理切纸，标签打印机默认为：按数量切纸
     *
     * @param printerDTO
     */
    private void pretreatmentPrintCut(PrinterDTO printerDTO) {
        // 如果添加的打印机为标签打印机，设置切纸方式：数量切纸
        if (BusinessTypeEnum.LABEL_PRINTER.getType().equals(printerDTO.getBusinessType())) {
            printerDTO.setPrintCut(PrintCutEnum.PER_ITEM_COUNT.getCode());
        }
    }

    /**
     * todo 让严防修复此处，不要拼接上传，使用dto上传
     * 预处理单据数据，将单据信息转换为PrinterInvoiceDTO
     *
     * @param printerDTO
     */
    private void pretreatmentPrinterInvoice(PrinterDTO printerDTO) {
        //清除掉错误类型
        if (CollUtil.isNotEmpty(printerDTO.getArrayOfInvoiceType())) {
            printerDTO.getArrayOfInvoiceType().removeIf(i -> !Objects.equals(InvoiceTypeEnum.ofType(i).getBizType(), printerDTO.getBusinessType()));
        }
        // 如果添加的打印机为后厨打印机，添加单据类型: 点菜单、退菜单、后厨转台单
        if (BusinessTypeEnum.KITCHEN_PRINTER.getType().equals(printerDTO.getBusinessType())) {
            if (Objects.isNull(printerDTO.getArrayOfInvoiceType())) {
                printerDTO.setArrayOfInvoiceType(Lists.newArrayList());
            }
            List<Integer> arrayOfInvoiceType = printerDTO.getArrayOfInvoiceType();
            if (arrayOfInvoiceType.contains(InvoiceTypeEnum.ITEM_REPEAT_ORDER.getType())
                    && arrayOfInvoiceType.contains(InvoiceTypeEnum.PRE_CHECKOUT_TABLES.getType())) {
                // 如果点菜单 和 退菜单都不包含的情况，则说明是老包，默认添加全部
                arrayOfInvoiceType.add(InvoiceTypeEnum.ORDER_ITEM.getType());
                arrayOfInvoiceType.add(InvoiceTypeEnum.REFUND_ITEM.getType());
                arrayOfInvoiceType.add(InvoiceTypeEnum.TURN_TABLE_ITEM.getType());
            }
            // 是否添加了后厨单
            if (arrayOfInvoiceType.contains(InvoiceTypeEnum.TURN_TABLE_ITEM.getType())) {
                printerDTO.getArrayOfInvoiceType().add(InvoiceTypeEnum.TURN_TABLE_ITEM.getType());
            }
        }

        // 如果添加的打印机为标签打印机，添加单据类型；标签单
        if (BusinessTypeEnum.LABEL_PRINTER.getType().equals(printerDTO.getBusinessType())) {
            printerDTO.setArrayOfInvoiceType(Collections.singletonList(
                    InvoiceTypeEnum.LABEL.getType()
            ));
        }

        // 填充arrayOfInvoiceDTO
        List<Integer> arrayOfInvoiceType = printerDTO.getArrayOfInvoiceType();
        if (CollectionUtils.isEmpty(arrayOfInvoiceType)) {
            return;
        }

        // 设置打印机支持的单独集合，单据实体（单据类型、单据名称）
        if (arrayOfInvoiceType.contains(InvoiceTypeEnum.TAKEOUT.getType())) {
            arrayOfInvoiceType.add(InvoiceTypeEnum.RETAIL_TAKEOUT.getType());
        }

        //设置打印机勾选了报表单的情况下，默认增加预点餐商品统计项
        if (arrayOfInvoiceType.contains(InvoiceTypeEnum.ITEM_GIFT_STATS.getType())) {
            arrayOfInvoiceType.add(InvoiceTypeEnum.RESERVE_ITEM_STATS.getType());
            arrayOfInvoiceType.add(InvoiceTypeEnum.MEM_CONSU_STATS.getType());
            arrayOfInvoiceType.add(InvoiceTypeEnum.MEM_RECHAR_STATS.getType());
        }

        if (arrayOfInvoiceType.contains(InvoiceTypeEnum.HANDOVER.getType())) {
            arrayOfInvoiceType.add(InvoiceTypeEnum.HANDOVER_NEW.getType());
            arrayOfInvoiceType.add(InvoiceTypeEnum.HANDOVER_PRE_NEW.getType());
        }

        printerDTO.setArrayOfInvoiceDTO(arrayOfInvoiceType.stream()
                .map(invoiceType -> new PrinterInvoiceDTO()
                        .setInvoiceType(invoiceType)
                        .setInvoiceName(InvoiceTypeEnum.getInvoiceName(invoiceType)))
                .collect(Collectors.toList())
        );
    }

    /**
     * todo 让严防修复此处，不要拼接上传，使用dto上传
     * 预处理菜品数据，将用“，”拼接的菜品信息转换为PrinterItemDTO
     *
     * @param printerDTO
     */
    private void pretreatmentPrinterItem(PrinterDTO printerDTO) {
        List<String> arrayOfItemGuid = printerDTO.getArrayOfItemGuid();
        if (CollectionUtils.isEmpty(arrayOfItemGuid)) {
            return;
        }
        printerDTO.setArrayOfItemDTO(arrayOfItemGuid.stream()
                .filter(s -> s.contains(","))
                .map(s -> {
                    int indexOfSeparator = s.indexOf(",");
                    String itemName = s.substring(0, indexOfSeparator);
                    String itemGuid = s.substring(indexOfSeparator + 1);
                    return new PrinterItemDTO(itemGuid, itemName);
                })
                .collect(Collectors.toList())
        );
    }

    /**
     * todo 让严防修复此处，不要拼接上传，使用dto上传
     * 预处理区域数据，将用“，”拼接的区域信息转换为PrinterAreaDTO
     *
     * @param printerDTO
     */
    private void pretreatmentPrinterArea(PrinterDTO printerDTO) {
        List<String> arrayOfAreaGuid = printerDTO.getArrayOfAreaGuid();
        if (CollectionUtils.isEmpty(arrayOfAreaGuid)) {
            return;
        }
        printerDTO.setArrayOfAreaDTO(arrayOfAreaGuid.stream()
                .filter(s -> s.contains(","))
                .map(s -> {
                    int indexOfSeparator = s.indexOf(",");
                    String areaName = s.substring(0, indexOfSeparator);
                    String areaGuid = s.substring(indexOfSeparator + 1);
                    return new PrinterAreaDTO(areaGuid, areaName);
                })
                .collect(Collectors.toList())
        );
    }

    /**
     * 预处理区域数据，将用“，”拼接的区域信息转换为PrinterTableDTO
     */
    private void pretreatmentPrinterTable(PrinterDTO printerDTO) {
        List<String> arrayOfTableGuid = printerDTO.getArrayOfTableGuid();
        if (CollectionUtils.isEmpty(arrayOfTableGuid)) {
            return;
        }
        printerDTO.setArrayOfTableDTO(arrayOfTableGuid.stream()
                .filter(s -> s.contains(","))
                .map(s -> {
                    int indexOfSeparator = s.indexOf(",");
                    String tableName = s.substring(0, indexOfSeparator);
                    String tableGuid = s.substring(indexOfSeparator + 1);
                    return new PrinterTableDTO(tableGuid, tableName);
                })
                .collect(Collectors.toList())
        );
    }

    /**
     * 是否已存在本地打印机
     *
     * @param printerDTO
     * @return
     */
    private boolean existLocalPrinterByDevice(PrinterDTO printerDTO) {
        return count(new LambdaQueryWrapper<PrinterDO>()
                .eq(PrinterDO::getStoreGuid, printerDTO.getStoreGuid())
                .eq(PrinterDO::getDeviceId, printerDTO.getDeviceId())
                .eq(PrinterDO::getBusinessType, printerDTO.getBusinessType())
                .eq(PrinterDO::getPrinterType, PrinterTypeEnum.LOCAL_PRINTER.getType())) > 0;
    }

    /**
     * 删除门店打印机
     *
     * @param storeGuid
     */
    private void deleteStorePrinter(String storeGuid) {
        remove(new LambdaQueryWrapper<PrinterDO>()
                .eq(PrinterDO::getStoreGuid, storeGuid)
                .ne(PrinterDO::getPrinterType, PrinterTypeEnum.CLOUD_PRINTER.getType())
        );
    }

    /**
     * 批量删除打印机
     *
     * @param printerGuid
     */
    private void batchDeletePrinter(List<String> printerGuid) {
        remove(new LambdaQueryWrapper<PrinterDO>()
                .in(PrinterDO::getPrinterGuid, printerGuid));
    }

    /**
     * 前台、后厨、标签打印机的业务类型列表
     *
     * @return
     */
    private List<Integer> bizTypeOfFrontOrLabel() {
        return Arrays.asList(BusinessTypeEnum.FRONT_PRINTER.getType(), BusinessTypeEnum.KITCHEN_PRINTER.getType(),
                BusinessTypeEnum.LABEL_PRINTER.getType());
    }

    private Wrapper<PrinterDO> wrapperByPrinterGuid(String printerGuid) {
        return new LambdaQueryWrapper<PrinterDO>().eq(PrinterDO::getPrinterGuid, printerGuid);
    }

    private static PrinterQuery buildQueryCondition(PrinterDTO printerDTO) {
        PrinterQuery printerQuery = new PrinterQuery();
        printerQuery.setStoreGuid(printerDTO.getStoreGuid());
        printerQuery.setDeviceId(printerDTO.getDeviceId());
        printerQuery.setBusinessType(printerDTO.getBusinessType());
        return printerQuery;
    }


}

