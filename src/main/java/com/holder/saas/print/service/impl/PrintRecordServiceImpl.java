package com.holder.saas.print.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holder.saas.print.config.RocketMqConfig;
import com.holder.saas.print.entity.biz.TraceContext;
import com.holder.saas.print.entity.domain.PrintRecordDO;
import com.holder.saas.print.entity.enums.PrintPageEnum;
import com.holder.saas.print.entity.query.PrintRecordQuery;
import com.holder.saas.print.entity.read.PrintRecordReadDO;
import com.holder.saas.print.entity.read.PrinterReadDO;
import com.holder.saas.print.mapper.PrintRecordMapper;
import com.holder.saas.print.mapstruct.PrintRecordMapstruct;
import com.holder.saas.print.service.*;
import com.holder.saas.print.template.cloud.CloudCheckOutTemplate;
import com.holder.saas.print.template.cloud.CloudOrderItemTemplate;
import com.holder.saas.print.template.cloud.CloudRefundItemTemplate;
import com.holder.saas.print.utils.PrintLogUtils;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.constant.takeaway.TakeawayConstants;
import com.holderzone.saas.store.dto.print.PrintOrderDTO;
import com.holderzone.saas.store.dto.print.PrintRecordDTO;
import com.holderzone.saas.store.dto.print.PrintRecordReqDTO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.content.PrintOrderItemDTO;
import com.holderzone.saas.store.dto.print.content.PrintTakeoutDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.enums.print.PrinterTypeEnum;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintRecordServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印记录管理实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
@Transactional
@SuppressWarnings("Duplicates")
public class PrintRecordServiceImpl extends ServiceImpl<PrintRecordMapper, PrintRecordDO> implements PrintRecordService {

    private final PrinterService printerService;

    private final PrinterRoutingService printerRoutingService;

    private final PrintRecordCloneService printRecordCloneService;

    private final PrintRecordRespService printRecordRespService;

    private final PrintPushService printPushService;

    private final ContentCacheService contentCacheService;

    private final PrintRecordMapstruct printRecordMapstruct;

    private final DefaultRocketMqProducer defaultRocketMqProducer;

    private final RedisTemplate redisTemplate;

    private final CloudPrinterService cloudPrinterService;

    @Autowired
    @Qualifier("printCloudInvoiceThreadPool")
    private ExecutorService printCloudInvoiceThreadPool;

    @Autowired
    public PrintRecordServiceImpl(PrinterService printerService, PrinterRoutingService printerRoutingService,
                                  PrintRecordCloneService printRecordCloneService, PrintRecordRespService printRecordRespService,
                                  PrintPushService printPushService, ContentCacheService contentCacheService,
                                  PrintRecordMapstruct printRecordMapstruct, DefaultRocketMqProducer defaultRocketMqProducer,
                                  RedisTemplate redisTemplate, CloudPrinterService cloudPrinterService) {
        this.printerService = printerService;
        this.printerRoutingService = printerRoutingService;
        this.printRecordCloneService = printRecordCloneService;
        this.printRecordRespService = printRecordRespService;
        this.printPushService = printPushService;
        this.contentCacheService = contentCacheService;
        this.printRecordMapstruct = printRecordMapstruct;
        this.defaultRocketMqProducer = defaultRocketMqProducer;
        this.redisTemplate = redisTemplate;
        this.cloudPrinterService = cloudPrinterService;
    }

    /**
     * 该方法逻辑按照doc/README.md来实施
     */
    @Override
    public String insertRecord(PrintDTO printDTO) {

        //查询可以使用的打印机列表：打印来源不为POS、AIO，查找主机作为打印机。将主机的id设置进PrintDTO的deviceId中。
        List<PrinterReadDO> printers = printerRoutingService.findPrinterAvailable(printDTO);

        if (PrinterTypeEnum.CLOUD_PRINTER.getType().equals(printDTO.getAppointPrinterType())) {
            printers.clear();
        }
        // 查询云打印机
        List<PrinterReadDO> cloudPrinters = printerRoutingService.findCloudPrinterAvailable(printDTO);
        log.info("[云打印机信息]cloudPrinters={}", JacksonUtils.writeValueAsString(cloudPrinters));
        if (!CollectionUtils.isEmpty(cloudPrinters)) {
            List<String> printerGuidList = cloudPrinters.stream()
                    .map(PrinterReadDO::getPrinterGuid)
                    .distinct()
                    .collect(Collectors.toList());
            handleDateAndDelete(printDTO, cloudPrinters);
            printers.removeIf(c -> printerGuidList.contains(c.getPrinterGuid()));
        }

        log.info("打印机信息 findPrinterAvailable :{}", JacksonUtils.writeValueAsString(printers));

        // Fast fail
        if (CollectionUtils.isEmpty(printers)) {
            log.warn("未匹配到打印机");
            return "未匹配到打印机";
        }

        ArrayList<PrinterReadDO> printerList = new ArrayList<>();

        // 如果是挂起单，则需要过滤是否需要打印挂起单字段
        if (PrintOrderItemDTO.ItemInvoiceTypeEnum.HANG_UP.getType().equals(printDTO.getItemInvoiceType())) {

            List<PrinterReadDO> printers1 = printers.stream()
                    .filter(p -> Objects.nonNull(p.getIsPrintHangUp()) && p.getIsPrintHangUp())
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(printers1)) {
                log.warn("未匹配到挂起单打印机");
                return "未匹配到挂起单打印机";
            } else {
                printerList.addAll(printers1);
            }
        } else {
            printerList.addAll(printers);
        }

        log.info("匹配到打印机，处理中");
        PrinterReadDO printerReadDO = printerList.get(0);
        boolean notAutoPrint  = printerReadDO != null && printerReadDO.getAutoPrint() != null && printerReadDO.getAutoPrint() == BooleanEnum.FALSE.getCode();
        //若不是自动并且手动标识为false则不推送
        if(!printDTO.getManualPrint() && notAutoPrint){
            log.info("自动打印失效，不进行推送：{}",JacksonUtils.writeValueAsString(printerList));
            return null;
        }

        // Clone basePrint by invoiceType, then save batch printRecord
        List<String> arrayOfPrintRecordGuidInserted = handleDateAndDelete(printDTO, printerList);

        // Fast fail
        if (CollectionUtils.isEmpty(arrayOfPrintRecordGuidInserted)) {
            return "打印失败";
        }

        //推送消息到base-service,base-service负责触发一体机声音以及让一体机打印出小票
        printPushService.pushPrintTaskMsg(printDTO, arrayOfPrintRecordGuidInserted);

        //自发自收消息mq，下面这句mq的核心目的就是执行数据库的更新操作。若成功则不做其他操作,若失败则推送失败消息
        arrayOfPrintRecordGuidInserted.forEach(recordGuid -> {
            Message message = new Message(
                    RocketMqConfig.PRINT_RECORD_TIMEOUT_TOPIC,
                    RocketMqConfig.PRINT_RECORD_TIMEOUT_TAG,
                    JacksonUtils.toJsonByte(recordGuid)
            );
            message.setDelayTimeLevel(4);
            message.getProperties().put(RocketMqConfig.MESSAGE_CONTEXT, UserContextUtils.getJsonStr());
            defaultRocketMqProducer.sendMessage(message);
        });

        return "SUCCESS";
    }

    /**
     * 处理打印数据
     * 保存打印记录
     * 延迟删除打印记录
     */
    private List<String> handleDateAndDelete(PrintDTO printDTO, List<PrinterReadDO> printers) {
        // Clone basePrint by invoiceType, then save batch printRecord
        List<String> arrayOfPrintRecordGuidInserted = clonePrintRecordThenSave(printDTO, printers);

        //将本次插入到打印记录表中的数据发送到打印记录删除队列
        Message removeMessage = new Message(RocketMqConfig.PRINT_RECORD_REMOVE_TOPIC,
                RocketMqConfig.PRINT_RECORD_REMOVE_TAG,
                JacksonUtils.toJsonByte(arrayOfPrintRecordGuidInserted));
        removeMessage.setDelayTimeLevel(17);  //延迟等级17，自定义配置为1小时
        removeMessage.getProperties().put(RocketMqConfig.MESSAGE_CONTEXT, UserContextUtils.getJsonStr());
        defaultRocketMqProducer.sendMessage(removeMessage);
        return arrayOfPrintRecordGuidInserted;
    }

    @Override
    public void deleteRecord(PrintRecordReqDTO printRecordReqDTO) {
        remove(wrapperByRecordGuid(printRecordReqDTO.getRecordGuid()));
        // 删除外卖自动接单打印队列
        String redisKey = TakeawayConstants.TakeawayAutoAcceptConstants.TIMEOUT_PRINT_QUEUE + "_" + printRecordReqDTO.getStoreGuid();
        redisTemplate.opsForHash().delete(redisKey, printRecordReqDTO.getRecordGuid());
    }

    @Override
    public void batchDeleteRecord(List<String> arrayOfRecordGuid) {
        if (CollectionUtils.isEmpty(arrayOfRecordGuid)) {
            return;
        }
        remove(new LambdaQueryWrapper<PrintRecordDO>().in(PrintRecordDO::getRecordGuid, arrayOfRecordGuid));

        String redisKey = TakeawayConstants.TakeawayAutoAcceptConstants.TIMEOUT_PRINT_QUEUE + "_" + UserContextUtils.getStoreGuid();
        // List<Object> recordGuids = new ArrayList<>(arrayOfRecordGuid);
        Object[] recordGuidArr = arrayOfRecordGuid.toArray();
        redisTemplate.opsForHash().delete(redisKey, recordGuidArr);
    }

    @Override
    public void deletePrinterRecord(String printerGuid) {
        remove(new LambdaQueryWrapper<PrintRecordDO>().eq(PrintRecordDO::getPrinterGuid, printerGuid));
    }

    @Override
    public void batchDeletePrinterRecord(List<String> arrayOfPrinterGuid) {
        if (CollectionUtils.isEmpty(arrayOfPrinterGuid)) {
            return;
        }
        remove(new LambdaQueryWrapper<PrintRecordDO>().in(PrintRecordDO::getPrinterGuid, arrayOfPrinterGuid));
    }

    @Override
    public void deleteStorePrintRecord(String storeGuid) {
        remove(new LambdaQueryWrapper<PrintRecordDO>().eq(PrintRecordDO::getStoreGuid, storeGuid));
    }

    @Override
    public void deleteHistoryRecord() {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String beginTime = DateTimeUtils.now().plusDays(-3).format(dateTimeFormatter);
        PrintRecordDTO printRecordDTO = new PrintRecordDTO();
        printRecordDTO.setBeginTime(beginTime);
        baseMapper.deleteHistory(printRecordDTO);
    }

    @Override
    public void updatePrintResult(PrintRecordReqDTO printRecordReqDTO) {
        String recordGuid = printRecordReqDTO.getRecordGuid();
        Wrapper<PrintRecordDO> wrapper = wrapperByRecordGuid(recordGuid);
        if (0 == count(wrapper)) {
            return;
        }
        if (PrintRecordReqDTO.STATUS_FAILED.equals(printRecordReqDTO.getPrintStatus())) {
            PrintRecordDO printRecord2Save = new PrintRecordDO()
                    .setPrintStatus(printRecordReqDTO.getPrintStatus())
                    .setPrintStatusMsg(Optional.ofNullable(printRecordReqDTO.getPrintStatusMsg())
                            .map(msg -> msg.substring(0, Math.min(45, msg.length()))).orElse(null));
            update(printRecord2Save, wrapper);
            PrintRecordDO printRecordDO = printRecordMapstruct.fromPrintRecordReqDTO(printRecordReqDTO);
            printRecordDO.setPrintStatus(PrintRecordReqDTO.STATUS_FAILED);
            long countFiledByDevice = baseMapper.countByDeviceAndStatus(printRecordDO);
            PrintRecordDO printRecordBefore = getOne(new LambdaQueryWrapper<PrintRecordDO>()
                    .select(PrintRecordDO::getRecordUid,
                            PrintRecordDO::getInvoiceType, PrintRecordDO::getPrintContent)
                    .eq(PrintRecordDO::getRecordGuid, recordGuid));
            String searchKey = PrintLogUtils.searchKey(printRecordBefore.getRecordUid(), printRecordBefore.getInvoiceType());
            log.info("Client端更新打印结果：打印失败，context={}，searchKey={}，recordGuid={}",
                    TraceContext.ids(), searchKey, recordGuid);
            log.warn("Client端更新打印结果：打印失败，context={}，searchKey={}，recordGuid={}，printStatusMsg={}",
                    TraceContext.names(), searchKey, recordGuid, printRecordReqDTO.getPrintStatusMsg());
            if (appendTakeawayFailQueue(printRecordDO)) {
                return;
            }
            printPushService.pushPrintFailedMsg(printRecordReqDTO, printRecordBefore, (int) countFiledByDevice);
        } else {
            PrintRecordDO printRecordBefore = getOne(new LambdaQueryWrapper<PrintRecordDO>()
                    .select(PrintRecordDO::getRecordUid, PrintRecordDO::getPrintStatus,
                            PrintRecordDO::getInvoiceType, PrintRecordDO::getPrintContent)
                    .eq(PrintRecordDO::getRecordGuid, recordGuid));
            String searchKey = PrintLogUtils.searchKey(printRecordBefore.getRecordUid(), printRecordBefore.getInvoiceType());
            PrintRecordDO printRecord2Save = new PrintRecordDO()
                    .setPrintStatus(printRecordReqDTO.getPrintStatus())
                    .setPrintStatusMsg(Optional.ofNullable(printRecordReqDTO.getPrintStatusMsg())
                            .map(msg -> msg.substring(0, Math.min(45, msg.length()))).orElse(null));
            update(printRecord2Save, wrapper);
            if (PrintRecordReqDTO.STATUS_FAILED.equals(printRecordBefore.getPrintStatus())) {
                log.info("Client端更新打印结果：重打成功，context={}，searchKey={}，recordGuid={}",
                        TraceContext.ids(), searchKey, recordGuid);
                PrintRecordDO printRecordDO = printRecordMapstruct.fromPrintRecordReqDTO(printRecordReqDTO);
                printRecordDO.setPrintStatus(PrintRecordReqDTO.STATUS_FAILED);
                long countFiledByDevice = baseMapper.countByDeviceAndStatus(printRecordDO);
                printPushService.pushPrintSucceedMsg(printRecordReqDTO, printRecordBefore, (int) countFiledByDevice);
            } else {
                log.info("Client端更新打印结果：打印成功，context={}，searchKey={}，recordGuid={}",
                        TraceContext.ids(), searchKey, recordGuid);
            }
            String redisKey = TakeawayConstants.TakeawayAutoAcceptConstants.TIMEOUT_PRINT_QUEUE + "_" + printRecordReqDTO.getStoreGuid();
            redisTemplate.opsForHash().delete(redisKey, printRecordReqDTO.getRecordGuid());
        }
    }

    @Override
    public void updatePendingResult(String recordGuid) {
        PrintRecordDO printRecordDO = new PrintRecordDO();
        printRecordDO.setPrintStatus(PrintRecordReqDTO.STATUS_FAILED);
        printRecordDO.setPrintStatusMsg("客户端打印超时，系统默认失败");
        if (update(printRecordDO, new LambdaQueryWrapper<PrintRecordDO>()
                .eq(PrintRecordDO::getPrintStatus, PrintRecordReqDTO.STATUS_INITIAL)
                .eq(PrintRecordDO::getRecordGuid, recordGuid))) {
            PrintRecordDO printRecordInSql = getOne(new LambdaQueryWrapper<PrintRecordDO>()
                    .select(PrintRecordDO::getRecordUid, PrintRecordDO::getDeviceId,
                            PrintRecordDO::getInvoiceType, PrintRecordDO::getPrintContent, PrintRecordDO::getRecordGuid)
                    .eq(PrintRecordDO::getRecordGuid, recordGuid));

            String searchKey = PrintLogUtils.searchKey(printRecordInSql.getRecordUid(), printRecordInSql.getInvoiceType());
            log.info("Server端更新打印结果：打印失败，searchKey={}", searchKey);
            String enterpriseName = Optional.ofNullable(UserContextUtils.getEnterpriseName())
                    .orElse(UserContextUtils.getEnterpriseGuid());
            String storeName = Optional.ofNullable(UserContextUtils.getStoreName())
                    .orElse(UserContextUtils.getStoreGuid());
            log.warn("Server端更新打印结果：打印失败，searchKey={}，enterpriseName={}，storeName={}，printStatusMsg={}",
                    searchKey, enterpriseName, storeName, printRecordDO.getPrintStatusMsg());

            PrintRecordDO query = new PrintRecordDO();
            query.setDeviceId(printRecordInSql.getDeviceId());
            query.setPrintStatus(PrintRecordReqDTO.STATUS_FAILED);
            long countFiledByDevice = baseMapper.countByDeviceAndStatus(query);

            PrintRecordReqDTO printRecordReqDTO = new PrintRecordReqDTO();
            printRecordReqDTO.setDeviceId(printRecordInSql.getDeviceId());
            printRecordReqDTO.setRecordGuid(recordGuid);

            if (appendTakeawayFailQueue(printRecordInSql)) {
                return;
            }
            printPushService.pushPrintFailedMsg(printRecordReqDTO, printRecordInSql, (int) countFiledByDevice);
        }
    }

    @Override
    public List<PrintRecordDTO> listRecord(PrintRecordReqDTO printRecordReqDTO) {
        PrintRecordDO printRecordDO = printRecordMapstruct.fromPrintRecordReqDTO(printRecordReqDTO);
        if (printerService.isMasterPrinterDevice(printRecordReqDTO.getStoreGuid(), printRecordReqDTO.getDeviceId())) {
            printRecordDO.setDeviceId(null);
        }
        /**
         * bugfixed: 17759
         * 解决2020.05.30何师烧烤打印宕机问题。
         * 解决方案：
         * 1.将gmt_create作为开始时间，控制查询的时间在三天内。
         * 2.在job中加入定时任务，定期删除数据。
         */
        PrintRecordDTO printRecordDTO = printRecordMapstruct.mapRecordDTO(printRecordDO);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String beginTime = DateTimeUtils.now().plusDays(-3).format(dateTimeFormatter);
        printRecordDTO.setBeginTime(beginTime);
        List<PrintRecordReadDO> arrayOfPrintRecord = baseMapper.listByDeviceAndStatusFor3Days(printRecordDTO);
        if (CollectionUtils.isEmpty(arrayOfPrintRecord)) {
            return Collections.emptyList();
        }
        return arrayOfPrintRecord.stream().map(printRecordRespService::do2Dto).collect(Collectors.toList());
    }

    @Override
    public List<PrintOrderDTO> getPrintOrder(PrintRecordReqDTO printRecordReqDTO) {
        if (contentCacheService.hasMsgId(printRecordReqDTO.getMsgId(), printRecordReqDTO.getRecordGuid(),
                printRecordReqDTO.getArrayOfRecordGuid())) {
            log.error("重复获取打印内容, printRecordReqDTO:{}", JacksonUtils.writeValueAsString(printRecordReqDTO));
            return Lists.newArrayList();
        }
        List<PrintOrderDTO> printOrderContent = getPrintOrderContent(printRecordReqDTO);
        contentCacheService.saveMsgId(printRecordReqDTO.getMsgId(), printRecordReqDTO.getRecordGuid(),
                printRecordReqDTO.getArrayOfRecordGuid());
        return printOrderContent;
    }

    @Override
    public PrintOrderDTO getTestPrintOrder(FormatDTO formatDTO) {
        String storeGuid = formatDTO.getStoreGuid();
        Integer invoiceType = formatDTO.getInvoiceType();
        Integer pageSize = Objects.requireNonNull(formatDTO.getPageSize(), "纸张宽度不得为空");
        String deviceId = Objects.requireNonNull(formatDTO.getDeviceId(), "设备编号不得为空");
        PrinterDTO testOrderPrinter = printerService.findTestOrderPrinter(storeGuid, deviceId, invoiceType, pageSize);
        return printRecordRespService.getOrderByPerMock(invoiceType, pageSize, formatDTO, testOrderPrinter);
    }

    @Override
    public List<PrintOrderDTO> getTestPrintOrders(FormatDTO formatDTO) {
        String storeGuid = formatDTO.getStoreGuid();
        Integer invoiceType = formatDTO.getInvoiceType();
        String deviceId = Objects.requireNonNull(formatDTO.getDeviceId(), "设备编号不得为空");
        List<PrinterDTO> testOrderPrinter = printerService.findTestOrderPrinters(storeGuid, deviceId, invoiceType);
        return testOrderPrinter.stream()
                .map(printer -> printRecordRespService.getOrderByPerMock(
                        invoiceType, PrintPageEnum.ofPageSize(printer.getPrintPage()).getPageWidth(), formatDTO, printer
                ))
                .collect(Collectors.toList());
    }

    /**
     * 重新打印外卖自动接单未打印小票
     */
    @Override
    public void reprintTakeaway(String storeGuid, String masterDeviceId) {
        try {
            String redisKey = TakeawayConstants.TakeawayAutoAcceptConstants.TIMEOUT_PRINT_QUEUE + "_" + storeGuid;
            Map<String, String> takeawayTimeoutMap = redisTemplate.opsForHash().entries(redisKey);
            List<String> recordGuids = takeawayTimeoutMap.keySet().stream().sorted().collect(Collectors.toList());
            log.info("重打外卖自动接单小票开始,takeawayTimeoutMap:{}", JacksonUtils.writeValueAsString(takeawayTimeoutMap));
            recordGuids.forEach(recordGuid -> {
                String printContent = takeawayTimeoutMap.get(recordGuid);
                if (StringUtils.isEmpty(printContent)) {
                    return;
                }
                // 重新设置打印设备
                PrintDTO printDTO = JSONObject.parseObject(printContent, PrintDTO.class);
                Integer invoiceType = printDTO.getInvoiceType();
                if (!InvoiceTypeEnum.TAKEOUT.getType().equals(invoiceType) && !InvoiceTypeEnum.ORDER_ITEM.getType().equals(invoiceType)) {
                    return;
                }
                List<String> recordGuidList = Lists.newArrayList(recordGuid);
                if (InvoiceTypeEnum.TAKEOUT.getType().equals(invoiceType)) {
                    PrintTakeoutDTO printTakeoutDTO = JSONObject.parseObject(printContent, PrintTakeoutDTO.class);
                    printTakeoutDTO.setDeviceId(masterDeviceId);
                    sendPrintTimeoutMessage(recordGuidList);
                    printPushService.pushPrintTaskMsg(printTakeoutDTO, recordGuidList);
                } else {
                    PrintOrderItemDTO printOrderItemDTO = JSONObject.parseObject(printContent, PrintOrderItemDTO.class);
                    printOrderItemDTO.setDeviceId(masterDeviceId);
                    sendPrintTimeoutMessage(recordGuidList);
                    printPushService.pushPrintTaskMsg(printOrderItemDTO, recordGuidList);
                }
                redisTemplate.opsForHash().delete(redisKey, recordGuid);
            });
        } catch (Exception e) {
            e.printStackTrace();
            log.error("重打外卖自动接单小票失败,e:{}", e.getMessage());
        }
    }

    @Override
    public List<PrintOrderDTO> reprintTakeawayPrintOrderList(String storeGuid) {
        String redisKey = TakeawayConstants.TakeawayAutoAcceptConstants.TIMEOUT_PRINT_QUEUE + "_" + storeGuid;
        Map<String, String> takeawayTimeoutMap = redisTemplate.opsForHash().entries(redisKey);
        if (MapUtils.isEmpty(takeawayTimeoutMap)) {
            return Lists.newArrayList();
        }
        log.info("获取外卖自动接单漏单小票打印,takeawayTimeoutMap:{}", JacksonUtils.writeValueAsString(takeawayTimeoutMap));
        List<PrintOrderDTO> result = Lists.newArrayList();
        takeawayTimeoutMap.forEach((k, v) -> {
            try {
                Long milliSecond = BatchIdGenerator.parseGuid(Long.parseLong(k)).get(0);
                if (System.currentTimeMillis() - 1000 * 60 * 20 > milliSecond) {
                    return;
                }
                PrintRecordReadDO printRecordReadDO = JSON.parseObject(v, PrintRecordReadDO.class);
                if (printRecordReadDO == null) {
                    return;
                }
                result.add(printRecordRespService.getOrderByPerDO(printRecordReadDO));
            } catch (NumberFormatException e) {
                e.printStackTrace();
                log.error("外卖单漏打印记录解析失败storeGuid:{},recordGuid:{},content:{}", storeGuid, k, v);
            }
        });
        redisTemplate.delete(redisKey);
        return result.stream().sorted(Comparator.comparing(PrintOrderDTO::getPrintKey)).collect(Collectors.toList());
    }

    private Wrapper<PrintRecordDO> wrapperByRecordGuid(String recordGuid) {
        return new LambdaQueryWrapper<PrintRecordDO>().eq(PrintRecordDO::getRecordGuid, recordGuid);
    }

    /**
     * 打印1.2.0
     * clone执行完毕之后，将printRecordsToInsert进行批量的保存，将printRecordsToCache进行批量的缓存。
     * 然后将printRecordsToInsert的RecordGuid查询出来，返回List<String>
     *
     * @param printDTO
     * @param printers
     * @return
     */
    private List<String> clonePrintRecordThenSave(PrintDTO printDTO, List<PrinterReadDO> printers) {
        List<PrintRecordDO> printRecordsToInsert = new ArrayList<>();
        List<PrintRecordReadDO> printRecordsToCache = new ArrayList<>();
        printRecordCloneService.cloneRecord(printDTO, printers, printRecordsToInsert, printRecordsToCache);
        if (CollectionUtils.isEmpty(printRecordsToInsert)) {
            return Collections.emptyList();
        }
        // 长日志分段打印，一般分一次够用了
        String printRecordsToInsertJson = JacksonUtils.writeValueAsString(printRecordsToInsert);
        if (printRecordsToInsertJson.length() > 8000) {
            String substring = printRecordsToInsertJson.substring(8000);
            log.info("最终打印保存记录[1] clonePrintRecordThenSave:{}", printRecordsToInsertJson);
            log.info("最终打印保存记录[2] clonePrintRecordThenSave:{}", substring);
        } else {
            log.info("最终打印保存记录 clonePrintRecordThenSave:{}", printRecordsToInsertJson);
        }
        saveBatch(printRecordsToInsert);
        printCloudInvoice(printDTO, printers, printRecordsToInsert);
        contentCacheService.save(printRecordsToCache);
        return printRecordsToInsert.stream().map(PrintRecordDO::getRecordGuid).collect(Collectors.toList());
    }

    private void printCloudInvoice(PrintDTO printDTO,
                                   List<PrinterReadDO> printers,
                                   List<PrintRecordDO> printRecordsToInsert) {
        // 目前仅支持快餐
        if (Objects.nonNull(printDTO.getTradeMode()) && Objects.equals(TradeModeEnum.FAST.getCode(), printDTO.getTradeMode())) {
            List<PrinterReadDO> cloudPrinterList = printers.stream()
                    .filter(p -> Objects.equals(PrinterTypeEnum.CLOUD_PRINTER.getType(), p.getPrinterType()))
                    .collect(Collectors.toList());
            log.info("[printCloudInvoice]cloudPrinterList={}", JacksonUtils.writeValueAsString(cloudPrinterList));
            if (CollectionUtils.isEmpty(cloudPrinterList)) {
                return;
            }
            // 异步云打印
            UserContext userContext = UserContextUtils.get();
            CompletableFuture.runAsync(() -> {
                // 设置国际化字段,目前只有中文
                Locale locale = Locale.SIMPLIFIED_CHINESE;
                LocaleContextHolder.setLocale(locale);
                UserContextUtils.put(userContext);
                toPrintCloudInvoice(printRecordsToInsert, cloudPrinterList);
            }, printCloudInvoiceThreadPool).exceptionally(e -> {
                log.error("[异步云打印]运行异常信息: " + e.getMessage(), e);
                throw new BusinessException(e.getMessage());
            });
        }
    }

    private void toPrintCloudInvoice(List<PrintRecordDO> printRecordsToInsert, List<PrinterReadDO> cloudPrinterList) {
        List<String> cloudPrinterGuidList = cloudPrinterList.stream()
                .map(PrinterReadDO::getPrinterGuid)
                .distinct()
                .collect(Collectors.toList());
        List<PrintRecordDO> toCloudPrinterList = printRecordsToInsert.stream()
                .filter(p -> cloudPrinterGuidList.contains(p.getPrinterGuid()))
                .collect(Collectors.toList());
        Map<String, PrinterReadDO> cloudPrinterMap = cloudPrinterList.stream()
                .collect(Collectors.toMap(PrinterReadDO::getPrinterGuid, Function.identity(), (v1, v2) -> v1));
        if (!CollectionUtils.isEmpty(cloudPrinterMap)) {
            for (PrintRecordDO printRecord : toCloudPrinterList) {
                String content = getContent(printRecord);
                PrinterReadDO printerReadDO = cloudPrinterMap.get(printRecord.getPrinterGuid());
                log.warn("云打印内容={},云打印机={}", content, JacksonUtils.writeValueAsString(printerReadDO));
                String deviceNo = printerReadDO.getDeviceNo();
                Integer printCount = printerReadDO.getPrintCount();
                cloudPrinterService.print(content, deviceNo, printCount);
            }
        }
    }

    private String getContent(PrintRecordDO printRecord) {
        String content = "";
        switch (InvoiceTypeEnum.ofType(printRecord.getInvoiceType())) {
            case REFUND_ITEM: {
                content = CloudRefundItemTemplate.getContent(printRecord);
                break;
            }
            case ORDER_ITEM: {
                content = CloudOrderItemTemplate.getContent(printRecord);
                break;
            }
            case CHECKOUT: {
                content = CloudCheckOutTemplate.getContent(printRecord);
                break;
            }
            default: {
                break;
            }
        }
        return content;
    }


    /**
     * 外卖小票自动接单时打印失败,则记录重打
     */
    private boolean appendTakeawayFailQueue(PrintRecordDO printRecordInSql) {
        try {
            String printContent = printRecordInSql.getPrintContent();
            if (StringUtils.isEmpty(printContent)) {
                return false;
            }
            PrintDTO printDTO = JSONObject.parseObject(printContent, PrintDTO.class);
            if (!InvoiceTypeEnum.TAKEOUT.getType().equals(printDTO.getInvoiceType())
                    && !InvoiceTypeEnum.ORDER_ITEM.getType().equals(printDTO.getInvoiceType())) {
                return false;
            }
            log.info("打印失败小票,printDTO:{}", JacksonUtils.writeValueAsString(printDTO));
            if (InvoiceTypeEnum.TAKEOUT.getType().equals(printDTO.getInvoiceType())) {
                PrintTakeoutDTO printTakeoutDTO = JSONObject.parseObject(printContent, PrintTakeoutDTO.class);
                if (Objects.isNull(printTakeoutDTO.getIsAutoAccept()) || !printTakeoutDTO.getIsAutoAccept()) {
                    return false;
                }
            }
            if (InvoiceTypeEnum.ORDER_ITEM.getType().equals(printDTO.getInvoiceType())) {
                PrintOrderItemDTO printOrderItemDTO = JSONObject.parseObject(printContent, PrintOrderItemDTO.class);
                if (Objects.isNull(printOrderItemDTO.getIsAutoAccept()) || !printOrderItemDTO.getIsAutoAccept()) {
                    return false;
                }
            }
            PrintRecordReadDO printRecordRead = transferPrintOrder(printRecordInSql.getRecordGuid());
            if (printRecordRead == null) {
                log.info("外卖自动接单记录打印失败小票追加失败,记录不存在,recordGuid:{}", printRecordInSql.getRecordGuid());
                return true;
            }
            // 外卖系统自动接单：外卖单，点菜单
            String redisKey = TakeawayConstants.TakeawayAutoAcceptConstants.TIMEOUT_PRINT_QUEUE + "_" + printDTO.getStoreGuid();
            log.info("外卖自动接单记录打印失败小票追加,redisKey:{},recordGuid:{},printContent:{}", redisKey, printRecordInSql.getRecordGuid(),
                    JacksonUtils.writeValueAsString(printRecordRead));
            redisTemplate.opsForHash().put(redisKey, printRecordInSql.getRecordGuid(), JSONObject.toJSONString(printRecordRead));
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("外卖自动接单记录打印失败小票失败,e:{}", e.getMessage());
        }
        return false;
    }

    private void sendPrintTimeoutMessage(List<String> recordGuidList) {
        /**
         * 打印1.2.2
         * 自发自收消息mq，下面这句mq的核心目的就是执行数据库的更新操作。
         * 若成功则不做其他操作
         * 若失败则推送失败消息
         */
        // Delivery timeout message
        recordGuidList.forEach(recordGuid -> {
            Message message = new Message(
                    RocketMqConfig.PRINT_RECORD_TIMEOUT_TOPIC,
                    RocketMqConfig.PRINT_RECORD_TIMEOUT_TAG,
                    JacksonUtils.toJsonByte(recordGuid)
            );
            message.setDelayTimeLevel(3);
            message.getProperties().put(RocketMqConfig.MESSAGE_CONTEXT, UserContextUtils.getJsonStr());
            defaultRocketMqProducer.sendMessage(message);
        });
    }


    private PrintRecordReadDO transferPrintOrder(String recordGuid) {
        // 查询printOrder
        PrintRecordQuery printRecordQuery = new PrintRecordQuery();
        printRecordQuery.setRecordGuid(recordGuid);
        return baseMapper.queryByRecordGuid(printRecordQuery);
    }

    /**
     * 获取打印内容
     */
    private List<PrintOrderDTO> getPrintOrderContent(PrintRecordReqDTO printRecordReqDTO) {
        PrintRecordQuery printRecordQuery = printRecordMapstruct.printRecordReqToQuery(printRecordReqDTO);
        if (StringUtils.hasText(printRecordQuery.getRecordGuid())) {
            PrintRecordReadDO printRecordReadDO = contentCacheService.popSingle(printRecordQuery.getRecordGuid());
            // redis 没有缓存
            if (null == printRecordReadDO) {
                printRecordReadDO = baseMapper.queryByRecordGuid(printRecordQuery);
            }
            if (null == printRecordReadDO) {
                return Collections.emptyList();
            }
            // 打印成功的返回空list
            if (printRecordReadDO.getPrintStatus() != null && printRecordReadDO.getPrintStatus() == 1) {
                return Collections.emptyList();
            }
            String searchKey = PrintLogUtils.searchKey(printRecordReadDO.getRecordUid(), printRecordReadDO.getInvoiceType());
            log.info("Client端拉取打印单，context={}，searchKey={}，recordGuid={}",
                    TraceContext.ids(), searchKey, printRecordQuery.getRecordGuid());
            // 用专门的服务返回结果
            return Collections.singletonList(printRecordRespService.getOrderByPerDO(printRecordReadDO));
        }
        if (!CollectionUtils.isEmpty(printRecordQuery.getArrayOfRecordGuid())) {
            List<PrintRecordReadDO> arrayOfPrintRecordReadDO = contentCacheService.popBatch(printRecordQuery.getArrayOfRecordGuid());
            if (CollectionUtils.isEmpty(arrayOfPrintRecordReadDO)) {
                arrayOfPrintRecordReadDO = baseMapper.queryInRecordGuid(printRecordQuery);
            }
            if (CollectionUtils.isEmpty(arrayOfPrintRecordReadDO)) {
                return Collections.emptyList();
            }
            return arrayOfPrintRecordReadDO.stream()
                    .peek(printRecordReadDO -> {
                        String searchKey = PrintLogUtils.searchKey(printRecordReadDO.getRecordUid(), printRecordReadDO.getInvoiceType());
                        log.info("Client端拉取打印单，context={}，searchKey={}，recordGuid={}",
                                TraceContext.ids(), searchKey, printRecordReadDO.getRecordGuid());
                    })
                    .map(printRecordRespService::getOrderByPerDO).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

}

