package com.holder.saas.print.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.print.entity.domain.PrinterBackupsDO;
import com.holder.saas.print.entity.domain.PrinterDO;
import com.holder.saas.print.entity.query.PrinterQuery;
import com.holder.saas.print.entity.read.PrinterReadDO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.raw.PrinterRawDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface PrinterBackupsService extends IService<PrinterBackupsDO> {

    PrinterBackupsDO queryPrinters(String storeGuid, String deviceId);

    boolean backupsPrinter(String storeGuid, String deviceId, List<PrinterDTO> printerBackupsDOS);
}
