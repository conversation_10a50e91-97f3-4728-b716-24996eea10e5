package com.holder.saas.print.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holder.saas.print.entity.domain.PrinterAreaDO;
import com.holder.saas.print.mapper.PrinterAreaMapper;
import com.holder.saas.print.mapstruct.PrinterMapstruct;
import com.holder.saas.print.mapstruct.PrinterRawMaptstruct;
import com.holder.saas.print.service.DistributedService;
import com.holder.saas.print.service.PrinterAreaService;
import com.holderzone.saas.store.dto.print.PrinterAreaDTO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.raw.PrinterAreaRawDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterAreaServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印机区域管理实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class PrinterAreaServiceImpl extends ServiceImpl<PrinterAreaMapper, PrinterAreaDO> implements PrinterAreaService {

    private final DistributedService distributedService;

    private final PrinterMapstruct printerMapstruct;

    private final PrinterRawMaptstruct printerRawMaptstruct;

    @Autowired
    public PrinterAreaServiceImpl(DistributedService distributedService, PrinterMapstruct printerMapstruct,
                                  PrinterRawMaptstruct printerRawMaptstruct) {
        this.distributedService = distributedService;
        this.printerMapstruct = printerMapstruct;
        this.printerRawMaptstruct = printerRawMaptstruct;
    }

    @Override
    public void bindPrinterArea(PrinterDTO printerDTO) {
        List<PrinterAreaDTO> areaDTOList = printerDTO.getArrayOfAreaDTO();
        if (CollectionUtils.isEmpty(areaDTOList)) return;
        String storeGuid = printerDTO.getStoreGuid();
        String printerGuid = printerDTO.getPrinterGuid();
        List<String> batchGuid = distributedService.nextBatchPrinterAreaGuid(areaDTOList.size());
        List<PrinterAreaDO> arrayOfPrinterAreaDO = areaDTOList.stream()
                .map(areaDTO -> new PrinterAreaDO()
                        .setStoreGuid(storeGuid)
                        .setPrinterGuid(printerGuid)
                        .setAreaGuid(areaDTO.getAreaGuid())
                        .setAreaName(areaDTO.getAreaName())
                        .setGuid(batchGuid.remove(batchGuid.size() - 1)))
                .collect(Collectors.toList());
        saveBatch(arrayOfPrinterAreaDO);
    }

    @Override
    public List<PrinterAreaDTO> listPrinterArea(PrinterDTO printerDTO) {
        List<PrinterAreaDO> arrayOfPrinterAreaDO = list(new LambdaQueryWrapper<PrinterAreaDO>()
                .eq(PrinterAreaDO::getPrinterGuid, printerDTO.getPrinterGuid()));
        if (CollectionUtils.isEmpty(arrayOfPrinterAreaDO)) return Collections.emptyList();
        return printerMapstruct.toPrinterAreaDTO(arrayOfPrinterAreaDO);
    }

    @Override
    public void deletePrinterArea(String printerGuid) {
        remove(new LambdaQueryWrapper<PrinterAreaDO>()
                .eq(PrinterAreaDO::getPrinterGuid, printerGuid));
    }

    @Override
    public void batchDeletePrinterArea(List<String> arrayOfPrinterGuid) {
        if (CollectionUtils.isEmpty(arrayOfPrinterGuid)) return;
        remove(new LambdaQueryWrapper<PrinterAreaDO>()
                .in(PrinterAreaDO::getPrinterGuid, arrayOfPrinterGuid));
    }

    @Override
    public void deleteStorePrinterArea(String storeGuid) {
        remove(new LambdaQueryWrapper<PrinterAreaDO>()
                .eq(PrinterAreaDO::getStoreGuid, storeGuid));
    }

    @Override
    public void batchDeleteStorePrinterArea(List<String> arrayOfStoreGuid) {
        if (CollectionUtils.isEmpty(arrayOfStoreGuid)) return;
        remove(new LambdaQueryWrapper<PrinterAreaDO>()
                .in(PrinterAreaDO::getStoreGuid, arrayOfStoreGuid));
    }

    @Override
    public List<PrinterAreaRawDTO> listRaw(String storeGuid) {
        List<PrinterAreaDO> list = list(new LambdaQueryWrapper<PrinterAreaDO>()
                .eq(PrinterAreaDO::getStoreGuid, storeGuid));
        return printerRawMaptstruct.toAreaRawDTO(list);
    }

}

