package com.holder.saas.print.service;

import com.holder.saas.print.entity.domain.PrintRecordDO;
import com.holder.saas.print.entity.read.PrintRecordReadDO;
import com.holder.saas.print.entity.read.PrinterReadDO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintRecordCloneService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface PrintRecordCloneService {

    void cloneRecord(PrintDTO printDTO, List<PrinterReadDO> printers,
                     List<PrintRecordDO> printRecordsToInsert, List<PrintRecordReadDO> printRecordsToCache);
}
