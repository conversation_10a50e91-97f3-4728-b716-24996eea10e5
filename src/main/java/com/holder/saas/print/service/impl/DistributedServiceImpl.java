package com.holder.saas.print.service.impl;

import com.holder.saas.print.service.DistributedService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DistributedServiceImpl
 * @date 2018/02/14 09:00
 * @description 分布式id服务实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class DistributedServiceImpl implements DistributedService {

    private static final String TAG_PRINT_PRINTER = "print/printer";

    private static final String TAG_PRINT_PRINTER_INVOICE = "print/printer_invoice";

    private static final String TAG_PRINT_PRINTER_AREA = "print/printer_area";

    private static final String TAG_PRINT_PRINTER_ITEM = "print/printer_item";

    private static final String TAG_PRINT_PRINT_RECORD = "print/print_record";

    private static final String TAG_PRINT_INVOICE_FORMAT = "print/invoice_format";

    private static final String PRINT_TYPE_TEMPLATE = "print/print_type_template";

    private static final String PRINT_TYPE_TEMPLATE_RTYPE = "print/print_type_template_rtype";

    private final RedisTemplate redisTemplate;

    @Autowired
    public DistributedServiceImpl(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public Long rawId(String tag) {
        try {
            return BatchIdGenerator.getGuid(redisTemplate, tag);
        } catch (IOException e) {
            throw new BusinessException("生成Guid失败：" + e.getMessage());
        }
    }

    @Override
    public String nextId(String tag) {
        return String.valueOf(rawId(tag));
    }

    @Override
    public List<String> nextBatchId(String tag, long count) {
        return BatchIdGenerator.batchGetGuids(redisTemplate, tag, count)
                .stream().map(String::valueOf).collect(Collectors.toList());
    }

    @Override
    public String nextPrinterGuid() {
        return nextId(TAG_PRINT_PRINTER);
    }

    @Override
    public List<String> nextBatchPrinterGuid(long count) {
        return nextBatchId(TAG_PRINT_PRINTER, count);
    }

    @Override
    public String nextPrinterInvoiceGuid() {
        return nextId(TAG_PRINT_PRINTER_INVOICE);
    }

    @Override
    public List<String> nextBatchPrinterInvoiceGuid(long count) {
        return nextBatchId(TAG_PRINT_PRINTER_INVOICE, count);
    }

    @Override
    public String nextPrinterAreaGuid() {
        return nextId(TAG_PRINT_PRINTER_AREA);
    }

    @Override
    public List<String> nextBatchPrinterAreaGuid(long count) {
        return nextBatchId(TAG_PRINT_PRINTER_AREA, count);
    }

    @Override
    public String nextPrinterItemGuid() {
        return nextId(TAG_PRINT_PRINTER_ITEM);
    }

    @Override
    public List<String> nextBatchPrinterItemGuid(long count) {
        return nextBatchId(TAG_PRINT_PRINTER_AREA, count);
    }

    @Override
    public String nextPrintRecordGuid() {
        return nextId(TAG_PRINT_PRINT_RECORD);
    }

    @Override
    public List<String> nextBatchPrintRecordGuid(long count) {
        return nextBatchId(TAG_PRINT_PRINT_RECORD, count);
    }

    @Override
    public String nextInvoiceFormatId() {
        return nextId(TAG_PRINT_INVOICE_FORMAT);
    }

    @Override
    public String nextPrintTypeTemplateGuid() {
        return nextId(PRINT_TYPE_TEMPLATE);
    }

    @Override
    public List<String> nextBatchTemplateRTypeGuid(long count) {
        return nextBatchId(PRINT_TYPE_TEMPLATE_RTYPE, count);
    }
}
