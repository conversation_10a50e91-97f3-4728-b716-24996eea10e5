package com.holder.saas.print.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holder.saas.print.entity.domain.type.PrintTypeTemplateDO;
import com.holder.saas.print.entity.domain.type.PrintTypeTemplateRItemDO;
import com.holder.saas.print.entity.domain.type.PrintTypeTemplateRStoreDO;
import com.holder.saas.print.entity.domain.type.PrintTypeTemplateRTypeDO;
import com.holder.saas.print.mapper.PrintTypeTemplateMapper;
import com.holder.saas.print.service.*;
import com.holder.saas.print.service.feign.StoreDeviceFeignService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.SingleDataPageDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.print.type.*;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 打印分类模版表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Slf4j
@Service
@AllArgsConstructor
public class PrintTypeTemplateServiceImpl extends ServiceImpl<PrintTypeTemplateMapper, PrintTypeTemplateDO>
        implements PrintTypeTemplateService {

    private final PrintTypeTemplateMapper printTypeTemplateMapper;

    private final DistributedService distributedService;

    private final PrintTypeTemplateRTypeService templateRTypeService;

    private final PrintTypeTemplateRItemService templateRItemService;

    private final PrintTypeTemplateRStoreService templateRStoreService;

    private final StoreDeviceFeignService storeDeviceFeignService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(PrintTypeTemplateDTO createDTO) {
        // 参数校验
        checkParam(createDTO, null);

        // 模版
        PrintTypeTemplateDO templateDO = new PrintTypeTemplateDO();
        String templateGuid = distributedService.nextPrintTypeTemplateGuid();
        templateDO.setGuid(templateGuid);
        templateDO.setIsEnable(Boolean.TRUE);
        templateDO.setName(createDTO.getName());
        templateDO.setInvoiceType(String.join(",", createDTO.getInvoiceType()));
        templateDO.setIsAllStore(createDTO.getIsAllStore());
        templateDO.setBrandGuid(createDTO.getBrandGuid());
        printTypeTemplateMapper.insert(templateDO);

        // 模版分类
        saveTemplateType(createDTO, templateGuid);

        // 模版门店
        saveTemplateStore(createDTO, templateGuid);
    }

    /**
     * 保存模版门店
     */
    private void saveTemplateStore(PrintTypeTemplateDTO createDTO, String templateGuid) {
        if (Boolean.FALSE.equals(createDTO.getIsAllStore())) {
            List<String> storeGuidList = createDTO.getStoreGuidList();
            List<PrintTypeTemplateRStoreDO> templateRStoreDOList = Lists.newArrayList();
            storeGuidList.forEach(storeGuid -> {
                PrintTypeTemplateRStoreDO templateRStoreDO = new PrintTypeTemplateRStoreDO();
                templateRStoreDO.setTemplateGuid(templateGuid);
                templateRStoreDO.setStoreGuid(storeGuid);
                templateRStoreDO.setBrandGuid(createDTO.getBrandGuid());
                templateRStoreDOList.add(templateRStoreDO);
            });
            templateRStoreService.saveBatch(templateRStoreDOList);
        }
    }

    /**
     * 保存模版分类
     */
    private void saveTemplateType(PrintTypeTemplateDTO createDTO, String templateGuid) {
        List<TemplateTypeDTO> typeDTOList = createDTO.getTypeDTOList();
        List<String> templateRTypeGuidList = distributedService.nextBatchTemplateRTypeGuid(typeDTOList.size());
        List<PrintTypeTemplateRTypeDO> templateRTypeDOList = Lists.newArrayList();
        List<PrintTypeTemplateRItemDO> templateRItemDOList = Lists.newArrayList();
        typeDTOList.forEach(templateTypeDTO -> {
            PrintTypeTemplateRTypeDO templateRTypeDO = new PrintTypeTemplateRTypeDO();
            String templateRTypeGuid = templateRTypeGuidList.remove(templateRTypeGuidList.size() - 1);
            templateRTypeDO.setGuid(templateRTypeGuid);
            templateRTypeDO.setTemplateGuid(templateGuid);
            templateRTypeDO.setName(templateTypeDTO.getName());
            templateRTypeDOList.add(templateRTypeDO);

            // 模版商品
            List<String> itemGuidList = templateTypeDTO.getItemGuidList().stream().map(TemplateItemQO::getItemGuid).collect(Collectors.toList());
            itemGuidList.forEach(itemGuid -> {
                PrintTypeTemplateRItemDO templateRItemDO = new PrintTypeTemplateRItemDO();
                templateRItemDO.setTypeGuid(templateRTypeGuid);
                templateRItemDO.setTemplateGuid(templateGuid);
                templateRItemDO.setItemGuid(itemGuid);
                templateRItemDOList.add(templateRItemDO);
            });
        });
        templateRTypeService.saveBatch(templateRTypeDOList);
        templateRItemService.saveBatch(templateRItemDOList);
    }

    /**
     * 参数校验
     */
    private void checkParam(PrintTypeTemplateDTO createDTO, String guid) {
        if (Boolean.FALSE.equals(createDTO.getIsAllStore())) {
            if (CollectionUtils.isEmpty(createDTO.getStoreGuidList())) {
                throw new ParameterException("请选择门店");
            }
            int storeCount = templateRStoreService.queryRepeatTemplateCount(createDTO.getBrandGuid(), createDTO.getStoreGuidList(),
                    createDTO.getInvoiceType(), guid);
            if (storeCount > 0) {
                throw new ParameterException("门店已配置模版");
            }
        }
        // 全部门店
        if (Boolean.TRUE.equals(createDTO.getIsAllStore())) {
            int templateCount = printTypeTemplateMapper.queryRepeatTemplateCount(createDTO.getBrandGuid(), createDTO.getInvoiceType(), guid);
            if (templateCount > 0) {
                throw new ParameterException("门店已配置模版");
            }
        }
        int checkTemplateName = printTypeTemplateMapper.checkTemplateName(createDTO.getBrandGuid(), createDTO.getName(), guid);
        if (checkTemplateName > 0) {
            throw new ParameterException("模版名称不可重复");
        }
        List<TemplateTypeDTO> typeDTOList = createDTO.getTypeDTOList();
        Set<String> typeNameSet = typeDTOList.stream()
                .map(TemplateTypeDTO::getName)
                .collect(Collectors.toSet());
        if (typeNameSet.size() != typeDTOList.size()) {
            throw new ParameterException("分类名称不可重复");
        }
    }

    @Override
    public Page<PrintTypeTemplateVO> queryPage(SingleDataPageDTO query) {
        if (StringUtils.isEmpty(query.getData())) {
            throw new BusinessException("品牌guid不为空");
        }
        List<PrintTypeTemplateDO> templateDOList = printTypeTemplateMapper.queryPage(query);
        if (CollectionUtils.isEmpty(templateDOList)) {
            log.warn("品牌下模版为空，brandGuid={}", query.getData());
            return new Page<>(query.getCurrentPage(), query.getPageSize());
        }
        Page<PrintTypeTemplateVO> pageResult = new Page<>(query.getCurrentPage(), query.getPageSize());
        int count = this.count(new LambdaQueryWrapper<PrintTypeTemplateDO>()
                .eq(PrintTypeTemplateDO::getIsDelete, Boolean.FALSE)
                .eq(PrintTypeTemplateDO::getBrandGuid, query.getData()));
        pageResult.setTotalCount(count);

        // 构建返回数据
        pageResult.setData(buildPrintTypeTemplateVOList(templateDOList));
        return pageResult;
    }

    private List<PrintTypeTemplateVO> buildPrintTypeTemplateVOList(List<PrintTypeTemplateDO> templateDOList) {
        // 查询模版门店
        List<String> templateGuidList = templateDOList.stream()
                .map(PrintTypeTemplateDO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        List<PrintTypeTemplateRStoreDO> storeDOList = templateRStoreService.list(
                new LambdaQueryWrapper<PrintTypeTemplateRStoreDO>()
                        .in(PrintTypeTemplateRStoreDO::getTemplateGuid, templateGuidList)
                        .eq(PrintTypeTemplateRStoreDO::getIsDelete, Boolean.FALSE)
        );
        Map<String, StoreDTO> storeDTOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(storeDOList)) {
            List<String> storeGuidList = storeDOList.stream()
                    .map(PrintTypeTemplateRStoreDO::getStoreGuid)
                    .distinct()
                    .collect(Collectors.toList());
            SingleDataDTO singleDataDTO = new SingleDataDTO();
            singleDataDTO.setDatas(storeGuidList);
            List<StoreDTO> storeDTOList = storeDeviceFeignService.queryStoreByIdList(singleDataDTO);
            if (!CollectionUtils.isEmpty(storeDTOList)) {
                storeDTOMap = storeDTOList.stream()
                        .collect(Collectors.toMap(StoreDTO::getGuid, Function.identity(), (oldValue, newValue) -> oldValue));
            }
        }
        Map<String, List<PrintTypeTemplateRStoreDO>> templateStoreMap = storeDOList.stream()
                .collect(Collectors.groupingBy(PrintTypeTemplateRStoreDO::getTemplateGuid));

        // 处理返回数据
        List<PrintTypeTemplateVO> templateVOList = Lists.newArrayList();
        for (PrintTypeTemplateDO templateDO : templateDOList) {
            PrintTypeTemplateVO templateVO = new PrintTypeTemplateVO();
            templateVO.setGuid(templateDO.getGuid());
            templateVO.setName(templateDO.getName());
            templateVO.setIsEnable(templateDO.getIsEnable());

            List<String> invoiceTypeList = Arrays.asList(templateDO.getInvoiceType().split(","));
            templateVO.setInvoiceType(InvoiceTypeEnum.converInvoiceTtile(invoiceTypeList, "、"));

            String storeNames = getStoreNames(templateStoreMap, storeDTOMap, templateDO);
            templateVO.setStoreNames(storeNames);

            templateVOList.add(templateVO);
        }
        return templateVOList;
    }

    private String getStoreNames(Map<String, List<PrintTypeTemplateRStoreDO>> templateStoreMap,
                                 Map<String, StoreDTO> storeDTOMap,
                                 PrintTypeTemplateDO templateDO) {
        String storeNames;
        if (templateDO.getIsAllStore()) {
            storeNames = "全部门店";
        } else {
            List<String> storeNameList = new ArrayList<>();
            List<PrintTypeTemplateRStoreDO> templateRStoreDOList = templateStoreMap.get(templateDO.getGuid());
            if (!CollectionUtils.isEmpty(templateRStoreDOList)) {
                for (PrintTypeTemplateRStoreDO rStore : templateRStoreDOList) {
                    StoreDTO storeDTO = storeDTOMap.get(rStore.getStoreGuid());
                    if (!ObjectUtils.isEmpty(storeDTO)) {
                        storeNameList.add(storeDTO.getName());
                    }
                }
            }
            storeNames = String.join("、", storeNameList);
        }
        return storeNames;
    }

    @Override
    public PrintTypeTemplateDetailDTO queryDetail(SingleDataDTO query) {
        String templateGuid = query.getData();
        if (StringUtils.isEmpty(templateGuid)) {
            throw new ParameterException("模版Guid为空");
        }
        PrintTypeTemplateDO templateDO = this.getOne(
                new LambdaQueryWrapper<PrintTypeTemplateDO>()
                        .eq(PrintTypeTemplateDO::getIsDelete, Boolean.FALSE)
                        .eq(PrintTypeTemplateDO::getGuid, templateGuid)
        );
        if (ObjectUtils.isEmpty(templateDO)) {
            log.warn("[queryDetail]模版不存在，templateGuid={}", templateGuid);
            throw new BusinessException(Constant.TEMPLATE_NOT_EXIST);
        }
        PrintTypeTemplateDetailDTO detailDTO = new PrintTypeTemplateDetailDTO();

        // 分类信息
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("queryTemplateTypeVOList");
        List<TemplateTypeVO> typeList = templateRTypeService.queryTemplateTypeVOList(templateGuid, null);
        stopWatch.stop();
        detailDTO.setTypeList(typeList);
        detailDTO.setGuid(templateDO.getGuid());
        detailDTO.setName(templateDO.getName());
        detailDTO.setInvoiceType(Arrays.asList(templateDO.getInvoiceType().split(",")));
        detailDTO.setIsAllStore(templateDO.getIsAllStore());

        // 门店信息
        stopWatch.start("queryTemplateStoreVOList");
        List<TemplateStoreVO> storeList = templateRStoreService.queryTemplateStoreVOList(templateDO.getIsAllStore(), templateGuid);
        stopWatch.stop();
        detailDTO.setStoreList(storeList);

        log.info("[性能排查]queryDetail={}", stopWatch.prettyPrint());
        return detailDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(PrintTypeTemplateDTO modifyDTO) {
        // 参数校验
        checkParam(modifyDTO, modifyDTO.getGuid());

        PrintTypeTemplateDO templateDO = this.getOne(
                new LambdaQueryWrapper<PrintTypeTemplateDO>()
                        .eq(PrintTypeTemplateDO::getGuid, modifyDTO.getGuid())
                        .eq(PrintTypeTemplateDO::getIsDelete, Boolean.FALSE)
        );
        if (ObjectUtils.isEmpty(templateDO)) {
            log.warn("[modify]模版不存在，templateGuid={}", modifyDTO.getGuid());
            throw new BusinessException(Constant.TEMPLATE_NOT_EXIST);
        }

        // 模版
        templateDO.setGmtModified(null);
        templateDO.setName(modifyDTO.getName());
        templateDO.setInvoiceType(String.join(",", modifyDTO.getInvoiceType()));
        templateDO.setIsAllStore(modifyDTO.getIsAllStore());
        printTypeTemplateMapper.updateById(templateDO);

        // 模版分类
        modifyTemplateType(modifyDTO);

        // 模版门店
        modifyTemplateStore(modifyDTO);
    }

    // 私有方法，用于修改模板的存储信息
    private void modifyTemplateStore(PrintTypeTemplateDTO modifyDTO) {
        // 删除与模板对应的旧的存储与模板对应关系数据
        templateRStoreService.removeByTemplateGuid(modifyDTO.getGuid());
        // 判断是否需要修改所有门店的存储信息
        if (Boolean.FALSE.equals(modifyDTO.getIsAllStore())) {
            // 获取需要修改的门店GUID列表
            List<String> storeGuidList = modifyDTO.getStoreGuidList();
            // 创建一个新的存储与模板对应关系列表
            List<PrintTypeTemplateRStoreDO> templateRStoreDOList = Lists.newArrayList();
            // 遍历门店GUID列表
            storeGuidList.forEach(storeGuid -> {
                // 创建一个新的存储与模板对应关系对象
                PrintTypeTemplateRStoreDO templateRStoreDO = new PrintTypeTemplateRStoreDO();
                // 设置模板GUID和门店GUID
                templateRStoreDO.setTemplateGuid(modifyDTO.getGuid());
                templateRStoreDO.setStoreGuid(storeGuid);
                templateRStoreDO.setBrandGuid(modifyDTO.getBrandGuid());
                // 将新的存储与模板对应关系添加到列表中
                templateRStoreDOList.add(templateRStoreDO);
            });
            // 保存新的存储与模板对应关系数据
            templateRStoreService.saveBatch(templateRStoreDOList);
        }
    }

    private void modifyTemplateType(PrintTypeTemplateDTO modifyDTO) {
        List<TemplateTypeDTO> typeDTOList = modifyDTO.getTypeDTOList();
        List<String> templateRTypeGuidList = distributedService.nextBatchTemplateRTypeGuid(typeDTOList.size());
        List<PrintTypeTemplateRTypeDO> templateRTypeDOList = Lists.newArrayList();
        List<PrintTypeTemplateRItemDO> templateRItemDOList = Lists.newArrayList();
        typeDTOList.forEach(templateTypeDTO -> {
            PrintTypeTemplateRTypeDO templateRTypeDO = new PrintTypeTemplateRTypeDO();
            String templateRTypeGuid = templateRTypeGuidList.remove(templateRTypeGuidList.size() - 1);
            templateRTypeDO.setGuid(templateRTypeGuid);
            templateRTypeDO.setTemplateGuid(modifyDTO.getGuid());
            templateRTypeDO.setName(templateTypeDTO.getName());
            templateRTypeDOList.add(templateRTypeDO);

            // 模版商品
            List<String> itemGuidList = templateTypeDTO.getItemGuidList().stream().map(TemplateItemQO::getItemGuid).collect(Collectors.toList());
            itemGuidList.forEach(itemGuid -> {
                PrintTypeTemplateRItemDO templateRItemDO = new PrintTypeTemplateRItemDO();
                templateRItemDO.setTypeGuid(templateRTypeGuid);
                templateRItemDO.setTemplateGuid(modifyDTO.getGuid());
                templateRItemDO.setItemGuid(itemGuid);
                templateRItemDOList.add(templateRItemDO);
            });
        });

        templateRTypeService.removeByTemplateGuid(modifyDTO.getGuid());
        templateRTypeService.saveBatch(templateRTypeDOList);

        templateRItemService.removeByTemplateGuid(modifyDTO.getGuid());
        templateRItemService.saveBatch(templateRItemDOList);
    }

    @Override
    public boolean enable(PrintTypeTemplateEnableDTO enableDTO) {
        PrintTypeTemplateDO templateDO = this.getOne(new LambdaQueryWrapper<PrintTypeTemplateDO>()
                .eq(PrintTypeTemplateDO::getGuid, enableDTO.getTemplateGuid())
                .eq(PrintTypeTemplateDO::getIsDelete, Boolean.FALSE)
        );
        if (ObjectUtils.isEmpty(templateDO)) {
            log.warn("[enable]模版不存在，templateGuid={}", enableDTO.getTemplateGuid());
            return false;
        }
        templateDO.setGmtModified(null);
        templateDO.setIsEnable(enableDTO.getIsEnable());
        return this.updateById(templateDO);
    }

    @Override
    public boolean delete(SingleDataDTO deleteDTO) {
        String templateGuid = deleteDTO.getData();
        if (StringUtils.isEmpty(templateGuid)) {
            throw new ParameterException("模版Guid为空");
        }
        PrintTypeTemplateDO templateDO = this.getOne(new LambdaQueryWrapper<PrintTypeTemplateDO>()
                .eq(PrintTypeTemplateDO::getGuid, templateGuid)
                .eq(PrintTypeTemplateDO::getIsDelete, Boolean.FALSE)
        );
        if (ObjectUtils.isEmpty(templateDO)) {
            log.warn("[delete]模版不存在，templateGuid={}", templateGuid);
            throw new BusinessException(Constant.TEMPLATE_NOT_EXIST);
        }
        printTypeTemplateMapper.deleteById(templateDO.getId());
        templateRTypeService.removeByTemplateGuid(templateGuid);
        templateRItemService.removeByTemplateGuid(templateGuid);
        templateRStoreService.removeByTemplateGuid(templateGuid);

        return true;
    }

    @Override
    public PrintTypeTemplateDetailDTO queryByStoreAndInvoiceType(TemplateDetailQO query) {
        if (StringUtils.isEmpty(query.getStoreGuid())) {
            throw new BusinessException("门店guid不能为空");
        }
        BrandDTO brandDTO = storeDeviceFeignService.queryBrandByStoreGuid(query.getStoreGuid());
        if (Objects.isNull(brandDTO)) {
            log.warn("未查询到品牌,storeGuid={}", query.getStoreGuid());
            return new PrintTypeTemplateDetailDTO();
        }
        // 查询部分门店
        PrintTypeTemplateRStoreDO one = templateRStoreService.queryTemplateByInvoiceTypeAndStoreGuid(
                String.valueOf(query.getInvoiceType()), query.getStoreGuid());
        // 查询所有模板
        List<PrintTypeTemplateDO> templateDOList = printTypeTemplateMapper.selectList(
                new LambdaQueryWrapper<PrintTypeTemplateDO>()
                        .eq(PrintTypeTemplateDO::getBrandGuid, brandDTO.getGuid())
                        .like(PrintTypeTemplateDO::getInvoiceType, query.getInvoiceType())
                        .eq(PrintTypeTemplateDO::getIsDelete, Boolean.FALSE)
                        .eq(PrintTypeTemplateDO::getIsEnable, Boolean.TRUE)
        );
        if (CollectionUtils.isEmpty(templateDOList) && Objects.isNull(one)) {
            log.warn("未查询到模版,query={}", JacksonUtils.writeValueAsString(query));
            return new PrintTypeTemplateDetailDTO();
        }
        PrintTypeTemplateDO storeTemplateDO = templateDOList.stream()
                .filter(PrintTypeTemplateDO::getIsAllStore)
                .findFirst()
                .orElse(null);
        // 优先是部分门店, 如果部分门店没有，则匹配全部门店
        if (Objects.nonNull(one)) {
            storeTemplateDO = templateDOList.stream()
                    .filter(e -> one.getTemplateGuid().equals(e.getGuid()))
                    .findFirst()
                    .orElse(null);
        }
        if (Objects.isNull(storeTemplateDO)) {
            log.error("数据异常");
            return new PrintTypeTemplateDetailDTO();
        }
        PrintTypeTemplateDetailDTO templateDetailDTO = new PrintTypeTemplateDetailDTO();
        templateDetailDTO.setGuid(storeTemplateDO.getGuid());
        templateDetailDTO.setName(storeTemplateDO.getName());
        List<TemplateTypeVO> typeList = templateRTypeService.queryTemplateTypeVOList(storeTemplateDO.getGuid(), query.getItemGuidList());
        templateDetailDTO.setTypeList(typeList);
        templateDetailDTO.setInvoiceType(Arrays.asList(storeTemplateDO.getInvoiceType().split(",")));
        templateDetailDTO.setIsAllStore(storeTemplateDO.getIsAllStore());
        return templateDetailDTO;
    }

    @Override
    public List<String> queryStoreByBrand(TemplateStoreQO query) {
        List<PrintTypeTemplateRStoreDO> list = templateRStoreService.queryTemplateStoreGuidList(query.getBrandGuid(),
                query.getInvoiceType(), query.getTemplateGuid());
        return list.stream()
                .map(PrintTypeTemplateRStoreDO::getStoreGuid)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public Boolean checkTemplateName(PrintTypeTemplateDTO query) {
        return printTypeTemplateMapper.checkTemplateName(query.getBrandGuid(), query.getName(), query.getGuid()) > 0;
    }

}
