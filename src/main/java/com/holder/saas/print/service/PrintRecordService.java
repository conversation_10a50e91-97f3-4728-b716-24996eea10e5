package com.holder.saas.print.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holder.saas.print.entity.domain.PrintRecordDO;
import com.holderzone.saas.store.dto.print.PrintOrderDTO;
import com.holderzone.saas.store.dto.print.PrintRecordDTO;
import com.holderzone.saas.store.dto.print.PrintRecordReqDTO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintRecordService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface PrintRecordService extends IService<PrintRecordDO> {

    String insertRecord(PrintDTO printDTO);

    void deleteRecord(PrintRecordReqDTO printRecordReqDTO);

    void batchDeleteRecord(List<String> arrayOfRecordGuid);

    void deletePrinterRecord(String printerGuid);

    void batchDeletePrinterRecord(List<String> arrayOfPrinterGuid);

    void deleteStorePrintRecord(String storeGuid);

    void deleteHistoryRecord();

    void updatePrintResult(PrintRecordReqDTO printRecordReqDTO);

    void updatePendingResult(String recordGuid);

    List<PrintRecordDTO> listRecord(PrintRecordReqDTO printRecordReqDTO);

    List<PrintOrderDTO> getPrintOrder(PrintRecordReqDTO printRecordReqDTO);

    PrintOrderDTO getTestPrintOrder(FormatDTO formatDTO);

    List<PrintOrderDTO> getTestPrintOrders(FormatDTO formatDTO);

    void reprintTakeaway(String storeGuid, String masterDeviceId);

    List<PrintOrderDTO> reprintTakeawayPrintOrderList(String storeGuid);
}
