package com.holder.saas.print.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holder.saas.print.entity.domain.type.PrintTypeTemplateRItemDO;
import com.holder.saas.print.entity.domain.type.PrintTypeTemplateRTypeDO;
import com.holder.saas.print.mapper.PrintTypeTemplateRItemMapper;
import com.holder.saas.print.mapper.PrintTypeTemplateRTypeMapper;
import com.holder.saas.print.service.PrintTypeTemplateRTypeService;
import com.holder.saas.print.service.feign.ItemClientService;
import com.holder.saas.print.service.feign.StoreDeviceFeignService;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayItemRespDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.print.type.TemplateItemVO;
import com.holderzone.saas.store.dto.print.type.TemplateTypeVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 打印分类模版关联分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Slf4j
@Service
@AllArgsConstructor
public class PrintTypeTemplateRTypeServiceImpl extends ServiceImpl<PrintTypeTemplateRTypeMapper, PrintTypeTemplateRTypeDO>
        implements PrintTypeTemplateRTypeService {

    private final PrintTypeTemplateRTypeMapper rTypeMapper;

    private final PrintTypeTemplateRItemMapper rItemMapper;

    private final ItemClientService itemClientService;

    @Resource
    private StoreDeviceFeignService storeDeviceFeignService;

    @Override
    public void removeByTemplateGuid(String templateGuid) {
        rTypeMapper.delete(new LambdaQueryWrapper<PrintTypeTemplateRTypeDO>()
                .eq(PrintTypeTemplateRTypeDO::getTemplateGuid, templateGuid));
    }

    /**
     * 根据模板Guid查询模板类型VO列表
     *
     * @param templateGuid      模板Guid
     * @param printItemGuidList
     * @return 模板类型VO列表
     */
    @Override
    public List<TemplateTypeVO> queryTemplateTypeVOList(String templateGuid, List<String> printItemGuidList) {
        // 查询PrintTypeTemplateRTypeDO列表
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("rTypeDOList");
        List<PrintTypeTemplateRTypeDO> rTypeDOList = rTypeMapper.selectList(
                new LambdaQueryWrapper<PrintTypeTemplateRTypeDO>()
                        .eq(PrintTypeTemplateRTypeDO::getTemplateGuid, templateGuid)
                        .eq(PrintTypeTemplateRTypeDO::getIsDelete, Boolean.FALSE)
        );
        stopWatch.stop();

        // 查询PrintTypeTemplateRItemDO列表
        stopWatch.start("rItemDOList");
        List<PrintTypeTemplateRItemDO> rItemDOList = rItemMapper.selectList(
                new LambdaQueryWrapper<PrintTypeTemplateRItemDO>()
                        .eq(PrintTypeTemplateRItemDO::getTemplateGuid, templateGuid)
                        .in(!CollectionUtils.isEmpty(printItemGuidList), PrintTypeTemplateRItemDO::getItemGuid, printItemGuidList)
                        .eq(PrintTypeTemplateRItemDO::getIsDelete, Boolean.FALSE)
        );
        stopWatch.stop();

        List<String> skuGuidList = rItemDOList.stream()
                .map(PrintTypeTemplateRItemDO::getProductSpecGuid)
                .distinct()
                .collect(Collectors.toList());

        Map<String, ItemWebRespDTO> skuForNameMap = mapSkuForName(skuGuidList);
        log.info("skuForNameMap:{}", skuForNameMap.size());

        // 根据类型Guid分组
        Map<String, List<PrintTypeTemplateRItemDO>> typeItemMap = rItemDOList.stream()
                .collect(Collectors.groupingBy(PrintTypeTemplateRItemDO::getTypeGuid));

        // 获取唯一的ItemGuid列表
        List<String> itemGuidList = rItemDOList.stream()
                .map(PrintTypeTemplateRItemDO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());

        // 创建ItemStringListDTO并设置数据列表
        ItemStringListDTO listDTO = new ItemStringListDTO();
        listDTO.setDataList(itemGuidList);

        // 查询Item信息列表
        stopWatch.start("selectItemInfoList");
        List<ItemInfoRespDTO> itemInfoList = itemClientService.getItemNameList(listDTO);
        stopWatch.stop();

        // 将ItemInfoRespDTO按ItemGuid作为键进行分组
        Map<String, ItemInfoRespDTO> itemInfoMap = itemInfoList.stream()
                .collect(Collectors.toMap(ItemInfoRespDTO::getItemGuid, Function.identity(), (oldValue, newValue) -> oldValue));

        // 创建模板类型VO列表
        List<TemplateTypeVO> typeList = Lists.newArrayList();

        String bandGuid = itemInfoList.get(0).getBrandGuid();
        BrandDTO brandDTO = storeDeviceFeignService.queryBrandByGuid(bandGuid);
        log.info("菜品模式数据：{}", JacksonUtils.writeValueAsString(brandDTO));

        // 遍历PrintTypeTemplateRTypeDO列表
        addTypeList(rTypeDOList, typeItemMap, itemInfoMap, skuForNameMap, typeList, brandDTO);
        log.info("[性能排查]queryTemplateTypeVOList={}", stopWatch.prettyPrint());

        // 返回模板类型VO列表
        return typeList;
    }

    private static void addTypeList(List<PrintTypeTemplateRTypeDO> rTypeDOList,
                                    Map<String, List<PrintTypeTemplateRItemDO>> typeItemMap,
                                    Map<String, ItemInfoRespDTO> itemInfoMap,
                                    Map<String, ItemWebRespDTO> skuForNameMap,
                                    List<TemplateTypeVO> typeList,
                                    BrandDTO brandDTO) {
        rTypeDOList.forEach(rType -> {
            // 创建模板类型VO
            TemplateTypeVO typeVO = new TemplateTypeVO();
            typeVO.setTypeGuid(rType.getGuid());
            typeVO.setTypeName(rType.getName());

            // 创建模板项列表
            List<TemplateItemVO> itemList = Lists.newArrayList();

            // 获取对应类型的PrintTypeTemplateRItemDO列表
            List<PrintTypeTemplateRItemDO> itemDOList = typeItemMap.get(rType.getGuid());

            // 如果不为空
            if (!CollectionUtils.isEmpty(itemDOList)) {
                // 遍历PrintTypeTemplateRItemDO列表
                itemDOList.forEach(itemDO -> {
                    // 创建模板项VO
                    TemplateItemVO itemVO = new TemplateItemVO();
                    itemVO.setItemGuid(itemDO.getItemGuid());

                    // 获取对应的ItemInfoRespDTO
                    ItemInfoRespDTO itemInfoRespDTO = itemInfoMap.get(itemDO.getItemGuid());

                    // 如果为空
                    if (ObjectUtils.isEmpty(itemInfoRespDTO)) {
                        return;
                    }

                    checkItemVO(skuForNameMap, itemDO, itemVO, itemInfoRespDTO, brandDTO);
                    itemList.add(itemVO);
                });
            }

            // 设置模板项列表到模板类型VO
            typeVO.setItemList(itemList);
            typeList.add(typeVO);
        });
    }

    private static void checkItemVO(Map<String, ItemWebRespDTO> skuForNameMap,
                                    PrintTypeTemplateRItemDO itemDO,
                                    TemplateItemVO itemVO,
                                    ItemInfoRespDTO itemInfoRespDTO,
                                    BrandDTO brandDTO) {
        if (!StringUtils.isEmpty(itemDO.getProductSpecGuid())) {
            log.warn("productSpecGuid为空");
            ItemWebRespDTO dto = skuForNameMap.get(itemDO.getProductSpecGuid());
            if (!ObjectUtils.isEmpty(dto)
                    && (!StringUtils.isEmpty(dto.getPlanItemName()))) {
                if (brandDTO.getSalesModel() == 2) {
                    itemVO.setPlanItemName(dto.getPlanItemName());
                }
                itemVO.setProductSpecGuid(itemDO.getProductSpecGuid());
            }
        }

        itemVO.setName(itemInfoRespDTO.getName());
        itemVO.setTypeGuid(itemInfoRespDTO.getTypeGuid());
    }


    /**
     * 处理规格名称
     *
     * @param skuGuidList 规格
     * @return Map<String, ItemWebRespDTO>
     */
    public Map<String, ItemWebRespDTO> mapSkuForName(List<String> skuGuidList) {
        List<ItemWebRespDTO> skuForNameList = itemClientService.listSkuForName(skuGuidList);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(skuForNameList)) {
            log.warn("未查询到规格对应名称,skuGuidList={}", skuGuidList);
            return new HashMap<>();
        }
        return skuForNameList.stream()
                .collect(Collectors.toMap(ItemWebRespDTO::getSkuGuid, Function.identity(), (v1, v2) -> v2));
    }
}
