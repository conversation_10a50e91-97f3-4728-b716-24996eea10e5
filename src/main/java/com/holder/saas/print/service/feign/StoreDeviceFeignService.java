package com.holder.saas.print.service.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreDeviceFeignService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
@Component
@FeignClient(value = "holder-saas-store-organization", fallbackFactory = StoreDeviceFeignService.DefaultFallbackFactory.class)
public interface StoreDeviceFeignService {

    /**
     * 根据门店guid查询该门店下的master一体机
     *
     * @param storeGuid 门店guid
     * @return 设备dto
     */
    @RequestMapping(method = RequestMethod.GET, value = "/device/get_master_device_by_storeguid/{storeGuid}")
    StoreDeviceDTO getMasterDeviceByStoreGuid(@PathVariable("storeGuid") String storeGuid);

    /**
     * 根据传入的guid数组查询门店列表（返回扁平结构、只包含guid和name两列）
     *
     * @param singleDataDTO 门店guid集合及品牌guid
     * @return 门店列表（扁平结构、只包含guid和name两列）
     */
    @ApiOperation("根据传入的guid数组查询组织列表")
    @PostMapping("/store/query_store_by_idlist_and_brand")
    List<StoreDTO> queryStoreByIdList(@RequestBody SingleDataDTO singleDataDTO);

    /**
     * 根据品牌guid查询品牌信息
     *
     * @param brandGuid 品牌guid
     * @return 品牌信息
     */
    @ApiOperation("根据品牌guid查询品牌信息")
    @PostMapping("/brand/query_brand_by_guid")
    BrandDTO queryBrandByGuid(@RequestParam("brandGuid") String brandGuid);

    /**
     * 根据门店guid查询门店关联的品牌信息
     *
     * @param storeGuid 门店guid
     * @return 品牌信息
     */
    @ApiOperation(value = "根据门店guid查询门店关联的品牌信息")
    @RequestMapping("/store/query_brand_by_storeguid")
    BrandDTO queryBrandByStoreGuid(@RequestParam("storeGuid") String storeGuid);

    @Slf4j
    @Component
    class DefaultFallbackFactory implements FallbackFactory<StoreDeviceFeignService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public StoreDeviceFeignService create(Throwable throwable) {
            return new StoreDeviceFeignService() {

                @Override
                public StoreDeviceDTO getMasterDeviceByStoreGuid(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "getMasterDeviceByStoreGuid", storeGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<StoreDTO> queryStoreByIdList(SingleDataDTO singleDataDTO) {
                    log.error(HYSTRIX_PATTERN, "queryStoreByIdList", JacksonUtils.writeValueAsString(singleDataDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public BrandDTO queryBrandByGuid(String brandGuid) {
                    log.error(HYSTRIX_PATTERN, "queryBrandByGuid", JacksonUtils.writeValueAsString(brandGuid),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public BrandDTO queryBrandByStoreGuid(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "queryBrandByStoreGuid", storeGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

            };
        }
    }
}
