package com.holder.saas.print.service.feign;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.common.PrintItemTypeDTO;
import com.holderzone.saas.store.dto.item.req.ItemSpuReqDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className ItemClientService
 * @date 2021/1/8 17:12
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-store-item", fallbackFactory = ItemClientService.FallBack.class)
public interface ItemClientService {

    @PostMapping("item/get_erp_sku_guid")
    Map<String, String> getErpSkuGuids(@RequestBody SingleDataDTO skuIdList);

    @PostMapping("/item/get_item_info")
    ItemInfoRespDTO getItemInfo(@RequestBody ItemSingleDTO itemSingleDTO);

    /**
     * @param skuGuid 根据skuGuid
     * @return SkuInfoRespDTO
     */
    @GetMapping("/item_sku/info")
    SkuInfoRespDTO info(@RequestParam String skuGuid);

    /**
     * 通过商品guid和门店guid查询对应spu的所有商品Guid
     *
     * @param itemSpuReqDTO 请求参数
     * @return 商品guid集合
     */
    @PostMapping("/item/selectSpuItems")
    List<String> selectSpuItems(@RequestBody ItemSpuReqDTO itemSpuReqDTO);

    /**
     * 通过商品guid和门店guid查询对应spu的所有商品Guid
     *
     * @param itemSpuReqDTO 请求参数
     * @return 商品guid集合
     */
    @PostMapping("/item/selectSpuItemMaps")
    Map<String, List<String>> selectSpuItemMaps(@RequestBody ItemSpuReqDTO itemSpuReqDTO);

    /**
     * 获取门店分类以及指定商品
     *
     * @param itemTypeDTO guid
     * @return 分类以及指定商品
     */
    @ApiOperation(value = "获取品牌下所有分类及商品")
    @PostMapping("/item/select_print_item_type")
    PrintSortRespDTO selectPrintItemType(@RequestBody PrintItemTypeDTO itemTypeDTO);

    /**
     * 根据门店GUID查询所有商品详情已经分类信息（区分销售模式）
     *
     * @param itemSingleDTO storeGuid
     * @return 分类和商品详情
     */
    @PostMapping("/item/query_store_item_By_sales_model")
    ItemInfoAndTypeRespDTO queryStoreItemBySalesModel(@RequestBody ItemSingleDTO itemSingleDTO);


    /**
     * 新根据门店GUID查询所有商品详情已经分类信息（区分销售模式）
     * 只适用于打印业务
     *
     * @param itemSingleDTO storeGuid
     * @return 分类和商品详情
     */
    @ApiOperation(value = "新根据门店GUID查询所有商品详情已经分类信息（区分销售模式）")
    @PostMapping("/item/query_store_item_By_sales_model_new")
    ItemInfoAndTypeRespDTO queryStoreItemBySalesNew(@RequestBody ItemSingleDTO itemSingleDTO);


    /**
     * 根据门店Guid和商品Guid查询门店下有没有这些个商品
     * 讲道理我觉得这个接口是没有必要的
     *
     * @param itemStringListDTO 门店guid，商品guid
     * @return 有则返回商品信息，无则返回空
     */
    @PostMapping("/item/query_store_item_and_filter")
    ItemInfoAndTypeRespDTO queryStoreItemAndFilter(@RequestBody ItemStringListDTO itemStringListDTO);

    @ApiOperation(value = "根据商品guid获取门店商品详情列表（区分销售模式）")
    @PostMapping("/item/list_item_info_by_sales_model")
    List<ItemInfoRespDTO> listItemInfoBySalesModel(@RequestBody ItemStringListDTO itemStringListDTO);


    @ApiOperation(value = "新根据商品guid获取门店商品详情列表（区分销售模式）")
    @PostMapping("/item/list_item_info_by_sales_model_new")
    List<ItemInfoRespDTO> listItemInfoBySalesModelNew(@RequestBody ItemStringListDTO itemStringListDTO);

    @ApiOperation(value = "获取商品详情列表", notes = "获取商品详情列表")
    @PostMapping("/item/get_item_info_list")
    List<ItemInfoRespDTO> selectItemInfoList(@RequestBody ItemStringListDTO itemStringListDTO);

    @ApiOperation(value = "获取商品名称")
    @PostMapping("/item/get_item_name_list")
    List<ItemInfoRespDTO> getItemNameList(@RequestBody ItemStringListDTO itemStringListDTO);

    /**
     * 通过sku查询父级ItemGuid
     * 如果已经是父级，返回本身ItemGuid
     */
    @ApiOperation(value = "通过sku查询父级ItemGuid")
    @PostMapping("/item/query_parent_item_guid_by_sku")
    Map<String, String> queryParentItemGuidBySku(@RequestBody ItemStringListDTO query);

    /**
     * 通过item查询父级ItemGuid
     * 如果已经是父级，返回本身ItemGuid
     */
    @ApiOperation(value = "通过item查询父级ItemGuid")
    @PostMapping("/item/query_parent_item_guid_by_item")
    Map<String, String> queryParentItemGuidByItem(@RequestBody ItemStringListDTO query);

    /**
     * 根据规格guid查询对应商品全名
     * 包含菜谱
     *
     * @param skuGuidList 规格guid列表
     * @return 商品全名
     */
    @ApiOperation(value = "根据规格guid查询对应商品全名")
    @PostMapping("/item_sku/list_sku_for_name")
    List<ItemWebRespDTO> listSkuForName(@RequestBody List<String> skuGuidList);

    @Component
    class FallBack implements FallbackFactory<ItemClientService> {
        private static final Logger logger = LoggerFactory.getLogger(FallBack.class);

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ItemClientService create(Throwable throwable) {
            return new ItemClientService() {

                @Override
                public Map<String, String> getErpSkuGuids(SingleDataDTO skuguids) {
                    logger.error("获取erpGuid错误，e={}", throwable.getMessage());
                    throw new ParameterException("获取erpGuid调用异常");
                }

                @Override
                public ItemInfoRespDTO getItemInfo(ItemSingleDTO itemSingleDTO) {
                    logger.error("获取单个菜品信息错误，e={}", throwable.getMessage());
                    throw new ParameterException("获取单个菜品信息调用异常");
                }

                @Override
                public SkuInfoRespDTO info(String skuGuid) {
                    logger.error("根据skuGuid获取sku信息错误，e={}", throwable.getMessage());
                    throw new ParameterException("根据skuGuid获取sku信息调用异常");
                }

                @Override
                public List<String> selectSpuItems(ItemSpuReqDTO itemSpuReqDTO) {
                    logger.error("查询对应spu商品guid异常，e={}", throwable.getMessage());
                    throw new ParameterException("查询对应spu商品guid异常");
                }

                @Override
                public Map<String, List<String>> selectSpuItemMaps(ItemSpuReqDTO itemSpuReqDTO) {
                    logger.error("查询对应spu商品guids异常，e={}", throwable.getMessage());
                    throw new ParameterException("查询对应spu商品guid异常");
                }

                @Override
                public PrintSortRespDTO selectPrintItemType(PrintItemTypeDTO itemTypeDTO) {
                    logger.error("查询selectPrintItemType方法异常，e={}", throwable.getMessage());
                    throw new ParameterException("查询selectPrintItemType方法异常");
                }

                @Override
                public ItemInfoAndTypeRespDTO queryStoreItemBySalesModel(ItemSingleDTO itemSingleDTO) {
                    logger.error("查询queryStoreItemBySalesModel方法异常，e={}", throwable.getMessage());
                    throw new ParameterException("查询queryStoreItemBySalesModel方法异常");
                }

                @Override
                public ItemInfoAndTypeRespDTO queryStoreItemBySalesNew(ItemSingleDTO itemSingleDTO) {
                    logger.error("查询queryStoreItemBySalesNew方法异常，e={}", throwable.getMessage());
                    throw new ParameterException("查询queryStoreItemBySalesNew方法异常");
                }

                @Override
                public ItemInfoAndTypeRespDTO queryStoreItemAndFilter(ItemStringListDTO itemStringListDTO) {
                    logger.error("查询queryStoreItemAndFilter方法异常，e={}", throwable.getMessage());
                    throw new ParameterException("查询queryStoreItemAndFilter方法异常");
                }

                @Override
                public List<ItemInfoRespDTO> listItemInfoBySalesModel(ItemStringListDTO itemStringListDTO) {
                    logger.error("查询listItemInfoBySalesModel方法异常，e={}", throwable.getMessage());
                    throw new ParameterException("查询listItemInfoBySalesModel方法异常");
                }

                @Override
                public List<ItemInfoRespDTO> listItemInfoBySalesModelNew(ItemStringListDTO itemStringListDTO) {
                    logger.error("查询listItemInfoBySalesModelNew方法异常，e={}", throwable.getMessage());
                    throw new ParameterException("查询listItemInfoBySalesModelNew方法异常");
                }

                @Override
                public List<ItemInfoRespDTO> selectItemInfoList(ItemStringListDTO itemStringListDTO) {
                    logger.error(HYSTRIX_PATTERN, "selectItemInfoList", JacksonUtils.writeValueAsString(itemStringListDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<ItemInfoRespDTO> getItemNameList(ItemStringListDTO itemStringListDTO) {
                    logger.error(HYSTRIX_PATTERN, "getItemNameList", JacksonUtils.writeValueAsString(itemStringListDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Map<String, String> queryParentItemGuidBySku(ItemStringListDTO query) {
                    logger.error(HYSTRIX_PATTERN, "queryParentItemGuidBySku", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Map<String, String> queryParentItemGuidByItem(ItemStringListDTO query) {
                    logger.error(HYSTRIX_PATTERN, "queryParentItemGuidByItem", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<ItemWebRespDTO> listSkuForName(List<String> skuGuidList) {
                    logger.error(HYSTRIX_PATTERN, "listSkuForName", JacksonUtils.writeValueAsString(skuGuidList),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

            };
        }
    }

}
