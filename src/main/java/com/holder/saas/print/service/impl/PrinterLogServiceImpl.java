package com.holder.saas.print.service.impl;

import com.holder.saas.print.service.PrinterLogService;
import com.holder.saas.print.utils.HttpRequestUtils;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.base.dto.log.LogDTO;
import com.holderzone.framework.exception.ParamException;
import com.holderzone.framework.log.busines.DefaultLogBuilder;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.log.busines.RecordType;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.print.PrinterAreaDTO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.PrinterItemDTO;
import com.holderzone.saas.store.enums.print.BusinessTypeEnum;
import com.holderzone.sdk.event.LogPublisher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterLogServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印机日志管理实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class PrinterLogServiceImpl implements PrinterLogService {

    private static final String LOG_MODULE = "打印设置";

    private final LogPublisher logPublisher;

    @Autowired
    public PrinterLogServiceImpl(LogPublisher logPublisher) {
        this.logPublisher = logPublisher;
    }

    @Override
    public void publishAddLog(Object source, PrinterDTO printerDtoAfter) {
//        fakeGuidReplacedByName(printerDtoAfter);
        BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.ofType(printerDtoAfter.getBusinessType());
        List<String> operationTarget = Collections.singletonList(Objects.requireNonNull(businessTypeEnum).getDesc());
        publishLog(source, HttpRequestUtils.getRequestUri(),
                "添加打印机", operationTarget, OperatorType.ADD,
                null, printerDtoAfter,
                printerDtoAfter.getStoreGuid(), printerDtoAfter.getStoreName());
    }

    @Override
    public void publishDeleteLog(Object source, PrinterDTO printerDtoBefore) {
//        fakeGuidReplacedByName(printerDtoBefore);
        BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.ofType(printerDtoBefore.getBusinessType());
        List<String> operationTarget = Collections.singletonList(Objects.requireNonNull(businessTypeEnum).getDesc());
        publishLog(source, HttpRequestUtils.getRequestUri(),
                "删除打印机", operationTarget, OperatorType.DELETE,
                printerDtoBefore, null,
                printerDtoBefore.getStoreGuid(), printerDtoBefore.getStoreName());
    }

    @Override
    public void publishUpdateLog(Object source, PrinterDTO printerDtoBefore, PrinterDTO printerDtoAfter) {
//        fakeGuidReplacedByName(printerDtoBefore);
//        fakeGuidReplacedByName(printerDtoAfter);
        BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.ofType(printerDtoBefore.getBusinessType());
        List<String> operationTarget = Collections.singletonList(Objects.requireNonNull(businessTypeEnum).getDesc());
        publishLog(source, HttpRequestUtils.getRequestUri(),
                "修改打印机", operationTarget, OperatorType.UPDARE,
                printerDtoBefore, printerDtoAfter,
                printerDtoBefore.getStoreGuid(), printerDtoBefore.getStoreName());
    }

    /**
     * 发布日志记录事件
     *
     * @param source          this
     * @param requestURI      请求uri
     * @param button          操作按钮
     * @param operationTarget 操作对象
     * @param operatorType    操作类型
     * @param contentBefore   操作前
     * @param contentAfter    操作后
     * @param storeGuid       门店guid
     * @param storeName       门店名称
     */
    private void publishLog(Object source, String requestURI, String button,
                            List<String> operationTarget, OperatorType operatorType,
                            Object contentBefore, Object contentAfter, String storeGuid, String storeName) {
        String userName = UserContextUtils.getUserName();
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        DefaultLogBuilder builder = new DefaultLogBuilder(userName,
                requestURI, operatorType, Platform.CASHIERSYSTEM, RecordType.NORMAL);
        builder.setContentBefore(contentBefore);
        builder.setContentAfter(contentAfter);
        LogDTO logDTO = builder.build();
        logDTO.setModule(LOG_MODULE);
        logDTO.setExt1(enterpriseGuid);
        logDTO.setExt2(storeGuid + "/" + storeName);
        logDTO.setExt3(button);
        logDTO.setParams(JacksonUtils.writeValueAsString(operationTarget));
        try {
            log.info("发送的日志:{}", JacksonUtils.writeValueAsString(logDTO));
            logPublisher.send(source, logDTO);
        } catch (ParamException e) {
            log.warn("日志记录失败" + JacksonUtils.writeValueAsString(logDTO), e);
        }
    }

    /**
     * todo 告诉陈印此处没有Fake字段了，枚举按照真实的自己取，单据、菜品名、区域名从真实字段取值
     *
     * @param printerDTO
     */
    private void fakeGuidReplacedByName(PrinterDTO printerDTO) {
        System.out.println(JacksonUtils.writeValueAsString(printerDTO));
        List<PrinterItemDTO> arrayOfItemDTO = printerDTO.getArrayOfItemDTO();
        if (!CollectionUtils.isEmpty(arrayOfItemDTO)) {
            List<String> fakeItemGuidReplacedByName = arrayOfItemDTO.stream()
                    .map(PrinterItemDTO::getItemName)
                    .collect(Collectors.toList());
            printerDTO.setArrayOfItemGuid(fakeItemGuidReplacedByName);
        } else {
            printerDTO.setArrayOfItemGuid(Collections.emptyList());
            printerDTO.setArrayOfItemDTO(Collections.emptyList());
        }
        List<PrinterAreaDTO> areaDTOList = printerDTO.getArrayOfAreaDTO();
        if (!CollectionUtils.isEmpty(areaDTOList)) {
            List<String> fakeAreaGuidReplacedByName = areaDTOList.stream()
                    .map(PrinterAreaDTO::getAreaName)
                    .collect(Collectors.toList());
            printerDTO.setArrayOfAreaGuid(fakeAreaGuidReplacedByName);
        } else {
            printerDTO.setArrayOfAreaGuid(Collections.emptyList());
            printerDTO.setArrayOfAreaDTO(Collections.emptyList());
        }
    }
}
