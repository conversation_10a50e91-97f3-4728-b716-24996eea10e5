package com.holder.saas.print.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.holder.saas.print.entity.domain.PrintRecordDO;
import com.holder.saas.print.entity.domain.PrinterDO;
import com.holder.saas.print.entity.read.PrintRecordReadDO;
import com.holder.saas.print.entity.read.PrinterReadDO;
import com.holder.saas.print.service.DistributedService;
import com.holder.saas.print.service.PrintRecordCloneService;
import com.holder.saas.print.service.PrintTypeTemplateService;
import com.holder.saas.print.service.PrinterFormatService;
import com.holder.saas.print.service.feign.ItemClientService;
import com.holder.saas.print.service.feign.TakeoutClientService;
import com.holder.saas.print.utils.DeepCloneUtils;
import com.holder.saas.print.utils.template.content.PrintCutUtils;
import com.holder.saas.print.utils.template.content.PrintRepairUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.bo.FoodFinishBarCodeBO;
import com.holderzone.saas.store.constant.RedisKeyConstant;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.ItemSpuReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.print.content.*;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.format.OrderItemFormatDTO;
import com.holderzone.saas.store.dto.print.format.TakeoutFormatDTO;
import com.holderzone.saas.store.dto.print.type.PrintTypeTemplateDetailDTO;
import com.holderzone.saas.store.dto.print.type.TemplateDetailQO;
import com.holderzone.saas.store.dto.print.type.TemplateItemVO;
import com.holderzone.saas.store.dto.print.type.TemplateTypeVO;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintRecordCloneServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印单切纸拷贝实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public final class PrintRecordCloneServiceImpl implements PrintRecordCloneService {

    private final DistributedService distributedService;
    private final ItemClientService itemClientService;

    private final PrintTypeTemplateService printTypeTemplateService;

    private final RedissonClient redissonSingleClient;

    private final TakeoutClientService takeoutClientService;

    private final PrinterFormatService printerFormatService;

    @Autowired
    public PrintRecordCloneServiceImpl(DistributedService distributedService,
                                       ItemClientService itemClientService,
                                       PrintTypeTemplateService printTypeTemplateService,
                                       RedissonClient redissonSingleClient,
                                       TakeoutClientService takeoutClientService,
                                       PrinterFormatService printerFormatService) {
        this.distributedService = distributedService;
        this.itemClientService = itemClientService;
        this.printTypeTemplateService = printTypeTemplateService;
        this.redissonSingleClient = redissonSingleClient;
        this.takeoutClientService = takeoutClientService;
        this.printerFormatService = printerFormatService;
    }

    @Override
    public void cloneRecord(PrintDTO printDTO, List<PrinterReadDO> printers,
                            List<PrintRecordDO> printRecordsToInsert, List<PrintRecordReadDO> printRecordsToCache) {
        PrintRepairUtils.correctSubRecordItemNameAndUnit(printDTO);
        switch (InvoiceTypeEnum.ofType(printDTO.getInvoiceType())) {
            case ORDER_ITEM:
            case REFUND_ITEM:
            case LABEL: {
                splitAndClone((PrintBaseItemDTO) printDTO, printers,
                        printRecordsToInsert, printRecordsToCache);
                break;
            }
            default: {
                directClone(printDTO, printers,
                        printRecordsToInsert, printRecordsToCache);
                break;
            }
        }
    }

    private void splitAndClone(PrintBaseItemDTO printBaseItemDTO, List<PrinterReadDO> printers,
                               List<PrintRecordDO> printRecordsToInsert, List<PrintRecordReadDO> printRecordsToCache) {
        log.info("需打印的菜品数据 splitAndClone:{}", JacksonUtils.writeValueAsString(printBaseItemDTO));
        if (CollectionUtils.isEmpty(printBaseItemDTO.getItemRecordList())) {
            log.warn("商品列表为空");
            return;
        }
        // 非套餐
        List<PrintItemRecord> normal = printBaseItemDTO.getItemRecordList().stream()
                .filter(itemRecord -> CollectionUtils.isEmpty(itemRecord.getSubItemRecords()))
                .collect(Collectors.toList());
        // 套餐
        List<PrintItemRecord> setMeal = printBaseItemDTO.getItemRecordList().stream()
                .filter(itemRecord -> !CollectionUtils.isEmpty(itemRecord.getSubItemRecords()))
                .collect(Collectors.toList());
        // 是否为标签单
        boolean isPrintLabel = printBaseItemDTO instanceof PrintLabelDTO;
        boolean isOrderItem = printBaseItemDTO instanceof PrintOrderItemDTO;
        // 匹配菜品
        for (PrinterReadDO printerReadDO : printers) {
            //通过商品guid查询spu相同的商品信息
            ItemSpuReqDTO reqDTO = new ItemSpuReqDTO();
            reqDTO.setStoreGuid(printBaseItemDTO.getStoreGuid());
            reqDTO.setItemGuids(printerReadDO.getArrayOfItemGuid());
            log.info("[selectSpuItems]reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
            List<String> arrayOfItemGuid = itemClientService.selectSpuItems(reqDTO);
            log.info("[selectSpuItems]arrayOfItemGuid={}", arrayOfItemGuid);
            // 匹配非套餐菜品(筛选出这个打印机能打印的菜品集合)
            List<PrintItemRecord> printItemRecordsMatched = normal.stream()
                    .filter(itemRecord -> arrayOfItemGuid.contains(itemRecord.getItemGuid()))
                    .collect(Collectors.toList());
            // 匹配套餐菜品
            handelSetMeal(setMeal, arrayOfItemGuid, printItemRecordsMatched);
            // 重排序
            printItemRecordsMatched = sortedPrintItemRecordsMatched(printItemRecordsMatched, printBaseItemDTO.getItemRecordList());
            if (CollectionUtils.isEmpty(printItemRecordsMatched)) {
                log.warn("打印机商品匹配时发生错误");
                continue;
            }
            // 转换为PrintRecord
            // 根据切纸方式Clone多个PrintDTO
            List<List<PrintItemRecord>> itemRecordsByPrintCut = PrintCutUtils.splitItemByPrintCut(printItemRecordsMatched, printerReadDO, printBaseItemDTO);
            int size = itemRecordsByPrintCut.size();
            List<String> batchGuid = distributedService.nextBatchPrintRecordGuid(size);
            for (int i = 0; i < size; i++) {
                List<PrintItemRecord> printItemRecords = itemRecordsByPrintCut.get(i);
                PrintBaseItemDTO printBaseItemDtoCopied = (PrintBaseItemDTO) DeepCloneUtils.cloneObject(printBaseItemDTO);
                if (null == printBaseItemDtoCopied) {
                    continue;
                }
                if (isPrintLabel) {
                    PrintLabelDTO printLabelDTO = (PrintLabelDTO) printBaseItemDtoCopied;
                    printLabelDTO.setCurrentNo(i + 1);
                    printLabelDTO.setTotalNo(size);
                }
                printBaseItemDtoCopied.setItemRecordList(printItemRecords);
                String printerGuid = printerReadDO.getPrinterGuid();
                String recordGuid = batchGuid.get(size - i - 1);
                addCacheByTakeoutAndOrderItem(printBaseItemDTO, printBaseItemDtoCopied, isOrderItem, recordGuid);
                PrintRecordDO printRecordDO = buildPrintRecord(printBaseItemDtoCopied, printerGuid, recordGuid, printerReadDO.getPrintPriceType());
                printRecordsToInsert.add(printRecordDO);
                printRecordsToCache.add(mockPrintRecordReadDO(printRecordDO, printerReadDO));
            }
        }

        if (CollectionUtils.isEmpty(printRecordsToInsert)) {
            log.warn("打印机所有商品匹配时均发生错误");
        }
    }

    private List<PrintItemRecord> sortedPrintItemRecordsMatched(List<PrintItemRecord> printItemRecordsMatched,
                                                                List<PrintItemRecord> itemRecordList) {
        if (CollectionUtils.isEmpty(printItemRecordsMatched)) {
            log.warn("printItemRecordsMatched为空");
            return Lists.newArrayList();
        }
        List<String> orderItemGuidList = itemRecordList.stream().map(PrintItemRecord::getOrderItemGuid)
                .distinct()
                .collect(Collectors.toList());
        Map<String, List<PrintItemRecord>> printItemRecordsMatchedMap = printItemRecordsMatched.stream()
                .collect(Collectors.groupingBy(printItemRecord -> printItemRecord.getOrderItemGuid() != null ? printItemRecord.getOrderItemGuid() : "0",
                        Collectors.toList()));
        List<PrintItemRecord> sortedPrintItemRecordsMatched = Lists.newArrayList();
        for (String orderItemGuid : orderItemGuidList) {
            List<PrintItemRecord> printItemRecords = printItemRecordsMatchedMap.get(orderItemGuid == null ? "0" : orderItemGuid);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(printItemRecords)) {
                sortedPrintItemRecordsMatched.addAll(printItemRecords);
            }
        }
        return sortedPrintItemRecordsMatched;
    }

    private void addCacheByTakeoutAndOrderItem(PrintBaseItemDTO printBaseItemDTO,
                                               PrintBaseItemDTO printBaseItemDtoCopied,
                                               boolean isOrderItem,
                                               String recordGuid) {
        if (Objects.equals(InvoiceTypeEnum.ORDER_ITEM.getType(), printBaseItemDTO.getInvoiceType())
                || Objects.equals(InvoiceTypeEnum.TAKEOUT.getType(), printBaseItemDTO.getInvoiceType())) {
            OrderItemFormatDTO formatDTO = printerFormatService.query(printBaseItemDTO.getStoreGuid(), printBaseItemDTO.getInvoiceType());
            if (Objects.nonNull(formatDTO) && Objects.nonNull(formatDTO.getFoodFinishBarCode())
                    && formatDTO.getFoodFinishBarCode().isEnable()) {
                handelOrderItemCache(printBaseItemDtoCopied, isOrderItem, recordGuid);
            }
        }
    }

    private void handelOrderItemCache(PrintBaseItemDTO printBaseItemDtoCopied,
                                      boolean isOrderItem,
                                      String recordGuid) {
        if (Objects.equals(printBaseItemDtoCopied.getInvoiceType(), InvoiceTypeEnum.ORDER_ITEM.getType()) && isOrderItem) {
            PrintOrderItemDTO orderItemDTO = (PrintOrderItemDTO) printBaseItemDtoCopied;
            log.info("[点菜单][setFinishCodeToRedis]orderItemDTO={}", JacksonUtils.writeValueAsString(orderItemDTO));
            orderItemDTO.setFoodFinishBarCode(recordGuid);
            List<PrintItemRecord> itemRecordList = orderItemDTO.getItemRecordList();
            log.info("[点菜单]itemRecordList={}", JacksonUtils.writeValueAsString(itemRecordList));

            // 外卖订单商品guid处理
            setFinishCodeToRedis(orderItemDTO.getOrderNo(), itemRecordList, recordGuid);
        }
    }

    private void handelSetMeal(List<PrintItemRecord> setMeal, List<String> arrayOfItemGuid, List<PrintItemRecord> printItemRecordsMatched) {
        for (PrintItemRecord printItemRecord : setMeal) {
            List<PrintItemRecord> subPrintItemRecordsMatched = printItemRecord.getSubItemRecords().stream()
                    .filter(subItemRecord -> arrayOfItemGuid.contains(subItemRecord.getItemGuid()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(subPrintItemRecordsMatched)) {

                // 套餐子菜不空，深拷贝一个套餐
                PrintItemRecord printItemRecordClone = (PrintItemRecord) DeepCloneUtils.cloneObject(printItemRecord);
                if (null == printItemRecordClone) {
                    log.warn("套餐商品深拷贝时发生错误");
                    continue;
                }
                printItemRecordClone.setSubItemRecords(subPrintItemRecordsMatched);
                printItemRecordsMatched.add(printItemRecordClone);
            }
        }
    }

    private void setFinishCodeToRedis(String orderNo,
                                      List<PrintItemRecord> printItemRecords,
                                      String recordGuid) {
        String foodFinishBarCodeKey = RedisKeyConstant.FOOD_FINISH_BAR_CODE + recordGuid;
        RBucket<Object> foodFinishBarCodeBucket = redissonSingleClient.getBucket(foodFinishBarCodeKey, new StringCodec(StandardCharsets.UTF_8));
        FoodFinishBarCodeBO barCodeBO = new FoodFinishBarCodeBO();
        barCodeBO.setOrderNo(orderNo);
        List<String> orderItemGuidList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(printItemRecords)) {
            orderItemGuidList = printItemRecords.stream()
                    .map(PrintItemRecord::getOrderItemGuid)
                    .distinct()
                    .collect(Collectors.toList());
            List<String> subOrderItemGuidList = printItemRecords.stream()
                    .filter(itemRecord -> !CollectionUtils.isEmpty(itemRecord.getSubItemRecords()))
                    .flatMap(itemRecord -> itemRecord.getSubItemRecords().stream())
                    .map(PrintItemRecord::getOrderItemGuid)
                    .distinct()
                    .collect(Collectors.toList());
            orderItemGuidList.addAll(subOrderItemGuidList);
        }
        barCodeBO.setOrderItemGuidList(orderItemGuidList);
        foodFinishBarCodeBucket.set(JacksonUtils.writeValueAsString(barCodeBO), 7, TimeUnit.DAYS);

    }

    private void directClone(PrintDTO printDTO, List<PrinterReadDO> printers,
                             List<PrintRecordDO> printRecordsToInsert, List<PrintRecordReadDO> printRecordsToCache) {
        List<String> batchGuid = distributedService.nextBatchPrintRecordGuid(printers.size());
        for (PrinterReadDO printerReadDO : printers) {
            String printerGuid = printerReadDO.getPrinterGuid();
            String recordGuid = batchGuid.remove(batchGuid.size() - 1);
            setPrintContentArrayOfItemGuid(printerReadDO, printDTO);
            PrintRecordDO printRecordDO = buildPrintRecord(printDTO, printerGuid, recordGuid, printerReadDO.getPrintPriceType());
            printRecordsToInsert.add(printRecordDO);
            printRecordsToCache.add(mockPrintRecordReadDO(printRecordDO, printerReadDO));
        }
    }


    private void setPrintContentArrayOfItemGuid(PrinterReadDO printerReadDO, PrintDTO printDTO) {
        if (ObjectUtil.equal(printDTO.getInvoiceType(), InvoiceTypeEnum.CHANGE_ITEM.getType())) {
            try {
                PrintChangeItemDTO printChangeItemDTO = (PrintChangeItemDTO) printDTO;
                List<String> arrayOfItemGuid = printerReadDO.getArrayOfItemGuid();
                log.info("arrayOfItemGuid：{}", JacksonUtils.writeValueAsString(arrayOfItemGuid));
                printChangeItemDTO.setArrayOfItemGuid(arrayOfItemGuid);
            } catch (Exception e) {
                log.error("设置打印商品guid失败：{}", e.getMessage());
            }
        }
    }

    private PrintRecordDO buildPrintRecord(PrintDTO printDTO, String printerGuid, String recordGuid, Boolean printPriceType) {
        printDTO.setPrintPriceType(printPriceType);
        log.warn("[日志排查]printDTO={},printPriceType={}", JacksonUtils.writeValueAsString(printDTO), printPriceType);
        //堂食预结单和结账单需要重排序
        if (ObjectUtil.equal(printDTO.getInvoiceType(), InvoiceTypeEnum.PRE_CHECKOUT.getType())
                || ObjectUtil.equal(printDTO.getInvoiceType(), InvoiceTypeEnum.CHECKOUT.getType())) {
            try {
                //新PrintItemRecord
                List<PrintItemRecord> newPrintItemRecords = Lists.newArrayList();
                log.info("转化printDTO{}", JacksonUtils.writeValueAsString(printDTO));
                PrintBaseItemDTO printBaseItemDTO = (PrintBaseItemDTO) printDTO;
                log.info("排序之前数据printBaseItemDTO{}", JacksonUtils.writeValueAsString(printBaseItemDTO));
                List<PrintItemRecord> printItemRecords = printBaseItemDTO.getItemRecordList();
                log.info("排序之前数据{}", JacksonUtils.writeValueAsString(printItemRecords));


                if (!CollectionUtils.isEmpty(printItemRecords)
                        && printItemRecords.size() > 1
                        && (Objects.isNull(printDTO.getTradeMode()) || Objects.nonNull(printDTO.getTradeMode()) && printDTO.getTradeMode() != 2)) {
                    getNewPrintSortResponse(printDTO, newPrintItemRecords, printBaseItemDTO, printItemRecords);
                }
            } catch (Exception e) {
                log.error("打印排序失败：{}", e.getMessage());
            }
        }

        // 打印分类模版处理
        handlePrintType(printDTO, recordGuid);

        PrintRecordDO printRecordDO = new PrintRecordDO();
        printRecordDO.setRecordUid(StringUtils.isEmpty(printDTO.getPrintUid()) ? "0" : printDTO.getPrintUid());
        printRecordDO.setRecordGuid(recordGuid);
        printRecordDO.setStoreGuid(printDTO.getStoreGuid());
        printRecordDO.setDeviceId(printDTO.getDeviceId());
        printRecordDO.setInvoiceType(printDTO.getInvoiceType());
        printRecordDO.setPrinterGuid(printerGuid);
        printRecordDO.setCreateStaffGuid(printDTO.getOperatorStaffGuid());
        printRecordDO.setPrintContent(JacksonUtils.writeValueAsString(printDTO));

        return printRecordDO;
    }

    private void handlePrintType(PrintDTO printDTO, String recordGuid) {
        if (Lists.newArrayList(InvoiceTypeEnum.TAKEOUT.getType(), InvoiceTypeEnum.PRE_CHECKOUT.getType(),
                InvoiceTypeEnum.CHECKOUT.getType()).contains(printDTO.getInvoiceType())) {
            log.info("[TypeChange]转化printDTO={}", JacksonUtils.writeValueAsString(printDTO));
            PrintBaseItemDTO printBaseItemDTO = (PrintBaseItemDTO) printDTO;
            log.info("[TypeChange]处理之前数据printBaseItemDTO={}", JacksonUtils.writeValueAsString(printBaseItemDTO));
            List<PrintItemRecord> printItemRecords = printBaseItemDTO.getItemRecordList();
            log.info("[TypeChange]处理之前商品数据={}", JacksonUtils.writeValueAsString(printItemRecords));
            handlePrintTypeTemplate(printDTO, printItemRecords, printBaseItemDTO);
            // 外卖单存redis
            if (InvoiceTypeEnum.TAKEOUT.getType().equals(printDTO.getInvoiceType()) && printBaseItemDTO instanceof PrintTakeoutDTO) {
                PrintTakeoutDTO printTakeoutDTO = (PrintTakeoutDTO) printBaseItemDTO;
                log.info("[外卖单][setFinishCodeToRedis]printTakeoutDTO={}", JacksonUtils.writeValueAsString(printTakeoutDTO));
                printTakeoutDTO.setFoodFinishBarCode(recordGuid);

                // 外卖订单
                TakeoutFormatDTO formatDTO = printerFormatService.query(printDTO.getStoreGuid(), printDTO.getInvoiceType());
                if (Objects.nonNull(formatDTO) && Objects.nonNull(formatDTO.getFoodFinishBarCode())
                        && formatDTO.getFoodFinishBarCode().isEnable()) {
                    setTakeoutFinishCodeToRedis(printTakeoutDTO.getOrderNo(), printDTO.getStoreGuid(), recordGuid);
                }
            }
        }
    }

    private void setTakeoutFinishCodeToRedis(String orderNo, String storeGuid, String recordGuid) {
        String takeoutItemKey = RedisKeyConstant.TAKEOUT_ITEM + storeGuid + ":" + orderNo;
        RBucket<String> takeoutItemBucket = redissonSingleClient.getBucket(takeoutItemKey, new StringCodec(StandardCharsets.UTF_8));
        String takeoutItemJson = takeoutItemBucket.get();
        if (StringUtils.isEmpty(takeoutItemJson)) {
            log.warn("[setTakeoutFinishCodeToRedis]外卖单商品信息为空");
            return;
        }
        List<String> orderItemGuidList = JacksonUtils.toObjectList(String.class, takeoutItemJson);

        String foodFinishBarCodeKey = RedisKeyConstant.FOOD_FINISH_BAR_CODE + recordGuid;
        RBucket<Object> foodFinishBarCodeBucket = redissonSingleClient.getBucket(foodFinishBarCodeKey, new StringCodec(StandardCharsets.UTF_8));
        FoodFinishBarCodeBO barCodeBO = new FoodFinishBarCodeBO();
        barCodeBO.setOrderNo(orderNo);
        barCodeBO.setOrderItemGuidList(orderItemGuidList);
        foodFinishBarCodeBucket.set(JacksonUtils.writeValueAsString(barCodeBO), 7, TimeUnit.DAYS);
    }

    /**
     * 打印分类模版处理
     */
    private void handlePrintTypeTemplate(PrintDTO printDTO,
                                         List<PrintItemRecord> printItemRecords,
                                         PrintBaseItemDTO printBaseItemDTO) {
        try {
            // 查询模版
            List<String> printItemGuidList = printItemRecords.stream()
                    .map(PrintItemRecord::getItemGuid)
                    .distinct()
                    .collect(Collectors.toList());
            ItemStringListDTO listDTO = new ItemStringListDTO();
            listDTO.setDataList(printItemGuidList);
            Map<String, String> itemMap = itemClientService.queryParentItemGuidByItem(listDTO);
            List<String> itemParentGuidList = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(itemMap)) {
                itemParentGuidList.addAll(itemMap.values());
            }

            TemplateDetailQO query = new TemplateDetailQO();
            query.setStoreGuid(printDTO.getStoreGuid());
            query.setInvoiceType(printDTO.getInvoiceType());
            query.setItemGuidList(itemParentGuidList);
            PrintTypeTemplateDetailDTO templateDetailDTO = printTypeTemplateService.queryByStoreAndInvoiceType(query);
            log.info("templateDetailDTO:{}", JacksonUtils.writeValueAsString(templateDetailDTO));
            if (Objects.isNull(templateDetailDTO)) {
                log.warn("[TypeChange]未查询到分类模版,storeGuid={},invoiceType={}", printDTO.getStoreGuid(), printDTO.getInvoiceType());
                return;
            }

            Map<String, List<PrintItemRecord>> printByItemGuidMap = printItemRecords.stream()
                    .collect(Collectors.groupingBy(PrintItemRecord::getItemGuid));
            List<PrintItemRecord> newPrintItemList = Lists.newArrayList();
            List<TemplateTypeVO> typeList = templateDetailDTO.getTypeList();
            typeList.forEach(rType -> {
                List<String> itemGuidList = rType.getItemList().stream()
                        .map(TemplateItemVO::getItemGuid)
                        .distinct()
                        .collect(Collectors.toList());
                printByItemGuidMap.forEach((itemGuid, itemRecordList) -> {
                    if (CollectionUtils.isEmpty(itemRecordList)) {
                        return;
                    }
                    String parentGuid = itemMap.get(itemGuid);
                    if (itemGuidList.contains(parentGuid)) {
                        itemRecordList.forEach(itemRecord -> {
                            itemRecord.setItemTypeGuid(rType.getTypeGuid());
                            itemRecord.setItemTypeName(rType.getTypeName());
                            itemRecord.setIsChange(Boolean.TRUE);
                            newPrintItemList.add(itemRecord);
                        });
                    }
                });
            });

            // 开启分类模版后，未配置分类的商品默认为“其他”分类
            List<PrintItemRecord> otherList = printItemRecords.stream()
                    .filter(p -> Boolean.FALSE.equals(p.getIsChange()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(otherList)) {
                otherList.forEach(other -> {
                    other.setItemTypeGuid("0");
                    other.setItemTypeName("其他");
                    newPrintItemList.add(other);
                });
            }
            log.info("[TypeChange]处理之后商品数据={}", JacksonUtils.writeValueAsString(newPrintItemList));
            printBaseItemDTO.setItemRecordList(newPrintItemList);
        } catch (Exception e) {
            log.error("打印分类模版处理失败：{}", e.getMessage());
        }
    }

    private void getNewPrintSortResponse(PrintDTO printDTO, List<PrintItemRecord> newPrintItemRecords, PrintBaseItemDTO printBaseItemDTO, List<PrintItemRecord> printItemRecords) {
        ItemStringListDTO itemTypeDTO = new ItemStringListDTO();
        itemTypeDTO.setStoreGuid(printDTO.getStoreGuid());
        itemTypeDTO.setDataList(printItemRecords.stream().map(PrintItemRecord::getItemGuid).collect(Collectors.toList()));
        List<ItemInfoRespDTO> printSortResponse = itemClientService.listItemInfoBySalesModelNew(itemTypeDTO);
        log.info("远程调用printSortResponse{}", JacksonUtils.writeValueAsString(printSortResponse));

        if (!CollectionUtils.isEmpty(printSortResponse)) {

            Map<String, List<PrintItemRecord>> printItemRecordMap = printItemRecords
                    .stream().collect(Collectors.groupingBy(PrintItemRecord::getItemGuid));

            List<String> itemTypeGuid = Lists.newArrayList();
            Set<String> itemTypeSetGuid = printSortResponse.stream()
                    .map(ItemInfoRespDTO::getTypeGuid).collect(Collectors.toSet());


            //分类排序
            printSortResponse = printSortResponse.stream()
                    .sorted(Comparator.comparing(ItemInfoRespDTO::getTypeSort)).collect(Collectors.toList());
            forPrintSortResponse(printSortResponse, itemTypeGuid, itemTypeSetGuid);

            Map<String, List<ItemInfoRespDTO>> itemInfoMap = printSortResponse
                    .stream()
                    .collect(Collectors.groupingBy(ItemInfoRespDTO::getTypeGuid));


            forItemTypeGuid(newPrintItemRecords, printItemRecordMap, itemTypeGuid, itemInfoMap);
            List<String> orderItemGuidList = newPrintItemRecords.stream().map(PrintItemRecord::getOrderItemGuid).collect(Collectors.toList());
            List<PrintItemRecord> differenceItemList = printItemRecords.stream().filter(e -> !orderItemGuidList.contains(e.getOrderItemGuid())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(differenceItemList)) {
                log.info("当前没有匹配到商品分类的明细:{}", JacksonUtils.writeValueAsString(differenceItemList));
                newPrintItemRecords.addAll(differenceItemList);
            }
            log.info("排序之后newPrintItemRecords数据{}", JacksonUtils.writeValueAsString(newPrintItemRecords));
            printBaseItemDTO.setItemRecordList(newPrintItemRecords);
            log.info("排序之后itemRecordList数据{}", JacksonUtils.writeValueAsString(printBaseItemDTO));
            log.info("排序之后printDTO数据{}", JacksonUtils.writeValueAsString(printDTO));
        }
    }

    private static void forItemTypeGuid(List<PrintItemRecord> newPrintItemRecords, Map<String, List<PrintItemRecord>> printItemRecordMap, List<String> itemTypeGuid, Map<String, List<ItemInfoRespDTO>> itemInfoMap) {
        for (String typeGuid : itemTypeGuid) {

            List<ItemInfoRespDTO> itemInfoRespDTOS = itemInfoMap.get(typeGuid);
            itemInfoRespDTOS = itemInfoRespDTOS.stream()
                    .sorted(Comparator.comparing(ItemInfoRespDTO::getSort)).collect(Collectors.toList());

            for (ItemInfoRespDTO itemInfoRespDTO : itemInfoRespDTOS) {
                List<PrintItemRecord> printItemRecord = printItemRecordMap.get(itemInfoRespDTO.getItemGuid());
                if (!CollectionUtils.isEmpty(printItemRecord)) {
                    newPrintItemRecords.addAll(printItemRecord);
                }
            }
        }
    }

    private static void forPrintSortResponse(List<ItemInfoRespDTO> printSortResponse, List<String> itemTypeGuid, Set<String> itemTypeSetGuid) {
        for (ItemInfoRespDTO printItemRecord : printSortResponse) {
            if (itemTypeGuid.size() == itemTypeSetGuid.size()) {
                break;
            }
            if (CollectionUtils.isEmpty(itemTypeGuid)) {
                itemTypeGuid.add(printItemRecord.getTypeGuid());
                continue;
            }
            if (!itemTypeGuid.contains(printItemRecord.getTypeGuid())) {
                itemTypeGuid.add(printItemRecord.getTypeGuid());
            }
        }
    }

    private static PrintRecordReadDO mockPrintRecordReadDO(PrintRecordDO printRecordDO, PrinterReadDO printerReadDO) {
        PrintRecordReadDO printRecordReadDO = new PrintRecordReadDO();
        printRecordReadDO.setRecordUid(printRecordDO.getRecordUid());
        printRecordReadDO.setRecordGuid(printRecordDO.getRecordGuid());
        printRecordReadDO.setStoreGuid(printRecordDO.getStoreGuid());
        printRecordReadDO.setInvoiceType(printRecordDO.getInvoiceType());
        printRecordReadDO.setPrintContent(printRecordDO.getPrintContent());
        PrinterDO printerDO = getPrinterDO(printerReadDO);
        printRecordReadDO.setPrinterDO(printerDO);
        return printRecordReadDO;
    }

    private static PrinterDO getPrinterDO(PrinterReadDO printerReadDO) {
        PrinterDO printerDO = new PrinterDO();
        printerDO.setPrintPage(printerReadDO.getPrintPage());
        printerDO.setPrintCount(printerReadDO.getPrintCount());
        printerDO.setBusinessType(printerReadDO.getBusinessType());
        printerDO.setPrinterType(printerReadDO.getPrinterType());
        printerDO.setPrinterIp(printerReadDO.getPrinterIp());
        printerDO.setPrinterPort(printerReadDO.getPrinterPort());
        printerDO.setDeviceNo(printerReadDO.getDeviceNo());
        printerDO.setDeviceKey(printerReadDO.getDeviceKey());
        return printerDO;
    }
}
