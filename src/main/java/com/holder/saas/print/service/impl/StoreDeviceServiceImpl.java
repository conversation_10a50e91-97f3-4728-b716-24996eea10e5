package com.holder.saas.print.service.impl;

import com.holder.saas.print.service.StoreDeviceService;
import com.holder.saas.print.service.feign.StoreDeviceFeignService;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Slf4j
@Service
public class StoreDeviceServiceImpl implements StoreDeviceService {

    private final StoreDeviceFeignService storeDeviceFeignService;

    @Autowired
    public StoreDeviceServiceImpl(StoreDeviceFeignService storeDeviceFeignService) {
        this.storeDeviceFeignService = storeDeviceFeignService;
    }

    @Override
    public String findMasterDevice(String storeGuid) {
        StoreDeviceDTO storeDeviceDTO = storeDeviceFeignService.getMasterDeviceByStoreGuid(storeGuid);
        log.info("从门店：{}获取到Master一体机，storeDeviceDTO: {}",
                storeGuid, JacksonUtils.writeValueAsString(storeDeviceDTO));
        if (storeDeviceDTO == null
                || !StringUtils.hasText(storeDeviceDTO.getDeviceGuid())) {
            log.warn("该门店下无主机一体机");
            return null;
        }
        return storeDeviceDTO.getDeviceGuid();
    }

    @Override
    public boolean isMasterDevice(String storeGuid, String deviceId) {
        String masterDevice = findMasterDevice(storeGuid);
        if (!StringUtils.hasText(masterDevice)) {
            return false;
        }
        return masterDevice.equalsIgnoreCase(deviceId);
    }
}
