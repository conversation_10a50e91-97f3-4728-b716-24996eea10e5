package com.holder.saas.print.service;

import com.holderzone.saas.store.dto.print.PrinterDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterLogService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface PrinterLogService {

    void publishAddLog(Object source, PrinterDTO printerDTO);

    void publishDeleteLog(Object source, PrinterDTO printerDTO);

    void publishUpdateLog(Object source, PrinterDTO printerDtoBefore, PrinterDTO printerDtoAfter);
}
