package com.holder.saas.print.service.impl;

import com.holder.saas.print.entity.PrintData;
import com.holder.saas.print.entity.ability.PrintTemplate3;
import com.holder.saas.print.entity.domain.PrinterDO;
import com.holder.saas.print.entity.enums.PrintPageEnum;
import com.holder.saas.print.entity.read.PrintRecordReadDO;
import com.holder.saas.print.mapstruct.PrintRecordMapstruct;
import com.holder.saas.print.service.PrintRecordRespService;
import com.holder.saas.print.template.base.PrintTemplate;
import com.holder.saas.print.template.factory.PrintTemplateFactory;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.WhitelistUtils;
import com.holder.saas.print.utils.template.TradeModeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.print.PrintOrderDTO;
import com.holderzone.saas.store.dto.print.PrintRecordDTO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.content.*;
import com.holderzone.saas.store.dto.print.content.retail.PrintRetailCheckOutDTO;
import com.holderzone.saas.store.dto.print.content.retail.PrintRetailHandOverDTO;
import com.holderzone.saas.store.dto.print.content.retail.PrintRetailOpStatsDTO;
import com.holderzone.saas.store.dto.print.deprecate.PrintContentDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.enums.print.BusinessTypeEnum;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.enums.print.PrinterTypeEnum;
import com.holderzone.saas.store.enums.print.TradeModeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintRecordRespServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印单、打印记录响应值处理实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class PrintRecordRespServiceImpl implements PrintRecordRespService {

    private final PrintRecordMapstruct printRecordMapstruct;

    private final PrintTemplateFactory printTemplateFactory;

    @Autowired
    public PrintRecordRespServiceImpl(PrintRecordMapstruct printRecordMapstruct,
                                      PrintTemplateFactory printTemplateFactory) {
        this.printRecordMapstruct = printRecordMapstruct;
        this.printTemplateFactory = printTemplateFactory;
    }

    @Override
    public PrintRecordDTO do2Dto(PrintRecordReadDO printRecordReadDO) {
        InvoiceTypeEnum printBusiness = InvoiceTypeEnum.ofType(printRecordReadDO.getInvoiceType());
        switch (printBusiness) {
            case ITEM_LIST: {
                PrintItemDetailDTO itemListDTO = JacksonUtils.toObject(PrintItemDetailDTO.class, printRecordReadDO.getPrintContent());
                String markName = TradeModeUtils.getMarkNameWithoutColon(TradeModeEnum.DINE.getMode());
                return handlerRecordDtoList(markName, String.valueOf(itemListDTO.getMarkNo()), itemListDTO, printRecordReadDO);
            }
            case PRE_CHECKOUT: {
                PrintPreCheckoutDTO printPreCheckoutDTO = JacksonUtils.toObject(PrintPreCheckoutDTO.class, printRecordReadDO.getPrintContent());
                String markName = TradeModeUtils.getMarkNameWithoutColon(TradeModeEnum.DINE.getMode());
                return handlerRecordDtoList(markName, String.valueOf(printPreCheckoutDTO.getMarkNo()), printPreCheckoutDTO, printRecordReadDO);
            }
            case PRE_CHECKOUT_TABLES: {
                PrintPreCoTableCbDTO printPreCoTableCbDTO = JacksonUtils.toObject(PrintPreCoTableCbDTO.class, printRecordReadDO.getPrintContent());
                PrintPreCoTableCbDTO.PrintTableDTO printTableDTO = printPreCoTableCbDTO.getTableOrderList().get(0);
                String markName = TradeModeUtils.getMarkNameWithoutColon(TradeModeEnum.DINE.getMode());
                return handlerRecordDtoList(markName, String.valueOf(printTableDTO.getMarkNo()), printPreCoTableCbDTO, printRecordReadDO);
            }
            case CHECKOUT: {
                PrintCheckOutDTO checkOutDto = JacksonUtils.toObject(PrintCheckOutDTO.class, printRecordReadDO.getPrintContent());
                String markName = TradeModeUtils.getMarkNameWithoutColon(checkOutDto.getTradeMode());
                return handlerRecordDtoList(markName, String.valueOf(checkOutDto.getMarkNo()), checkOutDto, printRecordReadDO);
            }
            case CHECKOUT_TABLES: {
                PrintCoTableCbDTO checkOutDto = JacksonUtils.toObject(PrintCoTableCbDTO.class, printRecordReadDO.getPrintContent());
                PrintCoTableCbDTO.PrintTableDTO printTableDTO = checkOutDto.getTableOrderList().get(0);
                String markName = TradeModeUtils.getMarkNameWithoutColon(TradeModeEnum.DINE.getMode());
                return handlerRecordDtoList(markName, String.valueOf(printTableDTO.getMarkNo()), checkOutDto, printRecordReadDO);
            }
            case STORED_CASH: {
                PrintStoredCashDTO printStoredCashDto = JacksonUtils.toObject(PrintStoredCashDTO.class, printRecordReadDO.getPrintContent());
                return handlerRecordDtoList(MultiLangUtils.get("stored_value_invoice"), String.valueOf(printStoredCashDto.getSerialNumber()), printStoredCashDto, printRecordReadDO);
            }
            case QUEUE: {
                PrintQueueDTO printQueueDTO = JacksonUtils.toObject(PrintQueueDTO.class, printRecordReadDO.getPrintContent());
                return handlerRecordDtoList(MultiLangUtils.get("queue_invoice"), String.valueOf(printQueueDTO.getQueueNumber()), printQueueDTO, printRecordReadDO);
            }
            case TURN_TABLE: {
                PrintTurnTableDTO printTurnTableDTO = JacksonUtils.toObject(PrintTurnTableDTO.class, printRecordReadDO.getPrintContent());
                return handlerRecordDtoList(MultiLangUtils.get("turn_table_invoice"), "", printTurnTableDTO, printRecordReadDO);
            }
            case TAKEOUT: {
                PrintTakeoutDTO takeoutDto = JacksonUtils.toObject(PrintTakeoutDTO.class, printRecordReadDO.getPrintContent());
                return handlerRecordDtoList(takeoutDto.getPlatform(), String.valueOf(takeoutDto.getPlatformOrder()), takeoutDto, printRecordReadDO);
            }
            case HANDOVER: {
                PrintHandOverDTO printHandOverDTO = JacksonUtils.toObject(PrintHandOverDTO.class, printRecordReadDO.getPrintContent());
                return handlerRecordDtoList(MultiLangUtils.get("turnover_invoice"), "", printHandOverDTO, printRecordReadDO);
            }
            case OP_STATS: {
                PrintOpStatsDTO printOpStatsDTO = JacksonUtils.toObject(PrintOpStatsDTO.class, printRecordReadDO.getPrintContent());
                return handlerRecordDtoList(MultiLangUtils.get("op_stats_invoice"), "", printOpStatsDTO, printRecordReadDO);
            }
            case RECEIPT_STATS: {
                PrintReceiptStatsDTO printReceiptStatsDTO = JacksonUtils.toObject(PrintReceiptStatsDTO.class, printRecordReadDO.getPrintContent());
                return handlerRecordDtoList(MultiLangUtils.get("receipt_stats_invoice"), "", printReceiptStatsDTO, printRecordReadDO);
            }
            case MEM_STATS: {
                PrintMemStatsDTO printMemStatsDTO = JacksonUtils.toObject(PrintMemStatsDTO.class, printRecordReadDO.getPrintContent());
                return handlerRecordDtoList(MultiLangUtils.get("mem_stats_invoice"), "", printMemStatsDTO, printRecordReadDO);
            }
            case TRADE_STATS: {
                PrintTradeStatsDTO printTradeStatsDTO = JacksonUtils.toObject(PrintTradeStatsDTO.class, printRecordReadDO.getPrintContent());
                return handlerRecordDtoList(MultiLangUtils.get("trade_stats_invoice"), "", printTradeStatsDTO, printRecordReadDO);
            }
            case TYPE_STATS: {
                PrintTypeStatsDTO printTypeStatsDTO = JacksonUtils.toObject(PrintTypeStatsDTO.class, printRecordReadDO.getPrintContent());
                return handlerRecordDtoList(MultiLangUtils.get("type_stats_invoice"), "", printTypeStatsDTO, printRecordReadDO);
            }
            case ITEM_STATS: {
                PrintItemStatsDTO printItemStatsDTO = JacksonUtils.toObject(PrintItemStatsDTO.class, printRecordReadDO.getPrintContent());
                return handlerRecordDtoList(MultiLangUtils.get("item_stats_invoice"), "", printItemStatsDTO, printRecordReadDO);
            }
            case PROP_STATS: {
                PrintPropStatsDTO printPropStatsDTO = JacksonUtils.toObject(PrintPropStatsDTO.class, printRecordReadDO.getPrintContent());
                return handlerRecordDtoList(MultiLangUtils.get("prop_stats_invoice"), "", printPropStatsDTO, printRecordReadDO);
            }
            case ITEM_REFUND_STATS: {
                PrintItemStatsDTO printItemStatsDTO = JacksonUtils.toObject(PrintItemStatsDTO.class, printRecordReadDO.getPrintContent());
                return handlerRecordDtoList(MultiLangUtils.get("item_refund_stats_invoice"), "", printItemStatsDTO, printRecordReadDO);
            }
            case ITEM_GIFT_STATS: {
                PrintItemStatsDTO printItemStatsDTO = JacksonUtils.toObject(PrintItemStatsDTO.class, printRecordReadDO.getPrintContent());
                return handlerRecordDtoList(MultiLangUtils.get("item_gift__stats_invoice"), "", printItemStatsDTO, printRecordReadDO);
            }
            case ORDER_ITEM: {
                PrintOrderItemDTO printOrderItemDTODto = JacksonUtils.toObject(PrintOrderItemDTO.class, printRecordReadDO.getPrintContent());
                String markName = TradeModeUtils.getMarkName(printOrderItemDTODto.getTradeMode(), printOrderItemDTODto.getMarkName());
                return handlerRecordDtoList(markName, String.valueOf(printOrderItemDTODto.getMarkNo()), printOrderItemDTODto, printRecordReadDO);
            }
            case REFUND_ITEM: {
                PrintRefundItemDTO refundItemDto = JacksonUtils.toObject(PrintRefundItemDTO.class, printRecordReadDO.getPrintContent());
                String markName = TradeModeUtils.getMarkName(refundItemDto.getTradeMode(), refundItemDto.getMarkName());
                return handlerRecordDtoList(markName, String.valueOf(refundItemDto.getMarkNo()), refundItemDto, printRecordReadDO);
            }
            case TURN_TABLE_ITEM: {
                PrintTurnTableDTO checkOutDto = JacksonUtils.toObject(PrintTurnTableDTO.class, printRecordReadDO.getPrintContent());
                String markName = TradeModeUtils.getMarkNameWithoutColon(TradeModeEnum.DINE.getMode());
                return handlerRecordDtoList(markName, "", checkOutDto, printRecordReadDO);
            }
            case LABEL: {
                PrintLabelDTO printLabelDTODto = JacksonUtils.toObject(PrintLabelDTO.class, printRecordReadDO.getPrintContent());
                return handlerRecordDtoList(MultiLangUtils.get("label_invoice"), String.valueOf(printLabelDTODto.getCurrentNo()), printLabelDTODto, printRecordReadDO);
            }
            case RETAIL_CHECKOUT: {
                PrintRetailCheckOutDTO printRetailCheckOutDTO = JacksonUtils.toObject(PrintRetailCheckOutDTO.class, printRecordReadDO.getPrintContent());
                return handlerRecordDtoList(InvoiceTypeEnum.RETAIL_CHECKOUT.getTitle(), "", printRetailCheckOutDTO, printRecordReadDO);
            }
            case RETAIL_HANDOVER: {
                PrintRetailHandOverDTO printRetailHandOverDTO = JacksonUtils.toObject(PrintRetailHandOverDTO.class, printRecordReadDO.getPrintContent());
                PrintRecordDTO printRecordDTO = handlerRecordDtoList(InvoiceTypeEnum.RETAIL_HANDOVER.getTitle(), "", printRetailHandOverDTO, printRecordReadDO);
                printRecordDTO.setRecordUid(InvoiceTypeEnum.RETAIL_HANDOVER.getTitle());
                return printRecordDTO;
            }
            case RETAIL_OP_STATS: {
                PrintRetailOpStatsDTO printOpStatsDTO = JacksonUtils.toObject(PrintRetailOpStatsDTO.class, printRecordReadDO.getPrintContent());
                PrintRecordDTO printRecordDTO = handlerRecordDtoList(MultiLangUtils.get("op_stats_invoice"), "", printOpStatsDTO, printRecordReadDO);
                printRecordDTO.setRecordUid(MultiLangUtils.get("op_stats_invoice"));
                return printRecordDTO;
            }
            case RETAIL_TYPE_STATS: {
                PrintTypeStatsDTO printTypeStatsDTO = JacksonUtils.toObject(PrintTypeStatsDTO.class, printRecordReadDO.getPrintContent());
                PrintRecordDTO printRecordDTO = handlerRecordDtoList(InvoiceTypeEnum.RETAIL_TYPE_STATS.getTitle(), "", printTypeStatsDTO, printRecordReadDO);
                printRecordDTO.setRecordUid(InvoiceTypeEnum.RETAIL_TYPE_STATS.getTitle());
                return printRecordDTO;
            }
            case RETAIL_ITEM_STATS: {
                PrintItemStatsDTO printItemStatsDTO = JacksonUtils.toObject(PrintItemStatsDTO.class, printRecordReadDO.getPrintContent());
                PrintRecordDTO printRecordDTO = handlerRecordDtoList(InvoiceTypeEnum.RETAIL_ITEM_STATS.getTitle(), "", printItemStatsDTO, printRecordReadDO);
                printRecordDTO.setRecordUid(InvoiceTypeEnum.RETAIL_ITEM_STATS.getTitle());
                return printRecordDTO;
            }
            case RETAIL_ITEM_REFUND_STATS: {
                PrintItemStatsDTO printItemStatsDTO = JacksonUtils.toObject(PrintItemStatsDTO.class, printRecordReadDO.getPrintContent());
                PrintRecordDTO printRecordDTO = handlerRecordDtoList(InvoiceTypeEnum.RETAIL_ITEM_REFUND_STATS.getTitle(), "", printItemStatsDTO, printRecordReadDO);
                printRecordDTO.setRecordUid(InvoiceTypeEnum.RETAIL_ITEM_REFUND_STATS.getTitle());
                return printRecordDTO;
            }
            case RETAIL_ITEM_GIFT_STATS: {
                PrintItemStatsDTO printItemStatsDTO = JacksonUtils.toObject(PrintItemStatsDTO.class, printRecordReadDO.getPrintContent());
                PrintRecordDTO printRecordDTO = handlerRecordDtoList(InvoiceTypeEnum.RETAIL_ITEM_GIFT_STATS.getTitle(), "", printItemStatsDTO, printRecordReadDO);
                printRecordDTO.setRecordUid(InvoiceTypeEnum.RETAIL_ITEM_GIFT_STATS.getTitle());
                return printRecordDTO;
            }
            case REFUND_INVOICE: {
                PrintRefundDTO printRefundDTO = JacksonUtils.toObject(PrintRefundDTO.class, printRecordReadDO.getPrintContent());
                PrintRecordDTO printRecordDTO = handlerRecordDtoList(printBusiness.getTitle(), "", printRefundDTO, printRecordReadDO);
                printRecordDTO.setRecordUid(printBusiness.getTitle());
                return handlerRecordDtoList(MultiLangUtils.get("refund_invoice"), "", printRefundDTO, printRecordReadDO);
            }
            default: {
                log.warn("请实现单据 invoiceType = {} 的 do2Dto 逻辑", printRecordReadDO.getInvoiceType());
                PrintDTO printDTO = JacksonUtils.toObject(PrintDTO.class, printRecordReadDO.getPrintContent());
                return handlerRecordDtoList(printBusiness.getTitle(), printBusiness.getTitle(), printDTO, printRecordReadDO);
            }
        }
    }

    @Value("${takeout.whitelist:}")
    private String takeoutWhitelist;

    @Value("${takeout.blacklist:}")
    private String takeoutBlacklist;

    @Override
    public PrintOrderDTO getOrderByPerDO(PrintRecordReadDO printRecordReadDO) {
        // 获取打印记录关联的打印机
        PrinterDO printerDO = getPrinterRelated(printRecordReadDO);
        int pageSize = PrintPageEnum.ofPageSize(printerDO.getPrintPage()).getPageWidth();
        Integer invoiceType = printRecordReadDO.getInvoiceType();
        String printContent = printRecordReadDO.getPrintContent();
        String storeGuid = printRecordReadDO.getStoreGuid();
        WhitelistUtils.setWhitelist(takeoutWhitelist, takeoutBlacklist);
        PrintTemplate<? extends PrintDTO, ? extends FormatDTO> printTemplate =
                printTemplateFactory.create(invoiceType, pageSize, printContent, storeGuid);
        List<PrintRow> printRows = printTemplate.getPrintRows();
        WhitelistUtils.clearWhitelist();

        return new PrintOrderDTO()
                .setPrintKey(printRecordReadDO.getRecordGuid())
                .setPageSize(pageSize)
                .setPrintTimes(printerDO.getPrintCount())
                .setBusinessType(printerDO.getBusinessType())
                .setPrinterType(printerDO.getPrinterType())
                .setPrinterIp(printerDO.getPrinterIp() != null ? printerDO.getPrinterIp() : "")
                .setPrinterPort(printerDO.getPrinterPort())
                .setPrintRows(printRows);
    }

    @Override
    public PrintOrderDTO getOrderByPerMock(int invoiceType, int pageSize, FormatDTO formatDTO, PrinterDTO printerDTO) {
        PrintTemplate<? extends PrintDTO, ? extends FormatDTO> printTemplate =
                printTemplateFactory.create(invoiceType, pageSize,
                        JacksonUtils.writeValueAsString(InvoiceTypeEnum.mockPrintBy(invoiceType)), formatDTO);

        return new PrintOrderDTO()
                .setPrintKey("mock_key")
                .setPageSize(pageSize)
                .setPrintTimes(printerDTO.getPrintCount())
                .setPrinterType(printerDTO.getPrinterType())
                .setPrinterIp(printerDTO.getPrinterIp() != null
                        ? printerDTO.getPrinterIp() : "")
                .setPrinterPort(printerDTO.getPrinterPort())
                .setPrintRows(printTemplate.getPrintRows());
    }

    private PrintRecordDTO handlerRecordDtoList(String markName, String markNo, PrintDTO printDTO, PrintRecordReadDO printRecordDo) {
        PrintRecordDTO printRecordDTO = printRecordMapstruct.mapRecordDTO(printRecordDo);
        PrinterDO printerDO = printRecordDo.getPrinterDO();
        PrinterTypeEnum printerType = PrinterTypeEnum.ofType(printerDO.getPrinterType());
        switch (printerType) {
            case WLAN_PRINTER:
                printRecordDTO.setPrinterIp(printerDO.getPrinterIp());
                break;
            case LOCAL_PRINTER:
                printRecordDTO.setPrinterIp("本机");
                break;
            case USB_PRINTER:
                printRecordDTO.setPrinterIp("USB");
                break;
        }
        printRecordDTO.setMarkName(markName);
        printRecordDTO.setMarkNo(markNo);
        printRecordDTO.setPrinterName("【"
                + BusinessTypeEnum.ofType(printerDO.getBusinessType()).getDesc()
                + "】" + printerDO.getPrinterName());
        printRecordDTO.setPrinterType(printerDO.getPrinterType());
        printRecordDTO.setPrintContent(printDTO);
        return printRecordDTO;
    }

    @Deprecated
    private PrintContentDTO getPrintContent(PrinterDO printerDO, PrintTemplate3 printTemplate3, PrintDTO printDto, String recordGuid) {
        List<PrintData> printDataList = new ArrayList<>();
        printDataList.add(printTemplate3.getHeader(printDto));
        int pageSize = PrintPageEnum.ofPageSize(printerDO.getPrintPage()).getPageWidth();
        printDataList.add(printTemplate3.getBody(printDto, pageSize));
        printDataList.add(printTemplate3.getFooter(printDto, pageSize));
        PrintContentDTO printContent = PrintContentDTO.builder()
                .enterpriseInfoGUID(printDto.getEnterpriseGuid())
                .storeGUID(printDto.getStoreGuid())
                .printKey(recordGuid)
                .printerTypeCode(printDto.getInvoiceType())
                .paper(printerDO.getPrintPage())
                .printTimes(printerDO.getPrintCount())
                .onLocal(Integer.valueOf(printerDO.getPrinterType()))
                .ip(printerDO.getPrinterIp() != null ? printerDO.getPrinterIp() : "")
                .port(String.valueOf(printerDO.getPrinterPort()))
                .arrayOfPrintRowE(printTemplate3.getAllPrintRow(printDataList))
                .build();
        return printContent;
    }

    private PrinterDO getPrinterRelated(PrintRecordReadDO printRecordReadDO) {
        if (null == printRecordReadDO.getPrinterDO()) {
            // POS机前台单据时返回虚拟前台打印机
            return new PrinterDO().setPrinterType(1)
                    .setPrintPage("58").setPrintCount(1);
        }
        return printRecordReadDO.getPrinterDO();
    }
}
