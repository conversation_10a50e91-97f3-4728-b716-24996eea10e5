package com.holder.saas.print.service;

import com.holder.saas.print.entity.read.PrintRecordReadDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ContentCacheService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface ContentCacheService {

    void save(List<PrintRecordReadDO> arrayOfPrintRecordReadDO);

    PrintRecordReadDO popSingle(String recordGuid);

    List<PrintRecordReadDO> popBatch(List<String> arrayOfRecordGuid);

    boolean hasMsgId(String msgId, String recordGuid, List<String> arrayOfRecordGuid);

    void saveMsgId(String msgId, String recordGuid, List<String> arrayOfRecordGuid);
}
