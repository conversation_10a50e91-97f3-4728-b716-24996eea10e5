package com.holder.saas.print.utils.template.calculator.item;

import com.holder.saas.print.utils.MultiLangUtils;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 80 纸显示：商品、单价、数量、小计
 */
public class Print80Price1Total1Calculator extends PrintItemCommonCalculator {

    public Print80Price1Total1Calculator(Font componentFont) {
        super(componentFont);
    }

    @Override
    public List<Text> getColumnHeaderList() {
        return Stream.of(MultiLangUtils.get("item"), MultiLangUtils.get("price"), MultiLangUtils.get("quantity"), MultiLangUtils.get("item_sum"))
                .map(this::convertToText)
                .collect(Collectors.toList());
    }

    @Override
    public List<Text> getColumnHeaderList(boolean isRefund) {
        return Stream.of(isRefund ? MultiLangUtils.get("item_refund") : MultiLangUtils.get("item"),
                        MultiLangUtils.get("price"),
                        isRefund ? MultiLangUtils.get("quantity_refund") : MultiLangUtils.get("quantity"),
                        MultiLangUtils.get("item_sum"))
                .map(this::convertToText)
                .collect(Collectors.toList());
    }

    @Override
    public List<Integer> getColumnWidthList() {
        return Arrays.asList(24, 8, 6, 10);
    }

    @Override
    public List<Integer> getColumnWidthList(boolean isRefund) {
        // 退款商品表头字数要多一些，重新配置宽度
        return isRefund ? Arrays.asList(22, 8, 8, 10) : getColumnWidthList();
    }

    @Override
    public List<Boolean> getAlignRights() {
        return Arrays.asList(false, false, false, true);
    }

    @Override
    public List<Text> getItem(String item, String price, String quantity, String subTotal) {
        return Stream.of(item, price, quantity, subTotal).map(this::convertToText).collect(Collectors.toList());
    }

    @Override
    public List<Text> getItemSingle(String item) {
        return Stream.of(item).map(this::convertToText).collect(Collectors.toList());
    }

    @Override
    public List<Text> getSubItem(String subItemName, String subItemNumber) {
        return Stream.of(subItemName, "s", subItemNumber, "s").map(this::convertToText).collect(Collectors.toList());
    }

    @Override
    public List<Text> getTotal(String numTotal, String moneyTotal, Font font) {
        return Stream.of(MultiLangUtils.get("item_price_total"), "s", Optional.ofNullable(numTotal).orElse("s"), moneyTotal)
                .map(s -> new Text(s, font)).collect(Collectors.toList());
    }
}
