package com.holder.saas.print.utils;

import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;
import java.util.ResourceBundle;

/**
 * 国际化工具类
 */
@Component
public class MultiLangUtils {

    /**
     * 获取单个国际化翻译值
     */
    public static String get(String msgKey) {
        try {
            ResourceBundle bundle = ResourceBundle.getBundle("i18n/messages", LocaleContextHolder.getLocale());
            return bundle.getString(msgKey);
        } catch (Exception e) {
            return msgKey;
        }
    }

    /**
     *
     */
    public static String getByOrderItemType(int type){
        switch (type){
            case 1:
                return get("call_for_order_invoice_header");
            case 2:
                return get("expedite_order_invoice_header");
            case 3:
                return get("suspended_order_invoice_header");
            default:
                return get("order_item_invoice_header");
        }
    }
}
