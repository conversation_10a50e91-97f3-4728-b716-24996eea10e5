package com.holder.saas.print.utils.valid;

import com.holderzone.framework.exception.unchecked.ParameterException;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.ValidatorFactory;
import java.util.Set;

public final class HibernateValidUtils {

    public static <T> void validate(T obj) {
        ValidatorFactory vf = Validation.buildDefaultValidatorFactory();
        javax.validation.Validator validator = vf.getValidator();
        Set<ConstraintViolation<T>> set = validator.validate(obj);
        StringBuilder msg = new StringBuilder();
        for (ConstraintViolation<T> constraintViolation : set) {
            msg.append(constraintViolation.getMessage()).append(";");
        }
        if (!set.isEmpty()) {
            //抛出参数校验异常
            String message = msg.toString();
            throw new ParameterException(message.endsWith(";") ? message.substring(0, message.length() - 1) : message);
        }
    }
}
