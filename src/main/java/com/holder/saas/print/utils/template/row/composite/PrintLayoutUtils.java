/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:PrintModuleUtils.java
 * Date:2019-12-4
 * Author:terry
 */

package com.holder.saas.print.utils.template.row.composite;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.composite.table.TableRowContext;
import com.holder.saas.print.utils.template.calculator.stats.PrintStatsCalculator;
import com.holder.saas.print.utils.template.calculator.stats.PrintStatsCalculatorFactory;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.print.content.nested.AdditionalCharge;
import com.holderzone.saas.store.dto.print.content.nested.InOutRecord;
import com.holderzone.saas.store.dto.print.content.nested.ReduceRecord;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

public final class PrintLayoutUtils {

    /**
     * 优惠金额
     *
     * @param reduceRecordList
     * @param printRows
     */
    @Deprecated
    public static void addReduceRecord(List<ReduceRecord> reduceRecordList, List<PrintRow> printRows) {
        if (!CollectionUtils.isEmpty(reduceRecordList)) {
            BigDecimal reduceSum = reduceRecordList.stream()
                    .map(ReduceRecord::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);
            if (reduceSum.compareTo(BigDecimal.ZERO) != 0) {
                PrintRowUtils.add(printRows, new Separator("优惠金额：" + BigDecimalUtils.moneyTrimmedString(reduceSum)));
                reduceRecordList.stream()
                        .filter(reduce -> reduce.getAmount() != null
                                && reduce.getAmount().compareTo(BigDecimal.ZERO) != 0)
                        .forEach(reduce -> PrintRowUtils.add(printRows, new KeyValue()
                                .setKeyString(reduce.getName())
                                .setValueString(BigDecimalUtils.moneyTrimmedString(reduce.getAmount()))));
            }
        }
    }

    /**
     * 附加费
     *
     * @param additionalChargeList
     * @param printRows
     */
    @Deprecated
    public static void addAdditionalCharge(List<AdditionalCharge> additionalChargeList, List<PrintRow> printRows) {
        if (!CollectionUtils.isEmpty(additionalChargeList)) {
            BigDecimal chargeValueSum = additionalChargeList.stream()
                    .map(AdditionalCharge::getChargeValue)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);
            if (chargeValueSum.compareTo(BigDecimal.ZERO) > 0) {
                PrintRowUtils.add(printRows, new Separator("附加费：" + BigDecimalUtils.moneyTrimmedString(chargeValueSum)));
                additionalChargeList.stream()
                        .filter(additionalCharge -> additionalCharge.getChargeValue() != null
                                && additionalCharge.getChargeValue().compareTo(BigDecimal.ZERO) > 0)
                        .forEach(additionalCharge -> PrintRowUtils.add(printRows, new KeyValue()
                                .setKeyString(additionalCharge.getChargeName())
                                .setValueString(BigDecimalUtils.moneyTrimmedString(additionalCharge.getChargeValue()))));
            }
        }
    }

    /**
     * 下单时间，结账单打印时间
     *
     * @param pageSize
     * @param orderTime
     * @param createTime
     * @param printRows
     */
    @Deprecated
    public static void addOrderTimeAndPrintTime(int pageSize, long orderTime, long createTime, List<PrintRow> printRows) {
        if (80 == pageSize) {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString("下单：" + DateTimeUtils.mills2String(orderTime, "MM-dd HH:mm"))
                    .setValueString(MultiLangUtils.get(Constant.PRINT_TIME) + DateTimeUtils.mills2String(createTime, "MM-dd HH:mm")));
        } else {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKeyString("下单：")
                    .setValueString(DateTimeUtils.mills2String(orderTime, "MM-dd HH:mm")));
            PrintRowUtils.add(printRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKeyString(MultiLangUtils.get(Constant.PRINT_TIME))
                    .setValueString(DateTimeUtils.mills2String(createTime, "MM-dd HH:mm")));
        }
    }

    /**
     * 结账操作员，结账单打印时间
     *
     * @param pageSize
     * @param operatorStaffName
     * @param createTime
     * @param printRows
     */
    public static void addOpStaffAndPrintTime(int pageSize, String operatorStaffName, long createTime, List<PrintRow> printRows) {
        if (80 == pageSize) {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(MultiLangUtils.get(Constant.OPERATOR) + operatorStaffName)
                    .setValueString(MultiLangUtils.get(Constant.PRINT_TIME) + DateTimeUtils.mills2String(createTime, "yyyy-MM-dd HH:mm")));
        } else {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKeyString(MultiLangUtils.get(Constant.OPERATOR))
                    .setValueString(operatorStaffName));
            PrintRowUtils.add(printRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKeyString(MultiLangUtils.get(Constant.PRINT_TIME))
                    .setValueString(DateTimeUtils.mills2String(createTime, "yyyy-MM-dd HH:mm")));
        }
    }

    public static void addStats(List<InOutRecord> inOutRecordList, int pageSize, List<PrintRow> printRows) {
        if (!CollectionUtils.isEmpty(inOutRecordList)) {
            PrintStatsCalculator calculator = PrintStatsCalculatorFactory.create(pageSize);
            List<Boolean> alignRights = calculator.getAlignRights();
            List<Integer> columnWidthList = calculator.getColumnWidthList();
            boolean lineFeedInnerCell = calculator.getLineFeedInnerCell();
            List<Text> columnHeaderList = calculator.getColumnHeaderList();
            TableRowContext context = new TableRowContext(printRows, columnWidthList, alignRights, lineFeedInnerCell);
            context.addSeparator();
            context.addHeaders(columnHeaderList);
            context.addSeparator();
            for (InOutRecord inOutRecord : inOutRecordList) {
                context.addRow(calculator.getColumnText(
                        inOutRecord.getName(),
                        String.valueOf(inOutRecord.getNumber()),
                        BigDecimalUtils.moneyTrimmedString(inOutRecord.getMoney())
                ));
            }
            context.addSeparator();
        }
    }
}
