package com.holder.saas.print.utils.valid;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.print.PrintRecordReqDTO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import org.springframework.util.CollectionUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.ValidatorFactory;
import java.util.Objects;
import java.util.Set;

public class RecordValidUtils {

    public static void createPrintTaskValidate(PrintDTO printDto) {
        // 菜品复单不校验
        if (Objects.equals(InvoiceTypeEnum.ITEM_REPEAT_ORDER.getType(), printDto.getInvoiceType())) {
            return;
        }
        ValidatorFactory vf = Validation.buildDefaultValidatorFactory();
        javax.validation.Validator validator = vf.getValidator();
        Set<ConstraintViolation<PrintDTO>> set = validator.validate(printDto);
        StringBuilder msg = new StringBuilder();
        for (ConstraintViolation<PrintDTO> constraintViolation : set) {
            msg.append(constraintViolation.getMessage()).append(";");
        }
        if (!set.isEmpty()) {
            //抛出参数校验异常
            String message = msg.toString();
            throw new ParameterException(message.endsWith(";") ? message.substring(0, message.length() - 1) : message);
        }
    }

    public static void createGetContentValidate(PrintRecordReqDTO printRecordReqDTO) {
        if (!StringUtils.hasText(printRecordReqDTO.getRecordGuid())
                && CollectionUtils.isEmpty(printRecordReqDTO.getArrayOfRecordGuid())) {
            throw new ParameterException("recordGuid不得为空");
        }
    }
}
