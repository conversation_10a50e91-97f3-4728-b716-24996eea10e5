/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:PreciousUtils.java
 * Date:2020-1-31
 * Author:terry
 */

package com.holder.saas.print.utils;

import com.holderzone.framework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2020-01-31 下午8:13
 */
public class WhitelistUtils {

    private static final ThreadLocal<String> WHITELIST = new ThreadLocal<>();

    private static final ThreadLocal<String> BLACKLIST = new ThreadLocal<>();

    public static void setWhitelist(String whitelist, String blacklist) {
        WHITELIST.set(whitelist);
        BLACKLIST.set(blacklist);
    }

    public static boolean isWhitelist(String value) {
        String whitelist = WHITELIST.get();
        if (!StringUtils.hasText(whitelist)) {
            return false;
        }
        boolean isWhitelist = whitelist.contains("all") || whitelist.contains(value);
        if (!isWhitelist) {
            return false;
        }
        String blacklist = BLACKLIST.get();
        if (!StringUtils.hasText(blacklist)) {
            return true;
        }
        return !(blacklist.contains("all") || blacklist.contains(value));
    }

    public static void clearWhitelist() {
        WHITELIST.remove();
        BLACKLIST.remove();
    }
}
