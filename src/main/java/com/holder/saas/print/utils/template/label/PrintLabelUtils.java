/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:PrintLabelUtils.java
 * Date:2019-12-4
 * Author:terry
 */

package com.holder.saas.print.utils.template.label;

import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.CoordinateRow;
import com.holderzone.saas.store.dto.print.template.printable.LabelBarCode;
import com.holderzone.saas.store.dto.print.template.printable.Section;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintLabelUtils
 * @date 2018/02/14 09:00
 * @description 标签单据坐标计算工具类
 * @program holder-saas-store-print
 */
public final class PrintLabelUtils {

    public static void addKeyValueAsCoordinateRow(List<PrintRow> printRows, String key, String value, int pageSize, int xMultiple) {
        int[] keyPosition = new int[]{0, 0};
        int[] valuePosition = PrintLabelUtils.getPosition(key, value, pageSize, xMultiple);
        PrintRowUtils.add(printRows, new CoordinateRow()
                .addCoordinateText(key, keyPosition[0], keyPosition[1])
                .addCoordinateText(value, valuePosition[0], valuePosition[1]));
    }

    public static void addSeparatorAsSection(List<PrintRow> printRows, String text, int pageSize, int xMultiple) {
        int count = PrintCalcUtils.getCharCountOfPage(pageSize, xMultiple);
        StringBuilder separator = new StringBuilder();
        for (int i = 0; i < count; i++) separator.append(text);
        PrintRowUtils.add(printRows, new Section(separator.toString()));
    }

    public static void addSection(List<PrintRow> printRows, String text, int pageSize, int xMultiple) {
        PrintLabelUtils.getContent(text, pageSize, xMultiple)
                .forEach(content -> PrintRowUtils.add(printRows, new Section(content)));
    }

    public static void addSection(List<PrintRow> printRows, String text, Text.Align align, int pageSize, int xMultiple) {
        PrintLabelUtils.getContent(text, pageSize, xMultiple)
                .forEach(content -> PrintRowUtils.add(printRows, new Section(content, align)));
    }

    public static void addBarCode(List<PrintRow> printRows, String serialNumber) {
        PrintRowUtils.add(printRows, new LabelBarCode(serialNumber));
    }

    private static int[] getPosition(String key, String value, int pageSize, int xMultiple) {
        double widthOfKey = PrintCalcUtils.getWidthOfText(key, pageSize, xMultiple);
        double widthOfValue = PrintCalcUtils.getWidthOfText(value, pageSize, xMultiple);
        int peer;
        if (widthOfKey + widthOfValue > pageSize) {
            peer = (int) PrintCalcUtils.getPointOfWidth(widthOfKey);
        } else {
            peer = (int) PrintCalcUtils.getPointOfWidth(pageSize - widthOfValue);
        }
        return new int[]{peer, 0};
    }

    private static List<String> getContent(String text, int pageSize, int xMultiple) {
        List<String> result = new ArrayList<>();
        int textCount = PrintCalcUtils.getCharCountOfPage(pageSize, xMultiple);
        do {
            String textCutOff = PrintCalcUtils.getCutOffText(text, textCount);
            result.add(textCutOff);
            text = text.substring(textCutOff.length());
        } while (text.length() > 0);
        return result;
    }

}
