/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:PrintRowUtils.java
 * Date:2019-12-4
 * Author:terry
 */

package com.holder.saas.print.utils.template.row;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.*;
import com.holderzone.saas.store.enums.print.ContentTypeEnum;

import java.util.Collection;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintRowUtils
 * @date 2018/02/14 09:00
 * @description 打印ContentType辅助工具类
 * @program holder-saas-store-print
 */
public class PrintRowUtils {

    public static void add(Collection<PrintRow> printRows, BarCode barCode) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.BAR_CODE.getType()).setBarCode(barCode));
    }

    public static void add(Collection<PrintRow> printRows, LabelBarCode labelBarCode) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.LABEL_BAR_CODE.getType()).setLabelBarCode(labelBarCode));
    }

    public static void add(Collection<PrintRow> printRows, BlankRow blankRow) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.BLANK_ROW.getType()).setBlankRow(blankRow));
    }

    public static void add(Collection<PrintRow> printRows, CoordinateRow coordinateRow) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.COORDINATE_ROW.getType()).setCoordinateRow(coordinateRow));
    }

    public static void add(Collection<PrintRow> printRows, Image image) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.IMAGE.getType()).setImage(image));
    }

    public static void add(Collection<PrintRow> printRows, KeyValue keyValue) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.KEY_VALUE.getType()).setKeyValue(keyValue));
    }

    public static void add(Collection<PrintRow> printRows, Line line) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.LINE.getType()).setLine(line));
    }

    public static void add(Collection<PrintRow> printRows, QrCode qrCode) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.QR_CODE.getType()).setQrCode(qrCode));
    }

    public static void add(Collection<PrintRow> printRows, ReverseText reverseText) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.REVERSE_TEXT.getType()).setReverseText(reverseText));
    }

    public static void add(Collection<PrintRow> printRows, Section section) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.SECTION.getType()).setSection(section));
    }

    public static void add(Collection<PrintRow> printRows, Separator separator) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.SEPARATOR.getType()).setSeparator(separator));
    }

    public static void add(Collection<PrintRow> printRows, Table table) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.TABLE.getType()).setTable(table));
    }

    public static void add(Collection<PrintRow> printRows, TableRow tableRow) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.TABLE_ROW.getType()).setTableRow(tableRow));
    }

    public static void add(Collection<PrintRow> printRows, String key, String value, FormatMetadata formatMetadata) {
        add(printRows, new Section(MultiLangUtils.get(key) + value, formatMetadata.toFont()));
    }

    public static void addLeft(Collection<PrintRow> printRows, String key, String value, FormatMetadata formatMetadata) {
        add(printRows, new Section(MultiLangUtils.get(key) + value, formatMetadata.toFont(), Text.Align.Left));
    }

    public static void addRight(Collection<PrintRow> printRows, String key, String value, FormatMetadata formatMetadata) {
        add(printRows, new Section(MultiLangUtils.get(key) + value, formatMetadata.toFont(), Text.Align.Right));
    }

    public static void addCenter(Collection<PrintRow> printRows, String key, String value, FormatMetadata formatMetadata) {
        add(printRows, new Section(MultiLangUtils.get(key) + value, formatMetadata.toFont(), Text.Align.Center));
    }
}
