package com.holder.saas.print.utils.template.calculator.stats;

import com.holderzone.saas.store.dto.print.template.convertable.Text;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public interface PrintStatsCalculator {

    default List<Text> getColumnHeaderList() {
        return Stream.of("项目", "订单数", "金额").map(Text::new).collect(Collectors.toList());
    }

    List<Integer> getColumnWidthList();

    default List<Boolean> getAlignRights() {
        return Arrays.asList(false, false, true);
    }

    default List<Text> getColumnText(String name, String number, String money) {
        return Stream.of(name, number, money).map(Text::new).collect(Collectors.toList());
    }

    default boolean getLineFeedInnerCell() {
        return false;
    }
}
