package com.holder.saas.print.utils;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class MPPage<T> extends Page<T> {

    public MPPage() {
        super(1, 10);
    }

    public MPPage(long current, long size) {
        super(current, size);
    }

    public MPPage(long current, long size, long total) {
        super(current, size, total);
    }

}