/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:PrintCutUtils.java
 * Date:2019-12-4
 * Author:terry
 */

package com.holder.saas.print.utils.template.content;

import com.holder.saas.print.entity.enums.PrintCutEnum;
import com.holder.saas.print.entity.read.PrinterReadDO;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.DeepCloneUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.print.content.PrintBaseItemDTO;
import com.holderzone.saas.store.dto.print.content.PrintLabelDTO;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintCutUtils
 * @date 2018/02/14 09:00
 * @description 后厨商品切纸类型工具类
 * @program holder-saas-store-print
 */
@Slf4j
@SuppressWarnings("Duplicates")
public final class PrintCutUtils {

    public static List<List<PrintItemRecord>> splitItemByPrintCut(List<PrintItemRecord> printItemRecordsMatched,
                                                                  PrinterReadDO printerReadDO, PrintBaseItemDTO printBaseItemDTO) {
        PrintCutEnum printCutEnum = PrintCutEnum.ofCodeDefaultAll(printerReadDO.getPrintCut());
        switch (printCutEnum) {
            case ALL:
                return splitItemByAll(printItemRecordsMatched);
            case PER_ITEM:
                return splitItemByItem(printItemRecordsMatched);
            case PER_ITEM_TYPE:
                return splitItemByItemType(printItemRecordsMatched);
            case PER_ITEM_COUNT:
                return splitItemByCountOne(printItemRecordsMatched, printBaseItemDTO);
            default:
                throw new BusinessException("不支持的切纸类型：" + printerReadDO.getPrintCut());
        }
    }


    private static List<List<PrintItemRecord>> splitItemByAll(List<PrintItemRecord> printItemRecordsMatched) {
        return Collections.singletonList(printItemRecordsMatched);
    }

    private static List<List<PrintItemRecord>> splitItemByItem(List<PrintItemRecord> printItemRecordsMatched) {
        return splitItemByItemKey(printItemRecordsMatched, PrintItemRecord::getItemGuid);
    }

    private static List<List<PrintItemRecord>> splitItemByItemType(List<PrintItemRecord> printItemRecordsMatched) {
        return splitItemByItemKey(printItemRecordsMatched, PrintItemRecord::getItemTypeGuid);
    }

    /**
     * 对已经匹配上打印机的菜品，根据切纸类型，进行分组操作
     *
     * @param printItemRecordsMatched
     * @param itemKeyFunc
     * @return
     */
    private static List<List<PrintItemRecord>> splitItemByItemKey(List<PrintItemRecord> printItemRecordsMatched,
                                                                  Function<PrintItemRecord, String> itemKeyFunc) {
        // 非套餐
        List<PrintItemRecord> nonPkgPrintItemRecords = printItemRecordsMatched.stream()
                .filter(itemRecord -> CollectionUtils.isEmpty(itemRecord.getSubItemRecords()))
                .collect(Collectors.toList());
        // 套餐
        List<PrintItemRecord> pkgPrintItemRecords = printItemRecordsMatched.stream()
                .filter(itemRecord -> !CollectionUtils.isEmpty(itemRecord.getSubItemRecords()))
                .collect(Collectors.toList());

        // 非套餐分组
        Map<String, List<PrintItemRecord>> itemRecordsMapByItemKey = nonPkgPrintItemRecords.stream()
                .collect(Collectors.groupingBy(itemKeyFunc));

        // 套餐分组
        for (PrintItemRecord pkgPrintItemRecord : pkgPrintItemRecords) {

            // 套餐子菜分类结果集
            Map<String, List<PrintItemRecord>> itemRecordsMapByTypeOfCurPkgItem = new HashMap<>();

            // 处理套餐项子菜，将套餐里面的子菜根据分类key进行分类
            for (PrintItemRecord subPrintItemRecord : pkgPrintItemRecord.getSubItemRecords()) {
                String certainItemKey = itemKeyFunc.apply(subPrintItemRecord);

                // 根据分类Key获取该分类下的菜品集合
                List<PrintItemRecord> printItemRecordsOfCertainItemKey = itemRecordsMapByTypeOfCurPkgItem.get(certainItemKey);

                // 该分类key下的菜品集合为空，则新建一个该分类key对应的菜品集合
                if (CollectionUtils.isEmpty(printItemRecordsOfCertainItemKey)) {
                    printItemRecordsOfCertainItemKey = new ArrayList<>();
                    itemRecordsMapByTypeOfCurPkgItem.put(certainItemKey, printItemRecordsOfCertainItemKey);
                }

                // 该分类key下的菜品集合存在，则将菜品加入该分类集合即可
                printItemRecordsOfCertainItemKey.add(subPrintItemRecord);
            }

            // 处理套餐子菜分类结果集
            for (Map.Entry<String, List<PrintItemRecord>> stringListEntry : itemRecordsMapByTypeOfCurPkgItem.entrySet()) {
                String itemKey = stringListEntry.getKey();
                List<PrintItemRecord> subPrintItemRecords = stringListEntry.getValue();

                // 套餐项深度clone
                PrintItemRecord pkgPrintItemRecordCloned = (PrintItemRecord) DeepCloneUtils.cloneObject(pkgPrintItemRecord);
                if (ObjectUtils.isEmpty(pkgPrintItemRecordCloned)) continue;
                pkgPrintItemRecordCloned.setSubItemRecords(subPrintItemRecords);

                // 非套餐，获取id对应的分组
                List<PrintItemRecord> printItemRecords = itemRecordsMapByItemKey.get(itemKey);
                if (CollectionUtils.isEmpty(printItemRecords)) {
                    printItemRecords = new ArrayList<>();
                    itemRecordsMapByItemKey.put(itemKey, printItemRecords);
                }
                printItemRecords.add(pkgPrintItemRecordCloned);
            }
        }

        return new ArrayList<>(itemRecordsMapByItemKey.values());
    }

    private static List<List<PrintItemRecord>> splitItemByCountOne(List<PrintItemRecord> printItemRecordsMatched,
                                                                   PrintBaseItemDTO printBaseItemDTO) {
        if (CollectionUtils.isEmpty(printItemRecordsMatched)) return Collections.emptyList();
        // 如果是标签单，需预处理金额
        if (printBaseItemDTO instanceof PrintLabelDTO) {
            for (PrintItemRecord printItemRecord : printItemRecordsMatched) {
                BigDecimal tempItemTotal;
                BigDecimal itemPrice = Objects.requireNonNull(printItemRecord.getPrice(), "商品价格不得为空");
                if (printItemRecord.getAsWeight()) {
                    BigDecimal propertyPrice = BigDecimalUtils.nonNullValue(printItemRecord.getPropertyPrice());
                    tempItemTotal = itemPrice.multiply(printItemRecord.getNumber()).add(propertyPrice);
                } else if (printItemRecord.getAsPackage()) {
                    BigDecimal propertyPrice = BigDecimalUtils.nonNullValue(printItemRecord.getPropertyPrice());
                    BigDecimal ingredientPrice = BigDecimalUtils.nonNullValue(printItemRecord.getIngredientPrice());
                    tempItemTotal = itemPrice.add(propertyPrice).add(ingredientPrice);
                    if (!CollectionUtils.isEmpty(printItemRecord.getSubItemRecords())) {
                        for (PrintItemRecord subPrintItemRecord : printItemRecord.getSubItemRecords()) {
                            BigDecimal subPropertyPrice = BigDecimalUtils.nonNullValue(subPrintItemRecord.getPropertyPrice());
                            if (subPrintItemRecord.getAsWeight()) {
                                tempItemTotal = tempItemTotal.add(subPropertyPrice.multiply(subPrintItemRecord.getNumber()));
                            } else {
                                BigDecimal pkgCnt = Optional.ofNullable(subPrintItemRecord.getPkgCnt()).orElse(BigDecimal.ONE);
                                tempItemTotal = tempItemTotal.add(subPropertyPrice.multiply(pkgCnt).multiply(subPrintItemRecord.getNumber()));
                            }
                            BigDecimal subIngredientPrice = BigDecimalUtils.nonNullValue(subPrintItemRecord.getIngredientPrice());
                            tempItemTotal = tempItemTotal.add(subIngredientPrice.multiply(subPrintItemRecord.getNumber()));
                        }
                    }
                } else {
                    tempItemTotal = itemPrice.add(BigDecimalUtils.nonNullValue(printItemRecord.getPropertyPrice()));
                }
                printItemRecord.setPrice(tempItemTotal);
            }
        }
        // 切纸逻辑
        List<List<PrintItemRecord>> resultItemRecord = new ArrayList<>();
        for (PrintItemRecord printItemRecord : printItemRecordsMatched) {
            List<PrintItemRecord> subPrintItemRecords = printItemRecord.getSubItemRecords();
            if (!CollectionUtils.isEmpty(subPrintItemRecords)) {
                // 子菜品不为空，说明是套餐
                for (PrintItemRecord subPrintItemRecord : subPrintItemRecords) {
                    for (int i = 0; i < subPrintItemRecord.getNumber().intValue(); i++) {
                        PrintItemRecord printItemRecordCloned = (PrintItemRecord) DeepCloneUtils.cloneObject(printItemRecord);
                        if (ObjectUtils.isEmpty(printItemRecordCloned)) continue;

                        PrintItemRecord subPrintItemRecordCloned = (PrintItemRecord) DeepCloneUtils.cloneObject(subPrintItemRecord);
                        if (ObjectUtils.isEmpty(subPrintItemRecordCloned)) continue;
                        subPrintItemRecordCloned.setNumber(BigDecimal.ONE);
                        subPrintItemRecordCloned.setUnit(MultiLangUtils.get("copies"));
                        printItemRecordCloned.setNumber(BigDecimal.ONE);
                        printItemRecordCloned.setSubItemRecords(Collections.singletonList(subPrintItemRecordCloned));
                        if (0 == printItemRecord.getNumber().intValue()) {
                            log.info("套餐(" + printItemRecord.getItemName() + ")"
                                    + "子菜品(" + printItemRecord.getItemName() + ")"
                                    + "数量为0,无法打印");
                        }
                        for (int number = 0; number < printItemRecord.getNumber().intValue(); number++) {
                            resultItemRecord.add(Collections.singletonList(printItemRecordCloned));
                        }
                    }
                }
            } else {
                PrintItemRecord printItemRecordCloned = (PrintItemRecord) DeepCloneUtils.cloneObject(printItemRecord);
                if (ObjectUtils.isEmpty(printItemRecordCloned)) continue;

                // 称重菜品循环add次数为1
                // 非称重菜品循环add次数为原数量，同时需对每一个clone的菜品设置数量为1
                if (!printItemRecordCloned.getAsWeight()) printItemRecordCloned.setNumber(BigDecimal.ONE);
                int itemRecordNumber = printItemRecordCloned.getAsWeight() ? 1 : printItemRecord.getNumber().intValue();
                for (int number = 0; number < itemRecordNumber; number++) {
                    resultItemRecord.add(Collections.singletonList(printItemRecordCloned));
                }
            }
        }
        return resultItemRecord;
    }
}
