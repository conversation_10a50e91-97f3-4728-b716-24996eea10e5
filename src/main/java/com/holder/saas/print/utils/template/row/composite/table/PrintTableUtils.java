/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:PrintTableUtils.java
 * Date:2019-12-4
 * Author:terry
 */

package com.holder.saas.print.utils.template.row.composite.table;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.holder.saas.print.entity.biz.ItemTableFormatBO;
import com.holder.saas.print.entity.enums.ItemActualTypeEnum;
import com.holder.saas.print.entity.enums.ItemStateEnum;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.calculator.item.PrintCalculatorFactory;
import com.holder.saas.print.utils.template.calculator.item.PrintItemCalculator;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.print.content.PrintBaseItemDTO;
import com.holderzone.saas.store.dto.print.content.nested.AdditionalCharge;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import com.holderzone.saas.store.dto.print.template.printable.Table;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintTableUtils
 * @date 2018/02/14 09:00
 * @description 商品Table模板工具类
 * @program holder-saas-store-print
 */
@Slf4j
public final class PrintTableUtils {

    /**
     * 以Table的方式添加前台菜品表单
     *
     * @param printItemRecordList
     * @param total
     * @param pageSize
     * @param cartSeparator
     */
    public static List<PrintRow> resolveTableItemRow(List<PrintItemRecord> printItemRecordList, BigDecimal total,
                                                     int pageSize, ItemTableFormatBO itemTableFormatBo, String cartSeparator) {
        if (CollectionUtils.isEmpty(printItemRecordList)) {
            // 允许菜品列表为空，强校验交给使用方来控制
            return Collections.emptyList();
        }
        // 商品去重
        if (itemTableFormatBo.isDuplicatePackage()) {
            PrintTableDuplicateUtils.duplicatePackage(printItemRecordList);
            log.info("打印处理套餐去重完成，菜品信息: {}", JSONUtil.toJsonStr(printItemRecordList));
        }

        List<PrintRow> printRows = new ArrayList<>();

        boolean dynamicTable = itemTableFormatBo != null;
        Font layoutFont = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::getLayout)
                .map(FormatMetadata::toFont).orElse(Font.SMALL);
        boolean showTypeTotal = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::getTypeTotal)
                .map(FormatMetadata::isEnable).orElse(false);
        boolean showItemPrice = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::getItemPrice)
                .map(FormatMetadata::isEnable).orElse(true);
        boolean showItemDiscountAfterTotal = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::getItemDiscountAfterTotal)
                .map(FormatMetadata::isEnable).orElse(false);
        boolean showItemTotal = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::getItemTotal)
                .map(FormatMetadata::isEnable).orElse(true);
        boolean showItemProperty = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::getItemProperty)
                .map(FormatMetadata::isEnable).orElse(true);
        boolean showItemRemark = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::getItemRemark)
                .map(FormatMetadata::isEnable).orElse(true);
        boolean showItemNumTotal = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::getItemNumTotal)
                .map(FormatMetadata::isEnable).orElse(false);
        boolean showItemSumTotal = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::getItemSumTotal)
                .map(FormatMetadata::isEnable).orElse(true);

        boolean takeout = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::isTakeOut).orElse(false);
        // 是否退款进入
        boolean isRefund = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::isRefund).orElse(false);
        Font itemSumTotalFont = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::getItemSumTotal)
                .map(FormatMetadata::toFont).orElseGet(() -> dynamicTable ? Font.NORMAL : Font.SMALL);

        PrintItemCalculator printItemCalculator = PrintCalculatorFactory.create(
                pageSize, dynamicTable, showItemPrice, showItemTotal, layoutFont);

        List<Integer> headerColumnWidthList = printItemCalculator.getColumnWidthList(isRefund);
        List<Boolean> alignRights = printItemCalculator.getAlignRights();
        boolean lineFeedInnerCell = printItemCalculator.getLineFeedInnerCell();
        List<Text> headerColumnTextList = printItemCalculator.getColumnHeaderList(isRefund);
        if (!Font.SMALL.equals(layoutFont)) {
            headerColumnTextList.forEach(text -> text.setFont(layoutFont));
        }

        TableRowContext context = new TableRowContext(printRows, headerColumnWidthList, alignRights, lineFeedInnerCell);
        context.addSeparator(cartSeparator);
        context.addHeaders(headerColumnTextList);
        context.setTakeOut(takeout);
        /**
         * 注：
         * 属性加价 是 所有属性乘上份数后的总价格
         *
         * 称重菜品：
         * 显示单价=菜品单价+属性加价
         * 显示小计=菜品单价*菜品数量+属性加价*1
         *
         * 套餐菜品：
         * 显示单价=菜品单价+成分加价(固定为0)+属性加价(固定为0)
         *                      +（子菜品成分加价*选择数量）
         *                      +（称重子菜品属性加价*选择数量）+（非称重子菜品属性加价*成分配置数量*选择数量）
         * 显示小计=显示单价*菜品数量
         *
         * 其他菜品：
         * 显示单价=菜品单价+属性加价
         * 显示小计=显示单价*菜品数量
         */
        if (showTypeTotal) {
            showTypeHandler(context, printItemRecordList, printItemCalculator, itemTableFormatBo);
        } else {
            cal(printItemRecordList, showItemDiscountAfterTotal, showItemProperty,
                    showItemRemark, context, printItemCalculator, itemTableFormatBo);
            context.applyStashed();
        }
        if (total != null && showItemSumTotal) {
            if (dynamicTable) {
                if (showItemNumTotal) {
                    BigDecimal itemNumTotal = printItemRecordList.stream()
                            .map(PrintItemRecord::getNumber)
                            .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    String numTotalStr = "  " + BigDecimalUtils.quantityTrimmedString(itemNumTotal);
                    String moneyTotalStr = BigDecimalUtils.moneyTrimmedString(total);
                    context.addRow(printItemCalculator.getTotal(numTotalStr, moneyTotalStr, itemSumTotalFont));
                } else {
                    context.addItemSumTotal(BigDecimalUtils.moneyTrimmedString(total), itemSumTotalFont);
                }
            } else {
                context.addSumTotal(BigDecimalUtils.moneyTrimmedString(total), itemSumTotalFont);
            }
        }
//        context.addSeparator();
        return printRows;
    }

    public static void showTypeHandler(TableRowContext context,
                                       List<PrintItemRecord> printItemRecordList,
                                       PrintItemCalculator printItemCalculator,
                                       ItemTableFormatBO itemTableFormatBo) {
        boolean showItemProperty = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::getItemProperty)
                .map(FormatMetadata::isEnable).orElse(true);

        boolean showItemRemark = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::getItemRemark)
                .map(FormatMetadata::isEnable).orElse(true);

        boolean showItemDiscountAfterTotal = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::getItemDiscountAfterTotal)
                .map(FormatMetadata::isEnable).orElse(false);

        boolean showItemNumTotal = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::getItemNumTotal)
                .map(FormatMetadata::isEnable).orElse(false);

        Map<String, List<PrintItemRecord>> printItemMap = printItemRecordList.stream()
                .filter(e -> !StringUtils.isEmpty(e.getItemTypeName()))
                .collect(Collectors.groupingBy(PrintItemRecord::getItemTypeName));
        List<String> typeNameList = printItemRecordList.stream()
                .map(PrintItemRecord::getItemTypeName)
                .distinct()
                .collect(Collectors.toList());
        Map<String, List<PrintItemRecord>> result = new LinkedHashMap<>();
        typeNameList.forEach(typeName -> {
            List<PrintItemRecord> recordList = printItemMap.get(typeName);
            if (!CollectionUtils.isEmpty(recordList)) {
                result.put(typeName, recordList);
            }
        });
        log.info("有商品分类的明细：{}", JacksonUtils.writeValueAsString(result));
        List<PrintItemRecord> unItemTypeResult = printItemRecordList.stream()
                .filter(e -> StringUtils.isEmpty(e.getItemTypeName())).collect(Collectors.toList());
        log.info("没有商品分类的明细：{}", JacksonUtils.writeValueAsString(unItemTypeResult));
        result.forEach((typeName, printItemRecords) -> {
            Pair<BigDecimal, BigDecimal> pair = cal(printItemRecords, showItemDiscountAfterTotal, showItemProperty,
                    showItemRemark, context, printItemCalculator, itemTableFormatBo);
            log.info("pair对象{}，", JacksonUtils.writeValueAsString(pair));
            if (showItemNumTotal) {
                context.addKeyValueStashed(printItemCalculator.getType("              ---" + typeName + "（" + BigDecimalUtils.quantityTrimmed(pair.getKey()) + "）" + "---",
                        BigDecimalUtils.moneyTrimmedString(pair.getValue())));
            } else {
                context.addKeyValueStashed(printItemCalculator.getType("              ---" + typeName + "---",
                        BigDecimalUtils.moneyTrimmedString(pair.getValue())));
            }
            context.addItemSeparator();
            context.applyStashed();
        });
        if (!CollectionUtils.isEmpty(unItemTypeResult)) {
            cal(unItemTypeResult, showItemDiscountAfterTotal, showItemProperty, showItemRemark, context, printItemCalculator, itemTableFormatBo);
            context.applyStashed();
        }
    }

    private static List<String> dealOtherList(String otherRemark) {
        List<String> list = Lists.newArrayList();
        if (StringUtils.isEmpty(otherRemark)) {
            return new ArrayList<>();
        }
        int length = otherRemark.length();
        if (length <= 10) {
            return new ArrayList<>(Collections.singleton(otherRemark));
        }
        int number = otherRemark.length() / 10;
        int remainder = otherRemark.length() % 10;
        int io = 0;
        int in = 10;
        for (int i = 0; i < number; i++) {
            String remark = otherRemark.substring(io, in);
//            otherRemark = otherRemark.replace(remark, "");
            io = io + 10;
            in = in + 10;
            list.add(remark);
        }


        if (remainder > 0) {
            int num = number * 10;
            String remark = otherRemark.substring(num);
            list.add(remark);
        }

        return list;
    }

    private static Pair<BigDecimal, BigDecimal> cal(List<PrintItemRecord> printItemRecordList,
                                                    boolean showItemDiscountAfterTotal,
                                                    boolean showItemProperty, boolean showItemRemark,
                                                    TableRowContext context, PrintItemCalculator printItemCalculator,
                                                    ItemTableFormatBO itemTableFormatBo) {
        BigDecimal subNumber = BigDecimal.ZERO;
        BigDecimal subMoney = BigDecimal.ZERO;
        log.info("calInfo：{}", JacksonUtils.writeValueAsString(printItemRecordList));

        boolean showMakeNum = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::getShowMakeNum)
                .map(FormatMetadata::isEnable).orElse(false);
        boolean showPackageContent = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::getPackageContent)
                .map(FormatMetadata::isEnable).orElse(false);
        for (PrintItemRecord printItemRecord : printItemRecordList) {
            // 菜品名
            String hangUp = "";
            if (Objects.nonNull(printItemRecord.getItemState())) {
                hangUp = printItemRecord.getItemState().equals(ItemStateEnum.HANG_UP.getCode()) ? MultiLangUtils.get("hang") : "";
            }
            String markName = "";
            if (printItemRecord.getAsPackage()) {
                markName = MultiLangUtils.get("set");
            }
            String giftName = Objects.requireNonNull(printItemRecord.getAsGift(), "赠送标识不得为空") ? MultiLangUtils.get("compl") : "";
            //商品全称
            String itemNameStr = hangUp + markName + printItemRecord.getItemName() + giftName;
            itemNameStr = itemNameStr.replace("\n", " ");
            itemNameStr = itemNameStr.replace("\\n", " ");
            log.info("商品未换行全称：{}", itemNameStr);
            List<String> itemNameStrList = Lists.newArrayList(itemNameStr);
            if (!context.isTakeOut()) {
                itemNameStrList = dealOtherList(itemNameStr);
                log.info("商品换行全称：{}", itemNameStr);
            }
            // 菜品单价、小计、数量
            BigDecimal tempPrice;
            BigDecimal tempSubTotal;
            BigDecimal itemPrice = Objects.requireNonNull(printItemRecord.getPrice(), "商品价格不得为空");
            if (printItemRecord.getAsWeight()) {
                BigDecimal propertyPrice = BigDecimalUtils.nonNullValue(printItemRecord.getPropertyPrice());
                tempPrice = itemPrice.add(propertyPrice);
                tempSubTotal = printItemRecord.getPrice().multiply(printItemRecord.getNumber()).add(propertyPrice);
            } else if (printItemRecord.getAsPackage()) {
                tempPrice = getPackagePrice(printItemRecord);
                tempSubTotal = tempPrice.multiply(printItemRecord.getNumber());
            } else {
                tempPrice = itemPrice.add(BigDecimalUtils.nonNullValue(printItemRecord.getPropertyPrice()));
                tempSubTotal = tempPrice.multiply(printItemRecord.getNumber());
            }
            // 商品合计
            tempSubTotal = getItemTotalPrice(showItemDiscountAfterTotal, printItemRecord, tempSubTotal);
            subMoney = subMoney.add(tempSubTotal);
            subNumber = subNumber.add(BigDecimalUtils.quantityTrimmed(printItemRecord.getNumber()));
            // 商品单价
            String itemPriceStr = BigDecimalUtils.moneyTrimmedString(tempPrice);
            // 商品数量
            String itemNumberStr = "  " + BigDecimalUtils.quantityTrimmedString(printItemRecord.getNumber());
            // 商品小计
            String itemTotalMoneyStr;
            // 如果是退款，直接使用单价即可，赋值的时候单价=小计
            if (Objects.nonNull(itemTableFormatBo) && itemTableFormatBo.isRefundRefund()) {
                BigDecimal price = printItemRecord.getPrice().add(BigDecimalUtils.nonNullValue(printItemRecord.getPropertyPrice()));
                itemTotalMoneyStr = BigDecimalUtils.moneyTrimmedString(price);
            } else {
                itemTotalMoneyStr = BigDecimalUtils.moneyTrimmedString(tempSubTotal);
            }

            // 商品信息
            if (!CollectionUtils.isEmpty(itemNameStrList)) {
                context.addItem(printItemCalculator.getItem(itemNameStrList.get(0), itemPriceStr, itemNumberStr, itemTotalMoneyStr));
                contextIterator(context, printItemCalculator, itemNameStrList);
            }

            // 商品属性
            if (!StringUtils.isEmpty(printItemRecord.getProperty()) && showItemProperty) {
                String property = "  [" + printItemRecord.getProperty() + "]";
                context.addPropRemark(printItemCalculator.getPropOrRemark(property));
            }
            // 商品备注
            if (!StringUtils.isEmpty(printItemRecord.getRemark()) && showItemRemark) {
                String remark = "  [" + MultiLangUtils.get("remark") + printItemRecord.getRemark() + "]";
                context.addPropRemark(printItemCalculator.getPropOrRemark(remark));
            }
            // 套餐子商品的商品信息、属性加价、做法
            if (!CollectionUtils.isEmpty(printItemRecord.getSubItemRecords())) {
                for (PrintItemRecord subPrintItemRecord : printItemRecord.getSubItemRecords()) {
                    String subItemName = " -|" + subPrintItemRecord.getItemName();
                    subItemName = subItemName.replace("\n", " ");
                    subItemName = subItemName.replace("\\n", " ");
                    List<String> strNameList = dealOtherList(subItemName);
                    String subItemNumber = getSubItemNumber(printItemRecord, subPrintItemRecord, showMakeNum);
                    log.info("套餐子菜品显示制作数量{}", subItemNumber);
                    // 套餐子商品信息 showPackageContent
                    boolean isTakeout = !ObjectUtils.isEmpty(printItemRecord.getInvoiceType())
                            && Objects.equals(InvoiceTypeEnum.TAKEOUT.getType(), printItemRecord.getInvoiceType());
                    if (isTakeout) {
                        if (showPackageContent && (!CollectionUtils.isEmpty(itemNameStrList))) {
                            context.addItem(printItemCalculator.getSubItem(strNameList.get(0), subItemNumber));
                            contextIterator(context, printItemCalculator, strNameList);
                        }
                    } else {
                        if (!CollectionUtils.isEmpty(itemNameStrList)) {
                            context.addItem(printItemCalculator.getSubItem(strNameList.get(0), subItemNumber));
                            contextIterator(context, printItemCalculator, strNameList);
                        }
                    }


                    // 套餐子商品属性
                    if (!StringUtils.isEmpty(subPrintItemRecord.getProperty()) && showItemProperty) {
                        String subProperty = "  [" + subPrintItemRecord.getProperty() + "]";
                        context.addPropRemark(printItemCalculator.getPropOrRemark(subProperty));
                    }
                    // 套餐子商品备注
                    if (!StringUtils.isEmpty(subPrintItemRecord.getRemark()) && showItemRemark) {
                        String subRemark = "  [" + MultiLangUtils.get("remark") + subPrintItemRecord.getRemark() + "]";
                        context.addPropRemark(printItemCalculator.getPropOrRemark(subRemark));
                    }
                }
            }
            // 商品活动价 另起一行
            log.info("打印中，菜品信息printItemRecord：{}", printItemRecord);
            if (Objects.nonNull(printItemRecord.getAsGift()) && !printItemRecord.getAsGift()
                    && Objects.nonNull(printItemRecord.getActualPrice())
                    && Objects.nonNull(printItemRecord.getActualType())) {
                String desc = ItemActualTypeEnum.getActualInfo(printItemRecord.getActualType(), printItemRecord.getActualPrice());
                context.addKeyValue(printItemCalculator.getDiscount("", desc));
            }
        }
        return new Pair<>(subNumber, subMoney);
    }

    private static String getSubItemNumber(PrintItemRecord printItemRecord, PrintItemRecord subPrintItemRecord, boolean showMakeNum) {
        String subItemNumber = "  " + BigDecimalUtils.quantityTrimmedString(subPrintItemRecord.getNumber());
        log.info("是否套餐子菜品显示制作数量{}", showMakeNum);
        if (showMakeNum) {
            subItemNumber = BigDecimalUtils.quantityTrimmedString(subPrintItemRecord.getNumber().multiply(printItemRecord.getNumber()));
        }
        return subItemNumber;
    }

    private static BigDecimal getItemTotalPrice(boolean showItemDiscountAfterTotal, PrintItemRecord printItemRecord, BigDecimal tempSubTotal) {
        if (!showItemDiscountAfterTotal || Objects.isNull(printItemRecord.getItemDiscountAfterPrice())) {
            return tempSubTotal;
        }
        return printItemRecord.getItemDiscountAfterPrice();
    }

    /**
     * 获取套餐价格
     */
    public static BigDecimal getPackagePrice(PrintItemRecord printItemRecord) {
        BigDecimal propertyPrice = BigDecimalUtils.nonNullValue(printItemRecord.getPropertyPrice());
        BigDecimal ingredientPrice = BigDecimalUtils.nonNullValue(printItemRecord.getIngredientPrice());
        BigDecimal itemPrice = BigDecimalUtils.nonNullValue(printItemRecord.getPrice());
        BigDecimal tempPrice = itemPrice.add(propertyPrice).add(ingredientPrice);
        if (CollectionUtils.isEmpty(printItemRecord.getSubItemRecords())) {
            return tempPrice;
        }
        if (Objects.nonNull(printItemRecord.getHasAttr()) && BooleanEnum.TRUE.getCode() == printItemRecord.getHasAttr()) {
            BigDecimal singleItemAttrTotal = Optional.ofNullable(printItemRecord.getSingleItemAttrTotal()).orElse(BigDecimal.ZERO);
            BigDecimal singleAddPriceTotal = Optional.ofNullable(printItemRecord.getSingleAddPriceTotal()).orElse(BigDecimal.ZERO);
            tempPrice = tempPrice.add(singleItemAttrTotal).add(singleAddPriceTotal);
            return tempPrice;
        }
        for (PrintItemRecord subPrintItemRecord : printItemRecord.getSubItemRecords()) {
            BigDecimal subPropertyPrice = BigDecimalUtils.nonNullValue(subPrintItemRecord.getPropertyPrice());
            BigDecimal subIngredientPrice = BigDecimalUtils.nonNullValue(subPrintItemRecord.getIngredientPrice());
            if (Boolean.TRUE.equals(subPrintItemRecord.getAsWeight())) {
                tempPrice = tempPrice.add(subPropertyPrice.multiply(subPrintItemRecord.getNumber()));
                tempPrice = tempPrice.add(subIngredientPrice.multiply(subPrintItemRecord.getNumber()));
            } else {
                tempPrice = tempPrice.add(subPropertyPrice.multiply(subPrintItemRecord.getNumber()));
                BigDecimal pkgDefaultCnt = Optional.ofNullable(subPrintItemRecord.getPkgCnt()).orElse(BigDecimal.ONE);
                BigDecimal pkgSelectCount = subPrintItemRecord.getNumber().divide(pkgDefaultCnt, 2, RoundingMode.HALF_UP);
                tempPrice = tempPrice.add(subIngredientPrice.multiply(pkgSelectCount));
            }
        }
        return tempPrice;
    }

    private static void contextIterator(TableRowContext context, PrintItemCalculator printItemCalculator, List<String> strNameList) {
        if (strNameList.size() > 1) {
            for (int i = 1; i < strNameList.size(); i++) {
                context.addItem(printItemCalculator.getItem(strNameList.get(i), "", "", ""));
            }
        }
    }

    /**
     * 以KeyValue的方式添加后厨菜品表单
     *
     * @param printDTO
     * @param isCancel
     */
    public static List<PrintRow> resolveKitchenItemRow(PrintBaseItemDTO printDTO, boolean isCancel,
                                                       ItemTableFormatBO itemTableFormatBo) {
        log.info("打印中itemTableFormatBo：{}", JacksonUtils.writeValueAsString(itemTableFormatBo));
        List<PrintRow> printRows = new ArrayList<>();

        PrintRowUtils.add(printRows, new Separator());

        Font itemNameFont = Optional.ofNullable(itemTableFormatBo.getItemName())
                .map(FormatMetadata::toFont).orElse(Font.NORMAL);
        //数量和名字统一字体大小
//        Font itemNumberFont = Optional.ofNullable(itemTableFormatBo.getItemNumber())
//                .map(FormatMetadata::toFont).orElse(Font.NORMAL);

        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("item"), itemNameFont)
                .setValueString(MultiLangUtils.get("quantity"), itemNameFont));

        List<PrintItemRecord> printItemRecordList = printDTO.getItemRecordList();
        if (CollectionUtils.isEmpty(printItemRecordList)) {
            throw new IllegalArgumentException("菜品列表为空，无法打印后厨单");
        }

        boolean isItemUnitEnable = Optional.of(itemTableFormatBo)
                .map(ItemTableFormatBO::getItemUnit)
                .map(FormatMetadata::isEnable).orElse(true);
        boolean isItemPropertyEnable = isItemPropertyEnable(itemTableFormatBo);
        Font itemPropertyFont = Optional.of(itemTableFormatBo)
                .map(ItemTableFormatBO::getItemProperty)
                .map(FormatMetadata::toFont).orElse(Font.NORMAL_BOLD);
        boolean isItemRemarkEnable = isItemRemarkEnable(itemTableFormatBo);
        Font itemRemarkFont = Optional.of(itemTableFormatBo)
                .map(ItemTableFormatBO::getItemRemark)
                .map(FormatMetadata::toFont).orElse(Font.NORMAL_BOLD);

        for (int i = 0; i < printItemRecordList.size(); i++) {
            PrintItemRecord printItemRecord = printItemRecordList.get(i);
            String itemNumberUnit = BigDecimalUtils.quantityTrimmedString(printItemRecord.getNumber());
            if (isItemUnitEnable && !StringUtils.isEmpty(printItemRecord.getUnit())) {
                itemNumberUnit = itemNumberUnit + printItemRecord.getUnit();
            }
            StringBuilder sb = getItemNameStringBuilder(isCancel, i, printItemRecord);
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(sb.toString(), itemNameFont)
                    .setValueString(itemNumberUnit, itemNameFont));
            if (!StringUtils.isEmpty(printItemRecord.getProperty()) && isItemPropertyEnable) {
                PrintRowUtils.add(printRows, new Section()
                        .addText(" [" + printItemRecord.getProperty() + "]", itemPropertyFont));
            }
            if (!StringUtils.isEmpty(printItemRecord.getRemark()) && isItemRemarkEnable) {
                PrintRowUtils.add(printRows, new Section()
                        .addText(" [备注：" + printItemRecord.getRemark() + "]", itemRemarkFont));
            }
            setSubItemPrintRow(printItemRecord,
                    printRows,
                    itemNameFont,
                    itemPropertyFont,
                    itemRemarkFont,
                    itemTableFormatBo);
        }

        PrintRowUtils.add(printRows, new Separator());

        return printRows;
    }

    private static void setSubItemPrintRow(PrintItemRecord printItemRecord,
                                           List<PrintRow> printRows,
                                           Font itemNameFont,
                                           Font itemPropertyFont,
                                           Font itemRemarkFont,
                                           ItemTableFormatBO itemTableFormatBo) {
        boolean isItemPropertyEnable = isItemPropertyEnable(itemTableFormatBo);

        boolean isItemRemarkEnable = isItemRemarkEnable(itemTableFormatBo);

        boolean isShowMakeNumEnable = isShowMakeNumEnable(itemTableFormatBo);
        log.info("isShowMakeNumEnable:{}", isShowMakeNumEnable);
        if (!CollectionUtils.isEmpty(printItemRecord.getSubItemRecords())) {
            for (PrintItemRecord subPrintItemRecord : printItemRecord.getSubItemRecords()) {
                String subNumberStr = BigDecimalUtils.quantityTrimmedString(subPrintItemRecord.getNumber());
                if (isShowMakeNumEnable) {
                    subNumberStr = BigDecimalUtils.quantityTrimmedString
                            (subPrintItemRecord.getNumber().multiply(printItemRecord.getNumber()));
                }
                log.info("subNumberStr:{}", subNumberStr);

                String subItemNumberUnit = subNumberStr + (StringUtils.isEmpty(subPrintItemRecord.getUnit())
                        ? "" : subPrintItemRecord.getUnit());
                PrintRowUtils.add(printRows, new KeyValue()
                        .setKeyString(" -|" + subPrintItemRecord.getItemName(), itemNameFont)
                        .setValueString(subItemNumberUnit, itemNameFont));
                addText(printRows, itemPropertyFont, itemRemarkFont, subPrintItemRecord, isItemPropertyEnable, isItemRemarkEnable);
            }
        }
    }

    private static void addText(List<PrintRow> printRows, Font itemPropertyFont, Font itemRemarkFont, PrintItemRecord subPrintItemRecord, boolean isItemPropertyEnable, boolean isItemRemarkEnable) {
        if (!StringUtils.isEmpty(subPrintItemRecord.getProperty()) && isItemPropertyEnable) {
            PrintRowUtils.add(printRows, new Section()
                    .addText("   [" + subPrintItemRecord.getProperty() + "]", itemPropertyFont));
        }
        if (!StringUtils.isEmpty(subPrintItemRecord.getRemark()) && isItemRemarkEnable) {
            PrintRowUtils.add(printRows, new Section()
                    .addText("   [备注：" + subPrintItemRecord.getRemark() + "]", itemRemarkFont));
        }
    }

    private static boolean isShowMakeNumEnable(ItemTableFormatBO itemTableFormatBo) {
        return Optional.of(itemTableFormatBo)
                .map(ItemTableFormatBO::getShowMakeNum)
                .map(FormatMetadata::isEnable).orElse(false);
    }

    private static boolean isItemRemarkEnable(ItemTableFormatBO itemTableFormatBo) {
        return Optional.of(itemTableFormatBo)
                .map(ItemTableFormatBO::getItemRemark)
                .map(FormatMetadata::isEnable).orElse(true);
    }

    private static boolean isItemPropertyEnable(ItemTableFormatBO itemTableFormatBo) {
        return Optional.of(itemTableFormatBo)
                .map(ItemTableFormatBO::getItemProperty)
                .map(FormatMetadata::isEnable).orElse(true);
    }

    @NotNull
    private static StringBuilder getItemNameStringBuilder(boolean isCancel,
                                                          int i,
                                                          PrintItemRecord printItemRecord) {
        StringBuilder sb = new StringBuilder();
        sb.append(i + 1).append(isCancel ? ".【退】" : ".");
        if (Objects.nonNull(printItemRecord.getItemState())) {
            if (printItemRecord.getItemState().equals(ItemStateEnum.HANG_UP.getCode())) {
                sb.append("【挂】");
            }
            if (printItemRecord.getItemState().equals(ItemStateEnum.CALL_UP.getCode())) {
                sb.append("【叫起】");
            }
        }
        if (Boolean.TRUE.equals(printItemRecord.getAsPackage())) {
            sb.append("[套]");
        }
        String itemName = printItemRecord.getItemName().replace("\n", " ");
        itemName = itemName.replace("\\n", " ");
        sb.append(itemName);
        return sb;
    }

    public static List<Integer> getStatsItemColWidthList(int pageSize) {
        if (58 == pageSize) return Arrays.asList(16, 6, 10);
        return Arrays.asList(26, 10, 12);
    }

    public static List<Integer> getStatsPaymentColWidthList(int pageSize) {
        if (58 == pageSize) return Arrays.asList(8, 8, 8, 8);
        return Arrays.asList(18, 10, 10, 10);
    }

    public static List<PrintRow> resolveTableAdditionalChargeRow(List<AdditionalCharge> additionalChargeList,
                                                                 int pageSize, ItemTableFormatBO itemTableFormatBo) {
        List<PrintRow> printRows = new ArrayList<>();
        boolean dynamicTable = itemTableFormatBo != null;
        Font layoutFont = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::getAdditionalCharge)
                .map(FormatMetadata::toFont).orElse(Font.SMALL);
        boolean additionalCharge = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::getAdditionalCharge)
                .map(FormatMetadata::isEnable).orElse(false);
        boolean showItemPrice = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::getAdditionalCharge)
                .map(FormatMetadata::isEnable).orElse(true);
        boolean showItemTotal = Optional.ofNullable(itemTableFormatBo)
                .map(ItemTableFormatBO::getAdditionalCharge)
                .map(FormatMetadata::isEnable).orElse(true);
        if (additionalCharge) {
            // 附加费明细
            PrintItemCalculator printItemCalculator = PrintCalculatorFactory.create(
                    pageSize, dynamicTable, showItemPrice, showItemTotal, layoutFont);
            List<Integer> headerColumnWidthList = printItemCalculator.getColumnWidthList();
            List<Boolean> alignRights = printItemCalculator.getAlignRights();
            boolean lineFeedInnerCell = printItemCalculator.getLineFeedInnerCell();
            List<Text> headerColumnTextList = printItemCalculator.getColumnHeaderList();
            if (!Font.SMALL.equals(layoutFont)) {
                headerColumnTextList.forEach(text -> text.setFont(layoutFont));
            }

            TableRowContext context = new TableRowContext(printRows, headerColumnWidthList, alignRights, lineFeedInnerCell);
            // context.addSeparator();
            // context.addHeaders(headerColumnTextList);

            // 具体明细
            additionalChargeList.forEach(item -> {
                BigDecimal number = BigDecimal.ONE;
                if (Objects.isNull(item.getUnitValue())) {
                    item.setUnitValue(item.getChargeValue());
                }
                if (item.getUnitValue().compareTo(BigDecimal.ZERO) > 0) {
                    number = item.getChargeValue().divide(item.getUnitValue(), 2, BigDecimal.ROUND_DOWN);
                }
                String unitValueStr = BigDecimalUtils.moneyTrimmedString(item.getUnitValue());
                String numberStr = BigDecimalUtils.quantityTrimmedString(number);
                String chargeValueStr = BigDecimalUtils.moneyTrimmedString(item.getChargeValue());
                context.addItem(printItemCalculator.getItem(item.getChargeName(), unitValueStr, "  " + numberStr, chargeValueStr));
                context.applyStashed();
            });
        }
        return printRows;
    }

    public static String cutOutTextLength(String salesIncome, List<String> newLineList, Integer length) {
        if (length == null) {
            length = 5;
        }
        String replaced = salesIncome.replace("￥", "").replace("-￥", "");
        if (replaced.length() <= length) {
            newLineList.add("");
            return salesIncome;
        }
        String lineString = replaced.substring(length);
        newLineList.add(lineString);
        return salesIncome.replace(lineString, "");

    }

    public static void addNewLine(Table table, List<String> newLine) {
        if (CollectionUtil.isEmpty(newLine)) {
            return;
        }
        if (newLine.stream().anyMatch(e -> !"".equals(e))) {
            List<Text> newLineRow = Lists.newArrayList();
            newLine.forEach(e -> newLineRow.add(new Text(e)));
            //添加新行
            table.addRow(newLineRow);
        }
        newLine.clear();
    }

    public static List<Integer> getOverViewNetSaleColWidthList(int pageSize) {
        if (58 == pageSize) return Arrays.asList(12, 10, 10);
        return Arrays.asList(26, 10, 12);
    }

    public static List<Integer> getOverViewGrouponColWidthList(int pageSize) {
        if (58 == pageSize) return Arrays.asList(8, 8, 8, 8);
        return Arrays.asList(18, 10, 10, 10);
    }
}
