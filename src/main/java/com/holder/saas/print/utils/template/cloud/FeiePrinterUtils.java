package com.holder.saas.print.utils.template.cloud;

/**
 * <AUTHOR>
 * @date 2024/4/8
 * @description 飞蛾打印机工具类
 */
public class FeiePrinterUtils {

    public static final String DIVIDING_LINE = "--------------------------------<BR>";

    private FeiePrinterUtils() {
    }

    /**
     * 换行符
     */
    public static String addLineBreak(String content) {
        return content + "<BR>";
    }

    /**
     * 居中放大
     */
    public static String addCenterMagnify(String content) {
        return String.format("<CB>%s</CB>", content);
    }

    /**
     * 放大一倍
     */
    public static String addMagnifyOnce(String content) {
        return String.format("<B>%s</B>", content);
    }

    /**
     * 放大两倍
     */
    public static String addMagnifyTwice(String content) {
        return String.format("<DB>%s</DB>", content);
    }

    /**
     * 变高一倍
     */
    public static String addHeightenOnce(String content) {
        return String.format("<L>%s</L>", content);
    }

    /**
     * 变宽一倍
     */
    public static String addWidenOnce(String content) {
        return String.format("<W>%s</W>", content);
    }

    /**
     * 字体加粗
     */
    public static String addBoldFont(String content) {
        return String.format("<BOLD>%s</BOLD>", content);
    }

    /**
     * 放大一倍加粗
     */
    public static String addMagnifyOnceBold(String content) {
        return String.format("<B><BOLD>%s</BOLD></B>", content);
    }

    /**
     * 变高一倍加粗
     */
    public static String addHeightenOnceBold(String content) {
        return String.format("<L><BOLD>%s</BOLD></L>", content);
    }

    /**
     * 变宽一倍加粗
     */
    public static String addWidenOnceBold(String content) {
        return String.format("<W><BOLD>%s</BOLD></W>", content);
    }

    /**
     * 分割线
     */
    public static String addDividingLine() {
        return DIVIDING_LINE;
    }

    /**
     * 右对齐
     */
    public static String addRight(String content) {
        return String.format("<RIGHT>%s</RIGHT>", content);
    }

}
