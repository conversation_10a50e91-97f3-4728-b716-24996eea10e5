package com.holder.saas.print.utils;

import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 * @date 2020-01-31 下午8:13
 */
public class StringPrintUtils {

    private StringPrintUtils() {

    }

    /**
     * @param orignal 要截取的字符串
     * @param start   开始下标
     * @param count   截取长度
     * @return
     */
    public static String substringByte(String orignal, int start, int count) {
        // 如果目标字符串为空，则直接返回，不进入截取逻辑；
        if (orignal == null || "".equals(orignal)) {
            return orignal;
        }
        // 截取Byte长度必须>0
        if (count <= 0) {
            return orignal;
        }
        // 截取的起始字节数必须比
        if (start < 0) {
            start = 0;
        }
        try {
            // 截取字节起始字节位置大于目标String的Byte的length则返回空值
            if (start >= getStringByteLengths(orignal)) {
                return null;
            }
            return subStr(orignal, start, count);
        } catch (UnsupportedEncodingException e) {
            return orignal;
        }
    }


    public static String subStr(String orignal, int start, int count) throws UnsupportedEncodingException {
        StringBuilder buff = new StringBuilder();
        int len = 0;
        char c;

        // 遍历String的每一个Char字符，计算当前总长度
        // 如果到当前Char的的字节长度大于要截取的字符总长度，则跳出循环返回截取的字符串。
        for (int i = 0; i < orignal.toCharArray().length; i++) {

            c = orignal.charAt(i);

            // 当起始位置为0时候
            if (start == 0) {
                len += String.valueOf(c).getBytes("GBK").length;
                if (len <= count) {
                    buff.append(c);
                } else {
                    break;
                }
            } else {
                // 截取字符串从非0位置开始
                len += String.valueOf(c).getBytes("GBK").length;
                if (len > start && len <= start + count) {
                    buff.append(c);
                }
                if (len > start + count) {
                    break;
                }
            }
        }
        return buff.toString();
    }

    public static int getStringByteLengths(String args) throws UnsupportedEncodingException {
        return args != null && !"".equals(args) ? args.getBytes("GBK").length : 0;
    }

    /**
     * 获取字符串字节长度
     */
    public static int getLength(String s) {
        int length = 0;
        for (int i = 0; i < s.length(); i++) {
            int ascii = Character.codePointAt(s, i);
            if (ascii >= 0 && ascii <= 255) {
                length++;
            } else {
                length += 2;
            }
        }
        return length;
    }

    /**
     * 字符串居中
     */
    public static String strToCenter(String s, int max) {
        if (getLength(s) >= max) {
            return s;
        }
        int difference = max - getLength(s);
        int suffixCount = difference / 2;
        int prefixCount = difference - suffixCount;
        return supplyBlankSpace(prefixCount) + s + supplyBlankSpace(suffixCount);
    }

    /**
     * 提供空格
     */
    public static String supplyBlankSpace(int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(" ");
        }
        return sb.toString();
    }

    public static final Integer RESULT_NAME_LENGTH = 2;

    public static String protectedName(String userName) {
        userName = userName.trim();
        char[] r = userName.toCharArray();
        String resultName = "";
        if (r.length < RESULT_NAME_LENGTH) {
            resultName = userName;
        }
        if (r.length == RESULT_NAME_LENGTH) {
            resultName = r[0] + "*";
        }
        if (r.length > RESULT_NAME_LENGTH) {
            StringBuilder star = new StringBuilder();
            for (int i = 0; i < r.length - RESULT_NAME_LENGTH; i++) {
                star.append("*");
            }
            resultName = r[0] + star.toString() + r[r.length - 1];
        }
        return resultName;
    }
}
