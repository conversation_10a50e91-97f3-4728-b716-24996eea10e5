package com.holder.saas.print.utils.template.calculator.item;

import com.holder.saas.print.utils.MultiLangUtils;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class Print80Price0Total1Calculator extends PrintItemCommonCalculator {

    public Print80Price0Total1Calculator(Font componentFont) {
        super(componentFont);
    }

    @Override
    public List<Text> getColumnHeaderList() {
        return Stream.of(MultiLangUtils.get("item"), MultiLangUtils.get("quantity"), MultiLangUtils.get("item_sum"))
                .map(this::convertToText)
                .collect(Collectors.toList());
    }

    @Override
    public List<Text> getColumnHeaderList(boolean isRefund) {
        return Stream.of(isRefund ? MultiLangUtils.get("item_refund") : MultiLangUtils.get("item"),
                        isRefund ? MultiLangUtils.get("quantity_refund") : MultiLangUtils.get("quantity"),
                        MultiLangUtils.get("item_sum"))
                .map(this::convertToText)
                .collect(Collectors.toList());
    }

    @Override
    public List<Integer> getColumnWidthList() {
        return Arrays.asList(32, 6, 10);
    }

    @Override
    public List<Integer> getColumnWidthList(boolean isRefund) {
        // 退款商品表头字数要多一些，重新配置宽度
        return isRefund ? Arrays.asList(30, 8, 10) : getColumnWidthList();
    }

    @Override
    public List<Boolean> getAlignRights() {
        return Arrays.asList(false, false, true);
    }

    @Override
    public List<Text> getItem(String item, String price, String quantity, String subTotal) {
        return Stream.of(item, quantity, subTotal).map(this::convertToText).collect(Collectors.toList());
    }

    @Override
    public List<Text> getItemSingle(String item) {
        return Stream.of(item).map(this::convertToText).collect(Collectors.toList());
    }

    @Override
    public List<Text> getSubItem(String subItemName, String subItemNumber) {
        return Stream.of(subItemName, subItemNumber, "s").map(this::convertToText).collect(Collectors.toList());
    }

    @Override
    public List<Text> getTotal(String numTotal, String moneyTotal, Font font) {
        return Stream.of(MultiLangUtils.get("item_price_total"), Optional.ofNullable(numTotal).orElse("s"), moneyTotal)
                .map(s -> new Text(s, font)).collect(Collectors.toList());
    }
}
