package com.holder.saas.print.utils.template;

import com.holder.saas.print.utils.MultiLangUtils;
import com.holderzone.saas.store.enums.print.TradeModeEnum;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TradeModeUtils
 * @date 2018/02/14 09:00
 * @description 就餐类型工具类：堂食、快餐、外卖
 * @program holder-saas-store-print
 */
public final class TradeModeUtils {

    public static String getCoStartName(int tradeMode) {
        switch (TradeModeEnum.ofMode(tradeMode)) {
            case DINE:
                return MultiLangUtils.get("open_table_time");
            case SNACK:
                return MultiLangUtils.get("order_time");
            default:
                return MultiLangUtils.get("open_table_time");
        }
    }

    public static String getMarkName(Integer tradeMode) {
        switch (TradeModeEnum.ofMode(tradeMode)) {
            case DINE:
                return MultiLangUtils.get("table_name");
            case SNACK:
                return MultiLangUtils.get("mark_name");
            default:
                return MultiLangUtils.get("table_name");
        }
    }

    public static String getMarkNameWithoutColon(int tradeMode) {
        switch (TradeModeEnum.ofMode(tradeMode)) {
            case DINE:
                return MultiLangUtils.get("table_name_without_colon");
            case SNACK:
                return MultiLangUtils.get("mark_name_without_colon");
            default:
                return MultiLangUtils.get("table_name_without_colon");
        }
    }

    public static String getMarkName(int tradeMode, String markName) {
        switch (TradeModeEnum.ofMode(tradeMode)) {
            case DINE:
            case SNACK:
                return getMarkName(tradeMode);
            default:
                return markName;
        }
    }
}
