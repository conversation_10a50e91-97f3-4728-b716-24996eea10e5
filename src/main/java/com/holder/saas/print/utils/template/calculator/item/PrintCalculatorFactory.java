package com.holder.saas.print.utils.template.calculator.item;

import com.holderzone.saas.store.dto.print.template.convertable.Font;

public class PrintCalculatorFactory {

    /**
     * 根据pageSize计算显示的内容
     * 1、58 纸最多显示三项，商品、数量、小计，商品总量配置显示
     * 2、80 纸最多显示4项，商品、单价、数量、小计，商品总量、单价配置显示
     *
     * @param pageSize
     * @param showItemPrice
     * @param showItemTotal
     * @return
     */
    public static PrintItemCalculator create(int pageSize, boolean dynamic,
                                             boolean showItemPrice, boolean showItemTotal, Font componentFont) {
        if (!dynamic) {
            if (58 == pageSize) {
                return new Print58Calculator(componentFont);
            }
            return new Print80Price1Total1Calculator(componentFont);
        }
        if (58 == pageSize) {
            if (showItemPrice && showItemTotal) {
                //单价和小计都显示
                return new Print58Calculator(componentFont);
            } else if (!showItemPrice && !showItemTotal) {
                //单价和小计都不显示
                return new Print58Price0Total0Calculator(componentFont);
            } else if (showItemPrice) {
                //显示单价不显示小计
                return new Print58Price1Total0Calculator(componentFont);
            } else {
                //显示小计不显示单价
                return new Print58Price0Total1Calculator(componentFont);
            }
        }
        if (!showItemPrice && !showItemTotal) {
            return new Print80Price0Total0Calculator(componentFont);
        }
        if (!showItemPrice) {
            return new Print80Price0Total1Calculator(componentFont);
        }
        if (!showItemTotal) {
            return new Print80Price1Total0Calculator(componentFont);
        }
        return new Print80Price1Total1Calculator(componentFont);
    }
}
