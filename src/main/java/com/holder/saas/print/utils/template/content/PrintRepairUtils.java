/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:PrintRepairUtils.java
 * Date:2019-12-4
 * Author:terry
 */

package com.holder.saas.print.utils.template.content;

import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holderzone.saas.store.dto.print.content.PrintBaseItemDTO;
import com.holderzone.saas.store.dto.print.content.PrintCoTableCbDTO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.content.PrintPreCoTableCbDTO;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public class PrintRepairUtils {

    public static void correctSubRecordItemNameAndUnit(PrintDTO printDTO) {
        if (printDTO instanceof PrintBaseItemDTO) {
            PrintBaseItemDTO printBaseItemDTO = (PrintBaseItemDTO) printDTO;
            correctSubRecordItemNameAndUnit(printBaseItemDTO.getItemRecordList());
            return;
        }
        if (printDTO instanceof PrintPreCoTableCbDTO) {
            PrintPreCoTableCbDTO printBaseItemDTO = (PrintPreCoTableCbDTO) printDTO;
            List<PrintPreCoTableCbDTO.PrintTableDTO> tableOrderList = printBaseItemDTO.getTableOrderList();
            for (PrintPreCoTableCbDTO.PrintTableDTO printTableDTO : tableOrderList) {
                List<PrintItemRecord> printItemRecordList = printTableDTO.getPrintItemRecordList();
                correctSubRecordItemNameAndUnit(printItemRecordList);
            }
            return;
        }
        if (printDTO instanceof PrintCoTableCbDTO) {
            PrintCoTableCbDTO printBaseItemDTO = (PrintCoTableCbDTO) printDTO;
            List<PrintCoTableCbDTO.PrintTableDTO> tableOrderList = printBaseItemDTO.getTableOrderList();
            for (PrintCoTableCbDTO.PrintTableDTO printTableDTO : tableOrderList) {
                List<PrintItemRecord> printItemRecordList = printTableDTO.getPrintItemRecordList();
                correctSubRecordItemNameAndUnit(printItemRecordList);
            }
        }
    }

    private static void correctSubRecordItemNameAndUnit(List<PrintItemRecord> itemRecordList) {
        if (!CollectionUtils.isEmpty(itemRecordList)) {
            for (PrintItemRecord printItemRecord : itemRecordList) {
                if (!CollectionUtils.isEmpty(printItemRecord.getSubItemRecords())) {
                    for (PrintItemRecord subItemRecord : printItemRecord.getSubItemRecords()) {
                        correctSubRecordItemNameAndUnit(subItemRecord);
                    }
                }
            }
        }
    }

    private static void correctSubRecordItemNameAndUnit(PrintItemRecord printItemRecord) {
        BigDecimal pkgDefaultCnt = Optional.ofNullable(printItemRecord.getPkgCnt()).orElse(BigDecimal.ONE);
        if (printItemRecord.getAsWeight()) {
            String itemName = printItemRecord.getItemName() + "x"
                    + BigDecimalUtils.quantityTrimmedString(pkgDefaultCnt) + printItemRecord.getUnit();
            printItemRecord.setItemName(itemName);
            printItemRecord.setUnit(MultiLangUtils.get("copies"));
        } else {
            printItemRecord.setNumber(printItemRecord.getNumber().multiply(pkgDefaultCnt));
        }
    }
}
