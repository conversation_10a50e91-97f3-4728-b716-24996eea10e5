package com.holder.saas.print.utils.template.calculator.item;

import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;

import java.util.List;

public interface PrintItemCalculator {

    List<Text> getColumnHeaderList();

    /**
     * 获取所有表头文案
     *
     * @param isRefund 是否退款
     * @return 表头文案
     */
    List<Text> getColumnHeaderList(boolean isRefund);

    List<Integer> getColumnWidthList();

    /**
     * 获取所有表头文案宽度
     *
     * @param isRefund 是否退款
     * @return 表头文案宽度
     */
    List<Integer> getColumnWidthList(boolean isRefund);

    List<Boolean> getAlignRights();

    Text getType(String text);

    List<Text> getItem(String item, String price, String quantity, String subTotal);

    List<Text> getItemSingle(String item);

    List<Text> getSubItem(String subItemName, String subItemNumber);

    Text getPropOrRemark(String text);

    KeyValue getDiscount(String key, String value);

    KeyValue getType(String key, String value);

    default boolean getLineFeedInnerCell() {
        return false;
    }

    List<Text> getTotal(String numTotal, String moneyTotal, Font font);
}
