/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:TableRowContext.java
 * Date:2019-12-4
 * Author:terry
 */

package com.holder.saas.print.utils.template.row.composite.table;

import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import com.holderzone.saas.store.dto.print.template.printable.TableRow;

import java.util.ArrayList;
import java.util.List;

public final class TableRowContext {

    private List<PrintRow> printRows;

    private List<Integer> columnWidths;

    private List<Boolean> alignRights;

    private boolean lineFeedInnerCell;

    private boolean isTakeOut;

    private List<PrintRow> tempPrintRows = new ArrayList<>();

    public TableRowContext(List<PrintRow> printRows, List<Integer> columnWidths,
                           List<Boolean> alignRights, boolean lineFeedInnerCell) {
        this.printRows = printRows;
        this.columnWidths = columnWidths;
        this.alignRights = alignRights;
        this.lineFeedInnerCell = lineFeedInnerCell;
    }

    public void addHeaders(List<Text> headers) {
        PrintRowUtils.add(printRows, new TableRow(headers, columnWidths, alignRights, lineFeedInnerCell));
    }

    public void addRow(List<Text> rows) {
        PrintRowUtils.add(printRows, new TableRow(rows, columnWidths, alignRights, lineFeedInnerCell));
    }

    public void addType(Text type) {
        PrintRowUtils.add(printRows, new Separator(type, 4));
    }

    public void addItem(List<Text> items) {
        PrintRowUtils.add(tempPrintRows, new TableRow(items, columnWidths, alignRights, lineFeedInnerCell));
    }

    public void addPropRemark(Text text) {
        PrintRowUtils.add(tempPrintRows, new Section(text));
    }

    public void addKeyValue(KeyValue keyValue) {
        PrintRowUtils.add(tempPrintRows, keyValue);
    }

    public void addKeyValueStashed(KeyValue keyValue) {
        PrintRowUtils.add(printRows, keyValue);
    }

    public void applyStashed() {
        printRows.addAll(tempPrintRows);
        tempPrintRows.clear();
    }

    public void addItemSumTotal(String total, Font itemSumTotalFont) {
        PrintRowUtils.add(printRows, new KeyValue(
                new Text(MultiLangUtils.get("item_price_total"), itemSumTotalFont),
                new Text(total, itemSumTotalFont)
        ));
    }

    public void addItemSeparator() {
        PrintRowUtils.add(tempPrintRows, new KeyValue(
                Text.BLANK,
                Text.BLANK
        ));
    }

    public void addSumTotal(String total, Font itemSumTotalFont) {
        PrintRowUtils.add(printRows, new KeyValue(
                new Text(MultiLangUtils.get("price_total"), itemSumTotalFont),
                new Text(total, itemSumTotalFont)
        ));
    }

    public void addSeparator() {
        PrintRowUtils.add(printRows, new Separator());
    }
    public void addSeparator(String separator) {
        PrintRowUtils.add(printRows, null == separator ? new Separator() : new Separator(new Text(separator,Font.SMALL)));
    }

    public boolean isTakeOut() {
        return isTakeOut;
    }

    public void setTakeOut(boolean takeOut) {
        isTakeOut = takeOut;
    }
}
