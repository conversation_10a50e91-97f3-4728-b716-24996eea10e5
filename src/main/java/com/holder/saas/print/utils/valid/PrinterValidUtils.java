package com.holder.saas.print.utils.valid;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.holder.saas.common.enums.BooleanEnum;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.cloud.CloudPrinterDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.print.BusinessTypeEnum;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.enums.print.PrinterTypeEnum;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterValidUtils
 * @date 2018/9/18 11:02
 * @description
 * @program holder-saas-store-print
 */
public class PrinterValidUtils {

    public static final String PRINT_BUSINESS_TYPE = "打印业务类型";
    public static final String STORE_GUID = "门店guid";
    public static final String PRINTER_TYPE = "打印机类型";
    public static final String PRINTER_NAME = "打印机名称";
    public static final String DEVICE_ID = "设备ID";
    public static final String STAFF_GUID = "员工guid";
    public static final String PRINTER_GUID = "打印机GUID";

    public static void createAddValidate(PrinterDTO printerDTO) {
        Asserts asserts = Asserts.create()
                .notNull(printerDTO, "参数")
                .notEmpty(printerDTO.getStoreGuid(), STORE_GUID)
                .notEmpty(printerDTO.getStaffGuid(), STAFF_GUID)
                .notNull(printerDTO.getBusinessType(), PRINT_BUSINESS_TYPE)
                .notEmpty(printerDTO.getPrinterName(), PRINTER_NAME)
                .notNull(printerDTO.getPrinterType(), PRINTER_TYPE)
                .notNull(printerDTO.getPrintCount(), "打印次数")
                .check(printerDTO.getPrintCount() != null && printerDTO.getPrintCount() > 0, "打印次数应该>0")
                .notEmpty(printerDTO.getPrintPage(), "打印纸张类型");
        //如果是pos机添加打印设备校验长度为50
        if (!StringUtils.isEmpty(UserContextUtils.getSource()) && Integer.parseInt(UserContextUtils.getSource()) == BaseDeviceTypeEnum.POS.getCode()) {
            asserts.check(printerDTO.getPrinterName().length() <= 50, "打印机名称应小于50个字符");
        } else {
            asserts.check(printerDTO.getPrinterName().length() <= 16, "打印机名称应小于16个字符");
        }
        // 当打印业务类型为前台打印机, 就必须传打印单据类型
        if (printerDTO.getBusinessType().equals(BusinessTypeEnum.FRONT_PRINTER.getType())) {
//            asserts.notEmpty(printerDTO.getArrayOfInvoiceType(), "打印单据类型(arrayOfInvoiceType)");
            if (!CollectionUtils.isEmpty(printerDTO.getArrayOfInvoiceType())) {
                printerDTO.getArrayOfInvoiceType().forEach(s -> asserts.between(s, 0, 255, "打印单据"));
            }
        }

        // 如果是网络打印机, 那么ip和port不能为空
        if (PrinterTypeEnum.WLAN_PRINTER.getType().equals(printerDTO.getPrinterType())) {
            asserts.notEmpty(printerDTO.getPrinterIp(), "打印机ip").notNull(printerDTO.getPrinterPort(), "打印机端口号");
        }

        // 如果是本地打印机/usb打印机, 设备编号不能为空
        if (PrinterTypeEnum.LOCAL_PRINTER.getType().equals(printerDTO.getPrinterType())
                || PrinterTypeEnum.USB_PRINTER.getType().equals(printerDTO.getPrinterType())) {
            asserts.notEmpty(printerDTO.getDeviceId(), "本机设备编号");
        }

        // 切纸方式验证
        if (!StringUtils.isEmpty(printerDTO.getPrintCut())) {
            asserts.between(printerDTO.getPrintCut(), 0, 3, "切纸方式");
        }
    }

    public static void createQueryValidate(PrinterDTO printerDTO) {
        Asserts.create().notBlank(printerDTO.getPrinterGuid(), PRINTER_GUID);
    }

    public static void queryListValidate(SingleDataDTO request) {
        Asserts.create().notEmpty(request.getDatas(), PRINTER_GUID);
    }

    public static void createListByBizValidate(PrinterDTO printerDTO) {
        Asserts.create()
                .notBlank(printerDTO.getStoreGuid(), STORE_GUID)
                .notBlank(printerDTO.getDeviceId(), DEVICE_ID)
                .notNull(printerDTO.getBusinessType(), PRINT_BUSINESS_TYPE);
    }

    public static void createListByDeviceValidate(PrinterDTO printerDTO) {
        Asserts.create()
                .notBlank(printerDTO.getStoreGuid(), STORE_GUID)
                .notBlank(printerDTO.getDeviceId(), DEVICE_ID);
    }

    public static void updatePrinterValidate(PrinterDTO printerDTO) {
        Asserts asserts = Asserts.create()
                .notBlank(printerDTO.getPrinterGuid(), "打印机guid")
                .notBlank(printerDTO.getStaffGuid(), "操作者");

        // 当打印业务类型为前台打印机, 就必须传打印单据类型
        if (printerDTO.getBusinessType().equals(BusinessTypeEnum.FRONT_PRINTER.getType())) {
//            asserts.notEmpty(printerDTO.getArrayOfInvoiceType(), "打印单据类型(arrayOfInvoiceType)");
            if (!CollectionUtils.isEmpty(printerDTO.getArrayOfInvoiceType())) {
                printerDTO.getArrayOfInvoiceType().forEach(s -> asserts.between(s, 0, 255, "打印单据"));
            }
        }

        // 如果是网络打印机, 那么ip和port不能为空
        if (PrinterTypeEnum.WLAN_PRINTER.getType().equals(printerDTO.getPrinterType())) {
            asserts.notBlank(printerDTO.getPrinterIp(), "打印机ip")
                    .notNull(printerDTO.getPrinterPort(), "打印机端口号");
        }

        // 切纸方式验证
        if (!StringUtils.isEmpty(printerDTO.getPrintCut())) {
            asserts.between(printerDTO.getPrintCut(), 0, 3, "切纸方式");
        }
    }

    public static void createChangeMasterValidate(PrinterDTO printerDTO) {
        Asserts.create()

                .notBlank(printerDTO.getStoreGuid(), STORE_GUID)
                .notBlank(printerDTO.getDeviceId(), DEVICE_ID);
    }

    public static void createDeleteValidate(PrinterDTO printerDTO) {
        Asserts.create().notBlank(printerDTO.getPrinterGuid(), PRINTER_GUID);
    }

    public static void createAddCloudValidate(CloudPrinterDTO cloudPrinterDTO) {
        Asserts asserts = Asserts.create()
                .notNull(cloudPrinterDTO, "参数")
                .notEmpty(cloudPrinterDTO.getStoreGuid(), STORE_GUID)
                .notEmpty(cloudPrinterDTO.getStaffGuid(), STAFF_GUID)
                .notNull(cloudPrinterDTO.getBusinessType(), PRINT_BUSINESS_TYPE)
                .notEmpty(cloudPrinterDTO.getPrinterName(), PRINTER_NAME)
                .notNull(cloudPrinterDTO.getPrinterType(), PRINTER_TYPE)
                .notEmpty(cloudPrinterDTO.getPrintPage(), "打印纸张类型");
        asserts.check(cloudPrinterDTO.getPrinterName().length() <= 20, "打印机名称应小于20个字符");
    }

    public static void createListCloudPrintersValidate(PrinterDTO printerDTO) {
        Asserts.create()
                .notBlank(printerDTO.getStoreGuid(), STORE_GUID)
                .notNull(printerDTO.getPrinterType(), PRINTER_TYPE);
    }

    public static void createCheckCloudValidate(CloudPrinterDTO cloudPrinterDTO) {
        Asserts.create()
                .notBlank(cloudPrinterDTO.getPrinterName(), PRINTER_NAME)
                .notBlank(cloudPrinterDTO.getDeviceKey(), "设备密钥")
                .notBlank(cloudPrinterDTO.getDeviceNo(), "设备编号");
    }

    private final static List<Integer> STATUS = Arrays.asList(0,1);

    public static void autoPrintValidate(Integer status, PrinterDTO printerDTO) {
        Asserts asserts = autoPrintQueryValidate(printerDTO);

        //状态值只能是0和1
        asserts.check(STATUS.contains(status),"状态值有误");
    }

    public static Asserts autoPrintQueryValidate(PrinterDTO printerDTO) {
        return Asserts.create()
                .notNull(printerDTO.getStoreGuid(), STORE_GUID)
                .notNull(printerDTO.getDeviceId(), DEVICE_ID);
    }
}

