package com.holder.saas.print.utils.template.row.composite.table;

import cn.hutool.json.JSONUtil;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 商品table类型对象去重工具类
 *
 * <AUTHOR>
 * @date 2025/7/9 10:08
 */
@Slf4j
public class PrintTableDuplicateUtils {

    /**
     * 套餐数据去重
     *
     * @param printItemRecordList 商品列表
     */
    public static void duplicatePackage(List<PrintItemRecord> printItemRecordList) {
        log.info("打印处理套餐去重开始，菜品信息: {}", JSONUtil.toJsonStr(printItemRecordList));
        for (int i = 0; i < printItemRecordList.size(); i++) {
            PrintItemRecord record = printItemRecordList.get(i);
            // 跳出条件
            if (isSkip(record) || printItemRecordList.size() <= i + 1) {
                continue;
            }
            for (int j = i + 1; j < printItemRecordList.size(); j++) {
                PrintItemRecord printItemRecord = printItemRecordList.get(j);
                // 跳出条件
                if (isSkip(printItemRecord)) {
                    continue;
                }
                // 对比名称、单价、备注、子商品属性
                if (compareParent(record, printItemRecord)) {
                    if (checkSubItem(printItemRecord.getSubItemRecords(), record.getSubItemRecords())) {
                        // 如果都相同，说明是相同套餐，修改数量
                        record.setNumber(record.getNumber().add(printItemRecord.getNumber()));
                        printItemRecord.setIsDuplicatePackage(true);
                    } else {
                        log.info("打印处理套餐去重判断，子套餐存在差异({}/{})", i, j);
                    }
                }
            }
        }
        log.info("打印处理套餐去重结束，菜品信息: {}", JSONUtil.toJsonStr(printItemRecordList));
        // 删除数据
        printItemRecordList.removeIf(item -> Boolean.TRUE.equals(item.getIsDuplicatePackage()));
    }

    /**
     * 比对商品属性
     *
     * @param parentList 对比子商品1
     * @param subList    对比子商品2
     * @return 比对结果
     */
    private static boolean checkSubItem(List<PrintItemRecord> parentList, List<PrintItemRecord> subList) {
        if (CollectionUtils.isEmpty(parentList) || CollectionUtils.isEmpty(subList) || parentList.size() != subList.size()) {
            return false;
        }
        for (int i = 0; i < parentList.size(); i++) {
            PrintItemRecord parent = parentList.get(i);
            PrintItemRecord sub = subList.get(i);
            // 对比名称、数量、属性、单位
            if (!Objects.equals(parent.getItemName(), sub.getItemName())
                    || !Objects.equals(parent.getNumber(), sub.getNumber())
                    || parent.getNumber().compareTo(sub.getNumber()) != 0
                    || !Objects.equals(parent.getUnit(), sub.getUnit())
                    || !Objects.equals(parent.getProperty(), sub.getProperty())) {
                log.info("打印处理套餐去重判断，子套餐存在差异，菜品信息1: {}, 菜品信息2: {}", JSONUtil.toJsonStr(parent),
                        JSONUtil.toJsonStr(sub));
                return false;
            }
        }
        return true;
    }

    /**
     * 是否需要跳过
     *
     * @param printItemRecord 打印数据
     * @return 是否需要跳过
     */
    private static boolean isSkip(PrintItemRecord printItemRecord) {
        // 不是套餐、已经去重
        return !Boolean.TRUE.equals(printItemRecord.getAsPackage())
                || Boolean.TRUE.equals(printItemRecord.getIsDuplicatePackage());
    }

    /**
     * 判断父套餐属性
     *
     * @param record1 套餐1
     * @param record2 套餐2
     * @return 比对结果
     */
    private static boolean compareParent(PrintItemRecord record1, PrintItemRecord record2) {
        // 首先对比名字，备注
        if (Objects.equals(record1.getItemName(), record2.getItemName())
                && Objects.equals(record1.getRemark(), record2.getRemark())) {
            // 都相同的情况下，比对套餐价格
            BigDecimal price1 = PrintTableUtils.getPackagePrice(record1);
            BigDecimal price2 = PrintTableUtils.getPackagePrice(record2);
            return price1.compareTo(price2) == 0;
        }
        return false;
    }
}
