package com.holder.saas.print.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

public final class BigDecimalUtils {

    public static BigDecimal quantityTrimmed(BigDecimal quantity) {
        BigDecimal moneyScale0 = quantity.setScale(0, RoundingMode.HALF_UP);
        if (quantity.compareTo(moneyScale0) == 0) return moneyScale0;
        BigDecimal moneyScale1 = quantity.setScale(1, RoundingMode.HALF_UP);
        if (quantity.compareTo(moneyScale1) == 0) return moneyScale1;
        BigDecimal moneyScale2 = quantity.setScale(2, RoundingMode.HALF_UP);
        if (quantity.compareTo(moneyScale2) == 0) return moneyScale2;
        return quantity.setScale(3, RoundingMode.HALF_UP);
    }

    public static String quantityTrimmedString(BigDecimal quantity) {
        return quantityTrimmed(quantity).toString();
    }

    public static String moneyTrimmedString(BigDecimal money) {
        money = BigDecimalUtils.nonNullValue(money);
        BigDecimal moneyScale0 = money.setScale(0, RoundingMode.HALF_UP);
        if (money.compareTo(moneyScale0) == 0) {
            if (moneyScale0.compareTo(BigDecimal.ZERO) >= 0) {
                return "￥" + moneyScale0;
            } else {
                return "-￥" + moneyScale0.abs();
            }
        }
        BigDecimal moneyScale2 = money.setScale(2, RoundingMode.HALF_UP);
        if (moneyScale2.compareTo(BigDecimal.ZERO) >= 0) {
            return "￥" + moneyScale2;
        } else {
            return "-￥" + moneyScale2.abs();
        }
    }

    public static BigDecimal nonNullValue(BigDecimal value) {
        return value != null ? value : BigDecimal.ZERO;
    }
}
