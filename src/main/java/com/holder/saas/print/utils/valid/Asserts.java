package com.holder.saas.print.utils.valid;

import com.holderzone.framework.exception.unchecked.ParameterException;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collection;

/**
 * <AUTHOR>
 * @version 1.0
 * @className Asserts
 * @date 2018/7/10 17:49
 * @description check-检查正确的范围/表达式
 * @program framework-resource-service
 */
public class Asserts {

    private Asserts() {
    }

    public static Asserts create() {
        return new Asserts();
    }

    public Asserts check(final boolean expression, final String message) {
        if (!expression) {
            throw new ParameterException(message);
        }
        return this;
    }

    public Asserts check(final boolean expression, final String message, final Object... args) {
        if (!expression) {
            throw new ParameterException(String.format(message, args));
        }
        return this;
    }

    public Asserts notNull(final Object object, final String name) {
        if (object == null) {
            throw new ParameterException(name + " is null");
        }
        return this;
    }

    public Asserts isNull(final Object object, final String name) {
        if (object != null) {
            throw new ParameterException(name + " is not null");
        }
        return this;
    }

    public Asserts notBlank(final CharSequence s, final String name) {
        if (!StringUtils.hasText(s)) {
            throw new ParameterException(name + " is blank");
        }
        return this;
    }

    public Asserts isBlank(final CharSequence s, final String name) {
        if (StringUtils.hasText(s)) {
            throw new ParameterException(name + " is not blank");
        }
        return this;
    }

    public Asserts notEmpty(final CharSequence s, final String name) {
        if (StringUtils.isEmpty(s)) {
            throw new ParameterException(name + " is empty");
        }
        return this;
    }

    public Asserts isEmpty(final CharSequence s, final String name) {
        if (!StringUtils.isEmpty(s)) {
            throw new ParameterException(name + " is not empty");
        }
        return this;
    }

    public Asserts notEmpty(Collection<?> collection, String fieldName) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new ParameterException(fieldName + " is empty");
        }
        return this;
    }

    public Asserts isEmpty(Collection<?> collection, String fieldName) {
        if (!CollectionUtils.isEmpty(collection)) {
            throw new ParameterException(fieldName + " is not empty");
        }
        return this;
    }

    public Asserts lessLength(final CharSequence s, final int len, final String name) {
        if (StringUtils.isEmpty(s)) {
            throw new ParameterException(name + " is empty");
        }
        if (s.length() > len) {
            throw new ParameterException(name + "长度大于" + len);
        }
        return this;
    }

    public Asserts between(Integer value, Integer left, Integer right, String fieldName) {
        if (value.compareTo(left) < 0) {
            throw new ParameterException(fieldName + " is less than " + left);
        }
        if (value.compareTo(right) > 0) {
            throw new ParameterException(fieldName + " is greater than " + right);
        }
        return this;
    }
}
