package com.holder.saas.print.utils;

import com.holder.saas.print.config.sensitive.SensitiveFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * 敏感词替换
 */
@Slf4j
public class SensitiveUtils {

    private SensitiveUtils(){

    }

    public static String replaceWord(String message){
        try {

            if(StringUtils.isEmpty(message)){
                return message;
            }
            //初始化敏感词过滤器
            SensitiveFilter sensitiveFilter = SensitiveFilter.DEFAULT;

            if(ObjectUtils.isEmpty(sensitiveFilter)){
                log.error("SensitiveFilter初始化失败");
                return message;
            }

            // 进行过滤
            String filter = sensitiveFilter.filter(message, '*');
            if(StringUtils.isEmpty(filter)){
                return message;
            }
            return filter;
        }catch (Exception e){
            log.error("敏感词替换出错："+e.getMessage());
            return message;
        }
    }
}
