package com.holder.saas.print.utils.template.cloud;

import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/9
 * @description 云打印机工具类
 */
@Slf4j
public class CloudPrinterUtils {

    private CloudPrinterUtils() {
    }

    public static final Integer ITEM_TITLE_SIZE = 10;

    public static final Integer LINE_SIZE = 16;

    public static final Integer LINE_SIZE_SMALL = 32;

    public static final Integer ITEM = 10;

    public static final Integer PRICE = 6;

    public static final Integer QUANTITY = 2;

    public static final Integer ITEM_SUM = 6;

    public static final Integer CHARGE = 17;

    public static String addSpaceAfter(String content, int size) {
        int contentLength = getContentLength(content);
        if (contentLength < size) {
            StringBuilder contentBuilder = new StringBuilder(content);
            for (int i = 0; i < size - contentLength; i++) {
                contentBuilder.append(" ");
            }
            content = contentBuilder.toString();
        }
        return content;
    }

    public static String addSpaceAfterByLength(String content, int size) {
        int contentLength = content.length();
        if (contentLength < size) {
            StringBuilder contentBuilder = new StringBuilder(content);
            for (int i = 0; i < size - contentLength; i++) {
                contentBuilder.append(" ");
            }
            content = contentBuilder.toString();
        }
        return content;
    }

    public static String addSpaceAfterByOverLength(String content, int size) {
        int contentLength = getContentLength(content);
        int overLength = contentLength % size;
        if (0 == overLength) {
            return content;
        }
        StringBuilder contentBuilder = new StringBuilder(content);
        for (int i = 0; i < size - overLength; i++) {
            contentBuilder.append(" ");
        }
        content = contentBuilder.toString();
        return content;
    }

    /**
     * 字符混排，末尾如果位数不够一个汉字，就会跳行
     *
     * @param content  传入字符
     * @param lineSize 长度
     */
    public static String handleMixChar(String content, Integer lineSize) {
        StringBuilder mixChar = new StringBuilder();
        int contentLength = getContentLength(content);
        int chSize = lineSize / 2;
        for (int j = 0; j < contentLength / lineSize; j++) {
            for (int i = 0; i <= chSize; i++) {
                int temp = chSize + i;
                if (content.length() < temp) {
                    temp = content.length();
                }
                String substring = content.substring(0, temp);
                int substringLength = getContentLength(substring);
                if (lineSize == substringLength) {
                    mixChar.append(substring);
                    content = content.substring(temp);
                    break;
                }
                if (lineSize == substringLength + 1) {
                    mixChar.append(substring).append(" ");
                    content = content.substring(temp);
                }
            }
        }
        return mixChar + content;
    }

    public static int getContentLength(String content) {
        int contentLength = 0;
        try {
            contentLength = content.getBytes("GBK").length;
        } catch (UnsupportedEncodingException e) {
            log.error("[getContentLength][getBytes]content={}e=", content, e);
        }
        return contentLength;
    }

    public static String addSpaceBefore(String content, int size) {
        int contentLength = getContentLength(content);
        if (contentLength < size) {
            StringBuilder contentBuilder = new StringBuilder();
            for (int i = 0; i < size - contentLength; i++) {
                contentBuilder.append(" ");
            }
            contentBuilder.append(content);
            content = contentBuilder.toString();
        }
        return content;
    }

    public static String addSpaceBeforeByLength(String content, int size) {
        int contentLength = content.length();
        if (contentLength < size) {
            StringBuilder contentBuilder = new StringBuilder();
            for (int i = 0; i < size - contentLength; i++) {
                contentBuilder.append(" ");
            }
            contentBuilder.append(content);
            content = contentBuilder.toString();
        }
        return content;
    }

    public static boolean isEn(String str) {
        boolean b = false;
        try {
            b = str.getBytes("GBK").length == str.length();
        } catch (UnsupportedEncodingException e) {
            log.error("[isEn][getBytes]e=", e);
        }
        return b;
    }

    public static List<String> getStrList(String inputString, int length) {
        int size = inputString.length() / length;
        if (inputString.length() % length != 0) {
            size += 1;
        }
        return getStrList(inputString, length, size);
    }

    private static List<String> getStrList(String inputString, int length, int size) {
        List<String> list = new ArrayList<>();
        for (int index = 0; index < size; index++) {
            String childStr = substring(inputString, index * length, (index + 1) * length);
            list.add(childStr);
        }
        return list;
    }

    private static String substring(String str, int f, int t) {
        if (f > str.length())
            return null;
        if (t > str.length()) {
            return str.substring(f);
        } else {
            return str.substring(f, t);
        }
    }

    public static String getStringByEnter(int length, String string) throws Exception {
        for (int i = 1; i <= string.length(); i++) {
            if (string.substring(0, i).getBytes("GBK").length > length) {
                return string.substring(0, i - 1) + "<BR>" + getStringByEnter(length, string.substring(i - 1));
            }
        }
        return string;
    }

    public static String itemNameAddSpace(String str) {
        int k = getContentLength(str);
        StringBuilder strBuilder = new StringBuilder(str);
        for (int i = 0; i < CloudPrinterUtils.ITEM_TITLE_SIZE - k; i++) {
            strBuilder.append(" ");
        }
        return strBuilder.toString();
    }

    /**
     * 处理间隙
     *
     * @param str1     字符串1
     * @param str2     字符串2
     * @param lineSize 行长度
     * @return 拼接好的字符串
     */
    public static String handleInterval(String str1, String str2, Integer lineSize) {
        String totalStr = str1 + str2;
        int str1Length = CloudPrinterUtils.getContentLength(str1);
        int str2Length = CloudPrinterUtils.getContentLength(str2);
        int totalStrLength = CloudPrinterUtils.getContentLength(totalStr);
        String resultStr = "";
        if (totalStrLength < lineSize) {
            // 同行
            str1 = CloudPrinterUtils.addSpaceAfter(str1, lineSize - str2Length);
            resultStr = str1 + str2;
        } else if (totalStrLength == lineSize) {
            // 异行加空格
            str1 = CloudPrinterUtils.addSpaceAfterByOverLength(str1, lineSize);
            str2 = CloudPrinterUtils.addSpaceBefore(str2, lineSize);
            resultStr = str1 + str2;
        } else {
            if (str1Length > lineSize) {
                // 余数+数量=总数？异行加空格：同行
                str1 = CloudPrinterUtils.handleMixChar(str1, lineSize);
                str1Length = CloudPrinterUtils.getContentLength(str1);
                int nameOverLength = str1Length % lineSize;
                if (nameOverLength + str2Length < lineSize) {
                    // 同行
                    str1 = CloudPrinterUtils.addSpaceAfterByOverLength2(str1, lineSize, str2Length);
                } else {
                    // 加空格
                    str1 = CloudPrinterUtils.addSpaceAfterByOverLength(str1, lineSize);
                    str2 = CloudPrinterUtils.addSpaceBefore(str2, lineSize);
                }
            } else {
                // 加空格
                str1 = CloudPrinterUtils.addSpaceAfterByOverLength(str1, lineSize);
                str2 = CloudPrinterUtils.addSpaceBefore(str2, lineSize);
            }
            resultStr = str1 + str2;
        }
        resultStr = FeiePrinterUtils.addLineBreak(resultStr);
        return resultStr;
    }

    private static String addSpaceAfterByOverLength2(String str1, Integer lineSize, int str2Length) {
        int contentLength = getContentLength(str1);
        int overLength = contentLength % lineSize;
        if (0 == overLength) {
            return str1;
        }
        StringBuilder contentBuilder = new StringBuilder(str1);
        for (int i = 0; i < lineSize - overLength - str2Length; i++) {
            contentBuilder.append(" ");
        }
        str1 = contentBuilder.toString();
        return str1;
    }

}
