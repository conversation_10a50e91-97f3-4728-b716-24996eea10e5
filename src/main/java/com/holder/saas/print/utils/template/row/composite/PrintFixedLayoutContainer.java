/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:PrintFixedContainer.java
 * Date:2019-12-4
 * Author:terry
 */

package com.holder.saas.print.utils.template.row.composite;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holder.saas.print.utils.template.row.composite.table.PrintTableUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.content.nested.ReduceRecord;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.BlankRow;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

public final class PrintFixedLayoutContainer {

    private final List<PrintRow> printRows = new ArrayList<>();

    public List<PrintRow> getPrintRows() {
        return printRows;
    }

    public void addSeparator() {
        PrintRowUtils.add(printRows, new Separator());
    }

    public void addBlankRow() {
        PrintRowUtils.add(printRows, new BlankRow());
    }

    public void addStoreName(String storeName) {
        PrintRowUtils.add(printRows, new Section()
                .addText(storeName, Font.NORMAL)
                .setAlign(Text.Align.Center));
        this.addBlankRow();
    }

    public void addOrderNo(String orderNo) {
        PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.ORDER_NUMBER) + orderNo));
    }

    public void addTableItem(List<PrintItemRecord> itemRecordList, BigDecimal total, int pageSize) {
        printRows.addAll(PrintTableUtils.resolveTableItemRow(itemRecordList, total, pageSize, null, null));
    }

    public void addReduceRecordAndPayable(List<ReduceRecord> reduceRecordList, BigDecimal payable) {
        if (!CollectionUtils.isEmpty(reduceRecordList)) {
            BigDecimal reduceSum = reduceRecordList.stream()
                    .map(ReduceRecord::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);
            if (reduceSum.compareTo(BigDecimal.ZERO) != 0) {
                reduceRecordList.stream()
                        .filter(reduce -> reduce.getAmount() != null
                                && reduce.getAmount().compareTo(BigDecimal.ZERO) != 0)
                        .forEach(reduce -> PrintRowUtils.add(printRows, new KeyValue()
                                .setKeyString(reduce.getName())
                                .setValueString(BigDecimalUtils.moneyTrimmedString(reduce.getAmount()))));
                PrintRowUtils.add(printRows, new KeyValue()
                        .setKeyString("活动合计")
                        .setValueString(BigDecimalUtils.moneyTrimmedString(reduceSum)));
            }
        }
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString("应付")
                .setValueString(BigDecimalUtils.moneyTrimmedString(payable)));
        this.addSeparator();
    }

    public void addPayRecordAndActuallyPay(List<PayRecord> payRecords, BigDecimal changedPay, BigDecimal actuallyPay) {
        if (!CollectionUtils.isEmpty(payRecords)) {
            payRecords.forEach(payRecord -> PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(payRecord.getPayName())
                    .setValueString(BigDecimalUtils.moneyTrimmedString(payRecord.getAmount())))
            );
        }
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString("找零")
                .setValueString(BigDecimalUtils.moneyTrimmedString(changedPay)));
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString("实付")
                .setValueString(BigDecimalUtils.moneyTrimmedString(actuallyPay)));
        this.addSeparator();
    }

    public void addOpStaffAndOpenTableTimeAndCheckTimeAndPrintTime(int pageSize,
                                                                   String operatorStaffName, Long openTableTime,
                                                                   Long checkOutTime, Long createTime) {
        String openTableTimeStr = DateTimeUtils.mills2String(openTableTime, "MM-dd HH:mm");
        String checkOutTimeStr = DateTimeUtils.mills2String(checkOutTime, "MM-dd HH:mm");
        String createTimeStr = DateTimeUtils.mills2String(createTime, "MM-dd HH:mm");
        if (80 == pageSize) {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString("操作员：" + operatorStaffName)
                    .setValueString("下单时间：" + openTableTimeStr));
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString("结账时间：" + checkOutTimeStr)
                    .setValueString("打印时间：" + createTimeStr));
        } else {
            PrintRowUtils.add(printRows, new Section("操作员：" + operatorStaffName));
            PrintRowUtils.add(printRows, new Section("下单时间：" + openTableTimeStr));
            PrintRowUtils.add(printRows, new Section("结账时间：" + checkOutTimeStr));
            PrintRowUtils.add(printRows, new Section("打印时间：" + createTimeStr));
        }
    }
}
