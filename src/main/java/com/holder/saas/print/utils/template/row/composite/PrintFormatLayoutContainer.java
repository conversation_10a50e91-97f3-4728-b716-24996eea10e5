/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:PrintElementContainer.java
 * Date:2019-12-4
 * Author:terry
 */

package com.holder.saas.print.utils.template.row.composite;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.entity.biz.ItemTableFormatBO;
import com.holder.saas.print.entity.biz.PrintCheckOutContentBO;
import com.holder.saas.print.entity.enums.PrintContentEnum;
import com.holder.saas.print.utils.*;
import com.holder.saas.print.utils.template.TradeModeUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holder.saas.print.utils.template.row.composite.table.PrintTableUtils;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.member.terminal.dto.member.response.CustomizeLabelDetails;
import com.holderzone.holder.saas.member.terminal.dto.member.response.MemberPortrayalDetailsDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.MemberPortrayalFieldDetailsDTO;
import com.holderzone.saas.store.dto.print.content.nested.*;
import com.holderzone.saas.store.dto.print.format.metadata.CustomMetadata;
import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.*;
import com.holderzone.saas.store.dto.takeaway.OrderType;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.common.UnitEnum;
import com.holderzone.saas.store.enums.marketing.portrayal.MemberPortrayalFieldEnum;
import com.holderzone.saas.store.enums.marketing.portrayal.StatisticalPeriodEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class PrintFormatLayoutContainer {

    /**
     * printRows 包含所有的打印内容，每一条内容包含内容的类型和内容（文本、图片、二维码、条码）
     */
    private final List<PrintRow> printRows = new ArrayList<>();

    private static final String MD_PATTERN = "MM-dd HH:mm";

    private static final String YMD_PATTERN = "yyyy-MM-dd HH:mm";

    public List<PrintRow> getPrintRows() {
        return printRows;
    }

    /**
     * 添加自定义Header
     *
     * @param headerFormat
     */
    public void addHeader(CustomMetadata headerFormat) {
        if (headerFormat == null) {
            return;
        }
        addCustom(headerFormat);
    }

    /**
     * 添加自定义Footer
     *
     * @param footerFormat
     */
    public void addFooter(CustomMetadata footerFormat) {
        if (footerFormat == null) {
            return;
        }
        addCustom(footerFormat);
    }

    /**
     * 门店名
     *
     * @param storeName
     * @param formatMetadata
     */
    public void addStoreName(String storeName, FormatMetadata formatMetadata) {
        if (!formatMetadata.isEnable()) {
            return;
        }
        PrintRowUtils.add(printRows, new Section()
                .addText(storeName, formatMetadata.toFont())
                .setAlign(Text.Align.Center));
        this.addBlankRow();
    }

    /**
     * 单据类型
     *
     * @param invoiceType
     * @param formatMetadata
     */
    public void addInvoiceType(String invoiceType, FormatMetadata formatMetadata) {
        if (!formatMetadata.isEnable()) {
            return;
        }
        PrintRowUtils.add(printRows, new Section()
                .addText(invoiceType, formatMetadata.toFont())
                .setAlign(Text.Align.Center));
        this.addBlankRow();
    }

    /**
     * 整单备注
     *
     * @param orderRemark
     * @param formatMetadata
     */
    public void addOrderRemark(String orderRemark, FormatMetadata formatMetadata) {
        if (!formatMetadata.isEnable()) {
            return;
        }
        if (!StringUtils.hasText(orderRemark)) {
            return;
        }
        PrintRowUtils.add(printRows, new Section().addText(MultiLangUtils.get("order_remark") + orderRemark,
                formatMetadata.toFont()));
        addOrderRemarkNoSeparator(orderRemark, formatMetadata);
        this.addSeparator();
    }

    /**
     * 整单备注
     *
     * @param orderRemark    备注数据
     * @param formatMetadata 格式
     */
    public void addOrderRemarkNoSeparator(String orderRemark, FormatMetadata formatMetadata) {
        if (formatMetadata.isEnable() && StringUtils.hasText(orderRemark)) {
            PrintRowUtils.add(printRows, new Section().addText(MultiLangUtils.get("order_remark") + orderRemark,
                    formatMetadata.toFont()));
        }
    }

    /**
     * 小票订单提示
     *
     * @param formatMetadata
     */
    public void addOrderPrompt(FormatMetadata formatMetadata) {
        if (!formatMetadata.isEnable()) {
            return;
        }
        PrintRowUtils.add(printRows, new Section().addText(MultiLangUtils.get("receipt_note"), formatMetadata.toFont()));
        this.addSeparator();
    }

    /**
     * 转台详情
     *
     * @param srcTableName
     * @param destTableName
     * @param oriTableFormat
     * @param newTableFormat
     */
    public void addTurnTableDetail(String srcTableName, String destTableName,
                                   FormatMetadata oriTableFormat, FormatMetadata newTableFormat) {
        if (!oriTableFormat.isEnable()) {
            return;
        }
        Font oriTableFont = oriTableFormat.toFont();
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("original_table"), oriTableFont)
                .setValue(new Text(srcTableName, oriTableFont)));
        Font newTableFont = newTableFormat.toFont();
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("transferred_to"), newTableFont)
                .setValue(new Text(destTableName, newTableFont)));
        this.addSeparator();
    }

    /**
     * 订单（可配置）、人数
     *
     * @param pageSize
     * @param orderNo
     * @param orderNoFormat
     * @param personNumber
     * @param personNumberFormat
     */
    @RequestMapping
    public void addMarkOrTableNoAndOrderAndGuest(int pageSize, int tradeMode,
                                                 String markOrTableNo, FormatMetadata markOrTableNoFormat,
                                                 String orderNo, FormatMetadata orderNoFormat,
                                                 Integer personNumber, FormatMetadata personNumberFormat) {
        if (!markOrTableNoFormat.isEnable() && !orderNoFormat.isEnable() && !personNumberFormat.isEnable()) {
            return;
        }

        // 启用排号，添加排号section
        if (markOrTableNoFormat.isEnable() && Objects.nonNull(markOrTableNo)) {
            String markName = TradeModeUtils.getMarkName(tradeMode);
            PrintRowUtils.add(printRows, new Section(markName + markOrTableNo, markOrTableNoFormat.toFont()));
        }

        handleItemRows(pageSize, orderNo, orderNoFormat, personNumber, personNumberFormat);
    }

    private void handleItemRows(int pageSize, String orderNo,
                                FormatMetadata orderNoFormat,
                                Integer personNumber,
                                FormatMetadata personNumberFormat) {
        boolean printOrderNo = orderNoFormat.isEnable() && Objects.nonNull(orderNo);
        boolean printPersonNumber = personNumberFormat.isEnable() && Objects.nonNull(personNumber);
        if (80 == pageSize || (Objects.nonNull(orderNo) && orderNo.length() < 16)) {
            // 启动单号和人数
            if (printOrderNo && printPersonNumber) {
                PrintRowUtils.add(printRows, new KeyValue()
                        .setKeyString(MultiLangUtils.get(Constant.ORDER_NUMBER) + orderNo, orderNoFormat.toFont())
                        .setValueString(MultiLangUtils.get(Constant.CUSTOMER_NUMBER) + personNumber, personNumberFormat.toFont()));
            } else if (printOrderNo) { // 仅启用单号
                PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.ORDER_NUMBER) + orderNo, orderNoFormat.toFont()));
            } else if (printPersonNumber) {
                PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.CUSTOMER_NUMBER) + personNumber, personNumberFormat.toFont())
                        .setAlign(Text.Align.Right));
            }
        } else {
            if (printOrderNo) {
                PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.ORDER_NUMBER) + orderNo, orderNoFormat.toFont()));
            }
            if (printPersonNumber) {
                PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.CUSTOMER_NUMBER) + personNumber, personNumberFormat.toFont()));
            }
        }
    }

    /**
     * 开台时间
     *
     * @param openTableTime
     * @param formatMetadata
     */
    public void addOpenTableTime(Long openTableTime, FormatMetadata formatMetadata) {
        if (!formatMetadata.isEnable()) {
            return;
        }
        String time = DateTimeUtils.mills2String(openTableTime, MD_PATTERN);
        PrintRowUtils.add(printRows, new Section(MultiLangUtils.get("open_table_time_short") + time, formatMetadata.toFont()));
    }

    /**
     * 开台、结帐时间
     *
     * @param pageSize
     * @param tradeMode
     * @param openTableTime
     * @param openTableFormat
     * @param checkoutTime
     * @param checkoutFormat
     */
    public void addOpenAndCheckTime(int pageSize, int tradeMode,
                                    Long openTableTime, FormatMetadata openTableFormat,
                                    Long checkoutTime, FormatMetadata checkoutFormat) {
        if (!openTableFormat.isEnable()) {
            return;
        }
        String openTableTimeStr = DateTimeUtils.mills2String(openTableTime, MD_PATTERN);
        String checkoutTimeStr = DateTimeUtils.mills2String(checkoutTime, MD_PATTERN);
        String openTableTimeDesc = TradeModeUtils.getCoStartName(tradeMode) + openTableTimeStr;
        String checkoutTimeDesc = MultiLangUtils.get("checkout") + checkoutTimeStr;
        if (80 == pageSize) {
            if (openTableFormat.isEnable() && checkoutFormat.isEnable()) {
                PrintRowUtils.add(printRows, new KeyValue()
                        .setKeyString(openTableTimeDesc, openTableFormat.toFont())
                        .setValueString(checkoutTimeDesc, checkoutFormat.toFont()));
            } else if (openTableFormat.isEnable()) {
                PrintRowUtils.add(printRows, new Section(openTableTimeDesc, openTableFormat.toFont()));
            } else {
                PrintRowUtils.add(printRows, new Section(checkoutTimeDesc, checkoutFormat.toFont(), Text.Align.Right));
            }
        } else {
            if (openTableFormat.isEnable()) {
                PrintRowUtils.add(printRows, new Section(openTableTimeDesc, openTableFormat.toFont()));
            }
            if (checkoutFormat.isEnable()) {
                PrintRowUtils.add(printRows, new Section(checkoutTimeDesc, checkoutFormat.toFont()));
            }
        }
    }

    /**
     * 商品总额
     *
     * @param total
     */
    public void addTotalMoney(BigDecimal total) {
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString("商品总额")
                .setValueString(BigDecimalUtils.moneyTrimmedString(total)));
    }

    /**
     * 并台合计金额
     *
     * @param total
     */
    public void addCombineTotalMoney(BigDecimal total) {
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("total_amt_merged_tables"))
                .setValueString(BigDecimalUtils.moneyTrimmedString(total)));
        this.addSeparator();
    }

    /**
     * 附加费
     *
     * @param additionalChargeList
     * @param additionalFeeDetail
     * @param additionalFeeTotal
     */
    public void addAdditionalCharge(List<AdditionalCharge> additionalChargeList,
                                    FormatMetadata additionalFeeDetail, FormatMetadata additionalFeeTotal) {
        if (!CollectionUtils.isEmpty(additionalChargeList)) {
            BigDecimal chargeValueSum = additionalChargeList.stream()
                    .map(AdditionalCharge::getChargeValue)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);
            if (chargeValueSum.compareTo(BigDecimal.ZERO) > 0) {
                if (additionalFeeDetail.isEnable()) {
                    Font font = additionalFeeDetail.toFont();
                    additionalChargeList.stream()
                            .filter(additionalCharge -> additionalCharge.getChargeValue() != null
                                    && additionalCharge.getChargeValue().compareTo(BigDecimal.ZERO) > 0)
                            .forEach(additionalCharge -> PrintRowUtils.add(printRows, new KeyValue()
                                    .setKeyString(additionalCharge.getChargeName(), font)
                                    .setValueString(
                                            BigDecimalUtils.moneyTrimmedString(additionalCharge.getChargeValue()),
                                            font
                                    )));
                }
                if (additionalFeeTotal.isEnable()) {
                    PrintRowUtils.add(printRows, new KeyValue()
                            .setKeyString("附加费合计")
                            .setValueString(BigDecimalUtils.moneyTrimmedString(chargeValueSum), additionalFeeTotal.toFont()));
                }
                this.addSeparator();
            }
        }
    }

    /**
     * 优惠金额
     *
     * @param reduceRecordList
     */
    public void addReduceRecordAndPayable(List<ReduceRecord> reduceRecordList,
                                          FormatMetadata listFormat, FormatMetadata totalFormat,
                                          BigDecimal payable, FormatMetadata payableFormat,
                                          BigDecimal originalPrice,
                                          FormatMetadata originalPriceFormat) {
        if (!listFormat.isEnable() && !totalFormat.isEnable() && !payableFormat.isEnable()) {
            return;
        }
        if (!CollectionUtils.isEmpty(reduceRecordList)) {
            BigDecimal reduceSum = reduceRecordList.stream()
                    .map(ReduceRecord::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);
            if (reduceSum.compareTo(BigDecimal.ZERO) != 0) {
                if (listFormat.isEnable()) {
                    Font font = listFormat.toFont();
                    reduceRecordList.stream()
                            .filter(reduce -> reduce.getAmount() != null
                                    && reduce.getAmount().compareTo(BigDecimal.ZERO) != 0)
                            .forEach(reduce -> PrintRowUtils.add(printRows, new KeyValue()
                                    .setKeyString(DiscountTypeEnum.getLocaleDescByName(reduce.getName()), font)
                                    .setValueString(BigDecimalUtils.moneyTrimmedString(reduce.getAmount()), font)));
                }
                if (totalFormat.isEnable()) {
                    Font font = totalFormat.toFont();
                    PrintRowUtils.add(printRows, new KeyValue()
                            .setKeyString(MultiLangUtils.get("promotion_total"), font)
                            .setValueString(BigDecimalUtils.moneyTrimmedString(reduceSum), font));
                }
            }
        }

        //优惠差异金额
        if (Objects.nonNull(originalPrice)
                && Objects.nonNull(originalPriceFormat)
                && originalPriceFormat.isEnable()) {
            Font font = originalPriceFormat.toFont();
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(MultiLangUtils.get(Constant.REFUND_DISCOUNTS_TOTAL), font)
                    .setValueString(BigDecimalUtils.moneyTrimmedString(originalPrice), font));
        }

        if (payableFormat.isEnable()) {
            Font font = payableFormat.toFont();
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(MultiLangUtils.get("total_due"), font)
                    .setValueString(BigDecimalUtils.moneyTrimmedString(payable), font));
        }
        this.addSeparator();
    }

    /**
     * 支付方式
     *
     * @param payRecords
     * @param payRecordsFormat
     */
    public void addPayRecordAndActuallyPay(List<PayRecord> payRecords, FormatMetadata payRecordsFormat,
                                           BigDecimal changedPay, FormatMetadata payChangedFormat,
                                           BigDecimal actuallyPay, FormatMetadata actuallyPayFormat) {
        if (!payRecordsFormat.isEnable() && !payChangedFormat.isEnable() && !actuallyPayFormat.isEnable()) {
            return;
        }

        if (!CollectionUtils.isEmpty(payRecords) && payRecordsFormat.isEnable()) {
            Font font = payRecordsFormat.toFont();
            payRecords.forEach(payRecord -> PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(PaymentTypeEnum.PaymentType.getLocaleName(payRecord.getPayName()), font)
                    .setValueString(BigDecimalUtils.moneyTrimmedString(payRecord.getAmount()), font))
            );
        }
        if (payChangedFormat.isEnable()) {
            // 找零金额大于0才展示
            if (changedPay.compareTo(BigDecimal.ZERO) > 0) {
                Font font = payChangedFormat.toFont();
                PrintRowUtils.add(printRows, new KeyValue()
                        .setKeyString(MultiLangUtils.get("change"), font)
                        .setValueString(BigDecimalUtils.moneyTrimmedString(changedPay), font));
            }
        }
        if (actuallyPayFormat.isEnable()) {
            Font font = actuallyPayFormat.toFont();
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(MultiLangUtils.get("paid"), font)
                    .setValueString(BigDecimalUtils.moneyTrimmedString(actuallyPay), font));
        }
        this.addSeparator();
    }

    /**
     * 支付方式
     */
    public void addPrePayRecordAndActuallyPay(List<PayRecord> payRecords, FormatMetadata payRecordsFormat, BigDecimal payAble,
                                              BigDecimal actuallyPay, FormatMetadata actuallyPayFormat) {
        if (CollectionUtils.isEmpty(payRecords)) {
            return;
        }
        Font font = payRecordsFormat.toFont();
        payRecords.forEach(payRecord -> PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(PaymentTypeEnum.PaymentType.getLocaleName(payRecord.getPayName()), font)
                .setValueString(BigDecimalUtils.moneyTrimmedString(payRecord.getAmount()), font))
        );
        Font actuallyPayFormatFontfont = actuallyPayFormat.toFont();
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("paid"), actuallyPayFormatFontfont)
                .setValueString(BigDecimalUtils.moneyTrimmedString(actuallyPay), actuallyPayFormatFontfont));
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("un_paid"), actuallyPayFormatFontfont)
                .setValueString(BigDecimalUtils.moneyTrimmedString(payAble.subtract(actuallyPay)), actuallyPayFormatFontfont));
        this.addSeparator();
    }

    /**
     * 操作员、打印时间
     *
     * @param pageSize
     * @param operatorStaffName
     * @param createTime
     */
    public void addOpStaffAndPrintTime(int pageSize,
                                       String operatorStaffName, FormatMetadata opStaffFormat,
                                       Long createTime, FormatMetadata createTimeFormat) {
        if (!opStaffFormat.isEnable() && !createTimeFormat.isEnable()) {
            return;
        }
        if (80 == pageSize) {
            if (createTimeFormat.isEnable()) {
                String createTimeStr = DateTimeUtils.mills2String(createTime, MD_PATTERN);
                if (opStaffFormat.isEnable()) {
                    PrintRowUtils.add(printRows, new KeyValue()
                            .setKeyString(MultiLangUtils.get(Constant.OPERATOR) + operatorStaffName, opStaffFormat.toFont())
                            .setValueString(MultiLangUtils.get(Constant.PRINT_TIME) + createTimeStr, createTimeFormat.toFont()));
                } else {
                    PrintRowUtils.add(printRows, new Section(
                            MultiLangUtils.get(Constant.PRINT_TIME) + createTimeStr,
                            createTimeFormat.toFont(), Text.Align.Right
                    ));
                }
            } else {
                PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.OPERATOR) + operatorStaffName, opStaffFormat.toFont()));
            }
        } else {
            if (opStaffFormat.isEnable()) {
                PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.OPERATOR) + operatorStaffName, opStaffFormat.toFont()));
            }
            if (createTimeFormat.isEnable()) {
                String createTimeStr = DateTimeUtils.mills2String(createTime, MD_PATTERN);
                PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.PRINT_TIME) + createTimeStr, createTimeFormat.toFont()));
            }
        }
    }

    /**
     * 操作员、下单时间
     *
     * @param pageSize
     * @param operatorStaffName
     * @param openTableTime
     */
    public void addOpStaffAndOpenTableTimeAndPrintTime(int pageSize,
                                                       String operatorStaffName, FormatMetadata opStaffFormat,
                                                       Long openTableTime, FormatMetadata openTableTimeFormat,
                                                       Long createTime, FormatMetadata createTimeFormat,
                                                       FormatMetadata yearMonthDayFormat) {
        addOpStaffAndSomeTimeAndPrintTime(pageSize, operatorStaffName, opStaffFormat,
                MultiLangUtils.get("open_table_time"), openTableTime, openTableTimeFormat, createTime, createTimeFormat,
                yearMonthDayFormat);
    }

    /**
     * 操作员、下单时间、打印时间
     *
     * @param pageSize
     * @param operatorStaffName
     * @param opStaffFormat
     * @param orderTime
     * @param orderTimeFormat
     * @param printTime
     * @param printTimeFormat
     */
    public void addOpStaffAndOrderTimeAndPrintTime(int pageSize,
                                                   String operatorStaffName, FormatMetadata opStaffFormat,
                                                   Long orderTime, FormatMetadata orderTimeFormat,
                                                   Long printTime, FormatMetadata printTimeFormat) {
        addOpStaffAndSomeTimeAndPrintTime(pageSize, operatorStaffName, opStaffFormat,
                MultiLangUtils.get("order_time"), orderTime, orderTimeFormat, printTime, printTimeFormat, null);
    }

    /**
     * 操作员、转台时间、打印时间
     *
     * @param pageSize
     * @param operatorStaffName
     * @param turnTime
     */
    public void addOpStaffAndTurnTimeAndPrintTime(int pageSize,
                                                  String operatorStaffName, FormatMetadata opStaffFormat,
                                                  Long turnTime, FormatMetadata turnTimeFormat,
                                                  Long createTime, FormatMetadata createTimeFormat) {
        addOpStaffAndSomeTimeAndPrintTime(pageSize, operatorStaffName, opStaffFormat,
                MultiLangUtils.get("transfer_time"), turnTime, turnTimeFormat, createTime, createTimeFormat, null);
    }

    /**
     * 操作员、充值时间、打印时间
     *
     * @param pageSize
     * @param operatorStaffName
     * @param rechargeTime
     */
    public void addOpStaffAndRechargeTimeAndPrintTime(int pageSize,
                                                      String operatorStaffName, FormatMetadata opStaffFormat,
                                                      Long rechargeTime, FormatMetadata rechargeTimeFormat,
                                                      Long createTime, FormatMetadata createTimeFormat) {
        addOpStaffAndSomeTimeAndPrintTime(pageSize, operatorStaffName, opStaffFormat,
                MultiLangUtils.get("recharge_time"), rechargeTime, rechargeTimeFormat, createTime, createTimeFormat, null);
    }

    /**
     * 操作员、Some时间、打印时间
     *
     * @param pageSize
     * @param operatorStaffName
     * @param someTime
     */
    @RequestMapping
    public void addOpStaffAndSomeTimeAndPrintTime(int pageSize,
                                                  String operatorStaffName, FormatMetadata opStaffFormat,
                                                  String someTimeDesc, Long someTime, FormatMetadata someTimeFormat,
                                                  Long createTime, FormatMetadata createTimeFormat,
                                                  FormatMetadata yearMonthDayFormat) {
        if (!opStaffFormat.isEnable() && !someTimeFormat.isEnable() && !createTimeFormat.isEnable()) {
            return;
        }
        String pattern;
        if (Objects.nonNull(yearMonthDayFormat) && yearMonthDayFormat.isEnable()) {
            pattern = YMD_PATTERN;
        } else {
            pattern = MD_PATTERN;
        }
        if (80 == pageSize) {
            if (someTimeFormat.isEnable()) {
                if (Objects.nonNull(someTime)) {
                    String openTableTimeStr = DateTimeUtils.mills2String(someTime, pattern);
                    if (opStaffFormat.isEnable()) {
                        PrintRowUtils.add(printRows, new KeyValue()
                                .setKeyString(MultiLangUtils.get(Constant.OPERATOR) + operatorStaffName, opStaffFormat.toFont())
                                .setValueString(someTimeDesc + openTableTimeStr, someTimeFormat.toFont()));
                    } else {
                        PrintRowUtils.add(printRows, new Section(
                                someTimeDesc + openTableTimeStr,
                                someTimeFormat.toFont()
                        ).setAlign(Text.Align.Right));
                    }
                }
            } else {
                PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.OPERATOR) + operatorStaffName, opStaffFormat.toFont()));
            }
            if (createTimeFormat.isEnable()) {
                String createTimeStr = DateTimeUtils.mills2String(createTime, pattern);
                PrintRowUtils.add(printRows, new Section(
                        MultiLangUtils.get(Constant.PRT_TIME) + createTimeStr, createTimeFormat.toFont()
                ).setAlign(Text.Align.Right));
            }
        } else {
            if (opStaffFormat.isEnable()) {
                PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.OPERATOR) + operatorStaffName, opStaffFormat.toFont()));
            }
            if (someTimeFormat.isEnable() && Objects.nonNull(someTime)) {
                String openTableTimeStr = DateTimeUtils.mills2String(someTime, pattern);
                PrintRowUtils.add(printRows, new Section(someTimeDesc + openTableTimeStr, someTimeFormat.toFont()));
            }
            if (createTimeFormat.isEnable()) {
                String createTimeStr = DateTimeUtils.mills2String(createTime, pattern);
                PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.PRT_TIME) + createTimeStr, createTimeFormat.toFont()));
            }
        }
    }

    /**
     * 会员姓名
     */
    public void addMemberName(String memberName, FormatMetadata memberNameFormat) {
        if (memberNameFormat.isEnable() && !StringUtils.isEmpty(memberName)) {
            // 会员姓名
            Font font = memberNameFormat.toFont();
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(MultiLangUtils.get("member_name"), font)
                    .setValueString(StringPrintUtils.protectedName(memberName), font));
        }
    }

    public void addMemberPhone(String memberPhone, FormatMetadata memberPhoneFormat) {
        if (memberPhoneFormat.isEnable() && !StringUtils.isEmpty(memberPhone) && memberPhone.length() == 11) {
            // 会员姓名
            Font font = memberPhoneFormat.toFont();
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(MultiLangUtils.get("member_phone"), font)
                    .setValueString(memberPhone, font));
        }
    }

    /**
     * 支付卡余额
     */
    public void addMemberCardBalance(BigDecimal memberCardBalance, FormatMetadata memberCardBalanceFormat) {
        if (memberCardBalanceFormat.isEnable() && Objects.nonNull(memberCardBalance)) {
            // 当前余额
            Font font = memberCardBalanceFormat.toFont();
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(MultiLangUtils.get("current_balance1"), font)
                    .setValueString(BigDecimalUtils.moneyTrimmedString(memberCardBalance), font));
        }
    }

    /**
     * 会员消费信息
     */
    public void addMemberCardBalance(FormatMetadata memberCardBalanceType, FormatMetadata memberCardBalanceFormat,
                                     List<MultiMemberPayRecord> multiMemberPayRecords) {
        if (CollectionUtils.isEmpty(multiMemberPayRecords)) {
            return;
        }
        for (MultiMemberPayRecord multiMemberPayRecord : multiMemberPayRecords) {
            // 消费类型
            if (memberCardBalanceType.isEnable()) {
                addMemberCardBalanceType(multiMemberPayRecord, memberCardBalanceType);
            }
            // 消费余额
            if (memberCardBalanceFormat.isEnable()) {
                addMemberCardBalance(multiMemberPayRecord, memberCardBalanceFormat);
            }
        }
    }

    /**
     * 本次消费余额类型
     */
    public void addMemberCardBalanceType(MultiMemberPayRecord multiMemberPayRecord, FormatMetadata memberCardBalanceType) {
        BigDecimal rechargeAmount = multiMemberPayRecord.getRechargeAmount();
        BigDecimal giftAmount = multiMemberPayRecord.getGiftAmount();
        BigDecimal subsidyAmount = multiMemberPayRecord.getSubsidyAmount();
        Font font = memberCardBalanceType.toFont();
        if (Objects.nonNull(rechargeAmount) && rechargeAmount.compareTo(BigDecimal.ZERO) > 0) {
            // 实充消费金额
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(MultiLangUtils.get("member_recharge_amount"), font)
                    .setValueString(BigDecimalUtils.moneyTrimmedString(rechargeAmount), font));
        }
        if (Objects.nonNull(giftAmount) && giftAmount.compareTo(BigDecimal.ZERO) > 0) {
            // 赠送消费金额
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(MultiLangUtils.get("member_gift_amount"), font)
                    .setValueString(BigDecimalUtils.moneyTrimmedString(giftAmount), font));
        }
        if (Objects.nonNull(subsidyAmount) && subsidyAmount.compareTo(BigDecimal.ZERO) > 0) {
            // 补贴消费金额
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(MultiLangUtils.get("member_subsidy_amount"), font)
                    .setValueString(BigDecimalUtils.moneyTrimmedString(subsidyAmount), font));
        }
    }

    /**
     * 本次消费余额
     */
    public void addMemberCardBalance(MultiMemberPayRecord multiMemberPayRecord, FormatMetadata memberCardBalanceFormat) {
        BigDecimal memberCardBalance = multiMemberPayRecord.getMemberCardBalance();
        String memberCardNum = multiMemberPayRecord.getMemberCardNum();
        if (Objects.nonNull(memberCardBalance) && memberCardBalance.compareTo(BigDecimal.ZERO) > 0) {
            // 尾号
            String lastCardNum = memberCardNum.length() > 4 ? memberCardNum.substring(memberCardNum.length() - 4) : memberCardNum;
            // 当前余额
            Font memberCardBalanceFormatFont = memberCardBalanceFormat.toFont();
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(MultiLangUtils.get("current_balance1")
                            + " (" + MultiLangUtils.get("card_num") + lastCardNum + ")", memberCardBalanceFormatFont)
                    .setValueString(BigDecimalUtils.moneyTrimmedString(memberCardBalance), memberCardBalanceFormatFont));
        }
    }


    /**
     * 挂账还款信息
     */
    public void addDebtInfo(String debtUnitName, String debtContactName, String debtContactTel,
                            FormatMetadata debtUnitNameFormat, FormatMetadata debtContactNameFormat, FormatMetadata debtContactTelFormat) {
        if (debtUnitNameFormat.isEnable() && !StringUtils.isEmpty(debtUnitName)) {
            // 挂账单位名称
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(MultiLangUtils.get("debt_unit_name"), debtUnitNameFormat.toFont())
                    .setValueString(debtUnitName, debtUnitNameFormat.toFont()));
        }
        if (debtContactNameFormat.isEnable() && !StringUtils.isEmpty(debtContactName)) {
            // 挂账联系人
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(MultiLangUtils.get("debt_contact_name"), debtContactNameFormat.toFont())
                    .setValueString(StringPrintUtils.protectedName(debtContactName), debtContactNameFormat.toFont()));
        }
        if (debtContactTelFormat.isEnable() && !StringUtils.isEmpty(debtContactTel)) {
            // 挂账单位电话
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(MultiLangUtils.get("debt_contact_tel"), debtContactTelFormat.toFont())
                    .setValueString(debtContactTel, debtContactTelFormat.toFont()));
        }
    }


    /**
     * 扫码开票
     */
    public void addInvoiceCodeName(FormatMetadata memberNameFormat) {
        if (memberNameFormat.isEnable()) {
            // 扫码开票
            Font font = memberNameFormat.toFont();
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(MultiLangUtils.get("invoice_code_name"), font)
                    .setValueString(StringPrintUtils.protectedName("扫码开票"), font));
        }
    }

    /**
     * 操作员、下单时间
     */
    public void addOpStaffAndOpenTableTimeAndCheckTimeAndPrintTime(PrintCheckOutContentBO content) {
        if (!content.isEnable()) {
            return;
        }

        String openTableTimeDesc = TradeModeUtils.getCoStartName(content.getTradeMode());

        if (80 == content.getPageSize()) {
            addTimeInfoFor80PageSize(content, openTableTimeDesc);
        } else {
            addTimeInfoForOtherPageSize(content, openTableTimeDesc);
        }
    }

    private void addTimeInfoFor80PageSize(PrintCheckOutContentBO content, String openTableTimeDesc) {
        // 处理开台时间和操作员信息
        if (content.openTableTimeEnable()) {
            addOpenTableAndOperatorInfo(content, openTableTimeDesc);
        } else {
            addOperatorInfo(content);
        }

        // 处理创建时间和结账时间
        if (content.createTimeEnable()) {
            addCreateAndCheckoutTime(content);
        } else {
            addCheckoutTimeOnly(content);
        }
    }

    private void addTimeInfoForOtherPageSize(PrintCheckOutContentBO content, String openTableTimeDesc) {
        if (content.opStaffIsEnable()) {
            addOperatorInfo(content);
        }

        if (content.openTableTimeEnable()) {
            addOpenTableTime(content, openTableTimeDesc);
        }

        if (content.checkOutTimeEnable()) {
            addCheckoutTimeOnly(content);
            addPrintTimeInfo(content);
        }
    }

    private void addOpenTableAndOperatorInfo(PrintCheckOutContentBO content, String openTableTimeDesc) {
        if (content.opStaffIsEnable() && content.isTimeIsYMD()) {
            PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.OPERATOR) + content.getOperatorStaffName(),
                    content.getOpStaffFormat().toFont()));
            PrintRowUtils.add(printRows, new Section(openTableTimeDesc + content.getOpenTableTimeStr(),
                    content.getOpenTableTimeFormat().toFont()));
            return;
        }
        if (content.opStaffIsEnable()) {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(MultiLangUtils.get(Constant.OPERATOR) + content.getOperatorStaffName(),
                            content.getOpStaffFormat().toFont())
                    .setValueString(openTableTimeDesc + content.getOpenTableTimeStr(),
                            content.getOpenTableTimeFormat().toFont()));
            return;
        }
        PrintRowUtils.add(printRows, new Section(
                openTableTimeDesc + content.getOpenTableTimeStr(),
                content.getOpenTableTimeFormat().toFont(),
                Text.Align.Right));

    }

    private void addOperatorInfo(PrintCheckOutContentBO content) {
        PrintRowUtils.add(printRows, new Section(
                MultiLangUtils.get(Constant.OPERATOR) + content.getOperatorStaffName(),
                content.getOpStaffFormat().toFont()));
    }

    private void addCreateAndCheckoutTime(PrintCheckOutContentBO content) {
        if (content.checkOutTimeEnable() && content.isTimeIsYMD()) {
            PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.CHECKOUT_TIME) + content.getCheckOutTimeStr(),
                    content.getCheckOutTimeFormat().toFont()));
            PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.PRT_TIME) + content.getCreateTimeStr(),
                    content.getCreateTimeFormat().toFont()));
            return;
        }
        if (content.checkOutTimeEnable()) {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(MultiLangUtils.get(Constant.CHECKOUT_TIME) + content.getCheckOutTimeStr(),
                            content.getCheckOutTimeFormat().toFont())
                    .setValueString(MultiLangUtils.get(Constant.PRT_TIME) + content.getCreateTimeStr(),
                            content.getCreateTimeFormat().toFont()));
            return;
        }
        PrintRowUtils.add(printRows, new Section(
                MultiLangUtils.get(Constant.PRT_TIME) + content.getCreateTimeStr(),
                content.getCreateTimeFormat().toFont(),
                Text.Align.Right));
    }

    private void addCheckoutTimeOnly(PrintCheckOutContentBO content) {
        PrintRowUtils.add(printRows, new Section(
                MultiLangUtils.get(Constant.CHECKOUT_TIME) + content.getCheckOutTimeStr(),
                content.getCheckOutTimeFormat().toFont()));
    }

    private void addOpenTableTime(PrintCheckOutContentBO content, String openTableTimeDesc) {
        PrintRowUtils.add(printRows, new Section(
                openTableTimeDesc + content.getOpenTableTimeStr(),
                content.getOpenTableTimeFormat().toFont()));
    }

    private void addPrintTimeInfo(PrintCheckOutContentBO content) {
        PrintRowUtils.add(printRows, new Section(
                MultiLangUtils.get(Constant.PRT_TIME) + content.getCreateTimeStr(),
                content.getCreateTimeFormat().toFont()));
    }

    /**
     * 操作员、打印时间
     *
     * @param pageSize
     * @param createTime
     */
    public void addPrintTime(int pageSize, Long createTime, FormatMetadata createTimeFormat) {
        if (!createTimeFormat.isEnable()) {
            return;
        }
        String createTimeStr = DateTimeUtils.mills2String(createTime, MD_PATTERN);
        if (80 == pageSize) {
            PrintRowUtils.add(printRows, new Section("打印：" + createTimeStr).setAlign(Text.Align.Right));
        } else {
            PrintRowUtils.add(printRows, new Section("打印：" + createTimeStr));
        }
    }

    /**
     * 下单时间，结账单打印时间
     *
     * @param pageSize
     * @param orderTime
     * @param createTime
     */
    public void addOrderTimeAndPrintTime(int pageSize, long orderTime, long createTime) {
        if (80 == pageSize) {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString("下单：" + DateTimeUtils.mills2String(orderTime, MD_PATTERN))
                    .setValueString("打印：" + DateTimeUtils.mills2String(createTime, MD_PATTERN)));
        } else {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKeyString("下单：")
                    .setValueString(DateTimeUtils.mills2String(orderTime, MD_PATTERN)));
            PrintRowUtils.add(printRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKeyString("打印：")
                    .setValueString(DateTimeUtils.mills2String(createTime, MD_PATTERN)));
        }
    }

    /**
     * 转台时间
     *
     * @param turnTime
     * @param formatMetadata
     */
    public void addTurnTableTime(Long turnTime, FormatMetadata formatMetadata) {
        if (!formatMetadata.isEnable()) {
            return;
        }
        String turnTimeStr = DateTimeUtils.mills2String(turnTime, "yyyy-MM-dd HH:mm");
        PrintRowUtils.add(printRows, new Section("转台时间：" + turnTimeStr).setAlign(Text.Align.Left));
    }

    /**
     * 牌号/桌号
     *
     * @param markOrTableNo
     * @param formatMetadata
     */
    public void addMarkOrTableNo(String markOrTableNo, FormatMetadata formatMetadata) {
        if (!formatMetadata.isEnable()) {
            return;
        }
        this.addSeparator();
        PrintRowUtils.add(printRows, new Section()
                .addText(markOrTableNo, formatMetadata.toFont())
                .setAlign(Text.Align.Left));
    }

    /**
     * 牌号/桌号
     *
     * @param markOrTableNo  桌号
     * @param formatMetadata 桌号格式
     * @param tradeMode      交易类型
     */
    public void addMarkOrTableNoNew(String markOrTableNo, FormatMetadata formatMetadata, Integer tradeMode) {
        if (!formatMetadata.isEnable()) {
            return;
        }
        // 启用排号，添加排号section
        if (Objects.nonNull(markOrTableNo)) {
            String markName = TradeModeUtils.getMarkName(tradeMode);
            PrintRowUtils.add(printRows, new Section(markName + markOrTableNo, formatMetadata.toFont()));
        }
    }

    /**
     * 订单号（不可配置）、人数
     *
     * @param pageSize
     * @param orderNo
     * @param personNumber
     * @param personNumberFormat
     */
    public void addPresentOrderAndOptionalGuest(int pageSize, String orderNo,
                                                Integer personNumber, FormatMetadata personNumberFormat) {
        if (80 == pageSize || orderNo.length() < 16) {
            if (!personNumberFormat.isEnable()) {
                PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.ORDER_NUMBER) + orderNo));
            } else {
                PrintRowUtils.add(printRows, new KeyValue()
                        .setKeyString(MultiLangUtils.get(Constant.ORDER_NUMBER) + orderNo)
                        .setValueString(MultiLangUtils.get(Constant.CUSTOMER_NUMBER) + personNumber));
            }
        } else {
            PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.ORDER_NUMBER) + orderNo));
            if (personNumberFormat.isEnable()) {
                PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.CUSTOMER_NUMBER) + personNumber));
            }
        }
    }

    /**
     * 订单（可配置）、人数
     *
     * @param pageSize
     * @param orderNo
     * @param orderNoFormat
     * @param personNumber
     * @param personNumberFormat
     */
    public void addOptionalOrderAndOptionalGuest(int pageSize, String orderNo, FormatMetadata orderNoFormat,
                                                 Integer personNumber, FormatMetadata personNumberFormat) {
        if (!orderNoFormat.isEnable() && !personNumberFormat.isEnable()) {
            return;
        }
        if (80 == pageSize || orderNo.length() < 16) {
            if (orderNoFormat.isEnable() && personNumberFormat.isEnable()) {
                PrintRowUtils.add(printRows, new KeyValue()
                        .setKeyString(MultiLangUtils.get(Constant.ORDER_NUMBER) + orderNo)
                        .setValueString(MultiLangUtils.get(Constant.CUSTOMER_NUMBER) + personNumber));
            } else if (orderNoFormat.isEnable()) {
                PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.ORDER_NUMBER) + orderNo));
            } else {
                PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.CUSTOMER_NUMBER) + personNumber).setAlign(Text.Align.Right));
            }
        } else {
            PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.ORDER_NUMBER) + orderNo));
            if (personNumberFormat.isEnable()) {
                PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.CUSTOMER_NUMBER) + personNumber));
            }
        }
    }

    /**
     * 应付金额
     *
     * @param payable
     * @param formatMetadata
     */
    public void addPayable(BigDecimal payable, FormatMetadata formatMetadata) {
        if (!formatMetadata.isEnable()) {
            return;
        }
        PrintRowUtils.add(printRows, new Separator());
        Font font = formatMetadata.toFont();
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString("应付金额", font)
                .setValueString(BigDecimalUtils.moneyTrimmedString(payable), font));

    }

    /**
     * 实付金额
     *
     * @param actuallyPay
     * @param formatMetadata
     */
    public void addActuallyPay(BigDecimal actuallyPay, FormatMetadata formatMetadata) {
        if (!formatMetadata.isEnable()) return;
        PrintRowUtils.add(printRows, new Separator());
        Font font = formatMetadata.toFont();
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString("实付金额", font)
                .setValueString(BigDecimalUtils.moneyTrimmedString(actuallyPay), font));

    }

    /**
     * 门店地址、电话
     *
     * @param storeAddress
     * @param storeAddressFormat
     * @param storeTel
     * @param storeTelFormat
     */
    public void addStoreAddressAndTel(String storeAddress, FormatMetadata storeAddressFormat,
                                      String storeTel, FormatMetadata storeTelFormat) {
        if (!storeAddressFormat.isEnable() && !storeTelFormat.isEnable()) {
            return;
        }
        PrintRowUtils.add(printRows, new Separator());
        if (storeAddressFormat.isEnable()) {
            String address = StringUtils.isEmpty(storeAddress) ? "无" : storeAddress;
            PrintRowUtils.add(printRows, new Section("门店地址：" + address));
        }
        if (storeTelFormat.isEnable()) {
            String tel = StringUtils.isEmpty(storeTel) ? "无" : storeTel;
            PrintRowUtils.add(printRows, new Section("门店电话：" + tel));
        }
    }

    public void addSeparator() {
        PrintRowUtils.add(printRows, new Separator());
    }

    public void addBlankRow() {
        PrintRowUtils.add(printRows, new BlankRow());
    }

    public void addSection(Section section) {
        PrintRowUtils.add(printRows, section);
    }

    public void addKeyValue(KeyValue keyValue) {
        PrintRowUtils.add(printRows, keyValue);
    }

    public void addImage(Image image) {
        PrintRowUtils.add(printRows, image);
    }

    public void addAll(List<PrintRow> printRows) {
        this.printRows.addAll(printRows);
    }

    private void addCustom(CustomMetadata customMetadata) {
        // 文本、图片、条码、二维码
        switch (customMetadata.getType()) {
            // 文本
            case 0:
                extracted0(customMetadata);
                break;

            // 图形
            case 1:
                extracted1(customMetadata);
                break;

            // 条码
            case 2:
                extracted2(customMetadata);
                break;

            // 二维码
            case 3:
                extracted3(customMetadata);
                break;

            default:
                throw new BusinessException("不支持当前自定义元素");

        }
    }

    private void extracted3(CustomMetadata customMetadata) {
        PrintRowUtils.add(printRows, new QrCode(customMetadata.getText()));
    }

    private void extracted2(CustomMetadata customMetadata) {
        PrintRowUtils.add(printRows, new BarCode(customMetadata.getText()));
    }

    private void extracted1(CustomMetadata customMetadata) {
        PrintRowUtils.add(printRows, new Image(customMetadata.getText()));
    }

    private void extracted0(CustomMetadata customMetadata) {
        PrintRowUtils.add(printRows, new Section()
                .addText(customMetadata.getText(), customMetadata.toFont())
                .setAlign(Text.Align.Center));
    }

    public void addTableItem(List<PrintItemRecord> itemRecordList, BigDecimal total, int pageSize, ItemTableFormatBO itemTableFormatBO) {
        printRows.addAll(PrintTableUtils.resolveTableItemRow(itemRecordList, total, pageSize, itemTableFormatBO, null));
    }

    public void addTableAdditionalCharge(List<AdditionalCharge> additionalChargeList, int pageSize, ItemTableFormatBO itemTableFormatBo) {
        if (!CollectionUtils.isEmpty(additionalChargeList)) {
            this.addSeparator();
            BigDecimal chargeValueSum = additionalChargeList.stream()
                    .map(AdditionalCharge::getChargeValue)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);
            if (chargeValueSum.compareTo(BigDecimal.ZERO) > 0) {
                boolean additionalCharge = Optional.ofNullable(itemTableFormatBo)
                        .map(ItemTableFormatBO::getAdditionalCharge)
                        .map(FormatMetadata::isEnable).orElse(false);
                boolean additionalChargeTotal = Optional.ofNullable(itemTableFormatBo)
                        .map(ItemTableFormatBO::getAdditionalChargeTotal)
                        .map(FormatMetadata::isEnable).orElse(false);
                if (additionalCharge) {
                    // 附加费明细
                    printRows.addAll(PrintTableUtils.resolveTableAdditionalChargeRow(additionalChargeList, pageSize, itemTableFormatBo));
                }
                if (additionalChargeTotal) {
                    // 附加费合计
                    PrintRowUtils.add(printRows, new KeyValue()
                            .setKeyString(MultiLangUtils.get("surcharge_total"), itemTableFormatBo.getAdditionalChargeTotal().toFont())
                            .setValueString(BigDecimalUtils.moneyTrimmedString(chargeValueSum), itemTableFormatBo.getAdditionalChargeTotal().toFont()));
                }
                this.addSeparator();
            }
        }
    }

    public boolean addHasTableAdditionalCharge(List<AdditionalCharge> additionalChargeList, ItemTableFormatBO itemTableFormatBo) {
        if (CollectionUtils.isEmpty(additionalChargeList)) {
            return false;
        }
        BigDecimal chargeValueSum = additionalChargeList.stream()
                .map(AdditionalCharge::getChargeValue)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
        if (chargeValueSum.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        boolean additionalCharge = Optional.ofNullable(itemTableFormatBo.getAdditionalCharge())
                .map(FormatMetadata::isEnable).orElse(false);
        boolean additionalChargeTotal = Optional.ofNullable(itemTableFormatBo.getAdditionalChargeTotal())
                .map(FormatMetadata::isEnable).orElse(false);
        if (!additionalCharge && !additionalChargeTotal) {
            return false;
        }
        this.addSeparator();
        if (additionalCharge) {
            // 附加费明细
            additionalChargeList.forEach(item -> PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(item.getChargeName(), itemTableFormatBo.getAdditionalCharge().toFont())
                    .setValueString(BigDecimalUtils.moneyTrimmedString(item.getChargeValue()), itemTableFormatBo.getAdditionalCharge().toFont())));
        }
        if (additionalChargeTotal) {
            // 附加费合计
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(MultiLangUtils.get("surcharge_total"), itemTableFormatBo.getAdditionalChargeTotal().toFont())
                    .setValueString(BigDecimalUtils.moneyTrimmedString(chargeValueSum), itemTableFormatBo.getAdditionalChargeTotal().toFont()));
        }
        this.addSeparator();
        return true;
    }

    public void addQrCode(String qrCodeUrl, FormatMetadata qrCodeFormat) {
        if (qrCodeUrl == null || !qrCodeFormat.isEnable()) {
            return;
        }
        CustomMetadata customMetadata = new CustomMetadata();
        customMetadata.setType(3);
        customMetadata.setText(qrCodeUrl);
        addCustom(customMetadata);
    }

    public void addInvoiceQrCode(String qrCodeUrl,
                                 String invoiceAmount,
                                 FormatMetadata invoiceAmountFormat,
                                 FormatMetadata qrCodeFormat,
                                 FormatMetadata invoiceCodeFormat,
                                 FormatMetadata invoiceMarkedFormat) {
        this.addSeparator();

        if (qrCodeUrl == null || !qrCodeFormat.isEnable()) {
            return;
        }

        if (invoiceCodeFormat.isEnable()) {
            // 扫码开票
            PrintRowUtils.add(printRows, new Section()
                    .addText("扫码开票", invoiceCodeFormat.toFont())
                    .setAlign(Text.Align.Center));
        }

        if (Objects.nonNull(invoiceAmount)) {
            // 开票金额
            PrintRowUtils.add(printRows, new Section()
                    .addText(invoiceAmount, invoiceAmountFormat.toFont())
                    .setAlign(Text.Align.Center));
        }

        CustomMetadata customMetadata = new CustomMetadata();
        customMetadata.setType(3);
        customMetadata.setText(qrCodeUrl);
        addCustom(customMetadata);

        this.addSeparator();

        if (invoiceMarkedFormat.isEnable()) {
            // 扫码开票
            PrintRowUtils.add(printRows, new Section()
                    .addText("若扫码开票失败请联系商家协助解决", invoiceMarkedFormat.toFont())
                    .setAlign(Text.Align.Center));
        }
    }

    /**
     * 外卖单
     * 平台名称
     */
    public void appendTakeoutPlatform(String platform, String platformOrder, Boolean cancelFlag, FormatMetadata platformMetaData) {
        String cancelOrderShow = Boolean.TRUE.equals(cancelFlag) ? MultiLangUtils.get("order_cancel") : Strings.EMPTY;
        PrintRowUtils.add(printRows, new Section()
                .addText(PrintContentEnum.getLocale(platform) + cancelOrderShow + platformOrder, platformMetaData.toFont())
                .setAlign(Text.Align.Center));
    }

    /**
     * 外卖单
     * 门店名称
     */
    public void appendTakeoutStoreName(String storeName, FormatMetadata storeNameMetaData) {
        if (!storeNameMetaData.isEnable()) {
            return;
        }
        PrintRowUtils.add(printRows, new Section()
                .addText(storeName, storeNameMetaData.toFont())
                .setAlign(Text.Align.Center));
    }

    /**
     * 外卖单
     * 支付情况
     */
    public void appendTakeoutPayMsg(String payMsg, FormatMetadata payMsgMetaData) {
        if (!payMsgMetaData.isEnable()) {
            return;
        }
        PrintRowUtils.add(printRows, new Section()
                .addText("--" + PrintContentEnum.getLocale(payMsg) + "--", payMsgMetaData.toFont())
                .setAlign(Text.Align.Center));
    }

    /**
     * 异常单信息
     */
    public void appendTakeoutAbnormal() {
        PrintRowUtils.add(printRows, new BlankRow());
        // 外卖打印异常说明
        PrintRowUtils.add(printRows, new ReverseText(MultiLangUtils.get("takeout_print_warning"), Font.NORMAL_BOLD, Text.Align.Center));
        PrintRowUtils.add(printRows, new BlankRow());
        // 外卖打印异常菜品说明
        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get("cannot_printed_kitchen"))
                .setAlign(Text.Align.Left));
    }

    /**
     * 外卖单
     * 期望送达时间
     * 不可取消
     */
    public void appendTakeoutExpectedDeliveryTime(Boolean reserve, String expectTime, FormatMetadata expectTimeMetaData) {
        String reservePrefix = Boolean.TRUE.equals(reserve) ? MultiLangUtils.get("reserve") : Strings.EMPTY;
        PrintRowUtils.add(printRows, new Section().addText(MultiLangUtils.get("expected_delivery_time")
                + reservePrefix + PrintContentEnum.getLocale(expectTime), expectTimeMetaData.toFont()));
    }

    /**
     * 外卖单
     * 下单时间
     */
    public void appendTakeoutOrderTime(Long orderTime, FormatMetadata orderTimeMetaData) {
        if (!orderTimeMetaData.isEnable()) {
            return;
        }
        Font font = orderTimeMetaData.toFont();
        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKey(new Text(MultiLangUtils.get("order_time"), font))
                .setValue(new Text(DateTimeUtils.mills2String(orderTime), font)));
    }


    /**
     * 发票税号
     * 固定不控制
     */
    public void appendTakeoutInvoice(Boolean invoiced, String invoiceTitle, String taxpayerId) {
        if (Optional.ofNullable(invoiced).orElse(false)) {
            // 抬头
            if (StringUtils.hasText(invoiceTitle)) {
                PrintRowUtils.add(printRows, new Section().addText(MultiLangUtils.get("bill_looked_up")
                        + invoiceTitle, Font.SMALL_NORMAL_THIN));
            }
            // 税号
            if (StringUtils.hasText(taxpayerId)) {
                PrintRowUtils.add(printRows, new Section().addText(MultiLangUtils.get("duty_paragraph")
                        + taxpayerId, Font.SMALL_NORMAL_THIN));
            }
        } else {
            // 不要发票信息
            PrintRowUtils.add(printRows, new Section().addText(MultiLangUtils.get("no_invoice_required"),
                    Font.SMALL_NORMAL_THIN));
        }
    }

    /**
     * 备注 (订单备注/餐具备注)
     * 不可取消
     */
    public void appendTakeoutRemark(String remark, String dinnersNumber, FormatMetadata remarkMetadata) {
        Font font = remarkMetadata.toFont();
        if (StringUtils.hasText(remark)) {
            String remarkMessage;
            log.info("订单备注，敏感词过滤替换之前。备注信息：" + remark);
            try {
                //订单备注，敏感词过滤替换
                remarkMessage = SensitiveUtils.replaceWord(remark);
            } catch (Exception e) {
                remarkMessage = remark;
                log.error("订单备注，敏感词过滤替换报错：" + e.getMessage());
            }
            log.info("订单备注，敏感词过滤替换之后。备注信息：" + remarkMessage);
            PrintRowUtils.add(printRows, new Separator());
            PrintRowUtils.add(printRows, new Section()
                    .addText(MultiLangUtils.get("remark") + remarkMessage, font));
        }
        if (StringUtils.hasText(dinnersNumber)) {
            PrintRowUtils.add(printRows, new Section()
                    .addText(MultiLangUtils.get("tableware") + dinnersNumber, font));
        }
    }

    /**
     * 外卖单
     * 商品列表
     */
    public void appendTakeoutItemList(int pageSize, List<PrintItemRecord> itemRecordList,
                                      ItemTableFormatBO itemTableFormatBO, String fullGiftRemark) {
        if (CollectionUtils.isEmpty(itemRecordList)) {
            return;
        }
        itemRecordList.forEach(item -> {
            item.setInvoiceType(InvoiceTypeEnum.TAKEOUT.getType());
            item.setCartId(item.getCartId() == null ? 0 : item.getCartId() + 1);
        });
        // 根据袋子分类
        Map<Integer, List<PrintItemRecord>> collectByCart = itemRecordList.stream().collect(Collectors.groupingBy(PrintItemRecord::getCartId));
        collectByCart.forEach((cartId, printItemRecord) -> printRows.addAll(PrintTableUtils.resolveTableItemRow(
                printItemRecord, null, pageSize, itemTableFormatBO, getCartSeparator(cartId))));
        // 满赠
        appendFullGift(fullGiftRemark, itemTableFormatBO.getLayout());
        PrintRowUtils.add(printRows, new Separator());
    }

    /**
     * 外卖单
     * 商品总额 + 餐盒费
     */
    public void appendTakeoutOrderTotalAmount(List<AdditionalCharge> additionalChargeList,
                                              BigDecimal itemTotalPrice, FormatMetadata itemSumTotalMetadata,
                                              FormatMetadata packageTotalMetadata, FormatMetadata shipTotalMetadata) {
        if (!itemSumTotalMetadata.isEnable() && !packageTotalMetadata.isEnable() && !shipTotalMetadata.isEnable()) {
            return;
        }
        // 商品总额
        if (itemSumTotalMetadata.isEnable()) {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKey(new Text(MultiLangUtils.get("item_total_amount"), itemSumTotalMetadata.toFont()))
                    .setValue(new Text(BigDecimalUtils.moneyTrimmedString(itemTotalPrice), itemSumTotalMetadata.toFont())));
        }
        // 附加费 (餐盒费/配送费)
        appendAdditionalCharge(additionalChargeList, packageTotalMetadata, shipTotalMetadata);
        // -------------------------------------------
        PrintRowUtils.add(printRows, new Separator());
    }

    /**
     * 外卖单
     * 商品原价 + 优惠明细 + 实付金额
     */
    public void appendTakeoutOrderAmount(BigDecimal originalPrice, BigDecimal actuallyPay, List<ReduceRecord> reduceRecordList,
                                         FormatMetadata originalPriceMetadata, FormatMetadata actuallyPayMetadata, FormatMetadata discountMetadata) {
        // 原价
        if (originalPriceMetadata.isEnable()) {
            PrintRowUtils.add(printRows, new Section()
                    .setAlign(Text.Align.Left)
                    .addText(MultiLangUtils.get("original_price")
                            + BigDecimalUtils.moneyTrimmedString(originalPrice), originalPriceMetadata.toFont()));
        }
        // 优惠明细 (折扣明细)
        if (discountMetadata.isEnable() && !CollectionUtils.isEmpty(reduceRecordList)) {
            for (ReduceRecord reduceRecord : reduceRecordList) {
                if (reduceRecord.getAmount().compareTo(BigDecimal.ZERO) != 0) {
                    PrintRowUtils.add(printRows, new Section()
                            .setAlign(Text.Align.Left)
                            .addText(reduceRecord.getName() + "："
                                    + BigDecimalUtils.moneyTrimmedString(reduceRecord.getAmount()), discountMetadata.toFont()));
                }
            }
        }
        // 实付
        if (actuallyPayMetadata.isEnable()) {
            PrintRowUtils.add(printRows, new Section()
                    .setAlign(Text.Align.Left)
                    .addText(MultiLangUtils.get("user_online_payment")
                            + BigDecimalUtils.moneyTrimmedString(actuallyPay), actuallyPayMetadata.toFont()));
        }
        // -------------------------------------------
        PrintRowUtils.add(printRows, new Separator());
    }

    /**
     * 附加费 (餐盒费/配送费)
     */
    private void appendAdditionalCharge(List<AdditionalCharge> additionalChargeList,
                                        FormatMetadata packageTotalMetadata, FormatMetadata shipTotalMetadata) {
        if (CollectionUtils.isEmpty(additionalChargeList)) {
            return;
        }
        Font packageTotalFont = packageTotalMetadata.toFont();
        Font shipTotalFont = shipTotalMetadata.toFont();
        additionalChargeList = additionalChargeList.stream()
                .filter(e -> e.getChargeValue().compareTo(BigDecimal.ZERO) != 0)
                .collect(Collectors.toList());
        for (AdditionalCharge additionalCharge : additionalChargeList) {
            if (PrintContentEnum.TAKEOUT_MEAL_BOX_FEE.getMessage().equals(additionalCharge.getChargeName())) {
                if (packageTotalMetadata.isEnable()) {
                    PrintRowUtils.add(printRows, new KeyValue()
                            .setKey(new Text(PrintContentEnum.getLocale(additionalCharge.getChargeName()) + "：", packageTotalFont))
                            .setValue(new Text(BigDecimalUtils.moneyTrimmedString(additionalCharge.getChargeValue()), packageTotalFont)));
                }
            } else if (PrintContentEnum.TAKEOUT_DELIVERY_FEE.getMessage().equals(additionalCharge.getChargeName())) {
                if (shipTotalMetadata.isEnable()) {
                    PrintRowUtils.add(printRows, new KeyValue()
                            .setKey(new Text(PrintContentEnum.getLocale(additionalCharge.getChargeName()) + "：", shipTotalFont))
                            .setValue(new Text(BigDecimalUtils.moneyTrimmedString(additionalCharge.getChargeValue()), shipTotalFont)));
                }
            } else {
                PrintRowUtils.add(printRows, new KeyValue()
                        .setKeyString(PrintContentEnum.getLocale(additionalCharge.getChargeName()) + "：", Font.SMALL)
                        .setValueString(BigDecimalUtils.moneyTrimmedString(additionalCharge.getChargeValue()), Font.SMALL));
            }
        }
    }

    /**
     * 外卖单
     * 顾客信息
     */
    public void appendTakeoutCustomerInfo(String platform, String receiverName, String receiverTel, String privacyPhone,
                                          String receiverAddress, String recipientAddressDesensitization,
                                          FormatMetadata customerMetadata) {
        if (!customerMetadata.isEnable()) {
            return;
        }
        Font font = customerMetadata.toFont();
        // 客户姓名
        PrintRowUtils.add(printRows, new Section(MultiLangUtils.get("name") + receiverName, font));

        // 客户电话
        appendReceiverTel(receiverTel, font);

        // 客户隐私号
        appendPrivacyPhone(privacyPhone, font);

        // 客户地址 (隐私地址)
        appendReceiverAddress(platform, receiverAddress, recipientAddressDesensitization, font);
    }

    /**
     * 外卖单
     * 客户电话
     */
    private void appendReceiverTel(String receiverTel, Font font) {
        if (StringUtils.hasText(receiverTel)) {
            List<String> receiverTelList = JacksonUtils.toObjectList(String.class, receiverTel);
            if (receiverTelList.size() == 1) {
                PrintRowUtils.add(printRows, new Section(String.format(MultiLangUtils.get("customer_phone_number"), "")
                        + receiverTelList.get(0), font));
            } else {
                for (int i = 0; i < receiverTelList.size(); i++) {
                    PrintRowUtils.add(printRows, new Section(String.format(MultiLangUtils.get("customer_phone_number"), i + 1)
                            + receiverTelList.get(i), font));
                }
            }
        }
    }

    /**
     * 外卖单
     * 客户隐私号
     */
    private void appendPrivacyPhone(String privacyPhone, Font font) {
        if (StringUtils.hasText(privacyPhone)) {
            List<String> privacyPhoneList = JacksonUtils.toObjectList(String.class, privacyPhone);
            if (privacyPhoneList.size() == 1) {
                String[] temp = privacyPhoneList.get(0).split("&");
                privacyPhone = temp.length == 1 ? temp[0] : temp[0] + MultiLangUtils.get("transfer") + temp[1];
                PrintRowUtils.add(printRows, new Section(String.format(MultiLangUtils.get("virtual_phone"), "")
                        + privacyPhone, font));
            } else {
                for (int i = 0; i < privacyPhoneList.size(); i++) {
                    String[] temp = privacyPhoneList.get(i).split("&");
                    privacyPhone = temp.length == 1 ? temp[0] : temp[0] + MultiLangUtils.get("transfer") + temp[1];
                    PrintRowUtils.add(printRows, new Section(String.format(MultiLangUtils.get("virtual_phone"), i + 1)
                            + privacyPhone, font));
                }
            }
        }
    }


    /**
     * 外卖单
     * 客户地址 (隐私地址)
     */
    private void appendReceiverAddress(String platform, String receiverAddress, String recipientAddressDesensitization, Font font) {
        boolean containsSymbol = !StringUtils.isEmpty(receiverAddress) && receiverAddress.contains("@#");
        if (OrderType.TakeoutSubType.MT_TAKEOUT.getDesc().equals(platform)) {
            if (containsSymbol) {
                String[] mtAddressList = receiverAddress.split("@#");
                receiverAddress = mtAddressList[0];
            }
            if (!StringUtils.isEmpty(recipientAddressDesensitization)) {
                receiverAddress = recipientAddressDesensitization;
            }
        } else if (OrderType.TakeoutSubType.TCD_TAKEOUT.getDesc().equals(platform) && containsSymbol) {
            String[] mtAddressList = receiverAddress.split("@#");
            receiverAddress = mtAddressList[1];
        }
        PrintRowUtils.add(printRows, new Section(MultiLangUtils.get("address") + receiverAddress, font));
    }

    /**
     * 外卖单
     * 打印条形码
     */
    public void appendTakeoutOrderNoBarCode(String orderNo, FormatMetadata orderNoMetadata, FormatMetadata orderNoBarCodeMetadata) {
        // 所有小票都打印条形码
        if (!orderNoMetadata.isEnable() && !orderNoBarCodeMetadata.isEnable()) {
            return;
        }
        PrintRowUtils.add(printRows, new Separator());
        // 订单条形码
        if (orderNoBarCodeMetadata.isEnable()) {
            PrintRowUtils.add(printRows, new BarCode((orderNo)));
        }
        // 订单号
        if (orderNoMetadata.isEnable()) {
            PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.ORDER_NUMBER) +
                    orderNo, orderNoMetadata.toFont()).setAlign(Text.Align.Center));
        }
    }

    /**
     * 外卖单
     * 结账操作员，结账单打印时间
     */
    public void appendTakeoutOpStaffAndPrintTime(int pageSize, String operatorStaffName, Long createTime,
                                                 FormatMetadata operatorMetadata, FormatMetadata printTimeMetadata) {
        if (!operatorMetadata.isEnable() && !printTimeMetadata.isEnable()) {
            return;
        }
        // -------------------------------------------
        PrintRowUtils.add(printRows, new Separator());
        Font operationFont = operatorMetadata.toFont();
        Font printTimeFont = printTimeMetadata.toFont();
        if (80 == pageSize) {
            if (operatorMetadata.isEnable() && printTimeMetadata.isEnable()) {
                PrintRowUtils.add(printRows, new KeyValue()
                        .setKey(new Text(MultiLangUtils.get(Constant.OPERATOR) + operatorStaffName, operationFont))
                        .setValue(new Text(MultiLangUtils.get(Constant.PRINT_TIME)
                                + DateTimeUtils.mills2String(createTime, MD_PATTERN), printTimeFont)));
            }
            if (operatorMetadata.isEnable() && !printTimeMetadata.isEnable()) {
                PrintRowUtils.add(printRows, new KeyValue()
                        .setKey(new Text(MultiLangUtils.get(Constant.OPERATOR) + operatorStaffName, operationFont))
                        .setValue(new Text(Strings.EMPTY, printTimeFont)));
            }
            if (!operatorMetadata.isEnable() && printTimeMetadata.isEnable()) {
                PrintRowUtils.add(printRows, new KeyValue()
                        .setKey(new Text(Strings.EMPTY, operationFont))
                        .setValue(new Text(MultiLangUtils.get(Constant.PRINT_TIME)
                                + DateTimeUtils.mills2String(createTime, MD_PATTERN), printTimeFont)));
            }
            return;
        }
        if (operatorMetadata.isEnable()) {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKey(new Text(MultiLangUtils.get(Constant.OPERATOR), operationFont))
                    .setValue(new Text(operatorStaffName, operationFont)));
        }
        if (printTimeMetadata.isEnable()) {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKey(new Text(MultiLangUtils.get(Constant.PRINT_TIME), printTimeFont))
                    .setValue(new Text(DateTimeUtils.mills2String(createTime, MD_PATTERN), printTimeFont)));
        }
    }


    /**
     * 外卖单
     * 外卖安心卡
     */
    public void appendTakeoutAssuranceCard(int pageSize) {
        if (!WhitelistUtils.isWhitelist(UserContextUtils.getEnterpriseGuid())) {
            return;
        }
        try {
            PrintRowUtils.add(printRows, new Separator("*"));
            PrintRowUtils.add(printRows, new Section(MultiLangUtils.get("takeout_assurance_card"), Font.NORMAL_BOLD, Text.Align.Center));
            PrintRowUtils.add(printRows, new Separator());
            if (58 == pageSize) {
                PrintRowUtils.add(printRows, new Section(MultiLangUtils.get("maker") + Constant.BLANK + MultiLangUtils.get(Constant.BODY_TEMPERATURE) + Constant.SMALL_BLANK));
            } else {
                PrintRowUtils.add(printRows, new Section(MultiLangUtils.get("maker") + Constant.BIG_BLANK + MultiLangUtils.get(Constant.BODY_TEMPERATURE) + Constant.BLANK));
            }
            PrintRowUtils.add(printRows, new Separator());
            if (58 == pageSize) {
                PrintRowUtils.add(printRows, new Section(MultiLangUtils.get("packager") + Constant.BLANK + MultiLangUtils.get(Constant.BODY_TEMPERATURE) + Constant.SMALL_BLANK));
            } else {
                PrintRowUtils.add(printRows, new Section(MultiLangUtils.get("packager") + Constant.BIG_BLANK + MultiLangUtils.get(Constant.BODY_TEMPERATURE) + Constant.BLANK));
            }
            PrintRowUtils.add(printRows, new Separator());
            if (58 == pageSize) {
                PrintRowUtils.add(printRows, new Section(MultiLangUtils.get("delivery_driver") + "            " + MultiLangUtils.get(Constant.BODY_TEMPERATURE) + Constant.SMALL_BLANK));
            } else {
                PrintRowUtils.add(printRows, new Section(MultiLangUtils.get("delivery_driver") + "                    " + MultiLangUtils.get(Constant.BODY_TEMPERATURE) + Constant.BLANK));
            }
            PrintRowUtils.add(printRows, new Separator());
            PrintRowUtils.add(printRows, new Section(MultiLangUtils.get("safety_tip"), Font.SMALL, Text.Align.Center));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String getCartSeparator(Integer cartId) {
        if (cartId != null && cartId > 0) {
            return String.format(MultiLangUtils.get("pocket_number"), cartId);
        }
        return null;
    }

    public void appendFoodFinishBarCode(String orderNo, String foodFinishBarCode, FormatMetadata foodFinishBarCodeFormat) {
        // 打印出餐条形码
        if (!foodFinishBarCodeFormat.isEnable()) {
            return;
        }
        // 出餐条形码
        PrintRowUtils.add(printRows, new BarCode(foodFinishBarCode));
        // 出餐码
        PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.FOOD_FINISH_CODE) +
                "CC" + orderNo, foodFinishBarCodeFormat.toFont()).setAlign(Text.Align.Center));
    }

    public void addMemberPortrayal(MemberPortrayalDetailsDTO memberPortrayalDTO,
                                   FormatMetadata memberPortrayal) {
        if (!memberPortrayal.isEnable()) {
            return;
        }
        if (ObjectUtils.isEmpty(memberPortrayalDTO)) {
            return;
        }
        addBasicsFieldList(memberPortrayalDTO, memberPortrayal);
        addConsumeFieldList(memberPortrayalDTO, memberPortrayal);
        addRechargeFieldList(memberPortrayalDTO, memberPortrayal);
        this.addSeparator();
    }

    private void addRechargeFieldList(MemberPortrayalDetailsDTO memberPortrayalDTO, FormatMetadata memberPortrayal) {
        List<MemberPortrayalFieldDetailsDTO> rechargeFieldList = memberPortrayalDTO.getRechargeFieldList();
        if (!CollectionUtils.isEmpty(rechargeFieldList)) {
            rechargeFieldList.sort(Comparator.comparing(MemberPortrayalFieldDetailsDTO::getFieldSort));
            rechargeFieldList.forEach(rechargeField -> {
                MemberPortrayalFieldEnum fieldEnum = MemberPortrayalFieldEnum.getEnum(rechargeField.getField());
                switch (Objects.requireNonNull(fieldEnum)) {
                    case RECHARGE_COUNT:
                        addStatisticalPeriod(memberPortrayal, rechargeField,
                                MemberPortrayalFieldEnum.RECHARGE_COUNT.name(), UnitEnum.CI.getDesc());
                        break;
                    case RECHARGE_AMOUNT:
                        addStatisticalPeriod(memberPortrayal, rechargeField,
                                MemberPortrayalFieldEnum.RECHARGE_AMOUNT.name(), UnitEnum.YUAN.getDesc());
                        break;
                    case AVERAGE_RECHARGE_AMOUNT:
                        addStatisticalPeriod(memberPortrayal, rechargeField,
                                MemberPortrayalFieldEnum.AVERAGE_RECHARGE_AMOUNT.name(), UnitEnum.YUAN.getDesc());
                        break;
                    case LAST_RECHARGE_TIME:
                        PrintRowUtils.add(printRows, new Section().addText(MultiLangUtils.get(MemberPortrayalFieldEnum.LAST_RECHARGE_TIME.name())
                                + rechargeField.getFieldValue(), memberPortrayal.toFont()));
                        break;
                    default:
                        break;
                }
            });
        }
    }

    private void addConsumeFieldList(MemberPortrayalDetailsDTO memberPortrayalDTO, FormatMetadata memberPortrayal) {
        List<MemberPortrayalFieldDetailsDTO> consumeFieldList = memberPortrayalDTO.getConsumeFieldList();
        if (!CollectionUtils.isEmpty(consumeFieldList)) {
            consumeFieldList.sort(Comparator.comparing(MemberPortrayalFieldDetailsDTO::getFieldSort));
            consumeFieldList.forEach(consumeField -> {
                MemberPortrayalFieldEnum fieldEnum = MemberPortrayalFieldEnum.getEnum(consumeField.getField());
                switch (Objects.requireNonNull(fieldEnum)) {
                    case CONSUME_COUNT:
                        String consumeCount = assembleFieldName(consumeField, MemberPortrayalFieldEnum.CONSUME_COUNT.name())
                                + consumeField.getFieldValue() + UnitEnum.CI.getDesc();
                        PrintRowUtils.add(printRows, new Section().addText(consumeCount, memberPortrayal.toFont()));
                        break;
                    case CONSUME_AMOUNT:
                        addStatisticalPeriod(memberPortrayal, consumeField,
                                MemberPortrayalFieldEnum.CONSUME_AMOUNT.name(), UnitEnum.YUAN.getDesc());
                        break;
                    case GUEST_SINGLE_PRICE:
                        addStatisticalPeriod(memberPortrayal, consumeField,
                                MemberPortrayalFieldEnum.GUEST_SINGLE_PRICE.name(), UnitEnum.YUAN.getDesc());
                        break;
                    case LAST_CONSUME_TIME:
                        PrintRowUtils.add(printRows, new Section().addText(MultiLangUtils.get(MemberPortrayalFieldEnum.LAST_CONSUME_TIME.name())
                                + consumeField.getFieldValue(), memberPortrayal.toFont()));
                        break;
                    default:
                        break;
                }
            });
        }
    }

    private void addStatisticalPeriod(FormatMetadata memberPortrayal,
                                      MemberPortrayalFieldDetailsDTO field,
                                      String name,
                                      String unitDesc) {
        String amountLimit = "";
        if (!ObjectUtils.isEmpty(field.getIsSelectAmountLimit())
                && field.getIsSelectAmountLimit() == BooleanEnum.TRUE.getCode()) {
            amountLimit = String.format(Constant.GREATER_THAN, field.getAmountLimit());
        }
        StatisticalPeriodEnum periodEnum = StatisticalPeriodEnum.getEnum(field.getStatisticalPeriod());
        switch (Objects.requireNonNull(periodEnum)) {
            case REGISTERED_TO_NOW:
            case THIS_YEAR:
            case CURRENT_MONTH:
                String thisYear = assembleFieldName(field, name) + periodEnum.getDesc()
                        + field.getFieldValue() + unitDesc + amountLimit;
                PrintRowUtils.add(printRows, new Section().addText(thisYear, memberPortrayal.toFont()));
                break;
            case SOME_TIME_RECENTLY:
                String someTimeRecently = assembleFieldName(field, name)
                        + String.format(periodEnum.getDesc(), field.getRecentDays())
                        + field.getFieldValue() + unitDesc + amountLimit;
                PrintRowUtils.add(printRows, new Section().addText(someTimeRecently, memberPortrayal.toFont()));
                break;
            default:
                break;
        }
    }

    private String assembleFieldName(MemberPortrayalFieldDetailsDTO field, String name) {
        String fieldName = MultiLangUtils.get(name);
        if (!CollectionUtils.isEmpty(field.getCustomizeLabelList())) {
            for (CustomizeLabelDetails customizeLabel : field.getCustomizeLabelList()) {
                fieldName = judgeFieldName(field, customizeLabel, fieldName);
            }
        }
        return fieldName;
    }

    private static String judgeFieldName(MemberPortrayalFieldDetailsDTO field,
                                         CustomizeLabelDetails customizeLabel,
                                         String fieldName) {
        if (ObjectUtils.isEmpty(customizeLabel.getIncludeStartNum())
                && !ObjectUtils.isEmpty(customizeLabel.getIncludeEndNum())
                && (Integer.parseInt(field.getFieldValue()) <= customizeLabel.getIncludeEndNum())) {
            fieldName = customizeLabel.getLabelName() + Constant.COLON;
        }
        if (!ObjectUtils.isEmpty(customizeLabel.getIncludeStartNum())
                && !ObjectUtils.isEmpty(customizeLabel.getIncludeEndNum())
                && (Integer.parseInt(field.getFieldValue()) >= customizeLabel.getIncludeStartNum()
                && Integer.parseInt(field.getFieldValue()) <= customizeLabel.getIncludeEndNum())) {
            fieldName = customizeLabel.getLabelName() + Constant.COLON;
        }
        if (!ObjectUtils.isEmpty(customizeLabel.getIncludeStartNum())
                && ObjectUtils.isEmpty(customizeLabel.getIncludeEndNum())
                && (Integer.parseInt(field.getFieldValue()) >= customizeLabel.getIncludeStartNum())) {
            fieldName = customizeLabel.getLabelName() + Constant.COLON;
        }
        return fieldName;
    }

    private void addBasicsFieldList(MemberPortrayalDetailsDTO memberPortrayalDTO,
                                    FormatMetadata memberPortrayal) {
        List<MemberPortrayalFieldDetailsDTO> basicsFieldList = memberPortrayalDTO.getBasicsFieldList();
        if (!CollectionUtils.isEmpty(basicsFieldList)) {
            basicsFieldList.sort(Comparator.comparing(MemberPortrayalFieldDetailsDTO::getFieldSort));
            basicsFieldList.forEach(basicsField -> {
                MemberPortrayalFieldEnum fieldEnum = MemberPortrayalFieldEnum.getEnum(basicsField.getField());
                switch (Objects.requireNonNull(fieldEnum)) {
                    case GENDER:
                        PrintRowUtils.add(printRows, new Section().addText(MultiLangUtils.get(MemberPortrayalFieldEnum.GENDER.name())
                                + basicsField.getFieldValue(), memberPortrayal.toFont()));
                        break;
                    case BIRTHDAY:
                        PrintRowUtils.add(printRows, new Section().addText(MultiLangUtils.get(MemberPortrayalFieldEnum.BIRTHDAY.name())
                                + basicsField.getFieldValue(), memberPortrayal.toFont()));
                        break;
                    case REGISTER_TIME:
                        PrintRowUtils.add(printRows, new Section().addText(MultiLangUtils.get(MemberPortrayalFieldEnum.REGISTER_TIME.name())
                                + basicsField.getFieldValue(), memberPortrayal.toFont()));
                        break;
                    case MEMBER_GRADE:
                        PrintRowUtils.add(printRows, new Section().addText(MultiLangUtils.get(MemberPortrayalFieldEnum.MEMBER_GRADE.name())
                                + basicsField.getFieldValue(), memberPortrayal.toFont()));
                        break;
                    default:
                        break;
                }
            });
        }
    }

    public void appendFullGift(String fullGiftRemark, FormatMetadata itemLayout) {
        if (StringUtils.hasText(fullGiftRemark)) {
            PrintRowUtils.add(printRows, new Section()
                    .addText(fullGiftRemark, itemLayout.toFont()));
        }
    }

    /**
     * 添加操作员，单独一行
     *
     * @param operatorStaffName 操作员名称
     * @param operator          操作员格式
     */
    public void addOperatorOnly(String operatorStaffName, FormatMetadata operator) {
        PrintRowUtils.add(printRows, new Section(MultiLangUtils.get(Constant.OPERATOR) + operatorStaffName, operator.toFont()));
    }
}
