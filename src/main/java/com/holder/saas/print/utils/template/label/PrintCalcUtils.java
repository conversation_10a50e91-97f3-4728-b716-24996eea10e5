/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:PrintCalcUtils.java
 * Date:2019-12-4
 * Author:terry
 */

package com.holder.saas.print.utils.template.label;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintCalcUtils
 * @date 2018/02/14 09:00
 * @description ESC/POS坐标计算工具类
 * @program holder-saas-store-print
 */
public final class PrintCalcUtils {

    public static String getCutOffText(String src, int destLen) {
        StringBuilder sb = new StringBuilder(src);
        int i = getCharCountOfText(sb.toString());
        if (i > destLen) {
            do {
                sb.deleteCharAt(sb.length() - 1);
                i = getCharCountOfText(sb.toString());
            } while (i > destLen);
        }
        return sb.toString();
    }

    public static double getPointOfWidth(double width) {
        return width * 8;
    }

    public static double getPointOfText(String text, int pageSize, int xMultiple) {
        return getWidthOfText(text, pageSize, xMultiple) * 8;
    }

    public static double getWidthOfText(String text, int pageSize, int xMultiple) {
        return getCharCountOfText(text) * getWidthOfSingleChar(pageSize, xMultiple);
    }

    public static int getCharCountOfText(String text) {
        if (text == null) return 0;
        StringBuilder buffer = new StringBuilder(text);
        int length = 0;
        String everyChar;
        Charset charset = StandardCharsets.UTF_8;
        for (int i = 0; i < buffer.length(); i++) {
            everyChar = buffer.substring(i, i + 1);
            try {
                everyChar = new String(everyChar.getBytes(charset));
            } catch (Exception e) {
                e.printStackTrace();
            }
            length += everyChar.getBytes().length > 1 ? 2 : 1;
        }
        return length;
    }

    public static int getByteCountOfText(String text, int xMultiple) {
        return getCharCountOfText(text) * xMultiple;
    }

    public static double getPointOfSingleByte(int pageSize) {
        return getWidthOfSingleByte(pageSize) * 8;
    }

    public static double getWidthOfSingleByte(int pageSize) {
        return pageSize * 1.0 / getByteCountOfPage(pageSize);
    }

    public static int getByteCountOfPage(int pageSize) {
        if (80 == pageSize) return 48;
        if (58 == pageSize) return 32;
        if (40 == pageSize) return 24;
        if (30 == pageSize) return 18;
        return 48;
    }

    public static double getPointOfSingleChar(int pageSize, int xMultiple) {
        return getPointOfSingleByte(pageSize) * xMultiple;
    }

    public static double getWidthOfSingleChar(int pageSize, int xMultiple) {
        return getWidthOfSingleByte(pageSize) * xMultiple;
    }

    public static int getCharCountOfPage(int pageSize, int xMultiple) {
        return getByteCountOfPage(pageSize) / xMultiple;
    }
}
