/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:AbsPrintItemCalculator.java
 * Date:2019-12-5
 * Author:terry
 */

package com.holder.saas.print.utils.template.calculator.item;

import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;

/**
 * <AUTHOR>
 * @date 2019-12-05 下午4:54
 */
public abstract class PrintItemCommonCalculator implements PrintItemCalculator {

    private final Font componentFont;

    public PrintItemCommonCalculator(Font componentFont) {
        this.componentFont = componentFont;
    }

    @Override
    public Text getType(String text) {
        return convertToText(text);
    }

    @Override
    public Text getPropOrRemark(String text) {
        return convertToText(text);
    }

    @Override
    public KeyValue getDiscount(String key, String value) {
        return new KeyValue()
                .setKeyString(key, componentFont)
                .setValueString(value, componentFont);
    }

    @Override
    public KeyValue getType(String key, String value) {
        return new KeyValue()
                .setKeyString(key, componentFont)
                .setValueString(value, componentFont);
    }

    protected Text convertToText(String text) {
        return new Text(text, componentFont);
    }
}
