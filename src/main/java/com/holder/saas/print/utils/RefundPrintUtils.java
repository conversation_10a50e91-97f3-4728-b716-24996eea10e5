package com.holder.saas.print.utils;

import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.print.content.PrintRefundDTO;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 退款单打印工具类
 *
 * <AUTHOR>
 * @date 2025/07/01
 */
@Slf4j
public class RefundPrintUtils {

    /**
     * 创建模拟退款单数据
     * 用于测试和演示
     */
    public static PrintRefundDTO createMockRefundData() {
        PrintRefundDTO refundDTO = new PrintRefundDTO();
        
        // 基础信息
        refundDTO.setInvoiceType(61); // REFUND_INVOICE 的类型值
        refundDTO.setStoreName("花开仲夏（双桥店）");
        refundDTO.setMarkNo("A01");
        refundDTO.setOrderNo("XXX");
        refundDTO.setRefundTime(System.currentTimeMillis());
        refundDTO.setOperatorStaffName("XXX");
        refundDTO.setOperationTime(System.currentTimeMillis());
        refundDTO.setTradeMode(0);
        
        // 退款信息
        refundDTO.setRefundAmount(new BigDecimal("12.00"));
        refundDTO.setRefundMethodType("原路返回");
        refundDTO.setRefundMethod("聚合支付");
        refundDTO.setRefundReason("测试退款");

        // 退款商品列表
        List<PrintItemRecord> refundItems = new ArrayList<>();
        PrintItemRecord item = new PrintItemRecord();
        item.setItemGuid("item-guid-001");
        item.setItemName("夫妻肺片");
        item.setItemTypeGuid("type-guid-001");
        item.setPrice(new BigDecimal("12.00"));
        item.setNumber(new BigDecimal("1"));
        item.setProperty("微辣");
        item.setAsWeight(false);
        item.setAsPackage(false);
        item.setAsGift(false);
        refundItems.add(item);

        refundDTO.setItemRecordList(refundItems);
        
        return refundDTO;
    }

    /**
     * 验证退款单数据的完整性
     */
    public static boolean validateRefundData(PrintRefundDTO refundDTO) {
        if (refundDTO == null) {
            log.error("退款单数据为空");
            return false;
        }
        
        if (refundDTO.getStoreName() == null || refundDTO.getStoreName().trim().isEmpty()) {
            log.error("门店名称不能为空");
            return false;
        }
        
        if (refundDTO.getOrderNo() == null || refundDTO.getOrderNo().trim().isEmpty()) {
            log.error("订单号不能为空");
            return false;
        }
        
        if (refundDTO.getRefundAmount() == null || refundDTO.getRefundAmount().compareTo(BigDecimal.ZERO) <= 0) {
            log.error("退款金额必须大于0");
            return false;
        }
        
        if (refundDTO.getRefundMethod() == null || refundDTO.getRefundMethod().trim().isEmpty()) {
            log.error("退款方式不能为空");
            return false;
        }
        
        if (CollectionUtils.isEmpty(refundDTO.getItemRecordList())) {
            log.error("退款商品列表不能为空");
            return false;
        }

        // 验证退款商品
        for (PrintItemRecord item : refundDTO.getItemRecordList()) {
            if (item.getItemName() == null || item.getItemName().trim().isEmpty()) {
                log.error("商品名称不能为空");
                return false;
            }
            if (item.getPrice() == null || item.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
                log.error("商品单价必须大于0");
                return false;
            }
            if (item.getNumber() == null || item.getNumber().compareTo(BigDecimal.ZERO) <= 0) {
                log.error("商品数量必须大于0");
                return false;
            }
        }
        
        return true;
    }

    /**
     * 计算退款商品总金额
     */
    public static BigDecimal calculateTotalRefundAmount(List<PrintItemRecord> refundItems) {
        if (CollectionUtils.isEmpty(refundItems)) {
            return BigDecimal.ZERO;
        }

        return refundItems.stream()
                .map(item -> item.getPrice().multiply(item.getNumber()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 格式化时间显示
     */
    public static String formatTime(Long timestamp) {
        if (timestamp == null) {
            return "";
        }
        return DateTimeUtils.mills2String(timestamp, "yyyy-MM-dd HH:mm");
    }

    /**
     * 设置退款单的基础信息
     */
    public static void setRefundBaseInfo(PrintRefundDTO refundDTO, String storeGuid, String enterpriseGuid, 
                                       String printUid, String operatorGuid, String operatorName, 
                                       String deviceId) {
        if (refundDTO == null) {
            return;
        }
        
        refundDTO.setStoreGuid(storeGuid);
        refundDTO.setEnterpriseGuid(enterpriseGuid);
        refundDTO.setPrintUid(printUid);
        refundDTO.setOperatorStaffGuid(operatorGuid);
        refundDTO.setOperatorStaffName(operatorName);
        refundDTO.setDeviceId(deviceId);
        refundDTO.setCreateTime(DateTimeUtils.nowMillis());
        
        // 设置默认值
        if (refundDTO.getRefundTime() == null) {
            refundDTO.setRefundTime(System.currentTimeMillis());
        }
        if (refundDTO.getOperationTime() == null) {
            refundDTO.setOperationTime(System.currentTimeMillis());
        }
    }

    /**
     * 从订单数据创建退款商品列表
     * 这个方法可以根据实际业务需求来实现
     */
    public static List<PrintItemRecord> createRefundItemsFromOrder(String orderData) {
        // 这里应该根据实际的订单数据结构来解析
        // 目前返回一个示例数据
        List<PrintItemRecord> items = new ArrayList<>();

        PrintItemRecord item = new PrintItemRecord();
        item.setItemGuid("item-guid-sample");
        item.setItemName("示例商品");
        item.setItemTypeGuid("type-guid-sample");
        item.setPrice(new BigDecimal("10.00"));
        item.setNumber(new BigDecimal("1"));
        item.setAsWeight(false);
        item.setAsPackage(false);
        item.setAsGift(false);
        items.add(item);

        return items;
    }

    /**
     * 生成退款单的打印标识
     */
    public static String generateRefundPrintUid(String orderNo) {
        return "REFUND_" + orderNo + "_" + System.currentTimeMillis();
    }
}
