package com.holder.saas.print.utils.template.calculator.item;

import com.holder.saas.print.utils.MultiLangUtils;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 58无单价无小计 纸显示：商品、数量
 */
public class Print58Price0Total0Calculator extends PrintItemCommonCalculator {

    public Print58Price0Total0Calculator(Font componentFont) {
        super(componentFont);
    }

    @Override
    public List<Text> getColumnHeaderList() {
        return Stream.of(MultiLangUtils.get("item"), MultiLangUtils.get("quantity")).map(this::convertToText).collect(Collectors.toList());
    }

    @Override
    public List<Text> getColumnHeaderList(boolean isRefund) {
        return Stream.of(isRefund ? MultiLangUtils.get("item_refund") : MultiLangUtils.get("item"),
                        isRefund ? MultiLangUtils.get("quantity_refund") : MultiLangUtils.get("quantity"))
                .map(this::convertToText)
                .collect(Collectors.toList());
    }

    @Override
    public List<Integer> getColumnWidthList() {
        return Arrays.asList(26, 6);
    }

    @Override
    public List<Integer> getColumnWidthList(boolean isRefund) {
        // 退款商品表头字数要多一些，重新配置宽度
        return isRefund ? Arrays.asList(24, 8) : getColumnWidthList();
    }

    @Override
    public List<Boolean> getAlignRights() {
        return Arrays.asList(false, true);
    }

    @Override
    public List<Text> getItem(String item, String price, String quantity, String subTotal) {
        return Stream.of(item, quantity).map(this::convertToText).collect(Collectors.toList());
    }

    @Override
    public List<Text> getItemSingle(String item) {
        return Stream.of(item).map(this::convertToText).collect(Collectors.toList());
    }

    @Override
    public List<Text> getSubItem(String subItemName, String subItemNumber) {
        return Stream.of(subItemName, subItemNumber).map(this::convertToText).collect(Collectors.toList());
    }

    @Override
    public List<Text> getTotal(String numTotal, String moneyTotal, Font font) {
        return Stream.of(MultiLangUtils.get("item_price_total"), Optional.ofNullable(numTotal).orElse("s"))
                .map(s -> new Text(s, font)).collect(Collectors.toList());
    }
}
