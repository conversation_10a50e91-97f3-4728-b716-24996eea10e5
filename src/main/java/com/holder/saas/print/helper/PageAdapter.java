package com.holder.saas.print.helper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import com.holderzone.saas.store.dto.common.PageDTO;

import java.util.Collections;
import java.util.List;

public class PageAdapter<T> extends Page<T> implements IPage<T> {

    private static final long serialVersionUID = 8545996863226528798L;

    /**
     * 查询数据列表
     */
    private List<T> records = Collections.emptyList();

    /**
     * 总数，当 total 不为 0 时分页插件不会进行 count 查询
     */
    private long total = 0;

    /**
     * 每页显示条数，默认 10
     */
    private long size = 10;

    /**
     * 当前页
     */
    private long current = 1;

    /**
     * <p>
     * SQL 排序 ASC 数组
     * </p>
     */
    private String[] ascs;

    /**
     * <p>
     * SQL 排序 DESC 数组
     * </p>
     */
    private String[] descs;

    /**
     * <p>
     * 自动优化 COUNT SQL
     * </p>
     */
    private boolean optimizeCountSql = true;

    public PageAdapter() {
        // to do nothing
    }

    public PageAdapter(Page page) {
        this(page.getCurrentPage(), page.getPageSize());
    }

    public PageAdapter(PageDTO pageDTO) {
        this(pageDTO.getCurrentPage(), pageDTO.getPageSize());
    }

    public PageAdapter(BasePageDTO basePageDTO) {
        this(basePageDTO.getCurrentPage() == null ? 1 : basePageDTO.getCurrentPage(), basePageDTO.getPageSize() ==
                null ? 10 : basePageDTO.getPageSize());
    }

    public PageAdapter(IPage iPage, List<T> records) {
        this(iPage.getCurrent(), iPage.getSize(), iPage.getTotal());
        this.records = records;
    }

    /**
     * <p>
     * 分页构造函数
     * </p>
     *
     * @param current 当前页
     * @param size    每页显示条数
     */
    public PageAdapter(long current, long size) {
        this(current, size, 0);
    }

    public PageAdapter(long current, long size, long total) {
        if (current > 1) {
            this.current = current;
        }
        this.size = size;
        this.total = total;
    }

    /**
     * <p>
     * 是否存在上一页
     * </p>
     *
     * @return true / false
     */
    public boolean hasPrevious() {
        return this.current > 1;
    }

    /**
     * <p>
     * 是否存在下一页
     * </p>
     *
     * @return true / false
     */
    public boolean hasNext() {
        return this.current < this.getPages();
    }

    @Override
    @JsonIgnore
    public long getPages() {
        return IPage.super.getPages();
    }

    @Override
    @JsonIgnore
    public List<T> getRecords() {
        return this.records;
    }

    @Override
    @JsonIgnore
    public PageAdapter<T> setRecords(List<T> records) {
        this.records = records;
        return this;
    }

    @Override
    @JsonIgnore
    public long getTotal() {
        return this.total;
    }

    @Override
    @JsonIgnore
    public PageAdapter<T> setTotal(long total) {
        this.total = total;
        return this;
    }

    @Override
    @JsonIgnore
    public long getSize() {
        return this.size;
    }

    @Override
    @JsonIgnore
    public PageAdapter<T> setSize(long size) {
        this.size = size;
        return this;
    }

    @Override
    @JsonIgnore
    public long getCurrent() {
        return this.current;
    }

    @Override
    @JsonIgnore
    public PageAdapter<T> setCurrent(long current) {
        this.current = current;
        return this;
    }

    @Override
    public String[] ascs() {
        return ascs;
    }

    public PageAdapter<T> setAscs(List<String> ascs) {
        if (CollectionUtils.isNotEmpty(ascs)) {
            this.ascs = ascs.toArray(new String[0]);
        }
        return this;
    }


    /**
     * <p>
     * 升序
     * </p>
     *
     * @param ascs 多个升序字段
     */
    public PageAdapter<T> setAsc(String... ascs) {
        this.ascs = ascs;
        return this;
    }

    @Override
    public String[] descs() {
        return descs;
    }

    public PageAdapter<T> setDescs(List<String> descs) {
        if (CollectionUtils.isNotEmpty(descs)) {
            this.descs = descs.toArray(new String[0]);
        }
        return this;
    }

    /**
     * <p>
     * 降序
     * </p>
     *
     * @param descs 多个降序字段
     */
    public PageAdapter<T> setDesc(String... descs) {
        this.descs = descs;
        return this;
    }

    @Override
    public boolean optimizeCountSql() {
        return optimizeCountSql;
    }

    public PageAdapter<T> setOptimizeCountSql(boolean optimizeCountSql) {
        this.optimizeCountSql = optimizeCountSql;
        return this;
    }

    @Override
    public List<T> getData() {
        return records;
    }

    @Override
    public void setData(List<T> data) {
        this.records = data;
    }

    @Override
    public long getTotalCount() {
        return total;
    }

    @Override
    public void setTotalCount(long totalCount) {
        this.total = totalCount;
    }

    @Override
    public long getPageSize() {
        return size;
    }

    @Override
    public void setPageSize(long pageSize) {
        this.size = pageSize;
    }

    @Override
    public long getCurrentPage() {
        return current;
    }

    @Override
    public void setCurrentPage(long currentPage) {
        this.current = currentPage;
    }
}
