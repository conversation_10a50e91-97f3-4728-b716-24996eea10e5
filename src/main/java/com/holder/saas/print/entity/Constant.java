package com.holder.saas.print.entity;


/**
 * <AUTHOR>
 */
public final class Constant {

    private Constant() {
        throw new UnsupportedOperationException();
    }

    public static final String MARK_PREFIX = "#";

    public static final String MM_DD_HH_MM = "MM-dd HH:mm";

    public static final String TRANSACTIONS = "transactions";

    public static final String BLANK = "          ";

    public static final String SMALL_BLANK = "     ";

    public static final String BIG_BLANK = "                  ";

    public static final String BODY_TEMPERATURE = "body_temperature";

    public static final String ORDER_COUNT = "order_count";

    public static final String CONSUMERS = "consumers";

    public static final String PERSON = "person";

    public static final String NET_SALES = "net_sales";

    public static final String AVERAGE_ORDER_AMOUNT = "average_order_amount";

    public static final String ORDER_NUMBER = "order_number";

    public static final String FOOD_FINISH_CODE = "food_finish_code";

    public static final String CUSTOMER_NUMBER = "customer_number";

    public static final String OPERATOR = "operator";

    public static final String PRINT_TIME = "print_time";

    public static final String PRT_TIME = "prt_time";

    public static final String CHECKOUT_TIME = "checkout_time";

    public static final String ORDER_REMARK = "order_remark";

    public static final String MARK_NAME = "mark_name";

    public static final String RECEIPT_NOTE = "receipt_note";

    public static final String ORDER_TIME = "order_time";

    public static final String ITEM = "item";

    public static final String QUANTITY = "quantity";

    public static final String PRICE = "price";

    public static final String ITEM_SUM = "item_sum";

    public static final String SET = "set";

    public static final String WEIGHT = "weight";

    public static final String HANG = "hang";

    public static final String COMPL = "compl";

    public static final String ATTR = "attr";

    public static final String REMARK = "remark";

    public static final String REFUND_ITEM_INVOICE_HEADER = "refund_item_invoice_header";

    public static final String ORDER_ITEM_INVOICE_HEADER = "order_item_invoice_header";

    public static final String CHECKOUT_INVOICE_HEADER = "checkout_invoice_header";

    public static final String RESERVATION_PAY_INVOICE_HEADER = "reservation_pay_invoice_header";

    public static final String ITEM_PRICE_TOTAL = "item_price_total";

    public static final String SURCHARGE_TOTAL = "surcharge_total";

    public static final String TOTAL_DUE = "total_due";

    public static final String PROMOTION_TOTAL = "promotion_total";
    public static final String CHANGE = "change";
    public static final String PAID = "paid";

    public static final String REFUND_DISCOUNTS_TOTAL = "refund_discounts_total";

    public static final String COLON = "：";

    public static final String GREATER_THAN = "（%s元以上）";

    public static final String DINEIN_PART_REFUND_STATISTICS = "dinein_part_refund_statistics";

    public static final String DINEIN_RECOVERY_STATISTICS = "dinein_recovery_statistics";

    public static final String FAST_PART_REFUND_STATISTICS = "fast_part_refund_statistics";

    public static final String FAST_RECOVERY_STATISTICS = "fast_recovery_statistics";

    public static final String REFUND_TOTAL__STATISTICS = "refund_total__statistics";

    public static final String REFUND_ORDER_COUNT = "refund_order_count";

    public static final String REFUND_AMOUNT = "refund_amount";

    public static final String CUSTOMER_TRAFFIC = "customer_traffic";

    public static final String OCCUPANCY_RATE_PERCENT = "occupancy_rate_percent";

    public static final String OPEN_TABLE_TATE_PERCENT = "open_table_tate_percent";

    public static final String FLIP_TABLE_TATE_PERCENT = "flip_table_tate_percent";

    public static final String AVG_DINE_IN_TIME = "avg_dine_in_time";

    public static final String BUSINESS_OVERVIEW_SALE = "business_overview_sale";

    public static final String BUSINESS_OVERVIEW_NET_SALE = "business_overview_net_sale";

    public static final String SALES_REVENUE = "sales_revenue";

    public static final String TOTAL_DISCOUNTS = "total_discounts";

    public static final String TOTAL_REFUND = "total_refund";

    public static final String TOTAL_NET_SALES = "total_net_sales";

    public static final String TOTAL_NET_SALES_WITH_COMMENT = "total_net_sales_with_comment";

    public static final String OTHER_RECEIPTS = "other_receipts";

    public static final String MEMBER_RECHARGE = "member_recharge";

    public static final String NOT_CONTAINS_MEMBER_RECHARGE = "not_contains_member_recharge";

    public static final String EMPTY_STRING = "";

    public static final String LEFT_BRACKET = "(";

    public static final String RIGHT_BRACKET = ")";

    public static final String REMAINING_BALANCE = "remaining_balance";

    public static final String ESTIMATED_AMOUNT = "estimated_amount";

    public static final String BUSINESS_OVERVIEW_DISCOUNT = "business_overview_discount";

    public static final String COUPONS = "coupons";

    public static final String BUSINESS_OVERVIEW_GROUPON = "business_overview_groupon";

    public static final String GROUPON_NAME = "groupon_name";

    public static final String GROUPON_COUNT = "groupon_count";

    public static final String GROUPON_DISCOUNT = "groupon_discount";

    public static final String GROUPON_NET_SALES = "groupon_net_sales";

    public static final String NET_SALES_OTHER_RECEIPTS = "net_sales_other_receipts";

    public static final String TINY_BLANK = "    ";

    public static final String TABLE_NAME = "table_name";

    public static final String CHECKOUT_STAFFS = "checkout_staffs";

    public static final String RESERVATION_PAY_AMOUNT = "reservation_pay_amount";

    public static final String PAYMENT_TYPE = "payment_type";

    public static final String PAYMENT_TIME = "payment_time";

    public static final String RESERVE_START_TIME = "reserve_start_time";

    public static final String ARRIVE_TIME = "arrive_time";

    public static final String RESERVE_TABLE_NAME = "reserve_table_name";

    public static final String RESERVE_NUMBER = "reserve_number";

    public static final String RESERVE_NAME = "reserve_name";

    public static final String GENDER_MAN = "gender_man";

    public static final String GENDER_WOMAN = "gender_woman";

    public static final String RESERVE_PHONE = "reserve_phone";

    public static final String BUSINESS_OVERVIEW_ESTIMATED = "business_overview_estimated";

    /**
     * 退款金额
     */
    public static final String REFUND_ONLY_AMOUNT = "refund_only_amount";

    /**
     * 退款时间：
     */
    public static final String REFUND_TIME = "refund_time";

    /**
     * 退款方式
     */
    public static final String REFUND_WAY = "refund_way";

    /**
     * 退款原因：
     */
    public static final String REFUND_REASON = "refund_reason";

    /**
     * 操作时间
     */
    public static final String OPERATION_TIME = "operation_time";

}
