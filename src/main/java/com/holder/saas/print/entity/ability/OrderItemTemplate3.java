package com.holder.saas.print.entity.ability;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.entity.PrintData;
import com.holder.saas.print.utils.template.TradeModeUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.print.content.PrintOrderItemDTO;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.enums.print.ContentTypeEnum;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/09/25 10:16
 */
@Component
@Deprecated
public class OrderItemTemplate3 implements PrintTemplate3<PrintOrderItemDTO> {
    @Override
    public PrintData getHeader(PrintOrderItemDTO printDto) {
        PrintData.Builder builder = PrintData.builder().setHeaderLine(MultiLangUtils.get("order_item_invoice_header"), 2, 2, true);
        if (StringUtils.isEmpty(printDto.getEstimateDeliveredTimeString())) return builder.builder();
        return builder.setHeaderLine(MultiLangUtils.get("meal_time") + printDto.getEstimateDeliveredTimeString(), 1, 1, false).builder();
    }

    @Override
    public PrintData getBody(PrintOrderItemDTO printDto, int pageSize) {
        String markName = TradeModeUtils.getMarkName(printDto.getTradeMode(), printDto.getMarkName());
        PrintData.Builder printBuilder = PrintData.builder().addBodyLine(markName + printDto.getMarkNo(), ContentTypeEnum.SECTION, 2, 2, true);
        if (58 == pageSize) {
            printBuilder.addBodyKeyValueLine(MultiLangUtils.get(Constant.ORDER_NUMBER), printDto.getOrderNo(), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get(Constant.CUSTOMER_NUMBER), "" + printDto.getPersonNumber(), 1, 1, false, pageSize);
        } else {
            printBuilder.addBodyKeyValueLine(MultiLangUtils.get(Constant.ORDER_NUMBER) + printDto.getOrderNo(), MultiLangUtils.get(Constant.CUSTOMER_NUMBER) + printDto.getPersonNumber(), 1, 1, false, pageSize);
        }
        if (printDto.getItemRecordList() != null && !printDto.getItemRecordList().isEmpty()) {
            getKitchenItemListContent(printDto.getItemRecordList(), printBuilder, this, false, pageSize);
        }
        printBuilder.addPartLine(this, null, 1, 1, false);
        if (!StringUtils.isEmpty(printDto.getRemark())) {
            printBuilder.addBodyLine(MultiLangUtils.get("order_remark") + printDto.getRemark(), ContentTypeEnum.SECTION, 2, 2, true)
                    .addPartLine(this, null, 1, 1, false).builder();
        }
        int sum = 0;
        if (printDto.getItemRecordList() != null && !printDto.getItemRecordList().isEmpty()) {
            int weight = printDto.getItemRecordList().stream().filter(PrintItemRecord::getAsWeight).collect(Collectors.toList()).size();
            //普通商品份数
            sum = printDto.getItemRecordList().stream().filter(itemRecord -> !itemRecord.getAsWeight()).mapToInt(value -> value.getNumber().intValue()).sum();
            sum += weight;
        }
        //称重商品份数
        return printBuilder.addBodyLine(MultiLangUtils.get("total_copies") + sum, ContentTypeEnum.SECTION, 1, 1, false)
                .addPartLine(this, null, 1, 1, false).builder();
    }

    @Override
    public PrintData getFooter(PrintOrderItemDTO printDto, int pageSize) {
        PrintData.Builder builder = PrintData.builder();
        if (58 == pageSize) {
            builder.addBodyKeyValueLine(MultiLangUtils.get(Constant.OPERATOR), printDto.getOperatorStaffName(), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get("order_time_short"), DateTimeUtils.mills2String(printDto.getOrderTime(), "MM-dd HH:mm"), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get(Constant.PRINT_TIME), DateTimeUtils.mills2String(System.currentTimeMillis(), "MM-dd HH:mm"), 1, 1, false, pageSize);
        } else {
            builder.addBodyLine(MultiLangUtils.get(Constant.OPERATOR) + printDto.getOperatorStaffName(), ContentTypeEnum.SECTION, 1, 1, false)
                    .addBodyKeyValueLine(MultiLangUtils.get("order_time_short") + DateTimeUtils.mills2String(printDto.getOrderTime(), "MM-dd HH:mm"), MultiLangUtils.get(Constant.PRINT_TIME) + DateTimeUtils.mills2String(System.currentTimeMillis(), "MM-dd HH:mm"), 1, 1, false, pageSize);
        }
        return builder.builder();
    }

    @Override
    public String getPartLine() {
        return "-";
    }

    @Override
    public Class<PrintOrderItemDTO> getClassOfPrintDTO() {
        return PrintOrderItemDTO.class;
    }

    @Override
    public String getPrintFailedMessage(PrintOrderItemDTO printDto) {
        return MultiLangUtils.get("order_item_invoice")
                + String.format(MultiLangUtils.get("print_failed_please_handle_it_promptly"), printDto.getMarkNo());
    }
}
