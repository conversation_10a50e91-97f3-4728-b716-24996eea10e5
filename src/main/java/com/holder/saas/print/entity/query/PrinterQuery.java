package com.holder.saas.print.entity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class PrinterQuery implements Serializable {

    private static final long serialVersionUID = 5011174281376425691L;

    /**
     * 门店GUID
     */
    private String storeGuid;

    /**
     * 票据类型
     */
    private Integer invoiceType;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 桌台区域GUID
     */
    private String areaGuid;

    /**
     * 桌台GUID
     */
    private String tableGuid;

    /**
     * 打印机GUID
     */
    private String printerGuid;

    /**
     * 菜品GUID列表
     */
    private List<String> arrayOfItemGuid;

    @ApiModelProperty(value = "打印业务类型: 1/前台打印; 2后厨打印; 3标签打印;")
    private Integer businessType;

    /**
     * 订单模式 正餐 快餐
     */
    private Integer tradeMode;

    /**
     * 是否部分退款
     */
    private Boolean partRefundFlag;
}
