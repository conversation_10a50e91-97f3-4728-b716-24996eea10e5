package com.holder.saas.print.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AlignTypeEnum
 * @date 2018/07/26 13:54
 * @description ..
 * @program holder-saas-store-print
 * @deprecated 未知
 */
@Deprecated
public enum AlignTypeEnum {

    CENTER("C"),

    LEFT("L"),

    RIGHT("R");

    private String value;

    AlignTypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
