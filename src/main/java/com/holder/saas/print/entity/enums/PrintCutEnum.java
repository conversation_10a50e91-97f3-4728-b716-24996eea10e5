package com.holder.saas.print.entity.enums;

import com.holderzone.framework.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 切纸方式
 *
 * <AUTHOR>
 * @date 2018/09/25 9:44
 */
@Slf4j
public enum PrintCutEnum {

    /**
     * 整单切纸
     * 全部商品一张单
     */
    ALL(0),

    /**
     * 商品切纸
     * 一种商品一张单
     * 其中“商品”定义：普通菜品、称重菜品、规格菜品、套餐菜品（只取决其子项？）
     */
    PER_ITEM(1),

    /**
     * 分类切纸
     * 同种分类一张单
     * 其中“分类”定义：普通菜品所属分类、称重菜品所属分类、规格菜品所属分类、套餐子菜品所属分类（不考虑套餐所属分类）
     */
    PER_ITEM_TYPE(2),

    /**
     * 数量切纸
     * 一份数量一张单
     * 其中“数量”定义：
     */
    PER_ITEM_COUNT(3);

    private Integer code;

    PrintCutEnum(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    /**
     * 根据code获取切纸方式
     *
     * @param code
     * @return
     */
    public static PrintCutEnum ofCode(Integer code) {
        for (PrintCutEnum printCutEnum : PrintCutEnum.values()) {
            if (printCutEnum.getCode().equals(code)) {
                return printCutEnum;
            }
        }
        return null;
    }

    public static PrintCutEnum ofCodeDefaultAll(Integer code) {
        for (PrintCutEnum printCutEnum : PrintCutEnum.values()) {
            if (printCutEnum.getCode().equals(code)) {
                return printCutEnum;
            }
        }
        log.error(StringUtils.isEmpty(code) ? "切纸类型为空"
                : "不支持的切纸类型，将使用默认切纸方式：整单切纸");
        return ALL;
    }
}
