package com.holder.saas.print.entity.ability;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.entity.PrintData;
import com.holder.saas.print.utils.template.TradeModeUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.print.content.PrintRefundItemDTO;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.deprecate.PrintEDTO;
import com.holderzone.saas.store.enums.print.ContentTypeEnum;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

/**
 * 退菜单
 *
 * <AUTHOR>
 * @date 2018/9/25 14:22
 */
@Component
@Deprecated
public class RefundItemTemplate3 implements PrintTemplate3<PrintRefundItemDTO> {

    @Override
    public PrintData getHeader(PrintRefundItemDTO printDto) {
        return PrintData.builder().setHeaderLine(MultiLangUtils.get("refund_item_invoice_header"), 2, 2, true).builder();
    }

    @Override
    public PrintData getBody(PrintRefundItemDTO printDto, int pageSize) {
        String markName = TradeModeUtils.getMarkName(printDto.getTradeMode(), printDto.getMarkName());
        PrintData.Builder printDataBuilder = PrintData.builder().
                addBodyLine(PrintEDTO.section(markName + printDto.getMarkNo()), ContentTypeEnum.SECTION, 2, 2, true);
        // 订单号 人数
        if (58 == pageSize) {
            printDataBuilder.addBodyKeyValueLine(MultiLangUtils.get(Constant.ORDER_NUMBER), printDto.getOrderNo(), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get(Constant.CUSTOMER_NUMBER), "" + printDto.getPersonNumber(), 1, 1, false, pageSize);
        } else {
            printDataBuilder.addBodyKeyValueLine(MultiLangUtils.get(Constant.ORDER_NUMBER) + printDto.getOrderNo(), MultiLangUtils.get(Constant.CUSTOMER_NUMBER) + printDto.getPersonNumber(), 1, 1, false, pageSize);
        }

        // 各类退菜列表
        if (printDto.getItemRecordList() != null && !printDto.getItemRecordList().isEmpty()) {
            getKitchenItemListContent(printDto.getItemRecordList(), printDataBuilder, this, true, pageSize);
        }
        int sum = 0;
        if (printDto.getItemRecordList() != null && !printDto.getItemRecordList().isEmpty()) {
            int weight = printDto.getItemRecordList().stream().filter(PrintItemRecord::getAsWeight).collect(Collectors.toList()).size();
            //普通商品份数
            sum = printDto.getItemRecordList().stream().filter(itemRecord -> !itemRecord.getAsWeight()).mapToInt(value -> value.getNumber().intValue()).sum();
            sum += weight;
        }
        printDataBuilder.addPartLine(this, null, 1, 1, false)
                .addBodyLine(PrintEDTO.section(MultiLangUtils.get("total_copies") + sum), ContentTypeEnum.SECTION, 1, 1, false);
        return printDataBuilder.builder();
    }

    @Override
    public PrintData getFooter(PrintRefundItemDTO printDto, int pageSize) {
        PrintData.Builder builder = PrintData.builder();
        if (58 == pageSize) {
            builder.addBodyKeyValueLine(MultiLangUtils.get(Constant.OPERATOR), printDto.getOperatorStaffName(), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get("order_time_short"), DateTimeUtils.mills2String(printDto.getOrderTime(), "MM-dd HH:mm"), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get(Constant.PRINT_TIME), DateTimeUtils.mills2String(System.currentTimeMillis(), "MM-dd HH:mm"), 1, 1, false, pageSize);
        } else {
            builder.addBodyLine(MultiLangUtils.get(Constant.OPERATOR) + printDto.getOperatorStaffName(), ContentTypeEnum.SECTION, 1, 1, false)
                    .addBodyKeyValueLine(MultiLangUtils.get("order_time_short") + DateTimeUtils.mills2String(printDto.getOrderTime(), "MM-dd HH:mm"), MultiLangUtils.get(Constant.PRINT_TIME) + DateTimeUtils.mills2String(System.currentTimeMillis(), "MM-dd HH:mm"), 1, 1, false, pageSize);
        }
        return builder.builder();
    }

    @Override
    public String getPartLine() {
        return "-";
    }

    @Override
    public Class<PrintRefundItemDTO> getClassOfPrintDTO() {
        return PrintRefundItemDTO.class;
    }

    @Override
    public String getPrintFailedMessage(PrintRefundItemDTO printDto) {
        return MultiLangUtils.get("refund_item_invoice")
                + String.format(MultiLangUtils.get("print_failed_please_handle_it_promptly"), printDto.getMarkNo());
    }
}
