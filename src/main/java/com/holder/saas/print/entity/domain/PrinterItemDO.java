package com.holder.saas.print.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@TableName("hsp_printer_item")
public class PrinterItemDO implements Serializable {

    private static final long serialVersionUID = 4495132042816105577L;

    /**
     * 主键id，自增
     */
    @TableId
    private Long id;

    /**
     * 唯一标识
     * todo 告诉DaoDao该Sql列由link_guid变为guid了
     */
    private String guid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 打印机guid
     */
    private String printerGuid;

    /**
     * 菜品guid
     */
    private String itemGuid;

    /**
     * 菜品名称
     */
    private String itemName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}