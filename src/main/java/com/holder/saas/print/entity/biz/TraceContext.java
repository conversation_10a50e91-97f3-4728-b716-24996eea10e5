package com.holder.saas.print.entity.biz;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import lombok.Data;

@Data
public class TraceContext {

    private String erpGuid;

    private String erpName;

    private String storeGuid;

    private String storeName;

    private String deviceId;

    public static String ids() {
        TraceContext traceContext = new TraceContext();
        traceContext.setErpGuid(UserContextUtils.getEnterpriseGuid());
        traceContext.setStoreGuid(UserContextUtils.getStoreGuid());
        return JacksonUtils.writeValueAsString(traceContext);
    }

    public static String names() {
        TraceContext traceContext = new TraceContext();
        traceContext.setErpGuid(UserContextUtils.getEnterpriseGuid());
        traceContext.setErpName(UserContextUtils.getEnterpriseName());
        traceContext.setStoreGuid(UserContextUtils.getStoreGuid());
        traceContext.setStoreName(UserContextUtils.getStoreName());
        return JacksonUtils.writeValueAsString(traceContext);
    }

    public static String device(String deviceId) {
        TraceContext traceContext = new TraceContext();
        traceContext.setErpGuid(UserContextUtils.getEnterpriseGuid());
        traceContext.setErpName(UserContextUtils.getEnterpriseName());
        traceContext.setStoreGuid(UserContextUtils.getStoreGuid());
        traceContext.setStoreName(UserContextUtils.getStoreName());
        traceContext.setDeviceId(deviceId);
        return JacksonUtils.writeValueAsString(traceContext);
    }
}
