package com.holder.saas.print.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@TableName("hsp_printer_invoice")
public class PrinterInvoiceDO implements Serializable {

    private static final long serialVersionUID = 4355151640787064236L;

    /**
     * 主键id，自增
     */
    @TableId
    private Long id;

    /**
     * 唯一标识
     */
    private String guid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 打印机guid
     */
    private String printerGuid;

    /**
     * 票据类型
     */
    private Integer invoiceType;

    /**
     * 票据名称
     */
    private String invoiceName;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 打印次数
     */
    private Integer printCount;

    /**
     * 自动打印小票默认自动
     */
    private Integer autoPrint;

}