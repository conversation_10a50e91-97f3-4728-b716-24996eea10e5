package com.holder.saas.print.entity.ability;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.entity.PrintData;
import com.holder.saas.print.utils.template.TradeModeUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.print.content.PrintItemDetailDTO;
import com.holderzone.saas.store.enums.print.ContentTypeEnum;
import com.holderzone.saas.store.enums.print.TradeModeEnum;
import org.springframework.stereotype.Component;

/**
 * 菜品清单
 *
 * <AUTHOR>
 * @date 2018/09/21 15:10
 */
@Component
@Deprecated
public class ItemDetailTemplate3 implements PrintTemplate3<PrintItemDetailDTO> {
    @Override
    public PrintData getHeader(PrintItemDetailDTO printDto) {
        return PrintData.builder().setHeaderLine(printDto.getStoreName(), 2, 2, true).addBodyCenter(MultiLangUtils.get("item_list_invoice_header"), 2, 2, false).builder();
    }

    @Override
    public PrintData getBody(PrintItemDetailDTO printDto, int pageSize) {
        String markName = TradeModeUtils.getMarkName(TradeModeEnum.DINE.getMode());
        PrintData.Builder printBuilder = PrintData.builder().addPartLine(this, null, 1, 1, false)
                .addBodyLine(markName + printDto.getMarkNo(), ContentTypeEnum.SECTION, 2, 2, true)
                .addPartLine(this, null, 1, 1, false);
        if (58 == pageSize) {
            printBuilder.addBodyKeyValueLine(MultiLangUtils.get(Constant.ORDER_NUMBER), printDto.getOrderNo(), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get(Constant.CUSTOMER_NUMBER), "" + printDto.getPersonNumber(), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get("cashier"), printDto.getOperatorStaffName(), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get("open_table_time_short"), DateTimeUtils.mills2String(printDto.getOpenTableTime()), 1, 1, false, pageSize);
        } else {
            printBuilder.addBodyKeyValueLine(MultiLangUtils.get(Constant.ORDER_NUMBER) + printDto.getOrderNo(), MultiLangUtils.get(Constant.CUSTOMER_NUMBER) + printDto.getPersonNumber(), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get("cashier") + printDto.getOperatorStaffName(), MultiLangUtils.get("open_table_time_short") + DateTimeUtils.mills2String(printDto.getOpenTableTime()), 1, 1, false, pageSize);
        }
        //拼接菜品详情部分
        if (printDto.getItemRecordList() != null && !printDto.getItemRecordList().isEmpty()) {
            getItemListContent(printDto.getItemRecordList(), printBuilder, pageSize, this);
        }
        printBuilder.addBodyKeyValueLine(MultiLangUtils.get("item_total"), String.valueOf(printDto.getTotal()), 1, 1, false, pageSize);
        return printBuilder.builder();
    }

    @Override
    public PrintData getFooter(PrintItemDetailDTO printDto, int pageSize) {
        if (58 == pageSize) {
            return PrintData.builder().addPartLine(this, null, 1, 1, false)
                    .addBodyKeyValueLine(MultiLangUtils.get(Constant.OPERATOR), printDto.getOperatorStaffName(), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get(Constant.PRINT_TIME), DateTimeUtils.mills2String(System.currentTimeMillis()), 1, 1, false, pageSize)
                    .builder();
        }
        return PrintData.builder().addPartLine(this, null, 1, 1, false)
                .addBodyKeyValueLine(MultiLangUtils.get(Constant.OPERATOR) + printDto.getOperatorStaffName(), MultiLangUtils.get(Constant.PRINT_TIME) + DateTimeUtils.mills2String(System.currentTimeMillis()), 1, 1, false, pageSize)
                .builder();
    }

    @Override
    public String getPartLine() {
        return "-";
    }

    @Override
    public Class<PrintItemDetailDTO> getClassOfPrintDTO() {
        return PrintItemDetailDTO.class;
    }

    @Override
    public String getPrintFailedMessage(PrintItemDetailDTO printDto) {
        return MultiLangUtils.get("item_list_invoice")
                + String.format(MultiLangUtils.get("print_failed_please_handle_it_promptly"), printDto.getMarkNo());
    }
}
