package com.holder.saas.print.entity.enums;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * 纸张宽度
 *
 * <AUTHOR>
 * @date 2018/12/217 13:56
 */
public enum PrintPageEnum {

    WIDTH_58("58", "票据打印纸宽58mm"),

    WIDTH_80("80", "票据打印纸宽80mm"),

    W40_H30("40X30", "标签打印纸宽40mm高30mm"),

    W30_H20("30X20", "标签打印纸宽30mm高20mm");

    private String pageSize;

    private String desc;

    PrintPageEnum(String pageSize,String desc) {
        this.pageSize = pageSize;
        this.desc = desc;
    }

    public static PrintPageEnum ofPageSize(String pageSize) {
        for (PrintPageEnum printPageEnum : PrintPageEnum.values()) {
            if (printPageEnum.getPageSize().equalsIgnoreCase(pageSize)) {
                return printPageEnum;
            }
        }
        throw new BusinessException("不支持的纸宽类型：" + pageSize);
    }

    public String getPageSize() {
        return pageSize;
    }

    public int getPageWidth() {
        int indexOfSeparator = pageSize.indexOf("X");
        if (indexOfSeparator < 0) return Integer.valueOf(pageSize);
        return Integer.valueOf(pageSize.substring(0, indexOfSeparator));
    }
}
