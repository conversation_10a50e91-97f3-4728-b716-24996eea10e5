package com.holder.saas.print.entity.ability;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.entity.PrintData;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.print.content.PrintTakeoutDTO;
import com.holderzone.saas.store.enums.print.ContentTypeEnum;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;

/**
 * 外卖单
 *
 * <AUTHOR>
 * @date 2018/09/21 16:53
 */
@Component
@Deprecated
public class TakeoutTemplate3 implements PrintTemplate3<PrintTakeoutDTO> {
    @Override
    public PrintData getHeader(PrintTakeoutDTO printDto) {
        if (printDto.getAbnormal()) {
            return PrintData.builder().setHeaderLine(printDto.getPlatform() + printDto.getPlatformOrder(), 2, 2, true)
                    .addBodyCenter(" ", 1, 1, false)
                    .addBodyCenter(MultiLangUtils.get("takeout_print_warning"), 2, 2, true)
                    .addBodyCenter(" ", 1, 1, false)
                    .addBodyLine(MultiLangUtils.get("takeout_print_warning_description"), ContentTypeEnum.SECTION, 1, 1, false)
                    .builder();
        }
        return PrintData.builder().setHeaderLine(printDto.getPlatform() + printDto.getPlatformOrder(), 2, 2, true)
                .addBodyCenter(printDto.getStoreName(), 1, 1, false)
                // todo payMsg需要处理 不用处理，国外展示用不到外卖
                .addBodyCenter("--" + printDto.getPayMsg() + "--", 2, 2, true).builder();
    }

    @Override
    public PrintData getBody(PrintTakeoutDTO printDto, int pageSize) {
        PrintData.Builder printBuilder = PrintData.builder().addPartLine(this, null, 1, 1, false)
                .addBodyLine(MultiLangUtils.get("expected_delivery_time") + printDto.getExpectTime(), ContentTypeEnum.SECTION, 1, 1, false)
                .addBodyLine(MultiLangUtils.get("order_time") + DateTimeUtils.mills2String(printDto.getOrderTime()), ContentTypeEnum.SECTION, 1, 1, false)
                .addBodyLine(MultiLangUtils.get(Constant.ORDER_NUMBER) + printDto.getOrderNo(), ContentTypeEnum.SECTION, 1, 1, false);

        // 添加外卖单发票税号
        if (Optional.ofNullable(printDto.getInvoiced()).orElse(false)) {
            printBuilder.addPartLine(this, null, 1, 1, false)
                    .addBodyLine(MultiLangUtils.get("bill_looked_up") + Optional.ofNullable(printDto.getInvoiceTitle()).orElse(""), ContentTypeEnum.SECTION, 1, 1, false)
                    .addBodyLine(MultiLangUtils.get("duty_paragraph") + Optional.ofNullable(printDto.getTaxpayerId()).orElse(""), ContentTypeEnum.SECTION, 1, 1, false);
        } else {
            printBuilder.addPartLine(this, null, 1, 1, false)
                    .addBodyLine(MultiLangUtils.get("no_invoice_required"), ContentTypeEnum.SECTION, 1, 1, false);
        }

        if (!StringUtils.isEmpty(printDto.getRemark())) {
            printBuilder.addPartLine(this, null, 1, 1, false)
                    .addBodyLine(MultiLangUtils.get("remark") + printDto.getRemark(), ContentTypeEnum.SECTION, 2, 2, false);
        }
        if (printDto.getItemRecordList() != null && !printDto.getItemRecordList().isEmpty()) {
            getItemListContent(printDto.getItemRecordList(), printBuilder, pageSize, this);
        }
        if (printDto.getAbnormal()) return printBuilder.builder();
        printBuilder.addPartLine(this, null, 1, 1, false)
                .addBodyKeyValueLine(MultiLangUtils.get("item_total"), MultiLangUtils.get("money_unit") + printDto.getItemTotalPrice().toString(), 1, 1, false, pageSize)
                .addPartLine(this, null, 1, 1, false);
        //附加费
        if (printDto.getAdditionalChargeList() != null && !printDto.getAdditionalChargeList().isEmpty()) {
            printDto.getAdditionalChargeList().forEach(additionalCharge -> {
                if (additionalCharge.getChargeValue() != BigDecimal.ZERO) {
                    printBuilder.addBodyLine(additionalCharge.getChargeName() + MultiLangUtils.get("money_join") + additionalCharge.getChargeValue().setScale(2, RoundingMode.HALF_EVEN), ContentTypeEnum.SECTION, 1, 1, false);
                }
            });
        }
        if (printDto.getReduceRecordList() != null && !printDto.getReduceRecordList().isEmpty()) {
            printDto.getReduceRecordList().forEach(reduce -> {
                if (reduce.getAmount() != BigDecimal.ZERO) {
                    printBuilder.addBodyLine(reduce.getName() + MultiLangUtils.get("reduce_join") + reduce.getAmount().setScale(2, RoundingMode.HALF_EVEN), ContentTypeEnum.SECTION, 1, 1, false);
                }
            });
        }
        printBuilder.addPartLine(this, null, 1, 1, false);
        return printBuilder.addBodyRightLine(MultiLangUtils.get("total_money_join") + printDto.getOriginalPrice(), ContentTypeEnum.SECTION, 1, 1, false).
                addBodyRightLine(MultiLangUtils.get("paid_money_join") + printDto.getActuallyPay(), ContentTypeEnum.SECTION, 2, 2, false).builder();
    }

    @Override
    public PrintData getFooter(PrintTakeoutDTO printDto, int pageSize) {
        PrintData.Builder builder = PrintData.builder().addPartLine(this, null, 1, 1, false).addBodyLine(printDto.getReceiverName(), ContentTypeEnum.SECTION, 2, 2, true)
                .addBodyLine(printDto.getReceiverTel(), ContentTypeEnum.SECTION, 2, 2, true)
                .addBodyLine(printDto.getReceiverAddress(), ContentTypeEnum.SECTION, 2, 2, true)
                .addPartLine(this, null, 1, 1, false);
        if (58 == pageSize) {
            builder.addBodyKeyValueLine(MultiLangUtils.get(Constant.OPERATOR), printDto.getOperatorStaffName(), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get(Constant.PRINT_TIME), DateTimeUtils.mills2String(System.currentTimeMillis(), "MM-dd HH:mm"), 1, 1, false, pageSize);
        } else {
            builder.addBodyKeyValueLine(MultiLangUtils.get(Constant.OPERATOR) + printDto.getOperatorStaffName(), MultiLangUtils.get(Constant.PRINT_TIME) + DateTimeUtils.mills2String(System.currentTimeMillis(), "MM-dd HH:mm"), 1, 1, false, pageSize);
        }
        return builder.builder();
    }

    @Override
    public String getPartLine() {
        return "-";
    }

    @Override
    public Class<PrintTakeoutDTO> getClassOfPrintDTO() {
        return PrintTakeoutDTO.class;
    }

    @Override
    public String getPrintFailedMessage(PrintTakeoutDTO printDto) {
        return MultiLangUtils.get("takeout_invoice")
                + String.format(MultiLangUtils.get("print_failed_please_handle_it_promptly"), printDto.getPlatformOrder());
    }
}
