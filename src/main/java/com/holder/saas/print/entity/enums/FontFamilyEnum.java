package com.holder.saas.print.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FontFamilyEnum
 * @date 2018/07/26 13:47
 * @description qqq
 * @program holder-saas-store-print
 * @deprecated 未知
 */
@Deprecated
public enum FontFamilyEnum {

    SIMSUN("宋体");

    private String value;

    FontFamilyEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
