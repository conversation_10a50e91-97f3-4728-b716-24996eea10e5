package com.holder.saas.print.entity.read;

import com.holder.saas.print.entity.domain.PrinterDO;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class PrintRecordReadDO implements Serializable {

    private static final long serialVersionUID = -5999724076210711241L;

    /**
     * 主键id，自增
     */
    private Long id;

    /**
     * 打印UID
     */
    private String recordUid;

    /**
     * 打印记录guid
     */
    private String recordGuid;

    /**
     * 门店GUID
     */
    private String storeGuid;

    /**
     * 生成该打印记录的设备ID
     */
    private String deviceId;

    /**
     * 打印类型代码
     */
    private Integer invoiceType;

    /**
     * 打印机guid
     */
    private String printerGuid;

    /**
     * 打印内容，Json格式
     */
    private String printContent;

    /**
     * 0/打印中;1/打印成功;2/打印失败
     */
    private Integer printStatus;

    /**
     * 打印状态消息详情
     */
    private String printStatusMsg;

    /**
     * 创建该条记录的员工id
     */
    private String createStaffGuid;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 数据更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 冗余字段
     */
    private PrinterDO printerDO;
}