package com.holder.saas.print.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.holder.saas.print.entity.enums.PrintCutEnum;
import com.holder.saas.print.entity.enums.PrintPageEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("hsp_printer")
public class PrinterDO implements Serializable {

    private static final long serialVersionUID = -75342253696516107L;

    /**
     * 自增id
     */
    @TableId
    private Long id;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 设备编号
     */
    private String deviceId;

    /**
     * 打印机guid
     */
    private String printerGuid;

    /**
     * 打印机名称
     */
    private String printerName;

    /**
     * 打印业务类型; 参数: 0/前台打印; 1后厨打印; 2/标签打印
     *
     * @see com.holderzone.saas.store.enums.print.BusinessTypeEnum
     */
    private Integer businessType;

    /**
     * 打印机类型; 可选参数: 0/本机打印机; 1/网络打印机; 2/usb打印机 3/云打印机
     *
     * @see com.holderzone.saas.store.enums.print.PrinterTypeEnum
     */
    private Integer printerType;

    /**
     * 区域类型 0：按桌台 1：按区域
     *
     * @see com.holderzone.saas.store.enums.print.PrinterAreaTypeEnum
     */
    private Integer areaType;

    /**
     * 打印机ip
     */
    private String printerIp;

    /**
     * 打印端口
     */
    private Integer printerPort;

    /**
     * 打印次数
     */
    private Integer printCount;

    /**
     * 打印纸张类型; 参数: 80; 58; 40X30; 30X20
     *
     * @see PrintPageEnum
     */
    private String printPage;

    /**
     * 打印方式(切纸方式);  参数: 0/整单; 1/一菜一单; 2/一种类型一单; 3/一份数量一单; 默认0/整单
     *
     * @see PrintCutEnum
     */
    private Integer printCut;

    /**
     * 是否是主机：0=否，1=是
     */
    private Boolean isMaster;

    /**
     * 是否需要打印挂起单：0=否，1=是
     */
    private Boolean isPrintHangUp;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 设备编号
     */
    private String deviceNo;

    /**
     * 设备密钥
     */
    private String deviceKey;

    /**
     * 设备厂商类型 1飞蛾 2商米
     */
    private Integer manufacturersType;

    /**
     * 设备型号
     */
    private String deviceModel;

    @ApiModelProperty(value = "打印价格 0不打印 1打印")
    private Boolean printPriceType;

    /**
     * 部分退款是否打印退菜单
     *
     * @see com.holder.saas.print.entity.enums.PrintPartRefundEnum
     */
    private Integer partRefundPrintFlag;

}