package com.holder.saas.print.entity.ability;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.entity.PrintData;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.print.content.PrintHandOverDTO;
import com.holderzone.saas.store.enums.print.ContentTypeEnum;
import org.springframework.stereotype.Component;

/**
 * 交接单
 *
 * <AUTHOR>
 * @date 2018/09/21 17:41
 */
@Component
@Deprecated
public class HandOverTemplate3 implements PrintTemplate3<PrintHandOverDTO> {

    @Override
    public PrintData getHeader(PrintHandOverDTO printDto) {
        return PrintData.builder().setHeaderLine(printDto.getStoreName(), 2, 2, true).addBodyCenter(MultiLangUtils.get("handover_invoice_header"), 2, 2, false).builder();
    }

    @Override
    public PrintData getBody(PrintHandOverDTO printDto, int pageSize) {
        PrintData.Builder printBuilder = PrintData.builder();
        if (58 == pageSize) {
            printBuilder.addBodyKeyValueLine(MultiLangUtils.get("handover_employee"), printDto.getStaffName(), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get("duration_time"), printDto.getDuration(), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get("start_time"), printDto.getBeginTime(), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get("end_time"), printDto.getOverTime(), 1, 1, false, pageSize);
        } else {
            printBuilder.addBodyKeyValueLine(MultiLangUtils.get("handover_employee") + printDto.getStaffName(), MultiLangUtils.get("duration_time") + printDto.getDuration(), 1, 1, false, pageSize)
                    .addBodyLine(MultiLangUtils.get("start_time") + printDto.getBeginTime(), ContentTypeEnum.SECTION, 1, 1, false)
                    .addBodyLine(MultiLangUtils.get("end_time") + printDto.getOverTime(), ContentTypeEnum.SECTION, 1, 1, false);
        }
        printBuilder
                .addPartLine(this, null, 1, 1, false)
                .addBodyLine(MultiLangUtils.get("on_duty_turnover") + printDto.getDutyAmount(), ContentTypeEnum.SECTION, 1, 1, false)
                .addPartLine(this, null, 2, 2, false);
        printDto.getPayRecordList().forEach(payRecord -> {
            printBuilder.addBodyKeyValueLine(payRecord.getPayName(), BigDecimalUtils.moneyTrimmedString(payRecord.getAmount()), 1, 1, false, pageSize);
        });
        printBuilder.addPartLine(this, null, 1, 1, false)
                .addBodyLine(MultiLangUtils.get("project_income"), ContentTypeEnum.SECTION, 1, 1, false)
                .addPartLine(this, null, 1, 1, false);
        printBuilder.addBodyKeyValueLine(MultiLangUtils.get("sales_income"), printDto.getSaleIncome().toString(), 1, 1, false, pageSize);
        printBuilder.addBodyKeyValueLine(MultiLangUtils.get("recharge_income"), printDto.getRechargeIncome().toString(), 1, 1, false, pageSize);
        return printBuilder.addPartLine(this, null, 1, 1, false)
                .addBodyLine(MultiLangUtils.get("note_for_turnover"), ContentTypeEnum.SECTION, 1, 1, false)
                .addPartLine(this, null, 1, 1, false).builder();
    }

    @Override
    public PrintData getFooter(PrintHandOverDTO printDto, int pageSize) {
        if (58 == pageSize) {
            return PrintData.builder()
                    .addBodyKeyValueLine(MultiLangUtils.get(Constant.OPERATOR), printDto.getOperatorStaffName(), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get(Constant.PRINT_TIME), DateTimeUtils.mills2String(System.currentTimeMillis(), "MM-dd HH:mm"), 1, 1, false, pageSize).builder();
        }
        return PrintData.builder()
                .addBodyKeyValueLine(MultiLangUtils.get(Constant.OPERATOR) + printDto.getOperatorStaffName(), MultiLangUtils.get(Constant.PRINT_TIME) + DateTimeUtils.mills2String(System.currentTimeMillis(), "MM-dd HH:mm"), 1, 1, false, pageSize).builder();
    }

    @Override
    public String getPartLine() {
        return "-";
    }

    @Override
    public Class<PrintHandOverDTO> getClassOfPrintDTO() {
        return PrintHandOverDTO.class;
    }

    @Override
    public String getPrintFailedMessage(PrintHandOverDTO printDto) {
        return MultiLangUtils.get("print_failed_msg_for_turnover");
    }
}
