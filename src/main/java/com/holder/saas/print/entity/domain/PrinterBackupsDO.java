package com.holder.saas.print.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.holder.saas.print.entity.enums.PrintCutEnum;
import com.holder.saas.print.entity.enums.PrintPageEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("hsp_printer_backups")
public class PrinterBackupsDO implements Serializable {

    private static final long serialVersionUID = -75342253696516107L;

    /**
     * 自增id
     */
    @TableId
    private Long id;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 设备编号
     */
    private String deviceId;

    /**
     * 打印机备份Json
     */
    private String printListJson;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录更新时间
     */
    private LocalDateTime gmtModified;
}