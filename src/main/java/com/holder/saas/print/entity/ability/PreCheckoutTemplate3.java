package com.holder.saas.print.entity.ability;

import com.holder.saas.print.entity.PrintData;
import com.holderzone.saas.store.dto.print.content.PrintPreCheckoutDTO;
import org.springframework.stereotype.Component;

@Component
@Deprecated
public class PreCheckoutTemplate3 implements PrintTemplate3<PrintPreCheckoutDTO> {

    @Override
    public PrintData getHeader(PrintPreCheckoutDTO printDto) {
        return null;
    }

    @Override
    public PrintData getBody(PrintPreCheckoutDTO printDto, int pageSize) {
        return null;
    }

    @Override
    public PrintData getFooter(PrintPreCheckoutDTO printDto, int pageSize) {
        return null;
    }

    @Override
    public String getPartLine() {
        return null;
    }

    @Override
    public Class<PrintPreCheckoutDTO> getClassOfPrintDTO() {
        return null;
    }

    @Override
    public String getPrintFailedMessage(PrintPreCheckoutDTO printDto) {
        return null;
    }
}
