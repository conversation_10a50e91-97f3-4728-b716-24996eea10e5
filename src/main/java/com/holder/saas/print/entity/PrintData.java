package com.holder.saas.print.entity;

import com.holder.saas.print.entity.ability.PrintTemplate3;
import com.holder.saas.print.entity.enums.AlignTypeEnum;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.print.deprecate.PrintEDTO;
import com.holderzone.saas.store.dto.print.deprecate.PrintRowDTO;
import com.holderzone.saas.store.enums.print.ContentTypeEnum;
import lombok.val;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 打印行的集合,用于业务逻辑上的区分
 *
 * <AUTHOR>
 * @version 1.0
 * @description 打印模板抽象
 * @deprecated 上古前辈备注删除的，我也不清楚
 */
@Deprecated
public class PrintData {

    /**
     * 打印体
     */
    private List<PrintRowDTO> body = new ArrayList<>();

    public List<PrintRowDTO> getBody() {
        return body;
    }

    public void setBody(List<PrintRowDTO> body) {
        this.body = body;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private PrintData printData;

        public Builder() {
            this.printData = new PrintData();
        }

        public Builder setHeaderLine(String header, int xm, int ym, boolean bold) {
            PrintRowDTO printRow = PrintRowDTO.builder()
                    .setContentType(ContentTypeEnum.SECTION.getType())
                    .setXm(String.valueOf(xm))
                    .setYm(String.valueOf(ym))
                    .setBlod(bold)
                    .setAlign(AlignTypeEnum.CENTER.getValue())
                    .build();
            printRow.addPrintE(PrintEDTO.section(header));
            printData.getBody().add(printRow);
            return this;
        }

        /**
         * 打印一行数据,以空格区分列
         *
         * @param pageSize
         * @param columns  列名数组
         * @param xm
         * @param column
         * @param printRow
         * @return
         */
        private PrintRowDTO printLine(int pageSize, String[] columns, int xm, int column, PrintRowDTO printRow) {
            for (int i = 0; i < columns.length; i++) {
                int[] position = getPosition(i + 1, column, pageSize);
                String content = printContent(pageSize, i + 1, columns[i], xm, column, printRow);
                printRow.addPrintE(PrintEDTO.coordinateRow(position[0], position[1], content));
            }
            return printRow;
        }

        /**
         * 按坐标重新调整行数据
         *
         * @param pageSize
         * @param part
         * @param text
         * @param xm
         * @param parts
         * @param printRow
         * @return
         */
        private String printContent(int pageSize, int part, String text, int xm, int parts, PrintRowDTO printRow) {
            String content = text;
//            int charOrNumberCount = 0;
            val width = (double) pageSize / parts;   //列宽
            Double singleCharWidth = getSingleCharWidth(pageSize, xm);
//            Pattern pattern = Pattern.compile("([\\u4e00-\\u9fa5]+)");
//            Matcher matcher = pattern.matcher(text);
////            查找匹配出中文字符
//            while (matcher.find()) {
//                String ret = matcher.group();
//                charOrNumberCount += ret.length();
//            }
//            double length = (text.length() - charOrNumberCount) * singleCharWidth + charOrNumberCount * singleCharWidth * 2;   //整个字符串应该占有的宽度
            double length = getStringLength(text, pageSize, xm);
            if (length <= pageSize) {//小于行宽能够放下
                if (width <= length) {
                    //大于固定列宽
                    content = content + "\r\n";
                }
            } else {
                // 这里直接 /2 ，没有考虑非中文的情况，格式会不正常
                int index = (int) (pageSize / singleCharWidth / 2);     //pageSize能够放下多少个字符
                content = content.substring(0, index) + "\r\n";
                content += printContent(pageSize, part, text.substring(index), xm, parts, printRow);
            }
            return content;
        }

        /**
         * 计算每个字符所占的宽度mm
         *
         * @param pageSize 80 or 58 mm
         * @param xm       放大倍数
         * @return 单个字符所占的长度
         */
        private static Double getSingleCharWidth(int pageSize, int xm) {
            if (pageSize == 80) {
                return 80d / 48 * xm;
            }
            if (pageSize == 40) {
                return 40d / 24 * xm;
            }
            return 58d / 32 * xm;
        }

        /**
         * 计算每列的起始打印位置
         *
         * @param currentColumn 当前列
         * @param columnCount   总列数
         * @param pageSize      列宽
         * @return
         */
        private int[] getPosition(int currentColumn, int columnCount, int pageSize) {
            //计算每一列的毫米宽度对应的点位数
            int peer = pageSize / columnCount * 8;   //点位数
            if (currentColumn == 1) {
                return new int[]{0, 0};
            } else {
                peer = (currentColumn - 1) * peer;
                // 坐标不精确，导致最后一个column太窄，因此往前移30个点位，对应2个char
                if (58 == pageSize && currentColumn == columnCount) {
                    peer = peer - 30;
                }
                int nh = peer / 256 > 2 ? 2 : peer / 256;
                int nl = peer - nh * 256;
                return new int[]{nl, nh};
            }
        }

        /**
         * 计算value的起始打印位置
         *
         * @param pageSize 列宽
         * @return
         */
        private int[] getPosition(String key, String value, int pageSize, int xm) {
            int peer;   //计算毫米宽度对应的点位数
            double keyLength = getStringLength(key, pageSize, xm);
            double valueLength = getStringLength(value, pageSize, xm);
            //vale值长度放不下
            if (pageSize - keyLength < valueLength) {
                peer = (int) ((keyLength) * 8);
            } else {
                //计算value起始打印毫米数
                peer = (int) ((pageSize - keyLength - valueLength) * 8);
            }
            int nh = peer / 256 > 2 ? 2 : peer / 256;
            int nl = peer - nh * 256;
            return new int[]{nl, nh};
        }

        /**
         * 计算label的key-value格式坐标
         *
         * @param key
         * @param value
         * @param pageSize
         * @param xm
         * @return
         */
        public int[] getLabelPosition(String key, String value, int pageSize, int xm) {
            int peer;   //计算毫米宽度对应的点位数
            double keyLength = getStringLength(key, pageSize, xm);
            double valueLength = getStringLength(value, pageSize, xm);
            //vale值长度放不下
            if (pageSize - keyLength < valueLength) {
                peer = (int) ((keyLength) * 8);
            } else {
                //计算value起始打印毫米数
                peer = (int) ((pageSize - valueLength) * 8);
            }
//            int nh = peer / 256 > 2 ? 2 : peer / 256;
//            int nl = peer - nh * 256;
            return new int[]{peer, 0};
        }

        /**
         * 获取label打印文本,判断是否换行
         *
         * @param text
         * @param pageSize
         * @param xm
         * @return
         */
        public static String getLabelContent(String text, int pageSize, int xm) {
            //页宽mm数
            double length = (double) pageSize * 8;
            //字符串宽度
            double total = getStringLength(text, pageSize, xm) * 8;
            double singleCharWidth = getSingleCharWidth(pageSize, xm);
            if (length < total) {
                //不能够放下,换行
                int index = (int) (pageSize / singleCharWidth / 2);     //pageSize能够放下多少个字符
                return text.substring(0, index) + "\r\n" + getLabelContent(text.substring(index), pageSize, xm);
            }
            return text;
        }

        public static double getStringLength(String value, int pageSize, int xm) {
            int charOrNumberCount = 0;
            Double singleCharWidth = getSingleCharWidth(pageSize, xm);
            Pattern pattern = Pattern.compile("([^\\x00-\\xff]+)");
            Matcher matcher = pattern.matcher(value);
//            查找匹配出中文字符
            while (matcher.find()) {
                String ret = matcher.group();
                charOrNumberCount += ret.length();
            }
            return (value.length() - charOrNumberCount) * singleCharWidth + charOrNumberCount * singleCharWidth * 2;
        }

        /**
         * 打印分割线,用程伟dotline的方式
         *
         * @param printTemplate3
         * @param title
         * @return
         */
        public Builder addPartLine(PrintTemplate3<?> printTemplate3, String title, int xm, int ym, boolean bold) {
//            if (StringUtils.isEmpty(title)) {
//                double width = getSingleCharWidth(pageSize, xm);
//                for (int i = 0; i < pageSize / width; i++) {
//                    builder.append(printable.getPartLine(null));
//                }
//            }
            PrintRowDTO printRow = PrintRowDTO.builder()
                    .setAlign(AlignTypeEnum.LEFT.getValue())
                    .setContentType(ContentTypeEnum.SEPARATOR.getType())
                    .setXm(String.valueOf(xm))
                    .setBlod(bold).setYm(String.valueOf(ym))
                    .build();
            printRow.addPrintE(PrintEDTO.separator(printTemplate3.getPartLine(), title));
            printData.getBody().add(printRow);
            return this;
        }

        /**
         * 打印分割线,自己拼接--
         *
         * @param printTemplate3
         * @param title
         * @return
         */
        public Builder addPartLine(PrintTemplate3<?> printTemplate3, String title, int xm, int ym, int pageSize, boolean bold) {
            StringBuilder builder = new StringBuilder();
            if (StringUtils.isEmpty(title)) {
                double width = getSingleCharWidth(pageSize, xm);
                for (int i = 0; i < pageSize / width; i++) {
                    builder.append(printTemplate3.getPartLine());
                }
            }
            PrintRowDTO printRow = PrintRowDTO.builder()
                    .setAlign(AlignTypeEnum.LEFT.getValue())
                    .setContentType(ContentTypeEnum.SECTION.getType())
                    .setXm(String.valueOf(xm))
                    .setBlod(bold)
                    .setYm(String.valueOf(ym))
                    .build();
            printRow.addPrintE(PrintEDTO.section(builder.toString()));
            printData.getBody().add(printRow);
            return this;
        }

        /**
         * 增加打印行,按坐标,居左;以空格区分列
         *
         * @param columns  列名数组
         * @param pageSize 页大小
         * @param xm       放大倍数
         * @param column   列数
         * @return
         */
        public Builder addBodyLine(String[] columns, int pageSize, int column, int xm, int ym, boolean bold) {
            PrintRowDTO printRow = PrintRowDTO.builder()
                    .setAlign(AlignTypeEnum.LEFT.getValue())
                    .setContentType(ContentTypeEnum.COORDINATE_ROW.getType())
                    .setXm(String.valueOf(xm))
                    .setBlod(bold)
                    .setYm(String.valueOf(ym))
                    .build();
            printLine(pageSize, columns, xm, column, printRow);
//            printRow.addPrintE(body, 0, 0);
            printData.getBody().add(printRow);
            return this;
        }

        /**
         * 添加居中的一行数据
         *
         * @param content
         * @return
         */
        public Builder addBodyCenter(String content, int xm, int ym, boolean bold) {
            PrintRowDTO printRow = PrintRowDTO.builder()
                    .setAlign(AlignTypeEnum.CENTER.getValue())
                    .setContentType(ContentTypeEnum.SECTION.getType())
                    .setXm(String.valueOf(xm))
                    .setBlod(bold)
                    .setYm(String.valueOf(ym))
                    .build();
            printRow.addPrintE(PrintEDTO.section(content));
            printData.getBody().add(printRow);
            return this;
        }

        /**
         * 增加打印行,不按坐标(居左)
         *
         * @param contentTypeEnum
         * @return
         */
        public Builder addBodyLine(PrintEDTO printE, ContentTypeEnum contentTypeEnum, int xm, int ym, boolean bold) {
            PrintRowDTO printRow = PrintRowDTO.builder()
                    .setAlign(AlignTypeEnum.LEFT.getValue())
                    .setContentType(contentTypeEnum.getType())
                    .setXm(String.valueOf(xm))
                    .setBlod(bold)
                    .setYm(String.valueOf(ym))
                    .build();
            printRow.addPrintE(printE);
            printData.getBody().add(printRow);
            return this;
        }

        /**
         * 增加打印行,从左往右
         *
         * @param xm
         * @param ym
         * @return
         */
        public Builder addBodyLine(String conntent, ContentTypeEnum contentTypeEnum, int xm, int ym, boolean bold) {
            PrintRowDTO printRow = PrintRowDTO.builder()
                    .setAlign(AlignTypeEnum.LEFT.getValue())
                    .setContentType(contentTypeEnum.getType())
                    .setXm(String.valueOf(xm))
                    .setYm(String.valueOf(ym))
                    .setBlod(bold)
                    .build();
            printRow.addPrintE(PrintEDTO.section(conntent));
            printData.getBody().add(printRow);
            return this;
        }

        /**
         * 添加靠右对齐的行
         *
         * @param conntent
         * @param contentTypeEnum
         * @param xm
         * @param ym
         * @param bold
         * @return
         */
        public Builder addBodyRightLine(String conntent, ContentTypeEnum contentTypeEnum, int xm, int ym, boolean bold) {
            PrintRowDTO printRow = PrintRowDTO.builder()
                    .setAlign(AlignTypeEnum.RIGHT.getValue())
                    .setContentType(contentTypeEnum.getType())
                    .setXm(String.valueOf(xm))
                    .setYm(String.valueOf(ym))
                    .setBlod(bold).build();
            printRow.addPrintE(PrintEDTO.section(conntent));
            printData.getBody().add(printRow);
            return this;
        }

        /**
         * 增加打印行,两端对齐(key-value)
         *
         * @param left  左边的文本
         * @param right 右边的文本
         * @param xm
         * @param ym
         * @return
         */
        public Builder addBodyKeyValueLine(String left, String right, int xm, int ym, boolean bold, int pageSize) {
            PrintRowDTO printRow = PrintRowDTO.builder()
                    .setAlign(AlignTypeEnum.LEFT.getValue())
                    .setContentType(ContentTypeEnum.KEY_VALUE.getType())
                    .setXm(String.valueOf(xm))
                    .setBlod(bold)
                    .setYm(String.valueOf(ym))
                    .build();
            double stringLength = getStringLength(left + right, pageSize, xm);
//            Double singleCharWidth = getSingleCharWidth(pageSize, xm);
            if (stringLength >= pageSize) {//行宽不能够放下
//                int index = (int) (pageSize / singleCharWidth / 2);     //pageSize能够放下多少个字符
                if (pageSize - getStringLength(left, pageSize, xm) < 0) {
                    //左列key放不下,从左往右打
                    printRow.setContentType(ContentTypeEnum.SECTION.getType());
                    printRow.addPrintE(PrintEDTO.section(left));
                    printData.getBody().add(printRow);
                    PrintRowDTO printRow2 = PrintRowDTO.builder()
                            .setAlign(AlignTypeEnum.LEFT.getValue())
                            .setContentType(ContentTypeEnum.KEY_VALUE.getType())
                            .setXm(String.valueOf(xm)).setBlod(bold)
                            .setYm(String.valueOf(ym))
                            .build();
                    printRow2.addPrintE(PrintEDTO.keyValue(" ", right, "1"));
                    printData.getBody().add(printRow2);
                } else {
                    printRow.addPrintE(PrintEDTO.keyValue(left, "", "1"));
                    printData.getBody().add(printRow);
                    PrintRowDTO printRow2 = PrintRowDTO.builder()
                            .setAlign(AlignTypeEnum.LEFT.getValue())
                            .setContentType(ContentTypeEnum.KEY_VALUE.getType())
                            .setXm(String.valueOf(xm)).setBlod(bold)
                            .setYm(String.valueOf(ym))
                            .build();
                    printRow2.addPrintE(PrintEDTO.keyValue(" ", right, "1"));
                    printData.getBody().add(printRow2);
                }

               /* String str = left + right;
                printRow.addPrintE(PrintEDTO.keyValue(str.substring(0, index), "", "1"));
                printData.getBody().add(printRow);
                PrintRowDTO printRow2 = PrintRowDTO.builder().setAlign(AlignTypeEnum.LEFT.getValue()).setContentType(ContentTypeEnum.KEY_VALUE.getValue())
                        .setXm(String.valueOf(xm)).setBlod(bold).setYm(String.valueOf(ym)).build();
                printRow2.addPrintE(PrintEDTO.keyValue(str.substring(index), "", "1"));
                printData.getBody().add(printRow2);*/
            } else {
                printRow.addPrintE(PrintEDTO.keyValue(left, right, "1"));
                printData.getBody().add(printRow);
            }
            return this;
        }

        /**
         * key-value两端对齐方式打印坐标
         *
         * @param left
         * @param right
         * @param xm
         * @param ym
         * @param bold
         * @param pageSize
         * @return
         */
        public Builder addBodyKeyValueLine(String left, String right, int xm, int ym, int pageSize, boolean bold) {
            //key坐标
            int[] keyPosition = new int[]{0, 0};
            PrintRowDTO printRow = PrintRowDTO.builder()
                    .setAlign(AlignTypeEnum.LEFT.getValue())
                    .setContentType(ContentTypeEnum.COORDINATE_ROW.getType())
                    .setXm(String.valueOf(xm))
                    .setBlod(bold)
                    .setYm(String.valueOf(ym))
                    .build();
            printRow.addPrintE(PrintEDTO.coordinateRow(keyPosition[0], keyPosition[1], left));
            int[] valuePosition = getPosition(left, right, pageSize, xm);
            printRow.addPrintE(PrintEDTO.coordinateRow(valuePosition[0], valuePosition[1], right));
            printData.getBody().add(printRow);
            return this;
        }

        /**
         * 标签label打印key-value坐标
         *
         * @param left
         * @param right
         * @param xm
         * @param ym
         * @param pageSize
         * @param bold
         * @return
         */
        public Builder addLabelBodyKeyValue(String left, String right, int xm, int ym, int pageSize, boolean bold) {
            //key坐标
            int[] keyPosition = new int[]{0, 0};
            PrintRowDTO printRow = PrintRowDTO.builder()
                    .setAlign(AlignTypeEnum.LEFT.getValue())
                    .setContentType(ContentTypeEnum.COORDINATE_ROW.getType())
                    .setXm(String.valueOf(xm))
                    .setBlod(bold)
                    .setYm(String.valueOf(ym))
                    .build();
            printRow.addPrintE(PrintEDTO.coordinateRow(keyPosition[0], keyPosition[1], left));
            int[] valuePosition = getLabelPosition(left, right, pageSize, xm);
            printRow.addPrintE(PrintEDTO.coordinateRow(valuePosition[0], valuePosition[1], right));
            printData.getBody().add(printRow);
            return this;
        }

        public Builder setFooterLine(int xm, int ym, boolean bold) {
            PrintRowDTO printRow = PrintRowDTO.builder()
                    .setAlign(AlignTypeEnum.CENTER.getValue())
                    .setContentType(ContentTypeEnum.SECTION.getType())
                    .setXm(String.valueOf(xm))
                    .setBlod(bold)
                    .setYm(String.valueOf(ym))
                    .build();
            List<PrintRowDTO> list = new ArrayList<>();
            list.add(printRow);
            printData.setBody(list);
            return this;
        }

        public PrintData builder() {
            return this.printData;
        }
    }
}
