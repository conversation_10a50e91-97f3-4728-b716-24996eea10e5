package com.holder.saas.print.entity.enums;

import com.holderzone.saas.store.util.LocaleUtil;
import lombok.Getter;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

/**
 * <AUTHOR>
 * @create 2023-08-11
 * @description
 */
@Getter
public enum PrintContentEnum {

    TAKEOUT_ONLINE_PAYMENT("已在线支付"),
    TAKEOUT_IMMEDIATE_DELIVERY("立即送达"),
    TAKEOUT_MEAL_BOX_FEE("餐盒费"),
    TAKEOUT_DELIVERY_FEE("配送费"),
    ;

    private final String message;

    PrintContentEnum(String message){
        this.message = message;
    }
    public static String getLocale(String message){
        if( LocaleContextHolder.getLocale() == Locale.SIMPLIFIED_CHINESE){
            return message;
        }
        //若存在多语言则通过中文寻找对应语言
        for(PrintContentEnum localeMessageEnum :PrintContentEnum.values()){
            //若完全匹配
            if(localeMessageEnum.getMessage().equals(message)){
                return LocaleUtil.getMessage(localeMessageEnum.name());
            }
        }
        return message;
    }


}
