package com.holder.saas.print.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@TableName("hsp_print_record")
public class PrintRecordDO {

    /**
     * 主键id，自增
     */
    @TableId
    private Long id;

    /**
     * 打印UID
     */
    private String recordUid;

    /**
     * 打印记录guid
     */
    private String recordGuid;

    /**
     * 门店GUID
     */
    private String storeGuid;

    /**
     * 生成该打印记录的设备ID
     */
    private String deviceId;

    /**
     * 打印类型代码
     */
    private Integer invoiceType;

    /**
     * 打印机guid
     */
    private String printerGuid;

    /**
     * 0/打印中;1/打印成功;2/打印失败
     */
    private Integer printStatus;

    /**
     * 打印状态消息详情
     */
    private String printStatusMsg;

    /**
     * 打印内容，Json格式
     */
    private String printContent;

    /**
     * 创建该条记录的员工id
     */
    private String createStaffGuid;

    /**
     * 是否逻辑删除：0=未删除， 1=已删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录更新时间
     */
    private LocalDateTime gmtModified;
}