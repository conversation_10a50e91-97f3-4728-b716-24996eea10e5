package com.holder.saas.print.entity.read;

import com.holder.saas.print.entity.domain.PrinterAreaDO;
import com.holder.saas.print.entity.domain.PrinterItemDO;
import com.holder.saas.print.entity.domain.PrinterInvoiceDO;
import com.holder.saas.print.entity.domain.PrinterTableDO;
import com.holder.saas.print.entity.enums.PrintCutEnum;
import com.holder.saas.print.entity.enums.PrintPageEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class PrinterReadDO implements Serializable {

    private static final long serialVersionUID = 799133805086548343L;

    /**
     * 自增id
     */
    private Long id;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 设备编号
     */
    private String deviceId;

    /**
     * 打印机guid
     */
    private String printerGuid;

    /**
     * 打印机名称
     */
    private String printerName;

    /**
     * 打印业务类型; 参数: 1/前台打印; 2后厨打印; 3/标签打印
     *
     * @see com.holderzone.saas.store.enums.print.BusinessTypeEnum
     */
    private Integer businessType;

    /**
     * 打印机类型; 可选参数: 0/本机; 1/网络打印机; 2/usb打印机
     *
     * @see com.holderzone.saas.store.enums.print.PrinterTypeEnum
     */
    private Integer printerType;

    /**
     * 打印机ip
     */
    private String printerIp;

    /**
     * 打印端口
     */
    private Integer printerPort;

    /**
     * 打印次数
     */
    private Integer printCount;

    /**
     * 打印纸张类型; 参数: 1/80; 2/58; 3/40*30; 4/30*20
     *
     * @see PrintPageEnum
     */
    private String printPage;

    /**
     * 打印方式(切纸方式);  参数: 1/整单; 2/一菜一单; 3/一种类型一单; 4/一份数量一单; 默认1/整单
     *
     * @see PrintCutEnum
     */
    private Integer printCut;

    /**
     * 是否为主机
     */
    private Boolean isMaster;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 数据更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 所关联的invoiceType列表
     */
    private List<Integer> arrayOfInvoiceType;

    /**
     * 所关联的itemGuid列表
     */
    private List<String> arrayOfItemGuid;

    /**
     * 所关联的areaGuid列表
     */
    private List<String> arrayOfAreaGuid;

    /**
     * 所关联的tableGuid列表
     */
    private List<String> arrayOfTableGuid;

    /**
     * 所关联的invoice列表
     */
    private List<PrinterInvoiceDO> arrayOfInvoiceDO;

    /**
     * 所关联的item列表
     */
    private List<PrinterItemDO> arrayOfItemDO;

    /**
     * 所关联的area列表
     */
    private List<PrinterAreaDO> arrayOfAreaDO;

    /**
     * 所关联的桌台列表
     */
    private List<PrinterTableDO> arrayOfTableDO;

    /**
     * 是否需要打印挂起单
     */
    private Boolean isPrintHangUp;

    /**
     * 设备编号
     */
    private String deviceNo;

    /**
     * 设备密钥
     */
    private String deviceKey;

    /**
     * 设备厂商类型 1飞蛾 2商米
     */
    private Integer manufacturersType;

    /**
     * 设备型号
     */
    private String deviceModel;

    /**
     * 自动打印标识0否、1是
     */
    private Integer autoPrint;

    /**
     * 打印价格 0不打印 1打印
     */
    private Boolean printPriceType;

    /**
     * 区域类型
     *
     * @see com.holderzone.saas.store.enums.print.PrinterAreaTypeEnum
     */
    private Integer areaType;

    /**
     * 部分退款是否打印退菜单
     *
     * @see com.holder.saas.print.entity.enums.PrintPartRefundEnum
     */
    private Integer partRefundPrintFlag;
}