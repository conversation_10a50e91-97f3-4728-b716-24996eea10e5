package com.holder.saas.print.entity.ability;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.entity.PrintData;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.print.content.PrintStoredCashDTO;
import com.holderzone.saas.store.enums.print.ContentTypeEnum;
import org.springframework.stereotype.Component;

/**
 * 储值单
 *
 * <AUTHOR>
 * @date 2018/09/21 9:18
 */
@Component
@Deprecated
public class StoredCashTemplate3 implements PrintTemplate3<PrintStoredCashDTO> {
    @Override
    public PrintData getHeader(PrintStoredCashDTO printDto) {
        return PrintData.builder().setHeaderLine(printDto.getStoreName(), 2, 2, true).addBodyCenter(MultiLangUtils.get("stored_value_invoice_header"), 2, 2, false).builder();
    }

    @Override
    public PrintData getBody(PrintStoredCashDTO printDto, int pageSize) {
        PrintData.Builder printBuilder = PrintData.builder().addBodyLine(MultiLangUtils.get("transaction_id") + printDto.getSerialNumber(), ContentTypeEnum.SECTION, 1, 1, false)
                .addPartLine(this, null, 1, 1, false)
                .addBodyLine(MultiLangUtils.get("recharge_amount") + printDto.getRecharge(), ContentTypeEnum.SECTION, 1, 1, false)
                .addBodyLine(MultiLangUtils.get("gift_amount") + printDto.getPresented(), ContentTypeEnum.SECTION, 1, 1, false)
                .addBodyLine(MultiLangUtils.get("amount_of_arrival") + printDto.getArrival(), ContentTypeEnum.SECTION, 1, 1, false)
                .addBodyLine(MultiLangUtils.get("recharge_method") + printDto.getPayWay(), ContentTypeEnum.SECTION, 1, 1, false)
                .addPartLine(this, null, 1, 1, false)
                .addBodyLine(MultiLangUtils.get("recharge_card_number") + printDto.getCardNo(), ContentTypeEnum.SECTION, 1, 1, false)
                .addBodyLine(MultiLangUtils.get("current_balance") + printDto.getCurrentCash(), ContentTypeEnum.SECTION, 1, 1, false)
                .addBodyLine(MultiLangUtils.get("current_points") + printDto.getIntegration(), ContentTypeEnum.SECTION, 1, 1, false)
                .addBodyLine(MultiLangUtils.get("recharge_time") + DateTimeUtils.mills2String(printDto.getRechargeTime()), ContentTypeEnum.SECTION, 1, 1, false)
                .addPartLine(this, null, 1, 1, false);
        return printBuilder.builder();
    }

    @Override
    public PrintData getFooter(PrintStoredCashDTO printDto, int pageSize) {
        String storeName = StringUtils.isEmpty(printDto.getStoreAddress()) ? MultiLangUtils.get("undefined") : printDto.getStoreAddress();
        String tel = StringUtils.isEmpty(printDto.getTel()) ? MultiLangUtils.get("undefined") : printDto.getTel();
        PrintData.Builder builder = PrintData.builder().addBodyLine(MultiLangUtils.get("store_address") + storeName, ContentTypeEnum.SECTION, 1, 1, false)
                .addBodyLine(MultiLangUtils.get("store_phone") + tel, ContentTypeEnum.SECTION, 1, 1, false)
                .addPartLine(this, null, 1, 1, false);
        if (58 == pageSize) {
            builder.addBodyKeyValueLine(MultiLangUtils.get(Constant.OPERATOR), printDto.getOperatorStaffName(), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get(Constant.PRINT_TIME), DateTimeUtils.mills2String(System.currentTimeMillis(), "MM-dd HH:mm"), 1, 1, false, pageSize);
        } else {
            builder.addBodyKeyValueLine(MultiLangUtils.get(Constant.OPERATOR) + printDto.getOperatorStaffName(), MultiLangUtils.get(Constant.PRINT_TIME) + DateTimeUtils.mills2String(System.currentTimeMillis(), "MM-dd HH:mm"), 1, 1, false, pageSize);
        }
        return builder.builder();
    }

    @Override
    public String getPartLine() {
        return "-";
    }

    @Override
    public Class<PrintStoredCashDTO> getClassOfPrintDTO() {
        return PrintStoredCashDTO.class;
    }

    @Override
    public String getPrintFailedMessage(PrintStoredCashDTO printDto) {
        return MultiLangUtils.get("print_failed_msg_for_stored_value");
    }
}
