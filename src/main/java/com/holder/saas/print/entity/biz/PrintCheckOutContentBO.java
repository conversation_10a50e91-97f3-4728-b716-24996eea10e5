package com.holder.saas.print.entity.biz;

import com.google.common.collect.Sets;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.print.content.PrintCheckOutDTO;
import com.holderzone.saas.store.dto.print.format.CheckoutFormatDTO;
import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import lombok.Data;

import java.util.Objects;
import java.util.Set;

@Data
public class PrintCheckOutContentBO {

    private int pageSize;

    private Integer tradeMode;

    private String operatorStaffName;

    private FormatMetadata opStaffFormat;

    private FormatMetadata openTableTimeFormat;

    private FormatMetadata checkOutTimeFormat;

    private FormatMetadata createTimeFormat;

    private String openTableTimeStr;

    private String createTimeStr;

    private String checkOutTimeStr;

    private boolean timeIsYMD;

    public static PrintCheckOutContentBO of(int pageSize, PrintCheckOutDTO printDTO, CheckoutFormatDTO formatDTO) {
        PrintCheckOutContentBO printCheckOutContentBO = new PrintCheckOutContentBO();
        printCheckOutContentBO.setPageSize(pageSize);
        printCheckOutContentBO.setTradeMode(printDTO.getTradeMode());
        printCheckOutContentBO.setOperatorStaffName(printDTO.getOperatorStaffName());
        printCheckOutContentBO.setOpStaffFormat(formatDTO.getOperator());
        printCheckOutContentBO.setOpenTableTimeFormat(formatDTO.getOpenTableTime());
        printCheckOutContentBO.setCheckOutTimeFormat(formatDTO.getCheckoutTime());
        printCheckOutContentBO.setCreateTimeFormat(formatDTO.getPrintTime());

        printCheckOutContentBO.setCheckOutTimeStr(printDTO.getCheckOutTime(),printDTO.getStoreGuid(), formatDTO.getYearMonthDay());
        printCheckOutContentBO.setOpenTableTimeStr(printDTO.getOpenTableTime(),printDTO.getStoreGuid(), formatDTO.getYearMonthDay());
        printCheckOutContentBO.setCreateTimeStr(printDTO.getCreateTime(),printDTO.getStoreGuid(), formatDTO.getYearMonthDay());
        printCheckOutContentBO.setTimeIsYMD(HAS_YEARS_STORE_GUID.contains(printDTO.getStoreGuid()));
        return printCheckOutContentBO;
    }

    /**
     * 测试环境魅力城店，线上家婆抄手凯乐店
     */
    private final static Set<String> HAS_YEARS_STORE_GUID = Sets.newHashSet("6731814584836947968","2204111406268750000");

    private final static String MD_PATTER = "MM-dd HH:mm";

    private final static String YMD_PATTER = "yyyy-MM-dd HH:mm:ss";

    private void setOpenTableTimeStr(Long openTableTime,String storeGuid, FormatMetadata yearMonthDayFormat){
        String pattern;
        if (HAS_YEARS_STORE_GUID.contains(storeGuid)) {
            pattern = YMD_PATTER;
        } else {
            if (Objects.nonNull(yearMonthDayFormat) && yearMonthDayFormat.isEnable()) {
                pattern = YMD_PATTER;
            } else {
                pattern = MD_PATTER;
            }
        }
        this.openTableTimeStr = DateTimeUtils.mills2String(openTableTime, pattern);
    }

    private void setCreateTimeStr(Long createTime,String storeGuid, FormatMetadata yearMonthDayFormat){
        String pattern;
        if (HAS_YEARS_STORE_GUID.contains(storeGuid)) {
            pattern = YMD_PATTER;
        } else {
            if (Objects.nonNull(yearMonthDayFormat) && yearMonthDayFormat.isEnable()) {
                pattern = YMD_PATTER;
            } else {
                pattern = MD_PATTER;
            }
        }
        this.createTimeStr = DateTimeUtils.mills2String(createTime, pattern);
    }

    private void setCheckOutTimeStr(Long checkOutTime,String storeGuid, FormatMetadata yearMonthDayFormat){
        String pattern;
        if (HAS_YEARS_STORE_GUID.contains(storeGuid)) {
            pattern = YMD_PATTER;
        } else {
            if (Objects.nonNull(yearMonthDayFormat) && yearMonthDayFormat.isEnable()) {
                pattern = YMD_PATTER;
            } else {
                pattern = MD_PATTER;
            }
        }
        this.checkOutTimeStr = DateTimeUtils.mills2String(checkOutTime, pattern);
    }

    public boolean isEnable(){
        return this.opStaffFormat.isEnable() || this.openTableTimeFormat.isEnable()
                || this.checkOutTimeFormat.isEnable() || this.createTimeFormat.isEnable();
    }
    public boolean opStaffIsEnable(){
        return this.opStaffFormat.isEnable();
    }

    public boolean openTableTimeEnable(){
        return this.openTableTimeFormat.isEnable();
    }
    public boolean checkOutTimeEnable(){
        return this.checkOutTimeFormat.isEnable();
    }
    public boolean createTimeEnable(){
        return this.createTimeFormat.isEnable();
    }
}
