package com.holder.saas.print.entity.enums;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public enum ItemActualTypeEnum {

    // 折扣价
    DISCOUNT(1, "折扣价{}"),
    // 会员价
    MEMBER(2, "会员价{}");

    private int code;
    private String desc;

    ItemActualTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (ItemActualTypeEnum c : ItemActualTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 倒叙设置{}值
     */
    public static String getActualInfo(int code, BigDecimal... args) {
        for (ItemActualTypeEnum c : ItemActualTypeEnum.values()) {
            if (c.getCode() == code) {
                return replaceAmount(c.desc, args);
            }
        }
        return "";
    }

    public static String replaceAmount(String desc, BigDecimal... args) {
        int i = 0;
        // 匹配{} 的表达式
        String pattern = "\\{[^}]*\\}";
        Pattern p = Pattern.compile(pattern);
        Matcher m = p.matcher(desc);
        while (m.find()) {
            // 当字符串中有匹配到 {} 时
            desc = desc.replaceFirst("\\{[^}]*\\}", handleBigDecimal(args[i]));   // 替换 {} 和里面的内容
            i++;
        }
        return desc;
    }

    private static String handleBigDecimal(BigDecimal amount) {
        if (Objects.isNull(amount)) {
            return "";
        }
        return amount.setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
    }
}
