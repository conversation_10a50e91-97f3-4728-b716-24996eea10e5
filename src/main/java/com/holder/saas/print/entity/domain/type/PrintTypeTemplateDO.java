package com.holder.saas.print.entity.domain.type;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 打印分类模版表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("hsp_print_type_template")
@ApiModel(value = "HspPrintTypeTemplate对象", description = "打印分类模版表")
public class PrintTypeTemplateDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "模版guid")
    private String guid;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "是否删除 0：false,1:true")
    @TableLogic
    private Boolean isDelete;

    @ApiModelProperty(value = "状态 0：已禁用,1:已启用")
    private Boolean isEnable;

    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

    @ApiModelProperty(value = "模版名称")
    private String name;

    /**
     * 逗号分割的字符串
     * @see InvoiceTypeEnum
     */
    @ApiModelProperty(value = "应用单据-InvoiceTypeEnum")
    private String invoiceType;

    @ApiModelProperty(value = "是否全部门店 0：否,1:是")
    private Boolean isAllStore;


}
