package com.holder.saas.print.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("hsp_printer_format")
public class PrinterFormatDO implements Serializable {

    private static final long serialVersionUID = 7204953273824470912L;

    /**
     * 自增id
     */
    @TableId
    private Long id;

    /**
     * 唯一标识
     */
    private String guid;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 单据类型
     */
    private Integer invoiceType;

    /**
     * 格式的Json字符串
     * 目前是TEXT，考虑使用VARCHAR(2000)，因为目前最大的是占1010字符的结账单
     * 如果使用Compress，结账单字符占用仅仅207
     */
    private String formatJsonString;

    /**
     * 是否已启用
     * 0=未启用
     * 1=已启用
     */
    private Boolean isEnable;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
