package com.holder.saas.print.entity.biz;

import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import lombok.Data;

@Data
public class TypeGroupKey {

    private String guid;

    private String name;

    public static TypeGroupKey of(PrintItemRecord printItemRecord) {
        TypeGroupKey typeGroupKey = new TypeGroupKey();
        typeGroupKey.setGuid(printItemRecord.getItemTypeGuid());
        typeGroupKey.setName(printItemRecord.getItemTypeName());
        return typeGroupKey;
    }
}
