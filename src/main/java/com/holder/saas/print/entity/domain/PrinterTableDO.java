package com.holder.saas.print.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 打印区域桌台
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@TableName("hsp_printer_table")
public class PrinterTableDO implements Serializable {

    private static final long serialVersionUID = -7004841021433658238L;

    /**
     * 主键id，自增
     */
    @TableId
    private Long id;

    /**
     * 唯一标识
     */
    private String guid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 打印机guid
     */
    private String printerGuid;

    /**
     * 桌台guid
     */
    private String tableGuid;

    /**
     * 桌台名称
     */
    private String tableName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}