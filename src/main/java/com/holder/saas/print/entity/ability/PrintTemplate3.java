package com.holder.saas.print.entity.ability;

import com.holder.saas.print.entity.PrintData;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.deprecate.PrintRowDTO;
import com.holderzone.saas.store.enums.print.ContentTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 打印功能接口
 *
 * <AUTHOR>
 * @date 2018/09/18 20:04
 */
@Deprecated
public interface PrintTemplate3<T extends PrintDTO> {

    Logger LOGGER = LoggerFactory.getLogger(PrintTemplate3.class);

    /**
     * 获取标题
     *
     * @return
     */
    PrintData getHeader(T printDto);

    /**
     * 获取单据详细信息
     *
     * @return
     */
    PrintData getBody(T printDto, int pageSize);

    /**
     * 打印footer
     *
     * @return
     */
    PrintData getFooter(T printDto, int pageSize);

    /**
     * 分割线
     *
     * @return
     */
    String getPartLine();

    /**
     * 获取打印失败语音播报的内容
     *
     * @param printDto
     * @return
     */
    String getPrintFailedMessage(T printDto);

    /**
     * 获取打印原始内容的Class类型
     *
     * @return
     */
    Class<T> getClassOfPrintDTO();

    /**
     * 获取所有的打印行
     *
     * @return
     */
    default List<PrintRowDTO> getAllPrintRow(List<PrintData> printDataList) {
        List<PrintRowDTO> list = new ArrayList<>();
        printDataList.forEach(printData -> list.addAll(printData.getBody()));
        return list;
    }

    /**
     * 根据菜品列表获取菜品详情部分打印内容
     *
     * @return
     */
    default void getItemListContent(List<PrintItemRecord> list, PrintData.Builder builder, int pageSize, PrintTemplate3 printTemplate3) {
        builder.addPartLine(printTemplate3, null, 1, 1, false);
        builder.addBodyLine(new String[]{MultiLangUtils.get("item"), MultiLangUtils.get("price"), MultiLangUtils.get("quantity"), MultiLangUtils.get("item_sum")}, pageSize, 4, 1, 1, false);
        builder.addPartLine(printTemplate3, null, 1, 1, false);
        list.forEach(itemRecord -> {
            String numberStr = itemRecord.getNumber().toString();
            if (!itemRecord.getAsWeight()) {
                numberStr = String.valueOf(itemRecord.getNumber().intValue());
            }
            //小计
            BigDecimal subTotal = itemRecord.getPropertyPrice() == null ? BigDecimal.ZERO : itemRecord.getPropertyPrice().multiply(new BigDecimal(numberStr));
            //属性加价
            subTotal = subTotal.add(itemRecord.getSubtotal() == null ? BigDecimal.ZERO : itemRecord.getSubtotal());
            if (itemRecord.getSubItemRecords() != null && !itemRecord.getSubItemRecords().isEmpty()) {
                for (PrintItemRecord subItem : itemRecord.getSubItemRecords()) {
                    subTotal = subTotal.add(subItem.getPropertyPrice() == null
                            ? BigDecimal.ZERO
                            : subItem.getPropertyPrice().multiply(subItem.getNumber())
                            .multiply(itemRecord.getNumber())
                    );
                }
            }
            builder.addBodyLine(new String[]{itemRecord.getItemName(),
                            String.valueOf(itemRecord.getPrice().setScale(2, RoundingMode.HALF_EVEN)),
                            numberStr, String.valueOf(subTotal.setScale(2, RoundingMode.HALF_EVEN))},
                    pageSize, 4, 1, 1, false);
            if (!StringUtils.isEmpty(itemRecord.getPropertyPrice()) && !itemRecord.getPropertyPrice().equals(BigDecimal.ZERO)) {
                builder.addBodyLine(new String[]{"  " + MultiLangUtils.get("prop_fee"), itemRecord.getPropertyPrice().setScale(2, RoundingMode.HALF_EVEN).toString()}, pageSize, 4, 1, 1, false);
            }
            if (!StringUtils.isEmpty(itemRecord.getProperty())) {
                builder.addBodyLine("  " + itemRecord.getProperty(), ContentTypeEnum.SECTION, 1, 1, true);
            }
            //套餐商品
            if (itemRecord.getSubItemRecords() != null && !itemRecord.getSubItemRecords().isEmpty()) {
                itemRecord.getSubItemRecords().forEach(subItem -> {
                    String number = subItem.getNumber().toString();
                    if (!subItem.getAsWeight()) {
                        number = String.valueOf(subItem.getNumber().intValue());
                    }
                    builder.addBodyLine(new String[]{"  -|" + subItem.getItemName(), number}, pageSize, 2, 1, 1, false);
                    if (!StringUtils.isEmpty(subItem.getPropertyPrice()) && !subItem.getPropertyPrice().equals(BigDecimal.ZERO)) {
                        builder.addBodyLine(new String[]{"    " + MultiLangUtils.get("prop_fee"), subItem.getPropertyPrice().toString()}, pageSize, 4, 1, 1, false);
                    }
                    if (!StringUtils.isEmpty(subItem.getProperty())) {
                        builder.addBodyLine("    " + subItem.getProperty(), ContentTypeEnum.SECTION, 1, 1, true);
                    }
                });
            }
        });
    }

    /**
     * 根据菜品列表获取后厨打印菜品需要的详情部分打印内容
     *
     * @return
     */
    default void getKitchenItemListContent(List<PrintItemRecord> list, PrintData.Builder builder, PrintTemplate3 printTemplate3, boolean isCancel, int pageSize) {
        builder.addPartLine(printTemplate3, null, 1, 1, false);
        builder.addBodyKeyValueLine(MultiLangUtils.get("item"), MultiLangUtils.get("quantity"), 2, 2, false, pageSize);
        builder.addPartLine(printTemplate3, null, 1, 1, false);
        list.forEach(itemRecord -> {
            try {
                String numberStr = itemRecord.getNumber().toString();
                if (!itemRecord.getAsWeight()) {
                    numberStr = String.valueOf(itemRecord.getNumber().intValue());
                }
                builder.addBodyKeyValueLine(itemRecord.getItemName(), numberStr + (StringUtils.isEmpty(itemRecord.getUnit()) || "null".equals(itemRecord.getUnit()) ? "" : "/" + itemRecord.getUnit()), 2, 2, false, pageSize);
                if (!StringUtils.isEmpty(itemRecord.getProperty()) && !isCancel) {
                    builder.addBodyLine("  " + MultiLangUtils.get("sub_prop") + itemRecord.getProperty(), ContentTypeEnum.SECTION, 2, 2, true);
                }
                if (!StringUtils.isEmpty(itemRecord.getRemark()) && !isCancel) {
                    builder.addBodyLine("  " + MultiLangUtils.get("remark") + itemRecord.getRemark(), ContentTypeEnum.SECTION, 2, 2, true);
                }
                if (itemRecord.getSubItemRecords() != null && !itemRecord.getSubItemRecords().isEmpty()) {
                    itemRecord.getSubItemRecords().forEach(subItem -> {
                        String number = subItem.getNumber().toString();
                        if (!subItem.getAsWeight()) {
                            number = String.valueOf(subItem.getNumber().intValue());
                        }
                        builder.addBodyKeyValueLine("  -|" + subItem.getItemName(), number + "/" + subItem.getUnit(), 2, 2, false, pageSize);
                        if (!StringUtils.isEmpty(subItem.getPropertyPrice()) && !subItem.getPropertyPrice().equals(BigDecimal.ZERO) && !isCancel) {
                            builder.addBodyLine("  " + MultiLangUtils.get("sub_prop") + subItem.getProperty(), ContentTypeEnum.SECTION, 2, 2, true);
                        }
                        if (!StringUtils.isEmpty(subItem.getRemark()) && !isCancel) {
                            builder.addBodyLine("  " + MultiLangUtils.get("remark") + subItem.getRemark(), ContentTypeEnum.SECTION, 2, 2, true);
                        }
                    });
                }
            } catch (Exception e) {
                System.out.println(JacksonUtils.writeValueAsString(itemRecord));
                LOGGER.error("菜品解析失败", e);
            }
        });
//        builder.addBodyLine(new String[]{"商品", "数量"}, pageSize, 2, 2, 2, false);
    }
}
