package com.holder.saas.print.entity.domain.type;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 打印分类模版关联分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("hsp_print_type_template_r_type")
@ApiModel(value="HspPrintTypeTemplateRType对象", description="打印分类模版关联分类表")
public class PrintTypeTemplateRTypeDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "'分类guid'")
    private String guid;

    @ApiModelProperty(value = "模版guid")
    private String templateGuid;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;

    @TableLogic
    @ApiModelProperty(value = "是否删除 0：false,1:true")
    private Boolean isDelete;

    @ApiModelProperty(value = "分类名称")
    private String name;


}
