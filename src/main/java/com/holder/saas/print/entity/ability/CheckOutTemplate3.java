package com.holder.saas.print.entity.ability;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.entity.PrintData;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.TradeModeUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.print.content.PrintCheckOutDTO;
import com.holderzone.saas.store.dto.print.deprecate.PrintEDTO;
import com.holderzone.saas.store.enums.print.ContentTypeEnum;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 结账单
 *
 * <AUTHOR>
 * @date 2018/09/19 19:56
 * @deprecated 我也不晓得，我是来清除sonar的
 */
@Component
@Deprecated
public class CheckOutTemplate3 implements PrintTemplate3<PrintCheckOutDTO> {
    @Override
    public PrintData getHeader(PrintCheckOutDTO printDto) {
        String markName = TradeModeUtils.getMarkName(printDto.getTradeMode());
        return PrintData.builder().setHeaderLine(printDto.getStoreName(), 2, 2, true).addBodyCenter(MultiLangUtils.get("checkout_invoice_header"), 2, 2, false)
                .addBodyLine(PrintEDTO.section(markName + printDto.getMarkNo()), ContentTypeEnum.SECTION, 2, 2, true)
                .builder();
    }

    @Override
    public PrintData getBody(PrintCheckOutDTO printDto, int pageSize) {
        PrintData.Builder printDataBuilder = PrintData.builder().addPartLine(this, null, 1, 1, false);
        if (58 == pageSize) {
            printDataBuilder.addBodyKeyValueLine(MultiLangUtils.get(Constant.ORDER_NUMBER), printDto.getOrderNo(), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get(Constant.CUSTOMER_NUMBER), "" + printDto.getPersonNumber(), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get("cashier"), printDto.getOperatorStaffName(), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get("checkout"), DateTimeUtils.mills2String(printDto.getOpenTableTime(), "MM-dd HH:mm"), 1, 1, false, pageSize);
        } else {
            printDataBuilder.addBodyKeyValueLine(MultiLangUtils.get(Constant.ORDER_NUMBER) + printDto.getOrderNo(), MultiLangUtils.get(Constant.CUSTOMER_NUMBER) + printDto.getPersonNumber(), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get("cashier") + printDto.getOperatorStaffGuid(), " " + MultiLangUtils.get("checkout") + DateTimeUtils.mills2String(printDto.getOpenTableTime(), "MM-dd HH:mm"), 1, 1, false, pageSize);
        }
//                .addPartLine(this, null, 1, 1).addBodyLine("商品 单价 数量 小计", pageSize, 4, 1, 1);
        //各类商品明细
        if (printDto.getItemRecordList() != null && !printDto.getItemRecordList().isEmpty()) {
            getItemListContent(printDto.getItemRecordList(), printDataBuilder, pageSize, this);
        }
        printDataBuilder.addPartLine(this, null, 1, 1, false);
        //商品总额
        printDataBuilder.addBodyKeyValueLine(MultiLangUtils.get("item_total"), String.valueOf(printDto.getTotal()),
                1, 1, false, pageSize);
        //附加费
        if (printDto.getAdditionalChargeList() != null && !printDto.getAdditionalChargeList().isEmpty()) {
            double sum = printDto.getAdditionalChargeList().stream()
                    .mapToDouble(additionalCharge -> additionalCharge.getChargeValue().doubleValue()).sum();
            printDataBuilder.addPartLine(this, MultiLangUtils.get("additional_fee")
                    + BigDecimal.valueOf(sum).setScale(2, RoundingMode.HALF_EVEN), 1, 1, true);
            printDto.getAdditionalChargeList().forEach(additionalCharge ->
                    printDataBuilder.addBodyKeyValueLine(additionalCharge.getChargeName(),
                            String.valueOf(additionalCharge.getChargeValue().setScale(2, RoundingMode.HALF_EVEN)),
                            1, 1, false, pageSize)
            );
        }
        //优惠折扣
        if (printDto.getReduceRecordList() != null && !printDto.getReduceRecordList().isEmpty()) {
            //过滤大于0的列表
            double sum = printDto.getReduceRecordList().stream()
                    .mapToDouble(value -> value.getAmount().doubleValue()).sum();
            printDataBuilder.addPartLine(this, MultiLangUtils.get("amount_discounted")
                    + BigDecimal.valueOf(sum).setScale(2, RoundingMode.HALF_EVEN), 1, 1, true);
            printDto.getReduceRecordList().forEach(reduce -> {
                if (reduce.getAmount() != null && reduce.getAmount().doubleValue() != 0) {
                    printDataBuilder.addBodyKeyValueLine(reduce.getName(),
                            String.valueOf(reduce.getAmount().setScale(2, RoundingMode.HALF_EVEN)),
                            1, 1, false, pageSize);
                }
            });
        }
        printDataBuilder.addPartLine(this, null, 1, 1, false);
        printDataBuilder.addBodyKeyValueLine(MultiLangUtils.get("amount_should_pay"),
                String.valueOf(printDto.getPayAble().setScale(2, RoundingMode.HALF_EVEN)),
                2, 2, false, pageSize);
        printDataBuilder.addPartLine(this, null, 1, 1, false);
        printDataBuilder.addBodyKeyValueLine(MultiLangUtils.get("amount_actually_paid"),
                String.valueOf(printDto.getActuallyPay().setScale(2, RoundingMode.HALF_EVEN)),
                2, 2, false, pageSize);
        //支付方式
        if (printDto.getPayRecordList() != null && !printDto.getPayRecordList().isEmpty()) {
            printDataBuilder.addPartLine(this, null, 1, 1, false);
            printDto.getPayRecordList().forEach(payRecord ->
                    printDataBuilder.addBodyKeyValueLine(payRecord.getPayName(),
                            payRecord.getAmount().setScale(2, RoundingMode.HALF_EVEN).toString(),
                            1, 1, false, pageSize)
            );
        }
        return printDataBuilder.builder();
    }

    @Override
    public PrintData getFooter(PrintCheckOutDTO printDto, int pageSize) {
        String storeName = StringUtils.isEmpty(printDto.getStoreAddress()) ? MultiLangUtils.get("undefined") : printDto.getStoreAddress();
        String tel = StringUtils.isEmpty(printDto.getTel()) ? MultiLangUtils.get("undefined") : printDto.getTel();
        PrintData.Builder builder = PrintData.builder().addPartLine(this, null, 1, 1, false).addBodyLine(MultiLangUtils.get("store_address") + storeName, ContentTypeEnum.SECTION, 1, 1, false)
                .addBodyLine(MultiLangUtils.get("store_phone") + tel, ContentTypeEnum.SECTION, 1, 1, false)
                .addPartLine(this, null, 1, 1, false);
        if (58 == pageSize) {
            builder.addBodyKeyValueLine(MultiLangUtils.get(Constant.OPERATOR), printDto.getOperatorStaffName(), 1, 1, false, pageSize)
                    .addBodyKeyValueLine(MultiLangUtils.get(Constant.PRINT_TIME), DateTimeUtils.mills2String(System.currentTimeMillis(), "MM-dd HH:mm"), 1, 1, false, pageSize);
        } else {
            builder.addBodyKeyValueLine(MultiLangUtils.get(Constant.OPERATOR) + printDto.getOperatorStaffName(), MultiLangUtils.get(Constant.PRINT_TIME) + DateTimeUtils.mills2String(System.currentTimeMillis(), "MM-dd HH:mm"), 1, 1, false, pageSize);

        }
        return builder.builder();
    }

    @Override
    public String getPartLine() {
        return "-";
    }

    @Override
    public String getPrintFailedMessage(PrintCheckOutDTO printDto) {
        return MultiLangUtils.get("checkout_invoice")
                + String.format(MultiLangUtils.get("print_failed_please_handle_it_promptly"), printDto.getMarkNo());
    }

    @Override
    public Class<PrintCheckOutDTO> getClassOfPrintDTO() {
        return PrintCheckOutDTO.class;
    }
}
