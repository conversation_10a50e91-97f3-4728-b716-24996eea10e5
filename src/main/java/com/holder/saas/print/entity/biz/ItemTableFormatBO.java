package com.holder.saas.print.entity.biz;

import com.holderzone.saas.store.dto.print.format.*;
import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@SuppressWarnings("Duplicates")
public class ItemTableFormatBO {

    private FormatMetadata layout;

    private FormatMetadata typeTotal;

    private FormatMetadata itemName;

    private FormatMetadata itemPrice;

    private FormatMetadata itemNumber;

    private FormatMetadata itemTotal;

    private FormatMetadata itemDiscountAfterTotal;

    private FormatMetadata itemUnit;

    private FormatMetadata itemProperty;

    private FormatMetadata itemRemark;

    private FormatMetadata itemNumTotal;

    private FormatMetadata itemSumTotal;

    /**
     * 附加费明细
     */
    private FormatMetadata additionalCharge;

    /**
     * 附加费合计
     */
    private FormatMetadata additionalChargeTotal;

    private FormatMetadata showMakeNum;

    private FormatMetadata packageContent;

    private boolean isTakeOut = false;

    /**
     * 是否退款
     */
    private boolean isRefund = false;

    /**
     * 是否退款操作的退款单，另有反结账操作的
     */
    private boolean isRefundRefund = false;

    /**
     * 是否需要套餐去重
     */
    private boolean isDuplicatePackage = false;

    public static ItemTableFormatBO of(ItemDetailFormatDTO itemDetailFormatDTO) {
        ItemTableFormatBO itemTableFormatBo = new ItemTableFormatBO();
        itemTableFormatBo.setLayout(itemDetailFormatDTO.getItemLayout());
        itemTableFormatBo.setTypeTotal(itemDetailFormatDTO.getTypeTotal());
        itemTableFormatBo.setItemPrice(itemDetailFormatDTO.getItemPrice());
        itemTableFormatBo.setItemTotal(itemDetailFormatDTO.getItemTotal());
        itemTableFormatBo.setItemProperty(itemDetailFormatDTO.getItemProperty());
        itemTableFormatBo.setItemRemark(itemDetailFormatDTO.getItemRemark());
        itemTableFormatBo.setItemNumTotal(new FormatMetadata().setEnable(true));
        itemTableFormatBo.setItemSumTotal(itemDetailFormatDTO.getItemSumTotal());
        itemTableFormatBo.setAdditionalCharge(itemDetailFormatDTO.getAdditionalCharge());
        itemTableFormatBo.setAdditionalChargeTotal(itemDetailFormatDTO.getAdditionalChargeTotal());
        return itemTableFormatBo;
    }

    public static ItemTableFormatBO of(PreCheckoutFormatDTO preCheckoutFormatDTO) {
        ItemTableFormatBO itemTableFormatBo = new ItemTableFormatBO();
        itemTableFormatBo.setLayout(preCheckoutFormatDTO.getItemLayout());
        itemTableFormatBo.setTypeTotal(preCheckoutFormatDTO.getTypeTotal());
        itemTableFormatBo.setItemPrice(preCheckoutFormatDTO.getItemPrice());
        itemTableFormatBo.setItemTotal(preCheckoutFormatDTO.getItemTotal());
        itemTableFormatBo.setItemDiscountAfterTotal(preCheckoutFormatDTO.getItemDiscountAfterTotal());
        itemTableFormatBo.setItemProperty(preCheckoutFormatDTO.getItemProperty());
        itemTableFormatBo.setItemRemark(preCheckoutFormatDTO.getItemRemark());
        itemTableFormatBo.setItemNumTotal(new FormatMetadata().setEnable(true));
        itemTableFormatBo.setItemSumTotal(preCheckoutFormatDTO.getItemSumTotal());
        itemTableFormatBo.setAdditionalCharge(preCheckoutFormatDTO.getAdditionalCharge());
        itemTableFormatBo.setAdditionalChargeTotal(preCheckoutFormatDTO.getAdditionalChargeTotal());
        return itemTableFormatBo;
    }

    public static ItemTableFormatBO of(CheckoutFormatDTO checkoutFormatDTO) {
        ItemTableFormatBO itemTableFormatBo = new ItemTableFormatBO();
        itemTableFormatBo.setLayout(checkoutFormatDTO.getItemLayout());
        itemTableFormatBo.setTypeTotal(checkoutFormatDTO.getTypeTotal());
        itemTableFormatBo.setItemPrice(checkoutFormatDTO.getItemPrice());
        itemTableFormatBo.setItemTotal(checkoutFormatDTO.getItemTotal());
        itemTableFormatBo.setItemDiscountAfterTotal(checkoutFormatDTO.getItemDiscountAfterTotal());
        itemTableFormatBo.setItemProperty(checkoutFormatDTO.getItemProperty());
        itemTableFormatBo.setItemRemark(checkoutFormatDTO.getItemRemark());
        itemTableFormatBo.setItemNumTotal(new FormatMetadata().setEnable(true));
        itemTableFormatBo.setItemSumTotal(checkoutFormatDTO.getItemSumTotal());
        itemTableFormatBo.setAdditionalCharge(checkoutFormatDTO.getAdditionalCharge());
        itemTableFormatBo.setAdditionalChargeTotal(checkoutFormatDTO.getAdditionalChargeTotal());
        itemTableFormatBo.setShowMakeNum(checkoutFormatDTO.getShowMakeNum());
        return itemTableFormatBo;
    }

    public static ItemTableFormatBO of(TakeoutFormatDTO takeoutFormatDTO) {
        ItemTableFormatBO itemTableFormatBo = new ItemTableFormatBO();
        itemTableFormatBo.setLayout(takeoutFormatDTO.getItemLayout());
        itemTableFormatBo.setTypeTotal(takeoutFormatDTO.getTypeTotal());
        itemTableFormatBo.setItemNumTotal(takeoutFormatDTO.getTypeTotal());
        itemTableFormatBo.setItemPrice(takeoutFormatDTO.getItemPrice());
        itemTableFormatBo.setItemTotal(takeoutFormatDTO.getItemTotal());
        itemTableFormatBo.setItemProperty(new FormatMetadata().setEnable(true));
        itemTableFormatBo.setItemRemark(new FormatMetadata().setEnable(true).setSize(1));
        itemTableFormatBo.setItemSumTotal(takeoutFormatDTO.getItemSumTotal());
        itemTableFormatBo.setTakeOut(Boolean.TRUE);
        itemTableFormatBo.setPackageContent(takeoutFormatDTO.getPackageContent());
        return itemTableFormatBo;
    }

    public static ItemTableFormatBO of(RefundFormatDTO refundFormatDTO) {
        ItemTableFormatBO itemTableFormatBo = new ItemTableFormatBO();
        itemTableFormatBo.setLayout(refundFormatDTO.getItemLayout());
        itemTableFormatBo.setItemTotal(refundFormatDTO.getItemTotal());
        itemTableFormatBo.setItemPrice(refundFormatDTO.getItemPrice());
        itemTableFormatBo.setItemProperty(refundFormatDTO.getItemProperty());
        // 附加费
        itemTableFormatBo.setAdditionalCharge(refundFormatDTO.getAdditionalCharge());
        itemTableFormatBo.setAdditionalChargeTotal(refundFormatDTO.getAdditionalChargeTotal());
        // 退款标识
        itemTableFormatBo.setRefund(true);
        // 不显示备注
        itemTableFormatBo.setItemRemark(new FormatMetadata().setEnable(false));
        // 不显示单价
        itemTableFormatBo.setItemPrice(new FormatMetadata().setEnable(false));
        return itemTableFormatBo;
    }
}
