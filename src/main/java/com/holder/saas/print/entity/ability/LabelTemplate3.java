package com.holder.saas.print.entity.ability;

import com.holder.saas.print.entity.PrintData;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holderzone.saas.store.dto.print.content.PrintLabelDTO;
import com.holderzone.saas.store.enums.print.ContentTypeEnum;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.RoundingMode;

/**
 * 标签
 *
 * <AUTHOR>
 * @date 2018/9/25 16:31
 */
@Component
@Deprecated
public class LabelTemplate3 implements PrintTemplate3<PrintLabelDTO> {
    @Override
    public PrintData getHeader(PrintLabelDTO printDto) {
        return PrintData.builder().builder();
    }

    @Override
    public PrintData getBody(PrintLabelDTO printDto, int pageSize) {

        PrintData.Builder printDataBuilder = PrintData.builder()
                .addLabelBodyKeyValue(printDto.getSerialNumber(),
                        printDto.getCurrentNo() + "/" + printDto.getTotalNo() + " ", 1, 1, pageSize, false)
                .addPartLine(this, null, 1, 1, pageSize, false);
        printDto.getItemRecordList().forEach(itemRecord -> {
           /* double subTotal = itemRecord.getPrice();
            if (itemRecord.getSubItemRecords() != null && !itemRecord.getSubItemRecords().isEmpty()) {
                for (ItemRecord record : itemRecord.getSubItemRecords()) {
                    subTotal+=record.getPropertyPrice() == null ? BigDecimal.ZERO.doubleValue(): record.getPropertyPrice().doubleValue();
                }
            }*/
            buildLabelPrintContent(pageSize, printDataBuilder, itemRecord.getItemName());
//            printDataBuilder.addBodyLine(PrintData.Builder.getLabelContent(itemRecord.getItemName(), pageSize, 1), ContentTypeEnum.SECTION, 1, 1, false);
            if (itemRecord.getSubItemRecords() != null && !itemRecord.getSubItemRecords().isEmpty()) {
                itemRecord.getSubItemRecords().forEach(subItem -> {
                    buildLabelPrintContent(pageSize, printDataBuilder, "  -|" + subItem.getItemName());
//                    printDataBuilder.addBodyLine(PrintData.Builder.getLabelContent("  -|" + subItem.getItemName(), pageSize, 1), ContentTypeEnum.SECTION, 1, 1, false);
                });
            }
            if (!StringUtils.isEmpty(itemRecord.getRemark())) {
                buildLabelPrintContent(pageSize, printDataBuilder, MultiLangUtils.get("remark") + itemRecord.getRemark());
//                printDataBuilder.addBodyLine(PrintData.Builder.getLabelContent("备注:" + itemRecord.getRemark(), pageSize, 1), ContentTypeEnum.SECTION, 1, 1, false);
            }
            if (!StringUtils.isEmpty(itemRecord.getPrice())) {
                printDataBuilder.addBodyLine(" "+ MultiLangUtils.get("money_unit") + itemRecord.getPrice().setScale(2, RoundingMode.HALF_EVEN), ContentTypeEnum.SECTION, 1, 1, true);
            }
        });
        return printDataBuilder.builder();
    }

    @Override
    public PrintData getFooter(PrintLabelDTO printDto, int pageSize) {
        String tel = StringUtils.isEmpty(printDto.getTel()) ? MultiLangUtils.get("undefined") : printDto.getTel();
        return buildLabelPrintContent(pageSize, PrintData.builder(), printDto.getStoreName() + ":" + tel).builder();
//        return PrintData.builder().addBodyLine(PrintData.Builder.getLabelContent(printDto.getStoreName() + ":" + tel, pageSize, 1), ContentTypeEnum.SECTION, 1, 1, false).builder();
    }

    @Override
    public String getPartLine() {
        return "-";
    }

    @Override
    public Class<PrintLabelDTO> getClassOfPrintDTO() {
        return PrintLabelDTO.class;
    }

    @Override
    public String getPrintFailedMessage(PrintLabelDTO printDto) {
        return MultiLangUtils.get("print_failed_msg_for_label");
    }

    private PrintData.Builder buildLabelPrintContent(int pageSize, PrintData.Builder printDataBuilder, String text) {
        String[] args = PrintData.Builder.getLabelContent(text, pageSize, 1).split("\r\n");
        for (String arg : args) {
            printDataBuilder.addBodyLine(arg, ContentTypeEnum.SECTION, 1, 1, false);
        }
        return printDataBuilder;
    }
}
