package com.holder.saas.print;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@EnableAsync
@EnableSwagger2
@EnableFeignClients
@EnableEurekaClient
@SpringBootApplication
public class HolderSaasStorePrintApplication {

    public static void main(String[] args) {
        SpringApplication.run(HolderSaasStorePrintApplication.class, args);
    }
}
