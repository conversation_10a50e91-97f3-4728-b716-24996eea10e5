package com.holder.saas.print.config;

import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import org.springframework.core.MethodParameter;
import org.springframework.lang.NonNull;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.ServletInputStream;
import java.nio.charset.Charset;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandlerPrintArgumentResolver
 * @date 2019/02/14 09:00
 * @description PrintDTO参数反序列化预处理
 * @program holder-saas-store-print
 */
public class HandlerPrintArgumentResolver implements HandlerMethodArgumentResolver {

    @Override
    public boolean supportsParameter(@NonNull MethodParameter methodParameter) {
        return PrintDTO.class.isAssignableFrom(methodParameter.getParameterType());
    }

    @Override
    public Object resolveArgument(@NonNull MethodParameter methodParameter, ModelAndViewContainer modelAndViewContainer,
                                  @NonNull NativeWebRequest nativeWebRequest, WebDataBinderFactory webDataBinderFactory) throws Exception {
        ServletInputStream inputStream = ((ServletWebRequest) nativeWebRequest).getRequest().getInputStream();
        return InvoiceTypeEnum.resolvePrintBy(StreamUtils.copyToString(inputStream, Charset.forName("UTF-8")));
    }
}
