package com.holder.saas.print.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/09/28 15:46
 */
@Configuration
public class MethodArgumentResolverConfig {

    private final RequestMappingHandlerAdapter requestMappingHandlerAdapter;

    @Autowired
    public MethodArgumentResolverConfig(RequestMappingHandlerAdapter requestMappingHandlerAdapter) {
        this.requestMappingHandlerAdapter = requestMappingHandlerAdapter;
    }

    @PostConstruct
    public void init() {
        List<HandlerMethodArgumentResolver> argumentResolvers = new ArrayList<>();
        argumentResolvers.add(new HandlerPrintArgumentResolver());
        argumentResolvers.add(new HandlerFormatArgumentResolver());
        List<HandlerMethodArgumentResolver> originArgumentResolvers = requestMappingHandlerAdapter.getArgumentResolvers();
        if (originArgumentResolvers != null && !originArgumentResolvers.isEmpty()) {
            argumentResolvers.addAll(originArgumentResolvers);
        }
        requestMappingHandlerAdapter.setArgumentResolvers(argumentResolvers);
    }
}
