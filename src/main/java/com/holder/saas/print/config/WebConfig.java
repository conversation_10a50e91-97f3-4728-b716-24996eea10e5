package com.holder.saas.print.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.i18n.LocaleChangeInterceptor;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import java.util.Locale;

/**
 * web app configure
 *
 * <AUTHOR>
 * @date 17-12-5
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    /**
     * 默认解析器 其中locale表示默认语言
     */
//    @Bean
//    public LocaleResolver localeResolver() {
//        SessionLocaleResolver localeResolver = new SessionLocaleResolver();
//        // fixme 巴塞罗那版代码提交后，需要更改默认语言(国家)为中国
//        localeResolver.setDefaultLocale(Locale.CHINA);
////        localeResolver.setDefaultLocale(Locale.US);
//        return localeResolver;
//    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //一体机实现多语言切换
        MyLocaleChangeInterceptor myLocaleChangeInterceptor = new MyLocaleChangeInterceptor();
        registry.addInterceptor(myLocaleChangeInterceptor);
    }
}
