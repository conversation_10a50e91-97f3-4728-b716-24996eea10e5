package com.holder.saas.print.config;


import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.LocaleResolver;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Locale;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class I18nLocalResolver implements LocaleResolver {

    public static final String DEFAULT_PARAM_NAME = "language"; //多语言切换参数

    @Override
    public Locale resolveLocale(HttpServletRequest request) {

        Locale locale = Locale.SIMPLIFIED_CHINESE;
        String lang = request.getHeader(DEFAULT_PARAM_NAME);
        //设置默认国际化语言为简体中文
        if(StringUtils.isEmpty(lang)){
            return locale;
        }
        return Locale.forLanguageTag(lang);
    }

    @Override
    public void setLocale(HttpServletRequest request, HttpServletResponse response, Locale locale) {
        // 设置语言环境的上下文
        LocaleContextHolder.setLocale(locale);
    }
}