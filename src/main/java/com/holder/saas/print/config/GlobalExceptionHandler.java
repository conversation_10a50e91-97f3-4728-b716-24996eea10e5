package com.holder.saas.print.config;

import com.holderzone.feign.spring.boot.exception.ExceptionHandlerAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GlobalExceptionHandler
 * @date 2018/12/19 20:48
 * @description 全局异常处理
 * @program holder-saas-store-print
 */
@Slf4j
public class GlobalExceptionHandler extends ExceptionHandlerAdapter {

}
