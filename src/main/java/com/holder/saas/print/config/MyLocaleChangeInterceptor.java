package com.holder.saas.print.config;


import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Locale;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class MyLocaleChangeInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        I18nLocalResolver i18NLocalResolver = new I18nLocalResolver();
        Locale locale = i18NLocalResolver.resolveLocale(request);
        i18NLocalResolver.setLocale(request,response,locale);
        return true;
    }
}
