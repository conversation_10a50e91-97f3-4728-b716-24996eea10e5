package com.holder.saas.print.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/3/27
 * @description 打印机配置
 */
@Data
@RefreshScope
@Configuration
public class PrinterConfig {

    @Value("${feie.url}")
    private String url;

    @Value("${feie.user}")
    private String user;

    @Value("${feie.uKey}")
    private String uKey;

}
