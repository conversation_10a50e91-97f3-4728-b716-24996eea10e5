package com.holder.saas.print.config;

public class RocketMqConfig {

    public static final String PRINT_MESSAGE_TOPIC = "print-message-topic";

    public static final String PRINT_MESSAGE_TAG = "print-message-tag";

    public static final String PRINT_MESSAGE_GROUP = "print-message-group";

    public static final String PRINT_RECORD_TIMEOUT_TOPIC = "print-record-timeout-topic";

    public static final String PRINT_RECORD_TIMEOUT_TAG = "print-record-timeout-tag";

    public static final String PRINT_RECORD_TIMEOUT_GROUP = "print-record-timeout-group";

    public static final String MESSAGE_CONTEXT = "message-context";

    public static final String MESSAGE_LOCALE = "message-locale";

    public static final String PRINT_RECORD_REMOVE_TOPIC = "print-record-remove-topic";

    public static final String PRINT_RECORD_REMOVE_TAG = "print-record-remove-tag";

    public static final String PRINT_RECORD_REMOVE_GROUP = "print-record-remove-group";
}
