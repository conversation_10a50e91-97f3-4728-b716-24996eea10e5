package com.holder.saas.print.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskDecorator;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
public class ThreadPoolConfig {

    @Bean
    public ExecutorService executorService(@Qualifier("taskDecorator") TaskDecorator taskDecorator) {
        return new ThreadPoolExecutor(5, 50,
                5L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(512),
                new com.holderzone.feign.spring.boot.plugin.async.ThreadFactoryBuilder()
                        .setNameFormat("push-msg-%d")
                        .setTaskDecorator(taskDecorator)
                        .setUncaughtExceptionHandler((t, e) -> log.error("推送线程池执行任务发生异常：", e)).build()
        );
    }

    @Bean(value = "printCloudInvoiceThreadPool")
    public ExecutorService printCloudInvoiceThreadPool() {
        return new ThreadPoolExecutor(5, 10, 5L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("print-cloud-toPrint-pool-%d").build());
    }
}
