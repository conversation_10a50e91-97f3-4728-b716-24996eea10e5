package com.holder.saas.print.template;

import com.holder.saas.print.entity.biz.ItemTableFormatBO;
import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.composite.PrintFormatLayoutContainer;
import com.holderzone.saas.store.dto.print.content.PrintCoTableCbDTO;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.format.CheckoutFormatDTO;
import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.enums.print.TradeModeEnum;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CoTableCbTemplate
 * @date 2018/02/14 09:00
 * @description 并台结帐单模板
 * @program holder-saas-store-print
 */
public class CoTableCbTemplate extends AbsPrintTemplate<PrintCoTableCbDTO, CheckoutFormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintCoTableCbDTO printDTO = getPrintDTO();
        CheckoutFormatDTO formatDTO = getFormatDTO();
        PrintFormatLayoutContainer container = new PrintFormatLayoutContainer();

        // 门店名
        container.addStoreName(printDTO.getStoreName(), formatDTO.getStoreName());

        // 票据类型
        container.addInvoiceType(MultiLangUtils.get("combined_checkout_invoice_header"), formatDTO.getInvoiceName());

        // 桌台订单
        // 商品合计
        BigDecimal itemTotal = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(printDTO.getTableOrderList())) {
            ItemTableFormatBO itemTableFormatBo = ItemTableFormatBO.of(formatDTO);
            itemTableFormatBo.setDuplicatePackage(true);
            for (PrintCoTableCbDTO.PrintTableDTO printTableDTO : printDTO.getTableOrderList()) {

                // 整单备注
                container.addOrderRemark(printTableDTO.getOrderRemark(), formatDTO.getOrderRemark());

                // 牌号
                // 订单号、人数
                container.addMarkOrTableNoAndOrderAndGuest(
                        getPageSize(), TradeModeEnum.DINE.getMode(),
                        printTableDTO.getMarkNo(), formatDTO.getMarkNo(),
                        printTableDTO.getOrderNo(), formatDTO.getOrderNo(),
                        printTableDTO.getPersonNumber(), formatDTO.getPersonNumber());

                // 开台时间、结帐时间
                container.addOpenAndCheckTime(getPageSize(), TradeModeEnum.DINE.getMode(),
                        printTableDTO.getOpenTableTime(), formatDTO.getOpenTableTime(),
                        printTableDTO.getCheckOutTime(), formatDTO.getCheckoutTime());

                // 商品列表排序
                List<PrintItemRecord> sortedPrintItemRecords = sortedItemRecordList(printTableDTO.getPrintItemRecordList());

                // 菜品、商品总额
                // 商品合计
                BigDecimal total = printTableDTO.getTotal();
                itemTotal = itemTotal.add(total);
                container.addTableItem(sortedPrintItemRecords, total, getPageSize(), itemTableFormatBo);
            }
        }

        // 并台合计金额
        container.addCombineTotalMoney(itemTotal);

        // 附加费明细
        // 附加费合计
/*        container.addAdditionalCharge(printDTO.getAdditionalChargeList(),
                formatDTO.getAdditionalCharge(), formatDTO.getAdditionalChargeTotal());*/
        container.addTableAdditionalCharge(printDTO.getAdditionalChargeList(), getPageSize(), ItemTableFormatBO.of(formatDTO));

        // 优惠明细
        // 优惠合计
        // 应付金额
        container.addReduceRecordAndPayable(printDTO.getReduceRecordList(),
                formatDTO.getReduceRecord(), formatDTO.getReduceRecordTotal(),
                printDTO.getPayAble(), formatDTO.getPayableMoney(), null
                , null);

        // 支付方式
        // 实付金额
        container.addPayRecordAndActuallyPay(
                printDTO.getPayRecordList(), formatDTO.getPaidMoneyRecord(),
                printDTO.getChangedPay(), formatDTO.getPaidMoneyChanged(),
                printDTO.getActuallyPay(), formatDTO.getActuallyPayMoney());

        // 添加会员和挂账信息
        addMemberAndDebtInfo(container, printDTO);

        // 结账操作员，结账单打印时间
        container.addOpStaffAndPrintTime(getPageSize(),
                printDTO.getOperatorStaffName(), formatDTO.getOperator(),
                printDTO.getCreateTime(), formatDTO.getPrintTime());

        return container.getPrintRows();
    }

    /**
     * 添加会员和挂账信息
     */
    private void addMemberAndDebtInfo(PrintFormatLayoutContainer container, PrintCoTableCbDTO printDTO) {
        CheckoutFormatDTO formatDTO = getFormatDTO();
        // 会员姓名
        container.addMemberName(printDTO.getMemberName(), formatDTO.getMemberName());
        // 会员电话
        container.addMemberPhone(printDTO.getMemberPhone(), formatDTO.getMemberPhone());
        // 本次消费余额类型 支付卡余额
        container.addMemberCardBalance(formatDTO.getMemberCardBalanceType(), formatDTO.getMemberCardBalance(),
                printDTO.getMultiMemberPayRecords());
        // 挂账还款信息
        container.addDebtInfo(printDTO.getDebtUnitName(), printDTO.getDebtContactName(), printDTO.getDebtContactTel(),
                formatDTO.getDebtUnitName(), formatDTO.getDebtContactName(), formatDTO.getDebtContactTel());
        List<PrintCoTableCbDTO> otherPrintCoTableCbs = printDTO.getOtherPrintCoTableCbs();
        if (!CollectionUtils.isEmpty(otherPrintCoTableCbs)) {
            otherPrintCoTableCbs.forEach(e -> addMemberAndDebtInfo(container, e));
        }
    }


    /**
     * 商品列表排序
     */
    private List<PrintItemRecord> sortedItemRecordList(List<PrintItemRecord> coTableCheckoutitemRecordList) {
        if (CollectionUtils.isEmpty(coTableCheckoutitemRecordList)) {
            return coTableCheckoutitemRecordList;
        }
        CheckoutFormatDTO formatDTO = getFormatDTO();
        FormatMetadata typeTotal = formatDTO.getTypeTotal();
        if (typeTotal.isEnable()) {
            return coTableCheckoutitemRecordList;
        }
        // 排序
        return coTableCheckoutitemRecordList.stream()
                .sorted(Comparator.comparing(PrintItemRecord::getOrderItemGuid))
                .collect(Collectors.toList());
    }

    @Override
    public String getFailedMsg() {
        return "并台结账单 [" + getPrintDTO().getTableOrderList().get(0).getMarkNo() + "]号订单打印失败，请及时处理";
    }
}
