package com.holder.saas.print.template.retail;

import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.template.row.composite.PrintLayoutUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import com.holderzone.saas.store.dto.print.content.retail.PrintRetailHandOverDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandOverTemplate
 * @date 2018/02/14 09:00
 * @description 交接单模板
 * @program holder-saas-store-print
 */
public class RetailHandOverTemplate extends AbsPrintTemplate<PrintRetailHandOverDTO, FormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintRetailHandOverDTO printDTO = getPrintDTO();
        List<PrintRow> printRows = new ArrayList<>();

        PrintRowUtils.add(printRows, new Section()
                .addText(printDTO.getStoreName(), Font.NORMAL_BOLD)
                .setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new Section()
                .addText("**交接单**", Font.NORMAL)
                .setAlign(Text.Align.Center));

        if (80 == getPageSize()) {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString("交接员工：" + printDTO.getStaffName())
                    .setValueString("时长：" + printDTO.getDuration()));
        } else {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKeyString("交接员工：")
                    .setValueString(printDTO.getStaffName()));
            PrintRowUtils.add(printRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKeyString("时长：")
                    .setValueString(printDTO.getDuration()));
        }

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString("开始时间：")
                .setValueString(printDTO.getBeginTime()));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString("结束时间：")
                .setValueString(printDTO.getOverTime()));

        PrintRowUtils.add(printRows, new Separator());

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString("当班营业额：")
                .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getDutyAmount())));

        PrintRowUtils.add(printRows, new Separator());

        if (!CollectionUtils.isEmpty(printDTO.getPayRecordList())) {
            for (PayRecord payRecord : printDTO.getPayRecordList()) {
                PrintRowUtils.add(printRows, new KeyValue()
                        .setKeyString(payRecord.getPayName())
                        .setValueString(BigDecimalUtils.moneyTrimmedString(payRecord.getAmount())));
            }
            PrintRowUtils.add(printRows, new Separator());
        }


        PrintRowUtils.add(printRows, new Section("收支汇总", Font.NORMAL_BOLD));
        PrintLayoutUtils.addStats(printDTO.getInOutRecordList(), getPageSize(), printRows);

        PrintLayoutUtils.addOpStaffAndPrintTime(getPageSize(), printDTO.getOperatorStaffName(), printDTO.getCreateTime(), printRows);

        return printRows;
    }

    @Override
    public String getFailedMsg() {
        return "交接单打印失败，请及时处理";
    }
}
