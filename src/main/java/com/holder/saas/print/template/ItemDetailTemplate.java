package com.holder.saas.print.template;

import com.holder.saas.print.entity.biz.ItemTableFormatBO;
import com.holder.saas.print.template.base.AbsFrontItemTemplate;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.composite.PrintFormatLayoutContainer;
import com.holderzone.saas.store.dto.print.content.PrintItemDetailDTO;
import com.holderzone.saas.store.dto.print.format.ItemDetailFormatDTO;
import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.enums.print.TradeModeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemDetailTemplate
 * @date 2018/02/14 09:00
 * @description 菜品清单模板
 * @program holder-saas-store-print
 */
@Slf4j
public class ItemDetailTemplate extends AbsFrontItemTemplate<PrintItemDetailDTO, ItemDetailFormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintItemDetailDTO printDTO = getPrintDTO();
        ItemDetailFormatDTO formatDTO = getFormatDTO();
        PrintFormatLayoutContainer container = new PrintFormatLayoutContainer();

        // 门店名
        container.addStoreName(printDTO.getStoreName(), formatDTO.getStoreName());

        // 票据类型
        container.addInvoiceType(MultiLangUtils.get("item_list_invoice_header"), formatDTO.getInvoiceName());

        // 整单备注
        container.addOrderRemark(printDTO.getOrderRemark(), formatDTO.getOrderRemark());

        // 会员画像
        container.addMemberPortrayal(printDTO.getMemberPortrayalDTO(), formatDTO.getMemberPortrayal());

        // 牌号
        // 订单号、人数
        container.addMarkOrTableNoAndOrderAndGuest(
                getPageSize(), TradeModeEnum.DINE.getMode(),
                printDTO.getMarkNo(), formatDTO.getMarkNo(),
                printDTO.getOrderNo(), formatDTO.getOrderNo(),
                printDTO.getPersonNumber(), formatDTO.getPersonNumber());

        // 菜品、商品总额
        ItemTableFormatBO itemTableFormatBO = ItemTableFormatBO.of(formatDTO);
        itemTableFormatBO.setDuplicatePackage(true);
        // 去除会员价显示
        if (Objects.nonNull(printDTO.getItemRecordList())) {
            printDTO.getItemRecordList().forEach(itemRecord -> itemRecord.setActualPrice(null));
        }
        container.addTableItem(printDTO.getItemRecordList(), printDTO.getTotal(), getPageSize(), itemTableFormatBO);

        // 附加费明细
        // 附加费合计
        container.addHasTableAdditionalCharge(printDTO.getAdditionalChargeList(), itemTableFormatBO);

        boolean additionalCharge = Optional.ofNullable(itemTableFormatBO.getAdditionalCharge())
                .map(FormatMetadata::isEnable).orElse(false);
        boolean additionalChargeTotal = Optional.ofNullable(itemTableFormatBO.getAdditionalChargeTotal())
                .map(FormatMetadata::isEnable).orElse(false);
        if ((!additionalCharge && !additionalChargeTotal) || CollectionUtils.isEmpty(printDTO.getAdditionalChargeList())) {
            container.addSeparator();
        }

        // 操作员、开台时间
        // 打印时间
        container.addOpStaffAndOpenTableTimeAndPrintTime(
                getPageSize(),
                printDTO.getOperatorStaffName(), formatDTO.getOperator(),
                printDTO.getOpenTableTime(), formatDTO.getOpenTableTime(),
                printDTO.getCreateTime(), formatDTO.getPrintTime(), formatDTO.getYearMonthDay());

        return container.getPrintRows();
    }

    @Override
    public String getFailedMsg() {
        return "菜品清单 [" + getPrintDTO().getMarkNo() + "]号订单打印失败，请及时处理";
    }
}
