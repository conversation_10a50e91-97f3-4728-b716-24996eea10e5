package com.holder.saas.print.template;

import com.holder.saas.print.entity.biz.ItemTableFormatBO;
import com.holder.saas.print.entity.biz.PrintCheckOutContentBO;
import com.holder.saas.print.template.base.AbsFrontItemTemplate;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.composite.PrintFormatLayoutContainer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.print.content.PrintCheckOutDTO;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.format.CheckoutFormatDTO;
import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CheckOutTemplate
 * @date 2018/02/14 09:00
 * @description 结帐单模板
 * @program holder-saas-store-print
 */
@Slf4j
public class CheckOutTemplate extends AbsFrontItemTemplate<PrintCheckOutDTO, CheckoutFormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintCheckOutDTO printDTO = getPrintDTO();
        CheckoutFormatDTO formatDTO = getFormatDTO();
        PrintFormatLayoutContainer container = new PrintFormatLayoutContainer();
        log.info("CheckOutTemplate参数:{}", JacksonUtils.writeValueAsString(printDTO));
        // 门店名
        container.addStoreName(printDTO.getStoreName(), formatDTO.getStoreName());

        // 票据类型
        container.addInvoiceType(MultiLangUtils.get("checkout_invoice_header"), formatDTO.getInvoiceName());

        // 整单备注
        container.addOrderRemark(printDTO.getOrderRemark(), formatDTO.getOrderRemark());

        // 牌号
        // 订单号、人数
        container.addMarkOrTableNoAndOrderAndGuest(
                getPageSize(), printDTO.getTradeMode(),
                printDTO.getMarkNo(), formatDTO.getMarkNo(),
                printDTO.getOrderNo(), formatDTO.getOrderNo(),
                printDTO.getPersonNumber(), formatDTO.getPersonNumber());

        // 商品列表排序
        List<PrintItemRecord> sortedPrintItemRecords = sortedItemRecordList(printDTO.getItemRecordList());

        // 菜品、商品总额
        ItemTableFormatBO itemTableFormatBO = ItemTableFormatBO.of(formatDTO);
        itemTableFormatBO.setDuplicatePackage(true);
        container.addTableItem(sortedPrintItemRecords, printDTO.getTotal(), getPageSize(), itemTableFormatBO);

        // 附加费明细
        // 附加费合计
        container.addTableAdditionalCharge(printDTO.getAdditionalChargeList(), getPageSize(), ItemTableFormatBO.of(formatDTO));

        // 优惠明细
        // 优惠合计
        // 应付金额
        container.addReduceRecordAndPayable(printDTO.getReduceRecordList(),
                formatDTO.getReduceRecord(), formatDTO.getReduceRecordTotal(),
                printDTO.getPayAble(), formatDTO.getPayableMoney(), printDTO.getOriginalPrice()
                , formatDTO.getOriginalPrice());

        // 支付方式
        // 实付金额
        container.addPayRecordAndActuallyPay(
                printDTO.getPayRecordList(), formatDTO.getPaidMoneyRecord(),
                printDTO.getChangedPay(), formatDTO.getPaidMoneyChanged(),
                printDTO.getActuallyPay(), formatDTO.getActuallyPayMoney());

        // 添加会员和挂账信息
        addMemberAndDebtInfo(container, printDTO);

        // 操作员、下单时间
        // 结账时间、打印时间
        container.addOpStaffAndOpenTableTimeAndCheckTimeAndPrintTime(PrintCheckOutContentBO.of(getPageSize(), printDTO, formatDTO));

        //开票二维码
        if (!StringUtils.isEmpty(printDTO.getInvoiceCode())) {
            container.addInvoiceQrCode(
                    printDTO.getInvoiceCode(),
                    printDTO.getInvoiceAmount(),
                    formatDTO.getInvoiceAmount(),
                    formatDTO.getInvoiceQrCode(),
                    formatDTO.getInvoiceCodeName(),
                    formatDTO.getInvoiceMarkedWords());
        }


        log.info("页脚{}", JacksonUtils.writeValueAsString(formatDTO.getFooters()));
        if (CollectionUtils.isEmpty(formatDTO.getFooters())) {
            for (int i = 0; i < formatDTO.getFooters().size(); i++) {
                container.addBlankRow();
                log.info("空白行输出{}", i + 1);
            }
        }
        return container.getPrintRows();
    }


    /**
     * 添加会员和挂账信息
     */
    private void addMemberAndDebtInfo(PrintFormatLayoutContainer container, PrintCheckOutDTO printDTO) {
        CheckoutFormatDTO formatDTO = getFormatDTO();
        // 会员姓名
        container.addMemberName(printDTO.getMemberName(), formatDTO.getMemberName());
        // 会员电话
        container.addMemberPhone(printDTO.getMemberPhone(), formatDTO.getMemberPhone());
        // 本次消费余额类型 支付卡余额
        container.addMemberCardBalance(formatDTO.getMemberCardBalanceType(), formatDTO.getMemberCardBalance(),
                printDTO.getMultiMemberPayRecords());
        // 挂账还款信息
        container.addDebtInfo(printDTO.getDebtUnitName(), printDTO.getDebtContactName(), printDTO.getDebtContactTel(),
                formatDTO.getDebtUnitName(), formatDTO.getDebtContactName(), formatDTO.getDebtContactTel());
        List<PrintCheckOutDTO> otherPrintCheckOuts = printDTO.getOtherPrintCheckOuts();
        if (!CollectionUtils.isEmpty(otherPrintCheckOuts)) {
            otherPrintCheckOuts.forEach(e -> addMemberAndDebtInfo(container, e));
        }
    }

    /**
     * 商品列表排序
     */
    private List<PrintItemRecord> sortedItemRecordList(List<PrintItemRecord> checkoutItemRecordList) {
        if (CollectionUtils.isEmpty(checkoutItemRecordList)) {
            return checkoutItemRecordList;
        }
        CheckoutFormatDTO formatDTO = getFormatDTO();
        FormatMetadata typeTotal = formatDTO.getTypeTotal();
        if (typeTotal.isEnable()) {
            return checkoutItemRecordList;
        }
        // 排序
        return checkoutItemRecordList.stream()
                .sorted(Comparator.comparing(PrintItemRecord::getOrderItemGuid))
                .collect(Collectors.toList());
    }

    @Override
    public String getFailedMsg() {
        return "结账单 [" + getPrintDTO().getMarkNo() + "]号订单打印失败，请及时处理";
    }
}
