package com.holder.saas.print.template;

import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.composite.PrintLayoutUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holder.saas.print.utils.template.row.composite.table.PrintTableUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.print.content.PrintPropStatsDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Table;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.util.EncryptionSymbolUtil;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PropStatsTemplate
 * @date 2018/02/14 09:00
 * @description 属性销售统计模板
 * @program holder-saas-store-print
 */
public class PropStatsTemplate extends AbsPrintTemplate<PrintPropStatsDTO, FormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintPropStatsDTO printDTO = getPrintDTO();
        List<PrintRow> printRows = new ArrayList<>();

        // 门店名
        PrintRowUtils.add(printRows, new Section()
                .addText(printDTO.getStoreName(), Font.NORMAL_BOLD)
                .setAlign(Text.Align.Center));

        // 票据类型
        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get("attribute_sales_statistics_invoice_header"), Font.NORMAL)
                .setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("start_time"))
                .setValueString(DateTimeUtils.mills2String(printDTO.getStartTime(), "yyyy-MM-dd HH:mm:ss")));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("end_time"))
                .setValueString(DateTimeUtils.mills2String(printDTO.getEndTime(), "yyyy-MM-dd HH:mm:ss")));

        buildPropGroupList(printRows);

        PrintLayoutUtils.addOpStaffAndPrintTime(getPageSize(), printDTO.getOperatorStaffName(), printDTO.getCreateTime(), printRows);

        return printRows;
    }

    /**
     * 构建属性列表
     */
    private void buildPropGroupList(List<PrintRow> printRows) {
        PrintPropStatsDTO printDTO = getPrintDTO();
        if (CollectionUtils.isEmpty(printDTO.getPropGroupList())) {
            return;
        }
        // 表头
        Table table = new Table(
                Arrays.asList(new Text(MultiLangUtils.get("attribute_name")), new Text(MultiLangUtils.get("quantity")), new Text(MultiLangUtils.get("amount"))),
                PrintTableUtils.getStatsItemColWidthList(getPageSize()),
                Arrays.asList(false, false, true),
                false
        );
        PrintPropStatsDTO.PropGroup firstPropGroup = printDTO.getPropGroupList().get(0);
        boolean isQuantityEncryption = EncryptionSymbolUtil.isEncryption(firstPropGroup.getPropList().get(0).getQuantityStr());
        boolean isMoneyEncryption = EncryptionSymbolUtil.isEncryption(firstPropGroup.getPropList().get(0).getMoneyStr());

        for (PrintPropStatsDTO.PropGroup propGroup : printDTO.getPropGroupList()) {
            long groupQuantity = propGroup.getPropList().stream()
                    .mapToLong(PrintPropStatsDTO.PropItem::getQuantity)
                    .sum();
            BigDecimal groupMoney = propGroup.getPropList().stream()
                    .map(PrintPropStatsDTO.PropItem::getMoney)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            table.addRow(Arrays.asList(
                    new Text(MultiLangUtils.get("attribute_group") + propGroup.getName()),
                    new Text(isQuantityEncryption ? Constant.ENCRYPTION_SYMBOL : String.valueOf(groupQuantity)),
                    new Text(isMoneyEncryption ? Constant.ENCRYPTION_SYMBOL : BigDecimalUtils.moneyTrimmedString(groupMoney))
            ));
            table.addRow(Collections.singletonList(new Text("-")));
            // 添加 属性列表
            appendTablePropItem(table, propGroup);
            table.addRow(Collections.singletonList(new Text("-")));
        }
        long totalQuantity = printDTO.getPropGroupList().stream()
                .mapToLong(propGroup -> propGroup.getPropList().stream()
                        .mapToLong(PrintPropStatsDTO.PropItem::getQuantity)
                        .sum())
                .sum();
        BigDecimal totalMoney = printDTO.getPropGroupList().stream()
                .map(propGroup -> propGroup.getPropList().stream()
                        .map(PrintPropStatsDTO.PropItem::getMoney)
                        .reduce(BigDecimal.ZERO, BigDecimal::add))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        table.addRow(Arrays.asList(
                new Text(MultiLangUtils.get("price_total")),
                new Text(isQuantityEncryption ? Constant.ENCRYPTION_SYMBOL : String.valueOf(totalQuantity)),
                new Text(isMoneyEncryption ? Constant.ENCRYPTION_SYMBOL : BigDecimalUtils.moneyTrimmedString(totalMoney))
        ));
        PrintRowUtils.add(printRows, table);
    }

    /**
     * 添加 属性列表
     */
    private void appendTablePropItem(Table table, PrintPropStatsDTO.PropGroup propGroup) {
        if (CollectionUtils.isEmpty(propGroup.getPropList())) {
            return;
        }
        for (PrintPropStatsDTO.PropItem propItem : propGroup.getPropList()) {
            boolean isQuantityEncryption = EncryptionSymbolUtil.isEncryption(propItem.getQuantityStr());
            boolean isMoneyEncryption = EncryptionSymbolUtil.isEncryption(propItem.getMoneyStr());
            table.addRow(Arrays.asList(
                    new Text(propItem.getName()),
                    new Text(isQuantityEncryption ? Constant.ENCRYPTION_SYMBOL : String.valueOf(propItem.getQuantity())),
                    new Text(isMoneyEncryption ? propItem.getMoneyStr() : BigDecimalUtils.moneyTrimmedString(propItem.getMoney()))
            ));
        }
    }

    @Override
    public String getFailedMsg() {
        return "属性销售统计单打印失败，请及时处理";
    }
}
