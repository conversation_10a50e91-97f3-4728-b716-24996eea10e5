package com.holder.saas.print.template.cloud;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.entity.domain.PrintRecordDO;
import com.holder.saas.print.entity.enums.ItemStateEnum;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.SensitiveUtils;
import com.holder.saas.print.utils.template.cloud.CloudPrinterUtils;
import com.holder.saas.print.utils.template.cloud.FeiePrinterUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.print.content.PrintOrderItemDTO;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2024/4/8
 * @description 飞蛾打印机点菜单模板
 */
@Slf4j
public class CloudOrderItemTemplate {

    private CloudOrderItemTemplate() {
    }

    public static String getContent(PrintRecordDO printRecordDO) {
        String orderItemContent = printRecordDO.getPrintContent();
        PrintOrderItemDTO orderItemDTO = JacksonUtils.toObject(PrintOrderItemDTO.class, orderItemContent);
        StringBuilder orderItemContentBuilder = new StringBuilder();
        // 标题
        orderItemContentBuilder.append(getTitle());

        // 整单备注
        orderItemContentBuilder.append(getRemark(orderItemDTO.getRemark()));

        // 牌号
        orderItemContentBuilder.append(getTrademark(orderItemDTO.getMarkNo()));

        // 订单号
        orderItemContentBuilder.append(getOrderNo(orderItemDTO.getOrderNo()));

        // 人数
        orderItemContentBuilder.append(getPersonNumber(orderItemDTO.getPersonNumber()));

        // 分割
        orderItemContentBuilder.append(FeiePrinterUtils.addDividingLine());

        // 商品、数量
        orderItemContentBuilder.append(getItemContent(orderItemDTO.getItemRecordList()));

        // 分割
        orderItemContentBuilder.append(FeiePrinterUtils.addDividingLine());

        // 操作员
        orderItemContentBuilder.append(getOperatorStaffName(orderItemDTO.getOperatorStaffName()));

        // 下单时间
        orderItemContentBuilder.append(getOrderTime(orderItemDTO.getOrderTime()));

        // 打印时间
        orderItemContentBuilder.append(getCreateTime(orderItemDTO.getCreateTime()));

        // 小票提示
        orderItemContentBuilder.append(getReceiptNote());

        // 分割
        orderItemContentBuilder.append(FeiePrinterUtils.addDividingLine());
        return orderItemContentBuilder.toString();
    }

    /**
     * 商品、数量
     */
    private static String getItemContent(List<PrintItemRecord> itemRecordList) {
        StringBuilder itemContent = new StringBuilder();
        String itemTitle = getItemTitle();
        itemContent.append(itemTitle);
        if (!CollectionUtils.isEmpty(itemRecordList)) {
            for (int i = 0; i < itemRecordList.size(); i++) {
                PrintItemRecord itemRecord = itemRecordList.get(i);
                BigDecimal number = itemRecord.getNumber();
                String unit = itemRecord.getUnit();
                String numberStr = number.stripTrailingZeros().toPlainString() + unit;
                String itemStr = getItemNameStr(itemRecord, i);
                itemStr = CloudPrinterUtils.handleInterval(itemStr, numberStr, CloudPrinterUtils.LINE_SIZE);
                itemStr = handleItem(itemRecord, itemStr);
                itemStr = FeiePrinterUtils.addMagnifyOnce(itemStr);
                itemContent.append(itemStr);
            }
        }
        return itemContent.toString();
    }

    @NotNull
    private static String handleItem(PrintItemRecord itemRecord, String itemStr) {
        // 属性
        String itemProperty = getItemProperty(itemRecord.getProperty());
        itemStr += itemProperty;
        // 商品备注
        String itemRemark = getItemRemark(itemRecord.getRemark());
        itemStr += itemRemark;
        // 套餐子菜
        if (!CollectionUtils.isEmpty(itemRecord.getSubItemRecords())) {
            StringBuilder itemStrBuilder = new StringBuilder(itemStr);
            for (PrintItemRecord subPrintItemRecord : itemRecord.getSubItemRecords()) {
                String subNumberStr = BigDecimalUtils.quantityTrimmedString(subPrintItemRecord.getNumber());
                String subItemNumberUnit = subNumberStr + (StringUtils.isEmpty(subPrintItemRecord.getUnit())
                        ? "" : subPrintItemRecord.getUnit());
                String subItemName = " -|" + subPrintItemRecord.getItemName();
                itemStrBuilder.append(CloudPrinterUtils.handleInterval(subItemName, subItemNumberUnit, CloudPrinterUtils.LINE_SIZE));
                itemStrBuilder.append(getSubItemProperty(subPrintItemRecord.getProperty()));
            }
            itemStr = itemStrBuilder.toString();
        }
        return itemStr;
    }

    /**
     * 商品属性
     */
    private static String getItemProperty(String property) {
        if (StringUtils.isEmpty(property)) {
            return "";
        }
        String itemProperty = " [" + MultiLangUtils.get(Constant.ATTR) + property + "]";
        return FeiePrinterUtils.addLineBreak(itemProperty);
    }

    /**
     * 商品备注
     */
    private static String getItemRemark(String remark) {
        if (StringUtils.isEmpty(remark)) {
            return "";
        }
        String itemRemark = " [" + MultiLangUtils.get(Constant.REMARK) + remark + "]";
        return FeiePrinterUtils.addLineBreak(itemRemark);
    }

    /**
     * 子商品属性
     */
    private static String getSubItemProperty(String property) {
        if (StringUtils.isEmpty(property)) {
            return "";
        }
        String itemProperty = "   [" + MultiLangUtils.get(Constant.ATTR) + property + "]";
        return FeiePrinterUtils.addLineBreak(itemProperty);
    }

    /**
     * 商品名称
     */
    private static String getItemNameStr(PrintItemRecord itemRecord, int index) {
        // 商品加标签
        String hangUp = "";
        if (Objects.nonNull(itemRecord.getItemState())) {
            hangUp = itemRecord.getItemState().equals(ItemStateEnum.HANG_UP.getCode()) ? MultiLangUtils.get(Constant.HANG) : "";
        }
        String ifPackage = "";
        if (Boolean.TRUE.equals(itemRecord.getAsPackage())) {
            ifPackage = MultiLangUtils.get(Constant.SET);
        }
        String ifWeight = "";
        if (Boolean.TRUE.equals(itemRecord.getAsWeight())) {
            ifWeight = MultiLangUtils.get(Constant.WEIGHT);
        }
        String giftName = "";
        if (Boolean.TRUE.equals(itemRecord.getAsGift())) {
            giftName = MultiLangUtils.get(Constant.COMPL);
        }
        String itemName = itemRecord.getItemName().replace("\n", " ");
        itemName = itemName.replace("\\n", " ");
        itemName = hangUp + ifPackage + ifWeight + itemName + giftName;
        // 商品加序号
        return index + 1 + "." + itemName;
    }

    private static String getItemTitle() {
        String quantity = MultiLangUtils.get(Constant.QUANTITY);
        int quantityLength = CloudPrinterUtils.getContentLength(quantity);
        String item = CloudPrinterUtils.addSpaceAfter(MultiLangUtils.get(Constant.ITEM), CloudPrinterUtils.LINE_SIZE - quantityLength);
        String itemTitle = FeiePrinterUtils.addMagnifyOnce(item);
        itemTitle += FeiePrinterUtils.addMagnifyOnce(quantity);
        itemTitle = FeiePrinterUtils.addLineBreak(itemTitle);
        return itemTitle;
    }

    /**
     * 打印时间
     */
    private static String getCreateTime(Long createTimeNum) {
        String createTimeStr = DateTimeUtils.mills2String(createTimeNum, Constant.MM_DD_HH_MM);
        String createTime = MultiLangUtils.get(Constant.PRT_TIME) + createTimeStr;
        return FeiePrinterUtils.addLineBreak(createTime);
    }

    /**
     * 下单时间
     */
    private static String getOrderTime(Long orderTimeNum) {
        String openTableTimeStr = DateTimeUtils.mills2String(orderTimeNum, Constant.MM_DD_HH_MM);
        String orderTime = MultiLangUtils.get(Constant.ORDER_TIME) + openTableTimeStr;
        return FeiePrinterUtils.addLineBreak(orderTime);
    }

    /**
     * 操作员
     */
    private static String getOperatorStaffName(String operator) {
        String operatorStaffName = MultiLangUtils.get(Constant.OPERATOR) + operator;
        return FeiePrinterUtils.addLineBreak(operatorStaffName);
    }

    /**
     * 小票提示
     */
    private static String getReceiptNote() {
        String receiptNote = MultiLangUtils.get(Constant.RECEIPT_NOTE);
        receiptNote = FeiePrinterUtils.addLineBreak(receiptNote);
        return receiptNote;
    }

    /**
     * 整单备注
     */
    private static String getRemark(String remark) {
        String remarkMessage = "";
        if (StringUtils.hasText(remark)) {
            try {
                // 整单备注，敏感词过滤替换
                remarkMessage = SensitiveUtils.replaceWord(remark);
            } catch (Exception e) {
                log.error("[整单备注]敏感词过滤替换报错：" + e.getMessage());
            }
            remarkMessage = MultiLangUtils.get(Constant.ORDER_REMARK) + remarkMessage;
            remarkMessage = FeiePrinterUtils.addLineBreak(remarkMessage);
            remarkMessage += FeiePrinterUtils.addDividingLine();
        }
        return remarkMessage;
    }

    /**
     * 人数
     */
    private static String getPersonNumber(Integer personNumber) {
        String format = MultiLangUtils.get(Constant.CUSTOMER_NUMBER) + personNumber;
        return FeiePrinterUtils.addLineBreak(format);
    }

    /**
     * 订单号
     */
    private static String getOrderNo(String orderNo) {
        String orderNumber = MultiLangUtils.get(Constant.ORDER_NUMBER) + orderNo;
        return FeiePrinterUtils.addLineBreak(orderNumber);
    }

    /**
     * 牌号
     */
    private static String getTrademark(String markNo) {
        String trademark = MultiLangUtils.get(Constant.MARK_NAME) + markNo;
        trademark = FeiePrinterUtils.addMagnifyOnce(trademark);
        return FeiePrinterUtils.addLineBreak(trademark);
    }

    /**
     * 标题
     */
    private static String getTitle() {
        String title = MultiLangUtils.get(Constant.ORDER_ITEM_INVOICE_HEADER);
        title = FeiePrinterUtils.addCenterMagnify(title);
        title = FeiePrinterUtils.addLineBreak(title);
        return title;
    }

}
