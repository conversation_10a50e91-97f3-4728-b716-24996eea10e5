package com.holder.saas.print.template;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.entity.biz.ItemTableFormatBO;
import com.holder.saas.print.template.base.AbsFrontItemTemplate;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holder.saas.print.utils.template.row.composite.PrintFormatLayoutContainer;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.print.content.PrintRefundDTO;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.format.RefundFormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import com.holderzone.saas.store.enums.print.RefundPrintTypeEnum;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 餐饮退款单模板
 *
 * <AUTHOR>
 * @date 2025/07/01
 */
@Slf4j
public class RefundTemplate extends AbsFrontItemTemplate<PrintRefundDTO, RefundFormatDTO> {

    private static final String YMD_PATTERN = "yyyy-MM-dd HH:mm";

    @Override
    public List<PrintRow> getContent() {
        PrintRefundDTO printDTO = getPrintDTO();
        RefundFormatDTO formatDTO = getFormatDTO();
        PrintFormatLayoutContainer container = new PrintFormatLayoutContainer();

        // 门店名
        container.addStoreName(printDTO.getStoreName(), formatDTO.getStoreName());

        // 票据类型 - **退款单**
        container.addInvoiceType(MultiLangUtils.get("refund_invoice_header"), formatDTO.getInvoiceName());

        // 牌号/桌号
        container.addMarkOrTableNoNew(printDTO.getMarkNo(), formatDTO.getMarkNo(), printDTO.getTradeMode());

        // 退款时间(左对齐)
        addRefundTime(container, printDTO, formatDTO);

        // 订单编号（左对齐）
        PrintRowUtils.add(container.getPrintRows(), new Section(MultiLangUtils.get(Constant.ORDER_NUMBER) +
                printDTO.getOrderNo(), formatDTO.getOrderNo().toFont()).setAlign(Text.Align.Left));

        // 封装对象
        ItemTableFormatBO itemTableFormatBO = ItemTableFormatBO.of(formatDTO);

        // 退款商品列表
        addRefundItemList(container, printDTO, itemTableFormatBO);

        // 附加费合计
        boolean charge = container.addHasTableAdditionalCharge(printDTO.getAdditionalChargeList(), itemTableFormatBO);

        // 如果没有添加附加费，新增分割符
        if (!charge) {
            PrintRowUtils.add(container.getPrintRows(), new Separator());
        }

        // 退款金额
        addRefundAmount(container, printDTO, formatDTO);

        // 退款方式
        addRefundMethod(container, printDTO, formatDTO);

        // 退款原因
        addRefundReason(container, printDTO, formatDTO);

        // 操作员、操作时间、打印时间
        addOperatorAndTime(container, printDTO, formatDTO);

        if (!CollectionUtils.isEmpty(formatDTO.getFooters())) {
            for (int i = 0; i < formatDTO.getFooters().size(); i++) {
                container.addBlankRow();
                log.info("退款单 - 空白行输出{}", i + 1);
            }
        }
        return container.getPrintRows();
    }

    /**
     * 添加退款时间
     */
    private void addRefundTime(PrintFormatLayoutContainer container, PrintRefundDTO printDTO, RefundFormatDTO formatDTO) {
        if (formatDTO.getRefundTime().isEnable()) {
            PrintRowUtils.addLeft(container.getPrintRows(), Constant.REFUND_TIME, getTimeStr(printDTO.getRefundTime()),
                    formatDTO.getRefundTime());
        }
    }

    /**
     * 添加退款商品列表
     *
     * @param container         容器信息
     * @param printDTO          打印对象
     * @param itemTableFormatBO 商品格式化对象
     */
    private void addRefundItemList(PrintFormatLayoutContainer container, PrintRefundDTO printDTO,
                                   ItemTableFormatBO itemTableFormatBO) {
        if (CollectionUtils.isEmpty(printDTO.getItemRecordList())) {
            return;
        }
        // 去除会员价和单价
        for (PrintItemRecord record : printDTO.getItemRecordList()) {
            // 去除会员价
            record.setActualPrice(null);
        }
        printDTO.getItemRecordList().forEach(e -> e.setActualPrice(null));
        // 是否退款操作进入
        if (Objects.equals(printDTO.getOperationType(), RefundPrintTypeEnum.REFUND)) {
            itemTableFormatBO.setRefundRefund(true);
        }
        // 添加商品列表
        container.addTableItem(printDTO.getItemRecordList(), null, getPageSize(), itemTableFormatBO);
    }

    /**
     * 添加退款金额
     */
    private void addRefundAmount(PrintFormatLayoutContainer container, PrintRefundDTO printDTO, RefundFormatDTO formatDTO) {
        if (!formatDTO.getRefundAmount().isEnable()) {
            return;
        }
        Font font = formatDTO.getRefundAmount().toFont();
        PrintRowUtils.add(container.getPrintRows(), new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.REFUND_ONLY_AMOUNT), font)
                .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getRefundAmount()), font));
    }

    /**
     * 添加退款方式
     */
    private void addRefundMethod(PrintFormatLayoutContainer container, PrintRefundDTO printDTO, RefundFormatDTO formatDTO) {
        if (!formatDTO.getRefundMethod().isEnable()) {
            return;
        }
        Font font = formatDTO.getRefundMethod().toFont();
        // 所有字符
        String allValue = MultiLangUtils.get(Constant.REFUND_WAY);
        if (StringUtils.hasText(printDTO.getRefundMethodType())) {
            allValue += String.format("(%s)", printDTO.getRefundMethodType());
        }
        // 获取限制字数
        Pair<Integer, Integer> pair = getLimit();
        // 换行展示数据信息
        List<String> leftStrList = getRefundMethodHandle(allValue, pair.getKey());
        List<String> rightStrList = getRefundMethodHandle(printDTO.getRefundMethod(), pair.getValue());
        // 获取最大长度
        int max = Math.max(leftStrList.size(), rightStrList.size());

        for (int i = 0; i < max; i++) {
            // 第一行，默认左右
            if (i == 0) {
                PrintRowUtils.add(container.getPrintRows(), new KeyValue()
                        .setKeyString(leftStrList.get(i), font)
                        .setValueString(rightStrList.get(i), font));
                continue;
            }
            // 有左侧字
            if (leftStrList.size() > i) {
                // 如果有右侧字，左右显示
                if (rightStrList.size() > i) {
                    PrintRowUtils.add(container.getPrintRows(), new KeyValue()
                            .setKeyString(leftStrList.get(i), font)
                            .setValueString(rightStrList.get(i), font));
                } else {
                    // 其他情况，只显示左侧
                    PrintRowUtils.add(container.getPrintRows(), new Section()
                            .addText(leftStrList.get(i), font)
                            .setAlign(Text.Align.Left));
                }

            } else if (rightStrList.size() > i) {
                // 只显示右侧
                PrintRowUtils.add(container.getPrintRows(), new Section()
                        .addText(rightStrList.get(i), font)
                        .setAlign(Text.Align.Right));
            }
        }
    }

    /**
     * 计算退款方式限制字数
     *
     * @return 查询结果
     */
    private Pair<Integer, Integer> getLimit() {
        if (80 == getPageSize()) {
            return new Pair<>(7, 5);
        }
        return new Pair<>(4, 3);
    }

    /**
     * 退款方式换行处理
     *
     * @param allValue 所有字符
     * @param limit    限制字数
     * @return 换行数据
     */
    private List<String> getRefundMethodHandle(String allValue, int limit) {
        if (allValue.length() <= limit) {
            return Collections.singletonList(allValue);
        }
        List<String> result = new ArrayList<>();
        int length = allValue.length();
        int start = 0;
        int end = limit;
        while (start < length) {
            if (end > length) {
                end = length;
            }
            result.add(allValue.substring(start, end));
            start = end;
            end += limit;
        }
        return result;
    }

    /**
     * 添加退款原因
     */
    private void addRefundReason(PrintFormatLayoutContainer container, PrintRefundDTO printDTO, RefundFormatDTO formatDTO) {
        if (StringUtils.isEmpty(printDTO.getRefundReason()) || !formatDTO.getRefundReason().isEnable()) {
            return;
        }
        // 添加分隔线
        PrintRowUtils.add(container.getPrintRows(), new Separator());

        Font font = formatDTO.getRefundReason().toFont();
        PrintRowUtils.add(container.getPrintRows(), new Section()
                .addText(MultiLangUtils.get(Constant.REFUND_REASON) + printDTO.getRefundReason(), font)
                .setAlign(com.holderzone.saas.store.dto.print.template.convertable.Text.Align.Left));
    }

    /**
     * 添加操作员和时间
     *
     * @param container 数据信息
     * @param printDTO  打印信息
     * @param formatDTO 格式信息
     */
    private void addOperatorAndTime(PrintFormatLayoutContainer container, PrintRefundDTO printDTO, RefundFormatDTO formatDTO) {
        if (!formatDTO.getOperator().isEnable() && !formatDTO.getOperationTime().isEnable()
                && !formatDTO.getPrintTime().isEnable()) {
            return;
        }

        PrintRowUtils.add(container.getPrintRows(), new Separator());

        if (80 == getPageSize()) {
            addTimeInfoFor80PageSize(printDTO, formatDTO, container);
        } else {
            // 操作员，靠左
            if (formatDTO.getOperator().isEnable()) {
                container.addOperatorOnly(printDTO.getOperatorStaffName(), formatDTO.getOperator());
            }

            // 操作时间，靠左
            if (formatDTO.getOperationTime().isEnable()) {
                PrintRowUtils.addLeft(container.getPrintRows(), Constant.OPERATION_TIME, getTimeStr(printDTO.getOperationTime()),
                        formatDTO.getOperationTime());
            }

            // 打印时间，靠左
            if (formatDTO.getPrintTime().isEnable()) {
                PrintRowUtils.addLeft(container.getPrintRows(), Constant.PRT_TIME, getTimeStr(System.currentTimeMillis()),
                        formatDTO.getPrintTime());
            }
        }
    }

    /**
     * 80mm打印机制
     *
     * @param printDTO  打印参数
     * @param formatDTO 模版参数
     * @param container 容器
     */
    private void addTimeInfoFor80PageSize(PrintRefundDTO printDTO, RefundFormatDTO formatDTO,
                                          PrintFormatLayoutContainer container) {
        // 有操作员
        if (formatDTO.getOperator().isEnable()) {
            // 有操作时间
            if (formatDTO.getOperationTime().isEnable()) {
                // 操作员在左，操作时间在右
                PrintRowUtils.add(container.getPrintRows(), new KeyValue()
                        .setKeyString(MultiLangUtils.get(Constant.OPERATOR) + printDTO.getOperatorStaffName(),
                                formatDTO.getOperator().toFont())
                        .setValueString(MultiLangUtils.get(Constant.OPERATION_TIME) + getTimeStr(printDTO.getOperationTime()),
                                formatDTO.getOperationTime().toFont()));
            } else {
                // 没有操作时间，单独一行
                container.addOperatorOnly(printDTO.getOperatorStaffName(), formatDTO.getOperator());
            }
        } else {
            // 操作时间，靠右
            if (formatDTO.getOperationTime().isEnable()) {
                PrintRowUtils.addRight(container.getPrintRows(), Constant.OPERATION_TIME, getTimeStr(printDTO.getOperationTime()),
                        formatDTO.getOperationTime());
            }

        }
        // 打印时间，靠右
        if (formatDTO.getPrintTime().isEnable()) {
            PrintRowUtils.addRight(container.getPrintRows(), Constant.PRT_TIME, getTimeStr(System.currentTimeMillis()),
                    formatDTO.getPrintTime());
        }
    }

    /**
     * 获取时间格式
     *
     * @param time 时间戳
     * @return 时间字符串
     */
    private String getTimeStr(Long time) {
        return DateTimeUtils.mills2String(time, YMD_PATTERN);
    }

    @Override
    public String getFailedMsg() {
        return "退款单 [" + getPrintDTO().getOrderNo() + "] 打印失败，请及时处理";
    }
}
