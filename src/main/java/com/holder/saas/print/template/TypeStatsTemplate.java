package com.holder.saas.print.template;

import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.composite.PrintLayoutUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holder.saas.print.utils.template.row.composite.table.PrintTableUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.print.content.PrintTypeStatsDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Table;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.util.EncryptionSymbolUtil;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TypeStatsTemplate
 * @date 2018/02/14 09:00
 * @description 分类销售统计模板
 * @program holder-saas-store-print
 */
public class TypeStatsTemplate extends AbsPrintTemplate<PrintTypeStatsDTO, FormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintTypeStatsDTO printDTO = getPrintDTO();
        List<PrintRow> printRows = new ArrayList<>();

        // 门店名
        PrintRowUtils.add(printRows, new Section()
                .addText(printDTO.getStoreName(), Font.NORMAL_BOLD)
                .setAlign(Text.Align.Center));

        // 票据类型
        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get("category_sales_statistics_invoice_header"), Font.NORMAL)
                .setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("start_time"))
                .setValueString(DateTimeUtils.mills2String(printDTO.getStartTime(), "yyyy-MM-dd HH:mm:ss")));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("end_time"))
                .setValueString(DateTimeUtils.mills2String(printDTO.getEndTime(), "yyyy-MM-dd HH:mm:ss")));

        // 构建商品分类列表
        buildItemTypeList(printRows);

        PrintLayoutUtils.addOpStaffAndPrintTime(getPageSize(), printDTO.getOperatorStaffName(), printDTO.getCreateTime(), printRows);
        return printRows;
    }


    /**
     * 构建商品分类列表
     */
    private void buildItemTypeList(List<PrintRow> printRows) {
        PrintTypeStatsDTO printDTO = getPrintDTO();
        if (CollectionUtils.isEmpty(printDTO.getItemTypeList())) {
            return;
        }
        Table table = new Table(
                Arrays.asList(new Text(MultiLangUtils.get("product_categories")),
                        new Text(MultiLangUtils.get("sale_quantity")),
                        new Text(MultiLangUtils.get("sale_amount")),
                        new Text(MultiLangUtils.get("paid"))),
                PrintTableUtils.getStatsPaymentColWidthList(getPageSize()),
                Arrays.asList(false, false, false, true),
                false
        );
        PrintTypeStatsDTO.ItemType firstItemType = printDTO.getItemTypeList().get(0);
        boolean isQuantityEncryption = EncryptionSymbolUtil.isEncryption(firstItemType.getQuantityStr());
        boolean isMoneyEncryption = EncryptionSymbolUtil.isEncryption(firstItemType.getMoneyStr());
        boolean isPayMoneyEncryption = EncryptionSymbolUtil.isEncryption(firstItemType.getPayMoneyStr());
        for (PrintTypeStatsDTO.ItemType itemType : printDTO.getItemTypeList()) {
            table.addRow(Arrays.asList(
                    new Text(itemType.getName()),
                    new Text(isQuantityEncryption ? Constant.ENCRYPTION_SYMBOL : BigDecimalUtils.quantityTrimmedString(itemType.getQuantity())),
                    new Text(isMoneyEncryption ? Constant.ENCRYPTION_SYMBOL : BigDecimalUtils.moneyTrimmedString(itemType.getMoney())),
                    new Text(isPayMoneyEncryption ? Constant.ENCRYPTION_SYMBOL : BigDecimalUtils.moneyTrimmedString(itemType.getPayMoney()))
            ));
        }
        BigDecimal totalQuantity = printDTO.getItemTypeList().stream()
                .map(PrintTypeStatsDTO.ItemType::getQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalMoney = printDTO.getItemTypeList().stream()
                .map(PrintTypeStatsDTO.ItemType::getMoney)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalPayMoney = printDTO.getItemTypeList().stream()
                .map(PrintTypeStatsDTO.ItemType::getPayMoney)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        table.addRow(Collections.singletonList(new Text("-")));
        table.addRow(Arrays.asList(
                new Text(MultiLangUtils.get("price_total")),
                new Text(isQuantityEncryption ? Constant.ENCRYPTION_SYMBOL : BigDecimalUtils.quantityTrimmedString(totalQuantity)),
                new Text(isMoneyEncryption ? Constant.ENCRYPTION_SYMBOL : BigDecimalUtils.moneyTrimmedString(totalMoney)),
                new Text(isPayMoneyEncryption ? Constant.ENCRYPTION_SYMBOL : BigDecimalUtils.moneyTrimmedString(totalPayMoney))
        ));
        PrintRowUtils.add(printRows, table);
    }

    @Override
    public String getFailedMsg() {
        return "分类销售统计单打印失败，请及时处理";
    }
}
