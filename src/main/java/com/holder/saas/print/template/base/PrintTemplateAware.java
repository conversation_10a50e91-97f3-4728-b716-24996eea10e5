package com.holder.saas.print.template.base;

import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;

import java.util.List;

public interface PrintTemplateAware<T extends PrintDTO, F extends FormatDTO> extends PrintTemplate<T, F> {

    int getPageSize();

    T getPrintDTO();

    F getFormatDTO();
}
