package com.holder.saas.print.template;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.print.content.PrintReservePayDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.BlankRow;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 预约支付打印模板类。
 * 用于构建预约支付单据的打印内容。
 *
 * <AUTHOR>
 * @since 2025/6/9
 */
public class ReservePayTemplate extends AbsPrintTemplate<PrintReservePayDTO, FormatDTO> {

    private static final String YMD_PATTERN = "yyyy-MM-dd HH:mm:ss";

    private static final Font AMOUNT_FONT = Font.SMALL_BOLD;

    @Override
    public List<PrintRow> getContent() {
        PrintReservePayDTO printDTO = getPrintDTO();
        List<PrintRow> printRows = new ArrayList<>();

        // 门店名称（居中加粗）
        addCenteredText(printRows, printDTO.getStoreName(), Font.NORMAL_BOLD);

        // 标题（居中普通）
        addCenteredText(printRows, MultiLangUtils.get(Constant.RESERVATION_PAY_INVOICE_HEADER), Font.NORMAL);

        // 空行
        PrintRowUtils.add(printRows, new BlankRow());

        // 预订时间
        addKeyStringValue(printRows, Constant.RESERVE_START_TIME, printDTO.getConfirmTime());

        // 收银员
        addOptionalKeyValue(printRows, Constant.CHECKOUT_STAFFS, printDTO.getCheckoutStaffs());

        // 分隔线
        PrintRowUtils.add(printRows, new Separator());

        // 就餐时间
        addOptionalKeyValue(printRows, Constant.ARRIVE_TIME, printDTO.getReserveStartTime());

        // 就餐人数
        addOptionalKeyNumber(printRows, Constant.RESERVE_NUMBER, printDTO.getNumber());

        // 预订桌台
        addOptionalKeyValue(printRows, Constant.RESERVE_TABLE_NAME, printDTO.getTableName());

        // 预订人
        if (StringUtils.isNotEmpty(printDTO.getName())) {
            String genderStr = formatGender(printDTO.getGender());
            String nameWithGender = printDTO.getName() + genderStr;
            addKeyStringValue(printRows, Constant.RESERVE_NAME, nameWithGender);
        }

        // 预留手机号
        addOptionalKeyValue(printRows, Constant.RESERVE_PHONE, printDTO.getPhone());

        // 预付金额（特殊字体）
        addAmountRow(printRows, Constant.RESERVATION_PAY_AMOUNT, printDTO.getReserveAmount());

        // 备注
        addOptionalKeyValue(printRows, Constant.REMARK, printDTO.getRemark());

        // 支付方式
        addOptionalKeyValue(printRows, Constant.PAYMENT_TYPE, printDTO.getPaymentTypeName());

        // 分隔线
        PrintRowUtils.add(printRows, new Separator());

        // 支付时间
        addOptionalKeyValue(printRows, Constant.PAYMENT_TIME, printDTO.getPaymentTime());

        // 打印时间
        addKeyStringValue(printRows, Constant.PRT_TIME, formatTime(printDTO.getCreateTime()));

        return printRows;
    }

    /**
     * 添加居中文本段落
     */
    private void addCenteredText(List<PrintRow> printRows, String text, Font font) {
        PrintRowUtils.add(printRows, new Section()
                .addText(text, font)
                .setAlign(Text.Align.Center));
    }

    /**
     * 添加固定键值对（非空也添加）
     */
    private void addKeyStringValue(List<PrintRow> printRows, String keyConstant, String value) {
        String key = MultiLangUtils.get(keyConstant);
        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(key)
                .setValueString(value));
    }

    /**
     * 添加可选键值对（仅当值不为空时添加）
     */
    private void addOptionalKeyValue(List<PrintRow> printRows, String keyConstant, String value) {
        if (StringUtils.isNotEmpty(value)) {
            addKeyStringValue(printRows, keyConstant, value);
        }
    }

    /**
     * 添加可选数字键值对
     */
    private void addOptionalKeyNumber(List<PrintRow> printRows, String keyConstant, Integer number) {
        if (Objects.nonNull(number)) {
            addKeyStringValue(printRows, keyConstant, String.valueOf(number));
        }
    }

    /**
     * 格式化性别为语言字符串
     */
    private String formatGender(Byte gender) {
        if (Objects.isNull(gender)) {
            return MultiLangUtils.get(Constant.GENDER_MAN);
        }
        return gender == 0 ?
                MultiLangUtils.get(Constant.GENDER_WOMAN) :
                MultiLangUtils.get(Constant.GENDER_MAN);
    }

    /**
     * 添加金额行（使用小号加粗字体）
     */
    private void addAmountRow(List<PrintRow> printRows, String keyConstant, BigDecimal amount) {
        String key = MultiLangUtils.get(keyConstant);
        String amountStr = BigDecimalUtils.moneyTrimmedString(amount);
        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(key, AMOUNT_FONT)
                .setValueString(amountStr, AMOUNT_FONT));
    }

    /**
     * 格式化时间戳为字符串
     */
    private String formatTime(Long timestamp) {
        return DateTimeUtils.mills2String(timestamp, YMD_PATTERN);
    }

    @Override
    public String getFailedMsg() {
        return "预付金单打印失败，请及时处理";
    }
}
