package com.holder.saas.print.template;

import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holder.saas.print.utils.template.row.composite.PrintLayoutUtils;
import com.holderzone.saas.store.dto.print.content.PrintReserveDTO;
import com.holderzone.saas.store.dto.print.content.nested.PrintReserveItem;
import com.holderzone.saas.store.dto.print.content.nested.PrintReserveType;
import com.holderzone.saas.store.dto.print.format.ReservePreOrderingFormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandOverTemplate
 * @date 2018/02/14 09:00
 * @description 交接单模板
 * @program holder-saas-store-print
 */
public class ReserveItemsTemplate extends AbsPrintTemplate<PrintReserveDTO, ReservePreOrderingFormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintReserveDTO printDTO = getPrintDTO();
        List<PrintRow> printRows = new ArrayList<>();

        PrintRowUtils.add(printRows, new Section()
                .addText(printDTO.getStoreName(), Font.NORMAL_BOLD)
                .setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get("pre_order_items_statistics_invoice_header"), Font.NORMAL)
                .setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("start_time"))
                .setValueString(printDTO.getStartTime()));
        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("end_time"))
                .setValueString(printDTO.getEndTime()));

        PrintRowUtils.add(printRows, new Separator());

        String typeName;
        if (printDTO.getType() == 1) {
            typeName = MultiLangUtils.get("single_item");
        } else {
            typeName = MultiLangUtils.get("combo");
        }
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("item")+"(" + typeName + ")")
                .setValueString(MultiLangUtils.get("pre_order_quantity")));

        List<PrintReserveType> typeRecordList = printDTO.getTypeRecordList();
        if (!ObjectUtils.isEmpty(typeRecordList)) {
            for (PrintReserveType reserveType : typeRecordList) {
                PrintRowUtils.add(printRows, new Separator());
                PrintRowUtils.add(printRows, new Section()
                        .addText(reserveType.getItemTypeName() + "("+MultiLangUtils.get("item_sum")+"):" + reserveType.getItemTypeNum(),
                                Font.SMALL)
                        .setAlign(Text.Align.Center));

                List<PrintReserveItem> itemRecordList = reserveType.getItemRecordList();
                if (!ObjectUtils.isEmpty(itemRecordList)) {
                    for (PrintReserveItem printReserveItem : itemRecordList) {
                        PrintRowUtils.add(printRows, new KeyValue()
                                .setKeyString(printReserveItem.getItemName())
                                .setValueString(printReserveItem.getNumber().toString()));
                    }
                }
            }
        }
        PrintRowUtils.add(printRows, new Separator());
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("price_total"))
                .setValueString(printDTO.getTotalNum().toString()));
        PrintRowUtils.add(printRows, new Separator());
        PrintLayoutUtils.addOpStaffAndPrintTime(getPageSize(), printDTO.getOperatorStaffName(), printDTO.getCreateTime(), printRows);
        return printRows;

    }



    @Override
    public String getFailedMsg() {
        return "预点餐商品统计打印失败，请及时处理";
    }
}
