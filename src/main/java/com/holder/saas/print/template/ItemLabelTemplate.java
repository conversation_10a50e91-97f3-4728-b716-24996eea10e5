package com.holder.saas.print.template;

import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.StringPrintUtils;
import com.holder.saas.print.utils.template.label.PrintLabelUtils;
import com.holderzone.saas.store.dto.print.content.PrintItemLabelDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Text;

import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LabelTemplate
 * @date 2018/02/14 09:00
 * @description 商品标签单模板
 * @program holder-saas-store-print
 */
public class ItemLabelTemplate extends AbsPrintTemplate<PrintItemLabelDTO, FormatDTO> {

    public static final Integer MAX_TEXT_LENGTH = 20;

    public static final Integer MAX_TEXT_CENTER_LENGTH = 24;

    @Override
    public List<PrintRow> getContent() {
        PrintItemLabelDTO printDTO = getPrintDTO();
        List<PrintRow> printRows = new ArrayList<>();
        int pageSize = getPageSize();
        // 菜品名字
        String itemName = printDTO.getItemName();
        // 截取长度
        if (StringPrintUtils.getLength(itemName) > MAX_TEXT_LENGTH) {
            itemName = StringPrintUtils.substringByte(itemName, 0, MAX_TEXT_LENGTH) + "...";
        }
        itemName = StringPrintUtils.strToCenter(itemName, MAX_TEXT_CENTER_LENGTH);
        PrintLabelUtils.addSection(printRows, itemName, Text.Align.Center, pageSize, 1);
        // 条形码
        PrintLabelUtils.addBarCode(printRows, printDTO.getSerialNumber());
        // 重量 + 价格
        String count = "   " + printDTO.getCurrentCount().toPlainString() + printDTO.getUnit();
        String price = "￥" + printDTO.getItemPrice().setScale(2, RoundingMode.DOWN).toPlainString() + " ";
        PrintLabelUtils.addKeyValueAsCoordinateRow(printRows, count, price, pageSize, 1);
        return printRows;
    }

    @Override
    public String getFailedMsg() {
        return "商品标签单打印失败，请及时处理";
    }

}
