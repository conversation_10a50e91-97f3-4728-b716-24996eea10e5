package com.holder.saas.print.template.base;

import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;

import java.util.function.Consumer;

public interface PrintTemplateBuilder<T extends PrintDTO, F extends FormatDTO> {

    void setPageSize(int pageSize);

    void setPrintDTO(T t);

    void setPrintDTO(T t, Consumer<T> consumer);

    void setFormatDTO(F f);

    void setFormatDTO(F f, Consumer<F> consumer);

    PrintTemplate<T, F> build();

    PrintTemplateAware<T, F> buildAware();
}
