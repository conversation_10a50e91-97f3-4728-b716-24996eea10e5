package com.holder.saas.print.template.retail;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holder.saas.print.utils.template.row.composite.PrintLayoutUtils;
import com.holder.saas.print.utils.template.row.composite.table.PrintTableUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.print.content.PrintTakeoutDTO;
import com.holderzone.saas.store.dto.print.content.nested.AdditionalCharge;
import com.holderzone.saas.store.dto.print.content.nested.ReduceRecord;
import com.holderzone.saas.store.dto.print.format.TakeoutFormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.*;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeoutTemplate
 * @date 2018/02/14 09:00
 * @description 外卖单模板
 * @program holder-saas-store-print
 */
public class RetailTakeoutTemplate extends AbsPrintTemplate<PrintTakeoutDTO, TakeoutFormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintTakeoutDTO printDTO = getPrintDTO();
        List<PrintRow> printRows = new ArrayList<>();

        if (printDTO.getAbnormal()) {
            // 平台
            PrintRowUtils.add(printRows, new Section()
                    .addText(printDTO.getPlatform() + printDTO.getPlatformOrder(), Font.NORMAL_BOLD)
                    .setAlign(Text.Align.Center));

            PrintRowUtils.add(printRows, new BlankRow());

            // 外卖打印异常说明
            PrintRowUtils.add(printRows, new ReverseText("[外卖打印异常单]", Font.NORMAL_BOLD, Text.Align.Center));

            PrintRowUtils.add(printRows, new BlankRow());

            // 外卖打印异常菜品说明
            PrintRowUtils.add(printRows, new Section()
                    .addText("以下菜品未与门店系统菜品进行绑定，无法打印后厨")
                    .setAlign(Text.Align.Left));
        } else {

            // 平台
            PrintRowUtils.add(printRows, new Section()
                    .addText(printDTO.getPlatform() + printDTO.getPlatformOrder(), Font.NORMAL_BOLD)
                    .setAlign(Text.Align.Center));

            // 门店名
            PrintRowUtils.add(printRows, new Section()
                    .addText(printDTO.getStoreName())
                    .setAlign(Text.Align.Center));

            // 支付情况
            PrintRowUtils.add(printRows, new Section()
                    .addText("--" + printDTO.getPayMsg() + "--", Font.NORMAL_BOLD)
                    .setAlign(Text.Align.Center));
        }

        PrintRowUtils.add(printRows, new Separator());

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString("期望送达时间：")
                .setValueString(printDTO.getExpectTime()));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString("下单时间：")
                .setValueString(DateTimeUtils.mills2String(printDTO.getOrderTime())));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get(Constant.ORDER_NUMBER))
                .setValueString(printDTO.getOrderNo()));

        if (StringUtils.hasText(printDTO.getRemark())) {
            PrintRowUtils.add(printRows, new Separator());
            PrintRowUtils.add(printRows, new Section()
                    .addText("备注：" + printDTO.getRemark(), Font.NORMAL_BOLD));
        }

        printRows.addAll(PrintTableUtils.resolveTableItemRow(printDTO.getItemRecordList(), null, getPageSize(), null, null));

        if (!printDTO.getAbnormal()) {
            // 商品总额
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString("商品总额")
                    .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getItemTotalPrice())));
            // 附加费
            if (!CollectionUtils.isEmpty(printDTO.getAdditionalChargeList())) {
                for (AdditionalCharge additionalCharge : printDTO.getAdditionalChargeList()) {
                    if (additionalCharge.getChargeValue().compareTo(BigDecimal.ZERO) != 0) {
                        PrintRowUtils.add(printRows, new KeyValue()
                                .setAlignEdges(false)
                                .setKeyString(additionalCharge.getChargeName() + "：")
                                .setValueString(BigDecimalUtils.moneyTrimmedString(additionalCharge.getChargeValue())));
                    }
                }
            }
            // 折扣
            if (!CollectionUtils.isEmpty(printDTO.getReduceRecordList())) {
                for (ReduceRecord reduceRecord : printDTO.getReduceRecordList()) {
                    if (reduceRecord.getAmount().compareTo(BigDecimal.ZERO) != 0) {
                        PrintRowUtils.add(printRows, new KeyValue()
                                .setAlignEdges(false)
                                .setKeyString(reduceRecord.getName() + "：")
                                .setValueString(BigDecimalUtils.moneyTrimmedString(reduceRecord.getAmount())));
                    }
                }
            }

            PrintRowUtils.add(printRows, new Separator());

            // 原价
            PrintRowUtils.add(printRows, new Section()
                    .setAlign(Text.Align.Right)
                    .addText("原价：" + BigDecimalUtils.moneyTrimmedString(printDTO.getOriginalPrice())));

            // 实付
            PrintRowUtils.add(printRows, new Section()
                    .setAlign(Text.Align.Right)
                    .addText("实付：" + BigDecimalUtils.moneyTrimmedString(printDTO.getActuallyPay()), Font.NORMAL_BOLD));
        }

        // 客户姓名
        PrintRowUtils.add(printRows, new Section("姓名:" + printDTO.getReceiverName(), Font.NORMAL_BOLD));

        // 客户电话
        String[] receiverTels = printDTO.getReceiverTel().split(",");
        if (receiverTels.length > 0) {
            String[] phoneInfo = receiverTels[0].split("_");
            String privacyPhone = phoneInfo.length == 1 ? phoneInfo[0] : phoneInfo[0] + "转" + phoneInfo[1];
            PrintRowUtils.add(printRows, new Section("手机号:" + privacyPhone, Font.NORMAL_BOLD));
        }
        if (receiverTels.length > 1) {
            String[] phoneInfo = receiverTels[1].split("_");
            String privacyPhone = phoneInfo.length == 1 ? phoneInfo[0] : phoneInfo[0] + "转" + phoneInfo[1];
            PrintRowUtils.add(printRows, new Section("虚拟号:" + privacyPhone, Font.NORMAL_BOLD));
        }
        if (receiverTels.length > 2) {
            String[] phoneInfo = receiverTels[2].split("_");
            String privacyPhone = phoneInfo.length == 1 ? phoneInfo[0] : phoneInfo[0] + "转" + phoneInfo[1];
            PrintRowUtils.add(printRows, new Section("备用号:" + privacyPhone, Font.NORMAL_BOLD));
        }

        // 客户地址信息
        PrintRowUtils.add(printRows, new Section("地址:" + printDTO.getReceiverAddress(), Font.NORMAL_BOLD));

        PrintRowUtils.add(printRows, new Separator());

        // 结账操作员，结账单打印时间
        PrintLayoutUtils.addOpStaffAndPrintTime(getPageSize(), printDTO.getOperatorStaffName(), printDTO.getCreateTime(), printRows);

        return printRows;
    }

    @Override
    public String getFailedMsg() {
        return "外卖单 [" + getPrintDTO().getPlatformOrder() + "]号订单打印失败，请及时处理";
    }
}
