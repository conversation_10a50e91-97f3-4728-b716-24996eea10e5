package com.holder.saas.print.template;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.composite.PrintLayoutUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.print.content.PrintMemStatsDTO;
import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemStatsTemplate
 * @date 2018/02/14 09:00
 * @description 会员销售统计模板
 * @program holder-saas-store-print
 */
public class MemStatsTemplate extends AbsPrintTemplate<PrintMemStatsDTO, FormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintMemStatsDTO printDTO = getPrintDTO();
        List<PrintRow> printRows = new ArrayList<>();

        // 门店名
        PrintRowUtils.add(printRows, new Section()
                .addText(printDTO.getStoreName(), Font.NORMAL_BOLD)
                .setAlign(Text.Align.Center));

        // 票据类型
        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get("member_consumption_statistics_invoice_header"), Font.NORMAL)
                .setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("start_time"))
                .setValueString(DateTimeUtils.mills2String(printDTO.getStartTime(), "yyyy-MM-dd HH:mm:ss")));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("end_time"))
                .setValueString(DateTimeUtils.mills2String(printDTO.getEndTime(), "yyyy-MM-dd HH:mm:ss")));

        PrintRowUtils.add(printRows, new Separator());

        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get("member_consumption_overview"))
                .setAlign(Text.Align.Left));

        PrintRowUtils.add(printRows, new Separator());

        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("number_of_consumption_orders"))
                .setValueString(printDTO.getConsumeQuantity() + "/" +MultiLangUtils.get(Constant.TRANSACTIONS)));

        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("total_consumption_amount"))
                .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getConsumeTotal())));

        PrintRowUtils.add(printRows, new Separator());

        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get("member_recharge_overview"))
                .setAlign(Text.Align.Left));

        PrintRowUtils.add(printRows, new Separator());

        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("number_of_recharge_orders"))
                .setValueString(printDTO.getRechargeQuantity() + "/" + MultiLangUtils.get(Constant.TRANSACTIONS)));

        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("total_recharge_amount"))
                .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getRechargeTotal())));

        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("recharge_bonus"))
                .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getRechargeGift())));

        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("recharge_income"))
                .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getRechargeIncome())));

        PrintRowUtils.add(printRows, new Separator());

        if (!CollectionUtils.isEmpty(printDTO.getRechargeDetail())) {
            String prefix = 80 == getPageSize() ? "————" : "——";
            for (PayRecord payRecord : printDTO.getRechargeDetail()) {
                PrintRowUtils.add(printRows, new KeyValue()
                        .setKeyString(prefix + payRecord.getPayName())
                        .setValueString(BigDecimalUtils.moneyTrimmedString(payRecord.getAmount())));
            }
            PrintRowUtils.add(printRows, new Separator());
        }

        PrintLayoutUtils.addOpStaffAndPrintTime(getPageSize(), printDTO.getOperatorStaffName(), printDTO.getCreateTime(), printRows);

        return printRows;
    }

    @Override
    public String getFailedMsg() {
        return "会员消费统计单打印失败，请及时处理";
    }
}
