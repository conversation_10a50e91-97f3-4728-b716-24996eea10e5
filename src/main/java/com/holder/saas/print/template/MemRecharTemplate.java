package com.holder.saas.print.template;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holder.saas.print.utils.template.row.composite.PrintLayoutUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.print.content.PrintRechargeStatsDTO;
import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import com.holderzone.saas.store.util.EncryptionSymbolUtil;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemStatsTemplate
 * @date 2018/02/14 09:00
 * @description 会员销售统计模板
 * @program holder-saas-store-print
 */
public class MemRecharTemplate extends AbsPrintTemplate<PrintRechargeStatsDTO, FormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintRechargeStatsDTO printDTO = getPrintDTO();
        List<PrintRow> printRows = new ArrayList<>();

        // 门店名
        PrintRowUtils.add(printRows, new Section()
                .addText(printDTO.getStoreName(), Font.NORMAL_BOLD)
                .setAlign(Text.Align.Center));

        // 票据类型
        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get("member_recharge_statistics_invoice_header"), Font.NORMAL)
                .setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("start_time"))
                .setValueString(DateTimeUtils.mills2String(printDTO.getStartTime(), "yyyy-MM-dd HH:mm:ss")));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("end_time"))
                .setValueString(DateTimeUtils.mills2String(printDTO.getEndTime(), "yyyy-MM-dd HH:mm:ss")));

        PrintRowUtils.add(printRows, new Separator());

        // 充值单数
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("number_of_recharge_orders"))
                .setValueString(getRechargeQuantity()));

        // 充值人数
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("recharge_users"))
                .setValueString(getRechargeMemberNum()));

        // 充值金额
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("total_recharge_amount"))
                .setValueString(getRechargeTotal()));

        // 充值赠送金额
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("recharge_bonus"))
                .setValueString(getRechargeGift()));

        // 充值收入金额
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("recharge_income"))
                .setValueString(getRechargeIncome()));

        PrintRowUtils.add(printRows, new Separator());

        // 充值收入权限
        boolean isConsumeTotalEncryption = EncryptionSymbolUtil.isEncryption(printDTO.getRechargeIncomeStr());
        if (!isConsumeTotalEncryption && !CollectionUtils.isEmpty(printDTO.getRechargeDetail())) {
            String prefix = 80 == getPageSize() ? "————" : "——";
            for (PayRecord payRecord : printDTO.getRechargeDetail()) {
                PrintRowUtils.add(printRows, new KeyValue()
                        .setKeyString(prefix + payRecord.getPayName())
                        .setValueString(BigDecimalUtils.moneyTrimmedString(payRecord.getAmount())));
            }
            PrintRowUtils.add(printRows, new Separator());
        }

        PrintLayoutUtils.addOpStaffAndPrintTime(getPageSize(), printDTO.getOperatorStaffName(), printDTO.getCreateTime(), printRows);

        return printRows;
    }

    /**
     * 获取充值单数
     */
    private String getRechargeQuantity() {
        PrintRechargeStatsDTO printDTO = getPrintDTO();
        return EncryptionSymbolUtil.isEncryption(printDTO.getRechargeQuantityStr()) ?
                printDTO.getRechargeQuantityStr() :
                printDTO.getRechargeQuantity() + "/" + MultiLangUtils.get(Constant.TRANSACTIONS);
    }

    /**
     * 获取充值人数
     */
    private String getRechargeMemberNum() {
        PrintRechargeStatsDTO printDTO = getPrintDTO();
        return EncryptionSymbolUtil.isEncryption(printDTO.getRechargeMemberNumStr()) ?
                printDTO.getRechargeMemberNumStr() :
                printDTO.getRechargeMemberNum() + "/" + MultiLangUtils.get(Constant.PERSON);
    }

    /**
     * 获取充值金额
     */
    private String getRechargeTotal() {
        PrintRechargeStatsDTO printDTO = getPrintDTO();
        return EncryptionSymbolUtil.isEncryption(printDTO.getRechargeTotalStr()) ?
                printDTO.getRechargeTotalStr() :
                BigDecimalUtils.moneyTrimmedString(printDTO.getRechargeTotal());
    }


    /**
     * 获取充值赠送金额
     */
    private String getRechargeGift() {
        PrintRechargeStatsDTO printDTO = getPrintDTO();
        return EncryptionSymbolUtil.isEncryption(printDTO.getRechargeGiftStr()) ?
                printDTO.getRechargeGiftStr() :
                BigDecimalUtils.moneyTrimmedString(printDTO.getRechargeGift());
    }

    /**
     * 获取充值收入金额
     */
    private String getRechargeIncome() {
        PrintRechargeStatsDTO printDTO = getPrintDTO();
        return EncryptionSymbolUtil.isEncryption(printDTO.getRechargeIncomeStr()) ?
                printDTO.getRechargeIncomeStr() :
                BigDecimalUtils.moneyTrimmedString(printDTO.getRechargeIncome());
    }


    @Override
    public String getFailedMsg() {
        return "会员消费统计单打印失败，请及时处理";
    }
}
