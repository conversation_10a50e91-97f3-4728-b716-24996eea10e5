package com.holder.saas.print.template;

import com.holder.saas.print.template.base.AbsKitchenItemTemplate;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.composite.PrintFormatLayoutContainer;
import com.holderzone.saas.store.dto.print.content.PrintRefundItemDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.format.RefundItemFormatDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RefundItemTemplate
 * @date 2018/02/14 09:00
 * @description 退菜单模板
 * @program holder-saas-store-print
 */
public class RefundItemTemplate extends AbsKitchenItemTemplate<PrintRefundItemDTO, RefundItemFormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintRefundItemDTO printDTO = getPrintDTO();
        RefundItemFormatDTO formatDTO = getFormatDTO();
        PrintFormatLayoutContainer container = new PrintFormatLayoutContainer();

        // 票据类型
        container.addInvoiceType(MultiLangUtils.get("refund_item_invoice_header"), formatDTO.getInvoiceName());

        // 牌号
        // 订单号、人数
        container.addMarkOrTableNoAndOrderAndGuest(
                getPageSize(), printDTO.getTradeMode(),
                printDTO.getMarkNo(), formatDTO.getMarkNo(),
                printDTO.getOrderNo(), formatDTO.getOrderNo(),
                printDTO.getPersonNumber(), formatDTO.getPersonNumber());

        // 商品
        // fixme “退”也需要英文版，不能其他服务传了，需要自己标记
        container.addAll(super.getContent());

        // 操作员、下单、打印
        container.addOpStaffAndOrderTimeAndPrintTime(getPageSize(),
                printDTO.getOperatorStaffName(), formatDTO.getOperator(),
                printDTO.getOrderTime(), formatDTO.getOrderTime(),
                printDTO.getCreateTime(), formatDTO.getPrintTime());

        return container.getPrintRows();
    }

    @Override
    protected boolean isCancel() {
        return true;
    }

    @Override
    public String getFailedMsg() {
        return "退菜单 [" + getPrintDTO().getMarkNo() + "]号订单打印失败，请及时处理";
    }
}
