package com.holder.saas.print.template;

import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.print.content.PrintQueueDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.QrCode;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueTemplate
 * @date 2019/05/07 13:40
 * @description 排队单模板
 * @program holder-saas-store-print
 */
public class QueueTemplate extends AbsPrintTemplate<PrintQueueDTO, FormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintQueueDTO printDTO = getPrintDTO();
        List<PrintRow> printRows = new ArrayList<>();

        PrintRowUtils.add(printRows, new Section(printDTO.getStoreName(), Font.NORMAL_BOLD).setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new Section(MultiLangUtils.get("pick_up_time")
                + DateTimeUtils.mills2String(printDTO.getQueueTime())));

        PrintRowUtils.add(printRows, new Section(MultiLangUtils.get("number_diners") + printDTO.getPersonNumber()));

        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get("your_number_is"))
                .addText(printDTO.getQueueNumber(), Font.NORMAL_BOLD));

        PrintRowUtils.add(printRows, new Section(String.format(MultiLangUtils.get("waiting_tables"),printDTO.getWaitNumber()) ));

        if (StringUtils.hasText(printDTO.getQueueUrl())) {
            PrintRowUtils.add(printRows, new QrCode(printDTO.getQueueUrl()));
            PrintRowUtils.add(printRows, new Section(MultiLangUtils.get("scan_qr")).setAlign(Text.Align.Center));
        }

        PrintRowUtils.add(printRows, new Separator());

        if (StringUtils.hasText(printDTO.getMarketingInfo())) {
            PrintRowUtils.add(printRows, new Section(printDTO.getMarketingInfo()));
        }

        PrintRowUtils.add(printRows, new Section(MultiLangUtils.get("proceed_ticket")));

        if (StringUtils.hasText(printDTO.getTel())) {
            PrintRowUtils.add(printRows, new Section(MultiLangUtils.get("store_phone_number") + printDTO.getTel()));
        }

        PrintRowUtils.add(printRows, new Separator());

        PrintRowUtils.add(printRows, new Section(MultiLangUtils.get("provided_company")).setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new Section("www.holderzone.com").setAlign(Text.Align.Center));

        return printRows;
    }

    @Override
    public String getFailedMsg() {
        return "排队单打印失败，请及时处理";
    }
}
