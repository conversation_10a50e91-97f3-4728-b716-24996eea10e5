package com.holder.saas.print.template;

import cn.hutool.core.util.DesensitizedUtil;
import com.holder.saas.print.template.base.AbsCustomTemplate;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.StringPrintUtils;
import com.holder.saas.print.utils.template.row.composite.PrintFormatLayoutContainer;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.print.content.PrintStoredCashDTO;
import com.holderzone.saas.store.dto.print.format.StoredCashFormatDTO;
import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoredCashTemplate
 * @date 2018/02/14 09:00
 * @description 储值单模板
 * @program holder-saas-store-print
 */
public class StoredCashTemplate extends AbsCustomTemplate<PrintStoredCashDTO, StoredCashFormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintStoredCashDTO printDTO = getPrintDTO();
        StoredCashFormatDTO formatDTO = getFormatDTO();
        PrintFormatLayoutContainer container = new PrintFormatLayoutContainer();

        // 门店名
        container.addStoreName(printDTO.getStoreName(), formatDTO.getStoreName());

        // 票据类型
        container.addInvoiceType(MultiLangUtils.get("stored_value_invoice_header"), formatDTO.getInvoiceName());

        // 流水
        FormatMetadata serialNumber = formatDTO.getSerialNumber();
        if (serialNumber.isEnable()) {
            container.addSection(new Section(MultiLangUtils.get("transaction") + printDTO.getSerialNumber(), serialNumber.toFont()));
        }
        //会员姓名
        FormatMetadata memberName = formatDTO.getMemberName();
        if(memberName.isEnable() && !StringUtils.isEmpty(printDTO.getMemberName())){
            container.addSection(new Section(MultiLangUtils.get("member_name_colon") + StringPrintUtils.protectedName(printDTO.getMemberName()), memberName.toFont()));
        }
        //会员手机
        FormatMetadata memberPhone = formatDTO.getMemberPhone();
        if(memberPhone.isEnable() && !StringUtils.isEmpty(printDTO.getMemberPhone())){
            container.addSection(new Section(MultiLangUtils.get("member_phone_colon") + DesensitizedUtil.mobilePhone(printDTO.getMemberPhone()), memberPhone.toFont()));
        }
        // 充值卡号
        FormatMetadata cardNo = formatDTO.getCardNo();
        if (cardNo.isEnable()) {
            container.addSection(new Section(MultiLangUtils.get("recharge_card_number1") + printDTO.getCardNo(), cardNo.toFont()));
        }

        // 充值金额
        FormatMetadata recharge = formatDTO.getRecharge();
        if (recharge.isEnable()) {
            container.addKeyValue(new KeyValue()
                    .setKeyString(MultiLangUtils.get("recharge_amount1"), recharge.toFont())
                    .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getRecharge()), recharge.toFont()));
        }

        // 支付方式
        FormatMetadata payType = formatDTO.getPayType();
        if (payType.isEnable()) {
            container.addKeyValue(new KeyValue()
                    .setKeyString(MultiLangUtils.get("pay_type"), payType.toFont())
                    .setValueString(printDTO.getPayWay(), payType.toFont()));
        }

        container.addSeparator();

        // 赠送金额
        // 赠送积分
        addGiftContainer(formatDTO, container, printDTO);

        // 当前金额
        // 当前积分
        addCurrentContainer(formatDTO, container, printDTO);

        // 操作员、充值时间
        // 打印时间
        container.addOpStaffAndRechargeTimeAndPrintTime(getPageSize(),
                printDTO.getOperatorStaffName(), formatDTO.getOperator(),
                printDTO.getRechargeTime(), formatDTO.getRechargeTime(),
                printDTO.getCreateTime(), formatDTO.getPrintTime());

        return container.getPrintRows();
    }

    private void addCurrentContainer(StoredCashFormatDTO formatDTO,
                                     PrintFormatLayoutContainer container,
                                     PrintStoredCashDTO printDTO) {
        FormatMetadata currentCash = formatDTO.getCurrentCash();
        FormatMetadata integration = formatDTO.getIntegration();
        if (currentCash.isEnable() || integration.isEnable()) {
            if (currentCash.isEnable()) {
                container.addKeyValue(new KeyValue()
                        .setKeyString(MultiLangUtils.get("current_balance1"), currentCash.toFont())
                        .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getCurrentCash()), currentCash.toFont()));
            }
            if (integration.isEnable()) {
                container.addKeyValue(new KeyValue()
                        .setKeyString(MultiLangUtils.get("current_points1"), integration.toFont())
                        .setValueString(printDTO.getIntegration(), integration.toFont()));
            }
            container.addSeparator();
        }
    }

    private void addGiftContainer(StoredCashFormatDTO formatDTO,
                                  PrintFormatLayoutContainer container,
                                  PrintStoredCashDTO printDTO) {
        FormatMetadata presented = formatDTO.getPresented();
        FormatMetadata presentedIntegral = formatDTO.getPresentedIntegral();
        if (presented.isEnable() || presentedIntegral.isEnable()) {
            if (presented.isEnable()) {
                container.addKeyValue(new KeyValue()
                        .setKeyString(MultiLangUtils.get("gift_amount1"), presented.toFont())
                        .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getPresented()), presented.toFont()));
            }
            if (presentedIntegral.isEnable()) {
                container.addKeyValue(new KeyValue()
                        .setKeyString(MultiLangUtils.get("reward_points"), presentedIntegral.toFont())
                        .setValueString(BigDecimalUtils.quantityTrimmedString(printDTO.getPresentedIntegral()), presentedIntegral.toFont()));
            }
            container.addSeparator();
        }
    }

    @Override
    public String getFailedMsg() {
        return "储值单打印失败，请及时处理";
    }
}
