package com.holder.saas.print.template;

import cn.hutool.core.util.ObjectUtil;
import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holder.saas.print.utils.template.row.composite.PrintLayoutUtils;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.dto.print.content.PrintHandOverNewDTO;
import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import com.holderzone.saas.store.dto.print.format.HandOverFormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandOverTemplate
 * @date 2018/02/14 09:00
 * @description 交接单模板
 * @program holder-saas-store-print
 */
public class HandOverPreNewTemplate extends AbsPrintTemplate<PrintHandOverNewDTO, HandOverFormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintHandOverNewDTO printDTO = getPrintDTO();
        List<PrintRow> printHandOverNewRows = new ArrayList<>();

        PrintRowUtils.add(printHandOverNewRows, new Section()
                .addText(printDTO.getStoreName(), Font.NORMAL_BOLD)
                .setAlign(Text.Align.Center));

        PrintRowUtils.add(printHandOverNewRows, new Section()
                .addText(MultiLangUtils.get("pre_handover_invoice_header"), Font.NORMAL)
                .setAlign(Text.Align.Center));

        if (Objects.nonNull(printDTO.getIsMultiHandover()) && printDTO.getIsMultiHandover() == 1) {
            PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKeyString(MultiLangUtils.get("handover_supervisor"))
                    .setValueString(printDTO.getHandoverName()));
        }
        PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("handover_employee"))
                .setValueString(printDTO.getStaffName()));
        PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("shift_duration"))
                .setValueString(printDTO.getDuration()));

        PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("shift_start_time"))
                .setValueString(printDTO.getBeginTime()));

        PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("shift_end_time"))
                .setValueString(printDTO.getOverTime()));

        PrintRowUtils.add(printHandOverNewRows, new Separator());

        if (!ObjectUtils.isEmpty(printDTO.getHandOnCash())) {
            PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKeyString(MultiLangUtils.get("amount_to_be_handed_over"))
                    .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getHandOnCash())));
        }

        if (!ObjectUtils.isEmpty(printDTO.getRealHandOnCash())) {
            PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKeyString(MultiLangUtils.get("actual_handed_over_cash"))
                    .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getRealHandOnCash())));

            PrintRowUtils.add(printHandOverNewRows, new Separator());
        }

        if (!ObjectUtils.isEmpty(printDTO.getTraffic())) {
            PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKeyString(MultiLangUtils.get("hand_over_traffic"))
                    .setValueString(printDTO.getTraffic() + "/" + MultiLangUtils.get(Constant.PERSON)));
        }

        if (!ObjectUtils.isEmpty(printDTO.getOccupancyRatePercent())) {
            PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKeyString(MultiLangUtils.get("hand_over_occupancy_rate_percent"))
                    .setValueString(printDTO.getOccupancyRatePercent()));
        }

        if (!ObjectUtils.isEmpty(printDTO.getOpenTableRatePercent())) {
            PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKeyString(MultiLangUtils.get("hand_over_open_table_tate_percent"))
                    .setValueString(printDTO.getOpenTableRatePercent()));
        }

        if (!ObjectUtils.isEmpty(printDTO.getFlipTableRatePercent())) {
            PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKeyString(MultiLangUtils.get("hand_over_flip_table_tate_percent"))
                    .setValueString(printDTO.getFlipTableRatePercent()));
        }

        if (!ObjectUtils.isEmpty(printDTO.getAvgDineInTime())) {
            PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKeyString(MultiLangUtils.get("hand_over_avg_dine_in_time"))
                    .setValueString(String.valueOf(printDTO.getAvgDineInTime())));
        }

        PrintRowUtils.add(printHandOverNewRows, new Separator());

        PrintRowUtils.add(printHandOverNewRows, new Section()
                .addText(MultiLangUtils.get("sales_statistics"), Font.SMALL).setAlign(Text.Align.Center));

        PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("number_of_completed_sales_orders"), Font.SMALL_NORMAL_THIN_BOLD)
                .setValueString(printDTO.getSaleCount() + MultiLangUtils.get(Constant.TRANSACTIONS), Font.SMALL_NORMAL_THIN_BOLD));

        PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("hand_over_sales_amount"), Font.SMALL_NORMAL_THIN_BOLD)
                .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getSaleAmount()), Font.SMALL_NORMAL_THIN_BOLD));

        PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("hand_over_discount_amount"))
                .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getDiscountAmount())));

        PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("hand_over_refund_amount"))
                .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getRefundAmount())));

        PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("net_sales_amount"), Font.SMALL_NORMAL_THIN_BOLD)
                .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getSaleIncome()), Font.SMALL_NORMAL_THIN_BOLD));

        PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("hand_over_before_discount_per_order"))
                .setValueString(getComputeData(printDTO.getSaleAmount(), printDTO.getSaleCount())));

        PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("hand_over_after_discount_per_order"))
                .setValueString(getComputeData(printDTO.getSaleIncome(), printDTO.getSaleCount())));

        PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("hand_over_before_discount_per_person"))
                .setValueString(getComputeData(printDTO.getSaleAmount(), printDTO.getTraffic())));

        PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("hand_over_after_discount_per_person"))
                .setValueString(getComputeData(printDTO.getSaleIncome(), printDTO.getTraffic())));

        if (!CollectionUtils.isEmpty(printDTO.getSalePayRecordList())) {
            for (PayRecord payRecord : printDTO.getSalePayRecordList()) {
                PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                        .setKeyString(PaymentTypeEnum.PaymentType.getLocaleName(payRecord.getPayName()))
                        .setValueString(BigDecimalUtils.moneyTrimmedString(payRecord.getAmount())));
                if (Objects.nonNull(payRecord.getExcessAmount()) && payRecord.getExcessAmount().compareTo(BigDecimal.ZERO) > 0) {
                    PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                            .setKeyString("")
                            .setValueString("("+ MultiLangUtils.get("remaining_balance") + BigDecimalUtils.moneyTrimmedString(payRecord.getExcessAmount()) + ")"));
                }
                if (!CollectionUtils.isEmpty(payRecord.getInnerDetails())) {
                    // 打印第三方活动具体明细
                    for (AmountItemDTO.InnerDetails innerDetail : payRecord.getInnerDetails()) {
                        /*
                        PrintRowUtils.add(printRows, new KeyValue()
                                .setKeyString("  " + innerDetail.getName())
                                .setValueString(BigDecimalUtils.moneyTrimmedString(innerDetail.getAmount())));
                        */
                        if (!CollectionUtils.isEmpty(innerDetail.getInnerDetails())) {
                            innerDetail.getInnerDetails().forEach(details -> {
                                PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                                        .setKeyString("  " + details.getName() + ": " + details.getCount())
                                        .setValueString(BigDecimalUtils.moneyTrimmedString(details.getAmount())));
                            });
                        }
                    }
                }
            }
        }
        // 优惠方式统计
        if (!CollectionUtils.isEmpty(printDTO.getDiscountRecordList())) {
            for (PayRecord payRecord : printDTO.getDiscountRecordList()) {
                PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                        .setKeyString(payRecord.getPayName())
                        .setValueString(BigDecimalUtils.moneyTrimmedString(payRecord.getAmount())));
                if (!CollectionUtils.isEmpty(payRecord.getInnerDetails())) {
                    payRecord.getInnerDetails().forEach(details -> {
                        PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                                .setKeyString("    " + details.getName() + ": " + details.getCount())
                                .setValueString(BigDecimalUtils.moneyTrimmedString(details.getAmount())));
                    });
                }
            }
        }

        PrintRowUtils.add(printHandOverNewRows, new Separator());

        PrintRowUtils.add(printHandOverNewRows, new Section()
                .addText(MultiLangUtils.get("stored_value_statistics"), Font.SMALL).setAlign(Text.Align.Center));

        PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("number_of_stored_value_orders"), Font.SMALL_NORMAL_THIN_BOLD)
                .setValueString(printDTO.getRechargeCount() + MultiLangUtils.get(Constant.TRANSACTIONS), Font.SMALL_NORMAL_THIN_BOLD));

        PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("total_stored_value_amount"), Font.SMALL_NORMAL_THIN_BOLD)
                .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getRechargeIncome()), Font.SMALL_NORMAL_THIN_BOLD));

        if (!CollectionUtils.isEmpty(printDTO.getRechargePayRecordList())) {
            for (PayRecord payRecord : printDTO.getRechargePayRecordList()) {
                PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                        .setKeyString(payRecord.getPayName())
                        .setValueString(BigDecimalUtils.moneyTrimmedString(payRecord.getAmount())));
            }
        }

        PrintRowUtils.add(printHandOverNewRows, new Separator());

        PrintRowUtils.add(printHandOverNewRows, new Section()
                .addText(MultiLangUtils.get("reservation_statistics"), Font.SMALL).setAlign(Text.Align.Center));

        PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("number_of_reservation_orders"), Font.SMALL_NORMAL_THIN_BOLD)
                .setValueString(printDTO.getReserveCount() + MultiLangUtils.get(Constant.TRANSACTIONS), Font.SMALL_NORMAL_THIN_BOLD));

        PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("total_reservation_deposit"), Font.SMALL_NORMAL_THIN_BOLD)
                .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getReserveIncome()), Font.SMALL_NORMAL_THIN_BOLD));

        if (!CollectionUtils.isEmpty(printDTO.getReservePayRecordList())) {
            for (PayRecord payRecord : printDTO.getReservePayRecordList()) {
                PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                        .setKeyString(payRecord.getPayName())
                        .setValueString(BigDecimalUtils.moneyTrimmedString(payRecord.getAmount())));
            }
        }

        if (ObjectUtil.isNotNull(printDTO.getRepaymentFeeCount())) {
            PrintRowUtils.add(printHandOverNewRows, new Separator());

            PrintRowUtils.add(printHandOverNewRows, new Section()
                    .addText(MultiLangUtils.get("credit_account_statistics"), Font.SMALL).setAlign(Text.Align.Center));

            PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKeyString(MultiLangUtils.get("number_of_credit_account_orders"), Font.SMALL_NORMAL_THIN_BOLD)
                    .setValueString((ObjectUtil.isNotNull(printDTO.getRepaymentFeeCount()) ? printDTO.getRepaymentFeeCount() : 0) + MultiLangUtils.get(Constant.TRANSACTIONS), Font.SMALL_NORMAL_THIN_BOLD));

            PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKeyString(MultiLangUtils.get("total_credit_account_amount"), Font.SMALL_NORMAL_THIN_BOLD)
                    .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getRepaymentFeeTotal()), Font.SMALL_NORMAL_THIN_BOLD));

            if (!CollectionUtils.isEmpty(printDTO.getRepaymentList())) {
                for (PayRecord payRecord : printDTO.getRepaymentList()) {
                    PrintRowUtils.add(printHandOverNewRows, new KeyValue()
                            .setKeyString(payRecord.getPayName())
                            .setValueString(BigDecimalUtils.moneyTrimmedString(payRecord.getAmount())));
                }
            }
        }

        PrintRowUtils.add(printHandOverNewRows, new Separator());

        PrintLayoutUtils.addOpStaffAndPrintTime(getPageSize(), printDTO.getOperatorStaffName(), printDTO.getCreateTime(), printHandOverNewRows);

        return printHandOverNewRows;
    }

    private String getComputeData(BigDecimal amount, Integer count) {
        if (Objects.isNull(amount) || Objects.isNull(count) || count == 0) {
            return BigDecimalUtils.moneyTrimmedString(BigDecimal.ZERO);
        }
        return BigDecimalUtils.moneyTrimmedString(amount.divide(new BigDecimal(count), 2, RoundingMode.HALF_UP));
    }

    @Override
    public String getFailedMsg() {
        return "交接单打印失败，请及时处理";
    }
}
