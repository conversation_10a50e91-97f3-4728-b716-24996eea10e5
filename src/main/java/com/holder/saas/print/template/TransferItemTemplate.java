package com.holder.saas.print.template;

import com.google.common.collect.Lists;
import com.holder.saas.print.template.base.AbsFrontItemTemplate;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.kds.resp.KdsAttrGroupDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsItemAttrDTO;
import com.holderzone.saas.store.dto.print.content.PrintTransferItemDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Table;
import com.holderzone.saas.store.dto.trade.req.TransferItemDetailsDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 转菜单模板
 */
@Slf4j
public class TransferItemTemplate extends AbsFrontItemTemplate<PrintTransferItemDTO, FormatDTO> {

    private static final String TRANSFER = "【转】";

    @Override
    public List<PrintRow> getContent() {
        List<PrintRow> printRows = new ArrayList<>();
        PrintTransferItemDTO printDTO = getPrintDTO();
        // 单据名称
        PrintRowUtils.add(printRows, new Section()
                .addText(LocaleUtil.getMessage("transfer_item_list_invoice_header"), Font.NORMAL_BOLD)
                .setAlign(Text.Align.Center));
        // 空白行
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(Strings.EMPTY, Font.SMALL)
                .setValueString(Strings.EMPTY, Font.SMALL));
        // 原桌台
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(LocaleUtil.getMessage("old_table") + printDTO.getOldDiningTableName(), Font.NORMAL)
                .setValueString(Strings.EMPTY, Font.NORMAL));
        // 转桌台
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(LocaleUtil.getMessage("transfer_table") + printDTO.getNewDiningTableName(), Font.NORMAL)
                .setValueString(Strings.EMPTY, Font.NORMAL));
        // 订单号
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(LocaleUtil.getMessage("order_number") + printDTO.getOrderNo(), Font.SMALL)
                .setValueString(Strings.EMPTY, Font.SMALL));
        // ------------------------------------------------
        // 构建商品转菜table
        Table table = buildTransferItemTable();
        List<TransferItemDetailsDTO> transferItemDTOList = printDTO.getTransferItemList();
        for (TransferItemDetailsDTO transferItemDTO : transferItemDTOList) {
            // 商品名称
            String itemName = transferItemDTO.getItemName();
            if (!StringUtils.isEmpty(transferItemDTO.getSkuName())) {
                itemName = itemName + " (" + transferItemDTO.getSkuName() + ")";
            }
            table.addRow(Lists.newArrayList(
                    new Text(TRANSFER + itemName, Font.NORMAL_BOLD),
                    new Text("X" + BigDecimalUtils.quantityTrimmed(transferItemDTO.getCurrentCount()), Font.NORMAL_BOLD)));
            // 如果有属性
            addTransferItemAttrRows(table, transferItemDTO, false);
            // 如果有备注
            addTransferItemRemarkRows(table, transferItemDTO);
            // 套餐子项
            setSubItemPrintRow(table, transferItemDTO);
        }
        PrintRowUtils.add(printRows, table);

        // ------------------------------------------------
        // 操作员 + 更换时间
        String transferTime = DateTimeUtils.localDateTime2String(printDTO.getTransferTime(), "MM-dd HH:mm");
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(LocaleUtil.getMessage("operator_user") + printDTO.getOperatorStaffName(), Font.SMALL)
                .setValueString(LocaleUtil.getMessage("change_time") + transferTime, Font.SMALL));
        // 打印时间
        LocalDateTime printTime = LocalDateTime.now();
        String printTimeStr = DateTimeUtils.localDateTime2String(printTime, "MM-dd HH:mm");
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(Strings.EMPTY, Font.SMALL)
                .setValueString(LocaleUtil.getMessage("prt_time") + printTimeStr, Font.SMALL));
        return printRows;
    }

    @Override
    public String getFailedMsg() {
        return "转菜单打印失败，请及时处理";
    }

    /**
     * 构建商品转菜table
     */
    private Table buildTransferItemTable() {
        int pageSize = getPageSize();
        List<Text> headers = Lists.newArrayList(new Text(LocaleUtil.getMessage("item_name")), new Text(LocaleUtil.getMessage("quantity")));
        List<Integer> columnWidths = 58 == pageSize ? Lists.newArrayList(22, 10) : Lists.newArrayList(38, 10);
        List<Boolean> alignRights = Lists.newArrayList(false, true);
        return new Table(headers, columnWidths, alignRights, true);
    }


    /**
     * 转菜属性明细
     */
    private void addTransferItemAttrRows(Table table, TransferItemDetailsDTO itemDetailsDTO, boolean subItemFlag) {
        List<KdsAttrGroupDTO> attrGroupList = itemDetailsDTO.getAttrGroup();
        if (CollectionUtils.isEmpty(attrGroupList)) {
            return;
        }
        String space = subItemFlag ? "      " : "";
        for (KdsAttrGroupDTO kdsAttrGroupDTO : attrGroupList) {
            List<String> attrNameList = kdsAttrGroupDTO.getAttrs().stream().map(KdsItemAttrDTO::getAttrName).collect(Collectors.toList());
            String attr = kdsAttrGroupDTO.getGroupName() + ":" + String.join(",", attrNameList);
            table.addRow(Lists.newArrayList(
                    new Text(space + attr, Font.SMALL),
                    Text.BLANK));
        }
    }

    /**
     * 转菜备注
     */
    private void addTransferItemRemarkRows(Table table, TransferItemDetailsDTO transferItemDTO) {
        String itemRemark = transferItemDTO.getItemRemark();
        if (StringUtils.isEmpty(itemRemark)) {
            return;
        }
        table.addRow(Lists.newArrayList(
                new Text(MultiLangUtils.get("remark") + itemRemark, Font.SMALL),
                Text.BLANK));
    }

    private void setSubItemPrintRow(Table table, TransferItemDetailsDTO transferItemDTO) {
        if (CollectionUtils.isEmpty(transferItemDTO.getSubItemList())) {
            return;
        }
        List<TransferItemDetailsDTO> subItemList = transferItemDTO.getSubItemList();
        for (TransferItemDetailsDTO transferItem : subItemList) {
            table.addRow(Lists.newArrayList(
                    new Text("      -|" + transferItem.getItemName(), Font.SMALL),
                    new Text("X" + BigDecimalUtils.quantityTrimmedString(transferItem.getCurrentCount()), Font.SMALL)));
            // 如果有属性
            addTransferItemAttrRows(table, transferItem, true);
        }
    }
}
