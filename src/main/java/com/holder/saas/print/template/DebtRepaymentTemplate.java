package com.holder.saas.print.template;

import com.google.common.collect.Lists;
import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.StringPrintUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holder.saas.print.utils.template.row.composite.table.TableRowContext;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.print.content.PrintDebtRepaymentDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.BlankRow;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.util.LocaleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


/**
 * 挂账还款单模板
 */
@Slf4j
public class DebtRepaymentTemplate extends AbsPrintTemplate<PrintDebtRepaymentDTO, FormatDTO> {

    private static final String SPLIT = ": ";

    @Override
    public List<PrintRow> getContent() {
        List<PrintRow> printRows = new ArrayList<>();
        PrintDebtRepaymentDTO printDTO = getPrintDTO();
        // 单据名称
        PrintRowUtils.add(printRows, new Section()
                .addText(LocaleUtil.getMessage("debt_repayment_invoice_header"), Font.NORMAL_BOLD)
                .setAlign(Text.Align.Center));
        // 空白行
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(Strings.EMPTY, Font.SMALL)
                .setValueString(Strings.EMPTY, Font.SMALL));
        // 单位名称
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(LocaleUtil.getMessage("debt_repayment_unit_name") + SPLIT + printDTO.getUnitName(), Font.SMALL)
                .setValueString(Strings.EMPTY, Font.SMALL));
        // 挂账单位联系人
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(LocaleUtil.getMessage("debt_repayment_unit_contact_name") + SPLIT +
                        StringPrintUtils.protectedName(printDTO.getUnitContactName()), Font.SMALL)
                .setValueString(Strings.EMPTY, Font.SMALL));
        // 挂账单位联系人电话
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(LocaleUtil.getMessage("debt_repayment_unit_contact_tel") + SPLIT + printDTO.getUnitContactTel(), Font.SMALL)
                .setValueString(Strings.EMPTY, Font.SMALL));
        // ------------------------------------------------
        // 还款信息
        addDebtRepaymentRows(printRows);
        // ------------------------------------------------

        // 会员信息
        addDebtMemberInfo(printRows, printDTO);

        // 还款时间 + 操作员
        appendRepaymentTimeAndStaff(printRows);
        // 空白行
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(Strings.EMPTY, Font.SMALL)
                .setValueString(Strings.EMPTY, Font.SMALL));
        return printRows;
    }

    private void addDebtMemberInfo(List<PrintRow> printRows, PrintDebtRepaymentDTO printDTO) {
        boolean hasMember = false;
        // 会员姓名
        if (StringUtils.hasText(printDTO.getMemberName())) {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(LocaleUtil.getMessage("debt_member_name"), Font.SMALL)
                    .setValueString(StringPrintUtils.protectedName(printDTO.getMemberName()), Font.SMALL));
            hasMember = true;
        }

        // 会员手机号
        if (StringUtils.hasText(printDTO.getMemberPhone())) {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(LocaleUtil.getMessage("debt_member_phone"), Font.SMALL)
                    .setValueString(printDTO.getMemberPhone(), Font.SMALL));
            hasMember = true;
        }

        // 会员余额
        if (!ObjectUtils.isEmpty(printDTO.getMemberMoney())) {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(LocaleUtil.getMessage("debt_member_money"), Font.SMALL)
                    .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getMemberMoney()), Font.SMALL));
            hasMember = true;
        }

        // 空白行
        if (hasMember) {
            int pageSize = getPageSize();
            List<Integer> columnWidths = 58 == pageSize ? Lists.newArrayList(20, 12) : Lists.newArrayList(34, 14);
            List<Boolean> alignRights = Lists.newArrayList(false, true);
            TableRowContext context = new TableRowContext(printRows, columnWidths, alignRights, false);
            context.addSeparator();
        }
    }

    @Override
    public String getFailedMsg() {
        return "挂账还款单打印失败，请及时处理";
    }


    /**
     * 挂账还款记录
     */
    private void addDebtRepaymentRows(List<PrintRow> printRows) {
        PrintDebtRepaymentDTO printDTO = getPrintDTO();
        // 构建挂账还款记录table
        TableRowContext table = buildDebtRepaymentRecordTable(printRows);
        // 明细记录
        List<PrintDebtRepaymentDTO.InnerRepaymentRecord> recordList = printDTO.getRecordList();
        for (PrintDebtRepaymentDTO.InnerRepaymentRecord repaymentRecord : recordList) {
            // 挂账时间
            String debtTime = DateTimeUtils.localDateTime2String(repaymentRecord.getGmtCreate(), "yyyy-MM-dd HH:mm");
            table.addRow(Lists.newArrayList(
                    new Text(LocaleUtil.getMessage("debt_time") + SPLIT + debtTime, Font.SMALL),
                    new Text(BigDecimalUtils.moneyTrimmedString(repaymentRecord.getRepaymentFee()))));
            String diningTableName = repaymentRecord.getDiningTableName();
            diningTableName = Optional.ofNullable(diningTableName).orElse("-");
            table.addRow(Lists.newArrayList(
                    new Text(LocaleUtil.getMessage("debt_table_name") + SPLIT + diningTableName, Font.SMALL),
                    Text.EMPTY));
            PrintRowUtils.add(printRows, new BlankRow());
        }
        // 还款合计
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(LocaleUtil.getMessage("debt_repayment_total_fee"), Font.NORMAL_BOLD)
                .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getRepaymentTotalFee()), Font.NORMAL_BOLD));
        // 空白行
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(Strings.EMPTY, Font.SMALL)
                .setValueString(Strings.EMPTY, Font.SMALL));
        // 结账方式
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(LocaleUtil.getMessage("debt_repayment_type"), Font.NORMAL_BOLD)
                .setValueString(printDTO.getPaymentTypeName(), Font.NORMAL_BOLD));
        table.addSeparator();
    }

    /**
     * 构建挂账还款记录table
     */
    private TableRowContext buildDebtRepaymentRecordTable(List<PrintRow> printRows) {
        int pageSize = getPageSize();
        List<Integer> columnWidths = 58 == pageSize ? Lists.newArrayList(20, 12) : Lists.newArrayList(34, 14);
        List<Boolean> alignRights = Lists.newArrayList(false, true);
        TableRowContext context = new TableRowContext(printRows, columnWidths, alignRights, false);
        context.addSeparator(null);
        return context;
    }


    /**
     * 还款时间
     * 操作员
     */
    private void appendRepaymentTimeAndStaff(List<PrintRow> printRows) {
        int pageSize = getPageSize();
        PrintDebtRepaymentDTO printDTO = getPrintDTO();
        String repaymentTime = DateTimeUtils.localDateTime2String(printDTO.getGmtModified(), "yyyy-MM-dd HH:mm");
        if (58 == pageSize) {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(LocaleUtil.getMessage("debt_repayment_time") + SPLIT, Font.SMALL)
                    .setValueString(repaymentTime, Font.SMALL));
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(LocaleUtil.getMessage("operator_user"), Font.SMALL)
                    .setValueString(printDTO.getOperatorStaffName(), Font.SMALL));
        } else {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(LocaleUtil.getMessage("debt_repayment_time") + SPLIT + repaymentTime, Font.SMALL)
                    .setValueString(LocaleUtil.getMessage("operator_user") + printDTO.getOperatorStaffName(), Font.SMALL));
        }
    }

}
