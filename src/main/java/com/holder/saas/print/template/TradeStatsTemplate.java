package com.holder.saas.print.template;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.composite.PrintLayoutUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.print.content.PrintTradeStatsDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import com.holderzone.saas.store.util.EncryptionSymbolUtil;
import org.apache.logging.log4j.util.Strings;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TradeStatsTemplate
 * @date 2018/02/14 09:00
 * @description 用餐类型统计模板
 * @program holder-saas-store-print
 */
public class TradeStatsTemplate extends AbsPrintTemplate<PrintTradeStatsDTO, FormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintTradeStatsDTO printDTO = getPrintDTO();
        List<PrintRow> printRows = new ArrayList<>();

        // 门店名
        PrintRowUtils.add(printRows, new Section()
                .addText(printDTO.getStoreName(), Font.NORMAL_BOLD)
                .setAlign(Text.Align.Center));

        // 票据类型
        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get("dining_type_statistics_invoice_header"), Font.NORMAL)
                .setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("start_time"))
                .setValueString(DateTimeUtils.mills2String(printDTO.getStartTime(), "yyyy-MM-dd HH:mm:ss")));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("end_time"))
                .setValueString(DateTimeUtils.mills2String(printDTO.getEndTime(), "yyyy-MM-dd HH:mm:ss")));

        PrintRowUtils.add(printRows, new Separator());

        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.ORDER_COUNT))
                .setValueString(getOrderQuantity()));

        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.CONSUMERS))
                .setValueString(getCustomerNumber()));

        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.NET_SALES))
                .setValueString(getSalesTotal()));

        PrintRowUtils.add(printRows, new Separator());

        if (printDTO.getDineMode() == null
                && printDTO.getSnackMode() == null
                && printDTO.getTakeoutMode() == null) {
            throw new IllegalArgumentException("正餐、快餐、外卖统计不能全为空");
        }
        // 正餐
        dineModeHandler(printRows);

        // 快餐
        snackModeHandler(printRows);

        // 外卖
        takeoutModeHandler(printRows);

        PrintLayoutUtils.addOpStaffAndPrintTime(getPageSize(), printDTO.getOperatorStaffName(), printDTO.getCreateTime(), printRows);
        return printRows;
    }


    /**
     * 正餐
     */
    private void dineModeHandler(List<PrintRow> printRows) {
        PrintTradeStatsDTO printDTO = getPrintDTO();
        PrintTradeStatsDTO.DineSnackTradeStats dineMode = printDTO.getDineMode();
        if (dineMode == null) {
            return;
        }
        PrintRowUtils.add(printRows, new Section()
                .addText(EncryptionSymbolUtil.isEncryption(dineMode.getTypeName()) ? dineMode.getTypeName() : MultiLangUtils.get("dine_in_statistics"))
                .setAlign(Text.Align.Left));
        PrintRowUtils.add(printRows, new Separator());
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.ORDER_COUNT))
                .setValueString(getOrderQuantity(dineMode.getOrderQuantity(), dineMode.getOrderQuantityStr())));
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.CONSUMERS))
                .setValueString(getCustomerNumber(dineMode.getCustomerNumber(), dineMode.getCustomerNumberStr())));
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.NET_SALES))
                .setValueString(getSalesTotal(dineMode.getSalesIncome(), dineMode.getSalesIncomeStr())));
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.AVERAGE_ORDER_AMOUNT))
                .setValueString(getOrderAvgConsume(dineMode.getOrderAvgConsume(), dineMode.getOrderAvgConsumeStr())));
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("average_per_capita_spending"))
                .setValueString(getCustomerAvgConsume(dineMode.getCustomerAvgConsume(), dineMode.getCustomerAvgConsumeStr())));
        PrintRowUtils.add(printRows, new Separator());
    }

    /**
     * 快餐
     */
    private void snackModeHandler(List<PrintRow> printRows) {
        PrintTradeStatsDTO printDTO = getPrintDTO();
        PrintTradeStatsDTO.DineSnackTradeStats snackMode = printDTO.getSnackMode();
        if (snackMode == null) {
            return;
        }
        PrintRowUtils.add(printRows, new Section()
                .addText(EncryptionSymbolUtil.isEncryption(snackMode.getTypeName()) ? snackMode.getTypeName() : MultiLangUtils.get("snack_statistics"))
                .setAlign(Text.Align.Left));
        PrintRowUtils.add(printRows, new Separator());
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.ORDER_COUNT))
                .setValueString(getOrderQuantity(snackMode.getOrderQuantity(), snackMode.getOrderQuantityStr())));
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.CONSUMERS))
                .setValueString(getCustomerNumber(snackMode.getCustomerNumber(), snackMode.getCustomerNumberStr())));
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.NET_SALES))
                .setValueString(getSalesTotal(snackMode.getSalesIncome(), snackMode.getSalesIncomeStr())));
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.AVERAGE_ORDER_AMOUNT))
                .setValueString(getOrderAvgConsume(snackMode.getOrderAvgConsume(), snackMode.getOrderAvgConsumeStr())));
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("average_per_capita_spending"))
                .setValueString(getCustomerAvgConsume(snackMode.getCustomerAvgConsume(), snackMode.getCustomerAvgConsumeStr())));
        PrintRowUtils.add(printRows, new Separator());
    }

    /**
     * 外卖
     */
    private void takeoutModeHandler(List<PrintRow> printRows) {
        PrintTradeStatsDTO printDTO = getPrintDTO();
        List<PrintTradeStatsDTO.TakeoutTradeStats> takeoutMode = printDTO.getTakeoutMode();
        if (takeoutMode == null) {
            return;
        }
        String takeoutTypeName = printDTO.getTakeoutTypeName();
        PrintRowUtils.add(printRows, new Section()
                .addText(EncryptionSymbolUtil.isEncryption(takeoutTypeName) ? takeoutTypeName : MultiLangUtils.get("takeaway_statistics"))
                .setAlign(Text.Align.Left));
        PrintRowUtils.add(printRows, new Separator());
        // 订单数
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.ORDER_COUNT))
                .setValueString(getTakeoutOrderQuantity()));
        String prefix = "--";
        boolean orderQuantityEncryption = EncryptionSymbolUtil.isEncryption(printDTO.getOrderQuantityStr());
        if (!orderQuantityEncryption) {
            takeoutMode.forEach(tradeStats -> PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(prefix + tradeStats.getPlatformName())
                    .setValueString(tradeStats.getOrderQuantity() + "/" + MultiLangUtils.get(Constant.TRANSACTIONS))));
        }
        // 销售净额
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.NET_SALES))
                .setValueString(getTakeoutSalesTotal()));
        boolean salesIncomeEncryption = EncryptionSymbolUtil.isEncryption(printDTO.getSalesIncomeStr());
        if (!salesIncomeEncryption) {
            takeoutMode.forEach(tradeStats -> PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(prefix + tradeStats.getPlatformName())
                    .setValueString(BigDecimalUtils.moneyTrimmedString(tradeStats.getSalesIncome()))));
        }
        // 单均消费
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.AVERAGE_ORDER_AMOUNT))
                .setValueString(getTakeoutOrderAvgConsume()));
        boolean orderAvgConsumeEncryption = EncryptionSymbolUtil.isEncryption(printDTO.getOrderAvgConsumeStr());
        if (!orderAvgConsumeEncryption) {
            takeoutMode.forEach(tradeStats -> PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(prefix + tradeStats.getPlatformName())
                    .setValueString(BigDecimalUtils.moneyTrimmedString(tradeStats.getOrderAvgConsume()))));
        }
        PrintRowUtils.add(printRows, new Separator());
    }


    /**
     * 获取订单数
     */
    private String getOrderQuantity() {
        PrintTradeStatsDTO printDTO = getPrintDTO();
        return EncryptionSymbolUtil.isEncryption(printDTO.getOrderQuantityStr()) ?
                printDTO.getOrderQuantityStr() :
                printDTO.getOrderQuantity() + "/" + MultiLangUtils.get(Constant.TRANSACTIONS);
    }

    /**
     * 获取消费人数
     */
    private String getCustomerNumber() {
        PrintTradeStatsDTO printDTO = getPrintDTO();
        return EncryptionSymbolUtil.isEncryption(printDTO.getCustomerNumberStr()) ?
                printDTO.getCustomerNumberStr() :
                printDTO.getCustomerNumber() + "/" + MultiLangUtils.get(Constant.PERSON);
    }

    /**
     * 获取销售净额
     */
    private String getSalesTotal() {
        PrintTradeStatsDTO printDTO = getPrintDTO();
        return EncryptionSymbolUtil.isEncryption(printDTO.getSalesIncomeStr()) ?
                printDTO.getSalesIncomeStr() :
                BigDecimalUtils.moneyTrimmedString(printDTO.getSalesIncome());
    }


    /**
     * 获取正餐/快餐订单数
     */
    private String getOrderQuantity(Long orderQuantity, String orderQuantityStr) {
        return EncryptionSymbolUtil.isEncryption(orderQuantityStr) ?
                orderQuantityStr :
                orderQuantity + "/" + MultiLangUtils.get(Constant.TRANSACTIONS);
    }

    /**
     * 获取正餐/快餐消费人数
     */
    private String getCustomerNumber(Long customerNumber, String customerNumberStr) {
        return EncryptionSymbolUtil.isEncryption(customerNumberStr) ?
                customerNumberStr :
                customerNumber + "/" + MultiLangUtils.get(Constant.PERSON);
    }


    /**
     * 获取正餐/快餐消费净额
     */
    private String getSalesTotal(BigDecimal salesIncome, String salesIncomeStr) {
        return EncryptionSymbolUtil.isEncryption(salesIncomeStr) ?
                salesIncomeStr :
                BigDecimalUtils.moneyTrimmedString(salesIncome);
    }


    /**
     * 获取正餐/快餐单均消费
     */
    private String getOrderAvgConsume(BigDecimal orderAvgConsume, String orderAvgConsumeStr) {
        return EncryptionSymbolUtil.isEncryption(orderAvgConsumeStr) ?
                orderAvgConsumeStr :
                BigDecimalUtils.moneyTrimmedString(orderAvgConsume);
    }

    /**
     * 获取正餐/快餐人均消费
     */
    private String getCustomerAvgConsume(BigDecimal customerAvgConsume, String customerAvgConsumeStr) {
        return EncryptionSymbolUtil.isEncryption(customerAvgConsumeStr) ?
                customerAvgConsumeStr :
                BigDecimalUtils.moneyTrimmedString(customerAvgConsume);
    }


    /**
     * 获取外卖订单数
     */
    private String getTakeoutOrderQuantity() {
        PrintTradeStatsDTO printDTO = getPrintDTO();
        List<PrintTradeStatsDTO.TakeoutTradeStats> takeoutMode = printDTO.getTakeoutMode();
        if (takeoutMode == null) {
            return Strings.EMPTY;
        }
        return EncryptionSymbolUtil.isEncryption(printDTO.getOrderQuantityStr()) ?
                printDTO.getOrderQuantityStr() :
                takeoutMode.stream()
                        .mapToLong(PrintTradeStatsDTO.TradeStats::getOrderQuantity).sum() + "/" + MultiLangUtils.get(Constant.TRANSACTIONS);
    }


    /**
     * 获取外卖销售净额
     */
    private String getTakeoutSalesTotal() {
        PrintTradeStatsDTO printDTO = getPrintDTO();
        List<PrintTradeStatsDTO.TakeoutTradeStats> takeoutMode = printDTO.getTakeoutMode();
        if (takeoutMode == null) {
            return Strings.EMPTY;
        }
        return EncryptionSymbolUtil.isEncryption(printDTO.getSalesIncomeStr()) ?
                printDTO.getSalesIncomeStr() :
                BigDecimalUtils.moneyTrimmedString(takeoutMode.stream()
                        .map(PrintTradeStatsDTO.TradeStats::getSalesIncome)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
    }

    /**
     * 获取外卖单均消费
     */
    private String getTakeoutOrderAvgConsume() {
        PrintTradeStatsDTO printDTO = getPrintDTO();
        List<PrintTradeStatsDTO.TakeoutTradeStats> takeoutMode = printDTO.getTakeoutMode();
        if (takeoutMode == null) {
            return Strings.EMPTY;
        }
        return EncryptionSymbolUtil.isEncryption(printDTO.getOrderAvgConsumeStr()) ?
                printDTO.getOrderAvgConsumeStr() :
                BigDecimalUtils.moneyTrimmedString(takeoutMode.stream()
                        .map(PrintTradeStatsDTO.TradeStats::getOrderAvgConsume)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
    }

    @Override
    public String getFailedMsg() {
        return "用餐类型统计单打印失败，请及时处理";
    }
}
