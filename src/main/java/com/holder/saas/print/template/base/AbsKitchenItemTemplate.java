package com.holder.saas.print.template.base;

import com.holder.saas.print.entity.biz.ItemTableFormatBO;
import com.holder.saas.print.utils.template.row.composite.table.PrintTableUtils;
import com.holderzone.saas.store.dto.print.content.PrintBaseItemDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.format.OrderItemFormatDTO;
import com.holderzone.saas.store.dto.print.format.RefundItemFormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;

import java.util.List;

public abstract class AbsKitchenItemTemplate<T extends PrintBaseItemDTO, F extends FormatDTO> extends AbsCustomTemplate<T, F> {

    @Override
    public List<PrintRow> getContent() {
        F formatDTO = getFormatDTO();
        ItemTableFormatBO itemTableFormatBo;
        if (formatDTO instanceof OrderItemFormatDTO) {
            itemTableFormatBo = new ItemTableFormatBO()
                    .setItemName(((OrderItemFormatDTO) formatDTO).getItemName())
                    .setItemNumber(((OrderItemFormatDTO) formatDTO).getItemNumber())
                    .setItemProperty(((OrderItemFormatDTO) formatDTO).getItemProperty())
                    .setShowMakeNum(((OrderItemFormatDTO) formatDTO).getShowMakeNum())
                    .setItemRemark(((OrderItemFormatDTO) formatDTO).getItemRemark());
        } else if (formatDTO instanceof RefundItemFormatDTO) {
            itemTableFormatBo = new ItemTableFormatBO()
                    .setItemName(((RefundItemFormatDTO) formatDTO).getItemName())
                    .setItemNumber(((RefundItemFormatDTO) formatDTO).getItemNumber())
                    .setItemProperty(((RefundItemFormatDTO) formatDTO).getItemProperty())
                    .setItemRemark(((RefundItemFormatDTO) formatDTO).getItemRemark());
        } else {
            itemTableFormatBo = null;
        }
        return PrintTableUtils.resolveKitchenItemRow(getPrintDTO(), isCancel(), itemTableFormatBo);
    }

    /**
     * 是否是退菜单
     *
     * @return 是否是退菜单
     */
    protected abstract boolean isCancel();
}
