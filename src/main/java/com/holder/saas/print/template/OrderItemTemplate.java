package com.holder.saas.print.template;

import cn.hutool.json.JSONUtil;
import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.template.base.AbsKitchenItemTemplate;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.SensitiveUtils;
import com.holder.saas.print.utils.template.TradeModeUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holder.saas.print.utils.template.row.composite.PrintFormatLayoutContainer;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.print.content.PrintOrderItemDTO;
import com.holderzone.saas.store.dto.print.format.OrderItemFormatDTO;
import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.enums.print.TradeModeEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderItemTemplate
 * @date 2018/02/14 09:00
 * @description 点菜单模板
 * @program holder-saas-store-print
 */
@Slf4j
public class OrderItemTemplate extends AbsKitchenItemTemplate<PrintOrderItemDTO, OrderItemFormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintOrderItemDTO printDTO = getPrintDTO();
        OrderItemFormatDTO formatDTO = getFormatDTO();
        PrintFormatLayoutContainer container = new PrintFormatLayoutContainer();

        // 票据类型 点菜单 叫起单 催菜单 挂起单
        container.addInvoiceType(MultiLangUtils.getByOrderItemType(printDTO.getItemInvoiceType()), formatDTO.getInvoiceName());

        // 单据类型
        if (formatDTO.getOrderTypeName().isEnable()) {
            String orderTypeName = printDTO.getOrderTypeName();
            if (printDTO.getTakeoutBusinessType() == 1) {
                orderTypeName += MultiLangUtils.get("assembled_meals");
            }
            container.addSection(new Section()
                    .addText("【" + orderTypeName + "】", formatDTO.getOrderTypeName().toFont())
                    .setAlign(Text.Align.Center));
        }

        // 如果是外卖，不打印人数
        if (!StringUtils.isEmpty(printDTO.getMarkName())) {
            formatDTO.getPersonNumber().setEnable(false);
        }
        // 牌号、人数
        addMarkOrTableNoAndOrderAndGuest(container, printDTO, formatDTO);

        // 外卖送餐时间
        if (!StringUtils.isEmpty(printDTO.getEstimateDeliveredTimeString()) && formatDTO.getDinnerTime().isEnable()) {
            PrintRowUtils.addLeft(container.getPrintRows(), "meal_time", printDTO.getEstimateDeliveredTimeString(),
                    formatDTO.getDinnerTime());
        }

        String remarkMessage = printDTO.getRemark();
        log.info("订单备注，敏感词过滤替换之前。备注信息{}", remarkMessage);
        // 整单备注
        try {
            //订单备注，敏感词过滤替换
            remarkMessage = SensitiveUtils.replaceWord(printDTO.getRemark());
        } catch (Exception e) {
            log.error("订单备注，敏感词过滤替换报错：{}", e.getMessage());
        }
        log.info("订单备注，敏感词过滤替换之后。备注信息：{}", remarkMessage);
        container.addOrderRemarkNoSeparator(remarkMessage, formatDTO.getRemark());

        // 商品
        container.addAll(super.getContent());

        // 订单号
        if (formatDTO.getOrderNo().isEnable()) {
            PrintRowUtils.add(container.getPrintRows(), Constant.ORDER_NUMBER, printDTO.getOrderNo(), formatDTO.getOrderNo());
            container.addSeparator();
        }

        // 出餐码 CC+订单号
        container.appendFoodFinishBarCode(printDTO.getOrderNo(), printDTO.getFoodFinishBarCode(), formatDTO.getFoodFinishBarCode());

        // 操作员、下单、打印
        container.addOpStaffAndOrderTimeAndPrintTime(getPageSize(),
                printDTO.getOperatorStaffName(), formatDTO.getOperator(),
                printDTO.getOrderTime(), formatDTO.getOrderTime(),
                printDTO.getCreateTime(), formatDTO.getPrintTime());


        // 美团外卖小票提示
        container.addOrderPrompt(formatDTO.getOrderPrompt());

        return container.getPrintRows();
    }

    @Override
    protected boolean isCancel() {
        return false;
    }

    @Override
    public String getFailedMsg() {
        String invoiceName = PrintOrderItemDTO.ItemInvoiceTypeEnum.getNameByType(getPrintDTO().getItemInvoiceType());
        return invoiceName + " [" + getPrintDTO().getMarkNo() + "]号订单打印失败，请及时处理";
    }

    /**
     * 新增桌号和人数
     *
     * @param container 容器信息
     * @param printDTO  打印信息
     * @param formatDTO 格式信息
     */
    public void addMarkOrTableNoAndOrderAndGuest(PrintFormatLayoutContainer container, PrintOrderItemDTO printDTO,
                                                 OrderItemFormatDTO formatDTO) {
        FormatMetadata markOrTableNoFormat = formatDTO.getMarkNo();
        FormatMetadata personNumberFormat = formatDTO.getPersonNumber();
        if (!markOrTableNoFormat.isEnable() && !personNumberFormat.isEnable()) {
            return;
        }
        Integer personNumber = printDTO.getPersonNumber();
        String markOrTableNo = printDTO.getMarkNo();

        // 启用排号，添加排号section
        if (markOrTableNoFormat.isEnable()) {
            String markName;
            // 外卖单
            if (Objects.equals(TradeModeEnum.ofMode(printDTO.getTradeMode()).getMode(), TradeModeEnum.TAKEOUT.getMode())) {
                markName = MultiLangUtils.get("takeout_name");
            } else {
                markName = TradeModeUtils.getMarkName(printDTO.getTradeMode());
            }
            // 存在人数，桌号在左，人数在右
            if (personNumberFormat.isEnable()) {
                PrintRowUtils.add(container.getPrintRows(), new KeyValue()
                        .setKeyString(markName + markOrTableNo, formatDTO.getMarkNo().toFont())
                        .setValueString(getPersonNum(personNumber), personNumberFormat.toFont()));
            } else {
                PrintRowUtils.add(container.getPrintRows(), new Section(markName + markOrTableNo,
                        formatDTO.getMarkNo().toFont()));
            }
        } else {
            if (80 == getPageSize()) {
                PrintRowUtils.add(container.getPrintRows(), new Section(
                        getPersonNum(personNumber), personNumberFormat.toFont()).setAlign(Text.Align.Right));
            } else {
                PrintRowUtils.add(container.getPrintRows(), new Section(
                        getPersonNum(personNumber), personNumberFormat.toFont()));
            }
        }
    }

    /**
     * 获取人数信息
     *
     * @param personNumber 人数
     * @return 人数信息
     */
    private String getPersonNum(Integer personNumber) {
        return MultiLangUtils.get(Constant.CUSTOMER_NUMBER) + personNumber;
    }
}
