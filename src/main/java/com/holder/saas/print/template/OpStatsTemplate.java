package com.holder.saas.print.template;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holder.saas.print.utils.template.row.composite.PrintLayoutUtils;
import com.holder.saas.print.utils.template.row.composite.table.PrintTableUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.dto.print.content.PrintOpStatsDTO;
import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import com.holderzone.saas.store.dto.print.content.nested.ReduceRecord;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.BlankRow;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import com.holderzone.saas.store.dto.print.template.printable.TableRow;
import com.holderzone.saas.store.util.EncryptionSymbolUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OpStatsTemplate
 * @date 2018/02/14 09:00
 * @description 营业概况模板
 * @program holder-saas-store-print
 */
public class OpStatsTemplate extends AbsPrintTemplate<PrintOpStatsDTO, FormatDTO> {

    private static final String INDENT = " ";

    private static final String PREFIX = "--";

    @Override
    public List<PrintRow> getContent() {
        PrintOpStatsDTO printDTO = getPrintDTO();
        List<PrintRow> printRows = new ArrayList<>();

        // 营业概况标题
        handleTitleInfo(printRows, printDTO);

        PrintRowUtils.add(printRows, new Separator());

        // 正餐客流量，上座率，开台率，翻台率，平均用餐时长
        handleStatisticDataPrintInfo(printRows, printDTO);

        PrintRowUtils.add(printRows, new Separator());

        // 营业统计
        handleSale(printRows);

        PrintRowUtils.add(printRows, new Separator());

        //  销售净额构成
        handleNetSale(printRows);

        PrintRowUtils.add(printRows, new Separator());

        //  预计应得金额构成
        handleEstimated(printRows);

        PrintRowUtils.add(printRows, new Separator());

        // 优惠总额构成
        handleTotalDiscounts(printRows);

        PrintRowUtils.add(printRows, new Separator());

        // 团购券统计
        handleGroupon(printRows);

        PrintLayoutUtils.addOpStaffAndPrintTime(getPageSize(), printDTO.getOperatorStaffName(), printDTO.getCreateTime(), printRows);

        return printRows;
    }

    private void handleEstimated(List<PrintRow> printRows) {
        PrintOpStatsDTO printDTO = getPrintDTO();
        // 【预计应得金额构成】
        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get(Constant.BUSINESS_OVERVIEW_ESTIMATED), Font.NORMAL)
                .setAlign(Text.Align.Center));

        // 未包含“会员充值”金额
        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get(Constant.NOT_CONTAINS_MEMBER_RECHARGE))
                .setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new BlankRow());

        // 预计应得金额
        TableRow estimatedTablerow = getTableRow();
        estimatedTablerow.setColumns(Arrays.asList(
                new Text().setText(MultiLangUtils.get(Constant.ESTIMATED_AMOUNT)).setFont(Font.SMALL_NORMAL_THIN_BOLD),
                new Text().setText(Constant.TINY_BLANK).setFont(Font.SMALL_NORMAL_THIN_BOLD),
                new Text().setText(getEstimatedAmount()).setFont(Font.SMALL_NORMAL_THIN_BOLD)
        ));
        PrintRowUtils.add(printRows, estimatedTablerow);
        // 预计应得金额构成
        boolean isEstimatedSaleEncryption = EncryptionSymbolUtil.isEncryption(printDTO.getEstimatedAmountStr());
        if (isEstimatedSaleEncryption || CollectionUtils.isEmpty(printDTO.getSalesDetail())) {
            return;
        }
        for (PayRecord payRecord : printDTO.getSalesDetail()) {
            TableRow tableRow = getTableRow();
            tableRow.setColumns(Arrays.asList(
                    new Text().setText(payRecord.getPayName()),
                    new Text().setText(getPayRecordOrderQuantity(payRecord)),
                    new Text().setText(BigDecimalUtils.moneyTrimmedString(payRecord.getEstimatedAmount()))
            ));
            PrintRowUtils.add(printRows, tableRow);
        }
    }

    private void handleGroupon(List<PrintRow> printRows) {
        PrintOpStatsDTO printDTO = getPrintDTO();
        if (CollectionUtils.isEmpty(printDTO.getSalesDetail())
            || printDTO.getSalesDetail().stream()
                .noneMatch(payRecord ->
                        Objects.nonNull(payRecord) && Objects.nonNull(payRecord.getIsGroupon()) && payRecord.getIsGroupon())) {
            return;
        }

        // 【团购券统计】
        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get(Constant.BUSINESS_OVERVIEW_GROUPON), Font.NORMAL)
                .setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new BlankRow());

        // 团购券统计
        TableRow grouponTableHeaderRow = getGrouponTableRow();
        grouponTableHeaderRow.setColumns(Arrays.asList(new Text(MultiLangUtils.get(Constant.GROUPON_NAME)),
                new Text(MultiLangUtils.get(Constant.GROUPON_COUNT)),
                new Text(MultiLangUtils.get(Constant.GROUPON_DISCOUNT)),
                new Text(MultiLangUtils.get(Constant.GROUPON_NET_SALES))));
        PrintRowUtils.add(printRows, grouponTableHeaderRow);

        //  团购券统计
        boolean isSaleIncomeEncryption = EncryptionSymbolUtil.isEncryption(printDTO.getSalesIncomeStr());
        if (!isSaleIncomeEncryption && !CollectionUtils.isEmpty(printDTO.getSalesDetail())) {
            for (PayRecord payRecord : printDTO.getSalesDetail()) {
                if (Objects.nonNull(payRecord.getIsGroupon()) && payRecord.getIsGroupon()) {
                    TableRow grouponTableRow = getGrouponTableRow();
                    grouponTableRow.setColumns(Arrays.asList(
                            new Text(payRecord.getPayName()),
                            new Text(getGrouponQuantity(payRecord)),
                            new Text(BigDecimalUtils.moneyTrimmedString(payRecord.getDiscountAmount())),
                            new Text(BigDecimalUtils.moneyTrimmedString(payRecord.getAmount()))));
                    PrintRowUtils.add(printRows, grouponTableRow);
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(payRecord.getInnerDetails())) {
                        handleGrouponInnerDetails(printRows, payRecord.getInnerDetails());
                    }
                }
            }
        }

        PrintRowUtils.add(printRows, new Separator());
    }

    private void handleGrouponInnerDetails(List<PrintRow> printRows, List<AmountItemDTO.InnerDetails> innerDetails) {
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(innerDetails)) {
            for (AmountItemDTO.InnerDetails innerDetail : innerDetails) {
                TableRow grouponTableRow = getGrouponTableRow();
                grouponTableRow.setColumns(Arrays.asList(
                        new Text(Constant.TINY_BLANK + innerDetail.getName()),
                        new Text(getInnerDetailGrouponQuantity(innerDetail)),
                        new Text(BigDecimalUtils.moneyTrimmedString(innerDetail.getDiscountAmount())),
                        new Text(BigDecimalUtils.moneyTrimmedString(innerDetail.getAmount()))));
                PrintRowUtils.add(printRows, grouponTableRow);
            }
        }
    }

    private String getInnerDetailGrouponQuantity(AmountItemDTO.InnerDetails innerDetail) {
        Integer grouponCount = 0;
        if (Objects.nonNull(innerDetail.getIsGroupon()) && innerDetail.getIsGroupon()) {
            grouponCount = innerDetail.getGrouponCount();
        }
        return grouponCount + MultiLangUtils.get(Constant.COUPONS);
    }

    private void handleTotalDiscounts(List<PrintRow> printRows) {
        // 【优惠总额构成】
        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get(Constant.BUSINESS_OVERVIEW_DISCOUNT), Font.NORMAL)
                .setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new BlankRow());

        // 优惠总额
        TableRow totalDiscountTableRow = getTableRow();
        PrintRowUtils.add(printRows, totalDiscountTableRow);

        // 优惠总额构成
        PrintOpStatsDTO printDTO = getPrintDTO();
        boolean isReduceTotalEncryption = EncryptionSymbolUtil.isEncryption(printDTO.getReduceTotalStr());
        String totalDiscountStr;
        if (!isReduceTotalEncryption) {
            Long totalNetSalesOrderCount = 0L;
            if (!CollectionUtils.isEmpty(printDTO.getReduceDetail())) {
                for (ReduceRecord reduceRecord : printDTO.getReduceDetail()) {
                    totalNetSalesOrderCount += reduceRecord.getOrderCount();
                    TableRow tableRow = getTableRow();
                    tableRow.setColumns(Arrays.asList(
                            new Text().setText(reduceRecord.getName()),
                            new Text().setText(getDiscountOrderQuantity(reduceRecord)),
                            new Text().setText(BigDecimalUtils.moneyTrimmedString(reduceRecord.getAmount()))
                    ));
                    PrintRowUtils.add(printRows, tableRow);
                }
            }
            totalDiscountStr = totalNetSalesOrderCount + "/" + MultiLangUtils.get(Constant.TRANSACTIONS);
        } else {
            totalDiscountStr = com.holderzone.saas.store.constant.Constant.ENCRYPTION_SYMBOL;
        }

        totalDiscountTableRow.setColumns(Arrays.asList(
                new Text().setText(MultiLangUtils.get(Constant.TOTAL_DISCOUNTS)),
                new Text().setText(totalDiscountStr),
                new Text().setText(getReduceTotal())
        ));
    }

    private void handleNetSale(List<PrintRow> printRows) {
        // 【销售总净额构成】
        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get(Constant.BUSINESS_OVERVIEW_NET_SALE), Font.NORMAL)
                .setAlign(Text.Align.Center));

        // 未包含“会员充值”金额
        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get(Constant.NOT_CONTAINS_MEMBER_RECHARGE))
                .setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new BlankRow());

        // 销售总净额
        TableRow netSaleTableRow = getTableRow();
        PrintRowUtils.add(printRows, netSaleTableRow);

        // 销售总净额构成
        PrintOpStatsDTO printDTO = getPrintDTO();
        boolean isSaleIncomeEncryption = EncryptionSymbolUtil.isEncryption(printDTO.getSalesIncomeStr());
        String totalNetSalesOrderCountStr;
        if (!isSaleIncomeEncryption) {
            Long totalNetSalesOrderCount = 0L;
            if (!CollectionUtils.isEmpty(printDTO.getSalesDetail())) {
                for (PayRecord payRecord : printDTO.getSalesDetail()) {
                    totalNetSalesOrderCount += payRecord.getOrderCount();
                    TableRow tableRow = getTableRow();
                    tableRow.setColumns(Arrays.asList(
                            new Text().setText(payRecord.getPayName()),
                            new Text().setText(getPayRecordOrderQuantity(payRecord)),
                            new Text().setText(BigDecimalUtils.moneyTrimmedString(payRecord.getAmount()))
                    ));
                    PrintRowUtils.add(printRows, tableRow);
                    if (Objects.nonNull(payRecord.getExcessAmount()) && payRecord.getExcessAmount().compareTo(BigDecimal.ZERO) > 0) {
                        PrintRowUtils.add(printRows, new KeyValue()
                                .setKeyString(Constant.EMPTY_STRING)
                                .setValueString(Constant.LEFT_BRACKET
                                        + MultiLangUtils.get(Constant.REMAINING_BALANCE)
                                        + BigDecimalUtils.moneyTrimmedString(payRecord.getExcessAmount())
                                        + Constant.RIGHT_BRACKET));
                    }
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(payRecord.getInnerDetails())) {
                        handleNetSaleInnerDetails(printRows, payRecord.getInnerDetails());
                    }
                }
            }
            totalNetSalesOrderCountStr = totalNetSalesOrderCount + "/" + MultiLangUtils.get(Constant.TRANSACTIONS);
        } else {
            totalNetSalesOrderCountStr = com.holderzone.saas.store.constant.Constant.ENCRYPTION_SYMBOL;
        }
        netSaleTableRow.setColumns(Arrays.asList(
                new Text().setText(MultiLangUtils.get(Constant.TOTAL_NET_SALES)).setFont(Font.SMALL_NORMAL_THIN_BOLD),
                new Text().setText(totalNetSalesOrderCountStr).setFont(Font.SMALL_NORMAL_THIN_BOLD),
                new Text().setText(getSalesIncome()).setFont(Font.SMALL_NORMAL_THIN_BOLD)
                ));
    }

    private void handleNetSaleInnerDetails(List<PrintRow> printRows, List<AmountItemDTO.InnerDetails> innerDetails) {
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(innerDetails)) {
            for (AmountItemDTO.InnerDetails innerDetail : innerDetails) {
                TableRow tableRow = getTableRow();
                tableRow.setColumns(Arrays.asList(
                        new Text().setText(Constant.TINY_BLANK + innerDetail.getName()),
                        new Text().setText(getInnerDetailOrderQuantity(innerDetail)),
                        new Text().setText(BigDecimalUtils.moneyTrimmedString(innerDetail.getAmount()))
                ));
                PrintRowUtils.add(printRows, tableRow);
            }
        }
    }

    private void handleSale(List<PrintRow> printRows) {
        // 【营业统计】
        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get(Constant.BUSINESS_OVERVIEW_SALE), Font.NORMAL)
                .setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new BlankRow());

        // 订单数
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.ORDER_COUNT))
                .setValueString(getOrderQuantity()));

        // 销售额（已结账订单销售总金额）
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.SALES_REVENUE))
                .setValueString(getSalesTotal()));
        // 优惠总额
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.TOTAL_DISCOUNTS))
                .setValueString(getReduceTotal()));
        // 退款总额
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.TOTAL_REFUND))
                .setValueString(getRefundTotal()));
        // 销售总净额（已结账订单顾客实付金额）
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.TOTAL_NET_SALES_WITH_COMMENT), Font.SMALL_NORMAL_THIN_BOLD)
                .setValueString(getSalesIncome(), Font.SMALL_NORMAL_THIN_BOLD));
        // 预计应得金额
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.ESTIMATED_AMOUNT), Font.SMALL_NORMAL_THIN_BOLD)
                .setValueString(getEstimatedAmount(), Font.SMALL_NORMAL_THIN_BOLD));
        // 其他收款
        PrintRowUtils.add(printRows, new Section().addText(MultiLangUtils.get(Constant.OTHER_RECEIPTS)));
        // 会员充值
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(Constant.TINY_BLANK + MultiLangUtils.get(Constant.MEMBER_RECHARGE))
                .setValueString(getMemberRecharge()));
        // 销售总净额+其他收款
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.NET_SALES_OTHER_RECEIPTS))
                .setValueString(getMemberRechargeAndOtherReceipts()));
    }

    private static void handleTitleInfo(List<PrintRow> printRows, PrintOpStatsDTO printDTO) {
        // 门店名
        PrintRowUtils.add(printRows, new Section()
                .addText(printDTO.getStoreName(), Font.NORMAL_BOLD)
                .setAlign(Text.Align.Center));

        // 票据类型
        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get("business_overview_invoice_header"), Font.NORMAL)
                .setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("start_time"))
                .setValueString(DateTimeUtils.mills2String(printDTO.getStartTime(), "yyyy-MM-dd HH:mm:ss")));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("end_time"))
                .setValueString(DateTimeUtils.mills2String(printDTO.getEndTime(), "yyyy-MM-dd HH:mm:ss")));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("checkout_staffs"))
                .setValueString(printDTO.getCheckoutStaffs()));
    }

    private void handleStatisticDataPrintInfo(List<PrintRow> printRows, PrintOpStatsDTO printDTO) {
        // 正餐客流量
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.CUSTOMER_TRAFFIC))
                .setValueString(getDineInCustomerNumber()));

        // 正餐上座率
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.OCCUPANCY_RATE_PERCENT))
                .setValueString(printDTO.getOccupancyRatePercent()));

        // 正餐开台率
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.OPEN_TABLE_TATE_PERCENT))
                .setValueString(printDTO.getOpenTableRatePercent()));

        // 正餐翻台率
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.FLIP_TABLE_TATE_PERCENT))
                .setValueString(printDTO.getFlipTableRatePercent()));

        // 正餐平均用餐时长（分钟）
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.AVG_DINE_IN_TIME))
                .setValueString(printDTO.getAvgDineInTimeStr()));
    }

    private void addEstimatedDetail(PrintOpStatsDTO printDTO, List<PrintRow> printRows) {
        boolean isEstimatedSaleEncryption = EncryptionSymbolUtil.isEncryption(printDTO.getEstimatedAmountStr());
        if (!isEstimatedSaleEncryption && !CollectionUtils.isEmpty(printDTO.getSalesDetail())) {
            String prefix = "--";
            for (PayRecord payRecord : printDTO.getSalesDetail()) {
                PrintRowUtils.add(printRows, new KeyValue()
                        .setKeyString(prefix + payRecord.getPayName())
                        .setValueString(BigDecimalUtils.moneyTrimmedString(payRecord.getEstimatedAmount())));
            }
        }
    }


    /**
     * 获取订单数
     */
    private String getOrderQuantity() {
        PrintOpStatsDTO printDTO = getPrintDTO();
        return EncryptionSymbolUtil.isEncryption(printDTO.getOrderQuantityStr()) ?
                printDTO.getOrderQuantityStr() :
                printDTO.getOrderQuantity() + "/" + MultiLangUtils.get(Constant.TRANSACTIONS);
    }

    /**
     * 获取客流量
     */
    private String getCustomerNumber() {
        PrintOpStatsDTO printDTO = getPrintDTO();
        return EncryptionSymbolUtil.isEncryption(printDTO.getCustomerNumberStr()) ?
                printDTO.getCustomerNumberStr() :
                printDTO.getCustomerNumber() + "/" + MultiLangUtils.get(Constant.PERSON);
    }

    /**
     * 获取销售额
     */
    private String getSalesTotal() {
        PrintOpStatsDTO printDTO = getPrintDTO();
        return EncryptionSymbolUtil.isEncryption(printDTO.getSalesTotalStr()) ?
                printDTO.getSalesTotalStr() :
                BigDecimalUtils.moneyTrimmedString(printDTO.getSalesTotal());
    }


    /**
     * 获取销售总净额
     */
    private String getSalesIncome() {
        PrintOpStatsDTO printDTO = getPrintDTO();
        return EncryptionSymbolUtil.isEncryption(printDTO.getSalesIncomeStr()) ?
                printDTO.getSalesIncomeStr() :
                BigDecimalUtils.moneyTrimmedString(printDTO.getSalesIncome());
    }

    /**
     * 获取预计应得金额
     */
    private String getEstimatedAmount() {
        PrintOpStatsDTO printDTO = getPrintDTO();
        return EncryptionSymbolUtil.isEncryption(printDTO.getEstimatedAmountStr()) ?
                printDTO.getEstimatedAmountStr() :
                BigDecimalUtils.moneyTrimmedString(printDTO.getEstimatedAmount());
    }

    /**
     * 获取优惠总额
     */
    private String getReduceTotal() {
        PrintOpStatsDTO printDTO = getPrintDTO();
        return EncryptionSymbolUtil.isEncryption(printDTO.getReduceTotalStr()) ?
                printDTO.getReduceTotalStr() :
                BigDecimalUtils.moneyTrimmedString(printDTO.getReduceTotal());
    }

    /**
     * 获取优惠总额
     */
    private String getRefundTotal() {
        PrintOpStatsDTO printDTO = getPrintDTO();
        return EncryptionSymbolUtil.isEncryption(printDTO.getRefundAmountStr()) ?
                printDTO.getRefundAmountStr() :
                BigDecimalUtils.moneyTrimmedString(printDTO.getRefundAmount());
    }

    /**
     * 获取正餐客流量
     */
    private String getDineInCustomerNumber() {
        PrintOpStatsDTO printDTO = getPrintDTO();
        return EncryptionSymbolUtil.isEncryption(printDTO.getDineInCustomerNumberStr()) ?
                printDTO.getDineInCustomerNumberStr() :
                printDTO.getDineInCustomerNumber() + "/" + MultiLangUtils.get(Constant.PERSON);
    }

    private String getMemberRecharge() {
        PrintOpStatsDTO printDTO = getPrintDTO();
        return EncryptionSymbolUtil.isEncryption(printDTO.getRechargeMoneyStr()) ?
                printDTO.getRechargeMoneyStr() :
                BigDecimalUtils.moneyTrimmedString(printDTO.getRechargeMoney());
    }

    private String getMemberRechargeAndOtherReceipts() {
        PrintOpStatsDTO printDTO = getPrintDTO();
        if (EncryptionSymbolUtil.isEncryption(printDTO.getRechargeMoneyStr())) {
            return printDTO.getRechargeMoneyStr();
        }
        if (EncryptionSymbolUtil.isEncryption(printDTO.getSalesIncomeStr())) {
            return printDTO.getSalesIncomeStr();
        }
        return BigDecimalUtils.moneyTrimmedString(printDTO.getRechargeMoney().add(printDTO.getSalesIncome()));
    }

    /**
     * 获取优惠订单数
     */
    private String getDiscountOrderQuantity(ReduceRecord reduceRecord) {
        return EncryptionSymbolUtil.isEncryption(reduceRecord.getOrderCountStr()) ?
                reduceRecord.getOrderCountStr() :
                reduceRecord.getOrderCount() + "/" + MultiLangUtils.get(Constant.TRANSACTIONS);
    }

    /**
     * 获取支付方式订单数
     */
    private String getInnerDetailOrderQuantity(AmountItemDTO.InnerDetails innerDetail) {
        if (Objects.nonNull(innerDetail.getIsGroupon()) && innerDetail.getIsGroupon()) {
            return innerDetail.getOrderCount() + MultiLangUtils.get(Constant.TRANSACTIONS)
                    + "/" + innerDetail.getGrouponCount() + MultiLangUtils.get(Constant.COUPONS);
        }
        return innerDetail.getOrderCount() + "/" + MultiLangUtils.get(Constant.TRANSACTIONS);
    }

    /**
     * 获取支付方式订单数
     */
    private String getGrouponQuantity(PayRecord payRecord) {
        Long grouponCount = 0L;
        if (Objects.nonNull(payRecord.getIsGroupon()) && payRecord.getIsGroupon()) {
            grouponCount = payRecord.getGrouponCount();
        }
        return grouponCount + MultiLangUtils.get(Constant.COUPONS);
    }

    /**
     * 获取支付方式订单数
     */
    private String getPayRecordOrderQuantity(PayRecord payRecord) {
        if (Objects.nonNull(payRecord.getIsGroupon()) && payRecord.getIsGroupon()) {
            return EncryptionSymbolUtil.isEncryption(payRecord.getOrderCountStr()) ?
                    payRecord.getOrderCountStr() :
                    payRecord.getOrderCount() + MultiLangUtils.get(Constant.TRANSACTIONS)
                    + "/" + payRecord.getGrouponCount() + MultiLangUtils.get(Constant.COUPONS);
        }
        return EncryptionSymbolUtil.isEncryption(payRecord.getOrderCountStr()) ?
                payRecord.getOrderCountStr() :
                payRecord.getOrderCount() + "/" + MultiLangUtils.get(Constant.TRANSACTIONS);
    }

    private TableRow getTableRow() {
        TableRow newSaleTableRow = new TableRow();
        newSaleTableRow.setAlignRights(Arrays.asList(false, false, false));
        newSaleTableRow.setColumnWidths(PrintTableUtils.getOverViewNetSaleColWidthList(getPageSize()));
        return newSaleTableRow;
    }

    private TableRow getGrouponTableRow() {
        TableRow newGrouponTableRow = new TableRow();
        newGrouponTableRow.setAlignRights(Arrays.asList(false, false, false, false));
        newGrouponTableRow.setColumnWidths(PrintTableUtils.getOverViewGrouponColWidthList(getPageSize()));
        return newGrouponTableRow;
    }

    @Override
    public String getFailedMsg() {
        return "营业概况单打印失败，请及时处理";
    }
}
