package com.holder.saas.print.template.base;

import com.holder.saas.print.utils.template.row.composite.PrintFormatLayoutContainer;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.format.metadata.CustomMetadata;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

public abstract class AbsCustomTemplate<T extends PrintDTO, F extends FormatDTO> extends AbsPrintTemplate<T, F> {

    @Override
    public List<PrintRow> getHeader() {
        F formatDTO = getFormatDTO();
        if (formatDTO == null) {
            return Collections.emptyList();
        }
        return getMetadata(formatDTO.getHeaders());
    }

    @Override
    public List<PrintRow> getFooter() {
        F formatDTO = getFormatDTO();
        if (formatDTO == null) {
            return Collections.emptyList();
        }
        return getMetadata(formatDTO.getFooters());
    }

    private List<PrintRow> getMetadata(List<CustomMetadata> metadata) {
        F formatDTO = getFormatDTO();
        if (formatDTO == null) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(metadata)) {
            return Collections.emptyList();
        }
        // 将模板页眉、页脚列表加入到打印元素的容器中
        PrintFormatLayoutContainer container = new PrintFormatLayoutContainer();
        container.addBlankRow();
        metadata.forEach(container::addFooter);
        container.addBlankRow();
        return container.getPrintRows();
    }
}
