package com.holder.saas.print.template;

import com.holder.saas.print.template.base.AbsCustomTemplate;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.composite.PrintFormatLayoutContainer;
import com.holderzone.saas.store.dto.print.content.PrintTurnTableDTO;
import com.holderzone.saas.store.dto.print.format.TurnTableFormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TurnTableTemplate
 * @date 2018/02/14 09:00
 * @description 转台单模板
 * @program holder-saas-store-print
 */
public class TurnTableTemplate extends AbsCustomTemplate<PrintTurnTableDTO, TurnTableFormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintTurnTableDTO printDTO = getPrintDTO();
        TurnTableFormatDTO formatDTO = getFormatDTO();
        PrintFormatLayoutContainer container = new PrintFormatLayoutContainer();

        // 门店名
        if (InvoiceTypeEnum.TURN_TABLE.getType().equals(printDTO.getInvoiceType())) {
            Assert.notNull(formatDTO.getStoreName(), "门店名称不能为空");
            container.addStoreName(printDTO.getStoreName(), formatDTO.getStoreName());
        }

        // 票据类型
        container.addInvoiceType(MultiLangUtils.get("table_transfer_invoice_header"), formatDTO.getInvoiceName());

        // 转台详情
        container.addTurnTableDetail(printDTO.getSrcTableName(),
                printDTO.getDestTableName(), formatDTO.getOriTable(), formatDTO.getNewTable());

        // 操作员，转台时间
        // 打印时间
        container.addOpStaffAndTurnTimeAndPrintTime(getPageSize(),
                printDTO.getOperatorStaffName(), formatDTO.getOperator(),
                printDTO.getTurnTime(), formatDTO.getTurnTime(),
                printDTO.getCreateTime(), formatDTO.getPrintTime());

        return container.getPrintRows();
    }

    @Override
    public String getFailedMsg() {
        return "转台单打印失败，请及时处理";
    }

    @Override
    public List<PrintRow> getHeader() {
        PrintTurnTableDTO printDTO = getPrintDTO();
        if (InvoiceTypeEnum.TURN_TABLE_ITEM.getType().equals(printDTO.getInvoiceType())) {
            return super.getHeader();
        }
        TurnTableFormatDTO formatDTO = getFormatDTO();
        if (formatDTO == null) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(formatDTO.getHeaders())) {
            return Collections.emptyList();
        }
        PrintFormatLayoutContainer container = new PrintFormatLayoutContainer();
        formatDTO.getHeaders().forEach(container::addHeader);
        return container.getPrintRows();
    }

    @Override
    public List<PrintRow> getFooter() {
        PrintTurnTableDTO printDTO = getPrintDTO();
        if (InvoiceTypeEnum.TURN_TABLE_ITEM.getType().equals(printDTO.getInvoiceType())) {
            return super.getFooter();
        }
        TurnTableFormatDTO formatDTO = getFormatDTO();
        if (formatDTO == null) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(formatDTO.getFooters())) {
            return Collections.emptyList();
        }
        PrintFormatLayoutContainer container = new PrintFormatLayoutContainer();
        formatDTO.getFooters().forEach(container::addFooter);
        return container.getPrintRows();
    }
}
