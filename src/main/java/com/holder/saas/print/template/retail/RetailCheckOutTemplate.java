package com.holder.saas.print.template.retail;

import com.holder.saas.print.template.base.AbsFrontItemTemplate;
import com.holder.saas.print.utils.template.row.composite.PrintFixedLayoutContainer;
import com.holderzone.saas.store.dto.print.content.retail.PrintRetailCheckOutDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CheckOutTemplate
 * @date 2018/02/14 09:00
 * @description 结帐单模板
 * @program holder-saas-store-print
 */
public class RetailCheckOutTemplate extends AbsFrontItemTemplate<PrintRetailCheckOutDTO, FormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintRetailCheckOutDTO printDTO = getPrintDTO();
        PrintFixedLayoutContainer container = new PrintFixedLayoutContainer();

        // 门店名
        container.addStoreName(printDTO.getStoreName());

        // 订单号
        container.addOrderNo(printDTO.getOrderNo());

        // 菜品、商品总额
        container.addTableItem(printDTO.getItemRecordList(), printDTO.getTotal(), getPageSize());

        // 优惠明细
        // 优惠合计
        // 应付金额
        container.addReduceRecordAndPayable(printDTO.getReduceRecordList(), printDTO.getPayAble());

        // 支付方式
        // 实付金额
        container.addPayRecordAndActuallyPay(printDTO.getPayRecordList(), printDTO.getChangedPay(), printDTO.getActuallyPay());

        // 操作员、下单时间
        // 结账时间、打印时间
        container.addOpStaffAndOpenTableTimeAndCheckTimeAndPrintTime(
                getPageSize(), printDTO.getOperatorStaffName(),
                printDTO.getOpenTableTime(), printDTO.getCheckOutTime(), printDTO.getCreateTime()
        );

        return container.getPrintRows();
    }

    @Override
    public String getFailedMsg() {
        String orderNo = getPrintDTO().getOrderNo();
        if (orderNo.length() >= 4) {
            orderNo = orderNo.substring(orderNo.length() - 4);
        }
        return "结账单，订单尾号[" + orderNo + "]打印失败，请及时处理";
    }
}
