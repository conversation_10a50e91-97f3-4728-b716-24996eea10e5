package com.holder.saas.print.template.factory;

import com.holder.saas.print.service.PrinterFormatService;
import com.holder.saas.print.template.*;
import com.holder.saas.print.template.base.PrintTemplate;
import com.holder.saas.print.template.base.PrintTemplateAware;
import com.holder.saas.print.template.base.PrintTemplateBuilder;
import com.holder.saas.print.template.retail.*;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintComponentFactory
 * @date 2018/02/14 09:00
 * @description 打印单据模板工厂类
 * @program holder-saas-store-print
 */
@Component
@Slf4j
@SuppressWarnings("unchecked")
public class PrintTemplateFactory {

    private static final String NOT_SUPPORT_INVOICE_TYPE = "不支持的票据类型：";

    private final PrinterFormatService printerFormatService;

    @Autowired
    public PrintTemplateFactory(PrinterFormatService printerFormatService) {
        this.printerFormatService = printerFormatService;
    }

    public PrintTemplate<? extends PrintDTO, ? extends FormatDTO> create(Integer invoiceType, int pageSize,
                                                                         String content, String storeGuid) {
        PrintTemplateBuilder<? extends PrintDTO, ? extends FormatDTO>
                printTemplate = create(invoiceType, pageSize, content);

//        log.info("printTemplate={}", JSONObject.toJSONString(printTemplate));

        invoiceType = InvoiceTypeEnum.ofType(invoiceType).getFormatReference().getType();
        printTemplate.setFormatDTO(printerFormatService.query(storeGuid, invoiceType));
        return printTemplate.build();
    }

    public PrintTemplate<? extends PrintDTO, ? extends FormatDTO> create(Integer invoiceType, int pageSize,
                                                                         String content, FormatDTO formatDTO) {
        PrintTemplateBuilder<? extends PrintDTO, ? extends FormatDTO>
                printTemplate = create(invoiceType, pageSize, content);
        printTemplate.setFormatDTO(resolveMockFormat(formatDTO));
        return printTemplate.build();
    }

    /**
     * 给打印模板设置 printDTO
     *
     * @param invoiceType
     * @param content
     * @return
     */
    public PrintTemplateAware<? extends PrintDTO, ? extends FormatDTO> create(Integer invoiceType, String content) {
        return create(invoiceType, 0, content).buildAware();
    }

    /**
     * 给打印模板设置 printDTO
     *
     * @param invoiceType
     * @param pageSize
     * @param content
     * @return
     */
    private PrintTemplateBuilder<? extends PrintDTO, ? extends FormatDTO> create(Integer invoiceType, int pageSize, String content) {
        PrintTemplateBuilder<? extends PrintDTO, ? extends FormatDTO> printTemplate = create(invoiceType);
        printTemplate.setPageSize(pageSize);
        printTemplate.setPrintDTO(
                InvoiceTypeEnum.resolvePrintBy(content),
                o -> o.setCreateTime(DateTimeUtils.nowMillis())
        );
        return printTemplate;
    }

    /**
     * 根据票据类型返回对应的打印模板
     *
     * @param invoiceType
     * @return
     */
    private PrintTemplateBuilder<? extends PrintDTO, ? extends FormatDTO> create(Integer invoiceType) {
        switch (InvoiceTypeEnum.ofType(invoiceType)) {
            case ITEM_LIST:
            case ITEM_REPEAT_ORDER:
            case CHANGE_ITEM:
            case TRANSFER_ITEM:
            case ORDER_ITEM:
            case REFUND_ITEM:
            case ITEM_STATS:
            case ITEM_REFUND_STATS:
            case ITEM_GIFT_STATS:
            case RETAIL_ITEM_STATS:
            case RETAIL_ITEM_REFUND_STATS:
            case RETAIL_ITEM_GIFT_STATS: {
                return getItemTemplate(invoiceType);
            }
            case PRE_CHECKOUT: {
                return new PreCheckoutTemplate();
            }
            case PRE_CHECKOUT_TABLES: {
                return new PreCoTableCbTemplate();
            }
            case CHECKOUT: {
                return new CheckOutTemplate();
            }
            case CHECKOUT_TABLES: {
                return new CoTableCbTemplate();
            }
            case STORED_CASH: {
                return new StoredCashTemplate();
            }
            case QUEUE: {
                return new QueueTemplate();
            }
            case TURN_TABLE:
            case TURN_TABLE_ITEM: {
                return new TurnTableTemplate();
            }
            case TAKEOUT: {
                return new TakeoutTemplate();
            }
            case HANDOVER: {
                return new HandOverTemplate();
            }
            case HANDOVER_NEW: {
                return new HandOverNewTemplate();
            }
            case HANDOVER_PRE_NEW: {
                return new HandOverPreNewTemplate();
            }
            case RESERVE_ITEM_STATS: {
                return new ReserveItemsTemplate();
            }
            case OP_STATS: {
                return new OpStatsTemplate();
            }
            case RECEIPT_STATS: {
                return new ReceiptStatsTemplate();
            }
            case MEM_STATS: {
                return new MemStatsTemplate();
            }
            case MEM_CONSU_STATS: {
                return new MemConsuTemplate();
            }
            case MEM_RECHAR_STATS: {
                return new MemRecharTemplate();
            }
            case TRADE_STATS: {
                return new TradeStatsTemplate();
            }
            case TYPE_STATS:
            case RETAIL_TYPE_STATS: {
                return new TypeStatsTemplate();
            }
            case PROP_STATS: {
                return new PropStatsTemplate();
            }
            case LABEL:
            case ITEM_LABEL:
                return getLabelTemplate(invoiceType);
            case RETAIL_CHECKOUT: {
                return new RetailCheckOutTemplate();
            }
            case RETAIL_HANDOVER: {
                return new RetailHandOverTemplate();
            }
            case RETAIL_TAKEOUT: {
                return new RetailTakeoutTemplate();
            }
            case RETAIL_OP_STATS: {
                return new RetailOpStatsTemplate();
            }
            case DEBT_REPAYMENT: {
                return new DebtRepaymentTemplate();
            }
            case SALE_REFUND_STATS: {
                return new SaleRefundStatsTemplate();
            }
            case RESERVE_PAY_STATS: {
                return new ReservePayTemplate();
            }
            case REFUND_INVOICE: {
                return new RefundTemplate();
            }
            default: {
                throw new BusinessException(NOT_SUPPORT_INVOICE_TYPE + invoiceType);
            }
        }
    }

    private static <T extends FormatDTO> T resolveMockFormat(FormatDTO formatDTO) {
        return (T) formatDTO;
    }

    private PrintTemplateBuilder<? extends PrintDTO, ? extends FormatDTO> getLabelTemplate(Integer invoiceType) {
        switch (InvoiceTypeEnum.ofType(invoiceType)) {
            case LABEL:
                return new LabelTemplate();
            case ITEM_LABEL:
                return new ItemLabelTemplate();
            default:
                throw new BusinessException(NOT_SUPPORT_INVOICE_TYPE + invoiceType);
        }
    }

    private PrintTemplateBuilder<? extends PrintDTO, ? extends FormatDTO> getItemTemplate(Integer invoiceType) {
        switch (InvoiceTypeEnum.ofType(invoiceType)) {
            case ITEM_LIST:
            case ITEM_REPEAT_ORDER: {
                return new ItemDetailTemplate();
            }
            case CHANGE_ITEM: {
                return new ChangeItemTemplate();
            }
            case TRANSFER_ITEM: {
                return new TransferItemTemplate();
            }
            case ORDER_ITEM: {
                return new OrderItemTemplate();
            }
            case REFUND_ITEM: {
                return new RefundItemTemplate();
            }
            case ITEM_STATS:
            case ITEM_REFUND_STATS:
            case ITEM_GIFT_STATS:
            case RETAIL_ITEM_STATS:
            case RETAIL_ITEM_REFUND_STATS:
            case RETAIL_ITEM_GIFT_STATS: {
                return new ItemStatsTemplate();
            }
            default:
                throw new BusinessException(NOT_SUPPORT_INVOICE_TYPE + invoiceType);
        }
    }
}
