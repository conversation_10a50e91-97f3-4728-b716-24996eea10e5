package com.holder.saas.print.template.base;

import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;

import java.util.List;

public interface PrintTemplate<T extends PrintDTO, F extends FormatDTO> {

    String getFailedMsg();

    String getSucceedMsg();

    List<PrintRow> getPrintRows();
}
