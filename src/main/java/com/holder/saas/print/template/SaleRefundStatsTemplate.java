package com.holder.saas.print.template;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holder.saas.print.utils.template.row.composite.PrintLayoutUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.print.content.PrintSaleRefundStatsDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import com.holderzone.saas.store.util.EncryptionSymbolUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/15
 * @since 1.8
 */
public class SaleRefundStatsTemplate extends AbsPrintTemplate<PrintSaleRefundStatsDTO, FormatDTO> {
    @Override
    public List<PrintRow> getContent() {
        PrintSaleRefundStatsDTO printDTO = getPrintDTO();
        List<PrintRow> printRows = new ArrayList<>();
        // 门店名
        PrintRowUtils.add(printRows, new Section()
                .addText(printDTO.getStoreName(), Font.NORMAL_BOLD)
                .setAlign(Text.Align.Center));
        // 票据类型
        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get("sale_refund_statistics_invoice_header"), Font.NORMAL)
                .setAlign(Text.Align.Center));
        // 开始时间
        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("start_time"))
                .setValueString(DateTimeUtils.mills2String(printDTO.getStartTime(), "yyyy-MM-dd HH:mm:ss")));

        // 结束时间
        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("end_time"))
                .setValueString(DateTimeUtils.mills2String(printDTO.getEndTime(), "yyyy-MM-dd HH:mm:ss")));

        // 收银员
        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("checkout_staffs"))
                .setValueString(printDTO.getCheckoutStaffs()));

        //正餐部分退款
        printStatistics(printRows, printDTO.getDineinPartRefund(), Constant.DINEIN_PART_REFUND_STATISTICS, false);

        //正餐反结账
        printStatistics(printRows, printDTO.getDineinRecovery(), Constant.DINEIN_RECOVERY_STATISTICS, false);

        //快销部分退款
        printStatistics(printRows, printDTO.getFastPartRefund(), Constant.FAST_PART_REFUND_STATISTICS, false);

        //快销反结账
        printStatistics(printRows, printDTO.getFastRecovery(), Constant.FAST_RECOVERY_STATISTICS, false);

        //总计
        printStatistics(printRows, printDTO.getTotal(), Constant.REFUND_TOTAL__STATISTICS,true);

        PrintRowUtils.add(printRows, new Separator());
        PrintLayoutUtils.addOpStaffAndPrintTime(getPageSize(), printDTO.getOperatorStaffName(), printDTO.getCreateTime(), printRows);
        return printRows;
    }

    private void printStatistics(List<PrintRow> printRows, PrintSaleRefundStatsDTO.SaleRefundStats saleRefundStats,
                                 String refundName, boolean isTotal) {
        if (saleRefundStats == null) {
            return;
        }
        if (!isTotal && StringUtils.equals(saleRefundStats.getRefundOrderCountStr(), "0")) {
            return;
        }
        PrintRowUtils.add(printRows, new Separator());
        PrintRowUtils.add(printRows, new Section()
                .addText(EncryptionSymbolUtil.isEncryption(saleRefundStats.getRefundName()) ? saleRefundStats.getRefundName() : MultiLangUtils.get(refundName), Font.NORMAL_BOLD)
                .setAlign(Text.Align.Left));
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.REFUND_ORDER_COUNT))
                .setValueString(saleRefundStats.getRefundOrderCountStr()));
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.REFUND_AMOUNT))
                .setValueString(EncryptionSymbolUtil.isEncryption(saleRefundStats.getRefundAmountStr()) ?
                        saleRefundStats.getRefundAmountStr() :
                        BigDecimalUtils.moneyTrimmedString(saleRefundStats.getRefundAmount())));
    }

    @Override
    public String getFailedMsg() {
        return "退款统计单打印失败，请及时处理";
    }
}
