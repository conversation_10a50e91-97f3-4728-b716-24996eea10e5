package com.holder.saas.print.template;

import com.holder.saas.print.entity.biz.ItemTableFormatBO;
import com.holder.saas.print.template.base.AbsFrontItemTemplate;
import com.holder.saas.print.utils.template.row.composite.PrintFormatLayoutContainer;
import com.holderzone.saas.store.dto.print.content.PrintTakeoutDTO;
import com.holderzone.saas.store.dto.print.format.TakeoutFormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeoutTemplate
 * @date 2018/02/14 09:00
 * @description 外卖单模板
 * @program holder-saas-store-print
 */
@Slf4j
public class TakeoutTemplate extends AbsFrontItemTemplate<PrintTakeoutDTO, TakeoutFormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintTakeoutDTO printDTO = getPrintDTO();
        TakeoutFormatDTO formatDTO = getFormatDTO();
        // 自定义模板容器
        PrintFormatLayoutContainer container = new PrintFormatLayoutContainer();
        // 平台名称
        container.appendTakeoutPlatform(printDTO.getPlatform(), printDTO.getPlatformOrder(), printDTO.getCancelFlag(), formatDTO.getPlatform());
        if (Boolean.TRUE.equals(printDTO.getAbnormal())) {
            // 异常单
            container.appendTakeoutAbnormal();
        } else {
            // 正常外卖单
            // 门店名称
            container.appendTakeoutStoreName(printDTO.getStoreName(), formatDTO.getStoreName());
            // 支付状态
            container.appendTakeoutPayMsg(printDTO.getPayMsg(), formatDTO.getPayMsg());
        }
        // 空行
        container.addBlankRow();
        // 是否预订单 + 期望送达时间
        container.appendTakeoutExpectedDeliveryTime(printDTO.getReserve(), printDTO.getExpectTime(), formatDTO.getExpectTime());
        // 下单时间
        container.appendTakeoutOrderTime(printDTO.getOrderTime(), formatDTO.getOrderTime());
        // -------------------------------------------
        container.addSeparator();
        // 发票税号
        container.appendTakeoutInvoice(printDTO.getInvoiced(), printDTO.getInvoiceTitle(), printDTO.getTaxpayerId());
        // 备注 (订单备注/餐具备注)
        container.appendTakeoutRemark(printDTO.getRemark(), printDTO.getDinnersNumber(), formatDTO.getRemark());
        // -------------------------------------------
        // 商品列表
        container.appendTakeoutItemList(getPageSize(), printDTO.getItemRecordList(), ItemTableFormatBO.of(formatDTO), printDTO.getFullGiftRemark());
        // -------------------------------------------
        if (!Boolean.TRUE.equals(printDTO.getAbnormal())) {
            // 商品总额 + 餐盒费
            container.appendTakeoutOrderTotalAmount(printDTO.getAdditionalChargeList(), printDTO.getItemTotalPrice(),
                    formatDTO.getItemSumTotal(), formatDTO.getPackageTotal(), formatDTO.getShipTotal());
            // 商品原价 + 优惠明细 + 实付金额
            container.appendTakeoutOrderAmount(printDTO.getOriginalPrice(), printDTO.getActuallyPay(), printDTO.getReduceRecordList(),
                    formatDTO.getOriginalPrice(), formatDTO.getActuallyPay(), formatDTO.getDiscount());
        }
        // 顾客信息 (客户姓名/客户电话/客户隐私号/客户地址)
        container.appendTakeoutCustomerInfo(printDTO.getPlatform(), printDTO.getReceiverName(), printDTO.getReceiverTel(),
                printDTO.getPrivacyPhone(), printDTO.getReceiverAddress(), printDTO.getRecipientAddressDesensitization(),
                formatDTO.getCustomerInfo());
        // 结账操作员 + 结账单打印时间
        container.appendTakeoutOpStaffAndPrintTime(getPageSize(), printDTO.getOperatorStaffName(), printDTO.getCreateTime(),
                formatDTO.getOperator(), formatDTO.getPrintTime());
        // 订单条形码 + 订单号
        container.appendTakeoutOrderNoBarCode(printDTO.getOrderNo(), formatDTO.getOrderNo(), formatDTO.getOrderNoBarCode());
        // 外卖出餐码 CC+订单号
        if (Boolean.FALSE.equals(printDTO.getAbnormal())) {
            container.appendFoodFinishBarCode(printDTO.getOrderNo(), printDTO.getFoodFinishBarCode(), formatDTO.getFoodFinishBarCode());
        }
        // 外卖安心卡
        container.appendTakeoutAssuranceCard(getPageSize());
        return container.getPrintRows();
    }

    @Override
    public String getFailedMsg() {
        return "外卖单 [" + getPrintDTO().getPlatformOrder() + "]号订单打印失败，请及时处理";
    }
}
