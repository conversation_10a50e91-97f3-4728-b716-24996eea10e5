package com.holder.saas.print.template;

import com.google.common.collect.Lists;
import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.composite.PrintLayoutUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holder.saas.print.utils.template.row.composite.table.PrintTableUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.order.response.daily.GatherRespDTO;
import com.holderzone.saas.store.dto.print.content.PrintReceiptStatsDTO;
import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import com.holderzone.saas.store.dto.print.content.nested.PayRecordStats;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import com.holderzone.saas.store.dto.print.template.printable.Table;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.util.EncryptionSymbolUtil;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReceiptStatsTemplate
 * @date 2018/02/14 09:00
 * @description 收款统计模板
 * @program holder-saas-store-print
 */
public class ReceiptStatsTemplate extends AbsPrintTemplate<PrintReceiptStatsDTO, FormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintReceiptStatsDTO printDTO = getPrintDTO();
        List<PrintRow> printRows = new ArrayList<>();

        // 门店名
        PrintRowUtils.add(printRows, new Section()
                .addText(printDTO.getStoreName(), Font.NORMAL_BOLD)
                .setAlign(Text.Align.Center));

        // 票据类型
        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get("payment_statistics_invoice_header"), Font.NORMAL)
                .setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("start_time"))
                .setValueString(DateTimeUtils.mills2String(printDTO.getStartTime(), "yyyy-MM-dd HH:mm:ss")));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("end_time"))
                .setValueString(DateTimeUtils.mills2String(printDTO.getEndTime(), "yyyy-MM-dd HH:mm:ss")));

        PrintRowUtils.add(printRows, new Separator());

        // 收款合计
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("total_amount_received"))
                .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getReceiptTotal())));

        PrintRowUtils.add(printRows, new Separator());

        // 收款合计明细
        addReceiptDetail(printRows);

        // 添加收款方式列表
        addReceiptStatsDetail(printRows);

        PrintLayoutUtils.addOpStaffAndPrintTime(getPageSize(), printDTO.getOperatorStaffName(), printDTO.getCreateTime(), printRows);
        return printRows;
    }

    /**
     * 添加收款合计明细
     */
    private void addReceiptDetail(List<PrintRow> printRows) {
        PrintReceiptStatsDTO printDTO = getPrintDTO();
        if (CollectionUtils.isEmpty(printDTO.getReceiptDetail())) {
            return;
        }
        String prefix = "--";
        for (PayRecord payRecord : printDTO.getReceiptDetail()) {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(prefix + payRecord.getPayName())
                    .setValueString(getAmount(payRecord.getAmount(), payRecord.getAmountStr())));
            boolean amountEncryption = EncryptionSymbolUtil.isEncryption(payRecord.getAmountStr());
            if (!amountEncryption && Objects.nonNull(payRecord.getExcessAmount()) && payRecord.getExcessAmount().compareTo(BigDecimal.ZERO) > 0) {
                PrintRowUtils.add(printRows, new KeyValue()
                        .setKeyString("")
                        .setValueString("(" + MultiLangUtils.get("remaining_balance") + BigDecimalUtils.moneyTrimmedString(payRecord.getExcessAmount()) + ")"));
            }
        }
    }

    /**
     * 添加收款方式列表
     */
    private void addReceiptStatsDetail(List<PrintRow> printRows) {
        PrintReceiptStatsDTO printDTO = getPrintDTO();
        if (CollectionUtils.isEmpty(printDTO.getReceiptStatsDetail())) {
            return;
        }
        // 定义表格
        Table table = new Table(
                Arrays.asList(new Text(MultiLangUtils.get("payment_methods")), new Text(MultiLangUtils.get(Constant.NET_SALES)), new Text(MultiLangUtils.get("recharge_amount2")), new Text(MultiLangUtils.get("reservation_amount"))),
                PrintTableUtils.getStatsPaymentColWidthList(getPageSize()),
                Arrays.asList(false, false, false, false),
                true
        );
        List<String> newLine = Lists.newArrayList();
        // 表格
        addReceiptStatsTable(table, newLine);
        table.addRow(Collections.singletonList(new Text("-")));
        // 合计
        addReceiptStatsTotal(table, newLine);
        PrintTableUtils.addNewLine(table, newLine);
        PrintRowUtils.add(printRows, table);
    }

    /**
     * 添加收款方式表格
     */
    private void addReceiptStatsTable(Table table, List<String> newLine) {
        PrintReceiptStatsDTO printDTO = getPrintDTO();
        if (CollectionUtils.isEmpty(printDTO.getReceiptStatsDetail())) {
            return;
        }
        boolean smallSize = (getPageSize() == 58);
        for (PayRecordStats payRecordStats : printDTO.getReceiptStatsDetail()) {
            // 销售净额
            String salesIncome = getAmountOrElse(payRecordStats.getSalesIncome(), payRecordStats.getSalesIncomeStr());
            // 充值额
            String rechargeIncome = getAmountOrElse(payRecordStats.getRechargeIncome(), payRecordStats.getRechargeIncomeStr());
            // 预付金
            String reserveIncome = getAmountOrElse(payRecordStats.getReserveIncome(), payRecordStats.getReserveIncomeStr());
            table.addRow(Arrays.asList(
                    new Text(smallSize ? PrintTableUtils.cutOutTextLength(payRecordStats.getPayName(), newLine, 4) : payRecordStats.getPayName()),
                    new Text(smallSize ? PrintTableUtils.cutOutTextLength(salesIncome, newLine, null) : salesIncome),
                    new Text(smallSize ? PrintTableUtils.cutOutTextLength(rechargeIncome, newLine, null) : rechargeIncome),
                    new Text(smallSize ? PrintTableUtils.cutOutTextLength(reserveIncome, newLine, null) : reserveIncome)));
            PrintTableUtils.addNewLine(table, newLine);
            // 特殊情况：三方活动余出金额
            if (EncryptionSymbolUtil.isNormal(payRecordStats.getExcessAmountStr())
                    && Objects.nonNull(payRecordStats.getExcessAmount())
                    && payRecordStats.getExcessAmount().compareTo(BigDecimal.ZERO) > 0) {
                table.addRow(Arrays.asList(
                        new Text("            (" + MultiLangUtils.get("remaining_balance")),
                        new Text(BigDecimalUtils.moneyTrimmedString(payRecordStats.getExcessAmount())),
                        new Text(")"),
                        new Text("")));
            }
            // 如果有innerDetail
            if (!CollectionUtils.isEmpty(payRecordStats.getInnerDetails())) {
                for (GatherRespDTO.InnerDetails innerDetail : payRecordStats.getInnerDetails()) {
                    table.addRow(Arrays.asList(
                            new Text(Constant.TINY_BLANK + innerDetail.getGatherName()),
                            new Text(BigDecimalUtils.moneyTrimmedString(innerDetail.getConsumerAmount())),
                            new Text("——"),
                            new Text("——")));
                }
            }
        }
    }


    /**
     * 添加收款方式表格合计
     */
    private void addReceiptStatsTotal(Table table, List<String> newLine) {
        PrintReceiptStatsDTO printDTO = getPrintDTO();
        if (CollectionUtils.isEmpty(printDTO.getReceiptStatsDetail())) {
            return;
        }
        boolean smallSize = (getPageSize() == 58);
        PayRecordStats firstPayRecordStats = printDTO.getReceiptStatsDetail().get(0);
        // 销售净额合计
        BigDecimal salesIncome = printDTO.getReceiptStatsDetail().stream()
                .map(PayRecordStats::getSalesIncome)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        String salesIncomeStr = getAmountOrElse(salesIncome, firstPayRecordStats.getSalesIncomeStr());

        // 充值额合计
        BigDecimal rechargeIncome = printDTO.getReceiptStatsDetail().stream()
                .map(PayRecordStats::getRechargeIncome)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        String rechargeIncomeStr = getAmountOrElse(rechargeIncome, firstPayRecordStats.getRechargeIncomeStr());

        // 预付金合计
        BigDecimal reserveIncome = printDTO.getReceiptStatsDetail().stream()
                .map(PayRecordStats::getReserveIncome)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        String reserveIncomeStr = getAmountOrElse(reserveIncome, firstPayRecordStats.getReserveIncomeStr());
        table.addRow(Arrays.asList(
                new Text(smallSize ? PrintTableUtils.cutOutTextLength(MultiLangUtils.get("price_total"), newLine, null) : MultiLangUtils.get("price_total")),
                new Text(smallSize ? PrintTableUtils.cutOutTextLength(salesIncomeStr, newLine, null) : salesIncomeStr),
                new Text(smallSize ? PrintTableUtils.cutOutTextLength(rechargeIncomeStr, newLine, null) : rechargeIncomeStr),
                new Text(smallSize ? PrintTableUtils.cutOutTextLength(reserveIncomeStr, newLine, null) : reserveIncomeStr)
        ));
    }

    private String getAmount(BigDecimal amount, String amountStr) {
        return EncryptionSymbolUtil.isEncryption(amountStr) ?
                amountStr :
                BigDecimalUtils.moneyTrimmedString(amount);
    }

    private String getAmountOrElse(BigDecimal amount, String amountStr) {
        return EncryptionSymbolUtil.isEncryption(amountStr) ?
                amountStr :
                Optional.ofNullable(amount)
                        .filter(bigDecimal -> bigDecimal.compareTo(BigDecimal.ZERO) != 0)
                        .map(BigDecimalUtils::moneyTrimmedString).orElse("——");
    }

    @Override
    public String getFailedMsg() {
        return "收款统计单打印失败，请及时处理";
    }
}
