package com.holder.saas.print.template;

import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.composite.PrintLayoutUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holderzone.saas.store.dto.print.content.PrintHandOverDTO;
import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import com.holderzone.saas.store.dto.print.format.HandOverFormatDTO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandOverTemplate
 * @date 2018/02/14 09:00
 * @description 交接单模板
 * @program holder-saas-store-print
 */
public class HandOverTemplate extends AbsPrintTemplate<PrintHandOverDTO, HandOverFormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintHandOverDTO printDTO = getPrintDTO();
        List<PrintRow> printRows = new ArrayList<>();

        PrintRowUtils.add(printRows, new Section()
                .addText(printDTO.getStoreName(), Font.NORMAL_BOLD)
                .setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get("handover_invoice_header"), Font.NORMAL)
                .setAlign(Text.Align.Center));

        if (80 == getPageSize()) {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(MultiLangUtils.get("handover_employee") + printDTO.getStaffName())
                    .setValueString(MultiLangUtils.get("duration_time") + printDTO.getDuration()));
        } else {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKeyString(MultiLangUtils.get("handover_employee"))
                    .setValueString(printDTO.getStaffName()));
            PrintRowUtils.add(printRows, new KeyValue()
                    .setAlignEdges(false)
                    .setKeyString(MultiLangUtils.get("duration_time"))
                    .setValueString(printDTO.getDuration()));
        }

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("start_time"))
                .setValueString(printDTO.getBeginTime()));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("end_time"))
                .setValueString(printDTO.getOverTime()));

        PrintRowUtils.add(printRows, new Separator());

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("on_duty_turnover"))
                .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getDutyAmount())));

        PrintRowUtils.add(printRows, new Separator());

        if (!CollectionUtils.isEmpty(printDTO.getPayRecordList())) {
            for (PayRecord payRecord : printDTO.getPayRecordList()) {
                PrintRowUtils.add(printRows, new KeyValue()
                        .setKeyString(payRecord.getPayName())
                        .setValueString(BigDecimalUtils.moneyTrimmedString(payRecord.getAmount())));
            }
        }

        PrintRowUtils.add(printRows, new Separator());

        PrintRowUtils.add(printRows, new Section(MultiLangUtils.get("project_income"), Font.NORMAL_BOLD));

        PrintRowUtils.add(printRows, new Separator());

        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("sales_income"))
                .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getSaleIncome())));

        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("recharge_income"))
                .setValueString(BigDecimalUtils.moneyTrimmedString(printDTO.getRechargeIncome())));

        PrintRowUtils.add(printRows, new Separator());

        PrintRowUtils.add(printRows, new Section(MultiLangUtils.get("note_for_turnover")));

        PrintRowUtils.add(printRows, new Separator());

        PrintLayoutUtils.addOpStaffAndPrintTime(getPageSize(), printDTO.getOperatorStaffName(), printDTO.getCreateTime(), printRows);

        return printRows;
    }

    @Override
    public String getFailedMsg() {
        return "交接单打印失败，请及时处理";
    }
}
