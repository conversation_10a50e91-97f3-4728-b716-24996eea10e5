package com.holder.saas.print.template.cloud;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.entity.domain.PrintRecordDO;
import com.holder.saas.print.entity.enums.ItemStateEnum;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.cloud.CloudPrinterUtils;
import com.holder.saas.print.utils.template.cloud.FeiePrinterUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.print.content.PrintCheckOutDTO;
import com.holderzone.saas.store.dto.print.content.nested.AdditionalCharge;
import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.content.nested.ReduceRecord;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * <AUTHOR>
 * @date 2024/4/8
 * @description 飞蛾打印机结账单模板
 */
@Slf4j
public class CloudCheckOutTemplate {

    private CloudCheckOutTemplate() {
    }

    public static String getContent(PrintRecordDO printRecordDO) {
        String checkOutContent = printRecordDO.getPrintContent();
        PrintCheckOutDTO checkOutDTO = JacksonUtils.toObject(PrintCheckOutDTO.class, checkOutContent);
        StringBuilder checkOutContentBuilder = new StringBuilder();

        // 门店名
        checkOutContentBuilder.append(getStoreName(checkOutDTO.getStoreName()));

        // 标题
        checkOutContentBuilder.append(getTitle());

        // 牌号
        checkOutContentBuilder.append(getTrademark(checkOutDTO.getMarkNo()));

        // 订单号
        checkOutContentBuilder.append(getOrderNo(checkOutDTO.getOrderNo()));

        // 人数
        checkOutContentBuilder.append(getPersonNumber(checkOutDTO.getPersonNumber()));

        // 备注
        checkOutContentBuilder.append(getOrderRemark(checkOutDTO.getOrderRemark()));

        // 分割
        checkOutContentBuilder.append(FeiePrinterUtils.addDividingLine());

        // 商品、单价、数量、小计 商品合计
        checkOutContentBuilder.append(getItemContent(checkOutDTO.getItemRecordList(), checkOutDTO.getTotal()));

        // 附加费明细 附加费合计
        checkOutContentBuilder.append(getChargeContent(checkOutDTO.getAdditionalChargeList()));

        // 分割
        checkOutContentBuilder.append(FeiePrinterUtils.addDividingLine());

        // 优惠明细 优惠合计 应付金额
        List<ReduceRecord> reduceRecordList = checkOutDTO.getReduceRecordList();
        checkOutContentBuilder.append(getReduceContent(reduceRecordList));

        // 分割
        checkOutContentBuilder.append(FeiePrinterUtils.addDividingLine());

        // 支付方式 实付金额
        List<PayRecord> payRecordList = checkOutDTO.getPayRecordList();
        BigDecimal changedPay = checkOutDTO.getChangedPay();
        BigDecimal actuallyPay = checkOutDTO.getActuallyPay();
        String payRecordContent = getPayRecordContent(payRecordList, changedPay, actuallyPay);
        checkOutContentBuilder.append(payRecordContent);

        // 分割
        checkOutContentBuilder.append(FeiePrinterUtils.addDividingLine());

        // 操作员

        // 下单时间

        // 结账时间
        checkOutContentBuilder.append(getCheckOutTime(checkOutDTO.getCheckOutTime()));

        // 打印时间

        return checkOutContentBuilder.toString();
    }

    /**
     * 支付方式 实付金额
     */
    private static String getPayRecordContent(List<PayRecord> payRecordList,
                                              BigDecimal changedPay,
                                              BigDecimal actuallyPay) {
        StringBuilder payRecordContent = new StringBuilder();
        if (!CollectionUtils.isEmpty(payRecordList)) {
            for (PayRecord payRecord : payRecordList) {
                String payName = PaymentTypeEnum.PaymentType.getLocaleName(payRecord.getPayName());
                String payAmount = BigDecimalUtils.moneyTrimmedString(payRecord.getAmount());
                String payStr = CloudPrinterUtils.handleInterval(payName, payAmount, CloudPrinterUtils.LINE_SIZE_SMALL);
                payRecordContent.append(payStr);
            }
        }

        // 找零金额大于0才展示
        if (Objects.nonNull(changedPay) && changedPay.compareTo(BigDecimal.ZERO) > 0) {
            String change = MultiLangUtils.get(Constant.CHANGE);
            String changedPayStr = BigDecimalUtils.moneyTrimmedString(changedPay);
            String changeStr = CloudPrinterUtils.handleInterval(change, changedPayStr, CloudPrinterUtils.LINE_SIZE);
            payRecordContent.append(changeStr);
        }

        // 实付金额
        String paid = MultiLangUtils.get(Constant.PAID);
        String actuallyPayStr = BigDecimalUtils.moneyTrimmedString(actuallyPay);
        String paidStr = CloudPrinterUtils.handleInterval(paid, actuallyPayStr, CloudPrinterUtils.LINE_SIZE);
        paidStr = FeiePrinterUtils.addMagnifyOnce(paidStr);
        payRecordContent.append(paidStr);
        return payRecordContent.toString();
    }

    /**
     * 优惠明细 优惠合计 应付金额
     */
    private static String getReduceContent(List<ReduceRecord> reduceRecordList) {
        if (CollectionUtils.isEmpty(reduceRecordList)) {
            return "";
        }
        BigDecimal reduceSum = reduceRecordList.stream()
                .map(ReduceRecord::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
        StringBuilder reduceContent = new StringBuilder();
        if (reduceSum.compareTo(BigDecimal.ZERO) != 0) {
            for (ReduceRecord reduce : reduceRecordList) {
                if (reduce.getAmount() != null && reduce.getAmount().compareTo(BigDecimal.ZERO) != 0) {
                    String reduceName = DiscountTypeEnum.getLocaleDescByName(reduce.getName());
                    String reduceAmount = BigDecimalUtils.moneyTrimmedString(reduce.getAmount());
                    String amountDueStr = CloudPrinterUtils.handleInterval(reduceName, reduceAmount, CloudPrinterUtils.LINE_SIZE_SMALL);
                    reduceContent.append(amountDueStr);
                }
            }
            // 活动合计
            String promotionTotal = MultiLangUtils.get(Constant.PROMOTION_TOTAL);
            String moneyed = BigDecimalUtils.moneyTrimmedString(reduceSum);
            String promotionTotalStr = CloudPrinterUtils.handleInterval(promotionTotal, moneyed, CloudPrinterUtils.LINE_SIZE_SMALL);
            reduceContent.append(promotionTotalStr);
        }

        // 应付金额
        return reduceContent.toString();
    }


    private static String getChargeContent(List<AdditionalCharge> additionalChargeList) {
        if (CollectionUtils.isEmpty(additionalChargeList)) {
            return "";
        }
        log.info("[结账单][getChargeContent]additionalChargeList={}", JacksonUtils.writeValueAsString(additionalChargeList));
        BigDecimal chargeValueSum = additionalChargeList.stream()
                .map(AdditionalCharge::getChargeValue)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
        String chargeContent = "";
        if (chargeValueSum.compareTo(BigDecimal.ZERO) > 0) {
            // 附加费明细
            StringBuilder chargeDetailContent = new StringBuilder();
            for (AdditionalCharge item : additionalChargeList) {
                BigDecimal number = BigDecimal.ONE;
                if (Objects.isNull(item.getUnitValue())) {
                    item.setUnitValue(item.getChargeValue());
                }
                if (item.getUnitValue().compareTo(BigDecimal.ZERO) > 0) {
                    number = item.getChargeValue().divide(item.getUnitValue(), 2, RoundingMode.DOWN);
                }
                String chargeName = item.getChargeName();
                String priceStr = BigDecimalUtils.moneyTrimmedString(item.getUnitValue());
                priceStr = CloudPrinterUtils.addSpaceAfterByLength(priceStr, CloudPrinterUtils.PRICE + 1);
                String numberStr = BigDecimalUtils.quantityTrimmedString(number);
                numberStr = CloudPrinterUtils.addSpaceBeforeByLength(numberStr, CloudPrinterUtils.QUANTITY * 2);
                String chargeValueStr = BigDecimalUtils.moneyTrimmedString(item.getChargeValue());
                chargeValueStr = CloudPrinterUtils.addSpaceBeforeByLength(chargeValueStr, CloudPrinterUtils.ITEM_SUM + 1);
                String chargeOtherStr = priceStr + numberStr + chargeValueStr;
                String interval = CloudPrinterUtils.handleInterval(chargeName, chargeOtherStr, CloudPrinterUtils.LINE_SIZE_SMALL);
                chargeDetailContent.append(interval);
            }
            chargeContent += chargeDetailContent;

            // 附加费合计
            chargeContent = getChargeTotalContent(chargeValueSum, chargeContent);
        }
        return chargeContent;
    }

    @NotNull
    private static String getChargeTotalContent(BigDecimal chargeValueSum, String chargeContent) {
        String chargeSumTitleStr = MultiLangUtils.get(Constant.SURCHARGE_TOTAL);
        String chargeSumStr = BigDecimalUtils.moneyTrimmedString(chargeValueSum);
        String interval = CloudPrinterUtils.handleInterval(chargeSumTitleStr, chargeSumStr, CloudPrinterUtils.LINE_SIZE_SMALL);
        chargeContent = chargeContent + interval;
        return chargeContent;
    }

    /**
     * 商品、单价、数量、小计 商品合计
     */
    private static String getItemContent(List<PrintItemRecord> itemRecordList, BigDecimal total) {
        if (CollectionUtils.isEmpty(itemRecordList)) {
            // 允许菜品列表为空，强校验交给使用方来控制
            return "";
        }
        StringBuilder itemContent = new StringBuilder();
        String itemTitle = getItemTitle();
        itemContent.append(itemTitle);
        if (!CollectionUtils.isEmpty(itemRecordList)) {
            for (PrintItemRecord itemRecord : itemRecordList) {
                String itemNameStr = getItemNameStr(itemRecord);
                // 菜品单价、数量、小计
                String itemOtherStr = getItemOtherStr(itemRecord);
                String itemStr = CloudPrinterUtils.handleInterval(itemNameStr, itemOtherStr, CloudPrinterUtils.LINE_SIZE_SMALL);
                itemStr = handleItem(itemRecord, itemStr);
                itemStr = FeiePrinterUtils.addHeightenOnce(itemStr);
                itemContent.append(itemStr);
            }
        }

        // 商品合计
        String itemTotalStr = getItemTotalStr(itemRecordList, total);
        itemContent.append(itemTotalStr);

        return itemContent.toString();
    }

    @NotNull
    private static String getItemTotalStr(List<PrintItemRecord> itemRecordList, BigDecimal total) {

        String itemTotalStr = CloudPrinterUtils.addSpaceAfterByLength(MultiLangUtils.get(Constant.ITEM_PRICE_TOTAL),
                CloudPrinterUtils.ITEM + CloudPrinterUtils.PRICE);
        BigDecimal itemNumTotal = itemRecordList.stream()
                .map(PrintItemRecord::getNumber)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        String numberStr = BigDecimalUtils.quantityTrimmedString(itemNumTotal);
        itemTotalStr += CloudPrinterUtils.addSpaceBeforeByLength(numberStr, CloudPrinterUtils.QUANTITY * 2);
        String totalStr = BigDecimalUtils.moneyTrimmedString(total);
        itemTotalStr = CloudPrinterUtils.handleInterval(itemTotalStr, totalStr, CloudPrinterUtils.LINE_SIZE_SMALL);
        return FeiePrinterUtils.addLineBreak(itemTotalStr);
    }

    @NotNull
    private static String handleItem(PrintItemRecord itemRecord, String itemStr) {
        // 属性
        String itemProperty = getItemProperty(itemRecord.getProperty());
        itemStr += itemProperty;
        // 商品备注
        String itemRemark = getItemRemark(itemRecord.getRemark());
        itemStr += itemRemark;
        // 套餐子菜
        if (!CollectionUtils.isEmpty(itemRecord.getSubItemRecords())) {
            StringBuilder itemStrBuilder = new StringBuilder(itemStr);
            for (PrintItemRecord subPrintItemRecord : itemRecord.getSubItemRecords()) {
                String subNumberStr = BigDecimalUtils.quantityTrimmedString(subPrintItemRecord.getNumber());
                subNumberStr = CloudPrinterUtils.addSpaceAfterByLength(subNumberStr, CloudPrinterUtils.QUANTITY + CloudPrinterUtils.ITEM_SUM + 1);
                String subItemName = " -|" + subPrintItemRecord.getItemName();
                itemStrBuilder.append(CloudPrinterUtils.handleInterval(subItemName, subNumberStr, CloudPrinterUtils.LINE_SIZE_SMALL));
                itemStrBuilder.append(getSubItemProperty(subPrintItemRecord.getProperty()));
            }
            itemStr = itemStrBuilder.toString();
        }
        return itemStr;
    }

    /**
     * 商品属性
     */
    private static String getItemProperty(String property) {
        if (StringUtils.isEmpty(property)) {
            return "";
        }
        String itemProperty = " [" + MultiLangUtils.get(Constant.ATTR) + property + "]";
        return FeiePrinterUtils.addLineBreak(itemProperty);
    }

    /**
     * 商品备注
     */
    private static String getItemRemark(String remark) {
        if (StringUtils.isEmpty(remark)) {
            return "";
        }
        String itemRemark = " [" + MultiLangUtils.get(Constant.REMARK) + remark + "]";
        return FeiePrinterUtils.addLineBreak(itemRemark);
    }

    /**
     * 子商品属性
     */
    private static String getSubItemProperty(String property) {
        if (StringUtils.isEmpty(property)) {
            return "";
        }
        String itemProperty = "   [" + MultiLangUtils.get(Constant.ATTR) + property + "]";
        return FeiePrinterUtils.addLineBreak(itemProperty);
    }

    /**
     * 商品名称
     */
    private static String getItemNameStr(PrintItemRecord itemRecord) {
        // 商品加标签
        String hangUp = "";
        if (Objects.nonNull(itemRecord.getItemState())) {
            hangUp = itemRecord.getItemState().equals(ItemStateEnum.HANG_UP.getCode()) ? MultiLangUtils.get(Constant.HANG) : "";
        }
        String ifPackage = "";
        if (Boolean.TRUE.equals(itemRecord.getAsPackage())) {
            ifPackage = MultiLangUtils.get(Constant.SET);
        }
        String ifWeight = "";
        if (Boolean.TRUE.equals(itemRecord.getAsWeight())) {
            ifWeight = MultiLangUtils.get(Constant.WEIGHT);
        }
        String giftName = "";
        if (Boolean.TRUE.equals(itemRecord.getAsGift())) {
            giftName = MultiLangUtils.get(Constant.COMPL);
        }
        String itemName = itemRecord.getItemName().replace("\n", " ");
        itemName = itemName.replace("\\n", " ");
        itemName = hangUp + ifPackage + ifWeight + itemName + giftName;
        return itemName;
    }

    /**
     * 商品、单价、数量、小计
     * 9,6,2,6
     */
    private static String getItemTitle() {
        String item = CloudPrinterUtils.addSpaceAfterByLength(MultiLangUtils.get(Constant.ITEM), CloudPrinterUtils.ITEM);
        String price = CloudPrinterUtils.addSpaceAfterByLength(MultiLangUtils.get(Constant.PRICE), CloudPrinterUtils.PRICE);
        String quantity = CloudPrinterUtils.addSpaceAfterByLength(MultiLangUtils.get(Constant.QUANTITY), CloudPrinterUtils.QUANTITY);
        String itemSum = CloudPrinterUtils.addSpaceBeforeByLength(MultiLangUtils.get(Constant.ITEM_SUM), CloudPrinterUtils.ITEM_SUM);
        String itemTitle = item + price + quantity + itemSum;
        itemTitle = FeiePrinterUtils.addHeightenOnce(itemTitle);
        itemTitle = FeiePrinterUtils.addLineBreak(itemTitle);
        return itemTitle;
    }

    /**
     * 打印时间
     */


    /**
     * 下单时间
     */


    /**
     * 结账时间
     */
    private static String getCheckOutTime(Long checkOutTimeNum) {
        String checkOutTimeStr = DateTimeUtils.mills2String(checkOutTimeNum, Constant.MM_DD_HH_MM);
        String checkOutTimeTime = MultiLangUtils.get(Constant.CHECKOUT_TIME) + checkOutTimeStr;
        return FeiePrinterUtils.addLineBreak(checkOutTimeTime);
    }

    /**
     * 操作员
     */


    /**
     * 人数
     */
    private static String getPersonNumber(Integer personNumber) {
        String format = MultiLangUtils.get(Constant.CUSTOMER_NUMBER) + personNumber;
        return FeiePrinterUtils.addLineBreak(format);
    }

    /**
     * 备注
     */
    private static String getOrderRemark(String orderRemark) {
        if (StringUtils.isEmpty(orderRemark)) {
            return "";
        }
        String mark = MultiLangUtils.get(Constant.ORDER_REMARK) + orderRemark;
        mark = FeiePrinterUtils.addMagnifyOnceBold(mark);
        return FeiePrinterUtils.addLineBreak(mark);
    }

    /**
     * 订单号
     */
    private static String getOrderNo(String orderNo) {
        String orderNumber = MultiLangUtils.get(Constant.ORDER_NUMBER) + orderNo;
        return FeiePrinterUtils.addLineBreak(orderNumber);
    }

    /**
     * 牌号
     */
    private static String getTrademark(String markNo) {
        String trademark = MultiLangUtils.get(Constant.MARK_NAME) + markNo;
        trademark = FeiePrinterUtils.addMagnifyOnceBold(trademark);
        return FeiePrinterUtils.addLineBreak(trademark);
    }

    /**
     * 门店
     */
    private static String getStoreName(@NotBlank(message = "门店名不能为空") String storeName) {
        storeName = FeiePrinterUtils.addCenterMagnify(storeName);
        storeName = FeiePrinterUtils.addLineBreak(storeName);
        return storeName;
    }

    /**
     * 标题
     */
    private static String getTitle() {
        String title = MultiLangUtils.get(Constant.CHECKOUT_INVOICE_HEADER);
        title = FeiePrinterUtils.addCenterMagnify(title);
        title = FeiePrinterUtils.addLineBreak(title);
        return title;
    }

    /**
     * 菜品单价、数量、小计
     */
    @NotNull
    private static String getItemOtherStr(PrintItemRecord itemRecord) {
        Pair<BigDecimal, BigDecimal> pair = getPricePair(itemRecord);
        BigDecimal tempPrice = pair.getKey();
        BigDecimal tempSubTotal = pair.getValue();
        // 商品单价
        String itemPriceStr = BigDecimalUtils.moneyTrimmedString(tempPrice);
        itemPriceStr = CloudPrinterUtils.addSpaceAfterByLength(itemPriceStr, CloudPrinterUtils.PRICE + 1);
        // 商品数量
        String itemNumberStr = getNumberStr(itemRecord);
        // 商品小计
        String itemTotalMoneyStr = BigDecimalUtils.moneyTrimmedString(tempSubTotal);
        itemTotalMoneyStr = CloudPrinterUtils.addSpaceBeforeByLength(itemTotalMoneyStr, CloudPrinterUtils.ITEM_SUM + 1);
        return itemPriceStr + itemNumberStr + itemTotalMoneyStr;
    }

    /**
     * 菜品单价、小计
     *
     * @param itemRecord 商品详情
     * @return 菜品单价, 小计
     */
    private static Pair<BigDecimal, BigDecimal> getPricePair(PrintItemRecord itemRecord) {
        BigDecimal tempPrice;
        BigDecimal tempSubTotal;
        BigDecimal itemPrice = Objects.requireNonNull(itemRecord.getPrice(), "商品价格不得为空");
        if (Boolean.TRUE.equals(itemRecord.getAsWeight())) {
            BigDecimal propertyPrice = BigDecimalUtils.nonNullValue(itemRecord.getPropertyPrice());
            tempPrice = itemPrice.add(propertyPrice);
            tempSubTotal = itemPrice.multiply(itemRecord.getNumber()).add(propertyPrice);
        } else if (Boolean.TRUE.equals(itemRecord.getAsPackage())) {
            BigDecimal propertyPrice = BigDecimalUtils.nonNullValue(itemRecord.getPropertyPrice());
            BigDecimal ingredientPrice = BigDecimalUtils.nonNullValue(itemRecord.getIngredientPrice());
            tempPrice = itemPrice.add(propertyPrice).add(ingredientPrice);
            if (!CollectionUtils.isEmpty(itemRecord.getSubItemRecords())) {
                for (PrintItemRecord subPrintItemRecord : itemRecord.getSubItemRecords()) {
                    BigDecimal subPropertyPrice = BigDecimalUtils.nonNullValue(subPrintItemRecord.getPropertyPrice());
                    BigDecimal subIngredientPrice = BigDecimalUtils.nonNullValue(subPrintItemRecord.getIngredientPrice());
                    if (Boolean.TRUE.equals(subPrintItemRecord.getAsWeight())) {
                        tempPrice = tempPrice.add(subPropertyPrice.multiply(subPrintItemRecord.getNumber()));
                        tempPrice = tempPrice.add(subIngredientPrice.multiply(subPrintItemRecord.getNumber()));
                    } else {
                        tempPrice = tempPrice.add(subPropertyPrice.multiply(subPrintItemRecord.getNumber()));
                        BigDecimal pkgDefaultCnt = Optional.ofNullable(subPrintItemRecord.getPkgCnt()).orElse(BigDecimal.ONE);
                        BigDecimal pkgSelectCount = subPrintItemRecord.getNumber().divide(pkgDefaultCnt, RoundingMode.HALF_UP);
                        tempPrice = tempPrice.add(subIngredientPrice.multiply(pkgSelectCount));
                    }
                }
            }
            tempSubTotal = tempPrice.multiply(itemRecord.getNumber());
        } else {
            tempPrice = itemPrice.add(BigDecimalUtils.nonNullValue(itemRecord.getPropertyPrice()));
            tempSubTotal = tempPrice.multiply(itemRecord.getNumber());
        }
        return new Pair<>(tempPrice, tempSubTotal);
    }

    @NotNull
    private static String getNumberStr(PrintItemRecord itemRecord) {
        BigDecimal number = itemRecord.getNumber();
        String numberStr = number.stripTrailingZeros().toPlainString();
        return CloudPrinterUtils.addSpaceBeforeByLength(numberStr, CloudPrinterUtils.QUANTITY * 2);
    }

}
