package com.holder.saas.print.template.retail;

import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.template.row.composite.PrintLayoutUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.print.content.nested.PayDetailRecord;
import com.holderzone.saas.store.dto.print.content.retail.PrintRetailOpStatsDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OpStatsTemplate
 * @date 2018/02/14 09:00
 * @description 营业概况模板
 * @program holder-saas-store-print
 */
public class RetailOpStatsTemplate extends AbsPrintTemplate<PrintRetailOpStatsDTO, FormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintRetailOpStatsDTO printDTO = getPrintDTO();
        List<PrintRow> printRows = new ArrayList<>();

        // 门店名
        PrintRowUtils.add(printRows, new Section()
                .addText(printDTO.getStoreName(), Font.NORMAL_BOLD)
                .setAlign(Text.Align.Center));

        // 票据类型
        PrintRowUtils.add(printRows, new Section()
                .addText("**营业概况**", Font.NORMAL)
                .setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString("开始时间：")
                .setValueString(DateTimeUtils.mills2String(printDTO.getStartTime(), "yyyy-MM-dd HH:mm")));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString("结束时间：")
                .setValueString(DateTimeUtils.mills2String(printDTO.getEndTime(), "yyyy-MM-dd HH:mm")));

        PrintLayoutUtils.addStats(printDTO.getInOutRecordList(), getPageSize(), printRows);

        BigDecimal salesAmountTotal = BigDecimal.ZERO;
        BigDecimal refundAmountTotal = BigDecimal.ZERO;
        BigDecimal actuallyAmountTotal = BigDecimal.ZERO;

        List<PayDetailRecord> payDetailRecordList = printDTO.getPayDetailRecordList();
        if (!CollectionUtils.isEmpty(payDetailRecordList)) {
            for (PayDetailRecord payDetailRecord : payDetailRecordList) {
                salesAmountTotal = salesAmountTotal.add(payDetailRecord.getSalesAmount());
                refundAmountTotal = refundAmountTotal.add(payDetailRecord.getRefundAmount());
                actuallyAmountTotal = actuallyAmountTotal.add(payDetailRecord.getActuallyAmount());
                PrintRowUtils.add(printRows, new Section(payDetailRecord.getPayName()));
                if (58 == getPageSize()) {
                    PrintRowUtils.add(printRows, new Section("    销售金额：" + BigDecimalUtils.moneyTrimmedString(payDetailRecord.getSalesAmount())));
                    PrintRowUtils.add(printRows, new Section("    退货金额：" + BigDecimalUtils.moneyTrimmedString(payDetailRecord.getRefundAmount())));
                } else {
                    PrintRowUtils.add(printRows, new KeyValue()
                            .setKeyString("    销售金额：" + BigDecimalUtils.moneyTrimmedString(payDetailRecord.getSalesAmount()))
                            .setValueString("退货金额：" + BigDecimalUtils.moneyTrimmedString(payDetailRecord.getRefundAmount()) + "    "));
                }
                PrintRowUtils.add(printRows, new Section("    实收金额：" + BigDecimalUtils.moneyTrimmedString(payDetailRecord.getActuallyAmount())));
            }
            PrintRowUtils.add(printRows, new Separator());
        }

        PrintRowUtils.add(printRows, new Section("收支合计"));
        if (58 == getPageSize()) {
            PrintRowUtils.add(printRows, new Section("    销售金额：" + BigDecimalUtils.moneyTrimmedString(salesAmountTotal)));
            PrintRowUtils.add(printRows, new Section("    退货金额：" + BigDecimalUtils.moneyTrimmedString(refundAmountTotal)));
        } else {
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString("    销售金额：" + BigDecimalUtils.moneyTrimmedString(salesAmountTotal))
                    .setValueString("退货金额：" + BigDecimalUtils.moneyTrimmedString(refundAmountTotal) + "    "));
        }
        PrintRowUtils.add(printRows, new Section("    实收金额：" + BigDecimalUtils.moneyTrimmedString(actuallyAmountTotal)));

        PrintRowUtils.add(printRows, new Separator());

        PrintLayoutUtils.addOpStaffAndPrintTime(getPageSize(), printDTO.getOperatorStaffName(), printDTO.getCreateTime(), printRows);

        return printRows;
    }

    @Override
    public String getFailedMsg() {
        return "营业概况单打印失败，请及时处理";
    }
}
