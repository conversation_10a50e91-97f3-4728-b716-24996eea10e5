package com.holder.saas.print.template;

import com.holder.saas.print.entity.biz.ItemTableFormatBO;
import com.holder.saas.print.template.base.AbsFrontItemTemplate;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.composite.PrintFormatLayoutContainer;
import com.holderzone.saas.store.dto.print.content.PrintPreCheckoutDTO;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.format.PreCheckoutFormatDTO;
import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.enums.print.TradeModeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PreCheckoutTemplate
 * @date 2018/02/14 09:00
 * @description 预结单模板
 * @program holder-saas-store-print
 */
@Slf4j
public class PreCheckoutTemplate extends AbsFrontItemTemplate<PrintPreCheckoutDTO, PreCheckoutFormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintPreCheckoutDTO printDTO = getPrintDTO();
        PreCheckoutFormatDTO formatDTO = getFormatDTO();
        PrintFormatLayoutContainer container = new PrintFormatLayoutContainer();

        // 门店名
        container.addStoreName(printDTO.getStoreName(), formatDTO.getStoreName());

        // 票据类型
        container.addInvoiceType(MultiLangUtils.get("pre_bill_invoice_header"), formatDTO.getInvoiceName());

        // 整单备注
        container.addOrderRemark(printDTO.getOrderRemark(), formatDTO.getOrderRemark());

        // 牌号
        // 订单号、人数
        container.addMarkOrTableNoAndOrderAndGuest(
                getPageSize(), TradeModeEnum.DINE.getMode(),
                printDTO.getMarkNo(), formatDTO.getMarkNo(),
                printDTO.getOrderNo(), formatDTO.getOrderNo(),
                printDTO.getPersonNumber(), formatDTO.getPersonNumber());

        // 商品列表排序
        List<PrintItemRecord> sortedPrintItemRecords = sortedItemRecordList(printDTO.getItemRecordList());

        // 菜品、商品总额
        ItemTableFormatBO itemTableFormatBO = ItemTableFormatBO.of(formatDTO);
        itemTableFormatBO.setDuplicatePackage(true);
        container.addTableItem(sortedPrintItemRecords, printDTO.getTotal(), getPageSize(), itemTableFormatBO);

        // 附加费明细
        // 附加费合计
        container.addTableAdditionalCharge(printDTO.getAdditionalChargeList(), getPageSize(), ItemTableFormatBO.of(formatDTO));

        // 优惠明细
        // 优惠合计
        // 应付金额
        container.addReduceRecordAndPayable(printDTO.getReduceRecordList(),
                formatDTO.getReduceRecord(), formatDTO.getReduceRecordTotal(),
                printDTO.getPayAble(), formatDTO.getPayableMoney(), null
                , null);

        // 支付方式
        // 实付金额
        container.addPrePayRecordAndActuallyPay(
                printDTO.getPayRecordList(), formatDTO.getReduceRecord(), printDTO.getPayAble(),
                printDTO.getActuallyPay(), formatDTO.getPayableMoney());

        // 操作员、下单时间
        // 打印时间
        container.addOpStaffAndOpenTableTimeAndPrintTime(
                getPageSize(),
                printDTO.getOperatorStaffName(), formatDTO.getOperator(),
                printDTO.getOpenTableTime(), formatDTO.getOpenTableTime(),
                printDTO.getCreateTime(), formatDTO.getPrintTime(), formatDTO.getYearMonthDay());
        //支付二维码
        container.addQrCode(printDTO.getPayQrCode(), formatDTO.getPayQrCode());
        return container.getPrintRows();
    }

    /**
     * 商品列表排序
     */
    private List<PrintItemRecord> sortedItemRecordList(List<PrintItemRecord> preCheckoutItemRecordList) {
        if (CollectionUtils.isEmpty(preCheckoutItemRecordList)) {
            return preCheckoutItemRecordList;
        }
        PreCheckoutFormatDTO formatDTO = getFormatDTO();
        FormatMetadata typeTotal = formatDTO.getTypeTotal();
        if (typeTotal.isEnable()) {
            return preCheckoutItemRecordList;
        }
        // 排序
        return preCheckoutItemRecordList.stream()
                .sorted(Comparator.comparing(PrintItemRecord::getOrderItemGuid))
                .collect(Collectors.toList());
    }

    @Override
    public String getFailedMsg() {
        return "预结单 [" + getPrintDTO().getMarkNo() + "]号订单打印失败，请及时处理";
    }
}
