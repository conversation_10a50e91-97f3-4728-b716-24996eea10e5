package com.holder.saas.print.template;

import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.template.label.PrintLabelUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.print.content.PrintLabelDTO;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LabelTemplate
 * @date 2018/02/14 09:00
 * @description 标签单模板
 * @program holder-saas-store-print
 */
public class LabelTemplate extends AbsPrintTemplate<PrintLabelDTO, FormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintLabelDTO printDTO = getPrintDTO();
        int pageSize = getPageSize();

        List<PrintRow> printRows = new ArrayList<>();

        // 牌号
        String key = printDTO.getSerialNumber();
        String value = printDTO.getCurrentNo() + "/" + printDTO.getTotalNo();
        PrintLabelUtils.addKeyValueAsCoordinateRow(printRows, key, value, pageSize, 1);

        // 分割线
        PrintLabelUtils.addSeparatorAsSection(printRows, "-", pageSize, 1);

        // 菜品信息
        if (!CollectionUtils.isEmpty(printDTO.getItemRecordList())) {
            for (PrintItemRecord printItemRecord : printDTO.getItemRecordList()) {
                // 菜品价格
                // 菜品名字
                PrintLabelUtils.addSection(printRows, printItemRecord.getItemName(), pageSize, 1);

                // 菜品属性、备注
                addPropAndRemark(printRows, printItemRecord.getProperty(), printItemRecord.getRemark(), pageSize);
                // 套餐子菜品名字
                if (!CollectionUtils.isEmpty(printItemRecord.getSubItemRecords())) {
                    for (PrintItemRecord subPrintItemRecord : printItemRecord.getSubItemRecords()) {
                        PrintLabelUtils.addSection(printRows, "  -|" + subPrintItemRecord.getItemName(), pageSize, 1);
                        // 套餐子菜品属性、备注
                        addPropAndRemark(printRows, subPrintItemRecord.getProperty(), subPrintItemRecord.getRemark(), pageSize);
                    }
                }
                // 菜品价格
                if (ObjectUtils.isEmpty(printDTO.getPrintPriceType()) || printDTO.getPrintPriceType()) {
                    String keyText = "";
                    String valueText = " " + BigDecimalUtils.moneyTrimmedString(Objects.requireNonNull(printItemRecord.getPrice()));
                    PrintLabelUtils.addKeyValueAsCoordinateRow(printRows, keyText, valueText, pageSize, 1);
                }
            }
        }
        //打印时间
        long timeNow = DateTimeUtils.nowMillis();
        String printDate = DateTimeUtils.mills2String(timeNow, "yyyyMMdd");
        String printTime = DateTimeUtils.mills2String(timeNow, "HH:mm:ss");
        PrintLabelUtils.addKeyValueAsCoordinateRow(printRows, printDate, printTime, pageSize, 1);
        return printRows;
    }

    private void addPropAndRemark(List<PrintRow> printRows, String property, String remark, int pageSize) {
        StringBuilder propRemarkSb = new StringBuilder();
        if (!StringUtils.isEmpty(property)) {
            propRemarkSb.append(property);
        }
        if (!StringUtils.isEmpty(remark)) {
            propRemarkSb.append("，");
            propRemarkSb.append(remark);
        }
        String propRemark = propRemarkSb.toString();
        if (!StringUtils.isEmpty(propRemark)) {
            PrintLabelUtils.addSection(printRows, propRemark, pageSize, 1);
        }
    }

    @Override
    public String getFailedMsg() {
        return "标签单打印失败，请及时处理";
    }
}
