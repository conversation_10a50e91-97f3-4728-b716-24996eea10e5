package com.holder.saas.print.template.base;

import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;

public abstract class AbsPrintTemplate<T extends PrintDTO, F extends FormatDTO>
        implements PrintTemplateAware<T, F>, PrintTemplateBuilder<T, F> {

    private int pageSize;

    private T printDTO;

    private F formatDTO;

    @Override
    public int getPageSize() {
        return pageSize;
    }

    @Override
    public T getPrintDTO() {
        return printDTO;
    }

    @Override
    public F getFormatDTO() {
        return formatDTO;
    }

    @Override
    public String getSucceedMsg() {
        return null;
    }

    @Override
    public List<PrintRow> getPrintRows() {
        List<PrintRow> printRows = new ArrayList<>();
        printRows.addAll(getHeader());
        printRows.addAll(getContent());
        printRows.addAll(getFooter());
        return printRows;
    }

    @Override
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public void setPrintDTO(T printDTO) {
        this.printDTO = printDTO;
    }

    @Override
    public void setPrintDTO(T t, Consumer<T> consumer) {
        consumer.accept(t);
        this.printDTO = t;
    }

    @Override
    public void setFormatDTO(F f) {
        this.formatDTO = f;
    }

    @Override
    public void setFormatDTO(F f, Consumer<F> consumer) {
        consumer.accept(f);
        this.formatDTO = f;
    }

    @Override
    public PrintTemplate<T, F> build() {
        return this;
    }

    @Override
    public PrintTemplateAware<T, F> buildAware() {
        return this;
    }

    public List<PrintRow> getHeader() {
        return Collections.emptyList();
    }

    public List<PrintRow> getFooter() {
        return Collections.emptyList();
    }

    public abstract List<PrintRow> getContent();
}