package com.holder.saas.print.template;

import com.holder.saas.print.entity.Constant;
import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holder.saas.print.utils.template.row.composite.PrintLayoutUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.print.content.PrintConsumeStatsDTO;
import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import com.holderzone.saas.store.util.EncryptionSymbolUtil;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemStatsTemplate
 * @date 2018/02/14 09:00
 * @description 会员销售统计模板
 * @program holder-saas-store-print
 */
public class MemConsuTemplate extends AbsPrintTemplate<PrintConsumeStatsDTO, FormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintConsumeStatsDTO printDTO = getPrintDTO();
        List<PrintRow> printRows = new ArrayList<>();

        // 门店名
        PrintRowUtils.add(printRows, new Section()
                .addText(printDTO.getStoreName(), Font.NORMAL_BOLD)
                .setAlign(Text.Align.Center));

        // 票据类型
        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get("member_consumption_statistics_invoice_header"), Font.NORMAL)
                .setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("start_time"))
                .setValueString(DateTimeUtils.mills2String(printDTO.getStartTime(), "yyyy-MM-dd HH:mm:ss")));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("end_time"))
                .setValueString(DateTimeUtils.mills2String(printDTO.getEndTime(), "yyyy-MM-dd HH:mm:ss")));

        PrintRowUtils.add(printRows, new Separator());

        // 消费单数
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("number_of_consumption_orders"))
                .setValueString(getConsumeQuantity()));

        // 消费人数
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get(Constant.CONSUMERS))
                .setValueString(getConsumeMemberNum()));

        // 消费金额
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(MultiLangUtils.get("total_consumption_amount"))
                .setValueString(getConsumeTotal()));

        PrintRowUtils.add(printRows, new Separator());

        // 消费金额权限
        boolean isConsumeTotalEncryption = EncryptionSymbolUtil.isEncryption(printDTO.getConsumeTotalStr());
        if (!isConsumeTotalEncryption && !CollectionUtils.isEmpty(printDTO.getConsumeDetail())) {
            String prefix = 80 == getPageSize() ? "————" : "——";
            for (PayRecord payRecord : printDTO.getConsumeDetail()) {
                PrintRowUtils.add(printRows, new KeyValue()
                        .setKeyString(prefix + payRecord.getPayName())
                        .setValueString(BigDecimalUtils.moneyTrimmedString(payRecord.getAmount())));
                if (Objects.nonNull(payRecord.getExcessAmount()) && payRecord.getExcessAmount().compareTo(BigDecimal.ZERO) > 0) {
                    PrintRowUtils.add(printRows, new KeyValue()
                            .setKeyString("")
                            .setValueString("(" + MultiLangUtils.get("remaining_balance") + BigDecimalUtils.moneyTrimmedString(payRecord.getExcessAmount()) + ")"));
                }
            }
            PrintRowUtils.add(printRows, new Separator());
        }

        PrintLayoutUtils.addOpStaffAndPrintTime(getPageSize(), printDTO.getOperatorStaffName(), printDTO.getCreateTime(), printRows);

        return printRows;
    }


    /**
     * 获取消费单数
     */
    private String getConsumeQuantity() {
        PrintConsumeStatsDTO printDTO = getPrintDTO();
        return EncryptionSymbolUtil.isEncryption(printDTO.getConsumeQuantityStr()) ?
                printDTO.getConsumeQuantityStr() :
                printDTO.getConsumeQuantity() + "/" + MultiLangUtils.get(Constant.TRANSACTIONS);
    }

    /**
     * 获取消费人数
     */
    private String getConsumeMemberNum() {
        PrintConsumeStatsDTO printDTO = getPrintDTO();
        return EncryptionSymbolUtil.isEncryption(printDTO.getConsumeMemberNumStr()) ?
                printDTO.getConsumeMemberNumStr() :
                printDTO.getConsumeMemberNum() + "/" + MultiLangUtils.get(Constant.PERSON);
    }


    /**
     * 获取消费金额
     */
    private String getConsumeTotal() {
        PrintConsumeStatsDTO printDTO = getPrintDTO();
        return EncryptionSymbolUtil.isEncryption(printDTO.getConsumeTotalStr()) ?
                printDTO.getConsumeTotalStr() :
                BigDecimalUtils.moneyTrimmedString(printDTO.getConsumeTotal());
    }

    @Override
    public String getFailedMsg() {
        return "会员消费统计单打印失败，请及时处理";
    }
}
