package com.holder.saas.print.template;

import com.holder.saas.print.template.base.AbsPrintTemplate;
import com.holder.saas.print.utils.BigDecimalUtils;
import com.holder.saas.print.utils.MultiLangUtils;
import com.holder.saas.print.utils.template.row.composite.PrintLayoutUtils;
import com.holder.saas.print.utils.template.row.PrintRowUtils;
import com.holder.saas.print.utils.template.row.composite.table.PrintTableUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.print.content.PrintItemStatsDTO;
import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Table;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.util.EncryptionSymbolUtil;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemStatsTemplate
 * @date 2018/02/14 09:00
 * @description 商品销售统计、退菜统计、赠菜统计模板
 * @program holder-saas-store-print
 */
public class ItemStatsTemplate extends AbsPrintTemplate<PrintItemStatsDTO, FormatDTO> {

    @Override
    public List<PrintRow> getContent() {
        PrintItemStatsDTO printDTO = getPrintDTO();
        List<PrintRow> printRows = new ArrayList<>();

        // 门店名
        PrintRowUtils.add(printRows, new Section()
                .addText(printDTO.getStoreName(), Font.NORMAL_BOLD)
                .setAlign(Text.Align.Center));

        // 票据类型：商品销售统计、退菜统计、赠菜统计
        PrintRowUtils.add(printRows, new Section()
                .addText(MultiLangUtils.get(InvoiceTypeEnum.ofType(printDTO.getInvoiceType()).name().toLowerCase()), Font.NORMAL)
                .setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("start_time"))
                .setValueString(DateTimeUtils.mills2String(printDTO.getStartTime(), "yyyy-MM-dd HH:mm:ss")));

        PrintRowUtils.add(printRows, new KeyValue()
                .setAlignEdges(false)
                .setKeyString(MultiLangUtils.get("end_time"))
                .setValueString(DateTimeUtils.mills2String(printDTO.getEndTime(), "yyyy-MM-dd HH:mm:ss")));

        buildItemList(printDTO, printRows);

        PrintLayoutUtils.addOpStaffAndPrintTime(getPageSize(), printDTO.getOperatorStaffName(), printDTO.getCreateTime(), printRows);

        return printRows;
    }


    /**
     * 商品表单构建
     */
    private void buildItemList(PrintItemStatsDTO printDTO, List<PrintRow> printRows) {
        List<PrintItemStatsDTO.Item> itemList = printDTO.getItemList();
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        Table table = new Table(
                Arrays.asList(new Text(MultiLangUtils.get("product_name")), new Text(MultiLangUtils.get("quantity")), new Text(MultiLangUtils.get("amount"))),
                PrintTableUtils.getStatsItemColWidthList(getPageSize()),
                Arrays.asList(false, false, true),
                false
        );
        boolean isNameEncryption = EncryptionSymbolUtil.isEncryption(itemList.get(0).getName());
        boolean isQuantityEncryption = EncryptionSymbolUtil.isEncryption(itemList.get(0).getQuantityStr());
        boolean isMoneyEncryption = EncryptionSymbolUtil.isEncryption(itemList.get(0).getMoneyStr());
        for (PrintItemStatsDTO.Item item : itemList) {
            String itemName = (item.getIsPackage() ? MultiLangUtils.get("set") : "") + (item.getIsWeight() ? MultiLangUtils.get("weight") : "") + item.getName();
            table.addRow(Arrays.asList(
                    new Text(isNameEncryption ? item.getName() : itemName),
                    new Text(isQuantityEncryption ? item.getQuantityStr() : BigDecimalUtils.quantityTrimmedString(item.getQuantity())),
                    new Text(isMoneyEncryption ? item.getMoneyStr() : BigDecimalUtils.moneyTrimmedString(item.getMoney()))
            ));
        }
        table.addRow(Collections.singletonList(new Text("-")));
        String totalQuantityStr = isQuantityEncryption ? Constant.ENCRYPTION_SYMBOL : BigDecimalUtils.quantityTrimmedString(printDTO.getItemList().stream()
                .map(PrintItemStatsDTO.Item::getQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        String totalMoneyStr = isMoneyEncryption ? Constant.ENCRYPTION_SYMBOL : BigDecimalUtils.quantityTrimmedString(printDTO.getItemList().stream()
                .map(PrintItemStatsDTO.Item::getMoney)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        table.addRow(Arrays.asList(
                new Text(MultiLangUtils.get("price_total")),
                new Text(totalQuantityStr),
                new Text(totalMoneyStr)
        ));
        PrintRowUtils.add(printRows, table);
    }

    @Override
    public String getFailedMsg() {
        PrintItemStatsDTO printDTO = getPrintDTO();
        String invoiceName = InvoiceTypeEnum.ITEM_STATS.getType().equals(printDTO.getInvoiceType()) ? "商品销售统计单"
                : InvoiceTypeEnum.ITEM_REFUND_STATS.getType().equals(printDTO.getInvoiceType()) ? "退菜统计单"
                : InvoiceTypeEnum.ITEM_GIFT_STATS.getType().equals(printDTO.getInvoiceType()) ? "赠菜统计单"
                : InvoiceTypeEnum.RETAIL_ITEM_STATS.getType().equals(printDTO.getInvoiceType()) ? "商品销售统计单"
                : InvoiceTypeEnum.RETAIL_ITEM_REFUND_STATS.getType().equals(printDTO.getInvoiceType()) ? "退货统计单"
                : InvoiceTypeEnum.RETAIL_ITEM_GIFT_STATS.getType().equals(printDTO.getInvoiceType()) ? "赠送统计单"
                : "统计单";
        return invoiceName + "打印失败，请及时处理";
    }
}
