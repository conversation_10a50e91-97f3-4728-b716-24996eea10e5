package com.holder.saas.print.mapstruct;

import com.holder.saas.print.entity.domain.PrintRecordDO;
import com.holder.saas.print.entity.query.PrintRecordQuery;
import com.holder.saas.print.entity.read.PrintRecordReadDO;
import com.holderzone.saas.store.dto.print.PrintRecordDTO;
import com.holderzone.saas.store.dto.print.PrintRecordReqDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintRecordMapstruct
 * @date 2018/02/14 09:00
 * @description 打印记录实体转换工具
 * @program holder-saas-store-print
 */
@Component
@Mapper(componentModel = "spring")
public interface PrintRecordMapstruct {

    PrintRecordDO fromPrintRecordReqDTO(PrintRecordReqDTO printRecordReqDTO);

    PrintRecordQuery printRecordReqToQuery(PrintRecordReqDTO printRecordReqDTO);

    @Mapping(target = "printContent", ignore = true)
    PrintRecordDTO mapRecordDTO(PrintRecordDO printRecordDo);

    @Mapping(target = "printContent", ignore = true)
    PrintRecordDTO mapRecordDTO(PrintRecordReadDO printRecordDo);
}
