package com.holder.saas.print.mapstruct;

import com.holder.saas.print.entity.domain.*;
import com.holderzone.saas.store.dto.print.raw.*;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper(componentModel = "spring")
public interface PrinterRawMaptstruct {

    PrinterAreaRawDTO toAreaRawDTO(PrinterAreaDO printerAreaDO);

    PrinterTableRawDTO toAreaTableRawDTO(PrinterTableDO printerTableDO);

    PrinterItemRawDTO toItemRawDTO(PrinterItemDO printerItemDO);

    PrinterInvoiceRawDTO toInvoiceRawDTO(PrinterInvoiceDO printerInvoiceDO);

    PrinterFormatRawDTO toFormatRawDTO(PrinterFormatDO printerFormatDO);

    PrinterRawDTO toFormatRawDTO(PrinterDO printerFormatDO);

    List<PrinterAreaRawDTO> toAreaRawDTO(List<PrinterAreaDO> list);

    List<PrinterTableRawDTO> toAreaTableRawDTO(List<PrinterTableDO> list);

    List<PrinterItemRawDTO> toItemRawDTO(List<PrinterItemDO> list);

    List<PrinterInvoiceRawDTO> toInvoiceRawDTO(List<PrinterInvoiceDO> list);

    List<PrinterFormatRawDTO> toFormatRawDTO(List<PrinterFormatDO> list);

    List<PrinterRawDTO> toPrinterRawDTO(List<PrinterDO> list);
}
