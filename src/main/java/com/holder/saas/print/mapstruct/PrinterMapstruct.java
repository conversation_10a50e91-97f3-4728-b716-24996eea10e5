package com.holder.saas.print.mapstruct;

import com.holder.saas.print.entity.domain.*;
import com.holder.saas.print.entity.read.PrinterReadDO;
import com.holderzone.saas.store.dto.print.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterMapstruct
 * @date 2018/02/14 09:00
 * @description 打印机实体转换工具
 * @program holder-saas-store-print
 */
@Component
@Mapper(componentModel = "spring")
public interface PrinterMapstruct {

    PrinterDO clonePrinter(PrinterDO printerDO);

    PrinterDO fromPrinterDTO(PrinterDTO printerDTO);

    PrinterDTO toPrinterDTO(PrinterDO printerDO);

    List<PrinterDTO> toPrinterDTO(List<PrinterDO> list);

    @Mappings({
            @Mapping(target = "arrayOfInvoiceDTO", source = "arrayOfInvoiceDO"),
            @Mapping(target = "arrayOfItemDTO", source = "arrayOfItemDO"),
            @Mapping(target = "arrayOfAreaDTO", source = "arrayOfAreaDO"),
            @Mapping(target = "arrayOfTableDTO", source = "arrayOfTableDO"),
    })
    PrinterDTO toPrinterDTO(PrinterReadDO printerReadDO);

    List<PrinterDTO> readToPrinterDTO(List<PrinterReadDO> printerReadDO);

    PrinterInvoiceDTO toPrinterInvoiceDTO(PrinterInvoiceDO printerInvoiceDO);

    List<PrinterInvoiceDTO> toPrinterInvoiceDTO(List<PrinterInvoiceDO> printerInvoiceDO);

    PrinterInvoiceDO fromPrinterInvoiceDTO(PrinterInvoiceDTO printerInvoiceDTO);

    List<PrinterInvoiceDO> fromPrinterInvoiceDTO(List<PrinterInvoiceDTO> printerInvoiceDTO);

    PrinterItemDO fromPrinterItemDTO(PrinterItemDTO printerItemDto);

    List<PrinterItemDO> fromPrinterItemDTO(List<PrinterItemDTO> printerItemDTO);

    PrinterItemDTO toPrinterItemDTO(PrinterItemDO printerItemDO);

    List<PrinterItemDTO> toPrinterItemDTO(List<PrinterItemDO> list);

    PrinterAreaDTO toPrinterAreaDTO(PrinterAreaDO printerAreaDO);

    List<PrinterAreaDTO> toPrinterAreaDTO(List<PrinterAreaDO> printerAreaDO);

    PrinterTableDTO toPrinterAreaTableDTO(PrinterTableDO printerTableDO);

    List<PrinterTableDTO> toPrinterAreaTableDTO(List<PrinterTableDO> printerTableDO);

    PrinterAreaDO fromPrinterAreaDTO(PrinterAreaDTO printerAreaDTO);

    List<PrinterAreaDO> fromPrinterAreaDTO(List<PrinterAreaDTO> printerAreaDTO);

    PrinterBackupsDO fromOriginalPrinterDO(PrinterDO printerDO);

    PrinterDO fromBackupsPrinterDO(PrinterBackupsDO printerBackupsDO);

    List<PrinterBackupsDO> fromOriginalPrinterDOS(List<PrinterDO> printerDOS);
}
