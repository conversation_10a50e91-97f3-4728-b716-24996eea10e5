package com.holderzone.erp.controller;

import com.holderzone.erp.service.WarehouseService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.WarehouseDTO;
import com.holderzone.saas.store.dto.erp.WarehouseQueryDTO;
import com.holderzone.saas.store.dto.erp.WarehouseReqDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;

@RunWith(SpringRunner.class)
@WebMvcTest(WarehouseController.class)
public class WarehouseControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WarehouseService mockWarehouseService;

    @Test
    public void testCreateWarehouse() throws Exception {
        // Setup
        when(mockWarehouseService.createWarehouse(any(WarehouseReqDTO.class))).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/warehouse")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testUpdateWarehouse() throws Exception {
        // Setup
        when(mockWarehouseService.updateWarehouse(any(WarehouseReqDTO.class))).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(put("/warehouse")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testUpdateStoreWarehouse() throws Exception {
        // Setup
        when(mockWarehouseService.updateStoreWarehouse(any(WarehouseReqDTO.class))).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(put("/warehouse/store")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetWarehouseByGuid() throws Exception {
        // Setup
        // Configure WarehouseService.getWarehouseByGuid(...).
        final WarehouseDTO warehouseDTO = new WarehouseDTO();
        warehouseDTO.setGuid("3ca9d50e-3cfa-4342-aee9-5a28d5abc187");
        warehouseDTO.setName("name");
        warehouseDTO.setCode("code");
        warehouseDTO.setAddr("addr");
        warehouseDTO.setPic("pic");
        when(mockWarehouseService.getWarehouseByGuid("4ed1da7e-eed5-4674-9745-89f4c1e9e6f6")).thenReturn(warehouseDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/warehouse")
                        .param("guid", "4ed1da7e-eed5-4674-9745-89f4c1e9e6f6")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetWarehouseList() throws Exception {
        // Setup
        // Configure WarehouseService.getWarehouseList(...).
        final WarehouseDTO warehouseDTO = new WarehouseDTO();
        warehouseDTO.setGuid("3ca9d50e-3cfa-4342-aee9-5a28d5abc187");
        warehouseDTO.setName("name");
        warehouseDTO.setCode("code");
        warehouseDTO.setAddr("addr");
        warehouseDTO.setPic("pic");
        final Page<WarehouseDTO> warehouseDTOPage = new Page<>(0L, 0L, Arrays.asList(warehouseDTO));
        final WarehouseQueryDTO queryDTO = new WarehouseQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setSearchConditions("searchConditions");
        when(mockWarehouseService.getWarehouseList(queryDTO)).thenReturn(warehouseDTOPage);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/warehouse/list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetWarehouseListByName() throws Exception {
        // Setup
        // Configure WarehouseService.getWarehouseListByName(...).
        final WarehouseDTO warehouseDTO = new WarehouseDTO();
        warehouseDTO.setGuid("3ca9d50e-3cfa-4342-aee9-5a28d5abc187");
        warehouseDTO.setName("name");
        warehouseDTO.setCode("code");
        warehouseDTO.setAddr("addr");
        warehouseDTO.setPic("pic");
        final List<WarehouseDTO> warehouseDTOS = Arrays.asList(warehouseDTO);
        final WarehouseQueryDTO queryDTO = new WarehouseQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setSearchConditions("searchConditions");
        when(mockWarehouseService.getWarehouseListByName(queryDTO)).thenReturn(warehouseDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/warehouse/name")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetWarehouseListByName_WarehouseServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure WarehouseService.getWarehouseListByName(...).
        final WarehouseQueryDTO queryDTO = new WarehouseQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setSearchConditions("searchConditions");
        when(mockWarehouseService.getWarehouseListByName(queryDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/warehouse/name")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testEnableOrDisableWarehouse() throws Exception {
        // Setup
        when(mockWarehouseService.enableOrDisableWarehouse("64b6d610-0da7-47f1-a5c2-9f20db39db90")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        put("/warehouse/enable/{guid}", "64b6d610-0da7-47f1-a5c2-9f20db39db90")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testEnableOrDisableWarehouse_WarehouseServiceReturnsTrue() throws Exception {
        // Setup
        when(mockWarehouseService.enableOrDisableWarehouse("64b6d610-0da7-47f1-a5c2-9f20db39db90")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        put("/warehouse/enable/{guid}", "64b6d610-0da7-47f1-a5c2-9f20db39db90")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testLockOrUnlockWarehouse() throws Exception {
        // Setup
        when(mockWarehouseService.lockOrUnlockWarehouse("972544b8-b654-4fd7-8841-4a5832380c14")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        put("/warehouse/lock/{guid}", "972544b8-b654-4fd7-8841-4a5832380c14")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testLockOrUnlockWarehouse_WarehouseServiceReturnsTrue() throws Exception {
        // Setup
        when(mockWarehouseService.lockOrUnlockWarehouse("972544b8-b654-4fd7-8841-4a5832380c14")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        put("/warehouse/lock/{guid}", "972544b8-b654-4fd7-8841-4a5832380c14")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testIsLock() throws Exception {
        // Setup
        when(mockWarehouseService.isLock("9f264ae4-a640-4e64-ac79-69dedfe0dd42")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("/warehouse/lock/{guid}", "9f264ae4-a640-4e64-ac79-69dedfe0dd42")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testIsLock_WarehouseServiceReturnsTrue() throws Exception {
        // Setup
        when(mockWarehouseService.isLock("9f264ae4-a640-4e64-ac79-69dedfe0dd42")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("/warehouse/lock/{guid}", "9f264ae4-a640-4e64-ac79-69dedfe0dd42")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDeleteWarehouse() throws Exception {
        // Setup
        when(mockWarehouseService.deleteWarehouse("62f46fe4-18e7-4acb-93da-ea5b470fb882")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        delete("/warehouse/delete/{guid}", "62f46fe4-18e7-4acb-93da-ea5b470fb882")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDeleteWarehouse_WarehouseServiceReturnsTrue() throws Exception {
        // Setup
        when(mockWarehouseService.deleteWarehouse("62f46fe4-18e7-4acb-93da-ea5b470fb882")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        delete("/warehouse/delete/{guid}", "62f46fe4-18e7-4acb-93da-ea5b470fb882")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testWarehouseCode() throws Exception {
        // Setup
        when(mockWarehouseService.warehouseCode()).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/warehouse/code")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
