package com.holderzone.erp.controller;

import com.holderzone.erp.service.CheckoutDocumentService;
import com.holderzone.erp.validate.CheckoutDocumentValidator;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(SpringRunner.class)
@WebMvcTest(CheckoutDocumentController.class)
public class CheckoutDocumentControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private CheckoutDocumentValidator mockValidator;
    @MockBean
    private CheckoutDocumentService mockCheckoutDocumentService;

    @Test
    public void testAddOrUpdateCheckoutDocument() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/checkoutDocument/addOrUpdateCheckoutDocument")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm CheckoutDocumentValidator.addOrUpdateCheckoutDocumentValidate(...).
        final CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO = new CheckoutDocumentAddOrUpdateDTO();
        addOrUpdateDTO.setUserGuid("operatorGuid");
        addOrUpdateDTO.setUserName("operatorName");
        addOrUpdateDTO.setDocumentDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        addOrUpdateDTO.setGuid("guid");
        addOrUpdateDTO.setOperatorGuid("operatorGuid");
        addOrUpdateDTO.setOperatorName("operatorName");
        verify(mockValidator).addOrUpdateCheckoutDocumentValidate(addOrUpdateDTO);

        // Confirm CheckoutDocumentService.insertCheckoutDocumentAndDetail(...).
        final CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO1 = new CheckoutDocumentAddOrUpdateDTO();
        addOrUpdateDTO1.setUserGuid("operatorGuid");
        addOrUpdateDTO1.setUserName("operatorName");
        addOrUpdateDTO1.setDocumentDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        addOrUpdateDTO1.setGuid("guid");
        addOrUpdateDTO1.setOperatorGuid("operatorGuid");
        addOrUpdateDTO1.setOperatorName("operatorName");
        verify(mockCheckoutDocumentService).insertCheckoutDocumentAndDetail(addOrUpdateDTO1);

        // Confirm CheckoutDocumentService.updateCheckoutDocumentAndDetail(...).
        final CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO2 = new CheckoutDocumentAddOrUpdateDTO();
        addOrUpdateDTO2.setUserGuid("operatorGuid");
        addOrUpdateDTO2.setUserName("operatorName");
        addOrUpdateDTO2.setDocumentDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        addOrUpdateDTO2.setGuid("guid");
        addOrUpdateDTO2.setOperatorGuid("operatorGuid");
        addOrUpdateDTO2.setOperatorName("operatorName");
        verify(mockCheckoutDocumentService).updateCheckoutDocumentAndDetail(addOrUpdateDTO2);
    }

    @Test
    public void testSelectDocumentDetailForAdd() throws Exception {
        // Setup
        // Configure CheckoutDocumentService.selectDocumentDetailForAdd(...).
        final CheckoutDocumentDetailSelectDTO checkoutDocumentDetailSelectDTO = new CheckoutDocumentDetailSelectDTO();
        checkoutDocumentDetailSelectDTO.setGuid("384ba194-e460-44f0-b8c9-a5d8dd62123b");
        checkoutDocumentDetailSelectDTO.setDocumentGuid("documentGuid");
        checkoutDocumentDetailSelectDTO.setMaterialGuid("materialGuid");
        checkoutDocumentDetailSelectDTO.setMaterialCode("materialCode");
        checkoutDocumentDetailSelectDTO.setMaterialName("materialName");
        final List<CheckoutDocumentDetailSelectDTO> checkoutDocumentDetailSelectDTOS = Arrays.asList(
                checkoutDocumentDetailSelectDTO);
        final CheckoutDocumentDetailQueryDTO queryDTO = new CheckoutDocumentDetailQueryDTO();
        queryDTO.setWarehouseGuid("warehouseGuid");
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setMaterialGuidList(Arrays.asList("value"));
        when(mockCheckoutDocumentService.selectDocumentDetailForAdd(queryDTO))
                .thenReturn(checkoutDocumentDetailSelectDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/checkoutDocument/selectDocumentDetailForAdd")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm CheckoutDocumentValidator.selectDocumentDetailForAddValidate(...).
        final CheckoutDocumentDetailQueryDTO queryDTO1 = new CheckoutDocumentDetailQueryDTO();
        queryDTO1.setWarehouseGuid("warehouseGuid");
        queryDTO1.setStoreGuid("storeGuid");
        queryDTO1.setMaterialGuidList(Arrays.asList("value"));
        verify(mockValidator).selectDocumentDetailForAddValidate(queryDTO1);
    }

    @Test
    public void testSelectDocumentDetailForAdd_CheckoutDocumentServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure CheckoutDocumentService.selectDocumentDetailForAdd(...).
        final CheckoutDocumentDetailQueryDTO queryDTO = new CheckoutDocumentDetailQueryDTO();
        queryDTO.setWarehouseGuid("warehouseGuid");
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setMaterialGuidList(Arrays.asList("value"));
        when(mockCheckoutDocumentService.selectDocumentDetailForAdd(queryDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/checkoutDocument/selectDocumentDetailForAdd")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");

        // Confirm CheckoutDocumentValidator.selectDocumentDetailForAddValidate(...).
        final CheckoutDocumentDetailQueryDTO queryDTO1 = new CheckoutDocumentDetailQueryDTO();
        queryDTO1.setWarehouseGuid("warehouseGuid");
        queryDTO1.setStoreGuid("storeGuid");
        queryDTO1.setMaterialGuidList(Arrays.asList("value"));
        verify(mockValidator).selectDocumentDetailForAddValidate(queryDTO1);
    }

    @Test
    public void testSelectDocumentAndDetailForSelect() throws Exception {
        // Setup
        // Configure CheckoutDocumentService.selectDocumentAndDetailForSelect(...).
        final CheckoutDocumentSelectDTO checkoutDocumentSelectDTO = new CheckoutDocumentSelectDTO();
        checkoutDocumentSelectDTO.setCreateStaffName("createStaffName");
        checkoutDocumentSelectDTO.setDocumentStoreGuid("documentStoreGuid");
        checkoutDocumentSelectDTO.setDocumentStoreName("documentStoreName");
        final CheckoutDocumentDetailSelectDTO checkoutDocumentDetailSelectDTO = new CheckoutDocumentDetailSelectDTO();
        checkoutDocumentDetailSelectDTO.setGuid("384ba194-e460-44f0-b8c9-a5d8dd62123b");
        checkoutDocumentSelectDTO.setDetailList(Arrays.asList(checkoutDocumentDetailSelectDTO));
        when(mockCheckoutDocumentService.selectDocumentAndDetailForSelect("documentGuid"))
                .thenReturn(checkoutDocumentSelectDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        post("/checkoutDocument/selectDocumentAndDetailForSelect")
                                .param("documentGuid", "documentGuid")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSelectDocumentAndDetailForUpdate() throws Exception {
        // Setup
        // Configure CheckoutDocumentService.selectDocument(...).
        final CheckoutDocumentSelectDTO checkoutDocumentSelectDTO = new CheckoutDocumentSelectDTO();
        checkoutDocumentSelectDTO.setCreateStaffName("createStaffName");
        checkoutDocumentSelectDTO.setDocumentStoreGuid("documentStoreGuid");
        checkoutDocumentSelectDTO.setDocumentStoreName("documentStoreName");
        final CheckoutDocumentDetailSelectDTO checkoutDocumentDetailSelectDTO = new CheckoutDocumentDetailSelectDTO();
        checkoutDocumentDetailSelectDTO.setGuid("384ba194-e460-44f0-b8c9-a5d8dd62123b");
        checkoutDocumentSelectDTO.setDetailList(Arrays.asList(checkoutDocumentDetailSelectDTO));
        when(mockCheckoutDocumentService.selectDocument("documentGuid")).thenReturn(checkoutDocumentSelectDTO);

        // Configure CheckoutDocumentService.selectDocumentDetailForUpdate(...).
        final CheckoutDocumentDetailSelectDTO checkoutDocumentDetailSelectDTO1 = new CheckoutDocumentDetailSelectDTO();
        checkoutDocumentDetailSelectDTO1.setGuid("384ba194-e460-44f0-b8c9-a5d8dd62123b");
        checkoutDocumentDetailSelectDTO1.setDocumentGuid("documentGuid");
        checkoutDocumentDetailSelectDTO1.setMaterialGuid("materialGuid");
        checkoutDocumentDetailSelectDTO1.setMaterialCode("materialCode");
        checkoutDocumentDetailSelectDTO1.setMaterialName("materialName");
        final List<CheckoutDocumentDetailSelectDTO> checkoutDocumentDetailSelectDTOS = Arrays.asList(
                checkoutDocumentDetailSelectDTO1);
        when(mockCheckoutDocumentService.selectDocumentDetailForUpdate("documentGuid"))
                .thenReturn(checkoutDocumentDetailSelectDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        post("/checkoutDocument/selectDocumentAndDetailForUpdate")
                                .param("documentGuid", "documentGuid")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSelectDocumentAndDetailForUpdate_CheckoutDocumentServiceSelectDocumentDetailForUpdateReturnsNoItems() throws Exception {
        // Setup
        // Configure CheckoutDocumentService.selectDocument(...).
        final CheckoutDocumentSelectDTO checkoutDocumentSelectDTO = new CheckoutDocumentSelectDTO();
        checkoutDocumentSelectDTO.setCreateStaffName("createStaffName");
        checkoutDocumentSelectDTO.setDocumentStoreGuid("documentStoreGuid");
        checkoutDocumentSelectDTO.setDocumentStoreName("documentStoreName");
        final CheckoutDocumentDetailSelectDTO checkoutDocumentDetailSelectDTO = new CheckoutDocumentDetailSelectDTO();
        checkoutDocumentDetailSelectDTO.setGuid("384ba194-e460-44f0-b8c9-a5d8dd62123b");
        checkoutDocumentSelectDTO.setDetailList(Arrays.asList(checkoutDocumentDetailSelectDTO));
        when(mockCheckoutDocumentService.selectDocument("documentGuid")).thenReturn(checkoutDocumentSelectDTO);

        when(mockCheckoutDocumentService.selectDocumentDetailForUpdate("documentGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        post("/checkoutDocument/selectDocumentAndDetailForUpdate")
                                .param("documentGuid", "documentGuid")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDeleteDocument() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/checkoutDocument/deleteDocument")
                        .param("documentGuid", "documentGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
        verify(mockValidator).deleteDocumentValidate("documentGuid");
        verify(mockCheckoutDocumentService).deleteCheckoutDocumentAndDetail("documentGuid");
    }

    @Test
    public void testSubmitCheckoutDocument() throws Exception {
        // Setup
        // Configure CheckoutDocumentService.insertAndSubmitCheckoutDocument(...).
        final CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO = new CheckoutDocumentAddOrUpdateDTO();
        addOrUpdateDTO.setUserGuid("operatorGuid");
        addOrUpdateDTO.setUserName("operatorName");
        addOrUpdateDTO.setDocumentDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        addOrUpdateDTO.setGuid("guid");
        addOrUpdateDTO.setOperatorGuid("operatorGuid");
        addOrUpdateDTO.setOperatorName("operatorName");
        when(mockCheckoutDocumentService.insertAndSubmitCheckoutDocument(addOrUpdateDTO)).thenReturn("guid");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/checkoutDocument/submitCheckoutDocument")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm CheckoutDocumentValidator.addOrUpdateCheckoutDocumentValidate(...).
        final CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO1 = new CheckoutDocumentAddOrUpdateDTO();
        addOrUpdateDTO1.setUserGuid("operatorGuid");
        addOrUpdateDTO1.setUserName("operatorName");
        addOrUpdateDTO1.setDocumentDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        addOrUpdateDTO1.setGuid("guid");
        addOrUpdateDTO1.setOperatorGuid("operatorGuid");
        addOrUpdateDTO1.setOperatorName("operatorName");
        verify(mockValidator).addOrUpdateCheckoutDocumentValidate(addOrUpdateDTO1);

        // Confirm CheckoutDocumentService.updateAndSubmitCheckoutDocumentWithLock(...).
        final CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO2 = new CheckoutDocumentAddOrUpdateDTO();
        addOrUpdateDTO2.setUserGuid("operatorGuid");
        addOrUpdateDTO2.setUserName("operatorName");
        addOrUpdateDTO2.setDocumentDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        addOrUpdateDTO2.setGuid("guid");
        addOrUpdateDTO2.setOperatorGuid("operatorGuid");
        addOrUpdateDTO2.setOperatorName("operatorName");
        verify(mockCheckoutDocumentService).updateAndSubmitCheckoutDocumentWithLock(addOrUpdateDTO2);
    }

    @Test
    public void testSelectCheckoutDocumentForPage() throws Exception {
        // Setup
        // Configure CheckoutDocumentService.selectCheckoutDocumentForPage(...).
        final CheckoutDocumentSelectDTO checkoutDocumentSelectDTO = new CheckoutDocumentSelectDTO();
        checkoutDocumentSelectDTO.setCreateStaffName("createStaffName");
        checkoutDocumentSelectDTO.setDocumentStoreGuid("documentStoreGuid");
        checkoutDocumentSelectDTO.setDocumentStoreName("documentStoreName");
        final CheckoutDocumentDetailSelectDTO checkoutDocumentDetailSelectDTO = new CheckoutDocumentDetailSelectDTO();
        checkoutDocumentDetailSelectDTO.setGuid("384ba194-e460-44f0-b8c9-a5d8dd62123b");
        checkoutDocumentSelectDTO.setDetailList(Arrays.asList(checkoutDocumentDetailSelectDTO));
        final Page<CheckoutDocumentSelectDTO> checkoutDocumentSelectDTOPage = new Page<>(0L, 0L,
                Arrays.asList(checkoutDocumentSelectDTO));
        final CheckoutDocumentQueryDTO queryDTO = new CheckoutDocumentQueryDTO();
        queryDTO.setUserGuid("operatorGuid");
        queryDTO.setUserName("operatorName");
        queryDTO.setWarehouseGuidList(Arrays.asList("value"));
        queryDTO.setStartDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        queryDTO.setEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockCheckoutDocumentService.selectCheckoutDocumentForPage(queryDTO))
                .thenReturn(checkoutDocumentSelectDTOPage);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/checkoutDocument/selectCheckoutDocumentForPage")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm CheckoutDocumentValidator.selectCheckoutDocumentForPageValidate(...).
        final CheckoutDocumentQueryDTO queryDTO1 = new CheckoutDocumentQueryDTO();
        queryDTO1.setUserGuid("operatorGuid");
        queryDTO1.setUserName("operatorName");
        queryDTO1.setWarehouseGuidList(Arrays.asList("value"));
        queryDTO1.setStartDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        queryDTO1.setEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockValidator).selectCheckoutDocumentForPageValidate(queryDTO1);
    }
}
