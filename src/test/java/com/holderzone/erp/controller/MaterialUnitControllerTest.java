package com.holderzone.erp.controller;

import com.holderzone.erp.service.IMaterialUnitService;
import com.holderzone.saas.store.dto.erp.MaterialUnitDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(SpringRunner.class)
@WebMvcTest(MaterialUnitController.class)
public class MaterialUnitControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IMaterialUnitService mockMaterialUnitService;

    @Test
    public void testAdd() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("materialUnit")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
        verify(mockMaterialUnitService).add(any(MaterialUnitDTO.class));
    }

    @Test
    public void testFindList() throws Exception {
        // Setup
        // Configure IMaterialUnitService.list(...).
        final MaterialUnitDTO materialUnitDTO = new MaterialUnitDTO();
        materialUnitDTO.setGuid("e76e827b-30c5-459e-bc02-491e1493c120");
        materialUnitDTO.setName("name");
        materialUnitDTO.setCode("code");
        final List<MaterialUnitDTO> materialUnitDTOS = Arrays.asList(materialUnitDTO);
        when(mockMaterialUnitService.list()).thenReturn(materialUnitDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("materialUnit")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testFindList_IMaterialUnitServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockMaterialUnitService.list()).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("materialUnit")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }
}
