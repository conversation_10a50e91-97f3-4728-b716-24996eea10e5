package com.holderzone.erp.controller;

import com.holderzone.erp.service.SuppliersService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;

@RunWith(SpringRunner.class)
@WebMvcTest(SuppliersController.class)
public class SuppliersControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private SuppliersService mockSuppliersService;

    @Test
    public void testCreateSuppliers() throws Exception {
        // Setup
        when(mockSuppliersService.createSuppliers(any(SuppliersReqDTO.class))).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/suppliers")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testUpdateSuppliers() throws Exception {
        // Setup
        when(mockSuppliersService.updateSuppliers(any(SuppliersReqDTO.class))).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(put("/suppliers")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetSuppliersByGuid() throws Exception {
        // Setup
        // Configure SuppliersService.getSuppliersByGuid(...).
        final SuppliersDTO suppliersDTO = new SuppliersDTO();
        suppliersDTO.setEnabled(0);
        suppliersDTO.setDeleted(0);
        suppliersDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGuid("a23a96f9-ccf5-4237-9d5e-21974239408b");
        when(mockSuppliersService.getSuppliersByGuid("195c91f1-ca30-466c-950a-0e4ae1ae80a8")).thenReturn(suppliersDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("/suppliers/{guid}", "195c91f1-ca30-466c-950a-0e4ae1ae80a8")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testEnableOrDisableSuppliers() throws Exception {
        // Setup
        when(mockSuppliersService.enableOrDisableSuppliers("73cbac84-9e40-49ca-bc31-5ccab456a59b")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        put("/suppliers/enable/{guid}", "73cbac84-9e40-49ca-bc31-5ccab456a59b")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testEnableOrDisableSuppliers_SuppliersServiceReturnsTrue() throws Exception {
        // Setup
        when(mockSuppliersService.enableOrDisableSuppliers("73cbac84-9e40-49ca-bc31-5ccab456a59b")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        put("/suppliers/enable/{guid}", "73cbac84-9e40-49ca-bc31-5ccab456a59b")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDeleteSuppliers() throws Exception {
        // Setup
        when(mockSuppliersService.deleteSuppliers("c06494db-ce7a-44a9-983d-4a202f7c29a6")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        delete("/suppliers/{guid}", "c06494db-ce7a-44a9-983d-4a202f7c29a6")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDeleteSuppliers_SuppliersServiceReturnsTrue() throws Exception {
        // Setup
        when(mockSuppliersService.deleteSuppliers("c06494db-ce7a-44a9-983d-4a202f7c29a6")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        delete("/suppliers/{guid}", "c06494db-ce7a-44a9-983d-4a202f7c29a6")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetSuppliersList() throws Exception {
        // Setup
        // Configure SuppliersService.getSuppliersList(...).
        final SuppliersDTO suppliersDTO = new SuppliersDTO();
        suppliersDTO.setEnabled(0);
        suppliersDTO.setDeleted(0);
        suppliersDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGuid("a23a96f9-ccf5-4237-9d5e-21974239408b");
        final Page<SuppliersDTO> suppliersDTOPage = new Page<>(0L, 0L, Arrays.asList(suppliersDTO));
        final SuppliersQueryDTO queryDTO = new SuppliersQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setSearchConditions("searchConditions");
        queryDTO.setForeignKey("foreignKey");
        queryDTO.setEnabled(0);
        when(mockSuppliersService.getSuppliersList(queryDTO)).thenReturn(suppliersDTOPage);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/suppliers/query/list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetAllOfSuppliersList() throws Exception {
        // Setup
        // Configure SuppliersService.getAllOfSuppliersList(...).
        final SuppliersDTO suppliersDTO = new SuppliersDTO();
        suppliersDTO.setEnabled(0);
        suppliersDTO.setDeleted(0);
        suppliersDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGuid("a23a96f9-ccf5-4237-9d5e-21974239408b");
        final List<SuppliersDTO> suppliersDTOS = Arrays.asList(suppliersDTO);
        final SuppliersQueryDTO queryDTO = new SuppliersQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setSearchConditions("searchConditions");
        queryDTO.setForeignKey("foreignKey");
        queryDTO.setEnabled(0);
        when(mockSuppliersService.getAllOfSuppliersList(queryDTO)).thenReturn(suppliersDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/suppliers/query/all")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetAllOfSuppliersList_SuppliersServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure SuppliersService.getAllOfSuppliersList(...).
        final SuppliersQueryDTO queryDTO = new SuppliersQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setSearchConditions("searchConditions");
        queryDTO.setForeignKey("foreignKey");
        queryDTO.setEnabled(0);
        when(mockSuppliersService.getAllOfSuppliersList(queryDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/suppliers/query/all")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testGetSuppliersMaterialListAll() throws Exception {
        // Setup
        // Configure SuppliersService.getSuppliersMaterialListAll(...).
        final CategoryDTO categoryDTO = new CategoryDTO();
        categoryDTO.setCategoryGuid("categoryGuid");
        categoryDTO.setCategoryName("categoryName");
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("383f210e-77bf-4b16-9d46-2b18daa36d5b");
        categoryDTO.setMaterialDTOList(Arrays.asList(materialDTO));
        final List<CategoryDTO> categoryDTOS = Arrays.asList(categoryDTO);
        when(mockSuppliersService.getSuppliersMaterialListAll(any(SuppliersMaterialQueryDTO.class)))
                .thenReturn(categoryDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/suppliers/category/all")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetSuppliersMaterialListAll_SuppliersServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockSuppliersService.getSuppliersMaterialListAll(any(SuppliersMaterialQueryDTO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/suppliers/category/all")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }
}
