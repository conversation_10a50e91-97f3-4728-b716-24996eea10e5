package com.holderzone.erp.controller;

import com.holderzone.erp.service.PricingSchemesService;
import com.holderzone.saas.store.dto.erp.MaterialUnitDTO;
import com.holderzone.saas.store.dto.erp.PricingReqDTO;
import com.holderzone.saas.store.dto.erp.PricingSchemesDTO;
import com.holderzone.saas.store.dto.erp.PricingSchemesQueryDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;

@RunWith(SpringRunner.class)
@WebMvcTest(PricingSchemesController.class)
public class PricingSchemesControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PricingSchemesService mockPricingSchemesService;

    @Test
    public void testSavePricingSchemes() throws Exception {
        // Setup
        when(mockPricingSchemesService.savePricingSchemes(any(PricingReqDTO.class))).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/pricing")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSavePricingSchemes_PricingSchemesServiceReturnsTrue() throws Exception {
        // Setup
        when(mockPricingSchemesService.savePricingSchemes(any(PricingReqDTO.class))).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/pricing")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetPricingSchemesListIncludeDisabled() throws Exception {
        // Setup
        // Configure PricingSchemesService.getPricingSchemesList(...).
        final PricingSchemesDTO pricingSchemesDTO = new PricingSchemesDTO();
        pricingSchemesDTO.setMaterialName("materialName");
        final MaterialUnitDTO materialUnitDTO = new MaterialUnitDTO();
        materialUnitDTO.setGuid("e23c2bb7-a42b-41a6-b62a-581d99b8f83d");
        materialUnitDTO.setName("name");
        materialUnitDTO.setCode("code");
        pricingSchemesDTO.setUnitList(Arrays.asList(materialUnitDTO));
        final List<PricingSchemesDTO> pricingSchemesDTOS = Arrays.asList(pricingSchemesDTO);
        when(mockPricingSchemesService.getPricingSchemesList("suppliersGuid")).thenReturn(pricingSchemesDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/pricing/{suppliersGuid}", "suppliersGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetPricingSchemesListIncludeDisabled_PricingSchemesServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockPricingSchemesService.getPricingSchemesList("suppliersGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/pricing/{suppliersGuid}", "suppliersGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testDeletePricingSchemes() throws Exception {
        // Setup
        when(mockPricingSchemesService.deletePricingSchemes(Arrays.asList("value"))).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        delete("/pricing/{guid}", "f509ea2c-ef89-47c6-b005-9154da2db44d")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDeletePricingSchemes_PricingSchemesServiceReturnsTrue() throws Exception {
        // Setup
        when(mockPricingSchemesService.deletePricingSchemes(Arrays.asList("value"))).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        delete("/pricing/{guid}", "f509ea2c-ef89-47c6-b005-9154da2db44d")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testEnableOrDisablePricingSchemes() throws Exception {
        // Setup
        when(mockPricingSchemesService.enableOrDisablePricingSchemes(
                "2768501a-5be5-468d-b508-d1f905b7aa49")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        put("/pricing/{guid}", "2768501a-5be5-468d-b508-d1f905b7aa49")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testEnableOrDisablePricingSchemes_PricingSchemesServiceReturnsTrue() throws Exception {
        // Setup
        when(mockPricingSchemesService.enableOrDisablePricingSchemes(
                "2768501a-5be5-468d-b508-d1f905b7aa49")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        put("/pricing/{guid}", "2768501a-5be5-468d-b508-d1f905b7aa49")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testBatchQueryPricingSchemesList() throws Exception {
        // Setup
        // Configure PricingSchemesService.batchQueryPricingSchemesList(...).
        final PricingSchemesDTO pricingSchemesDTO = new PricingSchemesDTO();
        pricingSchemesDTO.setMaterialName("materialName");
        final MaterialUnitDTO materialUnitDTO = new MaterialUnitDTO();
        materialUnitDTO.setGuid("e23c2bb7-a42b-41a6-b62a-581d99b8f83d");
        materialUnitDTO.setName("name");
        materialUnitDTO.setCode("code");
        pricingSchemesDTO.setUnitList(Arrays.asList(materialUnitDTO));
        final List<PricingSchemesDTO> pricingSchemesDTOS = Arrays.asList(pricingSchemesDTO);
        when(mockPricingSchemesService.batchQueryPricingSchemesList(any(PricingSchemesQueryDTO.class)))
                .thenReturn(pricingSchemesDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/pricing/batch")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testBatchQueryPricingSchemesList_PricingSchemesServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockPricingSchemesService.batchQueryPricingSchemesList(any(PricingSchemesQueryDTO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/pricing/batch")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }
}
