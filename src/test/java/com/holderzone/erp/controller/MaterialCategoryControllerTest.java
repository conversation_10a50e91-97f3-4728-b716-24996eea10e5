package com.holderzone.erp.controller;

import com.holderzone.erp.service.IMaterialCategoryService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;

@RunWith(SpringRunner.class)
@WebMvcTest(MaterialCategoryController.class)
public class MaterialCategoryControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IMaterialCategoryService mockMaterialCategoryService;

    @Test
    public void testAdd() throws Exception {
        // Setup
        // Configure IMaterialCategoryService.countByCode(...).
        final MaterialCategoryDTO materialCategoryDTO = new MaterialCategoryDTO();
        materialCategoryDTO.setGuid("2815bd78-2cfd-4e14-9684-3db0e5d9935a");
        materialCategoryDTO.setWarehouseGuid("warehouseGuid");
        materialCategoryDTO.setName("name");
        when(mockMaterialCategoryService.countByCode(materialCategoryDTO)).thenReturn(0L);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("materialCategory")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm IMaterialCategoryService.add(...).
        final MaterialCategoryDTO materialCategoryDTO1 = new MaterialCategoryDTO();
        materialCategoryDTO1.setGuid("2815bd78-2cfd-4e14-9684-3db0e5d9935a");
        materialCategoryDTO1.setWarehouseGuid("warehouseGuid");
        materialCategoryDTO1.setName("name");
        verify(mockMaterialCategoryService).add(materialCategoryDTO1);
    }

    @Test
    public void testUpdate() throws Exception {
        // Setup
        // Configure IMaterialCategoryService.countByCode(...).
        final MaterialCategoryDTO materialCategoryDTO = new MaterialCategoryDTO();
        materialCategoryDTO.setGuid("2815bd78-2cfd-4e14-9684-3db0e5d9935a");
        materialCategoryDTO.setWarehouseGuid("warehouseGuid");
        materialCategoryDTO.setName("name");
        when(mockMaterialCategoryService.countByCode(materialCategoryDTO)).thenReturn(0L);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(put("materialCategory")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm IMaterialCategoryService.update(...).
        final MaterialCategoryDTO materialCategoryDTO1 = new MaterialCategoryDTO();
        materialCategoryDTO1.setGuid("2815bd78-2cfd-4e14-9684-3db0e5d9935a");
        materialCategoryDTO1.setWarehouseGuid("warehouseGuid");
        materialCategoryDTO1.setName("name");
        verify(mockMaterialCategoryService).update(materialCategoryDTO1);
    }

    @Test
    public void testFindByGuid() throws Exception {
        // Setup
        // Configure IMaterialCategoryService.findByGuid(...).
        final MaterialCategoryDTO materialCategoryDTO = new MaterialCategoryDTO();
        materialCategoryDTO.setGuid("2815bd78-2cfd-4e14-9684-3db0e5d9935a");
        materialCategoryDTO.setWarehouseGuid("warehouseGuid");
        materialCategoryDTO.setName("name");
        when(mockMaterialCategoryService.findByGuid("b4a4609e-7b84-4912-99ad-626693f464ff"))
                .thenReturn(materialCategoryDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("materialCategory/{guid}", "b4a4609e-7b84-4912-99ad-626693f464ff")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testFindByCondition() throws Exception {
        // Setup
        // Configure IMaterialCategoryService.findByCondition(...).
        final MaterialCategoryDTO materialCategoryDTO = new MaterialCategoryDTO();
        materialCategoryDTO.setGuid("2815bd78-2cfd-4e14-9684-3db0e5d9935a");
        materialCategoryDTO.setWarehouseGuid("warehouseGuid");
        materialCategoryDTO.setName("name");
        final List<MaterialCategoryDTO> materialCategoryDTOS = Arrays.asList(materialCategoryDTO);
        final MaterialCategoryQueryDTO queryDTO = new MaterialCategoryQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setWarehouseGuid("warehouseGuid");
        queryDTO.setSearchConditions("searchConditions");
        queryDTO.setStoreGuid("storeGuid");
        when(mockMaterialCategoryService.findByCondition(eq(queryDTO), any(Page.class)))
                .thenReturn(materialCategoryDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("materialCategory/findByCondition")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testFindByCondition_IMaterialCategoryServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure IMaterialCategoryService.findByCondition(...).
        final MaterialCategoryQueryDTO queryDTO = new MaterialCategoryQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setWarehouseGuid("warehouseGuid");
        queryDTO.setSearchConditions("searchConditions");
        queryDTO.setStoreGuid("storeGuid");
        when(mockMaterialCategoryService.findByCondition(eq(queryDTO), any(Page.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("materialCategory/findByCondition")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testFindList() throws Exception {
        // Setup
        // Configure IMaterialCategoryService.findList(...).
        final MaterialCategoryDTO materialCategoryDTO = new MaterialCategoryDTO();
        materialCategoryDTO.setGuid("2815bd78-2cfd-4e14-9684-3db0e5d9935a");
        materialCategoryDTO.setWarehouseGuid("warehouseGuid");
        materialCategoryDTO.setName("name");
        final List<MaterialCategoryDTO> materialCategoryDTOS = Arrays.asList(materialCategoryDTO);
        when(mockMaterialCategoryService.findList(any(CategoryListQueryDTO.class))).thenReturn(materialCategoryDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("materialCategory/list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testFindList_IMaterialCategoryServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockMaterialCategoryService.findList(any(CategoryListQueryDTO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("materialCategory/list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testCountCategory() throws Exception {
        // Setup
        when(mockMaterialCategoryService.countMaterialByCategory("6eb01b4e-8690-49a0-a73a-a1dcff307671"))
                .thenReturn(0L);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("materialCategory/countCategory/{guid}", "6eb01b4e-8690-49a0-a73a-a1dcff307671")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testFindCategoryAndMaterial() throws Exception {
        // Setup
        // Configure IMaterialCategoryService.findCategoryAndMaterial(...).
        final CategoryDTO categoryDTO = new CategoryDTO();
        categoryDTO.setCategoryGuid("categoryGuid");
        categoryDTO.setCategoryName("categoryName");
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("078d9e29-e672-41e7-8520-aae8559150e5");
        categoryDTO.setMaterialDTOList(Arrays.asList(materialDTO));
        final List<CategoryDTO> categoryDTOS = Arrays.asList(categoryDTO);
        when(mockMaterialCategoryService.findCategoryAndMaterial("storeGuid", "searchConditions"))
                .thenReturn(categoryDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("materialCategory/listCategory")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testFindCategoryAndMaterial_IMaterialCategoryServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockMaterialCategoryService.findCategoryAndMaterial("storeGuid", "searchConditions"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("materialCategory/listCategory")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testDelete() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        delete("materialCategory/{guid}", "e97285d9-abe2-4255-af77-3404aa616c8e")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
        verify(mockMaterialCategoryService).delete("e97285d9-abe2-4255-af77-3404aa616c8e");
    }
}
