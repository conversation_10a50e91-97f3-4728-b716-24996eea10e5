package com.holderzone.erp.controller;

import com.holderzone.erp.service.IBomService;
import com.holderzone.erp.service.IMaterialService;
import com.holderzone.erp.service.InOutDocumentService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.MaterialConsumeReqDTO;
import com.holderzone.saas.store.dto.erp.MaterialConsumeRespDTO;
import com.holderzone.saas.store.dto.erp.MaterialDTO;
import com.holderzone.saas.store.dto.erp.MaterialQueryDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;

@RunWith(SpringRunner.class)
@WebMvcTest(MaterialController.class)
public class MaterialControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IMaterialService mockMaterialService;
    @MockBean
    private IBomService mockBomService;
    @MockBean
    private InOutDocumentService mockInOutDocumentService;

    @Test
    public void testAdd() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("material")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm IMaterialService.checkNameOrCode(...).
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("63cc2b39-f6e0-400e-b07a-ae5e0fb2650d");
        materialDTO.setEnterpriseGuid("enterpriseGuid");
        materialDTO.setProperty("property");
        materialDTO.setCode("code");
        verify(mockMaterialService).checkNameOrCode(materialDTO);

        // Confirm IMaterialService.add(...).
        final MaterialDTO materialDTO1 = new MaterialDTO();
        materialDTO1.setCategoryName("categoryName");
        materialDTO1.setGuid("63cc2b39-f6e0-400e-b07a-ae5e0fb2650d");
        materialDTO1.setEnterpriseGuid("enterpriseGuid");
        materialDTO1.setProperty("property");
        materialDTO1.setCode("code");
        verify(mockMaterialService).add(materialDTO1);
    }

    @Test
    public void testUpdate() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(put("material")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm IMaterialService.checkNameOrCode(...).
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("63cc2b39-f6e0-400e-b07a-ae5e0fb2650d");
        materialDTO.setEnterpriseGuid("enterpriseGuid");
        materialDTO.setProperty("property");
        materialDTO.setCode("code");
        verify(mockMaterialService).checkNameOrCode(materialDTO);

        // Confirm IMaterialService.update(...).
        final MaterialDTO materialDTO1 = new MaterialDTO();
        materialDTO1.setCategoryName("categoryName");
        materialDTO1.setGuid("63cc2b39-f6e0-400e-b07a-ae5e0fb2650d");
        materialDTO1.setEnterpriseGuid("enterpriseGuid");
        materialDTO1.setProperty("property");
        materialDTO1.setCode("code");
        verify(mockMaterialService).update(materialDTO1);
    }

    @Test
    public void testChangeStatus() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(put("material/changeStatus")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm IMaterialService.changeStatus(...).
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("63cc2b39-f6e0-400e-b07a-ae5e0fb2650d");
        materialDTO.setEnterpriseGuid("enterpriseGuid");
        materialDTO.setProperty("property");
        materialDTO.setCode("code");
        verify(mockMaterialService).changeStatus(materialDTO);
    }

    @Test
    public void testFindByCondition() throws Exception {
        // Setup
        // Configure IMaterialService.findByCondition(...).
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("63cc2b39-f6e0-400e-b07a-ae5e0fb2650d");
        materialDTO.setEnterpriseGuid("enterpriseGuid");
        materialDTO.setProperty("property");
        materialDTO.setCode("code");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO);
        final MaterialQueryDTO queryDTO = new MaterialQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setCategory("category");
        queryDTO.setState(0);
        queryDTO.setStoreGuid("storeGuid");
        when(mockMaterialService.findByCondition(eq(queryDTO), any(Page.class))).thenReturn(materialDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("material/findByCondition")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testFindByCondition_IMaterialServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure IMaterialService.findByCondition(...).
        final MaterialQueryDTO queryDTO = new MaterialQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setCategory("category");
        queryDTO.setState(0);
        queryDTO.setStoreGuid("storeGuid");
        when(mockMaterialService.findByCondition(eq(queryDTO), any(Page.class))).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("material/findByCondition")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testFindListByCondition() throws Exception {
        // Setup
        // Configure IMaterialService.findByCondition(...).
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("63cc2b39-f6e0-400e-b07a-ae5e0fb2650d");
        materialDTO.setEnterpriseGuid("enterpriseGuid");
        materialDTO.setProperty("property");
        materialDTO.setCode("code");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO);
        final MaterialQueryDTO queryDTO = new MaterialQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setCategory("category");
        queryDTO.setState(0);
        queryDTO.setStoreGuid("storeGuid");
        when(mockMaterialService.findByCondition(eq(queryDTO), any(Page.class))).thenReturn(materialDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("material/findListByCondition")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testFindListByCondition_IMaterialServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure IMaterialService.findByCondition(...).
        final MaterialQueryDTO queryDTO = new MaterialQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setCategory("category");
        queryDTO.setState(0);
        queryDTO.setStoreGuid("storeGuid");
        when(mockMaterialService.findByCondition(eq(queryDTO), any(Page.class))).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("material/findListByCondition")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testFindByGuid() throws Exception {
        // Setup
        // Configure IMaterialService.findByGuid(...).
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("63cc2b39-f6e0-400e-b07a-ae5e0fb2650d");
        materialDTO.setEnterpriseGuid("enterpriseGuid");
        materialDTO.setProperty("property");
        materialDTO.setCode("code");
        when(mockMaterialService.findByGuid("2f729d5b-fd80-417f-89f0-eea4db1d5cff")).thenReturn(materialDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("material/{guid}", "2f729d5b-fd80-417f-89f0-eea4db1d5cff")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testFindList() throws Exception {
        // Setup
        // Configure IMaterialService.findAllList(...).
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("63cc2b39-f6e0-400e-b07a-ae5e0fb2650d");
        materialDTO.setEnterpriseGuid("enterpriseGuid");
        materialDTO.setProperty("property");
        materialDTO.setCode("code");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO);
        when(mockMaterialService.findAllList("storeGuid", Arrays.asList("value"))).thenReturn(materialDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("material/findByGuidList")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testFindList_IMaterialServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockMaterialService.findAllList("storeGuid", Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("material/findByGuidList")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testCountMaterialBom() throws Exception {
        // Setup
        when(mockBomService.countBomByMaterial("materialGuid")).thenReturn(0L);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("material/countBom/{materialGuid}", "materialGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDelete() throws Exception {
        // Setup
        when(mockInOutDocumentService.selectInOutDocumentCountByMaterial(
                "087719dc-7613-4383-a825-36c81433bdd9")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        delete("material/{guid}", "087719dc-7613-4383-a825-36c81433bdd9")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
        verify(mockMaterialService).delete("087719dc-7613-4383-a825-36c81433bdd9");
    }

    @Test
    public void testDelete_InOutDocumentServiceReturnsFalse() throws Exception {
        // Setup
        when(mockInOutDocumentService.selectInOutDocumentCountByMaterial(
                "087719dc-7613-4383-a825-36c81433bdd9")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        delete("material/{guid}", "087719dc-7613-4383-a825-36c81433bdd9")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testBatchAdd() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("material/batch")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm IMaterialService.fillCodeAndGuid(...).
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("63cc2b39-f6e0-400e-b07a-ae5e0fb2650d");
        materialDTO.setEnterpriseGuid("enterpriseGuid");
        materialDTO.setProperty("property");
        materialDTO.setCode("code");
        final List<MaterialDTO> materialDTOList = Arrays.asList(materialDTO);
        verify(mockMaterialService).fillCodeAndGuid(materialDTOList);

        // Confirm IMaterialService.batchCheckName(...).
        final MaterialDTO materialDTO1 = new MaterialDTO();
        materialDTO1.setCategoryName("categoryName");
        materialDTO1.setGuid("63cc2b39-f6e0-400e-b07a-ae5e0fb2650d");
        materialDTO1.setEnterpriseGuid("enterpriseGuid");
        materialDTO1.setProperty("property");
        materialDTO1.setCode("code");
        final List<MaterialDTO> materialDTOList1 = Arrays.asList(materialDTO1);
        verify(mockMaterialService).batchCheckName(materialDTOList1);

        // Confirm IMaterialService.batchCheckCode(...).
        final MaterialDTO materialDTO2 = new MaterialDTO();
        materialDTO2.setCategoryName("categoryName");
        materialDTO2.setGuid("63cc2b39-f6e0-400e-b07a-ae5e0fb2650d");
        materialDTO2.setEnterpriseGuid("enterpriseGuid");
        materialDTO2.setProperty("property");
        materialDTO2.setCode("code");
        final List<MaterialDTO> materialDTOList2 = Arrays.asList(materialDTO2);
        verify(mockMaterialService).batchCheckCode(materialDTOList2);

        // Confirm IMaterialService.addBatch(...).
        final MaterialDTO materialDTO3 = new MaterialDTO();
        materialDTO3.setCategoryName("categoryName");
        materialDTO3.setGuid("63cc2b39-f6e0-400e-b07a-ae5e0fb2650d");
        materialDTO3.setEnterpriseGuid("enterpriseGuid");
        materialDTO3.setProperty("property");
        materialDTO3.setCode("code");
        final List<MaterialDTO> materialDTOList3 = Arrays.asList(materialDTO3);
        verify(mockMaterialService).addBatch(materialDTOList3);
    }

    @Test
    public void testGeneratorCode() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("material/generatorCode")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm IMaterialService.checkNameOrCode(...).
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("63cc2b39-f6e0-400e-b07a-ae5e0fb2650d");
        materialDTO.setEnterpriseGuid("enterpriseGuid");
        materialDTO.setProperty("property");
        materialDTO.setCode("code");
        verify(mockMaterialService).checkNameOrCode(materialDTO);
    }

    @Test
    public void testMaterialConsumeSum() throws Exception {
        // Setup
        // Configure IMaterialService.materialConsumeSum(...).
        final MaterialConsumeRespDTO materialConsumeRespDTO = new MaterialConsumeRespDTO();
        materialConsumeRespDTO.setMaterialGuid("materialGuid");
        materialConsumeRespDTO.setCode("code");
        materialConsumeRespDTO.setName("name");
        materialConsumeRespDTO.setClassify("classify");
        materialConsumeRespDTO.setUnit("unit");
        final Page<MaterialConsumeRespDTO> materialConsumeRespDTOPage = new Page<>(0L, 0L,
                Arrays.asList(materialConsumeRespDTO));
        final MaterialConsumeReqDTO materialConsumeReqDTO = new MaterialConsumeReqDTO();
        materialConsumeReqDTO.setCurrentPage(0);
        materialConsumeReqDTO.setPageSize(0);
        materialConsumeReqDTO.setWarehouseGuid("warehouseGuid");
        materialConsumeReqDTO.setStartDate("startDate");
        materialConsumeReqDTO.setEndDate("endDate");
        when(mockMaterialService.materialConsumeSum(materialConsumeReqDTO)).thenReturn(materialConsumeRespDTOPage);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("material/materialConsumeSum")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
