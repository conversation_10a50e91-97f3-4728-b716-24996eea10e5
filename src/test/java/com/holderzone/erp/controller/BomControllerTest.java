package com.holderzone.erp.controller;

import com.holderzone.erp.service.IBomService;
import com.holderzone.erp.service.feign.ItemFeignService;
import com.holderzone.saas.store.dto.erp.GoodsBomConfigDTO;
import com.holderzone.saas.store.dto.erp.GoodsBomDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(SpringRunner.class)
@WebMvcTest(BomController.class)
public class BomControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IBomService mockIBomService;
    @MockBean
    private ItemFeignService mockItemFeignService;

    @Test
    public void testAdd() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("bom")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
        verify(mockIBomService).add(any(GoodsBomConfigDTO.class));
    }

    @Test
    public void testFindBomByGoods() throws Exception {
        // Setup
        // Configure IBomService.findBomByGoods(...).
        final GoodsBomDTO goodsBomDTO = new GoodsBomDTO();
        goodsBomDTO.setGoodsGuid("goodsGuid");
        goodsBomDTO.setMaterialGuid("materialGuid");
        goodsBomDTO.setUsage(new BigDecimal("0.00"));
        goodsBomDTO.setUnit("unit");
        goodsBomDTO.setUnitName("unitName");
        final List<GoodsBomDTO> goodsBomDTOS = Arrays.asList(goodsBomDTO);
        when(mockIBomService.findBomByGoods("goodsGuid", "goodsSku")).thenReturn(goodsBomDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("bom/{goodsGuid}/{goodsSku}", "goodsGuid", "goodsSku")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testFindBomByGoods_IBomServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockIBomService.findBomByGoods("goodsGuid", "goodsSku")).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("bom/{goodsGuid}/{goodsSku}", "goodsGuid", "goodsSku")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testFindGoodsList() throws Exception {
        // Setup
        // Configure IBomService.countBomTypeBySkuList(...).
        final GoodsBomDTO goodsBomDTO = new GoodsBomDTO();
        goodsBomDTO.setGoodsGuid("goodsGuid");
        goodsBomDTO.setMaterialGuid("materialGuid");
        goodsBomDTO.setUsage(new BigDecimal("0.00"));
        goodsBomDTO.setUnit("unit");
        goodsBomDTO.setUnitName("unitName");
        final List<GoodsBomDTO> goodsBomDTOS = Arrays.asList(goodsBomDTO);
        when(mockIBomService.countBomTypeBySkuList(Arrays.asList("value"))).thenReturn(goodsBomDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("bom/findGoodsBomCount")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testFindGoodsList_IBomServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockIBomService.countBomTypeBySkuList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("bom/findGoodsBomCount")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }
}
