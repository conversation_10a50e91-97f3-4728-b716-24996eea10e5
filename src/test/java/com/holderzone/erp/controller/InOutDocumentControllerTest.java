package com.holderzone.erp.controller;

import com.holderzone.erp.service.InOutDocumentService;
import com.holderzone.erp.service.WarehouseService;
import com.holderzone.erp.validate.InOutDocumentValidator;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(SpringRunner.class)
@WebMvcTest(InOutDocumentController.class)
public class InOutDocumentControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private InOutDocumentValidator mockValidator;
    @MockBean
    private InOutDocumentService mockInOutDocumentService;
    @MockBean
    private WarehouseService mockWarehouseService;

    @Test
    public void testInsertOrUpdateInOutDocument() throws Exception {
        // Setup
        when(mockWarehouseService.isLock("warehouseGuid")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/insertOrUpdate")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm InOutDocumentValidator.insertOrUpdateInOutDocumentValidate(...).
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setUserGuid("operatorGuid");
        inOutDocumentDTO.setUserName("operatorName");
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setGuid("guid");
        inOutDocumentDTO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDTO.setDocumentDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inOutDocumentDTO.setType(0);
        inOutDocumentDTO.setOperatorGuid("operatorGuid");
        inOutDocumentDTO.setOperatorName("operatorName");
        verify(mockValidator).insertOrUpdateInOutDocumentValidate(inOutDocumentDTO);

        // Confirm InOutDocumentService.insertInOutDocumentAndDetail(...).
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO1 = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO1.setUserGuid("operatorGuid");
        inOutDocumentDTO1.setUserName("operatorName");
        inOutDocumentDTO1.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO1.setGuid("guid");
        inOutDocumentDTO1.setWarehouseGuid("warehouseGuid");
        inOutDocumentDTO1.setDocumentDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inOutDocumentDTO1.setType(0);
        inOutDocumentDTO1.setOperatorGuid("operatorGuid");
        inOutDocumentDTO1.setOperatorName("operatorName");
        verify(mockInOutDocumentService).insertInOutDocumentAndDetail(inOutDocumentDTO1);

        // Confirm InOutDocumentService.updateInOutDocumentAndDetail(...).
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO2 = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO2.setUserGuid("operatorGuid");
        inOutDocumentDTO2.setUserName("operatorName");
        inOutDocumentDTO2.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO2.setGuid("guid");
        inOutDocumentDTO2.setWarehouseGuid("warehouseGuid");
        inOutDocumentDTO2.setDocumentDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inOutDocumentDTO2.setType(0);
        inOutDocumentDTO2.setOperatorGuid("operatorGuid");
        inOutDocumentDTO2.setOperatorName("operatorName");
        verify(mockInOutDocumentService).updateInOutDocumentAndDetail(inOutDocumentDTO2);
    }

    @Test
    public void testInsertOrUpdateInOutDocument_WarehouseServiceReturnsTrue() throws Exception {
        // Setup
        when(mockWarehouseService.isLock("warehouseGuid")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/insertOrUpdate")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm InOutDocumentValidator.insertOrUpdateInOutDocumentValidate(...).
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setUserGuid("operatorGuid");
        inOutDocumentDTO.setUserName("operatorName");
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setGuid("guid");
        inOutDocumentDTO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDTO.setDocumentDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inOutDocumentDTO.setType(0);
        inOutDocumentDTO.setOperatorGuid("operatorGuid");
        inOutDocumentDTO.setOperatorName("operatorName");
        verify(mockValidator).insertOrUpdateInOutDocumentValidate(inOutDocumentDTO);
    }

    @Test
    public void testSelectMaterialListForAdd() throws Exception {
        // Setup
        // Configure InOutDocumentService.selectMaterialListForAdd(...).
        final InOutDocumentDetailSelectDTO inOutDocumentDetailSelectDTO = new InOutDocumentDetailSelectDTO();
        inOutDocumentDetailSelectDTO.setReturnCount(new BigDecimal("0.00"));
        final InOutDocumentMaterialUnitDTO inOutDocumentMaterialUnitDTO = new InOutDocumentMaterialUnitDTO();
        inOutDocumentMaterialUnitDTO.setUnitGuid("unitGuid");
        inOutDocumentMaterialUnitDTO.setUnitName("unitName");
        inOutDocumentDetailSelectDTO.setMaterialUnitList(Arrays.asList(inOutDocumentMaterialUnitDTO));
        inOutDocumentDetailSelectDTO.setGuid("ce861990-f582-4fae-b535-03880122b7f9");
        final List<InOutDocumentDetailSelectDTO> inOutDocumentDetailSelectDTOS = Arrays.asList(
                inOutDocumentDetailSelectDTO);
        when(mockInOutDocumentService.selectMaterialListForAdd(any(InOutDocumentDetailQueryDTO.class)))
                .thenReturn(inOutDocumentDetailSelectDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/selectMaterialListForAdd")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
        verify(mockValidator).selectMaterialListValidate(any(InOutDocumentDetailQueryDTO.class));
    }

    @Test
    public void testSelectMaterialListForAdd_InOutDocumentServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockInOutDocumentService.selectMaterialListForAdd(any(InOutDocumentDetailQueryDTO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/selectMaterialListForAdd")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
        verify(mockValidator).selectMaterialListValidate(any(InOutDocumentDetailQueryDTO.class));
    }

    @Test
    public void testSelectMaterialListForReturn() throws Exception {
        // Setup
        // Configure InOutDocumentService.selectMaterialListForReturn(...).
        final InOutDocumentDetailSelectDTO inOutDocumentDetailSelectDTO = new InOutDocumentDetailSelectDTO();
        inOutDocumentDetailSelectDTO.setReturnCount(new BigDecimal("0.00"));
        final InOutDocumentMaterialUnitDTO inOutDocumentMaterialUnitDTO = new InOutDocumentMaterialUnitDTO();
        inOutDocumentMaterialUnitDTO.setUnitGuid("unitGuid");
        inOutDocumentMaterialUnitDTO.setUnitName("unitName");
        inOutDocumentDetailSelectDTO.setMaterialUnitList(Arrays.asList(inOutDocumentMaterialUnitDTO));
        inOutDocumentDetailSelectDTO.setGuid("ce861990-f582-4fae-b535-03880122b7f9");
        final List<InOutDocumentDetailSelectDTO> inOutDocumentDetailSelectDTOS = Arrays.asList(
                inOutDocumentDetailSelectDTO);
        when(mockInOutDocumentService.selectMaterialListForReturn("contactDocumentGuid"))
                .thenReturn(inOutDocumentDetailSelectDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/selectMaterialListForReturn")
                        .param("contactDocumentGuid", "contactDocumentGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSelectMaterialListForReturn_InOutDocumentServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockInOutDocumentService.selectMaterialListForReturn("contactDocumentGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/selectMaterialListForReturn")
                        .param("contactDocumentGuid", "contactDocumentGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testSubmitInOutDocument() throws Exception {
        // Setup
        when(mockWarehouseService.isLock("warehouseGuid")).thenReturn(false);

        // Configure InOutDocumentService.insertAndSubmitInOutDocumentWithLock(...).
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setUserGuid("operatorGuid");
        inOutDocumentDTO.setUserName("operatorName");
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setGuid("guid");
        inOutDocumentDTO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDTO.setDocumentDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inOutDocumentDTO.setType(0);
        inOutDocumentDTO.setOperatorGuid("operatorGuid");
        inOutDocumentDTO.setOperatorName("operatorName");
        when(mockInOutDocumentService.insertAndSubmitInOutDocumentWithLock(inOutDocumentDTO)).thenReturn("guid");

        // Configure InOutDocumentService.insertAndSubmitInOutDocument(...).
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO1 = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO1.setUserGuid("operatorGuid");
        inOutDocumentDTO1.setUserName("operatorName");
        inOutDocumentDTO1.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO1.setGuid("guid");
        inOutDocumentDTO1.setWarehouseGuid("warehouseGuid");
        inOutDocumentDTO1.setDocumentDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inOutDocumentDTO1.setType(0);
        inOutDocumentDTO1.setOperatorGuid("operatorGuid");
        inOutDocumentDTO1.setOperatorName("operatorName");
        when(mockInOutDocumentService.insertAndSubmitInOutDocument(inOutDocumentDTO1)).thenReturn("guid");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/submitInOutDocument")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm InOutDocumentValidator.insertOrUpdateInOutDocumentValidate(...).
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO2 = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO2.setUserGuid("operatorGuid");
        inOutDocumentDTO2.setUserName("operatorName");
        inOutDocumentDTO2.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO2.setGuid("guid");
        inOutDocumentDTO2.setWarehouseGuid("warehouseGuid");
        inOutDocumentDTO2.setDocumentDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inOutDocumentDTO2.setType(0);
        inOutDocumentDTO2.setOperatorGuid("operatorGuid");
        inOutDocumentDTO2.setOperatorName("operatorName");
        verify(mockValidator).insertOrUpdateInOutDocumentValidate(inOutDocumentDTO2);

        // Confirm InOutDocumentService.updateAndSubmitInOutDocumentWithLock(...).
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO3 = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO3.setUserGuid("operatorGuid");
        inOutDocumentDTO3.setUserName("operatorName");
        inOutDocumentDTO3.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO3.setGuid("guid");
        inOutDocumentDTO3.setWarehouseGuid("warehouseGuid");
        inOutDocumentDTO3.setDocumentDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inOutDocumentDTO3.setType(0);
        inOutDocumentDTO3.setOperatorGuid("operatorGuid");
        inOutDocumentDTO3.setOperatorName("operatorName");
        verify(mockInOutDocumentService).updateAndSubmitInOutDocumentWithLock(inOutDocumentDTO3);
    }

    @Test
    public void testSubmitInOutDocument_WarehouseServiceReturnsTrue() throws Exception {
        // Setup
        when(mockWarehouseService.isLock("warehouseGuid")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/submitInOutDocument")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm InOutDocumentValidator.insertOrUpdateInOutDocumentValidate(...).
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setUserGuid("operatorGuid");
        inOutDocumentDTO.setUserName("operatorName");
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setGuid("guid");
        inOutDocumentDTO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDTO.setDocumentDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inOutDocumentDTO.setType(0);
        inOutDocumentDTO.setOperatorGuid("operatorGuid");
        inOutDocumentDTO.setOperatorName("operatorName");
        verify(mockValidator).insertOrUpdateInOutDocumentValidate(inOutDocumentDTO);
    }

    @Test
    public void testDeleteDocument() throws Exception {
        // Setup
        when(mockInOutDocumentService.selectWarehouseGuidByDocumentGuid("documentGuid")).thenReturn("guid");
        when(mockWarehouseService.isLock("guid")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/deleteDocument")
                        .param("documentGuid", "documentGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
        verify(mockInOutDocumentService).deleteDocument("documentGuid");
    }

    @Test
    public void testDeleteDocument_WarehouseServiceReturnsTrue() throws Exception {
        // Setup
        when(mockInOutDocumentService.selectWarehouseGuidByDocumentGuid("documentGuid")).thenReturn("guid");
        when(mockWarehouseService.isLock("guid")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/deleteDocument")
                        .param("documentGuid", "documentGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSelectDocumentGuidList() throws Exception {
        // Setup
        when(mockInOutDocumentService.selectDocumentGuidList(any(InOutContactDocumentQueryDTO.class)))
                .thenReturn(Arrays.asList("value"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/selectDocumentGuidList")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
        verify(mockValidator).selectDocumentGuidListValidate(any(InOutContactDocumentQueryDTO.class));
    }

    @Test
    public void testSelectDocumentGuidList_InOutDocumentServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockInOutDocumentService.selectDocumentGuidList(any(InOutContactDocumentQueryDTO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/selectDocumentGuidList")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
        verify(mockValidator).selectDocumentGuidListValidate(any(InOutContactDocumentQueryDTO.class));
    }

    @Test
    public void testSelectDocumentAndDetailForUpdate() throws Exception {
        // Setup
        // Configure InOutDocumentService.selectDocumentForUpdate(...).
        final InOutDocumentSelectDTO inOutDocumentSelectDTO = new InOutDocumentSelectDTO();
        inOutDocumentSelectDTO.setDocumentStoreGuid("documentStoreGuid");
        inOutDocumentSelectDTO.setDocumentStoreName("documentStoreName");
        inOutDocumentSelectDTO.setSettleStatus(0);
        inOutDocumentSelectDTO.setGmtCreate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final InOutDocumentDetailSelectDTO inOutDocumentDetailSelectDTO = new InOutDocumentDetailSelectDTO();
        inOutDocumentSelectDTO.setDetailList(Arrays.asList(inOutDocumentDetailSelectDTO));
        when(mockInOutDocumentService.selectDocumentForUpdate("documentGuid")).thenReturn(inOutDocumentSelectDTO);

        // Configure InOutDocumentService.selectMaterialListForUpdate(...).
        final InOutDocumentDetailSelectDTO inOutDocumentDetailSelectDTO1 = new InOutDocumentDetailSelectDTO();
        inOutDocumentDetailSelectDTO1.setReturnCount(new BigDecimal("0.00"));
        final InOutDocumentMaterialUnitDTO inOutDocumentMaterialUnitDTO = new InOutDocumentMaterialUnitDTO();
        inOutDocumentMaterialUnitDTO.setUnitGuid("unitGuid");
        inOutDocumentMaterialUnitDTO.setUnitName("unitName");
        inOutDocumentDetailSelectDTO1.setMaterialUnitList(Arrays.asList(inOutDocumentMaterialUnitDTO));
        inOutDocumentDetailSelectDTO1.setGuid("ce861990-f582-4fae-b535-03880122b7f9");
        final List<InOutDocumentDetailSelectDTO> inOutDocumentDetailSelectDTOS = Arrays.asList(
                inOutDocumentDetailSelectDTO1);
        when(mockInOutDocumentService.selectMaterialListForUpdate("documentGuid"))
                .thenReturn(inOutDocumentDetailSelectDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/selectDocumentAndDetailForUpdate")
                        .param("documentGuid", "documentGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSelectDocumentAndDetailForUpdate_InOutDocumentServiceSelectMaterialListForUpdateReturnsNoItems() throws Exception {
        // Setup
        // Configure InOutDocumentService.selectDocumentForUpdate(...).
        final InOutDocumentSelectDTO inOutDocumentSelectDTO = new InOutDocumentSelectDTO();
        inOutDocumentSelectDTO.setDocumentStoreGuid("documentStoreGuid");
        inOutDocumentSelectDTO.setDocumentStoreName("documentStoreName");
        inOutDocumentSelectDTO.setSettleStatus(0);
        inOutDocumentSelectDTO.setGmtCreate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final InOutDocumentDetailSelectDTO inOutDocumentDetailSelectDTO = new InOutDocumentDetailSelectDTO();
        inOutDocumentSelectDTO.setDetailList(Arrays.asList(inOutDocumentDetailSelectDTO));
        when(mockInOutDocumentService.selectDocumentForUpdate("documentGuid")).thenReturn(inOutDocumentSelectDTO);

        when(mockInOutDocumentService.selectMaterialListForUpdate("documentGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/selectDocumentAndDetailForUpdate")
                        .param("documentGuid", "documentGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSelectDocumentAndDetailForSelect() throws Exception {
        // Setup
        // Configure InOutDocumentService.selectDocumentAndDetailForSelect(...).
        final InOutDocumentSelectDTO inOutDocumentSelectDTO = new InOutDocumentSelectDTO();
        inOutDocumentSelectDTO.setDocumentStoreGuid("documentStoreGuid");
        inOutDocumentSelectDTO.setDocumentStoreName("documentStoreName");
        inOutDocumentSelectDTO.setSettleStatus(0);
        inOutDocumentSelectDTO.setGmtCreate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final InOutDocumentDetailSelectDTO inOutDocumentDetailSelectDTO = new InOutDocumentDetailSelectDTO();
        inOutDocumentSelectDTO.setDetailList(Arrays.asList(inOutDocumentDetailSelectDTO));
        when(mockInOutDocumentService.selectDocumentAndDetailForSelect("documentGuid"))
                .thenReturn(inOutDocumentSelectDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/selectDocumentAndDetailForSelect")
                        .param("documentGuid", "documentGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSelectDocumentListForPage() throws Exception {
        // Setup
        // Configure InOutDocumentService.selectDocumentListForPage(...).
        final InOutDocumentSelectDTO inOutDocumentSelectDTO = new InOutDocumentSelectDTO();
        inOutDocumentSelectDTO.setDocumentStoreGuid("documentStoreGuid");
        inOutDocumentSelectDTO.setDocumentStoreName("documentStoreName");
        inOutDocumentSelectDTO.setSettleStatus(0);
        inOutDocumentSelectDTO.setGmtCreate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final InOutDocumentDetailSelectDTO inOutDocumentDetailSelectDTO = new InOutDocumentDetailSelectDTO();
        inOutDocumentSelectDTO.setDetailList(Arrays.asList(inOutDocumentDetailSelectDTO));
        final Page<InOutDocumentSelectDTO> inOutDocumentSelectDTOPage = new Page<>(0L, 0L,
                Arrays.asList(inOutDocumentSelectDTO));
        final InOutDocumentQueryDTO queryDTO = new InOutDocumentQueryDTO();
        queryDTO.setUserGuid("operatorGuid");
        queryDTO.setUserName("operatorName");
        queryDTO.setWarehouseGuidList(Arrays.asList("value"));
        queryDTO.setStatus(0);
        queryDTO.setType(0);
        when(mockInOutDocumentService.selectDocumentListForPage(queryDTO)).thenReturn(inOutDocumentSelectDTOPage);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/selectDocumentListForPage")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm InOutDocumentValidator.selectDocumentListForPageValidate(...).
        final InOutDocumentQueryDTO queryDTO1 = new InOutDocumentQueryDTO();
        queryDTO1.setUserGuid("operatorGuid");
        queryDTO1.setUserName("operatorName");
        queryDTO1.setWarehouseGuidList(Arrays.asList("value"));
        queryDTO1.setStatus(0);
        queryDTO1.setType(0);
        verify(mockValidator).selectDocumentListForPageValidate(queryDTO1);
    }

    @Test
    public void testSelectSuppliersReconciliation() throws Exception {
        // Setup
        // Configure InOutDocumentService.selectSuppliersReconciliation(...).
        final InOutDocumentSelectDTO inOutDocumentSelectDTO = new InOutDocumentSelectDTO();
        inOutDocumentSelectDTO.setDocumentStoreGuid("documentStoreGuid");
        inOutDocumentSelectDTO.setDocumentStoreName("documentStoreName");
        inOutDocumentSelectDTO.setSettleStatus(0);
        inOutDocumentSelectDTO.setGmtCreate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final InOutDocumentDetailSelectDTO inOutDocumentDetailSelectDTO = new InOutDocumentDetailSelectDTO();
        inOutDocumentSelectDTO.setDetailList(Arrays.asList(inOutDocumentDetailSelectDTO));
        final Page<InOutDocumentSelectDTO> inOutDocumentSelectDTOPage = new Page<>(0L, 0L,
                Arrays.asList(inOutDocumentSelectDTO));
        final SuppliersReconciliationQueryDTO queryDTO = new SuppliersReconciliationQueryDTO();
        queryDTO.setUserGuid("operatorGuid");
        queryDTO.setUserName("operatorName");
        queryDTO.setGuid("69c62502-3cec-4b34-978a-6cea2ea44cbb");
        queryDTO.setSupplierGuid("supplierGuid");
        queryDTO.setStatus(0);
        when(mockInOutDocumentService.selectSuppliersReconciliation(queryDTO)).thenReturn(inOutDocumentSelectDTOPage);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/reconciliation")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm InOutDocumentValidator.selectSuppliersReconciliationValidate(...).
        final SuppliersReconciliationQueryDTO queryDTO1 = new SuppliersReconciliationQueryDTO();
        queryDTO1.setUserGuid("operatorGuid");
        queryDTO1.setUserName("operatorName");
        queryDTO1.setGuid("69c62502-3cec-4b34-978a-6cea2ea44cbb");
        queryDTO1.setSupplierGuid("supplierGuid");
        queryDTO1.setStatus(0);
        verify(mockValidator).selectSuppliersReconciliationValidate(queryDTO1);
    }

    @Test
    public void testSelectSuppliersReconciliationTotalAmount() throws Exception {
        // Setup
        // Configure InOutDocumentService.selectSuppliersReconciliationTotalAmount(...).
        final SuppliersReconciliationQueryDTO queryDTO = new SuppliersReconciliationQueryDTO();
        queryDTO.setUserGuid("operatorGuid");
        queryDTO.setUserName("operatorName");
        queryDTO.setGuid("69c62502-3cec-4b34-978a-6cea2ea44cbb");
        queryDTO.setSupplierGuid("supplierGuid");
        queryDTO.setStatus(0);
        when(mockInOutDocumentService.selectSuppliersReconciliationTotalAmount(queryDTO))
                .thenReturn(new BigDecimal("0.00"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/reconciliation/total")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm InOutDocumentValidator.selectSuppliersReconciliationValidate(...).
        final SuppliersReconciliationQueryDTO queryDTO1 = new SuppliersReconciliationQueryDTO();
        queryDTO1.setUserGuid("operatorGuid");
        queryDTO1.setUserName("operatorName");
        queryDTO1.setGuid("69c62502-3cec-4b34-978a-6cea2ea44cbb");
        queryDTO1.setSupplierGuid("supplierGuid");
        queryDTO1.setStatus(0);
        verify(mockValidator).selectSuppliersReconciliationValidate(queryDTO1);
    }

    @Test
    public void testSettleSuppliersReconciliation() throws Exception {
        // Setup
        when(mockInOutDocumentService.settleSuppliersReconciliation(Arrays.asList("value"))).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/reconciliation/settle")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
        verify(mockValidator).settleSuppliersReconciliationValidate(Arrays.asList("value"));
    }

    @Test
    public void testSettleSuppliersReconciliation_InOutDocumentServiceReturnsTrue() throws Exception {
        // Setup
        when(mockInOutDocumentService.settleSuppliersReconciliation(Arrays.asList("value"))).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/reconciliation/settle")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
        verify(mockValidator).settleSuppliersReconciliationValidate(Arrays.asList("value"));
    }

    @Test
    public void testSelectFlowDetailListForPage() throws Exception {
        // Setup
        // Configure InOutDocumentService.selectFlowDetailListForPage(...).
        final InOutDocumentFolwDetailSelectDTO inOutDocumentFolwDetailSelectDTO = new InOutDocumentFolwDetailSelectDTO();
        inOutDocumentFolwDetailSelectDTO.setInOutType(0);
        inOutDocumentFolwDetailSelectDTO.setModifiedCount("modifiedCount");
        inOutDocumentFolwDetailSelectDTO.setGuid("3ed5203a-62f0-40e4-b27f-bafde5d1cc8a");
        inOutDocumentFolwDetailSelectDTO.setDocumentGuid("documentGuid");
        inOutDocumentFolwDetailSelectDTO.setMaterialGuid("materialGuid");
        final Page<InOutDocumentFolwDetailSelectDTO> inOutDocumentFolwDetailSelectDTOPage = new Page<>(0L, 0L,
                Arrays.asList(inOutDocumentFolwDetailSelectDTO));
        final InOutDocumentFlowDetailQueryDTO queryDTO = new InOutDocumentFlowDetailQueryDTO();
        queryDTO.setUserGuid("operatorGuid");
        queryDTO.setUserName("operatorName");
        queryDTO.setWarehouseGuidList(Arrays.asList("value"));
        queryDTO.setStartDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        queryDTO.setEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockInOutDocumentService.selectFlowDetailListForPage(queryDTO))
                .thenReturn(inOutDocumentFolwDetailSelectDTOPage);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/selectFlowDetailListForPage")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm InOutDocumentValidator.selectFlowDetailListForPage(...).
        final InOutDocumentFlowDetailQueryDTO queryDTO1 = new InOutDocumentFlowDetailQueryDTO();
        queryDTO1.setUserGuid("operatorGuid");
        queryDTO1.setUserName("operatorName");
        queryDTO1.setWarehouseGuidList(Arrays.asList("value"));
        queryDTO1.setStartDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        queryDTO1.setEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockValidator).selectFlowDetailListForPage(queryDTO1);
    }

    @Test
    public void testReduceStockForOrder() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/reduceStockForOrder")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
        verify(mockValidator).reduceStockForOrderValidate(any(OrderSkuDTO.class));
        verify(mockInOutDocumentService).publishOrderReduceStockMsg(any(OrderSkuDTO.class));
    }

    @Test
    public void testReduceStockForOrderBatch() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inOutDocument/reduceStockForOrderBatch")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm InOutDocumentValidator.reduceStockForOrderValidateBatch(...).
        final OrderSkuDTO orderSkuDTO = new OrderSkuDTO();
        orderSkuDTO.setOut(false);
        orderSkuDTO.setEnterpriseGuid("enterpriseGuid");
        orderSkuDTO.setOperatorGuid("operatorGuid");
        orderSkuDTO.setOperatorName("operatorName");
        final SkuInfo skuInfo = new SkuInfo();
        orderSkuDTO.setSkuList(Arrays.asList(skuInfo));
        final List<OrderSkuDTO> orderSkuDTOList = Arrays.asList(orderSkuDTO);
        verify(mockValidator).reduceStockForOrderValidateBatch(orderSkuDTOList);

        // Confirm InOutDocumentService.publishOrderReduceStockMsgBatch(...).
        final OrderSkuDTO orderSkuDTO1 = new OrderSkuDTO();
        orderSkuDTO1.setOut(false);
        orderSkuDTO1.setEnterpriseGuid("enterpriseGuid");
        orderSkuDTO1.setOperatorGuid("operatorGuid");
        orderSkuDTO1.setOperatorName("operatorName");
        final SkuInfo skuInfo1 = new SkuInfo();
        orderSkuDTO1.setSkuList(Arrays.asList(skuInfo1));
        final List<OrderSkuDTO> orderSkuDTOList1 = Arrays.asList(orderSkuDTO1);
        verify(mockInOutDocumentService).publishOrderReduceStockMsgBatch(orderSkuDTOList1);
    }
}
