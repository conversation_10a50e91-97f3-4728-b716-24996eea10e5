package com.holderzone.erp.controller;

import com.holderzone.erp.service.GoodsSerialService;
import com.holderzone.erp.service.GoodsService;
import com.holderzone.erp.service.InRepertoryService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.erpretail.GoodsExportDTO;
import com.holderzone.saas.store.dto.erp.erpretail.InOutGoodsDTO;
import com.holderzone.saas.store.dto.erp.erpretail.RepertorySumDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.*;
import com.holderzone.saas.store.dto.erp.erpretail.resp.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(SpringRunner.class)
@WebMvcTest(RepertoryController.class)
public class RepertoryControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private InRepertoryService mockInRepertoryService;
    @MockBean
    private GoodsService mockGoodsService;
    @MockBean
    private GoodsSerialService mockGoodsSerialService;

    @Test
    public void testInsertRepertory() throws Exception {
        // Setup
        // Configure InRepertoryService.insertRepertoryAndDetail(...).
        final CreateRepertoryReqDTO createRepertoryReqDTO = new CreateRepertoryReqDTO();
        createRepertoryReqDTO.setInvoiceType(0);
        createRepertoryReqDTO.setInOut(0);
        createRepertoryReqDTO.setOperatorGuid("operatorGuid");
        createRepertoryReqDTO.setOperatorName("operatorName");
        createRepertoryReqDTO.setInvoiceMakeTime("invoiceMakeTime");
        when(mockInRepertoryService.insertRepertoryAndDetail(createRepertoryReqDTO)).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/insert_repertory")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testInsertRepertory_InRepertoryServiceReturnsTrue() throws Exception {
        // Setup
        // Configure InRepertoryService.insertRepertoryAndDetail(...).
        final CreateRepertoryReqDTO createRepertoryReqDTO = new CreateRepertoryReqDTO();
        createRepertoryReqDTO.setInvoiceType(0);
        createRepertoryReqDTO.setInOut(0);
        createRepertoryReqDTO.setOperatorGuid("operatorGuid");
        createRepertoryReqDTO.setOperatorName("operatorName");
        createRepertoryReqDTO.setInvoiceMakeTime("invoiceMakeTime");
        when(mockInRepertoryService.insertRepertoryAndDetail(createRepertoryReqDTO)).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/insert_repertory")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSaleOutRepertory() throws Exception {
        // Setup
        // Configure InRepertoryService.saleOutRepertory(...).
        final SubstractRepertoryForTradeReqDTO substractRepertoryForTradeReqDTO = new SubstractRepertoryForTradeReqDTO();
        substractRepertoryForTradeReqDTO.setInvoiceType(0);
        substractRepertoryForTradeReqDTO.setInvoiceNo("invoiceNo");
        final SubstractGoodsReqDTO substractGoodsReqDTO = new SubstractGoodsReqDTO();
        substractGoodsReqDTO.setGoodsGuid("goodsGuid");
        substractGoodsReqDTO.setCount(new BigDecimal("0.00"));
        substractRepertoryForTradeReqDTO.setDetailList(Arrays.asList(substractGoodsReqDTO));
        when(mockInRepertoryService.saleOutRepertory(substractRepertoryForTradeReqDTO)).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/sale_out_repertory")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSaleOutRepertory_InRepertoryServiceReturnsTrue() throws Exception {
        // Setup
        // Configure InRepertoryService.saleOutRepertory(...).
        final SubstractRepertoryForTradeReqDTO substractRepertoryForTradeReqDTO = new SubstractRepertoryForTradeReqDTO();
        substractRepertoryForTradeReqDTO.setInvoiceType(0);
        substractRepertoryForTradeReqDTO.setInvoiceNo("invoiceNo");
        final SubstractGoodsReqDTO substractGoodsReqDTO = new SubstractGoodsReqDTO();
        substractGoodsReqDTO.setGoodsGuid("goodsGuid");
        substractGoodsReqDTO.setCount(new BigDecimal("0.00"));
        substractRepertoryForTradeReqDTO.setDetailList(Arrays.asList(substractGoodsReqDTO));
        when(mockInRepertoryService.saleOutRepertory(substractRepertoryForTradeReqDTO)).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/sale_out_repertory")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryGoodsRepertorySumInfo() throws Exception {
        // Setup
        // Configure InRepertoryService.queryGoodsRepertorySumInfo(...).
        final GoodsSumInfoRespDTO goodsSumInfoRespDTO = new GoodsSumInfoRespDTO();
        goodsSumInfoRespDTO.setGoodsGuid("goodsGuid");
        goodsSumInfoRespDTO.setGoodsCode("goodsCode");
        goodsSumInfoRespDTO.setGoodsName("goodsName");
        goodsSumInfoRespDTO.setGoodsClassifyName("goodsClassifyName");
        goodsSumInfoRespDTO.setCount(new BigDecimal("0.00"));
        final Page<GoodsSumInfoRespDTO> goodsSumInfoRespDTOPage = new Page<>(0L, 0L,
                Arrays.asList(goodsSumInfoRespDTO));
        final QueryGoodsSumInfoReqDTO queryGoodsSumInfoReqDTO = new QueryGoodsSumInfoReqDTO();
        queryGoodsSumInfoReqDTO.setGoodsClassifyGuid("goodsClassifyGuid");
        queryGoodsSumInfoReqDTO.setNameOrCode("nameOrCode");
        queryGoodsSumInfoReqDTO.setStoreGuid("storeGuid");
        when(mockInRepertoryService.queryGoodsRepertorySumInfo(queryGoodsSumInfoReqDTO))
                .thenReturn(goodsSumInfoRespDTOPage);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/query_goods_repertory_sum_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryGoodsSerial() throws Exception {
        // Setup
        // Configure GoodsSerialService.queryGoodsSerial(...).
        final GoodsSerialRespDTO goodsSerialRespDTO = new GoodsSerialRespDTO();
        goodsSerialRespDTO.setInvoiceName("invoiceName");
        goodsSerialRespDTO.setInvoiceType(0);
        goodsSerialRespDTO.setChangeNum(new BigDecimal("0.00"));
        goodsSerialRespDTO.setUnitName("unitName");
        goodsSerialRespDTO.setInvoiceTime("invoiceTime");
        final Page<GoodsSerialRespDTO> goodsSerialRespDTOPage = new Page<>(0L, 0L, Arrays.asList(goodsSerialRespDTO));
        final QueryGoodsSerialReqDTO queryGoodsSerialReqDTO = new QueryGoodsSerialReqDTO();
        queryGoodsSerialReqDTO.setStartDate("startDate");
        queryGoodsSerialReqDTO.setEndDate("endDate");
        queryGoodsSerialReqDTO.setInvoiceType(0);
        queryGoodsSerialReqDTO.setGoodsGuid("goodsGuid");
        queryGoodsSerialReqDTO.setStartDateTime("startDateTime");
        when(mockGoodsSerialService.queryGoodsSerial(queryGoodsSerialReqDTO)).thenReturn(goodsSerialRespDTOPage);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/query_goods_serial")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryRepertoryDetail() throws Exception {
        // Setup
        // Configure InRepertoryService.queryRepertoryDetail(...).
        final RepertoryDetailInfoRespDTO repertoryDetailInfoRespDTO = new RepertoryDetailInfoRespDTO();
        repertoryDetailInfoRespDTO.setGuid("7ca5476d-7915-4804-bbf5-da3c67f79e4f");
        repertoryDetailInfoRespDTO.setInvoiceName("invoiceName");
        repertoryDetailInfoRespDTO.setOperatorName("operatorName");
        repertoryDetailInfoRespDTO.setInvoiceMakeTime("invoiceMakeTime");
        repertoryDetailInfoRespDTO.setSupplierName("supplierName");
        when(mockInRepertoryService.queryRepertoryDetail("data")).thenReturn(repertoryDetailInfoRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/query_repertory_detail")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryInOutRepertoryList() throws Exception {
        // Setup
        // Configure InRepertoryService.queryRepertoryManageList(...).
        final RepertoryManageRespDTO repertoryManageRespDTO = new RepertoryManageRespDTO();
        repertoryManageRespDTO.setGuid("4855708b-6775-4bf8-8d0d-53f09ddd52e1");
        repertoryManageRespDTO.setInvoiceName("invoiceName");
        repertoryManageRespDTO.setInvoiceMakeTime("invoiceMakeTime");
        repertoryManageRespDTO.setStatus(0);
        repertoryManageRespDTO.setTotalAmount(new BigDecimal("0.00"));
        final Page<RepertoryManageRespDTO> repertoryManageRespDTOPage = new Page<>(0L, 0L,
                Arrays.asList(repertoryManageRespDTO));
        final QueryRepertoryManageReqDTO queryRepertoryManageReqDTO = new QueryRepertoryManageReqDTO();
        queryRepertoryManageReqDTO.setStatus("status");
        queryRepertoryManageReqDTO.setInvoiceType(0);
        queryRepertoryManageReqDTO.setGuid("106254f4-5576-45f9-975a-76207d89dbec");
        queryRepertoryManageReqDTO.setInOut(0);
        when(mockInRepertoryService.queryRepertoryManageList(queryRepertoryManageReqDTO))
                .thenReturn(repertoryManageRespDTOPage);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/query_in_out_repertory_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testInvalidRepertory() throws Exception {
        // Setup
        when(mockInRepertoryService.invalidRepertory(new SingleDataDTO("data", Arrays.asList("value"))))
                .thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/invalid_repertory")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testInvalidRepertory_InRepertoryServiceReturnsTrue() throws Exception {
        // Setup
        when(mockInRepertoryService.invalidRepertory(new SingleDataDTO("data", Arrays.asList("value"))))
                .thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/invalid_repertory")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryGoodsList() throws Exception {
        // Setup
        // Configure GoodsService.queryGoodsList(...).
        final GoodsClassifyAndItemRespDTO goodsClassifyAndItemRespDTO = new GoodsClassifyAndItemRespDTO();
        goodsClassifyAndItemRespDTO.setGoodsClassifyGuid("goodsClassifyGuid");
        goodsClassifyAndItemRespDTO.setGoodsClassifyName("goodsClassifyName");
        final InOutGoodsDTO inOutGoodsDTO = new InOutGoodsDTO();
        inOutGoodsDTO.setGoodsGuid("goodsGuid");
        inOutGoodsDTO.setGoodsCode("goodsCode");
        goodsClassifyAndItemRespDTO.setGoodsList(Arrays.asList(inOutGoodsDTO));
        final List<GoodsClassifyAndItemRespDTO> goodsClassifyAndItemRespDTOS = Arrays.asList(
                goodsClassifyAndItemRespDTO);
        when(mockGoodsService.queryGoodsList(new SingleDataDTO("data", Arrays.asList("value"))))
                .thenReturn(goodsClassifyAndItemRespDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/query_goods_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryGoodsList_GoodsServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockGoodsService.queryGoodsList(new SingleDataDTO("data", Arrays.asList("value"))))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/query_goods_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testModifyGoodsClassify() throws Exception {
        // Setup
        when(mockGoodsService.modifyClassifyName("classifyGuid", "classifyName")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/modify_goods_classify")
                        .param("classifyGuid", "classifyGuid")
                        .param("classifyName", "classifyName")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testModifyGoodsClassify_GoodsServiceReturnsTrue() throws Exception {
        // Setup
        when(mockGoodsService.modifyClassifyName("classifyGuid", "classifyName")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/modify_goods_classify")
                        .param("classifyGuid", "classifyGuid")
                        .param("classifyName", "classifyName")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testModifyGoodsInfo() throws Exception {
        // Setup
        // Configure GoodsService.modifyGoodsInfo(...).
        final InOutGoodsDTO inOutGoodsDTO = new InOutGoodsDTO();
        inOutGoodsDTO.setGoodsGuid("goodsGuid");
        inOutGoodsDTO.setGoodsCode("goodsCode");
        inOutGoodsDTO.setGoodsName("goodsName");
        inOutGoodsDTO.setBarCode("barCode");
        inOutGoodsDTO.setPinyin("pinyin");
        when(mockGoodsService.modifyGoodsInfo(inOutGoodsDTO)).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/modify_goods_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testModifyGoodsInfo_GoodsServiceReturnsTrue() throws Exception {
        // Setup
        // Configure GoodsService.modifyGoodsInfo(...).
        final InOutGoodsDTO inOutGoodsDTO = new InOutGoodsDTO();
        inOutGoodsDTO.setGoodsGuid("goodsGuid");
        inOutGoodsDTO.setGoodsCode("goodsCode");
        inOutGoodsDTO.setGoodsName("goodsName");
        inOutGoodsDTO.setBarCode("barCode");
        inOutGoodsDTO.setPinyin("pinyin");
        when(mockGoodsService.modifyGoodsInfo(inOutGoodsDTO)).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/modify_goods_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testCancelRelateRepertory() throws Exception {
        // Setup
        when(mockGoodsService.cancelRelateRepertory("goodsGuid")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/cancel_relate_repertory")
                        .param("goodsGuid", "goodsGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testCancelRelateRepertory_GoodsServiceReturnsTrue() throws Exception {
        // Setup
        when(mockGoodsService.cancelRelateRepertory("goodsGuid")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/cancel_relate_repertory")
                        .param("goodsGuid", "goodsGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryGoodsInfo1() throws Exception {
        // Setup
        // Configure GoodsService.queryGoodsInfo(...).
        final InOutGoodsDTO inOutGoodsDTO = new InOutGoodsDTO();
        inOutGoodsDTO.setGoodsGuid("goodsGuid");
        inOutGoodsDTO.setGoodsCode("goodsCode");
        inOutGoodsDTO.setGoodsName("goodsName");
        inOutGoodsDTO.setBarCode("barCode");
        inOutGoodsDTO.setPinyin("pinyin");
        when(mockGoodsService.queryGoodsInfo("goodsGuid")).thenReturn(inOutGoodsDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/query_goods_info")
                        .param("goodsGuid", "goodsGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryExportGoodsList() throws Exception {
        // Setup
        // Configure GoodsService.queryExportGoodsList(...).
        final List<GoodsExportDTO> goodsExportDTOS = Arrays.asList(
                new GoodsExportDTO("goodsGuid", new BigDecimal("0.00"), new BigDecimal("0.00")));
        when(mockGoodsService.queryExportGoodsList(Arrays.asList("value"))).thenReturn(goodsExportDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/query_export_goods_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryExportGoodsList_GoodsServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockGoodsService.queryExportGoodsList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/query_export_goods_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testQueryGoodsInfo2() throws Exception {
        // Setup
        // Configure GoodsService.queryRepertorySum(...).
        final RepertorySumDTO repertorySumDTO = new RepertorySumDTO();
        repertorySumDTO.setRepertorySum(new BigDecimal("0.00"));
        repertorySumDTO.setSkuSum(0);
        repertorySumDTO.setSevenDaySkuSum("sevenDaySkuSum");
        repertorySumDTO.setThirtyDaySkuSum("thirtyDaySkuSum");
        when(mockGoodsService.queryRepertorySum(new SingleDataDTO("data", Arrays.asList("value"))))
                .thenReturn(repertorySumDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/repertory/query_repertory_sum")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
