package com.holderzone.erp.controller;

import com.holderzone.erp.service.InventoryService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.CreateInventoryReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.InventoryOverviewReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.GoodsSumInfoRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.InventoryDetailRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.InventoryManageRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(SpringRunner.class)
@WebMvcTest(InventoryController.class)
public class InventoryControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private InventoryService mockInventoryService;

    @Test
    public void testCreateInventory() throws Exception {
        // Setup
        // Configure InventoryService.createInventory(...).
        final CreateInventoryReqDTO createInventoryReqDTO = new CreateInventoryReqDTO();
        createInventoryReqDTO.setInvoiceType("invoiceType");
        createInventoryReqDTO.setOperator("operator");
        createInventoryReqDTO.setInventoryDate("inventoryDate");
        createInventoryReqDTO.setRemark("remark");
        final GoodsSumInfoRespDTO goodsSumInfoRespDTO = new GoodsSumInfoRespDTO();
        createInventoryReqDTO.setGoodsList(Arrays.asList(goodsSumInfoRespDTO));
        when(mockInventoryService.createInventory(createInventoryReqDTO)).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inventory/create_inventory")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryInventoryDetail() throws Exception {
        // Setup
        // Configure InventoryService.queryInventoryDetail(...).
        final InventoryDetailRespDTO inventoryDetailRespDTO = new InventoryDetailRespDTO();
        inventoryDetailRespDTO.setInvoiceNo("invoiceNo");
        inventoryDetailRespDTO.setDate("date");
        inventoryDetailRespDTO.setInvoiceName("invoiceName");
        inventoryDetailRespDTO.setOperator("operator");
        inventoryDetailRespDTO.setInvoiceTime("invoiceTime");
        when(mockInventoryService.queryInventoryDetail(new SingleDataDTO("data", Arrays.asList("value"))))
                .thenReturn(inventoryDetailRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inventory/query_inventory_detail")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryInventoryOverView() throws Exception {
        // Setup
        // Configure InventoryService.queryInventoryOverview(...).
        final InventoryManageRespDTO inventoryManageRespDTO = new InventoryManageRespDTO();
        inventoryManageRespDTO.setInvoiceNo("invoiceNo");
        inventoryManageRespDTO.setInvoiceName("invoiceName");
        inventoryManageRespDTO.setInventoryDate("inventoryDate");
        inventoryManageRespDTO.setOperator("operator");
        inventoryManageRespDTO.setStatus(0);
        final Page<InventoryManageRespDTO> inventoryManageRespDTOPage = new Page<>(0L, 0L,
                Arrays.asList(inventoryManageRespDTO));
        final InventoryOverviewReqDTO inventoryOverviewReqDTO = new InventoryOverviewReqDTO();
        inventoryOverviewReqDTO.setStartDate("startDate");
        inventoryOverviewReqDTO.setEndDate("endDate");
        inventoryOverviewReqDTO.setType("type");
        inventoryOverviewReqDTO.setOrderStatus("orderStatus");
        inventoryOverviewReqDTO.setOperatorOrOrderNo("operatorOrOrderNo");
        when(mockInventoryService.queryInventoryOverview(inventoryOverviewReqDTO))
                .thenReturn(inventoryManageRespDTOPage);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inventory/query_inventory_overview")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testInvalidInventory() throws Exception {
        // Setup
        when(mockInventoryService.invalidInventory("data")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inventory/invalid_inventory")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testInvalidInventory_InventoryServiceReturnsTrue() throws Exception {
        // Setup
        when(mockInventoryService.invalidInventory("data")).thenReturn(true);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/inventory/invalid_inventory")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
