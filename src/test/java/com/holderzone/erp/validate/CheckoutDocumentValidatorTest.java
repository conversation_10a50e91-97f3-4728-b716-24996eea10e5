package com.holderzone.erp.validate;

import com.holderzone.erp.entity.domain.CheckoutDocumentDO;
import com.holderzone.erp.entity.domain.CheckoutDocumentDetailDO;
import com.holderzone.erp.service.CheckoutDocumentService;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.dto.erp.CheckoutDocumentAddOrUpdateDTO;
import com.holderzone.saas.store.dto.erp.CheckoutDocumentDetailAddOrUpdateDTO;
import com.holderzone.saas.store.dto.erp.CheckoutDocumentDetailQueryDTO;
import com.holderzone.saas.store.dto.erp.CheckoutDocumentQueryDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Calendar;
import java.util.GregorianCalendar;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CheckoutDocumentValidatorTest {

    @Mock
    private CheckoutDocumentService mockCheckoutDocumentService;

    @InjectMocks
    private CheckoutDocumentValidator checkoutDocumentValidatorUnderTest;

    @Test
    public void testAddOrUpdateCheckoutDocumentValidate() {
        // Setup
        final CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO = new CheckoutDocumentAddOrUpdateDTO();
        addOrUpdateDTO.setLock(false);
        final CheckoutDocumentDetailAddOrUpdateDTO checkoutDocumentDetailAddOrUpdateDTO = new CheckoutDocumentDetailAddOrUpdateDTO();
        checkoutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        checkoutDocumentDetailAddOrUpdateDTO.setMaterialGuid("materialGuid");
        checkoutDocumentDetailAddOrUpdateDTO.setMaterialCode("materialCode");
        checkoutDocumentDetailAddOrUpdateDTO.setMaterialName("materialName");
        checkoutDocumentDetailAddOrUpdateDTO.setStock(new BigDecimal("0.00"));
        checkoutDocumentDetailAddOrUpdateDTO.setUnitGuid("unitGuid");
        checkoutDocumentDetailAddOrUpdateDTO.setUnitName("unitName");
        checkoutDocumentDetailAddOrUpdateDTO.setCheckCount(new BigDecimal("0.00"));
        addOrUpdateDTO.setDetailList(Arrays.asList(checkoutDocumentDetailAddOrUpdateDTO));
        addOrUpdateDTO.setGuid("3a7f3632-3677-42ab-bcf6-8a9ccc34e200");
        addOrUpdateDTO.setCode("code");
        addOrUpdateDTO.setWarehouseGuid("warehouseGuid");
        addOrUpdateDTO.setWarehouseName("warehouseName");
        addOrUpdateDTO.setType(0);
        addOrUpdateDTO.setRemark("remark");

        // Run the test
        checkoutDocumentValidatorUnderTest.addOrUpdateCheckoutDocumentValidate(addOrUpdateDTO);

        // Verify the results
    }

    @Test
    public void testSelectDocumentDetailForAddValidate() {
        // Setup
        final CheckoutDocumentDetailQueryDTO queryDTO = new CheckoutDocumentDetailQueryDTO();
        queryDTO.setWarehouseGuid("warehouseGuid");
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setMaterialGuidList(Arrays.asList("value"));

        // Run the test
        checkoutDocumentValidatorUnderTest.selectDocumentDetailForAddValidate(queryDTO);

        // Verify the results
    }

    @Test
    public void testDeleteDocumentValidate() {
        // Setup
        // Configure CheckoutDocumentService.selectDocumentStatus(...).
        final CheckoutDocumentDO checkoutDocumentDO = new CheckoutDocumentDO();
        checkoutDocumentDO.setStoreGuid("storeGuid");
        final CheckoutDocumentDetailDO checkoutDocumentDetailDO = new CheckoutDocumentDetailDO();
        checkoutDocumentDetailDO.setMaterialCode("materialCode");
        checkoutDocumentDetailDO.setGuid("bad698f9-e77d-4d56-9d4e-4ec9e73d89a1");
        checkoutDocumentDO.setDetailList(Arrays.asList(checkoutDocumentDetailDO));
        checkoutDocumentDO.setStatus(0);
        when(mockCheckoutDocumentService.selectDocumentStatus("documentGuid")).thenReturn(checkoutDocumentDO);

        // Run the test
        checkoutDocumentValidatorUnderTest.deleteDocumentValidate("documentGuid");

        // Verify the results
    }

    @Test
    public void testDeleteDocumentValidate_CheckoutDocumentServiceReturnsNull() {
        // Setup
        when(mockCheckoutDocumentService.selectDocumentStatus("documentGuid")).thenReturn(null);

        // Run the test
        assertThatThrownBy(
                () -> checkoutDocumentValidatorUnderTest.deleteDocumentValidate("documentGuid"))
                .isInstanceOf(ParameterException.class);
    }

    @Test
    public void testSelectCheckoutDocumentForPageValidate() {
        // Setup
        final CheckoutDocumentQueryDTO queryDTO = new CheckoutDocumentQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setWarehouseGuidList(Arrays.asList("value"));
        queryDTO.setStartDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        queryDTO.setEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        checkoutDocumentValidatorUnderTest.selectCheckoutDocumentForPageValidate(queryDTO);

        // Verify the results
    }
}
