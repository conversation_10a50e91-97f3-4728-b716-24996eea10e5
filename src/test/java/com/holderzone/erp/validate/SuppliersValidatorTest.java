package com.holderzone.erp.validate;

import com.holderzone.saas.store.dto.erp.SuppliersMaterialQueryDTO;
import com.holderzone.saas.store.dto.erp.SuppliersQueryDTO;
import com.holderzone.saas.store.dto.erp.SuppliersReqDTO;
import org.junit.Test;

public class SuppliersValidatorTest {

    @Test
    public void testValidateCreateSuppliers() {
        // Setup
        final SuppliersReqDTO reqDTO = new SuppliersReqDTO();
        reqDTO.setEnabled(0);
        reqDTO.setGuid("6e4a03f6-aec7-40ae-b455-************");
        reqDTO.setName("name");
        reqDTO.setOfficeTel("officeTel");
        reqDTO.setContactName("contactName");
        reqDTO.setContactTel("contactTel");
        reqDTO.setAddr("addr");
        reqDTO.setSettlementInterval(0);
        reqDTO.setRemark("remark");
        reqDTO.setForeignKey("foreignKey");

        // Run the test
        SuppliersValidator.validateCreateSuppliers(reqDTO);

        // Verify the results
    }

    @Test
    public void testValidateUpdateSuppliers() {
        // Setup
        final SuppliersReqDTO reqDTO = new SuppliersReqDTO();
        reqDTO.setEnabled(0);
        reqDTO.setGuid("6e4a03f6-aec7-40ae-b455-************");
        reqDTO.setName("name");
        reqDTO.setOfficeTel("officeTel");
        reqDTO.setContactName("contactName");
        reqDTO.setContactTel("contactTel");
        reqDTO.setAddr("addr");
        reqDTO.setSettlementInterval(0);
        reqDTO.setRemark("remark");
        reqDTO.setForeignKey("foreignKey");

        // Run the test
        SuppliersValidator.validateUpdateSuppliers(reqDTO);

        // Verify the results
    }

    @Test
    public void testValidateGetSuppliersList() {
        // Setup
        final SuppliersQueryDTO queryDTO = new SuppliersQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setSearchConditions("searchConditions");
        queryDTO.setForeignKey("foreignKey");
        queryDTO.setEnabled(0);

        // Run the test
        SuppliersValidator.validateGetSuppliersList(queryDTO);

        // Verify the results
    }

    @Test
    public void testValidateGetAllOfSuppliersList() {
        // Setup
        final SuppliersQueryDTO queryDTO = new SuppliersQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setSearchConditions("searchConditions");
        queryDTO.setForeignKey("foreignKey");
        queryDTO.setEnabled(0);

        // Run the test
        SuppliersValidator.validateGetAllOfSuppliersList(queryDTO);

        // Verify the results
    }

    @Test
    public void testValidateGetSuppliersMaterialListAll() {
        // Setup
        final SuppliersMaterialQueryDTO queryDTO = new SuppliersMaterialQueryDTO();
        queryDTO.setSuppliersGuid("suppliersGuid");
        queryDTO.setSearchName("searchName");

        // Run the test
        SuppliersValidator.validateGetSuppliersMaterialListAll(queryDTO);

        // Verify the results
    }
}
