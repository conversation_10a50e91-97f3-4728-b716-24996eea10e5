package com.holderzone.erp.validate;

import com.holderzone.saas.store.dto.erp.WarehouseQueryDTO;
import com.holderzone.saas.store.dto.erp.WarehouseReqDTO;
import org.junit.Test;

public class WarehouseValidatorTest {

    @Test
    public void testValidateCreateWarehouse() {
        // Setup
        final WarehouseReqDTO reqDTO = new WarehouseReqDTO();
        reqDTO.setGuid("0f8e4af0-491d-4d33-ad90-6a5d49744c12");
        reqDTO.setName("name");
        reqDTO.setCode("code");
        reqDTO.setAddr("addr");
        reqDTO.setPic("pic");
        reqDTO.setTel("tel");
        reqDTO.setRemark("remark");
        reqDTO.setType(0);
        reqDTO.setForeignKey("foreignKey");

        // Run the test
        WarehouseValidator.validateCreateWarehouse(reqDTO);

        // Verify the results
    }

    @Test
    public void testValidateUpdateWarehouse() {
        // Setup
        final WarehouseReqDTO reqDTO = new WarehouseReqDTO();
        reqDTO.setGuid("0f8e4af0-491d-4d33-ad90-6a5d49744c12");
        reqDTO.setName("name");
        reqDTO.setCode("code");
        reqDTO.setAddr("addr");
        reqDTO.setPic("pic");
        reqDTO.setTel("tel");
        reqDTO.setRemark("remark");
        reqDTO.setType(0);
        reqDTO.setForeignKey("foreignKey");

        // Run the test
        WarehouseValidator.validateUpdateWarehouse(reqDTO);

        // Verify the results
    }

    @Test
    public void testValidateGetWarehouseList() {
        // Setup
        final WarehouseQueryDTO queryDTO = new WarehouseQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setSearchConditions("searchConditions");

        // Run the test
        WarehouseValidator.validateGetWarehouseList(queryDTO);

        // Verify the results
    }
}
