package com.holderzone.erp.validate;

import com.holderzone.saas.store.dto.erp.PricingReqDTO;
import com.holderzone.saas.store.dto.erp.PricingSchemesReqDTO;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.Arrays;

public class PricingSchemesValidatorTest {

    @Test
    public void testValidateSavePricingSchemes() {
        // Setup
        final PricingReqDTO pricingReqDTO = new PricingReqDTO();
        pricingReqDTO.setSuppliersGuid("suppliersGuid");
        final PricingSchemesReqDTO pricingSchemesReqDTO = new PricingSchemesReqDTO();
        pricingSchemesReqDTO.setMaterialGuid("materialGuid");
        pricingSchemesReqDTO.setDealPrice(new BigDecimal("0.00"));
        pricingSchemesReqDTO.setDealUnit("dealUnit");
        pricingReqDTO.setPricingSchemesList(Arrays.asList(pricingSchemesReqDTO));

        // Run the test
        PricingSchemesValidator.validateSavePricingSchemes(pricingReqDTO);

        // Verify the results
    }
}
