package com.holderzone.erp.validate;

import com.holderzone.erp.entity.bo.InOutDocumentDetailBO;
import com.holderzone.erp.entity.domain.InOutDocumentDO;
import com.holderzone.erp.service.InOutDocumentService;
import com.holderzone.erp.service.SuppliersService;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.dto.erp.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class InOutDocumentValidatorTest {

    @Mock
    private InOutDocumentService mockInOutDocumentService;
    @Mock
    private SuppliersService mockSuppliersService;

    @InjectMocks
    private InOutDocumentValidator inOutDocumentValidatorUnderTest;

    @Test
    public void testInsertOrUpdateInOutDocumentValidate() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        inOutDocumentDTO.setWarehouseName("warehouseName");
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setStock(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialCode("materialCode");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialName("materialName");
        inOutDocumentDetailAddOrUpdateDTO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setUnitGuid("unitGuid");
        inOutDocumentDetailAddOrUpdateDTO.setUnitName("unitName");
        inOutDocumentDetailAddOrUpdateDTO.setUnitPrice(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("8b243827-8280-47a8-b1a6-817283d44c1d");
        inOutDocumentDTO.setSupplierGuid("supplierGuid");
        inOutDocumentDTO.setSupplierName("supplierName");
        inOutDocumentDTO.setCode("code");
        inOutDocumentDTO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDTO.setType(0);
        inOutDocumentDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setShouldPayAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setRemark("remark");

        // Configure InOutDocumentService.selectDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setSettleStatus(0);
        inOutDocumentDO.setGmtCreate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inOutDocumentDO.setCreateStaffGuid("createStaffGuid");
        inOutDocumentDO.setStatus(0);
        when(mockInOutDocumentService.selectDocumentStatus("8b243827-8280-47a8-b1a6-817283d44c1d"))
                .thenReturn(inOutDocumentDO);

        // Configure SuppliersService.getSuppliersStatus(...).
        final SuppliersDTO suppliersDTO = new SuppliersDTO();
        suppliersDTO.setEnabled(0);
        suppliersDTO.setDeleted(0);
        suppliersDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGuid("4b124452-61ea-4f9a-9190-847515960fd4");
        when(mockSuppliersService.getSuppliersStatus("supplierGuid")).thenReturn(suppliersDTO);

        // Run the test
        inOutDocumentValidatorUnderTest.insertOrUpdateInOutDocumentValidate(inOutDocumentDTO);

        // Verify the results
        // Confirm InOutDocumentService.documentDetailOutCountValidate(...).
        final InOutDocumentDetailBO inOutDocumentDetailBO = new InOutDocumentDetailBO();
        inOutDocumentDetailBO.setStock(new BigDecimal("0.00"));
        inOutDocumentDetailBO.setMainUnitGuid("mainUnitGuid");
        inOutDocumentDetailBO.setMainUnitName("mainUnitName");
        inOutDocumentDetailBO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailBO.setGuid("a57fafe2-94e6-43bd-b2b5-cbb249bc7207");
        final List<InOutDocumentDetailBO> detailBOList = Arrays.asList(inOutDocumentDetailBO);
        verify(mockInOutDocumentService).documentDetailOutCountValidate("contactDocumentGuid", detailBOList);
    }

    @Test
    public void testInsertOrUpdateInOutDocumentValidate_InOutDocumentServiceSelectDocumentStatusReturnsNull() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        inOutDocumentDTO.setWarehouseName("warehouseName");
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setStock(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialCode("materialCode");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialName("materialName");
        inOutDocumentDetailAddOrUpdateDTO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setUnitGuid("unitGuid");
        inOutDocumentDetailAddOrUpdateDTO.setUnitName("unitName");
        inOutDocumentDetailAddOrUpdateDTO.setUnitPrice(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("8b243827-8280-47a8-b1a6-817283d44c1d");
        inOutDocumentDTO.setSupplierGuid("supplierGuid");
        inOutDocumentDTO.setSupplierName("supplierName");
        inOutDocumentDTO.setCode("code");
        inOutDocumentDTO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDTO.setType(0);
        inOutDocumentDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setShouldPayAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setRemark("remark");

        when(mockInOutDocumentService.selectDocumentStatus("8b243827-8280-47a8-b1a6-817283d44c1d")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inOutDocumentValidatorUnderTest.insertOrUpdateInOutDocumentValidate(
                inOutDocumentDTO)).isInstanceOf(ParameterException.class);

        // Confirm InOutDocumentService.documentDetailOutCountValidate(...).
        final InOutDocumentDetailBO inOutDocumentDetailBO = new InOutDocumentDetailBO();
        inOutDocumentDetailBO.setStock(new BigDecimal("0.00"));
        inOutDocumentDetailBO.setMainUnitGuid("mainUnitGuid");
        inOutDocumentDetailBO.setMainUnitName("mainUnitName");
        inOutDocumentDetailBO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailBO.setGuid("a57fafe2-94e6-43bd-b2b5-cbb249bc7207");
        final List<InOutDocumentDetailBO> detailBOList = Arrays.asList(inOutDocumentDetailBO);
        verify(mockInOutDocumentService).documentDetailOutCountValidate("contactDocumentGuid", detailBOList);
    }

    @Test
    public void testInsertOrUpdateInOutDocumentValidate_SuppliersServiceReturnsNull() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        inOutDocumentDTO.setWarehouseName("warehouseName");
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setStock(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialCode("materialCode");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialName("materialName");
        inOutDocumentDetailAddOrUpdateDTO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setUnitGuid("unitGuid");
        inOutDocumentDetailAddOrUpdateDTO.setUnitName("unitName");
        inOutDocumentDetailAddOrUpdateDTO.setUnitPrice(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("8b243827-8280-47a8-b1a6-817283d44c1d");
        inOutDocumentDTO.setSupplierGuid("supplierGuid");
        inOutDocumentDTO.setSupplierName("supplierName");
        inOutDocumentDTO.setCode("code");
        inOutDocumentDTO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDTO.setType(0);
        inOutDocumentDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setShouldPayAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setRemark("remark");

        // Configure InOutDocumentService.selectDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setSettleStatus(0);
        inOutDocumentDO.setGmtCreate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inOutDocumentDO.setCreateStaffGuid("createStaffGuid");
        inOutDocumentDO.setStatus(0);
        when(mockInOutDocumentService.selectDocumentStatus("8b243827-8280-47a8-b1a6-817283d44c1d"))
                .thenReturn(inOutDocumentDO);

        when(mockSuppliersService.getSuppliersStatus("supplierGuid")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inOutDocumentValidatorUnderTest.insertOrUpdateInOutDocumentValidate(
                inOutDocumentDTO)).isInstanceOf(ParameterException.class);

        // Confirm InOutDocumentService.documentDetailOutCountValidate(...).
        final InOutDocumentDetailBO inOutDocumentDetailBO = new InOutDocumentDetailBO();
        inOutDocumentDetailBO.setStock(new BigDecimal("0.00"));
        inOutDocumentDetailBO.setMainUnitGuid("mainUnitGuid");
        inOutDocumentDetailBO.setMainUnitName("mainUnitName");
        inOutDocumentDetailBO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailBO.setGuid("a57fafe2-94e6-43bd-b2b5-cbb249bc7207");
        final List<InOutDocumentDetailBO> detailBOList = Arrays.asList(inOutDocumentDetailBO);
        verify(mockInOutDocumentService).documentDetailOutCountValidate("contactDocumentGuid", detailBOList);
    }

    @Test
    public void testDocumentDetailOutCountValidate() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        inOutDocumentDTO.setWarehouseName("warehouseName");
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setStock(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialCode("materialCode");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialName("materialName");
        inOutDocumentDetailAddOrUpdateDTO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setUnitGuid("unitGuid");
        inOutDocumentDetailAddOrUpdateDTO.setUnitName("unitName");
        inOutDocumentDetailAddOrUpdateDTO.setUnitPrice(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("8b243827-8280-47a8-b1a6-817283d44c1d");
        inOutDocumentDTO.setSupplierGuid("supplierGuid");
        inOutDocumentDTO.setSupplierName("supplierName");
        inOutDocumentDTO.setCode("code");
        inOutDocumentDTO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDTO.setType(0);
        inOutDocumentDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setShouldPayAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setRemark("remark");

        // Run the test
        inOutDocumentValidatorUnderTest.documentDetailOutCountValidate(inOutDocumentDTO);

        // Verify the results
        // Confirm InOutDocumentService.documentDetailOutCountValidate(...).
        final InOutDocumentDetailBO inOutDocumentDetailBO = new InOutDocumentDetailBO();
        inOutDocumentDetailBO.setStock(new BigDecimal("0.00"));
        inOutDocumentDetailBO.setMainUnitGuid("mainUnitGuid");
        inOutDocumentDetailBO.setMainUnitName("mainUnitName");
        inOutDocumentDetailBO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailBO.setGuid("a57fafe2-94e6-43bd-b2b5-cbb249bc7207");
        final List<InOutDocumentDetailBO> detailBOList = Arrays.asList(inOutDocumentDetailBO);
        verify(mockInOutDocumentService).documentDetailOutCountValidate("contactDocumentGuid", detailBOList);
    }

    @Test
    public void testDetailDocumentGuidValidate() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        inOutDocumentDTO.setWarehouseName("warehouseName");
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setStock(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialCode("materialCode");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialName("materialName");
        inOutDocumentDetailAddOrUpdateDTO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setUnitGuid("unitGuid");
        inOutDocumentDetailAddOrUpdateDTO.setUnitName("unitName");
        inOutDocumentDetailAddOrUpdateDTO.setUnitPrice(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("8b243827-8280-47a8-b1a6-817283d44c1d");
        inOutDocumentDTO.setSupplierGuid("supplierGuid");
        inOutDocumentDTO.setSupplierName("supplierName");
        inOutDocumentDTO.setCode("code");
        inOutDocumentDTO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDTO.setType(0);
        inOutDocumentDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setShouldPayAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setRemark("remark");

        // Run the test
        inOutDocumentValidatorUnderTest.detailDocumentGuidValidate(inOutDocumentDTO);

        // Verify the results
    }

    @Test
    public void testSelectMaterialListValidate() {
        // Setup
        final InOutDocumentDetailQueryDTO materialQueryDTO = new InOutDocumentDetailQueryDTO();
        materialQueryDTO.setWarehouseGuid("warehouseGuid");
        materialQueryDTO.setSupplierGuid("supplierGuid");
        materialQueryDTO.setStoreGuid("storeGuid");
        materialQueryDTO.setMaterialGuidList(Arrays.asList("value"));

        // Run the test
        inOutDocumentValidatorUnderTest.selectMaterialListValidate(materialQueryDTO);

        // Verify the results
    }

    @Test
    public void testSelectDocumentListForPageValidate() {
        // Setup
        final InOutDocumentQueryDTO queryDTO = new InOutDocumentQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setWarehouseGuidList(Arrays.asList("value"));
        queryDTO.setStatus(0);
        queryDTO.setInOutType(0);

        // Run the test
        inOutDocumentValidatorUnderTest.selectDocumentListForPageValidate(queryDTO);

        // Verify the results
    }

    @Test
    public void testSelectSuppliersReconciliationValidate() {
        // Setup
        final SuppliersReconciliationQueryDTO queryDTO = new SuppliersReconciliationQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setGuid("c8425e39-e8ac-4da5-81b4-11248c166450");
        queryDTO.setSupplierGuid("supplierGuid");
        queryDTO.setStatus(0);

        // Run the test
        inOutDocumentValidatorUnderTest.selectSuppliersReconciliationValidate(queryDTO);

        // Verify the results
    }

    @Test
    public void testSelectDocumentGuidListValidate() {
        // Setup
        final InOutContactDocumentQueryDTO queryDTO = new InOutContactDocumentQueryDTO();
        queryDTO.setSupplierGuid("supplierGuid");
        queryDTO.setWarehouseGuid("warehouseGuid");
        queryDTO.setDocumentGuid("documentGuid");

        // Run the test
        inOutDocumentValidatorUnderTest.selectDocumentGuidListValidate(queryDTO);

        // Verify the results
    }

    @Test
    public void testSelectFlowDetailListForPage() {
        // Setup
        final InOutDocumentFlowDetailQueryDTO queryDTO = new InOutDocumentFlowDetailQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setWarehouseGuidList(Arrays.asList("value"));
        queryDTO.setStartDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        queryDTO.setEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        inOutDocumentValidatorUnderTest.selectFlowDetailListForPage(queryDTO);

        // Verify the results
    }

    @Test
    public void testSettleSuppliersReconciliationValidate() {
        // Setup
        // Run the test
        inOutDocumentValidatorUnderTest.settleSuppliersReconciliationValidate(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testReduceStockForOrderValidate() {
        // Setup
        final OrderSkuDTO orderSkuDTO = new OrderSkuDTO();
        orderSkuDTO.setOperatorGuid("operatorGuid");
        orderSkuDTO.setOperatorName("operatorName");
        orderSkuDTO.setStoreGuid("storeGuid");
        final SkuInfo skuInfo = new SkuInfo();
        skuInfo.setSkuGuid("skuGuid");
        skuInfo.setCount(new BigDecimal("0.00"));
        orderSkuDTO.setSkuList(Arrays.asList(skuInfo));

        // Run the test
        inOutDocumentValidatorUnderTest.reduceStockForOrderValidate(orderSkuDTO);

        // Verify the results
    }

    @Test
    public void testReduceStockForOrderValidateBatch() {
        // Setup
        final OrderSkuDTO orderSkuDTO = new OrderSkuDTO();
        orderSkuDTO.setOperatorGuid("operatorGuid");
        orderSkuDTO.setOperatorName("operatorName");
        orderSkuDTO.setStoreGuid("storeGuid");
        final SkuInfo skuInfo = new SkuInfo();
        skuInfo.setSkuGuid("skuGuid");
        skuInfo.setCount(new BigDecimal("0.00"));
        orderSkuDTO.setSkuList(Arrays.asList(skuInfo));
        final List<OrderSkuDTO> orderSkuDTOList = Arrays.asList(orderSkuDTO);

        // Run the test
        inOutDocumentValidatorUnderTest.reduceStockForOrderValidateBatch(orderSkuDTOList);

        // Verify the results
    }
}
