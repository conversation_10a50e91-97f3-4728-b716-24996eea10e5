package com.holderzone.erp.service.impl;

import com.holderzone.erp.dao.GoodsBomDOMapper;
import com.holderzone.erp.dao.MaterialDOMapper;
import com.holderzone.erp.entity.bo.UnitConvertBO;
import com.holderzone.erp.entity.domain.ChangeUnitDO;
import com.holderzone.erp.entity.domain.GoodsBomDOExample;
import com.holderzone.erp.entity.domain.MaterialDO;
import com.holderzone.erp.entity.domain.MaterialDOExample;
import com.holderzone.erp.entity.domain.read.MaterialConsumeQuery;
import com.holderzone.erp.entity.domain.read.MaterialConsumeReadDO;
import com.holderzone.erp.entity.domain.read.MaterialDocDetailReadDO;
import com.holderzone.erp.mapperstruct.ErpModuleMapper;
import com.holderzone.erp.mapperstruct.MaterialMapstruct;
import com.holderzone.erp.service.IBomService;
import com.holderzone.erp.service.PricingSchemesService;
import com.holderzone.erp.utils.PageAdapter;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.MaterialConsumeReqDTO;
import com.holderzone.saas.store.dto.erp.MaterialConsumeRespDTO;
import com.holderzone.saas.store.dto.erp.MaterialDTO;
import com.holderzone.saas.store.dto.erp.MaterialQueryDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MaterialServiceImplTest {

    @Mock
    private MaterialDOMapper mockMaterialDOMapper;
    @Mock
    private ErpModuleMapper mockModuleMapper;
    @Mock
    private GoodsBomDOMapper mockGoodsBomDOMapper;
    @Mock
    private IBomService mockBomService;
    @Mock
    private PricingSchemesService mockPricingSchemesService;
    @Mock
    private MaterialMapstruct mockMaterialMapstruct;

    private MaterialServiceImpl materialServiceImplUnderTest;

    @Before
    public void setUp() {
        materialServiceImplUnderTest = new MaterialServiceImpl(mockMaterialDOMapper, mockModuleMapper,
                mockGoodsBomDOMapper, mockBomService, mockPricingSchemesService, mockMaterialMapstruct);
    }

    @Test
    public void testAdd() {
        // Setup
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");

        // Configure ErpModuleMapper.mapToMaterialDO(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO.setName("name");
        materialDO.setUnit("unitGuid");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setCode("code");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO.setLowestStock(new BigDecimal("0.00"));
        materialDO.setEnabled(false);
        materialDO.setDeleted(false);
        materialDO.setUnitName("unitName");
        final MaterialDTO materialDTO1 = new MaterialDTO();
        materialDTO1.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO1.setName("name");
        materialDTO1.setUnit("newMainUnit");
        materialDTO1.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO1.setCode("code");
        materialDTO1.setStoreGuid("storeGuid");
        when(mockModuleMapper.mapToMaterialDO(materialDTO1)).thenReturn(materialDO);

        when(mockMaterialDOMapper.insertSelective(any(MaterialDO.class))).thenReturn(0);

        // Run the test
        final boolean result = materialServiceImplUnderTest.add(materialDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testUpdate() {
        // Setup
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");

        // Configure ErpModuleMapper.mapToMaterialDO(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO.setName("name");
        materialDO.setUnit("unitGuid");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setCode("code");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO.setLowestStock(new BigDecimal("0.00"));
        materialDO.setEnabled(false);
        materialDO.setDeleted(false);
        materialDO.setUnitName("unitName");
        final MaterialDTO materialDTO1 = new MaterialDTO();
        materialDTO1.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO1.setName("name");
        materialDTO1.setUnit("newMainUnit");
        materialDTO1.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO1.setCode("code");
        materialDTO1.setStoreGuid("storeGuid");
        when(mockModuleMapper.mapToMaterialDO(materialDTO1)).thenReturn(materialDO);

        // Configure MaterialDOMapper.selectByExample(...).
        final MaterialDO materialDO1 = new MaterialDO();
        materialDO1.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO1.setName("name");
        materialDO1.setUnit("unitGuid");
        materialDO1.setAuxiliaryUnit("auxiliaryUnit");
        materialDO1.setCode("code");
        materialDO1.setConversionMain(new BigDecimal("0.00"));
        materialDO1.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO1.setLowestStock(new BigDecimal("0.00"));
        materialDO1.setEnabled(false);
        materialDO1.setDeleted(false);
        materialDO1.setUnitName("unitName");
        final List<MaterialDO> materialDOS = Arrays.asList(materialDO1);
        when(mockMaterialDOMapper.selectByExample(any(MaterialDOExample.class))).thenReturn(materialDOS);

        when(mockMaterialDOMapper.updateByExampleSelective(any(MaterialDO.class),
                any(MaterialDOExample.class))).thenReturn(0);

        // Run the test
        final boolean result = materialServiceImplUnderTest.update(materialDTO);

        // Verify the results
        assertThat(result).isFalse();

        // Confirm IBomService.updateBomUnit(...).
        final MaterialDTO materialDTO2 = new MaterialDTO();
        materialDTO2.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO2.setName("name");
        materialDTO2.setUnit("newMainUnit");
        materialDTO2.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO2.setCode("code");
        materialDTO2.setStoreGuid("storeGuid");
        verify(mockBomService).updateBomUnit(eq(materialDTO2), any(MaterialDO.class));
        verify(mockPricingSchemesService).changeMaterialUnitInPricingSchemes(any(ChangeUnitDO.class));
    }

    @Test
    public void testUpdate_MaterialDOMapperSelectByExampleReturnsNoItems() {
        // Setup
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");

        // Configure ErpModuleMapper.mapToMaterialDO(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO.setName("name");
        materialDO.setUnit("unitGuid");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setCode("code");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO.setLowestStock(new BigDecimal("0.00"));
        materialDO.setEnabled(false);
        materialDO.setDeleted(false);
        materialDO.setUnitName("unitName");
        final MaterialDTO materialDTO1 = new MaterialDTO();
        materialDTO1.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO1.setName("name");
        materialDTO1.setUnit("newMainUnit");
        materialDTO1.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO1.setCode("code");
        materialDTO1.setStoreGuid("storeGuid");
        when(mockModuleMapper.mapToMaterialDO(materialDTO1)).thenReturn(materialDO);

        when(mockMaterialDOMapper.selectByExample(any(MaterialDOExample.class))).thenReturn(Collections.emptyList());
        when(mockMaterialDOMapper.updateByExampleSelective(any(MaterialDO.class),
                any(MaterialDOExample.class))).thenReturn(0);

        // Run the test
        final boolean result = materialServiceImplUnderTest.update(materialDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testDelete() {
        // Setup
        when(mockMaterialDOMapper.updateByExampleSelective(any(MaterialDO.class),
                any(MaterialDOExample.class))).thenReturn(0);

        // Run the test
        final boolean result = materialServiceImplUnderTest.delete("83483497-403d-48cc-8961-958b7725623a");

        // Verify the results
        assertThat(result).isFalse();
        verify(mockGoodsBomDOMapper).deleteByExample(any(GoodsBomDOExample.class));
    }

    @Test
    public void testFindByCondition() {
        // Setup
        final MaterialQueryDTO queryDTO = new MaterialQueryDTO();
        queryDTO.setCategory("category");
        queryDTO.setState(0);
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setWarehouseGuid("warehouseGuid");
        queryDTO.setMaterialGuidList(Arrays.asList("value"));

        final Page page = new Page<>(0L, 0L, Arrays.asList());
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");
        final List<MaterialDTO> expectedResult = Arrays.asList(materialDTO);

        // Configure MaterialDOMapper.findByCondition(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO.setName("name");
        materialDO.setUnit("unitGuid");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setCode("code");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO.setLowestStock(new BigDecimal("0.00"));
        materialDO.setEnabled(false);
        materialDO.setDeleted(false);
        materialDO.setUnitName("unitName");
        final List<MaterialDO> materialDOS = Arrays.asList(materialDO);
        final MaterialQueryDTO materialQueryDTO = new MaterialQueryDTO();
        materialQueryDTO.setCategory("category");
        materialQueryDTO.setState(0);
        materialQueryDTO.setStoreGuid("storeGuid");
        materialQueryDTO.setWarehouseGuid("warehouseGuid");
        materialQueryDTO.setMaterialGuidList(Arrays.asList("value"));
        when(mockMaterialDOMapper.findByCondition(eq(materialQueryDTO), any(Page.class))).thenReturn(materialDOS);

        // Configure ErpModuleMapper.mapToMaterialDTOList(...).
        final MaterialDTO materialDTO1 = new MaterialDTO();
        materialDTO1.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO1.setName("name");
        materialDTO1.setUnit("newMainUnit");
        materialDTO1.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO1.setCode("code");
        materialDTO1.setStoreGuid("storeGuid");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO1);
        final MaterialDO materialDO1 = new MaterialDO();
        materialDO1.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO1.setName("name");
        materialDO1.setUnit("unitGuid");
        materialDO1.setAuxiliaryUnit("auxiliaryUnit");
        materialDO1.setCode("code");
        materialDO1.setConversionMain(new BigDecimal("0.00"));
        materialDO1.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO1.setLowestStock(new BigDecimal("0.00"));
        materialDO1.setEnabled(false);
        materialDO1.setDeleted(false);
        materialDO1.setUnitName("unitName");
        final List<MaterialDO> list = Arrays.asList(materialDO1);
        when(mockModuleMapper.mapToMaterialDTOList(list)).thenReturn(materialDTOS);

        // Run the test
        final List<MaterialDTO> result = materialServiceImplUnderTest.findByCondition(queryDTO, page);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindByCondition_MaterialDOMapperReturnsNoItems() {
        // Setup
        final MaterialQueryDTO queryDTO = new MaterialQueryDTO();
        queryDTO.setCategory("category");
        queryDTO.setState(0);
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setWarehouseGuid("warehouseGuid");
        queryDTO.setMaterialGuidList(Arrays.asList("value"));

        final Page page = new Page<>(0L, 0L, Arrays.asList());
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");
        final List<MaterialDTO> expectedResult = Arrays.asList(materialDTO);

        // Configure MaterialDOMapper.findByCondition(...).
        final MaterialQueryDTO materialQueryDTO = new MaterialQueryDTO();
        materialQueryDTO.setCategory("category");
        materialQueryDTO.setState(0);
        materialQueryDTO.setStoreGuid("storeGuid");
        materialQueryDTO.setWarehouseGuid("warehouseGuid");
        materialQueryDTO.setMaterialGuidList(Arrays.asList("value"));
        when(mockMaterialDOMapper.findByCondition(eq(materialQueryDTO), any(Page.class)))
                .thenReturn(Collections.emptyList());

        // Configure ErpModuleMapper.mapToMaterialDTOList(...).
        final MaterialDTO materialDTO1 = new MaterialDTO();
        materialDTO1.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO1.setName("name");
        materialDTO1.setUnit("newMainUnit");
        materialDTO1.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO1.setCode("code");
        materialDTO1.setStoreGuid("storeGuid");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO1);
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO.setName("name");
        materialDO.setUnit("unitGuid");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setCode("code");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO.setLowestStock(new BigDecimal("0.00"));
        materialDO.setEnabled(false);
        materialDO.setDeleted(false);
        materialDO.setUnitName("unitName");
        final List<MaterialDO> list = Arrays.asList(materialDO);
        when(mockModuleMapper.mapToMaterialDTOList(list)).thenReturn(materialDTOS);

        // Run the test
        final List<MaterialDTO> result = materialServiceImplUnderTest.findByCondition(queryDTO, page);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindByCondition_ErpModuleMapperReturnsNoItems() {
        // Setup
        final MaterialQueryDTO queryDTO = new MaterialQueryDTO();
        queryDTO.setCategory("category");
        queryDTO.setState(0);
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setWarehouseGuid("warehouseGuid");
        queryDTO.setMaterialGuidList(Arrays.asList("value"));

        final Page page = new Page<>(0L, 0L, Arrays.asList());

        // Configure MaterialDOMapper.findByCondition(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO.setName("name");
        materialDO.setUnit("unitGuid");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setCode("code");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO.setLowestStock(new BigDecimal("0.00"));
        materialDO.setEnabled(false);
        materialDO.setDeleted(false);
        materialDO.setUnitName("unitName");
        final List<MaterialDO> materialDOS = Arrays.asList(materialDO);
        final MaterialQueryDTO materialQueryDTO = new MaterialQueryDTO();
        materialQueryDTO.setCategory("category");
        materialQueryDTO.setState(0);
        materialQueryDTO.setStoreGuid("storeGuid");
        materialQueryDTO.setWarehouseGuid("warehouseGuid");
        materialQueryDTO.setMaterialGuidList(Arrays.asList("value"));
        when(mockMaterialDOMapper.findByCondition(eq(materialQueryDTO), any(Page.class))).thenReturn(materialDOS);

        // Configure ErpModuleMapper.mapToMaterialDTOList(...).
        final MaterialDO materialDO1 = new MaterialDO();
        materialDO1.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO1.setName("name");
        materialDO1.setUnit("unitGuid");
        materialDO1.setAuxiliaryUnit("auxiliaryUnit");
        materialDO1.setCode("code");
        materialDO1.setConversionMain(new BigDecimal("0.00"));
        materialDO1.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO1.setLowestStock(new BigDecimal("0.00"));
        materialDO1.setEnabled(false);
        materialDO1.setDeleted(false);
        materialDO1.setUnitName("unitName");
        final List<MaterialDO> list = Arrays.asList(materialDO1);
        when(mockModuleMapper.mapToMaterialDTOList(list)).thenReturn(Collections.emptyList());

        // Run the test
        final List<MaterialDTO> result = materialServiceImplUnderTest.findByCondition(queryDTO, page);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testFindByGuid() {
        // Setup
        final MaterialDTO expectedResult = new MaterialDTO();
        expectedResult.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        expectedResult.setName("name");
        expectedResult.setUnit("newMainUnit");
        expectedResult.setAuxiliaryUnit("newAuxiliaryUnit");
        expectedResult.setCode("code");
        expectedResult.setStoreGuid("storeGuid");

        // Configure MaterialDOMapper.findAllByWarehouseOrGuidList(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO.setName("name");
        materialDO.setUnit("unitGuid");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setCode("code");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO.setLowestStock(new BigDecimal("0.00"));
        materialDO.setEnabled(false);
        materialDO.setDeleted(false);
        materialDO.setUnitName("unitName");
        final List<MaterialDO> materialDOS = Arrays.asList(materialDO);
        when(mockMaterialDOMapper.findAllByWarehouseOrGuidList("storeGuid", Arrays.asList("value")))
                .thenReturn(materialDOS);

        // Configure ErpModuleMapper.mapToMaterialDTO(...).
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");
        when(mockModuleMapper.mapToMaterialDTO(any(MaterialDO.class))).thenReturn(materialDTO);

        // Run the test
        final MaterialDTO result = materialServiceImplUnderTest.findByGuid("798bca34-c702-4cf8-a645-3ffa5a565f92");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindByGuid_MaterialDOMapperReturnsNoItems() {
        // Setup
        when(mockMaterialDOMapper.findAllByWarehouseOrGuidList("storeGuid", Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MaterialDTO result = materialServiceImplUnderTest.findByGuid("798bca34-c702-4cf8-a645-3ffa5a565f92");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testCheckNameOrCode() {
        // Setup
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");

        when(mockMaterialDOMapper.countByExample(any(MaterialDOExample.class))).thenReturn(0L);

        // Run the test
        materialServiceImplUnderTest.checkNameOrCode(materialDTO);

        // Verify the results
    }

    @Test
    public void testBatchCheckNameOrCode_ThrowsBusinessException() {
        // Setup
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");
        final List<MaterialDTO> materialDTOList = Arrays.asList(materialDTO);

        // Configure MaterialDOMapper.selectByExample(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO.setName("name");
        materialDO.setUnit("unitGuid");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setCode("code");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO.setLowestStock(new BigDecimal("0.00"));
        materialDO.setEnabled(false);
        materialDO.setDeleted(false);
        materialDO.setUnitName("unitName");
        final List<MaterialDO> materialDOS = Arrays.asList(materialDO);
        when(mockMaterialDOMapper.selectByExample(any(MaterialDOExample.class))).thenReturn(materialDOS);

        // Run the test
        assertThatThrownBy(() -> materialServiceImplUnderTest.batchCheckNameOrCode(materialDTOList))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testBatchCheckNameOrCode_MaterialDOMapperReturnsNoItems() {
        // Setup
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");
        final List<MaterialDTO> materialDTOList = Arrays.asList(materialDTO);
        when(mockMaterialDOMapper.selectByExample(any(MaterialDOExample.class))).thenReturn(Collections.emptyList());

        // Run the test
        materialServiceImplUnderTest.batchCheckNameOrCode(materialDTOList);

        // Verify the results
    }

    @Test
    public void testBatchCheckCode_ThrowsBusinessException() {
        // Setup
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");
        final List<MaterialDTO> materialDTOList = Arrays.asList(materialDTO);

        // Configure MaterialDOMapper.selectByExample(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO.setName("name");
        materialDO.setUnit("unitGuid");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setCode("code");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO.setLowestStock(new BigDecimal("0.00"));
        materialDO.setEnabled(false);
        materialDO.setDeleted(false);
        materialDO.setUnitName("unitName");
        final List<MaterialDO> materialDOS = Arrays.asList(materialDO);
        when(mockMaterialDOMapper.selectByExample(any(MaterialDOExample.class))).thenReturn(materialDOS);

        // Run the test
        assertThatThrownBy(() -> materialServiceImplUnderTest.batchCheckCode(materialDTOList))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testBatchCheckCode_MaterialDOMapperReturnsNoItems() {
        // Setup
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");
        final List<MaterialDTO> materialDTOList = Arrays.asList(materialDTO);
        when(mockMaterialDOMapper.selectByExample(any(MaterialDOExample.class))).thenReturn(Collections.emptyList());

        // Run the test
        materialServiceImplUnderTest.batchCheckCode(materialDTOList);

        // Verify the results
    }

    @Test
    public void testBatchCheckName_ThrowsBusinessException() {
        // Setup
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");
        final List<MaterialDTO> materialDTOList = Arrays.asList(materialDTO);

        // Configure MaterialDOMapper.selectByExample(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO.setName("name");
        materialDO.setUnit("unitGuid");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setCode("code");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO.setLowestStock(new BigDecimal("0.00"));
        materialDO.setEnabled(false);
        materialDO.setDeleted(false);
        materialDO.setUnitName("unitName");
        final List<MaterialDO> materialDOS = Arrays.asList(materialDO);
        when(mockMaterialDOMapper.selectByExample(any(MaterialDOExample.class))).thenReturn(materialDOS);

        // Run the test
        assertThatThrownBy(() -> materialServiceImplUnderTest.batchCheckName(materialDTOList))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testBatchCheckName_MaterialDOMapperReturnsNoItems() {
        // Setup
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");
        final List<MaterialDTO> materialDTOList = Arrays.asList(materialDTO);
        when(mockMaterialDOMapper.selectByExample(any(MaterialDOExample.class))).thenReturn(Collections.emptyList());

        // Run the test
        materialServiceImplUnderTest.batchCheckName(materialDTOList);

        // Verify the results
    }

    @Test
    public void testFindList() {
        // Setup
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");
        final List<MaterialDTO> expectedResult = Arrays.asList(materialDTO);

        // Configure MaterialDOMapper.findByWarehouseOrGuidList(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO.setName("name");
        materialDO.setUnit("unitGuid");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setCode("code");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO.setLowestStock(new BigDecimal("0.00"));
        materialDO.setEnabled(false);
        materialDO.setDeleted(false);
        materialDO.setUnitName("unitName");
        final List<MaterialDO> materialDOS = Arrays.asList(materialDO);
        when(mockMaterialDOMapper.findByWarehouseOrGuidList("warehouseGuid", "storeGuid",
                Arrays.asList("value"))).thenReturn(materialDOS);

        // Configure MaterialDOMapper.findAllByWarehouseOrGuidList(...).
        final MaterialDO materialDO1 = new MaterialDO();
        materialDO1.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO1.setName("name");
        materialDO1.setUnit("unitGuid");
        materialDO1.setAuxiliaryUnit("auxiliaryUnit");
        materialDO1.setCode("code");
        materialDO1.setConversionMain(new BigDecimal("0.00"));
        materialDO1.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO1.setLowestStock(new BigDecimal("0.00"));
        materialDO1.setEnabled(false);
        materialDO1.setDeleted(false);
        materialDO1.setUnitName("unitName");
        final List<MaterialDO> materialDOS1 = Arrays.asList(materialDO1);
        when(mockMaterialDOMapper.findAllByWarehouseOrGuidList("storeGuid", Arrays.asList("value")))
                .thenReturn(materialDOS1);

        // Configure ErpModuleMapper.mapToMaterialDTOList(...).
        final MaterialDTO materialDTO1 = new MaterialDTO();
        materialDTO1.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO1.setName("name");
        materialDTO1.setUnit("newMainUnit");
        materialDTO1.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO1.setCode("code");
        materialDTO1.setStoreGuid("storeGuid");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO1);
        final MaterialDO materialDO2 = new MaterialDO();
        materialDO2.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO2.setName("name");
        materialDO2.setUnit("unitGuid");
        materialDO2.setAuxiliaryUnit("auxiliaryUnit");
        materialDO2.setCode("code");
        materialDO2.setConversionMain(new BigDecimal("0.00"));
        materialDO2.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO2.setLowestStock(new BigDecimal("0.00"));
        materialDO2.setEnabled(false);
        materialDO2.setDeleted(false);
        materialDO2.setUnitName("unitName");
        final List<MaterialDO> list = Arrays.asList(materialDO2);
        when(mockModuleMapper.mapToMaterialDTOList(list)).thenReturn(materialDTOS);

        // Run the test
        final List<MaterialDTO> result = materialServiceImplUnderTest.findList("storeGuid", "warehouseGuid",
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindList_MaterialDOMapperFindByWarehouseOrGuidListReturnsNoItems() {
        // Setup
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");
        final List<MaterialDTO> expectedResult = Arrays.asList(materialDTO);
        when(mockMaterialDOMapper.findByWarehouseOrGuidList("warehouseGuid", "storeGuid",
                Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure MaterialDOMapper.findAllByWarehouseOrGuidList(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO.setName("name");
        materialDO.setUnit("unitGuid");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setCode("code");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO.setLowestStock(new BigDecimal("0.00"));
        materialDO.setEnabled(false);
        materialDO.setDeleted(false);
        materialDO.setUnitName("unitName");
        final List<MaterialDO> materialDOS = Arrays.asList(materialDO);
        when(mockMaterialDOMapper.findAllByWarehouseOrGuidList("storeGuid", Arrays.asList("value")))
                .thenReturn(materialDOS);

        // Configure ErpModuleMapper.mapToMaterialDTOList(...).
        final MaterialDTO materialDTO1 = new MaterialDTO();
        materialDTO1.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO1.setName("name");
        materialDTO1.setUnit("newMainUnit");
        materialDTO1.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO1.setCode("code");
        materialDTO1.setStoreGuid("storeGuid");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO1);
        final MaterialDO materialDO1 = new MaterialDO();
        materialDO1.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO1.setName("name");
        materialDO1.setUnit("unitGuid");
        materialDO1.setAuxiliaryUnit("auxiliaryUnit");
        materialDO1.setCode("code");
        materialDO1.setConversionMain(new BigDecimal("0.00"));
        materialDO1.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO1.setLowestStock(new BigDecimal("0.00"));
        materialDO1.setEnabled(false);
        materialDO1.setDeleted(false);
        materialDO1.setUnitName("unitName");
        final List<MaterialDO> list = Arrays.asList(materialDO1);
        when(mockModuleMapper.mapToMaterialDTOList(list)).thenReturn(materialDTOS);

        // Run the test
        final List<MaterialDTO> result = materialServiceImplUnderTest.findList("storeGuid", "warehouseGuid",
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindList_MaterialDOMapperFindAllByWarehouseOrGuidListReturnsNoItems() {
        // Setup
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");
        final List<MaterialDTO> expectedResult = Arrays.asList(materialDTO);

        // Configure MaterialDOMapper.findByWarehouseOrGuidList(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO.setName("name");
        materialDO.setUnit("unitGuid");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setCode("code");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO.setLowestStock(new BigDecimal("0.00"));
        materialDO.setEnabled(false);
        materialDO.setDeleted(false);
        materialDO.setUnitName("unitName");
        final List<MaterialDO> materialDOS = Arrays.asList(materialDO);
        when(mockMaterialDOMapper.findByWarehouseOrGuidList("warehouseGuid", "storeGuid",
                Arrays.asList("value"))).thenReturn(materialDOS);

        when(mockMaterialDOMapper.findAllByWarehouseOrGuidList("storeGuid", Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Configure ErpModuleMapper.mapToMaterialDTOList(...).
        final MaterialDTO materialDTO1 = new MaterialDTO();
        materialDTO1.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO1.setName("name");
        materialDTO1.setUnit("newMainUnit");
        materialDTO1.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO1.setCode("code");
        materialDTO1.setStoreGuid("storeGuid");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO1);
        final MaterialDO materialDO1 = new MaterialDO();
        materialDO1.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO1.setName("name");
        materialDO1.setUnit("unitGuid");
        materialDO1.setAuxiliaryUnit("auxiliaryUnit");
        materialDO1.setCode("code");
        materialDO1.setConversionMain(new BigDecimal("0.00"));
        materialDO1.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO1.setLowestStock(new BigDecimal("0.00"));
        materialDO1.setEnabled(false);
        materialDO1.setDeleted(false);
        materialDO1.setUnitName("unitName");
        final List<MaterialDO> list = Arrays.asList(materialDO1);
        when(mockModuleMapper.mapToMaterialDTOList(list)).thenReturn(materialDTOS);

        // Run the test
        final List<MaterialDTO> result = materialServiceImplUnderTest.findList("storeGuid", "warehouseGuid",
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindList_ErpModuleMapperReturnsNoItems() {
        // Setup
        // Configure MaterialDOMapper.findByWarehouseOrGuidList(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO.setName("name");
        materialDO.setUnit("unitGuid");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setCode("code");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO.setLowestStock(new BigDecimal("0.00"));
        materialDO.setEnabled(false);
        materialDO.setDeleted(false);
        materialDO.setUnitName("unitName");
        final List<MaterialDO> materialDOS = Arrays.asList(materialDO);
        when(mockMaterialDOMapper.findByWarehouseOrGuidList("warehouseGuid", "storeGuid",
                Arrays.asList("value"))).thenReturn(materialDOS);

        // Configure MaterialDOMapper.findAllByWarehouseOrGuidList(...).
        final MaterialDO materialDO1 = new MaterialDO();
        materialDO1.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO1.setName("name");
        materialDO1.setUnit("unitGuid");
        materialDO1.setAuxiliaryUnit("auxiliaryUnit");
        materialDO1.setCode("code");
        materialDO1.setConversionMain(new BigDecimal("0.00"));
        materialDO1.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO1.setLowestStock(new BigDecimal("0.00"));
        materialDO1.setEnabled(false);
        materialDO1.setDeleted(false);
        materialDO1.setUnitName("unitName");
        final List<MaterialDO> materialDOS1 = Arrays.asList(materialDO1);
        when(mockMaterialDOMapper.findAllByWarehouseOrGuidList("storeGuid", Arrays.asList("value")))
                .thenReturn(materialDOS1);

        // Configure ErpModuleMapper.mapToMaterialDTOList(...).
        final MaterialDO materialDO2 = new MaterialDO();
        materialDO2.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO2.setName("name");
        materialDO2.setUnit("unitGuid");
        materialDO2.setAuxiliaryUnit("auxiliaryUnit");
        materialDO2.setCode("code");
        materialDO2.setConversionMain(new BigDecimal("0.00"));
        materialDO2.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO2.setLowestStock(new BigDecimal("0.00"));
        materialDO2.setEnabled(false);
        materialDO2.setDeleted(false);
        materialDO2.setUnitName("unitName");
        final List<MaterialDO> list = Arrays.asList(materialDO2);
        when(mockModuleMapper.mapToMaterialDTOList(list)).thenReturn(Collections.emptyList());

        // Run the test
        final List<MaterialDTO> result = materialServiceImplUnderTest.findList("storeGuid", "warehouseGuid",
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testFindAllList() {
        // Setup
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");
        final List<MaterialDTO> expectedResult = Arrays.asList(materialDTO);

        // Configure MaterialDOMapper.findAllByWarehouseOrGuidList(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO.setName("name");
        materialDO.setUnit("unitGuid");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setCode("code");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO.setLowestStock(new BigDecimal("0.00"));
        materialDO.setEnabled(false);
        materialDO.setDeleted(false);
        materialDO.setUnitName("unitName");
        final List<MaterialDO> materialDOS = Arrays.asList(materialDO);
        when(mockMaterialDOMapper.findAllByWarehouseOrGuidList("storeGuid", Arrays.asList("value")))
                .thenReturn(materialDOS);

        // Configure ErpModuleMapper.mapToMaterialDTOList(...).
        final MaterialDTO materialDTO1 = new MaterialDTO();
        materialDTO1.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO1.setName("name");
        materialDTO1.setUnit("newMainUnit");
        materialDTO1.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO1.setCode("code");
        materialDTO1.setStoreGuid("storeGuid");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO1);
        final MaterialDO materialDO1 = new MaterialDO();
        materialDO1.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO1.setName("name");
        materialDO1.setUnit("unitGuid");
        materialDO1.setAuxiliaryUnit("auxiliaryUnit");
        materialDO1.setCode("code");
        materialDO1.setConversionMain(new BigDecimal("0.00"));
        materialDO1.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO1.setLowestStock(new BigDecimal("0.00"));
        materialDO1.setEnabled(false);
        materialDO1.setDeleted(false);
        materialDO1.setUnitName("unitName");
        final List<MaterialDO> list = Arrays.asList(materialDO1);
        when(mockModuleMapper.mapToMaterialDTOList(list)).thenReturn(materialDTOS);

        // Run the test
        final List<MaterialDTO> result = materialServiceImplUnderTest.findAllList("storeGuid", Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindAllList_MaterialDOMapperReturnsNoItems() {
        // Setup
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");
        final List<MaterialDTO> expectedResult = Arrays.asList(materialDTO);
        when(mockMaterialDOMapper.findAllByWarehouseOrGuidList("storeGuid", Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Configure ErpModuleMapper.mapToMaterialDTOList(...).
        final MaterialDTO materialDTO1 = new MaterialDTO();
        materialDTO1.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO1.setName("name");
        materialDTO1.setUnit("newMainUnit");
        materialDTO1.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO1.setCode("code");
        materialDTO1.setStoreGuid("storeGuid");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO1);
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO.setName("name");
        materialDO.setUnit("unitGuid");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setCode("code");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO.setLowestStock(new BigDecimal("0.00"));
        materialDO.setEnabled(false);
        materialDO.setDeleted(false);
        materialDO.setUnitName("unitName");
        final List<MaterialDO> list = Arrays.asList(materialDO);
        when(mockModuleMapper.mapToMaterialDTOList(list)).thenReturn(materialDTOS);

        // Run the test
        final List<MaterialDTO> result = materialServiceImplUnderTest.findAllList("storeGuid", Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindAllList_ErpModuleMapperReturnsNoItems() {
        // Setup
        // Configure MaterialDOMapper.findAllByWarehouseOrGuidList(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO.setName("name");
        materialDO.setUnit("unitGuid");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setCode("code");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO.setLowestStock(new BigDecimal("0.00"));
        materialDO.setEnabled(false);
        materialDO.setDeleted(false);
        materialDO.setUnitName("unitName");
        final List<MaterialDO> materialDOS = Arrays.asList(materialDO);
        when(mockMaterialDOMapper.findAllByWarehouseOrGuidList("storeGuid", Arrays.asList("value")))
                .thenReturn(materialDOS);

        // Configure ErpModuleMapper.mapToMaterialDTOList(...).
        final MaterialDO materialDO1 = new MaterialDO();
        materialDO1.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO1.setName("name");
        materialDO1.setUnit("unitGuid");
        materialDO1.setAuxiliaryUnit("auxiliaryUnit");
        materialDO1.setCode("code");
        materialDO1.setConversionMain(new BigDecimal("0.00"));
        materialDO1.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO1.setLowestStock(new BigDecimal("0.00"));
        materialDO1.setEnabled(false);
        materialDO1.setDeleted(false);
        materialDO1.setUnitName("unitName");
        final List<MaterialDO> list = Arrays.asList(materialDO1);
        when(mockModuleMapper.mapToMaterialDTOList(list)).thenReturn(Collections.emptyList());

        // Run the test
        final List<MaterialDTO> result = materialServiceImplUnderTest.findAllList("storeGuid", Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testFillCodeAndGuid() {
        // Setup
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");
        final List<MaterialDTO> materialDTOList = Arrays.asList(materialDTO);

        // Run the test
        materialServiceImplUnderTest.fillCodeAndGuid(materialDTOList);

        // Verify the results
    }

    @Test
    public void testAddBatch() {
        // Setup
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");
        final List<MaterialDTO> materialDTOList = Arrays.asList(materialDTO);

        // Configure ErpModuleMapper.mapToMaterialDOList(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO.setName("name");
        materialDO.setUnit("unitGuid");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setCode("code");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO.setLowestStock(new BigDecimal("0.00"));
        materialDO.setEnabled(false);
        materialDO.setDeleted(false);
        materialDO.setUnitName("unitName");
        final List<MaterialDO> materialDOS = Arrays.asList(materialDO);
        final MaterialDTO materialDTO1 = new MaterialDTO();
        materialDTO1.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO1.setName("name");
        materialDTO1.setUnit("newMainUnit");
        materialDTO1.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO1.setCode("code");
        materialDTO1.setStoreGuid("storeGuid");
        final List<MaterialDTO> list = Arrays.asList(materialDTO1);
        when(mockModuleMapper.mapToMaterialDOList(list)).thenReturn(materialDOS);

        // Run the test
        materialServiceImplUnderTest.addBatch(materialDTOList);

        // Verify the results
        // Confirm MaterialDOMapper.addBatch(...).
        final MaterialDO materialDO1 = new MaterialDO();
        materialDO1.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO1.setName("name");
        materialDO1.setUnit("unitGuid");
        materialDO1.setAuxiliaryUnit("auxiliaryUnit");
        materialDO1.setCode("code");
        materialDO1.setConversionMain(new BigDecimal("0.00"));
        materialDO1.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO1.setLowestStock(new BigDecimal("0.00"));
        materialDO1.setEnabled(false);
        materialDO1.setDeleted(false);
        materialDO1.setUnitName("unitName");
        final List<MaterialDO> materialDOS1 = Arrays.asList(materialDO1);
        verify(mockMaterialDOMapper).addBatch(materialDOS1);
    }

    @Test
    public void testAddBatch_ErpModuleMapperReturnsNoItems() {
        // Setup
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");
        final List<MaterialDTO> materialDTOList = Arrays.asList(materialDTO);

        // Configure ErpModuleMapper.mapToMaterialDOList(...).
        final MaterialDTO materialDTO1 = new MaterialDTO();
        materialDTO1.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO1.setName("name");
        materialDTO1.setUnit("newMainUnit");
        materialDTO1.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO1.setCode("code");
        materialDTO1.setStoreGuid("storeGuid");
        final List<MaterialDTO> list = Arrays.asList(materialDTO1);
        when(mockModuleMapper.mapToMaterialDOList(list)).thenReturn(Collections.emptyList());

        // Run the test
        materialServiceImplUnderTest.addBatch(materialDTOList);

        // Verify the results
        // Confirm MaterialDOMapper.addBatch(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO.setName("name");
        materialDO.setUnit("unitGuid");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setCode("code");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO.setLowestStock(new BigDecimal("0.00"));
        materialDO.setEnabled(false);
        materialDO.setDeleted(false);
        materialDO.setUnitName("unitName");
        final List<MaterialDO> materialDOS = Arrays.asList(materialDO);
        verify(mockMaterialDOMapper).addBatch(materialDOS);
    }

    @Test
    public void testUnitAdapterMain() {
        // Setup
        final List<UnitConvertBO> unitConvertBOList = Arrays.asList(
                new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")));

        // Configure MaterialDOMapper.findAllByWarehouseOrGuidList(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO.setName("name");
        materialDO.setUnit("unitGuid");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setCode("code");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO.setLowestStock(new BigDecimal("0.00"));
        materialDO.setEnabled(false);
        materialDO.setDeleted(false);
        materialDO.setUnitName("unitName");
        final List<MaterialDO> materialDOS = Arrays.asList(materialDO);
        when(mockMaterialDOMapper.findAllByWarehouseOrGuidList("storeGuid", Arrays.asList("value")))
                .thenReturn(materialDOS);

        // Run the test
        final List<UnitConvertBO> result = materialServiceImplUnderTest.unitAdapterMain(unitConvertBOList);

        // Verify the results
    }

    @Test
    public void testUnitAdapterMain_MaterialDOMapperReturnsNoItems() {
        // Setup
        final List<UnitConvertBO> unitConvertBOList = Arrays.asList(
                new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")));
        when(mockMaterialDOMapper.findAllByWarehouseOrGuidList("storeGuid", Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<UnitConvertBO> result = materialServiceImplUnderTest.unitAdapterMain(unitConvertBOList);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testChangeStatus() {
        // Setup
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO.setName("name");
        materialDTO.setUnit("newMainUnit");
        materialDTO.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO.setCode("code");
        materialDTO.setStoreGuid("storeGuid");

        // Configure ErpModuleMapper.mapToMaterialDO(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("c9405338-75d8-45a4-b9a4-f71225d745f3");
        materialDO.setName("name");
        materialDO.setUnit("unitGuid");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setCode("code");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        materialDO.setLowestStock(new BigDecimal("0.00"));
        materialDO.setEnabled(false);
        materialDO.setDeleted(false);
        materialDO.setUnitName("unitName");
        final MaterialDTO materialDTO1 = new MaterialDTO();
        materialDTO1.setGuid("45b112ba-b342-4f06-b4e4-39ead69256fa");
        materialDTO1.setName("name");
        materialDTO1.setUnit("newMainUnit");
        materialDTO1.setAuxiliaryUnit("newAuxiliaryUnit");
        materialDTO1.setCode("code");
        materialDTO1.setStoreGuid("storeGuid");
        when(mockModuleMapper.mapToMaterialDO(materialDTO1)).thenReturn(materialDO);

        // Run the test
        materialServiceImplUnderTest.changeStatus(materialDTO);

        // Verify the results
        verify(mockMaterialDOMapper).updateByExampleSelective(any(MaterialDO.class), any(MaterialDOExample.class));
    }

    @Test
    public void testMaterialConsumeSum() {
        // Setup
        final MaterialConsumeReqDTO materialConsumeReqDTO = new MaterialConsumeReqDTO();
        materialConsumeReqDTO.setWarehouseGuid("warehouseGuid");
        materialConsumeReqDTO.setStartDate("startDate");
        materialConsumeReqDTO.setEndDate("endDate");
        materialConsumeReqDTO.setClassifyGuid("classifyGuid");
        materialConsumeReqDTO.setType(0);

        // Configure MaterialDOMapper.materialConsumeSum(...).
        final MaterialConsumeReqDTO materialConsumeReqDTO1 = new MaterialConsumeReqDTO();
        materialConsumeReqDTO1.setWarehouseGuid("warehouseGuid");
        materialConsumeReqDTO1.setStartDate("startDate");
        materialConsumeReqDTO1.setEndDate("endDate");
        materialConsumeReqDTO1.setClassifyGuid("classifyGuid");
        materialConsumeReqDTO1.setType(0);
        when(mockMaterialDOMapper.materialConsumeSum(any(PageAdapter.class), eq(materialConsumeReqDTO1)))
                .thenReturn(null);

        // Configure MaterialDOMapper.queryDetailList(...).
        final MaterialDocDetailReadDO materialDocDetailReadDO = new MaterialDocDetailReadDO();
        materialDocDetailReadDO.setMaterialGuid("materialGuid");
        materialDocDetailReadDO.setDocType(0);
        materialDocDetailReadDO.setStock(new BigDecimal("0.00"));
        materialDocDetailReadDO.setCount(new BigDecimal("0.00"));
        materialDocDetailReadDO.setStartStock(new BigDecimal("0.00"));
        materialDocDetailReadDO.setEndStock(new BigDecimal("0.00"));
        materialDocDetailReadDO.setChangeStock(new BigDecimal("0.00"));
        final List<MaterialDocDetailReadDO> materialDocDetailReadDOS = Arrays.asList(materialDocDetailReadDO);
        when(mockMaterialDOMapper.queryDetailList(
                new MaterialConsumeQuery(LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        "warehouseGuid", Arrays.asList("value")))).thenReturn(materialDocDetailReadDOS);

        // Configure MaterialMapstruct.fromMaterialConsumeReadDO(...).
        final MaterialConsumeRespDTO materialConsumeRespDTO = new MaterialConsumeRespDTO();
        materialConsumeRespDTO.setMaterialGuid("materialGuid");
        materialConsumeRespDTO.setClassify("默认分类");
        materialConsumeRespDTO.setCount(new BigDecimal("0.00"));
        materialConsumeRespDTO.setActualCount("-");
        materialConsumeRespDTO.setDiff("-");
        materialConsumeRespDTO.setDiffRate("-");
        final MaterialConsumeReadDO materialConsumeReadDO = new MaterialConsumeReadDO();
        materialConsumeReadDO.setGuid("eae980b7-f0c3-4658-b60e-b5d0d502c08e");
        materialConsumeReadDO.setCode("code");
        materialConsumeReadDO.setName("name");
        materialConsumeReadDO.setClassify("classify");
        materialConsumeReadDO.setCount(new BigDecimal("0.00"));
        when(mockMaterialMapstruct.fromMaterialConsumeReadDO(materialConsumeReadDO)).thenReturn(materialConsumeRespDTO);

        // Run the test
        final Page<MaterialConsumeRespDTO> result = materialServiceImplUnderTest.materialConsumeSum(
                materialConsumeReqDTO);

        // Verify the results
    }

    @Test
    public void testMaterialConsumeSum_MaterialDOMapperQueryDetailListReturnsNoItems() {
        // Setup
        final MaterialConsumeReqDTO materialConsumeReqDTO = new MaterialConsumeReqDTO();
        materialConsumeReqDTO.setWarehouseGuid("warehouseGuid");
        materialConsumeReqDTO.setStartDate("startDate");
        materialConsumeReqDTO.setEndDate("endDate");
        materialConsumeReqDTO.setClassifyGuid("classifyGuid");
        materialConsumeReqDTO.setType(0);

        // Configure MaterialDOMapper.materialConsumeSum(...).
        final MaterialConsumeReqDTO materialConsumeReqDTO1 = new MaterialConsumeReqDTO();
        materialConsumeReqDTO1.setWarehouseGuid("warehouseGuid");
        materialConsumeReqDTO1.setStartDate("startDate");
        materialConsumeReqDTO1.setEndDate("endDate");
        materialConsumeReqDTO1.setClassifyGuid("classifyGuid");
        materialConsumeReqDTO1.setType(0);
        when(mockMaterialDOMapper.materialConsumeSum(any(PageAdapter.class), eq(materialConsumeReqDTO1)))
                .thenReturn(null);

        when(mockMaterialDOMapper.queryDetailList(
                new MaterialConsumeQuery(LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        "warehouseGuid", Arrays.asList("value")))).thenReturn(Collections.emptyList());

        // Configure MaterialMapstruct.fromMaterialConsumeReadDO(...).
        final MaterialConsumeRespDTO materialConsumeRespDTO = new MaterialConsumeRespDTO();
        materialConsumeRespDTO.setMaterialGuid("materialGuid");
        materialConsumeRespDTO.setClassify("默认分类");
        materialConsumeRespDTO.setCount(new BigDecimal("0.00"));
        materialConsumeRespDTO.setActualCount("-");
        materialConsumeRespDTO.setDiff("-");
        materialConsumeRespDTO.setDiffRate("-");
        final MaterialConsumeReadDO materialConsumeReadDO = new MaterialConsumeReadDO();
        materialConsumeReadDO.setGuid("eae980b7-f0c3-4658-b60e-b5d0d502c08e");
        materialConsumeReadDO.setCode("code");
        materialConsumeReadDO.setName("name");
        materialConsumeReadDO.setClassify("classify");
        materialConsumeReadDO.setCount(new BigDecimal("0.00"));
        when(mockMaterialMapstruct.fromMaterialConsumeReadDO(materialConsumeReadDO)).thenReturn(materialConsumeRespDTO);

        // Run the test
        final Page<MaterialConsumeRespDTO> result = materialServiceImplUnderTest.materialConsumeSum(
                materialConsumeReqDTO);

        // Verify the results
    }
}
