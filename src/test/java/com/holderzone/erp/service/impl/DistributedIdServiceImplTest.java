package com.holderzone.erp.service.impl;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Arrays;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class DistributedIdServiceImplTest {

    @Mock
    private RedisTemplate mockRedisTemplate;

    private DistributedIdServiceImpl distributedIdServiceImplUnderTest;

    @Before
    public void setUp() {
        distributedIdServiceImplUnderTest = new DistributedIdServiceImpl(mockRedisTemplate);
    }

    @Test
    public void testRawId() {
        assertThat(distributedIdServiceImplUnderTest.rawId("tag")).isEqualTo(0L);
    }

    @Test
    public void testNextId() {
        assertThat(distributedIdServiceImplUnderTest.nextId("tag")).isEqualTo("result");
    }

    @Test
    public void testNextRepertoryGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextRepertoryGuid()).isEqualTo("result");
    }

    @Test
    public void testNextInventoryGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextInventoryGuid()).isEqualTo("result");
    }

    @Test
    public void testNextBatchGoodsItemGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextBatchGoodsItemGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedIdServiceImplUnderTest.nextBatchGoodsItemGuid(0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextBatchGoodsSerialGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextBatchGoodsSerialGuid(0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedIdServiceImplUnderTest.nextBatchGoodsSerialGuid(0L)).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testNextBatchId() {
        assertThat(distributedIdServiceImplUnderTest.nextBatchId("tag", 0L)).isEqualTo(Arrays.asList("value"));
        assertThat(distributedIdServiceImplUnderTest.nextBatchId("tag", 0L)).isEqualTo(Collections.emptyList());
    }
}
