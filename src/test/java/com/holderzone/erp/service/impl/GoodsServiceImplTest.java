package com.holderzone.erp.service.impl;

import com.holderzone.erp.dao.GoodsMapper;
import com.holderzone.erp.entity.domain.GoodsDO;
import com.holderzone.erp.mapperstruct.GoodsMapstruct;
import com.holderzone.erp.service.GoodsSerialService;
import com.holderzone.erp.utils.PageAdapter;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.erpretail.GoodsExportDTO;
import com.holderzone.saas.store.dto.erp.erpretail.InOutGoodsDTO;
import com.holderzone.saas.store.dto.erp.erpretail.RepertorySumDTO;
import com.holderzone.saas.store.dto.erp.erpretail.SaleSkuSumDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.QueryGoodsSumInfoReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.SubstractGoodsReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.GoodsClassifyAndItemRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.GoodsSumInfoRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GoodsServiceImplTest {

    @Mock
    private GoodsMapper mockGoodsMapper;
    @Mock
    private GoodsMapstruct mockGoodsMapstruct;
    @Mock
    private GoodsSerialService mockGoodsSerialService;

    @InjectMocks
    private GoodsServiceImpl goodsServiceImplUnderTest;

    @Test
    public void testInsertBatchGoods() {
        // Setup
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setGuid("30169725-8c37-425f-ad52-cf270a23697a");
        goodsDO.setStoreGuid("storeGuid");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO.setGoodsClassifyGuid("goodsClassifyGuid");
        goodsDO.setGoodsClassifyName("classifyName");
        goodsDO.setIsOpenStock(0);
        goodsDO.setSafeNum(new BigDecimal("0.00"));
        final List<GoodsDO> goodsDOS = Arrays.asList(goodsDO);

        // Run the test
        final boolean result = goodsServiceImplUnderTest.insertBatchGoods(goodsDOS, 0, "storeGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testModifyGoodsRepertoryNum() {
        // Setup
        final SubstractGoodsReqDTO substractGoodsReqDTO = new SubstractGoodsReqDTO();
        substractGoodsReqDTO.setGoodsGuid("goodsGuid");
        substractGoodsReqDTO.setCount(new BigDecimal("0.00"));
        substractGoodsReqDTO.setUnitPrice(new BigDecimal("0.00"));
        final List<SubstractGoodsReqDTO> goodsList = Arrays.asList(substractGoodsReqDTO);

        // Run the test
        final boolean result = goodsServiceImplUnderTest.modifyGoodsRepertoryNum(goodsList, 0);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testQueryGoods1() {
        // Setup
        final QueryGoodsSumInfoReqDTO queryGoodsSumInfoReqDTO = new QueryGoodsSumInfoReqDTO();
        queryGoodsSumInfoReqDTO.setGoodsClassifyGuid("goodsClassifyGuid");
        queryGoodsSumInfoReqDTO.setNameOrCode("nameOrCode");
        queryGoodsSumInfoReqDTO.setStoreGuid("storeGuid");

        // Configure GoodsMapper.queryGoodsSumInfo(...).
        final QueryGoodsSumInfoReqDTO queryGoodsSumInfoReqDTO1 = new QueryGoodsSumInfoReqDTO();
        queryGoodsSumInfoReqDTO1.setGoodsClassifyGuid("goodsClassifyGuid");
        queryGoodsSumInfoReqDTO1.setNameOrCode("nameOrCode");
        queryGoodsSumInfoReqDTO1.setStoreGuid("storeGuid");
        when(mockGoodsMapper.queryGoodsSumInfo(any(PageAdapter.class), eq(queryGoodsSumInfoReqDTO1))).thenReturn(null);

        // Configure GoodsMapstruct.fromGoodsListToGoodsRepertorySumList(...).
        final GoodsSumInfoRespDTO goodsSumInfoRespDTO = new GoodsSumInfoRespDTO();
        goodsSumInfoRespDTO.setGoodsGuid("goodsGuid");
        goodsSumInfoRespDTO.setGoodsCode("goodsCode");
        goodsSumInfoRespDTO.setGoodsName("goodsName");
        goodsSumInfoRespDTO.setGoodsClassifyName("goodsClassifyName");
        goodsSumInfoRespDTO.setCount(new BigDecimal("0.00"));
        final List<GoodsSumInfoRespDTO> goodsSumInfoRespDTOS = Arrays.asList(goodsSumInfoRespDTO);
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setGuid("30169725-8c37-425f-ad52-cf270a23697a");
        goodsDO.setStoreGuid("storeGuid");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO.setGoodsClassifyGuid("goodsClassifyGuid");
        goodsDO.setGoodsClassifyName("classifyName");
        goodsDO.setIsOpenStock(0);
        goodsDO.setSafeNum(new BigDecimal("0.00"));
        final List<GoodsDO> goodsDOList = Arrays.asList(goodsDO);
        when(mockGoodsMapstruct.fromGoodsListToGoodsRepertorySumList(goodsDOList)).thenReturn(goodsSumInfoRespDTOS);

        // Run the test
        final Page<GoodsSumInfoRespDTO> result = goodsServiceImplUnderTest.queryGoods(queryGoodsSumInfoReqDTO);

        // Verify the results
    }

    @Test
    public void testQueryGoods1_GoodsMapstructReturnsNoItems() {
        // Setup
        final QueryGoodsSumInfoReqDTO queryGoodsSumInfoReqDTO = new QueryGoodsSumInfoReqDTO();
        queryGoodsSumInfoReqDTO.setGoodsClassifyGuid("goodsClassifyGuid");
        queryGoodsSumInfoReqDTO.setNameOrCode("nameOrCode");
        queryGoodsSumInfoReqDTO.setStoreGuid("storeGuid");

        // Configure GoodsMapper.queryGoodsSumInfo(...).
        final QueryGoodsSumInfoReqDTO queryGoodsSumInfoReqDTO1 = new QueryGoodsSumInfoReqDTO();
        queryGoodsSumInfoReqDTO1.setGoodsClassifyGuid("goodsClassifyGuid");
        queryGoodsSumInfoReqDTO1.setNameOrCode("nameOrCode");
        queryGoodsSumInfoReqDTO1.setStoreGuid("storeGuid");
        when(mockGoodsMapper.queryGoodsSumInfo(any(PageAdapter.class), eq(queryGoodsSumInfoReqDTO1))).thenReturn(null);

        // Configure GoodsMapstruct.fromGoodsListToGoodsRepertorySumList(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setGuid("30169725-8c37-425f-ad52-cf270a23697a");
        goodsDO.setStoreGuid("storeGuid");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO.setGoodsClassifyGuid("goodsClassifyGuid");
        goodsDO.setGoodsClassifyName("classifyName");
        goodsDO.setIsOpenStock(0);
        goodsDO.setSafeNum(new BigDecimal("0.00"));
        final List<GoodsDO> goodsDOList = Arrays.asList(goodsDO);
        when(mockGoodsMapstruct.fromGoodsListToGoodsRepertorySumList(goodsDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final Page<GoodsSumInfoRespDTO> result = goodsServiceImplUnderTest.queryGoods(queryGoodsSumInfoReqDTO);

        // Verify the results
    }

    @Test
    public void testQueryGoods2() {
        // Setup
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setGuid("30169725-8c37-425f-ad52-cf270a23697a");
        goodsDO.setStoreGuid("storeGuid");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO.setGoodsClassifyGuid("goodsClassifyGuid");
        goodsDO.setGoodsClassifyName("classifyName");
        goodsDO.setIsOpenStock(0);
        goodsDO.setSafeNum(new BigDecimal("0.00"));
        final List<GoodsDO> expectedResult = Arrays.asList(goodsDO);

        // Run the test
        final List<GoodsDO> result = goodsServiceImplUnderTest.queryGoods(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryGoodsList() {
        // Setup
        final SingleDataDTO singleDataDTO = new SingleDataDTO("data", Arrays.asList("value"));
        final GoodsClassifyAndItemRespDTO goodsClassifyAndItemRespDTO = new GoodsClassifyAndItemRespDTO();
        goodsClassifyAndItemRespDTO.setGoodsClassifyGuid("goodsClassifyGuid");
        goodsClassifyAndItemRespDTO.setGoodsClassifyName("classifyName");
        final InOutGoodsDTO inOutGoodsDTO = new InOutGoodsDTO();
        inOutGoodsDTO.setGoodsGuid("goodsGuid");
        inOutGoodsDTO.setGoodsCode("goodsCode");
        goodsClassifyAndItemRespDTO.setGoodsList(Arrays.asList(inOutGoodsDTO));
        final List<GoodsClassifyAndItemRespDTO> expectedResult = Arrays.asList(goodsClassifyAndItemRespDTO);

        // Configure GoodsMapstruct.fromGoodsList(...).
        final InOutGoodsDTO inOutGoodsDTO1 = new InOutGoodsDTO();
        inOutGoodsDTO1.setGoodsGuid("goodsGuid");
        inOutGoodsDTO1.setGoodsCode("goodsCode");
        inOutGoodsDTO1.setGoodsName("goodsName");
        inOutGoodsDTO1.setBarCode("barCode");
        inOutGoodsDTO1.setPinyin("pinyin");
        final List<InOutGoodsDTO> inOutGoodsDTOS = Arrays.asList(inOutGoodsDTO1);
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setGuid("30169725-8c37-425f-ad52-cf270a23697a");
        goodsDO.setStoreGuid("storeGuid");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO.setGoodsClassifyGuid("goodsClassifyGuid");
        goodsDO.setGoodsClassifyName("classifyName");
        goodsDO.setIsOpenStock(0);
        goodsDO.setSafeNum(new BigDecimal("0.00"));
        final List<GoodsDO> goodsDOList = Arrays.asList(goodsDO);
        when(mockGoodsMapstruct.fromGoodsList(goodsDOList)).thenReturn(inOutGoodsDTOS);

        // Run the test
        final List<GoodsClassifyAndItemRespDTO> result = goodsServiceImplUnderTest.queryGoodsList(singleDataDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryGoodsList_GoodsMapstructReturnsNoItems() {
        // Setup
        final SingleDataDTO singleDataDTO = new SingleDataDTO("data", Arrays.asList("value"));
        final GoodsClassifyAndItemRespDTO goodsClassifyAndItemRespDTO = new GoodsClassifyAndItemRespDTO();
        goodsClassifyAndItemRespDTO.setGoodsClassifyGuid("goodsClassifyGuid");
        goodsClassifyAndItemRespDTO.setGoodsClassifyName("classifyName");
        final InOutGoodsDTO inOutGoodsDTO = new InOutGoodsDTO();
        inOutGoodsDTO.setGoodsGuid("goodsGuid");
        inOutGoodsDTO.setGoodsCode("goodsCode");
        goodsClassifyAndItemRespDTO.setGoodsList(Arrays.asList(inOutGoodsDTO));
        final List<GoodsClassifyAndItemRespDTO> expectedResult = Arrays.asList(goodsClassifyAndItemRespDTO);

        // Configure GoodsMapstruct.fromGoodsList(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setGuid("30169725-8c37-425f-ad52-cf270a23697a");
        goodsDO.setStoreGuid("storeGuid");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO.setGoodsClassifyGuid("goodsClassifyGuid");
        goodsDO.setGoodsClassifyName("classifyName");
        goodsDO.setIsOpenStock(0);
        goodsDO.setSafeNum(new BigDecimal("0.00"));
        final List<GoodsDO> goodsDOList = Arrays.asList(goodsDO);
        when(mockGoodsMapstruct.fromGoodsList(goodsDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodsClassifyAndItemRespDTO> result = goodsServiceImplUnderTest.queryGoodsList(singleDataDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testModifyClassifyName() {
        assertThat(goodsServiceImplUnderTest.modifyClassifyName("classifyGuid", "classifyName")).isFalse();
    }

    @Test
    public void testModifyGoodsInfo() {
        // Setup
        final InOutGoodsDTO inOutGoodsDTO = new InOutGoodsDTO();
        inOutGoodsDTO.setGoodsGuid("goodsGuid");
        inOutGoodsDTO.setGoodsCode("goodsCode");
        inOutGoodsDTO.setGoodsName("goodsName");
        inOutGoodsDTO.setBarCode("barCode");
        inOutGoodsDTO.setPinyin("pinyin");

        // Configure GoodsMapstruct.fromInOutGoodsDTO(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setGuid("30169725-8c37-425f-ad52-cf270a23697a");
        goodsDO.setStoreGuid("storeGuid");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO.setGoodsClassifyGuid("goodsClassifyGuid");
        goodsDO.setGoodsClassifyName("classifyName");
        goodsDO.setIsOpenStock(0);
        goodsDO.setSafeNum(new BigDecimal("0.00"));
        final InOutGoodsDTO inOutGoodsDTO1 = new InOutGoodsDTO();
        inOutGoodsDTO1.setGoodsGuid("goodsGuid");
        inOutGoodsDTO1.setGoodsCode("goodsCode");
        inOutGoodsDTO1.setGoodsName("goodsName");
        inOutGoodsDTO1.setBarCode("barCode");
        inOutGoodsDTO1.setPinyin("pinyin");
        when(mockGoodsMapstruct.fromInOutGoodsDTO(inOutGoodsDTO1)).thenReturn(goodsDO);

        // Run the test
        final boolean result = goodsServiceImplUnderTest.modifyGoodsInfo(inOutGoodsDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testCancelRelateRepertory() {
        assertThat(goodsServiceImplUnderTest.cancelRelateRepertory("goodsGuid")).isFalse();
    }

    @Test
    public void testUpdateGoodsRepertoryNum() {
        assertThat(goodsServiceImplUnderTest.updateGoodsRepertoryNum("goodsGuid", new BigDecimal("0.00"))).isFalse();
    }

    @Test
    public void testQueryGoodsInfo() {
        // Setup
        final InOutGoodsDTO expectedResult = new InOutGoodsDTO();
        expectedResult.setGoodsGuid("goodsGuid");
        expectedResult.setGoodsCode("goodsCode");
        expectedResult.setGoodsName("goodsName");
        expectedResult.setBarCode("barCode");
        expectedResult.setPinyin("pinyin");

        // Configure GoodsMapstruct.fromGoodsDO(...).
        final InOutGoodsDTO inOutGoodsDTO = new InOutGoodsDTO();
        inOutGoodsDTO.setGoodsGuid("goodsGuid");
        inOutGoodsDTO.setGoodsCode("goodsCode");
        inOutGoodsDTO.setGoodsName("goodsName");
        inOutGoodsDTO.setBarCode("barCode");
        inOutGoodsDTO.setPinyin("pinyin");
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setGuid("30169725-8c37-425f-ad52-cf270a23697a");
        goodsDO.setStoreGuid("storeGuid");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO.setGoodsClassifyGuid("goodsClassifyGuid");
        goodsDO.setGoodsClassifyName("classifyName");
        goodsDO.setIsOpenStock(0);
        goodsDO.setSafeNum(new BigDecimal("0.00"));
        when(mockGoodsMapstruct.fromGoodsDO(goodsDO)).thenReturn(inOutGoodsDTO);

        // Run the test
        final InOutGoodsDTO result = goodsServiceImplUnderTest.queryGoodsInfo("goodsGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryExportGoodsList() {
        // Setup
        final List<GoodsExportDTO> expectedResult = Arrays.asList(
                new GoodsExportDTO("goodsGuid", new BigDecimal("0.00"), new BigDecimal("0.00")));

        // Configure GoodsMapstruct.fromGoodsDOList(...).
        final List<GoodsExportDTO> goodsExportDTOS = Arrays.asList(
                new GoodsExportDTO("goodsGuid", new BigDecimal("0.00"), new BigDecimal("0.00")));
        final GoodsDO goodsDO1 = new GoodsDO();
        goodsDO1.setGuid("30169725-8c37-425f-ad52-cf270a23697a");
        goodsDO1.setStoreGuid("storeGuid");
        goodsDO1.setGoodsName("goodsName");
        goodsDO1.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO1.setGoodsClassifyGuid("goodsClassifyGuid");
        goodsDO1.setGoodsClassifyName("classifyName");
        goodsDO1.setIsOpenStock(0);
        goodsDO1.setSafeNum(new BigDecimal("0.00"));
        final List<GoodsDO> goodsDO = Arrays.asList(goodsDO1);
        when(mockGoodsMapstruct.fromGoodsDOList(goodsDO)).thenReturn(goodsExportDTOS);

        // Run the test
        final List<GoodsExportDTO> result = goodsServiceImplUnderTest.queryExportGoodsList(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryExportGoodsList_GoodsMapstructReturnsNoItems() {
        // Setup
        // Configure GoodsMapstruct.fromGoodsDOList(...).
        final GoodsDO goodsDO1 = new GoodsDO();
        goodsDO1.setGuid("30169725-8c37-425f-ad52-cf270a23697a");
        goodsDO1.setStoreGuid("storeGuid");
        goodsDO1.setGoodsName("goodsName");
        goodsDO1.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO1.setGoodsClassifyGuid("goodsClassifyGuid");
        goodsDO1.setGoodsClassifyName("classifyName");
        goodsDO1.setIsOpenStock(0);
        goodsDO1.setSafeNum(new BigDecimal("0.00"));
        final List<GoodsDO> goodsDO = Arrays.asList(goodsDO1);
        when(mockGoodsMapstruct.fromGoodsDOList(goodsDO)).thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodsExportDTO> result = goodsServiceImplUnderTest.queryExportGoodsList(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryRepertorySum() {
        // Setup
        final SingleDataDTO singleDataDTO = new SingleDataDTO("data", Arrays.asList("value"));
        final RepertorySumDTO expectedResult = new RepertorySumDTO();
        expectedResult.setRepertorySum(new BigDecimal("0.00"));
        expectedResult.setSkuSum(0);
        expectedResult.setSevenDaySkuSum("0");
        expectedResult.setThirtyDaySkuSum("0");

        // Configure GoodsSerialService.queryGoodsSaleSkuSum(...).
        final SaleSkuSumDTO saleSkuSumDTO = new SaleSkuSumDTO();
        saleSkuSumDTO.setSevenDaySkuSum(1);
        saleSkuSumDTO.setThirtyDaySkuSum(1);
        when(mockGoodsSerialService.queryGoodsSaleSkuSum()).thenReturn(saleSkuSumDTO);

        // Run the test
        final RepertorySumDTO result = goodsServiceImplUnderTest.queryRepertorySum(singleDataDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
