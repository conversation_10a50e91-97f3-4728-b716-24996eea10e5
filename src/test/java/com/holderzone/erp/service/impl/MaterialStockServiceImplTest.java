package com.holderzone.erp.service.impl;

import com.holderzone.erp.dao.MaterialDOMapper;
import com.holderzone.erp.dao.MaterialStockDOMapper;
import com.holderzone.erp.entity.bo.UpdateStockBO;
import com.holderzone.erp.entity.domain.*;
import com.holderzone.erp.mapperstruct.ErpModuleMapper;
import com.holderzone.erp.service.IMaterialCategoryService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.*;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MaterialStockServiceImplTest {

    @Mock
    private MaterialStockDOMapper mockStockMapper;
    @Mock
    private MaterialDOMapper mockMaterialDOMapper;
    @Mock
    private ErpModuleMapper mockModuleMapper;
    @Mock
    private IMaterialCategoryService mockCategoryService;

    private MaterialStockServiceImpl materialStockServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        materialStockServiceImplUnderTest = new MaterialStockServiceImpl();
        materialStockServiceImplUnderTest.stockMapper = mockStockMapper;
        materialStockServiceImplUnderTest.materialDOMapper = mockMaterialDOMapper;
        materialStockServiceImplUnderTest.moduleMapper = mockModuleMapper;
        materialStockServiceImplUnderTest.categoryService = mockCategoryService;
    }

    @Test
    public void testFindStockPage() {
        // Setup
        final StockQueryDTO stockQueryDTO = new StockQueryDTO();
        stockQueryDTO.setCategory("category");
        stockQueryDTO.setSearchConditions("searchConditions");
        stockQueryDTO.setWarehouseGuid("warehouseGuid");

        final Page page = new Page<>(0L, 0L, Arrays.asList());
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setInUnitPrice(new BigDecimal("0.00"));
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("4c97db9d-74f2-48c1-a000-ad8a5dd892c2");
        materialDTO.setEnterpriseGuid("enterpriseGuid");
        materialDTO.setCategory("category");
        final List<MaterialDTO> expectedResult = Arrays.asList(materialDTO);

        // Configure MaterialStockDOMapper.findStockPage(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setInUnitPrice(new BigDecimal("0.00"));
        materialDO.setGuid("86d916c5-8605-4cef-bf93-63bb5a6cf6c0");
        materialDO.setUnit("unit");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        final List<MaterialDO> materialDOList = Arrays.asList(materialDO);
        final StockQueryDTO stockQueryDTO1 = new StockQueryDTO();
        stockQueryDTO1.setCategory("category");
        stockQueryDTO1.setSearchConditions("searchConditions");
        stockQueryDTO1.setWarehouseGuid("warehouseGuid");
        when(mockStockMapper.findStockPage(eq(stockQueryDTO1), any(Page.class))).thenReturn(materialDOList);

        // Configure ErpModuleMapper.mapToMaterialDTOList(...).
        final MaterialDTO materialDTO1 = new MaterialDTO();
        materialDTO1.setInUnitPrice(new BigDecimal("0.00"));
        materialDTO1.setCategoryName("categoryName");
        materialDTO1.setGuid("4c97db9d-74f2-48c1-a000-ad8a5dd892c2");
        materialDTO1.setEnterpriseGuid("enterpriseGuid");
        materialDTO1.setCategory("category");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO1);
        final MaterialDO materialDO1 = new MaterialDO();
        materialDO1.setInUnitPrice(new BigDecimal("0.00"));
        materialDO1.setGuid("86d916c5-8605-4cef-bf93-63bb5a6cf6c0");
        materialDO1.setUnit("unit");
        materialDO1.setAuxiliaryUnit("auxiliaryUnit");
        materialDO1.setConversionMain(new BigDecimal("0.00"));
        materialDO1.setConversionAuxiliary(new BigDecimal("0.00"));
        final List<MaterialDO> list = Arrays.asList(materialDO1);
        when(mockModuleMapper.mapToMaterialDTOList(list)).thenReturn(materialDTOS);

        // Run the test
        final List<MaterialDTO> result = materialStockServiceImplUnderTest.findStockPage(stockQueryDTO, page);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindStockPage_MaterialStockDOMapperReturnsNoItems() {
        // Setup
        final StockQueryDTO stockQueryDTO = new StockQueryDTO();
        stockQueryDTO.setCategory("category");
        stockQueryDTO.setSearchConditions("searchConditions");
        stockQueryDTO.setWarehouseGuid("warehouseGuid");

        final Page page = new Page<>(0L, 0L, Arrays.asList());
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setInUnitPrice(new BigDecimal("0.00"));
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("4c97db9d-74f2-48c1-a000-ad8a5dd892c2");
        materialDTO.setEnterpriseGuid("enterpriseGuid");
        materialDTO.setCategory("category");
        final List<MaterialDTO> expectedResult = Arrays.asList(materialDTO);

        // Configure MaterialStockDOMapper.findStockPage(...).
        final StockQueryDTO stockQueryDTO1 = new StockQueryDTO();
        stockQueryDTO1.setCategory("category");
        stockQueryDTO1.setSearchConditions("searchConditions");
        stockQueryDTO1.setWarehouseGuid("warehouseGuid");
        when(mockStockMapper.findStockPage(eq(stockQueryDTO1), any(Page.class))).thenReturn(Collections.emptyList());

        // Configure ErpModuleMapper.mapToMaterialDTOList(...).
        final MaterialDTO materialDTO1 = new MaterialDTO();
        materialDTO1.setInUnitPrice(new BigDecimal("0.00"));
        materialDTO1.setCategoryName("categoryName");
        materialDTO1.setGuid("4c97db9d-74f2-48c1-a000-ad8a5dd892c2");
        materialDTO1.setEnterpriseGuid("enterpriseGuid");
        materialDTO1.setCategory("category");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO1);
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setInUnitPrice(new BigDecimal("0.00"));
        materialDO.setGuid("86d916c5-8605-4cef-bf93-63bb5a6cf6c0");
        materialDO.setUnit("unit");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        final List<MaterialDO> list = Arrays.asList(materialDO);
        when(mockModuleMapper.mapToMaterialDTOList(list)).thenReturn(materialDTOS);

        // Run the test
        final List<MaterialDTO> result = materialStockServiceImplUnderTest.findStockPage(stockQueryDTO, page);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindStockPage_ErpModuleMapperReturnsNoItems() {
        // Setup
        final StockQueryDTO stockQueryDTO = new StockQueryDTO();
        stockQueryDTO.setCategory("category");
        stockQueryDTO.setSearchConditions("searchConditions");
        stockQueryDTO.setWarehouseGuid("warehouseGuid");

        final Page page = new Page<>(0L, 0L, Arrays.asList());

        // Configure MaterialStockDOMapper.findStockPage(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setInUnitPrice(new BigDecimal("0.00"));
        materialDO.setGuid("86d916c5-8605-4cef-bf93-63bb5a6cf6c0");
        materialDO.setUnit("unit");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        final List<MaterialDO> materialDOList = Arrays.asList(materialDO);
        final StockQueryDTO stockQueryDTO1 = new StockQueryDTO();
        stockQueryDTO1.setCategory("category");
        stockQueryDTO1.setSearchConditions("searchConditions");
        stockQueryDTO1.setWarehouseGuid("warehouseGuid");
        when(mockStockMapper.findStockPage(eq(stockQueryDTO1), any(Page.class))).thenReturn(materialDOList);

        // Configure ErpModuleMapper.mapToMaterialDTOList(...).
        final MaterialDO materialDO1 = new MaterialDO();
        materialDO1.setInUnitPrice(new BigDecimal("0.00"));
        materialDO1.setGuid("86d916c5-8605-4cef-bf93-63bb5a6cf6c0");
        materialDO1.setUnit("unit");
        materialDO1.setAuxiliaryUnit("auxiliaryUnit");
        materialDO1.setConversionMain(new BigDecimal("0.00"));
        materialDO1.setConversionAuxiliary(new BigDecimal("0.00"));
        final List<MaterialDO> list = Arrays.asList(materialDO1);
        when(mockModuleMapper.mapToMaterialDTOList(list)).thenReturn(Collections.emptyList());

        // Run the test
        final List<MaterialDTO> result = materialStockServiceImplUnderTest.findStockPage(stockQueryDTO, page);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testListByCondition() {
        // Setup
        final StockQueryDTO stockQueryDTO = new StockQueryDTO();
        stockQueryDTO.setCategory("category");
        stockQueryDTO.setSearchConditions("searchConditions");
        stockQueryDTO.setWarehouseGuid("warehouseGuid");

        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setInUnitPrice(new BigDecimal("0.00"));
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("4c97db9d-74f2-48c1-a000-ad8a5dd892c2");
        materialDTO.setEnterpriseGuid("enterpriseGuid");
        materialDTO.setCategory("category");
        final List<MaterialDTO> expectedResult = Arrays.asList(materialDTO);

        // Configure MaterialStockDOMapper.findStockPage(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setInUnitPrice(new BigDecimal("0.00"));
        materialDO.setGuid("86d916c5-8605-4cef-bf93-63bb5a6cf6c0");
        materialDO.setUnit("unit");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        final List<MaterialDO> materialDOList = Arrays.asList(materialDO);
        final StockQueryDTO stockQueryDTO1 = new StockQueryDTO();
        stockQueryDTO1.setCategory("category");
        stockQueryDTO1.setSearchConditions("searchConditions");
        stockQueryDTO1.setWarehouseGuid("warehouseGuid");
        when(mockStockMapper.findStockPage(eq(stockQueryDTO1), any(Page.class))).thenReturn(materialDOList);

        // Configure ErpModuleMapper.mapToMaterialDTOList(...).
        final MaterialDTO materialDTO1 = new MaterialDTO();
        materialDTO1.setInUnitPrice(new BigDecimal("0.00"));
        materialDTO1.setCategoryName("categoryName");
        materialDTO1.setGuid("4c97db9d-74f2-48c1-a000-ad8a5dd892c2");
        materialDTO1.setEnterpriseGuid("enterpriseGuid");
        materialDTO1.setCategory("category");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO1);
        final MaterialDO materialDO1 = new MaterialDO();
        materialDO1.setInUnitPrice(new BigDecimal("0.00"));
        materialDO1.setGuid("86d916c5-8605-4cef-bf93-63bb5a6cf6c0");
        materialDO1.setUnit("unit");
        materialDO1.setAuxiliaryUnit("auxiliaryUnit");
        materialDO1.setConversionMain(new BigDecimal("0.00"));
        materialDO1.setConversionAuxiliary(new BigDecimal("0.00"));
        final List<MaterialDO> list = Arrays.asList(materialDO1);
        when(mockModuleMapper.mapToMaterialDTOList(list)).thenReturn(materialDTOS);

        // Configure IMaterialCategoryService.listByGuidList(...).
        final CategoryDTO categoryDTO = new CategoryDTO();
        categoryDTO.setCategoryGuid("categoryGuid");
        categoryDTO.setCategoryName("categoryName");
        final MaterialDTO materialDTO2 = new MaterialDTO();
        materialDTO2.setInUnitPrice(new BigDecimal("0.00"));
        materialDTO2.setCategoryName("categoryName");
        categoryDTO.setMaterialDTOList(Arrays.asList(materialDTO2));
        final List<CategoryDTO> categoryDTOS = Arrays.asList(categoryDTO);
        final SingleListDTO dto = new SingleListDTO();
        dto.setList(Arrays.asList("value"));
        when(mockCategoryService.listByGuidList(dto)).thenReturn(categoryDTOS);

        // Run the test
        final List<MaterialDTO> result = materialStockServiceImplUnderTest.listByCondition(stockQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListByCondition_MaterialStockDOMapperReturnsNoItems() {
        // Setup
        final StockQueryDTO stockQueryDTO = new StockQueryDTO();
        stockQueryDTO.setCategory("category");
        stockQueryDTO.setSearchConditions("searchConditions");
        stockQueryDTO.setWarehouseGuid("warehouseGuid");

        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setInUnitPrice(new BigDecimal("0.00"));
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("4c97db9d-74f2-48c1-a000-ad8a5dd892c2");
        materialDTO.setEnterpriseGuid("enterpriseGuid");
        materialDTO.setCategory("category");
        final List<MaterialDTO> expectedResult = Arrays.asList(materialDTO);

        // Configure MaterialStockDOMapper.findStockPage(...).
        final StockQueryDTO stockQueryDTO1 = new StockQueryDTO();
        stockQueryDTO1.setCategory("category");
        stockQueryDTO1.setSearchConditions("searchConditions");
        stockQueryDTO1.setWarehouseGuid("warehouseGuid");
        when(mockStockMapper.findStockPage(eq(stockQueryDTO1), any(Page.class))).thenReturn(Collections.emptyList());

        // Configure ErpModuleMapper.mapToMaterialDTOList(...).
        final MaterialDTO materialDTO1 = new MaterialDTO();
        materialDTO1.setInUnitPrice(new BigDecimal("0.00"));
        materialDTO1.setCategoryName("categoryName");
        materialDTO1.setGuid("4c97db9d-74f2-48c1-a000-ad8a5dd892c2");
        materialDTO1.setEnterpriseGuid("enterpriseGuid");
        materialDTO1.setCategory("category");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO1);
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setInUnitPrice(new BigDecimal("0.00"));
        materialDO.setGuid("86d916c5-8605-4cef-bf93-63bb5a6cf6c0");
        materialDO.setUnit("unit");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        final List<MaterialDO> list = Arrays.asList(materialDO);
        when(mockModuleMapper.mapToMaterialDTOList(list)).thenReturn(materialDTOS);

        // Configure IMaterialCategoryService.listByGuidList(...).
        final CategoryDTO categoryDTO = new CategoryDTO();
        categoryDTO.setCategoryGuid("categoryGuid");
        categoryDTO.setCategoryName("categoryName");
        final MaterialDTO materialDTO2 = new MaterialDTO();
        materialDTO2.setInUnitPrice(new BigDecimal("0.00"));
        materialDTO2.setCategoryName("categoryName");
        categoryDTO.setMaterialDTOList(Arrays.asList(materialDTO2));
        final List<CategoryDTO> categoryDTOS = Arrays.asList(categoryDTO);
        final SingleListDTO dto = new SingleListDTO();
        dto.setList(Arrays.asList("value"));
        when(mockCategoryService.listByGuidList(dto)).thenReturn(categoryDTOS);

        // Run the test
        final List<MaterialDTO> result = materialStockServiceImplUnderTest.listByCondition(stockQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListByCondition_ErpModuleMapperReturnsNoItems() {
        // Setup
        final StockQueryDTO stockQueryDTO = new StockQueryDTO();
        stockQueryDTO.setCategory("category");
        stockQueryDTO.setSearchConditions("searchConditions");
        stockQueryDTO.setWarehouseGuid("warehouseGuid");

        // Configure MaterialStockDOMapper.findStockPage(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setInUnitPrice(new BigDecimal("0.00"));
        materialDO.setGuid("86d916c5-8605-4cef-bf93-63bb5a6cf6c0");
        materialDO.setUnit("unit");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        final List<MaterialDO> materialDOList = Arrays.asList(materialDO);
        final StockQueryDTO stockQueryDTO1 = new StockQueryDTO();
        stockQueryDTO1.setCategory("category");
        stockQueryDTO1.setSearchConditions("searchConditions");
        stockQueryDTO1.setWarehouseGuid("warehouseGuid");
        when(mockStockMapper.findStockPage(eq(stockQueryDTO1), any(Page.class))).thenReturn(materialDOList);

        // Configure ErpModuleMapper.mapToMaterialDTOList(...).
        final MaterialDO materialDO1 = new MaterialDO();
        materialDO1.setInUnitPrice(new BigDecimal("0.00"));
        materialDO1.setGuid("86d916c5-8605-4cef-bf93-63bb5a6cf6c0");
        materialDO1.setUnit("unit");
        materialDO1.setAuxiliaryUnit("auxiliaryUnit");
        materialDO1.setConversionMain(new BigDecimal("0.00"));
        materialDO1.setConversionAuxiliary(new BigDecimal("0.00"));
        final List<MaterialDO> list = Arrays.asList(materialDO1);
        when(mockModuleMapper.mapToMaterialDTOList(list)).thenReturn(Collections.emptyList());

        // Run the test
        final List<MaterialDTO> result = materialStockServiceImplUnderTest.listByCondition(stockQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testListByCondition_IMaterialCategoryServiceReturnsNoItems() {
        // Setup
        final StockQueryDTO stockQueryDTO = new StockQueryDTO();
        stockQueryDTO.setCategory("category");
        stockQueryDTO.setSearchConditions("searchConditions");
        stockQueryDTO.setWarehouseGuid("warehouseGuid");

        // Configure MaterialStockDOMapper.findStockPage(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setInUnitPrice(new BigDecimal("0.00"));
        materialDO.setGuid("86d916c5-8605-4cef-bf93-63bb5a6cf6c0");
        materialDO.setUnit("unit");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        final List<MaterialDO> materialDOList = Arrays.asList(materialDO);
        final StockQueryDTO stockQueryDTO1 = new StockQueryDTO();
        stockQueryDTO1.setCategory("category");
        stockQueryDTO1.setSearchConditions("searchConditions");
        stockQueryDTO1.setWarehouseGuid("warehouseGuid");
        when(mockStockMapper.findStockPage(eq(stockQueryDTO1), any(Page.class))).thenReturn(materialDOList);

        // Configure ErpModuleMapper.mapToMaterialDTOList(...).
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setInUnitPrice(new BigDecimal("0.00"));
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("4c97db9d-74f2-48c1-a000-ad8a5dd892c2");
        materialDTO.setEnterpriseGuid("enterpriseGuid");
        materialDTO.setCategory("category");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO);
        final MaterialDO materialDO1 = new MaterialDO();
        materialDO1.setInUnitPrice(new BigDecimal("0.00"));
        materialDO1.setGuid("86d916c5-8605-4cef-bf93-63bb5a6cf6c0");
        materialDO1.setUnit("unit");
        materialDO1.setAuxiliaryUnit("auxiliaryUnit");
        materialDO1.setConversionMain(new BigDecimal("0.00"));
        materialDO1.setConversionAuxiliary(new BigDecimal("0.00"));
        final List<MaterialDO> list = Arrays.asList(materialDO1);
        when(mockModuleMapper.mapToMaterialDTOList(list)).thenReturn(materialDTOS);

        // Configure IMaterialCategoryService.listByGuidList(...).
        final SingleListDTO dto = new SingleListDTO();
        dto.setList(Arrays.asList("value"));
        when(mockCategoryService.listByGuidList(dto)).thenReturn(Collections.emptyList());

        // Run the test
        final List<MaterialDTO> result = materialStockServiceImplUnderTest.listByCondition(stockQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryMaterialBySupplier() {
        // Setup
        final MaterialBySupplierQueryDTO queryDTO = new MaterialBySupplierQueryDTO();
        queryDTO.setSupplierGuid("supplierGuid");
        queryDTO.setStatus("status");
        queryDTO.setInOutType("inOutType");

        // Configure MaterialStockDOMapper.queryMaterialBySupplier(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setSettleStatus(0);
        inOutDocumentDO.setGmtCreate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inOutDocumentDO.setCreateStaffGuid("createStaffGuid");
        inOutDocumentDO.setCreateStaffName("createStaffName");
        final List<InOutDocumentDO> inOutDocumentDOS = Arrays.asList(inOutDocumentDO);
        final MaterialBySupplierQueryDTO queryDTO1 = new MaterialBySupplierQueryDTO();
        queryDTO1.setSupplierGuid("supplierGuid");
        queryDTO1.setStatus("status");
        queryDTO1.setInOutType("inOutType");
        when(mockStockMapper.queryMaterialBySupplier(queryDTO1)).thenReturn(inOutDocumentDOS);

        // Run the test
        final List<InOutDocumentSelectDTO> result = materialStockServiceImplUnderTest.queryMaterialBySupplier(queryDTO);

        // Verify the results
    }

    @Test
    public void testQueryMaterialBySupplier_MaterialStockDOMapperReturnsNoItems() {
        // Setup
        final MaterialBySupplierQueryDTO queryDTO = new MaterialBySupplierQueryDTO();
        queryDTO.setSupplierGuid("supplierGuid");
        queryDTO.setStatus("status");
        queryDTO.setInOutType("inOutType");

        // Configure MaterialStockDOMapper.queryMaterialBySupplier(...).
        final MaterialBySupplierQueryDTO queryDTO1 = new MaterialBySupplierQueryDTO();
        queryDTO1.setSupplierGuid("supplierGuid");
        queryDTO1.setStatus("status");
        queryDTO1.setInOutType("inOutType");
        when(mockStockMapper.queryMaterialBySupplier(queryDTO1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<InOutDocumentSelectDTO> result = materialStockServiceImplUnderTest.queryMaterialBySupplier(queryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testAddStock() {
        // Setup
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");

        // Configure MaterialDOMapper.selectByExample(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setInUnitPrice(new BigDecimal("0.00"));
        materialDO.setGuid("86d916c5-8605-4cef-bf93-63bb5a6cf6c0");
        materialDO.setUnit("unit");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        final List<MaterialDO> materialDOList = Arrays.asList(materialDO);
        when(mockMaterialDOMapper.selectByExample(any(MaterialDOExample.class))).thenReturn(materialDOList);

        when(mockStockMapper.countByExample(any(MaterialStockDOExample.class))).thenReturn(0L);
        when(mockStockMapper.addStock("materialGuid", new BigDecimal("0.00"), "warehouseGuid")).thenReturn(0);
        when(mockStockMapper.insertSelective(any(MaterialStockDO.class))).thenReturn(0);

        // Run the test
        final boolean result = materialStockServiceImplUnderTest.addStock(updateStockBO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testAddStock_MaterialDOMapperReturnsNoItems() {
        // Setup
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");

        when(mockMaterialDOMapper.selectByExample(any(MaterialDOExample.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> materialStockServiceImplUnderTest.addStock(updateStockBO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testBatchAddStock() {
        // Setup
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOS = Arrays.asList(updateStockBO);

        // Configure MaterialDOMapper.selectByExample(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setInUnitPrice(new BigDecimal("0.00"));
        materialDO.setGuid("86d916c5-8605-4cef-bf93-63bb5a6cf6c0");
        materialDO.setUnit("unit");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        final List<MaterialDO> materialDOList = Arrays.asList(materialDO);
        when(mockMaterialDOMapper.selectByExample(any(MaterialDOExample.class))).thenReturn(materialDOList);

        // Configure MaterialStockDOMapper.selectByExample(...).
        final MaterialStockDO materialStockDO = new MaterialStockDO();
        materialStockDO.setGuid("bb275a13-dff4-49ee-ae1e-2c68fa846591");
        materialStockDO.setMaterialGuid("materialGuid");
        materialStockDO.setWarehouseGuid("warehouseGuid");
        materialStockDO.setUnit("materialUnit");
        materialStockDO.setStock(new BigDecimal("0.00"));
        final List<MaterialStockDO> materialStockDOS = Arrays.asList(materialStockDO);
        when(mockStockMapper.selectByExample(any(MaterialStockDOExample.class))).thenReturn(materialStockDOS);

        // Configure MaterialStockDOMapper.addStockBatch(...).
        final UpdateStockBO updateStockBO1 = new UpdateStockBO();
        updateStockBO1.setMaterialGuid("materialGuid");
        updateStockBO1.setCount(new BigDecimal("0.00"));
        updateStockBO1.setMaterialUnit("materialUnit");
        updateStockBO1.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOS1 = Arrays.asList(updateStockBO1);
        when(mockStockMapper.addStockBatch(updateStockBOS1)).thenReturn(0);

        // Configure MaterialStockDOMapper.insertStockBatch(...).
        final MaterialStockDO materialStockDO1 = new MaterialStockDO();
        materialStockDO1.setGuid("bb275a13-dff4-49ee-ae1e-2c68fa846591");
        materialStockDO1.setMaterialGuid("materialGuid");
        materialStockDO1.setWarehouseGuid("warehouseGuid");
        materialStockDO1.setUnit("materialUnit");
        materialStockDO1.setStock(new BigDecimal("0.00"));
        final List<MaterialStockDO> materialStockDOS1 = Arrays.asList(materialStockDO1);
        when(mockStockMapper.insertStockBatch(materialStockDOS1)).thenReturn(0);

        // Run the test
        final boolean result = materialStockServiceImplUnderTest.batchAddStock(updateStockBOS);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testBatchAddStock_MaterialDOMapperReturnsNoItems() {
        // Setup
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOS = Arrays.asList(updateStockBO);
        when(mockMaterialDOMapper.selectByExample(any(MaterialDOExample.class))).thenReturn(Collections.emptyList());

        // Configure MaterialStockDOMapper.selectByExample(...).
        final MaterialStockDO materialStockDO = new MaterialStockDO();
        materialStockDO.setGuid("bb275a13-dff4-49ee-ae1e-2c68fa846591");
        materialStockDO.setMaterialGuid("materialGuid");
        materialStockDO.setWarehouseGuid("warehouseGuid");
        materialStockDO.setUnit("materialUnit");
        materialStockDO.setStock(new BigDecimal("0.00"));
        final List<MaterialStockDO> materialStockDOS = Arrays.asList(materialStockDO);
        when(mockStockMapper.selectByExample(any(MaterialStockDOExample.class))).thenReturn(materialStockDOS);

        // Configure MaterialStockDOMapper.addStockBatch(...).
        final UpdateStockBO updateStockBO1 = new UpdateStockBO();
        updateStockBO1.setMaterialGuid("materialGuid");
        updateStockBO1.setCount(new BigDecimal("0.00"));
        updateStockBO1.setMaterialUnit("materialUnit");
        updateStockBO1.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOS1 = Arrays.asList(updateStockBO1);
        when(mockStockMapper.addStockBatch(updateStockBOS1)).thenReturn(0);

        // Configure MaterialStockDOMapper.insertStockBatch(...).
        final MaterialStockDO materialStockDO1 = new MaterialStockDO();
        materialStockDO1.setGuid("bb275a13-dff4-49ee-ae1e-2c68fa846591");
        materialStockDO1.setMaterialGuid("materialGuid");
        materialStockDO1.setWarehouseGuid("warehouseGuid");
        materialStockDO1.setUnit("materialUnit");
        materialStockDO1.setStock(new BigDecimal("0.00"));
        final List<MaterialStockDO> materialStockDOS1 = Arrays.asList(materialStockDO1);
        when(mockStockMapper.insertStockBatch(materialStockDOS1)).thenReturn(0);

        // Run the test
        final boolean result = materialStockServiceImplUnderTest.batchAddStock(updateStockBOS);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testBatchAddStock_MaterialStockDOMapperSelectByExampleReturnsNoItems() {
        // Setup
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOS = Arrays.asList(updateStockBO);

        // Configure MaterialDOMapper.selectByExample(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setInUnitPrice(new BigDecimal("0.00"));
        materialDO.setGuid("86d916c5-8605-4cef-bf93-63bb5a6cf6c0");
        materialDO.setUnit("unit");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        final List<MaterialDO> materialDOList = Arrays.asList(materialDO);
        when(mockMaterialDOMapper.selectByExample(any(MaterialDOExample.class))).thenReturn(materialDOList);

        when(mockStockMapper.selectByExample(any(MaterialStockDOExample.class))).thenReturn(Collections.emptyList());

        // Configure MaterialStockDOMapper.addStockBatch(...).
        final UpdateStockBO updateStockBO1 = new UpdateStockBO();
        updateStockBO1.setMaterialGuid("materialGuid");
        updateStockBO1.setCount(new BigDecimal("0.00"));
        updateStockBO1.setMaterialUnit("materialUnit");
        updateStockBO1.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOS1 = Arrays.asList(updateStockBO1);
        when(mockStockMapper.addStockBatch(updateStockBOS1)).thenReturn(0);

        // Configure MaterialStockDOMapper.insertStockBatch(...).
        final MaterialStockDO materialStockDO = new MaterialStockDO();
        materialStockDO.setGuid("bb275a13-dff4-49ee-ae1e-2c68fa846591");
        materialStockDO.setMaterialGuid("materialGuid");
        materialStockDO.setWarehouseGuid("warehouseGuid");
        materialStockDO.setUnit("materialUnit");
        materialStockDO.setStock(new BigDecimal("0.00"));
        final List<MaterialStockDO> materialStockDOS = Arrays.asList(materialStockDO);
        when(mockStockMapper.insertStockBatch(materialStockDOS)).thenReturn(0);

        // Run the test
        final boolean result = materialStockServiceImplUnderTest.batchAddStock(updateStockBOS);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testReduceStock() {
        // Setup
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");

        // Configure MaterialDOMapper.selectByExample(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setInUnitPrice(new BigDecimal("0.00"));
        materialDO.setGuid("86d916c5-8605-4cef-bf93-63bb5a6cf6c0");
        materialDO.setUnit("unit");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        final List<MaterialDO> materialDOList = Arrays.asList(materialDO);
        when(mockMaterialDOMapper.selectByExample(any(MaterialDOExample.class))).thenReturn(materialDOList);

        // Configure MaterialStockDOMapper.selectByExample(...).
        final MaterialStockDO materialStockDO = new MaterialStockDO();
        materialStockDO.setGuid("bb275a13-dff4-49ee-ae1e-2c68fa846591");
        materialStockDO.setMaterialGuid("materialGuid");
        materialStockDO.setWarehouseGuid("warehouseGuid");
        materialStockDO.setUnit("materialUnit");
        materialStockDO.setStock(new BigDecimal("0.00"));
        final List<MaterialStockDO> materialStockDOS = Arrays.asList(materialStockDO);
        when(mockStockMapper.selectByExample(any(MaterialStockDOExample.class))).thenReturn(materialStockDOS);

        when(mockStockMapper.reduceStock("materialGuid", new BigDecimal("0.00"), "warehouseGuid")).thenReturn(0);

        // Run the test
        final boolean result = materialStockServiceImplUnderTest.reduceStock(updateStockBO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testReduceStock_MaterialDOMapperReturnsNoItems() {
        // Setup
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");

        when(mockMaterialDOMapper.selectByExample(any(MaterialDOExample.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> materialStockServiceImplUnderTest.reduceStock(updateStockBO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testReduceStock_MaterialStockDOMapperSelectByExampleReturnsNoItems() {
        // Setup
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");

        // Configure MaterialDOMapper.selectByExample(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setInUnitPrice(new BigDecimal("0.00"));
        materialDO.setGuid("86d916c5-8605-4cef-bf93-63bb5a6cf6c0");
        materialDO.setUnit("unit");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        final List<MaterialDO> materialDOList = Arrays.asList(materialDO);
        when(mockMaterialDOMapper.selectByExample(any(MaterialDOExample.class))).thenReturn(materialDOList);

        when(mockStockMapper.selectByExample(any(MaterialStockDOExample.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> materialStockServiceImplUnderTest.reduceStock(updateStockBO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testBatchReduceStock() {
        // Setup
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList = Arrays.asList(updateStockBO);

        // Configure MaterialDOMapper.selectByExample(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setInUnitPrice(new BigDecimal("0.00"));
        materialDO.setGuid("86d916c5-8605-4cef-bf93-63bb5a6cf6c0");
        materialDO.setUnit("unit");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        final List<MaterialDO> materialDOList = Arrays.asList(materialDO);
        when(mockMaterialDOMapper.selectByExample(any(MaterialDOExample.class))).thenReturn(materialDOList);

        // Configure MaterialStockDOMapper.selectByExample(...).
        final MaterialStockDO materialStockDO = new MaterialStockDO();
        materialStockDO.setGuid("bb275a13-dff4-49ee-ae1e-2c68fa846591");
        materialStockDO.setMaterialGuid("materialGuid");
        materialStockDO.setWarehouseGuid("warehouseGuid");
        materialStockDO.setUnit("materialUnit");
        materialStockDO.setStock(new BigDecimal("0.00"));
        final List<MaterialStockDO> materialStockDOS = Arrays.asList(materialStockDO);
        when(mockStockMapper.selectByExample(any(MaterialStockDOExample.class))).thenReturn(materialStockDOS);

        // Run the test
        final boolean result = materialStockServiceImplUnderTest.batchReduceStock(updateStockBOList);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm MaterialStockDOMapper.reduceStockBatch(...).
        final UpdateStockBO updateStockBO1 = new UpdateStockBO();
        updateStockBO1.setMaterialGuid("materialGuid");
        updateStockBO1.setCount(new BigDecimal("0.00"));
        updateStockBO1.setMaterialUnit("materialUnit");
        updateStockBO1.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList1 = Arrays.asList(updateStockBO1);
        verify(mockStockMapper).reduceStockBatch(updateStockBOList1);

        // Confirm MaterialStockDOMapper.insertStockBatch(...).
        final MaterialStockDO materialStockDO1 = new MaterialStockDO();
        materialStockDO1.setGuid("bb275a13-dff4-49ee-ae1e-2c68fa846591");
        materialStockDO1.setMaterialGuid("materialGuid");
        materialStockDO1.setWarehouseGuid("warehouseGuid");
        materialStockDO1.setUnit("materialUnit");
        materialStockDO1.setStock(new BigDecimal("0.00"));
        final List<MaterialStockDO> materialStockDOS1 = Arrays.asList(materialStockDO1);
        verify(mockStockMapper).insertStockBatch(materialStockDOS1);
    }

    @Test
    public void testBatchReduceStock_MaterialDOMapperReturnsNoItems() {
        // Setup
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList = Arrays.asList(updateStockBO);
        when(mockMaterialDOMapper.selectByExample(any(MaterialDOExample.class))).thenReturn(Collections.emptyList());

        // Configure MaterialStockDOMapper.selectByExample(...).
        final MaterialStockDO materialStockDO = new MaterialStockDO();
        materialStockDO.setGuid("bb275a13-dff4-49ee-ae1e-2c68fa846591");
        materialStockDO.setMaterialGuid("materialGuid");
        materialStockDO.setWarehouseGuid("warehouseGuid");
        materialStockDO.setUnit("materialUnit");
        materialStockDO.setStock(new BigDecimal("0.00"));
        final List<MaterialStockDO> materialStockDOS = Arrays.asList(materialStockDO);
        when(mockStockMapper.selectByExample(any(MaterialStockDOExample.class))).thenReturn(materialStockDOS);

        // Run the test
        final boolean result = materialStockServiceImplUnderTest.batchReduceStock(updateStockBOList);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm MaterialStockDOMapper.reduceStockBatch(...).
        final UpdateStockBO updateStockBO1 = new UpdateStockBO();
        updateStockBO1.setMaterialGuid("materialGuid");
        updateStockBO1.setCount(new BigDecimal("0.00"));
        updateStockBO1.setMaterialUnit("materialUnit");
        updateStockBO1.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList1 = Arrays.asList(updateStockBO1);
        verify(mockStockMapper).reduceStockBatch(updateStockBOList1);

        // Confirm MaterialStockDOMapper.insertStockBatch(...).
        final MaterialStockDO materialStockDO1 = new MaterialStockDO();
        materialStockDO1.setGuid("bb275a13-dff4-49ee-ae1e-2c68fa846591");
        materialStockDO1.setMaterialGuid("materialGuid");
        materialStockDO1.setWarehouseGuid("warehouseGuid");
        materialStockDO1.setUnit("materialUnit");
        materialStockDO1.setStock(new BigDecimal("0.00"));
        final List<MaterialStockDO> materialStockDOS1 = Arrays.asList(materialStockDO1);
        verify(mockStockMapper).insertStockBatch(materialStockDOS1);
    }

    @Test
    public void testBatchReduceStock_MaterialStockDOMapperSelectByExampleReturnsNoItems() {
        // Setup
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList = Arrays.asList(updateStockBO);

        // Configure MaterialDOMapper.selectByExample(...).
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setInUnitPrice(new BigDecimal("0.00"));
        materialDO.setGuid("86d916c5-8605-4cef-bf93-63bb5a6cf6c0");
        materialDO.setUnit("unit");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");
        materialDO.setConversionMain(new BigDecimal("0.00"));
        materialDO.setConversionAuxiliary(new BigDecimal("0.00"));
        final List<MaterialDO> materialDOList = Arrays.asList(materialDO);
        when(mockMaterialDOMapper.selectByExample(any(MaterialDOExample.class))).thenReturn(materialDOList);

        when(mockStockMapper.selectByExample(any(MaterialStockDOExample.class))).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = materialStockServiceImplUnderTest.batchReduceStock(updateStockBOList);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm MaterialStockDOMapper.reduceStockBatch(...).
        final UpdateStockBO updateStockBO1 = new UpdateStockBO();
        updateStockBO1.setMaterialGuid("materialGuid");
        updateStockBO1.setCount(new BigDecimal("0.00"));
        updateStockBO1.setMaterialUnit("materialUnit");
        updateStockBO1.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList1 = Arrays.asList(updateStockBO1);
        verify(mockStockMapper).reduceStockBatch(updateStockBOList1);

        // Confirm MaterialStockDOMapper.insertStockBatch(...).
        final MaterialStockDO materialStockDO = new MaterialStockDO();
        materialStockDO.setGuid("bb275a13-dff4-49ee-ae1e-2c68fa846591");
        materialStockDO.setMaterialGuid("materialGuid");
        materialStockDO.setWarehouseGuid("warehouseGuid");
        materialStockDO.setUnit("materialUnit");
        materialStockDO.setStock(new BigDecimal("0.00"));
        final List<MaterialStockDO> materialStockDOS = Arrays.asList(materialStockDO);
        verify(mockStockMapper).insertStockBatch(materialStockDOS);
    }
}
