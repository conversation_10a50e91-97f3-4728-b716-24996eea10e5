package com.holderzone.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.erp.dao.InventoryMapper;
import com.holderzone.erp.entity.domain.GoodsDO;
import com.holderzone.erp.entity.domain.GoodsOfInventoryDO;
import com.holderzone.erp.entity.domain.InventoryDO;
import com.holderzone.erp.mapperstruct.InventoryMapstruct;
import com.holderzone.erp.service.DistributedIdService;
import com.holderzone.erp.service.GoodsOfInventoryService;
import com.holderzone.erp.service.GoodsSerialService;
import com.holderzone.erp.service.GoodsService;
import com.holderzone.erp.utils.PageAdapter;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.erpretail.InOutGoodsDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.CreateInventoryReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.InsertGoodsSerialReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.InventoryOverviewReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.GoodsSumInfoRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.InventoryDetailRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.InventoryManageRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class InventoryServiceImplTest {

    @Mock
    private DistributedIdService mockDistributedIdService;
    @Mock
    private InventoryMapstruct mockInventoryMapstruct;
    @Mock
    private GoodsOfInventoryService mockGoodsOfInventoryService;
    @Mock
    private GoodsService mockGoodsService;
    @Mock
    private InventoryMapper mockInventoryMapper;
    @Mock
    private GoodsSerialService mockGoodsSerialService;

    @InjectMocks
    private InventoryServiceImpl inventoryServiceImplUnderTest;

    @Test
    public void testCreateInventory() {
        // Setup
        final CreateInventoryReqDTO createInventoryReqDTO = new CreateInventoryReqDTO();
        createInventoryReqDTO.setInventoryDate("inventoryDate");
        final GoodsSumInfoRespDTO goodsSumInfoRespDTO = new GoodsSumInfoRespDTO();
        goodsSumInfoRespDTO.setGoodsGuid("goodsGuid");
        goodsSumInfoRespDTO.setGoodsName("goodsName");
        goodsSumInfoRespDTO.setCount(new BigDecimal("0.00"));
        goodsSumInfoRespDTO.setUnitName("unitName");
        goodsSumInfoRespDTO.setInventoryCount(new BigDecimal("0.00"));
        createInventoryReqDTO.setGoodsList(Arrays.asList(goodsSumInfoRespDTO));

        // Configure InventoryMapstruct.fromCreateInventoryReqDTOTOInventoryDO(...).
        final InventoryDO inventoryDO = new InventoryDO();
        inventoryDO.setGuid("575a80a5-7542-42a2-9171-da9681ba902d");
        inventoryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        inventoryDO.setInvoiceType(0);
        inventoryDO.setInvoiceNo("575a80a5-7542-42a2-9171-da9681ba902d");
        inventoryDO.setInventoryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        inventoryDO.setOperator("operator");
        inventoryDO.setStatus(0);
        inventoryDO.setInvoiceMaker("invoiceMaker");
        final CreateInventoryReqDTO createInventoryReqDTO1 = new CreateInventoryReqDTO();
        createInventoryReqDTO1.setInventoryDate("inventoryDate");
        final GoodsSumInfoRespDTO goodsSumInfoRespDTO1 = new GoodsSumInfoRespDTO();
        goodsSumInfoRespDTO1.setGoodsGuid("goodsGuid");
        goodsSumInfoRespDTO1.setGoodsName("goodsName");
        goodsSumInfoRespDTO1.setCount(new BigDecimal("0.00"));
        goodsSumInfoRespDTO1.setUnitName("unitName");
        goodsSumInfoRespDTO1.setInventoryCount(new BigDecimal("0.00"));
        createInventoryReqDTO1.setGoodsList(Arrays.asList(goodsSumInfoRespDTO1));
        when(mockInventoryMapstruct.fromCreateInventoryReqDTOTOInventoryDO(createInventoryReqDTO1))
                .thenReturn(inventoryDO);

        when(mockDistributedIdService.nextInventoryGuid()).thenReturn("575a80a5-7542-42a2-9171-da9681ba902d");
        when(mockDistributedIdService.nextBatchGoodsItemGuid(0L)).thenReturn(Arrays.asList("value"));

        // Run the test
        final String result = inventoryServiceImplUnderTest.createInventory(createInventoryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo("575a80a5-7542-42a2-9171-da9681ba902d");
        verify(mockGoodsService).updateGoodsRepertoryNum("goodsGuid", new BigDecimal("0.00"));

        // Confirm GoodsOfInventoryService.insertGoodsOfInventory(...).
        final GoodsOfInventoryDO goodsOfInventoryDO = new GoodsOfInventoryDO();
        goodsOfInventoryDO.setGuid("c70a5e2a-8ce3-40c3-9cc1-fae145b45fad");
        goodsOfInventoryDO.setGoodsGuid("goodsGuid");
        goodsOfInventoryDO.setInventoryGuid("575a80a5-7542-42a2-9171-da9681ba902d");
        goodsOfInventoryDO.setInventoryCount(new BigDecimal("0.00"));
        goodsOfInventoryDO.setRepertorySnapshot(new BigDecimal("0.00"));
        final List<GoodsOfInventoryDO> list = Arrays.asList(goodsOfInventoryDO);
        verify(mockGoodsOfInventoryService).insertGoodsOfInventory(list);

        // Confirm GoodsSerialService.insertGoodsSerial(...).
        final InsertGoodsSerialReqDTO insertGoodsSerialReqDTO = new InsertGoodsSerialReqDTO();
        insertGoodsSerialReqDTO.setGoodsGuid("goodsGuid");
        insertGoodsSerialReqDTO.setInvoiceType(0);
        insertGoodsSerialReqDTO.setChangeNum(new BigDecimal("0.00"));
        insertGoodsSerialReqDTO.setUnitName("unitName");
        insertGoodsSerialReqDTO.setInvoiceNo("575a80a5-7542-42a2-9171-da9681ba902d");
        final List<InsertGoodsSerialReqDTO> insertGoodsSerialReqDTOList = Arrays.asList(insertGoodsSerialReqDTO);
        verify(mockGoodsSerialService).insertGoodsSerial(insertGoodsSerialReqDTOList);
    }

    @Test
    public void testCreateInventory_DistributedIdServiceNextBatchGoodsItemGuidReturnsNoItems() {
        // Setup
        final CreateInventoryReqDTO createInventoryReqDTO = new CreateInventoryReqDTO();
        createInventoryReqDTO.setInventoryDate("inventoryDate");
        final GoodsSumInfoRespDTO goodsSumInfoRespDTO = new GoodsSumInfoRespDTO();
        goodsSumInfoRespDTO.setGoodsGuid("goodsGuid");
        goodsSumInfoRespDTO.setGoodsName("goodsName");
        goodsSumInfoRespDTO.setCount(new BigDecimal("0.00"));
        goodsSumInfoRespDTO.setUnitName("unitName");
        goodsSumInfoRespDTO.setInventoryCount(new BigDecimal("0.00"));
        createInventoryReqDTO.setGoodsList(Arrays.asList(goodsSumInfoRespDTO));

        // Configure InventoryMapstruct.fromCreateInventoryReqDTOTOInventoryDO(...).
        final InventoryDO inventoryDO = new InventoryDO();
        inventoryDO.setGuid("575a80a5-7542-42a2-9171-da9681ba902d");
        inventoryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        inventoryDO.setInvoiceType(0);
        inventoryDO.setInvoiceNo("575a80a5-7542-42a2-9171-da9681ba902d");
        inventoryDO.setInventoryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        inventoryDO.setOperator("operator");
        inventoryDO.setStatus(0);
        inventoryDO.setInvoiceMaker("invoiceMaker");
        final CreateInventoryReqDTO createInventoryReqDTO1 = new CreateInventoryReqDTO();
        createInventoryReqDTO1.setInventoryDate("inventoryDate");
        final GoodsSumInfoRespDTO goodsSumInfoRespDTO1 = new GoodsSumInfoRespDTO();
        goodsSumInfoRespDTO1.setGoodsGuid("goodsGuid");
        goodsSumInfoRespDTO1.setGoodsName("goodsName");
        goodsSumInfoRespDTO1.setCount(new BigDecimal("0.00"));
        goodsSumInfoRespDTO1.setUnitName("unitName");
        goodsSumInfoRespDTO1.setInventoryCount(new BigDecimal("0.00"));
        createInventoryReqDTO1.setGoodsList(Arrays.asList(goodsSumInfoRespDTO1));
        when(mockInventoryMapstruct.fromCreateInventoryReqDTOTOInventoryDO(createInventoryReqDTO1))
                .thenReturn(inventoryDO);

        when(mockDistributedIdService.nextInventoryGuid()).thenReturn("575a80a5-7542-42a2-9171-da9681ba902d");
        when(mockDistributedIdService.nextBatchGoodsItemGuid(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final String result = inventoryServiceImplUnderTest.createInventory(createInventoryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo("575a80a5-7542-42a2-9171-da9681ba902d");
        verify(mockGoodsService).updateGoodsRepertoryNum("goodsGuid", new BigDecimal("0.00"));

        // Confirm GoodsOfInventoryService.insertGoodsOfInventory(...).
        final GoodsOfInventoryDO goodsOfInventoryDO = new GoodsOfInventoryDO();
        goodsOfInventoryDO.setGuid("c70a5e2a-8ce3-40c3-9cc1-fae145b45fad");
        goodsOfInventoryDO.setGoodsGuid("goodsGuid");
        goodsOfInventoryDO.setInventoryGuid("575a80a5-7542-42a2-9171-da9681ba902d");
        goodsOfInventoryDO.setInventoryCount(new BigDecimal("0.00"));
        goodsOfInventoryDO.setRepertorySnapshot(new BigDecimal("0.00"));
        final List<GoodsOfInventoryDO> list = Arrays.asList(goodsOfInventoryDO);
        verify(mockGoodsOfInventoryService).insertGoodsOfInventory(list);

        // Confirm GoodsSerialService.insertGoodsSerial(...).
        final InsertGoodsSerialReqDTO insertGoodsSerialReqDTO = new InsertGoodsSerialReqDTO();
        insertGoodsSerialReqDTO.setGoodsGuid("goodsGuid");
        insertGoodsSerialReqDTO.setInvoiceType(0);
        insertGoodsSerialReqDTO.setChangeNum(new BigDecimal("0.00"));
        insertGoodsSerialReqDTO.setUnitName("unitName");
        insertGoodsSerialReqDTO.setInvoiceNo("575a80a5-7542-42a2-9171-da9681ba902d");
        final List<InsertGoodsSerialReqDTO> insertGoodsSerialReqDTOList = Arrays.asList(insertGoodsSerialReqDTO);
        verify(mockGoodsSerialService).insertGoodsSerial(insertGoodsSerialReqDTOList);
    }

    @Test
    public void testQueryInventoryDetail() {
        // Setup
        final SingleDataDTO singleDataDTO = new SingleDataDTO("data", Arrays.asList("value"));
        final InventoryDetailRespDTO expectedResult = new InventoryDetailRespDTO();
        expectedResult.setDate("date");
        expectedResult.setInvoiceName("des");
        expectedResult.setInvoiceTime("invoiceTime");
        final GoodsSumInfoRespDTO goodsSumInfoRespDTO = new GoodsSumInfoRespDTO();
        goodsSumInfoRespDTO.setGoodsGuid("goodsGuid");
        goodsSumInfoRespDTO.setGoodsName("goodsName");
        goodsSumInfoRespDTO.setCount(new BigDecimal("0.00"));
        goodsSumInfoRespDTO.setUnitName("unitName");
        goodsSumInfoRespDTO.setInventoryCount(new BigDecimal("0.00"));
        expectedResult.setGoodsList(Arrays.asList(goodsSumInfoRespDTO));

        // Configure InventoryMapstruct.fromInventoryDOTODetailRespDTO(...).
        final InventoryDetailRespDTO inventoryDetailRespDTO = new InventoryDetailRespDTO();
        inventoryDetailRespDTO.setDate("date");
        inventoryDetailRespDTO.setInvoiceName("des");
        inventoryDetailRespDTO.setInvoiceTime("invoiceTime");
        final GoodsSumInfoRespDTO goodsSumInfoRespDTO1 = new GoodsSumInfoRespDTO();
        goodsSumInfoRespDTO1.setGoodsGuid("goodsGuid");
        goodsSumInfoRespDTO1.setGoodsName("goodsName");
        goodsSumInfoRespDTO1.setCount(new BigDecimal("0.00"));
        goodsSumInfoRespDTO1.setUnitName("unitName");
        goodsSumInfoRespDTO1.setInventoryCount(new BigDecimal("0.00"));
        inventoryDetailRespDTO.setGoodsList(Arrays.asList(goodsSumInfoRespDTO1));
        final InventoryDO inventoryDO = new InventoryDO();
        inventoryDO.setGuid("575a80a5-7542-42a2-9171-da9681ba902d");
        inventoryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        inventoryDO.setInvoiceType(0);
        inventoryDO.setInvoiceNo("575a80a5-7542-42a2-9171-da9681ba902d");
        inventoryDO.setInventoryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        inventoryDO.setOperator("operator");
        inventoryDO.setStatus(0);
        inventoryDO.setInvoiceMaker("invoiceMaker");
        when(mockInventoryMapstruct.fromInventoryDOTODetailRespDTO(inventoryDO)).thenReturn(inventoryDetailRespDTO);

        // Configure GoodsOfInventoryService.queryGoodsOfInventory(...).
        final GoodsOfInventoryDO goodsOfInventoryDO = new GoodsOfInventoryDO();
        goodsOfInventoryDO.setGuid("c70a5e2a-8ce3-40c3-9cc1-fae145b45fad");
        goodsOfInventoryDO.setGoodsGuid("goodsGuid");
        goodsOfInventoryDO.setInventoryGuid("575a80a5-7542-42a2-9171-da9681ba902d");
        goodsOfInventoryDO.setInventoryCount(new BigDecimal("0.00"));
        goodsOfInventoryDO.setRepertorySnapshot(new BigDecimal("0.00"));
        final List<GoodsOfInventoryDO> goodsOfInventoryDOS = Arrays.asList(goodsOfInventoryDO);
        when(mockGoodsOfInventoryService.queryGoodsOfInventory("data")).thenReturn(goodsOfInventoryDOS);

        // Configure GoodsService.queryGoodsInfo(...).
        final InOutGoodsDTO inOutGoodsDTO = new InOutGoodsDTO();
        inOutGoodsDTO.setGoodsGuid("goodsGuid");
        inOutGoodsDTO.setGoodsCode("goodsCode");
        inOutGoodsDTO.setGoodsName("goodsName");
        inOutGoodsDTO.setBarCode("barCode");
        inOutGoodsDTO.setUnitName("unitName");
        when(mockGoodsService.queryGoodsInfo("goodsGuid")).thenReturn(inOutGoodsDTO);

        // Run the test
        final InventoryDetailRespDTO result = inventoryServiceImplUnderTest.queryInventoryDetail(singleDataDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryInventoryDetail_GoodsOfInventoryServiceReturnsNoItems() {
        // Setup
        final SingleDataDTO singleDataDTO = new SingleDataDTO("data", Arrays.asList("value"));
        final InventoryDetailRespDTO expectedResult = new InventoryDetailRespDTO();
        expectedResult.setDate("date");
        expectedResult.setInvoiceName("des");
        expectedResult.setInvoiceTime("invoiceTime");
        final GoodsSumInfoRespDTO goodsSumInfoRespDTO = new GoodsSumInfoRespDTO();
        goodsSumInfoRespDTO.setGoodsGuid("goodsGuid");
        goodsSumInfoRespDTO.setGoodsName("goodsName");
        goodsSumInfoRespDTO.setCount(new BigDecimal("0.00"));
        goodsSumInfoRespDTO.setUnitName("unitName");
        goodsSumInfoRespDTO.setInventoryCount(new BigDecimal("0.00"));
        expectedResult.setGoodsList(Arrays.asList(goodsSumInfoRespDTO));

        // Configure InventoryMapstruct.fromInventoryDOTODetailRespDTO(...).
        final InventoryDetailRespDTO inventoryDetailRespDTO = new InventoryDetailRespDTO();
        inventoryDetailRespDTO.setDate("date");
        inventoryDetailRespDTO.setInvoiceName("des");
        inventoryDetailRespDTO.setInvoiceTime("invoiceTime");
        final GoodsSumInfoRespDTO goodsSumInfoRespDTO1 = new GoodsSumInfoRespDTO();
        goodsSumInfoRespDTO1.setGoodsGuid("goodsGuid");
        goodsSumInfoRespDTO1.setGoodsName("goodsName");
        goodsSumInfoRespDTO1.setCount(new BigDecimal("0.00"));
        goodsSumInfoRespDTO1.setUnitName("unitName");
        goodsSumInfoRespDTO1.setInventoryCount(new BigDecimal("0.00"));
        inventoryDetailRespDTO.setGoodsList(Arrays.asList(goodsSumInfoRespDTO1));
        final InventoryDO inventoryDO = new InventoryDO();
        inventoryDO.setGuid("575a80a5-7542-42a2-9171-da9681ba902d");
        inventoryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        inventoryDO.setInvoiceType(0);
        inventoryDO.setInvoiceNo("575a80a5-7542-42a2-9171-da9681ba902d");
        inventoryDO.setInventoryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        inventoryDO.setOperator("operator");
        inventoryDO.setStatus(0);
        inventoryDO.setInvoiceMaker("invoiceMaker");
        when(mockInventoryMapstruct.fromInventoryDOTODetailRespDTO(inventoryDO)).thenReturn(inventoryDetailRespDTO);

        when(mockGoodsOfInventoryService.queryGoodsOfInventory("data")).thenReturn(Collections.emptyList());

        // Run the test
        final InventoryDetailRespDTO result = inventoryServiceImplUnderTest.queryInventoryDetail(singleDataDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryInventoryOverview() {
        // Setup
        final InventoryOverviewReqDTO inventoryOverviewReqDTO = new InventoryOverviewReqDTO();
        inventoryOverviewReqDTO.setStartDate("startDate");
        inventoryOverviewReqDTO.setEndDate("endDate");
        inventoryOverviewReqDTO.setType("type");
        inventoryOverviewReqDTO.setStartDateTime("startDateTime");
        inventoryOverviewReqDTO.setEndDateTime("endDateTime");

        // Configure InventoryMapper.queryInventoryOverview(...).
        final InventoryOverviewReqDTO inventoryOverviewReqDTO1 = new InventoryOverviewReqDTO();
        inventoryOverviewReqDTO1.setStartDate("startDate");
        inventoryOverviewReqDTO1.setEndDate("endDate");
        inventoryOverviewReqDTO1.setType("type");
        inventoryOverviewReqDTO1.setStartDateTime("startDateTime");
        inventoryOverviewReqDTO1.setEndDateTime("endDateTime");
        when(mockInventoryMapper.queryInventoryOverview(any(PageAdapter.class),
                eq(inventoryOverviewReqDTO1))).thenReturn(null);

        // Configure InventoryMapstruct.fromInventoryDOTOManageRespDTO(...).
        final InventoryManageRespDTO inventoryManageRespDTO = new InventoryManageRespDTO();
        inventoryManageRespDTO.setInvoiceNo("invoiceNo");
        inventoryManageRespDTO.setInvoiceName("des");
        inventoryManageRespDTO.setInventoryDate("inventoryDate");
        inventoryManageRespDTO.setOperator("operator");
        inventoryManageRespDTO.setStatus(0);
        final InventoryDO inventoryDO = new InventoryDO();
        inventoryDO.setGuid("575a80a5-7542-42a2-9171-da9681ba902d");
        inventoryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        inventoryDO.setInvoiceType(0);
        inventoryDO.setInvoiceNo("575a80a5-7542-42a2-9171-da9681ba902d");
        inventoryDO.setInventoryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        inventoryDO.setOperator("operator");
        inventoryDO.setStatus(0);
        inventoryDO.setInvoiceMaker("invoiceMaker");
        when(mockInventoryMapstruct.fromInventoryDOTOManageRespDTO(inventoryDO)).thenReturn(inventoryManageRespDTO);

        // Run the test
        final Page<InventoryManageRespDTO> result = inventoryServiceImplUnderTest.queryInventoryOverview(
                inventoryOverviewReqDTO);

        // Verify the results
    }

    @Test
    public void testInvalidInventory() {
        // Setup
        // Configure GoodsOfInventoryService.queryGoodsOfInventory(...).
        final GoodsOfInventoryDO goodsOfInventoryDO = new GoodsOfInventoryDO();
        goodsOfInventoryDO.setGuid("c70a5e2a-8ce3-40c3-9cc1-fae145b45fad");
        goodsOfInventoryDO.setGoodsGuid("goodsGuid");
        goodsOfInventoryDO.setInventoryGuid("575a80a5-7542-42a2-9171-da9681ba902d");
        goodsOfInventoryDO.setInventoryCount(new BigDecimal("0.00"));
        goodsOfInventoryDO.setRepertorySnapshot(new BigDecimal("0.00"));
        final List<GoodsOfInventoryDO> goodsOfInventoryDOS = Arrays.asList(goodsOfInventoryDO);
        when(mockGoodsOfInventoryService.queryGoodsOfInventory("inventoryGuid")).thenReturn(goodsOfInventoryDOS);

        // Configure GoodsService.getOne(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setId(0L);
        goodsDO.setGuid("0c811716-70a1-4869-9134-9fdb677e9c45");
        goodsDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        goodsDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        when(mockGoodsService.getOne(any(LambdaQueryWrapper.class))).thenReturn(goodsDO);

        // Configure GoodsService.saveOrUpdateBatch(...).
        final GoodsDO goodsDO1 = new GoodsDO();
        goodsDO1.setId(0L);
        goodsDO1.setGuid("0c811716-70a1-4869-9134-9fdb677e9c45");
        goodsDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        goodsDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        goodsDO1.setRemainRepertoryNum(new BigDecimal("0.00"));
        final List<GoodsDO> entityList = Arrays.asList(goodsDO1);
        when(mockGoodsService.saveOrUpdateBatch(entityList)).thenReturn(false);

        // Run the test
        final boolean result = inventoryServiceImplUnderTest.invalidInventory("inventoryGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testInvalidInventory_GoodsOfInventoryServiceReturnsNoItems() {
        // Setup
        when(mockGoodsOfInventoryService.queryGoodsOfInventory("inventoryGuid")).thenReturn(Collections.emptyList());

        // Configure GoodsService.saveOrUpdateBatch(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setId(0L);
        goodsDO.setGuid("0c811716-70a1-4869-9134-9fdb677e9c45");
        goodsDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        goodsDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        final List<GoodsDO> entityList = Arrays.asList(goodsDO);
        when(mockGoodsService.saveOrUpdateBatch(entityList)).thenReturn(false);

        // Run the test
        final boolean result = inventoryServiceImplUnderTest.invalidInventory("inventoryGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testInvalidInventory_GoodsServiceSaveOrUpdateBatchReturnsTrue() {
        // Setup
        // Configure GoodsOfInventoryService.queryGoodsOfInventory(...).
        final GoodsOfInventoryDO goodsOfInventoryDO = new GoodsOfInventoryDO();
        goodsOfInventoryDO.setGuid("c70a5e2a-8ce3-40c3-9cc1-fae145b45fad");
        goodsOfInventoryDO.setGoodsGuid("goodsGuid");
        goodsOfInventoryDO.setInventoryGuid("575a80a5-7542-42a2-9171-da9681ba902d");
        goodsOfInventoryDO.setInventoryCount(new BigDecimal("0.00"));
        goodsOfInventoryDO.setRepertorySnapshot(new BigDecimal("0.00"));
        final List<GoodsOfInventoryDO> goodsOfInventoryDOS = Arrays.asList(goodsOfInventoryDO);
        when(mockGoodsOfInventoryService.queryGoodsOfInventory("inventoryGuid")).thenReturn(goodsOfInventoryDOS);

        // Configure GoodsService.getOne(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setId(0L);
        goodsDO.setGuid("0c811716-70a1-4869-9134-9fdb677e9c45");
        goodsDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        goodsDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        when(mockGoodsService.getOne(any(LambdaQueryWrapper.class))).thenReturn(goodsDO);

        // Configure GoodsService.saveOrUpdateBatch(...).
        final GoodsDO goodsDO1 = new GoodsDO();
        goodsDO1.setId(0L);
        goodsDO1.setGuid("0c811716-70a1-4869-9134-9fdb677e9c45");
        goodsDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        goodsDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        goodsDO1.setRemainRepertoryNum(new BigDecimal("0.00"));
        final List<GoodsDO> entityList = Arrays.asList(goodsDO1);
        when(mockGoodsService.saveOrUpdateBatch(entityList)).thenReturn(true);

        // Run the test
        final boolean result = inventoryServiceImplUnderTest.invalidInventory("inventoryGuid");

        // Verify the results
        assertThat(result).isTrue();
    }
}
