package com.holderzone.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.erp.dao.GoodsSerialMapper;
import com.holderzone.erp.entity.domain.GoodsSerialDO;
import com.holderzone.erp.entity.domain.InventoryDO;
import com.holderzone.erp.entity.domain.RepertoryDO;
import com.holderzone.erp.mapperstruct.GoodsSerialMapstruct;
import com.holderzone.erp.service.DistributedIdService;
import com.holderzone.erp.service.GoodsService;
import com.holderzone.erp.service.InRepertoryService;
import com.holderzone.erp.service.InventoryService;
import com.holderzone.erp.utils.PageAdapter;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.erpretail.InOutGoodsDTO;
import com.holderzone.saas.store.dto.erp.erpretail.SaleSkuSumDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.InsertGoodsSerialReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.QueryGoodsSerialReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.GoodsSerialRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GoodsSerialServiceImplTest {

    @Mock
    private GoodsSerialMapper mockGoodsSerialMapper;
    @Mock
    private DistributedIdService mockDistributedIdService;
    @Mock
    private GoodsSerialMapstruct mockGoodsSerialMapstruct;
    @Mock
    private GoodsService mockGoodsService;
    @Mock
    private InRepertoryService mockInRepertoryService;
    @Mock
    private InventoryService mockInventoryService;

    @InjectMocks
    private GoodsSerialServiceImpl goodsSerialServiceImplUnderTest;

    @Before
    public void setUp() {
        goodsSerialServiceImplUnderTest.inRepertoryService = mockInRepertoryService;
    }

    @Test
    public void testQueryGoodsSerial() {
        // Setup
        final QueryGoodsSerialReqDTO queryGoodsSerialReqDTO = new QueryGoodsSerialReqDTO();
        queryGoodsSerialReqDTO.setStartDate("startDate");
        queryGoodsSerialReqDTO.setEndDate("endDate");
        queryGoodsSerialReqDTO.setInvoiceType(0);
        queryGoodsSerialReqDTO.setStartDateTime("startDateTime");
        queryGoodsSerialReqDTO.setEndDateTime("endDateTime");

        // Configure GoodsSerialMapper.queryGoodsSerial(...).
        final QueryGoodsSerialReqDTO queryGoodsSerialReqDTO1 = new QueryGoodsSerialReqDTO();
        queryGoodsSerialReqDTO1.setStartDate("startDate");
        queryGoodsSerialReqDTO1.setEndDate("endDate");
        queryGoodsSerialReqDTO1.setInvoiceType(0);
        queryGoodsSerialReqDTO1.setStartDateTime("startDateTime");
        queryGoodsSerialReqDTO1.setEndDateTime("endDateTime");
        when(mockGoodsSerialMapper.queryGoodsSerial(any(PageAdapter.class), eq(queryGoodsSerialReqDTO1)))
                .thenReturn(null);

        // Configure GoodsSerialMapstruct.fromGoodsSerialDO(...).
        final GoodsSerialRespDTO goodsSerialRespDTO = new GoodsSerialRespDTO();
        goodsSerialRespDTO.setInvoiceName("des");
        goodsSerialRespDTO.setInvoiceType(0);
        goodsSerialRespDTO.setInvoiceTime("invoiceTime");
        goodsSerialRespDTO.setSafeNum(new BigDecimal("0.00"));
        goodsSerialRespDTO.setInvalid(false);
        final GoodsSerialDO goodsSerialDO = new GoodsSerialDO();
        goodsSerialDO.setGuid("7da744f0-aca5-4379-a57c-561d36ea653e");
        goodsSerialDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        goodsSerialDO.setGoodsGuid("goodsGuid");
        goodsSerialDO.setInvoiceType(0);
        goodsSerialDO.setInvoiceNo("invoiceNo");
        when(mockGoodsSerialMapstruct.fromGoodsSerialDO(goodsSerialDO)).thenReturn(goodsSerialRespDTO);

        // Configure GoodsService.queryGoodsInfo(...).
        final InOutGoodsDTO inOutGoodsDTO = new InOutGoodsDTO();
        inOutGoodsDTO.setGoodsGuid("goodsGuid");
        inOutGoodsDTO.setGoodsCode("goodsCode");
        inOutGoodsDTO.setGoodsName("goodsName");
        inOutGoodsDTO.setBarCode("barCode");
        inOutGoodsDTO.setSafeNum(new BigDecimal("0.00"));
        when(mockGoodsService.queryGoodsInfo("goodsGuid")).thenReturn(inOutGoodsDTO);

        // Configure InRepertoryService.getOne(...).
        final RepertoryDO repertoryDO = new RepertoryDO();
        repertoryDO.setId(0L);
        repertoryDO.setGuid("95d3a255-8be6-49ca-9d35-54f21eb7b6d7");
        repertoryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        repertoryDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        repertoryDO.setStatus("status");
        when(mockInRepertoryService.getOne(any(LambdaQueryWrapper.class))).thenReturn(repertoryDO);

        // Configure InventoryService.getOne(...).
        final InventoryDO inventoryDO = new InventoryDO();
        inventoryDO.setId(0L);
        inventoryDO.setGuid("8dabcb5f-64b6-47fe-a7ad-7137e772b768");
        inventoryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        inventoryDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        inventoryDO.setStatus(0);
        when(mockInventoryService.getOne(any(LambdaQueryWrapper.class))).thenReturn(inventoryDO);

        // Run the test
        final Page<GoodsSerialRespDTO> result = goodsSerialServiceImplUnderTest.queryGoodsSerial(
                queryGoodsSerialReqDTO);

        // Verify the results
    }

    @Test
    public void testInsertGoodsSerial() {
        // Setup
        final InsertGoodsSerialReqDTO insertGoodsSerialReqDTO = new InsertGoodsSerialReqDTO();
        insertGoodsSerialReqDTO.setGoodsGuid("goodsGuid");
        insertGoodsSerialReqDTO.setInvoiceType(0);
        insertGoodsSerialReqDTO.setChangeNum(new BigDecimal("0.00"));
        insertGoodsSerialReqDTO.setUnitName("unitName");
        insertGoodsSerialReqDTO.setInvoiceNo("invoiceNo");
        final List<InsertGoodsSerialReqDTO> insertGoodsSerialReqDTOList = Arrays.asList(insertGoodsSerialReqDTO);
        when(mockDistributedIdService.nextBatchGoodsSerialGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure GoodsSerialMapstruct.fromInsertGoodsSerialDTO(...).
        final GoodsSerialDO goodsSerialDO = new GoodsSerialDO();
        goodsSerialDO.setGuid("7da744f0-aca5-4379-a57c-561d36ea653e");
        goodsSerialDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        goodsSerialDO.setGoodsGuid("goodsGuid");
        goodsSerialDO.setInvoiceType(0);
        goodsSerialDO.setInvoiceNo("invoiceNo");
        final InsertGoodsSerialReqDTO insertGoodsSerialReqDTO1 = new InsertGoodsSerialReqDTO();
        insertGoodsSerialReqDTO1.setGoodsGuid("goodsGuid");
        insertGoodsSerialReqDTO1.setInvoiceType(0);
        insertGoodsSerialReqDTO1.setChangeNum(new BigDecimal("0.00"));
        insertGoodsSerialReqDTO1.setUnitName("unitName");
        insertGoodsSerialReqDTO1.setInvoiceNo("invoiceNo");
        when(mockGoodsSerialMapstruct.fromInsertGoodsSerialDTO(insertGoodsSerialReqDTO1)).thenReturn(goodsSerialDO);

        // Run the test
        final boolean result = goodsSerialServiceImplUnderTest.insertGoodsSerial(insertGoodsSerialReqDTOList);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testInsertGoodsSerial_DistributedIdServiceReturnsNoItems() {
        // Setup
        final InsertGoodsSerialReqDTO insertGoodsSerialReqDTO = new InsertGoodsSerialReqDTO();
        insertGoodsSerialReqDTO.setGoodsGuid("goodsGuid");
        insertGoodsSerialReqDTO.setInvoiceType(0);
        insertGoodsSerialReqDTO.setChangeNum(new BigDecimal("0.00"));
        insertGoodsSerialReqDTO.setUnitName("unitName");
        insertGoodsSerialReqDTO.setInvoiceNo("invoiceNo");
        final List<InsertGoodsSerialReqDTO> insertGoodsSerialReqDTOList = Arrays.asList(insertGoodsSerialReqDTO);
        when(mockDistributedIdService.nextBatchGoodsSerialGuid(0L)).thenReturn(Collections.emptyList());

        // Configure GoodsSerialMapstruct.fromInsertGoodsSerialDTO(...).
        final GoodsSerialDO goodsSerialDO = new GoodsSerialDO();
        goodsSerialDO.setGuid("7da744f0-aca5-4379-a57c-561d36ea653e");
        goodsSerialDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        goodsSerialDO.setGoodsGuid("goodsGuid");
        goodsSerialDO.setInvoiceType(0);
        goodsSerialDO.setInvoiceNo("invoiceNo");
        final InsertGoodsSerialReqDTO insertGoodsSerialReqDTO1 = new InsertGoodsSerialReqDTO();
        insertGoodsSerialReqDTO1.setGoodsGuid("goodsGuid");
        insertGoodsSerialReqDTO1.setInvoiceType(0);
        insertGoodsSerialReqDTO1.setChangeNum(new BigDecimal("0.00"));
        insertGoodsSerialReqDTO1.setUnitName("unitName");
        insertGoodsSerialReqDTO1.setInvoiceNo("invoiceNo");
        when(mockGoodsSerialMapstruct.fromInsertGoodsSerialDTO(insertGoodsSerialReqDTO1)).thenReturn(goodsSerialDO);

        // Run the test
        final boolean result = goodsSerialServiceImplUnderTest.insertGoodsSerial(insertGoodsSerialReqDTOList);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testQueryGoodsSaleSkuSum() {
        // Setup
        final SaleSkuSumDTO expectedResult = new SaleSkuSumDTO();
        expectedResult.setSevenDaySkuSum(1);
        expectedResult.setThirtyDaySkuSum(1);

        // Run the test
        final SaleSkuSumDTO result = goodsSerialServiceImplUnderTest.queryGoodsSaleSkuSum();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
