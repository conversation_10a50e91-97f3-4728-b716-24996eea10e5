package com.holderzone.erp.service.impl;

import com.holderzone.erp.dao.MaterialCategoryDOMapper;
import com.holderzone.erp.dao.MaterialDOMapper;
import com.holderzone.erp.entity.domain.*;
import com.holderzone.erp.mapperstruct.ErpModuleMapper;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.*;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MaterialCategoryServiceImplTest {

    @Mock
    private MaterialCategoryDOMapper mockCategoryDOMapper;
    @Mock
    private ErpModuleMapper mockModuleMapper;
    @Mock
    private MaterialDOMapper mockMaterialDOMapper;

    private MaterialCategoryServiceImpl materialCategoryServiceImplUnderTest;

    @Before
    public void setUp() {
        materialCategoryServiceImplUnderTest = new MaterialCategoryServiceImpl();
        materialCategoryServiceImplUnderTest.categoryDOMapper = mockCategoryDOMapper;
        materialCategoryServiceImplUnderTest.moduleMapper = mockModuleMapper;
        materialCategoryServiceImplUnderTest.materialDOMapper = mockMaterialDOMapper;
    }

    @Test
    public void testAdd() {
        // Setup
        final MaterialCategoryDTO materialCategoryDTO = new MaterialCategoryDTO();
        materialCategoryDTO.setStoreGuid("storeGuid");
        materialCategoryDTO.setGuid("4152e9ef-437d-4c14-8cd4-69515f699237");
        materialCategoryDTO.setWarehouseGuid("warehouseGuid");
        materialCategoryDTO.setName("默认分类");

        // Configure ErpModuleMapper.mapToMaterialCategoryDO(...).
        final MaterialCategoryDO materialCategoryDO = new MaterialCategoryDO();
        materialCategoryDO.setGuid("5c6c55af-8aee-4994-a9af-64eacb715274");
        materialCategoryDO.setEnterpriseGuid("enterpriseGuid");
        materialCategoryDO.setStoreGuid("storeGuid");
        materialCategoryDO.setWarehouseGuid("warehouseGuid");
        materialCategoryDO.setDeleted(false);
        final MaterialCategoryDTO materialCategoryDTO1 = new MaterialCategoryDTO();
        materialCategoryDTO1.setStoreGuid("storeGuid");
        materialCategoryDTO1.setGuid("4152e9ef-437d-4c14-8cd4-69515f699237");
        materialCategoryDTO1.setWarehouseGuid("warehouseGuid");
        materialCategoryDTO1.setName("默认分类");
        when(mockModuleMapper.mapToMaterialCategoryDO(materialCategoryDTO1)).thenReturn(materialCategoryDO);

        // Run the test
        final boolean result = materialCategoryServiceImplUnderTest.add(materialCategoryDTO);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockCategoryDOMapper).insertSelective(any(MaterialCategoryDO.class));
    }

    @Test
    public void testUpdate() {
        // Setup
        final MaterialCategoryDTO materialCategoryDTO = new MaterialCategoryDTO();
        materialCategoryDTO.setStoreGuid("storeGuid");
        materialCategoryDTO.setGuid("4152e9ef-437d-4c14-8cd4-69515f699237");
        materialCategoryDTO.setWarehouseGuid("warehouseGuid");
        materialCategoryDTO.setName("默认分类");

        // Configure ErpModuleMapper.mapToMaterialCategoryDO(...).
        final MaterialCategoryDO materialCategoryDO = new MaterialCategoryDO();
        materialCategoryDO.setGuid("5c6c55af-8aee-4994-a9af-64eacb715274");
        materialCategoryDO.setEnterpriseGuid("enterpriseGuid");
        materialCategoryDO.setStoreGuid("storeGuid");
        materialCategoryDO.setWarehouseGuid("warehouseGuid");
        materialCategoryDO.setDeleted(false);
        final MaterialCategoryDTO materialCategoryDTO1 = new MaterialCategoryDTO();
        materialCategoryDTO1.setStoreGuid("storeGuid");
        materialCategoryDTO1.setGuid("4152e9ef-437d-4c14-8cd4-69515f699237");
        materialCategoryDTO1.setWarehouseGuid("warehouseGuid");
        materialCategoryDTO1.setName("默认分类");
        when(mockModuleMapper.mapToMaterialCategoryDO(materialCategoryDTO1)).thenReturn(materialCategoryDO);

        // Run the test
        final boolean result = materialCategoryServiceImplUnderTest.update(materialCategoryDTO);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockCategoryDOMapper).updateByExampleSelective(any(MaterialCategoryDO.class),
                any(MaterialCategoryDOExample.class));
    }

    @Test
    public void testFindByGuid() {
        // Setup
        final MaterialCategoryDTO expectedResult = new MaterialCategoryDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setGuid("4152e9ef-437d-4c14-8cd4-69515f699237");
        expectedResult.setWarehouseGuid("warehouseGuid");
        expectedResult.setName("默认分类");

        // Configure MaterialCategoryDOMapper.selectByExample(...).
        final MaterialCategoryDO materialCategoryDO = new MaterialCategoryDO();
        materialCategoryDO.setGuid("5c6c55af-8aee-4994-a9af-64eacb715274");
        materialCategoryDO.setEnterpriseGuid("enterpriseGuid");
        materialCategoryDO.setStoreGuid("storeGuid");
        materialCategoryDO.setWarehouseGuid("warehouseGuid");
        materialCategoryDO.setDeleted(false);
        final List<MaterialCategoryDO> materialCategoryDOS = Arrays.asList(materialCategoryDO);
        when(mockCategoryDOMapper.selectByExample(any(MaterialCategoryDOExample.class)))
                .thenReturn(materialCategoryDOS);

        // Configure ErpModuleMapper.mapToMaterialCategoryDTO(...).
        final MaterialCategoryDTO materialCategoryDTO = new MaterialCategoryDTO();
        materialCategoryDTO.setStoreGuid("storeGuid");
        materialCategoryDTO.setGuid("4152e9ef-437d-4c14-8cd4-69515f699237");
        materialCategoryDTO.setWarehouseGuid("warehouseGuid");
        materialCategoryDTO.setName("默认分类");
        when(mockModuleMapper.mapToMaterialCategoryDTO(any(MaterialCategoryDO.class))).thenReturn(materialCategoryDTO);

        // Run the test
        final MaterialCategoryDTO result = materialCategoryServiceImplUnderTest.findByGuid(
                "2a18d0dc-a53e-4524-9ef5-7e32a4464356");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindByGuid_MaterialCategoryDOMapperReturnsNoItems() {
        // Setup
        when(mockCategoryDOMapper.selectByExample(any(MaterialCategoryDOExample.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MaterialCategoryDTO result = materialCategoryServiceImplUnderTest.findByGuid(
                "2a18d0dc-a53e-4524-9ef5-7e32a4464356");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testFindByCondition() {
        // Setup
        final MaterialCategoryQueryDTO queryDTO = new MaterialCategoryQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setWarehouseGuid("warehouseGuid");
        queryDTO.setSearchConditions("searchConditions");
        queryDTO.setStoreGuid("storeGuid");

        final Page page = new Page<>(0L, 0L, Arrays.asList());
        final MaterialCategoryDTO materialCategoryDTO = new MaterialCategoryDTO();
        materialCategoryDTO.setStoreGuid("storeGuid");
        materialCategoryDTO.setGuid("4152e9ef-437d-4c14-8cd4-69515f699237");
        materialCategoryDTO.setWarehouseGuid("warehouseGuid");
        materialCategoryDTO.setName("默认分类");
        final List<MaterialCategoryDTO> expectedResult = Arrays.asList(materialCategoryDTO);

        // Configure MaterialCategoryDOMapper.selectByExample(...).
        final MaterialCategoryDO materialCategoryDO = new MaterialCategoryDO();
        materialCategoryDO.setGuid("5c6c55af-8aee-4994-a9af-64eacb715274");
        materialCategoryDO.setEnterpriseGuid("enterpriseGuid");
        materialCategoryDO.setStoreGuid("storeGuid");
        materialCategoryDO.setWarehouseGuid("warehouseGuid");
        materialCategoryDO.setDeleted(false);
        final List<MaterialCategoryDO> materialCategoryDOS = Arrays.asList(materialCategoryDO);
        when(mockCategoryDOMapper.selectByExample(any(MaterialCategoryDOExample.class)))
                .thenReturn(materialCategoryDOS);

        // Configure ErpModuleMapper.mapToMaterialCategoryDTOList(...).
        final MaterialCategoryDTO materialCategoryDTO1 = new MaterialCategoryDTO();
        materialCategoryDTO1.setStoreGuid("storeGuid");
        materialCategoryDTO1.setGuid("4152e9ef-437d-4c14-8cd4-69515f699237");
        materialCategoryDTO1.setWarehouseGuid("warehouseGuid");
        materialCategoryDTO1.setName("默认分类");
        final List<MaterialCategoryDTO> materialCategoryDTOS = Arrays.asList(materialCategoryDTO1);
        final MaterialCategoryDO materialCategoryDO1 = new MaterialCategoryDO();
        materialCategoryDO1.setGuid("5c6c55af-8aee-4994-a9af-64eacb715274");
        materialCategoryDO1.setEnterpriseGuid("enterpriseGuid");
        materialCategoryDO1.setStoreGuid("storeGuid");
        materialCategoryDO1.setWarehouseGuid("warehouseGuid");
        materialCategoryDO1.setDeleted(false);
        final List<MaterialCategoryDO> materialCategoryDOList = Arrays.asList(materialCategoryDO1);
        when(mockModuleMapper.mapToMaterialCategoryDTOList(materialCategoryDOList)).thenReturn(materialCategoryDTOS);

        // Run the test
        final List<MaterialCategoryDTO> result = materialCategoryServiceImplUnderTest.findByCondition(queryDTO, page);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindByCondition_MaterialCategoryDOMapperReturnsNoItems() {
        // Setup
        final MaterialCategoryQueryDTO queryDTO = new MaterialCategoryQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setWarehouseGuid("warehouseGuid");
        queryDTO.setSearchConditions("searchConditions");
        queryDTO.setStoreGuid("storeGuid");

        final Page page = new Page<>(0L, 0L, Arrays.asList());
        final MaterialCategoryDTO materialCategoryDTO = new MaterialCategoryDTO();
        materialCategoryDTO.setStoreGuid("storeGuid");
        materialCategoryDTO.setGuid("4152e9ef-437d-4c14-8cd4-69515f699237");
        materialCategoryDTO.setWarehouseGuid("warehouseGuid");
        materialCategoryDTO.setName("默认分类");
        final List<MaterialCategoryDTO> expectedResult = Arrays.asList(materialCategoryDTO);
        when(mockCategoryDOMapper.selectByExample(any(MaterialCategoryDOExample.class)))
                .thenReturn(Collections.emptyList());

        // Configure ErpModuleMapper.mapToMaterialCategoryDTOList(...).
        final MaterialCategoryDTO materialCategoryDTO1 = new MaterialCategoryDTO();
        materialCategoryDTO1.setStoreGuid("storeGuid");
        materialCategoryDTO1.setGuid("4152e9ef-437d-4c14-8cd4-69515f699237");
        materialCategoryDTO1.setWarehouseGuid("warehouseGuid");
        materialCategoryDTO1.setName("默认分类");
        final List<MaterialCategoryDTO> materialCategoryDTOS = Arrays.asList(materialCategoryDTO1);
        final MaterialCategoryDO materialCategoryDO = new MaterialCategoryDO();
        materialCategoryDO.setGuid("5c6c55af-8aee-4994-a9af-64eacb715274");
        materialCategoryDO.setEnterpriseGuid("enterpriseGuid");
        materialCategoryDO.setStoreGuid("storeGuid");
        materialCategoryDO.setWarehouseGuid("warehouseGuid");
        materialCategoryDO.setDeleted(false);
        final List<MaterialCategoryDO> materialCategoryDOList = Arrays.asList(materialCategoryDO);
        when(mockModuleMapper.mapToMaterialCategoryDTOList(materialCategoryDOList)).thenReturn(materialCategoryDTOS);

        // Run the test
        final List<MaterialCategoryDTO> result = materialCategoryServiceImplUnderTest.findByCondition(queryDTO, page);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindByCondition_ErpModuleMapperReturnsNoItems() {
        // Setup
        final MaterialCategoryQueryDTO queryDTO = new MaterialCategoryQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setWarehouseGuid("warehouseGuid");
        queryDTO.setSearchConditions("searchConditions");
        queryDTO.setStoreGuid("storeGuid");

        final Page page = new Page<>(0L, 0L, Arrays.asList());

        // Configure MaterialCategoryDOMapper.selectByExample(...).
        final MaterialCategoryDO materialCategoryDO = new MaterialCategoryDO();
        materialCategoryDO.setGuid("5c6c55af-8aee-4994-a9af-64eacb715274");
        materialCategoryDO.setEnterpriseGuid("enterpriseGuid");
        materialCategoryDO.setStoreGuid("storeGuid");
        materialCategoryDO.setWarehouseGuid("warehouseGuid");
        materialCategoryDO.setDeleted(false);
        final List<MaterialCategoryDO> materialCategoryDOS = Arrays.asList(materialCategoryDO);
        when(mockCategoryDOMapper.selectByExample(any(MaterialCategoryDOExample.class)))
                .thenReturn(materialCategoryDOS);

        // Configure ErpModuleMapper.mapToMaterialCategoryDTOList(...).
        final MaterialCategoryDO materialCategoryDO1 = new MaterialCategoryDO();
        materialCategoryDO1.setGuid("5c6c55af-8aee-4994-a9af-64eacb715274");
        materialCategoryDO1.setEnterpriseGuid("enterpriseGuid");
        materialCategoryDO1.setStoreGuid("storeGuid");
        materialCategoryDO1.setWarehouseGuid("warehouseGuid");
        materialCategoryDO1.setDeleted(false);
        final List<MaterialCategoryDO> materialCategoryDOList = Arrays.asList(materialCategoryDO1);
        when(mockModuleMapper.mapToMaterialCategoryDTOList(materialCategoryDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<MaterialCategoryDTO> result = materialCategoryServiceImplUnderTest.findByCondition(queryDTO, page);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testCountByCode() {
        // Setup
        final MaterialCategoryDTO materialCategoryDTO = new MaterialCategoryDTO();
        materialCategoryDTO.setStoreGuid("storeGuid");
        materialCategoryDTO.setGuid("4152e9ef-437d-4c14-8cd4-69515f699237");
        materialCategoryDTO.setWarehouseGuid("warehouseGuid");
        materialCategoryDTO.setName("默认分类");

        when(mockCategoryDOMapper.countByExample(any(MaterialCategoryDOExample.class))).thenReturn(0L);

        // Run the test
        final long result = materialCategoryServiceImplUnderTest.countByCode(materialCategoryDTO);

        // Verify the results
        assertThat(result).isEqualTo(0L);
    }

    @Test
    public void testFindList() {
        // Setup
        final CategoryListQueryDTO categoryListQueryDTO = new CategoryListQueryDTO();
        categoryListQueryDTO.setWarehouseGuid("warehouseGuid");
        categoryListQueryDTO.setSearchConditions("searchConditions");
        categoryListQueryDTO.setStoreGuid("storeGuid");

        final MaterialCategoryDTO materialCategoryDTO = new MaterialCategoryDTO();
        materialCategoryDTO.setStoreGuid("storeGuid");
        materialCategoryDTO.setGuid("4152e9ef-437d-4c14-8cd4-69515f699237");
        materialCategoryDTO.setWarehouseGuid("warehouseGuid");
        materialCategoryDTO.setName("默认分类");
        final List<MaterialCategoryDTO> expectedResult = Arrays.asList(materialCategoryDTO);

        // Configure MaterialCategoryDOMapper.selectByExample(...).
        final MaterialCategoryDO materialCategoryDO = new MaterialCategoryDO();
        materialCategoryDO.setGuid("5c6c55af-8aee-4994-a9af-64eacb715274");
        materialCategoryDO.setEnterpriseGuid("enterpriseGuid");
        materialCategoryDO.setStoreGuid("storeGuid");
        materialCategoryDO.setWarehouseGuid("warehouseGuid");
        materialCategoryDO.setDeleted(false);
        final List<MaterialCategoryDO> materialCategoryDOS = Arrays.asList(materialCategoryDO);
        when(mockCategoryDOMapper.selectByExample(any(MaterialCategoryDOExample.class)))
                .thenReturn(materialCategoryDOS);

        // Configure ErpModuleMapper.mapToMaterialCategoryDTOList(...).
        final MaterialCategoryDTO materialCategoryDTO1 = new MaterialCategoryDTO();
        materialCategoryDTO1.setStoreGuid("storeGuid");
        materialCategoryDTO1.setGuid("4152e9ef-437d-4c14-8cd4-69515f699237");
        materialCategoryDTO1.setWarehouseGuid("warehouseGuid");
        materialCategoryDTO1.setName("默认分类");
        final List<MaterialCategoryDTO> materialCategoryDTOS = Arrays.asList(materialCategoryDTO1);
        final MaterialCategoryDO materialCategoryDO1 = new MaterialCategoryDO();
        materialCategoryDO1.setGuid("5c6c55af-8aee-4994-a9af-64eacb715274");
        materialCategoryDO1.setEnterpriseGuid("enterpriseGuid");
        materialCategoryDO1.setStoreGuid("storeGuid");
        materialCategoryDO1.setWarehouseGuid("warehouseGuid");
        materialCategoryDO1.setDeleted(false);
        final List<MaterialCategoryDO> materialCategoryDOList = Arrays.asList(materialCategoryDO1);
        when(mockModuleMapper.mapToMaterialCategoryDTOList(materialCategoryDOList)).thenReturn(materialCategoryDTOS);

        // Run the test
        final List<MaterialCategoryDTO> result = materialCategoryServiceImplUnderTest.findList(categoryListQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindList_MaterialCategoryDOMapperReturnsNoItems() {
        // Setup
        final CategoryListQueryDTO categoryListQueryDTO = new CategoryListQueryDTO();
        categoryListQueryDTO.setWarehouseGuid("warehouseGuid");
        categoryListQueryDTO.setSearchConditions("searchConditions");
        categoryListQueryDTO.setStoreGuid("storeGuid");

        when(mockCategoryDOMapper.selectByExample(any(MaterialCategoryDOExample.class)))
                .thenReturn(Collections.emptyList());

        // Configure ErpModuleMapper.mapToMaterialCategoryDTOList(...).
        final MaterialCategoryDTO materialCategoryDTO = new MaterialCategoryDTO();
        materialCategoryDTO.setStoreGuid("storeGuid");
        materialCategoryDTO.setGuid("4152e9ef-437d-4c14-8cd4-69515f699237");
        materialCategoryDTO.setWarehouseGuid("warehouseGuid");
        materialCategoryDTO.setName("默认分类");
        final List<MaterialCategoryDTO> materialCategoryDTOS = Arrays.asList(materialCategoryDTO);
        final MaterialCategoryDO materialCategoryDO = new MaterialCategoryDO();
        materialCategoryDO.setGuid("5c6c55af-8aee-4994-a9af-64eacb715274");
        materialCategoryDO.setEnterpriseGuid("enterpriseGuid");
        materialCategoryDO.setStoreGuid("storeGuid");
        materialCategoryDO.setWarehouseGuid("warehouseGuid");
        materialCategoryDO.setDeleted(false);
        final List<MaterialCategoryDO> materialCategoryDOList = Arrays.asList(materialCategoryDO);
        when(mockModuleMapper.mapToMaterialCategoryDTOList(materialCategoryDOList)).thenReturn(materialCategoryDTOS);

        // Run the test
        final List<MaterialCategoryDTO> result = materialCategoryServiceImplUnderTest.findList(categoryListQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testFindList_ErpModuleMapperReturnsNoItems() {
        // Setup
        final CategoryListQueryDTO categoryListQueryDTO = new CategoryListQueryDTO();
        categoryListQueryDTO.setWarehouseGuid("warehouseGuid");
        categoryListQueryDTO.setSearchConditions("searchConditions");
        categoryListQueryDTO.setStoreGuid("storeGuid");

        // Configure MaterialCategoryDOMapper.selectByExample(...).
        final MaterialCategoryDO materialCategoryDO = new MaterialCategoryDO();
        materialCategoryDO.setGuid("5c6c55af-8aee-4994-a9af-64eacb715274");
        materialCategoryDO.setEnterpriseGuid("enterpriseGuid");
        materialCategoryDO.setStoreGuid("storeGuid");
        materialCategoryDO.setWarehouseGuid("warehouseGuid");
        materialCategoryDO.setDeleted(false);
        final List<MaterialCategoryDO> materialCategoryDOS = Arrays.asList(materialCategoryDO);
        when(mockCategoryDOMapper.selectByExample(any(MaterialCategoryDOExample.class)))
                .thenReturn(materialCategoryDOS);

        // Configure ErpModuleMapper.mapToMaterialCategoryDTOList(...).
        final MaterialCategoryDO materialCategoryDO1 = new MaterialCategoryDO();
        materialCategoryDO1.setGuid("5c6c55af-8aee-4994-a9af-64eacb715274");
        materialCategoryDO1.setEnterpriseGuid("enterpriseGuid");
        materialCategoryDO1.setStoreGuid("storeGuid");
        materialCategoryDO1.setWarehouseGuid("warehouseGuid");
        materialCategoryDO1.setDeleted(false);
        final List<MaterialCategoryDO> materialCategoryDOList = Arrays.asList(materialCategoryDO1);
        when(mockModuleMapper.mapToMaterialCategoryDTOList(materialCategoryDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<MaterialCategoryDTO> result = materialCategoryServiceImplUnderTest.findList(categoryListQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testCountMaterialByCategory() {
        // Setup
        when(mockMaterialDOMapper.countByExample(any(MaterialDOExample.class))).thenReturn(0L);

        // Run the test
        final long result = materialCategoryServiceImplUnderTest.countMaterialByCategory(
                "07fa3217-8a29-4994-98ca-4d9e4e5590dc");

        // Verify the results
        assertThat(result).isEqualTo(0L);
    }

    @Test
    public void testFindCategoryAndMaterial() {
        // Setup
        // Configure MaterialCategoryDOMapper.getMaterialCategoryListAll(...).
        final CategoryDO categoryDO = new CategoryDO();
        categoryDO.setCategoryGuid("categoryGuid");
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setCategory("category");
        materialDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        categoryDO.setMaterialDOList(Arrays.asList(materialDO));
        categoryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<CategoryDO> categoryDOS = Arrays.asList(categoryDO);
        when(mockCategoryDOMapper.getMaterialCategoryListAll("storeGuid", "searchName")).thenReturn(categoryDOS);

        // Configure ErpModuleMapper.mapToCategoryDTO(...).
        final CategoryDTO categoryDTO = new CategoryDTO();
        categoryDTO.setCategoryGuid("categoryGuid");
        categoryDTO.setCategoryName("categoryName");
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("cec325d4-03bf-452f-b9e7-e094642e466f");
        categoryDTO.setMaterialDTOList(Arrays.asList(materialDTO));
        final List<CategoryDTO> categoryDTOS = Arrays.asList(categoryDTO);
        final CategoryDO categoryDO1 = new CategoryDO();
        categoryDO1.setCategoryGuid("categoryGuid");
        final MaterialDO materialDO1 = new MaterialDO();
        materialDO1.setCategory("category");
        materialDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        categoryDO1.setMaterialDOList(Arrays.asList(materialDO1));
        categoryDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<CategoryDO> categoryDOList = Arrays.asList(categoryDO1);
        when(mockModuleMapper.mapToCategoryDTO(categoryDOList)).thenReturn(categoryDTOS);

        // Run the test
        final List<CategoryDTO> result = materialCategoryServiceImplUnderTest.findCategoryAndMaterial("storeGuid",
                "searchName");

        // Verify the results
    }

    @Test
    public void testFindCategoryAndMaterial_MaterialCategoryDOMapperReturnsNoItems() {
        // Setup
        when(mockCategoryDOMapper.getMaterialCategoryListAll("storeGuid", "searchName"))
                .thenReturn(Collections.emptyList());

        // Configure ErpModuleMapper.mapToCategoryDTO(...).
        final CategoryDTO categoryDTO = new CategoryDTO();
        categoryDTO.setCategoryGuid("categoryGuid");
        categoryDTO.setCategoryName("categoryName");
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("cec325d4-03bf-452f-b9e7-e094642e466f");
        categoryDTO.setMaterialDTOList(Arrays.asList(materialDTO));
        final List<CategoryDTO> categoryDTOS = Arrays.asList(categoryDTO);
        final CategoryDO categoryDO = new CategoryDO();
        categoryDO.setCategoryGuid("categoryGuid");
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setCategory("category");
        materialDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        categoryDO.setMaterialDOList(Arrays.asList(materialDO));
        categoryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<CategoryDO> categoryDOList = Arrays.asList(categoryDO);
        when(mockModuleMapper.mapToCategoryDTO(categoryDOList)).thenReturn(categoryDTOS);

        // Run the test
        final List<CategoryDTO> result = materialCategoryServiceImplUnderTest.findCategoryAndMaterial("storeGuid",
                "searchName");

        // Verify the results
    }

    @Test
    public void testFindCategoryAndMaterial_ErpModuleMapperReturnsNoItems() {
        // Setup
        // Configure MaterialCategoryDOMapper.getMaterialCategoryListAll(...).
        final CategoryDO categoryDO = new CategoryDO();
        categoryDO.setCategoryGuid("categoryGuid");
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setCategory("category");
        materialDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        categoryDO.setMaterialDOList(Arrays.asList(materialDO));
        categoryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<CategoryDO> categoryDOS = Arrays.asList(categoryDO);
        when(mockCategoryDOMapper.getMaterialCategoryListAll("storeGuid", "searchName")).thenReturn(categoryDOS);

        // Configure ErpModuleMapper.mapToCategoryDTO(...).
        final CategoryDO categoryDO1 = new CategoryDO();
        categoryDO1.setCategoryGuid("categoryGuid");
        final MaterialDO materialDO1 = new MaterialDO();
        materialDO1.setCategory("category");
        materialDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        categoryDO1.setMaterialDOList(Arrays.asList(materialDO1));
        categoryDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<CategoryDO> categoryDOList = Arrays.asList(categoryDO1);
        when(mockModuleMapper.mapToCategoryDTO(categoryDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<CategoryDTO> result = materialCategoryServiceImplUnderTest.findCategoryAndMaterial("storeGuid",
                "searchName");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testDelete() {
        // Setup
        when(mockCategoryDOMapper.updateByExampleSelective(any(MaterialCategoryDO.class),
                any(MaterialCategoryDOExample.class))).thenReturn(0);

        // Run the test
        final boolean result = materialCategoryServiceImplUnderTest.delete("b921e0d8-**************-f0ecebc7a752");

        // Verify the results
        assertThat(result).isFalse();
        verify(mockMaterialDOMapper).updateByExampleSelective(any(MaterialDO.class), any(MaterialDOExample.class));
    }

    @Test
    public void testListByGuidList() {
        // Setup
        final SingleListDTO dto = new SingleListDTO();
        dto.setStoreGuid("storeGuid");
        dto.setList(Arrays.asList("value"));

        // Configure MaterialCategoryDOMapper.listByGuidList(...).
        final CategoryDTO categoryDTO = new CategoryDTO();
        categoryDTO.setCategoryGuid("categoryGuid");
        categoryDTO.setCategoryName("categoryName");
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("cec325d4-03bf-452f-b9e7-e094642e466f");
        categoryDTO.setMaterialDTOList(Arrays.asList(materialDTO));
        final List<CategoryDTO> categoryDTOS = Arrays.asList(categoryDTO);
        final SingleListDTO dto1 = new SingleListDTO();
        dto1.setStoreGuid("storeGuid");
        dto1.setList(Arrays.asList("value"));
        when(mockCategoryDOMapper.listByGuidList(dto1)).thenReturn(categoryDTOS);

        // Run the test
        final List<CategoryDTO> result = materialCategoryServiceImplUnderTest.listByGuidList(dto);

        // Verify the results
    }

    @Test
    public void testListByGuidList_MaterialCategoryDOMapperReturnsNoItems() {
        // Setup
        final SingleListDTO dto = new SingleListDTO();
        dto.setStoreGuid("storeGuid");
        dto.setList(Arrays.asList("value"));

        // Configure MaterialCategoryDOMapper.listByGuidList(...).
        final SingleListDTO dto1 = new SingleListDTO();
        dto1.setStoreGuid("storeGuid");
        dto1.setList(Arrays.asList("value"));
        when(mockCategoryDOMapper.listByGuidList(dto1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<CategoryDTO> result = materialCategoryServiceImplUnderTest.listByGuidList(dto);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
