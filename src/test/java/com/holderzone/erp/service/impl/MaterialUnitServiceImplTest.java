package com.holderzone.erp.service.impl;

import com.holderzone.erp.dao.MaterialUnitDOMapper;
import com.holderzone.erp.entity.domain.MaterialUnitDO;
import com.holderzone.erp.entity.domain.MaterialUnitDOExample;
import com.holderzone.erp.mapperstruct.ErpModuleMapper;
import com.holderzone.saas.store.dto.erp.MaterialUnitDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MaterialUnitServiceImplTest {

    @Mock
    private MaterialUnitDOMapper mockUnitDOMapper;
    @Mock
    private ErpModuleMapper mockModuleMapper;

    private MaterialUnitServiceImpl materialUnitServiceImplUnderTest;

    @Before
    public void setUp() {
        materialUnitServiceImplUnderTest = new MaterialUnitServiceImpl();
        materialUnitServiceImplUnderTest.unitDOMapper = mockUnitDOMapper;
        materialUnitServiceImplUnderTest.moduleMapper = mockModuleMapper;
    }

    @Test
    public void testAdd() {
        // Setup
        final MaterialUnitDTO materialUnitDTO = new MaterialUnitDTO();
        materialUnitDTO.setGuid("5b7131ea-bd5e-4d52-87cf-c8aa84a11637");
        materialUnitDTO.setName("name");
        materialUnitDTO.setCode("code");

        // Configure ErpModuleMapper.mapToUnitDO(...).
        final MaterialUnitDO materialUnitDO = new MaterialUnitDO();
        materialUnitDO.setGuid("97270cd6-7914-499c-b6d0-73cd40ad212e");
        materialUnitDO.setName("name");
        materialUnitDO.setCode("code");
        materialUnitDO.setGmtCreate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        materialUnitDO.setGmtModified(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockModuleMapper.mapToUnitDO(any(MaterialUnitDTO.class))).thenReturn(materialUnitDO);

        when(mockUnitDOMapper.insertSelective(any(MaterialUnitDO.class))).thenReturn(0);

        // Run the test
        final boolean result = materialUnitServiceImplUnderTest.add(materialUnitDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testList() {
        // Setup
        // Configure MaterialUnitDOMapper.selectByExample(...).
        final MaterialUnitDO materialUnitDO = new MaterialUnitDO();
        materialUnitDO.setGuid("97270cd6-7914-499c-b6d0-73cd40ad212e");
        materialUnitDO.setName("name");
        materialUnitDO.setCode("code");
        materialUnitDO.setGmtCreate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        materialUnitDO.setGmtModified(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<MaterialUnitDO> materialUnitDOS = Arrays.asList(materialUnitDO);
        when(mockUnitDOMapper.selectByExample(any(MaterialUnitDOExample.class))).thenReturn(materialUnitDOS);

        // Configure ErpModuleMapper.mapToUnitDTOList(...).
        final MaterialUnitDTO materialUnitDTO = new MaterialUnitDTO();
        materialUnitDTO.setGuid("5b7131ea-bd5e-4d52-87cf-c8aa84a11637");
        materialUnitDTO.setName("name");
        materialUnitDTO.setCode("code");
        final List<MaterialUnitDTO> materialUnitDTOS = Arrays.asList(materialUnitDTO);
        final MaterialUnitDO materialUnitDO1 = new MaterialUnitDO();
        materialUnitDO1.setGuid("97270cd6-7914-499c-b6d0-73cd40ad212e");
        materialUnitDO1.setName("name");
        materialUnitDO1.setCode("code");
        materialUnitDO1.setGmtCreate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        materialUnitDO1.setGmtModified(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<MaterialUnitDO> list = Arrays.asList(materialUnitDO1);
        when(mockModuleMapper.mapToUnitDTOList(list)).thenReturn(materialUnitDTOS);

        // Run the test
        final List<MaterialUnitDTO> result = materialUnitServiceImplUnderTest.list();

        // Verify the results
    }

    @Test
    public void testList_MaterialUnitDOMapperReturnsNoItems() {
        // Setup
        when(mockUnitDOMapper.selectByExample(any(MaterialUnitDOExample.class))).thenReturn(Collections.emptyList());

        // Configure ErpModuleMapper.mapToUnitDTOList(...).
        final MaterialUnitDTO materialUnitDTO = new MaterialUnitDTO();
        materialUnitDTO.setGuid("5b7131ea-bd5e-4d52-87cf-c8aa84a11637");
        materialUnitDTO.setName("name");
        materialUnitDTO.setCode("code");
        final List<MaterialUnitDTO> materialUnitDTOS = Arrays.asList(materialUnitDTO);
        final MaterialUnitDO materialUnitDO = new MaterialUnitDO();
        materialUnitDO.setGuid("97270cd6-7914-499c-b6d0-73cd40ad212e");
        materialUnitDO.setName("name");
        materialUnitDO.setCode("code");
        materialUnitDO.setGmtCreate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        materialUnitDO.setGmtModified(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<MaterialUnitDO> list = Arrays.asList(materialUnitDO);
        when(mockModuleMapper.mapToUnitDTOList(list)).thenReturn(materialUnitDTOS);

        // Run the test
        final List<MaterialUnitDTO> result = materialUnitServiceImplUnderTest.list();

        // Verify the results
    }

    @Test
    public void testList_ErpModuleMapperReturnsNoItems() {
        // Setup
        // Configure MaterialUnitDOMapper.selectByExample(...).
        final MaterialUnitDO materialUnitDO = new MaterialUnitDO();
        materialUnitDO.setGuid("97270cd6-7914-499c-b6d0-73cd40ad212e");
        materialUnitDO.setName("name");
        materialUnitDO.setCode("code");
        materialUnitDO.setGmtCreate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        materialUnitDO.setGmtModified(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<MaterialUnitDO> materialUnitDOS = Arrays.asList(materialUnitDO);
        when(mockUnitDOMapper.selectByExample(any(MaterialUnitDOExample.class))).thenReturn(materialUnitDOS);

        // Configure ErpModuleMapper.mapToUnitDTOList(...).
        final MaterialUnitDO materialUnitDO1 = new MaterialUnitDO();
        materialUnitDO1.setGuid("97270cd6-7914-499c-b6d0-73cd40ad212e");
        materialUnitDO1.setName("name");
        materialUnitDO1.setCode("code");
        materialUnitDO1.setGmtCreate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        materialUnitDO1.setGmtModified(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<MaterialUnitDO> list = Arrays.asList(materialUnitDO1);
        when(mockModuleMapper.mapToUnitDTOList(list)).thenReturn(Collections.emptyList());

        // Run the test
        final List<MaterialUnitDTO> result = materialUnitServiceImplUnderTest.list();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
