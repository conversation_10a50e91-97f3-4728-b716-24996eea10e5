package com.holderzone.erp.service.impl;

import com.holderzone.erp.dao.GoodsBomDOMapper;
import com.holderzone.erp.entity.bo.InOutDocumentBomBO;
import com.holderzone.erp.entity.bo.InOutDocumentBomQueryBO;
import com.holderzone.erp.entity.bo.UnitConvertBO;
import com.holderzone.erp.entity.domain.GoodsBomDO;
import com.holderzone.erp.entity.domain.GoodsBomDOExample;
import com.holderzone.erp.entity.domain.MaterialDO;
import com.holderzone.erp.mapperstruct.ErpModuleMapper;
import com.holderzone.erp.service.IMaterialService;
import com.holderzone.saas.store.dto.erp.GoodsBomConfigDTO;
import com.holderzone.saas.store.dto.erp.GoodsBomDTO;
import com.holderzone.saas.store.dto.erp.MaterialDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BomServiceImplTest {

    @Mock
    private GoodsBomDOMapper mockGoodsBomDOMapper;
    @Mock
    private ErpModuleMapper mockModuleMapper;
    @Mock
    private IMaterialService mockMaterialService;

    private BomServiceImpl bomServiceImplUnderTest;

    @Before
    public void setUp() {
        bomServiceImplUnderTest = new BomServiceImpl();
        bomServiceImplUnderTest.goodsBomDOMapper = mockGoodsBomDOMapper;
        bomServiceImplUnderTest.moduleMapper = mockModuleMapper;
        bomServiceImplUnderTest.materialService = mockMaterialService;
    }

    @Test
    public void testAdd() {
        // Setup
        final GoodsBomConfigDTO goodsBom = new GoodsBomConfigDTO();
        goodsBom.setGoodsGuid("goodsGuid");
        goodsBom.setGoodsSku("goodsSku");
        final GoodsBomDTO goodsBomDTO = new GoodsBomDTO();
        goodsBomDTO.setGoodsGuid("goodsGuid");
        goodsBomDTO.setMaterialGuid("materialGuid");
        goodsBom.setBomList(Arrays.asList(goodsBomDTO));

        // Configure ErpModuleMapper.mapToBomDOList(...).
        final GoodsBomDO goodsBomDO = new GoodsBomDO();
        goodsBomDO.setGuid("f5f83da2-dc2b-4ff7-b5ca-e9f87295b872");
        goodsBomDO.setGoodsGuid("goodsGuid");
        goodsBomDO.setGoodsSku("goodsSku");
        goodsBomDO.setMaterialGuid("materialGuid");
        goodsBomDO.setUsage(new BigDecimal("0.00"));
        goodsBomDO.setUnit("unitGuid");
        final List<GoodsBomDO> goodsBomDOS = Arrays.asList(goodsBomDO);
        final GoodsBomDTO goodsBomDTO1 = new GoodsBomDTO();
        goodsBomDTO1.setGoodsGuid("goodsGuid");
        goodsBomDTO1.setMaterialGuid("materialGuid");
        goodsBomDTO1.setUsage(new BigDecimal("0.00"));
        goodsBomDTO1.setUnit("unit");
        goodsBomDTO1.setUnitName("unitName");
        final List<GoodsBomDTO> list = Arrays.asList(goodsBomDTO1);
        when(mockModuleMapper.mapToBomDOList(list)).thenReturn(goodsBomDOS);

        // Configure GoodsBomDOMapper.insertBatch(...).
        final GoodsBomDO goodsBomDO1 = new GoodsBomDO();
        goodsBomDO1.setGuid("f5f83da2-dc2b-4ff7-b5ca-e9f87295b872");
        goodsBomDO1.setGoodsGuid("goodsGuid");
        goodsBomDO1.setGoodsSku("goodsSku");
        goodsBomDO1.setMaterialGuid("materialGuid");
        goodsBomDO1.setUsage(new BigDecimal("0.00"));
        goodsBomDO1.setUnit("unitGuid");
        final List<GoodsBomDO> goodsBomDOS1 = Arrays.asList(goodsBomDO1);
        when(mockGoodsBomDOMapper.insertBatch(goodsBomDOS1)).thenReturn(0);

        // Run the test
        final boolean result = bomServiceImplUnderTest.add(goodsBom);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockGoodsBomDOMapper).deleteByExample(any(GoodsBomDOExample.class));
    }

    @Test
    public void testAdd_ErpModuleMapperReturnsNoItems() {
        // Setup
        final GoodsBomConfigDTO goodsBom = new GoodsBomConfigDTO();
        goodsBom.setGoodsGuid("goodsGuid");
        goodsBom.setGoodsSku("goodsSku");
        final GoodsBomDTO goodsBomDTO = new GoodsBomDTO();
        goodsBomDTO.setGoodsGuid("goodsGuid");
        goodsBomDTO.setMaterialGuid("materialGuid");
        goodsBom.setBomList(Arrays.asList(goodsBomDTO));

        // Configure ErpModuleMapper.mapToBomDOList(...).
        final GoodsBomDTO goodsBomDTO1 = new GoodsBomDTO();
        goodsBomDTO1.setGoodsGuid("goodsGuid");
        goodsBomDTO1.setMaterialGuid("materialGuid");
        goodsBomDTO1.setUsage(new BigDecimal("0.00"));
        goodsBomDTO1.setUnit("unit");
        goodsBomDTO1.setUnitName("unitName");
        final List<GoodsBomDTO> list = Arrays.asList(goodsBomDTO1);
        when(mockModuleMapper.mapToBomDOList(list)).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = bomServiceImplUnderTest.add(goodsBom);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockGoodsBomDOMapper).deleteByExample(any(GoodsBomDOExample.class));
    }

    @Test
    public void testFindBomByGoods() {
        // Setup
        final GoodsBomDTO goodsBomDTO = new GoodsBomDTO();
        goodsBomDTO.setGoodsGuid("goodsGuid");
        goodsBomDTO.setMaterialGuid("materialGuid");
        goodsBomDTO.setUsage(new BigDecimal("0.00"));
        goodsBomDTO.setUnit("unit");
        goodsBomDTO.setUnitName("unitName");
        final List<GoodsBomDTO> expectedResult = Arrays.asList(goodsBomDTO);

        // Configure GoodsBomDOMapper.findGoodsBom(...).
        final GoodsBomDO goodsBomDO = new GoodsBomDO();
        goodsBomDO.setGuid("f5f83da2-dc2b-4ff7-b5ca-e9f87295b872");
        goodsBomDO.setGoodsGuid("goodsGuid");
        goodsBomDO.setGoodsSku("goodsSku");
        goodsBomDO.setMaterialGuid("materialGuid");
        goodsBomDO.setUsage(new BigDecimal("0.00"));
        goodsBomDO.setUnit("unitGuid");
        final List<GoodsBomDO> goodsBomDOS = Arrays.asList(goodsBomDO);
        when(mockGoodsBomDOMapper.findGoodsBom("goodsGuid", "goodsSku")).thenReturn(goodsBomDOS);

        // Configure ErpModuleMapper.mapToBomDTOList(...).
        final GoodsBomDTO goodsBomDTO1 = new GoodsBomDTO();
        goodsBomDTO1.setGoodsGuid("goodsGuid");
        goodsBomDTO1.setMaterialGuid("materialGuid");
        goodsBomDTO1.setUsage(new BigDecimal("0.00"));
        goodsBomDTO1.setUnit("unit");
        goodsBomDTO1.setUnitName("unitName");
        final List<GoodsBomDTO> goodsBomDTOS = Arrays.asList(goodsBomDTO1);
        final GoodsBomDO goodsBomDO1 = new GoodsBomDO();
        goodsBomDO1.setGuid("f5f83da2-dc2b-4ff7-b5ca-e9f87295b872");
        goodsBomDO1.setGoodsGuid("goodsGuid");
        goodsBomDO1.setGoodsSku("goodsSku");
        goodsBomDO1.setMaterialGuid("materialGuid");
        goodsBomDO1.setUsage(new BigDecimal("0.00"));
        goodsBomDO1.setUnit("unitGuid");
        final List<GoodsBomDO> list = Arrays.asList(goodsBomDO1);
        when(mockModuleMapper.mapToBomDTOList(list)).thenReturn(goodsBomDTOS);

        // Run the test
        final List<GoodsBomDTO> result = bomServiceImplUnderTest.findBomByGoods("goodsGuid", "goodsSku");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindBomByGoods_GoodsBomDOMapperReturnsNoItems() {
        // Setup
        final GoodsBomDTO goodsBomDTO = new GoodsBomDTO();
        goodsBomDTO.setGoodsGuid("goodsGuid");
        goodsBomDTO.setMaterialGuid("materialGuid");
        goodsBomDTO.setUsage(new BigDecimal("0.00"));
        goodsBomDTO.setUnit("unit");
        goodsBomDTO.setUnitName("unitName");
        final List<GoodsBomDTO> expectedResult = Arrays.asList(goodsBomDTO);
        when(mockGoodsBomDOMapper.findGoodsBom("goodsGuid", "goodsSku")).thenReturn(Collections.emptyList());

        // Configure ErpModuleMapper.mapToBomDTOList(...).
        final GoodsBomDTO goodsBomDTO1 = new GoodsBomDTO();
        goodsBomDTO1.setGoodsGuid("goodsGuid");
        goodsBomDTO1.setMaterialGuid("materialGuid");
        goodsBomDTO1.setUsage(new BigDecimal("0.00"));
        goodsBomDTO1.setUnit("unit");
        goodsBomDTO1.setUnitName("unitName");
        final List<GoodsBomDTO> goodsBomDTOS = Arrays.asList(goodsBomDTO1);
        final GoodsBomDO goodsBomDO = new GoodsBomDO();
        goodsBomDO.setGuid("f5f83da2-dc2b-4ff7-b5ca-e9f87295b872");
        goodsBomDO.setGoodsGuid("goodsGuid");
        goodsBomDO.setGoodsSku("goodsSku");
        goodsBomDO.setMaterialGuid("materialGuid");
        goodsBomDO.setUsage(new BigDecimal("0.00"));
        goodsBomDO.setUnit("unitGuid");
        final List<GoodsBomDO> list = Arrays.asList(goodsBomDO);
        when(mockModuleMapper.mapToBomDTOList(list)).thenReturn(goodsBomDTOS);

        // Run the test
        final List<GoodsBomDTO> result = bomServiceImplUnderTest.findBomByGoods("goodsGuid", "goodsSku");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindBomByGoods_ErpModuleMapperReturnsNoItems() {
        // Setup
        // Configure GoodsBomDOMapper.findGoodsBom(...).
        final GoodsBomDO goodsBomDO = new GoodsBomDO();
        goodsBomDO.setGuid("f5f83da2-dc2b-4ff7-b5ca-e9f87295b872");
        goodsBomDO.setGoodsGuid("goodsGuid");
        goodsBomDO.setGoodsSku("goodsSku");
        goodsBomDO.setMaterialGuid("materialGuid");
        goodsBomDO.setUsage(new BigDecimal("0.00"));
        goodsBomDO.setUnit("unitGuid");
        final List<GoodsBomDO> goodsBomDOS = Arrays.asList(goodsBomDO);
        when(mockGoodsBomDOMapper.findGoodsBom("goodsGuid", "goodsSku")).thenReturn(goodsBomDOS);

        // Configure ErpModuleMapper.mapToBomDTOList(...).
        final GoodsBomDO goodsBomDO1 = new GoodsBomDO();
        goodsBomDO1.setGuid("f5f83da2-dc2b-4ff7-b5ca-e9f87295b872");
        goodsBomDO1.setGoodsGuid("goodsGuid");
        goodsBomDO1.setGoodsSku("goodsSku");
        goodsBomDO1.setMaterialGuid("materialGuid");
        goodsBomDO1.setUsage(new BigDecimal("0.00"));
        goodsBomDO1.setUnit("unitGuid");
        final List<GoodsBomDO> list = Arrays.asList(goodsBomDO1);
        when(mockModuleMapper.mapToBomDTOList(list)).thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodsBomDTO> result = bomServiceImplUnderTest.findBomByGoods("goodsGuid", "goodsSku");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testCountBomByMaterial() {
        // Setup
        when(mockGoodsBomDOMapper.countByExample(any(GoodsBomDOExample.class))).thenReturn(0L);

        // Run the test
        final Long result = bomServiceImplUnderTest.countBomByMaterial("materialGuid");

        // Verify the results
        assertThat(result).isEqualTo(0L);
    }

    @Test
    public void testCountBomTypeBySkuList() {
        // Setup
        final GoodsBomDTO goodsBomDTO = new GoodsBomDTO();
        goodsBomDTO.setGoodsGuid("goodsGuid");
        goodsBomDTO.setMaterialGuid("materialGuid");
        goodsBomDTO.setUsage(new BigDecimal("0.00"));
        goodsBomDTO.setUnit("unit");
        goodsBomDTO.setUnitName("unitName");
        final List<GoodsBomDTO> expectedResult = Arrays.asList(goodsBomDTO);

        // Configure GoodsBomDOMapper.countMaterialTypeBySkuList(...).
        final GoodsBomDO goodsBomDO = new GoodsBomDO();
        goodsBomDO.setGuid("f5f83da2-dc2b-4ff7-b5ca-e9f87295b872");
        goodsBomDO.setGoodsGuid("goodsGuid");
        goodsBomDO.setGoodsSku("goodsSku");
        goodsBomDO.setMaterialGuid("materialGuid");
        goodsBomDO.setUsage(new BigDecimal("0.00"));
        goodsBomDO.setUnit("unitGuid");
        final List<GoodsBomDO> goodsBomDOS = Arrays.asList(goodsBomDO);
        when(mockGoodsBomDOMapper.countMaterialTypeBySkuList(Arrays.asList("value"))).thenReturn(goodsBomDOS);

        // Configure ErpModuleMapper.mapToBomDTOList(...).
        final GoodsBomDTO goodsBomDTO1 = new GoodsBomDTO();
        goodsBomDTO1.setGoodsGuid("goodsGuid");
        goodsBomDTO1.setMaterialGuid("materialGuid");
        goodsBomDTO1.setUsage(new BigDecimal("0.00"));
        goodsBomDTO1.setUnit("unit");
        goodsBomDTO1.setUnitName("unitName");
        final List<GoodsBomDTO> goodsBomDTOS = Arrays.asList(goodsBomDTO1);
        final GoodsBomDO goodsBomDO1 = new GoodsBomDO();
        goodsBomDO1.setGuid("f5f83da2-dc2b-4ff7-b5ca-e9f87295b872");
        goodsBomDO1.setGoodsGuid("goodsGuid");
        goodsBomDO1.setGoodsSku("goodsSku");
        goodsBomDO1.setMaterialGuid("materialGuid");
        goodsBomDO1.setUsage(new BigDecimal("0.00"));
        goodsBomDO1.setUnit("unitGuid");
        final List<GoodsBomDO> list = Arrays.asList(goodsBomDO1);
        when(mockModuleMapper.mapToBomDTOList(list)).thenReturn(goodsBomDTOS);

        // Run the test
        final List<GoodsBomDTO> result = bomServiceImplUnderTest.countBomTypeBySkuList(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCountBomTypeBySkuList_GoodsBomDOMapperReturnsNoItems() {
        // Setup
        final GoodsBomDTO goodsBomDTO = new GoodsBomDTO();
        goodsBomDTO.setGoodsGuid("goodsGuid");
        goodsBomDTO.setMaterialGuid("materialGuid");
        goodsBomDTO.setUsage(new BigDecimal("0.00"));
        goodsBomDTO.setUnit("unit");
        goodsBomDTO.setUnitName("unitName");
        final List<GoodsBomDTO> expectedResult = Arrays.asList(goodsBomDTO);
        when(mockGoodsBomDOMapper.countMaterialTypeBySkuList(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Configure ErpModuleMapper.mapToBomDTOList(...).
        final GoodsBomDTO goodsBomDTO1 = new GoodsBomDTO();
        goodsBomDTO1.setGoodsGuid("goodsGuid");
        goodsBomDTO1.setMaterialGuid("materialGuid");
        goodsBomDTO1.setUsage(new BigDecimal("0.00"));
        goodsBomDTO1.setUnit("unit");
        goodsBomDTO1.setUnitName("unitName");
        final List<GoodsBomDTO> goodsBomDTOS = Arrays.asList(goodsBomDTO1);
        final GoodsBomDO goodsBomDO = new GoodsBomDO();
        goodsBomDO.setGuid("f5f83da2-dc2b-4ff7-b5ca-e9f87295b872");
        goodsBomDO.setGoodsGuid("goodsGuid");
        goodsBomDO.setGoodsSku("goodsSku");
        goodsBomDO.setMaterialGuid("materialGuid");
        goodsBomDO.setUsage(new BigDecimal("0.00"));
        goodsBomDO.setUnit("unitGuid");
        final List<GoodsBomDO> list = Arrays.asList(goodsBomDO);
        when(mockModuleMapper.mapToBomDTOList(list)).thenReturn(goodsBomDTOS);

        // Run the test
        final List<GoodsBomDTO> result = bomServiceImplUnderTest.countBomTypeBySkuList(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCountBomTypeBySkuList_ErpModuleMapperReturnsNoItems() {
        // Setup
        // Configure GoodsBomDOMapper.countMaterialTypeBySkuList(...).
        final GoodsBomDO goodsBomDO = new GoodsBomDO();
        goodsBomDO.setGuid("f5f83da2-dc2b-4ff7-b5ca-e9f87295b872");
        goodsBomDO.setGoodsGuid("goodsGuid");
        goodsBomDO.setGoodsSku("goodsSku");
        goodsBomDO.setMaterialGuid("materialGuid");
        goodsBomDO.setUsage(new BigDecimal("0.00"));
        goodsBomDO.setUnit("unitGuid");
        final List<GoodsBomDO> goodsBomDOS = Arrays.asList(goodsBomDO);
        when(mockGoodsBomDOMapper.countMaterialTypeBySkuList(Arrays.asList("value"))).thenReturn(goodsBomDOS);

        // Configure ErpModuleMapper.mapToBomDTOList(...).
        final GoodsBomDO goodsBomDO1 = new GoodsBomDO();
        goodsBomDO1.setGuid("f5f83da2-dc2b-4ff7-b5ca-e9f87295b872");
        goodsBomDO1.setGoodsGuid("goodsGuid");
        goodsBomDO1.setGoodsSku("goodsSku");
        goodsBomDO1.setMaterialGuid("materialGuid");
        goodsBomDO1.setUsage(new BigDecimal("0.00"));
        goodsBomDO1.setUnit("unitGuid");
        final List<GoodsBomDO> list = Arrays.asList(goodsBomDO1);
        when(mockModuleMapper.mapToBomDTOList(list)).thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodsBomDTO> result = bomServiceImplUnderTest.countBomTypeBySkuList(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testParseGoodsByBom() {
        // Setup
        final InOutDocumentBomQueryBO inOutDocumentBomQueryBO = new InOutDocumentBomQueryBO("storeGuid");

        // Configure GoodsBomDOMapper.findBomBySku(...).
        final GoodsBomDO goodsBomDO = new GoodsBomDO();
        goodsBomDO.setGuid("f5f83da2-dc2b-4ff7-b5ca-e9f87295b872");
        goodsBomDO.setGoodsGuid("goodsGuid");
        goodsBomDO.setGoodsSku("goodsSku");
        goodsBomDO.setMaterialGuid("materialGuid");
        goodsBomDO.setUsage(new BigDecimal("0.00"));
        goodsBomDO.setUnit("unitGuid");
        final List<GoodsBomDO> goodsBomDOS = Arrays.asList(goodsBomDO);
        when(mockGoodsBomDOMapper.findBomBySku(Arrays.asList("value"))).thenReturn(goodsBomDOS);

        // Configure IMaterialService.unitAdapterMain(...).
        final List<UnitConvertBO> unitConvertBOS = Arrays.asList(
                new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")));
        when(mockMaterialService.unitAdapterMain(
                Arrays.asList(new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")))))
                .thenReturn(unitConvertBOS);

        // Run the test
        final List<InOutDocumentBomBO> result = bomServiceImplUnderTest.parseGoodsByBom(inOutDocumentBomQueryBO);

        // Verify the results
    }

    @Test
    public void testParseGoodsByBom_GoodsBomDOMapperReturnsNoItems() {
        // Setup
        final InOutDocumentBomQueryBO inOutDocumentBomQueryBO = new InOutDocumentBomQueryBO("storeGuid");
        when(mockGoodsBomDOMapper.findBomBySku(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<InOutDocumentBomBO> result = bomServiceImplUnderTest.parseGoodsByBom(inOutDocumentBomQueryBO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testParseGoodsByBom_IMaterialServiceReturnsNoItems() {
        // Setup
        final InOutDocumentBomQueryBO inOutDocumentBomQueryBO = new InOutDocumentBomQueryBO("storeGuid");

        // Configure GoodsBomDOMapper.findBomBySku(...).
        final GoodsBomDO goodsBomDO = new GoodsBomDO();
        goodsBomDO.setGuid("f5f83da2-dc2b-4ff7-b5ca-e9f87295b872");
        goodsBomDO.setGoodsGuid("goodsGuid");
        goodsBomDO.setGoodsSku("goodsSku");
        goodsBomDO.setMaterialGuid("materialGuid");
        goodsBomDO.setUsage(new BigDecimal("0.00"));
        goodsBomDO.setUnit("unitGuid");
        final List<GoodsBomDO> goodsBomDOS = Arrays.asList(goodsBomDO);
        when(mockGoodsBomDOMapper.findBomBySku(Arrays.asList("value"))).thenReturn(goodsBomDOS);

        when(mockMaterialService.unitAdapterMain(
                Arrays.asList(new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")))))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<InOutDocumentBomBO> result = bomServiceImplUnderTest.parseGoodsByBom(inOutDocumentBomQueryBO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testUpdateBomUnit() {
        // Setup
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("ce190714-9aff-4016-8ba2-14aea288fdef");
        materialDTO.setEnterpriseGuid("enterpriseGuid");
        materialDTO.setUnit("unitGuid");
        materialDTO.setAuxiliaryUnit("unitGuid");

        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("33d47f45-f435-4bd9-89fb-7f020d0eb413");
        materialDO.setEnterpriseGuid("enterpriseGuid");
        materialDO.setStoreGuid("storeGuid");
        materialDO.setUnit("unit");
        materialDO.setAuxiliaryUnit("auxiliaryUnit");

        // Run the test
        bomServiceImplUnderTest.updateBomUnit(materialDTO, materialDO);

        // Verify the results
        verify(mockGoodsBomDOMapper).updateByExampleSelective(any(GoodsBomDO.class), any(GoodsBomDOExample.class));
    }
}
