package com.holderzone.erp.service.impl;

import com.holderzone.erp.dao.PricingSchemesMapper;
import com.holderzone.erp.entity.domain.ChangeUnitDO;
import com.holderzone.erp.entity.domain.PricingSchemesDO;
import com.holderzone.erp.entity.domain.PricingSchemesLogDO;
import com.holderzone.erp.mapperstruct.ErpModuleMapper;
import com.holderzone.saas.store.dto.erp.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PricingSchemesServiceImplTest {

    @Mock
    private ErpModuleMapper mockModuleMapper;
    @Mock
    private PricingSchemesMapper mockPricingSchemesMapper;

    private PricingSchemesServiceImpl pricingSchemesServiceImplUnderTest;

    @Before
    public void setUp() {
        pricingSchemesServiceImplUnderTest = new PricingSchemesServiceImpl(mockModuleMapper, mockPricingSchemesMapper);
    }

    @Test
    public void testSavePricingSchemes() {
        // Setup
        final PricingReqDTO pricingReqDTO = new PricingReqDTO();
        pricingReqDTO.setSuppliersGuid("suppliersGuid");
        final PricingSchemesReqDTO pricingSchemesReqDTO = new PricingSchemesReqDTO();
        pricingSchemesReqDTO.setGuid("d62b7c90-e7dc-4a54-b511-deebb419a12a");
        pricingSchemesReqDTO.setSuppliersGuid("suppliersGuid");
        pricingReqDTO.setPricingSchemesList(Arrays.asList(pricingSchemesReqDTO));
        pricingReqDTO.setDeleteList(Arrays.asList("value"));

        // Configure ErpModuleMapper.mapToPricingSchemesDoList(...).
        final PricingSchemesDO pricingSchemesDO = new PricingSchemesDO();
        pricingSchemesDO.setUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO.setUnitName("auxiliaryUnitName");
        pricingSchemesDO.setAuxiliaryUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO.setAuxiliaryUnitName("auxiliaryUnitName");
        pricingSchemesDO.setGuid("guid");
        pricingSchemesDO.setDealPrice(new BigDecimal("0.00"));
        pricingSchemesDO.setDealUnit("dealUnit");
        final List<PricingSchemesDO> pricingSchemesDOS = Arrays.asList(pricingSchemesDO);
        final PricingSchemesReqDTO pricingSchemesReqDTO1 = new PricingSchemesReqDTO();
        pricingSchemesReqDTO1.setEnabled(0);
        pricingSchemesReqDTO1.setGuid("d62b7c90-e7dc-4a54-b511-deebb419a12a");
        pricingSchemesReqDTO1.setSuppliersGuid("suppliersGuid");
        pricingSchemesReqDTO1.setMaterialGuid("materialGuid");
        pricingSchemesReqDTO1.setDealPrice(new BigDecimal("0.00"));
        final List<PricingSchemesReqDTO> pricingSchemesReqDTOList = Arrays.asList(pricingSchemesReqDTO1);
        when(mockModuleMapper.mapToPricingSchemesDoList(pricingSchemesReqDTOList)).thenReturn(pricingSchemesDOS);

        // Configure PricingSchemesMapper.getPricingSchemesListBySuppliersGuid(...).
        final PricingSchemesDO pricingSchemesDO1 = new PricingSchemesDO();
        pricingSchemesDO1.setUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO1.setUnitName("auxiliaryUnitName");
        pricingSchemesDO1.setAuxiliaryUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO1.setAuxiliaryUnitName("auxiliaryUnitName");
        pricingSchemesDO1.setGuid("guid");
        pricingSchemesDO1.setDealPrice(new BigDecimal("0.00"));
        pricingSchemesDO1.setDealUnit("dealUnit");
        final List<PricingSchemesDO> pricingSchemesDOS1 = Arrays.asList(pricingSchemesDO1);
        when(mockPricingSchemesMapper.getPricingSchemesListBySuppliersGuid("suppliersGuid"))
                .thenReturn(pricingSchemesDOS1);

        // Run the test
        final Boolean result = pricingSchemesServiceImplUnderTest.savePricingSchemes(pricingReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm PricingSchemesMapper.savePricingSchemesLog(...).
        final PricingSchemesLogDO pricingSchemesLogDO = new PricingSchemesLogDO();
        pricingSchemesLogDO.setGuid("1b03a3ff-faac-4326-b868-a5fd8ff4803e");
        pricingSchemesLogDO.setPricingSchemesGuid("guid");
        pricingSchemesLogDO.setLastDealPrice(new BigDecimal("0.00"));
        pricingSchemesLogDO.setCurrentDealPrice(new BigDecimal("0.00"));
        pricingSchemesLogDO.setOperator("operator");
        final List<PricingSchemesLogDO> list = Arrays.asList(pricingSchemesLogDO);
        verify(mockPricingSchemesMapper).savePricingSchemesLog(list);

        // Confirm PricingSchemesMapper.savePricingSchemes(...).
        final PricingSchemesDO pricingSchemesDO2 = new PricingSchemesDO();
        pricingSchemesDO2.setUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO2.setUnitName("auxiliaryUnitName");
        pricingSchemesDO2.setAuxiliaryUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO2.setAuxiliaryUnitName("auxiliaryUnitName");
        pricingSchemesDO2.setGuid("guid");
        pricingSchemesDO2.setDealPrice(new BigDecimal("0.00"));
        pricingSchemesDO2.setDealUnit("dealUnit");
        final List<PricingSchemesDO> list1 = Arrays.asList(pricingSchemesDO2);
        verify(mockPricingSchemesMapper).savePricingSchemes(list1);
        verify(mockPricingSchemesMapper).deletePricingSchemes(Arrays.asList("value"));
    }

    @Test
    public void testSavePricingSchemes_ErpModuleMapperReturnsNoItems() {
        // Setup
        final PricingReqDTO pricingReqDTO = new PricingReqDTO();
        pricingReqDTO.setSuppliersGuid("suppliersGuid");
        final PricingSchemesReqDTO pricingSchemesReqDTO = new PricingSchemesReqDTO();
        pricingSchemesReqDTO.setGuid("d62b7c90-e7dc-4a54-b511-deebb419a12a");
        pricingSchemesReqDTO.setSuppliersGuid("suppliersGuid");
        pricingReqDTO.setPricingSchemesList(Arrays.asList(pricingSchemesReqDTO));
        pricingReqDTO.setDeleteList(Arrays.asList("value"));

        // Configure ErpModuleMapper.mapToPricingSchemesDoList(...).
        final PricingSchemesReqDTO pricingSchemesReqDTO1 = new PricingSchemesReqDTO();
        pricingSchemesReqDTO1.setEnabled(0);
        pricingSchemesReqDTO1.setGuid("d62b7c90-e7dc-4a54-b511-deebb419a12a");
        pricingSchemesReqDTO1.setSuppliersGuid("suppliersGuid");
        pricingSchemesReqDTO1.setMaterialGuid("materialGuid");
        pricingSchemesReqDTO1.setDealPrice(new BigDecimal("0.00"));
        final List<PricingSchemesReqDTO> pricingSchemesReqDTOList = Arrays.asList(pricingSchemesReqDTO1);
        when(mockModuleMapper.mapToPricingSchemesDoList(pricingSchemesReqDTOList)).thenReturn(Collections.emptyList());

        // Configure PricingSchemesMapper.getPricingSchemesListBySuppliersGuid(...).
        final PricingSchemesDO pricingSchemesDO = new PricingSchemesDO();
        pricingSchemesDO.setUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO.setUnitName("auxiliaryUnitName");
        pricingSchemesDO.setAuxiliaryUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO.setAuxiliaryUnitName("auxiliaryUnitName");
        pricingSchemesDO.setGuid("guid");
        pricingSchemesDO.setDealPrice(new BigDecimal("0.00"));
        pricingSchemesDO.setDealUnit("dealUnit");
        final List<PricingSchemesDO> pricingSchemesDOS = Arrays.asList(pricingSchemesDO);
        when(mockPricingSchemesMapper.getPricingSchemesListBySuppliersGuid("suppliersGuid"))
                .thenReturn(pricingSchemesDOS);

        // Run the test
        final Boolean result = pricingSchemesServiceImplUnderTest.savePricingSchemes(pricingReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm PricingSchemesMapper.savePricingSchemesLog(...).
        final PricingSchemesLogDO pricingSchemesLogDO = new PricingSchemesLogDO();
        pricingSchemesLogDO.setGuid("1b03a3ff-faac-4326-b868-a5fd8ff4803e");
        pricingSchemesLogDO.setPricingSchemesGuid("guid");
        pricingSchemesLogDO.setLastDealPrice(new BigDecimal("0.00"));
        pricingSchemesLogDO.setCurrentDealPrice(new BigDecimal("0.00"));
        pricingSchemesLogDO.setOperator("operator");
        final List<PricingSchemesLogDO> list = Arrays.asList(pricingSchemesLogDO);
        verify(mockPricingSchemesMapper).savePricingSchemesLog(list);

        // Confirm PricingSchemesMapper.savePricingSchemes(...).
        final PricingSchemesDO pricingSchemesDO1 = new PricingSchemesDO();
        pricingSchemesDO1.setUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO1.setUnitName("auxiliaryUnitName");
        pricingSchemesDO1.setAuxiliaryUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO1.setAuxiliaryUnitName("auxiliaryUnitName");
        pricingSchemesDO1.setGuid("guid");
        pricingSchemesDO1.setDealPrice(new BigDecimal("0.00"));
        pricingSchemesDO1.setDealUnit("dealUnit");
        final List<PricingSchemesDO> list1 = Arrays.asList(pricingSchemesDO1);
        verify(mockPricingSchemesMapper).savePricingSchemes(list1);
        verify(mockPricingSchemesMapper).deletePricingSchemes(Arrays.asList("value"));
    }

    @Test
    public void testSavePricingSchemes_PricingSchemesMapperGetPricingSchemesListBySuppliersGuidReturnsNoItems() {
        // Setup
        final PricingReqDTO pricingReqDTO = new PricingReqDTO();
        pricingReqDTO.setSuppliersGuid("suppliersGuid");
        final PricingSchemesReqDTO pricingSchemesReqDTO = new PricingSchemesReqDTO();
        pricingSchemesReqDTO.setGuid("d62b7c90-e7dc-4a54-b511-deebb419a12a");
        pricingSchemesReqDTO.setSuppliersGuid("suppliersGuid");
        pricingReqDTO.setPricingSchemesList(Arrays.asList(pricingSchemesReqDTO));
        pricingReqDTO.setDeleteList(Arrays.asList("value"));

        // Configure ErpModuleMapper.mapToPricingSchemesDoList(...).
        final PricingSchemesDO pricingSchemesDO = new PricingSchemesDO();
        pricingSchemesDO.setUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO.setUnitName("auxiliaryUnitName");
        pricingSchemesDO.setAuxiliaryUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO.setAuxiliaryUnitName("auxiliaryUnitName");
        pricingSchemesDO.setGuid("guid");
        pricingSchemesDO.setDealPrice(new BigDecimal("0.00"));
        pricingSchemesDO.setDealUnit("dealUnit");
        final List<PricingSchemesDO> pricingSchemesDOS = Arrays.asList(pricingSchemesDO);
        final PricingSchemesReqDTO pricingSchemesReqDTO1 = new PricingSchemesReqDTO();
        pricingSchemesReqDTO1.setEnabled(0);
        pricingSchemesReqDTO1.setGuid("d62b7c90-e7dc-4a54-b511-deebb419a12a");
        pricingSchemesReqDTO1.setSuppliersGuid("suppliersGuid");
        pricingSchemesReqDTO1.setMaterialGuid("materialGuid");
        pricingSchemesReqDTO1.setDealPrice(new BigDecimal("0.00"));
        final List<PricingSchemesReqDTO> pricingSchemesReqDTOList = Arrays.asList(pricingSchemesReqDTO1);
        when(mockModuleMapper.mapToPricingSchemesDoList(pricingSchemesReqDTOList)).thenReturn(pricingSchemesDOS);

        when(mockPricingSchemesMapper.getPricingSchemesListBySuppliersGuid("suppliersGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Boolean result = pricingSchemesServiceImplUnderTest.savePricingSchemes(pricingReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm PricingSchemesMapper.savePricingSchemesLog(...).
        final PricingSchemesLogDO pricingSchemesLogDO = new PricingSchemesLogDO();
        pricingSchemesLogDO.setGuid("1b03a3ff-faac-4326-b868-a5fd8ff4803e");
        pricingSchemesLogDO.setPricingSchemesGuid("guid");
        pricingSchemesLogDO.setLastDealPrice(new BigDecimal("0.00"));
        pricingSchemesLogDO.setCurrentDealPrice(new BigDecimal("0.00"));
        pricingSchemesLogDO.setOperator("operator");
        final List<PricingSchemesLogDO> list = Arrays.asList(pricingSchemesLogDO);
        verify(mockPricingSchemesMapper).savePricingSchemesLog(list);

        // Confirm PricingSchemesMapper.savePricingSchemes(...).
        final PricingSchemesDO pricingSchemesDO1 = new PricingSchemesDO();
        pricingSchemesDO1.setUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO1.setUnitName("auxiliaryUnitName");
        pricingSchemesDO1.setAuxiliaryUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO1.setAuxiliaryUnitName("auxiliaryUnitName");
        pricingSchemesDO1.setGuid("guid");
        pricingSchemesDO1.setDealPrice(new BigDecimal("0.00"));
        pricingSchemesDO1.setDealUnit("dealUnit");
        final List<PricingSchemesDO> list1 = Arrays.asList(pricingSchemesDO1);
        verify(mockPricingSchemesMapper).savePricingSchemes(list1);
        verify(mockPricingSchemesMapper).deletePricingSchemes(Arrays.asList("value"));
    }

    @Test
    public void testGetPricingSchemesList() {
        // Setup
        // Configure PricingSchemesMapper.getPricingSchemesListBySuppliersGuid(...).
        final PricingSchemesDO pricingSchemesDO = new PricingSchemesDO();
        pricingSchemesDO.setUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO.setUnitName("auxiliaryUnitName");
        pricingSchemesDO.setAuxiliaryUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO.setAuxiliaryUnitName("auxiliaryUnitName");
        pricingSchemesDO.setGuid("guid");
        pricingSchemesDO.setDealPrice(new BigDecimal("0.00"));
        pricingSchemesDO.setDealUnit("dealUnit");
        final List<PricingSchemesDO> pricingSchemesDOS = Arrays.asList(pricingSchemesDO);
        when(mockPricingSchemesMapper.getPricingSchemesListBySuppliersGuid("suppliersGuid"))
                .thenReturn(pricingSchemesDOS);

        // Configure ErpModuleMapper.mapToPricingSchemesDtoList(...).
        final PricingSchemesDTO pricingSchemesDTO = new PricingSchemesDTO();
        pricingSchemesDTO.setMaterialName("materialName");
        final MaterialUnitDTO materialUnitDTO = new MaterialUnitDTO();
        materialUnitDTO.setGuid("116682c6-f6d7-4978-ad5b-055896aca017");
        materialUnitDTO.setName("auxiliaryUnitName");
        pricingSchemesDTO.setUnitList(Arrays.asList(materialUnitDTO));
        pricingSchemesDTO.setGuid("69eb7a75-6839-4cdb-b0d5-28a25f97f447");
        final List<PricingSchemesDTO> pricingSchemesDTOS = Arrays.asList(pricingSchemesDTO);
        final PricingSchemesDO pricingSchemesDO1 = new PricingSchemesDO();
        pricingSchemesDO1.setUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO1.setUnitName("auxiliaryUnitName");
        pricingSchemesDO1.setAuxiliaryUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO1.setAuxiliaryUnitName("auxiliaryUnitName");
        pricingSchemesDO1.setGuid("guid");
        pricingSchemesDO1.setDealPrice(new BigDecimal("0.00"));
        pricingSchemesDO1.setDealUnit("dealUnit");
        final List<PricingSchemesDO> list = Arrays.asList(pricingSchemesDO1);
        when(mockModuleMapper.mapToPricingSchemesDtoList(list)).thenReturn(pricingSchemesDTOS);

        // Run the test
        final List<PricingSchemesDTO> result = pricingSchemesServiceImplUnderTest.getPricingSchemesList(
                "suppliersGuid");

        // Verify the results
    }

    @Test
    public void testGetPricingSchemesList_PricingSchemesMapperReturnsNoItems() {
        // Setup
        when(mockPricingSchemesMapper.getPricingSchemesListBySuppliersGuid("suppliersGuid"))
                .thenReturn(Collections.emptyList());

        // Configure ErpModuleMapper.mapToPricingSchemesDtoList(...).
        final PricingSchemesDTO pricingSchemesDTO = new PricingSchemesDTO();
        pricingSchemesDTO.setMaterialName("materialName");
        final MaterialUnitDTO materialUnitDTO = new MaterialUnitDTO();
        materialUnitDTO.setGuid("116682c6-f6d7-4978-ad5b-055896aca017");
        materialUnitDTO.setName("auxiliaryUnitName");
        pricingSchemesDTO.setUnitList(Arrays.asList(materialUnitDTO));
        pricingSchemesDTO.setGuid("69eb7a75-6839-4cdb-b0d5-28a25f97f447");
        final List<PricingSchemesDTO> pricingSchemesDTOS = Arrays.asList(pricingSchemesDTO);
        final PricingSchemesDO pricingSchemesDO = new PricingSchemesDO();
        pricingSchemesDO.setUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO.setUnitName("auxiliaryUnitName");
        pricingSchemesDO.setAuxiliaryUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO.setAuxiliaryUnitName("auxiliaryUnitName");
        pricingSchemesDO.setGuid("guid");
        pricingSchemesDO.setDealPrice(new BigDecimal("0.00"));
        pricingSchemesDO.setDealUnit("dealUnit");
        final List<PricingSchemesDO> list = Arrays.asList(pricingSchemesDO);
        when(mockModuleMapper.mapToPricingSchemesDtoList(list)).thenReturn(pricingSchemesDTOS);

        // Run the test
        final List<PricingSchemesDTO> result = pricingSchemesServiceImplUnderTest.getPricingSchemesList(
                "suppliersGuid");

        // Verify the results
    }

    @Test
    public void testGetPricingSchemesList_ErpModuleMapperReturnsNoItems() {
        // Setup
        // Configure PricingSchemesMapper.getPricingSchemesListBySuppliersGuid(...).
        final PricingSchemesDO pricingSchemesDO = new PricingSchemesDO();
        pricingSchemesDO.setUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO.setUnitName("auxiliaryUnitName");
        pricingSchemesDO.setAuxiliaryUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO.setAuxiliaryUnitName("auxiliaryUnitName");
        pricingSchemesDO.setGuid("guid");
        pricingSchemesDO.setDealPrice(new BigDecimal("0.00"));
        pricingSchemesDO.setDealUnit("dealUnit");
        final List<PricingSchemesDO> pricingSchemesDOS = Arrays.asList(pricingSchemesDO);
        when(mockPricingSchemesMapper.getPricingSchemesListBySuppliersGuid("suppliersGuid"))
                .thenReturn(pricingSchemesDOS);

        // Configure ErpModuleMapper.mapToPricingSchemesDtoList(...).
        final PricingSchemesDO pricingSchemesDO1 = new PricingSchemesDO();
        pricingSchemesDO1.setUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO1.setUnitName("auxiliaryUnitName");
        pricingSchemesDO1.setAuxiliaryUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO1.setAuxiliaryUnitName("auxiliaryUnitName");
        pricingSchemesDO1.setGuid("guid");
        pricingSchemesDO1.setDealPrice(new BigDecimal("0.00"));
        pricingSchemesDO1.setDealUnit("dealUnit");
        final List<PricingSchemesDO> list = Arrays.asList(pricingSchemesDO1);
        when(mockModuleMapper.mapToPricingSchemesDtoList(list)).thenReturn(Collections.emptyList());

        // Run the test
        final List<PricingSchemesDTO> result = pricingSchemesServiceImplUnderTest.getPricingSchemesList(
                "suppliersGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testChangeMaterialUnitInPricingSchemes() {
        // Setup
        final ChangeUnitDO changeUnitDO = new ChangeUnitDO();
        changeUnitDO.setMaterialGuid("materialGuid");
        changeUnitDO.setOriginalMainUnit("originalMainUnit");
        changeUnitDO.setOriginalAuxiliaryUnit("originalAuxiliaryUnit");
        changeUnitDO.setNewMainUnit("dealUnit");
        changeUnitDO.setNewAuxiliaryUnit("newAuxiliaryUnit");

        // Configure PricingSchemesMapper.getPricingSchemesByMaterialGuid(...).
        final PricingSchemesDO pricingSchemesDO = new PricingSchemesDO();
        pricingSchemesDO.setUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO.setUnitName("auxiliaryUnitName");
        pricingSchemesDO.setAuxiliaryUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO.setAuxiliaryUnitName("auxiliaryUnitName");
        pricingSchemesDO.setGuid("guid");
        pricingSchemesDO.setDealPrice(new BigDecimal("0.00"));
        pricingSchemesDO.setDealUnit("dealUnit");
        final List<PricingSchemesDO> pricingSchemesDOS = Arrays.asList(pricingSchemesDO);
        when(mockPricingSchemesMapper.getPricingSchemesByMaterialGuid("materialGuid")).thenReturn(pricingSchemesDOS);

        // Run the test
        final Boolean result = pricingSchemesServiceImplUnderTest.changeMaterialUnitInPricingSchemes(changeUnitDO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm PricingSchemesMapper.changeMaterialUnitInPricingSchemes(...).
        final PricingSchemesDO pricingSchemesDO1 = new PricingSchemesDO();
        pricingSchemesDO1.setUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO1.setUnitName("auxiliaryUnitName");
        pricingSchemesDO1.setAuxiliaryUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO1.setAuxiliaryUnitName("auxiliaryUnitName");
        pricingSchemesDO1.setGuid("guid");
        pricingSchemesDO1.setDealPrice(new BigDecimal("0.00"));
        pricingSchemesDO1.setDealUnit("dealUnit");
        final List<PricingSchemesDO> list = Arrays.asList(pricingSchemesDO1);
        verify(mockPricingSchemesMapper).changeMaterialUnitInPricingSchemes(list);
    }

    @Test
    public void testChangeMaterialUnitInPricingSchemes_PricingSchemesMapperGetPricingSchemesByMaterialGuidReturnsNoItems() {
        // Setup
        final ChangeUnitDO changeUnitDO = new ChangeUnitDO();
        changeUnitDO.setMaterialGuid("materialGuid");
        changeUnitDO.setOriginalMainUnit("originalMainUnit");
        changeUnitDO.setOriginalAuxiliaryUnit("originalAuxiliaryUnit");
        changeUnitDO.setNewMainUnit("dealUnit");
        changeUnitDO.setNewAuxiliaryUnit("newAuxiliaryUnit");

        when(mockPricingSchemesMapper.getPricingSchemesByMaterialGuid("materialGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Boolean result = pricingSchemesServiceImplUnderTest.changeMaterialUnitInPricingSchemes(changeUnitDO);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testDeletePricingSchemes() {
        // Setup
        // Run the test
        final Boolean result = pricingSchemesServiceImplUnderTest.deletePricingSchemes(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isTrue();
        verify(mockPricingSchemesMapper).deletePricingSchemes(Arrays.asList("value"));
    }

    @Test
    public void testEnableOrDisablePricingSchemes() {
        // Setup
        // Run the test
        final Boolean result = pricingSchemesServiceImplUnderTest.enableOrDisablePricingSchemes(
                "4d41c294-df73-4787-8678-3a874542a68a");

        // Verify the results
        assertThat(result).isTrue();
        verify(mockPricingSchemesMapper).enableOrDisablePricingSchemes("4d41c294-df73-4787-8678-3a874542a68a");
    }

    @Test
    public void testBatchQueryPricingSchemesList() {
        // Setup
        final PricingSchemesQueryDTO queryDTO = new PricingSchemesQueryDTO();
        queryDTO.setSuppliersGuid("suppliersGuid");
        queryDTO.setList(Arrays.asList("value"));

        // Configure PricingSchemesMapper.batchQueryPricingSchemesList(...).
        final PricingSchemesDO pricingSchemesDO = new PricingSchemesDO();
        pricingSchemesDO.setUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO.setUnitName("auxiliaryUnitName");
        pricingSchemesDO.setAuxiliaryUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO.setAuxiliaryUnitName("auxiliaryUnitName");
        pricingSchemesDO.setGuid("guid");
        pricingSchemesDO.setDealPrice(new BigDecimal("0.00"));
        pricingSchemesDO.setDealUnit("dealUnit");
        final List<PricingSchemesDO> pricingSchemesDOS = Arrays.asList(pricingSchemesDO);
        when(mockPricingSchemesMapper.batchQueryPricingSchemesList("suppliersGuid", Arrays.asList("value")))
                .thenReturn(pricingSchemesDOS);

        // Configure ErpModuleMapper.mapToPricingSchemesDtoList(...).
        final PricingSchemesDTO pricingSchemesDTO = new PricingSchemesDTO();
        pricingSchemesDTO.setMaterialName("materialName");
        final MaterialUnitDTO materialUnitDTO = new MaterialUnitDTO();
        materialUnitDTO.setGuid("116682c6-f6d7-4978-ad5b-055896aca017");
        materialUnitDTO.setName("auxiliaryUnitName");
        pricingSchemesDTO.setUnitList(Arrays.asList(materialUnitDTO));
        pricingSchemesDTO.setGuid("69eb7a75-6839-4cdb-b0d5-28a25f97f447");
        final List<PricingSchemesDTO> pricingSchemesDTOS = Arrays.asList(pricingSchemesDTO);
        final PricingSchemesDO pricingSchemesDO1 = new PricingSchemesDO();
        pricingSchemesDO1.setUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO1.setUnitName("auxiliaryUnitName");
        pricingSchemesDO1.setAuxiliaryUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO1.setAuxiliaryUnitName("auxiliaryUnitName");
        pricingSchemesDO1.setGuid("guid");
        pricingSchemesDO1.setDealPrice(new BigDecimal("0.00"));
        pricingSchemesDO1.setDealUnit("dealUnit");
        final List<PricingSchemesDO> list = Arrays.asList(pricingSchemesDO1);
        when(mockModuleMapper.mapToPricingSchemesDtoList(list)).thenReturn(pricingSchemesDTOS);

        // Run the test
        final List<PricingSchemesDTO> result = pricingSchemesServiceImplUnderTest.batchQueryPricingSchemesList(
                queryDTO);

        // Verify the results
    }

    @Test
    public void testBatchQueryPricingSchemesList_PricingSchemesMapperReturnsNoItems() {
        // Setup
        final PricingSchemesQueryDTO queryDTO = new PricingSchemesQueryDTO();
        queryDTO.setSuppliersGuid("suppliersGuid");
        queryDTO.setList(Arrays.asList("value"));

        when(mockPricingSchemesMapper.batchQueryPricingSchemesList("suppliersGuid", Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Configure ErpModuleMapper.mapToPricingSchemesDtoList(...).
        final PricingSchemesDTO pricingSchemesDTO = new PricingSchemesDTO();
        pricingSchemesDTO.setMaterialName("materialName");
        final MaterialUnitDTO materialUnitDTO = new MaterialUnitDTO();
        materialUnitDTO.setGuid("116682c6-f6d7-4978-ad5b-055896aca017");
        materialUnitDTO.setName("auxiliaryUnitName");
        pricingSchemesDTO.setUnitList(Arrays.asList(materialUnitDTO));
        pricingSchemesDTO.setGuid("69eb7a75-6839-4cdb-b0d5-28a25f97f447");
        final List<PricingSchemesDTO> pricingSchemesDTOS = Arrays.asList(pricingSchemesDTO);
        final PricingSchemesDO pricingSchemesDO = new PricingSchemesDO();
        pricingSchemesDO.setUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO.setUnitName("auxiliaryUnitName");
        pricingSchemesDO.setAuxiliaryUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO.setAuxiliaryUnitName("auxiliaryUnitName");
        pricingSchemesDO.setGuid("guid");
        pricingSchemesDO.setDealPrice(new BigDecimal("0.00"));
        pricingSchemesDO.setDealUnit("dealUnit");
        final List<PricingSchemesDO> list = Arrays.asList(pricingSchemesDO);
        when(mockModuleMapper.mapToPricingSchemesDtoList(list)).thenReturn(pricingSchemesDTOS);

        // Run the test
        final List<PricingSchemesDTO> result = pricingSchemesServiceImplUnderTest.batchQueryPricingSchemesList(
                queryDTO);

        // Verify the results
    }

    @Test
    public void testBatchQueryPricingSchemesList_ErpModuleMapperReturnsNoItems() {
        // Setup
        final PricingSchemesQueryDTO queryDTO = new PricingSchemesQueryDTO();
        queryDTO.setSuppliersGuid("suppliersGuid");
        queryDTO.setList(Arrays.asList("value"));

        // Configure PricingSchemesMapper.batchQueryPricingSchemesList(...).
        final PricingSchemesDO pricingSchemesDO = new PricingSchemesDO();
        pricingSchemesDO.setUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO.setUnitName("auxiliaryUnitName");
        pricingSchemesDO.setAuxiliaryUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO.setAuxiliaryUnitName("auxiliaryUnitName");
        pricingSchemesDO.setGuid("guid");
        pricingSchemesDO.setDealPrice(new BigDecimal("0.00"));
        pricingSchemesDO.setDealUnit("dealUnit");
        final List<PricingSchemesDO> pricingSchemesDOS = Arrays.asList(pricingSchemesDO);
        when(mockPricingSchemesMapper.batchQueryPricingSchemesList("suppliersGuid", Arrays.asList("value")))
                .thenReturn(pricingSchemesDOS);

        // Configure ErpModuleMapper.mapToPricingSchemesDtoList(...).
        final PricingSchemesDO pricingSchemesDO1 = new PricingSchemesDO();
        pricingSchemesDO1.setUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO1.setUnitName("auxiliaryUnitName");
        pricingSchemesDO1.setAuxiliaryUnit("116682c6-f6d7-4978-ad5b-055896aca017");
        pricingSchemesDO1.setAuxiliaryUnitName("auxiliaryUnitName");
        pricingSchemesDO1.setGuid("guid");
        pricingSchemesDO1.setDealPrice(new BigDecimal("0.00"));
        pricingSchemesDO1.setDealUnit("dealUnit");
        final List<PricingSchemesDO> list = Arrays.asList(pricingSchemesDO1);
        when(mockModuleMapper.mapToPricingSchemesDtoList(list)).thenReturn(Collections.emptyList());

        // Run the test
        final List<PricingSchemesDTO> result = pricingSchemesServiceImplUnderTest.batchQueryPricingSchemesList(
                queryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
