package com.holderzone.erp.service.impl;

import com.holderzone.erp.dao.CheckoutDocumentDetailMapper;
import com.holderzone.erp.dao.CheckoutDocumentMapper;
import com.holderzone.erp.entity.domain.CheckoutDocumentDO;
import com.holderzone.erp.entity.domain.CheckoutDocumentDetailDO;
import com.holderzone.erp.entity.domain.CheckoutDocumentQuery;
import com.holderzone.erp.service.CheckoutDocumentService;
import com.holderzone.erp.service.IMaterialService;
import com.holderzone.erp.service.InOutDocumentService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CheckoutDocumentServiceImplTest {

    @Mock
    private CheckoutDocumentMapper mockCheckoutDocumentMapper;
    @Mock
    private CheckoutDocumentDetailMapper mockDetailMapper;
    @Mock
    private IMaterialService mockMaterialService;
    @Mock
    private InOutDocumentService mockInOutDocumentService;
    @Mock
    private CheckoutDocumentService mockCheckoutDocumentService;
    @Mock
    private RedissonClient mockRedisson;

    @InjectMocks
    private CheckoutDocumentServiceImpl checkoutDocumentServiceImplUnderTest;

    @Test
    public void testInsertCheckoutDocumentAndDetail() {
        // Setup
        final CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO = new CheckoutDocumentAddOrUpdateDTO();
        addOrUpdateDTO.setUserGuid("createStaffGuid");
        addOrUpdateDTO.setUserName("createStaffName");
        final CheckoutDocumentDetailAddOrUpdateDTO checkoutDocumentDetailAddOrUpdateDTO = new CheckoutDocumentDetailAddOrUpdateDTO();
        checkoutDocumentDetailAddOrUpdateDTO.setGuid("4aa701cc-dced-4bcd-ab4a-9cc7c7eb8e82");
        checkoutDocumentDetailAddOrUpdateDTO.setDocumentGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        addOrUpdateDTO.setDetailList(Arrays.asList(checkoutDocumentDetailAddOrUpdateDTO));
        addOrUpdateDTO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");

        // Run the test
        checkoutDocumentServiceImplUnderTest.insertCheckoutDocumentAndDetail(addOrUpdateDTO);

        // Verify the results
        verify(mockCheckoutDocumentMapper).insertCheckoutDocument(any(CheckoutDocumentDO.class));

        // Confirm CheckoutDocumentDetailMapper.insertCheckoutDocumentDetailList(...).
        final CheckoutDocumentDetailDO checkoutDocumentDetailDO = new CheckoutDocumentDetailDO();
        checkoutDocumentDetailDO.setMaterialCode("materialCode");
        checkoutDocumentDetailDO.setMaterialGuid("materialGuid");
        checkoutDocumentDetailDO.setMaterialName("materialName");
        checkoutDocumentDetailDO.setStock(new BigDecimal("0.00"));
        checkoutDocumentDetailDO.setUnitGuid("unitGuid");
        checkoutDocumentDetailDO.setUnitName("unitName");
        checkoutDocumentDetailDO.setCheckCount(new BigDecimal("0.00"));
        checkoutDocumentDetailDO.setCheckoutResult(0);
        final List<CheckoutDocumentDetailDO> detailDOList = Arrays.asList(checkoutDocumentDetailDO);
        verify(mockDetailMapper).insertCheckoutDocumentDetailList(detailDOList);
    }

    @Test
    public void testInsertCheckoutDocument() {
        // Setup
        final CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO = new CheckoutDocumentAddOrUpdateDTO();
        addOrUpdateDTO.setUserGuid("createStaffGuid");
        addOrUpdateDTO.setUserName("createStaffName");
        final CheckoutDocumentDetailAddOrUpdateDTO checkoutDocumentDetailAddOrUpdateDTO = new CheckoutDocumentDetailAddOrUpdateDTO();
        checkoutDocumentDetailAddOrUpdateDTO.setGuid("4aa701cc-dced-4bcd-ab4a-9cc7c7eb8e82");
        checkoutDocumentDetailAddOrUpdateDTO.setDocumentGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        addOrUpdateDTO.setDetailList(Arrays.asList(checkoutDocumentDetailAddOrUpdateDTO));
        addOrUpdateDTO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");

        // Run the test
        checkoutDocumentServiceImplUnderTest.insertCheckoutDocument(addOrUpdateDTO);

        // Verify the results
        verify(mockCheckoutDocumentMapper).insertCheckoutDocument(any(CheckoutDocumentDO.class));
    }

    @Test
    public void testInsertCheckoutDocumentDetail() {
        // Setup
        final CheckoutDocumentDetailAddOrUpdateDTO checkoutDocumentDetailAddOrUpdateDTO = new CheckoutDocumentDetailAddOrUpdateDTO();
        checkoutDocumentDetailAddOrUpdateDTO.setGuid("4aa701cc-dced-4bcd-ab4a-9cc7c7eb8e82");
        checkoutDocumentDetailAddOrUpdateDTO.setDocumentGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        checkoutDocumentDetailAddOrUpdateDTO.setMaterialGuid("materialGuid");
        checkoutDocumentDetailAddOrUpdateDTO.setMaterialCode("materialCode");
        checkoutDocumentDetailAddOrUpdateDTO.setMaterialName("materialName");
        final List<CheckoutDocumentDetailAddOrUpdateDTO> detailList = Arrays.asList(
                checkoutDocumentDetailAddOrUpdateDTO);

        // Run the test
        checkoutDocumentServiceImplUnderTest.insertCheckoutDocumentDetail(detailList);

        // Verify the results
        // Confirm CheckoutDocumentDetailMapper.insertCheckoutDocumentDetailList(...).
        final CheckoutDocumentDetailDO checkoutDocumentDetailDO = new CheckoutDocumentDetailDO();
        checkoutDocumentDetailDO.setMaterialCode("materialCode");
        checkoutDocumentDetailDO.setMaterialGuid("materialGuid");
        checkoutDocumentDetailDO.setMaterialName("materialName");
        checkoutDocumentDetailDO.setStock(new BigDecimal("0.00"));
        checkoutDocumentDetailDO.setUnitGuid("unitGuid");
        checkoutDocumentDetailDO.setUnitName("unitName");
        checkoutDocumentDetailDO.setCheckCount(new BigDecimal("0.00"));
        checkoutDocumentDetailDO.setCheckoutResult(0);
        final List<CheckoutDocumentDetailDO> detailDOList = Arrays.asList(checkoutDocumentDetailDO);
        verify(mockDetailMapper).insertCheckoutDocumentDetailList(detailDOList);
    }

    @Test
    public void testUpdateCheckoutDocumentAndDetail() {
        // Setup
        final CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO = new CheckoutDocumentAddOrUpdateDTO();
        addOrUpdateDTO.setUserGuid("createStaffGuid");
        addOrUpdateDTO.setUserName("createStaffName");
        final CheckoutDocumentDetailAddOrUpdateDTO checkoutDocumentDetailAddOrUpdateDTO = new CheckoutDocumentDetailAddOrUpdateDTO();
        checkoutDocumentDetailAddOrUpdateDTO.setGuid("4aa701cc-dced-4bcd-ab4a-9cc7c7eb8e82");
        checkoutDocumentDetailAddOrUpdateDTO.setDocumentGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        addOrUpdateDTO.setDetailList(Arrays.asList(checkoutDocumentDetailAddOrUpdateDTO));
        addOrUpdateDTO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");

        // Run the test
        checkoutDocumentServiceImplUnderTest.updateCheckoutDocumentAndDetail(addOrUpdateDTO);

        // Verify the results
        verify(mockCheckoutDocumentMapper).deleteCheckoutDocumentAndDetail("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        verify(mockCheckoutDocumentMapper).insertCheckoutDocument(any(CheckoutDocumentDO.class));

        // Confirm CheckoutDocumentDetailMapper.insertCheckoutDocumentDetailList(...).
        final CheckoutDocumentDetailDO checkoutDocumentDetailDO = new CheckoutDocumentDetailDO();
        checkoutDocumentDetailDO.setMaterialCode("materialCode");
        checkoutDocumentDetailDO.setMaterialGuid("materialGuid");
        checkoutDocumentDetailDO.setMaterialName("materialName");
        checkoutDocumentDetailDO.setStock(new BigDecimal("0.00"));
        checkoutDocumentDetailDO.setUnitGuid("unitGuid");
        checkoutDocumentDetailDO.setUnitName("unitName");
        checkoutDocumentDetailDO.setCheckCount(new BigDecimal("0.00"));
        checkoutDocumentDetailDO.setCheckoutResult(0);
        final List<CheckoutDocumentDetailDO> detailDOList = Arrays.asList(checkoutDocumentDetailDO);
        verify(mockDetailMapper).insertCheckoutDocumentDetailList(detailDOList);
    }

    @Test
    public void testDeleteCheckoutDocumentAndDetail() {
        // Setup
        // Run the test
        checkoutDocumentServiceImplUnderTest.deleteCheckoutDocumentAndDetail("e7202d9a-8604-4769-af9c-a834cd3aba1b");

        // Verify the results
        verify(mockCheckoutDocumentMapper).deleteCheckoutDocumentAndDetail("e7202d9a-8604-4769-af9c-a834cd3aba1b");
    }

    @Test
    public void testSelectDocumentDetailForAdd() {
        // Setup
        final CheckoutDocumentDetailQueryDTO queryDTO = new CheckoutDocumentDetailQueryDTO();
        queryDTO.setWarehouseGuid("warehouseGuid");
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setMaterialGuidList(Arrays.asList("value"));

        // Configure IMaterialService.findList(...).
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setUserGuid("createStaffGuid");
        materialDTO.setUserName("createStaffName");
        materialDTO.setGuid("b37cb80f-aeac-4343-ae06-13addcc1ba48");
        materialDTO.setUnit("unit");
        materialDTO.setStock(new BigDecimal("0.00"));
        materialDTO.setUnitName("unitName");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO);
        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value")))
                .thenReturn(materialDTOS);

        // Run the test
        final List<CheckoutDocumentDetailSelectDTO> result = checkoutDocumentServiceImplUnderTest.selectDocumentDetailForAdd(
                queryDTO);

        // Verify the results
    }

    @Test
    public void testSelectDocumentDetailForAdd_IMaterialServiceReturnsNull() {
        // Setup
        final CheckoutDocumentDetailQueryDTO queryDTO = new CheckoutDocumentDetailQueryDTO();
        queryDTO.setWarehouseGuid("warehouseGuid");
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setMaterialGuidList(Arrays.asList("value"));

        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value"))).thenReturn(null);

        // Run the test
        assertThatThrownBy(
                () -> checkoutDocumentServiceImplUnderTest.selectDocumentDetailForAdd(queryDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSelectDocumentDetailForAdd_IMaterialServiceReturnsNoItems() {
        // Setup
        final CheckoutDocumentDetailQueryDTO queryDTO = new CheckoutDocumentDetailQueryDTO();
        queryDTO.setWarehouseGuid("warehouseGuid");
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setMaterialGuidList(Arrays.asList("value"));

        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(
                () -> checkoutDocumentServiceImplUnderTest.selectDocumentDetailForAdd(queryDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSelectDocumentAndDetailForSelect() {
        // Setup
        // Configure CheckoutDocumentMapper.selectDocumentAndDetail(...).
        final CheckoutDocumentDO checkoutDocumentDO = new CheckoutDocumentDO();
        checkoutDocumentDO.setStoreGuid("storeGuid");
        checkoutDocumentDO.setCreateStaffGuid("createStaffGuid");
        checkoutDocumentDO.setCreateStaffName("createStaffName");
        checkoutDocumentDO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        checkoutDocumentDO.setWarehouseGuid("warehouseGuid");
        checkoutDocumentDO.setWarehouseName("warehouseName");
        checkoutDocumentDO.setStatus(0);
        when(mockCheckoutDocumentMapper.selectDocumentAndDetail("documentGuid")).thenReturn(checkoutDocumentDO);

        // Run the test
        final CheckoutDocumentSelectDTO result = checkoutDocumentServiceImplUnderTest.selectDocumentAndDetailForSelect(
                "documentGuid");

        // Verify the results
    }

    @Test
    public void testExistDocument() {
        // Setup
        when(mockCheckoutDocumentMapper.selectDocumentCount("documentGuid")).thenReturn(0);

        // Run the test
        final int result = checkoutDocumentServiceImplUnderTest.existDocument("documentGuid");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testSelectDocumentStatus() {
        // Setup
        // Configure CheckoutDocumentMapper.selectDocumentStatus(...).
        final CheckoutDocumentDO checkoutDocumentDO = new CheckoutDocumentDO();
        checkoutDocumentDO.setStoreGuid("storeGuid");
        checkoutDocumentDO.setCreateStaffGuid("createStaffGuid");
        checkoutDocumentDO.setCreateStaffName("createStaffName");
        checkoutDocumentDO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        checkoutDocumentDO.setWarehouseGuid("warehouseGuid");
        checkoutDocumentDO.setWarehouseName("warehouseName");
        checkoutDocumentDO.setStatus(0);
        when(mockCheckoutDocumentMapper.selectDocumentStatus("documentGuid")).thenReturn(checkoutDocumentDO);

        // Run the test
        final CheckoutDocumentDO result = checkoutDocumentServiceImplUnderTest.selectDocumentStatus("documentGuid");

        // Verify the results
    }

    @Test
    public void testSubmitCheckoutDocument() {
        // Setup
        // Configure CheckoutDocumentMapper.selectDocumentWarehouseAndStoreAndCreateStaff(...).
        final CheckoutDocumentDO checkoutDocumentDO = new CheckoutDocumentDO();
        checkoutDocumentDO.setStoreGuid("storeGuid");
        checkoutDocumentDO.setCreateStaffGuid("createStaffGuid");
        checkoutDocumentDO.setCreateStaffName("createStaffName");
        checkoutDocumentDO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        checkoutDocumentDO.setWarehouseGuid("warehouseGuid");
        checkoutDocumentDO.setWarehouseName("warehouseName");
        checkoutDocumentDO.setStatus(0);
        when(mockCheckoutDocumentMapper.selectDocumentWarehouseAndStoreAndCreateStaff(
                "e7202d9a-8604-4769-af9c-a834cd3aba1b")).thenReturn(checkoutDocumentDO);

        // Configure CheckoutDocumentDetailMapper.selectDocumentDetailList(...).
        final CheckoutDocumentDetailDO checkoutDocumentDetailDO = new CheckoutDocumentDetailDO();
        checkoutDocumentDetailDO.setMaterialCode("materialCode");
        checkoutDocumentDetailDO.setMaterialGuid("materialGuid");
        checkoutDocumentDetailDO.setMaterialName("materialName");
        checkoutDocumentDetailDO.setStock(new BigDecimal("0.00"));
        checkoutDocumentDetailDO.setUnitGuid("unitGuid");
        checkoutDocumentDetailDO.setUnitName("unitName");
        checkoutDocumentDetailDO.setCheckCount(new BigDecimal("0.00"));
        checkoutDocumentDetailDO.setCheckoutResult(0);
        final List<CheckoutDocumentDetailDO> checkoutDocumentDetailDOS = Arrays.asList(checkoutDocumentDetailDO);
        when(mockDetailMapper.selectDocumentDetailList("e7202d9a-8604-4769-af9c-a834cd3aba1b"))
                .thenReturn(checkoutDocumentDetailDOS);

        // Run the test
        checkoutDocumentServiceImplUnderTest.submitCheckoutDocument("e7202d9a-8604-4769-af9c-a834cd3aba1b");

        // Verify the results
        verify(mockCheckoutDocumentMapper).submitCheckoutDocument("e7202d9a-8604-4769-af9c-a834cd3aba1b");

        // Confirm InOutDocumentService.insertAndSubmitInOutDocument(...).
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setUserGuid("createStaffGuid");
        inOutDocumentDTO.setUserName("createStaffName");
        inOutDocumentDTO.setDocumentStoreGuid("storeGuid");
        inOutDocumentDTO.setInOutType(0);
        inOutDocumentDTO.setWarehouseName("warehouseName");
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setStock(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setMaterialGuid("materialGuid");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialCode("materialCode");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialName("materialName");
        inOutDocumentDetailAddOrUpdateDTO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setUnitGuid("unitGuid");
        inOutDocumentDetailAddOrUpdateDTO.setUnitName("unitName");
        inOutDocumentDetailAddOrUpdateDTO.setUnitPrice(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDTO.setDocumentDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inOutDocumentDTO.setType(0);
        inOutDocumentDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setOperatorGuid("createStaffGuid");
        inOutDocumentDTO.setOperatorName("createStaffName");
        verify(mockInOutDocumentService).insertAndSubmitInOutDocument(inOutDocumentDTO);
    }

    @Test
    public void testSubmitCheckoutDocument_CheckoutDocumentDetailMapperReturnsNoItems() {
        // Setup
        // Configure CheckoutDocumentMapper.selectDocumentWarehouseAndStoreAndCreateStaff(...).
        final CheckoutDocumentDO checkoutDocumentDO = new CheckoutDocumentDO();
        checkoutDocumentDO.setStoreGuid("storeGuid");
        checkoutDocumentDO.setCreateStaffGuid("createStaffGuid");
        checkoutDocumentDO.setCreateStaffName("createStaffName");
        checkoutDocumentDO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        checkoutDocumentDO.setWarehouseGuid("warehouseGuid");
        checkoutDocumentDO.setWarehouseName("warehouseName");
        checkoutDocumentDO.setStatus(0);
        when(mockCheckoutDocumentMapper.selectDocumentWarehouseAndStoreAndCreateStaff(
                "e7202d9a-8604-4769-af9c-a834cd3aba1b")).thenReturn(checkoutDocumentDO);

        when(mockDetailMapper.selectDocumentDetailList("e7202d9a-8604-4769-af9c-a834cd3aba1b"))
                .thenReturn(Collections.emptyList());

        // Run the test
        checkoutDocumentServiceImplUnderTest.submitCheckoutDocument("e7202d9a-8604-4769-af9c-a834cd3aba1b");

        // Verify the results
        verify(mockCheckoutDocumentMapper).submitCheckoutDocument("e7202d9a-8604-4769-af9c-a834cd3aba1b");

        // Confirm InOutDocumentService.insertAndSubmitInOutDocument(...).
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setUserGuid("createStaffGuid");
        inOutDocumentDTO.setUserName("createStaffName");
        inOutDocumentDTO.setDocumentStoreGuid("storeGuid");
        inOutDocumentDTO.setInOutType(0);
        inOutDocumentDTO.setWarehouseName("warehouseName");
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setStock(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setMaterialGuid("materialGuid");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialCode("materialCode");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialName("materialName");
        inOutDocumentDetailAddOrUpdateDTO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setUnitGuid("unitGuid");
        inOutDocumentDetailAddOrUpdateDTO.setUnitName("unitName");
        inOutDocumentDetailAddOrUpdateDTO.setUnitPrice(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDTO.setDocumentDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inOutDocumentDTO.setType(0);
        inOutDocumentDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setOperatorGuid("createStaffGuid");
        inOutDocumentDTO.setOperatorName("createStaffName");
        verify(mockInOutDocumentService).insertAndSubmitInOutDocument(inOutDocumentDTO);
    }

    @Test
    public void testInsertAndSubmitCheckoutDocument() {
        // Setup
        final CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO = new CheckoutDocumentAddOrUpdateDTO();
        addOrUpdateDTO.setUserGuid("createStaffGuid");
        addOrUpdateDTO.setUserName("createStaffName");
        final CheckoutDocumentDetailAddOrUpdateDTO checkoutDocumentDetailAddOrUpdateDTO = new CheckoutDocumentDetailAddOrUpdateDTO();
        checkoutDocumentDetailAddOrUpdateDTO.setGuid("4aa701cc-dced-4bcd-ab4a-9cc7c7eb8e82");
        checkoutDocumentDetailAddOrUpdateDTO.setDocumentGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        addOrUpdateDTO.setDetailList(Arrays.asList(checkoutDocumentDetailAddOrUpdateDTO));
        addOrUpdateDTO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");

        // Configure CheckoutDocumentMapper.selectDocumentWarehouseAndStoreAndCreateStaff(...).
        final CheckoutDocumentDO checkoutDocumentDO = new CheckoutDocumentDO();
        checkoutDocumentDO.setStoreGuid("storeGuid");
        checkoutDocumentDO.setCreateStaffGuid("createStaffGuid");
        checkoutDocumentDO.setCreateStaffName("createStaffName");
        checkoutDocumentDO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        checkoutDocumentDO.setWarehouseGuid("warehouseGuid");
        checkoutDocumentDO.setWarehouseName("warehouseName");
        checkoutDocumentDO.setStatus(0);
        when(mockCheckoutDocumentMapper.selectDocumentWarehouseAndStoreAndCreateStaff(
                "e7202d9a-8604-4769-af9c-a834cd3aba1b")).thenReturn(checkoutDocumentDO);

        // Configure CheckoutDocumentDetailMapper.selectDocumentDetailList(...).
        final CheckoutDocumentDetailDO checkoutDocumentDetailDO = new CheckoutDocumentDetailDO();
        checkoutDocumentDetailDO.setMaterialCode("materialCode");
        checkoutDocumentDetailDO.setMaterialGuid("materialGuid");
        checkoutDocumentDetailDO.setMaterialName("materialName");
        checkoutDocumentDetailDO.setStock(new BigDecimal("0.00"));
        checkoutDocumentDetailDO.setUnitGuid("unitGuid");
        checkoutDocumentDetailDO.setUnitName("unitName");
        checkoutDocumentDetailDO.setCheckCount(new BigDecimal("0.00"));
        checkoutDocumentDetailDO.setCheckoutResult(0);
        final List<CheckoutDocumentDetailDO> checkoutDocumentDetailDOS = Arrays.asList(checkoutDocumentDetailDO);
        when(mockDetailMapper.selectDocumentDetailList("e7202d9a-8604-4769-af9c-a834cd3aba1b"))
                .thenReturn(checkoutDocumentDetailDOS);

        // Run the test
        final String result = checkoutDocumentServiceImplUnderTest.insertAndSubmitCheckoutDocument(addOrUpdateDTO);

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockCheckoutDocumentMapper).insertCheckoutDocument(any(CheckoutDocumentDO.class));

        // Confirm CheckoutDocumentDetailMapper.insertCheckoutDocumentDetailList(...).
        final CheckoutDocumentDetailDO checkoutDocumentDetailDO1 = new CheckoutDocumentDetailDO();
        checkoutDocumentDetailDO1.setMaterialCode("materialCode");
        checkoutDocumentDetailDO1.setMaterialGuid("materialGuid");
        checkoutDocumentDetailDO1.setMaterialName("materialName");
        checkoutDocumentDetailDO1.setStock(new BigDecimal("0.00"));
        checkoutDocumentDetailDO1.setUnitGuid("unitGuid");
        checkoutDocumentDetailDO1.setUnitName("unitName");
        checkoutDocumentDetailDO1.setCheckCount(new BigDecimal("0.00"));
        checkoutDocumentDetailDO1.setCheckoutResult(0);
        final List<CheckoutDocumentDetailDO> detailDOList = Arrays.asList(checkoutDocumentDetailDO1);
        verify(mockDetailMapper).insertCheckoutDocumentDetailList(detailDOList);
        verify(mockCheckoutDocumentMapper).submitCheckoutDocument("e7202d9a-8604-4769-af9c-a834cd3aba1b");

        // Confirm InOutDocumentService.insertAndSubmitInOutDocument(...).
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setUserGuid("createStaffGuid");
        inOutDocumentDTO.setUserName("createStaffName");
        inOutDocumentDTO.setDocumentStoreGuid("storeGuid");
        inOutDocumentDTO.setInOutType(0);
        inOutDocumentDTO.setWarehouseName("warehouseName");
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setStock(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setMaterialGuid("materialGuid");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialCode("materialCode");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialName("materialName");
        inOutDocumentDetailAddOrUpdateDTO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setUnitGuid("unitGuid");
        inOutDocumentDetailAddOrUpdateDTO.setUnitName("unitName");
        inOutDocumentDetailAddOrUpdateDTO.setUnitPrice(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDTO.setDocumentDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inOutDocumentDTO.setType(0);
        inOutDocumentDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setOperatorGuid("createStaffGuid");
        inOutDocumentDTO.setOperatorName("createStaffName");
        verify(mockInOutDocumentService).insertAndSubmitInOutDocument(inOutDocumentDTO);
    }

    @Test
    public void testInsertAndSubmitCheckoutDocument_CheckoutDocumentDetailMapperSelectDocumentDetailListReturnsNoItems() {
        // Setup
        final CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO = new CheckoutDocumentAddOrUpdateDTO();
        addOrUpdateDTO.setUserGuid("createStaffGuid");
        addOrUpdateDTO.setUserName("createStaffName");
        final CheckoutDocumentDetailAddOrUpdateDTO checkoutDocumentDetailAddOrUpdateDTO = new CheckoutDocumentDetailAddOrUpdateDTO();
        checkoutDocumentDetailAddOrUpdateDTO.setGuid("4aa701cc-dced-4bcd-ab4a-9cc7c7eb8e82");
        checkoutDocumentDetailAddOrUpdateDTO.setDocumentGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        addOrUpdateDTO.setDetailList(Arrays.asList(checkoutDocumentDetailAddOrUpdateDTO));
        addOrUpdateDTO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");

        // Configure CheckoutDocumentMapper.selectDocumentWarehouseAndStoreAndCreateStaff(...).
        final CheckoutDocumentDO checkoutDocumentDO = new CheckoutDocumentDO();
        checkoutDocumentDO.setStoreGuid("storeGuid");
        checkoutDocumentDO.setCreateStaffGuid("createStaffGuid");
        checkoutDocumentDO.setCreateStaffName("createStaffName");
        checkoutDocumentDO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        checkoutDocumentDO.setWarehouseGuid("warehouseGuid");
        checkoutDocumentDO.setWarehouseName("warehouseName");
        checkoutDocumentDO.setStatus(0);
        when(mockCheckoutDocumentMapper.selectDocumentWarehouseAndStoreAndCreateStaff(
                "e7202d9a-8604-4769-af9c-a834cd3aba1b")).thenReturn(checkoutDocumentDO);

        when(mockDetailMapper.selectDocumentDetailList("e7202d9a-8604-4769-af9c-a834cd3aba1b"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final String result = checkoutDocumentServiceImplUnderTest.insertAndSubmitCheckoutDocument(addOrUpdateDTO);

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockCheckoutDocumentMapper).insertCheckoutDocument(any(CheckoutDocumentDO.class));

        // Confirm CheckoutDocumentDetailMapper.insertCheckoutDocumentDetailList(...).
        final CheckoutDocumentDetailDO checkoutDocumentDetailDO = new CheckoutDocumentDetailDO();
        checkoutDocumentDetailDO.setMaterialCode("materialCode");
        checkoutDocumentDetailDO.setMaterialGuid("materialGuid");
        checkoutDocumentDetailDO.setMaterialName("materialName");
        checkoutDocumentDetailDO.setStock(new BigDecimal("0.00"));
        checkoutDocumentDetailDO.setUnitGuid("unitGuid");
        checkoutDocumentDetailDO.setUnitName("unitName");
        checkoutDocumentDetailDO.setCheckCount(new BigDecimal("0.00"));
        checkoutDocumentDetailDO.setCheckoutResult(0);
        final List<CheckoutDocumentDetailDO> detailDOList = Arrays.asList(checkoutDocumentDetailDO);
        verify(mockDetailMapper).insertCheckoutDocumentDetailList(detailDOList);
        verify(mockCheckoutDocumentMapper).submitCheckoutDocument("e7202d9a-8604-4769-af9c-a834cd3aba1b");

        // Confirm InOutDocumentService.insertAndSubmitInOutDocument(...).
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setUserGuid("createStaffGuid");
        inOutDocumentDTO.setUserName("createStaffName");
        inOutDocumentDTO.setDocumentStoreGuid("storeGuid");
        inOutDocumentDTO.setInOutType(0);
        inOutDocumentDTO.setWarehouseName("warehouseName");
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setStock(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setMaterialGuid("materialGuid");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialCode("materialCode");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialName("materialName");
        inOutDocumentDetailAddOrUpdateDTO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setUnitGuid("unitGuid");
        inOutDocumentDetailAddOrUpdateDTO.setUnitName("unitName");
        inOutDocumentDetailAddOrUpdateDTO.setUnitPrice(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDTO.setDocumentDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inOutDocumentDTO.setType(0);
        inOutDocumentDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setOperatorGuid("createStaffGuid");
        inOutDocumentDTO.setOperatorName("createStaffName");
        verify(mockInOutDocumentService).insertAndSubmitInOutDocument(inOutDocumentDTO);
    }

    @Test
    public void testSelectCheckoutDocumentForPage() {
        // Setup
        final CheckoutDocumentQueryDTO queryDTO = new CheckoutDocumentQueryDTO();
        queryDTO.setUserGuid("createStaffGuid");
        queryDTO.setUserName("createStaffName");
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setWarehouseGuidList(Arrays.asList("value"));

        // Configure CheckoutDocumentMapper.selectCheckoutDocumentList(...).
        final CheckoutDocumentDO checkoutDocumentDO = new CheckoutDocumentDO();
        checkoutDocumentDO.setStoreGuid("storeGuid");
        checkoutDocumentDO.setCreateStaffGuid("createStaffGuid");
        checkoutDocumentDO.setCreateStaffName("createStaffName");
        checkoutDocumentDO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        checkoutDocumentDO.setWarehouseGuid("warehouseGuid");
        checkoutDocumentDO.setWarehouseName("warehouseName");
        checkoutDocumentDO.setStatus(0);
        final List<CheckoutDocumentDO> checkoutDocumentDOS = Arrays.asList(checkoutDocumentDO);
        when(mockCheckoutDocumentMapper.selectCheckoutDocumentList(any(CheckoutDocumentQuery.class)))
                .thenReturn(checkoutDocumentDOS);

        when(mockCheckoutDocumentMapper.selectDocumentListCount(any(CheckoutDocumentQuery.class))).thenReturn(0L);

        // Run the test
        final Page<CheckoutDocumentSelectDTO> result = checkoutDocumentServiceImplUnderTest.selectCheckoutDocumentForPage(
                queryDTO);

        // Verify the results
    }

    @Test
    public void testSelectCheckoutDocumentForPage_CheckoutDocumentMapperSelectCheckoutDocumentListReturnsNoItems() {
        // Setup
        final CheckoutDocumentQueryDTO queryDTO = new CheckoutDocumentQueryDTO();
        queryDTO.setUserGuid("createStaffGuid");
        queryDTO.setUserName("createStaffName");
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setWarehouseGuidList(Arrays.asList("value"));

        when(mockCheckoutDocumentMapper.selectCheckoutDocumentList(any(CheckoutDocumentQuery.class)))
                .thenReturn(Collections.emptyList());
        when(mockCheckoutDocumentMapper.selectDocumentListCount(any(CheckoutDocumentQuery.class))).thenReturn(0L);

        // Run the test
        final Page<CheckoutDocumentSelectDTO> result = checkoutDocumentServiceImplUnderTest.selectCheckoutDocumentForPage(
                queryDTO);

        // Verify the results
    }

    @Test
    public void testUpdateAndSubmitCheckoutDocumentWithLock() {
        // Setup
        final CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO = new CheckoutDocumentAddOrUpdateDTO();
        addOrUpdateDTO.setUserGuid("createStaffGuid");
        addOrUpdateDTO.setUserName("createStaffName");
        final CheckoutDocumentDetailAddOrUpdateDTO checkoutDocumentDetailAddOrUpdateDTO = new CheckoutDocumentDetailAddOrUpdateDTO();
        checkoutDocumentDetailAddOrUpdateDTO.setGuid("4aa701cc-dced-4bcd-ab4a-9cc7c7eb8e82");
        checkoutDocumentDetailAddOrUpdateDTO.setDocumentGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        addOrUpdateDTO.setDetailList(Arrays.asList(checkoutDocumentDetailAddOrUpdateDTO));
        addOrUpdateDTO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");

        when(mockRedisson.getLock("e7202d9a-8604-4769-af9c-a834cd3aba1b")).thenReturn(null);

        // Run the test
        checkoutDocumentServiceImplUnderTest.updateAndSubmitCheckoutDocumentWithLock(addOrUpdateDTO);

        // Verify the results
        // Confirm CheckoutDocumentService.updateAndSubmitCheckoutDocument(...).
        final CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO1 = new CheckoutDocumentAddOrUpdateDTO();
        addOrUpdateDTO1.setUserGuid("createStaffGuid");
        addOrUpdateDTO1.setUserName("createStaffName");
        final CheckoutDocumentDetailAddOrUpdateDTO checkoutDocumentDetailAddOrUpdateDTO1 = new CheckoutDocumentDetailAddOrUpdateDTO();
        checkoutDocumentDetailAddOrUpdateDTO1.setGuid("4aa701cc-dced-4bcd-ab4a-9cc7c7eb8e82");
        checkoutDocumentDetailAddOrUpdateDTO1.setDocumentGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        addOrUpdateDTO1.setDetailList(Arrays.asList(checkoutDocumentDetailAddOrUpdateDTO1));
        addOrUpdateDTO1.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        verify(mockCheckoutDocumentService).updateAndSubmitCheckoutDocument(addOrUpdateDTO1);
    }

    @Test
    public void testUpdateAndSubmitCheckoutDocument() {
        // Setup
        final CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO = new CheckoutDocumentAddOrUpdateDTO();
        addOrUpdateDTO.setUserGuid("createStaffGuid");
        addOrUpdateDTO.setUserName("createStaffName");
        final CheckoutDocumentDetailAddOrUpdateDTO checkoutDocumentDetailAddOrUpdateDTO = new CheckoutDocumentDetailAddOrUpdateDTO();
        checkoutDocumentDetailAddOrUpdateDTO.setGuid("4aa701cc-dced-4bcd-ab4a-9cc7c7eb8e82");
        checkoutDocumentDetailAddOrUpdateDTO.setDocumentGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        addOrUpdateDTO.setDetailList(Arrays.asList(checkoutDocumentDetailAddOrUpdateDTO));
        addOrUpdateDTO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");

        // Configure CheckoutDocumentMapper.selectDocumentWarehouseAndStoreAndCreateStaff(...).
        final CheckoutDocumentDO checkoutDocumentDO = new CheckoutDocumentDO();
        checkoutDocumentDO.setStoreGuid("storeGuid");
        checkoutDocumentDO.setCreateStaffGuid("createStaffGuid");
        checkoutDocumentDO.setCreateStaffName("createStaffName");
        checkoutDocumentDO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        checkoutDocumentDO.setWarehouseGuid("warehouseGuid");
        checkoutDocumentDO.setWarehouseName("warehouseName");
        checkoutDocumentDO.setStatus(0);
        when(mockCheckoutDocumentMapper.selectDocumentWarehouseAndStoreAndCreateStaff(
                "e7202d9a-8604-4769-af9c-a834cd3aba1b")).thenReturn(checkoutDocumentDO);

        // Configure CheckoutDocumentDetailMapper.selectDocumentDetailList(...).
        final CheckoutDocumentDetailDO checkoutDocumentDetailDO = new CheckoutDocumentDetailDO();
        checkoutDocumentDetailDO.setMaterialCode("materialCode");
        checkoutDocumentDetailDO.setMaterialGuid("materialGuid");
        checkoutDocumentDetailDO.setMaterialName("materialName");
        checkoutDocumentDetailDO.setStock(new BigDecimal("0.00"));
        checkoutDocumentDetailDO.setUnitGuid("unitGuid");
        checkoutDocumentDetailDO.setUnitName("unitName");
        checkoutDocumentDetailDO.setCheckCount(new BigDecimal("0.00"));
        checkoutDocumentDetailDO.setCheckoutResult(0);
        final List<CheckoutDocumentDetailDO> checkoutDocumentDetailDOS = Arrays.asList(checkoutDocumentDetailDO);
        when(mockDetailMapper.selectDocumentDetailList("e7202d9a-8604-4769-af9c-a834cd3aba1b"))
                .thenReturn(checkoutDocumentDetailDOS);

        // Run the test
        checkoutDocumentServiceImplUnderTest.updateAndSubmitCheckoutDocument(addOrUpdateDTO);

        // Verify the results
        verify(mockCheckoutDocumentMapper).deleteCheckoutDocumentAndDetail("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        verify(mockCheckoutDocumentMapper).insertCheckoutDocument(any(CheckoutDocumentDO.class));

        // Confirm CheckoutDocumentDetailMapper.insertCheckoutDocumentDetailList(...).
        final CheckoutDocumentDetailDO checkoutDocumentDetailDO1 = new CheckoutDocumentDetailDO();
        checkoutDocumentDetailDO1.setMaterialCode("materialCode");
        checkoutDocumentDetailDO1.setMaterialGuid("materialGuid");
        checkoutDocumentDetailDO1.setMaterialName("materialName");
        checkoutDocumentDetailDO1.setStock(new BigDecimal("0.00"));
        checkoutDocumentDetailDO1.setUnitGuid("unitGuid");
        checkoutDocumentDetailDO1.setUnitName("unitName");
        checkoutDocumentDetailDO1.setCheckCount(new BigDecimal("0.00"));
        checkoutDocumentDetailDO1.setCheckoutResult(0);
        final List<CheckoutDocumentDetailDO> detailDOList = Arrays.asList(checkoutDocumentDetailDO1);
        verify(mockDetailMapper).insertCheckoutDocumentDetailList(detailDOList);
        verify(mockCheckoutDocumentMapper).submitCheckoutDocument("e7202d9a-8604-4769-af9c-a834cd3aba1b");

        // Confirm InOutDocumentService.insertAndSubmitInOutDocument(...).
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setUserGuid("createStaffGuid");
        inOutDocumentDTO.setUserName("createStaffName");
        inOutDocumentDTO.setDocumentStoreGuid("storeGuid");
        inOutDocumentDTO.setInOutType(0);
        inOutDocumentDTO.setWarehouseName("warehouseName");
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setStock(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setMaterialGuid("materialGuid");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialCode("materialCode");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialName("materialName");
        inOutDocumentDetailAddOrUpdateDTO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setUnitGuid("unitGuid");
        inOutDocumentDetailAddOrUpdateDTO.setUnitName("unitName");
        inOutDocumentDetailAddOrUpdateDTO.setUnitPrice(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDTO.setDocumentDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inOutDocumentDTO.setType(0);
        inOutDocumentDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setOperatorGuid("createStaffGuid");
        inOutDocumentDTO.setOperatorName("createStaffName");
        verify(mockInOutDocumentService).insertAndSubmitInOutDocument(inOutDocumentDTO);
    }

    @Test
    public void testUpdateAndSubmitCheckoutDocument_CheckoutDocumentMapperSelectDocumentWarehouseAndStoreAndCreateStaffReturnsNull() {
        // Setup
        final CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO = new CheckoutDocumentAddOrUpdateDTO();
        addOrUpdateDTO.setUserGuid("createStaffGuid");
        addOrUpdateDTO.setUserName("createStaffName");
        final CheckoutDocumentDetailAddOrUpdateDTO checkoutDocumentDetailAddOrUpdateDTO = new CheckoutDocumentDetailAddOrUpdateDTO();
        checkoutDocumentDetailAddOrUpdateDTO.setGuid("4aa701cc-dced-4bcd-ab4a-9cc7c7eb8e82");
        checkoutDocumentDetailAddOrUpdateDTO.setDocumentGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        addOrUpdateDTO.setDetailList(Arrays.asList(checkoutDocumentDetailAddOrUpdateDTO));
        addOrUpdateDTO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");

        when(mockCheckoutDocumentMapper.selectDocumentWarehouseAndStoreAndCreateStaff(
                "e7202d9a-8604-4769-af9c-a834cd3aba1b")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> checkoutDocumentServiceImplUnderTest.updateAndSubmitCheckoutDocument(
                addOrUpdateDTO)).isInstanceOf(ParameterException.class);
    }

    @Test
    public void testUpdateAndSubmitCheckoutDocument_CheckoutDocumentDetailMapperSelectDocumentDetailListReturnsNoItems() {
        // Setup
        final CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO = new CheckoutDocumentAddOrUpdateDTO();
        addOrUpdateDTO.setUserGuid("createStaffGuid");
        addOrUpdateDTO.setUserName("createStaffName");
        final CheckoutDocumentDetailAddOrUpdateDTO checkoutDocumentDetailAddOrUpdateDTO = new CheckoutDocumentDetailAddOrUpdateDTO();
        checkoutDocumentDetailAddOrUpdateDTO.setGuid("4aa701cc-dced-4bcd-ab4a-9cc7c7eb8e82");
        checkoutDocumentDetailAddOrUpdateDTO.setDocumentGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        addOrUpdateDTO.setDetailList(Arrays.asList(checkoutDocumentDetailAddOrUpdateDTO));
        addOrUpdateDTO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");

        // Configure CheckoutDocumentMapper.selectDocumentWarehouseAndStoreAndCreateStaff(...).
        final CheckoutDocumentDO checkoutDocumentDO = new CheckoutDocumentDO();
        checkoutDocumentDO.setStoreGuid("storeGuid");
        checkoutDocumentDO.setCreateStaffGuid("createStaffGuid");
        checkoutDocumentDO.setCreateStaffName("createStaffName");
        checkoutDocumentDO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        checkoutDocumentDO.setWarehouseGuid("warehouseGuid");
        checkoutDocumentDO.setWarehouseName("warehouseName");
        checkoutDocumentDO.setStatus(0);
        when(mockCheckoutDocumentMapper.selectDocumentWarehouseAndStoreAndCreateStaff(
                "e7202d9a-8604-4769-af9c-a834cd3aba1b")).thenReturn(checkoutDocumentDO);

        when(mockDetailMapper.selectDocumentDetailList("e7202d9a-8604-4769-af9c-a834cd3aba1b"))
                .thenReturn(Collections.emptyList());

        // Run the test
        checkoutDocumentServiceImplUnderTest.updateAndSubmitCheckoutDocument(addOrUpdateDTO);

        // Verify the results
        verify(mockCheckoutDocumentMapper).deleteCheckoutDocumentAndDetail("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        verify(mockCheckoutDocumentMapper).insertCheckoutDocument(any(CheckoutDocumentDO.class));

        // Confirm CheckoutDocumentDetailMapper.insertCheckoutDocumentDetailList(...).
        final CheckoutDocumentDetailDO checkoutDocumentDetailDO = new CheckoutDocumentDetailDO();
        checkoutDocumentDetailDO.setMaterialCode("materialCode");
        checkoutDocumentDetailDO.setMaterialGuid("materialGuid");
        checkoutDocumentDetailDO.setMaterialName("materialName");
        checkoutDocumentDetailDO.setStock(new BigDecimal("0.00"));
        checkoutDocumentDetailDO.setUnitGuid("unitGuid");
        checkoutDocumentDetailDO.setUnitName("unitName");
        checkoutDocumentDetailDO.setCheckCount(new BigDecimal("0.00"));
        checkoutDocumentDetailDO.setCheckoutResult(0);
        final List<CheckoutDocumentDetailDO> detailDOList = Arrays.asList(checkoutDocumentDetailDO);
        verify(mockDetailMapper).insertCheckoutDocumentDetailList(detailDOList);
        verify(mockCheckoutDocumentMapper).submitCheckoutDocument("e7202d9a-8604-4769-af9c-a834cd3aba1b");

        // Confirm InOutDocumentService.insertAndSubmitInOutDocument(...).
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setUserGuid("createStaffGuid");
        inOutDocumentDTO.setUserName("createStaffName");
        inOutDocumentDTO.setDocumentStoreGuid("storeGuid");
        inOutDocumentDTO.setInOutType(0);
        inOutDocumentDTO.setWarehouseName("warehouseName");
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setStock(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setMaterialGuid("materialGuid");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialCode("materialCode");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialName("materialName");
        inOutDocumentDetailAddOrUpdateDTO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setUnitGuid("unitGuid");
        inOutDocumentDetailAddOrUpdateDTO.setUnitName("unitName");
        inOutDocumentDetailAddOrUpdateDTO.setUnitPrice(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDTO.setDocumentDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        inOutDocumentDTO.setType(0);
        inOutDocumentDTO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDTO.setOperatorGuid("createStaffGuid");
        inOutDocumentDTO.setOperatorName("createStaffName");
        verify(mockInOutDocumentService).insertAndSubmitInOutDocument(inOutDocumentDTO);
    }

    @Test
    public void testSelectDocument() {
        // Setup
        // Configure CheckoutDocumentMapper.selectDocument(...).
        final CheckoutDocumentDO checkoutDocumentDO = new CheckoutDocumentDO();
        checkoutDocumentDO.setStoreGuid("storeGuid");
        checkoutDocumentDO.setCreateStaffGuid("createStaffGuid");
        checkoutDocumentDO.setCreateStaffName("createStaffName");
        checkoutDocumentDO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        checkoutDocumentDO.setWarehouseGuid("warehouseGuid");
        checkoutDocumentDO.setWarehouseName("warehouseName");
        checkoutDocumentDO.setStatus(0);
        when(mockCheckoutDocumentMapper.selectDocument("documentGuid")).thenReturn(checkoutDocumentDO);

        // Run the test
        final CheckoutDocumentSelectDTO result = checkoutDocumentServiceImplUnderTest.selectDocument("documentGuid");

        // Verify the results
    }

    @Test
    public void testSelectDocumentDetailForUpdate() {
        // Setup
        // Configure CheckoutDocumentDetailMapper.selectDocumentDetailList(...).
        final CheckoutDocumentDetailDO checkoutDocumentDetailDO = new CheckoutDocumentDetailDO();
        checkoutDocumentDetailDO.setMaterialCode("materialCode");
        checkoutDocumentDetailDO.setMaterialGuid("materialGuid");
        checkoutDocumentDetailDO.setMaterialName("materialName");
        checkoutDocumentDetailDO.setStock(new BigDecimal("0.00"));
        checkoutDocumentDetailDO.setUnitGuid("unitGuid");
        checkoutDocumentDetailDO.setUnitName("unitName");
        checkoutDocumentDetailDO.setCheckCount(new BigDecimal("0.00"));
        checkoutDocumentDetailDO.setCheckoutResult(0);
        final List<CheckoutDocumentDetailDO> checkoutDocumentDetailDOS = Arrays.asList(checkoutDocumentDetailDO);
        when(mockDetailMapper.selectDocumentDetailList("documentGuid")).thenReturn(checkoutDocumentDetailDOS);

        // Configure CheckoutDocumentMapper.selectDocumentStoreGuidAndWarehouseGuid(...).
        final CheckoutDocumentDO checkoutDocumentDO = new CheckoutDocumentDO();
        checkoutDocumentDO.setStoreGuid("storeGuid");
        checkoutDocumentDO.setCreateStaffGuid("createStaffGuid");
        checkoutDocumentDO.setCreateStaffName("createStaffName");
        checkoutDocumentDO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        checkoutDocumentDO.setWarehouseGuid("warehouseGuid");
        checkoutDocumentDO.setWarehouseName("warehouseName");
        checkoutDocumentDO.setStatus(0);
        when(mockCheckoutDocumentMapper.selectDocumentStoreGuidAndWarehouseGuid("documentGuid"))
                .thenReturn(checkoutDocumentDO);

        // Configure IMaterialService.findList(...).
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setUserGuid("createStaffGuid");
        materialDTO.setUserName("createStaffName");
        materialDTO.setGuid("b37cb80f-aeac-4343-ae06-13addcc1ba48");
        materialDTO.setUnit("unit");
        materialDTO.setStock(new BigDecimal("0.00"));
        materialDTO.setUnitName("unitName");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO);
        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value")))
                .thenReturn(materialDTOS);

        // Run the test
        final List<CheckoutDocumentDetailSelectDTO> result = checkoutDocumentServiceImplUnderTest.selectDocumentDetailForUpdate(
                "documentGuid");

        // Verify the results
    }

    @Test
    public void testSelectDocumentDetailForUpdate_CheckoutDocumentDetailMapperReturnsNoItems() {
        // Setup
        when(mockDetailMapper.selectDocumentDetailList("documentGuid")).thenReturn(Collections.emptyList());

        // Configure CheckoutDocumentMapper.selectDocumentStoreGuidAndWarehouseGuid(...).
        final CheckoutDocumentDO checkoutDocumentDO = new CheckoutDocumentDO();
        checkoutDocumentDO.setStoreGuid("storeGuid");
        checkoutDocumentDO.setCreateStaffGuid("createStaffGuid");
        checkoutDocumentDO.setCreateStaffName("createStaffName");
        checkoutDocumentDO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        checkoutDocumentDO.setWarehouseGuid("warehouseGuid");
        checkoutDocumentDO.setWarehouseName("warehouseName");
        checkoutDocumentDO.setStatus(0);
        when(mockCheckoutDocumentMapper.selectDocumentStoreGuidAndWarehouseGuid("documentGuid"))
                .thenReturn(checkoutDocumentDO);

        // Configure IMaterialService.findList(...).
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setUserGuid("createStaffGuid");
        materialDTO.setUserName("createStaffName");
        materialDTO.setGuid("b37cb80f-aeac-4343-ae06-13addcc1ba48");
        materialDTO.setUnit("unit");
        materialDTO.setStock(new BigDecimal("0.00"));
        materialDTO.setUnitName("unitName");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO);
        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value")))
                .thenReturn(materialDTOS);

        // Run the test
        final List<CheckoutDocumentDetailSelectDTO> result = checkoutDocumentServiceImplUnderTest.selectDocumentDetailForUpdate(
                "documentGuid");

        // Verify the results
    }

    @Test
    public void testSelectDocumentDetailForUpdate_IMaterialServiceReturnsNoItems() {
        // Setup
        // Configure CheckoutDocumentDetailMapper.selectDocumentDetailList(...).
        final CheckoutDocumentDetailDO checkoutDocumentDetailDO = new CheckoutDocumentDetailDO();
        checkoutDocumentDetailDO.setMaterialCode("materialCode");
        checkoutDocumentDetailDO.setMaterialGuid("materialGuid");
        checkoutDocumentDetailDO.setMaterialName("materialName");
        checkoutDocumentDetailDO.setStock(new BigDecimal("0.00"));
        checkoutDocumentDetailDO.setUnitGuid("unitGuid");
        checkoutDocumentDetailDO.setUnitName("unitName");
        checkoutDocumentDetailDO.setCheckCount(new BigDecimal("0.00"));
        checkoutDocumentDetailDO.setCheckoutResult(0);
        final List<CheckoutDocumentDetailDO> checkoutDocumentDetailDOS = Arrays.asList(checkoutDocumentDetailDO);
        when(mockDetailMapper.selectDocumentDetailList("documentGuid")).thenReturn(checkoutDocumentDetailDOS);

        // Configure CheckoutDocumentMapper.selectDocumentStoreGuidAndWarehouseGuid(...).
        final CheckoutDocumentDO checkoutDocumentDO = new CheckoutDocumentDO();
        checkoutDocumentDO.setStoreGuid("storeGuid");
        checkoutDocumentDO.setCreateStaffGuid("createStaffGuid");
        checkoutDocumentDO.setCreateStaffName("createStaffName");
        checkoutDocumentDO.setGuid("e7202d9a-8604-4769-af9c-a834cd3aba1b");
        checkoutDocumentDO.setWarehouseGuid("warehouseGuid");
        checkoutDocumentDO.setWarehouseName("warehouseName");
        checkoutDocumentDO.setStatus(0);
        when(mockCheckoutDocumentMapper.selectDocumentStoreGuidAndWarehouseGuid("documentGuid"))
                .thenReturn(checkoutDocumentDO);

        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<CheckoutDocumentDetailSelectDTO> result = checkoutDocumentServiceImplUnderTest.selectDocumentDetailForUpdate(
                "documentGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testSelectCountByWarehouseGuid() {
        // Setup
        when(mockCheckoutDocumentMapper.selectCountByWarehouseGuid("warehouseGuid")).thenReturn(0);

        // Run the test
        final int result = checkoutDocumentServiceImplUnderTest.selectCountByWarehouseGuid("warehouseGuid");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
