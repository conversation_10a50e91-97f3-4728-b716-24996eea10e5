package com.holderzone.erp.service.impl;

import com.holderzone.erp.entity.domain.GoodsOfInventoryDO;
import com.holderzone.erp.service.GoodsService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class GoodsOfInventoryServiceImplTest {

    @Mock
    private GoodsService mockGoodsService;

    @InjectMocks
    private GoodsOfInventoryServiceImpl goodsOfInventoryServiceImplUnderTest;

    @Test
    public void testInsertGoodsOfInventory() {
        // Setup
        final GoodsOfInventoryDO goodsOfInventoryDO = new GoodsOfInventoryDO();
        goodsOfInventoryDO.setId(0L);
        goodsOfInventoryDO.setGuid("3dd0470f-2406-4e28-9e3e-51fb056cbe30");
        goodsOfInventoryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        goodsOfInventoryDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        goodsOfInventoryDO.setInventoryGuid("inventoryGuid");
        final List<GoodsOfInventoryDO> list = Arrays.asList(goodsOfInventoryDO);

        // Run the test
        goodsOfInventoryServiceImplUnderTest.insertGoodsOfInventory(list);

        // Verify the results
    }

    @Test
    public void testQueryGoodsOfInventory() {
        // Setup
        final GoodsOfInventoryDO goodsOfInventoryDO = new GoodsOfInventoryDO();
        goodsOfInventoryDO.setId(0L);
        goodsOfInventoryDO.setGuid("3dd0470f-2406-4e28-9e3e-51fb056cbe30");
        goodsOfInventoryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        goodsOfInventoryDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        goodsOfInventoryDO.setInventoryGuid("inventoryGuid");
        final List<GoodsOfInventoryDO> expectedResult = Arrays.asList(goodsOfInventoryDO);

        // Run the test
        final List<GoodsOfInventoryDO> result = goodsOfInventoryServiceImplUnderTest.queryGoodsOfInventory(
                "inventoryGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
