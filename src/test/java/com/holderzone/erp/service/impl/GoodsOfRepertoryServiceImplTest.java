package com.holderzone.erp.service.impl;

import com.holderzone.erp.dao.GoodsOfRepertoryMapper;
import com.holderzone.erp.entity.domain.GoodsOfRepertoryDO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class GoodsOfRepertoryServiceImplTest {

    @Mock
    private GoodsOfRepertoryMapper mockGoodsOfRepertoryMapper;

    @InjectMocks
    private GoodsOfRepertoryServiceImpl goodsOfRepertoryServiceImplUnderTest;

    @Test
    public void testInsertGoods() {
        // Setup
        final GoodsOfRepertoryDO goodsOfRepertoryDO = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO.setId(0L);
        goodsOfRepertoryDO.setGuid("cbc5efa0-4eda-4704-b12a-8a3813de4dab");
        goodsOfRepertoryDO.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        goodsOfRepertoryDO.setRepertoryGuid("repertoryGuid");

        // Run the test
        goodsOfRepertoryServiceImplUnderTest.insertGoods(goodsOfRepertoryDO);

        // Verify the results
    }

    @Test
    public void testInsertBatchGoods() {
        // Setup
        final GoodsOfRepertoryDO goodsOfRepertoryDO1 = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO1.setId(0L);
        goodsOfRepertoryDO1.setGuid("cbc5efa0-4eda-4704-b12a-8a3813de4dab");
        goodsOfRepertoryDO1.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        goodsOfRepertoryDO1.setRepertoryGuid("repertoryGuid");
        final List<GoodsOfRepertoryDO> goodsOfRepertoryDO = Arrays.asList(goodsOfRepertoryDO1);

        // Run the test
        goodsOfRepertoryServiceImplUnderTest.insertBatchGoods(goodsOfRepertoryDO);

        // Verify the results
    }

    @Test
    public void testQueryGoodsOfRepertory() {
        // Setup
        final GoodsOfRepertoryDO goodsOfRepertoryDO = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO.setId(0L);
        goodsOfRepertoryDO.setGuid("cbc5efa0-4eda-4704-b12a-8a3813de4dab");
        goodsOfRepertoryDO.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        goodsOfRepertoryDO.setRepertoryGuid("repertoryGuid");
        final List<GoodsOfRepertoryDO> expectedResult = Arrays.asList(goodsOfRepertoryDO);

        // Run the test
        final List<GoodsOfRepertoryDO> result = goodsOfRepertoryServiceImplUnderTest.queryGoodsOfRepertory(
                "repertoryGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
