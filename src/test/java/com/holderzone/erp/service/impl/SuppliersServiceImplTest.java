package com.holderzone.erp.service.impl;

import com.holderzone.erp.dao.SuppliersMapper;
import com.holderzone.erp.entity.domain.CategoryDO;
import com.holderzone.erp.entity.domain.MaterialDO;
import com.holderzone.erp.entity.domain.SuppliersDO;
import com.holderzone.erp.entity.domain.SuppliersQueryDO;
import com.holderzone.erp.mapperstruct.ErpModuleMapper;
import com.holderzone.erp.service.InOutDocumentService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SuppliersServiceImplTest {

    @Mock
    private ErpModuleMapper mockModuleMapper;
    @Mock
    private SuppliersMapper mockSuppliersMapper;
    @Mock
    private InOutDocumentService mockInOutDocumentService;

    private SuppliersServiceImpl suppliersServiceImplUnderTest;

    @Before
    public void setUp() {
        suppliersServiceImplUnderTest = new SuppliersServiceImpl(mockModuleMapper, mockSuppliersMapper,
                mockInOutDocumentService);
    }

    @Test
    public void testCreateSuppliers() {
        // Setup
        final SuppliersReqDTO reqDTO = new SuppliersReqDTO();
        reqDTO.setEnabled(0);
        reqDTO.setGuid("guid");
        reqDTO.setName("name");
        reqDTO.setOfficeTel("officeTel");
        reqDTO.setForeignKey("foreignKey");

        when(mockSuppliersMapper.verifyNameRepeat("name", "foreignKey", "guid")).thenReturn(0);

        // Configure ErpModuleMapper.mapToSuppliersDO(...).
        final SuppliersDO suppliersDO = new SuppliersDO();
        suppliersDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO.setGuid("guid");
        suppliersDO.setName("name");
        suppliersDO.setOfficeTel("officeTel");
        when(mockModuleMapper.mapToSuppliersDO(any(SuppliersReqDTO.class))).thenReturn(suppliersDO);

        // Run the test
        final String result = suppliersServiceImplUnderTest.createSuppliers(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockSuppliersMapper).createSuppliers(any(SuppliersDO.class));
    }

    @Test
    public void testUpdateSuppliers() {
        // Setup
        final SuppliersReqDTO reqDTO = new SuppliersReqDTO();
        reqDTO.setEnabled(0);
        reqDTO.setGuid("guid");
        reqDTO.setName("name");
        reqDTO.setOfficeTel("officeTel");
        reqDTO.setForeignKey("foreignKey");

        when(mockSuppliersMapper.verifyNameRepeat("name", "foreignKey", "guid")).thenReturn(0);

        // Configure ErpModuleMapper.mapToSuppliersDO(...).
        final SuppliersDO suppliersDO = new SuppliersDO();
        suppliersDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO.setGuid("guid");
        suppliersDO.setName("name");
        suppliersDO.setOfficeTel("officeTel");
        when(mockModuleMapper.mapToSuppliersDO(any(SuppliersReqDTO.class))).thenReturn(suppliersDO);

        // Run the test
        final String result = suppliersServiceImplUnderTest.updateSuppliers(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo("guid");
        verify(mockSuppliersMapper).updateSuppliers(any(SuppliersDO.class));
    }

    @Test
    public void testGetSuppliersByGuid() {
        // Setup
        // Configure SuppliersMapper.getSuppliersByGuid(...).
        final SuppliersDO suppliersDO = new SuppliersDO();
        suppliersDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO.setGuid("guid");
        suppliersDO.setName("name");
        suppliersDO.setOfficeTel("officeTel");
        when(mockSuppliersMapper.getSuppliersByGuid("aeda82d8-a495-4f2a-b7b9-23c9931a7735")).thenReturn(suppliersDO);

        // Configure ErpModuleMapper.mapToSuppliersDTO(...).
        final SuppliersDTO suppliersDTO = new SuppliersDTO();
        suppliersDTO.setEnabled(0);
        suppliersDTO.setDeleted(0);
        suppliersDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGuid("426d9bc3-efce-4ffa-a823-f80c5a38d0b5");
        when(mockModuleMapper.mapToSuppliersDTO(any(SuppliersDO.class))).thenReturn(suppliersDTO);

        // Run the test
        final SuppliersDTO result = suppliersServiceImplUnderTest.getSuppliersByGuid(
                "aeda82d8-a495-4f2a-b7b9-23c9931a7735");

        // Verify the results
    }

    @Test
    public void testGetSuppliersByGuid_SuppliersMapperReturnsNull() {
        // Setup
        when(mockSuppliersMapper.getSuppliersByGuid("aeda82d8-a495-4f2a-b7b9-23c9931a7735")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> suppliersServiceImplUnderTest.getSuppliersByGuid(
                "aeda82d8-a495-4f2a-b7b9-23c9931a7735")).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testEnableOrDisableSuppliers() {
        // Setup
        // Run the test
        final Boolean result = suppliersServiceImplUnderTest.enableOrDisableSuppliers(
                "c072a1d9-f1f2-44af-9b30-753a1b3fb629");

        // Verify the results
        assertThat(result).isTrue();
        verify(mockSuppliersMapper).enableOrDisableSuppliers("c072a1d9-f1f2-44af-9b30-753a1b3fb629");
    }

    @Test
    public void testDeleteSuppliers() {
        // Setup
        when(mockInOutDocumentService.existDocumentOfSupplier("a217f305-5a82-43d0-a956-df46835ec96c"))
                .thenReturn(false);

        // Run the test
        final Boolean result = suppliersServiceImplUnderTest.deleteSuppliers("a217f305-5a82-43d0-a956-df46835ec96c");

        // Verify the results
        assertThat(result).isTrue();
        verify(mockSuppliersMapper).deleteSuppliers("a217f305-5a82-43d0-a956-df46835ec96c");
    }

    @Test
    public void testDeleteSuppliers_InOutDocumentServiceReturnsTrue() {
        // Setup
        when(mockInOutDocumentService.existDocumentOfSupplier("a217f305-5a82-43d0-a956-df46835ec96c")).thenReturn(true);

        // Run the test
        assertThatThrownBy(() -> suppliersServiceImplUnderTest.deleteSuppliers(
                "a217f305-5a82-43d0-a956-df46835ec96c")).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testGetSuppliersList() {
        // Setup
        final SuppliersQueryDTO queryDTO = new SuppliersQueryDTO();
        queryDTO.setSearchConditions("searchConditions");
        queryDTO.setForeignKey("foreignKey");
        queryDTO.setEnabled(0);

        // Configure ErpModuleMapper.mapToSuppliersQueryDO(...).
        final SuppliersQueryDO suppliersQueryDO = new SuppliersQueryDO();
        suppliersQueryDO.setStart(0);
        suppliersQueryDO.setSearchConditions("searchConditions");
        suppliersQueryDO.setPageSize(0);
        suppliersQueryDO.setCurrentPage(0);
        suppliersQueryDO.setEnabled(0);
        final SuppliersQueryDTO queryDTO1 = new SuppliersQueryDTO();
        queryDTO1.setSearchConditions("searchConditions");
        queryDTO1.setForeignKey("foreignKey");
        queryDTO1.setEnabled(0);
        when(mockModuleMapper.mapToSuppliersQueryDO(queryDTO1)).thenReturn(suppliersQueryDO);

        when(mockSuppliersMapper.getSuppliersListTotal(any(SuppliersQueryDO.class))).thenReturn(0L);

        // Configure SuppliersMapper.getSuppliersList(...).
        final SuppliersDO suppliersDO = new SuppliersDO();
        suppliersDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO.setGuid("guid");
        suppliersDO.setName("name");
        suppliersDO.setOfficeTel("officeTel");
        final List<SuppliersDO> suppliersDOS = Arrays.asList(suppliersDO);
        when(mockSuppliersMapper.getSuppliersList(any(SuppliersQueryDO.class))).thenReturn(suppliersDOS);

        // Configure ErpModuleMapper.mapToSuppliersDtoList(...).
        final SuppliersDTO suppliersDTO = new SuppliersDTO();
        suppliersDTO.setEnabled(0);
        suppliersDTO.setDeleted(0);
        suppliersDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGuid("426d9bc3-efce-4ffa-a823-f80c5a38d0b5");
        final List<SuppliersDTO> suppliersDTOS = Arrays.asList(suppliersDTO);
        final SuppliersDO suppliersDO1 = new SuppliersDO();
        suppliersDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO1.setGuid("guid");
        suppliersDO1.setName("name");
        suppliersDO1.setOfficeTel("officeTel");
        final List<SuppliersDO> suppliersDoList = Arrays.asList(suppliersDO1);
        when(mockModuleMapper.mapToSuppliersDtoList(suppliersDoList)).thenReturn(suppliersDTOS);

        // Run the test
        final Page<SuppliersDTO> result = suppliersServiceImplUnderTest.getSuppliersList(queryDTO);

        // Verify the results
    }

    @Test
    public void testGetSuppliersList_SuppliersMapperGetSuppliersListReturnsNoItems() {
        // Setup
        final SuppliersQueryDTO queryDTO = new SuppliersQueryDTO();
        queryDTO.setSearchConditions("searchConditions");
        queryDTO.setForeignKey("foreignKey");
        queryDTO.setEnabled(0);

        // Configure ErpModuleMapper.mapToSuppliersQueryDO(...).
        final SuppliersQueryDO suppliersQueryDO = new SuppliersQueryDO();
        suppliersQueryDO.setStart(0);
        suppliersQueryDO.setSearchConditions("searchConditions");
        suppliersQueryDO.setPageSize(0);
        suppliersQueryDO.setCurrentPage(0);
        suppliersQueryDO.setEnabled(0);
        final SuppliersQueryDTO queryDTO1 = new SuppliersQueryDTO();
        queryDTO1.setSearchConditions("searchConditions");
        queryDTO1.setForeignKey("foreignKey");
        queryDTO1.setEnabled(0);
        when(mockModuleMapper.mapToSuppliersQueryDO(queryDTO1)).thenReturn(suppliersQueryDO);

        when(mockSuppliersMapper.getSuppliersListTotal(any(SuppliersQueryDO.class))).thenReturn(0L);
        when(mockSuppliersMapper.getSuppliersList(any(SuppliersQueryDO.class))).thenReturn(Collections.emptyList());

        // Configure ErpModuleMapper.mapToSuppliersDtoList(...).
        final SuppliersDTO suppliersDTO = new SuppliersDTO();
        suppliersDTO.setEnabled(0);
        suppliersDTO.setDeleted(0);
        suppliersDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGuid("426d9bc3-efce-4ffa-a823-f80c5a38d0b5");
        final List<SuppliersDTO> suppliersDTOS = Arrays.asList(suppliersDTO);
        final SuppliersDO suppliersDO = new SuppliersDO();
        suppliersDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO.setGuid("guid");
        suppliersDO.setName("name");
        suppliersDO.setOfficeTel("officeTel");
        final List<SuppliersDO> suppliersDoList = Arrays.asList(suppliersDO);
        when(mockModuleMapper.mapToSuppliersDtoList(suppliersDoList)).thenReturn(suppliersDTOS);

        // Run the test
        final Page<SuppliersDTO> result = suppliersServiceImplUnderTest.getSuppliersList(queryDTO);

        // Verify the results
    }

    @Test
    public void testGetSuppliersList_ErpModuleMapperMapToSuppliersDtoListReturnsNoItems() {
        // Setup
        final SuppliersQueryDTO queryDTO = new SuppliersQueryDTO();
        queryDTO.setSearchConditions("searchConditions");
        queryDTO.setForeignKey("foreignKey");
        queryDTO.setEnabled(0);

        // Configure ErpModuleMapper.mapToSuppliersQueryDO(...).
        final SuppliersQueryDO suppliersQueryDO = new SuppliersQueryDO();
        suppliersQueryDO.setStart(0);
        suppliersQueryDO.setSearchConditions("searchConditions");
        suppliersQueryDO.setPageSize(0);
        suppliersQueryDO.setCurrentPage(0);
        suppliersQueryDO.setEnabled(0);
        final SuppliersQueryDTO queryDTO1 = new SuppliersQueryDTO();
        queryDTO1.setSearchConditions("searchConditions");
        queryDTO1.setForeignKey("foreignKey");
        queryDTO1.setEnabled(0);
        when(mockModuleMapper.mapToSuppliersQueryDO(queryDTO1)).thenReturn(suppliersQueryDO);

        when(mockSuppliersMapper.getSuppliersListTotal(any(SuppliersQueryDO.class))).thenReturn(0L);

        // Configure SuppliersMapper.getSuppliersList(...).
        final SuppliersDO suppliersDO = new SuppliersDO();
        suppliersDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO.setGuid("guid");
        suppliersDO.setName("name");
        suppliersDO.setOfficeTel("officeTel");
        final List<SuppliersDO> suppliersDOS = Arrays.asList(suppliersDO);
        when(mockSuppliersMapper.getSuppliersList(any(SuppliersQueryDO.class))).thenReturn(suppliersDOS);

        // Configure ErpModuleMapper.mapToSuppliersDtoList(...).
        final SuppliersDO suppliersDO1 = new SuppliersDO();
        suppliersDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO1.setGuid("guid");
        suppliersDO1.setName("name");
        suppliersDO1.setOfficeTel("officeTel");
        final List<SuppliersDO> suppliersDoList = Arrays.asList(suppliersDO1);
        when(mockModuleMapper.mapToSuppliersDtoList(suppliersDoList)).thenReturn(Collections.emptyList());

        // Run the test
        final Page<SuppliersDTO> result = suppliersServiceImplUnderTest.getSuppliersList(queryDTO);

        // Verify the results
    }

    @Test
    public void testGetAllOfSuppliersList() {
        // Setup
        final SuppliersQueryDTO queryDTO = new SuppliersQueryDTO();
        queryDTO.setSearchConditions("searchConditions");
        queryDTO.setForeignKey("foreignKey");
        queryDTO.setEnabled(0);

        // Configure ErpModuleMapper.mapToSuppliersQueryDO(...).
        final SuppliersQueryDO suppliersQueryDO = new SuppliersQueryDO();
        suppliersQueryDO.setStart(0);
        suppliersQueryDO.setSearchConditions("searchConditions");
        suppliersQueryDO.setPageSize(0);
        suppliersQueryDO.setCurrentPage(0);
        suppliersQueryDO.setEnabled(0);
        final SuppliersQueryDTO queryDTO1 = new SuppliersQueryDTO();
        queryDTO1.setSearchConditions("searchConditions");
        queryDTO1.setForeignKey("foreignKey");
        queryDTO1.setEnabled(0);
        when(mockModuleMapper.mapToSuppliersQueryDO(queryDTO1)).thenReturn(suppliersQueryDO);

        // Configure SuppliersMapper.getSuppliersList(...).
        final SuppliersDO suppliersDO = new SuppliersDO();
        suppliersDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO.setGuid("guid");
        suppliersDO.setName("name");
        suppliersDO.setOfficeTel("officeTel");
        final List<SuppliersDO> suppliersDOS = Arrays.asList(suppliersDO);
        when(mockSuppliersMapper.getSuppliersList(any(SuppliersQueryDO.class))).thenReturn(suppliersDOS);

        // Configure ErpModuleMapper.mapToSuppliersDtoList(...).
        final SuppliersDTO suppliersDTO = new SuppliersDTO();
        suppliersDTO.setEnabled(0);
        suppliersDTO.setDeleted(0);
        suppliersDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGuid("426d9bc3-efce-4ffa-a823-f80c5a38d0b5");
        final List<SuppliersDTO> suppliersDTOS = Arrays.asList(suppliersDTO);
        final SuppliersDO suppliersDO1 = new SuppliersDO();
        suppliersDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO1.setGuid("guid");
        suppliersDO1.setName("name");
        suppliersDO1.setOfficeTel("officeTel");
        final List<SuppliersDO> suppliersDoList = Arrays.asList(suppliersDO1);
        when(mockModuleMapper.mapToSuppliersDtoList(suppliersDoList)).thenReturn(suppliersDTOS);

        // Run the test
        final List<SuppliersDTO> result = suppliersServiceImplUnderTest.getAllOfSuppliersList(queryDTO);

        // Verify the results
    }

    @Test
    public void testGetAllOfSuppliersList_SuppliersMapperReturnsNoItems() {
        // Setup
        final SuppliersQueryDTO queryDTO = new SuppliersQueryDTO();
        queryDTO.setSearchConditions("searchConditions");
        queryDTO.setForeignKey("foreignKey");
        queryDTO.setEnabled(0);

        // Configure ErpModuleMapper.mapToSuppliersQueryDO(...).
        final SuppliersQueryDO suppliersQueryDO = new SuppliersQueryDO();
        suppliersQueryDO.setStart(0);
        suppliersQueryDO.setSearchConditions("searchConditions");
        suppliersQueryDO.setPageSize(0);
        suppliersQueryDO.setCurrentPage(0);
        suppliersQueryDO.setEnabled(0);
        final SuppliersQueryDTO queryDTO1 = new SuppliersQueryDTO();
        queryDTO1.setSearchConditions("searchConditions");
        queryDTO1.setForeignKey("foreignKey");
        queryDTO1.setEnabled(0);
        when(mockModuleMapper.mapToSuppliersQueryDO(queryDTO1)).thenReturn(suppliersQueryDO);

        when(mockSuppliersMapper.getSuppliersList(any(SuppliersQueryDO.class))).thenReturn(Collections.emptyList());

        // Configure ErpModuleMapper.mapToSuppliersDtoList(...).
        final SuppliersDTO suppliersDTO = new SuppliersDTO();
        suppliersDTO.setEnabled(0);
        suppliersDTO.setDeleted(0);
        suppliersDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGuid("426d9bc3-efce-4ffa-a823-f80c5a38d0b5");
        final List<SuppliersDTO> suppliersDTOS = Arrays.asList(suppliersDTO);
        final SuppliersDO suppliersDO = new SuppliersDO();
        suppliersDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO.setGuid("guid");
        suppliersDO.setName("name");
        suppliersDO.setOfficeTel("officeTel");
        final List<SuppliersDO> suppliersDoList = Arrays.asList(suppliersDO);
        when(mockModuleMapper.mapToSuppliersDtoList(suppliersDoList)).thenReturn(suppliersDTOS);

        // Run the test
        final List<SuppliersDTO> result = suppliersServiceImplUnderTest.getAllOfSuppliersList(queryDTO);

        // Verify the results
    }

    @Test
    public void testGetAllOfSuppliersList_ErpModuleMapperMapToSuppliersDtoListReturnsNoItems() {
        // Setup
        final SuppliersQueryDTO queryDTO = new SuppliersQueryDTO();
        queryDTO.setSearchConditions("searchConditions");
        queryDTO.setForeignKey("foreignKey");
        queryDTO.setEnabled(0);

        // Configure ErpModuleMapper.mapToSuppliersQueryDO(...).
        final SuppliersQueryDO suppliersQueryDO = new SuppliersQueryDO();
        suppliersQueryDO.setStart(0);
        suppliersQueryDO.setSearchConditions("searchConditions");
        suppliersQueryDO.setPageSize(0);
        suppliersQueryDO.setCurrentPage(0);
        suppliersQueryDO.setEnabled(0);
        final SuppliersQueryDTO queryDTO1 = new SuppliersQueryDTO();
        queryDTO1.setSearchConditions("searchConditions");
        queryDTO1.setForeignKey("foreignKey");
        queryDTO1.setEnabled(0);
        when(mockModuleMapper.mapToSuppliersQueryDO(queryDTO1)).thenReturn(suppliersQueryDO);

        // Configure SuppliersMapper.getSuppliersList(...).
        final SuppliersDO suppliersDO = new SuppliersDO();
        suppliersDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO.setGuid("guid");
        suppliersDO.setName("name");
        suppliersDO.setOfficeTel("officeTel");
        final List<SuppliersDO> suppliersDOS = Arrays.asList(suppliersDO);
        when(mockSuppliersMapper.getSuppliersList(any(SuppliersQueryDO.class))).thenReturn(suppliersDOS);

        // Configure ErpModuleMapper.mapToSuppliersDtoList(...).
        final SuppliersDO suppliersDO1 = new SuppliersDO();
        suppliersDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO1.setGuid("guid");
        suppliersDO1.setName("name");
        suppliersDO1.setOfficeTel("officeTel");
        final List<SuppliersDO> suppliersDoList = Arrays.asList(suppliersDO1);
        when(mockModuleMapper.mapToSuppliersDtoList(suppliersDoList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<SuppliersDTO> result = suppliersServiceImplUnderTest.getAllOfSuppliersList(queryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetSuppliersMaterialListAll() {
        // Setup
        final SuppliersMaterialQueryDTO queryDTO = new SuppliersMaterialQueryDTO();
        queryDTO.setSuppliersGuid("suppliersGuid");
        queryDTO.setSearchName("searchName");

        // Configure SuppliersMapper.getSuppliersMaterialListAll(...).
        final CategoryDO categoryDO = new CategoryDO();
        categoryDO.setCategoryGuid("categoryGuid");
        categoryDO.setCategoryName("categoryName");
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("a8f8d32b-4d04-41b8-a781-fddd72eb1f1a");
        materialDO.setEnterpriseGuid("enterpriseGuid");
        categoryDO.setMaterialDOList(Arrays.asList(materialDO));
        final List<CategoryDO> categoryDOS = Arrays.asList(categoryDO);
        when(mockSuppliersMapper.getSuppliersMaterialListAll("suppliersGuid", "searchName")).thenReturn(categoryDOS);

        // Configure ErpModuleMapper.mapToCategoryDTO(...).
        final CategoryDTO categoryDTO = new CategoryDTO();
        categoryDTO.setCategoryGuid("categoryGuid");
        categoryDTO.setCategoryName("categoryName");
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("53501fef-d36e-49e5-a90c-9ff62fad5052");
        categoryDTO.setMaterialDTOList(Arrays.asList(materialDTO));
        final List<CategoryDTO> categoryDTOS = Arrays.asList(categoryDTO);
        final CategoryDO categoryDO1 = new CategoryDO();
        categoryDO1.setCategoryGuid("categoryGuid");
        categoryDO1.setCategoryName("categoryName");
        final MaterialDO materialDO1 = new MaterialDO();
        materialDO1.setGuid("a8f8d32b-4d04-41b8-a781-fddd72eb1f1a");
        materialDO1.setEnterpriseGuid("enterpriseGuid");
        categoryDO1.setMaterialDOList(Arrays.asList(materialDO1));
        final List<CategoryDO> categoryDOList = Arrays.asList(categoryDO1);
        when(mockModuleMapper.mapToCategoryDTO(categoryDOList)).thenReturn(categoryDTOS);

        // Run the test
        final List<CategoryDTO> result = suppliersServiceImplUnderTest.getSuppliersMaterialListAll(queryDTO);

        // Verify the results
    }

    @Test
    public void testGetSuppliersMaterialListAll_SuppliersMapperReturnsNoItems() {
        // Setup
        final SuppliersMaterialQueryDTO queryDTO = new SuppliersMaterialQueryDTO();
        queryDTO.setSuppliersGuid("suppliersGuid");
        queryDTO.setSearchName("searchName");

        when(mockSuppliersMapper.getSuppliersMaterialListAll("suppliersGuid", "searchName"))
                .thenReturn(Collections.emptyList());

        // Configure ErpModuleMapper.mapToCategoryDTO(...).
        final CategoryDTO categoryDTO = new CategoryDTO();
        categoryDTO.setCategoryGuid("categoryGuid");
        categoryDTO.setCategoryName("categoryName");
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("53501fef-d36e-49e5-a90c-9ff62fad5052");
        categoryDTO.setMaterialDTOList(Arrays.asList(materialDTO));
        final List<CategoryDTO> categoryDTOS = Arrays.asList(categoryDTO);
        final CategoryDO categoryDO = new CategoryDO();
        categoryDO.setCategoryGuid("categoryGuid");
        categoryDO.setCategoryName("categoryName");
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("a8f8d32b-4d04-41b8-a781-fddd72eb1f1a");
        materialDO.setEnterpriseGuid("enterpriseGuid");
        categoryDO.setMaterialDOList(Arrays.asList(materialDO));
        final List<CategoryDO> categoryDOList = Arrays.asList(categoryDO);
        when(mockModuleMapper.mapToCategoryDTO(categoryDOList)).thenReturn(categoryDTOS);

        // Run the test
        final List<CategoryDTO> result = suppliersServiceImplUnderTest.getSuppliersMaterialListAll(queryDTO);

        // Verify the results
    }

    @Test
    public void testGetSuppliersMaterialListAll_ErpModuleMapperReturnsNoItems() {
        // Setup
        final SuppliersMaterialQueryDTO queryDTO = new SuppliersMaterialQueryDTO();
        queryDTO.setSuppliersGuid("suppliersGuid");
        queryDTO.setSearchName("searchName");

        // Configure SuppliersMapper.getSuppliersMaterialListAll(...).
        final CategoryDO categoryDO = new CategoryDO();
        categoryDO.setCategoryGuid("categoryGuid");
        categoryDO.setCategoryName("categoryName");
        final MaterialDO materialDO = new MaterialDO();
        materialDO.setGuid("a8f8d32b-4d04-41b8-a781-fddd72eb1f1a");
        materialDO.setEnterpriseGuid("enterpriseGuid");
        categoryDO.setMaterialDOList(Arrays.asList(materialDO));
        final List<CategoryDO> categoryDOS = Arrays.asList(categoryDO);
        when(mockSuppliersMapper.getSuppliersMaterialListAll("suppliersGuid", "searchName")).thenReturn(categoryDOS);

        // Configure ErpModuleMapper.mapToCategoryDTO(...).
        final CategoryDO categoryDO1 = new CategoryDO();
        categoryDO1.setCategoryGuid("categoryGuid");
        categoryDO1.setCategoryName("categoryName");
        final MaterialDO materialDO1 = new MaterialDO();
        materialDO1.setGuid("a8f8d32b-4d04-41b8-a781-fddd72eb1f1a");
        materialDO1.setEnterpriseGuid("enterpriseGuid");
        categoryDO1.setMaterialDOList(Arrays.asList(materialDO1));
        final List<CategoryDO> categoryDOList = Arrays.asList(categoryDO1);
        when(mockModuleMapper.mapToCategoryDTO(categoryDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<CategoryDTO> result = suppliersServiceImplUnderTest.getSuppliersMaterialListAll(queryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetSuppliersStatus() {
        // Setup
        // Configure SuppliersMapper.selectSupplierStatus(...).
        final SuppliersDO suppliersDO = new SuppliersDO();
        suppliersDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDO.setGuid("guid");
        suppliersDO.setName("name");
        suppliersDO.setOfficeTel("officeTel");
        when(mockSuppliersMapper.selectSupplierStatus("supplierGuid")).thenReturn(suppliersDO);

        // Configure ErpModuleMapper.mapToSuppliersDTO(...).
        final SuppliersDTO suppliersDTO = new SuppliersDTO();
        suppliersDTO.setEnabled(0);
        suppliersDTO.setDeleted(0);
        suppliersDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGuid("426d9bc3-efce-4ffa-a823-f80c5a38d0b5");
        when(mockModuleMapper.mapToSuppliersDTO(any(SuppliersDO.class))).thenReturn(suppliersDTO);

        // Run the test
        final SuppliersDTO result = suppliersServiceImplUnderTest.getSuppliersStatus("supplierGuid");

        // Verify the results
    }
}
