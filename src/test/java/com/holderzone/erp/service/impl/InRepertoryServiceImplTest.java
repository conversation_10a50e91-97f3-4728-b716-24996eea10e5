package com.holderzone.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.erp.dao.RepertoryMapper;
import com.holderzone.erp.entity.domain.GoodsDO;
import com.holderzone.erp.entity.domain.GoodsOfRepertoryDO;
import com.holderzone.erp.entity.domain.RepertoryDO;
import com.holderzone.erp.mapperstruct.GoodsMapstruct;
import com.holderzone.erp.mapperstruct.RepertoryMapstruct;
import com.holderzone.erp.service.DistributedIdService;
import com.holderzone.erp.service.GoodsOfRepertoryService;
import com.holderzone.erp.service.GoodsSerialService;
import com.holderzone.erp.service.GoodsService;
import com.holderzone.erp.utils.PageAdapter;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.erpretail.InOutGoodsDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.*;
import com.holderzone.saas.store.dto.erp.erpretail.resp.GoodsSumInfoRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.RepertoryDetailInfoRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.RepertoryManageRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class InRepertoryServiceImplTest {

    @Mock
    private RepertoryMapstruct mockRepertoryMapstruct;
    @Mock
    private GoodsMapstruct mockGoodsMapstruct;
    @Mock
    private DistributedIdService mockDistributedIdService;
    @Mock
    private GoodsService mockGoodsService;
    @Mock
    private GoodsOfRepertoryService mockGoodsOfRepertoryService;
    @Mock
    private RepertoryMapper mockRepertoryMapper;
    @Mock
    private GoodsSerialService mockGoodsSerialService;

    @InjectMocks
    private InRepertoryServiceImpl inRepertoryServiceImplUnderTest;

    @Test
    public void testQueryRepertoryDetail() {
        // Setup
        final RepertoryDetailInfoRespDTO expectedResult = new RepertoryDetailInfoRespDTO();
        expectedResult.setInvoiceName("des");
        final InOutGoodsDTO inOutGoodsDTO = new InOutGoodsDTO();
        inOutGoodsDTO.setGoodsGuid("goodsGuid");
        inOutGoodsDTO.setGoodsName("goodsName");
        inOutGoodsDTO.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO.setUnitName("unitName");
        expectedResult.setDetailList(Arrays.asList(inOutGoodsDTO));

        // Configure RepertoryMapstruct.fromRepertoryDO(...).
        final RepertoryDetailInfoRespDTO repertoryDetailInfoRespDTO = new RepertoryDetailInfoRespDTO();
        repertoryDetailInfoRespDTO.setInvoiceName("des");
        final InOutGoodsDTO inOutGoodsDTO1 = new InOutGoodsDTO();
        inOutGoodsDTO1.setGoodsGuid("goodsGuid");
        inOutGoodsDTO1.setGoodsName("goodsName");
        inOutGoodsDTO1.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO1.setUnitName("unitName");
        repertoryDetailInfoRespDTO.setDetailList(Arrays.asList(inOutGoodsDTO1));
        final RepertoryDO repertoryDO = new RepertoryDO();
        repertoryDO.setGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        repertoryDO.setStoreGuid("storeGuid");
        repertoryDO.setInvoiceType(0);
        repertoryDO.setInvoiceNo("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        repertoryDO.setInOut(0);
        repertoryDO.setInvoiceMakeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        repertoryDO.setInvoiceMaker("invoiceMaker");
        repertoryDO.setStatus("1");
        when(mockRepertoryMapstruct.fromRepertoryDO(repertoryDO)).thenReturn(repertoryDetailInfoRespDTO);

        // Configure GoodsOfRepertoryService.queryGoodsOfRepertory(...).
        final GoodsOfRepertoryDO goodsOfRepertoryDO = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO.setGuid("73a931bf-a020-4c01-85a2-f265cbd63ea5");
        goodsOfRepertoryDO.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO.setRepertoryGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        goodsOfRepertoryDO.setCount(new BigDecimal("0.00"));
        goodsOfRepertoryDO.setUnitPrice(new BigDecimal("0.00"));
        final List<GoodsOfRepertoryDO> goodsOfRepertoryDOS = Arrays.asList(goodsOfRepertoryDO);
        when(mockGoodsOfRepertoryService.queryGoodsOfRepertory("repertoryGuid")).thenReturn(goodsOfRepertoryDOS);

        // Configure GoodsMapstruct.fromGoodsListToGoodsOfRepertoryList(...).
        final InOutGoodsDTO inOutGoodsDTO2 = new InOutGoodsDTO();
        inOutGoodsDTO2.setGoodsGuid("goodsGuid");
        inOutGoodsDTO2.setGoodsCode("goodsCode");
        inOutGoodsDTO2.setGoodsName("goodsName");
        inOutGoodsDTO2.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO2.setUnitName("unitName");
        final List<InOutGoodsDTO> inOutGoodsDTOS = Arrays.asList(inOutGoodsDTO2);
        final GoodsOfRepertoryDO goodsOfRepertoryDO1 = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO1.setGuid("73a931bf-a020-4c01-85a2-f265cbd63ea5");
        goodsOfRepertoryDO1.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO1.setRepertoryGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        goodsOfRepertoryDO1.setCount(new BigDecimal("0.00"));
        goodsOfRepertoryDO1.setUnitPrice(new BigDecimal("0.00"));
        final List<GoodsOfRepertoryDO> goodsOfRepertoryDOList = Arrays.asList(goodsOfRepertoryDO1);
        when(mockGoodsMapstruct.fromGoodsListToGoodsOfRepertoryList(goodsOfRepertoryDOList)).thenReturn(inOutGoodsDTOS);

        // Configure GoodsService.getOne(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setId(0L);
        goodsDO.setGuid("546c4dc3-4cfb-43b0-a560-adfec7e37347");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO.setUnitName("unitName");
        when(mockGoodsService.getOne(any(LambdaQueryWrapper.class))).thenReturn(goodsDO);

        // Run the test
        final RepertoryDetailInfoRespDTO result = inRepertoryServiceImplUnderTest.queryRepertoryDetail("repertoryGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryRepertoryDetail_GoodsOfRepertoryServiceReturnsNoItems() {
        // Setup
        final RepertoryDetailInfoRespDTO expectedResult = new RepertoryDetailInfoRespDTO();
        expectedResult.setInvoiceName("des");
        final InOutGoodsDTO inOutGoodsDTO = new InOutGoodsDTO();
        inOutGoodsDTO.setGoodsGuid("goodsGuid");
        inOutGoodsDTO.setGoodsName("goodsName");
        inOutGoodsDTO.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO.setUnitName("unitName");
        expectedResult.setDetailList(Arrays.asList(inOutGoodsDTO));

        // Configure RepertoryMapstruct.fromRepertoryDO(...).
        final RepertoryDetailInfoRespDTO repertoryDetailInfoRespDTO = new RepertoryDetailInfoRespDTO();
        repertoryDetailInfoRespDTO.setInvoiceName("des");
        final InOutGoodsDTO inOutGoodsDTO1 = new InOutGoodsDTO();
        inOutGoodsDTO1.setGoodsGuid("goodsGuid");
        inOutGoodsDTO1.setGoodsName("goodsName");
        inOutGoodsDTO1.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO1.setUnitName("unitName");
        repertoryDetailInfoRespDTO.setDetailList(Arrays.asList(inOutGoodsDTO1));
        final RepertoryDO repertoryDO = new RepertoryDO();
        repertoryDO.setGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        repertoryDO.setStoreGuid("storeGuid");
        repertoryDO.setInvoiceType(0);
        repertoryDO.setInvoiceNo("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        repertoryDO.setInOut(0);
        repertoryDO.setInvoiceMakeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        repertoryDO.setInvoiceMaker("invoiceMaker");
        repertoryDO.setStatus("1");
        when(mockRepertoryMapstruct.fromRepertoryDO(repertoryDO)).thenReturn(repertoryDetailInfoRespDTO);

        when(mockGoodsOfRepertoryService.queryGoodsOfRepertory("repertoryGuid")).thenReturn(Collections.emptyList());

        // Configure GoodsMapstruct.fromGoodsListToGoodsOfRepertoryList(...).
        final InOutGoodsDTO inOutGoodsDTO2 = new InOutGoodsDTO();
        inOutGoodsDTO2.setGoodsGuid("goodsGuid");
        inOutGoodsDTO2.setGoodsCode("goodsCode");
        inOutGoodsDTO2.setGoodsName("goodsName");
        inOutGoodsDTO2.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO2.setUnitName("unitName");
        final List<InOutGoodsDTO> inOutGoodsDTOS = Arrays.asList(inOutGoodsDTO2);
        final GoodsOfRepertoryDO goodsOfRepertoryDO = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO.setGuid("73a931bf-a020-4c01-85a2-f265cbd63ea5");
        goodsOfRepertoryDO.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO.setRepertoryGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        goodsOfRepertoryDO.setCount(new BigDecimal("0.00"));
        goodsOfRepertoryDO.setUnitPrice(new BigDecimal("0.00"));
        final List<GoodsOfRepertoryDO> goodsOfRepertoryDOList = Arrays.asList(goodsOfRepertoryDO);
        when(mockGoodsMapstruct.fromGoodsListToGoodsOfRepertoryList(goodsOfRepertoryDOList)).thenReturn(inOutGoodsDTOS);

        // Configure GoodsService.getOne(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setId(0L);
        goodsDO.setGuid("546c4dc3-4cfb-43b0-a560-adfec7e37347");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO.setUnitName("unitName");
        when(mockGoodsService.getOne(any(LambdaQueryWrapper.class))).thenReturn(goodsDO);

        // Run the test
        final RepertoryDetailInfoRespDTO result = inRepertoryServiceImplUnderTest.queryRepertoryDetail("repertoryGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryRepertoryDetail_GoodsMapstructReturnsNoItems() {
        // Setup
        final RepertoryDetailInfoRespDTO expectedResult = new RepertoryDetailInfoRespDTO();
        expectedResult.setInvoiceName("des");
        final InOutGoodsDTO inOutGoodsDTO = new InOutGoodsDTO();
        inOutGoodsDTO.setGoodsGuid("goodsGuid");
        inOutGoodsDTO.setGoodsName("goodsName");
        inOutGoodsDTO.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO.setUnitName("unitName");
        expectedResult.setDetailList(Arrays.asList(inOutGoodsDTO));

        // Configure RepertoryMapstruct.fromRepertoryDO(...).
        final RepertoryDetailInfoRespDTO repertoryDetailInfoRespDTO = new RepertoryDetailInfoRespDTO();
        repertoryDetailInfoRespDTO.setInvoiceName("des");
        final InOutGoodsDTO inOutGoodsDTO1 = new InOutGoodsDTO();
        inOutGoodsDTO1.setGoodsGuid("goodsGuid");
        inOutGoodsDTO1.setGoodsName("goodsName");
        inOutGoodsDTO1.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO1.setUnitName("unitName");
        repertoryDetailInfoRespDTO.setDetailList(Arrays.asList(inOutGoodsDTO1));
        final RepertoryDO repertoryDO = new RepertoryDO();
        repertoryDO.setGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        repertoryDO.setStoreGuid("storeGuid");
        repertoryDO.setInvoiceType(0);
        repertoryDO.setInvoiceNo("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        repertoryDO.setInOut(0);
        repertoryDO.setInvoiceMakeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        repertoryDO.setInvoiceMaker("invoiceMaker");
        repertoryDO.setStatus("1");
        when(mockRepertoryMapstruct.fromRepertoryDO(repertoryDO)).thenReturn(repertoryDetailInfoRespDTO);

        // Configure GoodsOfRepertoryService.queryGoodsOfRepertory(...).
        final GoodsOfRepertoryDO goodsOfRepertoryDO = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO.setGuid("73a931bf-a020-4c01-85a2-f265cbd63ea5");
        goodsOfRepertoryDO.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO.setRepertoryGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        goodsOfRepertoryDO.setCount(new BigDecimal("0.00"));
        goodsOfRepertoryDO.setUnitPrice(new BigDecimal("0.00"));
        final List<GoodsOfRepertoryDO> goodsOfRepertoryDOS = Arrays.asList(goodsOfRepertoryDO);
        when(mockGoodsOfRepertoryService.queryGoodsOfRepertory("repertoryGuid")).thenReturn(goodsOfRepertoryDOS);

        // Configure GoodsMapstruct.fromGoodsListToGoodsOfRepertoryList(...).
        final GoodsOfRepertoryDO goodsOfRepertoryDO1 = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO1.setGuid("73a931bf-a020-4c01-85a2-f265cbd63ea5");
        goodsOfRepertoryDO1.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO1.setRepertoryGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        goodsOfRepertoryDO1.setCount(new BigDecimal("0.00"));
        goodsOfRepertoryDO1.setUnitPrice(new BigDecimal("0.00"));
        final List<GoodsOfRepertoryDO> goodsOfRepertoryDOList = Arrays.asList(goodsOfRepertoryDO1);
        when(mockGoodsMapstruct.fromGoodsListToGoodsOfRepertoryList(goodsOfRepertoryDOList))
                .thenReturn(Collections.emptyList());

        // Run the test
        final RepertoryDetailInfoRespDTO result = inRepertoryServiceImplUnderTest.queryRepertoryDetail("repertoryGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryRepertoryManageList() {
        // Setup
        final QueryRepertoryManageReqDTO queryRepertoryManageReqDTO = new QueryRepertoryManageReqDTO();
        queryRepertoryManageReqDTO.setStoreGuid("storeGuid");
        queryRepertoryManageReqDTO.setStatus("status");
        queryRepertoryManageReqDTO.setInvoiceType(0);
        queryRepertoryManageReqDTO.setGuid("946d5399-52d7-46b1-a58d-27819e1d7b31");
        queryRepertoryManageReqDTO.setInOut(0);

        // Configure RepertoryMapper.queryInOutRepertoryList(...).
        final QueryRepertoryManageReqDTO queryRepertoryManageReqDTO1 = new QueryRepertoryManageReqDTO();
        queryRepertoryManageReqDTO1.setStoreGuid("storeGuid");
        queryRepertoryManageReqDTO1.setStatus("status");
        queryRepertoryManageReqDTO1.setInvoiceType(0);
        queryRepertoryManageReqDTO1.setGuid("946d5399-52d7-46b1-a58d-27819e1d7b31");
        queryRepertoryManageReqDTO1.setInOut(0);
        when(mockRepertoryMapper.queryInOutRepertoryList(any(PageAdapter.class),
                eq(queryRepertoryManageReqDTO1))).thenReturn(null);

        // Configure RepertoryMapstruct.repertoryDOToRepertoryManageRespDTO(...).
        final RepertoryManageRespDTO repertoryManageRespDTO = new RepertoryManageRespDTO();
        repertoryManageRespDTO.setGuid("84d7263e-b720-4095-9119-57dd238bb973");
        repertoryManageRespDTO.setInvoiceName("des");
        repertoryManageRespDTO.setInvoiceMakeTime("invoiceMakeTime");
        repertoryManageRespDTO.setStatus(0);
        repertoryManageRespDTO.setTotalAmount(new BigDecimal("0.00"));
        final RepertoryDO repertoryDO = new RepertoryDO();
        repertoryDO.setGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        repertoryDO.setStoreGuid("storeGuid");
        repertoryDO.setInvoiceType(0);
        repertoryDO.setInvoiceNo("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        repertoryDO.setInOut(0);
        repertoryDO.setInvoiceMakeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        repertoryDO.setInvoiceMaker("invoiceMaker");
        repertoryDO.setStatus("1");
        when(mockRepertoryMapstruct.repertoryDOToRepertoryManageRespDTO(repertoryDO))
                .thenReturn(repertoryManageRespDTO);

        // Run the test
        final Page<RepertoryManageRespDTO> result = inRepertoryServiceImplUnderTest.queryRepertoryManageList(
                queryRepertoryManageReqDTO);

        // Verify the results
    }

    @Test
    public void testInvalidRepertory() {
        // Setup
        final SingleDataDTO singleDataDTO = new SingleDataDTO("data", Arrays.asList("value"));

        // Configure GoodsOfRepertoryService.queryGoodsOfRepertory(...).
        final GoodsOfRepertoryDO goodsOfRepertoryDO = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO.setGuid("73a931bf-a020-4c01-85a2-f265cbd63ea5");
        goodsOfRepertoryDO.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO.setRepertoryGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        goodsOfRepertoryDO.setCount(new BigDecimal("0.00"));
        goodsOfRepertoryDO.setUnitPrice(new BigDecimal("0.00"));
        final List<GoodsOfRepertoryDO> goodsOfRepertoryDOS = Arrays.asList(goodsOfRepertoryDO);
        when(mockGoodsOfRepertoryService.queryGoodsOfRepertory("data")).thenReturn(goodsOfRepertoryDOS);

        // Configure GoodsService.queryGoods(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setId(0L);
        goodsDO.setGuid("546c4dc3-4cfb-43b0-a560-adfec7e37347");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO.setUnitName("unitName");
        final List<GoodsDO> goodsDOS = Arrays.asList(goodsDO);
        when(mockGoodsService.queryGoods(Arrays.asList("value"))).thenReturn(goodsDOS);

        // Configure GoodsService.saveOrUpdateBatch(...).
        final GoodsDO goodsDO1 = new GoodsDO();
        goodsDO1.setId(0L);
        goodsDO1.setGuid("546c4dc3-4cfb-43b0-a560-adfec7e37347");
        goodsDO1.setGoodsName("goodsName");
        goodsDO1.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO1.setUnitName("unitName");
        final List<GoodsDO> entityList = Arrays.asList(goodsDO1);
        when(mockGoodsService.saveOrUpdateBatch(entityList)).thenReturn(false);

        // Run the test
        final boolean result = inRepertoryServiceImplUnderTest.invalidRepertory(singleDataDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testInvalidRepertory_GoodsOfRepertoryServiceReturnsNoItems() {
        // Setup
        final SingleDataDTO singleDataDTO = new SingleDataDTO("data", Arrays.asList("value"));
        when(mockGoodsOfRepertoryService.queryGoodsOfRepertory("data")).thenReturn(Collections.emptyList());

        // Configure GoodsService.queryGoods(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setId(0L);
        goodsDO.setGuid("546c4dc3-4cfb-43b0-a560-adfec7e37347");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO.setUnitName("unitName");
        final List<GoodsDO> goodsDOS = Arrays.asList(goodsDO);
        when(mockGoodsService.queryGoods(Arrays.asList("value"))).thenReturn(goodsDOS);

        // Configure GoodsService.saveOrUpdateBatch(...).
        final GoodsDO goodsDO1 = new GoodsDO();
        goodsDO1.setId(0L);
        goodsDO1.setGuid("546c4dc3-4cfb-43b0-a560-adfec7e37347");
        goodsDO1.setGoodsName("goodsName");
        goodsDO1.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO1.setUnitName("unitName");
        final List<GoodsDO> entityList = Arrays.asList(goodsDO1);
        when(mockGoodsService.saveOrUpdateBatch(entityList)).thenReturn(false);

        // Run the test
        final boolean result = inRepertoryServiceImplUnderTest.invalidRepertory(singleDataDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testInvalidRepertory_GoodsServiceQueryGoodsReturnsNoItems() {
        // Setup
        final SingleDataDTO singleDataDTO = new SingleDataDTO("data", Arrays.asList("value"));

        // Configure GoodsOfRepertoryService.queryGoodsOfRepertory(...).
        final GoodsOfRepertoryDO goodsOfRepertoryDO = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO.setGuid("73a931bf-a020-4c01-85a2-f265cbd63ea5");
        goodsOfRepertoryDO.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO.setRepertoryGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        goodsOfRepertoryDO.setCount(new BigDecimal("0.00"));
        goodsOfRepertoryDO.setUnitPrice(new BigDecimal("0.00"));
        final List<GoodsOfRepertoryDO> goodsOfRepertoryDOS = Arrays.asList(goodsOfRepertoryDO);
        when(mockGoodsOfRepertoryService.queryGoodsOfRepertory("data")).thenReturn(goodsOfRepertoryDOS);

        when(mockGoodsService.queryGoods(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure GoodsService.saveOrUpdateBatch(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setId(0L);
        goodsDO.setGuid("546c4dc3-4cfb-43b0-a560-adfec7e37347");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO.setUnitName("unitName");
        final List<GoodsDO> entityList = Arrays.asList(goodsDO);
        when(mockGoodsService.saveOrUpdateBatch(entityList)).thenReturn(false);

        // Run the test
        final boolean result = inRepertoryServiceImplUnderTest.invalidRepertory(singleDataDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testInvalidRepertory_GoodsServiceSaveOrUpdateBatchReturnsTrue() {
        // Setup
        final SingleDataDTO singleDataDTO = new SingleDataDTO("data", Arrays.asList("value"));

        // Configure GoodsOfRepertoryService.queryGoodsOfRepertory(...).
        final GoodsOfRepertoryDO goodsOfRepertoryDO = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO.setGuid("73a931bf-a020-4c01-85a2-f265cbd63ea5");
        goodsOfRepertoryDO.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO.setRepertoryGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        goodsOfRepertoryDO.setCount(new BigDecimal("0.00"));
        goodsOfRepertoryDO.setUnitPrice(new BigDecimal("0.00"));
        final List<GoodsOfRepertoryDO> goodsOfRepertoryDOS = Arrays.asList(goodsOfRepertoryDO);
        when(mockGoodsOfRepertoryService.queryGoodsOfRepertory("data")).thenReturn(goodsOfRepertoryDOS);

        // Configure GoodsService.queryGoods(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setId(0L);
        goodsDO.setGuid("546c4dc3-4cfb-43b0-a560-adfec7e37347");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO.setUnitName("unitName");
        final List<GoodsDO> goodsDOS = Arrays.asList(goodsDO);
        when(mockGoodsService.queryGoods(Arrays.asList("value"))).thenReturn(goodsDOS);

        // Configure GoodsService.saveOrUpdateBatch(...).
        final GoodsDO goodsDO1 = new GoodsDO();
        goodsDO1.setId(0L);
        goodsDO1.setGuid("546c4dc3-4cfb-43b0-a560-adfec7e37347");
        goodsDO1.setGoodsName("goodsName");
        goodsDO1.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO1.setUnitName("unitName");
        final List<GoodsDO> entityList = Arrays.asList(goodsDO1);
        when(mockGoodsService.saveOrUpdateBatch(entityList)).thenReturn(true);

        // Run the test
        final boolean result = inRepertoryServiceImplUnderTest.invalidRepertory(singleDataDTO);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testQueryGoodsRepertorySumInfo() {
        // Setup
        final QueryGoodsSumInfoReqDTO queryGoodsSumInfoReqDTO = new QueryGoodsSumInfoReqDTO();
        queryGoodsSumInfoReqDTO.setGoodsClassifyGuid("goodsClassifyGuid");
        queryGoodsSumInfoReqDTO.setNameOrCode("nameOrCode");
        queryGoodsSumInfoReqDTO.setStoreGuid("storeGuid");

        // Configure GoodsService.queryGoods(...).
        final GoodsSumInfoRespDTO goodsSumInfoRespDTO = new GoodsSumInfoRespDTO();
        goodsSumInfoRespDTO.setGoodsGuid("goodsGuid");
        goodsSumInfoRespDTO.setGoodsCode("goodsCode");
        goodsSumInfoRespDTO.setGoodsName("goodsName");
        goodsSumInfoRespDTO.setGoodsClassifyName("goodsClassifyName");
        goodsSumInfoRespDTO.setCount(new BigDecimal("0.00"));
        final Page<GoodsSumInfoRespDTO> goodsSumInfoRespDTOPage = new Page<>(0L, 0L,
                Arrays.asList(goodsSumInfoRespDTO));
        final QueryGoodsSumInfoReqDTO queryGoodsSumInfoReqDTO1 = new QueryGoodsSumInfoReqDTO();
        queryGoodsSumInfoReqDTO1.setGoodsClassifyGuid("goodsClassifyGuid");
        queryGoodsSumInfoReqDTO1.setNameOrCode("nameOrCode");
        queryGoodsSumInfoReqDTO1.setStoreGuid("storeGuid");
        when(mockGoodsService.queryGoods(queryGoodsSumInfoReqDTO1)).thenReturn(goodsSumInfoRespDTOPage);

        // Run the test
        final Page<GoodsSumInfoRespDTO> result = inRepertoryServiceImplUnderTest.queryGoodsRepertorySumInfo(
                queryGoodsSumInfoReqDTO);

        // Verify the results
    }

    @Test
    public void testInsertRepertoryAndDetail() {
        // Setup
        final CreateRepertoryReqDTO createRepertoryReqDTO = new CreateRepertoryReqDTO();
        createRepertoryReqDTO.setStoreGuid("storeGuid");
        createRepertoryReqDTO.setInvoiceType(0);
        createRepertoryReqDTO.setInOut(0);
        final InOutGoodsDTO inOutGoodsDTO = new InOutGoodsDTO();
        inOutGoodsDTO.setGoodsGuid("goodsGuid");
        inOutGoodsDTO.setGoodsName("goodsName");
        inOutGoodsDTO.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO.setUnitName("unitName");
        createRepertoryReqDTO.setDetailList(Arrays.asList(inOutGoodsDTO));

        when(mockDistributedIdService.nextRepertoryGuid()).thenReturn("ed17ab73-ba82-4607-aaad-4626b9cd09c8");

        // Configure RepertoryMapstruct.fromCreateRepertoryReqDTO(...).
        final RepertoryDO repertoryDO = new RepertoryDO();
        repertoryDO.setGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        repertoryDO.setStoreGuid("storeGuid");
        repertoryDO.setInvoiceType(0);
        repertoryDO.setInvoiceNo("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        repertoryDO.setInOut(0);
        repertoryDO.setInvoiceMakeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        repertoryDO.setInvoiceMaker("invoiceMaker");
        repertoryDO.setStatus("1");
        final CreateRepertoryReqDTO inRepertoryDTO = new CreateRepertoryReqDTO();
        inRepertoryDTO.setStoreGuid("storeGuid");
        inRepertoryDTO.setInvoiceType(0);
        inRepertoryDTO.setInOut(0);
        final InOutGoodsDTO inOutGoodsDTO1 = new InOutGoodsDTO();
        inOutGoodsDTO1.setGoodsGuid("goodsGuid");
        inOutGoodsDTO1.setGoodsName("goodsName");
        inOutGoodsDTO1.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO1.setUnitName("unitName");
        inRepertoryDTO.setDetailList(Arrays.asList(inOutGoodsDTO1));
        when(mockRepertoryMapstruct.fromCreateRepertoryReqDTO(inRepertoryDTO)).thenReturn(repertoryDO);

        when(mockDistributedIdService.nextBatchGoodsItemGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure GoodsMapstruct.fromInOutGoodsOfRepertoryDTO(...).
        final GoodsOfRepertoryDO goodsOfRepertoryDO = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO.setGuid("73a931bf-a020-4c01-85a2-f265cbd63ea5");
        goodsOfRepertoryDO.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO.setRepertoryGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        goodsOfRepertoryDO.setCount(new BigDecimal("0.00"));
        goodsOfRepertoryDO.setUnitPrice(new BigDecimal("0.00"));
        final InOutGoodsDTO inOutGoodsDTO2 = new InOutGoodsDTO();
        inOutGoodsDTO2.setGoodsGuid("goodsGuid");
        inOutGoodsDTO2.setGoodsCode("goodsCode");
        inOutGoodsDTO2.setGoodsName("goodsName");
        inOutGoodsDTO2.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO2.setUnitName("unitName");
        when(mockGoodsMapstruct.fromInOutGoodsOfRepertoryDTO(inOutGoodsDTO2)).thenReturn(goodsOfRepertoryDO);

        // Configure GoodsMapstruct.fromGoodsDTOListToGoodsDOList(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setId(0L);
        goodsDO.setGuid("546c4dc3-4cfb-43b0-a560-adfec7e37347");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO.setUnitName("unitName");
        final List<GoodsDO> goodsDOS = Arrays.asList(goodsDO);
        final InOutGoodsDTO inOutGoodsDTO3 = new InOutGoodsDTO();
        inOutGoodsDTO3.setGoodsGuid("goodsGuid");
        inOutGoodsDTO3.setGoodsCode("goodsCode");
        inOutGoodsDTO3.setGoodsName("goodsName");
        inOutGoodsDTO3.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO3.setUnitName("unitName");
        final List<InOutGoodsDTO> inOutGoodsDTOList = Arrays.asList(inOutGoodsDTO3);
        when(mockGoodsMapstruct.fromGoodsDTOListToGoodsDOList(inOutGoodsDTOList)).thenReturn(goodsDOS);

        // Configure GoodsService.insertBatchGoods(...).
        final GoodsDO goodsDO1 = new GoodsDO();
        goodsDO1.setId(0L);
        goodsDO1.setGuid("546c4dc3-4cfb-43b0-a560-adfec7e37347");
        goodsDO1.setGoodsName("goodsName");
        goodsDO1.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO1.setUnitName("unitName");
        final List<GoodsDO> goodsDOS1 = Arrays.asList(goodsDO1);
        when(mockGoodsService.insertBatchGoods(goodsDOS1, 0, "storeGuid")).thenReturn(false);

        // Run the test
        final boolean result = inRepertoryServiceImplUnderTest.insertRepertoryAndDetail(createRepertoryReqDTO);

        // Verify the results
        assertThat(result).isFalse();

        // Confirm GoodsOfRepertoryService.insertBatchGoods(...).
        final GoodsOfRepertoryDO goodsOfRepertoryDO1 = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO1.setGuid("73a931bf-a020-4c01-85a2-f265cbd63ea5");
        goodsOfRepertoryDO1.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO1.setRepertoryGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        goodsOfRepertoryDO1.setCount(new BigDecimal("0.00"));
        goodsOfRepertoryDO1.setUnitPrice(new BigDecimal("0.00"));
        final List<GoodsOfRepertoryDO> goodsOfRepertoryDOS = Arrays.asList(goodsOfRepertoryDO1);
        verify(mockGoodsOfRepertoryService).insertBatchGoods(goodsOfRepertoryDOS);

        // Confirm GoodsSerialService.insertGoodsSerial(...).
        final InsertGoodsSerialReqDTO insertGoodsSerialReqDTO = new InsertGoodsSerialReqDTO();
        insertGoodsSerialReqDTO.setGoodsGuid("goodsGuid");
        insertGoodsSerialReqDTO.setInvoiceType(0);
        insertGoodsSerialReqDTO.setChangeNum(new BigDecimal("0.00"));
        insertGoodsSerialReqDTO.setUnitName("unitName");
        insertGoodsSerialReqDTO.setInvoiceNo("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        final List<InsertGoodsSerialReqDTO> insertGoodsSerialReqDTOList = Arrays.asList(insertGoodsSerialReqDTO);
        verify(mockGoodsSerialService).insertGoodsSerial(insertGoodsSerialReqDTOList);
    }

    @Test
    public void testInsertRepertoryAndDetail_DistributedIdServiceNextBatchGoodsItemGuidReturnsNoItems() {
        // Setup
        final CreateRepertoryReqDTO createRepertoryReqDTO = new CreateRepertoryReqDTO();
        createRepertoryReqDTO.setStoreGuid("storeGuid");
        createRepertoryReqDTO.setInvoiceType(0);
        createRepertoryReqDTO.setInOut(0);
        final InOutGoodsDTO inOutGoodsDTO = new InOutGoodsDTO();
        inOutGoodsDTO.setGoodsGuid("goodsGuid");
        inOutGoodsDTO.setGoodsName("goodsName");
        inOutGoodsDTO.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO.setUnitName("unitName");
        createRepertoryReqDTO.setDetailList(Arrays.asList(inOutGoodsDTO));

        when(mockDistributedIdService.nextRepertoryGuid()).thenReturn("ed17ab73-ba82-4607-aaad-4626b9cd09c8");

        // Configure RepertoryMapstruct.fromCreateRepertoryReqDTO(...).
        final RepertoryDO repertoryDO = new RepertoryDO();
        repertoryDO.setGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        repertoryDO.setStoreGuid("storeGuid");
        repertoryDO.setInvoiceType(0);
        repertoryDO.setInvoiceNo("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        repertoryDO.setInOut(0);
        repertoryDO.setInvoiceMakeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        repertoryDO.setInvoiceMaker("invoiceMaker");
        repertoryDO.setStatus("1");
        final CreateRepertoryReqDTO inRepertoryDTO = new CreateRepertoryReqDTO();
        inRepertoryDTO.setStoreGuid("storeGuid");
        inRepertoryDTO.setInvoiceType(0);
        inRepertoryDTO.setInOut(0);
        final InOutGoodsDTO inOutGoodsDTO1 = new InOutGoodsDTO();
        inOutGoodsDTO1.setGoodsGuid("goodsGuid");
        inOutGoodsDTO1.setGoodsName("goodsName");
        inOutGoodsDTO1.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO1.setUnitName("unitName");
        inRepertoryDTO.setDetailList(Arrays.asList(inOutGoodsDTO1));
        when(mockRepertoryMapstruct.fromCreateRepertoryReqDTO(inRepertoryDTO)).thenReturn(repertoryDO);

        when(mockDistributedIdService.nextBatchGoodsItemGuid(0L)).thenReturn(Collections.emptyList());

        // Configure GoodsMapstruct.fromInOutGoodsOfRepertoryDTO(...).
        final GoodsOfRepertoryDO goodsOfRepertoryDO = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO.setGuid("73a931bf-a020-4c01-85a2-f265cbd63ea5");
        goodsOfRepertoryDO.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO.setRepertoryGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        goodsOfRepertoryDO.setCount(new BigDecimal("0.00"));
        goodsOfRepertoryDO.setUnitPrice(new BigDecimal("0.00"));
        final InOutGoodsDTO inOutGoodsDTO2 = new InOutGoodsDTO();
        inOutGoodsDTO2.setGoodsGuid("goodsGuid");
        inOutGoodsDTO2.setGoodsCode("goodsCode");
        inOutGoodsDTO2.setGoodsName("goodsName");
        inOutGoodsDTO2.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO2.setUnitName("unitName");
        when(mockGoodsMapstruct.fromInOutGoodsOfRepertoryDTO(inOutGoodsDTO2)).thenReturn(goodsOfRepertoryDO);

        // Configure GoodsMapstruct.fromGoodsDTOListToGoodsDOList(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setId(0L);
        goodsDO.setGuid("546c4dc3-4cfb-43b0-a560-adfec7e37347");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO.setUnitName("unitName");
        final List<GoodsDO> goodsDOS = Arrays.asList(goodsDO);
        final InOutGoodsDTO inOutGoodsDTO3 = new InOutGoodsDTO();
        inOutGoodsDTO3.setGoodsGuid("goodsGuid");
        inOutGoodsDTO3.setGoodsCode("goodsCode");
        inOutGoodsDTO3.setGoodsName("goodsName");
        inOutGoodsDTO3.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO3.setUnitName("unitName");
        final List<InOutGoodsDTO> inOutGoodsDTOList = Arrays.asList(inOutGoodsDTO3);
        when(mockGoodsMapstruct.fromGoodsDTOListToGoodsDOList(inOutGoodsDTOList)).thenReturn(goodsDOS);

        // Configure GoodsService.insertBatchGoods(...).
        final GoodsDO goodsDO1 = new GoodsDO();
        goodsDO1.setId(0L);
        goodsDO1.setGuid("546c4dc3-4cfb-43b0-a560-adfec7e37347");
        goodsDO1.setGoodsName("goodsName");
        goodsDO1.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO1.setUnitName("unitName");
        final List<GoodsDO> goodsDOS1 = Arrays.asList(goodsDO1);
        when(mockGoodsService.insertBatchGoods(goodsDOS1, 0, "storeGuid")).thenReturn(false);

        // Run the test
        final boolean result = inRepertoryServiceImplUnderTest.insertRepertoryAndDetail(createRepertoryReqDTO);

        // Verify the results
        assertThat(result).isFalse();

        // Confirm GoodsOfRepertoryService.insertBatchGoods(...).
        final GoodsOfRepertoryDO goodsOfRepertoryDO1 = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO1.setGuid("73a931bf-a020-4c01-85a2-f265cbd63ea5");
        goodsOfRepertoryDO1.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO1.setRepertoryGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        goodsOfRepertoryDO1.setCount(new BigDecimal("0.00"));
        goodsOfRepertoryDO1.setUnitPrice(new BigDecimal("0.00"));
        final List<GoodsOfRepertoryDO> goodsOfRepertoryDOS = Arrays.asList(goodsOfRepertoryDO1);
        verify(mockGoodsOfRepertoryService).insertBatchGoods(goodsOfRepertoryDOS);

        // Confirm GoodsSerialService.insertGoodsSerial(...).
        final InsertGoodsSerialReqDTO insertGoodsSerialReqDTO = new InsertGoodsSerialReqDTO();
        insertGoodsSerialReqDTO.setGoodsGuid("goodsGuid");
        insertGoodsSerialReqDTO.setInvoiceType(0);
        insertGoodsSerialReqDTO.setChangeNum(new BigDecimal("0.00"));
        insertGoodsSerialReqDTO.setUnitName("unitName");
        insertGoodsSerialReqDTO.setInvoiceNo("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        final List<InsertGoodsSerialReqDTO> insertGoodsSerialReqDTOList = Arrays.asList(insertGoodsSerialReqDTO);
        verify(mockGoodsSerialService).insertGoodsSerial(insertGoodsSerialReqDTOList);
    }

    @Test
    public void testInsertRepertoryAndDetail_GoodsMapstructFromGoodsDTOListToGoodsDOListReturnsNoItems() {
        // Setup
        final CreateRepertoryReqDTO createRepertoryReqDTO = new CreateRepertoryReqDTO();
        createRepertoryReqDTO.setStoreGuid("storeGuid");
        createRepertoryReqDTO.setInvoiceType(0);
        createRepertoryReqDTO.setInOut(0);
        final InOutGoodsDTO inOutGoodsDTO = new InOutGoodsDTO();
        inOutGoodsDTO.setGoodsGuid("goodsGuid");
        inOutGoodsDTO.setGoodsName("goodsName");
        inOutGoodsDTO.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO.setUnitName("unitName");
        createRepertoryReqDTO.setDetailList(Arrays.asList(inOutGoodsDTO));

        when(mockDistributedIdService.nextRepertoryGuid()).thenReturn("ed17ab73-ba82-4607-aaad-4626b9cd09c8");

        // Configure RepertoryMapstruct.fromCreateRepertoryReqDTO(...).
        final RepertoryDO repertoryDO = new RepertoryDO();
        repertoryDO.setGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        repertoryDO.setStoreGuid("storeGuid");
        repertoryDO.setInvoiceType(0);
        repertoryDO.setInvoiceNo("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        repertoryDO.setInOut(0);
        repertoryDO.setInvoiceMakeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        repertoryDO.setInvoiceMaker("invoiceMaker");
        repertoryDO.setStatus("1");
        final CreateRepertoryReqDTO inRepertoryDTO = new CreateRepertoryReqDTO();
        inRepertoryDTO.setStoreGuid("storeGuid");
        inRepertoryDTO.setInvoiceType(0);
        inRepertoryDTO.setInOut(0);
        final InOutGoodsDTO inOutGoodsDTO1 = new InOutGoodsDTO();
        inOutGoodsDTO1.setGoodsGuid("goodsGuid");
        inOutGoodsDTO1.setGoodsName("goodsName");
        inOutGoodsDTO1.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO1.setUnitName("unitName");
        inRepertoryDTO.setDetailList(Arrays.asList(inOutGoodsDTO1));
        when(mockRepertoryMapstruct.fromCreateRepertoryReqDTO(inRepertoryDTO)).thenReturn(repertoryDO);

        when(mockDistributedIdService.nextBatchGoodsItemGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure GoodsMapstruct.fromInOutGoodsOfRepertoryDTO(...).
        final GoodsOfRepertoryDO goodsOfRepertoryDO = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO.setGuid("73a931bf-a020-4c01-85a2-f265cbd63ea5");
        goodsOfRepertoryDO.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO.setRepertoryGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        goodsOfRepertoryDO.setCount(new BigDecimal("0.00"));
        goodsOfRepertoryDO.setUnitPrice(new BigDecimal("0.00"));
        final InOutGoodsDTO inOutGoodsDTO2 = new InOutGoodsDTO();
        inOutGoodsDTO2.setGoodsGuid("goodsGuid");
        inOutGoodsDTO2.setGoodsCode("goodsCode");
        inOutGoodsDTO2.setGoodsName("goodsName");
        inOutGoodsDTO2.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO2.setUnitName("unitName");
        when(mockGoodsMapstruct.fromInOutGoodsOfRepertoryDTO(inOutGoodsDTO2)).thenReturn(goodsOfRepertoryDO);

        // Configure GoodsMapstruct.fromGoodsDTOListToGoodsDOList(...).
        final InOutGoodsDTO inOutGoodsDTO3 = new InOutGoodsDTO();
        inOutGoodsDTO3.setGoodsGuid("goodsGuid");
        inOutGoodsDTO3.setGoodsCode("goodsCode");
        inOutGoodsDTO3.setGoodsName("goodsName");
        inOutGoodsDTO3.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO3.setUnitName("unitName");
        final List<InOutGoodsDTO> inOutGoodsDTOList = Arrays.asList(inOutGoodsDTO3);
        when(mockGoodsMapstruct.fromGoodsDTOListToGoodsDOList(inOutGoodsDTOList)).thenReturn(Collections.emptyList());

        // Configure GoodsService.insertBatchGoods(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setId(0L);
        goodsDO.setGuid("546c4dc3-4cfb-43b0-a560-adfec7e37347");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO.setUnitName("unitName");
        final List<GoodsDO> goodsDOS = Arrays.asList(goodsDO);
        when(mockGoodsService.insertBatchGoods(goodsDOS, 0, "storeGuid")).thenReturn(false);

        // Run the test
        final boolean result = inRepertoryServiceImplUnderTest.insertRepertoryAndDetail(createRepertoryReqDTO);

        // Verify the results
        assertThat(result).isFalse();

        // Confirm GoodsOfRepertoryService.insertBatchGoods(...).
        final GoodsOfRepertoryDO goodsOfRepertoryDO1 = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO1.setGuid("73a931bf-a020-4c01-85a2-f265cbd63ea5");
        goodsOfRepertoryDO1.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO1.setRepertoryGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        goodsOfRepertoryDO1.setCount(new BigDecimal("0.00"));
        goodsOfRepertoryDO1.setUnitPrice(new BigDecimal("0.00"));
        final List<GoodsOfRepertoryDO> goodsOfRepertoryDOS = Arrays.asList(goodsOfRepertoryDO1);
        verify(mockGoodsOfRepertoryService).insertBatchGoods(goodsOfRepertoryDOS);

        // Confirm GoodsSerialService.insertGoodsSerial(...).
        final InsertGoodsSerialReqDTO insertGoodsSerialReqDTO = new InsertGoodsSerialReqDTO();
        insertGoodsSerialReqDTO.setGoodsGuid("goodsGuid");
        insertGoodsSerialReqDTO.setInvoiceType(0);
        insertGoodsSerialReqDTO.setChangeNum(new BigDecimal("0.00"));
        insertGoodsSerialReqDTO.setUnitName("unitName");
        insertGoodsSerialReqDTO.setInvoiceNo("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        final List<InsertGoodsSerialReqDTO> insertGoodsSerialReqDTOList = Arrays.asList(insertGoodsSerialReqDTO);
        verify(mockGoodsSerialService).insertGoodsSerial(insertGoodsSerialReqDTOList);
    }

    @Test
    public void testInsertRepertoryAndDetail_GoodsServiceReturnsTrue() {
        // Setup
        final CreateRepertoryReqDTO createRepertoryReqDTO = new CreateRepertoryReqDTO();
        createRepertoryReqDTO.setStoreGuid("storeGuid");
        createRepertoryReqDTO.setInvoiceType(0);
        createRepertoryReqDTO.setInOut(0);
        final InOutGoodsDTO inOutGoodsDTO = new InOutGoodsDTO();
        inOutGoodsDTO.setGoodsGuid("goodsGuid");
        inOutGoodsDTO.setGoodsName("goodsName");
        inOutGoodsDTO.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO.setUnitName("unitName");
        createRepertoryReqDTO.setDetailList(Arrays.asList(inOutGoodsDTO));

        when(mockDistributedIdService.nextRepertoryGuid()).thenReturn("ed17ab73-ba82-4607-aaad-4626b9cd09c8");

        // Configure RepertoryMapstruct.fromCreateRepertoryReqDTO(...).
        final RepertoryDO repertoryDO = new RepertoryDO();
        repertoryDO.setGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        repertoryDO.setStoreGuid("storeGuid");
        repertoryDO.setInvoiceType(0);
        repertoryDO.setInvoiceNo("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        repertoryDO.setInOut(0);
        repertoryDO.setInvoiceMakeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        repertoryDO.setInvoiceMaker("invoiceMaker");
        repertoryDO.setStatus("1");
        final CreateRepertoryReqDTO inRepertoryDTO = new CreateRepertoryReqDTO();
        inRepertoryDTO.setStoreGuid("storeGuid");
        inRepertoryDTO.setInvoiceType(0);
        inRepertoryDTO.setInOut(0);
        final InOutGoodsDTO inOutGoodsDTO1 = new InOutGoodsDTO();
        inOutGoodsDTO1.setGoodsGuid("goodsGuid");
        inOutGoodsDTO1.setGoodsName("goodsName");
        inOutGoodsDTO1.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO1.setUnitName("unitName");
        inRepertoryDTO.setDetailList(Arrays.asList(inOutGoodsDTO1));
        when(mockRepertoryMapstruct.fromCreateRepertoryReqDTO(inRepertoryDTO)).thenReturn(repertoryDO);

        when(mockDistributedIdService.nextBatchGoodsItemGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure GoodsMapstruct.fromInOutGoodsOfRepertoryDTO(...).
        final GoodsOfRepertoryDO goodsOfRepertoryDO = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO.setGuid("73a931bf-a020-4c01-85a2-f265cbd63ea5");
        goodsOfRepertoryDO.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO.setRepertoryGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        goodsOfRepertoryDO.setCount(new BigDecimal("0.00"));
        goodsOfRepertoryDO.setUnitPrice(new BigDecimal("0.00"));
        final InOutGoodsDTO inOutGoodsDTO2 = new InOutGoodsDTO();
        inOutGoodsDTO2.setGoodsGuid("goodsGuid");
        inOutGoodsDTO2.setGoodsCode("goodsCode");
        inOutGoodsDTO2.setGoodsName("goodsName");
        inOutGoodsDTO2.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO2.setUnitName("unitName");
        when(mockGoodsMapstruct.fromInOutGoodsOfRepertoryDTO(inOutGoodsDTO2)).thenReturn(goodsOfRepertoryDO);

        // Configure GoodsMapstruct.fromGoodsDTOListToGoodsDOList(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setId(0L);
        goodsDO.setGuid("546c4dc3-4cfb-43b0-a560-adfec7e37347");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO.setUnitName("unitName");
        final List<GoodsDO> goodsDOS = Arrays.asList(goodsDO);
        final InOutGoodsDTO inOutGoodsDTO3 = new InOutGoodsDTO();
        inOutGoodsDTO3.setGoodsGuid("goodsGuid");
        inOutGoodsDTO3.setGoodsCode("goodsCode");
        inOutGoodsDTO3.setGoodsName("goodsName");
        inOutGoodsDTO3.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO3.setUnitName("unitName");
        final List<InOutGoodsDTO> inOutGoodsDTOList = Arrays.asList(inOutGoodsDTO3);
        when(mockGoodsMapstruct.fromGoodsDTOListToGoodsDOList(inOutGoodsDTOList)).thenReturn(goodsDOS);

        // Configure GoodsService.insertBatchGoods(...).
        final GoodsDO goodsDO1 = new GoodsDO();
        goodsDO1.setId(0L);
        goodsDO1.setGuid("546c4dc3-4cfb-43b0-a560-adfec7e37347");
        goodsDO1.setGoodsName("goodsName");
        goodsDO1.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO1.setUnitName("unitName");
        final List<GoodsDO> goodsDOS1 = Arrays.asList(goodsDO1);
        when(mockGoodsService.insertBatchGoods(goodsDOS1, 0, "storeGuid")).thenReturn(true);

        // Run the test
        final boolean result = inRepertoryServiceImplUnderTest.insertRepertoryAndDetail(createRepertoryReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm GoodsOfRepertoryService.insertBatchGoods(...).
        final GoodsOfRepertoryDO goodsOfRepertoryDO1 = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO1.setGuid("73a931bf-a020-4c01-85a2-f265cbd63ea5");
        goodsOfRepertoryDO1.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO1.setRepertoryGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        goodsOfRepertoryDO1.setCount(new BigDecimal("0.00"));
        goodsOfRepertoryDO1.setUnitPrice(new BigDecimal("0.00"));
        final List<GoodsOfRepertoryDO> goodsOfRepertoryDOS = Arrays.asList(goodsOfRepertoryDO1);
        verify(mockGoodsOfRepertoryService).insertBatchGoods(goodsOfRepertoryDOS);

        // Confirm GoodsSerialService.insertGoodsSerial(...).
        final InsertGoodsSerialReqDTO insertGoodsSerialReqDTO = new InsertGoodsSerialReqDTO();
        insertGoodsSerialReqDTO.setGoodsGuid("goodsGuid");
        insertGoodsSerialReqDTO.setInvoiceType(0);
        insertGoodsSerialReqDTO.setChangeNum(new BigDecimal("0.00"));
        insertGoodsSerialReqDTO.setUnitName("unitName");
        insertGoodsSerialReqDTO.setInvoiceNo("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        final List<InsertGoodsSerialReqDTO> insertGoodsSerialReqDTOList = Arrays.asList(insertGoodsSerialReqDTO);
        verify(mockGoodsSerialService).insertGoodsSerial(insertGoodsSerialReqDTOList);
    }

    @Test
    public void testSaleOutRepertory() {
        // Setup
        final SubstractRepertoryForTradeReqDTO substractRepertoryForTradeReqDTO = new SubstractRepertoryForTradeReqDTO();
        substractRepertoryForTradeReqDTO.setInvoiceType(0);
        substractRepertoryForTradeReqDTO.setInvoiceNo("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        final SubstractGoodsReqDTO substractGoodsReqDTO = new SubstractGoodsReqDTO();
        substractGoodsReqDTO.setGoodsGuid("goodsGuid");
        substractGoodsReqDTO.setCount(new BigDecimal("0.00"));
        substractGoodsReqDTO.setUnitPrice(new BigDecimal("0.00"));
        substractRepertoryForTradeReqDTO.setDetailList(Arrays.asList(substractGoodsReqDTO));

        // Configure GoodsService.list(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setId(0L);
        goodsDO.setGuid("546c4dc3-4cfb-43b0-a560-adfec7e37347");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO.setUnitName("unitName");
        final List<GoodsDO> goodsDOS = Arrays.asList(goodsDO);
        when(mockGoodsService.list(any(LambdaQueryWrapper.class))).thenReturn(goodsDOS);

        when(mockDistributedIdService.nextRepertoryGuid()).thenReturn("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        when(mockDistributedIdService.nextBatchGoodsItemGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure GoodsService.queryGoodsInfo(...).
        final InOutGoodsDTO inOutGoodsDTO = new InOutGoodsDTO();
        inOutGoodsDTO.setGoodsGuid("goodsGuid");
        inOutGoodsDTO.setGoodsCode("goodsCode");
        inOutGoodsDTO.setGoodsName("goodsName");
        inOutGoodsDTO.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO.setUnitName("unitName");
        when(mockGoodsService.queryGoodsInfo("goodsGuid")).thenReturn(inOutGoodsDTO);

        // Configure GoodsService.modifyGoodsRepertoryNum(...).
        final SubstractGoodsReqDTO substractGoodsReqDTO1 = new SubstractGoodsReqDTO();
        substractGoodsReqDTO1.setGoodsGuid("goodsGuid");
        substractGoodsReqDTO1.setCount(new BigDecimal("0.00"));
        substractGoodsReqDTO1.setUnitPrice(new BigDecimal("0.00"));
        final List<SubstractGoodsReqDTO> goodsGuidList = Arrays.asList(substractGoodsReqDTO1);
        when(mockGoodsService.modifyGoodsRepertoryNum(goodsGuidList, 0)).thenReturn(false);

        // Run the test
        final boolean result = inRepertoryServiceImplUnderTest.saleOutRepertory(substractRepertoryForTradeReqDTO);

        // Verify the results
        assertThat(result).isFalse();

        // Confirm GoodsOfRepertoryService.insertBatchGoods(...).
        final GoodsOfRepertoryDO goodsOfRepertoryDO = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO.setGuid("73a931bf-a020-4c01-85a2-f265cbd63ea5");
        goodsOfRepertoryDO.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO.setRepertoryGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        goodsOfRepertoryDO.setCount(new BigDecimal("0.00"));
        goodsOfRepertoryDO.setUnitPrice(new BigDecimal("0.00"));
        final List<GoodsOfRepertoryDO> goodsOfRepertoryDOS = Arrays.asList(goodsOfRepertoryDO);
        verify(mockGoodsOfRepertoryService).insertBatchGoods(goodsOfRepertoryDOS);

        // Confirm GoodsSerialService.insertGoodsSerial(...).
        final InsertGoodsSerialReqDTO insertGoodsSerialReqDTO = new InsertGoodsSerialReqDTO();
        insertGoodsSerialReqDTO.setGoodsGuid("goodsGuid");
        insertGoodsSerialReqDTO.setInvoiceType(0);
        insertGoodsSerialReqDTO.setChangeNum(new BigDecimal("0.00"));
        insertGoodsSerialReqDTO.setUnitName("unitName");
        insertGoodsSerialReqDTO.setInvoiceNo("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        final List<InsertGoodsSerialReqDTO> insertGoodsSerialReqDTOList = Arrays.asList(insertGoodsSerialReqDTO);
        verify(mockGoodsSerialService).insertGoodsSerial(insertGoodsSerialReqDTOList);
    }

    @Test
    public void testSaleOutRepertory_GoodsServiceListReturnsNoItems() {
        // Setup
        final SubstractRepertoryForTradeReqDTO substractRepertoryForTradeReqDTO = new SubstractRepertoryForTradeReqDTO();
        substractRepertoryForTradeReqDTO.setInvoiceType(0);
        substractRepertoryForTradeReqDTO.setInvoiceNo("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        final SubstractGoodsReqDTO substractGoodsReqDTO = new SubstractGoodsReqDTO();
        substractGoodsReqDTO.setGoodsGuid("goodsGuid");
        substractGoodsReqDTO.setCount(new BigDecimal("0.00"));
        substractGoodsReqDTO.setUnitPrice(new BigDecimal("0.00"));
        substractRepertoryForTradeReqDTO.setDetailList(Arrays.asList(substractGoodsReqDTO));

        when(mockGoodsService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockDistributedIdService.nextRepertoryGuid()).thenReturn("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        when(mockDistributedIdService.nextBatchGoodsItemGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure GoodsService.queryGoodsInfo(...).
        final InOutGoodsDTO inOutGoodsDTO = new InOutGoodsDTO();
        inOutGoodsDTO.setGoodsGuid("goodsGuid");
        inOutGoodsDTO.setGoodsCode("goodsCode");
        inOutGoodsDTO.setGoodsName("goodsName");
        inOutGoodsDTO.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO.setUnitName("unitName");
        when(mockGoodsService.queryGoodsInfo("goodsGuid")).thenReturn(inOutGoodsDTO);

        // Configure GoodsService.modifyGoodsRepertoryNum(...).
        final SubstractGoodsReqDTO substractGoodsReqDTO1 = new SubstractGoodsReqDTO();
        substractGoodsReqDTO1.setGoodsGuid("goodsGuid");
        substractGoodsReqDTO1.setCount(new BigDecimal("0.00"));
        substractGoodsReqDTO1.setUnitPrice(new BigDecimal("0.00"));
        final List<SubstractGoodsReqDTO> goodsGuidList = Arrays.asList(substractGoodsReqDTO1);
        when(mockGoodsService.modifyGoodsRepertoryNum(goodsGuidList, 0)).thenReturn(false);

        // Run the test
        final boolean result = inRepertoryServiceImplUnderTest.saleOutRepertory(substractRepertoryForTradeReqDTO);

        // Verify the results
        assertThat(result).isFalse();

        // Confirm GoodsOfRepertoryService.insertBatchGoods(...).
        final GoodsOfRepertoryDO goodsOfRepertoryDO = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO.setGuid("73a931bf-a020-4c01-85a2-f265cbd63ea5");
        goodsOfRepertoryDO.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO.setRepertoryGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        goodsOfRepertoryDO.setCount(new BigDecimal("0.00"));
        goodsOfRepertoryDO.setUnitPrice(new BigDecimal("0.00"));
        final List<GoodsOfRepertoryDO> goodsOfRepertoryDOS = Arrays.asList(goodsOfRepertoryDO);
        verify(mockGoodsOfRepertoryService).insertBatchGoods(goodsOfRepertoryDOS);

        // Confirm GoodsSerialService.insertGoodsSerial(...).
        final InsertGoodsSerialReqDTO insertGoodsSerialReqDTO = new InsertGoodsSerialReqDTO();
        insertGoodsSerialReqDTO.setGoodsGuid("goodsGuid");
        insertGoodsSerialReqDTO.setInvoiceType(0);
        insertGoodsSerialReqDTO.setChangeNum(new BigDecimal("0.00"));
        insertGoodsSerialReqDTO.setUnitName("unitName");
        insertGoodsSerialReqDTO.setInvoiceNo("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        final List<InsertGoodsSerialReqDTO> insertGoodsSerialReqDTOList = Arrays.asList(insertGoodsSerialReqDTO);
        verify(mockGoodsSerialService).insertGoodsSerial(insertGoodsSerialReqDTOList);
    }

    @Test
    public void testSaleOutRepertory_DistributedIdServiceNextBatchGoodsItemGuidReturnsNoItems() {
        // Setup
        final SubstractRepertoryForTradeReqDTO substractRepertoryForTradeReqDTO = new SubstractRepertoryForTradeReqDTO();
        substractRepertoryForTradeReqDTO.setInvoiceType(0);
        substractRepertoryForTradeReqDTO.setInvoiceNo("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        final SubstractGoodsReqDTO substractGoodsReqDTO = new SubstractGoodsReqDTO();
        substractGoodsReqDTO.setGoodsGuid("goodsGuid");
        substractGoodsReqDTO.setCount(new BigDecimal("0.00"));
        substractGoodsReqDTO.setUnitPrice(new BigDecimal("0.00"));
        substractRepertoryForTradeReqDTO.setDetailList(Arrays.asList(substractGoodsReqDTO));

        // Configure GoodsService.list(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setId(0L);
        goodsDO.setGuid("546c4dc3-4cfb-43b0-a560-adfec7e37347");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO.setUnitName("unitName");
        final List<GoodsDO> goodsDOS = Arrays.asList(goodsDO);
        when(mockGoodsService.list(any(LambdaQueryWrapper.class))).thenReturn(goodsDOS);

        when(mockDistributedIdService.nextRepertoryGuid()).thenReturn("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        when(mockDistributedIdService.nextBatchGoodsItemGuid(0L)).thenReturn(Collections.emptyList());

        // Configure GoodsService.queryGoodsInfo(...).
        final InOutGoodsDTO inOutGoodsDTO = new InOutGoodsDTO();
        inOutGoodsDTO.setGoodsGuid("goodsGuid");
        inOutGoodsDTO.setGoodsCode("goodsCode");
        inOutGoodsDTO.setGoodsName("goodsName");
        inOutGoodsDTO.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO.setUnitName("unitName");
        when(mockGoodsService.queryGoodsInfo("goodsGuid")).thenReturn(inOutGoodsDTO);

        // Configure GoodsService.modifyGoodsRepertoryNum(...).
        final SubstractGoodsReqDTO substractGoodsReqDTO1 = new SubstractGoodsReqDTO();
        substractGoodsReqDTO1.setGoodsGuid("goodsGuid");
        substractGoodsReqDTO1.setCount(new BigDecimal("0.00"));
        substractGoodsReqDTO1.setUnitPrice(new BigDecimal("0.00"));
        final List<SubstractGoodsReqDTO> goodsGuidList = Arrays.asList(substractGoodsReqDTO1);
        when(mockGoodsService.modifyGoodsRepertoryNum(goodsGuidList, 0)).thenReturn(false);

        // Run the test
        final boolean result = inRepertoryServiceImplUnderTest.saleOutRepertory(substractRepertoryForTradeReqDTO);

        // Verify the results
        assertThat(result).isFalse();

        // Confirm GoodsOfRepertoryService.insertBatchGoods(...).
        final GoodsOfRepertoryDO goodsOfRepertoryDO = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO.setGuid("73a931bf-a020-4c01-85a2-f265cbd63ea5");
        goodsOfRepertoryDO.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO.setRepertoryGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        goodsOfRepertoryDO.setCount(new BigDecimal("0.00"));
        goodsOfRepertoryDO.setUnitPrice(new BigDecimal("0.00"));
        final List<GoodsOfRepertoryDO> goodsOfRepertoryDOS = Arrays.asList(goodsOfRepertoryDO);
        verify(mockGoodsOfRepertoryService).insertBatchGoods(goodsOfRepertoryDOS);

        // Confirm GoodsSerialService.insertGoodsSerial(...).
        final InsertGoodsSerialReqDTO insertGoodsSerialReqDTO = new InsertGoodsSerialReqDTO();
        insertGoodsSerialReqDTO.setGoodsGuid("goodsGuid");
        insertGoodsSerialReqDTO.setInvoiceType(0);
        insertGoodsSerialReqDTO.setChangeNum(new BigDecimal("0.00"));
        insertGoodsSerialReqDTO.setUnitName("unitName");
        insertGoodsSerialReqDTO.setInvoiceNo("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        final List<InsertGoodsSerialReqDTO> insertGoodsSerialReqDTOList = Arrays.asList(insertGoodsSerialReqDTO);
        verify(mockGoodsSerialService).insertGoodsSerial(insertGoodsSerialReqDTOList);
    }

    @Test
    public void testSaleOutRepertory_GoodsServiceModifyGoodsRepertoryNumReturnsTrue() {
        // Setup
        final SubstractRepertoryForTradeReqDTO substractRepertoryForTradeReqDTO = new SubstractRepertoryForTradeReqDTO();
        substractRepertoryForTradeReqDTO.setInvoiceType(0);
        substractRepertoryForTradeReqDTO.setInvoiceNo("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        final SubstractGoodsReqDTO substractGoodsReqDTO = new SubstractGoodsReqDTO();
        substractGoodsReqDTO.setGoodsGuid("goodsGuid");
        substractGoodsReqDTO.setCount(new BigDecimal("0.00"));
        substractGoodsReqDTO.setUnitPrice(new BigDecimal("0.00"));
        substractRepertoryForTradeReqDTO.setDetailList(Arrays.asList(substractGoodsReqDTO));

        // Configure GoodsService.list(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setId(0L);
        goodsDO.setGuid("546c4dc3-4cfb-43b0-a560-adfec7e37347");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setRemainRepertoryNum(new BigDecimal("0.00"));
        goodsDO.setUnitName("unitName");
        final List<GoodsDO> goodsDOS = Arrays.asList(goodsDO);
        when(mockGoodsService.list(any(LambdaQueryWrapper.class))).thenReturn(goodsDOS);

        when(mockDistributedIdService.nextRepertoryGuid()).thenReturn("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        when(mockDistributedIdService.nextBatchGoodsItemGuid(0L)).thenReturn(Arrays.asList("value"));

        // Configure GoodsService.queryGoodsInfo(...).
        final InOutGoodsDTO inOutGoodsDTO = new InOutGoodsDTO();
        inOutGoodsDTO.setGoodsGuid("goodsGuid");
        inOutGoodsDTO.setGoodsCode("goodsCode");
        inOutGoodsDTO.setGoodsName("goodsName");
        inOutGoodsDTO.setCount(new BigDecimal("0.00"));
        inOutGoodsDTO.setUnitName("unitName");
        when(mockGoodsService.queryGoodsInfo("goodsGuid")).thenReturn(inOutGoodsDTO);

        // Configure GoodsService.modifyGoodsRepertoryNum(...).
        final SubstractGoodsReqDTO substractGoodsReqDTO1 = new SubstractGoodsReqDTO();
        substractGoodsReqDTO1.setGoodsGuid("goodsGuid");
        substractGoodsReqDTO1.setCount(new BigDecimal("0.00"));
        substractGoodsReqDTO1.setUnitPrice(new BigDecimal("0.00"));
        final List<SubstractGoodsReqDTO> goodsGuidList = Arrays.asList(substractGoodsReqDTO1);
        when(mockGoodsService.modifyGoodsRepertoryNum(goodsGuidList, 0)).thenReturn(true);

        // Run the test
        final boolean result = inRepertoryServiceImplUnderTest.saleOutRepertory(substractRepertoryForTradeReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm GoodsOfRepertoryService.insertBatchGoods(...).
        final GoodsOfRepertoryDO goodsOfRepertoryDO = new GoodsOfRepertoryDO();
        goodsOfRepertoryDO.setGuid("73a931bf-a020-4c01-85a2-f265cbd63ea5");
        goodsOfRepertoryDO.setGoodsGuid("goodsGuid");
        goodsOfRepertoryDO.setRepertoryGuid("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        goodsOfRepertoryDO.setCount(new BigDecimal("0.00"));
        goodsOfRepertoryDO.setUnitPrice(new BigDecimal("0.00"));
        final List<GoodsOfRepertoryDO> goodsOfRepertoryDOS = Arrays.asList(goodsOfRepertoryDO);
        verify(mockGoodsOfRepertoryService).insertBatchGoods(goodsOfRepertoryDOS);

        // Confirm GoodsSerialService.insertGoodsSerial(...).
        final InsertGoodsSerialReqDTO insertGoodsSerialReqDTO = new InsertGoodsSerialReqDTO();
        insertGoodsSerialReqDTO.setGoodsGuid("goodsGuid");
        insertGoodsSerialReqDTO.setInvoiceType(0);
        insertGoodsSerialReqDTO.setChangeNum(new BigDecimal("0.00"));
        insertGoodsSerialReqDTO.setUnitName("unitName");
        insertGoodsSerialReqDTO.setInvoiceNo("ed17ab73-ba82-4607-aaad-4626b9cd09c8");
        final List<InsertGoodsSerialReqDTO> insertGoodsSerialReqDTOList = Arrays.asList(insertGoodsSerialReqDTO);
        verify(mockGoodsSerialService).insertGoodsSerial(insertGoodsSerialReqDTOList);
    }
}
