package com.holderzone.erp.service.impl;

import com.holderzone.erp.dao.WarehouseMapper;
import com.holderzone.erp.entity.domain.WarehouseDO;
import com.holderzone.erp.entity.domain.WarehouseQueryDO;
import com.holderzone.erp.mapperstruct.ErpModuleMapper;
import com.holderzone.erp.service.InOutDocumentService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.WarehouseDTO;
import com.holderzone.saas.store.dto.erp.WarehouseQueryDTO;
import com.holderzone.saas.store.dto.erp.WarehouseReqDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WarehouseServiceImplTest {

    @Mock
    private RedisTemplate mockRedisTemplate;
    @Mock
    private ErpModuleMapper mockModuleMapper;
    @Mock
    private WarehouseMapper mockWarehouseMapper;
    @Mock
    private InOutDocumentService mockInOutDocumentService;

    private WarehouseServiceImpl warehouseServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        warehouseServiceImplUnderTest = new WarehouseServiceImpl(mockRedisTemplate, mockModuleMapper,
                mockWarehouseMapper, mockInOutDocumentService);
    }

    @Test
    public void testCreateWarehouse() {
        // Setup
        final WarehouseReqDTO reqDTO = new WarehouseReqDTO();
        reqDTO.setGuid("e95f28a8-9c6b-4e4b-aa18-982c12129e9c");
        reqDTO.setName("name");
        reqDTO.setCode("code");
        reqDTO.setAddr("addr");
        reqDTO.setPic("pic");

        // Configure ErpModuleMapper.mapToWarehouseDO(...).
        final WarehouseDO warehouseDO = new WarehouseDO();
        warehouseDO.setGuid("1992415e-5ad9-490d-8d29-eb1372248353");
        warehouseDO.setName("name");
        warehouseDO.setCode("code");
        warehouseDO.setType(0);
        warehouseDO.setStatus(0);
        warehouseDO.setForeignKey("foreignKey");
        warehouseDO.setEnterpriseGuid("enterpriseGuid");
        when(mockModuleMapper.mapToWarehouseDO(any(WarehouseReqDTO.class))).thenReturn(warehouseDO);

        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockWarehouseMapper.getMaximumCode()).thenReturn("result");
        when(mockRedisTemplate.getConnectionFactory()).thenReturn(null);
        when(mockWarehouseMapper.checkCodeRepeat("code")).thenReturn(0);
        when(mockWarehouseMapper.checkNameRepeat("name", "1992415e-5ad9-490d-8d29-eb1372248353")).thenReturn(0);

        // Run the test
        final String result = warehouseServiceImplUnderTest.createWarehouse(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockWarehouseMapper).createWarehouse(any(WarehouseDO.class));
    }

    @Test
    public void testCreateWarehouse_RedisTemplateGetConnectionFactoryReturnsNull() {
        // Setup
        final WarehouseReqDTO reqDTO = new WarehouseReqDTO();
        reqDTO.setGuid("e95f28a8-9c6b-4e4b-aa18-982c12129e9c");
        reqDTO.setName("name");
        reqDTO.setCode("code");
        reqDTO.setAddr("addr");
        reqDTO.setPic("pic");

        // Configure ErpModuleMapper.mapToWarehouseDO(...).
        final WarehouseDO warehouseDO = new WarehouseDO();
        warehouseDO.setGuid("1992415e-5ad9-490d-8d29-eb1372248353");
        warehouseDO.setName("name");
        warehouseDO.setCode("code");
        warehouseDO.setType(0);
        warehouseDO.setStatus(0);
        warehouseDO.setForeignKey("foreignKey");
        warehouseDO.setEnterpriseGuid("enterpriseGuid");
        when(mockModuleMapper.mapToWarehouseDO(any(WarehouseReqDTO.class))).thenReturn(warehouseDO);

        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockWarehouseMapper.getMaximumCode()).thenReturn("result");
        when(mockRedisTemplate.getConnectionFactory()).thenReturn(null);
        when(mockWarehouseMapper.checkCodeRepeat("code")).thenReturn(0);
        when(mockWarehouseMapper.checkNameRepeat("name", "1992415e-5ad9-490d-8d29-eb1372248353")).thenReturn(0);

        // Run the test
        final String result = warehouseServiceImplUnderTest.createWarehouse(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockWarehouseMapper).createWarehouse(any(WarehouseDO.class));
    }

    @Test
    public void testGetWarehouseByGuid() {
        // Setup
        // Configure WarehouseMapper.getWarehouseByGuid(...).
        final WarehouseDO warehouseDO = new WarehouseDO();
        warehouseDO.setGuid("1992415e-5ad9-490d-8d29-eb1372248353");
        warehouseDO.setName("name");
        warehouseDO.setCode("code");
        warehouseDO.setType(0);
        warehouseDO.setStatus(0);
        warehouseDO.setForeignKey("foreignKey");
        warehouseDO.setEnterpriseGuid("enterpriseGuid");
        when(mockWarehouseMapper.getWarehouseByGuid("2fd1b2aa-23bf-48d8-8f64-55f5bdfdff70")).thenReturn(warehouseDO);

        // Configure ErpModuleMapper.mapToWarehouseDTO(...).
        final WarehouseDTO warehouseDTO = new WarehouseDTO();
        warehouseDTO.setGuid("a1e68b1a-3fed-4064-bcb3-4d66a8951da3");
        warehouseDTO.setName("name");
        warehouseDTO.setCode("code");
        warehouseDTO.setAddr("addr");
        warehouseDTO.setPic("pic");
        when(mockModuleMapper.mapToWarehouseDTO(any(WarehouseDO.class))).thenReturn(warehouseDTO);

        // Run the test
        final WarehouseDTO result = warehouseServiceImplUnderTest.getWarehouseByGuid(
                "2fd1b2aa-23bf-48d8-8f64-55f5bdfdff70");

        // Verify the results
    }

    @Test
    public void testGetWarehouseByGuid_WarehouseMapperReturnsNull() {
        // Setup
        when(mockWarehouseMapper.getWarehouseByGuid("2fd1b2aa-23bf-48d8-8f64-55f5bdfdff70")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> warehouseServiceImplUnderTest.getWarehouseByGuid(
                "2fd1b2aa-23bf-48d8-8f64-55f5bdfdff70")).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testGetWarehouseByStoreGuid() {
        // Setup
        // Configure WarehouseMapper.getWarehouseByStoreGuid(...).
        final WarehouseDO warehouseDO = new WarehouseDO();
        warehouseDO.setGuid("1992415e-5ad9-490d-8d29-eb1372248353");
        warehouseDO.setName("name");
        warehouseDO.setCode("code");
        warehouseDO.setType(0);
        warehouseDO.setStatus(0);
        warehouseDO.setForeignKey("foreignKey");
        warehouseDO.setEnterpriseGuid("enterpriseGuid");
        final List<WarehouseDO> warehouseDOS = Arrays.asList(warehouseDO);
        when(mockWarehouseMapper.getWarehouseByStoreGuid("storeGuid")).thenReturn(warehouseDOS);

        // Configure ErpModuleMapper.mapToWarehouseDtoList(...).
        final WarehouseDTO warehouseDTO = new WarehouseDTO();
        warehouseDTO.setGuid("a1e68b1a-3fed-4064-bcb3-4d66a8951da3");
        warehouseDTO.setName("name");
        warehouseDTO.setCode("code");
        warehouseDTO.setAddr("addr");
        warehouseDTO.setPic("pic");
        final List<WarehouseDTO> warehouseDTOS = Arrays.asList(warehouseDTO);
        final WarehouseDO warehouseDO1 = new WarehouseDO();
        warehouseDO1.setGuid("1992415e-5ad9-490d-8d29-eb1372248353");
        warehouseDO1.setName("name");
        warehouseDO1.setCode("code");
        warehouseDO1.setType(0);
        warehouseDO1.setStatus(0);
        warehouseDO1.setForeignKey("foreignKey");
        warehouseDO1.setEnterpriseGuid("enterpriseGuid");
        final List<WarehouseDO> warehouseDoList = Arrays.asList(warehouseDO1);
        when(mockModuleMapper.mapToWarehouseDtoList(warehouseDoList)).thenReturn(warehouseDTOS);

        // Run the test
        final List<WarehouseDTO> result = warehouseServiceImplUnderTest.getWarehouseByStoreGuid("storeGuid");

        // Verify the results
    }

    @Test
    public void testGetWarehouseByStoreGuid_WarehouseMapperReturnsNoItems() {
        // Setup
        when(mockWarehouseMapper.getWarehouseByStoreGuid("storeGuid")).thenReturn(Collections.emptyList());

        // Configure ErpModuleMapper.mapToWarehouseDtoList(...).
        final WarehouseDTO warehouseDTO = new WarehouseDTO();
        warehouseDTO.setGuid("a1e68b1a-3fed-4064-bcb3-4d66a8951da3");
        warehouseDTO.setName("name");
        warehouseDTO.setCode("code");
        warehouseDTO.setAddr("addr");
        warehouseDTO.setPic("pic");
        final List<WarehouseDTO> warehouseDTOS = Arrays.asList(warehouseDTO);
        final WarehouseDO warehouseDO = new WarehouseDO();
        warehouseDO.setGuid("1992415e-5ad9-490d-8d29-eb1372248353");
        warehouseDO.setName("name");
        warehouseDO.setCode("code");
        warehouseDO.setType(0);
        warehouseDO.setStatus(0);
        warehouseDO.setForeignKey("foreignKey");
        warehouseDO.setEnterpriseGuid("enterpriseGuid");
        final List<WarehouseDO> warehouseDoList = Arrays.asList(warehouseDO);
        when(mockModuleMapper.mapToWarehouseDtoList(warehouseDoList)).thenReturn(warehouseDTOS);

        // Run the test
        final List<WarehouseDTO> result = warehouseServiceImplUnderTest.getWarehouseByStoreGuid("storeGuid");

        // Verify the results
    }

    @Test
    public void testGetWarehouseByStoreGuid_ErpModuleMapperReturnsNoItems() {
        // Setup
        // Configure WarehouseMapper.getWarehouseByStoreGuid(...).
        final WarehouseDO warehouseDO = new WarehouseDO();
        warehouseDO.setGuid("1992415e-5ad9-490d-8d29-eb1372248353");
        warehouseDO.setName("name");
        warehouseDO.setCode("code");
        warehouseDO.setType(0);
        warehouseDO.setStatus(0);
        warehouseDO.setForeignKey("foreignKey");
        warehouseDO.setEnterpriseGuid("enterpriseGuid");
        final List<WarehouseDO> warehouseDOS = Arrays.asList(warehouseDO);
        when(mockWarehouseMapper.getWarehouseByStoreGuid("storeGuid")).thenReturn(warehouseDOS);

        // Configure ErpModuleMapper.mapToWarehouseDtoList(...).
        final WarehouseDO warehouseDO1 = new WarehouseDO();
        warehouseDO1.setGuid("1992415e-5ad9-490d-8d29-eb1372248353");
        warehouseDO1.setName("name");
        warehouseDO1.setCode("code");
        warehouseDO1.setType(0);
        warehouseDO1.setStatus(0);
        warehouseDO1.setForeignKey("foreignKey");
        warehouseDO1.setEnterpriseGuid("enterpriseGuid");
        final List<WarehouseDO> warehouseDoList = Arrays.asList(warehouseDO1);
        when(mockModuleMapper.mapToWarehouseDtoList(warehouseDoList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<WarehouseDTO> result = warehouseServiceImplUnderTest.getWarehouseByStoreGuid("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testUpdateWarehouse() {
        // Setup
        final WarehouseReqDTO reqDTO = new WarehouseReqDTO();
        reqDTO.setGuid("e95f28a8-9c6b-4e4b-aa18-982c12129e9c");
        reqDTO.setName("name");
        reqDTO.setCode("code");
        reqDTO.setAddr("addr");
        reqDTO.setPic("pic");

        // Configure ErpModuleMapper.mapToWarehouseDO(...).
        final WarehouseDO warehouseDO = new WarehouseDO();
        warehouseDO.setGuid("1992415e-5ad9-490d-8d29-eb1372248353");
        warehouseDO.setName("name");
        warehouseDO.setCode("code");
        warehouseDO.setType(0);
        warehouseDO.setStatus(0);
        warehouseDO.setForeignKey("foreignKey");
        warehouseDO.setEnterpriseGuid("enterpriseGuid");
        when(mockModuleMapper.mapToWarehouseDO(any(WarehouseReqDTO.class))).thenReturn(warehouseDO);

        when(mockWarehouseMapper.checkNameRepeat("name", "1992415e-5ad9-490d-8d29-eb1372248353")).thenReturn(0);

        // Run the test
        final String result = warehouseServiceImplUnderTest.updateWarehouse(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo("1992415e-5ad9-490d-8d29-eb1372248353");
        verify(mockWarehouseMapper).updateWarehouse(any(WarehouseDO.class));
    }

    @Test
    public void testGetWarehouseList() {
        // Setup
        final WarehouseQueryDTO queryDTO = new WarehouseQueryDTO();
        queryDTO.setSearchConditions("searchConditions");

        // Configure ErpModuleMapper.mapToWarehouseQueryDO(...).
        final WarehouseQueryDO warehouseQueryDO = new WarehouseQueryDO();
        warehouseQueryDO.setList(Arrays.asList("value"));
        warehouseQueryDO.setSearchConditions("searchConditions");
        warehouseQueryDO.setPageSize(0);
        warehouseQueryDO.setCurrentPage(0);
        warehouseQueryDO.setStart(0);
        final WarehouseQueryDTO queryDTO1 = new WarehouseQueryDTO();
        queryDTO1.setSearchConditions("searchConditions");
        when(mockModuleMapper.mapToWarehouseQueryDO(queryDTO1)).thenReturn(warehouseQueryDO);

        when(mockWarehouseMapper.getWarehouseListTotal(any(WarehouseQueryDO.class))).thenReturn(0L);

        // Configure WarehouseMapper.getWarehouseList(...).
        final WarehouseDO warehouseDO = new WarehouseDO();
        warehouseDO.setGuid("1992415e-5ad9-490d-8d29-eb1372248353");
        warehouseDO.setName("name");
        warehouseDO.setCode("code");
        warehouseDO.setType(0);
        warehouseDO.setStatus(0);
        warehouseDO.setForeignKey("foreignKey");
        warehouseDO.setEnterpriseGuid("enterpriseGuid");
        final List<WarehouseDO> warehouseDOS = Arrays.asList(warehouseDO);
        when(mockWarehouseMapper.getWarehouseList(any(WarehouseQueryDO.class))).thenReturn(warehouseDOS);

        // Configure ErpModuleMapper.mapToWarehouseDtoList(...).
        final WarehouseDTO warehouseDTO = new WarehouseDTO();
        warehouseDTO.setGuid("a1e68b1a-3fed-4064-bcb3-4d66a8951da3");
        warehouseDTO.setName("name");
        warehouseDTO.setCode("code");
        warehouseDTO.setAddr("addr");
        warehouseDTO.setPic("pic");
        final List<WarehouseDTO> warehouseDTOS = Arrays.asList(warehouseDTO);
        final WarehouseDO warehouseDO1 = new WarehouseDO();
        warehouseDO1.setGuid("1992415e-5ad9-490d-8d29-eb1372248353");
        warehouseDO1.setName("name");
        warehouseDO1.setCode("code");
        warehouseDO1.setType(0);
        warehouseDO1.setStatus(0);
        warehouseDO1.setForeignKey("foreignKey");
        warehouseDO1.setEnterpriseGuid("enterpriseGuid");
        final List<WarehouseDO> warehouseDoList = Arrays.asList(warehouseDO1);
        when(mockModuleMapper.mapToWarehouseDtoList(warehouseDoList)).thenReturn(warehouseDTOS);

        // Run the test
        final Page<WarehouseDTO> result = warehouseServiceImplUnderTest.getWarehouseList(queryDTO);

        // Verify the results
    }

    @Test
    public void testGetWarehouseList_WarehouseMapperGetWarehouseListReturnsNoItems() {
        // Setup
        final WarehouseQueryDTO queryDTO = new WarehouseQueryDTO();
        queryDTO.setSearchConditions("searchConditions");

        // Configure ErpModuleMapper.mapToWarehouseQueryDO(...).
        final WarehouseQueryDO warehouseQueryDO = new WarehouseQueryDO();
        warehouseQueryDO.setList(Arrays.asList("value"));
        warehouseQueryDO.setSearchConditions("searchConditions");
        warehouseQueryDO.setPageSize(0);
        warehouseQueryDO.setCurrentPage(0);
        warehouseQueryDO.setStart(0);
        final WarehouseQueryDTO queryDTO1 = new WarehouseQueryDTO();
        queryDTO1.setSearchConditions("searchConditions");
        when(mockModuleMapper.mapToWarehouseQueryDO(queryDTO1)).thenReturn(warehouseQueryDO);

        when(mockWarehouseMapper.getWarehouseListTotal(any(WarehouseQueryDO.class))).thenReturn(0L);
        when(mockWarehouseMapper.getWarehouseList(any(WarehouseQueryDO.class))).thenReturn(Collections.emptyList());

        // Configure ErpModuleMapper.mapToWarehouseDtoList(...).
        final WarehouseDTO warehouseDTO = new WarehouseDTO();
        warehouseDTO.setGuid("a1e68b1a-3fed-4064-bcb3-4d66a8951da3");
        warehouseDTO.setName("name");
        warehouseDTO.setCode("code");
        warehouseDTO.setAddr("addr");
        warehouseDTO.setPic("pic");
        final List<WarehouseDTO> warehouseDTOS = Arrays.asList(warehouseDTO);
        final WarehouseDO warehouseDO = new WarehouseDO();
        warehouseDO.setGuid("1992415e-5ad9-490d-8d29-eb1372248353");
        warehouseDO.setName("name");
        warehouseDO.setCode("code");
        warehouseDO.setType(0);
        warehouseDO.setStatus(0);
        warehouseDO.setForeignKey("foreignKey");
        warehouseDO.setEnterpriseGuid("enterpriseGuid");
        final List<WarehouseDO> warehouseDoList = Arrays.asList(warehouseDO);
        when(mockModuleMapper.mapToWarehouseDtoList(warehouseDoList)).thenReturn(warehouseDTOS);

        // Run the test
        final Page<WarehouseDTO> result = warehouseServiceImplUnderTest.getWarehouseList(queryDTO);

        // Verify the results
    }

    @Test
    public void testGetWarehouseList_ErpModuleMapperMapToWarehouseDtoListReturnsNoItems() {
        // Setup
        final WarehouseQueryDTO queryDTO = new WarehouseQueryDTO();
        queryDTO.setSearchConditions("searchConditions");

        // Configure ErpModuleMapper.mapToWarehouseQueryDO(...).
        final WarehouseQueryDO warehouseQueryDO = new WarehouseQueryDO();
        warehouseQueryDO.setList(Arrays.asList("value"));
        warehouseQueryDO.setSearchConditions("searchConditions");
        warehouseQueryDO.setPageSize(0);
        warehouseQueryDO.setCurrentPage(0);
        warehouseQueryDO.setStart(0);
        final WarehouseQueryDTO queryDTO1 = new WarehouseQueryDTO();
        queryDTO1.setSearchConditions("searchConditions");
        when(mockModuleMapper.mapToWarehouseQueryDO(queryDTO1)).thenReturn(warehouseQueryDO);

        when(mockWarehouseMapper.getWarehouseListTotal(any(WarehouseQueryDO.class))).thenReturn(0L);

        // Configure WarehouseMapper.getWarehouseList(...).
        final WarehouseDO warehouseDO = new WarehouseDO();
        warehouseDO.setGuid("1992415e-5ad9-490d-8d29-eb1372248353");
        warehouseDO.setName("name");
        warehouseDO.setCode("code");
        warehouseDO.setType(0);
        warehouseDO.setStatus(0);
        warehouseDO.setForeignKey("foreignKey");
        warehouseDO.setEnterpriseGuid("enterpriseGuid");
        final List<WarehouseDO> warehouseDOS = Arrays.asList(warehouseDO);
        when(mockWarehouseMapper.getWarehouseList(any(WarehouseQueryDO.class))).thenReturn(warehouseDOS);

        // Configure ErpModuleMapper.mapToWarehouseDtoList(...).
        final WarehouseDO warehouseDO1 = new WarehouseDO();
        warehouseDO1.setGuid("1992415e-5ad9-490d-8d29-eb1372248353");
        warehouseDO1.setName("name");
        warehouseDO1.setCode("code");
        warehouseDO1.setType(0);
        warehouseDO1.setStatus(0);
        warehouseDO1.setForeignKey("foreignKey");
        warehouseDO1.setEnterpriseGuid("enterpriseGuid");
        final List<WarehouseDO> warehouseDoList = Arrays.asList(warehouseDO1);
        when(mockModuleMapper.mapToWarehouseDtoList(warehouseDoList)).thenReturn(Collections.emptyList());

        // Run the test
        final Page<WarehouseDTO> result = warehouseServiceImplUnderTest.getWarehouseList(queryDTO);

        // Verify the results
    }

    @Test
    public void testGetWarehouseListByName() {
        // Setup
        final WarehouseQueryDTO queryDTO = new WarehouseQueryDTO();
        queryDTO.setSearchConditions("searchConditions");

        // Configure ErpModuleMapper.mapToWarehouseQueryDO(...).
        final WarehouseQueryDO warehouseQueryDO = new WarehouseQueryDO();
        warehouseQueryDO.setList(Arrays.asList("value"));
        warehouseQueryDO.setSearchConditions("searchConditions");
        warehouseQueryDO.setPageSize(0);
        warehouseQueryDO.setCurrentPage(0);
        warehouseQueryDO.setStart(0);
        final WarehouseQueryDTO queryDTO1 = new WarehouseQueryDTO();
        queryDTO1.setSearchConditions("searchConditions");
        when(mockModuleMapper.mapToWarehouseQueryDO(queryDTO1)).thenReturn(warehouseQueryDO);

        // Configure WarehouseMapper.getWarehouseListByName(...).
        final WarehouseDO warehouseDO = new WarehouseDO();
        warehouseDO.setGuid("1992415e-5ad9-490d-8d29-eb1372248353");
        warehouseDO.setName("name");
        warehouseDO.setCode("code");
        warehouseDO.setType(0);
        warehouseDO.setStatus(0);
        warehouseDO.setForeignKey("foreignKey");
        warehouseDO.setEnterpriseGuid("enterpriseGuid");
        final List<WarehouseDO> warehouseDOS = Arrays.asList(warehouseDO);
        when(mockWarehouseMapper.getWarehouseListByName(any(WarehouseQueryDO.class))).thenReturn(warehouseDOS);

        // Configure ErpModuleMapper.mapToWarehouseDtoList(...).
        final WarehouseDTO warehouseDTO = new WarehouseDTO();
        warehouseDTO.setGuid("a1e68b1a-3fed-4064-bcb3-4d66a8951da3");
        warehouseDTO.setName("name");
        warehouseDTO.setCode("code");
        warehouseDTO.setAddr("addr");
        warehouseDTO.setPic("pic");
        final List<WarehouseDTO> warehouseDTOS = Arrays.asList(warehouseDTO);
        final WarehouseDO warehouseDO1 = new WarehouseDO();
        warehouseDO1.setGuid("1992415e-5ad9-490d-8d29-eb1372248353");
        warehouseDO1.setName("name");
        warehouseDO1.setCode("code");
        warehouseDO1.setType(0);
        warehouseDO1.setStatus(0);
        warehouseDO1.setForeignKey("foreignKey");
        warehouseDO1.setEnterpriseGuid("enterpriseGuid");
        final List<WarehouseDO> warehouseDoList = Arrays.asList(warehouseDO1);
        when(mockModuleMapper.mapToWarehouseDtoList(warehouseDoList)).thenReturn(warehouseDTOS);

        // Run the test
        final List<WarehouseDTO> result = warehouseServiceImplUnderTest.getWarehouseListByName(queryDTO);

        // Verify the results
    }

    @Test
    public void testGetWarehouseListByName_WarehouseMapperReturnsNoItems() {
        // Setup
        final WarehouseQueryDTO queryDTO = new WarehouseQueryDTO();
        queryDTO.setSearchConditions("searchConditions");

        // Configure ErpModuleMapper.mapToWarehouseQueryDO(...).
        final WarehouseQueryDO warehouseQueryDO = new WarehouseQueryDO();
        warehouseQueryDO.setList(Arrays.asList("value"));
        warehouseQueryDO.setSearchConditions("searchConditions");
        warehouseQueryDO.setPageSize(0);
        warehouseQueryDO.setCurrentPage(0);
        warehouseQueryDO.setStart(0);
        final WarehouseQueryDTO queryDTO1 = new WarehouseQueryDTO();
        queryDTO1.setSearchConditions("searchConditions");
        when(mockModuleMapper.mapToWarehouseQueryDO(queryDTO1)).thenReturn(warehouseQueryDO);

        when(mockWarehouseMapper.getWarehouseListByName(any(WarehouseQueryDO.class)))
                .thenReturn(Collections.emptyList());

        // Configure ErpModuleMapper.mapToWarehouseDtoList(...).
        final WarehouseDTO warehouseDTO = new WarehouseDTO();
        warehouseDTO.setGuid("a1e68b1a-3fed-4064-bcb3-4d66a8951da3");
        warehouseDTO.setName("name");
        warehouseDTO.setCode("code");
        warehouseDTO.setAddr("addr");
        warehouseDTO.setPic("pic");
        final List<WarehouseDTO> warehouseDTOS = Arrays.asList(warehouseDTO);
        final WarehouseDO warehouseDO = new WarehouseDO();
        warehouseDO.setGuid("1992415e-5ad9-490d-8d29-eb1372248353");
        warehouseDO.setName("name");
        warehouseDO.setCode("code");
        warehouseDO.setType(0);
        warehouseDO.setStatus(0);
        warehouseDO.setForeignKey("foreignKey");
        warehouseDO.setEnterpriseGuid("enterpriseGuid");
        final List<WarehouseDO> warehouseDoList = Arrays.asList(warehouseDO);
        when(mockModuleMapper.mapToWarehouseDtoList(warehouseDoList)).thenReturn(warehouseDTOS);

        // Run the test
        final List<WarehouseDTO> result = warehouseServiceImplUnderTest.getWarehouseListByName(queryDTO);

        // Verify the results
    }

    @Test
    public void testGetWarehouseListByName_ErpModuleMapperMapToWarehouseDtoListReturnsNoItems() {
        // Setup
        final WarehouseQueryDTO queryDTO = new WarehouseQueryDTO();
        queryDTO.setSearchConditions("searchConditions");

        // Configure ErpModuleMapper.mapToWarehouseQueryDO(...).
        final WarehouseQueryDO warehouseQueryDO = new WarehouseQueryDO();
        warehouseQueryDO.setList(Arrays.asList("value"));
        warehouseQueryDO.setSearchConditions("searchConditions");
        warehouseQueryDO.setPageSize(0);
        warehouseQueryDO.setCurrentPage(0);
        warehouseQueryDO.setStart(0);
        final WarehouseQueryDTO queryDTO1 = new WarehouseQueryDTO();
        queryDTO1.setSearchConditions("searchConditions");
        when(mockModuleMapper.mapToWarehouseQueryDO(queryDTO1)).thenReturn(warehouseQueryDO);

        // Configure WarehouseMapper.getWarehouseListByName(...).
        final WarehouseDO warehouseDO = new WarehouseDO();
        warehouseDO.setGuid("1992415e-5ad9-490d-8d29-eb1372248353");
        warehouseDO.setName("name");
        warehouseDO.setCode("code");
        warehouseDO.setType(0);
        warehouseDO.setStatus(0);
        warehouseDO.setForeignKey("foreignKey");
        warehouseDO.setEnterpriseGuid("enterpriseGuid");
        final List<WarehouseDO> warehouseDOS = Arrays.asList(warehouseDO);
        when(mockWarehouseMapper.getWarehouseListByName(any(WarehouseQueryDO.class))).thenReturn(warehouseDOS);

        // Configure ErpModuleMapper.mapToWarehouseDtoList(...).
        final WarehouseDO warehouseDO1 = new WarehouseDO();
        warehouseDO1.setGuid("1992415e-5ad9-490d-8d29-eb1372248353");
        warehouseDO1.setName("name");
        warehouseDO1.setCode("code");
        warehouseDO1.setType(0);
        warehouseDO1.setStatus(0);
        warehouseDO1.setForeignKey("foreignKey");
        warehouseDO1.setEnterpriseGuid("enterpriseGuid");
        final List<WarehouseDO> warehouseDoList = Arrays.asList(warehouseDO1);
        when(mockModuleMapper.mapToWarehouseDtoList(warehouseDoList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<WarehouseDTO> result = warehouseServiceImplUnderTest.getWarehouseListByName(queryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testEnableOrDisableWarehouse() {
        // Setup
        // Run the test
        final Boolean result = warehouseServiceImplUnderTest.enableOrDisableWarehouse(
                "afd6e1c4-257e-427f-ab71-ce800aac5d60");

        // Verify the results
        assertThat(result).isTrue();
        verify(mockWarehouseMapper).enableOrDisableWarehouse("afd6e1c4-257e-427f-ab71-ce800aac5d60");
    }

    @Test
    public void testLockOrUnlockWarehouse() {
        // Setup
        // Run the test
        final Boolean result = warehouseServiceImplUnderTest.lockOrUnlockWarehouse(
                "06c4d251-311e-49c4-9f9e-e328ecb35404");

        // Verify the results
        assertThat(result).isTrue();
        verify(mockWarehouseMapper).lockOrUnlockWarehouse("06c4d251-311e-49c4-9f9e-e328ecb35404");
    }

    @Test
    public void testDeleteWarehouse() {
        // Setup
        when(mockInOutDocumentService.existDocumentOfWarehouse("fa6f0de7-4d0d-4c82-8cab-e7d53d92456e"))
                .thenReturn(false);

        // Run the test
        final Boolean result = warehouseServiceImplUnderTest.deleteWarehouse("fa6f0de7-4d0d-4c82-8cab-e7d53d92456e");

        // Verify the results
        assertThat(result).isTrue();
        verify(mockWarehouseMapper).deleteWarehouse("fa6f0de7-4d0d-4c82-8cab-e7d53d92456e");
    }

    @Test
    public void testDeleteWarehouse_InOutDocumentServiceReturnsTrue() {
        // Setup
        when(mockInOutDocumentService.existDocumentOfWarehouse("fa6f0de7-4d0d-4c82-8cab-e7d53d92456e"))
                .thenReturn(true);

        // Run the test
        assertThatThrownBy(() -> warehouseServiceImplUnderTest.deleteWarehouse(
                "fa6f0de7-4d0d-4c82-8cab-e7d53d92456e")).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testIsLock() {
        // Setup
        // Configure WarehouseMapper.getWarehouseByGuid(...).
        final WarehouseDO warehouseDO = new WarehouseDO();
        warehouseDO.setGuid("1992415e-5ad9-490d-8d29-eb1372248353");
        warehouseDO.setName("name");
        warehouseDO.setCode("code");
        warehouseDO.setType(0);
        warehouseDO.setStatus(0);
        warehouseDO.setForeignKey("foreignKey");
        warehouseDO.setEnterpriseGuid("enterpriseGuid");
        when(mockWarehouseMapper.getWarehouseByGuid("afa9c02f-c914-4c8a-95bd-40e522d58d0e")).thenReturn(warehouseDO);

        // Run the test
        final Boolean result = warehouseServiceImplUnderTest.isLock("afa9c02f-c914-4c8a-95bd-40e522d58d0e");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testWarehouseCode() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockWarehouseMapper.getMaximumCode()).thenReturn("result");
        when(mockRedisTemplate.getConnectionFactory()).thenReturn(null);

        // Run the test
        final String result = warehouseServiceImplUnderTest.warehouseCode();

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testWarehouseCode_RedisTemplateGetConnectionFactoryReturnsNull() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockWarehouseMapper.getMaximumCode()).thenReturn("result");
        when(mockRedisTemplate.getConnectionFactory()).thenReturn(null);

        // Run the test
        final String result = warehouseServiceImplUnderTest.warehouseCode();

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testUpdateStoreWarehouse() {
        // Setup
        final WarehouseReqDTO reqDTO = new WarehouseReqDTO();
        reqDTO.setGuid("e95f28a8-9c6b-4e4b-aa18-982c12129e9c");
        reqDTO.setName("name");
        reqDTO.setCode("code");
        reqDTO.setAddr("addr");
        reqDTO.setPic("pic");

        // Configure ErpModuleMapper.mapToWarehouseDO(...).
        final WarehouseDO warehouseDO = new WarehouseDO();
        warehouseDO.setGuid("1992415e-5ad9-490d-8d29-eb1372248353");
        warehouseDO.setName("name");
        warehouseDO.setCode("code");
        warehouseDO.setType(0);
        warehouseDO.setStatus(0);
        warehouseDO.setForeignKey("foreignKey");
        warehouseDO.setEnterpriseGuid("enterpriseGuid");
        when(mockModuleMapper.mapToWarehouseDO(any(WarehouseReqDTO.class))).thenReturn(warehouseDO);

        when(mockWarehouseMapper.checkNameRepeat("name", "1992415e-5ad9-490d-8d29-eb1372248353")).thenReturn(0);

        // Run the test
        final String result = warehouseServiceImplUnderTest.updateStoreWarehouse(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo("1992415e-5ad9-490d-8d29-eb1372248353");
        verify(mockWarehouseMapper).updateStoreWarehouse("name", "foreignKey");
    }
}
