package com.holderzone.erp.service.impl;

import com.holderzone.erp.dao.InOutDocumentDetailMapper;
import com.holderzone.erp.dao.InOutDocumentMapper;
import com.holderzone.erp.entity.bo.*;
import com.holderzone.erp.entity.domain.*;
import com.holderzone.erp.event.publisher.OrderUpdateStockPublisher;
import com.holderzone.erp.service.*;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class InOutDocumentServiceImplTest {

    @Mock
    private InOutDocumentMapper mockInOutDocumentMapper;
    @Mock
    private InOutDocumentDetailMapper mockDetailMapper;
    @Mock
    private RedissonClient mockRedisson;
    @Mock
    private IMaterialService mockMaterialService;
    @Mock
    private MaterialStockService mockMaterialStockService;
    @Mock
    private PricingSchemesService mockPricingSchemesService;
    @Mock
    private InOutDocumentService mockInOutDocumentService;
    @Mock
    private SuppliersService mockSuppliersService;
    @Mock
    private CheckoutDocumentService mockCheckoutDocumentService;
    @Mock
    private OrderUpdateStockPublisher mockPublisher;

    @InjectMocks
    private InOutDocumentServiceImpl inOutDocumentServiceImplUnderTest;

    @Test
    public void testInsertInOutDocumentAndDetail() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("documentGuid");
        inOutDocumentDTO.setType(0);

        // Configure IMaterialService.unitAdapterMain(...).
        final List<UnitConvertBO> unitConvertBOS = Arrays.asList(
                new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")));
        when(mockMaterialService.unitAdapterMain(
                Arrays.asList(new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")))))
                .thenReturn(unitConvertBOS);

        // Run the test
        inOutDocumentServiceImplUnderTest.insertInOutDocumentAndDetail(inOutDocumentDTO);

        // Verify the results
        verify(mockInOutDocumentMapper).insertInOutDocument(any(InOutDocumentDO.class));

        // Confirm InOutDocumentDetailMapper.insertInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailDOList = Arrays.asList(inOutDocumentDetailDO);
        verify(mockDetailMapper).insertInOutDocumentDetailList(detailDOList);
    }

    @Test
    public void testInsertInOutDocumentAndDetail_IMaterialServiceReturnsNoItems() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("documentGuid");
        inOutDocumentDTO.setType(0);

        when(mockMaterialService.unitAdapterMain(
                Arrays.asList(new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")))))
                .thenReturn(Collections.emptyList());

        // Run the test
        inOutDocumentServiceImplUnderTest.insertInOutDocumentAndDetail(inOutDocumentDTO);

        // Verify the results
        verify(mockInOutDocumentMapper).insertInOutDocument(any(InOutDocumentDO.class));

        // Confirm InOutDocumentDetailMapper.insertInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailDOList = Arrays.asList(inOutDocumentDetailDO);
        verify(mockDetailMapper).insertInOutDocumentDetailList(detailDOList);
    }

    @Test
    public void testDocumentDetailOutCountValidate() {
        // Setup
        final InOutDocumentDetailBO inOutDocumentDetailBO = new InOutDocumentDetailBO();
        inOutDocumentDetailBO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailBO.setMaterialGuid("materialGuid");
        inOutDocumentDetailBO.setMaterialName("materialName");
        inOutDocumentDetailBO.setUnitPrice(new BigDecimal("0.00"));
        inOutDocumentDetailBO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDetailBO.setCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailBO> detailBOList = Arrays.asList(inOutDocumentDetailBO);

        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(inOutDocumentDO);

        // Configure InOutDocumentDetailMapper.selectDocumentInAndReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectDocumentInAndReturnCount("contactDocumentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Run the test
        inOutDocumentServiceImplUnderTest.documentDetailOutCountValidate("contactDocumentGuid", detailBOList);

        // Verify the results
    }

    @Test
    public void testDocumentDetailOutCountValidate_InOutDocumentMapperReturnsNull() {
        // Setup
        final InOutDocumentDetailBO inOutDocumentDetailBO = new InOutDocumentDetailBO();
        inOutDocumentDetailBO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailBO.setMaterialGuid("materialGuid");
        inOutDocumentDetailBO.setMaterialName("materialName");
        inOutDocumentDetailBO.setUnitPrice(new BigDecimal("0.00"));
        inOutDocumentDetailBO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDetailBO.setCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailBO> detailBOList = Arrays.asList(inOutDocumentDetailBO);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inOutDocumentServiceImplUnderTest.documentDetailOutCountValidate("contactDocumentGuid",
                detailBOList)).isInstanceOf(ParameterException.class);
    }

    @Test
    public void testDocumentDetailOutCountValidate_InOutDocumentDetailMapperReturnsNoItems() {
        // Setup
        final InOutDocumentDetailBO inOutDocumentDetailBO = new InOutDocumentDetailBO();
        inOutDocumentDetailBO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailBO.setMaterialGuid("materialGuid");
        inOutDocumentDetailBO.setMaterialName("materialName");
        inOutDocumentDetailBO.setUnitPrice(new BigDecimal("0.00"));
        inOutDocumentDetailBO.setTotalAmount(new BigDecimal("0.00"));
        inOutDocumentDetailBO.setCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailBO> detailBOList = Arrays.asList(inOutDocumentDetailBO);

        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(inOutDocumentDO);

        when(mockDetailMapper.selectDocumentInAndReturnCount("contactDocumentGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        inOutDocumentServiceImplUnderTest.documentDetailOutCountValidate("contactDocumentGuid", detailBOList);

        // Verify the results
    }

    @Test
    public void testInDocumentStatusValidate() {
        // Setup
        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(inOutDocumentDO);

        // Run the test
        inOutDocumentServiceImplUnderTest.inDocumentStatusValidate("contactDocumentGuid");

        // Verify the results
    }

    @Test
    public void testInDocumentStatusValidate_InOutDocumentMapperReturnsNull() {
        // Setup
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(null);

        // Run the test
        assertThatThrownBy(
                () -> inOutDocumentServiceImplUnderTest.inDocumentStatusValidate("contactDocumentGuid"))
                .isInstanceOf(ParameterException.class);
    }

    @Test
    public void testInsertInOutDocument() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("documentGuid");
        inOutDocumentDTO.setType(0);

        // Run the test
        inOutDocumentServiceImplUnderTest.insertInOutDocument(inOutDocumentDTO);

        // Verify the results
        verify(mockInOutDocumentMapper).insertInOutDocument(any(InOutDocumentDO.class));
    }

    @Test
    public void testInsertInOutDocumentDetail() {
        // Setup
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setStock(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialGuid("materialGuid");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialCode("materialCode");
        final List<InOutDocumentDetailAddOrUpdateDTO> detailList = Arrays.asList(inOutDocumentDetailAddOrUpdateDTO);

        // Configure IMaterialService.unitAdapterMain(...).
        final List<UnitConvertBO> unitConvertBOS = Arrays.asList(
                new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")));
        when(mockMaterialService.unitAdapterMain(
                Arrays.asList(new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")))))
                .thenReturn(unitConvertBOS);

        // Run the test
        inOutDocumentServiceImplUnderTest.insertInOutDocumentDetail(detailList);

        // Verify the results
        // Confirm InOutDocumentDetailMapper.insertInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailDOList = Arrays.asList(inOutDocumentDetailDO);
        verify(mockDetailMapper).insertInOutDocumentDetailList(detailDOList);
    }

    @Test
    public void testInsertInOutDocumentDetail_IMaterialServiceReturnsNoItems() {
        // Setup
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setStock(new BigDecimal("0.00"));
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialGuid("materialGuid");
        inOutDocumentDetailAddOrUpdateDTO.setMaterialCode("materialCode");
        final List<InOutDocumentDetailAddOrUpdateDTO> detailList = Arrays.asList(inOutDocumentDetailAddOrUpdateDTO);
        when(mockMaterialService.unitAdapterMain(
                Arrays.asList(new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")))))
                .thenReturn(Collections.emptyList());

        // Run the test
        inOutDocumentServiceImplUnderTest.insertInOutDocumentDetail(detailList);

        // Verify the results
        // Confirm InOutDocumentDetailMapper.insertInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailDOList = Arrays.asList(inOutDocumentDetailDO);
        verify(mockDetailMapper).insertInOutDocumentDetailList(detailDOList);
    }

    @Test
    public void testUpdateInOutDocumentAndDetail() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("documentGuid");
        inOutDocumentDTO.setType(0);

        // Configure IMaterialService.unitAdapterMain(...).
        final List<UnitConvertBO> unitConvertBOS = Arrays.asList(
                new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")));
        when(mockMaterialService.unitAdapterMain(
                Arrays.asList(new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")))))
                .thenReturn(unitConvertBOS);

        // Run the test
        inOutDocumentServiceImplUnderTest.updateInOutDocumentAndDetail(inOutDocumentDTO);

        // Verify the results
        verify(mockInOutDocumentMapper).deleteInOutDocumentAndDetail("documentGuid");
        verify(mockInOutDocumentMapper).insertInOutDocument(any(InOutDocumentDO.class));

        // Confirm InOutDocumentDetailMapper.insertInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailDOList = Arrays.asList(inOutDocumentDetailDO);
        verify(mockDetailMapper).insertInOutDocumentDetailList(detailDOList);
    }

    @Test
    public void testUpdateInOutDocumentAndDetail_IMaterialServiceReturnsNoItems() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("documentGuid");
        inOutDocumentDTO.setType(0);

        when(mockMaterialService.unitAdapterMain(
                Arrays.asList(new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")))))
                .thenReturn(Collections.emptyList());

        // Run the test
        inOutDocumentServiceImplUnderTest.updateInOutDocumentAndDetail(inOutDocumentDTO);

        // Verify the results
        verify(mockInOutDocumentMapper).deleteInOutDocumentAndDetail("documentGuid");
        verify(mockInOutDocumentMapper).insertInOutDocument(any(InOutDocumentDO.class));

        // Confirm InOutDocumentDetailMapper.insertInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailDOList = Arrays.asList(inOutDocumentDetailDO);
        verify(mockDetailMapper).insertInOutDocumentDetailList(detailDOList);
    }

    @Test
    public void testDeleteInOutDocumentAndDetail() {
        // Setup
        // Run the test
        inOutDocumentServiceImplUnderTest.deleteInOutDocumentAndDetail("documentGuid");

        // Verify the results
        verify(mockInOutDocumentMapper).deleteInOutDocumentAndDetail("documentGuid");
    }

    @Test
    public void testSelectStoreMaterialInfo() {
        // Setup
        // Configure IMaterialService.findList(...).
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setInUnitPrice(new BigDecimal("0.00"));
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("ff07141f-7642-49cb-907e-b17415b52509");
        materialDTO.setEnterpriseGuid("enterpriseGuid");
        materialDTO.setProperty("property");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO);
        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value")))
                .thenReturn(materialDTOS);

        // Run the test
        final List<InOutDocumentMaterialInfoBO> result = inOutDocumentServiceImplUnderTest.selectStoreMaterialInfo(
                "storeGuid", "warehouseGuid", Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testSelectStoreMaterialInfo_IMaterialServiceReturnsNull() {
        // Setup
        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value"))).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inOutDocumentServiceImplUnderTest.selectStoreMaterialInfo("storeGuid", "warehouseGuid",
                Arrays.asList("value"))).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSelectStoreMaterialInfo_IMaterialServiceReturnsNoItems() {
        // Setup
        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> inOutDocumentServiceImplUnderTest.selectStoreMaterialInfo("storeGuid", "warehouseGuid",
                Arrays.asList("value"))).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSelectSupplierMaterialUnitPrice() {
        // Setup
        // Configure PricingSchemesService.batchQueryPricingSchemesList(...).
        final PricingSchemesDTO pricingSchemesDTO = new PricingSchemesDTO();
        pricingSchemesDTO.setMaterialName("materialName");
        final MaterialUnitDTO materialUnitDTO = new MaterialUnitDTO();
        materialUnitDTO.setGuid("d19ed052-cf51-4dfc-ae5e-d07df414b205");
        materialUnitDTO.setName("name");
        materialUnitDTO.setCode("code");
        pricingSchemesDTO.setUnitList(Arrays.asList(materialUnitDTO));
        final List<PricingSchemesDTO> pricingSchemesDTOS = Arrays.asList(pricingSchemesDTO);
        when(mockPricingSchemesService.batchQueryPricingSchemesList(any(PricingSchemesQueryDTO.class)))
                .thenReturn(pricingSchemesDTOS);

        // Run the test
        final List<InOutDocumentMaterialUnitPriceBO> result = inOutDocumentServiceImplUnderTest.selectSupplierMaterialUnitPrice(
                "supplierGuid", Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testSelectSupplierMaterialUnitPrice_PricingSchemesServiceReturnsNoItems() {
        // Setup
        when(mockPricingSchemesService.batchQueryPricingSchemesList(any(PricingSchemesQueryDTO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<InOutDocumentMaterialUnitPriceBO> result = inOutDocumentServiceImplUnderTest.selectSupplierMaterialUnitPrice(
                "supplierGuid", Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testSelectMaterialListForAdd() {
        // Setup
        final InOutDocumentDetailQueryDTO materialQueryDTO = new InOutDocumentDetailQueryDTO();
        materialQueryDTO.setWarehouseGuid("warehouseGuid");
        materialQueryDTO.setSupplierGuid("supplierGuid");
        materialQueryDTO.setStoreGuid("storeGuid");
        materialQueryDTO.setMaterialGuidList(Arrays.asList("value"));

        // Configure IMaterialService.findList(...).
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setInUnitPrice(new BigDecimal("0.00"));
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("ff07141f-7642-49cb-907e-b17415b52509");
        materialDTO.setEnterpriseGuid("enterpriseGuid");
        materialDTO.setProperty("property");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO);
        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value")))
                .thenReturn(materialDTOS);

        // Configure PricingSchemesService.batchQueryPricingSchemesList(...).
        final PricingSchemesDTO pricingSchemesDTO = new PricingSchemesDTO();
        pricingSchemesDTO.setMaterialName("materialName");
        final MaterialUnitDTO materialUnitDTO = new MaterialUnitDTO();
        materialUnitDTO.setGuid("d19ed052-cf51-4dfc-ae5e-d07df414b205");
        materialUnitDTO.setName("name");
        materialUnitDTO.setCode("code");
        pricingSchemesDTO.setUnitList(Arrays.asList(materialUnitDTO));
        final List<PricingSchemesDTO> pricingSchemesDTOS = Arrays.asList(pricingSchemesDTO);
        when(mockPricingSchemesService.batchQueryPricingSchemesList(any(PricingSchemesQueryDTO.class)))
                .thenReturn(pricingSchemesDTOS);

        // Run the test
        final List<InOutDocumentDetailSelectDTO> result = inOutDocumentServiceImplUnderTest.selectMaterialListForAdd(
                materialQueryDTO);

        // Verify the results
    }

    @Test
    public void testSelectMaterialListForAdd_IMaterialServiceReturnsNull() {
        // Setup
        final InOutDocumentDetailQueryDTO materialQueryDTO = new InOutDocumentDetailQueryDTO();
        materialQueryDTO.setWarehouseGuid("warehouseGuid");
        materialQueryDTO.setSupplierGuid("supplierGuid");
        materialQueryDTO.setStoreGuid("storeGuid");
        materialQueryDTO.setMaterialGuidList(Arrays.asList("value"));

        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value"))).thenReturn(null);

        // Run the test
        assertThatThrownBy(
                () -> inOutDocumentServiceImplUnderTest.selectMaterialListForAdd(materialQueryDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSelectMaterialListForAdd_IMaterialServiceReturnsNoItems() {
        // Setup
        final InOutDocumentDetailQueryDTO materialQueryDTO = new InOutDocumentDetailQueryDTO();
        materialQueryDTO.setWarehouseGuid("warehouseGuid");
        materialQueryDTO.setSupplierGuid("supplierGuid");
        materialQueryDTO.setStoreGuid("storeGuid");
        materialQueryDTO.setMaterialGuidList(Arrays.asList("value"));

        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(
                () -> inOutDocumentServiceImplUnderTest.selectMaterialListForAdd(materialQueryDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSelectMaterialListForAdd_PricingSchemesServiceReturnsNoItems() {
        // Setup
        final InOutDocumentDetailQueryDTO materialQueryDTO = new InOutDocumentDetailQueryDTO();
        materialQueryDTO.setWarehouseGuid("warehouseGuid");
        materialQueryDTO.setSupplierGuid("supplierGuid");
        materialQueryDTO.setStoreGuid("storeGuid");
        materialQueryDTO.setMaterialGuidList(Arrays.asList("value"));

        // Configure IMaterialService.findList(...).
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setInUnitPrice(new BigDecimal("0.00"));
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("ff07141f-7642-49cb-907e-b17415b52509");
        materialDTO.setEnterpriseGuid("enterpriseGuid");
        materialDTO.setProperty("property");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO);
        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value")))
                .thenReturn(materialDTOS);

        when(mockPricingSchemesService.batchQueryPricingSchemesList(any(PricingSchemesQueryDTO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<InOutDocumentDetailSelectDTO> result = inOutDocumentServiceImplUnderTest.selectMaterialListForAdd(
                materialQueryDTO);

        // Verify the results
    }

    @Test
    public void testSelectDocumentInAndReturnCount() {
        // Setup
        // Configure InOutDocumentDetailMapper.selectDocumentInAndReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectDocumentInAndReturnCount("contactDocumentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Run the test
        final List<DocumentMaterialInAndReturnCountBO> result = inOutDocumentServiceImplUnderTest.selectDocumentInAndReturnCount(
                "contactDocumentGuid");

        // Verify the results
    }

    @Test
    public void testSelectDocumentInAndReturnCount_InOutDocumentDetailMapperReturnsNoItems() {
        // Setup
        when(mockDetailMapper.selectDocumentInAndReturnCount("contactDocumentGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<DocumentMaterialInAndReturnCountBO> result = inOutDocumentServiceImplUnderTest.selectDocumentInAndReturnCount(
                "contactDocumentGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testSelectMaterialListForUpdate() {
        // Setup
        // Configure InOutDocumentDetailMapper.selectInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectInOutDocumentDetailList("contactDocumentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure InOutDocumentMapper.selectStoreGuidAndWarehouseGuid(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectStoreGuidAndWarehouseGuid("contactDocumentGuid"))
                .thenReturn(inOutDocumentDO);

        // Configure IMaterialService.findList(...).
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setInUnitPrice(new BigDecimal("0.00"));
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("ff07141f-7642-49cb-907e-b17415b52509");
        materialDTO.setEnterpriseGuid("enterpriseGuid");
        materialDTO.setProperty("property");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO);
        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value")))
                .thenReturn(materialDTOS);

        // Run the test
        final List<InOutDocumentDetailSelectDTO> result = inOutDocumentServiceImplUnderTest.selectMaterialListForUpdate(
                "contactDocumentGuid");

        // Verify the results
    }

    @Test
    public void testSelectMaterialListForUpdate_InOutDocumentDetailMapperReturnsNoItems() {
        // Setup
        when(mockDetailMapper.selectInOutDocumentDetailList("contactDocumentGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final List<InOutDocumentDetailSelectDTO> result = inOutDocumentServiceImplUnderTest.selectMaterialListForUpdate(
                "contactDocumentGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testSelectMaterialListForUpdate_IMaterialServiceReturnsNull() {
        // Setup
        // Configure InOutDocumentDetailMapper.selectInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectInOutDocumentDetailList("contactDocumentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure InOutDocumentMapper.selectStoreGuidAndWarehouseGuid(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectStoreGuidAndWarehouseGuid("contactDocumentGuid"))
                .thenReturn(inOutDocumentDO);

        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value"))).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inOutDocumentServiceImplUnderTest.selectMaterialListForUpdate(
                "contactDocumentGuid")).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSelectMaterialListForUpdate_IMaterialServiceReturnsNoItems() {
        // Setup
        // Configure InOutDocumentDetailMapper.selectInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectInOutDocumentDetailList("contactDocumentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure InOutDocumentMapper.selectStoreGuidAndWarehouseGuid(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectStoreGuidAndWarehouseGuid("contactDocumentGuid"))
                .thenReturn(inOutDocumentDO);

        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> inOutDocumentServiceImplUnderTest.selectMaterialListForUpdate(
                "contactDocumentGuid")).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSelectMaterialListForReturn() {
        // Setup
        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(inOutDocumentDO);

        // Configure InOutDocumentDetailMapper.selectInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectInOutDocumentDetailList("contactDocumentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure InOutDocumentMapper.selectStoreGuidAndWarehouseGuid(...).
        final InOutDocumentDO inOutDocumentDO1 = new InOutDocumentDO();
        inOutDocumentDO1.setStoreGuid("storeGuid");
        inOutDocumentDO1.setStatus(0);
        inOutDocumentDO1.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO1.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO1.setType(0);
        when(mockInOutDocumentMapper.selectStoreGuidAndWarehouseGuid("contactDocumentGuid"))
                .thenReturn(inOutDocumentDO1);

        // Configure IMaterialService.findList(...).
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setInUnitPrice(new BigDecimal("0.00"));
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("ff07141f-7642-49cb-907e-b17415b52509");
        materialDTO.setEnterpriseGuid("enterpriseGuid");
        materialDTO.setProperty("property");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO);
        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value")))
                .thenReturn(materialDTOS);

        // Run the test
        final List<InOutDocumentDetailSelectDTO> result = inOutDocumentServiceImplUnderTest.selectMaterialListForReturn(
                "contactDocumentGuid");

        // Verify the results
    }

    @Test
    public void testSelectMaterialListForReturn_InOutDocumentMapperSelectInOutDocumentStatusReturnsNull() {
        // Setup
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inOutDocumentServiceImplUnderTest.selectMaterialListForReturn(
                "contactDocumentGuid")).isInstanceOf(ParameterException.class);
    }

    @Test
    public void testSelectMaterialListForReturn_InOutDocumentDetailMapperReturnsNoItems() {
        // Setup
        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(inOutDocumentDO);

        when(mockDetailMapper.selectInOutDocumentDetailList("contactDocumentGuid")).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> inOutDocumentServiceImplUnderTest.selectMaterialListForReturn(
                "contactDocumentGuid")).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSelectMaterialListForReturn_IMaterialServiceReturnsNull() {
        // Setup
        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(inOutDocumentDO);

        // Configure InOutDocumentDetailMapper.selectInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectInOutDocumentDetailList("contactDocumentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure InOutDocumentMapper.selectStoreGuidAndWarehouseGuid(...).
        final InOutDocumentDO inOutDocumentDO1 = new InOutDocumentDO();
        inOutDocumentDO1.setStoreGuid("storeGuid");
        inOutDocumentDO1.setStatus(0);
        inOutDocumentDO1.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO1.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO1.setType(0);
        when(mockInOutDocumentMapper.selectStoreGuidAndWarehouseGuid("contactDocumentGuid"))
                .thenReturn(inOutDocumentDO1);

        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value"))).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inOutDocumentServiceImplUnderTest.selectMaterialListForReturn(
                "contactDocumentGuid")).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSelectMaterialListForReturn_IMaterialServiceReturnsNoItems() {
        // Setup
        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(inOutDocumentDO);

        // Configure InOutDocumentDetailMapper.selectInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectInOutDocumentDetailList("contactDocumentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure InOutDocumentMapper.selectStoreGuidAndWarehouseGuid(...).
        final InOutDocumentDO inOutDocumentDO1 = new InOutDocumentDO();
        inOutDocumentDO1.setStoreGuid("storeGuid");
        inOutDocumentDO1.setStatus(0);
        inOutDocumentDO1.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO1.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO1.setType(0);
        when(mockInOutDocumentMapper.selectStoreGuidAndWarehouseGuid("contactDocumentGuid"))
                .thenReturn(inOutDocumentDO1);

        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> inOutDocumentServiceImplUnderTest.selectMaterialListForReturn(
                "contactDocumentGuid")).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSubmitInoutDocument() {
        // Setup
        // Configure InOutDocumentDetailMapper.selectInDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectInDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure MaterialStockService.batchAddStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOS = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchAddStock(updateStockBOS)).thenReturn(false);

        // Run the test
        inOutDocumentServiceImplUnderTest.submitInoutDocument(0, "documentGuid");

        // Verify the results
        verify(mockInOutDocumentMapper).submitInOutDocument("documentGuid");
    }

    @Test
    public void testSubmitInoutDocument_InOutDocumentDetailMapperSelectInDocumentMaterialCountReturnsNoItems() {
        // Setup
        when(mockDetailMapper.selectInDocumentMaterialCount("documentGuid")).thenReturn(Collections.emptyList());

        // Configure MaterialStockService.batchAddStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOS = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchAddStock(updateStockBOS)).thenReturn(false);

        // Run the test
        inOutDocumentServiceImplUnderTest.submitInoutDocument(0, "documentGuid");

        // Verify the results
        verify(mockInOutDocumentMapper).submitInOutDocument("documentGuid");
    }

    @Test
    public void testSubmitInoutDocument_InOutDocumentDetailMapperSelectOutDocumentMaterialCountReturnsNoItems() {
        // Setup
        when(mockDetailMapper.selectOutDocumentMaterialCount("documentGuid")).thenReturn(Collections.emptyList());

        // Configure MaterialStockService.batchReduceStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchReduceStock(updateStockBOList)).thenReturn(false);

        // Configure InOutDocumentMapper.selectDocumentType(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectDocumentType("documentGuid")).thenReturn(inOutDocumentDO);

        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO1 = new InOutDocumentDO();
        inOutDocumentDO1.setStoreGuid("storeGuid");
        inOutDocumentDO1.setStatus(0);
        inOutDocumentDO1.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO1.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO1.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(inOutDocumentDO1);

        // Configure InOutDocumentDetailMapper.selectDocumentInAndReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectDocumentInAndReturnCount("contactDocumentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Run the test
        inOutDocumentServiceImplUnderTest.submitInoutDocument(0, "documentGuid");

        // Verify the results
        // Confirm InOutDocumentDetailMapper.updateInDocumentDetailReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO1 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO1.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO1.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO1.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO1.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO1.setUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailList = Arrays.asList(inOutDocumentDetailDO1);
        verify(mockDetailMapper).updateInDocumentDetailReturnCount("contactDocumentGuid", detailList);
        verify(mockInOutDocumentMapper).submitInOutDocument("documentGuid");
    }

    @Test
    public void testSubmitInoutDocument_InOutDocumentMapperSelectInOutDocumentStatusReturnsNull() {
        // Setup
        // Configure InOutDocumentDetailMapper.selectOutDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectOutDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure MaterialStockService.batchReduceStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchReduceStock(updateStockBOList)).thenReturn(false);

        // Configure InOutDocumentMapper.selectDocumentType(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectDocumentType("documentGuid")).thenReturn(inOutDocumentDO);

        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inOutDocumentServiceImplUnderTest.submitInoutDocument(0, "documentGuid"))
                .isInstanceOf(ParameterException.class);
    }

    @Test
    public void testSubmitInoutDocument_InOutDocumentDetailMapperSelectDocumentInAndReturnCountReturnsNoItems() {
        // Setup
        // Configure InOutDocumentDetailMapper.selectOutDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectOutDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure MaterialStockService.batchReduceStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchReduceStock(updateStockBOList)).thenReturn(false);

        // Configure InOutDocumentMapper.selectDocumentType(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectDocumentType("documentGuid")).thenReturn(inOutDocumentDO);

        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO1 = new InOutDocumentDO();
        inOutDocumentDO1.setStoreGuid("storeGuid");
        inOutDocumentDO1.setStatus(0);
        inOutDocumentDO1.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO1.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO1.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(inOutDocumentDO1);

        when(mockDetailMapper.selectDocumentInAndReturnCount("contactDocumentGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        inOutDocumentServiceImplUnderTest.submitInoutDocument(0, "documentGuid");

        // Verify the results
        // Confirm InOutDocumentDetailMapper.updateInDocumentDetailReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO1 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO1.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO1.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO1.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO1.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO1.setUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailList = Arrays.asList(inOutDocumentDetailDO1);
        verify(mockDetailMapper).updateInDocumentDetailReturnCount("contactDocumentGuid", detailList);
        verify(mockInOutDocumentMapper).submitInOutDocument("documentGuid");
    }

    @Test
    public void testUpdateContactDocumentReturnCountWithLock() {
        // Setup
        when(mockRedisson.getLock("contactDocumentGuid")).thenReturn(null);

        // Run the test
        inOutDocumentServiceImplUnderTest.updateContactDocumentReturnCountWithLock("documentGuid",
                "contactDocumentGuid");

        // Verify the results
        verify(mockInOutDocumentService).updateContactDocumentReturnCount("documentGuid", "contactDocumentGuid");
    }

    @Test
    public void testUpdateContactDocumentReturnCount() {
        // Setup
        // Configure InOutDocumentDetailMapper.selectOutDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectOutDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(inOutDocumentDO);

        // Configure InOutDocumentDetailMapper.selectDocumentInAndReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO1 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO1.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO1.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO1.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO1.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO1.setUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS1 = Arrays.asList(inOutDocumentDetailDO1);
        when(mockDetailMapper.selectDocumentInAndReturnCount("contactDocumentGuid"))
                .thenReturn(inOutDocumentDetailDOS1);

        // Run the test
        inOutDocumentServiceImplUnderTest.updateContactDocumentReturnCount("documentGuid", "contactDocumentGuid");

        // Verify the results
        // Confirm InOutDocumentDetailMapper.updateInDocumentDetailReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO2 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO2.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO2.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO2.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO2.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO2.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO2.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO2.setUnitGuid("unitGuid");
        inOutDocumentDetailDO2.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO2.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailList = Arrays.asList(inOutDocumentDetailDO2);
        verify(mockDetailMapper).updateInDocumentDetailReturnCount("contactDocumentGuid", detailList);
    }

    @Test
    public void testUpdateContactDocumentReturnCount_InOutDocumentDetailMapperSelectOutDocumentMaterialCountReturnsNoItems() {
        // Setup
        when(mockDetailMapper.selectOutDocumentMaterialCount("documentGuid")).thenReturn(Collections.emptyList());

        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(inOutDocumentDO);

        // Configure InOutDocumentDetailMapper.selectDocumentInAndReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectDocumentInAndReturnCount("contactDocumentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Run the test
        inOutDocumentServiceImplUnderTest.updateContactDocumentReturnCount("documentGuid", "contactDocumentGuid");

        // Verify the results
        // Confirm InOutDocumentDetailMapper.updateInDocumentDetailReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO1 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO1.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO1.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO1.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO1.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO1.setUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailList = Arrays.asList(inOutDocumentDetailDO1);
        verify(mockDetailMapper).updateInDocumentDetailReturnCount("contactDocumentGuid", detailList);
    }

    @Test
    public void testUpdateContactDocumentReturnCount_InOutDocumentMapperReturnsNull() {
        // Setup
        // Configure InOutDocumentDetailMapper.selectOutDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectOutDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS);

        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inOutDocumentServiceImplUnderTest.updateContactDocumentReturnCount("documentGuid",
                "contactDocumentGuid")).isInstanceOf(ParameterException.class);
    }

    @Test
    public void testUpdateContactDocumentReturnCount_InOutDocumentDetailMapperSelectDocumentInAndReturnCountReturnsNoItems() {
        // Setup
        // Configure InOutDocumentDetailMapper.selectOutDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectOutDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(inOutDocumentDO);

        when(mockDetailMapper.selectDocumentInAndReturnCount("contactDocumentGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        inOutDocumentServiceImplUnderTest.updateContactDocumentReturnCount("documentGuid", "contactDocumentGuid");

        // Verify the results
        // Confirm InOutDocumentDetailMapper.updateInDocumentDetailReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO1 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO1.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO1.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO1.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO1.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO1.setUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailList = Arrays.asList(inOutDocumentDetailDO1);
        verify(mockDetailMapper).updateInDocumentDetailReturnCount("contactDocumentGuid", detailList);
    }

    @Test
    public void testReduceStockByDocument() {
        // Setup
        // Configure InOutDocumentDetailMapper.selectOutDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectOutDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure MaterialStockService.batchReduceStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchReduceStock(updateStockBOList)).thenReturn(false);

        // Run the test
        final boolean result = inOutDocumentServiceImplUnderTest.reduceStockByDocument("documentGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testReduceStockByDocument_InOutDocumentDetailMapperReturnsNoItems() {
        // Setup
        when(mockDetailMapper.selectOutDocumentMaterialCount("documentGuid")).thenReturn(Collections.emptyList());

        // Configure MaterialStockService.batchReduceStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchReduceStock(updateStockBOList)).thenReturn(false);

        // Run the test
        final boolean result = inOutDocumentServiceImplUnderTest.reduceStockByDocument("documentGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testReduceStockByDocument_MaterialStockServiceReturnsTrue() {
        // Setup
        // Configure InOutDocumentDetailMapper.selectOutDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectOutDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure MaterialStockService.batchReduceStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchReduceStock(updateStockBOList)).thenReturn(true);

        // Run the test
        final boolean result = inOutDocumentServiceImplUnderTest.reduceStockByDocument("documentGuid");

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testAddStockByDocument() {
        // Setup
        // Configure InOutDocumentDetailMapper.selectInDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectInDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure MaterialStockService.batchAddStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOS = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchAddStock(updateStockBOS)).thenReturn(false);

        // Run the test
        final boolean result = inOutDocumentServiceImplUnderTest.addStockByDocument("documentGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testAddStockByDocument_InOutDocumentDetailMapperReturnsNoItems() {
        // Setup
        when(mockDetailMapper.selectInDocumentMaterialCount("documentGuid")).thenReturn(Collections.emptyList());

        // Configure MaterialStockService.batchAddStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOS = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchAddStock(updateStockBOS)).thenReturn(false);

        // Run the test
        final boolean result = inOutDocumentServiceImplUnderTest.addStockByDocument("documentGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testAddStockByDocument_MaterialStockServiceReturnsTrue() {
        // Setup
        // Configure InOutDocumentDetailMapper.selectInDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectInDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure MaterialStockService.batchAddStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOS = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchAddStock(updateStockBOS)).thenReturn(true);

        // Run the test
        final boolean result = inOutDocumentServiceImplUnderTest.addStockByDocument("documentGuid");

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testDeleteDocument() {
        // Setup
        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("documentGuid")).thenReturn(inOutDocumentDO);

        // Run the test
        inOutDocumentServiceImplUnderTest.deleteDocument("documentGuid");

        // Verify the results
        verify(mockInOutDocumentMapper).deleteInOutDocumentAndDetail("documentGuid");
    }

    @Test
    public void testSelectDocumentGuidList() {
        // Setup
        final InOutContactDocumentQueryDTO queryDTO = new InOutContactDocumentQueryDTO();
        queryDTO.setSupplierGuid("supplierGuid");
        queryDTO.setWarehouseGuid("warehouseGuid");
        queryDTO.setDocumentGuid("documentGuid");

        when(mockInOutDocumentMapper.selectDocumentGuidList(any(InOutContactDocumentQuery.class)))
                .thenReturn(Arrays.asList("value"));

        // Run the test
        final List<String> result = inOutDocumentServiceImplUnderTest.selectDocumentGuidList(queryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
    }

    @Test
    public void testSelectDocumentGuidList_InOutDocumentMapperReturnsNoItems() {
        // Setup
        final InOutContactDocumentQueryDTO queryDTO = new InOutContactDocumentQueryDTO();
        queryDTO.setSupplierGuid("supplierGuid");
        queryDTO.setWarehouseGuid("warehouseGuid");
        queryDTO.setDocumentGuid("documentGuid");

        when(mockInOutDocumentMapper.selectDocumentGuidList(any(InOutContactDocumentQuery.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = inOutDocumentServiceImplUnderTest.selectDocumentGuidList(queryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testSelectDocumentForUpdate() {
        // Setup
        // Configure InOutDocumentMapper.selectDocument(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectDocument("documentGuid")).thenReturn(inOutDocumentDO);

        // Configure SuppliersService.getSuppliersStatus(...).
        final SuppliersDTO suppliersDTO = new SuppliersDTO();
        suppliersDTO.setEnabled(0);
        suppliersDTO.setDeleted(0);
        suppliersDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        suppliersDTO.setGuid("883ed004-649c-4db5-beab-d8c3e961712b");
        when(mockSuppliersService.getSuppliersStatus("supplierGuid")).thenReturn(suppliersDTO);

        // Run the test
        final InOutDocumentSelectDTO result = inOutDocumentServiceImplUnderTest.selectDocumentForUpdate("documentGuid");

        // Verify the results
    }

    @Test
    public void testSelectDocumentForUpdate_SuppliersServiceReturnsNull() {
        // Setup
        // Configure InOutDocumentMapper.selectDocument(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectDocument("documentGuid")).thenReturn(inOutDocumentDO);

        when(mockSuppliersService.getSuppliersStatus("supplierGuid")).thenReturn(null);

        // Run the test
        final InOutDocumentSelectDTO result = inOutDocumentServiceImplUnderTest.selectDocumentForUpdate("documentGuid");

        // Verify the results
    }

    @Test
    public void testSelectDocumentAndDetailForSelect() {
        // Setup
        // Configure InOutDocumentMapper.selectDocumentAndDetail(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectDocumentAndDetail("documentGuid")).thenReturn(inOutDocumentDO);

        when(mockInOutDocumentMapper.selectContactDocumentGuidListForInDocument("documentGuid"))
                .thenReturn(Arrays.asList("value"));

        // Run the test
        final InOutDocumentSelectDTO result = inOutDocumentServiceImplUnderTest.selectDocumentAndDetailForSelect(
                "documentGuid");

        // Verify the results
    }

    @Test
    public void testSelectDocumentAndDetailForSelect_InOutDocumentMapperSelectDocumentAndDetailReturnsNull() {
        // Setup
        when(mockInOutDocumentMapper.selectDocumentAndDetail("documentGuid")).thenReturn(null);

        // Run the test
        final InOutDocumentSelectDTO result = inOutDocumentServiceImplUnderTest.selectDocumentAndDetailForSelect(
                "documentGuid");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testSelectDocumentAndDetailForSelect_InOutDocumentMapperSelectContactDocumentGuidListForInDocumentReturnsNoItems() {
        // Setup
        // Configure InOutDocumentMapper.selectDocumentAndDetail(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectDocumentAndDetail("documentGuid")).thenReturn(inOutDocumentDO);

        when(mockInOutDocumentMapper.selectContactDocumentGuidListForInDocument("documentGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final InOutDocumentSelectDTO result = inOutDocumentServiceImplUnderTest.selectDocumentAndDetailForSelect(
                "documentGuid");

        // Verify the results
    }

    @Test
    public void testExistDocumentOfWarehouse() {
        // Setup
        when(mockInOutDocumentMapper.selectCountByWarehouseGuid("warehouseGuid")).thenReturn(0);
        when(mockCheckoutDocumentService.selectCountByWarehouseGuid("warehouseGuid")).thenReturn(0);

        // Run the test
        final boolean result = inOutDocumentServiceImplUnderTest.existDocumentOfWarehouse("warehouseGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testExistDocumentOfSupplier() {
        // Setup
        when(mockInOutDocumentMapper.selectCountBySupplierGuid("supplierGuid")).thenReturn(0);

        // Run the test
        final boolean result = inOutDocumentServiceImplUnderTest.existDocumentOfSupplier("supplierGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testSelectDocumentListForPage() {
        // Setup
        final InOutDocumentQueryDTO queryDTO = new InOutDocumentQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setWarehouseGuidList(Arrays.asList("value"));
        queryDTO.setStatus(0);
        queryDTO.setType(0);

        // Configure InOutDocumentMapper.selectDocumentListForPage(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        final List<InOutDocumentDO> inOutDocumentDOS = Arrays.asList(inOutDocumentDO);
        when(mockInOutDocumentMapper.selectDocumentListForPage(any(InOutDocumentQuery.class)))
                .thenReturn(inOutDocumentDOS);

        when(mockInOutDocumentMapper.selectDocumentCount(any(InOutDocumentQuery.class))).thenReturn(0L);

        // Run the test
        final Page<InOutDocumentSelectDTO> result = inOutDocumentServiceImplUnderTest.selectDocumentListForPage(
                queryDTO);

        // Verify the results
    }

    @Test
    public void testSelectDocumentListForPage_InOutDocumentMapperSelectDocumentListForPageReturnsNoItems() {
        // Setup
        final InOutDocumentQueryDTO queryDTO = new InOutDocumentQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setWarehouseGuidList(Arrays.asList("value"));
        queryDTO.setStatus(0);
        queryDTO.setType(0);

        when(mockInOutDocumentMapper.selectDocumentListForPage(any(InOutDocumentQuery.class)))
                .thenReturn(Collections.emptyList());
        when(mockInOutDocumentMapper.selectDocumentCount(any(InOutDocumentQuery.class))).thenReturn(0L);

        // Run the test
        final Page<InOutDocumentSelectDTO> result = inOutDocumentServiceImplUnderTest.selectDocumentListForPage(
                queryDTO);

        // Verify the results
    }

    @Test
    public void testInsertAndSubmitInOutDocument() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("documentGuid");
        inOutDocumentDTO.setType(0);

        // Configure IMaterialService.unitAdapterMain(...).
        final List<UnitConvertBO> unitConvertBOS = Arrays.asList(
                new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")));
        when(mockMaterialService.unitAdapterMain(
                Arrays.asList(new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")))))
                .thenReturn(unitConvertBOS);

        // Configure InOutDocumentDetailMapper.selectInDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectInDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure MaterialStockService.batchAddStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOS = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchAddStock(updateStockBOS)).thenReturn(false);

        // Configure InOutDocumentDetailMapper.selectOutDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO1 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO1.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO1.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO1.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO1.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO1.setUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS1 = Arrays.asList(inOutDocumentDetailDO1);
        when(mockDetailMapper.selectOutDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS1);

        // Configure MaterialStockService.batchReduceStock(...).
        final UpdateStockBO updateStockBO1 = new UpdateStockBO();
        updateStockBO1.setMaterialGuid("materialGuid");
        updateStockBO1.setCount(new BigDecimal("0.00"));
        updateStockBO1.setMaterialUnit("materialUnit");
        updateStockBO1.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList = Arrays.asList(updateStockBO1);
        when(mockMaterialStockService.batchReduceStock(updateStockBOList)).thenReturn(false);

        // Configure InOutDocumentMapper.selectDocumentType(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectDocumentType("documentGuid")).thenReturn(inOutDocumentDO);

        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO1 = new InOutDocumentDO();
        inOutDocumentDO1.setStoreGuid("storeGuid");
        inOutDocumentDO1.setStatus(0);
        inOutDocumentDO1.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO1.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO1.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(inOutDocumentDO1);

        // Configure InOutDocumentDetailMapper.selectDocumentInAndReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO2 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO2.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO2.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO2.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO2.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO2.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO2.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO2.setUnitGuid("unitGuid");
        inOutDocumentDetailDO2.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO2.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS2 = Arrays.asList(inOutDocumentDetailDO2);
        when(mockDetailMapper.selectDocumentInAndReturnCount("contactDocumentGuid"))
                .thenReturn(inOutDocumentDetailDOS2);

        // Run the test
        final String result = inOutDocumentServiceImplUnderTest.insertAndSubmitInOutDocument(inOutDocumentDTO);

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockInOutDocumentMapper).insertInOutDocument(any(InOutDocumentDO.class));

        // Confirm InOutDocumentDetailMapper.insertInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO3 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO3.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO3.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO3.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO3.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO3.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO3.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO3.setUnitGuid("unitGuid");
        inOutDocumentDetailDO3.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO3.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailDOList = Arrays.asList(inOutDocumentDetailDO3);
        verify(mockDetailMapper).insertInOutDocumentDetailList(detailDOList);

        // Confirm InOutDocumentDetailMapper.updateInDocumentDetailReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO4 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO4.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO4.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO4.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO4.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO4.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO4.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO4.setUnitGuid("unitGuid");
        inOutDocumentDetailDO4.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO4.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailList = Arrays.asList(inOutDocumentDetailDO4);
        verify(mockDetailMapper).updateInDocumentDetailReturnCount("contactDocumentGuid", detailList);
        verify(mockInOutDocumentMapper).submitInOutDocument("documentGuid");
    }

    @Test
    public void testInsertAndSubmitInOutDocument_IMaterialServiceReturnsNoItems() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("documentGuid");
        inOutDocumentDTO.setType(0);

        when(mockMaterialService.unitAdapterMain(
                Arrays.asList(new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")))))
                .thenReturn(Collections.emptyList());

        // Configure InOutDocumentDetailMapper.selectInDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectInDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure MaterialStockService.batchAddStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOS = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchAddStock(updateStockBOS)).thenReturn(false);

        // Configure InOutDocumentDetailMapper.selectOutDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO1 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO1.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO1.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO1.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO1.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO1.setUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS1 = Arrays.asList(inOutDocumentDetailDO1);
        when(mockDetailMapper.selectOutDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS1);

        // Configure MaterialStockService.batchReduceStock(...).
        final UpdateStockBO updateStockBO1 = new UpdateStockBO();
        updateStockBO1.setMaterialGuid("materialGuid");
        updateStockBO1.setCount(new BigDecimal("0.00"));
        updateStockBO1.setMaterialUnit("materialUnit");
        updateStockBO1.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList = Arrays.asList(updateStockBO1);
        when(mockMaterialStockService.batchReduceStock(updateStockBOList)).thenReturn(false);

        // Configure InOutDocumentMapper.selectDocumentType(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectDocumentType("documentGuid")).thenReturn(inOutDocumentDO);

        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO1 = new InOutDocumentDO();
        inOutDocumentDO1.setStoreGuid("storeGuid");
        inOutDocumentDO1.setStatus(0);
        inOutDocumentDO1.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO1.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO1.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(inOutDocumentDO1);

        // Configure InOutDocumentDetailMapper.selectDocumentInAndReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO2 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO2.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO2.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO2.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO2.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO2.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO2.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO2.setUnitGuid("unitGuid");
        inOutDocumentDetailDO2.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO2.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS2 = Arrays.asList(inOutDocumentDetailDO2);
        when(mockDetailMapper.selectDocumentInAndReturnCount("contactDocumentGuid"))
                .thenReturn(inOutDocumentDetailDOS2);

        // Run the test
        final String result = inOutDocumentServiceImplUnderTest.insertAndSubmitInOutDocument(inOutDocumentDTO);

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockInOutDocumentMapper).insertInOutDocument(any(InOutDocumentDO.class));

        // Confirm InOutDocumentDetailMapper.insertInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO3 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO3.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO3.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO3.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO3.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO3.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO3.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO3.setUnitGuid("unitGuid");
        inOutDocumentDetailDO3.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO3.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailDOList = Arrays.asList(inOutDocumentDetailDO3);
        verify(mockDetailMapper).insertInOutDocumentDetailList(detailDOList);

        // Confirm InOutDocumentDetailMapper.updateInDocumentDetailReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO4 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO4.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO4.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO4.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO4.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO4.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO4.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO4.setUnitGuid("unitGuid");
        inOutDocumentDetailDO4.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO4.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailList = Arrays.asList(inOutDocumentDetailDO4);
        verify(mockDetailMapper).updateInDocumentDetailReturnCount("contactDocumentGuid", detailList);
        verify(mockInOutDocumentMapper).submitInOutDocument("documentGuid");
    }

    @Test
    public void testInsertAndSubmitInOutDocument_InOutDocumentDetailMapperSelectInDocumentMaterialCountReturnsNoItems() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("documentGuid");
        inOutDocumentDTO.setType(0);

        // Configure IMaterialService.unitAdapterMain(...).
        final List<UnitConvertBO> unitConvertBOS = Arrays.asList(
                new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")));
        when(mockMaterialService.unitAdapterMain(
                Arrays.asList(new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")))))
                .thenReturn(unitConvertBOS);

        when(mockDetailMapper.selectInDocumentMaterialCount("documentGuid")).thenReturn(Collections.emptyList());

        // Configure MaterialStockService.batchAddStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOS = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchAddStock(updateStockBOS)).thenReturn(false);

        // Run the test
        final String result = inOutDocumentServiceImplUnderTest.insertAndSubmitInOutDocument(inOutDocumentDTO);

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockInOutDocumentMapper).insertInOutDocument(any(InOutDocumentDO.class));

        // Confirm InOutDocumentDetailMapper.insertInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailDOList = Arrays.asList(inOutDocumentDetailDO);
        verify(mockDetailMapper).insertInOutDocumentDetailList(detailDOList);
        verify(mockInOutDocumentMapper).submitInOutDocument("documentGuid");
    }

    @Test
    public void testInsertAndSubmitInOutDocument_InOutDocumentDetailMapperSelectOutDocumentMaterialCountReturnsNoItems() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("documentGuid");
        inOutDocumentDTO.setType(0);

        // Configure IMaterialService.unitAdapterMain(...).
        final List<UnitConvertBO> unitConvertBOS = Arrays.asList(
                new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")));
        when(mockMaterialService.unitAdapterMain(
                Arrays.asList(new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")))))
                .thenReturn(unitConvertBOS);

        when(mockDetailMapper.selectOutDocumentMaterialCount("documentGuid")).thenReturn(Collections.emptyList());

        // Configure MaterialStockService.batchReduceStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchReduceStock(updateStockBOList)).thenReturn(false);

        // Configure InOutDocumentMapper.selectDocumentType(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectDocumentType("documentGuid")).thenReturn(inOutDocumentDO);

        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO1 = new InOutDocumentDO();
        inOutDocumentDO1.setStoreGuid("storeGuid");
        inOutDocumentDO1.setStatus(0);
        inOutDocumentDO1.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO1.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO1.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(inOutDocumentDO1);

        // Configure InOutDocumentDetailMapper.selectDocumentInAndReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectDocumentInAndReturnCount("contactDocumentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Run the test
        final String result = inOutDocumentServiceImplUnderTest.insertAndSubmitInOutDocument(inOutDocumentDTO);

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockInOutDocumentMapper).insertInOutDocument(any(InOutDocumentDO.class));

        // Confirm InOutDocumentDetailMapper.insertInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO1 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO1.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO1.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO1.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO1.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO1.setUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailDOList = Arrays.asList(inOutDocumentDetailDO1);
        verify(mockDetailMapper).insertInOutDocumentDetailList(detailDOList);

        // Confirm InOutDocumentDetailMapper.updateInDocumentDetailReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO2 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO2.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO2.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO2.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO2.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO2.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO2.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO2.setUnitGuid("unitGuid");
        inOutDocumentDetailDO2.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO2.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailList = Arrays.asList(inOutDocumentDetailDO2);
        verify(mockDetailMapper).updateInDocumentDetailReturnCount("contactDocumentGuid", detailList);
        verify(mockInOutDocumentMapper).submitInOutDocument("documentGuid");
    }

    @Test
    public void testInsertAndSubmitInOutDocument_InOutDocumentMapperSelectInOutDocumentStatusReturnsNull() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("documentGuid");
        inOutDocumentDTO.setType(0);

        // Configure IMaterialService.unitAdapterMain(...).
        final List<UnitConvertBO> unitConvertBOS = Arrays.asList(
                new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")));
        when(mockMaterialService.unitAdapterMain(
                Arrays.asList(new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")))))
                .thenReturn(unitConvertBOS);

        // Configure InOutDocumentDetailMapper.selectOutDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectOutDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure MaterialStockService.batchReduceStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchReduceStock(updateStockBOList)).thenReturn(false);

        // Configure InOutDocumentMapper.selectDocumentType(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectDocumentType("documentGuid")).thenReturn(inOutDocumentDO);

        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(null);

        // Run the test
        assertThatThrownBy(
                () -> inOutDocumentServiceImplUnderTest.insertAndSubmitInOutDocument(inOutDocumentDTO))
                .isInstanceOf(ParameterException.class);
        verify(mockInOutDocumentMapper).insertInOutDocument(any(InOutDocumentDO.class));

        // Confirm InOutDocumentDetailMapper.insertInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO1 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO1.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO1.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO1.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO1.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO1.setUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailDOList = Arrays.asList(inOutDocumentDetailDO1);
        verify(mockDetailMapper).insertInOutDocumentDetailList(detailDOList);
    }

    @Test
    public void testInsertAndSubmitInOutDocument_InOutDocumentDetailMapperSelectDocumentInAndReturnCountReturnsNoItems() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("documentGuid");
        inOutDocumentDTO.setType(0);

        // Configure IMaterialService.unitAdapterMain(...).
        final List<UnitConvertBO> unitConvertBOS = Arrays.asList(
                new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")));
        when(mockMaterialService.unitAdapterMain(
                Arrays.asList(new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")))))
                .thenReturn(unitConvertBOS);

        // Configure InOutDocumentDetailMapper.selectOutDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectOutDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure MaterialStockService.batchReduceStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchReduceStock(updateStockBOList)).thenReturn(false);

        // Configure InOutDocumentMapper.selectDocumentType(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectDocumentType("documentGuid")).thenReturn(inOutDocumentDO);

        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO1 = new InOutDocumentDO();
        inOutDocumentDO1.setStoreGuid("storeGuid");
        inOutDocumentDO1.setStatus(0);
        inOutDocumentDO1.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO1.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO1.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(inOutDocumentDO1);

        when(mockDetailMapper.selectDocumentInAndReturnCount("contactDocumentGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final String result = inOutDocumentServiceImplUnderTest.insertAndSubmitInOutDocument(inOutDocumentDTO);

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockInOutDocumentMapper).insertInOutDocument(any(InOutDocumentDO.class));

        // Confirm InOutDocumentDetailMapper.insertInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO1 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO1.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO1.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO1.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO1.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO1.setUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailDOList = Arrays.asList(inOutDocumentDetailDO1);
        verify(mockDetailMapper).insertInOutDocumentDetailList(detailDOList);

        // Confirm InOutDocumentDetailMapper.updateInDocumentDetailReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO2 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO2.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO2.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO2.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO2.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO2.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO2.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO2.setUnitGuid("unitGuid");
        inOutDocumentDetailDO2.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO2.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailList = Arrays.asList(inOutDocumentDetailDO2);
        verify(mockDetailMapper).updateInDocumentDetailReturnCount("contactDocumentGuid", detailList);
        verify(mockInOutDocumentMapper).submitInOutDocument("documentGuid");
    }

    @Test
    public void testInsertAndSubmitInOutDocumentWithLock() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("documentGuid");
        inOutDocumentDTO.setType(0);

        when(mockRedisson.getLock("contactDocumentGuid")).thenReturn(null);

        // Configure InOutDocumentService.insertAndSubmitInOutDocument(...).
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO1 = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO1.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO1.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO1 = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO1.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO1.setDocumentGuid("documentGuid");
        inOutDocumentDTO1.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO1));
        inOutDocumentDTO1.setGuid("documentGuid");
        inOutDocumentDTO1.setType(0);
        when(mockInOutDocumentService.insertAndSubmitInOutDocument(inOutDocumentDTO1)).thenReturn("result");

        // Run the test
        final String result = inOutDocumentServiceImplUnderTest.insertAndSubmitInOutDocumentWithLock(inOutDocumentDTO);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testUpdateAndSubmitInOutDocument() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("documentGuid");
        inOutDocumentDTO.setType(0);

        // Configure InOutDocumentMapper.selectDocumentStatusAndInOutType(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectDocumentStatusAndInOutType("documentGuid")).thenReturn(inOutDocumentDO);

        // Configure IMaterialService.unitAdapterMain(...).
        final List<UnitConvertBO> unitConvertBOS = Arrays.asList(
                new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")));
        when(mockMaterialService.unitAdapterMain(
                Arrays.asList(new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")))))
                .thenReturn(unitConvertBOS);

        // Configure InOutDocumentDetailMapper.selectInDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectInDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure MaterialStockService.batchAddStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOS = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchAddStock(updateStockBOS)).thenReturn(false);

        // Configure InOutDocumentDetailMapper.selectOutDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO1 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO1.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO1.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO1.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO1.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO1.setUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS1 = Arrays.asList(inOutDocumentDetailDO1);
        when(mockDetailMapper.selectOutDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS1);

        // Configure MaterialStockService.batchReduceStock(...).
        final UpdateStockBO updateStockBO1 = new UpdateStockBO();
        updateStockBO1.setMaterialGuid("materialGuid");
        updateStockBO1.setCount(new BigDecimal("0.00"));
        updateStockBO1.setMaterialUnit("materialUnit");
        updateStockBO1.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList = Arrays.asList(updateStockBO1);
        when(mockMaterialStockService.batchReduceStock(updateStockBOList)).thenReturn(false);

        // Configure InOutDocumentMapper.selectDocumentType(...).
        final InOutDocumentDO inOutDocumentDO1 = new InOutDocumentDO();
        inOutDocumentDO1.setStoreGuid("storeGuid");
        inOutDocumentDO1.setStatus(0);
        inOutDocumentDO1.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO1.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO1.setType(0);
        when(mockInOutDocumentMapper.selectDocumentType("documentGuid")).thenReturn(inOutDocumentDO1);

        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO2 = new InOutDocumentDO();
        inOutDocumentDO2.setStoreGuid("storeGuid");
        inOutDocumentDO2.setStatus(0);
        inOutDocumentDO2.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO2.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO2.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(inOutDocumentDO2);

        // Configure InOutDocumentDetailMapper.selectDocumentInAndReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO2 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO2.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO2.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO2.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO2.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO2.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO2.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO2.setUnitGuid("unitGuid");
        inOutDocumentDetailDO2.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO2.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS2 = Arrays.asList(inOutDocumentDetailDO2);
        when(mockDetailMapper.selectDocumentInAndReturnCount("contactDocumentGuid"))
                .thenReturn(inOutDocumentDetailDOS2);

        // Run the test
        inOutDocumentServiceImplUnderTest.updateAndSubmitInOutDocument(inOutDocumentDTO);

        // Verify the results
        verify(mockInOutDocumentMapper).deleteInOutDocumentAndDetail("documentGuid");
        verify(mockInOutDocumentMapper).insertInOutDocument(any(InOutDocumentDO.class));

        // Confirm InOutDocumentDetailMapper.insertInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO3 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO3.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO3.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO3.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO3.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO3.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO3.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO3.setUnitGuid("unitGuid");
        inOutDocumentDetailDO3.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO3.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailDOList = Arrays.asList(inOutDocumentDetailDO3);
        verify(mockDetailMapper).insertInOutDocumentDetailList(detailDOList);

        // Confirm InOutDocumentDetailMapper.updateInDocumentDetailReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO4 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO4.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO4.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO4.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO4.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO4.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO4.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO4.setUnitGuid("unitGuid");
        inOutDocumentDetailDO4.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO4.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailList = Arrays.asList(inOutDocumentDetailDO4);
        verify(mockDetailMapper).updateInDocumentDetailReturnCount("contactDocumentGuid", detailList);
        verify(mockInOutDocumentMapper).submitInOutDocument("documentGuid");
    }

    @Test
    public void testUpdateAndSubmitInOutDocument_InOutDocumentMapperSelectDocumentStatusAndInOutTypeReturnsNull() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("documentGuid");
        inOutDocumentDTO.setType(0);

        when(mockInOutDocumentMapper.selectDocumentStatusAndInOutType("documentGuid")).thenReturn(null);

        // Run the test
        assertThatThrownBy(
                () -> inOutDocumentServiceImplUnderTest.updateAndSubmitInOutDocument(inOutDocumentDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testUpdateAndSubmitInOutDocument_IMaterialServiceReturnsNoItems() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("documentGuid");
        inOutDocumentDTO.setType(0);

        // Configure InOutDocumentMapper.selectDocumentStatusAndInOutType(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectDocumentStatusAndInOutType("documentGuid")).thenReturn(inOutDocumentDO);

        when(mockMaterialService.unitAdapterMain(
                Arrays.asList(new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")))))
                .thenReturn(Collections.emptyList());

        // Configure InOutDocumentDetailMapper.selectInDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectInDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure MaterialStockService.batchAddStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOS = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchAddStock(updateStockBOS)).thenReturn(false);

        // Configure InOutDocumentDetailMapper.selectOutDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO1 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO1.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO1.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO1.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO1.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO1.setUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS1 = Arrays.asList(inOutDocumentDetailDO1);
        when(mockDetailMapper.selectOutDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS1);

        // Configure MaterialStockService.batchReduceStock(...).
        final UpdateStockBO updateStockBO1 = new UpdateStockBO();
        updateStockBO1.setMaterialGuid("materialGuid");
        updateStockBO1.setCount(new BigDecimal("0.00"));
        updateStockBO1.setMaterialUnit("materialUnit");
        updateStockBO1.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList = Arrays.asList(updateStockBO1);
        when(mockMaterialStockService.batchReduceStock(updateStockBOList)).thenReturn(false);

        // Configure InOutDocumentMapper.selectDocumentType(...).
        final InOutDocumentDO inOutDocumentDO1 = new InOutDocumentDO();
        inOutDocumentDO1.setStoreGuid("storeGuid");
        inOutDocumentDO1.setStatus(0);
        inOutDocumentDO1.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO1.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO1.setType(0);
        when(mockInOutDocumentMapper.selectDocumentType("documentGuid")).thenReturn(inOutDocumentDO1);

        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO2 = new InOutDocumentDO();
        inOutDocumentDO2.setStoreGuid("storeGuid");
        inOutDocumentDO2.setStatus(0);
        inOutDocumentDO2.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO2.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO2.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(inOutDocumentDO2);

        // Configure InOutDocumentDetailMapper.selectDocumentInAndReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO2 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO2.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO2.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO2.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO2.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO2.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO2.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO2.setUnitGuid("unitGuid");
        inOutDocumentDetailDO2.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO2.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS2 = Arrays.asList(inOutDocumentDetailDO2);
        when(mockDetailMapper.selectDocumentInAndReturnCount("contactDocumentGuid"))
                .thenReturn(inOutDocumentDetailDOS2);

        // Run the test
        inOutDocumentServiceImplUnderTest.updateAndSubmitInOutDocument(inOutDocumentDTO);

        // Verify the results
        verify(mockInOutDocumentMapper).deleteInOutDocumentAndDetail("documentGuid");
        verify(mockInOutDocumentMapper).insertInOutDocument(any(InOutDocumentDO.class));

        // Confirm InOutDocumentDetailMapper.insertInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO3 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO3.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO3.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO3.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO3.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO3.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO3.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO3.setUnitGuid("unitGuid");
        inOutDocumentDetailDO3.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO3.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailDOList = Arrays.asList(inOutDocumentDetailDO3);
        verify(mockDetailMapper).insertInOutDocumentDetailList(detailDOList);

        // Confirm InOutDocumentDetailMapper.updateInDocumentDetailReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO4 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO4.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO4.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO4.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO4.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO4.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO4.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO4.setUnitGuid("unitGuid");
        inOutDocumentDetailDO4.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO4.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailList = Arrays.asList(inOutDocumentDetailDO4);
        verify(mockDetailMapper).updateInDocumentDetailReturnCount("contactDocumentGuid", detailList);
        verify(mockInOutDocumentMapper).submitInOutDocument("documentGuid");
    }

    @Test
    public void testUpdateAndSubmitInOutDocument_InOutDocumentDetailMapperSelectInDocumentMaterialCountReturnsNoItems() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("documentGuid");
        inOutDocumentDTO.setType(0);

        // Configure InOutDocumentMapper.selectDocumentStatusAndInOutType(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectDocumentStatusAndInOutType("documentGuid")).thenReturn(inOutDocumentDO);

        // Configure IMaterialService.unitAdapterMain(...).
        final List<UnitConvertBO> unitConvertBOS = Arrays.asList(
                new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")));
        when(mockMaterialService.unitAdapterMain(
                Arrays.asList(new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")))))
                .thenReturn(unitConvertBOS);

        when(mockDetailMapper.selectInDocumentMaterialCount("documentGuid")).thenReturn(Collections.emptyList());

        // Configure MaterialStockService.batchAddStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOS = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchAddStock(updateStockBOS)).thenReturn(false);

        // Run the test
        inOutDocumentServiceImplUnderTest.updateAndSubmitInOutDocument(inOutDocumentDTO);

        // Verify the results
        verify(mockInOutDocumentMapper).deleteInOutDocumentAndDetail("documentGuid");
        verify(mockInOutDocumentMapper).insertInOutDocument(any(InOutDocumentDO.class));

        // Confirm InOutDocumentDetailMapper.insertInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailDOList = Arrays.asList(inOutDocumentDetailDO);
        verify(mockDetailMapper).insertInOutDocumentDetailList(detailDOList);
        verify(mockInOutDocumentMapper).submitInOutDocument("documentGuid");
    }

    @Test
    public void testUpdateAndSubmitInOutDocument_InOutDocumentDetailMapperSelectOutDocumentMaterialCountReturnsNoItems() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("documentGuid");
        inOutDocumentDTO.setType(0);

        // Configure InOutDocumentMapper.selectDocumentStatusAndInOutType(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectDocumentStatusAndInOutType("documentGuid")).thenReturn(inOutDocumentDO);

        // Configure IMaterialService.unitAdapterMain(...).
        final List<UnitConvertBO> unitConvertBOS = Arrays.asList(
                new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")));
        when(mockMaterialService.unitAdapterMain(
                Arrays.asList(new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")))))
                .thenReturn(unitConvertBOS);

        when(mockDetailMapper.selectOutDocumentMaterialCount("documentGuid")).thenReturn(Collections.emptyList());

        // Configure MaterialStockService.batchReduceStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchReduceStock(updateStockBOList)).thenReturn(false);

        // Configure InOutDocumentMapper.selectDocumentType(...).
        final InOutDocumentDO inOutDocumentDO1 = new InOutDocumentDO();
        inOutDocumentDO1.setStoreGuid("storeGuid");
        inOutDocumentDO1.setStatus(0);
        inOutDocumentDO1.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO1.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO1.setType(0);
        when(mockInOutDocumentMapper.selectDocumentType("documentGuid")).thenReturn(inOutDocumentDO1);

        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO2 = new InOutDocumentDO();
        inOutDocumentDO2.setStoreGuid("storeGuid");
        inOutDocumentDO2.setStatus(0);
        inOutDocumentDO2.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO2.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO2.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(inOutDocumentDO2);

        // Configure InOutDocumentDetailMapper.selectDocumentInAndReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectDocumentInAndReturnCount("contactDocumentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Run the test
        inOutDocumentServiceImplUnderTest.updateAndSubmitInOutDocument(inOutDocumentDTO);

        // Verify the results
        verify(mockInOutDocumentMapper).deleteInOutDocumentAndDetail("documentGuid");
        verify(mockInOutDocumentMapper).insertInOutDocument(any(InOutDocumentDO.class));

        // Confirm InOutDocumentDetailMapper.insertInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO1 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO1.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO1.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO1.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO1.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO1.setUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailDOList = Arrays.asList(inOutDocumentDetailDO1);
        verify(mockDetailMapper).insertInOutDocumentDetailList(detailDOList);

        // Confirm InOutDocumentDetailMapper.updateInDocumentDetailReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO2 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO2.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO2.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO2.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO2.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO2.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO2.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO2.setUnitGuid("unitGuid");
        inOutDocumentDetailDO2.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO2.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailList = Arrays.asList(inOutDocumentDetailDO2);
        verify(mockDetailMapper).updateInDocumentDetailReturnCount("contactDocumentGuid", detailList);
        verify(mockInOutDocumentMapper).submitInOutDocument("documentGuid");
    }

    @Test
    public void testUpdateAndSubmitInOutDocument_InOutDocumentMapperSelectInOutDocumentStatusReturnsNull() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("documentGuid");
        inOutDocumentDTO.setType(0);

        // Configure InOutDocumentMapper.selectDocumentStatusAndInOutType(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectDocumentStatusAndInOutType("documentGuid")).thenReturn(inOutDocumentDO);

        // Configure IMaterialService.unitAdapterMain(...).
        final List<UnitConvertBO> unitConvertBOS = Arrays.asList(
                new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")));
        when(mockMaterialService.unitAdapterMain(
                Arrays.asList(new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")))))
                .thenReturn(unitConvertBOS);

        // Configure InOutDocumentDetailMapper.selectOutDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectOutDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure MaterialStockService.batchReduceStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchReduceStock(updateStockBOList)).thenReturn(false);

        // Configure InOutDocumentMapper.selectDocumentType(...).
        final InOutDocumentDO inOutDocumentDO1 = new InOutDocumentDO();
        inOutDocumentDO1.setStoreGuid("storeGuid");
        inOutDocumentDO1.setStatus(0);
        inOutDocumentDO1.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO1.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO1.setType(0);
        when(mockInOutDocumentMapper.selectDocumentType("documentGuid")).thenReturn(inOutDocumentDO1);

        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(null);

        // Run the test
        assertThatThrownBy(
                () -> inOutDocumentServiceImplUnderTest.updateAndSubmitInOutDocument(inOutDocumentDTO))
                .isInstanceOf(ParameterException.class);
        verify(mockInOutDocumentMapper).deleteInOutDocumentAndDetail("documentGuid");
        verify(mockInOutDocumentMapper).insertInOutDocument(any(InOutDocumentDO.class));

        // Confirm InOutDocumentDetailMapper.insertInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO1 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO1.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO1.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO1.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO1.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO1.setUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailDOList = Arrays.asList(inOutDocumentDetailDO1);
        verify(mockDetailMapper).insertInOutDocumentDetailList(detailDOList);
    }

    @Test
    public void testUpdateAndSubmitInOutDocument_InOutDocumentDetailMapperSelectDocumentInAndReturnCountReturnsNoItems() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("documentGuid");
        inOutDocumentDTO.setType(0);

        // Configure InOutDocumentMapper.selectDocumentStatusAndInOutType(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectDocumentStatusAndInOutType("documentGuid")).thenReturn(inOutDocumentDO);

        // Configure IMaterialService.unitAdapterMain(...).
        final List<UnitConvertBO> unitConvertBOS = Arrays.asList(
                new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")));
        when(mockMaterialService.unitAdapterMain(
                Arrays.asList(new UnitConvertBO("materialGuid", "unitGuid", new BigDecimal("0.00")))))
                .thenReturn(unitConvertBOS);

        // Configure InOutDocumentDetailMapper.selectOutDocumentMaterialCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO = new InOutDocumentDetailDO();
        inOutDocumentDetailDO.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO.setUnitGuid("unitGuid");
        inOutDocumentDetailDO.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> inOutDocumentDetailDOS = Arrays.asList(inOutDocumentDetailDO);
        when(mockDetailMapper.selectOutDocumentMaterialCount("documentGuid")).thenReturn(inOutDocumentDetailDOS);

        // Configure MaterialStockService.batchReduceStock(...).
        final UpdateStockBO updateStockBO = new UpdateStockBO();
        updateStockBO.setMaterialGuid("materialGuid");
        updateStockBO.setCount(new BigDecimal("0.00"));
        updateStockBO.setMaterialUnit("materialUnit");
        updateStockBO.setWarehouseGuid("warehouseGuid");
        final List<UpdateStockBO> updateStockBOList = Arrays.asList(updateStockBO);
        when(mockMaterialStockService.batchReduceStock(updateStockBOList)).thenReturn(false);

        // Configure InOutDocumentMapper.selectDocumentType(...).
        final InOutDocumentDO inOutDocumentDO1 = new InOutDocumentDO();
        inOutDocumentDO1.setStoreGuid("storeGuid");
        inOutDocumentDO1.setStatus(0);
        inOutDocumentDO1.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO1.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO1.setType(0);
        when(mockInOutDocumentMapper.selectDocumentType("documentGuid")).thenReturn(inOutDocumentDO1);

        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO2 = new InOutDocumentDO();
        inOutDocumentDO2.setStoreGuid("storeGuid");
        inOutDocumentDO2.setStatus(0);
        inOutDocumentDO2.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO2.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO2.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("contactDocumentGuid")).thenReturn(inOutDocumentDO2);

        when(mockDetailMapper.selectDocumentInAndReturnCount("contactDocumentGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        inOutDocumentServiceImplUnderTest.updateAndSubmitInOutDocument(inOutDocumentDTO);

        // Verify the results
        verify(mockInOutDocumentMapper).deleteInOutDocumentAndDetail("documentGuid");
        verify(mockInOutDocumentMapper).insertInOutDocument(any(InOutDocumentDO.class));

        // Confirm InOutDocumentDetailMapper.insertInOutDocumentDetailList(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO1 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO1.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO1.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO1.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO1.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO1.setUnitGuid("unitGuid");
        inOutDocumentDetailDO1.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO1.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailDOList = Arrays.asList(inOutDocumentDetailDO1);
        verify(mockDetailMapper).insertInOutDocumentDetailList(detailDOList);

        // Confirm InOutDocumentDetailMapper.updateInDocumentDetailReturnCount(...).
        final InOutDocumentDetailDO inOutDocumentDetailDO2 = new InOutDocumentDetailDO();
        inOutDocumentDetailDO2.setMainUnitCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO2.setMainUnitName("mainUnitName");
        inOutDocumentDetailDO2.setMainUnitGuid("unitGuid");
        inOutDocumentDetailDO2.setGuid("66785357-d6e0-4f97-b5d4-aec3028f79e2");
        inOutDocumentDetailDO2.setDocumentGuid("documentGuid");
        inOutDocumentDetailDO2.setMaterialGuid("materialGuid");
        inOutDocumentDetailDO2.setUnitGuid("unitGuid");
        inOutDocumentDetailDO2.setCount(new BigDecimal("0.00"));
        inOutDocumentDetailDO2.setReturnCount(new BigDecimal("0.00"));
        final List<InOutDocumentDetailDO> detailList = Arrays.asList(inOutDocumentDetailDO2);
        verify(mockDetailMapper).updateInDocumentDetailReturnCount("contactDocumentGuid", detailList);
        verify(mockInOutDocumentMapper).submitInOutDocument("documentGuid");
    }

    @Test
    public void testUpdateAndSubmitInOutDocumentWithLock() {
        // Setup
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO.setDocumentGuid("documentGuid");
        inOutDocumentDTO.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO));
        inOutDocumentDTO.setGuid("documentGuid");
        inOutDocumentDTO.setType(0);

        when(mockRedisson.getLock("documentGuid")).thenReturn(null);

        // Run the test
        inOutDocumentServiceImplUnderTest.updateAndSubmitInOutDocumentWithLock(inOutDocumentDTO);

        // Verify the results
        // Confirm InOutDocumentService.updateAndSubmitInOutDocument(...).
        final InOutDocumentAddOrUpdateDTO inOutDocumentDTO1 = new InOutDocumentAddOrUpdateDTO();
        inOutDocumentDTO1.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDTO1.setInOutType(0);
        final InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailAddOrUpdateDTO1 = new InOutDocumentDetailAddOrUpdateDTO();
        inOutDocumentDetailAddOrUpdateDTO1.setGuid("e649a7f3-f3f9-468b-b35b-4a5da49017a3");
        inOutDocumentDetailAddOrUpdateDTO1.setDocumentGuid("documentGuid");
        inOutDocumentDTO1.setDetailList(Arrays.asList(inOutDocumentDetailAddOrUpdateDTO1));
        inOutDocumentDTO1.setGuid("documentGuid");
        inOutDocumentDTO1.setType(0);
        verify(mockInOutDocumentService).updateAndSubmitInOutDocument(inOutDocumentDTO1);
    }

    @Test
    public void testSelectSuppliersReconciliation() {
        // Setup
        final SuppliersReconciliationQueryDTO queryDTO = new SuppliersReconciliationQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setGuid("0829fe63-66c6-4216-80f5-e553f4eca8c5");
        queryDTO.setSupplierGuid("supplierGuid");
        queryDTO.setStatus(0);

        when(mockInOutDocumentMapper.selectSuppliersReconciliationTotal(
                any(SuppliersReconciliationQueryDO.class))).thenReturn(0L);

        // Configure InOutDocumentMapper.selectSuppliersReconciliationList(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        final List<InOutDocumentDO> inOutDocumentDOS = Arrays.asList(inOutDocumentDO);
        when(mockInOutDocumentMapper.selectSuppliersReconciliationList(
                any(SuppliersReconciliationQueryDO.class))).thenReturn(inOutDocumentDOS);

        // Run the test
        final Page<InOutDocumentSelectDTO> result = inOutDocumentServiceImplUnderTest.selectSuppliersReconciliation(
                queryDTO);

        // Verify the results
    }

    @Test
    public void testSelectSuppliersReconciliation_InOutDocumentMapperSelectSuppliersReconciliationListReturnsNoItems() {
        // Setup
        final SuppliersReconciliationQueryDTO queryDTO = new SuppliersReconciliationQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setGuid("0829fe63-66c6-4216-80f5-e553f4eca8c5");
        queryDTO.setSupplierGuid("supplierGuid");
        queryDTO.setStatus(0);

        when(mockInOutDocumentMapper.selectSuppliersReconciliationTotal(
                any(SuppliersReconciliationQueryDO.class))).thenReturn(0L);
        when(mockInOutDocumentMapper.selectSuppliersReconciliationList(
                any(SuppliersReconciliationQueryDO.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Page<InOutDocumentSelectDTO> result = inOutDocumentServiceImplUnderTest.selectSuppliersReconciliation(
                queryDTO);

        // Verify the results
    }

    @Test
    public void testSelectFlowDetailListForPage() {
        // Setup
        final InOutDocumentFlowDetailQueryDTO queryDTO = new InOutDocumentFlowDetailQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setWarehouseGuidList(Arrays.asList("value"));
        queryDTO.setStartDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        queryDTO.setEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure InOutDocumentDetailMapper.selectInOutDocumentFlowDetailList(...).
        final InOutDocumentFlowDetailDO inOutDocumentFlowDetailDO = new InOutDocumentFlowDetailDO();
        inOutDocumentFlowDetailDO.setInOutType(0);
        inOutDocumentFlowDetailDO.setGuid("763f7c6a-b119-4d3e-b861-c1153b37c74d");
        inOutDocumentFlowDetailDO.setMaterialGuid("materialGuid");
        inOutDocumentFlowDetailDO.setMaterialCode("materialCode");
        inOutDocumentFlowDetailDO.setMaterialName("materialName");
        final List<InOutDocumentFlowDetailDO> inOutDocumentFlowDetailDOS = Arrays.asList(inOutDocumentFlowDetailDO);
        when(mockDetailMapper.selectInOutDocumentFlowDetailList(any(InOutDocumentFlowDetailQuery.class)))
                .thenReturn(inOutDocumentFlowDetailDOS);

        when(mockDetailMapper.selectInOutDocumentFlowDetailCount(any(InOutDocumentFlowDetailQuery.class)))
                .thenReturn(0L);

        // Run the test
        final Page<InOutDocumentFolwDetailSelectDTO> result = inOutDocumentServiceImplUnderTest.selectFlowDetailListForPage(
                queryDTO);

        // Verify the results
    }

    @Test
    public void testSelectFlowDetailListForPage_InOutDocumentDetailMapperSelectInOutDocumentFlowDetailListReturnsNoItems() {
        // Setup
        final InOutDocumentFlowDetailQueryDTO queryDTO = new InOutDocumentFlowDetailQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setWarehouseGuidList(Arrays.asList("value"));
        queryDTO.setStartDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        queryDTO.setEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockDetailMapper.selectInOutDocumentFlowDetailList(any(InOutDocumentFlowDetailQuery.class)))
                .thenReturn(Collections.emptyList());
        when(mockDetailMapper.selectInOutDocumentFlowDetailCount(any(InOutDocumentFlowDetailQuery.class)))
                .thenReturn(0L);

        // Run the test
        final Page<InOutDocumentFolwDetailSelectDTO> result = inOutDocumentServiceImplUnderTest.selectFlowDetailListForPage(
                queryDTO);

        // Verify the results
    }

    @Test
    public void testSelectDocumentStatus() {
        // Setup
        // Configure InOutDocumentMapper.selectInOutDocumentStatus(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        when(mockInOutDocumentMapper.selectInOutDocumentStatus("f34de249-a524-4d2b-ad3d-993655913f7e"))
                .thenReturn(inOutDocumentDO);

        // Run the test
        final InOutDocumentDO result = inOutDocumentServiceImplUnderTest.selectDocumentStatus(
                "f34de249-a524-4d2b-ad3d-993655913f7e");

        // Verify the results
    }

    @Test
    public void testSelectSuppliersReconciliationTotalAmount() {
        // Setup
        final SuppliersReconciliationQueryDTO queryDTO = new SuppliersReconciliationQueryDTO();
        queryDTO.setCurrentPage(0);
        queryDTO.setPageSize(0);
        queryDTO.setGuid("0829fe63-66c6-4216-80f5-e553f4eca8c5");
        queryDTO.setSupplierGuid("supplierGuid");
        queryDTO.setStatus(0);

        when(mockInOutDocumentMapper.selectSuppliersReconciliationTotalAmount(
                any(SuppliersReconciliationQueryDO.class))).thenReturn(new BigDecimal("0.00"));

        // Run the test
        final BigDecimal result = inOutDocumentServiceImplUnderTest.selectSuppliersReconciliationTotalAmount(queryDTO);

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
    }

    @Test
    public void testSettleSuppliersReconciliation() {
        // Setup
        // Run the test
        final Boolean result = inOutDocumentServiceImplUnderTest.settleSuppliersReconciliation(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isTrue();
        verify(mockInOutDocumentMapper).settleSuppliersReconciliation(Arrays.asList("value"));
    }

    @Test
    public void testPublishOrderReduceStockMsg() {
        // Setup
        final OrderSkuDTO orderSkuDTO = new OrderSkuDTO();
        orderSkuDTO.setOut(false);
        orderSkuDTO.setEnterpriseGuid("enterpriseGuid");
        orderSkuDTO.setOperatorGuid("operatorGuid");
        orderSkuDTO.setOperatorName("operatorName");
        orderSkuDTO.setOrderId("orderId");

        // Run the test
        inOutDocumentServiceImplUnderTest.publishOrderReduceStockMsg(orderSkuDTO);

        // Verify the results
        verify(mockPublisher).publish(any(OrderSkuBO.class));
    }

    @Test
    public void testPublishOrderReduceStockMsgBatch() {
        // Setup
        final OrderSkuDTO orderSkuDTO = new OrderSkuDTO();
        orderSkuDTO.setOut(false);
        orderSkuDTO.setEnterpriseGuid("enterpriseGuid");
        orderSkuDTO.setOperatorGuid("operatorGuid");
        orderSkuDTO.setOperatorName("operatorName");
        orderSkuDTO.setOrderId("orderId");
        final List<OrderSkuDTO> orderSkuDTOList = Arrays.asList(orderSkuDTO);

        // Run the test
        inOutDocumentServiceImplUnderTest.publishOrderReduceStockMsgBatch(orderSkuDTOList);

        // Verify the results
        verify(mockPublisher).publish(any(OrderSkuBO.class));
    }

    @Test
    public void testCompleteMaterialInfo() {
        // Setup
        final InOutDocumentBomBO inOutDocumentBomBO = new InOutDocumentBomBO();
        inOutDocumentBomBO.setMaterialGuid("materialGuid");
        inOutDocumentBomBO.setUsage(new BigDecimal("0.00"));
        inOutDocumentBomBO.setUnit("unit");
        final List<InOutDocumentBomBO> inOutDocumentBomBOList = Arrays.asList(inOutDocumentBomBO);

        // Configure IMaterialService.findList(...).
        final MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setInUnitPrice(new BigDecimal("0.00"));
        materialDTO.setCategoryName("categoryName");
        materialDTO.setGuid("ff07141f-7642-49cb-907e-b17415b52509");
        materialDTO.setEnterpriseGuid("enterpriseGuid");
        materialDTO.setProperty("property");
        final List<MaterialDTO> materialDTOS = Arrays.asList(materialDTO);
        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value")))
                .thenReturn(materialDTOS);

        // Run the test
        final List<InOutDocumentDetailBO> result = inOutDocumentServiceImplUnderTest.completeMaterialInfo("storeGuid",
                "warehouseGuid", inOutDocumentBomBOList);

        // Verify the results
    }

    @Test
    public void testCompleteMaterialInfo_IMaterialServiceReturnsNull() {
        // Setup
        final InOutDocumentBomBO inOutDocumentBomBO = new InOutDocumentBomBO();
        inOutDocumentBomBO.setMaterialGuid("materialGuid");
        inOutDocumentBomBO.setUsage(new BigDecimal("0.00"));
        inOutDocumentBomBO.setUnit("unit");
        final List<InOutDocumentBomBO> inOutDocumentBomBOList = Arrays.asList(inOutDocumentBomBO);
        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value"))).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> inOutDocumentServiceImplUnderTest.completeMaterialInfo("storeGuid", "warehouseGuid",
                inOutDocumentBomBOList)).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testCompleteMaterialInfo_IMaterialServiceReturnsNoItems() {
        // Setup
        final InOutDocumentBomBO inOutDocumentBomBO = new InOutDocumentBomBO();
        inOutDocumentBomBO.setMaterialGuid("materialGuid");
        inOutDocumentBomBO.setUsage(new BigDecimal("0.00"));
        inOutDocumentBomBO.setUnit("unit");
        final List<InOutDocumentBomBO> inOutDocumentBomBOList = Arrays.asList(inOutDocumentBomBO);
        when(mockMaterialService.findList("storeGuid", "warehouseGuid", Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> inOutDocumentServiceImplUnderTest.completeMaterialInfo("storeGuid", "warehouseGuid",
                inOutDocumentBomBOList)).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSelectWarehouseGuidByDocumentGuid() {
        // Setup
        when(mockInOutDocumentMapper.selectWarehouseGuidByDocumentGuid("documentGuid")).thenReturn("result");

        // Run the test
        final String result = inOutDocumentServiceImplUnderTest.selectWarehouseGuidByDocumentGuid("documentGuid");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testSelectInOutDocumentCountByMaterial() {
        // Setup
        when(mockDetailMapper.selectInOutDocumentCountByMaterial("materialGuid")).thenReturn(0);

        // Run the test
        final boolean result = inOutDocumentServiceImplUnderTest.selectInOutDocumentCountByMaterial("materialGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testQueryDocumentDetailList() {
        // Setup
        final DocumentDetailListQueryDTO queryDTO = new DocumentDetailListQueryDTO();
        queryDTO.setDocumentGuidList(Arrays.asList("value"));

        // Configure InOutDocumentMapper.queryDocumentDetailList(...).
        final InOutDocumentDO inOutDocumentDO = new InOutDocumentDO();
        inOutDocumentDO.setStoreGuid("storeGuid");
        inOutDocumentDO.setStatus(0);
        inOutDocumentDO.setContactDocumentGuid("contactDocumentGuid");
        inOutDocumentDO.setWarehouseGuid("warehouseGuid");
        inOutDocumentDO.setType(0);
        final List<InOutDocumentDO> inOutDocumentDOS = Arrays.asList(inOutDocumentDO);
        final DocumentDetailListQueryDTO queryDTO1 = new DocumentDetailListQueryDTO();
        queryDTO1.setDocumentGuidList(Arrays.asList("value"));
        when(mockInOutDocumentMapper.queryDocumentDetailList(queryDTO1)).thenReturn(inOutDocumentDOS);

        // Run the test
        final List<InOutDocumentSelectDTO> result = inOutDocumentServiceImplUnderTest.queryDocumentDetailList(queryDTO);

        // Verify the results
    }

    @Test
    public void testQueryDocumentDetailList_InOutDocumentMapperReturnsNoItems() {
        // Setup
        final DocumentDetailListQueryDTO queryDTO = new DocumentDetailListQueryDTO();
        queryDTO.setDocumentGuidList(Arrays.asList("value"));

        // Configure InOutDocumentMapper.queryDocumentDetailList(...).
        final DocumentDetailListQueryDTO queryDTO1 = new DocumentDetailListQueryDTO();
        queryDTO1.setDocumentGuidList(Arrays.asList("value"));
        when(mockInOutDocumentMapper.queryDocumentDetailList(queryDTO1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<InOutDocumentSelectDTO> result = inOutDocumentServiceImplUnderTest.queryDocumentDetailList(queryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
