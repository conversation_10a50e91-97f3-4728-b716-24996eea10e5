package com.holderzone.erp.utils;

import org.junit.Test;

import java.util.Calendar;
import java.util.GregorianCalendar;

import static org.assertj.core.api.Assertions.assertThat;

public class DateUtilTest {

    @Test
    public void testGetCurrentDate() {
        assertThat(DateUtil.getCurrentDate()).isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testDateFormat() {
        assertThat(DateUtil.dateFormat(0L)).isEqualTo(0L);
    }
}
