package com.holderzone.erp.utils;

import org.junit.Test;

import static org.assertj.core.api.Assertions.*;

public class StdRandomTest {

    @Test
    public void testSetSeed() {
        // Setup
        // Run the test
        StdRandom.setSeed(0L);

        // Verify the results
    }

    @Test
    public void testGetSeed() {
        assertThat(StdRandom.getSeed()).isEqualTo(0L);
    }

    @Test
    public void testUniform1() {
        assertThat(StdRandom.uniform()).isEqualTo(0.0, within(0.0001));
    }

    @Test
    public void testUniform2() {
        assertThat(StdRandom.uniform(0)).isEqualTo(0);
        assertThatThrownBy(() -> StdRandom.uniform(0)).isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void testUniform3() {
        assertThat(StdRandom.uniform(0L)).isEqualTo(0L);
        assertThatThrownBy(() -> StdRandom.uniform(0L)).isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void testRandom() {
        assertThat(StdRandom.random()).isEqualTo(0.0, within(0.0001));
    }

    @Test
    public void testUniform4() {
        assertThat(StdRandom.uniform(0, 0)).isEqualTo(0);
        assertThatThrownBy(() -> StdRandom.uniform(0, 0)).isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void testUniform5() {
        assertThat(StdRandom.uniform(0.0, 0.0)).isEqualTo(0.0, within(0.0001));
        assertThatThrownBy(() -> StdRandom.uniform(0.0, 0.0)).isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void testBernoulli1() {
        assertThat(StdRandom.bernoulli(0.0)).isFalse();
        assertThatThrownBy(() -> StdRandom.bernoulli(0.0)).isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void testBernoulli2() {
        assertThat(StdRandom.bernoulli()).isFalse();
    }

    @Test
    public void testGaussian1() {
        assertThat(StdRandom.gaussian()).isEqualTo(0.0, within(0.0001));
    }

    @Test
    public void testGaussian2() {
        assertThat(StdRandom.gaussian(0.0, 0.0)).isEqualTo(0.0, within(0.0001));
    }

    @Test
    public void testGeometric() {
        assertThat(StdRandom.geometric(0.0)).isEqualTo(0);
        assertThatThrownBy(() -> StdRandom.geometric(0.0)).isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void testPoisson() {
        assertThat(StdRandom.poisson(0.0)).isEqualTo(0);
        assertThatThrownBy(() -> StdRandom.poisson(0.0)).isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void testPareto1() {
        assertThat(StdRandom.pareto()).isEqualTo(0.0, within(0.0001));
    }

    @Test
    public void testPareto2() {
        assertThat(StdRandom.pareto(0.0)).isEqualTo(0.0, within(0.0001));
        assertThatThrownBy(() -> StdRandom.pareto(0.0)).isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void testCauchy() {
        assertThat(StdRandom.cauchy()).isEqualTo(0.0, within(0.0001));
    }

    @Test
    public void testDiscrete1() {
        assertThat(StdRandom.discrete(new double[]{0.0})).isEqualTo(0);
        assertThatThrownBy(() -> StdRandom.discrete(new double[]{0.0})).isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void testDiscrete2() {
        assertThat(StdRandom.discrete(new int[]{0})).isEqualTo(0);
        assertThatThrownBy(() -> StdRandom.discrete(new int[]{0})).isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void testExp() {
        assertThat(StdRandom.exp(0.0)).isEqualTo(0.0, within(0.0001));
        assertThatThrownBy(() -> StdRandom.exp(0.0)).isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void testShuffle1() {
        // Setup
        // Run the test
        StdRandom.shuffle(new Object[]{"a"});

        // Verify the results
    }

    @Test
    public void testShuffle1_ThrowsIllegalArgumentException() {
        // Setup
        // Run the test
        assertThatThrownBy(() -> StdRandom.shuffle(new Object[]{"a"})).isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void testShuffle2() {
        // Setup
        // Run the test
        StdRandom.shuffle(new double[]{0.0});

        // Verify the results
    }

    @Test
    public void testShuffle2_ThrowsIllegalArgumentException() {
        // Setup
        // Run the test
        assertThatThrownBy(() -> StdRandom.shuffle(new double[]{0.0})).isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void testShuffle3() {
        // Setup
        // Run the test
        StdRandom.shuffle(new int[]{0});

        // Verify the results
    }

    @Test
    public void testShuffle3_ThrowsIllegalArgumentException() {
        // Setup
        // Run the test
        assertThatThrownBy(() -> StdRandom.shuffle(new int[]{0})).isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void testShuffle4() {
        // Setup
        // Run the test
        StdRandom.shuffle(new char[]{'a'});

        // Verify the results
    }

    @Test
    public void testShuffle4_ThrowsIllegalArgumentException() {
        // Setup
        // Run the test
        assertThatThrownBy(() -> StdRandom.shuffle(new char[]{'a'})).isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void testShuffle5() {
        // Setup
        // Run the test
        StdRandom.shuffle(new Object[]{"a"}, 0, 0);

        // Verify the results
    }

    @Test
    public void testShuffle5_ThrowsIllegalArgumentException() {
        // Setup
        // Run the test
        assertThatThrownBy(() -> StdRandom.shuffle(new Object[]{"a"}, 0, 0))
                .isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void testShuffle6() {
        // Setup
        // Run the test
        StdRandom.shuffle(new double[]{0.0}, 0, 0);

        // Verify the results
    }

    @Test
    public void testShuffle6_ThrowsIllegalArgumentException() {
        // Setup
        // Run the test
        assertThatThrownBy(() -> StdRandom.shuffle(new double[]{0.0}, 0, 0))
                .isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void testShuffle7() {
        // Setup
        // Run the test
        StdRandom.shuffle(new int[]{0}, 0, 0);

        // Verify the results
    }

    @Test
    public void testShuffle7_ThrowsIllegalArgumentException() {
        // Setup
        // Run the test
        assertThatThrownBy(() -> StdRandom.shuffle(new int[]{0}, 0, 0)).isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void testPermutation1() {
        assertThat(StdRandom.permutation(0)).isEqualTo(new int[]{0});
        assertThat(StdRandom.permutation(0)).isEqualTo(new int[]{});
        assertThatThrownBy(() -> StdRandom.permutation(0)).isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void testPermutation2() {
        assertThat(StdRandom.permutation(0, 0)).isEqualTo(new int[]{0});
        assertThat(StdRandom.permutation(0, 0)).isEqualTo(new int[]{});
        assertThatThrownBy(() -> StdRandom.permutation(0, 0)).isInstanceOf(IllegalArgumentException.class);
    }
}
