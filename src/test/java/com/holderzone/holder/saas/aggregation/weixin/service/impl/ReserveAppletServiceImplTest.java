package com.holderzone.holder.saas.aggregation.weixin.service.impl;

import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.ReserveClientService;
import com.holderzone.saas.store.reserve.api.dto.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ReserveAppletServiceImplTest {

    @Mock
    private ReserveClientService mockReserveClientService;

    private ReserveAppletServiceImpl reserveAppletServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        reserveAppletServiceImplUnderTest = new ReserveAppletServiceImpl(mockReserveClientService);
    }

    @Test
    public void testGetAvailableStoreList() {
        // Setup
        final ReserveAppletQueryDTO queryDTO = new ReserveAppletQueryDTO();
        queryDTO.setStoreGuids(Arrays.asList("value"));
        queryDTO.setReserveDate(LocalDate.of(2020, 1, 1));
        queryDTO.setAppletState("appletState");
        queryDTO.setStates(Arrays.asList(0));
        queryDTO.setCreateUserId("createUserId");

        final ReserveAvailableStoreDTO reserveAvailableStoreDTO = new ReserveAvailableStoreDTO();
        reserveAvailableStoreDTO.setStoreGuid("storeGuid");
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("db3863f9-db55-4ce2-a867-8c7680d44892");
        areaDTO.setName("name");
        areaDTO.setNum(0);
        reserveAvailableStoreDTO.setReserveAreaNames(Arrays.asList(areaDTO));
        final List<ReserveAvailableStoreDTO> expectedResult = Arrays.asList(reserveAvailableStoreDTO);

        // Configure ReserveClientService.getAvailableStoreList(...).
        final ReserveAvailableStoreDTO reserveAvailableStoreDTO1 = new ReserveAvailableStoreDTO();
        reserveAvailableStoreDTO1.setStoreGuid("storeGuid");
        final AreaDTO areaDTO1 = new AreaDTO();
        areaDTO1.setGuid("db3863f9-db55-4ce2-a867-8c7680d44892");
        areaDTO1.setName("name");
        areaDTO1.setNum(0);
        reserveAvailableStoreDTO1.setReserveAreaNames(Arrays.asList(areaDTO1));
        final List<ReserveAvailableStoreDTO> reserveAvailableStoreDTOS = Arrays.asList(reserveAvailableStoreDTO1);
        final ReserveAppletQueryDTO queryDTO1 = new ReserveAppletQueryDTO();
        queryDTO1.setStoreGuids(Arrays.asList("value"));
        queryDTO1.setReserveDate(LocalDate.of(2020, 1, 1));
        queryDTO1.setAppletState("appletState");
        queryDTO1.setStates(Arrays.asList(0));
        queryDTO1.setCreateUserId("createUserId");
        when(mockReserveClientService.getAvailableStoreList(queryDTO1)).thenReturn(reserveAvailableStoreDTOS);

        // Run the test
        final List<ReserveAvailableStoreDTO> result = reserveAppletServiceImplUnderTest.getAvailableStoreList(queryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetAvailableStoreList_ReserveClientServiceReturnsNoItems() {
        // Setup
        final ReserveAppletQueryDTO queryDTO = new ReserveAppletQueryDTO();
        queryDTO.setStoreGuids(Arrays.asList("value"));
        queryDTO.setReserveDate(LocalDate.of(2020, 1, 1));
        queryDTO.setAppletState("appletState");
        queryDTO.setStates(Arrays.asList(0));
        queryDTO.setCreateUserId("createUserId");

        // Configure ReserveClientService.getAvailableStoreList(...).
        final ReserveAppletQueryDTO queryDTO1 = new ReserveAppletQueryDTO();
        queryDTO1.setStoreGuids(Arrays.asList("value"));
        queryDTO1.setReserveDate(LocalDate.of(2020, 1, 1));
        queryDTO1.setAppletState("appletState");
        queryDTO1.setStates(Arrays.asList(0));
        queryDTO1.setCreateUserId("createUserId");
        when(mockReserveClientService.getAvailableStoreList(queryDTO1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ReserveAvailableStoreDTO> result = reserveAppletServiceImplUnderTest.getAvailableStoreList(queryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetAvailableStore() {
        // Setup
        final ReserveAppletQueryDTO queryDTO = new ReserveAppletQueryDTO();
        queryDTO.setStoreGuids(Arrays.asList("value"));
        queryDTO.setReserveDate(LocalDate.of(2020, 1, 1));
        queryDTO.setAppletState("appletState");
        queryDTO.setStates(Arrays.asList(0));
        queryDTO.setCreateUserId("createUserId");

        final ReserveAvailableStoreConfigDTO expectedResult = new ReserveAvailableStoreConfigDTO();
        expectedResult.setStoreGuid("storeGuid");
        final TimingSegmentDTO timingSegmentDTO = new TimingSegmentDTO();
        timingSegmentDTO.setStart(LocalTime.of(0, 0, 0));
        timingSegmentDTO.setEnd(LocalTime.of(0, 0, 0));
        timingSegmentDTO.setPeriod(0);
        expectedResult.setSegments(Arrays.asList(timingSegmentDTO));

        // Configure ReserveClientService.getAvailableStore(...).
        final ReserveAvailableStoreConfigDTO reserveAvailableStoreConfigDTO = new ReserveAvailableStoreConfigDTO();
        reserveAvailableStoreConfigDTO.setStoreGuid("storeGuid");
        final TimingSegmentDTO timingSegmentDTO1 = new TimingSegmentDTO();
        timingSegmentDTO1.setStart(LocalTime.of(0, 0, 0));
        timingSegmentDTO1.setEnd(LocalTime.of(0, 0, 0));
        timingSegmentDTO1.setPeriod(0);
        reserveAvailableStoreConfigDTO.setSegments(Arrays.asList(timingSegmentDTO1));
        final ReserveAppletQueryDTO queryDTO1 = new ReserveAppletQueryDTO();
        queryDTO1.setStoreGuids(Arrays.asList("value"));
        queryDTO1.setReserveDate(LocalDate.of(2020, 1, 1));
        queryDTO1.setAppletState("appletState");
        queryDTO1.setStates(Arrays.asList(0));
        queryDTO1.setCreateUserId("createUserId");
        when(mockReserveClientService.getAvailableStore(queryDTO1)).thenReturn(reserveAvailableStoreConfigDTO);

        // Run the test
        final ReserveAvailableStoreConfigDTO result = reserveAppletServiceImplUnderTest.getAvailableStore(queryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testLaunch() {
        // Setup
        final ReserveRecordDTO reserveRecordDTO = new ReserveRecordDTO();
        reserveRecordDTO.setGuid("84ab6c44-92db-40fc-b513-9a78bdaf3ae3");
        reserveRecordDTO.setStoreGuid("storeGuid");
        reserveRecordDTO.setNumber(0);
        reserveRecordDTO.setState("state");
        reserveRecordDTO.setName("name");

        // Configure ReserveClientService.launch(...).
        final ReserveRecordDTO reserveRecordDTO1 = new ReserveRecordDTO();
        reserveRecordDTO1.setGuid("84ab6c44-92db-40fc-b513-9a78bdaf3ae3");
        reserveRecordDTO1.setStoreGuid("storeGuid");
        reserveRecordDTO1.setNumber(0);
        reserveRecordDTO1.setState("state");
        reserveRecordDTO1.setName("name");
        when(mockReserveClientService.launch(reserveRecordDTO1)).thenReturn("result");

        // Run the test
        final String result = reserveAppletServiceImplUnderTest.launch(reserveRecordDTO);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }
}
