package com.holderzone.holder.saas.aggregation.app.controller.trade;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.aggregation.app.HolderSaasStoreAggregationAppApplication;
import com.holderzone.holder.saas.aggregation.app.controller.util.JsonFileUtil;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.UnsupportedEncodingException;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @date 2023/11/24
 * @description
 */
@Slf4j
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@RunWith(SpringRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@SpringBootTest(classes = HolderSaasStoreAggregationAppApplication.class)
public class DineInOrderAppControllerTest {

    private static final String USERINFO = "{\"operSubjectGuid\": \"2010121440477930009\"," +
            "\"enterpriseGuid\":" + " \"2009281531195930006\"," +
            "\"enterpriseName\": \"赵氏企业\"," +
            "\"enterpriseNo\": \"********\"," +
            "\"storeGuid\":" + " \"2106221850429620006\"," +
            "\"storeName\": \"交子大道测试门店\"," +
            "\"storeNo\": \"5796807\",\"userGuid\": " +
            "\"6787561298847596545\"," +
            "\"account\": \"196504\"," +
            "\"tel\": \"***********\"," +
            "\"name\": \"靓亮仔\"}\n";

    private static final String DINE_IN_ORDER = "/dine_in_order";

    private static final String RESPONSE = "response:";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void getOrderDetail() throws UnsupportedEncodingException {
        SingleDataDTO calculateReqDTO = JSON.parseObject(JsonFileUtil.read("dineInOrder/getOrderDetail.json"),
                SingleDataDTO.class);
        String calculateJsonString = JSON.toJSONString(calculateReqDTO);
        MvcResult calculateMvcResult = null;
        try {
            calculateMvcResult = mockMvc.perform(post(DINE_IN_ORDER + "/get_order_detail")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(calculateJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = calculateMvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void printItemRepeatOrder() throws UnsupportedEncodingException {
        CreateDineInOrderReqDTO printItemRepeatOrderReqDTO = JSON.parseObject(JsonFileUtil.read("dineInOrder/printItemRepeatOrder.json"),
                CreateDineInOrderReqDTO.class);
        String printItemRepeatOrderJsonString = JSON.toJSONString(printItemRepeatOrderReqDTO);
        MvcResult printItemRepeatOrderResult = null;
        try {
            printItemRepeatOrderResult = mockMvc.perform(post(DINE_IN_ORDER + "/print_item_repeat_order")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(printItemRepeatOrderJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = printItemRepeatOrderResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }
}