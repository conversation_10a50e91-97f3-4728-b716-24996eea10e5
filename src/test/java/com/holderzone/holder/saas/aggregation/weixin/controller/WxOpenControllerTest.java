package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxStoreTableClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.WxClientService;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.table.TableTagDTO;
import com.holderzone.saas.store.dto.weixin.req.WxQueryThirdPartUserInfoReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxThirdPartUserInfoReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxThirdPartUserInfoRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(WxOpenController.class)
public class WxOpenControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStoreTableClientService mockWxStoreTableClientService;
    @MockBean
    private WxClientService mockWxClientService;

    @Test
    public void testQueryTableByWeb() throws Exception {
        // Setup
        // Configure WxStoreTableClientService.queryTableByWeb(...).
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableTagDTO.setEnterpriseGuid("enterpriseGuid");
        tableTagDTO.setGuid("81004082-706d-40e1-bdec-26cc36d146ec");
        tableTagDTO.setTagName("tagName");
        tableTagDTO.setSort(0);
        final List<TableBasicDTO> tableBasicDTOS = Arrays.asList(
                new TableBasicDTO("13516a1e-cf6e-44b3-a70b-9717924ea96d", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableCode", 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), Arrays.asList("value"), Arrays.asList(tableTagDTO)));
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setEnterpriseGuid("enterpriseGuid");
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockWxStoreTableClientService.queryTableByWeb(tableBasicQueryDTO)).thenReturn(tableBasicDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_open/query_table_by_web")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryTableByWeb_WxStoreTableClientServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure WxStoreTableClientService.queryTableByWeb(...).
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setEnterpriseGuid("enterpriseGuid");
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockWxStoreTableClientService.queryTableByWeb(tableBasicQueryDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_open/query_table_by_web")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testSaveOrUpdateThirdPartUserInfo() throws Exception {
        // Setup
        // Configure WxClientService.saveOrUpdateThirdPartUserInfo(...).
        final WxThirdPartUserInfoReqDTO wxThirdPartUserInfoReqDTO = new WxThirdPartUserInfoReqDTO();
        wxThirdPartUserInfoReqDTO.setNickName("nickName");
        when(mockWxClientService.saveOrUpdateThirdPartUserInfo(wxThirdPartUserInfoReqDTO)).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_open/save_or_update_third_part_user_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testCheckThirdPartUserInfo() throws Exception {
        // Setup
        // Configure WxClientService.checkThirdPartUserInfo(...).
        final WxThirdPartUserInfoRespDTO wxThirdPartUserInfoRespDTO = new WxThirdPartUserInfoRespDTO();
        wxThirdPartUserInfoRespDTO.setGuid("bd2cc0da-8dc4-4562-a4c3-d23e35804ab2");
        wxThirdPartUserInfoRespDTO.setNickName("nickName");
        wxThirdPartUserInfoRespDTO.setOpenId("openId");
        wxThirdPartUserInfoRespDTO.setPhone("phone");
        wxThirdPartUserInfoRespDTO.setSource(0);
        final WxQueryThirdPartUserInfoReqDTO wxQueryThirdPartUserInfoReqDTO = new WxQueryThirdPartUserInfoReqDTO();
        wxQueryThirdPartUserInfoReqDTO.setOpenId("openId");
        wxQueryThirdPartUserInfoReqDTO.setTel("tel");
        wxQueryThirdPartUserInfoReqDTO.setSource(0);
        wxQueryThirdPartUserInfoReqDTO.setPassword("password");
        when(mockWxClientService.checkThirdPartUserInfo(wxQueryThirdPartUserInfoReqDTO))
                .thenReturn(wxThirdPartUserInfoRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_open/check_third_part_user_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryThirdPartUserInfo() throws Exception {
        // Setup
        // Configure WxClientService.queryThirdPartUserInfo(...).
        final WxThirdPartUserInfoRespDTO wxThirdPartUserInfoRespDTO = new WxThirdPartUserInfoRespDTO();
        wxThirdPartUserInfoRespDTO.setGuid("bd2cc0da-8dc4-4562-a4c3-d23e35804ab2");
        wxThirdPartUserInfoRespDTO.setNickName("nickName");
        wxThirdPartUserInfoRespDTO.setOpenId("openId");
        wxThirdPartUserInfoRespDTO.setPhone("phone");
        wxThirdPartUserInfoRespDTO.setSource(0);
        final WxQueryThirdPartUserInfoReqDTO wxQueryThirdPartUserInfoReqDTO = new WxQueryThirdPartUserInfoReqDTO();
        wxQueryThirdPartUserInfoReqDTO.setOpenId("openId");
        wxQueryThirdPartUserInfoReqDTO.setTel("tel");
        wxQueryThirdPartUserInfoReqDTO.setSource(0);
        wxQueryThirdPartUserInfoReqDTO.setPassword("password");
        when(mockWxClientService.queryThirdPartUserInfo(wxQueryThirdPartUserInfoReqDTO))
                .thenReturn(wxThirdPartUserInfoRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_open/query_third_part_user_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
