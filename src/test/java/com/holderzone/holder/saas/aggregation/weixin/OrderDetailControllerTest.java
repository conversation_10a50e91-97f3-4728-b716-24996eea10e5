package com.holderzone.holder.saas.aggregation.weixin;

import com.holderzone.holder.saas.aggregation.weixin.util.MockHttpUtil;
import com.holderzone.holder.saas.aggregation.weixin.utils.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.context.WebApplicationContext;


/**
 * 微信订单详情
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
@SpringBootTest(classes = HolderSaasAggregationWeixinApplication.class)
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class OrderDetailControllerTest {

    @Autowired
    private ApplicationContext ap;

    @Autowired
    private WebApplicationContext webApplicationContext;
    private MockMvc mockMvc;

    private static final String ORDER_DETAIL_REQUEST_MAPPING = "/deal/order";

    private static final String ORDER_GUID = "7118147906770567168";

    @Before
    public void setupMockMvc() {
        SpringContextUtil.getInstance().setCfgContext((ConfigurableApplicationContext) ap);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.webApplicationContext).build();
    }

    /**
     * 根据订单guid获取订单信息，订单未支付时不包含结算信息
     */
    @Test
    public void testDetail() {
        String content = MockHttpUtil.get(ORDER_DETAIL_REQUEST_MAPPING + "/detail/" + ORDER_GUID, new LinkedMultiValueMap<>(), mockMvc);
        log.info("根据订单guid获取订单信息返回参数:{}", content);
    }


    /**
     * 根据订单guid获取订单信息，包含结算信息
     */
    @Test
    public void testDetailSettlement() {
        String content = MockHttpUtil.get(ORDER_DETAIL_REQUEST_MAPPING + "/detail/settlement/" + ORDER_GUID, new LinkedMultiValueMap<>(), mockMvc);
        log.info("根据订单guid获取订单信息返回参数:{}", content);
    }
}
