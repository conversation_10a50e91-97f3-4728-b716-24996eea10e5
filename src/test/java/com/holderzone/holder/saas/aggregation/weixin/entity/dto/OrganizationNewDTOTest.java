package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import org.junit.Before;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class OrganizationNewDTOTest {

    private OrganizationNewDTO organizationNewDTOUnderTest;

    @Before
    public void setUp() throws Exception {
        organizationNewDTOUnderTest = new OrganizationNewDTO();
    }

    @Test
    public void testLinkGuidGetterAndSetter() {
        final String linkGuid = "linkGuid";
        organizationNewDTOUnderTest.setLinkGuid(linkGuid);
        assertThat(organizationNewDTOUnderTest.getLinkGuid()).isEqualTo(linkGuid);
    }

    @Test
    public void testOrganizationGuidGetterAndSetter() {
        final String organizationGuid = "organizationGuid";
        organizationNewDTOUnderTest.setOrganizationGuid(organizationGuid);
        assertThat(organizationNewDTOUnderTest.getOrganizationGuid()).isEqualTo(organizationGuid);
    }

    @Test
    public void testOrganizationCodeGetterAndSetter() {
        final String organizationCode = "organizationCode";
        organizationNewDTOUnderTest.setOrganizationCode(organizationCode);
        assertThat(organizationNewDTOUnderTest.getOrganizationCode()).isEqualTo(organizationCode);
    }

    @Test
    public void testNameGetterAndSetter() {
        final String name = "name";
        organizationNewDTOUnderTest.setName(name);
        assertThat(organizationNewDTOUnderTest.getName()).isEqualTo(name);
    }

    @Test
    public void testMultiMemberGuidGetterAndSetter() {
        final String multiMemberGuid = "multiMemberGuid";
        organizationNewDTOUnderTest.setMultiMemberGuid(multiMemberGuid);
        assertThat(organizationNewDTOUnderTest.getMultiMemberGuid()).isEqualTo(multiMemberGuid);
    }

    @Test
    public void testMultiMemberNameGetterAndSetter() {
        final String multiMemberName = "multiMemberName";
        organizationNewDTOUnderTest.setMultiMemberName(multiMemberName);
        assertThat(organizationNewDTOUnderTest.getMultiMemberName()).isEqualTo(multiMemberName);
    }

    @Test
    public void testAddressGetterAndSetter() {
        final String address = "address";
        organizationNewDTOUnderTest.setAddress(address);
        assertThat(organizationNewDTOUnderTest.getAddress()).isEqualTo(address);
    }

    @Test
    public void testProvinceGetterAndSetter() {
        final String province = "province";
        organizationNewDTOUnderTest.setProvince(province);
        assertThat(organizationNewDTOUnderTest.getProvince()).isEqualTo(province);
    }

    @Test
    public void testCityGetterAndSetter() {
        final String city = "city";
        organizationNewDTOUnderTest.setCity(city);
        assertThat(organizationNewDTOUnderTest.getCity()).isEqualTo(city);
    }

    @Test
    public void testDistrictGetterAndSetter() {
        final String district = "district";
        organizationNewDTOUnderTest.setDistrict(district);
        assertThat(organizationNewDTOUnderTest.getDistrict()).isEqualTo(district);
    }

    @Test
    public void testEnabledGetterAndSetter() {
        final Boolean enabled = false;
        organizationNewDTOUnderTest.setEnabled(enabled);
        assertThat(organizationNewDTOUnderTest.getEnabled()).isFalse();
    }

    @Test
    public void testEquals() {
        assertThat(organizationNewDTOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() {
        assertThat(organizationNewDTOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() {
        assertThat(organizationNewDTOUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() {
        assertThat(organizationNewDTOUnderTest.toString()).isEqualTo("result");
    }
}
