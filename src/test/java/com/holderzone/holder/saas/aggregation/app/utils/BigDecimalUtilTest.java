package com.holderzone.holder.saas.aggregation.app.utils;

import org.junit.Test;

import java.math.BigDecimal;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;

public class BigDecimalUtilTest {

    @Test
    public void testNegative() {
        assertEquals(new BigDecimal("0.00"), BigDecimalUtil.negative(new BigDecimal("0.00")));
    }

    @Test
    public void testGreaterThanZero() {
        assertFalse(BigDecimalUtil.greaterThanZero(new BigDecimal("0.00")));
    }

    @Test
    public void testEquelZero() {
        assertFalse(BigDecimalUtil.equelZero(new BigDecimal("0.00")));
    }

    @Test
    public void testLessThanZero() {
        assertFalse(BigDecimalUtil.lessThanZero(new BigDecimal("0.00")));
    }

    @Test
    public void testSetScale2() {
        assertEquals(new BigDecimal("0.00"), BigDecimalUtil.setScale2(new BigDecimal("0.00")));
    }

    @Test
    public void testQuantityTrimmed() {
        assertEquals("result", BigDecimalUtil.quantityTrimmed(new BigDecimal("0.00")));
    }

    @Test
    public void testLessThan() {
        assertFalse(BigDecimalUtil.lessThan(new BigDecimal("0.00"), new BigDecimal("0.00")));
    }

    @Test
    public void testLessEqual() {
        assertFalse(BigDecimalUtil.lessEqual(new BigDecimal("0.00"), new BigDecimal("0.00")));
    }

    @Test
    public void testGreaterThan() {
        assertFalse(BigDecimalUtil.greaterThan(new BigDecimal("0.00"), new BigDecimal("0.00")));
    }

    @Test
    public void testGreaterEqual() {
        assertFalse(BigDecimalUtil.greaterEqual(new BigDecimal("0.00"), new BigDecimal("0.00")));
    }

    @Test
    public void testEqual() {
        assertFalse(BigDecimalUtil.equal(new BigDecimal("0.00"), new BigDecimal("0.00")));
    }

    @Test
    public void testGetRealDiscount() {
        assertEquals(new BigDecimal("0.00"), BigDecimalUtil.getRealDiscount(new BigDecimal("0.00")));
    }

    @Test
    public void testNonNullValue() {
        assertEquals(new BigDecimal("0.00"), BigDecimalUtil.nonNullValue(new BigDecimal("0.00")));
    }

    @Test
    public void testDivide() {
        assertEquals(new BigDecimal("0.00"), BigDecimalUtil.divide(new BigDecimal("0.00"), new BigDecimal("0.00")));
    }

    @Test
    public void testMultiply2() {
        assertEquals(new BigDecimal("0.00"), BigDecimalUtil.multiply2(new BigDecimal("0.00"), new BigDecimal("0.00")));
    }
}
