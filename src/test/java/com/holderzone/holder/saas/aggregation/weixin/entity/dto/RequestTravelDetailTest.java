package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import org.junit.Before;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class RequestTravelDetailTest {

    private RequestTravelDetail requestTravelDetailUnderTest;

    @Before
    public void setUp() throws Exception {
        requestTravelDetailUnderTest = new RequestTravelDetail();
    }

    @Test
    public void testMemberInfoCardGuidGetterAndSetter() {
        final String memberInfoCardGuid = "memberInfoCardGuid";
        requestTravelDetailUnderTest.setMemberInfoCardGuid(memberInfoCardGuid);
        assertThat(requestTravelDetailUnderTest.getMemberInfoCardGuid()).isEqualTo(memberInfoCardGuid);
    }

    @Test
    public void testEquals() {
        assertThat(requestTravelDetailUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() {
        assertThat(requestTravelDetailUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() {
        assertThat(requestTravelDetailUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() {
        assertThat(requestTravelDetailUnderTest.toString()).isEqualTo("result");
    }
}
