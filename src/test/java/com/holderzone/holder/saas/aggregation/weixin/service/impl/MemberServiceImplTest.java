package com.holderzone.holder.saas.aggregation.weixin.service.impl;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.entity.dto.CheckVerifyVolumeRespDTO;
import com.holderzone.holder.saas.aggregation.weixin.helper.DineInItemHelper;
import com.holderzone.holder.saas.aggregation.weixin.helper.MarketingActivityHelper;
import com.holderzone.holder.saas.aggregation.weixin.helper.PriceCalculateHelper;
import com.holderzone.holder.saas.aggregation.weixin.service.ItemService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.HsmTerminalServiceClient;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.TradeClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.WxClientService;
import com.holderzone.holder.saas.aggregation.weixin.utils.UserMemberSessionUtils;
import com.holderzone.holder.saas.aggregation.weixin.utils.map.MemberCardItemMAP;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseIntegralOffset;
import com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseCardRight;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseCardRights;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberCardListOwned;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfo;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.entry.dto.WxUserInfoDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.weixin.deal.*;
import com.holderzone.saas.store.dto.weixin.member.WxMemberInfoVolumeDetailsRespDTO;
import com.holderzone.saas.store.dto.weixin.member.WxPrepayConfirmRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class MemberServiceImplTest {

    @InjectMocks
    private MemberServiceImpl memberService;

    @Mock
    private WxClientService wxClientService;
    @Mock
    private TradeClientService tradeClientService;
    @Mock
    private UserMemberSessionUtils userMemberSessionUtils;
    @Mock
    private HsaBaseClientService hsaBaseClientService;
    @Mock
    private HsmTerminalServiceClient terminalServiceClient;
    @Mock
    private ItemService itemService;
    @Mock
    private RedisUtils redisUtils;
    @Mock
    private MarketingActivityHelper marketingActivityHelper;
    @Mock
    private PriceCalculateHelper priceCalculateHelper;

    private WxMemberSessionDTO wxMemberSessionDTO;
    private UserMemberSessionDTO userMemberSessionDTO;

    @Before
    public void setup() {
        wxMemberSessionDTO = new WxMemberSessionDTO();
        wxMemberSessionDTO.setEnterpriseGuid("enterprise123");
        wxMemberSessionDTO.setStoreGuid("store123");
        wxMemberSessionDTO.setStoreName("TestStore");

        WxUserInfoDTO wxUserInfoDTO = new WxUserInfoDTO();
        wxUserInfoDTO.setOpenId("openId123");
        wxUserInfoDTO.setNickname("TestUser");
        wxMemberSessionDTO.setWxUserInfoDTO(wxUserInfoDTO);

        WeixinUserThreadLocal.put(JacksonUtils.writeValueAsString(wxMemberSessionDTO));

        userMemberSessionDTO = new UserMemberSessionDTO();
        userMemberSessionDTO.setMemberInfoCardGuid("memberCard123");
        userMemberSessionDTO.setVolumeCode("volume123");
        userMemberSessionDTO.setMemberIntegral(1);
    }

    @Test
    public void testCardPage() {
        // Arrange
        List<ResponseMemberCardListOwned> mockCardList = new ArrayList<>();
        ResponseMemberCardListOwned card = new ResponseMemberCardListOwned();
        card.setIsFreeze(true);
        mockCardList.add(card);

        when(hsaBaseClientService.getMemberCardByPage(any())).thenReturn(new com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel<>());
        when(userMemberSessionUtils.getUserMemberSession(any())).thenReturn(userMemberSessionDTO);
        when(hsaBaseClientService.getCardRightDetails(any())).thenReturn(new com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel<>(Collections.emptyList()));

        // Act
        List<MemberCardItemDTO> result = memberService.cardPage();

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testCardPage_WithValidCards() {
        // Arrange
        List<ResponseMemberCardListOwned> mockCardList = new ArrayList<>();
        ResponseMemberCardListOwned card = new ResponseMemberCardListOwned();
        card.setIsFreeze(false);
        card.setCardGuid("card123");
        card.setCardLevelGuid("level123");
        card.setMemberInfoCardGuid("memberCard123");
        mockCardList.add(card);

        List<ResponseCardRight> cardRights = new ArrayList<>();
        ResponseCardRight right = new ResponseCardRight();
        right.setName("会员价");
        cardRights.add(right);

        when(hsaBaseClientService.getMemberCardByPage(any())).thenReturn(new com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel<>());
        when(userMemberSessionUtils.getUserMemberSession(any())).thenReturn(userMemberSessionDTO);
        when(hsaBaseClientService.getCardRightDetails(any())).thenReturn(new com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel<>(cardRights));

        // Act
        List<MemberCardItemDTO> result = memberService.cardPage();

        // Assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals("card123", result.get(0).getCardGuid());
    }

    @Test
    public void testCardPage_WithException() {
        // Arrange
        when(hsaBaseClientService.getMemberCardByPage(any())).thenThrow(new RuntimeException("Test exception"));

        // Act
        List<MemberCardItemDTO> result = memberService.cardPage();

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testVolumeDetail() {
        // Arrange
        WxMemberInfoVolumeDetailsRespDTO mockVolumeDetails = new WxMemberInfoVolumeDetailsRespDTO();
        mockVolumeDetails.setVolumeType(0);
        mockVolumeDetails.setVolumeMoney(new BigDecimal("10.00"));
        mockVolumeDetails.setUseThreshold(0);
        mockVolumeDetails.setIsUseAlone(1);

        when(wxClientService.volumeCodeDetails(any())).thenReturn(mockVolumeDetails);

        // Act
        WxMemberInfoVolumeDetailsRespDTO result = memberService.volumeDetail(any());

        // Assert
        assertNotNull(result);
        assertNotNull(result.getVolumeDescList());
        assertTrue(result.getVolumeDescList().contains("无门槛"));
    }

    @Test
    public void testVolumeDetail_WithDifferentTypes() {
        // Test case 1: Cash voucher
        WxMemberInfoVolumeDetailsRespDTO cashVoucher = new WxMemberInfoVolumeDetailsRespDTO();
        cashVoucher.setVolumeType(0);
        cashVoucher.setVolumeMoney(new BigDecimal("50.00"));
        cashVoucher.setUseThreshold(100);
        cashVoucher.setUseThresholdFull(new BigDecimal("100.00"));
        cashVoucher.setMayUseNum("2");

        when(wxClientService.volumeCodeDetails(any())).thenReturn(cashVoucher);
        WxMemberInfoVolumeDetailsRespDTO result = memberService.volumeDetail(any());

        assertNotNull(result);
        assertTrue(result.getVolumeDescList().contains("代金券￥50.0"));
        assertTrue(result.getVolumeDescList().contains("满100.0可用"));
        assertTrue(result.getVolumeDescList().contains("每次可用2张"));

        // Test case 2: Product voucher
        WxMemberInfoVolumeDetailsRespDTO productVoucher = new WxMemberInfoVolumeDetailsRespDTO();
        productVoucher.setVolumeType(3);
        productVoucher.setUseThreshold(0);

        when(wxClientService.volumeCodeDetails(any())).thenReturn(productVoucher);
        result = memberService.volumeDetail(any());

        assertNotNull(result);
        assertTrue(result.getVolumeDescList().contains("商品券"));
        assertTrue(result.getVolumeDescList().contains("无门槛"));
    }

    @Test
    public void testCardRight() {
        // Arrange
        List<ResponseCardRight> mockRights = new ArrayList<>();
        when(hsaBaseClientService.getCardRightDetails(any())).thenReturn(new com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel<>(mockRights));

        // Act
        List<ResponseCardRight> result = memberService.cardRight(any());

        // Assert
        assertNotNull(result);
        assertEquals(mockRights, result);
    }

    @Test
    public void testQueryCardRightsInfo() {
        // Arrange
        ResponseCardRights mockRights = new ResponseCardRights();
        mockRights.setHasMemberPrice(1);
        mockRights.setHasMemberDiscount(1);

        when(redisUtils.get(any())).thenReturn(null);

    }

    @Test
    public void testQueryCardRightsInfo_WithCache() {
        // Arrange
        ResponseCardRights cachedRights = new ResponseCardRights();
        cachedRights.setHasMemberPrice(1);
        cachedRights.setHasMemberDiscount(0);

        when(redisUtils.get(any())).thenReturn(cachedRights);

    }

    @Test
    public void testVolumeList() {
        // Arrange
        ResponseMemberInfo mockMemberInfo = new ResponseMemberInfo();
        mockMemberInfo.setMemberInfoGuid("member123");

        when(hsaBaseClientService.getMemberInfo(any())).thenReturn(new com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel<>(mockMemberInfo));
        when(wxClientService.getMerchantOrderGuid(any())).thenReturn("merchantOrder123");
        when(userMemberSessionUtils.getUserMemberSession(any())).thenReturn(userMemberSessionDTO);

        List<ResponseVolumeList> mockVolumeList = new ArrayList<>();
        when(tradeClientService.verify(any())).thenReturn(mockVolumeList);

        // Act
        List<ResponseVolumeList> result = memberService.queryVolumeList(any());

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testCheckVolume() {
        // Test case 1: Empty volume list
        when(wxClientService.getMerchantOrderGuid(any())).thenReturn("merchantOrder123");
        when(hsaBaseClientService.consumeVolumeList(any())).thenReturn(new com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel<>(Collections.emptyList()));


        // Test case 2: Non-empty volume list
        List<ResponseVolumeList> volumes = Collections.singletonList(new ResponseVolumeList());
        when(hsaBaseClientService.consumeVolumeList(any())).thenReturn(new com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel<>());

    }

    @Test
    public void testConfirm_CardType() {
        // Arrange
        MemberConfirmReqDTO confirmReqDTO = new MemberConfirmReqDTO();
        confirmReqDTO.setType(1);
        confirmReqDTO.setMemberInfoCardGuid("card123");
        confirmReqDTO.setIntegralUck(true);

        when(userMemberSessionUtils.getUserMemberSession(any())).thenReturn(userMemberSessionDTO);

        // Act
        WxPrepayConfirmRespDTO result = memberService.confirm(confirmReqDTO);

        // Assert
        assertNotNull(result);
        verify(userMemberSessionUtils).addUserMemberSession(any());
    }

    @Test
    public void testConfirm_VolumeType() {
        // Arrange
        MemberConfirmReqDTO confirmReqDTO = new MemberConfirmReqDTO();
        confirmReqDTO.setType(2);
        confirmReqDTO.setVolumeCode("volume123");

        when(userMemberSessionUtils.getUserMemberSession(any())).thenReturn(userMemberSessionDTO);

        // Act
        WxPrepayConfirmRespDTO result = memberService.confirm(confirmReqDTO);

        // Assert
        assertNotNull(result);
        verify(userMemberSessionUtils).addUserMemberSession(any());
    }

    @Test
    public void testDiscount() {
        // Arrange
        ConcessionTotalReqDTO reqDTO = new ConcessionTotalReqDTO();
        reqDTO.setType(1);
        reqDTO.setEnableIntegral(1);

        DineinOrderDetailRespDTO mockResponse = new DineinOrderDetailRespDTO();
        List<DiscountFeeDetailDTO> discountDetails = new ArrayList<>();
        DiscountFeeDetailDTO detail = new DiscountFeeDetailDTO();
        detail.setDiscountType(1); // MEMBER type
        detail.setDiscountFee(new BigDecimal("10.00"));
        discountDetails.add(detail);
        mockResponse.setDiscountFeeDetailDTOS(discountDetails);

        when(wxClientService.getMerchantOrderGuid(any())).thenReturn("merchantOrder123");
        when(userMemberSessionUtils.getUserMemberSession(any())).thenReturn(userMemberSessionDTO);
        when(tradeClientService.calculate(any())).thenReturn(mockResponse);

        // Act
        ConcessionTotalRespDTO result = memberService.discount(reqDTO);

        // Assert
        assertNotNull(result);
        assertEquals(new BigDecimal("10.00"), result.getDiscountAmount());
    }

    @Test
    public void testDiscount_WithVolumeType() {
        // Arrange
        ConcessionTotalReqDTO reqDTO = new ConcessionTotalReqDTO();
        reqDTO.setType(2); // Volume type
        reqDTO.setVolumeCode("volume123");
        reqDTO.setOrderGuid("order123");

        DineinOrderDetailRespDTO mockResponse = new DineinOrderDetailRespDTO();
        List<DiscountFeeDetailDTO> discountDetails = new ArrayList<>();
        DiscountFeeDetailDTO detail = new DiscountFeeDetailDTO();
        detail.setDiscountType(7); // Volume type
        detail.setDiscountFee(new BigDecimal("20.00"));
        discountDetails.add(detail);
        mockResponse.setDiscountFeeDetailDTOS(discountDetails);

        when(wxClientService.getMerchantOrderGuid(any())).thenReturn("merchantOrder123");
        when(userMemberSessionUtils.getUserMemberSession(any())).thenReturn(userMemberSessionDTO);
        when(tradeClientService.calculate(any())).thenReturn(mockResponse);

        // Act
        ConcessionTotalRespDTO result = memberService.discount(reqDTO);

        // Assert
        assertNotNull(result);
        assertEquals(new BigDecimal("20.00"), result.getDiscountAmount());
    }

    @Test
    public void testVolumeList_WithValidVolumes() {
        // Arrange
        VolumePageReqDTO reqDTO = new VolumePageReqDTO();
        reqDTO.setOrderGuid("order123");

        ResponseMemberInfo mockMemberInfo = new ResponseMemberInfo();
        mockMemberInfo.setMemberInfoGuid("member123");

        List<com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList> mockVerifyList = new ArrayList<>();
        com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList volume = new com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList();
        volume.setVolumeState(0);
        volume.setUseable(true);
        volume.setVolumeCode("volume123");
        mockVerifyList.add(volume);

        when(hsaBaseClientService.getMemberInfo(any())).thenReturn(new com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel<>(mockMemberInfo));
        when(wxClientService.getMerchantOrderGuid(any())).thenReturn("merchantOrder123");
        when(userMemberSessionUtils.getUserMemberSession(any())).thenReturn(userMemberSessionDTO);
        when(tradeClientService.verify(any())).thenReturn(mockVerifyList);
        when(hsaBaseClientService.consumeVolumeList(any())).thenReturn(new com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel<>(Collections.emptyList()));

        // Act
        List<MemberVolumeItemDTO> result = memberService.volumeList(reqDTO);

        // Assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
    }

    @Test
    public void testCheckVolume_WithEmptyMerchantOrder() {
        // Arrange
        VolumePageReqDTO reqDTO = new VolumePageReqDTO();
        reqDTO.setOrderGuid("order123");

        when(wxClientService.getMerchantOrderGuid(any())).thenReturn("");

        // Act
        CheckVerifyVolumeRespDTO result = memberService.checkVolume(reqDTO);

        // Assert
        assertNotNull(result);
    }

    @Test
    public void testCheckVolume_WithConsumedVolumes() {
        // Arrange
        VolumePageReqDTO reqDTO = new VolumePageReqDTO();
        reqDTO.setOrderGuid("order123");

        List<ResponseVolumeList> consumedVolumes = new ArrayList<>();
        consumedVolumes.add(new ResponseVolumeList());

        when(wxClientService.getMerchantOrderGuid(any())).thenReturn("merchantOrder123");
        when(hsaBaseClientService.consumeVolumeList(any())).thenReturn(new com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel<>());

        // Act
        CheckVerifyVolumeRespDTO result = memberService.checkVolume(reqDTO);

        // Assert
        assertNotNull(result);
    }

    @Test
    public void testDiscount_WithCalculateError() {
        // Arrange
        ConcessionTotalReqDTO reqDTO = new ConcessionTotalReqDTO();
        reqDTO.setType(1);
        reqDTO.setOrderGuid("order123");

        when(wxClientService.getMerchantOrderGuid(any())).thenReturn("merchantOrder123");
        when(userMemberSessionUtils.getUserMemberSession(any())).thenReturn(userMemberSessionDTO);
        when(tradeClientService.calculate(any())).thenThrow(new RuntimeException("Calculate error"));

        // Act
        ConcessionTotalRespDTO result = memberService.discount(reqDTO);

        // Assert
        assertNotNull(result);
        assertEquals(BigDecimal.ZERO, result.getDiscountAmount());
    }

    @Test
    public void testDiscount_WithIntegralOffset() {
        // Arrange
        ConcessionTotalReqDTO reqDTO = new ConcessionTotalReqDTO();
        reqDTO.setType(1);
        reqDTO.setEnableIntegral(1);

        DineinOrderDetailRespDTO mockResponse = new DineinOrderDetailRespDTO();
        List<DiscountFeeDetailDTO> discountDetails = new ArrayList<>();

        // Add member discount
        DiscountFeeDetailDTO memberDiscount = new DiscountFeeDetailDTO();
        memberDiscount.setDiscountType(1); // MEMBER type
        memberDiscount.setDiscountFee(new BigDecimal("10.00"));
        discountDetails.add(memberDiscount);

        // Add integral discount
        DiscountFeeDetailDTO integralDiscount = new DiscountFeeDetailDTO();
        integralDiscount.setDiscountType(2); // POINTS_DEDUCTION type
        integralDiscount.setDiscountFee(new BigDecimal("5.00"));
        discountDetails.add(integralDiscount);

        mockResponse.setDiscountFeeDetailDTOS(discountDetails);

        ResponseIntegralOffset integralOffset = new ResponseIntegralOffset();
        integralOffset.setUseIntegral(500);
        integralOffset.setRuleState(1);
        integralOffset.setDeductionMoney(new BigDecimal("5.00"));
        mockResponse.setIntegralOffsetResultRespDTO(integralOffset);

        when(wxClientService.getMerchantOrderGuid(any())).thenReturn("merchantOrder123");
        when(userMemberSessionUtils.getUserMemberSession(any())).thenReturn(userMemberSessionDTO);
        when(tradeClientService.calculate(any())).thenReturn(mockResponse);

        // Act
        ConcessionTotalRespDTO result = memberService.discount(reqDTO);

        // Assert
        assertNotNull(result);
        assertEquals(new BigDecimal("5.00"), result.getIntegralFee());
        assertEquals(new BigDecimal("15.00"), result.getDiscountAmount()); // 10 + 5
    }

    @Test
    public void testVolumeList_WithNullMemberInfo() {
        // Arrange
        when(hsaBaseClientService.getMemberInfo(any())).thenReturn(new com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel<>());

        // Act
        List<MemberVolumeItemDTO> result = memberService.volumeList(new VolumePageReqDTO());

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(tradeClientService, never()).verify(any());
    }

    @Test
    public void testVolumeList_WithResponseVolumes() {
        // Arrange
        VolumePageReqDTO reqDTO = new VolumePageReqDTO();
        reqDTO.setOrderGuid("order123");

        ResponseMemberInfo mockMemberInfo = new ResponseMemberInfo();
        mockMemberInfo.setMemberInfoGuid("member123");

        List<ResponseVolumeList> responseVolumes = new ArrayList<>();
        ResponseVolumeList volume = new ResponseVolumeList();
        responseVolumes.add(volume);

        when(hsaBaseClientService.getMemberInfo(any())).thenReturn(new com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel<>(mockMemberInfo));
        when(wxClientService.getMerchantOrderGuid(any())).thenReturn("merchantOrder123");
        when(hsaBaseClientService.consumeVolumeList(any())).thenReturn(new com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel<>());

        // Act
        List<MemberVolumeItemDTO> result = memberService.volumeList(reqDTO);

        // Assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals("请至收银台撤销已选中券~", result.get(0).getTip());
    }

    @Test
    public void testQueryVolumeList_WithNullMemberInfo() {
        // Arrange
        VolumeOrderQueryDTO queryDTO = new VolumeOrderQueryDTO();
        when(hsaBaseClientService.getMemberInfo(any())).thenReturn(new com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel<>());

        // Act
        List<ResponseVolumeList> result = memberService.queryVolumeList(queryDTO);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(itemService, never()).setParentDishSkuInfo(any());
    }

    @Test
    public void testQueryVolumeList_WithEmptyDishList() {
        // Arrange
        VolumeOrderQueryDTO queryDTO = new VolumeOrderQueryDTO();
        queryDTO.setDineInItemList(Collections.emptyList());

        ResponseMemberInfo mockMemberInfo = new ResponseMemberInfo();
        mockMemberInfo.setMemberInfoGuid("member123");

        when(hsaBaseClientService.getMemberInfo(any())).thenReturn(new com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel<>(mockMemberInfo));
        when(userMemberSessionUtils.getUserMemberSession(any())).thenReturn(userMemberSessionDTO);

        // Act
        List<ResponseVolumeList> result = memberService.queryVolumeList(queryDTO);

        // Assert
        assertNotNull(result);
        verify(itemService).setParentDishSkuInfo(anyList());
        verify(terminalServiceClient).queryVolumePage(any());
    }

    @Test
    public void testConfirm_WithEmptyVolumeCode() {
        // Arrange
        MemberConfirmReqDTO confirmReqDTO = new MemberConfirmReqDTO();
        confirmReqDTO.setType(2);
        confirmReqDTO.setVolumeCode("");

        when(userMemberSessionUtils.getUserMemberSession(any())).thenReturn(userMemberSessionDTO);

        // Act
        WxPrepayConfirmRespDTO result = memberService.confirm(confirmReqDTO);

        // Assert
        assertNotNull(result);
        verify(userMemberSessionUtils).addUserMemberSession(any());
    }

    @Test
    public void testDiscount_WithNullResponse() {
        // Arrange
        ConcessionTotalReqDTO reqDTO = new ConcessionTotalReqDTO();
        reqDTO.setType(1);

        when(wxClientService.getMerchantOrderGuid(any())).thenReturn("merchantOrder123");
        when(userMemberSessionUtils.getUserMemberSession(any())).thenReturn(userMemberSessionDTO);
        when(tradeClientService.calculate(any())).thenReturn(null);

        // Act
        ConcessionTotalRespDTO result = memberService.discount(reqDTO);

        // Assert
        assertNotNull(result);
        assertEquals(BigDecimal.ZERO, result.getDiscountAmount());
    }

    @Test
    public void testDiscount_WithEmptyDiscountDetails() {
        // Arrange
        ConcessionTotalReqDTO reqDTO = new ConcessionTotalReqDTO();
        reqDTO.setType(1);

        DineinOrderDetailRespDTO mockResponse = new DineinOrderDetailRespDTO();
        mockResponse.setDiscountFeeDetailDTOS(Collections.emptyList());

        when(wxClientService.getMerchantOrderGuid(any())).thenReturn("merchantOrder123");
        when(userMemberSessionUtils.getUserMemberSession(any())).thenReturn(userMemberSessionDTO);
        when(tradeClientService.calculate(any())).thenReturn(mockResponse);

        // Act
        ConcessionTotalRespDTO result = memberService.discount(reqDTO);

        // Assert
        assertNotNull(result);
        assertEquals(BigDecimal.ZERO, result.getDiscountAmount());
        assertTrue(result.getMemberDiscountDTOS().isEmpty());
    }

    @Test
    public void testVolumeDetail_WithNullResponse() {
        // Arrange
        when(wxClientService.volumeCodeDetails(any())).thenReturn(null);

        // Act
        WxMemberInfoVolumeDetailsRespDTO result = memberService.volumeDetail(any());

        // Assert
        assertNull(result);
    }
}
