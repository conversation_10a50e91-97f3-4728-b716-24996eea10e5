package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class ReserveTableRespDTOTest {

    private ReserveTableRespDTO reserveTableRespDTOUnderTest;

    @Before
    public void setUp() throws Exception {
        reserveTableRespDTOUnderTest = new ReserveTableRespDTO("tableGuid", "tableCode", 0, Arrays.asList("value"));
    }

    @Test
    public void testTableGuidGetterAndSetter() {
        final String tableGuid = "tableGuid";
        reserveTableRespDTOUnderTest.setTableGuid(tableGuid);
        assertThat(reserveTableRespDTOUnderTest.getTableGuid()).isEqualTo(tableGuid);
    }

    @Test
    public void testTableCodeGetterAndSetter() {
        final String tableCode = "tableCode";
        reserveTableRespDTOUnderTest.setTableCode(tableCode);
        assertThat(reserveTableRespDTOUnderTest.getTableCode()).isEqualTo(tableCode);
    }

    @Test
    public void testSeatsGetterAndSetter() {
        final Integer seats = 0;
        reserveTableRespDTOUnderTest.setSeats(seats);
        assertThat(reserveTableRespDTOUnderTest.getSeats()).isEqualTo(seats);
    }

    @Test
    public void testTagsGetterAndSetter() {
        final List<String> tags = Arrays.asList("value");
        reserveTableRespDTOUnderTest.setTags(tags);
        assertThat(reserveTableRespDTOUnderTest.getTags()).isEqualTo(tags);
    }

    @Test
    public void testEquals() throws Exception {
        assertThat(reserveTableRespDTOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() throws Exception {
        assertThat(reserveTableRespDTOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() throws Exception {
        assertThat(reserveTableRespDTOUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() throws Exception {
        assertThat(reserveTableRespDTOUnderTest.toString()).isEqualTo("result");
    }
}
