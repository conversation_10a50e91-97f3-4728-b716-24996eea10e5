package com.holderzone.holder.saas.aggregation.weixin.service.impl;

import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.ItemClientService;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ItemServiceImplTest {

    @Mock
    private ItemClientService mockItemClientService;

    private ItemServiceImpl itemServiceImplUnderTest;

    @Before
    public void setUp() {
        itemServiceImplUnderTest = new ItemServiceImpl(mockItemClientService);
    }

    @Test
    public void testSetParentDishSkuInfo() {
        // Setup
        final RequestDishInfo requestDishInfo = new RequestDishInfo();
        requestDishInfo.setOrderItemGuid("orderItemGuid");
        requestDishInfo.setDishGuid("dishGuid");
        requestDishInfo.setParentDishGuid("parentDishGuid");
        requestDishInfo.setParentDishSpecification("parentDishSpecification");
        requestDishInfo.setDishSpecification("dishSpecification");
        final List<RequestDishInfo> dishInfoList = Arrays.asList(requestDishInfo);

        // Configure ItemClientService.selectItemInfoList(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setParentGuid("parentDishGuid");
        final SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setSkuGuid("skuGuid");
        skuInfoRespDTO.setParentGuid("parentDishSpecification");
        itemInfoRespDTO.setSkuList(Arrays.asList(skuInfoRespDTO));
        final List<ItemInfoRespDTO> itemInfoRespDTOS = Arrays.asList(itemInfoRespDTO);
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemClientService.selectItemInfoList(itemStringListDTO)).thenReturn(itemInfoRespDTOS);

        // Run the test
        itemServiceImplUnderTest.setParentDishSkuInfo(dishInfoList);

        // Verify the results
    }

    @Test
    public void testSetParentDishSkuInfo_ItemClientServiceReturnsNoItems() {
        // Setup
        final RequestDishInfo requestDishInfo = new RequestDishInfo();
        requestDishInfo.setOrderItemGuid("orderItemGuid");
        requestDishInfo.setDishGuid("dishGuid");
        requestDishInfo.setParentDishGuid("parentDishGuid");
        requestDishInfo.setParentDishSpecification("parentDishSpecification");
        requestDishInfo.setDishSpecification("dishSpecification");
        final List<RequestDishInfo> dishInfoList = Arrays.asList(requestDishInfo);

        // Configure ItemClientService.selectItemInfoList(...).
        final ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(Arrays.asList("value"));
        itemStringListDTO.setItemList(Arrays.asList("value"));
        itemStringListDTO.setRecordId(0L);
        when(mockItemClientService.selectItemInfoList(itemStringListDTO)).thenReturn(Collections.emptyList());

        // Run the test
        itemServiceImplUnderTest.setParentDishSkuInfo(dishInfoList);

        // Verify the results
    }
}
