package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.holder.saas.aggregation.app.service.feign.activity.ThirdActivityClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInOrderClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.GrouponClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.TradeThirdActivityClientService;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityRespDTO;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityTypeDTO;
import com.holderzone.saas.store.dto.member.activity.ThirdActivityInfoAndRecordRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.dto.trade.req.ThirdActivityRecordDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ThirdActivityServiceImplTest {

    @Mock
    private GrouponClientService mockGrouponClientService;
    @Mock
    private ThirdActivityClientService mockThirdActivityClientService;
    @Mock
    private TradeThirdActivityClientService mockTradeThirdActivityClientService;
    @Mock
    private DineInOrderClientService mockDineInOrderClientService;

    private ThirdActivityServiceImpl thirdActivityServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        thirdActivityServiceImplUnderTest = new ThirdActivityServiceImpl(mockGrouponClientService,
                mockThirdActivityClientService, mockTradeThirdActivityClientService, mockDineInOrderClientService);
    }

    @Test
    public void testListInfoAndRecord() {
        // Setup
        final ThirdActivityInfoAndRecordRespDTO thirdActivityInfoAndRecordRespDTO = new ThirdActivityInfoAndRecordRespDTO();
        thirdActivityInfoAndRecordRespDTO.setGuid("090d1faf-195b-4638-815c-5300d98f0dea");
        thirdActivityInfoAndRecordRespDTO.setThirdType("thirdType");
        thirdActivityInfoAndRecordRespDTO.setCouponFee(new BigDecimal("0.00"));
        thirdActivityInfoAndRecordRespDTO.setThirdActivityCodeList(Arrays.asList("value"));
        thirdActivityInfoAndRecordRespDTO.setJoinFee(new BigDecimal("0.00"));
        thirdActivityInfoAndRecordRespDTO.setNotJoinFee(new BigDecimal("0.00"));
        thirdActivityInfoAndRecordRespDTO.setDeductionAmount(new BigDecimal("0.00"));
        final List<ThirdActivityInfoAndRecordRespDTO> expectedResult = Arrays.asList(thirdActivityInfoAndRecordRespDTO);

        // Configure ThirdActivityClientService.list(...).
        final ThirdActivityRespDTO thirdActivityRespDTO = new ThirdActivityRespDTO();
        thirdActivityRespDTO.setGuid("2194dc5a-458f-43d0-89c0-f073c0f58b67");
        thirdActivityRespDTO.setActivityStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        thirdActivityRespDTO.setActivityEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        thirdActivityRespDTO.setThirdType("thirdType");
        thirdActivityRespDTO.setThirdName("thirdName");
        final List<ThirdActivityRespDTO> thirdActivityRespDTOS = Arrays.asList(thirdActivityRespDTO);
        when(mockThirdActivityClientService.list()).thenReturn(thirdActivityRespDTOS);

        // Configure GrouponClientService.list(...).
        final GrouponListRespDTO grouponListRespDTO = new GrouponListRespDTO();
        grouponListRespDTO.setOrderGuid("orderGuid");
        grouponListRespDTO.setAmount(new BigDecimal("0.00"));
        grouponListRespDTO.setGrouponType(0);
        grouponListRespDTO.setDeductionAmount(new BigDecimal("0.00"));
        grouponListRespDTO.setActivityGuid("activityGuid");
        final List<GrouponListRespDTO> grouponListRespDTOS = Arrays.asList(grouponListRespDTO);
        when(mockGrouponClientService.list("orderGuid", 0)).thenReturn(grouponListRespDTOS);

        // Configure TradeThirdActivityClientService.listThirdActivityByOrderGuid(...).
        final ThirdActivityRecordDTO thirdActivityRecordDTO = new ThirdActivityRecordDTO();
        thirdActivityRecordDTO.setActivityGuid("activityGuid");
        thirdActivityRecordDTO.setRuleType(0);
        thirdActivityRecordDTO.setThirdActivityCodeList(Arrays.asList("value"));
        thirdActivityRecordDTO.setJoinFee(new BigDecimal("0.00"));
        thirdActivityRecordDTO.setNotJoinFee(new BigDecimal("0.00"));
        thirdActivityRecordDTO.setCouponFee(new BigDecimal("0.00"));
        final List<ThirdActivityRecordDTO> thirdActivityRecordDTOS = Arrays.asList(thirdActivityRecordDTO);
        when(mockTradeThirdActivityClientService.listThirdActivityByOrderGuid("orderGuid"))
                .thenReturn(thirdActivityRecordDTOS);

        // Run the test
        final List<ThirdActivityInfoAndRecordRespDTO> result = thirdActivityServiceImplUnderTest.listInfoAndRecord(
                "orderGuid", 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListInfoAndRecord_ThirdActivityClientServiceReturnsNoItems() {
        // Setup
        final ThirdActivityInfoAndRecordRespDTO thirdActivityInfoAndRecordRespDTO = new ThirdActivityInfoAndRecordRespDTO();
        thirdActivityInfoAndRecordRespDTO.setGuid("090d1faf-195b-4638-815c-5300d98f0dea");
        thirdActivityInfoAndRecordRespDTO.setThirdType("thirdType");
        thirdActivityInfoAndRecordRespDTO.setCouponFee(new BigDecimal("0.00"));
        thirdActivityInfoAndRecordRespDTO.setThirdActivityCodeList(Arrays.asList("value"));
        thirdActivityInfoAndRecordRespDTO.setJoinFee(new BigDecimal("0.00"));
        thirdActivityInfoAndRecordRespDTO.setNotJoinFee(new BigDecimal("0.00"));
        thirdActivityInfoAndRecordRespDTO.setDeductionAmount(new BigDecimal("0.00"));
        final List<ThirdActivityInfoAndRecordRespDTO> expectedResult = Arrays.asList(thirdActivityInfoAndRecordRespDTO);
        when(mockThirdActivityClientService.list()).thenReturn(Collections.emptyList());

        // Configure GrouponClientService.list(...).
        final GrouponListRespDTO grouponListRespDTO = new GrouponListRespDTO();
        grouponListRespDTO.setOrderGuid("orderGuid");
        grouponListRespDTO.setAmount(new BigDecimal("0.00"));
        grouponListRespDTO.setGrouponType(0);
        grouponListRespDTO.setDeductionAmount(new BigDecimal("0.00"));
        grouponListRespDTO.setActivityGuid("activityGuid");
        final List<GrouponListRespDTO> grouponListRespDTOS = Arrays.asList(grouponListRespDTO);
        when(mockGrouponClientService.list("orderGuid", 0)).thenReturn(grouponListRespDTOS);

        // Configure TradeThirdActivityClientService.listThirdActivityByOrderGuid(...).
        final ThirdActivityRecordDTO thirdActivityRecordDTO = new ThirdActivityRecordDTO();
        thirdActivityRecordDTO.setActivityGuid("activityGuid");
        thirdActivityRecordDTO.setRuleType(0);
        thirdActivityRecordDTO.setThirdActivityCodeList(Arrays.asList("value"));
        thirdActivityRecordDTO.setJoinFee(new BigDecimal("0.00"));
        thirdActivityRecordDTO.setNotJoinFee(new BigDecimal("0.00"));
        thirdActivityRecordDTO.setCouponFee(new BigDecimal("0.00"));
        final List<ThirdActivityRecordDTO> thirdActivityRecordDTOS = Arrays.asList(thirdActivityRecordDTO);
        when(mockTradeThirdActivityClientService.listThirdActivityByOrderGuid("orderGuid"))
                .thenReturn(thirdActivityRecordDTOS);

        // Run the test
        final List<ThirdActivityInfoAndRecordRespDTO> result = thirdActivityServiceImplUnderTest.listInfoAndRecord(
                "orderGuid", 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListInfoAndRecord_GrouponClientServiceReturnsNoItems() {
        // Setup
        final ThirdActivityInfoAndRecordRespDTO thirdActivityInfoAndRecordRespDTO = new ThirdActivityInfoAndRecordRespDTO();
        thirdActivityInfoAndRecordRespDTO.setGuid("090d1faf-195b-4638-815c-5300d98f0dea");
        thirdActivityInfoAndRecordRespDTO.setThirdType("thirdType");
        thirdActivityInfoAndRecordRespDTO.setCouponFee(new BigDecimal("0.00"));
        thirdActivityInfoAndRecordRespDTO.setThirdActivityCodeList(Arrays.asList("value"));
        thirdActivityInfoAndRecordRespDTO.setJoinFee(new BigDecimal("0.00"));
        thirdActivityInfoAndRecordRespDTO.setNotJoinFee(new BigDecimal("0.00"));
        thirdActivityInfoAndRecordRespDTO.setDeductionAmount(new BigDecimal("0.00"));
        final List<ThirdActivityInfoAndRecordRespDTO> expectedResult = Arrays.asList(thirdActivityInfoAndRecordRespDTO);

        // Configure ThirdActivityClientService.list(...).
        final ThirdActivityRespDTO thirdActivityRespDTO = new ThirdActivityRespDTO();
        thirdActivityRespDTO.setGuid("2194dc5a-458f-43d0-89c0-f073c0f58b67");
        thirdActivityRespDTO.setActivityStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        thirdActivityRespDTO.setActivityEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        thirdActivityRespDTO.setThirdType("thirdType");
        thirdActivityRespDTO.setThirdName("thirdName");
        final List<ThirdActivityRespDTO> thirdActivityRespDTOS = Arrays.asList(thirdActivityRespDTO);
        when(mockThirdActivityClientService.list()).thenReturn(thirdActivityRespDTOS);

        when(mockGrouponClientService.list("orderGuid", 0)).thenReturn(Collections.emptyList());

        // Configure TradeThirdActivityClientService.listThirdActivityByOrderGuid(...).
        final ThirdActivityRecordDTO thirdActivityRecordDTO = new ThirdActivityRecordDTO();
        thirdActivityRecordDTO.setActivityGuid("activityGuid");
        thirdActivityRecordDTO.setRuleType(0);
        thirdActivityRecordDTO.setThirdActivityCodeList(Arrays.asList("value"));
        thirdActivityRecordDTO.setJoinFee(new BigDecimal("0.00"));
        thirdActivityRecordDTO.setNotJoinFee(new BigDecimal("0.00"));
        thirdActivityRecordDTO.setCouponFee(new BigDecimal("0.00"));
        final List<ThirdActivityRecordDTO> thirdActivityRecordDTOS = Arrays.asList(thirdActivityRecordDTO);
        when(mockTradeThirdActivityClientService.listThirdActivityByOrderGuid("orderGuid"))
                .thenReturn(thirdActivityRecordDTOS);

        // Run the test
        final List<ThirdActivityInfoAndRecordRespDTO> result = thirdActivityServiceImplUnderTest.listInfoAndRecord(
                "orderGuid", 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListInfoAndRecord_TradeThirdActivityClientServiceReturnsNoItems() {
        // Setup
        final ThirdActivityInfoAndRecordRespDTO thirdActivityInfoAndRecordRespDTO = new ThirdActivityInfoAndRecordRespDTO();
        thirdActivityInfoAndRecordRespDTO.setGuid("090d1faf-195b-4638-815c-5300d98f0dea");
        thirdActivityInfoAndRecordRespDTO.setThirdType("thirdType");
        thirdActivityInfoAndRecordRespDTO.setCouponFee(new BigDecimal("0.00"));
        thirdActivityInfoAndRecordRespDTO.setThirdActivityCodeList(Arrays.asList("value"));
        thirdActivityInfoAndRecordRespDTO.setJoinFee(new BigDecimal("0.00"));
        thirdActivityInfoAndRecordRespDTO.setNotJoinFee(new BigDecimal("0.00"));
        thirdActivityInfoAndRecordRespDTO.setDeductionAmount(new BigDecimal("0.00"));
        final List<ThirdActivityInfoAndRecordRespDTO> expectedResult = Arrays.asList(thirdActivityInfoAndRecordRespDTO);

        // Configure ThirdActivityClientService.list(...).
        final ThirdActivityRespDTO thirdActivityRespDTO = new ThirdActivityRespDTO();
        thirdActivityRespDTO.setGuid("2194dc5a-458f-43d0-89c0-f073c0f58b67");
        thirdActivityRespDTO.setActivityStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        thirdActivityRespDTO.setActivityEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        thirdActivityRespDTO.setThirdType("thirdType");
        thirdActivityRespDTO.setThirdName("thirdName");
        final List<ThirdActivityRespDTO> thirdActivityRespDTOS = Arrays.asList(thirdActivityRespDTO);
        when(mockThirdActivityClientService.list()).thenReturn(thirdActivityRespDTOS);

        // Configure GrouponClientService.list(...).
        final GrouponListRespDTO grouponListRespDTO = new GrouponListRespDTO();
        grouponListRespDTO.setOrderGuid("orderGuid");
        grouponListRespDTO.setAmount(new BigDecimal("0.00"));
        grouponListRespDTO.setGrouponType(0);
        grouponListRespDTO.setDeductionAmount(new BigDecimal("0.00"));
        grouponListRespDTO.setActivityGuid("activityGuid");
        final List<GrouponListRespDTO> grouponListRespDTOS = Arrays.asList(grouponListRespDTO);
        when(mockGrouponClientService.list("orderGuid", 0)).thenReturn(grouponListRespDTOS);

        when(mockTradeThirdActivityClientService.listThirdActivityByOrderGuid("orderGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ThirdActivityInfoAndRecordRespDTO> result = thirdActivityServiceImplUnderTest.listInfoAndRecord(
                "orderGuid", 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryThirdType() {
        // Setup
        final ThirdActivityTypeDTO thirdActivityTypeDTO = new ThirdActivityTypeDTO();
        thirdActivityTypeDTO.setThirdType("thirdType");
        thirdActivityTypeDTO.setThirdName("thirdName");
        thirdActivityTypeDTO.setSort(0);
        thirdActivityTypeDTO.setGroupBuyType(0);
        thirdActivityTypeDTO.setJoinFee(new BigDecimal("0.00"));
        final List<ThirdActivityTypeDTO> expectedResult = Arrays.asList(thirdActivityTypeDTO);

        // Configure DineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("705fc222-bf2d-4073-a975-da86196b26e1");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("696bd59b-56bd-4d79-ba00-e191fa7f0af3");
        dineinOrderDetailRespDTO.setReserveItems(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setTradeMode(0);
        when(mockDineInOrderClientService.getOrderDetail("orderGuid")).thenReturn(dineinOrderDetailRespDTO);

        // Configure GrouponClientService.list(...).
        final GrouponListRespDTO grouponListRespDTO = new GrouponListRespDTO();
        grouponListRespDTO.setOrderGuid("orderGuid");
        grouponListRespDTO.setAmount(new BigDecimal("0.00"));
        grouponListRespDTO.setGrouponType(0);
        grouponListRespDTO.setDeductionAmount(new BigDecimal("0.00"));
        grouponListRespDTO.setActivityGuid("activityGuid");
        final List<GrouponListRespDTO> grouponListRespDTOS = Arrays.asList(grouponListRespDTO);
        when(mockGrouponClientService.list("orderGuid", 0)).thenReturn(grouponListRespDTOS);

        // Configure ThirdActivityClientService.list(...).
        final ThirdActivityRespDTO thirdActivityRespDTO = new ThirdActivityRespDTO();
        thirdActivityRespDTO.setGuid("2194dc5a-458f-43d0-89c0-f073c0f58b67");
        thirdActivityRespDTO.setActivityStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        thirdActivityRespDTO.setActivityEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        thirdActivityRespDTO.setThirdType("thirdType");
        thirdActivityRespDTO.setThirdName("thirdName");
        final List<ThirdActivityRespDTO> thirdActivityRespDTOS = Arrays.asList(thirdActivityRespDTO);
        when(mockThirdActivityClientService.list()).thenReturn(thirdActivityRespDTOS);

        // Configure TradeThirdActivityClientService.listThirdActivityByOrderGuid(...).
        final ThirdActivityRecordDTO thirdActivityRecordDTO = new ThirdActivityRecordDTO();
        thirdActivityRecordDTO.setActivityGuid("activityGuid");
        thirdActivityRecordDTO.setRuleType(0);
        thirdActivityRecordDTO.setThirdActivityCodeList(Arrays.asList("value"));
        thirdActivityRecordDTO.setJoinFee(new BigDecimal("0.00"));
        thirdActivityRecordDTO.setNotJoinFee(new BigDecimal("0.00"));
        thirdActivityRecordDTO.setCouponFee(new BigDecimal("0.00"));
        final List<ThirdActivityRecordDTO> thirdActivityRecordDTOS = Arrays.asList(thirdActivityRecordDTO);
        when(mockTradeThirdActivityClientService.listThirdActivityByOrderGuid("orderGuid"))
                .thenReturn(thirdActivityRecordDTOS);

        // Run the test
        final List<ThirdActivityTypeDTO> result = thirdActivityServiceImplUnderTest.queryThirdType("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryThirdType_GrouponClientServiceReturnsNoItems() {
        // Setup
        // Configure DineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("705fc222-bf2d-4073-a975-da86196b26e1");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("696bd59b-56bd-4d79-ba00-e191fa7f0af3");
        dineinOrderDetailRespDTO.setReserveItems(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setTradeMode(0);
        when(mockDineInOrderClientService.getOrderDetail("orderGuid")).thenReturn(dineinOrderDetailRespDTO);

        when(mockGrouponClientService.list("orderGuid", 0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ThirdActivityTypeDTO> result = thirdActivityServiceImplUnderTest.queryThirdType("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryThirdType_ThirdActivityClientServiceReturnsNoItems() {
        // Setup
        // Configure DineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("705fc222-bf2d-4073-a975-da86196b26e1");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("696bd59b-56bd-4d79-ba00-e191fa7f0af3");
        dineinOrderDetailRespDTO.setReserveItems(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setTradeMode(0);
        when(mockDineInOrderClientService.getOrderDetail("orderGuid")).thenReturn(dineinOrderDetailRespDTO);

        when(mockThirdActivityClientService.list()).thenReturn(Collections.emptyList());

        // Run the test
        final List<ThirdActivityTypeDTO> result = thirdActivityServiceImplUnderTest.queryThirdType("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryThirdType_TradeThirdActivityClientServiceReturnsNoItems() {
        // Setup
        final ThirdActivityTypeDTO thirdActivityTypeDTO = new ThirdActivityTypeDTO();
        thirdActivityTypeDTO.setThirdType("thirdType");
        thirdActivityTypeDTO.setThirdName("thirdName");
        thirdActivityTypeDTO.setSort(0);
        thirdActivityTypeDTO.setGroupBuyType(0);
        thirdActivityTypeDTO.setJoinFee(new BigDecimal("0.00"));
        final List<ThirdActivityTypeDTO> expectedResult = Arrays.asList(thirdActivityTypeDTO);

        // Configure DineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("705fc222-bf2d-4073-a975-da86196b26e1");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("696bd59b-56bd-4d79-ba00-e191fa7f0af3");
        dineinOrderDetailRespDTO.setReserveItems(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setTradeMode(0);
        when(mockDineInOrderClientService.getOrderDetail("orderGuid")).thenReturn(dineinOrderDetailRespDTO);

        // Configure GrouponClientService.list(...).
        final GrouponListRespDTO grouponListRespDTO = new GrouponListRespDTO();
        grouponListRespDTO.setOrderGuid("orderGuid");
        grouponListRespDTO.setAmount(new BigDecimal("0.00"));
        grouponListRespDTO.setGrouponType(0);
        grouponListRespDTO.setDeductionAmount(new BigDecimal("0.00"));
        grouponListRespDTO.setActivityGuid("activityGuid");
        final List<GrouponListRespDTO> grouponListRespDTOS = Arrays.asList(grouponListRespDTO);
        when(mockGrouponClientService.list("orderGuid", 0)).thenReturn(grouponListRespDTOS);

        // Configure ThirdActivityClientService.list(...).
        final ThirdActivityRespDTO thirdActivityRespDTO = new ThirdActivityRespDTO();
        thirdActivityRespDTO.setGuid("2194dc5a-458f-43d0-89c0-f073c0f58b67");
        thirdActivityRespDTO.setActivityStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        thirdActivityRespDTO.setActivityEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        thirdActivityRespDTO.setThirdType("thirdType");
        thirdActivityRespDTO.setThirdName("thirdName");
        final List<ThirdActivityRespDTO> thirdActivityRespDTOS = Arrays.asList(thirdActivityRespDTO);
        when(mockThirdActivityClientService.list()).thenReturn(thirdActivityRespDTOS);

        when(mockTradeThirdActivityClientService.listThirdActivityByOrderGuid("orderGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ThirdActivityTypeDTO> result = thirdActivityServiceImplUnderTest.queryThirdType("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
