package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.google.common.util.concurrent.MoreExecutors;
import com.holderzone.holder.saas.aggregation.app.entity.auth.*;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.account.MemberDataClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.reserve.ReserveClient;
import com.holderzone.holder.saas.aggregation.app.service.feign.staff.UserClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.takeout.TakeoutDailyClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DinnerDailyClientService;
import com.holderzone.holder.saas.member.terminal.dto.statistics.RequestMemberDaily;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponseConsumpStatis;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponsePayWayDetail;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponseRechargeStatis;
import com.holderzone.saas.store.dto.business.manage.TakeoutStatsDTO;
import com.holderzone.saas.store.dto.business.manage.TakeoutStatsQueryDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.*;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BusinessDailyServiceImplTest {

    @Mock
    private DinnerDailyClientService mockDinnerDailyClientService;
    @Mock
    private TakeoutDailyClientService mockTakeoutDailyClientService;
    @Mock
    private MemberDataClientService mockDataClientService;
    @Mock
    private ReserveClient mockReserveClient;
    @Mock
    private UserClientService mockUserClientService;

    private BusinessDailyServiceImpl businessDailyServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        businessDailyServiceImplUnderTest = new BusinessDailyServiceImpl(mockDinnerDailyClientService,
                mockTakeoutDailyClientService, mockDataClientService, mockReserveClient, mockUserClientService,
                MoreExecutors.directExecutor());
    }

    @Test
    public void testDiningType() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final DiningTypeRespDTO diningTypeRespDTO = new DiningTypeRespDTO();
        diningTypeRespDTO.setTypeCode(0);
        diningTypeRespDTO.setTypeName("合计");
        diningTypeRespDTO.setOrderCount(0);
        diningTypeRespDTO.setGuestCount(0);
        diningTypeRespDTO.setAmount(new BigDecimal("0.00"));
        diningTypeRespDTO.setOrderPrice(new BigDecimal("0.00"));
        diningTypeRespDTO.setGuestPrice(new BigDecimal("0.00"));
        diningTypeRespDTO.setSubDiningTypes(Arrays.asList(new DiningTypeRespDTO()));
        diningTypeRespDTO.setIsTotal(0);
        final List<DiningTypeRespDTO> expectedResult = Arrays.asList(diningTypeRespDTO);

        // Configure DinnerDailyClientService.diningType(...).
        final DiningTypeRespDTO diningTypeRespDTO1 = new DiningTypeRespDTO();
        diningTypeRespDTO1.setTypeCode(0);
        diningTypeRespDTO1.setTypeName("合计");
        diningTypeRespDTO1.setOrderCount(0);
        diningTypeRespDTO1.setGuestCount(0);
        diningTypeRespDTO1.setAmount(new BigDecimal("0.00"));
        diningTypeRespDTO1.setOrderPrice(new BigDecimal("0.00"));
        diningTypeRespDTO1.setGuestPrice(new BigDecimal("0.00"));
        diningTypeRespDTO1.setSubDiningTypes(Arrays.asList(new DiningTypeRespDTO()));
        diningTypeRespDTO1.setIsTotal(0);
        final List<DiningTypeRespDTO> diningTypeRespDTOS = Arrays.asList(diningTypeRespDTO1);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.diningType(request1)).thenReturn(diningTypeRespDTOS);

        // Configure TakeoutDailyClientService.getTradeStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getTradeStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test
        final List<DiningTypeRespDTO> result = businessDailyServiceImplUnderTest.diningType(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDiningType_DinnerDailyClientServiceReturnsNull() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final DiningTypeRespDTO diningTypeRespDTO = new DiningTypeRespDTO();
        diningTypeRespDTO.setTypeCode(0);
        diningTypeRespDTO.setTypeName("合计");
        diningTypeRespDTO.setOrderCount(0);
        diningTypeRespDTO.setGuestCount(0);
        diningTypeRespDTO.setAmount(new BigDecimal("0.00"));
        diningTypeRespDTO.setOrderPrice(new BigDecimal("0.00"));
        diningTypeRespDTO.setGuestPrice(new BigDecimal("0.00"));
        diningTypeRespDTO.setSubDiningTypes(Arrays.asList(new DiningTypeRespDTO()));
        diningTypeRespDTO.setIsTotal(0);
        final List<DiningTypeRespDTO> expectedResult = Arrays.asList(diningTypeRespDTO);

        // Configure DinnerDailyClientService.diningType(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.diningType(request1)).thenReturn(null);

        // Configure TakeoutDailyClientService.getTradeStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getTradeStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test
        final List<DiningTypeRespDTO> result = businessDailyServiceImplUnderTest.diningType(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDiningType_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        // Configure DinnerDailyClientService.diningType(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.diningType(request1)).thenReturn(Collections.emptyList());

        // Configure TakeoutDailyClientService.getTradeStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getTradeStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test
        final List<DiningTypeRespDTO> result = businessDailyServiceImplUnderTest.diningType(request);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testDiningType_TakeoutDailyClientServiceReturnsNull() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final DiningTypeRespDTO diningTypeRespDTO = new DiningTypeRespDTO();
        diningTypeRespDTO.setTypeCode(0);
        diningTypeRespDTO.setTypeName("合计");
        diningTypeRespDTO.setOrderCount(0);
        diningTypeRespDTO.setGuestCount(0);
        diningTypeRespDTO.setAmount(new BigDecimal("0.00"));
        diningTypeRespDTO.setOrderPrice(new BigDecimal("0.00"));
        diningTypeRespDTO.setGuestPrice(new BigDecimal("0.00"));
        diningTypeRespDTO.setSubDiningTypes(Arrays.asList(new DiningTypeRespDTO()));
        diningTypeRespDTO.setIsTotal(0);
        final List<DiningTypeRespDTO> expectedResult = Arrays.asList(diningTypeRespDTO);

        // Configure DinnerDailyClientService.diningType(...).
        final DiningTypeRespDTO diningTypeRespDTO1 = new DiningTypeRespDTO();
        diningTypeRespDTO1.setTypeCode(0);
        diningTypeRespDTO1.setTypeName("合计");
        diningTypeRespDTO1.setOrderCount(0);
        diningTypeRespDTO1.setGuestCount(0);
        diningTypeRespDTO1.setAmount(new BigDecimal("0.00"));
        diningTypeRespDTO1.setOrderPrice(new BigDecimal("0.00"));
        diningTypeRespDTO1.setGuestPrice(new BigDecimal("0.00"));
        diningTypeRespDTO1.setSubDiningTypes(Arrays.asList(new DiningTypeRespDTO()));
        diningTypeRespDTO1.setIsTotal(0);
        final List<DiningTypeRespDTO> diningTypeRespDTOS = Arrays.asList(diningTypeRespDTO1);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.diningType(request1)).thenReturn(diningTypeRespDTOS);

        // Configure TakeoutDailyClientService.getTradeStats(...).
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getTradeStats(takeoutStatsQueryDTO)).thenReturn(null);

        // Run the test
        final List<DiningTypeRespDTO> result = businessDailyServiceImplUnderTest.diningType(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDiningTypeSale() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final DiningTypeSaleDTO diningTypeSaleDTO = new DiningTypeSaleDTO();
        diningTypeSaleDTO.setTypeCode("typeCode");
        diningTypeSaleDTO.setTypeName("typeName");
        diningTypeSaleDTO.setOrderCount("orderCount");
        diningTypeSaleDTO.setGuestCount("guestCount");
        diningTypeSaleDTO.setAmount("amount");
        final List<DiningTypeSaleDTO> expectedResult = Arrays.asList(diningTypeSaleDTO);

        // Configure DinnerDailyClientService.diningType(...).
        final DiningTypeRespDTO diningTypeRespDTO = new DiningTypeRespDTO();
        diningTypeRespDTO.setTypeCode(0);
        diningTypeRespDTO.setTypeName("合计");
        diningTypeRespDTO.setOrderCount(0);
        diningTypeRespDTO.setGuestCount(0);
        diningTypeRespDTO.setAmount(new BigDecimal("0.00"));
        diningTypeRespDTO.setOrderPrice(new BigDecimal("0.00"));
        diningTypeRespDTO.setGuestPrice(new BigDecimal("0.00"));
        diningTypeRespDTO.setSubDiningTypes(Arrays.asList(new DiningTypeRespDTO()));
        diningTypeRespDTO.setIsTotal(0);
        final List<DiningTypeRespDTO> diningTypeRespDTOS = Arrays.asList(diningTypeRespDTO);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.diningType(request1)).thenReturn(diningTypeRespDTOS);

        // Configure TakeoutDailyClientService.getTradeStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getTradeStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test
        final List<DiningTypeSaleDTO> result = businessDailyServiceImplUnderTest.diningTypeSale(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDiningTypeSale_DinnerDailyClientServiceReturnsNull() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final DiningTypeSaleDTO diningTypeSaleDTO = new DiningTypeSaleDTO();
        diningTypeSaleDTO.setTypeCode("typeCode");
        diningTypeSaleDTO.setTypeName("typeName");
        diningTypeSaleDTO.setOrderCount("orderCount");
        diningTypeSaleDTO.setGuestCount("guestCount");
        diningTypeSaleDTO.setAmount("amount");
        final List<DiningTypeSaleDTO> expectedResult = Arrays.asList(diningTypeSaleDTO);

        // Configure DinnerDailyClientService.diningType(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.diningType(request1)).thenReturn(null);

        // Configure TakeoutDailyClientService.getTradeStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getTradeStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test
        final List<DiningTypeSaleDTO> result = businessDailyServiceImplUnderTest.diningTypeSale(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDiningTypeSale_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        // Configure DinnerDailyClientService.diningType(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.diningType(request1)).thenReturn(Collections.emptyList());

        // Configure TakeoutDailyClientService.getTradeStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getTradeStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test
        final List<DiningTypeSaleDTO> result = businessDailyServiceImplUnderTest.diningTypeSale(request);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testDiningTypeSale_TakeoutDailyClientServiceReturnsNull() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final DiningTypeSaleDTO diningTypeSaleDTO = new DiningTypeSaleDTO();
        diningTypeSaleDTO.setTypeCode("typeCode");
        diningTypeSaleDTO.setTypeName("typeName");
        diningTypeSaleDTO.setOrderCount("orderCount");
        diningTypeSaleDTO.setGuestCount("guestCount");
        diningTypeSaleDTO.setAmount("amount");
        final List<DiningTypeSaleDTO> expectedResult = Arrays.asList(diningTypeSaleDTO);

        // Configure DinnerDailyClientService.diningType(...).
        final DiningTypeRespDTO diningTypeRespDTO = new DiningTypeRespDTO();
        diningTypeRespDTO.setTypeCode(0);
        diningTypeRespDTO.setTypeName("合计");
        diningTypeRespDTO.setOrderCount(0);
        diningTypeRespDTO.setGuestCount(0);
        diningTypeRespDTO.setAmount(new BigDecimal("0.00"));
        diningTypeRespDTO.setOrderPrice(new BigDecimal("0.00"));
        diningTypeRespDTO.setGuestPrice(new BigDecimal("0.00"));
        diningTypeRespDTO.setSubDiningTypes(Arrays.asList(new DiningTypeRespDTO()));
        diningTypeRespDTO.setIsTotal(0);
        final List<DiningTypeRespDTO> diningTypeRespDTOS = Arrays.asList(diningTypeRespDTO);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.diningType(request1)).thenReturn(diningTypeRespDTOS);

        // Configure TakeoutDailyClientService.getTradeStats(...).
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getTradeStats(takeoutStatsQueryDTO)).thenReturn(null);

        // Run the test
        final List<DiningTypeSaleDTO> result = businessDailyServiceImplUnderTest.diningTypeSale(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testClassify() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final ItemRespDTO itemRespDTO = new ItemRespDTO();
        itemRespDTO.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO.setName("");
        itemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO.setQuantum(new BigDecimal("0.00"));
        itemRespDTO.setAmount(new BigDecimal("0.00"));
        itemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setIsTotal(0);
        itemRespDTO.setHasSubInfo(0);
        itemRespDTO.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO.setSkuName("skuName");
        itemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> expectedResult = Arrays.asList(itemRespDTO);

        // Configure DinnerDailyClientService.classify(...).
        final ItemRespDTO itemRespDTO1 = new ItemRespDTO();
        itemRespDTO1.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO1.setName("");
        itemRespDTO1.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO1.setQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setAmount(new BigDecimal("0.00"));
        itemRespDTO1.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setIsTotal(0);
        itemRespDTO1.setHasSubInfo(0);
        itemRespDTO1.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO1.setSkuName("skuName");
        itemRespDTO1.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO1.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS = Arrays.asList(itemRespDTO1);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.classify(request1)).thenReturn(itemRespDTOS);

        // Configure TakeoutDailyClientService.listItemSale(...).
        final ItemRespDTO itemRespDTO2 = new ItemRespDTO();
        itemRespDTO2.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO2.setName("");
        itemRespDTO2.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO2.setQuantum(new BigDecimal("0.00"));
        itemRespDTO2.setAmount(new BigDecimal("0.00"));
        itemRespDTO2.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO2.setIsTotal(0);
        itemRespDTO2.setHasSubInfo(0);
        itemRespDTO2.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO2.setSkuName("skuName");
        itemRespDTO2.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO2.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO2.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO2.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO2.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO2.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO2.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS1 = Arrays.asList(itemRespDTO2);
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.listItemSale(takeoutStatsQueryDTO)).thenReturn(itemRespDTOS1);

        // Run the test
        final List<ItemRespDTO> result = businessDailyServiceImplUnderTest.classify(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testClassify_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final ItemRespDTO itemRespDTO = new ItemRespDTO();
        itemRespDTO.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO.setName("");
        itemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO.setQuantum(new BigDecimal("0.00"));
        itemRespDTO.setAmount(new BigDecimal("0.00"));
        itemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setIsTotal(0);
        itemRespDTO.setHasSubInfo(0);
        itemRespDTO.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO.setSkuName("skuName");
        itemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> expectedResult = Arrays.asList(itemRespDTO);

        // Configure DinnerDailyClientService.classify(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.classify(request1)).thenReturn(Collections.emptyList());

        // Configure TakeoutDailyClientService.listItemSale(...).
        final ItemRespDTO itemRespDTO1 = new ItemRespDTO();
        itemRespDTO1.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO1.setName("");
        itemRespDTO1.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO1.setQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setAmount(new BigDecimal("0.00"));
        itemRespDTO1.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setIsTotal(0);
        itemRespDTO1.setHasSubInfo(0);
        itemRespDTO1.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO1.setSkuName("skuName");
        itemRespDTO1.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO1.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS = Arrays.asList(itemRespDTO1);
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.listItemSale(takeoutStatsQueryDTO)).thenReturn(itemRespDTOS);

        // Run the test
        final List<ItemRespDTO> result = businessDailyServiceImplUnderTest.classify(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testClassify_TakeoutDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final ItemRespDTO itemRespDTO = new ItemRespDTO();
        itemRespDTO.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO.setName("");
        itemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO.setQuantum(new BigDecimal("0.00"));
        itemRespDTO.setAmount(new BigDecimal("0.00"));
        itemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setIsTotal(0);
        itemRespDTO.setHasSubInfo(0);
        itemRespDTO.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO.setSkuName("skuName");
        itemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> expectedResult = Arrays.asList(itemRespDTO);

        // Configure DinnerDailyClientService.classify(...).
        final ItemRespDTO itemRespDTO1 = new ItemRespDTO();
        itemRespDTO1.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO1.setName("");
        itemRespDTO1.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO1.setQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setAmount(new BigDecimal("0.00"));
        itemRespDTO1.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setIsTotal(0);
        itemRespDTO1.setHasSubInfo(0);
        itemRespDTO1.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO1.setSkuName("skuName");
        itemRespDTO1.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO1.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS = Arrays.asList(itemRespDTO1);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.classify(request1)).thenReturn(itemRespDTOS);

        // Configure TakeoutDailyClientService.listItemSale(...).
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.listItemSale(takeoutStatsQueryDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ItemRespDTO> result = businessDailyServiceImplUnderTest.classify(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testClassifySale() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final CategorySaleDTO categorySaleDTO = new CategorySaleDTO();
        categorySaleDTO.setName("name");
        categorySaleDTO.setQuantum("quantum");
        categorySaleDTO.setAmount("amount");
        categorySaleDTO.setDiscountAmount("discountAmount");
        categorySaleDTO.setIsTotal(0);
        final List<CategorySaleDTO> expectedResult = Arrays.asList(categorySaleDTO);

        // Configure DinnerDailyClientService.classify(...).
        final ItemRespDTO itemRespDTO = new ItemRespDTO();
        itemRespDTO.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO.setName("");
        itemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO.setQuantum(new BigDecimal("0.00"));
        itemRespDTO.setAmount(new BigDecimal("0.00"));
        itemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setIsTotal(0);
        itemRespDTO.setHasSubInfo(0);
        itemRespDTO.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO.setSkuName("skuName");
        itemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS = Arrays.asList(itemRespDTO);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.classify(request1)).thenReturn(itemRespDTOS);

        // Configure TakeoutDailyClientService.listItemSale(...).
        final ItemRespDTO itemRespDTO1 = new ItemRespDTO();
        itemRespDTO1.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO1.setName("");
        itemRespDTO1.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO1.setQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setAmount(new BigDecimal("0.00"));
        itemRespDTO1.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setIsTotal(0);
        itemRespDTO1.setHasSubInfo(0);
        itemRespDTO1.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO1.setSkuName("skuName");
        itemRespDTO1.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO1.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS1 = Arrays.asList(itemRespDTO1);
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.listItemSale(takeoutStatsQueryDTO)).thenReturn(itemRespDTOS1);

        // Run the test
        final List<CategorySaleDTO> result = businessDailyServiceImplUnderTest.classifySale(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testClassifySale_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final CategorySaleDTO categorySaleDTO = new CategorySaleDTO();
        categorySaleDTO.setName("name");
        categorySaleDTO.setQuantum("quantum");
        categorySaleDTO.setAmount("amount");
        categorySaleDTO.setDiscountAmount("discountAmount");
        categorySaleDTO.setIsTotal(0);
        final List<CategorySaleDTO> expectedResult = Arrays.asList(categorySaleDTO);

        // Configure DinnerDailyClientService.classify(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.classify(request1)).thenReturn(Collections.emptyList());

        // Configure TakeoutDailyClientService.listItemSale(...).
        final ItemRespDTO itemRespDTO = new ItemRespDTO();
        itemRespDTO.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO.setName("");
        itemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO.setQuantum(new BigDecimal("0.00"));
        itemRespDTO.setAmount(new BigDecimal("0.00"));
        itemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setIsTotal(0);
        itemRespDTO.setHasSubInfo(0);
        itemRespDTO.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO.setSkuName("skuName");
        itemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS = Arrays.asList(itemRespDTO);
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.listItemSale(takeoutStatsQueryDTO)).thenReturn(itemRespDTOS);

        // Run the test
        final List<CategorySaleDTO> result = businessDailyServiceImplUnderTest.classifySale(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testClassifySale_TakeoutDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final CategorySaleDTO categorySaleDTO = new CategorySaleDTO();
        categorySaleDTO.setName("name");
        categorySaleDTO.setQuantum("quantum");
        categorySaleDTO.setAmount("amount");
        categorySaleDTO.setDiscountAmount("discountAmount");
        categorySaleDTO.setIsTotal(0);
        final List<CategorySaleDTO> expectedResult = Arrays.asList(categorySaleDTO);

        // Configure DinnerDailyClientService.classify(...).
        final ItemRespDTO itemRespDTO = new ItemRespDTO();
        itemRespDTO.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO.setName("");
        itemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO.setQuantum(new BigDecimal("0.00"));
        itemRespDTO.setAmount(new BigDecimal("0.00"));
        itemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setIsTotal(0);
        itemRespDTO.setHasSubInfo(0);
        itemRespDTO.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO.setSkuName("skuName");
        itemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS = Arrays.asList(itemRespDTO);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.classify(request1)).thenReturn(itemRespDTOS);

        // Configure TakeoutDailyClientService.listItemSale(...).
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.listItemSale(takeoutStatsQueryDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final List<CategorySaleDTO> result = businessDailyServiceImplUnderTest.classifySale(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGoods() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final ItemRespDTO itemRespDTO = new ItemRespDTO();
        itemRespDTO.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO.setName("");
        itemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO.setQuantum(new BigDecimal("0.00"));
        itemRespDTO.setAmount(new BigDecimal("0.00"));
        itemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setIsTotal(0);
        itemRespDTO.setHasSubInfo(0);
        itemRespDTO.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO.setSkuName("skuName");
        itemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> expectedResult = Arrays.asList(itemRespDTO);

        // Configure DinnerDailyClientService.goods(...).
        final ItemRespDTO itemRespDTO1 = new ItemRespDTO();
        itemRespDTO1.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO1.setName("");
        itemRespDTO1.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO1.setQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setAmount(new BigDecimal("0.00"));
        itemRespDTO1.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setIsTotal(0);
        itemRespDTO1.setHasSubInfo(0);
        itemRespDTO1.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO1.setSkuName("skuName");
        itemRespDTO1.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO1.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS = Arrays.asList(itemRespDTO1);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.goods(request1)).thenReturn(itemRespDTOS);

        // Configure TakeoutDailyClientService.goods(...).
        final ItemRespDTO itemRespDTO2 = new ItemRespDTO();
        itemRespDTO2.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO2.setName("");
        itemRespDTO2.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO2.setQuantum(new BigDecimal("0.00"));
        itemRespDTO2.setAmount(new BigDecimal("0.00"));
        itemRespDTO2.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO2.setIsTotal(0);
        itemRespDTO2.setHasSubInfo(0);
        itemRespDTO2.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO2.setSkuName("skuName");
        itemRespDTO2.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO2.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO2.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO2.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO2.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO2.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO2.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS1 = Arrays.asList(itemRespDTO2);
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.goods(takeoutStatsQueryDTO)).thenReturn(itemRespDTOS1);

        // Run the test
        final List<ItemRespDTO> result = businessDailyServiceImplUnderTest.goods(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGoods_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final ItemRespDTO itemRespDTO = new ItemRespDTO();
        itemRespDTO.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO.setName("");
        itemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO.setQuantum(new BigDecimal("0.00"));
        itemRespDTO.setAmount(new BigDecimal("0.00"));
        itemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setIsTotal(0);
        itemRespDTO.setHasSubInfo(0);
        itemRespDTO.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO.setSkuName("skuName");
        itemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> expectedResult = Arrays.asList(itemRespDTO);

        // Configure DinnerDailyClientService.goods(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.goods(request1)).thenReturn(Collections.emptyList());

        // Configure TakeoutDailyClientService.goods(...).
        final ItemRespDTO itemRespDTO1 = new ItemRespDTO();
        itemRespDTO1.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO1.setName("");
        itemRespDTO1.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO1.setQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setAmount(new BigDecimal("0.00"));
        itemRespDTO1.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setIsTotal(0);
        itemRespDTO1.setHasSubInfo(0);
        itemRespDTO1.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO1.setSkuName("skuName");
        itemRespDTO1.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO1.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS = Arrays.asList(itemRespDTO1);
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.goods(takeoutStatsQueryDTO)).thenReturn(itemRespDTOS);

        // Run the test
        final List<ItemRespDTO> result = businessDailyServiceImplUnderTest.goods(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGoods_TakeoutDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final ItemRespDTO itemRespDTO = new ItemRespDTO();
        itemRespDTO.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO.setName("");
        itemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO.setQuantum(new BigDecimal("0.00"));
        itemRespDTO.setAmount(new BigDecimal("0.00"));
        itemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setIsTotal(0);
        itemRespDTO.setHasSubInfo(0);
        itemRespDTO.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO.setSkuName("skuName");
        itemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> expectedResult = Arrays.asList(itemRespDTO);

        // Configure DinnerDailyClientService.goods(...).
        final ItemRespDTO itemRespDTO1 = new ItemRespDTO();
        itemRespDTO1.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO1.setName("");
        itemRespDTO1.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO1.setQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setAmount(new BigDecimal("0.00"));
        itemRespDTO1.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setIsTotal(0);
        itemRespDTO1.setHasSubInfo(0);
        itemRespDTO1.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO1.setSkuName("skuName");
        itemRespDTO1.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO1.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS = Arrays.asList(itemRespDTO1);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.goods(request1)).thenReturn(itemRespDTOS);

        // Configure TakeoutDailyClientService.goods(...).
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.goods(takeoutStatsQueryDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ItemRespDTO> result = businessDailyServiceImplUnderTest.goods(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGoodsSale() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final GoodsSaleDTO goodsSaleDTO = new GoodsSaleDTO();
        goodsSaleDTO.setName("name");
        goodsSaleDTO.setSkuName("skuName");
        goodsSaleDTO.setUnitPrice("unitPrice");
        goodsSaleDTO.setDinnerQuantum("dinnerQuantum");
        goodsSaleDTO.setDinnerAmount("dinnerAmount");
        final List<GoodsSaleDTO> expectedResult = Arrays.asList(goodsSaleDTO);

        // Configure DinnerDailyClientService.goods(...).
        final ItemRespDTO itemRespDTO = new ItemRespDTO();
        itemRespDTO.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO.setName("");
        itemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO.setQuantum(new BigDecimal("0.00"));
        itemRespDTO.setAmount(new BigDecimal("0.00"));
        itemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setIsTotal(0);
        itemRespDTO.setHasSubInfo(0);
        itemRespDTO.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO.setSkuName("skuName");
        itemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS = Arrays.asList(itemRespDTO);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.goods(request1)).thenReturn(itemRespDTOS);

        // Configure TakeoutDailyClientService.goods(...).
        final ItemRespDTO itemRespDTO1 = new ItemRespDTO();
        itemRespDTO1.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO1.setName("");
        itemRespDTO1.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO1.setQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setAmount(new BigDecimal("0.00"));
        itemRespDTO1.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setIsTotal(0);
        itemRespDTO1.setHasSubInfo(0);
        itemRespDTO1.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO1.setSkuName("skuName");
        itemRespDTO1.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO1.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS1 = Arrays.asList(itemRespDTO1);
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.goods(takeoutStatsQueryDTO)).thenReturn(itemRespDTOS1);

        // Run the test
        final List<GoodsSaleDTO> result = businessDailyServiceImplUnderTest.goodsSale(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGoodsSale_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final GoodsSaleDTO goodsSaleDTO = new GoodsSaleDTO();
        goodsSaleDTO.setName("name");
        goodsSaleDTO.setSkuName("skuName");
        goodsSaleDTO.setUnitPrice("unitPrice");
        goodsSaleDTO.setDinnerQuantum("dinnerQuantum");
        goodsSaleDTO.setDinnerAmount("dinnerAmount");
        final List<GoodsSaleDTO> expectedResult = Arrays.asList(goodsSaleDTO);

        // Configure DinnerDailyClientService.goods(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.goods(request1)).thenReturn(Collections.emptyList());

        // Configure TakeoutDailyClientService.goods(...).
        final ItemRespDTO itemRespDTO = new ItemRespDTO();
        itemRespDTO.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO.setName("");
        itemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO.setQuantum(new BigDecimal("0.00"));
        itemRespDTO.setAmount(new BigDecimal("0.00"));
        itemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setIsTotal(0);
        itemRespDTO.setHasSubInfo(0);
        itemRespDTO.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO.setSkuName("skuName");
        itemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS = Arrays.asList(itemRespDTO);
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.goods(takeoutStatsQueryDTO)).thenReturn(itemRespDTOS);

        // Run the test
        final List<GoodsSaleDTO> result = businessDailyServiceImplUnderTest.goodsSale(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGoodsSale_TakeoutDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final GoodsSaleDTO goodsSaleDTO = new GoodsSaleDTO();
        goodsSaleDTO.setName("name");
        goodsSaleDTO.setSkuName("skuName");
        goodsSaleDTO.setUnitPrice("unitPrice");
        goodsSaleDTO.setDinnerQuantum("dinnerQuantum");
        goodsSaleDTO.setDinnerAmount("dinnerAmount");
        final List<GoodsSaleDTO> expectedResult = Arrays.asList(goodsSaleDTO);

        // Configure DinnerDailyClientService.goods(...).
        final ItemRespDTO itemRespDTO = new ItemRespDTO();
        itemRespDTO.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO.setName("");
        itemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO.setQuantum(new BigDecimal("0.00"));
        itemRespDTO.setAmount(new BigDecimal("0.00"));
        itemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setIsTotal(0);
        itemRespDTO.setHasSubInfo(0);
        itemRespDTO.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO.setSkuName("skuName");
        itemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS = Arrays.asList(itemRespDTO);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.goods(request1)).thenReturn(itemRespDTOS);

        // Configure TakeoutDailyClientService.goods(...).
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.goods(takeoutStatsQueryDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodsSaleDTO> result = businessDailyServiceImplUnderTest.goodsSale(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testAttr() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final PropStatsDTO expectedResult = new PropStatsDTO();
        final PropStatsDTO.PropGroup propGroup = new PropStatsDTO.PropGroup();
        propGroup.setName("attrGroupName");
        final PropStatsDTO.PropItem propItem = new PropStatsDTO.PropItem();
        propItem.setName("");
        propItem.setQuantity(0L);
        propItem.setUnitPrice(new BigDecimal("0.00"));
        propItem.setMoney(new BigDecimal("0.00"));
        propItem.setPropList(Arrays.asList(new PropStatsDTO.PropItem()));
        propGroup.setPropList(Arrays.asList(propItem));
        propGroup.setGroupQuantity(0L);
        propGroup.setGroupMoney(new BigDecimal("0.00"));
        expectedResult.setPropGroupList(Arrays.asList(propGroup));
        expectedResult.setTotalQuantity(0L);
        expectedResult.setTotalMoney(new BigDecimal("0.00"));

        // Configure DinnerDailyClientService.attr(...).
        final AttrItemRespDTO attrItemRespDTO = new AttrItemRespDTO();
        attrItemRespDTO.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        attrItemRespDTO.setName("");
        attrItemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        attrItemRespDTO.setQuantum(new BigDecimal("0.00"));
        attrItemRespDTO.setAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setIsTotal(0);
        attrItemRespDTO.setHasSubInfo(0);
        final ItemRespDTO itemRespDTO = new ItemRespDTO();
        itemRespDTO.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO.setName("");
        itemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO.setQuantum(new BigDecimal("0.00"));
        itemRespDTO.setAmount(new BigDecimal("0.00"));
        itemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setIsTotal(0);
        itemRespDTO.setHasSubInfo(0);
        itemRespDTO.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO.setSkuName("skuName");
        itemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        attrItemRespDTO.setSubs(Arrays.asList(itemRespDTO));
        attrItemRespDTO.setSkuName("skuName");
        attrItemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        attrItemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        attrItemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        attrItemRespDTO.setAttrGroupName("attrGroupName");
        attrItemRespDTO.setAttrs(Arrays.asList(new AttrItemRespDTO()));
        final List<AttrItemRespDTO> attrItemRespDTOS = Arrays.asList(attrItemRespDTO);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.attr(request1)).thenReturn(attrItemRespDTOS);

        // Run the test
        final PropStatsDTO result = businessDailyServiceImplUnderTest.attr(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testAttr_DinnerDailyClientServiceReturnsNull() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final PropStatsDTO expectedResult = new PropStatsDTO();
        final PropStatsDTO.PropGroup propGroup = new PropStatsDTO.PropGroup();
        propGroup.setName("attrGroupName");
        final PropStatsDTO.PropItem propItem = new PropStatsDTO.PropItem();
        propItem.setName("");
        propItem.setQuantity(0L);
        propItem.setUnitPrice(new BigDecimal("0.00"));
        propItem.setMoney(new BigDecimal("0.00"));
        propItem.setPropList(Arrays.asList(new PropStatsDTO.PropItem()));
        propGroup.setPropList(Arrays.asList(propItem));
        propGroup.setGroupQuantity(0L);
        propGroup.setGroupMoney(new BigDecimal("0.00"));
        expectedResult.setPropGroupList(Arrays.asList(propGroup));
        expectedResult.setTotalQuantity(0L);
        expectedResult.setTotalMoney(new BigDecimal("0.00"));

        // Configure DinnerDailyClientService.attr(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.attr(request1)).thenReturn(null);

        // Run the test
        final PropStatsDTO result = businessDailyServiceImplUnderTest.attr(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testAttr_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final PropStatsDTO expectedResult = new PropStatsDTO();
        final PropStatsDTO.PropGroup propGroup = new PropStatsDTO.PropGroup();
        propGroup.setName("attrGroupName");
        final PropStatsDTO.PropItem propItem = new PropStatsDTO.PropItem();
        propItem.setName("");
        propItem.setQuantity(0L);
        propItem.setUnitPrice(new BigDecimal("0.00"));
        propItem.setMoney(new BigDecimal("0.00"));
        propItem.setPropList(Arrays.asList(new PropStatsDTO.PropItem()));
        propGroup.setPropList(Arrays.asList(propItem));
        propGroup.setGroupQuantity(0L);
        propGroup.setGroupMoney(new BigDecimal("0.00"));
        expectedResult.setPropGroupList(Arrays.asList(propGroup));
        expectedResult.setTotalQuantity(0L);
        expectedResult.setTotalMoney(new BigDecimal("0.00"));

        // Configure DinnerDailyClientService.attr(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.attr(request1)).thenReturn(Collections.emptyList());

        // Run the test
        final PropStatsDTO result = businessDailyServiceImplUnderTest.attr(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testAttrSale() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final PropStatsSaleDTO expectedResult = new PropStatsSaleDTO();
        final PropStatsSaleDTO.PropGroup propGroup = new PropStatsSaleDTO.PropGroup();
        propGroup.setName("name");
        propGroup.setGroupQuantity("groupQuantity");
        propGroup.setGroupMoney("groupMoney");
        final PropStatsSaleDTO.PropItem propItem = new PropStatsSaleDTO.PropItem();
        propGroup.setPropList(Arrays.asList(propItem));
        expectedResult.setPropGroupList(Arrays.asList(propGroup));

        // Configure DinnerDailyClientService.attr(...).
        final AttrItemRespDTO attrItemRespDTO = new AttrItemRespDTO();
        attrItemRespDTO.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        attrItemRespDTO.setName("");
        attrItemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        attrItemRespDTO.setQuantum(new BigDecimal("0.00"));
        attrItemRespDTO.setAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setIsTotal(0);
        attrItemRespDTO.setHasSubInfo(0);
        final ItemRespDTO itemRespDTO = new ItemRespDTO();
        itemRespDTO.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO.setName("");
        itemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO.setQuantum(new BigDecimal("0.00"));
        itemRespDTO.setAmount(new BigDecimal("0.00"));
        itemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setIsTotal(0);
        itemRespDTO.setHasSubInfo(0);
        itemRespDTO.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO.setSkuName("skuName");
        itemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        attrItemRespDTO.setSubs(Arrays.asList(itemRespDTO));
        attrItemRespDTO.setSkuName("skuName");
        attrItemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        attrItemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        attrItemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        attrItemRespDTO.setAttrGroupName("attrGroupName");
        attrItemRespDTO.setAttrs(Arrays.asList(new AttrItemRespDTO()));
        final List<AttrItemRespDTO> attrItemRespDTOS = Arrays.asList(attrItemRespDTO);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.attr(request1)).thenReturn(attrItemRespDTOS);

        // Run the test
        final PropStatsSaleDTO result = businessDailyServiceImplUnderTest.attrSale(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testAttrSale_DinnerDailyClientServiceReturnsNull() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final PropStatsSaleDTO expectedResult = new PropStatsSaleDTO();
        final PropStatsSaleDTO.PropGroup propGroup = new PropStatsSaleDTO.PropGroup();
        propGroup.setName("name");
        propGroup.setGroupQuantity("groupQuantity");
        propGroup.setGroupMoney("groupMoney");
        final PropStatsSaleDTO.PropItem propItem = new PropStatsSaleDTO.PropItem();
        propGroup.setPropList(Arrays.asList(propItem));
        expectedResult.setPropGroupList(Arrays.asList(propGroup));

        // Configure DinnerDailyClientService.attr(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.attr(request1)).thenReturn(null);

        // Run the test
        final PropStatsSaleDTO result = businessDailyServiceImplUnderTest.attrSale(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testAttrSale_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final PropStatsSaleDTO expectedResult = new PropStatsSaleDTO();
        final PropStatsSaleDTO.PropGroup propGroup = new PropStatsSaleDTO.PropGroup();
        propGroup.setName("name");
        propGroup.setGroupQuantity("groupQuantity");
        propGroup.setGroupMoney("groupMoney");
        final PropStatsSaleDTO.PropItem propItem = new PropStatsSaleDTO.PropItem();
        propGroup.setPropList(Arrays.asList(propItem));
        expectedResult.setPropGroupList(Arrays.asList(propGroup));

        // Configure DinnerDailyClientService.attr(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.attr(request1)).thenReturn(Collections.emptyList());

        // Run the test
        final PropStatsSaleDTO result = businessDailyServiceImplUnderTest.attrSale(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testReturnVegetables() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final ItemRespDTO itemRespDTO = new ItemRespDTO();
        itemRespDTO.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO.setName("");
        itemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO.setQuantum(new BigDecimal("0.00"));
        itemRespDTO.setAmount(new BigDecimal("0.00"));
        itemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setIsTotal(0);
        itemRespDTO.setHasSubInfo(0);
        itemRespDTO.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO.setSkuName("skuName");
        itemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> expectedResult = Arrays.asList(itemRespDTO);

        // Configure DinnerDailyClientService.returnVegetables(...).
        final ItemRespDTO itemRespDTO1 = new ItemRespDTO();
        itemRespDTO1.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO1.setName("");
        itemRespDTO1.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO1.setQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setAmount(new BigDecimal("0.00"));
        itemRespDTO1.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setIsTotal(0);
        itemRespDTO1.setHasSubInfo(0);
        itemRespDTO1.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO1.setSkuName("skuName");
        itemRespDTO1.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO1.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS = Arrays.asList(itemRespDTO1);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.returnVegetables(request1)).thenReturn(itemRespDTOS);

        // Run the test
        final List<ItemRespDTO> result = businessDailyServiceImplUnderTest.returnVegetables(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testReturnVegetables_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        // Configure DinnerDailyClientService.returnVegetables(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.returnVegetables(request1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ItemRespDTO> result = businessDailyServiceImplUnderTest.returnVegetables(request);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testReturnVegetablesSale() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final ReturnSaleDTO returnSaleDTO = new ReturnSaleDTO();
        returnSaleDTO.setName("name");
        returnSaleDTO.setSkuName("skuName");
        returnSaleDTO.setUnitPrice("unitPrice");
        returnSaleDTO.setQuantum("quantum");
        returnSaleDTO.setAmount("amount");
        final List<ReturnSaleDTO> expectedResult = Arrays.asList(returnSaleDTO);

        // Configure DinnerDailyClientService.returnVegetables(...).
        final ItemRespDTO itemRespDTO = new ItemRespDTO();
        itemRespDTO.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO.setName("");
        itemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO.setQuantum(new BigDecimal("0.00"));
        itemRespDTO.setAmount(new BigDecimal("0.00"));
        itemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setIsTotal(0);
        itemRespDTO.setHasSubInfo(0);
        itemRespDTO.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO.setSkuName("skuName");
        itemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS = Arrays.asList(itemRespDTO);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.returnVegetables(request1)).thenReturn(itemRespDTOS);

        // Run the test
        final List<ReturnSaleDTO> result = businessDailyServiceImplUnderTest.returnVegetablesSale(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testReturnVegetablesSale_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        // Configure DinnerDailyClientService.returnVegetables(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.returnVegetables(request1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ReturnSaleDTO> result = businessDailyServiceImplUnderTest.returnVegetablesSale(request);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testDishGiving() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final ItemRespDTO itemRespDTO = new ItemRespDTO();
        itemRespDTO.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO.setName("");
        itemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO.setQuantum(new BigDecimal("0.00"));
        itemRespDTO.setAmount(new BigDecimal("0.00"));
        itemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setIsTotal(0);
        itemRespDTO.setHasSubInfo(0);
        itemRespDTO.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO.setSkuName("skuName");
        itemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> expectedResult = Arrays.asList(itemRespDTO);

        // Configure DinnerDailyClientService.dishGiving(...).
        final ItemRespDTO itemRespDTO1 = new ItemRespDTO();
        itemRespDTO1.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO1.setName("");
        itemRespDTO1.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO1.setQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setAmount(new BigDecimal("0.00"));
        itemRespDTO1.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setIsTotal(0);
        itemRespDTO1.setHasSubInfo(0);
        itemRespDTO1.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO1.setSkuName("skuName");
        itemRespDTO1.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO1.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO1.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS = Arrays.asList(itemRespDTO1);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.dishGiving(request1)).thenReturn(itemRespDTOS);

        // Run the test
        final List<ItemRespDTO> result = businessDailyServiceImplUnderTest.dishGiving(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDishGiving_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        // Configure DinnerDailyClientService.dishGiving(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.dishGiving(request1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ItemRespDTO> result = businessDailyServiceImplUnderTest.dishGiving(request);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testDishGivingSale() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final GiftSaleDTO giftSaleDTO = new GiftSaleDTO();
        giftSaleDTO.setName("name");
        giftSaleDTO.setSkuName("skuName");
        giftSaleDTO.setUnitPrice("unitPrice");
        giftSaleDTO.setQuantum("quantum");
        giftSaleDTO.setAmount("amount");
        final List<GiftSaleDTO> expectedResult = Arrays.asList(giftSaleDTO);

        // Configure DinnerDailyClientService.dishGiving(...).
        final ItemRespDTO itemRespDTO = new ItemRespDTO();
        itemRespDTO.setGuid("d5d95529-a57b-49ca-95de-02b184c679d1");
        itemRespDTO.setName("");
        itemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        itemRespDTO.setQuantum(new BigDecimal("0.00"));
        itemRespDTO.setAmount(new BigDecimal("0.00"));
        itemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setIsTotal(0);
        itemRespDTO.setHasSubInfo(0);
        itemRespDTO.setSubs(Arrays.asList(new ItemRespDTO()));
        itemRespDTO.setSkuName("skuName");
        itemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        itemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        itemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        itemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> itemRespDTOS = Arrays.asList(itemRespDTO);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.dishGiving(request1)).thenReturn(itemRespDTOS);

        // Run the test
        final List<GiftSaleDTO> result = businessDailyServiceImplUnderTest.dishGivingSale(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testDishGivingSale_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        // Configure DinnerDailyClientService.dishGiving(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.dishGiving(request1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<GiftSaleDTO> result = businessDailyServiceImplUnderTest.dishGivingSale(request);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testMemberConsume() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final MemberConsumeRespDTO expectedResult = new MemberConsumeRespDTO();
        expectedResult.setConsumerCount(0);
        expectedResult.setConsumerAmount(new BigDecimal("0.00"));
        expectedResult.setPrepaidCount(0);
        expectedResult.setPrepaidAmount(new BigDecimal("0.00"));
        expectedResult.setPrepaidGiveAmount(new BigDecimal("0.00"));

        // Configure DinnerDailyClientService.memberConsume(...).
        final MemberConsumeRespDTO memberConsumeRespDTO = new MemberConsumeRespDTO();
        memberConsumeRespDTO.setConsumerCount(0);
        memberConsumeRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidCount(0);
        memberConsumeRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidGiveAmount(new BigDecimal("0.00"));
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.memberConsume(request1)).thenReturn(memberConsumeRespDTO);

        // Run the test
        final MemberConsumeRespDTO result = businessDailyServiceImplUnderTest.memberConsume(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGather() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final GatherRespDTO gatherRespDTO = new GatherRespDTO();
        gatherRespDTO.setGatherCode(0);
        gatherRespDTO.setGatherName("gatherName");
        gatherRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO.setIsTotal(0);
        final List<GatherRespDTO> expectedResult = Arrays.asList(gatherRespDTO);

        // Configure DinnerDailyClientService.gather(...).
        final GatherRespDTO gatherRespDTO1 = new GatherRespDTO();
        gatherRespDTO1.setGatherCode(0);
        gatherRespDTO1.setGatherName("gatherName");
        gatherRespDTO1.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setIsTotal(0);
        final List<GatherRespDTO> gatherRespDTOS = Arrays.asList(gatherRespDTO1);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.gather(request1)).thenReturn(gatherRespDTOS);

        // Configure ReserveClient.gather(...).
        final GatherRespDTO gatherRespDTO2 = new GatherRespDTO();
        gatherRespDTO2.setGatherCode(0);
        gatherRespDTO2.setGatherName("gatherName");
        gatherRespDTO2.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO2.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO2.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO2.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO2.setIsTotal(0);
        final List<GatherRespDTO> gatherRespDTOS1 = Arrays.asList(gatherRespDTO2);
        final DailyReqDTO dailyReqDTO = new DailyReqDTO();
        dailyReqDTO.setStoreGuid("storeGuid");
        dailyReqDTO.setBeginTime("beginTime");
        dailyReqDTO.setEndTime("endTime");
        dailyReqDTO.setQueryType(0);
        dailyReqDTO.setOrderItem(0);
        dailyReqDTO.setOrderType(0);
        dailyReqDTO.setIsMember(0);
        dailyReqDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        dailyReqDTO.setStoreGuids(Arrays.asList("value"));
        when(mockReserveClient.gather(dailyReqDTO)).thenReturn(gatherRespDTOS1);

        // Configure TakeoutDailyClientService.getReceiptStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getReceiptStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test
        final List<GatherRespDTO> result = businessDailyServiceImplUnderTest.gather(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGather_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final GatherRespDTO gatherRespDTO = new GatherRespDTO();
        gatherRespDTO.setGatherCode(0);
        gatherRespDTO.setGatherName("gatherName");
        gatherRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO.setIsTotal(0);
        final List<GatherRespDTO> expectedResult = Arrays.asList(gatherRespDTO);

        // Configure DinnerDailyClientService.gather(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.gather(request1)).thenReturn(Collections.emptyList());

        // Configure ReserveClient.gather(...).
        final GatherRespDTO gatherRespDTO1 = new GatherRespDTO();
        gatherRespDTO1.setGatherCode(0);
        gatherRespDTO1.setGatherName("gatherName");
        gatherRespDTO1.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setIsTotal(0);
        final List<GatherRespDTO> gatherRespDTOS = Arrays.asList(gatherRespDTO1);
        final DailyReqDTO dailyReqDTO = new DailyReqDTO();
        dailyReqDTO.setStoreGuid("storeGuid");
        dailyReqDTO.setBeginTime("beginTime");
        dailyReqDTO.setEndTime("endTime");
        dailyReqDTO.setQueryType(0);
        dailyReqDTO.setOrderItem(0);
        dailyReqDTO.setOrderType(0);
        dailyReqDTO.setIsMember(0);
        dailyReqDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        dailyReqDTO.setStoreGuids(Arrays.asList("value"));
        when(mockReserveClient.gather(dailyReqDTO)).thenReturn(gatherRespDTOS);

        // Configure TakeoutDailyClientService.getReceiptStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getReceiptStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test
        final List<GatherRespDTO> result = businessDailyServiceImplUnderTest.gather(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGather_ReserveClientReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final GatherRespDTO gatherRespDTO = new GatherRespDTO();
        gatherRespDTO.setGatherCode(0);
        gatherRespDTO.setGatherName("gatherName");
        gatherRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO.setIsTotal(0);
        final List<GatherRespDTO> expectedResult = Arrays.asList(gatherRespDTO);

        // Configure DinnerDailyClientService.gather(...).
        final GatherRespDTO gatherRespDTO1 = new GatherRespDTO();
        gatherRespDTO1.setGatherCode(0);
        gatherRespDTO1.setGatherName("gatherName");
        gatherRespDTO1.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setIsTotal(0);
        final List<GatherRespDTO> gatherRespDTOS = Arrays.asList(gatherRespDTO1);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.gather(request1)).thenReturn(gatherRespDTOS);

        // Configure ReserveClient.gather(...).
        final DailyReqDTO dailyReqDTO = new DailyReqDTO();
        dailyReqDTO.setStoreGuid("storeGuid");
        dailyReqDTO.setBeginTime("beginTime");
        dailyReqDTO.setEndTime("endTime");
        dailyReqDTO.setQueryType(0);
        dailyReqDTO.setOrderItem(0);
        dailyReqDTO.setOrderType(0);
        dailyReqDTO.setIsMember(0);
        dailyReqDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        dailyReqDTO.setStoreGuids(Arrays.asList("value"));
        when(mockReserveClient.gather(dailyReqDTO)).thenReturn(Collections.emptyList());

        // Configure TakeoutDailyClientService.getReceiptStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getReceiptStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test
        final List<GatherRespDTO> result = businessDailyServiceImplUnderTest.gather(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGatherSale() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final GatherSaleDTO gatherSaleDTO = new GatherSaleDTO();
        gatherSaleDTO.setGatherCode(1);
        gatherSaleDTO.setGatherName("gatherName");
        gatherSaleDTO.setConsumerAmount("consumerAmount");
        gatherSaleDTO.setExcessAmount("excessAmount");
        gatherSaleDTO.setPrepaidAmount("prepaidAmount");
        final List<GatherSaleDTO> expectedResult = Arrays.asList(gatherSaleDTO);

        // Configure DinnerDailyClientService.gather(...).
        final GatherRespDTO gatherRespDTO = new GatherRespDTO();
        gatherRespDTO.setGatherCode(0);
        gatherRespDTO.setGatherName("gatherName");
        gatherRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO.setIsTotal(0);
        final List<GatherRespDTO> gatherRespDTOS = Arrays.asList(gatherRespDTO);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.gather(request1)).thenReturn(gatherRespDTOS);

        // Configure ReserveClient.gather(...).
        final GatherRespDTO gatherRespDTO1 = new GatherRespDTO();
        gatherRespDTO1.setGatherCode(0);
        gatherRespDTO1.setGatherName("gatherName");
        gatherRespDTO1.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setIsTotal(0);
        final List<GatherRespDTO> gatherRespDTOS1 = Arrays.asList(gatherRespDTO1);
        final DailyReqDTO dailyReqDTO = new DailyReqDTO();
        dailyReqDTO.setStoreGuid("storeGuid");
        dailyReqDTO.setBeginTime("beginTime");
        dailyReqDTO.setEndTime("endTime");
        dailyReqDTO.setQueryType(0);
        dailyReqDTO.setOrderItem(0);
        dailyReqDTO.setOrderType(0);
        dailyReqDTO.setIsMember(0);
        dailyReqDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        dailyReqDTO.setStoreGuids(Arrays.asList("value"));
        when(mockReserveClient.gather(dailyReqDTO)).thenReturn(gatherRespDTOS1);

        // Configure TakeoutDailyClientService.getReceiptStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getReceiptStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test
        final List<GatherSaleDTO> result = businessDailyServiceImplUnderTest.gatherSale(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGatherSale_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final GatherSaleDTO gatherSaleDTO = new GatherSaleDTO();
        gatherSaleDTO.setGatherCode(1);
        gatherSaleDTO.setGatherName("gatherName");
        gatherSaleDTO.setConsumerAmount("consumerAmount");
        gatherSaleDTO.setExcessAmount("excessAmount");
        gatherSaleDTO.setPrepaidAmount("prepaidAmount");
        final List<GatherSaleDTO> expectedResult = Arrays.asList(gatherSaleDTO);

        // Configure DinnerDailyClientService.gather(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.gather(request1)).thenReturn(Collections.emptyList());

        // Configure ReserveClient.gather(...).
        final GatherRespDTO gatherRespDTO = new GatherRespDTO();
        gatherRespDTO.setGatherCode(0);
        gatherRespDTO.setGatherName("gatherName");
        gatherRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO.setIsTotal(0);
        final List<GatherRespDTO> gatherRespDTOS = Arrays.asList(gatherRespDTO);
        final DailyReqDTO dailyReqDTO = new DailyReqDTO();
        dailyReqDTO.setStoreGuid("storeGuid");
        dailyReqDTO.setBeginTime("beginTime");
        dailyReqDTO.setEndTime("endTime");
        dailyReqDTO.setQueryType(0);
        dailyReqDTO.setOrderItem(0);
        dailyReqDTO.setOrderType(0);
        dailyReqDTO.setIsMember(0);
        dailyReqDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        dailyReqDTO.setStoreGuids(Arrays.asList("value"));
        when(mockReserveClient.gather(dailyReqDTO)).thenReturn(gatherRespDTOS);

        // Configure TakeoutDailyClientService.getReceiptStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getReceiptStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test
        final List<GatherSaleDTO> result = businessDailyServiceImplUnderTest.gatherSale(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGatherSale_ReserveClientReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final GatherSaleDTO gatherSaleDTO = new GatherSaleDTO();
        gatherSaleDTO.setGatherCode(1);
        gatherSaleDTO.setGatherName("gatherName");
        gatherSaleDTO.setConsumerAmount("consumerAmount");
        gatherSaleDTO.setExcessAmount("excessAmount");
        gatherSaleDTO.setPrepaidAmount("prepaidAmount");
        final List<GatherSaleDTO> expectedResult = Arrays.asList(gatherSaleDTO);

        // Configure DinnerDailyClientService.gather(...).
        final GatherRespDTO gatherRespDTO = new GatherRespDTO();
        gatherRespDTO.setGatherCode(0);
        gatherRespDTO.setGatherName("gatherName");
        gatherRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO.setIsTotal(0);
        final List<GatherRespDTO> gatherRespDTOS = Arrays.asList(gatherRespDTO);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.gather(request1)).thenReturn(gatherRespDTOS);

        // Configure ReserveClient.gather(...).
        final DailyReqDTO dailyReqDTO = new DailyReqDTO();
        dailyReqDTO.setStoreGuid("storeGuid");
        dailyReqDTO.setBeginTime("beginTime");
        dailyReqDTO.setEndTime("endTime");
        dailyReqDTO.setQueryType(0);
        dailyReqDTO.setOrderItem(0);
        dailyReqDTO.setOrderType(0);
        dailyReqDTO.setIsMember(0);
        dailyReqDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        dailyReqDTO.setStoreGuids(Arrays.asList("value"));
        when(mockReserveClient.gather(dailyReqDTO)).thenReturn(Collections.emptyList());

        // Configure TakeoutDailyClientService.getReceiptStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getReceiptStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test
        final List<GatherSaleDTO> result = businessDailyServiceImplUnderTest.gatherSale(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testOverview() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final OverviewRespDTO expectedResult = new OverviewRespDTO();
        expectedResult.setCheckoutStaffs(Arrays.asList("value"));
        expectedResult.setOrderCount(0);
        expectedResult.setGuestCount(0);
        expectedResult.setConsumerAmount(new BigDecimal("0.00"));
        expectedResult.setGatherAmount(new BigDecimal("0.00"));
        expectedResult.setEstimatedAmount(new BigDecimal("0.00"));
        final AmountItemDTO amountItemDTO = new AmountItemDTO();
        amountItemDTO.setCode(0);
        amountItemDTO.setName("name");
        amountItemDTO.setAmount(new BigDecimal("0.00"));
        amountItemDTO.setExcessAmount(new BigDecimal("0.00"));
        amountItemDTO.setEstimatedAmount(new BigDecimal("0.00"));
        expectedResult.setGatherItems(Arrays.asList(amountItemDTO));
        expectedResult.setDiscountAmount(new BigDecimal("0.00"));
        final AmountItemDTO amountItemDTO1 = new AmountItemDTO();
        amountItemDTO1.setCode(0);
        amountItemDTO1.setName("name");
        amountItemDTO1.setAmount(new BigDecimal("0.00"));
        amountItemDTO1.setExcessAmount(new BigDecimal("0.00"));
        amountItemDTO1.setEstimatedAmount(new BigDecimal("0.00"));
        expectedResult.setDiscountItems(Arrays.asList(amountItemDTO1));
        expectedResult.setMtGrouponEstimatedAmount(new BigDecimal("0.00"));
        expectedResult.setGrossProfitAmount(new BigDecimal("0.00"));
        expectedResult.setCostAmount(new BigDecimal("0.00"));

        // Configure DinnerDailyClientService.overview(...).
        final OverviewRespDTO overviewRespDTO = new OverviewRespDTO();
        overviewRespDTO.setCheckoutStaffs(Arrays.asList("value"));
        overviewRespDTO.setOrderCount(0);
        overviewRespDTO.setGuestCount(0);
        overviewRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        overviewRespDTO.setGatherAmount(new BigDecimal("0.00"));
        overviewRespDTO.setEstimatedAmount(new BigDecimal("0.00"));
        final AmountItemDTO amountItemDTO2 = new AmountItemDTO();
        amountItemDTO2.setCode(0);
        amountItemDTO2.setName("name");
        amountItemDTO2.setAmount(new BigDecimal("0.00"));
        amountItemDTO2.setExcessAmount(new BigDecimal("0.00"));
        amountItemDTO2.setEstimatedAmount(new BigDecimal("0.00"));
        overviewRespDTO.setGatherItems(Arrays.asList(amountItemDTO2));
        overviewRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        final AmountItemDTO amountItemDTO3 = new AmountItemDTO();
        amountItemDTO3.setCode(0);
        amountItemDTO3.setName("name");
        amountItemDTO3.setAmount(new BigDecimal("0.00"));
        amountItemDTO3.setExcessAmount(new BigDecimal("0.00"));
        amountItemDTO3.setEstimatedAmount(new BigDecimal("0.00"));
        overviewRespDTO.setDiscountItems(Arrays.asList(amountItemDTO3));
        overviewRespDTO.setMtGrouponEstimatedAmount(new BigDecimal("0.00"));
        overviewRespDTO.setGrossProfitAmount(new BigDecimal("0.00"));
        overviewRespDTO.setCostAmount(new BigDecimal("0.00"));
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.overview(request1)).thenReturn(overviewRespDTO);

        // Configure TakeoutDailyClientService.getOpStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getOpStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test
        final OverviewRespDTO result = businessDailyServiceImplUnderTest.overview(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testOverviewSale() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));

        final OverviewSaleDTO expectedResult = new OverviewSaleDTO();
        expectedResult.setOrderCount("orderCount");
        expectedResult.setGuestCount("guestCount");
        expectedResult.setConsumerAmount("consumerAmount");
        expectedResult.setGatherAmount("gatherAmount");
        final com.holderzone.holder.saas.aggregation.app.entity.auth.AmountItemDTO amountItemDTO = new com.holderzone.holder.saas.aggregation.app.entity.auth.AmountItemDTO();
        expectedResult.setGatherItems(Arrays.asList(amountItemDTO));

        // Configure DinnerDailyClientService.overview(...).
        final OverviewRespDTO overviewRespDTO = new OverviewRespDTO();
        overviewRespDTO.setCheckoutStaffs(Arrays.asList("value"));
        overviewRespDTO.setOrderCount(0);
        overviewRespDTO.setGuestCount(0);
        overviewRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        overviewRespDTO.setGatherAmount(new BigDecimal("0.00"));
        overviewRespDTO.setEstimatedAmount(new BigDecimal("0.00"));
        final AmountItemDTO amountItemDTO1 = new AmountItemDTO();
        amountItemDTO1.setCode(0);
        amountItemDTO1.setName("name");
        amountItemDTO1.setAmount(new BigDecimal("0.00"));
        amountItemDTO1.setExcessAmount(new BigDecimal("0.00"));
        amountItemDTO1.setEstimatedAmount(new BigDecimal("0.00"));
        overviewRespDTO.setGatherItems(Arrays.asList(amountItemDTO1));
        overviewRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        final AmountItemDTO amountItemDTO2 = new AmountItemDTO();
        amountItemDTO2.setCode(0);
        amountItemDTO2.setName("name");
        amountItemDTO2.setAmount(new BigDecimal("0.00"));
        amountItemDTO2.setExcessAmount(new BigDecimal("0.00"));
        amountItemDTO2.setEstimatedAmount(new BigDecimal("0.00"));
        overviewRespDTO.setDiscountItems(Arrays.asList(amountItemDTO2));
        overviewRespDTO.setMtGrouponEstimatedAmount(new BigDecimal("0.00"));
        overviewRespDTO.setGrossProfitAmount(new BigDecimal("0.00"));
        overviewRespDTO.setCostAmount(new BigDecimal("0.00"));
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.overview(request1)).thenReturn(overviewRespDTO);

        // Configure TakeoutDailyClientService.getOpStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getOpStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test
        final OverviewSaleDTO result = businessDailyServiceImplUnderTest.overviewSale(request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testMemberConsumeDaily() {
        // Setup
        final DailyReqDTO reqDTO = new DailyReqDTO();
        reqDTO.setStoreGuid("storeGuid");
        reqDTO.setBeginTime("beginTime");
        reqDTO.setEndTime("endTime");
        reqDTO.setQueryType(0);
        reqDTO.setOrderItem(0);
        reqDTO.setOrderType(0);
        reqDTO.setIsMember(0);
        reqDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        reqDTO.setStoreGuids(Arrays.asList("value"));

        final ResponseConsumpStatis expectedResult = new ResponseConsumpStatis();
        expectedResult.setConsumptionNum(0L);
        expectedResult.setConsumptionAmount(new BigDecimal("0.00"));
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        responsePayWayDetail.setPayAmount(new BigDecimal("0.00"));
        expectedResult.setPayWayDetailList(Arrays.asList(responsePayWayDetail));

        // Configure MemberDataClientService.queryOnlyConsumption(...).
        final ResponseConsumpStatis responseConsumpStatis = new ResponseConsumpStatis();
        responseConsumpStatis.setConsumptionNum(0L);
        responseConsumpStatis.setConsumptionAmount(new BigDecimal("0.00"));
        final ResponsePayWayDetail responsePayWayDetail1 = new ResponsePayWayDetail();
        responsePayWayDetail1.setPayWay(0);
        responsePayWayDetail1.setPayAmount(new BigDecimal("0.00"));
        responseConsumpStatis.setPayWayDetailList(Arrays.asList(responsePayWayDetail1));
        final RequestMemberDaily reqDTO1 = new RequestMemberDaily();
        reqDTO1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setStoreGuid("storeGuid");
        reqDTO1.setConsumpType(0);
        when(mockDataClientService.queryOnlyConsumption(reqDTO1)).thenReturn(responseConsumpStatis);

        // Configure DinnerDailyClientService.listByRequest(...).
        final List<AmountItemDTO> amountItemDTOS = Arrays.asList(
                new AmountItemDTO(0, "name", new BigDecimal("0.00"), new BigDecimal("0.00")));
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.listByRequest(request)).thenReturn(amountItemDTOS);

        // Run the test
        final ResponseConsumpStatis result = businessDailyServiceImplUnderTest.memberConsumeDaily(reqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testMemberConsumeDaily_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO reqDTO = new DailyReqDTO();
        reqDTO.setStoreGuid("storeGuid");
        reqDTO.setBeginTime("beginTime");
        reqDTO.setEndTime("endTime");
        reqDTO.setQueryType(0);
        reqDTO.setOrderItem(0);
        reqDTO.setOrderType(0);
        reqDTO.setIsMember(0);
        reqDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        reqDTO.setStoreGuids(Arrays.asList("value"));

        final ResponseConsumpStatis expectedResult = new ResponseConsumpStatis();
        expectedResult.setConsumptionNum(0L);
        expectedResult.setConsumptionAmount(new BigDecimal("0.00"));
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        responsePayWayDetail.setPayAmount(new BigDecimal("0.00"));
        expectedResult.setPayWayDetailList(Arrays.asList(responsePayWayDetail));

        // Configure MemberDataClientService.queryOnlyConsumption(...).
        final ResponseConsumpStatis responseConsumpStatis = new ResponseConsumpStatis();
        responseConsumpStatis.setConsumptionNum(0L);
        responseConsumpStatis.setConsumptionAmount(new BigDecimal("0.00"));
        final ResponsePayWayDetail responsePayWayDetail1 = new ResponsePayWayDetail();
        responsePayWayDetail1.setPayWay(0);
        responsePayWayDetail1.setPayAmount(new BigDecimal("0.00"));
        responseConsumpStatis.setPayWayDetailList(Arrays.asList(responsePayWayDetail1));
        final RequestMemberDaily reqDTO1 = new RequestMemberDaily();
        reqDTO1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setStoreGuid("storeGuid");
        reqDTO1.setConsumpType(0);
        when(mockDataClientService.queryOnlyConsumption(reqDTO1)).thenReturn(responseConsumpStatis);

        // Configure DinnerDailyClientService.listByRequest(...).
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.listByRequest(request)).thenReturn(Collections.emptyList());

        // Run the test
        final ResponseConsumpStatis result = businessDailyServiceImplUnderTest.memberConsumeDaily(reqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testMemberConsumeSale() {
        // Setup
        final DailyReqDTO reqDTO = new DailyReqDTO();
        reqDTO.setStoreGuid("storeGuid");
        reqDTO.setBeginTime("beginTime");
        reqDTO.setEndTime("endTime");
        reqDTO.setQueryType(0);
        reqDTO.setOrderItem(0);
        reqDTO.setOrderType(0);
        reqDTO.setIsMember(0);
        reqDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        reqDTO.setStoreGuids(Arrays.asList("value"));

        final MemberConsumeSaleDTO expectedResult = new MemberConsumeSaleDTO();
        expectedResult.setConsumptionNum("consumptionNum");
        expectedResult.setConsumptionMemberNum("consumptionMemberNum");
        expectedResult.setConsumptionAmount("consumptionAmount");
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        expectedResult.setPayWayDetailList(Arrays.asList(responsePayWayDetail));

        // Configure MemberDataClientService.queryOnlyConsumption(...).
        final ResponseConsumpStatis responseConsumpStatis = new ResponseConsumpStatis();
        responseConsumpStatis.setConsumptionNum(0L);
        responseConsumpStatis.setConsumptionAmount(new BigDecimal("0.00"));
        final ResponsePayWayDetail responsePayWayDetail1 = new ResponsePayWayDetail();
        responsePayWayDetail1.setPayWay(0);
        responsePayWayDetail1.setPayAmount(new BigDecimal("0.00"));
        responseConsumpStatis.setPayWayDetailList(Arrays.asList(responsePayWayDetail1));
        final RequestMemberDaily reqDTO1 = new RequestMemberDaily();
        reqDTO1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setStoreGuid("storeGuid");
        reqDTO1.setConsumpType(0);
        when(mockDataClientService.queryOnlyConsumption(reqDTO1)).thenReturn(responseConsumpStatis);

        // Configure DinnerDailyClientService.listByRequest(...).
        final List<AmountItemDTO> amountItemDTOS = Arrays.asList(
                new AmountItemDTO(0, "name", new BigDecimal("0.00"), new BigDecimal("0.00")));
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.listByRequest(request)).thenReturn(amountItemDTOS);

        // Run the test
        final MemberConsumeSaleDTO result = businessDailyServiceImplUnderTest.memberConsumeSale(reqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testMemberConsumeSale_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO reqDTO = new DailyReqDTO();
        reqDTO.setStoreGuid("storeGuid");
        reqDTO.setBeginTime("beginTime");
        reqDTO.setEndTime("endTime");
        reqDTO.setQueryType(0);
        reqDTO.setOrderItem(0);
        reqDTO.setOrderType(0);
        reqDTO.setIsMember(0);
        reqDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        reqDTO.setStoreGuids(Arrays.asList("value"));

        final MemberConsumeSaleDTO expectedResult = new MemberConsumeSaleDTO();
        expectedResult.setConsumptionNum("consumptionNum");
        expectedResult.setConsumptionMemberNum("consumptionMemberNum");
        expectedResult.setConsumptionAmount("consumptionAmount");
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        expectedResult.setPayWayDetailList(Arrays.asList(responsePayWayDetail));

        // Configure MemberDataClientService.queryOnlyConsumption(...).
        final ResponseConsumpStatis responseConsumpStatis = new ResponseConsumpStatis();
        responseConsumpStatis.setConsumptionNum(0L);
        responseConsumpStatis.setConsumptionAmount(new BigDecimal("0.00"));
        final ResponsePayWayDetail responsePayWayDetail1 = new ResponsePayWayDetail();
        responsePayWayDetail1.setPayWay(0);
        responsePayWayDetail1.setPayAmount(new BigDecimal("0.00"));
        responseConsumpStatis.setPayWayDetailList(Arrays.asList(responsePayWayDetail1));
        final RequestMemberDaily reqDTO1 = new RequestMemberDaily();
        reqDTO1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setStoreGuid("storeGuid");
        reqDTO1.setConsumpType(0);
        when(mockDataClientService.queryOnlyConsumption(reqDTO1)).thenReturn(responseConsumpStatis);

        // Configure DinnerDailyClientService.listByRequest(...).
        final DailyReqDTO request = new DailyReqDTO();
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        when(mockDinnerDailyClientService.listByRequest(request)).thenReturn(Collections.emptyList());

        // Run the test
        final MemberConsumeSaleDTO result = businessDailyServiceImplUnderTest.memberConsumeSale(reqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testMemberRechargeDaily() {
        // Setup
        final DailyReqDTO reqDTO = new DailyReqDTO();
        reqDTO.setStoreGuid("storeGuid");
        reqDTO.setBeginTime("beginTime");
        reqDTO.setEndTime("endTime");
        reqDTO.setQueryType(0);
        reqDTO.setOrderItem(0);
        reqDTO.setOrderType(0);
        reqDTO.setIsMember(0);
        reqDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        reqDTO.setStoreGuids(Arrays.asList("value"));

        final ResponseRechargeStatis expectedResult = new ResponseRechargeStatis();
        expectedResult.setRechargeNum(0L);
        expectedResult.setRechargeAmount(new BigDecimal("0.00"));
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        responsePayWayDetail.setPayAmount(new BigDecimal("0.00"));
        expectedResult.setPayWayDetailList(Arrays.asList(responsePayWayDetail));

        // Configure MemberDataClientService.queryOnlyRecharge(...).
        final ResponseRechargeStatis responseRechargeStatis = new ResponseRechargeStatis();
        responseRechargeStatis.setRechargeNum(0L);
        responseRechargeStatis.setRechargeAmount(new BigDecimal("0.00"));
        final ResponsePayWayDetail responsePayWayDetail1 = new ResponsePayWayDetail();
        responsePayWayDetail1.setPayWay(0);
        responsePayWayDetail1.setPayAmount(new BigDecimal("0.00"));
        responseRechargeStatis.setPayWayDetailList(Arrays.asList(responsePayWayDetail1));
        final RequestMemberDaily reqDTO1 = new RequestMemberDaily();
        reqDTO1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setStoreGuid("storeGuid");
        reqDTO1.setConsumpType(0);
        when(mockDataClientService.queryOnlyRecharge(reqDTO1)).thenReturn(responseRechargeStatis);

        // Run the test
        final ResponseRechargeStatis result = businessDailyServiceImplUnderTest.memberRechargeDaily(reqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testMemberRechargeSale() {
        // Setup
        final DailyReqDTO reqDTO = new DailyReqDTO();
        reqDTO.setStoreGuid("storeGuid");
        reqDTO.setBeginTime("beginTime");
        reqDTO.setEndTime("endTime");
        reqDTO.setQueryType(0);
        reqDTO.setOrderItem(0);
        reqDTO.setOrderType(0);
        reqDTO.setIsMember(0);
        reqDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        reqDTO.setStoreGuids(Arrays.asList("value"));

        final MemberRechargeSaleDTO expectedResult = new MemberRechargeSaleDTO();
        expectedResult.setRechargeNum("rechargeNum");
        expectedResult.setRechargeMemberNum("rechargeMemberNum");
        expectedResult.setRechargeAmount("rechargeAmount");
        expectedResult.setPresentAmount("presentAmount");
        expectedResult.setIncomeAmount("incomeAmount");

        // Configure MemberDataClientService.queryOnlyRecharge(...).
        final ResponseRechargeStatis responseRechargeStatis = new ResponseRechargeStatis();
        responseRechargeStatis.setRechargeNum(0L);
        responseRechargeStatis.setRechargeAmount(new BigDecimal("0.00"));
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        responsePayWayDetail.setPayAmount(new BigDecimal("0.00"));
        responseRechargeStatis.setPayWayDetailList(Arrays.asList(responsePayWayDetail));
        final RequestMemberDaily reqDTO1 = new RequestMemberDaily();
        reqDTO1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setStoreGuid("storeGuid");
        reqDTO1.setConsumpType(0);
        when(mockDataClientService.queryOnlyRecharge(reqDTO1)).thenReturn(responseRechargeStatis);

        // Run the test
        final MemberRechargeSaleDTO result = businessDailyServiceImplUnderTest.memberRechargeSale(reqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUsers() {
        // Setup
        final List<UserBriefDTO> expectedResult = Arrays.asList(new UserBriefDTO("userGuid", "userName"));

        // Configure UserClientService.storeUsers(...).
        final List<UserBriefDTO> userBriefDTOS = Arrays.asList(new UserBriefDTO("userGuid", "userName"));
        when(mockUserClientService.storeUsers("storeGuid")).thenReturn(userBriefDTOS);

        // Run the test
        final List<UserBriefDTO> result = businessDailyServiceImplUnderTest.users();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUsers_UserClientServiceReturnsNoItems() {
        // Setup
        when(mockUserClientService.storeUsers("storeGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final List<UserBriefDTO> result = businessDailyServiceImplUnderTest.users();

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }
}
