package com.holderzone.holder.saas.aggregation.weixin.controller.memberCenter;

import com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.UpdateUserLoginClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.memberCenter.WxMemberOverviewClientService;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseAccountStatus;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberCardListOwned;
import com.holderzone.saas.store.dto.weixin.WxMemberCenterCardRespDTO;
import com.holderzone.saas.store.dto.weixin.WxMemberOverviewModelDTO;
import com.holderzone.saas.store.dto.weixin.WxMemberOverviewRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(WxMemberCenterOverviewController.class)
public class WxMemberCenterOverviewControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxMemberOverviewClientService mockWxMemberOverviewClientService;
    @MockBean
    private HsaBaseClientService mockHsaBaseClientService;
    @MockBean
    private UpdateUserLoginClientService mockUpdateUserLoginClientService;

    @Test
    public void testAllModel() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getMemberState(...).
        final ResponseAccountStatus responseAccountStatus = new ResponseAccountStatus();
        responseAccountStatus.setMemberInfoGuid("memberInfoGuid");
        responseAccountStatus.setOperSubjectGuid("operSubjectGuid");
        responseAccountStatus.setPhoneNum("phoneNum");
        responseAccountStatus.setNickName("nickName");
        responseAccountStatus.setAccountState(0);
        final ResponseModel<ResponseAccountStatus> responseAccountStatusResponseModel = new ResponseModel<>(
                responseAccountStatus);
        when(mockHsaBaseClientService.getMemberState("openId", "enterpriseGuid", "operSubjectGuid"))
                .thenReturn(responseAccountStatusResponseModel);

        when(mockUpdateUserLoginClientService.updateUserLogin(
                new WxStoreConsumerDTO("consumerGuid", "openId", "unionId", "nickName", "headImgUrl", 0, "country",
                        "province", "city", "","","phone", 0, "deviceId", "enterpriseGuid", "enterpriseName", "storeGuid",
                        "storeName", "userGuid", "userName", "account", 0, "diningTableGuid", "tableCode",
                        "diningTableName", "areaGuid", "areaName", "brandName", "brandGuid", "brandLogo", false,
                        "memberInfoGuid", "phoneNum", false, "operSubjectGuid", false))).thenReturn(false);

        // Configure WxMemberOverviewClientService.allModel(...).
        final ResponseMemberCardListOwned responseMemberCardListOwned = new ResponseMemberCardListOwned();
        responseMemberCardListOwned.setCardQRCode("cardQRCode");
        responseMemberCardListOwned.setCardGuid("cardGuid");
        responseMemberCardListOwned.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCardListOwned.setSystemManagementGuid("systemManagementGuid");
        responseMemberCardListOwned.setEnterpriseMemberInfoSystemGuid("enterpriseMemberInfoSystemGuid");
        final WxMemberOverviewRespDTO wxMemberOverviewRespDTO = new WxMemberOverviewRespDTO(false, "phoneNum",
                Arrays.asList(new WxMemberOverviewModelDTO(0, "modelName", 0)),
                new WxMemberCenterCardRespDTO(Arrays.asList(responseMemberCardListOwned), false), "memberInfoGuid");
        when(mockWxMemberOverviewClientService.allModel(
                new WxStoreConsumerDTO("consumerGuid", "openId", "unionId", "nickName", "headImgUrl", 0, "country",
                        "province", "city", "", "", "phone", 0, "deviceId", "enterpriseGuid", "enterpriseName", "storeGuid",
                        "storeName", "userGuid", "userName", "account", 0, "diningTableGuid", "tableCode",
                        "diningTableName", "areaGuid", "areaName", "brandName", "brandGuid", "brandLogo", false,
                        "memberInfoGuid", "phoneNum", false, "operSubjectGuid", false)))
                .thenReturn(wxMemberOverviewRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_member_center_overview/all_model")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
