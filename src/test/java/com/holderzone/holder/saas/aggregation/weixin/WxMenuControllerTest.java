package com.holderzone.holder.saas.aggregation.weixin;

import com.alibaba.fastjson.JSON;
import com.holderzone.holder.saas.aggregation.weixin.util.JsonFileUtil;
import com.holderzone.holder.saas.aggregation.weixin.util.MockHttpUtil;
import com.holderzone.holder.saas.aggregation.weixin.utils.SpringContextUtil;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;


/**
 * 微信点餐门店菜品信息
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
@SpringBootTest(classes = HolderSaasAggregationWeixinApplication.class)
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class WxMenuControllerTest {

    @Autowired
    private ApplicationContext ap;

    @Autowired
    private WebApplicationContext webApplicationContext;
    private MockMvc mockMvc;

    private static final String WX_MENU_REQUEST_MAPPING = "/wx-store-menu";

    @Before
    public void setupMockMvc() {
        SpringContextUtil.getInstance().setCfgContext((ConfigurableApplicationContext) ap);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.webApplicationContext).build();
    }

    /**
     * 获取consumer信息
     */
    @Test
    public void testGetConsumerInfo() {
        WxPortalReqDTO reqVO = JSON.parseObject(JsonFileUtil.read("/wx_menu/get_consumer_info.json"), WxPortalReqDTO.class);
        String content = MockHttpUtil.post(WX_MENU_REQUEST_MAPPING + "/get_consumer_info", reqVO, mockMvc);
        log.info("获取consumer信息返回参数:{}", content);
    }

}
