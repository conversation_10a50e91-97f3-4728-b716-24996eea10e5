//package com.holderzone.holder.saas.aggregation.weixin;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.holderzone.feign.spring.boot.pojo.UserContext;
//import com.holderzone.feign.spring.boot.util.UserContextUtils;
//import com.holderzone.framework.dynamic.datasource.starter.utils.EnterpriseIdentifier;
//import com.holderzone.framework.response.Result;
//import com.holderzone.holder.saas.aggregation.weixin.service.rpc.cmember.MemberBaseClientService;
//import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.WeChatClientService;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.MediaType;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.web.servlet.MockMvc;
//import org.springframework.test.web.servlet.MvcResult;
//import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
//import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
//import org.springframework.test.web.servlet.setup.MockMvcBuilders;
//import org.springframework.web.context.WebApplicationContext;
//
//import javax.annotation.Resource;
//
//import static org.junit.Assert.assertEquals;
//
////配置Spring中的测试环境
//@RunWith(SpringJUnit4ClassRunner.class)
////指定测试环境使用的ApplicationContext是WebApplicationContext类型的
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//public class BaseControllerTest {
//
//	private static final Logger LOGGER = LoggerFactory.getLogger(BaseControllerTest.class);
//
//	// 注入webApplicationContext
//	@Resource
//	private WebApplicationContext webApplicationContext;
//
//	private MockMvc mockMvc;
//
//	// 初始化mockMvc,在每个测试方法前执行
//	@Before
//	public void setup() {
//		this.mockMvc = MockMvcBuilders.webAppContextSetup(this.webApplicationContext).build();
//	}
//
//	public String post(String uri, String jsonData, JSONObject jsonHeader) throws Exception {
//		MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.patch(uri)
//				.accept(MediaType.APPLICATION_JSON).contentType("application/json; charset=utf-8").content(jsonData);
//		if (jsonHeader != null) {
//			jsonHeader.entrySet().forEach(action -> {
//				requestBuilder.header(action.getKey(), action.getValue() + "");
//			});
//		}
//		MvcResult result = mockMvc.perform(requestBuilder).andReturn();
//		assertEquals(HttpStatus.ACCEPTED.value(), result.getResponse().getStatus());
//		return result.getResponse().getContentAsString();
//	}
//
//	public String post(String uri, Object jsonObject, JSONObject jsonHeader) throws Exception {
//		MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.patch(uri)
//				.accept(MediaType.APPLICATION_JSON).contentType("application/json; charset=utf-8").content(JSON.toJSONString(jsonObject));
//		if (jsonHeader != null) {
//			jsonHeader.entrySet().forEach(action -> {
//				requestBuilder.header(action.getKey(), action.getValue() + "");
//			});
//		}
//		MvcResult result = mockMvc.perform(requestBuilder).andReturn();
//		assertEquals(HttpStatus.ACCEPTED.value(), result.getResponse().getStatus());
//		return result.getResponse().getContentAsString();
//	}
//
//	public Result get(String uri, JSONObject jsonHeader) {
//		MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get(uri);
//		if (jsonHeader != null) {
//			jsonHeader.entrySet().forEach(action -> {
//				requestBuilder.header(action.getKey(), action.getValue() + "");
//			});
//		}
//		try {
//			MvcResult result = mockMvc.perform(requestBuilder).andReturn();
//			assertEquals(HttpStatus.OK.value(), result.getResponse().getStatus());
//			return JSONObject.parseObject(result.getResponse().getContentAsString(),Result.class);
//		} catch (Exception e) {
//			LOGGER.error("uri:{},jsonHeader:{}", uri, jsonHeader, e);
//		}
//		return null;
//	}
//
//	@Resource
//	private MemberBaseClientService memberBaseClientService;
//	@Resource
//	private WeChatClientService weChatClientService;
//	@Test
//	public void testGetOrder() {
//		String memberInfoCardGuid = "6571952526621409281";
//		EnterpriseIdentifier.setEnterpriseGuid("6506431195651982337");
//		UserContextUtils.put(UserContext.builder().enterpriseGuid("6506431195651982337")
//				.storeGuid("6506453252643487745").allianceId("1fb529b8da78459ca64187f94dc3ae3e").build());
//		boolean b = memberBaseClientService.hasMemberPrice(memberInfoCardGuid);
//		System.out.println("是否有会员价:{}"+b);
//
////		ProductDiscountRespDTO discountProducts = weChatClientService.getDiscountProducts(memberInfoCardGuid);
////		System.out.println("是否有会员价P:"+discountProducts);
//	}
//}
