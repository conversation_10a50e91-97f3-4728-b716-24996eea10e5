package com.holderzone.holder.saas.aggregation.weixin.service.impl;

import com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel;
import com.holderzone.holder.saas.aggregation.weixin.context.DiscountContext;
import com.holderzone.holder.saas.aggregation.weixin.entity.bo.DiscountRuleBO;
import com.holderzone.holder.saas.aggregation.weixin.helper.MarketingActivityHelper;
import com.holderzone.holder.saas.aggregation.weixin.helper.MemberRightHelper;
import com.holderzone.holder.saas.aggregation.weixin.service.MemberService;
import com.holderzone.holder.saas.aggregation.weixin.service.MenuItemService;
import com.holderzone.holder.saas.aggregation.weixin.service.WxStoreTradeOrderService;
import com.holderzone.holder.saas.aggregation.weixin.service.chain.DiscountChain;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.HsmTerminalServiceClient;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.ItemClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.TradeClientService;
import com.holderzone.holder.saas.aggregation.weixin.utils.UserMemberSessionUtils;
import com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseClientMarketActivity;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseCardRights;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseDiscount;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseProductDiscount;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityDetailsVO;
import com.holderzone.saas.store.dto.order.OrderDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.weixin.PricePairDTO;
import com.holderzone.saas.store.dto.weixin.deal.*;
import com.holderzone.saas.store.dto.weixin.resp.CalculateOrderRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.MarketingActivityInfoRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CalculateServiceImplTest {

    @Mock
    private UserMemberSessionUtils mockUserMemberSessionUtils;
    @Mock
    private WxStoreTradeOrderService mockWxStoreTradeOrderService;
    @Mock
    private MenuItemService mockMenuItemService;
    @Mock
    private HsmTerminalServiceClient mockTerminalServiceClient;
    @Mock
    private DiscountChain mockDiscountChain;
    @Mock
    private MemberService mockMemberService;
    @Mock
    private MarketingActivityHelper mockMarketingActivityHelper;
    @Mock
    private HsaBaseClientService mockWechatClientService;
    @Mock
    private TradeClientService mockTradeClientService;
    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private ItemClientService mockItemClientService;

    @Mock
    private MemberRightHelper memberRightHelper;

    private CalculateServiceImpl calculateServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        calculateServiceImplUnderTest = new CalculateServiceImpl(mockUserMemberSessionUtils,
                mockWxStoreTradeOrderService, mockMenuItemService, mockTerminalServiceClient, mockDiscountChain,
                mockMemberService, mockMarketingActivityHelper, mockWechatClientService, mockTradeClientService,
                mockRedisUtils, mockItemClientService, memberRightHelper);
    }

    @Test
    public void testCalculate() {
        // Setup
        final CalculateOrderDTO calculateDTO = new CalculateOrderDTO();
        calculateDTO.setDeviceType(0);
        calculateDTO.setUseMemberDiscountFlag(false);
        calculateDTO.setMemberIntegralStore(false);
        calculateDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        calculateDTO.setVolumeCodes(Arrays.asList("value"));
        final ActivitySelectDTO activitySelectDTO = new ActivitySelectDTO();
        activitySelectDTO.setActivityGuid("activityGuid");
        activitySelectDTO.setActivityType(0);
        calculateDTO.setActivitySelectList(Arrays.asList(activitySelectDTO));
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("c73de291-567a-48a9-b46c-8f0a00b5216e");
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPriceType(0);
        calculateDTO.setDineInItemList(Arrays.asList(dineInItemDTO));
        calculateDTO.setIsFirst(false);
        calculateDTO.setIsReplace(false);
        calculateDTO.setOrderGuid("orderGuid");
        calculateDTO.setVerify(0);

        final CalculateOrderRespDTO expectedResult = new CalculateOrderRespDTO();
        expectedResult.setUseMemberDiscountFlag(false);
        expectedResult.setHasMemberPrice(false);
        expectedResult.setHasMemberDiscount(false);
        expectedResult.setMemberDiscount(new BigDecimal("0.00"));
        expectedResult.setUnAbleMemberDiscountFlag(false);
        expectedResult.setUnAbleMemberDiscountReason("");
        expectedResult.setOrderSurplusFee(new BigDecimal("0.00"));
        expectedResult.setOrderSurplusFeeBySkipSpecials(new BigDecimal("0.00"));
        expectedResult.setOrderFee(new BigDecimal("0.00"));
        expectedResult.setAppendFee(new BigDecimal("0.00"));
        expectedResult.setDiscountFee(new BigDecimal("0.00"));
        expectedResult.setActuallyPayFee(new BigDecimal("0.00"));
        final ActivitySelectDTO activitySelectDTO1 = new ActivitySelectDTO();
        expectedResult.setActivitySelectList(Arrays.asList(activitySelectDTO1));
        final MarketingActivityInfoRespDTO marketingActivityInfoRespDTO = new MarketingActivityInfoRespDTO();
        expectedResult.setActivityInfoList(Arrays.asList(marketingActivityInfoRespDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        expectedResult.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        final SurchargeLinkDTO surchargeLinkDTO = new SurchargeLinkDTO();
        surchargeLinkDTO.setAmount(new BigDecimal("0.00"));
        surchargeLinkDTO.setType(0);
        expectedResult.setAppendFeeDetailDTOS(Arrays.asList(surchargeLinkDTO));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("c73de291-567a-48a9-b46c-8f0a00b5216e");
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPriceType(0);
        expectedResult.setDineInItemList(Arrays.asList(dineInItemDTO1));

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure HsmTerminalServiceClient.getVolumeByCode(...).
        final ResponseVolumeList responseVolumeList = new ResponseVolumeList();
        responseVolumeList.setVolumeCode("volumeCode");
        responseVolumeList.setVolumeInfoGuid("volumeGuid");
        responseVolumeList.setMemberVolumeGuid("memberVolumeGuid");
        responseVolumeList.setVolumeType(0);
        responseVolumeList.setIsUseAlone(0);
        when(mockTerminalServiceClient.getVolumeByCode("volumeCode")).thenReturn(responseVolumeList);

        // Configure UserMemberSessionUtils.getCardList(...).
        final List<UserMemberCardCacheDTO> userMemberCardCacheDTOS = Arrays.asList(
                new UserMemberCardCacheDTO("cardQRCode", "cardGuid", "memberInfoCardGuid", "systemManagementGuid",
                        "enterpriseMemberInfoSystemGuid", "cardName", "cardLogo", "cardIcon", "cardColour", 0,
                        "cardStartDate", "cardEndDate", 0, "systemManagementCardNum", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "cardIntegral", "cardLevelGuid", "cardLevelName", 0, "levelIcon", 0, 0,
                        false));
        when(mockUserMemberSessionUtils.getCardList("storeGuid", "openId")).thenReturn(userMemberCardCacheDTOS);

        // Configure MenuItemService.getCartItemList(...).
        final ActivityRuleDescDTO activityRuleDescDTO = new ActivityRuleDescDTO();
        activityRuleDescDTO.setActivityGuid("activityGuid");
        activityRuleDescDTO.setActivityType(0);
        activityRuleDescDTO.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO.setRelationRule(0);
        final ActivityRuleDescDTO activityRuleDescDTO1 = new ActivityRuleDescDTO();
        activityRuleDescDTO1.setActivityGuid("activityGuid");
        activityRuleDescDTO1.setActivityType(0);
        activityRuleDescDTO1.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO1.setRelationRule(0);
        final ItemMarketingActivityDTO itemMarketingActivityDTO = new ItemMarketingActivityDTO();
        itemMarketingActivityDTO.setSpecialsType(0);
        itemMarketingActivityDTO.setSpecialsNumber(new BigDecimal("0.00"));
        itemMarketingActivityDTO.setLimitNumber(0);
        final List<ShopCartItemReqDTO> shopCartItemReqDTOS = Arrays.asList(new ShopCartItemReqDTO(
                new ItemInfoDTO("itemGuid", "parentGuid", "typeGuid", "typeName", 0, new BigDecimal("0.00"), false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "showUnit", new BigDecimal("0.00"),
                        "showMemberRule", "name", "pinyin", "nameAbbr", "description", "englishBrief",
                        "englishIngredientsDesc", "pictureUrl", "bigPictureUrl", "detailBigPictureUrl", 0, 0, false, 0,
                        new BigDecimal("0.00"), Arrays.asList(new ItemInfoTagDTO("id", "name")), 0, 0, 0, Arrays.asList(
                        new ItemInfoSkuDTO("skuGuid", "parentGuid", "name", "unit", "code", new BigDecimal("0.00"),
                                false, new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                                new BigDecimal("0.00"), 0, 0, 0, 0, new BigDecimal("0.00"), Arrays.asList("value"),
                                Arrays.asList("value"), Arrays.asList(activityRuleDescDTO))), Arrays.asList(
                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name", new BigDecimal("0.00"), 0)),
                                0)), Arrays.asList(new ItemInfoSubgroupDTO("subgroupGuid", "name", 0, 0, false,
                        Arrays.asList(
                                new ItemInfoSubSkuDTO("itemGuid", "itemName", "code", "itemTypeGuid", "itemTypeName", 0,
                                        "unit", "skuGuid", "skuName", "pictureUrl", new BigDecimal("0.00"), new BigDecimal("0.00"),
                                        new BigDecimal("0.00"), 0, 0, Arrays.asList(
                                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name",
                                                        new BigDecimal("0.00"), 0)), 0)), 0, new BigDecimal("0.00"),
                                        0)))), new BigDecimal("0.00"), 0, false, Arrays.asList("value"),
                        Arrays.asList("value"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), Arrays.asList(activityRuleDescDTO1), Arrays.asList(0),
                        itemMarketingActivityDTO, 0, new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), null, null), false, new BigDecimal("0.00"), "openid", "nickName", "headImgUrl",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        when(mockMenuItemService.getCartItemList()).thenReturn(shopCartItemReqDTOS);

        // Configure MenuItemService.itemPrice(...).
        final PricePairDTO pricePairDTO = new PricePairDTO(new BigDecimal("0.00"), new BigDecimal("0.00"));
        final ActivityRuleDescDTO activityRuleDescDTO2 = new ActivityRuleDescDTO();
        activityRuleDescDTO2.setActivityGuid("activityGuid");
        activityRuleDescDTO2.setActivityType(0);
        activityRuleDescDTO2.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO2.setRelationRule(0);
        final ActivityRuleDescDTO activityRuleDescDTO3 = new ActivityRuleDescDTO();
        activityRuleDescDTO3.setActivityGuid("activityGuid");
        activityRuleDescDTO3.setActivityType(0);
        activityRuleDescDTO3.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO3.setRelationRule(0);
        final ItemMarketingActivityDTO itemMarketingActivityDTO1 = new ItemMarketingActivityDTO();
        itemMarketingActivityDTO1.setSpecialsType(0);
        itemMarketingActivityDTO1.setSpecialsNumber(new BigDecimal("0.00"));
        itemMarketingActivityDTO1.setLimitNumber(0);
        final ItemInfoDTO itemInfoDTO = new ItemInfoDTO("itemGuid", "parentGuid", "typeGuid", "typeName", 0,
                new BigDecimal("0.00"), false, new BigDecimal("0.00"), new BigDecimal("0.00"), "showUnit",
                new BigDecimal("0.00"), "showMemberRule", "name", "pinyin", "nameAbbr", "description", "englishBrief",
                "englishIngredientsDesc", "pictureUrl", "bigPictureUrl", "detailBigPictureUrl", 0, 0, false, 0,
                new BigDecimal("0.00"), Arrays.asList(new ItemInfoTagDTO("id", "name")), 0, 0, 0, Arrays.asList(
                new ItemInfoSkuDTO("skuGuid", "parentGuid", "name", "unit", "code", new BigDecimal("0.00"), false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        0, 0, 0, 0, new BigDecimal("0.00"), Arrays.asList("value"), Arrays.asList("value"),
                        Arrays.asList(activityRuleDescDTO2))), Arrays.asList(
                new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                        new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name", new BigDecimal("0.00"), 0)), 0)),
                Arrays.asList(new ItemInfoSubgroupDTO("subgroupGuid", "name", 0, 0, false, Arrays.asList(
                        new ItemInfoSubSkuDTO("itemGuid", "itemName", "code", "itemTypeGuid", "itemTypeName", 0, "unit",
                                "skuGuid", "skuName", "pictureUrl", new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), 0,
                                0, Arrays.asList(new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name", new BigDecimal("0.00"), 0)),
                                0)), 0, new BigDecimal("0.00"), 0)))), new BigDecimal("0.00"), 0, false,
                Arrays.asList("value"), Arrays.asList("value"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), 0,
                new BigDecimal("0.00"), Arrays.asList(activityRuleDescDTO3), Arrays.asList(0),
                itemMarketingActivityDTO1, 0, new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), null, null);
        when(mockMenuItemService.itemPrice(itemInfoDTO)).thenReturn(pricePairDTO);

        // Configure MemberService.queryCardRightsInfo(...).
        final ResponseCardRights responseCardRights = new ResponseCardRights();
        responseCardRights.setRightsGuid("rightsGuid");
        responseCardRights.setHasMemberPrice(0);
        responseCardRights.setHasMemberDiscount(0);
        responseCardRights.setMemberDiscountValue(new BigDecimal("0.00"));
        responseCardRights.setHasBirthdayRights(0);

        // Configure MarketingActivityHelper.queryMemberRights(...).
        final ResponseProductDiscount responseProductDiscount = new ResponseProductDiscount();
        responseProductDiscount.setMemberPrice(false);
        final ResponseDiscount responseDiscount = new ResponseDiscount();
        responseDiscount.setAllProducts(false);
        responseDiscount.setProductDiscounts(Arrays.asList("value"));
        responseDiscount.setProductKeys(Arrays.asList("value"));
        responseProductDiscount.setDiscountRespDTOList(Arrays.asList(responseDiscount));
        when(mockMarketingActivityHelper.queryMemberRights("wxToken")).thenReturn(responseProductDiscount);

        // Configure MarketingActivityHelper.querySelectSpecialsActivityDetailsVO(...).
        final LimitSpecialsActivityDetailsVO limitSpecialsActivityDetailsVO = new LimitSpecialsActivityDetailsVO();
        limitSpecialsActivityDetailsVO.setGuid("3cd72de4-005b-462e-a07a-e8fc23f814ba");
        limitSpecialsActivityDetailsVO.setName("name");
        limitSpecialsActivityDetailsVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        limitSpecialsActivityDetailsVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        limitSpecialsActivityDetailsVO.setIsLimitPeriod(0);
        final ActivitySelectDTO activitySelectDTO2 = new ActivitySelectDTO();
        activitySelectDTO2.setActivityGuid("activityGuid");
        activitySelectDTO2.setActivityType(0);
        final List<ActivitySelectDTO> activitySelectList = Arrays.asList(activitySelectDTO2);
        when(mockMarketingActivityHelper.querySelectSpecialsActivityDetailsVO(activitySelectList))
                .thenReturn(limitSpecialsActivityDetailsVO);

        // Configure HsaBaseClientService.getActivityInfo(...).
        final ResponseClientMarketActivity responseClientMarketActivity = new ResponseClientMarketActivity();
        responseClientMarketActivity.setActivityType(0);
        responseClientMarketActivity.setGuid("0a8056b4-466c-4094-96ea-b7ff560561da");
        responseClientMarketActivity.setActivityTitle("activityTitle");
        responseClientMarketActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        responseClientMarketActivity.setIsShare(0);
        final ResponseModel<ResponseClientMarketActivity> responseClientMarketActivityResponseModel = new ResponseModel<>(
                responseClientMarketActivity);
        when(mockWechatClientService.getActivityInfo("activityGuid"))
                .thenReturn(responseClientMarketActivityResponseModel);

        // Configure WxStoreTradeOrderService.querySurchargeListByRedis(...).
        final List<SurchargeLinkDTO> surchargeLinkDTOS = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        when(mockWxStoreTradeOrderService.querySurchargeListByRedis("tableGuid")).thenReturn(surchargeLinkDTOS);

        when(mockMenuItemService.queryGuestCount()).thenReturn(0);

        // Run the test
        final CalculateOrderRespDTO result = calculateServiceImplUnderTest.calculate(calculateDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockUserMemberSessionUtils).addUserMemberSession(
                new UserMemberSessionDTO("storeGuid", "openId", "userGuid", false, "nickName", "memberInfoGuid",
                        "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false, "volumeCode", false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid"));
        verify(mockRedisUtils).delete("key");

        // Confirm MenuItemService.calculateMemberDiscount(...).
        final ActivityRuleDescDTO activityRuleDescDTO4 = new ActivityRuleDescDTO();
        activityRuleDescDTO4.setActivityGuid("activityGuid");
        activityRuleDescDTO4.setActivityType(0);
        activityRuleDescDTO4.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO4.setRelationRule(0);
        final ActivityRuleDescDTO activityRuleDescDTO5 = new ActivityRuleDescDTO();
        activityRuleDescDTO5.setActivityGuid("activityGuid");
        activityRuleDescDTO5.setActivityType(0);
        activityRuleDescDTO5.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO5.setRelationRule(0);
        final ItemMarketingActivityDTO itemMarketingActivityDTO2 = new ItemMarketingActivityDTO();
        itemMarketingActivityDTO2.setSpecialsType(0);
        itemMarketingActivityDTO2.setSpecialsNumber(new BigDecimal("0.00"));
        itemMarketingActivityDTO2.setLimitNumber(0);
        final ShopCartRespDTO shopCartRespDTO = new ShopCartRespDTO(Arrays.asList(
                new ItemInfoDTO("itemGuid", "parentGuid", "typeGuid", "typeName", 0, new BigDecimal("0.00"), false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "showUnit", new BigDecimal("0.00"),
                        "showMemberRule", "name", "pinyin", "nameAbbr", "description", "englishBrief",
                        "englishIngredientsDesc", "pictureUrl", "bigPictureUrl", "detailBigPictureUrl", 0, 0, false, 0,
                        new BigDecimal("0.00"), Arrays.asList(new ItemInfoTagDTO("id", "name")), 0, 0, 0, Arrays.asList(
                        new ItemInfoSkuDTO("skuGuid", "parentGuid", "name", "unit", "code", new BigDecimal("0.00"),
                                false, new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                                new BigDecimal("0.00"), 0, 0, 0, 0, new BigDecimal("0.00"), Arrays.asList("value"),
                                Arrays.asList("value"), Arrays.asList(activityRuleDescDTO4))), Arrays.asList(
                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name", new BigDecimal("0.00"), 0)),
                                0)), Arrays.asList(new ItemInfoSubgroupDTO("subgroupGuid", "name", 0, 0, false,
                        Arrays.asList(
                                new ItemInfoSubSkuDTO("itemGuid", "itemName", "code", "itemTypeGuid", "itemTypeName", 0,
                                        "unit", "skuGuid", "skuName", "pictureUrl", new BigDecimal("0.00"), new BigDecimal("0.00"),
                                        new BigDecimal("0.00"), 0, 0, Arrays.asList(
                                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name",
                                                        new BigDecimal("0.00"), 0)), 0)), 0, new BigDecimal("0.00"),
                                        0)))), new BigDecimal("0.00"), 0, false, Arrays.asList("value"),
                        Arrays.asList("value"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), Arrays.asList(activityRuleDescDTO5), Arrays.asList(0),
                        itemMarketingActivityDTO2, 0, new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), null, null)), "areaName", "tableCode", false, new BigDecimal("0.00"), false,
                new BigDecimal("0.00"), 0, new BigDecimal("0.00"), new BigDecimal("0.00"));
        final ActivityRuleDescDTO activityRuleDescDTO6 = new ActivityRuleDescDTO();
        activityRuleDescDTO6.setActivityGuid("activityGuid");
        activityRuleDescDTO6.setActivityType(0);
        activityRuleDescDTO6.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO6.setRelationRule(0);
        final ActivityRuleDescDTO activityRuleDescDTO7 = new ActivityRuleDescDTO();
        activityRuleDescDTO7.setActivityGuid("activityGuid");
        activityRuleDescDTO7.setActivityType(0);
        activityRuleDescDTO7.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO7.setRelationRule(0);
        final ItemMarketingActivityDTO itemMarketingActivityDTO3 = new ItemMarketingActivityDTO();
        itemMarketingActivityDTO3.setSpecialsType(0);
        itemMarketingActivityDTO3.setSpecialsNumber(new BigDecimal("0.00"));
        itemMarketingActivityDTO3.setLimitNumber(0);
        final List<ShopCartItemReqDTO> cartItemList = Arrays.asList(new ShopCartItemReqDTO(
                new ItemInfoDTO("itemGuid", "parentGuid", "typeGuid", "typeName", 0, new BigDecimal("0.00"), false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "showUnit", new BigDecimal("0.00"),
                        "showMemberRule", "name", "pinyin", "nameAbbr", "description", "englishBrief",
                        "englishIngredientsDesc", "pictureUrl", "bigPictureUrl", "detailBigPictureUrl", 0, 0, false, 0,
                        new BigDecimal("0.00"), Arrays.asList(new ItemInfoTagDTO("id", "name")), 0, 0, 0, Arrays.asList(
                        new ItemInfoSkuDTO("skuGuid", "parentGuid", "name", "unit", "code", new BigDecimal("0.00"),
                                false, new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                                new BigDecimal("0.00"), 0, 0, 0, 0, new BigDecimal("0.00"), Arrays.asList("value"),
                                Arrays.asList("value"), Arrays.asList(activityRuleDescDTO6))), Arrays.asList(
                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name", new BigDecimal("0.00"), 0)),
                                0)), Arrays.asList(new ItemInfoSubgroupDTO("subgroupGuid", "name", 0, 0, false,
                        Arrays.asList(
                                new ItemInfoSubSkuDTO("itemGuid", "itemName", "code", "itemTypeGuid", "itemTypeName", 0,
                                        "unit", "skuGuid", "skuName", "pictureUrl", new BigDecimal("0.00"), new BigDecimal("0.00"),
                                        new BigDecimal("0.00"), 0, 0, Arrays.asList(
                                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name",
                                                        new BigDecimal("0.00"), 0)), 0)), 0, new BigDecimal("0.00"),
                                        0)))), new BigDecimal("0.00"), 0, false, Arrays.asList("value"),
                        Arrays.asList("value"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), Arrays.asList(activityRuleDescDTO7), Arrays.asList(0),
                        itemMarketingActivityDTO3, 0, new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), null, null), false, new BigDecimal("0.00"), "openid", "nickName", "headImgUrl",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        verify(mockMenuItemService).calculateMemberDiscount(shopCartRespDTO, cartItemList);

        // Confirm DiscountChain.doDiscount(...).
        final DiscountContext context = new DiscountContext();
        final CalculateOrderDTO calculateOrderDTO = new CalculateOrderDTO();
        calculateOrderDTO.setDeviceType(0);
        calculateOrderDTO.setUseMemberDiscountFlag(false);
        calculateOrderDTO.setMemberIntegralStore(false);
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setGuid("c73de291-567a-48a9-b46c-8f0a00b5216e");
        dineInItemDTO2.setSkuGuid("skuGuid");
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO2.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setMinPriceType(0);
        calculateOrderDTO.setDineInItemList(Arrays.asList(dineInItemDTO2));
        calculateOrderDTO.setIsFirst(false);
        calculateOrderDTO.setIsReplace(false);
        calculateOrderDTO.setOrderGuid("orderGuid");
        context.setCalculateOrderDTO(calculateOrderDTO);
        final CalculateOrderRespDTO calculateOrderRespDTO = new CalculateOrderRespDTO();
        calculateOrderRespDTO.setOrderSurplusFee(new BigDecimal("0.00"));
        calculateOrderRespDTO.setOrderSurplusFeeBySkipSpecials(new BigDecimal("0.00"));
        calculateOrderRespDTO.setOrderFee(new BigDecimal("0.00"));
        final ActivitySelectDTO activitySelectDTO3 = new ActivitySelectDTO();
        calculateOrderRespDTO.setActivitySelectList(Arrays.asList(activitySelectDTO3));
        final MarketingActivityInfoRespDTO marketingActivityInfoRespDTO1 = new MarketingActivityInfoRespDTO();
        calculateOrderRespDTO.setActivityInfoList(Arrays.asList(marketingActivityInfoRespDTO1));
        context.setCalculateOrderRespDTO(calculateOrderRespDTO);
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setGuid("c73de291-567a-48a9-b46c-8f0a00b5216e");
        dineInItemDTO3.setSkuGuid("skuGuid");
        dineInItemDTO3.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO3.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO3.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setMinPriceType(0);
        context.setAllItems(Arrays.asList(dineInItemDTO3));
        final DiscountRuleBO discountRuleBO = new DiscountRuleBO();
        discountRuleBO.setVolumeGuid("volumeGuid");
        discountRuleBO.setVolumeCodeType(0);
        discountRuleBO.setHasMemberPrice(false);
        discountRuleBO.setHasMemberDiscount(false);
        discountRuleBO.setMemberDiscount(new BigDecimal("0.00"));
        context.setDiscountRuleBO(discountRuleBO);
        context.setDiscountTypeMap(new HashMap<>());
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        context.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        context.setDineInItemDTOMap(new HashMap<>());
        final UserMemberSessionDTO userMemberSession = new UserMemberSessionDTO();
        userMemberSession.setIsLogin(false);
        context.setUserMemberSession(userMemberSession);
        context.setVolumeGuid("volumeGuid");
        context.setVolumeCodeType(0);
        context.setIntegralStore(false);
        context.setRejectDiscount(false);
        context.setHasMember(false);
        context.setUseMemberPriceFlag(false);
        context.setUseMemberDiscountFlag(false);
        context.setNeedFullMarketActivityList(false);
        context.setNeedLimitSpecialsMarketActivityList(false);
        context.setIsFirst(false);
        context.setIsReplace(false);
        context.setOrderGuid("orderGuid");
        context.setMemberConsumptionGuid("memberConsumptionGuid");
        verify(mockDiscountChain).doDiscount(context);
        verify(mockRedisUtils).setEx("key", "value", 30L, TimeUnit.MINUTES);

        // Confirm TradeClientService.updateMemberConsumptionGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setCheckoutStaffGuid("checkoutStaffGuid");
        orderDTO.setCheckoutStaffName("checkoutStaffName");
        orderDTO.setOrderGuid("orderGuid");
        orderDTO.setBillGuid("billGuid");
        orderDTO.setMemberConsumptionGuid("memberConsumptionGuid");
        verify(mockTradeClientService).updateMemberConsumptionGuid(orderDTO);
    }

    @Test
    public void testCalculate_UserMemberSessionUtilsGetCardListReturnsNoItems() {
        // Setup
        final CalculateOrderDTO calculateDTO = new CalculateOrderDTO();
        calculateDTO.setDeviceType(0);
        calculateDTO.setUseMemberDiscountFlag(false);
        calculateDTO.setMemberIntegralStore(false);
        calculateDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        calculateDTO.setVolumeCodes(Arrays.asList("value"));
        final ActivitySelectDTO activitySelectDTO = new ActivitySelectDTO();
        activitySelectDTO.setActivityGuid("activityGuid");
        activitySelectDTO.setActivityType(0);
        calculateDTO.setActivitySelectList(Arrays.asList(activitySelectDTO));
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("c73de291-567a-48a9-b46c-8f0a00b5216e");
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPriceType(0);
        calculateDTO.setDineInItemList(Arrays.asList(dineInItemDTO));
        calculateDTO.setIsFirst(false);
        calculateDTO.setIsReplace(false);
        calculateDTO.setOrderGuid("orderGuid");
        calculateDTO.setVerify(0);

        final CalculateOrderRespDTO expectedResult = new CalculateOrderRespDTO();
        expectedResult.setUseMemberDiscountFlag(false);
        expectedResult.setHasMemberPrice(false);
        expectedResult.setHasMemberDiscount(false);
        expectedResult.setMemberDiscount(new BigDecimal("0.00"));
        expectedResult.setUnAbleMemberDiscountFlag(false);
        expectedResult.setUnAbleMemberDiscountReason("");
        expectedResult.setOrderSurplusFee(new BigDecimal("0.00"));
        expectedResult.setOrderSurplusFeeBySkipSpecials(new BigDecimal("0.00"));
        expectedResult.setOrderFee(new BigDecimal("0.00"));
        expectedResult.setAppendFee(new BigDecimal("0.00"));
        expectedResult.setDiscountFee(new BigDecimal("0.00"));
        expectedResult.setActuallyPayFee(new BigDecimal("0.00"));
        final ActivitySelectDTO activitySelectDTO1 = new ActivitySelectDTO();
        expectedResult.setActivitySelectList(Arrays.asList(activitySelectDTO1));
        final MarketingActivityInfoRespDTO marketingActivityInfoRespDTO = new MarketingActivityInfoRespDTO();
        expectedResult.setActivityInfoList(Arrays.asList(marketingActivityInfoRespDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        expectedResult.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        final SurchargeLinkDTO surchargeLinkDTO = new SurchargeLinkDTO();
        surchargeLinkDTO.setAmount(new BigDecimal("0.00"));
        surchargeLinkDTO.setType(0);
        expectedResult.setAppendFeeDetailDTOS(Arrays.asList(surchargeLinkDTO));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("c73de291-567a-48a9-b46c-8f0a00b5216e");
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPriceType(0);
        expectedResult.setDineInItemList(Arrays.asList(dineInItemDTO1));

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure HsmTerminalServiceClient.getVolumeByCode(...).
        final ResponseVolumeList responseVolumeList = new ResponseVolumeList();
        responseVolumeList.setVolumeCode("volumeCode");
        responseVolumeList.setVolumeInfoGuid("volumeGuid");
        responseVolumeList.setMemberVolumeGuid("memberVolumeGuid");
        responseVolumeList.setVolumeType(0);
        responseVolumeList.setIsUseAlone(0);
        when(mockTerminalServiceClient.getVolumeByCode("volumeCode")).thenReturn(responseVolumeList);

        when(mockUserMemberSessionUtils.getCardList("storeGuid", "openId")).thenReturn(Collections.emptyList());

        // Configure MenuItemService.getCartItemList(...).
        final ActivityRuleDescDTO activityRuleDescDTO = new ActivityRuleDescDTO();
        activityRuleDescDTO.setActivityGuid("activityGuid");
        activityRuleDescDTO.setActivityType(0);
        activityRuleDescDTO.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO.setRelationRule(0);
        final ActivityRuleDescDTO activityRuleDescDTO1 = new ActivityRuleDescDTO();
        activityRuleDescDTO1.setActivityGuid("activityGuid");
        activityRuleDescDTO1.setActivityType(0);
        activityRuleDescDTO1.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO1.setRelationRule(0);
        final ItemMarketingActivityDTO itemMarketingActivityDTO = new ItemMarketingActivityDTO();
        itemMarketingActivityDTO.setSpecialsType(0);
        itemMarketingActivityDTO.setSpecialsNumber(new BigDecimal("0.00"));
        itemMarketingActivityDTO.setLimitNumber(0);
        final List<ShopCartItemReqDTO> shopCartItemReqDTOS = Arrays.asList(new ShopCartItemReqDTO(
                new ItemInfoDTO("itemGuid", "parentGuid", "typeGuid", "typeName", 0, new BigDecimal("0.00"), false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "showUnit", new BigDecimal("0.00"),
                        "showMemberRule", "name", "pinyin", "nameAbbr", "description", "englishBrief",
                        "englishIngredientsDesc", "pictureUrl", "bigPictureUrl", "detailBigPictureUrl", 0, 0, false, 0,
                        new BigDecimal("0.00"), Arrays.asList(new ItemInfoTagDTO("id", "name")), 0, 0, 0, Arrays.asList(
                        new ItemInfoSkuDTO("skuGuid", "parentGuid", "name", "unit", "code", new BigDecimal("0.00"),
                                false, new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                                new BigDecimal("0.00"), 0, 0, 0, 0, new BigDecimal("0.00"), Arrays.asList("value"),
                                Arrays.asList("value"), Arrays.asList(activityRuleDescDTO))), Arrays.asList(
                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name", new BigDecimal("0.00"), 0)),
                                0)), Arrays.asList(new ItemInfoSubgroupDTO("subgroupGuid", "name", 0, 0, false,
                        Arrays.asList(
                                new ItemInfoSubSkuDTO("itemGuid", "itemName", "code", "itemTypeGuid", "itemTypeName", 0,
                                        "unit", "skuGuid", "skuName", "pictureUrl", new BigDecimal("0.00"), new BigDecimal("0.00"),
                                        new BigDecimal("0.00"), 0, 0, Arrays.asList(
                                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name",
                                                        new BigDecimal("0.00"), 0)), 0)), 0, new BigDecimal("0.00"),
                                        0)))), new BigDecimal("0.00"), 0, false, Arrays.asList("value"),
                        Arrays.asList("value"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), Arrays.asList(activityRuleDescDTO1), Arrays.asList(0),
                        itemMarketingActivityDTO, 0, new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), null, null), false, new BigDecimal("0.00"), "openid", "nickName", "headImgUrl",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        when(mockMenuItemService.getCartItemList()).thenReturn(shopCartItemReqDTOS);

        // Configure MenuItemService.itemPrice(...).
        final PricePairDTO pricePairDTO = new PricePairDTO(new BigDecimal("0.00"), new BigDecimal("0.00"));
        final ActivityRuleDescDTO activityRuleDescDTO2 = new ActivityRuleDescDTO();
        activityRuleDescDTO2.setActivityGuid("activityGuid");
        activityRuleDescDTO2.setActivityType(0);
        activityRuleDescDTO2.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO2.setRelationRule(0);
        final ActivityRuleDescDTO activityRuleDescDTO3 = new ActivityRuleDescDTO();
        activityRuleDescDTO3.setActivityGuid("activityGuid");
        activityRuleDescDTO3.setActivityType(0);
        activityRuleDescDTO3.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO3.setRelationRule(0);
        final ItemMarketingActivityDTO itemMarketingActivityDTO1 = new ItemMarketingActivityDTO();
        itemMarketingActivityDTO1.setSpecialsType(0);
        itemMarketingActivityDTO1.setSpecialsNumber(new BigDecimal("0.00"));
        itemMarketingActivityDTO1.setLimitNumber(0);
        final ItemInfoDTO itemInfoDTO = new ItemInfoDTO("itemGuid", "parentGuid", "typeGuid", "typeName", 0,
                new BigDecimal("0.00"), false, new BigDecimal("0.00"), new BigDecimal("0.00"), "showUnit",
                new BigDecimal("0.00"), "showMemberRule", "name", "pinyin", "nameAbbr", "description", "englishBrief",
                "englishIngredientsDesc", "pictureUrl", "bigPictureUrl", "detailBigPictureUrl", 0, 0, false, 0,
                new BigDecimal("0.00"), Arrays.asList(new ItemInfoTagDTO("id", "name")), 0, 0, 0, Arrays.asList(
                new ItemInfoSkuDTO("skuGuid", "parentGuid", "name", "unit", "code", new BigDecimal("0.00"), false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        0, 0, 0, 0, new BigDecimal("0.00"), Arrays.asList("value"), Arrays.asList("value"),
                        Arrays.asList(activityRuleDescDTO2))), Arrays.asList(
                new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                        new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name", new BigDecimal("0.00"), 0)), 0)),
                Arrays.asList(new ItemInfoSubgroupDTO("subgroupGuid", "name", 0, 0, false, Arrays.asList(
                        new ItemInfoSubSkuDTO("itemGuid", "itemName", "code", "itemTypeGuid", "itemTypeName", 0, "unit",
                                "skuGuid", "skuName", "pictureUrl", new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), 0,
                                0, Arrays.asList(new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name", new BigDecimal("0.00"), 0)),
                                0)), 0, new BigDecimal("0.00"), 0)))), new BigDecimal("0.00"), 0, false,
                Arrays.asList("value"), Arrays.asList("value"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), 0,
                new BigDecimal("0.00"), Arrays.asList(activityRuleDescDTO3), Arrays.asList(0),
                itemMarketingActivityDTO1, 0, new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), null, null);
        when(mockMenuItemService.itemPrice(itemInfoDTO)).thenReturn(pricePairDTO);

        // Configure MemberService.queryCardRightsInfo(...).
        final ResponseCardRights responseCardRights = new ResponseCardRights();
        responseCardRights.setRightsGuid("rightsGuid");
        responseCardRights.setHasMemberPrice(0);
        responseCardRights.setHasMemberDiscount(0);
        responseCardRights.setMemberDiscountValue(new BigDecimal("0.00"));
        responseCardRights.setHasBirthdayRights(0);

        // Configure MarketingActivityHelper.queryMemberRights(...).
        final ResponseProductDiscount responseProductDiscount = new ResponseProductDiscount();
        responseProductDiscount.setMemberPrice(false);
        final ResponseDiscount responseDiscount = new ResponseDiscount();
        responseDiscount.setAllProducts(false);
        responseDiscount.setProductDiscounts(Arrays.asList("value"));
        responseDiscount.setProductKeys(Arrays.asList("value"));
        responseProductDiscount.setDiscountRespDTOList(Arrays.asList(responseDiscount));
        when(mockMarketingActivityHelper.queryMemberRights("wxToken")).thenReturn(responseProductDiscount);

        // Configure MarketingActivityHelper.querySelectSpecialsActivityDetailsVO(...).
        final LimitSpecialsActivityDetailsVO limitSpecialsActivityDetailsVO = new LimitSpecialsActivityDetailsVO();
        limitSpecialsActivityDetailsVO.setGuid("3cd72de4-005b-462e-a07a-e8fc23f814ba");
        limitSpecialsActivityDetailsVO.setName("name");
        limitSpecialsActivityDetailsVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        limitSpecialsActivityDetailsVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        limitSpecialsActivityDetailsVO.setIsLimitPeriod(0);
        final ActivitySelectDTO activitySelectDTO2 = new ActivitySelectDTO();
        activitySelectDTO2.setActivityGuid("activityGuid");
        activitySelectDTO2.setActivityType(0);
        final List<ActivitySelectDTO> activitySelectList = Arrays.asList(activitySelectDTO2);
        when(mockMarketingActivityHelper.querySelectSpecialsActivityDetailsVO(activitySelectList))
                .thenReturn(limitSpecialsActivityDetailsVO);

        // Configure HsaBaseClientService.getActivityInfo(...).
        final ResponseClientMarketActivity responseClientMarketActivity = new ResponseClientMarketActivity();
        responseClientMarketActivity.setActivityType(0);
        responseClientMarketActivity.setGuid("0a8056b4-466c-4094-96ea-b7ff560561da");
        responseClientMarketActivity.setActivityTitle("activityTitle");
        responseClientMarketActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        responseClientMarketActivity.setIsShare(0);
        final ResponseModel<ResponseClientMarketActivity> responseClientMarketActivityResponseModel = new ResponseModel<>(
                responseClientMarketActivity);
        when(mockWechatClientService.getActivityInfo("activityGuid"))
                .thenReturn(responseClientMarketActivityResponseModel);

        // Configure WxStoreTradeOrderService.querySurchargeListByRedis(...).
        final List<SurchargeLinkDTO> surchargeLinkDTOS = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        when(mockWxStoreTradeOrderService.querySurchargeListByRedis("tableGuid")).thenReturn(surchargeLinkDTOS);

        when(mockMenuItemService.queryGuestCount()).thenReturn(0);

        // Run the test
        final CalculateOrderRespDTO result = calculateServiceImplUnderTest.calculate(calculateDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockUserMemberSessionUtils).addUserMemberSession(
                new UserMemberSessionDTO("storeGuid", "openId", "userGuid", false, "nickName", "memberInfoGuid",
                        "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false, "volumeCode", false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid"));
        verify(mockRedisUtils).delete("key");

        // Confirm MenuItemService.calculateMemberDiscount(...).
        final ActivityRuleDescDTO activityRuleDescDTO4 = new ActivityRuleDescDTO();
        activityRuleDescDTO4.setActivityGuid("activityGuid");
        activityRuleDescDTO4.setActivityType(0);
        activityRuleDescDTO4.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO4.setRelationRule(0);
        final ActivityRuleDescDTO activityRuleDescDTO5 = new ActivityRuleDescDTO();
        activityRuleDescDTO5.setActivityGuid("activityGuid");
        activityRuleDescDTO5.setActivityType(0);
        activityRuleDescDTO5.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO5.setRelationRule(0);
        final ItemMarketingActivityDTO itemMarketingActivityDTO2 = new ItemMarketingActivityDTO();
        itemMarketingActivityDTO2.setSpecialsType(0);
        itemMarketingActivityDTO2.setSpecialsNumber(new BigDecimal("0.00"));
        itemMarketingActivityDTO2.setLimitNumber(0);
        final ShopCartRespDTO shopCartRespDTO = new ShopCartRespDTO(Arrays.asList(
                new ItemInfoDTO("itemGuid", "parentGuid", "typeGuid", "typeName", 0, new BigDecimal("0.00"), false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "showUnit", new BigDecimal("0.00"),
                        "showMemberRule", "name", "pinyin", "nameAbbr", "description", "englishBrief",
                        "englishIngredientsDesc", "pictureUrl", "bigPictureUrl", "detailBigPictureUrl", 0, 0, false, 0,
                        new BigDecimal("0.00"), Arrays.asList(new ItemInfoTagDTO("id", "name")), 0, 0, 0, Arrays.asList(
                        new ItemInfoSkuDTO("skuGuid", "parentGuid", "name", "unit", "code", new BigDecimal("0.00"),
                                false, new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                                new BigDecimal("0.00"), 0, 0, 0, 0, new BigDecimal("0.00"), Arrays.asList("value"),
                                Arrays.asList("value"), Arrays.asList(activityRuleDescDTO4))), Arrays.asList(
                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name", new BigDecimal("0.00"), 0)),
                                0)), Arrays.asList(new ItemInfoSubgroupDTO("subgroupGuid", "name", 0, 0, false,
                        Arrays.asList(
                                new ItemInfoSubSkuDTO("itemGuid", "itemName", "code", "itemTypeGuid", "itemTypeName", 0,
                                        "unit", "skuGuid", "skuName", "pictureUrl", new BigDecimal("0.00"), new BigDecimal("0.00"),
                                        new BigDecimal("0.00"), 0, 0, Arrays.asList(
                                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name",
                                                        new BigDecimal("0.00"), 0)), 0)), 0, new BigDecimal("0.00"),
                                        0)))), new BigDecimal("0.00"), 0, false, Arrays.asList("value"),
                        Arrays.asList("value"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), Arrays.asList(activityRuleDescDTO5), Arrays.asList(0),
                        itemMarketingActivityDTO2, 0, new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), null, null)), "areaName", "tableCode", false, new BigDecimal("0.00"), false,
                new BigDecimal("0.00"), 0, new BigDecimal("0.00"), new BigDecimal("0.00"));
        final ActivityRuleDescDTO activityRuleDescDTO6 = new ActivityRuleDescDTO();
        activityRuleDescDTO6.setActivityGuid("activityGuid");
        activityRuleDescDTO6.setActivityType(0);
        activityRuleDescDTO6.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO6.setRelationRule(0);
        final ActivityRuleDescDTO activityRuleDescDTO7 = new ActivityRuleDescDTO();
        activityRuleDescDTO7.setActivityGuid("activityGuid");
        activityRuleDescDTO7.setActivityType(0);
        activityRuleDescDTO7.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO7.setRelationRule(0);
        final ItemMarketingActivityDTO itemMarketingActivityDTO3 = new ItemMarketingActivityDTO();
        itemMarketingActivityDTO3.setSpecialsType(0);
        itemMarketingActivityDTO3.setSpecialsNumber(new BigDecimal("0.00"));
        itemMarketingActivityDTO3.setLimitNumber(0);
        final List<ShopCartItemReqDTO> cartItemList = Arrays.asList(new ShopCartItemReqDTO(
                new ItemInfoDTO("itemGuid", "parentGuid", "typeGuid", "typeName", 0, new BigDecimal("0.00"), false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "showUnit", new BigDecimal("0.00"),
                        "showMemberRule", "name", "pinyin", "nameAbbr", "description", "englishBrief",
                        "englishIngredientsDesc", "pictureUrl", "bigPictureUrl", "detailBigPictureUrl", 0, 0, false, 0,
                        new BigDecimal("0.00"), Arrays.asList(new ItemInfoTagDTO("id", "name")), 0, 0, 0, Arrays.asList(
                        new ItemInfoSkuDTO("skuGuid", "parentGuid", "name", "unit", "code", new BigDecimal("0.00"),
                                false, new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                                new BigDecimal("0.00"), 0, 0, 0, 0, new BigDecimal("0.00"), Arrays.asList("value"),
                                Arrays.asList("value"), Arrays.asList(activityRuleDescDTO6))), Arrays.asList(
                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name", new BigDecimal("0.00"), 0)),
                                0)), Arrays.asList(new ItemInfoSubgroupDTO("subgroupGuid", "name", 0, 0, false,
                        Arrays.asList(
                                new ItemInfoSubSkuDTO("itemGuid", "itemName", "code", "itemTypeGuid", "itemTypeName", 0,
                                        "unit", "skuGuid", "skuName", "pictureUrl", new BigDecimal("0.00"), new BigDecimal("0.00"),
                                        new BigDecimal("0.00"), 0, 0, Arrays.asList(
                                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name",
                                                        new BigDecimal("0.00"), 0)), 0)), 0, new BigDecimal("0.00"),
                                        0)))), new BigDecimal("0.00"), 0, false, Arrays.asList("value"),
                        Arrays.asList("value"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), Arrays.asList(activityRuleDescDTO7), Arrays.asList(0),
                        itemMarketingActivityDTO3, 0, new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), null, null), false, new BigDecimal("0.00"), "openid", "nickName", "headImgUrl",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        verify(mockMenuItemService).calculateMemberDiscount(shopCartRespDTO, cartItemList);

        // Confirm DiscountChain.doDiscount(...).
        final DiscountContext context = new DiscountContext();
        final CalculateOrderDTO calculateOrderDTO = new CalculateOrderDTO();
        calculateOrderDTO.setDeviceType(0);
        calculateOrderDTO.setUseMemberDiscountFlag(false);
        calculateOrderDTO.setMemberIntegralStore(false);
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setGuid("c73de291-567a-48a9-b46c-8f0a00b5216e");
        dineInItemDTO2.setSkuGuid("skuGuid");
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO2.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setMinPriceType(0);
        calculateOrderDTO.setDineInItemList(Arrays.asList(dineInItemDTO2));
        calculateOrderDTO.setIsFirst(false);
        calculateOrderDTO.setIsReplace(false);
        calculateOrderDTO.setOrderGuid("orderGuid");
        context.setCalculateOrderDTO(calculateOrderDTO);
        final CalculateOrderRespDTO calculateOrderRespDTO = new CalculateOrderRespDTO();
        calculateOrderRespDTO.setOrderSurplusFee(new BigDecimal("0.00"));
        calculateOrderRespDTO.setOrderSurplusFeeBySkipSpecials(new BigDecimal("0.00"));
        calculateOrderRespDTO.setOrderFee(new BigDecimal("0.00"));
        final ActivitySelectDTO activitySelectDTO3 = new ActivitySelectDTO();
        calculateOrderRespDTO.setActivitySelectList(Arrays.asList(activitySelectDTO3));
        final MarketingActivityInfoRespDTO marketingActivityInfoRespDTO1 = new MarketingActivityInfoRespDTO();
        calculateOrderRespDTO.setActivityInfoList(Arrays.asList(marketingActivityInfoRespDTO1));
        context.setCalculateOrderRespDTO(calculateOrderRespDTO);
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setGuid("c73de291-567a-48a9-b46c-8f0a00b5216e");
        dineInItemDTO3.setSkuGuid("skuGuid");
        dineInItemDTO3.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO3.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO3.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setMinPriceType(0);
        context.setAllItems(Arrays.asList(dineInItemDTO3));
        final DiscountRuleBO discountRuleBO = new DiscountRuleBO();
        discountRuleBO.setVolumeGuid("volumeGuid");
        discountRuleBO.setVolumeCodeType(0);
        discountRuleBO.setHasMemberPrice(false);
        discountRuleBO.setHasMemberDiscount(false);
        discountRuleBO.setMemberDiscount(new BigDecimal("0.00"));
        context.setDiscountRuleBO(discountRuleBO);
        context.setDiscountTypeMap(new HashMap<>());
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        context.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        context.setDineInItemDTOMap(new HashMap<>());
        final UserMemberSessionDTO userMemberSession = new UserMemberSessionDTO();
        userMemberSession.setIsLogin(false);
        context.setUserMemberSession(userMemberSession);
        context.setVolumeGuid("volumeGuid");
        context.setVolumeCodeType(0);
        context.setIntegralStore(false);
        context.setRejectDiscount(false);
        context.setHasMember(false);
        context.setUseMemberPriceFlag(false);
        context.setUseMemberDiscountFlag(false);
        context.setNeedFullMarketActivityList(false);
        context.setNeedLimitSpecialsMarketActivityList(false);
        context.setIsFirst(false);
        context.setIsReplace(false);
        context.setOrderGuid("orderGuid");
        context.setMemberConsumptionGuid("memberConsumptionGuid");
        verify(mockDiscountChain).doDiscount(context);
        verify(mockRedisUtils).setEx("key", "value", 30L, TimeUnit.MINUTES);

        // Confirm TradeClientService.updateMemberConsumptionGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setCheckoutStaffGuid("checkoutStaffGuid");
        orderDTO.setCheckoutStaffName("checkoutStaffName");
        orderDTO.setOrderGuid("orderGuid");
        orderDTO.setBillGuid("billGuid");
        orderDTO.setMemberConsumptionGuid("memberConsumptionGuid");
        verify(mockTradeClientService).updateMemberConsumptionGuid(orderDTO);
    }

    @Test
    public void testCalculate_MenuItemServiceGetCartItemListReturnsNoItems() {
        // Setup
        final CalculateOrderDTO calculateDTO = new CalculateOrderDTO();
        calculateDTO.setDeviceType(0);
        calculateDTO.setUseMemberDiscountFlag(false);
        calculateDTO.setMemberIntegralStore(false);
        calculateDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        calculateDTO.setVolumeCodes(Arrays.asList("value"));
        final ActivitySelectDTO activitySelectDTO = new ActivitySelectDTO();
        activitySelectDTO.setActivityGuid("activityGuid");
        activitySelectDTO.setActivityType(0);
        calculateDTO.setActivitySelectList(Arrays.asList(activitySelectDTO));
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("c73de291-567a-48a9-b46c-8f0a00b5216e");
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPriceType(0);
        calculateDTO.setDineInItemList(Arrays.asList(dineInItemDTO));
        calculateDTO.setIsFirst(false);
        calculateDTO.setIsReplace(false);
        calculateDTO.setOrderGuid("orderGuid");
        calculateDTO.setVerify(0);

        final CalculateOrderRespDTO expectedResult = new CalculateOrderRespDTO();
        expectedResult.setUseMemberDiscountFlag(false);
        expectedResult.setHasMemberPrice(false);
        expectedResult.setHasMemberDiscount(false);
        expectedResult.setMemberDiscount(new BigDecimal("0.00"));
        expectedResult.setUnAbleMemberDiscountFlag(false);
        expectedResult.setUnAbleMemberDiscountReason("");
        expectedResult.setOrderSurplusFee(new BigDecimal("0.00"));
        expectedResult.setOrderSurplusFeeBySkipSpecials(new BigDecimal("0.00"));
        expectedResult.setOrderFee(new BigDecimal("0.00"));
        expectedResult.setAppendFee(new BigDecimal("0.00"));
        expectedResult.setDiscountFee(new BigDecimal("0.00"));
        expectedResult.setActuallyPayFee(new BigDecimal("0.00"));
        final ActivitySelectDTO activitySelectDTO1 = new ActivitySelectDTO();
        expectedResult.setActivitySelectList(Arrays.asList(activitySelectDTO1));
        final MarketingActivityInfoRespDTO marketingActivityInfoRespDTO = new MarketingActivityInfoRespDTO();
        expectedResult.setActivityInfoList(Arrays.asList(marketingActivityInfoRespDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        expectedResult.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        final SurchargeLinkDTO surchargeLinkDTO = new SurchargeLinkDTO();
        surchargeLinkDTO.setAmount(new BigDecimal("0.00"));
        surchargeLinkDTO.setType(0);
        expectedResult.setAppendFeeDetailDTOS(Arrays.asList(surchargeLinkDTO));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("c73de291-567a-48a9-b46c-8f0a00b5216e");
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPriceType(0);
        expectedResult.setDineInItemList(Arrays.asList(dineInItemDTO1));

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure HsmTerminalServiceClient.getVolumeByCode(...).
        final ResponseVolumeList responseVolumeList = new ResponseVolumeList();
        responseVolumeList.setVolumeCode("volumeCode");
        responseVolumeList.setVolumeInfoGuid("volumeGuid");
        responseVolumeList.setMemberVolumeGuid("memberVolumeGuid");
        responseVolumeList.setVolumeType(0);
        responseVolumeList.setIsUseAlone(0);
        when(mockTerminalServiceClient.getVolumeByCode("volumeCode")).thenReturn(responseVolumeList);

        // Configure UserMemberSessionUtils.getCardList(...).
        final List<UserMemberCardCacheDTO> userMemberCardCacheDTOS = Arrays.asList(
                new UserMemberCardCacheDTO("cardQRCode", "cardGuid", "memberInfoCardGuid", "systemManagementGuid",
                        "enterpriseMemberInfoSystemGuid", "cardName", "cardLogo", "cardIcon", "cardColour", 0,
                        "cardStartDate", "cardEndDate", 0, "systemManagementCardNum", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "cardIntegral", "cardLevelGuid", "cardLevelName", 0, "levelIcon", 0, 0,
                        false));
        when(mockUserMemberSessionUtils.getCardList("storeGuid", "openId")).thenReturn(userMemberCardCacheDTOS);

        when(mockMenuItemService.getCartItemList()).thenReturn(Collections.emptyList());

        // Configure MemberService.queryCardRightsInfo(...).
        final ResponseCardRights responseCardRights = new ResponseCardRights();
        responseCardRights.setRightsGuid("rightsGuid");
        responseCardRights.setHasMemberPrice(0);
        responseCardRights.setHasMemberDiscount(0);
        responseCardRights.setMemberDiscountValue(new BigDecimal("0.00"));
        responseCardRights.setHasBirthdayRights(0);

        // Configure MarketingActivityHelper.queryMemberRights(...).
        final ResponseProductDiscount responseProductDiscount = new ResponseProductDiscount();
        responseProductDiscount.setMemberPrice(false);
        final ResponseDiscount responseDiscount = new ResponseDiscount();
        responseDiscount.setAllProducts(false);
        responseDiscount.setProductDiscounts(Arrays.asList("value"));
        responseDiscount.setProductKeys(Arrays.asList("value"));
        responseProductDiscount.setDiscountRespDTOList(Arrays.asList(responseDiscount));
        when(mockMarketingActivityHelper.queryMemberRights("wxToken")).thenReturn(responseProductDiscount);

        // Configure MarketingActivityHelper.querySelectSpecialsActivityDetailsVO(...).
        final LimitSpecialsActivityDetailsVO limitSpecialsActivityDetailsVO = new LimitSpecialsActivityDetailsVO();
        limitSpecialsActivityDetailsVO.setGuid("3cd72de4-005b-462e-a07a-e8fc23f814ba");
        limitSpecialsActivityDetailsVO.setName("name");
        limitSpecialsActivityDetailsVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        limitSpecialsActivityDetailsVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        limitSpecialsActivityDetailsVO.setIsLimitPeriod(0);
        final ActivitySelectDTO activitySelectDTO2 = new ActivitySelectDTO();
        activitySelectDTO2.setActivityGuid("activityGuid");
        activitySelectDTO2.setActivityType(0);
        final List<ActivitySelectDTO> activitySelectList = Arrays.asList(activitySelectDTO2);
        when(mockMarketingActivityHelper.querySelectSpecialsActivityDetailsVO(activitySelectList))
                .thenReturn(limitSpecialsActivityDetailsVO);

        // Configure HsaBaseClientService.getActivityInfo(...).
        final ResponseClientMarketActivity responseClientMarketActivity = new ResponseClientMarketActivity();
        responseClientMarketActivity.setActivityType(0);
        responseClientMarketActivity.setGuid("0a8056b4-466c-4094-96ea-b7ff560561da");
        responseClientMarketActivity.setActivityTitle("activityTitle");
        responseClientMarketActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        responseClientMarketActivity.setIsShare(0);
        final ResponseModel<ResponseClientMarketActivity> responseClientMarketActivityResponseModel = new ResponseModel<>(
                responseClientMarketActivity);
        when(mockWechatClientService.getActivityInfo("activityGuid"))
                .thenReturn(responseClientMarketActivityResponseModel);

        // Configure WxStoreTradeOrderService.querySurchargeListByRedis(...).
        final List<SurchargeLinkDTO> surchargeLinkDTOS = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        when(mockWxStoreTradeOrderService.querySurchargeListByRedis("tableGuid")).thenReturn(surchargeLinkDTOS);

        when(mockMenuItemService.queryGuestCount()).thenReturn(0);

        // Run the test
        final CalculateOrderRespDTO result = calculateServiceImplUnderTest.calculate(calculateDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockUserMemberSessionUtils).addUserMemberSession(
                new UserMemberSessionDTO("storeGuid", "openId", "userGuid", false, "nickName", "memberInfoGuid",
                        "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false, "volumeCode", false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid"));
        verify(mockRedisUtils).delete("key");

        // Confirm MenuItemService.calculateMemberDiscount(...).
        final ActivityRuleDescDTO activityRuleDescDTO = new ActivityRuleDescDTO();
        activityRuleDescDTO.setActivityGuid("activityGuid");
        activityRuleDescDTO.setActivityType(0);
        activityRuleDescDTO.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO.setRelationRule(0);
        final ActivityRuleDescDTO activityRuleDescDTO1 = new ActivityRuleDescDTO();
        activityRuleDescDTO1.setActivityGuid("activityGuid");
        activityRuleDescDTO1.setActivityType(0);
        activityRuleDescDTO1.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO1.setRelationRule(0);
        final ItemMarketingActivityDTO itemMarketingActivityDTO = new ItemMarketingActivityDTO();
        itemMarketingActivityDTO.setSpecialsType(0);
        itemMarketingActivityDTO.setSpecialsNumber(new BigDecimal("0.00"));
        itemMarketingActivityDTO.setLimitNumber(0);
        final ShopCartRespDTO shopCartRespDTO = new ShopCartRespDTO(Arrays.asList(
                new ItemInfoDTO("itemGuid", "parentGuid", "typeGuid", "typeName", 0, new BigDecimal("0.00"), false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "showUnit", new BigDecimal("0.00"),
                        "showMemberRule", "name", "pinyin", "nameAbbr", "description", "englishBrief",
                        "englishIngredientsDesc", "pictureUrl", "bigPictureUrl", "detailBigPictureUrl", 0, 0, false, 0,
                        new BigDecimal("0.00"), Arrays.asList(new ItemInfoTagDTO("id", "name")), 0, 0, 0, Arrays.asList(
                        new ItemInfoSkuDTO("skuGuid", "parentGuid", "name", "unit", "code", new BigDecimal("0.00"),
                                false, new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                                new BigDecimal("0.00"), 0, 0, 0, 0, new BigDecimal("0.00"), Arrays.asList("value"),
                                Arrays.asList("value"), Arrays.asList(activityRuleDescDTO))), Arrays.asList(
                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name", new BigDecimal("0.00"), 0)),
                                0)), Arrays.asList(new ItemInfoSubgroupDTO("subgroupGuid", "name", 0, 0, false,
                        Arrays.asList(
                                new ItemInfoSubSkuDTO("itemGuid", "itemName", "code", "itemTypeGuid", "itemTypeName", 0,
                                        "unit", "skuGuid", "skuName", "pictureUrl", new BigDecimal("0.00"), new BigDecimal("0.00"),
                                        new BigDecimal("0.00"), 0, 0, Arrays.asList(
                                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name",
                                                        new BigDecimal("0.00"), 0)), 0)), 0, new BigDecimal("0.00"),
                                        0)))), new BigDecimal("0.00"), 0, false, Arrays.asList("value"),
                        Arrays.asList("value"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), Arrays.asList(activityRuleDescDTO1), Arrays.asList(0),
                        itemMarketingActivityDTO, 0, new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), null, null)), "areaName", "tableCode", false, new BigDecimal("0.00"), false,
                new BigDecimal("0.00"), 0, new BigDecimal("0.00"), new BigDecimal("0.00"));
        final ActivityRuleDescDTO activityRuleDescDTO2 = new ActivityRuleDescDTO();
        activityRuleDescDTO2.setActivityGuid("activityGuid");
        activityRuleDescDTO2.setActivityType(0);
        activityRuleDescDTO2.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO2.setRelationRule(0);
        final ActivityRuleDescDTO activityRuleDescDTO3 = new ActivityRuleDescDTO();
        activityRuleDescDTO3.setActivityGuid("activityGuid");
        activityRuleDescDTO3.setActivityType(0);
        activityRuleDescDTO3.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO3.setRelationRule(0);
        final ItemMarketingActivityDTO itemMarketingActivityDTO1 = new ItemMarketingActivityDTO();
        itemMarketingActivityDTO1.setSpecialsType(0);
        itemMarketingActivityDTO1.setSpecialsNumber(new BigDecimal("0.00"));
        itemMarketingActivityDTO1.setLimitNumber(0);
        final List<ShopCartItemReqDTO> cartItemList = Arrays.asList(new ShopCartItemReqDTO(
                new ItemInfoDTO("itemGuid", "parentGuid", "typeGuid", "typeName", 0, new BigDecimal("0.00"), false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "showUnit", new BigDecimal("0.00"),
                        "showMemberRule", "name", "pinyin", "nameAbbr", "description", "englishBrief",
                        "englishIngredientsDesc", "pictureUrl", "bigPictureUrl", "detailBigPictureUrl", 0, 0, false, 0,
                        new BigDecimal("0.00"), Arrays.asList(new ItemInfoTagDTO("id", "name")), 0, 0, 0, Arrays.asList(
                        new ItemInfoSkuDTO("skuGuid", "parentGuid", "name", "unit", "code", new BigDecimal("0.00"),
                                false, new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                                new BigDecimal("0.00"), 0, 0, 0, 0, new BigDecimal("0.00"), Arrays.asList("value"),
                                Arrays.asList("value"), Arrays.asList(activityRuleDescDTO2))), Arrays.asList(
                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name", new BigDecimal("0.00"), 0)),
                                0)), Arrays.asList(new ItemInfoSubgroupDTO("subgroupGuid", "name", 0, 0, false,
                        Arrays.asList(
                                new ItemInfoSubSkuDTO("itemGuid", "itemName", "code", "itemTypeGuid", "itemTypeName", 0,
                                        "unit", "skuGuid", "skuName", "pictureUrl", new BigDecimal("0.00"), new BigDecimal("0.00"),
                                        new BigDecimal("0.00"), 0, 0, Arrays.asList(
                                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name",
                                                        new BigDecimal("0.00"), 0)), 0)), 0, new BigDecimal("0.00"),
                                        0)))), new BigDecimal("0.00"), 0, false, Arrays.asList("value"),
                        Arrays.asList("value"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), Arrays.asList(activityRuleDescDTO3), Arrays.asList(0),
                        itemMarketingActivityDTO1, 0, new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), null, null), false, new BigDecimal("0.00"), "openid", "nickName", "headImgUrl",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        verify(mockMenuItemService).calculateMemberDiscount(shopCartRespDTO, cartItemList);

        // Confirm DiscountChain.doDiscount(...).
        final DiscountContext context = new DiscountContext();
        final CalculateOrderDTO calculateOrderDTO = new CalculateOrderDTO();
        calculateOrderDTO.setDeviceType(0);
        calculateOrderDTO.setUseMemberDiscountFlag(false);
        calculateOrderDTO.setMemberIntegralStore(false);
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setGuid("c73de291-567a-48a9-b46c-8f0a00b5216e");
        dineInItemDTO2.setSkuGuid("skuGuid");
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO2.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setMinPriceType(0);
        calculateOrderDTO.setDineInItemList(Arrays.asList(dineInItemDTO2));
        calculateOrderDTO.setIsFirst(false);
        calculateOrderDTO.setIsReplace(false);
        calculateOrderDTO.setOrderGuid("orderGuid");
        context.setCalculateOrderDTO(calculateOrderDTO);
        final CalculateOrderRespDTO calculateOrderRespDTO = new CalculateOrderRespDTO();
        calculateOrderRespDTO.setOrderSurplusFee(new BigDecimal("0.00"));
        calculateOrderRespDTO.setOrderSurplusFeeBySkipSpecials(new BigDecimal("0.00"));
        calculateOrderRespDTO.setOrderFee(new BigDecimal("0.00"));
        final ActivitySelectDTO activitySelectDTO3 = new ActivitySelectDTO();
        calculateOrderRespDTO.setActivitySelectList(Arrays.asList(activitySelectDTO3));
        final MarketingActivityInfoRespDTO marketingActivityInfoRespDTO1 = new MarketingActivityInfoRespDTO();
        calculateOrderRespDTO.setActivityInfoList(Arrays.asList(marketingActivityInfoRespDTO1));
        context.setCalculateOrderRespDTO(calculateOrderRespDTO);
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setGuid("c73de291-567a-48a9-b46c-8f0a00b5216e");
        dineInItemDTO3.setSkuGuid("skuGuid");
        dineInItemDTO3.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO3.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO3.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setMinPriceType(0);
        context.setAllItems(Arrays.asList(dineInItemDTO3));
        final DiscountRuleBO discountRuleBO = new DiscountRuleBO();
        discountRuleBO.setVolumeGuid("volumeGuid");
        discountRuleBO.setVolumeCodeType(0);
        discountRuleBO.setHasMemberPrice(false);
        discountRuleBO.setHasMemberDiscount(false);
        discountRuleBO.setMemberDiscount(new BigDecimal("0.00"));
        context.setDiscountRuleBO(discountRuleBO);
        context.setDiscountTypeMap(new HashMap<>());
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        context.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        context.setDineInItemDTOMap(new HashMap<>());
        final UserMemberSessionDTO userMemberSession = new UserMemberSessionDTO();
        userMemberSession.setIsLogin(false);
        context.setUserMemberSession(userMemberSession);
        context.setVolumeGuid("volumeGuid");
        context.setVolumeCodeType(0);
        context.setIntegralStore(false);
        context.setRejectDiscount(false);
        context.setHasMember(false);
        context.setUseMemberPriceFlag(false);
        context.setUseMemberDiscountFlag(false);
        context.setNeedFullMarketActivityList(false);
        context.setNeedLimitSpecialsMarketActivityList(false);
        context.setIsFirst(false);
        context.setIsReplace(false);
        context.setOrderGuid("orderGuid");
        context.setMemberConsumptionGuid("memberConsumptionGuid");
        verify(mockDiscountChain).doDiscount(context);
        verify(mockRedisUtils).setEx("key", "value", 30L, TimeUnit.MINUTES);

        // Confirm TradeClientService.updateMemberConsumptionGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setCheckoutStaffGuid("checkoutStaffGuid");
        orderDTO.setCheckoutStaffName("checkoutStaffName");
        orderDTO.setOrderGuid("orderGuid");
        orderDTO.setBillGuid("billGuid");
        orderDTO.setMemberConsumptionGuid("memberConsumptionGuid");
        verify(mockTradeClientService).updateMemberConsumptionGuid(orderDTO);
    }

    @Test
    public void testCalculate_WxStoreTradeOrderServiceReturnsNoItems() {
        // Setup
        final CalculateOrderDTO calculateDTO = new CalculateOrderDTO();
        calculateDTO.setDeviceType(0);
        calculateDTO.setUseMemberDiscountFlag(false);
        calculateDTO.setMemberIntegralStore(false);
        calculateDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        calculateDTO.setVolumeCodes(Arrays.asList("value"));
        final ActivitySelectDTO activitySelectDTO = new ActivitySelectDTO();
        activitySelectDTO.setActivityGuid("activityGuid");
        activitySelectDTO.setActivityType(0);
        calculateDTO.setActivitySelectList(Arrays.asList(activitySelectDTO));
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("c73de291-567a-48a9-b46c-8f0a00b5216e");
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPriceType(0);
        calculateDTO.setDineInItemList(Arrays.asList(dineInItemDTO));
        calculateDTO.setIsFirst(false);
        calculateDTO.setIsReplace(false);
        calculateDTO.setOrderGuid("orderGuid");
        calculateDTO.setVerify(0);

        final CalculateOrderRespDTO expectedResult = new CalculateOrderRespDTO();
        expectedResult.setUseMemberDiscountFlag(false);
        expectedResult.setHasMemberPrice(false);
        expectedResult.setHasMemberDiscount(false);
        expectedResult.setMemberDiscount(new BigDecimal("0.00"));
        expectedResult.setUnAbleMemberDiscountFlag(false);
        expectedResult.setUnAbleMemberDiscountReason("");
        expectedResult.setOrderSurplusFee(new BigDecimal("0.00"));
        expectedResult.setOrderSurplusFeeBySkipSpecials(new BigDecimal("0.00"));
        expectedResult.setOrderFee(new BigDecimal("0.00"));
        expectedResult.setAppendFee(new BigDecimal("0.00"));
        expectedResult.setDiscountFee(new BigDecimal("0.00"));
        expectedResult.setActuallyPayFee(new BigDecimal("0.00"));
        final ActivitySelectDTO activitySelectDTO1 = new ActivitySelectDTO();
        expectedResult.setActivitySelectList(Arrays.asList(activitySelectDTO1));
        final MarketingActivityInfoRespDTO marketingActivityInfoRespDTO = new MarketingActivityInfoRespDTO();
        expectedResult.setActivityInfoList(Arrays.asList(marketingActivityInfoRespDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        expectedResult.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        final SurchargeLinkDTO surchargeLinkDTO = new SurchargeLinkDTO();
        surchargeLinkDTO.setAmount(new BigDecimal("0.00"));
        surchargeLinkDTO.setType(0);
        expectedResult.setAppendFeeDetailDTOS(Arrays.asList(surchargeLinkDTO));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("c73de291-567a-48a9-b46c-8f0a00b5216e");
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPriceType(0);
        expectedResult.setDineInItemList(Arrays.asList(dineInItemDTO1));

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure HsmTerminalServiceClient.getVolumeByCode(...).
        final ResponseVolumeList responseVolumeList = new ResponseVolumeList();
        responseVolumeList.setVolumeCode("volumeCode");
        responseVolumeList.setVolumeInfoGuid("volumeGuid");
        responseVolumeList.setMemberVolumeGuid("memberVolumeGuid");
        responseVolumeList.setVolumeType(0);
        responseVolumeList.setIsUseAlone(0);
        when(mockTerminalServiceClient.getVolumeByCode("volumeCode")).thenReturn(responseVolumeList);

        // Configure UserMemberSessionUtils.getCardList(...).
        final List<UserMemberCardCacheDTO> userMemberCardCacheDTOS = Arrays.asList(
                new UserMemberCardCacheDTO("cardQRCode", "cardGuid", "memberInfoCardGuid", "systemManagementGuid",
                        "enterpriseMemberInfoSystemGuid", "cardName", "cardLogo", "cardIcon", "cardColour", 0,
                        "cardStartDate", "cardEndDate", 0, "systemManagementCardNum", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "cardIntegral", "cardLevelGuid", "cardLevelName", 0, "levelIcon", 0, 0,
                        false));
        when(mockUserMemberSessionUtils.getCardList("storeGuid", "openId")).thenReturn(userMemberCardCacheDTOS);

        // Configure MenuItemService.getCartItemList(...).
        final ActivityRuleDescDTO activityRuleDescDTO = new ActivityRuleDescDTO();
        activityRuleDescDTO.setActivityGuid("activityGuid");
        activityRuleDescDTO.setActivityType(0);
        activityRuleDescDTO.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO.setRelationRule(0);
        final ActivityRuleDescDTO activityRuleDescDTO1 = new ActivityRuleDescDTO();
        activityRuleDescDTO1.setActivityGuid("activityGuid");
        activityRuleDescDTO1.setActivityType(0);
        activityRuleDescDTO1.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO1.setRelationRule(0);
        final ItemMarketingActivityDTO itemMarketingActivityDTO = new ItemMarketingActivityDTO();
        itemMarketingActivityDTO.setSpecialsType(0);
        itemMarketingActivityDTO.setSpecialsNumber(new BigDecimal("0.00"));
        itemMarketingActivityDTO.setLimitNumber(0);
        final List<ShopCartItemReqDTO> shopCartItemReqDTOS = Arrays.asList(new ShopCartItemReqDTO(
                new ItemInfoDTO("itemGuid", "parentGuid", "typeGuid", "typeName", 0, new BigDecimal("0.00"), false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "showUnit", new BigDecimal("0.00"),
                        "showMemberRule", "name", "pinyin", "nameAbbr", "description", "englishBrief",
                        "englishIngredientsDesc", "pictureUrl", "bigPictureUrl", "detailBigPictureUrl", 0, 0, false, 0,
                        new BigDecimal("0.00"), Arrays.asList(new ItemInfoTagDTO("id", "name")), 0, 0, 0, Arrays.asList(
                        new ItemInfoSkuDTO("skuGuid", "parentGuid", "name", "unit", "code", new BigDecimal("0.00"),
                                false, new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                                new BigDecimal("0.00"), 0, 0, 0, 0, new BigDecimal("0.00"), Arrays.asList("value"),
                                Arrays.asList("value"), Arrays.asList(activityRuleDescDTO))), Arrays.asList(
                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name", new BigDecimal("0.00"), 0)),
                                0)), Arrays.asList(new ItemInfoSubgroupDTO("subgroupGuid", "name", 0, 0, false,
                        Arrays.asList(
                                new ItemInfoSubSkuDTO("itemGuid", "itemName", "code", "itemTypeGuid", "itemTypeName", 0,
                                        "unit", "skuGuid", "skuName", "pictureUrl", new BigDecimal("0.00"), new BigDecimal("0.00"),
                                        new BigDecimal("0.00"), 0, 0, Arrays.asList(
                                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name",
                                                        new BigDecimal("0.00"), 0)), 0)), 0, new BigDecimal("0.00"),
                                        0)))), new BigDecimal("0.00"), 0, false, Arrays.asList("value"),
                        Arrays.asList("value"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), Arrays.asList(activityRuleDescDTO1), Arrays.asList(0),
                        itemMarketingActivityDTO, 0, new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), null, null), false, new BigDecimal("0.00"), "openid", "nickName", "headImgUrl",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        when(mockMenuItemService.getCartItemList()).thenReturn(shopCartItemReqDTOS);

        // Configure MenuItemService.itemPrice(...).
        final PricePairDTO pricePairDTO = new PricePairDTO(new BigDecimal("0.00"), new BigDecimal("0.00"));
        final ActivityRuleDescDTO activityRuleDescDTO2 = new ActivityRuleDescDTO();
        activityRuleDescDTO2.setActivityGuid("activityGuid");
        activityRuleDescDTO2.setActivityType(0);
        activityRuleDescDTO2.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO2.setRelationRule(0);
        final ActivityRuleDescDTO activityRuleDescDTO3 = new ActivityRuleDescDTO();
        activityRuleDescDTO3.setActivityGuid("activityGuid");
        activityRuleDescDTO3.setActivityType(0);
        activityRuleDescDTO3.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO3.setRelationRule(0);
        final ItemMarketingActivityDTO itemMarketingActivityDTO1 = new ItemMarketingActivityDTO();
        itemMarketingActivityDTO1.setSpecialsType(0);
        itemMarketingActivityDTO1.setSpecialsNumber(new BigDecimal("0.00"));
        itemMarketingActivityDTO1.setLimitNumber(0);
        final ItemInfoDTO itemInfoDTO = new ItemInfoDTO("itemGuid", "parentGuid", "typeGuid", "typeName", 0,
                new BigDecimal("0.00"), false, new BigDecimal("0.00"), new BigDecimal("0.00"), "showUnit",
                new BigDecimal("0.00"), "showMemberRule", "name", "pinyin", "nameAbbr", "description", "englishBrief",
                "englishIngredientsDesc", "pictureUrl", "bigPictureUrl", "detailBigPictureUrl", 0, 0, false, 0,
                new BigDecimal("0.00"), Arrays.asList(new ItemInfoTagDTO("id", "name")), 0, 0, 0, Arrays.asList(
                new ItemInfoSkuDTO("skuGuid", "parentGuid", "name", "unit", "code", new BigDecimal("0.00"), false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        0, 0, 0, 0, new BigDecimal("0.00"), Arrays.asList("value"), Arrays.asList("value"),
                        Arrays.asList(activityRuleDescDTO2))), Arrays.asList(
                new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                        new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name", new BigDecimal("0.00"), 0)), 0)),
                Arrays.asList(new ItemInfoSubgroupDTO("subgroupGuid", "name", 0, 0, false, Arrays.asList(
                        new ItemInfoSubSkuDTO("itemGuid", "itemName", "code", "itemTypeGuid", "itemTypeName", 0, "unit",
                                "skuGuid", "skuName", "pictureUrl", new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), 0,
                                0, Arrays.asList(new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name", new BigDecimal("0.00"), 0)),
                                0)), 0, new BigDecimal("0.00"), 0)))), new BigDecimal("0.00"), 0, false,
                Arrays.asList("value"), Arrays.asList("value"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), 0,
                new BigDecimal("0.00"), Arrays.asList(activityRuleDescDTO3), Arrays.asList(0),
                itemMarketingActivityDTO1, 0, new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), null, null);
        when(mockMenuItemService.itemPrice(itemInfoDTO)).thenReturn(pricePairDTO);

        // Configure MemberService.queryCardRightsInfo(...).
        final ResponseCardRights responseCardRights = new ResponseCardRights();
        responseCardRights.setRightsGuid("rightsGuid");
        responseCardRights.setHasMemberPrice(0);
        responseCardRights.setHasMemberDiscount(0);
        responseCardRights.setMemberDiscountValue(new BigDecimal("0.00"));
        responseCardRights.setHasBirthdayRights(0);

        // Configure MarketingActivityHelper.queryMemberRights(...).
        final ResponseProductDiscount responseProductDiscount = new ResponseProductDiscount();
        responseProductDiscount.setMemberPrice(false);
        final ResponseDiscount responseDiscount = new ResponseDiscount();
        responseDiscount.setAllProducts(false);
        responseDiscount.setProductDiscounts(Arrays.asList("value"));
        responseDiscount.setProductKeys(Arrays.asList("value"));
        responseProductDiscount.setDiscountRespDTOList(Arrays.asList(responseDiscount));
        when(mockMarketingActivityHelper.queryMemberRights("wxToken")).thenReturn(responseProductDiscount);

        // Configure MarketingActivityHelper.querySelectSpecialsActivityDetailsVO(...).
        final LimitSpecialsActivityDetailsVO limitSpecialsActivityDetailsVO = new LimitSpecialsActivityDetailsVO();
        limitSpecialsActivityDetailsVO.setGuid("3cd72de4-005b-462e-a07a-e8fc23f814ba");
        limitSpecialsActivityDetailsVO.setName("name");
        limitSpecialsActivityDetailsVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        limitSpecialsActivityDetailsVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        limitSpecialsActivityDetailsVO.setIsLimitPeriod(0);
        final ActivitySelectDTO activitySelectDTO2 = new ActivitySelectDTO();
        activitySelectDTO2.setActivityGuid("activityGuid");
        activitySelectDTO2.setActivityType(0);
        final List<ActivitySelectDTO> activitySelectList = Arrays.asList(activitySelectDTO2);
        when(mockMarketingActivityHelper.querySelectSpecialsActivityDetailsVO(activitySelectList))
                .thenReturn(limitSpecialsActivityDetailsVO);

        // Configure HsaBaseClientService.getActivityInfo(...).
        final ResponseClientMarketActivity responseClientMarketActivity = new ResponseClientMarketActivity();
        responseClientMarketActivity.setActivityType(0);
        responseClientMarketActivity.setGuid("0a8056b4-466c-4094-96ea-b7ff560561da");
        responseClientMarketActivity.setActivityTitle("activityTitle");
        responseClientMarketActivity.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        responseClientMarketActivity.setIsShare(0);
        final ResponseModel<ResponseClientMarketActivity> responseClientMarketActivityResponseModel = new ResponseModel<>(
                responseClientMarketActivity);
        when(mockWechatClientService.getActivityInfo("activityGuid"))
                .thenReturn(responseClientMarketActivityResponseModel);

        when(mockWxStoreTradeOrderService.querySurchargeListByRedis("tableGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final CalculateOrderRespDTO result = calculateServiceImplUnderTest.calculate(calculateDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockUserMemberSessionUtils).addUserMemberSession(
                new UserMemberSessionDTO("storeGuid", "openId", "userGuid", false, "nickName", "memberInfoGuid",
                        "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false, "volumeCode", false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid"));
        verify(mockRedisUtils).delete("key");

        // Confirm MenuItemService.calculateMemberDiscount(...).
        final ActivityRuleDescDTO activityRuleDescDTO4 = new ActivityRuleDescDTO();
        activityRuleDescDTO4.setActivityGuid("activityGuid");
        activityRuleDescDTO4.setActivityType(0);
        activityRuleDescDTO4.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO4.setRelationRule(0);
        final ActivityRuleDescDTO activityRuleDescDTO5 = new ActivityRuleDescDTO();
        activityRuleDescDTO5.setActivityGuid("activityGuid");
        activityRuleDescDTO5.setActivityType(0);
        activityRuleDescDTO5.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO5.setRelationRule(0);
        final ItemMarketingActivityDTO itemMarketingActivityDTO2 = new ItemMarketingActivityDTO();
        itemMarketingActivityDTO2.setSpecialsType(0);
        itemMarketingActivityDTO2.setSpecialsNumber(new BigDecimal("0.00"));
        itemMarketingActivityDTO2.setLimitNumber(0);
        final ShopCartRespDTO shopCartRespDTO = new ShopCartRespDTO(Arrays.asList(
                new ItemInfoDTO("itemGuid", "parentGuid", "typeGuid", "typeName", 0, new BigDecimal("0.00"), false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "showUnit", new BigDecimal("0.00"),
                        "showMemberRule", "name", "pinyin", "nameAbbr", "description", "englishBrief",
                        "englishIngredientsDesc", "pictureUrl", "bigPictureUrl", "detailBigPictureUrl", 0, 0, false, 0,
                        new BigDecimal("0.00"), Arrays.asList(new ItemInfoTagDTO("id", "name")), 0, 0, 0, Arrays.asList(
                        new ItemInfoSkuDTO("skuGuid", "parentGuid", "name", "unit", "code", new BigDecimal("0.00"),
                                false, new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                                new BigDecimal("0.00"), 0, 0, 0, 0, new BigDecimal("0.00"), Arrays.asList("value"),
                                Arrays.asList("value"), Arrays.asList(activityRuleDescDTO4))), Arrays.asList(
                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name", new BigDecimal("0.00"), 0)),
                                0)), Arrays.asList(new ItemInfoSubgroupDTO("subgroupGuid", "name", 0, 0, false,
                        Arrays.asList(
                                new ItemInfoSubSkuDTO("itemGuid", "itemName", "code", "itemTypeGuid", "itemTypeName", 0,
                                        "unit", "skuGuid", "skuName", "pictureUrl", new BigDecimal("0.00"), new BigDecimal("0.00"),
                                        new BigDecimal("0.00"), 0, 0, Arrays.asList(
                                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name",
                                                        new BigDecimal("0.00"), 0)), 0)), 0, new BigDecimal("0.00"),
                                        0)))), new BigDecimal("0.00"), 0, false, Arrays.asList("value"),
                        Arrays.asList("value"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), Arrays.asList(activityRuleDescDTO5), Arrays.asList(0),
                        itemMarketingActivityDTO2, 0, new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), null, null)), "areaName", "tableCode", false, new BigDecimal("0.00"), false,
                new BigDecimal("0.00"), 0, new BigDecimal("0.00"), new BigDecimal("0.00"));
        final ActivityRuleDescDTO activityRuleDescDTO6 = new ActivityRuleDescDTO();
        activityRuleDescDTO6.setActivityGuid("activityGuid");
        activityRuleDescDTO6.setActivityType(0);
        activityRuleDescDTO6.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO6.setRelationRule(0);
        final ActivityRuleDescDTO activityRuleDescDTO7 = new ActivityRuleDescDTO();
        activityRuleDescDTO7.setActivityGuid("activityGuid");
        activityRuleDescDTO7.setActivityType(0);
        activityRuleDescDTO7.setActivityRuleDesc("activityRuleDesc");
        activityRuleDescDTO7.setRelationRule(0);
        final ItemMarketingActivityDTO itemMarketingActivityDTO3 = new ItemMarketingActivityDTO();
        itemMarketingActivityDTO3.setSpecialsType(0);
        itemMarketingActivityDTO3.setSpecialsNumber(new BigDecimal("0.00"));
        itemMarketingActivityDTO3.setLimitNumber(0);
        final List<ShopCartItemReqDTO> cartItemList = Arrays.asList(new ShopCartItemReqDTO(
                new ItemInfoDTO("itemGuid", "parentGuid", "typeGuid", "typeName", 0, new BigDecimal("0.00"), false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "showUnit", new BigDecimal("0.00"),
                        "showMemberRule", "name", "pinyin", "nameAbbr", "description", "englishBrief",
                        "englishIngredientsDesc", "pictureUrl", "bigPictureUrl", "detailBigPictureUrl", 0, 0, false, 0,
                        new BigDecimal("0.00"), Arrays.asList(new ItemInfoTagDTO("id", "name")), 0, 0, 0, Arrays.asList(
                        new ItemInfoSkuDTO("skuGuid", "parentGuid", "name", "unit", "code", new BigDecimal("0.00"),
                                false, new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                                new BigDecimal("0.00"), 0, 0, 0, 0, new BigDecimal("0.00"), Arrays.asList("value"),
                                Arrays.asList("value"), Arrays.asList(activityRuleDescDTO6))), Arrays.asList(
                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name", new BigDecimal("0.00"), 0)),
                                0)), Arrays.asList(new ItemInfoSubgroupDTO("subgroupGuid", "name", 0, 0, false,
                        Arrays.asList(
                                new ItemInfoSubSkuDTO("itemGuid", "itemName", "code", "itemTypeGuid", "itemTypeName", 0,
                                        "unit", "skuGuid", "skuName", "pictureUrl", new BigDecimal("0.00"), new BigDecimal("0.00"),
                                        new BigDecimal("0.00"), 0, 0, Arrays.asList(
                                        new ItemInfoAttrGroupDTO("attrGroupGuid", "name", 0, 0, Arrays.asList(
                                                new ItemInfoAttrDTO("attrGroupGuid", "attrGuid", "name",
                                                        new BigDecimal("0.00"), 0)), 0)), 0, new BigDecimal("0.00"),
                                        0)))), new BigDecimal("0.00"), 0, false, Arrays.asList("value"),
                        Arrays.asList("value"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), Arrays.asList(activityRuleDescDTO7), Arrays.asList(0),
                        itemMarketingActivityDTO3, 0, new BigDecimal("0.00"), new BigDecimal("0.00"),
                        new BigDecimal("0.00"), null, null), false, new BigDecimal("0.00"), "openid", "nickName", "headImgUrl",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        verify(mockMenuItemService).calculateMemberDiscount(shopCartRespDTO, cartItemList);

        // Confirm DiscountChain.doDiscount(...).
        final DiscountContext context = new DiscountContext();
        final CalculateOrderDTO calculateOrderDTO = new CalculateOrderDTO();
        calculateOrderDTO.setDeviceType(0);
        calculateOrderDTO.setUseMemberDiscountFlag(false);
        calculateOrderDTO.setMemberIntegralStore(false);
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setGuid("c73de291-567a-48a9-b46c-8f0a00b5216e");
        dineInItemDTO2.setSkuGuid("skuGuid");
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO2.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setMinPriceType(0);
        calculateOrderDTO.setDineInItemList(Arrays.asList(dineInItemDTO2));
        calculateOrderDTO.setIsFirst(false);
        calculateOrderDTO.setIsReplace(false);
        calculateOrderDTO.setOrderGuid("orderGuid");
        context.setCalculateOrderDTO(calculateOrderDTO);
        final CalculateOrderRespDTO calculateOrderRespDTO = new CalculateOrderRespDTO();
        calculateOrderRespDTO.setOrderSurplusFee(new BigDecimal("0.00"));
        calculateOrderRespDTO.setOrderSurplusFeeBySkipSpecials(new BigDecimal("0.00"));
        calculateOrderRespDTO.setOrderFee(new BigDecimal("0.00"));
        final ActivitySelectDTO activitySelectDTO3 = new ActivitySelectDTO();
        calculateOrderRespDTO.setActivitySelectList(Arrays.asList(activitySelectDTO3));
        final MarketingActivityInfoRespDTO marketingActivityInfoRespDTO1 = new MarketingActivityInfoRespDTO();
        calculateOrderRespDTO.setActivityInfoList(Arrays.asList(marketingActivityInfoRespDTO1));
        context.setCalculateOrderRespDTO(calculateOrderRespDTO);
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setGuid("c73de291-567a-48a9-b46c-8f0a00b5216e");
        dineInItemDTO3.setSkuGuid("skuGuid");
        dineInItemDTO3.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO3.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO3.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setMinPriceType(0);
        context.setAllItems(Arrays.asList(dineInItemDTO3));
        final DiscountRuleBO discountRuleBO = new DiscountRuleBO();
        discountRuleBO.setVolumeGuid("volumeGuid");
        discountRuleBO.setVolumeCodeType(0);
        discountRuleBO.setHasMemberPrice(false);
        discountRuleBO.setHasMemberDiscount(false);
        discountRuleBO.setMemberDiscount(new BigDecimal("0.00"));
        context.setDiscountRuleBO(discountRuleBO);
        context.setDiscountTypeMap(new HashMap<>());
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        context.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        context.setDineInItemDTOMap(new HashMap<>());
        final UserMemberSessionDTO userMemberSession = new UserMemberSessionDTO();
        userMemberSession.setIsLogin(false);
        context.setUserMemberSession(userMemberSession);
        context.setVolumeGuid("volumeGuid");
        context.setVolumeCodeType(0);
        context.setIntegralStore(false);
        context.setRejectDiscount(false);
        context.setHasMember(false);
        context.setUseMemberPriceFlag(false);
        context.setUseMemberDiscountFlag(false);
        context.setNeedFullMarketActivityList(false);
        context.setNeedLimitSpecialsMarketActivityList(false);
        context.setIsFirst(false);
        context.setIsReplace(false);
        context.setOrderGuid("orderGuid");
        context.setMemberConsumptionGuid("memberConsumptionGuid");
        verify(mockDiscountChain).doDiscount(context);
        verify(mockRedisUtils).setEx("key", "value", 30L, TimeUnit.MINUTES);

        // Confirm TradeClientService.updateMemberConsumptionGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setCheckoutStaffGuid("checkoutStaffGuid");
        orderDTO.setCheckoutStaffName("checkoutStaffName");
        orderDTO.setOrderGuid("orderGuid");
        orderDTO.setBillGuid("billGuid");
        orderDTO.setMemberConsumptionGuid("memberConsumptionGuid");
        verify(mockTradeClientService).updateMemberConsumptionGuid(orderDTO);
    }
}
