package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import org.junit.Before;
import org.junit.Test;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;

public class ReserveAreaReqDTOTest {

    private ReserveAreaReqDTO reserveAreaReqDTOUnderTest;

    @Before
    public void setUp() throws Exception {
        reserveAreaReqDTOUnderTest = new ReserveAreaReqDTO(LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid");
    }

    @Test
    public void testAccessors() {
        ReserveAreaReqDTO reqDTO = new ReserveAreaReqDTO()
                .setCurrentTime(LocalDateTime.now())
                .setStoreGuid("123");
    }

    @Test
    public void testCurrentTimeGetterAndSetter() {
        final LocalDateTime currentTime = LocalDateTime.of(2020, 1, 1, 0, 0, 0);
        reserveAreaReqDTOUnderTest.setCurrentTime(currentTime);
        assertThat(reserveAreaReqDTOUnderTest.getCurrentTime()).isEqualTo(currentTime);
    }

    @Test
    public void testStoreGuidGetterAndSetter() {
        final String storeGuid = "storeGuid";
        reserveAreaReqDTOUnderTest.setStoreGuid(storeGuid);
        assertThat(reserveAreaReqDTOUnderTest.getStoreGuid()).isEqualTo(storeGuid);
    }

    @Test
    public void testEquals() throws Exception {
        assertThat(reserveAreaReqDTOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() throws Exception {
        assertThat(reserveAreaReqDTOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() throws Exception {
        assertThat(reserveAreaReqDTOUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() throws Exception {
        assertThat(reserveAreaReqDTOUnderTest.toString()).isEqualTo("result");
    }
}
