package com.holderzone.holder.saas.aggregation.weixin;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.util.JsonFileUtil;
import com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList;
import com.holderzone.saas.store.dto.common.CommonConstant;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.UnsupportedEncodingException;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


/**
 * 小程序优惠券列表
 */
@Slf4j
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@RunWith(MockitoJUnitRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@SpringBootTest(classes = HolderSaasAggregationWeixinApplication.class)
public class AppletCouponControllerTest {

    public static final String WX_TOKEN = "7166613249876557824";

    public static final String APPLET_QUERY_COUPON = "/deal/member/volume_list";

    public static final String RESPONSE = "response:";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    /**
     * 第一次进入确认订单页面
     * 查询优惠券列表
     */
    @Test
    public void firstQueryCouponList() throws UnsupportedEncodingException {
        WxPortalReqDTO firstReqDTO = JSON.parseObject(JsonFileUtil.read("/deal/first_query_coupon.json"), WxPortalReqDTO.class);
        String firstReqJson = JSON.toJSONString(firstReqDTO);
        MvcResult mvcResult = null;
        try {
            mvcResult = mockMvc.perform(post(APPLET_QUERY_COUPON)
                    .header(CommonConstant.WX_SESSION_TOKEN, WX_TOKEN)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(firstReqJson))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String firstQueryContent = mvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + firstQueryContent);
        Assert.notBlank(firstQueryContent, "第一次进入确认订单页面查询优惠券列表返回为空");
        Result result = JacksonUtils.toObject(Result.class, firstQueryContent);
        Assert.equals(result.getCode(), 0, "第一次进入确认订单页面查询优惠券列表返回失败");
        List<ResponseVolumeList> firstQueryCouponList = (List<ResponseVolumeList>) result.getTData();
        Assert.isTrue(CollectionUtils.isEmpty(firstQueryCouponList), "第一次进入确认订单页面查询优惠券列表返回为空");
        for (ResponseVolumeList memberVolumeItemDTO : firstQueryCouponList) {
            Assert.notNull(memberVolumeItemDTO, "第一次进入确认订单页面查询优惠券列表返回为null");
            Assert.notNull(memberVolumeItemDTO.getIsSelected(), "是否选中为空");
            Assert.notNull(memberVolumeItemDTO.getMemberVolumeGuid(), "会员优惠券guid为空");
            Assert.notNull(memberVolumeItemDTO.isUseAlone(), "是否仅原价购买时可用为空");
            Assert.notNull(memberVolumeItemDTO.getUseThreshold(), "使用门槛满限制为空");
            Assert.notNull(memberVolumeItemDTO.isUseable(), "是否可用为空");
            Assert.notNull(memberVolumeItemDTO.getVolumeCode(), "优惠券码为空");
            Assert.notNull(memberVolumeItemDTO.getVolumeEndDate(), "优惠券有效期为空");
            Assert.notNull(memberVolumeItemDTO.getVolumeInfoGuid(), "券guid为空");
            Assert.notNull(memberVolumeItemDTO.getVolumeInfoName(), "优惠券名为空");
            Assert.notNull(memberVolumeItemDTO.getVolumeMoney(), "优惠券面额为空");
            Assert.notNull(memberVolumeItemDTO.getVolumeRuleDesc(), "券规则描述为空");
            Assert.notNull(memberVolumeItemDTO.getVolumeStartDate(), "优惠券生效日期为空");
            Assert.notNull(memberVolumeItemDTO.getVolumeState(), "优惠券状态为空");
            Assert.notNull(memberVolumeItemDTO.getVolumeType(), "券类型为空");
        }
    }


    /**
     * 选中一些券
     * 查询优惠券列表
     */
    @Test
    public void selectQueryCouponList() throws UnsupportedEncodingException {
        WxPortalReqDTO firstReqDTO = JSON.parseObject(JsonFileUtil.read("/deal/select_query_coupon.json"), WxPortalReqDTO.class);
        String selectReqJson = JSON.toJSONString(firstReqDTO);
        MvcResult mvcResult = null;
        try {
            mvcResult = mockMvc.perform(post(APPLET_QUERY_COUPON)
                    .header(CommonConstant.WX_SESSION_TOKEN, WX_TOKEN)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(selectReqJson))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String selectQueryContent = mvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + selectQueryContent);
        Assert.notBlank(selectQueryContent, "选中一些券查询优惠券列表返回为空");
        Result result = JacksonUtils.toObject(Result.class, selectQueryContent);
        Assert.equals(result.getCode(), 0, "选中一些券查询优惠券列表返回失败");
        List<ResponseVolumeList> selectQueryCouponList = (List<ResponseVolumeList>) result.getTData();
        Assert.isTrue(CollectionUtils.isEmpty(selectQueryCouponList), "选中一些券查询优惠券列表返回为空");
        for (ResponseVolumeList selectCouponItem : selectQueryCouponList) {
            Assert.notNull(selectCouponItem, "选中一些券查询优惠券列表返回为null");
            Assert.notNull(selectCouponItem.getIsSelected(), "select是否选中为空");
            Assert.notNull(selectCouponItem.getMemberVolumeGuid(), "select会员优惠券guid为空");
            Assert.notNull(selectCouponItem.isUseAlone(), "select是否仅原价购买时可用为空");
            Assert.notNull(selectCouponItem.getUseThreshold(), "select使用门槛满限制为空");
            Assert.notNull(selectCouponItem.isUseable(), "select是否可用为空");
            Assert.notNull(selectCouponItem.getVolumeCode(), "select优惠券码为空");
            Assert.notNull(selectCouponItem.getVolumeEndDate(), "select优惠券有效期为空");
            Assert.notNull(selectCouponItem.getVolumeInfoGuid(), "select券guid为空");
            Assert.notNull(selectCouponItem.getVolumeInfoName(), "select优惠券名为空");
            Assert.notNull(selectCouponItem.getVolumeMoney(), "select优惠券面额为空");
            Assert.notNull(selectCouponItem.getVolumeRuleDesc(), "select券规则描述为空");
            Assert.notNull(selectCouponItem.getVolumeStartDate(), "select优惠券生效日期为空");
            Assert.notNull(selectCouponItem.getVolumeState(), "select优惠券状态为空");
            Assert.notNull(selectCouponItem.getVolumeType(), "select券类型为空");
        }
    }


    /**
     * 取消选中券
     * 查询优惠券列表
     */
    @Test
    public void cancelQueryCouponList() throws UnsupportedEncodingException {
        WxPortalReqDTO firstReqDTO = JSON.parseObject(JsonFileUtil.read("/deal/cancel_select_query_coupon.json"), WxPortalReqDTO.class);
        String cancelSelectReqJson = JSON.toJSONString(firstReqDTO);
        MvcResult mvcResult = null;
        try {
            mvcResult = mockMvc.perform(post(APPLET_QUERY_COUPON)
                    .header(CommonConstant.WX_SESSION_TOKEN, WX_TOKEN)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(cancelSelectReqJson))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String cancelSelectQueryContent = mvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + cancelSelectQueryContent);
        Assert.notBlank(cancelSelectQueryContent, "取消选中券查询优惠券列表返回为空");
        Result result = JacksonUtils.toObject(Result.class, cancelSelectQueryContent);
        Assert.equals(result.getCode(), 0, "取消选中券查询优惠券列表返回失败");
        List<ResponseVolumeList> cancelSelectQueryCouponList = (List<ResponseVolumeList>) result.getTData();
        Assert.isTrue(CollectionUtils.isEmpty(cancelSelectQueryCouponList), "取消选中券查询优惠券列表返回为空");
        for (ResponseVolumeList cancelSelectCouponItem : cancelSelectQueryCouponList) {
            Assert.notNull(cancelSelectCouponItem, "取消选中券查询优惠券列表返回为null");
            Assert.notNull(cancelSelectCouponItem.getIsSelected(), "selectCancel是否选中为空");
            Assert.notNull(cancelSelectCouponItem.getMemberVolumeGuid(), "selectCancel会员优惠券guid为空");
            Assert.notNull(cancelSelectCouponItem.isUseAlone(), "selectCancel是否仅原价购买时可用为空");
            Assert.notNull(cancelSelectCouponItem.getUseThreshold(), "selectCancel使用门槛满限制为空");
            Assert.notNull(cancelSelectCouponItem.isUseable(), "selectCancel是否可用为空");
            Assert.notNull(cancelSelectCouponItem.getVolumeCode(), "selectCancel优惠券码为空");
            Assert.notNull(cancelSelectCouponItem.getVolumeEndDate(), "selectCancel优惠券有效期为空");
            Assert.notNull(cancelSelectCouponItem.getVolumeInfoGuid(), "selectCancel券guid为空");
            Assert.notNull(cancelSelectCouponItem.getVolumeInfoName(), "selectCancel优惠券名为空");
            Assert.notNull(cancelSelectCouponItem.getVolumeMoney(), "selectCancel优惠券面额为空");
            Assert.notNull(cancelSelectCouponItem.getVolumeRuleDesc(), "selectCancel券规则描述为空");
            Assert.notNull(cancelSelectCouponItem.getVolumeStartDate(), "selectCancel优惠券生效日期为空");
            Assert.notNull(cancelSelectCouponItem.getVolumeState(), "selectCancel优惠券状态为空");
            Assert.notNull(cancelSelectCouponItem.getVolumeType(), "selectCancel券类型为空");
        }
    }

}