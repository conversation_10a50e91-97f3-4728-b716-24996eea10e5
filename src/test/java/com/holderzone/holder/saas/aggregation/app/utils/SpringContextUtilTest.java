package com.holderzone.holder.saas.aggregation.app.utils;

import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.ConfigurableApplicationContext;

import java.lang.annotation.Annotation;
import java.util.Map;

import static org.junit.Assert.assertEquals;

public class SpringContextUtilTest {

    private SpringContextUtil springContextUtilUnderTest;

    @Before
    public void setUp() throws Exception {
        springContextUtilUnderTest = SpringContextUtil.getInstance();
    }

    @Test
    public void testGetInstance() {
        // Setup
        // Run the test
        final SpringContextUtil result = SpringContextUtil.getInstance();

        // Verify the results
    }

    @Test
    public void testGetBean1() {
        // Setup
        // Run the test
        final String result = springContextUtilUnderTest.getBean("name");

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testGetBean2() {
        // Setup
        // Run the test
        final String result = springContextUtilUnderTest.getBean(String.class);

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testRegisterBean1() {
        // Setup
        // Run the test
        springContextUtilUnderTest.registerBean("beanName", "obj");

        // Verify the results
    }

    @Test
    public void testRegisterBean2() {
        // Setup
        final BeanDefinition beanDefinition = null;

        // Run the test
        springContextUtilUnderTest.registerBean("beanName", beanDefinition);

        // Verify the results
    }

    @Test
    public void testGetBeanWithAnnotation() {
        // Setup
        // Run the test
        final Map<String, Object> result = springContextUtilUnderTest.getBeanWithAnnotation(Annotation.class);

        // Verify the results
    }

    @Test
    public void testSetCfgContext() {
        final ConfigurableApplicationContext cfgContext = null;
        springContextUtilUnderTest.setCfgContext(cfgContext);
    }
}
