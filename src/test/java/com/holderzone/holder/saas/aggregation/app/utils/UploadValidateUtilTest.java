package com.holderzone.holder.saas.aggregation.app.utils;

import org.junit.Test;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import static org.junit.Assert.assertFalse;

public class UploadValidateUtilTest {

    @Test
    public void testTypeNotValidate() {
        assertFalse(UploadValidateUtil.typeNotValidate("type"));
    }

    @Test
    public void testWhetherImage() {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());

        // Run the test
        final Boolean result = UploadValidateUtil.whetherImage(file);

        // Verify the results
        assertFalse(result);
    }
}
