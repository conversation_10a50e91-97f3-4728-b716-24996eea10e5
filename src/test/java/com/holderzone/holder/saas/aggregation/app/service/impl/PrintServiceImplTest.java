package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.controller.util.JsonFileUtil;
import com.holderzone.holder.saas.aggregation.app.service.feign.PrintClientService;
import com.holderzone.saas.store.dto.print.PrinterDTO;
import com.holderzone.saas.store.dto.print.PrinterInvoiceDTO;
import com.holderzone.saas.store.dto.print.PrinterItemDTO;
import com.holderzone.saas.store.dto.print.cloud.CloudPrinterDTO;
import com.holderzone.saas.store.dto.print.cloud.DeviceUseDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrintServiceImplTest {

    @Mock
    private PrintClientService mockPrintClientService;

    private PrintServiceImpl printServiceImplUnderTest;

    @Before
    public void setUp() {
        printServiceImplUnderTest = new PrintServiceImpl(mockPrintClientService);
    }

    @Test
    public void testQueryCloudPrinter() {
        // Setup
        final CloudPrinterDTO printerDTO = new CloudPrinterDTO();
        printerDTO.setPrinterGuid("7178947474365612032");

        // Configure PrintClientService.getPrinter(...).
        PrinterDTO printer = JacksonUtils.toObject(PrinterDTO.class, JsonFileUtil.read("print/getPrinterResp.json"));
        when(mockPrintClientService.getPrinter(printerDTO)).thenReturn(printer);

        // Run the test
        final CloudPrinterDTO result = printServiceImplUnderTest.queryCloudPrinter(printerDTO);

        // Verify the results
        CloudPrinterDTO cloudPrinterDTO = JacksonUtils.toObject(CloudPrinterDTO.class,
                JsonFileUtil.read("print/queryCloudPrinterResp.json"));
        assertThat(result).isEqualTo(cloudPrinterDTO);
    }

    @Test
    public void testListCloudPrinters() {
        // Setup
        PrinterDTO query = new PrinterDTO();
        query.setPrinterType(3);
        query.setStoreGuid("4897");

        // Configure PrintClientService.listCloudPrinters(...).
        List<PrinterDTO> printerDTOList = JacksonUtils.toObjectList(PrinterDTO.class,
                JsonFileUtil.read("print/listCloudPrintersResp.json"));
        when(mockPrintClientService.listCloudPrinters(query)).thenReturn(printerDTOList);

        // Run the test
        final List<CloudPrinterDTO> result = printServiceImplUnderTest.listCloudPrinters(query);

        // Verify the results
        List<CloudPrinterDTO>  cloudPrinterDTOList = JacksonUtils.toObjectList(CloudPrinterDTO.class,
                JsonFileUtil.read("print/listCloudPrintersFinalResp.json"));
        assertThat(result).isEqualTo(cloudPrinterDTOList);
    }

}
