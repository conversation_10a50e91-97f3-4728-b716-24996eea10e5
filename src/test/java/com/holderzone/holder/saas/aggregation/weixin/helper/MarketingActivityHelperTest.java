package com.holderzone.holder.saas.aggregation.weixin.helper;

import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.MemberMarketingClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.aggregation.weixin.utils.UserMemberSessionUtils;
import com.holderzone.holder.saas.member.terminal.dto.feign.FeignModel;
import com.holderzone.holder.saas.member.wechat.dto.activitie.RequestMarketActivityUse;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseClientMarketActivity;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseMarketActivityUse;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseDiscount;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseProductDiscount;
import com.holderzone.holder.saas.member.wechat.dto.coupon.ResponseVolumeInfo;
import com.holderzone.holder.saas.member.wechat.dto.label.ResponseMemberLabel;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberCardListOwned;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.entry.dto.WxUserInfoDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.marketing.specials.ConsumptionGiftDetailsDTO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityDetailsVO;
import com.holderzone.saas.store.dto.marketing.UniteActivityQO;
import com.holderzone.saas.store.dto.marketing.UniteActivityVO;
import com.holderzone.saas.store.dto.member.pay.RedPacketOrderDTO;
import com.holderzone.saas.store.dto.weixin.deal.ActivitySelectDTO;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberSessionDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MarketingActivityHelperTest {

    @Mock
    private MemberMarketingClientService mockMarketingClientService;
    @Mock
    private HsaBaseClientService mockWechatClientService;
    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private UserMemberSessionUtils mockUserMemberSessionUtils;
    @Mock
    private PriceCalculateHelper mockPriceCalculateHelper;

    @Mock
    private MemberRightHelper memberRightHelper;

    private MarketingActivityHelper marketingActivityHelperUnderTest;

    @Before
    public void setUp() throws Exception {
        marketingActivityHelperUnderTest = new MarketingActivityHelper(mockMarketingClientService,
                mockWechatClientService, mockRedisUtils, mockUserMemberSessionUtils, mockPriceCalculateHelper, memberRightHelper);
    }

    @Test
    public void testQuerySpecialsActivityList() {
        // Setup
        final WxMemberSessionDTO wxMemberSessionDTO = new WxMemberSessionDTO();
        final WxUserInfoDTO wxUserInfoDTO = new WxUserInfoDTO();
        wxUserInfoDTO.setIsLogin(false);
        wxMemberSessionDTO.setWxUserInfoDTO(wxUserInfoDTO);
        wxMemberSessionDTO.setOperSubjectGuid("operSubjectGuid");
        wxMemberSessionDTO.setStoreGuid("storeGuid");
        wxMemberSessionDTO.setMemberInfoGuid("memberInfoGuid");
        wxMemberSessionDTO.setOrderState(0);
        wxMemberSessionDTO.setWxtoken("wxtoken");

        final LimitSpecialsActivityDetailsVO activityDetailsVO = new LimitSpecialsActivityDetailsVO();
        activityDetailsVO.setGuid("0ca0e6e2-707d-4238-a38a-095194c9e256");
        activityDetailsVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO.setGroupType(0);
        final ConsumptionGiftDetailsDTO labelDTO = new ConsumptionGiftDetailsDTO();
        labelDTO.setGuidList(Arrays.asList("value"));
        activityDetailsVO.setLabelDTO(labelDTO);
        final ConsumptionGiftDetailsDTO gradeDTO = new ConsumptionGiftDetailsDTO();
        gradeDTO.setGuidList(Arrays.asList("value"));
        activityDetailsVO.setGradeDTO(gradeDTO);
        final ConsumptionGiftDetailsDTO memberDTO = new ConsumptionGiftDetailsDTO();
        memberDTO.setGuidList(Arrays.asList("value"));
        activityDetailsVO.setMemberDTO(memberDTO);
        final List<LimitSpecialsActivityDetailsVO> expectedResult = Arrays.asList(activityDetailsVO);
        when(mockRedisUtils.get("LimitSpecialsActivity:storeGuid:wxtoken")).thenReturn("[{\"guid\":\"7227520740151525376\",\"name\":\"测试特价\",\"startTime\":\"2024-09-14 00:52:00\",\"endTime\":\"2024-09-15 11:38:00\",\"isLimitPeriod\":0,\"labelGuidList\":[\"7224226159775449088\"],\"relationRule\":1,\"applyBusiness\":1,\"applyBusinessList\":[\"1\"],\"isAllStore\":0,\"storeDTOList\":[{\"storeGuid\":\"2106221850429620006\",\"storeNumber\":\"5796807\",\"storeName\":\"交子大道测试门店\"}],\"groupType\":1,\"itemDTOList\":[{\"commodityId\":\"7238762736992124928\",\"commodityCode\":\"7238762736992124928\",\"commodityName\":\"koko0909\",\"commodityType\":4,\"specialsType\":1,\"specialsNumber\":5.00,\"limitNumber\":2,\"channel\":\"POS\",\"isExist\":1}],\"gmtCreate\":\"2024-08-09T11:47:05\",\"gmtModified\":\"2024-09-14T11:39:11\"}]");

        // Configure MemberMarketingClientService.query(...).
        final FeignModel<UniteActivityVO> uniteActivityVOFeignModel = new FeignModel<>();
        final UniteActivityVO uniteActivityVO = new UniteActivityVO();
        final LimitSpecialsActivityDetailsVO activityDetailsVO1 = new LimitSpecialsActivityDetailsVO();
        activityDetailsVO1.setGuid("0ca0e6e2-707d-4238-a38a-095194c9e256");
        activityDetailsVO1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO1.setGroupType(0);
        final ConsumptionGiftDetailsDTO labelDTO1 = new ConsumptionGiftDetailsDTO();
        labelDTO1.setGuidList(Arrays.asList("value"));
        activityDetailsVO1.setLabelDTO(labelDTO1);
        final ConsumptionGiftDetailsDTO gradeDTO1 = new ConsumptionGiftDetailsDTO();
        gradeDTO1.setGuidList(Arrays.asList("value"));
        activityDetailsVO1.setGradeDTO(gradeDTO1);
        final ConsumptionGiftDetailsDTO memberDTO1 = new ConsumptionGiftDetailsDTO();
        memberDTO1.setGuidList(Arrays.asList("value"));
        activityDetailsVO1.setMemberDTO(memberDTO1);
        uniteActivityVO.setLimitSpecialsList(Arrays.asList(activityDetailsVO1));
        uniteActivityVOFeignModel.setData(uniteActivityVO);
        final UniteActivityQO query = new UniteActivityQO();
        query.setOperSubjectGuid("operSubjectGuid");
        query.setQueryType(0);
        query.setStoreGuid("storeGuid");
        query.setOrderState(0);
        final RedPacketOrderDTO redPacketOrderDTO = new RedPacketOrderDTO();
        query.setOrderNumbers(Arrays.asList(redPacketOrderDTO));
        String feignModel = "{\"code\":200,\"message\":\"成功！\",\"data\":{\"limitSpecialsList\":[{\"guid\":\"7227520740151525376\",\"name\":\"测试特价\",\"startTime\":\"2024-09-14 00:52:00\",\"endTime\":\"2024-09-15 11:38:00\",\"isLimitPeriod\":0,\"labelGuidList\":[\"7224226159775449088\"],\"relationRule\":1,\"applyBusiness\":1,\"applyBusinessList\":[\"1\"],\"isAllStore\":0,\"storeDTOList\":[{\"storeGuid\":\"2106221850429620006\",\"storeNumber\":\"5796807\",\"storeName\":\"交子大道测试门店\"}],\"groupType\":1,\"itemDTOList\":[{\"commodityId\":\"7238762736992124928\",\"commodityCode\":\"7238762736992124928\",\"commodityName\":\"koko0909\",\"commodityType\":4,\"specialsType\":1,\"specialsNumber\":5.00,\"limitNumber\":2,\"channel\":\"POS\",\"isExist\":1}],\"gmtCreate\":\"2024-08-09T11:47:05\",\"gmtModified\":\"2024-09-14T11:39:11\"}],\"redPacketList\":[]}}";
        FeignModel model = JacksonUtils.toObject(FeignModel.class, feignModel);
        when(mockMarketingClientService.query(query)).thenReturn(model);

        // Configure HsaBaseClientService.getDefaultCard(...).
        final ResponseMemberCardListOwned responseMemberCardListOwned = new ResponseMemberCardListOwned();
        responseMemberCardListOwned.setCardQRCode("cardQRCode");
        responseMemberCardListOwned.setCardGuid("cardGuid");
        responseMemberCardListOwned.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCardListOwned.setSystemManagementGuid("systemManagementGuid");
        responseMemberCardListOwned.setCardLevelGuid("cardLevelGuid");
        final ResponseModel<ResponseMemberCardListOwned> responseMemberCardListOwnedResponseModel = new ResponseModel<>(
                responseMemberCardListOwned);
        when(mockWechatClientService.getDefaultCard("memberInfoGuid"))
                .thenReturn(responseMemberCardListOwnedResponseModel);

        // Configure HsaBaseClientService.getMemberInfoLabel(...).
        final ResponseMemberLabel label = new ResponseMemberLabel();
        label.setLabelGuid("labelGuid");
        label.setLabelType("labelType");
        label.setLabelName("labelName");
        final ResponseModel<List<ResponseMemberLabel>> listResponseModel = new ResponseModel<>(Arrays.asList(label));
        when(mockWechatClientService.getMemberInfoLabel("memberInfoGuid")).thenReturn(listResponseModel);

        // Run the test
        final List<LimitSpecialsActivityDetailsVO> result = marketingActivityHelperUnderTest.querySpecialsActivityList(
                wxMemberSessionDTO);

        // Verify the results
        assertThat(result).isEqualTo(Lists.newArrayList());
//        verify(mockRedisUtils).setEx("key", "value", 30L, TimeUnit.MINUTES);
    }

    @Test
    public void testQuerySpecialsActivityList_HsaBaseClientServiceGetMemberInfoLabelReturnsNoItems() {
        // Setup
        final WxMemberSessionDTO wxMemberSessionDTO = new WxMemberSessionDTO();
        final WxUserInfoDTO wxUserInfoDTO = new WxUserInfoDTO();
        wxUserInfoDTO.setIsLogin(false);
        wxMemberSessionDTO.setWxUserInfoDTO(wxUserInfoDTO);
        wxMemberSessionDTO.setOperSubjectGuid("operSubjectGuid");
        wxMemberSessionDTO.setStoreGuid("storeGuid");
        wxMemberSessionDTO.setMemberInfoGuid("memberInfoGuid");
        wxMemberSessionDTO.setOrderState(0);
        wxMemberSessionDTO.setWxtoken("wxtoken");

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure MemberMarketingClientService.query(...).
        final FeignModel<UniteActivityVO> uniteActivityVOFeignModel = new FeignModel<>();
        final LimitSpecialsActivityDetailsVO activityDetailsVO = new LimitSpecialsActivityDetailsVO();
        activityDetailsVO.setGuid("0ca0e6e2-707d-4238-a38a-095194c9e256");
        activityDetailsVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO.setGroupType(0);
        final ConsumptionGiftDetailsDTO labelDTO = new ConsumptionGiftDetailsDTO();
        labelDTO.setGuidList(Arrays.asList("value"));
        activityDetailsVO.setLabelDTO(labelDTO);
        final ConsumptionGiftDetailsDTO gradeDTO = new ConsumptionGiftDetailsDTO();
        gradeDTO.setGuidList(Arrays.asList("value"));
        activityDetailsVO.setGradeDTO(gradeDTO);
        final ConsumptionGiftDetailsDTO memberDTO = new ConsumptionGiftDetailsDTO();
        memberDTO.setGuidList(Arrays.asList("value"));
        activityDetailsVO.setMemberDTO(memberDTO);
        String uniteActivityVOJson = "{\"limitSpecialsList\":[{\"guid\":\"7227520740151525376\",\"name\":\"测试特价\",\"startTime\":\"2024-09-14 00:52:00\",\"endTime\":\"2024-09-15 11:38:00\",\"isLimitPeriod\":0,\"labelGuidList\":[\"7224226159775449088\"],\"relationRule\":1,\"applyBusiness\":1,\"applyBusinessList\":[\"1\"],\"isAllStore\":0,\"storeDTOList\":[{\"storeGuid\":\"2106221850429620006\",\"storeNumber\":\"5796807\",\"storeName\":\"交子大道测试门店\"}],\"groupType\":1,\"itemDTOList\":[{\"commodityId\":\"7238762736992124928\",\"commodityCode\":\"7238762736992124928\",\"commodityName\":\"koko0909\",\"commodityType\":4,\"specialsType\":1,\"specialsNumber\":5.00,\"limitNumber\":2,\"channel\":\"POS\",\"isExist\":1}],\"gmtCreate\":\"2024-08-09T11:47:05\",\"gmtModified\":\"2024-09-14T11:39:11\"}],\"redPacketList\":[]}";
        UniteActivityVO uniteActivityVO = JacksonUtils.toObject(UniteActivityVO.class, uniteActivityVOJson);
        uniteActivityVOFeignModel.setData(uniteActivityVO);
        final UniteActivityQO query = new UniteActivityQO();
        query.setOperSubjectGuid("operSubjectGuid");
        query.setQueryType(1);
        query.setStoreGuid("storeGuid");
        query.setOrderState(0);
        query.setOrderNumbers(null);
        when(mockMarketingClientService.query(query)).thenReturn(uniteActivityVOFeignModel);

        // Configure HsaBaseClientService.getDefaultCard(...).
        final ResponseMemberCardListOwned responseMemberCardListOwned = new ResponseMemberCardListOwned();
        responseMemberCardListOwned.setCardQRCode("cardQRCode");
        responseMemberCardListOwned.setCardGuid("cardGuid");
        responseMemberCardListOwned.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCardListOwned.setSystemManagementGuid("systemManagementGuid");
        responseMemberCardListOwned.setCardLevelGuid("cardLevelGuid");
        final ResponseModel<ResponseMemberCardListOwned> responseMemberCardListOwnedResponseModel = new ResponseModel<>(
                responseMemberCardListOwned);
        when(mockWechatClientService.getDefaultCard("memberInfoGuid"))
                .thenReturn(responseMemberCardListOwnedResponseModel);

        // Configure HsaBaseClientService.getMemberInfoLabel(...).
        final ResponseModel<List<ResponseMemberLabel>> listResponseModel = new ResponseModel<>(Collections.emptyList());
        when(mockWechatClientService.getMemberInfoLabel("memberInfoGuid")).thenReturn(listResponseModel);

        // Run the test
        final List<LimitSpecialsActivityDetailsVO> result = marketingActivityHelperUnderTest.querySpecialsActivityList(
                wxMemberSessionDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
        verify(mockRedisUtils).setEx("LimitSpecialsActivity:storeGuid:wxtoken", Collections.emptyList(), 30L, TimeUnit.MINUTES);
    }

    @Test
    public void testQuerySelectSpecialsActivityDetailsVO() {
        // Setup
        final ActivitySelectDTO activitySelectDTO = new ActivitySelectDTO();
        activitySelectDTO.setActivityGuid("activityGuid");
        activitySelectDTO.setActivityType(0);
        final List<ActivitySelectDTO> activitySelectList = Arrays.asList(activitySelectDTO);
        final LimitSpecialsActivityDetailsVO expectedResult = new LimitSpecialsActivityDetailsVO();
        expectedResult.setGuid("0ca0e6e2-707d-4238-a38a-095194c9e256");
        expectedResult.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGroupType(0);
        final ConsumptionGiftDetailsDTO labelDTO = new ConsumptionGiftDetailsDTO();
        labelDTO.setGuidList(Arrays.asList("value"));
        expectedResult.setLabelDTO(labelDTO);
        final ConsumptionGiftDetailsDTO gradeDTO = new ConsumptionGiftDetailsDTO();
        gradeDTO.setGuidList(Arrays.asList("value"));
        expectedResult.setGradeDTO(gradeDTO);
        final ConsumptionGiftDetailsDTO memberDTO = new ConsumptionGiftDetailsDTO();
        memberDTO.setGuidList(Arrays.asList("value"));
        expectedResult.setMemberDTO(memberDTO);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure MemberMarketingClientService.query(...).
        final FeignModel<UniteActivityVO> uniteActivityVOFeignModel = new FeignModel<>();
        final LimitSpecialsActivityDetailsVO activityDetailsVO = new LimitSpecialsActivityDetailsVO();
        activityDetailsVO.setGuid("0ca0e6e2-707d-4238-a38a-095194c9e256");
        activityDetailsVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO.setGroupType(0);
        final ConsumptionGiftDetailsDTO labelDTO1 = new ConsumptionGiftDetailsDTO();
        labelDTO1.setGuidList(Arrays.asList("value"));
        activityDetailsVO.setLabelDTO(labelDTO1);
        final ConsumptionGiftDetailsDTO gradeDTO1 = new ConsumptionGiftDetailsDTO();
        gradeDTO1.setGuidList(Arrays.asList("value"));
        activityDetailsVO.setGradeDTO(gradeDTO1);
        final ConsumptionGiftDetailsDTO memberDTO1 = new ConsumptionGiftDetailsDTO();
        memberDTO1.setGuidList(Arrays.asList("value"));
        activityDetailsVO.setMemberDTO(memberDTO1);
        String uniteActivityVOJson = "{\"limitSpecialsList\":[{\"guid\":\"7231938328499585024\",\"name\":\"111\",\"startTime\":\"2024-08-23 15:59:00\",\"endTime\":\"2024-08-31 15:59:00\",\"isLimitPeriod\":0,\"relationRule\":0,\"applyBusiness\":0,\"applyBusinessList\":[\"0\",\"1\"],\"isAllStore\":1,\"storeDTOList\":[],\"groupType\":0,\"itemDTOList\":[{\"commodityId\":\"7229773146226688000\",\"commodityCode\":\"7229773146226688000\",\"commodityName\":\"连山火锅肉\",\"commodityType\":4,\"specialsType\":1,\"specialsNumber\":8.80,\"limitNumber\":1,\"channel\":\"POS\",\"isExist\":1},{\"commodityId\":\"7229773054614700032\",\"commodityCode\":\"7229773054614700032\",\"commodityName\":\"歌乐山辣子鸡\",\"commodityType\":4,\"specialsType\":2,\"specialsNumber\":10.00,\"limitNumber\":2,\"channel\":\"POS\",\"isExist\":1},{\"commodityId\":\"7224297913952763904\",\"commodityCode\":\"7224297913952763904\",\"commodityName\":\"特价鸡翅\",\"commodityType\":2,\"specialsType\":1,\"specialsNumber\":5.00,\"limitNumber\":3,\"channel\":\"POS\",\"isExist\":1}],\"gmtCreate\":\"2024-08-21T16:21:00\",\"gmtModified\":\"2024-08-21T16:23:24\"},{\"guid\":\"7231471077333925888\",\"name\":\"魅力城-zl\",\"startTime\":\"2024-08-20 09:21:00\",\"endTime\":\"2024-08-31 09:24:00\",\"isLimitPeriod\":0,\"labelGuidList\":[\"6924280395978506240\"],\"relationRule\":1,\"applyBusiness\":0,\"applyBusinessList\":[\"0\",\"1\"],\"isAllStore\":0,\"storeDTOList\":[{\"storeGuid\":\"2011161546088800009\",\"storeNumber\":\"1767251\",\"storeName\":\"粉红色回忆\"},{\"storeGuid\":\"6731814584836947968\",\"storeNumber\":\"1833503\",\"storeName\":\" He's成都魅力城店\"},{\"storeGuid\":\"2106221850429620006\",\"storeNumber\":\"5796807\",\"storeName\":\"交子大道测试门店\"}],\"groupType\":1,\"itemDTOList\":[{\"commodityId\":\"7202146382650540032\",\"commodityCode\":\"7202146382650540032\",\"commodityName\":\"朗姆酒\",\"commodityType\":2,\"specialsType\":1,\"specialsNumber\":5.00,\"limitNumber\":3,\"channel\":\"POS\",\"isExist\":1},{\"commodityId\":\"7091671674348634112\",\"commodityCode\":\"7091671674348634112\",\"commodityName\":\"红烧牛犊\",\"commodityType\":4,\"specialsType\":1,\"specialsNumber\":5.00,\"limitNumber\":2,\"channel\":\"POS\",\"isExist\":1},{\"commodityId\":\"7036619387582808064\",\"commodityCode\":\"7036619387582808064\",\"commodityName\":\"六筒\",\"commodityType\":4,\"specialsType\":2,\"specialsNumber\":10.00,\"limitNumber\":1,\"channel\":\"POS\",\"isExist\":1},{\"commodityId\":\"7036619032388173824\",\"commodityCode\":\"7036619032388173824\",\"commodityName\":\"小火车\",\"commodityType\":4,\"specialsType\":3,\"specialsNumber\":0.01,\"limitNumber\":1,\"channel\":\"POS\",\"isExist\":1},{\"commodityId\":\"7027212385605648384\",\"commodityCode\":\"7027212385605648384\",\"commodityName\":\"九转大肠\",\"commodityType\":4,\"specialsType\":3,\"specialsNumber\":0.00,\"limitNumber\":2,\"channel\":\"POS\",\"isExist\":1},{\"commodityId\":\"7155816577596653568\",\"commodityCode\":\"7155816577596653568\",\"commodityName\":\"兰蓝套餐\",\"commodityType\":1,\"specialsType\":1,\"specialsNumber\":7.00,\"channel\":\"POS\",\"isExist\":1},{\"commodityId\":\"7155816057188384768\",\"commodityCode\":\"7155816057188384768\",\"commodityName\":\"罗拉兰\",\"commodityType\":4,\"specialsType\":2,\"specialsNumber\":4.00,\"limitNumber\":1,\"channel\":\"POS\",\"isExist\":1},{\"commodityId\":\"6789437855056789504\",\"commodityCode\":\"6789437855056789504\",\"commodityName\":\"凉拌橘子\",\"commodityType\":4,\"specialsType\":3,\"specialsNumber\":10.00,\"limitNumber\":2,\"channel\":\"POS\",\"isExist\":1},{\"commodityId\":\"7155815919938174976\",\"commodityCode\":\"7155815919938174976\",\"commodityName\":\"兰那罗\",\"commodityType\":4,\"specialsType\":1,\"specialsNumber\":5.00,\"channel\":\"POS\",\"isExist\":1}],\"gmtCreate\":\"2024-08-20T09:24:19\",\"gmtModified\":\"2024-08-20T09:27:51\"}],\"redPacketList\":[]}";
        UniteActivityVO uniteActivityVO = JacksonUtils.toObject(UniteActivityVO.class, uniteActivityVOJson);
        uniteActivityVOFeignModel.setData(uniteActivityVO);
        final UniteActivityQO query = new UniteActivityQO();
        query.setOperSubjectGuid("operSubjectGuid");
        query.setQueryType(1);
        query.setStoreGuid("storeGuid");
        query.setOrderState(0);
        query.setOrderNumbers(null);
        when(mockMarketingClientService.query(query)).thenReturn(uniteActivityVOFeignModel);

        // Configure HsaBaseClientService.getDefaultCard(...).
        final ResponseMemberCardListOwned responseMemberCardListOwned = new ResponseMemberCardListOwned();
        responseMemberCardListOwned.setCardQRCode("cardQRCode");
        responseMemberCardListOwned.setCardGuid("cardGuid");
        responseMemberCardListOwned.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCardListOwned.setSystemManagementGuid("systemManagementGuid");
        responseMemberCardListOwned.setCardLevelGuid("cardLevelGuid");
        final ResponseModel<ResponseMemberCardListOwned> responseMemberCardListOwnedResponseModel = new ResponseModel<>(
                responseMemberCardListOwned);
        when(mockWechatClientService.getDefaultCard("memberInfoGuid"))
                .thenReturn(responseMemberCardListOwnedResponseModel);

        // Configure HsaBaseClientService.getMemberInfoLabel(...).
        final ResponseMemberLabel label = new ResponseMemberLabel();
        label.setLabelGuid("labelGuid");
        label.setLabelType("labelType");
        label.setLabelName("labelName");
        final ResponseModel<List<ResponseMemberLabel>> listResponseModel = new ResponseModel<>(Arrays.asList(label));
        when(mockWechatClientService.getMemberInfoLabel("memberInfoGuid")).thenReturn(listResponseModel);

        // Run the test
        final LimitSpecialsActivityDetailsVO result = marketingActivityHelperUnderTest.querySelectSpecialsActivityDetailsVO(
                activitySelectList);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockRedisUtils).setEx("key", "value", 30L, TimeUnit.MINUTES);
    }

    @Test
    public void testQuerySelectSpecialsActivityDetailsVO_HsaBaseClientServiceGetMemberInfoLabelReturnsNoItems() {
        // Setup
        final ActivitySelectDTO activitySelectDTO = new ActivitySelectDTO();
        activitySelectDTO.setActivityGuid("activityGuid");
        activitySelectDTO.setActivityType(0);
        final List<ActivitySelectDTO> activitySelectList = Arrays.asList(activitySelectDTO);
        final LimitSpecialsActivityDetailsVO expectedResult = new LimitSpecialsActivityDetailsVO();
        expectedResult.setGuid("0ca0e6e2-707d-4238-a38a-095194c9e256");
        expectedResult.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGroupType(0);
        final ConsumptionGiftDetailsDTO labelDTO = new ConsumptionGiftDetailsDTO();
        labelDTO.setGuidList(Arrays.asList("value"));
        expectedResult.setLabelDTO(labelDTO);
        final ConsumptionGiftDetailsDTO gradeDTO = new ConsumptionGiftDetailsDTO();
        gradeDTO.setGuidList(Arrays.asList("value"));
        expectedResult.setGradeDTO(gradeDTO);
        final ConsumptionGiftDetailsDTO memberDTO = new ConsumptionGiftDetailsDTO();
        memberDTO.setGuidList(Arrays.asList("value"));
        expectedResult.setMemberDTO(memberDTO);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure MemberMarketingClientService.query(...).
        final FeignModel<UniteActivityVO> uniteActivityVOFeignModel = new FeignModel<>();
        final UniteActivityVO uniteActivityVO = new UniteActivityVO();
        final LimitSpecialsActivityDetailsVO activityDetailsVO = new LimitSpecialsActivityDetailsVO();
        activityDetailsVO.setGuid("0ca0e6e2-707d-4238-a38a-095194c9e256");
        activityDetailsVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO.setGroupType(0);
        final ConsumptionGiftDetailsDTO labelDTO1 = new ConsumptionGiftDetailsDTO();
        labelDTO1.setGuidList(Arrays.asList("value"));
        activityDetailsVO.setLabelDTO(labelDTO1);
        final ConsumptionGiftDetailsDTO gradeDTO1 = new ConsumptionGiftDetailsDTO();
        gradeDTO1.setGuidList(Arrays.asList("value"));
        activityDetailsVO.setGradeDTO(gradeDTO1);
        final ConsumptionGiftDetailsDTO memberDTO1 = new ConsumptionGiftDetailsDTO();
        memberDTO1.setGuidList(Arrays.asList("value"));
        activityDetailsVO.setMemberDTO(memberDTO1);
        uniteActivityVO.setLimitSpecialsList(Arrays.asList(activityDetailsVO));
        uniteActivityVOFeignModel.setData(uniteActivityVO);
        final UniteActivityQO query = new UniteActivityQO();
        query.setOperSubjectGuid("operSubjectGuid");
        query.setQueryType(0);
        query.setStoreGuid("storeGuid");
        query.setOrderState(0);
        final RedPacketOrderDTO redPacketOrderDTO = new RedPacketOrderDTO();
        query.setOrderNumbers(Arrays.asList(redPacketOrderDTO));
        when(mockMarketingClientService.query(query)).thenReturn(uniteActivityVOFeignModel);

        // Configure HsaBaseClientService.getDefaultCard(...).
        final ResponseMemberCardListOwned responseMemberCardListOwned = new ResponseMemberCardListOwned();
        responseMemberCardListOwned.setCardQRCode("cardQRCode");
        responseMemberCardListOwned.setCardGuid("cardGuid");
        responseMemberCardListOwned.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCardListOwned.setSystemManagementGuid("systemManagementGuid");
        responseMemberCardListOwned.setCardLevelGuid("cardLevelGuid");
        final ResponseModel<ResponseMemberCardListOwned> responseMemberCardListOwnedResponseModel = new ResponseModel<>(
                responseMemberCardListOwned);
        when(mockWechatClientService.getDefaultCard("memberInfoGuid"))
                .thenReturn(responseMemberCardListOwnedResponseModel);

        // Configure HsaBaseClientService.getMemberInfoLabel(...).
        final ResponseModel<List<ResponseMemberLabel>> listResponseModel = new ResponseModel<>(Collections.emptyList());
        when(mockWechatClientService.getMemberInfoLabel("memberInfoGuid")).thenReturn(listResponseModel);

        // Run the test
        final LimitSpecialsActivityDetailsVO result = marketingActivityHelperUnderTest.querySelectSpecialsActivityDetailsVO(
                activitySelectList);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockRedisUtils).setEx("key", "value", 30L, TimeUnit.MINUTES);
    }

    @Test
    public void testQueryMemberRights() {
        // Setup
        final ResponseProductDiscount expectedResult = new ResponseProductDiscount();
        expectedResult.setMemberPrice(false);
        final ResponseDiscount responseDiscount = new ResponseDiscount();
        responseDiscount.setAllProducts(false);
        responseDiscount.setProductDiscounts(Arrays.asList("value"));
        expectedResult.setDiscountRespDTOList(Arrays.asList(responseDiscount));
        expectedResult.setMemberRightsType(0);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO();
        userMemberSessionDTO.setStoreGuid("storeGuid");
        userMemberSessionDTO.setOpenId("openId");
        userMemberSessionDTO.setUserGuid("userGuid");
        userMemberSessionDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        userMemberSessionDTO.setWhetherSupportMemberPrice(false);
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure HsaBaseClientService.getDiscountProducts(...).
        final ResponseProductDiscount responseProductDiscount = new ResponseProductDiscount();
        responseProductDiscount.setMemberPrice(false);
        final ResponseDiscount responseDiscount1 = new ResponseDiscount();
        responseDiscount1.setAllProducts(false);
        responseDiscount1.setProductDiscounts(Arrays.asList("value"));
        responseProductDiscount.setDiscountRespDTOList(Arrays.asList(responseDiscount1));
        responseProductDiscount.setMemberRightsType(0);
        final ResponseModel<ResponseProductDiscount> responseProductDiscountResponseModel = new ResponseModel<>(
                responseProductDiscount);
        when(mockWechatClientService.getDiscountProducts("memberInfoCardGuid", "storeGuid", 1))
                .thenReturn(responseProductDiscountResponseModel);

        // Run the test
        final ResponseProductDiscount result = marketingActivityHelperUnderTest.queryMemberRights("wxToken");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm UserMemberSessionUtils.addUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO1 = new UserMemberSessionDTO();
        userMemberSessionDTO1.setStoreGuid("storeGuid");
        userMemberSessionDTO1.setOpenId("openId");
        userMemberSessionDTO1.setUserGuid("userGuid");
        userMemberSessionDTO1.setMemberInfoCardGuid("memberInfoCardGuid");
        userMemberSessionDTO1.setWhetherSupportMemberPrice(false);
        verify(mockUserMemberSessionUtils).addUserMemberSession(userMemberSessionDTO1);

        // Confirm RedisUtils.setEx(...).
        final ResponseProductDiscount value = new ResponseProductDiscount();
        value.setMemberPrice(false);
        final ResponseDiscount responseDiscount2 = new ResponseDiscount();
        responseDiscount2.setAllProducts(false);
        responseDiscount2.setProductDiscounts(Arrays.asList("value"));
        value.setDiscountRespDTOList(Arrays.asList(responseDiscount2));
        value.setMemberRightsType(0);
        verify(mockRedisUtils).setEx("key", value, 30L, TimeUnit.MINUTES);
    }

    @Test
    public void testCalculateFullDiscountAndReduction() {
        // Setup
        final RequestMarketActivityUse req = new RequestMarketActivityUse();
        req.setVolumeGuid("volumeGuid");
        req.setActivityGuid("activityGuid");
        final RequestDishInfo requestDishInfo = new RequestDishInfo();
        req.setDishInfoDTOList(Arrays.asList(requestDishInfo));
        req.setMemberInfoCardGuid("memberInfoCardGuid");
        req.setIsMemberDiscount(false);

        final ResponseMarketActivityUse expectedResult = new ResponseMarketActivityUse();
        expectedResult.setActivityGuid("activityGuid");
        final RequestDishInfo requestDishInfo1 = new RequestDishInfo();
        expectedResult.setDishInfoDTOList(Arrays.asList(requestDishInfo1));
        expectedResult.setDiscountMoney(new BigDecimal("0.00"));
        final ResponseClientMarketActivity marketActivity = new ResponseClientMarketActivity();
        marketActivity.setGuid("4c6b55bc-8757-43c3-a053-99f19bd70a4b");
        marketActivity.setIsShare(0);
        marketActivity.setUseAble(false);
        marketActivity.setUnUseReason("tips");
        expectedResult.setActivitiesList(Arrays.asList(marketActivity));

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure HsaBaseClientService.queryMarket(...).
        final ResponseClientMarketActivity marketActivity1 = new ResponseClientMarketActivity();
        marketActivity1.setActivityType(0);
        marketActivity1.setGuid("4c6b55bc-8757-43c3-a053-99f19bd70a4b");
        marketActivity1.setIsShare(0);
        marketActivity1.setUseAble(false);
        marketActivity1.setUnUseReason("tips");
        final ResponseModel<List<ResponseClientMarketActivity>> listResponseModel = new ResponseModel<>(
                Arrays.asList(marketActivity1));
        final RequestMarketActivityUse marketplaceActivityUse = new RequestMarketActivityUse();
        marketplaceActivityUse.setVolumeGuid("volumeGuid");
        marketplaceActivityUse.setActivityGuid("activityGuid");
        final RequestDishInfo requestDishInfo2 = new RequestDishInfo();
        marketplaceActivityUse.setDishInfoDTOList(Arrays.asList(requestDishInfo2));
        marketplaceActivityUse.setMemberInfoCardGuid("memberInfoCardGuid");
        marketplaceActivityUse.setIsMemberDiscount(false);
        when(mockWechatClientService.queryMarket(marketplaceActivityUse)).thenReturn(listResponseModel);

        // Configure HsaBaseClientService.queryUseConditions(...).
        final ResponseVolumeInfo responseVolumeInfo = new ResponseVolumeInfo();
        responseVolumeInfo.setGuid("ab6dae16-0a72-4aae-9c34-5dd6dbc65a3e");
        responseVolumeInfo.setVolumeInfoGuid("volumeInfoGuid");
        responseVolumeInfo.setEnterpriseGuid("enterpriseGuid");
        responseVolumeInfo.setVolumeType(0);
        responseVolumeInfo.setIsUseAlone(0);
        final ResponseModel<ResponseVolumeInfo> responseVolumeInfoResponseModel = new ResponseModel<>(
                responseVolumeInfo);
        when(mockWechatClientService.queryUseConditions("volumeGuid")).thenReturn(responseVolumeInfoResponseModel);

        // Run the test
        final ResponseMarketActivityUse result = marketingActivityHelperUnderTest.calculateFullDiscountAndReduction(
                req);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockRedisUtils).setEx("key", "value", 30L, TimeUnit.MINUTES);

        // Confirm PriceCalculateHelper.calculatePreferentialPrice(...).
        final ResponseClientMarketActivity activity = new ResponseClientMarketActivity();
        activity.setActivityType(0);
        activity.setGuid("4c6b55bc-8757-43c3-a053-99f19bd70a4b");
        activity.setIsShare(0);
        activity.setUseAble(false);
        activity.setUnUseReason("tips");
        final ResponseMarketActivityUse respDTO = new ResponseMarketActivityUse();
        respDTO.setActivityGuid("activityGuid");
        final RequestDishInfo requestDishInfo3 = new RequestDishInfo();
        respDTO.setDishInfoDTOList(Arrays.asList(requestDishInfo3));
        respDTO.setDiscountMoney(new BigDecimal("0.00"));
        final ResponseClientMarketActivity marketActivity2 = new ResponseClientMarketActivity();
        marketActivity2.setGuid("4c6b55bc-8757-43c3-a053-99f19bd70a4b");
        marketActivity2.setIsShare(0);
        marketActivity2.setUseAble(false);
        marketActivity2.setUnUseReason("tips");
        respDTO.setActivitiesList(Arrays.asList(marketActivity2));
        verify(mockPriceCalculateHelper).calculatePreferentialPrice(activity, respDTO);

        // Confirm PriceCalculateHelper.calculateItemDiscountPrice(...).
        final ResponseClientMarketActivity activity1 = new ResponseClientMarketActivity();
        activity1.setActivityType(0);
        activity1.setGuid("4c6b55bc-8757-43c3-a053-99f19bd70a4b");
        activity1.setIsShare(0);
        activity1.setUseAble(false);
        activity1.setUnUseReason("tips");
        final ResponseMarketActivityUse respDTO1 = new ResponseMarketActivityUse();
        respDTO1.setActivityGuid("activityGuid");
        final RequestDishInfo requestDishInfo4 = new RequestDishInfo();
        respDTO1.setDishInfoDTOList(Arrays.asList(requestDishInfo4));
        respDTO1.setDiscountMoney(new BigDecimal("0.00"));
        final ResponseClientMarketActivity marketActivity3 = new ResponseClientMarketActivity();
        marketActivity3.setGuid("4c6b55bc-8757-43c3-a053-99f19bd70a4b");
        marketActivity3.setIsShare(0);
        marketActivity3.setUseAble(false);
        marketActivity3.setUnUseReason("tips");
        respDTO1.setActivitiesList(Arrays.asList(marketActivity3));
        verify(mockPriceCalculateHelper).calculateItemDiscountPrice(activity1, respDTO1);
    }

    @Test
    public void testCalculateFullDiscountAndReduction_HsaBaseClientServiceQueryMarketReturnsNoItems() {
        // Setup
        final RequestMarketActivityUse req = new RequestMarketActivityUse();
        req.setVolumeGuid("volumeGuid");
        req.setActivityGuid("activityGuid");
        final RequestDishInfo requestDishInfo = new RequestDishInfo();
        req.setDishInfoDTOList(Arrays.asList(requestDishInfo));
        req.setMemberInfoCardGuid("memberInfoCardGuid");
        req.setIsMemberDiscount(false);

        final ResponseMarketActivityUse expectedResult = new ResponseMarketActivityUse();
        expectedResult.setActivityGuid("activityGuid");
        final RequestDishInfo requestDishInfo1 = new RequestDishInfo();
        expectedResult.setDishInfoDTOList(Arrays.asList(requestDishInfo1));
        expectedResult.setDiscountMoney(new BigDecimal("0.00"));
        final ResponseClientMarketActivity marketActivity = new ResponseClientMarketActivity();
        marketActivity.setGuid("4c6b55bc-8757-43c3-a053-99f19bd70a4b");
        marketActivity.setIsShare(0);
        marketActivity.setUseAble(false);
        marketActivity.setUnUseReason("tips");
        expectedResult.setActivitiesList(Arrays.asList(marketActivity));

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure HsaBaseClientService.queryMarket(...).
        final ResponseModel<List<ResponseClientMarketActivity>> listResponseModel = new ResponseModel<>(
                Collections.emptyList());
        final RequestMarketActivityUse marketplaceActivityUse = new RequestMarketActivityUse();
        marketplaceActivityUse.setVolumeGuid("volumeGuid");
        marketplaceActivityUse.setActivityGuid("activityGuid");
        final RequestDishInfo requestDishInfo2 = new RequestDishInfo();
        marketplaceActivityUse.setDishInfoDTOList(Arrays.asList(requestDishInfo2));
        marketplaceActivityUse.setMemberInfoCardGuid("memberInfoCardGuid");
        marketplaceActivityUse.setIsMemberDiscount(false);
        when(mockWechatClientService.queryMarket(marketplaceActivityUse)).thenReturn(listResponseModel);

        // Configure HsaBaseClientService.queryUseConditions(...).
        final ResponseVolumeInfo responseVolumeInfo = new ResponseVolumeInfo();
        responseVolumeInfo.setGuid("ab6dae16-0a72-4aae-9c34-5dd6dbc65a3e");
        responseVolumeInfo.setVolumeInfoGuid("volumeInfoGuid");
        responseVolumeInfo.setEnterpriseGuid("enterpriseGuid");
        responseVolumeInfo.setVolumeType(0);
        responseVolumeInfo.setIsUseAlone(0);
        final ResponseModel<ResponseVolumeInfo> responseVolumeInfoResponseModel = new ResponseModel<>(
                responseVolumeInfo);
        when(mockWechatClientService.queryUseConditions("volumeGuid")).thenReturn(responseVolumeInfoResponseModel);

        // Run the test
        final ResponseMarketActivityUse result = marketingActivityHelperUnderTest.calculateFullDiscountAndReduction(
                req);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockRedisUtils).setEx("key", "value", 30L, TimeUnit.MINUTES);

        // Confirm PriceCalculateHelper.calculatePreferentialPrice(...).
        final ResponseClientMarketActivity activity = new ResponseClientMarketActivity();
        activity.setActivityType(0);
        activity.setGuid("4c6b55bc-8757-43c3-a053-99f19bd70a4b");
        activity.setIsShare(0);
        activity.setUseAble(false);
        activity.setUnUseReason("tips");
        final ResponseMarketActivityUse respDTO = new ResponseMarketActivityUse();
        respDTO.setActivityGuid("activityGuid");
        final RequestDishInfo requestDishInfo3 = new RequestDishInfo();
        respDTO.setDishInfoDTOList(Arrays.asList(requestDishInfo3));
        respDTO.setDiscountMoney(new BigDecimal("0.00"));
        final ResponseClientMarketActivity marketActivity1 = new ResponseClientMarketActivity();
        marketActivity1.setGuid("4c6b55bc-8757-43c3-a053-99f19bd70a4b");
        marketActivity1.setIsShare(0);
        marketActivity1.setUseAble(false);
        marketActivity1.setUnUseReason("tips");
        respDTO.setActivitiesList(Arrays.asList(marketActivity1));
        verify(mockPriceCalculateHelper).calculatePreferentialPrice(activity, respDTO);

        // Confirm PriceCalculateHelper.calculateItemDiscountPrice(...).
        final ResponseClientMarketActivity activity1 = new ResponseClientMarketActivity();
        activity1.setActivityType(0);
        activity1.setGuid("4c6b55bc-8757-43c3-a053-99f19bd70a4b");
        activity1.setIsShare(0);
        activity1.setUseAble(false);
        activity1.setUnUseReason("tips");
        final ResponseMarketActivityUse respDTO1 = new ResponseMarketActivityUse();
        respDTO1.setActivityGuid("activityGuid");
        final RequestDishInfo requestDishInfo4 = new RequestDishInfo();
        respDTO1.setDishInfoDTOList(Arrays.asList(requestDishInfo4));
        respDTO1.setDiscountMoney(new BigDecimal("0.00"));
        final ResponseClientMarketActivity marketActivity2 = new ResponseClientMarketActivity();
        marketActivity2.setGuid("4c6b55bc-8757-43c3-a053-99f19bd70a4b");
        marketActivity2.setIsShare(0);
        marketActivity2.setUseAble(false);
        marketActivity2.setUnUseReason("tips");
        respDTO1.setActivitiesList(Arrays.asList(marketActivity2));
        verify(mockPriceCalculateHelper).calculateItemDiscountPrice(activity1, respDTO1);
    }

    @Test
    public void testQueryFullDiscountAndReductionActivityList() {
        // Setup
        final RequestMarketActivityUse req = new RequestMarketActivityUse();
        req.setVolumeGuid("volumeGuid");
        req.setActivityGuid("activityGuid");
        final RequestDishInfo requestDishInfo = new RequestDishInfo();
        req.setDishInfoDTOList(Arrays.asList(requestDishInfo));
        req.setMemberInfoCardGuid("memberInfoCardGuid");
        req.setIsMemberDiscount(false);

        final WxMemberSessionDTO wxMemberSessionDTO = new WxMemberSessionDTO();
        final WxUserInfoDTO wxUserInfoDTO = new WxUserInfoDTO();
        wxUserInfoDTO.setIsLogin(false);
        wxMemberSessionDTO.setWxUserInfoDTO(wxUserInfoDTO);
        wxMemberSessionDTO.setOperSubjectGuid("operSubjectGuid");
        wxMemberSessionDTO.setStoreGuid("storeGuid");
        wxMemberSessionDTO.setMemberInfoGuid("memberInfoGuid");
        wxMemberSessionDTO.setOrderState(0);
        wxMemberSessionDTO.setWxtoken("wxtoken");

        final ResponseClientMarketActivity marketActivity = new ResponseClientMarketActivity();
        marketActivity.setActivityType(0);
        marketActivity.setGuid("4c6b55bc-8757-43c3-a053-99f19bd70a4b");
        marketActivity.setIsShare(0);
        marketActivity.setUseAble(false);
        marketActivity.setUnUseReason("tips");
        final List<ResponseClientMarketActivity> expectedResult = Arrays.asList(marketActivity);
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure HsaBaseClientService.queryMarket(...).
        final ResponseClientMarketActivity marketActivity1 = new ResponseClientMarketActivity();
        marketActivity1.setActivityType(0);
        marketActivity1.setGuid("4c6b55bc-8757-43c3-a053-99f19bd70a4b");
        marketActivity1.setIsShare(0);
        marketActivity1.setUseAble(false);
        marketActivity1.setUnUseReason("tips");
        final ResponseModel<List<ResponseClientMarketActivity>> listResponseModel = new ResponseModel<>(
                Arrays.asList(marketActivity1));
        final RequestMarketActivityUse marketplaceActivityUse = new RequestMarketActivityUse();
        marketplaceActivityUse.setVolumeGuid("volumeGuid");
        marketplaceActivityUse.setActivityGuid("activityGuid");
        final RequestDishInfo requestDishInfo1 = new RequestDishInfo();
        marketplaceActivityUse.setDishInfoDTOList(Arrays.asList(requestDishInfo1));
        marketplaceActivityUse.setMemberInfoCardGuid("memberInfoCardGuid");
        marketplaceActivityUse.setIsMemberDiscount(false);
        when(mockWechatClientService.queryMarket(marketplaceActivityUse)).thenReturn(listResponseModel);

        // Run the test
        final List<ResponseClientMarketActivity> result = marketingActivityHelperUnderTest.queryFullDiscountAndReductionActivityList(
                req, wxMemberSessionDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockRedisUtils).setEx("key", "value", 30L, TimeUnit.MINUTES);
    }

    @Test
    public void testQueryFullDiscountAndReductionActivityList_HsaBaseClientServiceReturnsNoItems() {
        // Setup
        final RequestMarketActivityUse req = new RequestMarketActivityUse();
        req.setVolumeGuid("volumeGuid");
        req.setActivityGuid("activityGuid");
        final RequestDishInfo requestDishInfo = new RequestDishInfo();
        req.setDishInfoDTOList(Arrays.asList(requestDishInfo));
        req.setMemberInfoCardGuid("memberInfoCardGuid");
        req.setIsMemberDiscount(false);

        final WxMemberSessionDTO wxMemberSessionDTO = new WxMemberSessionDTO();
        final WxUserInfoDTO wxUserInfoDTO = new WxUserInfoDTO();
        wxUserInfoDTO.setIsLogin(false);
        wxMemberSessionDTO.setWxUserInfoDTO(wxUserInfoDTO);
        wxMemberSessionDTO.setOperSubjectGuid("operSubjectGuid");
        wxMemberSessionDTO.setStoreGuid("storeGuid");
        wxMemberSessionDTO.setMemberInfoGuid("memberInfoGuid");
        wxMemberSessionDTO.setOrderState(0);
        wxMemberSessionDTO.setWxtoken("wxtoken");

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure HsaBaseClientService.queryMarket(...).
        final ResponseModel<List<ResponseClientMarketActivity>> listResponseModel = new ResponseModel<>(
                Collections.emptyList());
        final RequestMarketActivityUse marketplaceActivityUse = new RequestMarketActivityUse();
        marketplaceActivityUse.setVolumeGuid("volumeGuid");
        marketplaceActivityUse.setActivityGuid("activityGuid");
        final RequestDishInfo requestDishInfo1 = new RequestDishInfo();
        marketplaceActivityUse.setDishInfoDTOList(Arrays.asList(requestDishInfo1));
        marketplaceActivityUse.setMemberInfoCardGuid("memberInfoCardGuid");
        marketplaceActivityUse.setIsMemberDiscount(false);
        when(mockWechatClientService.queryMarket(marketplaceActivityUse)).thenReturn(listResponseModel);

        // Run the test
        final List<ResponseClientMarketActivity> result = marketingActivityHelperUnderTest.queryFullDiscountAndReductionActivityList(
                req, wxMemberSessionDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
        verify(mockRedisUtils).setEx("key", "value", 30L, TimeUnit.MINUTES);
    }
}
