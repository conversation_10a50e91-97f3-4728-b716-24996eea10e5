package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.holder.saas.aggregation.weixin.service.WxStoreTradeOrderService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxOrderRecordClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxStorePayClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxStoreTradeOrderClientService;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseCardRight;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseCardRightDetail;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolumeDetails;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.AppendFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.OrderFeeDetailDTO;
import com.holderzone.saas.store.dto.pay.AggPayRespDTO;
import com.holderzone.saas.store.dto.pay.KbzPayStartDTO;
import com.holderzone.saas.store.dto.weixin.*;
import com.holderzone.saas.store.dto.weixin.member.*;
import com.holderzone.saas.store.dto.weixin.req.WxH5PayReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPaidOrderDetailsReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPrepayReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(WxStoreTradeOrderController.class)
public class WxStoreTradeOrderControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStoreTradeOrderClientService mockWxStoreTradeOrderClientService;
    @MockBean
    private WxStorePayClientService mockWxStorePayClientService;
    @MockBean
    private WxOrderRecordClientService mockWxOrderRecordClientService;
    @MockBean
    private WxStoreTradeOrderService mockWxStoreTradeOrderService;
    @MockBean
    private WxStoreTradeOrderController mockWxStoreTradeOrderController;


    @Test
    public void testTableOrderDetails() throws Exception {
        // Setup
        // Configure WxStoreTradeOrderClientService.tableOrderDetails(...).
        final WebSocketMessageDTO webSocketMessageDTO = new WebSocketMessageDTO<>(0, 0, "content", 0, "errorMsg");
        when(mockWxStoreTradeOrderClientService.tableOrderDetails(new WxStoreAdvanceConsumerReqDTO(0, "tradeOrderGuid",
                new WxStoreConsumerDTO("consumerGuid", "openId", "unionId", "nickName", "headImgUrl", 0, "country",
                        "province", "city", "","", "phone", 0, "deviceId", "enterpriseGuid", "enterpriseName", "storeGuid",
                        "storeName", "userGuid", "userName", "account", 0, "diningTableGuid", "tableCode",
                        "diningTableName", "areaGuid", "areaName", "brandName", "brandGuid", "brandLogo", false,
                        "memberInfoGuid", "phoneNum", false, "operSubjectGuid", false), 0, "orderRemark", 0, "nopenId",
                0, "payGuid", 0))).thenReturn(webSocketMessageDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_trade_order/table_details")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSubmitOrder() throws Exception {
        // Setup
        // Configure WxStoreTradeOrderClientService.submitOrder(...).
        final SubmitReturnDTO submitReturnDTO = new SubmitReturnDTO(0, "errorMsg", "orderGuid");
        when(mockWxStoreTradeOrderClientService.submitOrder(new WxStoreAdvanceConsumerReqDTO(0, "tradeOrderGuid",
                new WxStoreConsumerDTO("consumerGuid", "openId", "unionId", "nickName", "headImgUrl", 0, "country",
                        "province", "city", "","", "phone", 0, "deviceId", "enterpriseGuid", "enterpriseName", "storeGuid",
                        "storeName", "userGuid", "userName", "account", 0, "diningTableGuid", "tableCode",
                        "diningTableName", "areaGuid", "areaName", "brandName", "brandGuid", "brandLogo", false,
                        "memberInfoGuid", "phoneNum", false, "operSubjectGuid", false), 0, "orderRemark", 0, "nopenId",
                0, "payGuid", 0))).thenReturn(submitReturnDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_trade_order/submit")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testUpdateRemark() throws Exception {
        // Setup
        // Configure WxStoreTradeOrderClientService.updateRemark(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setGuid("56ed3d34-45de-4aa7-add8-ce45031ffbec");
        createDineInOrderReqDTO.setUserWxPublicOpenId("userWxPublicOpenId");
        createDineInOrderReqDTO.setRemark("remark");
        createDineInOrderReqDTO.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO.setDiningTableName("diningTableName");
        when(mockWxStoreTradeOrderClientService.updateRemark(createDineInOrderReqDTO)).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_trade_order/update_remark")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testUpdateGuestCount() throws Exception {
        // Setup
        // Configure WxStoreTradeOrderClientService.updateGuestCount(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setGuid("56ed3d34-45de-4aa7-add8-ce45031ffbec");
        createDineInOrderReqDTO.setUserWxPublicOpenId("userWxPublicOpenId");
        createDineInOrderReqDTO.setRemark("remark");
        createDineInOrderReqDTO.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO.setDiningTableName("diningTableName");
        when(mockWxStoreTradeOrderClientService.updateGuestCount(createDineInOrderReqDTO)).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_trade_order/update_guest_count")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testOrderPay() throws Exception {
        // Setup
        // Configure WxStoreTradeOrderClientService.orderPay(...).
        final WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO = new WxStoreTradeOrderDetailsDTO();
        wxStoreTradeOrderDetailsDTO.setMainTable(0);
        wxStoreTradeOrderDetailsDTO.setGuid("96ff4132-8d2e-4657-a4ec-50d61cce6cb6");
        wxStoreTradeOrderDetailsDTO.setWxGuid("wxGuid");
        wxStoreTradeOrderDetailsDTO.setBatchId("batchId");
        wxStoreTradeOrderDetailsDTO.setTableGuid("tableGuid");
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        orderFeeDetailDTO.setItemTotalFee(new BigDecimal("0.00"));
        final AppendFeeDetailDTO appendFeeDetailDTO = new AppendFeeDetailDTO();
        appendFeeDetailDTO.setGuid(0L);
        appendFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO.setName("name");
        orderFeeDetailDTO.setAppendFeeDetailDTOS(Arrays.asList(appendFeeDetailDTO));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setGuid("74ff1a5e-baba-41f3-bdab-562b5f217126");
        actuallyPayFeeDetailDTO.setBankTransactionId("bankTransactionId");
        actuallyPayFeeDetailDTO.setFaceCode("faceCode");
        actuallyPayFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        actuallyPayFeeDetailDTO.setTradingType(0);
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setGuid("ce1d41dc-48a4-4a8b-b549-290c82facb98");
        discountFeeDetailDTO.setOrderGuid("orderGuid");
        discountFeeDetailDTO.setDiscountName("discountName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        final AppendFeeDetailDTO appendFeeDetailDTO1 = new AppendFeeDetailDTO();
        appendFeeDetailDTO1.setGuid(0L);
        appendFeeDetailDTO1.setAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO1.setName("name");
        appendFeeDetailDTO1.setType(0);
        appendFeeDetailDTO1.setUnitPrice(new BigDecimal("0.00"));
        final WxStoreTradeOrderDetailsRespDTO wxStoreTradeOrderDetailsRespDTO = new WxStoreTradeOrderDetailsRespDTO(0,
                Arrays.asList(new WxStoreTradeDetailsGroupDTO("tableGuid", "tableCode", "areaName", 0,
                        Arrays.asList(wxStoreTradeOrderDetailsDTO))), "orderNo", "mark", "tableCode", 0,
                new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                Arrays.asList(orderFeeDetailDTO), Arrays.asList(actuallyPayFeeDetailDTO),
                Arrays.asList(discountFeeDetailDTO), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "diningTableName", 0, 0, "areaName", "storeName", "brandName",
                "payWay", "errorMsg", Arrays.asList(appendFeeDetailDTO1), new BigDecimal("0.00"),
                new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"), 0, 0, new BigDecimal("0.00"), new BigDecimal("0.00"), 0, new BigDecimal("0.00"),
                "cardGuid", "cardName", "memberInfoCardGuid", "systemManagementGuid", 0, "volumeCode");
        when(mockWxStoreTradeOrderClientService.orderPay(new WxStoreAdvanceConsumerReqDTO(0, "tradeOrderGuid",
                new WxStoreConsumerDTO("consumerGuid", "openId", "unionId", "nickName", "headImgUrl", 0, "country",
                        "province", "city", "","", "phone", 0, "deviceId", "enterpriseGuid", "enterpriseName", "storeGuid",
                        "storeName", "userGuid", "userName", "account", 0, "diningTableGuid", "tableCode",
                        "diningTableName", "areaGuid", "areaName", "brandName", "brandGuid", "brandLogo", false,
                        "memberInfoGuid", "phoneNum", false, "operSubjectGuid", false), 0, "orderRemark", 0, "nopenId",
                0, "payGuid", 0))).thenReturn(wxStoreTradeOrderDetailsRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_trade_order/order_pay")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testOrderDefrey() throws Exception {
        // Setup
        // Configure WxStorePayClientService.weChatPublic(...).
        final KbzPayStartDTO kbzPayStartDTO = new KbzPayStartDTO();
        kbzPayStartDTO.setPrePayId("prePayId");
        kbzPayStartDTO.setOrderInfo("orderInfo");
        kbzPayStartDTO.setSign("sign");
        final WxPayRespDTO wxPayRespDTO = new WxPayRespDTO("payUrl", 0, "errorMsg",
                new AggPayRespDTO("code", "msg", "result", "payGuid", "orderGuid", "paymentAppId"), kbzPayStartDTO);
        when(mockWxStorePayClientService.weChatPublic(new WxH5PayReqDTO(
                new WxStoreConsumerDTO("consumerGuid", "openId", "unionId", "nickName", "headImgUrl", 0, "country",
                        "province", "city", "","", "phone", 0, "deviceId", "enterpriseGuid", "enterpriseName", "storeGuid",
                        "storeName", "userGuid", "userName", "account", 0, "diningTableGuid", "tableCode",
                        "diningTableName", "areaGuid", "areaName", "brandName", "brandGuid", "brandLogo", false,
                        "memberInfoGuid", "phoneNum", false, "operSubjectGuid", false), "outNotifyUrl",
                "orderGuid"))).thenReturn(wxPayRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_trade_order/order_defrey")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testOrderDefrey_WxStorePayClientServiceReturnsFailure() throws Exception {
        // Setup
        // Configure WxStorePayClientService.weChatPublic(...).
        final WxPayRespDTO wxPayRespDTO = WxPayRespDTO.changeFailed();
        when(mockWxStorePayClientService.weChatPublic(new WxH5PayReqDTO(
                new WxStoreConsumerDTO("consumerGuid", "openId", "unionId", "nickName", "headImgUrl", 0, "country",
                        "province", "city", "","", "phone", 0, "deviceId", "enterpriseGuid", "enterpriseName", "storeGuid",
                        "storeName", "userGuid", "userName", "account", 0, "diningTableGuid", "tableCode",
                        "diningTableName", "areaGuid", "areaName", "brandName", "brandGuid", "brandLogo", false,
                        "memberInfoGuid", "phoneNum", false, "operSubjectGuid", false), "outNotifyUrl",
                "orderGuid"))).thenReturn(wxPayRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_trade_order/order_defrey")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetWxStoreUserOrder() throws Exception {
        // Setup
        // Configure WxOrderRecordClientService.getWxStoreUserOrder(...).
        final WxStoreUserOrderDTO wxStoreUserOrderDTO = new WxStoreUserOrderDTO();
        final WxStoreDineinOrderDetailsRespDTO wxStoreDineinOrderDetailsRespDTO = new WxStoreDineinOrderDetailsRespDTO();
        wxStoreDineinOrderDetailsRespDTO.setGuid("28ca4593-bdbc-450d-a0f6-a3b23e2aeb36");
        wxStoreDineinOrderDetailsRespDTO.setLogUrl("logUrl");
        wxStoreDineinOrderDetailsRespDTO.setStoreName("storeName");
        wxStoreDineinOrderDetailsRespDTO.setTradeMode(0);
        wxStoreUserOrderDTO.setOrders(Arrays.asList(wxStoreDineinOrderDetailsRespDTO));
        when(mockWxOrderRecordClientService.getWxStoreUserOrder(new WxStoreAdvanceConsumerReqDTO(0, "tradeOrderGuid",
                new WxStoreConsumerDTO("consumerGuid", "openId", "unionId", "nickName", "headImgUrl", 0, "country",
                        "province", "city", "","", "phone", 0, "deviceId", "enterpriseGuid", "enterpriseName", "storeGuid",
                        "storeName", "userGuid", "userName", "account", 0, "diningTableGuid", "tableCode",
                        "diningTableName", "areaGuid", "areaName", "brandName", "brandGuid", "brandLogo", false,
                        "memberInfoGuid", "phoneNum", false, "operSubjectGuid", false), 0, "orderRemark", 0, "nopenId",
                0, "payGuid", 0))).thenReturn(wxStoreUserOrderDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_trade_order/user_order_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testUpdateWxOrderRecord() throws Exception {
        // Setup
        when(mockWxOrderRecordClientService.updateWxOrderRecord(new WxStoreAdvanceConsumerReqDTO(0, "tradeOrderGuid",
                new WxStoreConsumerDTO("consumerGuid", "openId", "unionId", "nickName", "headImgUrl", 0, "country",
                        "province", "city", "","", "phone", 0, "deviceId", "enterpriseGuid", "enterpriseName", "storeGuid",
                        "storeName", "userGuid", "userName", "account", 0, "diningTableGuid", "tableCode",
                        "diningTableName", "areaGuid", "areaName", "brandName", "brandGuid", "brandLogo", false,
                        "memberInfoGuid", "phoneNum", false, "operSubjectGuid", false), 0, "orderRemark", 0, "nopenId",
                0, "payGuid", 0))).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_trade_order/update")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testValidateCardAndVolume() throws Exception {
        // Setup
        // Configure WxStoreTradeOrderClientService.validateCardAndVolume(...).
        final CardAndVolumeDTO cardAndVolumeDTO = new CardAndVolumeDTO(
                Arrays.asList(new MemberDiscountDTO("discountName", 0, new BigDecimal("0.00"))), new BigDecimal("0.00"),
                0, "errorMsg");
        when(mockWxStoreTradeOrderClientService.validateCardAndVolume(
                new CardAndVolumeDiscountReqDTO("enterpriseGuid", "brandGuid", "storeGuid", "tableGuid", "openId",
                        "volumeCode", "memberInfoCardGuid", 0, "volumeName", 0))).thenReturn(cardAndVolumeDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_trade_order/validate_card")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testCardList2() throws Exception {
        // Setup
        // Configure WxStoreTradeOrderClientService.cardList2(...).
        final ResponseCardRight responseCardRight = new ResponseCardRight();
        responseCardRight.setName("name");
        final ResponseCardRightDetail responseCardRightDetail = new ResponseCardRightDetail();
        responseCardRightDetail.setRightsGuid("rightsGuid");
        responseCardRightDetail.setRightsName("rightsName");
        responseCardRightDetail.setGainCondition("gainCondition");
        responseCardRight.setList(Arrays.asList(responseCardRightDetail));
        final WxMemberCardRespDTO wxMemberCardRespDTO = new WxMemberCardRespDTO(0, Arrays.asList(
                new WxMemberCardDTO("cardQRCode", "cardGuid", "memberInfoCardGuid", "systemManagementGuid",
                        "enterpriseMemberInfoSystemGuid", "cardName", "cardLogo", "cardIcon", "cardColour", 0,
                        "cardStartDate", "cardEndDate", 0, "systemManagementCardNum", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "cardIntegral", "cardGrowthValue", "cardLevelGuid", "cardLevelName", 0,
                        "levelIcon", Arrays.asList(responseCardRight), 0)), "errorMsg");
        when(mockWxStoreTradeOrderClientService.cardList2(
                new WxMemberCardListReqDTO("openId", "enterpriseGuid", "brandGuid", "storeGuid")))
                .thenReturn(wxMemberCardRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_trade_order/member_card_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testMemberPay() throws Exception {
        // Setup
        when(mockWxStorePayClientService.memberPay(
                new WxMemberPayDTO("enterpriseGuid", "storeGuid", "tableGuid", "openId", "nickName", "headImgUrl",
                        "memberPassWord", "storeName",
                        new WxStoreConsumerDTO("consumerGuid", "openId", "unionId", "nickName", "headImgUrl", 0,
                                "country", "province", "city", "","", "phone", 0, "deviceId", "enterpriseGuid",
                                "enterpriseName", "storeGuid", "storeName", "userGuid", "userName", "account", 0,
                                "diningTableGuid", "tableCode", "diningTableName", "areaGuid", "areaName", "brandName",
                                "brandGuid", "brandLogo", false, "memberInfoGuid", "phoneNum", false, "operSubjectGuid",
                                false)))).thenReturn(new WxStorePayResultDTO(0, "errorMsg"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_trade_order/member_pay")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetAllPayWay() throws Exception {
        // Setup
        // Configure WxStorePayClientService.getAllPayWay(...).
        final WxPayWayRespDTO wxPayWayRespDTO = new WxPayWayRespDTO("cardName", "payWayName", new BigDecimal("0.00"), 0,
                new BigDecimal("0.00"), 0);
        when(mockWxStorePayClientService.getAllPayWay(
                new WxStorePayReqDTO("enterpriseGuid", "brandGuid", "storeGuid", "tableGuid", "openId")))
                .thenReturn(wxPayWayRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_trade_order/pay_way")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testPrepay() throws Exception {
        // Setup
        when(mockWxStorePayClientService.prepay(
                new WxPrepayReqDTO("enterpriseGuid", "brandGuid", "storeGuid", "tableGuid", "openId",
                        new BigDecimal("0.00"), "memberCardGuid", "cardGuid", "memberInfoCardGuid",
                        "systemManagementGuid", 0, "volumeCode", "orderGuid", 0, new BigDecimal("0.00"),
                        "memberInfoGuid", new BigDecimal("0.00"), 0))).thenReturn(new WxPrepayRespDTO(0, "errorMsg"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_trade_order/prepay")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testPrepay_WxStorePayClientServiceReturnsFailure() throws Exception {
        // Setup
        when(mockWxStorePayClientService.prepay(
                new WxPrepayReqDTO("enterpriseGuid", "brandGuid", "storeGuid", "tableGuid", "openId",
                        new BigDecimal("0.00"), "memberCardGuid", "cardGuid", "memberInfoCardGuid",
                        "systemManagementGuid", 0, "volumeCode", "orderGuid", 0, new BigDecimal("0.00"),
                        "memberInfoGuid", new BigDecimal("0.00"), 0))).thenReturn(WxPrepayRespDTO.changeFailed());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_trade_order/prepay")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testZeroPay() throws Exception {
        // Setup
        when(mockWxStorePayClientService.zeroPay(new WxZeroPayReqDTO(
                new WxStoreConsumerDTO("consumerGuid", "openId", "unionId", "nickName", "headImgUrl", 0, "country",
                        "province", "city", "","", "phone", 0, "deviceId", "enterpriseGuid", "enterpriseName", "storeGuid",
                        "storeName", "userGuid", "userName", "account", 0, "diningTableGuid", "tableCode",
                        "diningTableName", "areaGuid", "areaName", "brandName", "brandGuid", "brandLogo", false,
                        "memberInfoGuid", "phoneNum", false, "operSubjectGuid", false))))
                .thenReturn(new WxStorePayResultDTO(0, "errorMsg"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_trade_order/zero_pay")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testVolumeCodeList() throws Exception {
        // Setup
        // Configure WxStoreTradeOrderClientService.volumeCodeList2(...).
        final ResponseMemberInfoVolumeDetails responseMemberInfoVolumeDetails = new ResponseMemberInfoVolumeDetails();
        responseMemberInfoVolumeDetails.setMemberVolumeGuid("memberVolumeGuid");
        responseMemberInfoVolumeDetails.setVolumeInfoGuid("volumeInfoGuid");
        responseMemberInfoVolumeDetails.setVolumeInfoName("volumeInfoName");
        responseMemberInfoVolumeDetails.setVolumeSimpleDesc("volumeSimpleDesc");
        responseMemberInfoVolumeDetails.setVolumeStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final WxVolumeCodeRespDTO wxVolumeCodeRespDTO = new WxVolumeCodeRespDTO(Arrays.asList(
                new WxVolumeCodeDTO("memberVolumeGuid", responseMemberInfoVolumeDetails, "volumeInfoGuid",
                        "volumeInfoName", 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, new BigDecimal("0.00"), 0, 0,
                        new BigDecimal("0.00"), 0, "volumeCode")), 0, "errorMsg");
        when(mockWxStoreTradeOrderClientService.volumeCodeList2(
                new WxVolumeCodeReqDTO("enterpriseGuid", "brandGuid", "storeGuid", "openId")))
                .thenReturn(wxVolumeCodeRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_trade_order/member_volume_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testValidateOrder() throws Exception {
        // Setup
        when(mockWxStorePayClientService.validateOrder(
                new WxStorePayReqDTO("enterpriseGuid", "brandGuid", "storeGuid", "tableGuid", "openId")))
                .thenReturn(new WxPrepayRespDTO(0, "errorMsg"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_trade_order/validate_order")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testValidateOrder_WxStorePayClientServiceReturnsFailure() throws Exception {
        // Setup
        when(mockWxStorePayClientService.validateOrder(
                new WxStorePayReqDTO("enterpriseGuid", "brandGuid", "storeGuid", "tableGuid", "openId")))
                .thenReturn(WxPrepayRespDTO.changeFailed());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_trade_order/validate_order")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testMemberConfirm() throws Exception {
        // Setup
        // Configure WxStorePayClientService.memberConfirm(...).
        final WxPrepayConfirmRespDTO wxPrepayConfirmRespDTO = new WxPrepayConfirmRespDTO("errorMsg", 0);
        when(mockWxStorePayClientService.memberConfirm(
                new WxPrepayConfirmReqDTO("enterpriseGuid", "brandGuid", "storeGuid", "tableGuid", "openId", "cardGuid",
                        "cardName", "memberInfoCardGuid", "systemManagementGuid", 0, "volumeCode")))
                .thenReturn(wxPrepayConfirmRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_trade_order/prepay_confirm")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testOrderDetails() throws Exception {
        // Setup
        // Configure WxStoreTradeOrderClientService.orderDetails(...).
        final WebSocketMessageDTO webSocketMessageDTO = new WebSocketMessageDTO<>(0, 0, "content", 0, "errorMsg");
        when(mockWxStoreTradeOrderClientService.orderDetails(
                new WxPaidOrderDetailsReqDTO("enterpriseGuid", "storeGuid", "openId", "orderGuid")))
                .thenReturn(webSocketMessageDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_trade_order/paid_order_details")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testExceptionTest() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/wx_store_trade_order/exception_test")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
        verify(mockWxStoreTradeOrderClientService).exceptionTest();
    }

    @Test
    public void testUpdateFastGuestCount() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        put("/wx_store_trade_order/update_fast_guest_count/{count}", 0)
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
        verify(mockWxStoreTradeOrderService).updateFastGuestCount(0);
    }
}
