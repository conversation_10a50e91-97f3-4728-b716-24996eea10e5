package com.holderzone.holder.saas.aggregation.app.aop;

import com.holderzone.holder.saas.aggregation.app.config.ApiVersionControlConfig;
import org.aspectj.lang.JoinPoint;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ApiVersionControlAspectTest {

    @Mock
    private ApiVersionControlConfig mockApiVersionControlConfig;

    @InjectMocks
    private ApiVersionControlAspect apiVersionControlAspectUnderTest;

    @Test
    public void testPointCut() {
        apiVersionControlAspectUnderTest.pointCut();
    }

    @Test
    public void testDoBefore() {
        // Setup
        final JoinPoint joinPoint = null;

        // Configure ApiVersionControlConfig.getControl(...).
        final ApiVersionControlConfig.InnerControl innerControl = new ApiVersionControlConfig.InnerControl();
        innerControl.setMin(0);
        innerControl.setUri("uri");
        final List<ApiVersionControlConfig.InnerControl> innerControls = Arrays.asList(innerControl);
        when(mockApiVersionControlConfig.getControl()).thenReturn(innerControls);

        // Run the test
        apiVersionControlAspectUnderTest.doBefore(joinPoint);

        // Verify the results
    }

    @Test
    public void testDoBefore_ApiVersionControlConfigReturnsNoItems() {
        // Setup
        final JoinPoint joinPoint = null;
        when(mockApiVersionControlConfig.getControl()).thenReturn(Collections.emptyList());

        // Run the test
        apiVersionControlAspectUnderTest.doBefore(joinPoint);

        // Verify the results
    }
}
