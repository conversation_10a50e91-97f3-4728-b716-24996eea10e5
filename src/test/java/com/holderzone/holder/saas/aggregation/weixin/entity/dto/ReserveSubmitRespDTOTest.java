package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import org.junit.Before;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class ReserveSubmitRespDTOTest {

    private ReserveSubmitRespDTO reserveSubmitRespDTOUnderTest;

    @Before
    public void setUp() throws Exception {
        reserveSubmitRespDTOUnderTest = new ReserveSubmitRespDTO(0, "errorMsg");
    }

    @Test
    public void testCodeGetterAndSetter() {
        final Integer code = 0;
        reserveSubmitRespDTOUnderTest.setCode(code);
        assertThat(reserveSubmitRespDTOUnderTest.getCode()).isEqualTo(code);
    }

    @Test
    public void testErrorMsgGetterAndSetter() {
        final String errorMsg = "errorMsg";
        reserveSubmitRespDTOUnderTest.setErrorMsg(errorMsg);
        assertThat(reserveSubmitRespDTOUnderTest.getErrorMsg()).isEqualTo(errorMsg);
    }

    @Test
    public void testEquals() throws Exception {
        assertThat(reserveSubmitRespDTOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() throws Exception {
        assertThat(reserveSubmitRespDTOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() throws Exception {
        assertThat(reserveSubmitRespDTOUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() throws Exception {
        assertThat(reserveSubmitRespDTOUnderTest.toString()).isEqualTo("result");
    }
}
