package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.aggregation.app.service.feign.activity.ThirdActivityClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.groupbuy.GroupClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.item.ItemClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInOrderClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.GrouponClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.TradeThirdActivityClientService;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityRespDTO;
import com.holderzone.saas.store.dto.order.inside.OrderGuidsDTO;
import com.holderzone.saas.store.dto.order.inside.OrderTableInfoDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.trade.req.ThirdActivityRecordDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GroupBuyServiceImplTest {

    @Mock
    private GroupClientService mockGroupClientService;
    @Mock
    private GrouponClientService mockGrouponClientService;
    @Mock
    private ThirdActivityClientService mockThirdActivityClientService;
    @Mock
    private TradeThirdActivityClientService mockTradeThirdActivityClientService;
    @Mock
    private DineInOrderClientService mockDineInOrderClientService;
    @Mock
    private ItemClientService mockItemClientService;

    private GroupBuyServiceImpl groupBuyServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        groupBuyServiceImplUnderTest = new GroupBuyServiceImpl(mockGroupClientService, mockGrouponClientService,
                mockThirdActivityClientService, mockTradeThirdActivityClientService, mockDineInOrderClientService,
                mockItemClientService);
    }

    @Test(expected = BusinessException.class)
    public void testPreCheck_ThrowsBusinessException() {
        // Setup
        final CouPonPreReqDTO couPonPreReqDTO = new CouPonPreReqDTO();
        couPonPreReqDTO.setCouponCode("couponCode");
        couPonPreReqDTO.setErpOrderId("orderGuid");
        couPonPreReqDTO.setActivityGuid("activityGuid");
        couPonPreReqDTO.setUsedGuidList(Arrays.asList("value"));
        couPonPreReqDTO.setGroupBuyType(0);

        // Configure GroupClientService.couponPrepare(...).
        final MtCouponPreRespDTO mtCouponPreRespDTO = new MtCouponPreRespDTO();
        mtCouponPreRespDTO.setDealId(0);
        mtCouponPreRespDTO.setDealTitle("dealTitle");
        mtCouponPreRespDTO.setCouponType(0);
        mtCouponPreRespDTO.setGroupBuyType(0);
        mtCouponPreRespDTO.setItemId("itemId");
        mtCouponPreRespDTO.setSkuId("itemId");
        mtCouponPreRespDTO.setItemGuid("itemGuid");
        mtCouponPreRespDTO.setSkuGuid("skuGuid");
        mtCouponPreRespDTO.setNewActivityGuid("activityGuid");
        mtCouponPreRespDTO.setActivityGuid("activityGuid");
        mtCouponPreRespDTO.setIsThirdShare((byte) 0b0);
        mtCouponPreRespDTO.setIsActivityShare((byte) 0b0);
        mtCouponPreRespDTO.setUseLimit(0);
        final List<MtCouponPreRespDTO> mtCouponPreRespDTOS = Arrays.asList(mtCouponPreRespDTO);
        final CouPonPreReqDTO couPonPreReqDTO1 = new CouPonPreReqDTO();
        couPonPreReqDTO1.setCouponCode("couponCode");
        couPonPreReqDTO1.setErpOrderId("orderGuid");
        couPonPreReqDTO1.setActivityGuid("activityGuid");
        couPonPreReqDTO1.setUsedGuidList(Arrays.asList("value"));
        couPonPreReqDTO1.setGroupBuyType(0);
        when(mockGroupClientService.couponPrepare(couPonPreReqDTO1)).thenReturn(mtCouponPreRespDTOS);

        // Configure DineInOrderClientService.batchGetTableInfo(...).
        final OrderTableInfoDTO orderTableInfoDTO = new OrderTableInfoDTO();
        orderTableInfoDTO.setGuid("cd14f6e0-98fa-4044-a52b-675ffd5bd65f");
        orderTableInfoDTO.setTradeMode(0);
        orderTableInfoDTO.setOrderFee(new BigDecimal("0.00"));
        orderTableInfoDTO.setState(0);
        orderTableInfoDTO.setStateName("stateName");
        final List<OrderTableInfoDTO> orderTableInfoDTOS = Arrays.asList(orderTableInfoDTO);
        when(mockDineInOrderClientService.batchGetTableInfo(new OrderGuidsDTO(Arrays.asList("value"))))
                .thenReturn(orderTableInfoDTOS);

        // Configure ThirdActivityClientService.getByThirdCode(...).
        final ThirdActivityRespDTO thirdActivityRespDTO = new ThirdActivityRespDTO();
        thirdActivityRespDTO.setGuid("activityGuid");
        thirdActivityRespDTO.setRuleType((byte) 0b0);
        thirdActivityRespDTO.setUseLimit(0);
        thirdActivityRespDTO.setIsActivityShare((byte) 0b0);
        thirdActivityRespDTO.setIsThirdShare((byte) 0b0);
        thirdActivityRespDTO.setItemGuid("itemGuid");
        thirdActivityRespDTO.setSkuGuid("skuGuid");
        when(mockThirdActivityClientService.getByThirdCode("itemId", "thirdType", "dealTitle"))
                .thenReturn(thirdActivityRespDTO);

        // Configure TradeThirdActivityClientService.listThirdActivityByOrderGuid(...).
        final ThirdActivityRecordDTO thirdActivityRecordDTO = new ThirdActivityRecordDTO();
        thirdActivityRecordDTO.setGuid("fdf6d577-07f5-406c-a0fe-904c3f816630");
        thirdActivityRecordDTO.setOrderGuid("orderGuid");
        thirdActivityRecordDTO.setActivityGuid("activityGuid");
        thirdActivityRecordDTO.setRuleType(0);
        thirdActivityRecordDTO.setIsThirdShare((byte) 0b0);
        final List<ThirdActivityRecordDTO> thirdActivityRecordDTOS = Arrays.asList(thirdActivityRecordDTO);
        when(mockTradeThirdActivityClientService.listThirdActivityByOrderGuid("orderGuid"))
                .thenReturn(thirdActivityRecordDTOS);

        // Configure GrouponClientService.list(...).
        final GrouponListRespDTO grouponListRespDTO = new GrouponListRespDTO();
        grouponListRespDTO.setOrderGuid("orderGuid");
        grouponListRespDTO.setAmount(new BigDecimal("0.00"));
        grouponListRespDTO.setName("name");
        grouponListRespDTO.setCode("code");
        grouponListRespDTO.setCouponType(0);
        final List<GrouponListRespDTO> grouponListRespDTOS = Arrays.asList(grouponListRespDTO);
        when(mockGrouponClientService.list("orderGuid", 0)).thenReturn(grouponListRespDTOS);

        // Run the test
        groupBuyServiceImplUnderTest.preCheck(couPonPreReqDTO);
    }

    @Test(expected = BusinessException.class)
    public void testPreCheck_GroupClientServiceReturnsNoItems() {
        // Setup
        final CouPonPreReqDTO couPonPreReqDTO = new CouPonPreReqDTO();
        couPonPreReqDTO.setCouponCode("couponCode");
        couPonPreReqDTO.setErpOrderId("orderGuid");
        couPonPreReqDTO.setActivityGuid("activityGuid");
        couPonPreReqDTO.setUsedGuidList(Arrays.asList("value"));
        couPonPreReqDTO.setGroupBuyType(0);

        // Configure GroupClientService.couponPrepare(...).
        final CouPonPreReqDTO couPonPreReqDTO1 = new CouPonPreReqDTO();
        couPonPreReqDTO1.setCouponCode("couponCode");
        couPonPreReqDTO1.setErpOrderId("orderGuid");
        couPonPreReqDTO1.setActivityGuid("activityGuid");
        couPonPreReqDTO1.setUsedGuidList(Arrays.asList("value"));
        couPonPreReqDTO1.setGroupBuyType(0);
        when(mockGroupClientService.couponPrepare(couPonPreReqDTO1)).thenReturn(Collections.emptyList());

        // Run the test
        groupBuyServiceImplUnderTest.preCheck(couPonPreReqDTO);
    }

    @Test(expected = BusinessException.class)
    public void testPreCheck_DineInOrderClientServiceReturnsNoItems() {
        // Setup
        final CouPonPreReqDTO couPonPreReqDTO = new CouPonPreReqDTO();
        couPonPreReqDTO.setCouponCode("couponCode");
        couPonPreReqDTO.setErpOrderId("orderGuid");
        couPonPreReqDTO.setActivityGuid("activityGuid");
        couPonPreReqDTO.setUsedGuidList(Arrays.asList("value"));
        couPonPreReqDTO.setGroupBuyType(0);

        // Configure GroupClientService.couponPrepare(...).
        final MtCouponPreRespDTO mtCouponPreRespDTO = new MtCouponPreRespDTO();
        mtCouponPreRespDTO.setDealId(0);
        mtCouponPreRespDTO.setDealTitle("dealTitle");
        mtCouponPreRespDTO.setCouponType(0);
        mtCouponPreRespDTO.setGroupBuyType(0);
        mtCouponPreRespDTO.setItemId("itemId");
        mtCouponPreRespDTO.setSkuId("itemId");
        mtCouponPreRespDTO.setItemGuid("itemGuid");
        mtCouponPreRespDTO.setSkuGuid("skuGuid");
        mtCouponPreRespDTO.setNewActivityGuid("activityGuid");
        mtCouponPreRespDTO.setActivityGuid("activityGuid");
        mtCouponPreRespDTO.setIsThirdShare((byte) 0b0);
        mtCouponPreRespDTO.setIsActivityShare((byte) 0b0);
        mtCouponPreRespDTO.setUseLimit(0);
        final List<MtCouponPreRespDTO> mtCouponPreRespDTOS = Arrays.asList(mtCouponPreRespDTO);
        final CouPonPreReqDTO couPonPreReqDTO1 = new CouPonPreReqDTO();
        couPonPreReqDTO1.setCouponCode("couponCode");
        couPonPreReqDTO1.setErpOrderId("orderGuid");
        couPonPreReqDTO1.setActivityGuid("activityGuid");
        couPonPreReqDTO1.setUsedGuidList(Arrays.asList("value"));
        couPonPreReqDTO1.setGroupBuyType(0);
        when(mockGroupClientService.couponPrepare(couPonPreReqDTO1)).thenReturn(mtCouponPreRespDTOS);

        when(mockDineInOrderClientService.batchGetTableInfo(new OrderGuidsDTO(Arrays.asList("value"))))
                .thenReturn(Collections.emptyList());

        // Configure ThirdActivityClientService.getByThirdCode(...).
        final ThirdActivityRespDTO thirdActivityRespDTO = new ThirdActivityRespDTO();
        thirdActivityRespDTO.setGuid("activityGuid");
        thirdActivityRespDTO.setRuleType((byte) 0b0);
        thirdActivityRespDTO.setUseLimit(0);
        thirdActivityRespDTO.setIsActivityShare((byte) 0b0);
        thirdActivityRespDTO.setIsThirdShare((byte) 0b0);
        thirdActivityRespDTO.setItemGuid("itemGuid");
        thirdActivityRespDTO.setSkuGuid("skuGuid");
        when(mockThirdActivityClientService.getByThirdCode("itemId", "thirdType", "dealTitle"))
                .thenReturn(thirdActivityRespDTO);

        // Configure TradeThirdActivityClientService.listThirdActivityByOrderGuid(...).
        final ThirdActivityRecordDTO thirdActivityRecordDTO = new ThirdActivityRecordDTO();
        thirdActivityRecordDTO.setGuid("fdf6d577-07f5-406c-a0fe-904c3f816630");
        thirdActivityRecordDTO.setOrderGuid("orderGuid");
        thirdActivityRecordDTO.setActivityGuid("activityGuid");
        thirdActivityRecordDTO.setRuleType(0);
        thirdActivityRecordDTO.setIsThirdShare((byte) 0b0);
        final List<ThirdActivityRecordDTO> thirdActivityRecordDTOS = Arrays.asList(thirdActivityRecordDTO);
        when(mockTradeThirdActivityClientService.listThirdActivityByOrderGuid("orderGuid"))
                .thenReturn(thirdActivityRecordDTOS);

        // Configure GrouponClientService.list(...).
        final GrouponListRespDTO grouponListRespDTO = new GrouponListRespDTO();
        grouponListRespDTO.setOrderGuid("orderGuid");
        grouponListRespDTO.setAmount(new BigDecimal("0.00"));
        grouponListRespDTO.setName("name");
        grouponListRespDTO.setCode("code");
        grouponListRespDTO.setCouponType(0);
        final List<GrouponListRespDTO> grouponListRespDTOS = Arrays.asList(grouponListRespDTO);
        when(mockGrouponClientService.list("orderGuid", 0)).thenReturn(grouponListRespDTOS);

        // Run the test
        groupBuyServiceImplUnderTest.preCheck(couPonPreReqDTO);
    }

    @Test(expected = BusinessException.class)
    public void testPreCheck_TradeThirdActivityClientServiceReturnsNoItems() {
        // Setup
        final CouPonPreReqDTO couPonPreReqDTO = new CouPonPreReqDTO();
        couPonPreReqDTO.setCouponCode("couponCode");
        couPonPreReqDTO.setErpOrderId("orderGuid");
        couPonPreReqDTO.setActivityGuid("activityGuid");
        couPonPreReqDTO.setUsedGuidList(Arrays.asList("value"));
        couPonPreReqDTO.setGroupBuyType(0);

        // Configure GroupClientService.couponPrepare(...).
        final MtCouponPreRespDTO mtCouponPreRespDTO = new MtCouponPreRespDTO();
        mtCouponPreRespDTO.setDealId(0);
        mtCouponPreRespDTO.setDealTitle("dealTitle");
        mtCouponPreRespDTO.setCouponType(0);
        mtCouponPreRespDTO.setGroupBuyType(0);
        mtCouponPreRespDTO.setItemId("itemId");
        mtCouponPreRespDTO.setSkuId("itemId");
        mtCouponPreRespDTO.setItemGuid("itemGuid");
        mtCouponPreRespDTO.setSkuGuid("skuGuid");
        mtCouponPreRespDTO.setNewActivityGuid("activityGuid");
        mtCouponPreRespDTO.setActivityGuid("activityGuid");
        mtCouponPreRespDTO.setIsThirdShare((byte) 0b0);
        mtCouponPreRespDTO.setIsActivityShare((byte) 0b0);
        mtCouponPreRespDTO.setUseLimit(0);
        final List<MtCouponPreRespDTO> mtCouponPreRespDTOS = Arrays.asList(mtCouponPreRespDTO);
        final CouPonPreReqDTO couPonPreReqDTO1 = new CouPonPreReqDTO();
        couPonPreReqDTO1.setCouponCode("couponCode");
        couPonPreReqDTO1.setErpOrderId("orderGuid");
        couPonPreReqDTO1.setActivityGuid("activityGuid");
        couPonPreReqDTO1.setUsedGuidList(Arrays.asList("value"));
        couPonPreReqDTO1.setGroupBuyType(0);
        when(mockGroupClientService.couponPrepare(couPonPreReqDTO1)).thenReturn(mtCouponPreRespDTOS);

        // Configure DineInOrderClientService.batchGetTableInfo(...).
        final OrderTableInfoDTO orderTableInfoDTO = new OrderTableInfoDTO();
        orderTableInfoDTO.setGuid("cd14f6e0-98fa-4044-a52b-675ffd5bd65f");
        orderTableInfoDTO.setTradeMode(0);
        orderTableInfoDTO.setOrderFee(new BigDecimal("0.00"));
        orderTableInfoDTO.setState(0);
        orderTableInfoDTO.setStateName("stateName");
        final List<OrderTableInfoDTO> orderTableInfoDTOS = Arrays.asList(orderTableInfoDTO);
        when(mockDineInOrderClientService.batchGetTableInfo(new OrderGuidsDTO(Arrays.asList("value"))))
                .thenReturn(orderTableInfoDTOS);

        // Configure ThirdActivityClientService.getByThirdCode(...).
        final ThirdActivityRespDTO thirdActivityRespDTO = new ThirdActivityRespDTO();
        thirdActivityRespDTO.setGuid("activityGuid");
        thirdActivityRespDTO.setRuleType((byte) 0b0);
        thirdActivityRespDTO.setUseLimit(0);
        thirdActivityRespDTO.setIsActivityShare((byte) 0b0);
        thirdActivityRespDTO.setIsThirdShare((byte) 0b0);
        thirdActivityRespDTO.setItemGuid("itemGuid");
        thirdActivityRespDTO.setSkuGuid("skuGuid");
        when(mockThirdActivityClientService.getByThirdCode("itemId", "thirdType", "dealTitle"))
                .thenReturn(thirdActivityRespDTO);

        when(mockTradeThirdActivityClientService.listThirdActivityByOrderGuid("orderGuid"))
                .thenReturn(Collections.emptyList());

        // Configure GrouponClientService.list(...).
        final GrouponListRespDTO grouponListRespDTO = new GrouponListRespDTO();
        grouponListRespDTO.setOrderGuid("orderGuid");
        grouponListRespDTO.setAmount(new BigDecimal("0.00"));
        grouponListRespDTO.setName("name");
        grouponListRespDTO.setCode("code");
        grouponListRespDTO.setCouponType(0);
        final List<GrouponListRespDTO> grouponListRespDTOS = Arrays.asList(grouponListRespDTO);
        when(mockGrouponClientService.list("orderGuid", 0)).thenReturn(grouponListRespDTOS);

        // Run the test
        groupBuyServiceImplUnderTest.preCheck(couPonPreReqDTO);
    }

    @Test
    public void testPreCheck_GrouponClientServiceReturnsNoItems() {
        // Setup
        final CouPonPreReqDTO couPonPreReqDTO = new CouPonPreReqDTO();
        couPonPreReqDTO.setCouponCode("couponCode");
        couPonPreReqDTO.setErpOrderId("orderGuid");
        couPonPreReqDTO.setActivityGuid("activityGuid");
        couPonPreReqDTO.setUsedGuidList(Arrays.asList("value"));
        couPonPreReqDTO.setGroupBuyType(0);

        final MtCouponPreRespDTO mtCouponPreRespDTO = new MtCouponPreRespDTO();
        mtCouponPreRespDTO.setDealId(0);
        mtCouponPreRespDTO.setDealTitle("dealTitle");
        mtCouponPreRespDTO.setCouponType(0);
        mtCouponPreRespDTO.setGroupBuyType(0);
        mtCouponPreRespDTO.setItemId("itemId");
        mtCouponPreRespDTO.setSkuId("itemId");
        mtCouponPreRespDTO.setItemGuid("itemGuid");
        mtCouponPreRespDTO.setSkuGuid("skuGuid");
        mtCouponPreRespDTO.setNewActivityGuid("activityGuid");
        mtCouponPreRespDTO.setActivityGuid("activityGuid");
        mtCouponPreRespDTO.setIsThirdShare((byte) 0b0);
        mtCouponPreRespDTO.setIsActivityShare((byte) 0b0);
        mtCouponPreRespDTO.setUseLimit(0);
        final List<MtCouponPreRespDTO> expectedResult = Arrays.asList(mtCouponPreRespDTO);

        // Configure GroupClientService.couponPrepare(...).
        final MtCouponPreRespDTO mtCouponPreRespDTO1 = new MtCouponPreRespDTO();
        mtCouponPreRespDTO1.setDealId(0);
        mtCouponPreRespDTO1.setDealTitle("dealTitle");
        mtCouponPreRespDTO1.setCouponType(0);
        mtCouponPreRespDTO1.setGroupBuyType(0);
        mtCouponPreRespDTO1.setItemId("itemId");
        mtCouponPreRespDTO1.setSkuId("itemId");
        mtCouponPreRespDTO1.setItemGuid("itemGuid");
        mtCouponPreRespDTO1.setSkuGuid("skuGuid");
        mtCouponPreRespDTO1.setNewActivityGuid("activityGuid");
        mtCouponPreRespDTO1.setActivityGuid("activityGuid");
        mtCouponPreRespDTO1.setIsThirdShare((byte) 0b0);
        mtCouponPreRespDTO1.setIsActivityShare((byte) 0b0);
        mtCouponPreRespDTO1.setUseLimit(0);
        final List<MtCouponPreRespDTO> mtCouponPreRespDTOS = Arrays.asList(mtCouponPreRespDTO1);
        final CouPonPreReqDTO couPonPreReqDTO1 = new CouPonPreReqDTO();
        couPonPreReqDTO1.setCouponCode("couponCode");
        couPonPreReqDTO1.setErpOrderId("orderGuid");
        couPonPreReqDTO1.setActivityGuid("activityGuid");
        couPonPreReqDTO1.setUsedGuidList(Arrays.asList("value"));
        couPonPreReqDTO1.setGroupBuyType(0);
        when(mockGroupClientService.couponPrepare(couPonPreReqDTO1)).thenReturn(mtCouponPreRespDTOS);

        // Configure DineInOrderClientService.batchGetTableInfo(...).
        final OrderTableInfoDTO orderTableInfoDTO = new OrderTableInfoDTO();
        orderTableInfoDTO.setGuid("cd14f6e0-98fa-4044-a52b-675ffd5bd65f");
        orderTableInfoDTO.setTradeMode(0);
        orderTableInfoDTO.setOrderFee(new BigDecimal("0.00"));
        orderTableInfoDTO.setState(0);
        orderTableInfoDTO.setStateName("stateName");
        final List<OrderTableInfoDTO> orderTableInfoDTOS = Arrays.asList(orderTableInfoDTO);
        when(mockDineInOrderClientService.batchGetTableInfo(new OrderGuidsDTO(Arrays.asList("value"))))
                .thenReturn(orderTableInfoDTOS);

        // Configure ThirdActivityClientService.getByThirdCode(...).
        final ThirdActivityRespDTO thirdActivityRespDTO = new ThirdActivityRespDTO();
        thirdActivityRespDTO.setGuid("activityGuid");
        thirdActivityRespDTO.setRuleType((byte) 0b0);
        thirdActivityRespDTO.setUseLimit(0);
        thirdActivityRespDTO.setIsActivityShare((byte) 0b0);
        thirdActivityRespDTO.setIsThirdShare((byte) 0b0);
        thirdActivityRespDTO.setItemGuid("itemGuid");
        thirdActivityRespDTO.setSkuGuid("skuGuid");
        when(mockThirdActivityClientService.getByThirdCode("itemId", "thirdType", "dealTitle"))
                .thenReturn(thirdActivityRespDTO);

        // Configure TradeThirdActivityClientService.listThirdActivityByOrderGuid(...).
        final ThirdActivityRecordDTO thirdActivityRecordDTO = new ThirdActivityRecordDTO();
        thirdActivityRecordDTO.setGuid("fdf6d577-07f5-406c-a0fe-904c3f816630");
        thirdActivityRecordDTO.setOrderGuid("orderGuid");
        thirdActivityRecordDTO.setActivityGuid("activityGuid");
        thirdActivityRecordDTO.setRuleType(0);
        thirdActivityRecordDTO.setIsThirdShare((byte) 0b0);
        final List<ThirdActivityRecordDTO> thirdActivityRecordDTOS = Arrays.asList(thirdActivityRecordDTO);
        when(mockTradeThirdActivityClientService.listThirdActivityByOrderGuid("orderGuid"))
                .thenReturn(thirdActivityRecordDTOS);

        when(mockGrouponClientService.list("orderGuid", 0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<MtCouponPreRespDTO> result = groupBuyServiceImplUnderTest.preCheck(couPonPreReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = BusinessException.class)
    public void testPreCheckQueryItem_ThrowsBusinessException() {
        // Setup
        final CouPonPreReqDTO couPonPreReqDTO = new CouPonPreReqDTO();
        couPonPreReqDTO.setCouponCode("couponCode");
        couPonPreReqDTO.setErpOrderId("orderGuid");
        couPonPreReqDTO.setActivityGuid("activityGuid");
        couPonPreReqDTO.setUsedGuidList(Arrays.asList("value"));
        couPonPreReqDTO.setGroupBuyType(0);

        // Configure GroupClientService.couponPrepare(...).
        final MtCouponPreRespDTO mtCouponPreRespDTO = new MtCouponPreRespDTO();
        mtCouponPreRespDTO.setDealId(0);
        mtCouponPreRespDTO.setDealTitle("dealTitle");
        mtCouponPreRespDTO.setCouponType(0);
        mtCouponPreRespDTO.setGroupBuyType(0);
        mtCouponPreRespDTO.setItemId("itemId");
        mtCouponPreRespDTO.setSkuId("itemId");
        mtCouponPreRespDTO.setItemGuid("itemGuid");
        mtCouponPreRespDTO.setSkuGuid("skuGuid");
        mtCouponPreRespDTO.setNewActivityGuid("activityGuid");
        mtCouponPreRespDTO.setActivityGuid("activityGuid");
        mtCouponPreRespDTO.setIsThirdShare((byte) 0b0);
        mtCouponPreRespDTO.setIsActivityShare((byte) 0b0);
        mtCouponPreRespDTO.setUseLimit(0);
        final List<MtCouponPreRespDTO> mtCouponPreRespDTOS = Arrays.asList(mtCouponPreRespDTO);
        final CouPonPreReqDTO couPonPreReqDTO1 = new CouPonPreReqDTO();
        couPonPreReqDTO1.setCouponCode("couponCode");
        couPonPreReqDTO1.setErpOrderId("orderGuid");
        couPonPreReqDTO1.setActivityGuid("activityGuid");
        couPonPreReqDTO1.setUsedGuidList(Arrays.asList("value"));
        couPonPreReqDTO1.setGroupBuyType(0);
        when(mockGroupClientService.couponPrepare(couPonPreReqDTO1)).thenReturn(mtCouponPreRespDTOS);

        // Configure ThirdActivityClientService.getByThirdCode(...).
        final ThirdActivityRespDTO thirdActivityRespDTO = new ThirdActivityRespDTO();
        thirdActivityRespDTO.setGuid("activityGuid");
        thirdActivityRespDTO.setRuleType((byte) 0b0);
        thirdActivityRespDTO.setUseLimit(0);
        thirdActivityRespDTO.setIsActivityShare((byte) 0b0);
        thirdActivityRespDTO.setIsThirdShare((byte) 0b0);
        thirdActivityRespDTO.setItemGuid("itemGuid");
        thirdActivityRespDTO.setSkuGuid("skuGuid");
        when(mockThirdActivityClientService.getByThirdCode("itemId", "thirdType", "dealTitle"))
                .thenReturn(thirdActivityRespDTO);

        when(mockItemClientService.getSubItemGuid("itemGuid")).thenReturn("result");

        // Configure TradeThirdActivityClientService.listThirdActivityByOrderGuid(...).
        final ThirdActivityRecordDTO thirdActivityRecordDTO = new ThirdActivityRecordDTO();
        thirdActivityRecordDTO.setGuid("fdf6d577-07f5-406c-a0fe-904c3f816630");
        thirdActivityRecordDTO.setOrderGuid("orderGuid");
        thirdActivityRecordDTO.setActivityGuid("activityGuid");
        thirdActivityRecordDTO.setRuleType(0);
        thirdActivityRecordDTO.setIsThirdShare((byte) 0b0);
        final List<ThirdActivityRecordDTO> thirdActivityRecordDTOS = Arrays.asList(thirdActivityRecordDTO);
        when(mockTradeThirdActivityClientService.listThirdActivityByOrderGuid("orderGuid"))
                .thenReturn(thirdActivityRecordDTOS);

        // Configure GrouponClientService.list(...).
        final GrouponListRespDTO grouponListRespDTO = new GrouponListRespDTO();
        grouponListRespDTO.setOrderGuid("orderGuid");
        grouponListRespDTO.setAmount(new BigDecimal("0.00"));
        grouponListRespDTO.setName("name");
        grouponListRespDTO.setCode("code");
        grouponListRespDTO.setCouponType(0);
        final List<GrouponListRespDTO> grouponListRespDTOS = Arrays.asList(grouponListRespDTO);
        when(mockGrouponClientService.list("orderGuid", 0)).thenReturn(grouponListRespDTOS);

        // Run the test
        groupBuyServiceImplUnderTest.preCheckQueryItem(couPonPreReqDTO);
    }

    @Test(expected = BusinessException.class)
    public void testPreCheckQueryItem_GroupClientServiceReturnsNoItems() {
        // Setup
        final CouPonPreReqDTO couPonPreReqDTO = new CouPonPreReqDTO();
        couPonPreReqDTO.setCouponCode("couponCode");
        couPonPreReqDTO.setErpOrderId("orderGuid");
        couPonPreReqDTO.setActivityGuid("activityGuid");
        couPonPreReqDTO.setUsedGuidList(Arrays.asList("value"));
        couPonPreReqDTO.setGroupBuyType(0);

        // Configure GroupClientService.couponPrepare(...).
        final CouPonPreReqDTO couPonPreReqDTO1 = new CouPonPreReqDTO();
        couPonPreReqDTO1.setCouponCode("couponCode");
        couPonPreReqDTO1.setErpOrderId("orderGuid");
        couPonPreReqDTO1.setActivityGuid("activityGuid");
        couPonPreReqDTO1.setUsedGuidList(Arrays.asList("value"));
        couPonPreReqDTO1.setGroupBuyType(0);
        when(mockGroupClientService.couponPrepare(couPonPreReqDTO1)).thenReturn(Collections.emptyList());

        // Run the test
        groupBuyServiceImplUnderTest.preCheckQueryItem(couPonPreReqDTO);
    }

    @Test(expected = BusinessException.class)
    public void testPreCheckQueryItem_TradeThirdActivityClientServiceReturnsNoItems() {
        // Setup
        final CouPonPreReqDTO couPonPreReqDTO = new CouPonPreReqDTO();
        couPonPreReqDTO.setCouponCode("couponCode");
        couPonPreReqDTO.setErpOrderId("orderGuid");
        couPonPreReqDTO.setActivityGuid("activityGuid");
        couPonPreReqDTO.setUsedGuidList(Arrays.asList("value"));
        couPonPreReqDTO.setGroupBuyType(0);

        // Configure GroupClientService.couponPrepare(...).
        final MtCouponPreRespDTO mtCouponPreRespDTO = new MtCouponPreRespDTO();
        mtCouponPreRespDTO.setDealId(0);
        mtCouponPreRespDTO.setDealTitle("dealTitle");
        mtCouponPreRespDTO.setCouponType(0);
        mtCouponPreRespDTO.setGroupBuyType(0);
        mtCouponPreRespDTO.setItemId("itemId");
        mtCouponPreRespDTO.setSkuId("itemId");
        mtCouponPreRespDTO.setItemGuid("itemGuid");
        mtCouponPreRespDTO.setSkuGuid("skuGuid");
        mtCouponPreRespDTO.setNewActivityGuid("activityGuid");
        mtCouponPreRespDTO.setActivityGuid("activityGuid");
        mtCouponPreRespDTO.setIsThirdShare((byte) 0b0);
        mtCouponPreRespDTO.setIsActivityShare((byte) 0b0);
        mtCouponPreRespDTO.setUseLimit(0);
        final List<MtCouponPreRespDTO> mtCouponPreRespDTOS = Arrays.asList(mtCouponPreRespDTO);
        final CouPonPreReqDTO couPonPreReqDTO1 = new CouPonPreReqDTO();
        couPonPreReqDTO1.setCouponCode("couponCode");
        couPonPreReqDTO1.setErpOrderId("orderGuid");
        couPonPreReqDTO1.setActivityGuid("activityGuid");
        couPonPreReqDTO1.setUsedGuidList(Arrays.asList("value"));
        couPonPreReqDTO1.setGroupBuyType(0);
        when(mockGroupClientService.couponPrepare(couPonPreReqDTO1)).thenReturn(mtCouponPreRespDTOS);

        // Configure ThirdActivityClientService.getByThirdCode(...).
        final ThirdActivityRespDTO thirdActivityRespDTO = new ThirdActivityRespDTO();
        thirdActivityRespDTO.setGuid("activityGuid");
        thirdActivityRespDTO.setRuleType((byte) 0b0);
        thirdActivityRespDTO.setUseLimit(0);
        thirdActivityRespDTO.setIsActivityShare((byte) 0b0);
        thirdActivityRespDTO.setIsThirdShare((byte) 0b0);
        thirdActivityRespDTO.setItemGuid("itemGuid");
        thirdActivityRespDTO.setSkuGuid("skuGuid");
        when(mockThirdActivityClientService.getByThirdCode("itemId", "thirdType", "dealTitle"))
                .thenReturn(thirdActivityRespDTO);

        when(mockItemClientService.getSubItemGuid("itemGuid")).thenReturn("result");
        when(mockTradeThirdActivityClientService.listThirdActivityByOrderGuid("orderGuid"))
                .thenReturn(Collections.emptyList());

        // Configure GrouponClientService.list(...).
        final GrouponListRespDTO grouponListRespDTO = new GrouponListRespDTO();
        grouponListRespDTO.setOrderGuid("orderGuid");
        grouponListRespDTO.setAmount(new BigDecimal("0.00"));
        grouponListRespDTO.setName("name");
        grouponListRespDTO.setCode("code");
        grouponListRespDTO.setCouponType(0);
        final List<GrouponListRespDTO> grouponListRespDTOS = Arrays.asList(grouponListRespDTO);
        when(mockGrouponClientService.list("orderGuid", 0)).thenReturn(grouponListRespDTOS);

        // Run the test
        groupBuyServiceImplUnderTest.preCheckQueryItem(couPonPreReqDTO);
    }

    @Test
    public void testPreCheckQueryItem_GrouponClientServiceReturnsNoItems() {
        // Setup
        final CouPonPreReqDTO couPonPreReqDTO = new CouPonPreReqDTO();
        couPonPreReqDTO.setCouponCode("couponCode");
        couPonPreReqDTO.setErpOrderId("orderGuid");
        couPonPreReqDTO.setActivityGuid("activityGuid");
        couPonPreReqDTO.setUsedGuidList(Arrays.asList("value"));
        couPonPreReqDTO.setGroupBuyType(0);

        final MtCouponPreRespDTO mtCouponPreRespDTO = new MtCouponPreRespDTO();
        mtCouponPreRespDTO.setDealId(0);
        mtCouponPreRespDTO.setDealTitle("dealTitle");
        mtCouponPreRespDTO.setCouponType(0);
        mtCouponPreRespDTO.setGroupBuyType(0);
        mtCouponPreRespDTO.setItemId("itemId");
        mtCouponPreRespDTO.setSkuId("itemId");
        mtCouponPreRespDTO.setItemGuid("itemGuid");
        mtCouponPreRespDTO.setSkuGuid("skuGuid");
        mtCouponPreRespDTO.setNewActivityGuid("activityGuid");
        mtCouponPreRespDTO.setActivityGuid("activityGuid");
        mtCouponPreRespDTO.setIsThirdShare((byte) 0b0);
        mtCouponPreRespDTO.setIsActivityShare((byte) 0b0);
        mtCouponPreRespDTO.setUseLimit(0);
        final List<MtCouponPreRespDTO> expectedResult = Arrays.asList(mtCouponPreRespDTO);

        // Configure GroupClientService.couponPrepare(...).
        final MtCouponPreRespDTO mtCouponPreRespDTO1 = new MtCouponPreRespDTO();
        mtCouponPreRespDTO1.setDealId(0);
        mtCouponPreRespDTO1.setDealTitle("dealTitle");
        mtCouponPreRespDTO1.setCouponType(0);
        mtCouponPreRespDTO1.setGroupBuyType(0);
        mtCouponPreRespDTO1.setItemId("itemId");
        mtCouponPreRespDTO1.setSkuId("itemId");
        mtCouponPreRespDTO1.setItemGuid("itemGuid");
        mtCouponPreRespDTO1.setSkuGuid("skuGuid");
        mtCouponPreRespDTO1.setNewActivityGuid("activityGuid");
        mtCouponPreRespDTO1.setActivityGuid("activityGuid");
        mtCouponPreRespDTO1.setIsThirdShare((byte) 0b0);
        mtCouponPreRespDTO1.setIsActivityShare((byte) 0b0);
        mtCouponPreRespDTO1.setUseLimit(0);
        final List<MtCouponPreRespDTO> mtCouponPreRespDTOS = Arrays.asList(mtCouponPreRespDTO1);
        final CouPonPreReqDTO couPonPreReqDTO1 = new CouPonPreReqDTO();
        couPonPreReqDTO1.setCouponCode("couponCode");
        couPonPreReqDTO1.setErpOrderId("orderGuid");
        couPonPreReqDTO1.setActivityGuid("activityGuid");
        couPonPreReqDTO1.setUsedGuidList(Arrays.asList("value"));
        couPonPreReqDTO1.setGroupBuyType(0);
        when(mockGroupClientService.couponPrepare(couPonPreReqDTO1)).thenReturn(mtCouponPreRespDTOS);

        // Configure ThirdActivityClientService.getByThirdCode(...).
        final ThirdActivityRespDTO thirdActivityRespDTO = new ThirdActivityRespDTO();
        thirdActivityRespDTO.setGuid("activityGuid");
        thirdActivityRespDTO.setRuleType((byte) 0b0);
        thirdActivityRespDTO.setUseLimit(0);
        thirdActivityRespDTO.setIsActivityShare((byte) 0b0);
        thirdActivityRespDTO.setIsThirdShare((byte) 0b0);
        thirdActivityRespDTO.setItemGuid("itemGuid");
        thirdActivityRespDTO.setSkuGuid("skuGuid");
        when(mockThirdActivityClientService.getByThirdCode("itemId", "thirdType", "dealTitle"))
                .thenReturn(thirdActivityRespDTO);

        when(mockItemClientService.getSubItemGuid("itemGuid")).thenReturn("result");

        // Configure TradeThirdActivityClientService.listThirdActivityByOrderGuid(...).
        final ThirdActivityRecordDTO thirdActivityRecordDTO = new ThirdActivityRecordDTO();
        thirdActivityRecordDTO.setGuid("fdf6d577-07f5-406c-a0fe-904c3f816630");
        thirdActivityRecordDTO.setOrderGuid("orderGuid");
        thirdActivityRecordDTO.setActivityGuid("activityGuid");
        thirdActivityRecordDTO.setRuleType(0);
        thirdActivityRecordDTO.setIsThirdShare((byte) 0b0);
        final List<ThirdActivityRecordDTO> thirdActivityRecordDTOS = Arrays.asList(thirdActivityRecordDTO);
        when(mockTradeThirdActivityClientService.listThirdActivityByOrderGuid("orderGuid"))
                .thenReturn(thirdActivityRecordDTOS);

        when(mockGrouponClientService.list("orderGuid", 0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<MtCouponPreRespDTO> result = groupBuyServiceImplUnderTest.preCheckQueryItem(couPonPreReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
