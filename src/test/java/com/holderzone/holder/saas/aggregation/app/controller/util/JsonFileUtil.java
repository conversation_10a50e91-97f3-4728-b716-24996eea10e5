package com.holderzone.holder.saas.aggregation.app.controller.util;

import java.io.BufferedReader;
import java.io.InputStreamReader;

/**
 * <AUTHOR>
 */
public class JsonFileUtil {

    private JsonFileUtil() {
    }

    public static String read(String name) {
        try {
            BufferedReader bufferedReader = new BufferedReader(
                    new InputStreamReader(JsonFileUtil.class.getClassLoader().getResourceAsStream(name), "UTF-8"));
            StringBuilder stringBuilder = new StringBuilder();
            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                stringBuilder.append(line);
            }
            return stringBuilder.toString();
        } catch (Exception e) {
            return null;
        }
    }
}
