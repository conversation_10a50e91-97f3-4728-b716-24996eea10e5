package com.holderzone.holder.saas.aggregation.app.manage;

import com.holderzone.holder.saas.aggregation.app.service.feign.OrganizationClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.print.PrinterClient;
import com.holderzone.holder.saas.aggregation.app.service.feign.takeout.TakeoutClientService;
import com.holderzone.saas.store.dto.print.PrintOrderDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TakeoutManageTest {

    @Mock
    private TakeoutClientService mockTakeoutClientService;
    @Mock
    private PrinterClient mockPrinterClient;
    @Mock
    private OrganizationClientService mockOrganizationClientService;

    @InjectMocks
    private TakeoutManage takeoutManageUnderTest;

    @Test
    public void testDelayAutoAcceptOrder() {
        // Setup
        final TakeoutOrderDTO takeoutOrderDTO = new TakeoutOrderDTO();
        takeoutOrderDTO.setDeviceId("deviceId");
        takeoutOrderDTO.setShopId(0L);
        takeoutOrderDTO.setShopName("shopName");
        takeoutOrderDTO.setOrderGuid("orderGuid");
        takeoutOrderDTO.setStoreGuid("storeGuid");

        final PrintOrderDTO printOrderDTO = new PrintOrderDTO();
        printOrderDTO.setPrintKey("printKey");
        printOrderDTO.setBusinessType(0);
        printOrderDTO.setPrinterType(0);
        printOrderDTO.setPrinterIp("printerIp");
        printOrderDTO.setPrinterPort(0);
        final List<PrintOrderDTO> expectedResult = Arrays.asList(printOrderDTO);

        // Configure OrganizationClientService.getMasterDeviceByStoreGuid(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("storeName");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        when(mockOrganizationClientService.getMasterDeviceByStoreGuid("storeGuid")).thenReturn(storeDeviceDTO);

        // Configure PrinterClient.reprintTakeawayPrintOrderList(...).
        final PrintOrderDTO printOrderDTO1 = new PrintOrderDTO();
        printOrderDTO1.setPrintKey("printKey");
        printOrderDTO1.setBusinessType(0);
        printOrderDTO1.setPrinterType(0);
        printOrderDTO1.setPrinterIp("printerIp");
        printOrderDTO1.setPrinterPort(0);
        final List<PrintOrderDTO> printOrderDTOS = Arrays.asList(printOrderDTO1);
        when(mockPrinterClient.reprintTakeawayPrintOrderList("storeGuid")).thenReturn(printOrderDTOS);

        // Run the test
        final List<PrintOrderDTO> result = takeoutManageUnderTest.delayAutoAcceptOrder(takeoutOrderDTO);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm TakeoutClientService.delayAutoAcceptOrder(...).
        final TakeoutOrderDTO reqDTO = new TakeoutOrderDTO();
        reqDTO.setDeviceId("deviceId");
        reqDTO.setShopId(0L);
        reqDTO.setShopName("shopName");
        reqDTO.setOrderGuid("orderGuid");
        reqDTO.setStoreGuid("storeGuid");
        verify(mockTakeoutClientService).delayAutoAcceptOrder(reqDTO);
    }

    @Test
    public void testDelayAutoAcceptOrder_PrinterClientReturnsNoItems() {
        // Setup
        final TakeoutOrderDTO takeoutOrderDTO = new TakeoutOrderDTO();
        takeoutOrderDTO.setDeviceId("deviceId");
        takeoutOrderDTO.setShopId(0L);
        takeoutOrderDTO.setShopName("shopName");
        takeoutOrderDTO.setOrderGuid("orderGuid");
        takeoutOrderDTO.setStoreGuid("storeGuid");

        // Configure OrganizationClientService.getMasterDeviceByStoreGuid(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("storeName");
        storeDeviceDTO.setDeviceGuid("deviceGuid");
        when(mockOrganizationClientService.getMasterDeviceByStoreGuid("storeGuid")).thenReturn(storeDeviceDTO);

        when(mockPrinterClient.reprintTakeawayPrintOrderList("storeGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final List<PrintOrderDTO> result = takeoutManageUnderTest.delayAutoAcceptOrder(takeoutOrderDTO);

        // Verify the results
        assertEquals(Collections.emptyList(), result);

        // Confirm TakeoutClientService.delayAutoAcceptOrder(...).
        final TakeoutOrderDTO reqDTO = new TakeoutOrderDTO();
        reqDTO.setDeviceId("deviceId");
        reqDTO.setShopId(0L);
        reqDTO.setShopName("shopName");
        reqDTO.setOrderGuid("orderGuid");
        reqDTO.setStoreGuid("storeGuid");
        verify(mockTakeoutClientService).delayAutoAcceptOrder(reqDTO);
    }
}
