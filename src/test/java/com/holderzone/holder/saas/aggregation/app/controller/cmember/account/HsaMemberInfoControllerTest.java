package com.holderzone.holder.saas.aggregation.app.controller.cmember.account;

import com.alibaba.fastjson.JSON;
import com.holderzone.holder.saas.aggregation.app.HolderSaasStoreAggregationAppApplication;
import com.holderzone.holder.saas.aggregation.app.controller.util.JsonFileUtil;
import com.holderzone.holder.saas.aggregation.app.execption.FileIllegalException;
import com.holderzone.holder.saas.member.terminal.dto.card.RequestMemberCardRecharge;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestSaveCMemberDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestUpdateMemberDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestUpdatePassword;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.UnsupportedEncodingException;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @date 2023/10/31
 * @description
 */
@Slf4j
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@RunWith(SpringRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@SpringBootTest(classes = HolderSaasStoreAggregationAppApplication.class)
public class HsaMemberInfoControllerTest {

    private static final String USERINFO = "{\"operSubjectGuid\": \"2010121440477930009\"," +
            "\"enterpriseGuid\":" + " \"2009281531195930006\"," +
            "\"enterpriseName\": \"赵氏企业\"," +
            "\"enterpriseNo\": \"********\"," +
            "\"storeGuid\":" + " \"2106221850429620006\"," +
            "\"storeName\": \"交子大道测试门店\"," +
            "\"storeNo\": \"5796807\",\"userGuid\": " +
            "\"6787561298847596545\"," +
            "\"account\": \"196504\"," +
            "\"tel\": \"***********\"," +
            "\"name\": \"靓亮仔\"}\n";

    private static final String HSMCA_MEMBER = "/hsmca/member";

    private static final String RESPONSE = "response:";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    /**
     * 会员注册
     */
    @Test
    public void add() throws UnsupportedEncodingException {
        RequestSaveCMemberDTO reqAddDTO = JSON.parseObject(JsonFileUtil.read("cmember/account/add.json"),
                RequestSaveCMemberDTO.class);
        String jsonOverviewString = JSON.toJSONString(reqAddDTO);
        MvcResult mvcAddResult = null;
        try {
            mvcAddResult = mockMvc.perform(post(HSMCA_MEMBER + "/add")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonOverviewString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcAddResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void getMemberInfoAndCard() throws UnsupportedEncodingException {
        MvcResult mvcMemberInfoAndCardResult = null;
        try {
            mvcMemberInfoAndCardResult = mockMvc.perform(get(HSMCA_MEMBER + "/getMemberInfoAndCard?" +
                            "enterpriseGuid=4895" +
                            "&storeGuid=4908" +
                            "&phoneNumOrCardNum=18210311157" +
                            "&isCurrentStoreCard=1" +
                            "&deviceType=3" +
                            "&moduleType=3" +
                            "&loginType=1")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcMemberInfoAndCardResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void getAllSystemManagementCardByStore() throws UnsupportedEncodingException {
        MvcResult mvcAllSystemManagementCardByStoreResult = null;
        try {
            mvcAllSystemManagementCardByStoreResult = mockMvc.perform(get(HSMCA_MEMBER +
                            "/systemManagementCard?enterpriseGuid=4895" +
                            "&brandGuid=6977481831851491328" +
                            "&operSubjectGuid=2209191214425540006" +
                            "&deviceSourceType=1")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcAllSystemManagementCardByStoreResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void getMemberInfoAndCardByMemberInfoGuid() throws UnsupportedEncodingException {
        MvcResult mvcMemberInfoAndCardByMemberInfoGuidResult = null;
        try {
            mvcMemberInfoAndCardByMemberInfoGuidResult = mockMvc.perform(get(HSMCA_MEMBER +
                            "/getMemberInfoAndCardByMemberInfoGuid?" +
                            "memberInfoGuid=7085093153023197184")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcMemberInfoAndCardByMemberInfoGuidResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    /**
     * 会员副卡充值
     */
    @Test
    public void memberCardRechargeForEquityCard() throws UnsupportedEncodingException {
        RequestMemberCardRecharge reqMemberCardRechargeForEquityCardDTO = JSON.parseObject(
                JsonFileUtil.read("cmember/account/memberCardRechargeForEquityCard.json"),
                RequestMemberCardRecharge.class);
        String jsonMemberCardRechargeForEquityCardString = JSON.toJSONString(reqMemberCardRechargeForEquityCardDTO);
        MvcResult mvcMemberCardRechargeForEquityCardResult = null;
        try {
            mvcMemberCardRechargeForEquityCardResult = mockMvc.perform(post(HSMCA_MEMBER +
                            "/memberCardRechargeForEquityCard")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonMemberCardRechargeForEquityCardString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcMemberCardRechargeForEquityCardResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    /**
     * 修改会员基本信息
     */
    @Test
    public void updateMemberInfo() throws UnsupportedEncodingException {
        RequestUpdateMemberDTO reqUpdateMemberInfoDTO = JSON.parseObject(
                JsonFileUtil.read("cmember/account/updateMemberInfo.json"),
                RequestUpdateMemberDTO.class);
        String jsonUpdateMemberInfoString = JSON.toJSONString(reqUpdateMemberInfoDTO);
        MvcResult mvcUpdateMemberInfoResult = null;
        try {
            mvcUpdateMemberInfoResult = mockMvc.perform(put(HSMCA_MEMBER + "/7085093153023197184")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonUpdateMemberInfoString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcUpdateMemberInfoResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    /**
     * 发送修改密码的验证码(忘记原密码)
     */
    @Test
    public void sendUpdatePasswordCode() throws UnsupportedEncodingException {
        MvcResult mvcSendUpdatePasswordCodeResult = null;
        try {
            mvcSendUpdatePasswordCodeResult = mockMvc.perform(post(HSMCA_MEMBER + "/18281110085/pwdCode")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcSendUpdatePasswordCodeResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    /**
     * 修改会员支付密码
     */
    @Test
    public void updatePassword() throws UnsupportedEncodingException {
        RequestUpdatePassword reqUpdatePasswordDTO = JSON.parseObject(JsonFileUtil.read("cmember/account/updatePassword.json"),
                RequestUpdatePassword.class);
        String jsonUpdatePasswordString = JSON.toJSONString(reqUpdatePasswordDTO);
        MvcResult mvcUpdatePasswordResult = null;
        try {
            mvcUpdatePasswordResult = mockMvc.perform(post(HSMCA_MEMBER + "/updatePassword")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonUpdatePasswordString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcUpdatePasswordResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    /**
     * 支付密码校验
     */
    @Test
    public void payPwdCheckOut() throws UnsupportedEncodingException {
        MvcResult mvcPayPwdCheckOutResult = null;
        try {
            mvcPayPwdCheckOutResult = mockMvc.perform(get(HSMCA_MEMBER +
                            "/7085093153023197184/payPwd/checkout?payPwd=123456")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcPayPwdCheckOutResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }
}