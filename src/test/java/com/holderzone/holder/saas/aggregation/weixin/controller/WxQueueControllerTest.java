package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxQueueClientService;
import com.holderzone.saas.store.dto.queue.HolderQueueDTO;
import com.holderzone.saas.store.dto.queue.QueueWechatDTO;
import com.holderzone.saas.store.dto.queue.StoreConfigDTO;
import com.holderzone.saas.store.dto.weixin.WxInQueueReqDTO;
import com.holderzone.saas.store.dto.weixin.WxQueueDetailDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxInQueueRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxTotalQueueDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.HashMap;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(WxQueueController.class)
public class WxQueueControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxQueueClientService mockWxQueueClientService;

    @Test
    public void testGetQueueDetail() throws Exception {
        // Setup
        // Configure WxQueueClientService.getDetail(...).
        final StoreConfigDTO storeConfigDTO = new StoreConfigDTO();
        storeConfigDTO.setGuid("c1b641f4-07b3-4d90-b017-fb920bb41597");
        storeConfigDTO.setStoreGuid("storeGuid");
        storeConfigDTO.setIsEnableEat(false);
        storeConfigDTO.setIsEnableRecovery(false);
        storeConfigDTO.setRecoveryNum("recoveryNum");
        final WxQueueDetailDTO wxQueueDetailDTO = new WxQueueDetailDTO("brandGuid", "brandName", "brandLogo",
                storeConfigDTO, 0);
        when(mockWxQueueClientService.getDetail(
                new WxPortalReqDTO("enterpriseGuid", "appId", "storeGuid", "brandGuid", "msgKey", "url",
                        0,"","","",""))).thenReturn(wxQueueDetailDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_queue/detail")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testInQueue() throws Exception {
        // Setup
        when(mockWxQueueClientService.inQueue(new WxInQueueReqDTO(
                new WxStoreConsumerDTO("consumerGuid", "openId", "unionId", "nickName", "headImgUrl", 0, "country",
                        "province", "city", "", "", "phone", 0, "deviceId", "enterpriseGuid", "enterpriseName", "storeGuid",
                        "storeName", "userGuid", "userName", "account", 0, "diningTableGuid", "tableCode",
                        "diningTableName", "areaGuid", "areaName", "brandName", "brandGuid", "brandLogo", false,
                        "memberInfoGuid", "phoneNum", false, "operSubjectGuid", false), (byte) 0b0,
                "storeGuid"))).thenReturn(new WxInQueueRespDTO(0, "respMsg"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_queue/in_queue")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetTotalDetail() throws Exception {
        // Setup
        // Configure WxQueueClientService.getTotalDetail(...).
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("ec62f7fc-5c08-484e-9e60-7a313649e5ba");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setLogoUrl("logoUrl");
        holderQueueDTO.setIsEnableRecovery(false);
        final WxTotalQueueDTO wxTotalQueueDTO = new WxTotalQueueDTO("storeGuid", "storeName", "brandGuid", "brandName",
                "brandLogo", Arrays.asList(holderQueueDTO));
        when(mockWxQueueClientService.getTotalDetail(
                new WxPortalReqDTO("enterpriseGuid", "appId", "storeGuid", "brandGuid", "msgKey", "url",
                        0, "","","",""))).thenReturn(wxTotalQueueDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_queue/total_detail")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryQueueStatusNum() throws Exception {
        // Setup
        when(mockWxQueueClientService.queryQueueStatusNum(
                new QueueWechatDTO("brandGuid", "userGuid", (byte) 0b0, "enterpriseGuid"))).thenReturn(new HashMap<>());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_queue/groupingByStatus")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
