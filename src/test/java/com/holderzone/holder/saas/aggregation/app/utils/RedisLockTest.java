package com.holderzone.holder.saas.aggregation.app.utils;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RedisLockTest {

    @Mock
    private RedisTemplate<String, String> mockRedisTemplate;

    @InjectMocks
    private RedisLock redisLockUnderTest;

    @Test
    public void testLock1() {
        // Setup
        when(mockRedisTemplate.hasKey("key")).thenReturn(false);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final boolean result = redisLockUnderTest.lock("key");

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testLock1_RedisTemplateHasKeyReturnsNull() {
        // Setup
        when(mockRedisTemplate.hasKey("key")).thenReturn(null);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final boolean result = redisLockUnderTest.lock("key");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testLock1_RedisTemplateHasKeyReturnsTrue() {
        // Setup
        when(mockRedisTemplate.hasKey("key")).thenReturn(true);

        // Run the test
        final boolean result = redisLockUnderTest.lock("key");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testLock2() {
        // Setup
        when(mockRedisTemplate.hasKey("key")).thenReturn(false);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final boolean result = redisLockUnderTest.lock("storeGuid", "key");

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testLock2_RedisTemplateHasKeyReturnsNull() {
        // Setup
        when(mockRedisTemplate.hasKey("key")).thenReturn(null);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final boolean result = redisLockUnderTest.lock("storeGuid", "key");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testLock2_RedisTemplateHasKeyReturnsTrue() {
        // Setup
        when(mockRedisTemplate.hasKey("key")).thenReturn(true);

        // Run the test
        final boolean result = redisLockUnderTest.lock("storeGuid", "key");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testDelete() {
        // Setup
        when(mockRedisTemplate.delete("key")).thenReturn(false);

        // Run the test
        final boolean result = redisLockUnderTest.delete("key");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testDelete_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.delete("key")).thenReturn(null);

        // Run the test
        final boolean result = redisLockUnderTest.delete("key");

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testDelete_RedisTemplateReturnsTrue() {
        // Setup
        when(mockRedisTemplate.delete("key")).thenReturn(true);

        // Run the test
        final boolean result = redisLockUnderTest.delete("key");

        // Verify the results
        assertTrue(result);
    }
}
