package com.holderzone.holder.saas.aggregation.app.controller.trade;

import com.alibaba.fastjson.JSON;
import com.holderzone.holder.saas.aggregation.app.HolderSaasStoreAggregationAppApplication;
import com.holderzone.holder.saas.aggregation.app.controller.util.JsonFileUtil;
import com.holderzone.holder.saas.aggregation.app.execption.FileIllegalException;
import com.holderzone.saas.store.dto.trade.req.RecordThirdActivityInfoReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.UnsupportedEncodingException;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @date 2023/8/23 17:42
 * @description 一体机营业报表测试
 * 聚合层有自己的逻辑，因此重新测试
 */
@Slf4j
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@RunWith(SpringRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@SpringBootTest(classes = HolderSaasStoreAggregationAppApplication.class)
public class BusinessDailyControllerTest {

    private static final String USERINFO = "{\"operSubjectGuid\": \"2010121440477930009\",\"enterpriseGuid\":" +
            " \"2009281531195930006\",\"enterpriseName\": \"赵氏企业\",\"enterpriseNo\": \"********\",\"storeGuid\":" +
            " \"2106221850429620006\",\"storeName\": \"交子大道测试门店\",\"storeNo\": \"5796807\",\"userGuid\": " +
            "\"6787561298847596545\",\"account\": \"196504\",\"tel\": \"***********\",\"name\": \"靓亮仔\"}\n";

    private static final String BUSINESS_DAILY = "/business_daily";

    private static final String RESPONSE = "response:";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void overview() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqOverviewDTO = JSON.parseObject(JsonFileUtil.read("businessDaily/overview.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonOverviewString = JSON.toJSONString(reqOverviewDTO);
        MvcResult mvcOverviewResult = null;
        try {
            mvcOverviewResult = mockMvc.perform(post(BUSINESS_DAILY + "/overview")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonOverviewString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcOverviewResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void gather() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqGatherDTO = JSON.parseObject(JsonFileUtil.read("businessDaily/gather.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonGatherString = JSON.toJSONString(reqGatherDTO);
        MvcResult mvcGatherResult = null;
        try {
            mvcGatherResult = mockMvc.perform(post(BUSINESS_DAILY + "/gather")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonGatherString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcGatherResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void memberConsume() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqMemberConsumeDTO = JSON.parseObject(JsonFileUtil.read("businessDaily/memberConsume.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonMemberConsumeString = JSON.toJSONString(reqMemberConsumeDTO);
        MvcResult mvcMemberConsumeResult = null;
        try {
            mvcMemberConsumeResult = mockMvc.perform(post(BUSINESS_DAILY + "/member_consumer")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonMemberConsumeString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcMemberConsumeResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void memberConsumeDaily() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqMemberConsumeDailyDTO = JSON.parseObject(JsonFileUtil.read("businessDaily/memberConsumeDaily.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonMemberConsumeDailyString = JSON.toJSONString(reqMemberConsumeDailyDTO);
        MvcResult mvcMemberConsumeDailyResult = null;
        try {
            mvcMemberConsumeDailyResult = mockMvc.perform(post(BUSINESS_DAILY + "/member_consume_only")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonMemberConsumeDailyString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcMemberConsumeDailyResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void memberRechargeDaily() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqMemberRechargeDailyDTO = JSON.parseObject(JsonFileUtil.read("businessDaily/memberRechargeDaily.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonMemberRechargeDailyString = JSON.toJSONString(reqMemberRechargeDailyDTO);
        MvcResult mvcMemberRechargeDailyResult = null;
        try {
            mvcMemberRechargeDailyResult = mockMvc.perform(post(BUSINESS_DAILY + "/member_recharge")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonMemberRechargeDailyString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcMemberRechargeDailyResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void diningType() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqDiningTypeDTO = JSON.parseObject(JsonFileUtil.read("businessDaily/diningType.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonDiningTypeString = JSON.toJSONString(reqDiningTypeDTO);
        MvcResult mvcDiningTypeResult = null;
        try {
            mvcDiningTypeResult = mockMvc.perform(post(BUSINESS_DAILY + "/dining_type")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonDiningTypeString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcDiningTypeResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void classify() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqClassifyDTO = JSON.parseObject(JsonFileUtil.read("businessDaily/classify.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonClassifyString = JSON.toJSONString(reqClassifyDTO);
        MvcResult mvcClassifyResult = null;
        try {
            mvcClassifyResult = mockMvc.perform(post(BUSINESS_DAILY + "/classify")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonClassifyString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcClassifyResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void goods() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqGoodsDTO = JSON.parseObject(JsonFileUtil.read("businessDaily/goods.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonGoodsString = JSON.toJSONString(reqGoodsDTO);
        MvcResult mvcGoodsResult = null;
        try {
            mvcGoodsResult = mockMvc.perform(post(BUSINESS_DAILY + "/goods")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonGoodsString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcGoodsResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void attr() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqAttrDTO = JSON.parseObject(JsonFileUtil.read("businessDaily/attr.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonAttrString = JSON.toJSONString(reqAttrDTO);
        MvcResult mvcAttrResult = null;
        try {
            mvcAttrResult = mockMvc.perform(post(BUSINESS_DAILY + "/attr")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonAttrString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcAttrResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void returnVegetables() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqReturnVegetablesDTO = JSON.parseObject(JsonFileUtil.read("businessDaily/returnVegetables.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonReturnVegetablesString = JSON.toJSONString(reqReturnVegetablesDTO);
        MvcResult mvcReturnVegetablesResult = null;
        try {
            mvcReturnVegetablesResult = mockMvc.perform(post(BUSINESS_DAILY + "/return_vegetables")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonReturnVegetablesString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcReturnVegetablesResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void dishGiving() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqDishGivingDTO = JSON.parseObject(JsonFileUtil.read("businessDaily/dishGiving.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonDishGivingString = JSON.toJSONString(reqDishGivingDTO);
        MvcResult mvcDishGivingResult = null;
        try {
            mvcDishGivingResult = mockMvc.perform(post(BUSINESS_DAILY + "/dish_giving")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonDishGivingString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcDishGivingResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }
}