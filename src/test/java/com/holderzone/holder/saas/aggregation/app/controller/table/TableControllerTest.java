package com.holderzone.holder.saas.aggregation.app.controller.table;

import com.alibaba.fastjson.JSON;
import com.holderzone.holder.saas.aggregation.app.HolderSaasStoreAggregationAppApplication;
import com.holderzone.holder.saas.aggregation.app.controller.util.JsonFileUtil;
import com.holderzone.holder.saas.aggregation.app.execption.FileIllegalException;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.table.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.UnsupportedEncodingException;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @date 2023/10/31
 * @description
 */
@Slf4j
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@RunWith(SpringRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@SpringBootTest(classes = HolderSaasStoreAggregationAppApplication.class)
public class TableControllerTest {

    private static final String USERINFO = "{\"operSubjectGuid\": \"2010121440477930009\"," +
            "\"enterpriseGuid\":" + " \"2009281531195930006\"," +
            "\"enterpriseName\": \"赵氏企业\"," +
            "\"enterpriseNo\": \"********\"," +
            "\"storeGuid\":" + " \"2106221850429620006\"," +
            "\"storeName\": \"交子大道测试门店\"," +
            "\"storeNo\": \"5796807\",\"userGuid\": " +
            "\"6787561298847596545\"," +
            "\"account\": \"196504\"," +
            "\"tel\": \"***********\"," +
            "\"name\": \"靓亮仔\"}\n";

    private static final String RESPONSE = "response:";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    /**
     * 查询桌台区域
     */
    @Test
    public void queryArea() throws UnsupportedEncodingException {
        BaseDTO reqQueryAreaDTO = JSON.parseObject(JsonFileUtil.read("table/queryArea.json"),
                BaseDTO.class);
        String jsonQueryAreaString = JSON.toJSONString(reqQueryAreaDTO);
        MvcResult mvcQueryAreaResult = null;
        try {
            mvcQueryAreaResult = mockMvc.perform(post("/area/query")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonQueryAreaString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcQueryAreaResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void queryTable() throws UnsupportedEncodingException {
        TableBasicQueryDTO reqQueryTableDTO = JSON.parseObject(JsonFileUtil.read("table/queryTable.json"),
                TableBasicQueryDTO.class);
        String jsonQueryTableString = JSON.toJSONString(reqQueryTableDTO);
        MvcResult mvcQueryTableResult = null;
        try {
            mvcQueryTableResult = mockMvc.perform(post("/table/query")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonQueryTableString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcQueryTableResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void openTable() throws UnsupportedEncodingException {
        OpenTableDTO reqOpenTableDTO = JSON.parseObject(JsonFileUtil.read("table/openTable.json"),
                OpenTableDTO.class);
        String jsonOpenTableString = JSON.toJSONString(reqOpenTableDTO);
        MvcResult mvcOpenTableResult = null;
        try {
            mvcOpenTableResult = mockMvc.perform(post("/table/open")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonOpenTableString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcOpenTableResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void cleanTable() throws UnsupportedEncodingException {
        TableStatusChangeDTO reqCleanTableDTO = JSON.parseObject(JsonFileUtil.read("table/cleanTable.json"),
                TableStatusChangeDTO.class);
        String jsonCleanTableString = JSON.toJSONString(reqCleanTableDTO);
        MvcResult mvcCleanTableResult = null;
        try {
            mvcCleanTableResult = mockMvc.perform(post("/table/clean")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonCleanTableString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcCleanTableResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void turnTale() throws UnsupportedEncodingException {
        TurnTableDTO reqTurnTaleDTO = JSON.parseObject(JsonFileUtil.read("table/turnTale.json"),
                TurnTableDTO.class);
        String jsonTurnTaleString = JSON.toJSONString(reqTurnTaleDTO);
        MvcResult mvcTurnTaleResult = null;
        try {
            mvcTurnTaleResult = mockMvc.perform(post("/table/turn")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonTurnTaleString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcTurnTaleResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void combine() throws UnsupportedEncodingException {
        TableCombineDTO reqCombineDTO = JSON.parseObject(JsonFileUtil.read("table/combine.json"),
                TableCombineDTO.class);
        String jsonCombineString = JSON.toJSONString(reqCombineDTO);
        MvcResult mvcCombineResult = null;
        try {
            mvcCombineResult = mockMvc.perform(post("/table/combine")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonCombineString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcCombineResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void combineV2() throws UnsupportedEncodingException {
        TableCombineDTO reqCombineV2DTO = JSON.parseObject(JsonFileUtil.read("table/combineV2.json"),
                TableCombineDTO.class);
        String jsonCombineV2String = JSON.toJSONString(reqCombineV2DTO);
        MvcResult mvcCombineV2Result = null;
        try {
            mvcCombineV2Result = mockMvc.perform(post("/table/combine_v2")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonCombineV2String))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcCombineV2Result.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void separateTable() throws UnsupportedEncodingException {
        TableOrderCombineDTO reqSeparateTableDTO = JSON.parseObject(JsonFileUtil.read("table/separateTable.json"),
                TableOrderCombineDTO.class);
        String jsonSeparateTableString = JSON.toJSONString(reqSeparateTableDTO);
        MvcResult mvcSeparateTableResult = null;
        try {
            mvcSeparateTableResult = mockMvc.perform(post("/table/separate")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonSeparateTableString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcSeparateTableResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void closeTable() throws UnsupportedEncodingException {
        CancelOrderReqDTO reqCloseTableDTO = JSON.parseObject(JsonFileUtil.read("table/closeTable.json"),
                CancelOrderReqDTO.class);
        String jsonCloseTableString = JSON.toJSONString(reqCloseTableDTO);
        MvcResult mvcCloseTableResult = null;
        try {
            mvcCloseTableResult = mockMvc.perform(post("/table/close")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonCloseTableString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcCloseTableResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void tryLock() throws UnsupportedEncodingException {
        TableLockDTO reqTryLockDTO = JSON.parseObject(JsonFileUtil.read("table/tryLock.json"),
                TableLockDTO.class);
        String jsonTryLockString = JSON.toJSONString(reqTryLockDTO);
        MvcResult mvcTryLockResult = null;
        try {
            mvcTryLockResult = mockMvc.perform(post("/table/lock")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonTryLockString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = mvcTryLockResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void releaseLock() throws UnsupportedEncodingException {
        TableLockDTO releaseLockReqDTO = JSON.parseObject(JsonFileUtil.read("table/releaseLock.json"),
                TableLockDTO.class);
        String releaseLockJsonString = JSON.toJSONString(releaseLockReqDTO);
        MvcResult releaseLockMvcResult = null;
        try {
            releaseLockMvcResult = mockMvc.perform(post("/release/lock")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(releaseLockJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = releaseLockMvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    /**
     * 根据门店列表查询桌台Guid列表
     */
    @Test
    public void listTableByStoreGuid() throws UnsupportedEncodingException {
        SingleDataDTO listTableByStoreGuidReqDTO = JSON.parseObject(JsonFileUtil.read("table/listTableByStoreGuid.json"),
                SingleDataDTO.class);
        String listTableByStoreGuidJsonString = JSON.toJSONString(listTableByStoreGuidReqDTO);
        MvcResult listTableByStoreGuidMvcResult = null;
        try {
            listTableByStoreGuidMvcResult = mockMvc.perform(post("/table/list_table_by_store_guid")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(listTableByStoreGuidJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String contentAsString = listTableByStoreGuidMvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }
}