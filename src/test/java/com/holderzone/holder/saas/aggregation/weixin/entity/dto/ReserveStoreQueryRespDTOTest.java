package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import org.junit.Before;
import org.junit.Test;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class ReserveStoreQueryRespDTOTest {

    private ReserveStoreQueryRespDTO reserveStoreQueryRespDTOUnderTest;

    @Before
    public void setUp() throws Exception {
        reserveStoreQueryRespDTOUnderTest = new ReserveStoreQueryRespDTO(
                Arrays.asList(new ReserveTimeInfoRespDTO(LocalDate.of(2020, 1, 1), "msg")),
                Arrays.asList(new ReserveTimeRespDTO(LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0))),
                Arrays.asList(new ReserveAreaRespDTO()), "nickName", 0, "phone");
    }

    @Test
    public void testReserveTimeInfoRespDTOSGetterAndSetter() {
        final List<ReserveTimeInfoRespDTO> reserveTimeInfoRespDTOS = Arrays.asList(
                new ReserveTimeInfoRespDTO(LocalDate.of(2020, 1, 1), "msg"));
        reserveStoreQueryRespDTOUnderTest.setReserveTimeInfoRespDTOS(reserveTimeInfoRespDTOS);
        assertThat(reserveStoreQueryRespDTOUnderTest.getReserveTimeInfoRespDTOS()).isEqualTo(reserveTimeInfoRespDTOS);
    }

    @Test
    public void testReserveTimeRespDTOSGetterAndSetter() {
        final List<ReserveTimeRespDTO> reserveTimeRespDTOS = Arrays.asList(
                new ReserveTimeRespDTO(LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0)));
        reserveStoreQueryRespDTOUnderTest.setReserveTimeRespDTOS(reserveTimeRespDTOS);
        assertThat(reserveStoreQueryRespDTOUnderTest.getReserveTimeRespDTOS()).isEqualTo(reserveTimeRespDTOS);
    }

    @Test
    public void testReserveAreaRespDTOSGetterAndSetter() {
        final List<ReserveAreaRespDTO> reserveAreaRespDTOS = Arrays.asList(new ReserveAreaRespDTO());
        reserveStoreQueryRespDTOUnderTest.setReserveAreaRespDTOS(reserveAreaRespDTOS);
        assertThat(reserveStoreQueryRespDTOUnderTest.getReserveAreaRespDTOS()).isEqualTo(reserveAreaRespDTOS);
    }

    @Test
    public void testNickNameGetterAndSetter() {
        final String nickName = "nickName";
        reserveStoreQueryRespDTOUnderTest.setNickName(nickName);
        assertThat(reserveStoreQueryRespDTOUnderTest.getNickName()).isEqualTo(nickName);
    }

    @Test
    public void testGenderGetterAndSetter() {
        final Integer gender = 0;
        reserveStoreQueryRespDTOUnderTest.setGender(gender);
        assertThat(reserveStoreQueryRespDTOUnderTest.getGender()).isEqualTo(gender);
    }

    @Test
    public void testPhoneGetterAndSetter() {
        final String phone = "phone";
        reserveStoreQueryRespDTOUnderTest.setPhone(phone);
        assertThat(reserveStoreQueryRespDTOUnderTest.getPhone()).isEqualTo(phone);
    }

    @Test
    public void testEquals() throws Exception {
        assertThat(reserveStoreQueryRespDTOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() throws Exception {
        assertThat(reserveStoreQueryRespDTOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() throws Exception {
        assertThat(reserveStoreQueryRespDTOUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() throws Exception {
        assertThat(reserveStoreQueryRespDTOUnderTest.toString()).isEqualTo("result");
    }
}
