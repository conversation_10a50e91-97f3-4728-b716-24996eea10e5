package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.holder.saas.aggregation.weixin.config.WeCatConfig;
import com.holderzone.holder.saas.aggregation.weixin.service.MenuItemService;
import com.holderzone.holder.saas.aggregation.weixin.service.WxCpService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.DebtClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxMpMessageHandlerClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxOpenMessageHandleClientService;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.entry.dto.WxUserInfoDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.trade.DebtRecordH5RespDTO;
import com.holderzone.saas.store.dto.trade.DebtUnitLoginH5ReqDTO;
import com.holderzone.saas.store.dto.weixin.menu.WxMenuUrlDTO;
import com.holderzone.saas.store.dto.weixin.req.*;
import com.holderzone.saas.store.dto.weixin.resp.WxConfigRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxSubjectRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(WxMpMessageHandler.class)
public class WxMpMessageHandlerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxMpMessageHandlerClientService mockWxMpMessageHandlerClientService;
    @MockBean
    private WxOpenMessageHandleClientService mockWxMessageHandleClientService;
    @MockBean
    private MenuItemService mockMenuItemService;
    @MockBean
    private WeCatConfig mockWeCatConfig;
    @MockBean
    private RedisUtils mockRedisUtils;
    @MockBean
    private DebtClientService mockDebtClientService;
    @MockBean
    private WxCpService mockWxCpService;

    @Test
    public void testAuthGet() throws Exception {
        // Setup
        // Configure WxMpMessageHandlerClientService.verify(...).
        final WxCommonReqDTO wxCommonReqDTO = new WxCommonReqDTO();
        wxCommonReqDTO.setSignature("signature");
        wxCommonReqDTO.setEchostr("echostr");
        wxCommonReqDTO.setTimestamp("timestamp");
        wxCommonReqDTO.setNonce("nonce");
        wxCommonReqDTO.setEncrypt_type("encrypt_type");
        when(mockWxMpMessageHandlerClientService.verify(wxCommonReqDTO)).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/wx_mp/portal")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testCallBack() throws Exception {
        // Setup
        when(mockWeCatConfig.getOrderingMessageExpire()).thenReturn(0);
        when(mockRedisUtils.generateGuid("xweixintoken")).thenReturn("weixinToken");
        when(mockWeCatConfig.getOrderingIndexPage()).thenReturn("result");

        // Configure WxOpenMessageHandleClientService.buildSession(...).
        final WxMemberSessionDTO wxMemberSessionDTO = new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0);
        final WxAuthorizeReqDTO wxAuthorizeReqDTO = new WxAuthorizeReqDTO();
        wxAuthorizeReqDTO.setCode("code");
        wxAuthorizeReqDTO.setEventKey("eventKey");
        wxAuthorizeReqDTO.setWeixinToken("weixinToken");
        wxAuthorizeReqDTO.setEnterpriseGuid("enterpriseGuid");
        wxAuthorizeReqDTO.setCurrentTime(0L);
        when(mockWxMessageHandleClientService.buildSession(wxAuthorizeReqDTO)).thenReturn(wxMemberSessionDTO);

        // Configure WxMpMessageHandlerClientService.getUserInfo(...).
        final WxAuthorizeReqDTO wxAuthorizeReqDTO1 = new WxAuthorizeReqDTO();
        wxAuthorizeReqDTO1.setCode("code");
        wxAuthorizeReqDTO1.setEventKey("eventKey");
        wxAuthorizeReqDTO1.setWeixinToken("weixinToken");
        wxAuthorizeReqDTO1.setEnterpriseGuid("enterpriseGuid");
        wxAuthorizeReqDTO1.setCurrentTime(0L);
        when(mockWxMpMessageHandlerClientService.getUserInfo(wxAuthorizeReqDTO1)).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/wx_mp/call_back")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
        verify(mockMenuItemService).saveQrCodeTypeByToken("weixinToken", 0);
    }

    @Test
    public void testShopList() throws Exception {
        // Setup
        when(mockWxMpMessageHandlerClientService.shopList(
                new WxPreCodReqDTO("brandGuid", "enterpriseGuid", "appId", 0,"",""))).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/wx_mp/shop_list")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetWxSubject() throws Exception {
        // Setup
        // Configure WxMpMessageHandlerClientService.getWxSubject(...).
        final WxSubjectRespDTO wxSubjectRespDTO = new WxSubjectRespDTO(false, "operSubjectGuid", false);
        when(mockWxMpMessageHandlerClientService.getWxSubject(
                new WxSubjectReqDTO("enterpriseGuid", "appId"))).thenReturn(wxSubjectRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_mp/get_wx_subject")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testMemberLogin() throws Exception {
        // Setup
        when(mockWxMessageHandleClientService.memberLogin(
                new WxPortalReqDTO("enterpriseGuid", "appId", "storeGuid", "brandGuid", "msgKey", "url",
                        0, "","", "",""))).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_mp/login")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testNewMemberLogin() throws Exception {
        // Setup
        when(mockWxMessageHandleClientService.newMemberLogin(
                new WxPortalReqDTO("enterpriseGuid", "appId", "storeGuid", "brandGuid", "msgKey", "url",
                        0,"","", "",""))).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/wx_mp/new_member_login")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetWxConfig() throws Exception {
        // Setup
        // Configure WxMpMessageHandlerClientService.getWxConfig(...).
        final WxCommonReqDTO wxCommonReqDTO = new WxCommonReqDTO();
        wxCommonReqDTO.setSignature("signature");
        wxCommonReqDTO.setEchostr("echostr");
        wxCommonReqDTO.setTimestamp("timestamp");
        wxCommonReqDTO.setNonce("nonce");
        wxCommonReqDTO.setEncrypt_type("encrypt_type");
        final WxConfigRespDTO wxConfigRespDTO = new WxConfigRespDTO(wxCommonReqDTO, "brandName", "appId",
                "enterpriseGuid");
        when(mockWxMpMessageHandlerClientService.getWxConfig(
                new WxPortalReqDTO("enterpriseGuid", "appId", "storeGuid", "brandGuid", "msgKey", "url",
                        0,"","","",""))).thenReturn(wxConfigRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_mp/get_wx_config")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testH5Login() throws Exception {
        // Setup
        when(mockDebtClientService.h5Login(any(DebtUnitLoginH5ReqDTO.class))).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_mp/unit/h5login")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryDebtRecordH5() throws Exception {
        // Setup
        // Configure DebtClientService.queryDebtRecordH5(...).
        final DebtRecordH5RespDTO debtRecordH5RespDTO = new DebtRecordH5RespDTO();
        debtRecordH5RespDTO.setName("name");
        debtRecordH5RespDTO.setCode("code");
        debtRecordH5RespDTO.setCreditLimitLeft(new BigDecimal("0.00"));
        debtRecordH5RespDTO.setCreditLimit(new BigDecimal("0.00"));
        debtRecordH5RespDTO.setDebtAmount(new BigDecimal("0.00"));
        when(mockDebtClientService.queryDebtRecordH5(any(DebtUnitLoginH5ReqDTO.class))).thenReturn(debtRecordH5RespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_mp/record/h5query")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testRedirectParams() throws Exception {
        // Setup
        when(mockWxMpMessageHandlerClientService.qrRedirect("t")).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/wx_mp/zhuancan/qr")
                        .param("t", "t")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testRedirect() throws Exception {
        // Setup
        when(mockWxMpMessageHandlerClientService.qrRedirect("t")).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/wx_mp/qr")
                        .param("t", "t")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testRedirectBossAuthorizeUrl() throws Exception {
        // Setup
        // Configure WxMpMessageHandlerClientService.getBossAuthorizeUrl(...).
        final WxMenuUrlDTO wxMenuUrlDTO = new WxMenuUrlDTO();
        wxMenuUrlDTO.setEnterpriseGuid("enterpriseGuid");
        wxMenuUrlDTO.setBrandGuid("brandGuid");
        wxMenuUrlDTO.setMenuType("menuType");
        wxMenuUrlDTO.setAppId("appId");
        wxMenuUrlDTO.setToken("token");
        when(mockWxMpMessageHandlerClientService.getBossAuthorizeUrl(wxMenuUrlDTO)).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/wx_mp/menu_url/boss")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testRedirectBossUrl() throws Exception {
        // Setup
        // Configure WxMpMessageHandlerClientService.getBossRedirectUrl(...).
        final WxAuthorizeReqDTO wxAuthorizeReqDTO = new WxAuthorizeReqDTO();
        wxAuthorizeReqDTO.setCode("code");
        wxAuthorizeReqDTO.setEventKey("eventKey");
        wxAuthorizeReqDTO.setWeixinToken("weixinToken");
        wxAuthorizeReqDTO.setEnterpriseGuid("enterpriseGuid");
        wxAuthorizeReqDTO.setCurrentTime(0L);
        when(mockWxMpMessageHandlerClientService.getBossRedirectUrl(wxAuthorizeReqDTO)).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/wx_mp/menu_url/boss/call_back")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSaveBossAuthToken() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_mp/save/boss/token")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm WxMpMessageHandlerClientService.saveBossAuthToken(...).
        final WxMenuUrlDTO wxMenuUrlDTO = new WxMenuUrlDTO();
        wxMenuUrlDTO.setEnterpriseGuid("enterpriseGuid");
        wxMenuUrlDTO.setBrandGuid("brandGuid");
        wxMenuUrlDTO.setMenuType("menuType");
        wxMenuUrlDTO.setAppId("appId");
        wxMenuUrlDTO.setToken("token");
        verify(mockWxMpMessageHandlerClientService).saveBossAuthToken(wxMenuUrlDTO);
    }

    @Test
    public void testCleanBossAuthToken() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_mp/clean/boss/token")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm WxMpMessageHandlerClientService.cleanBossAuthToken(...).
        final WxMenuUrlDTO wxMenuUrlDTO = new WxMenuUrlDTO();
        wxMenuUrlDTO.setEnterpriseGuid("enterpriseGuid");
        wxMenuUrlDTO.setBrandGuid("brandGuid");
        wxMenuUrlDTO.setMenuType("menuType");
        wxMenuUrlDTO.setAppId("appId");
        wxMenuUrlDTO.setToken("token");
        verify(mockWxMpMessageHandlerClientService).cleanBossAuthToken(wxMenuUrlDTO);
    }

    @Test
    public void testWxCpAuthorize() throws Exception {
        // Setup
        when(mockWxCpService.queryWxCpAuthorizeUrl("t")).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/wx_mp/cp/authorize")
                        .param("t", "t")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testWxCpAuthorizeUrl() throws Exception {
        // Setup
        when(mockWxCpService.queryWxCpAuthorizeUrl("t")).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/wx_mp/cp/authorize/url")
                        .param("t", "t")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
