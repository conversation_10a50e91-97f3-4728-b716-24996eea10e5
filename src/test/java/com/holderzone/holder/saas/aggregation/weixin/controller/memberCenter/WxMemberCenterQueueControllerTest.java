package com.holderzone.holder.saas.aggregation.weixin.controller.memberCenter;

import com.holderzone.holder.saas.aggregation.weixin.service.rpc.memberCenter.WxMemberCenterQueueService;
import com.holderzone.saas.store.dto.queue.QueueWechatDTO;
import com.holderzone.saas.store.dto.queue.WxQueueListDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(WxMemberCenterQueueController.class)
public class WxMemberCenterQueueControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxMemberCenterQueueService mockWxMemberCenterQueueService;

    @Test
    public void testQueryByUser() throws Exception {
        // Setup
        // Configure WxMemberCenterQueueService.queryByUser(...).
        final List<WxQueueListDTO> wxQueueListDTOS = Arrays.asList(
                new WxQueueListDTO("22cada13-ac7c-4de2-b98d-2983d2d8d295", "userGuid", "storeGuid", "storeName",
                        "brandName", "brandGuid", "weekDate", LocalDate.of(2020, 1, 1), "time", (byte) 0b0,
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        when(mockWxMemberCenterQueueService.queryByUser(
                new QueueWechatDTO("brandGuid", "userGuid", (byte) 0b0, "enterpriseGuid"))).thenReturn(wxQueueListDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_member_center_queue/queryByUser")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryByUser_WxMemberCenterQueueServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockWxMemberCenterQueueService.queryByUser(
                new QueueWechatDTO("brandGuid", "userGuid", (byte) 0b0, "enterpriseGuid")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_member_center_queue/queryByUser")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testCancel() throws Exception {
        // Setup
        when(mockWxMemberCenterQueueService.cancel("queueGuid", "enterpriseGuid")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_member_center_queue/cancel")
                        .param("queueGuid", "queueGuid")
                        .param("enterpriseGuid", "enterpriseGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
