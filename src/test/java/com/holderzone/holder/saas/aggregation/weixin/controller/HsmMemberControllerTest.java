package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.holder.saas.aggregation.weixin.service.HsmMemberService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.StorePayClientService;
import com.holderzone.saas.store.dto.member.hsm.HsmRechargeReqDTO;
import com.holderzone.saas.store.dto.pay.*;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(HsmMemberController.class)
public class HsmMemberControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private StorePayClientService mockStorePayClientService;
    @MockBean
    private HsmMemberService mockHsmMemberService;

    @Test
    public void testWxRecharge() throws Exception {
        // Setup
        // Configure HsmMemberService.wechatRecharge(...).
        final KbzPayStartDTO kbzPayStartDTO = new KbzPayStartDTO();
        kbzPayStartDTO.setPrePayId("prePayId");
        kbzPayStartDTO.setOrderInfo("orderInfo");
        kbzPayStartDTO.setSign("sign");
        final WxPayRespDTO wxPayRespDTO = new WxPayRespDTO("payUrl", 0, "errorMsg",
                new AggPayRespDTO("code", "msg", "result", "payGuid", "orderGuid", "paymentAppId"), kbzPayStartDTO);
        final HsmRechargeReqDTO hsmRechargeReqDTO = new HsmRechargeReqDTO();
        hsmRechargeReqDTO.setPayGuid("payGuid");
        hsmRechargeReqDTO.setOrderGuid("orderGuid");
        hsmRechargeReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        hsmRechargeReqDTO.setRechargeMoney(new BigDecimal("0.00"));
        hsmRechargeReqDTO.setBrandGuid("brandGuid");
        when(mockHsmMemberService.wechatRecharge(hsmRechargeReqDTO)).thenReturn(wxPayRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hsm_member/wx_recharge")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testWxRecharge_HsmMemberServiceReturnsFailure() throws Exception {
        // Setup
        // Configure HsmMemberService.wechatRecharge(...).
        final WxPayRespDTO wxPayRespDTO = WxPayRespDTO.changeFailed();
        final HsmRechargeReqDTO hsmRechargeReqDTO = new HsmRechargeReqDTO();
        hsmRechargeReqDTO.setPayGuid("payGuid");
        hsmRechargeReqDTO.setOrderGuid("orderGuid");
        hsmRechargeReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        hsmRechargeReqDTO.setRechargeMoney(new BigDecimal("0.00"));
        hsmRechargeReqDTO.setBrandGuid("brandGuid");
        when(mockHsmMemberService.wechatRecharge(hsmRechargeReqDTO)).thenReturn(wxPayRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hsm_member/wx_recharge")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testCallback() throws Exception {
        // Setup
        // Configure HsmMemberService.callback(...).
        final SaasNotifyDTO saasNotifyDTO = new SaasNotifyDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setMsg("msg");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setSubject("subject");
        saasNotifyDTO.setAggPayPollingRespDTO(aggPayPollingRespDTO);
        when(mockHsmMemberService.callback(saasNotifyDTO)).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hsm_member/callback")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testPollingWeChatPublic() throws Exception {
        // Setup
        // Configure StorePayClientService.pollingWeChatPublic(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setMsg("msg");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setSubject("subject");
        aggPayPollingRespDTO.setBody("body");
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setOrderGuid("orderGuid");
        saasPollingDTO.setPayGuid("payGuid");
        when(mockStorePayClientService.pollingWeChatPublic(saasPollingDTO)).thenReturn(aggPayPollingRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hsm_member/wechat/public/polling")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testPollingWeChatPublic_StorePayClientServiceReturnsError() throws Exception {
        // Setup
        // Configure StorePayClientService.pollingWeChatPublic(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = AggPayPollingRespDTO.errorResp("code", "msg");
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setOrderGuid("orderGuid");
        saasPollingDTO.setPayGuid("payGuid");
        when(mockStorePayClientService.pollingWeChatPublic(saasPollingDTO)).thenReturn(aggPayPollingRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hsm_member/wechat/public/polling")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
