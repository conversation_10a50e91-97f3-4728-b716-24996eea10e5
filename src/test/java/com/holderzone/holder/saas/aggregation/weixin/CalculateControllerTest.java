package com.holderzone.holder.saas.aggregation.weixin;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.util.JsonFileUtil;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.weixin.deal.CalculateOrderDTO;
import com.holderzone.saas.store.dto.weixin.resp.CalculateOrderRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.UnsupportedEncodingException;
import java.util.List;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


/**
 * 微信小程序计算优惠金额
 */
@Slf4j
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@RunWith(MockitoJUnitRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@SpringBootTest(classes = HolderSaasAggregationWeixinApplication.class)
public class CalculateControllerTest {

    public static final String USERINFO = "{\"enterpriseGuid\":\"2009281531195930006\",\"enterpriseNo\":\"********\",\"enterpriseName\":\"赵氏企业\"," +
            "\"commercialActivities\":\"10001\",\"storeGuid\":\"2106221850429620006\",\"storeNo\":\"5796807\",\"storeName\":\"交子大道测试门店\"," +
            "\"deviceGuid\":\"2304251132214660007\",\"userGuid\":\"6869112864859226113\",\"name\":\"zhouzixiang\",\"tel\":\"***********\"," +
            "\"account\":\"967452\",\"allianceId\":null,\"isAlliance\":false,\"operSubjectGuid\":\"2010121440477930009\",\"multiMemberStatus\":false}";

    public static final String APPLET_WECHAT_CALCULATE = "/calculate";

    public static final String RESPONSE = "response:";

    public static final String PREFIX = "/deal/pay";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    /**
     * 小程序确认订单 计算优惠金额
     * 不使用优惠券
     */
    @Test
    public void calculateUnUseCoupon() throws UnsupportedEncodingException {
        CalculateOrderDTO calculateOrderDTO = JSON.parseObject(JsonFileUtil.read("/deal/calculate_no_coupon.json"), CalculateOrderDTO.class);
        String calculateUnUseCouponReqJson = JSON.toJSONString(calculateOrderDTO);
        MvcResult mvcResult = null;
        try {
            mvcResult = mockMvc.perform(post(PREFIX + APPLET_WECHAT_CALCULATE)
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(calculateUnUseCouponReqJson))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String calculateUnUseCouponContent = mvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + calculateUnUseCouponContent);
        Assert.notBlank(calculateUnUseCouponContent, "计算优惠返回为空");
        Result result = JacksonUtils.toObject(Result.class, calculateUnUseCouponContent);
        Assert.equals(result.getCode(), 0, "计算优惠返回失败");
        CalculateOrderRespDTO calculateOrderRespDTO = (CalculateOrderRespDTO) result.getTData();
        Assert.notNull(calculateOrderRespDTO.getActuallyPayFee(), "实付金额不能为空");
        Assert.notNull(calculateOrderRespDTO.getAppendFee(), "附加费不能为空");
        Assert.notEmpty(calculateOrderRespDTO.getAppendFeeDetailDTOS(), "附加费明细不能为空");
        Assert.notNull(calculateOrderRespDTO.getActuallyPayFee(), "实付金额不能为空");
        Assert.notNull(calculateOrderRespDTO.getDiscountFee(), "优惠金额不能为空");
        Assert.notEmpty(calculateOrderRespDTO.getDiscountFeeDetailDTOS(), "优惠明细不能为空");
        Assert.notNull(calculateOrderRespDTO.getOrderFee(), "订单总金额不能为空");
        Assert.notNull(calculateOrderRespDTO.getOrderSurplusFee(), "订单剩余金额不能为空");
    }


    /**
     * 小程序确认订单 计算优惠金额
     * 使用优惠券
     */
    @Test
    public void calculateUseCoupon() throws UnsupportedEncodingException {
        CalculateOrderDTO calculateOrderDTO = JSON.parseObject(JsonFileUtil.read("/deal/calculate_use_coupon.json"), CalculateOrderDTO.class);
        String calculateUseCouponReqJson = JSON.toJSONString(calculateOrderDTO);
        MvcResult mvcResult = null;
        try {
            mvcResult = mockMvc.perform(post(PREFIX + APPLET_WECHAT_CALCULATE)
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(calculateUseCouponReqJson))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String calculateUseCouponContent = mvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + calculateUseCouponContent);
        Assert.notBlank(calculateUseCouponContent, "计算优惠返回为空");
        Result result = JacksonUtils.toObject(Result.class, calculateUseCouponContent);
        Assert.equals(result.getCode(), 0, "计算优惠返回失败");
        CalculateOrderRespDTO calculateOrderRespDTO = (CalculateOrderRespDTO) result.getTData();
        Assert.notEmpty(calculateOrderRespDTO.getAppendFeeDetailDTOS(), "附加费明细不能为空");
        Assert.notNull(calculateOrderRespDTO.getActuallyPayFee(), "实付金额不能为空");
        Assert.notNull(calculateOrderRespDTO.getDiscountFee(), "优惠金额不能为空");
        Assert.notNull(calculateOrderRespDTO.getActuallyPayFee(), "实付金额不能为空");
        Assert.notNull(calculateOrderRespDTO.getAppendFee(), "附加费不能为空");
        Assert.notEmpty(calculateOrderRespDTO.getDiscountFeeDetailDTOS(), "优惠明细不能为空");
        List<DiscountFeeDetailDTO> discountFeeDetailList = calculateOrderRespDTO.getDiscountFeeDetailDTOS();
        DiscountFeeDetailDTO couponDiscountDetail = discountFeeDetailList.stream().filter(e -> e.getDiscountType() == 7).findFirst().orElse(null);
        Assert.notNull(couponDiscountDetail, "优惠券使用失败");
        Assert.notNull(calculateOrderRespDTO.getOrderFee(), "订单总金额不能为空");
        Assert.notNull(calculateOrderRespDTO.getOrderSurplusFee(), "订单剩余金额不能为空");
    }

}