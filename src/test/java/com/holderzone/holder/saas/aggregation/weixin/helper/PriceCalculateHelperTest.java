package com.holderzone.holder.saas.aggregation.weixin.helper;

import com.holderzone.holder.saas.aggregation.weixin.context.DiscountContext;
import com.holderzone.holder.saas.aggregation.weixin.entity.bo.DiscountRuleBO;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.ItemClientService;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.member.wechat.dto.activitie.RequestActivityFullDiscountRule;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseActivitieProduct;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseClientMarketActivity;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseMarketActivityUse;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseDiscount;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseProductDiscount;
import com.holderzone.saas.store.bo.weixin.SpecialsActivityAmountBO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityDetailsVO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityItemDTO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityItemVO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.weixin.deal.ActivitySelectDTO;
import com.holderzone.saas.store.dto.weixin.deal.ItemInfoDTO;
import com.holderzone.saas.store.dto.weixin.deal.SpecialsActivityAmountDTO;
import com.holderzone.saas.store.dto.weixin.resp.CalculateOrderRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.MarketingActivityInfoRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PriceCalculateHelperTest {

    @Mock
    private ItemClientService mockItemClientService;

    private PriceCalculateHelper priceCalculateHelperUnderTest;

    @Before
    public void setUp() throws Exception {
        priceCalculateHelperUnderTest = new PriceCalculateHelper(mockItemClientService);
    }

    @Test
    public void testGetSpecialsPrice() {
        // Setup
        final ItemInfoDTO itemInfoDTO = ItemInfoDTO.builder()
                .currentCount(new BigDecimal("0.00"))
                .originalPrice(new BigDecimal("0.00"))
                .minPrice(new BigDecimal("0.00"))
                .build();
        final LimitSpecialsActivityItemVO activityItemVO = new LimitSpecialsActivityItemVO();
        activityItemVO.setCommodityId("commodityId");
        activityItemVO.setCommodityCode("commodityCode");
        activityItemVO.setSpecialsType(1);
        activityItemVO.setSpecialsNumber(new BigDecimal("0.00"));
        activityItemVO.setLimitNumber(0);
        activityItemVO.setItemUseNumber(0);
        activityItemVO.setActivityGuid("activityGuid");
        activityItemVO.setActivityName("activityName");
        activityItemVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityItemVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityItemVO.setState(0);
        activityItemVO.setIsLimitPeriod(0);
        activityItemVO.setLimitPeriodType(0);
        activityItemVO.setLimitPeriodJson("equitiesTimeLimitedJson");
        activityItemVO.setRelationRule(0);
        activityItemVO.setApplyBusiness(0);

        // Run the test
        final BigDecimal result = PriceCalculateHelper.getSpecialsPrice(itemInfoDTO, activityItemVO, false);

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
    }

    @Test
    public void testGetSpecialsPrice2() {
        // Setup
        final ItemInfoDTO itemInfoDTO = ItemInfoDTO.builder()
                .currentCount(new BigDecimal("1"))
                .originalPrice(new BigDecimal("6"))
                .minPrice(new BigDecimal("6.00"))
                .build();
        final LimitSpecialsActivityItemVO activityItemVO = new LimitSpecialsActivityItemVO();
        activityItemVO.setCommodityId("commodityId");
        activityItemVO.setCommodityCode("commodityCode");
        activityItemVO.setSpecialsType(1);
        activityItemVO.setSpecialsNumber(new BigDecimal("0.00"));
        activityItemVO.setLimitNumber(0);
        activityItemVO.setItemUseNumber(0);
        activityItemVO.setActivityGuid("activityGuid");
        activityItemVO.setActivityName("activityName");
        activityItemVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityItemVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityItemVO.setState(0);
        activityItemVO.setIsLimitPeriod(0);
        activityItemVO.setLimitPeriodType(0);
        activityItemVO.setLimitPeriodJson("equitiesTimeLimitedJson");
        activityItemVO.setRelationRule(0);
        activityItemVO.setApplyBusiness(0);

        // Run the test
        final BigDecimal result = PriceCalculateHelper.getSpecialsPrice(itemInfoDTO, activityItemVO, true);

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("6.00"));
    }

    @Test
    public void testGetSpecialsPrice3() {
        // Setup
        final ItemInfoDTO itemInfoDTO = ItemInfoDTO.builder()
                .currentCount(new BigDecimal("1"))
                .originalPrice(new BigDecimal("6"))
                .minPrice(new BigDecimal("6.00"))
                .build();
        final LimitSpecialsActivityItemVO activityItemVO = new LimitSpecialsActivityItemVO();
        activityItemVO.setCommodityId("commodityId");
        activityItemVO.setCommodityCode("commodityCode");
        activityItemVO.setSpecialsType(2);
        activityItemVO.setSpecialsNumber(new BigDecimal("0.00"));
        activityItemVO.setLimitNumber(0);
        activityItemVO.setItemUseNumber(0);
        activityItemVO.setActivityGuid("activityGuid");
        activityItemVO.setActivityName("activityName");
        activityItemVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityItemVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityItemVO.setState(0);
        activityItemVO.setIsLimitPeriod(0);
        activityItemVO.setLimitPeriodType(0);
        activityItemVO.setLimitPeriodJson("equitiesTimeLimitedJson");
        activityItemVO.setRelationRule(0);
        activityItemVO.setApplyBusiness(0);

        // Run the test
        final BigDecimal result = PriceCalculateHelper.getSpecialsPrice(itemInfoDTO, activityItemVO, true);

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("6.00"));
    }

    @Test
    public void testGetSpecialsPrice4() {
        // Setup
        final ItemInfoDTO itemInfoDTO = ItemInfoDTO.builder()
                .currentCount(new BigDecimal("1"))
                .originalPrice(new BigDecimal("6"))
                .minPrice(new BigDecimal("6.00"))
                .build();
        final LimitSpecialsActivityItemVO activityItemVO = new LimitSpecialsActivityItemVO();
        activityItemVO.setCommodityId("commodityId");
        activityItemVO.setCommodityCode("commodityCode");
        activityItemVO.setSpecialsType(3);
        activityItemVO.setSpecialsNumber(new BigDecimal("0.00"));
        activityItemVO.setLimitNumber(0);
        activityItemVO.setItemUseNumber(0);
        activityItemVO.setActivityGuid("activityGuid");
        activityItemVO.setActivityName("activityName");
        activityItemVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityItemVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityItemVO.setState(0);
        activityItemVO.setIsLimitPeriod(0);
        activityItemVO.setLimitPeriodType(0);
        activityItemVO.setLimitPeriodJson("equitiesTimeLimitedJson");
        activityItemVO.setRelationRule(0);
        activityItemVO.setApplyBusiness(0);

        // Run the test
        final BigDecimal result = PriceCalculateHelper.getSpecialsPrice(itemInfoDTO, activityItemVO, true);

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("6.00"));
    }

    @Test
    public void testCalculateSpecialsActivityDiscountPrice() {
        // Setup
        final DineInItemDTO itemInfoDTO = new DineInItemDTO();
        itemInfoDTO.setGuid("2ff6364d-0abe-4fe1-86de-ca109ada08b5");
        itemInfoDTO.setItemGuid("itemGuid");
        itemInfoDTO.setSkuGuid("skuGuid");
        itemInfoDTO.setPrice(new BigDecimal("0.00"));
        itemInfoDTO.setItemPrice(new BigDecimal("0.00"));
        itemInfoDTO.setTotalDiscountFee(new BigDecimal("0.00"));
        itemInfoDTO.setMemberPrice(new BigDecimal("0.00"));
        itemInfoDTO.setCurrentCount(new BigDecimal("0.00"));
        itemInfoDTO.setIsGoodsReduceDiscount(new BigDecimal("0.00"));
        itemInfoDTO.setOriginalPrice(new BigDecimal("0.00"));
        itemInfoDTO.setMemberPreferential(new BigDecimal("0.00"));
        itemInfoDTO.setTicketPreferential(new BigDecimal("0.00"));
        itemInfoDTO.setDiscountPreferential(new BigDecimal("0.00"));
        itemInfoDTO.setDiscountTotalPrice(new BigDecimal("0.00"));
        itemInfoDTO.setSpecialsTotalPrice(new BigDecimal("0.00"));
        itemInfoDTO.setSpecialsDiscountPrice(new BigDecimal("0.00"));
        itemInfoDTO.setSpecialsPrice(new BigDecimal("0.00"));
        itemInfoDTO.setSpecialsMemberPrice(new BigDecimal("0.00"));
        itemInfoDTO.setLimitPrice(new BigDecimal("0.00"));
        itemInfoDTO.setJoinSpecialsCount(new BigDecimal("0.00"));
        itemInfoDTO.setSpecialsSingleDistinctPrice(new BigDecimal("0.00"));
        itemInfoDTO.setMinPrice(new BigDecimal("0.00"));
        itemInfoDTO.setMinPriceType(0);
        itemInfoDTO.setSpecialsActivityGuid("specialsActivityGuid");

        final LimitSpecialsActivityItemVO activityItemVO = new LimitSpecialsActivityItemVO();
        activityItemVO.setCommodityId("commodityId");
        activityItemVO.setCommodityCode("commodityCode");
        activityItemVO.setSpecialsType(1);
        activityItemVO.setSpecialsNumber(new BigDecimal("0.00"));
        activityItemVO.setLimitNumber(0);
        activityItemVO.setItemUseNumber(0);
        activityItemVO.setActivityGuid("activityGuid");
        activityItemVO.setActivityName("activityName");
        activityItemVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityItemVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityItemVO.setState(0);
        activityItemVO.setIsLimitPeriod(0);
        activityItemVO.setLimitPeriodType(0);
        activityItemVO.setLimitPeriodJson("equitiesTimeLimitedJson");
        activityItemVO.setRelationRule(0);
        activityItemVO.setApplyBusiness(0);

        // Run the test
        final BigDecimal result = priceCalculateHelperUnderTest.calculateSpecialsActivityDiscountPrice(itemInfoDTO,
                activityItemVO);

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
    }

    @Test
    public void testCalculateSpecialsActivityByMemberPrice() {
        // Setup
        final DiscountContext context = new DiscountContext();
        final CalculateOrderRespDTO calculateOrderRespDTO = new CalculateOrderRespDTO();
        calculateOrderRespDTO.setOrderSurplusFee(new BigDecimal("0.00"));
        calculateOrderRespDTO.setOrderSurplusFeeBySkipSpecials(new BigDecimal("0.00"));
        final ActivitySelectDTO activitySelectDTO = new ActivitySelectDTO();
        activitySelectDTO.setActivityType(0);
        calculateOrderRespDTO.setActivitySelectList(Arrays.asList(activitySelectDTO));
        final MarketingActivityInfoRespDTO infoRespDTO = new MarketingActivityInfoRespDTO();
        infoRespDTO.setActivityGuid("activityGuid");
        infoRespDTO.setUseAble(false);
        infoRespDTO.setUnUseReason("view");
        infoRespDTO.setDiscountPrice(new BigDecimal("0.00"));
        calculateOrderRespDTO.setActivityInfoList(Arrays.asList(infoRespDTO));
        context.setCalculateOrderRespDTO(calculateOrderRespDTO);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("2ff6364d-0abe-4fe1-86de-ca109ada08b5");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsGoodsReduceDiscount(new BigDecimal("0.00"));
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setTicketPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsDiscountPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setLimitPrice(new BigDecimal("0.00"));
        dineInItemDTO.setJoinSpecialsCount(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsSingleDistinctPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPriceType(0);
        dineInItemDTO.setSpecialsActivityGuid("specialsActivityGuid");
        context.setAllItems(Arrays.asList(dineInItemDTO));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("2ff6364d-0abe-4fe1-86de-ca109ada08b5");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsGoodsReduceDiscount(new BigDecimal("0.00"));
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberPreferential(new BigDecimal("0.00"));
        dineInItemDTO1.setTicketPreferential(new BigDecimal("0.00"));
        dineInItemDTO1.setDiscountPreferential(new BigDecimal("0.00"));
        dineInItemDTO1.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsDiscountPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setLimitPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setJoinSpecialsCount(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsSingleDistinctPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPriceType(0);
        dineInItemDTO1.setSpecialsActivityGuid("specialsActivityGuid");
        context.setBeforeSpecialsItems(Arrays.asList(dineInItemDTO1));
        final DiscountRuleBO discountRuleBO = new DiscountRuleBO();
        final LimitSpecialsActivityDetailsVO specialsActivityDetailsVO = new LimitSpecialsActivityDetailsVO();
        specialsActivityDetailsVO.setRelationRule(0);
        final LimitSpecialsActivityItemDTO activityItemDTO = new LimitSpecialsActivityItemDTO();
        specialsActivityDetailsVO.setItemDTOList(Arrays.asList(activityItemDTO));
        discountRuleBO.setSpecialsActivityDetailsVO(specialsActivityDetailsVO);
        final ResponseProductDiscount responseProductDiscount = new ResponseProductDiscount();
        responseProductDiscount.setMemberRightsType(0);
        responseProductDiscount.setDiscountValue(new BigDecimal("0.00"));
        discountRuleBO.setResponseProductDiscount(responseProductDiscount);
        context.setDiscountRuleBO(discountRuleBO);
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        context.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));

        final LimitSpecialsActivityDetailsVO activityDetailsVO = new LimitSpecialsActivityDetailsVO();
        activityDetailsVO.setGuid("activityGuid");
        activityDetailsVO.setName("activityName");
        activityDetailsVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO.setIsLimitPeriod(0);
        activityDetailsVO.setEquitiesTimeLimitedType(0);
        activityDetailsVO.setEquitiesTimeLimitedJson("equitiesTimeLimitedJson");
        activityDetailsVO.setRelationRule(0);
        activityDetailsVO.setApplyBusiness(0);
        final LimitSpecialsActivityItemDTO activityItemDTO1 = new LimitSpecialsActivityItemDTO();
        activityItemDTO1.setCommodityId("commodityId");
        activityItemDTO1.setCommodityCode("commodityCode");
        activityItemDTO1.setSpecialsType(1);
        activityItemDTO1.setSpecialsNumber(new BigDecimal("0.00"));
        activityItemDTO1.setLimitNumber(0);
        activityDetailsVO.setItemDTOList(Arrays.asList(activityItemDTO1));

        // Run the test
        final BigDecimal result = priceCalculateHelperUnderTest.calculateSpecialsActivityByMemberPrice(context,
                activityDetailsVO);

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0"));
    }

    @Test
    public void testCalculateSpecialsActivityByMemberPrice1() {
        // Setup
        final DiscountContext context = new DiscountContext();
        final CalculateOrderRespDTO calculateOrderRespDTO = new CalculateOrderRespDTO();
        calculateOrderRespDTO.setOrderSurplusFee(new BigDecimal("0.00"));
        calculateOrderRespDTO.setOrderSurplusFeeBySkipSpecials(new BigDecimal("0.00"));
        final ActivitySelectDTO activitySelectDTO = new ActivitySelectDTO();
        activitySelectDTO.setActivityType(0);
        calculateOrderRespDTO.setActivitySelectList(Arrays.asList(activitySelectDTO));
        final MarketingActivityInfoRespDTO infoRespDTO = new MarketingActivityInfoRespDTO();
        infoRespDTO.setActivityGuid("activityGuid");
        infoRespDTO.setUseAble(false);
        infoRespDTO.setUnUseReason("view");
        infoRespDTO.setDiscountPrice(new BigDecimal("0.00"));
        calculateOrderRespDTO.setActivityInfoList(Arrays.asList(infoRespDTO));
        context.setCalculateOrderRespDTO(calculateOrderRespDTO);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("2ff6364d-0abe-4fe1-86de-ca109ada08b5");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsGoodsReduceDiscount(new BigDecimal("0.00"));
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setTicketPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsDiscountPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setLimitPrice(new BigDecimal("0.00"));
        dineInItemDTO.setJoinSpecialsCount(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsSingleDistinctPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPriceType(0);
        dineInItemDTO.setSpecialsActivityGuid("specialsActivityGuid");
        context.setAllItems(Arrays.asList(dineInItemDTO));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("2ff6364d-0abe-4fe1-86de-ca109ada08b5");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsGoodsReduceDiscount(new BigDecimal("0.00"));
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberPreferential(new BigDecimal("0.00"));
        dineInItemDTO1.setTicketPreferential(new BigDecimal("0.00"));
        dineInItemDTO1.setDiscountPreferential(new BigDecimal("0.00"));
        dineInItemDTO1.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsDiscountPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setLimitPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setJoinSpecialsCount(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsSingleDistinctPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPriceType(0);
        dineInItemDTO1.setSpecialsActivityGuid("specialsActivityGuid");
        context.setBeforeSpecialsItems(Arrays.asList(dineInItemDTO1));
        final DiscountRuleBO discountRuleBO = new DiscountRuleBO();
        final LimitSpecialsActivityDetailsVO specialsActivityDetailsVO = new LimitSpecialsActivityDetailsVO();
        specialsActivityDetailsVO.setRelationRule(0);
        final LimitSpecialsActivityItemDTO activityItemDTO = new LimitSpecialsActivityItemDTO();
        specialsActivityDetailsVO.setItemDTOList(Arrays.asList(activityItemDTO));
        discountRuleBO.setSpecialsActivityDetailsVO(specialsActivityDetailsVO);
        final ResponseProductDiscount responseProductDiscount = new ResponseProductDiscount();
        responseProductDiscount.setMemberRightsType(0);
        responseProductDiscount.setDiscountValue(new BigDecimal("0.00"));
        discountRuleBO.setResponseProductDiscount(responseProductDiscount);
        context.setDiscountRuleBO(discountRuleBO);
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(9);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("5"));
        context.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));

        final LimitSpecialsActivityDetailsVO activityDetailsVO = new LimitSpecialsActivityDetailsVO();
        activityDetailsVO.setGuid("activityGuid");
        activityDetailsVO.setName("activityName");
        activityDetailsVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO.setIsLimitPeriod(0);
        activityDetailsVO.setEquitiesTimeLimitedType(0);
        activityDetailsVO.setEquitiesTimeLimitedJson("equitiesTimeLimitedJson");
        activityDetailsVO.setRelationRule(0);
        activityDetailsVO.setApplyBusiness(0);
        final LimitSpecialsActivityItemDTO activityItemDTO1 = new LimitSpecialsActivityItemDTO();
        activityItemDTO1.setCommodityId("commodityId");
        activityItemDTO1.setCommodityCode("commodityCode");
        activityItemDTO1.setSpecialsType(1);
        activityItemDTO1.setSpecialsNumber(new BigDecimal("0.00"));
        activityItemDTO1.setLimitNumber(0);
        activityDetailsVO.setItemDTOList(Arrays.asList(activityItemDTO1));

        // Run the test
        final BigDecimal result = priceCalculateHelperUnderTest.calculateSpecialsActivityByMemberPrice(context,
                activityDetailsVO);

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0"));
    }

    @Test
    public void testCalculateSpecialsActivityByMemberPrice2() {
        // Setup
        final DiscountContext context = new DiscountContext();
        final CalculateOrderRespDTO calculateOrderRespDTO = new CalculateOrderRespDTO();
        calculateOrderRespDTO.setOrderSurplusFee(new BigDecimal("0.00"));
        calculateOrderRespDTO.setOrderSurplusFeeBySkipSpecials(new BigDecimal("0.00"));
        final ActivitySelectDTO activitySelectDTO = new ActivitySelectDTO();
        activitySelectDTO.setActivityType(0);
        calculateOrderRespDTO.setActivitySelectList(Arrays.asList(activitySelectDTO));
        final MarketingActivityInfoRespDTO infoRespDTO = new MarketingActivityInfoRespDTO();
        infoRespDTO.setActivityGuid("activityGuid");
        infoRespDTO.setUseAble(false);
        infoRespDTO.setUnUseReason("view");
        infoRespDTO.setDiscountPrice(new BigDecimal("0.00"));
        calculateOrderRespDTO.setActivityInfoList(Arrays.asList(infoRespDTO));
        context.setCalculateOrderRespDTO(calculateOrderRespDTO);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("2ff6364d-0abe-4fe1-86de-ca109ada08b5");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsGoodsReduceDiscount(new BigDecimal("0.00"));
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setTicketPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsTotalPrice(new BigDecimal("1.00"));
        dineInItemDTO.setSpecialsDiscountPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setLimitPrice(new BigDecimal("0.00"));
        dineInItemDTO.setJoinSpecialsCount(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsSingleDistinctPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPriceType(0);
        dineInItemDTO.setSpecialsActivityGuid("specialsActivityGuid");
        context.setAllItems(Arrays.asList(dineInItemDTO));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("2ff6364d-0abe-4fe1-86de-ca109ada08b5");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsGoodsReduceDiscount(new BigDecimal("0.00"));
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberPreferential(new BigDecimal("0.00"));
        dineInItemDTO1.setTicketPreferential(new BigDecimal("0.00"));
        dineInItemDTO1.setDiscountPreferential(new BigDecimal("0.00"));
        dineInItemDTO1.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsDiscountPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setLimitPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setJoinSpecialsCount(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsSingleDistinctPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPriceType(0);
        dineInItemDTO1.setSpecialsActivityGuid("specialsActivityGuid");
        context.setBeforeSpecialsItems(Arrays.asList(dineInItemDTO1));
        final DiscountRuleBO discountRuleBO = new DiscountRuleBO();
        final LimitSpecialsActivityDetailsVO specialsActivityDetailsVO = new LimitSpecialsActivityDetailsVO();
        specialsActivityDetailsVO.setRelationRule(0);
        final LimitSpecialsActivityItemDTO activityItemDTO = new LimitSpecialsActivityItemDTO();
        specialsActivityDetailsVO.setItemDTOList(Arrays.asList(activityItemDTO));
        discountRuleBO.setSpecialsActivityDetailsVO(specialsActivityDetailsVO);
        final ResponseProductDiscount responseProductDiscount = new ResponseProductDiscount();
        responseProductDiscount.setMemberRightsType(0);
        responseProductDiscount.setDiscountValue(new BigDecimal("0.00"));
        discountRuleBO.setResponseProductDiscount(responseProductDiscount);
        context.setDiscountRuleBO(discountRuleBO);
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(9);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("5"));
        context.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));

        final LimitSpecialsActivityDetailsVO activityDetailsVO = new LimitSpecialsActivityDetailsVO();
        activityDetailsVO.setGuid("activityGuid");
        activityDetailsVO.setName("activityName");
        activityDetailsVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO.setIsLimitPeriod(0);
        activityDetailsVO.setEquitiesTimeLimitedType(0);
        activityDetailsVO.setEquitiesTimeLimitedJson("equitiesTimeLimitedJson");
        activityDetailsVO.setRelationRule(1);
        activityDetailsVO.setApplyBusiness(0);
        final LimitSpecialsActivityItemDTO activityItemDTO1 = new LimitSpecialsActivityItemDTO();
        activityItemDTO1.setCommodityId("commodityId");
        activityItemDTO1.setCommodityCode("commodityCode");
        activityItemDTO1.setSpecialsType(1);
        activityItemDTO1.setSpecialsNumber(new BigDecimal("0.00"));
        activityItemDTO1.setLimitNumber(0);
        activityDetailsVO.setItemDTOList(Arrays.asList(activityItemDTO1));

        // Run the test
        final BigDecimal result = priceCalculateHelperUnderTest.calculateSpecialsActivityByMemberPrice(context,
                activityDetailsVO);

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0"));
    }

    @Test
    public void testCalculateSpecialsActivityByMemberPrice3() {
        // Setup
        final DiscountContext context = new DiscountContext();
        final CalculateOrderRespDTO calculateOrderRespDTO = new CalculateOrderRespDTO();
        calculateOrderRespDTO.setOrderSurplusFee(new BigDecimal("0.00"));
        calculateOrderRespDTO.setOrderSurplusFeeBySkipSpecials(new BigDecimal("0.00"));
        final ActivitySelectDTO activitySelectDTO = new ActivitySelectDTO();
        activitySelectDTO.setActivityType(0);
        calculateOrderRespDTO.setActivitySelectList(Arrays.asList(activitySelectDTO));
        final MarketingActivityInfoRespDTO infoRespDTO = new MarketingActivityInfoRespDTO();
        infoRespDTO.setActivityGuid("activityGuid");
        infoRespDTO.setUseAble(false);
        infoRespDTO.setUnUseReason("view");
        infoRespDTO.setDiscountPrice(new BigDecimal("0.00"));
        calculateOrderRespDTO.setActivityInfoList(Arrays.asList(infoRespDTO));
        context.setCalculateOrderRespDTO(calculateOrderRespDTO);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("2ff6364d-0abe-4fe1-86de-ca109ada08b5");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("1.00"));
        dineInItemDTO.setIsGoodsReduceDiscount(new BigDecimal("0.00"));
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setTicketPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsDiscountPrice(new BigDecimal("3.00"));
        dineInItemDTO.setSpecialsPrice(new BigDecimal("2.00"));
        dineInItemDTO.setSpecialsMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setLimitPrice(new BigDecimal("0.00"));
        dineInItemDTO.setJoinSpecialsCount(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsSingleDistinctPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPriceType(0);
        dineInItemDTO.setSpecialsActivityGuid("specialsActivityGuid");
        context.setAllItems(Arrays.asList(dineInItemDTO));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("2ff6364d-0abe-4fe1-86de-ca109ada08b5");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsGoodsReduceDiscount(new BigDecimal("0.00"));
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberPreferential(new BigDecimal("0.00"));
        dineInItemDTO1.setTicketPreferential(new BigDecimal("0.00"));
        dineInItemDTO1.setDiscountPreferential(new BigDecimal("0.00"));
        dineInItemDTO1.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsDiscountPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setLimitPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setJoinSpecialsCount(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsSingleDistinctPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPriceType(0);
        dineInItemDTO1.setSpecialsActivityGuid("specialsActivityGuid");
        context.setBeforeSpecialsItems(Arrays.asList(dineInItemDTO1));
        final DiscountRuleBO discountRuleBO = new DiscountRuleBO();
        final LimitSpecialsActivityDetailsVO specialsActivityDetailsVO = new LimitSpecialsActivityDetailsVO();
        specialsActivityDetailsVO.setRelationRule(0);
        final LimitSpecialsActivityItemDTO activityItemDTO = new LimitSpecialsActivityItemDTO();
        specialsActivityDetailsVO.setItemDTOList(Arrays.asList(activityItemDTO));
        discountRuleBO.setSpecialsActivityDetailsVO(specialsActivityDetailsVO);
        final ResponseProductDiscount responseProductDiscount = new ResponseProductDiscount();
        responseProductDiscount.setMemberRightsType(0);
        responseProductDiscount.setDiscountValue(new BigDecimal("0.00"));
        discountRuleBO.setResponseProductDiscount(responseProductDiscount);
        context.setDiscountRuleBO(discountRuleBO);
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(9);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0"));
        context.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));

        final LimitSpecialsActivityDetailsVO activityDetailsVO = new LimitSpecialsActivityDetailsVO();
        activityDetailsVO.setGuid("activityGuid");
        activityDetailsVO.setName("activityName");
        activityDetailsVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO.setIsLimitPeriod(0);
        activityDetailsVO.setEquitiesTimeLimitedType(0);
        activityDetailsVO.setEquitiesTimeLimitedJson("equitiesTimeLimitedJson");
        activityDetailsVO.setRelationRule(0);
        activityDetailsVO.setApplyBusiness(0);
        final LimitSpecialsActivityItemDTO activityItemDTO1 = new LimitSpecialsActivityItemDTO();
        activityItemDTO1.setCommodityId("commodityId");
        activityItemDTO1.setCommodityCode("commodityCode");
        activityItemDTO1.setSpecialsType(1);
        activityItemDTO1.setSpecialsNumber(new BigDecimal("0.00"));
        activityItemDTO1.setLimitNumber(0);
        activityDetailsVO.setItemDTOList(Arrays.asList(activityItemDTO1));

        // Run the test
        final BigDecimal result = priceCalculateHelperUnderTest.calculateSpecialsActivityByMemberPrice(context,
                activityDetailsVO);

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0"));
    }

    @Test
    public void testCalculateAgainBySort() {
        // Setup
        final LimitSpecialsActivityDetailsVO activityDetailsVO = new LimitSpecialsActivityDetailsVO();
        activityDetailsVO.setGuid("activityGuid");
        activityDetailsVO.setName("activityName");
        activityDetailsVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO.setIsLimitPeriod(0);
        activityDetailsVO.setEquitiesTimeLimitedType(0);
        activityDetailsVO.setEquitiesTimeLimitedJson("equitiesTimeLimitedJson");
        activityDetailsVO.setRelationRule(0);
        activityDetailsVO.setApplyBusiness(0);
        final LimitSpecialsActivityItemDTO activityItemDTO = new LimitSpecialsActivityItemDTO();
        activityItemDTO.setCommodityId("commodityId");
        activityItemDTO.setCommodityCode("commodityCode");
        activityItemDTO.setSpecialsType(1);
        activityItemDTO.setSpecialsNumber(new BigDecimal("0.00"));
        activityItemDTO.setLimitNumber(0);
        activityDetailsVO.setItemDTOList(Arrays.asList(activityItemDTO));

        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("2ff6364d-0abe-4fe1-86de-ca109ada08b5");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsGoodsReduceDiscount(new BigDecimal("0.00"));
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setTicketPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsDiscountPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setLimitPrice(new BigDecimal("0.00"));
        dineInItemDTO.setJoinSpecialsCount(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsSingleDistinctPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPriceType(0);
        dineInItemDTO.setSpecialsActivityGuid("specialsActivityGuid");
        final List<DineInItemDTO> skuItemList = Arrays.asList(dineInItemDTO);
        final LimitSpecialsActivityItemVO activityItemVO = new LimitSpecialsActivityItemVO();
        activityItemVO.setCommodityId("commodityId");
        activityItemVO.setCommodityCode("commodityCode");
        activityItemVO.setSpecialsType(1);
        activityItemVO.setSpecialsNumber(new BigDecimal("0.00"));
        activityItemVO.setLimitNumber(0);
        activityItemVO.setItemUseNumber(0);
        activityItemVO.setActivityGuid("activityGuid");
        activityItemVO.setActivityName("activityName");
        activityItemVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityItemVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityItemVO.setState(0);
        activityItemVO.setIsLimitPeriod(0);
        activityItemVO.setLimitPeriodType(0);
        activityItemVO.setLimitPeriodJson("equitiesTimeLimitedJson");
        activityItemVO.setRelationRule(0);
        activityItemVO.setApplyBusiness(0);

        final ResponseProductDiscount responseProductDiscount = new ResponseProductDiscount();
        responseProductDiscount.setMemberPrice(false);
        final ResponseDiscount responseDiscount = new ResponseDiscount();
        responseDiscount.setAllProducts(false);
        responseProductDiscount.setDiscountRespDTOList(Arrays.asList(responseDiscount));
        responseProductDiscount.setMemberRightsType(0);
        responseProductDiscount.setDiscountValue(new BigDecimal("0.00"));

        final DiscountContext context = new DiscountContext();
        final CalculateOrderRespDTO calculateOrderRespDTO = new CalculateOrderRespDTO();
        calculateOrderRespDTO.setOrderSurplusFee(new BigDecimal("0.00"));
        calculateOrderRespDTO.setOrderSurplusFeeBySkipSpecials(new BigDecimal("0.00"));
        final ActivitySelectDTO activitySelectDTO = new ActivitySelectDTO();
        activitySelectDTO.setActivityType(0);
        calculateOrderRespDTO.setActivitySelectList(Arrays.asList(activitySelectDTO));
        final MarketingActivityInfoRespDTO infoRespDTO = new MarketingActivityInfoRespDTO();
        infoRespDTO.setActivityGuid("activityGuid");
        infoRespDTO.setUseAble(false);
        infoRespDTO.setUnUseReason("view");
        infoRespDTO.setDiscountPrice(new BigDecimal("0.00"));
        calculateOrderRespDTO.setActivityInfoList(Arrays.asList(infoRespDTO));
        context.setCalculateOrderRespDTO(calculateOrderRespDTO);
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("2ff6364d-0abe-4fe1-86de-ca109ada08b5");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsGoodsReduceDiscount(new BigDecimal("0.00"));
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberPreferential(new BigDecimal("0.00"));
        dineInItemDTO1.setTicketPreferential(new BigDecimal("0.00"));
        dineInItemDTO1.setDiscountPreferential(new BigDecimal("0.00"));
        dineInItemDTO1.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsDiscountPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setLimitPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setJoinSpecialsCount(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsSingleDistinctPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPriceType(0);
        dineInItemDTO1.setSpecialsActivityGuid("specialsActivityGuid");
        context.setAllItems(Arrays.asList(dineInItemDTO1));
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setGuid("2ff6364d-0abe-4fe1-86de-ca109ada08b5");
        dineInItemDTO2.setItemGuid("itemGuid");
        dineInItemDTO2.setSkuGuid("skuGuid");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO2.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setIsGoodsReduceDiscount(new BigDecimal("0.00"));
        dineInItemDTO2.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setMemberPreferential(new BigDecimal("0.00"));
        dineInItemDTO2.setTicketPreferential(new BigDecimal("0.00"));
        dineInItemDTO2.setDiscountPreferential(new BigDecimal("0.00"));
        dineInItemDTO2.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setSpecialsTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setSpecialsDiscountPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setSpecialsPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setSpecialsMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setLimitPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setJoinSpecialsCount(new BigDecimal("0.00"));
        dineInItemDTO2.setSpecialsSingleDistinctPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setMinPriceType(0);
        dineInItemDTO2.setSpecialsActivityGuid("specialsActivityGuid");
        context.setBeforeSpecialsItems(Arrays.asList(dineInItemDTO2));
        final DiscountRuleBO discountRuleBO = new DiscountRuleBO();
        final LimitSpecialsActivityDetailsVO specialsActivityDetailsVO = new LimitSpecialsActivityDetailsVO();
        specialsActivityDetailsVO.setRelationRule(0);
        final LimitSpecialsActivityItemDTO activityItemDTO1 = new LimitSpecialsActivityItemDTO();
        specialsActivityDetailsVO.setItemDTOList(Arrays.asList(activityItemDTO1));
        discountRuleBO.setSpecialsActivityDetailsVO(specialsActivityDetailsVO);
        final ResponseProductDiscount responseProductDiscount1 = new ResponseProductDiscount();
        responseProductDiscount1.setMemberRightsType(0);
        responseProductDiscount1.setDiscountValue(new BigDecimal("0.00"));
        discountRuleBO.setResponseProductDiscount(responseProductDiscount1);
        context.setDiscountRuleBO(discountRuleBO);
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        context.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));

        // Run the test
        priceCalculateHelperUnderTest.calculateAgainBySort(activityDetailsVO, skuItemList, activityItemVO,
                responseProductDiscount, context);

        // Verify the results
    }

    @Test
    public void testGetSpecialsActivityAmountBO1() {
        // Setup
        final LimitSpecialsActivityDetailsVO activityDetailsVO = new LimitSpecialsActivityDetailsVO();
        activityDetailsVO.setGuid("activityGuid");
        activityDetailsVO.setName("activityName");
        activityDetailsVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityDetailsVO.setIsLimitPeriod(0);
        activityDetailsVO.setEquitiesTimeLimitedType(0);
        activityDetailsVO.setEquitiesTimeLimitedJson("equitiesTimeLimitedJson");
        activityDetailsVO.setRelationRule(0);
        activityDetailsVO.setApplyBusiness(0);
        final LimitSpecialsActivityItemDTO activityItemDTO = new LimitSpecialsActivityItemDTO();
        activityItemDTO.setCommodityId("commodityId");
        activityItemDTO.setCommodityCode("commodityCode");
        activityItemDTO.setSpecialsType(1);
        activityItemDTO.setSpecialsNumber(new BigDecimal("0.00"));
        activityItemDTO.setLimitNumber(0);
        activityDetailsVO.setItemDTOList(Arrays.asList(activityItemDTO));

        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("2ff6364d-0abe-4fe1-86de-ca109ada08b5");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("1"));
        dineInItemDTO.setIsGoodsReduceDiscount(new BigDecimal("0.00"));
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setTicketPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsDiscountPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setLimitPrice(new BigDecimal("0.00"));
        dineInItemDTO.setJoinSpecialsCount(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsSingleDistinctPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPriceType(0);
        dineInItemDTO.setSpecialsActivityGuid("specialsActivityGuid");

        final SpecialsActivityAmountBO expectedResult = new SpecialsActivityAmountBO();
        expectedResult.setCurrentCount(null);
        expectedResult.setOriginalPrice(null);
        expectedResult.setSpecialsNumber(null);
        expectedResult.setLimit(false);
        expectedResult.setLimitNumber(null);
        expectedResult.setMinPrice(new BigDecimal("0.00"));
        expectedResult.setMinPriceType(0);
        final SpecialsActivityAmountDTO activityAmountDTO = new SpecialsActivityAmountDTO();
        activityAmountDTO.setTotalPrice(null);
        activityAmountDTO.setDiscountPrice(null);
        activityAmountDTO.setSpecialsPrice(null);
        activityAmountDTO.setMemberPrice(null);
        activityAmountDTO.setLimitPrice(new BigDecimal("0"));
        activityAmountDTO.setJoinSpecialsCount(null);
        activityAmountDTO.setSpecialsSingleDistinctPrice(null);
        expectedResult.setActivityAmountDTO(activityAmountDTO);
        expectedResult.setIsFirst(true);
        expectedResult.setIsReplace(null);

        // Run the test
        final SpecialsActivityAmountBO result = priceCalculateHelperUnderTest.getSpecialsActivityAmountBO(
                activityDetailsVO, dineInItemDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCalculateMinPrice2() {
        // Setup
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("2ff6364d-0abe-4fe1-86de-ca109ada08b5");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsGoodsReduceDiscount(new BigDecimal("0.00"));
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setTicketPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsDiscountPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setLimitPrice(new BigDecimal("0.00"));
        dineInItemDTO.setJoinSpecialsCount(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsSingleDistinctPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPriceType(0);
        dineInItemDTO.setSpecialsActivityGuid("specialsActivityGuid");

        final LimitSpecialsActivityItemVO activityItemVO = new LimitSpecialsActivityItemVO();
        activityItemVO.setCommodityId("commodityId");
        activityItemVO.setCommodityCode("commodityCode");
        activityItemVO.setSpecialsType(1);
        activityItemVO.setSpecialsNumber(new BigDecimal("0.00"));
        activityItemVO.setLimitNumber(0);
        activityItemVO.setItemUseNumber(0);
        activityItemVO.setActivityGuid("activityGuid");
        activityItemVO.setActivityName("activityName");
        activityItemVO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityItemVO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        activityItemVO.setState(0);
        activityItemVO.setIsLimitPeriod(0);
        activityItemVO.setLimitPeriodType(0);
        activityItemVO.setLimitPeriodJson("equitiesTimeLimitedJson");
        activityItemVO.setRelationRule(0);
        activityItemVO.setApplyBusiness(0);

        final SpecialsActivityAmountBO amountBO = new SpecialsActivityAmountBO();
        amountBO.setCurrentCount(new BigDecimal("0.00"));
        amountBO.setOriginalPrice(new BigDecimal("0.00"));
        amountBO.setSpecialsNumber(new BigDecimal("0.00"));
        amountBO.setLimit(false);
        amountBO.setLimitNumber(new BigDecimal("0.00"));
        amountBO.setMinPrice(new BigDecimal("0.00"));
        amountBO.setMinPriceType(0);
        final SpecialsActivityAmountDTO activityAmountDTO = new SpecialsActivityAmountDTO();
        activityAmountDTO.setTotalPrice(new BigDecimal("0.00"));
        activityAmountDTO.setDiscountPrice(new BigDecimal("0.00"));
        activityAmountDTO.setSpecialsPrice(new BigDecimal("0.00"));
        activityAmountDTO.setMemberPrice(new BigDecimal("0.00"));
        activityAmountDTO.setLimitPrice(new BigDecimal("0.00"));
        activityAmountDTO.setJoinSpecialsCount(new BigDecimal("0.00"));
        activityAmountDTO.setSpecialsSingleDistinctPrice(new BigDecimal("0.00"));
        amountBO.setActivityAmountDTO(activityAmountDTO);
        amountBO.setIsFirst(false);
        amountBO.setIsReplace(false);

        final SpecialsActivityAmountDTO expectedResult = new SpecialsActivityAmountDTO();
        expectedResult.setTotalPrice(new BigDecimal("0.0000"));
        expectedResult.setDiscountPrice(new BigDecimal("0.0000"));
        expectedResult.setSpecialsPrice(new BigDecimal("0.0000"));
        expectedResult.setMemberPrice(new BigDecimal("0"));
        expectedResult.setLimitPrice(new BigDecimal("0"));
        expectedResult.setJoinSpecialsCount(new BigDecimal("0.00"));
        expectedResult.setSpecialsSingleDistinctPrice(new BigDecimal("0.00"));

        // Run the test
        final SpecialsActivityAmountDTO result = priceCalculateHelperUnderTest.calculateMinPrice(dineInItemDTO,
                activityItemVO, amountBO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCalculateSpecialsActivityByMemberDiscount() {
        // Setup
        final DiscountContext context = new DiscountContext();
        final CalculateOrderRespDTO calculateOrderRespDTO = new CalculateOrderRespDTO();
        calculateOrderRespDTO.setOrderSurplusFee(new BigDecimal("0.00"));
        calculateOrderRespDTO.setOrderSurplusFeeBySkipSpecials(new BigDecimal("0.00"));
        final ActivitySelectDTO activitySelectDTO = new ActivitySelectDTO();
        activitySelectDTO.setActivityType(0);
        calculateOrderRespDTO.setActivitySelectList(Arrays.asList(activitySelectDTO));
        final MarketingActivityInfoRespDTO infoRespDTO = new MarketingActivityInfoRespDTO();
        infoRespDTO.setActivityGuid("activityGuid");
        infoRespDTO.setUseAble(false);
        infoRespDTO.setUnUseReason("view");
        infoRespDTO.setDiscountPrice(new BigDecimal("0.00"));
        calculateOrderRespDTO.setActivityInfoList(Arrays.asList(infoRespDTO));
        context.setCalculateOrderRespDTO(calculateOrderRespDTO);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("2ff6364d-0abe-4fe1-86de-ca109ada08b5");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsGoodsReduceDiscount(new BigDecimal("0.00"));
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setTicketPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsDiscountPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setLimitPrice(new BigDecimal("0.00"));
        dineInItemDTO.setJoinSpecialsCount(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsSingleDistinctPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPriceType(0);
        dineInItemDTO.setSpecialsActivityGuid("specialsActivityGuid");
        context.setAllItems(Arrays.asList(dineInItemDTO));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("2ff6364d-0abe-4fe1-86de-ca109ada08b5");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsGoodsReduceDiscount(new BigDecimal("0.00"));
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberPreferential(new BigDecimal("0.00"));
        dineInItemDTO1.setTicketPreferential(new BigDecimal("0.00"));
        dineInItemDTO1.setDiscountPreferential(new BigDecimal("0.00"));
        dineInItemDTO1.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsDiscountPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setLimitPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setJoinSpecialsCount(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsSingleDistinctPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPriceType(0);
        dineInItemDTO1.setSpecialsActivityGuid("specialsActivityGuid");
        context.setBeforeSpecialsItems(Arrays.asList(dineInItemDTO1));
        Map<String, DineInItemDTO> dineInItemDTOMap = new HashMap<>();
        dineInItemDTOMap.put("orderItemGuid", dineInItemDTO1);
        context.setDineInItemDTOMap(dineInItemDTOMap);
        final DiscountRuleBO discountRuleBO = new DiscountRuleBO();
        final LimitSpecialsActivityDetailsVO specialsActivityDetailsVO = new LimitSpecialsActivityDetailsVO();
        specialsActivityDetailsVO.setRelationRule(0);
        final LimitSpecialsActivityItemDTO activityItemDTO = new LimitSpecialsActivityItemDTO();
        specialsActivityDetailsVO.setItemDTOList(Arrays.asList(activityItemDTO));
        discountRuleBO.setSpecialsActivityDetailsVO(specialsActivityDetailsVO);
        final ResponseProductDiscount responseProductDiscount = new ResponseProductDiscount();
        responseProductDiscount.setMemberRightsType(0);
        responseProductDiscount.setDiscountValue(new BigDecimal("0.00"));
        discountRuleBO.setResponseProductDiscount(responseProductDiscount);
        context.setDiscountRuleBO(discountRuleBO);
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        context.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));

        final DiscountFeeDetailDTO member = new DiscountFeeDetailDTO();
        member.setGuid("ba4dceda-29a7-4381-9cce-73fc13d49ebb");
        member.setOrderGuid("orderGuid");
        member.setDiscountName("discountName");
        member.setDiscountType(0);
        member.setDiscountFee(new BigDecimal("0.00"));

        final com.holderzone.holder.saas.member.terminal.dto.order.ResponseDiscount pureDiscount = new com.holderzone.holder.saas.member.terminal.dto.order.ResponseDiscount();
        pureDiscount.setDeductionMoney(new BigDecimal("0.00"));
        final RequestDishInfo dishInfo = new RequestDishInfo();
        dishInfo.setOrderItemGuid("orderItemGuid");
        dishInfo.setDishSpecification("dishSpecification");
        dishInfo.setDiscountMoney(new BigDecimal("0.00"));
        pureDiscount.setRequestDishInfoList(Arrays.asList(dishInfo));

        final DiscountFeeDetailDTO limitSpecialsActivity = new DiscountFeeDetailDTO();
        limitSpecialsActivity.setGuid("ba4dceda-29a7-4381-9cce-73fc13d49ebb");
        limitSpecialsActivity.setOrderGuid("orderGuid");
        limitSpecialsActivity.setDiscountName("discountName");
        limitSpecialsActivity.setDiscountType(0);
        limitSpecialsActivity.setDiscountFee(new BigDecimal("0.00"));

        final com.holderzone.holder.saas.member.terminal.dto.order.ResponseDiscount discountShare = new com.holderzone.holder.saas.member.terminal.dto.order.ResponseDiscount();
        discountShare.setDeductionMoney(new BigDecimal("0.00"));
        final RequestDishInfo dishInfo1 = new RequestDishInfo();
        dishInfo1.setOrderItemGuid("orderItemGuid");
        dishInfo1.setDishSpecification("dishSpecification");
        dishInfo1.setDiscountMoney(new BigDecimal("0.00"));
        discountShare.setRequestDishInfoList(Arrays.asList(dishInfo1));

        // Run the test
        priceCalculateHelperUnderTest.calculateSpecialsActivityByMemberDiscount(context, member, pureDiscount,
                limitSpecialsActivity, discountShare);

        // Verify the results
    }

    @Test
    public void testRespActivityHandle() {
        // Setup
        final DiscountContext context = new DiscountContext();
        final CalculateOrderRespDTO calculateOrderRespDTO = new CalculateOrderRespDTO();
        calculateOrderRespDTO.setOrderSurplusFee(new BigDecimal("0.00"));
        calculateOrderRespDTO.setOrderSurplusFeeBySkipSpecials(new BigDecimal("0.00"));
        final ActivitySelectDTO activitySelectDTO = new ActivitySelectDTO();
        activitySelectDTO.setActivityType(0);
        calculateOrderRespDTO.setActivitySelectList(Arrays.asList(activitySelectDTO));
        final MarketingActivityInfoRespDTO infoRespDTO = new MarketingActivityInfoRespDTO();
        infoRespDTO.setActivityGuid("activityGuid");
        infoRespDTO.setUseAble(false);
        infoRespDTO.setUnUseReason("view");
        infoRespDTO.setDiscountPrice(new BigDecimal("0.00"));
        calculateOrderRespDTO.setActivityInfoList(Arrays.asList(infoRespDTO));
        context.setCalculateOrderRespDTO(calculateOrderRespDTO);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("2ff6364d-0abe-4fe1-86de-ca109ada08b5");
        dineInItemDTO.setItemGuid("itemGuid");
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsGoodsReduceDiscount(new BigDecimal("0.00"));
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setTicketPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountPreferential(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsDiscountPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsPrice(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setLimitPrice(new BigDecimal("0.00"));
        dineInItemDTO.setJoinSpecialsCount(new BigDecimal("0.00"));
        dineInItemDTO.setSpecialsSingleDistinctPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMinPriceType(0);
        dineInItemDTO.setSpecialsActivityGuid("specialsActivityGuid");
        context.setAllItems(Arrays.asList(dineInItemDTO));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("2ff6364d-0abe-4fe1-86de-ca109ada08b5");
        dineInItemDTO1.setItemGuid("itemGuid");
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsGoodsReduceDiscount(new BigDecimal("0.00"));
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberPreferential(new BigDecimal("0.00"));
        dineInItemDTO1.setTicketPreferential(new BigDecimal("0.00"));
        dineInItemDTO1.setDiscountPreferential(new BigDecimal("0.00"));
        dineInItemDTO1.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsDiscountPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setLimitPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setJoinSpecialsCount(new BigDecimal("0.00"));
        dineInItemDTO1.setSpecialsSingleDistinctPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMinPriceType(0);
        dineInItemDTO1.setSpecialsActivityGuid("specialsActivityGuid");
        context.setBeforeSpecialsItems(Arrays.asList(dineInItemDTO1));
        final DiscountRuleBO discountRuleBO = new DiscountRuleBO();
        final LimitSpecialsActivityDetailsVO specialsActivityDetailsVO = new LimitSpecialsActivityDetailsVO();
        specialsActivityDetailsVO.setRelationRule(0);
        final LimitSpecialsActivityItemDTO activityItemDTO = new LimitSpecialsActivityItemDTO();
        specialsActivityDetailsVO.setItemDTOList(Arrays.asList(activityItemDTO));
        discountRuleBO.setSpecialsActivityDetailsVO(specialsActivityDetailsVO);
        final ResponseProductDiscount responseProductDiscount = new ResponseProductDiscount();
        responseProductDiscount.setMemberRightsType(0);
        responseProductDiscount.setDiscountValue(new BigDecimal("0.00"));
        discountRuleBO.setResponseProductDiscount(responseProductDiscount);
        context.setDiscountRuleBO(discountRuleBO);
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        context.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));

        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setGuid("2ff6364d-0abe-4fe1-86de-ca109ada08b5");
        dineInItemDTO2.setItemGuid("itemGuid");
        dineInItemDTO2.setSkuGuid("skuGuid");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO2.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setIsGoodsReduceDiscount(new BigDecimal("0.00"));
        dineInItemDTO2.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setMemberPreferential(new BigDecimal("0.00"));
        dineInItemDTO2.setTicketPreferential(new BigDecimal("0.00"));
        dineInItemDTO2.setDiscountPreferential(new BigDecimal("0.00"));
        dineInItemDTO2.setDiscountTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setSpecialsTotalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setSpecialsDiscountPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setSpecialsPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setSpecialsMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setLimitPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setJoinSpecialsCount(new BigDecimal("0.00"));
        dineInItemDTO2.setSpecialsSingleDistinctPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setMinPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setMinPriceType(0);
        dineInItemDTO2.setSpecialsActivityGuid("specialsActivityGuid");
        final List<DineInItemDTO> allItems = Arrays.asList(dineInItemDTO2);

        // Run the test
        priceCalculateHelperUnderTest.respActivityHandle(context, allItems);

        // Verify the results
    }

    @Test
    public void testCalculatePreferentialPrice() {
        // Setup
        final ResponseClientMarketActivity activity = new ResponseClientMarketActivity();
        activity.setGuid("5b069a20-1e2a-4e5a-bc61-c84215adb179");
        activity.setUseAble(false);
        activity.setUnUseReason("tips");
        activity.setDiscountPrice(new BigDecimal("0.00"));
        activity.setActivitieType(0);
        activity.setAllType(0);
        final ResponseActivitieProduct product = new ResponseActivitieProduct();
        product.setProductGuid("productGuid");
        activity.setProductList(Arrays.asList(product));
        final RequestActivityFullDiscountRule requestActivityFullDiscountRule = new RequestActivityFullDiscountRule();
        requestActivityFullDiscountRule.setFullMoney(new BigDecimal("0.00"));
        requestActivityFullDiscountRule.setDiscount(new BigDecimal("0.00"));
        activity.setFullDiscountRuleList(Arrays.asList(requestActivityFullDiscountRule));
        activity.setPromotionType(0);
        final RequestActivityFullDiscountRule requestActivityFullDiscountRule1 = new RequestActivityFullDiscountRule();
        requestActivityFullDiscountRule1.setFullMoney(new BigDecimal("0.00"));
        requestActivityFullDiscountRule1.setDiscount(new BigDecimal("0.00"));
        activity.setFullReductionLadderRuleList(Arrays.asList(requestActivityFullDiscountRule1));
        final RequestActivityFullDiscountRule fullReductionLoopRule = new RequestActivityFullDiscountRule();
        fullReductionLoopRule.setFullMoney(new BigDecimal("0.00"));
        fullReductionLoopRule.setDiscount(new BigDecimal("0.00"));
        activity.setFullReductionLoopRule(fullReductionLoopRule);

        final ResponseMarketActivityUse respDTO = new ResponseMarketActivityUse();
        respDTO.setActivityGuid("activityGuid");
        final com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo requestDishInfo = new com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo();
        requestDishInfo.setDishGuid("dishGuid");
        requestDishInfo.setParentDishGuid("parentDishGuid");
        requestDishInfo.setPayPrice(new BigDecimal("0.00"));
        requestDishInfo.setDiscountMoney(new BigDecimal("0.00"));
        respDTO.setDishInfoDTOList(Arrays.asList(requestDishInfo));
        respDTO.setDiscountMoney(new BigDecimal("0.00"));

        // Configure ItemClientService.getItemInfoList3(...).
        final ItemStringListDTO stringListDTO = new ItemStringListDTO();
        stringListDTO.setDataList(Arrays.asList("value"));
        stringListDTO.setItemList(Arrays.asList("value"));
        stringListDTO.setRecordId(1L);
        when(mockItemClientService.getItemInfoList3(stringListDTO)).thenReturn(Arrays.asList("value"));

        // Run the test
        priceCalculateHelperUnderTest.calculatePreferentialPrice(activity, respDTO);

        // Verify the results
    }

    @Test
    public void testCalculatePreferentialPrice_ItemClientServiceReturnsNoItems() {
        // Setup
        final ResponseClientMarketActivity activity = new ResponseClientMarketActivity();
        activity.setGuid("5b069a20-1e2a-4e5a-bc61-c84215adb179");
        activity.setUseAble(false);
        activity.setUnUseReason("tips");
        activity.setDiscountPrice(new BigDecimal("0.00"));
        activity.setActivitieType(0);
        activity.setAllType(0);
        final ResponseActivitieProduct product = new ResponseActivitieProduct();
        product.setProductGuid("productGuid");
        activity.setProductList(Arrays.asList(product));
        final RequestActivityFullDiscountRule requestActivityFullDiscountRule = new RequestActivityFullDiscountRule();
        requestActivityFullDiscountRule.setFullMoney(new BigDecimal("0.00"));
        requestActivityFullDiscountRule.setDiscount(new BigDecimal("0.00"));
        activity.setFullDiscountRuleList(Arrays.asList(requestActivityFullDiscountRule));
        activity.setPromotionType(0);
        final RequestActivityFullDiscountRule requestActivityFullDiscountRule1 = new RequestActivityFullDiscountRule();
        requestActivityFullDiscountRule1.setFullMoney(new BigDecimal("0.00"));
        requestActivityFullDiscountRule1.setDiscount(new BigDecimal("0.00"));
        activity.setFullReductionLadderRuleList(Arrays.asList(requestActivityFullDiscountRule1));
        final RequestActivityFullDiscountRule fullReductionLoopRule = new RequestActivityFullDiscountRule();
        fullReductionLoopRule.setFullMoney(new BigDecimal("0.00"));
        fullReductionLoopRule.setDiscount(new BigDecimal("0.00"));
        activity.setFullReductionLoopRule(fullReductionLoopRule);

        final ResponseMarketActivityUse respDTO = new ResponseMarketActivityUse();
        respDTO.setActivityGuid("activityGuid");
        final com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo requestDishInfo = new com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo();
        requestDishInfo.setDishGuid("dishGuid");
        requestDishInfo.setParentDishGuid("parentDishGuid");
        requestDishInfo.setPayPrice(new BigDecimal("0.00"));
        requestDishInfo.setDiscountMoney(new BigDecimal("0.00"));
        respDTO.setDishInfoDTOList(Arrays.asList(requestDishInfo));
        respDTO.setDiscountMoney(new BigDecimal("0.00"));

        // Configure ItemClientService.getItemInfoList3(...).
        final ItemStringListDTO stringListDTO = new ItemStringListDTO();
        stringListDTO.setDataList(Arrays.asList("value"));
        stringListDTO.setItemList(Arrays.asList("value"));
        stringListDTO.setRecordId(0L);
        when(mockItemClientService.getItemInfoList3(stringListDTO)).thenReturn(Arrays.asList("value"));

        // Run the test
        priceCalculateHelperUnderTest.calculatePreferentialPrice(activity, respDTO);

        // Verify the results
    }

    @Test
    public void testCalculatePreferentialPrice_ItemClientServiceReturnsNoItems1() {
        // Setup
        final ResponseClientMarketActivity activity = new ResponseClientMarketActivity();
        activity.setGuid("5b069a20-1e2a-4e5a-bc61-c84215adb179");
        activity.setUseAble(false);
        activity.setUnUseReason("tips");
        activity.setDiscountPrice(new BigDecimal("0.00"));
        activity.setActivitieType(1);
        activity.setAllType(2);
        final ResponseActivitieProduct product = new ResponseActivitieProduct();
        product.setProductGuid("productGuid");
        activity.setProductList(Arrays.asList(product));
        final RequestActivityFullDiscountRule requestActivityFullDiscountRule = new RequestActivityFullDiscountRule();
        requestActivityFullDiscountRule.setFullMoney(new BigDecimal("0.00"));
        requestActivityFullDiscountRule.setDiscount(new BigDecimal("0.00"));
        activity.setFullDiscountRuleList(Arrays.asList(requestActivityFullDiscountRule));
        activity.setPromotionType(0);
        final RequestActivityFullDiscountRule requestActivityFullDiscountRule1 = new RequestActivityFullDiscountRule();
        requestActivityFullDiscountRule1.setFullMoney(new BigDecimal("0.00"));
        requestActivityFullDiscountRule1.setDiscount(new BigDecimal("0.00"));
        activity.setFullReductionLadderRuleList(Arrays.asList(requestActivityFullDiscountRule1));
        final RequestActivityFullDiscountRule fullReductionLoopRule = new RequestActivityFullDiscountRule();
        fullReductionLoopRule.setFullMoney(new BigDecimal("0.00"));
        fullReductionLoopRule.setDiscount(new BigDecimal("0.00"));
        activity.setFullReductionLoopRule(fullReductionLoopRule);

        final ResponseMarketActivityUse respDTO = new ResponseMarketActivityUse();
        respDTO.setActivityGuid("activityGuid");
        final com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo requestDishInfo = new com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo();
        requestDishInfo.setDishGuid("dishGuid");
        requestDishInfo.setParentDishGuid("parentDishGuid");
        requestDishInfo.setPayPrice(new BigDecimal("0.00"));
        requestDishInfo.setDiscountMoney(new BigDecimal("0.00"));
        respDTO.setDishInfoDTOList(Arrays.asList(requestDishInfo));
        respDTO.setDiscountMoney(new BigDecimal("0.00"));

        // Configure ItemClientService.getItemInfoList3(...).
        final ItemStringListDTO stringListDTO = new ItemStringListDTO();
        stringListDTO.setDataList(Arrays.asList("value"));
        stringListDTO.setItemList(Arrays.asList("value"));
        stringListDTO.setRecordId(0L);
        when(mockItemClientService.getItemInfoList3(stringListDTO)).thenReturn(Arrays.asList("value"));

        // Run the test
        priceCalculateHelperUnderTest.calculatePreferentialPrice(activity, respDTO);

        // Verify the results
    }

    @Test
    public void testCalculateItemDiscountPrice() {
        // Setup
        final ResponseClientMarketActivity activity = new ResponseClientMarketActivity();
        activity.setGuid("5b069a20-1e2a-4e5a-bc61-c84215adb179");
        activity.setUseAble(false);
        activity.setUnUseReason("tips");
        activity.setDiscountPrice(new BigDecimal("0.00"));
        activity.setActivitieType(0);
        activity.setAllType(0);
        final ResponseActivitieProduct product = new ResponseActivitieProduct();
        product.setProductGuid("productGuid");
        activity.setProductList(Arrays.asList(product));
        final RequestActivityFullDiscountRule requestActivityFullDiscountRule = new RequestActivityFullDiscountRule();
        requestActivityFullDiscountRule.setFullMoney(new BigDecimal("0.00"));
        requestActivityFullDiscountRule.setDiscount(new BigDecimal("0.00"));
        activity.setFullDiscountRuleList(Arrays.asList(requestActivityFullDiscountRule));
        activity.setPromotionType(0);
        final RequestActivityFullDiscountRule requestActivityFullDiscountRule1 = new RequestActivityFullDiscountRule();
        requestActivityFullDiscountRule1.setFullMoney(new BigDecimal("0.00"));
        requestActivityFullDiscountRule1.setDiscount(new BigDecimal("0.00"));
        activity.setFullReductionLadderRuleList(Arrays.asList(requestActivityFullDiscountRule1));
        final RequestActivityFullDiscountRule fullReductionLoopRule = new RequestActivityFullDiscountRule();
        fullReductionLoopRule.setFullMoney(new BigDecimal("0.00"));
        fullReductionLoopRule.setDiscount(new BigDecimal("0.00"));
        activity.setFullReductionLoopRule(fullReductionLoopRule);

        final ResponseMarketActivityUse respDTO = new ResponseMarketActivityUse();
        respDTO.setActivityGuid("activityGuid");
        final com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo requestDishInfo = new com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo();
        requestDishInfo.setDishGuid("dishGuid");
        requestDishInfo.setParentDishGuid("parentDishGuid");
        requestDishInfo.setPayPrice(new BigDecimal("0.00"));
        requestDishInfo.setDiscountMoney(new BigDecimal("0.00"));
        respDTO.setDishInfoDTOList(Arrays.asList(requestDishInfo));
        respDTO.setDiscountMoney(new BigDecimal("0.00"));

        // Configure ItemClientService.getItemInfoList3(...).
        final ItemStringListDTO stringListDTO = new ItemStringListDTO();
        stringListDTO.setDataList(Arrays.asList("value"));
        stringListDTO.setItemList(Arrays.asList("value"));
        stringListDTO.setRecordId(1L);
        when(mockItemClientService.getItemInfoList3(stringListDTO)).thenReturn(Arrays.asList("value"));

        // Run the test
        priceCalculateHelperUnderTest.calculateItemDiscountPrice(activity, respDTO);

        // Verify the results
    }

    @Test
    public void testCalculateItemDiscountPrice_ItemClientServiceReturnsNoItems() {
        // Setup
        final ResponseClientMarketActivity activity = new ResponseClientMarketActivity();
        activity.setGuid("5b069a20-1e2a-4e5a-bc61-c84215adb179");
        activity.setUseAble(false);
        activity.setUnUseReason("tips");
        activity.setDiscountPrice(new BigDecimal("0.00"));
        activity.setActivitieType(0);
        activity.setAllType(0);
        final ResponseActivitieProduct product = new ResponseActivitieProduct();
        product.setProductGuid("productGuid");
        activity.setProductList(Arrays.asList(product));
        final RequestActivityFullDiscountRule requestActivityFullDiscountRule = new RequestActivityFullDiscountRule();
        requestActivityFullDiscountRule.setFullMoney(new BigDecimal("0.00"));
        requestActivityFullDiscountRule.setDiscount(new BigDecimal("0.00"));
        activity.setFullDiscountRuleList(Arrays.asList(requestActivityFullDiscountRule));
        activity.setPromotionType(0);
        final RequestActivityFullDiscountRule requestActivityFullDiscountRule1 = new RequestActivityFullDiscountRule();
        requestActivityFullDiscountRule1.setFullMoney(new BigDecimal("0.00"));
        requestActivityFullDiscountRule1.setDiscount(new BigDecimal("0.00"));
        activity.setFullReductionLadderRuleList(Arrays.asList(requestActivityFullDiscountRule1));
        final RequestActivityFullDiscountRule fullReductionLoopRule = new RequestActivityFullDiscountRule();
        fullReductionLoopRule.setFullMoney(new BigDecimal("0.00"));
        fullReductionLoopRule.setDiscount(new BigDecimal("0.00"));
        activity.setFullReductionLoopRule(fullReductionLoopRule);

        final ResponseMarketActivityUse respDTO = new ResponseMarketActivityUse();
        respDTO.setActivityGuid("activityGuid");
        final com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo requestDishInfo = new com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo();
        requestDishInfo.setDishGuid("dishGuid");
        requestDishInfo.setParentDishGuid("parentDishGuid");
        requestDishInfo.setPayPrice(new BigDecimal("0.00"));
        requestDishInfo.setDiscountMoney(new BigDecimal("0.00"));
        respDTO.setDishInfoDTOList(Arrays.asList(requestDishInfo));
        respDTO.setDiscountMoney(new BigDecimal("0.00"));

        // Configure ItemClientService.getItemInfoList3(...).
        final ItemStringListDTO stringListDTO = new ItemStringListDTO();
        stringListDTO.setDataList(Arrays.asList("value"));
        stringListDTO.setItemList(Arrays.asList("value"));
        stringListDTO.setRecordId(0L);
        when(mockItemClientService.getItemInfoList3(stringListDTO)).thenReturn(Arrays.asList("value"));

        // Run the test
        priceCalculateHelperUnderTest.calculateItemDiscountPrice(activity, respDTO);

        // Verify the results
    }

    @Test
    public void testCalculateItemDiscountPrice_ItemClientServiceReturnsNoItems_0() {
        // Setup
        final ResponseClientMarketActivity activity = new ResponseClientMarketActivity();
        activity.setGuid("5b069a20-1e2a-4e5a-bc61-c84215adb179");
        activity.setUseAble(false);
        activity.setUnUseReason("tips");
        activity.setDiscountPrice(new BigDecimal("0.00"));
        activity.setActivitieType(1);
        activity.setAllType(0);
        final ResponseActivitieProduct product = new ResponseActivitieProduct();
        product.setProductGuid("productGuid");
        activity.setProductList(Arrays.asList(product));
        final RequestActivityFullDiscountRule requestActivityFullDiscountRule = new RequestActivityFullDiscountRule();
        requestActivityFullDiscountRule.setFullMoney(new BigDecimal("0.00"));
        requestActivityFullDiscountRule.setDiscount(new BigDecimal("0.00"));
        activity.setFullDiscountRuleList(Arrays.asList(requestActivityFullDiscountRule));
        activity.setPromotionType(0);
        final RequestActivityFullDiscountRule requestActivityFullDiscountRule1 = new RequestActivityFullDiscountRule();
        requestActivityFullDiscountRule1.setFullMoney(new BigDecimal("0.00"));
        requestActivityFullDiscountRule1.setDiscount(new BigDecimal("0.00"));
        activity.setFullReductionLadderRuleList(Arrays.asList(requestActivityFullDiscountRule1));
        final RequestActivityFullDiscountRule fullReductionLoopRule = new RequestActivityFullDiscountRule();
        fullReductionLoopRule.setFullMoney(new BigDecimal("0.00"));
        fullReductionLoopRule.setDiscount(new BigDecimal("0.00"));
        activity.setFullReductionLoopRule(fullReductionLoopRule);

        final ResponseMarketActivityUse respDTO = new ResponseMarketActivityUse();
        respDTO.setActivityGuid("activityGuid");
        final com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo requestDishInfo = new com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo();
        requestDishInfo.setDishGuid("dishGuid");
        requestDishInfo.setParentDishGuid("parentDishGuid");
        requestDishInfo.setPayPrice(new BigDecimal("0.00"));
        requestDishInfo.setDiscountMoney(new BigDecimal("0.00"));
        respDTO.setDishInfoDTOList(Arrays.asList(requestDishInfo));
        respDTO.setDiscountMoney(new BigDecimal("0.00"));

        // Configure ItemClientService.getItemInfoList3(...).
        final ItemStringListDTO stringListDTO = new ItemStringListDTO();
        stringListDTO.setDataList(Arrays.asList("value"));
        stringListDTO.setItemList(Arrays.asList("value"));
        stringListDTO.setRecordId(0L);
        when(mockItemClientService.getItemInfoList3(stringListDTO)).thenReturn(Arrays.asList("value"));

        // Run the test
        priceCalculateHelperUnderTest.calculateItemDiscountPrice(activity, respDTO);

        // Verify the results
    }
}
