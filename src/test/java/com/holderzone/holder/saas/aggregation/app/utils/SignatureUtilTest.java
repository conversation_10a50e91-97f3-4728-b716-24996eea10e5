package com.holderzone.holder.saas.aggregation.app.utils;

import org.junit.Test;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

public class SignatureUtilTest {

    @Test
    public void testGenerateSignature() throws Exception {
        assertThat(SignatureUtil.generateSignature("data", "secret")).isEqualTo("GywWt1vSqHDBFBU8zaW8/KYzFLxyL6Fg1pDeEzzLuds=");

    }

    @Test
    public void testValid() throws Exception {
        assertThat(SignatureUtil.valid("data", "secret", "GywWt1vSqHDBFBU8zaW8/KYzFLxyL6Fg1pDeEzzLuds=")).isTrue();
    }
}
