package com.holderzone.holder.saas.aggregation.app.utils;

import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class RedisKeyUtilTest {

    @Test
    public void testGetKey() {
        assertEquals("result", RedisKeyUtil.getKey("businessGroup", "businessKey"));
    }

    @Test
    public void testGetHstOrderKey() {
        assertEquals("result", RedisKeyUtil.getHstOrderKey("businessKey"));
    }

    @Test
    public void testGetHstOrderItemKey() {
        assertEquals("result", RedisKeyUtil.getHstOrderItemKey("businessKey"));
    }

    @Test
    public void testGetHstOrderTradeKey() {
        assertEquals("result", RedisKeyUtil.getHstOrderTradeKey("businessKey"));
    }

    @Test
    public void testGetHstOrderVersionKey() {
        assertEquals("result", RedisKeyUtil.getHstOrderVersionKey("businessKey"));
    }
}
