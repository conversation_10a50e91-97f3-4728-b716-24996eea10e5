package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.holder.saas.aggregation.app.config.ZhuanCanConfig;
import com.holderzone.holder.saas.aggregation.app.service.TableService;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.account.NewMemberInfoClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.order.VolumeClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInBillClientService;
import com.holderzone.holder.saas.aggregation.app.support.TerminalMemberSupport;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberCard;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.volume.RequestUpdateVolumeRelevance;
import com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList;
import com.holderzone.saas.store.dto.order.request.bill.BilMemberCardCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DineInBillAppServiceImplTest {

    @Mock
    private NewMemberInfoClientService mockMemberInfoClientService;
    @Mock
    private DineInBillClientService mockDineInBillClientService;
    @Mock
    private ZhuanCanConfig mockZhuanCanConfig;
    @Mock
    private VolumeClientService mockVolumeClientService;
    @Mock
    private TableService mockTableService;
    @Mock
    private TerminalMemberSupport terminalMemberSupport;

    private DineInBillAppServiceImpl dineInBillAppServiceImplUnderTest;

    @Before
    public void setUp() {
        dineInBillAppServiceImplUnderTest = new DineInBillAppServiceImpl(mockMemberInfoClientService,
                mockDineInBillClientService, mockTableService, mockZhuanCanConfig, mockVolumeClientService,terminalMemberSupport);
    }

    @Test
    public void testGetMemberCardAndCalculate() {
        // Setup
        final BilMemberCardCalculateReqDTO reqDTO = new BilMemberCardCalculateReqDTO();
        reqDTO.setOperSubjectGuid("operSubjectGuid");
        reqDTO.setPhoneNumOrCardNum("memberPhone");
        reqDTO.setLoginType(0);
        reqDTO.setOrderGuid("orderGuid");
        reqDTO.setMemberLogin(0);
        reqDTO.setMemberPhone("memberPhone");

        final DineinOrderDetailRespDTO expectedResult = new DineinOrderDetailRespDTO();
        expectedResult.setGuid("eae2f3f0-**************-deaef44da7ef");
        expectedResult.setStoreGuid("storeGuid");
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("discountName");
        expectedResult.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        expectedResult.setMemberGuid("memberGuid");

        // Configure NewMemberInfoClientService.getMemberInfoAndCard(...).
        final ResponseMemberAndCardInfoDTO responseMemberAndCardInfoDTO = new ResponseMemberAndCardInfoDTO();
        final ResponseMemberInfoDTO memberInfoDTO = new ResponseMemberInfoDTO();
        memberInfoDTO.setMemberQRCode("memberQRCode");
        responseMemberAndCardInfoDTO.setMemberInfoDTO(memberInfoDTO);
        final ResponseMemberCard responseMemberCard = new ResponseMemberCard();
        responseMemberCard.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCard.setCardMoney(new BigDecimal("0.00"));
        responseMemberAndCardInfoDTO.setMemberCardListRespDTOs(Arrays.asList(responseMemberCard));
        final RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO = new RequestQueryStoreAndMemberAndCard();
        queryStoreAndMemberAndCardReqDTO.setPhoneNumOrCardNum("phoneNumOrCardNum");
        queryStoreAndMemberAndCardReqDTO.setStoreGuid("storeGuid");
        queryStoreAndMemberAndCardReqDTO.setEnterpriseGuid("enterpriseGuid");
        queryStoreAndMemberAndCardReqDTO.setIsCurrentStoreCard(0);
        queryStoreAndMemberAndCardReqDTO.setCheckExpireGuid("checkExpireGuid");
        when(mockMemberInfoClientService.getMemberInfoAndCard(queryStoreAndMemberAndCardReqDTO))
                .thenReturn(responseMemberAndCardInfoDTO);

        // Configure DineInBillClientService.calculate(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("eae2f3f0-**************-deaef44da7ef");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("discountName");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setOperSubjectGuid("operSubjectGuid");
        billCalculateReqDTO.setOrderGuid("orderGuid");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setLoginType(0);
        billCalculateReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO.setMemberPhone("memberPhone");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVolumeCodes(Arrays.asList("value"));
        billCalculateReqDTO.setMemberInfoGuid("memberGuid");
        when(mockDineInBillClientService.calculate(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO);

        // Configure VolumeClientService.consumeVolumeList(...).
        final ResponseVolumeList responseVolumeList = new ResponseVolumeList();
        responseVolumeList.setVolumeCode("volumeCode");
        responseVolumeList.setVolumeInfoGuid("volumeInfoGuid");
        responseVolumeList.setMemberVolumeGuid("memberVolumeGuid");
        responseVolumeList.setVolumeInfoName("volumeInfoName");
        responseVolumeList.setVolumeStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<ResponseVolumeList> responseVolumeLists = Arrays.asList(responseVolumeList);
        when(mockVolumeClientService.consumeVolumeList("orderGuid")).thenReturn(responseVolumeLists);

        when(mockMemberInfoClientService.queryNameByPhone("memberPhone")).thenReturn("memberName");
        when(mockZhuanCanConfig.getUpdateVolumeRelevance()).thenReturn("result");

        // Run the test
        final DineinOrderDetailRespDTO result = dineInBillAppServiceImplUnderTest.getMemberCardAndCalculate(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm NewMemberInfoClientService.updateVolumeRelevance(...).
        final RequestUpdateVolumeRelevance request = new RequestUpdateVolumeRelevance();
        request.setMemberInfoGuid("memberGuid");
        request.setOrderGuid("orderGuid");
        request.setVolumeCode(Arrays.asList("value"));
        request.setOperSubjectGuid("operSubjectGuid");
        request.setEnterpriseGuid("enterpriseGuid");
        verify(mockMemberInfoClientService).updateVolumeRelevance(request);
    }

    @Test
    public void testGetMemberCardAndCalculate_DineInBillClientServiceReturnsNull() {
        // Setup
        final BilMemberCardCalculateReqDTO reqDTO = new BilMemberCardCalculateReqDTO();
        reqDTO.setOperSubjectGuid("operSubjectGuid");
        reqDTO.setPhoneNumOrCardNum("memberPhone");
        reqDTO.setLoginType(0);
        reqDTO.setOrderGuid("orderGuid");
        reqDTO.setMemberLogin(0);
        reqDTO.setMemberPhone("memberPhone");

        // Configure NewMemberInfoClientService.getMemberInfoAndCard(...).
        final ResponseMemberAndCardInfoDTO responseMemberAndCardInfoDTO = new ResponseMemberAndCardInfoDTO();
        final ResponseMemberInfoDTO memberInfoDTO = new ResponseMemberInfoDTO();
        memberInfoDTO.setMemberQRCode("memberQRCode");
        responseMemberAndCardInfoDTO.setMemberInfoDTO(memberInfoDTO);
        final ResponseMemberCard responseMemberCard = new ResponseMemberCard();
        responseMemberCard.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCard.setCardMoney(new BigDecimal("0.00"));
        responseMemberAndCardInfoDTO.setMemberCardListRespDTOs(Arrays.asList(responseMemberCard));
        final RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO = new RequestQueryStoreAndMemberAndCard();
        queryStoreAndMemberAndCardReqDTO.setPhoneNumOrCardNum("phoneNumOrCardNum");
        queryStoreAndMemberAndCardReqDTO.setStoreGuid("storeGuid");
        queryStoreAndMemberAndCardReqDTO.setEnterpriseGuid("enterpriseGuid");
        queryStoreAndMemberAndCardReqDTO.setIsCurrentStoreCard(0);
        queryStoreAndMemberAndCardReqDTO.setCheckExpireGuid("checkExpireGuid");
        when(mockMemberInfoClientService.getMemberInfoAndCard(queryStoreAndMemberAndCardReqDTO))
                .thenReturn(responseMemberAndCardInfoDTO);

        // Configure DineInBillClientService.calculate(...).
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setOperSubjectGuid("operSubjectGuid");
        billCalculateReqDTO.setOrderGuid("orderGuid");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setLoginType(0);
        billCalculateReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO.setMemberPhone("memberPhone");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVolumeCodes(Arrays.asList("value"));
        billCalculateReqDTO.setMemberInfoGuid("memberGuid");
        when(mockDineInBillClientService.calculate(billCalculateReqDTO)).thenReturn(null);

        // Configure VolumeClientService.consumeVolumeList(...).
        final ResponseVolumeList responseVolumeList = new ResponseVolumeList();
        responseVolumeList.setVolumeCode("volumeCode");
        responseVolumeList.setVolumeInfoGuid("volumeInfoGuid");
        responseVolumeList.setMemberVolumeGuid("memberVolumeGuid");
        responseVolumeList.setVolumeInfoName("volumeInfoName");
        responseVolumeList.setVolumeStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<ResponseVolumeList> responseVolumeLists = Arrays.asList(responseVolumeList);
        when(mockVolumeClientService.consumeVolumeList("orderGuid")).thenReturn(responseVolumeLists);

        when(mockMemberInfoClientService.queryNameByPhone("memberPhone")).thenReturn("memberName");
        when(mockZhuanCanConfig.getUpdateVolumeRelevance()).thenReturn("result");

        // Run the test
        final DineinOrderDetailRespDTO result = dineInBillAppServiceImplUnderTest.getMemberCardAndCalculate(reqDTO);

        // Verify the results
        assertThat(result).isNull();

        // Confirm NewMemberInfoClientService.updateVolumeRelevance(...).
        final RequestUpdateVolumeRelevance request = new RequestUpdateVolumeRelevance();
        request.setMemberInfoGuid("memberGuid");
        request.setOrderGuid("orderGuid");
        request.setVolumeCode(Arrays.asList("value"));
        request.setOperSubjectGuid("operSubjectGuid");
        request.setEnterpriseGuid("enterpriseGuid");
        verify(mockMemberInfoClientService).updateVolumeRelevance(request);
    }

    @Test
    public void testGetMemberCardAndCalculate_VolumeClientServiceReturnsNoItems() {
        // Setup
        final BilMemberCardCalculateReqDTO reqDTO = new BilMemberCardCalculateReqDTO();
        reqDTO.setOperSubjectGuid("operSubjectGuid");
        reqDTO.setPhoneNumOrCardNum("memberPhone");
        reqDTO.setLoginType(0);
        reqDTO.setOrderGuid("orderGuid");
        reqDTO.setMemberLogin(0);
        reqDTO.setMemberPhone("memberPhone");

        final DineinOrderDetailRespDTO expectedResult = new DineinOrderDetailRespDTO();
        expectedResult.setGuid("eae2f3f0-**************-deaef44da7ef");
        expectedResult.setStoreGuid("storeGuid");
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("discountName");
        expectedResult.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        expectedResult.setMemberGuid("memberGuid");

        // Configure NewMemberInfoClientService.getMemberInfoAndCard(...).
        final ResponseMemberAndCardInfoDTO responseMemberAndCardInfoDTO = new ResponseMemberAndCardInfoDTO();
        final ResponseMemberInfoDTO memberInfoDTO = new ResponseMemberInfoDTO();
        memberInfoDTO.setMemberQRCode("memberQRCode");
        responseMemberAndCardInfoDTO.setMemberInfoDTO(memberInfoDTO);
        final ResponseMemberCard responseMemberCard = new ResponseMemberCard();
        responseMemberCard.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCard.setCardMoney(new BigDecimal("0.00"));
        responseMemberAndCardInfoDTO.setMemberCardListRespDTOs(Arrays.asList(responseMemberCard));
        final RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO = new RequestQueryStoreAndMemberAndCard();
        queryStoreAndMemberAndCardReqDTO.setPhoneNumOrCardNum("phoneNumOrCardNum");
        queryStoreAndMemberAndCardReqDTO.setStoreGuid("storeGuid");
        queryStoreAndMemberAndCardReqDTO.setEnterpriseGuid("enterpriseGuid");
        queryStoreAndMemberAndCardReqDTO.setIsCurrentStoreCard(0);
        queryStoreAndMemberAndCardReqDTO.setCheckExpireGuid("checkExpireGuid");
        when(mockMemberInfoClientService.getMemberInfoAndCard(queryStoreAndMemberAndCardReqDTO))
                .thenReturn(responseMemberAndCardInfoDTO);

        // Configure DineInBillClientService.calculate(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("eae2f3f0-**************-deaef44da7ef");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("discountName");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setOperSubjectGuid("operSubjectGuid");
        billCalculateReqDTO.setOrderGuid("orderGuid");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setLoginType(0);
        billCalculateReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO.setMemberPhone("memberPhone");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVolumeCodes(Arrays.asList("value"));
        billCalculateReqDTO.setMemberInfoGuid("memberGuid");
        when(mockDineInBillClientService.calculate(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO);

        when(mockVolumeClientService.consumeVolumeList("orderGuid")).thenReturn(Collections.emptyList());
        when(mockMemberInfoClientService.queryNameByPhone("memberPhone")).thenReturn("memberName");
        when(mockZhuanCanConfig.getUpdateVolumeRelevance()).thenReturn("result");

        // Run the test
        final DineinOrderDetailRespDTO result = dineInBillAppServiceImplUnderTest.getMemberCardAndCalculate(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm NewMemberInfoClientService.updateVolumeRelevance(...).
        final RequestUpdateVolumeRelevance request = new RequestUpdateVolumeRelevance();
        request.setMemberInfoGuid("memberGuid");
        request.setOrderGuid("orderGuid");
        request.setVolumeCode(Arrays.asList("value"));
        request.setOperSubjectGuid("operSubjectGuid");
        request.setEnterpriseGuid("enterpriseGuid");
        verify(mockMemberInfoClientService).updateVolumeRelevance(request);
    }

    @Test
    public void testSwitchoverMemberAndCalculate() {
        // Setup
        final BilMemberCardCalculateReqDTO reqDTO = new BilMemberCardCalculateReqDTO();
        reqDTO.setOperSubjectGuid("operSubjectGuid");
        reqDTO.setPhoneNumOrCardNum("memberPhone");
        reqDTO.setLoginType(0);
        reqDTO.setOrderGuid("orderGuid");
        reqDTO.setMemberLogin(0);
        reqDTO.setMemberPhone("memberPhone");

        final DineinOrderDetailRespDTO expectedResult = new DineinOrderDetailRespDTO();
        expectedResult.setGuid("eae2f3f0-**************-deaef44da7ef");
        expectedResult.setStoreGuid("storeGuid");
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("discountName");
        expectedResult.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        expectedResult.setMemberGuid("memberGuid");

        // Configure DineInBillClientService.calculate(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("eae2f3f0-**************-deaef44da7ef");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("discountName");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setOperSubjectGuid("operSubjectGuid");
        billCalculateReqDTO.setOrderGuid("orderGuid");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setLoginType(0);
        billCalculateReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO.setMemberPhone("memberPhone");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVolumeCodes(Arrays.asList("value"));
        billCalculateReqDTO.setMemberInfoGuid("memberGuid");
        when(mockDineInBillClientService.calculate(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO);

        // Configure NewMemberInfoClientService.getMemberInfoAndCard(...).
        final ResponseMemberAndCardInfoDTO responseMemberAndCardInfoDTO = new ResponseMemberAndCardInfoDTO();
        final ResponseMemberInfoDTO memberInfoDTO = new ResponseMemberInfoDTO();
        memberInfoDTO.setMemberQRCode("memberQRCode");
        responseMemberAndCardInfoDTO.setMemberInfoDTO(memberInfoDTO);
        final ResponseMemberCard responseMemberCard = new ResponseMemberCard();
        responseMemberCard.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCard.setCardMoney(new BigDecimal("0.00"));
        responseMemberAndCardInfoDTO.setMemberCardListRespDTOs(Arrays.asList(responseMemberCard));
        final RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO = new RequestQueryStoreAndMemberAndCard();
        queryStoreAndMemberAndCardReqDTO.setPhoneNumOrCardNum("phoneNumOrCardNum");
        queryStoreAndMemberAndCardReqDTO.setStoreGuid("storeGuid");
        queryStoreAndMemberAndCardReqDTO.setEnterpriseGuid("enterpriseGuid");
        queryStoreAndMemberAndCardReqDTO.setIsCurrentStoreCard(0);
        queryStoreAndMemberAndCardReqDTO.setCheckExpireGuid("checkExpireGuid");
        when(mockMemberInfoClientService.getMemberInfoAndCard(queryStoreAndMemberAndCardReqDTO))
                .thenReturn(responseMemberAndCardInfoDTO);

        // Configure VolumeClientService.consumeVolumeList(...).
        final ResponseVolumeList responseVolumeList = new ResponseVolumeList();
        responseVolumeList.setVolumeCode("volumeCode");
        responseVolumeList.setVolumeInfoGuid("volumeInfoGuid");
        responseVolumeList.setMemberVolumeGuid("memberVolumeGuid");
        responseVolumeList.setVolumeInfoName("volumeInfoName");
        responseVolumeList.setVolumeStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<ResponseVolumeList> responseVolumeLists = Arrays.asList(responseVolumeList);
        when(mockVolumeClientService.consumeVolumeList("orderGuid")).thenReturn(responseVolumeLists);

        when(mockMemberInfoClientService.queryNameByPhone("memberPhone")).thenReturn("memberName");
        when(mockZhuanCanConfig.getUpdateVolumeRelevance()).thenReturn("result");

        // Run the test
        final DineinOrderDetailRespDTO result = dineInBillAppServiceImplUnderTest.switchoverMemberAndCalculate(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm NewMemberInfoClientService.updateVolumeRelevance(...).
        final RequestUpdateVolumeRelevance request = new RequestUpdateVolumeRelevance();
        request.setMemberInfoGuid("memberGuid");
        request.setOrderGuid("orderGuid");
        request.setVolumeCode(Arrays.asList("value"));
        request.setOperSubjectGuid("operSubjectGuid");
        request.setEnterpriseGuid("enterpriseGuid");
        verify(mockMemberInfoClientService).updateVolumeRelevance(request);
    }

    @Test
    public void testSwitchoverMemberAndCalculate_DineInBillClientServiceReturnsNull() {
        // Setup
        final BilMemberCardCalculateReqDTO reqDTO = new BilMemberCardCalculateReqDTO();
        reqDTO.setOperSubjectGuid("operSubjectGuid");
        reqDTO.setPhoneNumOrCardNum("memberPhone");
        reqDTO.setLoginType(0);
        reqDTO.setOrderGuid("orderGuid");
        reqDTO.setMemberLogin(0);
        reqDTO.setMemberPhone("memberPhone");

        final DineinOrderDetailRespDTO expectedResult = new DineinOrderDetailRespDTO();
        expectedResult.setGuid("eae2f3f0-**************-deaef44da7ef");
        expectedResult.setStoreGuid("storeGuid");
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("discountName");
        expectedResult.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        expectedResult.setMemberGuid("memberGuid");

        // Configure DineInBillClientService.calculate(...).
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setOperSubjectGuid("operSubjectGuid");
        billCalculateReqDTO.setOrderGuid("orderGuid");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setLoginType(0);
        billCalculateReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO.setMemberPhone("memberPhone");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVolumeCodes(Arrays.asList("value"));
        billCalculateReqDTO.setMemberInfoGuid("memberGuid");
        when(mockDineInBillClientService.calculate(billCalculateReqDTO)).thenReturn(null);

        // Configure NewMemberInfoClientService.getMemberInfoAndCard(...).
        final ResponseMemberAndCardInfoDTO responseMemberAndCardInfoDTO = new ResponseMemberAndCardInfoDTO();
        final ResponseMemberInfoDTO memberInfoDTO = new ResponseMemberInfoDTO();
        memberInfoDTO.setMemberQRCode("memberQRCode");
        responseMemberAndCardInfoDTO.setMemberInfoDTO(memberInfoDTO);
        final ResponseMemberCard responseMemberCard = new ResponseMemberCard();
        responseMemberCard.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCard.setCardMoney(new BigDecimal("0.00"));
        responseMemberAndCardInfoDTO.setMemberCardListRespDTOs(Arrays.asList(responseMemberCard));
        final RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO = new RequestQueryStoreAndMemberAndCard();
        queryStoreAndMemberAndCardReqDTO.setPhoneNumOrCardNum("phoneNumOrCardNum");
        queryStoreAndMemberAndCardReqDTO.setStoreGuid("storeGuid");
        queryStoreAndMemberAndCardReqDTO.setEnterpriseGuid("enterpriseGuid");
        queryStoreAndMemberAndCardReqDTO.setIsCurrentStoreCard(0);
        queryStoreAndMemberAndCardReqDTO.setCheckExpireGuid("checkExpireGuid");
        when(mockMemberInfoClientService.getMemberInfoAndCard(queryStoreAndMemberAndCardReqDTO))
                .thenReturn(responseMemberAndCardInfoDTO);

        // Configure VolumeClientService.consumeVolumeList(...).
        final ResponseVolumeList responseVolumeList = new ResponseVolumeList();
        responseVolumeList.setVolumeCode("volumeCode");
        responseVolumeList.setVolumeInfoGuid("volumeInfoGuid");
        responseVolumeList.setMemberVolumeGuid("memberVolumeGuid");
        responseVolumeList.setVolumeInfoName("volumeInfoName");
        responseVolumeList.setVolumeStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<ResponseVolumeList> responseVolumeLists = Arrays.asList(responseVolumeList);
        when(mockVolumeClientService.consumeVolumeList("orderGuid")).thenReturn(responseVolumeLists);

        when(mockMemberInfoClientService.queryNameByPhone("memberPhone")).thenReturn("memberName");
        when(mockZhuanCanConfig.getUpdateVolumeRelevance()).thenReturn("result");

        // Run the test
        final DineinOrderDetailRespDTO result = dineInBillAppServiceImplUnderTest.switchoverMemberAndCalculate(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm NewMemberInfoClientService.updateVolumeRelevance(...).
        final RequestUpdateVolumeRelevance request = new RequestUpdateVolumeRelevance();
        request.setMemberInfoGuid("memberGuid");
        request.setOrderGuid("orderGuid");
        request.setVolumeCode(Arrays.asList("value"));
        request.setOperSubjectGuid("operSubjectGuid");
        request.setEnterpriseGuid("enterpriseGuid");
        verify(mockMemberInfoClientService).updateVolumeRelevance(request);
    }

    @Test
    public void testSwitchoverMemberAndCalculate_VolumeClientServiceReturnsNoItems() {
        // Setup
        final BilMemberCardCalculateReqDTO reqDTO = new BilMemberCardCalculateReqDTO();
        reqDTO.setOperSubjectGuid("operSubjectGuid");
        reqDTO.setPhoneNumOrCardNum("memberPhone");
        reqDTO.setLoginType(0);
        reqDTO.setOrderGuid("orderGuid");
        reqDTO.setMemberLogin(0);
        reqDTO.setMemberPhone("memberPhone");

        final DineinOrderDetailRespDTO expectedResult = new DineinOrderDetailRespDTO();
        expectedResult.setGuid("eae2f3f0-**************-deaef44da7ef");
        expectedResult.setStoreGuid("storeGuid");
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("discountName");
        expectedResult.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        expectedResult.setMemberGuid("memberGuid");

        // Configure DineInBillClientService.calculate(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("eae2f3f0-**************-deaef44da7ef");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("discountName");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setOperSubjectGuid("operSubjectGuid");
        billCalculateReqDTO.setOrderGuid("orderGuid");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setLoginType(0);
        billCalculateReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO.setMemberPhone("memberPhone");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVolumeCodes(Arrays.asList("value"));
        billCalculateReqDTO.setMemberInfoGuid("memberGuid");
        when(mockDineInBillClientService.calculate(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO);

        // Configure NewMemberInfoClientService.getMemberInfoAndCard(...).
        final ResponseMemberAndCardInfoDTO responseMemberAndCardInfoDTO = new ResponseMemberAndCardInfoDTO();
        final ResponseMemberInfoDTO memberInfoDTO = new ResponseMemberInfoDTO();
        memberInfoDTO.setMemberQRCode("memberQRCode");
        responseMemberAndCardInfoDTO.setMemberInfoDTO(memberInfoDTO);
        final ResponseMemberCard responseMemberCard = new ResponseMemberCard();
        responseMemberCard.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCard.setCardMoney(new BigDecimal("0.00"));
        responseMemberAndCardInfoDTO.setMemberCardListRespDTOs(Arrays.asList(responseMemberCard));
        final RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO = new RequestQueryStoreAndMemberAndCard();
        queryStoreAndMemberAndCardReqDTO.setPhoneNumOrCardNum("phoneNumOrCardNum");
        queryStoreAndMemberAndCardReqDTO.setStoreGuid("storeGuid");
        queryStoreAndMemberAndCardReqDTO.setEnterpriseGuid("enterpriseGuid");
        queryStoreAndMemberAndCardReqDTO.setIsCurrentStoreCard(0);
        queryStoreAndMemberAndCardReqDTO.setCheckExpireGuid("checkExpireGuid");
        when(mockMemberInfoClientService.getMemberInfoAndCard(queryStoreAndMemberAndCardReqDTO))
                .thenReturn(responseMemberAndCardInfoDTO);

        when(mockVolumeClientService.consumeVolumeList("orderGuid")).thenReturn(Collections.emptyList());
        when(mockMemberInfoClientService.queryNameByPhone("memberPhone")).thenReturn("memberName");
        when(mockZhuanCanConfig.getUpdateVolumeRelevance()).thenReturn("result");

        // Run the test
        final DineinOrderDetailRespDTO result = dineInBillAppServiceImplUnderTest.switchoverMemberAndCalculate(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm NewMemberInfoClientService.updateVolumeRelevance(...).
        final RequestUpdateVolumeRelevance request = new RequestUpdateVolumeRelevance();
        request.setMemberInfoGuid("memberGuid");
        request.setOrderGuid("orderGuid");
        request.setVolumeCode(Arrays.asList("value"));
        request.setOperSubjectGuid("operSubjectGuid");
        request.setEnterpriseGuid("enterpriseGuid");
        verify(mockMemberInfoClientService).updateVolumeRelevance(request);
    }

    @Test
    public void testCalculate() {
        // Setup
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setOperSubjectGuid("operSubjectGuid");
        billCalculateReqDTO.setOrderGuid("orderGuid");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setLoginType(0);
        billCalculateReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO.setMemberPhone("memberPhone");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVolumeCodes(Arrays.asList("value"));
        billCalculateReqDTO.setMemberInfoGuid("memberGuid");

        final DineinOrderDetailRespDTO expectedResult = new DineinOrderDetailRespDTO();
        expectedResult.setGuid("eae2f3f0-**************-deaef44da7ef");
        expectedResult.setStoreGuid("storeGuid");
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("discountName");
        expectedResult.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        expectedResult.setMemberGuid("memberGuid");

        when(mockMemberInfoClientService.queryNameByPhone("memberPhone")).thenReturn("memberName");
        when(mockZhuanCanConfig.getUpdateVolumeRelevance()).thenReturn("result");

        // Configure DineInBillClientService.calculate(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("eae2f3f0-**************-deaef44da7ef");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("discountName");
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        final BillCalculateReqDTO billCalculateReqDTO1 = new BillCalculateReqDTO();
        billCalculateReqDTO1.setOperSubjectGuid("operSubjectGuid");
        billCalculateReqDTO1.setOrderGuid("orderGuid");
        billCalculateReqDTO1.setMemberLogin(0);
        billCalculateReqDTO1.setLoginType(0);
        billCalculateReqDTO1.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO1.setMemberPhone("memberPhone");
        billCalculateReqDTO1.setVolumeCode("volumeCode");
        billCalculateReqDTO1.setVolumeCodes(Arrays.asList("value"));
        billCalculateReqDTO1.setMemberInfoGuid("memberGuid");
        when(mockDineInBillClientService.calculate(billCalculateReqDTO1)).thenReturn(dineinOrderDetailRespDTO);

        // Run the test
        final DineinOrderDetailRespDTO result = dineInBillAppServiceImplUnderTest.calculate(billCalculateReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm NewMemberInfoClientService.updateVolumeRelevance(...).
        final RequestUpdateVolumeRelevance request = new RequestUpdateVolumeRelevance();
        request.setMemberInfoGuid("memberGuid");
        request.setOrderGuid("orderGuid");
        request.setVolumeCode(Arrays.asList("value"));
        request.setOperSubjectGuid("operSubjectGuid");
        request.setEnterpriseGuid("enterpriseGuid");
        verify(mockMemberInfoClientService).updateVolumeRelevance(request);
    }
}
