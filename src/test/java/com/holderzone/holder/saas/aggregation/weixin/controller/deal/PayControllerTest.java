package com.holderzone.holder.saas.aggregation.weixin.controller.deal;

import com.holderzone.holder.saas.aggregation.weixin.entity.dto.DealMemberPayReqDTO;
import com.holderzone.holder.saas.aggregation.weixin.service.CalculateService;
import com.holderzone.holder.saas.aggregation.weixin.service.PayService;
import com.holderzone.saas.store.dto.pay.AggPayRespDTO;
import com.holderzone.saas.store.dto.pay.KbzPayStartDTO;
import com.holderzone.saas.store.dto.weixin.WxPrepayRespDTO;
import com.holderzone.saas.store.dto.weixin.deal.*;
import com.holderzone.saas.store.dto.weixin.resp.CalculateOrderRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.PayWayRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(PayController.class)
public class PayControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PayService mockPayService;
    @MockBean
    private CalculateService mockCalculateService;

    @Test
    public void testCalculate() throws Exception {
        // Setup
        // Configure CalculateService.calculate(...).
        final CalculateOrderRespDTO calculateOrderRespDTO = new CalculateOrderRespDTO();
        calculateOrderRespDTO.setUseMemberDiscountFlag(false);
        calculateOrderRespDTO.setHasMemberPrice(false);
        calculateOrderRespDTO.setHasMemberDiscount(false);
        calculateOrderRespDTO.setMemberDiscount(new BigDecimal("0.00"));
        calculateOrderRespDTO.setUnAbleMemberDiscountFlag(false);
        final CalculateOrderDTO calculateDTO = new CalculateOrderDTO();
        calculateDTO.setDeviceType(0);
        calculateDTO.setUseMemberDiscountFlag(false);
        calculateDTO.setMemberIntegralStore(false);
        calculateDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        calculateDTO.setVolumeCodes(Arrays.asList("value"));
        when(mockCalculateService.calculate(calculateDTO)).thenReturn(calculateOrderRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/pay/calculate")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetAllPayWay() throws Exception {
        // Setup
        // Configure PayService.getAllPayWay(...).
        final PayWayRespDTO payWayRespDTO = new PayWayRespDTO(0, "errorMsg", 0, 0, new BigDecimal("0.00"),
                new BigDecimal("0.00"), 0, new BigDecimal("0.00"), "cardName");
        when(mockPayService.getAllPayWay("orderGuid")).thenReturn(payWayRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/deal/pay/way")
                        .param("orderGuid", "orderGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetAllPayWay_PayServiceReturnsFailure() throws Exception {
        // Setup
        // Configure PayService.getAllPayWay(...).
        final PayWayRespDTO payWayRespDTO = PayWayRespDTO.changeFailed();
        when(mockPayService.getAllPayWay("orderGuid")).thenReturn(payWayRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/deal/pay/way")
                        .param("orderGuid", "orderGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testPrepay() throws Exception {
        // Setup
        when(mockPayService.prepay("orderGuid")).thenReturn(new WxPrepayRespDTO(0, "errorMsg"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/deal/pay/prepay")
                        .param("orderGuid", "orderGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testPrepay_PayServiceReturnsFailure() throws Exception {
        // Setup
        when(mockPayService.prepay("orderGuid")).thenReturn(WxPrepayRespDTO.changeFailed());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/deal/pay/prepay")
                        .param("orderGuid", "orderGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testZeroPay() throws Exception {
        // Setup
        when(mockPayService.zeroPay(
                new DealMemberPayReqDTO("orderGuid", "memberPassWord", 0, 0, 0, false, new BigDecimal("0.00"), false,
                        "memberInfoCardGuid"))).thenReturn(new ZeroPayResultRespDTO(0, "errorMsg"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/deal/pay/zero")
                        .param("orderGuid", "orderGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testZeroPay_PayServiceReturnsFailure() throws Exception {
        // Setup
        when(mockPayService.zeroPay(
                new DealMemberPayReqDTO("orderGuid", "memberPassWord", 0, 0, 0, false, new BigDecimal("0.00"), false,
                        "memberInfoCardGuid"))).thenReturn(ZeroPayResultRespDTO.changeFailed());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/deal/pay/zero")
                        .param("orderGuid", "orderGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testAppletZeroPay() throws Exception {
        // Setup
        when(mockPayService.zeroPay(
                new DealMemberPayReqDTO("orderGuid", "memberPassWord", 0, 0, 0, false, new BigDecimal("0.00"), false,
                        "memberInfoCardGuid"))).thenReturn(new ZeroPayResultRespDTO(0, "errorMsg"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/pay/applet/zero")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testAppletZeroPay_PayServiceReturnsFailure() throws Exception {
        // Setup
        when(mockPayService.zeroPay(
                new DealMemberPayReqDTO("orderGuid", "memberPassWord", 0, 0, 0, false, new BigDecimal("0.00"), false,
                        "memberInfoCardGuid"))).thenReturn(ZeroPayResultRespDTO.changeFailed());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/pay/applet/zero")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testMemberPay() throws Exception {
        // Setup
        // Configure PayService.memberPay(...).
        final MemberPayReusltRespDTO memberPayReusltRespDTO = new MemberPayReusltRespDTO(0, "errorMsg");
        when(mockPayService.memberPay(
                new DealMemberPayReqDTO("orderGuid", "memberPassWord", 0, 0, 0, false, new BigDecimal("0.00"), false,
                        "memberInfoCardGuid"))).thenReturn(memberPayReusltRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/pay/member")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testMemberPay_PayServiceReturnsFailure() throws Exception {
        // Setup
        // Configure PayService.memberPay(...).
        final MemberPayReusltRespDTO memberPayReusltRespDTO = MemberPayReusltRespDTO.changeFailed();
        when(mockPayService.memberPay(
                new DealMemberPayReqDTO("orderGuid", "memberPassWord", 0, 0, 0, false, new BigDecimal("0.00"), false,
                        "memberInfoCardGuid"))).thenReturn(memberPayReusltRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/pay/member")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testWeChatH5Pay() throws Exception {
        // Setup
        // Configure PayService.weChatPay(...).
        final KbzPayStartDTO kbzPayStartDTO = new KbzPayStartDTO();
        kbzPayStartDTO.setPrePayId("prePayId");
        kbzPayStartDTO.setOrderInfo("orderInfo");
        kbzPayStartDTO.setSign("sign");
        final WxPayRespDTO wxPayRespDTO = new WxPayRespDTO("payUrl", 0, "errorMsg",
                new AggPayRespDTO("code", "msg", "result", "payGuid", "orderGuid", "paymentAppId"), kbzPayStartDTO);
        when(mockPayService.aggPay(
                new WeChatH5PayReqDTO("orderGuid", "outNotifyUrl", 0, new BigDecimal("0.00"), false, "appId",
                        "clientIp", false, "", ""), null)).thenReturn(wxPayRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/pay/we_chat")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testWeChatH5Pay_PayServiceReturnsFailure() throws Exception {
        // Setup
        // Configure PayService.weChatPay(...).
        final WxPayRespDTO wxPayRespDTO = WxPayRespDTO.changeFailed();
        when(mockPayService.aggPay(
                new WeChatH5PayReqDTO("orderGuid", "outNotifyUrl", 0, new BigDecimal("0.00"), false, "appId",
                        "clientIp", false, "",""), null)).thenReturn(wxPayRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/pay/we_chat")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testAppletMemberPay() throws Exception {
        // Setup
        // Configure PayService.memberPay(...).
        final MemberPayReusltRespDTO memberPayReusltRespDTO = new MemberPayReusltRespDTO(0, "errorMsg");
        when(mockPayService.memberPay(
                new DealMemberPayReqDTO("orderGuid", "memberPassWord", 0, 0, 0, false, new BigDecimal("0.00"), false,
                        "memberInfoCardGuid"))).thenReturn(memberPayReusltRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/pay/applet/member")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testAppletMemberPay_PayServiceReturnsFailure() throws Exception {
        // Setup
        // Configure PayService.memberPay(...).
        final MemberPayReusltRespDTO memberPayReusltRespDTO = MemberPayReusltRespDTO.changeFailed();
        when(mockPayService.memberPay(
                new DealMemberPayReqDTO("orderGuid", "memberPassWord", 0, 0, 0, false, new BigDecimal("0.00"), false,
                        "memberInfoCardGuid"))).thenReturn(memberPayReusltRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/pay/applet/member")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testAppletWechatPay() throws Exception {
        // Setup
        // Configure PayService.appletWeChatPay(...).
        final KbzPayStartDTO kbzPayStartDTO = new KbzPayStartDTO();
        kbzPayStartDTO.setPrePayId("prePayId");
        kbzPayStartDTO.setOrderInfo("orderInfo");
        kbzPayStartDTO.setSign("sign");
        final WxPayRespDTO wxPayRespDTO = new WxPayRespDTO("payUrl", 0, "errorMsg",
                new AggPayRespDTO("code", "msg", "result", "payGuid", "orderGuid", "paymentAppId"), kbzPayStartDTO);
        when(mockPayService.aggPay(
                eq(new WeChatH5PayReqDTO("orderGuid", "outNotifyUrl", 0, new BigDecimal("0.00"), false, "appId",
                        "clientIp", false, "","")), any(HttpServletRequest.class))).thenReturn(wxPayRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/pay/applet/wechat")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testAppletWechatPay_PayServiceReturnsFailure() throws Exception {
        // Setup
        // Configure PayService.appletWeChatPay(...).
        final WxPayRespDTO wxPayRespDTO = WxPayRespDTO.changeFailed();
        when(mockPayService.aggPay(
                eq(new WeChatH5PayReqDTO("orderGuid", "outNotifyUrl", 0, new BigDecimal("0.00"), false, "appId",
                        "clientIp", false, "", "")), any(HttpServletRequest.class))).thenReturn(wxPayRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/pay/applet/wechat")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testAppletCancelPay() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/pay/applet/cancel")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm PayService.appletCancelPay(...).
        final WeChatCancelPayReqDTO weChatCancelPayReqDTO = new WeChatCancelPayReqDTO();
        weChatCancelPayReqDTO.setOrderGuid("orderGuid");
        weChatCancelPayReqDTO.setReason("reason");
        weChatCancelPayReqDTO.setFastFood(false);
        weChatCancelPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        verify(mockPayService).appletCancelPay(weChatCancelPayReqDTO);
    }
}
