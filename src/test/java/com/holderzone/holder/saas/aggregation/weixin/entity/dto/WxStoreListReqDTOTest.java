package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import org.junit.Before;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class WxStoreListReqDTOTest {

    private WxStoreListReqDTO wxStoreListReqDTOUnderTest;

    @Before
    public void setUp() throws Exception {
        wxStoreListReqDTOUnderTest = new WxStoreListReqDTO();
    }

    @Test
    public void testEnterpriseGuidGetterAndSetter() {
        final String enterpriseGuid = "enterpriseGuid";
        wxStoreListReqDTOUnderTest.setEnterpriseGuid(enterpriseGuid);
        assertThat(wxStoreListReqDTOUnderTest.getEnterpriseGuid()).isEqualTo(enterpriseGuid);
    }

    @Test
    public void testStoreTypeGetterAndSetter() {
        final Integer storeType = 0;
        wxStoreListReqDTOUnderTest.setStoreType(storeType);
        assertThat(wxStoreListReqDTOUnderTest.getStoreType()).isEqualTo(storeType);
    }

    @Test
    public void testBrandGuidGetterAndSetter() {
        final String brandGuid = "brandGuid";
        wxStoreListReqDTOUnderTest.setBrandGuid(brandGuid);
        assertThat(wxStoreListReqDTOUnderTest.getBrandGuid()).isEqualTo(brandGuid);
    }

    @Test
    public void testSystemManagementGuidGetterAndSetter() {
        final String systemManagementGuid = "systemManagementGuid";
        wxStoreListReqDTOUnderTest.setSystemManagementGuid(systemManagementGuid);
        assertThat(wxStoreListReqDTOUnderTest.getSystemManagementGuid()).isEqualTo(systemManagementGuid);
    }

    @Test
    public void testEquals() throws Exception {
        assertThat(wxStoreListReqDTOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() throws Exception {
        assertThat(wxStoreListReqDTOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() throws Exception {
        assertThat(wxStoreListReqDTOUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() throws Exception {
        assertThat(wxStoreListReqDTOUnderTest.toString()).isEqualTo("result");
    }
}
