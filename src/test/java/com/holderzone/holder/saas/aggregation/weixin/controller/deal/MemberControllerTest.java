package com.holderzone.holder.saas.aggregation.weixin.controller.deal;

import com.holderzone.holder.saas.aggregation.weixin.entity.dto.CheckVerifyVolumeRespDTO;
import com.holderzone.holder.saas.aggregation.weixin.service.MemberService;
import com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList;
import com.holderzone.holder.saas.member.wechat.dto.card.RequestCardRightDetails;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseCardRight;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseCardRightDetail;
import com.holderzone.holder.saas.member.wechat.dto.coupon.ResponseHsmProductDetail;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.weixin.deal.*;
import com.holderzone.saas.store.dto.weixin.member.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(MemberController.class)
public class MemberControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private MemberService mockMemberService;

    @Test
    public void testCardPage() throws Exception {
        // Setup
        // Configure MemberService.cardPage(...).
        final ResponseCardRight responseCardRight = new ResponseCardRight();
        responseCardRight.setName("name");
        final ResponseCardRightDetail responseCardRightDetail = new ResponseCardRightDetail();
        responseCardRightDetail.setRightsGuid("rightsGuid");
        responseCardRightDetail.setRightsName("rightsName");
        responseCardRightDetail.setGainCondition("gainCondition");
        responseCardRight.setList(Arrays.asList(responseCardRightDetail));
        final List<MemberCardItemDTO> memberCardItemDTOS = Arrays.asList(
                new MemberCardItemDTO("cardQRCode", "cardGuid", "memberInfoCardGuid", "systemManagementGuid",
                        "enterpriseMemberInfoSystemGuid", "cardName", "cardLogo", "cardIcon", "cardColour", 0,
                        "cardStartDate", "cardEndDate", 0, "systemManagementCardNum", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "cardIntegral", "cardGrowthValue", "cardLevelGuid", "cardLevelName", 0,
                        "levelIcon", 0, 0, 0, Arrays.asList(responseCardRight)));
        when(mockMemberService.cardPage()).thenReturn(memberCardItemDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/member/card_page")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testCardPage_MemberServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockMemberService.cardPage()).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/member/card_page")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testVolumePage() throws Exception {
        // Setup
        // Configure MemberService.volumeList(...).
        final List<MemberVolumeItemDTO> memberVolumeItemDTOS = Arrays.asList(
                new MemberVolumeItemDTO("memberVolumeGuid", "volumeInfoGuid", "volumeInfoName",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        new BigDecimal("0.00"), 0, 0, new BigDecimal("0.00"), 0, "volumeCode", 0, false, "tip"));
        when(mockMemberService.volumeList(new VolumePageReqDTO("orderGuid"))).thenReturn(memberVolumeItemDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/member/volume_page")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testVolumePage_MemberServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockMemberService.volumeList(new VolumePageReqDTO("orderGuid"))).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/member/volume_page")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testVolumeList() throws Exception {
        // Setup
        // Configure MemberService.queryVolumeList(...).
        final ResponseVolumeList responseVolumeList = new ResponseVolumeList();
        responseVolumeList.setVolumeCode("volumeCode");
        responseVolumeList.setVolumeInfoGuid("volumeInfoGuid");
        responseVolumeList.setMemberVolumeGuid("memberVolumeGuid");
        responseVolumeList.setVolumeInfoName("volumeInfoName");
        responseVolumeList.setVolumeStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<ResponseVolumeList> responseVolumeLists = Arrays.asList(responseVolumeList);
        final VolumeOrderQueryDTO queryDTO = new VolumeOrderQueryDTO();
        queryDTO.setActivityGuid("activityGuid");
        queryDTO.setVolumeCodes(Arrays.asList("value"));
        queryDTO.setUseMemberDiscountFlag(false);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("66d6d9e9-170c-4ef6-948f-7017ef5bfc54");
        queryDTO.setDineInItemList(Arrays.asList(dineInItemDTO));
        when(mockMemberService.queryVolumeList(queryDTO)).thenReturn(responseVolumeLists);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/member/volume_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testVolumeList_MemberServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure MemberService.queryVolumeList(...).
        final VolumeOrderQueryDTO queryDTO = new VolumeOrderQueryDTO();
        queryDTO.setActivityGuid("activityGuid");
        queryDTO.setVolumeCodes(Arrays.asList("value"));
        queryDTO.setUseMemberDiscountFlag(false);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("66d6d9e9-170c-4ef6-948f-7017ef5bfc54");
        queryDTO.setDineInItemList(Arrays.asList(dineInItemDTO));
        when(mockMemberService.queryVolumeList(queryDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/member/volume_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testVolumeCodeDetails() throws Exception {
        // Setup
        // Configure MemberService.volumeDetail(...).
        final ResponseHsmProductDetail responseHsmProductDetail = new ResponseHsmProductDetail();
        responseHsmProductDetail.setGuid("2441ae38-5338-4349-9d3c-ac747d52baeb");
        responseHsmProductDetail.setProductKey("productKey");
        responseHsmProductDetail.setEnterpriseGuid("enterpriseGuid");
        responseHsmProductDetail.setBrandGuid("brandGuid");
        responseHsmProductDetail.setStoreGuid("storeGuid");
        final WxMemberInfoVolumeDetailsRespDTO wxMemberInfoVolumeDetailsRespDTO = new WxMemberInfoVolumeDetailsRespDTO(
                "volumeInfoName", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                "volumeCode", "volumeQrCode", 0, new BigDecimal("0.00"), 0, 0, new BigDecimal("0.00"), "mayUseNum",
                Arrays.asList("value"), "description", 0,
                Arrays.asList(new WxApplicableProductStores("storeName", 0, Arrays.asList("value"))), "storeName", 0,
                Arrays.asList(responseHsmProductDetail));
        when(mockMemberService.volumeDetail(
                new WxVolumeDetailReqDTO("enterpriseGuid", "memberVolumeGuid", "volumeInfoGuid")))
                .thenReturn(wxMemberInfoVolumeDetailsRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/member/volume_details")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testCardRightList() throws Exception {
        // Setup
        // Configure MemberService.cardRight(...).
        final ResponseCardRight responseCardRight = new ResponseCardRight();
        responseCardRight.setName("name");
        final ResponseCardRightDetail responseCardRightDetail = new ResponseCardRightDetail();
        responseCardRightDetail.setRightsGuid("rightsGuid");
        responseCardRightDetail.setRightsName("rightsName");
        responseCardRightDetail.setGainCondition("gainCondition");
        responseCardRight.setList(Arrays.asList(responseCardRightDetail));
        final List<ResponseCardRight> responseCardRights = Arrays.asList(responseCardRight);
        final RequestCardRightDetails requestCardRightDetails = new RequestCardRightDetails();
        requestCardRightDetails.setMemberInfoCardGuid("memberInfoCardGuid");
        requestCardRightDetails.setCardGuid("cardGuid");
        requestCardRightDetails.setCardType(0);
        requestCardRightDetails.setCardLevelGuid("cardLevelGuid");
        when(mockMemberService.cardRight(requestCardRightDetails)).thenReturn(responseCardRights);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/member/card_right")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testCardRightList_MemberServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure MemberService.cardRight(...).
        final RequestCardRightDetails requestCardRightDetails = new RequestCardRightDetails();
        requestCardRightDetails.setMemberInfoCardGuid("memberInfoCardGuid");
        requestCardRightDetails.setCardGuid("cardGuid");
        requestCardRightDetails.setCardType(0);
        requestCardRightDetails.setCardLevelGuid("cardLevelGuid");
        when(mockMemberService.cardRight(requestCardRightDetails)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/member/card_right")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testMemberConfirm() throws Exception {
        // Setup
        // Configure MemberService.confirm(...).
        final WxPrepayConfirmRespDTO wxPrepayConfirmRespDTO = new WxPrepayConfirmRespDTO("errorMsg", 0);
        when(mockMemberService.confirm(
                new MemberConfirmReqDTO(0, "memberInfoCardGuid", false, "volumeCode")))
                .thenReturn(wxPrepayConfirmRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/member/prepay_confirm")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDiscount() throws Exception {
        // Setup
        // Configure MemberService.discount(...).
        final ConcessionTotalRespDTO concessionTotalRespDTO = new ConcessionTotalRespDTO(
                Arrays.asList(new MemberDiscountDTO("discountName", 0, new BigDecimal("0.00"))), new BigDecimal("0.00"),
                false, 0, 0, new BigDecimal("0.00"), 0);
        when(mockMemberService.discount(
                new ConcessionTotalReqDTO(0, "memberInfoCardGuid", 0, "volumeCode", "orderGuid")))
                .thenReturn(concessionTotalRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/member/discount")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testCheckVolume() throws Exception {
        // Setup
        when(mockMemberService.checkVolume(new VolumePageReqDTO("orderGuid")))
                .thenReturn(new CheckVerifyVolumeRespDTO(0, "meg"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/member/check")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
