package com.holderzone.holder.saas.aggregation.app.utils;

import org.junit.Test;

import static org.junit.Assert.assertArrayEquals;
import static org.junit.Assert.assertEquals;

public class AesEncryptUtilsTest {

    @Test
    public void testAesDecrypt1() {
        assertEquals("", AesEncryptUtils.aesDecrypt("encrypt"));
    }

    @Test
    public void testAesEncrypt1() {
        assertEquals("", AesEncryptUtils.aesEncrypt("content"));
    }

    @Test
    public void testBinary() {
        assertEquals("result", AesEncryptUtils.binary("content".getBytes(), 0));
    }

    @Test
    public void testBase64Encode() {
        assertEquals("result", AesEncryptUtils.base64Encode("content".getBytes()));
    }

    @Test
    public void testBase64Decode() throws Exception {
        assertArrayEquals("content".getBytes(), AesEncryptUtils.base64Decode("base64Code"));
    }

    @Test(expected = Exception.class)
    public void testBase64Decode_ThrowsException() throws Exception {
        AesEncryptUtils.base64Decode("base64Code");
    }

    @Test
    public void testAesEncryptToBytes() throws Exception {
        assertArrayEquals("content".getBytes(), AesEncryptUtils.aesEncryptToBytes("content", "encryptKey"));
    }

    @Test(expected = Exception.class)
    public void testAesEncryptToBytes_ThrowsException() throws Exception {
        AesEncryptUtils.aesEncryptToBytes("content", "encryptKey");
    }

    @Test
    public void testAesEncrypt2() throws Exception {
        assertEquals("result", AesEncryptUtils.aesEncrypt("content", "encryptKey"));
    }

    @Test(expected = Exception.class)
    public void testAesEncrypt2_ThrowsException() throws Exception {
        AesEncryptUtils.aesEncrypt("content", "encryptKey");
    }

    @Test
    public void testAesDecryptByBytes() throws Exception {
        assertEquals("result", AesEncryptUtils.aesDecryptByBytes("content".getBytes(), "decryptKey"));
    }

    @Test(expected = Exception.class)
    public void testAesDecryptByBytes_ThrowsException() throws Exception {
        AesEncryptUtils.aesDecryptByBytes("content".getBytes(), "decryptKey");
    }

    @Test
    public void testAesDecrypt2() throws Exception {
        assertEquals("result", AesEncryptUtils.aesDecrypt("encryptStr", "decryptKey"));
    }

    @Test(expected = Exception.class)
    public void testAesDecrypt2_ThrowsException() throws Exception {
        AesEncryptUtils.aesDecrypt("encryptStr", "decryptKey");
    }
}
