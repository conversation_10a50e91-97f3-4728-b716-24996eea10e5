package com.holderzone.holder.saas.aggregation.app.aop;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.aggregation.app.helper.RedisHelper;
import com.holderzone.holder.saas.aggregation.app.service.feign.table.TableClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInOrderClientService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TableValidatorAspectTest {

    @Mock
    private TableClientService mockTableServiceClient;
    @Mock
    private DineInOrderClientService mockDineInOrderClientService;
    @Mock
    private RedisHelper mockRedisHelper;

    @InjectMocks
    private TableValidatorAspect tableValidatorAspectUnderTest;

    @Test
    public void testTableValidatorCut() {
        tableValidatorAspectUnderTest.tableValidatorCut();
    }

    @Test
    public void testLockTableInRedissonCut() {
        tableValidatorAspectUnderTest.lockTableInRedissonCut();
    }

    @Test
    public void testLockInRedisson() {
        // Setup
        final ProceedingJoinPoint point = null;

        // Run the test
        final Object result = tableValidatorAspectUnderTest.lockInRedisson(point);

        // Verify the results
    }

    @Test
    public void testValidator() {
        // Setup
        final ProceedingJoinPoint point = null;
        when(mockTableServiceClient.checkTableIsLocked("deviceId", "tableGuid")).thenReturn(false);

        // Run the test
        final Object result = tableValidatorAspectUnderTest.validator(point);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testValidator_TableClientServiceReturnsTrue() {
        // Setup
        final ProceedingJoinPoint point = null;
        when(mockTableServiceClient.checkTableIsLocked("deviceId", "tableGuid")).thenReturn(true);

        // Run the test
        tableValidatorAspectUnderTest.validator(point);
    }
}
