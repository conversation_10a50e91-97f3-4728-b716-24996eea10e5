package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.app.service.feign.PrintClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.cloud.EnterpriseCloudService;
import com.holderzone.holder.saas.aggregation.app.service.feign.item.ItemClientService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.ItemQueryReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemReqDTO;
import com.holderzone.saas.store.dto.item.req.PadPictureDTO;
import com.holderzone.saas.store.dto.item.req.SkuSaveReqDTO;
import com.holderzone.saas.store.dto.boss.req.BossItemQueryDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.print.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ItemServiceImplTest {

    @Mock
    private ItemClientService mockItemClientService;
    @Mock
    private PrintClientService mockPrintClientService;
    @Mock
    private EnterpriseCloudService mockEnterpriseCloudService;

    private ItemServiceImpl itemServiceImplUnderTest;

    @Before
    public void setUp() {
        itemServiceImplUnderTest = new ItemServiceImpl(mockItemClientService, mockPrintClientService,
                mockEnterpriseCloudService);
    }

    @Test
    public void testQueryItemListToPad() {
        // Setup
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");

        final PadTypeItemRespDTO expectedResult = new PadTypeItemRespDTO();
        final PadTypeRespDTO padTypeRespDTO = new PadTypeRespDTO();
        padTypeRespDTO.setTypeGuid("typeGuid");
        final PadItemRespDTO padItemRespDTO = new PadItemRespDTO();
        padItemRespDTO.setItemGuid("itemGuid");
        padItemRespDTO.setTypeGuid("typeGuid");
        padItemRespDTO.setSmallPicture("");
        padItemRespDTO.setBigPicture("");
        padItemRespDTO.setVerticalPicture("");
        padItemRespDTO.setDetailPictureList(Arrays.asList("value"));
        final PadSkuRespDTO padSkuRespDTO = new PadSkuRespDTO();
        padSkuRespDTO.setStock(0);
        padSkuRespDTO.setIsOpenStock(0);
        padItemRespDTO.setSkuList(Arrays.asList(padSkuRespDTO));
        padTypeRespDTO.setItemList(Arrays.asList(padItemRespDTO));
        expectedResult.setTypeList(Arrays.asList(padTypeRespDTO));

        // Configure ItemClientService.selectItemAndTypeForSyn(...).
        final ItemAndTypeForAndroidRespDTO itemAndTypeForAndroidRespDTO = new ItemAndTypeForAndroidRespDTO();
        final ItemSynRespDTO itemSynRespDTO = new ItemSynRespDTO();
        itemSynRespDTO.setItemGuid("itemGuid");
        itemSynRespDTO.setItemType(0);
        itemSynRespDTO.setPictureUrl("pictureUrl");
        final SkuSynRespDTO skuSynRespDTO = new SkuSynRespDTO();
        skuSynRespDTO.setSkuGuid("skuGuid");
        itemSynRespDTO.setSkuList(Arrays.asList(skuSynRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        itemSynRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        itemSynRespDTO.setSmallPicture("smallPicture");
        itemSynRespDTO.setBigPicture("bigPicture");
        itemSynRespDTO.setVerticalPicture("");
        itemSynRespDTO.setDetailPicture("");
        itemAndTypeForAndroidRespDTO.setItemList(Arrays.asList(itemSynRespDTO));
        final TypeSynRespDTO typeSynRespDTO = new TypeSynRespDTO();
        typeSynRespDTO.setTypeGuid("typeGuid");
        itemAndTypeForAndroidRespDTO.setTypeList(Arrays.asList(typeSynRespDTO));
        itemAndTypeForAndroidRespDTO.setPricePlanGuid("planGuid");
        final BaseDTO baseDTO1 = new BaseDTO();
        baseDTO1.setDeviceType(0);
        baseDTO1.setDeviceId("deviceId");
        baseDTO1.setEnterpriseGuid("enterpriseGuid");
        baseDTO1.setEnterpriseName("enterpriseName");
        baseDTO1.setStoreGuid("storeGuid");
        when(mockItemClientService.selectItemAndTypeForSyn(baseDTO1)).thenReturn(itemAndTypeForAndroidRespDTO);

        // Configure ItemClientService.queryEstimateForSyn(...).
        final List<ItemEstimateForAndroidRespDTO> itemEstimateForAndroidRespDTOS = Arrays.asList(
                new ItemEstimateForAndroidRespDTO("skuGuid", 0, new BigDecimal("0.00"), new BigDecimal("0.00")));
        final BaseDTO baseDTO2 = new BaseDTO();
        baseDTO2.setDeviceType(0);
        baseDTO2.setDeviceId("deviceId");
        baseDTO2.setEnterpriseGuid("enterpriseGuid");
        baseDTO2.setEnterpriseName("enterpriseName");
        baseDTO2.setStoreGuid("storeGuid");
        when(mockItemClientService.queryEstimateForSyn(baseDTO2)).thenReturn(itemEstimateForAndroidRespDTOS);

        // Configure ItemClientService.queryPadPicture(...).
        final PadPictureRespDTO padPictureRespDTO = new PadPictureRespDTO();
        padPictureRespDTO.setItemGuid("itemGuid");
        padPictureRespDTO.setSmallPicture("smallPicture");
        padPictureRespDTO.setBigPicture("bigPicture");
        padPictureRespDTO.setVerticalPicture("verticalPicture");
        padPictureRespDTO.setDetailPicture("detailPicture");
        final Page<PadPictureRespDTO> padPictureRespDTOPage = new Page<>(0L, 0L, Arrays.asList(padPictureRespDTO));
        final PadPictureDTO padPictureDTO = new PadPictureDTO();
        padPictureDTO.setCurrentPage(0L);
        padPictureDTO.setPageSize(0L);
        padPictureDTO.setPlanGuid("planGuid");
        padPictureDTO.setStoreGuid("storeGuid");
        padPictureDTO.setTypeGuidList(Arrays.asList("value"));
        when(mockItemClientService.queryPadPicture(padPictureDTO)).thenReturn(padPictureRespDTOPage);

        // Run the test
        final PadTypeItemRespDTO result = itemServiceImplUnderTest.queryItemListToPad(baseDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryItemListToPad_ItemClientServiceQueryEstimateForSynReturnsNoItems() {
        // Setup
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");

        final PadTypeItemRespDTO expectedResult = new PadTypeItemRespDTO();
        final PadTypeRespDTO padTypeRespDTO = new PadTypeRespDTO();
        padTypeRespDTO.setTypeGuid("typeGuid");
        final PadItemRespDTO padItemRespDTO = new PadItemRespDTO();
        padItemRespDTO.setItemGuid("itemGuid");
        padItemRespDTO.setTypeGuid("typeGuid");
        padItemRespDTO.setSmallPicture("");
        padItemRespDTO.setBigPicture("");
        padItemRespDTO.setVerticalPicture("");
        padItemRespDTO.setDetailPictureList(Arrays.asList("value"));
        final PadSkuRespDTO padSkuRespDTO = new PadSkuRespDTO();
        padSkuRespDTO.setStock(0);
        padSkuRespDTO.setIsOpenStock(0);
        padItemRespDTO.setSkuList(Arrays.asList(padSkuRespDTO));
        padTypeRespDTO.setItemList(Arrays.asList(padItemRespDTO));
        expectedResult.setTypeList(Arrays.asList(padTypeRespDTO));

        // Configure ItemClientService.selectItemAndTypeForSyn(...).
        final ItemAndTypeForAndroidRespDTO itemAndTypeForAndroidRespDTO = new ItemAndTypeForAndroidRespDTO();
        final ItemSynRespDTO itemSynRespDTO = new ItemSynRespDTO();
        itemSynRespDTO.setItemGuid("itemGuid");
        itemSynRespDTO.setItemType(0);
        itemSynRespDTO.setPictureUrl("pictureUrl");
        final SkuSynRespDTO skuSynRespDTO = new SkuSynRespDTO();
        skuSynRespDTO.setSkuGuid("skuGuid");
        itemSynRespDTO.setSkuList(Arrays.asList(skuSynRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        itemSynRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        itemSynRespDTO.setSmallPicture("smallPicture");
        itemSynRespDTO.setBigPicture("bigPicture");
        itemSynRespDTO.setVerticalPicture("");
        itemSynRespDTO.setDetailPicture("");
        itemAndTypeForAndroidRespDTO.setItemList(Arrays.asList(itemSynRespDTO));
        final TypeSynRespDTO typeSynRespDTO = new TypeSynRespDTO();
        typeSynRespDTO.setTypeGuid("typeGuid");
        itemAndTypeForAndroidRespDTO.setTypeList(Arrays.asList(typeSynRespDTO));
        itemAndTypeForAndroidRespDTO.setPricePlanGuid("planGuid");
        final BaseDTO baseDTO1 = new BaseDTO();
        baseDTO1.setDeviceType(0);
        baseDTO1.setDeviceId("deviceId");
        baseDTO1.setEnterpriseGuid("enterpriseGuid");
        baseDTO1.setEnterpriseName("enterpriseName");
        baseDTO1.setStoreGuid("storeGuid");
        when(mockItemClientService.selectItemAndTypeForSyn(baseDTO1)).thenReturn(itemAndTypeForAndroidRespDTO);

        // Configure ItemClientService.queryEstimateForSyn(...).
        final BaseDTO baseDTO2 = new BaseDTO();
        baseDTO2.setDeviceType(0);
        baseDTO2.setDeviceId("deviceId");
        baseDTO2.setEnterpriseGuid("enterpriseGuid");
        baseDTO2.setEnterpriseName("enterpriseName");
        baseDTO2.setStoreGuid("storeGuid");
        when(mockItemClientService.queryEstimateForSyn(baseDTO2)).thenReturn(Collections.emptyList());

        // Configure ItemClientService.queryPadPicture(...).
        final PadPictureRespDTO padPictureRespDTO = new PadPictureRespDTO();
        padPictureRespDTO.setItemGuid("itemGuid");
        padPictureRespDTO.setSmallPicture("smallPicture");
        padPictureRespDTO.setBigPicture("bigPicture");
        padPictureRespDTO.setVerticalPicture("verticalPicture");
        padPictureRespDTO.setDetailPicture("detailPicture");
        final Page<PadPictureRespDTO> padPictureRespDTOPage = new Page<>(0L, 0L, Arrays.asList(padPictureRespDTO));
        final PadPictureDTO padPictureDTO = new PadPictureDTO();
        padPictureDTO.setCurrentPage(0L);
        padPictureDTO.setPageSize(0L);
        padPictureDTO.setPlanGuid("planGuid");
        padPictureDTO.setStoreGuid("storeGuid");
        padPictureDTO.setTypeGuidList(Arrays.asList("value"));
        when(mockItemClientService.queryPadPicture(padPictureDTO)).thenReturn(padPictureRespDTOPage);

        // Run the test
        final PadTypeItemRespDTO result = itemServiceImplUnderTest.queryItemListToPad(baseDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSelectItemAndTypeForSyn() {
        // Setup
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");

        final ItemAndTypeForAndroidRespDTO expectedResult = new ItemAndTypeForAndroidRespDTO();
        final ItemSynRespDTO itemSynRespDTO = new ItemSynRespDTO();
        itemSynRespDTO.setItemGuid("itemGuid");
        itemSynRespDTO.setItemType(0);
        itemSynRespDTO.setPictureUrl("pictureUrl");
        final SkuSynRespDTO skuSynRespDTO = new SkuSynRespDTO();
        skuSynRespDTO.setSkuGuid("skuGuid");
        itemSynRespDTO.setSkuList(Arrays.asList(skuSynRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        itemSynRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        itemSynRespDTO.setSmallPicture("smallPicture");
        itemSynRespDTO.setBigPicture("bigPicture");
        itemSynRespDTO.setVerticalPicture("");
        itemSynRespDTO.setDetailPicture("");
        expectedResult.setItemList(Arrays.asList(itemSynRespDTO));
        final TypeSynRespDTO typeSynRespDTO = new TypeSynRespDTO();
        typeSynRespDTO.setTypeGuid("typeGuid");
        expectedResult.setTypeList(Arrays.asList(typeSynRespDTO));
        expectedResult.setPricePlanGuid("planGuid");

        // Configure ItemClientService.selectItemAndTypeForSyn(...).
        final ItemAndTypeForAndroidRespDTO itemAndTypeForAndroidRespDTO = new ItemAndTypeForAndroidRespDTO();
        final ItemSynRespDTO itemSynRespDTO1 = new ItemSynRespDTO();
        itemSynRespDTO1.setItemGuid("itemGuid");
        itemSynRespDTO1.setItemType(0);
        itemSynRespDTO1.setPictureUrl("pictureUrl");
        final SkuSynRespDTO skuSynRespDTO1 = new SkuSynRespDTO();
        skuSynRespDTO1.setSkuGuid("skuGuid");
        itemSynRespDTO1.setSkuList(Arrays.asList(skuSynRespDTO1));
        final SubgroupSynRespDTO subgroupSynRespDTO1 = new SubgroupSynRespDTO();
        subgroupSynRespDTO1.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO1 = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO1.setSkuGuid("skuGuid");
        subgroupSynRespDTO1.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO1));
        itemSynRespDTO1.setSubgroupList(Arrays.asList(subgroupSynRespDTO1));
        itemSynRespDTO1.setSmallPicture("smallPicture");
        itemSynRespDTO1.setBigPicture("bigPicture");
        itemSynRespDTO1.setVerticalPicture("");
        itemSynRespDTO1.setDetailPicture("");
        itemAndTypeForAndroidRespDTO.setItemList(Arrays.asList(itemSynRespDTO1));
        final TypeSynRespDTO typeSynRespDTO1 = new TypeSynRespDTO();
        typeSynRespDTO1.setTypeGuid("typeGuid");
        itemAndTypeForAndroidRespDTO.setTypeList(Arrays.asList(typeSynRespDTO1));
        itemAndTypeForAndroidRespDTO.setPricePlanGuid("planGuid");
        final BaseDTO baseDTO1 = new BaseDTO();
        baseDTO1.setDeviceType(0);
        baseDTO1.setDeviceId("deviceId");
        baseDTO1.setEnterpriseGuid("enterpriseGuid");
        baseDTO1.setEnterpriseName("enterpriseName");
        baseDTO1.setStoreGuid("storeGuid");
        when(mockItemClientService.selectItemAndTypeForSyn(baseDTO1)).thenReturn(itemAndTypeForAndroidRespDTO);

        // Configure ItemClientService.queryPadPicture(...).
        final PadPictureRespDTO padPictureRespDTO = new PadPictureRespDTO();
        padPictureRespDTO.setItemGuid("itemGuid");
        padPictureRespDTO.setSmallPicture("smallPicture");
        padPictureRespDTO.setBigPicture("bigPicture");
        padPictureRespDTO.setVerticalPicture("verticalPicture");
        padPictureRespDTO.setDetailPicture("detailPicture");
        final Page<PadPictureRespDTO> padPictureRespDTOPage = new Page<>(0L, 0L, Arrays.asList(padPictureRespDTO));
        final PadPictureDTO padPictureDTO = new PadPictureDTO();
        padPictureDTO.setCurrentPage(0L);
        padPictureDTO.setPageSize(0L);
        padPictureDTO.setPlanGuid("planGuid");
        padPictureDTO.setStoreGuid("storeGuid");
        padPictureDTO.setTypeGuidList(Arrays.asList("value"));
        when(mockItemClientService.queryPadPicture(padPictureDTO)).thenReturn(padPictureRespDTOPage);

        // Run the test
        final ItemAndTypeForAndroidRespDTO result = itemServiceImplUnderTest.selectItemAndTypeForSyn(baseDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetItemInfoRespDTOResult() {
        // Setup
        final ItemReqDTO itemSaveReqDTO = new ItemReqDTO();
        itemSaveReqDTO.setDeviceType(0);
        itemSaveReqDTO.setFrom(0);
        itemSaveReqDTO.setDataList(Arrays.asList("value"));
        itemSaveReqDTO.setName("name");
        itemSaveReqDTO.setPinyin("pinyin");
        itemSaveReqDTO.setPictureUrl("");
        itemSaveReqDTO.setIsBestseller(0);
        itemSaveReqDTO.setIsNew(0);
        itemSaveReqDTO.setIsSign(0);
        itemSaveReqDTO.setDescription("");
        final SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setName("");
        skuSaveReqDTO.setIsWholeDiscount(0);
        skuSaveReqDTO.setIsRack(0);
        skuSaveReqDTO.setIsJoinAio(0);
        skuSaveReqDTO.setIsJoinPos(0);
        skuSaveReqDTO.setIsJoinPad(0);
        skuSaveReqDTO.setMinOrderNum(new BigDecimal("0.00"));
        skuSaveReqDTO.setIsJoinBuffet(0);
        skuSaveReqDTO.setIsJoinWeChat(0);
        itemSaveReqDTO.setSkuList(Arrays.asList(skuSaveReqDTO));
        itemSaveReqDTO.setPrinterGuid("printerGuid");
        itemSaveReqDTO.setUserGuid("staffGuid");
        itemSaveReqDTO.setTypeName("typeName");

        final ItemInfoRespDTO expectedResult = new ItemInfoRespDTO();
        expectedResult.setItemGuid("itemGuid");
        expectedResult.setBrandGuid("brandGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setTypeName("typeName");
        expectedResult.setName("name");

        // Configure ItemClientService.saveItem(...).
        final ItemReqDTO itemSaveReqDTO1 = new ItemReqDTO();
        itemSaveReqDTO1.setDeviceType(0);
        itemSaveReqDTO1.setFrom(0);
        itemSaveReqDTO1.setDataList(Arrays.asList("value"));
        itemSaveReqDTO1.setName("name");
        itemSaveReqDTO1.setPinyin("pinyin");
        itemSaveReqDTO1.setPictureUrl("");
        itemSaveReqDTO1.setIsBestseller(0);
        itemSaveReqDTO1.setIsNew(0);
        itemSaveReqDTO1.setIsSign(0);
        itemSaveReqDTO1.setDescription("");
        final SkuSaveReqDTO skuSaveReqDTO1 = new SkuSaveReqDTO();
        skuSaveReqDTO1.setName("");
        skuSaveReqDTO1.setIsWholeDiscount(0);
        skuSaveReqDTO1.setIsRack(0);
        skuSaveReqDTO1.setIsJoinAio(0);
        skuSaveReqDTO1.setIsJoinPos(0);
        skuSaveReqDTO1.setIsJoinPad(0);
        skuSaveReqDTO1.setMinOrderNum(new BigDecimal("0.00"));
        skuSaveReqDTO1.setIsJoinBuffet(0);
        skuSaveReqDTO1.setIsJoinWeChat(0);
        itemSaveReqDTO1.setSkuList(Arrays.asList(skuSaveReqDTO1));
        itemSaveReqDTO1.setPrinterGuid("printerGuid");
        itemSaveReqDTO1.setUserGuid("staffGuid");
        itemSaveReqDTO1.setTypeName("typeName");
        when(mockItemClientService.saveItem(itemSaveReqDTO1)).thenReturn("data");

        // Configure ItemClientService.getItemInfo(...).
        final ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid("itemGuid");
        itemInfoRespDTO.setBrandGuid("brandGuid");
        itemInfoRespDTO.setStoreGuid("storeGuid");
        itemInfoRespDTO.setTypeName("typeName");
        itemInfoRespDTO.setName("name");
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setDeviceType(0);
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData("data");
        itemSingleDTO.setKeywords("keywords");
        itemSingleDTO.setModel(0);
        when(mockItemClientService.getItemInfo(itemSingleDTO)).thenReturn(itemInfoRespDTO);

        // Configure PrintClientService.getPrinter(...).
        final PrinterInvoiceDTO printerInvoiceDTO = new PrinterInvoiceDTO();
        printerInvoiceDTO.setInvoiceType(0);
        printerInvoiceDTO.setInvoiceName("invoiceName");
        printerInvoiceDTO.setPrintCount(0);
        final TypeItemListDTO typeItemListDTO = new TypeItemListDTO();
        typeItemListDTO.setGuid("4328f3a2-b713-405f-9cf9-fbba17829e1e");
        typeItemListDTO.setName("name");
        typeItemListDTO.setUsed(false);
        typeItemListDTO.setItemList(Arrays.asList(new TypeItemListDTO()));
        final PrinterDTO printerDTO = new PrinterDTO("storeGuid", "storeName", "deviceId", "printerGuid", "printerName",
                0, 0, "printerIp", 0, 0, "printPage", 0, "staffGuid", Arrays.asList(0), 1, Arrays.asList("value"),
                Arrays.asList("value"), Arrays.asList("value"), Arrays.asList(printerInvoiceDTO),
                Arrays.asList(new PrinterItemDTO("itemGuid", "itemName")),
                Arrays.asList(new PrinterAreaDTO("areaGuid", "areaName")),
                Arrays.asList(new PrinterTableDTO("tableGuid", "tableName")),
                false, Arrays.asList(typeItemListDTO),
                "deviceNo", "deviceKey", 0, "deviceModel", 0, Arrays.asList(), true,1);
        final PrinterInvoiceDTO printerInvoiceDTO1 = new PrinterInvoiceDTO();
        printerInvoiceDTO1.setInvoiceType(0);
        printerInvoiceDTO1.setInvoiceName("invoiceName");
        printerInvoiceDTO1.setPrintCount(0);
        final TypeItemListDTO typeItemListDTO1 = new TypeItemListDTO();
        typeItemListDTO1.setGuid("4328f3a2-b713-405f-9cf9-fbba17829e1e");
        typeItemListDTO1.setName("name");
        typeItemListDTO1.setUsed(false);
        typeItemListDTO1.setItemList(Arrays.asList(new TypeItemListDTO()));
        final PrinterDTO printerDTO1 = new PrinterDTO("storeGuid", "storeName", "deviceId", "printerGuid", "printerName",
                0, 0, "printerIp", 0, 0, "printPage", 0, "staffGuid", Arrays.asList(0), 1, Arrays.asList("value"),
                Arrays.asList("value"), Arrays.asList("value"), Arrays.asList(printerInvoiceDTO),
                Arrays.asList(new PrinterItemDTO("itemGuid", "itemName")),
                Arrays.asList(new PrinterAreaDTO("areaGuid", "areaName")),
                Arrays.asList(new PrinterTableDTO("tableGuid", "tableName")),
                false, Arrays.asList(typeItemListDTO),
                "deviceNo", "deviceKey", 0, "deviceModel", 0, Arrays.asList(), true,1);
        when(mockPrintClientService.getPrinter(printerDTO1)).thenReturn(printerDTO);

        // Run the test
        final ItemInfoRespDTO result = itemServiceImplUnderTest.getItemInfoRespDTOResult(itemSaveReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm PrintClientService.updatePrinter(...).
        final PrinterInvoiceDTO printerInvoiceDTO2 = new PrinterInvoiceDTO();
        printerInvoiceDTO2.setInvoiceType(0);
        printerInvoiceDTO2.setInvoiceName("invoiceName");
        printerInvoiceDTO2.setPrintCount(0);
        final TypeItemListDTO typeItemListDTO2 = new TypeItemListDTO();
        typeItemListDTO2.setGuid("4328f3a2-b713-405f-9cf9-fbba17829e1e");
        typeItemListDTO2.setName("name");
        typeItemListDTO2.setUsed(false);
        typeItemListDTO2.setItemList(Arrays.asList(new TypeItemListDTO()));
        final PrinterDTO printerDTO2 = new PrinterDTO("storeGuid", "storeName", "deviceId", "printerGuid", "printerName",
                0, 0, "printerIp", 0, 0, "printPage", 0, "staffGuid", Arrays.asList(0), 1, Arrays.asList("value"),
                Arrays.asList("value"), Arrays.asList("value"), Arrays.asList(printerInvoiceDTO),
                Arrays.asList(new PrinterItemDTO("itemGuid", "itemName")),
                Arrays.asList(new PrinterAreaDTO("areaGuid", "areaName")),
                Arrays.asList(new PrinterTableDTO("tableGuid", "tableName")),
                false, Arrays.asList(typeItemListDTO),
                "deviceNo", "deviceKey", 0, "deviceModel", 0, Arrays.asList(), true,1);
        verify(mockPrintClientService).updatePrinter(printerDTO2);
    }

    @Test
    public void testListItemForBoss() {
        // Setup
        final BossItemQueryDTO queryDTO = new BossItemQueryDTO();
        queryDTO.setTypeGuid("typeGuid");
        queryDTO.setIsRack(0);
        queryDTO.setStoreGuid("storeGuid");

        final ItemWebRespDTO itemWebRespDTO = new ItemWebRespDTO();
        itemWebRespDTO.setItemGuid("itemGuid");
        itemWebRespDTO.setParentGuid("parentGuid");
        itemWebRespDTO.setName("name");
        itemWebRespDTO.setCode("code");
        itemWebRespDTO.setTypeGuid("typeGuid");
        final List<ItemWebRespDTO> expectedResult = Arrays.asList(itemWebRespDTO);

        // Configure ItemClientService.selectItemForWeb(...).
        final ItemWebRespDTO itemWebRespDTO1 = new ItemWebRespDTO();
        itemWebRespDTO1.setItemGuid("itemGuid");
        itemWebRespDTO1.setParentGuid("parentGuid");
        itemWebRespDTO1.setName("name");
        itemWebRespDTO1.setCode("code");
        itemWebRespDTO1.setTypeGuid("typeGuid");
        final Page<ItemWebRespDTO> itemWebRespDTOPage = new Page<>(0L, 0L, Arrays.asList(itemWebRespDTO1));
        final ItemQueryReqDTO itemQueryReqDTO = new ItemQueryReqDTO();
        itemQueryReqDTO.setCurrentPage(0L);
        itemQueryReqDTO.setPageSize(0L);
        itemQueryReqDTO.setTypeGuid("typeGuid");
        itemQueryReqDTO.setIsRack(0);
        itemQueryReqDTO.setStoreGuid("storeGuid");
        when(mockItemClientService.selectItemForWeb(itemQueryReqDTO)).thenReturn(itemWebRespDTOPage);

        // Run the test
        final List<ItemWebRespDTO> result = itemServiceImplUnderTest.listItemForBoss(queryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSaveItemFromBoss() {
        // Setup
        final ItemReqDTO itemSaveReqDTO = new ItemReqDTO();
        itemSaveReqDTO.setDeviceType(0);
        itemSaveReqDTO.setFrom(0);
        itemSaveReqDTO.setDataList(Arrays.asList("value"));
        itemSaveReqDTO.setName("name");
        itemSaveReqDTO.setPinyin("pinyin");
        itemSaveReqDTO.setPictureUrl("");
        itemSaveReqDTO.setIsBestseller(0);
        itemSaveReqDTO.setIsNew(0);
        itemSaveReqDTO.setIsSign(0);
        itemSaveReqDTO.setDescription("");
        final SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setName("");
        skuSaveReqDTO.setIsWholeDiscount(0);
        skuSaveReqDTO.setIsRack(0);
        skuSaveReqDTO.setIsJoinAio(0);
        skuSaveReqDTO.setIsJoinPos(0);
        skuSaveReqDTO.setIsJoinPad(0);
        skuSaveReqDTO.setMinOrderNum(new BigDecimal("0.00"));
        skuSaveReqDTO.setIsJoinBuffet(0);
        skuSaveReqDTO.setIsJoinWeChat(0);
        itemSaveReqDTO.setSkuList(Arrays.asList(skuSaveReqDTO));
        itemSaveReqDTO.setPrinterGuid("printerGuid");
        itemSaveReqDTO.setUserGuid("staffGuid");
        itemSaveReqDTO.setTypeName("typeName");

        // Run the test
        itemServiceImplUnderTest.saveItemFromBoss(itemSaveReqDTO);

        // Verify the results
        // Confirm ItemClientService.saveItem(...).
        final ItemReqDTO itemSaveReqDTO1 = new ItemReqDTO();
        itemSaveReqDTO1.setDeviceType(0);
        itemSaveReqDTO1.setFrom(0);
        itemSaveReqDTO1.setDataList(Arrays.asList("value"));
        itemSaveReqDTO1.setName("name");
        itemSaveReqDTO1.setPinyin("pinyin");
        itemSaveReqDTO1.setPictureUrl("");
        itemSaveReqDTO1.setIsBestseller(0);
        itemSaveReqDTO1.setIsNew(0);
        itemSaveReqDTO1.setIsSign(0);
        itemSaveReqDTO1.setDescription("");
        final SkuSaveReqDTO skuSaveReqDTO1 = new SkuSaveReqDTO();
        skuSaveReqDTO1.setName("");
        skuSaveReqDTO1.setIsWholeDiscount(0);
        skuSaveReqDTO1.setIsRack(0);
        skuSaveReqDTO1.setIsJoinAio(0);
        skuSaveReqDTO1.setIsJoinPos(0);
        skuSaveReqDTO1.setIsJoinPad(0);
        skuSaveReqDTO1.setMinOrderNum(new BigDecimal("0.00"));
        skuSaveReqDTO1.setIsJoinBuffet(0);
        skuSaveReqDTO1.setIsJoinWeChat(0);
        itemSaveReqDTO1.setSkuList(Arrays.asList(skuSaveReqDTO1));
        itemSaveReqDTO1.setPrinterGuid("printerGuid");
        itemSaveReqDTO1.setUserGuid("staffGuid");
        itemSaveReqDTO1.setTypeName("typeName");
        verify(mockItemClientService).saveItem(itemSaveReqDTO1);
    }

    @Test
    public void testUpdateItemFromBoss() {
        // Setup
        final ItemReqDTO itemUpdateReqDTO = new ItemReqDTO();
        itemUpdateReqDTO.setDeviceType(0);
        itemUpdateReqDTO.setFrom(0);
        itemUpdateReqDTO.setDataList(Arrays.asList("value"));
        itemUpdateReqDTO.setName("name");
        itemUpdateReqDTO.setPinyin("pinyin");
        itemUpdateReqDTO.setPictureUrl("");
        itemUpdateReqDTO.setIsBestseller(0);
        itemUpdateReqDTO.setIsNew(0);
        itemUpdateReqDTO.setIsSign(0);
        itemUpdateReqDTO.setDescription("");
        final SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
        skuSaveReqDTO.setName("");
        skuSaveReqDTO.setIsWholeDiscount(0);
        skuSaveReqDTO.setIsRack(0);
        skuSaveReqDTO.setIsJoinAio(0);
        skuSaveReqDTO.setIsJoinPos(0);
        skuSaveReqDTO.setIsJoinPad(0);
        skuSaveReqDTO.setMinOrderNum(new BigDecimal("0.00"));
        skuSaveReqDTO.setIsJoinBuffet(0);
        skuSaveReqDTO.setIsJoinWeChat(0);
        itemUpdateReqDTO.setSkuList(Arrays.asList(skuSaveReqDTO));
        itemUpdateReqDTO.setPrinterGuid("printerGuid");
        itemUpdateReqDTO.setUserGuid("staffGuid");
        itemUpdateReqDTO.setTypeName("typeName");

        // Configure ItemClientService.updateItem(...).
        final ItemReqDTO itemUpdateReqDTO1 = new ItemReqDTO();
        itemUpdateReqDTO1.setDeviceType(0);
        itemUpdateReqDTO1.setFrom(0);
        itemUpdateReqDTO1.setDataList(Arrays.asList("value"));
        itemUpdateReqDTO1.setName("name");
        itemUpdateReqDTO1.setPinyin("pinyin");
        itemUpdateReqDTO1.setPictureUrl("");
        itemUpdateReqDTO1.setIsBestseller(0);
        itemUpdateReqDTO1.setIsNew(0);
        itemUpdateReqDTO1.setIsSign(0);
        itemUpdateReqDTO1.setDescription("");
        final SkuSaveReqDTO skuSaveReqDTO1 = new SkuSaveReqDTO();
        skuSaveReqDTO1.setName("");
        skuSaveReqDTO1.setIsWholeDiscount(0);
        skuSaveReqDTO1.setIsRack(0);
        skuSaveReqDTO1.setIsJoinAio(0);
        skuSaveReqDTO1.setIsJoinPos(0);
        skuSaveReqDTO1.setIsJoinPad(0);
        skuSaveReqDTO1.setMinOrderNum(new BigDecimal("0.00"));
        skuSaveReqDTO1.setIsJoinBuffet(0);
        skuSaveReqDTO1.setIsJoinWeChat(0);
        itemUpdateReqDTO1.setSkuList(Arrays.asList(skuSaveReqDTO1));
        itemUpdateReqDTO1.setPrinterGuid("printerGuid");
        itemUpdateReqDTO1.setUserGuid("staffGuid");
        itemUpdateReqDTO1.setTypeName("typeName");
        when(mockItemClientService.updateItem(itemUpdateReqDTO1)).thenReturn(0);

        // Run the test
        final Integer result = itemServiceImplUnderTest.updateItemFromBoss(itemUpdateReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}
