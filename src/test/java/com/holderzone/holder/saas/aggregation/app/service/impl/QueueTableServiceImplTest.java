package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.holder.saas.aggregation.app.service.feign.queue.QueueClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.table.TableClientService;
import com.holderzone.saas.store.dto.queue.QueueGuidDTO;
import com.holderzone.saas.store.dto.queue.QueueTableDTO;
import com.holderzone.saas.store.dto.queue.TableDTO;
import com.holderzone.saas.store.dto.table.AreaDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.table.TableOrderDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueueTableServiceImplTest {

    @Mock
    private TableClientService mockTableClientService;
    @Mock
    private QueueClientService mockQueueClientService;

    @InjectMocks
    private QueueTableServiceImpl queueTableServiceImplUnderTest;

    @Test
    public void testMergeTables() {
        // Setup
        final QueueGuidDTO queueGuidDTO = new QueueGuidDTO();
        queueGuidDTO.setStoreGuid("storeGuid");
        queueGuidDTO.setQueueGuid("queueGuid");

        final QueueTableDTO expectedResult = new QueueTableDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setTables(Arrays.asList(TableDTO.builder()
                .tableGuid("tableGuid")
                .tableName("tableCode")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .seats(0)
                .build()));

        // Configure QueueClientService.obtain(...).
        final QueueTableDTO queueTableDTO = new QueueTableDTO();
        queueTableDTO.setStoreGuid("storeGuid");
        queueTableDTO.setTables(Arrays.asList(TableDTO.builder()
                .tableGuid("tableGuid")
                .tableName("tableCode")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .seats(0)
                .build()));
        final QueueGuidDTO queueGuidDTO1 = new QueueGuidDTO();
        queueGuidDTO1.setStoreGuid("storeGuid");
        queueGuidDTO1.setQueueGuid("queueGuid");
        when(mockQueueClientService.obtain(queueGuidDTO1)).thenReturn(queueTableDTO);

        // Configure TableClientService.queryArea(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("f4398288-2308-41f8-99d1-8c23101c1f20");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("areaName");
        areaDTO.setSort(0);
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockTableClientService.queryArea("storeGuid")).thenReturn(areaDTOS);

        // Configure TableClientService.queryTable(...).
        final TableOrderDTO tableOrderDTO = new TableOrderDTO();
        tableOrderDTO.setStoreGuid("storeGuid");
        tableOrderDTO.setTableGuid("tableGuid");
        tableOrderDTO.setAreaGuid("areaGuid");
        tableOrderDTO.setTableCode("tableCode");
        tableOrderDTO.setSeats(0);
        tableOrderDTO.setStatus(0);
        final List<TableOrderDTO> tableOrderDTOS = Arrays.asList(tableOrderDTO);
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableClientService.queryTable(tableBasicQueryDTO)).thenReturn(tableOrderDTOS);

        // Run the test
        final QueueTableDTO result = queueTableServiceImplUnderTest.mergeTables(queueGuidDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testMergeTables_TableClientServiceQueryAreaReturnsNoItems() {
        // Setup
        final QueueGuidDTO queueGuidDTO = new QueueGuidDTO();
        queueGuidDTO.setStoreGuid("storeGuid");
        queueGuidDTO.setQueueGuid("queueGuid");

        final QueueTableDTO expectedResult = new QueueTableDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setTables(Arrays.asList(TableDTO.builder()
                .tableGuid("tableGuid")
                .tableName("tableCode")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .seats(0)
                .build()));

        // Configure QueueClientService.obtain(...).
        final QueueTableDTO queueTableDTO = new QueueTableDTO();
        queueTableDTO.setStoreGuid("storeGuid");
        queueTableDTO.setTables(Arrays.asList(TableDTO.builder()
                .tableGuid("tableGuid")
                .tableName("tableCode")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .seats(0)
                .build()));
        final QueueGuidDTO queueGuidDTO1 = new QueueGuidDTO();
        queueGuidDTO1.setStoreGuid("storeGuid");
        queueGuidDTO1.setQueueGuid("queueGuid");
        when(mockQueueClientService.obtain(queueGuidDTO1)).thenReturn(queueTableDTO);

        when(mockTableClientService.queryArea("storeGuid")).thenReturn(Collections.emptyList());

        // Configure TableClientService.queryTable(...).
        final TableOrderDTO tableOrderDTO = new TableOrderDTO();
        tableOrderDTO.setStoreGuid("storeGuid");
        tableOrderDTO.setTableGuid("tableGuid");
        tableOrderDTO.setAreaGuid("areaGuid");
        tableOrderDTO.setTableCode("tableCode");
        tableOrderDTO.setSeats(0);
        tableOrderDTO.setStatus(0);
        final List<TableOrderDTO> tableOrderDTOS = Arrays.asList(tableOrderDTO);
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableClientService.queryTable(tableBasicQueryDTO)).thenReturn(tableOrderDTOS);

        // Run the test
        final QueueTableDTO result = queueTableServiceImplUnderTest.mergeTables(queueGuidDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testMergeTables_TableClientServiceQueryTableReturnsNoItems() {
        // Setup
        final QueueGuidDTO queueGuidDTO = new QueueGuidDTO();
        queueGuidDTO.setStoreGuid("storeGuid");
        queueGuidDTO.setQueueGuid("queueGuid");

        final QueueTableDTO expectedResult = new QueueTableDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setTables(Arrays.asList(TableDTO.builder()
                .tableGuid("tableGuid")
                .tableName("tableCode")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .seats(0)
                .build()));

        // Configure QueueClientService.obtain(...).
        final QueueTableDTO queueTableDTO = new QueueTableDTO();
        queueTableDTO.setStoreGuid("storeGuid");
        queueTableDTO.setTables(Arrays.asList(TableDTO.builder()
                .tableGuid("tableGuid")
                .tableName("tableCode")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .seats(0)
                .build()));
        final QueueGuidDTO queueGuidDTO1 = new QueueGuidDTO();
        queueGuidDTO1.setStoreGuid("storeGuid");
        queueGuidDTO1.setQueueGuid("queueGuid");
        when(mockQueueClientService.obtain(queueGuidDTO1)).thenReturn(queueTableDTO);

        // Configure TableClientService.queryArea(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("f4398288-2308-41f8-99d1-8c23101c1f20");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("areaName");
        areaDTO.setSort(0);
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockTableClientService.queryArea("storeGuid")).thenReturn(areaDTOS);

        // Configure TableClientService.queryTable(...).
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableClientService.queryTable(tableBasicQueryDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final QueueTableDTO result = queueTableServiceImplUnderTest.mergeTables(queueGuidDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
