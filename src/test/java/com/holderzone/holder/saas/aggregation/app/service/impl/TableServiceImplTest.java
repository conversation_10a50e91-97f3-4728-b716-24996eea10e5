package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.holder.saas.aggregation.app.service.feign.MessageClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.organization.OrgFeignClient;
import com.holderzone.holder.saas.aggregation.app.service.feign.table.TableClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInOrderClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.OrderItemClientService;
import com.holderzone.saas.store.dto.boss.req.BossTableQueryDTO;
import com.holderzone.saas.store.dto.boss.resp.BossTableRespDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.inside.OrderGuidsDTO;
import com.holderzone.saas.store.dto.order.inside.OrderTableInfoDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.organization.PadOrderTypeReqDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.table.*;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.trade.resp.PadOrderRespDTO;
import com.holderzone.saas.store.reserve.api.ReserveTableApi;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordLessDTO;
import com.holderzone.saas.store.reserve.api.dto.TableGuidsDTO;
import com.holderzone.saas.store.reserve.api.dto.TableOrderReserveDTO;
import com.holderzone.saas.store.reserve.api.dto.TableReserveRecordRef;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TableServiceImplTest {

    @Mock
    private TableClientService mockTableClientService;
    @Mock
    private DineInOrderClientService mockDineInOrderClientService;
    @Mock
    private ReserveTableApi mockReserveTableApi;
    @Mock
    private OrderItemClientService mockOrderItemClientService;
    @Mock
    private OrgFeignClient mockOrgFeignClient;
    @Mock
    private MessageClientService mockMessageClientService;

    private TableServiceImpl tableServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        tableServiceImplUnderTest = new TableServiceImpl(mockTableClientService, mockDineInOrderClientService,
                mockReserveTableApi, mockOrderItemClientService, mockOrgFeignClient, mockMessageClientService);
    }

    @Test
    public void testQueryArea() {
        // Setup
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setDeviceType(0);
        areaDTO.setGuid("3f51916a-8fc8-44ea-9f87-a42abd50ccbf");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("areaName");
        final List<AreaDTO> expectedResult = Arrays.asList(areaDTO);

        // Configure TableClientService.queryArea(...).
        final AreaDTO areaDTO1 = new AreaDTO();
        areaDTO1.setDeviceType(0);
        areaDTO1.setGuid("3f51916a-8fc8-44ea-9f87-a42abd50ccbf");
        areaDTO1.setStoreGuid("storeGuid");
        areaDTO1.setStoreName("storeName");
        areaDTO1.setAreaName("areaName");
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO1);
        when(mockTableClientService.queryArea("storeGuid")).thenReturn(areaDTOS);

        // Run the test
        final List<AreaDTO> result = tableServiceImplUnderTest.queryArea("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryArea_TableClientServiceReturnsNoItems() {
        // Setup
        when(mockTableClientService.queryArea("storeGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final List<AreaDTO> result = tableServiceImplUnderTest.queryArea("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryTable() {
        // Setup
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setDeviceType(0);
        tableBasicQueryDTO.setStoreName("storeName");
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));

        final StopWatch stopWatch = new StopWatch("id");
        final TableOrderReserveDTO dto = new TableOrderReserveDTO();
        final ReserveRecordLessDTO record = new ReserveRecordLessDTO();
        record.setDeviceType(0);
        record.setStoreName("storeName");
        record.setGuid("e283cb29-1520-4f04-8a69-de526bb49b04");
        record.setStoreGuid("storeGuid");
        dto.setRecord(record);
        final List<TableOrderReserveDTO> expectedResult = Arrays.asList(dto);

        // Configure TableClientService.queryTable(...).
        final TableOrderDTO tableOrderDTO = new TableOrderDTO();
        tableOrderDTO.setDeviceType(0);
        tableOrderDTO.setStoreName("storeName");
        tableOrderDTO.setTableGuid("tableGuid");
        tableOrderDTO.setOrderAmount(new BigDecimal("0.00"));
        tableOrderDTO.setOrderGuid("orderGuid");
        tableOrderDTO.setActualGuestsNo(0);
        tableOrderDTO.setStatus(0);
        tableOrderDTO.setSubStatus(new HashSet<>(Arrays.asList(0)));
        tableOrderDTO.setPrintPreBillNum(0);
        final List<TableOrderDTO> tableOrderDTOS = Arrays.asList(tableOrderDTO);
        final TableBasicQueryDTO tableBasicQueryDTO1 = new TableBasicQueryDTO();
        tableBasicQueryDTO1.setDeviceType(0);
        tableBasicQueryDTO1.setStoreName("storeName");
        tableBasicQueryDTO1.setStoreGuid("storeGuid");
        tableBasicQueryDTO1.setAreaGuid("areaGuid");
        tableBasicQueryDTO1.setTableGuidList(Arrays.asList("value"));
        when(mockTableClientService.queryTable(tableBasicQueryDTO1)).thenReturn(tableOrderDTOS);

        // Configure DineInOrderClientService.batchGetTableInfo2(...).
        final OrderTableInfoDTO tableInfoDTO = new OrderTableInfoDTO();
        tableInfoDTO.setGuid("fcfa140d-f870-4323-91d8-598428d48843");
        tableInfoDTO.setOrderFee(new BigDecimal("0.00"));
        tableInfoDTO.setState(0);
        tableInfoDTO.setPrintPreBillNum(0);
        tableInfoDTO.setGuestCount(0);
        tableInfoDTO.setUpperState(0);
        tableInfoDTO.setOrderFeeForCombine(new BigDecimal("0.00"));
        final List<OrderTableInfoDTO> orderTableInfoDTOS = Arrays.asList(tableInfoDTO);
        when(mockDineInOrderClientService.batchGetTableInfo2(new OrderGuidsDTO(Arrays.asList("value"))))
                .thenReturn(orderTableInfoDTOS);

        // Configure OrderItemClientService.findByOrderGuid(...).
        final OrderDTO orderDTO = OrderDTO.builder()
                .deviceType(0)
                .build();
        when(mockOrderItemClientService.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        // Configure OrgFeignClient.queryDeviceByStoreTable(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("storeName");
        storeDeviceDTO.setDeviceNo("deviceNo");
        when(mockOrgFeignClient.queryDeviceByStoreTable(any(PadOrderTypeReqDTO.class))).thenReturn(storeDeviceDTO);

        // Configure ReserveTableApi.query(...).
        final ReserveRecordLessDTO reserveRecordLessDTO = new ReserveRecordLessDTO();
        reserveRecordLessDTO.setDeviceType(0);
        reserveRecordLessDTO.setStoreName("storeName");
        reserveRecordLessDTO.setGuid("e283cb29-1520-4f04-8a69-de526bb49b04");
        reserveRecordLessDTO.setStoreGuid("storeGuid");
        reserveRecordLessDTO.setNumber(0);
        final List<TableReserveRecordRef> tableReserveRecordRefs = Arrays.asList(
                new TableReserveRecordRef("tableGuid", reserveRecordLessDTO));
        when(mockReserveTableApi.query(new TableGuidsDTO(Arrays.asList("value")))).thenReturn(tableReserveRecordRefs);

        // Run the test
        final List<TableOrderReserveDTO> result = tableServiceImplUnderTest.queryTable(tableBasicQueryDTO, stopWatch);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockTableClientService).compensationTableStatus(
                new CompensationTableReqDTO(Arrays.asList(new CompensationTableDTO("tableGuid", "orderGuid")),
                        "enterpriseGuid"));
        verify(mockMessageClientService).msg(BusinessMessageDTO.builder()
                .subject("name")
                .content("content")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .messageTypeStr("messageTypeStr")
                .build());
    }

    @Test
    public void testQueryTable_TableClientServiceQueryTableReturnsNoItems() {
        // Setup
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setDeviceType(0);
        tableBasicQueryDTO.setStoreName("storeName");
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));

        final StopWatch stopWatch = new StopWatch("id");

        // Configure TableClientService.queryTable(...).
        final TableBasicQueryDTO tableBasicQueryDTO1 = new TableBasicQueryDTO();
        tableBasicQueryDTO1.setDeviceType(0);
        tableBasicQueryDTO1.setStoreName("storeName");
        tableBasicQueryDTO1.setStoreGuid("storeGuid");
        tableBasicQueryDTO1.setAreaGuid("areaGuid");
        tableBasicQueryDTO1.setTableGuidList(Arrays.asList("value"));
        when(mockTableClientService.queryTable(tableBasicQueryDTO1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<TableOrderReserveDTO> result = tableServiceImplUnderTest.queryTable(tableBasicQueryDTO, stopWatch);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryTable_DineInOrderClientServiceReturnsNoItems() {
        // Setup
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setDeviceType(0);
        tableBasicQueryDTO.setStoreName("storeName");
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));

        final StopWatch stopWatch = new StopWatch("id");
        final TableOrderReserveDTO dto = new TableOrderReserveDTO();
        final ReserveRecordLessDTO record = new ReserveRecordLessDTO();
        record.setDeviceType(0);
        record.setStoreName("storeName");
        record.setGuid("e283cb29-1520-4f04-8a69-de526bb49b04");
        record.setStoreGuid("storeGuid");
        dto.setRecord(record);
        final List<TableOrderReserveDTO> expectedResult = Arrays.asList(dto);

        // Configure TableClientService.queryTable(...).
        final TableOrderDTO tableOrderDTO = new TableOrderDTO();
        tableOrderDTO.setDeviceType(0);
        tableOrderDTO.setStoreName("storeName");
        tableOrderDTO.setTableGuid("tableGuid");
        tableOrderDTO.setOrderAmount(new BigDecimal("0.00"));
        tableOrderDTO.setOrderGuid("orderGuid");
        tableOrderDTO.setActualGuestsNo(0);
        tableOrderDTO.setStatus(0);
        tableOrderDTO.setSubStatus(new HashSet<>(Arrays.asList(0)));
        tableOrderDTO.setPrintPreBillNum(0);
        final List<TableOrderDTO> tableOrderDTOS = Arrays.asList(tableOrderDTO);
        final TableBasicQueryDTO tableBasicQueryDTO1 = new TableBasicQueryDTO();
        tableBasicQueryDTO1.setDeviceType(0);
        tableBasicQueryDTO1.setStoreName("storeName");
        tableBasicQueryDTO1.setStoreGuid("storeGuid");
        tableBasicQueryDTO1.setAreaGuid("areaGuid");
        tableBasicQueryDTO1.setTableGuidList(Arrays.asList("value"));
        when(mockTableClientService.queryTable(tableBasicQueryDTO1)).thenReturn(tableOrderDTOS);

        when(mockDineInOrderClientService.batchGetTableInfo2(new OrderGuidsDTO(Arrays.asList("value"))))
                .thenReturn(Collections.emptyList());

        // Configure ReserveTableApi.query(...).
        final ReserveRecordLessDTO reserveRecordLessDTO = new ReserveRecordLessDTO();
        reserveRecordLessDTO.setDeviceType(0);
        reserveRecordLessDTO.setStoreName("storeName");
        reserveRecordLessDTO.setGuid("e283cb29-1520-4f04-8a69-de526bb49b04");
        reserveRecordLessDTO.setStoreGuid("storeGuid");
        reserveRecordLessDTO.setNumber(0);
        final List<TableReserveRecordRef> tableReserveRecordRefs = Arrays.asList(
                new TableReserveRecordRef("tableGuid", reserveRecordLessDTO));
        when(mockReserveTableApi.query(new TableGuidsDTO(Arrays.asList("value")))).thenReturn(tableReserveRecordRefs);

        // Run the test
        final List<TableOrderReserveDTO> result = tableServiceImplUnderTest.queryTable(tableBasicQueryDTO, stopWatch);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryTable_ReserveTableApiReturnsNoItems() {
        // Setup
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setDeviceType(0);
        tableBasicQueryDTO.setStoreName("storeName");
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));

        final StopWatch stopWatch = new StopWatch("id");
        final TableOrderReserveDTO dto = new TableOrderReserveDTO();
        final ReserveRecordLessDTO record = new ReserveRecordLessDTO();
        record.setDeviceType(0);
        record.setStoreName("storeName");
        record.setGuid("e283cb29-1520-4f04-8a69-de526bb49b04");
        record.setStoreGuid("storeGuid");
        dto.setRecord(record);
        final List<TableOrderReserveDTO> expectedResult = Arrays.asList(dto);

        // Configure TableClientService.queryTable(...).
        final TableOrderDTO tableOrderDTO = new TableOrderDTO();
        tableOrderDTO.setDeviceType(0);
        tableOrderDTO.setStoreName("storeName");
        tableOrderDTO.setTableGuid("tableGuid");
        tableOrderDTO.setOrderAmount(new BigDecimal("0.00"));
        tableOrderDTO.setOrderGuid("orderGuid");
        tableOrderDTO.setActualGuestsNo(0);
        tableOrderDTO.setStatus(0);
        tableOrderDTO.setSubStatus(new HashSet<>(Arrays.asList(0)));
        tableOrderDTO.setPrintPreBillNum(0);
        final List<TableOrderDTO> tableOrderDTOS = Arrays.asList(tableOrderDTO);
        final TableBasicQueryDTO tableBasicQueryDTO1 = new TableBasicQueryDTO();
        tableBasicQueryDTO1.setDeviceType(0);
        tableBasicQueryDTO1.setStoreName("storeName");
        tableBasicQueryDTO1.setStoreGuid("storeGuid");
        tableBasicQueryDTO1.setAreaGuid("areaGuid");
        tableBasicQueryDTO1.setTableGuidList(Arrays.asList("value"));
        when(mockTableClientService.queryTable(tableBasicQueryDTO1)).thenReturn(tableOrderDTOS);

        // Configure DineInOrderClientService.batchGetTableInfo2(...).
        final OrderTableInfoDTO tableInfoDTO = new OrderTableInfoDTO();
        tableInfoDTO.setGuid("fcfa140d-f870-4323-91d8-598428d48843");
        tableInfoDTO.setOrderFee(new BigDecimal("0.00"));
        tableInfoDTO.setState(0);
        tableInfoDTO.setPrintPreBillNum(0);
        tableInfoDTO.setGuestCount(0);
        tableInfoDTO.setUpperState(0);
        tableInfoDTO.setOrderFeeForCombine(new BigDecimal("0.00"));
        final List<OrderTableInfoDTO> orderTableInfoDTOS = Arrays.asList(tableInfoDTO);
        when(mockDineInOrderClientService.batchGetTableInfo2(new OrderGuidsDTO(Arrays.asList("value"))))
                .thenReturn(orderTableInfoDTOS);

        when(mockReserveTableApi.query(new TableGuidsDTO(Arrays.asList("value")))).thenReturn(Collections.emptyList());

        // Run the test
        final List<TableOrderReserveDTO> result = tableServiceImplUnderTest.queryTable(tableBasicQueryDTO, stopWatch);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testOpenTable() {
        // Setup
        final OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setStoreName("storeName");
        openTableDTO.setTableGuid("tableGuid");
        openTableDTO.setTableCode("tableCode");
        openTableDTO.setAreaName("areaName");

        // Configure TableClientService.openTable(...).
        final OpenTableDTO openTableDTO1 = new OpenTableDTO();
        openTableDTO1.setDeviceType(0);
        openTableDTO1.setStoreName("storeName");
        openTableDTO1.setTableGuid("tableGuid");
        openTableDTO1.setTableCode("tableCode");
        openTableDTO1.setAreaName("areaName");
        when(mockTableClientService.openTable(openTableDTO1)).thenReturn("result");

        // Run the test
        final String result = tableServiceImplUnderTest.openTable(openTableDTO);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testCleanTable() {
        // Setup
        final TableStatusChangeDTO tableStatusChangeDTO = new TableStatusChangeDTO();
        tableStatusChangeDTO.setDeviceType(0);
        tableStatusChangeDTO.setStoreName("storeName");
        tableStatusChangeDTO.setOrderGuid("orderGuid");
        tableStatusChangeDTO.setTableGuid("tableGuid");
        tableStatusChangeDTO.setTableStatusChange(0);

        // Configure TableClientService.cleanTable(...).
        final TableStatusChangeDTO tableStatusChangeDTO1 = new TableStatusChangeDTO();
        tableStatusChangeDTO1.setDeviceType(0);
        tableStatusChangeDTO1.setStoreName("storeName");
        tableStatusChangeDTO1.setOrderGuid("orderGuid");
        tableStatusChangeDTO1.setTableGuid("tableGuid");
        tableStatusChangeDTO1.setTableStatusChange(0);
        when(mockTableClientService.cleanTable(tableStatusChangeDTO1)).thenReturn("result");

        // Run the test
        final String result = tableServiceImplUnderTest.cleanTable(tableStatusChangeDTO);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testTurnTable() {
        // Setup
        final TurnTableDTO turnTableDTO = new TurnTableDTO();
        turnTableDTO.setDeviceType(0);
        turnTableDTO.setStoreName("storeName");
        turnTableDTO.setOriginTableGuid("originTableGuid");
        turnTableDTO.setOriginTableCode("originTableCode");
        turnTableDTO.setOriginTableAreaGuid("originTableAreaGuid");

        // Configure TableClientService.turnTale(...).
        final TurnTableDTO turnTableDTO1 = new TurnTableDTO();
        turnTableDTO1.setDeviceType(0);
        turnTableDTO1.setStoreName("storeName");
        turnTableDTO1.setOriginTableGuid("originTableGuid");
        turnTableDTO1.setOriginTableCode("originTableCode");
        turnTableDTO1.setOriginTableAreaGuid("originTableAreaGuid");
        when(mockTableClientService.turnTale(turnTableDTO1)).thenReturn(false);

        // Run the test
        final boolean result = tableServiceImplUnderTest.turnTable(turnTableDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testTurnTable_TableClientServiceReturnsTrue() {
        // Setup
        final TurnTableDTO turnTableDTO = new TurnTableDTO();
        turnTableDTO.setDeviceType(0);
        turnTableDTO.setStoreName("storeName");
        turnTableDTO.setOriginTableGuid("originTableGuid");
        turnTableDTO.setOriginTableCode("originTableCode");
        turnTableDTO.setOriginTableAreaGuid("originTableAreaGuid");

        // Configure TableClientService.turnTale(...).
        final TurnTableDTO turnTableDTO1 = new TurnTableDTO();
        turnTableDTO1.setDeviceType(0);
        turnTableDTO1.setStoreName("storeName");
        turnTableDTO1.setOriginTableGuid("originTableGuid");
        turnTableDTO1.setOriginTableCode("originTableCode");
        turnTableDTO1.setOriginTableAreaGuid("originTableAreaGuid");
        when(mockTableClientService.turnTale(turnTableDTO1)).thenReturn(true);

        // Run the test
        final boolean result = tableServiceImplUnderTest.turnTable(turnTableDTO);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testCombine() {
        // Setup
        final TableCombineDTO tableCombineDTO = new TableCombineDTO();
        tableCombineDTO.setDeviceType(0);
        tableCombineDTO.setStoreName("storeName");
        tableCombineDTO.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO.setCombineTimes(0);
        tableCombineDTO.setMainOrderGuid("mainOrderGuid");

        // Configure TableClientService.combine(...).
        final TableCombineDTO tableCombineDTO1 = new TableCombineDTO();
        tableCombineDTO1.setDeviceType(0);
        tableCombineDTO1.setStoreName("storeName");
        tableCombineDTO1.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO1.setCombineTimes(0);
        tableCombineDTO1.setMainOrderGuid("mainOrderGuid");
        when(mockTableClientService.combine(tableCombineDTO1)).thenReturn(Arrays.asList("value"));

        // Run the test
        final List<String> result = tableServiceImplUnderTest.combine(tableCombineDTO);

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
    }

    @Test
    public void testCombine_TableClientServiceReturnsNoItems() {
        // Setup
        final TableCombineDTO tableCombineDTO = new TableCombineDTO();
        tableCombineDTO.setDeviceType(0);
        tableCombineDTO.setStoreName("storeName");
        tableCombineDTO.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO.setCombineTimes(0);
        tableCombineDTO.setMainOrderGuid("mainOrderGuid");

        // Configure TableClientService.combine(...).
        final TableCombineDTO tableCombineDTO1 = new TableCombineDTO();
        tableCombineDTO1.setDeviceType(0);
        tableCombineDTO1.setStoreName("storeName");
        tableCombineDTO1.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO1.setCombineTimes(0);
        tableCombineDTO1.setMainOrderGuid("mainOrderGuid");
        when(mockTableClientService.combine(tableCombineDTO1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = tableServiceImplUnderTest.combine(tableCombineDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testCombineV2() {
        // Setup
        final TableCombineDTO tableCombineDTO = new TableCombineDTO();
        tableCombineDTO.setDeviceType(0);
        tableCombineDTO.setStoreName("storeName");
        tableCombineDTO.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO.setCombineTimes(0);
        tableCombineDTO.setMainOrderGuid("mainOrderGuid");

        final TableCombineRespDTO expectedResult = new TableCombineRespDTO();
        expectedResult.setFailTableArray(Arrays.asList("value"));
        expectedResult.setSuccessResult(new HashMap<>());

        // Configure TableClientService.combinev2(...).
        final TableCombineRespDTO tableCombineRespDTO = new TableCombineRespDTO();
        tableCombineRespDTO.setFailTableArray(Arrays.asList("value"));
        tableCombineRespDTO.setSuccessResult(new HashMap<>());
        final TableCombineDTO tableCombineDTO1 = new TableCombineDTO();
        tableCombineDTO1.setDeviceType(0);
        tableCombineDTO1.setStoreName("storeName");
        tableCombineDTO1.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO1.setCombineTimes(0);
        tableCombineDTO1.setMainOrderGuid("mainOrderGuid");
        when(mockTableClientService.combinev2(tableCombineDTO1)).thenReturn(tableCombineRespDTO);

        // Run the test
        final TableCombineRespDTO result = tableServiceImplUnderTest.combineV2(tableCombineDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSeparateTable() {
        // Setup
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setDeviceType(0);
        tableInfoDTO.setStoreName("storeName");
        tableInfoDTO.setTableName("tableName");
        tableInfoDTO.setAreaGuid("areaGuid");
        tableInfoDTO.setAreaName("areaName");
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid",true);

        // Configure TableClientService.separateTable(...).
        final TableInfoDTO tableInfoDTO1 = new TableInfoDTO();
        tableInfoDTO1.setDeviceType(0);
        tableInfoDTO1.setStoreName("storeName");
        tableInfoDTO1.setTableName("tableName");
        tableInfoDTO1.setAreaGuid("areaGuid");
        tableInfoDTO1.setAreaName("areaName");
        final TableOrderCombineDTO tableOrderCombineDTO1 = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO1),
                "mainOrderGuid", "mainTableGuid",true);
        when(mockTableClientService.separateTable(tableOrderCombineDTO1)).thenReturn(false);

        // Run the test
        final boolean result = tableServiceImplUnderTest.separateTable(tableOrderCombineDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testSeparateTable_TableClientServiceReturnsTrue() {
        // Setup
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setDeviceType(0);
        tableInfoDTO.setStoreName("storeName");
        tableInfoDTO.setTableName("tableName");
        tableInfoDTO.setAreaGuid("areaGuid");
        tableInfoDTO.setAreaName("areaName");
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid",true);

        // Configure TableClientService.separateTable(...).
        final TableInfoDTO tableInfoDTO1 = new TableInfoDTO();
        tableInfoDTO1.setDeviceType(0);
        tableInfoDTO1.setStoreName("storeName");
        tableInfoDTO1.setTableName("tableName");
        tableInfoDTO1.setAreaGuid("areaGuid");
        tableInfoDTO1.setAreaName("areaName");
        final TableOrderCombineDTO tableOrderCombineDTO1 = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO1),
                "mainOrderGuid", "mainTableGuid",true);
        when(mockTableClientService.separateTable(tableOrderCombineDTO1)).thenReturn(true);

        // Run the test
        final boolean result = tableServiceImplUnderTest.separateTable(tableOrderCombineDTO);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testCloseTable() {
        // Setup
        final CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setDeviceType(0);
        cancelOrderReqDTO.setStoreName("storeName");
        cancelOrderReqDTO.setReason("reason");
        cancelOrderReqDTO.setTable(false);
        cancelOrderReqDTO.setFastFood(false);

        // Configure TableClientService.closeTable(...).
        final CancelOrderReqDTO cancelOrderReqDTO1 = new CancelOrderReqDTO();
        cancelOrderReqDTO1.setDeviceType(0);
        cancelOrderReqDTO1.setStoreName("storeName");
        cancelOrderReqDTO1.setReason("reason");
        cancelOrderReqDTO1.setTable(false);
        cancelOrderReqDTO1.setFastFood(false);
        when(mockTableClientService.closeTable(cancelOrderReqDTO1)).thenReturn(false);

        // Run the test
        final boolean result = tableServiceImplUnderTest.closeTable(cancelOrderReqDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testCloseTable_TableClientServiceReturnsTrue() {
        // Setup
        final CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setDeviceType(0);
        cancelOrderReqDTO.setStoreName("storeName");
        cancelOrderReqDTO.setReason("reason");
        cancelOrderReqDTO.setTable(false);
        cancelOrderReqDTO.setFastFood(false);

        // Configure TableClientService.closeTable(...).
        final CancelOrderReqDTO cancelOrderReqDTO1 = new CancelOrderReqDTO();
        cancelOrderReqDTO1.setDeviceType(0);
        cancelOrderReqDTO1.setStoreName("storeName");
        cancelOrderReqDTO1.setReason("reason");
        cancelOrderReqDTO1.setTable(false);
        cancelOrderReqDTO1.setFastFood(false);
        when(mockTableClientService.closeTable(cancelOrderReqDTO1)).thenReturn(true);

        // Run the test
        final boolean result = tableServiceImplUnderTest.closeTable(cancelOrderReqDTO);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testPayCloseTable() {
        // Setup
        final CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setDeviceType(0);
        cancelOrderReqDTO.setStoreName("storeName");
        cancelOrderReqDTO.setReason("reason");
        cancelOrderReqDTO.setTable(false);
        cancelOrderReqDTO.setFastFood(false);

        // Configure TableClientService.payClose(...).
        final CancelOrderReqDTO cancelOrderReqDTO1 = new CancelOrderReqDTO();
        cancelOrderReqDTO1.setDeviceType(0);
        cancelOrderReqDTO1.setStoreName("storeName");
        cancelOrderReqDTO1.setReason("reason");
        cancelOrderReqDTO1.setTable(false);
        cancelOrderReqDTO1.setFastFood(false);
        when(mockTableClientService.payClose(cancelOrderReqDTO1)).thenReturn(false);

        // Run the test
        final boolean result = tableServiceImplUnderTest.payCloseTable(cancelOrderReqDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testPayCloseTable_TableClientServiceReturnsTrue() {
        // Setup
        final CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setDeviceType(0);
        cancelOrderReqDTO.setStoreName("storeName");
        cancelOrderReqDTO.setReason("reason");
        cancelOrderReqDTO.setTable(false);
        cancelOrderReqDTO.setFastFood(false);

        // Configure TableClientService.payClose(...).
        final CancelOrderReqDTO cancelOrderReqDTO1 = new CancelOrderReqDTO();
        cancelOrderReqDTO1.setDeviceType(0);
        cancelOrderReqDTO1.setStoreName("storeName");
        cancelOrderReqDTO1.setReason("reason");
        cancelOrderReqDTO1.setTable(false);
        cancelOrderReqDTO1.setFastFood(false);
        when(mockTableClientService.payClose(cancelOrderReqDTO1)).thenReturn(true);

        // Run the test
        final boolean result = tableServiceImplUnderTest.payCloseTable(cancelOrderReqDTO);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testTryLock() {
        // Setup
        final TableLockDTO tableLockDTO = new TableLockDTO();
        tableLockDTO.setDeviceType(0);
        tableLockDTO.setStoreName("storeName");

        // Configure TableClientService.tableLock(...).
        final TableLockDTO tableLockDTO1 = new TableLockDTO();
        tableLockDTO1.setDeviceType(0);
        tableLockDTO1.setStoreName("storeName");
        when(mockTableClientService.tableLock(tableLockDTO1)).thenReturn(false);

        // Run the test
        final boolean result = tableServiceImplUnderTest.tryLock(tableLockDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testTryLock_TableClientServiceReturnsTrue() {
        // Setup
        final TableLockDTO tableLockDTO = new TableLockDTO();
        tableLockDTO.setDeviceType(0);
        tableLockDTO.setStoreName("storeName");

        // Configure TableClientService.tableLock(...).
        final TableLockDTO tableLockDTO1 = new TableLockDTO();
        tableLockDTO1.setDeviceType(0);
        tableLockDTO1.setStoreName("storeName");
        when(mockTableClientService.tableLock(tableLockDTO1)).thenReturn(true);

        // Run the test
        final boolean result = tableServiceImplUnderTest.tryLock(tableLockDTO);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testReleaseLock() {
        // Setup
        final TableLockDTO tableLockDTO = new TableLockDTO();
        tableLockDTO.setDeviceType(0);
        tableLockDTO.setStoreName("storeName");

        // Configure TableClientService.releaseLock(...).
        final TableLockDTO tableLockDTO1 = new TableLockDTO();
        tableLockDTO1.setDeviceType(0);
        tableLockDTO1.setStoreName("storeName");
        when(mockTableClientService.releaseLock(tableLockDTO1)).thenReturn(false);

        // Run the test
        final boolean result = tableServiceImplUnderTest.releaseLock(tableLockDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testReleaseLock_TableClientServiceReturnsTrue() {
        // Setup
        final TableLockDTO tableLockDTO = new TableLockDTO();
        tableLockDTO.setDeviceType(0);
        tableLockDTO.setStoreName("storeName");

        // Configure TableClientService.releaseLock(...).
        final TableLockDTO tableLockDTO1 = new TableLockDTO();
        tableLockDTO1.setDeviceType(0);
        tableLockDTO1.setStoreName("storeName");
        when(mockTableClientService.releaseLock(tableLockDTO1)).thenReturn(true);

        // Run the test
        final boolean result = tableServiceImplUnderTest.releaseLock(tableLockDTO);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testQueryTableByGuid() {
        // Setup
        final TableDTO expectedResult = TableDTO.builder()
                .orderGuid("orderGuid")
                .isAcceptOrder(false)
                .build();

        // Configure TableClientService.getTableByGuid(...).
        final TableDTO tableDTO = TableDTO.builder()
                .orderGuid("orderGuid")
                .isAcceptOrder(false)
                .build();
        when(mockTableClientService.getTableByGuid("tableGuid")).thenReturn(tableDTO);

        // Configure OrderItemClientService.findByOrderGuid(...).
        final OrderDTO orderDTO = OrderDTO.builder()
                .deviceType(0)
                .build();
        when(mockOrderItemClientService.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        // Configure OrderItemClientService.listPadOrderDTOList(...).
        final PadOrderRespDTO padOrderRespDTO = new PadOrderRespDTO();
        padOrderRespDTO.setGuid(0L);
        padOrderRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setOrderGuid("orderGuid");
        padOrderRespDTO.setOrderState(0);
        final List<PadOrderRespDTO> padOrderRespDTOS = Arrays.asList(padOrderRespDTO);
        when(mockOrderItemClientService.listPadOrderDTOList("orderGuid")).thenReturn(padOrderRespDTOS);

        // Configure OrderItemClientService.queryItemByOrderGuid(...).
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("3d616f43-228b-46fc-96db-cb66ddb2c0ca");
        dineInItemDTO.setOrderGuid("orderGuid");
        dineInItemDTO.setOriginalOrderItemGuid(0L);
        dineInItemDTO.setAdjustNumber(new BigDecimal("0.00"));
        dineInItemDTO.setRollbackCount(new BigDecimal("0.00"));
        final List<DineInItemDTO> inItemDTOList = Arrays.asList(dineInItemDTO);
        when(mockOrderItemClientService.queryItemByOrderGuid("orderGuid")).thenReturn(inItemDTOList);

        // Run the test
        final TableDTO result = tableServiceImplUnderTest.queryTableByGuid("tableGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryTableByGuid_OrderItemClientServiceListPadOrderDTOListReturnsNoItems() {
        // Setup
        final TableDTO expectedResult = TableDTO.builder()
                .orderGuid("orderGuid")
                .isAcceptOrder(false)
                .build();

        // Configure TableClientService.getTableByGuid(...).
        final TableDTO tableDTO = TableDTO.builder()
                .orderGuid("orderGuid")
                .isAcceptOrder(false)
                .build();
        when(mockTableClientService.getTableByGuid("tableGuid")).thenReturn(tableDTO);

        // Configure OrderItemClientService.findByOrderGuid(...).
        final OrderDTO orderDTO = OrderDTO.builder()
                .deviceType(0)
                .build();
        when(mockOrderItemClientService.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        when(mockOrderItemClientService.listPadOrderDTOList("orderGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final TableDTO result = tableServiceImplUnderTest.queryTableByGuid("tableGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryTableByGuid_OrderItemClientServiceQueryItemByOrderGuidReturnsNoItems() {
        // Setup
        final TableDTO expectedResult = TableDTO.builder()
                .orderGuid("orderGuid")
                .isAcceptOrder(false)
                .build();

        // Configure TableClientService.getTableByGuid(...).
        final TableDTO tableDTO = TableDTO.builder()
                .orderGuid("orderGuid")
                .isAcceptOrder(false)
                .build();
        when(mockTableClientService.getTableByGuid("tableGuid")).thenReturn(tableDTO);

        // Configure OrderItemClientService.findByOrderGuid(...).
        final OrderDTO orderDTO = OrderDTO.builder()
                .deviceType(0)
                .build();
        when(mockOrderItemClientService.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        when(mockOrderItemClientService.queryItemByOrderGuid("orderGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final TableDTO result = tableServiceImplUnderTest.queryTableByGuid("tableGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListTable() {
        // Setup
        final BossTableQueryDTO queryDTO = new BossTableQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setAreaGuid("areaGuid");
        queryDTO.setTableGuidList(Arrays.asList("value"));
        queryDTO.setEnterpriseGuid("enterpriseGuid");

        final BossTableRespDTO tableRespDTO = new BossTableRespDTO();
        tableRespDTO.setTableGuid("tableGuid");
        tableRespDTO.setTableCode("tableCode");
        tableRespDTO.setAreaGuid("areaGuid");
        tableRespDTO.setAreaName("areaName");
        tableRespDTO.setOrderAmount(new BigDecimal("0.00"));
        final List<BossTableRespDTO> expectedResult = Arrays.asList(tableRespDTO);

        // Configure TableClientService.queryTable(...).
        final TableOrderDTO tableOrderDTO = new TableOrderDTO();
        tableOrderDTO.setDeviceType(0);
        tableOrderDTO.setStoreName("storeName");
        tableOrderDTO.setTableGuid("tableGuid");
        tableOrderDTO.setOrderAmount(new BigDecimal("0.00"));
        tableOrderDTO.setOrderGuid("orderGuid");
        tableOrderDTO.setActualGuestsNo(0);
        tableOrderDTO.setStatus(0);
        tableOrderDTO.setSubStatus(new HashSet<>(Arrays.asList(0)));
        tableOrderDTO.setPrintPreBillNum(0);
        final List<TableOrderDTO> tableOrderDTOS = Arrays.asList(tableOrderDTO);
        final BossTableQueryDTO queryDTO1 = new BossTableQueryDTO();
        queryDTO1.setStoreGuid("storeGuid");
        queryDTO1.setAreaGuid("areaGuid");
        queryDTO1.setTableGuidList(Arrays.asList("value"));
        queryDTO1.setEnterpriseGuid("enterpriseGuid");
        when(mockTableClientService.queryTable(queryDTO1)).thenReturn(tableOrderDTOS);

        // Configure DineInOrderClientService.batchGetTableInfo2(...).
        final OrderTableInfoDTO tableInfoDTO = new OrderTableInfoDTO();
        tableInfoDTO.setGuid("fcfa140d-f870-4323-91d8-598428d48843");
        tableInfoDTO.setOrderFee(new BigDecimal("0.00"));
        tableInfoDTO.setState(0);
        tableInfoDTO.setPrintPreBillNum(0);
        tableInfoDTO.setGuestCount(0);
        tableInfoDTO.setUpperState(0);
        tableInfoDTO.setOrderFeeForCombine(new BigDecimal("0.00"));
        final List<OrderTableInfoDTO> orderTableInfoDTOS = Arrays.asList(tableInfoDTO);
        when(mockDineInOrderClientService.batchGetTableInfo2(new OrderGuidsDTO(Arrays.asList("value"))))
                .thenReturn(orderTableInfoDTOS);

        // Run the test
        final List<BossTableRespDTO> result = tableServiceImplUnderTest.listTable(queryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockTableClientService).compensationTableStatus(
                new CompensationTableReqDTO(Arrays.asList(new CompensationTableDTO("tableGuid", "orderGuid")),
                        "enterpriseGuid"));
    }

    @Test
    public void testListTable_TableClientServiceQueryTableReturnsNoItems() {
        // Setup
        final BossTableQueryDTO queryDTO = new BossTableQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setAreaGuid("areaGuid");
        queryDTO.setTableGuidList(Arrays.asList("value"));
        queryDTO.setEnterpriseGuid("enterpriseGuid");

        final BossTableRespDTO tableRespDTO = new BossTableRespDTO();
        tableRespDTO.setTableGuid("tableGuid");
        tableRespDTO.setTableCode("tableCode");
        tableRespDTO.setAreaGuid("areaGuid");
        tableRespDTO.setAreaName("areaName");
        tableRespDTO.setOrderAmount(new BigDecimal("0.00"));
        final List<BossTableRespDTO> expectedResult = Arrays.asList(tableRespDTO);

        // Configure TableClientService.queryTable(...).
        final BossTableQueryDTO queryDTO1 = new BossTableQueryDTO();
        queryDTO1.setStoreGuid("storeGuid");
        queryDTO1.setAreaGuid("areaGuid");
        queryDTO1.setTableGuidList(Arrays.asList("value"));
        queryDTO1.setEnterpriseGuid("enterpriseGuid");
        when(mockTableClientService.queryTable(queryDTO1)).thenReturn(Collections.emptyList());

        // Configure DineInOrderClientService.batchGetTableInfo2(...).
        final OrderTableInfoDTO tableInfoDTO = new OrderTableInfoDTO();
        tableInfoDTO.setGuid("fcfa140d-f870-4323-91d8-598428d48843");
        tableInfoDTO.setOrderFee(new BigDecimal("0.00"));
        tableInfoDTO.setState(0);
        tableInfoDTO.setPrintPreBillNum(0);
        tableInfoDTO.setGuestCount(0);
        tableInfoDTO.setUpperState(0);
        tableInfoDTO.setOrderFeeForCombine(new BigDecimal("0.00"));
        final List<OrderTableInfoDTO> orderTableInfoDTOS = Arrays.asList(tableInfoDTO);
        when(mockDineInOrderClientService.batchGetTableInfo2(new OrderGuidsDTO(Arrays.asList("value"))))
                .thenReturn(orderTableInfoDTOS);

        // Run the test
        final List<BossTableRespDTO> result = tableServiceImplUnderTest.listTable(queryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockTableClientService).compensationTableStatus(
                new CompensationTableReqDTO(Arrays.asList(new CompensationTableDTO("tableGuid", "orderGuid")),
                        "enterpriseGuid"));
    }

    @Test
    public void testListTable_DineInOrderClientServiceReturnsNoItems() {
        // Setup
        final BossTableQueryDTO queryDTO = new BossTableQueryDTO();
        queryDTO.setStoreGuid("storeGuid");
        queryDTO.setAreaGuid("areaGuid");
        queryDTO.setTableGuidList(Arrays.asList("value"));
        queryDTO.setEnterpriseGuid("enterpriseGuid");

        final BossTableRespDTO tableRespDTO = new BossTableRespDTO();
        tableRespDTO.setTableGuid("tableGuid");
        tableRespDTO.setTableCode("tableCode");
        tableRespDTO.setAreaGuid("areaGuid");
        tableRespDTO.setAreaName("areaName");
        tableRespDTO.setOrderAmount(new BigDecimal("0.00"));
        final List<BossTableRespDTO> expectedResult = Arrays.asList(tableRespDTO);

        // Configure TableClientService.queryTable(...).
        final TableOrderDTO tableOrderDTO = new TableOrderDTO();
        tableOrderDTO.setDeviceType(0);
        tableOrderDTO.setStoreName("storeName");
        tableOrderDTO.setTableGuid("tableGuid");
        tableOrderDTO.setOrderAmount(new BigDecimal("0.00"));
        tableOrderDTO.setOrderGuid("orderGuid");
        tableOrderDTO.setActualGuestsNo(0);
        tableOrderDTO.setStatus(0);
        tableOrderDTO.setSubStatus(new HashSet<>(Arrays.asList(0)));
        tableOrderDTO.setPrintPreBillNum(0);
        final List<TableOrderDTO> tableOrderDTOS = Arrays.asList(tableOrderDTO);
        final BossTableQueryDTO queryDTO1 = new BossTableQueryDTO();
        queryDTO1.setStoreGuid("storeGuid");
        queryDTO1.setAreaGuid("areaGuid");
        queryDTO1.setTableGuidList(Arrays.asList("value"));
        queryDTO1.setEnterpriseGuid("enterpriseGuid");
        when(mockTableClientService.queryTable(queryDTO1)).thenReturn(tableOrderDTOS);

        when(mockDineInOrderClientService.batchGetTableInfo2(new OrderGuidsDTO(Arrays.asList("value"))))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<BossTableRespDTO> result = tableServiceImplUnderTest.listTable(queryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
