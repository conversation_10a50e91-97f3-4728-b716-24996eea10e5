package com.holderzone.holder.saas.aggregation.weixin.controller.deal;

import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.WxClientService;
import com.holderzone.saas.store.dto.weixin.deal.WxStoreUserOrderItemDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(UserOrderController.class)
public class UserOrderControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxClientService mockWxClientService;

    @Test
    public void testGetStoreUserOrderList() throws Exception {
        // Setup
        // Configure WxClientService.userStoreList(...).
        final List<WxStoreUserOrderItemDTO> wxStoreUserOrderItemDTOS = Arrays.asList(
                new WxStoreUserOrderItemDTO("48c3e667-f030-4fa0-9c17-1731fbbb7e06", "storeName", "brandName", "logUrl",
                        0, new BigDecimal("0.00"), 0, new BigDecimal("0.00"), new BigDecimal("0.00"), 0,
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), Arrays.asList("value")));
        when(mockWxClientService.userStoreList()).thenReturn(wxStoreUserOrderItemDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/deal/user_order/store")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetStoreUserOrderList_WxClientServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockWxClientService.userStoreList()).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/deal/user_order/store")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testUserStorePage() throws Exception {
        // Setup
        when(mockWxClientService.userStorePage(0, 0)).thenReturn(null);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/user_order/store_page")
                        .param("pageNo", "0")
                        .param("pageSize", "0")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
