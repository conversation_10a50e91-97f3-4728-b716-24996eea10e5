package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.holder.saas.aggregation.app.service.TableService;
import com.holderzone.holder.saas.aggregation.app.service.feign.OrganizationClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.business.BindupAccountsClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DinnerDailyClientService;
import com.holderzone.saas.store.dto.organization.BusinessDateReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.store.store.BindupAccountsDTO;
import com.holderzone.saas.store.dto.store.store.BindupAccountsTips;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordLessDTO;
import com.holderzone.saas.store.reserve.api.dto.TableOrderReserveDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.StopWatch;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BindUpAccountsServiceImplTest {

    @Mock
    private OrganizationClientService mockOrganizationClientService;
    @Mock
    private TableService mockTableService;
    @Mock
    private BindupAccountsClientService mockBindupAccountsClientService;
    @Mock
    private DinnerDailyClientService mockTradingClient;

    @InjectMocks
    private BindUpAccountsServiceImpl bindUpAccountsServiceImplUnderTest;

    @Test
    public void testLoginAutoBuAccounts() {
        // Setup
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));

        final BindupAccountsTips expectedResult = new BindupAccountsTips();
        expectedResult.setTips(0);
        expectedResult.setCurrTime(LocalDate.of(2020, 1, 1));
        expectedResult.setIsShowCash(false);
        expectedResult.setCanOpenTable(false);

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("425d2432-271e-4f2c-b260-cedfd6ffa772");
        storeDTO.setCode("code");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        when(mockTradingClient.queryOrderNumForStoreGuid("storeGuid")).thenReturn(0);

        // Configure OrganizationClientService.queryBusinessDay(...).
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");
        when(mockOrganizationClientService.queryBusinessDay(businessDateReqDTO)).thenReturn(LocalDate.of(2020, 1, 1));

        // Configure BindupAccountsClientService.queryBindUpAccountsLast(...).
        final BindupAccountsDTO bindupAccountsDTO = new BindupAccountsDTO();
        bindupAccountsDTO.setId(0L);
        bindupAccountsDTO.setBindupAccounts(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bindupAccountsDTO.setStoreGuid("storeGuid");
        bindupAccountsDTO.setUserGuid("userGuid");
        bindupAccountsDTO.setUserName("userName");
        when(mockBindupAccountsClientService.queryBindUpAccountsLast("storeGuid")).thenReturn(bindupAccountsDTO);

        // Configure TableService.queryTable(...).
        final TableOrderReserveDTO tableOrderReserveDTO = new TableOrderReserveDTO();
        final ReserveRecordLessDTO record = new ReserveRecordLessDTO();
        record.setGuid("02c9683f-6e71-4f5d-990e-9bd1c5f3b19c");
        record.setStoreGuid("storeGuid");
        record.setNumber(0);
        record.setState("state");
        tableOrderReserveDTO.setRecord(record);
        final List<TableOrderReserveDTO> tableOrderReserveDTOS = Arrays.asList(tableOrderReserveDTO);
        final TableBasicQueryDTO tableBasicQueryDTO1 = new TableBasicQueryDTO();
        tableBasicQueryDTO1.setStoreGuid("storeGuid");
        tableBasicQueryDTO1.setAreaGuid("areaGuid");
        tableBasicQueryDTO1.setTableGuidList(Arrays.asList("value"));
        when(mockTableService.queryTable(eq(tableBasicQueryDTO1), any(StopWatch.class)))
                .thenReturn(tableOrderReserveDTOS);

        // Run the test
        final BindupAccountsTips result = bindUpAccountsServiceImplUnderTest.loginAutoBuAccounts(tableBasicQueryDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testLoginAutoBuAccounts_BindupAccountsClientServiceReturnsNull() {
        // Setup
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));

        final BindupAccountsTips expectedResult = new BindupAccountsTips();
        expectedResult.setTips(0);
        expectedResult.setCurrTime(LocalDate.of(2020, 1, 1));
        expectedResult.setIsShowCash(false);
        expectedResult.setCanOpenTable(false);

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("425d2432-271e-4f2c-b260-cedfd6ffa772");
        storeDTO.setCode("code");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        when(mockTradingClient.queryOrderNumForStoreGuid("storeGuid")).thenReturn(0);
        when(mockBindupAccountsClientService.queryBindUpAccountsLast("storeGuid")).thenReturn(null);

        // Configure OrganizationClientService.queryBusinessDay(...).
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");
        when(mockOrganizationClientService.queryBusinessDay(businessDateReqDTO)).thenReturn(LocalDate.of(2020, 1, 1));

        when(mockTradingClient.getfristorderForStoreGuid("storeGuid"))
                .thenReturn(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure TableService.queryTable(...).
        final TableOrderReserveDTO tableOrderReserveDTO = new TableOrderReserveDTO();
        final ReserveRecordLessDTO record = new ReserveRecordLessDTO();
        record.setGuid("02c9683f-6e71-4f5d-990e-9bd1c5f3b19c");
        record.setStoreGuid("storeGuid");
        record.setNumber(0);
        record.setState("state");
        tableOrderReserveDTO.setRecord(record);
        final List<TableOrderReserveDTO> tableOrderReserveDTOS = Arrays.asList(tableOrderReserveDTO);
        final TableBasicQueryDTO tableBasicQueryDTO1 = new TableBasicQueryDTO();
        tableBasicQueryDTO1.setStoreGuid("storeGuid");
        tableBasicQueryDTO1.setAreaGuid("areaGuid");
        tableBasicQueryDTO1.setTableGuidList(Arrays.asList("value"));
        when(mockTableService.queryTable(eq(tableBasicQueryDTO1), any(StopWatch.class)))
                .thenReturn(tableOrderReserveDTOS);

        // Run the test
        final BindupAccountsTips result = bindUpAccountsServiceImplUnderTest.loginAutoBuAccounts(tableBasicQueryDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testLoginAutoBuAccounts_TableServiceReturnsNoItems() {
        // Setup
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));

        final BindupAccountsTips expectedResult = new BindupAccountsTips();
        expectedResult.setTips(0);
        expectedResult.setCurrTime(LocalDate.of(2020, 1, 1));
        expectedResult.setIsShowCash(false);
        expectedResult.setCanOpenTable(false);

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("425d2432-271e-4f2c-b260-cedfd6ffa772");
        storeDTO.setCode("code");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        when(mockTradingClient.queryOrderNumForStoreGuid("storeGuid")).thenReturn(0);

        // Configure OrganizationClientService.queryBusinessDay(...).
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");
        when(mockOrganizationClientService.queryBusinessDay(businessDateReqDTO)).thenReturn(LocalDate.of(2020, 1, 1));

        // Configure BindupAccountsClientService.queryBindUpAccountsLast(...).
        final BindupAccountsDTO bindupAccountsDTO = new BindupAccountsDTO();
        bindupAccountsDTO.setId(0L);
        bindupAccountsDTO.setBindupAccounts(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bindupAccountsDTO.setStoreGuid("storeGuid");
        bindupAccountsDTO.setUserGuid("userGuid");
        bindupAccountsDTO.setUserName("userName");
        when(mockBindupAccountsClientService.queryBindUpAccountsLast("storeGuid")).thenReturn(bindupAccountsDTO);

        when(mockTradingClient.getfristorderForStoreGuid("storeGuid"))
                .thenReturn(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure TableService.queryTable(...).
        final TableBasicQueryDTO tableBasicQueryDTO1 = new TableBasicQueryDTO();
        tableBasicQueryDTO1.setStoreGuid("storeGuid");
        tableBasicQueryDTO1.setAreaGuid("areaGuid");
        tableBasicQueryDTO1.setTableGuidList(Arrays.asList("value"));
        when(mockTableService.queryTable(eq(tableBasicQueryDTO1), any(StopWatch.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final BindupAccountsTips result = bindUpAccountsServiceImplUnderTest.loginAutoBuAccounts(tableBasicQueryDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testNoTipsBindUpAccount() {
        // Setup
        final BindupAccountsTips expectedResult = new BindupAccountsTips();
        expectedResult.setTips(0);
        expectedResult.setCurrTime(LocalDate.of(2020, 1, 1));
        expectedResult.setIsShowCash(false);
        expectedResult.setCanOpenTable(false);

        // Configure OrganizationClientService.queryBusinessDay(...).
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");
        when(mockOrganizationClientService.queryBusinessDay(businessDateReqDTO)).thenReturn(LocalDate.of(2020, 1, 1));

        // Run the test
        final BindupAccountsTips result = bindUpAccountsServiceImplUnderTest.noTipsBindUpAccount("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCurrentTimeDay() {
        // Setup
        // Configure OrganizationClientService.queryBusinessDay(...).
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");
        when(mockOrganizationClientService.queryBusinessDay(businessDateReqDTO)).thenReturn(LocalDate.of(2020, 1, 1));

        // Run the test
        final LocalDate result = bindUpAccountsServiceImplUnderTest.currentTimeDay("storeGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
        assertEquals(LocalDate.of(2020, 1, 1), result);
    }

    @Test
    public void testCheckTableStatus() {
        // Setup
        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("425d2432-271e-4f2c-b260-cedfd6ffa772");
        storeDTO.setCode("code");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure BindupAccountsClientService.queryBindUpAccountsLast(...).
        final BindupAccountsDTO bindupAccountsDTO = new BindupAccountsDTO();
        bindupAccountsDTO.setId(0L);
        bindupAccountsDTO.setBindupAccounts(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bindupAccountsDTO.setStoreGuid("storeGuid");
        bindupAccountsDTO.setUserGuid("userGuid");
        bindupAccountsDTO.setUserName("userName");
        when(mockBindupAccountsClientService.queryBindUpAccountsLast("storeGuid")).thenReturn(bindupAccountsDTO);

        // Configure OrganizationClientService.queryBusinessDay(...).
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");
        when(mockOrganizationClientService.queryBusinessDay(businessDateReqDTO)).thenReturn(LocalDate.of(2020, 1, 1));

        // Configure TableService.queryTable(...).
        final TableOrderReserveDTO tableOrderReserveDTO = new TableOrderReserveDTO();
        final ReserveRecordLessDTO record = new ReserveRecordLessDTO();
        record.setGuid("02c9683f-6e71-4f5d-990e-9bd1c5f3b19c");
        record.setStoreGuid("storeGuid");
        record.setNumber(0);
        record.setState("state");
        tableOrderReserveDTO.setRecord(record);
        final List<TableOrderReserveDTO> tableOrderReserveDTOS = Arrays.asList(tableOrderReserveDTO);
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableService.queryTable(eq(tableBasicQueryDTO), any(StopWatch.class)))
                .thenReturn(tableOrderReserveDTOS);

        // Run the test
        bindUpAccountsServiceImplUnderTest.checkTableStatus("storeGuid");

        // Verify the results
        verify(mockBindupAccountsClientService).sendMqForCanOpenTalbe("storeGuid");
    }

    @Test
    public void testCheckTableStatus_BindupAccountsClientServiceQueryBindUpAccountsLastReturnsNull() {
        // Setup
        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("425d2432-271e-4f2c-b260-cedfd6ffa772");
        storeDTO.setCode("code");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        when(mockBindupAccountsClientService.queryBindUpAccountsLast("storeGuid")).thenReturn(null);

        // Run the test
        bindUpAccountsServiceImplUnderTest.checkTableStatus("storeGuid");

        // Verify the results
    }

    @Test
    public void testCheckTableStatus_TableServiceReturnsNoItems() {
        // Setup
        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("425d2432-271e-4f2c-b260-cedfd6ffa772");
        storeDTO.setCode("code");
        storeDTO.setIsBuAccounts(0);
        storeDTO.setIsShowCash(0);
        storeDTO.setCanOpenTable(0);
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure BindupAccountsClientService.queryBindUpAccountsLast(...).
        final BindupAccountsDTO bindupAccountsDTO = new BindupAccountsDTO();
        bindupAccountsDTO.setId(0L);
        bindupAccountsDTO.setBindupAccounts(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bindupAccountsDTO.setStoreGuid("storeGuid");
        bindupAccountsDTO.setUserGuid("userGuid");
        bindupAccountsDTO.setUserName("userName");
        when(mockBindupAccountsClientService.queryBindUpAccountsLast("storeGuid")).thenReturn(bindupAccountsDTO);

        // Configure OrganizationClientService.queryBusinessDay(...).
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");
        when(mockOrganizationClientService.queryBusinessDay(businessDateReqDTO)).thenReturn(LocalDate.of(2020, 1, 1));

        // Configure TableService.queryTable(...).
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableService.queryTable(eq(tableBasicQueryDTO), any(StopWatch.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        bindUpAccountsServiceImplUnderTest.checkTableStatus("storeGuid");

        // Verify the results
        verify(mockBindupAccountsClientService).sendMqForCanOpenTalbe("storeGuid");
    }
}
