package com.holderzone.holder.saas.aggregation.weixin.controller.memberCenter;

import com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.WxBrandService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.memberCenter.WxMemberCenterCardService;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseNotOpenCardLevelRights;
import com.holderzone.holder.saas.member.wechat.dto.member.*;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.store.store.StoreProductDTO;
import com.holderzone.saas.store.dto.weixin.WxMemberCenterCardRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(WxMemberCenterCardController.class)
public class WxMemberCenterCardControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxMemberCenterCardService mockWxMemberCenterCardService;
    @MockBean
    private HsaBaseClientService mockHsaBaseClientService;
    @MockBean
    private WxBrandService mockWxBrandService;

    @Test
    public void testCardList() throws Exception {
        // Setup
        // Configure WxMemberCenterCardService.cardList(...).
        final ResponseMemberCardListOwned responseMemberCardListOwned = new ResponseMemberCardListOwned();
        responseMemberCardListOwned.setCardQRCode("cardQRCode");
        responseMemberCardListOwned.setCardGuid("cardGuid");
        responseMemberCardListOwned.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCardListOwned.setCardLogo("cardLogo");
        responseMemberCardListOwned.setCardIcon("");
        final WxMemberCenterCardRespDTO wxMemberCenterCardRespDTO = new WxMemberCenterCardRespDTO(
                Arrays.asList(responseMemberCardListOwned), false);
        when(mockWxMemberCenterCardService.cardList(
                new WxStoreConsumerDTO("consumerGuid", "openId", "unionId", "nickName", "headImgUrl", 0, "country",
                        "province", "city", "","","phone", 0, "deviceId", "enterpriseGuid", "enterpriseName", "storeGuid",
                        "storeName", "userGuid", "userName", "account", 0, "diningTableGuid", "tableCode",
                        "diningTableName", "areaGuid", "areaName", "brandName", "brandGuid", "brandLogo", false,
                        "memberInfoGuid", "phoneNum", false, "operSubjectGuid", false)))
                .thenReturn(wxMemberCenterCardRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_member_card/card_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testMemberCardList() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getMemberCard(...).
        final ResponseMemberCardAll responseMemberCardAll = new ResponseMemberCardAll();
        final ResponseMemberCardListOwned responseMemberCardListOwned = new ResponseMemberCardListOwned();
        responseMemberCardListOwned.setCardLogo("cardLogo");
        responseMemberCardListOwned.setCardIcon("");
        responseMemberCardAll.setOpenedCardList(Arrays.asList(responseMemberCardListOwned));
        final ResponseMemberCardListUnowned responseMemberCardListUnowned = new ResponseMemberCardListUnowned();
        responseMemberCardListUnowned.setCardIcon("");
        responseMemberCardListUnowned.setCardLogo("cardLogo");
        responseMemberCardAll.setNonactivatedCardList(Arrays.asList(responseMemberCardListUnowned));
        final ResponseModel<ResponseMemberCardAll> responseMemberCardAllResponseModel = new ResponseModel<>(
                responseMemberCardAll);
        final RequestQueryMemberCardList requestQueryMemberCardList = new RequestQueryMemberCardList();
        requestQueryMemberCardList.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberCardList.setEnterpriseGuid("enterpriseGuid");
        requestQueryMemberCardList.setBrandGuid("brandGuid");
        requestQueryMemberCardList.setStoreGuid("storeGuid");
        when(mockHsaBaseClientService.getMemberCard(requestQueryMemberCardList))
                .thenReturn(responseMemberCardAllResponseModel);

        // Configure WxBrandService.getBrandDetail(...).
        final BrandDTO brandDTO = new BrandDTO("94c8739e-cb40-4acd-901c-f1d999d21b6e",
                "a46ac91c-c4b6-410a-8131-c2566c8e5cdf", "name", "description", "logoUrl", false, false,
                "createUserGuid", "modifiedUserGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "mchntTypeCode", Arrays.asList(
                new StoreDTO("2ad1a582-9b99-48fc-8468-bf1716e3cdca", "code", "name", "belongBrandGuid",
                        "belongBrandName", "parentIds", LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0), "contactName",
                        "contactTel", "provinceCode", "provinceName", "cityCode", "cityName", "countyCode",
                        "countyName", "addressDetail", "longitude", "latitude", false, false, "photos",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createUserGuid",
                        "modifiedUserGuid", Arrays.asList(
                        new StoreProductDTO("productGuid", "productName", 0, LocalDate.of(2020, 1, 1),
                                LocalDate.of(2020, 1, 1), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, 0)),
                        Arrays.asList(), "mchntTypeCode", "logoUrl", "openingHours", 0, 0, 0, 0, "storeNotification",
                        "storeDoorPhoto", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                        0, 0, LocalDate.of(2020, 1, 1))), 0, 0, 0, 0, false);
        final RequestQueryMemberCardList memberCardListQueryReqDTO = new RequestQueryMemberCardList();
        memberCardListQueryReqDTO.setMemberInfoGuid("memberInfoGuid");
        memberCardListQueryReqDTO.setEnterpriseGuid("enterpriseGuid");
        memberCardListQueryReqDTO.setBrandGuid("brandGuid");
        memberCardListQueryReqDTO.setStoreGuid("storeGuid");
        when(mockWxBrandService.getBrandDetail(memberCardListQueryReqDTO)).thenReturn(brandDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_member_card/member_card_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetMemberCard() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getMemberCard(...).
        final ResponseMemberCardAll responseMemberCardAll = new ResponseMemberCardAll();
        final ResponseMemberCardListOwned responseMemberCardListOwned = new ResponseMemberCardListOwned();
        responseMemberCardListOwned.setCardLogo("cardLogo");
        responseMemberCardListOwned.setCardIcon("");
        responseMemberCardAll.setOpenedCardList(Arrays.asList(responseMemberCardListOwned));
        final ResponseMemberCardListUnowned responseMemberCardListUnowned = new ResponseMemberCardListUnowned();
        responseMemberCardListUnowned.setCardIcon("");
        responseMemberCardListUnowned.setCardLogo("cardLogo");
        responseMemberCardAll.setNonactivatedCardList(Arrays.asList(responseMemberCardListUnowned));
        final ResponseModel<ResponseMemberCardAll> responseMemberCardAllResponseModel = new ResponseModel<>(
                responseMemberCardAll);
        final RequestQueryMemberCardList requestQueryMemberCardList = new RequestQueryMemberCardList();
        requestQueryMemberCardList.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberCardList.setEnterpriseGuid("enterpriseGuid");
        requestQueryMemberCardList.setBrandGuid("brandGuid");
        requestQueryMemberCardList.setStoreGuid("storeGuid");
        when(mockHsaBaseClientService.getMemberCard(requestQueryMemberCardList))
                .thenReturn(responseMemberCardAllResponseModel);

        // Configure WxBrandService.getBrandDetail(...).
        final BrandDTO brandDTO = new BrandDTO("94c8739e-cb40-4acd-901c-f1d999d21b6e",
                "a46ac91c-c4b6-410a-8131-c2566c8e5cdf", "name", "description", "logoUrl", false, false,
                "createUserGuid", "modifiedUserGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "mchntTypeCode", Arrays.asList(
                new StoreDTO("2ad1a582-9b99-48fc-8468-bf1716e3cdca", "code", "name", "belongBrandGuid",
                        "belongBrandName", "parentIds", LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0), "contactName",
                        "contactTel", "provinceCode", "provinceName", "cityCode", "cityName", "countyCode",
                        "countyName", "addressDetail", "longitude", "latitude", false, false, "photos",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createUserGuid",
                        "modifiedUserGuid", Arrays.asList(
                        new StoreProductDTO("productGuid", "productName", 0, LocalDate.of(2020, 1, 1),
                                LocalDate.of(2020, 1, 1), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, 0)),
                        Arrays.asList(), "mchntTypeCode", "logoUrl", "openingHours", 0, 0, 0, 0, "storeNotification",
                        "storeDoorPhoto", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                        0, 0, LocalDate.of(2020, 1, 1))), 0, 0, 0, 0, false);
        final RequestQueryMemberCardList memberCardListQueryReqDTO = new RequestQueryMemberCardList();
        memberCardListQueryReqDTO.setMemberInfoGuid("memberInfoGuid");
        memberCardListQueryReqDTO.setEnterpriseGuid("enterpriseGuid");
        memberCardListQueryReqDTO.setBrandGuid("brandGuid");
        memberCardListQueryReqDTO.setStoreGuid("storeGuid");
        when(mockWxBrandService.getBrandDetail(memberCardListQueryReqDTO)).thenReturn(brandDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_member_card/no_open_memberCard")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetNewCardLevelSummaryInfo() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getNewCardLevelSummaryInfo(...).
        final ResponseCardLevelSummaryNew responseCardLevelSummaryNew = new ResponseCardLevelSummaryNew();
        final CurrentInfo currentInfo = new CurrentInfo();
        currentInfo.setMemberInfoGuid("memberInfoGuid");
        currentInfo.setHeadImgUrl("headImgUrl");
        currentInfo.setCardGuid("cardGuid");
        currentInfo.setCardName("cardName");
        responseCardLevelSummaryNew.setCurrentInfo(currentInfo);
        final ResponseModel<ResponseCardLevelSummaryNew> responseCardLevelSummaryNewResponseModel = new ResponseModel<>(
                responseCardLevelSummaryNew);
        final RequestMemberCardSummaryQuery memberCardSummaryQueryReqDTO = new RequestMemberCardSummaryQuery();
        memberCardSummaryQueryReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        memberCardSummaryQueryReqDTO.setCardGuid("cardGuid");
        memberCardSummaryQueryReqDTO.setLevelGuid("levelGuid");
        when(mockHsaBaseClientService.getNewCardLevelSummaryInfo(memberCardSummaryQueryReqDTO))
                .thenReturn(responseCardLevelSummaryNewResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_member_card/getNewCardLevelSummaryInfo")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetCardLevelAndRightInfo() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getCardLevelAndRightInfo(...).
        final CardLevelList cardLevelList = new CardLevelList();
        cardLevelList.setCardLevelGuid("cardLevelGuid");
        cardLevelList.setCardLevelName("cardLevelName");
        cardLevelList.setCardLevelNum(0);
        cardLevelList.setCardLevelIcon("cardLevelIcon");
        cardLevelList.setCardLevelGrowthValue(0);
        final ResponseModel<List<CardLevelList>> listResponseModel = new ResponseModel<>(Arrays.asList(cardLevelList));
        when(mockHsaBaseClientService.getCardLevelAndRightInfo("cardGuid")).thenReturn(listResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/wx_member_card/getCardLevelAndRightInfo")
                        .param("cardGuid", "cardGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetCardLevelAndRightInfo_HsaBaseClientServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getCardLevelAndRightInfo(...).
        final ResponseModel<List<CardLevelList>> listResponseModel = new ResponseModel<>(Collections.emptyList());
        when(mockHsaBaseClientService.getCardLevelAndRightInfo("cardGuid")).thenReturn(listResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/wx_member_card/getCardLevelAndRightInfo")
                        .param("cardGuid", "cardGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testGetNotOpenCardLevelRights() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getNotOpenCardLevelRights(...).
        final ResponseNotOpenCardLevelRights responseNotOpenCardLevelRights = new ResponseNotOpenCardLevelRights();
        final CurrentInfo currentInfo = new CurrentInfo();
        currentInfo.setMemberInfoGuid("memberInfoGuid");
        currentInfo.setHeadImgUrl("headImgUrl");
        currentInfo.setCardGuid("cardGuid");
        currentInfo.setCardName("cardName");
        responseNotOpenCardLevelRights.setCurrentInfo(currentInfo);
        final ResponseModel<ResponseNotOpenCardLevelRights> responseNotOpenCardLevelRightsResponseModel = new ResponseModel<>(
                responseNotOpenCardLevelRights);
        final RequestMemberCardSummaryQuery requestMemberCardSummaryQuery = new RequestMemberCardSummaryQuery();
        requestMemberCardSummaryQuery.setMemberInfoCardGuid("memberInfoCardGuid");
        requestMemberCardSummaryQuery.setCardGuid("cardGuid");
        requestMemberCardSummaryQuery.setLevelGuid("levelGuid");
        when(mockHsaBaseClientService.getNotOpenCardLevelRights(requestMemberCardSummaryQuery))
                .thenReturn(responseNotOpenCardLevelRightsResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_member_card/getNotOpenCardLevelRights")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
