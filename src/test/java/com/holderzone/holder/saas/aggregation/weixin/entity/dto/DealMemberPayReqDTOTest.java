package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;

public class DealMemberPayReqDTOTest {

    private DealMemberPayReqDTO dealMemberPayReqDTOUnderTest;

    @Before
    public void setUp() throws Exception {
        dealMemberPayReqDTOUnderTest = new DealMemberPayReqDTO("orderGuid", "memberPassWord", 0, 0, 0, false,
                new BigDecimal("0.00"), false, "memberInfoCardGuid");
    }

    @Test
    public void testOrderGuidGetterAndSetter() {
        final String orderGuid = "orderGuid";
        dealMemberPayReqDTOUnderTest.setOrderGuid(orderGuid);
        assertThat(dealMemberPayReqDTOUnderTest.getOrderGuid()).isEqualTo(orderGuid);
    }

    @Test
    public void testMemberPassWordGetterAndSetter() {
        final String memberPassWord = "memberPassWord";
        dealMemberPayReqDTOUnderTest.setMemberPassWord(memberPassWord);
        assertThat(dealMemberPayReqDTOUnderTest.getMemberPassWord()).isEqualTo(memberPassWord);
    }

    @Test
    public void testDeviceTypeGetterAndSetter() {
        final Integer deviceType = 0;
        dealMemberPayReqDTOUnderTest.setDeviceType(deviceType);
        assertThat(dealMemberPayReqDTOUnderTest.getDeviceType()).isEqualTo(deviceType);
    }

    @Test
    public void testPayTypeGetterAndSetter() {
        final Integer payType = 0;
        dealMemberPayReqDTOUnderTest.setPayType(payType);
        assertThat(dealMemberPayReqDTOUnderTest.getPayType()).isEqualTo(payType);
    }

    @Test
    public void testTradeModeGetterAndSetter() {
        final Integer tradeMode = 0;
        dealMemberPayReqDTOUnderTest.setTradeMode(tradeMode);
        assertThat(dealMemberPayReqDTOUnderTest.getTradeMode()).isEqualTo(tradeMode);
    }

    @Test
    public void testUseMemberDiscountFlagGetterAndSetter() {
        final Boolean useMemberDiscountFlag = false;
        dealMemberPayReqDTOUnderTest.setUseMemberDiscountFlag(useMemberDiscountFlag);
        assertThat(dealMemberPayReqDTOUnderTest.getUseMemberDiscountFlag()).isFalse();
    }

    @Test
    public void testPayAmountGetterAndSetter() {
        final BigDecimal payAmount = new BigDecimal("0.00");
        dealMemberPayReqDTOUnderTest.setPayAmount(payAmount);
        assertThat(dealMemberPayReqDTOUnderTest.getPayAmount()).isEqualTo(payAmount);
    }

    @Test
    public void testSecondPayFlagGetterAndSetter() {
        final Boolean secondPayFlag = false;
        dealMemberPayReqDTOUnderTest.setSecondPayFlag(secondPayFlag);
        assertThat(dealMemberPayReqDTOUnderTest.getSecondPayFlag()).isFalse();
    }

    @Test
    public void testMemberInfoCardGuidGetterAndSetter() {
        final String memberInfoCardGuid = "memberInfoCardGuid";
        dealMemberPayReqDTOUnderTest.setMemberInfoCardGuid(memberInfoCardGuid);
        assertThat(dealMemberPayReqDTOUnderTest.getMemberInfoCardGuid()).isEqualTo(memberInfoCardGuid);
    }

    @Test
    public void testEquals() {
        assertThat(dealMemberPayReqDTOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() {
        assertThat(dealMemberPayReqDTOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() {
        assertThat(dealMemberPayReqDTOUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() {
        assertThat(dealMemberPayReqDTOUnderTest.toString()).isEqualTo("result");
    }
}
