package com.holderzone.holder.saas.aggregation.app.builder;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderItemRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustOrderDetailRespDTO;
import org.junit.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

public class AdjustByOrderRespDTOBuilderTest {

    @Test
    public void testBuild() {
        // Setup
        final AdjustByOrderRespDTO respDTO = new AdjustByOrderRespDTO();
        final AdjustByOrderItemRespDTO adjustByOrderItemRespDTO = new AdjustByOrderItemRespDTO();
        adjustByOrderItemRespDTO.setOrderItemGuid("orderItemGuid");
        adjustByOrderItemRespDTO.setAdjustType(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("fc02aa82-a549-4017-9967-ac4684732d13");
        dineInItemDTO.setAdjustType(0);
        adjustByOrderItemRespDTO.setAdjustItemList(Arrays.asList(dineInItemDTO));
        respDTO.setOrderItemList(Arrays.asList(adjustByOrderItemRespDTO));
        respDTO.setAdjustNo("adjustNo");
        respDTO.setAdjustPrice(new BigDecimal("0.00"));
        respDTO.setReason("reason");
        respDTO.setCreateStaffName("createStaffName");
        respDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final AdjustOrderDetailRespDTO detailRespDTO = new AdjustOrderDetailRespDTO();
        detailRespDTO.setAdjustNo("adjustNo");
        detailRespDTO.setAdjustPrice(new BigDecimal("0.00"));
        detailRespDTO.setReason("reason");
        detailRespDTO.setCreateStaffName("createStaffName");
        detailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("fc02aa82-a549-4017-9967-ac4684732d13");
        dineInItemDTO1.setAdjustType(0);
        detailRespDTO.setAdjustItemList(Arrays.asList(dineInItemDTO1));

        // Run the test
        AdjustByOrderRespDTOBuilder.build(respDTO, detailRespDTO);

        // Verify the results
    }
}
