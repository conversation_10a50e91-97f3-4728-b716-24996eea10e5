package com.holderzone.holder.saas.aggregation.weixin.controller.item;

import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxItemClientService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.item.resp.ItemAndTypeForAndroidRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemSynRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(WxItemController.class)
public class WxItemControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxItemClientService mockWxItemClientService;

    @Test
    public void testGetItems() throws Exception {
        // Setup
        // Configure WxItemClientService.getItemsForWeixin(...).
        final ItemAndTypeForAndroidRespDTO itemAndTypeForAndroidRespDTO = new ItemAndTypeForAndroidRespDTO();
        final ItemSynRespDTO itemSynRespDTO = new ItemSynRespDTO();
        itemSynRespDTO.setItemGuid("itemGuid");
        itemSynRespDTO.setParentGuid("parentGuid");
        itemSynRespDTO.setTypeGuid("typeGuid");
        itemSynRespDTO.setTypeName("typeName");
        itemAndTypeForAndroidRespDTO.setItemList(Arrays.asList(itemSynRespDTO));
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockWxItemClientService.getItemsForWeixin(baseDTO)).thenReturn(itemAndTypeForAndroidRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wxItem/getItems")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
