package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.holder.saas.aggregation.weixin.service.rpc.StorePayClientService;
import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.dto.pay.SaasPollingDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(WxAggPayController.class)
public class WxAggPayControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private StorePayClientService mockStorePayClientService;

    @Test
    public void testPolling() throws Exception {
        // Setup
        // Configure StorePayClientService.h5Polling(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setMsg("msg");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setSubject("subject");
        aggPayPollingRespDTO.setBody("body");
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setOrderGuid("orderGuid");
        saasPollingDTO.setPayGuid("payGuid");
        when(mockStorePayClientService.h5Polling(saasPollingDTO)).thenReturn(aggPayPollingRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/agg/h5/polling")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testPolling_StorePayClientServiceReturnsError() throws Exception {
        // Setup
        // Configure StorePayClientService.h5Polling(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = AggPayPollingRespDTO.errorResp("code", "msg");
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setOrderGuid("orderGuid");
        saasPollingDTO.setPayGuid("payGuid");
        when(mockStorePayClientService.h5Polling(saasPollingDTO)).thenReturn(aggPayPollingRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/agg/h5/polling")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
