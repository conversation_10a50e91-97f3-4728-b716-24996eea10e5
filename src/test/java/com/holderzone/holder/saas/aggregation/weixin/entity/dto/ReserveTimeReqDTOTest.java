package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class ReserveTimeReqDTOTest {

    @Test
    public void testStoreGuidGetterAndSetter() {
        final ReserveTimeReqDTO reserveTimeReqDTOUnderTest = new ReserveTimeReqDTO("storeGuid");
        final String storeGuid = "storeGuid";
        reserveTimeReqDTOUnderTest.setStoreGuid(storeGuid);
        assertThat(reserveTimeReqDTOUnderTest.getStoreGuid()).isEqualTo(storeGuid);
    }

    @Test
    public void testEquals() throws Exception {
        final ReserveTimeReqDTO reserveTimeReqDTOUnderTest = new ReserveTimeReqDTO("storeGuid");
        assertThat(reserveTimeReqDTOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() throws Exception {
        final ReserveTimeReqDTO reserveTimeReqDTOUnderTest = new ReserveTimeReqDTO("storeGuid");
        assertThat(reserveTimeReqDTOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() throws Exception {
        final ReserveTimeReqDTO reserveTimeReqDTOUnderTest = new ReserveTimeReqDTO("storeGuid");
        assertThat(reserveTimeReqDTOUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() throws Exception {
        final ReserveTimeReqDTO reserveTimeReqDTOUnderTest = new ReserveTimeReqDTO("storeGuid");
        assertThat(reserveTimeReqDTOUnderTest.toString()).isEqualTo("result");
    }
}
