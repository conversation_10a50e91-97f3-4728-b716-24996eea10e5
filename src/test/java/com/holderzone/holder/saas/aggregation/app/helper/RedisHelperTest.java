package com.holderzone.holder.saas.aggregation.app.helper;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.connection.DataType;
import org.springframework.data.redis.core.*;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RedisHelperTest {

    @Mock
    private StringRedisTemplate mockRedisTemplate;

    private RedisHelper redisHelperUnderTest;

    @Before
    public void setUp() throws Exception {
        redisHelperUnderTest = new RedisHelper(mockRedisTemplate);
    }

    @Test
    public void testGenerated() {
        // Setup
        when(mockRedisTemplate.getConnectionFactory()).thenReturn(null);

        // Run the test
        final long result = redisHelperUnderTest.generated("key",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertEquals(0L, result);
    }

    @Test
    public void testGenerated_StringRedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.getConnectionFactory()).thenReturn(null);

        // Run the test
        final long result = redisHelperUnderTest.generated("key",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertEquals(0L, result);
    }

    @Test
    public void testSetNxEx() {
        // Setup
        when(mockRedisTemplate.execute(any(RedisCallback.class))).thenReturn(false);

        // Run the test
        final boolean result = redisHelperUnderTest.setNxEx("key", "value", 0);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testSetNxEx_StringRedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.execute(any(RedisCallback.class))).thenReturn(null);

        // Run the test
        final boolean result = redisHelperUnderTest.setNxEx("key", "value", 0);

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testSetNxEx_StringRedisTemplateReturnsTrue() {
        // Setup
        when(mockRedisTemplate.execute(any(RedisCallback.class))).thenReturn(true);

        // Run the test
        final boolean result = redisHelperUnderTest.setNxEx("key", "value", 0);

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testDelete() {
        // Setup
        when(mockRedisTemplate.delete("key")).thenReturn(false);

        // Run the test
        final boolean result = redisHelperUnderTest.delete("key");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testDelete_StringRedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.delete("key")).thenReturn(null);

        // Run the test
        final boolean result = redisHelperUnderTest.delete("key");

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testDelete_StringRedisTemplateReturnsTrue() {
        // Setup
        when(mockRedisTemplate.delete("key")).thenReturn(true);

        // Run the test
        final boolean result = redisHelperUnderTest.delete("key");

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testDump() {
        // Setup
        when(mockRedisTemplate.dump("key")).thenReturn("content".getBytes());

        // Run the test
        final byte[] result = redisHelperUnderTest.dump("key");

        // Verify the results
        assertArrayEquals("content".getBytes(), result);
    }

    @Test
    public void testDump_StringRedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.dump("key")).thenReturn(null);

        // Run the test
        final byte[] result = redisHelperUnderTest.dump("key");

        // Verify the results
        assertArrayEquals("content".getBytes(), result);
    }

    @Test
    public void testHasKey() {
        // Setup
        when(mockRedisTemplate.hasKey("key")).thenReturn(false);

        // Run the test
        final Boolean result = redisHelperUnderTest.hasKey("key");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testHasKey_StringRedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.hasKey("key")).thenReturn(null);

        // Run the test
        final Boolean result = redisHelperUnderTest.hasKey("key");

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testHasKey_StringRedisTemplateReturnsTrue() {
        // Setup
        when(mockRedisTemplate.hasKey("key")).thenReturn(true);

        // Run the test
        final Boolean result = redisHelperUnderTest.hasKey("key");

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testExpire() {
        // Setup
        when(mockRedisTemplate.expire("key", 0L, TimeUnit.MILLISECONDS)).thenReturn(false);

        // Run the test
        final Boolean result = redisHelperUnderTest.expire("key", 0L, TimeUnit.MILLISECONDS);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testExpire_StringRedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.expire("key", 0L, TimeUnit.MILLISECONDS)).thenReturn(null);

        // Run the test
        final Boolean result = redisHelperUnderTest.expire("key", 0L, TimeUnit.MILLISECONDS);

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testExpire_StringRedisTemplateReturnsTrue() {
        // Setup
        when(mockRedisTemplate.expire("key", 0L, TimeUnit.MILLISECONDS)).thenReturn(true);

        // Run the test
        final Boolean result = redisHelperUnderTest.expire("key", 0L, TimeUnit.MILLISECONDS);

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testExpireAt() {
        // Setup
        when(mockRedisTemplate.expireAt("key", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
                .thenReturn(false);

        // Run the test
        final Boolean result = redisHelperUnderTest.expireAt("key",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testExpireAt_StringRedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.expireAt("key", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
                .thenReturn(null);

        // Run the test
        final Boolean result = redisHelperUnderTest.expireAt("key",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testExpireAt_StringRedisTemplateReturnsTrue() {
        // Setup
        when(mockRedisTemplate.expireAt("key", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
                .thenReturn(true);

        // Run the test
        final Boolean result = redisHelperUnderTest.expireAt("key",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testKeys() {
        // Setup
        when(mockRedisTemplate.keys("pattern")).thenReturn(new HashSet<>(Arrays.asList("value")));

        // Run the test
        final Set<String> result = redisHelperUnderTest.keys("pattern");

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testKeys_StringRedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.keys("pattern")).thenReturn(null);

        // Run the test
        final Set<String> result = redisHelperUnderTest.keys("pattern");

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testKeys_StringRedisTemplateReturnsNoItems() {
        // Setup
        when(mockRedisTemplate.keys("pattern")).thenReturn(Collections.emptySet());

        // Run the test
        final Set<String> result = redisHelperUnderTest.keys("pattern");

        // Verify the results
        assertEquals(Collections.emptySet(), result);
    }

    @Test
    public void testMove() {
        // Setup
        when(mockRedisTemplate.move("key", 0)).thenReturn(false);

        // Run the test
        final Boolean result = redisHelperUnderTest.move("key", 0);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testMove_StringRedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.move("key", 0)).thenReturn(null);

        // Run the test
        final Boolean result = redisHelperUnderTest.move("key", 0);

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testMove_StringRedisTemplateReturnsTrue() {
        // Setup
        when(mockRedisTemplate.move("key", 0)).thenReturn(true);

        // Run the test
        final Boolean result = redisHelperUnderTest.move("key", 0);

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testPersist() {
        // Setup
        when(mockRedisTemplate.persist("key")).thenReturn(false);

        // Run the test
        final Boolean result = redisHelperUnderTest.persist("key");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testPersist_StringRedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.persist("key")).thenReturn(null);

        // Run the test
        final Boolean result = redisHelperUnderTest.persist("key");

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testPersist_StringRedisTemplateReturnsTrue() {
        // Setup
        when(mockRedisTemplate.persist("key")).thenReturn(true);

        // Run the test
        final Boolean result = redisHelperUnderTest.persist("key");

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testGetExpire1() {
        // Setup
        when(mockRedisTemplate.getExpire("key", TimeUnit.MILLISECONDS)).thenReturn(0L);

        // Run the test
        final Long result = redisHelperUnderTest.getExpire("key", TimeUnit.MILLISECONDS);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testGetExpire1_StringRedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.getExpire("key", TimeUnit.MILLISECONDS)).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.getExpire("key", TimeUnit.MILLISECONDS);

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testGetExpire2() {
        // Setup
        when(mockRedisTemplate.getExpire("key")).thenReturn(0L);

        // Run the test
        final Long result = redisHelperUnderTest.getExpire("key");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testGetExpire2_StringRedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.getExpire("key")).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.getExpire("key");

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testRandomKey() {
        // Setup
        when(mockRedisTemplate.randomKey()).thenReturn("result");

        // Run the test
        final String result = redisHelperUnderTest.randomKey();

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testRandomKey_StringRedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.randomKey()).thenReturn(null);

        // Run the test
        final String result = redisHelperUnderTest.randomKey();

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testRename() {
        // Setup
        // Run the test
        redisHelperUnderTest.rename("oldKey", "newKey");

        // Verify the results
        verify(mockRedisTemplate).rename("oldKey", "newKey");
    }

    @Test
    public void testRenameIfAbsent() {
        // Setup
        when(mockRedisTemplate.renameIfAbsent("oldKey", "newKey")).thenReturn(false);

        // Run the test
        final Boolean result = redisHelperUnderTest.renameIfAbsent("oldKey", "newKey");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testRenameIfAbsent_StringRedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.renameIfAbsent("oldKey", "newKey")).thenReturn(null);

        // Run the test
        final Boolean result = redisHelperUnderTest.renameIfAbsent("oldKey", "newKey");

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testRenameIfAbsent_StringRedisTemplateReturnsTrue() {
        // Setup
        when(mockRedisTemplate.renameIfAbsent("oldKey", "newKey")).thenReturn(true);

        // Run the test
        final Boolean result = redisHelperUnderTest.renameIfAbsent("oldKey", "newKey");

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testType() {
        // Setup
        when(mockRedisTemplate.type("key")).thenReturn(DataType.NONE);

        // Run the test
        final DataType result = redisHelperUnderTest.type("key");

        // Verify the results
        assertEquals(DataType.NONE, result);
    }

    @Test
    public void testType_StringRedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.type("key")).thenReturn(null);

        // Run the test
        final DataType result = redisHelperUnderTest.type("key");

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testSet() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        redisHelperUnderTest.set("key", "value");

        // Verify the results
    }

    @Test
    public void testGet() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final String result = redisHelperUnderTest.get("key");

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testGetRange() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final String result = redisHelperUnderTest.getRange("key", 0L, 0L);

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testGetAndSet() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final String result = redisHelperUnderTest.getAndSet("key", "value");

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testGetBit() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final Boolean result = redisHelperUnderTest.getBit("key", 0L);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testMultiGet() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final List<String> result = redisHelperUnderTest.multiGet(Arrays.asList("value"));

        // Verify the results
        assertEquals(Arrays.asList("value"), result);
    }

    @Test
    public void testSetBit() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final boolean result = redisHelperUnderTest.setBit("key", 0L, false);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testSetEx() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        redisHelperUnderTest.setEx("key", "value", 0L, TimeUnit.MILLISECONDS);

        // Verify the results
    }

    @Test
    public void testSetIfAbsent() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final boolean result = redisHelperUnderTest.setIfAbsent("key", "value");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testSetRange() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        redisHelperUnderTest.setRange("key", "value", 0L);

        // Verify the results
    }

    @Test
    public void testSize() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.size("key");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testMultiSet() {
        // Setup
        final Map<String, String> maps = new HashMap<>();
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        redisHelperUnderTest.multiSet(maps);

        // Verify the results
    }

    @Test
    public void testMultiSetIfAbsent() {
        // Setup
        final Map<String, String> maps = new HashMap<>();
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final boolean result = redisHelperUnderTest.multiSetIfAbsent(maps);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testIncrBy() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.incrBy("key", 0L);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testIncrByFloat() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final Double result = redisHelperUnderTest.incrByFloat("key", 0.0);

        // Verify the results
        assertEquals(0.0, result, 0.0001);
    }

    @Test
    public void testAppend() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final Integer result = redisHelperUnderTest.append("key", "value");

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testHGet() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final Object result = redisHelperUnderTest.hGet("key", "field");

        // Verify the results
    }

    @Test
    public void testHGetAll() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final Map<Object, Object> result = redisHelperUnderTest.hGetAll("key");

        // Verify the results
    }

    @Test
    public void testHMultiGet() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final List<Object> result = redisHelperUnderTest.hMultiGet("key", Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testHPut() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        redisHelperUnderTest.hPut("key", "hashKey", "value");

        // Verify the results
    }

    @Test
    public void testHPutAll() {
        // Setup
        final Map<String, String> maps = new HashMap<>();
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        redisHelperUnderTest.hPutAll("key", maps);

        // Verify the results
    }

    @Test
    public void testHPutIfAbsent() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final Boolean result = redisHelperUnderTest.hPutIfAbsent("key", "hashKey", "value");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testHDelete() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.hDelete("key", "fields");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testHExists() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final boolean result = redisHelperUnderTest.hExists("key", "field");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testHIncrBy() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.hIncrBy("key", "field", 0L);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testHIncrByFloat() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final Double result = redisHelperUnderTest.hIncrByFloat("key", "field", 0.0);

        // Verify the results
        assertEquals(0.0, result, 0.0001);
    }

    @Test
    public void testHKeys() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final Set<Object> result = redisHelperUnderTest.hKeys("key");

        // Verify the results
    }

    @Test
    public void testHSize() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.hSize("key");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testHValues() {
        // Setup
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final List<Object> result = redisHelperUnderTest.hValues("key");

        // Verify the results
    }

    @Test
    public void testHScan() {
        // Setup
        final ScanOptions options = null;
        when(mockRedisTemplate.opsForHash()).thenReturn(null);

        // Run the test
        final Cursor<Map.Entry<Object, Object>> result = redisHelperUnderTest.hScan("key", options);

        // Verify the results
    }

    @Test
    public void testLIndex() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final String result = redisHelperUnderTest.lIndex("key", 0L);

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testLRange() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final List<String> result = redisHelperUnderTest.lRange("key", 0L, 0L);

        // Verify the results
        assertEquals(Arrays.asList("value"), result);
    }

    @Test
    public void testLLeftPush1() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.lLeftPush("key", "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLLeftPushAll1() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.lLeftPushAll("key", "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLLeftPushAll2() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.lLeftPushAll("key", Arrays.asList("value"));

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLLeftPushIfPresent() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.lLeftPushIfPresent("key", "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLLeftPush2() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.lLeftPush("key", "pivot", "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLRightPush1() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.lRightPush("key", "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLRightPushAll1() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.lRightPushAll("key", "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLRightPushAll2() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.lRightPushAll("key", Arrays.asList("value"));

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLRightPushIfPresent() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.lRightPushIfPresent("key", "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLRightPush2() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.lRightPush("key", "pivot", "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLSet() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        redisHelperUnderTest.lSet("key", 0L, "value");

        // Verify the results
    }

    @Test
    public void testLLeftPop() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final String result = redisHelperUnderTest.lLeftPop("key");

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testLBLeftPop() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final String result = redisHelperUnderTest.lBLeftPop("key", 0L, TimeUnit.MILLISECONDS);

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testLRightPop() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final String result = redisHelperUnderTest.lRightPop("key");

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testLBRightPop() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final String result = redisHelperUnderTest.lBRightPop("key", 0L, TimeUnit.MILLISECONDS);

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testLRightPopAndLeftPush() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final String result = redisHelperUnderTest.lRightPopAndLeftPush("sourceKey", "destinationKey");

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testLBRightPopAndLeftPush() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final String result = redisHelperUnderTest.lBRightPopAndLeftPush("sourceKey", "destinationKey", 0L,
                TimeUnit.MILLISECONDS);

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testLRemove() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.lRemove("key", 0L, "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testLTrim() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        redisHelperUnderTest.lTrim("key", 0L, 0L);

        // Verify the results
    }

    @Test
    public void testLLen() {
        // Setup
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.lLen("key");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testSAdd() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.sAdd("key", "values");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testSRemove() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.sRemove("key", "values");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testSPop() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final String result = redisHelperUnderTest.sPop("key");

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testSMove() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Boolean result = redisHelperUnderTest.sMove("key", "value", "destKey");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testSSize() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.sSize("key");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testSIsMember() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Boolean result = redisHelperUnderTest.sIsMember("key", "value");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testSIntersect1() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisHelperUnderTest.sIntersect("key", "otherKey");

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testSIntersect2() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisHelperUnderTest.sIntersect("key", Arrays.asList("value"));

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testSIntersectAndStore1() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.sIntersectAndStore("key", "otherKey", "destKey");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testSIntersectAndStore2() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.sIntersectAndStore("key", Arrays.asList("value"), "destKey");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testSUnion1() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisHelperUnderTest.sUnion("key", "otherKeys");

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testSUnion2() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisHelperUnderTest.sUnion("key", Arrays.asList("value"));

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testSUnionAndStore1() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.sUnionAndStore("key", "otherKey", "destKey");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testSUnionAndStore2() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.sUnionAndStore("key", Arrays.asList("value"), "destKey");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testSDifference1() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisHelperUnderTest.sDifference("key", "otherKey");

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testSDifference2() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisHelperUnderTest.sDifference("key", Arrays.asList("value"));

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testSDifference3() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.sDifference("key", "otherKey", "destKey");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testSDifference4() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.sDifference("key", Arrays.asList("value"), "destKey");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testSetMembers() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisHelperUnderTest.setMembers("key");

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testSRandomMember() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final String result = redisHelperUnderTest.sRandomMember("key");

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testSRandomMembers() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final List<String> result = redisHelperUnderTest.sRandomMembers("key", 0L);

        // Verify the results
        assertEquals(Arrays.asList("value"), result);
    }

    @Test
    public void testSDistinctRandomMembers() {
        // Setup
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisHelperUnderTest.sDistinctRandomMembers("key", 0L);

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testSScan() {
        // Setup
        final ScanOptions options = null;
        when(mockRedisTemplate.opsForSet()).thenReturn(null);

        // Run the test
        final Cursor<String> result = redisHelperUnderTest.sScan("key", options);

        // Verify the results
    }

    @Test
    public void testZAdd1() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Boolean result = redisHelperUnderTest.zAdd("key", "value", 0.0);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testZAdd2() {
        // Setup
        final Set<ZSetOperations.TypedTuple<String>> values = new HashSet<>();
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.zAdd("key", values);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZRemove() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.zRemove("key", "values");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZIncrementScore() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Double result = redisHelperUnderTest.zIncrementScore("key", "value", 0.0);

        // Verify the results
        assertEquals(0.0, result, 0.0001);
    }

    @Test
    public void testZRank() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.zRank("key", "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZReverseRank() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.zReverseRank("key", "value");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZRange() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisHelperUnderTest.zRange("key", 0L, 0L);

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testZRangeWithScores() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Set<ZSetOperations.TypedTuple<String>> result = redisHelperUnderTest.zRangeWithScores("key", 0L, 0L);

        // Verify the results
    }

    @Test
    public void testZRangeByScore() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisHelperUnderTest.zRangeByScore("key", 0.0, 0.0);

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testZRangeByScoreWithScores1() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Set<ZSetOperations.TypedTuple<String>> result = redisHelperUnderTest.zRangeByScoreWithScores("key", 0.0,
                0.0);

        // Verify the results
    }

    @Test
    public void testZRangeByScoreWithScores2() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Set<ZSetOperations.TypedTuple<String>> result = redisHelperUnderTest.zRangeByScoreWithScores("key", 0.0,
                0.0, 0L, 0L);

        // Verify the results
    }

    @Test
    public void testZReverseRange() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisHelperUnderTest.zReverseRange("key", 0L, 0L);

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testZReverseRangeWithScores() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Set<ZSetOperations.TypedTuple<String>> result = redisHelperUnderTest.zReverseRangeWithScores("key", 0L,
                0L);

        // Verify the results
    }

    @Test
    public void testZReverseRangeByScore1() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisHelperUnderTest.zReverseRangeByScore("key", 0.0, 0.0);

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testZReverseRangeByScoreWithScores() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Set<ZSetOperations.TypedTuple<String>> result = redisHelperUnderTest.zReverseRangeByScoreWithScores("key",
                0.0, 0.0);

        // Verify the results
    }

    @Test
    public void testZReverseRangeByScore2() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Set<String> result = redisHelperUnderTest.zReverseRangeByScore("key", 0.0, 0.0, 0L, 0L);

        // Verify the results
        assertEquals(new HashSet<>(Arrays.asList("value")), result);
    }

    @Test
    public void testZCount() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.zCount("key", 0.0, 0.0);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZSize() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.zSize("key");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZZCard() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.zZCard("key");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZScore() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Double result = redisHelperUnderTest.zScore("key", "value");

        // Verify the results
        assertEquals(0.0, result, 0.0001);
    }

    @Test
    public void testZRemoveRange() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.zRemoveRange("key", 0L, 0L);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZRemoveRangeByScore() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.zRemoveRangeByScore("key", 0.0, 0.0);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZUnionAndStore1() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.zUnionAndStore("key", "otherKey", "destKey");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZUnionAndStore2() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.zUnionAndStore("key", Arrays.asList("value"), "destKey");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZIntersectAndStore1() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.zIntersectAndStore("key", "otherKey", "destKey");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZIntersectAndStore2() {
        // Setup
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Long result = redisHelperUnderTest.zIntersectAndStore("key", Arrays.asList("value"), "destKey");

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testZScan() {
        // Setup
        final ScanOptions options = null;
        when(mockRedisTemplate.opsForZSet()).thenReturn(null);

        // Run the test
        final Cursor<ZSetOperations.TypedTuple<String>> result = redisHelperUnderTest.zScan("key", options);

        // Verify the results
    }
}
