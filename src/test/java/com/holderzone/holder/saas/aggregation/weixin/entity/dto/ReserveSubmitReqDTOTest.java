package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import com.holderzone.saas.store.dto.queue.TableDTO;
import org.junit.Before;
import org.junit.Test;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collection;

import static org.assertj.core.api.Assertions.assertThat;

public class ReserveSubmitReqDTOTest {

    private ReserveSubmitReqDTO reserveSubmitReqDTOUnderTest;

    @Before
    public void setUp() throws Exception {
        reserveSubmitReqDTOUnderTest = new ReserveSubmitReqDTO("storeGuid", 0, "state", "name", "phone",
                Arrays.asList(TableDTO.builder().build()), (byte) 0b0, "remark", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }

    @Test
    public void testStoreGuidGetterAndSetter() {
        final String storeGuid = "storeGuid";
        reserveSubmitReqDTOUnderTest.setStoreGuid(storeGuid);
        assertThat(reserveSubmitReqDTOUnderTest.getStoreGuid()).isEqualTo(storeGuid);
    }

    @Test
    public void testNumberGetterAndSetter() {
        final Integer number = 0;
        reserveSubmitReqDTOUnderTest.setNumber(number);
        assertThat(reserveSubmitReqDTOUnderTest.getNumber()).isEqualTo(number);
    }

    @Test
    public void testStateGetterAndSetter() {
        final String state = "state";
        reserveSubmitReqDTOUnderTest.setState(state);
        assertThat(reserveSubmitReqDTOUnderTest.getState()).isEqualTo(state);
    }

    @Test
    public void testNameGetterAndSetter() {
        final String name = "name";
        reserveSubmitReqDTOUnderTest.setName(name);
        assertThat(reserveSubmitReqDTOUnderTest.getName()).isEqualTo(name);
    }

    @Test
    public void testPhoneGetterAndSetter() {
        final String phone = "phone";
        reserveSubmitReqDTOUnderTest.setPhone(phone);
        assertThat(reserveSubmitReqDTOUnderTest.getPhone()).isEqualTo(phone);
    }

    @Test
    public void testTablesGetterAndSetter() {
        final Collection<TableDTO> tables = Arrays.asList(TableDTO.builder().build());
        reserveSubmitReqDTOUnderTest.setTables(tables);
        assertThat(reserveSubmitReqDTOUnderTest.getTables()).isEqualTo(tables);
    }

    @Test
    public void testGenderGetterAndSetter() {
        final Byte gender = (byte) 0b0;
        reserveSubmitReqDTOUnderTest.setGender(gender);
        assertThat(reserveSubmitReqDTOUnderTest.getGender()).isEqualTo(gender);
    }

    @Test
    public void testRemarkGetterAndSetter() {
        final String remark = "remark";
        reserveSubmitReqDTOUnderTest.setRemark(remark);
        assertThat(reserveSubmitReqDTOUnderTest.getRemark()).isEqualTo(remark);
    }

    @Test
    public void testReserveStartTimeGetterAndSetter() {
        final LocalDateTime reserveStartTime = LocalDateTime.of(2020, 1, 1, 0, 0, 0);
        reserveSubmitReqDTOUnderTest.setReserveStartTime(reserveStartTime);
        assertThat(reserveSubmitReqDTOUnderTest.getReserveStartTime()).isEqualTo(reserveStartTime);
    }

    @Test
    public void testReservesEndTimeGetterAndSetter() {
        final LocalDateTime reservesEndTime = LocalDateTime.of(2020, 1, 1, 0, 0, 0);
        reserveSubmitReqDTOUnderTest.setReservesEndTime(reservesEndTime);
        assertThat(reserveSubmitReqDTOUnderTest.getReservesEndTime()).isEqualTo(reservesEndTime);
    }

    @Test
    public void testEquals() throws Exception {
        assertThat(reserveSubmitReqDTOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() throws Exception {
        assertThat(reserveSubmitReqDTOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() throws Exception {
        assertThat(reserveSubmitReqDTOUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() throws Exception {
        assertThat(reserveSubmitReqDTOUnderTest.toString()).isEqualTo("result");
    }
}
