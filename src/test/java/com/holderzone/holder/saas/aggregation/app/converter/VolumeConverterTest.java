package com.holderzone.holder.saas.aggregation.app.converter;

import com.holderzone.holder.saas.member.terminal.dto.volume.ResponseCheckSingleVolumeRedeem;
import com.holderzone.holder.saas.member.terminal.dto.volume.ResponseCheckVolumeRedeem;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.GregorianCalendar;

public class VolumeConverterTest {

    @Test
    public void testResponseCheckVolumeRedeemConverter() {
        // Setup
        final ResponseCheckVolumeRedeem responseCheckVolumeRedeem = new ResponseCheckVolumeRedeem();
        responseCheckVolumeRedeem.setBelongsMemberInfoGuid("belongsMemberInfoGuid");
        responseCheckVolumeRedeem.setVolumeName("volumeName");
        responseCheckVolumeRedeem.setVolumeMoney(new BigDecimal("0.00"));
        responseCheckVolumeRedeem.setCanNum(0);
        responseCheckVolumeRedeem.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        responseCheckVolumeRedeem.setVolumeCode("volumeCode");
        responseCheckVolumeRedeem.setStartValidityPeriod(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        responseCheckVolumeRedeem.setEndValidityPeriod(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        responseCheckVolumeRedeem.setUseThreshold(0);
        responseCheckVolumeRedeem.setUseThresholdFull(new BigDecimal("0.00"));
        responseCheckVolumeRedeem.setRuleTip("ruleTip");
        responseCheckVolumeRedeem.setVolumeType(0);

        final ResponseCheckSingleVolumeRedeem obj = new ResponseCheckSingleVolumeRedeem();
        obj.setBelongsMemberInfoGuid("belongsMemberInfoGuid");
        obj.setVolumeName("volumeName");
        obj.setVolumeMoney(new BigDecimal("0.00"));
        obj.setCanNum(0);
        obj.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        obj.setVolumeCode("volumeCode");
        obj.setStartValidityPeriod(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        obj.setEndValidityPeriod(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        obj.setUseThreshold(0);
        obj.setUseThresholdFull(new BigDecimal("0.00"));
        obj.setRuleTip("ruleTip");
        obj.setVolumeType(0);

        // Run the test
        VolumeConverter.responseCheckVolumeRedeemConverter(responseCheckVolumeRedeem, obj);

        // Verify the results
    }
}
