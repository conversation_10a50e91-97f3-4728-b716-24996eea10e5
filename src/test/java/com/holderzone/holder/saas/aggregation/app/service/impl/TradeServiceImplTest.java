package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.holder.saas.aggregation.app.service.feign.MessageClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.msg.MsgClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.organization.OrgFeignClient;
import com.holderzone.holder.saas.aggregation.app.service.feign.pay.AggPayClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInBillClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.OrderItemClientService;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillAggPayReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.BillAggPayRespDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.organization.PadOrderTypeReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.dto.pay.SaasPollingDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.trade.req.PadPayInfoReqDTO;
import com.holderzone.saas.store.dto.trade.resp.PadOrderRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TradeServiceImplTest {

    @Mock
    private AggPayClientService mockAggPayClientService;
    @Mock
    private DineInBillClientService mockDineInBillClientService;
    @Mock
    private OrderItemClientService mockOrderItemClientService;
    @Mock
    private MessageClientService mockMessageClientService;
    @Mock
    private MsgClientService mockMsgClientService;
    @Mock
    private OrgFeignClient mockOrgFeignClient;

    private TradeServiceImpl tradeServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        tradeServiceImplUnderTest = new TradeServiceImpl(mockAggPayClientService, mockDineInBillClientService,
                mockOrderItemClientService, mockMessageClientService, mockMsgClientService, mockOrgFeignClient);
    }

    @Test
    public void testPadPay() throws Exception {
        // Setup
        // Configure OrderItemClientService.findByOrderGuid(...).
        final OrderDTO orderDTO = OrderDTO.builder()
                .actuallyPayFee(new BigDecimal("0.00"))
                .state(0)
                .upperState(0)
                .mainOrderGuid("orderGuid")
                .build();
        when(mockOrderItemClientService.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        // Configure OrderItemClientService.getPadPayInfo(...).
        final PadPayInfoReqDTO padPayInfoReqDTO = new PadPayInfoReqDTO();
        padPayInfoReqDTO.setOrderTotalFee(new BigDecimal("0.00"));
        padPayInfoReqDTO.setMemberDiscountFee(new BigDecimal("0.00"));
        padPayInfoReqDTO.setVolumeFee(new BigDecimal("0.00"));
        padPayInfoReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        padPayInfoReqDTO.setUseIntegral(0);
        padPayInfoReqDTO.setIntegralDeductedAmount(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setIntegral(0);
        padPayInfoReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        padPayInfoReqDTO.setAppendFee(new BigDecimal("0.00"));
        padPayInfoReqDTO.setNickName("nickName");
        padPayInfoReqDTO.setMemberInfoGuid("memberInfoGuid");
        padPayInfoReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        padPayInfoReqDTO.setDeviceType(0);
        padPayInfoReqDTO.setOperSubjectGuid("operSubjectGuid");
        padPayInfoReqDTO.setStoreGuid("storeGuid");
        padPayInfoReqDTO.setStoreName("name");
        padPayInfoReqDTO.setEnterpriseGuid("enterpriseGuid");
        padPayInfoReqDTO.setVersion(0);
        padPayInfoReqDTO.setDeviceId("deviceId");
        when(mockOrderItemClientService.getPadPayInfo("orderGuid")).thenReturn(padPayInfoReqDTO);

        // Configure DineInBillClientService.aggPay(...).
        final BillAggPayRespDTO billAggPayRespDTO = new BillAggPayRespDTO();
        billAggPayRespDTO.setPayGuid("payGuid");
        billAggPayRespDTO.setJhOrderGuid("jhOrderGuid");
        billAggPayRespDTO.setResult(false);
        billAggPayRespDTO.setEstimate(false);
        billAggPayRespDTO.setEstimateInfo("estimateInfo");
        final BillAggPayReqDTO billPayReqDTO = new BillAggPayReqDTO();
        billPayReqDTO.setDeviceType(0);
        billPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        billPayReqDTO.setStoreGuid("storeGuid");
        billPayReqDTO.setStoreName("name");
        billPayReqDTO.setUserGuid("userGuid");
        billPayReqDTO.setUserName("userName");
        billPayReqDTO.setOperSubjectGuid("operSubjectGuid");
        billPayReqDTO.setOrderGuid("orderGuid");
        billPayReqDTO.setAmount(new BigDecimal("0.00"));
        billPayReqDTO.setLast(false);
        billPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        billPayReqDTO.setAppendFee(new BigDecimal("0.00"));
        billPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setUseIntegral(0);
        billPayReqDTO.setMemberIntegralStore(0);
        billPayReqDTO.setIntegralDiscountMoney(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setIntegral(0);
        billPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        billPayReqDTO.setVersion(0);
        billPayReqDTO.setPayPowerId("payPowerId");
        billPayReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billPayReqDTO.setMemberInfoGuid("memberInfoGuid");
        billPayReqDTO.setActiveScan(0);
        when(mockDineInBillClientService.aggPay(billPayReqDTO)).thenReturn(billAggPayRespDTO);

        // Configure AggPayClientService.query(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setMsg("msg");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setCodeUrl("codeUrl");
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setStoreName("name");
        saasPollingDTO.setUserGuid("userGuid");
        saasPollingDTO.setUserName("userName");
        saasPollingDTO.setOperSubjectGuid("operSubjectGuid");
        saasPollingDTO.setOrderGuid("orderGuid");
        saasPollingDTO.setPayGuid("payGuid");
        when(mockAggPayClientService.query(saasPollingDTO)).thenReturn(aggPayPollingRespDTO);

        // Run the test
        final String result = tradeServiceImplUnderTest.padPay("orderGuid", "payPowerId");

        // Verify the results
        assertEquals("codeUrl", result);
        verify(mockMessageClientService).msg(BusinessMessageDTO.builder()
                .subject("name")
                .content("payGuid")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("name")
                .messageTypeStr("messageTypeStr")
                .build());
    }

    @Test
    public void testPadPay_AggPayClientServiceReturnsError() throws Exception {
        // Setup
        // Configure OrderItemClientService.findByOrderGuid(...).
        final OrderDTO orderDTO = OrderDTO.builder()
                .actuallyPayFee(new BigDecimal("0.00"))
                .state(0)
                .upperState(0)
                .mainOrderGuid("orderGuid")
                .build();
        when(mockOrderItemClientService.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        // Configure OrderItemClientService.getPadPayInfo(...).
        final PadPayInfoReqDTO padPayInfoReqDTO = new PadPayInfoReqDTO();
        padPayInfoReqDTO.setOrderTotalFee(new BigDecimal("0.00"));
        padPayInfoReqDTO.setMemberDiscountFee(new BigDecimal("0.00"));
        padPayInfoReqDTO.setVolumeFee(new BigDecimal("0.00"));
        padPayInfoReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        padPayInfoReqDTO.setUseIntegral(0);
        padPayInfoReqDTO.setIntegralDeductedAmount(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setIntegral(0);
        padPayInfoReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        padPayInfoReqDTO.setAppendFee(new BigDecimal("0.00"));
        padPayInfoReqDTO.setNickName("nickName");
        padPayInfoReqDTO.setMemberInfoGuid("memberInfoGuid");
        padPayInfoReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        padPayInfoReqDTO.setDeviceType(0);
        padPayInfoReqDTO.setOperSubjectGuid("operSubjectGuid");
        padPayInfoReqDTO.setStoreGuid("storeGuid");
        padPayInfoReqDTO.setStoreName("name");
        padPayInfoReqDTO.setEnterpriseGuid("enterpriseGuid");
        padPayInfoReqDTO.setVersion(0);
        padPayInfoReqDTO.setDeviceId("deviceId");
        when(mockOrderItemClientService.getPadPayInfo("orderGuid")).thenReturn(padPayInfoReqDTO);

        // Configure DineInBillClientService.aggPay(...).
        final BillAggPayRespDTO billAggPayRespDTO = new BillAggPayRespDTO();
        billAggPayRespDTO.setPayGuid("payGuid");
        billAggPayRespDTO.setJhOrderGuid("jhOrderGuid");
        billAggPayRespDTO.setResult(false);
        billAggPayRespDTO.setEstimate(false);
        billAggPayRespDTO.setEstimateInfo("estimateInfo");
        final BillAggPayReqDTO billPayReqDTO = new BillAggPayReqDTO();
        billPayReqDTO.setDeviceType(0);
        billPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        billPayReqDTO.setStoreGuid("storeGuid");
        billPayReqDTO.setStoreName("name");
        billPayReqDTO.setUserGuid("userGuid");
        billPayReqDTO.setUserName("userName");
        billPayReqDTO.setOperSubjectGuid("operSubjectGuid");
        billPayReqDTO.setOrderGuid("orderGuid");
        billPayReqDTO.setAmount(new BigDecimal("0.00"));
        billPayReqDTO.setLast(false);
        billPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        billPayReqDTO.setAppendFee(new BigDecimal("0.00"));
        billPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setUseIntegral(0);
        billPayReqDTO.setMemberIntegralStore(0);
        billPayReqDTO.setIntegralDiscountMoney(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setIntegral(0);
        billPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        billPayReqDTO.setVersion(0);
        billPayReqDTO.setPayPowerId("payPowerId");
        billPayReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billPayReqDTO.setMemberInfoGuid("memberInfoGuid");
        billPayReqDTO.setActiveScan(0);
        when(mockDineInBillClientService.aggPay(billPayReqDTO)).thenReturn(billAggPayRespDTO);

        // Configure AggPayClientService.query(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = AggPayPollingRespDTO.errorResp("code", "msg");
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setStoreName("name");
        saasPollingDTO.setUserGuid("userGuid");
        saasPollingDTO.setUserName("userName");
        saasPollingDTO.setOperSubjectGuid("operSubjectGuid");
        saasPollingDTO.setOrderGuid("orderGuid");
        saasPollingDTO.setPayGuid("payGuid");
        when(mockAggPayClientService.query(saasPollingDTO)).thenReturn(aggPayPollingRespDTO);

        // Run the test
        final String result = tradeServiceImplUnderTest.padPay("orderGuid", "payPowerId");

        // Verify the results
        assertEquals("codeUrl", result);
        verify(mockMessageClientService).msg(BusinessMessageDTO.builder()
                .subject("name")
                .content("payGuid")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("name")
                .messageTypeStr("messageTypeStr")
                .build());
    }

    @Test
    public void testCyclicResult() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setStoreName("name");
        saasPollingDTO.setUserGuid("userGuid");
        saasPollingDTO.setUserName("userName");
        saasPollingDTO.setOperSubjectGuid("operSubjectGuid");
        saasPollingDTO.setOrderGuid("orderGuid");
        saasPollingDTO.setPayGuid("payGuid");

        // Configure AggPayClientService.query(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setMsg("msg");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setCodeUrl("codeUrl");
        final SaasPollingDTO saasPollingDTO1 = new SaasPollingDTO();
        saasPollingDTO1.setDeviceType(0);
        saasPollingDTO1.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO1.setStoreGuid("storeGuid");
        saasPollingDTO1.setStoreName("name");
        saasPollingDTO1.setUserGuid("userGuid");
        saasPollingDTO1.setUserName("userName");
        saasPollingDTO1.setOperSubjectGuid("operSubjectGuid");
        saasPollingDTO1.setOrderGuid("orderGuid");
        saasPollingDTO1.setPayGuid("payGuid");
        when(mockAggPayClientService.query(saasPollingDTO1)).thenReturn(aggPayPollingRespDTO);

        // Run the test
        final String result = tradeServiceImplUnderTest.cyclicResult(saasPollingDTO);

        // Verify the results
        assertEquals("codeUrl", result);
    }

    @Test
    public void testCyclicResult_AggPayClientServiceReturnsError() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setStoreName("name");
        saasPollingDTO.setUserGuid("userGuid");
        saasPollingDTO.setUserName("userName");
        saasPollingDTO.setOperSubjectGuid("operSubjectGuid");
        saasPollingDTO.setOrderGuid("orderGuid");
        saasPollingDTO.setPayGuid("payGuid");

        // Configure AggPayClientService.query(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = AggPayPollingRespDTO.errorResp("code", "msg");
        final SaasPollingDTO saasPollingDTO1 = new SaasPollingDTO();
        saasPollingDTO1.setDeviceType(0);
        saasPollingDTO1.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO1.setStoreGuid("storeGuid");
        saasPollingDTO1.setStoreName("name");
        saasPollingDTO1.setUserGuid("userGuid");
        saasPollingDTO1.setUserName("userName");
        saasPollingDTO1.setOperSubjectGuid("operSubjectGuid");
        saasPollingDTO1.setOrderGuid("orderGuid");
        saasPollingDTO1.setPayGuid("payGuid");
        when(mockAggPayClientService.query(saasPollingDTO1)).thenReturn(aggPayPollingRespDTO);

        // Run the test
        final String result = tradeServiceImplUnderTest.cyclicResult(saasPollingDTO);

        // Verify the results
        assertEquals("codeUrl", result);
    }

    @Test
    public void testSendMsgWhenCombine() {
        // Setup
        // Configure OrderItemClientService.listPadOrderInfoOnCombine(...).
        final PadOrderRespDTO padOrderRespDTO = new PadOrderRespDTO();
        padOrderRespDTO.setGuid(0L);
        padOrderRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO.setStoreGuid("storeGuid");
        final List<PadOrderRespDTO> padOrderRespDTOS = Arrays.asList(padOrderRespDTO);
        when(mockOrderItemClientService.listPadOrderInfoOnCombine("orderGuid")).thenReturn(padOrderRespDTOS);

        // Configure OrderItemClientService.findByOrderGuid(...).
        final OrderDTO orderDTO = OrderDTO.builder()
                .actuallyPayFee(new BigDecimal("0.00"))
                .state(0)
                .upperState(0)
                .mainOrderGuid("orderGuid")
                .build();
        when(mockOrderItemClientService.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        // Configure OrderItemClientService.listOrderByCombineOrderGuid(...).
        final List<OrderDTO> orderDTOS = Arrays.asList(OrderDTO.builder()
                .actuallyPayFee(new BigDecimal("0.00"))
                .state(0)
                .upperState(0)
                .mainOrderGuid("orderGuid")
                .build());
        when(mockOrderItemClientService.listOrderByCombineOrderGuid("orderGuid")).thenReturn(orderDTOS);

        // Configure OrgFeignClient.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("2a5b43c0-cf12-4448-9024-a54db5200af8");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrgFeignClient.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure OrgFeignClient.listDeviceByStoreTable(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("storeName");
        storeDeviceDTO.setDeviceNo("deviceNo");
        final List<StoreDeviceDTO> storeDeviceDTOS = Arrays.asList(storeDeviceDTO);
        when(mockOrgFeignClient.listDeviceByStoreTable(any(PadOrderTypeReqDTO.class))).thenReturn(storeDeviceDTOS);

        // Run the test
        final Boolean result = tradeServiceImplUnderTest.sendMsgWhenCombine("orderGuid");

        // Verify the results
        assertFalse(result);
        verify(mockMessageClientService).msg(BusinessMessageDTO.builder()
                .subject("name")
                .content("payGuid")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("name")
                .messageTypeStr("messageTypeStr")
                .build());
    }

    @Test
    public void testSendMsgWhenCombine_OrderItemClientServiceListPadOrderInfoOnCombineReturnsNoItems() {
        // Setup
        when(mockOrderItemClientService.listPadOrderInfoOnCombine("orderGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final Boolean result = tradeServiceImplUnderTest.sendMsgWhenCombine("orderGuid");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testSendMsgWhenCombine_OrderItemClientServiceListOrderByCombineOrderGuidReturnsNoItems() {
        // Setup
        // Configure OrderItemClientService.listPadOrderInfoOnCombine(...).
        final PadOrderRespDTO padOrderRespDTO = new PadOrderRespDTO();
        padOrderRespDTO.setGuid(0L);
        padOrderRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO.setStoreGuid("storeGuid");
        final List<PadOrderRespDTO> padOrderRespDTOS = Arrays.asList(padOrderRespDTO);
        when(mockOrderItemClientService.listPadOrderInfoOnCombine("orderGuid")).thenReturn(padOrderRespDTOS);

        // Configure OrderItemClientService.findByOrderGuid(...).
        final OrderDTO orderDTO = OrderDTO.builder()
                .actuallyPayFee(new BigDecimal("0.00"))
                .state(0)
                .upperState(0)
                .mainOrderGuid("orderGuid")
                .build();
        when(mockOrderItemClientService.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        when(mockOrderItemClientService.listOrderByCombineOrderGuid("orderGuid")).thenReturn(Collections.emptyList());

        // Configure OrgFeignClient.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("2a5b43c0-cf12-4448-9024-a54db5200af8");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrgFeignClient.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure OrgFeignClient.listDeviceByStoreTable(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setStoreName("storeName");
        storeDeviceDTO.setDeviceNo("deviceNo");
        final List<StoreDeviceDTO> storeDeviceDTOS = Arrays.asList(storeDeviceDTO);
        when(mockOrgFeignClient.listDeviceByStoreTable(any(PadOrderTypeReqDTO.class))).thenReturn(storeDeviceDTOS);

        // Run the test
        final Boolean result = tradeServiceImplUnderTest.sendMsgWhenCombine("orderGuid");

        // Verify the results
        assertFalse(result);
        verify(mockMessageClientService).msg(BusinessMessageDTO.builder()
                .subject("name")
                .content("payGuid")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid("storeGuid")
                .storeName("name")
                .messageTypeStr("messageTypeStr")
                .build());
    }

    @Test
    public void testSendMsgWhenCombine_OrgFeignClientListDeviceByStoreTableReturnsNoItems() {
        // Setup
        // Configure OrderItemClientService.listPadOrderInfoOnCombine(...).
        final PadOrderRespDTO padOrderRespDTO = new PadOrderRespDTO();
        padOrderRespDTO.setGuid(0L);
        padOrderRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        padOrderRespDTO.setDiningTableGuid("diningTableGuid");
        padOrderRespDTO.setStoreGuid("storeGuid");
        final List<PadOrderRespDTO> padOrderRespDTOS = Arrays.asList(padOrderRespDTO);
        when(mockOrderItemClientService.listPadOrderInfoOnCombine("orderGuid")).thenReturn(padOrderRespDTOS);

        // Configure OrderItemClientService.findByOrderGuid(...).
        final OrderDTO orderDTO = OrderDTO.builder()
                .actuallyPayFee(new BigDecimal("0.00"))
                .state(0)
                .upperState(0)
                .mainOrderGuid("orderGuid")
                .build();
        when(mockOrderItemClientService.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        // Configure OrderItemClientService.listOrderByCombineOrderGuid(...).
        final List<OrderDTO> orderDTOS = Arrays.asList(OrderDTO.builder()
                .actuallyPayFee(new BigDecimal("0.00"))
                .state(0)
                .upperState(0)
                .mainOrderGuid("orderGuid")
                .build());
        when(mockOrderItemClientService.listOrderByCombineOrderGuid("orderGuid")).thenReturn(orderDTOS);

        // Configure OrgFeignClient.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("2a5b43c0-cf12-4448-9024-a54db5200af8");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrgFeignClient.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        when(mockOrgFeignClient.listDeviceByStoreTable(any(PadOrderTypeReqDTO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Boolean result = tradeServiceImplUnderTest.sendMsgWhenCombine("orderGuid");

        // Verify the results
        assertFalse(result);
    }
}
