package com.holderzone.holder.saas.aggregation.weixin;

import com.holderzone.holder.saas.aggregation.weixin.util.MockHttpUtil;
import com.holderzone.holder.saas.aggregation.weixin.utils.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.context.WebApplicationContext;


/**
 * 微信订单管理
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
@SpringBootTest(classes = HolderSaasAggregationWeixinApplication.class)
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class WxStoreTradeControllerTest {

    @Autowired
    private ApplicationContext ap;

    @Autowired
    private WebApplicationContext webApplicationContext;
    private MockMvc mockMvc;

    private static final String WX_STORE_TRADE_REQUEST_MAPPING = "/wx_store_trade_order";

    @Before
    public void setupMockMvc() {
        SpringContextUtil.getInstance().setCfgContext((ConfigurableApplicationContext) ap);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.webApplicationContext).build();
    }

    /**
     * 快餐点餐人数，未下单修改
     */
    @Test
    public void testUpdateFastGuestCount() {
        int count = 5;
        String content = MockHttpUtil.put(WX_STORE_TRADE_REQUEST_MAPPING + "/update_fast_guest_count/" + count, new LinkedMultiValueMap<>(), mockMvc);
        log.info("快餐点餐人数返回参数:{}", content);
    }

}
