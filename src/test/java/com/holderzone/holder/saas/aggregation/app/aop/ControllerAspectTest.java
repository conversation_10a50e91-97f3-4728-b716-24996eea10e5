package com.holderzone.holder.saas.aggregation.app.aop;

import org.aspectj.lang.JoinPoint;
import org.junit.Before;
import org.junit.Test;

public class ControllerAspectTest {

    private ControllerAspect controllerAspectUnderTest;

    @Before
    public void setUp() throws Exception {
        controllerAspectUnderTest = new ControllerAspect();
    }

    @Test
    public void testPointCut() {
        controllerAspectUnderTest.pointCut();
    }

    @Test
    public void testDoBefore() {
        // Setup
        final JoinPoint joinPoint = null;

        // Run the test
        controllerAspectUnderTest.doBefore(joinPoint);

        // Verify the results
    }
}
