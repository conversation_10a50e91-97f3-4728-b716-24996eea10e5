package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.app.entity.auth.*;
import com.holderzone.holder.saas.aggregation.app.service.BusinessDailyService;
import com.holderzone.holder.saas.aggregation.app.service.feign.PrintClientService;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponsePayWayDetail;
import com.holderzone.saas.store.dto.order.request.daily.DailyPrintReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.MemberConsumeRespDTO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.enums.print.PrintSourceEnum;
import com.holderzone.saas.store.enums.trade.DiningTypeEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BusinessDailyPrintServiceImplTest {

    @Mock
    private BusinessDailyService mockBusinessDailyService;
    @Mock
    private PrintClientService mockPrintClientService;

    private BusinessDailyPrintServiceImpl businessDailyPrintServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        businessDailyPrintServiceImplUnderTest = new BusinessDailyPrintServiceImpl(mockBusinessDailyService,
                mockPrintClientService);
    }

    @Test
    public void testPrint() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setBeginTime("2025-01-01");
        request.setEndTime("2025-01-10");
        request.setIsPrint(0);
        request.setType(1);

        // Configure BusinessDailyService.overviewSale(...).
        final OverviewSaleDTO overviewSaleDTO = new OverviewSaleDTO();
        overviewSaleDTO.setOrderCount("10");
        overviewSaleDTO.setGuestCount("10");
        overviewSaleDTO.setConsumerAmount("10");
        overviewSaleDTO.setGatherAmount("10");
        final AmountItemDTO amountItemDTO = new AmountItemDTO();
        amountItemDTO.setName("name");
        amountItemDTO.setAmount("10");
        amountItemDTO.setExcessAmount("10");
        amountItemDTO.setEstimatedAmount("10");
        overviewSaleDTO.setGatherItems(Arrays.asList(amountItemDTO));
        overviewSaleDTO.setDiscountAmount("10");
        final DiscountAmountItemDTO discountAmountItemDTO = new DiscountAmountItemDTO();
        discountAmountItemDTO.setName("name");
        discountAmountItemDTO.setAmount("10");
        overviewSaleDTO.setDiscountItems(Arrays.asList(discountAmountItemDTO));
        overviewSaleDTO.setCheckoutStaffs(Arrays.asList("value"));
        overviewSaleDTO.setEstimatedAmount("10");
        when(mockBusinessDailyService.overviewSale(request)).thenReturn(overviewSaleDTO);

        // Configure BusinessDailyService.gatherSale(...).
        final GatherSaleDTO gatherSaleDTO = new GatherSaleDTO();
        gatherSaleDTO.setGatherName("gatherName");
        gatherSaleDTO.setConsumerAmount("10");
        gatherSaleDTO.setExcessAmount("10");
        gatherSaleDTO.setPrepaidAmount("10");
        gatherSaleDTO.setReserveAmount("10");
        gatherSaleDTO.setIsTotal(0);
        final List<GatherSaleDTO> gatherSaleDTOS = Arrays.asList(gatherSaleDTO);
        when(mockBusinessDailyService.gatherSale(request)).thenReturn(gatherSaleDTOS);

        // Configure BusinessDailyService.memberConsume(...).
        final MemberConsumeRespDTO memberConsumeRespDTO = new MemberConsumeRespDTO();
        memberConsumeRespDTO.setConsumerCount(0);
        memberConsumeRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidCount(0);
        memberConsumeRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidGiveAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidTakeInAmount(new BigDecimal("0.00"));
        final com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO amountItemDTO1 = new com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO();
        amountItemDTO1.setName("name");
        amountItemDTO1.setAmount(new BigDecimal("0.00"));
        amountItemDTO1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidItems(Arrays.asList(amountItemDTO1));
        when(mockBusinessDailyService.memberConsume(request)).thenReturn(memberConsumeRespDTO);

        // Configure BusinessDailyService.diningTypeSale(...).
        final DiningTypeSaleDTO diningTypeSaleDTO = new DiningTypeSaleDTO();
        diningTypeSaleDTO.setTypeCode("typeCode");
        diningTypeSaleDTO.setTypeName("platformName");
        diningTypeSaleDTO.setOrderCount("10");
        diningTypeSaleDTO.setGuestCount("10");
        diningTypeSaleDTO.setAmount("10");
        diningTypeSaleDTO.setOrderPrice("10");
        diningTypeSaleDTO.setGuestPrice("10");
        diningTypeSaleDTO.setSubDiningTypes(Arrays.asList(new DiningTypeSaleDTO()));
        diningTypeSaleDTO.setIsTotal(0);
        final List<DiningTypeSaleDTO> diningTypeSaleDTOS = Arrays.asList(diningTypeSaleDTO);
        when(mockBusinessDailyService.diningTypeSale(request)).thenReturn(diningTypeSaleDTOS);

        // Configure BusinessDailyService.classifySale(...).
        final CategorySaleDTO categorySaleDTO = new CategorySaleDTO();
        categorySaleDTO.setName("name");
        categorySaleDTO.setQuantum("10");
        categorySaleDTO.setAmount("10");
        categorySaleDTO.setDiscountAmount("10");
        categorySaleDTO.setIsTotal(0);
        final List<CategorySaleDTO> categorySaleDTOS = Arrays.asList(categorySaleDTO);
        when(mockBusinessDailyService.classifySale(request)).thenReturn(categorySaleDTOS);

        // Configure BusinessDailyService.goodsSale(...).
        final GoodsSaleDTO goodsSaleDTO = new GoodsSaleDTO();
        goodsSaleDTO.setName("name");
        goodsSaleDTO.setSkuName("skuName");
        goodsSaleDTO.setQuantum("10");
        goodsSaleDTO.setAmount("10");
        goodsSaleDTO.setItemType(0);
        goodsSaleDTO.setIsTotal(0);
        goodsSaleDTO.setSubs(Arrays.asList(new GoodsSaleDTO()));
        final List<GoodsSaleDTO> goodsSaleDTOS = Arrays.asList(goodsSaleDTO);
        when(mockBusinessDailyService.goodsSale(request)).thenReturn(goodsSaleDTOS);

        // Configure BusinessDailyService.attrSale(...).
        final PropStatsSaleDTO propStatsSaleDTO = new PropStatsSaleDTO();
        final PropStatsSaleDTO.PropGroup propGroup = new PropStatsSaleDTO.PropGroup();
        propGroup.setName("name");
        final PropStatsSaleDTO.PropItem propItem = new PropStatsSaleDTO.PropItem();
        propItem.setName("name");
        propItem.setQuantity("10");
        propItem.setMoney("10");
        propGroup.setPropList(Arrays.asList(propItem));
        propStatsSaleDTO.setPropGroupList(Arrays.asList(propGroup));
        when(mockBusinessDailyService.attrSale(request)).thenReturn(propStatsSaleDTO);

        // Configure BusinessDailyService.returnVegetablesSale(...).
        final ReturnSaleDTO returnSaleDTO = new ReturnSaleDTO();
        returnSaleDTO.setName("name");
        returnSaleDTO.setSkuName("skuName");
        returnSaleDTO.setQuantum("10");
        returnSaleDTO.setAmount("10");
        returnSaleDTO.setItemType(0);
        returnSaleDTO.setIsTotal(0);
        returnSaleDTO.setSubs(Arrays.asList(new ReturnSaleDTO()));
        final List<ReturnSaleDTO> returnSaleDTOS = Arrays.asList(returnSaleDTO);
        when(mockBusinessDailyService.returnVegetablesSale(request)).thenReturn(returnSaleDTOS);

        // Configure BusinessDailyService.dishGivingSale(...).
        final GiftSaleDTO giftSaleDTO = new GiftSaleDTO();
        giftSaleDTO.setName("name");
        giftSaleDTO.setSkuName("skuName");
        giftSaleDTO.setQuantum("10");
        giftSaleDTO.setAmount("10");
        giftSaleDTO.setItemType(0);
        giftSaleDTO.setIsTotal(0);
        giftSaleDTO.setSubs(Arrays.asList(new GiftSaleDTO()));
        final List<GiftSaleDTO> giftSaleDTOS = Arrays.asList(giftSaleDTO);
        when(mockBusinessDailyService.dishGivingSale(request)).thenReturn(giftSaleDTOS);

        // Configure BusinessDailyService.memberRechargeSale(...).
        final MemberRechargeSaleDTO memberRechargeSaleDTO = new MemberRechargeSaleDTO();
        memberRechargeSaleDTO.setRechargeNum("10");
        memberRechargeSaleDTO.setRechargeMemberNum("10");
        memberRechargeSaleDTO.setRechargeAmount("10");
        memberRechargeSaleDTO.setPresentAmount("10");
        memberRechargeSaleDTO.setIncomeAmount("10");
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        responsePayWayDetail.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail.setPayName("payName");
        responsePayWayDetail.setExcessAmount(new BigDecimal("0.00"));
        memberRechargeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail));
        when(mockBusinessDailyService.memberRechargeSale(request)).thenReturn(memberRechargeSaleDTO);

        // Configure BusinessDailyService.memberConsumeSale(...).
        final MemberConsumeSaleDTO memberConsumeSaleDTO = new MemberConsumeSaleDTO();
        memberConsumeSaleDTO.setConsumptionNum("10");
        memberConsumeSaleDTO.setConsumptionMemberNum("10");
        memberConsumeSaleDTO.setConsumptionAmount("10");
        final ResponsePayWayDetail responsePayWayDetail1 = new ResponsePayWayDetail();
        responsePayWayDetail1.setPayWay(0);
        responsePayWayDetail1.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail1.setPayName("payName");
        responsePayWayDetail1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail1));
        when(mockBusinessDailyService.memberConsumeSale(request)).thenReturn(memberConsumeSaleDTO);

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result<String> result = businessDailyPrintServiceImplUnderTest.print(request);

        // Verify the results
    }


    @Test
    public void testPrint2() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setBeginTime("2025-01-01");
        request.setEndTime("2025-01-10");
        request.setIsPrint(0);
        request.setType(2);

        // Configure BusinessDailyService.overviewSale(...).
        final OverviewSaleDTO overviewSaleDTO = new OverviewSaleDTO();
        overviewSaleDTO.setOrderCount("10");
        overviewSaleDTO.setGuestCount("10");
        overviewSaleDTO.setConsumerAmount("10");
        overviewSaleDTO.setGatherAmount("10");
        final AmountItemDTO amountItemDTO = new AmountItemDTO();
        amountItemDTO.setName("name");
        amountItemDTO.setAmount("10");
        amountItemDTO.setExcessAmount("10");
        amountItemDTO.setEstimatedAmount("10");
        overviewSaleDTO.setGatherItems(Arrays.asList(amountItemDTO));
        overviewSaleDTO.setDiscountAmount("10");
        final DiscountAmountItemDTO discountAmountItemDTO = new DiscountAmountItemDTO();
        discountAmountItemDTO.setName("name");
        discountAmountItemDTO.setAmount("10");
        overviewSaleDTO.setDiscountItems(Arrays.asList(discountAmountItemDTO));
        overviewSaleDTO.setCheckoutStaffs(Arrays.asList("value"));
        overviewSaleDTO.setEstimatedAmount("10");
        when(mockBusinessDailyService.overviewSale(request)).thenReturn(overviewSaleDTO);

        // Configure BusinessDailyService.gatherSale(...).
        final GatherSaleDTO gatherSaleDTO = new GatherSaleDTO();
        gatherSaleDTO.setGatherName("gatherName");
        gatherSaleDTO.setConsumerAmount("10");
        gatherSaleDTO.setExcessAmount("10");
        gatherSaleDTO.setPrepaidAmount("10");
        gatherSaleDTO.setReserveAmount("10");
        gatherSaleDTO.setIsTotal(0);
        final List<GatherSaleDTO> gatherSaleDTOS = Arrays.asList(gatherSaleDTO);
        when(mockBusinessDailyService.gatherSale(request)).thenReturn(gatherSaleDTOS);

        // Configure BusinessDailyService.memberConsume(...).
        final MemberConsumeRespDTO memberConsumeRespDTO = new MemberConsumeRespDTO();
        memberConsumeRespDTO.setConsumerCount(0);
        memberConsumeRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidCount(0);
        memberConsumeRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidGiveAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidTakeInAmount(new BigDecimal("0.00"));
        final com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO amountItemDTO1 = new com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO();
        amountItemDTO1.setName("name");
        amountItemDTO1.setAmount(new BigDecimal("0.00"));
        amountItemDTO1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidItems(Arrays.asList(amountItemDTO1));
        when(mockBusinessDailyService.memberConsume(request)).thenReturn(memberConsumeRespDTO);

        // Configure BusinessDailyService.diningTypeSale(...).
        final DiningTypeSaleDTO diningTypeSaleDTO = new DiningTypeSaleDTO();
        diningTypeSaleDTO.setTypeCode("typeCode");
        diningTypeSaleDTO.setTypeName("platformName");
        diningTypeSaleDTO.setOrderCount("10");
        diningTypeSaleDTO.setGuestCount("10");
        diningTypeSaleDTO.setAmount("10");
        diningTypeSaleDTO.setOrderPrice("10");
        diningTypeSaleDTO.setGuestPrice("10");
        diningTypeSaleDTO.setSubDiningTypes(Arrays.asList(new DiningTypeSaleDTO()));
        diningTypeSaleDTO.setIsTotal(0);
        final List<DiningTypeSaleDTO> diningTypeSaleDTOS = Arrays.asList(diningTypeSaleDTO);
        when(mockBusinessDailyService.diningTypeSale(request)).thenReturn(diningTypeSaleDTOS);

        // Configure BusinessDailyService.classifySale(...).
        final CategorySaleDTO categorySaleDTO = new CategorySaleDTO();
        categorySaleDTO.setName("name");
        categorySaleDTO.setQuantum("10");
        categorySaleDTO.setAmount("10");
        categorySaleDTO.setDiscountAmount("10");
        categorySaleDTO.setIsTotal(0);
        final List<CategorySaleDTO> categorySaleDTOS = Arrays.asList(categorySaleDTO);
        when(mockBusinessDailyService.classifySale(request)).thenReturn(categorySaleDTOS);

        // Configure BusinessDailyService.goodsSale(...).
        final GoodsSaleDTO goodsSaleDTO = new GoodsSaleDTO();
        goodsSaleDTO.setName("name");
        goodsSaleDTO.setSkuName("skuName");
        goodsSaleDTO.setQuantum("10");
        goodsSaleDTO.setAmount("10");
        goodsSaleDTO.setItemType(0);
        goodsSaleDTO.setIsTotal(0);
        goodsSaleDTO.setSubs(Arrays.asList(new GoodsSaleDTO()));
        final List<GoodsSaleDTO> goodsSaleDTOS = Arrays.asList(goodsSaleDTO);
        when(mockBusinessDailyService.goodsSale(request)).thenReturn(goodsSaleDTOS);

        // Configure BusinessDailyService.attrSale(...).
        final PropStatsSaleDTO propStatsSaleDTO = new PropStatsSaleDTO();
        final PropStatsSaleDTO.PropGroup propGroup = new PropStatsSaleDTO.PropGroup();
        propGroup.setName("name");
        final PropStatsSaleDTO.PropItem propItem = new PropStatsSaleDTO.PropItem();
        propItem.setName("name");
        propItem.setQuantity("10");
        propItem.setMoney("10");
        propGroup.setPropList(Arrays.asList(propItem));
        propStatsSaleDTO.setPropGroupList(Arrays.asList(propGroup));
        when(mockBusinessDailyService.attrSale(request)).thenReturn(propStatsSaleDTO);

        // Configure BusinessDailyService.returnVegetablesSale(...).
        final ReturnSaleDTO returnSaleDTO = new ReturnSaleDTO();
        returnSaleDTO.setName("name");
        returnSaleDTO.setSkuName("skuName");
        returnSaleDTO.setQuantum("10");
        returnSaleDTO.setAmount("10");
        returnSaleDTO.setItemType(0);
        returnSaleDTO.setIsTotal(0);
        returnSaleDTO.setSubs(Arrays.asList(new ReturnSaleDTO()));
        final List<ReturnSaleDTO> returnSaleDTOS = Arrays.asList(returnSaleDTO);
        when(mockBusinessDailyService.returnVegetablesSale(request)).thenReturn(returnSaleDTOS);

        // Configure BusinessDailyService.dishGivingSale(...).
        final GiftSaleDTO giftSaleDTO = new GiftSaleDTO();
        giftSaleDTO.setName("name");
        giftSaleDTO.setSkuName("skuName");
        giftSaleDTO.setQuantum("10");
        giftSaleDTO.setAmount("10");
        giftSaleDTO.setItemType(0);
        giftSaleDTO.setIsTotal(0);
        giftSaleDTO.setSubs(Arrays.asList(new GiftSaleDTO()));
        final List<GiftSaleDTO> giftSaleDTOS = Arrays.asList(giftSaleDTO);
        when(mockBusinessDailyService.dishGivingSale(request)).thenReturn(giftSaleDTOS);

        // Configure BusinessDailyService.memberRechargeSale(...).
        final MemberRechargeSaleDTO memberRechargeSaleDTO = new MemberRechargeSaleDTO();
        memberRechargeSaleDTO.setRechargeNum("10");
        memberRechargeSaleDTO.setRechargeMemberNum("10");
        memberRechargeSaleDTO.setRechargeAmount("10");
        memberRechargeSaleDTO.setPresentAmount("10");
        memberRechargeSaleDTO.setIncomeAmount("10");
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        responsePayWayDetail.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail.setPayName("payName");
        responsePayWayDetail.setExcessAmount(new BigDecimal("0.00"));
        memberRechargeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail));
        when(mockBusinessDailyService.memberRechargeSale(request)).thenReturn(memberRechargeSaleDTO);

        // Configure BusinessDailyService.memberConsumeSale(...).
        final MemberConsumeSaleDTO memberConsumeSaleDTO = new MemberConsumeSaleDTO();
        memberConsumeSaleDTO.setConsumptionNum("10");
        memberConsumeSaleDTO.setConsumptionMemberNum("10");
        memberConsumeSaleDTO.setConsumptionAmount("10");
        final ResponsePayWayDetail responsePayWayDetail1 = new ResponsePayWayDetail();
        responsePayWayDetail1.setPayWay(0);
        responsePayWayDetail1.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail1.setPayName("payName");
        responsePayWayDetail1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail1));
        when(mockBusinessDailyService.memberConsumeSale(request)).thenReturn(memberConsumeSaleDTO);

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result<String> result = businessDailyPrintServiceImplUnderTest.print(request);

        // Verify the results
    }

    @Test
    public void testPrint3() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setBeginTime("2025-01-01");
        request.setEndTime("2025-01-10");
        request.setIsPrint(0);
        request.setType(3);

        // Configure BusinessDailyService.overviewSale(...).
        final OverviewSaleDTO overviewSaleDTO = new OverviewSaleDTO();
        overviewSaleDTO.setOrderCount("10");
        overviewSaleDTO.setGuestCount("10");
        overviewSaleDTO.setConsumerAmount("10");
        overviewSaleDTO.setGatherAmount("10");
        final AmountItemDTO amountItemDTO = new AmountItemDTO();
        amountItemDTO.setName("name");
        amountItemDTO.setAmount("10");
        amountItemDTO.setExcessAmount("10");
        amountItemDTO.setEstimatedAmount("10");
        overviewSaleDTO.setGatherItems(Arrays.asList(amountItemDTO));
        overviewSaleDTO.setDiscountAmount("10");
        final DiscountAmountItemDTO discountAmountItemDTO = new DiscountAmountItemDTO();
        discountAmountItemDTO.setName("name");
        discountAmountItemDTO.setAmount("10");
        overviewSaleDTO.setDiscountItems(Arrays.asList(discountAmountItemDTO));
        overviewSaleDTO.setCheckoutStaffs(Arrays.asList("value"));
        overviewSaleDTO.setEstimatedAmount("10");
        when(mockBusinessDailyService.overviewSale(request)).thenReturn(overviewSaleDTO);

        // Configure BusinessDailyService.gatherSale(...).
        final GatherSaleDTO gatherSaleDTO = new GatherSaleDTO();
        gatherSaleDTO.setGatherName("gatherName");
        gatherSaleDTO.setConsumerAmount("10");
        gatherSaleDTO.setExcessAmount("10");
        gatherSaleDTO.setPrepaidAmount("10");
        gatherSaleDTO.setReserveAmount("10");
        gatherSaleDTO.setIsTotal(0);
        final List<GatherSaleDTO> gatherSaleDTOS = Arrays.asList(gatherSaleDTO);
        when(mockBusinessDailyService.gatherSale(request)).thenReturn(gatherSaleDTOS);

        // Configure BusinessDailyService.memberConsume(...).
        final MemberConsumeRespDTO memberConsumeRespDTO = new MemberConsumeRespDTO();
        memberConsumeRespDTO.setConsumerCount(0);
        memberConsumeRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidCount(0);
        memberConsumeRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidGiveAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidTakeInAmount(new BigDecimal("0.00"));
        final com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO amountItemDTO1 = new com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO();
        amountItemDTO1.setName("name");
        amountItemDTO1.setAmount(new BigDecimal("0.00"));
        amountItemDTO1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidItems(Arrays.asList(amountItemDTO1));
        when(mockBusinessDailyService.memberConsume(request)).thenReturn(memberConsumeRespDTO);

        // Configure BusinessDailyService.diningTypeSale(...).
        final DiningTypeSaleDTO diningTypeSaleDTO = new DiningTypeSaleDTO();
        diningTypeSaleDTO.setTypeCode("typeCode");
        diningTypeSaleDTO.setTypeName("platformName");
        diningTypeSaleDTO.setOrderCount("10");
        diningTypeSaleDTO.setGuestCount("10");
        diningTypeSaleDTO.setAmount("10");
        diningTypeSaleDTO.setOrderPrice("10");
        diningTypeSaleDTO.setGuestPrice("10");
        diningTypeSaleDTO.setSubDiningTypes(Arrays.asList(new DiningTypeSaleDTO()));
        diningTypeSaleDTO.setIsTotal(0);
        final List<DiningTypeSaleDTO> diningTypeSaleDTOS = Arrays.asList(diningTypeSaleDTO);
        when(mockBusinessDailyService.diningTypeSale(request)).thenReturn(diningTypeSaleDTOS);

        // Configure BusinessDailyService.classifySale(...).
        final CategorySaleDTO categorySaleDTO = new CategorySaleDTO();
        categorySaleDTO.setName("name");
        categorySaleDTO.setQuantum("10");
        categorySaleDTO.setAmount("10");
        categorySaleDTO.setDiscountAmount("10");
        categorySaleDTO.setIsTotal(0);
        final List<CategorySaleDTO> categorySaleDTOS = Arrays.asList(categorySaleDTO);
        when(mockBusinessDailyService.classifySale(request)).thenReturn(categorySaleDTOS);

        // Configure BusinessDailyService.goodsSale(...).
        final GoodsSaleDTO goodsSaleDTO = new GoodsSaleDTO();
        goodsSaleDTO.setName("name");
        goodsSaleDTO.setSkuName("skuName");
        goodsSaleDTO.setQuantum("10");
        goodsSaleDTO.setAmount("10");
        goodsSaleDTO.setItemType(0);
        goodsSaleDTO.setIsTotal(0);
        goodsSaleDTO.setSubs(Arrays.asList(new GoodsSaleDTO()));
        final List<GoodsSaleDTO> goodsSaleDTOS = Arrays.asList(goodsSaleDTO);
        when(mockBusinessDailyService.goodsSale(request)).thenReturn(goodsSaleDTOS);

        // Configure BusinessDailyService.attrSale(...).
        final PropStatsSaleDTO propStatsSaleDTO = new PropStatsSaleDTO();
        final PropStatsSaleDTO.PropGroup propGroup = new PropStatsSaleDTO.PropGroup();
        propGroup.setName("name");
        final PropStatsSaleDTO.PropItem propItem = new PropStatsSaleDTO.PropItem();
        propItem.setName("name");
        propItem.setQuantity("10");
        propItem.setMoney("10");
        propGroup.setPropList(Arrays.asList(propItem));
        propStatsSaleDTO.setPropGroupList(Arrays.asList(propGroup));
        when(mockBusinessDailyService.attrSale(request)).thenReturn(propStatsSaleDTO);

        // Configure BusinessDailyService.returnVegetablesSale(...).
        final ReturnSaleDTO returnSaleDTO = new ReturnSaleDTO();
        returnSaleDTO.setName("name");
        returnSaleDTO.setSkuName("skuName");
        returnSaleDTO.setQuantum("10");
        returnSaleDTO.setAmount("10");
        returnSaleDTO.setItemType(0);
        returnSaleDTO.setIsTotal(0);
        returnSaleDTO.setSubs(Arrays.asList(new ReturnSaleDTO()));
        final List<ReturnSaleDTO> returnSaleDTOS = Arrays.asList(returnSaleDTO);
        when(mockBusinessDailyService.returnVegetablesSale(request)).thenReturn(returnSaleDTOS);

        // Configure BusinessDailyService.dishGivingSale(...).
        final GiftSaleDTO giftSaleDTO = new GiftSaleDTO();
        giftSaleDTO.setName("name");
        giftSaleDTO.setSkuName("skuName");
        giftSaleDTO.setQuantum("10");
        giftSaleDTO.setAmount("10");
        giftSaleDTO.setItemType(0);
        giftSaleDTO.setIsTotal(0);
        giftSaleDTO.setSubs(Arrays.asList(new GiftSaleDTO()));
        final List<GiftSaleDTO> giftSaleDTOS = Arrays.asList(giftSaleDTO);
        when(mockBusinessDailyService.dishGivingSale(request)).thenReturn(giftSaleDTOS);

        // Configure BusinessDailyService.memberRechargeSale(...).
        final MemberRechargeSaleDTO memberRechargeSaleDTO = new MemberRechargeSaleDTO();
        memberRechargeSaleDTO.setRechargeNum("10");
        memberRechargeSaleDTO.setRechargeMemberNum("10");
        memberRechargeSaleDTO.setRechargeAmount("10");
        memberRechargeSaleDTO.setPresentAmount("10");
        memberRechargeSaleDTO.setIncomeAmount("10");
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        responsePayWayDetail.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail.setPayName("payName");
        responsePayWayDetail.setExcessAmount(new BigDecimal("0.00"));
        memberRechargeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail));
        when(mockBusinessDailyService.memberRechargeSale(request)).thenReturn(memberRechargeSaleDTO);

        // Configure BusinessDailyService.memberConsumeSale(...).
        final MemberConsumeSaleDTO memberConsumeSaleDTO = new MemberConsumeSaleDTO();
        memberConsumeSaleDTO.setConsumptionNum("10");
        memberConsumeSaleDTO.setConsumptionMemberNum("10");
        memberConsumeSaleDTO.setConsumptionAmount("10");
        final ResponsePayWayDetail responsePayWayDetail1 = new ResponsePayWayDetail();
        responsePayWayDetail1.setPayWay(0);
        responsePayWayDetail1.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail1.setPayName("payName");
        responsePayWayDetail1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail1));
        when(mockBusinessDailyService.memberConsumeSale(request)).thenReturn(memberConsumeSaleDTO);

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result<String> result = businessDailyPrintServiceImplUnderTest.print(request);

        // Verify the results
    }


    @Test
    public void testPrint4() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setBeginTime("2025-01-01");
        request.setEndTime("2025-01-10");
        request.setIsPrint(0);
        request.setType(4);

        // Configure BusinessDailyService.overviewSale(...).
        final OverviewSaleDTO overviewSaleDTO = new OverviewSaleDTO();
        overviewSaleDTO.setOrderCount("10");
        overviewSaleDTO.setGuestCount("10");
        overviewSaleDTO.setConsumerAmount("10");
        overviewSaleDTO.setGatherAmount("10");
        final AmountItemDTO amountItemDTO = new AmountItemDTO();
        amountItemDTO.setName("name");
        amountItemDTO.setAmount("10");
        amountItemDTO.setExcessAmount("10");
        amountItemDTO.setEstimatedAmount("10");
        overviewSaleDTO.setGatherItems(Arrays.asList(amountItemDTO));
        overviewSaleDTO.setDiscountAmount("10");
        final DiscountAmountItemDTO discountAmountItemDTO = new DiscountAmountItemDTO();
        discountAmountItemDTO.setName("name");
        discountAmountItemDTO.setAmount("10");
        overviewSaleDTO.setDiscountItems(Arrays.asList(discountAmountItemDTO));
        overviewSaleDTO.setCheckoutStaffs(Arrays.asList("value"));
        overviewSaleDTO.setEstimatedAmount("10");
        when(mockBusinessDailyService.overviewSale(request)).thenReturn(overviewSaleDTO);

        // Configure BusinessDailyService.gatherSale(...).
        final GatherSaleDTO gatherSaleDTO = new GatherSaleDTO();
        gatherSaleDTO.setGatherName("gatherName");
        gatherSaleDTO.setConsumerAmount("10");
        gatherSaleDTO.setExcessAmount("10");
        gatherSaleDTO.setPrepaidAmount("10");
        gatherSaleDTO.setReserveAmount("10");
        gatherSaleDTO.setIsTotal(0);
        final List<GatherSaleDTO> gatherSaleDTOS = Arrays.asList(gatherSaleDTO);
        when(mockBusinessDailyService.gatherSale(request)).thenReturn(gatherSaleDTOS);

        // Configure BusinessDailyService.memberConsume(...).
        final MemberConsumeRespDTO memberConsumeRespDTO = new MemberConsumeRespDTO();
        memberConsumeRespDTO.setConsumerCount(0);
        memberConsumeRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidCount(0);
        memberConsumeRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidGiveAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidTakeInAmount(new BigDecimal("0.00"));
        final com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO amountItemDTO1 = new com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO();
        amountItemDTO1.setName("name");
        amountItemDTO1.setAmount(new BigDecimal("0.00"));
        amountItemDTO1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidItems(Arrays.asList(amountItemDTO1));
        when(mockBusinessDailyService.memberConsume(request)).thenReturn(memberConsumeRespDTO);

        // Configure BusinessDailyService.diningTypeSale(...).
        final DiningTypeSaleDTO diningTypeSaleDTO = new DiningTypeSaleDTO();
        diningTypeSaleDTO.setTypeCode(String.valueOf(DiningTypeEnum.DINE.getCode()));
        diningTypeSaleDTO.setTypeName("platformName");
        diningTypeSaleDTO.setOrderCount("10");
        diningTypeSaleDTO.setGuestCount("10");
        diningTypeSaleDTO.setAmount("10");
        diningTypeSaleDTO.setOrderPrice("10");
        diningTypeSaleDTO.setGuestPrice("10");
        diningTypeSaleDTO.setSubDiningTypes(Arrays.asList(new DiningTypeSaleDTO()));
        diningTypeSaleDTO.setIsTotal(0);

        final DiningTypeSaleDTO diningTypeSaleDTO2 = new DiningTypeSaleDTO();
        diningTypeSaleDTO2.setTypeCode(String.valueOf(DiningTypeEnum.SNACK.getCode()));
        diningTypeSaleDTO2.setTypeName("platformName");
        diningTypeSaleDTO2.setOrderCount("10");
        diningTypeSaleDTO2.setGuestCount("10");
        diningTypeSaleDTO2.setAmount("10");
        diningTypeSaleDTO2.setOrderPrice("10");
        diningTypeSaleDTO2.setGuestPrice("10");
        diningTypeSaleDTO2.setSubDiningTypes(Arrays.asList(new DiningTypeSaleDTO()));
        diningTypeSaleDTO2.setIsTotal(0);

        final DiningTypeSaleDTO diningTypeSaleDTO3 = new DiningTypeSaleDTO();
        diningTypeSaleDTO3.setTypeCode(String.valueOf(DiningTypeEnum.TAKEOUT.getCode()));
        diningTypeSaleDTO3.setTypeName("platformName");
        diningTypeSaleDTO3.setOrderCount("10");
        diningTypeSaleDTO3.setGuestCount("10");
        diningTypeSaleDTO3.setAmount("10");
        diningTypeSaleDTO3.setOrderPrice("10");
        diningTypeSaleDTO3.setGuestPrice("10");
        DiningTypeSaleDTO diningTypeSaleDTO1 = new DiningTypeSaleDTO();
        diningTypeSaleDTO1.setOrderCount("10");
        diningTypeSaleDTO1.setAmount("10");
        diningTypeSaleDTO1.setOrderPrice("10");
        diningTypeSaleDTO3.setSubDiningTypes(Arrays.asList(diningTypeSaleDTO1));
        diningTypeSaleDTO3.setIsTotal(0);

        final DiningTypeSaleDTO diningTypeSaleDTO4 = new DiningTypeSaleDTO();
        diningTypeSaleDTO4.setTypeCode("typeCode");
        diningTypeSaleDTO4.setTypeName("platformName");
        diningTypeSaleDTO4.setOrderCount("10");
        diningTypeSaleDTO4.setGuestCount("10");
        diningTypeSaleDTO4.setAmount("10");
        diningTypeSaleDTO4.setOrderPrice("10");
        diningTypeSaleDTO4.setGuestPrice("10");
        diningTypeSaleDTO4.setSubDiningTypes(Arrays.asList(new DiningTypeSaleDTO()));
        diningTypeSaleDTO4.setIsTotal(1);

        final List<DiningTypeSaleDTO> diningTypeSaleDTOS = Arrays.asList(diningTypeSaleDTO, diningTypeSaleDTO2,
                diningTypeSaleDTO3, diningTypeSaleDTO4);
        when(mockBusinessDailyService.diningTypeSale(request)).thenReturn(diningTypeSaleDTOS);

        // Configure BusinessDailyService.classifySale(...).
        final CategorySaleDTO categorySaleDTO = new CategorySaleDTO();
        categorySaleDTO.setName("name");
        categorySaleDTO.setQuantum("10");
        categorySaleDTO.setAmount("10");
        categorySaleDTO.setDiscountAmount("10");
        categorySaleDTO.setIsTotal(0);
        final List<CategorySaleDTO> categorySaleDTOS = Arrays.asList(categorySaleDTO);
        when(mockBusinessDailyService.classifySale(request)).thenReturn(categorySaleDTOS);

        // Configure BusinessDailyService.goodsSale(...).
        final GoodsSaleDTO goodsSaleDTO = new GoodsSaleDTO();
        goodsSaleDTO.setName("name");
        goodsSaleDTO.setSkuName("skuName");
        goodsSaleDTO.setQuantum("10");
        goodsSaleDTO.setAmount("10");
        goodsSaleDTO.setItemType(0);
        goodsSaleDTO.setIsTotal(0);
        goodsSaleDTO.setSubs(Arrays.asList(new GoodsSaleDTO()));
        final List<GoodsSaleDTO> goodsSaleDTOS = Arrays.asList(goodsSaleDTO);
        when(mockBusinessDailyService.goodsSale(request)).thenReturn(goodsSaleDTOS);

        // Configure BusinessDailyService.attrSale(...).
        final PropStatsSaleDTO propStatsSaleDTO = new PropStatsSaleDTO();
        final PropStatsSaleDTO.PropGroup propGroup = new PropStatsSaleDTO.PropGroup();
        propGroup.setName("name");
        final PropStatsSaleDTO.PropItem propItem = new PropStatsSaleDTO.PropItem();
        propItem.setName("name");
        propItem.setQuantity("10");
        propItem.setMoney("10");
        propGroup.setPropList(Arrays.asList(propItem));
        propStatsSaleDTO.setPropGroupList(Arrays.asList(propGroup));
        when(mockBusinessDailyService.attrSale(request)).thenReturn(propStatsSaleDTO);

        // Configure BusinessDailyService.returnVegetablesSale(...).
        final ReturnSaleDTO returnSaleDTO = new ReturnSaleDTO();
        returnSaleDTO.setName("name");
        returnSaleDTO.setSkuName("skuName");
        returnSaleDTO.setQuantum("10");
        returnSaleDTO.setAmount("10");
        returnSaleDTO.setItemType(0);
        returnSaleDTO.setIsTotal(0);
        returnSaleDTO.setSubs(Arrays.asList(new ReturnSaleDTO()));
        final List<ReturnSaleDTO> returnSaleDTOS = Arrays.asList(returnSaleDTO);
        when(mockBusinessDailyService.returnVegetablesSale(request)).thenReturn(returnSaleDTOS);

        // Configure BusinessDailyService.dishGivingSale(...).
        final GiftSaleDTO giftSaleDTO = new GiftSaleDTO();
        giftSaleDTO.setName("name");
        giftSaleDTO.setSkuName("skuName");
        giftSaleDTO.setQuantum("10");
        giftSaleDTO.setAmount("10");
        giftSaleDTO.setItemType(0);
        giftSaleDTO.setIsTotal(0);
        giftSaleDTO.setSubs(Arrays.asList(new GiftSaleDTO()));
        final List<GiftSaleDTO> giftSaleDTOS = Arrays.asList(giftSaleDTO);
        when(mockBusinessDailyService.dishGivingSale(request)).thenReturn(giftSaleDTOS);

        // Configure BusinessDailyService.memberRechargeSale(...).
        final MemberRechargeSaleDTO memberRechargeSaleDTO = new MemberRechargeSaleDTO();
        memberRechargeSaleDTO.setRechargeNum("10");
        memberRechargeSaleDTO.setRechargeMemberNum("10");
        memberRechargeSaleDTO.setRechargeAmount("10");
        memberRechargeSaleDTO.setPresentAmount("10");
        memberRechargeSaleDTO.setIncomeAmount("10");
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        responsePayWayDetail.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail.setPayName("payName");
        responsePayWayDetail.setExcessAmount(new BigDecimal("0.00"));
        memberRechargeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail));
        when(mockBusinessDailyService.memberRechargeSale(request)).thenReturn(memberRechargeSaleDTO);

        // Configure BusinessDailyService.memberConsumeSale(...).
        final MemberConsumeSaleDTO memberConsumeSaleDTO = new MemberConsumeSaleDTO();
        memberConsumeSaleDTO.setConsumptionNum("10");
        memberConsumeSaleDTO.setConsumptionMemberNum("10");
        memberConsumeSaleDTO.setConsumptionAmount("10");
        final ResponsePayWayDetail responsePayWayDetail1 = new ResponsePayWayDetail();
        responsePayWayDetail1.setPayWay(0);
        responsePayWayDetail1.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail1.setPayName("payName");
        responsePayWayDetail1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail1));
        when(mockBusinessDailyService.memberConsumeSale(request)).thenReturn(memberConsumeSaleDTO);

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result<String> result = businessDailyPrintServiceImplUnderTest.print(request);

        // Verify the results
    }


    @Test
    public void testPrint5() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setBeginTime("2025-01-01");
        request.setEndTime("2025-01-10");
        request.setIsPrint(0);
        request.setType(5);

        // Configure BusinessDailyService.overviewSale(...).
        final OverviewSaleDTO overviewSaleDTO = new OverviewSaleDTO();
        overviewSaleDTO.setOrderCount("10");
        overviewSaleDTO.setGuestCount("10");
        overviewSaleDTO.setConsumerAmount("10");
        overviewSaleDTO.setGatherAmount("10");
        final AmountItemDTO amountItemDTO = new AmountItemDTO();
        amountItemDTO.setName("name");
        amountItemDTO.setAmount("10");
        amountItemDTO.setExcessAmount("10");
        amountItemDTO.setEstimatedAmount("10");
        overviewSaleDTO.setGatherItems(Arrays.asList(amountItemDTO));
        overviewSaleDTO.setDiscountAmount("10");
        final DiscountAmountItemDTO discountAmountItemDTO = new DiscountAmountItemDTO();
        discountAmountItemDTO.setName("name");
        discountAmountItemDTO.setAmount("10");
        overviewSaleDTO.setDiscountItems(Arrays.asList(discountAmountItemDTO));
        overviewSaleDTO.setCheckoutStaffs(Arrays.asList("value"));
        overviewSaleDTO.setEstimatedAmount("10");
        when(mockBusinessDailyService.overviewSale(request)).thenReturn(overviewSaleDTO);

        // Configure BusinessDailyService.gatherSale(...).
        final GatherSaleDTO gatherSaleDTO = new GatherSaleDTO();
        gatherSaleDTO.setGatherName("gatherName");
        gatherSaleDTO.setConsumerAmount("10");
        gatherSaleDTO.setExcessAmount("10");
        gatherSaleDTO.setPrepaidAmount("10");
        gatherSaleDTO.setReserveAmount("10");
        gatherSaleDTO.setIsTotal(0);
        final List<GatherSaleDTO> gatherSaleDTOS = Arrays.asList(gatherSaleDTO);
        when(mockBusinessDailyService.gatherSale(request)).thenReturn(gatherSaleDTOS);

        // Configure BusinessDailyService.memberConsume(...).
        final MemberConsumeRespDTO memberConsumeRespDTO = new MemberConsumeRespDTO();
        memberConsumeRespDTO.setConsumerCount(0);
        memberConsumeRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidCount(0);
        memberConsumeRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidGiveAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidTakeInAmount(new BigDecimal("0.00"));
        final com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO amountItemDTO1 = new com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO();
        amountItemDTO1.setName("name");
        amountItemDTO1.setAmount(new BigDecimal("0.00"));
        amountItemDTO1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidItems(Arrays.asList(amountItemDTO1));
        when(mockBusinessDailyService.memberConsume(request)).thenReturn(memberConsumeRespDTO);

        // Configure BusinessDailyService.diningTypeSale(...).
        final DiningTypeSaleDTO diningTypeSaleDTO = new DiningTypeSaleDTO();
        diningTypeSaleDTO.setTypeCode("typeCode");
        diningTypeSaleDTO.setTypeName("platformName");
        diningTypeSaleDTO.setOrderCount("10");
        diningTypeSaleDTO.setGuestCount("10");
        diningTypeSaleDTO.setAmount("10");
        diningTypeSaleDTO.setOrderPrice("10");
        diningTypeSaleDTO.setGuestPrice("10");
        diningTypeSaleDTO.setSubDiningTypes(Arrays.asList(new DiningTypeSaleDTO()));
        diningTypeSaleDTO.setIsTotal(0);
        final List<DiningTypeSaleDTO> diningTypeSaleDTOS = Arrays.asList(diningTypeSaleDTO);
        when(mockBusinessDailyService.diningTypeSale(request)).thenReturn(diningTypeSaleDTOS);

        // Configure BusinessDailyService.classifySale(...).
        final CategorySaleDTO categorySaleDTO = new CategorySaleDTO();
        categorySaleDTO.setName("name");
        categorySaleDTO.setQuantum("10");
        categorySaleDTO.setAmount("10");
        categorySaleDTO.setDiscountAmount("10");
        categorySaleDTO.setIsTotal(0);
        final List<CategorySaleDTO> categorySaleDTOS = Arrays.asList(categorySaleDTO);
        when(mockBusinessDailyService.classifySale(request)).thenReturn(categorySaleDTOS);

        // Configure BusinessDailyService.goodsSale(...).
        final GoodsSaleDTO goodsSaleDTO = new GoodsSaleDTO();
        goodsSaleDTO.setName("name");
        goodsSaleDTO.setSkuName("skuName");
        goodsSaleDTO.setQuantum("10");
        goodsSaleDTO.setAmount("10");
        goodsSaleDTO.setItemType(0);
        goodsSaleDTO.setIsTotal(0);
        goodsSaleDTO.setSubs(Arrays.asList(new GoodsSaleDTO()));
        final List<GoodsSaleDTO> goodsSaleDTOS = Arrays.asList(goodsSaleDTO);
        when(mockBusinessDailyService.goodsSale(request)).thenReturn(goodsSaleDTOS);

        // Configure BusinessDailyService.attrSale(...).
        final PropStatsSaleDTO propStatsSaleDTO = new PropStatsSaleDTO();
        final PropStatsSaleDTO.PropGroup propGroup = new PropStatsSaleDTO.PropGroup();
        propGroup.setName("name");
        final PropStatsSaleDTO.PropItem propItem = new PropStatsSaleDTO.PropItem();
        propItem.setName("name");
        propItem.setQuantity("10");
        propItem.setMoney("10");
        propGroup.setPropList(Arrays.asList(propItem));
        propStatsSaleDTO.setPropGroupList(Arrays.asList(propGroup));
        when(mockBusinessDailyService.attrSale(request)).thenReturn(propStatsSaleDTO);

        // Configure BusinessDailyService.returnVegetablesSale(...).
        final ReturnSaleDTO returnSaleDTO = new ReturnSaleDTO();
        returnSaleDTO.setName("name");
        returnSaleDTO.setSkuName("skuName");
        returnSaleDTO.setQuantum("10");
        returnSaleDTO.setAmount("10");
        returnSaleDTO.setItemType(0);
        returnSaleDTO.setIsTotal(0);
        returnSaleDTO.setSubs(Arrays.asList(new ReturnSaleDTO()));
        final List<ReturnSaleDTO> returnSaleDTOS = Arrays.asList(returnSaleDTO);
        when(mockBusinessDailyService.returnVegetablesSale(request)).thenReturn(returnSaleDTOS);

        // Configure BusinessDailyService.dishGivingSale(...).
        final GiftSaleDTO giftSaleDTO = new GiftSaleDTO();
        giftSaleDTO.setName("name");
        giftSaleDTO.setSkuName("skuName");
        giftSaleDTO.setQuantum("10");
        giftSaleDTO.setAmount("10");
        giftSaleDTO.setItemType(0);
        giftSaleDTO.setIsTotal(0);
        giftSaleDTO.setSubs(Arrays.asList(new GiftSaleDTO()));
        final List<GiftSaleDTO> giftSaleDTOS = Arrays.asList(giftSaleDTO);
        when(mockBusinessDailyService.dishGivingSale(request)).thenReturn(giftSaleDTOS);

        // Configure BusinessDailyService.memberRechargeSale(...).
        final MemberRechargeSaleDTO memberRechargeSaleDTO = new MemberRechargeSaleDTO();
        memberRechargeSaleDTO.setRechargeNum("10");
        memberRechargeSaleDTO.setRechargeMemberNum("10");
        memberRechargeSaleDTO.setRechargeAmount("10");
        memberRechargeSaleDTO.setPresentAmount("10");
        memberRechargeSaleDTO.setIncomeAmount("10");
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        responsePayWayDetail.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail.setPayName("payName");
        responsePayWayDetail.setExcessAmount(new BigDecimal("0.00"));
        memberRechargeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail));
        when(mockBusinessDailyService.memberRechargeSale(request)).thenReturn(memberRechargeSaleDTO);

        // Configure BusinessDailyService.memberConsumeSale(...).
        final MemberConsumeSaleDTO memberConsumeSaleDTO = new MemberConsumeSaleDTO();
        memberConsumeSaleDTO.setConsumptionNum("10");
        memberConsumeSaleDTO.setConsumptionMemberNum("10");
        memberConsumeSaleDTO.setConsumptionAmount("10");
        final ResponsePayWayDetail responsePayWayDetail1 = new ResponsePayWayDetail();
        responsePayWayDetail1.setPayWay(0);
        responsePayWayDetail1.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail1.setPayName("payName");
        responsePayWayDetail1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail1));
        when(mockBusinessDailyService.memberConsumeSale(request)).thenReturn(memberConsumeSaleDTO);

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result<String> result = businessDailyPrintServiceImplUnderTest.print(request);

        // Verify the results
    }


    @Test
    public void testPrint6() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setBeginTime("2025-01-01");
        request.setEndTime("2025-01-10");
        request.setIsPrint(0);
        request.setType(6);

        // Configure BusinessDailyService.overviewSale(...).
        final OverviewSaleDTO overviewSaleDTO = new OverviewSaleDTO();
        overviewSaleDTO.setOrderCount("10");
        overviewSaleDTO.setGuestCount("10");
        overviewSaleDTO.setConsumerAmount("10");
        overviewSaleDTO.setGatherAmount("10");
        final AmountItemDTO amountItemDTO = new AmountItemDTO();
        amountItemDTO.setName("name");
        amountItemDTO.setAmount("10");
        amountItemDTO.setExcessAmount("10");
        amountItemDTO.setEstimatedAmount("10");
        overviewSaleDTO.setGatherItems(Arrays.asList(amountItemDTO));
        overviewSaleDTO.setDiscountAmount("10");
        final DiscountAmountItemDTO discountAmountItemDTO = new DiscountAmountItemDTO();
        discountAmountItemDTO.setName("name");
        discountAmountItemDTO.setAmount("10");
        overviewSaleDTO.setDiscountItems(Arrays.asList(discountAmountItemDTO));
        overviewSaleDTO.setCheckoutStaffs(Arrays.asList("value"));
        overviewSaleDTO.setEstimatedAmount("10");
        when(mockBusinessDailyService.overviewSale(request)).thenReturn(overviewSaleDTO);

        // Configure BusinessDailyService.gatherSale(...).
        final GatherSaleDTO gatherSaleDTO = new GatherSaleDTO();
        gatherSaleDTO.setGatherName("gatherName");
        gatherSaleDTO.setConsumerAmount("10");
        gatherSaleDTO.setExcessAmount("10");
        gatherSaleDTO.setPrepaidAmount("10");
        gatherSaleDTO.setReserveAmount("10");
        gatherSaleDTO.setIsTotal(0);
        final List<GatherSaleDTO> gatherSaleDTOS = Arrays.asList(gatherSaleDTO);
        when(mockBusinessDailyService.gatherSale(request)).thenReturn(gatherSaleDTOS);

        // Configure BusinessDailyService.memberConsume(...).
        final MemberConsumeRespDTO memberConsumeRespDTO = new MemberConsumeRespDTO();
        memberConsumeRespDTO.setConsumerCount(0);
        memberConsumeRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidCount(0);
        memberConsumeRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidGiveAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidTakeInAmount(new BigDecimal("0.00"));
        final com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO amountItemDTO1 = new com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO();
        amountItemDTO1.setName("name");
        amountItemDTO1.setAmount(new BigDecimal("0.00"));
        amountItemDTO1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidItems(Arrays.asList(amountItemDTO1));
        when(mockBusinessDailyService.memberConsume(request)).thenReturn(memberConsumeRespDTO);

        // Configure BusinessDailyService.diningTypeSale(...).
        final DiningTypeSaleDTO diningTypeSaleDTO = new DiningTypeSaleDTO();
        diningTypeSaleDTO.setTypeCode("typeCode");
        diningTypeSaleDTO.setTypeName("platformName");
        diningTypeSaleDTO.setOrderCount("10");
        diningTypeSaleDTO.setGuestCount("10");
        diningTypeSaleDTO.setAmount("10");
        diningTypeSaleDTO.setOrderPrice("10");
        diningTypeSaleDTO.setGuestPrice("10");
        diningTypeSaleDTO.setSubDiningTypes(Arrays.asList(new DiningTypeSaleDTO()));
        diningTypeSaleDTO.setIsTotal(0);
        final List<DiningTypeSaleDTO> diningTypeSaleDTOS = Arrays.asList(diningTypeSaleDTO);
        when(mockBusinessDailyService.diningTypeSale(request)).thenReturn(diningTypeSaleDTOS);

        // Configure BusinessDailyService.classifySale(...).
        final CategorySaleDTO categorySaleDTO = new CategorySaleDTO();
        categorySaleDTO.setName("name");
        categorySaleDTO.setQuantum("10");
        categorySaleDTO.setAmount("10");
        categorySaleDTO.setDiscountAmount("10");
        categorySaleDTO.setIsTotal(0);
        final List<CategorySaleDTO> categorySaleDTOS = Arrays.asList(categorySaleDTO);
        when(mockBusinessDailyService.classifySale(request)).thenReturn(categorySaleDTOS);

        // Configure BusinessDailyService.goodsSale(...).
        final GoodsSaleDTO goodsSaleDTO = new GoodsSaleDTO();
        goodsSaleDTO.setName("name");
        goodsSaleDTO.setSkuName("skuName");
        goodsSaleDTO.setQuantum("10");
        goodsSaleDTO.setAmount("10");
        goodsSaleDTO.setItemType(0);
        goodsSaleDTO.setIsTotal(0);
        goodsSaleDTO.setSubs(Arrays.asList(new GoodsSaleDTO()));
        final List<GoodsSaleDTO> goodsSaleDTOS = Arrays.asList(goodsSaleDTO);
        when(mockBusinessDailyService.goodsSale(request)).thenReturn(goodsSaleDTOS);

        // Configure BusinessDailyService.attrSale(...).
        final PropStatsSaleDTO propStatsSaleDTO = new PropStatsSaleDTO();
        final PropStatsSaleDTO.PropGroup propGroup = new PropStatsSaleDTO.PropGroup();
        propGroup.setName("name");
        final PropStatsSaleDTO.PropItem propItem = new PropStatsSaleDTO.PropItem();
        propItem.setName("name");
        propItem.setQuantity("10");
        propItem.setMoney("10");
        propGroup.setPropList(Arrays.asList(propItem));
        propStatsSaleDTO.setPropGroupList(Arrays.asList(propGroup));
        when(mockBusinessDailyService.attrSale(request)).thenReturn(propStatsSaleDTO);

        // Configure BusinessDailyService.returnVegetablesSale(...).
        final ReturnSaleDTO returnSaleDTO = new ReturnSaleDTO();
        returnSaleDTO.setName("name");
        returnSaleDTO.setSkuName("skuName");
        returnSaleDTO.setQuantum("10");
        returnSaleDTO.setAmount("10");
        returnSaleDTO.setItemType(0);
        returnSaleDTO.setIsTotal(0);
        returnSaleDTO.setSubs(Arrays.asList(new ReturnSaleDTO()));
        final List<ReturnSaleDTO> returnSaleDTOS = Arrays.asList(returnSaleDTO);
        when(mockBusinessDailyService.returnVegetablesSale(request)).thenReturn(returnSaleDTOS);

        // Configure BusinessDailyService.dishGivingSale(...).
        final GiftSaleDTO giftSaleDTO = new GiftSaleDTO();
        giftSaleDTO.setName("name");
        giftSaleDTO.setSkuName("skuName");
        giftSaleDTO.setQuantum("10");
        giftSaleDTO.setAmount("10");
        giftSaleDTO.setItemType(0);
        giftSaleDTO.setIsTotal(0);
        giftSaleDTO.setSubs(Arrays.asList(new GiftSaleDTO()));
        final List<GiftSaleDTO> giftSaleDTOS = Arrays.asList(giftSaleDTO);
        when(mockBusinessDailyService.dishGivingSale(request)).thenReturn(giftSaleDTOS);

        // Configure BusinessDailyService.memberRechargeSale(...).
        final MemberRechargeSaleDTO memberRechargeSaleDTO = new MemberRechargeSaleDTO();
        memberRechargeSaleDTO.setRechargeNum("10");
        memberRechargeSaleDTO.setRechargeMemberNum("10");
        memberRechargeSaleDTO.setRechargeAmount("10");
        memberRechargeSaleDTO.setPresentAmount("10");
        memberRechargeSaleDTO.setIncomeAmount("10");
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        responsePayWayDetail.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail.setPayName("payName");
        responsePayWayDetail.setExcessAmount(new BigDecimal("0.00"));
        memberRechargeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail));
        when(mockBusinessDailyService.memberRechargeSale(request)).thenReturn(memberRechargeSaleDTO);

        // Configure BusinessDailyService.memberConsumeSale(...).
        final MemberConsumeSaleDTO memberConsumeSaleDTO = new MemberConsumeSaleDTO();
        memberConsumeSaleDTO.setConsumptionNum("10");
        memberConsumeSaleDTO.setConsumptionMemberNum("10");
        memberConsumeSaleDTO.setConsumptionAmount("10");
        final ResponsePayWayDetail responsePayWayDetail1 = new ResponsePayWayDetail();
        responsePayWayDetail1.setPayWay(0);
        responsePayWayDetail1.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail1.setPayName("payName");
        responsePayWayDetail1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail1));
        when(mockBusinessDailyService.memberConsumeSale(request)).thenReturn(memberConsumeSaleDTO);

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result<String> result = businessDailyPrintServiceImplUnderTest.print(request);

        // Verify the results
    }


    @Test
    public void testPrint7() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setBeginTime("2025-01-01");
        request.setEndTime("2025-01-10");
        request.setIsPrint(0);
        request.setType(7);

        // Configure BusinessDailyService.overviewSale(...).
        final OverviewSaleDTO overviewSaleDTO = new OverviewSaleDTO();
        overviewSaleDTO.setOrderCount("10");
        overviewSaleDTO.setGuestCount("10");
        overviewSaleDTO.setConsumerAmount("10");
        overviewSaleDTO.setGatherAmount("10");
        final AmountItemDTO amountItemDTO = new AmountItemDTO();
        amountItemDTO.setName("name");
        amountItemDTO.setAmount("10");
        amountItemDTO.setExcessAmount("10");
        amountItemDTO.setEstimatedAmount("10");
        overviewSaleDTO.setGatherItems(Arrays.asList(amountItemDTO));
        overviewSaleDTO.setDiscountAmount("10");
        final DiscountAmountItemDTO discountAmountItemDTO = new DiscountAmountItemDTO();
        discountAmountItemDTO.setName("name");
        discountAmountItemDTO.setAmount("10");
        overviewSaleDTO.setDiscountItems(Arrays.asList(discountAmountItemDTO));
        overviewSaleDTO.setCheckoutStaffs(Arrays.asList("value"));
        overviewSaleDTO.setEstimatedAmount("10");
        when(mockBusinessDailyService.overviewSale(request)).thenReturn(overviewSaleDTO);

        // Configure BusinessDailyService.gatherSale(...).
        final GatherSaleDTO gatherSaleDTO = new GatherSaleDTO();
        gatherSaleDTO.setGatherName("gatherName");
        gatherSaleDTO.setConsumerAmount("10");
        gatherSaleDTO.setExcessAmount("10");
        gatherSaleDTO.setPrepaidAmount("10");
        gatherSaleDTO.setReserveAmount("10");
        gatherSaleDTO.setIsTotal(0);
        final List<GatherSaleDTO> gatherSaleDTOS = Arrays.asList(gatherSaleDTO);
        when(mockBusinessDailyService.gatherSale(request)).thenReturn(gatherSaleDTOS);

        // Configure BusinessDailyService.memberConsume(...).
        final MemberConsumeRespDTO memberConsumeRespDTO = new MemberConsumeRespDTO();
        memberConsumeRespDTO.setConsumerCount(0);
        memberConsumeRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidCount(0);
        memberConsumeRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidGiveAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidTakeInAmount(new BigDecimal("0.00"));
        final com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO amountItemDTO1 = new com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO();
        amountItemDTO1.setName("name");
        amountItemDTO1.setAmount(new BigDecimal("0.00"));
        amountItemDTO1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidItems(Arrays.asList(amountItemDTO1));
        when(mockBusinessDailyService.memberConsume(request)).thenReturn(memberConsumeRespDTO);

        // Configure BusinessDailyService.diningTypeSale(...).
        final DiningTypeSaleDTO diningTypeSaleDTO = new DiningTypeSaleDTO();
        diningTypeSaleDTO.setTypeCode("typeCode");
        diningTypeSaleDTO.setTypeName("platformName");
        diningTypeSaleDTO.setOrderCount("10");
        diningTypeSaleDTO.setGuestCount("10");
        diningTypeSaleDTO.setAmount("10");
        diningTypeSaleDTO.setOrderPrice("10");
        diningTypeSaleDTO.setGuestPrice("10");
        diningTypeSaleDTO.setSubDiningTypes(Arrays.asList(new DiningTypeSaleDTO()));
        diningTypeSaleDTO.setIsTotal(0);
        final List<DiningTypeSaleDTO> diningTypeSaleDTOS = Arrays.asList(diningTypeSaleDTO);
        when(mockBusinessDailyService.diningTypeSale(request)).thenReturn(diningTypeSaleDTOS);

        // Configure BusinessDailyService.classifySale(...).
        final CategorySaleDTO categorySaleDTO = new CategorySaleDTO();
        categorySaleDTO.setName("name");
        categorySaleDTO.setQuantum("10");
        categorySaleDTO.setAmount("10");
        categorySaleDTO.setDiscountAmount("10");
        categorySaleDTO.setIsTotal(0);
        final List<CategorySaleDTO> categorySaleDTOS = Arrays.asList(categorySaleDTO);
        when(mockBusinessDailyService.classifySale(request)).thenReturn(categorySaleDTOS);

        // Configure BusinessDailyService.goodsSale(...).
        final GoodsSaleDTO goodsSaleDTO = new GoodsSaleDTO();
        goodsSaleDTO.setName("name");
        goodsSaleDTO.setSkuName("skuName");
        goodsSaleDTO.setQuantum("10");
        goodsSaleDTO.setAmount("10");
        goodsSaleDTO.setItemType(0);
        goodsSaleDTO.setIsTotal(0);
        goodsSaleDTO.setSubs(Arrays.asList(new GoodsSaleDTO()));
        final List<GoodsSaleDTO> goodsSaleDTOS = Arrays.asList(goodsSaleDTO);
        when(mockBusinessDailyService.goodsSale(request)).thenReturn(goodsSaleDTOS);

        // Configure BusinessDailyService.attrSale(...).
        final PropStatsSaleDTO propStatsSaleDTO = new PropStatsSaleDTO();
        final PropStatsSaleDTO.PropGroup propGroup = new PropStatsSaleDTO.PropGroup();
        propGroup.setName("name");
        final PropStatsSaleDTO.PropItem propItem = new PropStatsSaleDTO.PropItem();
        propItem.setName("name");
        propItem.setQuantity("10");
        propItem.setMoney("10");
        propGroup.setPropList(Arrays.asList(propItem));
        propStatsSaleDTO.setPropGroupList(Arrays.asList(propGroup));
        when(mockBusinessDailyService.attrSale(request)).thenReturn(propStatsSaleDTO);

        // Configure BusinessDailyService.returnVegetablesSale(...).
        final ReturnSaleDTO returnSaleDTO = new ReturnSaleDTO();
        returnSaleDTO.setName("name");
        returnSaleDTO.setSkuName("skuName");
        returnSaleDTO.setQuantum("10");
        returnSaleDTO.setAmount("10");
        returnSaleDTO.setItemType(0);
        returnSaleDTO.setIsTotal(0);
        returnSaleDTO.setSubs(Arrays.asList(new ReturnSaleDTO()));
        final List<ReturnSaleDTO> returnSaleDTOS = Arrays.asList(returnSaleDTO);
        when(mockBusinessDailyService.returnVegetablesSale(request)).thenReturn(returnSaleDTOS);

        // Configure BusinessDailyService.dishGivingSale(...).
        final GiftSaleDTO giftSaleDTO = new GiftSaleDTO();
        giftSaleDTO.setName("name");
        giftSaleDTO.setSkuName("skuName");
        giftSaleDTO.setQuantum("10");
        giftSaleDTO.setAmount("10");
        giftSaleDTO.setItemType(0);
        giftSaleDTO.setIsTotal(0);
        giftSaleDTO.setSubs(Arrays.asList(new GiftSaleDTO()));
        final List<GiftSaleDTO> giftSaleDTOS = Arrays.asList(giftSaleDTO);
        when(mockBusinessDailyService.dishGivingSale(request)).thenReturn(giftSaleDTOS);

        // Configure BusinessDailyService.memberRechargeSale(...).
        final MemberRechargeSaleDTO memberRechargeSaleDTO = new MemberRechargeSaleDTO();
        memberRechargeSaleDTO.setRechargeNum("10");
        memberRechargeSaleDTO.setRechargeMemberNum("10");
        memberRechargeSaleDTO.setRechargeAmount("10");
        memberRechargeSaleDTO.setPresentAmount("10");
        memberRechargeSaleDTO.setIncomeAmount("10");
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        responsePayWayDetail.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail.setPayName("payName");
        responsePayWayDetail.setExcessAmount(new BigDecimal("0.00"));
        memberRechargeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail));
        when(mockBusinessDailyService.memberRechargeSale(request)).thenReturn(memberRechargeSaleDTO);

        // Configure BusinessDailyService.memberConsumeSale(...).
        final MemberConsumeSaleDTO memberConsumeSaleDTO = new MemberConsumeSaleDTO();
        memberConsumeSaleDTO.setConsumptionNum("10");
        memberConsumeSaleDTO.setConsumptionMemberNum("10");
        memberConsumeSaleDTO.setConsumptionAmount("10");
        final ResponsePayWayDetail responsePayWayDetail1 = new ResponsePayWayDetail();
        responsePayWayDetail1.setPayWay(0);
        responsePayWayDetail1.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail1.setPayName("payName");
        responsePayWayDetail1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail1));
        when(mockBusinessDailyService.memberConsumeSale(request)).thenReturn(memberConsumeSaleDTO);

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result<String> result = businessDailyPrintServiceImplUnderTest.print(request);

        // Verify the results
    }


    @Test
    public void testPrint8() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setBeginTime("2025-01-01");
        request.setEndTime("2025-01-10");
        request.setIsPrint(0);
        request.setType(8);

        // Configure BusinessDailyService.overviewSale(...).
        final OverviewSaleDTO overviewSaleDTO = new OverviewSaleDTO();
        overviewSaleDTO.setOrderCount("10");
        overviewSaleDTO.setGuestCount("10");
        overviewSaleDTO.setConsumerAmount("10");
        overviewSaleDTO.setGatherAmount("10");
        final AmountItemDTO amountItemDTO = new AmountItemDTO();
        amountItemDTO.setName("name");
        amountItemDTO.setAmount("10");
        amountItemDTO.setExcessAmount("10");
        amountItemDTO.setEstimatedAmount("10");
        overviewSaleDTO.setGatherItems(Arrays.asList(amountItemDTO));
        overviewSaleDTO.setDiscountAmount("10");
        final DiscountAmountItemDTO discountAmountItemDTO = new DiscountAmountItemDTO();
        discountAmountItemDTO.setName("name");
        discountAmountItemDTO.setAmount("10");
        overviewSaleDTO.setDiscountItems(Arrays.asList(discountAmountItemDTO));
        overviewSaleDTO.setCheckoutStaffs(Arrays.asList("value"));
        overviewSaleDTO.setEstimatedAmount("10");
        when(mockBusinessDailyService.overviewSale(request)).thenReturn(overviewSaleDTO);

        // Configure BusinessDailyService.gatherSale(...).
        final GatherSaleDTO gatherSaleDTO = new GatherSaleDTO();
        gatherSaleDTO.setGatherName("gatherName");
        gatherSaleDTO.setConsumerAmount("10");
        gatherSaleDTO.setExcessAmount("10");
        gatherSaleDTO.setPrepaidAmount("10");
        gatherSaleDTO.setReserveAmount("10");
        gatherSaleDTO.setIsTotal(0);
        final List<GatherSaleDTO> gatherSaleDTOS = Arrays.asList(gatherSaleDTO);
        when(mockBusinessDailyService.gatherSale(request)).thenReturn(gatherSaleDTOS);

        // Configure BusinessDailyService.memberConsume(...).
        final MemberConsumeRespDTO memberConsumeRespDTO = new MemberConsumeRespDTO();
        memberConsumeRespDTO.setConsumerCount(0);
        memberConsumeRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidCount(0);
        memberConsumeRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidGiveAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidTakeInAmount(new BigDecimal("0.00"));
        final com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO amountItemDTO1 = new com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO();
        amountItemDTO1.setName("name");
        amountItemDTO1.setAmount(new BigDecimal("0.00"));
        amountItemDTO1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidItems(Arrays.asList(amountItemDTO1));
        when(mockBusinessDailyService.memberConsume(request)).thenReturn(memberConsumeRespDTO);

        // Configure BusinessDailyService.diningTypeSale(...).
        final DiningTypeSaleDTO diningTypeSaleDTO = new DiningTypeSaleDTO();
        diningTypeSaleDTO.setTypeCode("typeCode");
        diningTypeSaleDTO.setTypeName("platformName");
        diningTypeSaleDTO.setOrderCount("10");
        diningTypeSaleDTO.setGuestCount("10");
        diningTypeSaleDTO.setAmount("10");
        diningTypeSaleDTO.setOrderPrice("10");
        diningTypeSaleDTO.setGuestPrice("10");
        diningTypeSaleDTO.setSubDiningTypes(Arrays.asList(new DiningTypeSaleDTO()));
        diningTypeSaleDTO.setIsTotal(0);
        final List<DiningTypeSaleDTO> diningTypeSaleDTOS = Arrays.asList(diningTypeSaleDTO);
        when(mockBusinessDailyService.diningTypeSale(request)).thenReturn(diningTypeSaleDTOS);

        // Configure BusinessDailyService.classifySale(...).
        final CategorySaleDTO categorySaleDTO = new CategorySaleDTO();
        categorySaleDTO.setName("name");
        categorySaleDTO.setQuantum("10");
        categorySaleDTO.setAmount("10");
        categorySaleDTO.setDiscountAmount("10");
        categorySaleDTO.setIsTotal(0);
        final List<CategorySaleDTO> categorySaleDTOS = Arrays.asList(categorySaleDTO);
        when(mockBusinessDailyService.classifySale(request)).thenReturn(categorySaleDTOS);

        // Configure BusinessDailyService.goodsSale(...).
        final GoodsSaleDTO goodsSaleDTO = new GoodsSaleDTO();
        goodsSaleDTO.setName("name");
        goodsSaleDTO.setSkuName("skuName");
        goodsSaleDTO.setQuantum("10");
        goodsSaleDTO.setAmount("10");
        goodsSaleDTO.setItemType(0);
        goodsSaleDTO.setIsTotal(0);
        goodsSaleDTO.setSubs(Arrays.asList(new GoodsSaleDTO()));
        final List<GoodsSaleDTO> goodsSaleDTOS = Arrays.asList(goodsSaleDTO);
        when(mockBusinessDailyService.goodsSale(request)).thenReturn(goodsSaleDTOS);

        // Configure BusinessDailyService.attrSale(...).
        final PropStatsSaleDTO propStatsSaleDTO = new PropStatsSaleDTO();
        final PropStatsSaleDTO.PropGroup propGroup = new PropStatsSaleDTO.PropGroup();
        propGroup.setName("name");
        final PropStatsSaleDTO.PropItem propItem = new PropStatsSaleDTO.PropItem();
        propItem.setName("name");
        propItem.setQuantity("10");
        propItem.setMoney("10");
        propGroup.setPropList(Arrays.asList(propItem));
        propStatsSaleDTO.setPropGroupList(Arrays.asList(propGroup));
        when(mockBusinessDailyService.attrSale(request)).thenReturn(propStatsSaleDTO);

        // Configure BusinessDailyService.returnVegetablesSale(...).
        final ReturnSaleDTO returnSaleDTO = new ReturnSaleDTO();
        returnSaleDTO.setName("name");
        returnSaleDTO.setSkuName("skuName");
        returnSaleDTO.setQuantum("10");
        returnSaleDTO.setAmount("10");
        returnSaleDTO.setItemType(0);
        returnSaleDTO.setIsTotal(0);
        returnSaleDTO.setSubs(Arrays.asList(new ReturnSaleDTO()));
        final List<ReturnSaleDTO> returnSaleDTOS = Arrays.asList(returnSaleDTO);
        when(mockBusinessDailyService.returnVegetablesSale(request)).thenReturn(returnSaleDTOS);

        // Configure BusinessDailyService.dishGivingSale(...).
        final GiftSaleDTO giftSaleDTO = new GiftSaleDTO();
        giftSaleDTO.setName("name");
        giftSaleDTO.setSkuName("skuName");
        giftSaleDTO.setQuantum("10");
        giftSaleDTO.setAmount("10");
        giftSaleDTO.setItemType(0);
        giftSaleDTO.setIsTotal(0);
        giftSaleDTO.setSubs(Arrays.asList(new GiftSaleDTO()));
        final List<GiftSaleDTO> giftSaleDTOS = Arrays.asList(giftSaleDTO);
        when(mockBusinessDailyService.dishGivingSale(request)).thenReturn(giftSaleDTOS);

        // Configure BusinessDailyService.memberRechargeSale(...).
        final MemberRechargeSaleDTO memberRechargeSaleDTO = new MemberRechargeSaleDTO();
        memberRechargeSaleDTO.setRechargeNum("10");
        memberRechargeSaleDTO.setRechargeMemberNum("10");
        memberRechargeSaleDTO.setRechargeAmount("10");
        memberRechargeSaleDTO.setPresentAmount("10");
        memberRechargeSaleDTO.setIncomeAmount("10");
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        responsePayWayDetail.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail.setPayName("payName");
        responsePayWayDetail.setExcessAmount(new BigDecimal("0.00"));
        memberRechargeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail));
        when(mockBusinessDailyService.memberRechargeSale(request)).thenReturn(memberRechargeSaleDTO);

        // Configure BusinessDailyService.memberConsumeSale(...).
        final MemberConsumeSaleDTO memberConsumeSaleDTO = new MemberConsumeSaleDTO();
        memberConsumeSaleDTO.setConsumptionNum("10");
        memberConsumeSaleDTO.setConsumptionMemberNum("10");
        memberConsumeSaleDTO.setConsumptionAmount("10");
        final ResponsePayWayDetail responsePayWayDetail1 = new ResponsePayWayDetail();
        responsePayWayDetail1.setPayWay(0);
        responsePayWayDetail1.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail1.setPayName("payName");
        responsePayWayDetail1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail1));
        when(mockBusinessDailyService.memberConsumeSale(request)).thenReturn(memberConsumeSaleDTO);

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result<String> result = businessDailyPrintServiceImplUnderTest.print(request);

        // Verify the results
    }

    @Test
    public void testPrint9() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setBeginTime("2025-01-01");
        request.setEndTime("2025-01-10");
        request.setIsPrint(0);
        request.setType(9);

        // Configure BusinessDailyService.overviewSale(...).
        final OverviewSaleDTO overviewSaleDTO = new OverviewSaleDTO();
        overviewSaleDTO.setOrderCount("10");
        overviewSaleDTO.setGuestCount("10");
        overviewSaleDTO.setConsumerAmount("10");
        overviewSaleDTO.setGatherAmount("10");
        final AmountItemDTO amountItemDTO = new AmountItemDTO();
        amountItemDTO.setName("name");
        amountItemDTO.setAmount("10");
        amountItemDTO.setExcessAmount("10");
        amountItemDTO.setEstimatedAmount("10");
        overviewSaleDTO.setGatherItems(Arrays.asList(amountItemDTO));
        overviewSaleDTO.setDiscountAmount("10");
        final DiscountAmountItemDTO discountAmountItemDTO = new DiscountAmountItemDTO();
        discountAmountItemDTO.setName("name");
        discountAmountItemDTO.setAmount("10");
        overviewSaleDTO.setDiscountItems(Arrays.asList(discountAmountItemDTO));
        overviewSaleDTO.setCheckoutStaffs(Arrays.asList("value"));
        overviewSaleDTO.setEstimatedAmount("10");
        when(mockBusinessDailyService.overviewSale(request)).thenReturn(overviewSaleDTO);

        // Configure BusinessDailyService.gatherSale(...).
        final GatherSaleDTO gatherSaleDTO = new GatherSaleDTO();
        gatherSaleDTO.setGatherName("gatherName");
        gatherSaleDTO.setConsumerAmount("10");
        gatherSaleDTO.setExcessAmount("10");
        gatherSaleDTO.setPrepaidAmount("10");
        gatherSaleDTO.setReserveAmount("10");
        gatherSaleDTO.setIsTotal(0);
        final List<GatherSaleDTO> gatherSaleDTOS = Arrays.asList(gatherSaleDTO);
        when(mockBusinessDailyService.gatherSale(request)).thenReturn(gatherSaleDTOS);

        // Configure BusinessDailyService.memberConsume(...).
        final MemberConsumeRespDTO memberConsumeRespDTO = new MemberConsumeRespDTO();
        memberConsumeRespDTO.setConsumerCount(0);
        memberConsumeRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidCount(0);
        memberConsumeRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidGiveAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidTakeInAmount(new BigDecimal("0.00"));
        final com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO amountItemDTO1 = new com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO();
        amountItemDTO1.setName("name");
        amountItemDTO1.setAmount(new BigDecimal("0.00"));
        amountItemDTO1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidItems(Arrays.asList(amountItemDTO1));
        when(mockBusinessDailyService.memberConsume(request)).thenReturn(memberConsumeRespDTO);

        // Configure BusinessDailyService.diningTypeSale(...).
        final DiningTypeSaleDTO diningTypeSaleDTO = new DiningTypeSaleDTO();
        diningTypeSaleDTO.setTypeCode("typeCode");
        diningTypeSaleDTO.setTypeName("platformName");
        diningTypeSaleDTO.setOrderCount("10");
        diningTypeSaleDTO.setGuestCount("10");
        diningTypeSaleDTO.setAmount("10");
        diningTypeSaleDTO.setOrderPrice("10");
        diningTypeSaleDTO.setGuestPrice("10");
        diningTypeSaleDTO.setSubDiningTypes(Arrays.asList(new DiningTypeSaleDTO()));
        diningTypeSaleDTO.setIsTotal(0);
        final List<DiningTypeSaleDTO> diningTypeSaleDTOS = Arrays.asList(diningTypeSaleDTO);
        when(mockBusinessDailyService.diningTypeSale(request)).thenReturn(diningTypeSaleDTOS);

        // Configure BusinessDailyService.classifySale(...).
        final CategorySaleDTO categorySaleDTO = new CategorySaleDTO();
        categorySaleDTO.setName("name");
        categorySaleDTO.setQuantum("10");
        categorySaleDTO.setAmount("10");
        categorySaleDTO.setDiscountAmount("10");
        categorySaleDTO.setIsTotal(0);
        final List<CategorySaleDTO> categorySaleDTOS = Arrays.asList(categorySaleDTO);
        when(mockBusinessDailyService.classifySale(request)).thenReturn(categorySaleDTOS);

        // Configure BusinessDailyService.goodsSale(...).
        final GoodsSaleDTO goodsSaleDTO = new GoodsSaleDTO();
        goodsSaleDTO.setName("name");
        goodsSaleDTO.setSkuName("skuName");
        goodsSaleDTO.setQuantum("10");
        goodsSaleDTO.setAmount("10");
        goodsSaleDTO.setItemType(0);
        goodsSaleDTO.setIsTotal(0);
        goodsSaleDTO.setSubs(Arrays.asList(new GoodsSaleDTO()));
        final List<GoodsSaleDTO> goodsSaleDTOS = Arrays.asList(goodsSaleDTO);
        when(mockBusinessDailyService.goodsSale(request)).thenReturn(goodsSaleDTOS);

        // Configure BusinessDailyService.attrSale(...).
        final PropStatsSaleDTO propStatsSaleDTO = new PropStatsSaleDTO();
        final PropStatsSaleDTO.PropGroup propGroup = new PropStatsSaleDTO.PropGroup();
        propGroup.setName("name");
        final PropStatsSaleDTO.PropItem propItem = new PropStatsSaleDTO.PropItem();
        propItem.setName("name");
        propItem.setQuantity("10");
        propItem.setMoney("10");
        propGroup.setPropList(Arrays.asList(propItem));
        propStatsSaleDTO.setPropGroupList(Arrays.asList(propGroup));
        when(mockBusinessDailyService.attrSale(request)).thenReturn(propStatsSaleDTO);

        // Configure BusinessDailyService.returnVegetablesSale(...).
        final ReturnSaleDTO returnSaleDTO = new ReturnSaleDTO();
        returnSaleDTO.setName("name");
        returnSaleDTO.setSkuName("skuName");
        returnSaleDTO.setQuantum("10");
        returnSaleDTO.setAmount("10");
        returnSaleDTO.setItemType(0);
        returnSaleDTO.setIsTotal(0);
        returnSaleDTO.setSubs(Arrays.asList(new ReturnSaleDTO()));
        final List<ReturnSaleDTO> returnSaleDTOS = Arrays.asList(returnSaleDTO);
        when(mockBusinessDailyService.returnVegetablesSale(request)).thenReturn(returnSaleDTOS);

        // Configure BusinessDailyService.dishGivingSale(...).
        final GiftSaleDTO giftSaleDTO = new GiftSaleDTO();
        giftSaleDTO.setName("name");
        giftSaleDTO.setSkuName("skuName");
        giftSaleDTO.setQuantum("10");
        giftSaleDTO.setAmount("10");
        giftSaleDTO.setItemType(0);
        giftSaleDTO.setIsTotal(0);
        giftSaleDTO.setSubs(Arrays.asList(new GiftSaleDTO()));
        final List<GiftSaleDTO> giftSaleDTOS = Arrays.asList(giftSaleDTO);
        when(mockBusinessDailyService.dishGivingSale(request)).thenReturn(giftSaleDTOS);

        // Configure BusinessDailyService.memberRechargeSale(...).
        final MemberRechargeSaleDTO memberRechargeSaleDTO = new MemberRechargeSaleDTO();
        memberRechargeSaleDTO.setRechargeNum("10");
        memberRechargeSaleDTO.setRechargeMemberNum("10");
        memberRechargeSaleDTO.setRechargeAmount("10");
        memberRechargeSaleDTO.setPresentAmount("10");
        memberRechargeSaleDTO.setIncomeAmount("10");
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        responsePayWayDetail.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail.setPayName("payName");
        responsePayWayDetail.setExcessAmount(new BigDecimal("0.00"));
        memberRechargeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail));
        when(mockBusinessDailyService.memberRechargeSale(request)).thenReturn(memberRechargeSaleDTO);

        // Configure BusinessDailyService.memberConsumeSale(...).
        final MemberConsumeSaleDTO memberConsumeSaleDTO = new MemberConsumeSaleDTO();
        memberConsumeSaleDTO.setConsumptionNum("10");
        memberConsumeSaleDTO.setConsumptionMemberNum("10");
        memberConsumeSaleDTO.setConsumptionAmount("10");
        final ResponsePayWayDetail responsePayWayDetail1 = new ResponsePayWayDetail();
        responsePayWayDetail1.setPayWay(0);
        responsePayWayDetail1.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail1.setPayName("payName");
        responsePayWayDetail1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail1));
        when(mockBusinessDailyService.memberConsumeSale(request)).thenReturn(memberConsumeSaleDTO);

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result<String> result = businessDailyPrintServiceImplUnderTest.print(request);

        // Verify the results
    }

    @Test
    public void testPrint10() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setBeginTime("2025-01-01");
        request.setEndTime("2025-01-10");
        request.setIsPrint(0);
        request.setType(10);

        // Configure BusinessDailyService.overviewSale(...).
        final OverviewSaleDTO overviewSaleDTO = new OverviewSaleDTO();
        overviewSaleDTO.setOrderCount("10");
        overviewSaleDTO.setGuestCount("10");
        overviewSaleDTO.setConsumerAmount("10");
        overviewSaleDTO.setGatherAmount("10");
        final AmountItemDTO amountItemDTO = new AmountItemDTO();
        amountItemDTO.setName("name");
        amountItemDTO.setAmount("10");
        amountItemDTO.setExcessAmount("10");
        amountItemDTO.setEstimatedAmount("10");
        overviewSaleDTO.setGatherItems(Arrays.asList(amountItemDTO));
        overviewSaleDTO.setDiscountAmount("10");
        final DiscountAmountItemDTO discountAmountItemDTO = new DiscountAmountItemDTO();
        discountAmountItemDTO.setName("name");
        discountAmountItemDTO.setAmount("10");
        overviewSaleDTO.setDiscountItems(Arrays.asList(discountAmountItemDTO));
        overviewSaleDTO.setCheckoutStaffs(Arrays.asList("value"));
        overviewSaleDTO.setEstimatedAmount("10");
        when(mockBusinessDailyService.overviewSale(request)).thenReturn(overviewSaleDTO);

        // Configure BusinessDailyService.gatherSale(...).
        final GatherSaleDTO gatherSaleDTO = new GatherSaleDTO();
        gatherSaleDTO.setGatherName("gatherName");
        gatherSaleDTO.setConsumerAmount("10");
        gatherSaleDTO.setExcessAmount("10");
        gatherSaleDTO.setPrepaidAmount("10");
        gatherSaleDTO.setReserveAmount("10");
        gatherSaleDTO.setIsTotal(0);
        final List<GatherSaleDTO> gatherSaleDTOS = Arrays.asList(gatherSaleDTO);
        when(mockBusinessDailyService.gatherSale(request)).thenReturn(gatherSaleDTOS);

        // Configure BusinessDailyService.memberConsume(...).
        final MemberConsumeRespDTO memberConsumeRespDTO = new MemberConsumeRespDTO();
        memberConsumeRespDTO.setConsumerCount(0);
        memberConsumeRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidCount(0);
        memberConsumeRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidGiveAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidTakeInAmount(new BigDecimal("0.00"));
        final com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO amountItemDTO1 = new com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO();
        amountItemDTO1.setName("name");
        amountItemDTO1.setAmount(new BigDecimal("0.00"));
        amountItemDTO1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidItems(Arrays.asList(amountItemDTO1));
        when(mockBusinessDailyService.memberConsume(request)).thenReturn(memberConsumeRespDTO);

        // Configure BusinessDailyService.diningTypeSale(...).
        final DiningTypeSaleDTO diningTypeSaleDTO = new DiningTypeSaleDTO();
        diningTypeSaleDTO.setTypeCode("typeCode");
        diningTypeSaleDTO.setTypeName("platformName");
        diningTypeSaleDTO.setOrderCount("10");
        diningTypeSaleDTO.setGuestCount("10");
        diningTypeSaleDTO.setAmount("10");
        diningTypeSaleDTO.setOrderPrice("10");
        diningTypeSaleDTO.setGuestPrice("10");
        diningTypeSaleDTO.setSubDiningTypes(Arrays.asList(new DiningTypeSaleDTO()));
        diningTypeSaleDTO.setIsTotal(0);
        final List<DiningTypeSaleDTO> diningTypeSaleDTOS = Arrays.asList(diningTypeSaleDTO);
        when(mockBusinessDailyService.diningTypeSale(request)).thenReturn(diningTypeSaleDTOS);

        // Configure BusinessDailyService.classifySale(...).
        final CategorySaleDTO categorySaleDTO = new CategorySaleDTO();
        categorySaleDTO.setName("name");
        categorySaleDTO.setQuantum("10");
        categorySaleDTO.setAmount("10");
        categorySaleDTO.setDiscountAmount("10");
        categorySaleDTO.setIsTotal(0);
        final List<CategorySaleDTO> categorySaleDTOS = Arrays.asList(categorySaleDTO);
        when(mockBusinessDailyService.classifySale(request)).thenReturn(categorySaleDTOS);

        // Configure BusinessDailyService.goodsSale(...).
        final GoodsSaleDTO goodsSaleDTO = new GoodsSaleDTO();
        goodsSaleDTO.setName("name");
        goodsSaleDTO.setSkuName("skuName");
        goodsSaleDTO.setQuantum("10");
        goodsSaleDTO.setAmount("10");
        goodsSaleDTO.setItemType(0);
        goodsSaleDTO.setIsTotal(0);
        goodsSaleDTO.setSubs(Arrays.asList(new GoodsSaleDTO()));
        final List<GoodsSaleDTO> goodsSaleDTOS = Arrays.asList(goodsSaleDTO);
        when(mockBusinessDailyService.goodsSale(request)).thenReturn(goodsSaleDTOS);

        // Configure BusinessDailyService.attrSale(...).
        final PropStatsSaleDTO propStatsSaleDTO = new PropStatsSaleDTO();
        final PropStatsSaleDTO.PropGroup propGroup = new PropStatsSaleDTO.PropGroup();
        propGroup.setName("name");
        final PropStatsSaleDTO.PropItem propItem = new PropStatsSaleDTO.PropItem();
        propItem.setName("name");
        propItem.setQuantity("10");
        propItem.setMoney("10");
        propGroup.setPropList(Arrays.asList(propItem));
        propStatsSaleDTO.setPropGroupList(Arrays.asList(propGroup));
        when(mockBusinessDailyService.attrSale(request)).thenReturn(propStatsSaleDTO);

        // Configure BusinessDailyService.returnVegetablesSale(...).
        final ReturnSaleDTO returnSaleDTO = new ReturnSaleDTO();
        returnSaleDTO.setName("name");
        returnSaleDTO.setSkuName("skuName");
        returnSaleDTO.setQuantum("10");
        returnSaleDTO.setAmount("10");
        returnSaleDTO.setItemType(0);
        returnSaleDTO.setIsTotal(0);
        returnSaleDTO.setSubs(Arrays.asList(new ReturnSaleDTO()));
        final List<ReturnSaleDTO> returnSaleDTOS = Arrays.asList(returnSaleDTO);
        when(mockBusinessDailyService.returnVegetablesSale(request)).thenReturn(returnSaleDTOS);

        // Configure BusinessDailyService.dishGivingSale(...).
        final GiftSaleDTO giftSaleDTO = new GiftSaleDTO();
        giftSaleDTO.setName("name");
        giftSaleDTO.setSkuName("skuName");
        giftSaleDTO.setQuantum("10");
        giftSaleDTO.setAmount("10");
        giftSaleDTO.setItemType(0);
        giftSaleDTO.setIsTotal(0);
        giftSaleDTO.setSubs(Arrays.asList(new GiftSaleDTO()));
        final List<GiftSaleDTO> giftSaleDTOS = Arrays.asList(giftSaleDTO);
        when(mockBusinessDailyService.dishGivingSale(request)).thenReturn(giftSaleDTOS);

        // Configure BusinessDailyService.memberRechargeSale(...).
        final MemberRechargeSaleDTO memberRechargeSaleDTO = new MemberRechargeSaleDTO();
        memberRechargeSaleDTO.setRechargeNum("10");
        memberRechargeSaleDTO.setRechargeMemberNum("10");
        memberRechargeSaleDTO.setRechargeAmount("10");
        memberRechargeSaleDTO.setPresentAmount("10");
        memberRechargeSaleDTO.setIncomeAmount("10");
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        responsePayWayDetail.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail.setPayName("payName");
        responsePayWayDetail.setExcessAmount(new BigDecimal("0.00"));
        memberRechargeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail));
        when(mockBusinessDailyService.memberRechargeSale(request)).thenReturn(memberRechargeSaleDTO);

        // Configure BusinessDailyService.memberConsumeSale(...).
        final MemberConsumeSaleDTO memberConsumeSaleDTO = new MemberConsumeSaleDTO();
        memberConsumeSaleDTO.setConsumptionNum("10");
        memberConsumeSaleDTO.setConsumptionMemberNum("10");
        memberConsumeSaleDTO.setConsumptionAmount("10");
        final ResponsePayWayDetail responsePayWayDetail1 = new ResponsePayWayDetail();
        responsePayWayDetail1.setPayWay(0);
        responsePayWayDetail1.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail1.setPayName("payName");
        responsePayWayDetail1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail1));
        when(mockBusinessDailyService.memberConsumeSale(request)).thenReturn(memberConsumeSaleDTO);

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result<String> result = businessDailyPrintServiceImplUnderTest.print(request);

        // Verify the results
    }


    @Test
    public void testPrint11() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setBeginTime("2025-01-01");
        request.setEndTime("2025-01-10");
        request.setIsPrint(0);
        request.setType(11);

        // Configure BusinessDailyService.overviewSale(...).
        final OverviewSaleDTO overviewSaleDTO = new OverviewSaleDTO();
        overviewSaleDTO.setOrderCount("10");
        overviewSaleDTO.setGuestCount("10");
        overviewSaleDTO.setConsumerAmount("10");
        overviewSaleDTO.setGatherAmount("10");
        final AmountItemDTO amountItemDTO = new AmountItemDTO();
        amountItemDTO.setName("name");
        amountItemDTO.setAmount("10");
        amountItemDTO.setExcessAmount("10");
        amountItemDTO.setEstimatedAmount("10");
        overviewSaleDTO.setGatherItems(Arrays.asList(amountItemDTO));
        overviewSaleDTO.setDiscountAmount("10");
        final DiscountAmountItemDTO discountAmountItemDTO = new DiscountAmountItemDTO();
        discountAmountItemDTO.setName("name");
        discountAmountItemDTO.setAmount("10");
        overviewSaleDTO.setDiscountItems(Arrays.asList(discountAmountItemDTO));
        overviewSaleDTO.setCheckoutStaffs(Arrays.asList("value"));
        overviewSaleDTO.setEstimatedAmount("10");
        when(mockBusinessDailyService.overviewSale(request)).thenReturn(overviewSaleDTO);

        // Configure BusinessDailyService.gatherSale(...).
        final GatherSaleDTO gatherSaleDTO = new GatherSaleDTO();
        gatherSaleDTO.setGatherName("gatherName");
        gatherSaleDTO.setConsumerAmount("10");
        gatherSaleDTO.setExcessAmount("10");
        gatherSaleDTO.setPrepaidAmount("10");
        gatherSaleDTO.setReserveAmount("10");
        gatherSaleDTO.setIsTotal(0);
        final List<GatherSaleDTO> gatherSaleDTOS = Arrays.asList(gatherSaleDTO);
        when(mockBusinessDailyService.gatherSale(request)).thenReturn(gatherSaleDTOS);

        // Configure BusinessDailyService.memberConsume(...).
        final MemberConsumeRespDTO memberConsumeRespDTO = new MemberConsumeRespDTO();
        memberConsumeRespDTO.setConsumerCount(0);
        memberConsumeRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidCount(0);
        memberConsumeRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidGiveAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidTakeInAmount(new BigDecimal("0.00"));
        final com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO amountItemDTO1 = new com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO();
        amountItemDTO1.setName("name");
        amountItemDTO1.setAmount(new BigDecimal("0.00"));
        amountItemDTO1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidItems(Arrays.asList(amountItemDTO1));
        when(mockBusinessDailyService.memberConsume(request)).thenReturn(memberConsumeRespDTO);

        // Configure BusinessDailyService.diningTypeSale(...).
        final DiningTypeSaleDTO diningTypeSaleDTO = new DiningTypeSaleDTO();
        diningTypeSaleDTO.setTypeCode("typeCode");
        diningTypeSaleDTO.setTypeName("platformName");
        diningTypeSaleDTO.setOrderCount("10");
        diningTypeSaleDTO.setGuestCount("10");
        diningTypeSaleDTO.setAmount("10");
        diningTypeSaleDTO.setOrderPrice("10");
        diningTypeSaleDTO.setGuestPrice("10");
        diningTypeSaleDTO.setSubDiningTypes(Arrays.asList(new DiningTypeSaleDTO()));
        diningTypeSaleDTO.setIsTotal(0);
        final List<DiningTypeSaleDTO> diningTypeSaleDTOS = Arrays.asList(diningTypeSaleDTO);
        when(mockBusinessDailyService.diningTypeSale(request)).thenReturn(diningTypeSaleDTOS);

        // Configure BusinessDailyService.classifySale(...).
        final CategorySaleDTO categorySaleDTO = new CategorySaleDTO();
        categorySaleDTO.setName("name");
        categorySaleDTO.setQuantum("10");
        categorySaleDTO.setAmount("10");
        categorySaleDTO.setDiscountAmount("10");
        categorySaleDTO.setIsTotal(0);
        final List<CategorySaleDTO> categorySaleDTOS = Arrays.asList(categorySaleDTO);
        when(mockBusinessDailyService.classifySale(request)).thenReturn(categorySaleDTOS);

        // Configure BusinessDailyService.goodsSale(...).
        final GoodsSaleDTO goodsSaleDTO = new GoodsSaleDTO();
        goodsSaleDTO.setName("name");
        goodsSaleDTO.setSkuName("skuName");
        goodsSaleDTO.setQuantum("10");
        goodsSaleDTO.setAmount("10");
        goodsSaleDTO.setItemType(0);
        goodsSaleDTO.setIsTotal(0);
        goodsSaleDTO.setSubs(Arrays.asList(new GoodsSaleDTO()));
        final List<GoodsSaleDTO> goodsSaleDTOS = Arrays.asList(goodsSaleDTO);
        when(mockBusinessDailyService.goodsSale(request)).thenReturn(goodsSaleDTOS);

        // Configure BusinessDailyService.attrSale(...).
        final PropStatsSaleDTO propStatsSaleDTO = new PropStatsSaleDTO();
        final PropStatsSaleDTO.PropGroup propGroup = new PropStatsSaleDTO.PropGroup();
        propGroup.setName("name");
        final PropStatsSaleDTO.PropItem propItem = new PropStatsSaleDTO.PropItem();
        propItem.setName("name");
        propItem.setQuantity("10");
        propItem.setMoney("10");
        propGroup.setPropList(Arrays.asList(propItem));
        propStatsSaleDTO.setPropGroupList(Arrays.asList(propGroup));
        when(mockBusinessDailyService.attrSale(request)).thenReturn(propStatsSaleDTO);

        // Configure BusinessDailyService.returnVegetablesSale(...).
        final ReturnSaleDTO returnSaleDTO = new ReturnSaleDTO();
        returnSaleDTO.setName("name");
        returnSaleDTO.setSkuName("skuName");
        returnSaleDTO.setQuantum("10");
        returnSaleDTO.setAmount("10");
        returnSaleDTO.setItemType(0);
        returnSaleDTO.setIsTotal(0);
        returnSaleDTO.setSubs(Arrays.asList(new ReturnSaleDTO()));
        final List<ReturnSaleDTO> returnSaleDTOS = Arrays.asList(returnSaleDTO);
        when(mockBusinessDailyService.returnVegetablesSale(request)).thenReturn(returnSaleDTOS);

        // Configure BusinessDailyService.dishGivingSale(...).
        final GiftSaleDTO giftSaleDTO = new GiftSaleDTO();
        giftSaleDTO.setName("name");
        giftSaleDTO.setSkuName("skuName");
        giftSaleDTO.setQuantum("10");
        giftSaleDTO.setAmount("10");
        giftSaleDTO.setItemType(0);
        giftSaleDTO.setIsTotal(0);
        giftSaleDTO.setSubs(Arrays.asList(new GiftSaleDTO()));
        final List<GiftSaleDTO> giftSaleDTOS = Arrays.asList(giftSaleDTO);
        when(mockBusinessDailyService.dishGivingSale(request)).thenReturn(giftSaleDTOS);

        // Configure BusinessDailyService.memberRechargeSale(...).
        final MemberRechargeSaleDTO memberRechargeSaleDTO = new MemberRechargeSaleDTO();
        memberRechargeSaleDTO.setRechargeNum("10");
        memberRechargeSaleDTO.setRechargeMemberNum("10");
        memberRechargeSaleDTO.setRechargeAmount("10");
        memberRechargeSaleDTO.setPresentAmount("10");
        memberRechargeSaleDTO.setIncomeAmount("10");
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        responsePayWayDetail.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail.setPayName("payName");
        responsePayWayDetail.setExcessAmount(new BigDecimal("0.00"));
        memberRechargeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail));
        when(mockBusinessDailyService.memberRechargeSale(request)).thenReturn(memberRechargeSaleDTO);

        // Configure BusinessDailyService.memberConsumeSale(...).
        final MemberConsumeSaleDTO memberConsumeSaleDTO = new MemberConsumeSaleDTO();
        memberConsumeSaleDTO.setConsumptionNum("10");
        memberConsumeSaleDTO.setConsumptionMemberNum("10");
        memberConsumeSaleDTO.setConsumptionAmount("10");
        final ResponsePayWayDetail responsePayWayDetail1 = new ResponsePayWayDetail();
        responsePayWayDetail1.setPayWay(0);
        responsePayWayDetail1.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail1.setPayName("payName");
        responsePayWayDetail1.setExcessAmount(new BigDecimal("0.00"));
        memberConsumeSaleDTO.setPayWayDetailList(Arrays.asList(responsePayWayDetail1));
        when(mockBusinessDailyService.memberConsumeSale(request)).thenReturn(memberConsumeSaleDTO);

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result<String> result = businessDailyPrintServiceImplUnderTest.print(request);

        // Verify the results
    }

    @Test
    public void testPrint_BusinessDailyServiceGatherSaleReturnsNoItems() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setBeginTime("2025-01-01");
        request.setEndTime("2025-01-10");
        request.setIsPrint(0);
        request.setType(2);

        // Configure BusinessDailyService.gatherSale(...).
        when(mockBusinessDailyService.gatherSale(request)).thenReturn(Collections.emptyList());

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result<String> result = businessDailyPrintServiceImplUnderTest.print(request);

        // Verify the results
    }

    @Test
    public void testPrint_BusinessDailyServiceDiningTypeSaleReturnsNoItems() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setBeginTime("2025-01-01");
        request.setEndTime("2025-01-10");
        request.setIsPrint(0);
        request.setType(4);

        // Configure BusinessDailyService.diningTypeSale(...).
        when(mockBusinessDailyService.diningTypeSale(request)).thenReturn(Collections.emptyList());

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result<String> result = businessDailyPrintServiceImplUnderTest.print(request);

        // Verify the results
    }

    @Test
    public void testPrint_BusinessDailyServiceClassifySaleReturnsNoItems() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setBeginTime("2025-01-01");
        request.setEndTime("2025-01-10");
        request.setIsPrint(0);
        request.setType(5);

        // Configure BusinessDailyService.classifySale(...).
        when(mockBusinessDailyService.classifySale(request)).thenReturn(Collections.emptyList());

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result<String> result = businessDailyPrintServiceImplUnderTest.print(request);

        // Verify the results
    }

    @Test
    public void testPrint_BusinessDailyServiceGoodsSaleReturnsNoItems() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setBeginTime("2025-01-01");
        request.setEndTime("2025-01-10");
        request.setIsPrint(0);
        request.setType(6);

        // Configure BusinessDailyService.goodsSale(...).
        when(mockBusinessDailyService.goodsSale(request)).thenReturn(Collections.emptyList());

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result<String> result = businessDailyPrintServiceImplUnderTest.print(request);

        // Verify the results
    }

    @Test
    public void testPrint_BusinessDailyServiceReturnVegetablesSaleReturnsNoItems() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setBeginTime("2025-01-01");
        request.setEndTime("2025-01-10");
        request.setIsPrint(0);
        request.setType(8);

        // Configure BusinessDailyService.returnVegetablesSale(...).
        when(mockBusinessDailyService.returnVegetablesSale(request)).thenReturn(Collections.emptyList());

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result<String> result = businessDailyPrintServiceImplUnderTest.print(request);

        // Verify the results
    }

    @Test
    public void testPrint_BusinessDailyServiceDishGivingSaleReturnsNoItems() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setBeginTime("2025-01-01");
        request.setEndTime("2025-01-10");
        request.setIsPrint(0);
        request.setType(9);

        // Configure BusinessDailyService.dishGivingSale(...).
        when(mockBusinessDailyService.dishGivingSale(request)).thenReturn(Collections.emptyList());

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result<String> result = businessDailyPrintServiceImplUnderTest.print(request);

        // Verify the results
    }
}
