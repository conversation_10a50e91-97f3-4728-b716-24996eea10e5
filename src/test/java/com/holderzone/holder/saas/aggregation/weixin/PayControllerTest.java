package com.holderzone.holder.saas.aggregation.weixin;

import cn.hutool.core.lang.Assert;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.entity.dto.DealMemberPayReqDTO;
import com.holderzone.holder.saas.aggregation.weixin.service.PayService;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.dto.pay.AggPayRespDTO;
import com.holderzone.saas.store.dto.weixin.deal.MemberPayReusltRespDTO;
import com.holderzone.saas.store.dto.weixin.deal.WeChatH5PayReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.weixin.WxAppletMemberPayTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;

import static org.mockito.Mockito.mock;


/**
 * 微信小程序支付
 */
@Slf4j
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@RunWith(MockitoJUnitRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@SpringBootTest(classes = HolderSaasAggregationWeixinApplication.class)
public class PayControllerTest {

    public static final String USERINFO = "{\"enterpriseGuid\":\"2009281531195930006\"," +
            "\"enterpriseNo\":\"********\"," +
            "\"enterpriseName\":\"赵氏企业\"," +
            "\"commercialActivities\":\"10001\"," +
            "\"storeGuid\":\"2106221850429620006\"," +
            "\"storeNo\":\"5796807\"," +
            "\"storeName\":\"交子大道测试门店\"," +
            "\"deviceGuid\":\"2304251132214660007\"," +
            "\"userGuid\":\"6869112864859226113\"," +
            "\"name\":\"zhouzixiang\",\"tel\":\"***********\"," +
            "\"account\":\"967452\"," +
            "\"allianceId\":null," +
            "\"isAlliance\":false," +
            "\"operSubjectGuid\":\"2010121440477930009\"," +
            "\"multiMemberStatus\":false}";

    public static final String RESPONSE = "response:";

    public static final String ENTERPRISE_GUID = "2009281531195930006";

    public static final String OPER_SUBJECT_GUID = "2010121440477930009";

    public static final String APP_ID = "wx4b788f7d5151d9b5";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    private void preHandler() {
        UserContextUtils.put(JacksonUtils.writeValueAsString(USERINFO));
        WxMemberSessionDTO memberSession = new WxMemberSessionDTO();
        memberSession.setEnterpriseGuid(ENTERPRISE_GUID);
        memberSession.setOperSubjectGuid(OPER_SUBJECT_GUID);
        memberSession.setForbidden(false);
        memberSession.setPhoneNum("***********");
        memberSession.setDiningTableGuid("6912937312783433728");
        memberSession.setDiningTableCode("红包01");
        memberSession.setAreaGuid("6912937203584729088");
        memberSession.setAreaName("红包");
        memberSession.setBrandGuid("6716248408689999872");
        memberSession.setBrandName("企业品牌");
        memberSession.setStoreGuid("2106221850429620006");
        memberSession.setStoreName("交子大道测试门店");
        WeixinUserThreadLocal.set(memberSession);
    }

    /**
     * 小程序微信调起支付
     */
    @Test
    public void appletWechat() {
        preHandler();
        WeChatH5PayReqDTO reqDTO = new WeChatH5PayReqDTO();
        reqDTO.setOrderGuid("7155370363788984320");
        reqDTO.setAppId(APP_ID);
        reqDTO.setPayAmount(new BigDecimal("0.02"));
        PayService payService = mock(PayService.class);
        HttpServletRequest httpServletRequest = new MockHttpServletRequest();
        httpServletRequest.setAttribute("X-Forwarded-For", APP_ID);
        WxPayRespDTO wxPayRespDTO = payService.aggPay(reqDTO, httpServletRequest);
        log.info(RESPONSE + wxPayRespDTO);
        Assert.equals(wxPayRespDTO.getCouldPay(), 1, "未开启线上支付");
        AggPayRespDTO aggPayRespDTO = wxPayRespDTO.getResult();
        Assert.notNull(aggPayRespDTO, "微信支付返回参数为空");
        Assert.equals(aggPayRespDTO.getCode(), 10000, "预下单失败,code!=10000,失败原因:" + aggPayRespDTO.getMsg());
        Assert.notNull(aggPayRespDTO, "微信支付返回参数为空");
        Assert.notBlank(aggPayRespDTO.getOrderGuid(), "微信支付返回订单guid不能为空");
        Assert.notBlank(aggPayRespDTO.getPayGuid(), "微信支付返回支付guid不能为空");
    }


    /**
     * 小程序收益余额支付
     */
    @Test
    public void appletMemberIncomeBalance() {
        preHandler();
        DealMemberPayReqDTO reqDTO = new DealMemberPayReqDTO();
        reqDTO.setOrderGuid("7155376929451802624");
        reqDTO.setPayAmount(new BigDecimal("10.00"));
        reqDTO.setPayType(WxAppletMemberPayTypeEnum.INCOME_AMOUNT.getType());
        PayService payService = mock(PayService.class);
        MemberPayReusltRespDTO memberPayReusltRespDTO = payService.memberPay(reqDTO);
        log.info(RESPONSE + memberPayReusltRespDTO);
        Assert.equals(memberPayReusltRespDTO.getResult(), 0,
                "会员收益余额支付失败,失败原因:" + memberPayReusltRespDTO.getErrorMsg());
    }


    /**
     * 小程序储值余额支付
     */
    @Test
    public void appletMemberStoredBalance() {
        preHandler();
        DealMemberPayReqDTO reqDTO = new DealMemberPayReqDTO();
        reqDTO.setOrderGuid("7155376929451802625");
        reqDTO.setPayAmount(new BigDecimal("100.00"));
        reqDTO.setPayType(WxAppletMemberPayTypeEnum.STORED_AMOUNT.getType());
        reqDTO.setMemberPassWord("8un97U7+8OJk6/fAQuhSrQ==");
        PayService payService = mock(PayService.class);
        MemberPayReusltRespDTO memberPayReusltRespDTO = payService.memberPay(reqDTO);
        log.info(RESPONSE + memberPayReusltRespDTO);
        Assert.equals(memberPayReusltRespDTO.getResult(), 0,
                "会员储值余额支付失败,失败原因:" + memberPayReusltRespDTO.getErrorMsg());
    }


    /**
     * h5 会员支付
     */
    @Test
    public void h5MemberPay() {
        preHandler();
        DealMemberPayReqDTO reqDTO = new DealMemberPayReqDTO();
        reqDTO.setOrderGuid("7155376929451802629");
        reqDTO.setMemberPassWord("8un97U7+8OJk6/fAQuhSrQ==");
        PayService payService = mock(PayService.class);
        MemberPayReusltRespDTO memberPayReusltRespDTO = null;
        try {
            memberPayReusltRespDTO = payService.memberPay(reqDTO);
        } catch (Exception e) {
            throw new BusinessException("会员支付异常", e);
        }
        log.info(RESPONSE + memberPayReusltRespDTO);
        Assert.equals(memberPayReusltRespDTO.getResult(), 0,
                "h5会员支付失败,失败原因:" + memberPayReusltRespDTO.getErrorMsg());
    }


    /**
     * h5微信支付
     */
    @Test
    public void h5WechatPay() {
        preHandler();
        WeChatH5PayReqDTO reqDTO = new WeChatH5PayReqDTO();
        reqDTO.setOrderGuid("7155376929451802629");
        reqDTO.setPayAmount(new BigDecimal("0.02"));
        reqDTO.setDeviceType(BaseDeviceTypeEnum.WECHAT.getCode());
        PayService payService = mock(PayService.class);
        WxPayRespDTO wxPayRespDTO = payService.aggPay(reqDTO, null);
        log.info(RESPONSE + wxPayRespDTO);
        Assert.equals(wxPayRespDTO.getCouldPay(), 1, "H5未开启线上支付");
        AggPayRespDTO aggPayRespDTO = wxPayRespDTO.getResult();
        Assert.notNull(aggPayRespDTO, "H5微信支付返回参数为空");
        Assert.equals(aggPayRespDTO.getCode(), 10000,
                "H5预下单失败,code!=10000,失败原因:" + aggPayRespDTO.getMsg());
        Assert.notNull(aggPayRespDTO, "H5微信支付返回参数为空");
        Assert.notBlank(aggPayRespDTO.getOrderGuid(), "H5微信支付返回订单guid不能为空");
        Assert.notBlank(aggPayRespDTO.getPayGuid(), "H5微信支付返回支付guid不能为空");
    }
}