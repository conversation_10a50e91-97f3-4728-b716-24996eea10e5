package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import org.junit.Before;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class BasePageTest {

    private BasePage basePageUnderTest;

    @Before
    public void setUp() throws Exception {
        basePageUnderTest = new BasePage();
    }

    @Test
    public void testGetBeginIndex() {
        assertThat(basePageUnderTest.getBeginIndex()).isEqualTo(0);
    }

    @Test
    public void testPageSizeGetterAndSetter() {
        final Integer pageSize = 0;
        basePageUnderTest.setPageSize(pageSize);
        assertThat(basePageUnderTest.getPageSize()).isEqualTo(pageSize);
    }

    @Test
    public void testPageIndexGetterAndSetter() {
        final Integer pageIndex = 0;
        basePageUnderTest.setPageIndex(pageIndex);
        assertThat(basePageUnderTest.getPageIndex()).isEqualTo(pageIndex);
    }

    @Test
    public void testSortTypeGetterAndSetter() {
        final Integer sortType = 0;
        basePageUnderTest.setSortType(sortType);
        assertThat(basePageUnderTest.getSortType()).isEqualTo(sortType);
    }

    @Test
    public void testOrderByGetterAndSetter() {
        final String orderBy = "orderBy";
        basePageUnderTest.setOrderBy(orderBy);
        assertThat(basePageUnderTest.getOrderBy()).isEqualTo(orderBy);
    }

    @Test
    public void testEquals() {
        assertThat(basePageUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() {
        assertThat(basePageUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() {
        assertThat(basePageUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() {
        assertThat(basePageUnderTest.toString()).isEqualTo("result");
    }
}
