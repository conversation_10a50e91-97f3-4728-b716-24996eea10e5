package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class WxStoreListRespDTOTest {

    private WxStoreListRespDTO wxStoreListRespDTOUnderTest;

    @Before
    public void setUp() throws Exception {
        wxStoreListRespDTOUnderTest = new WxStoreListRespDTO();
    }

    @Test
    public void testStoreListGetterAndSetter() {
        final List<WxStoreDTO> storeList = Arrays.asList(new WxStoreDTO());
        wxStoreListRespDTOUnderTest.setStoreList(storeList);
        assertThat(wxStoreListRespDTOUnderTest.getStoreList()).isEqualTo(storeList);
    }

    @Test
    public void testEquals() throws Exception {
        assertThat(wxStoreListRespDTOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() throws Exception {
        assertThat(wxStoreListRespDTOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() throws Exception {
        assertThat(wxStoreListRespDTOUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() throws Exception {
        assertThat(wxStoreListRespDTOUnderTest.toString()).isEqualTo("result");
    }
}
