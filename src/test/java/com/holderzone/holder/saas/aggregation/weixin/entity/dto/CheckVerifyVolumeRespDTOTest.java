package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import org.junit.Before;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class CheckVerifyVolumeRespDTOTest {

    private CheckVerifyVolumeRespDTO checkVerifyVolumeRespDTOUnderTest;

    @Before
    public void setUp() throws Exception {
        checkVerifyVolumeRespDTOUnderTest = new CheckVerifyVolumeRespDTO(0, "meg");
    }

    @Test
    public void testCodeGetterAndSetter() {
        final Integer code = 0;
        checkVerifyVolumeRespDTOUnderTest.setCode(code);
        assertThat(checkVerifyVolumeRespDTOUnderTest.getCode()).isEqualTo(code);
    }

    @Test
    public void testMegGetterAndSetter() {
        final String meg = "meg";
        checkVerifyVolumeRespDTOUnderTest.setMeg(meg);
        assertThat(checkVerifyVolumeRespDTOUnderTest.getMeg()).isEqualTo(meg);
    }

    @Test
    public void testEquals() {
        assertThat(checkVerifyVolumeRespDTOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() {
        assertThat(checkVerifyVolumeRespDTOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() {
        assertThat(checkVerifyVolumeRespDTOUnderTest.hashCode()).isEqualTo(111464);
    }

    @Test
    public void testToString() {
        assertThat(checkVerifyVolumeRespDTOUnderTest.toString()).isEqualTo("CheckVerifyVolumeRespDTO(code=0, meg=meg)");
    }
}
