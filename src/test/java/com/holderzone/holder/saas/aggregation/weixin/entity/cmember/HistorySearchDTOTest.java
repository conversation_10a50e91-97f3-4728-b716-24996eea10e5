package com.holderzone.holder.saas.aggregation.weixin.entity.cmember;

import org.junit.Before;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class HistorySearchDTOTest {

    private HistorySearchDTO historySearchDTOUnderTest;

    @Before
    public void setUp() throws Exception {
        historySearchDTOUnderTest = new HistorySearchDTO();
    }

    @Test
    public void testCacheKey() {
        assertThat(historySearchDTOUnderTest.cacheKey()).isEqualTo("result");
    }

    @Test
    public void testVerifyParams() {
        // Setup
        // Run the test
        historySearchDTOUnderTest.verifyParams();

        // Verify the results
    }

    @Test
    public void testKeyGetterAndSetter() {
        final String key = "key";
        historySearchDTOUnderTest.setKey(key);
        assertThat(historySearchDTOUnderTest.getKey()).isEqualTo(key);
    }

    @Test
    public void testContentGetterAndSetter() {
        final String content = "content";
        historySearchDTOUnderTest.setContent(content);
        assertThat(historySearchDTOUnderTest.getContent()).isEqualTo(content);
    }

    @Test
    public void testTypeGetterAndSetter() {
        final int type = 0;
        historySearchDTOUnderTest.setType(type);
        assertThat(historySearchDTOUnderTest.getType()).isEqualTo(type);
    }

    @Test
    public void testEquals() throws Exception {
        assertThat(historySearchDTOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() throws Exception {
        assertThat(historySearchDTOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() throws Exception {
        assertThat(historySearchDTOUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() throws Exception {
        assertThat(historySearchDTOUnderTest.toString()).isEqualTo("result");
    }
}
