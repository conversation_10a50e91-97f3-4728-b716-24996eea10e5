package com.holderzone.holder.saas.aggregation.app.utils;

import org.apache.commons.io.input.BrokenInputStream;
import org.apache.commons.io.input.NullInputStream;
import org.junit.Test;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

import static org.junit.Assert.assertArrayEquals;
import static org.junit.Assert.assertEquals;

public class DigestUtilsTest {

    @Test
    public void testMd51() {
        assertArrayEquals("content".getBytes(), DigestUtils.md5("content".getBytes()));
    }

    @Test
    public void testMd52() {
        assertArrayEquals("content".getBytes(), DigestUtils.md5("content".getBytes(), "content".getBytes(), 0));
    }

    @Test
    public void testSha11() {
        assertArrayEquals("content".getBytes(), DigestUtils.sha1("content".getBytes()));
    }

    @Test
    public void testSha2561() {
        assertEquals("result", DigestUtils.sha256("str"));
    }

    @Test
    public void testSha2562() {
        assertArrayEquals("content".getBytes(), DigestUtils.sha256("content".getBytes()));
    }

    @Test
    public void testSha12() {
        assertArrayEquals("content".getBytes(), DigestUtils.sha1("content".getBytes(), "content".getBytes()));
    }

    @Test
    public void testSha13() {
        assertArrayEquals("content".getBytes(), DigestUtils.sha1("content".getBytes(), "content".getBytes(), 0));
    }

    @Test
    public void testGenerateSalt() {
        assertArrayEquals("content".getBytes(), DigestUtils.generateSalt(0));
    }

    @Test
    public void testMd53() throws Exception {
        // Setup
        final InputStream input = new ByteArrayInputStream("content".getBytes());

        // Run the test
        final byte[] result = DigestUtils.md5(input);

        // Verify the results
        assertArrayEquals("content".getBytes(), result);
    }

    @Test
    public void testMd53_EmptyInput() throws Exception {
        // Setup
        final InputStream input = new NullInputStream(0L);

        // Run the test
        final byte[] result = DigestUtils.md5(input);

        // Verify the results
        assertArrayEquals("content".getBytes(), result);
    }

    @Test(expected = IOException.class)
    public void testMd53_BrokenInput() throws Exception {
        // Setup
        final InputStream input = new BrokenInputStream();

        // Run the test
        DigestUtils.md5(input);
    }

    @Test
    public void testSha14() throws Exception {
        // Setup
        final InputStream input = new ByteArrayInputStream("content".getBytes());

        // Run the test
        final byte[] result = DigestUtils.sha1(input);

        // Verify the results
        assertArrayEquals("content".getBytes(), result);
    }

    @Test
    public void testSha14_EmptyInput() throws Exception {
        // Setup
        final InputStream input = new NullInputStream(0L);

        // Run the test
        final byte[] result = DigestUtils.sha1(input);

        // Verify the results
        assertArrayEquals("content".getBytes(), result);
    }

    @Test(expected = IOException.class)
    public void testSha14_BrokenInput() throws Exception {
        // Setup
        final InputStream input = new BrokenInputStream();

        // Run the test
        DigestUtils.sha1(input);
    }
}
