package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInOrderClientService;
import com.holderzone.saas.store.dto.boss.req.BossOrderItemQueryDTO;
import com.holderzone.saas.store.dto.boss.resp.BossOrderDetailRespDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.OrderDetailQueryDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.trade.OrderInfoRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderDetailsServiceImplTest {

    @Mock
    private DineInOrderClientService mockDineInOrderClientService;

    private OrderDetailsServiceImpl orderDetailsServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        orderDetailsServiceImplUnderTest = new OrderDetailsServiceImpl(mockDineInOrderClientService);
    }

    @Test
    public void testSensitization() {
        // Setup
        final OrderInfoRespDTO orderInfoRespDTO = new OrderInfoRespDTO();
        orderInfoRespDTO.setGuid("c836c8d5-ef4d-4669-baef-da60c8212e6f");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("dccc4d48-d798-4fbe-bffd-2e711237e118");
        dineInItemDTO.setOrderGuid("orderGuid");
        dineInItemDTO.setOriginalOrderItemGuid(0L);
        orderInfoRespDTO.setItemList(Arrays.asList(dineInItemDTO));

        final OrderInfoRespDTO expectedResult = new OrderInfoRespDTO();
        expectedResult.setGuid("c836c8d5-ef4d-4669-baef-da60c8212e6f");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("dccc4d48-d798-4fbe-bffd-2e711237e118");
        dineInItemDTO1.setOrderGuid("orderGuid");
        dineInItemDTO1.setOriginalOrderItemGuid(0L);
        expectedResult.setItemList(Arrays.asList(dineInItemDTO1));

        // Run the test
        final OrderInfoRespDTO result = orderDetailsServiceImplUnderTest.sensitization(orderInfoRespDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryOrderItemInfo() {
        // Setup
        final BossOrderItemQueryDTO queryDTO = new BossOrderItemQueryDTO();
        queryDTO.setOrderGuid("data");
        queryDTO.setEnterpriseGuid("enterpriseGuid");

        final BossOrderDetailRespDTO expectedResult = new BossOrderDetailRespDTO();
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setOrderNo("orderNo");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setUpperState(0);

        // Configure DineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("253b1d4f-2449-4dc2-9bda-d0a3ab5276b2");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("dccc4d48-d798-4fbe-bffd-2e711237e118");
        dineInItemDTO.setOrderGuid("orderGuid");
        dineinOrderDetailRespDTO.setReserveItems(Arrays.asList(dineInItemDTO));
        OrderDetailQueryDTO queryDTO1 = new OrderDetailQueryDTO();
        queryDTO1.setData("data");
        when(mockDineInOrderClientService.getOrderDetail(queryDTO1)).thenReturn(dineinOrderDetailRespDTO);

        // Run the test
        final BossOrderDetailRespDTO result = orderDetailsServiceImplUnderTest.queryOrderItemInfo(queryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
