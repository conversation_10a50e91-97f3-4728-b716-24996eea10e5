package com.holderzone.holder.saas.aggregation.weixin.controller.cmember.account;

import com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel;
import com.holderzone.holder.saas.aggregation.weixin.entity.dto.RequestTravelDetail;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.member.wechat.dto.card.RequestCardRightDetails;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseCardRight;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseCardRightDetail;
import com.holderzone.holder.saas.member.wechat.dto.member.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(HsmMemberInfoCardController.class)
public class HsmMemberInfoCardControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private HsaBaseClientService mockHsaBaseClientService;

    @Test
    public void testGetMemberCard() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getMemberCard(...).
        final ResponseMemberCardAll responseMemberCardAll = new ResponseMemberCardAll();
        final ResponseMemberCardListOwned responseMemberCardListOwned = new ResponseMemberCardListOwned();
        responseMemberCardListOwned.setCardQRCode("cardQRCode");
        responseMemberCardListOwned.setCardGuid("cardGuid");
        responseMemberCardListOwned.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCardListOwned.setSystemManagementGuid("systemManagementGuid");
        responseMemberCardAll.setOpenedCardList(Arrays.asList(responseMemberCardListOwned));
        final ResponseModel<ResponseMemberCardAll> responseMemberCardAllResponseModel = new ResponseModel<>(
                responseMemberCardAll);
        final RequestQueryMemberCardList requestQueryMemberCardList = new RequestQueryMemberCardList();
        requestQueryMemberCardList.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberCardList.setEnterpriseGuid("enterpriseGuid");
        requestQueryMemberCardList.setBrandGuid("brandGuid");
        requestQueryMemberCardList.setStoreGuid("storeGuid");
        when(mockHsaBaseClientService.getMemberCard(requestQueryMemberCardList))
                .thenReturn(responseMemberCardAllResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hsmcw/card/getMemberCard")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetMemberCardSummaryInfo() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getMemberCardSummaryInfo(...).
        final ResponseMemberCardSummary responseMemberCardSummary = new ResponseMemberCardSummary();
        responseMemberCardSummary.setCardQRCode("cardQRCode");
        responseMemberCardSummary.setCardGuid("cardGuid");
        responseMemberCardSummary.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCardSummary.setSystemManagementGuid("systemManagementGuid");
        responseMemberCardSummary.setCardType(0);
        final ResponseModel<ResponseMemberCardSummary> responseMemberCardSummaryResponseModel = new ResponseModel<>(
                responseMemberCardSummary);
        final RequestMemberCardSummaryQuery requestMemberCardSummaryQuery = new RequestMemberCardSummaryQuery();
        requestMemberCardSummaryQuery.setMemberInfoCardGuid("memberInfoCardGuid");
        requestMemberCardSummaryQuery.setCardGuid("cardGuid");
        requestMemberCardSummaryQuery.setLevelGuid("levelGuid");
        when(mockHsaBaseClientService.getMemberCardSummaryInfo(requestMemberCardSummaryQuery))
                .thenReturn(responseMemberCardSummaryResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hsmcw/card/getMemberCardSummaryInfo")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testOpenMemberCard() throws Exception {
        // Setup
        // Configure HsaBaseClientService.openMemberCard(...).
        final RequestMemberCardOpen requestMemberCardOpen = new RequestMemberCardOpen();
        requestMemberCardOpen.setMemberInfoGuid("memberInfoGuid");
        requestMemberCardOpen.setCardGuid("cardGuid");
        when(mockHsaBaseClientService.openMemberCard(requestMemberCardOpen)).thenReturn(new ResponseModel<>(false));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hsmcw/card/openMemberCard")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetFundingTravelDetails() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getFundingTravelDetails(...).
        final ResponseTravelDetails responseTravelDetails = new ResponseTravelDetails();
        responseTravelDetails.setValue("value");
        responseTravelDetails.setDetails(Arrays.asList());
        responseTravelDetails.setIsEnable(false);
        responseTravelDetails.setFreezeMoney(new BigDecimal("0.00"));
        final ResponseModel<ResponseTravelDetails> responseTravelDetailsResponseModel = new ResponseModel<>(
                responseTravelDetails);
        final RequestTravelDetail requestTravelDetail = new RequestTravelDetail();
        requestTravelDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        when(mockHsaBaseClientService.getFundingTravelDetails(requestTravelDetail))
                .thenReturn(responseTravelDetailsResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hsmcw/card/getFundingTravelDetails")
                        .param("memberInfoCardGuid", "memberInfoCardGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetGrowthValueDetails() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getGrowthValueDetails(...).
        final ResponseTravelDetails responseTravelDetails = new ResponseTravelDetails();
        responseTravelDetails.setValue("value");
        responseTravelDetails.setDetails(Arrays.asList());
        responseTravelDetails.setIsEnable(false);
        responseTravelDetails.setFreezeMoney(new BigDecimal("0.00"));
        final ResponseModel<ResponseTravelDetails> responseTravelDetailsResponseModel = new ResponseModel<>(
                responseTravelDetails);
        final RequestTravelDetail requestTravelDetail = new RequestTravelDetail();
        requestTravelDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        when(mockHsaBaseClientService.getGrowthValueDetails(requestTravelDetail))
                .thenReturn(responseTravelDetailsResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hsmcw/card/getGrowthValueDetails")
                        .param("memberInfoCardGuid", "memberInfoCardGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetIntegralTravelDetails() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getMemberCardSummaryInfo(...).
        final ResponseMemberCardSummary responseMemberCardSummary = new ResponseMemberCardSummary();
        responseMemberCardSummary.setCardQRCode("cardQRCode");
        responseMemberCardSummary.setCardGuid("cardGuid");
        responseMemberCardSummary.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCardSummary.setSystemManagementGuid("systemManagementGuid");
        responseMemberCardSummary.setCardType(0);
        final ResponseModel<ResponseMemberCardSummary> responseMemberCardSummaryResponseModel = new ResponseModel<>(
                responseMemberCardSummary);
        final RequestMemberCardSummaryQuery requestMemberCardSummaryQuery = new RequestMemberCardSummaryQuery();
        requestMemberCardSummaryQuery.setMemberInfoCardGuid("memberInfoCardGuid");
        requestMemberCardSummaryQuery.setCardGuid("cardGuid");
        requestMemberCardSummaryQuery.setLevelGuid("levelGuid");
        when(mockHsaBaseClientService.getMemberCardSummaryInfo(requestMemberCardSummaryQuery))
                .thenReturn(responseMemberCardSummaryResponseModel);

        // Configure HsaBaseClientService.getIntegralTravelDetails(...).
        final ResponseTravelDetails responseTravelDetails = new ResponseTravelDetails();
        responseTravelDetails.setValue("value");
        responseTravelDetails.setDetails(Arrays.asList());
        responseTravelDetails.setIsEnable(false);
        responseTravelDetails.setFreezeMoney(new BigDecimal("0.00"));
        final ResponseModel<ResponseTravelDetails> responseTravelDetailsResponseModel = new ResponseModel<>(
                responseTravelDetails);
        final RequestTravelDetail requestTravelDetail = new RequestTravelDetail();
        requestTravelDetail.setMemberInfoCardGuid("memberInfoCardGuid");
        when(mockHsaBaseClientService.getIntegralTravelDetails(requestTravelDetail))
                .thenReturn(responseTravelDetailsResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hsmcw/card/getIntegralTravelDetails")
                        .param("memberInfoCardGuid", "memberInfoCardGuid")
                        .param("cardGuid", "cardGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetCardRightDetails() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getCardRightDetails(...).
        final ResponseCardRight responseCardRight = new ResponseCardRight();
        responseCardRight.setName("name");
        final ResponseCardRightDetail responseCardRightDetail = new ResponseCardRightDetail();
        responseCardRightDetail.setRightsGuid("rightsGuid");
        responseCardRightDetail.setRightsName("rightsName");
        responseCardRightDetail.setGainCondition("gainCondition");
        responseCardRight.setList(Arrays.asList(responseCardRightDetail));
        final ResponseModel<List<ResponseCardRight>> listResponseModel = new ResponseModel<>(
                Arrays.asList(responseCardRight));
        final RequestCardRightDetails requestCardRightDetails = new RequestCardRightDetails();
        requestCardRightDetails.setMemberInfoCardGuid("memberInfoCardGuid");
        requestCardRightDetails.setCardGuid("cardGuid");
        requestCardRightDetails.setCardType(0);
        requestCardRightDetails.setCardLevelGuid("cardLevelGuid");
        when(mockHsaBaseClientService.getCardRightDetails(requestCardRightDetails)).thenReturn(listResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hsmcw/card/getCardRightDetails")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetCardRightDetails_HsaBaseClientServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getCardRightDetails(...).
        final ResponseModel<List<ResponseCardRight>> listResponseModel = new ResponseModel<>(Collections.emptyList());
        final RequestCardRightDetails requestCardRightDetails = new RequestCardRightDetails();
        requestCardRightDetails.setMemberInfoCardGuid("memberInfoCardGuid");
        requestCardRightDetails.setCardGuid("cardGuid");
        requestCardRightDetails.setCardType(0);
        requestCardRightDetails.setCardLevelGuid("cardLevelGuid");
        when(mockHsaBaseClientService.getCardRightDetails(requestCardRightDetails)).thenReturn(listResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hsmcw/card/getCardRightDetails")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testGetCardSimpleRechargeRule() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getCardSimpleRechargeRule(...).
        final ResponseCardRechargeRuleSummary responseCardRechargeRuleSummary = new ResponseCardRechargeRuleSummary();
        final ResponseCardRechargeRule responseCardRechargeRule = new ResponseCardRechargeRule();
        responseCardRechargeRule.setRechargeMoney("rechargeMoney");
        responseCardRechargeRule.setRechargeRule("rechargeRule");
        responseCardRechargeRule.setRechargeGiftNum(0);
        responseCardRechargeRule.setIsGiftMoney(false);
        responseCardRechargeRuleSummary.setCurrentLevelRuleList(Arrays.asList(responseCardRechargeRule));
        final ResponseModel<ResponseCardRechargeRuleSummary> responseCardRechargeRuleSummaryResponseModel = new ResponseModel<>(
                responseCardRechargeRuleSummary);
        final RequestCardRechargeRule cardRechargeRuleReqDTO = new RequestCardRechargeRule();
        cardRechargeRuleReqDTO.setCardGuid("cardGuid");
        cardRechargeRuleReqDTO.setStoreGuid("storeGuid");
        cardRechargeRuleReqDTO.setCardLevelGuid("cardLevelGuid");
        cardRechargeRuleReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        when(mockHsaBaseClientService.getCardSimpleRechargeRule(cardRechargeRuleReqDTO))
                .thenReturn(responseCardRechargeRuleSummaryResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hsmcw/card/getCardSimpleRechargeRule")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetFundingRule() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getFundingRule(...).
        final ResponseFundingRule responseFundingRule = new ResponseFundingRule();
        responseFundingRule.setRechargeType(0);
        responseFundingRule.setLadderRuleList(Arrays.asList("value"));
        responseFundingRule.setApplicableShopsDesc("applicableShopsDesc");
        responseFundingRule.setUseMoneyLimitDesc("useMoneyLimitDesc");
        final CardRechargeRule cardRechargeRule = new CardRechargeRule();
        responseFundingRule.setAllCardRechargeRules(Arrays.asList(cardRechargeRule));
        final ResponseModel<ResponseFundingRule> responseFundingRuleResponseModel = new ResponseModel<>(
                responseFundingRule);
        when(mockHsaBaseClientService.getFundingRule("memberInfoCardGuid", "cardGuid"))
                .thenReturn(responseFundingRuleResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hsmcw/card/getFundingRule")
                        .param("memberInfoCardGuid", "memberInfoCardGuid")
                        .param("cardGuid", "cardGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetGrowthValueRule() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getGrowthValueRule(...).
        final ResponseGrowthValueRule responseGrowthValueRule = new ResponseGrowthValueRule();
        responseGrowthValueRule.setAcquisitionGrowthValueDesc("acquisitionGrowthValueDesc");
        responseGrowthValueRule.setValidityPeriodDesc("validityPeriodDesc");
        final ResponseModel<ResponseGrowthValueRule> responseGrowthValueRuleResponseModel = new ResponseModel<>(
                responseGrowthValueRule);
        when(mockHsaBaseClientService.getGrowthValueRule("systemManagementGuid", "cardGuid"))
                .thenReturn(responseGrowthValueRuleResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hsmcw/card/getGrowthValueRule")
                        .param("systemManagementGuid", "systemManagementGuid")
                        .param("cardGuid", "cardGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetIntegralRule() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getIntegralRule(...).
        final ResponseIntegralRule responseIntegralRule = new ResponseIntegralRule();
        responseIntegralRule.setIntegralGainRule("integralGainRule");
        responseIntegralRule.setIntegralDeductionRule("integralDeductionRule");
        responseIntegralRule.setIntegralValidityPeriod("integralValidityPeriod");
        final ResponseModel<ResponseIntegralRule> responseIntegralRuleResponseModel = new ResponseModel<>(
                responseIntegralRule);
        when(mockHsaBaseClientService.getIntegralRule("systemManagementGuid", "cardGuid"))
                .thenReturn(responseIntegralRuleResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hsmcw/card/getIntegralRule")
                        .param("systemManagementGuid", "systemManagementGuid")
                        .param("cardGuid", "cardGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
