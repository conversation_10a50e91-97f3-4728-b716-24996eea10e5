package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import org.junit.Before;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class WxStoreDTOTest {

    private WxStoreDTO wxStoreDTOUnderTest;

    @Before
    public void setUp() throws Exception {
        wxStoreDTOUnderTest = new WxStoreDTO();
    }

    @Test
    public void testStoreGuidGetterAndSetter() {
        final String storeGuid = "storeGuid";
        wxStoreDTOUnderTest.setStoreGuid(storeGuid);
        assertThat(wxStoreDTOUnderTest.getStoreGuid()).isEqualTo(storeGuid);
    }

    @Test
    public void testStoreKeyGetterAndSetter() {
        final String storeKey = "storeKey";
        wxStoreDTOUnderTest.setStoreKey(storeKey);
        assertThat(wxStoreDTOUnderTest.getStoreKey()).isEqualTo(storeKey);
    }

    @Test
    public void testStoreNameGetterAndSetter() {
        final String storeName = "storeName";
        wxStoreDTOUnderTest.setStoreName(storeName);
        assertThat(wxStoreDTOUnderTest.getStoreName()).isEqualTo(storeName);
    }

    @Test
    public void testEquals() throws Exception {
        assertThat(wxStoreDTOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() throws Exception {
        assertThat(wxStoreDTOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() throws Exception {
        assertThat(wxStoreDTOUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() throws Exception {
        assertThat(wxStoreDTOUnderTest.toString()).isEqualTo("result");
    }
}
