package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import org.junit.Before;
import org.junit.Test;

import java.time.LocalTime;

import static org.assertj.core.api.Assertions.assertThat;

public class ReserveTimeRespDTOTest {

    private ReserveTimeRespDTO reserveTimeRespDTOUnderTest;

    @Before
    public void setUp() throws Exception {
        reserveTimeRespDTOUnderTest = new ReserveTimeRespDTO(LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0));
    }

    @Test
    public void testReserveStartTimeGetterAndSetter() {
        final LocalTime reserveStartTime = LocalTime.of(0, 0, 0);
        reserveTimeRespDTOUnderTest.setReserveStartTime(reserveStartTime);
        assertThat(reserveTimeRespDTOUnderTest.getReserveStartTime()).isEqualTo(reserveStartTime);
    }

    @Test
    public void testReserveEndTimeGetterAndSetter() {
        final LocalTime reserveEndTime = LocalTime.of(0, 0, 0);
        reserveTimeRespDTOUnderTest.setReserveEndTime(reserveEndTime);
        assertThat(reserveTimeRespDTOUnderTest.getReserveEndTime()).isEqualTo(reserveEndTime);
    }

    @Test
    public void testEquals() throws Exception {
        assertThat(reserveTimeRespDTOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() throws Exception {
        assertThat(reserveTimeRespDTOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() throws Exception {
        assertThat(reserveTimeRespDTOUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() throws Exception {
        assertThat(reserveTimeRespDTOUnderTest.toString()).isEqualTo("result");
    }
}
