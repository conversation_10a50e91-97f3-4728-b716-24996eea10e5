package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.holder.saas.aggregation.app.config.ZhuanCanConfig;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.order.VolumeClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInBillClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInOrderClientService;
import com.holderzone.holder.saas.member.terminal.dto.volume.*;
import com.holderzone.saas.store.dto.order.request.member.MemberCouponListReqDTO;
import com.holderzone.saas.store.dto.order.request.member.RequestQueryVolumeDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class VolumeServiceImplTest {

    @Mock
    private VolumeClientService mockVolumeClientService;
    @Mock
    private DineInOrderClientService mockDineInOrderClientService;
    @Mock
    private ZhuanCanConfig mockZhuanCanConfig;
    @Mock
    private DineInBillClientService mockDineInBillClientService;

    @InjectMocks
    private VolumeServiceImpl volumeServiceImplUnderTest;

    @Test
    public void testQueryMemberVolumeList() {
        // Setup
        final RequestQueryVolumeDTO requestQueryVolumeDTO = new RequestQueryVolumeDTO();
        requestQueryVolumeDTO.setStoreGuid("storeGuid");
        requestQueryVolumeDTO.setMemberInfoGuid("memberInfoGuid");
        requestQueryVolumeDTO.setOrderGuid("orderGuid");
        requestQueryVolumeDTO.setOrderNumber("orderNumber");
        requestQueryVolumeDTO.setMemberInfoCardGuid("memberInfoCardGuid");

        final ResponseQueryVolumeDTO expectedResult = new ResponseQueryVolumeDTO();
        final ResponseVolumeList responseVolumeList = new ResponseVolumeList();
        responseVolumeList.setVolumeCode("volumeCode");
        responseVolumeList.setVolumeInfoGuid("volumeInfoGuid");
        responseVolumeList.setMemberVolumeGuid("memberVolumeGuid");
        expectedResult.setCheckedVolumeLists(Arrays.asList(responseVolumeList));
        final ResponseVolumeList responseVolumeList1 = new ResponseVolumeList();
        responseVolumeList1.setVolumeCode("volumeCode");
        responseVolumeList1.setVolumeInfoGuid("volumeInfoGuid");
        responseVolumeList1.setMemberVolumeGuid("memberVolumeGuid");
        expectedResult.setUsableVolumeLists(Arrays.asList(responseVolumeList1));

        // Configure VolumeClientService.consumeVolumeList(...).
        final ResponseVolumeList responseVolumeList2 = new ResponseVolumeList();
        responseVolumeList2.setVolumeCode("volumeCode");
        responseVolumeList2.setVolumeInfoGuid("volumeInfoGuid");
        responseVolumeList2.setMemberVolumeGuid("memberVolumeGuid");
        responseVolumeList2.setVolumeInfoName("volumeInfoName");
        responseVolumeList2.setVolumeStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<ResponseVolumeList> responseVolumeLists = Arrays.asList(responseVolumeList2);
        when(mockVolumeClientService.consumeVolumeList("orderGuid")).thenReturn(responseVolumeLists);

        // Configure DineInOrderClientService.couponList(...).
        final ResponseVolumeList responseVolumeList3 = new ResponseVolumeList();
        responseVolumeList3.setVolumeCode("volumeCode");
        responseVolumeList3.setVolumeInfoGuid("volumeInfoGuid");
        responseVolumeList3.setMemberVolumeGuid("memberVolumeGuid");
        responseVolumeList3.setVolumeInfoName("volumeInfoName");
        responseVolumeList3.setVolumeStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<ResponseVolumeList> responseVolumeLists1 = Arrays.asList(responseVolumeList3);
        final MemberCouponListReqDTO memberCouponListReqDTO = new MemberCouponListReqDTO();
        memberCouponListReqDTO.setStoreGuid("storeGuid");
        memberCouponListReqDTO.setOrderGuid("orderGuid");
        memberCouponListReqDTO.setMemberInfoGuid("memberInfoGuid");
        memberCouponListReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        memberCouponListReqDTO.setVolumeCodes(Arrays.asList("value"));
        when(mockDineInOrderClientService.couponList(memberCouponListReqDTO)).thenReturn(responseVolumeLists1);

        // Run the test
        final ResponseQueryVolumeDTO result = volumeServiceImplUnderTest.queryMemberVolumeList(requestQueryVolumeDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryMemberVolumeList_VolumeClientServiceReturnsNoItems() {
        // Setup
        final RequestQueryVolumeDTO requestQueryVolumeDTO = new RequestQueryVolumeDTO();
        requestQueryVolumeDTO.setStoreGuid("storeGuid");
        requestQueryVolumeDTO.setMemberInfoGuid("memberInfoGuid");
        requestQueryVolumeDTO.setOrderGuid("orderGuid");
        requestQueryVolumeDTO.setOrderNumber("orderNumber");
        requestQueryVolumeDTO.setMemberInfoCardGuid("memberInfoCardGuid");

        final ResponseQueryVolumeDTO expectedResult = new ResponseQueryVolumeDTO();
        final ResponseVolumeList responseVolumeList = new ResponseVolumeList();
        responseVolumeList.setVolumeCode("volumeCode");
        responseVolumeList.setVolumeInfoGuid("volumeInfoGuid");
        responseVolumeList.setMemberVolumeGuid("memberVolumeGuid");
        expectedResult.setCheckedVolumeLists(Arrays.asList(responseVolumeList));
        final ResponseVolumeList responseVolumeList1 = new ResponseVolumeList();
        responseVolumeList1.setVolumeCode("volumeCode");
        responseVolumeList1.setVolumeInfoGuid("volumeInfoGuid");
        responseVolumeList1.setMemberVolumeGuid("memberVolumeGuid");
        expectedResult.setUsableVolumeLists(Arrays.asList(responseVolumeList1));

        when(mockVolumeClientService.consumeVolumeList("orderGuid")).thenReturn(Collections.emptyList());

        // Configure DineInOrderClientService.couponList(...).
        final ResponseVolumeList responseVolumeList2 = new ResponseVolumeList();
        responseVolumeList2.setVolumeCode("volumeCode");
        responseVolumeList2.setVolumeInfoGuid("volumeInfoGuid");
        responseVolumeList2.setMemberVolumeGuid("memberVolumeGuid");
        responseVolumeList2.setVolumeInfoName("volumeInfoName");
        responseVolumeList2.setVolumeStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<ResponseVolumeList> responseVolumeLists = Arrays.asList(responseVolumeList2);
        final MemberCouponListReqDTO memberCouponListReqDTO = new MemberCouponListReqDTO();
        memberCouponListReqDTO.setStoreGuid("storeGuid");
        memberCouponListReqDTO.setOrderGuid("orderGuid");
        memberCouponListReqDTO.setMemberInfoGuid("memberInfoGuid");
        memberCouponListReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        memberCouponListReqDTO.setVolumeCodes(Arrays.asList("value"));
        when(mockDineInOrderClientService.couponList(memberCouponListReqDTO)).thenReturn(responseVolumeLists);

        // Run the test
        final ResponseQueryVolumeDTO result = volumeServiceImplUnderTest.queryMemberVolumeList(requestQueryVolumeDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryMemberVolumeList_DineInOrderClientServiceReturnsNoItems() {
        // Setup
        final RequestQueryVolumeDTO requestQueryVolumeDTO = new RequestQueryVolumeDTO();
        requestQueryVolumeDTO.setStoreGuid("storeGuid");
        requestQueryVolumeDTO.setMemberInfoGuid("memberInfoGuid");
        requestQueryVolumeDTO.setOrderGuid("orderGuid");
        requestQueryVolumeDTO.setOrderNumber("orderNumber");
        requestQueryVolumeDTO.setMemberInfoCardGuid("memberInfoCardGuid");

        final ResponseQueryVolumeDTO expectedResult = new ResponseQueryVolumeDTO();
        final ResponseVolumeList responseVolumeList = new ResponseVolumeList();
        responseVolumeList.setVolumeCode("volumeCode");
        responseVolumeList.setVolumeInfoGuid("volumeInfoGuid");
        responseVolumeList.setMemberVolumeGuid("memberVolumeGuid");
        expectedResult.setCheckedVolumeLists(Arrays.asList(responseVolumeList));
        final ResponseVolumeList responseVolumeList1 = new ResponseVolumeList();
        responseVolumeList1.setVolumeCode("volumeCode");
        responseVolumeList1.setVolumeInfoGuid("volumeInfoGuid");
        responseVolumeList1.setMemberVolumeGuid("memberVolumeGuid");
        expectedResult.setUsableVolumeLists(Arrays.asList(responseVolumeList1));

        // Configure VolumeClientService.consumeVolumeList(...).
        final ResponseVolumeList responseVolumeList2 = new ResponseVolumeList();
        responseVolumeList2.setVolumeCode("volumeCode");
        responseVolumeList2.setVolumeInfoGuid("volumeInfoGuid");
        responseVolumeList2.setMemberVolumeGuid("memberVolumeGuid");
        responseVolumeList2.setVolumeInfoName("volumeInfoName");
        responseVolumeList2.setVolumeStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<ResponseVolumeList> responseVolumeLists = Arrays.asList(responseVolumeList2);
        when(mockVolumeClientService.consumeVolumeList("orderGuid")).thenReturn(responseVolumeLists);

        // Configure DineInOrderClientService.couponList(...).
        final MemberCouponListReqDTO memberCouponListReqDTO = new MemberCouponListReqDTO();
        memberCouponListReqDTO.setStoreGuid("storeGuid");
        memberCouponListReqDTO.setOrderGuid("orderGuid");
        memberCouponListReqDTO.setMemberInfoGuid("memberInfoGuid");
        memberCouponListReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        memberCouponListReqDTO.setVolumeCodes(Arrays.asList("value"));
        when(mockDineInOrderClientService.couponList(memberCouponListReqDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final ResponseQueryVolumeDTO result = volumeServiceImplUnderTest.queryMemberVolumeList(requestQueryVolumeDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCheckVolumeRedeem() {
        // Setup
        final RequestCheckVolumeRedeem request = new RequestCheckVolumeRedeem();
        request.setEnterpriseGuid("enterpriseGuid");
        request.setOperSubjectGuid("operSubjectGuid");
        request.setPhoneNum("phoneNum");
        request.setMemberInfoGuid("memberInfoGuid");
        request.setMemberName("memberName");
        request.setRedeemCodeVal("redeemCodeVal");
        request.setStoreGuid("storeGuid");
        request.setOrderGuid("orderGuid");

        final ResponseCheckVolumeRedeem expectedResult = new ResponseCheckVolumeRedeem();
        expectedResult.setBelongsMemberInfoGuid("belongsMemberInfoGuid");
        expectedResult.setVolumeName("volumeName");
        expectedResult.setVolumeMoney(new BigDecimal("0.00"));
        expectedResult.setCanNum(0);
        expectedResult.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setVolumeCode("volumeCode");
        expectedResult.setStartValidityPeriod(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setEndValidityPeriod(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setUseThreshold(0);
        expectedResult.setUseThresholdFull(new BigDecimal("0.00"));
        expectedResult.setRuleTip("ruleTip");
        expectedResult.setVolumeType(0);
        expectedResult.setResultState(0);

        when(mockDineInOrderClientService.getOrderStateByGuid("orderGuid")).thenReturn(0);

        // Configure VolumeClientService.checkSingleVolumeRedeem(...).
        final ResponseCheckSingleVolumeRedeem responseCheckSingleVolumeRedeem = new ResponseCheckSingleVolumeRedeem();
        responseCheckSingleVolumeRedeem.setBelongsMemberInfoGuid("belongsMemberInfoGuid");
        responseCheckSingleVolumeRedeem.setVolumeName("volumeName");
        responseCheckSingleVolumeRedeem.setVolumeMoney(new BigDecimal("0.00"));
        responseCheckSingleVolumeRedeem.setCanNum(0);
        responseCheckSingleVolumeRedeem.setReceiveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        responseCheckSingleVolumeRedeem.setVolumeCode("volumeCode");
        responseCheckSingleVolumeRedeem.setStartValidityPeriod(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        responseCheckSingleVolumeRedeem.setEndValidityPeriod(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        responseCheckSingleVolumeRedeem.setUseThreshold(0);
        responseCheckSingleVolumeRedeem.setUseThresholdFull(new BigDecimal("0.00"));
        responseCheckSingleVolumeRedeem.setRuleTip("ruleTip");
        responseCheckSingleVolumeRedeem.setVolumeType(0);
        responseCheckSingleVolumeRedeem.setMessage("message");
        final RequestCheckSingleVolumeRedeem request1 = new RequestCheckSingleVolumeRedeem();
        request1.setMemberInfoGuid("memberInfoGuid");
        request1.setOrderGuid("orderGuid");
        request1.setVolumeCode("redeemCodeVal");
        request1.setStoreGuid("storeGuid");
        when(mockVolumeClientService.checkSingleVolumeRedeem(request1)).thenReturn(responseCheckSingleVolumeRedeem);

        when(mockZhuanCanConfig.getMemberRedeem()).thenReturn("result");

        // Run the test
        final ResponseCheckVolumeRedeem result = volumeServiceImplUnderTest.checkVolumeRedeem(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockDineInBillClientService).updateOrderIsUpdatedEs("orderGuid");
    }
}
