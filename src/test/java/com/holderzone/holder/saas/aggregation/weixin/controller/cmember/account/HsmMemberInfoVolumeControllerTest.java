package com.holderzone.holder.saas.aggregation.weixin.controller.cmember.account;

import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel;
import com.holderzone.holder.saas.aggregation.weixin.entity.dto.OrganizationNewDTO;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.EnterpriseClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.OrganizationClientService;
import com.holderzone.holder.saas.member.wechat.dto.member.*;
import com.holderzone.resource.common.dto.enterprise.OrganizationQueryDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.organization.StoreParserDTO;
import com.holderzone.saas.store.dto.store.store.StoreProductDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(HsmMemberInfoVolumeController.class)
public class HsmMemberInfoVolumeControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private HsaBaseClientService mockHsaBaseClientService;
    @MockBean
    private OrganizationClientService mockOrganizationClientService;
    @MockBean
    private EnterpriseClientService mockEnterpriseClientService;

    @Test
    public void testGetMemberVolume() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getMemberVolume(...).
        final ResponseMemberInfoVolume responseMemberInfoVolume = new ResponseMemberInfoVolume();
        responseMemberInfoVolume.setMayUseVolumeNum(0);
        responseMemberInfoVolume.setNotUseVolumeNum(0);
        final MemberInfoVolume memberInfoVolume = new MemberInfoVolume();
        memberInfoVolume.setMemberVolumeGuid("memberVolumeGuid");
        memberInfoVolume.setVolumeInfoGuid("volumeInfoGuid");
        responseMemberInfoVolume.setMemberVolumeList(Arrays.asList(memberInfoVolume));
        final ResponseModel<ResponseMemberInfoVolume> responseMemberInfoVolumeResponseModel = new ResponseModel<>(
                responseMemberInfoVolume);
        final RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO = new RequestMemberInfoVolumeQuery();
        memberInfoVolumeQueryReqDTO.setData(Arrays.asList());
        memberInfoVolumeQueryReqDTO.setMemberInfoGuid("memberInfoGuid");
        memberInfoVolumeQueryReqDTO.setVolumeType(0);
        memberInfoVolumeQueryReqDTO.setStoreGuid("storeGuid");
        memberInfoVolumeQueryReqDTO.setEnterpriseGuid("enterpriseGuid");
        when(mockHsaBaseClientService.getMemberVolume(memberInfoVolumeQueryReqDTO))
                .thenReturn(responseMemberInfoVolumeResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hsmcw/volume/getMemberVolume")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetMemberVolumeDetails() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getMemberVolumeDetails(...).
        final ResponseMemberInfoVolumeDetails responseMemberInfoVolumeDetails = new ResponseMemberInfoVolumeDetails();
        responseMemberInfoVolumeDetails.setMemberVolumeGuid("memberVolumeGuid");
        responseMemberInfoVolumeDetails.setVolumeInfoGuid("volumeInfoGuid");
        responseMemberInfoVolumeDetails.setVolumeInfoName("volumeInfoName");
        responseMemberInfoVolumeDetails.setVolumeSimpleDesc("volumeSimpleDesc");
        responseMemberInfoVolumeDetails.setVolumeStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ResponseModel<ResponseMemberInfoVolumeDetails> responseMemberInfoVolumeDetailsResponseModel = new ResponseModel<>(
                responseMemberInfoVolumeDetails);
        when(mockHsaBaseClientService.getMemberVolumeDetails("memberVolumeGuid"))
                .thenReturn(responseMemberInfoVolumeDetailsResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hsmcw/volume/getMemberVolumeDetails")
                        .param("memberVolumeGuid", "memberVolumeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetEnterpriseStoreList() throws Exception {
        // Setup
        // Configure OrganizationClientService.queryStoreByConditionNoPage(...).
        final List<StoreDTO> storeDTOS = Arrays.asList(
                new StoreDTO("ab8f8074-eeca-4e18-90e4-64669f7e9753", "code", "name", "belongBrandGuid",
                        "belongBrandName", "parentIds", LocalTime.of(0, 0, 0), LocalTime.of(0, 0, 0), "contactName",
                        "contactTel", "provinceCode", "provinceName", "cityCode", "cityName", "countyCode",
                        "countyName", "addressDetail", "longitude", "latitude", false, false, "photos",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createUserGuid",
                        "modifiedUserGuid", Arrays.asList(
                        new StoreProductDTO("productGuid", "productName", 0, LocalDate.of(2020, 1, 1),
                                LocalDate.of(2020, 1, 1), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, 0)),
                        Arrays.asList(new BrandDTO("f607c3ed-1f56-4af7-806d-92b001187766",
                                "9a5765ea-5547-41d5-a57a-a9facfe037bd", "name", "description", "logoUrl", false, false,
                                "createUserGuid", "modifiedUserGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "mchntTypeCode", Arrays.asList(), 0, 0, 0, 0, false)),
                        "mchntTypeCode", "logoUrl", "openingHours", 0, 0, 0, 0, "storeNotification", "storeDoorPhoto",
                        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, 0,
                        LocalDate.of(2020, 1, 1)));
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrganizationClientService.queryStoreByConditionNoPage(storeParserDTO)).thenReturn(storeDTOS);

        // Configure EnterpriseClientService.pageQueryAllPlatformStore(...).
        final OrganizationNewDTO organizationNewDTO = new OrganizationNewDTO();
        organizationNewDTO.setLinkGuid("linkGuid");
        organizationNewDTO.setOrganizationGuid("organizationGuid");
        organizationNewDTO.setOrganizationCode("organizationCode");
        organizationNewDTO.setName("name");
        organizationNewDTO.setMultiMemberGuid("multiMemberGuid");
        final Page<OrganizationNewDTO> organizationNewDTOPage = new Page<>(0L, 0L, Arrays.asList(organizationNewDTO));
        final OrganizationQueryDTO organizationQueryDTO = new OrganizationQueryDTO();
        organizationQueryDTO.setPageSize(0);
        organizationQueryDTO.setProvince("province");
        organizationQueryDTO.setCity("city");
        organizationQueryDTO.setDistrict("district");
        organizationQueryDTO.setEnterpriseGuid("enterpriseGuid");
        when(mockEnterpriseClientService.pageQueryAllPlatformStore(organizationQueryDTO))
                .thenReturn(organizationNewDTOPage);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hsmcw/volume/getEnterpriseStoreList")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetEnterpriseStoreList_OrganizationClientServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure OrganizationClientService.queryStoreByConditionNoPage(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrganizationClientService.queryStoreByConditionNoPage(storeParserDTO))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hsmcw/volume/getEnterpriseStoreList")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetVolumeTypes() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getVolumeTypes(...).
        final ResponseMemberSourceType responseMemberSourceType = new ResponseMemberSourceType();
        responseMemberSourceType.setType(0);
        responseMemberSourceType.setName("name");
        final ResponseModel<List<ResponseMemberSourceType>> listResponseModel = new ResponseModel<>(
                Arrays.asList(responseMemberSourceType));
        when(mockHsaBaseClientService.getVolumeTypes()).thenReturn(listResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hsmcw/volume/getVolumeTypes")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetVolumeTypes_HsaBaseClientServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getVolumeTypes(...).
        final ResponseModel<List<ResponseMemberSourceType>> listResponseModel = new ResponseModel<>(
                Collections.emptyList());
        when(mockHsaBaseClientService.getVolumeTypes()).thenReturn(listResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hsmcw/volume/getVolumeTypes")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }
}
