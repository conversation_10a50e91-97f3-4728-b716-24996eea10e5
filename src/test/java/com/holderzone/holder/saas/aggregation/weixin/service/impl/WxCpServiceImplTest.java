package com.holderzone.holder.saas.aggregation.weixin.service.impl;

import com.holderzone.holder.saas.aggregation.weixin.service.rpc.MemberMarketingClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxMpMessageHandlerClientService;
import com.holderzone.holder.saas.member.terminal.dto.feign.FeignModel;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxCpServiceImplTest {

    @Mock
    private MemberMarketingClientService mockMemberMarketingClientService;
    @Mock
    private WxMpMessageHandlerClientService mockWxMpMessageHandlerClientService;

    private WxCpServiceImpl wxCpServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxCpServiceImplUnderTest = new WxCpServiceImpl(mockMemberMarketingClientService,
                mockWxMpMessageHandlerClientService);
    }

    @Test
    public void testQueryWxCpAuthorizeUrl() {
        // Setup
        when(mockWxMpMessageHandlerClientService.qrRedirect("t")).thenReturn("result");

        // Configure MemberMarketingClientService.queryWxCpAuthorizeUrl(...).
        final FeignModel<String> stringFeignModel = new FeignModel<>();
        stringFeignModel.setCode(0);
        stringFeignModel.setMessage("message");
        stringFeignModel.setData("value");
        when(mockMemberMarketingClientService.queryWxCpAuthorizeUrl("operSubjectGuid", "t"))
                .thenReturn(stringFeignModel);

        // Run the test
        final String result = wxCpServiceImplUnderTest.queryWxCpAuthorizeUrl("t");

        // Verify the results
        assertThat(result).isEqualTo("data");
    }
}
