package com.holderzone.holder.saas.aggregation.app.utils;

import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;

public class HttpsClientUtilsTest {

    @Test
    public void testDoGet1() {
        assertEquals("result", HttpsClientUtils.doGet("url"));
    }

    @Test
    public void testDoGet2() {
        // Setup
        final Map<String, Object> params = new HashMap<>();

        // Run the test
        final String result = HttpsClientUtils.doGet("url", params);

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testDoPost1() {
        assertEquals("result", HttpsClientUtils.doPost("apiUrl"));
    }

    @Test
    public void testDoPost2() {
        // Setup
        final Map<String, Object> params = new HashMap<>();

        // Run the test
        final String result = HttpsClientUtils.doPost("apiUrl", params);

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testDoPost3() {
        assertEquals("result", HttpsClientUtils.doPost("apiUrl", "json"));
    }

    @Test
    public void testDoPostJSON() {
        assertEquals("result", HttpsClientUtils.doPostJSON("apiUrl", "date"));
    }
}
