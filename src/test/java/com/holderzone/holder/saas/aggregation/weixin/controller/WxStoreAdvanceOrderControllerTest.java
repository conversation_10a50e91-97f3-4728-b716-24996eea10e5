package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxStoreAdvanceOrderClientService;
import com.holderzone.saas.store.dto.weixin.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(WxStoreAdvanceOrderController.class)
public class WxStoreAdvanceOrderControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStoreAdvanceOrderClientService mockWxStoreAdvanceOrderClientService;

    @Test
    public void testCreateAdvanceOrder() throws Exception {
        // Setup
        // Configure WxStoreAdvanceOrderClientService.createAdvanceOrder(...).
        final WxStoreAdvanceEstimateDTO wxStoreAdvanceEstimateDTO = new WxStoreAdvanceEstimateDTO(false,
                Arrays.asList(new WxStoreEstimateItem("skuGuid", "skuName", "itemName", "itemGuid")));
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = new WxStoreAdvanceConsumerReqDTO();
        wxStoreAdvanceConsumerReqDTO.setAdvanceType(0);
        wxStoreAdvanceConsumerReqDTO.setTradeOrderGuid("tradeOrderGuid");
        final WxStoreConsumerDTO wxStoreConsumerDTO = new WxStoreConsumerDTO();
        wxStoreConsumerDTO.setConsumerGuid("consumerGuid");
        wxStoreAdvanceConsumerReqDTO.setWxStoreConsumerDTO(wxStoreConsumerDTO);
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(wxStoreAdvanceConsumerReqDTO);
        when(mockWxStoreAdvanceOrderClientService.createAdvanceOrder(wxStoreAdvanceOrderDTO))
                .thenReturn(wxStoreAdvanceEstimateDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/wx_store_advance_order/add")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDelAdvanceOrder() throws Exception {
        // Setup
        when(mockWxStoreAdvanceOrderClientService.delAdvanceOrder(new WxStoreAdvanceConsumerReqDTO(0, "tradeOrderGuid",
                new WxStoreConsumerDTO("consumerGuid", "openId", "unionId", "nickName", "headImgUrl", 0, "country",
                        "province", "city", "","","phone", 0, "deviceId", "enterpriseGuid", "enterpriseName", "storeGuid",
                        "storeName", "userGuid", "userName", "account", 0, "diningTableGuid", "tableCode",
                        "diningTableName", "areaGuid", "areaName", "brandName", "brandGuid", "brandLogo", false,
                        "memberInfoGuid", "phoneNum", false, "operSubjectGuid", false), 0, "orderRemark", 0, "nopenId",
                0, "payGuid", 0))).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/wx_store_advance_order/del")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetPersonWxStoreAdvanceOrder() throws Exception {
        // Setup
        // Configure WxStoreAdvanceOrderClientService.getPersonWxStoreAdvanceOrder(...).
        final WxStoreAdvanceOrderPriceDTO wxStoreAdvanceOrderPriceDTO = new WxStoreAdvanceOrderPriceDTO();
        wxStoreAdvanceOrderPriceDTO.setAdvanceGuid("advanceGuid");
        wxStoreAdvanceOrderPriceDTO.setTradeOrderGuid("tradeOrderGuid");
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = new WxStoreAdvanceConsumerReqDTO();
        wxStoreAdvanceConsumerReqDTO.setAdvanceType(0);
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(wxStoreAdvanceConsumerReqDTO);
        wxStoreAdvanceOrderPriceDTO.setWxStoreAdvanceOrderDTOS(Arrays.asList(wxStoreAdvanceOrderDTO));
        when(mockWxStoreAdvanceOrderClientService.getPersonWxStoreAdvanceOrder(
                new WxStoreAdvanceConsumerReqDTO(0, "tradeOrderGuid",
                        new WxStoreConsumerDTO("consumerGuid", "openId", "unionId", "nickName", "headImgUrl", 0,
                                "country", "province", "city", "","","phone", 0, "deviceId", "enterpriseGuid",
                                "enterpriseName", "storeGuid", "storeName", "userGuid", "userName", "account", 0,
                                "diningTableGuid", "tableCode", "diningTableName", "areaGuid", "areaName", "brandName",
                                "brandGuid", "brandLogo", false, "memberInfoGuid", "phoneNum", false, "operSubjectGuid",
                                false), 0, "orderRemark", 0, "nopenId", 0, "payGuid", 0)))
                .thenReturn(wxStoreAdvanceOrderPriceDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/wx_store_advance_order/advance")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetTableWxStoreAdvanceOrder() throws Exception {
        // Setup
        // Configure WxStoreAdvanceOrderClientService.getTableWxStoreAdvanceOrder(...).
        final WxStoreAdvanceOrderPriceDTO wxStoreAdvanceOrderPriceDTO = new WxStoreAdvanceOrderPriceDTO();
        wxStoreAdvanceOrderPriceDTO.setAdvanceGuid("advanceGuid");
        wxStoreAdvanceOrderPriceDTO.setTradeOrderGuid("tradeOrderGuid");
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = new WxStoreAdvanceConsumerReqDTO();
        wxStoreAdvanceConsumerReqDTO.setAdvanceType(0);
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(wxStoreAdvanceConsumerReqDTO);
        wxStoreAdvanceOrderPriceDTO.setWxStoreAdvanceOrderDTOS(Arrays.asList(wxStoreAdvanceOrderDTO));
        when(mockWxStoreAdvanceOrderClientService.getTableWxStoreAdvanceOrder(
                new WxStoreAdvanceConsumerReqDTO(0, "tradeOrderGuid",
                        new WxStoreConsumerDTO("consumerGuid", "openId", "unionId", "nickName", "headImgUrl", 0,
                                "country", "province", "city","","", "phone", 0, "deviceId", "enterpriseGuid",
                                "enterpriseName", "storeGuid", "storeName", "userGuid", "userName", "account", 0,
                                "diningTableGuid", "tableCode", "diningTableName", "areaGuid", "areaName", "brandName",
                                "brandGuid", "brandLogo", false, "memberInfoGuid", "phoneNum", false, "operSubjectGuid",
                                false), 0, "orderRemark", 0, "nopenId", 0, "payGuid", 0)))
                .thenReturn(wxStoreAdvanceOrderPriceDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/wx_store_advance_order/table")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testUpdateAdvanceOrderRemark() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/deal/wx_store_advance_order/update_remark")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
