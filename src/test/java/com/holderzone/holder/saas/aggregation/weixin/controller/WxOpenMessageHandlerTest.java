package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.holder.saas.aggregation.weixin.config.WeCatConfig;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxOpenMessageHandleClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxStoreMenuDetailsClientService;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.entry.dto.WxUserInfoDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.weixin.WxStoreAuthorizerInfoDTO;
import com.holderzone.saas.store.dto.weixin.open.WxMessageHandleReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxAuthorizeReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxCommonReqDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(WxOpenMessageHandler.class)
public class WxOpenMessageHandlerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private RedisUtils mockRedisUtils;
    @MockBean
    private WxStoreMenuDetailsClientService mockWxStoreMenuDetailsClientService;
    @MockBean
    private WxOpenMessageHandleClientService mockWxMessageHandleClientService;
    @MockBean
    private WeCatConfig mockWeCatConfig;

    @Test
    public void testCallBack() throws Exception {
        // Setup
        // Configure WxOpenMessageHandleClientService.handleMessage(...).
        final WxCommonReqDTO wxCommonReqDTO = new WxCommonReqDTO();
        wxCommonReqDTO.setSignature("signature");
        wxCommonReqDTO.setEchostr("echostr");
        wxCommonReqDTO.setTimestamp("timestamp");
        wxCommonReqDTO.setNonce("nonce");
        wxCommonReqDTO.setEncrypt_type("encrypt_type");
        final WxMessageHandleReqDTO wxMessageHandleReqDTO = new WxMessageHandleReqDTO("message", "appId",
                wxCommonReqDTO);
        when(mockWxMessageHandleClientService.handleMessage(wxMessageHandleReqDTO)).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_handler/{APPID}/call_back", "appId")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testRedirect() throws Exception {
        // Setup
        // Configure WxStoreMenuDetailsClientService.findWxAuthInfo(...).
        final WxStoreAuthorizerInfoDTO wxStoreAuthorizerInfoDTO = new WxStoreAuthorizerInfoDTO();
        wxStoreAuthorizerInfoDTO.setId(0L);
        wxStoreAuthorizerInfoDTO.setGuid("44a5bc72-2aca-46ce-a664-e4b75e855531");
        wxStoreAuthorizerInfoDTO.setBrandGuid("brandGuid");
        wxStoreAuthorizerInfoDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreAuthorizerInfoDTO.setOperSubjectGuid("operSubjectGuid");
        when(mockWxStoreMenuDetailsClientService.findWxAuthInfo("appId")).thenReturn(wxStoreAuthorizerInfoDTO);

        when(mockWeCatConfig.getOrderingMessageExpire()).thenReturn(0);
        when(mockRedisUtils.generateGuid("xweixintoken")).thenReturn("weixinToken");
        when(mockWeCatConfig.getOrderingIndexPage()).thenReturn("result");

        // Configure WxOpenMessageHandleClientService.buildSession(...).
        final WxMemberSessionDTO wxMemberSessionDTO = new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0);
        final WxAuthorizeReqDTO wxAuthorizeReqDTO = new WxAuthorizeReqDTO();
        wxAuthorizeReqDTO.setEventKey("eventKey");
        wxAuthorizeReqDTO.setAppId("appId");
        wxAuthorizeReqDTO.setWeixinToken("weixinToken");
        wxAuthorizeReqDTO.setEnterpriseGuid("enterpriseGuid");
        wxAuthorizeReqDTO.setCurrentTime(0L);
        when(mockWxMessageHandleClientService.buildSession(wxAuthorizeReqDTO)).thenReturn(wxMemberSessionDTO);

        // Configure WxOpenMessageHandleClientService.getUserInfo(...).
        final WxAuthorizeReqDTO wxAuthorizeReqDTO1 = new WxAuthorizeReqDTO();
        wxAuthorizeReqDTO1.setEventKey("eventKey");
        wxAuthorizeReqDTO1.setAppId("appId");
        wxAuthorizeReqDTO1.setWeixinToken("weixinToken");
        wxAuthorizeReqDTO1.setEnterpriseGuid("enterpriseGuid");
        wxAuthorizeReqDTO1.setCurrentTime(0L);
        when(mockWxMessageHandleClientService.getUserInfo(wxAuthorizeReqDTO1)).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/wx_handler/{APPID}/redirect", "appId")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");

        // Confirm RedisUtils.setEx(...).
        final WxAuthorizeReqDTO value = new WxAuthorizeReqDTO();
        value.setEventKey("eventKey");
        value.setAppId("appId");
        value.setWeixinToken("weixinToken");
        value.setEnterpriseGuid("enterpriseGuid");
        value.setCurrentTime(0L);
        verify(mockRedisUtils).setEx("key", value, 5L, TimeUnit.MINUTES);
    }
}
