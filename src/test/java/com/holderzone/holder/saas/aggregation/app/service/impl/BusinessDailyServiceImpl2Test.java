package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.google.common.util.concurrent.MoreExecutors;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.app.service.feign.PrintClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.account.MemberDataClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.reserve.ReserveClient;
import com.holderzone.holder.saas.aggregation.app.service.feign.staff.UserClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.takeout.TakeoutDailyClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DinnerDailyClientService;
import com.holderzone.holder.saas.member.terminal.dto.statistics.RequestMemberDaily;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponseConsumpStatis;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponsePayWayDetail;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponseRechargeStatis;
import com.holderzone.saas.store.dto.business.manage.TakeoutStatsDTO;
import com.holderzone.saas.store.dto.business.manage.TakeoutStatsQueryDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyPrintReqDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.*;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import com.holderzone.saas.store.enums.print.PrintSourceEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BusinessDailyServiceImpl2Test {

    @Mock
    private DinnerDailyClientService mockDinnerDailyClientService;
    @Mock
    private TakeoutDailyClientService mockTakeoutDailyClientService;
    @Mock
    private PrintClientService mockPrintClientService;
    @Mock
    private MemberDataClientService mockDataClientService;
    @Mock
    private ReserveClient mockReserveClient;
    @Mock
    private UserClientService mockUserClientService;
    @Mock
    private BusinessDailyPrintServiceImpl mockBusinessDailyPrintServiceImpl;

    private BusinessDailyServiceImpl businessDailyServiceImplUnderTest;

    @Before
    public void setUp() {
        businessDailyServiceImplUnderTest = new BusinessDailyServiceImpl(mockDinnerDailyClientService,
                mockTakeoutDailyClientService, mockDataClientService, mockReserveClient,
                mockUserClientService, MoreExecutors.directExecutor());
    }

    @Test
    public void testDiningType() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        final DiningTypeRespDTO diningTypeRespDTO = new DiningTypeRespDTO();
        diningTypeRespDTO.setTypeCode(0);
        diningTypeRespDTO.setTypeName("合计");
        diningTypeRespDTO.setOrderCount(0);
        diningTypeRespDTO.setGuestCount(0);
        diningTypeRespDTO.setAmount(new BigDecimal("0.00"));
        diningTypeRespDTO.setOrderPrice(new BigDecimal("0.00"));
        diningTypeRespDTO.setGuestPrice(new BigDecimal("0.00"));
        diningTypeRespDTO.setSubDiningTypes(Arrays.asList(new DiningTypeRespDTO()));
        diningTypeRespDTO.setIsTotal(0);
        final List<DiningTypeRespDTO> expectedResult = Arrays.asList(diningTypeRespDTO);

        // Configure DinnerDailyClientService.diningType(...).
        final DiningTypeRespDTO diningTypeRespDTO1 = new DiningTypeRespDTO();
        diningTypeRespDTO1.setTypeCode(0);
        diningTypeRespDTO1.setTypeName("合计");
        diningTypeRespDTO1.setOrderCount(0);
        diningTypeRespDTO1.setGuestCount(0);
        diningTypeRespDTO1.setAmount(new BigDecimal("0.00"));
        diningTypeRespDTO1.setOrderPrice(new BigDecimal("0.00"));
        diningTypeRespDTO1.setGuestPrice(new BigDecimal("0.00"));
        diningTypeRespDTO1.setSubDiningTypes(Arrays.asList(new DiningTypeRespDTO()));
        diningTypeRespDTO1.setIsTotal(0);
        final List<DiningTypeRespDTO> diningTypeRespDTOS = Arrays.asList(diningTypeRespDTO1);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.diningType(request1)).thenReturn(diningTypeRespDTOS);

        // Configure TakeoutDailyClientService.getTradeStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getTradeStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test
        final List<DiningTypeRespDTO> result = businessDailyServiceImplUnderTest.diningType(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testDiningType_DinnerDailyClientServiceReturnsNull() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        final DiningTypeRespDTO diningTypeRespDTO = new DiningTypeRespDTO();
        diningTypeRespDTO.setTypeCode(0);
        diningTypeRespDTO.setTypeName("合计");
        diningTypeRespDTO.setOrderCount(0);
        diningTypeRespDTO.setGuestCount(0);
        diningTypeRespDTO.setAmount(new BigDecimal("0.00"));
        diningTypeRespDTO.setOrderPrice(new BigDecimal("0.00"));
        diningTypeRespDTO.setGuestPrice(new BigDecimal("0.00"));
        diningTypeRespDTO.setSubDiningTypes(Arrays.asList(new DiningTypeRespDTO()));
        diningTypeRespDTO.setIsTotal(0);
        final List<DiningTypeRespDTO> expectedResult = Arrays.asList(diningTypeRespDTO);

        // Configure DinnerDailyClientService.diningType(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.diningType(request1)).thenReturn(null);

        // Configure TakeoutDailyClientService.getTradeStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getTradeStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test
        final List<DiningTypeRespDTO> result = businessDailyServiceImplUnderTest.diningType(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testDiningType_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        // Configure DinnerDailyClientService.diningType(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.diningType(request1)).thenReturn(Collections.emptyList());

        // Configure TakeoutDailyClientService.getTradeStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getTradeStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test
        final List<DiningTypeRespDTO> result = businessDailyServiceImplUnderTest.diningType(request);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testDiningType_TakeoutDailyClientServiceReturnsNull() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        final DiningTypeRespDTO diningTypeRespDTO = new DiningTypeRespDTO();
        diningTypeRespDTO.setTypeCode(0);
        diningTypeRespDTO.setTypeName("合计");
        diningTypeRespDTO.setOrderCount(0);
        diningTypeRespDTO.setGuestCount(0);
        diningTypeRespDTO.setAmount(new BigDecimal("0.00"));
        diningTypeRespDTO.setOrderPrice(new BigDecimal("0.00"));
        diningTypeRespDTO.setGuestPrice(new BigDecimal("0.00"));
        diningTypeRespDTO.setSubDiningTypes(Arrays.asList(new DiningTypeRespDTO()));
        diningTypeRespDTO.setIsTotal(0);
        final List<DiningTypeRespDTO> expectedResult = Arrays.asList(diningTypeRespDTO);

        // Configure DinnerDailyClientService.diningType(...).
        final DiningTypeRespDTO diningTypeRespDTO1 = new DiningTypeRespDTO();
        diningTypeRespDTO1.setTypeCode(0);
        diningTypeRespDTO1.setTypeName("合计");
        diningTypeRespDTO1.setOrderCount(0);
        diningTypeRespDTO1.setGuestCount(0);
        diningTypeRespDTO1.setAmount(new BigDecimal("0.00"));
        diningTypeRespDTO1.setOrderPrice(new BigDecimal("0.00"));
        diningTypeRespDTO1.setGuestPrice(new BigDecimal("0.00"));
        diningTypeRespDTO1.setSubDiningTypes(Arrays.asList(new DiningTypeRespDTO()));
        diningTypeRespDTO1.setIsTotal(0);
        final List<DiningTypeRespDTO> diningTypeRespDTOS = Arrays.asList(diningTypeRespDTO1);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.diningType(request1)).thenReturn(diningTypeRespDTOS);

        // Configure TakeoutDailyClientService.getTradeStats(...).
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getTradeStats(takeoutStatsQueryDTO)).thenReturn(null);

        // Run the test
        final List<DiningTypeRespDTO> result = businessDailyServiceImplUnderTest.diningType(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testClassify() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        final ItemRespDTO dto = new ItemRespDTO();
        dto.setItemType(0);
        dto.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto.setName("");
        dto.setUnitPrice(new BigDecimal("0.00"));
        dto.setQuantum(new BigDecimal("0.00"));
        dto.setAmount(new BigDecimal("0.00"));
        dto.setDiscountAmount(new BigDecimal("0.00"));
        dto.setIsTotal(0);
        dto.setHasSubInfo(0);
        dto.setSubs(Arrays.asList(new ItemRespDTO()));
        dto.setSkuName("skuName");
        dto.setDinnerQuantum(new BigDecimal("0.00"));
        dto.setDinnerAmount(new BigDecimal("0.00"));
        dto.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutQuantum(new BigDecimal("0.00"));
        dto.setTakeoutAmount(new BigDecimal("0.00"));
        dto.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> expectedResult = Arrays.asList(dto);

        // Configure DinnerDailyClientService.classify(...).
        final ItemRespDTO dto1 = new ItemRespDTO();
        dto1.setItemType(0);
        dto1.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto1.setName("");
        dto1.setUnitPrice(new BigDecimal("0.00"));
        dto1.setQuantum(new BigDecimal("0.00"));
        dto1.setAmount(new BigDecimal("0.00"));
        dto1.setDiscountAmount(new BigDecimal("0.00"));
        dto1.setIsTotal(0);
        dto1.setHasSubInfo(0);
        dto1.setSubs(Arrays.asList(new ItemRespDTO()));
        dto1.setSkuName("skuName");
        dto1.setDinnerQuantum(new BigDecimal("0.00"));
        dto1.setDinnerAmount(new BigDecimal("0.00"));
        dto1.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto1.setTakeoutQuantum(new BigDecimal("0.00"));
        dto1.setTakeoutAmount(new BigDecimal("0.00"));
        dto1.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto1.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> list = Arrays.asList(dto1);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.classify(request1)).thenReturn(list);

        // Configure TakeoutDailyClientService.listItemSale(...).
        final ItemRespDTO dto2 = new ItemRespDTO();
        dto2.setItemType(0);
        dto2.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto2.setName("");
        dto2.setUnitPrice(new BigDecimal("0.00"));
        dto2.setQuantum(new BigDecimal("0.00"));
        dto2.setAmount(new BigDecimal("0.00"));
        dto2.setDiscountAmount(new BigDecimal("0.00"));
        dto2.setIsTotal(0);
        dto2.setHasSubInfo(0);
        dto2.setSubs(Arrays.asList(new ItemRespDTO()));
        dto2.setSkuName("skuName");
        dto2.setDinnerQuantum(new BigDecimal("0.00"));
        dto2.setDinnerAmount(new BigDecimal("0.00"));
        dto2.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto2.setTakeoutQuantum(new BigDecimal("0.00"));
        dto2.setTakeoutAmount(new BigDecimal("0.00"));
        dto2.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto2.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> list1 = Arrays.asList(dto2);
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setDeviceType(0);
        takeoutStatsQueryDTO.setDeviceId("deviceId");
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setIsPrint(0);
        when(mockTakeoutDailyClientService.listItemSale(takeoutStatsQueryDTO)).thenReturn(list1);

        // Run the test
        final List<ItemRespDTO> result = businessDailyServiceImplUnderTest.classify(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testClassify_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        final ItemRespDTO dto = new ItemRespDTO();
        dto.setItemType(0);
        dto.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto.setName("");
        dto.setUnitPrice(new BigDecimal("0.00"));
        dto.setQuantum(new BigDecimal("0.00"));
        dto.setAmount(new BigDecimal("0.00"));
        dto.setDiscountAmount(new BigDecimal("0.00"));
        dto.setIsTotal(0);
        dto.setHasSubInfo(0);
        dto.setSubs(Arrays.asList(new ItemRespDTO()));
        dto.setSkuName("skuName");
        dto.setDinnerQuantum(new BigDecimal("0.00"));
        dto.setDinnerAmount(new BigDecimal("0.00"));
        dto.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutQuantum(new BigDecimal("0.00"));
        dto.setTakeoutAmount(new BigDecimal("0.00"));
        dto.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> expectedResult = Arrays.asList(dto);

        // Configure DinnerDailyClientService.classify(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.classify(request1)).thenReturn(Collections.emptyList());

        // Configure TakeoutDailyClientService.listItemSale(...).
        final ItemRespDTO dto1 = new ItemRespDTO();
        dto1.setItemType(0);
        dto1.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto1.setName("");
        dto1.setUnitPrice(new BigDecimal("0.00"));
        dto1.setQuantum(new BigDecimal("0.00"));
        dto1.setAmount(new BigDecimal("0.00"));
        dto1.setDiscountAmount(new BigDecimal("0.00"));
        dto1.setIsTotal(0);
        dto1.setHasSubInfo(0);
        dto1.setSubs(Arrays.asList(new ItemRespDTO()));
        dto1.setSkuName("skuName");
        dto1.setDinnerQuantum(new BigDecimal("0.00"));
        dto1.setDinnerAmount(new BigDecimal("0.00"));
        dto1.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto1.setTakeoutQuantum(new BigDecimal("0.00"));
        dto1.setTakeoutAmount(new BigDecimal("0.00"));
        dto1.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto1.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> list = Arrays.asList(dto1);
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setDeviceType(0);
        takeoutStatsQueryDTO.setDeviceId("deviceId");
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setIsPrint(0);
        when(mockTakeoutDailyClientService.listItemSale(takeoutStatsQueryDTO)).thenReturn(list);

        // Run the test
        final List<ItemRespDTO> result = businessDailyServiceImplUnderTest.classify(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testClassify_TakeoutDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        final ItemRespDTO dto = new ItemRespDTO();
        dto.setItemType(0);
        dto.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto.setName("");
        dto.setUnitPrice(new BigDecimal("0.00"));
        dto.setQuantum(new BigDecimal("0.00"));
        dto.setAmount(new BigDecimal("0.00"));
        dto.setDiscountAmount(new BigDecimal("0.00"));
        dto.setIsTotal(0);
        dto.setHasSubInfo(0);
        dto.setSubs(Arrays.asList(new ItemRespDTO()));
        dto.setSkuName("skuName");
        dto.setDinnerQuantum(new BigDecimal("0.00"));
        dto.setDinnerAmount(new BigDecimal("0.00"));
        dto.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutQuantum(new BigDecimal("0.00"));
        dto.setTakeoutAmount(new BigDecimal("0.00"));
        dto.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> expectedResult = Arrays.asList(dto);

        // Configure DinnerDailyClientService.classify(...).
        final ItemRespDTO dto1 = new ItemRespDTO();
        dto1.setItemType(0);
        dto1.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto1.setName("");
        dto1.setUnitPrice(new BigDecimal("0.00"));
        dto1.setQuantum(new BigDecimal("0.00"));
        dto1.setAmount(new BigDecimal("0.00"));
        dto1.setDiscountAmount(new BigDecimal("0.00"));
        dto1.setIsTotal(0);
        dto1.setHasSubInfo(0);
        dto1.setSubs(Arrays.asList(new ItemRespDTO()));
        dto1.setSkuName("skuName");
        dto1.setDinnerQuantum(new BigDecimal("0.00"));
        dto1.setDinnerAmount(new BigDecimal("0.00"));
        dto1.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto1.setTakeoutQuantum(new BigDecimal("0.00"));
        dto1.setTakeoutAmount(new BigDecimal("0.00"));
        dto1.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto1.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> list = Arrays.asList(dto1);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.classify(request1)).thenReturn(list);

        // Configure TakeoutDailyClientService.listItemSale(...).
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setDeviceType(0);
        takeoutStatsQueryDTO.setDeviceId("deviceId");
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setIsPrint(0);
        when(mockTakeoutDailyClientService.listItemSale(takeoutStatsQueryDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ItemRespDTO> result = businessDailyServiceImplUnderTest.classify(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGoods() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        final ItemRespDTO dto = new ItemRespDTO();
        dto.setItemType(0);
        dto.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto.setName("");
        dto.setUnitPrice(new BigDecimal("0.00"));
        dto.setQuantum(new BigDecimal("0.00"));
        dto.setAmount(new BigDecimal("0.00"));
        dto.setDiscountAmount(new BigDecimal("0.00"));
        dto.setIsTotal(0);
        dto.setHasSubInfo(0);
        dto.setSubs(Arrays.asList(new ItemRespDTO()));
        dto.setSkuName("skuName");
        dto.setDinnerQuantum(new BigDecimal("0.00"));
        dto.setDinnerAmount(new BigDecimal("0.00"));
        dto.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutQuantum(new BigDecimal("0.00"));
        dto.setTakeoutAmount(new BigDecimal("0.00"));
        dto.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> expectedResult = Arrays.asList(dto);

        // Configure DinnerDailyClientService.goods(...).
        final ItemRespDTO dto1 = new ItemRespDTO();
        dto1.setItemType(0);
        dto1.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto1.setName("");
        dto1.setUnitPrice(new BigDecimal("0.00"));
        dto1.setQuantum(new BigDecimal("0.00"));
        dto1.setAmount(new BigDecimal("0.00"));
        dto1.setDiscountAmount(new BigDecimal("0.00"));
        dto1.setIsTotal(0);
        dto1.setHasSubInfo(0);
        dto1.setSubs(Arrays.asList(new ItemRespDTO()));
        dto1.setSkuName("skuName");
        dto1.setDinnerQuantum(new BigDecimal("0.00"));
        dto1.setDinnerAmount(new BigDecimal("0.00"));
        dto1.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto1.setTakeoutQuantum(new BigDecimal("0.00"));
        dto1.setTakeoutAmount(new BigDecimal("0.00"));
        dto1.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto1.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> list = Arrays.asList(dto1);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.goods(request1)).thenReturn(list);

        // Configure TakeoutDailyClientService.goods(...).
        final ItemRespDTO dto2 = new ItemRespDTO();
        dto2.setItemType(0);
        dto2.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto2.setName("");
        dto2.setUnitPrice(new BigDecimal("0.00"));
        dto2.setQuantum(new BigDecimal("0.00"));
        dto2.setAmount(new BigDecimal("0.00"));
        dto2.setDiscountAmount(new BigDecimal("0.00"));
        dto2.setIsTotal(0);
        dto2.setHasSubInfo(0);
        dto2.setSubs(Arrays.asList(new ItemRespDTO()));
        dto2.setSkuName("skuName");
        dto2.setDinnerQuantum(new BigDecimal("0.00"));
        dto2.setDinnerAmount(new BigDecimal("0.00"));
        dto2.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto2.setTakeoutQuantum(new BigDecimal("0.00"));
        dto2.setTakeoutAmount(new BigDecimal("0.00"));
        dto2.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto2.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> list1 = Arrays.asList(dto2);
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setDeviceType(0);
        takeoutStatsQueryDTO.setDeviceId("deviceId");
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setIsPrint(0);
        when(mockTakeoutDailyClientService.goods(takeoutStatsQueryDTO)).thenReturn(list1);

        // Run the test
        final List<ItemRespDTO> result = businessDailyServiceImplUnderTest.goods(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGoods_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        final ItemRespDTO dto = new ItemRespDTO();
        dto.setItemType(0);
        dto.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto.setName("");
        dto.setUnitPrice(new BigDecimal("0.00"));
        dto.setQuantum(new BigDecimal("0.00"));
        dto.setAmount(new BigDecimal("0.00"));
        dto.setDiscountAmount(new BigDecimal("0.00"));
        dto.setIsTotal(0);
        dto.setHasSubInfo(0);
        dto.setSubs(Arrays.asList(new ItemRespDTO()));
        dto.setSkuName("skuName");
        dto.setDinnerQuantum(new BigDecimal("0.00"));
        dto.setDinnerAmount(new BigDecimal("0.00"));
        dto.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutQuantum(new BigDecimal("0.00"));
        dto.setTakeoutAmount(new BigDecimal("0.00"));
        dto.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> expectedResult = Arrays.asList(dto);

        // Configure DinnerDailyClientService.goods(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.goods(request1)).thenReturn(Collections.emptyList());

        // Configure TakeoutDailyClientService.goods(...).
        final ItemRespDTO dto1 = new ItemRespDTO();
        dto1.setItemType(0);
        dto1.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto1.setName("");
        dto1.setUnitPrice(new BigDecimal("0.00"));
        dto1.setQuantum(new BigDecimal("0.00"));
        dto1.setAmount(new BigDecimal("0.00"));
        dto1.setDiscountAmount(new BigDecimal("0.00"));
        dto1.setIsTotal(0);
        dto1.setHasSubInfo(0);
        dto1.setSubs(Arrays.asList(new ItemRespDTO()));
        dto1.setSkuName("skuName");
        dto1.setDinnerQuantum(new BigDecimal("0.00"));
        dto1.setDinnerAmount(new BigDecimal("0.00"));
        dto1.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto1.setTakeoutQuantum(new BigDecimal("0.00"));
        dto1.setTakeoutAmount(new BigDecimal("0.00"));
        dto1.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto1.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> list = Arrays.asList(dto1);
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setDeviceType(0);
        takeoutStatsQueryDTO.setDeviceId("deviceId");
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setIsPrint(0);
        when(mockTakeoutDailyClientService.goods(takeoutStatsQueryDTO)).thenReturn(list);

        // Run the test
        final List<ItemRespDTO> result = businessDailyServiceImplUnderTest.goods(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGoods_TakeoutDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        final ItemRespDTO dto = new ItemRespDTO();
        dto.setItemType(0);
        dto.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto.setName("");
        dto.setUnitPrice(new BigDecimal("0.00"));
        dto.setQuantum(new BigDecimal("0.00"));
        dto.setAmount(new BigDecimal("0.00"));
        dto.setDiscountAmount(new BigDecimal("0.00"));
        dto.setIsTotal(0);
        dto.setHasSubInfo(0);
        dto.setSubs(Arrays.asList(new ItemRespDTO()));
        dto.setSkuName("skuName");
        dto.setDinnerQuantum(new BigDecimal("0.00"));
        dto.setDinnerAmount(new BigDecimal("0.00"));
        dto.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutQuantum(new BigDecimal("0.00"));
        dto.setTakeoutAmount(new BigDecimal("0.00"));
        dto.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> expectedResult = Arrays.asList(dto);

        // Configure DinnerDailyClientService.goods(...).
        final ItemRespDTO dto1 = new ItemRespDTO();
        dto1.setItemType(0);
        dto1.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto1.setName("");
        dto1.setUnitPrice(new BigDecimal("0.00"));
        dto1.setQuantum(new BigDecimal("0.00"));
        dto1.setAmount(new BigDecimal("0.00"));
        dto1.setDiscountAmount(new BigDecimal("0.00"));
        dto1.setIsTotal(0);
        dto1.setHasSubInfo(0);
        dto1.setSubs(Arrays.asList(new ItemRespDTO()));
        dto1.setSkuName("skuName");
        dto1.setDinnerQuantum(new BigDecimal("0.00"));
        dto1.setDinnerAmount(new BigDecimal("0.00"));
        dto1.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto1.setTakeoutQuantum(new BigDecimal("0.00"));
        dto1.setTakeoutAmount(new BigDecimal("0.00"));
        dto1.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto1.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> list = Arrays.asList(dto1);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.goods(request1)).thenReturn(list);

        // Configure TakeoutDailyClientService.goods(...).
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setDeviceType(0);
        takeoutStatsQueryDTO.setDeviceId("deviceId");
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setIsPrint(0);
        when(mockTakeoutDailyClientService.goods(takeoutStatsQueryDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ItemRespDTO> result = businessDailyServiceImplUnderTest.goods(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testAttr() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        final PropStatsDTO expectedResult = new PropStatsDTO();
        final PropStatsDTO.PropGroup propGroup = new PropStatsDTO.PropGroup();
        propGroup.setName("attrGroupName");
        final PropStatsDTO.PropItem propItem = new PropStatsDTO.PropItem();
        propItem.setName("");
        propItem.setQuantity(0L);
        propItem.setUnitPrice(new BigDecimal("0.00"));
        propItem.setMoney(new BigDecimal("0.00"));
        propItem.setPropList(Arrays.asList(new PropStatsDTO.PropItem()));
        propGroup.setPropList(Arrays.asList(propItem));
        propGroup.setGroupQuantity(0L);
        propGroup.setGroupMoney(new BigDecimal("0.00"));
        expectedResult.setPropGroupList(Arrays.asList(propGroup));
        expectedResult.setTotalQuantity(0L);
        expectedResult.setTotalMoney(new BigDecimal("0.00"));

        // Configure DinnerDailyClientService.attr(...).
        final AttrItemRespDTO attrItemRespDTO = new AttrItemRespDTO();
        attrItemRespDTO.setItemType(0);
        attrItemRespDTO.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        attrItemRespDTO.setName("");
        attrItemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        attrItemRespDTO.setQuantum(new BigDecimal("0.00"));
        attrItemRespDTO.setAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setIsTotal(0);
        attrItemRespDTO.setHasSubInfo(0);
        final ItemRespDTO dto = new ItemRespDTO();
        dto.setItemType(0);
        dto.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto.setName("");
        dto.setUnitPrice(new BigDecimal("0.00"));
        dto.setQuantum(new BigDecimal("0.00"));
        dto.setAmount(new BigDecimal("0.00"));
        dto.setDiscountAmount(new BigDecimal("0.00"));
        dto.setIsTotal(0);
        dto.setHasSubInfo(0);
        dto.setSubs(Arrays.asList(new ItemRespDTO()));
        dto.setSkuName("skuName");
        dto.setDinnerQuantum(new BigDecimal("0.00"));
        dto.setDinnerAmount(new BigDecimal("0.00"));
        dto.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutQuantum(new BigDecimal("0.00"));
        dto.setTakeoutAmount(new BigDecimal("0.00"));
        dto.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutUnitPrice(new BigDecimal("0.00"));
        attrItemRespDTO.setSubs(Arrays.asList(dto));
        attrItemRespDTO.setSkuName("skuName");
        attrItemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        attrItemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        attrItemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        attrItemRespDTO.setAttrGroupName("attrGroupName");
        attrItemRespDTO.setAttrs(Arrays.asList(new AttrItemRespDTO()));
        final List<AttrItemRespDTO> attrItemRespDTOS = Arrays.asList(attrItemRespDTO);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.attr(request1)).thenReturn(attrItemRespDTOS);

        // Run the test
        final PropStatsDTO result = businessDailyServiceImplUnderTest.attr(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testAttr_DinnerDailyClientServiceReturnsNull() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        final PropStatsDTO expectedResult = new PropStatsDTO();
        final PropStatsDTO.PropGroup propGroup = new PropStatsDTO.PropGroup();
        propGroup.setName("attrGroupName");
        final PropStatsDTO.PropItem propItem = new PropStatsDTO.PropItem();
        propItem.setName("");
        propItem.setQuantity(0L);
        propItem.setUnitPrice(new BigDecimal("0.00"));
        propItem.setMoney(new BigDecimal("0.00"));
        propItem.setPropList(Arrays.asList(new PropStatsDTO.PropItem()));
        propGroup.setPropList(Arrays.asList(propItem));
        propGroup.setGroupQuantity(0L);
        propGroup.setGroupMoney(new BigDecimal("0.00"));
        expectedResult.setPropGroupList(Arrays.asList(propGroup));
        expectedResult.setTotalQuantity(0L);
        expectedResult.setTotalMoney(new BigDecimal("0.00"));

        // Configure DinnerDailyClientService.attr(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.attr(request1)).thenReturn(null);

        // Run the test
        final PropStatsDTO result = businessDailyServiceImplUnderTest.attr(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testAttr_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        final PropStatsDTO expectedResult = new PropStatsDTO();
        final PropStatsDTO.PropGroup propGroup = new PropStatsDTO.PropGroup();
        propGroup.setName("attrGroupName");
        final PropStatsDTO.PropItem propItem = new PropStatsDTO.PropItem();
        propItem.setName("");
        propItem.setQuantity(0L);
        propItem.setUnitPrice(new BigDecimal("0.00"));
        propItem.setMoney(new BigDecimal("0.00"));
        propItem.setPropList(Arrays.asList(new PropStatsDTO.PropItem()));
        propGroup.setPropList(Arrays.asList(propItem));
        propGroup.setGroupQuantity(0L);
        propGroup.setGroupMoney(new BigDecimal("0.00"));
        expectedResult.setPropGroupList(Arrays.asList(propGroup));
        expectedResult.setTotalQuantity(0L);
        expectedResult.setTotalMoney(new BigDecimal("0.00"));

        // Configure DinnerDailyClientService.attr(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.attr(request1)).thenReturn(Collections.emptyList());

        // Run the test
        final PropStatsDTO result = businessDailyServiceImplUnderTest.attr(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testReturnVegetables() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        final ItemRespDTO dto = new ItemRespDTO();
        dto.setItemType(0);
        dto.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto.setName("");
        dto.setUnitPrice(new BigDecimal("0.00"));
        dto.setQuantum(new BigDecimal("0.00"));
        dto.setAmount(new BigDecimal("0.00"));
        dto.setDiscountAmount(new BigDecimal("0.00"));
        dto.setIsTotal(0);
        dto.setHasSubInfo(0);
        dto.setSubs(Arrays.asList(new ItemRespDTO()));
        dto.setSkuName("skuName");
        dto.setDinnerQuantum(new BigDecimal("0.00"));
        dto.setDinnerAmount(new BigDecimal("0.00"));
        dto.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutQuantum(new BigDecimal("0.00"));
        dto.setTakeoutAmount(new BigDecimal("0.00"));
        dto.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> expectedResult = Arrays.asList(dto);

        // Configure DinnerDailyClientService.returnVegetables(...).
        final ItemRespDTO dto1 = new ItemRespDTO();
        dto1.setItemType(0);
        dto1.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto1.setName("");
        dto1.setUnitPrice(new BigDecimal("0.00"));
        dto1.setQuantum(new BigDecimal("0.00"));
        dto1.setAmount(new BigDecimal("0.00"));
        dto1.setDiscountAmount(new BigDecimal("0.00"));
        dto1.setIsTotal(0);
        dto1.setHasSubInfo(0);
        dto1.setSubs(Arrays.asList(new ItemRespDTO()));
        dto1.setSkuName("skuName");
        dto1.setDinnerQuantum(new BigDecimal("0.00"));
        dto1.setDinnerAmount(new BigDecimal("0.00"));
        dto1.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto1.setTakeoutQuantum(new BigDecimal("0.00"));
        dto1.setTakeoutAmount(new BigDecimal("0.00"));
        dto1.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto1.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> list = Arrays.asList(dto1);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.returnVegetables(request1)).thenReturn(list);

        // Run the test
        final List<ItemRespDTO> result = businessDailyServiceImplUnderTest.returnVegetables(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testReturnVegetables_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        // Configure DinnerDailyClientService.returnVegetables(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.returnVegetables(request1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ItemRespDTO> result = businessDailyServiceImplUnderTest.returnVegetables(request);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testDishGiving() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        final ItemRespDTO dto = new ItemRespDTO();
        dto.setItemType(0);
        dto.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto.setName("");
        dto.setUnitPrice(new BigDecimal("0.00"));
        dto.setQuantum(new BigDecimal("0.00"));
        dto.setAmount(new BigDecimal("0.00"));
        dto.setDiscountAmount(new BigDecimal("0.00"));
        dto.setIsTotal(0);
        dto.setHasSubInfo(0);
        dto.setSubs(Arrays.asList(new ItemRespDTO()));
        dto.setSkuName("skuName");
        dto.setDinnerQuantum(new BigDecimal("0.00"));
        dto.setDinnerAmount(new BigDecimal("0.00"));
        dto.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutQuantum(new BigDecimal("0.00"));
        dto.setTakeoutAmount(new BigDecimal("0.00"));
        dto.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> expectedResult = Arrays.asList(dto);

        // Configure DinnerDailyClientService.dishGiving(...).
        final ItemRespDTO dto1 = new ItemRespDTO();
        dto1.setItemType(0);
        dto1.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto1.setName("");
        dto1.setUnitPrice(new BigDecimal("0.00"));
        dto1.setQuantum(new BigDecimal("0.00"));
        dto1.setAmount(new BigDecimal("0.00"));
        dto1.setDiscountAmount(new BigDecimal("0.00"));
        dto1.setIsTotal(0);
        dto1.setHasSubInfo(0);
        dto1.setSubs(Arrays.asList(new ItemRespDTO()));
        dto1.setSkuName("skuName");
        dto1.setDinnerQuantum(new BigDecimal("0.00"));
        dto1.setDinnerAmount(new BigDecimal("0.00"));
        dto1.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto1.setTakeoutQuantum(new BigDecimal("0.00"));
        dto1.setTakeoutAmount(new BigDecimal("0.00"));
        dto1.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto1.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> list = Arrays.asList(dto1);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.dishGiving(request1)).thenReturn(list);

        // Run the test
        final List<ItemRespDTO> result = businessDailyServiceImplUnderTest.dishGiving(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testDishGiving_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        // Configure DinnerDailyClientService.dishGiving(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.dishGiving(request1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ItemRespDTO> result = businessDailyServiceImplUnderTest.dishGiving(request);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testMemberConsume() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        final MemberConsumeRespDTO expectedResult = new MemberConsumeRespDTO();
        expectedResult.setConsumerCount(0);
        expectedResult.setConsumerAmount(new BigDecimal("0.00"));
        expectedResult.setPrepaidCount(0);
        expectedResult.setPrepaidAmount(new BigDecimal("0.00"));
        expectedResult.setPrepaidGiveAmount(new BigDecimal("0.00"));
        expectedResult.setPrepaidTakeInAmount(new BigDecimal("0.00"));
        final AmountItemDTO amountItemDTO = new AmountItemDTO();
        amountItemDTO.setCode(0);
        amountItemDTO.setName("name");
        amountItemDTO.setAmount(new BigDecimal("0.00"));
        amountItemDTO.setExcessAmount(new BigDecimal("0.00"));
        amountItemDTO.setEstimatedAmount(new BigDecimal("0.00"));
        expectedResult.setPrepaidItems(Arrays.asList(amountItemDTO));

        // Configure DinnerDailyClientService.memberConsume(...).
        final MemberConsumeRespDTO memberConsumeRespDTO = new MemberConsumeRespDTO();
        memberConsumeRespDTO.setConsumerCount(0);
        memberConsumeRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidCount(0);
        memberConsumeRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidGiveAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidTakeInAmount(new BigDecimal("0.00"));
        final AmountItemDTO amountItemDTO1 = new AmountItemDTO();
        amountItemDTO1.setCode(0);
        amountItemDTO1.setName("name");
        amountItemDTO1.setAmount(new BigDecimal("0.00"));
        amountItemDTO1.setExcessAmount(new BigDecimal("0.00"));
        amountItemDTO1.setEstimatedAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidItems(Arrays.asList(amountItemDTO1));
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.memberConsume(request1)).thenReturn(memberConsumeRespDTO);

        // Run the test
        final MemberConsumeRespDTO result = businessDailyServiceImplUnderTest.memberConsume(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGather() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        final GatherRespDTO gatherRespDTO = new GatherRespDTO();
        gatherRespDTO.setGatherCode(0);
        gatherRespDTO.setGatherName("gatherName");
        gatherRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO.setExcessAmount(new BigDecimal("0.00"));
        gatherRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO.setIsTotal(0);
        final List<GatherRespDTO> expectedResult = Arrays.asList(gatherRespDTO);

        // Configure DinnerDailyClientService.gather(...).
        final GatherRespDTO gatherRespDTO1 = new GatherRespDTO();
        gatherRespDTO1.setGatherCode(0);
        gatherRespDTO1.setGatherName("gatherName");
        gatherRespDTO1.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setExcessAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setIsTotal(0);
        final List<GatherRespDTO> gatherRespDTOS = Arrays.asList(gatherRespDTO1);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.gather(request1)).thenReturn(gatherRespDTOS);

        // Configure ReserveClient.gather(...).
        final GatherRespDTO gatherRespDTO2 = new GatherRespDTO();
        gatherRespDTO2.setGatherCode(0);
        gatherRespDTO2.setGatherName("gatherName");
        gatherRespDTO2.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO2.setExcessAmount(new BigDecimal("0.00"));
        gatherRespDTO2.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO2.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO2.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO2.setIsTotal(0);
        final List<GatherRespDTO> gatherRespDTOS1 = Arrays.asList(gatherRespDTO2);
        final DailyReqDTO dailyReqDTO = new DailyReqDTO();
        dailyReqDTO.setDeviceType(0);
        dailyReqDTO.setDeviceId("deviceId");
        dailyReqDTO.setStoreGuid("storeGuid");
        dailyReqDTO.setBeginTime("beginTime");
        dailyReqDTO.setEndTime("endTime");
        dailyReqDTO.setQueryType(0);
        dailyReqDTO.setOrderItem(0);
        dailyReqDTO.setOrderType(0);
        dailyReqDTO.setIsMember(0);
        dailyReqDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        dailyReqDTO.setStoreGuids(Arrays.asList("value"));
        dailyReqDTO.setIsPrint(0);
        when(mockReserveClient.gather(dailyReqDTO)).thenReturn(gatherRespDTOS1);

        // Configure TakeoutDailyClientService.getReceiptStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getReceiptStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test
        final List<GatherRespDTO> result = businessDailyServiceImplUnderTest.gather(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGather_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        final GatherRespDTO gatherRespDTO = new GatherRespDTO();
        gatherRespDTO.setGatherCode(0);
        gatherRespDTO.setGatherName("gatherName");
        gatherRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO.setExcessAmount(new BigDecimal("0.00"));
        gatherRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO.setIsTotal(0);
        final List<GatherRespDTO> expectedResult = Arrays.asList(gatherRespDTO);

        // Configure DinnerDailyClientService.gather(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.gather(request1)).thenReturn(Collections.emptyList());

        // Configure ReserveClient.gather(...).
        final GatherRespDTO gatherRespDTO1 = new GatherRespDTO();
        gatherRespDTO1.setGatherCode(0);
        gatherRespDTO1.setGatherName("gatherName");
        gatherRespDTO1.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setExcessAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setIsTotal(0);
        final List<GatherRespDTO> gatherRespDTOS = Arrays.asList(gatherRespDTO1);
        final DailyReqDTO dailyReqDTO = new DailyReqDTO();
        dailyReqDTO.setDeviceType(0);
        dailyReqDTO.setDeviceId("deviceId");
        dailyReqDTO.setStoreGuid("storeGuid");
        dailyReqDTO.setBeginTime("beginTime");
        dailyReqDTO.setEndTime("endTime");
        dailyReqDTO.setQueryType(0);
        dailyReqDTO.setOrderItem(0);
        dailyReqDTO.setOrderType(0);
        dailyReqDTO.setIsMember(0);
        dailyReqDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        dailyReqDTO.setStoreGuids(Arrays.asList("value"));
        dailyReqDTO.setIsPrint(0);
        when(mockReserveClient.gather(dailyReqDTO)).thenReturn(gatherRespDTOS);

        // Configure TakeoutDailyClientService.getReceiptStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getReceiptStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test
        final List<GatherRespDTO> result = businessDailyServiceImplUnderTest.gather(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGather_ReserveClientReturnsNoItems() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        final GatherRespDTO gatherRespDTO = new GatherRespDTO();
        gatherRespDTO.setGatherCode(0);
        gatherRespDTO.setGatherName("gatherName");
        gatherRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO.setExcessAmount(new BigDecimal("0.00"));
        gatherRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO.setIsTotal(0);
        final List<GatherRespDTO> expectedResult = Arrays.asList(gatherRespDTO);

        // Configure DinnerDailyClientService.gather(...).
        final GatherRespDTO gatherRespDTO1 = new GatherRespDTO();
        gatherRespDTO1.setGatherCode(0);
        gatherRespDTO1.setGatherName("gatherName");
        gatherRespDTO1.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setExcessAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setIsTotal(0);
        final List<GatherRespDTO> gatherRespDTOS = Arrays.asList(gatherRespDTO1);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.gather(request1)).thenReturn(gatherRespDTOS);

        // Configure ReserveClient.gather(...).
        final DailyReqDTO dailyReqDTO = new DailyReqDTO();
        dailyReqDTO.setDeviceType(0);
        dailyReqDTO.setDeviceId("deviceId");
        dailyReqDTO.setStoreGuid("storeGuid");
        dailyReqDTO.setBeginTime("beginTime");
        dailyReqDTO.setEndTime("endTime");
        dailyReqDTO.setQueryType(0);
        dailyReqDTO.setOrderItem(0);
        dailyReqDTO.setOrderType(0);
        dailyReqDTO.setIsMember(0);
        dailyReqDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        dailyReqDTO.setStoreGuids(Arrays.asList("value"));
        dailyReqDTO.setIsPrint(0);
        when(mockReserveClient.gather(dailyReqDTO)).thenReturn(Collections.emptyList());

        // Configure TakeoutDailyClientService.getReceiptStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getReceiptStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test
        final List<GatherRespDTO> result = businessDailyServiceImplUnderTest.gather(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testOverview() {
        // Setup
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);

        final OverviewRespDTO expectedResult = new OverviewRespDTO();
        expectedResult.setCheckoutStaffs(Arrays.asList("value"));
        expectedResult.setOrderCount(0);
        expectedResult.setGuestCount(0);
        expectedResult.setConsumerAmount(new BigDecimal("0.00"));
        expectedResult.setGatherAmount(new BigDecimal("0.00"));
        expectedResult.setEstimatedAmount(new BigDecimal("0.00"));
        final AmountItemDTO amountItemDTO = new AmountItemDTO();
        amountItemDTO.setCode(0);
        amountItemDTO.setName("name");
        amountItemDTO.setAmount(new BigDecimal("0.00"));
        amountItemDTO.setExcessAmount(new BigDecimal("0.00"));
        amountItemDTO.setEstimatedAmount(new BigDecimal("0.00"));
        expectedResult.setGatherItems(Arrays.asList(amountItemDTO));
        expectedResult.setDiscountAmount(new BigDecimal("0.00"));
        final AmountItemDTO amountItemDTO1 = new AmountItemDTO();
        amountItemDTO1.setCode(0);
        amountItemDTO1.setName("name");
        amountItemDTO1.setAmount(new BigDecimal("0.00"));
        amountItemDTO1.setExcessAmount(new BigDecimal("0.00"));
        amountItemDTO1.setEstimatedAmount(new BigDecimal("0.00"));
        expectedResult.setDiscountItems(Arrays.asList(amountItemDTO1));
        expectedResult.setMtGrouponEstimatedAmount(new BigDecimal("0.00"));
        expectedResult.setGrossProfitAmount(new BigDecimal("0.00"));
        expectedResult.setCostAmount(new BigDecimal("0.00"));

        // Configure DinnerDailyClientService.overview(...).
        final OverviewRespDTO overviewRespDTO = new OverviewRespDTO();
        overviewRespDTO.setCheckoutStaffs(Arrays.asList("value"));
        overviewRespDTO.setOrderCount(0);
        overviewRespDTO.setGuestCount(0);
        overviewRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        overviewRespDTO.setGatherAmount(new BigDecimal("0.00"));
        overviewRespDTO.setEstimatedAmount(new BigDecimal("0.00"));
        final AmountItemDTO amountItemDTO2 = new AmountItemDTO();
        amountItemDTO2.setCode(0);
        amountItemDTO2.setName("name");
        amountItemDTO2.setAmount(new BigDecimal("0.00"));
        amountItemDTO2.setExcessAmount(new BigDecimal("0.00"));
        amountItemDTO2.setEstimatedAmount(new BigDecimal("0.00"));
        overviewRespDTO.setGatherItems(Arrays.asList(amountItemDTO2));
        overviewRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        final AmountItemDTO amountItemDTO3 = new AmountItemDTO();
        amountItemDTO3.setCode(0);
        amountItemDTO3.setName("name");
        amountItemDTO3.setAmount(new BigDecimal("0.00"));
        amountItemDTO3.setExcessAmount(new BigDecimal("0.00"));
        amountItemDTO3.setEstimatedAmount(new BigDecimal("0.00"));
        overviewRespDTO.setDiscountItems(Arrays.asList(amountItemDTO3));
        overviewRespDTO.setMtGrouponEstimatedAmount(new BigDecimal("0.00"));
        overviewRespDTO.setGrossProfitAmount(new BigDecimal("0.00"));
        overviewRespDTO.setCostAmount(new BigDecimal("0.00"));
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.overview(request1)).thenReturn(overviewRespDTO);

        // Configure TakeoutDailyClientService.getOpStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getOpStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Run the test
        final OverviewRespDTO result = businessDailyServiceImplUnderTest.overview(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testPrint() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);
        request.setType(0);

        // Configure DinnerDailyClientService.overview(...).
        final OverviewRespDTO overviewRespDTO = new OverviewRespDTO();
        overviewRespDTO.setCheckoutStaffs(Arrays.asList("value"));
        overviewRespDTO.setOrderCount(0);
        overviewRespDTO.setGuestCount(0);
        overviewRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        overviewRespDTO.setGatherAmount(new BigDecimal("0.00"));
        overviewRespDTO.setEstimatedAmount(new BigDecimal("0.00"));
        final AmountItemDTO amountItemDTO = new AmountItemDTO();
        amountItemDTO.setCode(0);
        amountItemDTO.setName("name");
        amountItemDTO.setAmount(new BigDecimal("0.00"));
        amountItemDTO.setExcessAmount(new BigDecimal("0.00"));
        amountItemDTO.setEstimatedAmount(new BigDecimal("0.00"));
        overviewRespDTO.setGatherItems(Arrays.asList(amountItemDTO));
        overviewRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        final AmountItemDTO amountItemDTO1 = new AmountItemDTO();
        amountItemDTO1.setCode(0);
        amountItemDTO1.setName("name");
        amountItemDTO1.setAmount(new BigDecimal("0.00"));
        amountItemDTO1.setExcessAmount(new BigDecimal("0.00"));
        amountItemDTO1.setEstimatedAmount(new BigDecimal("0.00"));
        overviewRespDTO.setDiscountItems(Arrays.asList(amountItemDTO1));
        overviewRespDTO.setMtGrouponEstimatedAmount(new BigDecimal("0.00"));
        overviewRespDTO.setGrossProfitAmount(new BigDecimal("0.00"));
        overviewRespDTO.setCostAmount(new BigDecimal("0.00"));
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.overview(request1)).thenReturn(overviewRespDTO);

        // Configure TakeoutDailyClientService.getOpStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getOpStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Configure DinnerDailyClientService.gather(...).
        final GatherRespDTO gatherRespDTO = new GatherRespDTO();
        gatherRespDTO.setGatherCode(0);
        gatherRespDTO.setGatherName("gatherName");
        gatherRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO.setExcessAmount(new BigDecimal("0.00"));
        gatherRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO.setIsTotal(0);
        final List<GatherRespDTO> gatherRespDTOS = Arrays.asList(gatherRespDTO);
        final DailyReqDTO request2 = new DailyReqDTO();
        request2.setDeviceType(0);
        request2.setDeviceId("deviceId");
        request2.setStoreGuid("storeGuid");
        request2.setBeginTime("beginTime");
        request2.setEndTime("endTime");
        request2.setQueryType(0);
        request2.setOrderItem(0);
        request2.setOrderType(0);
        request2.setIsMember(0);
        request2.setCheckoutStaffGuids(Arrays.asList("value"));
        request2.setStoreGuids(Arrays.asList("value"));
        request2.setIsPrint(0);
        when(mockDinnerDailyClientService.gather(request2)).thenReturn(gatherRespDTOS);

        // Configure ReserveClient.gather(...).
        final GatherRespDTO gatherRespDTO1 = new GatherRespDTO();
        gatherRespDTO1.setGatherCode(0);
        gatherRespDTO1.setGatherName("gatherName");
        gatherRespDTO1.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setExcessAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO1.setIsTotal(0);
        final List<GatherRespDTO> gatherRespDTOS1 = Arrays.asList(gatherRespDTO1);
        final DailyReqDTO dailyReqDTO = new DailyReqDTO();
        dailyReqDTO.setDeviceType(0);
        dailyReqDTO.setDeviceId("deviceId");
        dailyReqDTO.setStoreGuid("storeGuid");
        dailyReqDTO.setBeginTime("beginTime");
        dailyReqDTO.setEndTime("endTime");
        dailyReqDTO.setQueryType(0);
        dailyReqDTO.setOrderItem(0);
        dailyReqDTO.setOrderType(0);
        dailyReqDTO.setIsMember(0);
        dailyReqDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        dailyReqDTO.setStoreGuids(Arrays.asList("value"));
        dailyReqDTO.setIsPrint(0);
        when(mockReserveClient.gather(dailyReqDTO)).thenReturn(gatherRespDTOS1);

        // Configure TakeoutDailyClientService.getReceiptStats(...).
        final TakeoutStatsDTO takeoutStatsDTO1 = new TakeoutStatsDTO();
        takeoutStatsDTO1.setOrderCount(0);
        takeoutStatsDTO1.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO1.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO1.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO1.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO1.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO1.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO1.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO1 = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO1.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO1.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO1.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO1.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getReceiptStats(takeoutStatsQueryDTO1)).thenReturn(takeoutStatsDTO1);

        // Configure DinnerDailyClientService.memberConsume(...).
        final MemberConsumeRespDTO memberConsumeRespDTO = new MemberConsumeRespDTO();
        memberConsumeRespDTO.setConsumerCount(0);
        memberConsumeRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidCount(0);
        memberConsumeRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidGiveAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidTakeInAmount(new BigDecimal("0.00"));
        final AmountItemDTO amountItemDTO2 = new AmountItemDTO();
        amountItemDTO2.setCode(0);
        amountItemDTO2.setName("name");
        amountItemDTO2.setAmount(new BigDecimal("0.00"));
        amountItemDTO2.setExcessAmount(new BigDecimal("0.00"));
        amountItemDTO2.setEstimatedAmount(new BigDecimal("0.00"));
        memberConsumeRespDTO.setPrepaidItems(Arrays.asList(amountItemDTO2));
        final DailyReqDTO request3 = new DailyReqDTO();
        request3.setDeviceType(0);
        request3.setDeviceId("deviceId");
        request3.setStoreGuid("storeGuid");
        request3.setBeginTime("beginTime");
        request3.setEndTime("endTime");
        request3.setQueryType(0);
        request3.setOrderItem(0);
        request3.setOrderType(0);
        request3.setIsMember(0);
        request3.setCheckoutStaffGuids(Arrays.asList("value"));
        request3.setStoreGuids(Arrays.asList("value"));
        request3.setIsPrint(0);
        when(mockDinnerDailyClientService.memberConsume(request3)).thenReturn(memberConsumeRespDTO);

        // Configure DinnerDailyClientService.diningType(...).
        final DiningTypeRespDTO diningTypeRespDTO = new DiningTypeRespDTO();
        diningTypeRespDTO.setTypeCode(0);
        diningTypeRespDTO.setTypeName("合计");
        diningTypeRespDTO.setOrderCount(0);
        diningTypeRespDTO.setGuestCount(0);
        diningTypeRespDTO.setAmount(new BigDecimal("0.00"));
        diningTypeRespDTO.setOrderPrice(new BigDecimal("0.00"));
        diningTypeRespDTO.setGuestPrice(new BigDecimal("0.00"));
        diningTypeRespDTO.setSubDiningTypes(Arrays.asList(new DiningTypeRespDTO()));
        diningTypeRespDTO.setIsTotal(0);
        final List<DiningTypeRespDTO> diningTypeRespDTOS = Arrays.asList(diningTypeRespDTO);
        final DailyReqDTO request4 = new DailyReqDTO();
        request4.setDeviceType(0);
        request4.setDeviceId("deviceId");
        request4.setStoreGuid("storeGuid");
        request4.setBeginTime("beginTime");
        request4.setEndTime("endTime");
        request4.setQueryType(0);
        request4.setOrderItem(0);
        request4.setOrderType(0);
        request4.setIsMember(0);
        request4.setCheckoutStaffGuids(Arrays.asList("value"));
        request4.setStoreGuids(Arrays.asList("value"));
        request4.setIsPrint(0);
        when(mockDinnerDailyClientService.diningType(request4)).thenReturn(diningTypeRespDTOS);

        // Configure TakeoutDailyClientService.getTradeStats(...).
        final TakeoutStatsDTO takeoutStatsDTO2 = new TakeoutStatsDTO();
        takeoutStatsDTO2.setOrderCount(0);
        takeoutStatsDTO2.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO2.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO2.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO2.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO2.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO2.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO2.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO2 = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO2.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO2.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO2.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO2.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO2.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getTradeStats(takeoutStatsQueryDTO2)).thenReturn(takeoutStatsDTO2);

        // Configure DinnerDailyClientService.classify(...).
        final ItemRespDTO dto = new ItemRespDTO();
        dto.setItemType(0);
        dto.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto.setName("");
        dto.setUnitPrice(new BigDecimal("0.00"));
        dto.setQuantum(new BigDecimal("0.00"));
        dto.setAmount(new BigDecimal("0.00"));
        dto.setDiscountAmount(new BigDecimal("0.00"));
        dto.setIsTotal(0);
        dto.setHasSubInfo(0);
        dto.setSubs(Arrays.asList(new ItemRespDTO()));
        dto.setSkuName("skuName");
        dto.setDinnerQuantum(new BigDecimal("0.00"));
        dto.setDinnerAmount(new BigDecimal("0.00"));
        dto.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutQuantum(new BigDecimal("0.00"));
        dto.setTakeoutAmount(new BigDecimal("0.00"));
        dto.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> list = Arrays.asList(dto);
        final DailyReqDTO request5 = new DailyReqDTO();
        request5.setDeviceType(0);
        request5.setDeviceId("deviceId");
        request5.setStoreGuid("storeGuid");
        request5.setBeginTime("beginTime");
        request5.setEndTime("endTime");
        request5.setQueryType(0);
        request5.setOrderItem(0);
        request5.setOrderType(0);
        request5.setIsMember(0);
        request5.setCheckoutStaffGuids(Arrays.asList("value"));
        request5.setStoreGuids(Arrays.asList("value"));
        request5.setIsPrint(0);
        when(mockDinnerDailyClientService.classify(request5)).thenReturn(list);

        // Configure TakeoutDailyClientService.listItemSale(...).
        final ItemRespDTO dto1 = new ItemRespDTO();
        dto1.setItemType(0);
        dto1.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto1.setName("");
        dto1.setUnitPrice(new BigDecimal("0.00"));
        dto1.setQuantum(new BigDecimal("0.00"));
        dto1.setAmount(new BigDecimal("0.00"));
        dto1.setDiscountAmount(new BigDecimal("0.00"));
        dto1.setIsTotal(0);
        dto1.setHasSubInfo(0);
        dto1.setSubs(Arrays.asList(new ItemRespDTO()));
        dto1.setSkuName("skuName");
        dto1.setDinnerQuantum(new BigDecimal("0.00"));
        dto1.setDinnerAmount(new BigDecimal("0.00"));
        dto1.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto1.setTakeoutQuantum(new BigDecimal("0.00"));
        dto1.setTakeoutAmount(new BigDecimal("0.00"));
        dto1.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto1.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> list1 = Arrays.asList(dto1);
        final DailyReqDTO takeoutStatsQueryDTO3 = new DailyReqDTO();
        takeoutStatsQueryDTO3.setDeviceType(0);
        takeoutStatsQueryDTO3.setDeviceId("deviceId");
        takeoutStatsQueryDTO3.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO3.setBeginTime("beginTime");
        takeoutStatsQueryDTO3.setEndTime("endTime");
        takeoutStatsQueryDTO3.setQueryType(0);
        takeoutStatsQueryDTO3.setOrderItem(0);
        takeoutStatsQueryDTO3.setOrderType(0);
        takeoutStatsQueryDTO3.setIsMember(0);
        takeoutStatsQueryDTO3.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO3.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO3.setIsPrint(0);
        when(mockTakeoutDailyClientService.listItemSale(takeoutStatsQueryDTO3)).thenReturn(list1);

        // Configure DinnerDailyClientService.goods(...).
        final ItemRespDTO dto2 = new ItemRespDTO();
        dto2.setItemType(0);
        dto2.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto2.setName("");
        dto2.setUnitPrice(new BigDecimal("0.00"));
        dto2.setQuantum(new BigDecimal("0.00"));
        dto2.setAmount(new BigDecimal("0.00"));
        dto2.setDiscountAmount(new BigDecimal("0.00"));
        dto2.setIsTotal(0);
        dto2.setHasSubInfo(0);
        dto2.setSubs(Arrays.asList(new ItemRespDTO()));
        dto2.setSkuName("skuName");
        dto2.setDinnerQuantum(new BigDecimal("0.00"));
        dto2.setDinnerAmount(new BigDecimal("0.00"));
        dto2.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto2.setTakeoutQuantum(new BigDecimal("0.00"));
        dto2.setTakeoutAmount(new BigDecimal("0.00"));
        dto2.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto2.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> list2 = Arrays.asList(dto2);
        final DailyReqDTO request6 = new DailyReqDTO();
        request6.setDeviceType(0);
        request6.setDeviceId("deviceId");
        request6.setStoreGuid("storeGuid");
        request6.setBeginTime("beginTime");
        request6.setEndTime("endTime");
        request6.setQueryType(0);
        request6.setOrderItem(0);
        request6.setOrderType(0);
        request6.setIsMember(0);
        request6.setCheckoutStaffGuids(Arrays.asList("value"));
        request6.setStoreGuids(Arrays.asList("value"));
        request6.setIsPrint(0);
        when(mockDinnerDailyClientService.goods(request6)).thenReturn(list2);

        // Configure TakeoutDailyClientService.goods(...).
        final ItemRespDTO dto3 = new ItemRespDTO();
        dto3.setItemType(0);
        dto3.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto3.setName("");
        dto3.setUnitPrice(new BigDecimal("0.00"));
        dto3.setQuantum(new BigDecimal("0.00"));
        dto3.setAmount(new BigDecimal("0.00"));
        dto3.setDiscountAmount(new BigDecimal("0.00"));
        dto3.setIsTotal(0);
        dto3.setHasSubInfo(0);
        dto3.setSubs(Arrays.asList(new ItemRespDTO()));
        dto3.setSkuName("skuName");
        dto3.setDinnerQuantum(new BigDecimal("0.00"));
        dto3.setDinnerAmount(new BigDecimal("0.00"));
        dto3.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto3.setTakeoutQuantum(new BigDecimal("0.00"));
        dto3.setTakeoutAmount(new BigDecimal("0.00"));
        dto3.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto3.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> list3 = Arrays.asList(dto3);
        final DailyReqDTO takeoutStatsQueryDTO4 = new DailyReqDTO();
        takeoutStatsQueryDTO4.setDeviceType(0);
        takeoutStatsQueryDTO4.setDeviceId("deviceId");
        takeoutStatsQueryDTO4.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO4.setBeginTime("beginTime");
        takeoutStatsQueryDTO4.setEndTime("endTime");
        takeoutStatsQueryDTO4.setQueryType(0);
        takeoutStatsQueryDTO4.setOrderItem(0);
        takeoutStatsQueryDTO4.setOrderType(0);
        takeoutStatsQueryDTO4.setIsMember(0);
        takeoutStatsQueryDTO4.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO4.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO4.setIsPrint(0);
        when(mockTakeoutDailyClientService.goods(takeoutStatsQueryDTO4)).thenReturn(list3);

        // Configure DinnerDailyClientService.attr(...).
        final AttrItemRespDTO attrItemRespDTO = new AttrItemRespDTO();
        attrItemRespDTO.setItemType(0);
        attrItemRespDTO.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        attrItemRespDTO.setName("");
        attrItemRespDTO.setUnitPrice(new BigDecimal("0.00"));
        attrItemRespDTO.setQuantum(new BigDecimal("0.00"));
        attrItemRespDTO.setAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setDiscountAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setIsTotal(0);
        attrItemRespDTO.setHasSubInfo(0);
        final ItemRespDTO dto4 = new ItemRespDTO();
        dto4.setItemType(0);
        dto4.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto4.setName("");
        dto4.setUnitPrice(new BigDecimal("0.00"));
        dto4.setQuantum(new BigDecimal("0.00"));
        dto4.setAmount(new BigDecimal("0.00"));
        dto4.setDiscountAmount(new BigDecimal("0.00"));
        dto4.setIsTotal(0);
        dto4.setHasSubInfo(0);
        dto4.setSubs(Arrays.asList(new ItemRespDTO()));
        dto4.setSkuName("skuName");
        dto4.setDinnerQuantum(new BigDecimal("0.00"));
        dto4.setDinnerAmount(new BigDecimal("0.00"));
        dto4.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto4.setTakeoutQuantum(new BigDecimal("0.00"));
        dto4.setTakeoutAmount(new BigDecimal("0.00"));
        dto4.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto4.setTakeoutUnitPrice(new BigDecimal("0.00"));
        attrItemRespDTO.setSubs(Arrays.asList(dto4));
        attrItemRespDTO.setSkuName("skuName");
        attrItemRespDTO.setDinnerQuantum(new BigDecimal("0.00"));
        attrItemRespDTO.setDinnerAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setDinnerDiscountAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setTakeoutQuantum(new BigDecimal("0.00"));
        attrItemRespDTO.setTakeoutAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        attrItemRespDTO.setTakeoutUnitPrice(new BigDecimal("0.00"));
        attrItemRespDTO.setAttrGroupName("attrGroupName");
        attrItemRespDTO.setAttrs(Arrays.asList(new AttrItemRespDTO()));
        final List<AttrItemRespDTO> attrItemRespDTOS = Arrays.asList(attrItemRespDTO);
        final DailyReqDTO request7 = new DailyReqDTO();
        request7.setDeviceType(0);
        request7.setDeviceId("deviceId");
        request7.setStoreGuid("storeGuid");
        request7.setBeginTime("beginTime");
        request7.setEndTime("endTime");
        request7.setQueryType(0);
        request7.setOrderItem(0);
        request7.setOrderType(0);
        request7.setIsMember(0);
        request7.setCheckoutStaffGuids(Arrays.asList("value"));
        request7.setStoreGuids(Arrays.asList("value"));
        request7.setIsPrint(0);
        when(mockDinnerDailyClientService.attr(request7)).thenReturn(attrItemRespDTOS);

        // Configure DinnerDailyClientService.returnVegetables(...).
        final ItemRespDTO dto5 = new ItemRespDTO();
        dto5.setItemType(0);
        dto5.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto5.setName("");
        dto5.setUnitPrice(new BigDecimal("0.00"));
        dto5.setQuantum(new BigDecimal("0.00"));
        dto5.setAmount(new BigDecimal("0.00"));
        dto5.setDiscountAmount(new BigDecimal("0.00"));
        dto5.setIsTotal(0);
        dto5.setHasSubInfo(0);
        dto5.setSubs(Arrays.asList(new ItemRespDTO()));
        dto5.setSkuName("skuName");
        dto5.setDinnerQuantum(new BigDecimal("0.00"));
        dto5.setDinnerAmount(new BigDecimal("0.00"));
        dto5.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto5.setTakeoutQuantum(new BigDecimal("0.00"));
        dto5.setTakeoutAmount(new BigDecimal("0.00"));
        dto5.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto5.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> list4 = Arrays.asList(dto5);
        final DailyReqDTO request8 = new DailyReqDTO();
        request8.setDeviceType(0);
        request8.setDeviceId("deviceId");
        request8.setStoreGuid("storeGuid");
        request8.setBeginTime("beginTime");
        request8.setEndTime("endTime");
        request8.setQueryType(0);
        request8.setOrderItem(0);
        request8.setOrderType(0);
        request8.setIsMember(0);
        request8.setCheckoutStaffGuids(Arrays.asList("value"));
        request8.setStoreGuids(Arrays.asList("value"));
        request8.setIsPrint(0);
        when(mockDinnerDailyClientService.returnVegetables(request8)).thenReturn(list4);

        // Configure DinnerDailyClientService.dishGiving(...).
        final ItemRespDTO dto6 = new ItemRespDTO();
        dto6.setItemType(0);
        dto6.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto6.setName("");
        dto6.setUnitPrice(new BigDecimal("0.00"));
        dto6.setQuantum(new BigDecimal("0.00"));
        dto6.setAmount(new BigDecimal("0.00"));
        dto6.setDiscountAmount(new BigDecimal("0.00"));
        dto6.setIsTotal(0);
        dto6.setHasSubInfo(0);
        dto6.setSubs(Arrays.asList(new ItemRespDTO()));
        dto6.setSkuName("skuName");
        dto6.setDinnerQuantum(new BigDecimal("0.00"));
        dto6.setDinnerAmount(new BigDecimal("0.00"));
        dto6.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto6.setTakeoutQuantum(new BigDecimal("0.00"));
        dto6.setTakeoutAmount(new BigDecimal("0.00"));
        dto6.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto6.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> list5 = Arrays.asList(dto6);
        final DailyReqDTO request9 = new DailyReqDTO();
        request9.setDeviceType(0);
        request9.setDeviceId("deviceId");
        request9.setStoreGuid("storeGuid");
        request9.setBeginTime("beginTime");
        request9.setEndTime("endTime");
        request9.setQueryType(0);
        request9.setOrderItem(0);
        request9.setOrderType(0);
        request9.setIsMember(0);
        request9.setCheckoutStaffGuids(Arrays.asList("value"));
        request9.setStoreGuids(Arrays.asList("value"));
        request9.setIsPrint(0);
        when(mockDinnerDailyClientService.dishGiving(request9)).thenReturn(list5);

        // Configure MemberDataClientService.queryOnlyRecharge(...).
        final ResponseRechargeStatis responseRechargeStatis = new ResponseRechargeStatis();
        responseRechargeStatis.setRechargeNum(0L);
        responseRechargeStatis.setRechargeAmount(new BigDecimal("0.00"));
        responseRechargeStatis.setPresentAmount(new BigDecimal("0.00"));
        responseRechargeStatis.setIncomeAmount(new BigDecimal("0.00"));
        responseRechargeStatis.setRechargeMemberNum(0);
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        responsePayWayDetail.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail.setPayName("payName");
        responsePayWayDetail.setExcessAmount(new BigDecimal("0.00"));
        responseRechargeStatis.setPayWayDetailList(Arrays.asList(responsePayWayDetail));
        final RequestMemberDaily reqDTO = new RequestMemberDaily();
        reqDTO.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setStoreGuid("storeGuid");
        reqDTO.setConsumpType(0);
        when(mockDataClientService.queryOnlyRecharge(reqDTO)).thenReturn(responseRechargeStatis);

        // Configure MemberDataClientService.queryOnlyConsumption(...).
        final ResponseConsumpStatis responseConsumpStatis = new ResponseConsumpStatis();
        responseConsumpStatis.setConsumptionNum(0L);
        responseConsumpStatis.setConsumptionAmount(new BigDecimal("0.00"));
        responseConsumpStatis.setConsumptionMemberNum(0);
        final ResponsePayWayDetail responsePayWayDetail1 = new ResponsePayWayDetail();
        responsePayWayDetail1.setPayWay(0);
        responsePayWayDetail1.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail1.setPayName("payName");
        responsePayWayDetail1.setExcessAmount(new BigDecimal("0.00"));
        responseConsumpStatis.setPayWayDetailList(Arrays.asList(responsePayWayDetail1));
        final RequestMemberDaily reqDTO1 = new RequestMemberDaily();
        reqDTO1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setStoreGuid("storeGuid");
        reqDTO1.setConsumpType(0);
        when(mockDataClientService.queryOnlyConsumption(reqDTO1)).thenReturn(responseConsumpStatis);

        // Configure DinnerDailyClientService.listByRequest(...).
        final List<AmountItemDTO> amountItemDTOS = Arrays.asList(
                new AmountItemDTO(0, "name", new BigDecimal("0.00"), new BigDecimal("0.00")));
        final DailyReqDTO request10 = new DailyReqDTO();
        request10.setDeviceType(0);
        request10.setDeviceId("deviceId");
        request10.setStoreGuid("storeGuid");
        request10.setBeginTime("beginTime");
        request10.setEndTime("endTime");
        request10.setQueryType(0);
        request10.setOrderItem(0);
        request10.setOrderType(0);
        request10.setIsMember(0);
        request10.setCheckoutStaffGuids(Arrays.asList("value"));
        request10.setStoreGuids(Arrays.asList("value"));
        request10.setIsPrint(0);
        when(mockDinnerDailyClientService.listByRequest(request10)).thenReturn(amountItemDTOS);

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result result = mockBusinessDailyPrintServiceImpl.print(request);

        // Verify the results
    }

    @Test
    public void testPrint_DinnerDailyClientServiceGatherReturnsNoItems() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);
        request.setType(0);

        // Configure DinnerDailyClientService.gather(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.gather(request1)).thenReturn(Collections.emptyList());

        // Configure ReserveClient.gather(...).
        final GatherRespDTO gatherRespDTO = new GatherRespDTO();
        gatherRespDTO.setGatherCode(0);
        gatherRespDTO.setGatherName("gatherName");
        gatherRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO.setExcessAmount(new BigDecimal("0.00"));
        gatherRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO.setIsTotal(0);
        final List<GatherRespDTO> gatherRespDTOS = Arrays.asList(gatherRespDTO);
        final DailyReqDTO dailyReqDTO = new DailyReqDTO();
        dailyReqDTO.setDeviceType(0);
        dailyReqDTO.setDeviceId("deviceId");
        dailyReqDTO.setStoreGuid("storeGuid");
        dailyReqDTO.setBeginTime("beginTime");
        dailyReqDTO.setEndTime("endTime");
        dailyReqDTO.setQueryType(0);
        dailyReqDTO.setOrderItem(0);
        dailyReqDTO.setOrderType(0);
        dailyReqDTO.setIsMember(0);
        dailyReqDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        dailyReqDTO.setStoreGuids(Arrays.asList("value"));
        dailyReqDTO.setIsPrint(0);
        when(mockReserveClient.gather(dailyReqDTO)).thenReturn(gatherRespDTOS);

        // Configure TakeoutDailyClientService.getReceiptStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getReceiptStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result result = mockBusinessDailyPrintServiceImpl.print(request);

        // Verify the results
    }

    @Test
    public void testPrint_ReserveClientReturnsNoItems() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);
        request.setType(0);

        // Configure DinnerDailyClientService.gather(...).
        final GatherRespDTO gatherRespDTO = new GatherRespDTO();
        gatherRespDTO.setGatherCode(0);
        gatherRespDTO.setGatherName("gatherName");
        gatherRespDTO.setConsumerAmount(new BigDecimal("0.00"));
        gatherRespDTO.setExcessAmount(new BigDecimal("0.00"));
        gatherRespDTO.setPrepaidAmount(new BigDecimal("0.00"));
        gatherRespDTO.setReserveAmount(new BigDecimal("0.00"));
        gatherRespDTO.setTotalAmount(new BigDecimal("0.00"));
        gatherRespDTO.setIsTotal(0);
        final List<GatherRespDTO> gatherRespDTOS = Arrays.asList(gatherRespDTO);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.gather(request1)).thenReturn(gatherRespDTOS);

        // Configure ReserveClient.gather(...).
        final DailyReqDTO dailyReqDTO = new DailyReqDTO();
        dailyReqDTO.setDeviceType(0);
        dailyReqDTO.setDeviceId("deviceId");
        dailyReqDTO.setStoreGuid("storeGuid");
        dailyReqDTO.setBeginTime("beginTime");
        dailyReqDTO.setEndTime("endTime");
        dailyReqDTO.setQueryType(0);
        dailyReqDTO.setOrderItem(0);
        dailyReqDTO.setOrderType(0);
        dailyReqDTO.setIsMember(0);
        dailyReqDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        dailyReqDTO.setStoreGuids(Arrays.asList("value"));
        dailyReqDTO.setIsPrint(0);
        when(mockReserveClient.gather(dailyReqDTO)).thenReturn(Collections.emptyList());

        // Configure TakeoutDailyClientService.getReceiptStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getReceiptStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result result = mockBusinessDailyPrintServiceImpl.print(request);

        // Verify the results
    }

    @Test
    public void testPrint_DinnerDailyClientServiceDiningTypeReturnsNull() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);
        request.setType(0);

        // Configure DinnerDailyClientService.diningType(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.diningType(request1)).thenReturn(null);

        // Configure TakeoutDailyClientService.getTradeStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getTradeStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result result = mockBusinessDailyPrintServiceImpl.print(request);

        // Verify the results
    }

    @Test
    public void testPrint_DinnerDailyClientServiceDiningTypeReturnsNoItems() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);
        request.setType(0);

        // Configure DinnerDailyClientService.diningType(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.diningType(request1)).thenReturn(Collections.emptyList());

        // Configure TakeoutDailyClientService.getTradeStats(...).
        final TakeoutStatsDTO takeoutStatsDTO = new TakeoutStatsDTO();
        takeoutStatsDTO.setOrderCount(0);
        takeoutStatsDTO.setSalesIncoming(new BigDecimal("0.00"));
        takeoutStatsDTO.setCostAmount(new BigDecimal("0.00"));
        takeoutStatsDTO.setOrderCountDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingDetail(new HashMap<>());
        takeoutStatsDTO.setSalesIncomingAvgDetail(new HashMap<>());
        takeoutStatsDTO.setDiscountAmountDetail(new HashMap<>());
        takeoutStatsDTO.setShopAmountDetail(new HashMap<>());
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getTradeStats(takeoutStatsQueryDTO)).thenReturn(takeoutStatsDTO);

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result result = mockBusinessDailyPrintServiceImpl.print(request);

        // Verify the results
    }

    @Test
    public void testPrint_TakeoutDailyClientServiceGetTradeStatsReturnsNull() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);
        request.setType(0);

        // Configure DinnerDailyClientService.diningType(...).
        final DiningTypeRespDTO diningTypeRespDTO = new DiningTypeRespDTO();
        diningTypeRespDTO.setTypeCode(0);
        diningTypeRespDTO.setTypeName("合计");
        diningTypeRespDTO.setOrderCount(0);
        diningTypeRespDTO.setGuestCount(0);
        diningTypeRespDTO.setAmount(new BigDecimal("0.00"));
        diningTypeRespDTO.setOrderPrice(new BigDecimal("0.00"));
        diningTypeRespDTO.setGuestPrice(new BigDecimal("0.00"));
        diningTypeRespDTO.setSubDiningTypes(Arrays.asList(new DiningTypeRespDTO()));
        diningTypeRespDTO.setIsTotal(0);
        final List<DiningTypeRespDTO> diningTypeRespDTOS = Arrays.asList(diningTypeRespDTO);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.diningType(request1)).thenReturn(diningTypeRespDTOS);

        // Configure TakeoutDailyClientService.getTradeStats(...).
        final TakeoutStatsQueryDTO takeoutStatsQueryDTO = new TakeoutStatsQueryDTO();
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStaffGuids(Arrays.asList("value"));
        when(mockTakeoutDailyClientService.getTradeStats(takeoutStatsQueryDTO)).thenReturn(null);

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result result = mockBusinessDailyPrintServiceImpl.print(request);

        // Verify the results
    }

    @Test
    public void testPrint_DinnerDailyClientServiceClassifyReturnsNoItems() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);
        request.setType(0);

        // Configure DinnerDailyClientService.classify(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.classify(request1)).thenReturn(Collections.emptyList());

        // Configure TakeoutDailyClientService.listItemSale(...).
        final ItemRespDTO dto = new ItemRespDTO();
        dto.setItemType(0);
        dto.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto.setName("");
        dto.setUnitPrice(new BigDecimal("0.00"));
        dto.setQuantum(new BigDecimal("0.00"));
        dto.setAmount(new BigDecimal("0.00"));
        dto.setDiscountAmount(new BigDecimal("0.00"));
        dto.setIsTotal(0);
        dto.setHasSubInfo(0);
        dto.setSubs(Arrays.asList(new ItemRespDTO()));
        dto.setSkuName("skuName");
        dto.setDinnerQuantum(new BigDecimal("0.00"));
        dto.setDinnerAmount(new BigDecimal("0.00"));
        dto.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutQuantum(new BigDecimal("0.00"));
        dto.setTakeoutAmount(new BigDecimal("0.00"));
        dto.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> list = Arrays.asList(dto);
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setDeviceType(0);
        takeoutStatsQueryDTO.setDeviceId("deviceId");
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setIsPrint(0);
        when(mockTakeoutDailyClientService.listItemSale(takeoutStatsQueryDTO)).thenReturn(list);

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result result = mockBusinessDailyPrintServiceImpl.print(request);

        // Verify the results
    }

    @Test
    public void testPrint_TakeoutDailyClientServiceListItemSaleReturnsNoItems() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);
        request.setType(0);

        // Configure DinnerDailyClientService.classify(...).
        final ItemRespDTO dto = new ItemRespDTO();
        dto.setItemType(0);
        dto.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto.setName("");
        dto.setUnitPrice(new BigDecimal("0.00"));
        dto.setQuantum(new BigDecimal("0.00"));
        dto.setAmount(new BigDecimal("0.00"));
        dto.setDiscountAmount(new BigDecimal("0.00"));
        dto.setIsTotal(0);
        dto.setHasSubInfo(0);
        dto.setSubs(Arrays.asList(new ItemRespDTO()));
        dto.setSkuName("skuName");
        dto.setDinnerQuantum(new BigDecimal("0.00"));
        dto.setDinnerAmount(new BigDecimal("0.00"));
        dto.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutQuantum(new BigDecimal("0.00"));
        dto.setTakeoutAmount(new BigDecimal("0.00"));
        dto.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> list = Arrays.asList(dto);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.classify(request1)).thenReturn(list);

        // Configure TakeoutDailyClientService.listItemSale(...).
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setDeviceType(0);
        takeoutStatsQueryDTO.setDeviceId("deviceId");
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setIsPrint(0);
        when(mockTakeoutDailyClientService.listItemSale(takeoutStatsQueryDTO)).thenReturn(Collections.emptyList());

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result result = mockBusinessDailyPrintServiceImpl.print(request);

        // Verify the results
    }

    @Test
    public void testPrint_DinnerDailyClientServiceGoodsReturnsNoItems() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);
        request.setType(0);

        // Configure DinnerDailyClientService.goods(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.goods(request1)).thenReturn(Collections.emptyList());

        // Configure TakeoutDailyClientService.goods(...).
        final ItemRespDTO dto = new ItemRespDTO();
        dto.setItemType(0);
        dto.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto.setName("");
        dto.setUnitPrice(new BigDecimal("0.00"));
        dto.setQuantum(new BigDecimal("0.00"));
        dto.setAmount(new BigDecimal("0.00"));
        dto.setDiscountAmount(new BigDecimal("0.00"));
        dto.setIsTotal(0);
        dto.setHasSubInfo(0);
        dto.setSubs(Arrays.asList(new ItemRespDTO()));
        dto.setSkuName("skuName");
        dto.setDinnerQuantum(new BigDecimal("0.00"));
        dto.setDinnerAmount(new BigDecimal("0.00"));
        dto.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutQuantum(new BigDecimal("0.00"));
        dto.setTakeoutAmount(new BigDecimal("0.00"));
        dto.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> list = Arrays.asList(dto);
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setDeviceType(0);
        takeoutStatsQueryDTO.setDeviceId("deviceId");
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setIsPrint(0);
        when(mockTakeoutDailyClientService.goods(takeoutStatsQueryDTO)).thenReturn(list);

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result result = mockBusinessDailyPrintServiceImpl.print(request);

        // Verify the results
    }

    @Test
    public void testPrint_TakeoutDailyClientServiceGoodsReturnsNoItems() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);
        request.setType(0);

        // Configure DinnerDailyClientService.goods(...).
        final ItemRespDTO dto = new ItemRespDTO();
        dto.setItemType(0);
        dto.setGuid("ba8b0a15-aeee-4353-988c-9d15f029c15a");
        dto.setName("");
        dto.setUnitPrice(new BigDecimal("0.00"));
        dto.setQuantum(new BigDecimal("0.00"));
        dto.setAmount(new BigDecimal("0.00"));
        dto.setDiscountAmount(new BigDecimal("0.00"));
        dto.setIsTotal(0);
        dto.setHasSubInfo(0);
        dto.setSubs(Arrays.asList(new ItemRespDTO()));
        dto.setSkuName("skuName");
        dto.setDinnerQuantum(new BigDecimal("0.00"));
        dto.setDinnerAmount(new BigDecimal("0.00"));
        dto.setDinnerDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutQuantum(new BigDecimal("0.00"));
        dto.setTakeoutAmount(new BigDecimal("0.00"));
        dto.setTakeoutDiscountAmount(new BigDecimal("0.00"));
        dto.setTakeoutUnitPrice(new BigDecimal("0.00"));
        final List<ItemRespDTO> list = Arrays.asList(dto);
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.goods(request1)).thenReturn(list);

        // Configure TakeoutDailyClientService.goods(...).
        final DailyReqDTO takeoutStatsQueryDTO = new DailyReqDTO();
        takeoutStatsQueryDTO.setDeviceType(0);
        takeoutStatsQueryDTO.setDeviceId("deviceId");
        takeoutStatsQueryDTO.setStoreGuid("storeGuid");
        takeoutStatsQueryDTO.setBeginTime("beginTime");
        takeoutStatsQueryDTO.setEndTime("endTime");
        takeoutStatsQueryDTO.setQueryType(0);
        takeoutStatsQueryDTO.setOrderItem(0);
        takeoutStatsQueryDTO.setOrderType(0);
        takeoutStatsQueryDTO.setIsMember(0);
        takeoutStatsQueryDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setStoreGuids(Arrays.asList("value"));
        takeoutStatsQueryDTO.setIsPrint(0);
        when(mockTakeoutDailyClientService.goods(takeoutStatsQueryDTO)).thenReturn(Collections.emptyList());

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result result = mockBusinessDailyPrintServiceImpl.print(request);

        // Verify the results
    }

    @Test
    public void testPrint_DinnerDailyClientServiceAttrReturnsNull() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);
        request.setType(0);

        // Configure DinnerDailyClientService.attr(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.attr(request1)).thenReturn(null);

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result result = mockBusinessDailyPrintServiceImpl.print(request);

        // Verify the results
    }

    @Test
    public void testPrint_DinnerDailyClientServiceAttrReturnsNoItems() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);
        request.setType(0);

        // Configure DinnerDailyClientService.attr(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.attr(request1)).thenReturn(Collections.emptyList());

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result result = mockBusinessDailyPrintServiceImpl.print(request);

        // Verify the results
    }

    @Test
    public void testPrint_DinnerDailyClientServiceReturnVegetablesReturnsNoItems() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);
        request.setType(0);

        // Configure DinnerDailyClientService.returnVegetables(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.returnVegetables(request1)).thenReturn(Collections.emptyList());

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result result = mockBusinessDailyPrintServiceImpl.print(request);

        // Verify the results
    }

    @Test
    public void testPrint_DinnerDailyClientServiceDishGivingReturnsNoItems() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);
        request.setType(0);

        // Configure DinnerDailyClientService.dishGiving(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.dishGiving(request1)).thenReturn(Collections.emptyList());

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result result = mockBusinessDailyPrintServiceImpl.print(request);

        // Verify the results
    }

    @Test
    public void testPrint_DinnerDailyClientServiceListByRequestReturnsNoItems() {
        // Setup
        final DailyPrintReqDTO request = new DailyPrintReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);
        request.setType(0);

        // Configure MemberDataClientService.queryOnlyConsumption(...).
        final ResponseConsumpStatis responseConsumpStatis = new ResponseConsumpStatis();
        responseConsumpStatis.setConsumptionNum(0L);
        responseConsumpStatis.setConsumptionAmount(new BigDecimal("0.00"));
        responseConsumpStatis.setConsumptionMemberNum(0);
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        responsePayWayDetail.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail.setPayName("payName");
        responsePayWayDetail.setExcessAmount(new BigDecimal("0.00"));
        responseConsumpStatis.setPayWayDetailList(Arrays.asList(responsePayWayDetail));
        final RequestMemberDaily reqDTO = new RequestMemberDaily();
        reqDTO.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO.setStoreGuid("storeGuid");
        reqDTO.setConsumpType(0);
        when(mockDataClientService.queryOnlyConsumption(reqDTO)).thenReturn(responseConsumpStatis);

        // Configure DinnerDailyClientService.listByRequest(...).
        final DailyReqDTO request1 = new DailyReqDTO();
        request1.setDeviceType(0);
        request1.setDeviceId("deviceId");
        request1.setStoreGuid("storeGuid");
        request1.setBeginTime("beginTime");
        request1.setEndTime("endTime");
        request1.setQueryType(0);
        request1.setOrderItem(0);
        request1.setOrderType(0);
        request1.setIsMember(0);
        request1.setCheckoutStaffGuids(Arrays.asList("value"));
        request1.setStoreGuids(Arrays.asList("value"));
        request1.setIsPrint(0);
        when(mockDinnerDailyClientService.listByRequest(request1)).thenReturn(Collections.emptyList());

        // Configure PrintClientService.printTask(...).
        final PrintDTO printDto = new PrintDTO();
        printDto.setInvoiceType(0);
        printDto.setEnterpriseGuid("enterpriseGuid");
        printDto.setStoreGuid("storeGuid");
        printDto.setPrintUid("printUid");
        printDto.setAreaGuid("areaGuid");
        printDto.setOperatorStaffGuid("operatorStaffGuid");
        printDto.setOperatorStaffName("operatorStaffName");
        printDto.setCreateTime(0L);
        printDto.setDeviceId("deviceId");
        printDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintClientService.printTask(printDto)).thenReturn("result");

        // Run the test
        final Result result = mockBusinessDailyPrintServiceImpl.print(request);

        // Verify the results
    }

    @Test
    public void testMemberConsumeDaily() {
        // Setup
        final DailyReqDTO reqDTO = new DailyReqDTO();
        reqDTO.setDeviceType(0);
        reqDTO.setDeviceId("deviceId");
        reqDTO.setStoreGuid("storeGuid");
        reqDTO.setBeginTime("beginTime");
        reqDTO.setEndTime("endTime");
        reqDTO.setQueryType(0);
        reqDTO.setOrderItem(0);
        reqDTO.setOrderType(0);
        reqDTO.setIsMember(0);
        reqDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        reqDTO.setStoreGuids(Arrays.asList("value"));
        reqDTO.setIsPrint(0);

        final ResponseConsumpStatis expectedResult = new ResponseConsumpStatis();
        expectedResult.setConsumptionNum(0L);
        expectedResult.setConsumptionAmount(new BigDecimal("0.00"));
        expectedResult.setConsumptionMemberNum(0);
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        responsePayWayDetail.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail.setPayName("payName");
        responsePayWayDetail.setExcessAmount(new BigDecimal("0.00"));
        expectedResult.setPayWayDetailList(Arrays.asList(responsePayWayDetail));

        // Configure MemberDataClientService.queryOnlyConsumption(...).
        final ResponseConsumpStatis responseConsumpStatis = new ResponseConsumpStatis();
        responseConsumpStatis.setConsumptionNum(0L);
        responseConsumpStatis.setConsumptionAmount(new BigDecimal("0.00"));
        responseConsumpStatis.setConsumptionMemberNum(0);
        final ResponsePayWayDetail responsePayWayDetail1 = new ResponsePayWayDetail();
        responsePayWayDetail1.setPayWay(0);
        responsePayWayDetail1.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail1.setPayName("payName");
        responsePayWayDetail1.setExcessAmount(new BigDecimal("0.00"));
        responseConsumpStatis.setPayWayDetailList(Arrays.asList(responsePayWayDetail1));
        final RequestMemberDaily reqDTO1 = new RequestMemberDaily();
        reqDTO1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setStoreGuid("storeGuid");
        reqDTO1.setConsumpType(0);
        when(mockDataClientService.queryOnlyConsumption(reqDTO1)).thenReturn(responseConsumpStatis);

        // Configure DinnerDailyClientService.listByRequest(...).
        final List<AmountItemDTO> amountItemDTOS = Arrays.asList(
                new AmountItemDTO(0, "name", new BigDecimal("0.00"), new BigDecimal("0.00")));
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);
        when(mockDinnerDailyClientService.listByRequest(request)).thenReturn(amountItemDTOS);

        // Run the test
        final ResponseConsumpStatis result = businessDailyServiceImplUnderTest.memberConsumeDaily(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testMemberConsumeDaily_DinnerDailyClientServiceReturnsNoItems() {
        // Setup
        final DailyReqDTO reqDTO = new DailyReqDTO();
        reqDTO.setDeviceType(0);
        reqDTO.setDeviceId("deviceId");
        reqDTO.setStoreGuid("storeGuid");
        reqDTO.setBeginTime("beginTime");
        reqDTO.setEndTime("endTime");
        reqDTO.setQueryType(0);
        reqDTO.setOrderItem(0);
        reqDTO.setOrderType(0);
        reqDTO.setIsMember(0);
        reqDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        reqDTO.setStoreGuids(Arrays.asList("value"));
        reqDTO.setIsPrint(0);

        final ResponseConsumpStatis expectedResult = new ResponseConsumpStatis();
        expectedResult.setConsumptionNum(0L);
        expectedResult.setConsumptionAmount(new BigDecimal("0.00"));
        expectedResult.setConsumptionMemberNum(0);
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        responsePayWayDetail.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail.setPayName("payName");
        responsePayWayDetail.setExcessAmount(new BigDecimal("0.00"));
        expectedResult.setPayWayDetailList(Arrays.asList(responsePayWayDetail));

        // Configure MemberDataClientService.queryOnlyConsumption(...).
        final ResponseConsumpStatis responseConsumpStatis = new ResponseConsumpStatis();
        responseConsumpStatis.setConsumptionNum(0L);
        responseConsumpStatis.setConsumptionAmount(new BigDecimal("0.00"));
        responseConsumpStatis.setConsumptionMemberNum(0);
        final ResponsePayWayDetail responsePayWayDetail1 = new ResponsePayWayDetail();
        responsePayWayDetail1.setPayWay(0);
        responsePayWayDetail1.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail1.setPayName("payName");
        responsePayWayDetail1.setExcessAmount(new BigDecimal("0.00"));
        responseConsumpStatis.setPayWayDetailList(Arrays.asList(responsePayWayDetail1));
        final RequestMemberDaily reqDTO1 = new RequestMemberDaily();
        reqDTO1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setStoreGuid("storeGuid");
        reqDTO1.setConsumpType(0);
        when(mockDataClientService.queryOnlyConsumption(reqDTO1)).thenReturn(responseConsumpStatis);

        // Configure DinnerDailyClientService.listByRequest(...).
        final DailyReqDTO request = new DailyReqDTO();
        request.setDeviceType(0);
        request.setDeviceId("deviceId");
        request.setStoreGuid("storeGuid");
        request.setBeginTime("beginTime");
        request.setEndTime("endTime");
        request.setQueryType(0);
        request.setOrderItem(0);
        request.setOrderType(0);
        request.setIsMember(0);
        request.setCheckoutStaffGuids(Arrays.asList("value"));
        request.setStoreGuids(Arrays.asList("value"));
        request.setIsPrint(0);
        when(mockDinnerDailyClientService.listByRequest(request)).thenReturn(Collections.emptyList());

        // Run the test
        final ResponseConsumpStatis result = businessDailyServiceImplUnderTest.memberConsumeDaily(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testMemberRechargeDaily() {
        // Setup
        final DailyReqDTO reqDTO = new DailyReqDTO();
        reqDTO.setDeviceType(0);
        reqDTO.setDeviceId("deviceId");
        reqDTO.setStoreGuid("storeGuid");
        reqDTO.setBeginTime("beginTime");
        reqDTO.setEndTime("endTime");
        reqDTO.setQueryType(0);
        reqDTO.setOrderItem(0);
        reqDTO.setOrderType(0);
        reqDTO.setIsMember(0);
        reqDTO.setCheckoutStaffGuids(Arrays.asList("value"));
        reqDTO.setStoreGuids(Arrays.asList("value"));
        reqDTO.setIsPrint(0);

        final ResponseRechargeStatis expectedResult = new ResponseRechargeStatis();
        expectedResult.setRechargeNum(0L);
        expectedResult.setRechargeAmount(new BigDecimal("0.00"));
        expectedResult.setPresentAmount(new BigDecimal("0.00"));
        expectedResult.setIncomeAmount(new BigDecimal("0.00"));
        expectedResult.setRechargeMemberNum(0);
        final ResponsePayWayDetail responsePayWayDetail = new ResponsePayWayDetail();
        responsePayWayDetail.setPayWay(0);
        responsePayWayDetail.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail.setPayName("payName");
        responsePayWayDetail.setExcessAmount(new BigDecimal("0.00"));
        expectedResult.setPayWayDetailList(Arrays.asList(responsePayWayDetail));

        // Configure MemberDataClientService.queryOnlyRecharge(...).
        final ResponseRechargeStatis responseRechargeStatis = new ResponseRechargeStatis();
        responseRechargeStatis.setRechargeNum(0L);
        responseRechargeStatis.setRechargeAmount(new BigDecimal("0.00"));
        responseRechargeStatis.setPresentAmount(new BigDecimal("0.00"));
        responseRechargeStatis.setIncomeAmount(new BigDecimal("0.00"));
        responseRechargeStatis.setRechargeMemberNum(0);
        final ResponsePayWayDetail responsePayWayDetail1 = new ResponsePayWayDetail();
        responsePayWayDetail1.setPayWay(0);
        responsePayWayDetail1.setPayAmount(new BigDecimal("0.00"));
        responsePayWayDetail1.setPayName("payName");
        responsePayWayDetail1.setExcessAmount(new BigDecimal("0.00"));
        responseRechargeStatis.setPayWayDetailList(Arrays.asList(responsePayWayDetail1));
        final RequestMemberDaily reqDTO1 = new RequestMemberDaily();
        reqDTO1.setStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reqDTO1.setStoreGuid("storeGuid");
        reqDTO1.setConsumpType(0);
        when(mockDataClientService.queryOnlyRecharge(reqDTO1)).thenReturn(responseRechargeStatis);

        // Run the test
        final ResponseRechargeStatis result = businessDailyServiceImplUnderTest.memberRechargeDaily(reqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUsers() {
        // Setup
        final List<UserBriefDTO> expectedResult = Arrays.asList(new UserBriefDTO("userGuid", "userName"));

        // Configure UserClientService.storeUsers(...).
        final List<UserBriefDTO> userBriefDTOS = Arrays.asList(new UserBriefDTO("userGuid", "userName"));
        when(mockUserClientService.storeUsers("storeGuid")).thenReturn(userBriefDTOS);

        // Run the test
        final List<UserBriefDTO> result = businessDailyServiceImplUnderTest.users();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUsers_UserClientServiceReturnsNoItems() {
        // Setup
        when(mockUserClientService.storeUsers("storeGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final List<UserBriefDTO> result = businessDailyServiceImplUnderTest.users();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
