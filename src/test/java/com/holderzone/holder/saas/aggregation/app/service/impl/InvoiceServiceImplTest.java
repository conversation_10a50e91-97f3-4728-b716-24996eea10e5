package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.holder.saas.aggregation.app.config.IPassInvoiceConfig;
import com.holderzone.holder.saas.aggregation.app.helper.RedisHelper;
import com.holderzone.holder.saas.aggregation.app.service.feign.staff.ValidateClientService;
import com.holderzone.saas.store.dto.invoice.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class InvoiceServiceImplTest {

    @Mock
    private IPassInvoiceConfig mockIPassInvoiceConfig;
    @Mock
    private RedisHelper mockRedisHelper;
    @Mock
    private ValidateClientService mockValidateClientService;

    private InvoiceServiceImpl invoiceServiceImplUnderTest;

    @Before
    public void setUp() {
        invoiceServiceImplUnderTest = new InvoiceServiceImpl(mockIPassInvoiceConfig, mockRedisHelper,
                mockValidateClientService);
    }

    @Test
    public void testReceiptValidate() {
        // Setup
        final RequestValidateDTO requestValidateDTO = new RequestValidateDTO();
        requestValidateDTO.setUserGuid("userGuid");
        requestValidateDTO.setAccount("account");
        requestValidateDTO.setAccountName("accountName");
        requestValidateDTO.setOrderNo("orderNo");
        requestValidateDTO.setStoreGuid("storeGuid");
        requestValidateDTO.setOrderAmount(new BigDecimal("0.00"));
        requestValidateDTO.setSms("sms");

        final ResponseVerifyInvoiceDTO expectedResult = new ResponseVerifyInvoiceDTO();
        expectedResult.setMessageCode(0);
        expectedResult.setVerifyCode(0);
        expectedResult.setElectronicTaxpayerName("electronicTaxpayerName");
        expectedResult.setElectronicTaxpayerPhone("account");

        when(mockRedisHelper.get("key")).thenReturn("result");

        // Configure ValidateClientService.queryUserInvoice(...).
        final UserAuthorityInvoiceDTO userAuthorityInvoiceDTO = new UserAuthorityInvoiceDTO();
        userAuthorityInvoiceDTO.setGuid("5ce9849d-6d61-4844-9875-94709509fb73");
        userAuthorityInvoiceDTO.setUserGuid("userGuid");
        userAuthorityInvoiceDTO.setSourceCode("sourceCode");
        userAuthorityInvoiceDTO.setElectronicTaxpayerName("electronicTaxpayerName");
        userAuthorityInvoiceDTO.setElectronicTaxpayerPhone("account");
        when(mockValidateClientService.queryUserInvoice("userGuid")).thenReturn(userAuthorityInvoiceDTO);

        when(mockIPassInvoiceConfig.getQueryResidueLimit()).thenReturn("result");
        when(mockIPassInvoiceConfig.getReceiptValidate()).thenReturn("result");

        // Run the test
        final ResponseVerifyInvoiceDTO result = invoiceServiceImplUnderTest.receiptValidate(requestValidateDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testNoteLogin() {
        // Setup
        final RequestValidateDTO requestValidateDTO = new RequestValidateDTO();
        requestValidateDTO.setUserGuid("userGuid");
        requestValidateDTO.setAccount("account");
        requestValidateDTO.setAccountName("accountName");
        requestValidateDTO.setOrderNo("orderNo");
        requestValidateDTO.setStoreGuid("storeGuid");
        requestValidateDTO.setOrderAmount(new BigDecimal("0.00"));
        requestValidateDTO.setSms("sms");

        when(mockIPassInvoiceConfig.getNoteLogin()).thenReturn("result");

        // Run the test
        final Boolean result = invoiceServiceImplUnderTest.noteLogin(requestValidateDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testAuthenticationUrl() {
        // Setup
        final RequestAuthenticationDTO requestAuthenticationDTO = new RequestAuthenticationDTO();
        requestAuthenticationDTO.setStoreGuid("storeGuid");
        requestAuthenticationDTO.setOrderNo("orderNo");
        requestAuthenticationDTO.setTaxType(0);
        requestAuthenticationDTO.setAccount("account");

        final ResponseAuthenticationUrlDTO expectedResult = new ResponseAuthenticationUrlDTO();
        expectedResult.setStoreId("storeId");
        expectedResult.setOrderNo("orderNo");
        expectedResult.setCode("code");
        expectedResult.setStatus("-1");
        expectedResult.setErrorMessage("第三方接口调用超时");

        when(mockIPassInvoiceConfig.getAuthenticationUrl()).thenReturn("result");

        // Run the test
        final ResponseAuthenticationUrlDTO result = invoiceServiceImplUnderTest.authenticationUrl(
                requestAuthenticationDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryAuthResult() {
        // Setup
        final RequestAuthenticationDTO requestAuthenticationDTO = new RequestAuthenticationDTO();
        requestAuthenticationDTO.setStoreGuid("storeGuid");
        requestAuthenticationDTO.setOrderNo("orderNo");
        requestAuthenticationDTO.setTaxType(0);
        requestAuthenticationDTO.setAccount("account");

        final ResponseQueryAuthDTO expectedResult = new ResponseQueryAuthDTO();
        expectedResult.setStoreId("storeId");
        expectedResult.setOrderNo("orderNo");
        expectedResult.setAccount("account");
        expectedResult.setStatus("-1");
        expectedResult.setErrorMessage("errorMessage");

        when(mockIPassInvoiceConfig.getAuthResult()).thenReturn("result");
        when(mockIPassInvoiceConfig.getReceiptValidate()).thenReturn("result");

        // Run the test
        final ResponseQueryAuthDTO result = invoiceServiceImplUnderTest.queryAuthResult(requestAuthenticationDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGenerateOrderInvoice() {
        // Setup
        final RequestValidateDTO requestAuthenticationDTO = new RequestValidateDTO();
        requestAuthenticationDTO.setUserGuid("userGuid");
        requestAuthenticationDTO.setAccount("account");
        requestAuthenticationDTO.setAccountName("accountName");
        requestAuthenticationDTO.setOrderNo("orderNo");
        requestAuthenticationDTO.setStoreGuid("storeGuid");
        requestAuthenticationDTO.setOrderAmount(new BigDecimal("0.00"));
        requestAuthenticationDTO.setSms("sms");

        when(mockIPassInvoiceConfig.getOrderInvoiceUrl()).thenReturn("result");

        // Run the test
        final String result = invoiceServiceImplUnderTest.generateOrderInvoice(requestAuthenticationDTO);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }
}
