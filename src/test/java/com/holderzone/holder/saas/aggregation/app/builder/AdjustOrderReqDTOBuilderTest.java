package com.holderzone.holder.saas.aggregation.app.builder;

import com.holderzone.saas.store.dto.item.resp.SkuTakeawayInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SubItemSkuWebRespDTO;
import com.holderzone.saas.store.dto.item.resp.SubgroupWebRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustOrderReqDTO;
import org.junit.Test;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;

public class AdjustOrderReqDTOBuilderTest {

    @Test
    public void testPreHandleReqDTO1() {
        // Setup
        final AdjustOrderReqDTO reqDTO = new AdjustOrderReqDTO();
        final AdjustOrderReqDTO.AdjustOrderItem adjustOrderItem = new AdjustOrderReqDTO.AdjustOrderItem();
        adjustOrderItem.setOrderItemGuid("orderItemGuid");
        adjustOrderItem.setAdjustType(0);
        adjustOrderItem.setAdjustCount(new BigDecimal("0.00"));
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("bfa6f415-1ef0-402e-adf6-5494250d17e9");
        dineInItemDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setSkuName("");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setUnit("");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setPackageDefaultCount(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        dineInItemDTO.setMappingCount(new BigDecimal("0.00"));
        adjustOrderItem.setItemList(Arrays.asList(dineInItemDTO));
        reqDTO.setOrderItemList(Arrays.asList(adjustOrderItem));
        reqDTO.setOrderNo("orderNo");
        reqDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        reqDTO.setState(0);
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("bfa6f415-1ef0-402e-adf6-5494250d17e9");
        dineInItemDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineInItemDTO1.setItemName("itemName");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setSkuName("");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setUnit("");
        final PackageSubgroupDTO packageSubgroupDTO1 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO1 = new SubDineInItemDTO();
        subDineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO1.setPackageDefaultCount(new BigDecimal("0.00"));
        packageSubgroupDTO1.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO1));
        dineInItemDTO1.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO1));
        dineInItemDTO1.setMappingCount(new BigDecimal("0.00"));
        reqDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));

        final DineinOrderDetailRespDTO orderRespDTO = new DineinOrderDetailRespDTO();
        orderRespDTO.setOrderNo("orderNo");
        orderRespDTO.setState(0);
        orderRespDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setGuid("bfa6f415-1ef0-402e-adf6-5494250d17e9");
        dineInItemDTO2.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineInItemDTO2.setItemName("itemName");
        dineInItemDTO2.setItemType(0);
        dineInItemDTO2.setSkuName("");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setUnit("");
        final PackageSubgroupDTO packageSubgroupDTO2 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO2 = new SubDineInItemDTO();
        subDineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO2.setPackageDefaultCount(new BigDecimal("0.00"));
        packageSubgroupDTO2.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO2));
        dineInItemDTO2.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO2));
        dineInItemDTO2.setMappingCount(new BigDecimal("0.00"));
        orderRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        orderRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));

        // Run the test
        AdjustOrderReqDTOBuilder.preHandleReqDTO(reqDTO, orderRespDTO);

        // Verify the results
    }

    @Test
    public void testPreHandleReqDTO2() {
        // Setup
        final AdjustOrderReqDTO reqDTO = new AdjustOrderReqDTO();
        final AdjustOrderReqDTO.AdjustOrderItem adjustOrderItem = new AdjustOrderReqDTO.AdjustOrderItem();
        adjustOrderItem.setOrderItemGuid("orderItemGuid");
        adjustOrderItem.setAdjustType(0);
        adjustOrderItem.setAdjustCount(new BigDecimal("0.00"));
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("bfa6f415-1ef0-402e-adf6-5494250d17e9");
        dineInItemDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setSkuName("");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setUnit("");
        final PackageSubgroupDTO packageSubgroupDTO = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
        subDineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO.setPackageDefaultCount(new BigDecimal("0.00"));
        packageSubgroupDTO.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO));
        dineInItemDTO.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO));
        dineInItemDTO.setMappingCount(new BigDecimal("0.00"));
        adjustOrderItem.setItemList(Arrays.asList(dineInItemDTO));
        reqDTO.setOrderItemList(Arrays.asList(adjustOrderItem));
        reqDTO.setOrderNo("orderNo");
        reqDTO.setBusinessDay(LocalDate.of(2020, 1, 1));
        reqDTO.setState(0);
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("bfa6f415-1ef0-402e-adf6-5494250d17e9");
        dineInItemDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineInItemDTO1.setItemName("itemName");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setSkuName("");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setUnit("");
        final PackageSubgroupDTO packageSubgroupDTO1 = new PackageSubgroupDTO();
        final SubDineInItemDTO subDineInItemDTO1 = new SubDineInItemDTO();
        subDineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        subDineInItemDTO1.setPackageDefaultCount(new BigDecimal("0.00"));
        packageSubgroupDTO1.setSubDineInItemDTOS(Arrays.asList(subDineInItemDTO1));
        dineInItemDTO1.setPackageSubgroupDTOS(Arrays.asList(packageSubgroupDTO1));
        dineInItemDTO1.setMappingCount(new BigDecimal("0.00"));
        reqDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));

        final TakeoutOrderDTO takeoutOrderDetail = new TakeoutOrderDTO();
        takeoutOrderDetail.setOrderViewId("orderNo");
        takeoutOrderDetail.setBusinessDay(LocalDate.of(2020, 1, 1));
        final TakeoutOrderDTO.OrderItemDTO orderItemDTO = new TakeoutOrderDTO.OrderItemDTO();
        orderItemDTO.setItemGuid("bfa6f415-1ef0-402e-adf6-5494250d17e9");
        orderItemDTO.setItemName("itemName");
        orderItemDTO.setItemPrice(new BigDecimal("0.00"));
        orderItemDTO.setItemCount(new BigDecimal("0.00"));
        orderItemDTO.setItemSpec("itemSpec");
        orderItemDTO.setActualItemCount(new BigDecimal("0.00"));
        final SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO = new SkuTakeawayInfoRespDTO();
        final SubgroupWebRespDTO subgroupWebRespDTO = new SubgroupWebRespDTO();
        final SubItemSkuWebRespDTO subItemSkuWebRespDTO = new SubItemSkuWebRespDTO();
        subItemSkuWebRespDTO.setItemNum(new BigDecimal("0.00"));
        subItemSkuWebRespDTO.setDefaultNum(0);
        subgroupWebRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuWebRespDTO));
        skuTakeawayInfoRespDTO.setSubgroupList(Arrays.asList(subgroupWebRespDTO));
        orderItemDTO.setSkuTakeawayInfoRespDTO(skuTakeawayInfoRespDTO);
        takeoutOrderDetail.setArrayOfItem(Arrays.asList(orderItemDTO));

        // Run the test
        AdjustOrderReqDTOBuilder.preHandleReqDTO(reqDTO, takeoutOrderDetail);

        // Verify the results
    }
}
