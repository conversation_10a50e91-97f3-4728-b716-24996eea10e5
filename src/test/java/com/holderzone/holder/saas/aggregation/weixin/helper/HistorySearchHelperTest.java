package com.holderzone.holder.saas.aggregation.weixin.helper;

import com.holderzone.holder.saas.aggregation.weixin.entity.cmember.HistorySearchDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.core.ZSetOperations;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HistorySearchHelperTest {

    @Mock
    private StringRedisTemplate mockRedisTemplate;

    private HistorySearchHelper historySearchHelperUnderTest;

    @Before
    public void setUp() throws Exception {
        historySearchHelperUnderTest = new HistorySearchHelper(mockRedisTemplate);
    }

    @Test
    public void testPutHistorySearchRecord() {
        // Setup
        final HistorySearchDTO historySearch = new HistorySearchDTO();
        historySearch.setKey("key");
        historySearch.setContent("content");
        historySearch.setType(0);

        ZSetOperations<String, String> valueOperationsMock = Mockito.mock(ZSetOperations.class);
        Mockito.when(mockRedisTemplate.opsForZSet()).thenReturn(valueOperationsMock);

        // Run the test
        historySearchHelperUnderTest.putHistorySearchRecord(historySearch);

        // Verify the results
    }

    @Test
    public void testGetHistorySearchRecord() {
        // Setup
        final HistorySearchDTO historySearch = new HistorySearchDTO();
        historySearch.setKey("key");
        historySearch.setContent("content");
        historySearch.setType(0);

        ZSetOperations<String, String> valueOperationsMock = Mockito.mock(ZSetOperations.class);
        Mockito.when(mockRedisTemplate.opsForZSet()).thenReturn(valueOperationsMock);

        // Run the test
        final List<String> result = historySearchHelperUnderTest.getHistorySearchRecord(historySearch);

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList());
    }

    @Test
    public void testRemoveHistorySearchRecord() {
        // Setup
        final HistorySearchDTO historySearch = new HistorySearchDTO();
        historySearch.setKey("key");
        historySearch.setContent("content");
        historySearch.setType(0);

        // Run the test
        historySearchHelperUnderTest.removeHistorySearchRecord(historySearch);

    }
}
