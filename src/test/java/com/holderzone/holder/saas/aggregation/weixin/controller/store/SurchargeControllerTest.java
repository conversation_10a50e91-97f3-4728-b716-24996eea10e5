package com.holderzone.holder.saas.aggregation.weixin.controller.store;

import com.holderzone.holder.saas.aggregation.weixin.service.WxStoreTradeOrderService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.BusinessClientService;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeCalculateDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(SurchargeController.class)
public class SurchargeControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private BusinessClientService mockBusinessClientService;
    @MockBean
    private WxStoreTradeOrderService mockWxStoreTradeOrderService;

    @Test
    public void testCalculateSurcharge() throws Exception {
        // Setup
        // Configure BusinessClientService.calculateSurcharge(...).
        final List<SurchargeLinkDTO> surchargeLinkDTOS = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        final SurchargeCalculateDTO surchargeDTO = new SurchargeCalculateDTO();
        surchargeDTO.setGuestCount(0);
        surchargeDTO.setTableGuid("tableGuid");
        surchargeDTO.setAreaGuid("areaGuid");
        surchargeDTO.setEnterpriseGuid("enterpriseGuid");
        surchargeDTO.setTradeMode(0);
        when(mockBusinessClientService.calculateSurcharge(surchargeDTO)).thenReturn(surchargeLinkDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/surcharge/calculate_surcharge")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testCalculateSurcharge_BusinessClientServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure BusinessClientService.calculateSurcharge(...).
        final SurchargeCalculateDTO surchargeDTO = new SurchargeCalculateDTO();
        surchargeDTO.setGuestCount(0);
        surchargeDTO.setTableGuid("tableGuid");
        surchargeDTO.setAreaGuid("areaGuid");
        surchargeDTO.setEnterpriseGuid("enterpriseGuid");
        surchargeDTO.setTradeMode(0);
        when(mockBusinessClientService.calculateSurcharge(surchargeDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/surcharge/calculate_surcharge")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testCalculateSurchargeNew() throws Exception {
        // Setup
        // Configure WxStoreTradeOrderService.querySurchargeListByRedis(...).
        final List<SurchargeLinkDTO> surchargeLinkDTOS = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        when(mockWxStoreTradeOrderService.querySurchargeListByRedis("tableGuid")).thenReturn(surchargeLinkDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/surcharge/calculate_surcharge_new")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testCalculateSurchargeNew_WxStoreTradeOrderServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockWxStoreTradeOrderService.querySurchargeListByRedis("tableGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/surcharge/calculate_surcharge_new")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }
}
