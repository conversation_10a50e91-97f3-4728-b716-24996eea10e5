package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class WxMemberSessionPlusDTOTest {

    @Mock
    private WxMemberSessionDTO mockWxMemberSessionDTO;

    private WxMemberSessionPlusDTO wxMemberSessionPlusDTOUnderTest;

    @Before
    public void setUp() throws Exception {
        wxMemberSessionPlusDTOUnderTest = new WxMemberSessionPlusDTO();
        wxMemberSessionPlusDTOUnderTest.setWxMemberSessionDTO(mockWxMemberSessionDTO);
    }

    @Test
    public void testGetWxMemberSessionDTO() {
        assertThat(wxMemberSessionPlusDTOUnderTest.getWxMemberSessionDTO()).isEqualTo(mockWxMemberSessionDTO);
    }

    @Test
    public void testOrderNumberGetterAndSetter() {
        final Integer orderNumber = 0;
        wxMemberSessionPlusDTOUnderTest.setOrderNumber(orderNumber);
        assertThat(wxMemberSessionPlusDTOUnderTest.getOrderNumber()).isEqualTo(orderNumber);
    }

    @Test
    public void testEquals() throws Exception {
        assertThat(wxMemberSessionPlusDTOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() throws Exception {
        assertThat(wxMemberSessionPlusDTOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() throws Exception {
        assertThat(wxMemberSessionPlusDTOUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() throws Exception {
        assertThat(wxMemberSessionPlusDTOUnderTest.toString()).isEqualTo("result");
    }
}
