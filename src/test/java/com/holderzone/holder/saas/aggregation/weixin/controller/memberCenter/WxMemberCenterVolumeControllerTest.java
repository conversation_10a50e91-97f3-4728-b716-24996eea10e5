package com.holderzone.holder.saas.aggregation.weixin.controller.memberCenter;

import com.holderzone.holder.saas.aggregation.weixin.service.rpc.memberCenter.WxMemberCenterVolumeService;
import com.holderzone.holder.saas.member.wechat.dto.coupon.ResponseHsmProductDetail;
import com.holderzone.holder.saas.member.wechat.dto.member.MemberInfoVolume;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolume;
import com.holderzone.saas.store.dto.weixin.member.WxApplicableProductStores;
import com.holderzone.saas.store.dto.weixin.member.WxMemberInfoVolumeDetailsRespDTO;
import com.holderzone.saas.store.dto.weixin.member.WxMemberVolumeInfoListReqDTO;
import com.holderzone.saas.store.dto.weixin.member.WxVolumeDetailReqDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(WxMemberCenterVolumeController.class)
public class WxMemberCenterVolumeControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxMemberCenterVolumeService mockWxMemberCenterVolumeService;

    @Test
    public void testVolumeInfoList() throws Exception {
        // Setup
        // Configure WxMemberCenterVolumeService.volumeInfoList(...).
        final ResponseMemberInfoVolume responseMemberInfoVolume = new ResponseMemberInfoVolume();
        responseMemberInfoVolume.setMayUseVolumeNum(0);
        responseMemberInfoVolume.setNotUseVolumeNum(0);
        final MemberInfoVolume memberInfoVolume = new MemberInfoVolume();
        memberInfoVolume.setMemberVolumeGuid("memberVolumeGuid");
        memberInfoVolume.setVolumeInfoGuid("volumeInfoGuid");
        responseMemberInfoVolume.setMemberVolumeList(Arrays.asList(memberInfoVolume));
        when(mockWxMemberCenterVolumeService.volumeInfoList(
                new WxMemberVolumeInfoListReqDTO("memberInfoGuid", 0, "enterpriseGuid", "brandGuid", 0,
                        "storeGuid"))).thenReturn(responseMemberInfoVolume);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_member_center_volume/volume_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testVolumeCodeDetails() throws Exception {
        // Setup
        // Configure WxMemberCenterVolumeService.volumeCodeDetails(...).
        final ResponseHsmProductDetail responseHsmProductDetail = new ResponseHsmProductDetail();
        responseHsmProductDetail.setGuid("5f2e09ad-57f0-4647-9666-3841b6ee18c1");
        responseHsmProductDetail.setProductKey("productKey");
        responseHsmProductDetail.setEnterpriseGuid("enterpriseGuid");
        responseHsmProductDetail.setBrandGuid("brandGuid");
        responseHsmProductDetail.setStoreGuid("storeGuid");
        final WxMemberInfoVolumeDetailsRespDTO wxMemberInfoVolumeDetailsRespDTO = new WxMemberInfoVolumeDetailsRespDTO(
                "volumeInfoName", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                "volumeCode", "volumeQrCode", 0, new BigDecimal("0.00"), 0, 0, new BigDecimal("0.00"), "mayUseNum",
                Arrays.asList("value"), "description", 0,
                Arrays.asList(new WxApplicableProductStores("storeName", 0, Arrays.asList("value"))), "storeName", 0,
                Arrays.asList(responseHsmProductDetail));
        when(mockWxMemberCenterVolumeService.volumeCodeDetails(
                new WxVolumeDetailReqDTO("enterpriseGuid", "memberVolumeGuid", "volumeInfoGuid")))
                .thenReturn(wxMemberInfoVolumeDetailsRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_member_center_volume/volume_details")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testVolumeCodeDetails_WxMemberCenterVolumeServiceReturnsNull() throws Exception {
        // Setup
        when(mockWxMemberCenterVolumeService.volumeCodeDetails(
                new WxVolumeDetailReqDTO("enterpriseGuid", "memberVolumeGuid", "volumeInfoGuid"))).thenReturn(null);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_member_center_volume/volume_details")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("");
    }
}
