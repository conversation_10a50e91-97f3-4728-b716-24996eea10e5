package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import org.junit.Test;

import java.time.LocalDate;

import static org.assertj.core.api.Assertions.assertThat;

public class ReserveTimeInfoRespDTOTest {

    @Test
    public void testLocalDateGetterAndSetter() {
        final ReserveTimeInfoRespDTO reserveTimeInfoRespDTOUnderTest = new ReserveTimeInfoRespDTO(
                LocalDate.of(2020, 1, 1), "msg");
        final LocalDate localDate = LocalDate.of(2020, 1, 1);
        reserveTimeInfoRespDTOUnderTest.setLocalDate(localDate);
        assertThat(reserveTimeInfoRespDTOUnderTest.getLocalDate()).isEqualTo(localDate);
    }

    @Test
    public void testMsgGetterAndSetter() {
        final ReserveTimeInfoRespDTO reserveTimeInfoRespDTOUnderTest = new ReserveTimeInfoRespDTO(
                LocalDate.of(2020, 1, 1), "msg");
        final String msg = "msg";
        reserveTimeInfoRespDTOUnderTest.setMsg(msg);
        assertThat(reserveTimeInfoRespDTOUnderTest.getMsg()).isEqualTo(msg);
    }

    @Test
    public void testEquals() throws Exception {
        final ReserveTimeInfoRespDTO reserveTimeInfoRespDTOUnderTest = new ReserveTimeInfoRespDTO(
                LocalDate.of(2020, 1, 1), "msg");
        assertThat(reserveTimeInfoRespDTOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() throws Exception {
        final ReserveTimeInfoRespDTO reserveTimeInfoRespDTOUnderTest = new ReserveTimeInfoRespDTO(
                LocalDate.of(2020, 1, 1), "msg");
        assertThat(reserveTimeInfoRespDTOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() throws Exception {
        final ReserveTimeInfoRespDTO reserveTimeInfoRespDTOUnderTest = new ReserveTimeInfoRespDTO(
                LocalDate.of(2020, 1, 1), "msg");
        assertThat(reserveTimeInfoRespDTOUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() throws Exception {
        final ReserveTimeInfoRespDTO reserveTimeInfoRespDTOUnderTest = new ReserveTimeInfoRespDTO(
                LocalDate.of(2020, 1, 1), "msg");
        assertThat(reserveTimeInfoRespDTOUnderTest.toString()).isEqualTo("result");
    }
}
