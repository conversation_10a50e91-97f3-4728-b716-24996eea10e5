package com.holderzone.holder.saas.aggregation.weixin.service.impl;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel;
import com.holderzone.holder.saas.aggregation.weixin.config.WeCatConfig;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.*;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestMemberCardRechargeForWechat;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseRecharge;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.saas.store.dto.member.hsm.HsmRechargeReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.dto.pay.SaasNotifyDTO;
import com.holderzone.saas.store.dto.trade.BaseInfo;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import com.netflix.hystrix.exception.HystrixRuntimeException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class HsmMemberServiceImplTest {

    @InjectMocks
    private HsmMemberServiceImpl hsmMemberService;

    @Mock
    private HsaBaseClientService hsaBaseClientService;
    @Mock
    private StorePayClientService storePayClientService;
    @Mock
    private StoreOrganizationClientService storeOrganizationClientService;
    @Mock
    private EntServiceClient entServiceClient;
    @Mock
    private WeCatConfig weChatPublic;
    @Mock
    private MemberTransactionPrintService memberTransactionPrintService;

    private UserContext userContext;

    @Before
    public void setUp() {
        userContext = new UserContext();
        userContext.setAllianceId("1fb529b8da78459ca64187f94dc3ae3e");
        UserContextUtils.put(userContext);

        when(weChatPublic.getAppId()).thenReturn("testAppId");
        when(weChatPublic.getMchntName()).thenReturn("testMchName");
        when(weChatPublic.getAppSecret()).thenReturn("testAppSecret");
    }

    @Test
    public void testCallback_Success() {
        // Arrange
        SaasNotifyDTO notifyDTO = new SaasNotifyDTO();
        AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setPaySt("2");
        pollingRespDTO.setPayGUID("testPayGuid");
        pollingRespDTO.setOrderGUID("testOrderGuid");
        pollingRespDTO.setPayPowerId("testPayPowerId");

        BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(8);

        notifyDTO.setAggPayPollingRespDTO(pollingRespDTO);
        notifyDTO.setBaseInfo(baseInfo);

        ResponseRecharge responseRecharge = new ResponseRecharge();
        ResponseModel<ResponseRecharge> resultDTO = new ResponseModel<>();
        resultDTO.setData(responseRecharge);

        when(hsaBaseClientService.memberCardRechargeForWechat(any())).thenReturn(resultDTO);

        // Act
        String result = hsmMemberService.callback(notifyDTO);

        // Assert
        assertEquals("SUCCESS", result);
        verify(hsaBaseClientService).memberCardRechargeForWechat(any());
        verify(memberTransactionPrintService).printMemberTransactionLog(any());
    }

    @Test
    public void testWechatRecharge_Success() {
        // Arrange
        HsmRechargeReqDTO reqDTO = new HsmRechargeReqDTO();
        reqDTO.setEnterpriseGuid("testEntGuid");
        reqDTO.setStoreGuid("testStoreGuid");
        reqDTO.setStoreName("testStore");
        reqDTO.setRechargeMoney(new BigDecimal("100"));
        reqDTO.setOutNotifyUrl("http://test.com");

        StoreDTO storeDTO = new StoreDTO();
        EnterpriseDTO enterpriseDTO = new EnterpriseDTO();
        enterpriseDTO.setName("testEnterprise");

        ResponseRecharge responseRecharge = new ResponseRecharge();
        responseRecharge.setPayGuid("testPayGuid");
        responseRecharge.setOrderGuid("testOrderGuid");

        ResponseModel<ResponseRecharge> resultDTO = new ResponseModel<>();
        resultDTO.setData(responseRecharge);

        when(storeOrganizationClientService.queryStoreByGuid(anyString())).thenReturn(storeDTO);
        when(entServiceClient.findEnterprise(any())).thenReturn(enterpriseDTO);
        when(hsaBaseClientService.memberCardRechargeForWechat(any())).thenReturn(resultDTO);
        when(storePayClientService.weChatPublic(any())).thenReturn("http://pay.url");

        // Act
        WxPayRespDTO result = hsmMemberService.wechatRecharge(reqDTO);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getCouldPay().intValue());
        assertEquals("http://pay.url", result.getPayUrl());
    }

    @Test
    public void testWechatRecharge_PaymentError() {
        // Arrange
        HsmRechargeReqDTO reqDTO = new HsmRechargeReqDTO();
        reqDTO.setEnterpriseGuid("testEntGuid");
        reqDTO.setStoreGuid("testStoreGuid");

        StoreDTO storeDTO = new StoreDTO();
        EnterpriseDTO enterpriseDTO = new EnterpriseDTO();
        ResponseRecharge responseRecharge = new ResponseRecharge();
        ResponseModel<ResponseRecharge> resultDTO = new ResponseModel<>();
        resultDTO.setData(responseRecharge);

        when(storeOrganizationClientService.queryStoreByGuid(anyString())).thenReturn(storeDTO);
        when(entServiceClient.findEnterprise(any())).thenReturn(enterpriseDTO);
        when(hsaBaseClientService.memberCardRechargeForWechat(any())).thenReturn(resultDTO);
        when(storePayClientService.weChatPublic(any())).thenThrow(new HystrixRuntimeException(null, null, null, null, null));

        // Act
        WxPayRespDTO result = hsmMemberService.wechatRecharge(reqDTO);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getCouldPay().intValue());
        assertNotNull(result.getErrorMsg());
    }

    @Test(expected = BusinessException.class)
    public void testWechatRecharge_StoreNotFound() {
        // Arrange
        HsmRechargeReqDTO reqDTO = new HsmRechargeReqDTO();
        reqDTO.setStoreGuid("nonExistentStore");

        when(storeOrganizationClientService.queryStoreByGuid(anyString())).thenReturn(null);

        // Act
        hsmMemberService.wechatRecharge(reqDTO);
    }

    @Test
    public void testCallback_PaymentFailed() {
        // Arrange
        SaasNotifyDTO notifyDTO = new SaasNotifyDTO();
        AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setPaySt("3"); // Failed payment status

        notifyDTO.setAggPayPollingRespDTO(pollingRespDTO);
        notifyDTO.setBaseInfo(new BaseInfo());

        // Act
        String result = hsmMemberService.callback(notifyDTO);

        // Assert
        assertEquals("SUCCESS", result);
        verify(hsaBaseClientService, never()).memberCardRechargeForWechat(any());
    }
}