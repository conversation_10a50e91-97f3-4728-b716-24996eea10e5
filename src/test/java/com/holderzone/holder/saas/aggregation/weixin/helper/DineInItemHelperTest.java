package com.holderzone.holder.saas.aggregation.weixin.helper;

import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class DineInItemHelperTest {

    @Test
    public void testDineInItem2DishList() {
        // Setup
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("guid");
        dineInItemDTO.setItemGuid("dishGuid");
        dineInItemDTO.setItemName("dishName");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setSkuGuid("dishSpecification");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setUnit("dishUnit");
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        final List<DineInItemDTO> dineInItemList = Arrays.asList(dineInItemDTO);
        final RequestDishInfo dishInfo = new RequestDishInfo();
        dishInfo.setOrderItemGuid("guid");
        dishInfo.setDishGuid("dishGuid");
        dishInfo.setDishName("dishName");
        dishInfo.setDishSpecification("dishSpecification");
        dishInfo.setDishUnit("dishUnit");
        dishInfo.setDishNum(new BigDecimal("0.00"));
        dishInfo.setGiftDishNum(new BigDecimal("0"));
        dishInfo.setDishSellUnitPrice(new BigDecimal("0.00"));
        dishInfo.setMainGoodGuid(null);
        dishInfo.setIsMainGood(1);
        dishInfo.setSurcharge(new BigDecimal("0"));
        dishInfo.setDishType(0);
        dishInfo.setSubtotal(new BigDecimal("0.00"));
        dishInfo.setPayPrice(new BigDecimal("0.00"));
        dishInfo.setDishOriginalUnitPrice(new BigDecimal("0.00"));
        dishInfo.setDishMemberPrice(new BigDecimal("0.00"));
        final List<RequestDishInfo> expectedResult = Arrays.asList(dishInfo);

        // Run the test
        final List<RequestDishInfo> result = DineInItemHelper.dineInItem2DishList(dineInItemList);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
