package com.holderzone.holder.saas.aggregation.weixin.controller.memberCenter;

import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxOrderRecordClientService;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.req.WxBrandUserOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxBrandUserOrderDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxBrandUserOrderItemDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxBrandUserOrderTitleGroup;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(WxMemberCenterDineController.class)
public class WxMemberCenterDineControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxOrderRecordClientService mockWxOrderRecordClientService;

    @Test
    public void testGetWxBrandUserOrder() throws Exception {
        // Setup
        // Configure WxOrderRecordClientService.getWxBrandUserOrder(...).
        final WxBrandUserOrderDTO wxBrandUserOrderDTO = new WxBrandUserOrderDTO(
                Arrays.asList(new WxBrandUserOrderTitleGroup(0, "orderName", 0)), Arrays.asList(
                new WxBrandUserOrderItemDTO("tradeOrderGuid", "logUrl",
                        new WxStoreConsumerDTO("consumerGuid", "openId", "unionId", "nickName", "headImgUrl", 0,
                                "country", "province", "city","","", "phone", 0, "deviceId", "enterpriseGuid",
                                "enterpriseName", "storeGuid", "storeName", "userGuid", "userName", "account", 0,
                                "diningTableGuid", "tableCode", "diningTableName", "areaGuid", "areaName", "brandName",
                                "brandGuid", "brandLogo", false, "memberInfoGuid", "phoneNum", false, "operSubjectGuid",
                                false), 0, new BigDecimal("0.00"), 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        Arrays.asList("value"))));
        when(mockWxOrderRecordClientService.getWxBrandUserOrder(new WxBrandUserOrderReqDTO(
                new WxStoreConsumerDTO("consumerGuid", "openId", "unionId", "nickName", "headImgUrl", 0, "country",
                        "province", "city", "","","phone", 0, "deviceId", "enterpriseGuid", "enterpriseName", "storeGuid",
                        "storeName", "userGuid", "userName", "account", 0, "diningTableGuid", "tableCode",
                        "diningTableName", "areaGuid", "areaName", "brandName", "brandGuid", "brandLogo", false,
                        "memberInfoGuid", "phoneNum", false, "operSubjectGuid", false), 0)))
                .thenReturn(wxBrandUserOrderDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_member_center_dine/brand_user_order")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
