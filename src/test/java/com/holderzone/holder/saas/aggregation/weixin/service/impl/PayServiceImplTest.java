package com.holderzone.holder.saas.aggregation.weixin.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.weixin.entity.dto.DealMemberPayReqDTO;
import com.holderzone.holder.saas.aggregation.weixin.service.MenuItemService;
import com.holderzone.holder.saas.aggregation.weixin.service.WxStoreTradeOrderService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.*;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.DineInOrderClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.TradeClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.WxClientService;
import com.holderzone.holder.saas.aggregation.weixin.utils.UserMemberSessionUtils;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseIntegralOffset;
import com.holderzone.holder.saas.weixin.entry.dto.*;
import com.holderzone.holder.saas.weixin.entry.dto.req.WxOrderDetailReqDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WebsocketUtils;
import com.holderzone.saas.store.dto.business.manage.HandoverPayDetailDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverRecordDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.ItemAttrDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.pay.AggPayRespDTO;
import com.holderzone.saas.store.dto.pay.KbzPayStartDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.weixin.WxPrepayRespDTO;
import com.holderzone.saas.store.dto.weixin.deal.*;
import com.holderzone.saas.store.dto.weixin.req.WxMemberTradeNotifyReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.PayWayRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PayServiceImplTest {

    @Mock
    private WxClientService mockWxClientService;
    @Mock
    private TradeClientService mockTradeClientService;
    @Mock
    private UserMemberSessionUtils mockUserMemberSessionUtils;
    @Mock
    private WxOrderRecordClientService mockWxOrderRecordClientService;
    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private WebsocketUtils mockWebsocketUtils;
    @Mock
    private MenuItemService mockMenuItemService;
    @Mock
    private WxStorePayClientService mockWxStorePayClientService;
    @Mock
    private HsaBaseClientService mockHsaBaseClientService;
    @Mock
    private BusinessClientService mockBusinessClientService;
    @Mock
    private StoreOrganizationClientService mockStoreOrganizationClientService;
    @Mock
    private WxStoreTradeOrderService mockWxStoreTradeOrderService;
    @Mock
    private DineInOrderClientService mockDineInOrderClientService;
    @Mock
    private TradeOrderService mockTradeOrderService;
    @Mock
    private StringRedisTemplate mockStringRedisTemplate;
    @Mock
    private MemberMarketingClientService mockMemberMarketingClientService;

    private PayServiceImpl payServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        payServiceImplUnderTest = new PayServiceImpl(mockWxClientService, mockTradeClientService,
                mockUserMemberSessionUtils, mockWxOrderRecordClientService, mockRedisUtils, mockWebsocketUtils);
        ReflectionTestUtils.setField(payServiceImplUnderTest, "memberPayKey", "memberPayKey");
        ReflectionTestUtils.setField(payServiceImplUnderTest, "zhuancanRequestHost", "zhuancanRequestHost");
        ReflectionTestUtils.setField(payServiceImplUnderTest, "wxStorePayClientService", mockWxStorePayClientService);
        ReflectionTestUtils.setField(payServiceImplUnderTest, "hsaBaseClientService", mockHsaBaseClientService);
        ReflectionTestUtils.setField(payServiceImplUnderTest, "businessClientService", mockBusinessClientService);
        ReflectionTestUtils.setField(payServiceImplUnderTest, "storeOrganizationClientService",
                mockStoreOrganizationClientService);
        ReflectionTestUtils.setField(payServiceImplUnderTest, "wxStoreTradeOrderService", mockWxStoreTradeOrderService);
        ReflectionTestUtils.setField(payServiceImplUnderTest, "dineInOrderClientService", mockDineInOrderClientService);
        ReflectionTestUtils.setField(payServiceImplUnderTest, "tradeOrderService", mockTradeOrderService);
        ReflectionTestUtils.setField(payServiceImplUnderTest, "stringRedisTemplate", mockStringRedisTemplate);
        ReflectionTestUtils.setField(payServiceImplUnderTest, "memberMarketingClientService",
                mockMemberMarketingClientService);
        payServiceImplUnderTest.menuItemService = mockMenuItemService;
    }

    @Test
    public void testPrepay() {
        // Setup
        final WxPrepayRespDTO expectedResult = new WxPrepayRespDTO(0, "errorMsg");

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure WxOrderRecordClientService.detailCalculate(...).
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setGuid("fc98ba63-be61-4e1c-a274-f9452209e4ac");
        itemAttrDTO.setAttrGuid("attrGuid");
        itemAttrDTO.setAttrName("attrName");
        itemAttrDTO.setAttrGroupName("attrGroupName");
        itemAttrDTO.setAttrGroupGuid("attrGroupGuid");
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setGuid("fc98ba63-be61-4e1c-a274-f9452209e4ac");
        itemAttrDTO1.setAttrGuid("attrGuid");
        itemAttrDTO1.setAttrName("attrName");
        itemAttrDTO1.setAttrGroupName("attrGroupName");
        itemAttrDTO1.setAttrGroupGuid("attrGroupGuid");
        final AppendFeeDetailDTO appendFeeDetailDTO = new AppendFeeDetailDTO();
        appendFeeDetailDTO.setName("name");
        appendFeeDetailDTO.setCount(0);
        appendFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO.setTotalAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO.setType(0);
        final Result<OrderDetailDTO> orderDetailDTOResult = Result.buildSuccessResult(
                new OrderDetailDTO(0, "orderNo", "19adbd61-b1e3-4e12-ad58-b1768b1ef050", "orderGuid", "mark", "remark",
                        "mergeInfo", "brandGuid", "brandName", "storeGuid", "storeName", "diningTableGuid",
                        "diningTableCode", "areaGuid", "areaName", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, Arrays.asList(
                        new OrderTableItemDTO("tableGuid", "tableCode", "areaName", "diningTableName", 0, 0, 0,
                                new HashSet<>(Arrays.asList(
                                        new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid",
                                                "userRecordGuid", "operateGuid", false,
                                                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "remark", "tableGuid",
                                                Arrays.asList(new DineItemDTO("ae79f0f5-851c-45f7-b328-ee06f228852c",
                                                        "itemGuid", "itemName", "code", 0, "skuGuid", "skuName",
                                                        new BigDecimal("0.00"), "amountUnit", new BigDecimal("0.00"),
                                                        new BigDecimal("0.00"), false, new BigDecimal("0.00"),
                                                        new BigDecimal("0.00"), Arrays.asList(itemAttrDTO),
                                                        Arrays.asList(
                                                                new PackageSubitemDTO("subgroupGuid", "subgroupName")),
                                                        "remark", "itemImg", false))))), Arrays.asList(
                                new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid",
                                        "userRecordGuid", "operateGuid", false, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                                        0, "remark", "tableGuid", Arrays.asList(
                                        new DineItemDTO("ae79f0f5-851c-45f7-b328-ee06f228852c", "itemGuid", "itemName",
                                                "code", 0, "skuGuid", "skuName", new BigDecimal("0.00"), "amountUnit",
                                                new BigDecimal("0.00"), new BigDecimal("0.00"), false,
                                                new BigDecimal("0.00"), new BigDecimal("0.00"),
                                                Arrays.asList(itemAttrDTO1),
                                                Arrays.asList(new PackageSubitemDTO("subgroupGuid", "subgroupName")),
                                                "remark", "itemImg", false)))), 0)),
                        Arrays.asList(new SettlementItemDTO("itemName", new BigDecimal("0.00"), 0, 0)),
                        Arrays.asList(appendFeeDetailDTO), new BigDecimal("0.00"), "couponId", false, 0,
                        new BigDecimal("0.00"), false, "memberCardGuid", false, 0, "payWay", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), 0, new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), false, 2, Lists.newArrayList()));
        when(mockWxOrderRecordClientService.detailCalculate(
                new WxOrderDetailReqDTO("fffc065f-ff57-4cd2-bd61-1150ecc23eb0", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(orderDetailDTOResult);

        // Run the test
        final WxPrepayRespDTO result = payServiceImplUnderTest.prepay("49b1551b-066a-4be2-b74c-83ff1fee1310");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testPrepay_UserMemberSessionUtilsReturnsNull() {
        // Setup
        final WxPrepayRespDTO expectedResult = new WxPrepayRespDTO(0, "errorMsg");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(null);

        // Configure WxOrderRecordClientService.detailCalculate(...).
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setGuid("fc98ba63-be61-4e1c-a274-f9452209e4ac");
        itemAttrDTO.setAttrGuid("attrGuid");
        itemAttrDTO.setAttrName("attrName");
        itemAttrDTO.setAttrGroupName("attrGroupName");
        itemAttrDTO.setAttrGroupGuid("attrGroupGuid");
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setGuid("fc98ba63-be61-4e1c-a274-f9452209e4ac");
        itemAttrDTO1.setAttrGuid("attrGuid");
        itemAttrDTO1.setAttrName("attrName");
        itemAttrDTO1.setAttrGroupName("attrGroupName");
        itemAttrDTO1.setAttrGroupGuid("attrGroupGuid");
        final AppendFeeDetailDTO appendFeeDetailDTO = new AppendFeeDetailDTO();
        appendFeeDetailDTO.setName("name");
        appendFeeDetailDTO.setCount(0);
        appendFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO.setTotalAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO.setType(0);
        final Result<OrderDetailDTO> orderDetailDTOResult = Result.buildSuccessResult(
                new OrderDetailDTO(0, "orderNo", "19adbd61-b1e3-4e12-ad58-b1768b1ef050", "orderGuid", "mark", "remark",
                        "mergeInfo", "brandGuid", "brandName", "storeGuid", "storeName", "diningTableGuid",
                        "diningTableCode", "areaGuid", "areaName", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, Arrays.asList(
                        new OrderTableItemDTO("tableGuid", "tableCode", "areaName", "diningTableName", 0, 0, 0,
                                new HashSet<>(Arrays.asList(
                                        new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid",
                                                "userRecordGuid", "operateGuid", false,
                                                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "remark", "tableGuid",
                                                Arrays.asList(new DineItemDTO("ae79f0f5-851c-45f7-b328-ee06f228852c",
                                                        "itemGuid", "itemName", "code", 0, "skuGuid", "skuName",
                                                        new BigDecimal("0.00"), "amountUnit", new BigDecimal("0.00"),
                                                        new BigDecimal("0.00"), false, new BigDecimal("0.00"),
                                                        new BigDecimal("0.00"), Arrays.asList(itemAttrDTO),
                                                        Arrays.asList(
                                                                new PackageSubitemDTO("subgroupGuid", "subgroupName")),
                                                        "remark", "itemImg", false))))), Arrays.asList(
                                new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid",
                                        "userRecordGuid", "operateGuid", false, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                                        0, "remark", "tableGuid", Arrays.asList(
                                        new DineItemDTO("ae79f0f5-851c-45f7-b328-ee06f228852c", "itemGuid", "itemName",
                                                "code", 0, "skuGuid", "skuName", new BigDecimal("0.00"), "amountUnit",
                                                new BigDecimal("0.00"), new BigDecimal("0.00"), false,
                                                new BigDecimal("0.00"), new BigDecimal("0.00"),
                                                Arrays.asList(itemAttrDTO1),
                                                Arrays.asList(new PackageSubitemDTO("subgroupGuid", "subgroupName")),
                                                "remark", "itemImg", false)))), 0)),
                        Arrays.asList(new SettlementItemDTO("itemName", new BigDecimal("0.00"), 0, 0)),
                        Arrays.asList(appendFeeDetailDTO), new BigDecimal("0.00"), "couponId", false, 0,
                        new BigDecimal("0.00"), false, "memberCardGuid", false, 0, "payWay", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), 0, new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), false, 2, Lists.newArrayList()));
        when(mockWxOrderRecordClientService.detailCalculate(
                new WxOrderDetailReqDTO("fffc065f-ff57-4cd2-bd61-1150ecc23eb0", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(orderDetailDTOResult);

        // Run the test
        final WxPrepayRespDTO result = payServiceImplUnderTest.prepay("49b1551b-066a-4be2-b74c-83ff1fee1310");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testPrepay_WxOrderRecordClientServiceReturnsNoItem() {
        // Setup
        final WxPrepayRespDTO expectedResult = new WxPrepayRespDTO(0, "errorMsg");

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        when(mockWxOrderRecordClientService.detailCalculate(
                new WxOrderDetailReqDTO("fffc065f-ff57-4cd2-bd61-1150ecc23eb0", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(Result.buildEmptySuccess());

        // Run the test
        final WxPrepayRespDTO result = payServiceImplUnderTest.prepay("49b1551b-066a-4be2-b74c-83ff1fee1310");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testPrepay_WxOrderRecordClientServiceReturnsFailure() {
        // Setup
        final WxPrepayRespDTO expectedResult = new WxPrepayRespDTO(0, "errorMsg");

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure WxOrderRecordClientService.detailCalculate(...).
        final Result<OrderDetailDTO> orderDetailDTOResult = Result.buildOpFailedResult(new Exception("message"));
        when(mockWxOrderRecordClientService.detailCalculate(
                new WxOrderDetailReqDTO("fffc065f-ff57-4cd2-bd61-1150ecc23eb0", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(orderDetailDTOResult);

        // Run the test
        final WxPrepayRespDTO result = payServiceImplUnderTest.prepay("49b1551b-066a-4be2-b74c-83ff1fee1310");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetAllPayWay() {
        // Setup
        final PayWayRespDTO expectedResult = new PayWayRespDTO(0, "errorMsg", 0, 0, new BigDecimal("0.00"),
                new BigDecimal("0.00"), 0, new BigDecimal("0.00"), "cardName");
        when(mockWxClientService.getMerchantOrderGuid("orderGuid")).thenReturn("result");

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure TradeClientService.calculate(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setSkuName("skuName");
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setMemberCardGuid("memberInfoCardGuid");
        dineinOrderDetailRespDTO.setVersion(0);
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setDeviceType(0);
        billCalculateReqDTO.setDeviceId("deviceId");
        billCalculateReqDTO.setEnterpriseGuid("enterpriseGuid");
        billCalculateReqDTO.setStoreGuid("storeGuid");
        billCalculateReqDTO.setStoreName("storeName");
        billCalculateReqDTO.setUserGuid("createGuid");
        billCalculateReqDTO.setUserName("createName");
        billCalculateReqDTO.setOrderGuid("merchantOrderGuid");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setMemberIntegral(0);
        billCalculateReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO.setMemberPhone("memberPhone");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVerify(0);
        when(mockTradeClientService.calculate(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO);

        // Configure MenuItemService.cardList(...).
        final List<UserMemberCardCacheDTO> userMemberCardCacheDTOS = Arrays.asList(
                new UserMemberCardCacheDTO("cardQRCode", "cardGuid", "memberInfoCardGuid", "systemManagementGuid",
                        "enterpriseMemberInfoSystemGuid", "cardName", "cardLogo", "cardIcon", "cardColour", 0,
                        "cardStartDate", "cardEndDate", 0, "systemManagementCardNum", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "cardIntegral", "cardLevelGuid", "cardLevelName", 0, "levelIcon", 0, 0,
                        false));
        when(mockMenuItemService.cardList()).thenReturn(userMemberCardCacheDTOS);

        // Run the test
        final PayWayRespDTO result = payServiceImplUnderTest.getAllPayWay("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockUserMemberSessionUtils).addUserMemberSession(
                new UserMemberSessionDTO("storeGuid", "openId", "userGuid", false, "nickName", "memberInfoGuid",
                        "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false, "volumeCode", false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid"));
    }

    @Test
    public void testGetAllPayWay_WxClientServiceReturnsNull() {
        // Setup
        final PayWayRespDTO expectedResult = new PayWayRespDTO(0, "errorMsg", 0, 0, new BigDecimal("0.00"),
                new BigDecimal("0.00"), 0, new BigDecimal("0.00"), "cardName");
        when(mockWxClientService.getMerchantOrderGuid("orderGuid")).thenReturn(null);

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure TradeClientService.calculate(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setSkuName("skuName");
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setMemberCardGuid("memberInfoCardGuid");
        dineinOrderDetailRespDTO.setVersion(0);
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setDeviceType(0);
        billCalculateReqDTO.setDeviceId("deviceId");
        billCalculateReqDTO.setEnterpriseGuid("enterpriseGuid");
        billCalculateReqDTO.setStoreGuid("storeGuid");
        billCalculateReqDTO.setStoreName("storeName");
        billCalculateReqDTO.setUserGuid("createGuid");
        billCalculateReqDTO.setUserName("createName");
        billCalculateReqDTO.setOrderGuid("merchantOrderGuid");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setMemberIntegral(0);
        billCalculateReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO.setMemberPhone("memberPhone");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVerify(0);
        when(mockTradeClientService.calculate(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO);

        // Configure MenuItemService.cardList(...).
        final List<UserMemberCardCacheDTO> userMemberCardCacheDTOS = Arrays.asList(
                new UserMemberCardCacheDTO("cardQRCode", "cardGuid", "memberInfoCardGuid", "systemManagementGuid",
                        "enterpriseMemberInfoSystemGuid", "cardName", "cardLogo", "cardIcon", "cardColour", 0,
                        "cardStartDate", "cardEndDate", 0, "systemManagementCardNum", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "cardIntegral", "cardLevelGuid", "cardLevelName", 0, "levelIcon", 0, 0,
                        false));
        when(mockMenuItemService.cardList()).thenReturn(userMemberCardCacheDTOS);

        // Run the test
        final PayWayRespDTO result = payServiceImplUnderTest.getAllPayWay("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockUserMemberSessionUtils).addUserMemberSession(
                new UserMemberSessionDTO("storeGuid", "openId", "userGuid", false, "nickName", "memberInfoGuid",
                        "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false, "volumeCode", false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid"));
    }

    @Test
    public void testGetAllPayWay_MenuItemServiceReturnsNoItems() {
        // Setup
        final PayWayRespDTO expectedResult = new PayWayRespDTO(0, "errorMsg", 0, 0, new BigDecimal("0.00"),
                new BigDecimal("0.00"), 0, new BigDecimal("0.00"), "cardName");
        when(mockWxClientService.getMerchantOrderGuid("orderGuid")).thenReturn("result");

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure TradeClientService.calculate(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setSkuName("skuName");
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setMemberCardGuid("memberInfoCardGuid");
        dineinOrderDetailRespDTO.setVersion(0);
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setDeviceType(0);
        billCalculateReqDTO.setDeviceId("deviceId");
        billCalculateReqDTO.setEnterpriseGuid("enterpriseGuid");
        billCalculateReqDTO.setStoreGuid("storeGuid");
        billCalculateReqDTO.setStoreName("storeName");
        billCalculateReqDTO.setUserGuid("createGuid");
        billCalculateReqDTO.setUserName("createName");
        billCalculateReqDTO.setOrderGuid("merchantOrderGuid");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setMemberIntegral(0);
        billCalculateReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO.setMemberPhone("memberPhone");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVerify(0);
        when(mockTradeClientService.calculate(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO);

        when(mockMenuItemService.cardList()).thenReturn(Collections.emptyList());

        // Run the test
        final PayWayRespDTO result = payServiceImplUnderTest.getAllPayWay("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockUserMemberSessionUtils).addUserMemberSession(
                new UserMemberSessionDTO("storeGuid", "openId", "userGuid", false, "nickName", "memberInfoGuid",
                        "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false, "volumeCode", false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid"));
    }

    @Test
    public void testZeroPay1() {
        // Setup
        final DealMemberPayReqDTO dealMemberPayReqDTO = new DealMemberPayReqDTO("orderGuid", "memberPassWord", 0, 0, 0,
                false, new BigDecimal("0.00"), false, "memberInfoCardGuid");
        final ZeroPayResultRespDTO expectedResult = new ZeroPayResultRespDTO(0, "errorMsg");
        when(mockWxClientService.getMerchantOrderGuid("orderGuid")).thenReturn("result");

        // Configure TradeClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setSkuName("skuName");
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setMemberCardGuid("memberInfoCardGuid");
        dineinOrderDetailRespDTO.setVersion(0);
        when(mockTradeClientService.getOrderDetail("orderGuid")).thenReturn(dineinOrderDetailRespDTO);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure BusinessClientService.queryOnDutyStaffs(...).
        final List<HandoverRecordDTO> handoverRecordDTOS = Arrays.asList(
                new HandoverRecordDTO("storeGuid", "storeName", "terminalId", "handoverRecordGuid", "createGuid",
                        "createName", new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        "confirmUserGuid", "confirmUserName", 0, 0, 0, 0, new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        new BigDecimal("0.00"), Arrays.asList(
                        new HandoverPayDetailDTO("handoverRecordGuid", "terminalId", "payType", "payTypeName",
                                new BigDecimal("0.00"), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                                LocalDateTime.of(2020, 1, 1, 0, 0, 0))), new BigDecimal("0.00"),
                        new BigDecimal("0.00")));
        when(mockBusinessClientService.queryOnDutyStaffs("storeGuid")).thenReturn(handoverRecordDTOS);

        // Configure StoreOrganizationClientService.getMasterDeviceByStoreGuid(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO("enterpriseGuid", "storeNo", "storeGuid", "storeName",
                "deviceNo", "deviceGuid", false, false, 0, "deviceName", 0, "createUserGuid", "createUserName",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "tableGuid", 0);
        when(mockStoreOrganizationClientService.getMasterDeviceByStoreGuid("enterpriseGuid", "storeGuid"))
                .thenReturn(storeDeviceDTO);

        // Configure TradeClientService.pay(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final BillPayReqDTO billPayReqDTO = new BillPayReqDTO();
        billPayReqDTO.setDeviceType(0);
        billPayReqDTO.setDeviceId("deviceId");
        billPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        billPayReqDTO.setStoreGuid("storeGuid");
        billPayReqDTO.setStoreName("storeName");
        billPayReqDTO.setUserGuid("createGuid");
        billPayReqDTO.setUserName("createName");
        billPayReqDTO.setOrderGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        billPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        billPayReqDTO.setAppendFee(new BigDecimal("0.00"));
        billPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setChangeFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final BillPayReqDTO.Payment payment = new BillPayReqDTO.Payment();
        payment.setAmount(new BigDecimal("0.00"));
        payment.setPaymentType(0);
        payment.setPaymentTypeName("会员卡支付");
        billPayReqDTO.setPayments(Arrays.asList(payment));
        billPayReqDTO.setFastFood(false);
        billPayReqDTO.setMemberPassWord("memberPassWord");
        billPayReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billPayReqDTO.setUseIntegral(0);
        billPayReqDTO.setIntegralDiscountMoney(new BigDecimal("0.00"));
        billPayReqDTO.setVersion(0);
        when(mockTradeClientService.pay(billPayReqDTO)).thenReturn(estimateItemRespDTO);

        when(mockRedisUtils.hKeyList("key")).thenReturn(new HashSet<>(Arrays.asList("value")));

        // Run the test
        final ZeroPayResultRespDTO result = payServiceImplUnderTest.zeroPay(dealMemberPayReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockRedisUtils).delete("key");
        verify(mockWxClientService).payBackOrder(
                new PayBackOrderRecordDTO("46197127-c9f2-429b-a853-a5ce0acdb4fb", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        verify(mockRedisUtils).set("key", "memberInfoCardGuid");
        verify(mockUserMemberSessionUtils).delAllUserSession(new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delTableCardList("storeGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockWebsocketUtils).clear("tableGuid");
        verify(mockUserMemberSessionUtils).expireUserSession(new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delCardList("storeGuid", "openId");
        verify(mockWxStoreTradeOrderService).removeFastGuestCount("diningTableGuid", "openId");
        verify(mockWxStoreTradeOrderService).transformSurchargeCache("53f8a09f-43d3-493f-be67-e8b275497007");
        verify(mockWxStorePayClientService).sendWeixinNotifyMessage(
                new WxMemberTradeNotifyReqDTO("enterpriseGuid", "openId", "storeGuid", "storeName",
                        new BigDecimal("0.00"), "orderGuid", "memberInfoCardGuid", "brandGuid"));
    }

    @Test
    public void testZeroPay1_WxClientServiceGetMerchantOrderGuidReturnsNull() {
        // Setup
        final DealMemberPayReqDTO dealMemberPayReqDTO = new DealMemberPayReqDTO("orderGuid", "memberPassWord", 0, 0, 0,
                false, new BigDecimal("0.00"), false, "memberInfoCardGuid");
        final ZeroPayResultRespDTO expectedResult = new ZeroPayResultRespDTO(0, "errorMsg");
        when(mockWxClientService.getMerchantOrderGuid("orderGuid")).thenReturn(null);

        // Configure TradeClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setSkuName("skuName");
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setMemberCardGuid("memberInfoCardGuid");
        dineinOrderDetailRespDTO.setVersion(0);
        when(mockTradeClientService.getOrderDetail("orderGuid")).thenReturn(dineinOrderDetailRespDTO);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure BusinessClientService.queryOnDutyStaffs(...).
        final List<HandoverRecordDTO> handoverRecordDTOS = Arrays.asList(
                new HandoverRecordDTO("storeGuid", "storeName", "terminalId", "handoverRecordGuid", "createGuid",
                        "createName", new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        "confirmUserGuid", "confirmUserName", 0, 0, 0, 0, new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        new BigDecimal("0.00"), Arrays.asList(
                        new HandoverPayDetailDTO("handoverRecordGuid", "terminalId", "payType", "payTypeName",
                                new BigDecimal("0.00"), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                                LocalDateTime.of(2020, 1, 1, 0, 0, 0))), new BigDecimal("0.00"),
                        new BigDecimal("0.00")));
        when(mockBusinessClientService.queryOnDutyStaffs("storeGuid")).thenReturn(handoverRecordDTOS);

        // Configure StoreOrganizationClientService.getMasterDeviceByStoreGuid(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO("enterpriseGuid", "storeNo", "storeGuid", "storeName",
                "deviceNo", "deviceGuid", false, false, 0, "deviceName", 0, "createUserGuid", "createUserName",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "tableGuid", 0);
        when(mockStoreOrganizationClientService.getMasterDeviceByStoreGuid("enterpriseGuid", "storeGuid"))
                .thenReturn(storeDeviceDTO);

        // Configure TradeClientService.pay(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final BillPayReqDTO billPayReqDTO = new BillPayReqDTO();
        billPayReqDTO.setDeviceType(0);
        billPayReqDTO.setDeviceId("deviceId");
        billPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        billPayReqDTO.setStoreGuid("storeGuid");
        billPayReqDTO.setStoreName("storeName");
        billPayReqDTO.setUserGuid("createGuid");
        billPayReqDTO.setUserName("createName");
        billPayReqDTO.setOrderGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        billPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        billPayReqDTO.setAppendFee(new BigDecimal("0.00"));
        billPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setChangeFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final BillPayReqDTO.Payment payment = new BillPayReqDTO.Payment();
        payment.setAmount(new BigDecimal("0.00"));
        payment.setPaymentType(0);
        payment.setPaymentTypeName("会员卡支付");
        billPayReqDTO.setPayments(Arrays.asList(payment));
        billPayReqDTO.setFastFood(false);
        billPayReqDTO.setMemberPassWord("memberPassWord");
        billPayReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billPayReqDTO.setUseIntegral(0);
        billPayReqDTO.setIntegralDiscountMoney(new BigDecimal("0.00"));
        billPayReqDTO.setVersion(0);
        when(mockTradeClientService.pay(billPayReqDTO)).thenReturn(estimateItemRespDTO);

        when(mockRedisUtils.hKeyList("key")).thenReturn(new HashSet<>(Arrays.asList("value")));

        // Run the test
        final ZeroPayResultRespDTO result = payServiceImplUnderTest.zeroPay(dealMemberPayReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockRedisUtils).delete("key");
        verify(mockWxClientService).payBackOrder(
                new PayBackOrderRecordDTO("46197127-c9f2-429b-a853-a5ce0acdb4fb", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        verify(mockRedisUtils).set("key", "memberInfoCardGuid");
        verify(mockUserMemberSessionUtils).delAllUserSession(new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delTableCardList("storeGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockWebsocketUtils).clear("tableGuid");
        verify(mockUserMemberSessionUtils).expireUserSession(new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delCardList("storeGuid", "openId");
        verify(mockWxStoreTradeOrderService).removeFastGuestCount("diningTableGuid", "openId");
        verify(mockWxStoreTradeOrderService).transformSurchargeCache("53f8a09f-43d3-493f-be67-e8b275497007");
        verify(mockWxStorePayClientService).sendWeixinNotifyMessage(
                new WxMemberTradeNotifyReqDTO("enterpriseGuid", "openId", "storeGuid", "storeName",
                        new BigDecimal("0.00"), "orderGuid", "memberInfoCardGuid", "brandGuid"));
    }

    @Test
    public void testZeroPay1_BusinessClientServiceReturnsNoItems() {
        // Setup
        final DealMemberPayReqDTO dealMemberPayReqDTO = new DealMemberPayReqDTO("orderGuid", "memberPassWord", 0, 0, 0,
                false, new BigDecimal("0.00"), false, "memberInfoCardGuid");
        final ZeroPayResultRespDTO expectedResult = new ZeroPayResultRespDTO(0, "errorMsg");
        when(mockWxClientService.getMerchantOrderGuid("orderGuid")).thenReturn("result");

        // Configure TradeClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setSkuName("skuName");
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setMemberCardGuid("memberInfoCardGuid");
        dineinOrderDetailRespDTO.setVersion(0);
        when(mockTradeClientService.getOrderDetail("orderGuid")).thenReturn(dineinOrderDetailRespDTO);

        when(mockBusinessClientService.queryOnDutyStaffs("storeGuid")).thenReturn(Collections.emptyList());

        // Configure TradeClientService.pay(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final BillPayReqDTO billPayReqDTO = new BillPayReqDTO();
        billPayReqDTO.setDeviceType(0);
        billPayReqDTO.setDeviceId("deviceId");
        billPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        billPayReqDTO.setStoreGuid("storeGuid");
        billPayReqDTO.setStoreName("storeName");
        billPayReqDTO.setUserGuid("createGuid");
        billPayReqDTO.setUserName("createName");
        billPayReqDTO.setOrderGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        billPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        billPayReqDTO.setAppendFee(new BigDecimal("0.00"));
        billPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setChangeFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final BillPayReqDTO.Payment payment = new BillPayReqDTO.Payment();
        payment.setAmount(new BigDecimal("0.00"));
        payment.setPaymentType(0);
        payment.setPaymentTypeName("会员卡支付");
        billPayReqDTO.setPayments(Arrays.asList(payment));
        billPayReqDTO.setFastFood(false);
        billPayReqDTO.setMemberPassWord("memberPassWord");
        billPayReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billPayReqDTO.setUseIntegral(0);
        billPayReqDTO.setIntegralDiscountMoney(new BigDecimal("0.00"));
        billPayReqDTO.setVersion(0);
        when(mockTradeClientService.pay(billPayReqDTO)).thenReturn(estimateItemRespDTO);

        when(mockRedisUtils.hKeyList("key")).thenReturn(new HashSet<>(Arrays.asList("value")));

        // Run the test
        final ZeroPayResultRespDTO result = payServiceImplUnderTest.zeroPay(dealMemberPayReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockRedisUtils).delete("key");
        verify(mockWxClientService).payBackOrder(
                new PayBackOrderRecordDTO("46197127-c9f2-429b-a853-a5ce0acdb4fb", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        verify(mockRedisUtils).set("key", "memberInfoCardGuid");
        verify(mockUserMemberSessionUtils).delAllUserSession(new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delTableCardList("storeGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockWebsocketUtils).clear("tableGuid");
        verify(mockUserMemberSessionUtils).expireUserSession(new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delCardList("storeGuid", "openId");
        verify(mockWxStoreTradeOrderService).removeFastGuestCount("diningTableGuid", "openId");
        verify(mockWxStoreTradeOrderService).transformSurchargeCache("53f8a09f-43d3-493f-be67-e8b275497007");
        verify(mockWxStorePayClientService).sendWeixinNotifyMessage(
                new WxMemberTradeNotifyReqDTO("enterpriseGuid", "openId", "storeGuid", "storeName",
                        new BigDecimal("0.00"), "orderGuid", "memberInfoCardGuid", "brandGuid"));
    }

    @Test
    public void testZeroPay1_RedisUtilsHKeyListReturnsNoItems() {
        // Setup
        final DealMemberPayReqDTO dealMemberPayReqDTO = new DealMemberPayReqDTO("orderGuid", "memberPassWord", 0, 0, 0,
                false, new BigDecimal("0.00"), false, "memberInfoCardGuid");
        final ZeroPayResultRespDTO expectedResult = new ZeroPayResultRespDTO(0, "errorMsg");
        when(mockWxClientService.getMerchantOrderGuid("orderGuid")).thenReturn("result");

        // Configure TradeClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setSkuName("skuName");
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setMemberCardGuid("memberInfoCardGuid");
        dineinOrderDetailRespDTO.setVersion(0);
        when(mockTradeClientService.getOrderDetail("orderGuid")).thenReturn(dineinOrderDetailRespDTO);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure TradeClientService.pay(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final BillPayReqDTO billPayReqDTO = new BillPayReqDTO();
        billPayReqDTO.setDeviceType(0);
        billPayReqDTO.setDeviceId("deviceId");
        billPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        billPayReqDTO.setStoreGuid("storeGuid");
        billPayReqDTO.setStoreName("storeName");
        billPayReqDTO.setUserGuid("createGuid");
        billPayReqDTO.setUserName("createName");
        billPayReqDTO.setOrderGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        billPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        billPayReqDTO.setAppendFee(new BigDecimal("0.00"));
        billPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setChangeFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final BillPayReqDTO.Payment payment = new BillPayReqDTO.Payment();
        payment.setAmount(new BigDecimal("0.00"));
        payment.setPaymentType(0);
        payment.setPaymentTypeName("会员卡支付");
        billPayReqDTO.setPayments(Arrays.asList(payment));
        billPayReqDTO.setFastFood(false);
        billPayReqDTO.setMemberPassWord("memberPassWord");
        billPayReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billPayReqDTO.setUseIntegral(0);
        billPayReqDTO.setIntegralDiscountMoney(new BigDecimal("0.00"));
        billPayReqDTO.setVersion(0);
        when(mockTradeClientService.pay(billPayReqDTO)).thenReturn(estimateItemRespDTO);

        when(mockRedisUtils.hKeyList("key")).thenReturn(Collections.emptySet());

        // Run the test
        final ZeroPayResultRespDTO result = payServiceImplUnderTest.zeroPay(dealMemberPayReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockRedisUtils).delete("key");
        verify(mockWxClientService).payBackOrder(
                new PayBackOrderRecordDTO("46197127-c9f2-429b-a853-a5ce0acdb4fb", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        verify(mockRedisUtils).set("key", "memberInfoCardGuid");
        verify(mockUserMemberSessionUtils).delAllUserSession(new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delTableCardList("storeGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockWebsocketUtils).clear("tableGuid");
        verify(mockWxStorePayClientService).sendWeixinNotifyMessage(
                new WxMemberTradeNotifyReqDTO("enterpriseGuid", "openId", "storeGuid", "storeName",
                        new BigDecimal("0.00"), "orderGuid", "memberInfoCardGuid", "brandGuid"));
    }

    @Test
    public void testMemberPay() {
        // Setup
        final DealMemberPayReqDTO dealMemberPayReqDTO = new DealMemberPayReqDTO("orderGuid", "memberPassWord", 0, 0, 0,
                false, new BigDecimal("0.00"), false, "memberInfoCardGuid");
        final MemberPayReusltRespDTO expectedResult = new MemberPayReusltRespDTO(0, "errorMsg");

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        when(mockWxClientService.getMerchantOrderGuid("orderGuid")).thenReturn("result");

        // Configure TradeClientService.calculate(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setSkuName("skuName");
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setMemberCardGuid("memberInfoCardGuid");
        dineinOrderDetailRespDTO.setVersion(0);
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setDeviceType(0);
        billCalculateReqDTO.setDeviceId("deviceId");
        billCalculateReqDTO.setEnterpriseGuid("enterpriseGuid");
        billCalculateReqDTO.setStoreGuid("storeGuid");
        billCalculateReqDTO.setStoreName("storeName");
        billCalculateReqDTO.setUserGuid("createGuid");
        billCalculateReqDTO.setUserName("createName");
        billCalculateReqDTO.setOrderGuid("merchantOrderGuid");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setMemberIntegral(0);
        billCalculateReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO.setMemberPhone("memberPhone");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVerify(0);
        when(mockTradeClientService.calculate(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO);

        // Configure TradeClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        dineinOrderDetailRespDTO1.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO1.setStoreName("storeName");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO1.setMemberGuid("memberGuid");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setItemName("itemName");
        dineInItemDTO1.setSkuName("skuName");
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        final ResponseIntegralOffset integralOffsetResultRespDTO1 = new ResponseIntegralOffset();
        integralOffsetResultRespDTO1.setUseIntegral(0);
        integralOffsetResultRespDTO1.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO1);
        dineinOrderDetailRespDTO1.setMemberCardGuid("memberInfoCardGuid");
        dineinOrderDetailRespDTO1.setVersion(0);
        when(mockTradeClientService.getOrderDetail("merchantOrderGuid")).thenReturn(dineinOrderDetailRespDTO1);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure BusinessClientService.queryOnDutyStaffs(...).
        final List<HandoverRecordDTO> handoverRecordDTOS = Arrays.asList(
                new HandoverRecordDTO("storeGuid", "storeName", "terminalId", "handoverRecordGuid", "createGuid",
                        "createName", new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        "confirmUserGuid", "confirmUserName", 0, 0, 0, 0, new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        new BigDecimal("0.00"), Arrays.asList(
                        new HandoverPayDetailDTO("handoverRecordGuid", "terminalId", "payType", "payTypeName",
                                new BigDecimal("0.00"), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                                LocalDateTime.of(2020, 1, 1, 0, 0, 0))), new BigDecimal("0.00"),
                        new BigDecimal("0.00")));
        when(mockBusinessClientService.queryOnDutyStaffs("storeGuid")).thenReturn(handoverRecordDTOS);

        // Configure StoreOrganizationClientService.getMasterDeviceByStoreGuid(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO("enterpriseGuid", "storeNo", "storeGuid", "storeName",
                "deviceNo", "deviceGuid", false, false, 0, "deviceName", 0, "createUserGuid", "createUserName",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "tableGuid", 0);
        when(mockStoreOrganizationClientService.getMasterDeviceByStoreGuid("enterpriseGuid", "storeGuid"))
                .thenReturn(storeDeviceDTO);

        // Configure TradeClientService.pay(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final BillPayReqDTO billPayReqDTO = new BillPayReqDTO();
        billPayReqDTO.setDeviceType(0);
        billPayReqDTO.setDeviceId("deviceId");
        billPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        billPayReqDTO.setStoreGuid("storeGuid");
        billPayReqDTO.setStoreName("storeName");
        billPayReqDTO.setUserGuid("createGuid");
        billPayReqDTO.setUserName("createName");
        billPayReqDTO.setOrderGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        billPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        billPayReqDTO.setAppendFee(new BigDecimal("0.00"));
        billPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setChangeFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO2 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO2.setDiscountType(0);
        discountFeeDetailDTO2.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO2));
        final BillPayReqDTO.Payment payment = new BillPayReqDTO.Payment();
        payment.setAmount(new BigDecimal("0.00"));
        payment.setPaymentType(0);
        payment.setPaymentTypeName("会员卡支付");
        billPayReqDTO.setPayments(Arrays.asList(payment));
        billPayReqDTO.setFastFood(false);
        billPayReqDTO.setMemberPassWord("memberPassWord");
        billPayReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billPayReqDTO.setUseIntegral(0);
        billPayReqDTO.setIntegralDiscountMoney(new BigDecimal("0.00"));
        billPayReqDTO.setVersion(0);
        when(mockTradeClientService.pay(billPayReqDTO)).thenReturn(estimateItemRespDTO);

        // Configure WxClientService.getItemListByOrderGuid(...).
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setGuid("85980212-72ab-4cfe-bfc8-9a4fcdac6a40");
        dineInItemDTO2.setOrderGuid("orderGuid");
        dineInItemDTO2.setItemName("itemName");
        dineInItemDTO2.setSkuName("skuName");
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        final List<DineInItemDTO> dineInItemDTOS = Arrays.asList(dineInItemDTO2);
        when(mockWxClientService.getItemListByOrderGuid("53f8a09f-43d3-493f-be67-e8b275497007"))
                .thenReturn(dineInItemDTOS);

        when(mockRedisUtils.hKeyList("key")).thenReturn(new HashSet<>(Arrays.asList("value")));

        // Run the test
        final MemberPayReusltRespDTO result = payServiceImplUnderTest.memberPay(dealMemberPayReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockUserMemberSessionUtils).addUserMemberSession(
                new UserMemberSessionDTO("storeGuid", "openId", "userGuid", false, "nickName", "memberInfoGuid",
                        "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false, "volumeCode", false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid"));
        verify(mockRedisUtils).delete("key");
        verify(mockWxClientService).payBackOrder(
                new PayBackOrderRecordDTO("46197127-c9f2-429b-a853-a5ce0acdb4fb", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        verify(mockRedisUtils).set("key", "memberInfoCardGuid");
        verify(mockUserMemberSessionUtils).delAllUserSession(new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delTableCardList("storeGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockWebsocketUtils).clear("tableGuid");
        verify(mockUserMemberSessionUtils).expireUserSession(new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delCardList("storeGuid", "openId");
        verify(mockWxStoreTradeOrderService).removeFastGuestCount("diningTableGuid", "openId");
        verify(mockWxStoreTradeOrderService).transformSurchargeCache("53f8a09f-43d3-493f-be67-e8b275497007");
        verify(mockWxStorePayClientService).sendWeixinNotifyMessage(
                new WxMemberTradeNotifyReqDTO("enterpriseGuid", "openId", "storeGuid", "storeName",
                        new BigDecimal("0.00"), "orderGuid", "memberInfoCardGuid", "brandGuid"));
    }

    @Test
    public void testMemberPay_WxClientServiceGetMerchantOrderGuidReturnsNull() {
        // Setup
        final DealMemberPayReqDTO dealMemberPayReqDTO = new DealMemberPayReqDTO("orderGuid", "memberPassWord", 0, 0, 0,
                false, new BigDecimal("0.00"), false, "memberInfoCardGuid");
        final MemberPayReusltRespDTO expectedResult = new MemberPayReusltRespDTO(0, "errorMsg");

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        when(mockWxClientService.getMerchantOrderGuid("orderGuid")).thenReturn(null);

        // Configure TradeClientService.calculate(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setSkuName("skuName");
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setMemberCardGuid("memberInfoCardGuid");
        dineinOrderDetailRespDTO.setVersion(0);
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setDeviceType(0);
        billCalculateReqDTO.setDeviceId("deviceId");
        billCalculateReqDTO.setEnterpriseGuid("enterpriseGuid");
        billCalculateReqDTO.setStoreGuid("storeGuid");
        billCalculateReqDTO.setStoreName("storeName");
        billCalculateReqDTO.setUserGuid("createGuid");
        billCalculateReqDTO.setUserName("createName");
        billCalculateReqDTO.setOrderGuid("merchantOrderGuid");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setMemberIntegral(0);
        billCalculateReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO.setMemberPhone("memberPhone");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVerify(0);
        when(mockTradeClientService.calculate(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO);

        // Configure TradeClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        dineinOrderDetailRespDTO1.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO1.setStoreName("storeName");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO1.setMemberGuid("memberGuid");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setItemName("itemName");
        dineInItemDTO1.setSkuName("skuName");
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        final ResponseIntegralOffset integralOffsetResultRespDTO1 = new ResponseIntegralOffset();
        integralOffsetResultRespDTO1.setUseIntegral(0);
        integralOffsetResultRespDTO1.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO1);
        dineinOrderDetailRespDTO1.setMemberCardGuid("memberInfoCardGuid");
        dineinOrderDetailRespDTO1.setVersion(0);
        when(mockTradeClientService.getOrderDetail("merchantOrderGuid")).thenReturn(dineinOrderDetailRespDTO1);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure BusinessClientService.queryOnDutyStaffs(...).
        final List<HandoverRecordDTO> handoverRecordDTOS = Arrays.asList(
                new HandoverRecordDTO("storeGuid", "storeName", "terminalId", "handoverRecordGuid", "createGuid",
                        "createName", new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        "confirmUserGuid", "confirmUserName", 0, 0, 0, 0, new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                        new BigDecimal("0.00"), Arrays.asList(
                        new HandoverPayDetailDTO("handoverRecordGuid", "terminalId", "payType", "payTypeName",
                                new BigDecimal("0.00"), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                                LocalDateTime.of(2020, 1, 1, 0, 0, 0))), new BigDecimal("0.00"),
                        new BigDecimal("0.00")));
        when(mockBusinessClientService.queryOnDutyStaffs("storeGuid")).thenReturn(handoverRecordDTOS);

        // Configure StoreOrganizationClientService.getMasterDeviceByStoreGuid(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO("enterpriseGuid", "storeNo", "storeGuid", "storeName",
                "deviceNo", "deviceGuid", false, false, 0, "deviceName", 0, "createUserGuid", "createUserName",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "tableGuid", 0);
        when(mockStoreOrganizationClientService.getMasterDeviceByStoreGuid("enterpriseGuid", "storeGuid"))
                .thenReturn(storeDeviceDTO);

        // Configure TradeClientService.pay(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final BillPayReqDTO billPayReqDTO = new BillPayReqDTO();
        billPayReqDTO.setDeviceType(0);
        billPayReqDTO.setDeviceId("deviceId");
        billPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        billPayReqDTO.setStoreGuid("storeGuid");
        billPayReqDTO.setStoreName("storeName");
        billPayReqDTO.setUserGuid("createGuid");
        billPayReqDTO.setUserName("createName");
        billPayReqDTO.setOrderGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        billPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        billPayReqDTO.setAppendFee(new BigDecimal("0.00"));
        billPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setChangeFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO2 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO2.setDiscountType(0);
        discountFeeDetailDTO2.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO2));
        final BillPayReqDTO.Payment payment = new BillPayReqDTO.Payment();
        payment.setAmount(new BigDecimal("0.00"));
        payment.setPaymentType(0);
        payment.setPaymentTypeName("会员卡支付");
        billPayReqDTO.setPayments(Arrays.asList(payment));
        billPayReqDTO.setFastFood(false);
        billPayReqDTO.setMemberPassWord("memberPassWord");
        billPayReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billPayReqDTO.setUseIntegral(0);
        billPayReqDTO.setIntegralDiscountMoney(new BigDecimal("0.00"));
        billPayReqDTO.setVersion(0);
        when(mockTradeClientService.pay(billPayReqDTO)).thenReturn(estimateItemRespDTO);

        // Configure WxClientService.getItemListByOrderGuid(...).
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setGuid("85980212-72ab-4cfe-bfc8-9a4fcdac6a40");
        dineInItemDTO2.setOrderGuid("orderGuid");
        dineInItemDTO2.setItemName("itemName");
        dineInItemDTO2.setSkuName("skuName");
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        final List<DineInItemDTO> dineInItemDTOS = Arrays.asList(dineInItemDTO2);
        when(mockWxClientService.getItemListByOrderGuid("53f8a09f-43d3-493f-be67-e8b275497007"))
                .thenReturn(dineInItemDTOS);

        when(mockRedisUtils.hKeyList("key")).thenReturn(new HashSet<>(Arrays.asList("value")));

        // Run the test
        final MemberPayReusltRespDTO result = payServiceImplUnderTest.memberPay(dealMemberPayReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockUserMemberSessionUtils).addUserMemberSession(
                new UserMemberSessionDTO("storeGuid", "openId", "userGuid", false, "nickName", "memberInfoGuid",
                        "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false, "volumeCode", false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid"));
        verify(mockRedisUtils).delete("key");
        verify(mockWxClientService).payBackOrder(
                new PayBackOrderRecordDTO("46197127-c9f2-429b-a853-a5ce0acdb4fb", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        verify(mockRedisUtils).set("key", "memberInfoCardGuid");
        verify(mockUserMemberSessionUtils).delAllUserSession(new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delTableCardList("storeGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockWebsocketUtils).clear("tableGuid");
        verify(mockUserMemberSessionUtils).expireUserSession(new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delCardList("storeGuid", "openId");
        verify(mockWxStoreTradeOrderService).removeFastGuestCount("diningTableGuid", "openId");
        verify(mockWxStoreTradeOrderService).transformSurchargeCache("53f8a09f-43d3-493f-be67-e8b275497007");
        verify(mockWxStorePayClientService).sendWeixinNotifyMessage(
                new WxMemberTradeNotifyReqDTO("enterpriseGuid", "openId", "storeGuid", "storeName",
                        new BigDecimal("0.00"), "orderGuid", "memberInfoCardGuid", "brandGuid"));
    }

    @Test
    public void testMemberPay_BusinessClientServiceReturnsNoItems() {
        // Setup
        final DealMemberPayReqDTO dealMemberPayReqDTO = new DealMemberPayReqDTO("orderGuid", "memberPassWord", 0, 0, 0,
                false, new BigDecimal("0.00"), false, "memberInfoCardGuid");
        final MemberPayReusltRespDTO expectedResult = new MemberPayReusltRespDTO(0, "errorMsg");

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        when(mockWxClientService.getMerchantOrderGuid("orderGuid")).thenReturn("result");

        // Configure TradeClientService.calculate(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setSkuName("skuName");
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setMemberCardGuid("memberInfoCardGuid");
        dineinOrderDetailRespDTO.setVersion(0);
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setDeviceType(0);
        billCalculateReqDTO.setDeviceId("deviceId");
        billCalculateReqDTO.setEnterpriseGuid("enterpriseGuid");
        billCalculateReqDTO.setStoreGuid("storeGuid");
        billCalculateReqDTO.setStoreName("storeName");
        billCalculateReqDTO.setUserGuid("createGuid");
        billCalculateReqDTO.setUserName("createName");
        billCalculateReqDTO.setOrderGuid("merchantOrderGuid");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setMemberIntegral(0);
        billCalculateReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO.setMemberPhone("memberPhone");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVerify(0);
        when(mockTradeClientService.calculate(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO);

        when(mockBusinessClientService.queryOnDutyStaffs("storeGuid")).thenReturn(Collections.emptyList());

        // Configure TradeClientService.pay(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final BillPayReqDTO billPayReqDTO = new BillPayReqDTO();
        billPayReqDTO.setDeviceType(0);
        billPayReqDTO.setDeviceId("deviceId");
        billPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        billPayReqDTO.setStoreGuid("storeGuid");
        billPayReqDTO.setStoreName("storeName");
        billPayReqDTO.setUserGuid("createGuid");
        billPayReqDTO.setUserName("createName");
        billPayReqDTO.setOrderGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        billPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        billPayReqDTO.setAppendFee(new BigDecimal("0.00"));
        billPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setChangeFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final BillPayReqDTO.Payment payment = new BillPayReqDTO.Payment();
        payment.setAmount(new BigDecimal("0.00"));
        payment.setPaymentType(0);
        payment.setPaymentTypeName("会员卡支付");
        billPayReqDTO.setPayments(Arrays.asList(payment));
        billPayReqDTO.setFastFood(false);
        billPayReqDTO.setMemberPassWord("memberPassWord");
        billPayReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billPayReqDTO.setUseIntegral(0);
        billPayReqDTO.setIntegralDiscountMoney(new BigDecimal("0.00"));
        billPayReqDTO.setVersion(0);
        when(mockTradeClientService.pay(billPayReqDTO)).thenReturn(estimateItemRespDTO);

        // Configure WxClientService.getItemListByOrderGuid(...).
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("85980212-72ab-4cfe-bfc8-9a4fcdac6a40");
        dineInItemDTO1.setOrderGuid("orderGuid");
        dineInItemDTO1.setItemName("itemName");
        dineInItemDTO1.setSkuName("skuName");
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        final List<DineInItemDTO> dineInItemDTOS = Arrays.asList(dineInItemDTO1);
        when(mockWxClientService.getItemListByOrderGuid("53f8a09f-43d3-493f-be67-e8b275497007"))
                .thenReturn(dineInItemDTOS);

        when(mockRedisUtils.hKeyList("key")).thenReturn(new HashSet<>(Arrays.asList("value")));

        // Run the test
        final MemberPayReusltRespDTO result = payServiceImplUnderTest.memberPay(dealMemberPayReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockUserMemberSessionUtils).addUserMemberSession(
                new UserMemberSessionDTO("storeGuid", "openId", "userGuid", false, "nickName", "memberInfoGuid",
                        "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false, "volumeCode", false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid"));
        verify(mockRedisUtils).delete("key");
        verify(mockWxClientService).payBackOrder(
                new PayBackOrderRecordDTO("46197127-c9f2-429b-a853-a5ce0acdb4fb", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        verify(mockRedisUtils).set("key", "memberInfoCardGuid");
        verify(mockUserMemberSessionUtils).delAllUserSession(new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delTableCardList("storeGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockWebsocketUtils).clear("tableGuid");
        verify(mockUserMemberSessionUtils).expireUserSession(new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delCardList("storeGuid", "openId");
        verify(mockWxStoreTradeOrderService).removeFastGuestCount("diningTableGuid", "openId");
        verify(mockWxStoreTradeOrderService).transformSurchargeCache("53f8a09f-43d3-493f-be67-e8b275497007");
        verify(mockWxStorePayClientService).sendWeixinNotifyMessage(
                new WxMemberTradeNotifyReqDTO("enterpriseGuid", "openId", "storeGuid", "storeName",
                        new BigDecimal("0.00"), "orderGuid", "memberInfoCardGuid", "brandGuid"));
    }

    @Test
    public void testMemberPay_WxClientServiceGetItemListByOrderGuidReturnsNoItems() {
        // Setup
        final DealMemberPayReqDTO dealMemberPayReqDTO = new DealMemberPayReqDTO("orderGuid", "memberPassWord", 0, 0, 0,
                false, new BigDecimal("0.00"), false, "memberInfoCardGuid");
        final MemberPayReusltRespDTO expectedResult = new MemberPayReusltRespDTO(0, "errorMsg");

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        when(mockWxClientService.getMerchantOrderGuid("orderGuid")).thenReturn("result");

        // Configure TradeClientService.calculate(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setSkuName("skuName");
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setMemberCardGuid("memberInfoCardGuid");
        dineinOrderDetailRespDTO.setVersion(0);
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setDeviceType(0);
        billCalculateReqDTO.setDeviceId("deviceId");
        billCalculateReqDTO.setEnterpriseGuid("enterpriseGuid");
        billCalculateReqDTO.setStoreGuid("storeGuid");
        billCalculateReqDTO.setStoreName("storeName");
        billCalculateReqDTO.setUserGuid("createGuid");
        billCalculateReqDTO.setUserName("createName");
        billCalculateReqDTO.setOrderGuid("merchantOrderGuid");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setMemberIntegral(0);
        billCalculateReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO.setMemberPhone("memberPhone");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVerify(0);
        when(mockTradeClientService.calculate(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure TradeClientService.pay(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final BillPayReqDTO billPayReqDTO = new BillPayReqDTO();
        billPayReqDTO.setDeviceType(0);
        billPayReqDTO.setDeviceId("deviceId");
        billPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        billPayReqDTO.setStoreGuid("storeGuid");
        billPayReqDTO.setStoreName("storeName");
        billPayReqDTO.setUserGuid("createGuid");
        billPayReqDTO.setUserName("createName");
        billPayReqDTO.setOrderGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        billPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        billPayReqDTO.setAppendFee(new BigDecimal("0.00"));
        billPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setChangeFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final BillPayReqDTO.Payment payment = new BillPayReqDTO.Payment();
        payment.setAmount(new BigDecimal("0.00"));
        payment.setPaymentType(0);
        payment.setPaymentTypeName("会员卡支付");
        billPayReqDTO.setPayments(Arrays.asList(payment));
        billPayReqDTO.setFastFood(false);
        billPayReqDTO.setMemberPassWord("memberPassWord");
        billPayReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billPayReqDTO.setUseIntegral(0);
        billPayReqDTO.setIntegralDiscountMoney(new BigDecimal("0.00"));
        billPayReqDTO.setVersion(0);
        when(mockTradeClientService.pay(billPayReqDTO)).thenReturn(estimateItemRespDTO);

        when(mockWxClientService.getItemListByOrderGuid("53f8a09f-43d3-493f-be67-e8b275497007"))
                .thenReturn(Collections.emptyList());
        when(mockRedisUtils.hKeyList("key")).thenReturn(new HashSet<>(Arrays.asList("value")));

        // Run the test
        final MemberPayReusltRespDTO result = payServiceImplUnderTest.memberPay(dealMemberPayReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockUserMemberSessionUtils).addUserMemberSession(
                new UserMemberSessionDTO("storeGuid", "openId", "userGuid", false, "nickName", "memberInfoGuid",
                        "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false, "volumeCode", false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid"));
        verify(mockRedisUtils).delete("key");
        verify(mockWxClientService).payBackOrder(
                new PayBackOrderRecordDTO("46197127-c9f2-429b-a853-a5ce0acdb4fb", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        verify(mockRedisUtils).set("key", "memberInfoCardGuid");
        verify(mockUserMemberSessionUtils).delAllUserSession(new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delTableCardList("storeGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockWebsocketUtils).clear("tableGuid");
        verify(mockUserMemberSessionUtils).expireUserSession(new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delCardList("storeGuid", "openId");
        verify(mockWxStoreTradeOrderService).removeFastGuestCount("diningTableGuid", "openId");
        verify(mockWxStoreTradeOrderService).transformSurchargeCache("53f8a09f-43d3-493f-be67-e8b275497007");
        verify(mockWxStorePayClientService).sendWeixinNotifyMessage(
                new WxMemberTradeNotifyReqDTO("enterpriseGuid", "openId", "storeGuid", "storeName",
                        new BigDecimal("0.00"), "orderGuid", "memberInfoCardGuid", "brandGuid"));
    }

    @Test
    public void testMemberPay_RedisUtilsHKeyListReturnsNoItems() {
        // Setup
        final DealMemberPayReqDTO dealMemberPayReqDTO = new DealMemberPayReqDTO("orderGuid", "memberPassWord", 0, 0, 0,
                false, new BigDecimal("0.00"), false, "memberInfoCardGuid");
        final MemberPayReusltRespDTO expectedResult = new MemberPayReusltRespDTO(0, "errorMsg");

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        when(mockWxClientService.getMerchantOrderGuid("orderGuid")).thenReturn("result");

        // Configure TradeClientService.calculate(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setStoreName("storeName");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setMemberGuid("memberGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        dineInItemDTO.setSkuName("skuName");
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        dineinOrderDetailRespDTO.setMemberCardGuid("memberInfoCardGuid");
        dineinOrderDetailRespDTO.setVersion(0);
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setDeviceType(0);
        billCalculateReqDTO.setDeviceId("deviceId");
        billCalculateReqDTO.setEnterpriseGuid("enterpriseGuid");
        billCalculateReqDTO.setStoreGuid("storeGuid");
        billCalculateReqDTO.setStoreName("storeName");
        billCalculateReqDTO.setUserGuid("createGuid");
        billCalculateReqDTO.setUserName("createName");
        billCalculateReqDTO.setOrderGuid("merchantOrderGuid");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setMemberIntegral(0);
        billCalculateReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO.setMemberPhone("memberPhone");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVerify(0);
        when(mockTradeClientService.calculate(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure TradeClientService.pay(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final BillPayReqDTO billPayReqDTO = new BillPayReqDTO();
        billPayReqDTO.setDeviceType(0);
        billPayReqDTO.setDeviceId("deviceId");
        billPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        billPayReqDTO.setStoreGuid("storeGuid");
        billPayReqDTO.setStoreName("storeName");
        billPayReqDTO.setUserGuid("createGuid");
        billPayReqDTO.setUserName("createName");
        billPayReqDTO.setOrderGuid("53f8a09f-43d3-493f-be67-e8b275497007");
        billPayReqDTO.setOrderFee(new BigDecimal("0.00"));
        billPayReqDTO.setAppendFee(new BigDecimal("0.00"));
        billPayReqDTO.setActuallyPayFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setChangeFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        billPayReqDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final BillPayReqDTO.Payment payment = new BillPayReqDTO.Payment();
        payment.setAmount(new BigDecimal("0.00"));
        payment.setPaymentType(0);
        payment.setPaymentTypeName("会员卡支付");
        billPayReqDTO.setPayments(Arrays.asList(payment));
        billPayReqDTO.setFastFood(false);
        billPayReqDTO.setMemberPassWord("memberPassWord");
        billPayReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billPayReqDTO.setUseIntegral(0);
        billPayReqDTO.setIntegralDiscountMoney(new BigDecimal("0.00"));
        billPayReqDTO.setVersion(0);
        when(mockTradeClientService.pay(billPayReqDTO)).thenReturn(estimateItemRespDTO);

        // Configure WxClientService.getItemListByOrderGuid(...).
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("85980212-72ab-4cfe-bfc8-9a4fcdac6a40");
        dineInItemDTO1.setOrderGuid("orderGuid");
        dineInItemDTO1.setItemName("itemName");
        dineInItemDTO1.setSkuName("skuName");
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        final List<DineInItemDTO> dineInItemDTOS = Arrays.asList(dineInItemDTO1);
        when(mockWxClientService.getItemListByOrderGuid("53f8a09f-43d3-493f-be67-e8b275497007"))
                .thenReturn(dineInItemDTOS);

        when(mockRedisUtils.hKeyList("key")).thenReturn(Collections.emptySet());

        // Run the test
        final MemberPayReusltRespDTO result = payServiceImplUnderTest.memberPay(dealMemberPayReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockUserMemberSessionUtils).addUserMemberSession(
                new UserMemberSessionDTO("storeGuid", "openId", "userGuid", false, "nickName", "memberInfoGuid",
                        "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false, "volumeCode", false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid"));
        verify(mockRedisUtils).delete("key");
        verify(mockWxClientService).payBackOrder(
                new PayBackOrderRecordDTO("46197127-c9f2-429b-a853-a5ce0acdb4fb", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        verify(mockRedisUtils).set("key", "memberInfoCardGuid");
        verify(mockUserMemberSessionUtils).delAllUserSession(new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delTableCardList("storeGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockWebsocketUtils).clear("tableGuid");
        verify(mockWxStorePayClientService).sendWeixinNotifyMessage(
                new WxMemberTradeNotifyReqDTO("enterpriseGuid", "openId", "storeGuid", "storeName",
                        new BigDecimal("0.00"), "orderGuid", "memberInfoCardGuid", "brandGuid"));
    }

    @Test
    public void testWeChatPay() {
        // Setup
        final WeChatH5PayReqDTO weChatH5PayReqDTO = new WeChatH5PayReqDTO("orderGuid", "outNotifyUrl", 0,
                new BigDecimal("0.00"), false, "appId", "clientIp", false,"","");
        final KbzPayStartDTO kbzPayStartDTO = new KbzPayStartDTO();
        kbzPayStartDTO.setPrePayId("prePayId");
        kbzPayStartDTO.setOrderInfo("orderInfo");
        kbzPayStartDTO.setSign("sign");
        final WxPayRespDTO expectedResult = new WxPayRespDTO("payUrl", 0, "errorMsg",
                new AggPayRespDTO("code", "msg", "result", "payGuid", "orderGuid", "paymentAppId"), kbzPayStartDTO);
        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure WxClientService.weChatPay(...).
        final KbzPayStartDTO kbzPayStartDTO1 = new KbzPayStartDTO();
        kbzPayStartDTO1.setPrePayId("prePayId");
        kbzPayStartDTO1.setOrderInfo("orderInfo");
        kbzPayStartDTO1.setSign("sign");
        final WxPayRespDTO wxPayRespDTO = new WxPayRespDTO("payUrl", 0, "errorMsg",
                new AggPayRespDTO("code", "msg", "result", "payGuid", "orderGuid", "paymentAppId"), kbzPayStartDTO1);
        when(mockWxClientService.aggPay(
                new WeChatH5PayReqDTO("orderGuid", "outNotifyUrl", 0, new BigDecimal("0.00"), false, "appId",
                        "clientIp", false, "",""))).thenReturn(wxPayRespDTO);

        // Run the test
        final WxPayRespDTO result = payServiceImplUnderTest.aggPay(weChatH5PayReqDTO, null);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockStringRedisTemplate).expire("key", 5L, TimeUnit.SECONDS);
        verify(mockStringRedisTemplate).delete("key");
    }

    @Test
    public void testWeChatPay_WxClientServiceReturnsFailure() {
        // Setup
        final WeChatH5PayReqDTO weChatH5PayReqDTO = new WeChatH5PayReqDTO("orderGuid", "outNotifyUrl", 0,
                new BigDecimal("0.00"), false, "appId", "clientIp", false,"","");
        final KbzPayStartDTO kbzPayStartDTO = new KbzPayStartDTO();
        kbzPayStartDTO.setPrePayId("prePayId");
        kbzPayStartDTO.setOrderInfo("orderInfo");
        kbzPayStartDTO.setSign("sign");
        final WxPayRespDTO expectedResult = new WxPayRespDTO("payUrl", 0, "errorMsg",
                new AggPayRespDTO("code", "msg", "result", "payGuid", "orderGuid", "paymentAppId"), kbzPayStartDTO);
        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure WxClientService.weChatPay(...).
        final WxPayRespDTO wxPayRespDTO = WxPayRespDTO.changeFailed();
        when(mockWxClientService.aggPay(
                new WeChatH5PayReqDTO("orderGuid", "outNotifyUrl", 0, new BigDecimal("0.00"), false, "appId",
                        "clientIp", false,"",""))).thenReturn(wxPayRespDTO);

        // Run the test
        final WxPayRespDTO result = payServiceImplUnderTest.aggPay(weChatH5PayReqDTO, null);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockStringRedisTemplate).expire("key", 5L, TimeUnit.SECONDS);
        verify(mockStringRedisTemplate).delete("key");
    }

    @Test
    public void testAppletWeChatPay() {
        // Setup
        final WeChatH5PayReqDTO weChatPayReqDTO = new WeChatH5PayReqDTO("orderGuid", "outNotifyUrl", 0,
                new BigDecimal("0.00"), false, "appId", "clientIp", false,"","");
        final MockHttpServletRequest request = new MockHttpServletRequest();
        final KbzPayStartDTO kbzPayStartDTO = new KbzPayStartDTO();
        kbzPayStartDTO.setPrePayId("prePayId");
        kbzPayStartDTO.setOrderInfo("orderInfo");
        kbzPayStartDTO.setSign("sign");
        final WxPayRespDTO expectedResult = new WxPayRespDTO("payUrl", 0, "errorMsg",
                new AggPayRespDTO("code", "msg", "result", "payGuid", "orderGuid", "paymentAppId"), kbzPayStartDTO);
        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure WxClientService.weChatPay(...).
        final KbzPayStartDTO kbzPayStartDTO1 = new KbzPayStartDTO();
        kbzPayStartDTO1.setPrePayId("prePayId");
        kbzPayStartDTO1.setOrderInfo("orderInfo");
        kbzPayStartDTO1.setSign("sign");
        final WxPayRespDTO wxPayRespDTO = new WxPayRespDTO("payUrl", 0, "errorMsg",
                new AggPayRespDTO("code", "msg", "result", "payGuid", "orderGuid", "paymentAppId"), kbzPayStartDTO1);
        when(mockWxClientService.aggPay(
                new WeChatH5PayReqDTO("orderGuid", "outNotifyUrl", 0, new BigDecimal("0.00"), false, "appId",
                        "clientIp", false,"",""))).thenReturn(wxPayRespDTO);

        // Run the test
        final WxPayRespDTO result = payServiceImplUnderTest.aggPay(weChatPayReqDTO, request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockStringRedisTemplate).expire("key", 5L, TimeUnit.SECONDS);
        verify(mockStringRedisTemplate).delete("key");
    }

    @Test
    public void testAppletWeChatPay_WxClientServiceReturnsFailure() {
        // Setup
        final WeChatH5PayReqDTO weChatPayReqDTO = new WeChatH5PayReqDTO("orderGuid", "outNotifyUrl", 0,
                new BigDecimal("0.00"), false, "appId", "clientIp", false,"","");
        final MockHttpServletRequest request = new MockHttpServletRequest();
        final KbzPayStartDTO kbzPayStartDTO = new KbzPayStartDTO();
        kbzPayStartDTO.setPrePayId("prePayId");
        kbzPayStartDTO.setOrderInfo("orderInfo");
        kbzPayStartDTO.setSign("sign");
        final WxPayRespDTO expectedResult = new WxPayRespDTO("payUrl", 0, "errorMsg",
                new AggPayRespDTO("code", "msg", "result", "payGuid", "orderGuid", "paymentAppId"), kbzPayStartDTO);
        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure WxClientService.weChatPay(...).
        final WxPayRespDTO wxPayRespDTO = WxPayRespDTO.changeFailed();
        when(mockWxClientService.aggPay(
                new WeChatH5PayReqDTO("orderGuid", "outNotifyUrl", 0, new BigDecimal("0.00"), false, "appId",
                        "clientIp", false,"",""))).thenReturn(wxPayRespDTO);

        // Run the test
        final WxPayRespDTO result = payServiceImplUnderTest.aggPay(weChatPayReqDTO, request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockStringRedisTemplate).expire("key", 5L, TimeUnit.SECONDS);
        verify(mockStringRedisTemplate).delete("key");
    }

    @Test
    public void testAppletCancelPay() {
        // Setup
        final WeChatCancelPayReqDTO weChatCancelPayReqDTO = new WeChatCancelPayReqDTO();
        weChatCancelPayReqDTO.setOrderGuid("orderGuid");
        weChatCancelPayReqDTO.setReason("reason");
        weChatCancelPayReqDTO.setFastFood(false);
        weChatCancelPayReqDTO.setEnterpriseGuid("enterpriseGuid");

        when(mockWxClientService.getMerchantOrderGuid("orderGuid")).thenReturn("result");

        // Configure TradeOrderService.findByOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO("42d636ff-cb38-450f-a7b8-ceafb2db865c",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "orderNo", 0, 0, 0,
                LocalDate.of(2020, 1, 1), "diningTableGuid", "diningTableName", 0, "cancelReason", "remark", "mark", 0,
                new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"), new BigDecimal("0.00"), 0, 0, 0, "mainOrderGuid", "memberGuid",
                "memberCardGuid", "memberConsumptionGuid", "memberPhone", "memberName", "userWxPublicOpenId", "qrcode",
                0L, 0L, 0, "recoveryReason", 0, 0, 0, "storeGuid", "storeName", "", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createStaffGuid",
                "createStaffName", "recoveryStaffGuid", "recoveryStaffName", "checkoutStaffGuid", "checkoutStaffName",
                "cancelStaffGuid", "cancelStaffName", 0, "localUploadId", "paymentAppId");
        when(mockTradeOrderService.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        // Configure DineInOrderClientService.cancelOrder(...).
        final CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setDeviceType(0);
        cancelOrderReqDTO.setDeviceId("deviceId");
        cancelOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        cancelOrderReqDTO.setStoreGuid("storeGuid");
        cancelOrderReqDTO.setStoreName("storeName");
        cancelOrderReqDTO.setUserGuid("createGuid");
        cancelOrderReqDTO.setUserName("createName");
        cancelOrderReqDTO.setOrderGuid("orderGuid");
        cancelOrderReqDTO.setReason("reason");
        cancelOrderReqDTO.setFastFood(false);
        when(mockDineInOrderClientService.cancelOrder(cancelOrderReqDTO)).thenReturn(false);

        // Run the test
        payServiceImplUnderTest.appletCancelPay(weChatCancelPayReqDTO);

        // Verify the results
    }

    @Test
    public void testAppletCancelPay_WxClientServiceReturnsNull() {
        // Setup
        final WeChatCancelPayReqDTO weChatCancelPayReqDTO = new WeChatCancelPayReqDTO();
        weChatCancelPayReqDTO.setOrderGuid("orderGuid");
        weChatCancelPayReqDTO.setReason("reason");
        weChatCancelPayReqDTO.setFastFood(false);
        weChatCancelPayReqDTO.setEnterpriseGuid("enterpriseGuid");

        when(mockWxClientService.getMerchantOrderGuid("orderGuid")).thenReturn(null);

        // Configure TradeOrderService.findByOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO("42d636ff-cb38-450f-a7b8-ceafb2db865c",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "orderNo", 0, 0, 0,
                LocalDate.of(2020, 1, 1), "diningTableGuid", "diningTableName", 0, "cancelReason", "remark", "mark", 0,
                new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"), new BigDecimal("0.00"), 0, 0, 0, "mainOrderGuid", "memberGuid",
                "memberCardGuid", "memberConsumptionGuid", "memberPhone", "memberName", "userWxPublicOpenId", "qrcode",
                0L, 0L, 0, "recoveryReason", 0, 0, 0, "storeGuid", "storeName", "", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createStaffGuid",
                "createStaffName", "recoveryStaffGuid", "recoveryStaffName", "checkoutStaffGuid", "checkoutStaffName",
                "cancelStaffGuid", "cancelStaffName", 0, "localUploadId", "paymentAppId");
        when(mockTradeOrderService.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        // Configure DineInOrderClientService.cancelOrder(...).
        final CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setDeviceType(0);
        cancelOrderReqDTO.setDeviceId("deviceId");
        cancelOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        cancelOrderReqDTO.setStoreGuid("storeGuid");
        cancelOrderReqDTO.setStoreName("storeName");
        cancelOrderReqDTO.setUserGuid("createGuid");
        cancelOrderReqDTO.setUserName("createName");
        cancelOrderReqDTO.setOrderGuid("orderGuid");
        cancelOrderReqDTO.setReason("reason");
        cancelOrderReqDTO.setFastFood(false);
        when(mockDineInOrderClientService.cancelOrder(cancelOrderReqDTO)).thenReturn(false);

        // Run the test
        payServiceImplUnderTest.appletCancelPay(weChatCancelPayReqDTO);

        // Verify the results
    }
}
