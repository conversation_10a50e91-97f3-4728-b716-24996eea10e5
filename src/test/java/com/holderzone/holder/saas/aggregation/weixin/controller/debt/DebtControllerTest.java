package com.holderzone.holder.saas.aggregation.weixin.controller.debt;

import com.holderzone.holder.saas.aggregation.weixin.service.rpc.DebtClientService;
import com.holderzone.saas.store.dto.trade.DebtRecordH5RespDTO;
import com.holderzone.saas.store.dto.trade.DebtUnitLoginH5ReqDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(DebtController.class)
public class DebtControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private DebtClientService mockDebtUnitService;

    @Test
    public void testQueryDebtRecordH5() throws Exception {
        // Setup
        // Configure DebtClientService.queryDebtRecordH5(...).
        final DebtRecordH5RespDTO debtRecordH5RespDTO = new DebtRecordH5RespDTO();
        debtRecordH5RespDTO.setName("name");
        debtRecordH5RespDTO.setCode("code");
        debtRecordH5RespDTO.setCreditLimitLeft(new BigDecimal("0.00"));
        debtRecordH5RespDTO.setCreditLimit(new BigDecimal("0.00"));
        debtRecordH5RespDTO.setDebtAmount(new BigDecimal("0.00"));
        when(mockDebtUnitService.queryDebtRecordH5(any(DebtUnitLoginH5ReqDTO.class))).thenReturn(debtRecordH5RespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/debt/record/h5query")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
