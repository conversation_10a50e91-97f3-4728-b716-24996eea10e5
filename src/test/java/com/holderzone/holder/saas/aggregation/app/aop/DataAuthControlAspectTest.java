package com.holderzone.holder.saas.aggregation.app.aop;

import com.holderzone.holder.saas.aggregation.app.service.feign.staff.UserClientService;
import com.holderzone.saas.store.dto.user.MenuSourceDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DataAuthControlAspectTest {

    @Mock
    private UserClientService mockUserClientService;

    @InjectMocks
    private DataAuthControlAspect dataAuthControlAspectUnderTest;

    @Test
    public void testAfterReturn() throws Exception {
        // Setup
        // Configure UserClientService.getSourceByUser(...).
        final MenuSourceDTO menuSourceDTO = new MenuSourceDTO();
        menuSourceDTO.setModuleName("moduleName");
        menuSourceDTO.setSourceGuid("sourceGuid");
        menuSourceDTO.setSourceName("sourceName");
        menuSourceDTO.setSourceCode("sourceCode");
        menuSourceDTO.setSourceUrl("sourceUrl");
        final List<MenuSourceDTO> menuSourceDTOS = Arrays.asList(menuSourceDTO);
        when(mockUserClientService.getSourceByUser("terminalCode")).thenReturn(menuSourceDTOS);

        // Run the test
        dataAuthControlAspectUnderTest.afterReturn("result");

        // Verify the results
    }

    @Test
    public void testAfterReturn_UserClientServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockUserClientService.getSourceByUser("terminalCode")).thenReturn(Collections.emptyList());

        // Run the test
        dataAuthControlAspectUnderTest.afterReturn("result");

        // Verify the results
    }
}
