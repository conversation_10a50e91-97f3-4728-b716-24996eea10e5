package com.holderzone.holder.saas.aggregation.weixin;

import com.holderzone.holder.dingding.handler.DingWarningHandler;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Optional;

//import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(MockitoJUnitRunner.class)
public class WxTest {

	@Resource
	private RedisTemplate<String, Object> redisTemplate;

	@Autowired
	private DingWarningHandler dingDingMsgSendUtils;

	public void test(){

	}

	public static void main(String[] args) {
		test2(4);
	}

	public static void test2(Integer num) {
//		Integer integer = Optional.ofNullable(num).orElseGet(()-> 3);
//		Integer integer2 = Optional.ofNullable(num).orElse(3);	//orElse在null不为空时，也会默认创建3，只是不使用而已，所以不建议使用
//		Integer integer1 = Optional.ofNullable(num).orElseThrow(RuntimeException::new);		//改成拋出其它异常

		//过滤:value必须大于3，否则还是转成空的optional，相当于null
//		Integer i3 = Optional.ofNullable(num).filter(x -> x > 3).orElseGet(() -> -1);

		//转换：map
		Integer i5 = Optional.ofNullable(num).map(x -> x + 3).orElseGet(() -> -1);
//		Optional.ofNullable(num).flatMap(x->Optional.of(num+1))

	}
}
