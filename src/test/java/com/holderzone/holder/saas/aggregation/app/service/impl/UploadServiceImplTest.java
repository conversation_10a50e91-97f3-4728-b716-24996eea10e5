package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.framework.base.dto.file.FileDto;
import com.holderzone.holder.saas.aggregation.app.service.feign.BaseFeignService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UploadServiceImplTest {

    @Mock
    private BaseFeignService mockBaseService;

    private UploadServiceImpl uploadServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        uploadServiceImplUnderTest = new UploadServiceImpl(mockBaseService);
    }

    @Test
    public void testUpload() {
        // Setup
        when(mockBaseService.upload(any(FileDto.class))).thenReturn("");

        // Run the test
        final String result = uploadServiceImplUnderTest.upload("picture");

        // Verify the results
        assertThat(result).isEqualTo("");
    }
}
