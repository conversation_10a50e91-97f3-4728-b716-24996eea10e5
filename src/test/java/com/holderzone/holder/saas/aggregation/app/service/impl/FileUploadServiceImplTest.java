package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.framework.base.dto.file.FileDto;
import com.holderzone.holder.saas.aggregation.app.entity.FileUploadDTO;
import com.holderzone.holder.saas.aggregation.app.service.feign.BaseFeignService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class FileUploadServiceImplTest {

    @Mock
    private BaseFeignService mockBaseService;

    private FileUploadServiceImpl fileUploadServiceImplUnderTest;

    @Before
    public void setUp() {
        fileUploadServiceImplUnderTest = new FileUploadServiceImpl(mockBaseService);
    }

    @Test
    public void testGetRandomFileName() {
        assertThat(FileUploadServiceImpl.getRandomFileName()).isEqualTo("result");
    }

    @Test
    public void testUpload() {
        // Setup
        final FileUploadDTO fileUploadDTO = new FileUploadDTO();
        fileUploadDTO.setHeight(0);
        fileUploadDTO.setWeight(0);
        fileUploadDTO.setSize(0L);

        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        when(mockBaseService.upload(any(FileDto.class))).thenReturn("result");

        // Run the test
        final String result = fileUploadServiceImplUnderTest.upload(fileUploadDTO, file);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testUpload_BaseFeignServiceReturnsNull() {
        // Setup
        final FileUploadDTO fileUploadDTO = new FileUploadDTO();
        fileUploadDTO.setHeight(0);
        fileUploadDTO.setWeight(0);
        fileUploadDTO.setSize(0L);

        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        when(mockBaseService.upload(any(FileDto.class))).thenReturn(null);

        // Run the test
        final String result = fileUploadServiceImplUnderTest.upload(fileUploadDTO, file);

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testCompressPicForScale() {
        assertThat(fileUploadServiceImplUnderTest.compressPicForScale("content".getBytes(), 0L))
                .isEqualTo("content".getBytes());
    }
}
