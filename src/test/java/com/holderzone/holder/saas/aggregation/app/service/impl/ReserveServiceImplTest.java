package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.saas.store.dto.reserve.ReserveRecordGuidDTO;
import com.holderzone.saas.store.reserve.api.ReserveRecordApi;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordDTO;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordDetailDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ReserveServiceImplTest {

    @Mock
    private ReserveRecordApi mockReserveRecordApi;

    @InjectMocks
    private ReserveServiceImpl reserveServiceImplUnderTest;

    @Test
    public void testLaunch() {
        // Setup
        final ReserveRecordDTO reserveRecordDTO = new ReserveRecordDTO();
        reserveRecordDTO.setGuid("01adb3aa-b2d3-495e-8e43-46296f56c8ad");
        reserveRecordDTO.setStoreGuid("storeGuid");
        reserveRecordDTO.setNumber(0);
        reserveRecordDTO.setState("state");
        reserveRecordDTO.setName("name");

        final ReserveRecordDetailDTO expectedResult = new ReserveRecordDetailDTO();
        expectedResult.setGuid("01adb3aa-b2d3-495e-8e43-46296f56c8ad");
        expectedResult.setRemark("remark");
        expectedResult.setOrderNo("orderNo");
        expectedResult.setConfirmUserGuid("confirmUserGuid");
        expectedResult.setConfirmUserName("confirmUserName");

        // Configure ReserveRecordApi.launch(...).
        final ReserveRecordDetailDTO reserveRecordDetailDTO = new ReserveRecordDetailDTO();
        reserveRecordDetailDTO.setGuid("01adb3aa-b2d3-495e-8e43-46296f56c8ad");
        reserveRecordDetailDTO.setRemark("remark");
        reserveRecordDetailDTO.setOrderNo("orderNo");
        reserveRecordDetailDTO.setConfirmUserGuid("confirmUserGuid");
        reserveRecordDetailDTO.setConfirmUserName("confirmUserName");
        final ReserveRecordDTO reserveRecordDTO1 = new ReserveRecordDTO();
        reserveRecordDTO1.setGuid("01adb3aa-b2d3-495e-8e43-46296f56c8ad");
        reserveRecordDTO1.setStoreGuid("storeGuid");
        reserveRecordDTO1.setNumber(0);
        reserveRecordDTO1.setState("state");
        reserveRecordDTO1.setName("name");
        when(mockReserveRecordApi.launch(reserveRecordDTO1)).thenReturn(reserveRecordDetailDTO);

        // Configure ReserveRecordApi.pass(...).
        final ReserveRecordDetailDTO reserveRecordDetailDTO1 = new ReserveRecordDetailDTO();
        reserveRecordDetailDTO1.setGuid("01adb3aa-b2d3-495e-8e43-46296f56c8ad");
        reserveRecordDetailDTO1.setRemark("remark");
        reserveRecordDetailDTO1.setOrderNo("orderNo");
        reserveRecordDetailDTO1.setConfirmUserGuid("confirmUserGuid");
        reserveRecordDetailDTO1.setConfirmUserName("confirmUserName");
        when(mockReserveRecordApi.pass(
                new ReserveRecordGuidDTO("a5997355-a03f-4b22-8373-662179e1ad13", "reason")))
                .thenReturn(reserveRecordDetailDTO1);

        // Run the test
        final ReserveRecordDetailDTO result = reserveServiceImplUnderTest.launch(reserveRecordDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
