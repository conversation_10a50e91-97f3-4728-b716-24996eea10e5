package com.holderzone.holder.saas.aggregation.weixin.entity;

import org.junit.Before;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class ErrorResultTest {

    private ErrorResult errorResultUnderTest;

    @Before
    public void setUp() throws Exception {
        errorResultUnderTest = new ErrorResult("timestamp", "path", 0, "error", "message");
    }

    @Test
    public void testTimestampGetterAndSetter() {
        final String timestamp = "timestamp";
        errorResultUnderTest.setTimestamp(timestamp);
        assertThat(errorResultUnderTest.getTimestamp()).isEqualTo(timestamp);
    }

    @Test
    public void testPathGetterAndSetter() {
        final String path = "path";
        errorResultUnderTest.setPath(path);
        assertThat(errorResultUnderTest.getPath()).isEqualTo(path);
    }

    @Test
    public void testStatusGetterAndSetter() {
        final Integer status = 0;
        errorResultUnderTest.setStatus(status);
        assertThat(errorResultUnderTest.getStatus()).isEqualTo(status);
    }

    @Test
    public void testErrorGetterAndSetter() {
        final String error = "error";
        errorResultUnderTest.setError(error);
        assertThat(errorResultUnderTest.getError()).isEqualTo(error);
    }

    @Test
    public void testMessageGetterAndSetter() {
        final String message = "message";
        errorResultUnderTest.setMessage(message);
        assertThat(errorResultUnderTest.getMessage()).isEqualTo(message);
    }

    @Test
    public void testEquals() throws Exception {
        assertThat(errorResultUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() throws Exception {
        assertThat(errorResultUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() throws Exception {
        assertThat(errorResultUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() throws Exception {
        assertThat(errorResultUnderTest.toString()).isEqualTo("result");
    }
}
