package com.holderzone.holder.saas.aggregation.weixin.entity;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.websocket.Session;

import static org.assertj.core.api.Assertions.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class ExtSessionTest {

    @Mock
    private Session mockSession;

    private ExtSession extSessionUnderTest;

    @Before
    public void setUp() throws Exception {
        extSessionUnderTest = new ExtSession(mockSession, 0L, (short) 0, "tableGuid", "openID");
    }

    @Test
    public void testRefreshTime() {
        // Setup
        // Run the test
        extSessionUnderTest.refreshTime();

        // Verify the results
    }

    @Test
    public void testGetSession() {
        // Setup
        // Run the test
        final Session result = extSessionUnderTest.getSession();

        // Verify the results
    }

    @Test
    public void testCheckAndClose() {
        assertThat(extSessionUnderTest.checkAndClose()).isFalse();
    }

    @Test
    public void testLastTimeGetterAndSetter() {
        final long lastTime = 0L;
        extSessionUnderTest.setLastTime(lastTime);
        assertThat(extSessionUnderTest.getLastTime()).isEqualTo(lastTime);
    }

    @Test
    public void testResetTimeGetterAndSetter() {
        final short resetTime = (short) 0;
        extSessionUnderTest.setResetTime(resetTime);
        assertThat(extSessionUnderTest.getResetTime()).isEqualTo(resetTime);
    }

    @Test
    public void testTableGuidGetterAndSetter() {
        final String tableGuid = "tableGuid";
        extSessionUnderTest.setTableGuid(tableGuid);
        assertThat(extSessionUnderTest.getTableGuid()).isEqualTo(tableGuid);
    }

    @Test
    public void testOpenIDGetterAndSetter() {
        final String openID = "openID";
        extSessionUnderTest.setOpenID(openID);
        assertThat(extSessionUnderTest.getOpenID()).isEqualTo(openID);
    }

    @Test
    public void testSetSession() {
        extSessionUnderTest.setSession(null);
    }

    @Test
    public void testEquals() throws Exception {
        assertThat(extSessionUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() throws Exception {
        assertThat(extSessionUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() throws Exception {
        assertThat(extSessionUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() throws Exception {
        assertThat(extSessionUnderTest.toString()).isEqualTo("result");
    }

    @Test
    public void testBuilder() {
        // Setup
        // Run the test
        final ExtSession.ExtSessionBuilder result = ExtSession.builder();

        // Verify the results
    }
}
