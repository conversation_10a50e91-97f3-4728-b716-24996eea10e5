package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import org.junit.Before;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class PayOnlineDTOTest {

    private PayOnlineDTO payOnlineDTOUnderTest;

    @Before
    public void setUp() throws Exception {
        payOnlineDTOUnderTest = new PayOnlineDTO(false);
    }

    @Test
    public void testNoArgsConstructor() {
        PayOnlineDTO dto = new PayOnlineDTO();
        dto.setIsOnlinePayed(false);
        assertThat(payOnlineDTOUnderTest.getIsOnlinePayed()).isFalse();
    }

    @Test
    public void testIsOnlinePayedGetterAndSetter() {
        final Boolean isOnlinePayed = false;
        payOnlineDTOUnderTest.setIsOnlinePayed(isOnlinePayed);
        assertThat(payOnlineDTOUnderTest.getIsOnlinePayed()).isFalse();
    }

    @Test
    public void testEquals() {
        assertThat(payOnlineDTOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() {
        assertThat(payOnlineDTOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() {
        assertThat(payOnlineDTOUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() {
        assertThat(payOnlineDTOUnderTest.toString()).isEqualTo("result");
    }
}
