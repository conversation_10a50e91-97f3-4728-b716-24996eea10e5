package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxStoreMerchantOrderClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestQueryMemberInfo;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfo;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOperationDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxOperateReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreMerchantOrderRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(WxStoreMerchantOrderController.class)
public class WxStoreMerchantOrderControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStoreMerchantOrderClientService mockWxStoreMerchantOrderClientService;
    @MockBean
    private HsaBaseClientService mockHsaBaseClientService;

    @Test
    public void testGetWxStoreMerchantOrderResp() throws Exception {
        // Setup
        // Configure WxStoreMerchantOrderClientService.getWxStoreMerchantOrderResp(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("39a2a503-3d09-4034-aac0-5e9b3d5a72b2");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setPhoneNum("phoneNum");
        final WxStoreMerchantOrderRespDTO wxStoreMerchantOrderRespDTO = new WxStoreMerchantOrderRespDTO(
                Arrays.asList(wxStoreMerchantOrderDTO));
        final WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO = new WxStoreMerchantOrderReqDTO();
        wxStoreMerchantOrderReqDTO.setGuid("c171e772-3f0b-4bcc-819d-b99d4aa76afd");
        wxStoreMerchantOrderReqDTO.setOrderState(0);
        wxStoreMerchantOrderReqDTO.setTradeMode(0);
        wxStoreMerchantOrderReqDTO.setTableCode("tableCode");
        wxStoreMerchantOrderReqDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockWxStoreMerchantOrderClientService.getWxStoreMerchantOrderResp(wxStoreMerchantOrderReqDTO))
                .thenReturn(wxStoreMerchantOrderRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_merchant_order/get_pend")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetDetailPend() throws Exception {
        // Setup
        // Configure WxStoreMerchantOrderClientService.getDetailPend(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("39a2a503-3d09-4034-aac0-5e9b3d5a72b2");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setPhoneNum("phoneNum");
        final WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO = new WxStoreMerchantOrderReqDTO();
        wxStoreMerchantOrderReqDTO.setGuid("c171e772-3f0b-4bcc-819d-b99d4aa76afd");
        wxStoreMerchantOrderReqDTO.setOrderState(0);
        wxStoreMerchantOrderReqDTO.setTradeMode(0);
        wxStoreMerchantOrderReqDTO.setTableCode("tableCode");
        wxStoreMerchantOrderReqDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockWxStoreMerchantOrderClientService.getDetailPend(wxStoreMerchantOrderReqDTO))
                .thenReturn(wxStoreMerchantOrderDTO);

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setMemberInfoGuid("memberInfoGuid");
        responseMemberInfo.setOperSubjectGuid("operSubjectGuid");
        responseMemberInfo.setEnterpriseGuid("enterpriseGuid");
        responseMemberInfo.setPhoneNum("phoneNum");
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_merchant_order/get_detail_pend")
                        .param("guid", "c171e772-3f0b-4bcc-819d-b99d4aa76afd")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetDetailPend_WxStoreMerchantOrderClientServiceReturnsNull() throws Exception {
        // Setup
        // Configure WxStoreMerchantOrderClientService.getDetailPend(...).
        final WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO = new WxStoreMerchantOrderReqDTO();
        wxStoreMerchantOrderReqDTO.setGuid("c171e772-3f0b-4bcc-819d-b99d4aa76afd");
        wxStoreMerchantOrderReqDTO.setOrderState(0);
        wxStoreMerchantOrderReqDTO.setTradeMode(0);
        wxStoreMerchantOrderReqDTO.setTableCode("tableCode");
        wxStoreMerchantOrderReqDTO.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockWxStoreMerchantOrderClientService.getDetailPend(wxStoreMerchantOrderReqDTO)).thenReturn(null);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_merchant_order/get_detail_pend")
                        .param("guid", "c171e772-3f0b-4bcc-819d-b99d4aa76afd")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("");
    }

    @Test
    public void testOperationMerchantOrder() throws Exception {
        // Setup
        // Configure WxStoreMerchantOrderClientService.operationMerchantOrder(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setEstimateSkuGuids(Arrays.asList("value"));
        final WxStoreMerchantOperationDTO wxStoreMerchantOperationDTO = new WxStoreMerchantOperationDTO(
                "enterpriseGuid", "storeGuid", "diningTableGuid", "tableCode", "areaName", "storeName", "brandName",
                estimateItemRespDTO, "errorMsg");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("a66665f9-f937-4008-adbb-6910153ebb6f");
        dineInItemDTO.setOrderGuid("orderGuid");
        dineInItemDTO.setOriginalOrderItemGuid(0L);
        dineInItemDTO.setAdjustNumber(new BigDecimal("0.00"));
        dineInItemDTO.setRollbackCount(new BigDecimal("0.00"));
        final WxOperateReqDTO wxOperateReqDTO = new WxOperateReqDTO("8d4e5bd2-69d1-4c78-885b-c8e0ba73f9fc", 0,
                "denialReason", "tableGuid", "tableName", 0, "areaName", "remark", Arrays.asList(dineInItemDTO));
        when(mockWxStoreMerchantOrderClientService.operationMerchantOrder(wxOperateReqDTO))
                .thenReturn(wxStoreMerchantOperationDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_merchant_order/operate")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testOperationMerchantOrder_WxStoreMerchantOrderClientServiceReturnsError() throws Exception {
        // Setup
        // Configure WxStoreMerchantOrderClientService.operationMerchantOrder(...).
        final WxStoreMerchantOperationDTO wxStoreMerchantOperationDTO = WxStoreMerchantOperationDTO.tableError();
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("a66665f9-f937-4008-adbb-6910153ebb6f");
        dineInItemDTO.setOrderGuid("orderGuid");
        dineInItemDTO.setOriginalOrderItemGuid(0L);
        dineInItemDTO.setAdjustNumber(new BigDecimal("0.00"));
        dineInItemDTO.setRollbackCount(new BigDecimal("0.00"));
        final WxOperateReqDTO wxOperateReqDTO = new WxOperateReqDTO("8d4e5bd2-69d1-4c78-885b-c8e0ba73f9fc", 0,
                "denialReason", "tableGuid", "tableName", 0, "areaName", "remark", Arrays.asList(dineInItemDTO));
        when(mockWxStoreMerchantOrderClientService.operationMerchantOrder(wxOperateReqDTO))
                .thenReturn(wxStoreMerchantOperationDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store_merchant_order/operate")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
