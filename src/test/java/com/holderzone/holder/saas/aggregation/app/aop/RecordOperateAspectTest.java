package com.holderzone.holder.saas.aggregation.app.aop;

import com.holderzone.holder.saas.aggregation.app.service.feign.trade.OperateRecordClientService;
import com.holderzone.saas.store.dto.trade.req.record.MemberOperateRecordReqDTO;
import org.aspectj.lang.ProceedingJoinPoint;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class RecordOperateAspectTest {

    @Mock
    private OperateRecordClientService mockOperateRecordClientService;

    @InjectMocks
    private RecordOperateAspect recordOperateAspectUnderTest;

    @Test
    public void testMemberOperateRecord() {
        recordOperateAspectUnderTest.memberOperateRecord();
    }

    @Test
    public void testMemberOperateRecordAround() {
        // Setup
        final ProceedingJoinPoint point = null;

        // Run the test
        final Object result = recordOperateAspectUnderTest.memberOperateRecordAround(point);

        // Verify the results
        // Confirm OperateRecordClientService.saveRecord(...).
        final MemberOperateRecordReqDTO reqDTO = new MemberOperateRecordReqDTO();
        reqDTO.setDeviceType(0);
        reqDTO.setLoginType(0);
        reqDTO.setModuleType(0);
        reqDTO.setTradeMode(0);
        reqDTO.setOperatorGuid("operatorGuid");
        reqDTO.setPhoneNum("phoneNum");
        reqDTO.setOperatorName("operatorName");
        reqDTO.setOrderGuid("orderGuid");
        reqDTO.setStoreGuid("storeGuid");
        reqDTO.setStoreName("storeName");
        verify(mockOperateRecordClientService).saveRecord(reqDTO);
    }
}
