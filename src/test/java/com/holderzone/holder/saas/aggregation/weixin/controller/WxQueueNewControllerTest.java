package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxQueueClientService;
import com.holderzone.saas.store.dto.queue.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(WxQueueNewController.class)
public class WxQueueNewControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxQueueClientService mockWxQueueClientService;

    @Test
    public void testQueryByGuid() throws Exception {
        // Setup
        // Configure WxQueueClientService.queryByGuid(...).
        final List<WxQueueListDTO> wxQueueListDTOS = Arrays.asList(
                new WxQueueListDTO("15621572-0212-4155-aeea-aa4f2948cc22", "userGuid", "storeGuid", "storeName",
                        "brandName", "brandGuid", "weekDate", LocalDate.of(2020, 1, 1), "time", (byte) 0b0,
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        when(mockWxQueueClientService.queryByGuid(
                new QueueWechatDTO("brandGuid", "userGuid", (byte) 0b0, "enterpriseGuid"))).thenReturn(wxQueueListDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_queue/query_by_guid")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryByGuid_WxQueueClientServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockWxQueueClientService.queryByGuid(
                new QueueWechatDTO("brandGuid", "userGuid", (byte) 0b0, "enterpriseGuid")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_queue/query_by_guid")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testGetQueueDetail() throws Exception {
        // Setup
        // Configure WxQueueClientService.getQueueDetail(...).
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("b8bb7a72-5a2b-409d-8838-74c183d20d02");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setLogoUrl("logoUrl");
        holderQueueDTO.setIsEnableRecovery(false);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setQueueCode("queueCode");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setCode("code");
        holderQueueItemDetailDTO.setStatus((byte) 0b0);
        final HolderQueueQueueRecordDTO holderQueueQueueRecordDTO = new HolderQueueQueueRecordDTO(holderQueueDTO,
                holderQueueItemDetailDTO);
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setItemGuid("itemGuid");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");
        when(mockWxQueueClientService.getQueueDetail(dto)).thenReturn(holderQueueQueueRecordDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_queue/query_detail")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetQueueDetail_WxQueueClientServiceReturnsNull() throws Exception {
        // Setup
        // Configure WxQueueClientService.getQueueDetail(...).
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setItemGuid("itemGuid");
        dto.setBrandGuid("brandGuid");
        dto.setEnterpriseGuid("enterpriseGuid");
        when(mockWxQueueClientService.getQueueDetail(dto)).thenReturn(null);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_queue/query_detail")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("");
    }

    @Test
    public void testCancelQueue() throws Exception {
        // Setup
        when(mockWxQueueClientService.cancelQueue("queueGuid", "enterpriseGuid")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/wx_queue/update_cancel_queue")
                        .param("queueGuid", "queueGuid")
                        .param("enterpriseGuid", "enterpriseGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
