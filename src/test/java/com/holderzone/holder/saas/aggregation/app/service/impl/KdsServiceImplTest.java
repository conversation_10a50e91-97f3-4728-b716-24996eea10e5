package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.config.ZhuanCanConfig;
import com.holderzone.holder.saas.aggregation.app.service.feign.MessageClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.WeiXinClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.business.StoreConfigClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.kds.KitchenItemRpcService;
import com.holderzone.holder.saas.aggregation.app.service.feign.takeout.TakeoutClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.OrderItemClientService;
import com.holderzone.saas.store.bo.FoodFinishBarCodeBO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigQueryDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.config.resp.FinishFoodRespDTO;
import com.holderzone.saas.store.dto.kds.req.ItemStateTransReqDTO;
import com.holderzone.saas.store.dto.kds.req.PrdDstItemQueryDTO;
import com.holderzone.saas.store.dto.kds.req.ScanFinishFoodReqDTO;
import com.holderzone.saas.store.dto.kds.resp.DistributeItemDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemTableDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderDTO;
import com.holderzone.saas.store.dto.weixin.open.WxOrderItemReqDTO;
import com.holderzone.saas.store.dto.weixin.open.WxOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.open.WxSendMessageReqDTO;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class KdsServiceImplTest {

    public static final String ORDER_NO = "7685091202403240010";
    public static final String ORDER_GUID = "7177558618479263744";
    public static final String DEVICE_ID = "2307311438538210000";
    public static final String STORE_GUID = "4897";
    public static final String USER_NAME = "赵亮";
    public static final String STORE_NAME = "门店1";
    public static final String ENTERPRISE_GUID = "4895";
    public static final String OPER_SUBJECT_GUID = "2209191214425540006";
    public static final String STORE_GUID1 = "4934";
    public static final String ENTERPRISE_NAME = "销售报表测试企业1";
    public static final String USER_GUID = "8647";
    public static final String NUMBER = "7177558695276969984";
    public static final String FOOD_FINISH_BAR_CODE_7177558695276969984 = "foodFinishBarCode:7177558695276969984";
    public static final String ORDER_NO1 = "orderNo";
    public static final String STORE_GUID2 = "storeGuid";
    public static final String SKU_GUID = "697755325720795545601";
    public static final String ITEM_GUID = "6977553257207955456";
    public static final String SKU_GUID1 = "697755325720376115301";
    public static final String ITEM_GUID1 = "6977553257203761153";
    public static final String ACCOUNT = "174825";
    public static final String ORDER_NO2 = "3800999013829713040";
    public static final String AREA_GUID = "snack_area_guid";
    @Mock
    private MessageClientService mockMsgClientService;
    @Mock
    private OrderItemClientService mockOrderItemClientService;
    @Mock
    private KitchenItemRpcService mockKitchenItemRpcService;
    @Mock
    private StoreConfigClientService mockStoreConfigClientService;
    @Mock
    private TakeoutClientService mockTakeoutClientService;
    @Mock
    private RedisTemplate<String, String> mockStringRedisTemplate;

    @Mock
    private WeiXinClientService mockWeiXinClientService;

    @Mock
    private ZhuanCanConfig mockZhuanCanConfig;

    private KdsServiceImpl kdsServiceImplUnderTest;

    @Before
    public void setUp() {
        UserContext userContext = new UserContext();
        userContext.setOperSubjectGuid(OPER_SUBJECT_GUID);
        userContext.setEnterpriseGuid(ENTERPRISE_GUID);
        userContext.setStoreGuid(STORE_GUID);
        userContext.setStoreName(STORE_NAME);
        userContext.setUserGuid("2043");
        userContext.setUserName(USER_NAME);
        UserContextUtils.put(userContext);

        kdsServiceImplUnderTest = new KdsServiceImpl(mockMsgClientService, mockOrderItemClientService,
                mockKitchenItemRpcService, mockStoreConfigClientService, mockTakeoutClientService,
                mockWeiXinClientService, mockZhuanCanConfig, mockStringRedisTemplate);
    }

    @Test
    public void testScanFinishFood_FastFood() {
        // Setup
        final ScanFinishFoodReqDTO request = getScanFinishFoodReqDTO(NUMBER);

        ValueOperations<String, String> valueOperationsMock = Mockito.mock(ValueOperations.class);
        Mockito.when(mockStringRedisTemplate.opsForValue()).thenReturn(valueOperationsMock);
        FoodFinishBarCodeBO barCodeBO = new FoodFinishBarCodeBO();
        barCodeBO.setOrderNo(ORDER_NO);
        List<String> orderItemGuidList = getOrderItemGuidList();
        barCodeBO.setOrderItemGuidList(orderItemGuidList);
        Mockito.when(valueOperationsMock.get(FOOD_FINISH_BAR_CODE_7177558695276969984)).thenReturn(JacksonUtils.writeValueAsString(barCodeBO));

        // Configure OrderItemClientService.findByOrderNoAndStoreGuid(...).
        final OrderDTO orderDTO = getOrderDTO();
        when(mockOrderItemClientService.findByOrderNoAndStoreGuid(ORDER_NO, STORE_GUID)).thenReturn(orderDTO);

        // Configure TakeoutClientService.getOrderByOrderNo(...).
        final TakeoutOrderDTO takeoutOrderDTO = getTakeoutOrderDTO();
        when(mockTakeoutClientService.getOrderByOrderNo(ORDER_NO1, STORE_GUID2)).thenReturn(takeoutOrderDTO);

        // Configure KitchenItemRpcService.queryByOrder(...).
        final List<PrdDstItemDTO> prdDstItemDTOList = getPrdDstItemDTOList();
        final PrdDstItemQueryDTO query = new PrdDstItemQueryDTO();
        query.setOrderItemGuidList(orderItemGuidList);
        when(mockKitchenItemRpcService.queryByOrder(query)).thenReturn(prdDstItemDTOList);

        // Configure KitchenItemRpcService.queryDistributeItemBySku(...).
        final List<DistributeItemDTO> itemDTOList = getDistributeItemDTOS(ITEM_GUID, SKU_GUID);
        when(mockKitchenItemRpcService.queryDistributeItemBySku(SingleDataDTO.builder()
                .data(STORE_GUID)
                .datas(Arrays.asList(SKU_GUID1, SKU_GUID))
                .build())).thenReturn(itemDTOList);

        // Configure StoreConfigClientService.queryFinishFood(...).
        final FinishFoodRespDTO respDTO = new FinishFoodRespDTO();
        respDTO.setPrepTime(1);
        respDTO.setFinishFoodVoiceSwitch(1);
        when(mockStoreConfigClientService.queryFinishFood(new StoreConfigQueryDTO(STORE_GUID, null))).thenReturn(respDTO);

        // Run the test
        kdsServiceImplUnderTest.scanFinishFood(request);

        // Verify the results
        // Confirm KitchenItemRpcService.batchDistribute(...).
        final ItemStateTransReqDTO reqDTO = getItemStateTransReqDTO(prdDstItemDTOList);
        final List<ItemStateTransReqDTO> reqDTOList = Collections.singletonList(reqDTO);
        verify(mockKitchenItemRpcService).batchDistribute(reqDTOList);
        verifyResult();
    }

    @NotNull
    private static List<DistributeItemDTO> getDistributeItemDTOS(String itemGuid, String skuGuid) {
        final DistributeItemDTO itemDTO1 = new DistributeItemDTO();
        itemDTO1.setStoreGuid(STORE_GUID1);
        itemDTO1.setDeviceId(DEVICE_ID);
        itemDTO1.setItemGuid(itemGuid);
        itemDTO1.setSkuGuid(skuGuid);

        final DistributeItemDTO itemDTO2 = new DistributeItemDTO();
        itemDTO2.setStoreGuid(STORE_GUID1);
        itemDTO2.setDeviceId(DEVICE_ID);
        itemDTO2.setItemGuid(ITEM_GUID1);
        itemDTO2.setSkuGuid(SKU_GUID1);
        final List<DistributeItemDTO> itemDTOList = new ArrayList<>();
        itemDTOList.add(itemDTO1);
        itemDTOList.add(itemDTO2);
        return itemDTOList;
    }

    @NotNull
    private static List<String> getOrderItemGuidList() {
        List<String> orderItemGuidList = new ArrayList<>();
        orderItemGuidList.add("7177558618718339072");
        orderItemGuidList.add("7177558618714144769");
        orderItemGuidList.add("7177558618714144768");
        return orderItemGuidList;
    }

    @NotNull
    private static ScanFinishFoodReqDTO getScanFinishFoodReqDTO(String number) {
        final ScanFinishFoodReqDTO request = new ScanFinishFoodReqDTO();
        request.setDeviceType(3);
        request.setDeviceId("2307211032202040005");
        request.setEnterpriseGuid(ENTERPRISE_GUID);
        request.setEnterpriseName(ENTERPRISE_NAME);
        request.setStoreGuid(STORE_GUID);
        request.setStoreName(STORE_NAME);
        request.setUserGuid(USER_GUID);
        request.setUserName(USER_NAME);
        request.setAccount(ACCOUNT);
        request.setRequestTimestamp(1711262204141L);
        request.setOrderNo(number);
        return request;
    }

    @NotNull
    private ItemStateTransReqDTO getItemStateTransReqDTO(List<PrdDstItemDTO> prdDstItemDTOList) {
        final ItemStateTransReqDTO reqDTO = new ItemStateTransReqDTO();
        reqDTO.setDeviceType(9);
        reqDTO.setDeviceId(DEVICE_ID);
        reqDTO.setEnterpriseGuid(ENTERPRISE_GUID);
        reqDTO.setEnterpriseName(ENTERPRISE_NAME);
        reqDTO.setStoreGuid(STORE_GUID);
        reqDTO.setStoreName(STORE_NAME);
        reqDTO.setUserGuid(USER_GUID);
        reqDTO.setUserName(USER_NAME);
        reqDTO.setAccount(ACCOUNT);
        reqDTO.setRequestTimestamp(1711334007622L);
        reqDTO.setOrderDifference(2);
        reqDTO.setOrderDesc("#009");
        reqDTO.setPrdDstItemList(prdDstItemDTOList);
        return reqDTO;
    }

    @Test
    public void testScanFinishFood_Takeout() {
        // Setup
        final ScanFinishFoodReqDTO request_Takeout = getScanFinishFoodReqDTO("7177608960701104128");

        ValueOperations<String, String> valueOperationsMock = Mockito.mock(ValueOperations.class);
        Mockito.when(mockStringRedisTemplate.opsForValue()).thenReturn(valueOperationsMock);
        FoodFinishBarCodeBO barCodeBO_Takeout = new FoodFinishBarCodeBO();
        barCodeBO_Takeout.setOrderNo(ORDER_NO2);
        List<String> orderItemGuidList_Takeout = new ArrayList<>();
        orderItemGuidList_Takeout.add("7177608952354439168,697755325720795545801");
        orderItemGuidList_Takeout.add("7177608952354439168,697755325720376115301");
        barCodeBO_Takeout.setOrderItemGuidList(orderItemGuidList_Takeout);
        Mockito.when(valueOperationsMock.get("foodFinishBarCode:7177608960701104128")).thenReturn(JacksonUtils.writeValueAsString(barCodeBO_Takeout));

        // Configure OrderItemClientService.findByOrderNoAndStoreGuid(...).
        when(mockOrderItemClientService.findByOrderNoAndStoreGuid(ORDER_NO2, STORE_GUID)).thenReturn(null);

        // Configure TakeoutClientService.getOrderByOrderNo(...).
        final TakeoutOrderDTO takeoutOrderDTO_Takeout = new TakeoutOrderDTO();
        takeoutOrderDTO_Takeout.setOrderGuid("7177608952354439168");
        takeoutOrderDTO_Takeout.setOrderViewId(ORDER_NO2);
        takeoutOrderDTO_Takeout.setOrderDaySn("#6");
        when(mockTakeoutClientService.getOrderByOrderNo(ORDER_NO2, STORE_GUID)).thenReturn(takeoutOrderDTO_Takeout);

        // Configure KitchenItemRpcService.queryByOrder(...).
        final List<PrdDstItemDTO> prdDstItemDTOList = getPrdDstItemDTOList();
        final PrdDstItemQueryDTO query_Takeout = new PrdDstItemQueryDTO();
        query_Takeout.setOrderItemGuidList(orderItemGuidList_Takeout);
        when(mockKitchenItemRpcService.queryByOrder(query_Takeout)).thenReturn(prdDstItemDTOList);

        // Configure KitchenItemRpcService.queryDistributeItemBySku(...).
        final List<DistributeItemDTO> itemDTOList_Takeout = getDistributeItemDTOS("6977553257207955458", "697755325720795545801");
        when(mockKitchenItemRpcService.queryDistributeItemBySku(SingleDataDTO.builder()
                .data(STORE_GUID)
                .datas(Arrays.asList("697755325720795545801", SKU_GUID1))
                .build())).thenReturn(itemDTOList_Takeout);

        // Configure StoreConfigClientService.queryFinishFood(...).
        final FinishFoodRespDTO respDTO_Takeout = new FinishFoodRespDTO();
        respDTO_Takeout.setPrepTime(1);
        respDTO_Takeout.setFinishFoodVoiceSwitch(1);
        when(mockStoreConfigClientService.queryFinishFood(new StoreConfigQueryDTO(STORE_GUID,"123"))).thenReturn(respDTO_Takeout);

        // Run the test
        kdsServiceImplUnderTest.scanFinishFood(request_Takeout);

        // Verify the results
        // Confirm KitchenItemRpcService.batchDistribute(...).
        final ItemStateTransReqDTO reqDTO_Takeout = new ItemStateTransReqDTO();
        reqDTO_Takeout.setDeviceType(9);
        reqDTO_Takeout.setDeviceId(DEVICE_ID);
        reqDTO_Takeout.setEnterpriseGuid(ENTERPRISE_GUID);
        reqDTO_Takeout.setEnterpriseName(ENTERPRISE_NAME);
        reqDTO_Takeout.setStoreGuid(STORE_GUID);
        reqDTO_Takeout.setStoreName(STORE_NAME);
        reqDTO_Takeout.setUserGuid(USER_GUID);
        reqDTO_Takeout.setUserName(USER_NAME);
        reqDTO_Takeout.setAccount(ACCOUNT);
        reqDTO_Takeout.setRequestTimestamp(1711334007622L);
        reqDTO_Takeout.setOrderDifference(2);
        reqDTO_Takeout.setOrderDesc("#6");
        reqDTO_Takeout.setPrdDstItemList(prdDstItemDTOList);
        final List<ItemStateTransReqDTO> reqDTOList = Collections.singletonList(reqDTO_Takeout);
        verify(mockKitchenItemRpcService).batchDistribute(reqDTOList);
        verifyResult();
    }

    @Test
    public void testScanFinishFood_FinishFoodCodeInvalid() {
        // Setup
        final ScanFinishFoodReqDTO request_FinishFoodCodeInvalid = getScanFinishFoodReqDTO("7177553857948418048");

        ValueOperations<String, String> valueOperationsMock_FinishFoodCodeInvalid = Mockito.mock(ValueOperations.class);
        Mockito.when(mockStringRedisTemplate.opsForValue()).thenReturn(valueOperationsMock_FinishFoodCodeInvalid);
        Mockito.when(valueOperationsMock_FinishFoodCodeInvalid.get("foodFinishBarCode:7177553857948418048"))
                .thenReturn(null);

        // Configure OrderItemClientService.findByOrderNoAndStoreGuid(...).
        when(mockOrderItemClientService.findByOrderNoAndStoreGuid(ORDER_NO, STORE_GUID)).thenReturn(null);

        // Configure TakeoutClientService.getOrderByOrderNo(...).
        when(mockTakeoutClientService.getOrderByOrderNo(ORDER_NO, STORE_GUID)).thenReturn(null);

        // Configure KitchenItemRpcService.queryByOrder(...).
        final PrdDstItemQueryDTO query = new PrdDstItemQueryDTO();
        query.setOrderGuid("orderGuid");
        query.setOrderItemGuidList(Collections.singletonList("value"));
        when(mockKitchenItemRpcService.queryByOrder(query)).thenReturn(Collections.emptyList());

        // Configure StoreConfigClientService.queryFinishFood(...).
        final FinishFoodRespDTO respDTO = new FinishFoodRespDTO();
        respDTO.setPrepTime(1);
        respDTO.setFinishFoodVoiceSwitch(1);
        when(mockStoreConfigClientService.queryFinishFood(new StoreConfigQueryDTO(STORE_GUID,"124"))).thenReturn(respDTO);

        // Run the test
        kdsServiceImplUnderTest.scanFinishFood(request_FinishFoodCodeInvalid);

        // Verify the results
        verifyResult();

    }

    @NotNull
    private List<PrdDstItemDTO> getPrdDstItemDTOList() {
        final PrdDstItemDTO prdDstItemDTO1 = getPrdDstItemDTO1();
        final PrdDstItemDTO prdDstItemDTO2 = getPrdDstItemDTO2();

        final List<PrdDstItemDTO> prdDstItemDTOList = new ArrayList<>();
        prdDstItemDTOList.add(prdDstItemDTO1);
        prdDstItemDTOList.add(prdDstItemDTO2);
        return prdDstItemDTOList;
    }

    @NotNull
    private PrdDstItemDTO getPrdDstItemDTO2() {
        final PrdDstItemDTO prdDstItemDTO2 = new PrdDstItemDTO();
        prdDstItemDTO2.setItemGuid(ITEM_GUID);
        prdDstItemDTO2.setItemName("白水菜汤");
        prdDstItemDTO2.setSkuGuid(SKU_GUID);
        prdDstItemDTO2.setSkuName("");
        prdDstItemDTO2.setIsWeight(false);
        prdDstItemDTO2.setCurrentCount(new BigDecimal("1"));
        prdDstItemDTO2.setSkuUnit("份");
        prdDstItemDTO2.setItemAttrMd5("1d2ed64428d16149b6811995aab02937");
        prdDstItemDTO2.setWaitTime(0);
        prdDstItemDTO2.setTimeout(0);
        prdDstItemDTO2.setIsCooking(false);
        prdDstItemDTO2.setUrgedItemNumber(0);
        prdDstItemDTO2.setHangedItemNumber(0);
        prdDstItemDTO2.setAreaGuid(AREA_GUID);
        prdDstItemDTO2.setIsUrged(false);
        prdDstItemDTO2.setIsHanged(false);
        prdDstItemDTO2.setBatch(999);

        List<PrdDstItemTableDTO> kitchenItemList2 = getKitchenItemList2();
        prdDstItemDTO2.setKitchenItemList(kitchenItemList2);
        return prdDstItemDTO2;
    }

    @NotNull
    private List<PrdDstItemTableDTO> getKitchenItemList2() {
        List<PrdDstItemTableDTO> kitchenItemList2 = new ArrayList<>();
        PrdDstItemTableDTO prdDstItemTableDTO2 = new PrdDstItemTableDTO();
        prdDstItemTableDTO2.setOrderGuid(ORDER_GUID);
        prdDstItemTableDTO2.setOrderDesc("快餐");
        prdDstItemTableDTO2.setOrderNumber(ORDER_NO);
        prdDstItemTableDTO2.setOrderSerialNo("#009");
        prdDstItemTableDTO2.setKitchenItemGuid("7177545545924214785");
        prdDstItemTableDTO2.setAreaGuid(AREA_GUID);
        prdDstItemTableDTO2.setIsUrged(false);
        prdDstItemTableDTO2.setIsHanged(false);
        kitchenItemList2.add(prdDstItemTableDTO2);
        return kitchenItemList2;
    }

    @NotNull
    private PrdDstItemDTO getPrdDstItemDTO1() {
        final PrdDstItemDTO prdDstItemDTO1 = new PrdDstItemDTO();
        prdDstItemDTO1.setItemGuid(ITEM_GUID1);
        prdDstItemDTO1.setItemName("盐白菜汤");
        prdDstItemDTO1.setSkuGuid(SKU_GUID1);
        prdDstItemDTO1.setSkuName("");
        prdDstItemDTO1.setIsWeight(false);
        prdDstItemDTO1.setCurrentCount(new BigDecimal("1"));
        prdDstItemDTO1.setSkuUnit("份");
        prdDstItemDTO1.setItemAttrMd5("8994756d2a263933d612ea9724d976c1");
        prdDstItemDTO1.setWaitTime(0);
        prdDstItemDTO1.setTimeout(0);
        prdDstItemDTO1.setIsCooking(false);
        prdDstItemDTO1.setUrgedItemNumber(0);
        prdDstItemDTO1.setHangedItemNumber(0);
        prdDstItemDTO1.setAreaGuid(AREA_GUID);
        prdDstItemDTO1.setIsUrged(false);
        prdDstItemDTO1.setIsHanged(false);
        prdDstItemDTO1.setBatch(999);

        List<PrdDstItemTableDTO> kitchenItemList1 = getKitchenItemList1();
        prdDstItemDTO1.setKitchenItemList(kitchenItemList1);
        return prdDstItemDTO1;
    }

    @NotNull
    private List<PrdDstItemTableDTO> getKitchenItemList1() {
        List<PrdDstItemTableDTO> kitchenItemList1 = new ArrayList<>();
        PrdDstItemTableDTO prdDstItemTableDTO1 = new PrdDstItemTableDTO();
        prdDstItemTableDTO1.setOrderGuid(ORDER_GUID);
        prdDstItemTableDTO1.setOrderDesc("快餐");
        prdDstItemTableDTO1.setOrderNumber(ORDER_NO);
        prdDstItemTableDTO1.setOrderSerialNo("#009");
        prdDstItemTableDTO1.setKitchenItemGuid("7177545545924214784");
        prdDstItemTableDTO1.setAreaGuid(AREA_GUID);
        prdDstItemTableDTO1.setIsUrged(false);
        prdDstItemTableDTO1.setIsHanged(false);
        kitchenItemList1.add(prdDstItemTableDTO1);
        return kitchenItemList1;
    }

    @Test
    public void testScanFinishFood_QueryByOrderReturnsNoItems() {
        // Setup
        final ScanFinishFoodReqDTO request_QueryByOrderReturnsNoItems = getScanFinishFoodReqDTO(NUMBER);

        ValueOperations<String, String> valueOperationsMock = Mockito.mock(ValueOperations.class);
        Mockito.when(mockStringRedisTemplate.opsForValue()).thenReturn(valueOperationsMock);
        FoodFinishBarCodeBO barCodeBO_QueryByOrderReturnsNoItems = new FoodFinishBarCodeBO();
        barCodeBO_QueryByOrderReturnsNoItems.setOrderNo(ORDER_NO);
        List<String> orderItemGuidList_QueryByOrderReturnsNoItems = getOrderItemGuidList();
        barCodeBO_QueryByOrderReturnsNoItems.setOrderItemGuidList(orderItemGuidList_QueryByOrderReturnsNoItems);
        Mockito.when(valueOperationsMock.get(FOOD_FINISH_BAR_CODE_7177558695276969984))
                .thenReturn(JacksonUtils.writeValueAsString(barCodeBO_QueryByOrderReturnsNoItems));

        // Configure OrderItemClientService.findByOrderNoAndStoreGuid(...).
        final OrderDTO orderDTO_QueryByOrderReturnsNoItems = getOrderDTO();
        when(mockOrderItemClientService.findByOrderNoAndStoreGuid(ORDER_NO, STORE_GUID))
                .thenReturn(orderDTO_QueryByOrderReturnsNoItems);

        // Configure TakeoutClientService.getOrderByOrderNo(...).
        final TakeoutOrderDTO takeoutOrderDTO_QueryByOrderReturnsNoItems = getTakeoutOrderDTO();
        when(mockTakeoutClientService.getOrderByOrderNo(ORDER_NO1, STORE_GUID2))
                .thenReturn(takeoutOrderDTO_QueryByOrderReturnsNoItems);

        // Configure KitchenItemRpcService.queryByOrder(...).
        final PrdDstItemQueryDTO query_QueryByOrderReturnsNoItems = new PrdDstItemQueryDTO();
        query_QueryByOrderReturnsNoItems.setOrderItemGuidList(orderItemGuidList_QueryByOrderReturnsNoItems);
        when(mockKitchenItemRpcService.queryByOrder(query_QueryByOrderReturnsNoItems)).thenReturn(Collections.emptyList());

        // Configure StoreConfigClientService.queryFinishFood(...).
        final FinishFoodRespDTO respDTO_QueryByOrderReturnsNoItems = new FinishFoodRespDTO();
        respDTO_QueryByOrderReturnsNoItems.setPrepTime(0);
        respDTO_QueryByOrderReturnsNoItems.setFinishFoodVoiceSwitch(0);
        when(mockStoreConfigClientService.queryFinishFood(new StoreConfigQueryDTO(STORE_GUID2,"124")))
                .thenReturn(respDTO_QueryByOrderReturnsNoItems);

        // Run the test
        kdsServiceImplUnderTest.scanFinishFood(request_QueryByOrderReturnsNoItems);

        // Verify the results
        verifyResult();
    }

    @Test
    public void testScanFinishFood_QueryDistributeItemBySkuReturnsNoItems() {
        // Setup
        final ScanFinishFoodReqDTO request_QueryDistributeItemBySkuReturnsNoItems = getScanFinishFoodReqDTO(NUMBER);

        ValueOperations<String, String> valueOperationsMock = Mockito.mock(ValueOperations.class);
        Mockito.when(mockStringRedisTemplate.opsForValue()).thenReturn(valueOperationsMock);
        FoodFinishBarCodeBO barCodeBO_QueryDistributeItemBySkuReturnsNoItems = new FoodFinishBarCodeBO();
        barCodeBO_QueryDistributeItemBySkuReturnsNoItems.setOrderNo(ORDER_NO);
        List<String> orderItemGuidList_QueryDistributeItemBySkuReturnsNoItems = getOrderItemGuidList();
        barCodeBO_QueryDistributeItemBySkuReturnsNoItems.setOrderItemGuidList(orderItemGuidList_QueryDistributeItemBySkuReturnsNoItems);
        Mockito.when(valueOperationsMock.get(FOOD_FINISH_BAR_CODE_7177558695276969984))
                .thenReturn(JacksonUtils.writeValueAsString(barCodeBO_QueryDistributeItemBySkuReturnsNoItems));

        // Configure OrderItemClientService.findByOrderNoAndStoreGuid(...).
        final OrderDTO orderDTO_QueryDistributeItemBySkuReturnsNoItems = getOrderDTO();
        when(mockOrderItemClientService.findByOrderNoAndStoreGuid(ORDER_NO, STORE_GUID))
                .thenReturn(orderDTO_QueryDistributeItemBySkuReturnsNoItems);

        // Configure TakeoutClientService.getOrderByOrderNo(...).
        final TakeoutOrderDTO takeoutOrderDTO_QueryDistributeItemBySkuReturnsNoItems = getTakeoutOrderDTO();
        when(mockTakeoutClientService.getOrderByOrderNo(ORDER_NO1, STORE_GUID2))
                .thenReturn(takeoutOrderDTO_QueryDistributeItemBySkuReturnsNoItems);

        // Configure KitchenItemRpcService.queryByOrder(...).
        final List<PrdDstItemDTO> prdDstItemDTOList = getPrdDstItemDTOList();
        final PrdDstItemQueryDTO query_QueryDistributeItemBySkuReturnsNoItems = new PrdDstItemQueryDTO();
        query_QueryDistributeItemBySkuReturnsNoItems.setOrderItemGuidList(orderItemGuidList_QueryDistributeItemBySkuReturnsNoItems);
        when(mockKitchenItemRpcService.queryByOrder(query_QueryDistributeItemBySkuReturnsNoItems)).thenReturn(prdDstItemDTOList);

        when(mockKitchenItemRpcService.queryDistributeItemBySku(SingleDataDTO.builder()
                .data(STORE_GUID)
                .datas(Arrays.asList(SKU_GUID1, SKU_GUID))
                .build())).thenReturn(Collections.emptyList());

        // Configure StoreConfigClientService.queryFinishFood(...).
        final FinishFoodRespDTO respDTO_QueryDistributeItemBySkuReturnsNoItems = new FinishFoodRespDTO();
        respDTO_QueryDistributeItemBySkuReturnsNoItems.setPrepTime(0);
        respDTO_QueryDistributeItemBySkuReturnsNoItems.setFinishFoodVoiceSwitch(0);
        when(mockStoreConfigClientService.queryFinishFood(new StoreConfigQueryDTO(STORE_GUID2,"125")))
                .thenReturn(respDTO_QueryDistributeItemBySkuReturnsNoItems);

        // Run the test
        kdsServiceImplUnderTest.scanFinishFood(request_QueryDistributeItemBySkuReturnsNoItems);

        // Verify the results
        verifyResult();
    }

    @NotNull
    private static TakeoutOrderDTO getTakeoutOrderDTO() {
        final TakeoutOrderDTO takeoutOrderDTO_QueryDistributeItemBySkuReturnsNoItems = new TakeoutOrderDTO();
        takeoutOrderDTO_QueryDistributeItemBySkuReturnsNoItems.setDeviceType(0);
        takeoutOrderDTO_QueryDistributeItemBySkuReturnsNoItems.setDeviceId("deviceId");
        takeoutOrderDTO_QueryDistributeItemBySkuReturnsNoItems.setUserGuid("userGuid");
        takeoutOrderDTO_QueryDistributeItemBySkuReturnsNoItems.setUserName("userName");
        takeoutOrderDTO_QueryDistributeItemBySkuReturnsNoItems.setAccount("account");
        takeoutOrderDTO_QueryDistributeItemBySkuReturnsNoItems.setRequestTimestamp(0L);
        takeoutOrderDTO_QueryDistributeItemBySkuReturnsNoItems.setOrderDaySn("mark");
        return takeoutOrderDTO_QueryDistributeItemBySkuReturnsNoItems;
    }

    private void verifyResult() {
        verify(mockMsgClientService).msg(BusinessMessageDTO.builder()
                .subject("subject")
                .content("deviceId")
                .messageType(0)
                .detailMessageType(0)
                .platform("platform")
                .storeGuid(STORE_GUID2)
                .storeName("storeName")
                .build());
    }

    private static OrderDTO getOrderDTO() {
        final OrderDTO orderDTO_QueryDistributeItemBySkuReturnsNoItems = OrderDTO.builder()
                .guid(ORDER_GUID)
                .orderNo(ORDER_NO)
                .tradeMode(1)
                .mark("#009")
                .build();
        return orderDTO_QueryDistributeItemBySkuReturnsNoItems;
    }

    @Test
    public void testSendCallMessage() {
        // Setup
        final OrderDTO orderDTO = OrderDTO.builder()
                .guid("guid")
                .tradeMode(1)
                .mark("orderDesc")
                .build();

        // Configure WeiXinClientService.getMerchantOrderPhone(...).
        final WxStoreMerchantOrderDTO merchantOrderDTO = new WxStoreMerchantOrderDTO();
        merchantOrderDTO.setGuid("5ccfc648-c36c-4ea9-9eee-31c9499d8d19");
        merchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        merchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        merchantOrderDTO.setOrderGuid("orderGuid");
        merchantOrderDTO.setOrderRecordGuid("orderRecordGuid");
        when(mockWeiXinClientService.getMerchantOrderPhone("guid")).thenReturn(merchantOrderDTO);

        when(mockZhuanCanConfig.getSendCallMessage()).thenReturn("result");

        // Run the test
        kdsServiceImplUnderTest.sendCallMessage(orderDTO);

        // Verify the results
    }

    @Test
    public void testDistributeBatchSendCallMessage() {
        // Setup
        final PrdDstItemDTO prdDstItemDTO = new PrdDstItemDTO();
        prdDstItemDTO.setItemGuid("itemGuid");
        prdDstItemDTO.setItemName("itemName");
        prdDstItemDTO.setSkuGuid("skuGuid");
        prdDstItemDTO.setSkuName("skuName");
        final PrdDstItemTableDTO prdDstItemTableDTO = new PrdDstItemTableDTO();
        prdDstItemTableDTO.setOrderGuid("orderGuid");
        prdDstItemDTO.setKitchenItemList(Arrays.asList(prdDstItemTableDTO));
        final List<PrdDstItemDTO> prdDstItemList = Arrays.asList(prdDstItemDTO);

        // Configure OrderItemClientService.listByOrderGuid(...).
        final List<OrderDTO> orderDTOList = Arrays.asList(OrderDTO.builder()
                .guid("guid")
                .tradeMode(0)
                .mark("orderDesc")
                .build());
        when(mockOrderItemClientService.listByOrderGuid(SingleDataDTO.builder()
                .data("storeGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(orderDTOList);

        // Configure WeiXinClientService.listByOrderGuid(...).
        final WxStoreMerchantOrderDTO merchantOrderDTO = new WxStoreMerchantOrderDTO();
        merchantOrderDTO.setGuid("5ccfc648-c36c-4ea9-9eee-31c9499d8d19");
        merchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        merchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        merchantOrderDTO.setOrderGuid("orderGuid");
        merchantOrderDTO.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDTO> merchantOrderDTOList = Arrays.asList(merchantOrderDTO);
        when(mockWeiXinClientService.listByOrderGuid(SingleDataDTO.builder()
                .data("storeGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(merchantOrderDTOList);

        when(mockZhuanCanConfig.getBatchSendCallMessage()).thenReturn("result");

        // Run the test
        kdsServiceImplUnderTest.distributeBatchSendCallMessage(prdDstItemList);

        // Verify the results
        // Confirm WeiXinClientService.sendCallMessage(...).
        final WxSendMessageReqDTO sendMessageReqDTO = new WxSendMessageReqDTO();
        final WxOrderReqDTO orderReqDTO = new WxOrderReqDTO();
        orderReqDTO.setOrderGuid("orderGuid");
        final WxOrderItemReqDTO wxOrderItemReqDTO = new WxOrderItemReqDTO();
        wxOrderItemReqDTO.setItemGuid("itemGuid");
        wxOrderItemReqDTO.setItemName("itemName");
        wxOrderItemReqDTO.setSkuGuid("skuGuid");
        wxOrderItemReqDTO.setSkuName("skuName");
        wxOrderItemReqDTO.setNumber(0);
        orderReqDTO.setOrderItemList(Arrays.asList(wxOrderItemReqDTO));
        sendMessageReqDTO.setOrderList(Arrays.asList(orderReqDTO));
        verify(mockWeiXinClientService).sendCallMessage(sendMessageReqDTO);
    }

    @Test
    public void testDistributeBatchSendCallMessage2() {
        // Setup
        final PrdDstItemDTO prdDstItemDTO = new PrdDstItemDTO();
        prdDstItemDTO.setItemGuid("itemGuid");
        prdDstItemDTO.setItemName("itemName");
        prdDstItemDTO.setSkuGuid("skuGuid");
        prdDstItemDTO.setSkuName("skuName");
        final PrdDstItemTableDTO prdDstItemTableDTO = new PrdDstItemTableDTO();
        prdDstItemTableDTO.setOrderGuid("orderGuid");
        prdDstItemDTO.setKitchenItemList(Arrays.asList(prdDstItemTableDTO));
        final List<PrdDstItemDTO> prdDstItemList = Arrays.asList(prdDstItemDTO);

        // Configure OrderItemClientService.listByOrderGuid(...).
        final List<OrderDTO> orderDTOList = Arrays.asList(OrderDTO.builder()
                .guid("guid")
                .tradeMode(1)
                .mark("orderDesc")
                .build());
        when(mockOrderItemClientService.listByOrderGuid(SingleDataDTO.builder()
                .datas(Arrays.asList("orderGuid"))
                .build())).thenReturn(orderDTOList);

        // Configure WeiXinClientService.listByOrderGuid(...).
        final WxStoreMerchantOrderDTO merchantOrderDTO = new WxStoreMerchantOrderDTO();
        merchantOrderDTO.setGuid("5ccfc648-c36c-4ea9-9eee-31c9499d8d19");
        merchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        merchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        merchantOrderDTO.setOrderGuid("orderGuid");
        merchantOrderDTO.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDTO> merchantOrderDTOList = Arrays.asList(merchantOrderDTO);
        SingleDataDTO dataDTO = new SingleDataDTO();
        dataDTO.setDatas(Arrays.asList("guid"));
        when(mockWeiXinClientService.listByOrderGuid(dataDTO)).thenReturn(merchantOrderDTOList);

        when(mockZhuanCanConfig.getBatchSendCallMessage()).thenReturn("result");

        // Run the test
        kdsServiceImplUnderTest.distributeBatchSendCallMessage(prdDstItemList);

        // Verify the results
        // Confirm WeiXinClientService.sendCallMessage(...).
        final WxSendMessageReqDTO sendMessageReqDTO = new WxSendMessageReqDTO();
        final WxOrderReqDTO orderReqDTO = new WxOrderReqDTO();
        orderReqDTO.setOrderGuid("orderGuid");
        final WxOrderItemReqDTO wxOrderItemReqDTO = new WxOrderItemReqDTO();
        wxOrderItemReqDTO.setItemGuid("itemGuid");
        wxOrderItemReqDTO.setItemName("itemName");
        wxOrderItemReqDTO.setSkuGuid("skuGuid");
        wxOrderItemReqDTO.setSkuName("skuName");
        wxOrderItemReqDTO.setNumber(0);
        orderReqDTO.setOrderItemList(Arrays.asList(wxOrderItemReqDTO));
        sendMessageReqDTO.setOrderList(Arrays.asList(orderReqDTO));
        verify(mockWeiXinClientService).sendCallMessage(sendMessageReqDTO);
    }


}
