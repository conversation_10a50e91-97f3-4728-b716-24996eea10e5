package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.websocket.Session;

import static org.assertj.core.api.Assertions.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class ExtSessionDTOTest {

    @Mock
    private Session mockSession;

    private ExtSessionDTO extSessionDTOUnderTest;

    @Before
    public void setUp() throws Exception {
        extSessionDTOUnderTest = new ExtSessionDTO(mockSession);
    }

    @Test
    public void testRefreshTime() {
        // Setup
        // Run the test
        extSessionDTOUnderTest.refreshTime();

        // Verify the results
    }

    @Test
    public void testCheckAndClose() {
        assertThat(extSessionDTOUnderTest.checkAndClose()).isFalse();
    }

    @Test
    public void testSessionGetterAndSetter() {
        final Session session = null;
        extSessionDTOUnderTest.setSession(session);
        assertThat(extSessionDTOUnderTest.getSession()).isEqualTo(session);
    }

    @Test
    public void testLastTimeGetterAndSetter() {
        final long lastTime = 0L;
        extSessionDTOUnderTest.setLastTime(lastTime);
        assertThat(extSessionDTOUnderTest.getLastTime()).isEqualTo(lastTime);
    }

    @Test
    public void testResetTimeGetterAndSetter() {
        final byte resetTime = (byte) 0b0;
        extSessionDTOUnderTest.setResetTime(resetTime);
        assertThat(extSessionDTOUnderTest.getResetTime()).isEqualTo(resetTime);
    }

    @Test
    public void testEquals() {
        assertThat(extSessionDTOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() {
        assertThat(extSessionDTOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() {
        assertThat(extSessionDTOUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() {
        assertThat(extSessionDTOUnderTest.toString()).isEqualTo("result");
    }
}
