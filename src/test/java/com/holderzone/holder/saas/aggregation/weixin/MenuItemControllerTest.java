package com.holderzone.holder.saas.aggregation.weixin;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.util.JsonFileUtil;
import com.holderzone.holder.saas.aggregation.weixin.util.MockHttpUtil;
import com.holderzone.holder.saas.aggregation.weixin.utils.SpringContextUtil;
import com.holderzone.saas.store.dto.order.request.dinein.CreateFastFoodReqDTO;
import com.holderzone.saas.store.dto.weixin.deal.MenuInfoAllDTO;
import com.holderzone.saas.store.dto.weixin.deal.OrderSubmitReqDTO;
import com.holderzone.saas.store.dto.weixin.deal.OrderSubmitRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.context.WebApplicationContext;

import java.io.UnsupportedEncodingException;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


/**
 * 门店配置与商品
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
@SpringBootTest(classes = HolderSaasAggregationWeixinApplication.class)
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class MenuItemControllerTest {

    public static final String USERINFO = "{\"operSubjectGuid\": \"2010121440477930009\",\"enterpriseGuid\":" +
            " \"2009281531195930006\",\"enterpriseName\": \"赵氏企业\",\"enterpriseNo\": \"********\",\"storeGuid\":" +
            " \"2106221850429620006\",\"storeName\": \"交子大道测试门店\",\"storeNo\": \"5796807\",\"userGuid\": " +
            "\"6787561298847596545\",\"account\": \"196504\",\"tel\": \"***********\",\"name\": \"靓亮仔\"}\n";

    @Autowired
    private ApplicationContext ap;

    @Autowired
    private WebApplicationContext webApplicationContext;
    private MockMvc mockMvc;

    public static final String MENU_ITEM_REQUEST_MAPPING = "/deal/menu";

    public static final String RESPONSE = "response:";

    public static final String PREFIX = "/deal/menu";

    @Before
    public void setupMockMvc() {
        SpringContextUtil.getInstance().setCfgContext((ConfigurableApplicationContext) ap);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.webApplicationContext).build();
    }

    private static final String WX_PERMISSION_FIELD = "wxPermission";

    private static final String WX_ADD_ITEM_FLAG_FIELD = "wxAddItemFlag";

    private static final String TRUE_FLAG = "1";

    private static final String FALSE_FLAG = "0";


    /**
     * 查询门店商品，分类，配置, h5扫码点餐首页请求
     */
    @Test
    public void testGetMenuInfoAll() throws UnsupportedEncodingException {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add(WX_PERMISSION_FIELD, TRUE_FLAG);
        params.add(WX_ADD_ITEM_FLAG_FIELD, FALSE_FLAG);
        MvcResult mvcResult = null;
        try {
            mvcResult = mockMvc.perform(get(PREFIX + "/item_config", params)
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = mvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
        Assert.notBlank(contentAsString, "查询门店商品及配置返回为空");
        Result result = JacksonUtils.toObject(Result.class, contentAsString);
        Assert.equals(result.getCode(), 0, "查询门店商品及配置返回失败");
        MenuInfoAllDTO menuInfoAllDTO = (MenuInfoAllDTO) result.getTData();
        Assert.notNull(menuInfoAllDTO, "查询门店商品及配置返回参数为空");
        Assert.notNull(menuInfoAllDTO.getMenuInfoConfigDTO(), "微信门店配置返回为空");
        Assert.notEmpty(menuInfoAllDTO.getCardList(), "卡列表为空");
        Assert.isTrue(menuInfoAllDTO.getIsLogin(), "会员未登录");
        Assert.notNull(menuInfoAllDTO.getSurchargeFlag(), "是否需要附加费不能为空");
    }


    /**
     * h5扫码点餐加菜页面跳转到首页
     */
    @Test
    public void testGetMenuInfoAllForAddItemBefore() {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add(WX_PERMISSION_FIELD, TRUE_FLAG);
        params.add(WX_ADD_ITEM_FLAG_FIELD, TRUE_FLAG);
        String content = MockHttpUtil.get(MENU_ITEM_REQUEST_MAPPING + "/item_config", params, mockMvc);
        log.info("查询门店商品，分类，配置返回参数:{}", content);
    }

    /**
     * 订单确认列表, 首次进入
     */
    @Test
    public void testGetPreOrderList() {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add(WX_ADD_ITEM_FLAG_FIELD, FALSE_FLAG);
        String content = MockHttpUtil.get(MENU_ITEM_REQUEST_MAPPING + "/pre_order_list", params, mockMvc);
        log.info("订单确认列表, 首次进入返回参数:{}", content);
    }


    /**
     * 订单确认列表, 加菜后进入
     */
    @Test
    public void testGetAddItemBeforePreOrderList() {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add(WX_ADD_ITEM_FLAG_FIELD, FALSE_FLAG);
        String content = MockHttpUtil.get(MENU_ITEM_REQUEST_MAPPING + "/pre_order_list", params, mockMvc);
        log.info("订单确认列表,加菜后进入返回参数:{}", content);
    }

    /**
     * 加菜时保存当前所需附加费明细
     */
    @Test
    public void testSaveAddItemBeforeSurcharges() throws UnsupportedEncodingException {
        CreateFastFoodReqDTO reqDTO = JSON.parseObject(JsonFileUtil.read("menu_item/add_item_before_surcharges.json"), CreateFastFoodReqDTO.class);
        String jsonString = JSON.toJSONString(reqDTO);
        MvcResult mvcResult = null;
        try {
            mvcResult = mockMvc.perform(post(PREFIX + "/add_item_before_surcharges")
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String addItemBeforeSurchargesContent = mvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + addItemBeforeSurchargesContent);
        Assert.notBlank(addItemBeforeSurchargesContent, "加菜保存当前所需附加费明细返回为空");
        Result addItemBeforeSurchargesResult = JacksonUtils.toObject(Result.class, addItemBeforeSurchargesContent);
        Assert.equals(addItemBeforeSurchargesResult.getCode(), 0, "加菜保存当前所需附加费明细返回失败");
    }


    /**
     * 提交订单 - 存在附加费的订单
     */
    @Test
    public void testSubmitOrder() throws UnsupportedEncodingException {
        OrderSubmitReqDTO reqDTO = JSON.parseObject(JsonFileUtil.read("menu_item/submit_order.json"), OrderSubmitReqDTO.class);
        String jsonString = JSON.toJSONString(reqDTO);
        MvcResult mvcResult = null;
        try {
            mvcResult = mockMvc.perform(post(PREFIX + "/submit_order")
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(jsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String submitOrderResultContent = mvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + submitOrderResultContent);
        Assert.notBlank(submitOrderResultContent, "提交订单返回为空");
        Result submitOrderResult = JacksonUtils.toObject(Result.class, submitOrderResultContent);
        Assert.equals(submitOrderResult.getCode(), 0, "提交订单返回失败");
        OrderSubmitRespDTO orderSubmitRespDTO = (OrderSubmitRespDTO) submitOrderResult.getTData();
        Assert.notNull(orderSubmitRespDTO, "提交订单返回参数为空");
        Assert.notBlank(orderSubmitRespDTO.getOrderRecordGuid(), "提交订单返回订单记录id为空");
        Assert.notBlank(orderSubmitRespDTO.getOrderGuid(), "提交订单返回订单guid为空");
        Assert.isFalse(orderSubmitRespDTO.getEstimate(), "提交订单返回有商品估清");
        Assert.isNull(orderSubmitRespDTO.getErrorMsg(), "提交订单返回错误信息:" + orderSubmitRespDTO.getErrorMsg());
    }
}
