package com.holderzone.holder.saas.aggregation.weixin.entity.bo;

import com.holderzone.holder.saas.member.wechat.dto.card.ResponseProductDiscount;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityDetailsVO;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;

public class DiscountRuleBOTest {

    private DiscountRuleBO discountRuleBOUnderTest;

    @Before
    public void setUp() throws Exception {
        discountRuleBOUnderTest = new DiscountRuleBO();
    }

    @Test
    public void testVolumeGuidGetterAndSetter() {
        final String volumeGuid = "volumeGuid";
        discountRuleBOUnderTest.setVolumeGuid(volumeGuid);
        assertThat(discountRuleBOUnderTest.getVolumeGuid()).isEqualTo(volumeGuid);
    }

    @Test
    public void testVolumeCodeTypeGetterAndSetter() {
        final Integer volumeCodeType = 0;
        discountRuleBOUnderTest.setVolumeCodeType(volumeCodeType);
        assertThat(discountRuleBOUnderTest.getVolumeCodeType()).isEqualTo(volumeCodeType);
    }

    @Test
    public void testVerifyGetterAndSetter() {
        final Integer verify = 0;
        discountRuleBOUnderTest.setVerify(verify);
        assertThat(discountRuleBOUnderTest.getVerify()).isEqualTo(verify);
    }

    @Test
    public void testVolumeShareFlagGetterAndSetter() {
        final Boolean volumeShareFlag = false;
        discountRuleBOUnderTest.setVolumeShareFlag(volumeShareFlag);
        assertThat(discountRuleBOUnderTest.getVolumeShareFlag()).isFalse();
    }

    @Test
    public void testActivityMemberShareFlagGetterAndSetter() {
        final Boolean activityMemberShareFlag = false;
        discountRuleBOUnderTest.setActivityMemberShareFlag(activityMemberShareFlag);
        assertThat(discountRuleBOUnderTest.getActivityMemberShareFlag()).isFalse();
    }

    @Test
    public void testHasMemberPriceGetterAndSetter() {
        final Boolean hasMemberPrice = false;
        discountRuleBOUnderTest.setHasMemberPrice(hasMemberPrice);
        assertThat(discountRuleBOUnderTest.getHasMemberPrice()).isFalse();
    }

    @Test
    public void testHasMemberDiscountGetterAndSetter() {
        final Boolean hasMemberDiscount = false;
        discountRuleBOUnderTest.setHasMemberDiscount(hasMemberDiscount);
        assertThat(discountRuleBOUnderTest.getHasMemberDiscount()).isFalse();
    }

    @Test
    public void testMemberDiscountGetterAndSetter() {
        final BigDecimal memberDiscount = new BigDecimal("0.00");
        discountRuleBOUnderTest.setMemberDiscount(memberDiscount);
        assertThat(discountRuleBOUnderTest.getMemberDiscount()).isEqualTo(memberDiscount);
    }

    @Test
    public void testFullShareFlagGetterAndSetter() {
        final Boolean fullShareFlag = false;
        discountRuleBOUnderTest.setFullShareFlag(fullShareFlag);
        assertThat(discountRuleBOUnderTest.getFullShareFlag()).isFalse();
    }

    @Test
    public void testSpecialsActivityDetailsVOGetterAndSetter() {
        final LimitSpecialsActivityDetailsVO specialsActivityDetailsVO = new LimitSpecialsActivityDetailsVO();
        discountRuleBOUnderTest.setSpecialsActivityDetailsVO(specialsActivityDetailsVO);
        assertThat(discountRuleBOUnderTest.getSpecialsActivityDetailsVO()).isEqualTo(specialsActivityDetailsVO);
    }

    @Test
    public void testResponseProductDiscountGetterAndSetter() {
        final ResponseProductDiscount responseProductDiscount = new ResponseProductDiscount();
        discountRuleBOUnderTest.setResponseProductDiscount(responseProductDiscount);
        assertThat(discountRuleBOUnderTest.getResponseProductDiscount()).isEqualTo(responseProductDiscount);
    }

    @Test
    public void testEquals() throws Exception {
        assertThat(discountRuleBOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() throws Exception {
        assertThat(discountRuleBOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() throws Exception {
        assertThat(discountRuleBOUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() throws Exception {
        assertThat(discountRuleBOUnderTest.toString()).isEqualTo("result");
    }
}
