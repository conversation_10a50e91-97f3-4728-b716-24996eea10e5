package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import org.junit.Before;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class IslandCouponDTOTest {

    private IslandCouponDTO islandCouponDTOUnderTest;

    @Before
    public void setUp() throws Exception {
        islandCouponDTOUnderTest = new IslandCouponDTO();
    }

    @Test
    public void testGuidGetterAndSetter() {
        final String guid = "guid";
        islandCouponDTOUnderTest.setGuid(guid);
        assertThat(islandCouponDTOUnderTest.getGuid()).isEqualTo(guid);
    }

    @Test
    public void testNameGetterAndSetter() {
        final String name = "name";
        islandCouponDTOUnderTest.setName(name);
        assertThat(islandCouponDTOUnderTest.getName()).isEqualTo(name);
    }

    @Test
    public void testNumGetterAndSetter() {
        final Integer num = 0;
        islandCouponDTOUnderTest.setNum(num);
        assertThat(islandCouponDTOUnderTest.getNum()).isEqualTo(num);
    }

    @Test
    public void testGiftTypeGetterAndSetter() {
        final Integer giftType = 0;
        islandCouponDTOUnderTest.setGiftType(giftType);
        assertThat(islandCouponDTOUnderTest.getGiftType()).isEqualTo(giftType);
    }

    @Test
    public void testEquals() {
        assertThat(islandCouponDTOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() {
        assertThat(islandCouponDTOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() {
        assertThat(islandCouponDTOUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() {
        assertThat(islandCouponDTOUnderTest.toString()).isEqualTo("result");
    }
}
