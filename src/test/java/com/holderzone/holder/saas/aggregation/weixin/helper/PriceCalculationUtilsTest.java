package com.holderzone.holder.saas.aggregation.weixin.helper;

import com.holderzone.holder.saas.aggregation.weixin.context.DiscountContext;
import com.holderzone.holder.saas.aggregation.weixin.entity.bo.DiscountRuleBO;
import com.holderzone.holder.saas.aggregation.weixin.service.ItemService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.HsmTerminalServiceClient;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.member.terminal.dto.volume.*;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.weixin.deal.CalculateOrderDTO;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberSessionDTO;
import com.holderzone.saas.store.dto.weixin.resp.CalculateOrderRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PriceCalculationUtilsTest {

    @Mock
    private HsmTerminalServiceClient mockTerminalServiceClient;
    @Mock
    private ItemService mockItemService;

    private PriceCalculationUtils priceCalculationUtilsUnderTest;

    @Before
    public void setUp() throws Exception {
        priceCalculationUtilsUnderTest = new PriceCalculationUtils(mockTerminalServiceClient, mockItemService);
    }

    @Test
    public void testDealWithVolume() {
        // Setup
        final DiscountContext context = new DiscountContext();
        final CalculateOrderDTO calculateOrderDTO = new CalculateOrderDTO();
        calculateOrderDTO.setVolumeCodes(Arrays.asList("value"));
        context.setCalculateOrderDTO(calculateOrderDTO);
        final CalculateOrderRespDTO calculateOrderRespDTO = new CalculateOrderRespDTO();
        calculateOrderRespDTO.setTip("tip");
        context.setCalculateOrderRespDTO(calculateOrderRespDTO);
        final DiscountRuleBO discountRuleBO = new DiscountRuleBO();
        discountRuleBO.setVerify(0);
        discountRuleBO.setHasMemberPrice(false);
        context.setDiscountRuleBO(discountRuleBO);
        final UserMemberSessionDTO userMemberSession = new UserMemberSessionDTO();
        userMemberSession.setNickName("nickName");
        userMemberSession.setMemberInfoGuid("memberInfoGuid");
        context.setUserMemberSession(userMemberSession);
        context.setUseMemberPriceFlag(false);
        context.setOrderGuid("orderGuid");
        context.setMemberConsumptionGuid("memberConsumptionGuid");

        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("guid");
        dineInItemDTO.setItemGuid("dishGuid");
        dineInItemDTO.setItemName("dishName");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setSkuGuid("dishSpecification");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setUnit("dishUnit");
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountTotalPrice(new BigDecimal("0.00"));
        final List<DineInItemDTO> allItems = Arrays.asList(dineInItemDTO);

        // Configure HsmTerminalServiceClient.calculateDiscount(...).
        final ResponseVolumeCalculate responseVolumeCalculate = new ResponseVolumeCalculate();
        final RequestDishInfo dishInfo = new RequestDishInfo();
        dishInfo.setOrderItemGuid("guid");
        dishInfo.setDishGuid("dishGuid");
        dishInfo.setDishName("dishName");
        dishInfo.setDishSpecification("dishSpecification");
        dishInfo.setDishUnit("dishUnit");
        dishInfo.setDishNum(new BigDecimal("0.00"));
        dishInfo.setGiftDishNum(new BigDecimal("0.00"));
        dishInfo.setDishSellUnitPrice(new BigDecimal("0.00"));
        dishInfo.setMainGoodGuid("mainGoodGuid");
        dishInfo.setIsMainGood(0);
        dishInfo.setSurcharge(new BigDecimal("0.00"));
        dishInfo.setDishType(0);
        dishInfo.setSubtotal(new BigDecimal("0.00"));
        dishInfo.setPayPrice(new BigDecimal("0.00"));
        dishInfo.setDiscountMoney(new BigDecimal("0.00"));
        dishInfo.setDishOriginalUnitPrice(new BigDecimal("0.00"));
        dishInfo.setDishMemberPrice(new BigDecimal("0.00"));
        responseVolumeCalculate.setDishInfoDTOList(Arrays.asList(dishInfo));
        responseVolumeCalculate.setTip("tip");
        final RequestVolumeCalculate requestVolumeCalculate = new RequestVolumeCalculate();
        requestVolumeCalculate.setStoreGuid("storeGuid");
        final RequestDishInfo dishInfo1 = new RequestDishInfo();
        dishInfo1.setOrderItemGuid("guid");
        dishInfo1.setDishGuid("dishGuid");
        dishInfo1.setDishName("dishName");
        dishInfo1.setDishSpecification("dishSpecification");
        dishInfo1.setDishUnit("dishUnit");
        dishInfo1.setDishNum(new BigDecimal("0.00"));
        dishInfo1.setGiftDishNum(new BigDecimal("0.00"));
        dishInfo1.setDishSellUnitPrice(new BigDecimal("0.00"));
        dishInfo1.setMainGoodGuid("mainGoodGuid");
        dishInfo1.setIsMainGood(0);
        dishInfo1.setSurcharge(new BigDecimal("0.00"));
        dishInfo1.setDishType(0);
        dishInfo1.setSubtotal(new BigDecimal("0.00"));
        dishInfo1.setPayPrice(new BigDecimal("0.00"));
        dishInfo1.setDiscountMoney(new BigDecimal("0.00"));
        dishInfo1.setDishOriginalUnitPrice(new BigDecimal("0.00"));
        dishInfo1.setDishMemberPrice(new BigDecimal("0.00"));
        requestVolumeCalculate.setDishInfoDTOList(Arrays.asList(dishInfo1));
        requestVolumeCalculate.setHasMemberPrice(false);
        requestVolumeCalculate.setVolumeCodeList(Arrays.asList("value"));
        when(mockTerminalServiceClient.calculateDiscount(requestVolumeCalculate)).thenReturn(responseVolumeCalculate);

        // Configure HsmTerminalServiceClient.consume(...).
        final ResponseVolumeConsume responseVolumeConsume = new ResponseVolumeConsume();
        responseVolumeConsume.setMemberConsumptionGuid("memberConsumptionGuid");
        final RequestDishInfo dishInfo2 = new RequestDishInfo();
        dishInfo2.setOrderItemGuid("guid");
        dishInfo2.setDishGuid("dishGuid");
        dishInfo2.setDishName("dishName");
        dishInfo2.setDishSpecification("dishSpecification");
        dishInfo2.setDishUnit("dishUnit");
        dishInfo2.setDishNum(new BigDecimal("0.00"));
        dishInfo2.setGiftDishNum(new BigDecimal("0.00"));
        dishInfo2.setDishSellUnitPrice(new BigDecimal("0.00"));
        dishInfo2.setMainGoodGuid("mainGoodGuid");
        dishInfo2.setIsMainGood(0);
        dishInfo2.setSurcharge(new BigDecimal("0.00"));
        dishInfo2.setDishType(0);
        dishInfo2.setSubtotal(new BigDecimal("0.00"));
        dishInfo2.setPayPrice(new BigDecimal("0.00"));
        dishInfo2.setDiscountMoney(new BigDecimal("0.00"));
        dishInfo2.setDishOriginalUnitPrice(new BigDecimal("0.00"));
        dishInfo2.setDishMemberPrice(new BigDecimal("0.00"));
        responseVolumeConsume.setRequestDishInfoList(Arrays.asList(dishInfo2));
        final RequestVolumeConsume volumeConsumeReqDTO = new RequestVolumeConsume();
        volumeConsumeReqDTO.setVolumeCodes(Arrays.asList("value"));
        volumeConsumeReqDTO.setStoreGuid("storeGuid");
        volumeConsumeReqDTO.setMemberConsumptionGuid("memberConsumptionGuid");
        volumeConsumeReqDTO.setMemberInfoGuid("memberInfoGuid");
        volumeConsumeReqDTO.setMemberName("nickName");
        volumeConsumeReqDTO.setOrderNumber("orderGuid");
        final RequestDishInfo dishInfo3 = new RequestDishInfo();
        dishInfo3.setOrderItemGuid("guid");
        dishInfo3.setDishGuid("dishGuid");
        dishInfo3.setDishName("dishName");
        dishInfo3.setDishSpecification("dishSpecification");
        dishInfo3.setDishUnit("dishUnit");
        dishInfo3.setDishNum(new BigDecimal("0.00"));
        dishInfo3.setGiftDishNum(new BigDecimal("0.00"));
        dishInfo3.setDishSellUnitPrice(new BigDecimal("0.00"));
        dishInfo3.setMainGoodGuid("mainGoodGuid");
        dishInfo3.setIsMainGood(0);
        dishInfo3.setSurcharge(new BigDecimal("0.00"));
        dishInfo3.setDishType(0);
        dishInfo3.setSubtotal(new BigDecimal("0.00"));
        dishInfo3.setPayPrice(new BigDecimal("0.00"));
        dishInfo3.setDiscountMoney(new BigDecimal("0.00"));
        dishInfo3.setDishOriginalUnitPrice(new BigDecimal("0.00"));
        dishInfo3.setDishMemberPrice(new BigDecimal("0.00"));
        volumeConsumeReqDTO.setRequestDishInfoList(Arrays.asList(dishInfo3));
        volumeConsumeReqDTO.setHasMemberPrice(false);
        when(mockTerminalServiceClient.consume(volumeConsumeReqDTO)).thenReturn(responseVolumeConsume);

        // Configure HsmTerminalServiceClient.cancel(...).
        final RequestDishInfo dishInfo4 = new RequestDishInfo();
        dishInfo4.setOrderItemGuid("guid");
        dishInfo4.setDishGuid("dishGuid");
        dishInfo4.setDishName("dishName");
        dishInfo4.setDishSpecification("dishSpecification");
        dishInfo4.setDishUnit("dishUnit");
        dishInfo4.setDishNum(new BigDecimal("0.00"));
        dishInfo4.setGiftDishNum(new BigDecimal("0.00"));
        dishInfo4.setDishSellUnitPrice(new BigDecimal("0.00"));
        dishInfo4.setMainGoodGuid("mainGoodGuid");
        dishInfo4.setIsMainGood(0);
        dishInfo4.setSurcharge(new BigDecimal("0.00"));
        dishInfo4.setDishType(0);
        dishInfo4.setSubtotal(new BigDecimal("0.00"));
        dishInfo4.setPayPrice(new BigDecimal("0.00"));
        dishInfo4.setDiscountMoney(new BigDecimal("0.00"));
        dishInfo4.setDishOriginalUnitPrice(new BigDecimal("0.00"));
        dishInfo4.setDishMemberPrice(new BigDecimal("0.00"));
        final List<RequestDishInfo> requestDishInfos = Arrays.asList(dishInfo4);
        final RequestVolumeCancel volumeCancelReqDTO = new RequestVolumeCancel();
        volumeCancelReqDTO.setNum(0);
        volumeCancelReqDTO.setMemberConsumptionGuid("memberConsumptionGuid");
        volumeCancelReqDTO.setStoreGuid("storeGuid");
        volumeCancelReqDTO.setVolumeCode("volumeCode");
        final RequestDishInfo dishInfo5 = new RequestDishInfo();
        dishInfo5.setOrderItemGuid("guid");
        dishInfo5.setDishGuid("dishGuid");
        dishInfo5.setDishName("dishName");
        dishInfo5.setDishSpecification("dishSpecification");
        dishInfo5.setDishUnit("dishUnit");
        dishInfo5.setDishNum(new BigDecimal("0.00"));
        dishInfo5.setGiftDishNum(new BigDecimal("0.00"));
        dishInfo5.setDishSellUnitPrice(new BigDecimal("0.00"));
        dishInfo5.setMainGoodGuid("mainGoodGuid");
        dishInfo5.setIsMainGood(0);
        dishInfo5.setSurcharge(new BigDecimal("0.00"));
        dishInfo5.setDishType(0);
        dishInfo5.setSubtotal(new BigDecimal("0.00"));
        dishInfo5.setPayPrice(new BigDecimal("0.00"));
        dishInfo5.setDiscountMoney(new BigDecimal("0.00"));
        dishInfo5.setDishOriginalUnitPrice(new BigDecimal("0.00"));
        dishInfo5.setDishMemberPrice(new BigDecimal("0.00"));
        volumeCancelReqDTO.setRequestDishInfoList(Arrays.asList(dishInfo5));
        when(mockTerminalServiceClient.cancel("volumeCode", volumeCancelReqDTO)).thenReturn(requestDishInfos);

        // Run the test
        final List<RequestDishInfo> result = priceCalculationUtilsUnderTest.dealWithVolume(context, allItems);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());

        // Confirm ItemService.setParentDishSkuInfo(...).
        final RequestDishInfo dishInfo6 = new RequestDishInfo();
        dishInfo6.setOrderItemGuid("guid");
        dishInfo6.setDishGuid("dishGuid");
        dishInfo6.setDishName("dishName");
        dishInfo6.setDishSpecification("dishSpecification");
        dishInfo6.setDishUnit("dishUnit");
        dishInfo6.setDishNum(new BigDecimal("0.00"));
        dishInfo6.setGiftDishNum(new BigDecimal("0.00"));
        dishInfo6.setDishSellUnitPrice(new BigDecimal("0.00"));
        dishInfo6.setMainGoodGuid("mainGoodGuid");
        dishInfo6.setIsMainGood(0);
        dishInfo6.setSurcharge(new BigDecimal("0.00"));
        dishInfo6.setDishType(0);
        dishInfo6.setSubtotal(new BigDecimal("0.00"));
        dishInfo6.setPayPrice(new BigDecimal("0.00"));
        dishInfo6.setDiscountMoney(new BigDecimal("0.00"));
        dishInfo6.setDishOriginalUnitPrice(new BigDecimal("0.00"));
        dishInfo6.setDishMemberPrice(new BigDecimal("0.00"));
        final List<RequestDishInfo> dishInfoList = Arrays.asList(dishInfo6);
        verify(mockItemService).setParentDishSkuInfo(dishInfoList);
    }

    @Test
    public void testDealWithVolume_HsmTerminalServiceClientCancelReturnsNoItems() {
        // Setup
        final DiscountContext context = new DiscountContext();
        final CalculateOrderDTO calculateOrderDTO = new CalculateOrderDTO();
        calculateOrderDTO.setVolumeCodes(Arrays.asList("value"));
        context.setCalculateOrderDTO(calculateOrderDTO);
        final CalculateOrderRespDTO calculateOrderRespDTO = new CalculateOrderRespDTO();
        calculateOrderRespDTO.setTip("tip");
        context.setCalculateOrderRespDTO(calculateOrderRespDTO);
        final DiscountRuleBO discountRuleBO = new DiscountRuleBO();
        discountRuleBO.setVerify(0);
        discountRuleBO.setHasMemberPrice(false);
        context.setDiscountRuleBO(discountRuleBO);
        final UserMemberSessionDTO userMemberSession = new UserMemberSessionDTO();
        userMemberSession.setNickName("nickName");
        userMemberSession.setMemberInfoGuid("memberInfoGuid");
        context.setUserMemberSession(userMemberSession);
        context.setUseMemberPriceFlag(false);
        context.setOrderGuid("orderGuid");
        context.setMemberConsumptionGuid("memberConsumptionGuid");

        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("guid");
        dineInItemDTO.setItemGuid("dishGuid");
        dineInItemDTO.setItemName("dishName");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setSkuGuid("dishSpecification");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setUnit("dishUnit");
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountTotalPrice(new BigDecimal("0.00"));
        final List<DineInItemDTO> allItems = Arrays.asList(dineInItemDTO);

        // Configure HsmTerminalServiceClient.cancel(...).
        final RequestVolumeCancel volumeCancelReqDTO = new RequestVolumeCancel();
        volumeCancelReqDTO.setNum(0);
        volumeCancelReqDTO.setMemberConsumptionGuid("memberConsumptionGuid");
        volumeCancelReqDTO.setStoreGuid("storeGuid");
        volumeCancelReqDTO.setVolumeCode("volumeCode");
        final RequestDishInfo dishInfo = new RequestDishInfo();
        dishInfo.setOrderItemGuid("guid");
        dishInfo.setDishGuid("dishGuid");
        dishInfo.setDishName("dishName");
        dishInfo.setDishSpecification("dishSpecification");
        dishInfo.setDishUnit("dishUnit");
        dishInfo.setDishNum(new BigDecimal("0.00"));
        dishInfo.setGiftDishNum(new BigDecimal("0.00"));
        dishInfo.setDishSellUnitPrice(new BigDecimal("0.00"));
        dishInfo.setMainGoodGuid("mainGoodGuid");
        dishInfo.setIsMainGood(0);
        dishInfo.setSurcharge(new BigDecimal("0.00"));
        dishInfo.setDishType(0);
        dishInfo.setSubtotal(new BigDecimal("0.00"));
        dishInfo.setPayPrice(new BigDecimal("0.00"));
        dishInfo.setDiscountMoney(new BigDecimal("0.00"));
        dishInfo.setDishOriginalUnitPrice(new BigDecimal("0.00"));
        dishInfo.setDishMemberPrice(new BigDecimal("0.00"));
        volumeCancelReqDTO.setRequestDishInfoList(Arrays.asList(dishInfo));
        when(mockTerminalServiceClient.cancel("volumeCode", volumeCancelReqDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final List<RequestDishInfo> result = priceCalculationUtilsUnderTest.dealWithVolume(context, allItems);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());

        // Confirm ItemService.setParentDishSkuInfo(...).
        final RequestDishInfo dishInfo1 = new RequestDishInfo();
        dishInfo1.setOrderItemGuid("guid");
        dishInfo1.setDishGuid("dishGuid");
        dishInfo1.setDishName("dishName");
        dishInfo1.setDishSpecification("dishSpecification");
        dishInfo1.setDishUnit("dishUnit");
        dishInfo1.setDishNum(new BigDecimal("0.00"));
        dishInfo1.setGiftDishNum(new BigDecimal("0.00"));
        dishInfo1.setDishSellUnitPrice(new BigDecimal("0.00"));
        dishInfo1.setMainGoodGuid("mainGoodGuid");
        dishInfo1.setIsMainGood(0);
        dishInfo1.setSurcharge(new BigDecimal("0.00"));
        dishInfo1.setDishType(0);
        dishInfo1.setSubtotal(new BigDecimal("0.00"));
        dishInfo1.setPayPrice(new BigDecimal("0.00"));
        dishInfo1.setDiscountMoney(new BigDecimal("0.00"));
        dishInfo1.setDishOriginalUnitPrice(new BigDecimal("0.00"));
        dishInfo1.setDishMemberPrice(new BigDecimal("0.00"));
        final List<RequestDishInfo> dishInfoList = Arrays.asList(dishInfo1);
        verify(mockItemService).setParentDishSkuInfo(dishInfoList);
    }

    @Test
    public void testDealWithRequestDishInfoByLadder() {
        // Setup
        final RequestDishInfo dishInfo = new RequestDishInfo();
        dishInfo.setOrderItemGuid("guid");
        dishInfo.setDishGuid("dishGuid");
        dishInfo.setDishName("dishName");
        dishInfo.setDishSpecification("dishSpecification");
        dishInfo.setDishUnit("dishUnit");
        dishInfo.setDishNum(new BigDecimal("0.00"));
        dishInfo.setGiftDishNum(new BigDecimal("0.00"));
        dishInfo.setDishSellUnitPrice(new BigDecimal("0.00"));
        dishInfo.setMainGoodGuid("mainGoodGuid");
        dishInfo.setIsMainGood(0);
        dishInfo.setSurcharge(new BigDecimal("0.00"));
        dishInfo.setDishType(0);
        dishInfo.setSubtotal(new BigDecimal("0.00"));
        dishInfo.setPayPrice(new BigDecimal("0.00"));
        dishInfo.setDiscountMoney(new BigDecimal("0.00"));
        dishInfo.setDishOriginalUnitPrice(new BigDecimal("0.00"));
        dishInfo.setDishMemberPrice(new BigDecimal("0.00"));
        final List<RequestDishInfo> requestDishInfos = Arrays.asList(dishInfo);

        // Run the test
        priceCalculationUtilsUnderTest.dealWithRequestDishInfoByLadder(requestDishInfos);

        // Verify the results
    }

    @Test
    public void testDealwithVolumeByDiscount() {
        // Setup
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("guid");
        dineInItemDTO.setItemGuid("dishGuid");
        dineInItemDTO.setItemName("dishName");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setSkuGuid("dishSpecification");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO.setMemberPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setUnit("dishUnit");
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountTotalPrice(new BigDecimal("0.00"));
        final List<DineInItemDTO> allItems = Arrays.asList(dineInItemDTO);
        final RequestDishInfo dishInfo = new RequestDishInfo();
        dishInfo.setOrderItemGuid("guid");
        dishInfo.setDishGuid("dishGuid");
        dishInfo.setDishName("dishName");
        dishInfo.setDishSpecification("dishSpecification");
        dishInfo.setDishUnit("dishUnit");
        dishInfo.setDishNum(new BigDecimal("0.00"));
        dishInfo.setGiftDishNum(new BigDecimal("0.00"));
        dishInfo.setDishSellUnitPrice(new BigDecimal("0.00"));
        dishInfo.setMainGoodGuid("mainGoodGuid");
        dishInfo.setIsMainGood(0);
        dishInfo.setSurcharge(new BigDecimal("0.00"));
        dishInfo.setDishType(0);
        dishInfo.setSubtotal(new BigDecimal("0.00"));
        dishInfo.setPayPrice(new BigDecimal("0.00"));
        dishInfo.setDiscountMoney(new BigDecimal("0.00"));
        dishInfo.setDishOriginalUnitPrice(new BigDecimal("0.00"));
        dishInfo.setDishMemberPrice(new BigDecimal("0.00"));
        final List<RequestDishInfo> dishInfoDTOList = Arrays.asList(dishInfo);

        // Run the test
        priceCalculationUtilsUnderTest.dealwithVolumeByDiscount(allItems, dishInfoDTOList);

        // Verify the results
    }
}
