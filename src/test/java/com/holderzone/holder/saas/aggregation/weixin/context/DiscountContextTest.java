package com.holderzone.holder.saas.aggregation.weixin.context;

import com.holderzone.holder.saas.aggregation.weixin.entity.bo.DiscountRuleBO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.trade.DiscountDTO;
import com.holderzone.saas.store.dto.weixin.deal.ActivitySelectDTO;
import com.holderzone.saas.store.dto.weixin.deal.CalculateOrderDTO;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberSessionDTO;
import com.holderzone.saas.store.dto.weixin.resp.CalculateOrderRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.MarketingActivityInfoRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class DiscountContextTest {

    @Mock
    private CalculateOrderDTO mockCalculateOrderDTO;
    @Mock
    private CalculateOrderRespDTO mockCalculateOrderRespDTO;
    @Mock
    private List<DineInItemDTO> mockAllItems;
    @Mock
    private List<DineInItemDTO> mockBeforeSpecialsItems;
    @Mock
    private BigDecimal mockMemberGrouponDiscount;
    @Mock
    private DiscountRuleBO mockDiscountRuleBO;
    @Mock
    private Map<Integer, DiscountDTO> mockDiscountTypeMap;
    @Mock
    private List<DiscountFeeDetailDTO> mockDiscountFeeDetailDTOS;
    @Mock
    private Map<String, DineInItemDTO> mockDineInItemDTOMap;
    @Mock
    private UserMemberSessionDTO mockUserMemberSession;

    private DiscountContext discountContextUnderTest;

    @Before
    public void setUp() throws Exception {
        discountContextUnderTest = new DiscountContext();
        discountContextUnderTest.setCalculateOrderDTO(mockCalculateOrderDTO);
        discountContextUnderTest.setCalculateOrderRespDTO(mockCalculateOrderRespDTO);
        discountContextUnderTest.setAllItems(mockAllItems);
        discountContextUnderTest.setBeforeSpecialsItems(mockBeforeSpecialsItems);
        discountContextUnderTest.setMemberGrouponDiscount(mockMemberGrouponDiscount);
        discountContextUnderTest.setDiscountRuleBO(mockDiscountRuleBO);
        discountContextUnderTest.setDiscountTypeMap(mockDiscountTypeMap);
        discountContextUnderTest.setDiscountFeeDetailDTOS(mockDiscountFeeDetailDTOS);
        discountContextUnderTest.setDineInItemDTOMap(mockDineInItemDTOMap);
        discountContextUnderTest.setUserMemberSession(mockUserMemberSession);
    }

    @Test
    public void testInit() {
        // Setup
        final CalculateOrderDTO calculateDTO = new CalculateOrderDTO();
        calculateDTO.setDeviceType(0);
        calculateDTO.setUseMemberDiscountFlag(false);
        calculateDTO.setMemberIntegralStore(false);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("9f8a64ab-7154-4638-b0d3-3653c31eab40");
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setTotalDiscountFee(new BigDecimal("0.00"));
        dineInItemDTO.setDiscountTotalPrice(new BigDecimal("0.00"));
        calculateDTO.setDineInItemList(Arrays.asList(dineInItemDTO));
        calculateDTO.setIsFirst(false);
        calculateDTO.setIsReplace(false);
        calculateDTO.setOrderGuid("orderGuid");

        final CalculateOrderRespDTO calculateOrderRespDTO = new CalculateOrderRespDTO();
        calculateOrderRespDTO.setOrderSurplusFee(new BigDecimal("0.00"));
        calculateOrderRespDTO.setOrderSurplusFeeBySkipSpecials(new BigDecimal("0.00"));
        calculateOrderRespDTO.setOrderFee(new BigDecimal("0.00"));
        final ActivitySelectDTO activitySelectDTO = new ActivitySelectDTO();
        calculateOrderRespDTO.setActivitySelectList(Arrays.asList(activitySelectDTO));
        final MarketingActivityInfoRespDTO infoRespDTO = new MarketingActivityInfoRespDTO();
        calculateOrderRespDTO.setActivityInfoList(Arrays.asList(infoRespDTO));

        final DiscountRuleBO discountRuleBO = new DiscountRuleBO();
        discountRuleBO.setVolumeGuid("volumeGuid");
        discountRuleBO.setVolumeCodeType(0);
        discountRuleBO.setHasMemberPrice(false);
        discountRuleBO.setHasMemberDiscount(false);
        discountRuleBO.setMemberDiscount(new BigDecimal("0.00"));

        final UserMemberSessionDTO userMemberSession = new UserMemberSessionDTO();
        userMemberSession.setStoreGuid("storeGuid");
        userMemberSession.setOpenId("openId");
        userMemberSession.setUserGuid("userGuid");
        userMemberSession.setIsLogin(false);
        userMemberSession.setNickName("nickName");

        // Run the test
        final DiscountContext result = DiscountContext.init(calculateDTO, calculateOrderRespDTO, discountRuleBO,
                userMemberSession);
        assertThat(result.getCalculateOrderDTO()).isEqualTo(new CalculateOrderDTO());
        assertThat(result.getCalculateOrderRespDTO()).isEqualTo(new CalculateOrderRespDTO());
        assertThat(result.getAllItems()).isEqualTo(Arrays.asList(new DineInItemDTO()));
        assertThat(result.getBeforeSpecialsItems()).isEqualTo(Arrays.asList(new DineInItemDTO()));
        assertThat(result.getMemberGrouponDiscount()).isEqualTo(new BigDecimal("0.00"));
        assertThat(result.getDiscountRuleBO()).isEqualTo(new DiscountRuleBO());
        assertThat(result.getDiscountTypeMap()).isEqualTo(new HashMap<>());
        assertThat(result.getDiscountFeeDetailDTOS()).isEqualTo(Arrays.asList(new DiscountFeeDetailDTO()));
        assertThat(result.getDineInItemDTOMap()).isEqualTo(new HashMap<>());
        assertThat(result.getUserMemberSession()).isEqualTo(new UserMemberSessionDTO());
        assertThat(result.getVolumeGuid()).isEqualTo("volumeGuid");
        assertThat(result.getVolumeCodeType()).isEqualTo(0);
        assertThat(result.isIntegralStore()).isFalse();
        assertThat(result.isRejectDiscount()).isFalse();
        assertThat(result.isHasMember()).isFalse();
        assertThat(result.getUseMemberPriceFlag()).isFalse();
        assertThat(result.getUseMemberDiscountFlag()).isFalse();
        assertThat(result.getNeedFullMarketActivityList()).isFalse();
        assertThat(result.getNeedLimitSpecialsMarketActivityList()).isFalse();
        assertThat(result.isCalculateMinPrice()).isFalse();
        assertThat(result.getIsFirst()).isFalse();
        assertThat(result.getIsReplace()).isFalse();
        assertThat(result.getOrderGuid()).isEqualTo("orderGuid");
        assertThat(result.getMemberConsumptionGuid()).isEqualTo("memberConsumptionGuid");
        assertThat(result.equals("o")).isFalse();
        assertThat(result.hashCode()).isEqualTo(0);
        assertThat(result.toString()).isEqualTo("result");
    }

    @Test
    public void testGetCalculateOrderDTO() {
        assertThat(discountContextUnderTest.getCalculateOrderDTO()).isEqualTo(mockCalculateOrderDTO);
    }

    @Test
    public void testGetCalculateOrderRespDTO() {
        assertThat(discountContextUnderTest.getCalculateOrderRespDTO()).isEqualTo(mockCalculateOrderRespDTO);
    }

    @Test
    public void testGetAllItems() {
        assertThat(discountContextUnderTest.getAllItems()).isEqualTo(mockAllItems);
    }

    @Test
    public void testGetBeforeSpecialsItems() {
        assertThat(discountContextUnderTest.getBeforeSpecialsItems()).isEqualTo(mockBeforeSpecialsItems);
    }

    @Test
    public void testGetMemberGrouponDiscount() {
        assertThat(discountContextUnderTest.getMemberGrouponDiscount()).isEqualTo(mockMemberGrouponDiscount);
    }

    @Test
    public void testGetDiscountRuleBO() {
        assertThat(discountContextUnderTest.getDiscountRuleBO()).isEqualTo(mockDiscountRuleBO);
    }

    @Test
    public void testGetDiscountTypeMap() {
        assertThat(discountContextUnderTest.getDiscountTypeMap()).isEqualTo(mockDiscountTypeMap);
    }

    @Test
    public void testGetDiscountFeeDetailDTOS() {
        assertThat(discountContextUnderTest.getDiscountFeeDetailDTOS()).isEqualTo(mockDiscountFeeDetailDTOS);
    }

    @Test
    public void testGetDineInItemDTOMap() {
        assertThat(discountContextUnderTest.getDineInItemDTOMap()).isEqualTo(mockDineInItemDTOMap);
    }

    @Test
    public void testGetUserMemberSession() {
        assertThat(discountContextUnderTest.getUserMemberSession()).isEqualTo(mockUserMemberSession);
    }

    @Test
    public void testVolumeGuidGetterAndSetter() {
        final String volumeGuid = "volumeGuid";
        discountContextUnderTest.setVolumeGuid(volumeGuid);
        assertThat(discountContextUnderTest.getVolumeGuid()).isEqualTo(volumeGuid);
    }

    @Test
    public void testVolumeCodeTypeGetterAndSetter() {
        final Integer volumeCodeType = 0;
        discountContextUnderTest.setVolumeCodeType(volumeCodeType);
        assertThat(discountContextUnderTest.getVolumeCodeType()).isEqualTo(volumeCodeType);
    }

    @Test
    public void testIntegralStoreGetterAndSetter() {
        final boolean integralStore = false;
        discountContextUnderTest.setIntegralStore(integralStore);
        assertThat(discountContextUnderTest.isIntegralStore()).isFalse();
    }

    @Test
    public void testRejectDiscountGetterAndSetter() {
        final boolean rejectDiscount = false;
        discountContextUnderTest.setRejectDiscount(rejectDiscount);
        assertThat(discountContextUnderTest.isRejectDiscount()).isFalse();
    }

    @Test
    public void testHasMemberGetterAndSetter() {
        final boolean hasMember = false;
        discountContextUnderTest.setHasMember(hasMember);
        assertThat(discountContextUnderTest.isHasMember()).isFalse();
    }

    @Test
    public void testUseMemberPriceFlagGetterAndSetter() {
        final Boolean useMemberPriceFlag = false;
        discountContextUnderTest.setUseMemberPriceFlag(useMemberPriceFlag);
        assertThat(discountContextUnderTest.getUseMemberPriceFlag()).isFalse();
    }

    @Test
    public void testUseMemberDiscountFlagGetterAndSetter() {
        final Boolean useMemberDiscountFlag = false;
        discountContextUnderTest.setUseMemberDiscountFlag(useMemberDiscountFlag);
        assertThat(discountContextUnderTest.getUseMemberDiscountFlag()).isFalse();
    }

    @Test
    public void testNeedFullMarketActivityListGetterAndSetter() {
        final Boolean needFullMarketActivityList = false;
        discountContextUnderTest.setNeedFullMarketActivityList(needFullMarketActivityList);
        assertThat(discountContextUnderTest.getNeedFullMarketActivityList()).isFalse();
    }

    @Test
    public void testNeedLimitSpecialsMarketActivityListGetterAndSetter() {
        final Boolean needLimitSpecialsMarketActivityList = false;
        discountContextUnderTest.setNeedLimitSpecialsMarketActivityList(needLimitSpecialsMarketActivityList);
        assertThat(discountContextUnderTest.getNeedLimitSpecialsMarketActivityList()).isFalse();
    }

    @Test
    public void testIsCalculateMinPriceGetterAndSetter() {
        final boolean isCalculateMinPrice = false;
        discountContextUnderTest.setCalculateMinPrice(isCalculateMinPrice);
        assertThat(discountContextUnderTest.isCalculateMinPrice()).isFalse();
    }

    @Test
    public void testIsFirstGetterAndSetter() {
        final Boolean isFirst = false;
        discountContextUnderTest.setIsFirst(isFirst);
        assertThat(discountContextUnderTest.getIsFirst()).isFalse();
    }

    @Test
    public void testIsReplaceGetterAndSetter() {
        final Boolean isReplace = false;
        discountContextUnderTest.setIsReplace(isReplace);
        assertThat(discountContextUnderTest.getIsReplace()).isFalse();
    }

    @Test
    public void testOrderGuidGetterAndSetter() {
        final String orderGuid = "orderGuid";
        discountContextUnderTest.setOrderGuid(orderGuid);
        assertThat(discountContextUnderTest.getOrderGuid()).isEqualTo(orderGuid);
    }

    @Test
    public void testMemberConsumptionGuidGetterAndSetter() {
        final String memberConsumptionGuid = "memberConsumptionGuid";
        discountContextUnderTest.setMemberConsumptionGuid(memberConsumptionGuid);
        assertThat(discountContextUnderTest.getMemberConsumptionGuid()).isEqualTo(memberConsumptionGuid);
    }

    @Test
    public void testEquals() throws Exception {
        assertThat(discountContextUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() throws Exception {
        assertThat(discountContextUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() throws Exception {
        assertThat(discountContextUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() throws Exception {
        assertThat(discountContextUnderTest.toString()).isEqualTo("result");
    }
}
