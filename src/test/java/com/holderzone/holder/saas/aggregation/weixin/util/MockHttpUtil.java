package com.holderzone.holder.saas.aggregation.weixin.util;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.util.MultiValueMap;

/**
 * <AUTHOR>
 */
@Slf4j
public class MockHttpUtil {

    private MockHttpUtil() {

    }

    private static String headerOfOperSubjectGuid = "operSubjectGuid";
    private static String operSubjectGuid = "2010121440477930009";

    private static String headerOfSource = "source";
    private static String source = "8";

    private static String headerOfEnterpriseGuid = "enterpriseGuid";
    private static String enterpriseGuid = "2009281531195930006";

    private static String headerOfStoreGuid = "storeGuid";
    private static String storeGuid = "2106221850429620006";

    private static String headerOfWxToken = "wxToken";
    private static String wxToken = "7118089909948121088";

    /**
     * get
     */
    public static String get(String uri, MultiValueMap<String, String> params, MockMvc mockMvc) {
        try {
            MockHttpServletRequestBuilder getRequest = MockMvcRequestBuilders.get(uri);
            getRequest.header(headerOfOperSubjectGuid, operSubjectGuid);
            getRequest.header(headerOfSource, source);
            getRequest.header(headerOfEnterpriseGuid, enterpriseGuid);
            getRequest.header(headerOfStoreGuid, storeGuid);
            getRequest.header(headerOfWxToken, wxToken);
            if (params != null) {
                getRequest.params(params);
            }
            MvcResult tradeResult = mockMvc.perform(getRequest).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            MockHttpServletResponse response = tradeResult.getResponse();
            return response.getContentAsString();
        } catch (Exception e) {
            log.error("get error:{}", e.getMessage());
            return null;
        }
    }

    /**
     * post
     */
    public static String post(String uri, MultiValueMap<String, String> params, MockMvc mockMvc) {
        try {
            MockHttpServletRequestBuilder postDataRequest = MockMvcRequestBuilders.post(uri);
            postDataRequest.header(headerOfOperSubjectGuid, operSubjectGuid);
            postDataRequest.header(headerOfSource, source);
            postDataRequest.header(headerOfEnterpriseGuid, enterpriseGuid);
            postDataRequest.header(headerOfStoreGuid, storeGuid);
            postDataRequest.header(headerOfWxToken, wxToken);
            if (params != null) {
                postDataRequest.params(params);
            }
            MvcResult tradeResult = mockMvc.perform(postDataRequest).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            MockHttpServletResponse response = tradeResult.getResponse();
            return response.getContentAsString();
        } catch (Exception e) {
            log.error("post data error:{}", e.getMessage());
            return null;
        }
    }

    /**
     * post
     */
    public static String post(String uri, Object body, MockMvc mockMvc) {
        try {
            MockHttpServletRequestBuilder postBodyRequest = MockMvcRequestBuilders.post(uri)
                    .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(body));
            postBodyRequest.header(headerOfOperSubjectGuid, operSubjectGuid);
            postBodyRequest.header(headerOfSource, source);
            postBodyRequest.header(headerOfEnterpriseGuid, enterpriseGuid);
            postBodyRequest.header(headerOfStoreGuid, storeGuid);
            postBodyRequest.header(headerOfWxToken, wxToken);
            MvcResult tradeResult = mockMvc.perform(postBodyRequest).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            MockHttpServletResponse response = tradeResult.getResponse();
            return response.getContentAsString();
        } catch (Exception e) {
            log.error("post body error:{}", e.getMessage());
            return null;
        }
    }

    /**
     * put
     */
    public static String put(String uri, MultiValueMap<String, String> params, MockMvc mockMvc) {
        try {
            MockHttpServletRequestBuilder putDataRequest = MockMvcRequestBuilders.put(uri);
            putDataRequest.header(headerOfOperSubjectGuid, operSubjectGuid);
            putDataRequest.header(headerOfSource, source);
            putDataRequest.header(headerOfEnterpriseGuid, enterpriseGuid);
            putDataRequest.header(headerOfStoreGuid, storeGuid);
            putDataRequest.header(headerOfWxToken, wxToken);
            if (params != null) {
                putDataRequest.params(params);
            }
            MvcResult tradeResult = mockMvc.perform(putDataRequest).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            MockHttpServletResponse response = tradeResult.getResponse();
            return response.getContentAsString();
        } catch (Exception e) {
            log.error("put data error:{}", e.getMessage());
            return null;
        }
    }

    /**
     * put
     */
    public static String put(String uri, Object body, MockMvc mockMvc) {
        try {
            MockHttpServletRequestBuilder putBodyRequest = MockMvcRequestBuilders.put(uri)
                    .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(body));
            putBodyRequest.header(headerOfOperSubjectGuid, operSubjectGuid);
            putBodyRequest.header(headerOfSource, source);
            putBodyRequest.header(headerOfEnterpriseGuid, enterpriseGuid);
            putBodyRequest.header(headerOfStoreGuid, storeGuid);
            putBodyRequest.header(headerOfWxToken, wxToken);
            MvcResult tradeResult = mockMvc.perform(putBodyRequest).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            MockHttpServletResponse response = tradeResult.getResponse();
            return response.getContentAsString();
        } catch (Exception e) {
            log.error("put body error:{}", e.getMessage());
            return null;
        }
    }

    /**
     * delete
     */
    public static String delete(String uri, MockMvc mockMvc) {
        try {
            MockHttpServletRequestBuilder deleteRequest = MockMvcRequestBuilders.delete(uri);
            deleteRequest.header(headerOfOperSubjectGuid, operSubjectGuid);
            deleteRequest.header(headerOfSource, source);
            deleteRequest.header(headerOfEnterpriseGuid, enterpriseGuid);
            deleteRequest.header(headerOfStoreGuid, storeGuid);
            deleteRequest.header(headerOfWxToken, wxToken);
            MvcResult tradeResult = mockMvc.perform(deleteRequest).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            MockHttpServletResponse response = tradeResult.getResponse();
            return response.getContentAsString();
        } catch (Exception e) {
            log.error("delete error:{}", e.getMessage());
            return null;
        }
    }

}
