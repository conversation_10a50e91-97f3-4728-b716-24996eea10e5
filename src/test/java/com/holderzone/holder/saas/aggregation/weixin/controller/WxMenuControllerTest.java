package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.holder.saas.aggregation.weixin.service.WxStoreTradeOrderService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.EnterpriseClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxStoreMenuDetailsClientService;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.weixin.MultiMemberDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAuthorizerInfoDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreMenuReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxMenuDetailsDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreConfigRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreItemRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxTypeAndTagDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(WxMenuController.class)
public class WxMenuControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStoreMenuDetailsClientService mockWxStoreMenuDetailsClientService;
    @MockBean
    private EnterpriseClientService mockEnterpriseClientService;
    @MockBean
    private WxStoreTradeOrderService mockWxStoreTradeOrderService;
    @MockBean
    private RedisUtils mockRedisUtils;
    @MockBean
    private RedisTemplate<String, Integer> mockRedisTemplate;

    @Test
    public void testWxMenuDetails() throws Exception {
        // Setup
        // Configure WxStoreMenuDetailsClientService.getWxMenuDetails(...).
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setTypeGuid("typeGuid");
        wxStoreItemRespDTO.setTypeName("typeName");
        wxStoreItemRespDTO.setItemType(0);
        wxStoreItemRespDTO.setIsSoldOut(0);
        final WxTypeAndTagDTO wxTypeAndTagDTO = new WxTypeAndTagDTO();
        wxTypeAndTagDTO.setIsType(0);
        wxTypeAndTagDTO.setIsOpened((byte) 0b0);
        final WxStoreConfigRespDTO wxStoreConfigRespDTO = new WxStoreConfigRespDTO();
        wxStoreConfigRespDTO.setIsOrderOpen(0);
        wxStoreConfigRespDTO.setOrderModel(0);
        wxStoreConfigRespDTO.setTakingModel(0);
        wxStoreConfigRespDTO.setIsOnlinePayed(0);
        wxStoreConfigRespDTO.setPayWayNames("payWayNames");
        final WxMenuDetailsDTO wxMenuDetailsDTO = new WxMenuDetailsDTO(Arrays.asList(wxStoreItemRespDTO),
                Arrays.asList(wxTypeAndTagDTO), wxStoreConfigRespDTO, 0, 0, "orderGuid");
        when(mockWxStoreMenuDetailsClientService.getWxMenuDetails(
                new WxStoreMenuReqDTO("enterpriseGuid", "storeGuid", 0,
                        new WxStoreConsumerDTO("consumerGuid", "openId", "unionId", "nickName", "headImgUrl", 0,
                                "country", "province", "city","","", "phone", 0, "deviceId", "enterpriseGuid",
                                "enterpriseName", "storeGuid", "storeName", "userGuid", "userName", "account", 0,
                                "diningTableGuid", "tableCode", "diningTableName", "areaGuid", "areaName", "brandName",
                                "brandGuid", "brandLogo", false, "memberInfoGuid", "phoneNum", false, "operSubjectGuid",
                                false)))).thenReturn(wxMenuDetailsDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx-store-menu/details")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetConsumerInfo() throws Exception {
        // Setup
        // Configure WxStoreMenuDetailsClientService.getWxAuthInfo(...).
        final WxStoreAuthorizerInfoDTO wxStoreAuthorizerInfoDTO = new WxStoreAuthorizerInfoDTO();
        wxStoreAuthorizerInfoDTO.setId(0L);
        wxStoreAuthorizerInfoDTO.setGuid("6063f05b-3289-4701-a514-f85ac9197907");
        wxStoreAuthorizerInfoDTO.setBrandGuid("brandGuid");
        wxStoreAuthorizerInfoDTO.setIsAlliance(false);
        wxStoreAuthorizerInfoDTO.setOperSubjectGuid("");
        when(mockWxStoreMenuDetailsClientService.getWxAuthInfo("brandGuid")).thenReturn(wxStoreAuthorizerInfoDTO);

        // Configure EnterpriseClientService.list(...).
        final MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
        multiMemberDTO.setId(0L);
        multiMemberDTO.setEnterpriseGuid("enterpriseGuid");
        multiMemberDTO.setMultiMemberGuid("multiMemberGuid");
        multiMemberDTO.setMultiMemberName("multiMemberName");
        multiMemberDTO.setEnabled(false);
        final List<MultiMemberDTO> multiMemberDTOS = Arrays.asList(multiMemberDTO);
        when(mockEnterpriseClientService.list(new HashSet<>(Arrays.asList("value")))).thenReturn(multiMemberDTOS);

        // Configure WxStoreMenuDetailsClientService.getConsumerInfo(...).
        final WxStoreConsumerDTO wxStoreConsumerDTO = new WxStoreConsumerDTO("consumerGuid", "openId", "unionId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "","","phone", 0, "deviceId", "enterpriseGuid",
                "enterpriseName", "storeGuid", "storeName", "userGuid", "userName", "account", 0, "diningTableGuid",
                "tableCode", "diningTableName", "areaGuid", "areaName", "brandName", "brandGuid", "brandLogo", false,
                "memberInfoGuid", "phoneNum", false, "operSubjectGuid", false);
        when(mockWxStoreMenuDetailsClientService.getConsumerInfo(
                new WxPortalReqDTO("enterpriseGuid", "appId", "storeGuid", "brandGuid", "msgKey", "url",
                        0, "","","",""))).thenReturn(wxStoreConsumerDTO);

        when(mockWxStoreTradeOrderService.getFastGuestCount("diningTableGuid", "openId")).thenReturn(0);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx-store-menu/get_consumer_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetConsumerInfo_EnterpriseClientServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure WxStoreMenuDetailsClientService.getWxAuthInfo(...).
        final WxStoreAuthorizerInfoDTO wxStoreAuthorizerInfoDTO = new WxStoreAuthorizerInfoDTO();
        wxStoreAuthorizerInfoDTO.setId(0L);
        wxStoreAuthorizerInfoDTO.setGuid("6063f05b-3289-4701-a514-f85ac9197907");
        wxStoreAuthorizerInfoDTO.setBrandGuid("brandGuid");
        wxStoreAuthorizerInfoDTO.setIsAlliance(false);
        wxStoreAuthorizerInfoDTO.setOperSubjectGuid("");
        when(mockWxStoreMenuDetailsClientService.getWxAuthInfo("brandGuid")).thenReturn(wxStoreAuthorizerInfoDTO);

        when(mockEnterpriseClientService.list(new HashSet<>(Arrays.asList("value"))))
                .thenReturn(Collections.emptyList());
        when(mockWxStoreTradeOrderService.getFastGuestCount("diningTableGuid", "openId")).thenReturn(0);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx-store-menu/get_consumer_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetConsumer() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/wx-store-menu/get_consumer")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSetOrderNumber() throws Exception {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx-store-menu/set_order_number")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetOrderNumber() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx-store-menu/get_order_number")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDeleteOrderNumber() throws Exception {
        // Setup
        when(mockRedisTemplate.delete("key")).thenReturn(false);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx-store-menu/delete_order_number")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDeleteOrderNumber_RedisTemplateReturnsNull() throws Exception {
        // Setup
        when(mockRedisTemplate.delete("key")).thenReturn(null);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx-store-menu/delete_order_number")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("");
    }
}
