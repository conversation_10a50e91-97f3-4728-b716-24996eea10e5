package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import org.junit.Before;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;

public class AuthRespDTOTest {

    private AuthRespDTO authRespDTOUnderTest;

    @Before
    public void setUp() throws Exception {
        authRespDTOUnderTest = new AuthRespDTO("token");
    }

    @Test
    public void testSetGetUsername() {
        AuthRespDTO authRespDTO = new AuthRespDTO();
        authRespDTO.setToken("token");
        assertEquals("token", authRespDTO.getToken());
    }

    @Test
    public void testTokenGetterAndSetter() {
        final String token = "token";
        authRespDTOUnderTest.setToken(token);
        assertThat(authRespDTOUnderTest.getToken()).isEqualTo(token);
    }

    @Test
    public void testEquals() {
        assertThat(authRespDTOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() {
        assertThat(authRespDTOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() {
        assertThat(authRespDTOUnderTest.hashCode()).isEqualTo(110541364);
    }

    @Test
    public void testToString() {
        assertThat(authRespDTOUnderTest.toString()).isEqualTo("AuthRespDTO(token=token)");
    }
}
