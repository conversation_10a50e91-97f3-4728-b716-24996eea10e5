package com.holderzone.holder.saas.aggregation.weixin.controller.reserve;

import com.holderzone.holder.saas.aggregation.weixin.service.ReserveAppletService;
import com.holderzone.saas.store.reserve.api.dto.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(ReserveController.class)
public class ReserveControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ReserveAppletService mockReserveAppletService;

    @Test
    public void testGetAvailableStoreList() throws Exception {
        // Setup
        // Configure ReserveAppletService.getAvailableStoreList(...).
        final ReserveAvailableStoreDTO reserveAvailableStoreDTO = new ReserveAvailableStoreDTO();
        reserveAvailableStoreDTO.setStoreGuid("storeGuid");
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("b9128821-5e4d-492a-8405-f36d70d4d3b6");
        areaDTO.setName("name");
        areaDTO.setNum(0);
        reserveAvailableStoreDTO.setReserveAreaNames(Arrays.asList(areaDTO));
        final List<ReserveAvailableStoreDTO> reserveAvailableStoreDTOS = Arrays.asList(reserveAvailableStoreDTO);
        final ReserveAppletQueryDTO queryDTO = new ReserveAppletQueryDTO();
        queryDTO.setStoreGuids(Arrays.asList("value"));
        queryDTO.setReserveDate(LocalDate.of(2020, 1, 1));
        queryDTO.setAppletState("appletState");
        queryDTO.setStates(Arrays.asList(0));
        queryDTO.setCreateUserId("createUserId");
        when(mockReserveAppletService.getAvailableStoreList(queryDTO)).thenReturn(reserveAvailableStoreDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/available_store_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetAvailableStoreList_ReserveAppletServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure ReserveAppletService.getAvailableStoreList(...).
        final ReserveAppletQueryDTO queryDTO = new ReserveAppletQueryDTO();
        queryDTO.setStoreGuids(Arrays.asList("value"));
        queryDTO.setReserveDate(LocalDate.of(2020, 1, 1));
        queryDTO.setAppletState("appletState");
        queryDTO.setStates(Arrays.asList(0));
        queryDTO.setCreateUserId("createUserId");
        when(mockReserveAppletService.getAvailableStoreList(queryDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/available_store_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testGetAvailableStore() throws Exception {
        // Setup
        // Configure ReserveAppletService.getAvailableStore(...).
        final ReserveAvailableStoreConfigDTO reserveAvailableStoreConfigDTO = new ReserveAvailableStoreConfigDTO();
        reserveAvailableStoreConfigDTO.setStoreGuid("storeGuid");
        final TimingSegmentDTO timingSegmentDTO = new TimingSegmentDTO();
        timingSegmentDTO.setStart(LocalTime.of(0, 0, 0));
        timingSegmentDTO.setEnd(LocalTime.of(0, 0, 0));
        timingSegmentDTO.setPeriod(0);
        reserveAvailableStoreConfigDTO.setSegments(Arrays.asList(timingSegmentDTO));
        final ReserveAppletQueryDTO queryDTO = new ReserveAppletQueryDTO();
        queryDTO.setStoreGuids(Arrays.asList("value"));
        queryDTO.setReserveDate(LocalDate.of(2020, 1, 1));
        queryDTO.setAppletState("appletState");
        queryDTO.setStates(Arrays.asList(0));
        queryDTO.setCreateUserId("createUserId");
        when(mockReserveAppletService.getAvailableStore(queryDTO)).thenReturn(reserveAvailableStoreConfigDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/available_store")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testLaunch() throws Exception {
        // Setup
        // Configure ReserveAppletService.launch(...).
        final ReserveRecordDTO reserveRecordDTO = new ReserveRecordDTO();
        reserveRecordDTO.setGuid("dde19845-4cd5-4e41-9639-ceb7ecf296df");
        reserveRecordDTO.setStoreGuid("storeGuid");
        reserveRecordDTO.setNumber(0);
        reserveRecordDTO.setState("state");
        reserveRecordDTO.setName("name");
        when(mockReserveAppletService.launch(reserveRecordDTO)).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/launch")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
