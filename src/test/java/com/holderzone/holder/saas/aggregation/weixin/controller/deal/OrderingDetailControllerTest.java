package com.holderzone.holder.saas.aggregation.weixin.controller.deal;

import com.google.common.collect.Lists;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.*;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.aggregation.weixin.utils.UserMemberSessionUtils;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ConsumptionGiftDetailDTO;
import com.holderzone.holder.saas.weixin.common.ErrorCoderEnum;
import com.holderzone.holder.saas.weixin.entry.dto.*;
import com.holderzone.holder.saas.weixin.entry.dto.req.WxOrderDetailReqDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.order.common.ItemAttrDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderDTO;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberSessionDTO;
import com.holderzone.saas.store.dto.weixin.deal.WechatOrderInfoDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(OrderingDetailController.class)
public class OrderingDetailControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStoreMenuDetailsClientService mockWxStoreMenuDetailsClientService;
    @MockBean
    private WxOrderRecordClientService mockWxOrderRecordClientService;
    @MockBean
    private WxQueueClientService mockWxQueueClientService;
    @MockBean
    private UserMemberSessionUtils mockUserMemberSessionUtils;
    @MockBean
    private HsaBaseClientService mockHsaBaseClientService;
    @MockBean
    private RedisUtils mockRedisUtils;
    @MockBean
    private TradeOrderService mockTradeOrderService;
    @MockBean
    private WxStoreMerchantOrderClientService mockMerchantOrderClientService;
    @MockBean
    private MemberMarketingClientService mockMemberMarketingClientService;

    @Test
    public void testDetail() throws Exception {
        // Setup
        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        when(mockHsaBaseClientService.hasMemberPrice("memberCardGuid")).thenReturn(new ResponseModel<>(false));

        // Configure WxOrderRecordClientService.getOrderDetail(...).
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setGuid("5597aa19-a8db-4094-b817-bcc0042a1140");
        itemAttrDTO.setAttrGuid("attrGuid");
        itemAttrDTO.setAttrName("attrName");
        itemAttrDTO.setAttrGroupName("attrGroupName");
        itemAttrDTO.setAttrGroupGuid("attrGroupGuid");
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setGuid("5597aa19-a8db-4094-b817-bcc0042a1140");
        itemAttrDTO1.setAttrGuid("attrGuid");
        itemAttrDTO1.setAttrName("attrName");
        itemAttrDTO1.setAttrGroupName("attrGroupName");
        itemAttrDTO1.setAttrGroupGuid("attrGroupGuid");
        final AppendFeeDetailDTO appendFeeDetailDTO = new AppendFeeDetailDTO();
        appendFeeDetailDTO.setName("name");
        appendFeeDetailDTO.setCount(0);
        appendFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO.setTotalAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO.setType(0);
        final OrderDetailDTO orderDetailDTO = new OrderDetailDTO(0, "orderNo", "ca22cfdd-329e-49b1-8008-9d427760ba4f",
                "orderGuid", "mark", "remark", "mergeInfo", "brandGuid", "brandName", "storeGuid", "storeName",
                "diningTableGuid", "diningTableCode", "areaGuid", "areaName", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, Arrays.asList(
                new OrderTableItemDTO("tableGuid", "tableCode", "areaName", "diningTableName", 0, 0, 0, new HashSet<>(
                        Arrays.asList(new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid",
                                "userRecordGuid", "operateGuid", false, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                                "remark", "tableGuid", Arrays.asList(
                                new DineItemDTO("523a7607-0fcd-4aac-aa90-e0f2909781cd", "itemGuid", "itemName", "code",
                                        0, "skuGuid", "skuName", new BigDecimal("0.00"), "amountUnit",
                                        new BigDecimal("0.00"), new BigDecimal("0.00"), false, new BigDecimal("0.00"),
                                        new BigDecimal("0.00"), Arrays.asList(itemAttrDTO),
                                        Arrays.asList(new PackageSubitemDTO("subgroupGuid", "subgroupName")), "remark",
                                        "itemImg", false))))), Arrays.asList(
                        new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid", "userRecordGuid",
                                "operateGuid", false, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "remark", "tableGuid",
                                Arrays.asList(
                                        new DineItemDTO("523a7607-0fcd-4aac-aa90-e0f2909781cd", "itemGuid", "itemName",
                                                "code", 0, "skuGuid", "skuName", new BigDecimal("0.00"), "amountUnit",
                                                new BigDecimal("0.00"), new BigDecimal("0.00"), false,
                                                new BigDecimal("0.00"), new BigDecimal("0.00"),
                                                Arrays.asList(itemAttrDTO1),
                                                Arrays.asList(new PackageSubitemDTO("subgroupGuid", "subgroupName")),
                                                "remark", "itemImg", false)))), 0)),
                Arrays.asList(new SettlementItemDTO("itemName", new BigDecimal("0.00"), 0, 0)),
                Arrays.asList(appendFeeDetailDTO), new BigDecimal("0.00"), "couponId", false, 0, new BigDecimal("0.00"),
                false, "memberCardGuid", false, 0, "payWay", new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"), 0, new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"), false, 2, Lists.newArrayList());
        when(mockWxOrderRecordClientService.getOrderDetail(
                new WxOrderDetailReqDTO("c4ca4246-e927-4962-8311-940343f20b76", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(orderDetailDTO);

        // Configure WxOrderRecordClientService.detailCalculate(...).
        final ItemAttrDTO itemAttrDTO2 = new ItemAttrDTO();
        itemAttrDTO2.setGuid("5597aa19-a8db-4094-b817-bcc0042a1140");
        itemAttrDTO2.setAttrGuid("attrGuid");
        itemAttrDTO2.setAttrName("attrName");
        itemAttrDTO2.setAttrGroupName("attrGroupName");
        itemAttrDTO2.setAttrGroupGuid("attrGroupGuid");
        final ItemAttrDTO itemAttrDTO3 = new ItemAttrDTO();
        itemAttrDTO3.setGuid("5597aa19-a8db-4094-b817-bcc0042a1140");
        itemAttrDTO3.setAttrGuid("attrGuid");
        itemAttrDTO3.setAttrName("attrName");
        itemAttrDTO3.setAttrGroupName("attrGroupName");
        itemAttrDTO3.setAttrGroupGuid("attrGroupGuid");
        final AppendFeeDetailDTO appendFeeDetailDTO1 = new AppendFeeDetailDTO();
        appendFeeDetailDTO1.setName("name");
        appendFeeDetailDTO1.setCount(0);
        appendFeeDetailDTO1.setAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO1.setTotalAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO1.setType(0);
        final Result<OrderDetailDTO> orderDetailDTOResult = Result.buildSuccessResult(
                new OrderDetailDTO(0, "orderNo", "ca22cfdd-329e-49b1-8008-9d427760ba4f", "orderGuid", "mark", "remark",
                        "mergeInfo", "brandGuid", "brandName", "storeGuid", "storeName", "diningTableGuid",
                        "diningTableCode", "areaGuid", "areaName", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, Arrays.asList(
                        new OrderTableItemDTO("tableGuid", "tableCode", "areaName", "diningTableName", 0, 0, 0,
                                new HashSet<>(Arrays.asList(
                                        new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid",
                                                "userRecordGuid", "operateGuid", false,
                                                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "remark", "tableGuid",
                                                Arrays.asList(new DineItemDTO("523a7607-0fcd-4aac-aa90-e0f2909781cd",
                                                        "itemGuid", "itemName", "code", 0, "skuGuid", "skuName",
                                                        new BigDecimal("0.00"), "amountUnit", new BigDecimal("0.00"),
                                                        new BigDecimal("0.00"), false, new BigDecimal("0.00"),
                                                        new BigDecimal("0.00"), Arrays.asList(itemAttrDTO2),
                                                        Arrays.asList(
                                                                new PackageSubitemDTO("subgroupGuid", "subgroupName")),
                                                        "remark", "itemImg", false))))), Arrays.asList(
                                new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid",
                                        "userRecordGuid", "operateGuid", false, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                                        0, "remark", "tableGuid", Arrays.asList(
                                        new DineItemDTO("523a7607-0fcd-4aac-aa90-e0f2909781cd", "itemGuid", "itemName",
                                                "code", 0, "skuGuid", "skuName", new BigDecimal("0.00"), "amountUnit",
                                                new BigDecimal("0.00"), new BigDecimal("0.00"), false,
                                                new BigDecimal("0.00"), new BigDecimal("0.00"),
                                                Arrays.asList(itemAttrDTO3),
                                                Arrays.asList(new PackageSubitemDTO("subgroupGuid", "subgroupName")),
                                                "remark", "itemImg", false)))), 0)),
                        Arrays.asList(new SettlementItemDTO("itemName", new BigDecimal("0.00"), 0, 0)),
                        Arrays.asList(appendFeeDetailDTO1), new BigDecimal("0.00"), "couponId", false, 0,
                        new BigDecimal("0.00"), false, "memberCardGuid", false, 0, "payWay", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), 0, new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), false, 2, Lists.newArrayList()));
        when(mockWxOrderRecordClientService.detailCalculate(
                new WxOrderDetailReqDTO("c4ca4246-e927-4962-8311-940343f20b76", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(orderDetailDTOResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("/deal/order/detail/{guid}", "90f1713b-d204-494d-8c72-f66840f12652")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDetail_HsaBaseClientServiceReturnsNull() throws Exception {
        // Setup
        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        when(mockHsaBaseClientService.hasMemberPrice("memberCardGuid")).thenReturn(null);

        // Configure WxOrderRecordClientService.getOrderDetail(...).
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setGuid("5597aa19-a8db-4094-b817-bcc0042a1140");
        itemAttrDTO.setAttrGuid("attrGuid");
        itemAttrDTO.setAttrName("attrName");
        itemAttrDTO.setAttrGroupName("attrGroupName");
        itemAttrDTO.setAttrGroupGuid("attrGroupGuid");
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setGuid("5597aa19-a8db-4094-b817-bcc0042a1140");
        itemAttrDTO1.setAttrGuid("attrGuid");
        itemAttrDTO1.setAttrName("attrName");
        itemAttrDTO1.setAttrGroupName("attrGroupName");
        itemAttrDTO1.setAttrGroupGuid("attrGroupGuid");
        final AppendFeeDetailDTO appendFeeDetailDTO = new AppendFeeDetailDTO();
        appendFeeDetailDTO.setName("name");
        appendFeeDetailDTO.setCount(0);
        appendFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO.setTotalAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO.setType(0);
        final OrderDetailDTO orderDetailDTO = new OrderDetailDTO(0, "orderNo", "ca22cfdd-329e-49b1-8008-9d427760ba4f",
                "orderGuid", "mark", "remark", "mergeInfo", "brandGuid", "brandName", "storeGuid", "storeName",
                "diningTableGuid", "diningTableCode", "areaGuid", "areaName", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, Arrays.asList(
                new OrderTableItemDTO("tableGuid", "tableCode", "areaName", "diningTableName", 0, 0, 0, new HashSet<>(
                        Arrays.asList(new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid",
                                "userRecordGuid", "operateGuid", false, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                                "remark", "tableGuid", Arrays.asList(
                                new DineItemDTO("523a7607-0fcd-4aac-aa90-e0f2909781cd", "itemGuid", "itemName", "code",
                                        0, "skuGuid", "skuName", new BigDecimal("0.00"), "amountUnit",
                                        new BigDecimal("0.00"), new BigDecimal("0.00"), false, new BigDecimal("0.00"),
                                        new BigDecimal("0.00"), Arrays.asList(itemAttrDTO),
                                        Arrays.asList(new PackageSubitemDTO("subgroupGuid", "subgroupName")), "remark",
                                        "itemImg", false))))), Arrays.asList(
                        new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid", "userRecordGuid",
                                "operateGuid", false, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "remark", "tableGuid",
                                Arrays.asList(
                                        new DineItemDTO("523a7607-0fcd-4aac-aa90-e0f2909781cd", "itemGuid", "itemName",
                                                "code", 0, "skuGuid", "skuName", new BigDecimal("0.00"), "amountUnit",
                                                new BigDecimal("0.00"), new BigDecimal("0.00"), false,
                                                new BigDecimal("0.00"), new BigDecimal("0.00"),
                                                Arrays.asList(itemAttrDTO1),
                                                Arrays.asList(new PackageSubitemDTO("subgroupGuid", "subgroupName")),
                                                "remark", "itemImg", false)))), 0)),
                Arrays.asList(new SettlementItemDTO("itemName", new BigDecimal("0.00"), 0, 0)),
                Arrays.asList(appendFeeDetailDTO), new BigDecimal("0.00"), "couponId", false, 0, new BigDecimal("0.00"),
                false, "memberCardGuid", false, 0, "payWay", new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"), 0, new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"), false, 2, Lists.newArrayList());
        when(mockWxOrderRecordClientService.getOrderDetail(
                new WxOrderDetailReqDTO("c4ca4246-e927-4962-8311-940343f20b76", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(orderDetailDTO);

        // Configure WxOrderRecordClientService.detailCalculate(...).
        final ItemAttrDTO itemAttrDTO2 = new ItemAttrDTO();
        itemAttrDTO2.setGuid("5597aa19-a8db-4094-b817-bcc0042a1140");
        itemAttrDTO2.setAttrGuid("attrGuid");
        itemAttrDTO2.setAttrName("attrName");
        itemAttrDTO2.setAttrGroupName("attrGroupName");
        itemAttrDTO2.setAttrGroupGuid("attrGroupGuid");
        final ItemAttrDTO itemAttrDTO3 = new ItemAttrDTO();
        itemAttrDTO3.setGuid("5597aa19-a8db-4094-b817-bcc0042a1140");
        itemAttrDTO3.setAttrGuid("attrGuid");
        itemAttrDTO3.setAttrName("attrName");
        itemAttrDTO3.setAttrGroupName("attrGroupName");
        itemAttrDTO3.setAttrGroupGuid("attrGroupGuid");
        final AppendFeeDetailDTO appendFeeDetailDTO1 = new AppendFeeDetailDTO();
        appendFeeDetailDTO1.setName("name");
        appendFeeDetailDTO1.setCount(0);
        appendFeeDetailDTO1.setAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO1.setTotalAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO1.setType(0);
        final Result<OrderDetailDTO> orderDetailDTOResult = Result.buildSuccessResult(
                new OrderDetailDTO(0, "orderNo", "ca22cfdd-329e-49b1-8008-9d427760ba4f", "orderGuid", "mark", "remark",
                        "mergeInfo", "brandGuid", "brandName", "storeGuid", "storeName", "diningTableGuid",
                        "diningTableCode", "areaGuid", "areaName", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, Arrays.asList(
                        new OrderTableItemDTO("tableGuid", "tableCode", "areaName", "diningTableName", 0, 0, 0,
                                new HashSet<>(Arrays.asList(
                                        new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid",
                                                "userRecordGuid", "operateGuid", false,
                                                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "remark", "tableGuid",
                                                Arrays.asList(new DineItemDTO("523a7607-0fcd-4aac-aa90-e0f2909781cd",
                                                        "itemGuid", "itemName", "code", 0, "skuGuid", "skuName",
                                                        new BigDecimal("0.00"), "amountUnit", new BigDecimal("0.00"),
                                                        new BigDecimal("0.00"), false, new BigDecimal("0.00"),
                                                        new BigDecimal("0.00"), Arrays.asList(itemAttrDTO2),
                                                        Arrays.asList(
                                                                new PackageSubitemDTO("subgroupGuid", "subgroupName")),
                                                        "remark", "itemImg", false))))), Arrays.asList(
                                new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid",
                                        "userRecordGuid", "operateGuid", false, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                                        0, "remark", "tableGuid", Arrays.asList(
                                        new DineItemDTO("523a7607-0fcd-4aac-aa90-e0f2909781cd", "itemGuid", "itemName",
                                                "code", 0, "skuGuid", "skuName", new BigDecimal("0.00"), "amountUnit",
                                                new BigDecimal("0.00"), new BigDecimal("0.00"), false,
                                                new BigDecimal("0.00"), new BigDecimal("0.00"),
                                                Arrays.asList(itemAttrDTO3),
                                                Arrays.asList(new PackageSubitemDTO("subgroupGuid", "subgroupName")),
                                                "remark", "itemImg", false)))), 0)),
                        Arrays.asList(new SettlementItemDTO("itemName", new BigDecimal("0.00"), 0, 0)),
                        Arrays.asList(appendFeeDetailDTO1), new BigDecimal("0.00"), "couponId", false, 0,
                        new BigDecimal("0.00"), false, "memberCardGuid", false, 0, "payWay", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), 0, new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), false, 2, Lists.newArrayList()));
        when(mockWxOrderRecordClientService.detailCalculate(
                new WxOrderDetailReqDTO("c4ca4246-e927-4962-8311-940343f20b76", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(orderDetailDTOResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("/deal/order/detail/{guid}", "90f1713b-d204-494d-8c72-f66840f12652")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDetail_WxOrderRecordClientServiceGetOrderDetailReturnsNull() throws Exception {
        // Setup
        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        when(mockHsaBaseClientService.hasMemberPrice("memberCardGuid")).thenReturn(new ResponseModel<>(false));
        when(mockWxOrderRecordClientService.getOrderDetail(
                new WxOrderDetailReqDTO("c4ca4246-e927-4962-8311-940343f20b76", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(null);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("/deal/order/detail/{guid}", "90f1713b-d204-494d-8c72-f66840f12652")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("");
    }

    @Test
    public void testDetail_WxOrderRecordClientServiceGetOrderDetailReturnsError() throws Exception {
        // Setup
        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        when(mockHsaBaseClientService.hasMemberPrice("memberCardGuid")).thenReturn(new ResponseModel<>(false));

        // Configure WxOrderRecordClientService.getOrderDetail(...).
        final OrderDetailDTO orderDetailDTO = OrderDetailDTO.buildByError(ErrorCoderEnum.ORDER_CALCULATE_ERROR);
        when(mockWxOrderRecordClientService.getOrderDetail(
                new WxOrderDetailReqDTO("c4ca4246-e927-4962-8311-940343f20b76", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(orderDetailDTO);

        // Configure WxOrderRecordClientService.detailCalculate(...).
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setGuid("5597aa19-a8db-4094-b817-bcc0042a1140");
        itemAttrDTO.setAttrGuid("attrGuid");
        itemAttrDTO.setAttrName("attrName");
        itemAttrDTO.setAttrGroupName("attrGroupName");
        itemAttrDTO.setAttrGroupGuid("attrGroupGuid");
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setGuid("5597aa19-a8db-4094-b817-bcc0042a1140");
        itemAttrDTO1.setAttrGuid("attrGuid");
        itemAttrDTO1.setAttrName("attrName");
        itemAttrDTO1.setAttrGroupName("attrGroupName");
        itemAttrDTO1.setAttrGroupGuid("attrGroupGuid");
        final AppendFeeDetailDTO appendFeeDetailDTO = new AppendFeeDetailDTO();
        appendFeeDetailDTO.setName("name");
        appendFeeDetailDTO.setCount(0);
        appendFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO.setTotalAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO.setType(0);
        final Result<OrderDetailDTO> orderDetailDTOResult = Result.buildSuccessResult(
                new OrderDetailDTO(0, "orderNo", "ca22cfdd-329e-49b1-8008-9d427760ba4f", "orderGuid", "mark", "remark",
                        "mergeInfo", "brandGuid", "brandName", "storeGuid", "storeName", "diningTableGuid",
                        "diningTableCode", "areaGuid", "areaName", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, Arrays.asList(
                        new OrderTableItemDTO("tableGuid", "tableCode", "areaName", "diningTableName", 0, 0, 0,
                                new HashSet<>(Arrays.asList(
                                        new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid",
                                                "userRecordGuid", "operateGuid", false,
                                                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "remark", "tableGuid",
                                                Arrays.asList(new DineItemDTO("523a7607-0fcd-4aac-aa90-e0f2909781cd",
                                                        "itemGuid", "itemName", "code", 0, "skuGuid", "skuName",
                                                        new BigDecimal("0.00"), "amountUnit", new BigDecimal("0.00"),
                                                        new BigDecimal("0.00"), false, new BigDecimal("0.00"),
                                                        new BigDecimal("0.00"), Arrays.asList(itemAttrDTO),
                                                        Arrays.asList(
                                                                new PackageSubitemDTO("subgroupGuid", "subgroupName")),
                                                        "remark", "itemImg", false))))), Arrays.asList(
                                new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid",
                                        "userRecordGuid", "operateGuid", false, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                                        0, "remark", "tableGuid", Arrays.asList(
                                        new DineItemDTO("523a7607-0fcd-4aac-aa90-e0f2909781cd", "itemGuid", "itemName",
                                                "code", 0, "skuGuid", "skuName", new BigDecimal("0.00"), "amountUnit",
                                                new BigDecimal("0.00"), new BigDecimal("0.00"), false,
                                                new BigDecimal("0.00"), new BigDecimal("0.00"),
                                                Arrays.asList(itemAttrDTO1),
                                                Arrays.asList(new PackageSubitemDTO("subgroupGuid", "subgroupName")),
                                                "remark", "itemImg", false)))), 0)),
                        Arrays.asList(new SettlementItemDTO("itemName", new BigDecimal("0.00"), 0, 0)),
                        Arrays.asList(appendFeeDetailDTO), new BigDecimal("0.00"), "couponId", false, 0,
                        new BigDecimal("0.00"), false, "memberCardGuid", false, 0, "payWay", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), 0, new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), false, 2, Lists.newArrayList()));
        when(mockWxOrderRecordClientService.detailCalculate(
                new WxOrderDetailReqDTO("c4ca4246-e927-4962-8311-940343f20b76", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(orderDetailDTOResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("/deal/order/detail/{guid}", "90f1713b-d204-494d-8c72-f66840f12652")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDetail_WxOrderRecordClientServiceDetailCalculateReturnsNoItem() throws Exception {
        // Setup
        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        when(mockHsaBaseClientService.hasMemberPrice("memberCardGuid")).thenReturn(new ResponseModel<>(false));

        // Configure WxOrderRecordClientService.getOrderDetail(...).
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setGuid("5597aa19-a8db-4094-b817-bcc0042a1140");
        itemAttrDTO.setAttrGuid("attrGuid");
        itemAttrDTO.setAttrName("attrName");
        itemAttrDTO.setAttrGroupName("attrGroupName");
        itemAttrDTO.setAttrGroupGuid("attrGroupGuid");
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setGuid("5597aa19-a8db-4094-b817-bcc0042a1140");
        itemAttrDTO1.setAttrGuid("attrGuid");
        itemAttrDTO1.setAttrName("attrName");
        itemAttrDTO1.setAttrGroupName("attrGroupName");
        itemAttrDTO1.setAttrGroupGuid("attrGroupGuid");
        final AppendFeeDetailDTO appendFeeDetailDTO = new AppendFeeDetailDTO();
        appendFeeDetailDTO.setName("name");
        appendFeeDetailDTO.setCount(0);
        appendFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO.setTotalAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO.setType(0);
        final OrderDetailDTO orderDetailDTO = new OrderDetailDTO(0, "orderNo", "ca22cfdd-329e-49b1-8008-9d427760ba4f",
                "orderGuid", "mark", "remark", "mergeInfo", "brandGuid", "brandName", "storeGuid", "storeName",
                "diningTableGuid", "diningTableCode", "areaGuid", "areaName", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, Arrays.asList(
                new OrderTableItemDTO("tableGuid", "tableCode", "areaName", "diningTableName", 0, 0, 0, new HashSet<>(
                        Arrays.asList(new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid",
                                "userRecordGuid", "operateGuid", false, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                                "remark", "tableGuid", Arrays.asList(
                                new DineItemDTO("523a7607-0fcd-4aac-aa90-e0f2909781cd", "itemGuid", "itemName", "code",
                                        0, "skuGuid", "skuName", new BigDecimal("0.00"), "amountUnit",
                                        new BigDecimal("0.00"), new BigDecimal("0.00"), false, new BigDecimal("0.00"),
                                        new BigDecimal("0.00"), Arrays.asList(itemAttrDTO),
                                        Arrays.asList(new PackageSubitemDTO("subgroupGuid", "subgroupName")), "remark",
                                        "itemImg", false))))), Arrays.asList(
                        new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid", "userRecordGuid",
                                "operateGuid", false, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "remark", "tableGuid",
                                Arrays.asList(
                                        new DineItemDTO("523a7607-0fcd-4aac-aa90-e0f2909781cd", "itemGuid", "itemName",
                                                "code", 0, "skuGuid", "skuName", new BigDecimal("0.00"), "amountUnit",
                                                new BigDecimal("0.00"), new BigDecimal("0.00"), false,
                                                new BigDecimal("0.00"), new BigDecimal("0.00"),
                                                Arrays.asList(itemAttrDTO1),
                                                Arrays.asList(new PackageSubitemDTO("subgroupGuid", "subgroupName")),
                                                "remark", "itemImg", false)))), 0)),
                Arrays.asList(new SettlementItemDTO("itemName", new BigDecimal("0.00"), 0, 0)),
                Arrays.asList(appendFeeDetailDTO), new BigDecimal("0.00"), "couponId", false, 0, new BigDecimal("0.00"),
                false, "memberCardGuid", false, 0, "payWay", new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"), 0, new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"), false, 2, Lists.newArrayList());
        when(mockWxOrderRecordClientService.getOrderDetail(
                new WxOrderDetailReqDTO("c4ca4246-e927-4962-8311-940343f20b76", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(orderDetailDTO);

        when(mockWxOrderRecordClientService.detailCalculate(
                new WxOrderDetailReqDTO("c4ca4246-e927-4962-8311-940343f20b76", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(Result.buildEmptySuccess());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("/deal/order/detail/{guid}", "90f1713b-d204-494d-8c72-f66840f12652")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDetail_WxOrderRecordClientServiceDetailCalculateReturnsFailure() throws Exception {
        // Setup
        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        when(mockHsaBaseClientService.hasMemberPrice("memberCardGuid")).thenReturn(new ResponseModel<>(false));

        // Configure WxOrderRecordClientService.getOrderDetail(...).
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setGuid("5597aa19-a8db-4094-b817-bcc0042a1140");
        itemAttrDTO.setAttrGuid("attrGuid");
        itemAttrDTO.setAttrName("attrName");
        itemAttrDTO.setAttrGroupName("attrGroupName");
        itemAttrDTO.setAttrGroupGuid("attrGroupGuid");
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setGuid("5597aa19-a8db-4094-b817-bcc0042a1140");
        itemAttrDTO1.setAttrGuid("attrGuid");
        itemAttrDTO1.setAttrName("attrName");
        itemAttrDTO1.setAttrGroupName("attrGroupName");
        itemAttrDTO1.setAttrGroupGuid("attrGroupGuid");
        final AppendFeeDetailDTO appendFeeDetailDTO = new AppendFeeDetailDTO();
        appendFeeDetailDTO.setName("name");
        appendFeeDetailDTO.setCount(0);
        appendFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO.setTotalAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO.setType(0);
        final OrderDetailDTO orderDetailDTO = new OrderDetailDTO(0, "orderNo", "ca22cfdd-329e-49b1-8008-9d427760ba4f",
                "orderGuid", "mark", "remark", "mergeInfo", "brandGuid", "brandName", "storeGuid", "storeName",
                "diningTableGuid", "diningTableCode", "areaGuid", "areaName", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, Arrays.asList(
                new OrderTableItemDTO("tableGuid", "tableCode", "areaName", "diningTableName", 0, 0, 0, new HashSet<>(
                        Arrays.asList(new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid",
                                "userRecordGuid", "operateGuid", false, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0,
                                "remark", "tableGuid", Arrays.asList(
                                new DineItemDTO("523a7607-0fcd-4aac-aa90-e0f2909781cd", "itemGuid", "itemName", "code",
                                        0, "skuGuid", "skuName", new BigDecimal("0.00"), "amountUnit",
                                        new BigDecimal("0.00"), new BigDecimal("0.00"), false, new BigDecimal("0.00"),
                                        new BigDecimal("0.00"), Arrays.asList(itemAttrDTO),
                                        Arrays.asList(new PackageSubitemDTO("subgroupGuid", "subgroupName")), "remark",
                                        "itemImg", false))))), Arrays.asList(
                        new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid", "userRecordGuid",
                                "operateGuid", false, LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "remark", "tableGuid",
                                Arrays.asList(
                                        new DineItemDTO("523a7607-0fcd-4aac-aa90-e0f2909781cd", "itemGuid", "itemName",
                                                "code", 0, "skuGuid", "skuName", new BigDecimal("0.00"), "amountUnit",
                                                new BigDecimal("0.00"), new BigDecimal("0.00"), false,
                                                new BigDecimal("0.00"), new BigDecimal("0.00"),
                                                Arrays.asList(itemAttrDTO1),
                                                Arrays.asList(new PackageSubitemDTO("subgroupGuid", "subgroupName")),
                                                "remark", "itemImg", false)))), 0)),
                Arrays.asList(new SettlementItemDTO("itemName", new BigDecimal("0.00"), 0, 0)),
                Arrays.asList(appendFeeDetailDTO), new BigDecimal("0.00"), "couponId", false, 0, new BigDecimal("0.00"),
                false, "memberCardGuid", false, 0, "payWay", new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"), 0, new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"), false, 2, Lists.newArrayList());
        when(mockWxOrderRecordClientService.getOrderDetail(
                new WxOrderDetailReqDTO("c4ca4246-e927-4962-8311-940343f20b76", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(orderDetailDTO);

        // Configure WxOrderRecordClientService.detailCalculate(...).
        final Result<OrderDetailDTO> orderDetailDTOResult = Result.buildOpFailedResult(new Exception("message"));
        when(mockWxOrderRecordClientService.detailCalculate(
                new WxOrderDetailReqDTO("c4ca4246-e927-4962-8311-940343f20b76", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(orderDetailDTOResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("/deal/order/detail/{guid}", "90f1713b-d204-494d-8c72-f66840f12652")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetOrderGift() throws Exception {
        // Setup
        // Configure MemberMarketingClientService.getOrderGift(...).
        final ConsumptionGiftDetailDTO consumptionGiftDetailDTO = new ConsumptionGiftDetailDTO();
        consumptionGiftDetailDTO.setMemberGuid("memberGuid");
        consumptionGiftDetailDTO.setOrderAmount(new BigDecimal("0.00"));
        consumptionGiftDetailDTO.setOrderNum("orderNum");
        consumptionGiftDetailDTO.setActivityGuid("activityGuid");
        consumptionGiftDetailDTO.setGiftType(0);
        when(mockMemberMarketingClientService.getOrderGift("orderNumber")).thenReturn(consumptionGiftDetailDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/deal/order/get/order/gift")
                        .param("orderNumber", "orderNumber")
                        .param("orderHolderNo", "orderHolderNo")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDetailSettement() throws Exception {
        // Setup
        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        when(mockHsaBaseClientService.hasMemberPrice("memberCardGuid")).thenReturn(new ResponseModel<>(false));

        // Configure WxOrderRecordClientService.detailCalculate(...).
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setGuid("5597aa19-a8db-4094-b817-bcc0042a1140");
        itemAttrDTO.setAttrGuid("attrGuid");
        itemAttrDTO.setAttrName("attrName");
        itemAttrDTO.setAttrGroupName("attrGroupName");
        itemAttrDTO.setAttrGroupGuid("attrGroupGuid");
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setGuid("5597aa19-a8db-4094-b817-bcc0042a1140");
        itemAttrDTO1.setAttrGuid("attrGuid");
        itemAttrDTO1.setAttrName("attrName");
        itemAttrDTO1.setAttrGroupName("attrGroupName");
        itemAttrDTO1.setAttrGroupGuid("attrGroupGuid");
        final AppendFeeDetailDTO appendFeeDetailDTO = new AppendFeeDetailDTO();
        appendFeeDetailDTO.setName("name");
        appendFeeDetailDTO.setCount(0);
        appendFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO.setTotalAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO.setType(0);
        final Result<OrderDetailDTO> orderDetailDTOResult = Result.buildSuccessResult(
                new OrderDetailDTO(0, "orderNo", "ca22cfdd-329e-49b1-8008-9d427760ba4f", "orderGuid", "mark", "remark",
                        "mergeInfo", "brandGuid", "brandName", "storeGuid", "storeName", "diningTableGuid",
                        "diningTableCode", "areaGuid", "areaName", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, Arrays.asList(
                        new OrderTableItemDTO("tableGuid", "tableCode", "areaName", "diningTableName", 0, 0, 0,
                                new HashSet<>(Arrays.asList(
                                        new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid",
                                                "userRecordGuid", "operateGuid", false,
                                                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "remark", "tableGuid",
                                                Arrays.asList(new DineItemDTO("523a7607-0fcd-4aac-aa90-e0f2909781cd",
                                                        "itemGuid", "itemName", "code", 0, "skuGuid", "skuName",
                                                        new BigDecimal("0.00"), "amountUnit", new BigDecimal("0.00"),
                                                        new BigDecimal("0.00"), false, new BigDecimal("0.00"),
                                                        new BigDecimal("0.00"), Arrays.asList(itemAttrDTO),
                                                        Arrays.asList(
                                                                new PackageSubitemDTO("subgroupGuid", "subgroupName")),
                                                        "remark", "itemImg", false))))), Arrays.asList(
                                new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid",
                                        "userRecordGuid", "operateGuid", false, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                                        0, "remark", "tableGuid", Arrays.asList(
                                        new DineItemDTO("523a7607-0fcd-4aac-aa90-e0f2909781cd", "itemGuid", "itemName",
                                                "code", 0, "skuGuid", "skuName", new BigDecimal("0.00"), "amountUnit",
                                                new BigDecimal("0.00"), new BigDecimal("0.00"), false,
                                                new BigDecimal("0.00"), new BigDecimal("0.00"),
                                                Arrays.asList(itemAttrDTO1),
                                                Arrays.asList(new PackageSubitemDTO("subgroupGuid", "subgroupName")),
                                                "remark", "itemImg", false)))), 0)),
                        Arrays.asList(new SettlementItemDTO("itemName", new BigDecimal("0.00"), 0, 0)),
                        Arrays.asList(appendFeeDetailDTO), new BigDecimal("0.00"), "couponId", false, 0,
                        new BigDecimal("0.00"), false, "memberCardGuid", false, 0, "payWay", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), 0, new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), false, 2, Lists.newArrayList()));
        when(mockWxOrderRecordClientService.detailCalculate(
                new WxOrderDetailReqDTO("c4ca4246-e927-4962-8311-940343f20b76", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(orderDetailDTOResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("/deal/order/detail/settlement/{guid}", "90f1713b-d204-494d-8c72-f66840f12652")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDetailSettement_UserMemberSessionUtilsReturnsNull() throws Exception {
        // Setup
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(null);
        when(mockHsaBaseClientService.hasMemberPrice("memberCardGuid")).thenReturn(new ResponseModel<>(false));

        // Configure WxOrderRecordClientService.detailCalculate(...).
        final ItemAttrDTO itemAttrDTO = new ItemAttrDTO();
        itemAttrDTO.setGuid("5597aa19-a8db-4094-b817-bcc0042a1140");
        itemAttrDTO.setAttrGuid("attrGuid");
        itemAttrDTO.setAttrName("attrName");
        itemAttrDTO.setAttrGroupName("attrGroupName");
        itemAttrDTO.setAttrGroupGuid("attrGroupGuid");
        final ItemAttrDTO itemAttrDTO1 = new ItemAttrDTO();
        itemAttrDTO1.setGuid("5597aa19-a8db-4094-b817-bcc0042a1140");
        itemAttrDTO1.setAttrGuid("attrGuid");
        itemAttrDTO1.setAttrName("attrName");
        itemAttrDTO1.setAttrGroupName("attrGroupName");
        itemAttrDTO1.setAttrGroupGuid("attrGroupGuid");
        final AppendFeeDetailDTO appendFeeDetailDTO = new AppendFeeDetailDTO();
        appendFeeDetailDTO.setName("name");
        appendFeeDetailDTO.setCount(0);
        appendFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO.setTotalAmount(new BigDecimal("0.00"));
        appendFeeDetailDTO.setType(0);
        final Result<OrderDetailDTO> orderDetailDTOResult = Result.buildSuccessResult(
                new OrderDetailDTO(0, "orderNo", "ca22cfdd-329e-49b1-8008-9d427760ba4f", "orderGuid", "mark", "remark",
                        "mergeInfo", "brandGuid", "brandName", "storeGuid", "storeName", "diningTableGuid",
                        "diningTableCode", "areaGuid", "areaName", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, 0, Arrays.asList(
                        new OrderTableItemDTO("tableGuid", "tableCode", "areaName", "diningTableName", 0, 0, 0,
                                new HashSet<>(Arrays.asList(
                                        new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid",
                                                "userRecordGuid", "operateGuid", false,
                                                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "remark", "tableGuid",
                                                Arrays.asList(new DineItemDTO("523a7607-0fcd-4aac-aa90-e0f2909781cd",
                                                        "itemGuid", "itemName", "code", 0, "skuGuid", "skuName",
                                                        new BigDecimal("0.00"), "amountUnit", new BigDecimal("0.00"),
                                                        new BigDecimal("0.00"), false, new BigDecimal("0.00"),
                                                        new BigDecimal("0.00"), Arrays.asList(itemAttrDTO),
                                                        Arrays.asList(
                                                                new PackageSubitemDTO("subgroupGuid", "subgroupName")),
                                                        "remark", "itemImg", false))))), Arrays.asList(
                                new OrderBatchItemDTO("batchCode", "nickname", "headPortrait", "openid",
                                        "userRecordGuid", "operateGuid", false, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                                        0, "remark", "tableGuid", Arrays.asList(
                                        new DineItemDTO("523a7607-0fcd-4aac-aa90-e0f2909781cd", "itemGuid", "itemName",
                                                "code", 0, "skuGuid", "skuName", new BigDecimal("0.00"), "amountUnit",
                                                new BigDecimal("0.00"), new BigDecimal("0.00"), false,
                                                new BigDecimal("0.00"), new BigDecimal("0.00"),
                                                Arrays.asList(itemAttrDTO1),
                                                Arrays.asList(new PackageSubitemDTO("subgroupGuid", "subgroupName")),
                                                "remark", "itemImg", false)))), 0)),
                        Arrays.asList(new SettlementItemDTO("itemName", new BigDecimal("0.00"), 0, 0)),
                        Arrays.asList(appendFeeDetailDTO), new BigDecimal("0.00"), "couponId", false, 0,
                        new BigDecimal("0.00"), false, "memberCardGuid", false, 0, "payWay", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), 0, new BigDecimal("0.00"),
                        new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), false, 2, Lists.newArrayList()));
        when(mockWxOrderRecordClientService.detailCalculate(
                new WxOrderDetailReqDTO("c4ca4246-e927-4962-8311-940343f20b76", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(orderDetailDTOResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("/deal/order/detail/settlement/{guid}", "90f1713b-d204-494d-8c72-f66840f12652")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDetailSettement_WxOrderRecordClientServiceReturnsNoItem() throws Exception {
        // Setup
        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        when(mockHsaBaseClientService.hasMemberPrice("memberCardGuid")).thenReturn(new ResponseModel<>(false));
        when(mockWxOrderRecordClientService.detailCalculate(
                new WxOrderDetailReqDTO("c4ca4246-e927-4962-8311-940343f20b76", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(Result.buildEmptySuccess());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("/deal/order/detail/settlement/{guid}", "90f1713b-d204-494d-8c72-f66840f12652")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("");
    }

    @Test
    public void testDetailSettement_WxOrderRecordClientServiceReturnsFailure() throws Exception {
        // Setup
        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        when(mockHsaBaseClientService.hasMemberPrice("memberCardGuid")).thenReturn(new ResponseModel<>(false));

        // Configure WxOrderRecordClientService.detailCalculate(...).
        final Result<OrderDetailDTO> orderDetailDTOResult = Result.buildOpFailedResult(new Exception("message"));
        when(mockWxOrderRecordClientService.detailCalculate(
                new WxOrderDetailReqDTO("c4ca4246-e927-4962-8311-940343f20b76", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(orderDetailDTOResult);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("/deal/order/detail/settlement/{guid}", "90f1713b-d204-494d-8c72-f66840f12652")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDetailCalculateCheck() throws Exception {
        // Setup
        when(mockWxOrderRecordClientService.detailCalculateCheck(
                new WxOrderDetailReqDTO("c4ca4246-e927-4962-8311-940343f20b76", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(Result.buildSuccessResult(null));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("/deal/order/settlement/check/{guid}", "90f1713b-d204-494d-8c72-f66840f12652")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testDetailCalculateCheck_WxOrderRecordClientServiceReturnsNoItem() throws Exception {
        // Setup
        when(mockWxOrderRecordClientService.detailCalculateCheck(
                new WxOrderDetailReqDTO("c4ca4246-e927-4962-8311-940343f20b76", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(Result.buildEmptySuccess());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("/deal/order/settlement/check/{guid}", "90f1713b-d204-494d-8c72-f66840f12652")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("");
    }

    @Test
    public void testDetailCalculateCheck_WxOrderRecordClientServiceReturnsFailure() throws Exception {
        // Setup
        when(mockWxOrderRecordClientService.detailCalculateCheck(
                new WxOrderDetailReqDTO("c4ca4246-e927-4962-8311-940343f20b76", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(Result.buildOpFailedResult(new Exception("message")));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(
                        get("/deal/order/settlement/check/{guid}", "90f1713b-d204-494d-8c72-f66840f12652")
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testFindByOrderGuid() throws Exception {
        // Setup
        // Configure TradeOrderService.findByOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO("d8ce280d-9b59-42c4-89f6-7a79f485fdd3",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, "orderNo", 0, 0, 0,
                LocalDate.of(2020, 1, 1), "diningTableGuid", "diningTableName", 0, "cancelReason", "remark", "mark", 0,
                new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"),
                new BigDecimal("0.00"), new BigDecimal("0.00"), 0, 0, 0, "mainOrderGuid", "memberGuid",
                "memberCardGuid", "memberConsumptionGuid", "memberPhone", "memberName", "userWxPublicOpenId", "qrcode",
                0L, 0L, 0, "recoveryReason", 0, 0, 0, "storeGuid", "storeName", "storeContactTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "createStaffGuid",
                "createStaffName", "recoveryStaffGuid", "recoveryStaffName", "checkoutStaffGuid", "checkoutStaffName",
                "cancelStaffGuid", "cancelStaffName", 0, "localUploadId", "paymentAppId");
        when(mockTradeOrderService.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/deal/order/find_by_order_guid")
                        .param("orderGuid", "orderGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetDetailByOrderRecordGuid() throws Exception {
        // Setup
        // Configure WxStoreMerchantOrderClientService.getDetailByOrderRecordGuid(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("6404870f-a80b-4e8a-a88c-b1ae95e0f7fe");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS = Arrays.asList(wxStoreMerchantOrderDTO);
        when(mockMerchantOrderClientService.getDetailByOrderRecordGuid("orderRecordGuid"))
                .thenReturn(wxStoreMerchantOrderDTOS);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/deal/order/get_detail_by_order_record_guid")
                        .param("orderRecordGuid", "orderRecordGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetDetailByOrderRecordGuid_WxStoreMerchantOrderClientServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockMerchantOrderClientService.getDetailByOrderRecordGuid("orderRecordGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/deal/order/get_detail_by_order_record_guid")
                        .param("orderRecordGuid", "orderRecordGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testGetWechatOrderFeeByOrderGuid() throws Exception {
        // Setup
        when(mockMerchantOrderClientService.getWechatOrderFeeByOrderGuid("orderGuid"))
                .thenReturn(new BigDecimal("0.00"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/deal/order/get_wechat_order_fee_by_order_guid")
                        .param("orderGuid", "orderGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetWechatOrderInfoByOrderGuid() throws Exception {
        // Setup
        // Configure WxStoreMerchantOrderClientService.getWechatOrderInfoByOrderGuid(...).
        final WechatOrderInfoDTO wechatOrderInfoDTO = new WechatOrderInfoDTO();
        wechatOrderInfoDTO.setWechatOrderFee(new BigDecimal("0.00"));
        wechatOrderInfoDTO.setOrderState(0);
        wechatOrderInfoDTO.setOrderSource(0);
        wechatOrderInfoDTO.setOrderGuid("orderGuid");
        when(mockMerchantOrderClientService.getWechatOrderInfoByOrderGuid("orderGuid")).thenReturn(wechatOrderInfoDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/deal/order/get_wechat_order_info_by_order_guid")
                        .param("orderGuid", "orderGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetWechatOrderInfoByGuid() throws Exception {
        // Setup
        // Configure WxStoreMerchantOrderClientService.getWechatOrderInfoByGuid(...).
        final WechatOrderInfoDTO wechatOrderInfoDTO = new WechatOrderInfoDTO();
        wechatOrderInfoDTO.setWechatOrderFee(new BigDecimal("0.00"));
        wechatOrderInfoDTO.setOrderState(0);
        wechatOrderInfoDTO.setOrderSource(0);
        wechatOrderInfoDTO.setOrderGuid("orderGuid");
        when(mockMerchantOrderClientService.getWechatOrderInfoByGuid(
                "0bfd620d-ee38-4607-b365-ef36f496ae81")).thenReturn(wechatOrderInfoDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/deal/order/get_wechat_order_info_by_guid")
                        .param("guid", "0bfd620d-ee38-4607-b365-ef36f496ae81")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
