package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.account.NewMemberInfoClientService;
import com.holderzone.saas.store.dto.common.BaseRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MemberServiceImplTest {

    @Mock
    private NewMemberInfoClientService mockMemberInfoClientService;

    private MemberServiceImpl memberServiceImplUnderTest;

    @Before
    public void setUp() {
        memberServiceImplUnderTest = new MemberServiceImpl(mockMemberInfoClientService);
    }

    @Test
    public void testQueryMemberUseIntegral() {
        // Setup
        final BaseRespDTO expectedResult = new BaseRespDTO("returnGuid", 0, "promptMsg");

        // Configure NewMemberInfoClientService.queryDeductionRule(...).
        final BaseRespDTO baseRespDTO = new BaseRespDTO("returnGuid", 0, "promptMsg");
        when(mockMemberInfoClientService.queryDeductionRule("operSubjectGuid")).thenReturn(baseRespDTO);

        // Run the test
        final BaseRespDTO result = memberServiceImplUnderTest.queryMemberUseIntegral("operSubjectGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
