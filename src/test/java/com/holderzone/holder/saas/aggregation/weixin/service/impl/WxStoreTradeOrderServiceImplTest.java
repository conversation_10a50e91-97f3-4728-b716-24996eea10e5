package com.holderzone.holder.saas.aggregation.weixin.service.impl;

import com.holderzone.holder.saas.aggregation.weixin.service.rpc.BusinessClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.StoreOrganizationClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.TradeOrderService;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.business.manage.SurchargeConditionQuery;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStoreTradeOrderServiceImplTest {

    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private BusinessClientService mockBusinessClientService;
    @Mock
    private StoreOrganizationClientService storeOrganizationClientService;
    @Mock
    private TradeOrderService tradeOrderService;

    private WxStoreTradeOrderServiceImpl wxStoreTradeOrderServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxStoreTradeOrderServiceImplUnderTest = new WxStoreTradeOrderServiceImpl(mockRedisUtils,
                mockBusinessClientService, storeOrganizationClientService, tradeOrderService);
    }

    @Test
    public void testUpdateFastGuestCount() {
        // Setup
        // Run the test
        wxStoreTradeOrderServiceImplUnderTest.updateFastGuestCount(0);

        // Verify the results
        verify(mockRedisUtils).setEx("key", 0, 48L, TimeUnit.HOURS);
    }

    @Test
    public void testRemoveFastGuestCount() {
        // Setup
        // Run the test
        wxStoreTradeOrderServiceImplUnderTest.removeFastGuestCount("diningTableGuid", "openId");

        // Verify the results
        verify(mockRedisUtils).delete("key");
    }

    @Test
    public void testGetFastGuestCount() {
        // Setup
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final Integer result = wxStoreTradeOrderServiceImplUnderTest.getFastGuestCount("diningTableGuid", "openId");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testFilterTimeLimitTableSurchargeList() {
        // Setup
        final List<SurchargeLinkDTO> surchargeLinkList = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        final List<SurchargeLinkDTO> expectedResult = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        when(mockRedisUtils.hasKey("key")).thenReturn(false);

        // Run the test
        final List<SurchargeLinkDTO> result = wxStoreTradeOrderServiceImplUnderTest.filterTimeLimitTableSurchargeList(
                "tableGuid", surchargeLinkList);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFilterTimeLimitTableSurchargeList_RedisUtilsReturnsTrue() {
        // Setup
        final List<SurchargeLinkDTO> surchargeLinkList = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        when(mockRedisUtils.hasKey("key")).thenReturn(true);

        // Run the test
        final List<SurchargeLinkDTO> result = wxStoreTradeOrderServiceImplUnderTest.filterTimeLimitTableSurchargeList(
                "tableGuid", surchargeLinkList);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQuerySurchargeList() {
        // Setup
        final List<SurchargeLinkDTO> expectedResult = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));

        // Configure BusinessClientService.listSurchargeByCondition(...).
        final List<SurchargeLinkDTO> surchargeLinkDTOS = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        final SurchargeConditionQuery query = new SurchargeConditionQuery();
        query.setStoreGuid("storeGuid");
        query.setType(0);
        query.setTradeMode(0);
        query.setAreaGuid("areaGuid");
        when(mockBusinessClientService.listSurchargeByCondition(query)).thenReturn(surchargeLinkDTOS);

        when(mockRedisUtils.hasKey("key")).thenReturn(false);

        // Run the test
        final List<SurchargeLinkDTO> result = wxStoreTradeOrderServiceImplUnderTest.querySurchargeList(0, "areaGuid",
                "diningTableGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuerySurchargeList_BusinessClientServiceReturnsNoItems() {
        // Setup
        final List<SurchargeLinkDTO> expectedResult = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));

        // Configure BusinessClientService.listSurchargeByCondition(...).
        final SurchargeConditionQuery query = new SurchargeConditionQuery();
        query.setStoreGuid("storeGuid");
        query.setType(0);
        query.setTradeMode(0);
        query.setAreaGuid("areaGuid");
        when(mockBusinessClientService.listSurchargeByCondition(query)).thenReturn(Collections.emptyList());

        when(mockRedisUtils.hasKey("key")).thenReturn(false);

        // Run the test
        final List<SurchargeLinkDTO> result = wxStoreTradeOrderServiceImplUnderTest.querySurchargeList(0, "areaGuid",
                "diningTableGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuerySurchargeList_RedisUtilsReturnsTrue() {
        // Setup
        // Configure BusinessClientService.listSurchargeByCondition(...).
        final List<SurchargeLinkDTO> surchargeLinkDTOS = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        final SurchargeConditionQuery query = new SurchargeConditionQuery();
        query.setStoreGuid("storeGuid");
        query.setType(0);
        query.setTradeMode(0);
        query.setAreaGuid("areaGuid");
        when(mockBusinessClientService.listSurchargeByCondition(query)).thenReturn(surchargeLinkDTOS);

        when(mockRedisUtils.hasKey("key")).thenReturn(true);

        // Run the test
        final List<SurchargeLinkDTO> result = wxStoreTradeOrderServiceImplUnderTest.querySurchargeList(0, "areaGuid",
                "diningTableGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testTransformSurchargeCache() {
        // Setup
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        wxStoreTradeOrderServiceImplUnderTest.transformSurchargeCache("orderGuid");

        // Verify the results
        verify(mockRedisUtils).setEx("key", "value", 0L, TimeUnit.MINUTES);
    }

    @Test
    public void testSaveAddItemBeforeSurcharges() {
        // Setup
        final List<SurchargeLinkDTO> surchargeLinkList = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));

        // Run the test
        wxStoreTradeOrderServiceImplUnderTest.saveAddItemBeforeSurcharges(surchargeLinkList);

        // Verify the results
        verify(mockRedisUtils).setEx("key", "value", 12L, TimeUnit.HOURS);
    }

    @Test
    public void testQueryAddItemBeforeSurcharges() {
        // Setup
        final List<SurchargeLinkDTO> expectedResult = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final List<SurchargeLinkDTO> result = wxStoreTradeOrderServiceImplUnderTest.queryAddItemBeforeSurcharges();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuerySurchargeListByRedis() {
        // Setup
        final List<SurchargeLinkDTO> expectedResult = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final List<SurchargeLinkDTO> result = wxStoreTradeOrderServiceImplUnderTest.querySurchargeListByRedis(
                "tableGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
