package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.holderzone.holder.saas.aggregation.app.config.WeChatConfig;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.account.NewMemberInfoClientService;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberInfoDTO;
import com.holderzone.saas.store.dto.common.RedisReqDTO;
import com.holderzone.saas.store.dto.member.response.PadLoginMemberRespDTO;
import com.holderzone.saas.store.dto.weixin.auth.WeChatAuthParamDTO;
import com.holderzone.saas.store.dto.weixin.auth.WeChatUserInfoDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WeChatServiceImplTest {

    @Mock
    private NewMemberInfoClientService mockMemberInfoClientService;
    @Mock
    private WeChatConfig mockWeChatConfig;

    private WeChatServiceImpl weChatServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        weChatServiceImplUnderTest = new WeChatServiceImpl(mockMemberInfoClientService);
        ReflectionTestUtils.setField(weChatServiceImplUnderTest, "weChatConfig", mockWeChatConfig);
    }

    @Test
    public void testGetWeChatAuthParam() {
        // Setup
        final WeChatAuthParamDTO expectedResult = new WeChatAuthParamDTO();
        expectedResult.setAppId("appId");
        expectedResult.setScope("scope");
        expectedResult.setNoncestr("noncestr");
        expectedResult.setTimestamp("timestamp");
        expectedResult.setSignature("signature");

        when(mockWeChatConfig.getAppId()).thenReturn("result");
        when(mockWeChatConfig.getSecret()).thenReturn("result");
        when(mockWeChatConfig.getAccessTokenUrl()).thenReturn("result");
        when(mockWeChatConfig.getTicketUrl()).thenReturn("result");
        when(mockWeChatConfig.getScope()).thenReturn("scope");

        // Run the test
        final WeChatAuthParamDTO result = weChatServiceImplUnderTest.getWeChatAuthParam();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetWechatUserInfo() {
        // Setup
        final PadLoginMemberRespDTO expectedResult = new PadLoginMemberRespDTO();
        expectedResult.setLoginState(0);
        final ResponseMemberAndCardInfoDTO memberAndCardInfoDTO = new ResponseMemberAndCardInfoDTO();
        final ResponseMemberInfoDTO memberInfoDTO = new ResponseMemberInfoDTO();
        memberInfoDTO.setMemberQRCode("memberQRCode");
        memberInfoDTO.setMemberInfoGuid("memberInfoGuid");
        memberAndCardInfoDTO.setMemberInfoDTO(memberInfoDTO);
        expectedResult.setMemberAndCardInfoDTO(memberAndCardInfoDTO);

        when(mockWeChatConfig.getAppId()).thenReturn("result");
        when(mockWeChatConfig.getSecret()).thenReturn("result");
        when(mockWeChatConfig.getAccessTokenUrl2()).thenReturn("result");
        when(mockWeChatConfig.getUserInfoUrl()).thenReturn("result");

        // Configure NewMemberInfoClientService.getMemberInfoByUnionId(...).
        final PadLoginMemberRespDTO padLoginMemberRespDTO = new PadLoginMemberRespDTO();
        padLoginMemberRespDTO.setLoginState(0);
        final ResponseMemberAndCardInfoDTO memberAndCardInfoDTO1 = new ResponseMemberAndCardInfoDTO();
        final ResponseMemberInfoDTO memberInfoDTO1 = new ResponseMemberInfoDTO();
        memberInfoDTO1.setMemberQRCode("memberQRCode");
        memberInfoDTO1.setMemberInfoGuid("memberInfoGuid");
        memberAndCardInfoDTO1.setMemberInfoDTO(memberInfoDTO1);
        padLoginMemberRespDTO.setMemberAndCardInfoDTO(memberAndCardInfoDTO1);
        final WeChatUserInfoDTO weChatUserInfoDTO = new WeChatUserInfoDTO();
        weChatUserInfoDTO.setOpenid("openid");
        weChatUserInfoDTO.setNickname("nickname");
        weChatUserInfoDTO.setSex("sex");
        weChatUserInfoDTO.setLanguage("language");
        weChatUserInfoDTO.setCity("city");
        when(mockMemberInfoClientService.getMemberInfoByUnionId(weChatUserInfoDTO)).thenReturn(padLoginMemberRespDTO);

        // Run the test
        final PadLoginMemberRespDTO result = weChatServiceImplUnderTest.getWechatUserInfo("authCode");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm NewMemberInfoClientService.putWeChatUserInfoToRedis(...).
        final RedisReqDTO redisReqDTO = new RedisReqDTO();
        redisReqDTO.setRedisValue("redisValue");
        redisReqDTO.setRedisKey("redisKey");
        redisReqDTO.setRedisTime(0L);
        redisReqDTO.setTimeUnit(TimeUnit.MILLISECONDS);
        verify(mockMemberInfoClientService).putWeChatUserInfoToRedis(redisReqDTO);
    }
}
