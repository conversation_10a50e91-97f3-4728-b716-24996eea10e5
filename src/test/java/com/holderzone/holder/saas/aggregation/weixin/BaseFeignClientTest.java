//package com.holderzone.holder.saas.aggregation.weixin;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.holderzone.holder.saas.aggregation.weixin.service.rpc.cmember.MemberBaseClientService;
//import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.WeChatClientService;
//import org.junit.runner.RunWith;
//import org.springframework.http.HttpEntity;
//import org.springframework.http.HttpMethod;
//import org.springframework.http.ResponseEntity;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.web.client.RestTemplate;
//
//import javax.annotation.Resource;
//import java.util.Map;
//
//@RunWith(SpringJUnit4ClassRunner.class)
//public class BaseFeignClientTest {
//
//    private  final  static String url =  "http://localhost:8163";
//
//    private static RestTemplate restTemplate = new RestTemplate();
//
//	@Resource
//	private MemberBaseClientService memberBaseClientService;
//
//	@Resource
//	private WeChatClientService weChatClientService;
//
//    public JSONObject post(String url,Object data,JSONObject headers) {
//    	ResponseEntity<String> response = restTemplate.exchange(url + "/deal/order/detail/settlement/123" ,
//                HttpMethod.POST,
//               new HttpEntity<String>(JSONObject.toJSONString(data)),
//                String.class);
//    	return JSON.parseObject(response.getBody());
//    }
//    public JSONObject get(String url,Map<String,Object> data,JSONObject headers) {
//    	ResponseEntity<String> response = restTemplate.exchange(url + "/deal/order/detail/settlement/123" ,
//                HttpMethod.GET,
//                null,
//                String.class,
//                data);
//    	return JSON.parseObject(response.getBody());
//    }
//
//
//}
