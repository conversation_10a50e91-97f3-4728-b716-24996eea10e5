package com.holderzone.holder.saas.aggregation.app.controller.trade;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.HolderSaasStoreAggregationAppApplication;
import com.holderzone.holder.saas.aggregation.app.controller.util.JsonFileUtil;
import com.holderzone.holder.saas.aggregation.app.execption.FileIllegalException;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.bill.OrderRefundReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.RefundOrderDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.RefundOrderRecordDTO;
import com.holderzone.saas.store.enums.order.RefundTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Objects;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


/**
 * 订单部分退款
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreAggregationAppApplication.class)
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class RefundOrderControllerTest {

    private static final String USERINFO = "{\"enterpriseGuid\":\"4895\",\"enterpriseNo\":\"********\",\"enterpriseName\":\"销售报表测试企业1\"," +
            "\"commercialActivities\":\"\",\"storeGuid\":\"4908\",\"storeNo\":\"2976518\",\"storeName\":\"会员门店1\",\"deviceGuid\":\"2304251132214660007\"," +
            "\"userGuid\":\"8645\",\"name\":\"孙悟空\",\"tel\":\"***********\",\"account\":\"697124\",\"allianceId\":null,\"isAlliance\":false," +
            "\"operSubjectGuid\":\"2209191214425540006\",\"multiMemberStatus\":false}";

    private static final String ORDER_REFUND = "/dine_in_bill/order/refund";

    private static final String ORDER_DETAILS = "/dine_in_order/get_order_detail";

    private static final String RESPONSE = "response:";

    private static final String ORDER_GUID = "7120973835473518592";

    private static final String REQUEST_PARAM_NOT_EMPTY = "退款请求参数为空";

    private MockMvc mockMvc;

    @Autowired
    private WebApplicationContext wac;

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    /**
     * 查询订单详情
     * 订单明细中返回的优惠价
     */
    @Test
    public void orderDetail() throws UnsupportedEncodingException {
        SingleDataDTO detailReqDTO = JSON.parseObject(JsonFileUtil.read("order/detail_req.json"),
                SingleDataDTO.class);
        String detailReqJsonStr = JSON.toJSONString(detailReqDTO);
        MvcResult mvcDetailResult = null;
        try {
            mvcDetailResult = mockMvc.perform(post(ORDER_DETAILS)
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(detailReqJsonStr))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String content = mvcDetailResult.getResponse().getContentAsString();
        log.info(RESPONSE + content);
        DineinOrderDetailRespDTO dineinOrderDetailRespDTO = JacksonUtils.toObject(DineinOrderDetailRespDTO.class, content);
        List<DineInItemDTO> dineInItemList = dineinOrderDetailRespDTO.getDineInItemDTOS();
        log.info("订单商品明细:{}", JacksonUtils.writeValueAsString(dineInItemList));
        dineInItemList.forEach(e -> log.info("商品优惠价：{}", e.getPrice()));
    }


    /**
     * 查询订单详情
     * 订单明细中返回并桌商品
     */
    @Test
    public void orderDetailCombine() throws UnsupportedEncodingException {
        SingleDataDTO combineDetailReqDTO = JSON.parseObject(JsonFileUtil.read("order/detail_req.json"),
                SingleDataDTO.class);
        String combineDetailReqJsonStr = JSON.toJSONString(combineDetailReqDTO);
        MvcResult mvcCombineDetailResult = null;
        try {
            mvcCombineDetailResult = mockMvc.perform(post(ORDER_DETAILS)
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(combineDetailReqJsonStr))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String content = mvcCombineDetailResult.getResponse().getContentAsString();
        log.info(RESPONSE + content);
        DineinOrderDetailRespDTO combineOrderDetailRespDTO = JacksonUtils.toObject(DineinOrderDetailRespDTO.class, content);
        List<DineinOrderDetailRespDTO> subOrderDetails = combineOrderDetailRespDTO.getSubOrderDetails();
        log.info("订单并桌订单明细:{}", JacksonUtils.writeValueAsString(subOrderDetails));
        for (DineinOrderDetailRespDTO subOrderDetail : subOrderDetails) {
            List<DineInItemDTO> dineInItemList = subOrderDetail.getDineInItemDTOS();
            log.info("并桌订单商品明细：{}", JacksonUtils.writeValueAsString(dineInItemList));
        }
    }

    /**
     * 查询订单可退款的详情
     */
    @Test
    public void getAvailableRefundDetail() throws UnsupportedEncodingException {
        MvcResult availableRefundDetailResult = null;
        try {
            availableRefundDetailResult = mockMvc.perform(get(ORDER_REFUND + "/available/" + ORDER_GUID)
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String content = availableRefundDetailResult.getResponse().getContentAsString();
        log.info(RESPONSE + content);
        DineinOrderDetailRespDTO availableRefundDetail = JacksonUtils.toObject(DineinOrderDetailRespDTO.class, content);
        log.info("可退款详情:{}", JacksonUtils.writeValueAsString(availableRefundDetail));
    }

    /**
     * 查询订单可退款的详情 - 并台退款
     */
    @Test
    public void getAvailableRefundDetailForCombine() throws UnsupportedEncodingException {
        MvcResult availableRefundDetailForCombineResult = null;
        try {
            availableRefundDetailForCombineResult = mockMvc.perform(get(ORDER_REFUND + "/available/" + ORDER_GUID)
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String content = availableRefundDetailForCombineResult.getResponse().getContentAsString();
        log.info(RESPONSE + content);
        DineinOrderDetailRespDTO availableRefundDetail = JacksonUtils.toObject(DineinOrderDetailRespDTO.class, content);
        log.info("并台可退款详情:{}", JacksonUtils.writeValueAsString(availableRefundDetail));
    }


    /**
     * 订单退款 - 原路返回
     */
    @Test
    public void backRefund() throws UnsupportedEncodingException {
        OrderRefundReqDTO orderRefundBackReqDTO = JSON.parseObject(JsonFileUtil.read("order/refund_back.json"),
                OrderRefundReqDTO.class);
        log.info("原路退款请求参数入参:{}", JacksonUtils.writeValueAsString(orderRefundBackReqDTO));
        if (Objects.isNull(orderRefundBackReqDTO)) {
            log.error(REQUEST_PARAM_NOT_EMPTY);
            return;
        }
        if (RefundTypeEnum.BACKTRACK.getCode() != orderRefundBackReqDTO.getRefundType()) {
            log.error("非原路返回退款");
            return;
        }
        String orderRefundBackReqJsonStr = JSON.toJSONString(orderRefundBackReqDTO);
        MvcResult mvcRefundBackResult = null;
        try {
            mvcRefundBackResult = mockMvc.perform(post(ORDER_REFUND)
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(orderRefundBackReqJsonStr))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String content = mvcRefundBackResult.getResponse().getContentAsString();
        log.info(RESPONSE + content);
    }

    /**
     * 订单退款 - 线下退款
     */
    @Test
    public void offlineRefund() throws UnsupportedEncodingException {
        OrderRefundReqDTO orderOfflineRefundReqDTO = JSON.parseObject(JsonFileUtil.read("order/refund_offline.json"),
                OrderRefundReqDTO.class);
        log.info("线下退款请求参数入参:{}", JacksonUtils.writeValueAsString(orderOfflineRefundReqDTO));
        if (Objects.isNull(orderOfflineRefundReqDTO)) {
            log.error(REQUEST_PARAM_NOT_EMPTY);
            return;
        }
        if (RefundTypeEnum.OFFLINE_REFUND.getCode() != orderOfflineRefundReqDTO.getRefundType()) {
            log.error("非线下退款");
            return;
        }
        String orderOfflineRefundReqJsonStr = JSON.toJSONString(orderOfflineRefundReqDTO);
        MvcResult mvcOfflineRefundResult = null;
        try {
            mvcOfflineRefundResult = mockMvc.perform(post(ORDER_REFUND)
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(orderOfflineRefundReqJsonStr))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String content = mvcOfflineRefundResult.getResponse().getContentAsString();
        log.info(RESPONSE + content);
    }

    /**
     * 订单退款 - 并台退款
     * <p>
     * fixBug: 只退子桌的菜品, 没有更新原子桌订单商品明细的退款数量
     */
    @Test
    public void combineOrderRefund() throws UnsupportedEncodingException {
        OrderRefundReqDTO orderCombineRefundReqDTO = JSON.parseObject(JsonFileUtil.read("order/refund_combine.json"),
                OrderRefundReqDTO.class);
        log.info("并台退款请求参数入参:{}", JacksonUtils.writeValueAsString(orderCombineRefundReqDTO));
        if (Objects.isNull(orderCombineRefundReqDTO)) {
            log.error(REQUEST_PARAM_NOT_EMPTY);
            return;
        }
        String orderCombineRefundReqJsonStr = JSON.toJSONString(orderCombineRefundReqDTO);
        MvcResult mvcCombineRefundResult = null;
        try {
            mvcCombineRefundResult = mockMvc.perform(post(ORDER_REFUND)
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(orderCombineRefundReqJsonStr))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String content = mvcCombineRefundResult.getResponse().getContentAsString();
        log.info(RESPONSE + content);
    }

    /**
     * 查询退款单详情
     */
    @Test
    public void refundOrderDetail() throws UnsupportedEncodingException {
        SingleDataDTO refundDetailReqDTO = JSON.parseObject(JsonFileUtil.read("order/refund_detail_req.json"),
                SingleDataDTO.class);
        String detailReqJsonStr = JSON.toJSONString(refundDetailReqDTO);
        MvcResult mvcRefundDetailResult = null;
        try {
            mvcRefundDetailResult = mockMvc.perform(post(ORDER_DETAILS)
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(detailReqJsonStr))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String content = mvcRefundDetailResult.getResponse().getContentAsString();
        log.info(RESPONSE + content);
        DineinOrderDetailRespDTO refundOrderDetailRespDTO = JacksonUtils.toObject(DineinOrderDetailRespDTO.class, content);
        log.info("退款商品明细:{}", JacksonUtils.writeValueAsString(refundOrderDetailRespDTO.getDineInItemDTOS()));
        List<RefundOrderRecordDTO> refundOrderRecordList = refundOrderDetailRespDTO.getRefundOrderRecordList();
        log.info("退款记录明细:{}", JacksonUtils.writeValueAsString(refundOrderRecordList));
        RefundOrderDetailDTO refundOrderDetailDTO = refundOrderDetailRespDTO.getRefundOrderDetailDTO();
        log.info("退款支付明细:{}", JacksonUtils.writeValueAsString(refundOrderDetailDTO));
    }

    /**
     * 查询有退款单的原订单详情
     * 展示原单明细 + 退款单明细
     */
    @Test
    public void refundOriginOrderDetail() throws UnsupportedEncodingException {
        SingleDataDTO refundOriginDetailReqDTO = JSON.parseObject(JsonFileUtil.read("order/refund_detail_req.json"),
                SingleDataDTO.class);
        String originDetailReqJsonStr = JSON.toJSONString(refundOriginDetailReqDTO);
        MvcResult mvcRefundOriginDetailResult = null;
        try {
            mvcRefundOriginDetailResult = mockMvc.perform(post(ORDER_DETAILS)
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(originDetailReqJsonStr))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new FileIllegalException(e.getMessage());
        }
        String content = mvcRefundOriginDetailResult.getResponse().getContentAsString();
        log.info(RESPONSE + content);
        DineinOrderDetailRespDTO refundOrderDetailRespDTO = JacksonUtils.toObject(DineinOrderDetailRespDTO.class, content);
        RefundOrderDetailDTO refundOrderDetailDTO = refundOrderDetailRespDTO.getRefundOrderDetailDTO();
        log.info("退款记录明细:{}", JacksonUtils.writeValueAsString(refundOrderDetailDTO));
    }

}
