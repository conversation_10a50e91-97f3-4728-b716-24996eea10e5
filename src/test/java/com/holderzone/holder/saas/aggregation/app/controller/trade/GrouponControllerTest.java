package com.holderzone.holder.saas.aggregation.app.controller.trade;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.HolderSaasStoreAggregationAppApplication;
import com.holderzone.holder.saas.aggregation.app.controller.util.JsonFileUtil;
import com.holderzone.holder.saas.aggregation.app.controller.util.MockHttpUtil;
import com.holderzone.holder.saas.aggregation.app.utils.SpringContextUtil;
import com.holderzone.saas.store.dto.order.request.groupon.GrouponReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Objects;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


/**
 * 团购验券
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreAggregationAppApplication.class)
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class GrouponControllerTest {


    @Autowired
    private ApplicationContext ap;

    @Autowired
    private WebApplicationContext webApplicationContext;
    private MockMvc mockMvc;

    private static final String GROUPON_REQUEST_MAPPING = "/groupon";

    private static final String THIRD_REQUEST_MAPPING = "/groupon";

    private static final String PRE_CHECK = "/pre_check";

    private static final String PRE_CHECK_QUERY_ITEM = "/third_activity/pre_check/query_item";


    private static final String RESPONSE = "response:";

    public static final String USERINFO = "{\"enterpriseGuid\":\"2009281531195930006\",\"enterpriseNo\":\"********\",\"enterpriseName\":\"赵氏企业\"," +
            "\"commercialActivities\":\"10001\",\"storeGuid\":\"2106221850429620006\",\"storeNo\":\"5796807\",\"storeName\":\"交子大道测试门店\"," +
            "\"deviceGuid\":\"2304251132214660007\",\"userGuid\":\"6869112864859226113\",\"name\":\"zhouzixiang\",\"tel\":\"***********\"," +
            "\"account\":\"967452\",\"allianceId\":null,\"isAlliance\":false,\"operSubjectGuid\":\"2010121440477930009\",\"multiMemberStatus\":false}";

    @Before
    public void setupMockMvc() {
        SpringContextUtil.getInstance().setCfgContext((ConfigurableApplicationContext) ap);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.webApplicationContext).build();
    }

    /**
     * 结账页面预验券
     */
    @Test
    public void testPreCheckMt() {
        CouPonPreReqDTO reqVO = JSON.parseObject(JsonFileUtil.read("groupon/preCheck_mt.json"), CouPonPreReqDTO.class);
        String content = MockHttpUtil.post(GROUPON_REQUEST_MAPPING + PRE_CHECK, reqVO, mockMvc);
        log.info("美团结账预验券结果:{}", JacksonUtils.writeValueAsString(content));
    }

    /**
     * 结账页面预验券
     */
    @Test
    public void testPreCheckDy() {
        CouPonPreReqDTO reqVO = JSON.parseObject(JsonFileUtil.read("groupon/preCheck_dy.json"), CouPonPreReqDTO.class);
        String content = MockHttpUtil.post(GROUPON_REQUEST_MAPPING + PRE_CHECK, reqVO, mockMvc);
        log.info("抖音结账预验券结果:{}", JacksonUtils.writeValueAsString(content));
    }

    /**
     * 结账页面支付宝预验券
     */
    @Test
    public void testPreCheckAlipay() {
        CouPonPreReqDTO reqVO = JSON.parseObject(JsonFileUtil.read("groupon/preCheck_alipay.json"), CouPonPreReqDTO.class);
        String content = MockHttpUtil.post(GROUPON_REQUEST_MAPPING + PRE_CHECK, reqVO, mockMvc);
        log.info("支付宝结账预验券结果:{}", JacksonUtils.writeValueAsString(content));
    }

    /**
     * 结账页面支付宝预验券
     * 使用POS预验券
     */
    @Test
    public void testPreCheckAlipayByPOS() throws UnsupportedEncodingException {
        CouPonPreReqDTO preCheckAlipayByPOSReqVO = JSON.parseObject(JsonFileUtil.read("groupon/preCheck_alipay.json"), CouPonPreReqDTO.class);
        if (Objects.isNull(preCheckAlipayByPOSReqVO)) {
            throw new BusinessException("提交参数为空");
        }
        Assert.notNull(preCheckAlipayByPOSReqVO.getCouponCode(), "支付宝券码不能为空");
        Assert.notNull(preCheckAlipayByPOSReqVO.getGroupBuyType(), "团购类型不能为空");
        String preCheckAlipayReqJson = JSON.toJSONString(preCheckAlipayByPOSReqVO);
        MvcResult mvcResult = null;
        try {
            mvcResult = mockMvc.perform(post(GROUPON_REQUEST_MAPPING + PRE_CHECK)
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(preCheckAlipayReqJson))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String preCheckAlipayContent = mvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + preCheckAlipayContent);
        Assert.notBlank(preCheckAlipayContent, "支付宝验券返回为空");
        Result result = JacksonUtils.toObject(Result.class, preCheckAlipayContent);
        Assert.equals(result.getCode(), 0, "支付宝验券返回失败");
        List<MtCouponPreRespDTO> couponPreRespList = (List<MtCouponPreRespDTO>) result.getTData();
        Assert.notEmpty(couponPreRespList, "支付宝验券券码列表返回为空");
        for (MtCouponPreRespDTO couponPreRespDTO : couponPreRespList) {
            Assert.notNull(couponPreRespDTO.getCouponCode(), "支付宝券码为空");
            Assert.notNull(couponPreRespDTO.getIsVoucher(), "支付宝是否代金券为空");
            Assert.notNull(couponPreRespDTO.getCouponEndTime(), "支付宝券有效期为空");
            Assert.notNull(couponPreRespDTO.getDealValue(), "支付宝券面值为空");
            Assert.notNull(couponPreRespDTO.getCount(), "支付宝最大可验证张数为空");
            Assert.notNull(couponPreRespDTO.getIsThirdShare(), "支付宝是否与其他活动类型共享为空");
            Assert.notNull(couponPreRespDTO.getIsActivityShare(), "支付宝是否与其他平台活动共享为空");
        }
    }


    /**
     * 验券加商品页面预验券
     */
    @Test
    public void testPreCheckQueryItemMt() {
        CouPonPreReqDTO reqVO = JSON.parseObject(JsonFileUtil.read("groupon/preCheck_mt.json"), CouPonPreReqDTO.class);
        String content = MockHttpUtil.post(THIRD_REQUEST_MAPPING + "/pre_check/query_item", reqVO, mockMvc);
        log.info("美团验券查商品预验券结果:{}", JacksonUtils.writeValueAsString(content));
    }

    /**
     * 验券加商品页面预验券支付宝团购券
     * 使用POS预验券
     */
    @Test
    public void testPreCheckQueryItemAlipayByPOS() throws UnsupportedEncodingException {
        CouPonPreReqDTO preCheckAlipayQueryItemByPOSReqVO = JSON.parseObject(JsonFileUtil.read("groupon/preCheck_alipay.json"), CouPonPreReqDTO.class);
        if (Objects.isNull(preCheckAlipayQueryItemByPOSReqVO)) {
            throw new BusinessException("提交参数为空");
        }
        Assert.notNull(preCheckAlipayQueryItemByPOSReqVO.getCouponCode(), "支付宝券码不能为空");
        Assert.notNull(preCheckAlipayQueryItemByPOSReqVO.getGroupBuyType(), "团购类型不能为空");
        // 验券加购商品 请求参数
        String preCheckAlipayReqJson = JSON.toJSONString(preCheckAlipayQueryItemByPOSReqVO);
        MvcResult mvcResult = null;
        try {
            mvcResult = mockMvc.perform(post(PRE_CHECK_QUERY_ITEM)
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(preCheckAlipayReqJson))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String preCheckQueryItemAlipayContent = mvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + preCheckQueryItemAlipayContent);
        Assert.notBlank(preCheckQueryItemAlipayContent, "支付宝验券返回为空");
        Result result = JacksonUtils.toObject(Result.class, preCheckQueryItemAlipayContent);
        Assert.equals(result.getCode(), 0, "支付宝验券返回失败");
        List<MtCouponPreRespDTO> couponPreRespList = (List<MtCouponPreRespDTO>) result.getTData();
        Assert.notEmpty(couponPreRespList, "支付宝验券券码列表返回为空");
        for (MtCouponPreRespDTO alipayCouponPreResp : couponPreRespList) {
            Assert.notBlank(alipayCouponPreResp.getItemGuid(), "支付宝第三方活动关联套餐商品guid为空");
            Assert.notBlank(alipayCouponPreResp.getSkuGuid(), "支付宝第三方活动关联套餐商品规格guid为空");
            Assert.notNull(alipayCouponPreResp.getCouponCode(), "支付宝券码为空");
            Assert.notNull(alipayCouponPreResp.getCouponEndTime(), "支付宝券有效期为空");
            Assert.notNull(alipayCouponPreResp.getDealValue(), "支付宝券面值为空");
            Assert.notNull(alipayCouponPreResp.getCount(), "支付宝最大可验证张数为空");
            Assert.notNull(alipayCouponPreResp.getIsThirdShare(), "支付宝是否与其他活动类型共享为空");
            Assert.notNull(alipayCouponPreResp.getIsActivityShare(), "支付宝是否与其他平台活动共享为空");
        }
    }


    /**
     * 验券加商品页面预验券
     */
    @Test
    public void testPreCheckQueryItemDy() {
        CouPonPreReqDTO reqVO = JSON.parseObject(JsonFileUtil.read("groupon/preCheck_dy.json"), CouPonPreReqDTO.class);
        String content = MockHttpUtil.post(THIRD_REQUEST_MAPPING + "/pre_check/query_item", reqVO, mockMvc);
        log.info("抖音验券查商品预验券结果:{}", JacksonUtils.writeValueAsString(content));
    }

    @Test
    public void verify() {
        GrouponReqDTO reqVO = JSON.parseObject(JsonFileUtil.read("groupon/verify_alipay.json"), GrouponReqDTO.class);
        String content = MockHttpUtil.post(THIRD_REQUEST_MAPPING + "/verify", reqVO, mockMvc);
        log.info("[支付宝]结账页面验券结果:{}", JacksonUtils.writeValueAsString(content));
    }

    @Test
    public void undo() {
        GrouponReqDTO reqVO = JSON.parseObject(JsonFileUtil.read("groupon/undo_alipay.json"), GrouponReqDTO.class);
        String content = MockHttpUtil.post(THIRD_REQUEST_MAPPING + "/undo", reqVO, mockMvc);
        log.info("[支付宝]结账页面撤销结果:{}", JacksonUtils.writeValueAsString(content));
    }
}
