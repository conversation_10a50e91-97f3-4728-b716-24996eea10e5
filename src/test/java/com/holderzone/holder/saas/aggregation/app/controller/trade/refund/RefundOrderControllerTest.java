package com.holderzone.holder.saas.aggregation.app.controller.trade.refund;

import cn.hutool.core.lang.Assert;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.HolderSaasStoreAggregationAppApplication;
import com.holderzone.holder.saas.aggregation.app.controller.util.JsonFileUtil;
import com.holderzone.holder.saas.aggregation.app.utils.SpringContextUtil;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.bill.OrderRefundReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.enums.order.RefundTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.UnsupportedEncodingException;
import java.util.List;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


/**
 * 订单部分退款
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreAggregationAppApplication.class)
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class RefundOrderControllerTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private WebApplicationContext webApplicationContext;
    private MockMvc mockMvc;

    private static final String REQUEST_MAPPING = "/dine_in_bill/order/refund";

    private static final String PREFIX = "/dine_in_bill/order/refund";

    private static final String ORDER_GUID = "7102203003318304768";

    private static final String RESPONSE = "response:";

    public static final String USERINFO = "{\"enterpriseGuid\":\"2009281531195930006\"," +
            "\"enterpriseNo\":\"********\"," +
            "\"enterpriseName\":\"赵氏企业\"," +
            "\"commercialActivities\":\"10001\"," +
            "\"storeGuid\":\"2106221850429620006\"," +
            "\"storeNo\":\"5796807\"," +
            "\"storeName\":\"交子大道测试门店\"," +
            "\"deviceGuid\":\"2304251132214660007\"," +
            "\"userGuid\":\"6869112864859226113\"," +
            "\"name\":\"zhouzixiang\"," +
            "\"tel\":\"***********\"," +
            "\"account\":\"967452\"," +
            "\"allianceId\":null," +
            "\"isAlliance\":false," +
            "\"operSubjectGuid\":\"2010121440477930009\"," +
            "\"multiMemberStatus\":false}";


    @Before
    public void setupMockMvc() {
        SpringContextUtil.getInstance().setCfgContext((ConfigurableApplicationContext) applicationContext);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.webApplicationContext).build();
    }

    /**
     * 查询可退款的订单详情
     */
    @Test
    public void testGetAvailableRefundDetail() throws UnsupportedEncodingException {
        Assert.notEmpty(ORDER_GUID, "订单guid不能为空");
        MvcResult availableRefundDetailMvcResult = null;
        try {
            availableRefundDetailMvcResult = mockMvc.perform(get(PREFIX + "/available/" + ORDER_GUID)
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String availableRefundDetailContent = availableRefundDetailMvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + availableRefundDetailContent);
        Assert.notBlank(availableRefundDetailContent, "可退款的订单详情返回为空");
        Result result = JacksonUtils.toObject(Result.class, availableRefundDetailContent);
        Assert.equals(result.getCode(), 0, "可退款的订单详情返回失败, 失败原因:" + result.getMessage());
        DineinOrderDetailRespDTO dineinOrderDetailRespDTO = (DineinOrderDetailRespDTO) result.getTData();
        Assert.notNull(dineinOrderDetailRespDTO.getGuid(), "可退款的订单详情返回订单guid为空");
        Assert.notNull(dineinOrderDetailRespDTO.getStoreName(), "可退款的订单详情返回门店名称为空");
        Assert.notNull(dineinOrderDetailRespDTO.getOrderFee(), "可退款的订单详情返回订单总额为空");
        Assert.notNull(dineinOrderDetailRespDTO.getActuallyPayFee(), "可退款的订单详情返回实收金额为空");
        Assert.notNull(dineinOrderDetailRespDTO.getDiscountFee(), "可退款的订单详情返回优惠金额为空");
        Assert.notNull(dineinOrderDetailRespDTO.getActuallyPayFeeDetailDTOS(), "可退款的订单详情返回实收金额明细为空");
        Assert.notEmpty(dineinOrderDetailRespDTO.getActuallyPayFeeDetailDTOS(), "可退款的订单详情返回实收金额明细为空");
        // 实付金额明细 前端需要根据这个展示明细
        for (ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO : dineinOrderDetailRespDTO.getActuallyPayFeeDetailDTOS()) {
            Assert.notNull(actuallyPayFeeDetailDTO.getGuid(), "实收金额明细中唯一标识为空");
            Assert.notNull(actuallyPayFeeDetailDTO.getAmount(), "实收金额明细中金额为空");
            Assert.notNull(actuallyPayFeeDetailDTO.getPaymentType(), "实收金额明细中支付方式为空");
            Assert.notNull(actuallyPayFeeDetailDTO.getGrouponRefundableFlag(), "实收金额明细中团购是否可退款为空");
        }
        Assert.notNull(dineinOrderDetailRespDTO.getDiscountFeeDetailDTOS(), "可退款的订单详情返回优惠金额明细为空");
        Assert.notEmpty(dineinOrderDetailRespDTO.getDiscountFeeDetailDTOS(), "可退款的订单详情返回优惠金额明细为空");
        Assert.notNull(dineinOrderDetailRespDTO.getDineInItemDTOS(), "可退款的订单详情返回商品明细为空");
        Assert.notEmpty(dineinOrderDetailRespDTO.getDineInItemDTOS(), "可退款的订单详情返回商品明细为空");
    }


    /**
     * 订单退款 - 线下退款
     */
    @Test
    public void testOfflineOrderRefund() throws UnsupportedEncodingException {
        OrderRefundReqDTO refundReqVO = JacksonUtils.toObject(OrderRefundReqDTO.class, JsonFileUtil.read("refund/order_refund_offline.json"));
        Assert.notNull(refundReqVO.getVersion(), "线下退款版本号为空");
        Assert.notNull(refundReqVO.getOrderGuid(), "线下退款订单guid为空");
        Assert.notNull(refundReqVO.getRefundType(), "线下退款版本号为空");
        Assert.equals(refundReqVO.getRefundType() == RefundTypeEnum.OFFLINE_REFUND.getCode(), "线下退款类型为空");
        Assert.notNull(refundReqVO.getRefundReason(), "线下退款退款原因为空");
        Assert.notNull(refundReqVO.getRefundAmount(), "线下退款退款金额为空");
        Assert.notNull(refundReqVO.getDineInItemDTOS(), "线下退款退款商品明细为空");
        List<DineInItemDTO> dineInItemList = refundReqVO.getDineInItemDTOS();
        for (DineInItemDTO dineInItemDTO : dineInItemList) {
            Assert.notNull(dineInItemDTO.getGuid(), "线下退款退款商品明细中商品明细guid为空");
            Assert.notNull(dineInItemDTO.getPrice(), "线下退款退款商品明细中商品明细价格为空");
            Assert.notNull(dineInItemDTO.getCurrentCount(), "线下退款退款商品明细中商品明细数量为空");
        }
        MvcResult offlineOrderRefundMvcResult = null;
        try {
            offlineOrderRefundMvcResult = mockMvc.perform(post(PREFIX)
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String offlineOrderRefundContent = offlineOrderRefundMvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + offlineOrderRefundContent);
        Assert.notBlank(offlineOrderRefundContent, "线下退款返回为空");
        Result result = JacksonUtils.toObject(Result.class, offlineOrderRefundContent);
        Assert.equals(result.getCode(), 0, "线下退款返回失败, 失败原因:" + result.getMessage());
        String refundOrderGuid = (String) result.getTData();
        Assert.notNull(refundOrderGuid, "线下退款退款返回退款单guid为空");
    }


    /**
     * 订单退款 - 原路退款
     */
    @Test
    public void testBackTrackOrderRefund() throws UnsupportedEncodingException {
        OrderRefundReqDTO backRefundReqVO = JacksonUtils.toObject(OrderRefundReqDTO.class, JsonFileUtil.read("refund/order_refund_offline.json"));
        Assert.notNull(backRefundReqVO.getVersion(), "原路返回退款版本号为空");
        Assert.notNull(backRefundReqVO.getOrderGuid(), "原路返回退款订单guid为空");
        Assert.notNull(backRefundReqVO.getRefundType(), "原路返回退款版本号为空");
        Assert.equals(backRefundReqVO.getRefundType() == RefundTypeEnum.BACKTRACK.getCode(), "原路返回退款类型为空");
        Assert.notNull(backRefundReqVO.getRefundReason(), "原路返回退款退款原因为空");
        Assert.notNull(backRefundReqVO.getRefundAmount(), "原路返回退款退款金额为空");
        Assert.notNull(backRefundReqVO.getDineInItemDTOS(), "原路返回退款退款商品明细为空");
        List<DineInItemDTO> dineInItemList = backRefundReqVO.getDineInItemDTOS();
        for (DineInItemDTO dineInItemDTO : dineInItemList) {
            Assert.notNull(dineInItemDTO.getGuid(), "原路返回退款退款商品明细中商品明细guid为空");
            Assert.notNull(dineInItemDTO.getPrice(), "原路返回退款退款商品明细中商品明细价格为空");
            Assert.notNull(dineInItemDTO.getCurrentCount(), "原路返回退款退款商品明细中商品明细数量为空");
        }
        MvcResult backTrackOrderRefundMvcResult = null;
        try {
            backTrackOrderRefundMvcResult = mockMvc.perform(post(PREFIX)
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String backTrackOrderRefundContent = backTrackOrderRefundMvcResult.getResponse().getContentAsString();
        log.info(RESPONSE + backTrackOrderRefundContent);
        Assert.notBlank(backTrackOrderRefundContent, "原路返回退款返回为空");
        Result result = JacksonUtils.toObject(Result.class, backTrackOrderRefundContent);
        Assert.equals(result.getCode(), 0, "原路返回退款返回失败, 失败原因:" + result.getMessage());
        String refundOrderGuid = (String) result.getTData();
        Assert.notNull(refundOrderGuid, "原路返回退款退款返回退款单guid为空");
    }
}
