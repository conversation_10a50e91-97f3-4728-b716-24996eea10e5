package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxStoreListClientService;
import com.holderzone.saas.store.dto.weixin.*;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreCityListRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(WxStoreListController.class)
public class WxStoreListControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStoreListClientService mockWxStoreListClientService;

    @Test
    public void testListStoreConfig() throws Exception {
        // Setup
        // Configure WxStoreListClientService.listStoreConfig(...).
        final WxStoreListDTO wxStoreListDTO = new WxStoreListDTO("enterpriseGuid", "brandGuid", "brandName",
                "brandLogoUrl",
                Arrays.asList(new WxStoreConfigDTO(new WxStoreQueueConfigDTO(false, false, false, 0, false))), 0);
        when(mockWxStoreListClientService.listStoreConfig(
                new WxPositionDTO(new BigDecimal("0.00"), new BigDecimal("0.00"), "cityCode", "cityName")))
                .thenReturn(wxStoreListDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store/list_store_config")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testListStoreCity() throws Exception {
        // Setup
        // Configure WxStoreListClientService.listStoreCity(...).
        final WxStoreCityListRespDTO wxStoreCityListRespDTO = new WxStoreCityListRespDTO();
        final WxStoreCityDTO wxStoreCityDTO = new WxStoreCityDTO();
        wxStoreCityDTO.setCityName("cityName");
        wxStoreCityDTO.setCode("code");
        wxStoreCityDTO.setCount(0);
        wxStoreCityListRespDTO.setCityList(Arrays.asList(wxStoreCityDTO));
        final WxStoreCityDTO wxStoreCityDTO1 = new WxStoreCityDTO();
        wxStoreCityDTO1.setCityName("cityName");
        wxStoreCityDTO1.setCode("code");
        wxStoreCityDTO1.setCount(0);
        wxStoreCityListRespDTO.setOtherCities(Arrays.asList(wxStoreCityDTO1));
        when(mockWxStoreListClientService.listStoreCity(
                new WxPositionDTO(new BigDecimal("0.00"), new BigDecimal("0.00"), "cityCode", "cityName")))
                .thenReturn(wxStoreCityListRespDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/wx_store/list_store_city")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetStoreConfig() throws Exception {
        // Setup
        // Configure WxStoreListClientService.getStoreConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("677dbeda-2e99-4949-95e8-40e85889a821");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreListClientService.getStoreConfig("storeGuid")).thenReturn(wxOrderConfigDTO);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/wx_store/store_config")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
