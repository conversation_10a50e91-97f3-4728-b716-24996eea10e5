package com.holderzone.holder.saas.aggregation.weixin.controller.cmember.account;

import com.holderzone.holder.saas.aggregation.weixin.config.ResponseModel;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxUserRecordClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.WxHsmMemberBasicBufferService;
import com.holderzone.holder.saas.member.wechat.dto.base.RequestMemberPasswore;
import com.holderzone.holder.saas.member.wechat.dto.base.RequestMemberPhoneSmsCode;
import com.holderzone.holder.saas.member.wechat.dto.member.*;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(WxHsmMemberBasicController.class)
public class WxHsmMemberBasicControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private RedisUtils mockRedisUtils;
    @MockBean
    private WxUserRecordClientService mockWxUserRecordClientService;
    @MockBean
    private WxHsmMemberBasicBufferService mockWxHsmMemberBasicBufferService;
    @MockBean
    private HsaBaseClientService mockHsaBaseClientService;

    @Test
    public void testSendRegSmsCode() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setMemberInfoGuid("memberInfoGuid");
        responseMemberInfo.setOperSubjectGuid("operSubjectGuid");
        responseMemberInfo.setEnterpriseGuid("enterpriseGuid");
        responseMemberInfo.setPhoneNum("phoneNum");
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        when(mockHsaBaseClientService.sendLoginSmsCode("phoneNum")).thenReturn(new ResponseModel<>(false));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hsmcw/member/sendRegSmsCode")
                        .param("phoneNum", "phoneNum")
                        .param("enterpriseGuid", "enterpriseGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSendLoginSmsCode() throws Exception {
        // Setup
        when(mockHsaBaseClientService.sendLoginSmsCode("phoneNum")).thenReturn(new ResponseModel<>(false));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hsmcw/member/sendLoginSmsCode")
                        .param("phoneNum", "phoneNum")
                        .param("enterpriseGuid", "enterpriseGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testRegisterOrLoginMemberInfo() throws Exception {
        // Setup
        // Configure HsaBaseClientService.registerOrLoginMemberInfo(...).
        final RequestMemberBasic requestMemberBasic = new RequestMemberBasic();
        requestMemberBasic.setMemberInfoGuid("memberInfoGuid");
        requestMemberBasic.setPhoneNum("newPhoneNum");
        requestMemberBasic.setMemberEnterpriseGuid("memberEnterpriseGuid");
        requestMemberBasic.setMemberEnterpriseName("memberEnterpriseName");
        requestMemberBasic.setOpenid("openid");
        when(mockHsaBaseClientService.registerOrLoginMemberInfo(requestMemberBasic))
                .thenReturn(new ResponseModel<>(false));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hsmcw/member/registerOrLoginMemberInfo")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
        verify(mockWxHsmMemberBasicBufferService).removeByOpenId("openid", "memberEnterpriseGuid");
        verify(mockWxUserRecordClientService).loginWxUserRecordAndFlushCard("memberEnterpriseGuid", "newPhoneNum",
                "openid");
    }

    @Test
    public void testGetMemberState1() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getMemberState(...).
        final ResponseAccountStatus responseAccountStatus = new ResponseAccountStatus();
        responseAccountStatus.setMemberInfoGuid("memberInfoGuid");
        responseAccountStatus.setOperSubjectGuid("operSubjectGuid");
        responseAccountStatus.setPhoneNum("phoneNum");
        responseAccountStatus.setNickName("nickName");
        responseAccountStatus.setOpenId("openId");
        final ResponseModel<ResponseAccountStatus> responseAccountStatusResponseModel = new ResponseModel<>(
                responseAccountStatus);
        when(mockHsaBaseClientService.getMemberState("phoneNumOrOpenid", "enterpriseGuid",
                "operSubjectGuid")).thenReturn(responseAccountStatusResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hsmcw/member/getMemberState")
                        .param("phoneNumOrOpenid", "phoneNumOrOpenid")
                        .param("enterpriseGuid", "enterpriseGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetMemberInfo() throws Exception {
        // Setup
        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setMemberInfoGuid("memberInfoGuid");
        responseMemberInfo.setOperSubjectGuid("operSubjectGuid");
        responseMemberInfo.setEnterpriseGuid("enterpriseGuid");
        responseMemberInfo.setPhoneNum("phoneNum");
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hsmcw/member/getMemberInfo")
                        .param("phoneNumOrOpenid", "phoneNum")
                        .param("enterpriseGuid", "enterpriseGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetMemberQrcode() throws Exception {
        // Setup
        when(mockHsaBaseClientService.getMemberQrcode("key")).thenReturn(new ResponseModel<>("data"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hsmcw/member/getQrcode")
                        .param("key", "key")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryMemberMainCardQRCode() throws Exception {
        // Setup
        when(mockHsaBaseClientService.queryMemberMainCardQRCode("phoneNum")).thenReturn(new ResponseModel<>("data"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hsmcw/member/queryMemberMainCardQRCode")
                        .param("phoneNum", "phoneNum")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testUpdateMemberInfo() throws Exception {
        // Setup
        // Configure HsaBaseClientService.updateMemberInfo(...).
        final RequestUpdateMemberInfo requestUpdateMemberInfo = new RequestUpdateMemberInfo();
        requestUpdateMemberInfo.setMemberInfoGuid("memberInfoGuid");
        requestUpdateMemberInfo.setPhoneNum("phoneNum");
        requestUpdateMemberInfo.setSex(0);
        requestUpdateMemberInfo.setNickName("nickName");
        requestUpdateMemberInfo.setHeadImgUrl("headImgUrl");
        when(mockHsaBaseClientService.updateMemberInfo(requestUpdateMemberInfo)).thenReturn(new ResponseModel<>(false));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hsmcw/member/updateMemberInfo")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSendChangeSmsCode() throws Exception {
        // Setup
        when(mockHsaBaseClientService.sendChangeSmsCode("oldPhoneNum", "newPhoneNum", "enterpriseGuid"))
                .thenReturn(new ResponseModel<>(false));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hsmcw/member/sendChangeSmsCode")
                        .param("oldPhoneNum", "oldPhoneNum")
                        .param("newPhoneNum", "newPhoneNum")
                        .param("enterpriseGuid", "enterpriseGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testBindChangeMemberPhone() throws Exception {
        // Setup
        // Configure HsaBaseClientService.bindChangeMemberPhone(...).
        final RequestMemberPhoneSmsCode hsmMemberPhoneSmsCodeReqDTO = new RequestMemberPhoneSmsCode();
        hsmMemberPhoneSmsCodeReqDTO.setOldPhoneNum("oldPhoneNum");
        hsmMemberPhoneSmsCodeReqDTO.setSmsCode("smsCode");
        hsmMemberPhoneSmsCodeReqDTO.setNewPhoneNum("newPhoneNum");
        hsmMemberPhoneSmsCodeReqDTO.setOpenId("openId");
        hsmMemberPhoneSmsCodeReqDTO.setEnterpriseGuid("enterpriseGuid");
        when(mockHsaBaseClientService.bindChangeMemberPhone(hsmMemberPhoneSmsCodeReqDTO))
                .thenReturn(new ResponseModel<>(false));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hsmcw/member/bindChangeMemberPhone")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testSendBindSmsCode() throws Exception {
        // Setup
        when(mockHsaBaseClientService.sendBindSmsCode("phoneNum", "enterpriseGuid"))
                .thenReturn(new ResponseModel<>(false));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hsmcw/member/sendBindSmsCode")
                        .param("phoneNum", "phoneNum")
                        .param("enterpriseGuid", "enterpriseGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testBindMemberPhone() throws Exception {
        // Setup
        // Configure HsaBaseClientService.bindMemberPhone(...).
        final RequestBindMemberPhone requestBindMemberPhone = new RequestBindMemberPhone();
        requestBindMemberPhone.setOldPhoneNum("newPhoneNum");
        requestBindMemberPhone.setSmsCode("smsCode");
        requestBindMemberPhone.setNewPhoneNum("newPhoneNum");
        requestBindMemberPhone.setOpenId("openId");
        requestBindMemberPhone.setEnterpriseGuid("enterpriseGuid");
        when(mockHsaBaseClientService.bindMemberPhone(requestBindMemberPhone)).thenReturn(new ResponseModel<>(false));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hsmcw/member/bindMemberPhone")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
        verify(mockWxUserRecordClientService).loginWxUserRecordAndFlushCard("enterpriseGuid", "newPhoneNum", "openId");
    }

    @Test
    public void testUpdateMemberPassword() throws Exception {
        // Setup
        // Configure HsaBaseClientService.updateMemberPassword(...).
        final RequestMemberPasswore hsaMemberPassworeReqDTO = new RequestMemberPasswore();
        hsaMemberPassworeReqDTO.setMemberInfoGuid("memberInfoGuid");
        hsaMemberPassworeReqDTO.setOldPayPassword("oldPayPassword");
        hsaMemberPassworeReqDTO.setPayPassword("payPassword");
        hsaMemberPassworeReqDTO.setConfirmPayPassword("confirmPayPassword");
        hsaMemberPassworeReqDTO.setEnterpriseGuid("enterpriseGuid");
        when(mockHsaBaseClientService.updateMemberPassword(hsaMemberPassworeReqDTO))
                .thenReturn(new ResponseModel<>(false));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hsmcw/member/updateMemberPassword")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryMemberByOpenId() throws Exception {
        // Setup
        // Configure HsaBaseClientService.queryMemberByOpenId(...).
        final ResponseModel<List<ResponseMemberAccount>> listResponseModel = new ResponseModel<>(
                Arrays.asList(new ResponseMemberAccount("memberInfoGuid", "phoneNum", "unionId", "openId", false)));
        when(mockHsaBaseClientService.queryMemberByOpenId("openId")).thenReturn(listResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hsmcw/member/queryMemberByOpenId")
                        .param("openId", "openId")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testQueryMemberByOpenId_HsaBaseClientServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure HsaBaseClientService.queryMemberByOpenId(...).
        final ResponseModel<List<ResponseMemberAccount>> listResponseModel = new ResponseModel<>(
                Collections.emptyList());
        when(mockHsaBaseClientService.queryMemberByOpenId("openId")).thenReturn(listResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hsmcw/member/queryMemberByOpenId")
                        .param("openId", "openId")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("[]");
    }

    @Test
    public void testLoginByPhone() throws Exception {
        // Setup
        // Configure HsaBaseClientService.loginByPhone(...).
        final ResponseLoginResult responseLoginResult = new ResponseLoginResult();
        responseLoginResult.setSuccess(false);
        responseLoginResult.setNeedConfirmChangeBind(false);
        responseLoginResult.setResultCode(0);
        responseLoginResult.setMessage("message");
        final ResponseMemberAccount memberAccount = new ResponseMemberAccount();
        responseLoginResult.setMemberAccount(memberAccount);
        final ResponseModel<ResponseLoginResult> responseLoginResultResponseModel = new ResponseModel<>(
                responseLoginResult);
        final RequestMemberLogin request = new RequestMemberLogin();
        request.setSourceType(0);
        request.setSmsCode("smsCode");
        request.setPhoneNum("phoneNum");
        request.setOpenId("openId");
        request.setUnionId("unionId");
        when(mockHsaBaseClientService.loginByPhone(request)).thenReturn(responseLoginResultResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hsmcw/member/loginByPhone")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testRegisterAndLogin() throws Exception {
        // Setup
        // Configure HsaBaseClientService.registerAndLogin(...).
        final ResponseLoginResult responseLoginResult = new ResponseLoginResult();
        responseLoginResult.setSuccess(false);
        responseLoginResult.setNeedConfirmChangeBind(false);
        responseLoginResult.setResultCode(0);
        responseLoginResult.setMessage("message");
        final ResponseMemberAccount memberAccount = new ResponseMemberAccount();
        responseLoginResult.setMemberAccount(memberAccount);
        final ResponseModel<ResponseLoginResult> responseLoginResultResponseModel = new ResponseModel<>(
                responseLoginResult);
        final RequestMemberBasic request = new RequestMemberBasic();
        request.setMemberInfoGuid("memberInfoGuid");
        request.setPhoneNum("newPhoneNum");
        request.setMemberEnterpriseGuid("memberEnterpriseGuid");
        request.setMemberEnterpriseName("memberEnterpriseName");
        request.setOpenid("openid");
        when(mockHsaBaseClientService.registerAndLogin(request)).thenReturn(responseLoginResultResponseModel);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/hsmcw/member/registerAndLogin")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetMemberState2() throws Exception {
        // Setup
        when(mockHsaBaseClientService.isNeedRegister(0)).thenReturn(new ResponseModel<>(false));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/hsmcw/member/isNeedRegister")
                        .param("channel", "0")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }
}
