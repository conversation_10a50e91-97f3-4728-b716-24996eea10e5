package com.holderzone.holder.saas.aggregation.app.manage;

import com.holderzone.holder.saas.aggregation.app.service.feign.takeout.TakeoutClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInOrderClientService;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderItemQuery;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustOrderQueryDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustOrderReqDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustTakeoutOrderReqDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderItemRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustOrderDetailRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AdjustOrderManageTest {

    @Mock
    private DineInOrderClientService mockDineInOrderClientService;
    @Mock
    private TakeoutClientService mockTakeoutClientService;

    @InjectMocks
    private AdjustOrderManage adjustOrderManageUnderTest;

    @Test
    public void testQuery() {
        // Setup
        final AdjustOrderQueryDTO queryDTO = new AdjustOrderQueryDTO();
        queryDTO.setOrderGuid(0L);
        queryDTO.setAdjustOrderGuid(0L);
        queryDTO.setOrderItemGuids(Arrays.asList(0L));
        queryDTO.setOrderGuids(Arrays.asList(0L));

        final AdjustByOrderRespDTO expectedResult = new AdjustByOrderRespDTO();
        final AdjustByOrderItemRespDTO adjustByOrderItemRespDTO = new AdjustByOrderItemRespDTO();
        adjustByOrderItemRespDTO.setOrderItemGuid("orderItemGuid");
        adjustByOrderItemRespDTO.setAdjustType(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setAdjustType(0);
        adjustByOrderItemRespDTO.setAdjustItemList(Arrays.asList(dineInItemDTO));
        expectedResult.setOrderItemList(Arrays.asList(adjustByOrderItemRespDTO));
        expectedResult.setAdjustNo("adjustNo");
        expectedResult.setAdjustPrice(new BigDecimal("0.00"));
        expectedResult.setReason("reason");
        expectedResult.setCreateStaffName("createStaffName");
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure DineInOrderClientService.queryAdjustOrder(...).
        final AdjustOrderDetailRespDTO adjustOrderDetailRespDTO = new AdjustOrderDetailRespDTO();
        adjustOrderDetailRespDTO.setOrderGuid(0L);
        adjustOrderDetailRespDTO.setTradeMode(0);
        adjustOrderDetailRespDTO.setAdjustNo("adjustNo");
        adjustOrderDetailRespDTO.setAdjustPrice(new BigDecimal("0.00"));
        adjustOrderDetailRespDTO.setReason("reason");
        adjustOrderDetailRespDTO.setCreateStaffName("createStaffName");
        adjustOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setAdjustType(0);
        adjustOrderDetailRespDTO.setAdjustItemList(Arrays.asList(dineInItemDTO1));
        final AdjustOrderQueryDTO queryDTO1 = new AdjustOrderQueryDTO();
        queryDTO1.setOrderGuid(0L);
        queryDTO1.setAdjustOrderGuid(0L);
        queryDTO1.setOrderItemGuids(Arrays.asList(0L));
        queryDTO1.setOrderGuids(Arrays.asList(0L));
        when(mockDineInOrderClientService.queryAdjustOrder(queryDTO1)).thenReturn(adjustOrderDetailRespDTO);

        // Configure TakeoutClientService.listOrderItem(...).
        final AdjustByOrderRespDTO adjustByOrderRespDTO = new AdjustByOrderRespDTO();
        final AdjustByOrderItemRespDTO adjustByOrderItemRespDTO1 = new AdjustByOrderItemRespDTO();
        adjustByOrderItemRespDTO1.setOrderItemGuid("orderItemGuid");
        adjustByOrderItemRespDTO1.setAdjustType(0);
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setAdjustType(0);
        adjustByOrderItemRespDTO1.setAdjustItemList(Arrays.asList(dineInItemDTO2));
        adjustByOrderRespDTO.setOrderItemList(Arrays.asList(adjustByOrderItemRespDTO1));
        adjustByOrderRespDTO.setAdjustNo("adjustNo");
        adjustByOrderRespDTO.setAdjustPrice(new BigDecimal("0.00"));
        adjustByOrderRespDTO.setReason("reason");
        adjustByOrderRespDTO.setCreateStaffName("createStaffName");
        adjustByOrderRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final AdjustByOrderItemQuery query = new AdjustByOrderItemQuery();
        query.setTradeMode(0);
        query.setOrderGuid("orderGuid");
        when(mockTakeoutClientService.listOrderItem(query)).thenReturn(adjustByOrderRespDTO);

        // Configure DineInOrderClientService.listOrderItem(...).
        final AdjustByOrderRespDTO adjustByOrderRespDTO1 = new AdjustByOrderRespDTO();
        final AdjustByOrderItemRespDTO adjustByOrderItemRespDTO2 = new AdjustByOrderItemRespDTO();
        adjustByOrderItemRespDTO2.setOrderItemGuid("orderItemGuid");
        adjustByOrderItemRespDTO2.setAdjustType(0);
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setAdjustType(0);
        adjustByOrderItemRespDTO2.setAdjustItemList(Arrays.asList(dineInItemDTO3));
        adjustByOrderRespDTO1.setOrderItemList(Arrays.asList(adjustByOrderItemRespDTO2));
        adjustByOrderRespDTO1.setAdjustNo("adjustNo");
        adjustByOrderRespDTO1.setAdjustPrice(new BigDecimal("0.00"));
        adjustByOrderRespDTO1.setReason("reason");
        adjustByOrderRespDTO1.setCreateStaffName("createStaffName");
        adjustByOrderRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final AdjustByOrderItemQuery query1 = new AdjustByOrderItemQuery();
        query1.setTradeMode(0);
        query1.setOrderGuid("orderGuid");
        when(mockDineInOrderClientService.listOrderItem(query1)).thenReturn(adjustByOrderRespDTO1);

        // Run the test
        final AdjustByOrderRespDTO result = adjustOrderManageUnderTest.query(queryDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCreate() {
        // Setup
        final AdjustOrderReqDTO reqDTO = new AdjustOrderReqDTO();
        reqDTO.setTradeMode(0);
        reqDTO.setOrderGuid(0L);
        final AdjustOrderReqDTO.AdjustOrderItem adjustOrderItem = new AdjustOrderReqDTO.AdjustOrderItem();
        adjustOrderItem.setOrderItemGuid("orderItemGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setAdjustType(0);
        adjustOrderItem.setItemList(Arrays.asList(dineInItemDTO));
        reqDTO.setOrderItemList(Arrays.asList(adjustOrderItem));

        // Configure DineInOrderClientService.createAdjustOrder(...).
        final AdjustOrderReqDTO reqDTO1 = new AdjustOrderReqDTO();
        reqDTO1.setTradeMode(0);
        reqDTO1.setOrderGuid(0L);
        final AdjustOrderReqDTO.AdjustOrderItem adjustOrderItem1 = new AdjustOrderReqDTO.AdjustOrderItem();
        adjustOrderItem1.setOrderItemGuid("orderItemGuid");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setAdjustType(0);
        adjustOrderItem1.setItemList(Arrays.asList(dineInItemDTO1));
        reqDTO1.setOrderItemList(Arrays.asList(adjustOrderItem1));
        when(mockDineInOrderClientService.createAdjustOrder(reqDTO1)).thenReturn(0L);

        // Run the test
        final Long result = adjustOrderManageUnderTest.create(reqDTO);

        // Verify the results
        assertEquals(Long.valueOf(0L), result);
        verify(mockTakeoutClientService).adjustOrder(new AdjustTakeoutOrderReqDTO("orderGuid", Arrays.asList("value")));
    }
}
