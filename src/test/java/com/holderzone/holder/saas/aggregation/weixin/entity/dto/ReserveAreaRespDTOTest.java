package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class ReserveAreaRespDTOTest {

    private ReserveAreaRespDTO reserveAreaRespDTOUnderTest;

    @Before
    public void setUp() throws Exception {
        reserveAreaRespDTOUnderTest = new ReserveAreaRespDTO("areaGuid", "areaName",
                Arrays.asList(new ReserveTableRespDTO("tableGuid", "tableCode", 0, Arrays.asList("value"))),
                Arrays.asList(0), Arrays.asList("value"));
    }

    @Test
    public void testAreaGuidGetterAndSetter() {
        final String areaGuid = "areaGuid";
        reserveAreaRespDTOUnderTest.setAreaGuid(areaGuid);
        assertThat(reserveAreaRespDTOUnderTest.getAreaGuid()).isEqualTo(areaGuid);
    }

    @Test
    public void testAreaNameGetterAndSetter() {
        final String areaName = "areaName";
        reserveAreaRespDTOUnderTest.setAreaName(areaName);
        assertThat(reserveAreaRespDTOUnderTest.getAreaName()).isEqualTo(areaName);
    }

    @Test
    public void testReserveTableRespDTOSGetterAndSetter() {
        final List<ReserveTableRespDTO> reserveTableRespDTOS = Arrays.asList(
                new ReserveTableRespDTO("tableGuid", "tableCode", 0, Arrays.asList("value")));
        reserveAreaRespDTOUnderTest.setReserveTableRespDTOS(reserveTableRespDTOS);
        assertThat(reserveAreaRespDTOUnderTest.getReserveTableRespDTOS()).isEqualTo(reserveTableRespDTOS);
    }

    @Test
    public void testAreaTableSeatsGetterAndSetter() {
        final List<Integer> areaTableSeats = Arrays.asList(0);
        reserveAreaRespDTOUnderTest.setAreaTableSeats(areaTableSeats);
        assertThat(reserveAreaRespDTOUnderTest.getAreaTableSeats()).isEqualTo(areaTableSeats);
    }

    @Test
    public void testAreaTableTagsGetterAndSetter() {
        final List<String> areaTableTags = Arrays.asList("value");
        reserveAreaRespDTOUnderTest.setAreaTableTags(areaTableTags);
        assertThat(reserveAreaRespDTOUnderTest.getAreaTableTags()).isEqualTo(areaTableTags);
    }

    @Test
    public void testEquals() throws Exception {
        assertThat(reserveAreaRespDTOUnderTest.equals("o")).isFalse();
    }

    @Test
    public void testCanEqual() throws Exception {
        assertThat(reserveAreaRespDTOUnderTest.canEqual("other")).isFalse();
    }

    @Test
    public void testHashCode() throws Exception {
        assertThat(reserveAreaRespDTOUnderTest.hashCode()).isEqualTo(0);
    }

    @Test
    public void testToString() throws Exception {
        assertThat(reserveAreaRespDTOUnderTest.toString()).isEqualTo("result");
    }
}
