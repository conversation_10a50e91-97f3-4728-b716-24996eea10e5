package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxOpenMessageHandleClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxUserRecordClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.TableClientService;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.entry.dto.WxUserInfoDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(AuthController.class)
public class AuthControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private RedisUtils mockRedisUtils;
    @MockBean
    private WxUserRecordClientService mockWxUserRecordClientService;
    @MockBean
    private TableClientService mockTableClientService;
    @MockBean
    private WxOpenMessageHandleClientService mockWxOpenMessageHandleClientService;

    @Test
    public void testGetToken() throws Exception {
        // Setup
        when(mockWxOpenMessageHandleClientService.getToken(new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0))).thenReturn("token");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/auth/getToken")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("expectedResponse");
    }

    @Test
    public void testGetToken_WxOpenMessageHandleClientServiceReturnsNull() throws Exception {
        // Setup
        when(mockWxOpenMessageHandleClientService.getToken(new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0))).thenReturn(null);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/auth/getToken")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("");
    }
}
