package com.holderzone.holder.saas.store.table.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.store.table.domain.AreaDO;
import com.holderzone.holder.saas.store.table.domain.TableBasicDO;
import com.holderzone.holder.saas.store.table.domain.TableOrderDO;
import com.holderzone.holder.saas.store.table.mapper.AreaMapper;
import com.holderzone.holder.saas.store.table.mapper.TableBasicMapper;
import com.holderzone.holder.saas.store.table.service.RedisService;
import com.holderzone.holder.saas.store.table.service.TableBasicService;
import com.holderzone.holder.saas.store.table.service.TableOrderService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.table.AreaDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AreaServiceImplTest {

    @Mock
    private AreaMapper mockAreaMapper;
    @Mock
    private RedisService mockRedisService;
    @Mock
    private TableBasicService mockTableBasicService;
    @Mock
    private TableOrderService mockTableOrderService;
    @Mock
    private TableBasicMapper mockTableBasicMapper;

    private AreaServiceImpl areaServiceImplUnderTest;

    @Before
    public void setUp() {
        areaServiceImplUnderTest = new AreaServiceImpl(mockAreaMapper, mockRedisService, mockTableBasicService,
                mockTableOrderService, mockTableBasicMapper);
    }

    @Test
    public void testInitArea() {
        // Setup
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setStoreGuid("storeGuid");
        baseDTO.setStoreName("storeName");

        when(mockRedisService.singleGuid("hst_area")).thenReturn("tableGuid");
        when(mockRedisService.batchGuid(10, "hst_table_basic")).thenReturn(Arrays.asList("value"));

        // Run the test
        final String result = areaServiceImplUnderTest.initArea(baseDTO);

        // Verify the results
        assertThat(result).isEqualTo("SUCCESS");

        // Confirm AreaMapper.insert(...).
        final AreaDO entity = new AreaDO();
        entity.setGuid("tableGuid");
        entity.setStoreGuid("storeGuid");
        entity.setAreaName("areaName");
        entity.setSort(0);
        entity.setDeleted(0);
        verify(mockAreaMapper).insert(entity);

        // Confirm TableBasicService.saveBatch(...).
        final TableBasicDO tableBasicDO = new TableBasicDO();
        tableBasicDO.setGuid("tableGuid");
        tableBasicDO.setStoreGuid("storeGuid");
        tableBasicDO.setAreaGuid("areaGuid");
        tableBasicDO.setAreaName("areaName");
        tableBasicDO.setTableCode("tableCode");
        tableBasicDO.setSort(0);
        final List<TableBasicDO> entityList = Arrays.asList(tableBasicDO);
        verify(mockTableBasicService).saveBatch(entityList, 0);

        // Confirm TableOrderService.saveBatch(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setGuid("f02b2ec0-12f5-4c9c-8298-3c70db05ad30");
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("orderGuid");
        final List<TableOrderDO> entityList1 = Arrays.asList(tableOrderDO);
        verify(mockTableOrderService).saveBatch(entityList1, 0);
    }

    @Test
    public void testInitArea_RedisServiceBatchGuidReturnsNoItems() {
        // Setup
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setStoreGuid("storeGuid");
        baseDTO.setStoreName("storeName");

        when(mockRedisService.singleGuid("hst_area")).thenReturn("tableGuid");
        when(mockRedisService.batchGuid(10, "hst_table_basic")).thenReturn(Collections.emptyList());

        // Run the test
        final String result = areaServiceImplUnderTest.initArea(baseDTO);

        // Verify the results
        assertThat(result).isEqualTo("SUCCESS");

        // Confirm AreaMapper.insert(...).
        final AreaDO entity = new AreaDO();
        entity.setGuid("tableGuid");
        entity.setStoreGuid("storeGuid");
        entity.setAreaName("areaName");
        entity.setSort(0);
        entity.setDeleted(0);
        verify(mockAreaMapper).insert(entity);

        // Confirm TableBasicService.saveBatch(...).
        final TableBasicDO tableBasicDO = new TableBasicDO();
        tableBasicDO.setGuid("tableGuid");
        tableBasicDO.setStoreGuid("storeGuid");
        tableBasicDO.setAreaGuid("areaGuid");
        tableBasicDO.setAreaName("areaName");
        tableBasicDO.setTableCode("tableCode");
        tableBasicDO.setSort(0);
        final List<TableBasicDO> entityList = Arrays.asList(tableBasicDO);
        verify(mockTableBasicService).saveBatch(entityList, 0);

        // Confirm TableOrderService.saveBatch(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setGuid("f02b2ec0-12f5-4c9c-8298-3c70db05ad30");
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("orderGuid");
        final List<TableOrderDO> entityList1 = Arrays.asList(tableOrderDO);
        verify(mockTableOrderService).saveBatch(entityList1, 0);
    }

    @Test
    public void testCreateArea_ThrowsBusinessException() {
        // Setup
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("1ea7cc14-c627-4dea-963b-8cd61b7350d1");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("areaName");
        areaDTO.setSort(0);

        when(mockAreaMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        assertThatThrownBy(() -> areaServiceImplUnderTest.createArea(areaDTO)).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testCreateArea_AreaMapperSelectCountReturnsNull() {
        // Setup
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("1ea7cc14-c627-4dea-963b-8cd61b7350d1");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("areaName");
        areaDTO.setSort(0);

        when(mockAreaMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(mockRedisService.singleGuid("hst_area")).thenReturn("tableGuid");
        when(mockAreaMapper.maxSort("storeGuid")).thenReturn(0);

        // Run the test
        final String result = areaServiceImplUnderTest.createArea(areaDTO);

        // Verify the results
        assertThat(result).isEqualTo("SUCCESS");
    }

    @Test
    public void testUpdateArea_ThrowsBusinessException() {
        // Setup
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("1ea7cc14-c627-4dea-963b-8cd61b7350d1");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("areaName");
        areaDTO.setSort(0);

        // Configure AreaMapper.selectOne(...).
        final AreaDO areaDO = new AreaDO();
        areaDO.setGuid("tableGuid");
        areaDO.setStoreGuid("storeGuid");
        areaDO.setAreaName("areaName");
        areaDO.setSort(0);
        areaDO.setDeleted(0);
        when(mockAreaMapper.selectOne(any(Wrapper.class))).thenReturn(areaDO);

        when(mockAreaMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        assertThatThrownBy(() -> areaServiceImplUnderTest.updateArea(areaDTO)).isInstanceOf(BusinessException.class);
    }

    @Test
    public void testUpdateArea_AreaMapperSelectCountReturnsNull() {
        // Setup
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("1ea7cc14-c627-4dea-963b-8cd61b7350d1");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("areaName");
        areaDTO.setSort(0);

        // Configure AreaMapper.selectOne(...).
        final AreaDO areaDO = new AreaDO();
        areaDO.setGuid("tableGuid");
        areaDO.setStoreGuid("storeGuid");
        areaDO.setAreaName("areaName");
        areaDO.setSort(0);
        areaDO.setDeleted(0);
        when(mockAreaMapper.selectOne(any(Wrapper.class))).thenReturn(areaDO);

        when(mockAreaMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure AreaMapper.update(...).
        final AreaDO entity = new AreaDO();
        entity.setGuid("tableGuid");
        entity.setStoreGuid("storeGuid");
        entity.setAreaName("areaName");
        entity.setSort(0);
        entity.setDeleted(0);
        when(mockAreaMapper.update(eq(entity), any(Wrapper.class))).thenReturn(0);

        // Run the test
        final String result = areaServiceImplUnderTest.updateArea(areaDTO);

        // Verify the results
        assertThat(result).isEqualTo("SUCCESS");

        // Confirm TableBasicMapper.update(...).
        final TableBasicDO entity1 = new TableBasicDO();
        entity1.setGuid("tableGuid");
        entity1.setStoreGuid("storeGuid");
        entity1.setAreaGuid("areaGuid");
        entity1.setAreaName("areaName");
        entity1.setTableCode("tableCode");
        entity1.setSort(0);
        verify(mockTableBasicMapper).update(eq(entity1), any(LambdaQueryWrapper.class));
    }

    @Test
    public void testDeleteArea() {
        // Setup
        when(mockTableBasicService.isTableOccupied("6bd4e9a1-f615-4032-844a-c352dbb0dd4c")).thenReturn(false);
        when(mockAreaMapper.delete(any(Wrapper.class))).thenReturn(0);

        // Run the test
        final String result = areaServiceImplUnderTest.deleteArea("6bd4e9a1-f615-4032-844a-c352dbb0dd4c");

        // Verify the results
        assertThat(result).isEqualTo("SUCCESS");
    }

    @Test
    public void testDeleteArea_TableBasicServiceReturnsTrue() {
        // Setup
        when(mockTableBasicService.isTableOccupied("6bd4e9a1-f615-4032-844a-c352dbb0dd4c")).thenReturn(true);

        // Run the test
        assertThatThrownBy(
                () -> areaServiceImplUnderTest.deleteArea("6bd4e9a1-f615-4032-844a-c352dbb0dd4c"))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testQueryAll() {
        // Setup
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("1ea7cc14-c627-4dea-963b-8cd61b7350d1");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("areaName");
        areaDTO.setSort(0);
        final List<AreaDTO> expectedResult = Arrays.asList(areaDTO);

        // Configure AreaMapper.selectList(...).
        final AreaDO areaDO = new AreaDO();
        areaDO.setGuid("tableGuid");
        areaDO.setStoreGuid("storeGuid");
        areaDO.setAreaName("areaName");
        areaDO.setSort(0);
        areaDO.setDeleted(0);
        final List<AreaDO> areaDOS = Arrays.asList(areaDO);
        when(mockAreaMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(areaDOS);

        // Run the test
        final List<AreaDTO> result = areaServiceImplUnderTest.queryAll("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryAll_AreaMapperReturnsNoItems() {
        // Setup
        when(mockAreaMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<AreaDTO> result = areaServiceImplUnderTest.queryAll("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryAreaByTable() {
        // Setup
        final AreaDTO expectedResult = new AreaDTO();
        expectedResult.setGuid("1ea7cc14-c627-4dea-963b-8cd61b7350d1");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setAreaName("areaName");
        expectedResult.setSort(0);

        // Run the test
        final AreaDTO result = areaServiceImplUnderTest.queryAreaByTable("areaGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryBatchAreaByTable() {
        // Setup
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid("1ea7cc14-c627-4dea-963b-8cd61b7350d1");
        areaDTO.setStoreGuid("storeGuid");
        areaDTO.setStoreName("storeName");
        areaDTO.setAreaName("areaName");
        areaDTO.setSort(0);
        final List<AreaDTO> expectedResult = Arrays.asList(areaDTO);

        // Run the test
        final List<AreaDTO> result = areaServiceImplUnderTest.queryBatchAreaByTable(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
