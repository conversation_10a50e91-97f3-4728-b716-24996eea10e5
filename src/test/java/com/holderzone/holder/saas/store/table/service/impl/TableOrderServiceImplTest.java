package com.holderzone.holder.saas.store.table.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.holder.saas.store.table.client.TradeRpcClient;
import com.holderzone.holder.saas.store.table.config.AutoDelayRelease;
import com.holderzone.holder.saas.store.table.domain.TableDO;
import com.holderzone.holder.saas.store.table.domain.TableOrderDO;
import com.holderzone.holder.saas.store.table.domain.bo.DelayAutoUnlockBO;
import com.holderzone.holder.saas.store.table.domain.bo.TableInfoBO;
import com.holderzone.holder.saas.store.table.mapper.TableBasicMapper;
import com.holderzone.holder.saas.store.table.mapper.TableOrderMapper;
import com.holderzone.holder.saas.store.table.service.*;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.ReserveBatchCreateOrderReqDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.table.*;
import com.holderzone.saas.store.dto.table.trade.TradeTableDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreTableCombineDTO;
import org.apache.rocketmq.common.message.Message;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TableOrderServiceImplTest {

    @Mock
    private TableOrderMapper mockTableOrderMapper;
    @Mock
    private RedisService mockRedisService;
    @Mock
    private TradeService mockTradeService;
    @Mock
    private ReserveService mockReserveService;
    @Mock
    private BizMsgService mockBizMsgService;
    @Mock
    private AutoDelayRelease mockRelease;
    @Mock
    private PrintService mockPrintService;
    @Mock
    private DefaultRocketMqProducer mockDefaultRocketMqProducer;
    @Mock
    private TradeRpcClient mockTradeRpcClient;
    @Mock
    private BindUpAccountsService mockBindUpAccountsService;
    @Mock
    private TableBasicMapper mockTableBasicMapper;
    @Mock
    private AreaService mockAreaService;
    @Mock
    private TableAssociatedService tableAssociatedService;

    private TableOrderServiceImpl tableOrderServiceImplUnderTest;

    @Before
    public void setUp() {
        tableOrderServiceImplUnderTest = new TableOrderServiceImpl(mockTableOrderMapper, mockRedisService,
                mockTradeService, mockReserveService, mockBizMsgService, mockRelease, mockPrintService, mockDefaultRocketMqProducer,
                mockTradeRpcClient, mockBindUpAccountsService, mockTableBasicMapper, tableAssociatedService);
        ReflectionTestUtils.setField(tableOrderServiceImplUnderTest, "memberCenterHostUrl", "memberCenterHostUrl");
        ReflectionTestUtils.setField(tableOrderServiceImplUnderTest, "areaService", mockAreaService);
    }

    @Test
    public void testListTable() {
        // Setup
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setDeviceType(0);
        tableBasicQueryDTO.setDeviceId("deviceId");
        tableBasicQueryDTO.setEnterpriseGuid("enterpriseGuid");
        tableBasicQueryDTO.setStoreName("storeName");
        tableBasicQueryDTO.setUserGuid("openStaffGuid");
        tableBasicQueryDTO.setUserName("openStaffName");
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));

        final TableOrderDTO tableOrderDTO = new TableOrderDTO();
        tableOrderDTO.setDeviceType(0);
        tableOrderDTO.setDeviceId("deviceId");
        tableOrderDTO.setEnterpriseGuid("enterpriseGuid");
        tableOrderDTO.setStoreGuid("storeGuid");
        tableOrderDTO.setStoreName("storeName");
        tableOrderDTO.setUserGuid("openStaffGuid");
        tableOrderDTO.setUserName("openStaffName");
        tableOrderDTO.setTableGuid("tableGuid");
        tableOrderDTO.setStatus(0);
        tableOrderDTO.setSubStatus(new HashSet<>(Arrays.asList(0)));
        tableOrderDTO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDTO> expectedResult = Arrays.asList(tableOrderDTO);

        // Configure TableOrderMapper.selectAllTable(...).
        final TableDO tableDO = new TableDO();
        tableDO.setGuid("areaGuid");
        tableDO.setStoreGuid("storeGuid");
        tableDO.setStoreName("storeName");
        tableDO.setAreaGuid("areaGuid");
        tableDO.setAreaName("areaName");
        tableDO.setTableCode("tableCode");
        tableDO.setSeats(0);
        tableDO.setMainOrderGuid("mainOrderGuid");
        tableDO.setOrderGuid("orderGuid");
        tableDO.setStatus(0);
        final List<TableDO> tableDOS = Arrays.asList(tableDO);
        final TableBasicQueryDTO tableBasicQueryDTO1 = new TableBasicQueryDTO();
        tableBasicQueryDTO1.setDeviceType(0);
        tableBasicQueryDTO1.setDeviceId("deviceId");
        tableBasicQueryDTO1.setEnterpriseGuid("enterpriseGuid");
        tableBasicQueryDTO1.setStoreName("storeName");
        tableBasicQueryDTO1.setUserGuid("openStaffGuid");
        tableBasicQueryDTO1.setUserName("openStaffName");
        tableBasicQueryDTO1.setStoreGuid("storeGuid");
        tableBasicQueryDTO1.setTableGuidList(Arrays.asList("value"));
        when(mockTableOrderMapper.selectAllTable(tableBasicQueryDTO1)).thenReturn(tableDOS);

        // Run the test
        final List<TableOrderDTO> result = tableOrderServiceImplUnderTest.listTable(tableBasicQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListTable_TableOrderMapperReturnsNoItems() {
        // Setup
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setDeviceType(0);
        tableBasicQueryDTO.setDeviceId("deviceId");
        tableBasicQueryDTO.setEnterpriseGuid("enterpriseGuid");
        tableBasicQueryDTO.setStoreName("storeName");
        tableBasicQueryDTO.setUserGuid("openStaffGuid");
        tableBasicQueryDTO.setUserName("openStaffName");
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));

        // Configure TableOrderMapper.selectAllTable(...).
        final TableBasicQueryDTO tableBasicQueryDTO1 = new TableBasicQueryDTO();
        tableBasicQueryDTO1.setDeviceType(0);
        tableBasicQueryDTO1.setDeviceId("deviceId");
        tableBasicQueryDTO1.setEnterpriseGuid("enterpriseGuid");
        tableBasicQueryDTO1.setStoreName("storeName");
        tableBasicQueryDTO1.setUserGuid("openStaffGuid");
        tableBasicQueryDTO1.setUserName("openStaffName");
        tableBasicQueryDTO1.setStoreGuid("storeGuid");
        tableBasicQueryDTO1.setTableGuidList(Arrays.asList("value"));
        when(mockTableOrderMapper.selectAllTable(tableBasicQueryDTO1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<TableOrderDTO> result = tableOrderServiceImplUnderTest.listTable(tableBasicQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testListTableOccupied() {
        // Setup
        // Configure TableOrderMapper.selectList(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> tableOrderDOS = Arrays.asList(tableOrderDO);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDOS);

        // Run the test
        final List<String> result = tableOrderServiceImplUnderTest.listTableOccupied(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
    }

    @Test
    public void testListTableOccupied_TableOrderMapperReturnsNoItems() {
        // Setup
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = tableOrderServiceImplUnderTest.listTableOccupied(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testOpen1() {
        // Setup
        final OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        openTableDTO.setTableGuid("areaGuid");
        openTableDTO.setTableCode("tableCode");
        openTableDTO.setAreaName("areaName");
        openTableDTO.setActualGuestsNo(0);
        openTableDTO.setAreaGuid("areaGuid");

        // Configure TableOrderMapper.selectOne(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDO);

        when(mockRedisService.singleGuid("hst_order")).thenReturn("8379e3a2-032a-484c-90ec-91cdcaf186b6");

        // Configure TableOrderMapper.update(...).
        final TableOrderDO entity = new TableOrderDO();
        entity.setId(0L);
        entity.setTableGuid("tableGuid");
        entity.setMainOrderGuid("mainOrderGuid");
        entity.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        entity.setStatus(0);
        entity.setSubStatus("subStatus");
        entity.setCombineTimes(0);
        entity.setOpenStaffGuid("openStaffGuid");
        entity.setOpenStaffName("openStaffName");
        entity.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.update(eq(entity), any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO1 = new BaseDTO();
        openTableDTO1.setDeviceType(0);
        openTableDTO1.setDeviceId("deviceId");
        openTableDTO1.setEnterpriseGuid("enterpriseGuid");
        openTableDTO1.setStoreGuid("storeGuid");
        openTableDTO1.setStoreName("storeName");
        openTableDTO1.setUserGuid("openStaffGuid");
        openTableDTO1.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO1, Arrays.asList("value"))).thenReturn("result");

        // Run the test
        final String result = tableOrderServiceImplUnderTest.open(openTableDTO, 0);

        // Verify the results
        assertThat(result).isEqualTo("8379e3a2-032a-484c-90ec-91cdcaf186b6");

        // Confirm TradeService.openTable(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceType(0);
        createDineInOrderReqDTO.setDeviceId("deviceId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        createDineInOrderReqDTO.setStoreName("storeName");
        createDineInOrderReqDTO.setUserGuid("openStaffGuid");
        createDineInOrderReqDTO.setUserName("openStaffName");
        createDineInOrderReqDTO.setGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        createDineInOrderReqDTO.setDiningTableGuid("areaGuid");
        createDineInOrderReqDTO.setDiningTableName("tableCode");
        createDineInOrderReqDTO.setAreaName("areaName");
        createDineInOrderReqDTO.setGuestCount(0);
        createDineInOrderReqDTO.setAreaGuid("areaGuid");
        verify(mockTradeService).openTable(createDineInOrderReqDTO);
    }

    @Test
    public void testOpen1_TableOrderMapperSelectOneReturnsNull() {
        // Setup
        final OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        openTableDTO.setTableGuid("areaGuid");
        openTableDTO.setTableCode("tableCode");
        openTableDTO.setAreaName("areaName");
        openTableDTO.setActualGuestsNo(0);
        openTableDTO.setAreaGuid("areaGuid");

        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> tableOrderServiceImplUnderTest.open(openTableDTO, 0))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testBatchOpen() {
        // Setup
        final BatchOpenTableDTO batchOpenTableDTO = new BatchOpenTableDTO();
        final OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        openTableDTO.setTableGuid("areaGuid");
        openTableDTO.setTableCode("tableCode");
        openTableDTO.setAreaName("areaName");
        openTableDTO.setActualGuestsNo(0);
        openTableDTO.setAreaGuid("areaGuid");
        batchOpenTableDTO.setOpenTableDTOS(Arrays.asList(openTableDTO));
        batchOpenTableDTO.setReserveGuid("reserveGuid");
        batchOpenTableDTO.setContainDish(false);
        batchOpenTableDTO.setReserveAmount(new BigDecimal("0.00"));
        batchOpenTableDTO.setReservePhone("reservePhone");

        final ReserveOpenTableDTO expectedResult = new ReserveOpenTableDTO(Arrays.asList("value"),
                Arrays.asList("value"), Lists.newArrayList("noExistTables"), false, "mainOrderGuid", "mainTableGuid");

        // Configure TableOrderMapper.selectAllTable(...).
        final TableDO tableDO = new TableDO();
        tableDO.setGuid("areaGuid");
        tableDO.setStoreGuid("storeGuid");
        tableDO.setStoreName("storeName");
        tableDO.setAreaGuid("areaGuid");
        tableDO.setAreaName("areaName");
        tableDO.setTableCode("tableCode");
        tableDO.setSeats(0);
        tableDO.setMainOrderGuid("mainOrderGuid");
        tableDO.setOrderGuid("orderGuid");
        tableDO.setStatus(0);
        final List<TableDO> tableDOS = Arrays.asList(tableDO);
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setDeviceType(0);
        tableBasicQueryDTO.setDeviceId("deviceId");
        tableBasicQueryDTO.setEnterpriseGuid("enterpriseGuid");
        tableBasicQueryDTO.setStoreName("storeName");
        tableBasicQueryDTO.setUserGuid("openStaffGuid");
        tableBasicQueryDTO.setUserName("openStaffName");
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableOrderMapper.selectAllTable(tableBasicQueryDTO)).thenReturn(tableDOS);

        when(mockRedisService.batchGuid(0, "hst_order")).thenReturn(Arrays.asList("value"));

        // Configure TableOrderMapper.update(...).
        final TableOrderDO entity = new TableOrderDO();
        entity.setId(0L);
        entity.setTableGuid("tableGuid");
        entity.setMainOrderGuid("mainOrderGuid");
        entity.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        entity.setStatus(0);
        entity.setSubStatus("subStatus");
        entity.setCombineTimes(0);
        entity.setOpenStaffGuid("openStaffGuid");
        entity.setOpenStaffName("openStaffName");
        entity.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.update(eq(entity), any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO1 = new BaseDTO();
        openTableDTO1.setDeviceType(0);
        openTableDTO1.setDeviceId("deviceId");
        openTableDTO1.setEnterpriseGuid("enterpriseGuid");
        openTableDTO1.setStoreGuid("storeGuid");
        openTableDTO1.setStoreName("storeName");
        openTableDTO1.setUserGuid("openStaffGuid");
        openTableDTO1.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO1, Arrays.asList("value"))).thenReturn("result");

        when(mockRedisService.isTableLockedByOthers("deviceId", "mainTableGuid")).thenReturn(false);

        // Configure TableOrderMapper.selectList(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> tableOrderDOS = Arrays.asList(tableOrderDO);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDOS);

        when(mockRedisService.getCombineTimes("storeGuid")).thenReturn(0);
        when(mockRedisService.singleGuid("hst_order")).thenReturn("8379e3a2-032a-484c-90ec-91cdcaf186b6");

        // Configure TableOrderMapper.selectFullInfo(...).
        final TableDO tableDO1 = new TableDO();
        tableDO1.setGuid("areaGuid");
        tableDO1.setStoreGuid("storeGuid");
        tableDO1.setStoreName("storeName");
        tableDO1.setAreaGuid("areaGuid");
        tableDO1.setAreaName("areaName");
        tableDO1.setTableCode("tableCode");
        tableDO1.setSeats(0);
        tableDO1.setMainOrderGuid("mainOrderGuid");
        tableDO1.setOrderGuid("orderGuid");
        tableDO1.setStatus(0);
        when(mockTableOrderMapper.selectFullInfo("tableGuid")).thenReturn(tableDO1);

        // Run the test
        final ReserveOpenTableDTO result = tableOrderServiceImplUnderTest.batchOpen(batchOpenTableDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm TradeService.bacthopenTable(...).
        final ReserveBatchCreateOrderReqDTO batchCreateOrderReqDTO = new ReserveBatchCreateOrderReqDTO();
        batchCreateOrderReqDTO.setDeviceType(0);
        batchCreateOrderReqDTO.setDeviceId("deviceId");
        batchCreateOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        batchCreateOrderReqDTO.setStoreGuid("storeGuid");
        batchCreateOrderReqDTO.setStoreName("storeName");
        batchCreateOrderReqDTO.setUserGuid("openStaffGuid");
        batchCreateOrderReqDTO.setUserName("openStaffName");
        batchCreateOrderReqDTO.setReserveGuid("reserveGuid");
        batchCreateOrderReqDTO.setMainOrderGuid("mainOrderGuid");
        batchCreateOrderReqDTO.setContainDish(false);
        batchCreateOrderReqDTO.setReserveFee(new BigDecimal("0.00"));
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceType(0);
        createDineInOrderReqDTO.setDeviceId("deviceId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        createDineInOrderReqDTO.setStoreName("storeName");
        createDineInOrderReqDTO.setUserGuid("openStaffGuid");
        createDineInOrderReqDTO.setUserName("openStaffName");
        createDineInOrderReqDTO.setGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        createDineInOrderReqDTO.setDiningTableGuid("areaGuid");
        createDineInOrderReqDTO.setDiningTableName("tableCode");
        createDineInOrderReqDTO.setAreaName("areaName");
        createDineInOrderReqDTO.setGuestCount(0);
        createDineInOrderReqDTO.setAreaGuid("areaGuid");
        batchCreateOrderReqDTO.setCreateDineInOrderReqDTOS(Arrays.asList(createDineInOrderReqDTO));
        batchCreateOrderReqDTO.setReservePhone("reservePhone");
        verify(mockTradeService).bacthopenTable(batchCreateOrderReqDTO);

        // Confirm TradeService.openTable(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO1 = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO1.setDeviceType(0);
        createDineInOrderReqDTO1.setDeviceId("deviceId");
        createDineInOrderReqDTO1.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO1.setStoreGuid("storeGuid");
        createDineInOrderReqDTO1.setStoreName("storeName");
        createDineInOrderReqDTO1.setUserGuid("openStaffGuid");
        createDineInOrderReqDTO1.setUserName("openStaffName");
        createDineInOrderReqDTO1.setGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        createDineInOrderReqDTO1.setDiningTableGuid("areaGuid");
        createDineInOrderReqDTO1.setDiningTableName("tableCode");
        createDineInOrderReqDTO1.setAreaName("areaName");
        createDineInOrderReqDTO1.setGuestCount(0);
        createDineInOrderReqDTO1.setAreaGuid("areaGuid");
        verify(mockTradeService).openTable(createDineInOrderReqDTO1);

        // Confirm TradeService.notifyTradeCombine(...).
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setDeviceType(0);
        tableInfoDTO.setDeviceId("deviceId");
        tableInfoDTO.setEnterpriseGuid("enterpriseGuid");
        tableInfoDTO.setStoreGuid("storeGuid");
        tableInfoDTO.setStoreName("storeName");
        tableInfoDTO.setUserGuid("openStaffGuid");
        tableInfoDTO.setUserName("openStaffName");
        tableInfoDTO.setTableGuid("tableGuid");
        tableInfoDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);
        verify(mockTradeService).notifyTradeCombine(tableOrderCombineDTO);

        // Confirm TradeService.batchRevokeThirdActivity(...).
        final TableCombineDTO tableCombineDTO = new TableCombineDTO();
        tableCombineDTO.setDeviceType(0);
        tableCombineDTO.setDeviceId("deviceId");
        tableCombineDTO.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO.setStoreGuid("storeGuid");
        tableCombineDTO.setStoreName("storeName");
        tableCombineDTO.setUserGuid("openStaffGuid");
        tableCombineDTO.setUserName("openStaffName");
        tableCombineDTO.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO.setCombineTimes(0);
        tableCombineDTO.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO.setMainTableGuid("mainTableGuid");
        verify(mockTradeService).batchRevokeThirdActivity(tableCombineDTO);
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));

        // Confirm BizMsgService.sendMsg(...).
        final TableCombineDTO tableCombineDTO1 = new TableCombineDTO();
        tableCombineDTO1.setDeviceType(0);
        tableCombineDTO1.setDeviceId("deviceId");
        tableCombineDTO1.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO1.setStoreGuid("storeGuid");
        tableCombineDTO1.setStoreName("storeName");
        tableCombineDTO1.setUserGuid("openStaffGuid");
        tableCombineDTO1.setUserName("openStaffName");
        tableCombineDTO1.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO1.setCombineTimes(0);
        tableCombineDTO1.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO1.setMainTableGuid("mainTableGuid");
        verify(mockBizMsgService).sendMsg(tableCombineDTO1);
    }

    @Test
    public void testBatchOpen_TableOrderMapperSelectAllTableReturnsNoItems() {
        // Setup
        final BatchOpenTableDTO batchOpenTableDTO = new BatchOpenTableDTO();
        final OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        openTableDTO.setTableGuid("areaGuid");
        openTableDTO.setTableCode("tableCode");
        openTableDTO.setAreaName("areaName");
        openTableDTO.setActualGuestsNo(0);
        openTableDTO.setAreaGuid("areaGuid");
        batchOpenTableDTO.setOpenTableDTOS(Arrays.asList(openTableDTO));
        batchOpenTableDTO.setReserveGuid("reserveGuid");
        batchOpenTableDTO.setContainDish(false);
        batchOpenTableDTO.setReserveAmount(new BigDecimal("0.00"));
        batchOpenTableDTO.setReservePhone("reservePhone");

        final ReserveOpenTableDTO expectedResult = new ReserveOpenTableDTO(Arrays.asList("value"),
                Arrays.asList("value"), Lists.newArrayList("noExistTables"), false, "mainOrderGuid", "mainTableGuid");

        // Configure TableOrderMapper.selectAllTable(...).
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setDeviceType(0);
        tableBasicQueryDTO.setDeviceId("deviceId");
        tableBasicQueryDTO.setEnterpriseGuid("enterpriseGuid");
        tableBasicQueryDTO.setStoreName("storeName");
        tableBasicQueryDTO.setUserGuid("openStaffGuid");
        tableBasicQueryDTO.setUserName("openStaffName");
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableOrderMapper.selectAllTable(tableBasicQueryDTO)).thenReturn(Collections.emptyList());

        when(mockRedisService.batchGuid(0, "hst_order")).thenReturn(Arrays.asList("value"));

        // Configure TableOrderMapper.update(...).
        final TableOrderDO entity = new TableOrderDO();
        entity.setId(0L);
        entity.setTableGuid("tableGuid");
        entity.setMainOrderGuid("mainOrderGuid");
        entity.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        entity.setStatus(0);
        entity.setSubStatus("subStatus");
        entity.setCombineTimes(0);
        entity.setOpenStaffGuid("openStaffGuid");
        entity.setOpenStaffName("openStaffName");
        entity.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.update(eq(entity), any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO1 = new BaseDTO();
        openTableDTO1.setDeviceType(0);
        openTableDTO1.setDeviceId("deviceId");
        openTableDTO1.setEnterpriseGuid("enterpriseGuid");
        openTableDTO1.setStoreGuid("storeGuid");
        openTableDTO1.setStoreName("storeName");
        openTableDTO1.setUserGuid("openStaffGuid");
        openTableDTO1.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO1, Arrays.asList("value"))).thenReturn("result");

        when(mockRedisService.isTableLockedByOthers("deviceId", "mainTableGuid")).thenReturn(false);

        // Configure TableOrderMapper.selectList(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> tableOrderDOS = Arrays.asList(tableOrderDO);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDOS);

        when(mockRedisService.getCombineTimes("storeGuid")).thenReturn(0);
        when(mockRedisService.singleGuid("hst_order")).thenReturn("8379e3a2-032a-484c-90ec-91cdcaf186b6");

        // Configure TableOrderMapper.selectFullInfo(...).
        final TableDO tableDO = new TableDO();
        tableDO.setGuid("areaGuid");
        tableDO.setStoreGuid("storeGuid");
        tableDO.setStoreName("storeName");
        tableDO.setAreaGuid("areaGuid");
        tableDO.setAreaName("areaName");
        tableDO.setTableCode("tableCode");
        tableDO.setSeats(0);
        tableDO.setMainOrderGuid("mainOrderGuid");
        tableDO.setOrderGuid("orderGuid");
        tableDO.setStatus(0);
        when(mockTableOrderMapper.selectFullInfo("tableGuid")).thenReturn(tableDO);

        // Run the test
        final ReserveOpenTableDTO result = tableOrderServiceImplUnderTest.batchOpen(batchOpenTableDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm TradeService.bacthopenTable(...).
        final ReserveBatchCreateOrderReqDTO batchCreateOrderReqDTO = new ReserveBatchCreateOrderReqDTO();
        batchCreateOrderReqDTO.setDeviceType(0);
        batchCreateOrderReqDTO.setDeviceId("deviceId");
        batchCreateOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        batchCreateOrderReqDTO.setStoreGuid("storeGuid");
        batchCreateOrderReqDTO.setStoreName("storeName");
        batchCreateOrderReqDTO.setUserGuid("openStaffGuid");
        batchCreateOrderReqDTO.setUserName("openStaffName");
        batchCreateOrderReqDTO.setReserveGuid("reserveGuid");
        batchCreateOrderReqDTO.setMainOrderGuid("mainOrderGuid");
        batchCreateOrderReqDTO.setContainDish(false);
        batchCreateOrderReqDTO.setReserveFee(new BigDecimal("0.00"));
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceType(0);
        createDineInOrderReqDTO.setDeviceId("deviceId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        createDineInOrderReqDTO.setStoreName("storeName");
        createDineInOrderReqDTO.setUserGuid("openStaffGuid");
        createDineInOrderReqDTO.setUserName("openStaffName");
        createDineInOrderReqDTO.setGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        createDineInOrderReqDTO.setDiningTableGuid("areaGuid");
        createDineInOrderReqDTO.setDiningTableName("tableCode");
        createDineInOrderReqDTO.setAreaName("areaName");
        createDineInOrderReqDTO.setGuestCount(0);
        createDineInOrderReqDTO.setAreaGuid("areaGuid");
        batchCreateOrderReqDTO.setCreateDineInOrderReqDTOS(Arrays.asList(createDineInOrderReqDTO));
        batchCreateOrderReqDTO.setReservePhone("reservePhone");
        verify(mockTradeService).bacthopenTable(batchCreateOrderReqDTO);

        // Confirm TradeService.openTable(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO1 = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO1.setDeviceType(0);
        createDineInOrderReqDTO1.setDeviceId("deviceId");
        createDineInOrderReqDTO1.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO1.setStoreGuid("storeGuid");
        createDineInOrderReqDTO1.setStoreName("storeName");
        createDineInOrderReqDTO1.setUserGuid("openStaffGuid");
        createDineInOrderReqDTO1.setUserName("openStaffName");
        createDineInOrderReqDTO1.setGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        createDineInOrderReqDTO1.setDiningTableGuid("areaGuid");
        createDineInOrderReqDTO1.setDiningTableName("tableCode");
        createDineInOrderReqDTO1.setAreaName("areaName");
        createDineInOrderReqDTO1.setGuestCount(0);
        createDineInOrderReqDTO1.setAreaGuid("areaGuid");
        verify(mockTradeService).openTable(createDineInOrderReqDTO1);

        // Confirm TradeService.notifyTradeCombine(...).
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setDeviceType(0);
        tableInfoDTO.setDeviceId("deviceId");
        tableInfoDTO.setEnterpriseGuid("enterpriseGuid");
        tableInfoDTO.setStoreGuid("storeGuid");
        tableInfoDTO.setStoreName("storeName");
        tableInfoDTO.setUserGuid("openStaffGuid");
        tableInfoDTO.setUserName("openStaffName");
        tableInfoDTO.setTableGuid("tableGuid");
        tableInfoDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);
        verify(mockTradeService).notifyTradeCombine(tableOrderCombineDTO);

        // Confirm TradeService.batchRevokeThirdActivity(...).
        final TableCombineDTO tableCombineDTO = new TableCombineDTO();
        tableCombineDTO.setDeviceType(0);
        tableCombineDTO.setDeviceId("deviceId");
        tableCombineDTO.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO.setStoreGuid("storeGuid");
        tableCombineDTO.setStoreName("storeName");
        tableCombineDTO.setUserGuid("openStaffGuid");
        tableCombineDTO.setUserName("openStaffName");
        tableCombineDTO.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO.setCombineTimes(0);
        tableCombineDTO.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO.setMainTableGuid("mainTableGuid");
        verify(mockTradeService).batchRevokeThirdActivity(tableCombineDTO);
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));

        // Confirm BizMsgService.sendMsg(...).
        final TableCombineDTO tableCombineDTO1 = new TableCombineDTO();
        tableCombineDTO1.setDeviceType(0);
        tableCombineDTO1.setDeviceId("deviceId");
        tableCombineDTO1.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO1.setStoreGuid("storeGuid");
        tableCombineDTO1.setStoreName("storeName");
        tableCombineDTO1.setUserGuid("openStaffGuid");
        tableCombineDTO1.setUserName("openStaffName");
        tableCombineDTO1.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO1.setCombineTimes(0);
        tableCombineDTO1.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO1.setMainTableGuid("mainTableGuid");
        verify(mockBizMsgService).sendMsg(tableCombineDTO1);
    }

    @Test
    public void testBatchOpen_RedisServiceBatchGuidReturnsNoItems() {
        // Setup
        final BatchOpenTableDTO batchOpenTableDTO = new BatchOpenTableDTO();
        final OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        openTableDTO.setTableGuid("areaGuid");
        openTableDTO.setTableCode("tableCode");
        openTableDTO.setAreaName("areaName");
        openTableDTO.setActualGuestsNo(0);
        openTableDTO.setAreaGuid("areaGuid");
        batchOpenTableDTO.setOpenTableDTOS(Arrays.asList(openTableDTO));
        batchOpenTableDTO.setReserveGuid("reserveGuid");
        batchOpenTableDTO.setContainDish(false);
        batchOpenTableDTO.setReserveAmount(new BigDecimal("0.00"));
        batchOpenTableDTO.setReservePhone("reservePhone");

        final ReserveOpenTableDTO expectedResult = new ReserveOpenTableDTO(Arrays.asList("value"),
                Arrays.asList("value"), Lists.newArrayList("noExistTables"),false, "mainOrderGuid", "mainTableGuid");

        // Configure TableOrderMapper.selectAllTable(...).
        final TableDO tableDO = new TableDO();
        tableDO.setGuid("areaGuid");
        tableDO.setStoreGuid("storeGuid");
        tableDO.setStoreName("storeName");
        tableDO.setAreaGuid("areaGuid");
        tableDO.setAreaName("areaName");
        tableDO.setTableCode("tableCode");
        tableDO.setSeats(0);
        tableDO.setMainOrderGuid("mainOrderGuid");
        tableDO.setOrderGuid("orderGuid");
        tableDO.setStatus(0);
        final List<TableDO> tableDOS = Arrays.asList(tableDO);
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setDeviceType(0);
        tableBasicQueryDTO.setDeviceId("deviceId");
        tableBasicQueryDTO.setEnterpriseGuid("enterpriseGuid");
        tableBasicQueryDTO.setStoreName("storeName");
        tableBasicQueryDTO.setUserGuid("openStaffGuid");
        tableBasicQueryDTO.setUserName("openStaffName");
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableOrderMapper.selectAllTable(tableBasicQueryDTO)).thenReturn(tableDOS);

        when(mockRedisService.batchGuid(0, "hst_order")).thenReturn(Collections.emptyList());

        // Configure TableOrderMapper.update(...).
        final TableOrderDO entity = new TableOrderDO();
        entity.setId(0L);
        entity.setTableGuid("tableGuid");
        entity.setMainOrderGuid("mainOrderGuid");
        entity.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        entity.setStatus(0);
        entity.setSubStatus("subStatus");
        entity.setCombineTimes(0);
        entity.setOpenStaffGuid("openStaffGuid");
        entity.setOpenStaffName("openStaffName");
        entity.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.update(eq(entity), any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO1 = new BaseDTO();
        openTableDTO1.setDeviceType(0);
        openTableDTO1.setDeviceId("deviceId");
        openTableDTO1.setEnterpriseGuid("enterpriseGuid");
        openTableDTO1.setStoreGuid("storeGuid");
        openTableDTO1.setStoreName("storeName");
        openTableDTO1.setUserGuid("openStaffGuid");
        openTableDTO1.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO1, Arrays.asList("value"))).thenReturn("result");

        when(mockRedisService.isTableLockedByOthers("deviceId", "mainTableGuid")).thenReturn(false);

        // Configure TableOrderMapper.selectList(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> tableOrderDOS = Arrays.asList(tableOrderDO);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDOS);

        when(mockRedisService.getCombineTimes("storeGuid")).thenReturn(0);
        when(mockRedisService.singleGuid("hst_order")).thenReturn("8379e3a2-032a-484c-90ec-91cdcaf186b6");

        // Configure TableOrderMapper.selectFullInfo(...).
        final TableDO tableDO1 = new TableDO();
        tableDO1.setGuid("areaGuid");
        tableDO1.setStoreGuid("storeGuid");
        tableDO1.setStoreName("storeName");
        tableDO1.setAreaGuid("areaGuid");
        tableDO1.setAreaName("areaName");
        tableDO1.setTableCode("tableCode");
        tableDO1.setSeats(0);
        tableDO1.setMainOrderGuid("mainOrderGuid");
        tableDO1.setOrderGuid("orderGuid");
        tableDO1.setStatus(0);
        when(mockTableOrderMapper.selectFullInfo("tableGuid")).thenReturn(tableDO1);

        // Run the test
        final ReserveOpenTableDTO result = tableOrderServiceImplUnderTest.batchOpen(batchOpenTableDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm TradeService.bacthopenTable(...).
        final ReserveBatchCreateOrderReqDTO batchCreateOrderReqDTO = new ReserveBatchCreateOrderReqDTO();
        batchCreateOrderReqDTO.setDeviceType(0);
        batchCreateOrderReqDTO.setDeviceId("deviceId");
        batchCreateOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        batchCreateOrderReqDTO.setStoreGuid("storeGuid");
        batchCreateOrderReqDTO.setStoreName("storeName");
        batchCreateOrderReqDTO.setUserGuid("openStaffGuid");
        batchCreateOrderReqDTO.setUserName("openStaffName");
        batchCreateOrderReqDTO.setReserveGuid("reserveGuid");
        batchCreateOrderReqDTO.setMainOrderGuid("mainOrderGuid");
        batchCreateOrderReqDTO.setContainDish(false);
        batchCreateOrderReqDTO.setReserveFee(new BigDecimal("0.00"));
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceType(0);
        createDineInOrderReqDTO.setDeviceId("deviceId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        createDineInOrderReqDTO.setStoreName("storeName");
        createDineInOrderReqDTO.setUserGuid("openStaffGuid");
        createDineInOrderReqDTO.setUserName("openStaffName");
        createDineInOrderReqDTO.setGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        createDineInOrderReqDTO.setDiningTableGuid("areaGuid");
        createDineInOrderReqDTO.setDiningTableName("tableCode");
        createDineInOrderReqDTO.setAreaName("areaName");
        createDineInOrderReqDTO.setGuestCount(0);
        createDineInOrderReqDTO.setAreaGuid("areaGuid");
        batchCreateOrderReqDTO.setCreateDineInOrderReqDTOS(Arrays.asList(createDineInOrderReqDTO));
        batchCreateOrderReqDTO.setReservePhone("reservePhone");
        verify(mockTradeService).bacthopenTable(batchCreateOrderReqDTO);

        // Confirm TradeService.openTable(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO1 = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO1.setDeviceType(0);
        createDineInOrderReqDTO1.setDeviceId("deviceId");
        createDineInOrderReqDTO1.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO1.setStoreGuid("storeGuid");
        createDineInOrderReqDTO1.setStoreName("storeName");
        createDineInOrderReqDTO1.setUserGuid("openStaffGuid");
        createDineInOrderReqDTO1.setUserName("openStaffName");
        createDineInOrderReqDTO1.setGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        createDineInOrderReqDTO1.setDiningTableGuid("areaGuid");
        createDineInOrderReqDTO1.setDiningTableName("tableCode");
        createDineInOrderReqDTO1.setAreaName("areaName");
        createDineInOrderReqDTO1.setGuestCount(0);
        createDineInOrderReqDTO1.setAreaGuid("areaGuid");
        verify(mockTradeService).openTable(createDineInOrderReqDTO1);

        // Confirm TradeService.notifyTradeCombine(...).
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setDeviceType(0);
        tableInfoDTO.setDeviceId("deviceId");
        tableInfoDTO.setEnterpriseGuid("enterpriseGuid");
        tableInfoDTO.setStoreGuid("storeGuid");
        tableInfoDTO.setStoreName("storeName");
        tableInfoDTO.setUserGuid("openStaffGuid");
        tableInfoDTO.setUserName("openStaffName");
        tableInfoDTO.setTableGuid("tableGuid");
        tableInfoDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);
        verify(mockTradeService).notifyTradeCombine(tableOrderCombineDTO);

        // Confirm TradeService.batchRevokeThirdActivity(...).
        final TableCombineDTO tableCombineDTO = new TableCombineDTO();
        tableCombineDTO.setDeviceType(0);
        tableCombineDTO.setDeviceId("deviceId");
        tableCombineDTO.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO.setStoreGuid("storeGuid");
        tableCombineDTO.setStoreName("storeName");
        tableCombineDTO.setUserGuid("openStaffGuid");
        tableCombineDTO.setUserName("openStaffName");
        tableCombineDTO.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO.setCombineTimes(0);
        tableCombineDTO.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO.setMainTableGuid("mainTableGuid");
        verify(mockTradeService).batchRevokeThirdActivity(tableCombineDTO);
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));

        // Confirm BizMsgService.sendMsg(...).
        final TableCombineDTO tableCombineDTO1 = new TableCombineDTO();
        tableCombineDTO1.setDeviceType(0);
        tableCombineDTO1.setDeviceId("deviceId");
        tableCombineDTO1.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO1.setStoreGuid("storeGuid");
        tableCombineDTO1.setStoreName("storeName");
        tableCombineDTO1.setUserGuid("openStaffGuid");
        tableCombineDTO1.setUserName("openStaffName");
        tableCombineDTO1.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO1.setCombineTimes(0);
        tableCombineDTO1.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO1.setMainTableGuid("mainTableGuid");
        verify(mockBizMsgService).sendMsg(tableCombineDTO1);
    }

    @Test
    public void testBatchOpen_RedisServiceIsTableLockedByOthersReturnsTrue() {
        // Setup
        final BatchOpenTableDTO batchOpenTableDTO = new BatchOpenTableDTO();
        final OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        openTableDTO.setTableGuid("areaGuid");
        openTableDTO.setTableCode("tableCode");
        openTableDTO.setAreaName("areaName");
        openTableDTO.setActualGuestsNo(0);
        openTableDTO.setAreaGuid("areaGuid");
        batchOpenTableDTO.setOpenTableDTOS(Arrays.asList(openTableDTO));
        batchOpenTableDTO.setReserveGuid("reserveGuid");
        batchOpenTableDTO.setContainDish(false);
        batchOpenTableDTO.setReserveAmount(new BigDecimal("0.00"));
        batchOpenTableDTO.setReservePhone("reservePhone");

        // Configure TableOrderMapper.selectAllTable(...).
        final TableDO tableDO = new TableDO();
        tableDO.setGuid("areaGuid");
        tableDO.setStoreGuid("storeGuid");
        tableDO.setStoreName("storeName");
        tableDO.setAreaGuid("areaGuid");
        tableDO.setAreaName("areaName");
        tableDO.setTableCode("tableCode");
        tableDO.setSeats(0);
        tableDO.setMainOrderGuid("mainOrderGuid");
        tableDO.setOrderGuid("orderGuid");
        tableDO.setStatus(0);
        final List<TableDO> tableDOS = Arrays.asList(tableDO);
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setDeviceType(0);
        tableBasicQueryDTO.setDeviceId("deviceId");
        tableBasicQueryDTO.setEnterpriseGuid("enterpriseGuid");
        tableBasicQueryDTO.setStoreName("storeName");
        tableBasicQueryDTO.setUserGuid("openStaffGuid");
        tableBasicQueryDTO.setUserName("openStaffName");
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableOrderMapper.selectAllTable(tableBasicQueryDTO)).thenReturn(tableDOS);

        when(mockRedisService.batchGuid(0, "hst_order")).thenReturn(Arrays.asList("value"));

        // Configure TableOrderMapper.update(...).
        final TableOrderDO entity = new TableOrderDO();
        entity.setId(0L);
        entity.setTableGuid("tableGuid");
        entity.setMainOrderGuid("mainOrderGuid");
        entity.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        entity.setStatus(0);
        entity.setSubStatus("subStatus");
        entity.setCombineTimes(0);
        entity.setOpenStaffGuid("openStaffGuid");
        entity.setOpenStaffName("openStaffName");
        entity.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.update(eq(entity), any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO1 = new BaseDTO();
        openTableDTO1.setDeviceType(0);
        openTableDTO1.setDeviceId("deviceId");
        openTableDTO1.setEnterpriseGuid("enterpriseGuid");
        openTableDTO1.setStoreGuid("storeGuid");
        openTableDTO1.setStoreName("storeName");
        openTableDTO1.setUserGuid("openStaffGuid");
        openTableDTO1.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO1, Arrays.asList("value"))).thenReturn("result");

        when(mockRedisService.isTableLockedByOthers("deviceId", "mainTableGuid")).thenReturn(true);

        // Run the test
        assertThatThrownBy(() -> tableOrderServiceImplUnderTest.batchOpen(batchOpenTableDTO))
                .isInstanceOf(BusinessException.class);

        // Confirm TradeService.bacthopenTable(...).
        final ReserveBatchCreateOrderReqDTO batchCreateOrderReqDTO = new ReserveBatchCreateOrderReqDTO();
        batchCreateOrderReqDTO.setDeviceType(0);
        batchCreateOrderReqDTO.setDeviceId("deviceId");
        batchCreateOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        batchCreateOrderReqDTO.setStoreGuid("storeGuid");
        batchCreateOrderReqDTO.setStoreName("storeName");
        batchCreateOrderReqDTO.setUserGuid("openStaffGuid");
        batchCreateOrderReqDTO.setUserName("openStaffName");
        batchCreateOrderReqDTO.setReserveGuid("reserveGuid");
        batchCreateOrderReqDTO.setMainOrderGuid("mainOrderGuid");
        batchCreateOrderReqDTO.setContainDish(false);
        batchCreateOrderReqDTO.setReserveFee(new BigDecimal("0.00"));
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceType(0);
        createDineInOrderReqDTO.setDeviceId("deviceId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        createDineInOrderReqDTO.setStoreName("storeName");
        createDineInOrderReqDTO.setUserGuid("openStaffGuid");
        createDineInOrderReqDTO.setUserName("openStaffName");
        createDineInOrderReqDTO.setGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        createDineInOrderReqDTO.setDiningTableGuid("areaGuid");
        createDineInOrderReqDTO.setDiningTableName("tableCode");
        createDineInOrderReqDTO.setAreaName("areaName");
        createDineInOrderReqDTO.setGuestCount(0);
        createDineInOrderReqDTO.setAreaGuid("areaGuid");
        batchCreateOrderReqDTO.setCreateDineInOrderReqDTOS(Arrays.asList(createDineInOrderReqDTO));
        batchCreateOrderReqDTO.setReservePhone("reservePhone");
        verify(mockTradeService).bacthopenTable(batchCreateOrderReqDTO);
    }

    @Test
    public void testBatchOpen_TableOrderMapperSelectListReturnsNoItems() {
        // Setup
        final BatchOpenTableDTO batchOpenTableDTO = new BatchOpenTableDTO();
        final OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        openTableDTO.setTableGuid("areaGuid");
        openTableDTO.setTableCode("tableCode");
        openTableDTO.setAreaName("areaName");
        openTableDTO.setActualGuestsNo(0);
        openTableDTO.setAreaGuid("areaGuid");
        batchOpenTableDTO.setOpenTableDTOS(Arrays.asList(openTableDTO));
        batchOpenTableDTO.setReserveGuid("reserveGuid");
        batchOpenTableDTO.setContainDish(false);
        batchOpenTableDTO.setReserveAmount(new BigDecimal("0.00"));
        batchOpenTableDTO.setReservePhone("reservePhone");

        final ReserveOpenTableDTO expectedResult = new ReserveOpenTableDTO(Arrays.asList("value"),
                Arrays.asList("value"), Lists.newArrayList("noExistTables"), false, "mainOrderGuid", "mainTableGuid");

        // Configure TableOrderMapper.selectAllTable(...).
        final TableDO tableDO = new TableDO();
        tableDO.setGuid("areaGuid");
        tableDO.setStoreGuid("storeGuid");
        tableDO.setStoreName("storeName");
        tableDO.setAreaGuid("areaGuid");
        tableDO.setAreaName("areaName");
        tableDO.setTableCode("tableCode");
        tableDO.setSeats(0);
        tableDO.setMainOrderGuid("mainOrderGuid");
        tableDO.setOrderGuid("orderGuid");
        tableDO.setStatus(0);
        final List<TableDO> tableDOS = Arrays.asList(tableDO);
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setDeviceType(0);
        tableBasicQueryDTO.setDeviceId("deviceId");
        tableBasicQueryDTO.setEnterpriseGuid("enterpriseGuid");
        tableBasicQueryDTO.setStoreName("storeName");
        tableBasicQueryDTO.setUserGuid("openStaffGuid");
        tableBasicQueryDTO.setUserName("openStaffName");
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableOrderMapper.selectAllTable(tableBasicQueryDTO)).thenReturn(tableDOS);

        when(mockRedisService.batchGuid(0, "hst_order")).thenReturn(Arrays.asList("value"));

        // Configure TableOrderMapper.update(...).
        final TableOrderDO entity = new TableOrderDO();
        entity.setId(0L);
        entity.setTableGuid("tableGuid");
        entity.setMainOrderGuid("mainOrderGuid");
        entity.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        entity.setStatus(0);
        entity.setSubStatus("subStatus");
        entity.setCombineTimes(0);
        entity.setOpenStaffGuid("openStaffGuid");
        entity.setOpenStaffName("openStaffName");
        entity.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.update(eq(entity), any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO1 = new BaseDTO();
        openTableDTO1.setDeviceType(0);
        openTableDTO1.setDeviceId("deviceId");
        openTableDTO1.setEnterpriseGuid("enterpriseGuid");
        openTableDTO1.setStoreGuid("storeGuid");
        openTableDTO1.setStoreName("storeName");
        openTableDTO1.setUserGuid("openStaffGuid");
        openTableDTO1.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO1, Arrays.asList("value"))).thenReturn("result");

        when(mockRedisService.isTableLockedByOthers("deviceId", "mainTableGuid")).thenReturn(false);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockRedisService.getCombineTimes("storeGuid")).thenReturn(0);

        // Run the test
        final ReserveOpenTableDTO result = tableOrderServiceImplUnderTest.batchOpen(batchOpenTableDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm TradeService.bacthopenTable(...).
        final ReserveBatchCreateOrderReqDTO batchCreateOrderReqDTO = new ReserveBatchCreateOrderReqDTO();
        batchCreateOrderReqDTO.setDeviceType(0);
        batchCreateOrderReqDTO.setDeviceId("deviceId");
        batchCreateOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        batchCreateOrderReqDTO.setStoreGuid("storeGuid");
        batchCreateOrderReqDTO.setStoreName("storeName");
        batchCreateOrderReqDTO.setUserGuid("openStaffGuid");
        batchCreateOrderReqDTO.setUserName("openStaffName");
        batchCreateOrderReqDTO.setReserveGuid("reserveGuid");
        batchCreateOrderReqDTO.setMainOrderGuid("mainOrderGuid");
        batchCreateOrderReqDTO.setContainDish(false);
        batchCreateOrderReqDTO.setReserveFee(new BigDecimal("0.00"));
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceType(0);
        createDineInOrderReqDTO.setDeviceId("deviceId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        createDineInOrderReqDTO.setStoreName("storeName");
        createDineInOrderReqDTO.setUserGuid("openStaffGuid");
        createDineInOrderReqDTO.setUserName("openStaffName");
        createDineInOrderReqDTO.setGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        createDineInOrderReqDTO.setDiningTableGuid("areaGuid");
        createDineInOrderReqDTO.setDiningTableName("tableCode");
        createDineInOrderReqDTO.setAreaName("areaName");
        createDineInOrderReqDTO.setGuestCount(0);
        createDineInOrderReqDTO.setAreaGuid("areaGuid");
        batchCreateOrderReqDTO.setCreateDineInOrderReqDTOS(Arrays.asList(createDineInOrderReqDTO));
        batchCreateOrderReqDTO.setReservePhone("reservePhone");
        verify(mockTradeService).bacthopenTable(batchCreateOrderReqDTO);

        // Confirm TradeService.notifyTradeCombine(...).
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setDeviceType(0);
        tableInfoDTO.setDeviceId("deviceId");
        tableInfoDTO.setEnterpriseGuid("enterpriseGuid");
        tableInfoDTO.setStoreGuid("storeGuid");
        tableInfoDTO.setStoreName("storeName");
        tableInfoDTO.setUserGuid("openStaffGuid");
        tableInfoDTO.setUserName("openStaffName");
        tableInfoDTO.setTableGuid("tableGuid");
        tableInfoDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);
        verify(mockTradeService).notifyTradeCombine(tableOrderCombineDTO);

        // Confirm TradeService.batchRevokeThirdActivity(...).
        final TableCombineDTO tableCombineDTO = new TableCombineDTO();
        tableCombineDTO.setDeviceType(0);
        tableCombineDTO.setDeviceId("deviceId");
        tableCombineDTO.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO.setStoreGuid("storeGuid");
        tableCombineDTO.setStoreName("storeName");
        tableCombineDTO.setUserGuid("openStaffGuid");
        tableCombineDTO.setUserName("openStaffName");
        tableCombineDTO.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO.setCombineTimes(0);
        tableCombineDTO.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO.setMainTableGuid("mainTableGuid");
        verify(mockTradeService).batchRevokeThirdActivity(tableCombineDTO);
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));

        // Confirm BizMsgService.sendMsg(...).
        final TableCombineDTO tableCombineDTO1 = new TableCombineDTO();
        tableCombineDTO1.setDeviceType(0);
        tableCombineDTO1.setDeviceId("deviceId");
        tableCombineDTO1.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO1.setStoreGuid("storeGuid");
        tableCombineDTO1.setStoreName("storeName");
        tableCombineDTO1.setUserGuid("openStaffGuid");
        tableCombineDTO1.setUserName("openStaffName");
        tableCombineDTO1.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO1.setCombineTimes(0);
        tableCombineDTO1.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO1.setMainTableGuid("mainTableGuid");
        verify(mockBizMsgService).sendMsg(tableCombineDTO1);
    }

    @Test
    public void testReserve() {
        // Setup
        // Configure TableOrderMapper.selectAllTable(...).
        final TableDO tableDO = new TableDO();
        tableDO.setGuid("areaGuid");
        tableDO.setStoreGuid("storeGuid");
        tableDO.setStoreName("storeName");
        tableDO.setAreaGuid("areaGuid");
        tableDO.setAreaName("areaName");
        tableDO.setTableCode("tableCode");
        tableDO.setSeats(0);
        tableDO.setMainOrderGuid("mainOrderGuid");
        tableDO.setOrderGuid("orderGuid");
        tableDO.setStatus(0);
        final List<TableDO> tableDOS = Arrays.asList(tableDO);
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setDeviceType(0);
        tableBasicQueryDTO.setDeviceId("deviceId");
        tableBasicQueryDTO.setEnterpriseGuid("enterpriseGuid");
        tableBasicQueryDTO.setStoreName("storeName");
        tableBasicQueryDTO.setUserGuid("openStaffGuid");
        tableBasicQueryDTO.setUserName("openStaffName");
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableOrderMapper.selectAllTable(tableBasicQueryDTO)).thenReturn(tableDOS);

        // Configure TableOrderMapper.update(...).
        final TableOrderDO entity = new TableOrderDO();
        entity.setId(0L);
        entity.setTableGuid("tableGuid");
        entity.setMainOrderGuid("mainOrderGuid");
        entity.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        entity.setStatus(0);
        entity.setSubStatus("subStatus");
        entity.setCombineTimes(0);
        entity.setOpenStaffGuid("openStaffGuid");
        entity.setOpenStaffName("openStaffName");
        entity.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.update(eq(entity), any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final List<String> result = tableOrderServiceImplUnderTest.reserve(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));

        // Confirm BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        verify(mockBizMsgService).sendMsg(openTableDTO, Arrays.asList("value"));
    }

    @Test
    public void testReserve_TableOrderMapperSelectAllTableReturnsNoItems() {
        // Setup
        // Configure TableOrderMapper.selectAllTable(...).
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setDeviceType(0);
        tableBasicQueryDTO.setDeviceId("deviceId");
        tableBasicQueryDTO.setEnterpriseGuid("enterpriseGuid");
        tableBasicQueryDTO.setStoreName("storeName");
        tableBasicQueryDTO.setUserGuid("openStaffGuid");
        tableBasicQueryDTO.setUserName("openStaffName");
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableOrderMapper.selectAllTable(tableBasicQueryDTO)).thenReturn(Collections.emptyList());

        // Configure TableOrderMapper.update(...).
        final TableOrderDO entity = new TableOrderDO();
        entity.setId(0L);
        entity.setTableGuid("tableGuid");
        entity.setMainOrderGuid("mainOrderGuid");
        entity.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        entity.setStatus(0);
        entity.setSubStatus("subStatus");
        entity.setCombineTimes(0);
        entity.setOpenStaffGuid("openStaffGuid");
        entity.setOpenStaffName("openStaffName");
        entity.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.update(eq(entity), any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final List<String> result = tableOrderServiceImplUnderTest.reserve(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());

        // Confirm BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        verify(mockBizMsgService).sendMsg(openTableDTO, Arrays.asList("value"));
    }

    @Test
    public void testPrepare() {
        // Setup
        // Configure TableOrderMapper.selectList(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> tableOrderDOS = Arrays.asList(tableOrderDO);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDOS);

        // Configure TableOrderMapper.update(...).
        final TableOrderDO entity = new TableOrderDO();
        entity.setId(0L);
        entity.setTableGuid("tableGuid");
        entity.setMainOrderGuid("mainOrderGuid");
        entity.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        entity.setStatus(0);
        entity.setSubStatus("subStatus");
        entity.setCombineTimes(0);
        entity.setOpenStaffGuid("openStaffGuid");
        entity.setOpenStaffName("openStaffName");
        entity.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.update(eq(entity), any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure TableOrderMapper.selectOne(...).
        final TableOrderDO tableOrderDO1 = new TableOrderDO();
        tableOrderDO1.setId(0L);
        tableOrderDO1.setTableGuid("tableGuid");
        tableOrderDO1.setMainOrderGuid("mainOrderGuid");
        tableOrderDO1.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO1.setStatus(0);
        tableOrderDO1.setSubStatus("subStatus");
        tableOrderDO1.setCombineTimes(0);
        tableOrderDO1.setOpenStaffGuid("openStaffGuid");
        tableOrderDO1.setOpenStaffName("openStaffName");
        tableOrderDO1.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDO1);

        // Run the test
        tableOrderServiceImplUnderTest.prepare(Arrays.asList("value"), Arrays.asList("value"));

        // Verify the results
        // Confirm BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        verify(mockBizMsgService).sendMsg(openTableDTO, Arrays.asList("value"));
    }

    @Test
    public void testPrepare_TableOrderMapperSelectListReturnsNoItems() {
        // Setup
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        tableOrderServiceImplUnderTest.prepare(Arrays.asList("value"), Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testCancleReserve() {
        // Setup
        // Configure TableOrderMapper.selectAllTable(...).
        final TableDO tableDO = new TableDO();
        tableDO.setGuid("areaGuid");
        tableDO.setStoreGuid("storeGuid");
        tableDO.setStoreName("storeName");
        tableDO.setAreaGuid("areaGuid");
        tableDO.setAreaName("areaName");
        tableDO.setTableCode("tableCode");
        tableDO.setSeats(0);
        tableDO.setMainOrderGuid("mainOrderGuid");
        tableDO.setOrderGuid("orderGuid");
        tableDO.setStatus(0);
        final List<TableDO> tableDOS = Arrays.asList(tableDO);
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setDeviceType(0);
        tableBasicQueryDTO.setDeviceId("deviceId");
        tableBasicQueryDTO.setEnterpriseGuid("enterpriseGuid");
        tableBasicQueryDTO.setStoreName("storeName");
        tableBasicQueryDTO.setUserGuid("openStaffGuid");
        tableBasicQueryDTO.setUserName("openStaffName");
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableOrderMapper.selectAllTable(tableBasicQueryDTO)).thenReturn(tableDOS);

        // Configure TableOrderMapper.update(...).
        final TableOrderDO entity = new TableOrderDO();
        entity.setId(0L);
        entity.setTableGuid("tableGuid");
        entity.setMainOrderGuid("mainOrderGuid");
        entity.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        entity.setStatus(0);
        entity.setSubStatus("subStatus");
        entity.setCombineTimes(0);
        entity.setOpenStaffGuid("openStaffGuid");
        entity.setOpenStaffName("openStaffName");
        entity.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.update(eq(entity), any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final List<String> result = tableOrderServiceImplUnderTest.cancleReserve(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));

        // Confirm BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        verify(mockBizMsgService).sendMsg(openTableDTO, Arrays.asList("value"));
    }

    @Test
    public void testCancleReserve_TableOrderMapperSelectAllTableReturnsNoItems() {
        // Setup
        // Configure TableOrderMapper.selectAllTable(...).
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setDeviceType(0);
        tableBasicQueryDTO.setDeviceId("deviceId");
        tableBasicQueryDTO.setEnterpriseGuid("enterpriseGuid");
        tableBasicQueryDTO.setStoreName("storeName");
        tableBasicQueryDTO.setUserGuid("openStaffGuid");
        tableBasicQueryDTO.setUserName("openStaffName");
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableOrderMapper.selectAllTable(tableBasicQueryDTO)).thenReturn(Collections.emptyList());

        // Configure TableOrderMapper.update(...).
        final TableOrderDO entity = new TableOrderDO();
        entity.setId(0L);
        entity.setTableGuid("tableGuid");
        entity.setMainOrderGuid("mainOrderGuid");
        entity.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        entity.setStatus(0);
        entity.setSubStatus("subStatus");
        entity.setCombineTimes(0);
        entity.setOpenStaffGuid("openStaffGuid");
        entity.setOpenStaffName("openStaffName");
        entity.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.update(eq(entity), any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final List<String> result = tableOrderServiceImplUnderTest.cancleReserve(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());

        // Confirm BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        verify(mockBizMsgService).sendMsg(openTableDTO, Arrays.asList("value"));
    }

    @Test
    public void testTurn() {
        // Setup
        final TurnTableDTO turnTableDTO = new TurnTableDTO();
        turnTableDTO.setDeviceType(0);
        turnTableDTO.setDeviceId("deviceId");
        turnTableDTO.setEnterpriseGuid("enterpriseGuid");
        turnTableDTO.setStoreGuid("storeGuid");
        turnTableDTO.setStoreName("storeName");
        turnTableDTO.setUserGuid("openStaffGuid");
        turnTableDTO.setUserName("openStaffName");
        turnTableDTO.setOriginTableGuid("originTableGuid");
        turnTableDTO.setOriginTableAreaGuid("originTableAreaGuid");
        turnTableDTO.setOriginTableAreaName("originTableAreaName");
        turnTableDTO.setNewTableAreaName("newTableAreaName");
        turnTableDTO.setNewTableAreaGuid("newTableAreaGuid");
        turnTableDTO.setNewTableGuid("newTableGuid");

        when(mockRedisService.isTableLockedByOthers("deviceId", Arrays.asList("value"))).thenReturn(false);

        // Configure TableOrderMapper.selectList(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> tableOrderDOS = Arrays.asList(tableOrderDO);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDOS);

        // Configure AreaService.queryBatchAreaByTable(...).
        final AreaDTO areaDTO = new AreaDTO();
        areaDTO.setDeviceType(0);
        areaDTO.setDeviceId("deviceId");
        areaDTO.setEnterpriseGuid("enterpriseGuid");
        areaDTO.setUserGuid("openStaffGuid");
        areaDTO.setUserName("openStaffName");
        areaDTO.setGuid("c875def8-32f1-423c-99ad-7ba5e8ffc3b2");
        areaDTO.setAreaName("areaName");
        final List<AreaDTO> areaDTOS = Arrays.asList(areaDTO);
        when(mockAreaService.queryBatchAreaByTable(Arrays.asList("value"))).thenReturn(areaDTOS);

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO, Arrays.asList("value"))).thenReturn("result");

        // Configure TradeRpcClient.findByOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setDeviceType(0);
        orderDTO.setGuestCount(0);
        orderDTO.setDiningTableGuid("diningTableGuid");
        orderDTO.setState(0);
        orderDTO.setUpperState(0);
        orderDTO.setMainOrderGuid("mainOrderGuid");
        orderDTO.setStoreGuid("storeGuid");
        orderDTO.setStoreName("storeName");
        when(mockTradeRpcClient.findByOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6")).thenReturn(orderDTO);

        // Run the test
        final boolean result = tableOrderServiceImplUnderTest.turn(turnTableDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm TableOrderMapper.updateAll(...).
        final TableOrderDO tableOrderDO1 = new TableOrderDO();
        tableOrderDO1.setId(0L);
        tableOrderDO1.setTableGuid("tableGuid");
        tableOrderDO1.setMainOrderGuid("mainOrderGuid");
        tableOrderDO1.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO1.setStatus(0);
        tableOrderDO1.setSubStatus("subStatus");
        tableOrderDO1.setCombineTimes(0);
        tableOrderDO1.setOpenStaffGuid("openStaffGuid");
        tableOrderDO1.setOpenStaffName("openStaffName");
        tableOrderDO1.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> tableOrderDos = Arrays.asList(tableOrderDO1);
        verify(mockTableOrderMapper).updateAll(tableOrderDos);
        verify(mockTradeService).notifyTradeTurn(new TradeTableDTO("tableName", "areaName", "areaGuid"));

        // Confirm PrintService.printTurnTable(...).
        final TurnTableDTO turnTableDTO1 = new TurnTableDTO();
        turnTableDTO1.setDeviceType(0);
        turnTableDTO1.setDeviceId("deviceId");
        turnTableDTO1.setEnterpriseGuid("enterpriseGuid");
        turnTableDTO1.setStoreGuid("storeGuid");
        turnTableDTO1.setStoreName("storeName");
        turnTableDTO1.setUserGuid("openStaffGuid");
        turnTableDTO1.setUserName("openStaffName");
        turnTableDTO1.setOriginTableGuid("originTableGuid");
        turnTableDTO1.setOriginTableAreaGuid("originTableAreaGuid");
        turnTableDTO1.setOriginTableAreaName("originTableAreaName");
        turnTableDTO1.setNewTableAreaName("newTableAreaName");
        turnTableDTO1.setNewTableAreaGuid("newTableAreaGuid");
        turnTableDTO1.setNewTableGuid("newTableGuid");
        verify(mockPrintService).printTurnTable(turnTableDTO1);
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));

        // Confirm BizMsgService.sendMsg(...).
        final TurnTableDTO turnTableDTO2 = new TurnTableDTO();
        turnTableDTO2.setDeviceType(0);
        turnTableDTO2.setDeviceId("deviceId");
        turnTableDTO2.setEnterpriseGuid("enterpriseGuid");
        turnTableDTO2.setStoreGuid("storeGuid");
        turnTableDTO2.setStoreName("storeName");
        turnTableDTO2.setUserGuid("openStaffGuid");
        turnTableDTO2.setUserName("openStaffName");
        turnTableDTO2.setOriginTableGuid("originTableGuid");
        turnTableDTO2.setOriginTableAreaGuid("originTableAreaGuid");
        turnTableDTO2.setOriginTableAreaName("originTableAreaName");
        turnTableDTO2.setNewTableAreaName("newTableAreaName");
        turnTableDTO2.setNewTableAreaGuid("newTableAreaGuid");
        turnTableDTO2.setNewTableGuid("newTableGuid");
        verify(mockBizMsgService).sendMsg(turnTableDTO2);
    }

    @Test
    public void testTurn_RedisServiceReturnsTrue() {
        // Setup
        final TurnTableDTO turnTableDTO = new TurnTableDTO();
        turnTableDTO.setDeviceType(0);
        turnTableDTO.setDeviceId("deviceId");
        turnTableDTO.setEnterpriseGuid("enterpriseGuid");
        turnTableDTO.setStoreGuid("storeGuid");
        turnTableDTO.setStoreName("storeName");
        turnTableDTO.setUserGuid("openStaffGuid");
        turnTableDTO.setUserName("openStaffName");
        turnTableDTO.setOriginTableGuid("originTableGuid");
        turnTableDTO.setOriginTableAreaGuid("originTableAreaGuid");
        turnTableDTO.setOriginTableAreaName("originTableAreaName");
        turnTableDTO.setNewTableAreaName("newTableAreaName");
        turnTableDTO.setNewTableAreaGuid("newTableAreaGuid");
        turnTableDTO.setNewTableGuid("newTableGuid");

        when(mockRedisService.isTableLockedByOthers("deviceId", Arrays.asList("value"))).thenReturn(true);

        // Run the test
        assertThatThrownBy(() -> tableOrderServiceImplUnderTest.turn(turnTableDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testTurn_TableOrderMapperSelectListReturnsNoItems() {
        // Setup
        final TurnTableDTO turnTableDTO = new TurnTableDTO();
        turnTableDTO.setDeviceType(0);
        turnTableDTO.setDeviceId("deviceId");
        turnTableDTO.setEnterpriseGuid("enterpriseGuid");
        turnTableDTO.setStoreGuid("storeGuid");
        turnTableDTO.setStoreName("storeName");
        turnTableDTO.setUserGuid("openStaffGuid");
        turnTableDTO.setUserName("openStaffName");
        turnTableDTO.setOriginTableGuid("originTableGuid");
        turnTableDTO.setOriginTableAreaGuid("originTableAreaGuid");
        turnTableDTO.setOriginTableAreaName("originTableAreaName");
        turnTableDTO.setNewTableAreaName("newTableAreaName");
        turnTableDTO.setNewTableAreaGuid("newTableAreaGuid");
        turnTableDTO.setNewTableGuid("newTableGuid");

        when(mockRedisService.isTableLockedByOthers("deviceId", Arrays.asList("value"))).thenReturn(false);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> tableOrderServiceImplUnderTest.turn(turnTableDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testTurn_AreaServiceReturnsNoItems() {
        // Setup
        final TurnTableDTO turnTableDTO = new TurnTableDTO();
        turnTableDTO.setDeviceType(0);
        turnTableDTO.setDeviceId("deviceId");
        turnTableDTO.setEnterpriseGuid("enterpriseGuid");
        turnTableDTO.setStoreGuid("storeGuid");
        turnTableDTO.setStoreName("storeName");
        turnTableDTO.setUserGuid("openStaffGuid");
        turnTableDTO.setUserName("openStaffName");
        turnTableDTO.setOriginTableGuid("originTableGuid");
        turnTableDTO.setOriginTableAreaGuid("originTableAreaGuid");
        turnTableDTO.setOriginTableAreaName("originTableAreaName");
        turnTableDTO.setNewTableAreaName("newTableAreaName");
        turnTableDTO.setNewTableAreaGuid("newTableAreaGuid");
        turnTableDTO.setNewTableGuid("newTableGuid");

        when(mockRedisService.isTableLockedByOthers("deviceId", Arrays.asList("value"))).thenReturn(false);

        // Configure TableOrderMapper.selectList(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> tableOrderDOS = Arrays.asList(tableOrderDO);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDOS);

        when(mockAreaService.queryBatchAreaByTable(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO, Arrays.asList("value"))).thenReturn("result");

        // Configure TradeRpcClient.findByOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setDeviceType(0);
        orderDTO.setGuestCount(0);
        orderDTO.setDiningTableGuid("diningTableGuid");
        orderDTO.setState(0);
        orderDTO.setUpperState(0);
        orderDTO.setMainOrderGuid("mainOrderGuid");
        orderDTO.setStoreGuid("storeGuid");
        orderDTO.setStoreName("storeName");
        when(mockTradeRpcClient.findByOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6")).thenReturn(orderDTO);

        // Run the test
        final boolean result = tableOrderServiceImplUnderTest.turn(turnTableDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm TableOrderMapper.updateAll(...).
        final TableOrderDO tableOrderDO1 = new TableOrderDO();
        tableOrderDO1.setId(0L);
        tableOrderDO1.setTableGuid("tableGuid");
        tableOrderDO1.setMainOrderGuid("mainOrderGuid");
        tableOrderDO1.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO1.setStatus(0);
        tableOrderDO1.setSubStatus("subStatus");
        tableOrderDO1.setCombineTimes(0);
        tableOrderDO1.setOpenStaffGuid("openStaffGuid");
        tableOrderDO1.setOpenStaffName("openStaffName");
        tableOrderDO1.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> tableOrderDos = Arrays.asList(tableOrderDO1);
        verify(mockTableOrderMapper).updateAll(tableOrderDos);
        verify(mockTradeService).notifyTradeTurn(new TradeTableDTO("tableName", "areaName", "areaGuid"));

        // Confirm PrintService.printTurnTable(...).
        final TurnTableDTO turnTableDTO1 = new TurnTableDTO();
        turnTableDTO1.setDeviceType(0);
        turnTableDTO1.setDeviceId("deviceId");
        turnTableDTO1.setEnterpriseGuid("enterpriseGuid");
        turnTableDTO1.setStoreGuid("storeGuid");
        turnTableDTO1.setStoreName("storeName");
        turnTableDTO1.setUserGuid("openStaffGuid");
        turnTableDTO1.setUserName("openStaffName");
        turnTableDTO1.setOriginTableGuid("originTableGuid");
        turnTableDTO1.setOriginTableAreaGuid("originTableAreaGuid");
        turnTableDTO1.setOriginTableAreaName("originTableAreaName");
        turnTableDTO1.setNewTableAreaName("newTableAreaName");
        turnTableDTO1.setNewTableAreaGuid("newTableAreaGuid");
        turnTableDTO1.setNewTableGuid("newTableGuid");
        verify(mockPrintService).printTurnTable(turnTableDTO1);
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));

        // Confirm BizMsgService.sendMsg(...).
        final TurnTableDTO turnTableDTO2 = new TurnTableDTO();
        turnTableDTO2.setDeviceType(0);
        turnTableDTO2.setDeviceId("deviceId");
        turnTableDTO2.setEnterpriseGuid("enterpriseGuid");
        turnTableDTO2.setStoreGuid("storeGuid");
        turnTableDTO2.setStoreName("storeName");
        turnTableDTO2.setUserGuid("openStaffGuid");
        turnTableDTO2.setUserName("openStaffName");
        turnTableDTO2.setOriginTableGuid("originTableGuid");
        turnTableDTO2.setOriginTableAreaGuid("originTableAreaGuid");
        turnTableDTO2.setOriginTableAreaName("originTableAreaName");
        turnTableDTO2.setNewTableAreaName("newTableAreaName");
        turnTableDTO2.setNewTableAreaGuid("newTableAreaGuid");
        turnTableDTO2.setNewTableGuid("newTableGuid");
        verify(mockBizMsgService).sendMsg(turnTableDTO2);
    }

    @Test
    public void testCombine() {
        // Setup
        final TableCombineDTO tableCombineDTO = new TableCombineDTO();
        tableCombineDTO.setDeviceType(0);
        tableCombineDTO.setDeviceId("deviceId");
        tableCombineDTO.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO.setStoreGuid("storeGuid");
        tableCombineDTO.setStoreName("storeName");
        tableCombineDTO.setUserGuid("openStaffGuid");
        tableCombineDTO.setUserName("openStaffName");
        tableCombineDTO.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO.setCombineTimes(0);
        tableCombineDTO.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO.setMainTableGuid("mainTableGuid");

        when(mockRedisService.isTableLockedByOthers("deviceId", "mainTableGuid")).thenReturn(false);

        // Configure TableOrderMapper.selectList(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> tableOrderDOS = Arrays.asList(tableOrderDO);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDOS);

        when(mockRedisService.getCombineTimes("storeGuid")).thenReturn(0);
        when(mockRedisService.singleGuid("hst_order")).thenReturn("8379e3a2-032a-484c-90ec-91cdcaf186b6");

        // Configure TableOrderMapper.selectFullInfo(...).
        final TableDO tableDO = new TableDO();
        tableDO.setGuid("areaGuid");
        tableDO.setStoreGuid("storeGuid");
        tableDO.setStoreName("storeName");
        tableDO.setAreaGuid("areaGuid");
        tableDO.setAreaName("areaName");
        tableDO.setTableCode("tableCode");
        tableDO.setSeats(0);
        tableDO.setMainOrderGuid("mainOrderGuid");
        tableDO.setOrderGuid("orderGuid");
        tableDO.setStatus(0);
        when(mockTableOrderMapper.selectFullInfo("tableGuid")).thenReturn(tableDO);

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO, Arrays.asList("value"))).thenReturn("result");

        // Run the test
        final List<String> result = tableOrderServiceImplUnderTest.combine(tableCombineDTO, 0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());

        // Confirm TradeService.openTable(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceType(0);
        createDineInOrderReqDTO.setDeviceId("deviceId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        createDineInOrderReqDTO.setStoreName("storeName");
        createDineInOrderReqDTO.setUserGuid("openStaffGuid");
        createDineInOrderReqDTO.setUserName("openStaffName");
        createDineInOrderReqDTO.setGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        createDineInOrderReqDTO.setDiningTableGuid("areaGuid");
        createDineInOrderReqDTO.setDiningTableName("tableCode");
        createDineInOrderReqDTO.setAreaName("areaName");
        createDineInOrderReqDTO.setGuestCount(0);
        createDineInOrderReqDTO.setAreaGuid("areaGuid");
        verify(mockTradeService).openTable(createDineInOrderReqDTO);

        // Confirm TradeService.notifyTradeCombine(...).
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setDeviceType(0);
        tableInfoDTO.setDeviceId("deviceId");
        tableInfoDTO.setEnterpriseGuid("enterpriseGuid");
        tableInfoDTO.setStoreGuid("storeGuid");
        tableInfoDTO.setStoreName("storeName");
        tableInfoDTO.setUserGuid("openStaffGuid");
        tableInfoDTO.setUserName("openStaffName");
        tableInfoDTO.setTableGuid("tableGuid");
        tableInfoDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);
        verify(mockTradeService).notifyTradeCombine(tableOrderCombineDTO);

        // Confirm TradeService.batchRevokeThirdActivity(...).
        final TableCombineDTO tableCombineDTO1 = new TableCombineDTO();
        tableCombineDTO1.setDeviceType(0);
        tableCombineDTO1.setDeviceId("deviceId");
        tableCombineDTO1.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO1.setStoreGuid("storeGuid");
        tableCombineDTO1.setStoreName("storeName");
        tableCombineDTO1.setUserGuid("openStaffGuid");
        tableCombineDTO1.setUserName("openStaffName");
        tableCombineDTO1.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO1.setCombineTimes(0);
        tableCombineDTO1.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO1.setMainTableGuid("mainTableGuid");
        verify(mockTradeService).batchRevokeThirdActivity(tableCombineDTO1);
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));

        // Confirm BizMsgService.sendMsg(...).
        final TableCombineDTO tableCombineDTO2 = new TableCombineDTO();
        tableCombineDTO2.setDeviceType(0);
        tableCombineDTO2.setDeviceId("deviceId");
        tableCombineDTO2.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO2.setStoreGuid("storeGuid");
        tableCombineDTO2.setStoreName("storeName");
        tableCombineDTO2.setUserGuid("openStaffGuid");
        tableCombineDTO2.setUserName("openStaffName");
        tableCombineDTO2.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO2.setCombineTimes(0);
        tableCombineDTO2.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO2.setMainTableGuid("mainTableGuid");
        verify(mockBizMsgService).sendMsg(tableCombineDTO2);
    }

    @Test
    public void testCombine_RedisServiceIsTableLockedByOthersReturnsTrue() {
        // Setup
        final TableCombineDTO tableCombineDTO = new TableCombineDTO();
        tableCombineDTO.setDeviceType(0);
        tableCombineDTO.setDeviceId("deviceId");
        tableCombineDTO.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO.setStoreGuid("storeGuid");
        tableCombineDTO.setStoreName("storeName");
        tableCombineDTO.setUserGuid("openStaffGuid");
        tableCombineDTO.setUserName("openStaffName");
        tableCombineDTO.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO.setCombineTimes(0);
        tableCombineDTO.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO.setMainTableGuid("mainTableGuid");

        when(mockRedisService.isTableLockedByOthers("deviceId", "mainTableGuid")).thenReturn(true);

        // Run the test
        assertThatThrownBy(() -> tableOrderServiceImplUnderTest.combine(tableCombineDTO, 0))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testCombine_TableOrderMapperSelectListReturnsNoItems() {
        // Setup
        final TableCombineDTO tableCombineDTO = new TableCombineDTO();
        tableCombineDTO.setDeviceType(0);
        tableCombineDTO.setDeviceId("deviceId");
        tableCombineDTO.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO.setStoreGuid("storeGuid");
        tableCombineDTO.setStoreName("storeName");
        tableCombineDTO.setUserGuid("openStaffGuid");
        tableCombineDTO.setUserName("openStaffName");
        tableCombineDTO.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO.setCombineTimes(0);
        tableCombineDTO.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO.setMainTableGuid("mainTableGuid");

        when(mockRedisService.isTableLockedByOthers("deviceId", "mainTableGuid")).thenReturn(false);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockRedisService.getCombineTimes("storeGuid")).thenReturn(0);

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO, Arrays.asList("value"))).thenReturn("result");

        // Run the test
        final List<String> result = tableOrderServiceImplUnderTest.combine(tableCombineDTO, 0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());

        // Confirm TradeService.notifyTradeCombine(...).
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setDeviceType(0);
        tableInfoDTO.setDeviceId("deviceId");
        tableInfoDTO.setEnterpriseGuid("enterpriseGuid");
        tableInfoDTO.setStoreGuid("storeGuid");
        tableInfoDTO.setStoreName("storeName");
        tableInfoDTO.setUserGuid("openStaffGuid");
        tableInfoDTO.setUserName("openStaffName");
        tableInfoDTO.setTableGuid("tableGuid");
        tableInfoDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);
        verify(mockTradeService).notifyTradeCombine(tableOrderCombineDTO);

        // Confirm TradeService.batchRevokeThirdActivity(...).
        final TableCombineDTO tableCombineDTO1 = new TableCombineDTO();
        tableCombineDTO1.setDeviceType(0);
        tableCombineDTO1.setDeviceId("deviceId");
        tableCombineDTO1.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO1.setStoreGuid("storeGuid");
        tableCombineDTO1.setStoreName("storeName");
        tableCombineDTO1.setUserGuid("openStaffGuid");
        tableCombineDTO1.setUserName("openStaffName");
        tableCombineDTO1.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO1.setCombineTimes(0);
        tableCombineDTO1.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO1.setMainTableGuid("mainTableGuid");
        verify(mockTradeService).batchRevokeThirdActivity(tableCombineDTO1);
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));

        // Confirm BizMsgService.sendMsg(...).
        final TableCombineDTO tableCombineDTO2 = new TableCombineDTO();
        tableCombineDTO2.setDeviceType(0);
        tableCombineDTO2.setDeviceId("deviceId");
        tableCombineDTO2.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO2.setStoreGuid("storeGuid");
        tableCombineDTO2.setStoreName("storeName");
        tableCombineDTO2.setUserGuid("openStaffGuid");
        tableCombineDTO2.setUserName("openStaffName");
        tableCombineDTO2.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO2.setCombineTimes(0);
        tableCombineDTO2.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO2.setMainTableGuid("mainTableGuid");
        verify(mockBizMsgService).sendMsg(tableCombineDTO2);
    }

    @Test
    public void testCombine_v2() {
        // Setup
        final TableCombineDTO tableCombineDTO = new TableCombineDTO();
        tableCombineDTO.setDeviceType(0);
        tableCombineDTO.setDeviceId("deviceId");
        tableCombineDTO.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO.setStoreGuid("storeGuid");
        tableCombineDTO.setStoreName("storeName");
        tableCombineDTO.setUserGuid("openStaffGuid");
        tableCombineDTO.setUserName("openStaffName");
        tableCombineDTO.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO.setCombineTimes(0);
        tableCombineDTO.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO.setMainTableGuid("mainTableGuid");

        final TableCombineRespDTO expectedResult = new TableCombineRespDTO();
        expectedResult.setFailTableArray(Arrays.asList("value"));
        expectedResult.setSuccessResult(new HashMap<>());

        when(mockRedisService.isTableLockedByOthers("deviceId", "mainTableGuid")).thenReturn(false);

        // Configure TableOrderMapper.selectList(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> tableOrderDOS = Arrays.asList(tableOrderDO);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDOS);

        when(mockRedisService.getCombineTimes("storeGuid")).thenReturn(0);
        when(mockRedisService.singleGuid("hst_order")).thenReturn("8379e3a2-032a-484c-90ec-91cdcaf186b6");

        // Configure TableOrderMapper.selectFullInfo(...).
        final TableDO tableDO = new TableDO();
        tableDO.setGuid("areaGuid");
        tableDO.setStoreGuid("storeGuid");
        tableDO.setStoreName("storeName");
        tableDO.setAreaGuid("areaGuid");
        tableDO.setAreaName("areaName");
        tableDO.setTableCode("tableCode");
        tableDO.setSeats(0);
        tableDO.setMainOrderGuid("mainOrderGuid");
        tableDO.setOrderGuid("orderGuid");
        tableDO.setStatus(0);
        when(mockTableOrderMapper.selectFullInfo("tableGuid")).thenReturn(tableDO);

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO, Arrays.asList("value"))).thenReturn("result");

        // Run the test
        final TableCombineRespDTO result = tableOrderServiceImplUnderTest.combine_v2(tableCombineDTO, 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm TradeService.openTable(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceType(0);
        createDineInOrderReqDTO.setDeviceId("deviceId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        createDineInOrderReqDTO.setStoreName("storeName");
        createDineInOrderReqDTO.setUserGuid("openStaffGuid");
        createDineInOrderReqDTO.setUserName("openStaffName");
        createDineInOrderReqDTO.setGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        createDineInOrderReqDTO.setDiningTableGuid("areaGuid");
        createDineInOrderReqDTO.setDiningTableName("tableCode");
        createDineInOrderReqDTO.setAreaName("areaName");
        createDineInOrderReqDTO.setGuestCount(0);
        createDineInOrderReqDTO.setAreaGuid("areaGuid");
        verify(mockTradeService).openTable(createDineInOrderReqDTO);

        // Confirm TradeService.notifyTradeCombine(...).
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setDeviceType(0);
        tableInfoDTO.setDeviceId("deviceId");
        tableInfoDTO.setEnterpriseGuid("enterpriseGuid");
        tableInfoDTO.setStoreGuid("storeGuid");
        tableInfoDTO.setStoreName("storeName");
        tableInfoDTO.setUserGuid("openStaffGuid");
        tableInfoDTO.setUserName("openStaffName");
        tableInfoDTO.setTableGuid("tableGuid");
        tableInfoDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);
        verify(mockTradeService).notifyTradeCombine(tableOrderCombineDTO);
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testCombine_v2_RedisServiceIsTableLockedByOthersReturnsTrue() {
        // Setup
        final TableCombineDTO tableCombineDTO = new TableCombineDTO();
        tableCombineDTO.setDeviceType(0);
        tableCombineDTO.setDeviceId("deviceId");
        tableCombineDTO.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO.setStoreGuid("storeGuid");
        tableCombineDTO.setStoreName("storeName");
        tableCombineDTO.setUserGuid("openStaffGuid");
        tableCombineDTO.setUserName("openStaffName");
        tableCombineDTO.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO.setCombineTimes(0);
        tableCombineDTO.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO.setMainTableGuid("mainTableGuid");

        when(mockRedisService.isTableLockedByOthers("deviceId", "mainTableGuid")).thenReturn(true);

        // Run the test
        assertThatThrownBy(() -> tableOrderServiceImplUnderTest.combine_v2(tableCombineDTO, 0))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testCombine_v2_TableOrderMapperSelectListReturnsNoItems() {
        // Setup
        final TableCombineDTO tableCombineDTO = new TableCombineDTO();
        tableCombineDTO.setDeviceType(0);
        tableCombineDTO.setDeviceId("deviceId");
        tableCombineDTO.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO.setStoreGuid("storeGuid");
        tableCombineDTO.setStoreName("storeName");
        tableCombineDTO.setUserGuid("openStaffGuid");
        tableCombineDTO.setUserName("openStaffName");
        tableCombineDTO.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO.setCombineTimes(0);
        tableCombineDTO.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO.setMainTableGuid("mainTableGuid");

        final TableCombineRespDTO expectedResult = new TableCombineRespDTO();
        expectedResult.setFailTableArray(Arrays.asList("value"));
        expectedResult.setSuccessResult(new HashMap<>());

        when(mockRedisService.isTableLockedByOthers("deviceId", "mainTableGuid")).thenReturn(false);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockRedisService.getCombineTimes("storeGuid")).thenReturn(0);

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO, Arrays.asList("value"))).thenReturn("result");

        // Run the test
        final TableCombineRespDTO result = tableOrderServiceImplUnderTest.combine_v2(tableCombineDTO, 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm TradeService.notifyTradeCombine(...).
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setDeviceType(0);
        tableInfoDTO.setDeviceId("deviceId");
        tableInfoDTO.setEnterpriseGuid("enterpriseGuid");
        tableInfoDTO.setStoreGuid("storeGuid");
        tableInfoDTO.setStoreName("storeName");
        tableInfoDTO.setUserGuid("openStaffGuid");
        tableInfoDTO.setUserName("openStaffName");
        tableInfoDTO.setTableGuid("tableGuid");
        tableInfoDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);
        verify(mockTradeService).notifyTradeCombine(tableOrderCombineDTO);
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testSeparate() {
        // Setup
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setDeviceType(0);
        tableInfoDTO.setDeviceId("deviceId");
        tableInfoDTO.setEnterpriseGuid("enterpriseGuid");
        tableInfoDTO.setStoreGuid("storeGuid");
        tableInfoDTO.setStoreName("storeName");
        tableInfoDTO.setUserGuid("openStaffGuid");
        tableInfoDTO.setUserName("openStaffName");
        tableInfoDTO.setTableGuid("tableGuid");
        tableInfoDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);
        when(mockRedisService.isTableLockedByOthers("deviceId", Arrays.asList("value"))).thenReturn(false);

        // Configure TableOrderMapper.selectList(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> tableOrderDOS = Arrays.asList(tableOrderDO);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDOS);

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO, Arrays.asList("value"))).thenReturn("result");

        // Run the test
        final boolean result = tableOrderServiceImplUnderTest.separate(tableOrderCombineDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm TableOrderMapper.removeCombine(...).
        final TableOrderDO tableOrderDO1 = new TableOrderDO();
        tableOrderDO1.setId(0L);
        tableOrderDO1.setTableGuid("tableGuid");
        tableOrderDO1.setMainOrderGuid("mainOrderGuid");
        tableOrderDO1.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO1.setStatus(0);
        tableOrderDO1.setSubStatus("subStatus");
        tableOrderDO1.setCombineTimes(0);
        tableOrderDO1.setOpenStaffGuid("openStaffGuid");
        tableOrderDO1.setOpenStaffName("openStaffName");
        tableOrderDO1.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> updateList = Arrays.asList(tableOrderDO1);
        verify(mockTableOrderMapper).removeCombine(updateList);

        // Confirm TradeService.separate(...).
        final TableInfoDTO tableInfoDTO1 = new TableInfoDTO();
        tableInfoDTO1.setDeviceType(0);
        tableInfoDTO1.setDeviceId("deviceId");
        tableInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        tableInfoDTO1.setStoreGuid("storeGuid");
        tableInfoDTO1.setStoreName("storeName");
        tableInfoDTO1.setUserGuid("openStaffGuid");
        tableInfoDTO1.setUserName("openStaffName");
        tableInfoDTO1.setTableGuid("tableGuid");
        tableInfoDTO1.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        final TableOrderCombineDTO tableOrderCombineDTO1 = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO1),
                "mainOrderGuid", "mainTableGuid", true);
        verify(mockTradeService).separate(tableOrderCombineDTO1);
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));

        // Confirm BizMsgService.sendMsg(...).
        final TableInfoDTO tableInfoDTO2 = new TableInfoDTO();
        tableInfoDTO2.setDeviceType(0);
        tableInfoDTO2.setDeviceId("deviceId");
        tableInfoDTO2.setEnterpriseGuid("enterpriseGuid");
        tableInfoDTO2.setStoreGuid("storeGuid");
        tableInfoDTO2.setStoreName("storeName");
        tableInfoDTO2.setUserGuid("openStaffGuid");
        tableInfoDTO2.setUserName("openStaffName");
        tableInfoDTO2.setTableGuid("tableGuid");
        tableInfoDTO2.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        final TableOrderCombineDTO tableOrderCombineDTO2 = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO2),
                "mainOrderGuid", "mainTableGuid", true);
        verify(mockBizMsgService).sendMsg(tableOrderCombineDTO2, Lists.newArrayList("1"));
    }

    @Test
    public void testSeparate_RedisServiceReturnsTrue() {
        // Setup
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setDeviceType(0);
        tableInfoDTO.setDeviceId("deviceId");
        tableInfoDTO.setEnterpriseGuid("enterpriseGuid");
        tableInfoDTO.setStoreGuid("storeGuid");
        tableInfoDTO.setStoreName("storeName");
        tableInfoDTO.setUserGuid("openStaffGuid");
        tableInfoDTO.setUserName("openStaffName");
        tableInfoDTO.setTableGuid("tableGuid");
        tableInfoDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);
        when(mockRedisService.isTableLockedByOthers("deviceId", Arrays.asList("value"))).thenReturn(true);

        // Run the test
        assertThatThrownBy(() -> tableOrderServiceImplUnderTest.separate(tableOrderCombineDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSeparate_TableOrderMapperSelectListReturnsNoItems() {
        // Setup
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setDeviceType(0);
        tableInfoDTO.setDeviceId("deviceId");
        tableInfoDTO.setEnterpriseGuid("enterpriseGuid");
        tableInfoDTO.setStoreGuid("storeGuid");
        tableInfoDTO.setStoreName("storeName");
        tableInfoDTO.setUserGuid("openStaffGuid");
        tableInfoDTO.setUserName("openStaffName");
        tableInfoDTO.setTableGuid("tableGuid");
        tableInfoDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);
        when(mockRedisService.isTableLockedByOthers("deviceId", Arrays.asList("value"))).thenReturn(false);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO, Arrays.asList("value"))).thenReturn("result");

        // Run the test
        final boolean result = tableOrderServiceImplUnderTest.separate(tableOrderCombineDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm TableOrderMapper.removeCombine(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> updateList = Arrays.asList(tableOrderDO);
        verify(mockTableOrderMapper).removeCombine(updateList);

        // Confirm TradeService.separate(...).
        final TableInfoDTO tableInfoDTO1 = new TableInfoDTO();
        tableInfoDTO1.setDeviceType(0);
        tableInfoDTO1.setDeviceId("deviceId");
        tableInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        tableInfoDTO1.setStoreGuid("storeGuid");
        tableInfoDTO1.setStoreName("storeName");
        tableInfoDTO1.setUserGuid("openStaffGuid");
        tableInfoDTO1.setUserName("openStaffName");
        tableInfoDTO1.setTableGuid("tableGuid");
        tableInfoDTO1.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        final TableOrderCombineDTO tableOrderCombineDTO1 = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO1),
                "mainOrderGuid", "mainTableGuid", true);
        verify(mockTradeService).separate(tableOrderCombineDTO1);
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));

        // Confirm BizMsgService.sendMsg(...).
        final TableInfoDTO tableInfoDTO2 = new TableInfoDTO();
        tableInfoDTO2.setDeviceType(0);
        tableInfoDTO2.setDeviceId("deviceId");
        tableInfoDTO2.setEnterpriseGuid("enterpriseGuid");
        tableInfoDTO2.setStoreGuid("storeGuid");
        tableInfoDTO2.setStoreName("storeName");
        tableInfoDTO2.setUserGuid("openStaffGuid");
        tableInfoDTO2.setUserName("openStaffName");
        tableInfoDTO2.setTableGuid("tableGuid");
        tableInfoDTO2.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        final TableOrderCombineDTO tableOrderCombineDTO2 = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO2),
                "mainOrderGuid", "mainTableGuid", true);
        verify(mockBizMsgService).sendMsg(tableOrderCombineDTO2, Lists.newArrayList("1"));
    }

    @Test
    public void testClose() {
        // Setup
        final CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setDeviceType(0);
        cancelOrderReqDTO.setDeviceId("deviceId");
        cancelOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        cancelOrderReqDTO.setStoreGuid("storeGuid");
        cancelOrderReqDTO.setStoreName("storeName");
        cancelOrderReqDTO.setUserGuid("openStaffGuid");
        cancelOrderReqDTO.setUserName("openStaffName");
        cancelOrderReqDTO.setTableGuid("tableGuid");
        cancelOrderReqDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");

        // Configure TableOrderMapper.selectOne(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDO);

        // Configure TableOrderMapper.selectList(...).
        final TableOrderDO tableOrderDO1 = new TableOrderDO();
        tableOrderDO1.setId(0L);
        tableOrderDO1.setTableGuid("tableGuid");
        tableOrderDO1.setMainOrderGuid("mainOrderGuid");
        tableOrderDO1.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO1.setStatus(0);
        tableOrderDO1.setSubStatus("subStatus");
        tableOrderDO1.setCombineTimes(0);
        tableOrderDO1.setOpenStaffGuid("openStaffGuid");
        tableOrderDO1.setOpenStaffName("openStaffName");
        tableOrderDO1.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> tableOrderDOS = Arrays.asList(tableOrderDO1);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDOS);

        when(mockTableOrderMapper.closeTable("tableGuid", "8379e3a2-032a-484c-90ec-91cdcaf186b6",
                "subStatus")).thenReturn(0);

        // Configure TradeRpcClient.findByOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setDeviceType(0);
        orderDTO.setGuestCount(0);
        orderDTO.setDiningTableGuid("diningTableGuid");
        orderDTO.setState(0);
        orderDTO.setUpperState(0);
        orderDTO.setMainOrderGuid("mainOrderGuid");
        orderDTO.setStoreGuid("storeGuid");
        orderDTO.setStoreName("storeName");
        when(mockTradeRpcClient.findByOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6")).thenReturn(orderDTO);

        // Configure TradeRpcClient.listOrderByCombineOrderGuid(...).
        final OrderDTO orderDTO1 = new OrderDTO();
        orderDTO1.setDeviceType(0);
        orderDTO1.setGuestCount(0);
        orderDTO1.setDiningTableGuid("diningTableGuid");
        orderDTO1.setState(0);
        orderDTO1.setUpperState(0);
        orderDTO1.setMainOrderGuid("mainOrderGuid");
        orderDTO1.setStoreGuid("storeGuid");
        orderDTO1.setStoreName("storeName");
        final List<OrderDTO> orderDTOS = Arrays.asList(orderDTO1);
        when(mockTradeRpcClient.listOrderByCombineOrderGuid("mainOrderGuid")).thenReturn(orderDTOS);

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO, Arrays.asList("value"))).thenReturn("result");

        // Run the test
        final boolean result = tableOrderServiceImplUnderTest.close(cancelOrderReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm TableOrderMapper.removeCombine(...).
        final TableOrderDO tableOrderDO2 = new TableOrderDO();
        tableOrderDO2.setId(0L);
        tableOrderDO2.setTableGuid("tableGuid");
        tableOrderDO2.setMainOrderGuid("mainOrderGuid");
        tableOrderDO2.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO2.setStatus(0);
        tableOrderDO2.setSubStatus("subStatus");
        tableOrderDO2.setCombineTimes(0);
        tableOrderDO2.setOpenStaffGuid("openStaffGuid");
        tableOrderDO2.setOpenStaffName("openStaffName");
        tableOrderDO2.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> updateList = Arrays.asList(tableOrderDO2);
        verify(mockTableOrderMapper).removeCombine(updateList);

        // Confirm TableOrderMapper.update(...).
        final TableOrderDO entity = new TableOrderDO();
        entity.setId(0L);
        entity.setTableGuid("tableGuid");
        entity.setMainOrderGuid("mainOrderGuid");
        entity.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        entity.setStatus(0);
        entity.setSubStatus("subStatus");
        entity.setCombineTimes(0);
        entity.setOpenStaffGuid("openStaffGuid");
        entity.setOpenStaffName("openStaffName");
        entity.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockTableOrderMapper).update(eq(entity), any(LambdaUpdateWrapper.class));
        verify(mockBizMsgService).sendCloseMsg("storeGuid", "storeName", "diningTableGuid");

        // Confirm TradeService.closeOrder(...).
        final CancelOrderReqDTO cancelOrderReqDTO1 = new CancelOrderReqDTO();
        cancelOrderReqDTO1.setDeviceType(0);
        cancelOrderReqDTO1.setDeviceId("deviceId");
        cancelOrderReqDTO1.setEnterpriseGuid("enterpriseGuid");
        cancelOrderReqDTO1.setStoreGuid("storeGuid");
        cancelOrderReqDTO1.setStoreName("storeName");
        cancelOrderReqDTO1.setUserGuid("openStaffGuid");
        cancelOrderReqDTO1.setUserName("openStaffName");
        cancelOrderReqDTO1.setTableGuid("tableGuid");
        cancelOrderReqDTO1.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        verify(mockTradeService).closeOrder(cancelOrderReqDTO1);
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testClose_TableOrderMapperSelectOneReturnsNull() {
        // Setup
        final CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setDeviceType(0);
        cancelOrderReqDTO.setDeviceId("deviceId");
        cancelOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        cancelOrderReqDTO.setStoreGuid("storeGuid");
        cancelOrderReqDTO.setStoreName("storeName");
        cancelOrderReqDTO.setUserGuid("openStaffGuid");
        cancelOrderReqDTO.setUserName("openStaffName");
        cancelOrderReqDTO.setTableGuid("tableGuid");
        cancelOrderReqDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");

        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> tableOrderServiceImplUnderTest.close(cancelOrderReqDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testClose_TableOrderMapperSelectListReturnsNoItems() {
        // Setup
        final CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setDeviceType(0);
        cancelOrderReqDTO.setDeviceId("deviceId");
        cancelOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        cancelOrderReqDTO.setStoreGuid("storeGuid");
        cancelOrderReqDTO.setStoreName("storeName");
        cancelOrderReqDTO.setUserGuid("openStaffGuid");
        cancelOrderReqDTO.setUserName("openStaffName");
        cancelOrderReqDTO.setTableGuid("tableGuid");
        cancelOrderReqDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");

        // Configure TableOrderMapper.selectOne(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDO);

        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockTableOrderMapper.closeTable("tableGuid", "8379e3a2-032a-484c-90ec-91cdcaf186b6",
                "subStatus")).thenReturn(0);

        // Configure TradeRpcClient.findByOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setDeviceType(0);
        orderDTO.setGuestCount(0);
        orderDTO.setDiningTableGuid("diningTableGuid");
        orderDTO.setState(0);
        orderDTO.setUpperState(0);
        orderDTO.setMainOrderGuid("mainOrderGuid");
        orderDTO.setStoreGuid("storeGuid");
        orderDTO.setStoreName("storeName");
        when(mockTradeRpcClient.findByOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6")).thenReturn(orderDTO);

        // Configure TradeRpcClient.listOrderByCombineOrderGuid(...).
        final OrderDTO orderDTO1 = new OrderDTO();
        orderDTO1.setDeviceType(0);
        orderDTO1.setGuestCount(0);
        orderDTO1.setDiningTableGuid("diningTableGuid");
        orderDTO1.setState(0);
        orderDTO1.setUpperState(0);
        orderDTO1.setMainOrderGuid("mainOrderGuid");
        orderDTO1.setStoreGuid("storeGuid");
        orderDTO1.setStoreName("storeName");
        final List<OrderDTO> orderDTOS = Arrays.asList(orderDTO1);
        when(mockTradeRpcClient.listOrderByCombineOrderGuid("mainOrderGuid")).thenReturn(orderDTOS);

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO, Arrays.asList("value"))).thenReturn("result");

        // Run the test
        final boolean result = tableOrderServiceImplUnderTest.close(cancelOrderReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm TableOrderMapper.removeCombine(...).
        final TableOrderDO tableOrderDO1 = new TableOrderDO();
        tableOrderDO1.setId(0L);
        tableOrderDO1.setTableGuid("tableGuid");
        tableOrderDO1.setMainOrderGuid("mainOrderGuid");
        tableOrderDO1.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO1.setStatus(0);
        tableOrderDO1.setSubStatus("subStatus");
        tableOrderDO1.setCombineTimes(0);
        tableOrderDO1.setOpenStaffGuid("openStaffGuid");
        tableOrderDO1.setOpenStaffName("openStaffName");
        tableOrderDO1.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> updateList = Arrays.asList(tableOrderDO1);
        verify(mockTableOrderMapper).removeCombine(updateList);

        // Confirm TableOrderMapper.update(...).
        final TableOrderDO entity = new TableOrderDO();
        entity.setId(0L);
        entity.setTableGuid("tableGuid");
        entity.setMainOrderGuid("mainOrderGuid");
        entity.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        entity.setStatus(0);
        entity.setSubStatus("subStatus");
        entity.setCombineTimes(0);
        entity.setOpenStaffGuid("openStaffGuid");
        entity.setOpenStaffName("openStaffName");
        entity.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockTableOrderMapper).update(eq(entity), any(LambdaUpdateWrapper.class));
        verify(mockBizMsgService).sendCloseMsg("storeGuid", "storeName", "diningTableGuid");

        // Confirm TradeService.closeOrder(...).
        final CancelOrderReqDTO cancelOrderReqDTO1 = new CancelOrderReqDTO();
        cancelOrderReqDTO1.setDeviceType(0);
        cancelOrderReqDTO1.setDeviceId("deviceId");
        cancelOrderReqDTO1.setEnterpriseGuid("enterpriseGuid");
        cancelOrderReqDTO1.setStoreGuid("storeGuid");
        cancelOrderReqDTO1.setStoreName("storeName");
        cancelOrderReqDTO1.setUserGuid("openStaffGuid");
        cancelOrderReqDTO1.setUserName("openStaffName");
        cancelOrderReqDTO1.setTableGuid("tableGuid");
        cancelOrderReqDTO1.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        verify(mockTradeService).closeOrder(cancelOrderReqDTO1);
        verify(mockDefaultRocketMqProducer).sendMessage(any(Message.class));
    }

    @Test
    public void testClose_TradeRpcClientListOrderByCombineOrderGuidReturnsNoItems() {
        // Setup
        final CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setDeviceType(0);
        cancelOrderReqDTO.setDeviceId("deviceId");
        cancelOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        cancelOrderReqDTO.setStoreGuid("storeGuid");
        cancelOrderReqDTO.setStoreName("storeName");
        cancelOrderReqDTO.setUserGuid("openStaffGuid");
        cancelOrderReqDTO.setUserName("openStaffName");
        cancelOrderReqDTO.setTableGuid("tableGuid");
        cancelOrderReqDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");

        // Configure TableOrderMapper.selectOne(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDO);

        // Configure TableOrderMapper.selectList(...).
        final TableOrderDO tableOrderDO1 = new TableOrderDO();
        tableOrderDO1.setId(0L);
        tableOrderDO1.setTableGuid("tableGuid");
        tableOrderDO1.setMainOrderGuid("mainOrderGuid");
        tableOrderDO1.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO1.setStatus(0);
        tableOrderDO1.setSubStatus("subStatus");
        tableOrderDO1.setCombineTimes(0);
        tableOrderDO1.setOpenStaffGuid("openStaffGuid");
        tableOrderDO1.setOpenStaffName("openStaffName");
        tableOrderDO1.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> tableOrderDOS = Arrays.asList(tableOrderDO1);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDOS);

        when(mockTableOrderMapper.closeTable("tableGuid", "8379e3a2-032a-484c-90ec-91cdcaf186b6",
                "subStatus")).thenReturn(0);

        // Configure TradeRpcClient.findByOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setDeviceType(0);
        orderDTO.setGuestCount(0);
        orderDTO.setDiningTableGuid("diningTableGuid");
        orderDTO.setState(0);
        orderDTO.setUpperState(0);
        orderDTO.setMainOrderGuid("mainOrderGuid");
        orderDTO.setStoreGuid("storeGuid");
        orderDTO.setStoreName("storeName");
        when(mockTradeRpcClient.findByOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6")).thenReturn(orderDTO);

        when(mockTradeRpcClient.listOrderByCombineOrderGuid("mainOrderGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = tableOrderServiceImplUnderTest.close(cancelOrderReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm TableOrderMapper.removeCombine(...).
        final TableOrderDO tableOrderDO2 = new TableOrderDO();
        tableOrderDO2.setId(0L);
        tableOrderDO2.setTableGuid("tableGuid");
        tableOrderDO2.setMainOrderGuid("mainOrderGuid");
        tableOrderDO2.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO2.setStatus(0);
        tableOrderDO2.setSubStatus("subStatus");
        tableOrderDO2.setCombineTimes(0);
        tableOrderDO2.setOpenStaffGuid("openStaffGuid");
        tableOrderDO2.setOpenStaffName("openStaffName");
        tableOrderDO2.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> updateList = Arrays.asList(tableOrderDO2);
        verify(mockTableOrderMapper).removeCombine(updateList);
        verify(mockBizMsgService).sendCloseMsg("storeGuid", "storeName", "diningTableGuid");
    }

    @Test
    public void testCouldOpen() {
        // Setup
        final OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        openTableDTO.setTableGuid("areaGuid");
        openTableDTO.setTableCode("tableCode");
        openTableDTO.setAreaName("areaName");
        openTableDTO.setActualGuestsNo(0);
        openTableDTO.setAreaGuid("areaGuid");

        when(mockRedisService.isTableLockedByOthers("deviceId", "areaGuid")).thenReturn(false);

        // Configure TableOrderMapper.selectOne(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDO);

        // Run the test
        final boolean result = tableOrderServiceImplUnderTest.couldOpen(openTableDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testCouldOpen_RedisServiceReturnsTrue() {
        // Setup
        final OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        openTableDTO.setTableGuid("areaGuid");
        openTableDTO.setTableCode("tableCode");
        openTableDTO.setAreaName("areaName");
        openTableDTO.setActualGuestsNo(0);
        openTableDTO.setAreaGuid("areaGuid");

        when(mockRedisService.isTableLockedByOthers("deviceId", "areaGuid")).thenReturn(true);

        // Run the test
        assertThatThrownBy(() -> tableOrderServiceImplUnderTest.couldOpen(openTableDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testCouldOpen_TableOrderMapperReturnsNull() {
        // Setup
        final OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        openTableDTO.setTableGuid("areaGuid");
        openTableDTO.setTableCode("tableCode");
        openTableDTO.setAreaName("areaName");
        openTableDTO.setActualGuestsNo(0);
        openTableDTO.setAreaGuid("areaGuid");

        when(mockRedisService.isTableLockedByOthers("deviceId", "areaGuid")).thenReturn(false);
        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> tableOrderServiceImplUnderTest.couldOpen(openTableDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testRecheckOpen() {
        // Setup
        final BaseTableDTO baseTableDTO = new BaseTableDTO("tableGuid", "orderGuid", false);
        final TableWhetherOpenDTO expectedResult = new TableWhetherOpenDTO();
        expectedResult.setDeviceType(0);
        expectedResult.setDeviceId("deviceId");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setUserGuid("openStaffGuid");
        expectedResult.setUserName("openStaffName");
        expectedResult.setTableGuid("tableGuid");
        expectedResult.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        expectedResult.setNewTableCode("tableCode");
        expectedResult.setNewTableAreaName("areaName");
        expectedResult.setWhetherOpened(false);

        // Configure TableOrderMapper.selectFullInfo(...).
        final TableDO tableDO = new TableDO();
        tableDO.setGuid("areaGuid");
        tableDO.setStoreGuid("storeGuid");
        tableDO.setStoreName("storeName");
        tableDO.setAreaGuid("areaGuid");
        tableDO.setAreaName("areaName");
        tableDO.setTableCode("tableCode");
        tableDO.setSeats(0);
        tableDO.setMainOrderGuid("mainOrderGuid");
        tableDO.setOrderGuid("orderGuid");
        tableDO.setStatus(0);
        when(mockTableOrderMapper.selectFullInfo("tableGuid")).thenReturn(tableDO);

        // Configure TableOrderMapper.update(...).
        final TableOrderDO entity = new TableOrderDO();
        entity.setId(0L);
        entity.setTableGuid("tableGuid");
        entity.setMainOrderGuid("mainOrderGuid");
        entity.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        entity.setStatus(0);
        entity.setSubStatus("subStatus");
        entity.setCombineTimes(0);
        entity.setOpenStaffGuid("openStaffGuid");
        entity.setOpenStaffName("openStaffName");
        entity.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.update(eq(entity), any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO, Arrays.asList("value"))).thenReturn("result");

        // Run the test
        final TableWhetherOpenDTO result = tableOrderServiceImplUnderTest.recheckOpen(baseTableDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testRecheckOpen_TableOrderMapperSelectFullInfoReturnsNull() {
        // Setup
        final BaseTableDTO baseTableDTO = new BaseTableDTO("tableGuid", "orderGuid", false);
        when(mockTableOrderMapper.selectFullInfo("tableGuid")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> tableOrderServiceImplUnderTest.recheckOpen(baseTableDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testTryOpen() {
        // Setup
        final OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        openTableDTO.setTableGuid("areaGuid");
        openTableDTO.setTableCode("tableCode");
        openTableDTO.setAreaName("areaName");
        openTableDTO.setActualGuestsNo(0);
        openTableDTO.setAreaGuid("areaGuid");

        when(mockRedisService.singleGuid("hst_order")).thenReturn("8379e3a2-032a-484c-90ec-91cdcaf186b6");

        // Configure TableOrderMapper.update(...).
        final TableOrderDO entity = new TableOrderDO();
        entity.setId(0L);
        entity.setTableGuid("tableGuid");
        entity.setMainOrderGuid("mainOrderGuid");
        entity.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        entity.setStatus(0);
        entity.setSubStatus("subStatus");
        entity.setCombineTimes(0);
        entity.setOpenStaffGuid("openStaffGuid");
        entity.setOpenStaffName("openStaffName");
        entity.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.update(eq(entity), any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure TableOrderMapper.selectOne(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDO);

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO1 = new BaseDTO();
        openTableDTO1.setDeviceType(0);
        openTableDTO1.setDeviceId("deviceId");
        openTableDTO1.setEnterpriseGuid("enterpriseGuid");
        openTableDTO1.setStoreGuid("storeGuid");
        openTableDTO1.setStoreName("storeName");
        openTableDTO1.setUserGuid("openStaffGuid");
        openTableDTO1.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO1, Arrays.asList("value"))).thenReturn("result");

        // Run the test
        final String result = tableOrderServiceImplUnderTest.tryOpen(openTableDTO);

        // Verify the results
        assertThat(result).isEqualTo("8379e3a2-032a-484c-90ec-91cdcaf186b6");

        // Confirm TradeService.openTable(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceType(0);
        createDineInOrderReqDTO.setDeviceId("deviceId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        createDineInOrderReqDTO.setStoreName("storeName");
        createDineInOrderReqDTO.setUserGuid("openStaffGuid");
        createDineInOrderReqDTO.setUserName("openStaffName");
        createDineInOrderReqDTO.setGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        createDineInOrderReqDTO.setDiningTableGuid("areaGuid");
        createDineInOrderReqDTO.setDiningTableName("tableCode");
        createDineInOrderReqDTO.setAreaName("areaName");
        createDineInOrderReqDTO.setGuestCount(0);
        createDineInOrderReqDTO.setAreaGuid("areaGuid");
        verify(mockTradeService).openTable(createDineInOrderReqDTO);
    }

    @Test
    public void testTryOpen_TableOrderMapperSelectOneReturnsNull() {
        // Setup
        final OpenTableDTO openTableDTO = new OpenTableDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        openTableDTO.setTableGuid("areaGuid");
        openTableDTO.setTableCode("tableCode");
        openTableDTO.setAreaName("areaName");
        openTableDTO.setActualGuestsNo(0);
        openTableDTO.setAreaGuid("areaGuid");

        when(mockRedisService.singleGuid("hst_order")).thenReturn("8379e3a2-032a-484c-90ec-91cdcaf186b6");

        // Configure TableOrderMapper.update(...).
        final TableOrderDO entity = new TableOrderDO();
        entity.setId(0L);
        entity.setTableGuid("tableGuid");
        entity.setMainOrderGuid("mainOrderGuid");
        entity.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        entity.setStatus(0);
        entity.setSubStatus("subStatus");
        entity.setCombineTimes(0);
        entity.setOpenStaffGuid("openStaffGuid");
        entity.setOpenStaffName("openStaffName");
        entity.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.update(eq(entity), any(LambdaQueryWrapper.class))).thenReturn(0);

        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> tableOrderServiceImplUnderTest.tryOpen(openTableDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testIsLocked() {
        // Setup
        when(mockRedisService.isTableLockedByOthers("deviceId", "tableGuid")).thenReturn(false);

        // Run the test
        final boolean result = tableOrderServiceImplUnderTest.isLocked("deviceId", "tableGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testIsLocked_RedisServiceReturnsTrue() {
        // Setup
        when(mockRedisService.isTableLockedByOthers("deviceId", "tableGuid")).thenReturn(true);

        // Run the test
        final boolean result = tableOrderServiceImplUnderTest.isLocked("deviceId", "tableGuid");

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testTryLock() {
        // Setup
        final TableLockDTO tableLockDTO = new TableLockDTO();
        tableLockDTO.setDeviceType(0);
        tableLockDTO.setDeviceId("deviceId");
        tableLockDTO.setEnterpriseGuid("enterpriseGuid");
        tableLockDTO.setStoreGuid("storeGuid");
        tableLockDTO.setStoreName("storeName");
        tableLockDTO.setUserGuid("openStaffGuid");
        tableLockDTO.setUserName("openStaffName");
        tableLockDTO.setTableGuid("tableGuid");
        tableLockDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");

        // Configure TableOrderMapper.selectOne(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDO);

        // Configure TableOrderMapper.selectList(...).
        final TableOrderDO tableOrderDO1 = new TableOrderDO();
        tableOrderDO1.setId(0L);
        tableOrderDO1.setTableGuid("tableGuid");
        tableOrderDO1.setMainOrderGuid("mainOrderGuid");
        tableOrderDO1.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO1.setStatus(0);
        tableOrderDO1.setSubStatus("subStatus");
        tableOrderDO1.setCombineTimes(0);
        tableOrderDO1.setOpenStaffGuid("openStaffGuid");
        tableOrderDO1.setOpenStaffName("openStaffName");
        tableOrderDO1.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> tableOrderDOS = Arrays.asList(tableOrderDO1);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDOS);

        // Run the test
        final boolean result = tableOrderServiceImplUnderTest.tryLock(tableLockDTO);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockRedisService).lockSingleTable("tableGuid", "deviceId", "8379e3a2-032a-484c-90ec-91cdcaf186b6", 0);
        verify(mockRedisService).lockMultiTable(
                Arrays.asList(new TableInfoBO("tableGuid", "deviceId", "8379e3a2-032a-484c-90ec-91cdcaf186b6")), 0);

        // Confirm BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        verify(mockBizMsgService).sendMsg(openTableDTO, Arrays.asList("value"));
        verify(mockRelease).wxDoRelease(new DelayAutoUnlockBO(
                Arrays.asList(new TableInfoBO("tableGuid", "deviceId", "8379e3a2-032a-484c-90ec-91cdcaf186b6"))));
        verify(mockRelease).doRelease(new DelayAutoUnlockBO(
                Arrays.asList(new TableInfoBO("tableGuid", "deviceId", "8379e3a2-032a-484c-90ec-91cdcaf186b6"))));
    }

    @Test
    public void testTryLock_TableOrderMapperSelectOneReturnsNull() {
        // Setup
        final TableLockDTO tableLockDTO = new TableLockDTO();
        tableLockDTO.setDeviceType(0);
        tableLockDTO.setDeviceId("deviceId");
        tableLockDTO.setEnterpriseGuid("enterpriseGuid");
        tableLockDTO.setStoreGuid("storeGuid");
        tableLockDTO.setStoreName("storeName");
        tableLockDTO.setUserGuid("openStaffGuid");
        tableLockDTO.setUserName("openStaffName");
        tableLockDTO.setTableGuid("tableGuid");
        tableLockDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");

        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> tableOrderServiceImplUnderTest.tryLock(tableLockDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testTryLock_TableOrderMapperSelectListReturnsNoItems() {
        // Setup
        final TableLockDTO tableLockDTO = new TableLockDTO();
        tableLockDTO.setDeviceType(0);
        tableLockDTO.setDeviceId("deviceId");
        tableLockDTO.setEnterpriseGuid("enterpriseGuid");
        tableLockDTO.setStoreGuid("storeGuid");
        tableLockDTO.setStoreName("storeName");
        tableLockDTO.setUserGuid("openStaffGuid");
        tableLockDTO.setUserName("openStaffName");
        tableLockDTO.setTableGuid("tableGuid");
        tableLockDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");

        // Configure TableOrderMapper.selectOne(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDO);

        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = tableOrderServiceImplUnderTest.tryLock(tableLockDTO);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockRedisService).lockMultiTable(
                Arrays.asList(new TableInfoBO("tableGuid", "deviceId", "8379e3a2-032a-484c-90ec-91cdcaf186b6")), 0);

        // Confirm BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        verify(mockBizMsgService).sendMsg(openTableDTO, Arrays.asList("value"));
        verify(mockRelease).wxDoRelease(new DelayAutoUnlockBO(
                Arrays.asList(new TableInfoBO("tableGuid", "deviceId", "8379e3a2-032a-484c-90ec-91cdcaf186b6"))));
        verify(mockRelease).doRelease(new DelayAutoUnlockBO(
                Arrays.asList(new TableInfoBO("tableGuid", "deviceId", "8379e3a2-032a-484c-90ec-91cdcaf186b6"))));
    }

    @Test
    public void testTryUnlock() {
        // Setup
        final TableLockDTO tableLockDTO = new TableLockDTO();
        tableLockDTO.setDeviceType(0);
        tableLockDTO.setDeviceId("deviceId");
        tableLockDTO.setEnterpriseGuid("enterpriseGuid");
        tableLockDTO.setStoreGuid("storeGuid");
        tableLockDTO.setStoreName("storeName");
        tableLockDTO.setUserGuid("openStaffGuid");
        tableLockDTO.setUserName("openStaffName");
        tableLockDTO.setTableGuid("tableGuid");
        tableLockDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");

        // Configure TableOrderMapper.selectOne(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDO);

        // Configure TableOrderMapper.selectList(...).
        final TableOrderDO tableOrderDO1 = new TableOrderDO();
        tableOrderDO1.setId(0L);
        tableOrderDO1.setTableGuid("tableGuid");
        tableOrderDO1.setMainOrderGuid("mainOrderGuid");
        tableOrderDO1.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO1.setStatus(0);
        tableOrderDO1.setSubStatus("subStatus");
        tableOrderDO1.setCombineTimes(0);
        tableOrderDO1.setOpenStaffGuid("openStaffGuid");
        tableOrderDO1.setOpenStaffName("openStaffName");
        tableOrderDO1.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> tableOrderDOS = Arrays.asList(tableOrderDO1);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDOS);

        when(mockRedisService.isUnlockAllowed("tableGuid", "deviceId",
                "8379e3a2-032a-484c-90ec-91cdcaf186b6")).thenReturn(false);

        // Run the test
        final boolean result = tableOrderServiceImplUnderTest.tryUnlock(tableLockDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testTryUnlock_TableOrderMapperSelectOneReturnsNull() {
        // Setup
        final TableLockDTO tableLockDTO = new TableLockDTO();
        tableLockDTO.setDeviceType(0);
        tableLockDTO.setDeviceId("deviceId");
        tableLockDTO.setEnterpriseGuid("enterpriseGuid");
        tableLockDTO.setStoreGuid("storeGuid");
        tableLockDTO.setStoreName("storeName");
        tableLockDTO.setUserGuid("openStaffGuid");
        tableLockDTO.setUserName("openStaffName");
        tableLockDTO.setTableGuid("tableGuid");
        tableLockDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");

        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> tableOrderServiceImplUnderTest.tryUnlock(tableLockDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testTryUnlock_TableOrderMapperSelectListReturnsNoItems() {
        // Setup
        final TableLockDTO tableLockDTO = new TableLockDTO();
        tableLockDTO.setDeviceType(0);
        tableLockDTO.setDeviceId("deviceId");
        tableLockDTO.setEnterpriseGuid("enterpriseGuid");
        tableLockDTO.setStoreGuid("storeGuid");
        tableLockDTO.setStoreName("storeName");
        tableLockDTO.setUserGuid("openStaffGuid");
        tableLockDTO.setUserName("openStaffName");
        tableLockDTO.setTableGuid("tableGuid");
        tableLockDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");

        // Configure TableOrderMapper.selectOne(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDO);

        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> tableOrderServiceImplUnderTest.tryUnlock(tableLockDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testTryUnlock_RedisServiceIsUnlockAllowedReturnsTrue() {
        // Setup
        final TableLockDTO tableLockDTO = new TableLockDTO();
        tableLockDTO.setDeviceType(0);
        tableLockDTO.setDeviceId("deviceId");
        tableLockDTO.setEnterpriseGuid("enterpriseGuid");
        tableLockDTO.setStoreGuid("storeGuid");
        tableLockDTO.setStoreName("storeName");
        tableLockDTO.setUserGuid("openStaffGuid");
        tableLockDTO.setUserName("openStaffName");
        tableLockDTO.setTableGuid("tableGuid");
        tableLockDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");

        // Configure TableOrderMapper.selectOne(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDO);

        // Configure TableOrderMapper.selectList(...).
        final TableOrderDO tableOrderDO1 = new TableOrderDO();
        tableOrderDO1.setId(0L);
        tableOrderDO1.setTableGuid("tableGuid");
        tableOrderDO1.setMainOrderGuid("mainOrderGuid");
        tableOrderDO1.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO1.setStatus(0);
        tableOrderDO1.setSubStatus("subStatus");
        tableOrderDO1.setCombineTimes(0);
        tableOrderDO1.setOpenStaffGuid("openStaffGuid");
        tableOrderDO1.setOpenStaffName("openStaffName");
        tableOrderDO1.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> tableOrderDOS = Arrays.asList(tableOrderDO1);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDOS);

        when(mockRedisService.isUnlockAllowed("tableGuid", "deviceId",
                "8379e3a2-032a-484c-90ec-91cdcaf186b6")).thenReturn(true);

        // Run the test
        final boolean result = tableOrderServiceImplUnderTest.tryUnlock(tableLockDTO);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockRedisService).unlockMultiTable(Arrays.asList("value"));

        // Confirm BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        verify(mockBizMsgService).sendMsg(openTableDTO, Arrays.asList("value"));
    }

    @Test
    public void testStatusSync() {
        // Setup
        final TableStatusChangeDTO tableStatusChangeDTO = new TableStatusChangeDTO();
        tableStatusChangeDTO.setDeviceType(0);
        tableStatusChangeDTO.setDeviceId("deviceId");
        tableStatusChangeDTO.setEnterpriseGuid("enterpriseGuid");
        tableStatusChangeDTO.setStoreGuid("storeGuid");
        tableStatusChangeDTO.setStoreName("storeName");
        tableStatusChangeDTO.setUserGuid("openStaffGuid");
        tableStatusChangeDTO.setUserName("openStaffName");
        tableStatusChangeDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableStatusChangeDTO.setTableGuid("tableGuid");
        tableStatusChangeDTO.setTableStatusChange(0);

        // Configure TableOrderMapper.selectOne(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDO);

        // Configure TableOrderMapper.update(...).
        final TableOrderDO entity = new TableOrderDO();
        entity.setId(0L);
        entity.setTableGuid("tableGuid");
        entity.setMainOrderGuid("mainOrderGuid");
        entity.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        entity.setStatus(0);
        entity.setSubStatus("subStatus");
        entity.setCombineTimes(0);
        entity.setOpenStaffGuid("openStaffGuid");
        entity.setOpenStaffName("openStaffName");
        entity.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.update(eq(entity), any(LambdaUpdateWrapper.class))).thenReturn(0);

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO, Arrays.asList("value"))).thenReturn("result");

        // Configure TableOrderMapper.selectList(...).
        final TableOrderDO tableOrderDO1 = new TableOrderDO();
        tableOrderDO1.setId(0L);
        tableOrderDO1.setTableGuid("tableGuid");
        tableOrderDO1.setMainOrderGuid("mainOrderGuid");
        tableOrderDO1.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO1.setStatus(0);
        tableOrderDO1.setSubStatus("subStatus");
        tableOrderDO1.setCombineTimes(0);
        tableOrderDO1.setOpenStaffGuid("openStaffGuid");
        tableOrderDO1.setOpenStaffName("openStaffName");
        tableOrderDO1.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> tableOrderDOS = Arrays.asList(tableOrderDO1);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDOS);

        // Configure TradeRpcClient.findByOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setDeviceType(0);
        orderDTO.setGuestCount(0);
        orderDTO.setDiningTableGuid("diningTableGuid");
        orderDTO.setState(0);
        orderDTO.setUpperState(0);
        orderDTO.setMainOrderGuid("mainOrderGuid");
        orderDTO.setStoreGuid("storeGuid");
        orderDTO.setStoreName("storeName");
        when(mockTradeRpcClient.findByOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6")).thenReturn(orderDTO);

        when(mockBindUpAccountsService.checkBindUpAccountStatus("storeGuid")).thenReturn(false);

        // Configure TableOrderMapper.selectAllTable(...).
        final TableDO tableDO = new TableDO();
        tableDO.setGuid("areaGuid");
        tableDO.setStoreGuid("storeGuid");
        tableDO.setStoreName("storeName");
        tableDO.setAreaGuid("areaGuid");
        tableDO.setAreaName("areaName");
        tableDO.setTableCode("tableCode");
        tableDO.setSeats(0);
        tableDO.setMainOrderGuid("mainOrderGuid");
        tableDO.setOrderGuid("orderGuid");
        tableDO.setStatus(0);
        final List<TableDO> tableDOS = Arrays.asList(tableDO);
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setDeviceType(0);
        tableBasicQueryDTO.setDeviceId("deviceId");
        tableBasicQueryDTO.setEnterpriseGuid("enterpriseGuid");
        tableBasicQueryDTO.setStoreName("storeName");
        tableBasicQueryDTO.setUserGuid("openStaffGuid");
        tableBasicQueryDTO.setUserName("openStaffName");
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableOrderMapper.selectAllTable(tableBasicQueryDTO)).thenReturn(tableDOS);

        when(mockBindUpAccountsService.currentTimeDay("storeGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0)))
                .thenReturn(LocalDate.of(2020, 1, 1));

        // Run the test
        final boolean result = tableOrderServiceImplUnderTest.statusSync(tableStatusChangeDTO);

        // Verify the results
        assertThat(result).isFalse();

        // Confirm TableOrderMapper.removeCombine(...).
        final TableOrderDO tableOrderDO2 = new TableOrderDO();
        tableOrderDO2.setId(0L);
        tableOrderDO2.setTableGuid("tableGuid");
        tableOrderDO2.setMainOrderGuid("mainOrderGuid");
        tableOrderDO2.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO2.setStatus(0);
        tableOrderDO2.setSubStatus("subStatus");
        tableOrderDO2.setCombineTimes(0);
        tableOrderDO2.setOpenStaffGuid("openStaffGuid");
        tableOrderDO2.setOpenStaffName("openStaffName");
        tableOrderDO2.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> updateList = Arrays.asList(tableOrderDO2);
        verify(mockTableOrderMapper).removeCombine(updateList);
        verify(mockBizMsgService).sendCloseMsg("storeGuid", "storeName", "tableGuid");
        verify(mockBindUpAccountsService).sendMqForCanOpenTable("storeGuid");
        verify(mockRedisService).unlockMultiTable(Arrays.asList("value"));
    }

    @Test
    public void testStatusSync_TableOrderMapperSelectOneReturnsNull() {
        // Setup
        final TableStatusChangeDTO tableStatusChangeDTO = new TableStatusChangeDTO();
        tableStatusChangeDTO.setDeviceType(0);
        tableStatusChangeDTO.setDeviceId("deviceId");
        tableStatusChangeDTO.setEnterpriseGuid("enterpriseGuid");
        tableStatusChangeDTO.setStoreGuid("storeGuid");
        tableStatusChangeDTO.setStoreName("storeName");
        tableStatusChangeDTO.setUserGuid("openStaffGuid");
        tableStatusChangeDTO.setUserName("openStaffName");
        tableStatusChangeDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableStatusChangeDTO.setTableGuid("tableGuid");
        tableStatusChangeDTO.setTableStatusChange(0);

        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> tableOrderServiceImplUnderTest.statusSync(tableStatusChangeDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testStatusSync_TableOrderMapperSelectListReturnsNoItems() {
        // Setup
        final TableStatusChangeDTO tableStatusChangeDTO = new TableStatusChangeDTO();
        tableStatusChangeDTO.setDeviceType(0);
        tableStatusChangeDTO.setDeviceId("deviceId");
        tableStatusChangeDTO.setEnterpriseGuid("enterpriseGuid");
        tableStatusChangeDTO.setStoreGuid("storeGuid");
        tableStatusChangeDTO.setStoreName("storeName");
        tableStatusChangeDTO.setUserGuid("openStaffGuid");
        tableStatusChangeDTO.setUserName("openStaffName");
        tableStatusChangeDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableStatusChangeDTO.setTableGuid("tableGuid");
        tableStatusChangeDTO.setTableStatusChange(0);

        // Configure TableOrderMapper.selectOne(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDO);

        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure TableOrderMapper.update(...).
        final TableOrderDO entity = new TableOrderDO();
        entity.setId(0L);
        entity.setTableGuid("tableGuid");
        entity.setMainOrderGuid("mainOrderGuid");
        entity.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        entity.setStatus(0);
        entity.setSubStatus("subStatus");
        entity.setCombineTimes(0);
        entity.setOpenStaffGuid("openStaffGuid");
        entity.setOpenStaffName("openStaffName");
        entity.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.update(eq(entity), any(LambdaUpdateWrapper.class))).thenReturn(0);

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO, Arrays.asList("value"))).thenReturn("result");

        // Configure TradeRpcClient.findByOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setDeviceType(0);
        orderDTO.setGuestCount(0);
        orderDTO.setDiningTableGuid("diningTableGuid");
        orderDTO.setState(0);
        orderDTO.setUpperState(0);
        orderDTO.setMainOrderGuid("mainOrderGuid");
        orderDTO.setStoreGuid("storeGuid");
        orderDTO.setStoreName("storeName");
        when(mockTradeRpcClient.findByOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6")).thenReturn(orderDTO);

        when(mockBindUpAccountsService.checkBindUpAccountStatus("storeGuid")).thenReturn(false);

        // Run the test
        final boolean result = tableOrderServiceImplUnderTest.statusSync(tableStatusChangeDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm TableOrderMapper.removeCombine(...).
        final TableOrderDO tableOrderDO1 = new TableOrderDO();
        tableOrderDO1.setId(0L);
        tableOrderDO1.setTableGuid("tableGuid");
        tableOrderDO1.setMainOrderGuid("mainOrderGuid");
        tableOrderDO1.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO1.setStatus(0);
        tableOrderDO1.setSubStatus("subStatus");
        tableOrderDO1.setCombineTimes(0);
        tableOrderDO1.setOpenStaffGuid("openStaffGuid");
        tableOrderDO1.setOpenStaffName("openStaffName");
        tableOrderDO1.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> updateList = Arrays.asList(tableOrderDO1);
        verify(mockTableOrderMapper).removeCombine(updateList);
        verify(mockBizMsgService).sendCloseMsg("storeGuid", "storeName", "tableGuid");
    }

    @Test
    public void testStatusSync_BindUpAccountsServiceCheckBindUpAccountStatusReturnsTrue() {
        // Setup
        final TableStatusChangeDTO tableStatusChangeDTO = new TableStatusChangeDTO();
        tableStatusChangeDTO.setDeviceType(0);
        tableStatusChangeDTO.setDeviceId("deviceId");
        tableStatusChangeDTO.setEnterpriseGuid("enterpriseGuid");
        tableStatusChangeDTO.setStoreGuid("storeGuid");
        tableStatusChangeDTO.setStoreName("storeName");
        tableStatusChangeDTO.setUserGuid("openStaffGuid");
        tableStatusChangeDTO.setUserName("openStaffName");
        tableStatusChangeDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableStatusChangeDTO.setTableGuid("tableGuid");
        tableStatusChangeDTO.setTableStatusChange(0);

        // Configure TableOrderMapper.selectOne(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDO);

        // Configure TableOrderMapper.update(...).
        final TableOrderDO entity = new TableOrderDO();
        entity.setId(0L);
        entity.setTableGuid("tableGuid");
        entity.setMainOrderGuid("mainOrderGuid");
        entity.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        entity.setStatus(0);
        entity.setSubStatus("subStatus");
        entity.setCombineTimes(0);
        entity.setOpenStaffGuid("openStaffGuid");
        entity.setOpenStaffName("openStaffName");
        entity.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.update(eq(entity), any(LambdaUpdateWrapper.class))).thenReturn(0);

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO, Arrays.asList("value"))).thenReturn("result");

        // Configure TableOrderMapper.selectList(...).
        final TableOrderDO tableOrderDO1 = new TableOrderDO();
        tableOrderDO1.setId(0L);
        tableOrderDO1.setTableGuid("tableGuid");
        tableOrderDO1.setMainOrderGuid("mainOrderGuid");
        tableOrderDO1.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO1.setStatus(0);
        tableOrderDO1.setSubStatus("subStatus");
        tableOrderDO1.setCombineTimes(0);
        tableOrderDO1.setOpenStaffGuid("openStaffGuid");
        tableOrderDO1.setOpenStaffName("openStaffName");
        tableOrderDO1.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> tableOrderDOS = Arrays.asList(tableOrderDO1);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDOS);

        // Configure TradeRpcClient.findByOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setDeviceType(0);
        orderDTO.setGuestCount(0);
        orderDTO.setDiningTableGuid("diningTableGuid");
        orderDTO.setState(0);
        orderDTO.setUpperState(0);
        orderDTO.setMainOrderGuid("mainOrderGuid");
        orderDTO.setStoreGuid("storeGuid");
        orderDTO.setStoreName("storeName");
        when(mockTradeRpcClient.findByOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6")).thenReturn(orderDTO);

        when(mockBindUpAccountsService.checkBindUpAccountStatus("storeGuid")).thenReturn(true);

        // Configure TableOrderMapper.selectAllTable(...).
        final TableDO tableDO = new TableDO();
        tableDO.setGuid("areaGuid");
        tableDO.setStoreGuid("storeGuid");
        tableDO.setStoreName("storeName");
        tableDO.setAreaGuid("areaGuid");
        tableDO.setAreaName("areaName");
        tableDO.setTableCode("tableCode");
        tableDO.setSeats(0);
        tableDO.setMainOrderGuid("mainOrderGuid");
        tableDO.setOrderGuid("orderGuid");
        tableDO.setStatus(0);
        final List<TableDO> tableDOS = Arrays.asList(tableDO);
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setDeviceType(0);
        tableBasicQueryDTO.setDeviceId("deviceId");
        tableBasicQueryDTO.setEnterpriseGuid("enterpriseGuid");
        tableBasicQueryDTO.setStoreName("storeName");
        tableBasicQueryDTO.setUserGuid("openStaffGuid");
        tableBasicQueryDTO.setUserName("openStaffName");
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableOrderMapper.selectAllTable(tableBasicQueryDTO)).thenReturn(tableDOS);

        when(mockBindUpAccountsService.currentTimeDay("storeGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0)))
                .thenReturn(LocalDate.of(2020, 1, 1));

        // Run the test
        final boolean result = tableOrderServiceImplUnderTest.statusSync(tableStatusChangeDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm TableOrderMapper.removeCombine(...).
        final TableOrderDO tableOrderDO2 = new TableOrderDO();
        tableOrderDO2.setId(0L);
        tableOrderDO2.setTableGuid("tableGuid");
        tableOrderDO2.setMainOrderGuid("mainOrderGuid");
        tableOrderDO2.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO2.setStatus(0);
        tableOrderDO2.setSubStatus("subStatus");
        tableOrderDO2.setCombineTimes(0);
        tableOrderDO2.setOpenStaffGuid("openStaffGuid");
        tableOrderDO2.setOpenStaffName("openStaffName");
        tableOrderDO2.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> updateList = Arrays.asList(tableOrderDO2);
        verify(mockTableOrderMapper).removeCombine(updateList);
        verify(mockBizMsgService).sendCloseMsg("storeGuid", "storeName", "tableGuid");
        verify(mockBindUpAccountsService).sendMqForCanOpenTable("storeGuid");
    }

    @Test
    public void testStatusSync_TableOrderMapperSelectAllTableReturnsNoItems() {
        // Setup
        final TableStatusChangeDTO tableStatusChangeDTO = new TableStatusChangeDTO();
        tableStatusChangeDTO.setDeviceType(0);
        tableStatusChangeDTO.setDeviceId("deviceId");
        tableStatusChangeDTO.setEnterpriseGuid("enterpriseGuid");
        tableStatusChangeDTO.setStoreGuid("storeGuid");
        tableStatusChangeDTO.setStoreName("storeName");
        tableStatusChangeDTO.setUserGuid("openStaffGuid");
        tableStatusChangeDTO.setUserName("openStaffName");
        tableStatusChangeDTO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableStatusChangeDTO.setTableGuid("tableGuid");
        tableStatusChangeDTO.setTableStatusChange(0);

        // Configure TableOrderMapper.selectOne(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDO);

        // Configure TableOrderMapper.update(...).
        final TableOrderDO entity = new TableOrderDO();
        entity.setId(0L);
        entity.setTableGuid("tableGuid");
        entity.setMainOrderGuid("mainOrderGuid");
        entity.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        entity.setStatus(0);
        entity.setSubStatus("subStatus");
        entity.setCombineTimes(0);
        entity.setOpenStaffGuid("openStaffGuid");
        entity.setOpenStaffName("openStaffName");
        entity.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.update(eq(entity), any(LambdaUpdateWrapper.class))).thenReturn(0);

        // Configure BizMsgService.sendMsg(...).
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");
        openTableDTO.setUserGuid("openStaffGuid");
        openTableDTO.setUserName("openStaffName");
        when(mockBizMsgService.sendMsg(openTableDTO, Arrays.asList("value"))).thenReturn("result");

        // Configure TableOrderMapper.selectList(...).
        final TableOrderDO tableOrderDO1 = new TableOrderDO();
        tableOrderDO1.setId(0L);
        tableOrderDO1.setTableGuid("tableGuid");
        tableOrderDO1.setMainOrderGuid("mainOrderGuid");
        tableOrderDO1.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO1.setStatus(0);
        tableOrderDO1.setSubStatus("subStatus");
        tableOrderDO1.setCombineTimes(0);
        tableOrderDO1.setOpenStaffGuid("openStaffGuid");
        tableOrderDO1.setOpenStaffName("openStaffName");
        tableOrderDO1.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> tableOrderDOS = Arrays.asList(tableOrderDO1);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDOS);

        // Configure TradeRpcClient.findByOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setDeviceType(0);
        orderDTO.setGuestCount(0);
        orderDTO.setDiningTableGuid("diningTableGuid");
        orderDTO.setState(0);
        orderDTO.setUpperState(0);
        orderDTO.setMainOrderGuid("mainOrderGuid");
        orderDTO.setStoreGuid("storeGuid");
        orderDTO.setStoreName("storeName");
        when(mockTradeRpcClient.findByOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6")).thenReturn(orderDTO);

        when(mockBindUpAccountsService.checkBindUpAccountStatus("storeGuid")).thenReturn(true);

        // Configure TableOrderMapper.selectAllTable(...).
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setDeviceType(0);
        tableBasicQueryDTO.setDeviceId("deviceId");
        tableBasicQueryDTO.setEnterpriseGuid("enterpriseGuid");
        tableBasicQueryDTO.setStoreName("storeName");
        tableBasicQueryDTO.setUserGuid("openStaffGuid");
        tableBasicQueryDTO.setUserName("openStaffName");
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableOrderMapper.selectAllTable(tableBasicQueryDTO)).thenReturn(Collections.emptyList());

        when(mockBindUpAccountsService.currentTimeDay("storeGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0)))
                .thenReturn(LocalDate.of(2020, 1, 1));

        // Run the test
        final boolean result = tableOrderServiceImplUnderTest.statusSync(tableStatusChangeDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm TableOrderMapper.removeCombine(...).
        final TableOrderDO tableOrderDO2 = new TableOrderDO();
        tableOrderDO2.setId(0L);
        tableOrderDO2.setTableGuid("tableGuid");
        tableOrderDO2.setMainOrderGuid("mainOrderGuid");
        tableOrderDO2.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO2.setStatus(0);
        tableOrderDO2.setSubStatus("subStatus");
        tableOrderDO2.setCombineTimes(0);
        tableOrderDO2.setOpenStaffGuid("openStaffGuid");
        tableOrderDO2.setOpenStaffName("openStaffName");
        tableOrderDO2.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<TableOrderDO> updateList = Arrays.asList(tableOrderDO2);
        verify(mockTableOrderMapper).removeCombine(updateList);
        verify(mockBizMsgService).sendCloseMsg("storeGuid", "storeName", "tableGuid");
        verify(mockBindUpAccountsService).sendMqForCanOpenTable("storeGuid");
    }

    @Test
    public void testCompensationStatus() {
        // Setup
        final List<CompensationTableDTO> list = Arrays.asList(new CompensationTableDTO("tableGuid", "orderGuid"));

        // Run the test
        tableOrderServiceImplUnderTest.compensationStatus(list);

        // Verify the results
        verify(mockTableOrderMapper).compensationStatus(
                Arrays.asList(new CompensationTableDTO("tableGuid", "orderGuid")));
    }

    @Test
    public void testQueryAreaGuidByTableGuid() {
        // Setup
        // Configure TableOrderMapper.selectFullInfo(...).
        final TableDO tableDO = new TableDO();
        tableDO.setGuid("areaGuid");
        tableDO.setStoreGuid("storeGuid");
        tableDO.setStoreName("storeName");
        tableDO.setAreaGuid("areaGuid");
        tableDO.setAreaName("areaName");
        tableDO.setTableCode("tableCode");
        tableDO.setSeats(0);
        tableDO.setMainOrderGuid("mainOrderGuid");
        tableDO.setOrderGuid("orderGuid");
        tableDO.setStatus(0);
        when(mockTableOrderMapper.selectFullInfo("tableGuid")).thenReturn(tableDO);

        // Run the test
        final String result = tableOrderServiceImplUnderTest.queryAreaGuidByTableGuid("tableGuid");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testQueryAreaGuidByTableGuid_TableOrderMapperReturnsNull() {
        // Setup
        when(mockTableOrderMapper.selectFullInfo("tableGuid")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> tableOrderServiceImplUnderTest.queryAreaGuidByTableGuid("tableGuid"))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testQueryOrderGuidByTableGuid() {
        // Setup
        // Configure TableOrderMapper.selectOne(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDO);

        // Run the test
        final String result = tableOrderServiceImplUnderTest.queryOrderGuidByTableGuid("tableGuid");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testQueryOrderGuidByTableGuid_TableOrderMapperReturnsNull() {
        // Setup
        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        final String result = tableOrderServiceImplUnderTest.queryOrderGuidByTableGuid("tableGuid");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testTableList() {
        // Setup
        final WxStoreTableCombineDTO wxStoreTableCombineDTO = new WxStoreTableCombineDTO();
        wxStoreTableCombineDTO.setTableGuid("tableGuid");
        wxStoreTableCombineDTO.setAreaGuid("areaGuid");
        wxStoreTableCombineDTO.setAreaName("areaName");
        wxStoreTableCombineDTO.setTableCode("tableCode");
        wxStoreTableCombineDTO.setSeats(0);
        final List<WxStoreTableCombineDTO> expectedResult = Arrays.asList(wxStoreTableCombineDTO);

        // Run the test
        final List<WxStoreTableCombineDTO> result = tableOrderServiceImplUnderTest.tableList("tableGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetTableByGuid() {
        // Setup
        final TableDTO expectedResult = new TableDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setAreaGuid("areaGuid");
        expectedResult.setAreaName("areaName");
        expectedResult.setGuestCount(0);

        // Configure TableOrderMapper.selectFullInfo(...).
        final TableDO tableDO = new TableDO();
        tableDO.setGuid("areaGuid");
        tableDO.setStoreGuid("storeGuid");
        tableDO.setStoreName("storeName");
        tableDO.setAreaGuid("areaGuid");
        tableDO.setAreaName("areaName");
        tableDO.setTableCode("tableCode");
        tableDO.setSeats(0);
        tableDO.setMainOrderGuid("mainOrderGuid");
        tableDO.setOrderGuid("orderGuid");
        tableDO.setStatus(0);
        when(mockTableOrderMapper.selectFullInfo("tableGuid")).thenReturn(tableDO);

        // Configure TradeRpcClient.findByOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setDeviceType(0);
        orderDTO.setGuestCount(0);
        orderDTO.setDiningTableGuid("diningTableGuid");
        orderDTO.setState(0);
        orderDTO.setUpperState(0);
        orderDTO.setMainOrderGuid("mainOrderGuid");
        orderDTO.setStoreGuid("storeGuid");
        orderDTO.setStoreName("storeName");
        when(mockTradeRpcClient.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        // Run the test
        final TableDTO result = tableOrderServiceImplUnderTest.getFullTableByGuid("tableGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateBatchByTableOrderGuid() {
        // Setup
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("8379e3a2-032a-484c-90ec-91cdcaf186b6");
        tableOrderDO.setStatus(0);
        tableOrderDO.setSubStatus("subStatus");
        tableOrderDO.setCombineTimes(0);
        tableOrderDO.setOpenStaffGuid("openStaffGuid");
        tableOrderDO.setOpenStaffName("openStaffName");
        tableOrderDO.setOpenTableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final Collection<TableOrderDO> entityList = Arrays.asList(tableOrderDO);

        // Run the test
        final boolean result = tableOrderServiceImplUnderTest.updateBatchByTableOrderGuid(entityList);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testTableCombineList() {
        // Setup
        final WxStoreTableCombineDTO wxStoreTableCombineDTO = new WxStoreTableCombineDTO();
        wxStoreTableCombineDTO.setTableGuid("tableGuid");
        wxStoreTableCombineDTO.setAreaGuid("areaGuid");
        wxStoreTableCombineDTO.setAreaName("areaName");
        wxStoreTableCombineDTO.setTableCode("tableCode");
        wxStoreTableCombineDTO.setSeats(0);
        final List<WxStoreTableCombineDTO> expectedResult = Arrays.asList(wxStoreTableCombineDTO);

        // Configure TableOrderMapper.tableCombineList(...).
        final WxStoreTableCombineDTO wxStoreTableCombineDTO1 = new WxStoreTableCombineDTO();
        wxStoreTableCombineDTO1.setTableGuid("tableGuid");
        wxStoreTableCombineDTO1.setAreaGuid("areaGuid");
        wxStoreTableCombineDTO1.setAreaName("areaName");
        wxStoreTableCombineDTO1.setTableCode("tableCode");
        wxStoreTableCombineDTO1.setSeats(0);
        final List<WxStoreTableCombineDTO> wxStoreTableCombineDTOS = Arrays.asList(wxStoreTableCombineDTO1);
        when(mockTableOrderMapper.tableCombineList("orderGuid", "tableGuid")).thenReturn(wxStoreTableCombineDTOS);

        // Run the test
        final List<WxStoreTableCombineDTO> result = tableOrderServiceImplUnderTest.tableCombineList("orderGuid",
                "tableGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testTableCombineList_TableOrderMapperReturnsNoItems() {
        // Setup
        when(mockTableOrderMapper.tableCombineList("orderGuid", "tableGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final List<WxStoreTableCombineDTO> result = tableOrderServiceImplUnderTest.tableCombineList("orderGuid",
                "tableGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
