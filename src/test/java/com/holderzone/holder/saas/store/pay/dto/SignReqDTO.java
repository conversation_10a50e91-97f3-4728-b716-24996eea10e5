package com.holderzone.holder.saas.store.pay.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 签约 DTO
 */
@Data
public class SignReqDTO implements Serializable {

    private static final long serialVersionUID = 783426453487749933L;

    @ApiModelProperty(value = "商户唯一标识，appId")
    @NotBlank
    protected String appId;

    @ApiModelProperty(value = "发起请求的时间")
    @NotNull
    protected Long timestamp;

    @ApiModelProperty(value = "开发者ID")
    @NotBlank
    protected String developerId;

    @ApiModelProperty(value = "签名")
    @NotBlank
    protected String signature;

    @NotNull(message = "平台不能为空")
    @ApiModelProperty(value = "平台")
    private String platform;

    @NotNull(message = "支付渠道不能为空")
    @ApiModelProperty(value = "支付渠道code")
    private String channelCode;

    @NotNull(message = "签约渠道不能为空")
    @ApiModelProperty(value = "签约渠道")
    private String signedChannel;

    @NotBlank(message = "签约完成的回调地址不能为空")
    @ApiModelProperty(value = "签约完成的回调地址")
    private String notifyUrl;

    /**
     * 用户点击签约结果按钮后跳转的地址
     * 翼支付 h5签约 需要传入
     */
    @ApiModelProperty(value = "用户点击签约结果按钮后跳转的地址")
    private String frontNotifyUrl;

    @NotBlank(message = "签约用户id不能为空")
    @ApiModelProperty(value = "签约用户id")
    private String userNo;

    @ApiModelProperty(value = "签约/解约guid")
    private String signGuid;
}
