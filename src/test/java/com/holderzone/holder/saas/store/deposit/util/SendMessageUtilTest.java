package com.holderzone.holder.saas.store.deposit.util;

import com.holderzone.framework.base.dto.message.MailMessageDTO;
import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.framework.base.dto.message.MessageType;
import com.holderzone.holder.saas.store.deposit.service.SendMessageService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class SendMessageUtilTest {

    @Mock
    private SendMessageService mockSendMessageService;

    @InjectMocks
    private SendMessageUtil sendMessageUtilUnderTest;

    @Test
    public void testSendMessage() throws Exception {
        // Setup
        final MessageDTO m1 = new MessageDTO();
        m1.setMessageType(MessageType.SHORT_MESSAGE);
        final MailMessageDTO mailMessage = new MailMessageDTO();
        mailMessage.setSubject("subject");
        mailMessage.setContext("context");
        mailMessage.setReceivers(Arrays.asList("value"));
        m1.setMailMessage(mailMessage);

        // Run the test
        sendMessageUtilUnderTest.sendMessage(m1, "entGuid");

        // Verify the results
        verify(mockSendMessageService).sendMessage(any(MessageDTO.class), eq("entGuid"));
    }

    @Test
    public void testSendMessage_SendMessageServiceThrowsException() throws Exception {
        // Setup
        final MessageDTO m1 = new MessageDTO();
        m1.setMessageType(MessageType.SHORT_MESSAGE);
        final MailMessageDTO mailMessage = new MailMessageDTO();
        mailMessage.setSubject("subject");
        mailMessage.setContext("context");
        mailMessage.setReceivers(Arrays.asList("value"));
        m1.setMailMessage(mailMessage);

        doThrow(Exception.class).when(mockSendMessageService).sendMessage(any(MessageDTO.class), eq("entGuid"));

        // Run the test
        sendMessageUtilUnderTest.sendMessage(m1, "entGuid");

        // Verify the results
    }
}
