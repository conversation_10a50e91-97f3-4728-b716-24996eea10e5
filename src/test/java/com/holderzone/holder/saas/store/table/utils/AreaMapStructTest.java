package com.holderzone.holder.saas.store.table.utils;

import com.holderzone.holder.saas.store.table.domain.bo.AreaBO;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * {@link AreaMapStructTest}
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/20 11:16
 */
public class AreaMapStructTest {

    AreaMapStruct areaMapStruct = AreaMapStruct.AREA_MAP_STRUCT;

    @Before
    public void before(){
        System.out.println("============== start ================");
    }

    @After
    public void after(){
        System.out.println("============== end ================");
    }

    @Test(timeout = 1000)
    public void testAreaBo2Do(){
        AreaBO areaBO = new AreaBO();
        String areaName = "ASD";
        areaBO.setAreaName(areaName);
        Assert.assertEquals(areaName,areaMapStruct.areaBo2Do(areaBO).getAreaName());
    }

    @Test(timeout = 1000)
    public void testAreaBo2Do2(){
        AreaBO areaBO = new AreaBO();
        String areaName = "ASDss";
        areaBO.setAreaName(areaName);
        Assert.assertEquals(areaName,areaMapStruct.areaBo2Do(areaBO).getAreaName());
    }

}