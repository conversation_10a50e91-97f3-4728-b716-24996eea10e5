package com.holderzone.holder.saas.store.table.service.impl;

import com.holderzone.holder.saas.store.table.client.PrintRpcClient;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.table.TurnTableDTO;
import com.holderzone.saas.store.enums.print.PrintSourceEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrintServiceImplTest {

    @Mock
    private PrintRpcClient mockPrintRpcClient;

    private PrintServiceImpl printServiceImplUnderTest;

    @Before
    public void setUp() {
        printServiceImplUnderTest = new PrintServiceImpl(mockPrintRpcClient);
    }

    @Test
    public void testPrintTurnTable() {
        // Setup
        final TurnTableDTO turnTableDTO = new TurnTableDTO();
        turnTableDTO.setDeviceType(0);
        turnTableDTO.setEnterpriseGuid("enterpriseGuid");
        turnTableDTO.setOriginTableCode("originTableCode");
        turnTableDTO.setOriginTableAreaName("originTableAreaName");
        turnTableDTO.setNewTableAreaName("newTableAreaName");
        turnTableDTO.setNewTableCode("newTableCode");

        // Configure PrintRpcClient.print(...).
        final PrintDTO printDTO = new PrintDTO();
        printDTO.setInvoiceType(0);
        printDTO.setEnterpriseGuid("enterpriseGuid");
        printDTO.setStoreGuid("storeGuid");
        printDTO.setCreateTime(0L);
        printDTO.setPrintSourceEnum(PrintSourceEnum.AIO);
        when(mockPrintRpcClient.print(printDTO)).thenReturn("result");

        // Run the test
        printServiceImplUnderTest.printTurnTable(turnTableDTO);

        // Verify the results
    }
}
