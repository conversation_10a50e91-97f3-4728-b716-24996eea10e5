package com.holderzone.holder.saas.store.deposit.mapstruct;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.holderzone.holder.saas.store.deposit.entity.bo.OperationDO;
import com.holderzone.holder.saas.store.deposit.entity.bo.OperationGoodsDO;
import com.holderzone.saas.store.dto.deposit.req.OperationCreateReqDTO;
import com.holderzone.saas.store.dto.deposit.resp.GoodsSimpleRespDTO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@ContextConfiguration(classes = {OperationMapstructImpl.class})
@RunWith(SpringJUnit4ClassRunner.class)
public class OperationMapstructImplDiffblueTest {
    @Autowired
    private OperationMapstructImpl operationMapstructImpl;

    /**
     * Method under test:
     * {@link OperationMapstructImpl#fromOperationDTO(OperationCreateReqDTO)}
     */
    @Test
    public void testFromOperationDTO() {
        OperationCreateReqDTO operationCreateReqDTO = new OperationCreateReqDTO();
        operationCreateReqDTO.setOperationWay(1);
        operationCreateReqDTO.setOperator("Operator");
        operationCreateReqDTO.setRemark("Remark");
        operationCreateReqDTO.setUserId("42");
        OperationDO actualFromOperationDTOResult = operationMapstructImpl.fromOperationDTO(operationCreateReqDTO);
        assertEquals("42", actualFromOperationDTOResult.getUserId());
        assertEquals("Operator", actualFromOperationDTOResult.getOperator());
        assertEquals("Remark", actualFromOperationDTOResult.getRemark());
        assertEquals(1, actualFromOperationDTOResult.getOperationWay());
    }

    /**
     * Method under test:
     * {@link OperationMapstructImpl#fromOperationDTO(OperationCreateReqDTO)}
     */
    @Test
    public void testFromOperationDTO2() {
        OperationCreateReqDTO operationCreateReqDTO = mock(OperationCreateReqDTO.class);
        when(operationCreateReqDTO.getOperationWay()).thenReturn(1);
        when(operationCreateReqDTO.getOperator()).thenReturn("Operator");
        when(operationCreateReqDTO.getRemark()).thenReturn("Remark");
        when(operationCreateReqDTO.getUserId()).thenReturn("42");
        doNothing().when(operationCreateReqDTO).setOperationWay(anyInt());
        doNothing().when(operationCreateReqDTO).setOperator(Mockito.<String>any());
        doNothing().when(operationCreateReqDTO).setRemark(Mockito.<String>any());
        doNothing().when(operationCreateReqDTO).setUserId(Mockito.<String>any());
        operationCreateReqDTO.setOperationWay(1);
        operationCreateReqDTO.setOperator("Operator");
        operationCreateReqDTO.setRemark("Remark");
        operationCreateReqDTO.setUserId("42");
        OperationDO actualFromOperationDTOResult = operationMapstructImpl.fromOperationDTO(operationCreateReqDTO);
        verify(operationCreateReqDTO).getOperationWay();
        verify(operationCreateReqDTO).getOperator();
        verify(operationCreateReqDTO).getRemark();
        verify(operationCreateReqDTO).getUserId();
        verify(operationCreateReqDTO).setOperationWay(anyInt());
        verify(operationCreateReqDTO).setOperator(Mockito.<String>any());
        verify(operationCreateReqDTO).setRemark(Mockito.<String>any());
        verify(operationCreateReqDTO).setUserId(Mockito.<String>any());
        assertEquals("42", actualFromOperationDTOResult.getUserId());
        assertEquals("Operator", actualFromOperationDTOResult.getOperator());
        assertEquals("Remark", actualFromOperationDTOResult.getRemark());
        assertEquals(1, actualFromOperationDTOResult.getOperationWay());
    }

    /**
     * Method under test: {@link OperationMapstructImpl#fromGoodsOfOperation(List)}
     */
    @Test
    public void testFromGoodsOfOperation() {
        assertTrue(operationMapstructImpl.fromGoodsOfOperation(new ArrayList<>()).isEmpty());
    }

    /**
     * Method under test: {@link OperationMapstructImpl#fromGoodsOfOperation(List)}
     */
    @Test
    public void testFromGoodsOfOperation2() {
        OperationGoodsDO operationGoodsDO = new OperationGoodsDO();
        operationGoodsDO.setDepositGoodGuid("1234");
        operationGoodsDO.setDepositGuid("1234");
        operationGoodsDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO.setGoodsName("Goods Name");
        operationGoodsDO.setGuid("1234");
        operationGoodsDO.setId(1L);
        operationGoodsDO.setOperatorNum(10);
        operationGoodsDO.setResidueNum(1);
        operationGoodsDO.setSkuGuid("1234");
        operationGoodsDO.setSkuName("Sku Name");

        ArrayList<OperationGoodsDO> list = new ArrayList<>();
        list.add(operationGoodsDO);
        List<GoodsSimpleRespDTO> actualFromGoodsOfOperationResult = operationMapstructImpl.fromGoodsOfOperation(list);
        assertEquals(1, actualFromGoodsOfOperationResult.size());
        GoodsSimpleRespDTO getResult = actualFromGoodsOfOperationResult.get(0);
        assertEquals("Goods Name", getResult.getGoodsName());
        assertEquals("Sku Name", getResult.getSkuName());
        assertEquals(1, getResult.getResidueNum());
        assertEquals(10, getResult.getOperatorNum());
    }

    /**
     * Method under test: {@link OperationMapstructImpl#fromGoodsOfOperation(List)}
     */
    @Test
    public void testFromGoodsOfOperation3() {
        OperationGoodsDO operationGoodsDO = new OperationGoodsDO();
        operationGoodsDO.setDepositGoodGuid("1234");
        operationGoodsDO.setDepositGuid("1234");
        operationGoodsDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO.setGoodsName("Goods Name");
        operationGoodsDO.setGuid("1234");
        operationGoodsDO.setId(1L);
        operationGoodsDO.setOperatorNum(10);
        operationGoodsDO.setResidueNum(1);
        operationGoodsDO.setSkuGuid("1234");
        operationGoodsDO.setSkuName("Sku Name");

        OperationGoodsDO operationGoodsDO2 = new OperationGoodsDO();
        operationGoodsDO2.setDepositGoodGuid("Deposit Good Guid");
        operationGoodsDO2.setDepositGuid("Deposit Guid");
        operationGoodsDO2.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO2.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO2.setGoodsName("com.holderzone.holder.saas.store.deposit.entity.bo.OperationGoodsDO");
        operationGoodsDO2.setGuid("Guid");
        operationGoodsDO2.setId(2L);
        operationGoodsDO2.setOperatorNum(1);
        operationGoodsDO2.setResidueNum(2);
        operationGoodsDO2.setSkuGuid("Sku Guid");
        operationGoodsDO2.setSkuName("com.holderzone.holder.saas.store.deposit.entity.bo.OperationGoodsDO");

        ArrayList<OperationGoodsDO> list = new ArrayList<>();
        list.add(operationGoodsDO2);
        list.add(operationGoodsDO);
        List<GoodsSimpleRespDTO> actualFromGoodsOfOperationResult = operationMapstructImpl.fromGoodsOfOperation(list);
        assertEquals(2, actualFromGoodsOfOperationResult.size());
        GoodsSimpleRespDTO getResult = actualFromGoodsOfOperationResult.get(1);
        assertEquals("Goods Name", getResult.getGoodsName());
        assertEquals("Sku Name", getResult.getSkuName());
        GoodsSimpleRespDTO getResult2 = actualFromGoodsOfOperationResult.get(0);
        assertEquals("com.holderzone.holder.saas.store.deposit.entity.bo.OperationGoodsDO", getResult2.getGoodsName());
        assertEquals("com.holderzone.holder.saas.store.deposit.entity.bo.OperationGoodsDO", getResult2.getSkuName());
        assertEquals(1, getResult2.getOperatorNum());
        assertEquals(1, getResult.getResidueNum());
        assertEquals(10, getResult.getOperatorNum());
        assertEquals(2, getResult2.getResidueNum());
    }

    /**
     * Method under test: {@link OperationMapstructImpl#fromGoodsOfOperation(List)}
     */
    @Test
    public void testFromGoodsOfOperation4() {
        OperationGoodsDO operationGoodsDO = new OperationGoodsDO();
        operationGoodsDO.setDepositGoodGuid("1234");
        operationGoodsDO.setDepositGuid("1234");
        operationGoodsDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO.setGoodsName("Goods Name");
        operationGoodsDO.setGuid("1234");
        operationGoodsDO.setId(1L);
        operationGoodsDO.setOperatorNum(10);
        operationGoodsDO.setResidueNum(1);
        operationGoodsDO.setSkuGuid("1234");
        operationGoodsDO.setSkuName("Sku Name");

        OperationGoodsDO operationGoodsDO2 = new OperationGoodsDO();
        operationGoodsDO2.setDepositGoodGuid("1234");
        operationGoodsDO2.setDepositGuid("1234");
        operationGoodsDO2.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO2.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO2.setGoodsName("Goods Name");
        operationGoodsDO2.setGuid("1234");
        operationGoodsDO2.setId(1L);
        operationGoodsDO2.setOperatorNum(10);
        operationGoodsDO2.setResidueNum(1);
        operationGoodsDO2.setSkuGuid("1234");
        operationGoodsDO2.setSkuName("Sku Name");

        OperationGoodsDO operationGoodsDO3 = new OperationGoodsDO();
        operationGoodsDO3.setDepositGoodGuid("1234");
        operationGoodsDO3.setDepositGuid("1234");
        operationGoodsDO3.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO3.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO3.setGoodsName("Goods Name");
        operationGoodsDO3.setGuid("1234");
        operationGoodsDO3.setId(1L);
        operationGoodsDO3.setOperatorNum(10);
        operationGoodsDO3.setResidueNum(1);
        operationGoodsDO3.setSkuGuid("1234");
        operationGoodsDO3.setSkuName("Sku Name");

        OperationGoodsDO operationGoodsDO4 = new OperationGoodsDO();
        operationGoodsDO4.setDepositGoodGuid("1234");
        operationGoodsDO4.setDepositGuid("1234");
        operationGoodsDO4.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO4.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO4.setGoodsName("Goods Name");
        operationGoodsDO4.setGuid("1234");
        operationGoodsDO4.setId(1L);
        operationGoodsDO4.setOperatorNum(10);
        operationGoodsDO4.setResidueNum(1);
        operationGoodsDO4.setSkuGuid("1234");
        operationGoodsDO4.setSkuName("Sku Name");

        OperationGoodsDO operationGoodsDO5 = new OperationGoodsDO();
        operationGoodsDO5.setDepositGoodGuid("1234");
        operationGoodsDO5.setDepositGuid("1234");
        operationGoodsDO5.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO5.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO5.setGoodsName("Goods Name");
        operationGoodsDO5.setGuid("1234");
        operationGoodsDO5.setId(1L);
        operationGoodsDO5.setOperatorNum(10);
        operationGoodsDO5.setResidueNum(1);
        operationGoodsDO5.setSkuGuid("1234");
        operationGoodsDO5.setSkuName("Sku Name");

        OperationGoodsDO operationGoodsDO6 = new OperationGoodsDO();
        operationGoodsDO6.setDepositGoodGuid("1234");
        operationGoodsDO6.setDepositGuid("1234");
        operationGoodsDO6.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO6.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO6.setGoodsName("Goods Name");
        operationGoodsDO6.setGuid("1234");
        operationGoodsDO6.setId(1L);
        operationGoodsDO6.setOperatorNum(10);
        operationGoodsDO6.setResidueNum(1);
        operationGoodsDO6.setSkuGuid("1234");
        operationGoodsDO6.setSkuName("Sku Name");

        OperationGoodsDO operationGoodsDO7 = new OperationGoodsDO();
        operationGoodsDO7.setDepositGoodGuid("1234");
        operationGoodsDO7.setDepositGuid("1234");
        operationGoodsDO7.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO7.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO7.setGoodsName("Goods Name");
        operationGoodsDO7.setGuid("1234");
        operationGoodsDO7.setId(1L);
        operationGoodsDO7.setOperatorNum(10);
        operationGoodsDO7.setResidueNum(1);
        operationGoodsDO7.setSkuGuid("1234");
        operationGoodsDO7.setSkuName("Sku Name");

        OperationGoodsDO operationGoodsDO8 = new OperationGoodsDO();
        operationGoodsDO8.setDepositGoodGuid("1234");
        operationGoodsDO8.setDepositGuid("1234");
        operationGoodsDO8.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO8.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO8.setGoodsName("Goods Name");
        operationGoodsDO8.setGuid("1234");
        operationGoodsDO8.setId(1L);
        operationGoodsDO8.setOperatorNum(10);
        operationGoodsDO8.setResidueNum(1);
        operationGoodsDO8.setSkuGuid("1234");
        operationGoodsDO8.setSkuName("Sku Name");

        OperationGoodsDO operationGoodsDO9 = new OperationGoodsDO();
        operationGoodsDO9.setDepositGoodGuid("1234");
        operationGoodsDO9.setDepositGuid("1234");
        operationGoodsDO9.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO9.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO9.setGoodsName("Goods Name");
        operationGoodsDO9.setGuid("1234");
        operationGoodsDO9.setId(1L);
        operationGoodsDO9.setOperatorNum(10);
        operationGoodsDO9.setResidueNum(1);
        operationGoodsDO9.setSkuGuid("1234");
        operationGoodsDO9.setSkuName("Sku Name");

        OperationGoodsDO operationGoodsDO10 = new OperationGoodsDO();
        operationGoodsDO10.setDepositGoodGuid("1234");
        operationGoodsDO10.setDepositGuid("1234");
        operationGoodsDO10.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO10.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO10.setGoodsName("Goods Name");
        operationGoodsDO10.setGuid("1234");
        operationGoodsDO10.setId(1L);
        operationGoodsDO10.setOperatorNum(10);
        operationGoodsDO10.setResidueNum(1);
        operationGoodsDO10.setSkuGuid("1234");
        operationGoodsDO10.setSkuName("Sku Name");

        OperationGoodsDO operationGoodsDO11 = new OperationGoodsDO();
        operationGoodsDO11.setDepositGoodGuid("1234");
        operationGoodsDO11.setDepositGuid("1234");
        operationGoodsDO11.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO11.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO11.setGoodsName("Goods Name");
        operationGoodsDO11.setGuid("1234");
        operationGoodsDO11.setId(1L);
        operationGoodsDO11.setOperatorNum(10);
        operationGoodsDO11.setResidueNum(1);
        operationGoodsDO11.setSkuGuid("1234");
        operationGoodsDO11.setSkuName("Sku Name");
        OperationGoodsDO operationGoodsDO12 = mock(OperationGoodsDO.class);
        when(operationGoodsDO12.setDepositGoodGuid(Mockito.<String>any())).thenReturn(operationGoodsDO);
        when(operationGoodsDO12.setDepositGuid(Mockito.<String>any())).thenReturn(operationGoodsDO2);
        when(operationGoodsDO12.setGmtCreate(Mockito.<LocalDateTime>any())).thenReturn(operationGoodsDO3);
        when(operationGoodsDO12.setGmtModified(Mockito.<LocalDateTime>any())).thenReturn(operationGoodsDO4);
        when(operationGoodsDO12.setGoodsName(Mockito.<String>any())).thenReturn(operationGoodsDO5);
        when(operationGoodsDO12.setGuid(Mockito.<String>any())).thenReturn(operationGoodsDO6);
        when(operationGoodsDO12.setId(Mockito.<Long>any())).thenReturn(operationGoodsDO7);
        when(operationGoodsDO12.setOperatorNum(anyInt())).thenReturn(operationGoodsDO8);
        when(operationGoodsDO12.setResidueNum(anyInt())).thenReturn(operationGoodsDO9);
        when(operationGoodsDO12.setSkuGuid(Mockito.<String>any())).thenReturn(operationGoodsDO10);
        when(operationGoodsDO12.setSkuName(Mockito.<String>any())).thenReturn(operationGoodsDO11);
        when(operationGoodsDO12.getOperatorNum()).thenReturn(10);
        when(operationGoodsDO12.getResidueNum()).thenReturn(1);
        when(operationGoodsDO12.getGoodsName()).thenReturn("Goods Name");
        when(operationGoodsDO12.getSkuName()).thenReturn("Sku Name");
        operationGoodsDO12.setDepositGoodGuid("1234");
        operationGoodsDO12.setDepositGuid("1234");
        operationGoodsDO12.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO12.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO12.setGoodsName("Goods Name");
        operationGoodsDO12.setGuid("1234");
        operationGoodsDO12.setId(1L);
        operationGoodsDO12.setOperatorNum(10);
        operationGoodsDO12.setResidueNum(1);
        operationGoodsDO12.setSkuGuid("1234");
        operationGoodsDO12.setSkuName("Sku Name");

        ArrayList<OperationGoodsDO> list = new ArrayList<>();
        list.add(operationGoodsDO12);
        List<GoodsSimpleRespDTO> actualFromGoodsOfOperationResult = operationMapstructImpl.fromGoodsOfOperation(list);
        verify(operationGoodsDO12).getGoodsName();
        verify(operationGoodsDO12).getOperatorNum();
        verify(operationGoodsDO12).getResidueNum();
        verify(operationGoodsDO12).getSkuName();
        verify(operationGoodsDO12).setDepositGoodGuid(Mockito.<String>any());
        verify(operationGoodsDO12).setDepositGuid(Mockito.<String>any());
        verify(operationGoodsDO12).setGmtCreate(Mockito.<LocalDateTime>any());
        verify(operationGoodsDO12).setGmtModified(Mockito.<LocalDateTime>any());
        verify(operationGoodsDO12).setGoodsName(Mockito.<String>any());
        verify(operationGoodsDO12).setGuid(Mockito.<String>any());
        verify(operationGoodsDO12).setId(Mockito.<Long>any());
        verify(operationGoodsDO12).setOperatorNum(anyInt());
        verify(operationGoodsDO12).setResidueNum(anyInt());
        verify(operationGoodsDO12).setSkuGuid(Mockito.<String>any());
        verify(operationGoodsDO12).setSkuName(Mockito.<String>any());
        assertEquals(1, actualFromGoodsOfOperationResult.size());
        GoodsSimpleRespDTO getResult = actualFromGoodsOfOperationResult.get(0);
        assertEquals("Goods Name", getResult.getGoodsName());
        assertEquals("Sku Name", getResult.getSkuName());
        assertEquals(1, getResult.getResidueNum());
        assertEquals(10, getResult.getOperatorNum());
    }

    /**
     * Method under test:
     * {@link OperationMapstructImpl#operationGoodsDOToGoodsSimpleRespDTO(OperationGoodsDO)}
     */
    @Test
    public void testOperationGoodsDOToGoodsSimpleRespDTO() {
        OperationGoodsDO operationGoodsDO = new OperationGoodsDO();
        operationGoodsDO.setDepositGoodGuid("1234");
        operationGoodsDO.setDepositGuid("1234");
        operationGoodsDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO.setGoodsName("Goods Name");
        operationGoodsDO.setGuid("1234");
        operationGoodsDO.setId(1L);
        operationGoodsDO.setOperatorNum(10);
        operationGoodsDO.setResidueNum(1);
        operationGoodsDO.setSkuGuid("1234");
        operationGoodsDO.setSkuName("Sku Name");
        GoodsSimpleRespDTO actualOperationGoodsDOToGoodsSimpleRespDTOResult = operationMapstructImpl
                .operationGoodsDOToGoodsSimpleRespDTO(operationGoodsDO);
        assertEquals("Goods Name", actualOperationGoodsDOToGoodsSimpleRespDTOResult.getGoodsName());
        assertEquals("Sku Name", actualOperationGoodsDOToGoodsSimpleRespDTOResult.getSkuName());
        assertEquals(1, actualOperationGoodsDOToGoodsSimpleRespDTOResult.getResidueNum());
        assertEquals(10, actualOperationGoodsDOToGoodsSimpleRespDTOResult.getOperatorNum());
    }

    /**
     * Method under test:
     * {@link OperationMapstructImpl#operationGoodsDOToGoodsSimpleRespDTO(OperationGoodsDO)}
     */
    @Test
    public void testOperationGoodsDOToGoodsSimpleRespDTO2() {
        OperationGoodsDO operationGoodsDO = new OperationGoodsDO();
        operationGoodsDO.setDepositGoodGuid("1234");
        operationGoodsDO.setDepositGuid("1234");
        operationGoodsDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO.setGoodsName("Goods Name");
        operationGoodsDO.setGuid("1234");
        operationGoodsDO.setId(1L);
        operationGoodsDO.setOperatorNum(10);
        operationGoodsDO.setResidueNum(1);
        operationGoodsDO.setSkuGuid("1234");
        operationGoodsDO.setSkuName("Sku Name");

        OperationGoodsDO operationGoodsDO2 = new OperationGoodsDO();
        operationGoodsDO2.setDepositGoodGuid("1234");
        operationGoodsDO2.setDepositGuid("1234");
        operationGoodsDO2.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO2.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO2.setGoodsName("Goods Name");
        operationGoodsDO2.setGuid("1234");
        operationGoodsDO2.setId(1L);
        operationGoodsDO2.setOperatorNum(10);
        operationGoodsDO2.setResidueNum(1);
        operationGoodsDO2.setSkuGuid("1234");
        operationGoodsDO2.setSkuName("Sku Name");

        OperationGoodsDO operationGoodsDO3 = new OperationGoodsDO();
        operationGoodsDO3.setDepositGoodGuid("1234");
        operationGoodsDO3.setDepositGuid("1234");
        operationGoodsDO3.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO3.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO3.setGoodsName("Goods Name");
        operationGoodsDO3.setGuid("1234");
        operationGoodsDO3.setId(1L);
        operationGoodsDO3.setOperatorNum(10);
        operationGoodsDO3.setResidueNum(1);
        operationGoodsDO3.setSkuGuid("1234");
        operationGoodsDO3.setSkuName("Sku Name");

        OperationGoodsDO operationGoodsDO4 = new OperationGoodsDO();
        operationGoodsDO4.setDepositGoodGuid("1234");
        operationGoodsDO4.setDepositGuid("1234");
        operationGoodsDO4.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO4.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO4.setGoodsName("Goods Name");
        operationGoodsDO4.setGuid("1234");
        operationGoodsDO4.setId(1L);
        operationGoodsDO4.setOperatorNum(10);
        operationGoodsDO4.setResidueNum(1);
        operationGoodsDO4.setSkuGuid("1234");
        operationGoodsDO4.setSkuName("Sku Name");

        OperationGoodsDO operationGoodsDO5 = new OperationGoodsDO();
        operationGoodsDO5.setDepositGoodGuid("1234");
        operationGoodsDO5.setDepositGuid("1234");
        operationGoodsDO5.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO5.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO5.setGoodsName("Goods Name");
        operationGoodsDO5.setGuid("1234");
        operationGoodsDO5.setId(1L);
        operationGoodsDO5.setOperatorNum(10);
        operationGoodsDO5.setResidueNum(1);
        operationGoodsDO5.setSkuGuid("1234");
        operationGoodsDO5.setSkuName("Sku Name");

        OperationGoodsDO operationGoodsDO6 = new OperationGoodsDO();
        operationGoodsDO6.setDepositGoodGuid("1234");
        operationGoodsDO6.setDepositGuid("1234");
        operationGoodsDO6.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO6.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO6.setGoodsName("Goods Name");
        operationGoodsDO6.setGuid("1234");
        operationGoodsDO6.setId(1L);
        operationGoodsDO6.setOperatorNum(10);
        operationGoodsDO6.setResidueNum(1);
        operationGoodsDO6.setSkuGuid("1234");
        operationGoodsDO6.setSkuName("Sku Name");

        OperationGoodsDO operationGoodsDO7 = new OperationGoodsDO();
        operationGoodsDO7.setDepositGoodGuid("1234");
        operationGoodsDO7.setDepositGuid("1234");
        operationGoodsDO7.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO7.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO7.setGoodsName("Goods Name");
        operationGoodsDO7.setGuid("1234");
        operationGoodsDO7.setId(1L);
        operationGoodsDO7.setOperatorNum(10);
        operationGoodsDO7.setResidueNum(1);
        operationGoodsDO7.setSkuGuid("1234");
        operationGoodsDO7.setSkuName("Sku Name");

        OperationGoodsDO operationGoodsDO8 = new OperationGoodsDO();
        operationGoodsDO8.setDepositGoodGuid("1234");
        operationGoodsDO8.setDepositGuid("1234");
        operationGoodsDO8.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO8.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO8.setGoodsName("Goods Name");
        operationGoodsDO8.setGuid("1234");
        operationGoodsDO8.setId(1L);
        operationGoodsDO8.setOperatorNum(10);
        operationGoodsDO8.setResidueNum(1);
        operationGoodsDO8.setSkuGuid("1234");
        operationGoodsDO8.setSkuName("Sku Name");

        OperationGoodsDO operationGoodsDO9 = new OperationGoodsDO();
        operationGoodsDO9.setDepositGoodGuid("1234");
        operationGoodsDO9.setDepositGuid("1234");
        operationGoodsDO9.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO9.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO9.setGoodsName("Goods Name");
        operationGoodsDO9.setGuid("1234");
        operationGoodsDO9.setId(1L);
        operationGoodsDO9.setOperatorNum(10);
        operationGoodsDO9.setResidueNum(1);
        operationGoodsDO9.setSkuGuid("1234");
        operationGoodsDO9.setSkuName("Sku Name");

        OperationGoodsDO operationGoodsDO10 = new OperationGoodsDO();
        operationGoodsDO10.setDepositGoodGuid("1234");
        operationGoodsDO10.setDepositGuid("1234");
        operationGoodsDO10.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO10.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO10.setGoodsName("Goods Name");
        operationGoodsDO10.setGuid("1234");
        operationGoodsDO10.setId(1L);
        operationGoodsDO10.setOperatorNum(10);
        operationGoodsDO10.setResidueNum(1);
        operationGoodsDO10.setSkuGuid("1234");
        operationGoodsDO10.setSkuName("Sku Name");

        OperationGoodsDO operationGoodsDO11 = new OperationGoodsDO();
        operationGoodsDO11.setDepositGoodGuid("1234");
        operationGoodsDO11.setDepositGuid("1234");
        operationGoodsDO11.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO11.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO11.setGoodsName("Goods Name");
        operationGoodsDO11.setGuid("1234");
        operationGoodsDO11.setId(1L);
        operationGoodsDO11.setOperatorNum(10);
        operationGoodsDO11.setResidueNum(1);
        operationGoodsDO11.setSkuGuid("1234");
        operationGoodsDO11.setSkuName("Sku Name");
        OperationGoodsDO operationGoodsDO12 = mock(OperationGoodsDO.class);
        when(operationGoodsDO12.setDepositGoodGuid(Mockito.<String>any())).thenReturn(operationGoodsDO);
        when(operationGoodsDO12.setDepositGuid(Mockito.<String>any())).thenReturn(operationGoodsDO2);
        when(operationGoodsDO12.setGmtCreate(Mockito.<LocalDateTime>any())).thenReturn(operationGoodsDO3);
        when(operationGoodsDO12.setGmtModified(Mockito.<LocalDateTime>any())).thenReturn(operationGoodsDO4);
        when(operationGoodsDO12.setGoodsName(Mockito.<String>any())).thenReturn(operationGoodsDO5);
        when(operationGoodsDO12.setGuid(Mockito.<String>any())).thenReturn(operationGoodsDO6);
        when(operationGoodsDO12.setId(Mockito.<Long>any())).thenReturn(operationGoodsDO7);
        when(operationGoodsDO12.setOperatorNum(anyInt())).thenReturn(operationGoodsDO8);
        when(operationGoodsDO12.setResidueNum(anyInt())).thenReturn(operationGoodsDO9);
        when(operationGoodsDO12.setSkuGuid(Mockito.<String>any())).thenReturn(operationGoodsDO10);
        when(operationGoodsDO12.setSkuName(Mockito.<String>any())).thenReturn(operationGoodsDO11);
        when(operationGoodsDO12.getOperatorNum()).thenReturn(10);
        when(operationGoodsDO12.getResidueNum()).thenReturn(1);
        when(operationGoodsDO12.getGoodsName()).thenReturn("Goods Name");
        when(operationGoodsDO12.getSkuName()).thenReturn("Sku Name");
        operationGoodsDO12.setDepositGoodGuid("1234");
        operationGoodsDO12.setDepositGuid("1234");
        operationGoodsDO12.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO12.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationGoodsDO12.setGoodsName("Goods Name");
        operationGoodsDO12.setGuid("1234");
        operationGoodsDO12.setId(1L);
        operationGoodsDO12.setOperatorNum(10);
        operationGoodsDO12.setResidueNum(1);
        operationGoodsDO12.setSkuGuid("1234");
        operationGoodsDO12.setSkuName("Sku Name");
        GoodsSimpleRespDTO actualOperationGoodsDOToGoodsSimpleRespDTOResult = operationMapstructImpl
                .operationGoodsDOToGoodsSimpleRespDTO(operationGoodsDO12);
        verify(operationGoodsDO12).getGoodsName();
        verify(operationGoodsDO12).getOperatorNum();
        verify(operationGoodsDO12).getResidueNum();
        verify(operationGoodsDO12).getSkuName();
        verify(operationGoodsDO12).setDepositGoodGuid(Mockito.<String>any());
        verify(operationGoodsDO12).setDepositGuid(Mockito.<String>any());
        verify(operationGoodsDO12).setGmtCreate(Mockito.<LocalDateTime>any());
        verify(operationGoodsDO12).setGmtModified(Mockito.<LocalDateTime>any());
        verify(operationGoodsDO12).setGoodsName(Mockito.<String>any());
        verify(operationGoodsDO12).setGuid(Mockito.<String>any());
        verify(operationGoodsDO12).setId(Mockito.<Long>any());
        verify(operationGoodsDO12).setOperatorNum(anyInt());
        verify(operationGoodsDO12).setResidueNum(anyInt());
        verify(operationGoodsDO12).setSkuGuid(Mockito.<String>any());
        verify(operationGoodsDO12).setSkuName(Mockito.<String>any());
        assertEquals("Goods Name", actualOperationGoodsDOToGoodsSimpleRespDTOResult.getGoodsName());
        assertEquals("Sku Name", actualOperationGoodsDOToGoodsSimpleRespDTOResult.getSkuName());
        assertEquals(1, actualOperationGoodsDOToGoodsSimpleRespDTOResult.getResidueNum());
        assertEquals(10, actualOperationGoodsDOToGoodsSimpleRespDTOResult.getOperatorNum());
    }
}
