package com.holderzone.holder.saas.store.deposit.util;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GenerateDepositOrderIDTest {

    @Mock
    private StringRedisTemplate mockRedisTemplate;

    private GenerateDepositOrderID generateDepositOrderIDUnderTest;

    @Before
    public void setUp() {
        generateDepositOrderIDUnderTest = new GenerateDepositOrderID(mockRedisTemplate);
    }

    @Test
    public void testGenerateDepositID() {
        // Setup
        when(mockRedisTemplate.getConnectionFactory()).thenReturn(null);

        // Run the test
        final String result = generateDepositOrderIDUnderTest.generateDepositID("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testGenerateDepositID_StringRedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.getConnectionFactory()).thenReturn(null);

        // Run the test
        final String result = generateDepositOrderIDUnderTest.generateDepositID("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }
}
