package com.holderzone.holder.saas.store.report.controller;

import com.holderzone.holder.saas.store.report.service.TradeOrderService;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.OrderItemTypeRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(TradeOrderController.class)
public class TradeOrderControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private TradeOrderService mockTradeOrderService;

    @Test
    public void testQueryItemTypes() throws Exception {
        // Setup
        // Configure TradeOrderService.queryItemTypes(...).
        final OrderItemTypeRespDTO orderItemTypeRespDTO = new OrderItemTypeRespDTO();
        orderItemTypeRespDTO.setItemType("itemType");
        orderItemTypeRespDTO.setItemName("itemName");
        final List<OrderItemTypeRespDTO> orderItemTypeRespDTOS = Arrays.asList(orderItemTypeRespDTO);
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeOrderService.queryItemTypes(query)).thenReturn(orderItemTypeRespDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/trade/order/item/types")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryItemTypes_TradeOrderServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure TradeOrderService.queryItemTypes(...).
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeOrderService.queryItemTypes(query)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/trade/order/item/types")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testQueryItemCategories() throws Exception {
        // Setup
        // Configure TradeOrderService.queryItemCategories(...).
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeOrderService.queryItemCategories(query)).thenReturn(Arrays.asList("value"));

        // Run the test and verify the results
        mockMvc.perform(post("/trade/order/item/categories")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryItemCategories_TradeOrderServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure TradeOrderService.queryItemCategories(...).
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeOrderService.queryItemCategories(query)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/trade/order/item/categories")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }
}
