package com.holderzone.holder.saas.store.table;

import com.holderzone.saas.store.dto.common.BaseDTO;

/**
 * {@link BaseSet}
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/20 11:25
 */
public class BaseSet {

    public static final String enterpriseGuid = "6506431195651982337";

    public static final String storeGuid = "6506433495173169154";

    public static final String storeName = "Decy的java单元测试";

    public static final String userGuid = "6506433495173169155";

    public static final String deviceId = "6506433495173169156";

    public static void makeDTO(BaseDTO baseDTO){
        baseDTO.setStoreGuid(storeGuid);
        baseDTO.setStoreName(storeName);
        baseDTO.setAccount("decy");
        baseDTO.setEnterpriseGuid(enterpriseGuid);
        baseDTO.setUserGuid(userGuid);
        baseDTO.setUserName("decy-name");
        baseDTO.setDeviceType(3);
        baseDTO.setDeviceId(deviceId);
    }

}