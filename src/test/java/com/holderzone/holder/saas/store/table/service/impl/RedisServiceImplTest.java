package com.holderzone.holder.saas.store.table.service.impl;

import com.holderzone.holder.saas.store.table.config.DelayReleaseLockConfig;
import com.holderzone.holder.saas.store.table.domain.TableDO;
import com.holderzone.holder.saas.store.table.domain.bo.TableInfoBO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RedisServiceImplTest {

    @Mock
    private RedisTemplate mockRedisTemplate;
    @Mock
    private DelayReleaseLockConfig mockReleaseLockConfig;

    private RedisServiceImpl redisServiceImplUnderTest;

    @Before
    public void setUp() {
        redisServiceImplUnderTest = new RedisServiceImpl(mockRedisTemplate, mockReleaseLockConfig);
    }

    @Test
    public void testSingleGuid() {
        assertThat(redisServiceImplUnderTest.singleGuid("tableName")).isEqualTo("result");
    }

    @Test
    public void testBatchGuid() {
        assertThat(redisServiceImplUnderTest.batchGuid(0, "tableName")).isEqualTo(Arrays.asList("value"));
        assertThat(redisServiceImplUnderTest.batchGuid(0, "tableName")).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testLockSingleTable() {
        // Setup
        when(mockReleaseLockConfig.getWxTime()).thenReturn(0L);
        when(mockReleaseLockConfig.getTime()).thenReturn(0L);
        when(mockRedisTemplate.execute(any(DefaultRedisScript.class), eq(Arrays.asList("value")),
                eq(Arrays.asList("value")))).thenReturn("result");

        // Run the test
        redisServiceImplUnderTest.lockSingleTable("tableGuid", "deviceId", "orderGuid", 0);

        // Verify the results
    }

    @Test
    public void testLockSingleTable_RedisTemplateReturnsNull() {
        // Setup
        when(mockReleaseLockConfig.getWxTime()).thenReturn(0L);
        when(mockRedisTemplate.execute(any(DefaultRedisScript.class), eq(Arrays.asList("value")),
                eq(Arrays.asList("value")))).thenReturn(null);

        // Run the test
        redisServiceImplUnderTest.lockSingleTable("tableGuid", "deviceId", "orderGuid", 0);

        // Verify the results
    }

    @Test
    public void testLockMultiTable() {
        // Setup
        final List<TableInfoBO> tableInfoBOS = Arrays.asList(new TableInfoBO("tableGuid", "deviceId", "orderGuid"));
        when(mockReleaseLockConfig.getWxTime()).thenReturn(0L);
        when(mockReleaseLockConfig.getTime()).thenReturn(0L);
        when(mockRedisTemplate.execute(any(DefaultRedisScript.class), eq(Arrays.asList("value")),
                eq(Arrays.asList("value")))).thenReturn("result");

        // Run the test
        redisServiceImplUnderTest.lockMultiTable(tableInfoBOS, 0);

        // Verify the results
    }

    @Test
    public void testLockMultiTable_RedisTemplateReturnsNull() {
        // Setup
        final List<TableInfoBO> tableInfoBOS = Arrays.asList(new TableInfoBO("tableGuid", "deviceId", "orderGuid"));
        when(mockReleaseLockConfig.getWxTime()).thenReturn(0L);
        when(mockRedisTemplate.execute(any(DefaultRedisScript.class), eq(Arrays.asList("value")),
                eq(Arrays.asList("value")))).thenReturn(null);

        // Run the test
        redisServiceImplUnderTest.lockMultiTable(tableInfoBOS, 0);

        // Verify the results
    }

    @Test
    public void testIsTableLockedByOthers1() {
        // Setup
        when(mockRedisTemplate.execute(any(DefaultRedisScript.class), eq(Arrays.asList("value")),
                eq("tableGuid"))).thenReturn("result");

        // Run the test
        final boolean result = redisServiceImplUnderTest.isTableLockedByOthers("deviceId", "tableGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testIsTableLockedByOthers1_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.execute(any(DefaultRedisScript.class), eq(Arrays.asList("value")),
                eq("tableGuid"))).thenReturn(null);

        // Run the test
        final boolean result = redisServiceImplUnderTest.isTableLockedByOthers("deviceId", "tableGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testIsTableLockedByOthers2() {
        // Setup
        when(mockRedisTemplate.execute(any(DefaultRedisScript.class), eq(Arrays.asList("value")),
                eq("tableGuid"))).thenReturn("result");

        // Run the test
        final boolean result = redisServiceImplUnderTest.isTableLockedByOthers("deviceId", Arrays.asList("value"));

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testIsTableLockedByOthers2_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.execute(any(DefaultRedisScript.class), eq(Arrays.asList("value")),
                eq("tableGuid"))).thenReturn(null);

        // Run the test
        final boolean result = redisServiceImplUnderTest.isTableLockedByOthers("deviceId", Arrays.asList("value"));

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testIsUnlockAllowed() {
        // Setup
        when(mockRedisTemplate.execute(any(DefaultRedisScript.class), eq(Arrays.asList("value")),
                eq("tableGuid"))).thenReturn("result");

        // Run the test
        final boolean result = redisServiceImplUnderTest.isUnlockAllowed("tableGuid", "deviceId", "orderGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testIsUnlockAllowed_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.execute(any(DefaultRedisScript.class), eq(Arrays.asList("value")),
                eq("tableGuid"))).thenReturn(null);

        // Run the test
        final boolean result = redisServiceImplUnderTest.isUnlockAllowed("tableGuid", "deviceId", "orderGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testUnlockMultiTable() {
        // Setup
        // Run the test
        redisServiceImplUnderTest.unlockMultiTable(Arrays.asList("value"));

        // Verify the results
        verify(mockRedisTemplate).delete(Arrays.asList("value"));
    }

    @Test
    public void testGetCombineTimes() {
        // Setup
        when(mockRedisTemplate.execute(any(DefaultRedisScript.class), eq(Arrays.asList("value")), eq(0L)))
                .thenReturn("result");

        // Run the test
        final Integer result = redisServiceImplUnderTest.getCombineTimes("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testGetCombineTimes_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.execute(any(DefaultRedisScript.class), eq(Arrays.asList("value")), eq(0L)))
                .thenReturn(null);

        // Run the test
        final Integer result = redisServiceImplUnderTest.getCombineTimes("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testPutTableDoList() {
        // Setup
        final TableDO tableDO = new TableDO();
        tableDO.setAreaGuid("areaGuid");
        tableDO.setAreaName("areaName");
        tableDO.setTableCode("tableCode");
        tableDO.setSeats(0);
        tableDO.setSort(0);
        final List<TableDO> tableDOList = Arrays.asList(tableDO);
        when(mockRedisTemplate.opsForList()).thenReturn(null);

        // Run the test
        redisServiceImplUnderTest.putTableDoList(tableDOList, "storeGuid");

        // Verify the results
    }

    @Test
    public void testGetTableLockStatus1() {
        // Setup
        final TableInfoBO expectedResult = new TableInfoBO("tableGuid", "deviceId", "orderGuid");
        when(mockRedisTemplate.execute(any(DefaultRedisScript.class), eq(Arrays.asList("value")),
                eq("tableGuid"))).thenReturn("result");

        // Run the test
        final TableInfoBO result = redisServiceImplUnderTest.getTableLockStatus("tableGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetTableLockStatus1_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.execute(any(DefaultRedisScript.class), eq(Arrays.asList("value")),
                eq("tableGuid"))).thenReturn(null);

        // Run the test
        final TableInfoBO result = redisServiceImplUnderTest.getTableLockStatus("tableGuid");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testGetTableLockStatus2() {
        // Setup
        final List<TableInfoBO> expectedResult = Arrays.asList(new TableInfoBO("tableGuid", "deviceId", "orderGuid"));
        when(mockRedisTemplate.execute(any(DefaultRedisScript.class), eq(Arrays.asList("value")),
                eq("tableGuid"))).thenReturn("result");

        // Run the test
        final List<TableInfoBO> result = redisServiceImplUnderTest.getTableLockStatus(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetTableLockStatus2_RedisTemplateReturnsNull() {
        // Setup
        when(mockRedisTemplate.execute(any(DefaultRedisScript.class), eq(Arrays.asList("value")),
                eq("tableGuid"))).thenReturn(null);

        // Run the test
        final List<TableInfoBO> result = redisServiceImplUnderTest.getTableLockStatus(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
