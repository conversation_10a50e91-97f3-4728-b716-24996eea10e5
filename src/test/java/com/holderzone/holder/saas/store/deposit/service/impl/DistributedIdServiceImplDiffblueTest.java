package com.holderzone.holder.saas.store.deposit.service.impl;

import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@ContextConfiguration(classes = {DistributedIdServiceImpl.class})
@RunWith(SpringJUnit4ClassRunner.class)
public class DistributedIdServiceImplDiffblueTest {
    @Autowired
    private DistributedIdServiceImpl distributedIdServiceImpl;

    /**
     * Method under test: {@link DistributedIdServiceImpl#rawId(String)}
     */
    @Test
    @Ignore("TODO: Complete this test")
    public void testRawId() {
        // TODO: Complete this test.
        //   Diffblue AI was unable to find a test

        // Arrange
        // TODO: Populate arranged inputs
        String tag = "";

        // Act
        Long actualRawIdResult = this.distributedIdServiceImpl.rawId(tag);

        // Assert
        // TODO: Add assertions on result
    }

    /**
     * Method under test: {@link DistributedIdServiceImpl#nextId(String)}
     */
    @Test
    @Ignore("TODO: Complete this test")
    public void testNextId() {
        // TODO: Complete this test.
        //   Diffblue AI was unable to find a test

        // Arrange
        // TODO: Populate arranged inputs
        String tag = "";

        // Act
        String actualNextIdResult = this.distributedIdServiceImpl.nextId(tag);

        // Assert
        // TODO: Add assertions on result
    }

    /**
     * Method under test: {@link DistributedIdServiceImpl#nextDepositItemGuid()}
     */
    @Test
    @Ignore("TODO: Complete this test")
    public void testNextDepositItemGuid() {
        // TODO: Complete this test.
        //   Diffblue AI was unable to find a test

        // Arrange and Act
        // TODO: Populate arranged inputs
        String actualNextDepositItemGuidResult = this.distributedIdServiceImpl.nextDepositItemGuid();

        // Assert
        // TODO: Add assertions on result
    }

    /**
     * Method under test: {@link DistributedIdServiceImpl#nextGoodsGuid()}
     */
    @Test
    @Ignore("TODO: Complete this test")
    public void testNextGoodsGuid() {
        // TODO: Complete this test.
        //   Diffblue AI was unable to find a test

        // Arrange and Act
        // TODO: Populate arranged inputs
        String actualNextGoodsGuidResult = this.distributedIdServiceImpl.nextGoodsGuid();

        // Assert
        // TODO: Add assertions on result
    }

    /**
     * Method under test: {@link DistributedIdServiceImpl#nextOperationGuid()}
     */
    @Test
    @Ignore("TODO: Complete this test")
    public void testNextOperationGuid() {
        // TODO: Complete this test.
        //   Diffblue AI was unable to find a test

        // Arrange and Act
        // TODO: Populate arranged inputs
        String actualNextOperationGuidResult = this.distributedIdServiceImpl.nextOperationGuid();

        // Assert
        // TODO: Add assertions on result
    }

    /**
     * Method under test: {@link DistributedIdServiceImpl#nextOperationGoodsGuid()}
     */
    @Test
    @Ignore("TODO: Complete this test")
    public void testNextOperationGoodsGuid() {
        // TODO: Complete this test.
        //   Diffblue AI was unable to find a test

        // Arrange and Act
        // TODO: Populate arranged inputs
        String actualNextOperationGoodsGuidResult = this.distributedIdServiceImpl.nextOperationGoodsGuid();

        // Assert
        // TODO: Add assertions on result
    }

    /**
     * Method under test: {@link DistributedIdServiceImpl#nextRemindGuid()}
     */
    @Test
    @Ignore("TODO: Complete this test")
    public void testNextRemindGuid() {
        // TODO: Complete this test.
        //   Diffblue AI was unable to find a test

        // Arrange and Act
        // TODO: Populate arranged inputs
        String actualNextRemindGuidResult = this.distributedIdServiceImpl.nextRemindGuid();

        // Assert
        // TODO: Add assertions on result
    }
}
