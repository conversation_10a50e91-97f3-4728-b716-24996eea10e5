package com.holderzone.holder.saas.store.report.service.impl;

import com.google.common.util.concurrent.MoreExecutors;
import com.holderzone.holder.saas.store.report.mapper.TradeDetailMapper;
import com.holderzone.holder.saas.store.report.mapper.TradeItemMapper;
import com.holderzone.holder.saas.store.report.mapper.TradeOrderMapper;
import com.holderzone.saas.store.dto.report.openapi.*;
import com.holderzone.saas.store.dto.report.openapi.SaleDetailRespDTO;
import com.holderzone.saas.store.dto.report.openapi.SalePayDetailRespDTO;
import com.holderzone.saas.store.dto.report.openapi.SaleProductDetailRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OpenSaleDetailServiceImplTest {

    @Mock
    private TradeDetailMapper mockTradeDetailMapper;
    @Mock
    private TradeOrderMapper mockTradeOrderMapper;
    @Mock
    private TradeItemMapper mockTradeItemMapper;

    private OpenSaleDetailServiceImpl openSaleDetailServiceImplUnderTest;

    @Before
    public void setUp() {
        openSaleDetailServiceImplUnderTest = new OpenSaleDetailServiceImpl(mockTradeDetailMapper, mockTradeOrderMapper,
                mockTradeItemMapper, MoreExecutors.directExecutor());
    }

    @Test
    public void testQuery() {
        // Setup
        final SaleDetailQueryDTO saleDetailQueryDTO = new SaleDetailQueryDTO();
        saleDetailQueryDTO.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        saleDetailQueryDTO.setCursor(0L);
        saleDetailQueryDTO.setLimit(0);
        saleDetailQueryDTO.setEnterpriseGuid("enterpriseGuid");
        saleDetailQueryDTO.setStoreGuid("storeGuid");

        final SaleDetailLimitRespDTO expectedResult = new SaleDetailLimitRespDTO();
        final SaleDetailRespDTO saleDetailRespDTO = new SaleDetailRespDTO();
        saleDetailRespDTO.setOrderGuid("orderGuid");
        saleDetailRespDTO.setStoreGuId("storeGuId");
        saleDetailRespDTO.setStoreName("storeName");
        saleDetailRespDTO.setPosNO("posNO");
        expectedResult.setList(Arrays.asList(saleDetailRespDTO));

        // Configure TradeOrderMapper.querySaleDetail(...).
        final SaleDetailRespDTO saleDetailRespDTO1 = new SaleDetailRespDTO();
        saleDetailRespDTO1.setOrderGuid("orderGuid");
        saleDetailRespDTO1.setSaleCount(0);
        saleDetailRespDTO1.setReceiptType(0);
        saleDetailRespDTO1.setRemark("");
        final SaleProductDetailRespDTO saleProductDetailRespDTO = new SaleProductDetailRespDTO();
        saleProductDetailRespDTO.setIndex(0);
        saleProductDetailRespDTO.setOrderGuid("orderGuid");
        saleProductDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        saleProductDetailRespDTO.setPrice(0L);
        saleProductDetailRespDTO.setTotalPrice(0L);
        saleProductDetailRespDTO.setDiscountFee(0L);
        saleProductDetailRespDTO.setActuallyPayFee(0L);
        saleDetailRespDTO1.setProductDetails(Arrays.asList(saleProductDetailRespDTO));
        final SalePayDetailRespDTO salePayDetailRespDTO = new SalePayDetailRespDTO();
        salePayDetailRespDTO.setIndex(0);
        salePayDetailRespDTO.setOrderGuid("orderGuid");
        salePayDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        salePayDetailRespDTO.setPaymentType(0);
        salePayDetailRespDTO.setPayWayCode("x1001");
        saleDetailRespDTO1.setPayDetails(Arrays.asList(salePayDetailRespDTO));
        final List<SaleDetailRespDTO> saleDetailRespDTOS = Arrays.asList(saleDetailRespDTO1);
        final SaleDetailQueryDTO query = new SaleDetailQueryDTO();
        query.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        query.setCursor(0L);
        query.setLimit(0);
        query.setEnterpriseGuid("enterpriseGuid");
        query.setStoreGuid("storeGuid");
        when(mockTradeOrderMapper.querySaleDetail(query)).thenReturn(saleDetailRespDTOS);

        // Configure TradeItemMapper.querySaleProductDetail(...).
        final SaleProductDetailRespDTO saleProductDetailRespDTO1 = new SaleProductDetailRespDTO();
        saleProductDetailRespDTO1.setIndex(0);
        saleProductDetailRespDTO1.setOrderGuid("orderGuid");
        saleProductDetailRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        saleProductDetailRespDTO1.setPrice(0L);
        saleProductDetailRespDTO1.setTotalPrice(0L);
        saleProductDetailRespDTO1.setDiscountFee(0L);
        saleProductDetailRespDTO1.setActuallyPayFee(0L);
        final List<SaleProductDetailRespDTO> saleProductDetailRespDTOS = Arrays.asList(saleProductDetailRespDTO1);
        when(mockTradeItemMapper.querySaleProductDetail("enterpriseGuid", Arrays.asList(0L)))
                .thenReturn(saleProductDetailRespDTOS);

        // Configure TradeDetailMapper.querySalePayDetail(...).
        final SalePayDetailRespDTO salePayDetailRespDTO1 = new SalePayDetailRespDTO();
        salePayDetailRespDTO1.setIndex(0);
        salePayDetailRespDTO1.setOrderGuid("orderGuid");
        salePayDetailRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        salePayDetailRespDTO1.setPaymentType(0);
        salePayDetailRespDTO1.setPayWayCode("x1001");
        final List<SalePayDetailRespDTO> salePayDetailRespDTOS = Arrays.asList(salePayDetailRespDTO1);
        when(mockTradeDetailMapper.querySalePayDetail("enterpriseGuid", Arrays.asList(0L)))
                .thenReturn(salePayDetailRespDTOS);

        // Run the test
        final SaleDetailLimitRespDTO result = openSaleDetailServiceImplUnderTest.query(saleDetailQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuery_TradeOrderMapperReturnsNoItems() {
        // Setup
        final SaleDetailQueryDTO saleDetailQueryDTO = new SaleDetailQueryDTO();
        saleDetailQueryDTO.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        saleDetailQueryDTO.setCursor(0L);
        saleDetailQueryDTO.setLimit(0);
        saleDetailQueryDTO.setEnterpriseGuid("enterpriseGuid");
        saleDetailQueryDTO.setStoreGuid("storeGuid");

        final SaleDetailLimitRespDTO expectedResult = new SaleDetailLimitRespDTO();
        final SaleDetailRespDTO saleDetailRespDTO = new SaleDetailRespDTO();
        saleDetailRespDTO.setOrderGuid("orderGuid");
        saleDetailRespDTO.setStoreGuId("storeGuId");
        saleDetailRespDTO.setStoreName("storeName");
        saleDetailRespDTO.setPosNO("posNO");
        expectedResult.setList(Arrays.asList(saleDetailRespDTO));

        // Configure TradeOrderMapper.querySaleDetail(...).
        final SaleDetailQueryDTO query = new SaleDetailQueryDTO();
        query.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        query.setCursor(0L);
        query.setLimit(0);
        query.setEnterpriseGuid("enterpriseGuid");
        query.setStoreGuid("storeGuid");
        when(mockTradeOrderMapper.querySaleDetail(query)).thenReturn(Collections.emptyList());

        // Run the test
        final SaleDetailLimitRespDTO result = openSaleDetailServiceImplUnderTest.query(saleDetailQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuery_TradeItemMapperReturnsNoItems() {
        // Setup
        final SaleDetailQueryDTO saleDetailQueryDTO = new SaleDetailQueryDTO();
        saleDetailQueryDTO.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        saleDetailQueryDTO.setCursor(0L);
        saleDetailQueryDTO.setLimit(0);
        saleDetailQueryDTO.setEnterpriseGuid("enterpriseGuid");
        saleDetailQueryDTO.setStoreGuid("storeGuid");

        final SaleDetailLimitRespDTO expectedResult = new SaleDetailLimitRespDTO();
        final SaleDetailRespDTO saleDetailRespDTO = new SaleDetailRespDTO();
        saleDetailRespDTO.setOrderGuid("orderGuid");
        saleDetailRespDTO.setStoreGuId("storeGuId");
        saleDetailRespDTO.setStoreName("storeName");
        saleDetailRespDTO.setPosNO("posNO");
        expectedResult.setList(Arrays.asList(saleDetailRespDTO));

        // Configure TradeOrderMapper.querySaleDetail(...).
        final SaleDetailRespDTO saleDetailRespDTO1 = new SaleDetailRespDTO();
        saleDetailRespDTO1.setOrderGuid("orderGuid");
        saleDetailRespDTO1.setSaleCount(0);
        saleDetailRespDTO1.setReceiptType(0);
        saleDetailRespDTO1.setRemark("");
        final SaleProductDetailRespDTO saleProductDetailRespDTO = new SaleProductDetailRespDTO();
        saleProductDetailRespDTO.setIndex(0);
        saleProductDetailRespDTO.setOrderGuid("orderGuid");
        saleProductDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        saleProductDetailRespDTO.setPrice(0L);
        saleProductDetailRespDTO.setTotalPrice(0L);
        saleProductDetailRespDTO.setDiscountFee(0L);
        saleProductDetailRespDTO.setActuallyPayFee(0L);
        saleDetailRespDTO1.setProductDetails(Arrays.asList(saleProductDetailRespDTO));
        final SalePayDetailRespDTO salePayDetailRespDTO = new SalePayDetailRespDTO();
        salePayDetailRespDTO.setIndex(0);
        salePayDetailRespDTO.setOrderGuid("orderGuid");
        salePayDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        salePayDetailRespDTO.setPaymentType(0);
        salePayDetailRespDTO.setPayWayCode("x1001");
        saleDetailRespDTO1.setPayDetails(Arrays.asList(salePayDetailRespDTO));
        final List<SaleDetailRespDTO> saleDetailRespDTOS = Arrays.asList(saleDetailRespDTO1);
        final SaleDetailQueryDTO query = new SaleDetailQueryDTO();
        query.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        query.setCursor(0L);
        query.setLimit(0);
        query.setEnterpriseGuid("enterpriseGuid");
        query.setStoreGuid("storeGuid");
        when(mockTradeOrderMapper.querySaleDetail(query)).thenReturn(saleDetailRespDTOS);

        when(mockTradeItemMapper.querySaleProductDetail("enterpriseGuid", Arrays.asList(0L)))
                .thenReturn(Collections.emptyList());

        // Configure TradeDetailMapper.querySalePayDetail(...).
        final SalePayDetailRespDTO salePayDetailRespDTO1 = new SalePayDetailRespDTO();
        salePayDetailRespDTO1.setIndex(0);
        salePayDetailRespDTO1.setOrderGuid("orderGuid");
        salePayDetailRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        salePayDetailRespDTO1.setPaymentType(0);
        salePayDetailRespDTO1.setPayWayCode("x1001");
        final List<SalePayDetailRespDTO> salePayDetailRespDTOS = Arrays.asList(salePayDetailRespDTO1);
        when(mockTradeDetailMapper.querySalePayDetail("enterpriseGuid", Arrays.asList(0L)))
                .thenReturn(salePayDetailRespDTOS);

        // Run the test
        final SaleDetailLimitRespDTO result = openSaleDetailServiceImplUnderTest.query(saleDetailQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuery_TradeDetailMapperReturnsNoItems() {
        // Setup
        final SaleDetailQueryDTO saleDetailQueryDTO = new SaleDetailQueryDTO();
        saleDetailQueryDTO.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        saleDetailQueryDTO.setCursor(0L);
        saleDetailQueryDTO.setLimit(0);
        saleDetailQueryDTO.setEnterpriseGuid("enterpriseGuid");
        saleDetailQueryDTO.setStoreGuid("storeGuid");

        final SaleDetailLimitRespDTO expectedResult = new SaleDetailLimitRespDTO();
        final SaleDetailRespDTO saleDetailRespDTO = new SaleDetailRespDTO();
        saleDetailRespDTO.setOrderGuid("orderGuid");
        saleDetailRespDTO.setStoreGuId("storeGuId");
        saleDetailRespDTO.setStoreName("storeName");
        saleDetailRespDTO.setPosNO("posNO");
        expectedResult.setList(Arrays.asList(saleDetailRespDTO));

        // Configure TradeOrderMapper.querySaleDetail(...).
        final SaleDetailRespDTO saleDetailRespDTO1 = new SaleDetailRespDTO();
        saleDetailRespDTO1.setOrderGuid("orderGuid");
        saleDetailRespDTO1.setSaleCount(0);
        saleDetailRespDTO1.setReceiptType(0);
        saleDetailRespDTO1.setRemark("");
        final SaleProductDetailRespDTO saleProductDetailRespDTO = new SaleProductDetailRespDTO();
        saleProductDetailRespDTO.setIndex(0);
        saleProductDetailRespDTO.setOrderGuid("orderGuid");
        saleProductDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        saleProductDetailRespDTO.setPrice(0L);
        saleProductDetailRespDTO.setTotalPrice(0L);
        saleProductDetailRespDTO.setDiscountFee(0L);
        saleProductDetailRespDTO.setActuallyPayFee(0L);
        saleDetailRespDTO1.setProductDetails(Arrays.asList(saleProductDetailRespDTO));
        final SalePayDetailRespDTO salePayDetailRespDTO = new SalePayDetailRespDTO();
        salePayDetailRespDTO.setIndex(0);
        salePayDetailRespDTO.setOrderGuid("orderGuid");
        salePayDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        salePayDetailRespDTO.setPaymentType(0);
        salePayDetailRespDTO.setPayWayCode("x1001");
        saleDetailRespDTO1.setPayDetails(Arrays.asList(salePayDetailRespDTO));
        final List<SaleDetailRespDTO> saleDetailRespDTOS = Arrays.asList(saleDetailRespDTO1);
        final SaleDetailQueryDTO query = new SaleDetailQueryDTO();
        query.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        query.setCursor(0L);
        query.setLimit(0);
        query.setEnterpriseGuid("enterpriseGuid");
        query.setStoreGuid("storeGuid");
        when(mockTradeOrderMapper.querySaleDetail(query)).thenReturn(saleDetailRespDTOS);

        // Configure TradeItemMapper.querySaleProductDetail(...).
        final SaleProductDetailRespDTO saleProductDetailRespDTO1 = new SaleProductDetailRespDTO();
        saleProductDetailRespDTO1.setIndex(0);
        saleProductDetailRespDTO1.setOrderGuid("orderGuid");
        saleProductDetailRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        saleProductDetailRespDTO1.setPrice(0L);
        saleProductDetailRespDTO1.setTotalPrice(0L);
        saleProductDetailRespDTO1.setDiscountFee(0L);
        saleProductDetailRespDTO1.setActuallyPayFee(0L);
        final List<SaleProductDetailRespDTO> saleProductDetailRespDTOS = Arrays.asList(saleProductDetailRespDTO1);
        when(mockTradeItemMapper.querySaleProductDetail("enterpriseGuid", Arrays.asList(0L)))
                .thenReturn(saleProductDetailRespDTOS);

        when(mockTradeDetailMapper.querySalePayDetail("enterpriseGuid", Arrays.asList(0L)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final SaleDetailLimitRespDTO result = openSaleDetailServiceImplUnderTest.query(saleDetailQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
