package com.holderzone.holder.saas.store.deposit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.cmember.app.dto.account.response.SimpleMemberInfoDTO;
import com.holderzone.holder.saas.store.deposit.entity.bo.GoodsDO;
import com.holderzone.holder.saas.store.deposit.entity.bo.OperationGoodsDO;
import com.holderzone.holder.saas.store.deposit.mapper.HsdDepositMapper;
import com.holderzone.holder.saas.store.deposit.mapstruct.GoodsMapstruct;
import com.holderzone.holder.saas.store.deposit.service.*;
import com.holderzone.holder.saas.store.deposit.service.rpc.ItemRpcService;
import com.holderzone.holder.saas.store.deposit.service.rpc.MemberRpcService;
import com.holderzone.holder.saas.store.deposit.util.GenerateDepositOrderID;
import com.holderzone.holder.saas.store.deposit.util.PageAdapter;
import com.holderzone.holder.saas.store.deposit.util.SendMessageUtil;
import com.holderzone.saas.store.dto.deposit.req.*;
import com.holderzone.saas.store.dto.deposit.resp.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class HsdDepositServiceImplTest {

    @Mock
    private ItemRpcService mockItemRpcService;
    @Mock
    private DistributedIdService mockDistributedIdService;
    @Mock
    private IHsdGoodsService mockIHsdGoodsService;
    @Mock
    private IHsdOperationService mockIHsdOperationService;
    @Mock
    private IHsdOperationGoodsService mockIHsdOperationGoodsService;
    @Mock
    private MemberRpcService mockMemberRpcService;
    @Mock
    private IHsdRemindService mockIHsdRemindService;
    @Mock
    private GenerateDepositOrderID mockGenerateDepositOrderID;
    @Mock
    private GoodsMapstruct mockGoodsMapstruct;
    @Mock
    private HsdDepositMapper mockHsdDepositMapper;
    @Mock
    private SendMessageUtil mockSendMessageUtil;
    @Mock
    private ThreadPoolTaskExecutor mockTaskExecutor;

    private HsdDepositServiceImpl hsdDepositServiceImplUnderTest;

    @Before
    public void setUp() {
        hsdDepositServiceImplUnderTest = new HsdDepositServiceImpl(mockItemRpcService, mockDistributedIdService,
                mockIHsdGoodsService, mockIHsdOperationService, mockIHsdOperationGoodsService, mockMemberRpcService,
                mockIHsdRemindService, mockGenerateDepositOrderID, mockGoodsMapstruct);
        ReflectionTestUtils.setField(hsdDepositServiceImplUnderTest, "hsdDepositMapper", mockHsdDepositMapper);
        ReflectionTestUtils.setField(hsdDepositServiceImplUnderTest, "sendMessageUtil", mockSendMessageUtil);
        ReflectionTestUtils.setField(hsdDepositServiceImplUnderTest, "taskExecutor", mockTaskExecutor);
        ReflectionTestUtils.setField(hsdDepositServiceImplUnderTest, "erpHost", "erpHost");
    }

    @Test
    public void testCreateDepositRecord() {
        // Setup
        final DepositCreateReqDTO depositCreateReqDTO = new DepositCreateReqDTO();
        depositCreateReqDTO.setStoreGuid("storeGuid");
        depositCreateReqDTO.setMemberGuid("memberGuid");
        depositCreateReqDTO.setHeadPortrait("headPortrait");
        final GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
        goodsRespDTO.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO.setGoodsName("goodsName");
        goodsRespDTO.setSkuName("skuName");
        goodsRespDTO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO.setGoodsUnit("goodsUnit");
        goodsRespDTO.setExpireTime("永久有效");
        goodsRespDTO.setDepositNum(0);
        goodsRespDTO.setTakeOutNum(0);
        depositCreateReqDTO.setGoods(Arrays.asList(goodsRespDTO));
        depositCreateReqDTO.setRemark("remark");
        depositCreateReqDTO.setCustomerName("customerName");
        depositCreateReqDTO.setPhoneNum("phoneNum");

        when(mockDistributedIdService.nextDepositItemGuid()).thenReturn("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        when(mockGenerateDepositOrderID.generateDepositID("storeGuid")).thenReturn("depositOrderId");

        // Configure GoodsMapstruct.fromGoodsDTO(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setStoreGuid("storeGuid");
        goodsDO.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setSkuName("skuName");
        goodsDO.setGoodsUnit("goodsUnit");
        goodsDO.setGoodsClassify(0);
        goodsDO.setExpireTime("永久有效");
        goodsDO.setResidueNum(0);
        goodsDO.setDepositNum(0);
        final GoodsRespDTO goodsRespDTO1 = new GoodsRespDTO();
        goodsRespDTO1.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO1.setGoodsName("goodsName");
        goodsRespDTO1.setSkuName("skuName");
        goodsRespDTO1.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO1.setGoodsUnit("goodsUnit");
        goodsRespDTO1.setExpireTime("永久有效");
        goodsRespDTO1.setDepositNum(0);
        goodsRespDTO1.setTakeOutNum(0);
        when(mockGoodsMapstruct.fromGoodsDTO(goodsRespDTO1)).thenReturn(goodsDO);

        when(mockDistributedIdService.nextGoodsGuid()).thenReturn("22c512b2-f243-4d96-804b-a894920227c8");

        // Configure IHsdRemindService.queryRemindRecord(...).
        final MessageRemindReqDTO messageRemindReqDTO = new MessageRemindReqDTO();
        messageRemindReqDTO.setStoreName("storeName");
        messageRemindReqDTO.setDepositRemind(0);
        messageRemindReqDTO.setGetRemind(0);
        messageRemindReqDTO.setExpireRemind(0);
        messageRemindReqDTO.setAdvanceDays(0);
        when(mockIHsdRemindService.queryRemindRecord("storeGuid")).thenReturn(messageRemindReqDTO);

        when(mockDistributedIdService.nextOperationGuid()).thenReturn("6b8ef5d7-f595-446d-b4e6-76c49d4760f8");
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockTaskExecutor).execute(any(Runnable.class));
        when(mockItemRpcService.findSkusByItemName(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Run the test
        final Boolean result = hsdDepositServiceImplUnderTest.createDepositRecord(depositCreateReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm IHsdGoodsService.saveBatch(...).
        final GoodsDO goodsDO1 = new GoodsDO();
        goodsDO1.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO1.setStoreGuid("storeGuid");
        goodsDO1.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        goodsDO1.setGoodsName("goodsName");
        goodsDO1.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO1.setSkuName("skuName");
        goodsDO1.setGoodsUnit("goodsUnit");
        goodsDO1.setGoodsClassify(0);
        goodsDO1.setExpireTime("永久有效");
        goodsDO1.setResidueNum(0);
        goodsDO1.setDepositNum(0);
        final List<GoodsDO> entityList = Arrays.asList(goodsDO1);
        verify(mockIHsdGoodsService).saveBatch(entityList);
        verify(mockSendMessageUtil).sendMessage(any(MessageDTO.class), eq("entGuid"));

        // Confirm IHsdOperationService.createOperationRecord(...).
        final OperationCreateReqDTO operationCreateReqDTO = new OperationCreateReqDTO();
        operationCreateReqDTO.setOperationWay(0);
        operationCreateReqDTO.setUserId("memberGuid");
        operationCreateReqDTO.setOperator("operator");
        operationCreateReqDTO.setRemark("remark");
        verify(mockIHsdOperationService).createOperationRecord(operationCreateReqDTO,
                "6b8ef5d7-f595-446d-b4e6-76c49d4760f8", "2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");

        // Confirm IHsdOperationGoodsService.saveBatch(...).
        final OperationGoodsDO operationGoodsDO = new OperationGoodsDO();
        operationGoodsDO.setGuid("6b8ef5d7-f595-446d-b4e6-76c49d4760f8");
        operationGoodsDO.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        operationGoodsDO.setGoodsName("goodsName");
        operationGoodsDO.setSkuName("skuName");
        operationGoodsDO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        operationGoodsDO.setOperatorNum(0);
        operationGoodsDO.setResidueNum(0);
        operationGoodsDO.setDepositGoodGuid("22c512b2-f243-4d96-804b-a894920227c8");
        final List<OperationGoodsDO> entityList1 = Arrays.asList(operationGoodsDO);
        verify(mockIHsdOperationGoodsService).saveBatch(entityList1);
        verify(mockTaskExecutor).execute(any(Runnable.class));
    }

    @Test
    public void testQueryDepositRecord() {
        // Setup
        final DepositQueryReqDTO depositQueryReqDTO = new DepositQueryReqDTO();
        depositQueryReqDTO.setStoreGuid("storeGuid");
        depositQueryReqDTO.setCondition("condition");
        depositQueryReqDTO.setPhoneGuid("memberInfoGuid");
        depositQueryReqDTO.setWxGuid("memberInfoGuid");

        // Configure MemberRpcService.getMemberInfo(...).
        final SimpleMemberInfoDTO simpleMemberInfoDTO = new SimpleMemberInfoDTO();
        simpleMemberInfoDTO.setMemberInfoGuid("memberInfoGuid");
        simpleMemberInfoDTO.setPhoneNum("phoneNum");
        simpleMemberInfoDTO.setOpenId("openId");
        simpleMemberInfoDTO.setNickName("nickName");
        final List<SimpleMemberInfoDTO> simpleMemberInfoDTOS = Arrays.asList(simpleMemberInfoDTO);
        when(mockMemberRpcService.getMemberInfo("condition")).thenReturn(simpleMemberInfoDTOS);

        // Configure HsdDepositMapper.queryDepositRecordFromMemberGuid(...).
        final DepositQueryReqDTO depositQueryReqDTO1 = new DepositQueryReqDTO();
        depositQueryReqDTO1.setStoreGuid("storeGuid");
        depositQueryReqDTO1.setCondition("condition");
        depositQueryReqDTO1.setPhoneGuid("memberInfoGuid");
        depositQueryReqDTO1.setWxGuid("memberInfoGuid");
        when(mockHsdDepositMapper.queryDepositRecordFromMemberGuid(any(PageAdapter.class),
                eq(depositQueryReqDTO1))).thenReturn(null);

        // Configure IHsdGoodsService.queryGoodsList(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setStoreGuid("storeGuid");
        goodsDO.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setSkuName("skuName");
        goodsDO.setGoodsUnit("goodsUnit");
        goodsDO.setGoodsClassify(0);
        goodsDO.setExpireTime("永久有效");
        goodsDO.setResidueNum(0);
        goodsDO.setDepositNum(0);
        final List<GoodsDO> goodsDOS = Arrays.asList(goodsDO);
        when(mockIHsdGoodsService.queryGoodsList("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf")).thenReturn(goodsDOS);

        // Run the test
        final Page<DepositQueryRespDTO> result = hsdDepositServiceImplUnderTest.queryDepositRecord(depositQueryReqDTO);

        // Verify the results
    }

    @Test
    public void testQueryDepositRecord_MemberRpcServiceReturnsNoItems() {
        // Setup
        final DepositQueryReqDTO depositQueryReqDTO = new DepositQueryReqDTO();
        depositQueryReqDTO.setStoreGuid("storeGuid");
        depositQueryReqDTO.setCondition("condition");
        depositQueryReqDTO.setPhoneGuid("memberInfoGuid");
        depositQueryReqDTO.setWxGuid("memberInfoGuid");

        when(mockMemberRpcService.getMemberInfo("condition")).thenReturn(Collections.emptyList());

        // Configure HsdDepositMapper.queryDepositRecordFromOrderId(...).
        final DepositQueryReqDTO depositQueryReqDTO1 = new DepositQueryReqDTO();
        depositQueryReqDTO1.setStoreGuid("storeGuid");
        depositQueryReqDTO1.setCondition("condition");
        depositQueryReqDTO1.setPhoneGuid("memberInfoGuid");
        depositQueryReqDTO1.setWxGuid("memberInfoGuid");
        when(mockHsdDepositMapper.queryDepositRecordFromOrderId(any(PageAdapter.class),
                eq(depositQueryReqDTO1))).thenReturn(null);

        // Run the test
        final Page<DepositQueryRespDTO> result = hsdDepositServiceImplUnderTest.queryDepositRecord(depositQueryReqDTO);

        // Verify the results
    }

    @Test
    public void testQueryDepositRecord_IHsdGoodsServiceReturnsNoItems() {
        // Setup
        final DepositQueryReqDTO depositQueryReqDTO = new DepositQueryReqDTO();
        depositQueryReqDTO.setStoreGuid("storeGuid");
        depositQueryReqDTO.setCondition("condition");
        depositQueryReqDTO.setPhoneGuid("memberInfoGuid");
        depositQueryReqDTO.setWxGuid("memberInfoGuid");

        // Configure MemberRpcService.getMemberInfo(...).
        final SimpleMemberInfoDTO simpleMemberInfoDTO = new SimpleMemberInfoDTO();
        simpleMemberInfoDTO.setMemberInfoGuid("memberInfoGuid");
        simpleMemberInfoDTO.setPhoneNum("phoneNum");
        simpleMemberInfoDTO.setOpenId("openId");
        simpleMemberInfoDTO.setNickName("nickName");
        final List<SimpleMemberInfoDTO> simpleMemberInfoDTOS = Arrays.asList(simpleMemberInfoDTO);
        when(mockMemberRpcService.getMemberInfo("condition")).thenReturn(simpleMemberInfoDTOS);

        // Configure HsdDepositMapper.queryDepositRecordFromMemberGuid(...).
        final DepositQueryReqDTO depositQueryReqDTO1 = new DepositQueryReqDTO();
        depositQueryReqDTO1.setStoreGuid("storeGuid");
        depositQueryReqDTO1.setCondition("condition");
        depositQueryReqDTO1.setPhoneGuid("memberInfoGuid");
        depositQueryReqDTO1.setWxGuid("memberInfoGuid");
        when(mockHsdDepositMapper.queryDepositRecordFromMemberGuid(any(PageAdapter.class),
                eq(depositQueryReqDTO1))).thenReturn(null);

        when(mockIHsdGoodsService.queryGoodsList("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Page<DepositQueryRespDTO> result = hsdDepositServiceImplUnderTest.queryDepositRecord(depositQueryReqDTO);

        // Verify the results
    }

    @Test
    public void testQueryDepositDetail() {
        // Setup
        final QueryDepositDetailReqDTO depositQueryReqDTO = new QueryDepositDetailReqDTO();
        depositQueryReqDTO.setDepositGuid("depositGuid");

        final GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
        goodsRespDTO.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO.setGoodsName("goodsName");
        goodsRespDTO.setSkuName("skuName");
        goodsRespDTO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO.setGoodsUnit("goodsUnit");
        goodsRespDTO.setExpireTime("永久有效");
        goodsRespDTO.setDepositNum(0);
        goodsRespDTO.setTakeOutNum(0);
        final List<GoodsRespDTO> expectedResult = Arrays.asList(goodsRespDTO);

        // Configure IHsdGoodsService.queryGoodsList(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setStoreGuid("storeGuid");
        goodsDO.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setSkuName("skuName");
        goodsDO.setGoodsUnit("goodsUnit");
        goodsDO.setGoodsClassify(0);
        goodsDO.setExpireTime("永久有效");
        goodsDO.setResidueNum(0);
        goodsDO.setDepositNum(0);
        final List<GoodsDO> goodsDOS = Arrays.asList(goodsDO);
        when(mockIHsdGoodsService.queryGoodsList("depositGuid")).thenReturn(goodsDOS);

        // Configure GoodsMapstruct.fromGoodsList(...).
        final GoodsRespDTO goodsRespDTO1 = new GoodsRespDTO();
        goodsRespDTO1.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO1.setGoodsName("goodsName");
        goodsRespDTO1.setSkuName("skuName");
        goodsRespDTO1.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO1.setGoodsUnit("goodsUnit");
        goodsRespDTO1.setExpireTime("永久有效");
        goodsRespDTO1.setDepositNum(0);
        goodsRespDTO1.setTakeOutNum(0);
        final List<GoodsRespDTO> goodsRespDTOS = Arrays.asList(goodsRespDTO1);
        final GoodsDO goodsDO1 = new GoodsDO();
        goodsDO1.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO1.setStoreGuid("storeGuid");
        goodsDO1.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        goodsDO1.setGoodsName("goodsName");
        goodsDO1.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO1.setSkuName("skuName");
        goodsDO1.setGoodsUnit("goodsUnit");
        goodsDO1.setGoodsClassify(0);
        goodsDO1.setExpireTime("永久有效");
        goodsDO1.setResidueNum(0);
        goodsDO1.setDepositNum(0);
        final List<GoodsDO> goodsDOList = Arrays.asList(goodsDO1);
        when(mockGoodsMapstruct.fromGoodsList(goodsDOList)).thenReturn(goodsRespDTOS);

        // Run the test
        final List<GoodsRespDTO> result = hsdDepositServiceImplUnderTest.queryDepositDetail(depositQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryDepositDetail_IHsdGoodsServiceReturnsNoItems() {
        // Setup
        final QueryDepositDetailReqDTO depositQueryReqDTO = new QueryDepositDetailReqDTO();
        depositQueryReqDTO.setDepositGuid("depositGuid");

        final GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
        goodsRespDTO.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO.setGoodsName("goodsName");
        goodsRespDTO.setSkuName("skuName");
        goodsRespDTO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO.setGoodsUnit("goodsUnit");
        goodsRespDTO.setExpireTime("永久有效");
        goodsRespDTO.setDepositNum(0);
        goodsRespDTO.setTakeOutNum(0);
        final List<GoodsRespDTO> expectedResult = Arrays.asList(goodsRespDTO);
        when(mockIHsdGoodsService.queryGoodsList("depositGuid")).thenReturn(Collections.emptyList());

        // Configure GoodsMapstruct.fromGoodsList(...).
        final GoodsRespDTO goodsRespDTO1 = new GoodsRespDTO();
        goodsRespDTO1.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO1.setGoodsName("goodsName");
        goodsRespDTO1.setSkuName("skuName");
        goodsRespDTO1.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO1.setGoodsUnit("goodsUnit");
        goodsRespDTO1.setExpireTime("永久有效");
        goodsRespDTO1.setDepositNum(0);
        goodsRespDTO1.setTakeOutNum(0);
        final List<GoodsRespDTO> goodsRespDTOS = Arrays.asList(goodsRespDTO1);
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setStoreGuid("storeGuid");
        goodsDO.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setSkuName("skuName");
        goodsDO.setGoodsUnit("goodsUnit");
        goodsDO.setGoodsClassify(0);
        goodsDO.setExpireTime("永久有效");
        goodsDO.setResidueNum(0);
        goodsDO.setDepositNum(0);
        final List<GoodsDO> goodsDOList = Arrays.asList(goodsDO);
        when(mockGoodsMapstruct.fromGoodsList(goodsDOList)).thenReturn(goodsRespDTOS);

        // Run the test
        final List<GoodsRespDTO> result = hsdDepositServiceImplUnderTest.queryDepositDetail(depositQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryDepositDetail_GoodsMapstructReturnsNoItems() {
        // Setup
        final QueryDepositDetailReqDTO depositQueryReqDTO = new QueryDepositDetailReqDTO();
        depositQueryReqDTO.setDepositGuid("depositGuid");

        // Configure IHsdGoodsService.queryGoodsList(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setStoreGuid("storeGuid");
        goodsDO.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setSkuName("skuName");
        goodsDO.setGoodsUnit("goodsUnit");
        goodsDO.setGoodsClassify(0);
        goodsDO.setExpireTime("永久有效");
        goodsDO.setResidueNum(0);
        goodsDO.setDepositNum(0);
        final List<GoodsDO> goodsDOS = Arrays.asList(goodsDO);
        when(mockIHsdGoodsService.queryGoodsList("depositGuid")).thenReturn(goodsDOS);

        // Configure GoodsMapstruct.fromGoodsList(...).
        final GoodsDO goodsDO1 = new GoodsDO();
        goodsDO1.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO1.setStoreGuid("storeGuid");
        goodsDO1.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        goodsDO1.setGoodsName("goodsName");
        goodsDO1.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO1.setSkuName("skuName");
        goodsDO1.setGoodsUnit("goodsUnit");
        goodsDO1.setGoodsClassify(0);
        goodsDO1.setExpireTime("永久有效");
        goodsDO1.setResidueNum(0);
        goodsDO1.setDepositNum(0);
        final List<GoodsDO> goodsDOList = Arrays.asList(goodsDO1);
        when(mockGoodsMapstruct.fromGoodsList(goodsDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodsRespDTO> result = hsdDepositServiceImplUnderTest.queryDepositDetail(depositQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryDepositDetailForPos() {
        // Setup
        final QueryDepositDetailReqDTO depositQueryReqDTO = new QueryDepositDetailReqDTO();
        depositQueryReqDTO.setDepositGuid("depositGuid");

        final DepositDetailForPosRespDTO expectedResult = new DepositDetailForPosRespDTO();
        expectedResult.setDepositOrderId("depositOrderId");
        expectedResult.setSaveTime("saveTime");
        expectedResult.setHeadPortrait("headPortrait");
        expectedResult.setCustomerName("customerName");
        expectedResult.setPhoneNum("phoneNum");
        final GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
        goodsRespDTO.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO.setGoodsName("goodsName");
        goodsRespDTO.setSkuName("skuName");
        goodsRespDTO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO.setGoodsUnit("goodsUnit");
        goodsRespDTO.setExpireTime("永久有效");
        goodsRespDTO.setDepositNum(0);
        goodsRespDTO.setTakeOutNum(0);
        expectedResult.setGoodsRespDTOS(Arrays.asList(goodsRespDTO));
        expectedResult.setRemark("remark");

        // Configure IHsdGoodsService.queryGoodsList(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setStoreGuid("storeGuid");
        goodsDO.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setSkuName("skuName");
        goodsDO.setGoodsUnit("goodsUnit");
        goodsDO.setGoodsClassify(0);
        goodsDO.setExpireTime("永久有效");
        goodsDO.setResidueNum(0);
        goodsDO.setDepositNum(0);
        final List<GoodsDO> goodsDOS = Arrays.asList(goodsDO);
        when(mockIHsdGoodsService.queryGoodsList("depositGuid")).thenReturn(goodsDOS);

        // Configure GoodsMapstruct.fromGoodsList(...).
        final GoodsRespDTO goodsRespDTO1 = new GoodsRespDTO();
        goodsRespDTO1.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO1.setGoodsName("goodsName");
        goodsRespDTO1.setSkuName("skuName");
        goodsRespDTO1.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO1.setGoodsUnit("goodsUnit");
        goodsRespDTO1.setExpireTime("永久有效");
        goodsRespDTO1.setDepositNum(0);
        goodsRespDTO1.setTakeOutNum(0);
        final List<GoodsRespDTO> goodsRespDTOS = Arrays.asList(goodsRespDTO1);
        final GoodsDO goodsDO1 = new GoodsDO();
        goodsDO1.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO1.setStoreGuid("storeGuid");
        goodsDO1.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        goodsDO1.setGoodsName("goodsName");
        goodsDO1.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO1.setSkuName("skuName");
        goodsDO1.setGoodsUnit("goodsUnit");
        goodsDO1.setGoodsClassify(0);
        goodsDO1.setExpireTime("永久有效");
        goodsDO1.setResidueNum(0);
        goodsDO1.setDepositNum(0);
        final List<GoodsDO> goodsDOList = Arrays.asList(goodsDO1);
        when(mockGoodsMapstruct.fromGoodsList(goodsDOList)).thenReturn(goodsRespDTOS);

        // Run the test
        final DepositDetailForPosRespDTO result = hsdDepositServiceImplUnderTest.queryDepositDetailForPos(
                depositQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryDepositDetailForPos_IHsdGoodsServiceReturnsNoItems() {
        // Setup
        final QueryDepositDetailReqDTO depositQueryReqDTO = new QueryDepositDetailReqDTO();
        depositQueryReqDTO.setDepositGuid("depositGuid");

        final DepositDetailForPosRespDTO expectedResult = new DepositDetailForPosRespDTO();
        expectedResult.setDepositOrderId("depositOrderId");
        expectedResult.setSaveTime("saveTime");
        expectedResult.setHeadPortrait("headPortrait");
        expectedResult.setCustomerName("customerName");
        expectedResult.setPhoneNum("phoneNum");
        final GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
        goodsRespDTO.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO.setGoodsName("goodsName");
        goodsRespDTO.setSkuName("skuName");
        goodsRespDTO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO.setGoodsUnit("goodsUnit");
        goodsRespDTO.setExpireTime("永久有效");
        goodsRespDTO.setDepositNum(0);
        goodsRespDTO.setTakeOutNum(0);
        expectedResult.setGoodsRespDTOS(Arrays.asList(goodsRespDTO));
        expectedResult.setRemark("remark");

        when(mockIHsdGoodsService.queryGoodsList("depositGuid")).thenReturn(Collections.emptyList());

        // Configure GoodsMapstruct.fromGoodsList(...).
        final GoodsRespDTO goodsRespDTO1 = new GoodsRespDTO();
        goodsRespDTO1.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO1.setGoodsName("goodsName");
        goodsRespDTO1.setSkuName("skuName");
        goodsRespDTO1.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO1.setGoodsUnit("goodsUnit");
        goodsRespDTO1.setExpireTime("永久有效");
        goodsRespDTO1.setDepositNum(0);
        goodsRespDTO1.setTakeOutNum(0);
        final List<GoodsRespDTO> goodsRespDTOS = Arrays.asList(goodsRespDTO1);
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setStoreGuid("storeGuid");
        goodsDO.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setSkuName("skuName");
        goodsDO.setGoodsUnit("goodsUnit");
        goodsDO.setGoodsClassify(0);
        goodsDO.setExpireTime("永久有效");
        goodsDO.setResidueNum(0);
        goodsDO.setDepositNum(0);
        final List<GoodsDO> goodsDOList = Arrays.asList(goodsDO);
        when(mockGoodsMapstruct.fromGoodsList(goodsDOList)).thenReturn(goodsRespDTOS);

        // Run the test
        final DepositDetailForPosRespDTO result = hsdDepositServiceImplUnderTest.queryDepositDetailForPos(
                depositQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryDepositDetailForPos_GoodsMapstructReturnsNoItems() {
        // Setup
        final QueryDepositDetailReqDTO depositQueryReqDTO = new QueryDepositDetailReqDTO();
        depositQueryReqDTO.setDepositGuid("depositGuid");

        final DepositDetailForPosRespDTO expectedResult = new DepositDetailForPosRespDTO();
        expectedResult.setDepositOrderId("depositOrderId");
        expectedResult.setSaveTime("saveTime");
        expectedResult.setHeadPortrait("headPortrait");
        expectedResult.setCustomerName("customerName");
        expectedResult.setPhoneNum("phoneNum");
        final GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
        goodsRespDTO.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO.setGoodsName("goodsName");
        goodsRespDTO.setSkuName("skuName");
        goodsRespDTO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO.setGoodsUnit("goodsUnit");
        goodsRespDTO.setExpireTime("永久有效");
        goodsRespDTO.setDepositNum(0);
        goodsRespDTO.setTakeOutNum(0);
        expectedResult.setGoodsRespDTOS(Arrays.asList(goodsRespDTO));
        expectedResult.setRemark("remark");

        // Configure IHsdGoodsService.queryGoodsList(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setStoreGuid("storeGuid");
        goodsDO.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setSkuName("skuName");
        goodsDO.setGoodsUnit("goodsUnit");
        goodsDO.setGoodsClassify(0);
        goodsDO.setExpireTime("永久有效");
        goodsDO.setResidueNum(0);
        goodsDO.setDepositNum(0);
        final List<GoodsDO> goodsDOS = Arrays.asList(goodsDO);
        when(mockIHsdGoodsService.queryGoodsList("depositGuid")).thenReturn(goodsDOS);

        // Configure GoodsMapstruct.fromGoodsList(...).
        final GoodsDO goodsDO1 = new GoodsDO();
        goodsDO1.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO1.setStoreGuid("storeGuid");
        goodsDO1.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        goodsDO1.setGoodsName("goodsName");
        goodsDO1.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO1.setSkuName("skuName");
        goodsDO1.setGoodsUnit("goodsUnit");
        goodsDO1.setGoodsClassify(0);
        goodsDO1.setExpireTime("永久有效");
        goodsDO1.setResidueNum(0);
        goodsDO1.setDepositNum(0);
        final List<GoodsDO> goodsDOList = Arrays.asList(goodsDO1);
        when(mockGoodsMapstruct.fromGoodsList(goodsDOList)).thenReturn(Collections.emptyList());

        // Run the test
        final DepositDetailForPosRespDTO result = hsdDepositServiceImplUnderTest.queryDepositDetailForPos(
                depositQueryReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetDeposit() {
        // Setup
        final DepositGetReqDTO depositGetReqDTO = new DepositGetReqDTO();
        depositGetReqDTO.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        depositGetReqDTO.setUserGuid("memberGuid");
        final GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
        goodsRespDTO.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO.setGoodsName("goodsName");
        goodsRespDTO.setSkuName("skuName");
        goodsRespDTO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO.setGoodsUnit("goodsUnit");
        goodsRespDTO.setExpireTime("永久有效");
        goodsRespDTO.setDepositNum(0);
        goodsRespDTO.setTakeOutNum(0);
        depositGetReqDTO.setGoodsList(Arrays.asList(goodsRespDTO));
        depositGetReqDTO.setRemark("remark");

        when(mockDistributedIdService.nextOperationGuid()).thenReturn("6b8ef5d7-f595-446d-b4e6-76c49d4760f8");

        // Configure IHsdGoodsService.list(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setStoreGuid("storeGuid");
        goodsDO.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setSkuName("skuName");
        goodsDO.setGoodsUnit("goodsUnit");
        goodsDO.setGoodsClassify(0);
        goodsDO.setExpireTime("永久有效");
        goodsDO.setResidueNum(0);
        goodsDO.setDepositNum(0);
        final List<GoodsDO> goodsDOS = Arrays.asList(goodsDO);
        when(mockIHsdGoodsService.list(any(LambdaQueryWrapper.class))).thenReturn(goodsDOS);

        // Configure IHsdRemindService.queryRemindRecord(...).
        final MessageRemindReqDTO messageRemindReqDTO = new MessageRemindReqDTO();
        messageRemindReqDTO.setStoreName("storeName");
        messageRemindReqDTO.setDepositRemind(0);
        messageRemindReqDTO.setGetRemind(0);
        messageRemindReqDTO.setExpireRemind(0);
        messageRemindReqDTO.setAdvanceDays(0);
        when(mockIHsdRemindService.queryRemindRecord("storeGuid")).thenReturn(messageRemindReqDTO);

        // Configure IHsdGoodsService.queryGoodsItem(...).
        final GoodsDO goodsDO1 = new GoodsDO();
        goodsDO1.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO1.setStoreGuid("storeGuid");
        goodsDO1.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        goodsDO1.setGoodsName("goodsName");
        goodsDO1.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO1.setSkuName("skuName");
        goodsDO1.setGoodsUnit("goodsUnit");
        goodsDO1.setGoodsClassify(0);
        goodsDO1.setExpireTime("永久有效");
        goodsDO1.setResidueNum(0);
        goodsDO1.setDepositNum(0);
        when(mockIHsdGoodsService.queryGoodsItem("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf",
                "22c512b2-f243-4d96-804b-a894920227c8")).thenReturn(goodsDO1);

        // Configure IHsdGoodsService.queryGoodsList(...).
        final GoodsDO goodsDO2 = new GoodsDO();
        goodsDO2.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO2.setStoreGuid("storeGuid");
        goodsDO2.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        goodsDO2.setGoodsName("goodsName");
        goodsDO2.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO2.setSkuName("skuName");
        goodsDO2.setGoodsUnit("goodsUnit");
        goodsDO2.setGoodsClassify(0);
        goodsDO2.setExpireTime("永久有效");
        goodsDO2.setResidueNum(0);
        goodsDO2.setDepositNum(0);
        final List<GoodsDO> goodsDOS1 = Arrays.asList(goodsDO2);
        when(mockIHsdGoodsService.queryGoodsList("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf")).thenReturn(goodsDOS1);

        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockTaskExecutor).execute(any(Runnable.class));
        when(mockItemRpcService.findSkusByItemName(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Run the test
        final boolean result = hsdDepositServiceImplUnderTest.getDeposit(depositGetReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm IHsdOperationService.createOperationRecord(...).
        final OperationCreateReqDTO operationCreateReqDTO = new OperationCreateReqDTO();
        operationCreateReqDTO.setOperationWay(0);
        operationCreateReqDTO.setUserId("memberGuid");
        operationCreateReqDTO.setOperator("operator");
        operationCreateReqDTO.setRemark("remark");
        verify(mockIHsdOperationService).createOperationRecord(operationCreateReqDTO,
                "6b8ef5d7-f595-446d-b4e6-76c49d4760f8", "2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");

        // Confirm IHsdGoodsService.saveOrUpdateBatch(...).
        final GoodsDO goodsDO3 = new GoodsDO();
        goodsDO3.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO3.setStoreGuid("storeGuid");
        goodsDO3.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        goodsDO3.setGoodsName("goodsName");
        goodsDO3.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO3.setSkuName("skuName");
        goodsDO3.setGoodsUnit("goodsUnit");
        goodsDO3.setGoodsClassify(0);
        goodsDO3.setExpireTime("永久有效");
        goodsDO3.setResidueNum(0);
        goodsDO3.setDepositNum(0);
        final List<GoodsDO> entityList = Arrays.asList(goodsDO3);
        verify(mockIHsdGoodsService).saveOrUpdateBatch(entityList);
        verify(mockSendMessageUtil).sendMessage(any(MessageDTO.class), eq("entGuid"));

        // Confirm IHsdOperationGoodsService.saveBatch(...).
        final OperationGoodsDO operationGoodsDO = new OperationGoodsDO();
        operationGoodsDO.setGuid("6b8ef5d7-f595-446d-b4e6-76c49d4760f8");
        operationGoodsDO.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        operationGoodsDO.setGoodsName("goodsName");
        operationGoodsDO.setSkuName("skuName");
        operationGoodsDO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        operationGoodsDO.setOperatorNum(0);
        operationGoodsDO.setResidueNum(0);
        operationGoodsDO.setDepositGoodGuid("22c512b2-f243-4d96-804b-a894920227c8");
        final List<OperationGoodsDO> entityList1 = Arrays.asList(operationGoodsDO);
        verify(mockIHsdOperationGoodsService).saveBatch(entityList1);
        verify(mockTaskExecutor).execute(any(Runnable.class));
    }

    @Test
    public void testGetDeposit_IHsdGoodsServiceListReturnsNoItems() {
        // Setup
        final DepositGetReqDTO depositGetReqDTO = new DepositGetReqDTO();
        depositGetReqDTO.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        depositGetReqDTO.setUserGuid("memberGuid");
        final GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
        goodsRespDTO.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO.setGoodsName("goodsName");
        goodsRespDTO.setSkuName("skuName");
        goodsRespDTO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO.setGoodsUnit("goodsUnit");
        goodsRespDTO.setExpireTime("永久有效");
        goodsRespDTO.setDepositNum(0);
        goodsRespDTO.setTakeOutNum(0);
        depositGetReqDTO.setGoodsList(Arrays.asList(goodsRespDTO));
        depositGetReqDTO.setRemark("remark");

        when(mockDistributedIdService.nextOperationGuid()).thenReturn("6b8ef5d7-f595-446d-b4e6-76c49d4760f8");
        when(mockIHsdGoodsService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> hsdDepositServiceImplUnderTest.getDeposit(depositGetReqDTO))
                .isInstanceOf(BusinessException.class);

        // Confirm IHsdOperationService.createOperationRecord(...).
        final OperationCreateReqDTO operationCreateReqDTO = new OperationCreateReqDTO();
        operationCreateReqDTO.setOperationWay(0);
        operationCreateReqDTO.setUserId("memberGuid");
        operationCreateReqDTO.setOperator("operator");
        operationCreateReqDTO.setRemark("remark");
        verify(mockIHsdOperationService).createOperationRecord(operationCreateReqDTO,
                "6b8ef5d7-f595-446d-b4e6-76c49d4760f8", "2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
    }

    @Test
    public void testGetDeposit_IHsdGoodsServiceQueryGoodsListReturnsNoItems() {
        // Setup
        final DepositGetReqDTO depositGetReqDTO = new DepositGetReqDTO();
        depositGetReqDTO.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        depositGetReqDTO.setUserGuid("memberGuid");
        final GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
        goodsRespDTO.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO.setGoodsName("goodsName");
        goodsRespDTO.setSkuName("skuName");
        goodsRespDTO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsRespDTO.setGoodsUnit("goodsUnit");
        goodsRespDTO.setExpireTime("永久有效");
        goodsRespDTO.setDepositNum(0);
        goodsRespDTO.setTakeOutNum(0);
        depositGetReqDTO.setGoodsList(Arrays.asList(goodsRespDTO));
        depositGetReqDTO.setRemark("remark");

        when(mockDistributedIdService.nextOperationGuid()).thenReturn("6b8ef5d7-f595-446d-b4e6-76c49d4760f8");

        // Configure IHsdGoodsService.list(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setStoreGuid("storeGuid");
        goodsDO.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setSkuName("skuName");
        goodsDO.setGoodsUnit("goodsUnit");
        goodsDO.setGoodsClassify(0);
        goodsDO.setExpireTime("永久有效");
        goodsDO.setResidueNum(0);
        goodsDO.setDepositNum(0);
        final List<GoodsDO> goodsDOS = Arrays.asList(goodsDO);
        when(mockIHsdGoodsService.list(any(LambdaQueryWrapper.class))).thenReturn(goodsDOS);

        // Configure IHsdRemindService.queryRemindRecord(...).
        final MessageRemindReqDTO messageRemindReqDTO = new MessageRemindReqDTO();
        messageRemindReqDTO.setStoreName("storeName");
        messageRemindReqDTO.setDepositRemind(0);
        messageRemindReqDTO.setGetRemind(0);
        messageRemindReqDTO.setExpireRemind(0);
        messageRemindReqDTO.setAdvanceDays(0);
        when(mockIHsdRemindService.queryRemindRecord("storeGuid")).thenReturn(messageRemindReqDTO);

        // Configure IHsdGoodsService.queryGoodsItem(...).
        final GoodsDO goodsDO1 = new GoodsDO();
        goodsDO1.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO1.setStoreGuid("storeGuid");
        goodsDO1.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        goodsDO1.setGoodsName("goodsName");
        goodsDO1.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO1.setSkuName("skuName");
        goodsDO1.setGoodsUnit("goodsUnit");
        goodsDO1.setGoodsClassify(0);
        goodsDO1.setExpireTime("永久有效");
        goodsDO1.setResidueNum(0);
        goodsDO1.setDepositNum(0);
        when(mockIHsdGoodsService.queryGoodsItem("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf",
                "22c512b2-f243-4d96-804b-a894920227c8")).thenReturn(goodsDO1);

        when(mockIHsdGoodsService.queryGoodsList("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf"))
                .thenReturn(Collections.emptyList());
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockTaskExecutor).execute(any(Runnable.class));
        when(mockItemRpcService.findSkusByItemName(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Run the test
        final boolean result = hsdDepositServiceImplUnderTest.getDeposit(depositGetReqDTO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm IHsdOperationService.createOperationRecord(...).
        final OperationCreateReqDTO operationCreateReqDTO = new OperationCreateReqDTO();
        operationCreateReqDTO.setOperationWay(0);
        operationCreateReqDTO.setUserId("memberGuid");
        operationCreateReqDTO.setOperator("operator");
        operationCreateReqDTO.setRemark("remark");
        verify(mockIHsdOperationService).createOperationRecord(operationCreateReqDTO,
                "6b8ef5d7-f595-446d-b4e6-76c49d4760f8", "2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");

        // Confirm IHsdGoodsService.saveOrUpdateBatch(...).
        final GoodsDO goodsDO2 = new GoodsDO();
        goodsDO2.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO2.setStoreGuid("storeGuid");
        goodsDO2.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        goodsDO2.setGoodsName("goodsName");
        goodsDO2.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO2.setSkuName("skuName");
        goodsDO2.setGoodsUnit("goodsUnit");
        goodsDO2.setGoodsClassify(0);
        goodsDO2.setExpireTime("永久有效");
        goodsDO2.setResidueNum(0);
        goodsDO2.setDepositNum(0);
        final List<GoodsDO> entityList = Arrays.asList(goodsDO2);
        verify(mockIHsdGoodsService).saveOrUpdateBatch(entityList);
        verify(mockSendMessageUtil).sendMessage(any(MessageDTO.class), eq("entGuid"));

        // Confirm IHsdOperationGoodsService.saveBatch(...).
        final OperationGoodsDO operationGoodsDO = new OperationGoodsDO();
        operationGoodsDO.setGuid("6b8ef5d7-f595-446d-b4e6-76c49d4760f8");
        operationGoodsDO.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        operationGoodsDO.setGoodsName("goodsName");
        operationGoodsDO.setSkuName("skuName");
        operationGoodsDO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        operationGoodsDO.setOperatorNum(0);
        operationGoodsDO.setResidueNum(0);
        operationGoodsDO.setDepositGoodGuid("22c512b2-f243-4d96-804b-a894920227c8");
        final List<OperationGoodsDO> entityList1 = Arrays.asList(operationGoodsDO);
        verify(mockIHsdOperationGoodsService).saveBatch(entityList1);
        verify(mockTaskExecutor).execute(any(Runnable.class));
    }

    @Test
    public void testQueryOperationHistory() {
        // Setup
        final OperationHistoryQueryReqDTO operationHistoryQueryReqDTO = new OperationHistoryQueryReqDTO();
        operationHistoryQueryReqDTO.setDepositGuid("depositGuid");

        // Configure IHsdOperationService.queryOperationHistory(...).
        final OperationQueryRespDTO operationQueryRespDTO = new OperationQueryRespDTO();
        operationQueryRespDTO.setOperationTime("operationTime");
        operationQueryRespDTO.setOperationWay(0);
        operationQueryRespDTO.setOperator("operator");
        final GoodsSimpleRespDTO goodsSimpleRespDTO = new GoodsSimpleRespDTO();
        goodsSimpleRespDTO.setGoodsName("goodsName");
        operationQueryRespDTO.setGoodsList(Arrays.asList(goodsSimpleRespDTO));
        final Page<OperationQueryRespDTO> operationQueryRespDTOPage = new Page<>(0L, 0L,
                Arrays.asList(operationQueryRespDTO));
        final OperationHistoryQueryReqDTO operationHistoryQueryReqDTO1 = new OperationHistoryQueryReqDTO();
        operationHistoryQueryReqDTO1.setDepositGuid("depositGuid");
        when(mockIHsdOperationService.queryOperationHistory(operationHistoryQueryReqDTO1))
                .thenReturn(operationQueryRespDTOPage);

        // Run the test
        final Page<OperationQueryRespDTO> result = hsdDepositServiceImplUnderTest.queryOperationHistory(
                operationHistoryQueryReqDTO);

        // Verify the results
    }

    @Test
    public void testQueryGoodsSummary() {
        // Setup
        final DepositQueryReqForWebDTO depositQueryReqForWebDTO = new DepositQueryReqForWebDTO();
        depositQueryReqForWebDTO.setStoreGuid("storeGuid");
        depositQueryReqForWebDTO.setCondition("condition");

        // Configure IHsdGoodsService.queryGoodsSummary(...).
        final GoodsSummaryRespDTO goodsSummaryRespDTO = new GoodsSummaryRespDTO();
        goodsSummaryRespDTO.setGoodsName("goodsName");
        goodsSummaryRespDTO.setSum(0);
        final Page<GoodsSummaryRespDTO> goodsSummaryRespDTOPage = new Page<>(0L, 0L,
                Arrays.asList(goodsSummaryRespDTO));
        final DepositQueryReqForWebDTO depositQueryReqForWebDTO1 = new DepositQueryReqForWebDTO();
        depositQueryReqForWebDTO1.setStoreGuid("storeGuid");
        depositQueryReqForWebDTO1.setCondition("condition");
        when(mockIHsdGoodsService.queryGoodsSummary(depositQueryReqForWebDTO1)).thenReturn(goodsSummaryRespDTOPage);

        // Run the test
        final Page<GoodsSummaryRespDTO> result = hsdDepositServiceImplUnderTest.queryGoodsSummary(
                depositQueryReqForWebDTO);

        // Verify the results
    }

    @Test
    public void testSendExpireRemindMessage() {
        // Setup
        // Configure IHsdRemindService.queryRemindRecord(...).
        final MessageRemindReqDTO messageRemindReqDTO = new MessageRemindReqDTO();
        messageRemindReqDTO.setStoreName("storeName");
        messageRemindReqDTO.setDepositRemind(0);
        messageRemindReqDTO.setGetRemind(0);
        messageRemindReqDTO.setExpireRemind(0);
        messageRemindReqDTO.setAdvanceDays(0);
        when(mockIHsdRemindService.queryRemindRecord("storeGuid")).thenReturn(messageRemindReqDTO);

        // Configure IHsdGoodsService.queryGoodsList(...).
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setStoreGuid("storeGuid");
        goodsDO.setDepositGuid("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setSkuGuid("22c512b2-f243-4d96-804b-a894920227c8");
        goodsDO.setSkuName("skuName");
        goodsDO.setGoodsUnit("goodsUnit");
        goodsDO.setGoodsClassify(0);
        goodsDO.setExpireTime("永久有效");
        goodsDO.setResidueNum(0);
        goodsDO.setDepositNum(0);
        final List<GoodsDO> goodsDOS = Arrays.asList(goodsDO);
        when(mockIHsdGoodsService.queryGoodsList("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf")).thenReturn(goodsDOS);

        // Run the test
        hsdDepositServiceImplUnderTest.sendExpireRemindMessage();

        // Verify the results
        verify(mockSendMessageUtil).sendMessage(any(MessageDTO.class), eq("entGuid"));
    }

    @Test
    public void testSendExpireRemindMessage_IHsdGoodsServiceReturnsNoItems() {
        // Setup
        // Configure IHsdRemindService.queryRemindRecord(...).
        final MessageRemindReqDTO messageRemindReqDTO = new MessageRemindReqDTO();
        messageRemindReqDTO.setStoreName("storeName");
        messageRemindReqDTO.setDepositRemind(0);
        messageRemindReqDTO.setGetRemind(0);
        messageRemindReqDTO.setExpireRemind(0);
        messageRemindReqDTO.setAdvanceDays(0);
        when(mockIHsdRemindService.queryRemindRecord("storeGuid")).thenReturn(messageRemindReqDTO);

        when(mockIHsdGoodsService.queryGoodsList("2ca66b5f-f81e-4c67-9792-2ed35c2acdcf"))
                .thenReturn(Collections.emptyList());

        // Run the test
        hsdDepositServiceImplUnderTest.sendExpireRemindMessage();

        // Verify the results
        verify(mockSendMessageUtil).sendMessage(any(MessageDTO.class), eq("entGuid"));
    }

    @Test
    public void testRemindSet() {
        // Setup
        final MessageRemindReqDTO messageRemindReqDTO = new MessageRemindReqDTO();
        messageRemindReqDTO.setStoreName("storeName");
        messageRemindReqDTO.setDepositRemind(0);
        messageRemindReqDTO.setGetRemind(0);
        messageRemindReqDTO.setExpireRemind(0);
        messageRemindReqDTO.setAdvanceDays(0);

        // Configure IHsdRemindService.createRemindRecord(...).
        final MessageRemindReqDTO messageRemindReqDTO1 = new MessageRemindReqDTO();
        messageRemindReqDTO1.setStoreName("storeName");
        messageRemindReqDTO1.setDepositRemind(0);
        messageRemindReqDTO1.setGetRemind(0);
        messageRemindReqDTO1.setExpireRemind(0);
        messageRemindReqDTO1.setAdvanceDays(0);
        when(mockIHsdRemindService.createRemindRecord(messageRemindReqDTO1)).thenReturn(false);

        // Run the test
        final Boolean result = hsdDepositServiceImplUnderTest.remindSet(messageRemindReqDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemindSet_IHsdRemindServiceReturnsTrue() {
        // Setup
        final MessageRemindReqDTO messageRemindReqDTO = new MessageRemindReqDTO();
        messageRemindReqDTO.setStoreName("storeName");
        messageRemindReqDTO.setDepositRemind(0);
        messageRemindReqDTO.setGetRemind(0);
        messageRemindReqDTO.setExpireRemind(0);
        messageRemindReqDTO.setAdvanceDays(0);

        // Configure IHsdRemindService.createRemindRecord(...).
        final MessageRemindReqDTO messageRemindReqDTO1 = new MessageRemindReqDTO();
        messageRemindReqDTO1.setStoreName("storeName");
        messageRemindReqDTO1.setDepositRemind(0);
        messageRemindReqDTO1.setGetRemind(0);
        messageRemindReqDTO1.setExpireRemind(0);
        messageRemindReqDTO1.setAdvanceDays(0);
        when(mockIHsdRemindService.createRemindRecord(messageRemindReqDTO1)).thenReturn(true);

        // Run the test
        final Boolean result = hsdDepositServiceImplUnderTest.remindSet(messageRemindReqDTO);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testQueryRemind() {
        // Setup
        final MessageRemindReqDTO expectedResult = new MessageRemindReqDTO();
        expectedResult.setStoreName("storeName");
        expectedResult.setDepositRemind(0);
        expectedResult.setGetRemind(0);
        expectedResult.setExpireRemind(0);
        expectedResult.setAdvanceDays(0);

        // Configure IHsdRemindService.queryRemindRecord(...).
        final MessageRemindReqDTO messageRemindReqDTO = new MessageRemindReqDTO();
        messageRemindReqDTO.setStoreName("storeName");
        messageRemindReqDTO.setDepositRemind(0);
        messageRemindReqDTO.setGetRemind(0);
        messageRemindReqDTO.setExpireRemind(0);
        messageRemindReqDTO.setAdvanceDays(0);
        when(mockIHsdRemindService.queryRemindRecord("storeGuid")).thenReturn(messageRemindReqDTO);

        // Run the test
        final MessageRemindReqDTO result = hsdDepositServiceImplUnderTest.queryRemind("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
