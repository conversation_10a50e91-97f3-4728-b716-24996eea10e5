package com.holderzone.holder.saas.store.report.controller;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.report.HolderSaasReportApplication;
import com.holderzone.holder.saas.store.report.util.JsonFileUtil;
import com.holderzone.saas.store.dto.exception.TestException;
import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.RefundDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.UnsupportedEncodingException;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 退款报表
 */
@Slf4j
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@RunWith(SpringRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@SpringBootTest(classes = HolderSaasReportApplication.class)
public class TradeRefundControllerTest {

    private static final String USERINFO = "{\"enterpriseGuid\":\"4895\",\"enterpriseNo\":\"********\",\"enterpriseName\":\"销售报表测试企业1\"," +
            "\"commercialActivities\":\"\",\"storeGuid\":\"4908\",\"storeNo\":\"2976518\",\"storeName\":\"会员门店1\",\"deviceGuid\":\"2304251132214660007\"," +
            "\"userGuid\":\"8645\",\"name\":\"孙悟空\",\"tel\":\"***********\",\"account\":\"697124\",\"allianceId\":null,\"isAlliance\":false," +
            "\"operSubjectGuid\":\"2209191214425540006\",\"multiMemberStatus\":false}";

    private static final String RESPONSE = "response:";

    private static final String TRADE_REFUND = "/trade/refund";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    /**
     * 退款明细报表查询
     */
    @Test
    public void list() throws UnsupportedEncodingException {
        ReportQueryVO reportQueryVO = JSON.parseObject(JsonFileUtil.read("tradeRefund/tradeRefund.json"),
                ReportQueryVO.class);
        String reportQueryJsonStr = JSON.toJSONString(reportQueryVO);
        MvcResult mvcTradeRefundResult = null;
        try {
            mvcTradeRefundResult = mockMvc.perform(post(TRADE_REFUND + "/list")
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(reportQueryJsonStr))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String content = mvcTradeRefundResult.getResponse().getContentAsString();
        log.info(RESPONSE + content);
        // 结果
        Message<RefundDetailDTO> message = (Message<RefundDetailDTO>) JacksonUtils.toObject(Message.class, content);
        log.info("合计：{}", JacksonUtils.writeValueAsString(message.getData()));
        log.info("列表：{}", JacksonUtils.writeValueAsString(message.getList()));
    }


    /**
     * 退款明细报表查询导出
     */
    @Test
    public void export() throws UnsupportedEncodingException {
        ReportQueryVO reportQueryVO = JSON.parseObject(JsonFileUtil.read("tradeRefund/tradeRefund.json"),
                ReportQueryVO.class);
        String reportQueryJsonStr = JSON.toJSONString(reportQueryVO);
        MvcResult mvcTradeRefundExportResult = null;
        try {
            mvcTradeRefundExportResult = mockMvc.perform(post(TRADE_REFUND + "/export")
                    .header(USER_INFO, USERINFO)
                    .accept(MediaType.APPLICATION_JSON_VALUE)
                    .contentType(MediaType.APPLICATION_JSON).content(reportQueryJsonStr))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String content = mvcTradeRefundExportResult.getResponse().getContentAsString();
        log.info("导出excel地址:{}", content);
    }

}