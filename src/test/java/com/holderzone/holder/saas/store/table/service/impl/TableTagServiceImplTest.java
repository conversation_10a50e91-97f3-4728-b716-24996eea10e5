package com.holderzone.holder.saas.store.table.service.impl;

import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.table.service.RedisService;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import com.holderzone.saas.store.dto.table.TableTagDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TableTagServiceImplTest {

    @Mock
    private RedisService mockRedisService;

    private TableTagServiceImpl tableTagServiceImplUnderTest;

    @Before
    public void setUp() {
        tableTagServiceImplUnderTest = new TableTagServiceImpl(mockRedisService);
    }

    @Test
    public void testCreateTag() {
        // Setup
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableTagDTO.setStoreGuid("storeGuid");
        tableTagDTO.setGuid("284a86bd-0849-4f76-8244-5eb2c6359dd3");
        tableTagDTO.setTagName("tagName");
        tableTagDTO.setSort(0);

        when(mockRedisService.singleGuid("hst_table_tag")).thenReturn("284a86bd-0849-4f76-8244-5eb2c6359dd3");

        // Run the test
        final boolean result = tableTagServiceImplUnderTest.createTag(tableTagDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testHasSameNameTag() {
        // Setup
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableTagDTO.setStoreGuid("storeGuid");
        tableTagDTO.setGuid("284a86bd-0849-4f76-8244-5eb2c6359dd3");
        tableTagDTO.setTagName("tagName");
        tableTagDTO.setSort(0);

        // Run the test
        final boolean result = tableTagServiceImplUnderTest.hasSameNameTag(tableTagDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testListTag() {
        // Setup
        final BasePageDTO basePageDTO = new BasePageDTO();
        basePageDTO.setStoreGuid("storeGuid");
        basePageDTO.setCurrentPage(0);
        basePageDTO.setPageSize(0);
        basePageDTO.setMaxId(0L);

        // Run the test
        final Page<TableTagDTO> result = tableTagServiceImplUnderTest.listTag(basePageDTO);

        // Verify the results
    }

    @Test
    public void testUpdateTag() {
        // Setup
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableTagDTO.setStoreGuid("storeGuid");
        tableTagDTO.setGuid("284a86bd-0849-4f76-8244-5eb2c6359dd3");
        tableTagDTO.setTagName("tagName");
        tableTagDTO.setSort(0);

        // Run the test
        final boolean result = tableTagServiceImplUnderTest.updateTag(tableTagDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testDeleteTag() {
        // Setup
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableTagDTO.setStoreGuid("storeGuid");
        tableTagDTO.setGuid("284a86bd-0849-4f76-8244-5eb2c6359dd3");
        tableTagDTO.setTagName("tagName");
        tableTagDTO.setSort(0);

        // Run the test
        final boolean result = tableTagServiceImplUnderTest.deleteTag(tableTagDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testGetTagWithGuidMap() {
        // Setup
        final Map<String, TableTagDTO> expectedResult = new HashMap<>();

        // Run the test
        final Map<String, TableTagDTO> result = tableTagServiceImplUnderTest.getTagWithGuidMap(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
