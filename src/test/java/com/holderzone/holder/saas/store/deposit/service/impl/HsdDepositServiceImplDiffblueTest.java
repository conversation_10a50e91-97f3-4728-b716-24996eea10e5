package com.holderzone.holder.saas.store.deposit.service.impl;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.store.deposit.mapper.HsdDepositMapper;
import com.holderzone.holder.saas.store.deposit.mapstruct.GoodsMapstruct;
import com.holderzone.holder.saas.store.deposit.mapstruct.GoodsMapstructImpl;
import com.holderzone.holder.saas.store.deposit.mapstruct.OperationMapstructImpl;
import com.holderzone.holder.saas.store.deposit.mapstruct.RemindMapstructImpl;
import com.holderzone.holder.saas.store.deposit.service.DistributedIdService;
import com.holderzone.holder.saas.store.deposit.service.IHsdGoodsService;
import com.holderzone.holder.saas.store.deposit.service.IHsdOperationGoodsService;
import com.holderzone.holder.saas.store.deposit.service.IHsdOperationService;
import com.holderzone.holder.saas.store.deposit.service.IHsdRemindService;
import com.holderzone.holder.saas.store.deposit.service.rpc.ItemRpcService;
import com.holderzone.holder.saas.store.deposit.service.rpc.MemberRpcService;
import com.holderzone.holder.saas.store.deposit.service.rpc.StoreRpcService;
import com.holderzone.holder.saas.store.deposit.util.GenerateDepositOrderID;
import com.holderzone.holder.saas.store.deposit.util.SendMessageUtil;
import com.holderzone.saas.store.dto.deposit.req.DepositCreateReqDTO;
import com.holderzone.saas.store.dto.deposit.req.DepositGetReqDTO;
import com.holderzone.saas.store.dto.deposit.req.QueryDepositDetailReqDTO;
import com.holderzone.saas.store.dto.deposit.resp.GoodsRespDTO;

import java.util.ArrayList;
import java.util.List;

import org.junit.Ignore;

import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@ContextConfiguration(classes = {HsdDepositServiceImpl.class, ThreadPoolTaskExecutor.class})
@RunWith(SpringJUnit4ClassRunner.class)
public class HsdDepositServiceImplDiffblueTest {
    @MockBean
    private DistributedIdService distributedIdService;

    @MockBean
    private GenerateDepositOrderID generateDepositOrderID;

    @MockBean
    private GoodsMapstruct goodsMapstruct;

    @MockBean
    private HsdDepositMapper hsdDepositMapper;

    @Autowired
    private HsdDepositServiceImpl hsdDepositServiceImpl;

    @MockBean
    private IHsdGoodsService iHsdGoodsService;

    @MockBean
    private IHsdOperationGoodsService iHsdOperationGoodsService;

    @MockBean
    private IHsdOperationService iHsdOperationService;

    @MockBean
    private IHsdRemindService iHsdRemindService;

    @MockBean
    private ItemRpcService itemRpcService;

    @MockBean
    private MemberRpcService memberRpcService;

    @MockBean
    private SendMessageUtil sendMessageUtil;

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    /**
     * Method under test:
     * {@link HsdDepositServiceImpl#createDepositRecord(DepositCreateReqDTO)}
     */
    @Test
    public void testCreateDepositRecord() {
        //   Diffblue Cover was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R027 Missing beans when creating Spring context.
        //   Failed to create Spring context due to missing beans
        //   in the current Spring profile:
        //   See https://diff.blue/R027 to resolve this issue.

        ItemRpcService itemRpcService = mock(ItemRpcService.class);
        DistributedIdServiceImpl distributedIdService = new DistributedIdServiceImpl(new RedisTemplate());
        ItemRpcService itemRpcService2 = mock(ItemRpcService.class);
        DistributedIdServiceImpl distributedIdService2 = new DistributedIdServiceImpl(new RedisTemplate());
        HsdGoodsServiceImpl iHsdGoodsService = new HsdGoodsServiceImpl(itemRpcService2, distributedIdService2,
                new GoodsMapstructImpl());

        HsdOperationGoodsServiceImpl iHsdOperationGoodsService = new HsdOperationGoodsServiceImpl(
                new OperationMapstructImpl());
        DistributedIdServiceImpl distributedIdService3 = new DistributedIdServiceImpl(new RedisTemplate());
        HsdOperationServiceImpl iHsdOperationService = new HsdOperationServiceImpl(iHsdOperationGoodsService,
                distributedIdService3, new OperationMapstructImpl());

        HsdOperationGoodsServiceImpl iHsdOperationGoodsService2 = new HsdOperationGoodsServiceImpl(
                new OperationMapstructImpl());
        MemberRpcService memberRpcService = mock(MemberRpcService.class);
        DistributedIdServiceImpl distributedIdService4 = new DistributedIdServiceImpl(new RedisTemplate());
        StoreRpcService storeRpcService = mock(StoreRpcService.class);
        HsdRemindServiceImpl iHsdRemindService = new HsdRemindServiceImpl(distributedIdService4, storeRpcService,
                new RemindMapstructImpl());

        GenerateDepositOrderID generateDepositOrderID = new GenerateDepositOrderID(new StringRedisTemplate());
        HsdDepositServiceImpl hsdDepositServiceImpl = new HsdDepositServiceImpl(itemRpcService, distributedIdService,
                iHsdGoodsService, iHsdOperationService, iHsdOperationGoodsService2, memberRpcService, iHsdRemindService,
                generateDepositOrderID, new GoodsMapstructImpl());

        DepositCreateReqDTO depositCreateReqDTO = new DepositCreateReqDTO();
        depositCreateReqDTO.setCustomerName("Customer Name");
        depositCreateReqDTO.setGoods(new ArrayList<>());
        depositCreateReqDTO.setHeadPortrait("Head Portrait");
        depositCreateReqDTO.setMemberGuid("1234");
        depositCreateReqDTO.setPhoneNum("6625550144");
        depositCreateReqDTO.setRemark("Remark");
        depositCreateReqDTO.setStoreGuid("1234");
        thrown.expect(BusinessException.class);
        hsdDepositServiceImpl.createDepositRecord(depositCreateReqDTO);
    }

    /**
     * Method under test:
     * {@link HsdDepositServiceImpl#createDepositRecord(DepositCreateReqDTO)}
     */
    @Test
    public void testCreateDepositRecord2() {
        //   Diffblue Cover was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R027 Missing beans when creating Spring context.
        //   Failed to create Spring context due to missing beans
        //   in the current Spring profile:
        //   See https://diff.blue/R027 to resolve this issue.

        ItemRpcService itemRpcService = mock(ItemRpcService.class);
        DistributedIdServiceImpl distributedIdService = new DistributedIdServiceImpl(new RedisTemplate());
        ItemRpcService itemRpcService2 = mock(ItemRpcService.class);
        DistributedIdServiceImpl distributedIdService2 = new DistributedIdServiceImpl(new RedisTemplate());
        HsdGoodsServiceImpl iHsdGoodsService = new HsdGoodsServiceImpl(itemRpcService2, distributedIdService2,
                new GoodsMapstructImpl());

        HsdOperationGoodsServiceImpl iHsdOperationGoodsService = new HsdOperationGoodsServiceImpl(
                new OperationMapstructImpl());
        DistributedIdServiceImpl distributedIdService3 = new DistributedIdServiceImpl(new RedisTemplate());
        HsdOperationServiceImpl iHsdOperationService = new HsdOperationServiceImpl(iHsdOperationGoodsService,
                distributedIdService3, new OperationMapstructImpl());

        HsdOperationGoodsServiceImpl iHsdOperationGoodsService2 = new HsdOperationGoodsServiceImpl(
                new OperationMapstructImpl());
        MemberRpcService memberRpcService = mock(MemberRpcService.class);
        DistributedIdServiceImpl distributedIdService4 = new DistributedIdServiceImpl(new RedisTemplate());
        StoreRpcService storeRpcService = mock(StoreRpcService.class);
        HsdRemindServiceImpl iHsdRemindService = new HsdRemindServiceImpl(distributedIdService4, storeRpcService,
                new RemindMapstructImpl());

        GenerateDepositOrderID generateDepositOrderID = new GenerateDepositOrderID(new StringRedisTemplate());
        HsdDepositServiceImpl hsdDepositServiceImpl = new HsdDepositServiceImpl(itemRpcService, distributedIdService,
                iHsdGoodsService, iHsdOperationService, iHsdOperationGoodsService2, memberRpcService, iHsdRemindService,
                generateDepositOrderID, new GoodsMapstructImpl());

        DepositCreateReqDTO depositCreateReqDTO = new DepositCreateReqDTO();
        depositCreateReqDTO.setCustomerName("Customer Name");
        depositCreateReqDTO.setHeadPortrait("Head Portrait");
        depositCreateReqDTO.setPhoneNum("6625550144");
        depositCreateReqDTO.setRemark("Remark");
        depositCreateReqDTO.setGoods(null);
        depositCreateReqDTO.setStoreGuid(null);
        depositCreateReqDTO.setMemberGuid(null);
        thrown.expect(BusinessException.class);
        hsdDepositServiceImpl.createDepositRecord(depositCreateReqDTO);
    }

    /**
     * Method under test:
     * {@link HsdDepositServiceImpl#createDepositRecord(DepositCreateReqDTO)}
     */
    @Test
    public void testCreateDepositRecord3() {
        //   Diffblue Cover was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R027 Missing beans when creating Spring context.
        //   Failed to create Spring context due to missing beans
        //   in the current Spring profile:
        //   See https://diff.blue/R027 to resolve this issue.

        ItemRpcService itemRpcService = mock(ItemRpcService.class);
        DistributedIdServiceImpl distributedIdService = new DistributedIdServiceImpl(new RedisTemplate());
        ItemRpcService itemRpcService2 = mock(ItemRpcService.class);
        DistributedIdServiceImpl distributedIdService2 = new DistributedIdServiceImpl(new RedisTemplate());
        HsdGoodsServiceImpl iHsdGoodsService = new HsdGoodsServiceImpl(itemRpcService2, distributedIdService2,
                new GoodsMapstructImpl());

        HsdOperationGoodsServiceImpl iHsdOperationGoodsService = new HsdOperationGoodsServiceImpl(
                new OperationMapstructImpl());
        DistributedIdServiceImpl distributedIdService3 = new DistributedIdServiceImpl(new RedisTemplate());
        HsdOperationServiceImpl iHsdOperationService = new HsdOperationServiceImpl(iHsdOperationGoodsService,
                distributedIdService3, new OperationMapstructImpl());

        HsdOperationGoodsServiceImpl iHsdOperationGoodsService2 = new HsdOperationGoodsServiceImpl(
                new OperationMapstructImpl());
        MemberRpcService memberRpcService = mock(MemberRpcService.class);
        DistributedIdServiceImpl distributedIdService4 = new DistributedIdServiceImpl(new RedisTemplate());
        StoreRpcService storeRpcService = mock(StoreRpcService.class);
        HsdRemindServiceImpl iHsdRemindService = new HsdRemindServiceImpl(distributedIdService4, storeRpcService,
                new RemindMapstructImpl());

        GenerateDepositOrderID generateDepositOrderID = new GenerateDepositOrderID(new StringRedisTemplate());
        HsdDepositServiceImpl hsdDepositServiceImpl = new HsdDepositServiceImpl(itemRpcService, distributedIdService,
                iHsdGoodsService, iHsdOperationService, iHsdOperationGoodsService2, memberRpcService, iHsdRemindService,
                generateDepositOrderID, new GoodsMapstructImpl());

        DepositCreateReqDTO depositCreateReqDTO = new DepositCreateReqDTO();
        depositCreateReqDTO.setCustomerName("Customer Name");
        depositCreateReqDTO.setHeadPortrait("Head Portrait");
        depositCreateReqDTO.setPhoneNum("6625550144");
        depositCreateReqDTO.setRemark("Remark");
        depositCreateReqDTO.setGoods(null);
        depositCreateReqDTO.setStoreGuid((String) "");
        depositCreateReqDTO.setMemberGuid(null);
        thrown.expect(BusinessException.class);
        hsdDepositServiceImpl.createDepositRecord(depositCreateReqDTO);
    }

    /**
     * Method under test:
     * {@link HsdDepositServiceImpl#createDepositRecord(DepositCreateReqDTO)}
     */
    @Test
    public void testCreateDepositRecord4() {
        //   Diffblue Cover was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R027 Missing beans when creating Spring context.
        //   Failed to create Spring context due to missing beans
        //   in the current Spring profile:
        //   See https://diff.blue/R027 to resolve this issue.

        ItemRpcService itemRpcService = mock(ItemRpcService.class);
        DistributedIdServiceImpl distributedIdService = new DistributedIdServiceImpl(new RedisTemplate());
        ItemRpcService itemRpcService2 = mock(ItemRpcService.class);
        DistributedIdServiceImpl distributedIdService2 = new DistributedIdServiceImpl(new RedisTemplate());
        HsdGoodsServiceImpl iHsdGoodsService = new HsdGoodsServiceImpl(itemRpcService2, distributedIdService2,
                new GoodsMapstructImpl());

        HsdOperationGoodsServiceImpl iHsdOperationGoodsService = new HsdOperationGoodsServiceImpl(
                new OperationMapstructImpl());
        DistributedIdServiceImpl distributedIdService3 = new DistributedIdServiceImpl(new RedisTemplate());
        HsdOperationServiceImpl iHsdOperationService = new HsdOperationServiceImpl(iHsdOperationGoodsService,
                distributedIdService3, new OperationMapstructImpl());

        HsdOperationGoodsServiceImpl iHsdOperationGoodsService2 = new HsdOperationGoodsServiceImpl(
                new OperationMapstructImpl());
        MemberRpcService memberRpcService = mock(MemberRpcService.class);
        DistributedIdServiceImpl distributedIdService4 = new DistributedIdServiceImpl(new RedisTemplate());
        StoreRpcService storeRpcService = mock(StoreRpcService.class);
        HsdRemindServiceImpl iHsdRemindService = new HsdRemindServiceImpl(distributedIdService4, storeRpcService,
                new RemindMapstructImpl());

        GenerateDepositOrderID generateDepositOrderID = new GenerateDepositOrderID(new StringRedisTemplate());
        HsdDepositServiceImpl hsdDepositServiceImpl = new HsdDepositServiceImpl(itemRpcService, distributedIdService,
                iHsdGoodsService, iHsdOperationService, iHsdOperationGoodsService2, memberRpcService, iHsdRemindService,
                generateDepositOrderID, new GoodsMapstructImpl());

        DepositCreateReqDTO depositCreateReqDTO = new DepositCreateReqDTO();
        depositCreateReqDTO.setCustomerName("Customer Name");
        depositCreateReqDTO.setHeadPortrait("Head Portrait");
        depositCreateReqDTO.setPhoneNum("6625550144");
        depositCreateReqDTO.setRemark("Remark");
        depositCreateReqDTO.setGoods(null);
        depositCreateReqDTO.setStoreGuid("foo");
        depositCreateReqDTO.setMemberGuid(null);
        thrown.expect(BusinessException.class);
        hsdDepositServiceImpl.createDepositRecord(depositCreateReqDTO);
    }

    /**
     * Method under test:
     * {@link HsdDepositServiceImpl#createDepositRecord(DepositCreateReqDTO)}
     */
    @Test
    public void testCreateDepositRecord5() {
        //   Diffblue Cover was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R027 Missing beans when creating Spring context.
        //   Failed to create Spring context due to missing beans
        //   in the current Spring profile:
        //   See https://diff.blue/R027 to resolve this issue.

        ItemRpcService itemRpcService = mock(ItemRpcService.class);
        DistributedIdServiceImpl distributedIdService = new DistributedIdServiceImpl(new RedisTemplate());
        ItemRpcService itemRpcService2 = mock(ItemRpcService.class);
        DistributedIdServiceImpl distributedIdService2 = new DistributedIdServiceImpl(new RedisTemplate());
        HsdGoodsServiceImpl iHsdGoodsService = new HsdGoodsServiceImpl(itemRpcService2, distributedIdService2,
                new GoodsMapstructImpl());

        HsdOperationGoodsServiceImpl iHsdOperationGoodsService = new HsdOperationGoodsServiceImpl(
                new OperationMapstructImpl());
        DistributedIdServiceImpl distributedIdService3 = new DistributedIdServiceImpl(new RedisTemplate());
        HsdOperationServiceImpl iHsdOperationService = new HsdOperationServiceImpl(iHsdOperationGoodsService,
                distributedIdService3, new OperationMapstructImpl());

        HsdOperationGoodsServiceImpl iHsdOperationGoodsService2 = new HsdOperationGoodsServiceImpl(
                new OperationMapstructImpl());
        MemberRpcService memberRpcService = mock(MemberRpcService.class);
        DistributedIdServiceImpl distributedIdService4 = new DistributedIdServiceImpl(new RedisTemplate());
        StoreRpcService storeRpcService = mock(StoreRpcService.class);
        HsdRemindServiceImpl iHsdRemindService = new HsdRemindServiceImpl(distributedIdService4, storeRpcService,
                new RemindMapstructImpl());

        GenerateDepositOrderID generateDepositOrderID = new GenerateDepositOrderID(new StringRedisTemplate());
        HsdDepositServiceImpl hsdDepositServiceImpl = new HsdDepositServiceImpl(itemRpcService, distributedIdService,
                iHsdGoodsService, iHsdOperationService, iHsdOperationGoodsService2, memberRpcService, iHsdRemindService,
                generateDepositOrderID, new GoodsMapstructImpl());

        DepositCreateReqDTO depositCreateReqDTO = new DepositCreateReqDTO();
        depositCreateReqDTO.setCustomerName("Customer Name");
        depositCreateReqDTO.setHeadPortrait("Head Portrait");
        depositCreateReqDTO.setPhoneNum("6625550144");
        depositCreateReqDTO.setRemark("Remark");
        depositCreateReqDTO.setGoods(null);
        depositCreateReqDTO.setStoreGuid("foo");
        depositCreateReqDTO.setMemberGuid("foo");
        thrown.expect(BusinessException.class);
        hsdDepositServiceImpl.createDepositRecord(depositCreateReqDTO);
    }

    /**
     * Method under test:
     * {@link HsdDepositServiceImpl#queryDepositDetail(QueryDepositDetailReqDTO)}
     */
    @Test
    public void testQueryDepositDetail() {
        //   Diffblue Cover was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R027 Missing beans when creating Spring context.
        //   Failed to create Spring context due to missing beans
        //   in the current Spring profile:
        //   See https://diff.blue/R027 to resolve this issue.

        IHsdGoodsService iHsdGoodsService = mock(IHsdGoodsService.class);
        when(iHsdGoodsService.queryGoodsList(Mockito.<String>any())).thenReturn(new ArrayList<>());
        ItemRpcService itemRpcService = mock(ItemRpcService.class);
        DistributedIdServiceImpl distributedIdService = new DistributedIdServiceImpl(new RedisTemplate());
        HsdOperationGoodsServiceImpl iHsdOperationGoodsService = new HsdOperationGoodsServiceImpl(
                new OperationMapstructImpl());
        DistributedIdServiceImpl distributedIdService2 = new DistributedIdServiceImpl(new RedisTemplate());
        HsdOperationServiceImpl iHsdOperationService = new HsdOperationServiceImpl(iHsdOperationGoodsService,
                distributedIdService2, new OperationMapstructImpl());

        HsdOperationGoodsServiceImpl iHsdOperationGoodsService2 = new HsdOperationGoodsServiceImpl(
                new OperationMapstructImpl());
        MemberRpcService memberRpcService = mock(MemberRpcService.class);
        DistributedIdServiceImpl distributedIdService3 = new DistributedIdServiceImpl(new RedisTemplate());
        StoreRpcService storeRpcService = mock(StoreRpcService.class);
        HsdRemindServiceImpl iHsdRemindService = new HsdRemindServiceImpl(distributedIdService3, storeRpcService,
                new RemindMapstructImpl());

        GenerateDepositOrderID generateDepositOrderID = new GenerateDepositOrderID(new StringRedisTemplate());
        HsdDepositServiceImpl hsdDepositServiceImpl = new HsdDepositServiceImpl(itemRpcService, distributedIdService,
                iHsdGoodsService, iHsdOperationService, iHsdOperationGoodsService2, memberRpcService, iHsdRemindService,
                generateDepositOrderID, new GoodsMapstructImpl());

        QueryDepositDetailReqDTO depositQueryReqDTO = new QueryDepositDetailReqDTO();
        depositQueryReqDTO.setDepositGuid("1234");
        List<GoodsRespDTO> actualQueryDepositDetailResult = hsdDepositServiceImpl.queryDepositDetail(depositQueryReqDTO);
        verify(iHsdGoodsService).queryGoodsList(Mockito.<String>any());
        assertTrue(actualQueryDepositDetailResult.isEmpty());
    }

    /**
     * Method under test:
     * {@link HsdDepositServiceImpl#queryDepositDetailForPos(QueryDepositDetailReqDTO)}
     */
    @Test
    @Ignore("TODO: Complete this test")
    public void testQueryDepositDetailForPos() {
        // TODO: Complete this test.
        //   Reason: R027 Missing beans when creating Spring context.
        //   Failed to create Spring context due to missing beans
        //   in the current Spring profile:
        //   See https://diff.blue/R027 to resolve this issue.

        QueryDepositDetailReqDTO depositQueryReqDTO = new QueryDepositDetailReqDTO();
        depositQueryReqDTO.setDepositGuid("1234");
        hsdDepositServiceImpl.queryDepositDetailForPos(depositQueryReqDTO);
    }

    /**
     * Method under test: {@link HsdDepositServiceImpl#getDeposit(DepositGetReqDTO)}
     */
    @Test
    public void testGetDeposit() {
        //   Diffblue Cover was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R027 Missing beans when creating Spring context.
        //   Failed to create Spring context due to missing beans
        //   in the current Spring profile:
        //   See https://diff.blue/R027 to resolve this issue.

        ItemRpcService itemRpcService = mock(ItemRpcService.class);
        DistributedIdServiceImpl distributedIdService = new DistributedIdServiceImpl(new RedisTemplate());
        ItemRpcService itemRpcService2 = mock(ItemRpcService.class);
        DistributedIdServiceImpl distributedIdService2 = new DistributedIdServiceImpl(new RedisTemplate());
        HsdGoodsServiceImpl iHsdGoodsService = new HsdGoodsServiceImpl(itemRpcService2, distributedIdService2,
                new GoodsMapstructImpl());

        HsdOperationGoodsServiceImpl iHsdOperationGoodsService = new HsdOperationGoodsServiceImpl(
                new OperationMapstructImpl());
        DistributedIdServiceImpl distributedIdService3 = new DistributedIdServiceImpl(new RedisTemplate());
        HsdOperationServiceImpl iHsdOperationService = new HsdOperationServiceImpl(iHsdOperationGoodsService,
                distributedIdService3, new OperationMapstructImpl());

        HsdOperationGoodsServiceImpl iHsdOperationGoodsService2 = new HsdOperationGoodsServiceImpl(
                new OperationMapstructImpl());
        MemberRpcService memberRpcService = mock(MemberRpcService.class);
        DistributedIdServiceImpl distributedIdService4 = new DistributedIdServiceImpl(new RedisTemplate());
        StoreRpcService storeRpcService = mock(StoreRpcService.class);
        HsdRemindServiceImpl iHsdRemindService = new HsdRemindServiceImpl(distributedIdService4, storeRpcService,
                new RemindMapstructImpl());

        GenerateDepositOrderID generateDepositOrderID = new GenerateDepositOrderID(new StringRedisTemplate());
        HsdDepositServiceImpl hsdDepositServiceImpl = new HsdDepositServiceImpl(itemRpcService, distributedIdService,
                iHsdGoodsService, iHsdOperationService, iHsdOperationGoodsService2, memberRpcService, iHsdRemindService,
                generateDepositOrderID, new GoodsMapstructImpl());

        GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
        goodsRespDTO.setDepositNum(10);
        goodsRespDTO.setExpireTime("Expire Time");
        goodsRespDTO.setGoodsName("Goods Name");
        goodsRespDTO.setGoodsUnit("Goods Unit");
        goodsRespDTO.setGuid("1234");
        goodsRespDTO.setResidueDay(1);
        goodsRespDTO.setResidueNum(1);
        goodsRespDTO.setSkuGuid("1234");
        goodsRespDTO.setSkuName("Sku Name");
        goodsRespDTO.setStorePosition("Store Position");
        goodsRespDTO.setTakeOutNum(0);

        ArrayList<GoodsRespDTO> goodsList = new ArrayList<>();
        goodsList.add(goodsRespDTO);

        DepositGetReqDTO depositGetReqDTO = new DepositGetReqDTO();
        depositGetReqDTO.setRemark("Remark");
        depositGetReqDTO.setUserGuid("1234");
        depositGetReqDTO.setDepositGuid(null);
        depositGetReqDTO.setGoodsList(goodsList);
        thrown.expect(BusinessException.class);
        hsdDepositServiceImpl.getDeposit(depositGetReqDTO);
    }

    /**
     * Method under test: {@link HsdDepositServiceImpl#getDeposit(DepositGetReqDTO)}
     */
    @Test
    public void testGetDeposit2() {
        //   Diffblue Cover was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R027 Missing beans when creating Spring context.
        //   Failed to create Spring context due to missing beans
        //   in the current Spring profile:
        //   See https://diff.blue/R027 to resolve this issue.

        ItemRpcService itemRpcService = mock(ItemRpcService.class);
        DistributedIdServiceImpl distributedIdService = new DistributedIdServiceImpl(new RedisTemplate());
        ItemRpcService itemRpcService2 = mock(ItemRpcService.class);
        DistributedIdServiceImpl distributedIdService2 = new DistributedIdServiceImpl(new RedisTemplate());
        HsdGoodsServiceImpl iHsdGoodsService = new HsdGoodsServiceImpl(itemRpcService2, distributedIdService2,
                new GoodsMapstructImpl());

        HsdOperationGoodsServiceImpl iHsdOperationGoodsService = new HsdOperationGoodsServiceImpl(
                new OperationMapstructImpl());
        DistributedIdServiceImpl distributedIdService3 = new DistributedIdServiceImpl(new RedisTemplate());
        HsdOperationServiceImpl iHsdOperationService = new HsdOperationServiceImpl(iHsdOperationGoodsService,
                distributedIdService3, new OperationMapstructImpl());

        HsdOperationGoodsServiceImpl iHsdOperationGoodsService2 = new HsdOperationGoodsServiceImpl(
                new OperationMapstructImpl());
        MemberRpcService memberRpcService = mock(MemberRpcService.class);
        DistributedIdServiceImpl distributedIdService4 = new DistributedIdServiceImpl(new RedisTemplate());
        StoreRpcService storeRpcService = mock(StoreRpcService.class);
        HsdRemindServiceImpl iHsdRemindService = new HsdRemindServiceImpl(distributedIdService4, storeRpcService,
                new RemindMapstructImpl());

        GenerateDepositOrderID generateDepositOrderID = new GenerateDepositOrderID(new StringRedisTemplate());
        HsdDepositServiceImpl hsdDepositServiceImpl = new HsdDepositServiceImpl(itemRpcService, distributedIdService,
                iHsdGoodsService, iHsdOperationService, iHsdOperationGoodsService2, memberRpcService, iHsdRemindService,
                generateDepositOrderID, new GoodsMapstructImpl());

        GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
        goodsRespDTO.setDepositNum(10);
        goodsRespDTO.setExpireTime("Expire Time");
        goodsRespDTO.setGoodsName("Goods Name");
        goodsRespDTO.setGoodsUnit("Goods Unit");
        goodsRespDTO.setGuid("1234");
        goodsRespDTO.setResidueDay(1);
        goodsRespDTO.setResidueNum(1);
        goodsRespDTO.setSkuGuid("1234");
        goodsRespDTO.setSkuName("Sku Name");
        goodsRespDTO.setStorePosition("Store Position");
        goodsRespDTO.setTakeOutNum(0);

        ArrayList<GoodsRespDTO> goodsList = new ArrayList<>();
        goodsList.add(goodsRespDTO);

        DepositGetReqDTO depositGetReqDTO = new DepositGetReqDTO();
        depositGetReqDTO.setRemark("Remark");
        depositGetReqDTO.setUserGuid("1234");
        depositGetReqDTO.setDepositGuid((String) "");
        depositGetReqDTO.setGoodsList(goodsList);
        thrown.expect(BusinessException.class);
        hsdDepositServiceImpl.getDeposit(depositGetReqDTO);
    }
}
