package com.holderzone.holder.saas.store.report.service.impl;

import com.holderzone.holder.saas.store.report.mapper.TradeOrderMapper;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.OrderItemTypeRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TradeOrderServiceImplTest {

    @Mock
    private TradeOrderMapper mockTradeOrderMapper;

    private TradeOrderServiceImpl tradeOrderServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        tradeOrderServiceImplUnderTest = new TradeOrderServiceImpl(mockTradeOrderMapper);
    }

    @Test
    public void testQueryItemTypes() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        final OrderItemTypeRespDTO orderItemTypeRespDTO = new OrderItemTypeRespDTO();
        orderItemTypeRespDTO.setItemType("itemType");
        orderItemTypeRespDTO.setItemName("itemName");
        final List<OrderItemTypeRespDTO> expectedResult = Arrays.asList(orderItemTypeRespDTO);

        // Configure TradeOrderMapper.queryItemTypes(...).
        final OrderItemTypeRespDTO orderItemTypeRespDTO1 = new OrderItemTypeRespDTO();
        orderItemTypeRespDTO1.setItemType("itemType");
        orderItemTypeRespDTO1.setItemName("itemName");
        final List<OrderItemTypeRespDTO> orderItemTypeRespDTOS = Arrays.asList(orderItemTypeRespDTO1);
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeOrderMapper.queryItemTypes(query1)).thenReturn(orderItemTypeRespDTOS);

        // Run the test
        final List<OrderItemTypeRespDTO> result = tradeOrderServiceImplUnderTest.queryItemTypes(query);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryItemTypes_TradeOrderMapperReturnsNoItems() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeOrderMapper.queryItemTypes(...).
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeOrderMapper.queryItemTypes(query1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderItemTypeRespDTO> result = tradeOrderServiceImplUnderTest.queryItemTypes(query);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryItemCategories() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeOrderMapper.queryItemCategories(...).
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeOrderMapper.queryItemCategories(query1)).thenReturn(Arrays.asList("value"));

        // Run the test
        final List<String> result = tradeOrderServiceImplUnderTest.queryItemCategories(query);

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
    }

    @Test
    public void testQueryItemCategories_TradeOrderMapperReturnsNoItems() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeOrderMapper.queryItemCategories(...).
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeOrderMapper.queryItemCategories(query1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = tradeOrderServiceImplUnderTest.queryItemCategories(query);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
