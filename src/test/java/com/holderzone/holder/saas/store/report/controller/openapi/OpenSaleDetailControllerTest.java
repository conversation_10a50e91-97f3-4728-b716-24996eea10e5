package com.holderzone.holder.saas.store.report.controller.openapi;

import com.holderzone.holder.saas.store.report.service.OpenSaleDetailService;
import com.holderzone.saas.store.dto.report.openapi.SaleDetailLimitRespDTO;
import com.holderzone.saas.store.dto.report.openapi.SaleDetailQueryDTO;
import com.holderzone.saas.store.dto.report.openapi.SaleDetailRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(OpenSaleDetailController.class)
public class OpenSaleDetailControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private OpenSaleDetailService mockOpenSaleDetailService;

    @Test
    public void testQuery() throws Exception {
        // Setup
        // Configure OpenSaleDetailService.query(...).
        final SaleDetailLimitRespDTO saleDetailLimitRespDTO = new SaleDetailLimitRespDTO();
        final SaleDetailRespDTO saleDetailRespDTO = new SaleDetailRespDTO();
        saleDetailRespDTO.setOrderGuid("orderGuid");
        saleDetailRespDTO.setStoreGuId("storeGuId");
        saleDetailRespDTO.setStoreName("storeName");
        saleDetailRespDTO.setPosNO("posNO");
        saleDetailLimitRespDTO.setList(Arrays.asList(saleDetailRespDTO));
        final SaleDetailQueryDTO saleDetailQueryDTO = new SaleDetailQueryDTO();
        saleDetailQueryDTO.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        saleDetailQueryDTO.setCursor(0L);
        saleDetailQueryDTO.setLimit(0);
        saleDetailQueryDTO.setEnterpriseGuid("enterpriseGuid");
        saleDetailQueryDTO.setStoreGuid("storeGuid");
        when(mockOpenSaleDetailService.query(saleDetailQueryDTO)).thenReturn(saleDetailLimitRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/openapi/sale_detail")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
