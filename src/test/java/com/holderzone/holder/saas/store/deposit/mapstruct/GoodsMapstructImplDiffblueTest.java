package com.holderzone.holder.saas.store.deposit.mapstruct;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.holderzone.holder.saas.store.deposit.entity.bo.GoodsDO;
import com.holderzone.saas.store.dto.deposit.resp.GoodsRespDTO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@ContextConfiguration(classes = {GoodsMapstructImpl.class})
@RunWith(SpringJUnit4ClassRunner.class)
public class GoodsMapstructImplDiffblueTest {
    @Autowired
    private GoodsMapstructImpl goodsMapstructImpl;

    /**
     * Method under test: {@link GoodsMapstructImpl#fromGoodsDTO(GoodsRespDTO)}
     */
    @Test
    public void testFromGoodsDTO() {
        GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
        goodsRespDTO.setDepositNum(10);
        goodsRespDTO.setExpireTime("Expire Time");
        goodsRespDTO.setGoodsName("Goods Name");
        goodsRespDTO.setGoodsUnit("Goods Unit");
        goodsRespDTO.setGuid("1234");
        goodsRespDTO.setResidueDay(1);
        goodsRespDTO.setResidueNum(1);
        goodsRespDTO.setSkuGuid("1234");
        goodsRespDTO.setSkuName("Sku Name");
        goodsRespDTO.setStorePosition("Store Position");
        goodsRespDTO.setTakeOutNum(10);
        GoodsDO actualFromGoodsDTOResult = goodsMapstructImpl.fromGoodsDTO(goodsRespDTO);
        assertEquals("1234", actualFromGoodsDTOResult.getGuid());
        assertEquals("1234", actualFromGoodsDTOResult.getSkuGuid());
        assertEquals("Expire Time", actualFromGoodsDTOResult.getExpireTime());
        assertEquals("Goods Name", actualFromGoodsDTOResult.getGoodsName());
        assertEquals("Goods Unit", actualFromGoodsDTOResult.getGoodsUnit());
        assertEquals("Sku Name", actualFromGoodsDTOResult.getSkuName());
        assertEquals("Store Position", actualFromGoodsDTOResult.getStorePosition());
        assertEquals(1, actualFromGoodsDTOResult.getResidueNum());
        assertEquals(10, actualFromGoodsDTOResult.getDepositNum());
    }

    /**
     * Method under test: {@link GoodsMapstructImpl#fromGoodsDTO(GoodsRespDTO)}
     */
    @Test
    public void testFromGoodsDTO2() {
        GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
        goodsRespDTO.setDepositNum(10);
        goodsRespDTO.setExpireTime("Expire Time");
        goodsRespDTO.setGoodsName("Goods Name");
        goodsRespDTO.setGoodsUnit("Goods Unit");
        goodsRespDTO.setGuid("1234");
        goodsRespDTO.setResidueDay(1);
        goodsRespDTO.setResidueNum(1);
        goodsRespDTO.setSkuGuid("1234");
        goodsRespDTO.setSkuName("Sku Name");
        goodsRespDTO.setStorePosition("Store Position");
        goodsRespDTO.setTakeOutNum(10);

        GoodsRespDTO goodsRespDTO2 = new GoodsRespDTO();
        goodsRespDTO2.setDepositNum(10);
        goodsRespDTO2.setExpireTime("Expire Time");
        goodsRespDTO2.setGoodsName("Goods Name");
        goodsRespDTO2.setGoodsUnit("Goods Unit");
        goodsRespDTO2.setGuid("1234");
        goodsRespDTO2.setResidueDay(1);
        goodsRespDTO2.setResidueNum(1);
        goodsRespDTO2.setSkuGuid("1234");
        goodsRespDTO2.setSkuName("Sku Name");
        goodsRespDTO2.setStorePosition("Store Position");
        goodsRespDTO2.setTakeOutNum(10);

        GoodsRespDTO goodsRespDTO3 = new GoodsRespDTO();
        goodsRespDTO3.setDepositNum(10);
        goodsRespDTO3.setExpireTime("Expire Time");
        goodsRespDTO3.setGoodsName("Goods Name");
        goodsRespDTO3.setGoodsUnit("Goods Unit");
        goodsRespDTO3.setGuid("1234");
        goodsRespDTO3.setResidueDay(1);
        goodsRespDTO3.setResidueNum(1);
        goodsRespDTO3.setSkuGuid("1234");
        goodsRespDTO3.setSkuName("Sku Name");
        goodsRespDTO3.setStorePosition("Store Position");
        goodsRespDTO3.setTakeOutNum(10);

        GoodsRespDTO goodsRespDTO4 = new GoodsRespDTO();
        goodsRespDTO4.setDepositNum(10);
        goodsRespDTO4.setExpireTime("Expire Time");
        goodsRespDTO4.setGoodsName("Goods Name");
        goodsRespDTO4.setGoodsUnit("Goods Unit");
        goodsRespDTO4.setGuid("1234");
        goodsRespDTO4.setResidueDay(1);
        goodsRespDTO4.setResidueNum(1);
        goodsRespDTO4.setSkuGuid("1234");
        goodsRespDTO4.setSkuName("Sku Name");
        goodsRespDTO4.setStorePosition("Store Position");
        goodsRespDTO4.setTakeOutNum(10);

        GoodsRespDTO goodsRespDTO5 = new GoodsRespDTO();
        goodsRespDTO5.setDepositNum(10);
        goodsRespDTO5.setExpireTime("Expire Time");
        goodsRespDTO5.setGoodsName("Goods Name");
        goodsRespDTO5.setGoodsUnit("Goods Unit");
        goodsRespDTO5.setGuid("1234");
        goodsRespDTO5.setResidueDay(1);
        goodsRespDTO5.setResidueNum(1);
        goodsRespDTO5.setSkuGuid("1234");
        goodsRespDTO5.setSkuName("Sku Name");
        goodsRespDTO5.setStorePosition("Store Position");
        goodsRespDTO5.setTakeOutNum(10);

        GoodsRespDTO goodsRespDTO6 = new GoodsRespDTO();
        goodsRespDTO6.setDepositNum(10);
        goodsRespDTO6.setExpireTime("Expire Time");
        goodsRespDTO6.setGoodsName("Goods Name");
        goodsRespDTO6.setGoodsUnit("Goods Unit");
        goodsRespDTO6.setGuid("1234");
        goodsRespDTO6.setResidueDay(1);
        goodsRespDTO6.setResidueNum(1);
        goodsRespDTO6.setSkuGuid("1234");
        goodsRespDTO6.setSkuName("Sku Name");
        goodsRespDTO6.setStorePosition("Store Position");
        goodsRespDTO6.setTakeOutNum(10);

        GoodsRespDTO goodsRespDTO7 = new GoodsRespDTO();
        goodsRespDTO7.setDepositNum(10);
        goodsRespDTO7.setExpireTime("Expire Time");
        goodsRespDTO7.setGoodsName("Goods Name");
        goodsRespDTO7.setGoodsUnit("Goods Unit");
        goodsRespDTO7.setGuid("1234");
        goodsRespDTO7.setResidueDay(1);
        goodsRespDTO7.setResidueNum(1);
        goodsRespDTO7.setSkuGuid("1234");
        goodsRespDTO7.setSkuName("Sku Name");
        goodsRespDTO7.setStorePosition("Store Position");
        goodsRespDTO7.setTakeOutNum(10);

        GoodsRespDTO goodsRespDTO8 = new GoodsRespDTO();
        goodsRespDTO8.setDepositNum(10);
        goodsRespDTO8.setExpireTime("Expire Time");
        goodsRespDTO8.setGoodsName("Goods Name");
        goodsRespDTO8.setGoodsUnit("Goods Unit");
        goodsRespDTO8.setGuid("1234");
        goodsRespDTO8.setResidueDay(1);
        goodsRespDTO8.setResidueNum(1);
        goodsRespDTO8.setSkuGuid("1234");
        goodsRespDTO8.setSkuName("Sku Name");
        goodsRespDTO8.setStorePosition("Store Position");
        goodsRespDTO8.setTakeOutNum(10);

        GoodsRespDTO goodsRespDTO9 = new GoodsRespDTO();
        goodsRespDTO9.setDepositNum(10);
        goodsRespDTO9.setExpireTime("Expire Time");
        goodsRespDTO9.setGoodsName("Goods Name");
        goodsRespDTO9.setGoodsUnit("Goods Unit");
        goodsRespDTO9.setGuid("1234");
        goodsRespDTO9.setResidueDay(1);
        goodsRespDTO9.setResidueNum(1);
        goodsRespDTO9.setSkuGuid("1234");
        goodsRespDTO9.setSkuName("Sku Name");
        goodsRespDTO9.setStorePosition("Store Position");
        goodsRespDTO9.setTakeOutNum(10);

        GoodsRespDTO goodsRespDTO10 = new GoodsRespDTO();
        goodsRespDTO10.setDepositNum(10);
        goodsRespDTO10.setExpireTime("Expire Time");
        goodsRespDTO10.setGoodsName("Goods Name");
        goodsRespDTO10.setGoodsUnit("Goods Unit");
        goodsRespDTO10.setGuid("1234");
        goodsRespDTO10.setResidueDay(1);
        goodsRespDTO10.setResidueNum(1);
        goodsRespDTO10.setSkuGuid("1234");
        goodsRespDTO10.setSkuName("Sku Name");
        goodsRespDTO10.setStorePosition("Store Position");
        goodsRespDTO10.setTakeOutNum(10);

        GoodsRespDTO goodsRespDTO11 = new GoodsRespDTO();
        goodsRespDTO11.setDepositNum(10);
        goodsRespDTO11.setExpireTime("Expire Time");
        goodsRespDTO11.setGoodsName("Goods Name");
        goodsRespDTO11.setGoodsUnit("Goods Unit");
        goodsRespDTO11.setGuid("1234");
        goodsRespDTO11.setResidueDay(1);
        goodsRespDTO11.setResidueNum(1);
        goodsRespDTO11.setSkuGuid("1234");
        goodsRespDTO11.setSkuName("Sku Name");
        goodsRespDTO11.setStorePosition("Store Position");
        goodsRespDTO11.setTakeOutNum(10);
        GoodsRespDTO goodsRespDTO12 = mock(GoodsRespDTO.class);
        when(goodsRespDTO12.setDepositNum(anyInt())).thenReturn(goodsRespDTO);
        when(goodsRespDTO12.setExpireTime(Mockito.<String>any())).thenReturn(goodsRespDTO2);
        when(goodsRespDTO12.setGoodsName(Mockito.<String>any())).thenReturn(goodsRespDTO3);
        when(goodsRespDTO12.setGoodsUnit(Mockito.<String>any())).thenReturn(goodsRespDTO4);
        when(goodsRespDTO12.setGuid(Mockito.<String>any())).thenReturn(goodsRespDTO5);
        when(goodsRespDTO12.setResidueDay(anyInt())).thenReturn(goodsRespDTO6);
        when(goodsRespDTO12.setResidueNum(anyInt())).thenReturn(goodsRespDTO7);
        when(goodsRespDTO12.setSkuGuid(Mockito.<String>any())).thenReturn(goodsRespDTO8);
        when(goodsRespDTO12.setSkuName(Mockito.<String>any())).thenReturn(goodsRespDTO9);
        when(goodsRespDTO12.setStorePosition(Mockito.<String>any())).thenReturn(goodsRespDTO10);
        when(goodsRespDTO12.setTakeOutNum(anyInt())).thenReturn(goodsRespDTO11);
        when(goodsRespDTO12.getDepositNum()).thenReturn(10);
        when(goodsRespDTO12.getResidueNum()).thenReturn(1);
        when(goodsRespDTO12.getExpireTime()).thenReturn("Expire Time");
        when(goodsRespDTO12.getGoodsName()).thenReturn("Goods Name");
        when(goodsRespDTO12.getGoodsUnit()).thenReturn("Goods Unit");
        when(goodsRespDTO12.getGuid()).thenReturn("1234");
        when(goodsRespDTO12.getSkuGuid()).thenReturn("1234");
        when(goodsRespDTO12.getSkuName()).thenReturn("Sku Name");
        when(goodsRespDTO12.getStorePosition()).thenReturn("Store Position");
        goodsRespDTO12.setDepositNum(10);
        goodsRespDTO12.setExpireTime("Expire Time");
        goodsRespDTO12.setGoodsName("Goods Name");
        goodsRespDTO12.setGoodsUnit("Goods Unit");
        goodsRespDTO12.setGuid("1234");
        goodsRespDTO12.setResidueDay(1);
        goodsRespDTO12.setResidueNum(1);
        goodsRespDTO12.setSkuGuid("1234");
        goodsRespDTO12.setSkuName("Sku Name");
        goodsRespDTO12.setStorePosition("Store Position");
        goodsRespDTO12.setTakeOutNum(10);
        GoodsDO actualFromGoodsDTOResult = goodsMapstructImpl.fromGoodsDTO(goodsRespDTO12);
        verify(goodsRespDTO12).getDepositNum();
        verify(goodsRespDTO12).getExpireTime();
        verify(goodsRespDTO12).getGoodsName();
        verify(goodsRespDTO12).getGoodsUnit();
        verify(goodsRespDTO12).getGuid();
        verify(goodsRespDTO12).getResidueNum();
        verify(goodsRespDTO12).getSkuGuid();
        verify(goodsRespDTO12).getSkuName();
        verify(goodsRespDTO12).getStorePosition();
        verify(goodsRespDTO12).setDepositNum(anyInt());
        verify(goodsRespDTO12).setExpireTime(Mockito.<String>any());
        verify(goodsRespDTO12).setGoodsName(Mockito.<String>any());
        verify(goodsRespDTO12).setGoodsUnit(Mockito.<String>any());
        verify(goodsRespDTO12).setGuid(Mockito.<String>any());
        verify(goodsRespDTO12).setResidueDay(anyInt());
        verify(goodsRespDTO12).setResidueNum(anyInt());
        verify(goodsRespDTO12).setSkuGuid(Mockito.<String>any());
        verify(goodsRespDTO12).setSkuName(Mockito.<String>any());
        verify(goodsRespDTO12).setStorePosition(Mockito.<String>any());
        verify(goodsRespDTO12).setTakeOutNum(anyInt());
        assertEquals("1234", actualFromGoodsDTOResult.getGuid());
        assertEquals("1234", actualFromGoodsDTOResult.getSkuGuid());
        assertEquals("Expire Time", actualFromGoodsDTOResult.getExpireTime());
        assertEquals("Goods Name", actualFromGoodsDTOResult.getGoodsName());
        assertEquals("Goods Unit", actualFromGoodsDTOResult.getGoodsUnit());
        assertEquals("Sku Name", actualFromGoodsDTOResult.getSkuName());
        assertEquals("Store Position", actualFromGoodsDTOResult.getStorePosition());
        assertEquals(1, actualFromGoodsDTOResult.getResidueNum());
        assertEquals(10, actualFromGoodsDTOResult.getDepositNum());
    }

    /**
     * Method under test: {@link GoodsMapstructImpl#fromGoodsList(List)}
     */
    @Test
    public void testFromGoodsList() {
        assertTrue(goodsMapstructImpl.fromGoodsList(new ArrayList<>()).isEmpty());
    }

    /**
     * Method under test: {@link GoodsMapstructImpl#fromGoodsList(List)}
     */
    @Test
    public void testFromGoodsList2() {
        GoodsDO goodsDO = new GoodsDO();
        goodsDO.setDepositGuid("1234");
        goodsDO.setDepositNum(10);
        goodsDO.setExpireTime("Expire Time");
        goodsDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO.setGoodsClassify(1);
        goodsDO.setGoodsName("Goods Name");
        goodsDO.setGoodsUnit("Goods Unit");
        goodsDO.setGuid("1234");
        goodsDO.setId(1L);
        goodsDO.setResidueNum(1);
        goodsDO.setSkuGuid("1234");
        goodsDO.setSkuName("Sku Name");
        goodsDO.setStoreGuid("1234");
        goodsDO.setStorePosition("Store Position");

        ArrayList<GoodsDO> goodsDOList = new ArrayList<>();
        goodsDOList.add(goodsDO);
        assertEquals(1, goodsMapstructImpl.fromGoodsList(goodsDOList).size());
    }

    /**
     * Method under test: {@link GoodsMapstructImpl#fromGoodsList(List)}
     */
    @Test
    public void testFromGoodsList3() {
        GoodsDO goodsDO = new GoodsDO();
        goodsDO.setDepositGuid("1234");
        goodsDO.setDepositNum(10);
        goodsDO.setExpireTime("Expire Time");
        goodsDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO.setGoodsClassify(1);
        goodsDO.setGoodsName("Goods Name");
        goodsDO.setGoodsUnit("Goods Unit");
        goodsDO.setGuid("1234");
        goodsDO.setId(1L);
        goodsDO.setResidueNum(1);
        goodsDO.setSkuGuid("1234");
        goodsDO.setSkuName("Sku Name");
        goodsDO.setStoreGuid("1234");
        goodsDO.setStorePosition("Store Position");

        GoodsDO goodsDO2 = new GoodsDO();
        goodsDO2.setDepositGuid("Deposit Guid");
        goodsDO2.setDepositNum(1);
        goodsDO2.setExpireTime("com.holderzone.holder.saas.store.deposit.entity.bo.GoodsDO");
        goodsDO2.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO2.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO2.setGoodsClassify(0);
        goodsDO2.setGoodsName("com.holderzone.holder.saas.store.deposit.entity.bo.GoodsDO");
        goodsDO2.setGoodsUnit("com.holderzone.holder.saas.store.deposit.entity.bo.GoodsDO");
        goodsDO2.setGuid("Guid");
        goodsDO2.setId(2L);
        goodsDO2.setResidueNum(2);
        goodsDO2.setSkuGuid("Sku Guid");
        goodsDO2.setSkuName("com.holderzone.holder.saas.store.deposit.entity.bo.GoodsDO");
        goodsDO2.setStoreGuid("Store Guid");
        goodsDO2.setStorePosition("com.holderzone.holder.saas.store.deposit.entity.bo.GoodsDO");

        ArrayList<GoodsDO> goodsDOList = new ArrayList<>();
        goodsDOList.add(goodsDO2);
        goodsDOList.add(goodsDO);
        assertEquals(2, goodsMapstructImpl.fromGoodsList(goodsDOList).size());
    }

    /**
     * Method under test: {@link GoodsMapstructImpl#fromGoodsList(List)}
     */
    @Test
    public void testFromGoodsList4() {
        GoodsDO goodsDO = new GoodsDO();
        goodsDO.setDepositGuid("1234");
        goodsDO.setDepositNum(10);
        goodsDO.setExpireTime("Expire Time");
        goodsDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO.setGoodsClassify(1);
        goodsDO.setGoodsName("Goods Name");
        goodsDO.setGoodsUnit("Goods Unit");
        goodsDO.setGuid("1234");
        goodsDO.setId(1L);
        goodsDO.setResidueNum(1);
        goodsDO.setSkuGuid("1234");
        goodsDO.setSkuName("Sku Name");
        goodsDO.setStoreGuid("1234");
        goodsDO.setStorePosition("Store Position");

        GoodsDO goodsDO2 = new GoodsDO();
        goodsDO2.setDepositGuid("1234");
        goodsDO2.setDepositNum(10);
        goodsDO2.setExpireTime("Expire Time");
        goodsDO2.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO2.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO2.setGoodsClassify(1);
        goodsDO2.setGoodsName("Goods Name");
        goodsDO2.setGoodsUnit("Goods Unit");
        goodsDO2.setGuid("1234");
        goodsDO2.setId(1L);
        goodsDO2.setResidueNum(1);
        goodsDO2.setSkuGuid("1234");
        goodsDO2.setSkuName("Sku Name");
        goodsDO2.setStoreGuid("1234");
        goodsDO2.setStorePosition("Store Position");

        GoodsDO goodsDO3 = new GoodsDO();
        goodsDO3.setDepositGuid("1234");
        goodsDO3.setDepositNum(10);
        goodsDO3.setExpireTime("Expire Time");
        goodsDO3.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO3.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO3.setGoodsClassify(1);
        goodsDO3.setGoodsName("Goods Name");
        goodsDO3.setGoodsUnit("Goods Unit");
        goodsDO3.setGuid("1234");
        goodsDO3.setId(1L);
        goodsDO3.setResidueNum(1);
        goodsDO3.setSkuGuid("1234");
        goodsDO3.setSkuName("Sku Name");
        goodsDO3.setStoreGuid("1234");
        goodsDO3.setStorePosition("Store Position");

        GoodsDO goodsDO4 = new GoodsDO();
        goodsDO4.setDepositGuid("1234");
        goodsDO4.setDepositNum(10);
        goodsDO4.setExpireTime("Expire Time");
        goodsDO4.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO4.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO4.setGoodsClassify(1);
        goodsDO4.setGoodsName("Goods Name");
        goodsDO4.setGoodsUnit("Goods Unit");
        goodsDO4.setGuid("1234");
        goodsDO4.setId(1L);
        goodsDO4.setResidueNum(1);
        goodsDO4.setSkuGuid("1234");
        goodsDO4.setSkuName("Sku Name");
        goodsDO4.setStoreGuid("1234");
        goodsDO4.setStorePosition("Store Position");

        GoodsDO goodsDO5 = new GoodsDO();
        goodsDO5.setDepositGuid("1234");
        goodsDO5.setDepositNum(10);
        goodsDO5.setExpireTime("Expire Time");
        goodsDO5.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO5.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO5.setGoodsClassify(1);
        goodsDO5.setGoodsName("Goods Name");
        goodsDO5.setGoodsUnit("Goods Unit");
        goodsDO5.setGuid("1234");
        goodsDO5.setId(1L);
        goodsDO5.setResidueNum(1);
        goodsDO5.setSkuGuid("1234");
        goodsDO5.setSkuName("Sku Name");
        goodsDO5.setStoreGuid("1234");
        goodsDO5.setStorePosition("Store Position");

        GoodsDO goodsDO6 = new GoodsDO();
        goodsDO6.setDepositGuid("1234");
        goodsDO6.setDepositNum(10);
        goodsDO6.setExpireTime("Expire Time");
        goodsDO6.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO6.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO6.setGoodsClassify(1);
        goodsDO6.setGoodsName("Goods Name");
        goodsDO6.setGoodsUnit("Goods Unit");
        goodsDO6.setGuid("1234");
        goodsDO6.setId(1L);
        goodsDO6.setResidueNum(1);
        goodsDO6.setSkuGuid("1234");
        goodsDO6.setSkuName("Sku Name");
        goodsDO6.setStoreGuid("1234");
        goodsDO6.setStorePosition("Store Position");

        GoodsDO goodsDO7 = new GoodsDO();
        goodsDO7.setDepositGuid("1234");
        goodsDO7.setDepositNum(10);
        goodsDO7.setExpireTime("Expire Time");
        goodsDO7.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO7.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO7.setGoodsClassify(1);
        goodsDO7.setGoodsName("Goods Name");
        goodsDO7.setGoodsUnit("Goods Unit");
        goodsDO7.setGuid("1234");
        goodsDO7.setId(1L);
        goodsDO7.setResidueNum(1);
        goodsDO7.setSkuGuid("1234");
        goodsDO7.setSkuName("Sku Name");
        goodsDO7.setStoreGuid("1234");
        goodsDO7.setStorePosition("Store Position");

        GoodsDO goodsDO8 = new GoodsDO();
        goodsDO8.setDepositGuid("1234");
        goodsDO8.setDepositNum(10);
        goodsDO8.setExpireTime("Expire Time");
        goodsDO8.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO8.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO8.setGoodsClassify(1);
        goodsDO8.setGoodsName("Goods Name");
        goodsDO8.setGoodsUnit("Goods Unit");
        goodsDO8.setGuid("1234");
        goodsDO8.setId(1L);
        goodsDO8.setResidueNum(1);
        goodsDO8.setSkuGuid("1234");
        goodsDO8.setSkuName("Sku Name");
        goodsDO8.setStoreGuid("1234");
        goodsDO8.setStorePosition("Store Position");

        GoodsDO goodsDO9 = new GoodsDO();
        goodsDO9.setDepositGuid("1234");
        goodsDO9.setDepositNum(10);
        goodsDO9.setExpireTime("Expire Time");
        goodsDO9.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO9.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO9.setGoodsClassify(1);
        goodsDO9.setGoodsName("Goods Name");
        goodsDO9.setGoodsUnit("Goods Unit");
        goodsDO9.setGuid("1234");
        goodsDO9.setId(1L);
        goodsDO9.setResidueNum(1);
        goodsDO9.setSkuGuid("1234");
        goodsDO9.setSkuName("Sku Name");
        goodsDO9.setStoreGuid("1234");
        goodsDO9.setStorePosition("Store Position");

        GoodsDO goodsDO10 = new GoodsDO();
        goodsDO10.setDepositGuid("1234");
        goodsDO10.setDepositNum(10);
        goodsDO10.setExpireTime("Expire Time");
        goodsDO10.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO10.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO10.setGoodsClassify(1);
        goodsDO10.setGoodsName("Goods Name");
        goodsDO10.setGoodsUnit("Goods Unit");
        goodsDO10.setGuid("1234");
        goodsDO10.setId(1L);
        goodsDO10.setResidueNum(1);
        goodsDO10.setSkuGuid("1234");
        goodsDO10.setSkuName("Sku Name");
        goodsDO10.setStoreGuid("1234");
        goodsDO10.setStorePosition("Store Position");

        GoodsDO goodsDO11 = new GoodsDO();
        goodsDO11.setDepositGuid("1234");
        goodsDO11.setDepositNum(10);
        goodsDO11.setExpireTime("Expire Time");
        goodsDO11.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO11.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO11.setGoodsClassify(1);
        goodsDO11.setGoodsName("Goods Name");
        goodsDO11.setGoodsUnit("Goods Unit");
        goodsDO11.setGuid("1234");
        goodsDO11.setId(1L);
        goodsDO11.setResidueNum(1);
        goodsDO11.setSkuGuid("1234");
        goodsDO11.setSkuName("Sku Name");
        goodsDO11.setStoreGuid("1234");
        goodsDO11.setStorePosition("Store Position");

        GoodsDO goodsDO12 = new GoodsDO();
        goodsDO12.setDepositGuid("1234");
        goodsDO12.setDepositNum(10);
        goodsDO12.setExpireTime("Expire Time");
        goodsDO12.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO12.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO12.setGoodsClassify(1);
        goodsDO12.setGoodsName("Goods Name");
        goodsDO12.setGoodsUnit("Goods Unit");
        goodsDO12.setGuid("1234");
        goodsDO12.setId(1L);
        goodsDO12.setResidueNum(1);
        goodsDO12.setSkuGuid("1234");
        goodsDO12.setSkuName("Sku Name");
        goodsDO12.setStoreGuid("1234");
        goodsDO12.setStorePosition("Store Position");

        GoodsDO goodsDO13 = new GoodsDO();
        goodsDO13.setDepositGuid("1234");
        goodsDO13.setDepositNum(10);
        goodsDO13.setExpireTime("Expire Time");
        goodsDO13.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO13.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO13.setGoodsClassify(1);
        goodsDO13.setGoodsName("Goods Name");
        goodsDO13.setGoodsUnit("Goods Unit");
        goodsDO13.setGuid("1234");
        goodsDO13.setId(1L);
        goodsDO13.setResidueNum(1);
        goodsDO13.setSkuGuid("1234");
        goodsDO13.setSkuName("Sku Name");
        goodsDO13.setStoreGuid("1234");
        goodsDO13.setStorePosition("Store Position");

        GoodsDO goodsDO14 = new GoodsDO();
        goodsDO14.setDepositGuid("1234");
        goodsDO14.setDepositNum(10);
        goodsDO14.setExpireTime("Expire Time");
        goodsDO14.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO14.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO14.setGoodsClassify(1);
        goodsDO14.setGoodsName("Goods Name");
        goodsDO14.setGoodsUnit("Goods Unit");
        goodsDO14.setGuid("1234");
        goodsDO14.setId(1L);
        goodsDO14.setResidueNum(1);
        goodsDO14.setSkuGuid("1234");
        goodsDO14.setSkuName("Sku Name");
        goodsDO14.setStoreGuid("1234");
        goodsDO14.setStorePosition("Store Position");

        GoodsDO goodsDO15 = new GoodsDO();
        goodsDO15.setDepositGuid("1234");
        goodsDO15.setDepositNum(10);
        goodsDO15.setExpireTime("Expire Time");
        goodsDO15.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO15.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO15.setGoodsClassify(1);
        goodsDO15.setGoodsName("Goods Name");
        goodsDO15.setGoodsUnit("Goods Unit");
        goodsDO15.setGuid("1234");
        goodsDO15.setId(1L);
        goodsDO15.setResidueNum(1);
        goodsDO15.setSkuGuid("1234");
        goodsDO15.setSkuName("Sku Name");
        goodsDO15.setStoreGuid("1234");
        goodsDO15.setStorePosition("Store Position");
        GoodsDO goodsDO16 = mock(GoodsDO.class);
        when(goodsDO16.setDepositGuid(Mockito.<String>any())).thenReturn(goodsDO);
        when(goodsDO16.setDepositNum(anyInt())).thenReturn(goodsDO2);
        when(goodsDO16.setExpireTime(Mockito.<String>any())).thenReturn(goodsDO3);
        when(goodsDO16.setGmtCreate(Mockito.<LocalDateTime>any())).thenReturn(goodsDO4);
        when(goodsDO16.setGmtModified(Mockito.<LocalDateTime>any())).thenReturn(goodsDO5);
        when(goodsDO16.setGoodsClassify(anyInt())).thenReturn(goodsDO6);
        when(goodsDO16.setGoodsName(Mockito.<String>any())).thenReturn(goodsDO7);
        when(goodsDO16.setGoodsUnit(Mockito.<String>any())).thenReturn(goodsDO8);
        when(goodsDO16.setGuid(Mockito.<String>any())).thenReturn(goodsDO9);
        when(goodsDO16.setId(Mockito.<Long>any())).thenReturn(goodsDO10);
        when(goodsDO16.setResidueNum(anyInt())).thenReturn(goodsDO11);
        when(goodsDO16.setSkuGuid(Mockito.<String>any())).thenReturn(goodsDO12);
        when(goodsDO16.setSkuName(Mockito.<String>any())).thenReturn(goodsDO13);
        when(goodsDO16.setStoreGuid(Mockito.<String>any())).thenReturn(goodsDO14);
        when(goodsDO16.setStorePosition(Mockito.<String>any())).thenReturn(goodsDO15);
        when(goodsDO16.getDepositNum()).thenReturn(10);
        when(goodsDO16.getResidueNum()).thenReturn(1);
        when(goodsDO16.getExpireTime()).thenReturn("Expire Time");
        when(goodsDO16.getGoodsName()).thenReturn("Goods Name");
        when(goodsDO16.getGoodsUnit()).thenReturn("Goods Unit");
        when(goodsDO16.getGuid()).thenReturn("1234");
        when(goodsDO16.getSkuGuid()).thenReturn("1234");
        when(goodsDO16.getSkuName()).thenReturn("Sku Name");
        when(goodsDO16.getStorePosition()).thenReturn("Store Position");
        goodsDO16.setDepositGuid("1234");
        goodsDO16.setDepositNum(10);
        goodsDO16.setExpireTime("Expire Time");
        goodsDO16.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO16.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO16.setGoodsClassify(1);
        goodsDO16.setGoodsName("Goods Name");
        goodsDO16.setGoodsUnit("Goods Unit");
        goodsDO16.setGuid("1234");
        goodsDO16.setId(1L);
        goodsDO16.setResidueNum(1);
        goodsDO16.setSkuGuid("1234");
        goodsDO16.setSkuName("Sku Name");
        goodsDO16.setStoreGuid("1234");
        goodsDO16.setStorePosition("Store Position");

        ArrayList<GoodsDO> goodsDOList = new ArrayList<>();
        goodsDOList.add(goodsDO16);
        List<GoodsRespDTO> actualFromGoodsListResult = goodsMapstructImpl.fromGoodsList(goodsDOList);
        verify(goodsDO16).getDepositNum();
        verify(goodsDO16).getExpireTime();
        verify(goodsDO16).getGoodsName();
        verify(goodsDO16).getGoodsUnit();
        verify(goodsDO16).getGuid();
        verify(goodsDO16).getResidueNum();
        verify(goodsDO16).getSkuGuid();
        verify(goodsDO16).getSkuName();
        verify(goodsDO16).getStorePosition();
        verify(goodsDO16).setDepositGuid(Mockito.<String>any());
        verify(goodsDO16).setDepositNum(anyInt());
        verify(goodsDO16).setExpireTime(Mockito.<String>any());
        verify(goodsDO16).setGmtCreate(Mockito.<LocalDateTime>any());
        verify(goodsDO16).setGmtModified(Mockito.<LocalDateTime>any());
        verify(goodsDO16).setGoodsClassify(anyInt());
        verify(goodsDO16).setGoodsName(Mockito.<String>any());
        verify(goodsDO16).setGoodsUnit(Mockito.<String>any());
        verify(goodsDO16).setGuid(Mockito.<String>any());
        verify(goodsDO16).setId(Mockito.<Long>any());
        verify(goodsDO16).setResidueNum(anyInt());
        verify(goodsDO16).setSkuGuid(Mockito.<String>any());
        verify(goodsDO16).setSkuName(Mockito.<String>any());
        verify(goodsDO16).setStoreGuid(Mockito.<String>any());
        verify(goodsDO16).setStorePosition(Mockito.<String>any());
        assertEquals(1, actualFromGoodsListResult.size());
    }

    /**
     * Method under test: {@link GoodsMapstructImpl#goodsDOToGoodsRespDTO(GoodsDO)}
     */
    @Test
    public void testGoodsDOToGoodsRespDTO() {
        GoodsDO goodsDO = new GoodsDO();
        goodsDO.setDepositGuid("1234");
        goodsDO.setDepositNum(10);
        goodsDO.setExpireTime("Expire Time");
        goodsDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO.setGoodsClassify(1);
        goodsDO.setGoodsName("Goods Name");
        goodsDO.setGoodsUnit("Goods Unit");
        goodsDO.setGuid("1234");
        goodsDO.setId(1L);
        goodsDO.setResidueNum(1);
        goodsDO.setSkuGuid("1234");
        goodsDO.setSkuName("Sku Name");
        goodsDO.setStoreGuid("1234");
        goodsDO.setStorePosition("Store Position");
        GoodsRespDTO actualGoodsDOToGoodsRespDTOResult = goodsMapstructImpl.goodsDOToGoodsRespDTO(goodsDO);
        assertEquals("1234", actualGoodsDOToGoodsRespDTOResult.getGuid());
        assertEquals("1234", actualGoodsDOToGoodsRespDTOResult.getSkuGuid());
        assertEquals("Expire Time", actualGoodsDOToGoodsRespDTOResult.getExpireTime());
        assertEquals("Goods Name", actualGoodsDOToGoodsRespDTOResult.getGoodsName());
        assertEquals("Goods Unit", actualGoodsDOToGoodsRespDTOResult.getGoodsUnit());
        assertEquals("Sku Name", actualGoodsDOToGoodsRespDTOResult.getSkuName());
        assertEquals("Store Position", actualGoodsDOToGoodsRespDTOResult.getStorePosition());
        assertEquals(1, actualGoodsDOToGoodsRespDTOResult.getResidueNum());
        assertEquals(10, actualGoodsDOToGoodsRespDTOResult.getDepositNum());
    }

    /**
     * Method under test: {@link GoodsMapstructImpl#goodsDOToGoodsRespDTO(GoodsDO)}
     */
    @Test
    public void testGoodsDOToGoodsRespDTO2() {
        GoodsDO goodsDO = new GoodsDO();
        goodsDO.setDepositGuid("1234");
        goodsDO.setDepositNum(10);
        goodsDO.setExpireTime("Expire Time");
        goodsDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO.setGoodsClassify(1);
        goodsDO.setGoodsName("Goods Name");
        goodsDO.setGoodsUnit("Goods Unit");
        goodsDO.setGuid("1234");
        goodsDO.setId(1L);
        goodsDO.setResidueNum(1);
        goodsDO.setSkuGuid("1234");
        goodsDO.setSkuName("Sku Name");
        goodsDO.setStoreGuid("1234");
        goodsDO.setStorePosition("Store Position");

        GoodsDO goodsDO2 = new GoodsDO();
        goodsDO2.setDepositGuid("1234");
        goodsDO2.setDepositNum(10);
        goodsDO2.setExpireTime("Expire Time");
        goodsDO2.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO2.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO2.setGoodsClassify(1);
        goodsDO2.setGoodsName("Goods Name");
        goodsDO2.setGoodsUnit("Goods Unit");
        goodsDO2.setGuid("1234");
        goodsDO2.setId(1L);
        goodsDO2.setResidueNum(1);
        goodsDO2.setSkuGuid("1234");
        goodsDO2.setSkuName("Sku Name");
        goodsDO2.setStoreGuid("1234");
        goodsDO2.setStorePosition("Store Position");

        GoodsDO goodsDO3 = new GoodsDO();
        goodsDO3.setDepositGuid("1234");
        goodsDO3.setDepositNum(10);
        goodsDO3.setExpireTime("Expire Time");
        goodsDO3.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO3.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO3.setGoodsClassify(1);
        goodsDO3.setGoodsName("Goods Name");
        goodsDO3.setGoodsUnit("Goods Unit");
        goodsDO3.setGuid("1234");
        goodsDO3.setId(1L);
        goodsDO3.setResidueNum(1);
        goodsDO3.setSkuGuid("1234");
        goodsDO3.setSkuName("Sku Name");
        goodsDO3.setStoreGuid("1234");
        goodsDO3.setStorePosition("Store Position");

        GoodsDO goodsDO4 = new GoodsDO();
        goodsDO4.setDepositGuid("1234");
        goodsDO4.setDepositNum(10);
        goodsDO4.setExpireTime("Expire Time");
        goodsDO4.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO4.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO4.setGoodsClassify(1);
        goodsDO4.setGoodsName("Goods Name");
        goodsDO4.setGoodsUnit("Goods Unit");
        goodsDO4.setGuid("1234");
        goodsDO4.setId(1L);
        goodsDO4.setResidueNum(1);
        goodsDO4.setSkuGuid("1234");
        goodsDO4.setSkuName("Sku Name");
        goodsDO4.setStoreGuid("1234");
        goodsDO4.setStorePosition("Store Position");

        GoodsDO goodsDO5 = new GoodsDO();
        goodsDO5.setDepositGuid("1234");
        goodsDO5.setDepositNum(10);
        goodsDO5.setExpireTime("Expire Time");
        goodsDO5.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO5.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO5.setGoodsClassify(1);
        goodsDO5.setGoodsName("Goods Name");
        goodsDO5.setGoodsUnit("Goods Unit");
        goodsDO5.setGuid("1234");
        goodsDO5.setId(1L);
        goodsDO5.setResidueNum(1);
        goodsDO5.setSkuGuid("1234");
        goodsDO5.setSkuName("Sku Name");
        goodsDO5.setStoreGuid("1234");
        goodsDO5.setStorePosition("Store Position");

        GoodsDO goodsDO6 = new GoodsDO();
        goodsDO6.setDepositGuid("1234");
        goodsDO6.setDepositNum(10);
        goodsDO6.setExpireTime("Expire Time");
        goodsDO6.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO6.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO6.setGoodsClassify(1);
        goodsDO6.setGoodsName("Goods Name");
        goodsDO6.setGoodsUnit("Goods Unit");
        goodsDO6.setGuid("1234");
        goodsDO6.setId(1L);
        goodsDO6.setResidueNum(1);
        goodsDO6.setSkuGuid("1234");
        goodsDO6.setSkuName("Sku Name");
        goodsDO6.setStoreGuid("1234");
        goodsDO6.setStorePosition("Store Position");

        GoodsDO goodsDO7 = new GoodsDO();
        goodsDO7.setDepositGuid("1234");
        goodsDO7.setDepositNum(10);
        goodsDO7.setExpireTime("Expire Time");
        goodsDO7.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO7.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO7.setGoodsClassify(1);
        goodsDO7.setGoodsName("Goods Name");
        goodsDO7.setGoodsUnit("Goods Unit");
        goodsDO7.setGuid("1234");
        goodsDO7.setId(1L);
        goodsDO7.setResidueNum(1);
        goodsDO7.setSkuGuid("1234");
        goodsDO7.setSkuName("Sku Name");
        goodsDO7.setStoreGuid("1234");
        goodsDO7.setStorePosition("Store Position");

        GoodsDO goodsDO8 = new GoodsDO();
        goodsDO8.setDepositGuid("1234");
        goodsDO8.setDepositNum(10);
        goodsDO8.setExpireTime("Expire Time");
        goodsDO8.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO8.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO8.setGoodsClassify(1);
        goodsDO8.setGoodsName("Goods Name");
        goodsDO8.setGoodsUnit("Goods Unit");
        goodsDO8.setGuid("1234");
        goodsDO8.setId(1L);
        goodsDO8.setResidueNum(1);
        goodsDO8.setSkuGuid("1234");
        goodsDO8.setSkuName("Sku Name");
        goodsDO8.setStoreGuid("1234");
        goodsDO8.setStorePosition("Store Position");

        GoodsDO goodsDO9 = new GoodsDO();
        goodsDO9.setDepositGuid("1234");
        goodsDO9.setDepositNum(10);
        goodsDO9.setExpireTime("Expire Time");
        goodsDO9.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO9.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO9.setGoodsClassify(1);
        goodsDO9.setGoodsName("Goods Name");
        goodsDO9.setGoodsUnit("Goods Unit");
        goodsDO9.setGuid("1234");
        goodsDO9.setId(1L);
        goodsDO9.setResidueNum(1);
        goodsDO9.setSkuGuid("1234");
        goodsDO9.setSkuName("Sku Name");
        goodsDO9.setStoreGuid("1234");
        goodsDO9.setStorePosition("Store Position");

        GoodsDO goodsDO10 = new GoodsDO();
        goodsDO10.setDepositGuid("1234");
        goodsDO10.setDepositNum(10);
        goodsDO10.setExpireTime("Expire Time");
        goodsDO10.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO10.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO10.setGoodsClassify(1);
        goodsDO10.setGoodsName("Goods Name");
        goodsDO10.setGoodsUnit("Goods Unit");
        goodsDO10.setGuid("1234");
        goodsDO10.setId(1L);
        goodsDO10.setResidueNum(1);
        goodsDO10.setSkuGuid("1234");
        goodsDO10.setSkuName("Sku Name");
        goodsDO10.setStoreGuid("1234");
        goodsDO10.setStorePosition("Store Position");

        GoodsDO goodsDO11 = new GoodsDO();
        goodsDO11.setDepositGuid("1234");
        goodsDO11.setDepositNum(10);
        goodsDO11.setExpireTime("Expire Time");
        goodsDO11.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO11.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO11.setGoodsClassify(1);
        goodsDO11.setGoodsName("Goods Name");
        goodsDO11.setGoodsUnit("Goods Unit");
        goodsDO11.setGuid("1234");
        goodsDO11.setId(1L);
        goodsDO11.setResidueNum(1);
        goodsDO11.setSkuGuid("1234");
        goodsDO11.setSkuName("Sku Name");
        goodsDO11.setStoreGuid("1234");
        goodsDO11.setStorePosition("Store Position");

        GoodsDO goodsDO12 = new GoodsDO();
        goodsDO12.setDepositGuid("1234");
        goodsDO12.setDepositNum(10);
        goodsDO12.setExpireTime("Expire Time");
        goodsDO12.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO12.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO12.setGoodsClassify(1);
        goodsDO12.setGoodsName("Goods Name");
        goodsDO12.setGoodsUnit("Goods Unit");
        goodsDO12.setGuid("1234");
        goodsDO12.setId(1L);
        goodsDO12.setResidueNum(1);
        goodsDO12.setSkuGuid("1234");
        goodsDO12.setSkuName("Sku Name");
        goodsDO12.setStoreGuid("1234");
        goodsDO12.setStorePosition("Store Position");

        GoodsDO goodsDO13 = new GoodsDO();
        goodsDO13.setDepositGuid("1234");
        goodsDO13.setDepositNum(10);
        goodsDO13.setExpireTime("Expire Time");
        goodsDO13.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO13.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO13.setGoodsClassify(1);
        goodsDO13.setGoodsName("Goods Name");
        goodsDO13.setGoodsUnit("Goods Unit");
        goodsDO13.setGuid("1234");
        goodsDO13.setId(1L);
        goodsDO13.setResidueNum(1);
        goodsDO13.setSkuGuid("1234");
        goodsDO13.setSkuName("Sku Name");
        goodsDO13.setStoreGuid("1234");
        goodsDO13.setStorePosition("Store Position");

        GoodsDO goodsDO14 = new GoodsDO();
        goodsDO14.setDepositGuid("1234");
        goodsDO14.setDepositNum(10);
        goodsDO14.setExpireTime("Expire Time");
        goodsDO14.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO14.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO14.setGoodsClassify(1);
        goodsDO14.setGoodsName("Goods Name");
        goodsDO14.setGoodsUnit("Goods Unit");
        goodsDO14.setGuid("1234");
        goodsDO14.setId(1L);
        goodsDO14.setResidueNum(1);
        goodsDO14.setSkuGuid("1234");
        goodsDO14.setSkuName("Sku Name");
        goodsDO14.setStoreGuid("1234");
        goodsDO14.setStorePosition("Store Position");

        GoodsDO goodsDO15 = new GoodsDO();
        goodsDO15.setDepositGuid("1234");
        goodsDO15.setDepositNum(10);
        goodsDO15.setExpireTime("Expire Time");
        goodsDO15.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO15.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO15.setGoodsClassify(1);
        goodsDO15.setGoodsName("Goods Name");
        goodsDO15.setGoodsUnit("Goods Unit");
        goodsDO15.setGuid("1234");
        goodsDO15.setId(1L);
        goodsDO15.setResidueNum(1);
        goodsDO15.setSkuGuid("1234");
        goodsDO15.setSkuName("Sku Name");
        goodsDO15.setStoreGuid("1234");
        goodsDO15.setStorePosition("Store Position");
        GoodsDO goodsDO16 = mock(GoodsDO.class);
        when(goodsDO16.setDepositGuid(Mockito.<String>any())).thenReturn(goodsDO);
        when(goodsDO16.setDepositNum(anyInt())).thenReturn(goodsDO2);
        when(goodsDO16.setExpireTime(Mockito.<String>any())).thenReturn(goodsDO3);
        when(goodsDO16.setGmtCreate(Mockito.<LocalDateTime>any())).thenReturn(goodsDO4);
        when(goodsDO16.setGmtModified(Mockito.<LocalDateTime>any())).thenReturn(goodsDO5);
        when(goodsDO16.setGoodsClassify(anyInt())).thenReturn(goodsDO6);
        when(goodsDO16.setGoodsName(Mockito.<String>any())).thenReturn(goodsDO7);
        when(goodsDO16.setGoodsUnit(Mockito.<String>any())).thenReturn(goodsDO8);
        when(goodsDO16.setGuid(Mockito.<String>any())).thenReturn(goodsDO9);
        when(goodsDO16.setId(Mockito.<Long>any())).thenReturn(goodsDO10);
        when(goodsDO16.setResidueNum(anyInt())).thenReturn(goodsDO11);
        when(goodsDO16.setSkuGuid(Mockito.<String>any())).thenReturn(goodsDO12);
        when(goodsDO16.setSkuName(Mockito.<String>any())).thenReturn(goodsDO13);
        when(goodsDO16.setStoreGuid(Mockito.<String>any())).thenReturn(goodsDO14);
        when(goodsDO16.setStorePosition(Mockito.<String>any())).thenReturn(goodsDO15);
        when(goodsDO16.getDepositNum()).thenReturn(10);
        when(goodsDO16.getResidueNum()).thenReturn(1);
        when(goodsDO16.getExpireTime()).thenReturn("Expire Time");
        when(goodsDO16.getGoodsName()).thenReturn("Goods Name");
        when(goodsDO16.getGoodsUnit()).thenReturn("Goods Unit");
        when(goodsDO16.getGuid()).thenReturn("1234");
        when(goodsDO16.getSkuGuid()).thenReturn("1234");
        when(goodsDO16.getSkuName()).thenReturn("Sku Name");
        when(goodsDO16.getStorePosition()).thenReturn("Store Position");
        goodsDO16.setDepositGuid("1234");
        goodsDO16.setDepositNum(10);
        goodsDO16.setExpireTime("Expire Time");
        goodsDO16.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO16.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        goodsDO16.setGoodsClassify(1);
        goodsDO16.setGoodsName("Goods Name");
        goodsDO16.setGoodsUnit("Goods Unit");
        goodsDO16.setGuid("1234");
        goodsDO16.setId(1L);
        goodsDO16.setResidueNum(1);
        goodsDO16.setSkuGuid("1234");
        goodsDO16.setSkuName("Sku Name");
        goodsDO16.setStoreGuid("1234");
        goodsDO16.setStorePosition("Store Position");
        GoodsRespDTO actualGoodsDOToGoodsRespDTOResult = goodsMapstructImpl.goodsDOToGoodsRespDTO(goodsDO16);
        verify(goodsDO16).getDepositNum();
        verify(goodsDO16).getExpireTime();
        verify(goodsDO16).getGoodsName();
        verify(goodsDO16).getGoodsUnit();
        verify(goodsDO16).getGuid();
        verify(goodsDO16).getResidueNum();
        verify(goodsDO16).getSkuGuid();
        verify(goodsDO16).getSkuName();
        verify(goodsDO16).getStorePosition();
        verify(goodsDO16).setDepositGuid(Mockito.<String>any());
        verify(goodsDO16).setDepositNum(anyInt());
        verify(goodsDO16).setExpireTime(Mockito.<String>any());
        verify(goodsDO16).setGmtCreate(Mockito.<LocalDateTime>any());
        verify(goodsDO16).setGmtModified(Mockito.<LocalDateTime>any());
        verify(goodsDO16).setGoodsClassify(anyInt());
        verify(goodsDO16).setGoodsName(Mockito.<String>any());
        verify(goodsDO16).setGoodsUnit(Mockito.<String>any());
        verify(goodsDO16).setGuid(Mockito.<String>any());
        verify(goodsDO16).setId(Mockito.<Long>any());
        verify(goodsDO16).setResidueNum(anyInt());
        verify(goodsDO16).setSkuGuid(Mockito.<String>any());
        verify(goodsDO16).setSkuName(Mockito.<String>any());
        verify(goodsDO16).setStoreGuid(Mockito.<String>any());
        verify(goodsDO16).setStorePosition(Mockito.<String>any());
        assertEquals("1234", actualGoodsDOToGoodsRespDTOResult.getGuid());
        assertEquals("1234", actualGoodsDOToGoodsRespDTOResult.getSkuGuid());
        assertEquals("Expire Time", actualGoodsDOToGoodsRespDTOResult.getExpireTime());
        assertEquals("Goods Name", actualGoodsDOToGoodsRespDTOResult.getGoodsName());
        assertEquals("Goods Unit", actualGoodsDOToGoodsRespDTOResult.getGoodsUnit());
        assertEquals("Sku Name", actualGoodsDOToGoodsRespDTOResult.getSkuName());
        assertEquals("Store Position", actualGoodsDOToGoodsRespDTOResult.getStorePosition());
        assertEquals(1, actualGoodsDOToGoodsRespDTOResult.getResidueNum());
        assertEquals(10, actualGoodsDOToGoodsRespDTOResult.getDepositNum());
    }
}
