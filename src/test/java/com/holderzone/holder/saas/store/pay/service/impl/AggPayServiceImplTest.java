package com.holderzone.holder.saas.store.pay.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.pay.config.AggPayConfig;
import com.holderzone.holder.saas.store.pay.config.DeveloperConfig;
import com.holderzone.holder.saas.store.pay.entity.AggPayAttachDataVO;
import com.holderzone.holder.saas.store.pay.entity.AggPayReserveVO;
import com.holderzone.holder.saas.store.pay.entity.HandlerPayBO;
import com.holderzone.holder.saas.store.pay.entity.domain.PayRecordDO;
import com.holderzone.holder.saas.store.pay.mapstruct.AggPayMapstruct;
import com.holderzone.holder.saas.store.pay.service.ErpConfigService;
import com.holderzone.holder.saas.store.pay.service.PayRecordService;
import com.holderzone.holder.saas.store.pay.service.PollingService;
import com.holderzone.holder.saas.store.pay.service.RedisService;
import com.holderzone.holder.saas.store.pay.service.rpc.AggPayRpcService;
import com.holderzone.holder.saas.store.pay.service.rpc.SaasResultRpcService;
import com.holderzone.holder.saas.store.pay.utils.PageAdapter;
import com.holderzone.holder.saas.store.pay.utils.RespFriendlyUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import com.holderzone.saas.store.dto.pay.*;
import com.holderzone.saas.store.dto.trade.BaseInfo;
import com.holderzone.saas.store.dto.trade.PaymentInfoDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AggPayServiceImplTest {

    @Mock
    private DeveloperConfig mockDeveloperConfig;
    @Mock
    private ErpConfigService mockErpConfigService;
    @Mock
    private RedisService mockRedisService;
    @Mock
    private AggPayRpcService mockAggPayRpcService;
    @Mock
    private PollingService mockPollingService;
    @Mock
    private AggPayMapstruct mockAggPayMapstruct;
    @Mock
    private PayRecordService mockPayRecordService;
    @Mock
    private SaasResultRpcService mockSaasResultRpcService;
    @Mock
    private AggPayConfig mockAggPayConfig;

    private AggPayServiceImpl aggPayServiceImplUnderTest;

    @Before
    public void setUp() {
        aggPayServiceImplUnderTest = new AggPayServiceImpl(mockDeveloperConfig, mockErpConfigService, mockRedisService,
                mockAggPayRpcService, mockPollingService, new RespFriendlyUtils(), mockAggPayMapstruct,
                mockPayRecordService, mockSaasResultRpcService, mockAggPayConfig);
    }

    @Test
    public void testPrePay() {
        // Setup
        final SaasAggPayDTO saasAggPayDTO = new SaasAggPayDTO();
        saasAggPayDTO.setDeviceType(0);
        saasAggPayDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggPayDTO.setStoreGuid("storeGuid");
        final AggPayPreTradingReqDTO reqDTO = new AggPayPreTradingReqDTO();
        reqDTO.setAmount(new BigDecimal("0.00"));
        reqDTO.setOrderGUID("orderGUID");
        reqDTO.setPayGUID("payGUID");
        reqDTO.setPayPowerId("payPowerId");
        reqDTO.setAttachData("attachData");
        reqDTO.setOutNotifyUrl("callBack");
        reqDTO.setAppId("appId");
        reqDTO.setTimestamp(0L);
        reqDTO.setDeveloperId("developerId");
        reqDTO.setSignature("signature");
        reqDTO.setMerchantUserId("empId");
        saasAggPayDTO.setReqDTO(reqDTO);
        saasAggPayDTO.setIsQuickReceipt(false);
        saasAggPayDTO.setIsLast(false);
        saasAggPayDTO.setSaasCallBackUrl("innerCallBackUrl");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid", "orderGUID"))
                .thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockAggPayConfig.getCallBack()).thenReturn("callBack");

        // Configure AggPayMapstruct.makeAttachVo(...).
        final AggPayAttachDataVO aggPayAttachDataVO = new AggPayAttachDataVO();
        aggPayAttachDataVO.setDeviceType(0);
        aggPayAttachDataVO.setEnterpriseGuid("enterpriseGuid");
        aggPayAttachDataVO.setStoreGuid("storeGuid");
        aggPayAttachDataVO.setSaasCallBackUrl("innerCallBackUrl");
        aggPayAttachDataVO.setIsQuickReceipt(false);
        aggPayAttachDataVO.setIsLast(false);
        aggPayAttachDataVO.setPlatformSource("掌控者智慧系统");
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.makeAttachVo(baseDTO)).thenReturn(aggPayAttachDataVO);

        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.preTradingAsync(...).
        final AggPayRespDTO aggPayRespDTO = new AggPayRespDTO();
        aggPayRespDTO.setCode("10000");
        aggPayRespDTO.setMsg("success");
        aggPayRespDTO.setPayGuid("payGUID");
        aggPayRespDTO.setOrderGuid("orderGUID");
        aggPayRespDTO.setPaymentAppId("appId");
        final Mono<AggPayRespDTO> aggPayRespDTOMono = Mono.just(aggPayRespDTO);
        final AggPayPreTradingReqDTO aggPayPreTradingReqDTO = new AggPayPreTradingReqDTO();
        aggPayPreTradingReqDTO.setAmount(new BigDecimal("0.00"));
        aggPayPreTradingReqDTO.setOrderGUID("orderGUID");
        aggPayPreTradingReqDTO.setPayGUID("payGUID");
        aggPayPreTradingReqDTO.setPayPowerId("payPowerId");
        aggPayPreTradingReqDTO.setAttachData("attachData");
        aggPayPreTradingReqDTO.setOutNotifyUrl("callBack");
        aggPayPreTradingReqDTO.setAppId("appId");
        aggPayPreTradingReqDTO.setTimestamp(0L);
        aggPayPreTradingReqDTO.setDeveloperId("developerId");
        aggPayPreTradingReqDTO.setSignature("signature");
        aggPayPreTradingReqDTO.setMerchantUserId("empId");
        when(mockAggPayRpcService.preTradingAsync(aggPayPreTradingReqDTO)).thenReturn(aggPayRespDTOMono);

        // Configure AggPayMapstruct.createPollingDto(...).
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggPayPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggPayPollingDTO.setAppId("appId");
        aggPayPollingDTO.setTimestamp(0L);
        aggPayPollingDTO.setDeveloperId("developerId");
        aggPayPollingDTO.setSignature("signature");
        final AggPayPreTradingReqDTO payPreTradingReqDTO = new AggPayPreTradingReqDTO();
        payPreTradingReqDTO.setAmount(new BigDecimal("0.00"));
        payPreTradingReqDTO.setOrderGUID("orderGUID");
        payPreTradingReqDTO.setPayGUID("payGUID");
        payPreTradingReqDTO.setPayPowerId("payPowerId");
        payPreTradingReqDTO.setAttachData("attachData");
        payPreTradingReqDTO.setOutNotifyUrl("callBack");
        payPreTradingReqDTO.setAppId("appId");
        payPreTradingReqDTO.setTimestamp(0L);
        payPreTradingReqDTO.setDeveloperId("developerId");
        payPreTradingReqDTO.setSignature("signature");
        payPreTradingReqDTO.setMerchantUserId("empId");
        when(mockAggPayMapstruct.createPollingDto(payPreTradingReqDTO)).thenReturn(aggPayPollingDTO);

        // Configure AggPayMapstruct.createBaseInfo(...).
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setTel("tel");
        final BaseDTO baseDTO1 = new BaseDTO();
        baseDTO1.setDeviceType(0);
        baseDTO1.setDeviceId("deviceId");
        baseDTO1.setEnterpriseGuid("enterpriseGuid");
        baseDTO1.setEnterpriseName("enterpriseName");
        baseDTO1.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.createBaseInfo(baseDTO1)).thenReturn(baseInfo);

        // Run the test
        final Mono<AggPayRespDTO> result = aggPayServiceImplUnderTest.prePay(saasAggPayDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("orderGUID", "payGUID", pollingRespDTO);

        // Confirm PollingService.startPrePayPolling(...).
        final AggPayPollingDTO aggPayPollingDTO1 = new AggPayPollingDTO();
        aggPayPollingDTO1.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggPayPollingDTO1.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggPayPollingDTO1.setAppId("appId");
        aggPayPollingDTO1.setTimestamp(0L);
        aggPayPollingDTO1.setDeveloperId("developerId");
        aggPayPollingDTO1.setSignature("signature");
        verify(mockPollingService).startPrePayPolling(aggPayPollingDTO1, HandlerPayBO.builder()
                .baseInfo(new BaseInfo())
                .paymentInfoDTO(new PaymentInfoDTO())
                .times(0)
                .enterpriseGuid("enterpriseGuid")
                .isQuickReceipt(false)
                .innerCallBackUrl("innerCallBackUrl")
                .handlerType(0)
                .payPowerId("payPowerId")
                .build());
    }

    @Test
    public void testPrePay_ErpConfigServiceReturnsNoItem() {
        // Setup
        final SaasAggPayDTO saasAggPayDTO = new SaasAggPayDTO();
        saasAggPayDTO.setDeviceType(0);
        saasAggPayDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggPayDTO.setStoreGuid("storeGuid");
        final AggPayPreTradingReqDTO reqDTO = new AggPayPreTradingReqDTO();
        reqDTO.setAmount(new BigDecimal("0.00"));
        reqDTO.setOrderGUID("orderGUID");
        reqDTO.setPayGUID("payGUID");
        reqDTO.setPayPowerId("payPowerId");
        reqDTO.setAttachData("attachData");
        reqDTO.setOutNotifyUrl("callBack");
        reqDTO.setAppId("appId");
        reqDTO.setTimestamp(0L);
        reqDTO.setDeveloperId("developerId");
        reqDTO.setSignature("signature");
        reqDTO.setMerchantUserId("empId");
        saasAggPayDTO.setReqDTO(reqDTO);
        saasAggPayDTO.setIsQuickReceipt(false);
        saasAggPayDTO.setIsLast(false);
        saasAggPayDTO.setSaasCallBackUrl("innerCallBackUrl");

        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid", "orderGUID"))
                .thenReturn(Mono.empty());
        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockAggPayConfig.getCallBack()).thenReturn("callBack");

        // Configure AggPayMapstruct.makeAttachVo(...).
        final AggPayAttachDataVO aggPayAttachDataVO = new AggPayAttachDataVO();
        aggPayAttachDataVO.setDeviceType(0);
        aggPayAttachDataVO.setEnterpriseGuid("enterpriseGuid");
        aggPayAttachDataVO.setStoreGuid("storeGuid");
        aggPayAttachDataVO.setSaasCallBackUrl("innerCallBackUrl");
        aggPayAttachDataVO.setIsQuickReceipt(false);
        aggPayAttachDataVO.setIsLast(false);
        aggPayAttachDataVO.setPlatformSource("掌控者智慧系统");
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.makeAttachVo(baseDTO)).thenReturn(aggPayAttachDataVO);

        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.preTradingAsync(...).
        final AggPayRespDTO aggPayRespDTO = new AggPayRespDTO();
        aggPayRespDTO.setCode("10000");
        aggPayRespDTO.setMsg("success");
        aggPayRespDTO.setPayGuid("payGUID");
        aggPayRespDTO.setOrderGuid("orderGUID");
        aggPayRespDTO.setPaymentAppId("appId");
        final Mono<AggPayRespDTO> aggPayRespDTOMono = Mono.just(aggPayRespDTO);
        final AggPayPreTradingReqDTO aggPayPreTradingReqDTO = new AggPayPreTradingReqDTO();
        aggPayPreTradingReqDTO.setAmount(new BigDecimal("0.00"));
        aggPayPreTradingReqDTO.setOrderGUID("orderGUID");
        aggPayPreTradingReqDTO.setPayGUID("payGUID");
        aggPayPreTradingReqDTO.setPayPowerId("payPowerId");
        aggPayPreTradingReqDTO.setAttachData("attachData");
        aggPayPreTradingReqDTO.setOutNotifyUrl("callBack");
        aggPayPreTradingReqDTO.setAppId("appId");
        aggPayPreTradingReqDTO.setTimestamp(0L);
        aggPayPreTradingReqDTO.setDeveloperId("developerId");
        aggPayPreTradingReqDTO.setSignature("signature");
        aggPayPreTradingReqDTO.setMerchantUserId("empId");
        when(mockAggPayRpcService.preTradingAsync(aggPayPreTradingReqDTO)).thenReturn(aggPayRespDTOMono);

        // Configure AggPayMapstruct.createPollingDto(...).
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggPayPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggPayPollingDTO.setAppId("appId");
        aggPayPollingDTO.setTimestamp(0L);
        aggPayPollingDTO.setDeveloperId("developerId");
        aggPayPollingDTO.setSignature("signature");
        final AggPayPreTradingReqDTO payPreTradingReqDTO = new AggPayPreTradingReqDTO();
        payPreTradingReqDTO.setAmount(new BigDecimal("0.00"));
        payPreTradingReqDTO.setOrderGUID("orderGUID");
        payPreTradingReqDTO.setPayGUID("payGUID");
        payPreTradingReqDTO.setPayPowerId("payPowerId");
        payPreTradingReqDTO.setAttachData("attachData");
        payPreTradingReqDTO.setOutNotifyUrl("callBack");
        payPreTradingReqDTO.setAppId("appId");
        payPreTradingReqDTO.setTimestamp(0L);
        payPreTradingReqDTO.setDeveloperId("developerId");
        payPreTradingReqDTO.setSignature("signature");
        payPreTradingReqDTO.setMerchantUserId("empId");
        when(mockAggPayMapstruct.createPollingDto(payPreTradingReqDTO)).thenReturn(aggPayPollingDTO);

        // Configure AggPayMapstruct.createBaseInfo(...).
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setTel("tel");
        final BaseDTO baseDTO1 = new BaseDTO();
        baseDTO1.setDeviceType(0);
        baseDTO1.setDeviceId("deviceId");
        baseDTO1.setEnterpriseGuid("enterpriseGuid");
        baseDTO1.setEnterpriseName("enterpriseName");
        baseDTO1.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.createBaseInfo(baseDTO1)).thenReturn(baseInfo);

        // Run the test
        final Mono<AggPayRespDTO> result = aggPayServiceImplUnderTest.prePay(saasAggPayDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("orderGUID", "payGUID", pollingRespDTO);

        // Confirm PollingService.startPrePayPolling(...).
        final AggPayPollingDTO aggPayPollingDTO1 = new AggPayPollingDTO();
        aggPayPollingDTO1.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggPayPollingDTO1.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggPayPollingDTO1.setAppId("appId");
        aggPayPollingDTO1.setTimestamp(0L);
        aggPayPollingDTO1.setDeveloperId("developerId");
        aggPayPollingDTO1.setSignature("signature");
        verify(mockPollingService).startPrePayPolling(aggPayPollingDTO1, HandlerPayBO.builder()
                .baseInfo(new BaseInfo())
                .paymentInfoDTO(new PaymentInfoDTO())
                .times(0)
                .enterpriseGuid("enterpriseGuid")
                .isQuickReceipt(false)
                .innerCallBackUrl("innerCallBackUrl")
                .handlerType(0)
                .payPowerId("payPowerId")
                .build());
    }

    @Test
    public void testPrePay_ErpConfigServiceReturnsError() {
        // Setup
        final SaasAggPayDTO saasAggPayDTO = new SaasAggPayDTO();
        saasAggPayDTO.setDeviceType(0);
        saasAggPayDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggPayDTO.setStoreGuid("storeGuid");
        final AggPayPreTradingReqDTO reqDTO = new AggPayPreTradingReqDTO();
        reqDTO.setAmount(new BigDecimal("0.00"));
        reqDTO.setOrderGUID("orderGUID");
        reqDTO.setPayGUID("payGUID");
        reqDTO.setPayPowerId("payPowerId");
        reqDTO.setAttachData("attachData");
        reqDTO.setOutNotifyUrl("callBack");
        reqDTO.setAppId("appId");
        reqDTO.setTimestamp(0L);
        reqDTO.setDeveloperId("developerId");
        reqDTO.setSignature("signature");
        reqDTO.setMerchantUserId("empId");
        saasAggPayDTO.setReqDTO(reqDTO);
        saasAggPayDTO.setIsQuickReceipt(false);
        saasAggPayDTO.setIsLast(false);
        saasAggPayDTO.setSaasCallBackUrl("innerCallBackUrl");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.error(new Exception("message"));
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid", "orderGUID"))
                .thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockAggPayConfig.getCallBack()).thenReturn("callBack");

        // Configure AggPayMapstruct.makeAttachVo(...).
        final AggPayAttachDataVO aggPayAttachDataVO = new AggPayAttachDataVO();
        aggPayAttachDataVO.setDeviceType(0);
        aggPayAttachDataVO.setEnterpriseGuid("enterpriseGuid");
        aggPayAttachDataVO.setStoreGuid("storeGuid");
        aggPayAttachDataVO.setSaasCallBackUrl("innerCallBackUrl");
        aggPayAttachDataVO.setIsQuickReceipt(false);
        aggPayAttachDataVO.setIsLast(false);
        aggPayAttachDataVO.setPlatformSource("掌控者智慧系统");
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.makeAttachVo(baseDTO)).thenReturn(aggPayAttachDataVO);

        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.preTradingAsync(...).
        final AggPayRespDTO aggPayRespDTO = new AggPayRespDTO();
        aggPayRespDTO.setCode("10000");
        aggPayRespDTO.setMsg("success");
        aggPayRespDTO.setPayGuid("payGUID");
        aggPayRespDTO.setOrderGuid("orderGUID");
        aggPayRespDTO.setPaymentAppId("appId");
        final Mono<AggPayRespDTO> aggPayRespDTOMono = Mono.just(aggPayRespDTO);
        final AggPayPreTradingReqDTO aggPayPreTradingReqDTO = new AggPayPreTradingReqDTO();
        aggPayPreTradingReqDTO.setAmount(new BigDecimal("0.00"));
        aggPayPreTradingReqDTO.setOrderGUID("orderGUID");
        aggPayPreTradingReqDTO.setPayGUID("payGUID");
        aggPayPreTradingReqDTO.setPayPowerId("payPowerId");
        aggPayPreTradingReqDTO.setAttachData("attachData");
        aggPayPreTradingReqDTO.setOutNotifyUrl("callBack");
        aggPayPreTradingReqDTO.setAppId("appId");
        aggPayPreTradingReqDTO.setTimestamp(0L);
        aggPayPreTradingReqDTO.setDeveloperId("developerId");
        aggPayPreTradingReqDTO.setSignature("signature");
        aggPayPreTradingReqDTO.setMerchantUserId("empId");
        when(mockAggPayRpcService.preTradingAsync(aggPayPreTradingReqDTO)).thenReturn(aggPayRespDTOMono);

        // Configure AggPayMapstruct.createPollingDto(...).
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggPayPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggPayPollingDTO.setAppId("appId");
        aggPayPollingDTO.setTimestamp(0L);
        aggPayPollingDTO.setDeveloperId("developerId");
        aggPayPollingDTO.setSignature("signature");
        final AggPayPreTradingReqDTO payPreTradingReqDTO = new AggPayPreTradingReqDTO();
        payPreTradingReqDTO.setAmount(new BigDecimal("0.00"));
        payPreTradingReqDTO.setOrderGUID("orderGUID");
        payPreTradingReqDTO.setPayGUID("payGUID");
        payPreTradingReqDTO.setPayPowerId("payPowerId");
        payPreTradingReqDTO.setAttachData("attachData");
        payPreTradingReqDTO.setOutNotifyUrl("callBack");
        payPreTradingReqDTO.setAppId("appId");
        payPreTradingReqDTO.setTimestamp(0L);
        payPreTradingReqDTO.setDeveloperId("developerId");
        payPreTradingReqDTO.setSignature("signature");
        payPreTradingReqDTO.setMerchantUserId("empId");
        when(mockAggPayMapstruct.createPollingDto(payPreTradingReqDTO)).thenReturn(aggPayPollingDTO);

        // Configure AggPayMapstruct.createBaseInfo(...).
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setTel("tel");
        final BaseDTO baseDTO1 = new BaseDTO();
        baseDTO1.setDeviceType(0);
        baseDTO1.setDeviceId("deviceId");
        baseDTO1.setEnterpriseGuid("enterpriseGuid");
        baseDTO1.setEnterpriseName("enterpriseName");
        baseDTO1.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.createBaseInfo(baseDTO1)).thenReturn(baseInfo);

        // Run the test
        final Mono<AggPayRespDTO> result = aggPayServiceImplUnderTest.prePay(saasAggPayDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("orderGUID", "payGUID", pollingRespDTO);

        // Confirm PollingService.startPrePayPolling(...).
        final AggPayPollingDTO aggPayPollingDTO1 = new AggPayPollingDTO();
        aggPayPollingDTO1.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggPayPollingDTO1.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggPayPollingDTO1.setAppId("appId");
        aggPayPollingDTO1.setTimestamp(0L);
        aggPayPollingDTO1.setDeveloperId("developerId");
        aggPayPollingDTO1.setSignature("signature");
        verify(mockPollingService).startPrePayPolling(aggPayPollingDTO1, HandlerPayBO.builder()
                .baseInfo(new BaseInfo())
                .paymentInfoDTO(new PaymentInfoDTO())
                .times(0)
                .enterpriseGuid("enterpriseGuid")
                .isQuickReceipt(false)
                .innerCallBackUrl("innerCallBackUrl")
                .handlerType(0)
                .payPowerId("payPowerId")
                .build());
    }

    @Test
    public void testPrePay_AggPayRpcServiceReturnsNoItem() {
        // Setup
        final SaasAggPayDTO saasAggPayDTO = new SaasAggPayDTO();
        saasAggPayDTO.setDeviceType(0);
        saasAggPayDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggPayDTO.setStoreGuid("storeGuid");
        final AggPayPreTradingReqDTO reqDTO = new AggPayPreTradingReqDTO();
        reqDTO.setAmount(new BigDecimal("0.00"));
        reqDTO.setOrderGUID("orderGUID");
        reqDTO.setPayGUID("payGUID");
        reqDTO.setPayPowerId("payPowerId");
        reqDTO.setAttachData("attachData");
        reqDTO.setOutNotifyUrl("callBack");
        reqDTO.setAppId("appId");
        reqDTO.setTimestamp(0L);
        reqDTO.setDeveloperId("developerId");
        reqDTO.setSignature("signature");
        reqDTO.setMerchantUserId("empId");
        saasAggPayDTO.setReqDTO(reqDTO);
        saasAggPayDTO.setIsQuickReceipt(false);
        saasAggPayDTO.setIsLast(false);
        saasAggPayDTO.setSaasCallBackUrl("innerCallBackUrl");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid", "orderGUID"))
                .thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockAggPayConfig.getCallBack()).thenReturn("callBack");

        // Configure AggPayMapstruct.makeAttachVo(...).
        final AggPayAttachDataVO aggPayAttachDataVO = new AggPayAttachDataVO();
        aggPayAttachDataVO.setDeviceType(0);
        aggPayAttachDataVO.setEnterpriseGuid("enterpriseGuid");
        aggPayAttachDataVO.setStoreGuid("storeGuid");
        aggPayAttachDataVO.setSaasCallBackUrl("innerCallBackUrl");
        aggPayAttachDataVO.setIsQuickReceipt(false);
        aggPayAttachDataVO.setIsLast(false);
        aggPayAttachDataVO.setPlatformSource("掌控者智慧系统");
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.makeAttachVo(baseDTO)).thenReturn(aggPayAttachDataVO);

        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.preTradingAsync(...).
        final AggPayPreTradingReqDTO aggPayPreTradingReqDTO = new AggPayPreTradingReqDTO();
        aggPayPreTradingReqDTO.setAmount(new BigDecimal("0.00"));
        aggPayPreTradingReqDTO.setOrderGUID("orderGUID");
        aggPayPreTradingReqDTO.setPayGUID("payGUID");
        aggPayPreTradingReqDTO.setPayPowerId("payPowerId");
        aggPayPreTradingReqDTO.setAttachData("attachData");
        aggPayPreTradingReqDTO.setOutNotifyUrl("callBack");
        aggPayPreTradingReqDTO.setAppId("appId");
        aggPayPreTradingReqDTO.setTimestamp(0L);
        aggPayPreTradingReqDTO.setDeveloperId("developerId");
        aggPayPreTradingReqDTO.setSignature("signature");
        aggPayPreTradingReqDTO.setMerchantUserId("empId");
        when(mockAggPayRpcService.preTradingAsync(aggPayPreTradingReqDTO)).thenReturn(Mono.empty());

        // Configure AggPayMapstruct.createPollingDto(...).
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggPayPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggPayPollingDTO.setAppId("appId");
        aggPayPollingDTO.setTimestamp(0L);
        aggPayPollingDTO.setDeveloperId("developerId");
        aggPayPollingDTO.setSignature("signature");
        final AggPayPreTradingReqDTO payPreTradingReqDTO = new AggPayPreTradingReqDTO();
        payPreTradingReqDTO.setAmount(new BigDecimal("0.00"));
        payPreTradingReqDTO.setOrderGUID("orderGUID");
        payPreTradingReqDTO.setPayGUID("payGUID");
        payPreTradingReqDTO.setPayPowerId("payPowerId");
        payPreTradingReqDTO.setAttachData("attachData");
        payPreTradingReqDTO.setOutNotifyUrl("callBack");
        payPreTradingReqDTO.setAppId("appId");
        payPreTradingReqDTO.setTimestamp(0L);
        payPreTradingReqDTO.setDeveloperId("developerId");
        payPreTradingReqDTO.setSignature("signature");
        payPreTradingReqDTO.setMerchantUserId("empId");
        when(mockAggPayMapstruct.createPollingDto(payPreTradingReqDTO)).thenReturn(aggPayPollingDTO);

        // Configure AggPayMapstruct.createBaseInfo(...).
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setTel("tel");
        final BaseDTO baseDTO1 = new BaseDTO();
        baseDTO1.setDeviceType(0);
        baseDTO1.setDeviceId("deviceId");
        baseDTO1.setEnterpriseGuid("enterpriseGuid");
        baseDTO1.setEnterpriseName("enterpriseName");
        baseDTO1.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.createBaseInfo(baseDTO1)).thenReturn(baseInfo);

        // Run the test
        final Mono<AggPayRespDTO> result = aggPayServiceImplUnderTest.prePay(saasAggPayDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("orderGUID", "payGUID", pollingRespDTO);

        // Confirm PollingService.startPrePayPolling(...).
        final AggPayPollingDTO aggPayPollingDTO1 = new AggPayPollingDTO();
        aggPayPollingDTO1.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggPayPollingDTO1.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggPayPollingDTO1.setAppId("appId");
        aggPayPollingDTO1.setTimestamp(0L);
        aggPayPollingDTO1.setDeveloperId("developerId");
        aggPayPollingDTO1.setSignature("signature");
        verify(mockPollingService).startPrePayPolling(aggPayPollingDTO1, HandlerPayBO.builder()
                .baseInfo(new BaseInfo())
                .paymentInfoDTO(new PaymentInfoDTO())
                .times(0)
                .enterpriseGuid("enterpriseGuid")
                .isQuickReceipt(false)
                .innerCallBackUrl("innerCallBackUrl")
                .handlerType(0)
                .payPowerId("payPowerId")
                .build());
    }

    @Test
    public void testPrePay_AggPayRpcServiceReturnsError() {
        // Setup
        final SaasAggPayDTO saasAggPayDTO = new SaasAggPayDTO();
        saasAggPayDTO.setDeviceType(0);
        saasAggPayDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggPayDTO.setStoreGuid("storeGuid");
        final AggPayPreTradingReqDTO reqDTO = new AggPayPreTradingReqDTO();
        reqDTO.setAmount(new BigDecimal("0.00"));
        reqDTO.setOrderGUID("orderGUID");
        reqDTO.setPayGUID("payGUID");
        reqDTO.setPayPowerId("payPowerId");
        reqDTO.setAttachData("attachData");
        reqDTO.setOutNotifyUrl("callBack");
        reqDTO.setAppId("appId");
        reqDTO.setTimestamp(0L);
        reqDTO.setDeveloperId("developerId");
        reqDTO.setSignature("signature");
        reqDTO.setMerchantUserId("empId");
        saasAggPayDTO.setReqDTO(reqDTO);
        saasAggPayDTO.setIsQuickReceipt(false);
        saasAggPayDTO.setIsLast(false);
        saasAggPayDTO.setSaasCallBackUrl("innerCallBackUrl");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid", "orderGUID"))
                .thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockAggPayConfig.getCallBack()).thenReturn("callBack");

        // Configure AggPayMapstruct.makeAttachVo(...).
        final AggPayAttachDataVO aggPayAttachDataVO = new AggPayAttachDataVO();
        aggPayAttachDataVO.setDeviceType(0);
        aggPayAttachDataVO.setEnterpriseGuid("enterpriseGuid");
        aggPayAttachDataVO.setStoreGuid("storeGuid");
        aggPayAttachDataVO.setSaasCallBackUrl("innerCallBackUrl");
        aggPayAttachDataVO.setIsQuickReceipt(false);
        aggPayAttachDataVO.setIsLast(false);
        aggPayAttachDataVO.setPlatformSource("掌控者智慧系统");
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.makeAttachVo(baseDTO)).thenReturn(aggPayAttachDataVO);

        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.preTradingAsync(...).
        final Mono<AggPayRespDTO> aggPayRespDTOMono = Mono.error(new Exception("message"));
        final AggPayPreTradingReqDTO aggPayPreTradingReqDTO = new AggPayPreTradingReqDTO();
        aggPayPreTradingReqDTO.setAmount(new BigDecimal("0.00"));
        aggPayPreTradingReqDTO.setOrderGUID("orderGUID");
        aggPayPreTradingReqDTO.setPayGUID("payGUID");
        aggPayPreTradingReqDTO.setPayPowerId("payPowerId");
        aggPayPreTradingReqDTO.setAttachData("attachData");
        aggPayPreTradingReqDTO.setOutNotifyUrl("callBack");
        aggPayPreTradingReqDTO.setAppId("appId");
        aggPayPreTradingReqDTO.setTimestamp(0L);
        aggPayPreTradingReqDTO.setDeveloperId("developerId");
        aggPayPreTradingReqDTO.setSignature("signature");
        aggPayPreTradingReqDTO.setMerchantUserId("empId");
        when(mockAggPayRpcService.preTradingAsync(aggPayPreTradingReqDTO)).thenReturn(aggPayRespDTOMono);

        // Configure AggPayMapstruct.createPollingDto(...).
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggPayPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggPayPollingDTO.setAppId("appId");
        aggPayPollingDTO.setTimestamp(0L);
        aggPayPollingDTO.setDeveloperId("developerId");
        aggPayPollingDTO.setSignature("signature");
        final AggPayPreTradingReqDTO payPreTradingReqDTO = new AggPayPreTradingReqDTO();
        payPreTradingReqDTO.setAmount(new BigDecimal("0.00"));
        payPreTradingReqDTO.setOrderGUID("orderGUID");
        payPreTradingReqDTO.setPayGUID("payGUID");
        payPreTradingReqDTO.setPayPowerId("payPowerId");
        payPreTradingReqDTO.setAttachData("attachData");
        payPreTradingReqDTO.setOutNotifyUrl("callBack");
        payPreTradingReqDTO.setAppId("appId");
        payPreTradingReqDTO.setTimestamp(0L);
        payPreTradingReqDTO.setDeveloperId("developerId");
        payPreTradingReqDTO.setSignature("signature");
        payPreTradingReqDTO.setMerchantUserId("empId");
        when(mockAggPayMapstruct.createPollingDto(payPreTradingReqDTO)).thenReturn(aggPayPollingDTO);

        // Configure AggPayMapstruct.createBaseInfo(...).
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setTel("tel");
        final BaseDTO baseDTO1 = new BaseDTO();
        baseDTO1.setDeviceType(0);
        baseDTO1.setDeviceId("deviceId");
        baseDTO1.setEnterpriseGuid("enterpriseGuid");
        baseDTO1.setEnterpriseName("enterpriseName");
        baseDTO1.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.createBaseInfo(baseDTO1)).thenReturn(baseInfo);

        // Run the test
        final Mono<AggPayRespDTO> result = aggPayServiceImplUnderTest.prePay(saasAggPayDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("orderGUID", "payGUID", pollingRespDTO);

        // Confirm PollingService.startPrePayPolling(...).
        final AggPayPollingDTO aggPayPollingDTO1 = new AggPayPollingDTO();
        aggPayPollingDTO1.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggPayPollingDTO1.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggPayPollingDTO1.setAppId("appId");
        aggPayPollingDTO1.setTimestamp(0L);
        aggPayPollingDTO1.setDeveloperId("developerId");
        aggPayPollingDTO1.setSignature("signature");
        verify(mockPollingService).startPrePayPolling(aggPayPollingDTO1, HandlerPayBO.builder()
                .baseInfo(new BaseInfo())
                .paymentInfoDTO(new PaymentInfoDTO())
                .times(0)
                .enterpriseGuid("enterpriseGuid")
                .isQuickReceipt(false)
                .innerCallBackUrl("innerCallBackUrl")
                .handlerType(0)
                .payPowerId("payPowerId")
                .build());
    }

    @Test
    public void testPrePayPolling() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure RedisService.getPollingResp(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO);
        final SaasPollingDTO saasPollingDTO1 = new SaasPollingDTO();
        saasPollingDTO1.setDeviceType(0);
        saasPollingDTO1.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO1.setStoreGuid("storeGuid");
        saasPollingDTO1.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO1.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");
        when(mockRedisService.getPollingResp(saasPollingDTO1)).thenReturn(aggPayPollingRespDTOMono);

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.prePayQueryBankAsync(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO1 = new AggPayPollingRespDTO();
        aggPayPollingRespDTO1.setCode("10000");
        aggPayPollingRespDTO1.setMsg("success");
        aggPayPollingRespDTO1.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO1.setBody("body");
        aggPayPollingRespDTO1.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono1 = Mono.just(aggPayPollingRespDTO1);
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.prePayQueryBankAsync(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTOMono1);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.prePayPolling(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testPrePayPolling_RedisServiceGetPollingRespReturnsNoItem() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure RedisService.getPollingResp(...).
        final SaasPollingDTO saasPollingDTO1 = new SaasPollingDTO();
        saasPollingDTO1.setDeviceType(0);
        saasPollingDTO1.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO1.setStoreGuid("storeGuid");
        saasPollingDTO1.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO1.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");
        when(mockRedisService.getPollingResp(saasPollingDTO1)).thenReturn(Mono.empty());

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.prePayQueryBankAsync(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO);
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.prePayQueryBankAsync(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTOMono);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.prePayPolling(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testPrePayPolling_RedisServiceGetPollingRespReturnsError() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure RedisService.getPollingResp(...).
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.error(new Exception("message"));
        final SaasPollingDTO saasPollingDTO1 = new SaasPollingDTO();
        saasPollingDTO1.setDeviceType(0);
        saasPollingDTO1.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO1.setStoreGuid("storeGuid");
        saasPollingDTO1.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO1.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");
        when(mockRedisService.getPollingResp(saasPollingDTO1)).thenReturn(aggPayPollingRespDTOMono);

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.prePayQueryBankAsync(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono1 = Mono.just(aggPayPollingRespDTO);
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.prePayQueryBankAsync(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTOMono1);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.prePayPolling(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testPrePayPolling_ErpConfigServiceReturnsNoItem() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure RedisService.getPollingResp(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO);
        final SaasPollingDTO saasPollingDTO1 = new SaasPollingDTO();
        saasPollingDTO1.setDeviceType(0);
        saasPollingDTO1.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO1.setStoreGuid("storeGuid");
        saasPollingDTO1.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO1.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");
        when(mockRedisService.getPollingResp(saasPollingDTO1)).thenReturn(aggPayPollingRespDTOMono);

        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(Mono.empty());
        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.prePayQueryBankAsync(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO1 = new AggPayPollingRespDTO();
        aggPayPollingRespDTO1.setCode("10000");
        aggPayPollingRespDTO1.setMsg("success");
        aggPayPollingRespDTO1.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO1.setBody("body");
        aggPayPollingRespDTO1.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono1 = Mono.just(aggPayPollingRespDTO1);
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.prePayQueryBankAsync(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTOMono1);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.prePayPolling(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testPrePayPolling_ErpConfigServiceReturnsError() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure RedisService.getPollingResp(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO);
        final SaasPollingDTO saasPollingDTO1 = new SaasPollingDTO();
        saasPollingDTO1.setDeviceType(0);
        saasPollingDTO1.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO1.setStoreGuid("storeGuid");
        saasPollingDTO1.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO1.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");
        when(mockRedisService.getPollingResp(saasPollingDTO1)).thenReturn(aggPayPollingRespDTOMono);

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.error(new Exception("message"));
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.prePayQueryBankAsync(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO1 = new AggPayPollingRespDTO();
        aggPayPollingRespDTO1.setCode("10000");
        aggPayPollingRespDTO1.setMsg("success");
        aggPayPollingRespDTO1.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO1.setBody("body");
        aggPayPollingRespDTO1.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono1 = Mono.just(aggPayPollingRespDTO1);
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.prePayQueryBankAsync(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTOMono1);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.prePayPolling(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testPrePayPolling_AggPayRpcServiceReturnsNoItem() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure RedisService.getPollingResp(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO);
        final SaasPollingDTO saasPollingDTO1 = new SaasPollingDTO();
        saasPollingDTO1.setDeviceType(0);
        saasPollingDTO1.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO1.setStoreGuid("storeGuid");
        saasPollingDTO1.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO1.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");
        when(mockRedisService.getPollingResp(saasPollingDTO1)).thenReturn(aggPayPollingRespDTOMono);

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.prePayQueryBankAsync(...).
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.prePayQueryBankAsync(pollingJHPayDTO)).thenReturn(Mono.empty());

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.prePayPolling(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testPrePayPolling_AggPayRpcServiceReturnsError() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure RedisService.getPollingResp(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO);
        final SaasPollingDTO saasPollingDTO1 = new SaasPollingDTO();
        saasPollingDTO1.setDeviceType(0);
        saasPollingDTO1.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO1.setStoreGuid("storeGuid");
        saasPollingDTO1.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO1.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");
        when(mockRedisService.getPollingResp(saasPollingDTO1)).thenReturn(aggPayPollingRespDTOMono);

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.prePayQueryBankAsync(...).
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono1 = Mono.error(new Exception("message"));
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.prePayQueryBankAsync(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTOMono1);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.prePayPolling(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testPrePayH5Polling() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.prePayQueryBankAsync(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO);
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.prePayQueryBankAsync(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTOMono);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.prePayH5Polling(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testPrePayH5Polling_ErpConfigServiceReturnsNoItem() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(Mono.empty());
        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.prePayQueryBankAsync(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO);
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.prePayQueryBankAsync(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTOMono);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.prePayH5Polling(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testPrePayH5Polling_ErpConfigServiceReturnsError() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.error(new Exception("message"));
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.prePayQueryBankAsync(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO);
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.prePayQueryBankAsync(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTOMono);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.prePayH5Polling(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testPrePayH5Polling_AggPayRpcServiceReturnsNoItem() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.prePayQueryBankAsync(...).
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.prePayQueryBankAsync(pollingJHPayDTO)).thenReturn(Mono.empty());

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.prePayH5Polling(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testPrePayH5Polling_AggPayRpcServiceReturnsError() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.prePayQueryBankAsync(...).
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.error(new Exception("message"));
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.prePayQueryBankAsync(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTOMono);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.prePayH5Polling(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testQueryPrePayResult() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.prePayQueryBankAsync(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO);
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.prePayQueryBankAsync(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTOMono);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.queryPrePayResult(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testQueryPrePayResult_ErpConfigServiceReturnsNoItem() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(Mono.empty());
        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.prePayQueryBankAsync(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO);
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.prePayQueryBankAsync(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTOMono);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.queryPrePayResult(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testQueryPrePayResult_ErpConfigServiceReturnsError() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.error(new Exception("message"));
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.prePayQueryBankAsync(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO);
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.prePayQueryBankAsync(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTOMono);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.queryPrePayResult(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testQueryPrePayResult_AggPayRpcServiceReturnsNoItem() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.prePayQueryBankAsync(...).
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.prePayQueryBankAsync(pollingJHPayDTO)).thenReturn(Mono.empty());

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.queryPrePayResult(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testQueryPrePayResult_AggPayRpcServiceReturnsError() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.prePayQueryBankAsync(...).
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.error(new Exception("message"));
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.prePayQueryBankAsync(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTOMono);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.queryPrePayResult(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testQueryPayState() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.payPayQueryBankAsync(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO);
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.payPayQueryBankAsync(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTOMono);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.queryPayState(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testQueryPayState_ErpConfigServiceReturnsNoItem() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(Mono.empty());
        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.payPayQueryBankAsync(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO);
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.payPayQueryBankAsync(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTOMono);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.queryPayState(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testQueryPayState_ErpConfigServiceReturnsError() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.error(new Exception("message"));
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.payPayQueryBankAsync(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO);
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.payPayQueryBankAsync(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTOMono);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.queryPayState(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testQueryPayState_AggPayRpcServiceReturnsNoItem() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.payPayQueryBankAsync(...).
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.payPayQueryBankAsync(pollingJHPayDTO)).thenReturn(Mono.empty());

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.queryPayState(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testQueryPayState_AggPayRpcServiceReturnsError() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.payPayQueryBankAsync(...).
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.error(new Exception("message"));
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        pollingJHPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        pollingJHPayDTO.setAppId("appId");
        pollingJHPayDTO.setTimestamp(0L);
        pollingJHPayDTO.setDeveloperId("developerId");
        pollingJHPayDTO.setSignature("signature");
        when(mockAggPayRpcService.payPayQueryBankAsync(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTOMono);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.queryPayState(saasPollingDTO);

        // Verify the results
        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", pollingRespDTO);
    }

    @Test
    public void testRefund() {
        // Setup
        final SaasAggRefundDTO saasAggRefundDTO = new SaasAggRefundDTO();
        saasAggRefundDTO.setDeviceType(0);
        saasAggRefundDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggRefundDTO.setStoreGuid("storeGuid");
        final AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
        aggRefundReqDTO.setPayGUID("payGUID");
        aggRefundReqDTO.setOrderGUID("orderGUID");
        aggRefundReqDTO.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO.setAttachData("attachData");
        aggRefundReqDTO.setAppId("appId");
        aggRefundReqDTO.setTimestamp(0L);
        aggRefundReqDTO.setDeveloperId("developerId");
        aggRefundReqDTO.setSignature("signature");
        saasAggRefundDTO.setAggRefundReqDTO(aggRefundReqDTO);
        saasAggRefundDTO.setIsQuickReceipt(false);

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid", "orderGUID"))
                .thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.refundAsync(...).
        final AggRefundRespDTO aggRefundRespDTO = new AggRefundRespDTO();
        aggRefundRespDTO.setCode("code");
        aggRefundRespDTO.setMsg("msg");
        aggRefundRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundRespDTO.setRefOrderNo("refOrderNo");
        aggRefundRespDTO.setState("state");
        final Mono<AggRefundRespDTO> aggRefundRespDTOMono = Mono.just(aggRefundRespDTO);
        final AggRefundReqDTO aggRefundReqDTO1 = new AggRefundReqDTO();
        aggRefundReqDTO1.setPayGUID("payGUID");
        aggRefundReqDTO1.setOrderGUID("orderGUID");
        aggRefundReqDTO1.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO1.setAttachData("attachData");
        aggRefundReqDTO1.setAppId("appId");
        aggRefundReqDTO1.setTimestamp(0L);
        aggRefundReqDTO1.setDeveloperId("developerId");
        aggRefundReqDTO1.setSignature("signature");
        when(mockAggPayRpcService.refundAsync(aggRefundReqDTO1)).thenReturn(aggRefundRespDTOMono);

        // Configure AggPayMapstruct.createRefundPollingDto(...).
        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO.setAppId("appId");
        aggRefundPollingDTO.setTimestamp(0L);
        aggRefundPollingDTO.setDeveloperId("developerId");
        aggRefundPollingDTO.setSignature("signature");
        final AggRefundReqDTO aggRefundReqDTO2 = new AggRefundReqDTO();
        aggRefundReqDTO2.setPayGUID("payGUID");
        aggRefundReqDTO2.setOrderGUID("orderGUID");
        aggRefundReqDTO2.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO2.setAttachData("attachData");
        aggRefundReqDTO2.setAppId("appId");
        aggRefundReqDTO2.setTimestamp(0L);
        aggRefundReqDTO2.setDeveloperId("developerId");
        aggRefundReqDTO2.setSignature("signature");
        when(mockAggPayMapstruct.createRefundPollingDto(aggRefundReqDTO2)).thenReturn(aggRefundPollingDTO);

        // Run the test
        final Mono<AggRefundRespDTO> result = aggPayServiceImplUnderTest.refund(saasAggRefundDTO);

        // Verify the results
        // Confirm PayRecordService.update(...).
        final PayRecordDO entity = new PayRecordDO();
        entity.setStoreGuid("storeGuid");
        entity.setOrderHolderNo("orderHolderNo");
        entity.setPaySt("4");
        entity.setGmtTimePaid(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setRefOrderNo("refOrderNo");
        entity.setGmtRefund(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockPayRecordService).update(eq(entity), any(LambdaQueryWrapper.class));

        // Confirm RedisService.putRefundPollingResp(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode("10000");
        aggRefundPollingRespDTO.setMsg("退款中");
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setOrderNo("orderNo");
        aggRefundPollingRespDTO.setMchntId("mchntId");
        verify(mockRedisService).putRefundPollingResp("orderGUID", "payGUID", aggRefundPollingRespDTO);

        // Confirm PollingService.startRefundPolling(...).
        final SaasAggRefundDTO saasAggRefundDTO1 = new SaasAggRefundDTO();
        saasAggRefundDTO1.setDeviceType(0);
        saasAggRefundDTO1.setEnterpriseGuid("enterpriseGuid");
        saasAggRefundDTO1.setStoreGuid("storeGuid");
        final AggRefundReqDTO aggRefundReqDTO3 = new AggRefundReqDTO();
        aggRefundReqDTO3.setPayGUID("payGUID");
        aggRefundReqDTO3.setOrderGUID("orderGUID");
        aggRefundReqDTO3.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO3.setAttachData("attachData");
        aggRefundReqDTO3.setAppId("appId");
        aggRefundReqDTO3.setTimestamp(0L);
        aggRefundReqDTO3.setDeveloperId("developerId");
        aggRefundReqDTO3.setSignature("signature");
        saasAggRefundDTO1.setAggRefundReqDTO(aggRefundReqDTO3);
        saasAggRefundDTO1.setIsQuickReceipt(false);
        final AggRefundPollingDTO aggRefundPollingDTO1 = new AggRefundPollingDTO();
        aggRefundPollingDTO1.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO1.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO1.setAppId("appId");
        aggRefundPollingDTO1.setTimestamp(0L);
        aggRefundPollingDTO1.setDeveloperId("developerId");
        aggRefundPollingDTO1.setSignature("signature");
        verify(mockPollingService).startRefundPolling(saasAggRefundDTO1, aggRefundPollingDTO1);
    }

    @Test
    public void testRefund_ErpConfigServiceReturnsNoItem() {
        // Setup
        final SaasAggRefundDTO saasAggRefundDTO = new SaasAggRefundDTO();
        saasAggRefundDTO.setDeviceType(0);
        saasAggRefundDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggRefundDTO.setStoreGuid("storeGuid");
        final AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
        aggRefundReqDTO.setPayGUID("payGUID");
        aggRefundReqDTO.setOrderGUID("orderGUID");
        aggRefundReqDTO.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO.setAttachData("attachData");
        aggRefundReqDTO.setAppId("appId");
        aggRefundReqDTO.setTimestamp(0L);
        aggRefundReqDTO.setDeveloperId("developerId");
        aggRefundReqDTO.setSignature("signature");
        saasAggRefundDTO.setAggRefundReqDTO(aggRefundReqDTO);
        saasAggRefundDTO.setIsQuickReceipt(false);

        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid", "orderGUID"))
                .thenReturn(Mono.empty());
        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.refundAsync(...).
        final AggRefundRespDTO aggRefundRespDTO = new AggRefundRespDTO();
        aggRefundRespDTO.setCode("code");
        aggRefundRespDTO.setMsg("msg");
        aggRefundRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundRespDTO.setRefOrderNo("refOrderNo");
        aggRefundRespDTO.setState("state");
        final Mono<AggRefundRespDTO> aggRefundRespDTOMono = Mono.just(aggRefundRespDTO);
        final AggRefundReqDTO aggRefundReqDTO1 = new AggRefundReqDTO();
        aggRefundReqDTO1.setPayGUID("payGUID");
        aggRefundReqDTO1.setOrderGUID("orderGUID");
        aggRefundReqDTO1.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO1.setAttachData("attachData");
        aggRefundReqDTO1.setAppId("appId");
        aggRefundReqDTO1.setTimestamp(0L);
        aggRefundReqDTO1.setDeveloperId("developerId");
        aggRefundReqDTO1.setSignature("signature");
        when(mockAggPayRpcService.refundAsync(aggRefundReqDTO1)).thenReturn(aggRefundRespDTOMono);

        // Configure AggPayMapstruct.createRefundPollingDto(...).
        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO.setAppId("appId");
        aggRefundPollingDTO.setTimestamp(0L);
        aggRefundPollingDTO.setDeveloperId("developerId");
        aggRefundPollingDTO.setSignature("signature");
        final AggRefundReqDTO aggRefundReqDTO2 = new AggRefundReqDTO();
        aggRefundReqDTO2.setPayGUID("payGUID");
        aggRefundReqDTO2.setOrderGUID("orderGUID");
        aggRefundReqDTO2.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO2.setAttachData("attachData");
        aggRefundReqDTO2.setAppId("appId");
        aggRefundReqDTO2.setTimestamp(0L);
        aggRefundReqDTO2.setDeveloperId("developerId");
        aggRefundReqDTO2.setSignature("signature");
        when(mockAggPayMapstruct.createRefundPollingDto(aggRefundReqDTO2)).thenReturn(aggRefundPollingDTO);

        // Run the test
        final Mono<AggRefundRespDTO> result = aggPayServiceImplUnderTest.refund(saasAggRefundDTO);

        // Verify the results
        // Confirm PayRecordService.update(...).
        final PayRecordDO entity = new PayRecordDO();
        entity.setStoreGuid("storeGuid");
        entity.setOrderHolderNo("orderHolderNo");
        entity.setPaySt("4");
        entity.setGmtTimePaid(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setRefOrderNo("refOrderNo");
        entity.setGmtRefund(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockPayRecordService).update(eq(entity), any(LambdaQueryWrapper.class));

        // Confirm RedisService.putRefundPollingResp(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode("10000");
        aggRefundPollingRespDTO.setMsg("退款中");
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setOrderNo("orderNo");
        aggRefundPollingRespDTO.setMchntId("mchntId");
        verify(mockRedisService).putRefundPollingResp("orderGUID", "payGUID", aggRefundPollingRespDTO);

        // Confirm PollingService.startRefundPolling(...).
        final SaasAggRefundDTO saasAggRefundDTO1 = new SaasAggRefundDTO();
        saasAggRefundDTO1.setDeviceType(0);
        saasAggRefundDTO1.setEnterpriseGuid("enterpriseGuid");
        saasAggRefundDTO1.setStoreGuid("storeGuid");
        final AggRefundReqDTO aggRefundReqDTO3 = new AggRefundReqDTO();
        aggRefundReqDTO3.setPayGUID("payGUID");
        aggRefundReqDTO3.setOrderGUID("orderGUID");
        aggRefundReqDTO3.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO3.setAttachData("attachData");
        aggRefundReqDTO3.setAppId("appId");
        aggRefundReqDTO3.setTimestamp(0L);
        aggRefundReqDTO3.setDeveloperId("developerId");
        aggRefundReqDTO3.setSignature("signature");
        saasAggRefundDTO1.setAggRefundReqDTO(aggRefundReqDTO3);
        saasAggRefundDTO1.setIsQuickReceipt(false);
        final AggRefundPollingDTO aggRefundPollingDTO1 = new AggRefundPollingDTO();
        aggRefundPollingDTO1.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO1.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO1.setAppId("appId");
        aggRefundPollingDTO1.setTimestamp(0L);
        aggRefundPollingDTO1.setDeveloperId("developerId");
        aggRefundPollingDTO1.setSignature("signature");
        verify(mockPollingService).startRefundPolling(saasAggRefundDTO1, aggRefundPollingDTO1);
    }

    @Test
    public void testRefund_ErpConfigServiceReturnsError() {
        // Setup
        final SaasAggRefundDTO saasAggRefundDTO = new SaasAggRefundDTO();
        saasAggRefundDTO.setDeviceType(0);
        saasAggRefundDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggRefundDTO.setStoreGuid("storeGuid");
        final AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
        aggRefundReqDTO.setPayGUID("payGUID");
        aggRefundReqDTO.setOrderGUID("orderGUID");
        aggRefundReqDTO.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO.setAttachData("attachData");
        aggRefundReqDTO.setAppId("appId");
        aggRefundReqDTO.setTimestamp(0L);
        aggRefundReqDTO.setDeveloperId("developerId");
        aggRefundReqDTO.setSignature("signature");
        saasAggRefundDTO.setAggRefundReqDTO(aggRefundReqDTO);
        saasAggRefundDTO.setIsQuickReceipt(false);

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.error(new Exception("message"));
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid", "orderGUID"))
                .thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.refundAsync(...).
        final AggRefundRespDTO aggRefundRespDTO = new AggRefundRespDTO();
        aggRefundRespDTO.setCode("code");
        aggRefundRespDTO.setMsg("msg");
        aggRefundRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundRespDTO.setRefOrderNo("refOrderNo");
        aggRefundRespDTO.setState("state");
        final Mono<AggRefundRespDTO> aggRefundRespDTOMono = Mono.just(aggRefundRespDTO);
        final AggRefundReqDTO aggRefundReqDTO1 = new AggRefundReqDTO();
        aggRefundReqDTO1.setPayGUID("payGUID");
        aggRefundReqDTO1.setOrderGUID("orderGUID");
        aggRefundReqDTO1.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO1.setAttachData("attachData");
        aggRefundReqDTO1.setAppId("appId");
        aggRefundReqDTO1.setTimestamp(0L);
        aggRefundReqDTO1.setDeveloperId("developerId");
        aggRefundReqDTO1.setSignature("signature");
        when(mockAggPayRpcService.refundAsync(aggRefundReqDTO1)).thenReturn(aggRefundRespDTOMono);

        // Configure AggPayMapstruct.createRefundPollingDto(...).
        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO.setAppId("appId");
        aggRefundPollingDTO.setTimestamp(0L);
        aggRefundPollingDTO.setDeveloperId("developerId");
        aggRefundPollingDTO.setSignature("signature");
        final AggRefundReqDTO aggRefundReqDTO2 = new AggRefundReqDTO();
        aggRefundReqDTO2.setPayGUID("payGUID");
        aggRefundReqDTO2.setOrderGUID("orderGUID");
        aggRefundReqDTO2.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO2.setAttachData("attachData");
        aggRefundReqDTO2.setAppId("appId");
        aggRefundReqDTO2.setTimestamp(0L);
        aggRefundReqDTO2.setDeveloperId("developerId");
        aggRefundReqDTO2.setSignature("signature");
        when(mockAggPayMapstruct.createRefundPollingDto(aggRefundReqDTO2)).thenReturn(aggRefundPollingDTO);

        // Run the test
        final Mono<AggRefundRespDTO> result = aggPayServiceImplUnderTest.refund(saasAggRefundDTO);

        // Verify the results
        // Confirm PayRecordService.update(...).
        final PayRecordDO entity = new PayRecordDO();
        entity.setStoreGuid("storeGuid");
        entity.setOrderHolderNo("orderHolderNo");
        entity.setPaySt("4");
        entity.setGmtTimePaid(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setRefOrderNo("refOrderNo");
        entity.setGmtRefund(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockPayRecordService).update(eq(entity), any(LambdaQueryWrapper.class));

        // Confirm RedisService.putRefundPollingResp(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode("10000");
        aggRefundPollingRespDTO.setMsg("退款中");
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setOrderNo("orderNo");
        aggRefundPollingRespDTO.setMchntId("mchntId");
        verify(mockRedisService).putRefundPollingResp("orderGUID", "payGUID", aggRefundPollingRespDTO);

        // Confirm PollingService.startRefundPolling(...).
        final SaasAggRefundDTO saasAggRefundDTO1 = new SaasAggRefundDTO();
        saasAggRefundDTO1.setDeviceType(0);
        saasAggRefundDTO1.setEnterpriseGuid("enterpriseGuid");
        saasAggRefundDTO1.setStoreGuid("storeGuid");
        final AggRefundReqDTO aggRefundReqDTO3 = new AggRefundReqDTO();
        aggRefundReqDTO3.setPayGUID("payGUID");
        aggRefundReqDTO3.setOrderGUID("orderGUID");
        aggRefundReqDTO3.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO3.setAttachData("attachData");
        aggRefundReqDTO3.setAppId("appId");
        aggRefundReqDTO3.setTimestamp(0L);
        aggRefundReqDTO3.setDeveloperId("developerId");
        aggRefundReqDTO3.setSignature("signature");
        saasAggRefundDTO1.setAggRefundReqDTO(aggRefundReqDTO3);
        saasAggRefundDTO1.setIsQuickReceipt(false);
        final AggRefundPollingDTO aggRefundPollingDTO1 = new AggRefundPollingDTO();
        aggRefundPollingDTO1.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO1.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO1.setAppId("appId");
        aggRefundPollingDTO1.setTimestamp(0L);
        aggRefundPollingDTO1.setDeveloperId("developerId");
        aggRefundPollingDTO1.setSignature("signature");
        verify(mockPollingService).startRefundPolling(saasAggRefundDTO1, aggRefundPollingDTO1);
    }

    @Test
    public void testRefund_AggPayRpcServiceReturnsNoItem() {
        // Setup
        final SaasAggRefundDTO saasAggRefundDTO = new SaasAggRefundDTO();
        saasAggRefundDTO.setDeviceType(0);
        saasAggRefundDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggRefundDTO.setStoreGuid("storeGuid");
        final AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
        aggRefundReqDTO.setPayGUID("payGUID");
        aggRefundReqDTO.setOrderGUID("orderGUID");
        aggRefundReqDTO.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO.setAttachData("attachData");
        aggRefundReqDTO.setAppId("appId");
        aggRefundReqDTO.setTimestamp(0L);
        aggRefundReqDTO.setDeveloperId("developerId");
        aggRefundReqDTO.setSignature("signature");
        saasAggRefundDTO.setAggRefundReqDTO(aggRefundReqDTO);
        saasAggRefundDTO.setIsQuickReceipt(false);

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid", "orderGUID"))
                .thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.refundAsync(...).
        final AggRefundReqDTO aggRefundReqDTO1 = new AggRefundReqDTO();
        aggRefundReqDTO1.setPayGUID("payGUID");
        aggRefundReqDTO1.setOrderGUID("orderGUID");
        aggRefundReqDTO1.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO1.setAttachData("attachData");
        aggRefundReqDTO1.setAppId("appId");
        aggRefundReqDTO1.setTimestamp(0L);
        aggRefundReqDTO1.setDeveloperId("developerId");
        aggRefundReqDTO1.setSignature("signature");
        when(mockAggPayRpcService.refundAsync(aggRefundReqDTO1)).thenReturn(Mono.empty());

        // Configure AggPayMapstruct.createRefundPollingDto(...).
        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO.setAppId("appId");
        aggRefundPollingDTO.setTimestamp(0L);
        aggRefundPollingDTO.setDeveloperId("developerId");
        aggRefundPollingDTO.setSignature("signature");
        final AggRefundReqDTO aggRefundReqDTO2 = new AggRefundReqDTO();
        aggRefundReqDTO2.setPayGUID("payGUID");
        aggRefundReqDTO2.setOrderGUID("orderGUID");
        aggRefundReqDTO2.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO2.setAttachData("attachData");
        aggRefundReqDTO2.setAppId("appId");
        aggRefundReqDTO2.setTimestamp(0L);
        aggRefundReqDTO2.setDeveloperId("developerId");
        aggRefundReqDTO2.setSignature("signature");
        when(mockAggPayMapstruct.createRefundPollingDto(aggRefundReqDTO2)).thenReturn(aggRefundPollingDTO);

        // Run the test
        final Mono<AggRefundRespDTO> result = aggPayServiceImplUnderTest.refund(saasAggRefundDTO);

        // Verify the results
        // Confirm PayRecordService.update(...).
        final PayRecordDO entity = new PayRecordDO();
        entity.setStoreGuid("storeGuid");
        entity.setOrderHolderNo("orderHolderNo");
        entity.setPaySt("4");
        entity.setGmtTimePaid(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setRefOrderNo("refOrderNo");
        entity.setGmtRefund(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockPayRecordService).update(eq(entity), any(LambdaQueryWrapper.class));

        // Confirm RedisService.putRefundPollingResp(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode("10000");
        aggRefundPollingRespDTO.setMsg("退款中");
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setOrderNo("orderNo");
        aggRefundPollingRespDTO.setMchntId("mchntId");
        verify(mockRedisService).putRefundPollingResp("orderGUID", "payGUID", aggRefundPollingRespDTO);

        // Confirm PollingService.startRefundPolling(...).
        final SaasAggRefundDTO saasAggRefundDTO1 = new SaasAggRefundDTO();
        saasAggRefundDTO1.setDeviceType(0);
        saasAggRefundDTO1.setEnterpriseGuid("enterpriseGuid");
        saasAggRefundDTO1.setStoreGuid("storeGuid");
        final AggRefundReqDTO aggRefundReqDTO3 = new AggRefundReqDTO();
        aggRefundReqDTO3.setPayGUID("payGUID");
        aggRefundReqDTO3.setOrderGUID("orderGUID");
        aggRefundReqDTO3.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO3.setAttachData("attachData");
        aggRefundReqDTO3.setAppId("appId");
        aggRefundReqDTO3.setTimestamp(0L);
        aggRefundReqDTO3.setDeveloperId("developerId");
        aggRefundReqDTO3.setSignature("signature");
        saasAggRefundDTO1.setAggRefundReqDTO(aggRefundReqDTO3);
        saasAggRefundDTO1.setIsQuickReceipt(false);
        final AggRefundPollingDTO aggRefundPollingDTO1 = new AggRefundPollingDTO();
        aggRefundPollingDTO1.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO1.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO1.setAppId("appId");
        aggRefundPollingDTO1.setTimestamp(0L);
        aggRefundPollingDTO1.setDeveloperId("developerId");
        aggRefundPollingDTO1.setSignature("signature");
        verify(mockPollingService).startRefundPolling(saasAggRefundDTO1, aggRefundPollingDTO1);
    }

    @Test
    public void testRefund_AggPayRpcServiceReturnsError() {
        // Setup
        final SaasAggRefundDTO saasAggRefundDTO = new SaasAggRefundDTO();
        saasAggRefundDTO.setDeviceType(0);
        saasAggRefundDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggRefundDTO.setStoreGuid("storeGuid");
        final AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
        aggRefundReqDTO.setPayGUID("payGUID");
        aggRefundReqDTO.setOrderGUID("orderGUID");
        aggRefundReqDTO.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO.setAttachData("attachData");
        aggRefundReqDTO.setAppId("appId");
        aggRefundReqDTO.setTimestamp(0L);
        aggRefundReqDTO.setDeveloperId("developerId");
        aggRefundReqDTO.setSignature("signature");
        saasAggRefundDTO.setAggRefundReqDTO(aggRefundReqDTO);
        saasAggRefundDTO.setIsQuickReceipt(false);

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid", "orderGUID"))
                .thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.refundAsync(...).
        final Mono<AggRefundRespDTO> aggRefundRespDTOMono = Mono.error(new Exception("message"));
        final AggRefundReqDTO aggRefundReqDTO1 = new AggRefundReqDTO();
        aggRefundReqDTO1.setPayGUID("payGUID");
        aggRefundReqDTO1.setOrderGUID("orderGUID");
        aggRefundReqDTO1.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO1.setAttachData("attachData");
        aggRefundReqDTO1.setAppId("appId");
        aggRefundReqDTO1.setTimestamp(0L);
        aggRefundReqDTO1.setDeveloperId("developerId");
        aggRefundReqDTO1.setSignature("signature");
        when(mockAggPayRpcService.refundAsync(aggRefundReqDTO1)).thenReturn(aggRefundRespDTOMono);

        // Configure AggPayMapstruct.createRefundPollingDto(...).
        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO.setAppId("appId");
        aggRefundPollingDTO.setTimestamp(0L);
        aggRefundPollingDTO.setDeveloperId("developerId");
        aggRefundPollingDTO.setSignature("signature");
        final AggRefundReqDTO aggRefundReqDTO2 = new AggRefundReqDTO();
        aggRefundReqDTO2.setPayGUID("payGUID");
        aggRefundReqDTO2.setOrderGUID("orderGUID");
        aggRefundReqDTO2.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO2.setAttachData("attachData");
        aggRefundReqDTO2.setAppId("appId");
        aggRefundReqDTO2.setTimestamp(0L);
        aggRefundReqDTO2.setDeveloperId("developerId");
        aggRefundReqDTO2.setSignature("signature");
        when(mockAggPayMapstruct.createRefundPollingDto(aggRefundReqDTO2)).thenReturn(aggRefundPollingDTO);

        // Run the test
        final Mono<AggRefundRespDTO> result = aggPayServiceImplUnderTest.refund(saasAggRefundDTO);

        // Verify the results
        // Confirm PayRecordService.update(...).
        final PayRecordDO entity = new PayRecordDO();
        entity.setStoreGuid("storeGuid");
        entity.setOrderHolderNo("orderHolderNo");
        entity.setPaySt("4");
        entity.setGmtTimePaid(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setRefOrderNo("refOrderNo");
        entity.setGmtRefund(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockPayRecordService).update(eq(entity), any(LambdaQueryWrapper.class));

        // Confirm RedisService.putRefundPollingResp(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode("10000");
        aggRefundPollingRespDTO.setMsg("退款中");
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setOrderNo("orderNo");
        aggRefundPollingRespDTO.setMchntId("mchntId");
        verify(mockRedisService).putRefundPollingResp("orderGUID", "payGUID", aggRefundPollingRespDTO);

        // Confirm PollingService.startRefundPolling(...).
        final SaasAggRefundDTO saasAggRefundDTO1 = new SaasAggRefundDTO();
        saasAggRefundDTO1.setDeviceType(0);
        saasAggRefundDTO1.setEnterpriseGuid("enterpriseGuid");
        saasAggRefundDTO1.setStoreGuid("storeGuid");
        final AggRefundReqDTO aggRefundReqDTO3 = new AggRefundReqDTO();
        aggRefundReqDTO3.setPayGUID("payGUID");
        aggRefundReqDTO3.setOrderGUID("orderGUID");
        aggRefundReqDTO3.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO3.setAttachData("attachData");
        aggRefundReqDTO3.setAppId("appId");
        aggRefundReqDTO3.setTimestamp(0L);
        aggRefundReqDTO3.setDeveloperId("developerId");
        aggRefundReqDTO3.setSignature("signature");
        saasAggRefundDTO1.setAggRefundReqDTO(aggRefundReqDTO3);
        saasAggRefundDTO1.setIsQuickReceipt(false);
        final AggRefundPollingDTO aggRefundPollingDTO1 = new AggRefundPollingDTO();
        aggRefundPollingDTO1.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO1.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO1.setAppId("appId");
        aggRefundPollingDTO1.setTimestamp(0L);
        aggRefundPollingDTO1.setDeveloperId("developerId");
        aggRefundPollingDTO1.setSignature("signature");
        verify(mockPollingService).startRefundPolling(saasAggRefundDTO1, aggRefundPollingDTO1);
    }

    @Test
    public void testRefundPolling() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure RedisService.getRefundPollingResp(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode("10000");
        aggRefundPollingRespDTO.setMsg("退款中");
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setOrderNo("orderNo");
        aggRefundPollingRespDTO.setMchntId("mchntId");
        final Mono<AggRefundPollingRespDTO> aggRefundPollingRespDTOMono = Mono.just(aggRefundPollingRespDTO);
        final SaasPollingDTO saasPollingDTO1 = new SaasPollingDTO();
        saasPollingDTO1.setDeviceType(0);
        saasPollingDTO1.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO1.setStoreGuid("storeGuid");
        saasPollingDTO1.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO1.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");
        when(mockRedisService.getRefundPollingResp(saasPollingDTO1)).thenReturn(aggRefundPollingRespDTOMono);

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.doRefundPollingAsync(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO1 = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO1.setCode("10000");
        aggRefundPollingRespDTO1.setMsg("退款中");
        aggRefundPollingRespDTO1.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO1.setOrderNo("orderNo");
        aggRefundPollingRespDTO1.setMchntId("mchntId");
        final Mono<AggRefundPollingRespDTO> aggRefundPollingRespDTOMono1 = Mono.just(aggRefundPollingRespDTO1);
        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO.setAppId("appId");
        aggRefundPollingDTO.setTimestamp(0L);
        aggRefundPollingDTO.setDeveloperId("developerId");
        aggRefundPollingDTO.setSignature("signature");
        when(mockAggPayRpcService.doRefundPollingAsync(aggRefundPollingDTO)).thenReturn(aggRefundPollingRespDTOMono1);

        // Run the test
        final Mono<AggRefundPollingRespDTO> result = aggPayServiceImplUnderTest.refundPolling(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testRefundPolling_RedisServiceReturnsNoItem() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure RedisService.getRefundPollingResp(...).
        final SaasPollingDTO saasPollingDTO1 = new SaasPollingDTO();
        saasPollingDTO1.setDeviceType(0);
        saasPollingDTO1.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO1.setStoreGuid("storeGuid");
        saasPollingDTO1.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO1.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");
        when(mockRedisService.getRefundPollingResp(saasPollingDTO1)).thenReturn(Mono.empty());

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.doRefundPollingAsync(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode("10000");
        aggRefundPollingRespDTO.setMsg("退款中");
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setOrderNo("orderNo");
        aggRefundPollingRespDTO.setMchntId("mchntId");
        final Mono<AggRefundPollingRespDTO> aggRefundPollingRespDTOMono = Mono.just(aggRefundPollingRespDTO);
        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO.setAppId("appId");
        aggRefundPollingDTO.setTimestamp(0L);
        aggRefundPollingDTO.setDeveloperId("developerId");
        aggRefundPollingDTO.setSignature("signature");
        when(mockAggPayRpcService.doRefundPollingAsync(aggRefundPollingDTO)).thenReturn(aggRefundPollingRespDTOMono);

        // Run the test
        final Mono<AggRefundPollingRespDTO> result = aggPayServiceImplUnderTest.refundPolling(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testRefundPolling_RedisServiceReturnsError() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure RedisService.getRefundPollingResp(...).
        final Mono<AggRefundPollingRespDTO> aggRefundPollingRespDTOMono = Mono.error(new Exception("message"));
        final SaasPollingDTO saasPollingDTO1 = new SaasPollingDTO();
        saasPollingDTO1.setDeviceType(0);
        saasPollingDTO1.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO1.setStoreGuid("storeGuid");
        saasPollingDTO1.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO1.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");
        when(mockRedisService.getRefundPollingResp(saasPollingDTO1)).thenReturn(aggRefundPollingRespDTOMono);

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.doRefundPollingAsync(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode("10000");
        aggRefundPollingRespDTO.setMsg("退款中");
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setOrderNo("orderNo");
        aggRefundPollingRespDTO.setMchntId("mchntId");
        final Mono<AggRefundPollingRespDTO> aggRefundPollingRespDTOMono1 = Mono.just(aggRefundPollingRespDTO);
        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO.setAppId("appId");
        aggRefundPollingDTO.setTimestamp(0L);
        aggRefundPollingDTO.setDeveloperId("developerId");
        aggRefundPollingDTO.setSignature("signature");
        when(mockAggPayRpcService.doRefundPollingAsync(aggRefundPollingDTO)).thenReturn(aggRefundPollingRespDTOMono1);

        // Run the test
        final Mono<AggRefundPollingRespDTO> result = aggPayServiceImplUnderTest.refundPolling(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testRefundPolling_ErpConfigServiceReturnsNoItem() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure RedisService.getRefundPollingResp(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode("10000");
        aggRefundPollingRespDTO.setMsg("退款中");
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setOrderNo("orderNo");
        aggRefundPollingRespDTO.setMchntId("mchntId");
        final Mono<AggRefundPollingRespDTO> aggRefundPollingRespDTOMono = Mono.just(aggRefundPollingRespDTO);
        final SaasPollingDTO saasPollingDTO1 = new SaasPollingDTO();
        saasPollingDTO1.setDeviceType(0);
        saasPollingDTO1.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO1.setStoreGuid("storeGuid");
        saasPollingDTO1.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO1.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");
        when(mockRedisService.getRefundPollingResp(saasPollingDTO1)).thenReturn(aggRefundPollingRespDTOMono);

        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(Mono.empty());
        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.doRefundPollingAsync(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO1 = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO1.setCode("10000");
        aggRefundPollingRespDTO1.setMsg("退款中");
        aggRefundPollingRespDTO1.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO1.setOrderNo("orderNo");
        aggRefundPollingRespDTO1.setMchntId("mchntId");
        final Mono<AggRefundPollingRespDTO> aggRefundPollingRespDTOMono1 = Mono.just(aggRefundPollingRespDTO1);
        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO.setAppId("appId");
        aggRefundPollingDTO.setTimestamp(0L);
        aggRefundPollingDTO.setDeveloperId("developerId");
        aggRefundPollingDTO.setSignature("signature");
        when(mockAggPayRpcService.doRefundPollingAsync(aggRefundPollingDTO)).thenReturn(aggRefundPollingRespDTOMono1);

        // Run the test
        final Mono<AggRefundPollingRespDTO> result = aggPayServiceImplUnderTest.refundPolling(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testRefundPolling_ErpConfigServiceReturnsError() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure RedisService.getRefundPollingResp(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode("10000");
        aggRefundPollingRespDTO.setMsg("退款中");
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setOrderNo("orderNo");
        aggRefundPollingRespDTO.setMchntId("mchntId");
        final Mono<AggRefundPollingRespDTO> aggRefundPollingRespDTOMono = Mono.just(aggRefundPollingRespDTO);
        final SaasPollingDTO saasPollingDTO1 = new SaasPollingDTO();
        saasPollingDTO1.setDeviceType(0);
        saasPollingDTO1.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO1.setStoreGuid("storeGuid");
        saasPollingDTO1.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO1.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");
        when(mockRedisService.getRefundPollingResp(saasPollingDTO1)).thenReturn(aggRefundPollingRespDTOMono);

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.error(new Exception("message"));
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.doRefundPollingAsync(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO1 = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO1.setCode("10000");
        aggRefundPollingRespDTO1.setMsg("退款中");
        aggRefundPollingRespDTO1.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO1.setOrderNo("orderNo");
        aggRefundPollingRespDTO1.setMchntId("mchntId");
        final Mono<AggRefundPollingRespDTO> aggRefundPollingRespDTOMono1 = Mono.just(aggRefundPollingRespDTO1);
        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO.setAppId("appId");
        aggRefundPollingDTO.setTimestamp(0L);
        aggRefundPollingDTO.setDeveloperId("developerId");
        aggRefundPollingDTO.setSignature("signature");
        when(mockAggPayRpcService.doRefundPollingAsync(aggRefundPollingDTO)).thenReturn(aggRefundPollingRespDTOMono1);

        // Run the test
        final Mono<AggRefundPollingRespDTO> result = aggPayServiceImplUnderTest.refundPolling(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testRefundPolling_AggPayRpcServiceReturnsNoItem() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure RedisService.getRefundPollingResp(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode("10000");
        aggRefundPollingRespDTO.setMsg("退款中");
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setOrderNo("orderNo");
        aggRefundPollingRespDTO.setMchntId("mchntId");
        final Mono<AggRefundPollingRespDTO> aggRefundPollingRespDTOMono = Mono.just(aggRefundPollingRespDTO);
        final SaasPollingDTO saasPollingDTO1 = new SaasPollingDTO();
        saasPollingDTO1.setDeviceType(0);
        saasPollingDTO1.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO1.setStoreGuid("storeGuid");
        saasPollingDTO1.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO1.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");
        when(mockRedisService.getRefundPollingResp(saasPollingDTO1)).thenReturn(aggRefundPollingRespDTOMono);

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.doRefundPollingAsync(...).
        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO.setAppId("appId");
        aggRefundPollingDTO.setTimestamp(0L);
        aggRefundPollingDTO.setDeveloperId("developerId");
        aggRefundPollingDTO.setSignature("signature");
        when(mockAggPayRpcService.doRefundPollingAsync(aggRefundPollingDTO)).thenReturn(Mono.empty());

        // Run the test
        final Mono<AggRefundPollingRespDTO> result = aggPayServiceImplUnderTest.refundPolling(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testRefundPolling_AggPayRpcServiceReturnsError() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure RedisService.getRefundPollingResp(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode("10000");
        aggRefundPollingRespDTO.setMsg("退款中");
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setOrderNo("orderNo");
        aggRefundPollingRespDTO.setMchntId("mchntId");
        final Mono<AggRefundPollingRespDTO> aggRefundPollingRespDTOMono = Mono.just(aggRefundPollingRespDTO);
        final SaasPollingDTO saasPollingDTO1 = new SaasPollingDTO();
        saasPollingDTO1.setDeviceType(0);
        saasPollingDTO1.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO1.setStoreGuid("storeGuid");
        saasPollingDTO1.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO1.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");
        when(mockRedisService.getRefundPollingResp(saasPollingDTO1)).thenReturn(aggRefundPollingRespDTOMono);

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.doRefundPollingAsync(...).
        final Mono<AggRefundPollingRespDTO> aggRefundPollingRespDTOMono1 = Mono.error(new Exception("message"));
        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO.setAppId("appId");
        aggRefundPollingDTO.setTimestamp(0L);
        aggRefundPollingDTO.setDeveloperId("developerId");
        aggRefundPollingDTO.setSignature("signature");
        when(mockAggPayRpcService.doRefundPollingAsync(aggRefundPollingDTO)).thenReturn(aggRefundPollingRespDTOMono1);

        // Run the test
        final Mono<AggRefundPollingRespDTO> result = aggPayServiceImplUnderTest.refundPolling(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testQueryRefundResult() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.doRefundPollingAsync(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode("10000");
        aggRefundPollingRespDTO.setMsg("退款中");
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setOrderNo("orderNo");
        aggRefundPollingRespDTO.setMchntId("mchntId");
        final Mono<AggRefundPollingRespDTO> aggRefundPollingRespDTOMono = Mono.just(aggRefundPollingRespDTO);
        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO.setAppId("appId");
        aggRefundPollingDTO.setTimestamp(0L);
        aggRefundPollingDTO.setDeveloperId("developerId");
        aggRefundPollingDTO.setSignature("signature");
        when(mockAggPayRpcService.doRefundPollingAsync(aggRefundPollingDTO)).thenReturn(aggRefundPollingRespDTOMono);

        // Run the test
        final Mono<AggRefundPollingRespDTO> result = aggPayServiceImplUnderTest.queryRefundResult(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testQueryRefundResult_ErpConfigServiceReturnsNoItem() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(Mono.empty());
        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.doRefundPollingAsync(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode("10000");
        aggRefundPollingRespDTO.setMsg("退款中");
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setOrderNo("orderNo");
        aggRefundPollingRespDTO.setMchntId("mchntId");
        final Mono<AggRefundPollingRespDTO> aggRefundPollingRespDTOMono = Mono.just(aggRefundPollingRespDTO);
        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO.setAppId("appId");
        aggRefundPollingDTO.setTimestamp(0L);
        aggRefundPollingDTO.setDeveloperId("developerId");
        aggRefundPollingDTO.setSignature("signature");
        when(mockAggPayRpcService.doRefundPollingAsync(aggRefundPollingDTO)).thenReturn(aggRefundPollingRespDTOMono);

        // Run the test
        final Mono<AggRefundPollingRespDTO> result = aggPayServiceImplUnderTest.queryRefundResult(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testQueryRefundResult_ErpConfigServiceReturnsError() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.error(new Exception("message"));
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.doRefundPollingAsync(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode("10000");
        aggRefundPollingRespDTO.setMsg("退款中");
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setOrderNo("orderNo");
        aggRefundPollingRespDTO.setMchntId("mchntId");
        final Mono<AggRefundPollingRespDTO> aggRefundPollingRespDTOMono = Mono.just(aggRefundPollingRespDTO);
        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO.setAppId("appId");
        aggRefundPollingDTO.setTimestamp(0L);
        aggRefundPollingDTO.setDeveloperId("developerId");
        aggRefundPollingDTO.setSignature("signature");
        when(mockAggPayRpcService.doRefundPollingAsync(aggRefundPollingDTO)).thenReturn(aggRefundPollingRespDTOMono);

        // Run the test
        final Mono<AggRefundPollingRespDTO> result = aggPayServiceImplUnderTest.queryRefundResult(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testQueryRefundResult_AggPayRpcServiceReturnsNoItem() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.doRefundPollingAsync(...).
        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO.setAppId("appId");
        aggRefundPollingDTO.setTimestamp(0L);
        aggRefundPollingDTO.setDeveloperId("developerId");
        aggRefundPollingDTO.setSignature("signature");
        when(mockAggPayRpcService.doRefundPollingAsync(aggRefundPollingDTO)).thenReturn(Mono.empty());

        // Run the test
        final Mono<AggRefundPollingRespDTO> result = aggPayServiceImplUnderTest.queryRefundResult(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testQueryRefundResult_AggPayRpcServiceReturnsError() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.doRefundPollingAsync(...).
        final Mono<AggRefundPollingRespDTO> aggRefundPollingRespDTOMono = Mono.error(new Exception("message"));
        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggRefundPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggRefundPollingDTO.setAppId("appId");
        aggRefundPollingDTO.setTimestamp(0L);
        aggRefundPollingDTO.setDeveloperId("developerId");
        aggRefundPollingDTO.setSignature("signature");
        when(mockAggPayRpcService.doRefundPollingAsync(aggRefundPollingDTO)).thenReturn(aggRefundPollingRespDTOMono);

        // Run the test
        final Mono<AggRefundPollingRespDTO> result = aggPayServiceImplUnderTest.queryRefundResult(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testWeChatPublic() {
        // Setup
        final SaasAggWeChatPublicAccountPayDTO saasAggWxPubAccPayDTO = new SaasAggWeChatPublicAccountPayDTO();
        saasAggWxPubAccPayDTO.setDeviceType(0);
        saasAggWxPubAccPayDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggWxPubAccPayDTO.setStoreGuid("storeGuid");
        final AggWeChatPublicAccountPayDTO publicAccountPayDTO = new AggWeChatPublicAccountPayDTO();
        publicAccountPayDTO.setAmount(new BigDecimal("0.00"));
        publicAccountPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        publicAccountPayDTO.setPayGUID("payGUID");
        publicAccountPayDTO.setOutNotifyUrl("callBack");
        publicAccountPayDTO.setEnterpriseName("enterpriseName");
        publicAccountPayDTO.setAppId("appId");
        publicAccountPayDTO.setMchntName("enterpriseName");
        publicAccountPayDTO.setAppSecret("appSecret");
        publicAccountPayDTO.setAttachData("attachData");
        saasAggWxPubAccPayDTO.setPublicAccountPayDTO(publicAccountPayDTO);
        saasAggWxPubAccPayDTO.setSaasCallBackUrl("innerCallBackUrl");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockAggPayConfig.getCallBack()).thenReturn("callBack");

        // Configure AggPayMapstruct.makeAttachVo(...).
        final AggPayAttachDataVO aggPayAttachDataVO = new AggPayAttachDataVO();
        aggPayAttachDataVO.setDeviceType(0);
        aggPayAttachDataVO.setEnterpriseGuid("enterpriseGuid");
        aggPayAttachDataVO.setStoreGuid("storeGuid");
        aggPayAttachDataVO.setSaasCallBackUrl("innerCallBackUrl");
        aggPayAttachDataVO.setIsQuickReceipt(false);
        aggPayAttachDataVO.setIsLast(false);
        aggPayAttachDataVO.setPlatformSource("掌控者智慧系统");
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.makeAttachVo(baseDTO)).thenReturn(aggPayAttachDataVO);

        // Configure AggPayRpcService.weChatPublicAccountPayAsync(...).
        final AggWeChatPublicAccountPayDTO accountPayDTO = new AggWeChatPublicAccountPayDTO();
        accountPayDTO.setAmount(new BigDecimal("0.00"));
        accountPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        accountPayDTO.setPayGUID("payGUID");
        accountPayDTO.setOutNotifyUrl("callBack");
        accountPayDTO.setEnterpriseName("enterpriseName");
        accountPayDTO.setAppId("appId");
        accountPayDTO.setMchntName("enterpriseName");
        accountPayDTO.setAppSecret("appSecret");
        accountPayDTO.setAttachData("attachData");
        when(mockAggPayRpcService.weChatPublicAccountPayAsync(accountPayDTO)).thenReturn(Mono.just("value"));

        // Configure AggPayMapstruct.createBaseInfo(...).
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setTel("tel");
        final BaseDTO baseDTO1 = new BaseDTO();
        baseDTO1.setDeviceType(0);
        baseDTO1.setDeviceId("deviceId");
        baseDTO1.setEnterpriseGuid("enterpriseGuid");
        baseDTO1.setEnterpriseName("enterpriseName");
        baseDTO1.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.createBaseInfo(baseDTO1)).thenReturn(baseInfo);

        // Run the test
        final Mono<String> result = aggPayServiceImplUnderTest.weChatPublic(saasAggWxPubAccPayDTO);

        // Verify the results
        // Confirm PollingService.startPrePayPolling(...).
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggPayPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggPayPollingDTO.setAppId("appId");
        aggPayPollingDTO.setTimestamp(0L);
        aggPayPollingDTO.setDeveloperId("developerId");
        aggPayPollingDTO.setSignature("signature");
        verify(mockPollingService).startPrePayPolling(aggPayPollingDTO, HandlerPayBO.builder()
                .baseInfo(new BaseInfo())
                .paymentInfoDTO(new PaymentInfoDTO())
                .times(0)
                .enterpriseGuid("enterpriseGuid")
                .isQuickReceipt(false)
                .innerCallBackUrl("innerCallBackUrl")
                .handlerType(0)
                .payPowerId("payPowerId")
                .build());

        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14", "payGUID", pollingRespDTO);
    }

    @Test
    public void testWeChatPublic_ErpConfigServiceReturnsNoItem() {
        // Setup
        final SaasAggWeChatPublicAccountPayDTO saasAggWxPubAccPayDTO = new SaasAggWeChatPublicAccountPayDTO();
        saasAggWxPubAccPayDTO.setDeviceType(0);
        saasAggWxPubAccPayDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggWxPubAccPayDTO.setStoreGuid("storeGuid");
        final AggWeChatPublicAccountPayDTO publicAccountPayDTO = new AggWeChatPublicAccountPayDTO();
        publicAccountPayDTO.setAmount(new BigDecimal("0.00"));
        publicAccountPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        publicAccountPayDTO.setPayGUID("payGUID");
        publicAccountPayDTO.setOutNotifyUrl("callBack");
        publicAccountPayDTO.setEnterpriseName("enterpriseName");
        publicAccountPayDTO.setAppId("appId");
        publicAccountPayDTO.setMchntName("enterpriseName");
        publicAccountPayDTO.setAppSecret("appSecret");
        publicAccountPayDTO.setAttachData("attachData");
        saasAggWxPubAccPayDTO.setPublicAccountPayDTO(publicAccountPayDTO);
        saasAggWxPubAccPayDTO.setSaasCallBackUrl("innerCallBackUrl");

        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(Mono.empty());
        when(mockAggPayConfig.getCallBack()).thenReturn("callBack");

        // Configure AggPayMapstruct.makeAttachVo(...).
        final AggPayAttachDataVO aggPayAttachDataVO = new AggPayAttachDataVO();
        aggPayAttachDataVO.setDeviceType(0);
        aggPayAttachDataVO.setEnterpriseGuid("enterpriseGuid");
        aggPayAttachDataVO.setStoreGuid("storeGuid");
        aggPayAttachDataVO.setSaasCallBackUrl("innerCallBackUrl");
        aggPayAttachDataVO.setIsQuickReceipt(false);
        aggPayAttachDataVO.setIsLast(false);
        aggPayAttachDataVO.setPlatformSource("掌控者智慧系统");
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.makeAttachVo(baseDTO)).thenReturn(aggPayAttachDataVO);

        // Configure AggPayRpcService.weChatPublicAccountPayAsync(...).
        final AggWeChatPublicAccountPayDTO accountPayDTO = new AggWeChatPublicAccountPayDTO();
        accountPayDTO.setAmount(new BigDecimal("0.00"));
        accountPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        accountPayDTO.setPayGUID("payGUID");
        accountPayDTO.setOutNotifyUrl("callBack");
        accountPayDTO.setEnterpriseName("enterpriseName");
        accountPayDTO.setAppId("appId");
        accountPayDTO.setMchntName("enterpriseName");
        accountPayDTO.setAppSecret("appSecret");
        accountPayDTO.setAttachData("attachData");
        when(mockAggPayRpcService.weChatPublicAccountPayAsync(accountPayDTO)).thenReturn(Mono.just("value"));

        // Configure AggPayMapstruct.createBaseInfo(...).
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setTel("tel");
        final BaseDTO baseDTO1 = new BaseDTO();
        baseDTO1.setDeviceType(0);
        baseDTO1.setDeviceId("deviceId");
        baseDTO1.setEnterpriseGuid("enterpriseGuid");
        baseDTO1.setEnterpriseName("enterpriseName");
        baseDTO1.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.createBaseInfo(baseDTO1)).thenReturn(baseInfo);

        // Run the test
        final Mono<String> result = aggPayServiceImplUnderTest.weChatPublic(saasAggWxPubAccPayDTO);

        // Verify the results
        // Confirm PollingService.startPrePayPolling(...).
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggPayPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggPayPollingDTO.setAppId("appId");
        aggPayPollingDTO.setTimestamp(0L);
        aggPayPollingDTO.setDeveloperId("developerId");
        aggPayPollingDTO.setSignature("signature");
        verify(mockPollingService).startPrePayPolling(aggPayPollingDTO, HandlerPayBO.builder()
                .baseInfo(new BaseInfo())
                .paymentInfoDTO(new PaymentInfoDTO())
                .times(0)
                .enterpriseGuid("enterpriseGuid")
                .isQuickReceipt(false)
                .innerCallBackUrl("innerCallBackUrl")
                .handlerType(0)
                .payPowerId("payPowerId")
                .build());

        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14", "payGUID", pollingRespDTO);
    }

    @Test
    public void testWeChatPublic_ErpConfigServiceReturnsError() {
        // Setup
        final SaasAggWeChatPublicAccountPayDTO saasAggWxPubAccPayDTO = new SaasAggWeChatPublicAccountPayDTO();
        saasAggWxPubAccPayDTO.setDeviceType(0);
        saasAggWxPubAccPayDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggWxPubAccPayDTO.setStoreGuid("storeGuid");
        final AggWeChatPublicAccountPayDTO publicAccountPayDTO = new AggWeChatPublicAccountPayDTO();
        publicAccountPayDTO.setAmount(new BigDecimal("0.00"));
        publicAccountPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        publicAccountPayDTO.setPayGUID("payGUID");
        publicAccountPayDTO.setOutNotifyUrl("callBack");
        publicAccountPayDTO.setEnterpriseName("enterpriseName");
        publicAccountPayDTO.setAppId("appId");
        publicAccountPayDTO.setMchntName("enterpriseName");
        publicAccountPayDTO.setAppSecret("appSecret");
        publicAccountPayDTO.setAttachData("attachData");
        saasAggWxPubAccPayDTO.setPublicAccountPayDTO(publicAccountPayDTO);
        saasAggWxPubAccPayDTO.setSaasCallBackUrl("innerCallBackUrl");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.error(new Exception("message"));
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockAggPayConfig.getCallBack()).thenReturn("callBack");

        // Configure AggPayMapstruct.makeAttachVo(...).
        final AggPayAttachDataVO aggPayAttachDataVO = new AggPayAttachDataVO();
        aggPayAttachDataVO.setDeviceType(0);
        aggPayAttachDataVO.setEnterpriseGuid("enterpriseGuid");
        aggPayAttachDataVO.setStoreGuid("storeGuid");
        aggPayAttachDataVO.setSaasCallBackUrl("innerCallBackUrl");
        aggPayAttachDataVO.setIsQuickReceipt(false);
        aggPayAttachDataVO.setIsLast(false);
        aggPayAttachDataVO.setPlatformSource("掌控者智慧系统");
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.makeAttachVo(baseDTO)).thenReturn(aggPayAttachDataVO);

        // Configure AggPayRpcService.weChatPublicAccountPayAsync(...).
        final AggWeChatPublicAccountPayDTO accountPayDTO = new AggWeChatPublicAccountPayDTO();
        accountPayDTO.setAmount(new BigDecimal("0.00"));
        accountPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        accountPayDTO.setPayGUID("payGUID");
        accountPayDTO.setOutNotifyUrl("callBack");
        accountPayDTO.setEnterpriseName("enterpriseName");
        accountPayDTO.setAppId("appId");
        accountPayDTO.setMchntName("enterpriseName");
        accountPayDTO.setAppSecret("appSecret");
        accountPayDTO.setAttachData("attachData");
        when(mockAggPayRpcService.weChatPublicAccountPayAsync(accountPayDTO)).thenReturn(Mono.just("value"));

        // Configure AggPayMapstruct.createBaseInfo(...).
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setTel("tel");
        final BaseDTO baseDTO1 = new BaseDTO();
        baseDTO1.setDeviceType(0);
        baseDTO1.setDeviceId("deviceId");
        baseDTO1.setEnterpriseGuid("enterpriseGuid");
        baseDTO1.setEnterpriseName("enterpriseName");
        baseDTO1.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.createBaseInfo(baseDTO1)).thenReturn(baseInfo);

        // Run the test
        final Mono<String> result = aggPayServiceImplUnderTest.weChatPublic(saasAggWxPubAccPayDTO);

        // Verify the results
        // Confirm PollingService.startPrePayPolling(...).
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggPayPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggPayPollingDTO.setAppId("appId");
        aggPayPollingDTO.setTimestamp(0L);
        aggPayPollingDTO.setDeveloperId("developerId");
        aggPayPollingDTO.setSignature("signature");
        verify(mockPollingService).startPrePayPolling(aggPayPollingDTO, HandlerPayBO.builder()
                .baseInfo(new BaseInfo())
                .paymentInfoDTO(new PaymentInfoDTO())
                .times(0)
                .enterpriseGuid("enterpriseGuid")
                .isQuickReceipt(false)
                .innerCallBackUrl("innerCallBackUrl")
                .handlerType(0)
                .payPowerId("payPowerId")
                .build());

        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14", "payGUID", pollingRespDTO);
    }

    @Test
    public void testWeChatPublic_AggPayRpcServiceReturnsNoItem() {
        // Setup
        final SaasAggWeChatPublicAccountPayDTO saasAggWxPubAccPayDTO = new SaasAggWeChatPublicAccountPayDTO();
        saasAggWxPubAccPayDTO.setDeviceType(0);
        saasAggWxPubAccPayDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggWxPubAccPayDTO.setStoreGuid("storeGuid");
        final AggWeChatPublicAccountPayDTO publicAccountPayDTO = new AggWeChatPublicAccountPayDTO();
        publicAccountPayDTO.setAmount(new BigDecimal("0.00"));
        publicAccountPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        publicAccountPayDTO.setPayGUID("payGUID");
        publicAccountPayDTO.setOutNotifyUrl("callBack");
        publicAccountPayDTO.setEnterpriseName("enterpriseName");
        publicAccountPayDTO.setAppId("appId");
        publicAccountPayDTO.setMchntName("enterpriseName");
        publicAccountPayDTO.setAppSecret("appSecret");
        publicAccountPayDTO.setAttachData("attachData");
        saasAggWxPubAccPayDTO.setPublicAccountPayDTO(publicAccountPayDTO);
        saasAggWxPubAccPayDTO.setSaasCallBackUrl("innerCallBackUrl");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockAggPayConfig.getCallBack()).thenReturn("callBack");

        // Configure AggPayMapstruct.makeAttachVo(...).
        final AggPayAttachDataVO aggPayAttachDataVO = new AggPayAttachDataVO();
        aggPayAttachDataVO.setDeviceType(0);
        aggPayAttachDataVO.setEnterpriseGuid("enterpriseGuid");
        aggPayAttachDataVO.setStoreGuid("storeGuid");
        aggPayAttachDataVO.setSaasCallBackUrl("innerCallBackUrl");
        aggPayAttachDataVO.setIsQuickReceipt(false);
        aggPayAttachDataVO.setIsLast(false);
        aggPayAttachDataVO.setPlatformSource("掌控者智慧系统");
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.makeAttachVo(baseDTO)).thenReturn(aggPayAttachDataVO);

        // Configure AggPayRpcService.weChatPublicAccountPayAsync(...).
        final AggWeChatPublicAccountPayDTO accountPayDTO = new AggWeChatPublicAccountPayDTO();
        accountPayDTO.setAmount(new BigDecimal("0.00"));
        accountPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        accountPayDTO.setPayGUID("payGUID");
        accountPayDTO.setOutNotifyUrl("callBack");
        accountPayDTO.setEnterpriseName("enterpriseName");
        accountPayDTO.setAppId("appId");
        accountPayDTO.setMchntName("enterpriseName");
        accountPayDTO.setAppSecret("appSecret");
        accountPayDTO.setAttachData("attachData");
        when(mockAggPayRpcService.weChatPublicAccountPayAsync(accountPayDTO)).thenReturn(Mono.empty());

        // Configure AggPayMapstruct.createBaseInfo(...).
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setTel("tel");
        final BaseDTO baseDTO1 = new BaseDTO();
        baseDTO1.setDeviceType(0);
        baseDTO1.setDeviceId("deviceId");
        baseDTO1.setEnterpriseGuid("enterpriseGuid");
        baseDTO1.setEnterpriseName("enterpriseName");
        baseDTO1.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.createBaseInfo(baseDTO1)).thenReturn(baseInfo);

        // Run the test
        final Mono<String> result = aggPayServiceImplUnderTest.weChatPublic(saasAggWxPubAccPayDTO);

        // Verify the results
        // Confirm PollingService.startPrePayPolling(...).
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggPayPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggPayPollingDTO.setAppId("appId");
        aggPayPollingDTO.setTimestamp(0L);
        aggPayPollingDTO.setDeveloperId("developerId");
        aggPayPollingDTO.setSignature("signature");
        verify(mockPollingService).startPrePayPolling(aggPayPollingDTO, HandlerPayBO.builder()
                .baseInfo(new BaseInfo())
                .paymentInfoDTO(new PaymentInfoDTO())
                .times(0)
                .enterpriseGuid("enterpriseGuid")
                .isQuickReceipt(false)
                .innerCallBackUrl("innerCallBackUrl")
                .handlerType(0)
                .payPowerId("payPowerId")
                .build());

        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14", "payGUID", pollingRespDTO);
    }

    @Test
    public void testWeChatPublic_AggPayRpcServiceReturnsError() {
        // Setup
        final SaasAggWeChatPublicAccountPayDTO saasAggWxPubAccPayDTO = new SaasAggWeChatPublicAccountPayDTO();
        saasAggWxPubAccPayDTO.setDeviceType(0);
        saasAggWxPubAccPayDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggWxPubAccPayDTO.setStoreGuid("storeGuid");
        final AggWeChatPublicAccountPayDTO publicAccountPayDTO = new AggWeChatPublicAccountPayDTO();
        publicAccountPayDTO.setAmount(new BigDecimal("0.00"));
        publicAccountPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        publicAccountPayDTO.setPayGUID("payGUID");
        publicAccountPayDTO.setOutNotifyUrl("callBack");
        publicAccountPayDTO.setEnterpriseName("enterpriseName");
        publicAccountPayDTO.setAppId("appId");
        publicAccountPayDTO.setMchntName("enterpriseName");
        publicAccountPayDTO.setAppSecret("appSecret");
        publicAccountPayDTO.setAttachData("attachData");
        saasAggWxPubAccPayDTO.setPublicAccountPayDTO(publicAccountPayDTO);
        saasAggWxPubAccPayDTO.setSaasCallBackUrl("innerCallBackUrl");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockAggPayConfig.getCallBack()).thenReturn("callBack");

        // Configure AggPayMapstruct.makeAttachVo(...).
        final AggPayAttachDataVO aggPayAttachDataVO = new AggPayAttachDataVO();
        aggPayAttachDataVO.setDeviceType(0);
        aggPayAttachDataVO.setEnterpriseGuid("enterpriseGuid");
        aggPayAttachDataVO.setStoreGuid("storeGuid");
        aggPayAttachDataVO.setSaasCallBackUrl("innerCallBackUrl");
        aggPayAttachDataVO.setIsQuickReceipt(false);
        aggPayAttachDataVO.setIsLast(false);
        aggPayAttachDataVO.setPlatformSource("掌控者智慧系统");
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.makeAttachVo(baseDTO)).thenReturn(aggPayAttachDataVO);

        // Configure AggPayRpcService.weChatPublicAccountPayAsync(...).
        final AggWeChatPublicAccountPayDTO accountPayDTO = new AggWeChatPublicAccountPayDTO();
        accountPayDTO.setAmount(new BigDecimal("0.00"));
        accountPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        accountPayDTO.setPayGUID("payGUID");
        accountPayDTO.setOutNotifyUrl("callBack");
        accountPayDTO.setEnterpriseName("enterpriseName");
        accountPayDTO.setAppId("appId");
        accountPayDTO.setMchntName("enterpriseName");
        accountPayDTO.setAppSecret("appSecret");
        accountPayDTO.setAttachData("attachData");
        when(mockAggPayRpcService.weChatPublicAccountPayAsync(accountPayDTO))
                .thenReturn(Mono.error(new Exception("message")));

        // Configure AggPayMapstruct.createBaseInfo(...).
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setTel("tel");
        final BaseDTO baseDTO1 = new BaseDTO();
        baseDTO1.setDeviceType(0);
        baseDTO1.setDeviceId("deviceId");
        baseDTO1.setEnterpriseGuid("enterpriseGuid");
        baseDTO1.setEnterpriseName("enterpriseName");
        baseDTO1.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.createBaseInfo(baseDTO1)).thenReturn(baseInfo);

        // Run the test
        final Mono<String> result = aggPayServiceImplUnderTest.weChatPublic(saasAggWxPubAccPayDTO);

        // Verify the results
        // Confirm PollingService.startPrePayPolling(...).
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        aggPayPollingDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        aggPayPollingDTO.setAppId("appId");
        aggPayPollingDTO.setTimestamp(0L);
        aggPayPollingDTO.setDeveloperId("developerId");
        aggPayPollingDTO.setSignature("signature");
        verify(mockPollingService).startPrePayPolling(aggPayPollingDTO, HandlerPayBO.builder()
                .baseInfo(new BaseInfo())
                .paymentInfoDTO(new PaymentInfoDTO())
                .times(0)
                .enterpriseGuid("enterpriseGuid")
                .isQuickReceipt(false)
                .innerCallBackUrl("innerCallBackUrl")
                .handlerType(0)
                .payPowerId("payPowerId")
                .build());

        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockRedisService).putPollingResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14", "payGUID", pollingRespDTO);
    }

    @Test
    public void testWeChatPublicPolling() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure RedisService.getPollingResp(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO);
        final SaasPollingDTO saasPollingDTO1 = new SaasPollingDTO();
        saasPollingDTO1.setDeviceType(0);
        saasPollingDTO1.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO1.setStoreGuid("storeGuid");
        saasPollingDTO1.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO1.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");
        when(mockRedisService.getPollingResp(saasPollingDTO1)).thenReturn(aggPayPollingRespDTOMono);

        // Configure AggPayRpcService.doWeChatPublicAccountPollingAsync(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO1 = new AggPayPollingRespDTO();
        aggPayPollingRespDTO1.setCode("10000");
        aggPayPollingRespDTO1.setMsg("success");
        aggPayPollingRespDTO1.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO1.setBody("body");
        aggPayPollingRespDTO1.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono1 = Mono.just(aggPayPollingRespDTO1);
        when(mockAggPayRpcService.doWeChatPublicAccountPollingAsync("4dc8fd4d-1c43-40f7-ad44-562beee10b14"))
                .thenReturn(aggPayPollingRespDTOMono1);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.weChatPublicPolling(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testWeChatPublicPolling_RedisServiceReturnsNoItem() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure RedisService.getPollingResp(...).
        final SaasPollingDTO saasPollingDTO1 = new SaasPollingDTO();
        saasPollingDTO1.setDeviceType(0);
        saasPollingDTO1.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO1.setStoreGuid("storeGuid");
        saasPollingDTO1.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO1.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");
        when(mockRedisService.getPollingResp(saasPollingDTO1)).thenReturn(Mono.empty());

        // Configure AggPayRpcService.doWeChatPublicAccountPollingAsync(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO);
        when(mockAggPayRpcService.doWeChatPublicAccountPollingAsync("4dc8fd4d-1c43-40f7-ad44-562beee10b14"))
                .thenReturn(aggPayPollingRespDTOMono);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.weChatPublicPolling(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testWeChatPublicPolling_RedisServiceReturnsError() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure RedisService.getPollingResp(...).
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.error(new Exception("message"));
        final SaasPollingDTO saasPollingDTO1 = new SaasPollingDTO();
        saasPollingDTO1.setDeviceType(0);
        saasPollingDTO1.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO1.setStoreGuid("storeGuid");
        saasPollingDTO1.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO1.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");
        when(mockRedisService.getPollingResp(saasPollingDTO1)).thenReturn(aggPayPollingRespDTOMono);

        // Configure AggPayRpcService.doWeChatPublicAccountPollingAsync(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono1 = Mono.just(aggPayPollingRespDTO);
        when(mockAggPayRpcService.doWeChatPublicAccountPollingAsync("4dc8fd4d-1c43-40f7-ad44-562beee10b14"))
                .thenReturn(aggPayPollingRespDTOMono1);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.weChatPublicPolling(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testWeChatPublicPolling_AggPayRpcServiceReturnsNoItem() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure RedisService.getPollingResp(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO);
        final SaasPollingDTO saasPollingDTO1 = new SaasPollingDTO();
        saasPollingDTO1.setDeviceType(0);
        saasPollingDTO1.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO1.setStoreGuid("storeGuid");
        saasPollingDTO1.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO1.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");
        when(mockRedisService.getPollingResp(saasPollingDTO1)).thenReturn(aggPayPollingRespDTOMono);

        when(mockAggPayRpcService.doWeChatPublicAccountPollingAsync("4dc8fd4d-1c43-40f7-ad44-562beee10b14"))
                .thenReturn(Mono.empty());

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.weChatPublicPolling(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testWeChatPublicPolling_AggPayRpcServiceReturnsError() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure RedisService.getPollingResp(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO);
        final SaasPollingDTO saasPollingDTO1 = new SaasPollingDTO();
        saasPollingDTO1.setDeviceType(0);
        saasPollingDTO1.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO1.setStoreGuid("storeGuid");
        saasPollingDTO1.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO1.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");
        when(mockRedisService.getPollingResp(saasPollingDTO1)).thenReturn(aggPayPollingRespDTOMono);

        // Configure AggPayRpcService.doWeChatPublicAccountPollingAsync(...).
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono1 = Mono.error(new Exception("message"));
        when(mockAggPayRpcService.doWeChatPublicAccountPollingAsync("4dc8fd4d-1c43-40f7-ad44-562beee10b14"))
                .thenReturn(aggPayPollingRespDTOMono1);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.weChatPublicPolling(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testCallback() {
        // Setup
        final AggPayCallbackDTO aggPayCallbackDTO = new AggPayCallbackDTO();
        aggPayCallbackDTO.setOrderGUID("orderGUID");
        aggPayCallbackDTO.setPayGUID("payGUID");
        aggPayCallbackDTO.setPaySt("paySt");
        aggPayCallbackDTO.setSignature("signature");
        aggPayCallbackDTO.setAttachData("attachData");

        when(mockPollingService.compareStatWithCache("orderGUID", "payGUID", "paySt")).thenReturn(Mono.just("value"));

        // Configure AggPayMapstruct.makecallBackDto(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final AggPayCallbackDTO aggPayCallbackDTO1 = new AggPayCallbackDTO();
        aggPayCallbackDTO1.setOrderGUID("orderGUID");
        aggPayCallbackDTO1.setPayGUID("payGUID");
        aggPayCallbackDTO1.setPaySt("paySt");
        aggPayCallbackDTO1.setSignature("signature");
        aggPayCallbackDTO1.setAttachData("attachData");
        when(mockAggPayMapstruct.makecallBackDto(aggPayCallbackDTO1)).thenReturn(aggPayPollingRespDTO);

        // Configure ErpConfigService.getPaymentInfo(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        when(mockErpConfigService.getPaymentInfo("enterpriseGuid", "storeGuid", "orderGUID"))
                .thenReturn(paymentInfoDTO);

        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayMapstruct.createBaseInfo(...).
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setTel("tel");
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.createBaseInfo(baseDTO)).thenReturn(baseInfo);

        // Run the test
        final Mono<String> result = aggPayServiceImplUnderTest.callback(aggPayCallbackDTO);

        // Verify the results
        // Confirm SaasResultRpcService.handlePayResult(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockSaasResultRpcService).handlePayResult(HandlerPayBO.builder()
                .baseInfo(new BaseInfo())
                .paymentInfoDTO(new PaymentInfoDTO())
                .times(0)
                .enterpriseGuid("enterpriseGuid")
                .isQuickReceipt(false)
                .innerCallBackUrl("innerCallBackUrl")
                .handlerType(0)
                .payPowerId("payPowerId")
                .build(), pollingRespDTO);

        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO1 = new AggPayPollingRespDTO();
        pollingRespDTO1.setCode("10000");
        pollingRespDTO1.setMsg("success");
        pollingRespDTO1.setAmount(new BigDecimal("0.00"));
        pollingRespDTO1.setBody("body");
        pollingRespDTO1.setPaySt("id");
        verify(mockRedisService).putPollingResp("orderGUID", "payGUID", pollingRespDTO1);
    }

    @Test
    public void testCallback_PollingServiceReturnsNoItem() {
        // Setup
        final AggPayCallbackDTO aggPayCallbackDTO = new AggPayCallbackDTO();
        aggPayCallbackDTO.setOrderGUID("orderGUID");
        aggPayCallbackDTO.setPayGUID("payGUID");
        aggPayCallbackDTO.setPaySt("paySt");
        aggPayCallbackDTO.setSignature("signature");
        aggPayCallbackDTO.setAttachData("attachData");

        when(mockPollingService.compareStatWithCache("orderGUID", "payGUID", "paySt")).thenReturn(Mono.empty());

        // Configure AggPayMapstruct.makecallBackDto(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final AggPayCallbackDTO aggPayCallbackDTO1 = new AggPayCallbackDTO();
        aggPayCallbackDTO1.setOrderGUID("orderGUID");
        aggPayCallbackDTO1.setPayGUID("payGUID");
        aggPayCallbackDTO1.setPaySt("paySt");
        aggPayCallbackDTO1.setSignature("signature");
        aggPayCallbackDTO1.setAttachData("attachData");
        when(mockAggPayMapstruct.makecallBackDto(aggPayCallbackDTO1)).thenReturn(aggPayPollingRespDTO);

        // Configure ErpConfigService.getPaymentInfo(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        when(mockErpConfigService.getPaymentInfo("enterpriseGuid", "storeGuid", "orderGUID"))
                .thenReturn(paymentInfoDTO);

        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayMapstruct.createBaseInfo(...).
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setTel("tel");
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.createBaseInfo(baseDTO)).thenReturn(baseInfo);

        // Run the test
        final Mono<String> result = aggPayServiceImplUnderTest.callback(aggPayCallbackDTO);

        // Verify the results
        // Confirm SaasResultRpcService.handlePayResult(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockSaasResultRpcService).handlePayResult(HandlerPayBO.builder()
                .baseInfo(new BaseInfo())
                .paymentInfoDTO(new PaymentInfoDTO())
                .times(0)
                .enterpriseGuid("enterpriseGuid")
                .isQuickReceipt(false)
                .innerCallBackUrl("innerCallBackUrl")
                .handlerType(0)
                .payPowerId("payPowerId")
                .build(), pollingRespDTO);

        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO1 = new AggPayPollingRespDTO();
        pollingRespDTO1.setCode("10000");
        pollingRespDTO1.setMsg("success");
        pollingRespDTO1.setAmount(new BigDecimal("0.00"));
        pollingRespDTO1.setBody("body");
        pollingRespDTO1.setPaySt("id");
        verify(mockRedisService).putPollingResp("orderGUID", "payGUID", pollingRespDTO1);
    }

    @Test
    public void testCallback_PollingServiceReturnsError() {
        // Setup
        final AggPayCallbackDTO aggPayCallbackDTO = new AggPayCallbackDTO();
        aggPayCallbackDTO.setOrderGUID("orderGUID");
        aggPayCallbackDTO.setPayGUID("payGUID");
        aggPayCallbackDTO.setPaySt("paySt");
        aggPayCallbackDTO.setSignature("signature");
        aggPayCallbackDTO.setAttachData("attachData");

        when(mockPollingService.compareStatWithCache("orderGUID", "payGUID", "paySt"))
                .thenReturn(Mono.error(new Exception("message")));

        // Configure AggPayMapstruct.makecallBackDto(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final AggPayCallbackDTO aggPayCallbackDTO1 = new AggPayCallbackDTO();
        aggPayCallbackDTO1.setOrderGUID("orderGUID");
        aggPayCallbackDTO1.setPayGUID("payGUID");
        aggPayCallbackDTO1.setPaySt("paySt");
        aggPayCallbackDTO1.setSignature("signature");
        aggPayCallbackDTO1.setAttachData("attachData");
        when(mockAggPayMapstruct.makecallBackDto(aggPayCallbackDTO1)).thenReturn(aggPayPollingRespDTO);

        // Configure ErpConfigService.getPaymentInfo(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        when(mockErpConfigService.getPaymentInfo("enterpriseGuid", "storeGuid", "orderGUID"))
                .thenReturn(paymentInfoDTO);

        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayMapstruct.createBaseInfo(...).
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setTel("tel");
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.createBaseInfo(baseDTO)).thenReturn(baseInfo);

        // Run the test
        final Mono<String> result = aggPayServiceImplUnderTest.callback(aggPayCallbackDTO);

        // Verify the results
        // Confirm SaasResultRpcService.handlePayResult(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockSaasResultRpcService).handlePayResult(HandlerPayBO.builder()
                .baseInfo(new BaseInfo())
                .paymentInfoDTO(new PaymentInfoDTO())
                .times(0)
                .enterpriseGuid("enterpriseGuid")
                .isQuickReceipt(false)
                .innerCallBackUrl("innerCallBackUrl")
                .handlerType(0)
                .payPowerId("payPowerId")
                .build(), pollingRespDTO);

        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO1 = new AggPayPollingRespDTO();
        pollingRespDTO1.setCode("10000");
        pollingRespDTO1.setMsg("success");
        pollingRespDTO1.setAmount(new BigDecimal("0.00"));
        pollingRespDTO1.setBody("body");
        pollingRespDTO1.setPaySt("id");
        verify(mockRedisService).putPollingResp("orderGUID", "payGUID", pollingRespDTO1);
    }

    @Test
    public void testCallback_AggPayMapstructMakecallBackDtoReturnsError() {
        // Setup
        final AggPayCallbackDTO aggPayCallbackDTO = new AggPayCallbackDTO();
        aggPayCallbackDTO.setOrderGUID("orderGUID");
        aggPayCallbackDTO.setPayGUID("payGUID");
        aggPayCallbackDTO.setPaySt("paySt");
        aggPayCallbackDTO.setSignature("signature");
        aggPayCallbackDTO.setAttachData("attachData");

        when(mockPollingService.compareStatWithCache("orderGUID", "payGUID", "paySt")).thenReturn(Mono.just("value"));

        // Configure AggPayMapstruct.makecallBackDto(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = AggPayPollingRespDTO.errorResp("code", "msg");
        final AggPayCallbackDTO aggPayCallbackDTO1 = new AggPayCallbackDTO();
        aggPayCallbackDTO1.setOrderGUID("orderGUID");
        aggPayCallbackDTO1.setPayGUID("payGUID");
        aggPayCallbackDTO1.setPaySt("paySt");
        aggPayCallbackDTO1.setSignature("signature");
        aggPayCallbackDTO1.setAttachData("attachData");
        when(mockAggPayMapstruct.makecallBackDto(aggPayCallbackDTO1)).thenReturn(aggPayPollingRespDTO);

        // Configure ErpConfigService.getPaymentInfo(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        when(mockErpConfigService.getPaymentInfo("enterpriseGuid", "storeGuid", "orderGUID"))
                .thenReturn(paymentInfoDTO);

        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayMapstruct.createBaseInfo(...).
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setDeviceType(0);
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setTel("tel");
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockAggPayMapstruct.createBaseInfo(baseDTO)).thenReturn(baseInfo);

        // Run the test
        final Mono<String> result = aggPayServiceImplUnderTest.callback(aggPayCallbackDTO);

        // Verify the results
        // Confirm SaasResultRpcService.handlePayResult(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockSaasResultRpcService).handlePayResult(HandlerPayBO.builder()
                .baseInfo(new BaseInfo())
                .paymentInfoDTO(new PaymentInfoDTO())
                .times(0)
                .enterpriseGuid("enterpriseGuid")
                .isQuickReceipt(false)
                .innerCallBackUrl("innerCallBackUrl")
                .handlerType(0)
                .payPowerId("payPowerId")
                .build(), pollingRespDTO);

        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO1 = new AggPayPollingRespDTO();
        pollingRespDTO1.setCode("10000");
        pollingRespDTO1.setMsg("success");
        pollingRespDTO1.setAmount(new BigDecimal("0.00"));
        pollingRespDTO1.setBody("body");
        pollingRespDTO1.setPaySt("id");
        verify(mockRedisService).putPollingResp("orderGUID", "payGUID", pollingRespDTO1);
    }

    @Test
    public void testCallback_ErpConfigServiceReturnsNull() {
        // Setup
        final AggPayCallbackDTO aggPayCallbackDTO = new AggPayCallbackDTO();
        aggPayCallbackDTO.setOrderGUID("orderGUID");
        aggPayCallbackDTO.setPayGUID("payGUID");
        aggPayCallbackDTO.setPaySt("paySt");
        aggPayCallbackDTO.setSignature("signature");
        aggPayCallbackDTO.setAttachData("attachData");

        when(mockPollingService.compareStatWithCache("orderGUID", "payGUID", "paySt")).thenReturn(Mono.just("value"));

        // Configure AggPayMapstruct.makecallBackDto(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final AggPayCallbackDTO aggPayCallbackDTO1 = new AggPayCallbackDTO();
        aggPayCallbackDTO1.setOrderGUID("orderGUID");
        aggPayCallbackDTO1.setPayGUID("payGUID");
        aggPayCallbackDTO1.setPaySt("paySt");
        aggPayCallbackDTO1.setSignature("signature");
        aggPayCallbackDTO1.setAttachData("attachData");
        when(mockAggPayMapstruct.makecallBackDto(aggPayCallbackDTO1)).thenReturn(aggPayPollingRespDTO);

        when(mockErpConfigService.getPaymentInfo("enterpriseGuid", "storeGuid", "orderGUID")).thenReturn(null);

        // Run the test
        final Mono<String> result = aggPayServiceImplUnderTest.callback(aggPayCallbackDTO);

        // Verify the results
        // Confirm SaasResultRpcService.handlePayResult(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("10000");
        pollingRespDTO.setMsg("success");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setBody("body");
        pollingRespDTO.setPaySt("id");
        verify(mockSaasResultRpcService).handlePayResult(HandlerPayBO.builder()
                .baseInfo(new BaseInfo())
                .paymentInfoDTO(new PaymentInfoDTO())
                .times(0)
                .enterpriseGuid("enterpriseGuid")
                .isQuickReceipt(false)
                .innerCallBackUrl("innerCallBackUrl")
                .handlerType(0)
                .payPowerId("payPowerId")
                .build(), pollingRespDTO);

        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO1 = new AggPayPollingRespDTO();
        pollingRespDTO1.setCode("10000");
        pollingRespDTO1.setMsg("success");
        pollingRespDTO1.setAmount(new BigDecimal("0.00"));
        pollingRespDTO1.setBody("body");
        pollingRespDTO1.setPaySt("id");
        verify(mockRedisService).putPollingResp("orderGUID", "payGUID", pollingRespDTO1);
    }

    @Test
    public void testReservePay() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.doAggPayReserve(...).
        final Mono<AggPayReserveResultDTO> aggPayReserveResultDTOMono = Mono.just(
                new AggPayReserveResultDTO("attachData", "code", "msg", "signature", "paySt"));
        final AggPayReserveVO accountPayDTO = new AggPayReserveVO();
        accountPayDTO.setAppId("appId");
        accountPayDTO.setAttachData("attachData");
        accountPayDTO.setDeveloperId("developerId");
        accountPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        accountPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        accountPayDTO.setSignature("signature");
        accountPayDTO.setTimestamp(0L);
        when(mockAggPayRpcService.doAggPayReserve(accountPayDTO)).thenReturn(aggPayReserveResultDTOMono);

        // Run the test
        final Mono<AggPayReserveResultDTO> result = aggPayServiceImplUnderTest.reservePay(saasPollingDTO);

        // Verify the results
        verify(mockRedisService).putCallBackResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", "id");
    }

    @Test
    public void testReservePay_ErpConfigServiceReturnsNoItem() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(Mono.empty());
        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.doAggPayReserve(...).
        final Mono<AggPayReserveResultDTO> aggPayReserveResultDTOMono = Mono.just(
                new AggPayReserveResultDTO("attachData", "code", "msg", "signature", "paySt"));
        final AggPayReserveVO accountPayDTO = new AggPayReserveVO();
        accountPayDTO.setAppId("appId");
        accountPayDTO.setAttachData("attachData");
        accountPayDTO.setDeveloperId("developerId");
        accountPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        accountPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        accountPayDTO.setSignature("signature");
        accountPayDTO.setTimestamp(0L);
        when(mockAggPayRpcService.doAggPayReserve(accountPayDTO)).thenReturn(aggPayReserveResultDTOMono);

        // Run the test
        final Mono<AggPayReserveResultDTO> result = aggPayServiceImplUnderTest.reservePay(saasPollingDTO);

        // Verify the results
        verify(mockRedisService).putCallBackResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", "id");
    }

    @Test
    public void testReservePay_ErpConfigServiceReturnsError() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.error(new Exception("message"));
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.doAggPayReserve(...).
        final Mono<AggPayReserveResultDTO> aggPayReserveResultDTOMono = Mono.just(
                new AggPayReserveResultDTO("attachData", "code", "msg", "signature", "paySt"));
        final AggPayReserveVO accountPayDTO = new AggPayReserveVO();
        accountPayDTO.setAppId("appId");
        accountPayDTO.setAttachData("attachData");
        accountPayDTO.setDeveloperId("developerId");
        accountPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        accountPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        accountPayDTO.setSignature("signature");
        accountPayDTO.setTimestamp(0L);
        when(mockAggPayRpcService.doAggPayReserve(accountPayDTO)).thenReturn(aggPayReserveResultDTOMono);

        // Run the test
        final Mono<AggPayReserveResultDTO> result = aggPayServiceImplUnderTest.reservePay(saasPollingDTO);

        // Verify the results
        verify(mockRedisService).putCallBackResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", "id");
    }

    @Test
    public void testReservePay_AggPayRpcServiceReturnsNoItem() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.doAggPayReserve(...).
        final AggPayReserveVO accountPayDTO = new AggPayReserveVO();
        accountPayDTO.setAppId("appId");
        accountPayDTO.setAttachData("attachData");
        accountPayDTO.setDeveloperId("developerId");
        accountPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        accountPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        accountPayDTO.setSignature("signature");
        accountPayDTO.setTimestamp(0L);
        when(mockAggPayRpcService.doAggPayReserve(accountPayDTO)).thenReturn(Mono.empty());

        // Run the test
        final Mono<AggPayReserveResultDTO> result = aggPayServiceImplUnderTest.reservePay(saasPollingDTO);

        // Verify the results
        verify(mockRedisService).putCallBackResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", "id");
    }

    @Test
    public void testReservePay_AggPayRpcServiceReturnsError() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure ErpConfigService.getShuntPaymentInfoAsync(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setAppId("appId");
        paymentInfoDTO.setAppSecret("appSecret");
        paymentInfoDTO.setEmpId("empId");
        final Mono<PaymentInfoDTO> paymentInfoDTOMono = Mono.just(paymentInfoDTO);
        when(mockErpConfigService.getShuntPaymentInfoAsync("enterpriseGuid", "storeGuid",
                "4dc8fd4d-1c43-40f7-ad44-562beee10b14")).thenReturn(paymentInfoDTOMono);

        when(mockDeveloperConfig.getId()).thenReturn("developerId");
        when(mockDeveloperConfig.getKey()).thenReturn("result");

        // Configure AggPayRpcService.doAggPayReserve(...).
        final Mono<AggPayReserveResultDTO> aggPayReserveResultDTOMono = Mono.error(new Exception("message"));
        final AggPayReserveVO accountPayDTO = new AggPayReserveVO();
        accountPayDTO.setAppId("appId");
        accountPayDTO.setAttachData("attachData");
        accountPayDTO.setDeveloperId("developerId");
        accountPayDTO.setOrderGUID("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        accountPayDTO.setPayGUID("c92dca22-c797-4741-8530-9f89e54761da");
        accountPayDTO.setSignature("signature");
        accountPayDTO.setTimestamp(0L);
        when(mockAggPayRpcService.doAggPayReserve(accountPayDTO)).thenReturn(aggPayReserveResultDTOMono);

        // Run the test
        final Mono<AggPayReserveResultDTO> result = aggPayServiceImplUnderTest.reservePay(saasPollingDTO);

        // Verify the results
        verify(mockRedisService).putCallBackResp("4dc8fd4d-1c43-40f7-ad44-562beee10b14",
                "c92dca22-c797-4741-8530-9f89e54761da", "id");
    }

    @Test
    public void testQueryWxPubAccResult() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure AggPayRpcService.doWeChatPublicAccountPollingAsync(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("10000");
        aggPayPollingRespDTO.setMsg("success");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setBody("body");
        aggPayPollingRespDTO.setPaySt("id");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO);
        when(mockAggPayRpcService.doWeChatPublicAccountPollingAsync("4dc8fd4d-1c43-40f7-ad44-562beee10b14"))
                .thenReturn(aggPayPollingRespDTOMono);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.queryWxPubAccResult(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testQueryWxPubAccResult_AggPayRpcServiceReturnsNoItem() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        when(mockAggPayRpcService.doWeChatPublicAccountPollingAsync("4dc8fd4d-1c43-40f7-ad44-562beee10b14"))
                .thenReturn(Mono.empty());

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.queryWxPubAccResult(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testQueryWxPubAccResult_AggPayRpcServiceReturnsError() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setDeviceType(0);
        saasPollingDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingDTO.setStoreGuid("storeGuid");
        saasPollingDTO.setOrderGuid("4dc8fd4d-1c43-40f7-ad44-562beee10b14");
        saasPollingDTO.setPayGuid("c92dca22-c797-4741-8530-9f89e54761da");

        // Configure AggPayRpcService.doWeChatPublicAccountPollingAsync(...).
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.error(new Exception("message"));
        when(mockAggPayRpcService.doWeChatPublicAccountPollingAsync("4dc8fd4d-1c43-40f7-ad44-562beee10b14"))
                .thenReturn(aggPayPollingRespDTOMono);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayServiceImplUnderTest.queryWxPubAccResult(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testQueryPayRecord() {
        // Setup
        final BasePageDTO basePageDTO = new BasePageDTO();
        basePageDTO.setDeviceType(0);
        basePageDTO.setEnterpriseGuid("enterpriseGuid");
        basePageDTO.setStoreGuid("storeGuid");
        basePageDTO.setCurrentPage(0);
        basePageDTO.setPageSize(0);

        when(mockPayRecordService.page(any(PageAdapter.class), any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure AggPayMapstruct.createPayRecordResp(...).
        final AggPayRecordDTO aggPayRecordDTO = new AggPayRecordDTO();
        aggPayRecordDTO.setId(0L);
        aggPayRecordDTO.setStoreGuid("storeGuid");
        aggPayRecordDTO.setPayPowerId("payPowerId");
        aggPayRecordDTO.setPayPowerName("payPowerName");
        aggPayRecordDTO.setPayChannelId("payChannelId");
        final List<AggPayRecordDTO> aggPayRecordDTOS = Arrays.asList(aggPayRecordDTO);
        final PayRecordDO payRecordDO1 = new PayRecordDO();
        payRecordDO1.setStoreGuid("storeGuid");
        payRecordDO1.setOrderHolderNo("orderHolderNo");
        payRecordDO1.setPaySt("4");
        payRecordDO1.setGmtTimePaid(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        payRecordDO1.setRefOrderNo("refOrderNo");
        payRecordDO1.setGmtRefund(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PayRecordDO> payRecordDO = Arrays.asList(payRecordDO1);
        when(mockAggPayMapstruct.createPayRecordResp(payRecordDO)).thenReturn(aggPayRecordDTOS);

        // Run the test
        final Mono<Page<AggPayRecordDTO>> result = aggPayServiceImplUnderTest.queryPayRecord(basePageDTO);

        // Verify the results
    }

    @Test
    public void testQueryPayRecord_AggPayMapstructReturnsNoItems() {
        // Setup
        final BasePageDTO basePageDTO = new BasePageDTO();
        basePageDTO.setDeviceType(0);
        basePageDTO.setEnterpriseGuid("enterpriseGuid");
        basePageDTO.setStoreGuid("storeGuid");
        basePageDTO.setCurrentPage(0);
        basePageDTO.setPageSize(0);

        when(mockPayRecordService.page(any(PageAdapter.class), any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure AggPayMapstruct.createPayRecordResp(...).
        final PayRecordDO payRecordDO1 = new PayRecordDO();
        payRecordDO1.setStoreGuid("storeGuid");
        payRecordDO1.setOrderHolderNo("orderHolderNo");
        payRecordDO1.setPaySt("4");
        payRecordDO1.setGmtTimePaid(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        payRecordDO1.setRefOrderNo("refOrderNo");
        payRecordDO1.setGmtRefund(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PayRecordDO> payRecordDO = Arrays.asList(payRecordDO1);
        when(mockAggPayMapstruct.createPayRecordResp(payRecordDO)).thenReturn(Collections.emptyList());

        // Run the test
        final Mono<Page<AggPayRecordDTO>> result = aggPayServiceImplUnderTest.queryPayRecord(basePageDTO);

        // Verify the results
    }

    @Test
    public void testQueryPayStatistics() {
        // Setup
        final BasePageDTO basePageDTO = new BasePageDTO();
        basePageDTO.setDeviceType(0);
        basePageDTO.setEnterpriseGuid("enterpriseGuid");
        basePageDTO.setStoreGuid("storeGuid");
        basePageDTO.setCurrentPage(0);
        basePageDTO.setPageSize(0);

        when(mockPayRecordService.page(any(PageAdapter.class), any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure AggPayMapstruct.createPayRecordResp(...).
        final AggPayRecordDTO aggPayRecordDTO = new AggPayRecordDTO();
        aggPayRecordDTO.setId(0L);
        aggPayRecordDTO.setStoreGuid("storeGuid");
        aggPayRecordDTO.setPayPowerId("payPowerId");
        aggPayRecordDTO.setPayPowerName("payPowerName");
        aggPayRecordDTO.setPayChannelId("payChannelId");
        final List<AggPayRecordDTO> aggPayRecordDTOS = Arrays.asList(aggPayRecordDTO);
        final PayRecordDO payRecordDO1 = new PayRecordDO();
        payRecordDO1.setStoreGuid("storeGuid");
        payRecordDO1.setOrderHolderNo("orderHolderNo");
        payRecordDO1.setPaySt("4");
        payRecordDO1.setGmtTimePaid(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        payRecordDO1.setRefOrderNo("refOrderNo");
        payRecordDO1.setGmtRefund(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PayRecordDO> payRecordDO = Arrays.asList(payRecordDO1);
        when(mockAggPayMapstruct.createPayRecordResp(payRecordDO)).thenReturn(aggPayRecordDTOS);

        // Configure PayRecordService.getStatisticsTotalAmount(...).
        final BasePageDTO basePageDTO1 = new BasePageDTO();
        basePageDTO1.setDeviceType(0);
        basePageDTO1.setEnterpriseGuid("enterpriseGuid");
        basePageDTO1.setStoreGuid("storeGuid");
        basePageDTO1.setCurrentPage(0);
        basePageDTO1.setPageSize(0);
        when(mockPayRecordService.getStatisticsTotalAmount(basePageDTO1)).thenReturn(new BigDecimal("0.00"));

        // Run the test
        final Mono<AggPayStatisticsDTO> result = aggPayServiceImplUnderTest.queryPayStatistics(basePageDTO);

        // Verify the results
    }

    @Test
    public void testQueryPayStatistics_AggPayMapstructReturnsNoItems() {
        // Setup
        final BasePageDTO basePageDTO = new BasePageDTO();
        basePageDTO.setDeviceType(0);
        basePageDTO.setEnterpriseGuid("enterpriseGuid");
        basePageDTO.setStoreGuid("storeGuid");
        basePageDTO.setCurrentPage(0);
        basePageDTO.setPageSize(0);

        when(mockPayRecordService.page(any(PageAdapter.class), any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure AggPayMapstruct.createPayRecordResp(...).
        final PayRecordDO payRecordDO1 = new PayRecordDO();
        payRecordDO1.setStoreGuid("storeGuid");
        payRecordDO1.setOrderHolderNo("orderHolderNo");
        payRecordDO1.setPaySt("4");
        payRecordDO1.setGmtTimePaid(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        payRecordDO1.setRefOrderNo("refOrderNo");
        payRecordDO1.setGmtRefund(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<PayRecordDO> payRecordDO = Arrays.asList(payRecordDO1);
        when(mockAggPayMapstruct.createPayRecordResp(payRecordDO)).thenReturn(Collections.emptyList());

        // Configure PayRecordService.getStatisticsTotalAmount(...).
        final BasePageDTO basePageDTO1 = new BasePageDTO();
        basePageDTO1.setDeviceType(0);
        basePageDTO1.setEnterpriseGuid("enterpriseGuid");
        basePageDTO1.setStoreGuid("storeGuid");
        basePageDTO1.setCurrentPage(0);
        basePageDTO1.setPageSize(0);
        when(mockPayRecordService.getStatisticsTotalAmount(basePageDTO1)).thenReturn(new BigDecimal("0.00"));

        // Run the test
        final Mono<AggPayStatisticsDTO> result = aggPayServiceImplUnderTest.queryPayStatistics(basePageDTO);

        // Verify the results
    }
}
