package com.holderzone.holder.saas.store.deposit.service.impl;

import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.holderzone.holder.saas.store.deposit.entity.bo.RemindDO;
import com.holderzone.holder.saas.store.deposit.mapper.HsdRemindMapper;
import com.holderzone.holder.saas.store.deposit.mapstruct.RemindMapstruct;
import com.holderzone.holder.saas.store.deposit.service.DistributedIdService;
import com.holderzone.holder.saas.store.deposit.service.rpc.StoreRpcService;
import com.holderzone.saas.store.dto.deposit.req.MessageRemindReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;

import java.time.LocalDate;

import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@ContextConfiguration(classes = {HsdRemindServiceImpl.class})
@RunWith(SpringJUnit4ClassRunner.class)
public class HsdRemindServiceImplDiffblueTest {
    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @MockBean
    private DistributedIdService distributedIdService;

    @MockBean
    private HsdRemindMapper hsdRemindMapper;

    @Autowired
    private HsdRemindServiceImpl hsdRemindServiceImpl;

    @MockBean
    private RemindMapstruct remindMapstruct;

    @MockBean
    private StoreRpcService storeRpcService;

    /**
     * Method under test:
     * {@link HsdRemindServiceImpl#createRemindRecord(MessageRemindReqDTO)}
     */
    @Test
    public void testCreateRemindRecord() {
        when(hsdRemindMapper.update(Mockito.<RemindDO>any(), Mockito.<Wrapper<RemindDO>>any())).thenReturn(1);
        when(hsdRemindMapper.selectCount(Mockito.<Wrapper<RemindDO>>any())).thenReturn(3);

        RemindDO remindDO = new RemindDO();
        remindDO.setAdvanceDays(1);
        remindDO.setDepositRemind(1);
        remindDO.setExpireRemind(1);
        remindDO.setGetRemind(1);
        remindDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        remindDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        remindDO.setGuid("1234");
        remindDO.setId(1L);
        remindDO.setStoreGuid("1234");
        remindDO.setStoreName("Store Name");
        when(remindMapstruct.fromRemindDTO(Mockito.<MessageRemindReqDTO>any())).thenReturn(remindDO);

        MessageRemindReqDTO messageRemindReqDTO = new MessageRemindReqDTO();
        messageRemindReqDTO.setAdvanceDays(1);
        messageRemindReqDTO.setDepositRemind(1);
        messageRemindReqDTO.setExpireRemind(1);
        messageRemindReqDTO.setGetRemind(1);
        messageRemindReqDTO.setStoreGuid("1234");
        messageRemindReqDTO.setStoreName("Store Name");
        Boolean actualCreateRemindRecordResult = hsdRemindServiceImpl.createRemindRecord(messageRemindReqDTO);
        verify(hsdRemindMapper).selectCount(Mockito.<Wrapper<RemindDO>>any());
        verify(hsdRemindMapper).update(Mockito.<RemindDO>any(), Mockito.<Wrapper<RemindDO>>any());
        verify(remindMapstruct).fromRemindDTO(Mockito.<MessageRemindReqDTO>any());
        assertTrue(actualCreateRemindRecordResult);
    }

    /**
     * Method under test:
     * {@link HsdRemindServiceImpl#createRemindRecord(MessageRemindReqDTO)}
     */
    @Test
    public void testCreateRemindRecord2() {
        when(remindMapstruct.fromRemindDTO(Mockito.<MessageRemindReqDTO>any()))
                .thenThrow(new IllegalArgumentException("foo"));

        MessageRemindReqDTO messageRemindReqDTO = new MessageRemindReqDTO();
        messageRemindReqDTO.setAdvanceDays(1);
        messageRemindReqDTO.setDepositRemind(1);
        messageRemindReqDTO.setExpireRemind(1);
        messageRemindReqDTO.setGetRemind(1);
        messageRemindReqDTO.setStoreGuid("1234");
        messageRemindReqDTO.setStoreName("Store Name");
        thrown.expect(IllegalArgumentException.class);
        hsdRemindServiceImpl.createRemindRecord(messageRemindReqDTO);
        verify(remindMapstruct).fromRemindDTO(Mockito.<MessageRemindReqDTO>any());
    }

    /**
     * Method under test:
     * {@link HsdRemindServiceImpl#createRemindRecord(MessageRemindReqDTO)}
     */
    @Test
    public void testCreateRemindRecord3() {
        when(hsdRemindMapper.insert(Mockito.<RemindDO>any())).thenReturn(1);
        when(hsdRemindMapper.selectCount(Mockito.<Wrapper<RemindDO>>any())).thenReturn(0);
        when(distributedIdService.nextRemindGuid()).thenReturn("1234");

        RemindDO remindDO = new RemindDO();
        remindDO.setAdvanceDays(1);
        remindDO.setDepositRemind(1);
        remindDO.setExpireRemind(1);
        remindDO.setGetRemind(1);
        remindDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        remindDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        remindDO.setGuid("1234");
        remindDO.setId(1L);
        remindDO.setStoreGuid("1234");
        remindDO.setStoreName("Store Name");
        when(remindMapstruct.fromRemindDTO(Mockito.<MessageRemindReqDTO>any())).thenReturn(remindDO);

        MessageRemindReqDTO messageRemindReqDTO = new MessageRemindReqDTO();
        messageRemindReqDTO.setAdvanceDays(1);
        messageRemindReqDTO.setDepositRemind(1);
        messageRemindReqDTO.setExpireRemind(1);
        messageRemindReqDTO.setGetRemind(1);
        messageRemindReqDTO.setStoreGuid("1234");
        messageRemindReqDTO.setStoreName("Store Name");
        Boolean actualCreateRemindRecordResult = hsdRemindServiceImpl.createRemindRecord(messageRemindReqDTO);
        verify(hsdRemindMapper).insert(Mockito.<RemindDO>any());
        verify(hsdRemindMapper).selectCount(Mockito.<Wrapper<RemindDO>>any());
        verify(remindMapstruct).fromRemindDTO(Mockito.<MessageRemindReqDTO>any());
        verify(distributedIdService).nextRemindGuid();
        assertTrue(actualCreateRemindRecordResult);
    }

    /**
     * Method under test:
     * {@link HsdRemindServiceImpl#createRemindRecord(MessageRemindReqDTO)}
     */
    @Test
    public void testCreateRemindRecord4() {
        when(hsdRemindMapper.selectCount(Mockito.<Wrapper<RemindDO>>any())).thenReturn(0);
        when(distributedIdService.nextRemindGuid()).thenThrow(new IllegalArgumentException("foo"));

        RemindDO remindDO = new RemindDO();
        remindDO.setAdvanceDays(1);
        remindDO.setDepositRemind(1);
        remindDO.setExpireRemind(1);
        remindDO.setGetRemind(1);
        remindDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        remindDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        remindDO.setGuid("1234");
        remindDO.setId(1L);
        remindDO.setStoreGuid("1234");
        remindDO.setStoreName("Store Name");
        when(remindMapstruct.fromRemindDTO(Mockito.<MessageRemindReqDTO>any())).thenReturn(remindDO);

        MessageRemindReqDTO messageRemindReqDTO = new MessageRemindReqDTO();
        messageRemindReqDTO.setAdvanceDays(1);
        messageRemindReqDTO.setDepositRemind(1);
        messageRemindReqDTO.setExpireRemind(1);
        messageRemindReqDTO.setGetRemind(1);
        messageRemindReqDTO.setStoreGuid("1234");
        messageRemindReqDTO.setStoreName("Store Name");
        thrown.expect(IllegalArgumentException.class);
        hsdRemindServiceImpl.createRemindRecord(messageRemindReqDTO);
        verify(hsdRemindMapper).selectCount(Mockito.<Wrapper<RemindDO>>any());
        verify(remindMapstruct).fromRemindDTO(Mockito.<MessageRemindReqDTO>any());
        verify(distributedIdService).nextRemindGuid();
    }

    /**
     * Method under test: {@link HsdRemindServiceImpl#queryRemindRecord(String)}
     */
    @Test
    public void testQueryRemindRecord() {
        RemindDO remindDO = new RemindDO();
        remindDO.setAdvanceDays(1);
        remindDO.setDepositRemind(1);
        remindDO.setExpireRemind(1);
        remindDO.setGetRemind(1);
        remindDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        remindDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        remindDO.setGuid("1234");
        remindDO.setId(1L);
        remindDO.setStoreGuid("1234");
        remindDO.setStoreName("Store Name");
        when(hsdRemindMapper.selectOne(Mockito.<Wrapper<RemindDO>>any())).thenReturn(remindDO);
        when(hsdRemindMapper.selectCount(Mockito.<Wrapper<RemindDO>>any())).thenReturn(3);
        when(storeRpcService.queryStoreByGuid(Mockito.<String>any())).thenReturn(new StoreDTO());

        MessageRemindReqDTO messageRemindReqDTO = new MessageRemindReqDTO();
        messageRemindReqDTO.setAdvanceDays(1);
        messageRemindReqDTO.setDepositRemind(1);
        messageRemindReqDTO.setExpireRemind(1);
        messageRemindReqDTO.setGetRemind(1);
        messageRemindReqDTO.setStoreGuid("1234");
        messageRemindReqDTO.setStoreName("Store Name");
        when(remindMapstruct.fromRemindDO(Mockito.<RemindDO>any())).thenReturn(messageRemindReqDTO);
        MessageRemindReqDTO actualQueryRemindRecordResult = hsdRemindServiceImpl.queryRemindRecord("1234");
        verify(hsdRemindMapper).selectCount(Mockito.<Wrapper<RemindDO>>any());
        verify(hsdRemindMapper).selectOne(Mockito.<Wrapper<RemindDO>>any());
        verify(remindMapstruct).fromRemindDO(Mockito.<RemindDO>any());
        verify(storeRpcService).queryStoreByGuid(Mockito.<String>any());
        assertSame(messageRemindReqDTO, actualQueryRemindRecordResult);
    }

    /**
     * Method under test: {@link HsdRemindServiceImpl#queryRemindRecord(String)}
     */
    @Test
    public void testQueryRemindRecord2() {
        RemindDO remindDO = new RemindDO();
        remindDO.setAdvanceDays(1);
        remindDO.setDepositRemind(1);
        remindDO.setExpireRemind(1);
        remindDO.setGetRemind(1);
        remindDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        remindDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        remindDO.setGuid("1234");
        remindDO.setId(1L);
        remindDO.setStoreGuid("1234");
        remindDO.setStoreName("Store Name");
        when(hsdRemindMapper.selectOne(Mockito.<Wrapper<RemindDO>>any())).thenReturn(remindDO);
        when(hsdRemindMapper.selectCount(Mockito.<Wrapper<RemindDO>>any())).thenReturn(3);
        when(storeRpcService.queryStoreByGuid(Mockito.<String>any())).thenReturn(new StoreDTO());
        when(remindMapstruct.fromRemindDO(Mockito.<RemindDO>any())).thenThrow(new IllegalArgumentException("查询短信配置记录:{}"));
        thrown.expect(IllegalArgumentException.class);
        hsdRemindServiceImpl.queryRemindRecord("1234");
        verify(hsdRemindMapper).selectCount(Mockito.<Wrapper<RemindDO>>any());
        verify(hsdRemindMapper).selectOne(Mockito.<Wrapper<RemindDO>>any());
        verify(remindMapstruct).fromRemindDO(Mockito.<RemindDO>any());
        verify(storeRpcService).queryStoreByGuid(Mockito.<String>any());
    }
}
