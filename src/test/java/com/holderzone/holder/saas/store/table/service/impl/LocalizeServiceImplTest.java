package com.holderzone.holder.saas.store.table.service.impl;

import com.holderzone.holder.saas.store.table.domain.TableOrderDO;
import com.holderzone.holder.saas.store.table.service.TableOrderService;
import com.holderzone.saas.store.dto.table.LocalizeTableDTO;
import com.holderzone.saas.store.dto.table.LocalizeTableOrderReqDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collection;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class LocalizeServiceImplTest {

    @Mock
    private TableOrderService mockTableOrderService;

    private LocalizeServiceImpl localizeServiceImplUnderTest;

    @Before
    public void setUp() {
        localizeServiceImplUnderTest = new LocalizeServiceImpl(mockTableOrderService);
    }

    @Test
    public void testUploadLocalData() {
        // Setup
        final LocalizeTableDTO localizeTableDTO = new LocalizeTableDTO();
        localizeTableDTO.setTableGuid("tableGuid");
        localizeTableDTO.setMainOrderGuid("mainOrderGuid");
        localizeTableDTO.setOrderGuid("orderGuid");
        localizeTableDTO.setStatus(0);
        localizeTableDTO.setSubStatus("subStatus");
        final LocalizeTableOrderReqDTO localizeTableOrderReqDTO = new LocalizeTableOrderReqDTO(
                Arrays.asList(localizeTableDTO));

        // Configure TableOrderService.updateBatchByTableOrderGuid(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setGuid("710fba62-9828-42fd-a5c2-035b43ddae90");
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("orderGuid");
        final Collection<TableOrderDO> entityList = Arrays.asList(tableOrderDO);
        when(mockTableOrderService.updateBatchByTableOrderGuid(entityList)).thenReturn(false);

        // Run the test
        final boolean result = localizeServiceImplUnderTest.uploadLocalData(localizeTableOrderReqDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testUploadLocalData_TableOrderServiceReturnsTrue() {
        // Setup
        final LocalizeTableDTO localizeTableDTO = new LocalizeTableDTO();
        localizeTableDTO.setTableGuid("tableGuid");
        localizeTableDTO.setMainOrderGuid("mainOrderGuid");
        localizeTableDTO.setOrderGuid("orderGuid");
        localizeTableDTO.setStatus(0);
        localizeTableDTO.setSubStatus("subStatus");
        final LocalizeTableOrderReqDTO localizeTableOrderReqDTO = new LocalizeTableOrderReqDTO(
                Arrays.asList(localizeTableDTO));

        // Configure TableOrderService.updateBatchByTableOrderGuid(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setGuid("710fba62-9828-42fd-a5c2-035b43ddae90");
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("orderGuid");
        final Collection<TableOrderDO> entityList = Arrays.asList(tableOrderDO);
        when(mockTableOrderService.updateBatchByTableOrderGuid(entityList)).thenReturn(true);

        // Run the test
        final boolean result = localizeServiceImplUnderTest.uploadLocalData(localizeTableOrderReqDTO);

        // Verify the results
        assertThat(result).isTrue();
    }
}
