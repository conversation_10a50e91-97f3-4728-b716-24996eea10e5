package com.holderzone.holder.saas.store.report.controller;

import com.holderzone.holder.saas.store.report.service.TradeChangeService;
import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.base.Pager;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.ChangeDetailDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashMap;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(TradeChangeController.class)
public class TradeChangeControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private TradeChangeService mockTradeChangeService;

    @Test
    public void testList() throws Exception {
        // Setup
        // Configure TradeChangeService.list(...).
        final ChangeDetailDTO changeDetailDTO = new ChangeDetailDTO();
        changeDetailDTO.setBrandName("brandName");
        changeDetailDTO.setStoreName("storeName");
        changeDetailDTO.setCancelFlag(0);
        changeDetailDTO.setChangeNode("changeNode");
        changeDetailDTO.setOrderNo("orderNo");
        final Pager pager = new Pager();
        pager.setPageNo(0);
        pager.setPageSize(0);
        pager.setTotalCount(0);
        pager.setTotalPages(0);
        final Message<ChangeDetailDTO> changeDetailDTOMessage = new Message<>(Arrays.asList(changeDetailDTO),
                new HashMap<>(), pager);
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeChangeService.list(query)).thenReturn(changeDetailDTOMessage);

        // Run the test and verify the results
        mockMvc.perform(post("/trade/change/list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testList_TradeChangeServiceReturnsNoItem() throws Exception {
        // Setup
        // Configure TradeChangeService.list(...).
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeChangeService.list(query)).thenReturn(Message.getInstance());

        // Run the test and verify the results
        mockMvc.perform(post("/trade/change/list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("", true));
    }

    @Test
    public void testExport() throws Exception {
        // Setup
        // Configure TradeChangeService.export(...).
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeChangeService.export(query)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/trade/change/export")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
