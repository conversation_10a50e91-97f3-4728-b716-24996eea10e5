package com.holderzone.holder.saas.store.report.service.impl;

import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.report.service.TradeItemService;
import com.holderzone.holder.saas.store.report.service.rpc.business.HandoverClientService;
import com.holderzone.saas.store.dto.journaling.req.SalesVolumeReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.SalesVolumeRespDTO;
import com.holderzone.saas.store.dto.report.query.HandOverReportQueryDTO;
import com.holderzone.saas.store.dto.report.resp.ExportRespDTO;
import com.holderzone.saas.store.dto.report.resp.HandoverReportRespDTO;
import com.holderzone.saas.store.dto.report.resp.HandoverReportStaffDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ExportServiceImplTest {

    @Mock
    private HandoverClientService mockHandoverClientService;
    @Mock
    private TradeItemService mockTradeItemService;

    private ExportServiceImpl exportServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        exportServiceImplUnderTest = new ExportServiceImpl(mockHandoverClientService, mockTradeItemService);
    }

    @Test
    public void testExport() throws Exception {
        // Setup
        // Configure TradeItemService.pageStoreSaleStatistics(...).
        final SalesVolumeRespDTO salesVolumeRespDTO = new SalesVolumeRespDTO();
        salesVolumeRespDTO.setItemName("itemName");
        salesVolumeRespDTO.setTypeName("typeName");
        salesVolumeRespDTO.setItemTypeName("itemTypeName");
        salesVolumeRespDTO.setSalesProportion(0.0);
        salesVolumeRespDTO.setSpotRate(0.0);
        final Page<SalesVolumeRespDTO> salesVolumeRespDTOPage = new Page<>(0L, 0L, Arrays.asList(salesVolumeRespDTO));
        final SalesVolumeReqDTO query = new SalesVolumeReqDTO();
        query.setCurrentPage(0L);
        query.setPageSize(0L);
        query.setData(Arrays.asList());
        query.setItemType(0);
        query.setTypeGuid("typeGuid");
        when(mockTradeItemService.pageStoreSaleStatistics(query)).thenReturn(salesVolumeRespDTOPage);

        // Configure HandoverClientService.report(...).
        final HandoverReportRespDTO handoverReportRespDTO = new HandoverReportRespDTO();
        handoverReportRespDTO.setStatisticalType("statisticalType");
        handoverReportRespDTO.setStatistical("statistical");
        final HandoverReportStaffDTO handoverReportStaffDTO = new HandoverReportStaffDTO();
        handoverReportStaffDTO.setUserGuid("userGuid");
        handoverReportStaffDTO.setUserName("userName");
        handoverReportStaffDTO.setStatistical("statistical");
        handoverReportRespDTO.setStaffStatisticals(Arrays.asList(handoverReportStaffDTO));
        final List<HandoverReportRespDTO> handoverReportRespDTOS = Arrays.asList(handoverReportRespDTO);
        final HandOverReportQueryDTO handOverQueryDTO = new HandOverReportQueryDTO();
        handOverQueryDTO.setState(0);
        handOverQueryDTO.setStoreGuid("storeGuid");
        handOverQueryDTO.setBusinessStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        handOverQueryDTO.setBusinessEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        handOverQueryDTO.setUserGuids(Arrays.asList("value"));
        when(mockHandoverClientService.report(handOverQueryDTO)).thenReturn(handoverReportRespDTOS);

        // Run the test
        final ExportRespDTO result = exportServiceImplUnderTest.export(0, "paramString");

        // Verify the results
    }

    @Test
    public void testExport_HandoverClientServiceReturnsNoItems() {
        // Setup
        // Configure HandoverClientService.report(...).
        final HandOverReportQueryDTO handOverQueryDTO = new HandOverReportQueryDTO();
        handOverQueryDTO.setState(0);
        handOverQueryDTO.setStoreGuid("storeGuid");
        handOverQueryDTO.setBusinessStartDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        handOverQueryDTO.setBusinessEndDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        handOverQueryDTO.setUserGuids(Arrays.asList("value"));
        when(mockHandoverClientService.report(handOverQueryDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final ExportRespDTO result = exportServiceImplUnderTest.export(0, "paramString");

        // Verify the results
    }
}
