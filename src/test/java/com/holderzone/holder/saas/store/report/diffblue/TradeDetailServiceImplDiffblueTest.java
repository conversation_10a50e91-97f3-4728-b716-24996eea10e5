package com.holderzone.holder.saas.store.report.diffblue;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.report.mapper.TradeDetailMapper;
import com.holderzone.holder.saas.store.report.service.impl.TradeDetailServiceImpl;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.report.query.PaymentConstituteQueryDTO;
import com.holderzone.saas.store.dto.report.resp.PaymentConstituteDTO;
import com.holderzone.saas.store.dto.report.resp.PaymentConstituteRespDTO;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@ContextConfiguration(classes = {TradeDetailServiceImpl.class})
@RunWith(SpringJUnit4ClassRunner.class)
public class TradeDetailServiceImplDiffblueTest {
    public static final String ENTERPRISE_GUID_NOT_NULL = "企业guid不为空";
    @Rule
    public ExpectedException thrown = ExpectedException.none();
    @MockBean
    private OssClient ossClient;

    @MockBean
    private TradeDetailMapper tradeDetailMapper;

    @Autowired
    private TradeDetailServiceImpl tradeDetailServiceImpl;

    /**
     * Method under test:
     * {@link TradeDetailServiceImpl#paymentConstitute(PaymentConstituteQueryDTO)}
     */
    @Test
    public void testPaymentConstitute() {
        when(tradeDetailMapper.summarizingPaymentConstitute(Mockito.<PaymentConstituteQueryDTO>any()))
                .thenReturn(new ArrayList<>());

        PaymentConstituteQueryDTO query = getPaymentConstituteQueryDTO();
        Page<PaymentConstituteRespDTO> actualPaymentConstituteResult = tradeDetailServiceImpl.paymentConstitute(query);
        verify(tradeDetailMapper).summarizingPaymentConstitute(Mockito.<PaymentConstituteQueryDTO>any());
        assertEquals(1L, actualPaymentConstituteResult.getCurrentPage());
        assertEquals(9L, actualPaymentConstituteResult.getTotalCount());
        assertEquals(9, actualPaymentConstituteResult.getData().size());
        assertEquals(1L, actualPaymentConstituteResult.getPageSize());
    }

    private PaymentConstituteQueryDTO getPaymentConstituteQueryDTO() {
        PaymentConstituteQueryDTO query = new PaymentConstituteQueryDTO();
        query.setBrandGuid(null);
        query.setCurrentPage(1L);
        query.setEndTime(LocalDate.parse("2024-02-28"));
        query.setEnterpriseGuid("4895");
        query.setPageSize(10L);
        query.setShowType(1);
        query.setStartTime(LocalDate.parse("2024-02-20"));
        List<String> storeGuidList = getStoreGuidList();
        query.setStoreGuidList(storeGuidList);
        return query;
    }

    private List<String> getStoreGuidList() {
        List<String> storeGuidList = new ArrayList<>();
        storeGuidList.add("4897");
        storeGuidList.add("4898");
        storeGuidList.add("4909");
        storeGuidList.add("4928");
        storeGuidList.add("4934");
        storeGuidList.add("4937");
        storeGuidList.add("4939");
        return storeGuidList;
    }

    /**
     * Method under test:
     * {@link TradeDetailServiceImpl#paymentConstitute(PaymentConstituteQueryDTO)}
     */
    @Test
    public void testPaymentConstitute2() {
        PaymentConstituteDTO paymentConstituteDTO = new PaymentConstituteDTO();
        paymentConstituteDTO.setBusinessDate(LocalDate.parse("2024-02-29"));
        paymentConstituteDTO.setDeposit(BigDecimal.ZERO);
        paymentConstituteDTO.setMemberRecharge(BigDecimal.ZERO);
        paymentConstituteDTO.setSalesRevenue(BigDecimal.ZERO);
        paymentConstituteDTO.setTotal(BigDecimal.ZERO);

        ArrayList<PaymentConstituteDTO> paymentConstituteDTOList = new ArrayList<>();
        paymentConstituteDTOList.add(paymentConstituteDTO);
        when(tradeDetailMapper.summarizingPaymentConstitute(Mockito.<PaymentConstituteQueryDTO>any()))
                .thenReturn(paymentConstituteDTOList);

        PaymentConstituteQueryDTO query = getPaymentConstituteQueryDTO();
        Page<PaymentConstituteRespDTO> actualPaymentConstituteResult2 = tradeDetailServiceImpl.paymentConstitute(query);
        verify(tradeDetailMapper).summarizingPaymentConstitute(Mockito.<PaymentConstituteQueryDTO>any());
        assertEquals(1L, actualPaymentConstituteResult2.getCurrentPage());
        assertEquals(9L, actualPaymentConstituteResult2.getTotalCount());
        assertEquals(9, actualPaymentConstituteResult2.getData().size());
        assertEquals(1L, actualPaymentConstituteResult2.getPageSize());
    }

    /**
     * Method under test:
     * {@link TradeDetailServiceImpl#paymentConstitute(PaymentConstituteQueryDTO)}
     */
    @Test
    public void testPaymentConstitute3() {
        PaymentConstituteDTO paymentConstituteDTO = getUnionPayConstituteDTO();

        PaymentConstituteDTO paymentConstituteDTO2 = getPaymentConstituteDTO2();

        ArrayList<PaymentConstituteDTO> paymentConstituteDTOList = new ArrayList<>();
        paymentConstituteDTOList.add(paymentConstituteDTO2);
        paymentConstituteDTOList.add(paymentConstituteDTO);
        when(tradeDetailMapper.summarizingPaymentConstitute(Mockito.<PaymentConstituteQueryDTO>any()))
                .thenReturn(paymentConstituteDTOList);

        PaymentConstituteQueryDTO query = getPaymentConstituteQueryDTO();
        Page<PaymentConstituteRespDTO> actualPaymentConstituteResult3 = tradeDetailServiceImpl.paymentConstitute(query);
        verify(tradeDetailMapper).summarizingPaymentConstitute(Mockito.<PaymentConstituteQueryDTO>any());
        assertEquals(1L, actualPaymentConstituteResult3.getCurrentPage());
        assertEquals(9L, actualPaymentConstituteResult3.getTotalCount());
        assertEquals(9, actualPaymentConstituteResult3.getData().size());
        assertEquals(1L, actualPaymentConstituteResult3.getPageSize());
    }

    private PaymentConstituteDTO getPaymentConstituteDTO2() {
        PaymentConstituteDTO paymentConstituteDTO2 = new PaymentConstituteDTO();
        paymentConstituteDTO2.setBusinessDate(LocalDate.of(2024, 2, 29));
        paymentConstituteDTO2.setDeposit(new BigDecimal("3"));
        paymentConstituteDTO2.setMemberRecharge(new BigDecimal("0"));
        paymentConstituteDTO2.setPayMethod("现金支付");
        paymentConstituteDTO2.setPaymentType(0);
        paymentConstituteDTO2.setSalesRevenue(new BigDecimal("4"));
        paymentConstituteDTO2.setStoreGuid("4934");
        paymentConstituteDTO2.setTotal(new BigDecimal("7"));
        return paymentConstituteDTO2;
    }

    private PaymentConstituteDTO getUnionPayConstituteDTO() {
        PaymentConstituteDTO unionPayConstituteDTO = new PaymentConstituteDTO();
        unionPayConstituteDTO.setBusinessDate(LocalDate.of(2024, 2, 29));
        unionPayConstituteDTO.setDeposit(new BigDecimal("5"));
        unionPayConstituteDTO.setMemberRecharge(new BigDecimal("3"));
        unionPayConstituteDTO.setPayMethod("银联支付");
        unionPayConstituteDTO.setPaymentType(3);
        unionPayConstituteDTO.setSalesRevenue(new BigDecimal("2"));
        unionPayConstituteDTO.setStoreGuid("4934");
        unionPayConstituteDTO.setTotal(new BigDecimal("10"));
        return unionPayConstituteDTO;
    }

    /**
     * Method under test:
     * {@link TradeDetailServiceImpl#paymentConstitute(PaymentConstituteQueryDTO)}
     */
    @Test
    public void testPaymentConstitute4() {
        PaymentConstituteQueryDTO query4 = mock(PaymentConstituteQueryDTO.class);
        when(query4.getEnterpriseGuid()).thenThrow(new BusinessException("企业guid为空"));
        when(query4.getEndTime()).thenReturn(LocalDate.of(2024, 2, 29));
        when(query4.getStartTime()).thenReturn(LocalDate.of(2024, 2, 20));
        when(query4.getShowType()).thenReturn(2);
        when(query4.getCurrentPage()).thenReturn(1L);
        when(query4.getPageSize()).thenReturn(3L);
        doNothing().when(query4).setCurrentPage(anyLong());
        doNothing().when(query4).setPageSize(anyLong());
        doNothing().when(query4).setBrandGuid(Mockito.<String>any());
        doNothing().when(query4).setEndTime(Mockito.<LocalDate>any());
        doNothing().when(query4).setEnterpriseGuid(Mockito.<String>any());
        doNothing().when(query4).setShowType(Mockito.<Integer>any());
        doNothing().when(query4).setStartTime(Mockito.<LocalDate>any());
        doNothing().when(query4).setStoreGuidList(Mockito.<List<String>>any());
        query4.setBrandGuid(null);
        query4.setCurrentPage(1L);
        query4.setEndTime(LocalDate.of(2024, 2, 29));
        query4.setEnterpriseGuid("4895");
        query4.setPageSize(3L);
        query4.setShowType(2);
        query4.setStartTime(LocalDate.of(2024, 2, 20));
        List<String> storeGuidList = getStoreGuidList();
        query4.setStoreGuidList(storeGuidList);
        tradeDetailServiceImpl.paymentConstitute(query4);
        verify(query4).getCurrentPage();
        verify(query4, atLeast(1)).getPageSize();
        verify(query4).setCurrentPage(anyLong());
        verify(query4).setPageSize(anyLong());
        verify(query4).getEndTime();
        verify(query4).getEnterpriseGuid();
        verify(query4).getShowType();
        verify(query4).getStartTime();
        verify(query4).setBrandGuid(Mockito.<String>any());
        verify(query4).setEndTime(Mockito.<LocalDate>any());
        verify(query4).setEnterpriseGuid(Mockito.<String>any());
        verify(query4).setShowType(Mockito.<Integer>any());
        verify(query4).setStartTime(Mockito.<LocalDate>any());
        verify(query4).setStoreGuidList(Mockito.<List<String>>any());
    }

    private StoreDTO getStoreDTO() {
        StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("4934");
        storeDTO.setName("门店A");
        storeDTO.setBelongBrandGuid("6977481831851491328");
        storeDTO.setBelongBrandName("销售品牌");
        return storeDTO;
    }
    /**
     * Method under test:
     * {@link TradeDetailServiceImpl#exportPaymentConstitute(PaymentConstituteQueryDTO)}
     */
    @Test
    public void testExportPaymentConstitute() {
        when(tradeDetailMapper.summarizingPaymentConstitute(Mockito.<PaymentConstituteQueryDTO>any()))
                .thenReturn(new ArrayList<>());
        when(ossClient.upload(Mockito.<String>any(), Mockito.<byte[]>any())).thenReturn("Upload");

        PaymentConstituteQueryDTO query = getConstituteQueryDTO();
        String actualExportPaymentConstituteResult = tradeDetailServiceImpl.exportPaymentConstitute(query);
        verify(ossClient).upload(Mockito.<String>any(), Mockito.<byte[]>any());
        verify(tradeDetailMapper).summarizingPaymentConstitute(Mockito.<PaymentConstituteQueryDTO>any());
        assertEquals("Upload", actualExportPaymentConstituteResult);
    }

    /**
     * Method under test:
     * {@link TradeDetailServiceImpl#exportPaymentConstitute(PaymentConstituteQueryDTO)}
     */
    @Test
    public void testExportPaymentConstitute2() {
        when(tradeDetailMapper.summarizingPaymentConstitute(Mockito.<PaymentConstituteQueryDTO>any()))
                .thenReturn(new ArrayList<>());
        when(ossClient.upload(Mockito.<String>any(), Mockito.<byte[]>any()))
                .thenThrow(new BusinessException(ENTERPRISE_GUID_NOT_NULL));

        PaymentConstituteQueryDTO query = getConstituteQueryDTO();
        thrown.expect(BusinessException.class);
        tradeDetailServiceImpl.exportPaymentConstitute(query);
        verify(ossClient).upload(Mockito.<String>any(), Mockito.<byte[]>any());
        verify(tradeDetailMapper).summarizingPaymentConstitute(Mockito.<PaymentConstituteQueryDTO>any());
    }

    private PaymentConstituteQueryDTO getConstituteQueryDTO() {
        PaymentConstituteQueryDTO query = new PaymentConstituteQueryDTO();
        query.setBrandGuid(null);
        query.setCurrentPage(1L);
        query.setEndTime(LocalDate.of(2024, 2, 29));
        query.setEnterpriseGuid("4895");
        query.setPageSize(3L);
        query.setShowType(1);
        query.setStartTime(LocalDate.of(2024, 2, 20));
        List<String> storeGuidList = getStoreGuidList();
        query.setStoreGuidList(storeGuidList);
        return query;
    }

    /**
     * Method under test:
     * {@link TradeDetailServiceImpl#exportPaymentConstitute(PaymentConstituteQueryDTO)}
     */
    @Test
    public void testExportPaymentConstitute3() {
        PaymentConstituteDTO paymentConstituteDTO = getMTConstituteDTO();

        ArrayList<PaymentConstituteDTO> paymentConstituteDTOList = new ArrayList<>();
        paymentConstituteDTOList.add(paymentConstituteDTO);
        when(tradeDetailMapper.summarizingPaymentConstitute(Mockito.<PaymentConstituteQueryDTO>any()))
                .thenReturn(paymentConstituteDTOList);
        when(ossClient.upload(Mockito.<String>any(), Mockito.<byte[]>any())).thenReturn("Upload");

        PaymentConstituteQueryDTO query = getConstituteQueryDTO();
        String actualExportPaymentConstituteResult = tradeDetailServiceImpl.exportPaymentConstitute(query);
        verify(ossClient).upload(Mockito.<String>any(), Mockito.<byte[]>any());
        verify(tradeDetailMapper).summarizingPaymentConstitute(Mockito.<PaymentConstituteQueryDTO>any());
        assertEquals("Upload", actualExportPaymentConstituteResult);
    }

    private PaymentConstituteDTO getMTConstituteDTO() {
        PaymentConstituteDTO paymentMTConstituteDTO = new PaymentConstituteDTO();
        paymentMTConstituteDTO.setBusinessDate(LocalDate.of(2024, 2, 2));
        paymentMTConstituteDTO.setDeposit(new BigDecimal("0"));
        paymentMTConstituteDTO.setMemberRecharge(new BigDecimal("0"));
        paymentMTConstituteDTO.setPayMethod("美团团购");
        paymentMTConstituteDTO.setPaymentType(20);
        paymentMTConstituteDTO.setSalesRevenue(new BigDecimal("0.2"));
        paymentMTConstituteDTO.setStoreGuid("4934");
        paymentMTConstituteDTO.setTotal(new BigDecimal("0.2"));
        return paymentMTConstituteDTO;
    }

    /**
     * Method under test:
     * {@link TradeDetailServiceImpl#exportPaymentConstitute(PaymentConstituteQueryDTO)}
     */
    @Test
    public void testExportPaymentConstitute4() {
        PaymentConstituteDTO paymentConstituteDTO = getAggPayConstituteDTO();

        PaymentConstituteDTO paymentConstituteDTO2 = getMemberPayConstituteDTO();

        ArrayList<PaymentConstituteDTO> paymentConstituteDTOList = new ArrayList<>();
        paymentConstituteDTOList.add(paymentConstituteDTO2);
        paymentConstituteDTOList.add(paymentConstituteDTO);
        when(tradeDetailMapper.summarizingPaymentConstitute(Mockito.<PaymentConstituteQueryDTO>any()))
                .thenReturn(paymentConstituteDTOList);
        when(ossClient.upload(Mockito.<String>any(), Mockito.<byte[]>any())).thenReturn("Upload");

        PaymentConstituteQueryDTO query = getConstituteQueryDTO();
        String actualExportPaymentConstituteResult = tradeDetailServiceImpl.exportPaymentConstitute(query);
        verify(ossClient).upload(Mockito.<String>any(), Mockito.<byte[]>any());
        verify(tradeDetailMapper).summarizingPaymentConstitute(Mockito.<PaymentConstituteQueryDTO>any());
        assertEquals("Upload", actualExportPaymentConstituteResult);
    }

    private PaymentConstituteDTO getMemberPayConstituteDTO() {
        PaymentConstituteDTO memberPayConstituteDTO = new PaymentConstituteDTO();
        memberPayConstituteDTO.setBusinessDate(LocalDate.of(2024, 2, 2));
        memberPayConstituteDTO.setDeposit(new BigDecimal("23"));
        memberPayConstituteDTO.setMemberRecharge(new BigDecimal("15"));
        memberPayConstituteDTO.setPayMethod("会员余额支付");
        memberPayConstituteDTO.setPaymentType(4);
        memberPayConstituteDTO.setSalesRevenue(new BigDecimal("0"));
        memberPayConstituteDTO.setStoreGuid("4934");
        memberPayConstituteDTO.setTotal(new BigDecimal("38"));
        return memberPayConstituteDTO;
    }

    private PaymentConstituteDTO getAggPayConstituteDTO() {
        PaymentConstituteDTO paymentAggPayConstituteDTO = new PaymentConstituteDTO();
        paymentAggPayConstituteDTO.setBusinessDate(LocalDate.of(2024, 2, 29));
        paymentAggPayConstituteDTO.setDeposit(new BigDecimal("3"));
        paymentAggPayConstituteDTO.setMemberRecharge(new BigDecimal("5"));
        paymentAggPayConstituteDTO.setPayMethod("聚合支付");
        paymentAggPayConstituteDTO.setPaymentType(2);
        paymentAggPayConstituteDTO.setSalesRevenue(new BigDecimal("6"));
        paymentAggPayConstituteDTO.setStoreGuid("4934");
        paymentAggPayConstituteDTO.setTotal(new BigDecimal("14"));
        return paymentAggPayConstituteDTO;
    }

    /**
     * Method under test:
     * {@link TradeDetailServiceImpl#exportPaymentConstitute(PaymentConstituteQueryDTO)}
     */
    @Test
    public void testExportPaymentConstitute5() {
        PaymentConstituteQueryDTO queryExport5 = mock(PaymentConstituteQueryDTO.class);
        when(queryExport5.getEnterpriseGuid()).thenThrow(new BusinessException(ENTERPRISE_GUID_NOT_NULL));
        when(queryExport5.getStartTime()).thenReturn(LocalDate.of(2024, 2, 20));
        when(queryExport5.getEndTime()).thenReturn(LocalDate.of(2024, 2, 29));
        when(queryExport5.getShowType()).thenReturn(2);
        when(queryExport5.getPageSize()).thenReturn(3L);
        when(queryExport5.getCurrentPage()).thenReturn(1L);
        doNothing().when(queryExport5).setCurrentPage(anyLong());
        doNothing().when(queryExport5).setPageSize(anyLong());
        doNothing().when(queryExport5).setEndTime(Mockito.<LocalDate>any());
        doNothing().when(queryExport5).setBrandGuid(Mockito.<String>any());
        doNothing().when(queryExport5).setEnterpriseGuid(Mockito.<String>any());
        doNothing().when(queryExport5).setStartTime(Mockito.<LocalDate>any());
        doNothing().when(queryExport5).setShowType(Mockito.<Integer>any());
        doNothing().when(queryExport5).setStoreGuidList(Mockito.<List<String>>any());
        queryExport5.setBrandGuid(null);
        queryExport5.setCurrentPage(1L);
        queryExport5.setEnterpriseGuid("4895");
        queryExport5.setEndTime(LocalDate.of(2024, 2, 29));
        queryExport5.setShowType(2);
        queryExport5.setPageSize(3L);
        queryExport5.setStartTime(LocalDate.of(2024, 2, 20));
        List<String> storeGuidList = getStoreGuidList();
        queryExport5.setStoreGuidList(storeGuidList);
        tradeDetailServiceImpl.exportPaymentConstitute(queryExport5);
        verify(queryExport5).getCurrentPage();
        verify(queryExport5, atLeast(1)).getPageSize();
        verify(queryExport5).setPageSize(anyLong());
        verify(queryExport5).getEndTime();
        verify(queryExport5).setCurrentPage(anyLong());
        verify(queryExport5).getEnterpriseGuid();
        verify(queryExport5).getStartTime();
        verify(queryExport5).setBrandGuid(Mockito.<String>any());
        verify(queryExport5).getShowType();
        verify(queryExport5).setEndTime(Mockito.<LocalDate>any());
        verify(queryExport5).setStartTime(Mockito.<LocalDate>any());
        verify(queryExport5).setShowType(Mockito.<Integer>any());
        verify(queryExport5).setStoreGuidList(Mockito.<List<String>>any());
        verify(queryExport5).setEnterpriseGuid(Mockito.<String>any());
    }
}
