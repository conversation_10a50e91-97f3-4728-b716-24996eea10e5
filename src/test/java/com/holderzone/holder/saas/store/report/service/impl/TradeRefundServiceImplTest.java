package com.holderzone.holder.saas.store.report.service.impl;

import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.holder.saas.store.report.mapper.TradeRefundMapper;
import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.RefundDetailDTO;
import com.holderzone.saas.store.dto.report.resp.TotalStatisticsDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TradeRefundServiceImplTest {

    @Mock
    private TradeRefundMapper mockTradeRefundMapper;
    @Mock
    private OssClient mockOssClient;

    private TradeRefundServiceImpl tradeRefundServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        tradeRefundServiceImplUnderTest = new TradeRefundServiceImpl(mockTradeRefundMapper, mockOssClient);
    }

    @Test
    public void testList() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeRefundMapper.statistics(...).
        final TotalStatisticsDTO totalStatisticsDTO = new TotalStatisticsDTO("brandGuid", "storeGuid", "goodsGuid",
                new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"));
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeRefundMapper.statistics(query1)).thenReturn(totalStatisticsDTO);

        // Configure TradeRefundMapper.pageInfo(...).
        final RefundDetailDTO refundDetailDTO = new RefundDetailDTO();
        refundDetailDTO.setType(0);
        refundDetailDTO.setActuallyPayFee(new BigDecimal("0.00"));
        refundDetailDTO.setRefundAmount(new BigDecimal("0.00"));
        refundDetailDTO.setAuthStaffName("-");
        refundDetailDTO.setRefundDetails("");
        refundDetailDTO.setMemberPhone("-");
        final List<RefundDetailDTO> refundDetailDTOS = Arrays.asList(refundDetailDTO);
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeRefundMapper.pageInfo(query2)).thenReturn(refundDetailDTOS);

        // Run the test
        final Message<RefundDetailDTO> result = tradeRefundServiceImplUnderTest.list(query);

        // Verify the results
    }

    @Test
    public void testList_TradeRefundMapperPageInfoReturnsNoItems() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeRefundMapper.statistics(...).
        final TotalStatisticsDTO totalStatisticsDTO = new TotalStatisticsDTO("brandGuid", "storeGuid", "goodsGuid",
                new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"));
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeRefundMapper.statistics(query1)).thenReturn(totalStatisticsDTO);

        // Configure TradeRefundMapper.pageInfo(...).
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeRefundMapper.pageInfo(query2)).thenReturn(Collections.emptyList());

        // Run the test
        final Message<RefundDetailDTO> result = tradeRefundServiceImplUnderTest.list(query);

        // Verify the results
    }

    @Test
    public void testExport() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeRefundMapper.count(...).
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeRefundMapper.count(query1)).thenReturn(0);

        // Configure TradeRefundMapper.pageInfo(...).
        final RefundDetailDTO refundDetailDTO = new RefundDetailDTO();
        refundDetailDTO.setType(0);
        refundDetailDTO.setActuallyPayFee(new BigDecimal("0.00"));
        refundDetailDTO.setRefundAmount(new BigDecimal("0.00"));
        refundDetailDTO.setAuthStaffName("-");
        refundDetailDTO.setRefundDetails("");
        refundDetailDTO.setMemberPhone("-");
        final List<RefundDetailDTO> refundDetailDTOS = Arrays.asList(refundDetailDTO);
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeRefundMapper.pageInfo(query2)).thenReturn(refundDetailDTOS);

        // Run the test
        final String result = tradeRefundServiceImplUnderTest.export(query);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testExport_TradeRefundMapperPageInfoReturnsNoItems() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeRefundMapper.count(...).
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeRefundMapper.count(query1)).thenReturn(0);

        // Configure TradeRefundMapper.pageInfo(...).
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeRefundMapper.pageInfo(query2)).thenReturn(Collections.emptyList());

        // Run the test
        final String result = tradeRefundServiceImplUnderTest.export(query);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }
}
