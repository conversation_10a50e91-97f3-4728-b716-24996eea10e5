package com.holderzone.holder.saas.store.report.service.impl;

import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.holder.saas.store.report.mapper.TradeChangeMapper;
import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.ChangeDetailDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TradeChangeServiceImplTest {

    @Mock
    private TradeChangeMapper mockTradeChangeMapper;
    @Mock
    private OssClient mockOssClient;

    private TradeChangeServiceImpl tradeChangeServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        tradeChangeServiceImplUnderTest = new TradeChangeServiceImpl(mockTradeChangeMapper, mockOssClient);
    }

    @Test
    public void testList() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeChangeMapper.count(...).
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeChangeMapper.count(query1)).thenReturn(0);

        // Configure TradeChangeMapper.pageInfo(...).
        final ChangeDetailDTO changeDetailDTO = new ChangeDetailDTO();
        changeDetailDTO.setChangeNode("changeNode");
        changeDetailDTO.setChangeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        changeDetailDTO.setOriginalItemPrice("0.00");
        changeDetailDTO.setChangeItemPrice("0.00");
        changeDetailDTO.setChangePrice(new BigDecimal("0.00"));
        final List<ChangeDetailDTO> changeDetailDTOS = Arrays.asList(changeDetailDTO);
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeChangeMapper.pageInfo(query2)).thenReturn(changeDetailDTOS);

        // Run the test
        final Message<ChangeDetailDTO> result = tradeChangeServiceImplUnderTest.list(query);

        // Verify the results
    }

    @Test
    public void testList_TradeChangeMapperPageInfoReturnsNoItems() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeChangeMapper.count(...).
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeChangeMapper.count(query1)).thenReturn(0);

        // Configure TradeChangeMapper.pageInfo(...).
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeChangeMapper.pageInfo(query2)).thenReturn(Collections.emptyList());

        // Run the test
        final Message<ChangeDetailDTO> result = tradeChangeServiceImplUnderTest.list(query);

        // Verify the results
    }

    @Test
    public void testExport() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeChangeMapper.count(...).
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeChangeMapper.count(query1)).thenReturn(0);

        // Configure TradeChangeMapper.pageInfo(...).
        final ChangeDetailDTO changeDetailDTO = new ChangeDetailDTO();
        changeDetailDTO.setChangeNode("changeNode");
        changeDetailDTO.setChangeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        changeDetailDTO.setOriginalItemPrice("0.00");
        changeDetailDTO.setChangeItemPrice("0.00");
        changeDetailDTO.setChangePrice(new BigDecimal("0.00"));
        final List<ChangeDetailDTO> changeDetailDTOS = Arrays.asList(changeDetailDTO);
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeChangeMapper.pageInfo(query2)).thenReturn(changeDetailDTOS);

        // Run the test
        final String result = tradeChangeServiceImplUnderTest.export(query);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testExport_TradeChangeMapperPageInfoReturnsNoItems() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeChangeMapper.count(...).
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeChangeMapper.count(query1)).thenReturn(0);

        // Configure TradeChangeMapper.pageInfo(...).
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeChangeMapper.pageInfo(query2)).thenReturn(Collections.emptyList());

        // Run the test
        final String result = tradeChangeServiceImplUnderTest.export(query);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }
}
