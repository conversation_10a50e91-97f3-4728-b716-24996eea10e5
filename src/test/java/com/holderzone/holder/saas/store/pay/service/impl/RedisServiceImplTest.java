package com.holderzone.holder.saas.store.pay.service.impl;

import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.dto.pay.AggRefundPollingRespDTO;
import com.holderzone.saas.store.dto.pay.SaasPollingDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RedisServiceImplTest {

    @Mock
    private ReactiveRedisTemplate<String, String> mockReactiveRedisTemplateString;
    @Mock
    private ReactiveRedisTemplate<String, AggPayPollingRespDTO> mockReactiveRedisTemplateAggPayPolling;
    @Mock
    private ReactiveRedisTemplate<String, AggRefundPollingRespDTO> mockReactiveRedisTemplateAggRefundPolling;

    private RedisServiceImpl redisServiceImplUnderTest;

    @Before
    public void setUp() {
        redisServiceImplUnderTest = new RedisServiceImpl(mockReactiveRedisTemplateString,
                mockReactiveRedisTemplateAggPayPolling, mockReactiveRedisTemplateAggRefundPolling);
    }

    @Test
    public void testPutPollingResp() {
        // Setup
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("code");
        pollingRespDTO.setMsg("msg");
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setSubject("subject");
        pollingRespDTO.setBody("body");

        when(mockReactiveRedisTemplateAggPayPolling.opsForValue()).thenReturn(null);

        // Run the test
        redisServiceImplUnderTest.putPollingResp("orderGuid", "payGuid", pollingRespDTO);

        // Verify the results
    }

    @Test
    public void testGetPollingResp() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setOrderGuid("orderGuid");
        saasPollingDTO.setPayGuid("payGuid");

        when(mockReactiveRedisTemplateAggPayPolling.opsForValue()).thenReturn(null);

        // Run the test
        final Mono<AggPayPollingRespDTO> result = redisServiceImplUnderTest.getPollingResp(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testPutCallBackResp() {
        // Setup
        when(mockReactiveRedisTemplateString.opsForValue()).thenReturn(null);

        // Run the test
        redisServiceImplUnderTest.putCallBackResp("orderGuid", "payGuid", "state");

        // Verify the results
    }

    @Test
    public void testGetCallBackResp() {
        // Setup
        when(mockReactiveRedisTemplateString.opsForValue()).thenReturn(null);

        // Run the test
        final Mono<String> result = redisServiceImplUnderTest.getCallBackResp("orderGuid", "payGuid");

        // Verify the results
    }

    @Test
    public void testGetRefundPollingResp() {
        // Setup
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setOrderGuid("orderGuid");
        saasPollingDTO.setPayGuid("payGuid");

        when(mockReactiveRedisTemplateAggRefundPolling.opsForValue()).thenReturn(null);

        // Run the test
        final Mono<AggRefundPollingRespDTO> result = redisServiceImplUnderTest.getRefundPollingResp(saasPollingDTO);

        // Verify the results
    }

    @Test
    public void testGetQrCodeDownloadUrl() {
        // Setup
        when(mockReactiveRedisTemplateString.opsForValue()).thenReturn(null);

        // Run the test
        final String result = redisServiceImplUnderTest.getQrCodeDownloadUrl("4825b3c0-011b-442a-a46e-6a5ee908a0bc");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testPutQrCodeDownloadUrl() {
        // Setup
        when(mockReactiveRedisTemplateString.opsForValue()).thenReturn(null);

        // Run the test
        redisServiceImplUnderTest.putQrCodeDownloadUrl("fb9e8a64-9d35-4db1-b2fa-b2140b818782", "downLoadUrl");

        // Verify the results
    }

    @Test
    public void testPutRefundPollingResp() {
        // Setup
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode("code");
        aggRefundPollingRespDTO.setMsg("msg");
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setOrderNo("orderNo");
        aggRefundPollingRespDTO.setMchntId("mchntId");

        when(mockReactiveRedisTemplateAggRefundPolling.opsForValue()).thenReturn(null);

        // Run the test
        redisServiceImplUnderTest.putRefundPollingResp("orderGuid", "payGuid", aggRefundPollingRespDTO);

        // Verify the results
    }
}
