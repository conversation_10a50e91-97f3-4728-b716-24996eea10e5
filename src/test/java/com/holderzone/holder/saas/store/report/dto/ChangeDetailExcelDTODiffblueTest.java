package com.holderzone.holder.saas.store.report.dto;

import static org.junit.Assert.assertEquals;

import org.junit.Test;

public class ChangeDetailExcelDTODiffblueTest {
    /**
     * Methods under test:
     *
     * <ul>
     *   <li>default or parameterless constructor of {@link ChangeDetailExcelDTO}
     *   <li>{@link ChangeDetailExcelDTO#setBrandName(String)}
     *   <li>{@link ChangeDetailExcelDTO#setCancelFlag(Integer)}
     *   <li>{@link ChangeDetailExcelDTO#setChangeItemCount(String)}
     *   <li>{@link ChangeDetailExcelDTO#setChangeItemName(String)}
     *   <li>{@link ChangeDetailExcelDTO#setChangeItemPrice(String)}
     *   <li>{@link ChangeDetailExcelDTO#setChangeItemTotalPrice(String)}
     *   <li>{@link ChangeDetailExcelDTO#setChangeNode(String)}
     *   <li>{@link ChangeDetailExcelDTO#setChangePrice(String)}
     *   <li>{@link ChangeDetailExcelDTO#setChangeTime(String)}
     *   <li>{@link ChangeDetailExcelDTO#setCreateStaffName(String)}
     *   <li>{@link ChangeDetailExcelDTO#setOrderNo(String)}
     *   <li>{@link ChangeDetailExcelDTO#setOriginalItemCount(String)}
     *   <li>{@link ChangeDetailExcelDTO#setOriginalItemName(String)}
     *   <li>{@link ChangeDetailExcelDTO#setOriginalItemPrice(String)}
     *   <li>{@link ChangeDetailExcelDTO#setOriginalItemTotalPrice(String)}
     *   <li>{@link ChangeDetailExcelDTO#setStoreName(String)}
     *   <li>{@link ChangeDetailExcelDTO#setSubgroupItemName(String)}
     *   <li>{@link ChangeDetailExcelDTO#toString()}
     *   <li>{@link ChangeDetailExcelDTO#getBrandName()}
     *   <li>{@link ChangeDetailExcelDTO#getCancelFlag()}
     *   <li>{@link ChangeDetailExcelDTO#getChangeItemCount()}
     *   <li>{@link ChangeDetailExcelDTO#getChangeItemName()}
     *   <li>{@link ChangeDetailExcelDTO#getChangeItemPrice()}
     *   <li>{@link ChangeDetailExcelDTO#getChangeItemTotalPrice()}
     *   <li>{@link ChangeDetailExcelDTO#getChangeNode()}
     *   <li>{@link ChangeDetailExcelDTO#getChangePrice()}
     *   <li>{@link ChangeDetailExcelDTO#getChangeTime()}
     *   <li>{@link ChangeDetailExcelDTO#getCreateStaffName()}
     *   <li>{@link ChangeDetailExcelDTO#getOrderNo()}
     *   <li>{@link ChangeDetailExcelDTO#getOriginalItemCount()}
     *   <li>{@link ChangeDetailExcelDTO#getOriginalItemName()}
     *   <li>{@link ChangeDetailExcelDTO#getOriginalItemPrice()}
     *   <li>{@link ChangeDetailExcelDTO#getOriginalItemTotalPrice()}
     *   <li>{@link ChangeDetailExcelDTO#getStoreName()}
     *   <li>{@link ChangeDetailExcelDTO#getSubgroupItemName()}
     * </ul>
     */
    @Test
    public void testConstructor() {
        ChangeDetailExcelDTO actualChangeDetailExcelDTO = new ChangeDetailExcelDTO();
        actualChangeDetailExcelDTO.setBrandName("Brand Name");
        actualChangeDetailExcelDTO.setCancelFlag(1);
        actualChangeDetailExcelDTO.setChangeItemCount("3");
        actualChangeDetailExcelDTO.setChangeItemName("Change Item Name");
        actualChangeDetailExcelDTO.setChangeItemPrice("Change Item Price");
        actualChangeDetailExcelDTO.setChangeItemTotalPrice("Change Item Total Price");
        actualChangeDetailExcelDTO.setChangeNode("Change Node");
        actualChangeDetailExcelDTO.setChangePrice("Change Price");
        actualChangeDetailExcelDTO.setChangeTime("Change Time");
        actualChangeDetailExcelDTO.setCreateStaffName("Create Staff Name");
        actualChangeDetailExcelDTO.setOrderNo("Order No");
        actualChangeDetailExcelDTO.setOriginalItemCount("3");
        actualChangeDetailExcelDTO.setOriginalItemName("Original Item Name");
        actualChangeDetailExcelDTO.setOriginalItemPrice("Original Item Price");
        actualChangeDetailExcelDTO.setOriginalItemTotalPrice("Original Item Total Price");
        actualChangeDetailExcelDTO.setStoreName("Store Name");
        actualChangeDetailExcelDTO.setSubgroupItemName("Subgroup Item Name");
        String actualToStringResult = actualChangeDetailExcelDTO.toString();
        String actualBrandName = actualChangeDetailExcelDTO.getBrandName();
        Integer actualCancelFlag = actualChangeDetailExcelDTO.getCancelFlag();
        String actualChangeItemCount = actualChangeDetailExcelDTO.getChangeItemCount();
        String actualChangeItemName = actualChangeDetailExcelDTO.getChangeItemName();
        String actualChangeItemPrice = actualChangeDetailExcelDTO.getChangeItemPrice();
        String actualChangeItemTotalPrice = actualChangeDetailExcelDTO.getChangeItemTotalPrice();
        String actualChangeNode = actualChangeDetailExcelDTO.getChangeNode();
        String actualChangePrice = actualChangeDetailExcelDTO.getChangePrice();
        String actualChangeTime = actualChangeDetailExcelDTO.getChangeTime();
        String actualCreateStaffName = actualChangeDetailExcelDTO.getCreateStaffName();
        String actualOrderNo = actualChangeDetailExcelDTO.getOrderNo();
        String actualOriginalItemCount = actualChangeDetailExcelDTO.getOriginalItemCount();
        String actualOriginalItemName = actualChangeDetailExcelDTO.getOriginalItemName();
        String actualOriginalItemPrice = actualChangeDetailExcelDTO.getOriginalItemPrice();
        String actualOriginalItemTotalPrice = actualChangeDetailExcelDTO.getOriginalItemTotalPrice();
        String actualStoreName = actualChangeDetailExcelDTO.getStoreName();
        assertEquals("3", actualChangeItemCount);
        assertEquals("3", actualOriginalItemCount);
        assertEquals("Brand Name", actualBrandName);
        assertEquals("Change Item Name", actualChangeItemName);
        assertEquals("Change Item Price", actualChangeItemPrice);
        assertEquals("Change Item Total Price", actualChangeItemTotalPrice);
        assertEquals("Change Node", actualChangeNode);
        assertEquals("Change Price", actualChangePrice);
        assertEquals("Change Time", actualChangeTime);
        assertEquals(
                "ChangeDetailExcelDTO(brandName=Brand Name, storeName=Store Name, cancelFlag=1, changeNode=Change Node,"
                        + " changeTime=Change Time, orderNo=Order No, subgroupItemName=Subgroup Item Name, originalItemName=Original"
                        + " Item Name, originalItemPrice=Original Item Price, originalItemCount=3, originalItemTotalPrice=Original"
                        + " Item Total Price, changeItemName=Change Item Name, changeItemCount=3, changeItemPrice=Change Item"
                        + " Price, changeItemTotalPrice=Change Item Total Price, changePrice=Change Price, createStaffName=Create"
                        + " Staff Name)",
                actualToStringResult);
        assertEquals("Create Staff Name", actualCreateStaffName);
        assertEquals("Order No", actualOrderNo);
        assertEquals("Original Item Name", actualOriginalItemName);
        assertEquals("Original Item Price", actualOriginalItemPrice);
        assertEquals("Original Item Total Price", actualOriginalItemTotalPrice);
        assertEquals("Store Name", actualStoreName);
        assertEquals("Subgroup Item Name", actualChangeDetailExcelDTO.getSubgroupItemName());
        assertEquals(1, actualCancelFlag.intValue());
    }
}
