package com.holderzone.holder.saas.store.report.service.impl;

import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.holder.saas.store.report.mapper.TradeDetailMapper;
import com.holderzone.holder.saas.store.report.service.rpc.organization.OrganizationService;
import com.holderzone.saas.store.dto.report.query.CloudPayConstituteQueryDTO;
import com.holderzone.saas.store.dto.report.resp.CloudPayConstituteRespDTO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;

import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@ContextConfiguration(classes = {TradeDetailServiceImpl.class})
@RunWith(SpringJUnit4ClassRunner.class)
public class TradeDetailServiceImplDiffblueTest {
    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @MockBean
    private OrganizationService organizationService;

    @MockBean
    private OssClient ossClient;

    @MockBean
    private TradeDetailMapper tradeDetailMapper;

    @Autowired
    private TradeDetailServiceImpl tradeDetailServiceImpl;

    /**
     * Method under test:
     * {@link TradeDetailServiceImpl#cloudPayConstitute(CloudPayConstituteQueryDTO)}
     */
    @Test
    public void testCloudPayConstitute() {
        CloudPayConstituteRespDTO cloudPayConstituteRespDTO = new CloudPayConstituteRespDTO();
        cloudPayConstituteRespDTO.setPayCode(1);
        cloudPayConstituteRespDTO.setPayMethod("Pay Method");
        cloudPayConstituteRespDTO.setTotalAmount(new BigDecimal("2.3"));
        when(tradeDetailMapper.cloudPayConstitute(Mockito.<CloudPayConstituteQueryDTO>any()))
                .thenReturn(cloudPayConstituteRespDTO);

        CloudPayConstituteQueryDTO query = new CloudPayConstituteQueryDTO();
        query.setEndTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        query.setEnterpriseGuid("1234");
        query.setStartTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        query.setStoreGuidList(new ArrayList<>());
        CloudPayConstituteRespDTO actualCloudPayConstituteResult = tradeDetailServiceImpl.cloudPayConstitute(query);
        verify(tradeDetailMapper).cloudPayConstitute(Mockito.<CloudPayConstituteQueryDTO>any());
        assertSame(cloudPayConstituteRespDTO, actualCloudPayConstituteResult);
    }

    /**
     * Method under test:
     * {@link TradeDetailServiceImpl#cloudPayConstitute(CloudPayConstituteQueryDTO)}
     */
    @Test
    public void testCloudPayConstitute2() {
        when(tradeDetailMapper.cloudPayConstitute(Mockito.<CloudPayConstituteQueryDTO>any()))
                .thenThrow(new BusinessException("An error occurred"));

        CloudPayConstituteQueryDTO query = new CloudPayConstituteQueryDTO();
        query.setEndTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        query.setEnterpriseGuid("1234");
        query.setStartTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        query.setStoreGuidList(new ArrayList<>());
        thrown.expect(BusinessException.class);
        tradeDetailServiceImpl.cloudPayConstitute(query);
        verify(tradeDetailMapper).cloudPayConstitute(Mockito.<CloudPayConstituteQueryDTO>any());
    }
}
