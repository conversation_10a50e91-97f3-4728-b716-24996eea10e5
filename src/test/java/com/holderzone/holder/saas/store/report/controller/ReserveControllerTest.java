package com.holderzone.holder.saas.store.report.controller;

import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.report.service.ReserveService;
import com.holderzone.saas.store.dto.report.query.ReserveReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.ReserveRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@ContextConfiguration(classes = {ReserveController.class})
@RunWith(SpringJUnit4ClassRunner.class)
public class ReserveControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ReserveService mockReserveService;

    @Test
    public void testQuery() throws Exception {
        // Setup
        // Configure ReserveService.page(...).
        final ReserveRespDTO reserveRespDTO = new ReserveRespDTO();
        reserveRespDTO.setGuid("0d8f886d-09c5-4331-b6dd-1d88d58c1d2f");
        reserveRespDTO.setStoreGuid("storeGuid");
        reserveRespDTO.setStoreName("storeName");
        reserveRespDTO.setDeviceType(0);
        reserveRespDTO.setNumber(0);
        final Page<ReserveRespDTO> reserveRespDTOPage = new Page<>(0L, 0L, Arrays.asList(reserveRespDTO));
        final ReserveReportQueryVO query = new ReserveReportQueryVO();
        query.setEnterpriseGuid("enterpriseGuid");
        query.setReserveStates(Arrays.asList(0));
        query.setIsDelay(false);
        query.setDeviceType(0);
        when(mockReserveService.page(query)).thenReturn(reserveRespDTOPage);

        // Run the test and verify the results
        mockMvc.perform(post("/reserve/list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testExport() throws Exception {
        // Setup
        // Configure ReserveService.export(...).
        final ReserveReportQueryVO query = new ReserveReportQueryVO();
        query.setEnterpriseGuid("enterpriseGuid");
        query.setReserveStates(Arrays.asList(0));
        query.setIsDelay(false);
        query.setDeviceType(0);
        when(mockReserveService.export(query)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/reserve/export")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
