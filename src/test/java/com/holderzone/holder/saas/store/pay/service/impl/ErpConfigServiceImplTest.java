package com.holderzone.holder.saas.store.pay.service.impl;

import com.holderzone.holder.saas.store.pay.service.rpc.EnterpriseRpcService;
import com.holderzone.holder.saas.store.pay.service.rpc.OrderRpcService;
import com.holderzone.holder.saas.store.pay.service.rpc.PaymentTypeRpcService;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.trade.PaymentInfoDTO;
import com.holderzone.saas.store.dto.trade.PaymentTypeDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ErpConfigServiceImplTest {

    @Mock
    private EnterpriseRpcService mockEnterpriseRpcService;
    @Mock
    private PaymentTypeRpcService mockPaymentTypeRpcService;
    @Mock
    private OrderRpcService mockOrderRpcService;

    private ErpConfigServiceImpl erpConfigServiceImplUnderTest;

    @Before
    public void setUp() {
        erpConfigServiceImplUnderTest = new ErpConfigServiceImpl(mockEnterpriseRpcService, mockPaymentTypeRpcService,
                mockOrderRpcService);
    }

    @Test
    public void testGetPaymentInfo1() {
        // Setup
        final PaymentInfoDTO expectedResult = new PaymentInfoDTO();
        expectedResult.setPaymentInfoGuid("paymentInfoGuid");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setIsDefaultAccount(0);
        expectedResult.setDiversionRules("diversionRules");

        // Configure EnterpriseRpcService.getJHInfo(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        when(mockEnterpriseRpcService.getJHInfo("enterpriseGuid", "storeGuid")).thenReturn(paymentInfoDTO);

        // Run the test
        final PaymentInfoDTO result = erpConfigServiceImplUnderTest.getPaymentInfo("enterpriseGuid", "storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetPaymentInfo2() {
        // Setup
        final PaymentInfoDTO expectedResult = new PaymentInfoDTO();
        expectedResult.setPaymentInfoGuid("paymentInfoGuid");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setIsDefaultAccount(0);
        expectedResult.setDiversionRules("diversionRules");

        // Configure PaymentTypeRpcService.getJhPaymentTypeInfo(...).
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setPaymentShunt(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        when(mockPaymentTypeRpcService.getJhPaymentTypeInfo("storeGuid")).thenReturn(paymentTypeDTO);

        // Configure OrderRpcService.findByOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setGuid("a10649ae-f4a1-4f11-a2be-e17afc1bcd96");
        orderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDTO.setOrderNo("orderNo");
        orderDTO.setRecoveryType(0);
        orderDTO.setPaymentAppId("paymentAppId");
        when(mockOrderRpcService.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        // Configure EnterpriseRpcService.listPayAppId(...).
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        final List<PaymentInfoDTO> paymentInfoDTOS = Arrays.asList(paymentInfoDTO1);
        when(mockEnterpriseRpcService.listPayAppId("paymentAppId", "enterpriseGuid", "storeGuid"))
                .thenReturn(paymentInfoDTOS);

        // Run the test
        final PaymentInfoDTO result = erpConfigServiceImplUnderTest.getPaymentInfo("enterpriseGuid", "storeGuid",
                "orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetPaymentInfo2_EnterpriseRpcServiceReturnsNoItems() {
        // Setup
        // Configure PaymentTypeRpcService.getJhPaymentTypeInfo(...).
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setPaymentShunt(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        when(mockPaymentTypeRpcService.getJhPaymentTypeInfo("storeGuid")).thenReturn(paymentTypeDTO);

        // Configure OrderRpcService.findByOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setGuid("a10649ae-f4a1-4f11-a2be-e17afc1bcd96");
        orderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDTO.setOrderNo("orderNo");
        orderDTO.setRecoveryType(0);
        orderDTO.setPaymentAppId("paymentAppId");
        when(mockOrderRpcService.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        when(mockEnterpriseRpcService.listPayAppId("paymentAppId", "enterpriseGuid", "storeGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final PaymentInfoDTO result = erpConfigServiceImplUnderTest.getPaymentInfo("enterpriseGuid", "storeGuid",
                "orderGuid");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testGetShuntPaymentInfo() {
        // Setup
        final PaymentInfoDTO expectedResult = new PaymentInfoDTO();
        expectedResult.setPaymentInfoGuid("paymentInfoGuid");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setIsDefaultAccount(0);
        expectedResult.setDiversionRules("diversionRules");

        // Configure PaymentTypeRpcService.getJhPaymentTypeInfo(...).
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setPaymentShunt(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        when(mockPaymentTypeRpcService.getJhPaymentTypeInfo("storeGuid")).thenReturn(paymentTypeDTO);

        // Configure OrderRpcService.findByOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setGuid("a10649ae-f4a1-4f11-a2be-e17afc1bcd96");
        orderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDTO.setOrderNo("orderNo");
        orderDTO.setRecoveryType(0);
        orderDTO.setPaymentAppId("paymentAppId");
        when(mockOrderRpcService.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        // Configure EnterpriseRpcService.listPayAppId(...).
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        final List<PaymentInfoDTO> paymentInfoDTOS = Arrays.asList(paymentInfoDTO1);
        when(mockEnterpriseRpcService.listPayAppId("paymentAppId", "enterpriseGuid", "storeGuid"))
                .thenReturn(paymentInfoDTOS);

        // Run the test
        final PaymentInfoDTO result = erpConfigServiceImplUnderTest.getShuntPaymentInfo("enterpriseGuid", "storeGuid",
                "orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetShuntPaymentInfo_EnterpriseRpcServiceReturnsNoItems() {
        // Setup
        final PaymentInfoDTO expectedResult = new PaymentInfoDTO();
        expectedResult.setPaymentInfoGuid("paymentInfoGuid");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setIsDefaultAccount(0);
        expectedResult.setDiversionRules("diversionRules");

        // Configure PaymentTypeRpcService.getJhPaymentTypeInfo(...).
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setPaymentShunt(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        when(mockPaymentTypeRpcService.getJhPaymentTypeInfo("storeGuid")).thenReturn(paymentTypeDTO);

        // Configure OrderRpcService.findByOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setGuid("a10649ae-f4a1-4f11-a2be-e17afc1bcd96");
        orderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDTO.setOrderNo("orderNo");
        orderDTO.setRecoveryType(0);
        orderDTO.setPaymentAppId("paymentAppId");
        when(mockOrderRpcService.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        when(mockEnterpriseRpcService.listPayAppId("paymentAppId", "enterpriseGuid", "storeGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final PaymentInfoDTO result = erpConfigServiceImplUnderTest.getShuntPaymentInfo("enterpriseGuid", "storeGuid",
                "orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetPaymentInfoAsync() {
        // Setup
        // Configure EnterpriseRpcService.getJHInfo(...).
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO.setStoreGuid("storeGuid");
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        when(mockEnterpriseRpcService.getJHInfo("enterpriseGuid", "storeGuid")).thenReturn(paymentInfoDTO);

        // Run the test
        final Mono<PaymentInfoDTO> result = erpConfigServiceImplUnderTest.getPaymentInfoAsync("enterpriseGuid",
                "storeGuid");

        // Verify the results
    }

    @Test
    public void testGetShuntPaymentInfoAsync() {
        // Setup
        // Configure PaymentTypeRpcService.getJhPaymentTypeInfo(...).
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setPaymentShunt(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        when(mockPaymentTypeRpcService.getJhPaymentTypeInfo("storeGuid")).thenReturn(paymentTypeDTO);

        // Configure OrderRpcService.findByOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setGuid("a10649ae-f4a1-4f11-a2be-e17afc1bcd96");
        orderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDTO.setOrderNo("orderNo");
        orderDTO.setRecoveryType(0);
        orderDTO.setPaymentAppId("paymentAppId");
        when(mockOrderRpcService.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        // Configure EnterpriseRpcService.listPayAppId(...).
        final PaymentInfoDTO paymentInfoDTO1 = new PaymentInfoDTO();
        paymentInfoDTO1.setPaymentInfoGuid("paymentInfoGuid");
        paymentInfoDTO1.setEnterpriseGuid("enterpriseGuid");
        paymentInfoDTO1.setStoreGuid("storeGuid");
        paymentInfoDTO1.setIsDefaultAccount(0);
        paymentInfoDTO1.setDiversionRules("diversionRules");
        final List<PaymentInfoDTO> paymentInfoDTOS = Arrays.asList(paymentInfoDTO1);
        when(mockEnterpriseRpcService.listPayAppId("paymentAppId", "enterpriseGuid", "storeGuid"))
                .thenReturn(paymentInfoDTOS);

        // Run the test
        final Mono<PaymentInfoDTO> result = erpConfigServiceImplUnderTest.getShuntPaymentInfoAsync("enterpriseGuid",
                "storeGuid", "orderGuid");

        // Verify the results
    }

    @Test
    public void testGetShuntPaymentInfoAsync_EnterpriseRpcServiceReturnsNoItems() {
        // Setup
        // Configure PaymentTypeRpcService.getJhPaymentTypeInfo(...).
        final PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
        paymentTypeDTO.setStoreGuid("storeGuid");
        paymentTypeDTO.setPaymentShunt(0);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setIsDefaultAccount(0);
        paymentInfoDTO.setDiversionRules("diversionRules");
        paymentTypeDTO.setJhPayInfoList(Arrays.asList(paymentInfoDTO));
        when(mockPaymentTypeRpcService.getJhPaymentTypeInfo("storeGuid")).thenReturn(paymentTypeDTO);

        // Configure OrderRpcService.findByOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setGuid("a10649ae-f4a1-4f11-a2be-e17afc1bcd96");
        orderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDTO.setOrderNo("orderNo");
        orderDTO.setRecoveryType(0);
        orderDTO.setPaymentAppId("paymentAppId");
        when(mockOrderRpcService.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        when(mockEnterpriseRpcService.listPayAppId("paymentAppId", "enterpriseGuid", "storeGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Mono<PaymentInfoDTO> result = erpConfigServiceImplUnderTest.getShuntPaymentInfoAsync("enterpriseGuid",
                "storeGuid", "orderGuid");

        // Verify the results
    }
}
