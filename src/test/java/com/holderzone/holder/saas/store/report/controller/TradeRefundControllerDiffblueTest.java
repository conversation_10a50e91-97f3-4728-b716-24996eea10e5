package com.holderzone.holder.saas.store.report.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.holderzone.holder.saas.store.report.service.TradeRefundService;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;

import java.time.LocalDate;
import java.util.ArrayList;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ContextConfiguration(classes = {TradeRefundController.class})
@RunWith(SpringJUnit4ClassRunner.class)
public class TradeRefundControllerDiffblueTest {
    @Autowired
    private TradeRefundController tradeRefundController;

    @MockBean
    private TradeRefundService tradeRefundService;

    /**
     * Method under test: {@link TradeRefundController#export(ReportQueryVO)}
     */
    @Test
    public void testExport() throws Exception {
        ReportQueryVO reportQueryVO = new ReportQueryVO();
        reportQueryVO.setBrandGuid("1234");
        reportQueryVO.setCateringType(1);
        reportQueryVO.setCurrentPage(1);
        reportQueryVO.setEndTime(LocalDate.of(1970, 1, 1));
        reportQueryVO.setEnterpriseGuid("1234");
        reportQueryVO.setGoodsCategories("Goods Categories");
        reportQueryVO.setGoodsType(1);
        reportQueryVO.setItemName("Item Name");
        reportQueryVO.setOperatorNode(1);
        reportQueryVO.setPageSize(3);
        reportQueryVO.setStartTime(LocalDate.of(1970, 1, 1));
        reportQueryVO.setStoreGuids(new ArrayList<>());
        String content = (new ObjectMapper()).writeValueAsString(reportQueryVO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/trade/refund/export")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);
        ResultActions actualPerformResult = MockMvcBuilders.standaloneSetup(tradeRefundController)
                .build()
                .perform(requestBuilder);
        actualPerformResult.andExpect(MockMvcResultMatchers.status().is(400));
    }

    /**
     * Method under test: {@link TradeRefundController#list(ReportQueryVO)}
     */
    @Test
    public void testList() throws Exception {
        ReportQueryVO reportQueryVO = new ReportQueryVO();
        reportQueryVO.setBrandGuid("1234");
        reportQueryVO.setCateringType(1);
        reportQueryVO.setCurrentPage(1);
        reportQueryVO.setEndTime(LocalDate.of(1970, 1, 1));
        reportQueryVO.setEnterpriseGuid("1234");
        reportQueryVO.setGoodsCategories("Goods Categories");
        reportQueryVO.setGoodsType(1);
        reportQueryVO.setItemName("Item Name");
        reportQueryVO.setOperatorNode(1);
        reportQueryVO.setPageSize(3);
        reportQueryVO.setStartTime(LocalDate.of(1970, 1, 1));
        reportQueryVO.setStoreGuids(new ArrayList<>());
        String content = (new ObjectMapper()).writeValueAsString(reportQueryVO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/trade/refund/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);
        ResultActions actualPerformResult = MockMvcBuilders.standaloneSetup(tradeRefundController)
                .build()
                .perform(requestBuilder);
        actualPerformResult.andExpect(MockMvcResultMatchers.status().is(400));
    }
}
