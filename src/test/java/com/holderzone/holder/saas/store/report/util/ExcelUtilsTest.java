package com.holderzone.holder.saas.store.report.util;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.holderzone.framework.oss.sdk.facde.OssClient;
import org.apache.commons.io.input.BrokenInputStream;
import org.apache.commons.io.input.NullInputStream;
import org.apache.poi.ss.usermodel.Workbook;
import org.junit.Test;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

public class ExcelUtilsTest {

    @Test
    public void testExportExcel1() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Run the test
        ExcelUtils.exportExcel(Arrays.asList("value"), "title", "sheetName", String.class, "fileName", false, response);

        // Verify the results
    }

    @Test
    public void testExportExcel1_ThrowsIOException() {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Run the test
        assertThatThrownBy(
                () -> ExcelUtils.exportExcel(Arrays.asList("value"), "title", "sheetName", String.class, "fileName",
                        false, response)).isInstanceOf(IOException.class);
    }

    @Test
    public void testExportExcel2() throws Exception {
        // Setup
        final OssClient ossClient = null;

        // Run the test
        final String result = ExcelUtils.exportExcel(Arrays.asList("value"), "title", "sheetName", String.class,
                "fileName", ossClient);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testExportExcel2_ThrowsIOException() {
        // Setup
        final OssClient ossClient = null;

        // Run the test
        assertThatThrownBy(
                () -> ExcelUtils.exportExcel(Arrays.asList("value"), "title", "sheetName", String.class, "fileName",
                        ossClient)).isInstanceOf(IOException.class);
    }

    @Test
    public void testOut() {
        // Setup
        final Workbook workbook = null;
        final OssClient ossClient = null;

        // Run the test
        final String result = ExcelUtils.out(workbook, "fileName", ossClient);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testExportExcel3() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Run the test
        ExcelUtils.exportExcel(Arrays.asList("value"), "title", "sheetName", String.class, "fileName", response);

        // Verify the results
    }

    @Test
    public void testExportExcel3_ThrowsIOException() {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Run the test
        assertThatThrownBy(
                () -> ExcelUtils.exportExcel(Arrays.asList("value"), "title", "sheetName", String.class, "fileName",
                        response)).isInstanceOf(IOException.class);
    }

    @Test
    public void testExportExcel4() throws Exception {
        // Setup
        final ExportParams exportParams = new ExportParams("title", "sheetName", ExcelType.HSSF);
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Run the test
        ExcelUtils.exportExcel(Arrays.asList("value"), String.class, "fileName", exportParams, response);

        // Verify the results
    }

    @Test
    public void testExportExcel4_ThrowsIOException() {
        // Setup
        final ExportParams exportParams = new ExportParams("title", "sheetName", ExcelType.HSSF);
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Run the test
        assertThatThrownBy(() -> ExcelUtils.exportExcel(Arrays.asList("value"), String.class, "fileName", exportParams,
                response)).isInstanceOf(IOException.class);
    }

    @Test
    public void testExportExcel5() throws Exception {
        // Setup
        final List<Map<String, Object>> list = Arrays.asList(new HashMap<>());
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Run the test
        ExcelUtils.exportExcel(list, "fileName", response);

        // Verify the results
    }

    @Test
    public void testExportExcel5_ThrowsIOException() {
        // Setup
        final List<Map<String, Object>> list = Arrays.asList(new HashMap<>());
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Run the test
        assertThatThrownBy(() -> ExcelUtils.exportExcel(list, "fileName", response)).isInstanceOf(IOException.class);
    }

    @Test
    public void testImportExcel1() throws Exception {
        assertThat(ExcelUtils.importExcel("filePath", 0, 0, String.class)).isEqualTo(Arrays.asList("value"));
        assertThat(ExcelUtils.importExcel("filePath", 0, 0, String.class)).isEqualTo(Collections.emptyList());
        assertThatThrownBy(() -> ExcelUtils.importExcel("filePath", 0, 0, String.class))
                .isInstanceOf(IOException.class);
    }

    @Test
    public void testImportExcel2() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());

        // Run the test
        final List<String> result = ExcelUtils.importExcel(file, String.class);

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
    }

    @Test
    public void testImportExcel2_ThrowsIOException() {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());

        // Run the test
        assertThatThrownBy(() -> ExcelUtils.importExcel(file, String.class)).isInstanceOf(IOException.class);
    }

    @Test
    public void testImportExcel3() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());

        // Run the test
        final List<String> result = ExcelUtils.importExcel(file, 0, 0, String.class);

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
    }

    @Test
    public void testImportExcel3_ThrowsIOException() {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());

        // Run the test
        assertThatThrownBy(() -> ExcelUtils.importExcel(file, 0, 0, String.class)).isInstanceOf(IOException.class);
    }

    @Test
    public void testImportExcel4() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());

        // Run the test
        final List<String> result = ExcelUtils.importExcel(file, 0, 0, false, String.class);

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
    }

    @Test
    public void testImportExcel4_ThrowsIOException() {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());

        // Run the test
        assertThatThrownBy(() -> ExcelUtils.importExcel(file, 0, 0, false, String.class))
                .isInstanceOf(IOException.class);
    }

    @Test
    public void testImportExcel5() throws Exception {
        // Setup
        final InputStream inputStream = new ByteArrayInputStream("content".getBytes());

        // Run the test
        final List<String> result = ExcelUtils.importExcel(inputStream, 0, 0, false, String.class);

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
    }

    @Test
    public void testImportExcel5_EmptyInputStream() throws Exception {
        // Setup
        final InputStream inputStream = new NullInputStream(0L);

        // Run the test
        final List<String> result = ExcelUtils.importExcel(inputStream, 0, 0, false, String.class);

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
    }

    @Test
    public void testImportExcel5_BrokenInputStream() {
        // Setup
        final InputStream inputStream = new BrokenInputStream();

        // Run the test
        assertThatThrownBy(() -> ExcelUtils.importExcel(inputStream, 0, 0, false, String.class))
                .isInstanceOf(IOException.class);
    }
}
