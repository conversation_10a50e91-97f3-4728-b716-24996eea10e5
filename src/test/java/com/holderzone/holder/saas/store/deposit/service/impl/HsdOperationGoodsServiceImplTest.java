package com.holderzone.holder.saas.store.deposit.service.impl;

import com.holderzone.holder.saas.store.deposit.entity.bo.OperationGoodsDO;
import com.holderzone.holder.saas.store.deposit.mapstruct.OperationMapstruct;
import com.holderzone.saas.store.dto.deposit.resp.GoodsSimpleRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsdOperationGoodsServiceImplTest {

    @Mock
    private OperationMapstruct mockOperationMapstruct;

    private HsdOperationGoodsServiceImpl hsdOperationGoodsServiceImplUnderTest;

    @Before
    public void setUp() {
        hsdOperationGoodsServiceImplUnderTest = new HsdOperationGoodsServiceImpl(mockOperationMapstruct);
    }

    @Test
    public void testQueryGoodsOfOperation() {
        // Setup
        final GoodsSimpleRespDTO goodsSimpleRespDTO = new GoodsSimpleRespDTO();
        goodsSimpleRespDTO.setGoodsName("goodsName");
        goodsSimpleRespDTO.setSkuName("skuName");
        goodsSimpleRespDTO.setResidueNum(0);
        goodsSimpleRespDTO.setOperatorNum(0);
        final List<GoodsSimpleRespDTO> expectedResult = Arrays.asList(goodsSimpleRespDTO);

        // Configure OperationMapstruct.fromGoodsOfOperation(...).
        final GoodsSimpleRespDTO goodsSimpleRespDTO1 = new GoodsSimpleRespDTO();
        goodsSimpleRespDTO1.setGoodsName("goodsName");
        goodsSimpleRespDTO1.setSkuName("skuName");
        goodsSimpleRespDTO1.setResidueNum(0);
        goodsSimpleRespDTO1.setOperatorNum(0);
        final List<GoodsSimpleRespDTO> goodsSimpleRespDTOS = Arrays.asList(goodsSimpleRespDTO1);
        final OperationGoodsDO operationGoodsDO = new OperationGoodsDO();
        operationGoodsDO.setId(0L);
        operationGoodsDO.setGuid("2798bef6-901b-4cf2-b0d5-fd912b1a0161");
        operationGoodsDO.setDepositGuid("depositGuid");
        operationGoodsDO.setGoodsName("goodsName");
        operationGoodsDO.setSkuName("skuName");
        final List<OperationGoodsDO> list = Arrays.asList(operationGoodsDO);
        when(mockOperationMapstruct.fromGoodsOfOperation(list)).thenReturn(goodsSimpleRespDTOS);

        // Run the test
        final List<GoodsSimpleRespDTO> result = hsdOperationGoodsServiceImplUnderTest.queryGoodsOfOperation(
                "operationGuid", "depositGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryGoodsOfOperation_OperationMapstructReturnsNoItems() {
        // Setup
        // Configure OperationMapstruct.fromGoodsOfOperation(...).
        final OperationGoodsDO operationGoodsDO = new OperationGoodsDO();
        operationGoodsDO.setId(0L);
        operationGoodsDO.setGuid("2798bef6-901b-4cf2-b0d5-fd912b1a0161");
        operationGoodsDO.setDepositGuid("depositGuid");
        operationGoodsDO.setGoodsName("goodsName");
        operationGoodsDO.setSkuName("skuName");
        final List<OperationGoodsDO> list = Arrays.asList(operationGoodsDO);
        when(mockOperationMapstruct.fromGoodsOfOperation(list)).thenReturn(Collections.emptyList());

        // Run the test
        final List<GoodsSimpleRespDTO> result = hsdOperationGoodsServiceImplUnderTest.queryGoodsOfOperation(
                "operationGuid", "depositGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
