package com.holderzone.holder.saas.store.deposit.interceptor;

import static org.junit.Assert.assertTrue;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.junit.Test;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.web.servlet.ModelAndView;

public class FakeInterceptorDiffblueTest {
  /**
   * Method under test:
   * {@link FakeInterceptor#preHandle(HttpServletRequest, HttpServletResponse, Object)}
   */
  @Test
  public void testPreHandle() throws Exception {
    FakeInterceptor fakeInterceptor = new FakeInterceptor();
    MockHttpServletRequest request = new MockHttpServletRequest();
    assertTrue(fakeInterceptor.preHandle(request, new MockHttpServletResponse(), "Handler"));
  }

  /**
   * Methods under test:
   *
   * <ul>
   *   <li>
   * {@link FakeInterceptor#afterCompletion(HttpServletRequest, HttpServletResponse, Object, Exception)}
   *   <li>
   * {@link FakeInterceptor#postHandle(HttpServletRequest, HttpServletResponse, Object, ModelAndView)}
   * </ul>
   */
  @Test
  public void testAfterCompletion() {
    // TODO: Complete this test.
    //   Reason: R002 Missing observers.
    //   Diffblue Cover was unable to create an assertion.
    //   There are no fields that could be asserted on.

    FakeInterceptor fakeInterceptor = new FakeInterceptor();
    MockHttpServletRequest request = new MockHttpServletRequest();
    MockHttpServletResponse response = new MockHttpServletResponse();
    fakeInterceptor.afterCompletion(request, response, "Handler", new Exception("foo"));
    MockHttpServletRequest request2 = new MockHttpServletRequest();
    MockHttpServletResponse response2 = new MockHttpServletResponse();
    fakeInterceptor.postHandle(request2, response2, "Handler", new ModelAndView("View Name"));
  }
}
