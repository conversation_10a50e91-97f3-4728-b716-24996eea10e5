package com.holderzone.holder.saas.store.deposit.service.impl;

import com.holderzone.framework.base.dto.message.MailMessageDTO;
import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.framework.base.dto.message.MessageType;
import com.holderzone.holder.saas.store.deposit.service.rpc.EntServiceClient;
import com.holderzone.holder.saas.store.deposit.service.rpc.MsgClientService;
import com.holderzone.resource.common.dto.enterprise.DeductShortMessageDTO;
import com.holderzone.resource.common.dto.enterprise.MessageConfigDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SendMessageServiceImplTest {

    @Mock
    private EntServiceClient mockEs;
    @Mock
    private MsgClientService mockMs;

    @InjectMocks
    private SendMessageServiceImpl sendMessageServiceImplUnderTest;

    @Test
    public void testSendMessage() {
        // Setup
        final MessageDTO m1 = new MessageDTO();
        m1.setMessageType(MessageType.SHORT_MESSAGE);
        final MailMessageDTO mailMessage = new MailMessageDTO();
        mailMessage.setSubject("subject");
        mailMessage.setContext("context");
        mailMessage.setReceivers(Arrays.asList("value"));
        m1.setMailMessage(mailMessage);

        // Configure EntServiceClient.getMessageInfo(...).
        final MessageConfigDTO messageConfigDTO = new MessageConfigDTO();
        messageConfigDTO.setId(0);
        messageConfigDTO.setAppreciateGuid("appreciateGuid");
        messageConfigDTO.setAfterConsume(0);
        messageConfigDTO.setAfterCharge(0);
        messageConfigDTO.setResidueCount(0);
        when(mockEs.getMessageInfo("entGuid")).thenReturn(messageConfigDTO);

        when(mockEs.deductShortMessage(Arrays.asList(DeductShortMessageDTO.builder().build()))).thenReturn(false);

        // Run the test
        sendMessageServiceImplUnderTest.sendMessage(m1, "entGuid");

        // Verify the results
    }

    @Test
    public void testSendMessage_EntServiceClientGetMessageInfoReturnsNull() {
        // Setup
        final MessageDTO m1 = new MessageDTO();
        m1.setMessageType(MessageType.SHORT_MESSAGE);
        final MailMessageDTO mailMessage = new MailMessageDTO();
        mailMessage.setSubject("subject");
        mailMessage.setContext("context");
        mailMessage.setReceivers(Arrays.asList("value"));
        m1.setMailMessage(mailMessage);

        when(mockEs.getMessageInfo("entGuid")).thenReturn(null);

        // Run the test
        sendMessageServiceImplUnderTest.sendMessage(m1, "entGuid");

        // Verify the results
    }

    @Test
    public void testSendMessage_EntServiceClientDeductShortMessageReturnsTrue() {
        // Setup
        final MessageDTO m1 = new MessageDTO();
        m1.setMessageType(MessageType.SHORT_MESSAGE);
        final MailMessageDTO mailMessage = new MailMessageDTO();
        mailMessage.setSubject("subject");
        mailMessage.setContext("context");
        mailMessage.setReceivers(Arrays.asList("value"));
        m1.setMailMessage(mailMessage);

        // Configure EntServiceClient.getMessageInfo(...).
        final MessageConfigDTO messageConfigDTO = new MessageConfigDTO();
        messageConfigDTO.setId(0);
        messageConfigDTO.setAppreciateGuid("appreciateGuid");
        messageConfigDTO.setAfterConsume(0);
        messageConfigDTO.setAfterCharge(0);
        messageConfigDTO.setResidueCount(0);
        when(mockEs.getMessageInfo("entGuid")).thenReturn(messageConfigDTO);

        when(mockEs.deductShortMessage(Arrays.asList(DeductShortMessageDTO.builder().build()))).thenReturn(true);

        // Run the test
        sendMessageServiceImplUnderTest.sendMessage(m1, "entGuid");

        // Verify the results
        verify(mockMs).sendMessage(any(MessageDTO.class));
    }
}
