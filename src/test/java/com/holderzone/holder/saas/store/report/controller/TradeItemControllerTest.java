package com.holderzone.holder.saas.store.report.controller;

import com.alibaba.fastjson.JSON;
import com.holderzone.holder.saas.store.report.HolderSaasReportApplication;
import com.holderzone.holder.saas.store.report.util.JsonFileUtil;
import com.holderzone.saas.store.dto.exception.TestException;
import com.holderzone.saas.store.dto.trade.req.RecordThirdActivityInfoReqDTO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.UnsupportedEncodingException;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @date 2023/9/25
 * @description
 */
@Slf4j
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@RunWith(SpringRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@SpringBootTest(classes = HolderSaasReportApplication.class)
public class TradeItemControllerTest {

    private static final String USERINFO = "{\"operSubjectGuid\": \"2010121440477930009\",\"enterpriseGuid\":" +
            " \"2009281531195930006\",\"enterpriseName\": \"赵氏企业\",\"enterpriseNo\": \"********\",\"storeGuid\":" +
            " \"2106221850429620006\",\"storeName\": \"交子大道测试门店\",\"storeNo\": \"5796807\",\"userGuid\": " +
            "\"6787561298847596545\",\"account\": \"196504\",\"tel\": \"***********\",\"name\": \"靓亮仔\"}\n";

    private static final String TRADE_ITEM = "/trade/item";

    private static final String RESPONSE = "response:";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    @ApiOperation(value = "商品分类统计")
    public void queryItemTypeStatistics() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqItemTypeStatisticsDTO = JSON.parseObject(JsonFileUtil.read("tradeItem/queryItemTypeStatistics.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonItemTypeStatisticsString = JSON.toJSONString(reqItemTypeStatisticsDTO);
        MvcResult mvcItemTypeStatisticsResult = null;
        try {
            mvcItemTypeStatisticsResult = mockMvc.perform(post(TRADE_ITEM + "/type/statistics")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonItemTypeStatisticsString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String contentAsString = mvcItemTypeStatisticsResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    @ApiOperation(value = "商品分类统计导出")
    public void exportItemTypeStatistics() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqItemTypeDTO = JSON.parseObject(JsonFileUtil.read("tradeItem/exportItemTypeStatistics.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonItemTypeString = JSON.toJSONString(reqItemTypeDTO);
        MvcResult mvcItemTypeResult = null;
        try {
            mvcItemTypeResult = mockMvc.perform(post(TRADE_ITEM + "/type/statistics/export")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonItemTypeString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String contentAsString = mvcItemTypeResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    @ApiOperation(value = "套餐销量统计")
    public void queryGroupItemSaleStatistics() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqGroupItemSaleStatisticsDTO = JSON.parseObject(JsonFileUtil.read("tradeItem/queryGroupItemSaleStatistics.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonGroupItemSaleStatisticsString = JSON.toJSONString(reqGroupItemSaleStatisticsDTO);
        MvcResult mvcGroupItemSaleStatisticsResult = null;
        try {
            mvcGroupItemSaleStatisticsResult = mockMvc.perform(post(TRADE_ITEM + "/group/sale/statistics")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonGroupItemSaleStatisticsString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String contentAsString = mvcGroupItemSaleStatisticsResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    @ApiOperation(value = "套餐销量统计导出")
    public void exportGroupItemSaleStatistics() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqGroupItemSaleDTO = JSON.parseObject(JsonFileUtil.read("tradeItem/exportGroupItemSaleStatistics.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonGroupItemSaleString = JSON.toJSONString(reqGroupItemSaleDTO);
        MvcResult mvcGroupItemSaleResult = null;
        try {
            mvcGroupItemSaleResult = mockMvc.perform(post(TRADE_ITEM + "/group/sale/statistics/export")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonGroupItemSaleString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String contentAsString = mvcGroupItemSaleResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    @ApiOperation(value = "门店商品销量分类查询")
    public void pageStoreSaleStatisticsType() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqStoreSaleStatisticsTypeDTO = JSON.parseObject(JsonFileUtil.read("tradeItem/pageStoreSaleStatisticsType.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonStoreSaleStatisticsTypeString = JSON.toJSONString(reqStoreSaleStatisticsTypeDTO);
        MvcResult mvcStoreSaleStatisticsTypeResult = null;
        try {
            mvcStoreSaleStatisticsTypeResult = mockMvc.perform(post(TRADE_ITEM + "/store/sale/statistics/type")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonStoreSaleStatisticsTypeString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String contentAsString = mvcStoreSaleStatisticsTypeResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    @ApiOperation(value = "门店商品销量")
    public void pageStoreSaleStatistics() throws UnsupportedEncodingException {
        RecordThirdActivityInfoReqDTO reqStoreSaleStatisticsDTO = JSON.parseObject(JsonFileUtil.read("tradeItem/pageStoreSaleStatistics.json"),
                RecordThirdActivityInfoReqDTO.class);
        String jsonStoreSaleStatisticsString = JSON.toJSONString(reqStoreSaleStatisticsDTO);
        MvcResult mvcStoreSaleStatisticsResult = null;
        try {
            mvcStoreSaleStatisticsResult = mockMvc.perform(post(TRADE_ITEM + "/store/sale/statistics")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(jsonStoreSaleStatisticsString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new TestException(e.getMessage());
        }
        String contentAsString = mvcStoreSaleStatisticsResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }
}