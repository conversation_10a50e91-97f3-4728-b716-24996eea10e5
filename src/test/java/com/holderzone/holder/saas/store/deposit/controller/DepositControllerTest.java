package com.holderzone.holder.saas.store.deposit.controller;

import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.deposit.HolderSaasStoreDepositApplication;
import com.holderzone.holder.saas.store.deposit.service.IHsdDepositService;
import com.holderzone.holder.saas.store.deposit.service.IHsdGoodsService;
import com.holderzone.saas.store.dto.deposit.req.*;
import com.holderzone.saas.store.dto.deposit.resp.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(DepositController.class)
public class DepositControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IHsdDepositService mockIHsdDepositService;
    @MockBean
    private IHsdGoodsService mockIHsdGoodsService;

    @Test
    public void testCreateDepositItem() throws Exception {
        // Setup
        // Configure IHsdDepositService.createDepositRecord(...).
        final DepositCreateReqDTO depositCreateReqDTO = new DepositCreateReqDTO();
        depositCreateReqDTO.setStoreGuid("storeGuid");
        depositCreateReqDTO.setMemberGuid("memberGuid");
        depositCreateReqDTO.setHeadPortrait("headPortrait");
        final GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
        goodsRespDTO.setGuid("c3f46955-8980-4c7b-bf62-d7ac8edd39fb");
        depositCreateReqDTO.setGoods(Arrays.asList(goodsRespDTO));
        when(mockIHsdDepositService.createDepositRecord(depositCreateReqDTO)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/deposit/create_deposit_item")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testCreateDepositItem_IHsdDepositServiceReturnsTrue() throws Exception {
        // Setup
        // Configure IHsdDepositService.createDepositRecord(...).
        final DepositCreateReqDTO depositCreateReqDTO = new DepositCreateReqDTO();
        depositCreateReqDTO.setStoreGuid("storeGuid");
        depositCreateReqDTO.setMemberGuid("memberGuid");
        depositCreateReqDTO.setHeadPortrait("headPortrait");
        final GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
        goodsRespDTO.setGuid("c3f46955-8980-4c7b-bf62-d7ac8edd39fb");
        depositCreateReqDTO.setGoods(Arrays.asList(goodsRespDTO));
        when(mockIHsdDepositService.createDepositRecord(depositCreateReqDTO)).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/deposit/create_deposit_item")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryDepositItem() throws Exception {
        // Setup
        // Configure IHsdDepositService.queryDepositRecord(...).
        final DepositQueryRespDTO depositQueryRespDTO = new DepositQueryRespDTO();
        depositQueryRespDTO.setGuid("0ca97350-8a52-4b97-b500-498f1d7ca381");
        depositQueryRespDTO.setDepositOrderId("depositOrderId");
        depositQueryRespDTO.setHeadPortrait("headPortrait");
        depositQueryRespDTO.setCustomerName("customerName");
        depositQueryRespDTO.setPhoneNum("phoneNum");
        final Page<DepositQueryRespDTO> depositQueryRespDTOPage = new Page<>(0L, 0L,
                Arrays.asList(depositQueryRespDTO));
        final DepositQueryReqDTO depositQueryReqDTO = new DepositQueryReqDTO();
        depositQueryReqDTO.setStoreGuid("storeGuid");
        depositQueryReqDTO.setCondition("condition");
        depositQueryReqDTO.setPhoneGuid("phoneGuid");
        depositQueryReqDTO.setWxGuid("wxGuid");
        when(mockIHsdDepositService.queryDepositRecord(depositQueryReqDTO)).thenReturn(depositQueryRespDTOPage);

        // Run the test and verify the results
        mockMvc.perform(post("/deposit/query_deposit_item")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryDepositDetail() throws Exception {
        // Setup
        // Configure IHsdDepositService.queryDepositDetail(...).
        final GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
        goodsRespDTO.setGuid("c3f46955-8980-4c7b-bf62-d7ac8edd39fb");
        goodsRespDTO.setGoodsName("goodsName");
        goodsRespDTO.setSkuName("skuName");
        goodsRespDTO.setSkuGuid("skuGuid");
        goodsRespDTO.setGoodsUnit("goodsUnit");
        final List<GoodsRespDTO> goodsRespDTOS = Arrays.asList(goodsRespDTO);
        final QueryDepositDetailReqDTO depositQueryReqDTO = new QueryDepositDetailReqDTO();
        depositQueryReqDTO.setDepositGuid("depositGuid");
        when(mockIHsdDepositService.queryDepositDetail(depositQueryReqDTO)).thenReturn(goodsRespDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/deposit/query_deposit_detail")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryDepositDetail_IHsdDepositServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure IHsdDepositService.queryDepositDetail(...).
        final QueryDepositDetailReqDTO depositQueryReqDTO = new QueryDepositDetailReqDTO();
        depositQueryReqDTO.setDepositGuid("depositGuid");
        when(mockIHsdDepositService.queryDepositDetail(depositQueryReqDTO)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/deposit/query_deposit_detail")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testQueryDepositDetailForPos() throws Exception {
        // Setup
        // Configure IHsdDepositService.queryDepositDetailForPos(...).
        final DepositDetailForPosRespDTO depositDetailForPosRespDTO = new DepositDetailForPosRespDTO();
        depositDetailForPosRespDTO.setDepositOrderId("depositOrderId");
        depositDetailForPosRespDTO.setSaveTime("saveTime");
        depositDetailForPosRespDTO.setHeadPortrait("headPortrait");
        depositDetailForPosRespDTO.setCustomerName("customerName");
        depositDetailForPosRespDTO.setPhoneNum("phoneNum");
        final QueryDepositDetailReqDTO depositQueryReqDTO = new QueryDepositDetailReqDTO();
        depositQueryReqDTO.setDepositGuid("depositGuid");
        when(mockIHsdDepositService.queryDepositDetailForPos(depositQueryReqDTO))
                .thenReturn(depositDetailForPosRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/deposit/query_deposit_detail_for_pos")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetDepositGoods() throws Exception {
        // Setup
        // Configure IHsdDepositService.getDeposit(...).
        final DepositGetReqDTO depositGetReqDTO = new DepositGetReqDTO();
        depositGetReqDTO.setDepositGuid("depositGuid");
        depositGetReqDTO.setUserGuid("userGuid");
        final GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
        goodsRespDTO.setGuid("c3f46955-8980-4c7b-bf62-d7ac8edd39fb");
        goodsRespDTO.setGoodsName("goodsName");
        depositGetReqDTO.setGoodsList(Arrays.asList(goodsRespDTO));
        when(mockIHsdDepositService.getDeposit(depositGetReqDTO)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/deposit/get_deposit_goods")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetDepositGoods_IHsdDepositServiceReturnsTrue() throws Exception {
        // Setup
        // Configure IHsdDepositService.getDeposit(...).
        final DepositGetReqDTO depositGetReqDTO = new DepositGetReqDTO();
        depositGetReqDTO.setDepositGuid("depositGuid");
        depositGetReqDTO.setUserGuid("userGuid");
        final GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
        goodsRespDTO.setGuid("c3f46955-8980-4c7b-bf62-d7ac8edd39fb");
        goodsRespDTO.setGoodsName("goodsName");
        depositGetReqDTO.setGoodsList(Arrays.asList(goodsRespDTO));
        when(mockIHsdDepositService.getDeposit(depositGetReqDTO)).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/deposit/get_deposit_goods")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryOperationHistory() throws Exception {
        // Setup
        // Configure IHsdDepositService.queryOperationHistory(...).
        final OperationQueryRespDTO operationQueryRespDTO = new OperationQueryRespDTO();
        operationQueryRespDTO.setOperationTime("operationTime");
        operationQueryRespDTO.setOperationWay(0);
        operationQueryRespDTO.setOperator("operator");
        final GoodsSimpleRespDTO goodsSimpleRespDTO = new GoodsSimpleRespDTO();
        goodsSimpleRespDTO.setGoodsName("goodsName");
        operationQueryRespDTO.setGoodsList(Arrays.asList(goodsSimpleRespDTO));
        final Page<OperationQueryRespDTO> operationQueryRespDTOPage = new Page<>(0L, 0L,
                Arrays.asList(operationQueryRespDTO));
        final OperationHistoryQueryReqDTO operationHistoryQueryReqDTO = new OperationHistoryQueryReqDTO();
        operationHistoryQueryReqDTO.setDepositGuid("depositGuid");
        when(mockIHsdDepositService.queryOperationHistory(operationHistoryQueryReqDTO))
                .thenReturn(operationQueryRespDTOPage);

        // Run the test and verify the results
        mockMvc.perform(post("/deposit/query_operation_history")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryGoodsSummary() throws Exception {
        // Setup
        // Configure IHsdDepositService.queryGoodsSummary(...).
        final GoodsSummaryRespDTO goodsSummaryRespDTO = new GoodsSummaryRespDTO();
        goodsSummaryRespDTO.setGoodsName("goodsName");
        goodsSummaryRespDTO.setSum(0);
        final Page<GoodsSummaryRespDTO> goodsSummaryRespDTOPage = new Page<>(0L, 0L,
                Arrays.asList(goodsSummaryRespDTO));
        final DepositQueryReqForWebDTO depositQueryReqForWebDTO = new DepositQueryReqForWebDTO();
        depositQueryReqForWebDTO.setStoreGuid("storeGuid");
        depositQueryReqForWebDTO.setCondition("condition");
        when(mockIHsdDepositService.queryGoodsSummary(depositQueryReqForWebDTO)).thenReturn(goodsSummaryRespDTOPage);

        // Run the test and verify the results
        mockMvc.perform(post("/deposit/query_goods_summary")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testDepositExpireRemind() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/deposit/deposit_expire_remind")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockIHsdDepositService).sendExpireRemindMessage();
    }

    @Test
    public void testRemindSet() throws Exception {
        // Setup
        // Configure IHsdDepositService.remindSet(...).
        final MessageRemindReqDTO messageRemindReqDTO = new MessageRemindReqDTO();
        messageRemindReqDTO.setStoreGuid("storeGuid");
        messageRemindReqDTO.setStoreName("storeName");
        messageRemindReqDTO.setDepositRemind(0);
        messageRemindReqDTO.setGetRemind(0);
        messageRemindReqDTO.setExpireRemind(0);
        when(mockIHsdDepositService.remindSet(messageRemindReqDTO)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/deposit/remind_set")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testRemindSet_IHsdDepositServiceReturnsTrue() throws Exception {
        // Setup
        // Configure IHsdDepositService.remindSet(...).
        final MessageRemindReqDTO messageRemindReqDTO = new MessageRemindReqDTO();
        messageRemindReqDTO.setStoreGuid("storeGuid");
        messageRemindReqDTO.setStoreName("storeName");
        messageRemindReqDTO.setDepositRemind(0);
        messageRemindReqDTO.setGetRemind(0);
        messageRemindReqDTO.setExpireRemind(0);
        when(mockIHsdDepositService.remindSet(messageRemindReqDTO)).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/deposit/remind_set")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryRemind() throws Exception {
        // Setup
        // Configure IHsdDepositService.queryRemind(...).
        final MessageRemindReqDTO messageRemindReqDTO = new MessageRemindReqDTO();
        messageRemindReqDTO.setStoreGuid("storeGuid");
        messageRemindReqDTO.setStoreName("storeName");
        messageRemindReqDTO.setDepositRemind(0);
        messageRemindReqDTO.setGetRemind(0);
        messageRemindReqDTO.setExpireRemind(0);
        when(mockIHsdDepositService.queryRemind("storeGuid")).thenReturn(messageRemindReqDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/deposit/query_remind")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
