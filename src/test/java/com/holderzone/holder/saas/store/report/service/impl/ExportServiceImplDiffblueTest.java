package com.holderzone.holder.saas.store.report.service.impl;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertSame;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.store.report.service.TradeItemService;
import com.holderzone.holder.saas.store.report.service.rpc.business.HandoverClientService;
import com.holderzone.saas.store.dto.journaling.resp.OrderDetailRespDTO;
import com.holderzone.saas.store.dto.report.resp.ExportRespDTO;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@ContextConfiguration(classes = {ExportServiceImpl.class})
@RunWith(SpringJUnit4ClassRunner.class)
public class ExportServiceImplDiffblueTest {
    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @Autowired
    private ExportServiceImpl exportServiceImpl;

    @MockBean
    private HandoverClientService handoverClientService;

    @MockBean
    private TradeItemService tradeItemService;

    /**
     * Method under test: {@link ExportServiceImpl#export(Integer, String)}
     */
    @Test
    public void testExport() {
        ExportRespDTO actualExportResult = exportServiceImpl.export(1, "Param String");
        assertEquals(
                "orderNo:订单号,stateName:订单状态,tradeModeName:用餐类型,guestCount:就餐人数,itemTotalFee:商品金额,appendFee:附加费,orderFee"
                        + ":订单金额,savingZero:系统省零,memberDiscount:会员折扣,memberCoupons:会员优惠券,itemPresent:菜品赠送,fullSinglePrice:整单让价"
                        + ",groupCoupons:团购券,wholeDiscount:整单折扣,points:积分抵扣,memberPrice:会员价,singleDiscount:单品折扣,goodsGrouper:商品券"
                        + ",campaign:营销活动,paymentTypes:付款方式,actuallyPayFee:实收,deviceTypeName:订单来源,operationAccount:操作人,storeName"
                        + ":门店,gmtCreate:下单时间,checkOutTime:结账时间",
                actualExportResult.getHead());
        assertEquals(400, actualExportResult.getHeight());
        Class<OrderDetailRespDTO> expectedClzz = OrderDetailRespDTO.class;
        assertSame(expectedClzz, actualExportResult.getClzz());
    }

    /**
     * Method under test: {@link ExportServiceImpl#export(Integer, String)}
     */
    @Test
    public void testExport2() {
        thrown.expect(BusinessException.class);
        exportServiceImpl.export(1, "");
    }
}
