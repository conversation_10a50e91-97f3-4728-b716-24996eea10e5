package com.holderzone.holder.saas.store.table.utils;

import java.io.BufferedReader;
import java.io.InputStreamReader;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/15 14:35
 * @className: JsonFileUtil
 */
public class JsonFileUtil {

    public static String read(String name) {
        try {
            BufferedReader bufferedReader = new BufferedReader(
                    new InputStreamReader(JsonFileUtil.class.getClassLoader().getResourceAsStream(name), "UTF-8"));
            StringBuilder stringBuilder = new StringBuilder();
            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                stringBuilder.append(line);
            }
            return stringBuilder.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
