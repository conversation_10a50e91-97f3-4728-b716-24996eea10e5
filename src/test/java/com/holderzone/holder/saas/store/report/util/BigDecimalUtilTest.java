package com.holderzone.holder.saas.store.report.util;

import org.junit.Test;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;

public class BigDecimalUtilTest {

    @Test
    public void testNegative() {
        assertThat(BigDecimalUtil.negative(new BigDecimal("0.00"))).isEqualTo(new BigDecimal("0.00"));
    }

    @Test
    public void testGreaterThanZero() {
        assertThat(BigDecimalUtil.greaterThanZero(new BigDecimal("0.00"))).isFalse();
    }

    @Test
    public void testEquelZero() {
        assertThat(BigDecimalUtil.equelZero(new BigDecimal("0.00"))).isFalse();
    }

    @Test
    public void testLessThanZero() {
        assertThat(BigDecimalUtil.lessThanZero(new BigDecimal("0.00"))).isFalse();
    }

    @Test
    public void testSetScale2() {
        assertThat(BigDecimalUtil.setScale2(new BigDecimal("0.00"))).isEqualTo(new BigDecimal("0.00"));
    }

    @Test
    public void testQuantityTrimmed() {
        assertThat(BigDecimalUtil.quantityTrimmed(new BigDecimal("0.00"))).isEqualTo("result");
    }

    @Test
    public void testLessThan() {
        assertThat(BigDecimalUtil.lessThan(new BigDecimal("0.00"), new BigDecimal("0.00"))).isFalse();
    }

    @Test
    public void testLessEqual() {
        assertThat(BigDecimalUtil.lessEqual(new BigDecimal("0.00"), new BigDecimal("0.00"))).isFalse();
    }

    @Test
    public void testGreaterThan() {
        assertThat(BigDecimalUtil.greaterThan(new BigDecimal("0.00"), new BigDecimal("0.00"))).isFalse();
    }

    @Test
    public void testGreaterEqual() {
        assertThat(BigDecimalUtil.greaterEqual(new BigDecimal("0.00"), new BigDecimal("0.00"))).isFalse();
    }

    @Test
    public void testEquals() {
        assertThat(BigDecimalUtil.equals(new BigDecimal("0.00"), new BigDecimal("0.00"))).isFalse();
    }

    @Test
    public void testGetRealDiscount() {
        assertThat(BigDecimalUtil.getRealDiscount(new BigDecimal("0.00"))).isEqualTo(new BigDecimal("0.00"));
    }

    @Test
    public void testNonNullValue() {
        assertThat(BigDecimalUtil.nonNullValue(new BigDecimal("0.00"))).isEqualTo(new BigDecimal("0.00"));
    }

    @Test
    public void testDivide() {
        assertThat(BigDecimalUtil.divide(new BigDecimal("0.00"), new BigDecimal("0.00")))
                .isEqualTo(new BigDecimal("0.00"));
    }

    @Test
    public void testMultiply2() {
        assertThat(BigDecimalUtil.multiply2(new BigDecimal("0.00"), new BigDecimal("0.00")))
                .isEqualTo(new BigDecimal("0.00"));
    }
}
