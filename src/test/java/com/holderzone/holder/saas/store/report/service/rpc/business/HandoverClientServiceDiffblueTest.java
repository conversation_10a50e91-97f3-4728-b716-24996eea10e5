package com.holderzone.holder.saas.store.report.service.rpc.business;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.saas.store.dto.report.query.HandOverReportQueryDTO;

import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;

import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;

public class HandoverClientServiceDiffblueTest {
    @Rule
    public ExpectedException thrown = ExpectedException.none();

    /**
     * Method under test:
     * {@link HandoverClientService.BusinessFallBack#create(Throwable)}
     */
    @Test
    public void testBusinessFallBackCreate() {
        //   Diffblue <PERSON> was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R002 Missing observers.
        //   Diffblue Cover was unable to create an assertion.
        //   Add getters for the following fields or make them package-private:
        //     1863795704.arg$1

        HandoverClientService.BusinessFallBack businessFallBack = new HandoverClientService.BusinessFallBack();
        HandoverClientService actualCreateResult = businessFallBack.create(new Throwable());
        HandOverReportQueryDTO handOverQueryDTO = new HandOverReportQueryDTO();
        handOverQueryDTO.setBusinessEndDateTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        handOverQueryDTO.setBusinessStartDateTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        handOverQueryDTO.setState(1);
        handOverQueryDTO.setStoreGuid("1234");
        handOverQueryDTO.setUserGuids(new ArrayList<>());
        thrown.expect(ServerException.class);
        actualCreateResult.report(handOverQueryDTO);
    }

    /**
     * Method under test:
     * {@link HandoverClientService.BusinessFallBack#create(Throwable)}
     */
    @Test
    public void testBusinessFallBackCreate2() {
        //   Diffblue Cover was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R002 Missing observers.
        //   Diffblue Cover was unable to create an assertion.
        //   Add getters for the following fields or make them package-private:
        //     1863795704.arg$1

        HandoverClientService.BusinessFallBack businessFallBack = new HandoverClientService.BusinessFallBack();
        HandoverClientService actualCreateResult = businessFallBack.create(new IOException("服务间调用{}熔断，入参{}，异常{}"));
        HandOverReportQueryDTO handOverQueryDTO = new HandOverReportQueryDTO();
        handOverQueryDTO.setBusinessEndDateTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        handOverQueryDTO.setBusinessStartDateTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        handOverQueryDTO.setState(1);
        handOverQueryDTO.setStoreGuid("1234");
        handOverQueryDTO.setUserGuids(new ArrayList<>());
        thrown.expect(ServerException.class);
        actualCreateResult.report(handOverQueryDTO);
    }
}
