package com.holderzone.holder.saas.store.report.util;

import org.junit.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;

import static org.assertj.core.api.Assertions.assertThat;

public class DateUtilsTest {

    @Test
    public void testBillDateStrToDateStr() {
        assertThat(DateUtils.billDateStrToDateStr("billStr")).isEqualTo("result");
    }

    @Test
    public void testBillDateStrToLocalDate() {
        assertThat(DateUtils.billDateStrToLocalDate("billStr")).isEqualTo(LocalDate.of(2020, 1, 1));
    }

    @Test
    public void testDateToTimestamp() {
        assertThat(DateUtils.dateToTimestamp(LocalDate.of(2020, 1, 1), false)).isEqualTo(0L);
    }

    @Test
    public void testToDateTime() {
        assertThat(DateUtils.toDateTime(0L)).isEqualTo(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }

    @Test
    public void testStringToDate() {
        assertThat(DateUtils.stringToDate("dateStr"))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testDateToEsString() {
        assertThat(DateUtils.dateToEsString(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
                .isEqualTo("result");
    }

    @Test
    public void testStringToEsDate() {
        assertThat(DateUtils.stringToEsDate("dateStr"))
                .isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testStringToDateToString() {
        assertThat(DateUtils.stringToDateToString("dateStr")).isEqualTo("result");
    }

    @Test
    public void testConvertToDateList() {
        assertThat(DateUtils.convertToDateList(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1)))
                .isEqualTo(Arrays.asList(LocalDate.of(2020, 1, 1)));
        assertThat(DateUtils.convertToDateList(LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 1)))
                .isEqualTo(Collections.emptyList());
    }
}
