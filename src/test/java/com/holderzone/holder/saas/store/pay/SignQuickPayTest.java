package com.holderzone.holder.saas.store.pay;

import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.pay.dto.SignReqDTO;
import com.holderzone.holder.saas.store.pay.utils.TradingUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;


@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStorePayApplication.class)
public class SignQuickPayTest {

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    public static final String APP_ID = "070db269-5279-4321-9d18-e29b9d8553f5";

    public static final String DEVELOPER_ID = "10000";

    public static final String CHANNEL_CODE = "100018";

    public static final String PLATFORM = "member";

    public static final String SIGN_GUID = "2309281556018670000";

    public static final String DEVELOPER_KEY = "A6041E8B17CA0082EECA481D623137F2";

    public static final String APP_SECRET = "m4LG9Hv9OJeyw30A9pOsXw==";

    @Test
    public void signObjectTest() {
        // 签约对象
        SignReqDTO signReqDTO = new SignReqDTO();
        signReqDTO.setAppId(APP_ID);
        signReqDTO.setDeveloperId(DEVELOPER_ID);
        signReqDTO.setTimestamp(DateTimeUtils.nowMillis());
        signReqDTO.setPlatform(PLATFORM);
        signReqDTO.setChannelCode(CHANNEL_CODE);
        signReqDTO.setSignedChannel("H5");
        signReqDTO.setUserNo("13880071947");
        signReqDTO.setNotifyUrl("https://member-base-test.holderzone.cn/sign");
        signReqDTO.setFrontNotifyUrl("https://member-base-test.holderzone.cn/signPage");
        signReqDTO.setSignature(TradingUtils.getSignature(
                signReqDTO, DEVELOPER_KEY, APP_SECRET
        ));
        log.info("signReqDTO:{}", JacksonUtils.writeValueAsString(signReqDTO));
    }

    @Test
    public void querySignObjectTest() {
        // 查询签约对象
        SignReqDTO signQueryReqDTO = new SignReqDTO();
        signQueryReqDTO.setAppId(APP_ID);
        signQueryReqDTO.setDeveloperId(DEVELOPER_ID);
        signQueryReqDTO.setTimestamp(DateTimeUtils.nowMillis());
        signQueryReqDTO.setChannelCode(CHANNEL_CODE);
        signQueryReqDTO.setPlatform(PLATFORM);
        signQueryReqDTO.setSignGuid(SIGN_GUID);
        signQueryReqDTO.setSignature(TradingUtils.getSignature(
                signQueryReqDTO, DEVELOPER_KEY, APP_SECRET
        ));
        log.info("signQueryReqDTO:{}", JacksonUtils.writeValueAsString(signQueryReqDTO));
    }

    @Test
    public void unSignObjectTest() {
        // 解约对象
        SignReqDTO signUnReqDTO = new SignReqDTO();
        signUnReqDTO.setAppId(APP_ID);
        signUnReqDTO.setDeveloperId(DEVELOPER_ID);
        signUnReqDTO.setTimestamp(DateTimeUtils.nowMillis());
        signUnReqDTO.setPlatform(PLATFORM);
        signUnReqDTO.setChannelCode(CHANNEL_CODE);
        signUnReqDTO.setSignGuid(SIGN_GUID);
        signUnReqDTO.setSignature(TradingUtils.getSignature(
                signUnReqDTO, DEVELOPER_KEY, APP_SECRET
        ));
        log.info("signUnReqDTO:{}", JacksonUtils.writeValueAsString(signUnReqDTO));
    }
}