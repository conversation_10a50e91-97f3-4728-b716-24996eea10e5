package com.holderzone.holder.saas.store.report.controller;

import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.holderzone.holder.saas.store.report.service.ExportService;
import com.holderzone.saas.store.dto.report.query.ReportExportDTO;
import com.holderzone.saas.store.dto.report.resp.ExportRespDTO;

import java.util.ArrayList;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ContextConfiguration(classes = {ReportExportController.class})
@RunWith(SpringJUnit4ClassRunner.class)
public class ReportExportControllerDiffblueTest {
    @MockBean
    private ExportService exportService;

    @Autowired
    private ReportExportController reportExportController;

    /**
     * Method under test: {@link ReportExportController#export(ReportExportDTO)}
     */
    @Test
    public void testExport() throws Exception {
        ExportRespDTO exportRespDTO = new ExportRespDTO();
        Class<Object> clzz = Object.class;
        exportRespDTO.setClzz(clzz);
        exportRespDTO.setExcelName("Excel Name");
        exportRespDTO.setHead("Head");
        exportRespDTO.setHeight(1);
        exportRespDTO.setList(new ArrayList<>());
        when(exportService.export(Mockito.<Integer>any(), Mockito.<String>any())).thenReturn(exportRespDTO);

        ReportExportDTO reportExportDTO = new ReportExportDTO();
        reportExportDTO.setExportType(1);
        reportExportDTO.setJsonStr("Json Str");
        String content = (new ObjectMapper()).writeValueAsString(reportExportDTO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/journal/export")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);
        MockMvcBuilders.standaloneSetup(reportExportController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content()
                        .string(
                                "{\"clzz\":\"java.lang.Object\",\"list\":[],\"head\":\"Head\",\"excelName\":\"Excel Name\",\"height\":1}"));
    }
}
