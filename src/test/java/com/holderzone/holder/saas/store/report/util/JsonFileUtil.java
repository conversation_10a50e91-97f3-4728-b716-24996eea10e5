package com.holderzone.holder.saas.store.report.util;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Objects;


/**
 * <AUTHOR>
 */
public class JsonFileUtil {

    private JsonFileUtil() {
    }

    public static String read(String name) {
        try {
            BufferedReader bufferedReader = new BufferedReader(
                    new InputStreamReader(Objects.requireNonNull(JsonFileUtil.class.getClassLoader().getResourceAsStream(name)), StandardCharsets.UTF_8));
            StringBuilder stringBuilder = new StringBuilder();
            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                stringBuilder.append(line);
            }
            return stringBuilder.toString();
        } catch (Exception e) {
            return null;
        }
    }
}
