package com.holderzone.holder.saas.store.report.controller;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.store.report.HolderSaasReportApplication;
import com.holderzone.holder.saas.store.report.util.JsonFileUtil;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import com.holderzone.saas.store.dto.report.query.PaymentConstituteQueryDTO;
import com.holderzone.saas.store.dto.report.query.TradeDetailQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.UnsupportedEncodingException;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.junit.Assert.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @date 2024/2/29
 * @description
 */
@Slf4j
@WebAppConfiguration
@ContextConfiguration
@AutoConfigureMockMvc
@RunWith(SpringRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@SpringBootTest(classes = HolderSaasReportApplication.class)
public class TradeDetailControllerTest {

    private static final String USERINFO = "{\"operSubjectGuid\": \"2010121440477930009\"," +
            "\"enterpriseGuid\":" + " \"2009281531195930006\"," +
            "\"enterpriseName\": \"赵氏企业\"," +
            "\"enterpriseNo\": \"********\"," +
            "\"storeGuid\":" + " \"2106221850429620006\"," +
            "\"storeName\": \"交子大道测试门店\"," +
            "\"storeNo\": \"5796807\"," +
            "\"userGuid\": \"6787561298847596545\"," +
            "\"account\": \"196504\"," +
            "\"tel\": \"***********\"," +
            "\"name\": \"靓亮仔\"}\n";

    private static final String TRADE_DETAIL = "/trade/detail";

    private static final String RESPONSE = "response:";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setupMockMvc() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void pageGroupon() throws UnsupportedEncodingException {
        TradeDetailQueryDTO pageGrouponReqDTO = JSON.parseObject(JsonFileUtil.read("tradeDetail/pageGroupon.json"),
                TradeDetailQueryDTO.class);
        String pageGrouponJsonString = JSON.toJSONString(pageGrouponReqDTO);
        MvcResult pageGrouponResult = null;
        try {
            pageGrouponResult = mockMvc.perform(post(TRADE_DETAIL + "/groupon")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(pageGrouponJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = pageGrouponResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void grouponExport() throws UnsupportedEncodingException {
        TradeDetailQueryDTO grouponExportReqDTO = JSON.parseObject(JsonFileUtil.read("tradeDetail/grouponExport.json"),
                TradeDetailQueryDTO.class);
        String grouponExportJsonString = JSON.toJSONString(grouponExportReqDTO);
        MvcResult grouponExportResult = null;
        try {
            grouponExportResult = mockMvc.perform(post(TRADE_DETAIL + "/groupon/export")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(grouponExportJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = grouponExportResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void pageTakeaway() throws UnsupportedEncodingException {
        TradeDetailQueryDTO pageTakeawayReqDTO = JSON.parseObject(JsonFileUtil.read("tradeDetail/pageTakeaway.json"),
                TradeDetailQueryDTO.class);
        String pageTakeawayJsonString = JSON.toJSONString(pageTakeawayReqDTO);
        MvcResult pageTakeawayResult = null;
        try {
            pageTakeawayResult = mockMvc.perform(post(TRADE_DETAIL + "/takeaway")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(pageTakeawayJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = pageTakeawayResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void takeawayExport() throws UnsupportedEncodingException {
        TradeDetailQueryDTO takeawayExportReqDTO = JSON.parseObject(JsonFileUtil.read("tradeDetail/takeawayExport.json"),
                TradeDetailQueryDTO.class);
        String takeawayExportJsonString = JSON.toJSONString(takeawayExportReqDTO);
        MvcResult takeawayExportResult = null;
        try {
            takeawayExportResult = mockMvc.perform(post(TRADE_DETAIL + "/takeaway/export")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(takeawayExportJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = takeawayExportResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void paymentConstitute() throws UnsupportedEncodingException {
        PaymentConstituteQueryDTO paymentConstituteReqDTO = JSON.parseObject(JsonFileUtil.read("tradeDetail/paymentConstitute.json"),
                PaymentConstituteQueryDTO.class);
        String takeawayExportJsonString = JSON.toJSONString(paymentConstituteReqDTO);
        MvcResult paymentConstituteResult = null;
        try {
            paymentConstituteResult = mockMvc.perform(post(TRADE_DETAIL + "/payment/constitute")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(takeawayExportJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = paymentConstituteResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }

    @Test
    public void paymentConstituteExport() throws UnsupportedEncodingException {
        PaymentConstituteQueryDTO paymentConstituteExportReqDTO = JSON.parseObject(JsonFileUtil.read("tradeDetail/paymentConstituteExport.json"),
                PaymentConstituteQueryDTO.class);
        String paymentConstituteExportJsonString = JSON.toJSONString(paymentConstituteExportReqDTO);
        MvcResult paymentConstituteExportResult = null;
        try {
            paymentConstituteExportResult = mockMvc.perform(post(TRADE_DETAIL + "/payment/constitute/export")
                            .header(USER_INFO, USERINFO)
                            .accept(MediaType.APPLICATION_JSON_VALUE)
                            .contentType(MediaType.APPLICATION_JSON).content(paymentConstituteExportJsonString))
                    .andExpect(status().isOk()).andDo(print()).andReturn();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        String contentAsString = paymentConstituteExportResult.getResponse().getContentAsString();
        log.info(RESPONSE + contentAsString);
    }
}