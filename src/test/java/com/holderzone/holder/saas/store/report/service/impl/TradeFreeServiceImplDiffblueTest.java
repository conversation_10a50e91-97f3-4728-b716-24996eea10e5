package com.holderzone.holder.saas.store.report.service.impl;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.holder.saas.store.report.mapper.TradeFreeMapper;
import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.base.Pager;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.FreeItemDTO;
import com.holderzone.saas.store.dto.report.resp.TotalStatisticsDTO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@ContextConfiguration(classes = {TradeFreeServiceImpl.class})
@RunWith(SpringJUnit4ClassRunner.class)
public class TradeFreeServiceImplDiffblueTest {
    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @MockBean
    private OssClient ossClient;

    @MockBean
    private TradeFreeMapper tradeFreeMapper;

    @Autowired
    private TradeFreeServiceImpl tradeFreeServiceImpl;

    /**
     * Method under test: {@link TradeFreeServiceImpl#list(ReportQueryVO)}
     */
    @Test
    public void testList() {
        when(tradeFreeMapper.count(Mockito.<ReportQueryVO>any())).thenReturn(3);
        when(tradeFreeMapper.pageInfo(Mockito.<ReportQueryVO>any())).thenReturn(new ArrayList<>());
        TotalStatisticsDTO.TotalStatisticsDTOBuilder builderResult = TotalStatisticsDTO.builder();
        TotalStatisticsDTO.TotalStatisticsDTOBuilder storeGuidResult = builderResult
                .actuallyRefundFee(new BigDecimal("2.3"))
                .brandGuid("1234")
                .goodsGuid("1234")
                .storeGuid("1234");
        TotalStatisticsDTO.TotalStatisticsDTOBuilder totalMoneyResult = storeGuidResult.totalMoney(new BigDecimal("2.3"));
        TotalStatisticsDTO buildResult = totalMoneyResult.totalQuantity(new BigDecimal("2.3")).build();
        when(tradeFreeMapper.statistics(Mockito.<ReportQueryVO>any())).thenReturn(buildResult);

        ReportQueryVO query = new ReportQueryVO();
        query.setBrandGuid("1234");
        query.setCateringType(1);
        query.setCurrentPage(1);
        query.setEndTime(LocalDate.of(1970, 1, 1));
        query.setEnterpriseGuid("1234");
        query.setGoodsCategories("Goods Categories");
        query.setGoodsType(1);
        query.setItemName("Item Name");
        query.setOperatorNode(1);
        query.setPageSize(3);
        query.setStartTime(LocalDate.of(1970, 1, 1));
        query.setStoreGuids(new ArrayList<>());
        Message<FreeItemDTO> actualListResult = tradeFreeServiceImpl.list(query);
        verify(tradeFreeMapper).count(Mockito.<ReportQueryVO>any());
        verify(tradeFreeMapper).pageInfo(Mockito.<ReportQueryVO>any());
        verify(tradeFreeMapper).statistics(Mockito.<ReportQueryVO>any());
        assertEquals("success!", actualListResult.getMsg());
        assertEquals(0, actualListResult.getCode());
        Pager pager = actualListResult.getPager();
        assertEquals(1, pager.getPageNo());
        assertEquals(1, pager.getTotalPages());
        assertEquals(3, pager.getPageSize());
        assertEquals(3, pager.getTotalCount());
        assertEquals(6, actualListResult.getData().size());
        assertTrue(actualListResult.getList().isEmpty());
    }

    /**
     * Method under test: {@link TradeFreeServiceImpl#list(ReportQueryVO)}
     */
    @Test
    public void testList2() {
        when(tradeFreeMapper.count(Mockito.<ReportQueryVO>any())).thenThrow(new BusinessException("An error occurred"));
        TotalStatisticsDTO.TotalStatisticsDTOBuilder builderResult = TotalStatisticsDTO.builder();
        TotalStatisticsDTO.TotalStatisticsDTOBuilder storeGuidResult = builderResult
                .actuallyRefundFee(new BigDecimal("2.3"))
                .brandGuid("1234")
                .goodsGuid("1234")
                .storeGuid("1234");
        TotalStatisticsDTO.TotalStatisticsDTOBuilder totalMoneyResult = storeGuidResult.totalMoney(new BigDecimal("2.3"));
        TotalStatisticsDTO buildResult = totalMoneyResult.totalQuantity(new BigDecimal("2.3")).build();
        when(tradeFreeMapper.statistics(Mockito.<ReportQueryVO>any())).thenReturn(buildResult);

        ReportQueryVO query = new ReportQueryVO();
        query.setBrandGuid("1234");
        query.setCateringType(1);
        query.setCurrentPage(1);
        query.setEndTime(LocalDate.of(1970, 1, 1));
        query.setEnterpriseGuid("1234");
        query.setGoodsCategories("Goods Categories");
        query.setGoodsType(1);
        query.setItemName("Item Name");
        query.setOperatorNode(1);
        query.setPageSize(3);
        query.setStartTime(LocalDate.of(1970, 1, 1));
        query.setStoreGuids(new ArrayList<>());
        thrown.expect(BusinessException.class);
        tradeFreeServiceImpl.list(query);
        verify(tradeFreeMapper).count(Mockito.<ReportQueryVO>any());
        verify(tradeFreeMapper).statistics(Mockito.<ReportQueryVO>any());
    }

    /**
     * Method under test: {@link TradeFreeServiceImpl#list(ReportQueryVO)}
     */
    @Test
    public void testList3() {
        when(tradeFreeMapper.count(Mockito.<ReportQueryVO>any())).thenReturn(0);
        when(tradeFreeMapper.pageInfo(Mockito.<ReportQueryVO>any())).thenReturn(new ArrayList<>());
        TotalStatisticsDTO.TotalStatisticsDTOBuilder builderResult = TotalStatisticsDTO.builder();
        TotalStatisticsDTO.TotalStatisticsDTOBuilder storeGuidResult = builderResult
                .actuallyRefundFee(new BigDecimal("2.3"))
                .brandGuid("1234")
                .goodsGuid("1234")
                .storeGuid("1234");
        TotalStatisticsDTO.TotalStatisticsDTOBuilder totalMoneyResult = storeGuidResult.totalMoney(new BigDecimal("2.3"));
        TotalStatisticsDTO buildResult = totalMoneyResult.totalQuantity(new BigDecimal("2.3")).build();
        when(tradeFreeMapper.statistics(Mockito.<ReportQueryVO>any())).thenReturn(buildResult);

        ReportQueryVO query = new ReportQueryVO();
        query.setBrandGuid("1234");
        query.setCateringType(1);
        query.setCurrentPage(1);
        query.setEndTime(LocalDate.of(1970, 1, 1));
        query.setEnterpriseGuid("1234");
        query.setGoodsCategories("Goods Categories");
        query.setGoodsType(1);
        query.setItemName("Item Name");
        query.setOperatorNode(1);
        query.setPageSize(3);
        query.setStartTime(LocalDate.of(1970, 1, 1));
        query.setStoreGuids(new ArrayList<>());
        Message<FreeItemDTO> actualListResult = tradeFreeServiceImpl.list(query);
        verify(tradeFreeMapper).count(Mockito.<ReportQueryVO>any());
        verify(tradeFreeMapper).pageInfo(Mockito.<ReportQueryVO>any());
        verify(tradeFreeMapper).statistics(Mockito.<ReportQueryVO>any());
        assertEquals("success!", actualListResult.getMsg());
        assertEquals(0, actualListResult.getCode());
        Pager pager = actualListResult.getPager();
        assertEquals(0, pager.getTotalCount());
        assertEquals(1, pager.getPageNo());
        assertEquals(1, pager.getTotalPages());
        assertEquals(3, pager.getPageSize());
        assertEquals(6, actualListResult.getData().size());
        assertTrue(actualListResult.getList().isEmpty());
    }

    /**
     * Method under test: {@link TradeFreeServiceImpl#list(ReportQueryVO)}
     */
    @Test
    public void testList4() {
        FreeItemDTO freeItemDTO = new FreeItemDTO();
        freeItemDTO.setBrandName("success!");
        freeItemDTO.setGivenQuantity(new BigDecimal("2.3"));
        freeItemDTO.setGivenRate("success!");
        freeItemDTO.setGivenRefund(new BigDecimal("2.3"));
        freeItemDTO.setGoodsCategories("success!");
        freeItemDTO.setGoodsGuid("1234");
        freeItemDTO.setGoodsName("success!");
        freeItemDTO.setSkuGuid("1234");
        freeItemDTO.setStoreName("success!");

        ArrayList<FreeItemDTO> freeItemDTOList = new ArrayList<>();
        freeItemDTOList.add(freeItemDTO);
        when(tradeFreeMapper.count(Mockito.<ReportQueryVO>any())).thenReturn(3);
        when(tradeFreeMapper.pageInfo(Mockito.<ReportQueryVO>any())).thenReturn(freeItemDTOList);
        TotalStatisticsDTO.TotalStatisticsDTOBuilder builderResult = TotalStatisticsDTO.builder();
        TotalStatisticsDTO.TotalStatisticsDTOBuilder storeGuidResult = builderResult
                .actuallyRefundFee(new BigDecimal("2.3"))
                .brandGuid("1234")
                .goodsGuid("1234")
                .storeGuid("1234");
        TotalStatisticsDTO.TotalStatisticsDTOBuilder totalMoneyResult = storeGuidResult.totalMoney(new BigDecimal("2.3"));
        TotalStatisticsDTO buildResult = totalMoneyResult.totalQuantity(new BigDecimal("2.3")).build();
        when(tradeFreeMapper.statistics(Mockito.<ReportQueryVO>any())).thenReturn(buildResult);

        ReportQueryVO query = new ReportQueryVO();
        query.setBrandGuid("1234");
        query.setCateringType(1);
        query.setCurrentPage(1);
        query.setEndTime(LocalDate.of(1970, 1, 1));
        query.setEnterpriseGuid("1234");
        query.setGoodsCategories("Goods Categories");
        query.setGoodsType(1);
        query.setItemName("Item Name");
        query.setOperatorNode(1);
        query.setPageSize(3);
        query.setStartTime(LocalDate.of(1970, 1, 1));
        query.setStoreGuids(new ArrayList<>());
        Message<FreeItemDTO> actualListResult = tradeFreeServiceImpl.list(query);
        verify(tradeFreeMapper).count(Mockito.<ReportQueryVO>any());
        verify(tradeFreeMapper).pageInfo(Mockito.<ReportQueryVO>any());
        verify(tradeFreeMapper).statistics(Mockito.<ReportQueryVO>any());
        assertEquals("success!", actualListResult.getMsg());
        assertEquals(0, actualListResult.getCode());
        Pager pager = actualListResult.getPager();
        assertEquals(1, pager.getPageNo());
        assertEquals(1, pager.getTotalPages());
        assertEquals(1, actualListResult.getList().size());
        assertEquals(3, pager.getPageSize());
        assertEquals(3, pager.getTotalCount());
        assertEquals(6, actualListResult.getData().size());
    }

    /**
     * Method under test: {@link TradeFreeServiceImpl#list(ReportQueryVO)}
     */
    @Test
    public void testList5() {
        when(tradeFreeMapper.count(Mockito.<ReportQueryVO>any())).thenReturn(3);
        when(tradeFreeMapper.pageInfo(Mockito.<ReportQueryVO>any())).thenReturn(new ArrayList<>());
        TotalStatisticsDTO.TotalStatisticsDTOBuilder builderResult = TotalStatisticsDTO.builder();
        TotalStatisticsDTO.TotalStatisticsDTOBuilder storeGuidResult = builderResult
                .actuallyRefundFee(new BigDecimal("2.3"))
                .brandGuid("1234")
                .goodsGuid("1234")
                .storeGuid("1234");
        TotalStatisticsDTO.TotalStatisticsDTOBuilder totalMoneyResult = storeGuidResult.totalMoney(new BigDecimal("2.3"));
        TotalStatisticsDTO buildResult = totalMoneyResult.totalQuantity(new BigDecimal("2.3")).build();
        when(tradeFreeMapper.statistics(Mockito.<ReportQueryVO>any())).thenReturn(buildResult);
        ReportQueryVO query = mock(ReportQueryVO.class);
        when(query.getCurrentPage()).thenReturn(1);
        when(query.getPageSize()).thenReturn(-1);
        doNothing().when(query).setBrandGuid(Mockito.<String>any());
        doNothing().when(query).setCateringType(Mockito.<Integer>any());
        doNothing().when(query).setCurrentPage(Mockito.<Integer>any());
        doNothing().when(query).setEndTime(Mockito.<LocalDate>any());
        doNothing().when(query).setEnterpriseGuid(Mockito.<String>any());
        doNothing().when(query).setGoodsCategories(Mockito.<String>any());
        doNothing().when(query).setGoodsType(Mockito.<Integer>any());
        doNothing().when(query).setItemName(Mockito.<String>any());
        doNothing().when(query).setOperatorNode(Mockito.<Integer>any());
        doNothing().when(query).setPageSize(Mockito.<Integer>any());
        doNothing().when(query).setStartTime(Mockito.<LocalDate>any());
        doNothing().when(query).setStoreGuids(Mockito.<List<String>>any());
        query.setBrandGuid("1234");
        query.setCateringType(1);
        query.setCurrentPage(1);
        query.setEndTime(LocalDate.of(1970, 1, 1));
        query.setEnterpriseGuid("1234");
        query.setGoodsCategories("Goods Categories");
        query.setGoodsType(1);
        query.setItemName("Item Name");
        query.setOperatorNode(1);
        query.setPageSize(3);
        query.setStartTime(LocalDate.of(1970, 1, 1));
        query.setStoreGuids(new ArrayList<>());
        Message<FreeItemDTO> actualListResult = tradeFreeServiceImpl.list(query);
        verify(tradeFreeMapper).count(Mockito.<ReportQueryVO>any());
        verify(tradeFreeMapper).pageInfo(Mockito.<ReportQueryVO>any());
        verify(tradeFreeMapper).statistics(Mockito.<ReportQueryVO>any());
        verify(query).getCurrentPage();
        verify(query).getPageSize();
        verify(query).setBrandGuid(Mockito.<String>any());
        verify(query).setCateringType(Mockito.<Integer>any());
        verify(query).setCurrentPage(Mockito.<Integer>any());
        verify(query).setEndTime(Mockito.<LocalDate>any());
        verify(query).setEnterpriseGuid(Mockito.<String>any());
        verify(query).setGoodsCategories(Mockito.<String>any());
        verify(query).setGoodsType(Mockito.<Integer>any());
        verify(query).setItemName(Mockito.<String>any());
        verify(query).setOperatorNode(Mockito.<Integer>any());
        verify(query).setPageSize(Mockito.<Integer>any());
        verify(query).setStartTime(Mockito.<LocalDate>any());
        verify(query).setStoreGuids(Mockito.<List<String>>any());
        assertEquals("success!", actualListResult.getMsg());
        Pager pager = actualListResult.getPager();
        assertEquals(-1, pager.getPageSize());
        assertEquals(0, actualListResult.getCode());
        assertEquals(1, pager.getPageNo());
        assertEquals(1, pager.getTotalPages());
        assertEquals(3, pager.getTotalCount());
        assertEquals(6, actualListResult.getData().size());
        assertTrue(actualListResult.getList().isEmpty());
    }

    /**
     * Method under test: {@link TradeFreeServiceImpl#export(ReportQueryVO)}
     */
    @Test
    public void testExport() {
        //   Diffblue Cover was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R013 No inputs found that don't throw a trivial exception.
        //   Diffblue Cover tried to run the arrange/act section, but the method under
        //   test threw
        //   java.lang.NullPointerException
        //       at com.holderzone.holder.saas.store.report.service.impl.TradeFreeServiceImpl.export(TradeFreeServiceImpl.java:88)
        //   See https://diff.blue/R013 to resolve this issue.

        TradeFreeMapper tradeFreeMapper = mock(TradeFreeMapper.class);
        when(tradeFreeMapper.pageInfo(Mockito.<ReportQueryVO>any())).thenReturn(new ArrayList<>());
        TotalStatisticsDTO.TotalStatisticsDTOBuilder builderResult = TotalStatisticsDTO.builder();
        TotalStatisticsDTO.TotalStatisticsDTOBuilder storeGuidResult = builderResult
                .actuallyRefundFee(new BigDecimal("2.3"))
                .brandGuid("1234")
                .goodsGuid("1234")
                .storeGuid("1234");
        TotalStatisticsDTO.TotalStatisticsDTOBuilder totalMoneyResult = storeGuidResult.totalMoney(new BigDecimal("2.3"));
        TotalStatisticsDTO buildResult = totalMoneyResult.totalQuantity(new BigDecimal("2.3")).build();
        when(tradeFreeMapper.statistics(Mockito.<ReportQueryVO>any())).thenReturn(buildResult);
        when(tradeFreeMapper.count(Mockito.<ReportQueryVO>any())).thenReturn(3);
        OssClient ossClient = mock(OssClient.class);
        when(ossClient.upload(Mockito.<String>any(), Mockito.<byte[]>any())).thenReturn("Upload");
        TradeFreeServiceImpl tradeFreeServiceImpl = new TradeFreeServiceImpl(tradeFreeMapper, ossClient);

        ReportQueryVO query = new ReportQueryVO();
        query.setBrandGuid("1234");
        query.setCateringType(1);
        query.setCurrentPage(1);
        query.setEndTime(LocalDate.of(1970, 1, 1));
        query.setEnterpriseGuid("1234");
        query.setGoodsCategories("Goods Categories");
        query.setGoodsType(1);
        query.setItemName("Item Name");
        query.setOperatorNode(1);
        query.setPageSize(3);
        query.setStartTime(LocalDate.of(1970, 1, 1));
        query.setStoreGuids(new ArrayList<>());
        String actualExportResult = tradeFreeServiceImpl.export(query);
        verify(ossClient).upload(Mockito.<String>any(), Mockito.<byte[]>any());
        verify(tradeFreeMapper).count(Mockito.<ReportQueryVO>any());
        verify(tradeFreeMapper).pageInfo(Mockito.<ReportQueryVO>any());
        verify(tradeFreeMapper).statistics(Mockito.<ReportQueryVO>any());
        assertEquals("Upload", actualExportResult);
        assertEquals(20000, query.getPageSize().intValue());
    }

    /**
     * Method under test: {@link TradeFreeServiceImpl#export(ReportQueryVO)}
     */
    @Test
    public void testExport2() {
        //   Diffblue Cover was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R013 No inputs found that don't throw a trivial exception.
        //   Diffblue Cover tried to run the arrange/act section, but the method under
        //   test threw
        //   java.lang.NullPointerException
        //       at com.holderzone.holder.saas.store.report.service.impl.TradeFreeServiceImpl.export(TradeFreeServiceImpl.java:88)
        //   See https://diff.blue/R013 to resolve this issue.

        TradeFreeMapper tradeFreeMapper = mock(TradeFreeMapper.class);
        when(tradeFreeMapper.pageInfo(Mockito.<ReportQueryVO>any())).thenReturn(new ArrayList<>());
        TotalStatisticsDTO.TotalStatisticsDTOBuilder builderResult = TotalStatisticsDTO.builder();
        TotalStatisticsDTO.TotalStatisticsDTOBuilder storeGuidResult = builderResult
                .actuallyRefundFee(new BigDecimal("2.3"))
                .brandGuid("1234")
                .goodsGuid("1234")
                .storeGuid("1234");
        TotalStatisticsDTO.TotalStatisticsDTOBuilder totalMoneyResult = storeGuidResult.totalMoney(new BigDecimal("2.3"));
        TotalStatisticsDTO buildResult = totalMoneyResult.totalQuantity(new BigDecimal("2.3")).build();
        when(tradeFreeMapper.statistics(Mockito.<ReportQueryVO>any())).thenReturn(buildResult);
        when(tradeFreeMapper.count(Mockito.<ReportQueryVO>any())).thenReturn(3);
        OssClient ossClient = mock(OssClient.class);
        when(ossClient.upload(Mockito.<String>any(), Mockito.<byte[]>any()))
                .thenThrow(new BusinessException("An error occurred"));
        TradeFreeServiceImpl tradeFreeServiceImpl = new TradeFreeServiceImpl(tradeFreeMapper, ossClient);

        ReportQueryVO query = new ReportQueryVO();
        query.setBrandGuid("1234");
        query.setCateringType(1);
        query.setCurrentPage(1);
        query.setEndTime(LocalDate.of(1970, 1, 1));
        query.setEnterpriseGuid("1234");
        query.setGoodsCategories("Goods Categories");
        query.setGoodsType(1);
        query.setItemName("Item Name");
        query.setOperatorNode(1);
        query.setPageSize(3);
        query.setStartTime(LocalDate.of(1970, 1, 1));
        query.setStoreGuids(new ArrayList<>());
        thrown.expect(BusinessException.class);
        tradeFreeServiceImpl.export(query);
        verify(ossClient).upload(Mockito.<String>any(), Mockito.<byte[]>any());
        verify(tradeFreeMapper).count(Mockito.<ReportQueryVO>any());
        verify(tradeFreeMapper).pageInfo(Mockito.<ReportQueryVO>any());
        verify(tradeFreeMapper).statistics(Mockito.<ReportQueryVO>any());
    }

    /**
     * Method under test: {@link TradeFreeServiceImpl#export(ReportQueryVO)}
     */
    @Test
    public void testExport3() {
        //   Diffblue Cover was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R013 No inputs found that don't throw a trivial exception.
        //   Diffblue Cover tried to run the arrange/act section, but the method under
        //   test threw
        //   java.lang.NullPointerException
        //       at com.holderzone.holder.saas.store.report.service.impl.TradeFreeServiceImpl.export(TradeFreeServiceImpl.java:88)
        //   See https://diff.blue/R013 to resolve this issue.

        FreeItemDTO freeItemDTO = new FreeItemDTO();
        freeItemDTO.setBrandName("UUU/UUU");
        freeItemDTO.setGivenQuantity(new BigDecimal("2.3"));
        freeItemDTO.setGivenRate("UUU/UUU");
        freeItemDTO.setGivenRefund(new BigDecimal("2.3"));
        freeItemDTO.setGoodsCategories("UUU/UUU");
        freeItemDTO.setGoodsGuid("1234");
        freeItemDTO.setGoodsName("UUU/UUU");
        freeItemDTO.setSkuGuid("1234");
        freeItemDTO.setStoreName("UUU/UUU");

        ArrayList<FreeItemDTO> freeItemDTOList = new ArrayList<>();
        freeItemDTOList.add(freeItemDTO);
        TradeFreeMapper tradeFreeMapper = mock(TradeFreeMapper.class);
        when(tradeFreeMapper.pageInfo(Mockito.<ReportQueryVO>any())).thenReturn(freeItemDTOList);
        TotalStatisticsDTO.TotalStatisticsDTOBuilder builderResult = TotalStatisticsDTO.builder();
        TotalStatisticsDTO.TotalStatisticsDTOBuilder storeGuidResult = builderResult
                .actuallyRefundFee(new BigDecimal("2.3"))
                .brandGuid("1234")
                .goodsGuid("1234")
                .storeGuid("1234");
        TotalStatisticsDTO.TotalStatisticsDTOBuilder totalMoneyResult = storeGuidResult.totalMoney(new BigDecimal("2.3"));
        TotalStatisticsDTO buildResult = totalMoneyResult.totalQuantity(new BigDecimal("2.3")).build();
        when(tradeFreeMapper.statistics(Mockito.<ReportQueryVO>any())).thenReturn(buildResult);
        when(tradeFreeMapper.count(Mockito.<ReportQueryVO>any())).thenReturn(3);
        OssClient ossClient = mock(OssClient.class);
        when(ossClient.upload(Mockito.<String>any(), Mockito.<byte[]>any())).thenReturn("Upload");
        TradeFreeServiceImpl tradeFreeServiceImpl = new TradeFreeServiceImpl(tradeFreeMapper, ossClient);

        ReportQueryVO query = new ReportQueryVO();
        query.setBrandGuid("1234");
        query.setCateringType(1);
        query.setCurrentPage(1);
        query.setEndTime(LocalDate.of(1970, 1, 1));
        query.setEnterpriseGuid("1234");
        query.setGoodsCategories("Goods Categories");
        query.setGoodsType(1);
        query.setItemName("Item Name");
        query.setOperatorNode(1);
        query.setPageSize(3);
        query.setStartTime(LocalDate.of(1970, 1, 1));
        query.setStoreGuids(new ArrayList<>());
        String actualExportResult = tradeFreeServiceImpl.export(query);
        verify(ossClient).upload(Mockito.<String>any(), Mockito.<byte[]>any());
        verify(tradeFreeMapper).count(Mockito.<ReportQueryVO>any());
        verify(tradeFreeMapper).pageInfo(Mockito.<ReportQueryVO>any());
        verify(tradeFreeMapper).statistics(Mockito.<ReportQueryVO>any());
        assertEquals("Upload", actualExportResult);
        assertEquals(20000, query.getPageSize().intValue());
    }

    /**
     * Method under test: {@link TradeFreeServiceImpl#export(ReportQueryVO)}
     */
    @Test
    public void testExport4() {
        //   Diffblue Cover was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R013 No inputs found that don't throw a trivial exception.
        //   Diffblue Cover tried to run the arrange/act section, but the method under
        //   test threw
        //   java.lang.NullPointerException
        //       at com.holderzone.holder.saas.store.report.service.impl.TradeFreeServiceImpl.export(TradeFreeServiceImpl.java:88)
        //   See https://diff.blue/R013 to resolve this issue.

        FreeItemDTO freeItemDTO = new FreeItemDTO();
        freeItemDTO.setBrandName("UUU/UUU");
        freeItemDTO.setGivenQuantity(new BigDecimal("2.3"));
        freeItemDTO.setGivenRate("UUU/UUU");
        freeItemDTO.setGivenRefund(new BigDecimal("2.3"));
        freeItemDTO.setGoodsCategories("UUU/UUU");
        freeItemDTO.setGoodsGuid("1234");
        freeItemDTO.setGoodsName("UUU/UUU");
        freeItemDTO.setSkuGuid("1234");
        freeItemDTO.setStoreName("UUU/UUU");

        FreeItemDTO freeItemDTO2 = new FreeItemDTO();
        freeItemDTO2.setBrandName("\\x");
        freeItemDTO2.setGivenQuantity(new BigDecimal("2.3"));
        freeItemDTO2.setGivenRate("\\x");
        freeItemDTO2.setGivenRefund(new BigDecimal("2.3"));
        freeItemDTO2.setGoodsCategories("\\x");
        freeItemDTO2.setGoodsGuid("UUU/UUU");
        freeItemDTO2.setGoodsName("\\x");
        freeItemDTO2.setSkuGuid("UUU/UUU");
        freeItemDTO2.setStoreName("\\x");

        ArrayList<FreeItemDTO> freeItemDTOList = new ArrayList<>();
        freeItemDTOList.add(freeItemDTO2);
        freeItemDTOList.add(freeItemDTO);
        TradeFreeMapper tradeFreeMapper = mock(TradeFreeMapper.class);
        when(tradeFreeMapper.pageInfo(Mockito.<ReportQueryVO>any())).thenReturn(freeItemDTOList);
        TotalStatisticsDTO.TotalStatisticsDTOBuilder builderResult = TotalStatisticsDTO.builder();
        TotalStatisticsDTO.TotalStatisticsDTOBuilder storeGuidResult = builderResult
                .actuallyRefundFee(new BigDecimal("2.3"))
                .brandGuid("1234")
                .goodsGuid("1234")
                .storeGuid("1234");
        TotalStatisticsDTO.TotalStatisticsDTOBuilder totalMoneyResult = storeGuidResult.totalMoney(new BigDecimal("2.3"));
        TotalStatisticsDTO buildResult = totalMoneyResult.totalQuantity(new BigDecimal("2.3")).build();
        when(tradeFreeMapper.statistics(Mockito.<ReportQueryVO>any())).thenReturn(buildResult);
        when(tradeFreeMapper.count(Mockito.<ReportQueryVO>any())).thenReturn(3);
        OssClient ossClient = mock(OssClient.class);
        when(ossClient.upload(Mockito.<String>any(), Mockito.<byte[]>any())).thenReturn("Upload");
        TradeFreeServiceImpl tradeFreeServiceImpl = new TradeFreeServiceImpl(tradeFreeMapper, ossClient);

        ReportQueryVO query = new ReportQueryVO();
        query.setBrandGuid("1234");
        query.setCateringType(1);
        query.setCurrentPage(1);
        query.setEndTime(LocalDate.of(1970, 1, 1));
        query.setEnterpriseGuid("1234");
        query.setGoodsCategories("Goods Categories");
        query.setGoodsType(1);
        query.setItemName("Item Name");
        query.setOperatorNode(1);
        query.setPageSize(3);
        query.setStartTime(LocalDate.of(1970, 1, 1));
        query.setStoreGuids(new ArrayList<>());
        String actualExportResult = tradeFreeServiceImpl.export(query);
        verify(ossClient).upload(Mockito.<String>any(), Mockito.<byte[]>any());
        verify(tradeFreeMapper).count(Mockito.<ReportQueryVO>any());
        verify(tradeFreeMapper).pageInfo(Mockito.<ReportQueryVO>any());
        verify(tradeFreeMapper).statistics(Mockito.<ReportQueryVO>any());
        assertEquals("Upload", actualExportResult);
        assertEquals(20000, query.getPageSize().intValue());
    }
}
