package com.holderzone.holder.saas.store.pay.service.rpc.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.holder.saas.store.pay.entity.HandlerPayBO;
import com.holderzone.holder.saas.store.pay.entity.domain.PayRecordDO;
import com.holderzone.holder.saas.store.pay.mapstruct.AggPayMapstruct;
import com.holderzone.holder.saas.store.pay.service.PayRecordService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.pay.*;
import com.holderzone.saas.store.dto.trade.BaseInfo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SaasResultRpcServiceImplTest {

    @Mock
    private RestTemplate mockRestTemplate;
    @Mock
    private AggPayMapstruct mockAggPayMapStruct;
    @Mock
    private PayRecordService mockPayRecordService;

    private SaasResultRpcServiceImpl saasResultRpcServiceImplUnderTest;

    @Before
    public void setUp() {
        saasResultRpcServiceImplUnderTest = new SaasResultRpcServiceImpl(mockRestTemplate, mockAggPayMapStruct,
                mockPayRecordService);
    }

    @Test
    public void testHandlePayResult() {
        // Setup
        final HandlerPayBO handlerPayBO = HandlerPayBO.builder()
                .baseInfo(new BaseInfo())
                .isQuickReceipt(false)
                .innerCallBackUrl("callBackUrl")
                .build();
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setPayPowerId("payPowerId");
        pollingRespDTO.setPaySt("paySt");
        pollingRespDTO.setOrderGUID("075b68ea-5663-4092-b6bc-b83db0ba8171");
        pollingRespDTO.setTimePaid("timePaid");
        pollingRespDTO.setAttachData("attachData");
        pollingRespDTO.setIsLast(false);

        // Configure AggPayMapstruct.createPayRecord(...).
        final PayRecordDO payRecordDO = new PayRecordDO();
        payRecordDO.setStoreGuid("storeGuid");
        payRecordDO.setPayPowerName("payPowerName");
        payRecordDO.setPayGuid("payGuid");
        payRecordDO.setAmount(new BigDecimal("0.00"));
        payRecordDO.setOrderHolderNo("orderHolderNo");
        payRecordDO.setPaySt("4");
        payRecordDO.setGmtTimePaid(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        payRecordDO.setRefOrderNo("refOrderno");
        payRecordDO.setGmtRefund(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final AggPayPollingRespDTO pollingRespDTO1 = new AggPayPollingRespDTO();
        pollingRespDTO1.setAmount(new BigDecimal("0.00"));
        pollingRespDTO1.setPayPowerId("payPowerId");
        pollingRespDTO1.setPaySt("paySt");
        pollingRespDTO1.setOrderGUID("075b68ea-5663-4092-b6bc-b83db0ba8171");
        pollingRespDTO1.setTimePaid("timePaid");
        pollingRespDTO1.setAttachData("attachData");
        pollingRespDTO1.setIsLast(false);
        when(mockAggPayMapStruct.createPayRecord(pollingRespDTO1)).thenReturn(payRecordDO);

        when(mockPayRecordService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure AggPayMapstruct.createUserInfo(...).
        final UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("enterpriseGuid");
        userInfoDTO.setEnterpriseName("enterpriseName");
        userInfoDTO.setEnterpriseNo("enterpriseNo");
        userInfoDTO.setStoreGuid("storeGuid");
        userInfoDTO.setStoreName("storeName");
        final BaseDTO baseInfo = new BaseDTO();
        baseInfo.setDeviceType(0);
        baseInfo.setDeviceId("deviceId");
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setEnterpriseName("enterpriseName");
        baseInfo.setStoreGuid("storeGuid");
        when(mockAggPayMapStruct.createUserInfo(baseInfo)).thenReturn(userInfoDTO);

        // Configure RestTemplate.exchange(...).
        final SaasNotifyDTO saasNotifyDTO = new SaasNotifyDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setPayPowerId("payPowerId");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("075b68ea-5663-4092-b6bc-b83db0ba8171");
        aggPayPollingRespDTO.setTimePaid("timePaid");
        aggPayPollingRespDTO.setAttachData("attachData");
        aggPayPollingRespDTO.setIsLast(false);
        saasNotifyDTO.setAggPayPollingRespDTO(aggPayPollingRespDTO);
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setAttachData("attachData");
        final AggRefundDetailRespDTO aggRefundDetailRespDTO = new AggRefundDetailRespDTO();
        aggRefundDetailRespDTO.setRefOrderno("refOrderno");
        aggRefundDetailRespDTO.setStatus("status");
        aggRefundPollingRespDTO.setRefundOrderDetial(Arrays.asList(aggRefundDetailRespDTO));
        saasNotifyDTO.setAggRefundPollingRespDTO(aggRefundPollingRespDTO);
        final BaseInfo baseInfo1 = new BaseInfo();
        baseInfo1.setEnterpriseGuid("enterpriseGuid");
        baseInfo1.setStoreGuid("storeGuid");
        saasNotifyDTO.setBaseInfo(baseInfo1);
        final HttpEntity<SaasNotifyDTO> requestEntity = new HttpEntity<>(saasNotifyDTO, new HttpHeaders());
        when(mockRestTemplate.exchange("callBackUrl", HttpMethod.POST, requestEntity,
                new ParameterizedTypeReference<String>() {})).thenReturn(new ResponseEntity<>("body", HttpStatus.OK));

        // Run the test
        saasResultRpcServiceImplUnderTest.handlePayResult(handlerPayBO, pollingRespDTO);

        // Verify the results
        // Confirm PayRecordService.save(...).
        final PayRecordDO entity = new PayRecordDO();
        entity.setStoreGuid("storeGuid");
        entity.setPayPowerName("payPowerName");
        entity.setPayGuid("payGuid");
        entity.setAmount(new BigDecimal("0.00"));
        entity.setOrderHolderNo("orderHolderNo");
        entity.setPaySt("4");
        entity.setGmtTimePaid(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setRefOrderNo("refOrderno");
        entity.setGmtRefund(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockPayRecordService).save(entity);
    }

    @Test
    public void testHandlePayResult_RestTemplateThrowsRestClientException() {
        // Setup
        final HandlerPayBO handlerPayBO = HandlerPayBO.builder()
                .baseInfo(new BaseInfo())
                .isQuickReceipt(false)
                .innerCallBackUrl("callBackUrl")
                .build();
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setAmount(new BigDecimal("0.00"));
        pollingRespDTO.setPayPowerId("payPowerId");
        pollingRespDTO.setPaySt("paySt");
        pollingRespDTO.setOrderGUID("075b68ea-5663-4092-b6bc-b83db0ba8171");
        pollingRespDTO.setTimePaid("timePaid");
        pollingRespDTO.setAttachData("attachData");
        pollingRespDTO.setIsLast(false);

        // Configure AggPayMapstruct.createUserInfo(...).
        final UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("enterpriseGuid");
        userInfoDTO.setEnterpriseName("enterpriseName");
        userInfoDTO.setEnterpriseNo("enterpriseNo");
        userInfoDTO.setStoreGuid("storeGuid");
        userInfoDTO.setStoreName("storeName");
        final BaseDTO baseInfo = new BaseDTO();
        baseInfo.setDeviceType(0);
        baseInfo.setDeviceId("deviceId");
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setEnterpriseName("enterpriseName");
        baseInfo.setStoreGuid("storeGuid");
        when(mockAggPayMapStruct.createUserInfo(baseInfo)).thenReturn(userInfoDTO);

        // Configure RestTemplate.exchange(...).
        final SaasNotifyDTO saasNotifyDTO = new SaasNotifyDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setPayPowerId("payPowerId");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("075b68ea-5663-4092-b6bc-b83db0ba8171");
        aggPayPollingRespDTO.setTimePaid("timePaid");
        aggPayPollingRespDTO.setAttachData("attachData");
        aggPayPollingRespDTO.setIsLast(false);
        saasNotifyDTO.setAggPayPollingRespDTO(aggPayPollingRespDTO);
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setAttachData("attachData");
        final AggRefundDetailRespDTO aggRefundDetailRespDTO = new AggRefundDetailRespDTO();
        aggRefundDetailRespDTO.setRefOrderno("refOrderno");
        aggRefundDetailRespDTO.setStatus("status");
        aggRefundPollingRespDTO.setRefundOrderDetial(Arrays.asList(aggRefundDetailRespDTO));
        saasNotifyDTO.setAggRefundPollingRespDTO(aggRefundPollingRespDTO);
        final BaseInfo baseInfo1 = new BaseInfo();
        baseInfo1.setEnterpriseGuid("enterpriseGuid");
        baseInfo1.setStoreGuid("storeGuid");
        saasNotifyDTO.setBaseInfo(baseInfo1);
        final HttpEntity<SaasNotifyDTO> requestEntity = new HttpEntity<>(saasNotifyDTO, new HttpHeaders());
        when(mockRestTemplate.exchange("callBackUrl", HttpMethod.POST, requestEntity,
                new ParameterizedTypeReference<RestClientException>() {})).thenThrow(RestClientException.class);

        // Run the test
        assertThatThrownBy(
                () -> saasResultRpcServiceImplUnderTest.handlePayResult(handlerPayBO, pollingRespDTO))
                .isInstanceOf(RuntimeException.class);
    }

    @Test
    public void testHandleRefundResult() {
        // Setup
        final SaasAggRefundDTO saasAggRefundDTO = new SaasAggRefundDTO();
        saasAggRefundDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggRefundDTO.setStoreGuid("storeGuid");
        final AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
        saasAggRefundDTO.setAggRefundReqDTO(aggRefundReqDTO);
        saasAggRefundDTO.setIsQuickReceipt(false);
        saasAggRefundDTO.setSaasCallBackUrl("callBackUrl");

        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setAttachData("attachData");
        final AggRefundDetailRespDTO aggRefundDetailRespDTO = new AggRefundDetailRespDTO();
        aggRefundDetailRespDTO.setRefOrderno("refOrderno");
        aggRefundDetailRespDTO.setStatus("status");
        aggRefundPollingRespDTO.setRefundOrderDetial(Arrays.asList(aggRefundDetailRespDTO));

        // Configure AggPayMapstruct.createBaseInfo(...).
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setTel("tel");
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockAggPayMapStruct.createBaseInfo(baseDTO)).thenReturn(baseInfo);

        // Configure AggPayMapstruct.createUserInfo(...).
        final UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("enterpriseGuid");
        userInfoDTO.setEnterpriseName("enterpriseName");
        userInfoDTO.setEnterpriseNo("enterpriseNo");
        userInfoDTO.setStoreGuid("storeGuid");
        userInfoDTO.setStoreName("storeName");
        final BaseDTO baseInfo1 = new BaseDTO();
        baseInfo1.setDeviceType(0);
        baseInfo1.setDeviceId("deviceId");
        baseInfo1.setEnterpriseGuid("enterpriseGuid");
        baseInfo1.setEnterpriseName("enterpriseName");
        baseInfo1.setStoreGuid("storeGuid");
        when(mockAggPayMapStruct.createUserInfo(baseInfo1)).thenReturn(userInfoDTO);

        // Configure RestTemplate.exchange(...).
        final SaasNotifyDTO saasNotifyDTO = new SaasNotifyDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setPayPowerId("payPowerId");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("075b68ea-5663-4092-b6bc-b83db0ba8171");
        aggPayPollingRespDTO.setTimePaid("timePaid");
        aggPayPollingRespDTO.setAttachData("attachData");
        aggPayPollingRespDTO.setIsLast(false);
        saasNotifyDTO.setAggPayPollingRespDTO(aggPayPollingRespDTO);
        final AggRefundPollingRespDTO aggRefundPollingRespDTO1 = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO1.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO1.setAttachData("attachData");
        final AggRefundDetailRespDTO aggRefundDetailRespDTO1 = new AggRefundDetailRespDTO();
        aggRefundDetailRespDTO1.setRefOrderno("refOrderno");
        aggRefundDetailRespDTO1.setStatus("status");
        aggRefundPollingRespDTO1.setRefundOrderDetial(Arrays.asList(aggRefundDetailRespDTO1));
        saasNotifyDTO.setAggRefundPollingRespDTO(aggRefundPollingRespDTO1);
        final BaseInfo baseInfo2 = new BaseInfo();
        baseInfo2.setEnterpriseGuid("enterpriseGuid");
        baseInfo2.setStoreGuid("storeGuid");
        saasNotifyDTO.setBaseInfo(baseInfo2);
        final HttpEntity<SaasNotifyDTO> requestEntity = new HttpEntity<>(saasNotifyDTO, new HttpHeaders());
        when(mockRestTemplate.exchange("callBackUrl", HttpMethod.POST, requestEntity,
                new ParameterizedTypeReference<String>() {})).thenReturn(new ResponseEntity<>("body", HttpStatus.OK));

        // Run the test
        saasResultRpcServiceImplUnderTest.handleRefundResult(saasAggRefundDTO, aggRefundPollingRespDTO);

        // Verify the results
        // Confirm PayRecordService.update(...).
        final PayRecordDO entity = new PayRecordDO();
        entity.setStoreGuid("storeGuid");
        entity.setPayPowerName("payPowerName");
        entity.setPayGuid("payGuid");
        entity.setAmount(new BigDecimal("0.00"));
        entity.setOrderHolderNo("orderHolderNo");
        entity.setPaySt("4");
        entity.setGmtTimePaid(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        entity.setRefOrderNo("refOrderno");
        entity.setGmtRefund(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockPayRecordService).update(eq(entity), any(LambdaQueryWrapper.class));
    }

    @Test
    public void testHandleRefundResult_RestTemplateThrowsRestClientException() {
        // Setup
        final SaasAggRefundDTO saasAggRefundDTO = new SaasAggRefundDTO();
        saasAggRefundDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggRefundDTO.setStoreGuid("storeGuid");
        final AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
        saasAggRefundDTO.setAggRefundReqDTO(aggRefundReqDTO);
        saasAggRefundDTO.setIsQuickReceipt(false);
        saasAggRefundDTO.setSaasCallBackUrl("callBackUrl");

        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setAttachData("attachData");
        final AggRefundDetailRespDTO aggRefundDetailRespDTO = new AggRefundDetailRespDTO();
        aggRefundDetailRespDTO.setRefOrderno("refOrderno");
        aggRefundDetailRespDTO.setStatus("status");
        aggRefundPollingRespDTO.setRefundOrderDetial(Arrays.asList(aggRefundDetailRespDTO));

        // Configure AggPayMapstruct.createBaseInfo(...).
        final BaseInfo baseInfo = new BaseInfo();
        baseInfo.setEnterpriseGuid("enterpriseGuid");
        baseInfo.setStoreGuid("storeGuid");
        baseInfo.setTel("tel");
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockAggPayMapStruct.createBaseInfo(baseDTO)).thenReturn(baseInfo);

        // Configure AggPayMapstruct.createUserInfo(...).
        final UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setEnterpriseGuid("enterpriseGuid");
        userInfoDTO.setEnterpriseName("enterpriseName");
        userInfoDTO.setEnterpriseNo("enterpriseNo");
        userInfoDTO.setStoreGuid("storeGuid");
        userInfoDTO.setStoreName("storeName");
        final BaseDTO baseInfo1 = new BaseDTO();
        baseInfo1.setDeviceType(0);
        baseInfo1.setDeviceId("deviceId");
        baseInfo1.setEnterpriseGuid("enterpriseGuid");
        baseInfo1.setEnterpriseName("enterpriseName");
        baseInfo1.setStoreGuid("storeGuid");
        when(mockAggPayMapStruct.createUserInfo(baseInfo1)).thenReturn(userInfoDTO);

        // Configure RestTemplate.exchange(...).
        final SaasNotifyDTO saasNotifyDTO = new SaasNotifyDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setPayPowerId("payPowerId");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("075b68ea-5663-4092-b6bc-b83db0ba8171");
        aggPayPollingRespDTO.setTimePaid("timePaid");
        aggPayPollingRespDTO.setAttachData("attachData");
        aggPayPollingRespDTO.setIsLast(false);
        saasNotifyDTO.setAggPayPollingRespDTO(aggPayPollingRespDTO);
        final AggRefundPollingRespDTO aggRefundPollingRespDTO1 = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO1.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO1.setAttachData("attachData");
        final AggRefundDetailRespDTO aggRefundDetailRespDTO1 = new AggRefundDetailRespDTO();
        aggRefundDetailRespDTO1.setRefOrderno("refOrderno");
        aggRefundDetailRespDTO1.setStatus("status");
        aggRefundPollingRespDTO1.setRefundOrderDetial(Arrays.asList(aggRefundDetailRespDTO1));
        saasNotifyDTO.setAggRefundPollingRespDTO(aggRefundPollingRespDTO1);
        final BaseInfo baseInfo2 = new BaseInfo();
        baseInfo2.setEnterpriseGuid("enterpriseGuid");
        baseInfo2.setStoreGuid("storeGuid");
        saasNotifyDTO.setBaseInfo(baseInfo2);
        final HttpEntity<SaasNotifyDTO> requestEntity = new HttpEntity<>(saasNotifyDTO, new HttpHeaders());
        when(mockRestTemplate.exchange("callBackUrl", HttpMethod.POST, requestEntity,
                new ParameterizedTypeReference<RestClientException>() {})).thenThrow(RestClientException.class);

        // Run the test
        saasResultRpcServiceImplUnderTest.handleRefundResult(saasAggRefundDTO, aggRefundPollingRespDTO);

        // Verify the results
    }
}
