package com.holderzone.holder.saas.store.table.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.store.table.client.TradeRpcClient;
import com.holderzone.holder.saas.store.table.domain.TableOrderDO;
import com.holderzone.holder.saas.store.table.mapper.TableOrderMapper;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.ReserveBatchCreateOrderReqDTO;
import com.holderzone.saas.store.dto.table.TableCombineDTO;
import com.holderzone.saas.store.dto.table.TableInfoDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.trade.TradeTableDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TradeServiceImplTest {

    @Mock
    private TradeRpcClient mockTradeRpcClient;
    @Mock
    private TableOrderMapper mockTableOrderMapper;

    private TradeServiceImpl tradeServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        tradeServiceImplUnderTest = new TradeServiceImpl(mockTradeRpcClient, mockTableOrderMapper);
    }

    @Test
    public void testCloseOrder() {
        // Setup
        final CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setReason("reason");
        cancelOrderReqDTO.setTable(false);
        cancelOrderReqDTO.setFastFood(false);
        cancelOrderReqDTO.setIsCalc(0);

        // Configure TradeRpcClient.tradeClient(...).
        final CancelOrderReqDTO cancelOrderReqDTO1 = new CancelOrderReqDTO();
        cancelOrderReqDTO1.setReason("reason");
        cancelOrderReqDTO1.setTable(false);
        cancelOrderReqDTO1.setFastFood(false);
        cancelOrderReqDTO1.setIsCalc(0);
        when(mockTradeRpcClient.tradeClient(cancelOrderReqDTO1)).thenReturn(true);

        // Run the test
        tradeServiceImplUnderTest.closeOrder(cancelOrderReqDTO);

        // Verify the results
    }

    @Test
    public void testCloseOrder_TradeRpcClientReturnsFalse() {
        // Setup
        final CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setReason("reason");
        cancelOrderReqDTO.setTable(false);
        cancelOrderReqDTO.setFastFood(false);
        cancelOrderReqDTO.setIsCalc(0);

        // Configure TradeRpcClient.tradeClient(...).
        final CancelOrderReqDTO cancelOrderReqDTO1 = new CancelOrderReqDTO();
        cancelOrderReqDTO1.setReason("reason");
        cancelOrderReqDTO1.setTable(false);
        cancelOrderReqDTO1.setFastFood(false);
        cancelOrderReqDTO1.setIsCalc(0);
        when(mockTradeRpcClient.tradeClient(cancelOrderReqDTO1)).thenReturn(false);

        // Run the test
        assertThatThrownBy(() -> tradeServiceImplUnderTest.closeOrder(cancelOrderReqDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testNotifyTradeCombine() {
        // Setup
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setTableName("tableName");
        tableInfoDTO.setAreaGuid("areaGuid");
        tableInfoDTO.setAreaName("areaName");
        tableInfoDTO.setSort(0);
        tableInfoDTO.setIsBind(false);
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);

        // Configure TradeRpcClient.notifyTradeCombine(...).
        final TableInfoDTO tableInfoDTO1 = new TableInfoDTO();
        tableInfoDTO1.setTableName("tableName");
        tableInfoDTO1.setAreaGuid("areaGuid");
        tableInfoDTO1.setAreaName("areaName");
        tableInfoDTO1.setSort(0);
        tableInfoDTO1.setIsBind(false);
        final TableOrderCombineDTO tableOrderCombineDTO1 = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO1),
                "mainOrderGuid", "mainTableGuid", true);
        when(mockTradeRpcClient.notifyTradeCombine(tableOrderCombineDTO1)).thenReturn(true);

        // Run the test
        tradeServiceImplUnderTest.notifyTradeCombine(tableOrderCombineDTO);

        // Verify the results
    }

    @Test
    public void testNotifyTradeCombine_TradeRpcClientReturnsFalse() {
        // Setup
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setTableName("tableName");
        tableInfoDTO.setAreaGuid("areaGuid");
        tableInfoDTO.setAreaName("areaName");
        tableInfoDTO.setSort(0);
        tableInfoDTO.setIsBind(false);
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);

        // Configure TradeRpcClient.notifyTradeCombine(...).
        final TableInfoDTO tableInfoDTO1 = new TableInfoDTO();
        tableInfoDTO1.setTableName("tableName");
        tableInfoDTO1.setAreaGuid("areaGuid");
        tableInfoDTO1.setAreaName("areaName");
        tableInfoDTO1.setSort(0);
        tableInfoDTO1.setIsBind(false);
        final TableOrderCombineDTO tableOrderCombineDTO1 = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO1),
                "mainOrderGuid", "mainTableGuid", true);
        when(mockTradeRpcClient.notifyTradeCombine(tableOrderCombineDTO1)).thenReturn(false);

        // Run the test
        assertThatThrownBy(() -> tradeServiceImplUnderTest.notifyTradeCombine(tableOrderCombineDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSeparate() {
        // Setup
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setTableName("tableName");
        tableInfoDTO.setAreaGuid("areaGuid");
        tableInfoDTO.setAreaName("areaName");
        tableInfoDTO.setSort(0);
        tableInfoDTO.setIsBind(false);
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);

        // Configure TradeRpcClient.split(...).
        final TableInfoDTO tableInfoDTO1 = new TableInfoDTO();
        tableInfoDTO1.setTableName("tableName");
        tableInfoDTO1.setAreaGuid("areaGuid");
        tableInfoDTO1.setAreaName("areaName");
        tableInfoDTO1.setSort(0);
        tableInfoDTO1.setIsBind(false);
        final TableOrderCombineDTO tableOrderCombineDTO1 = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO1),
                "mainOrderGuid", "mainTableGuid", true);
        when(mockTradeRpcClient.split(tableOrderCombineDTO1)).thenReturn(true);

        // Run the test
        tradeServiceImplUnderTest.separate(tableOrderCombineDTO);

        // Verify the results
    }

    @Test
    public void testSeparate_TradeRpcClientReturnsFalse() {
        // Setup
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setTableName("tableName");
        tableInfoDTO.setAreaGuid("areaGuid");
        tableInfoDTO.setAreaName("areaName");
        tableInfoDTO.setSort(0);
        tableInfoDTO.setIsBind(false);
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);

        // Configure TradeRpcClient.split(...).
        final TableInfoDTO tableInfoDTO1 = new TableInfoDTO();
        tableInfoDTO1.setTableName("tableName");
        tableInfoDTO1.setAreaGuid("areaGuid");
        tableInfoDTO1.setAreaName("areaName");
        tableInfoDTO1.setSort(0);
        tableInfoDTO1.setIsBind(false);
        final TableOrderCombineDTO tableOrderCombineDTO1 = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO1),
                "mainOrderGuid", "mainTableGuid", true);
        when(mockTradeRpcClient.split(tableOrderCombineDTO1)).thenReturn(false);

        // Run the test
        assertThatThrownBy(() -> tradeServiceImplUnderTest.separate(tableOrderCombineDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testOpenTable() {
        // Setup
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setGuid("guid");
        createDineInOrderReqDTO.setUserWxPublicOpenId("userWxPublicOpenId");
        createDineInOrderReqDTO.setRemark("remark");
        createDineInOrderReqDTO.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO.setDiningTableName("diningTableName");

        // Configure TradeRpcClient.openTable(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO1 = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO1.setGuid("guid");
        createDineInOrderReqDTO1.setUserWxPublicOpenId("userWxPublicOpenId");
        createDineInOrderReqDTO1.setRemark("remark");
        createDineInOrderReqDTO1.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO1.setDiningTableName("diningTableName");
        final CreateDineInOrderReqDTO createDineInOrderReqDTO2 = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO2.setGuid("guid");
        createDineInOrderReqDTO2.setUserWxPublicOpenId("userWxPublicOpenId");
        createDineInOrderReqDTO2.setRemark("remark");
        createDineInOrderReqDTO2.setDiningTableGuid("diningTableGuid");
        createDineInOrderReqDTO2.setDiningTableName("diningTableName");
        when(mockTradeRpcClient.openTable(createDineInOrderReqDTO2)).thenReturn(createDineInOrderReqDTO1);

        // Run the test
        final String result = tradeServiceImplUnderTest.openTable(createDineInOrderReqDTO);

        // Verify the results
        assertThat(result).isEqualTo("guid");
    }

    @Test
    public void testBacthopenTable() {
        // Setup
        final ReserveBatchCreateOrderReqDTO batchCreateOrderReqDTO = new ReserveBatchCreateOrderReqDTO();
        batchCreateOrderReqDTO.setReserveGuid("reserveGuid");
        batchCreateOrderReqDTO.setMainOrderGuid("mainOrderGuid");
        batchCreateOrderReqDTO.setContainDish(false);
        batchCreateOrderReqDTO.setReserveFee(new BigDecimal("0.00"));
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        batchCreateOrderReqDTO.setCreateDineInOrderReqDTOS(Arrays.asList(createDineInOrderReqDTO));

        // Configure TradeRpcClient.batchCreateOrder(...).
        final ReserveBatchCreateOrderReqDTO reserveBatchCreateOrderReqDTO = new ReserveBatchCreateOrderReqDTO();
        reserveBatchCreateOrderReqDTO.setReserveGuid("reserveGuid");
        reserveBatchCreateOrderReqDTO.setMainOrderGuid("mainOrderGuid");
        reserveBatchCreateOrderReqDTO.setContainDish(false);
        reserveBatchCreateOrderReqDTO.setReserveFee(new BigDecimal("0.00"));
        final CreateDineInOrderReqDTO createDineInOrderReqDTO1 = new CreateDineInOrderReqDTO();
        reserveBatchCreateOrderReqDTO.setCreateDineInOrderReqDTOS(Arrays.asList(createDineInOrderReqDTO1));
        when(mockTradeRpcClient.batchCreateOrder(reserveBatchCreateOrderReqDTO)).thenReturn("result");

        // Run the test
        final String result = tradeServiceImplUnderTest.bacthopenTable(batchCreateOrderReqDTO);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testNotifyTradeTurn() {
        // Setup
        final TradeTableDTO tradeTableDTO = new TradeTableDTO("tableName", "areaName", "areaGuid");
        when(mockTradeRpcClient.notifyTradeTurn(new TradeTableDTO("tableName", "areaName", "areaGuid"))).thenReturn(true);

        // Run the test
        tradeServiceImplUnderTest.notifyTradeTurn(tradeTableDTO);

        // Verify the results
    }

    @Test
    public void testNotifyTradeTurn_TradeRpcClientReturnsFalse() {
        // Setup
        final TradeTableDTO tradeTableDTO = new TradeTableDTO("tableName", "areaName", "areaGuid");
        when(mockTradeRpcClient.notifyTradeTurn(new TradeTableDTO("tableName", "areaName", "areaGuid"))).thenReturn(false);

        // Run the test
        assertThatThrownBy(() -> tradeServiceImplUnderTest.notifyTradeTurn(tradeTableDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testBatchRevokeThirdActivity() {
        // Setup
        final TableCombineDTO tableCombineDTO = new TableCombineDTO();
        tableCombineDTO.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO.setCombineTimes(0);
        tableCombineDTO.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO.setMainTableGuid("mainTableGuid");

        // Configure TableOrderMapper.selectList(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setGuid("ecc2e6e6-3810-485c-a8d3-92791bfbd67b");
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("orderGuid");
        final List<TableOrderDO> tableOrderDOS = Arrays.asList(tableOrderDO);
        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDOS);

        // Run the test
        tradeServiceImplUnderTest.batchRevokeThirdActivity(tableCombineDTO);

        // Verify the results
        verify(mockTradeRpcClient).batchRevokeThirdActivityRecord(new SingleDataDTO("data", Arrays.asList("value")));
    }

    @Test
    public void testBatchRevokeThirdActivity_TableOrderMapperReturnsNoItems() {
        // Setup
        final TableCombineDTO tableCombineDTO = new TableCombineDTO();
        tableCombineDTO.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO.setCombineTimes(0);
        tableCombineDTO.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO.setMainTableGuid("mainTableGuid");

        when(mockTableOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        tradeServiceImplUnderTest.batchRevokeThirdActivity(tableCombineDTO);

        // Verify the results
    }
}
