package com.holderzone.holder.saas.store.report.service.impl;

import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.holder.saas.store.report.mapper.TradeFreeMapper;
import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.FreeItemDTO;
import com.holderzone.saas.store.dto.report.resp.TotalStatisticsDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TradeFreeServiceImplTest {

    @Mock
    private TradeFreeMapper mockTradeFreeMapper;
    @Mock
    private OssClient mockOssClient;

    private TradeFreeServiceImpl tradeFreeServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        tradeFreeServiceImplUnderTest = new TradeFreeServiceImpl(mockTradeFreeMapper, mockOssClient);
    }

    @Test
    public void testList() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeFreeMapper.statistics(...).
        final TotalStatisticsDTO totalStatisticsDTO = new TotalStatisticsDTO("brandGuid", "storeGuid", "goodsGuid",
                new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"));
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeFreeMapper.statistics(query1)).thenReturn(totalStatisticsDTO);

        // Configure TradeFreeMapper.count(...).
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeFreeMapper.count(query2)).thenReturn(0);

        // Configure TradeFreeMapper.pageInfo(...).
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        freeItemDTO.setBrandName("brandName");
        freeItemDTO.setStoreName("storeName");
        freeItemDTO.setGoodsName("goodsName");
        freeItemDTO.setGivenQuantity(new BigDecimal("0.00"));
        freeItemDTO.setGivenRate("givenRate");
        final List<FreeItemDTO> freeItemDTOS = Arrays.asList(freeItemDTO);
        final ReportQueryVO query3 = new ReportQueryVO();
        query3.setCurrentPage(0);
        query3.setPageSize(0);
        query3.setStartTime(LocalDate.of(2020, 1, 1));
        query3.setEndTime(LocalDate.of(2020, 1, 1));
        query3.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeFreeMapper.pageInfo(query3)).thenReturn(freeItemDTOS);

        // Run the test
        final Message<FreeItemDTO> result = tradeFreeServiceImplUnderTest.list(query);

        // Verify the results
    }

    @Test
    public void testList_TradeFreeMapperPageInfoReturnsNoItems() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeFreeMapper.statistics(...).
        final TotalStatisticsDTO totalStatisticsDTO = new TotalStatisticsDTO("brandGuid", "storeGuid", "goodsGuid",
                new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"));
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeFreeMapper.statistics(query1)).thenReturn(totalStatisticsDTO);

        // Configure TradeFreeMapper.count(...).
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeFreeMapper.count(query2)).thenReturn(0);

        // Configure TradeFreeMapper.pageInfo(...).
        final ReportQueryVO query3 = new ReportQueryVO();
        query3.setCurrentPage(0);
        query3.setPageSize(0);
        query3.setStartTime(LocalDate.of(2020, 1, 1));
        query3.setEndTime(LocalDate.of(2020, 1, 1));
        query3.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeFreeMapper.pageInfo(query3)).thenReturn(Collections.emptyList());

        // Run the test
        final Message<FreeItemDTO> result = tradeFreeServiceImplUnderTest.list(query);

        // Verify the results
    }

    @Test
    public void testExport() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeFreeMapper.count(...).
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeFreeMapper.count(query1)).thenReturn(0);

        // Configure TradeFreeMapper.statistics(...).
        final TotalStatisticsDTO totalStatisticsDTO = new TotalStatisticsDTO("brandGuid", "storeGuid", "goodsGuid",
                new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"));
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeFreeMapper.statistics(query2)).thenReturn(totalStatisticsDTO);

        // Configure TradeFreeMapper.pageInfo(...).
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        freeItemDTO.setBrandName("brandName");
        freeItemDTO.setStoreName("storeName");
        freeItemDTO.setGoodsName("goodsName");
        freeItemDTO.setGivenQuantity(new BigDecimal("0.00"));
        freeItemDTO.setGivenRate("givenRate");
        final List<FreeItemDTO> freeItemDTOS = Arrays.asList(freeItemDTO);
        final ReportQueryVO query3 = new ReportQueryVO();
        query3.setCurrentPage(0);
        query3.setPageSize(0);
        query3.setStartTime(LocalDate.of(2020, 1, 1));
        query3.setEndTime(LocalDate.of(2020, 1, 1));
        query3.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeFreeMapper.pageInfo(query3)).thenReturn(freeItemDTOS);

        // Run the test
        final String result = tradeFreeServiceImplUnderTest.export(query);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testExport_TradeFreeMapperPageInfoReturnsNoItems() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeFreeMapper.count(...).
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeFreeMapper.count(query1)).thenReturn(0);

        // Configure TradeFreeMapper.statistics(...).
        final TotalStatisticsDTO totalStatisticsDTO = new TotalStatisticsDTO("brandGuid", "storeGuid", "goodsGuid",
                new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"));
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeFreeMapper.statistics(query2)).thenReturn(totalStatisticsDTO);

        // Configure TradeFreeMapper.pageInfo(...).
        final ReportQueryVO query3 = new ReportQueryVO();
        query3.setCurrentPage(0);
        query3.setPageSize(0);
        query3.setStartTime(LocalDate.of(2020, 1, 1));
        query3.setEndTime(LocalDate.of(2020, 1, 1));
        query3.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeFreeMapper.pageInfo(query3)).thenReturn(Collections.emptyList());

        // Run the test
        final String result = tradeFreeServiceImplUnderTest.export(query);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }
}
