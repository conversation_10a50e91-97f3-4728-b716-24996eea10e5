package com.holderzone.holder.saas.store.report.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.holder.saas.store.report.config.OldCouponCalculateConfig;
import com.holderzone.holder.saas.store.report.mapper.TradeItemMapper;
import com.holderzone.holder.saas.store.report.mapper.TradeOrderMapper;
import com.holderzone.holder.saas.store.report.service.rpc.organization.OrganizationService;
import com.holderzone.saas.store.dto.journaling.req.SalesVolumeReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.SalesVolumeRespDTO;
import com.holderzone.saas.store.dto.report.query.GoodsSalesVO;
import com.holderzone.saas.store.dto.report.query.SalesDetailQO;
import com.holderzone.saas.store.dto.report.resp.GoodsSalesDTO;
import com.holderzone.saas.store.dto.report.resp.GoodsSalesStatisticDTO;
import com.holderzone.saas.store.dto.report.resp.GoodsSalesTotalDTO;
import com.holderzone.saas.store.dto.report.resp.SalesDetailRespDTO;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Executor;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class TradeItemServiceImplTest {

    public static final String ENTERPRISE_GUID = "enterpriseGuid";
    public static final String VALUE = "value";
    public static final String RESULT = "result";
    @Mock
    private TradeItemMapper mockTradeItemMapper;
    @Mock
    private TradeOrderMapper mockTradeOrderMapper;
    @Mock
    private OrganizationService mockOrganizationService;
    @Mock
    private OssClient mockOssClient;
    @Mock
    private Executor mockReportQueryExecutor;
    @Mock
    private OldCouponCalculateConfig oldCouponCalculateConfig;

    private TradeItemServiceImpl tradeItemServiceImplUnderTest;

    @Before
    public void setUp() {
        UserContext userContext = new UserContext();
        userContext.setOperSubjectGuid("2209191214425540006");
        userContext.setEnterpriseGuid("4895");
        UserContextUtils.put(userContext);

        tradeItemServiceImplUnderTest = new TradeItemServiceImpl(mockTradeItemMapper, mockTradeOrderMapper, mockOssClient,
                mockReportQueryExecutor, oldCouponCalculateConfig);
    }

    @Test
    public void testQueryItemTypeStatistics() {
        // Setup
        final GoodsSalesVO query = getGoodsSalesVO();

        final GoodsSalesStatisticDTO expectedResult = getGoodsSalesStatisticDTO();
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockReportQueryExecutor).execute(any(Runnable.class));

        // Configure TradeItemMapper.queryItemTypeStatisticsCount(...).
        final GoodsSalesVO query1 = getGoodsSalesVO();
        when(mockTradeItemMapper.queryItemTypeStatisticsCount(query1)).thenReturn(1L);

        // Configure TradeItemMapper.queryItemTypeStatistics(...).
        final Page<GoodsSalesDTO> spyPage = spy(new Page<>(1, 20, false));
        final GoodsSalesVO query2 = getGoodsSalesVO();
        when(mockTradeItemMapper.queryItemTypeStatistics(query2)).thenReturn(spyPage);

        // Configure TradeItemMapper.queryItemTypeSalesTotal(...).
        final GoodsSalesTotalDTO goodsSalesTotalDTO1 = new GoodsSalesTotalDTO();
        goodsSalesTotalDTO1.setTotalSalesVolume(new BigDecimal("4.00"));
        goodsSalesTotalDTO1.setTotalReceivedSumPrice(new BigDecimal("5.00"));
        goodsSalesTotalDTO1.setTotalDiscountPrice(new BigDecimal("3.00"));
        goodsSalesTotalDTO1.setTotalGrossProfitAmount(new BigDecimal("5.00"));
        final GoodsSalesVO query3 = getGoodsSalesVO();
        when(mockTradeItemMapper.queryItemTypeSalesTotal(query3)).thenReturn(goodsSalesTotalDTO1);

        // Run the test
        final GoodsSalesStatisticDTO result = tradeItemServiceImplUnderTest.queryItemTypeStatistics(query);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockReportQueryExecutor).execute(any(Runnable.class));
        verify(spyPage).close();
    }

    @NotNull
    private static GoodsSalesVO getGoodsSalesVO() {
        final GoodsSalesVO query1 = new GoodsSalesVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2024, 3, 12));
        query1.setEndTime(LocalDate.of(2024, 3, 12));
        query1.setStoreGuid(Collections.singletonList(VALUE));
        query1.setGoodsNames("goodsNames");
        query1.setCateringType(0);
        query1.setEnterpriseGuid(ENTERPRISE_GUID);
        query1.setTotalOrderCount(0L);
        return query1;
    }

    @NotNull
    private static GoodsSalesStatisticDTO getGoodsSalesStatisticDTO() {
        final GoodsSalesDTO goodsSalesDTO = new GoodsSalesDTO();
        goodsSalesDTO.setBrandName("brandName");
        goodsSalesDTO.setSalesVolume(new BigDecimal("0.00"));
        goodsSalesDTO.setActualReceivedPrice(new BigDecimal("0.00"));
        goodsSalesDTO.setDiscountPrice(new BigDecimal("0.00"));
        goodsSalesDTO.setGrossProfitAmount(new BigDecimal("0.00"));
        final GoodsSalesTotalDTO goodsSalesTotalDTO = getGoodsSalesTotalDTO();
        return new GoodsSalesStatisticDTO(
                new PageInfo<>(Collections.singletonList(goodsSalesDTO), 0), goodsSalesTotalDTO);
    }

    @Test
    public void testQueryItemTypeStatistics_TradeItemMapperQueryItemTypeStatisticsReturnsNoItems() {
        // Setup
        final GoodsSalesVO query = getGoodsSalesVO();

        final GoodsSalesStatisticDTO expectedResult = getGoodsSalesStatisticDTO();
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockReportQueryExecutor).execute(any(Runnable.class));

        // Configure TradeItemMapper.queryItemTypeStatisticsCount(...).
        final GoodsSalesVO query1 = getGoodsSalesVO();
        when(mockTradeItemMapper.queryItemTypeStatisticsCount(query1)).thenReturn(0L);

        // Configure TradeItemMapper.queryItemTypeStatistics(...).
        final Page<GoodsSalesDTO> spyPage = spy(new Page<>());
        final GoodsSalesVO query2 = getGoodsSalesVO();
        when(mockTradeItemMapper.queryItemTypeStatistics(query2)).thenReturn(spyPage);

        // Configure TradeItemMapper.queryItemTypeSalesTotal(...).
        final GoodsSalesTotalDTO goodsSalesTotalDTO2 = new GoodsSalesTotalDTO();
        goodsSalesTotalDTO2.setTotalSalesVolume(new BigDecimal("8.00"));
        goodsSalesTotalDTO2.setTotalReceivedSumPrice(new BigDecimal("3.00"));
        goodsSalesTotalDTO2.setTotalDiscountPrice(new BigDecimal("5.00"));
        goodsSalesTotalDTO2.setTotalGrossProfitAmount(new BigDecimal("2.00"));
        final GoodsSalesVO query3 = getGoodsSalesVO();
        when(mockTradeItemMapper.queryItemTypeSalesTotal(query3)).thenReturn(goodsSalesTotalDTO2);

        // Run the test
        final GoodsSalesStatisticDTO result = tradeItemServiceImplUnderTest.queryItemTypeStatistics(query);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockReportQueryExecutor).execute(any(Runnable.class));
        verify(spyPage).close();
    }

    @Test
    public void testQueryGroupItemSaleStatistics() {
        // Setup
        final GoodsSalesVO query = getGoodsSalesVO();

        final GoodsSalesStatisticDTO expectedResult = getGoodsSalesStatisticDTO();

        // Configure TradeOrderMapper.queryOrderCount(...).
        final SalesVolumeReqDTO queryOrderCount = getSalesVolumeReqDTO();
        when(mockTradeOrderMapper.queryOrderCount(queryOrderCount)).thenReturn(0L);

        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockReportQueryExecutor).execute(any(Runnable.class));

        // Configure TradeItemMapper.queryGroupItemSaleStatisticsCount(...).
        final GoodsSalesVO queryGroupItemSaleStatisticsCount1 = getGoodsSalesVO();
        when(mockTradeItemMapper.queryGroupItemSaleStatisticsCount(queryGroupItemSaleStatisticsCount1)).thenReturn(0L);

        // Configure TradeItemMapper.queryGroupItemSaleStatistics(...).
        final Page<GoodsSalesDTO> spyPage = spy(new Page<>(0, 0, false));
        final GoodsSalesVO queryGroupItemSaleStatistics2 = getGoodsSalesVO();
        when(mockTradeItemMapper.queryGroupItemSaleStatistics(queryGroupItemSaleStatistics2)).thenReturn(spyPage);

        // Configure TradeItemMapper.getGroupItemSaleTotal(...).
        final GoodsSalesTotalDTO goodsSalesTotalDTO3 = new GoodsSalesTotalDTO();
        goodsSalesTotalDTO3.setTotalSalesVolume(new BigDecimal("60.00"));
        goodsSalesTotalDTO3.setTotalReceivedSumPrice(new BigDecimal("40.00"));
        goodsSalesTotalDTO3.setTotalDiscountPrice(new BigDecimal("30.00"));
        goodsSalesTotalDTO3.setTotalGrossProfitAmount(new BigDecimal("50.00"));
        final GoodsSalesVO query4 = getGoodsSalesVO();
        when(mockTradeItemMapper.getGroupItemSaleTotal(query4)).thenReturn(goodsSalesTotalDTO3);

        // Run the test
        final GoodsSalesStatisticDTO result = tradeItemServiceImplUnderTest.queryGroupItemSaleStatistics(query);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockReportQueryExecutor).execute(any(Runnable.class));
        verify(spyPage).close();
    }

    @NotNull
    private static SalesVolumeReqDTO getSalesVolumeReqDTO() {
        final SalesVolumeReqDTO query1 = new SalesVolumeReqDTO();
        query1.setCurrentPage(0L);
        query1.setPageSize(0L);
        query1.setStartDate(LocalDate.of(2024, 3, 12));
        query1.setEndDate(LocalDate.of(2024, 3, 12));
        query1.setStoreGuids(Collections.singletonList(VALUE));
        query1.setBusinessStartDateTime(LocalDateTime.of(2024, 3, 12, 0, 0, 0));
        query1.setBusinessEndDateTime(LocalDateTime.of(2024, 3, 12, 0, 0, 0));
        query1.setEnterpriseGuid(ENTERPRISE_GUID);
        query1.setItemName("goodsNames");
        query1.setSort(0);
        query1.setSortName("sortName");
        query1.setCateringType(0);
        return query1;
    }

    @Test
    public void testQueryGroupItemSaleStatistics_TradeItemMapperQueryGroupItemSaleStatisticsReturnsNoItems() {
        // Setup
        final GoodsSalesVO query = getGoodsSalesVO();

        final GoodsSalesStatisticDTO expectedResult = getGoodsSalesStatisticDTO();

        // Configure TradeOrderMapper.queryOrderCount(...).
        final SalesVolumeReqDTO query1 = getSalesVolumeReqDTO();
        when(mockTradeOrderMapper.queryOrderCount(query1)).thenReturn(0L);

        final Page<GoodsSalesDTO> spyPage = getGoodsSalesDTOS();

        // Configure TradeItemMapper.getGroupItemSaleTotal(...).
        final GoodsSalesTotalDTO goodsSalesTotalDTO4 = new GoodsSalesTotalDTO();
        goodsSalesTotalDTO4.setTotalSalesVolume(new BigDecimal("20.00"));
        goodsSalesTotalDTO4.setTotalReceivedSumPrice(new BigDecimal("30.00"));
        goodsSalesTotalDTO4.setTotalDiscountPrice(new BigDecimal("40.00"));
        goodsSalesTotalDTO4.setTotalGrossProfitAmount(new BigDecimal("50.00"));
        final GoodsSalesVO query4 = getGoodsSalesVO();
        when(mockTradeItemMapper.getGroupItemSaleTotal(query4)).thenReturn(goodsSalesTotalDTO4);

        // Run the test
        final GoodsSalesStatisticDTO result = tradeItemServiceImplUnderTest.queryGroupItemSaleStatistics(query);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockReportQueryExecutor).execute(any(Runnable.class));
        verify(spyPage).close();
    }

    private Page<GoodsSalesDTO> getGoodsSalesDTOS() {
        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockReportQueryExecutor).execute(any(Runnable.class));

        // Configure TradeItemMapper.queryGroupItemSaleStatisticsCount(...).
        final GoodsSalesVO query2 = getGoodsSalesVO();
        when(mockTradeItemMapper.queryGroupItemSaleStatisticsCount(query2)).thenReturn(0L);

        // Configure TradeItemMapper.queryGroupItemSaleStatistics(...).
        final Page<GoodsSalesDTO> spyPage = spy(new Page<>());
        final GoodsSalesVO query3 = getGoodsSalesVO();
        when(mockTradeItemMapper.queryGroupItemSaleStatistics(query3)).thenReturn(spyPage);
        return spyPage;
    }

    @Test
    public void testPageStoreSaleStatistics() {
        // Setup
        final SalesVolumeReqDTO query = getSalesVolumeReqDTO();

        // Configure TradeItemMapper.countStoreSaleStatistics(...).
        final SalesVolumeReqDTO query1 = getSalesVolumeReqDTO();
        when(mockTradeItemMapper.countStoreSaleStatistics(query1)).thenReturn(0L);

        // Configure TradeItemMapper.pageStoreSaleStatistics(...).
        final List<SalesVolumeRespDTO> salesVolumeRespDTOS = getSalesVolumeRespDTOS();
        final SalesVolumeReqDTO query2 = getSalesVolumeReqDTO();
        when(mockTradeItemMapper.pageStoreSaleStatistics(query2)).thenReturn(salesVolumeRespDTOS);

        // Configure TradeOrderMapper.queryOrderCount(...).
        final SalesVolumeReqDTO query3 = getSalesVolumeReqDTO();
        when(mockTradeOrderMapper.queryOrderCount(query3)).thenReturn(0L);

        // Run the test
        final com.holderzone.framework.util.Page<SalesVolumeRespDTO> result = tradeItemServiceImplUnderTest.pageStoreSaleStatistics(
                query);

        // Verify the results
        assertEquals(1, result.getTotalCount());
    }

    @NotNull
    private static List<SalesVolumeRespDTO> getSalesVolumeRespDTOS() {
        final SalesVolumeRespDTO salesVolumeRespDTO = new SalesVolumeRespDTO();
        salesVolumeRespDTO.setItemName("itemName");
        salesVolumeRespDTO.setOrderCount(0.0);
        salesVolumeRespDTO.setSalesAmount(new BigDecimal("0.00"));
        salesVolumeRespDTO.setSalesProportion(0.0);
        salesVolumeRespDTO.setSpotRate(0.0);
        return Collections.singletonList(salesVolumeRespDTO);
    }

    @Test
    public void testPageStoreSaleStatistics_TradeItemMapperPageStoreSaleStatisticsReturnsNoItems() {
        // Setup
        final SalesVolumeReqDTO query = getSalesVolumeReqDTO();

        // Configure TradeItemMapper.countStoreSaleStatistics(...).
        final SalesVolumeReqDTO query1 = getSalesVolumeReqDTO();
        when(mockTradeItemMapper.countStoreSaleStatistics(query1)).thenReturn(0L);

        // Configure TradeItemMapper.pageStoreSaleStatistics(...).
        final SalesVolumeReqDTO query2 = getSalesVolumeReqDTO();
        when(mockTradeItemMapper.pageStoreSaleStatistics(query2)).thenReturn(Collections.emptyList());

        // Configure TradeOrderMapper.queryOrderCount(...).
        final SalesVolumeReqDTO query3 = getSalesVolumeReqDTO();
        when(mockTradeOrderMapper.queryOrderCount(query3)).thenReturn(0L);

        // Run the test
        final com.holderzone.framework.util.Page<SalesVolumeRespDTO> result = tradeItemServiceImplUnderTest.pageStoreSaleStatistics(
                query);

        // Verify the results
        assertEquals(1, result.getTotalCount());
    }

    @Test
    public void testStoreSaleStatisticsTotal() {
        // Setup
        final SalesVolumeReqDTO query = getSalesVolumeReqDTO();

        final GoodsSalesTotalDTO expectedResult = getGoodsSalesTotalDTO();

        // Configure TradeItemMapper.getStoreSalesTotal(...).
        final GoodsSalesTotalDTO goodsSalesTotalDTO = getGoodsSalesTotalDTO();
        final SalesVolumeReqDTO query1 = getSalesVolumeReqDTO();
        when(mockTradeItemMapper.getStoreSalesTotal(query1)).thenReturn(goodsSalesTotalDTO);

        // Run the test
        final GoodsSalesTotalDTO result = tradeItemServiceImplUnderTest.storeSaleStatisticsTotal(query);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testExportItemTypeStatistics() {
        // Setup
        final GoodsSalesVO query = getGoodsSalesVO();

        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockReportQueryExecutor).execute(any(Runnable.class));

        // Configure TradeItemMapper.queryItemTypeStatisticsCount(...).
        final GoodsSalesVO query1 = getGoodsSalesVO();
        when(mockTradeItemMapper.queryItemTypeStatisticsCount(query1)).thenReturn(0L);

        // Configure TradeItemMapper.queryItemTypeStatistics(...).
        final Page<GoodsSalesDTO> spyPage = spy(new Page<>(0, 0, false));
        final GoodsSalesVO query2 = getGoodsSalesVO();
        when(mockTradeItemMapper.queryItemTypeStatistics(query2)).thenReturn(spyPage);

        // Configure TradeItemMapper.queryItemTypeSalesTotal(...).
        final GoodsSalesTotalDTO goodsSalesTotalDTO = getGoodsSalesTotalDTO();
        final GoodsSalesVO query3 = getGoodsSalesVO();
        when(mockTradeItemMapper.queryItemTypeSalesTotal(query3)).thenReturn(goodsSalesTotalDTO);

        // Run the test
        final String result = tradeItemServiceImplUnderTest.exportItemTypeStatistics(query);

        // Verify the results
        assertThat(result).isEqualTo(RESULT);
        verify(mockReportQueryExecutor).execute(any(Runnable.class));
        verify(spyPage).close();
    }

    @Test
    public void testExportItemTypeStatistics_TradeItemMapperQueryItemTypeStatisticsReturnsNoItems() {
        // Setup
        final GoodsSalesVO query = getGoodsSalesVO();

        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockReportQueryExecutor).execute(any(Runnable.class));

        // Configure TradeItemMapper.queryItemTypeStatisticsCount(...).
        final GoodsSalesVO query1 = getGoodsSalesVO();
        when(mockTradeItemMapper.queryItemTypeStatisticsCount(query1)).thenReturn(0L);

        // Configure TradeItemMapper.queryItemTypeStatistics(...).
        final Page<GoodsSalesDTO> spyPage = spy(new Page<>());
        final GoodsSalesVO queryNoItems2 = getGoodsSalesVO();
        when(mockTradeItemMapper.queryItemTypeStatistics(queryNoItems2)).thenReturn(spyPage);

        // Configure TradeItemMapper.queryItemTypeSalesTotal(...).
        final GoodsSalesTotalDTO goodsSalesTotalDTO = getGoodsSalesTotalDTO();
        final GoodsSalesVO queryNoItems3 = getGoodsSalesVO();
        when(mockTradeItemMapper.queryItemTypeSalesTotal(queryNoItems3)).thenReturn(goodsSalesTotalDTO);

        // Run the test
        final String resultExportItemTypeStatistics = tradeItemServiceImplUnderTest.exportItemTypeStatistics(query);

        // Verify the results
        assertThat(resultExportItemTypeStatistics).isEqualTo(RESULT);
        verify(mockReportQueryExecutor).execute(any(Runnable.class));
        verify(spyPage).close();
    }

    @Test
    public void testExportGroupItemSaleStatistics() {
        // Setup
        final GoodsSalesVO query = getGoodsSalesVO();

        // Configure TradeOrderMapper.queryOrderCount(...).
        final SalesVolumeReqDTO query1 = getSalesVolumeReqDTO();
        when(mockTradeOrderMapper.queryOrderCount(query1)).thenReturn(0L);

        doAnswer(invocation -> {
            ((Runnable) invocation.getArguments()[0]).run();
            return null;
        }).when(mockReportQueryExecutor).execute(any(Runnable.class));

        // Configure TradeItemMapper.queryGroupItemSaleStatisticsCount(...).
        final GoodsSalesVO query2 = getGoodsSalesVO();
        when(mockTradeItemMapper.queryGroupItemSaleStatisticsCount(query2)).thenReturn(0L);

        // Configure TradeItemMapper.queryGroupItemSaleStatistics(...).
        final Page<GoodsSalesDTO> spyPage = spy(new Page<>(0, 0, false));
        final GoodsSalesVO query3 = getGoodsSalesVO();
        when(mockTradeItemMapper.queryGroupItemSaleStatistics(query3)).thenReturn(spyPage);

        // Configure TradeItemMapper.getGroupItemSaleTotal(...).
        final GoodsSalesTotalDTO goodsSalesTotalDTO = getGoodsSalesTotalDTO();
        final GoodsSalesVO query4 = getGoodsSalesVO();
        when(mockTradeItemMapper.getGroupItemSaleTotal(query4)).thenReturn(goodsSalesTotalDTO);

        // Run the test
        final String result = tradeItemServiceImplUnderTest.exportGroupItemSaleStatistics(query);

        // Verify the results
        assertThat(result).isEqualTo(RESULT);
        verify(mockReportQueryExecutor).execute(any(Runnable.class));
        verify(spyPage).close();
    }

    @Test
    public void testExportGroupItemSaleStatistics_TradeItemMapperQueryGroupItemSaleStatisticsReturnsNoItems() {
        // Setup
        final GoodsSalesVO query = getGoodsSalesVO();

        // Configure TradeOrderMapper.queryOrderCount(...).
        final SalesVolumeReqDTO query1 = getSalesVolumeReqDTO();
        when(mockTradeOrderMapper.queryOrderCount(query1)).thenReturn(0L);

        final Page<GoodsSalesDTO> spyPage = getGoodsSalesDTOS();

        // Configure TradeItemMapper.getGroupItemSaleTotal(...).
        final GoodsSalesTotalDTO goodsSalesTotalDTO = getGoodsSalesTotalDTO();
        final GoodsSalesVO query4 = getGoodsSalesVO();
        when(mockTradeItemMapper.getGroupItemSaleTotal(query4)).thenReturn(goodsSalesTotalDTO);

        // Run the test
        final String result = tradeItemServiceImplUnderTest.exportGroupItemSaleStatistics(query);

        // Verify the results
        assertThat(result).isEqualTo(RESULT);
        verify(mockReportQueryExecutor).execute(any(Runnable.class));
        verify(spyPage).close();
    }

    @NotNull
    private static GoodsSalesTotalDTO getGoodsSalesTotalDTO() {
        final GoodsSalesTotalDTO goodsSalesTotalDTO = new GoodsSalesTotalDTO();
        goodsSalesTotalDTO.setTotalSalesVolume(new BigDecimal("0.00"));
        goodsSalesTotalDTO.setTotalReceivedSumPrice(new BigDecimal("0.00"));
        goodsSalesTotalDTO.setTotalDiscountPrice(new BigDecimal("0.00"));
        goodsSalesTotalDTO.setTotalGrossProfitAmount(new BigDecimal("0.00"));
        return goodsSalesTotalDTO;
    }

    @Test
    public void testPageStoreSaleStatisticsType() {
        // Setup
        final SalesVolumeReqDTO query = getSalesVolumeReqDTO();

        // Configure TradeItemMapper.pageStoreSaleStatisticsType(...).
        final SalesVolumeReqDTO query1 = getSalesVolumeReqDTO();
        when(mockTradeItemMapper.pageStoreSaleStatisticsType(query1)).thenReturn(Collections.singletonList(VALUE));

        // Run the test
        final List<String> result = tradeItemServiceImplUnderTest.pageStoreSaleStatisticsType(query);

        // Verify the results
        assertThat(result).isEqualTo(Collections.singletonList(VALUE));
    }

    @Test
    public void testPageStoreSaleStatisticsType_TradeItemMapperReturnsNoItems() {
        // Setup
        final SalesVolumeReqDTO query = getSalesVolumeReqDTO();

        // Configure TradeItemMapper.pageStoreSaleStatisticsType(...).
        final SalesVolumeReqDTO query1 = getSalesVolumeReqDTO();
        when(mockTradeItemMapper.pageStoreSaleStatisticsType(query1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = tradeItemServiceImplUnderTest.pageStoreSaleStatisticsType(query);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testPageSaleDetail() {

        // Configure TradeItemMapper.countSaleDetail(...).
        final SalesDetailQO queryCountSaleDetail = getSalesDetailQO();
        when(mockTradeItemMapper.countSaleDetail(queryCountSaleDetail)).thenReturn(0L);

        // Configure TradeItemMapper.pageSaleDetail(...).
        final SalesDetailRespDTO salesDetailRespDTO = getSalesDetailRespDTO();
        final List<SalesDetailRespDTO> salesDetailRespDTOS = Collections.singletonList(salesDetailRespDTO);
        final SalesDetailQO queryPageSaleDetail2 = getSalesDetailQO();
        when(mockTradeItemMapper.pageSaleDetail(queryPageSaleDetail2)).thenReturn(salesDetailRespDTOS);

        // Run the test
        // Setup
        final SalesDetailQO queryPageSaleDetail = getSalesDetailQO();
        final com.holderzone.framework.util.Page<SalesDetailRespDTO> result = tradeItemServiceImplUnderTest.pageSaleDetail(
                queryPageSaleDetail);

        // Verify the results
        assertEquals(1, result.getTotalCount());
    }

    @NotNull
    private static SalesDetailRespDTO getSalesDetailRespDTO() {
        final SalesDetailRespDTO salesDetailRespDTO = new SalesDetailRespDTO();
        salesDetailRespDTO.setItemName("itemName");
        salesDetailRespDTO.setItemTypeName("itemTypeName");
        salesDetailRespDTO.setItemType("itemType");
        salesDetailRespDTO.setOperation("operation");
        salesDetailRespDTO.setOrderNumber(0.0);
        salesDetailRespDTO.setUnit("unit");
        salesDetailRespDTO.setPrice(new BigDecimal("0.00"));
        salesDetailRespDTO.setAttrPrice(new BigDecimal("0.00"));
        salesDetailRespDTO.setOrderNo("orderNo");
        salesDetailRespDTO.setCreateTime(LocalDateTime.of(2024, 3, 12, 0, 0, 0));
        salesDetailRespDTO.setCheckoutTime(LocalDateTime.of(2024, 3, 12, 0, 0, 0));
        return salesDetailRespDTO;
    }

    @Test
    public void testPageSaleDetail_TradeItemMapperPageSaleDetailReturnsNoItems() {

        // Configure TradeItemMapper.countSaleDetail(...).
        final SalesDetailQO query1 = getSalesDetailQO();
        when(mockTradeItemMapper.countSaleDetail(query1)).thenReturn(0L);

        // Configure TradeItemMapper.pageSaleDetail(...).
        final SalesDetailQO query2 = getSalesDetailQO();
        when(mockTradeItemMapper.pageSaleDetail(query2)).thenReturn(Collections.emptyList());

        // Run the test
        // Setup
        final SalesDetailQO pageSaleDetailNoItems = getSalesDetailQO();
        final com.holderzone.framework.util.Page<SalesDetailRespDTO> result = tradeItemServiceImplUnderTest.pageSaleDetail(
                pageSaleDetailNoItems);

        // Verify the results
        assertEquals(1, result.getTotalCount());
    }

    @NotNull
    private static SalesDetailQO getSalesDetailQO() {
        final SalesDetailQO query1 = new SalesDetailQO();
        query1.setEnterpriseGuid(ENTERPRISE_GUID);
        query1.setStartTime(LocalDate.of(2024, 3, 12));
        query1.setEndTime(LocalDate.of(2024, 3, 12));
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        return query1;
    }

    @Test
    public void testExportSaleDetail() {
        // Setup
        final SalesDetailQO query = getSalesDetailQO();

        // Configure TradeItemMapper.countSaleDetail(...).
        final SalesDetailQO query1 = getSalesDetailQO();
        when(mockTradeItemMapper.countSaleDetail(query1)).thenReturn(0L);

        // Configure TradeItemMapper.pageSaleDetail(...).
        final SalesDetailRespDTO salesDetailRespDTO = getSalesDetailRespDTO();
        final List<SalesDetailRespDTO> salesDetailRespDTOS = Collections.singletonList(salesDetailRespDTO);
        final SalesDetailQO query2 = getSalesDetailQO();
        when(mockTradeItemMapper.pageSaleDetail(query2)).thenReturn(salesDetailRespDTOS);

        // Run the test
        final String result = tradeItemServiceImplUnderTest.exportSaleDetail(query);

        // Verify the results
        assertThat(result).isEqualTo(RESULT);
    }

    @Test
    public void testExportSaleDetail_TradeItemMapperPageSaleDetailReturnsNoItems() {
        // Setup
        final SalesDetailQO query = getSalesDetailQO();

        // Configure TradeItemMapper.countSaleDetail(...).
        final SalesDetailQO query1 = getSalesDetailQO();
        when(mockTradeItemMapper.countSaleDetail(query1)).thenReturn(0L);

        // Configure TradeItemMapper.pageSaleDetail(...).
        final SalesDetailQO query2 = getSalesDetailQO();
        when(mockTradeItemMapper.pageSaleDetail(query2)).thenReturn(Collections.emptyList());

        // Run the test
        final String result = tradeItemServiceImplUnderTest.exportSaleDetail(query);

        // Verify the results
        assertThat(result).isEqualTo(RESULT);
    }
}
