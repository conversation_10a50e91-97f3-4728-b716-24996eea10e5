package com.holderzone.holder.saas.store.report.util;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.util.MultiValueMap;

/**
 * <AUTHOR>
 */
@Slf4j
public class MockHttpUtil {

    private MockHttpUtil() {

    }

    private static final String AUTHORIZATION = "token";

    private static final String TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlbnRlcnByaXNlR3VpZCI6IjY1MDY0MzExOTU2NTE5ODIzMzciLCJzdG9yZU5vIjoiMzE4NzI4OSIsImRldmljZUd1aWQiOiIyMTEyMTUxNzUzMjAwODQwMDA2IiwidXNlckd1aWQiOiI2ODYzNjkyNTc4MTc3NDE3MjE3IiwiaWF0IjoxNjQyNTc4NjExLCJpc3MiOiJIb2xkZXIuY29tIn0=.Osaj77+977+977WdSO+/vXk977+9Owzvv70sT++/ve+/ve+/ve+/vQLehXrvv73vv71+SO+/vUc=";


    private static final String HEADER_OF_USER_INFO = "userInfo";
    private static final String USER_INFO = "{\"operSubjectGuid\": \"2008141005594470007\",\"enterpriseGuid\":" +
            " \"6506431195651982337\",\"enterpriseName\": \"企业0227\",\"enterpriseNo\": \"********\",\"storeGuid\":" +
            " \"6619160595813892096\",\"storeName\": \"听雨阁\",\"storeNo\": \"6148139\",\"userGuid\": " +
            "\"6653489337230950401\",\"account\": \"200003\",\"tel\": \"***********\",\"name\": \"赵亮\"}\n";


    /**
     * get
     */
    public static String get(String uri, MultiValueMap<String, String> params, MockMvc mockMvc) {
        try {
            MockHttpServletRequestBuilder getRequest = MockMvcRequestBuilders.get(uri);
            getRequest.header(AUTHORIZATION, TOKEN);
            getRequest.header(HEADER_OF_USER_INFO, USER_INFO);
            if (params != null) {
                getRequest.params(params);
            }
            MvcResult tradeResult = mockMvc.perform(getRequest).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            MockHttpServletResponse response = tradeResult.getResponse();
            return response.getContentAsString();
        } catch (Exception e) {
            log.error("get error:{}", e.getMessage());
            return null;
        }
    }

    /**
     * post
     */
    public static String post(String uri, MultiValueMap<String, String> params, MockMvc mockMvc) {
        try {
            MockHttpServletRequestBuilder postDataRequest = MockMvcRequestBuilders.post(uri);
            postDataRequest.header(AUTHORIZATION, TOKEN);
            postDataRequest.header(HEADER_OF_USER_INFO, USER_INFO);
            if (params != null) {
                postDataRequest.params(params);
            }
            MvcResult tradeResult = mockMvc.perform(postDataRequest).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            MockHttpServletResponse response = tradeResult.getResponse();
            return response.getContentAsString();
        } catch (Exception e) {
            log.error("post data error:{}", e.getMessage());
            return null;
        }
    }

    /**
     * post
     */
    public static String post(String uri, Object body, MockMvc mockMvc) {
        try {
            MockHttpServletRequestBuilder postBodyRequest = MockMvcRequestBuilders.post(uri)
                    .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(body));
            postBodyRequest.header(AUTHORIZATION, TOKEN);
            postBodyRequest.header(HEADER_OF_USER_INFO, USER_INFO);
            MvcResult tradeResult = mockMvc.perform(postBodyRequest).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            MockHttpServletResponse response = tradeResult.getResponse();
            return response.getContentAsString();
        } catch (Exception e) {
            log.error("post body error:{}", e.getMessage());
            return null;
        }
    }

    /**
     * put
     */
    public static String put(String uri, MultiValueMap<String, String> params, MockMvc mockMvc) {
        try {
            MockHttpServletRequestBuilder putDataRequest = MockMvcRequestBuilders.put(uri);
            putDataRequest.header(AUTHORIZATION, TOKEN);
            putDataRequest.header(HEADER_OF_USER_INFO, USER_INFO);
            if (params != null) {
                putDataRequest.params(params);
            }
            MvcResult tradeResult = mockMvc.perform(putDataRequest).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            MockHttpServletResponse response = tradeResult.getResponse();
            return response.getContentAsString();
        } catch (Exception e) {
            log.error("put data error:{}", e.getMessage());
            return null;
        }
    }

    /**
     * put
     */
    public static String put(String uri, Object body, MockMvc mockMvc) {
        try {
            MockHttpServletRequestBuilder putBodyRequest = MockMvcRequestBuilders.put(uri)
                    .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(body));
            putBodyRequest.header(AUTHORIZATION, TOKEN);
            putBodyRequest.header(HEADER_OF_USER_INFO, USER_INFO);
            MvcResult tradeResult = mockMvc.perform(putBodyRequest).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            MockHttpServletResponse response = tradeResult.getResponse();
            return response.getContentAsString();
        } catch (Exception e) {
            log.error("put body error:{}", e.getMessage());
            return null;
        }
    }

    /**
     * delete
     */
    public static String delete(String uri, MockMvc mockMvc) {
        try {
            MockHttpServletRequestBuilder deleteRequest = MockMvcRequestBuilders.delete(uri);
            deleteRequest.header(AUTHORIZATION, TOKEN);
            deleteRequest.header(HEADER_OF_USER_INFO, USER_INFO);
            MvcResult tradeResult = mockMvc.perform(deleteRequest).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
            MockHttpServletResponse response = tradeResult.getResponse();
            return response.getContentAsString();
        } catch (Exception e) {
            log.error("delete error:{}", e.getMessage());
            return null;
        }
    }

}
