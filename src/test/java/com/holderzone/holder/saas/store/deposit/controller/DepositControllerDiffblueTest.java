package com.holderzone.holder.saas.store.deposit.controller;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.deposit.service.IHsdDepositService;
import com.holderzone.holder.saas.store.deposit.service.IHsdGoodsService;
import com.holderzone.saas.store.dto.deposit.req.DepositCreateReqDTO;
import com.holderzone.saas.store.dto.deposit.req.DepositGetReqDTO;
import com.holderzone.saas.store.dto.deposit.req.DepositQueryReqDTO;
import com.holderzone.saas.store.dto.deposit.req.DepositQueryReqForWebDTO;
import com.holderzone.saas.store.dto.deposit.req.MessageRemindReqDTO;
import com.holderzone.saas.store.dto.deposit.req.ModifyRemindReqDTO;
import com.holderzone.saas.store.dto.deposit.req.OperationHistoryQueryReqDTO;
import com.holderzone.saas.store.dto.deposit.req.QueryDepositDetailReqDTO;
import com.holderzone.saas.store.dto.deposit.resp.DepositDetailForPosRespDTO;
import com.holderzone.saas.store.dto.deposit.resp.GoodsRespDTO;

import java.math.BigDecimal;
import java.util.ArrayList;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.ContentResultMatchers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ContextConfiguration(classes = {DepositController.class})
@RunWith(SpringJUnit4ClassRunner.class)
public class DepositControllerDiffblueTest {
    @Autowired
    private DepositController depositController;

    @MockBean
    private IHsdDepositService iHsdDepositService;

    @MockBean
    private IHsdGoodsService iHsdGoodsService;

    /**
     * Method under test:
     * {@link DepositController#createDepositItem(DepositCreateReqDTO)}
     */
    @Test
    public void testCreateDepositItem() throws Exception {
        DepositCreateReqDTO depositCreateReqDTO = new DepositCreateReqDTO();
        depositCreateReqDTO.setCustomerName("Customer Name");
        depositCreateReqDTO.setGoods(new ArrayList<>());
        depositCreateReqDTO.setHeadPortrait("Head Portrait");
        depositCreateReqDTO.setMemberGuid("1234");
        depositCreateReqDTO.setPhoneNum("**********");
        depositCreateReqDTO.setRemark("Remark");
        depositCreateReqDTO.setStoreGuid("1234");
        String content = (new ObjectMapper()).writeValueAsString(depositCreateReqDTO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/deposit/create_deposit_item")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);
        ResultActions actualPerformResult = MockMvcBuilders.standaloneSetup(depositController)
                .build()
                .perform(requestBuilder);
        actualPerformResult.andExpect(MockMvcResultMatchers.status().is(400));
    }

    /**
     * Method under test:
     * {@link DepositController#queryDepositDetail(QueryDepositDetailReqDTO)}
     */
    @Test
    public void testQueryDepositDetail() throws Exception {
        when(iHsdDepositService.queryDepositDetail(Mockito.<QueryDepositDetailReqDTO>any())).thenReturn(new ArrayList<>());

        QueryDepositDetailReqDTO queryDepositDetailReqDTO = new QueryDepositDetailReqDTO();
        queryDepositDetailReqDTO.setDepositGuid("1234");
        String content = (new ObjectMapper()).writeValueAsString(queryDepositDetailReqDTO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/deposit/query_deposit_detail")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);
        MockMvcBuilders.standaloneSetup(depositController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content().string("[]"));
    }

    /**
     * Method under test:
     * {@link DepositController#queryDepositDetail(QueryDepositDetailReqDTO)}
     */
    @Test
    public void testQueryDepositDetail2() throws Exception {
        GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
        goodsRespDTO.setDepositNum(10);
        goodsRespDTO.setExpireTime("deposit");
        goodsRespDTO.setGoodsName("deposit");
        goodsRespDTO.setGoodsUnit("deposit");
        goodsRespDTO.setGuid("1234");
        goodsRespDTO.setResidueDay(1);
        goodsRespDTO.setResidueNum(1);
        goodsRespDTO.setSkuGuid("1234");
        goodsRespDTO.setSkuName("deposit");
        goodsRespDTO.setStorePosition("deposit");
        goodsRespDTO.setTakeOutNum(10);

        ArrayList<GoodsRespDTO> goodsRespDTOList = new ArrayList<>();
        goodsRespDTOList.add(goodsRespDTO);
        when(iHsdDepositService.queryDepositDetail(Mockito.<QueryDepositDetailReqDTO>any())).thenReturn(goodsRespDTOList);

        QueryDepositDetailReqDTO queryDepositDetailReqDTO = new QueryDepositDetailReqDTO();
        queryDepositDetailReqDTO.setDepositGuid("1234");
        String content = (new ObjectMapper()).writeValueAsString(queryDepositDetailReqDTO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/deposit/query_deposit_detail")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);
        MockMvcBuilders.standaloneSetup(depositController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content()
                        .string(
                                "[{\"guid\":\"1234\",\"goodsName\":\"deposit\",\"skuName\":\"deposit\",\"skuGuid\":\"1234\",\"goodsUnit\":\"deposit\","
                                        + "\"storePosition\":\"deposit\",\"expireTime\":\"deposit\",\"residueDay\":1,\"depositNum\":10,\"residueNum\":1,"
                                        + "\"takeOutNum\":10}]"));
    }

    /**
     * Method under test:
     * {@link DepositController#queryDepositDetailForPos(QueryDepositDetailReqDTO)}
     */
    @Test
    public void testQueryDepositDetailForPos() throws Exception {
        DepositDetailForPosRespDTO depositDetailForPosRespDTO = new DepositDetailForPosRespDTO();
        depositDetailForPosRespDTO.setCustomerName("Customer Name");
        depositDetailForPosRespDTO.setDepositOrderId("42");
        depositDetailForPosRespDTO.setGoodsRespDTOS(new ArrayList<>());
        depositDetailForPosRespDTO.setHeadPortrait("Head Portrait");
        depositDetailForPosRespDTO.setPhoneNum("**********");
        depositDetailForPosRespDTO.setRemark("Remark");
        depositDetailForPosRespDTO.setSaveTime("Save Time");
        when(iHsdDepositService.queryDepositDetailForPos(Mockito.<QueryDepositDetailReqDTO>any()))
                .thenReturn(depositDetailForPosRespDTO);

        QueryDepositDetailReqDTO queryDepositDetailReqDTO = new QueryDepositDetailReqDTO();
        queryDepositDetailReqDTO.setDepositGuid("1234");
        String content = (new ObjectMapper()).writeValueAsString(queryDepositDetailReqDTO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/deposit/query_deposit_detail_for_pos")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);
        MockMvcBuilders.standaloneSetup(depositController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content()
                        .string(
                                "{\"depositOrderId\":\"42\",\"saveTime\":\"Save Time\",\"headPortrait\":\"Head Portrait\",\"customerName\":\"Customer"
                                        + " Name\",\"phoneNum\":\"**********\",\"goodsRespDTOS\":[],\"remark\":\"Remark\"}"));
    }

    /**
     * Method under test:
     * {@link DepositController#queryDepositDetailForPos(QueryDepositDetailReqDTO)}
     */
    @Test
    public void testQueryDepositDetailForPos2() throws Exception {
        GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
        goodsRespDTO.setDepositNum(10);
        goodsRespDTO.setExpireTime("deposit");
        goodsRespDTO.setGoodsName("deposit");
        goodsRespDTO.setGoodsUnit("deposit");
        goodsRespDTO.setGuid("1234");
        goodsRespDTO.setResidueDay(1);
        goodsRespDTO.setResidueNum(1);
        goodsRespDTO.setSkuGuid("1234");
        goodsRespDTO.setSkuName("deposit");
        goodsRespDTO.setStorePosition("deposit");
        goodsRespDTO.setTakeOutNum(10);

        ArrayList<GoodsRespDTO> goodsRespDTOS = new ArrayList<>();
        goodsRespDTOS.add(goodsRespDTO);

        DepositDetailForPosRespDTO depositDetailForPosRespDTO = new DepositDetailForPosRespDTO();
        depositDetailForPosRespDTO.setCustomerName("Customer Name");
        depositDetailForPosRespDTO.setDepositOrderId("42");
        depositDetailForPosRespDTO.setGoodsRespDTOS(goodsRespDTOS);
        depositDetailForPosRespDTO.setHeadPortrait("Head Portrait");
        depositDetailForPosRespDTO.setPhoneNum("**********");
        depositDetailForPosRespDTO.setRemark("Remark");
        depositDetailForPosRespDTO.setSaveTime("Save Time");
        when(iHsdDepositService.queryDepositDetailForPos(Mockito.<QueryDepositDetailReqDTO>any()))
                .thenReturn(depositDetailForPosRespDTO);

        QueryDepositDetailReqDTO queryDepositDetailReqDTO = new QueryDepositDetailReqDTO();
        queryDepositDetailReqDTO.setDepositGuid("1234");
        String content = (new ObjectMapper()).writeValueAsString(queryDepositDetailReqDTO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/deposit/query_deposit_detail_for_pos")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);
        MockMvcBuilders.standaloneSetup(depositController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content()
                        .string(
                                "{\"depositOrderId\":\"42\",\"saveTime\":\"Save Time\",\"headPortrait\":\"Head Portrait\",\"customerName\":\"Customer"
                                        + " Name\",\"phoneNum\":\"**********\",\"goodsRespDTOS\":[{\"guid\":\"1234\",\"goodsName\":\"deposit\",\"skuName\":\"deposit"
                                        + "\",\"skuGuid\":\"1234\",\"goodsUnit\":\"deposit\",\"storePosition\":\"deposit\",\"expireTime\":\"deposit\",\"residueDay"
                                        + "\":1,\"depositNum\":10,\"residueNum\":1,\"takeOutNum\":10}],\"remark\":\"Remark\"}"));
    }

    /**
     * Method under test:
     * {@link DepositController#queryOperationHistory(OperationHistoryQueryReqDTO)}
     */
    @Test
    public void testQueryOperationHistory() throws Exception {
        when(iHsdDepositService.queryOperationHistory(Mockito.<OperationHistoryQueryReqDTO>any()))
                .thenReturn(new Page<>(1L, 3L));

        OperationHistoryQueryReqDTO operationHistoryQueryReqDTO = new OperationHistoryQueryReqDTO();
        operationHistoryQueryReqDTO.setAccount("3");
        operationHistoryQueryReqDTO.setAccountName("Dr Jane Doe");
        operationHistoryQueryReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        operationHistoryQueryReqDTO.setCurrentPage(1);
        operationHistoryQueryReqDTO.setDepositGuid("1234");
        operationHistoryQueryReqDTO.setDeviceId("42");
        operationHistoryQueryReqDTO.setDeviceType(1);
        operationHistoryQueryReqDTO.setEnterpriseGuid("1234");
        operationHistoryQueryReqDTO.setEnterpriseName("Enterprise Name");
        operationHistoryQueryReqDTO.setInvoiceCode("Invoice Code");
        operationHistoryQueryReqDTO.setInvoicePhone("**********");
        operationHistoryQueryReqDTO.setIsInvoiceCode(1);
        operationHistoryQueryReqDTO.setLatitude("Latitude");
        operationHistoryQueryReqDTO.setLongitude("Longitude");
        operationHistoryQueryReqDTO.setMaxId(1L);
        operationHistoryQueryReqDTO.setOperSubjectGuid("1234");
        operationHistoryQueryReqDTO.setPageSize(3);
        operationHistoryQueryReqDTO.setRequestTimestamp(1L);
        operationHistoryQueryReqDTO.setStoreGuid("1234");
        operationHistoryQueryReqDTO.setStoreName("Store Name");
        operationHistoryQueryReqDTO.setUserGuid("1234");
        operationHistoryQueryReqDTO.setUserName("janedoe");
        String content = (new ObjectMapper()).writeValueAsString(operationHistoryQueryReqDTO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/deposit/query_operation_history")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);
        MockMvcBuilders.standaloneSetup(depositController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json;charset=UTF-8"))
                .andExpect(
                        MockMvcResultMatchers.content().string("{\"currentPage\":1,\"pageSize\":3,\"totalCount\":0,\"data\":[]}"));
    }

    /**
     * Method under test:
     * {@link DepositController#queryGoodsSummary(DepositQueryReqForWebDTO)}
     */
    @Test
    public void testQueryGoodsSummary() throws Exception {
        when(iHsdDepositService.queryGoodsSummary(Mockito.<DepositQueryReqForWebDTO>any())).thenReturn(new Page<>(1L, 3L));

        DepositQueryReqForWebDTO depositQueryReqForWebDTO = new DepositQueryReqForWebDTO();
        depositQueryReqForWebDTO.setAccount("3");
        depositQueryReqForWebDTO.setAccountName("Dr Jane Doe");
        depositQueryReqForWebDTO.setActuallyPayFee(new BigDecimal("2.3"));
        depositQueryReqForWebDTO.setCondition("Condition");
        depositQueryReqForWebDTO.setCurrentPage(1);
        depositQueryReqForWebDTO.setDeviceId("42");
        depositQueryReqForWebDTO.setDeviceType(1);
        depositQueryReqForWebDTO.setEnterpriseGuid("1234");
        depositQueryReqForWebDTO.setEnterpriseName("Enterprise Name");
        depositQueryReqForWebDTO.setInvoiceCode("Invoice Code");
        depositQueryReqForWebDTO.setInvoicePhone("**********");
        depositQueryReqForWebDTO.setIsInvoiceCode(1);
        depositQueryReqForWebDTO.setLatitude("Latitude");
        depositQueryReqForWebDTO.setLongitude("Longitude");
        depositQueryReqForWebDTO.setMaxId(1L);
        depositQueryReqForWebDTO.setOperSubjectGuid("1234");
        depositQueryReqForWebDTO.setPageSize(3);
        depositQueryReqForWebDTO.setRequestTimestamp(1L);
        depositQueryReqForWebDTO.setStoreGuid("1234");
        depositQueryReqForWebDTO.setStoreName("Store Name");
        depositQueryReqForWebDTO.setUserGuid("1234");
        depositQueryReqForWebDTO.setUserName("janedoe");
        String content = (new ObjectMapper()).writeValueAsString(depositQueryReqForWebDTO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/deposit/query_goods_summary")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);
        MockMvcBuilders.standaloneSetup(depositController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json;charset=UTF-8"))
                .andExpect(
                        MockMvcResultMatchers.content().string("{\"currentPage\":1,\"pageSize\":3,\"totalCount\":0,\"data\":[]}"));
    }

    /**
     * Method under test: {@link DepositController#remindSet(MessageRemindReqDTO)}
     */
    @Test
    public void testRemindSet() throws Exception {
        when(iHsdDepositService.remindSet(Mockito.<MessageRemindReqDTO>any())).thenReturn(true);

        MessageRemindReqDTO messageRemindReqDTO = new MessageRemindReqDTO();
        messageRemindReqDTO.setAdvanceDays(1);
        messageRemindReqDTO.setDepositRemind(1);
        messageRemindReqDTO.setExpireRemind(1);
        messageRemindReqDTO.setGetRemind(1);
        messageRemindReqDTO.setStoreGuid("1234");
        messageRemindReqDTO.setStoreName("Store Name");
        String content = (new ObjectMapper()).writeValueAsString(messageRemindReqDTO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/deposit/remind_set")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);
        ResultActions resultActions = MockMvcBuilders.standaloneSetup(depositController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json;charset=UTF-8"));
        ContentResultMatchers contentResult = MockMvcResultMatchers.content();
        resultActions.andExpect(contentResult.string(Boolean.TRUE.toString()));
    }

    /**
     * Method under test: {@link DepositController#queryRemind(ModifyRemindReqDTO)}
     */
    @Test
    public void testQueryRemind() throws Exception {
        MessageRemindReqDTO messageRemindReqDTO = new MessageRemindReqDTO();
        messageRemindReqDTO.setAdvanceDays(1);
        messageRemindReqDTO.setDepositRemind(1);
        messageRemindReqDTO.setExpireRemind(1);
        messageRemindReqDTO.setGetRemind(1);
        messageRemindReqDTO.setStoreGuid("1234");
        messageRemindReqDTO.setStoreName("Store Name");
        when(iHsdDepositService.queryRemind(Mockito.<String>any())).thenReturn(messageRemindReqDTO);

        ModifyRemindReqDTO modifyRemindReqDTO = new ModifyRemindReqDTO();
        modifyRemindReqDTO.setStoreGuid("1234");
        String content = (new ObjectMapper()).writeValueAsString(modifyRemindReqDTO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/deposit/query_remind")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);
        MockMvcBuilders.standaloneSetup(depositController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json;charset=UTF-8"))
                .andExpect(MockMvcResultMatchers.content()
                        .string(
                                "{\"storeGuid\":\"1234\",\"storeName\":\"Store Name\",\"depositRemind\":1,\"getRemind\":1,\"expireRemind\":1,\"advanceDays"
                                        + "\":1}"));
    }

    /**
     * Method under test:
     * {@link DepositController#createDepositItem(DepositCreateReqDTO)}
     */
    @Test
    public void testCreateDepositItem2() throws Exception {
        DepositCreateReqDTO depositCreateReqDTO = new DepositCreateReqDTO();
        depositCreateReqDTO.setCustomerName("Customer Name");
        depositCreateReqDTO.setGoods(new ArrayList<>());
        depositCreateReqDTO.setHeadPortrait("Head Portrait");
        depositCreateReqDTO.setMemberGuid("1234");
        depositCreateReqDTO.setPhoneNum("**********");
        depositCreateReqDTO.setRemark("Remark");
        depositCreateReqDTO.setStoreGuid("1234");
        String content = (new ObjectMapper()).writeValueAsString(depositCreateReqDTO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .post("/deposit/create_deposit_item", "Uri Vars")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);
        ResultActions actualPerformResult = MockMvcBuilders.standaloneSetup(depositController)
                .build()
                .perform(requestBuilder);
        actualPerformResult.andExpect(MockMvcResultMatchers.status().is(400));
    }

    /**
     * Method under test:
     * {@link DepositController#createDepositItem(DepositCreateReqDTO)}
     */
    @Test
    public void testCreateDepositItem3() throws Exception {
        when(iHsdDepositService.createDepositRecord(Mockito.<DepositCreateReqDTO>any())).thenReturn(true);

        GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
        goodsRespDTO.setDepositNum(10);
        goodsRespDTO.setExpireTime("deposit");
        goodsRespDTO.setGoodsName("deposit");
        goodsRespDTO.setGoodsUnit("deposit");
        goodsRespDTO.setGuid("1234");
        goodsRespDTO.setResidueDay(1);
        goodsRespDTO.setResidueNum(1);
        goodsRespDTO.setSkuGuid("1234");
        goodsRespDTO.setSkuName("deposit");
        goodsRespDTO.setStorePosition("deposit");
        goodsRespDTO.setTakeOutNum(10);

        ArrayList<GoodsRespDTO> goods = new ArrayList<>();
        goods.add(goodsRespDTO);

        DepositCreateReqDTO depositCreateReqDTO = new DepositCreateReqDTO();
        depositCreateReqDTO.setCustomerName("Customer Name");
        depositCreateReqDTO.setGoods(goods);
        depositCreateReqDTO.setHeadPortrait("Head Portrait");
        depositCreateReqDTO.setMemberGuid("1234");
        depositCreateReqDTO.setPhoneNum("**********");
        depositCreateReqDTO.setRemark("Remark");
        depositCreateReqDTO.setStoreGuid("1234");
        String content = (new ObjectMapper()).writeValueAsString(depositCreateReqDTO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/deposit/create_deposit_item")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);
        ResultActions resultActions = MockMvcBuilders.standaloneSetup(depositController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json;charset=UTF-8"));
        ContentResultMatchers contentResult = MockMvcResultMatchers.content();
        resultActions.andExpect(contentResult.string(Boolean.TRUE.toString()));
    }

    /**
     * Method under test: {@link DepositController#depositExpireRemind()}
     */
    @Test
    public void testDepositExpireRemind() throws Exception {
        doNothing().when(iHsdDepositService).sendExpireRemindMessage();
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/deposit/deposit_expire_remind");
        ResultActions resultActions = MockMvcBuilders.standaloneSetup(depositController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json;charset=UTF-8"));
        ContentResultMatchers contentResult = MockMvcResultMatchers.content();
        resultActions.andExpect(contentResult.string(Boolean.TRUE.toString()));
    }

    /**
     * Method under test: {@link DepositController#depositExpireRemind()}
     */
    @Test
    public void testDepositExpireRemind2() throws Exception {
        doNothing().when(iHsdDepositService).sendExpireRemindMessage();
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/deposit/deposit_expire_remind");
        requestBuilder.characterEncoding("Encoding");
        ResultActions resultActions = MockMvcBuilders.standaloneSetup(depositController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json;charset=UTF-8"));
        ContentResultMatchers contentResult = MockMvcResultMatchers.content();
        resultActions.andExpect(contentResult.string(Boolean.TRUE.toString()));
    }

    /**
     * Method under test:
     * {@link DepositController#getDepositGoods(DepositGetReqDTO)}
     */
    @Test
    public void testGetDepositGoods() throws Exception {
        when(iHsdDepositService.getDeposit(Mockito.<DepositGetReqDTO>any())).thenReturn(true);

        DepositGetReqDTO depositGetReqDTO = new DepositGetReqDTO();
        depositGetReqDTO.setDepositGuid("1234");
        depositGetReqDTO.setGoodsList(new ArrayList<>());
        depositGetReqDTO.setRemark("Remark");
        depositGetReqDTO.setUserGuid("1234");
        String content = (new ObjectMapper()).writeValueAsString(depositGetReqDTO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/deposit/get_deposit_goods")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);
        ResultActions resultActions = MockMvcBuilders.standaloneSetup(depositController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json;charset=UTF-8"));
        ContentResultMatchers contentResult = MockMvcResultMatchers.content();
        resultActions.andExpect(contentResult.string(Boolean.TRUE.toString()));
    }

    /**
     * Method under test:
     * {@link DepositController#queryDepositItem(DepositQueryReqDTO)}
     */
    @Test
    public void testQueryDepositItem() throws Exception {
        when(iHsdDepositService.queryDepositRecord(Mockito.<DepositQueryReqDTO>any())).thenReturn(new Page<>(1L, 3L));

        DepositQueryReqDTO depositQueryReqDTO = new DepositQueryReqDTO();
        depositQueryReqDTO.setAccount("3");
        depositQueryReqDTO.setAccountName("Dr Jane Doe");
        depositQueryReqDTO.setActuallyPayFee(new BigDecimal("2.3"));
        depositQueryReqDTO.setCondition("Condition");
        depositQueryReqDTO.setCurrentPage(1);
        depositQueryReqDTO.setDeviceId("42");
        depositQueryReqDTO.setDeviceType(1);
        depositQueryReqDTO.setEnterpriseGuid("1234");
        depositQueryReqDTO.setEnterpriseName("Enterprise Name");
        depositQueryReqDTO.setInvoiceCode("Invoice Code");
        depositQueryReqDTO.setInvoicePhone("**********");
        depositQueryReqDTO.setIsInvoiceCode(1);
        depositQueryReqDTO.setLatitude("Latitude");
        depositQueryReqDTO.setLongitude("Longitude");
        depositQueryReqDTO.setMaxId(1L);
        depositQueryReqDTO.setOperSubjectGuid("1234");
        depositQueryReqDTO.setPageSize(3);
        depositQueryReqDTO.setPhoneGuid("**********");
        depositQueryReqDTO.setRequestTimestamp(1L);
        depositQueryReqDTO.setStoreGuid("1234");
        depositQueryReqDTO.setStoreName("Store Name");
        depositQueryReqDTO.setUserGuid("1234");
        depositQueryReqDTO.setUserName("janedoe");
        depositQueryReqDTO.setWxGuid("1234");
        String content = (new ObjectMapper()).writeValueAsString(depositQueryReqDTO);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/deposit/query_deposit_item")
                .contentType(MediaType.APPLICATION_JSON)
                .content(content);
        MockMvcBuilders.standaloneSetup(depositController)
                .build()
                .perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType("application/json;charset=UTF-8"))
                .andExpect(
                        MockMvcResultMatchers.content().string("{\"currentPage\":1,\"pageSize\":3,\"totalCount\":0,\"data\":[]}"));
    }
}
