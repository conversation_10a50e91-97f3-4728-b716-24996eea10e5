package com.holderzone.holder.saas.store.deposit.service.impl;

import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.deposit.entity.bo.OperationDO;
import com.holderzone.holder.saas.store.deposit.mapstruct.OperationMapstruct;
import com.holderzone.holder.saas.store.deposit.service.DistributedIdService;
import com.holderzone.holder.saas.store.deposit.service.IHsdOperationGoodsService;
import com.holderzone.saas.store.dto.deposit.req.OperationCreateReqDTO;
import com.holderzone.saas.store.dto.deposit.req.OperationHistoryQueryReqDTO;
import com.holderzone.saas.store.dto.deposit.resp.GoodsSimpleRespDTO;
import com.holderzone.saas.store.dto.deposit.resp.OperationQueryRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsdOperationServiceImplTest {

    @Mock
    private IHsdOperationGoodsService mockIHsdOperationGoodsService;
    @Mock
    private DistributedIdService mockDistributedIdService;
    @Mock
    private OperationMapstruct mockOperationMapstruct;

    private HsdOperationServiceImpl hsdOperationServiceImplUnderTest;

    @Before
    public void setUp() {
        hsdOperationServiceImplUnderTest = new HsdOperationServiceImpl(mockIHsdOperationGoodsService,
                mockDistributedIdService, mockOperationMapstruct);
    }

    @Test
    public void testCreateOperationRecord() {
        // Setup
        final OperationCreateReqDTO operationCreateReqDTO = new OperationCreateReqDTO();
        operationCreateReqDTO.setOperationWay(0);
        operationCreateReqDTO.setUserId("userId");
        operationCreateReqDTO.setOperator("operator");
        operationCreateReqDTO.setRemark("remark");

        // Configure OperationMapstruct.fromOperationDTO(...).
        final OperationDO operationDO = new OperationDO();
        operationDO.setGuid("operationGuid");
        operationDO.setDepositGuid("depositGuid");
        operationDO.setOperator("operator");
        operationDO.setRemark("remark");
        operationDO.setOperationWay(0);
        operationDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        operationDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final OperationCreateReqDTO operationCreateReqDTO1 = new OperationCreateReqDTO();
        operationCreateReqDTO1.setOperationWay(0);
        operationCreateReqDTO1.setUserId("userId");
        operationCreateReqDTO1.setOperator("operator");
        operationCreateReqDTO1.setRemark("remark");
        when(mockOperationMapstruct.fromOperationDTO(operationCreateReqDTO1)).thenReturn(operationDO);

        // Run the test
        final Boolean result = hsdOperationServiceImplUnderTest.createOperationRecord(operationCreateReqDTO,
                "operationGuid", "depositGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testQueryOperationHistory() {
        // Setup
        final OperationHistoryQueryReqDTO operationHistoryQueryReqDTO = new OperationHistoryQueryReqDTO();
        operationHistoryQueryReqDTO.setDepositGuid("depositGuid");

        // Configure IHsdOperationGoodsService.queryGoodsOfOperation(...).
        final GoodsSimpleRespDTO goodsSimpleRespDTO = new GoodsSimpleRespDTO();
        goodsSimpleRespDTO.setGoodsName("goodsName");
        goodsSimpleRespDTO.setSkuName("skuName");
        goodsSimpleRespDTO.setResidueNum(0);
        goodsSimpleRespDTO.setOperatorNum(0);
        final List<GoodsSimpleRespDTO> goodsSimpleRespDTOS = Arrays.asList(goodsSimpleRespDTO);
        when(mockIHsdOperationGoodsService.queryGoodsOfOperation("operationGuid", "depositGuid"))
                .thenReturn(goodsSimpleRespDTOS);

        // Run the test
        final Page<OperationQueryRespDTO> result = hsdOperationServiceImplUnderTest.queryOperationHistory(
                operationHistoryQueryReqDTO);

        // Verify the results
    }

    @Test
    public void testQueryOperationHistory_IHsdOperationGoodsServiceReturnsNoItems() {
        // Setup
        final OperationHistoryQueryReqDTO operationHistoryQueryReqDTO = new OperationHistoryQueryReqDTO();
        operationHistoryQueryReqDTO.setDepositGuid("depositGuid");

        when(mockIHsdOperationGoodsService.queryGoodsOfOperation("operationGuid", "depositGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Page<OperationQueryRespDTO> result = hsdOperationServiceImplUnderTest.queryOperationHistory(
                operationHistoryQueryReqDTO);

        // Verify the results
    }
}
