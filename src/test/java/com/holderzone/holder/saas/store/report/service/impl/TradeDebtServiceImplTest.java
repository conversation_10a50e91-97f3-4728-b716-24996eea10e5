package com.holderzone.holder.saas.store.report.service.impl;

import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.holder.saas.store.report.mapper.TradeDebtMapper;
import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.DebtTotalStatisticsDTO;
import com.holderzone.saas.store.dto.report.resp.DebtUnitRecordDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TradeDebtServiceImplTest {

    @Mock
    private TradeDebtMapper mockTradeDebtMapper;
    @Mock
    private OssClient mockOssClient;

    private TradeDebtServiceImpl tradeDebtServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        tradeDebtServiceImplUnderTest = new TradeDebtServiceImpl(mockTradeDebtMapper, mockOssClient);
    }

    @Test
    public void testList() throws Exception {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeDebtMapper.statistics(...).
        final DebtTotalStatisticsDTO debtTotalStatisticsDTO = new DebtTotalStatisticsDTO(new BigDecimal("0.00"),
                new BigDecimal("0.00"), new BigDecimal("0.00"), 0);
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeDebtMapper.statistics(query1)).thenReturn(debtTotalStatisticsDTO);

        // Configure TradeDebtMapper.pageInfo(...).
        final DebtUnitRecordDTO debtUnitRecordDTO = new DebtUnitRecordDTO();
        debtUnitRecordDTO.setRepaymentStatus(0);
        debtUnitRecordDTO.setDebtFee(new BigDecimal("0.00"));
        debtUnitRecordDTO.setRepaymentFee(new BigDecimal("0.00"));
        debtUnitRecordDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        debtUnitRecordDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<DebtUnitRecordDTO> debtUnitRecordDTOS = Arrays.asList(debtUnitRecordDTO);
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeDebtMapper.pageInfo(query2)).thenReturn(debtUnitRecordDTOS);

        // Run the test
        final Message<DebtUnitRecordDTO> result = tradeDebtServiceImplUnderTest.list(query);

        // Verify the results
    }

    @Test
    public void testList_TradeDebtMapperPageInfoReturnsNoItems() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeDebtMapper.statistics(...).
        final DebtTotalStatisticsDTO debtTotalStatisticsDTO = new DebtTotalStatisticsDTO(new BigDecimal("0.00"),
                new BigDecimal("0.00"), new BigDecimal("0.00"), 0);
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeDebtMapper.statistics(query1)).thenReturn(debtTotalStatisticsDTO);

        // Configure TradeDebtMapper.pageInfo(...).
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeDebtMapper.pageInfo(query2)).thenReturn(Collections.emptyList());

        // Run the test
        final Message<DebtUnitRecordDTO> result = tradeDebtServiceImplUnderTest.list(query);

        // Verify the results
    }

    @Test
    public void testExport() throws Exception {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeDebtMapper.count(...).
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeDebtMapper.count(query1)).thenReturn(0);

        // Configure TradeDebtMapper.pageInfo(...).
        final DebtUnitRecordDTO debtUnitRecordDTO = new DebtUnitRecordDTO();
        debtUnitRecordDTO.setRepaymentStatus(0);
        debtUnitRecordDTO.setDebtFee(new BigDecimal("0.00"));
        debtUnitRecordDTO.setRepaymentFee(new BigDecimal("0.00"));
        debtUnitRecordDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        debtUnitRecordDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<DebtUnitRecordDTO> debtUnitRecordDTOS = Arrays.asList(debtUnitRecordDTO);
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeDebtMapper.pageInfo(query2)).thenReturn(debtUnitRecordDTOS);

        // Run the test
        final String result = tradeDebtServiceImplUnderTest.export(query);

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testExport_TradeDebtMapperPageInfoReturnsNoItems() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeDebtMapper.count(...).
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeDebtMapper.count(query1)).thenReturn(0);

        // Configure TradeDebtMapper.pageInfo(...).
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeDebtMapper.pageInfo(query2)).thenReturn(Collections.emptyList());

        // Run the test
        final String result = tradeDebtServiceImplUnderTest.export(query);

        // Verify the results
        assertEquals("result", result);
    }
}
