package com.holderzone.holder.saas.store.pay.service.impl;

import com.holderzone.framework.base.dto.file.FileDto;
import com.holderzone.holder.saas.store.pay.config.AggPayConfig;
import com.holderzone.holder.saas.store.pay.config.DeveloperConfig;
import com.holderzone.holder.saas.store.pay.entity.HandlerPayBO;
import com.holderzone.holder.saas.store.pay.service.RedisService;
import com.holderzone.holder.saas.store.pay.service.RocketService;
import com.holderzone.holder.saas.store.pay.service.rpc.AggPayRpcService;
import com.holderzone.holder.saas.store.pay.service.rpc.FileUploadRpcService;
import com.holderzone.holder.saas.store.pay.service.rpc.SaasResultRpcService;
import com.holderzone.saas.store.dto.pay.*;
import com.holderzone.saas.store.dto.trade.PaymentInfoDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PollingServiceImplTest {

    @Mock
    private DeveloperConfig mockDeveloperConfig;
    @Mock
    private AggPayConfig mockAggPayConfig;
    @Mock
    private RocketService mockRocketService;
    @Mock
    private RedisService mockRedisService;
    @Mock
    private AggPayRpcService mockAggPayRpcService;
    @Mock
    private FileUploadRpcService mockFileUploadRpcService;
    @Mock
    private SaasResultRpcService mockSaasResultRpcService;

    private PollingServiceImpl pollingServiceImplUnderTest;

    @Before
    public void setUp() {
        pollingServiceImplUnderTest = new PollingServiceImpl(mockDeveloperConfig, mockAggPayConfig, mockRocketService,
                mockRedisService, mockAggPayRpcService, mockFileUploadRpcService, mockSaasResultRpcService);
    }

    @Test
    public void testStartPrePayPolling() {
        // Setup
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("orderGuid");
        aggPayPollingDTO.setAttachData("attachData");
        aggPayPollingDTO.setRefundNo("refundNo");
        aggPayPollingDTO.setPayGUID("payGuid");
        aggPayPollingDTO.setAppId("appId");

        final HandlerPayBO handlerPayBO = HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build();

        // Configure RocketService.polling(...).
        final AggPayPollingDTO aggPayPollingDTO1 = new AggPayPollingDTO();
        aggPayPollingDTO1.setOrderGUID("orderGuid");
        aggPayPollingDTO1.setAttachData("attachData");
        aggPayPollingDTO1.setRefundNo("refundNo");
        aggPayPollingDTO1.setPayGUID("payGuid");
        aggPayPollingDTO1.setAppId("appId");
        when(mockRocketService.polling(aggPayPollingDTO1, HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build())).thenReturn(false);

        when(mockAggPayConfig.getPollingTimes()).thenReturn(0);

        // Configure AggPayRpcService.doPolling(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setMsg("msg");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("orderGuid");
        aggPayPollingRespDTO.setPayGUID("payGuid");
        aggPayPollingRespDTO.setCodeUrl("codeUrl");
        aggPayPollingRespDTO.setSignature("signature");
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("orderGuid");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("payGuid");
        pollingJHPayDTO.setAppId("appId");
        when(mockAggPayRpcService.doPolling(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTO);

        // Configure AggPayRpcService.doWeChatPublicAccountPolling(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO1 = new AggPayPollingRespDTO();
        aggPayPollingRespDTO1.setCode("code");
        aggPayPollingRespDTO1.setMsg("msg");
        aggPayPollingRespDTO1.setPaySt("paySt");
        aggPayPollingRespDTO1.setOrderGUID("orderGuid");
        aggPayPollingRespDTO1.setPayGUID("payGuid");
        aggPayPollingRespDTO1.setCodeUrl("codeUrl");
        aggPayPollingRespDTO1.setSignature("signature");
        when(mockAggPayRpcService.doWeChatPublicAccountPolling("orderGuid")).thenReturn(aggPayPollingRespDTO1);

        when(mockDeveloperConfig.getKey()).thenReturn("result");
        when(mockRedisService.getCallBackResp("orderGuid", "payGuid")).thenReturn(Mono.just("value"));
        when(mockRedisService.getQrCodeDownloadUrl("orderGuid")).thenReturn("result");
        when(mockFileUploadRpcService.upload(any(FileDto.class))).thenReturn("downLoadUrl");

        // Configure RedisService.getPollingResp(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO2 = new AggPayPollingRespDTO();
        aggPayPollingRespDTO2.setCode("code");
        aggPayPollingRespDTO2.setMsg("msg");
        aggPayPollingRespDTO2.setPaySt("paySt");
        aggPayPollingRespDTO2.setOrderGUID("orderGuid");
        aggPayPollingRespDTO2.setPayGUID("payGuid");
        aggPayPollingRespDTO2.setCodeUrl("codeUrl");
        aggPayPollingRespDTO2.setSignature("signature");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO2);
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setOrderGuid("orderGuid");
        saasPollingDTO.setPayGuid("payGuid");
        when(mockRedisService.getPollingResp(saasPollingDTO)).thenReturn(aggPayPollingRespDTOMono);

        // Run the test
        pollingServiceImplUnderTest.startPrePayPolling(aggPayPollingDTO, handlerPayBO);

        // Verify the results
        verify(mockRedisService).putCallBackResp("orderGuid", "payGuid", "paySt");
        verify(mockRedisService).putQrCodeDownloadUrl("orderGuid", "downLoadUrl");

        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("code");
        pollingRespDTO.setMsg("msg");
        pollingRespDTO.setPaySt("paySt");
        pollingRespDTO.setOrderGUID("orderGuid");
        pollingRespDTO.setPayGUID("payGuid");
        pollingRespDTO.setCodeUrl("codeUrl");
        pollingRespDTO.setSignature("signature");
        verify(mockRedisService).putPollingResp("orderGuid", "payGuid", pollingRespDTO);

        // Confirm SaasResultRpcService.handlePayResult(...).
        final AggPayPollingRespDTO pollingRespDTO1 = new AggPayPollingRespDTO();
        pollingRespDTO1.setCode("code");
        pollingRespDTO1.setMsg("msg");
        pollingRespDTO1.setPaySt("paySt");
        pollingRespDTO1.setOrderGUID("orderGuid");
        pollingRespDTO1.setPayGUID("payGuid");
        pollingRespDTO1.setCodeUrl("codeUrl");
        pollingRespDTO1.setSignature("signature");
        verify(mockSaasResultRpcService).handlePayResult(HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build(), pollingRespDTO1);
    }

    @Test
    public void testStartPrePayPolling_RocketServiceReturnsTrue() {
        // Setup
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("orderGuid");
        aggPayPollingDTO.setAttachData("attachData");
        aggPayPollingDTO.setRefundNo("refundNo");
        aggPayPollingDTO.setPayGUID("payGuid");
        aggPayPollingDTO.setAppId("appId");

        final HandlerPayBO handlerPayBO = HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build();

        // Configure RocketService.polling(...).
        final AggPayPollingDTO aggPayPollingDTO1 = new AggPayPollingDTO();
        aggPayPollingDTO1.setOrderGUID("orderGuid");
        aggPayPollingDTO1.setAttachData("attachData");
        aggPayPollingDTO1.setRefundNo("refundNo");
        aggPayPollingDTO1.setPayGUID("payGuid");
        aggPayPollingDTO1.setAppId("appId");
        when(mockRocketService.polling(aggPayPollingDTO1, HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build())).thenReturn(true);

        // Run the test
        pollingServiceImplUnderTest.startPrePayPolling(aggPayPollingDTO, handlerPayBO);

        // Verify the results
    }

    @Test
    public void testStartPrePayPolling_AggPayRpcServiceDoPollingReturnsError() {
        // Setup
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("orderGuid");
        aggPayPollingDTO.setAttachData("attachData");
        aggPayPollingDTO.setRefundNo("refundNo");
        aggPayPollingDTO.setPayGUID("payGuid");
        aggPayPollingDTO.setAppId("appId");

        final HandlerPayBO handlerPayBO = HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build();

        // Configure RocketService.polling(...).
        final AggPayPollingDTO aggPayPollingDTO1 = new AggPayPollingDTO();
        aggPayPollingDTO1.setOrderGUID("orderGuid");
        aggPayPollingDTO1.setAttachData("attachData");
        aggPayPollingDTO1.setRefundNo("refundNo");
        aggPayPollingDTO1.setPayGUID("payGuid");
        aggPayPollingDTO1.setAppId("appId");
        when(mockRocketService.polling(aggPayPollingDTO1, HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build())).thenReturn(false);

        when(mockAggPayConfig.getPollingTimes()).thenReturn(0);

        // Configure AggPayRpcService.doPolling(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = AggPayPollingRespDTO.errorResp("code", "msg");
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("orderGuid");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("payGuid");
        pollingJHPayDTO.setAppId("appId");
        when(mockAggPayRpcService.doPolling(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTO);

        when(mockDeveloperConfig.getKey()).thenReturn("result");
        when(mockRedisService.getCallBackResp("orderGuid", "payGuid")).thenReturn(Mono.just("value"));

        // Run the test
        pollingServiceImplUnderTest.startPrePayPolling(aggPayPollingDTO, handlerPayBO);

        // Verify the results
        verify(mockRedisService).putCallBackResp("orderGuid", "payGuid", "paySt");
    }

    @Test
    public void testStartPrePayPolling_AggPayRpcServiceDoWeChatPublicAccountPollingReturnsError() {
        // Setup
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("orderGuid");
        aggPayPollingDTO.setAttachData("attachData");
        aggPayPollingDTO.setRefundNo("refundNo");
        aggPayPollingDTO.setPayGUID("payGuid");
        aggPayPollingDTO.setAppId("appId");

        final HandlerPayBO handlerPayBO = HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build();

        // Configure RocketService.polling(...).
        final AggPayPollingDTO aggPayPollingDTO1 = new AggPayPollingDTO();
        aggPayPollingDTO1.setOrderGUID("orderGuid");
        aggPayPollingDTO1.setAttachData("attachData");
        aggPayPollingDTO1.setRefundNo("refundNo");
        aggPayPollingDTO1.setPayGUID("payGuid");
        aggPayPollingDTO1.setAppId("appId");
        when(mockRocketService.polling(aggPayPollingDTO1, HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build())).thenReturn(false);

        when(mockAggPayConfig.getPollingTimes()).thenReturn(0);

        // Configure AggPayRpcService.doWeChatPublicAccountPolling(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = AggPayPollingRespDTO.errorResp("code", "msg");
        when(mockAggPayRpcService.doWeChatPublicAccountPolling("orderGuid")).thenReturn(aggPayPollingRespDTO);

        when(mockDeveloperConfig.getKey()).thenReturn("result");
        when(mockRedisService.getCallBackResp("orderGuid", "payGuid")).thenReturn(Mono.just("value"));

        // Run the test
        pollingServiceImplUnderTest.startPrePayPolling(aggPayPollingDTO, handlerPayBO);

        // Verify the results
        verify(mockRedisService).putCallBackResp("orderGuid", "payGuid", "paySt");
    }

    @Test
    public void testStartPrePayPolling_RedisServiceGetCallBackRespReturnsNoItem() {
        // Setup
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("orderGuid");
        aggPayPollingDTO.setAttachData("attachData");
        aggPayPollingDTO.setRefundNo("refundNo");
        aggPayPollingDTO.setPayGUID("payGuid");
        aggPayPollingDTO.setAppId("appId");

        final HandlerPayBO handlerPayBO = HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build();

        // Configure RocketService.polling(...).
        final AggPayPollingDTO aggPayPollingDTO1 = new AggPayPollingDTO();
        aggPayPollingDTO1.setOrderGUID("orderGuid");
        aggPayPollingDTO1.setAttachData("attachData");
        aggPayPollingDTO1.setRefundNo("refundNo");
        aggPayPollingDTO1.setPayGUID("payGuid");
        aggPayPollingDTO1.setAppId("appId");
        when(mockRocketService.polling(aggPayPollingDTO1, HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build())).thenReturn(false);

        when(mockAggPayConfig.getPollingTimes()).thenReturn(0);

        // Configure AggPayRpcService.doPolling(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setMsg("msg");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("orderGuid");
        aggPayPollingRespDTO.setPayGUID("payGuid");
        aggPayPollingRespDTO.setCodeUrl("codeUrl");
        aggPayPollingRespDTO.setSignature("signature");
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("orderGuid");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("payGuid");
        pollingJHPayDTO.setAppId("appId");
        when(mockAggPayRpcService.doPolling(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTO);

        when(mockDeveloperConfig.getKey()).thenReturn("result");
        when(mockRedisService.getCallBackResp("orderGuid", "payGuid")).thenReturn(Mono.empty());

        // Run the test
        pollingServiceImplUnderTest.startPrePayPolling(aggPayPollingDTO, handlerPayBO);

        // Verify the results
        verify(mockRedisService).putCallBackResp("orderGuid", "payGuid", "paySt");
    }

    @Test
    public void testStartPrePayPolling_RedisServiceGetCallBackRespReturnsError() {
        // Setup
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("orderGuid");
        aggPayPollingDTO.setAttachData("attachData");
        aggPayPollingDTO.setRefundNo("refundNo");
        aggPayPollingDTO.setPayGUID("payGuid");
        aggPayPollingDTO.setAppId("appId");

        final HandlerPayBO handlerPayBO = HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build();

        // Configure RocketService.polling(...).
        final AggPayPollingDTO aggPayPollingDTO1 = new AggPayPollingDTO();
        aggPayPollingDTO1.setOrderGUID("orderGuid");
        aggPayPollingDTO1.setAttachData("attachData");
        aggPayPollingDTO1.setRefundNo("refundNo");
        aggPayPollingDTO1.setPayGUID("payGuid");
        aggPayPollingDTO1.setAppId("appId");
        when(mockRocketService.polling(aggPayPollingDTO1, HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build())).thenReturn(false);

        when(mockAggPayConfig.getPollingTimes()).thenReturn(0);

        // Configure AggPayRpcService.doPolling(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setMsg("msg");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("orderGuid");
        aggPayPollingRespDTO.setPayGUID("payGuid");
        aggPayPollingRespDTO.setCodeUrl("codeUrl");
        aggPayPollingRespDTO.setSignature("signature");
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("orderGuid");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("payGuid");
        pollingJHPayDTO.setAppId("appId");
        when(mockAggPayRpcService.doPolling(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTO);

        when(mockDeveloperConfig.getKey()).thenReturn("result");
        when(mockRedisService.getCallBackResp("orderGuid", "payGuid")).thenReturn(Mono.error(new Exception("message")));

        // Run the test
        pollingServiceImplUnderTest.startPrePayPolling(aggPayPollingDTO, handlerPayBO);

        // Verify the results
        verify(mockRedisService).putCallBackResp("orderGuid", "payGuid", "paySt");
    }

    @Test
    public void testStartPrePayPolling_RedisServiceGetPollingRespReturnsNoItem() {
        // Setup
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("orderGuid");
        aggPayPollingDTO.setAttachData("attachData");
        aggPayPollingDTO.setRefundNo("refundNo");
        aggPayPollingDTO.setPayGUID("payGuid");
        aggPayPollingDTO.setAppId("appId");

        final HandlerPayBO handlerPayBO = HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build();

        // Configure RocketService.polling(...).
        final AggPayPollingDTO aggPayPollingDTO1 = new AggPayPollingDTO();
        aggPayPollingDTO1.setOrderGUID("orderGuid");
        aggPayPollingDTO1.setAttachData("attachData");
        aggPayPollingDTO1.setRefundNo("refundNo");
        aggPayPollingDTO1.setPayGUID("payGuid");
        aggPayPollingDTO1.setAppId("appId");
        when(mockRocketService.polling(aggPayPollingDTO1, HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build())).thenReturn(false);

        when(mockAggPayConfig.getPollingTimes()).thenReturn(0);

        // Configure AggPayRpcService.doPolling(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setMsg("msg");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("orderGuid");
        aggPayPollingRespDTO.setPayGUID("payGuid");
        aggPayPollingRespDTO.setCodeUrl("codeUrl");
        aggPayPollingRespDTO.setSignature("signature");
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("orderGuid");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("payGuid");
        pollingJHPayDTO.setAppId("appId");
        when(mockAggPayRpcService.doPolling(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTO);

        when(mockDeveloperConfig.getKey()).thenReturn("result");
        when(mockRedisService.getCallBackResp("orderGuid", "payGuid")).thenReturn(Mono.just("value"));
        when(mockRedisService.getQrCodeDownloadUrl("orderGuid")).thenReturn("result");
        when(mockFileUploadRpcService.upload(any(FileDto.class))).thenReturn("downLoadUrl");

        // Configure RedisService.getPollingResp(...).
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setOrderGuid("orderGuid");
        saasPollingDTO.setPayGuid("payGuid");
        when(mockRedisService.getPollingResp(saasPollingDTO)).thenReturn(Mono.empty());

        // Run the test
        pollingServiceImplUnderTest.startPrePayPolling(aggPayPollingDTO, handlerPayBO);

        // Verify the results
        verify(mockRedisService).putCallBackResp("orderGuid", "payGuid", "paySt");
        verify(mockRedisService).putQrCodeDownloadUrl("orderGuid", "downLoadUrl");

        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("code");
        pollingRespDTO.setMsg("msg");
        pollingRespDTO.setPaySt("paySt");
        pollingRespDTO.setOrderGUID("orderGuid");
        pollingRespDTO.setPayGUID("payGuid");
        pollingRespDTO.setCodeUrl("codeUrl");
        pollingRespDTO.setSignature("signature");
        verify(mockRedisService).putPollingResp("orderGuid", "payGuid", pollingRespDTO);

        // Confirm SaasResultRpcService.handlePayResult(...).
        final AggPayPollingRespDTO pollingRespDTO1 = new AggPayPollingRespDTO();
        pollingRespDTO1.setCode("code");
        pollingRespDTO1.setMsg("msg");
        pollingRespDTO1.setPaySt("paySt");
        pollingRespDTO1.setOrderGUID("orderGuid");
        pollingRespDTO1.setPayGUID("payGuid");
        pollingRespDTO1.setCodeUrl("codeUrl");
        pollingRespDTO1.setSignature("signature");
        verify(mockSaasResultRpcService).handlePayResult(HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build(), pollingRespDTO1);
    }

    @Test
    public void testStartPrePayPolling_RedisServiceGetPollingRespReturnsError() {
        // Setup
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("orderGuid");
        aggPayPollingDTO.setAttachData("attachData");
        aggPayPollingDTO.setRefundNo("refundNo");
        aggPayPollingDTO.setPayGUID("payGuid");
        aggPayPollingDTO.setAppId("appId");

        final HandlerPayBO handlerPayBO = HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build();

        // Configure RocketService.polling(...).
        final AggPayPollingDTO aggPayPollingDTO1 = new AggPayPollingDTO();
        aggPayPollingDTO1.setOrderGUID("orderGuid");
        aggPayPollingDTO1.setAttachData("attachData");
        aggPayPollingDTO1.setRefundNo("refundNo");
        aggPayPollingDTO1.setPayGUID("payGuid");
        aggPayPollingDTO1.setAppId("appId");
        when(mockRocketService.polling(aggPayPollingDTO1, HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build())).thenReturn(false);

        when(mockAggPayConfig.getPollingTimes()).thenReturn(0);

        // Configure AggPayRpcService.doPolling(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setMsg("msg");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("orderGuid");
        aggPayPollingRespDTO.setPayGUID("payGuid");
        aggPayPollingRespDTO.setCodeUrl("codeUrl");
        aggPayPollingRespDTO.setSignature("signature");
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("orderGuid");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("payGuid");
        pollingJHPayDTO.setAppId("appId");
        when(mockAggPayRpcService.doPolling(pollingJHPayDTO)).thenReturn(aggPayPollingRespDTO);

        when(mockDeveloperConfig.getKey()).thenReturn("result");
        when(mockRedisService.getCallBackResp("orderGuid", "payGuid")).thenReturn(Mono.just("value"));
        when(mockRedisService.getQrCodeDownloadUrl("orderGuid")).thenReturn("result");
        when(mockFileUploadRpcService.upload(any(FileDto.class))).thenReturn("downLoadUrl");

        // Configure RedisService.getPollingResp(...).
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.error(new Exception("message"));
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setOrderGuid("orderGuid");
        saasPollingDTO.setPayGuid("payGuid");
        when(mockRedisService.getPollingResp(saasPollingDTO)).thenReturn(aggPayPollingRespDTOMono);

        // Run the test
        pollingServiceImplUnderTest.startPrePayPolling(aggPayPollingDTO, handlerPayBO);

        // Verify the results
        verify(mockRedisService).putCallBackResp("orderGuid", "payGuid", "paySt");
        verify(mockRedisService).putQrCodeDownloadUrl("orderGuid", "downLoadUrl");

        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("code");
        pollingRespDTO.setMsg("msg");
        pollingRespDTO.setPaySt("paySt");
        pollingRespDTO.setOrderGUID("orderGuid");
        pollingRespDTO.setPayGUID("payGuid");
        pollingRespDTO.setCodeUrl("codeUrl");
        pollingRespDTO.setSignature("signature");
        verify(mockRedisService).putPollingResp("orderGuid", "payGuid", pollingRespDTO);

        // Confirm SaasResultRpcService.handlePayResult(...).
        final AggPayPollingRespDTO pollingRespDTO1 = new AggPayPollingRespDTO();
        pollingRespDTO1.setCode("code");
        pollingRespDTO1.setMsg("msg");
        pollingRespDTO1.setPaySt("paySt");
        pollingRespDTO1.setOrderGUID("orderGuid");
        pollingRespDTO1.setPayGUID("payGuid");
        pollingRespDTO1.setCodeUrl("codeUrl");
        pollingRespDTO1.setSignature("signature");
        verify(mockSaasResultRpcService).handlePayResult(HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build(), pollingRespDTO1);
    }

    @Test
    public void testCompareStatWithCache() {
        // Setup
        when(mockRedisService.getCallBackResp("orderGuid", "payGuid")).thenReturn(Mono.just("value"));

        // Run the test
        final Mono<String> result = pollingServiceImplUnderTest.compareStatWithCache("orderGuid", "payGuid", "paySt");

        // Verify the results
        verify(mockRedisService).putCallBackResp("orderGuid", "payGuid", "paySt");
    }

    @Test
    public void testCompareStatWithCache_RedisServiceGetCallBackRespReturnsNoItem() {
        // Setup
        when(mockRedisService.getCallBackResp("orderGuid", "payGuid")).thenReturn(Mono.empty());

        // Run the test
        final Mono<String> result = pollingServiceImplUnderTest.compareStatWithCache("orderGuid", "payGuid", "paySt");

        // Verify the results
        assertThat(result).isNull();
        verify(mockRedisService).putCallBackResp("orderGuid", "payGuid", "paySt");
    }

    @Test
    public void testCompareStatWithCache_RedisServiceGetCallBackRespReturnsError() {
        // Setup
        when(mockRedisService.getCallBackResp("orderGuid", "payGuid")).thenReturn(Mono.error(new Exception("message")));

        // Run the test
        final Mono<String> result = pollingServiceImplUnderTest.compareStatWithCache("orderGuid", "payGuid", "paySt");

        // Verify the results
        verify(mockRedisService).putCallBackResp("orderGuid", "payGuid", "paySt");
    }

    @Test
    public void testStartRefundPolling() {
        // Setup
        final SaasAggRefundDTO saasAggRefundDTO = new SaasAggRefundDTO();
        final AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
        aggRefundReqDTO.setPayGUID("payGUID");
        aggRefundReqDTO.setOrderGUID("orderGUID");
        aggRefundReqDTO.setRefundType(0);
        aggRefundReqDTO.setRefundFee(new BigDecimal("0.00"));
        saasAggRefundDTO.setAggRefundReqDTO(aggRefundReqDTO);

        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("ae45f2d7-46b4-45b6-b8a2-382d289dca62");
        aggRefundPollingDTO.setAttachData("attachData");
        aggRefundPollingDTO.setRefundNo("refundNo");
        aggRefundPollingDTO.setPayGUID("9d8ffac8-627d-48f4-9124-50712fb07be8");
        aggRefundPollingDTO.setAppId("appId");

        when(mockAggPayConfig.getPollingTimes()).thenReturn(0);

        // Configure AggPayRpcService.doRefundPolling(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode("code");
        aggRefundPollingRespDTO.setMsg("msg");
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        final AggRefundDetailRespDTO aggRefundDetailRespDTO = new AggRefundDetailRespDTO();
        aggRefundDetailRespDTO.setStatus("status");
        aggRefundPollingRespDTO.setRefundOrderDetial(Arrays.asList(aggRefundDetailRespDTO));
        final AggRefundPollingDTO aggRefundPollingDTO1 = new AggRefundPollingDTO();
        aggRefundPollingDTO1.setOrderGUID("ae45f2d7-46b4-45b6-b8a2-382d289dca62");
        aggRefundPollingDTO1.setAttachData("attachData");
        aggRefundPollingDTO1.setRefundNo("refundNo");
        aggRefundPollingDTO1.setPayGUID("9d8ffac8-627d-48f4-9124-50712fb07be8");
        aggRefundPollingDTO1.setAppId("appId");
        when(mockAggPayRpcService.doRefundPolling(aggRefundPollingDTO1)).thenReturn(aggRefundPollingRespDTO);

        // Run the test
        pollingServiceImplUnderTest.startRefundPolling(saasAggRefundDTO, aggRefundPollingDTO);

        // Verify the results
        // Confirm RedisService.putRefundPollingResp(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO1 = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO1.setCode("code");
        aggRefundPollingRespDTO1.setMsg("msg");
        aggRefundPollingRespDTO1.setMchntOrderNo("mchntOrderNo");
        final AggRefundDetailRespDTO aggRefundDetailRespDTO1 = new AggRefundDetailRespDTO();
        aggRefundDetailRespDTO1.setStatus("status");
        aggRefundPollingRespDTO1.setRefundOrderDetial(Arrays.asList(aggRefundDetailRespDTO1));
        verify(mockRedisService).putRefundPollingResp("orderGUID", "payGUID", aggRefundPollingRespDTO1);

        // Confirm SaasResultRpcService.handleRefundResult(...).
        final SaasAggRefundDTO saasAggRefundDTO1 = new SaasAggRefundDTO();
        final AggRefundReqDTO aggRefundReqDTO1 = new AggRefundReqDTO();
        aggRefundReqDTO1.setPayGUID("payGUID");
        aggRefundReqDTO1.setOrderGUID("orderGUID");
        aggRefundReqDTO1.setRefundType(0);
        aggRefundReqDTO1.setRefundFee(new BigDecimal("0.00"));
        saasAggRefundDTO1.setAggRefundReqDTO(aggRefundReqDTO1);
        final AggRefundPollingRespDTO aggRefundPollingRespDTO2 = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO2.setCode("code");
        aggRefundPollingRespDTO2.setMsg("msg");
        aggRefundPollingRespDTO2.setMchntOrderNo("mchntOrderNo");
        final AggRefundDetailRespDTO aggRefundDetailRespDTO2 = new AggRefundDetailRespDTO();
        aggRefundDetailRespDTO2.setStatus("status");
        aggRefundPollingRespDTO2.setRefundOrderDetial(Arrays.asList(aggRefundDetailRespDTO2));
        verify(mockSaasResultRpcService).handleRefundResult(saasAggRefundDTO1, aggRefundPollingRespDTO2);
    }

    @Test
    public void testStartRefundPolling_AggPayRpcServiceReturnsError() {
        // Setup
        final SaasAggRefundDTO saasAggRefundDTO = new SaasAggRefundDTO();
        final AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
        aggRefundReqDTO.setPayGUID("payGUID");
        aggRefundReqDTO.setOrderGUID("orderGUID");
        aggRefundReqDTO.setRefundType(0);
        aggRefundReqDTO.setRefundFee(new BigDecimal("0.00"));
        saasAggRefundDTO.setAggRefundReqDTO(aggRefundReqDTO);

        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("ae45f2d7-46b4-45b6-b8a2-382d289dca62");
        aggRefundPollingDTO.setAttachData("attachData");
        aggRefundPollingDTO.setRefundNo("refundNo");
        aggRefundPollingDTO.setPayGUID("9d8ffac8-627d-48f4-9124-50712fb07be8");
        aggRefundPollingDTO.setAppId("appId");

        when(mockAggPayConfig.getPollingTimes()).thenReturn(0);

        // Configure AggPayRpcService.doRefundPolling(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = AggRefundPollingRespDTO.errorResp("code", "msg");
        final AggRefundPollingDTO aggRefundPollingDTO1 = new AggRefundPollingDTO();
        aggRefundPollingDTO1.setOrderGUID("ae45f2d7-46b4-45b6-b8a2-382d289dca62");
        aggRefundPollingDTO1.setAttachData("attachData");
        aggRefundPollingDTO1.setRefundNo("refundNo");
        aggRefundPollingDTO1.setPayGUID("9d8ffac8-627d-48f4-9124-50712fb07be8");
        aggRefundPollingDTO1.setAppId("appId");
        when(mockAggPayRpcService.doRefundPolling(aggRefundPollingDTO1)).thenReturn(aggRefundPollingRespDTO);

        // Run the test
        pollingServiceImplUnderTest.startRefundPolling(saasAggRefundDTO, aggRefundPollingDTO);

        // Verify the results
        // Confirm RedisService.putRefundPollingResp(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO1 = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO1.setCode("code");
        aggRefundPollingRespDTO1.setMsg("msg");
        aggRefundPollingRespDTO1.setMchntOrderNo("mchntOrderNo");
        final AggRefundDetailRespDTO aggRefundDetailRespDTO = new AggRefundDetailRespDTO();
        aggRefundDetailRespDTO.setStatus("status");
        aggRefundPollingRespDTO1.setRefundOrderDetial(Arrays.asList(aggRefundDetailRespDTO));
        verify(mockRedisService).putRefundPollingResp("orderGUID", "payGUID", aggRefundPollingRespDTO1);

        // Confirm SaasResultRpcService.handleRefundResult(...).
        final SaasAggRefundDTO saasAggRefundDTO1 = new SaasAggRefundDTO();
        final AggRefundReqDTO aggRefundReqDTO1 = new AggRefundReqDTO();
        aggRefundReqDTO1.setPayGUID("payGUID");
        aggRefundReqDTO1.setOrderGUID("orderGUID");
        aggRefundReqDTO1.setRefundType(0);
        aggRefundReqDTO1.setRefundFee(new BigDecimal("0.00"));
        saasAggRefundDTO1.setAggRefundReqDTO(aggRefundReqDTO1);
        final AggRefundPollingRespDTO aggRefundPollingRespDTO2 = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO2.setCode("code");
        aggRefundPollingRespDTO2.setMsg("msg");
        aggRefundPollingRespDTO2.setMchntOrderNo("mchntOrderNo");
        final AggRefundDetailRespDTO aggRefundDetailRespDTO1 = new AggRefundDetailRespDTO();
        aggRefundDetailRespDTO1.setStatus("status");
        aggRefundPollingRespDTO2.setRefundOrderDetial(Arrays.asList(aggRefundDetailRespDTO1));
        verify(mockSaasResultRpcService).handleRefundResult(saasAggRefundDTO1, aggRefundPollingRespDTO2);
    }

    @Test
    public void testHandlePollingResult() {
        // Setup
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("orderGuid");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("payGuid");
        pollingJHPayDTO.setAppId("appId");

        final HandlerPayBO handlerPayBO = HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build();

        // Configure AggPayRpcService.doPolling(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setMsg("msg");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("orderGuid");
        aggPayPollingRespDTO.setPayGUID("payGuid");
        aggPayPollingRespDTO.setCodeUrl("codeUrl");
        aggPayPollingRespDTO.setSignature("signature");
        final AggPayPollingDTO pollingJHPayDTO1 = new AggPayPollingDTO();
        pollingJHPayDTO1.setOrderGUID("orderGuid");
        pollingJHPayDTO1.setAttachData("attachData");
        pollingJHPayDTO1.setRefundNo("refundNo");
        pollingJHPayDTO1.setPayGUID("payGuid");
        pollingJHPayDTO1.setAppId("appId");
        when(mockAggPayRpcService.doPolling(pollingJHPayDTO1)).thenReturn(aggPayPollingRespDTO);

        // Configure AggPayRpcService.doWeChatPublicAccountPolling(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO1 = new AggPayPollingRespDTO();
        aggPayPollingRespDTO1.setCode("code");
        aggPayPollingRespDTO1.setMsg("msg");
        aggPayPollingRespDTO1.setPaySt("paySt");
        aggPayPollingRespDTO1.setOrderGUID("orderGuid");
        aggPayPollingRespDTO1.setPayGUID("payGuid");
        aggPayPollingRespDTO1.setCodeUrl("codeUrl");
        aggPayPollingRespDTO1.setSignature("signature");
        when(mockAggPayRpcService.doWeChatPublicAccountPolling("orderGuid")).thenReturn(aggPayPollingRespDTO1);

        when(mockDeveloperConfig.getKey()).thenReturn("result");
        when(mockRedisService.getCallBackResp("orderGuid", "payGuid")).thenReturn(Mono.just("value"));
        when(mockRedisService.getQrCodeDownloadUrl("orderGuid")).thenReturn("result");
        when(mockFileUploadRpcService.upload(any(FileDto.class))).thenReturn("downLoadUrl");

        // Configure RedisService.getPollingResp(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO2 = new AggPayPollingRespDTO();
        aggPayPollingRespDTO2.setCode("code");
        aggPayPollingRespDTO2.setMsg("msg");
        aggPayPollingRespDTO2.setPaySt("paySt");
        aggPayPollingRespDTO2.setOrderGUID("orderGuid");
        aggPayPollingRespDTO2.setPayGUID("payGuid");
        aggPayPollingRespDTO2.setCodeUrl("codeUrl");
        aggPayPollingRespDTO2.setSignature("signature");
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.just(aggPayPollingRespDTO2);
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setOrderGuid("orderGuid");
        saasPollingDTO.setPayGuid("payGuid");
        when(mockRedisService.getPollingResp(saasPollingDTO)).thenReturn(aggPayPollingRespDTOMono);

        // Run the test
        final boolean result = pollingServiceImplUnderTest.handlePollingResult(pollingJHPayDTO, handlerPayBO, false);

        // Verify the results
        assertThat(result).isFalse();

        // Confirm RocketService.polling(...).
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("orderGuid");
        aggPayPollingDTO.setAttachData("attachData");
        aggPayPollingDTO.setRefundNo("refundNo");
        aggPayPollingDTO.setPayGUID("payGuid");
        aggPayPollingDTO.setAppId("appId");
        verify(mockRocketService).polling(aggPayPollingDTO, HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build());
        verify(mockRedisService).putCallBackResp("orderGuid", "payGuid", "paySt");
        verify(mockRedisService).putQrCodeDownloadUrl("orderGuid", "downLoadUrl");

        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("code");
        pollingRespDTO.setMsg("msg");
        pollingRespDTO.setPaySt("paySt");
        pollingRespDTO.setOrderGUID("orderGuid");
        pollingRespDTO.setPayGUID("payGuid");
        pollingRespDTO.setCodeUrl("codeUrl");
        pollingRespDTO.setSignature("signature");
        verify(mockRedisService).putPollingResp("orderGuid", "payGuid", pollingRespDTO);

        // Confirm SaasResultRpcService.handlePayResult(...).
        final AggPayPollingRespDTO pollingRespDTO1 = new AggPayPollingRespDTO();
        pollingRespDTO1.setCode("code");
        pollingRespDTO1.setMsg("msg");
        pollingRespDTO1.setPaySt("paySt");
        pollingRespDTO1.setOrderGUID("orderGuid");
        pollingRespDTO1.setPayGUID("payGuid");
        pollingRespDTO1.setCodeUrl("codeUrl");
        pollingRespDTO1.setSignature("signature");
        verify(mockSaasResultRpcService).handlePayResult(HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build(), pollingRespDTO1);
    }

    @Test
    public void testHandlePollingResult_AggPayRpcServiceDoPollingReturnsError() {
        // Setup
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("orderGuid");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("payGuid");
        pollingJHPayDTO.setAppId("appId");

        final HandlerPayBO handlerPayBO = HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build();

        // Configure AggPayRpcService.doPolling(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = AggPayPollingRespDTO.errorResp("code", "msg");
        final AggPayPollingDTO pollingJHPayDTO1 = new AggPayPollingDTO();
        pollingJHPayDTO1.setOrderGUID("orderGuid");
        pollingJHPayDTO1.setAttachData("attachData");
        pollingJHPayDTO1.setRefundNo("refundNo");
        pollingJHPayDTO1.setPayGUID("payGuid");
        pollingJHPayDTO1.setAppId("appId");
        when(mockAggPayRpcService.doPolling(pollingJHPayDTO1)).thenReturn(aggPayPollingRespDTO);

        when(mockDeveloperConfig.getKey()).thenReturn("result");
        when(mockRedisService.getCallBackResp("orderGuid", "payGuid")).thenReturn(Mono.just("value"));

        // Run the test
        final boolean result = pollingServiceImplUnderTest.handlePollingResult(pollingJHPayDTO, handlerPayBO, false);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm RocketService.polling(...).
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("orderGuid");
        aggPayPollingDTO.setAttachData("attachData");
        aggPayPollingDTO.setRefundNo("refundNo");
        aggPayPollingDTO.setPayGUID("payGuid");
        aggPayPollingDTO.setAppId("appId");
        verify(mockRocketService).polling(aggPayPollingDTO, HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build());
        verify(mockRedisService).putCallBackResp("orderGuid", "payGuid", "paySt");
    }

    @Test
    public void testHandlePollingResult_AggPayRpcServiceDoWeChatPublicAccountPollingReturnsError() {
        // Setup
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("orderGuid");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("payGuid");
        pollingJHPayDTO.setAppId("appId");

        final HandlerPayBO handlerPayBO = HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build();

        // Configure AggPayRpcService.doWeChatPublicAccountPolling(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = AggPayPollingRespDTO.errorResp("code", "msg");
        when(mockAggPayRpcService.doWeChatPublicAccountPolling("orderGuid")).thenReturn(aggPayPollingRespDTO);

        when(mockDeveloperConfig.getKey()).thenReturn("result");
        when(mockRedisService.getCallBackResp("orderGuid", "payGuid")).thenReturn(Mono.just("value"));

        // Run the test
        final boolean result = pollingServiceImplUnderTest.handlePollingResult(pollingJHPayDTO, handlerPayBO, false);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm RocketService.polling(...).
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("orderGuid");
        aggPayPollingDTO.setAttachData("attachData");
        aggPayPollingDTO.setRefundNo("refundNo");
        aggPayPollingDTO.setPayGUID("payGuid");
        aggPayPollingDTO.setAppId("appId");
        verify(mockRocketService).polling(aggPayPollingDTO, HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build());
        verify(mockRedisService).putCallBackResp("orderGuid", "payGuid", "paySt");
    }

    @Test
    public void testHandlePollingResult_RedisServiceGetCallBackRespReturnsNoItem() {
        // Setup
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("orderGuid");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("payGuid");
        pollingJHPayDTO.setAppId("appId");

        final HandlerPayBO handlerPayBO = HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build();

        // Configure AggPayRpcService.doPolling(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setMsg("msg");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("orderGuid");
        aggPayPollingRespDTO.setPayGUID("payGuid");
        aggPayPollingRespDTO.setCodeUrl("codeUrl");
        aggPayPollingRespDTO.setSignature("signature");
        final AggPayPollingDTO pollingJHPayDTO1 = new AggPayPollingDTO();
        pollingJHPayDTO1.setOrderGUID("orderGuid");
        pollingJHPayDTO1.setAttachData("attachData");
        pollingJHPayDTO1.setRefundNo("refundNo");
        pollingJHPayDTO1.setPayGUID("payGuid");
        pollingJHPayDTO1.setAppId("appId");
        when(mockAggPayRpcService.doPolling(pollingJHPayDTO1)).thenReturn(aggPayPollingRespDTO);

        when(mockDeveloperConfig.getKey()).thenReturn("result");
        when(mockRedisService.getCallBackResp("orderGuid", "payGuid")).thenReturn(Mono.empty());

        // Run the test
        final boolean result = pollingServiceImplUnderTest.handlePollingResult(pollingJHPayDTO, handlerPayBO, false);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockRedisService).putCallBackResp("orderGuid", "payGuid", "paySt");
    }

    @Test
    public void testHandlePollingResult_RedisServiceGetCallBackRespReturnsError() {
        // Setup
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("orderGuid");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("payGuid");
        pollingJHPayDTO.setAppId("appId");

        final HandlerPayBO handlerPayBO = HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build();

        // Configure AggPayRpcService.doPolling(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setMsg("msg");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("orderGuid");
        aggPayPollingRespDTO.setPayGUID("payGuid");
        aggPayPollingRespDTO.setCodeUrl("codeUrl");
        aggPayPollingRespDTO.setSignature("signature");
        final AggPayPollingDTO pollingJHPayDTO1 = new AggPayPollingDTO();
        pollingJHPayDTO1.setOrderGUID("orderGuid");
        pollingJHPayDTO1.setAttachData("attachData");
        pollingJHPayDTO1.setRefundNo("refundNo");
        pollingJHPayDTO1.setPayGUID("payGuid");
        pollingJHPayDTO1.setAppId("appId");
        when(mockAggPayRpcService.doPolling(pollingJHPayDTO1)).thenReturn(aggPayPollingRespDTO);

        when(mockDeveloperConfig.getKey()).thenReturn("result");
        when(mockRedisService.getCallBackResp("orderGuid", "payGuid")).thenReturn(Mono.error(new Exception("message")));

        // Run the test
        final boolean result = pollingServiceImplUnderTest.handlePollingResult(pollingJHPayDTO, handlerPayBO, false);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockRedisService).putCallBackResp("orderGuid", "payGuid", "paySt");
    }

    @Test
    public void testHandlePollingResult_RedisServiceGetPollingRespReturnsNoItem() {
        // Setup
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("orderGuid");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("payGuid");
        pollingJHPayDTO.setAppId("appId");

        final HandlerPayBO handlerPayBO = HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build();

        // Configure AggPayRpcService.doPolling(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setMsg("msg");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("orderGuid");
        aggPayPollingRespDTO.setPayGUID("payGuid");
        aggPayPollingRespDTO.setCodeUrl("codeUrl");
        aggPayPollingRespDTO.setSignature("signature");
        final AggPayPollingDTO pollingJHPayDTO1 = new AggPayPollingDTO();
        pollingJHPayDTO1.setOrderGUID("orderGuid");
        pollingJHPayDTO1.setAttachData("attachData");
        pollingJHPayDTO1.setRefundNo("refundNo");
        pollingJHPayDTO1.setPayGUID("payGuid");
        pollingJHPayDTO1.setAppId("appId");
        when(mockAggPayRpcService.doPolling(pollingJHPayDTO1)).thenReturn(aggPayPollingRespDTO);

        when(mockDeveloperConfig.getKey()).thenReturn("result");
        when(mockRedisService.getCallBackResp("orderGuid", "payGuid")).thenReturn(Mono.just("value"));
        when(mockRedisService.getQrCodeDownloadUrl("orderGuid")).thenReturn("result");
        when(mockFileUploadRpcService.upload(any(FileDto.class))).thenReturn("downLoadUrl");

        // Configure RedisService.getPollingResp(...).
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setOrderGuid("orderGuid");
        saasPollingDTO.setPayGuid("payGuid");
        when(mockRedisService.getPollingResp(saasPollingDTO)).thenReturn(Mono.empty());

        // Run the test
        final boolean result = pollingServiceImplUnderTest.handlePollingResult(pollingJHPayDTO, handlerPayBO, false);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockRedisService).putCallBackResp("orderGuid", "payGuid", "paySt");
        verify(mockRedisService).putQrCodeDownloadUrl("orderGuid", "downLoadUrl");

        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("code");
        pollingRespDTO.setMsg("msg");
        pollingRespDTO.setPaySt("paySt");
        pollingRespDTO.setOrderGUID("orderGuid");
        pollingRespDTO.setPayGUID("payGuid");
        pollingRespDTO.setCodeUrl("codeUrl");
        pollingRespDTO.setSignature("signature");
        verify(mockRedisService).putPollingResp("orderGuid", "payGuid", pollingRespDTO);

        // Confirm RocketService.polling(...).
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("orderGuid");
        aggPayPollingDTO.setAttachData("attachData");
        aggPayPollingDTO.setRefundNo("refundNo");
        aggPayPollingDTO.setPayGUID("payGuid");
        aggPayPollingDTO.setAppId("appId");
        verify(mockRocketService).polling(aggPayPollingDTO, HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build());

        // Confirm SaasResultRpcService.handlePayResult(...).
        final AggPayPollingRespDTO pollingRespDTO1 = new AggPayPollingRespDTO();
        pollingRespDTO1.setCode("code");
        pollingRespDTO1.setMsg("msg");
        pollingRespDTO1.setPaySt("paySt");
        pollingRespDTO1.setOrderGUID("orderGuid");
        pollingRespDTO1.setPayGUID("payGuid");
        pollingRespDTO1.setCodeUrl("codeUrl");
        pollingRespDTO1.setSignature("signature");
        verify(mockSaasResultRpcService).handlePayResult(HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build(), pollingRespDTO1);
    }

    @Test
    public void testHandlePollingResult_RedisServiceGetPollingRespReturnsError() {
        // Setup
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("orderGuid");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("payGuid");
        pollingJHPayDTO.setAppId("appId");

        final HandlerPayBO handlerPayBO = HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build();

        // Configure AggPayRpcService.doPolling(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setMsg("msg");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setOrderGUID("orderGuid");
        aggPayPollingRespDTO.setPayGUID("payGuid");
        aggPayPollingRespDTO.setCodeUrl("codeUrl");
        aggPayPollingRespDTO.setSignature("signature");
        final AggPayPollingDTO pollingJHPayDTO1 = new AggPayPollingDTO();
        pollingJHPayDTO1.setOrderGUID("orderGuid");
        pollingJHPayDTO1.setAttachData("attachData");
        pollingJHPayDTO1.setRefundNo("refundNo");
        pollingJHPayDTO1.setPayGUID("payGuid");
        pollingJHPayDTO1.setAppId("appId");
        when(mockAggPayRpcService.doPolling(pollingJHPayDTO1)).thenReturn(aggPayPollingRespDTO);

        when(mockDeveloperConfig.getKey()).thenReturn("result");
        when(mockRedisService.getCallBackResp("orderGuid", "payGuid")).thenReturn(Mono.just("value"));
        when(mockRedisService.getQrCodeDownloadUrl("orderGuid")).thenReturn("result");
        when(mockFileUploadRpcService.upload(any(FileDto.class))).thenReturn("downLoadUrl");

        // Configure RedisService.getPollingResp(...).
        final Mono<AggPayPollingRespDTO> aggPayPollingRespDTOMono = Mono.error(new Exception("message"));
        final SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setOrderGuid("orderGuid");
        saasPollingDTO.setPayGuid("payGuid");
        when(mockRedisService.getPollingResp(saasPollingDTO)).thenReturn(aggPayPollingRespDTOMono);

        // Run the test
        final boolean result = pollingServiceImplUnderTest.handlePollingResult(pollingJHPayDTO, handlerPayBO, false);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockRedisService).putCallBackResp("orderGuid", "payGuid", "paySt");
        verify(mockRedisService).putQrCodeDownloadUrl("orderGuid", "downLoadUrl");

        // Confirm RedisService.putPollingResp(...).
        final AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode("code");
        pollingRespDTO.setMsg("msg");
        pollingRespDTO.setPaySt("paySt");
        pollingRespDTO.setOrderGUID("orderGuid");
        pollingRespDTO.setPayGUID("payGuid");
        pollingRespDTO.setCodeUrl("codeUrl");
        pollingRespDTO.setSignature("signature");
        verify(mockRedisService).putPollingResp("orderGuid", "payGuid", pollingRespDTO);

        // Confirm RocketService.polling(...).
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("orderGuid");
        aggPayPollingDTO.setAttachData("attachData");
        aggPayPollingDTO.setRefundNo("refundNo");
        aggPayPollingDTO.setPayGUID("payGuid");
        aggPayPollingDTO.setAppId("appId");
        verify(mockRocketService).polling(aggPayPollingDTO, HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build());

        // Confirm SaasResultRpcService.handlePayResult(...).
        final AggPayPollingRespDTO pollingRespDTO1 = new AggPayPollingRespDTO();
        pollingRespDTO1.setCode("code");
        pollingRespDTO1.setMsg("msg");
        pollingRespDTO1.setPaySt("paySt");
        pollingRespDTO1.setOrderGUID("orderGuid");
        pollingRespDTO1.setPayGUID("payGuid");
        pollingRespDTO1.setCodeUrl("codeUrl");
        pollingRespDTO1.setSignature("signature");
        verify(mockSaasResultRpcService).handlePayResult(HandlerPayBO.builder()
                .paymentInfoDTO(new PaymentInfoDTO())
                .handlerType(0)
                .payPowerId("payPowerId")
                .build(), pollingRespDTO1);
    }
}
