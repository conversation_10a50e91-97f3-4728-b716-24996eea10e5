package com.holderzone.holder.saas.store.report.service.impl;

import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.report.mapper.ReserveMapper;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.query.ReserveReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.ReserveRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ReserveServiceImplTest {

    @Mock
    private ReserveMapper mockReserveMapper;
    @Mock
    private OssClient mockOssClient;

    private ReserveServiceImpl reserveServiceImplUnderTest;

    @Before
    public void setUp() {
        reserveServiceImplUnderTest = new ReserveServiceImpl(mockReserveMapper, mockOssClient);
    }

    @Test
    public void testPage() {
        // Setup
        final ReserveReportQueryVO query = new ReserveReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setReserveStates(Arrays.asList(0));
        query.setIsDelay(false);

        // Configure ReserveMapper.count(...).
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockReserveMapper.count(query1)).thenReturn(0L);

        // Configure ReserveMapper.pageInfo(...).
        final ReserveRespDTO reserveRespDTO = new ReserveRespDTO();
        reserveRespDTO.setDeviceType(0);
        reserveRespDTO.setNumber(0);
        reserveRespDTO.setState("state");
        reserveRespDTO.setStateName("stateName");
        reserveRespDTO.setName("name");
        reserveRespDTO.setTables("");
        reserveRespDTO.setAreaName("");
        reserveRespDTO.setPaymentType(0);
        reserveRespDTO.setPaymentTypeName("paymentTypeName");
        reserveRespDTO.setReserveAmount(new BigDecimal("0.00"));
        reserveRespDTO.setGender((byte) 0b0);
        reserveRespDTO.setRemark("remark");
        reserveRespDTO.setReserveStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reserveRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reserveRespDTO.setIsDelay(false);
        final List<ReserveRespDTO> reserveRespDTOS = Arrays.asList(reserveRespDTO);
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockReserveMapper.pageInfo(query2)).thenReturn(reserveRespDTOS);

        // Run the test
        final Page<ReserveRespDTO> result = reserveServiceImplUnderTest.page(query);

        // Verify the results
    }

    @Test
    public void testPage_ReserveMapperPageInfoReturnsNoItems() {
        // Setup
        final ReserveReportQueryVO query = new ReserveReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setReserveStates(Arrays.asList(0));
        query.setIsDelay(false);

        // Configure ReserveMapper.count(...).
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockReserveMapper.count(query1)).thenReturn(0L);

        // Configure ReserveMapper.pageInfo(...).
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockReserveMapper.pageInfo(query2)).thenReturn(Collections.emptyList());

        // Run the test
        final Page<ReserveRespDTO> result = reserveServiceImplUnderTest.page(query);

        // Verify the results
    }

    @Test
    public void testExport() {
        // Setup
        final ReserveReportQueryVO query = new ReserveReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setReserveStates(Arrays.asList(0));
        query.setIsDelay(false);

        // Configure ReserveMapper.count(...).
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockReserveMapper.count(query1)).thenReturn(0L);

        // Configure ReserveMapper.list(...).
        final ReserveRespDTO reserveRespDTO = new ReserveRespDTO();
        reserveRespDTO.setDeviceType(0);
        reserveRespDTO.setNumber(0);
        reserveRespDTO.setState("state");
        reserveRespDTO.setStateName("stateName");
        reserveRespDTO.setName("name");
        reserveRespDTO.setTables("");
        reserveRespDTO.setAreaName("");
        reserveRespDTO.setPaymentType(0);
        reserveRespDTO.setPaymentTypeName("paymentTypeName");
        reserveRespDTO.setReserveAmount(new BigDecimal("0.00"));
        reserveRespDTO.setGender((byte) 0b0);
        reserveRespDTO.setRemark("remark");
        reserveRespDTO.setReserveStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reserveRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        reserveRespDTO.setIsDelay(false);
        final List<ReserveRespDTO> reserveRespDTOS = Arrays.asList(reserveRespDTO);
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockReserveMapper.list(query2)).thenReturn(reserveRespDTOS);

        // Run the test
        final String result = reserveServiceImplUnderTest.export(query);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testExport_ReserveMapperListReturnsNoItems() {
        // Setup
        final ReserveReportQueryVO query = new ReserveReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setReserveStates(Arrays.asList(0));
        query.setIsDelay(false);

        // Configure ReserveMapper.count(...).
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockReserveMapper.count(query1)).thenReturn(0L);

        // Configure ReserveMapper.list(...).
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockReserveMapper.list(query2)).thenReturn(Collections.emptyList());

        // Run the test
        final String result = reserveServiceImplUnderTest.export(query);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }
}
