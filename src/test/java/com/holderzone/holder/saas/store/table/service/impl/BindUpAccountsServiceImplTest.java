package com.holderzone.holder.saas.store.table.service.impl;

import com.holderzone.holder.saas.store.table.client.BindupAccountsClientService;
import com.holderzone.holder.saas.store.table.client.OrganizationClientService;
import com.holderzone.saas.store.dto.organization.BusinessDateReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.store.store.BindupAccountsDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BindUpAccountsServiceImplTest {

    @Mock
    private OrganizationClientService mockOrganizationClientService;
    @Mock
    private BindupAccountsClientService mockBindupAccountsClientService;

    @InjectMocks
    private BindUpAccountsServiceImpl bindUpAccountsServiceImplUnderTest;

    @Test
    public void testCurrentTimeDay() {
        // Setup
        // Configure OrganizationClientService.queryBusinessDay(...).
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");
        when(mockOrganizationClientService.queryBusinessDay(businessDateReqDTO)).thenReturn(LocalDate.of(2020, 1, 1));

        // Run the test
        final LocalDate result = bindUpAccountsServiceImplUnderTest.currentTimeDay("storeGuid",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
        assertThat(result).isEqualTo(LocalDate.of(2020, 1, 1));
    }

    @Test
    public void testSendMqForCanOpenTable() {
        // Setup
        // Run the test
        bindUpAccountsServiceImplUnderTest.sendMqForCanOpenTable("storeGuid");

        // Verify the results
        verify(mockBindupAccountsClientService).sendMqForCanOpenTable("storeGuid");
    }

    @Test
    public void testCheckBindUpAccountStatus() {
        // Setup
        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("60106a5c-ed71-491b-8995-71b54717e659");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setIsBuAccounts(0);
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure BindupAccountsClientService.queryBindUpAccountsLast(...).
        final BindupAccountsDTO bindupAccountsDTO = new BindupAccountsDTO();
        bindupAccountsDTO.setId(0L);
        bindupAccountsDTO.setBindupAccounts(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        bindupAccountsDTO.setStoreGuid("storeGuid");
        bindupAccountsDTO.setUserGuid("userGuid");
        bindupAccountsDTO.setUserName("userName");
        when(mockBindupAccountsClientService.queryBindUpAccountsLast("storeGuid")).thenReturn(bindupAccountsDTO);

        // Configure OrganizationClientService.queryBusinessDay(...).
        final BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        businessDateReqDTO.setQueryDateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        businessDateReqDTO.setStoreGuidList(Arrays.asList("value"));
        businessDateReqDTO.setStoreGuid("storeGuid");
        when(mockOrganizationClientService.queryBusinessDay(businessDateReqDTO)).thenReturn(LocalDate.of(2020, 1, 1));

        // Run the test
        final boolean result = bindUpAccountsServiceImplUnderTest.checkBindUpAccountStatus("storeGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testCheckBindUpAccountStatus_BindupAccountsClientServiceReturnsNull() {
        // Setup
        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("60106a5c-ed71-491b-8995-71b54717e659");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setIsBuAccounts(0);
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        when(mockBindupAccountsClientService.queryBindUpAccountsLast("storeGuid")).thenReturn(null);

        // Run the test
        final boolean result = bindUpAccountsServiceImplUnderTest.checkBindUpAccountStatus("storeGuid");

        // Verify the results
        assertThat(result).isFalse();
    }
}
