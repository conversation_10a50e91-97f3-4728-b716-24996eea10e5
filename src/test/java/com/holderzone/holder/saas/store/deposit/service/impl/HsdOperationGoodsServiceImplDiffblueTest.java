package com.holderzone.holder.saas.store.deposit.service.impl;

import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.store.deposit.entity.bo.OperationGoodsDO;
import com.holderzone.holder.saas.store.deposit.mapper.HsdOperationGoodsMapper;
import com.holderzone.holder.saas.store.deposit.mapstruct.OperationMapstruct;
import com.holderzone.saas.store.dto.deposit.resp.GoodsSimpleRespDTO;

import java.util.ArrayList;
import java.util.List;

import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@ContextConfiguration(classes = {HsdOperationGoodsServiceImpl.class})
@RunWith(SpringJUnit4ClassRunner.class)
public class HsdOperationGoodsServiceImplDiffblueTest {
    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @MockBean
    private HsdOperationGoodsMapper hsdOperationGoodsMapper;

    @Autowired
    private HsdOperationGoodsServiceImpl hsdOperationGoodsServiceImpl;

    @MockBean
    private OperationMapstruct operationMapstruct;

    /**
     * Method under test:
     * {@link HsdOperationGoodsServiceImpl#queryGoodsOfOperation(String, String)}
     */
    @Test
    public void testQueryGoodsOfOperation() {
        when(hsdOperationGoodsMapper.selectList(Mockito.<Wrapper<OperationGoodsDO>>any())).thenReturn(new ArrayList<>());
        ArrayList<GoodsSimpleRespDTO> goodsSimpleRespDTOList = new ArrayList<>();
        when(operationMapstruct.fromGoodsOfOperation(Mockito.<List<OperationGoodsDO>>any()))
                .thenReturn(goodsSimpleRespDTOList);
        List<GoodsSimpleRespDTO> actualQueryGoodsOfOperationResult = hsdOperationGoodsServiceImpl
                .queryGoodsOfOperation("1234", "1234");
        verify(hsdOperationGoodsMapper).selectList(Mockito.<Wrapper<OperationGoodsDO>>any());
        verify(operationMapstruct).fromGoodsOfOperation(Mockito.<List<OperationGoodsDO>>any());
        assertTrue(actualQueryGoodsOfOperationResult.isEmpty());
        assertSame(goodsSimpleRespDTOList, actualQueryGoodsOfOperationResult);
    }

    /**
     * Method under test:
     * {@link HsdOperationGoodsServiceImpl#queryGoodsOfOperation(String, String)}
     */
    @Test
    public void testQueryGoodsOfOperation2() {
        when(hsdOperationGoodsMapper.selectList(Mockito.<Wrapper<OperationGoodsDO>>any())).thenReturn(new ArrayList<>());
        when(operationMapstruct.fromGoodsOfOperation(Mockito.<List<OperationGoodsDO>>any()))
                .thenThrow(new BusinessException("An error occurred"));
        thrown.expect(BusinessException.class);
        hsdOperationGoodsServiceImpl.queryGoodsOfOperation("1234", "1234");
        verify(hsdOperationGoodsMapper).selectList(Mockito.<Wrapper<OperationGoodsDO>>any());
        verify(operationMapstruct).fromGoodsOfOperation(Mockito.<List<OperationGoodsDO>>any());
    }

    /**
     * Method under test:
     * {@link HsdOperationGoodsServiceImpl#queryGoodsOfOperation(String, String)}
     */
    @Test
    public void testQueryGoodsOfOperation3() {
        thrown.expect(BusinessException.class);
        hsdOperationGoodsServiceImpl.queryGoodsOfOperation("", "1234");
    }

    /**
     * Method under test:
     * {@link HsdOperationGoodsServiceImpl#queryGoodsOfOperation(String, String)}
     */
    @Test
    public void testQueryGoodsOfOperation4() {
        thrown.expect(BusinessException.class);
        hsdOperationGoodsServiceImpl.queryGoodsOfOperation("1234", "");
    }
}
