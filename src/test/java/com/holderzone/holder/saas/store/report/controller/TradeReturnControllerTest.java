package com.holderzone.holder.saas.store.report.controller;

import com.holderzone.holder.saas.store.report.service.TradeReturnService;
import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.base.Pager;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.ReturnDetailItemDTO;
import com.holderzone.saas.store.dto.report.resp.ReturnItemDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashMap;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(TradeReturnController.class)
public class TradeReturnControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private TradeReturnService mockTradeReturnService;

    @Test
    public void testList() throws Exception {
        // Setup
        // Configure TradeReturnService.list(...).
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        returnItemDTO.setBrandName("brandName");
        returnItemDTO.setBrandGuid("brandGuid");
        returnItemDTO.setStoreName("storeName");
        returnItemDTO.setStoreGuid("storeGuid");
        returnItemDTO.setGoodsName("goodsName");
        final Pager pager = new Pager();
        pager.setPageNo(0);
        pager.setPageSize(0);
        pager.setTotalCount(0);
        pager.setTotalPages(0);
        final Message<ReturnItemDTO> returnItemDTOMessage = new Message<>(Arrays.asList(returnItemDTO), new HashMap<>(),
                pager);
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeReturnService.list(query)).thenReturn(returnItemDTOMessage);

        // Run the test and verify the results
        mockMvc.perform(post("/trade/return/list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testList_TradeReturnServiceReturnsNoItem() throws Exception {
        // Setup
        // Configure TradeReturnService.list(...).
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeReturnService.list(query)).thenReturn(Message.getInstance());

        // Run the test and verify the results
        mockMvc.perform(post("/trade/return/list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("", true));
    }

    @Test
    public void testListDetail() throws Exception {
        // Setup
        // Configure TradeReturnService.listDetail(...).
        final ReturnDetailItemDTO returnDetailItemDTO = new ReturnDetailItemDTO();
        returnDetailItemDTO.setStoreName("storeName");
        returnDetailItemDTO.setOrderNo("orderNo");
        returnDetailItemDTO.setCateringType("cateringType");
        returnDetailItemDTO.setSkuGuid("skuGuid");
        returnDetailItemDTO.setGoodsName("goodsName");
        final Pager pager = new Pager();
        pager.setPageNo(0);
        pager.setPageSize(0);
        pager.setTotalCount(0);
        pager.setTotalPages(0);
        final Message<ReturnDetailItemDTO> returnDetailItemDTOMessage = new Message<>(
                Arrays.asList(returnDetailItemDTO), new HashMap<>(), pager);
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeReturnService.listDetail(query)).thenReturn(returnDetailItemDTOMessage);

        // Run the test and verify the results
        mockMvc.perform(post("/trade/return/list_detail")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListDetail_TradeReturnServiceReturnsNoItem() throws Exception {
        // Setup
        // Configure TradeReturnService.listDetail(...).
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeReturnService.listDetail(query)).thenReturn(Message.getInstance());

        // Run the test and verify the results
        mockMvc.perform(post("/trade/return/list_detail")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("", true));
    }

    @Test
    public void testDetailExport() throws Exception {
        // Setup
        // Configure TradeReturnService.exportReturnDetail(...).
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeReturnService.exportReturnDetail(query)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/trade/return/detail_export")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
