package com.holderzone.holder.saas.store.report.service.rpc.organization;

import com.holderzone.framework.exception.unchecked.BusinessException;

import java.io.IOException;
import java.util.ArrayList;

import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;

public class OrganizationServiceDiffblueTest {
    @Rule
    public ExpectedException thrown = ExpectedException.none();

    /**
     * Method under test:
     * {@link OrganizationService.ServiceFallBack#create(Throwable)}
     */
    @Test
    public void testServiceFallBackCreate() {
        //   Diffblue Cover was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R002 Missing observers.
        //   Diffblue Cover was unable to create an assertion.
        //   Add getters for the following fields or make them package-private:
        //     327321305.arg$1

        OrganizationService.ServiceFallBack serviceFallBack = new OrganizationService.ServiceFallBack();
        OrganizationService actualCreateResult = serviceFallBack.create(new Throwable());
        thrown.expect(BusinessException.class);
        actualCreateResult.queryStoreAndBrandByIdList(new ArrayList<>());
    }

    /**
     * Method under test:
     * {@link OrganizationService.ServiceFallBack#create(Throwable)}
     */
    @Test
    public void testServiceFallBackCreate2() {
        //   Diffblue Cover was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R002 Missing observers.
        //   Diffblue Cover was unable to create an assertion.
        //   Add getters for the following fields or make them package-private:
        //     327321305.arg$1

        OrganizationService.ServiceFallBack serviceFallBack = new OrganizationService.ServiceFallBack();
        OrganizationService actualCreateResult = serviceFallBack.create(new IOException("服务间调用{}熔断，入参{}，异常{}"));
        thrown.expect(BusinessException.class);
        actualCreateResult.queryStoreAndBrandByIdList(new ArrayList<>());
    }

    /**
     * Method under test:
     * {@link OrganizationService.ServiceFallBack#create(Throwable)}
     */
    @Test
    public void testServiceFallBackCreate3() {
        //   Diffblue Cover was unable to write a Spring test,
        //   so wrote a non-Spring test instead.
        //   Reason: R002 Missing observers.
        //   Diffblue Cover was unable to create an assertion.
        //   Add getters for the following fields or make them package-private:
        //     327321305.arg$1

        OrganizationService.ServiceFallBack serviceFallBack = new OrganizationService.ServiceFallBack();
        OrganizationService actualCreateResult = serviceFallBack.create(new Throwable());
        ArrayList<String> storeGuidList = new ArrayList<>();
        storeGuidList.add("服务间调用{}熔断，入参{}，异常{}");
        thrown.expect(BusinessException.class);
        actualCreateResult.queryStoreAndBrandByIdList(storeGuidList);
    }
}
