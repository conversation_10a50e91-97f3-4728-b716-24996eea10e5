package com.holderzone.holder.saas.store.table.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.store.table.domain.TableBasicDO;
import com.holderzone.holder.saas.store.table.domain.TableOrderDO;
import com.holderzone.holder.saas.store.table.mapper.TableBasicMapper;
import com.holderzone.holder.saas.store.table.service.RedisService;
import com.holderzone.holder.saas.store.table.service.TableOrderService;
import com.holderzone.holder.saas.store.table.service.TableTagRelationService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.store.table.StoreAndTableDTO;
import com.holderzone.saas.store.dto.table.*;
import com.holderzone.saas.store.enums.table.BatchTableEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TableBasicServiceImplTest {

    @Mock
    private TableBasicMapper mockTableBasicMapper;
    @Mock
    private TableOrderService mockTableOrderService;
    @Mock
    private RedisService mockRedisService;
    @Mock
    private TableTagRelationService mockTableTagRelationService;

    private TableBasicServiceImpl tableBasicServiceImplUnderTest;

    @Before
    public void setUp() {
        tableBasicServiceImplUnderTest = new TableBasicServiceImpl(mockTableBasicMapper, mockTableOrderService,
                mockRedisService, mockTableTagRelationService);
    }

    @Test
    public void testCreate_ThrowsBusinessException() {
        // Setup
        final TableBasicDTO tableBasicDTO = new TableBasicDTO();
        tableBasicDTO.setGuid("guid");
        tableBasicDTO.setStoreGuid("storeGuid");
        tableBasicDTO.setAreaGuid("areaGuid");
        tableBasicDTO.setTableCode("tableCode");
        tableBasicDTO.setSort(0);
        tableBasicDTO.setTagList(Arrays.asList("value"));
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableBasicDTO.setTableTagDTOS(Arrays.asList(tableTagDTO));

        when(mockTableBasicMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        assertThatThrownBy(() -> tableBasicServiceImplUnderTest.create(tableBasicDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testCreate_TableBasicMapperSelectCountReturnsNull() {
        // Setup
        final TableBasicDTO tableBasicDTO = new TableBasicDTO();
        tableBasicDTO.setGuid("guid");
        tableBasicDTO.setStoreGuid("storeGuid");
        tableBasicDTO.setAreaGuid("areaGuid");
        tableBasicDTO.setTableCode("tableCode");
        tableBasicDTO.setSort(0);
        tableBasicDTO.setTagList(Arrays.asList("value"));
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableBasicDTO.setTableTagDTOS(Arrays.asList(tableTagDTO));

        when(mockTableBasicMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(mockRedisService.singleGuid("hst_table_basic")).thenReturn("guid");
        when(mockTableBasicMapper.maxSort("storeGuid", "areaGuid")).thenReturn(0);

        // Run the test
        final String result = tableBasicServiceImplUnderTest.create(tableBasicDTO);

        // Verify the results
        assertThat(result).isEqualTo("SUCCESS");

        // Confirm TableBasicMapper.insert(...).
        final TableBasicDO entity = new TableBasicDO();
        entity.setGuid("guid");
        entity.setStoreGuid("storeGuid");
        entity.setAreaGuid("areaGuid");
        entity.setAreaName("areaName");
        entity.setTableCode("tableCode");
        entity.setSeats(0);
        entity.setSort(0);
        entity.setEnable(0);
        entity.setDeleted(0);
        verify(mockTableBasicMapper).insert(entity);

        // Confirm TableOrderService.save(...).
        final TableOrderDO entity1 = new TableOrderDO();
        entity1.setId(0L);
        entity1.setGuid("c67e6dfb-0a81-4deb-a3b3-e2a83ad3bbc0");
        entity1.setTableGuid("tableGuid");
        entity1.setMainOrderGuid("mainOrderGuid");
        entity1.setOrderGuid("orderGuid");
        verify(mockTableOrderService).save(entity1);
        verify(mockTableTagRelationService).createTags(Arrays.asList("value"), "guid");
    }

    @Test
    public void testUpdate_ThrowsBusinessException() {
        // Setup
        final TableBasicDTO tableBasicDTO = new TableBasicDTO();
        tableBasicDTO.setGuid("guid");
        tableBasicDTO.setStoreGuid("storeGuid");
        tableBasicDTO.setAreaGuid("areaGuid");
        tableBasicDTO.setTableCode("tableCode");
        tableBasicDTO.setSort(0);
        tableBasicDTO.setTagList(Arrays.asList("value"));
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableBasicDTO.setTableTagDTOS(Arrays.asList(tableTagDTO));

        // Configure TableBasicMapper.selectOne(...).
        final TableBasicDO tableBasicDO = new TableBasicDO();
        tableBasicDO.setGuid("guid");
        tableBasicDO.setStoreGuid("storeGuid");
        tableBasicDO.setAreaGuid("areaGuid");
        tableBasicDO.setAreaName("areaName");
        tableBasicDO.setTableCode("tableCode");
        tableBasicDO.setSeats(0);
        tableBasicDO.setSort(0);
        tableBasicDO.setEnable(0);
        tableBasicDO.setDeleted(0);
        when(mockTableBasicMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableBasicDO);

        when(mockTableBasicMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        assertThatThrownBy(() -> tableBasicServiceImplUnderTest.update(tableBasicDTO))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testUpdate_TableBasicMapperSelectCountReturnsNull() {
        // Setup
        final TableBasicDTO tableBasicDTO = new TableBasicDTO();
        tableBasicDTO.setGuid("guid");
        tableBasicDTO.setStoreGuid("storeGuid");
        tableBasicDTO.setAreaGuid("areaGuid");
        tableBasicDTO.setTableCode("tableCode");
        tableBasicDTO.setSort(0);
        tableBasicDTO.setTagList(Arrays.asList("value"));
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableBasicDTO.setTableTagDTOS(Arrays.asList(tableTagDTO));

        // Configure TableBasicMapper.selectOne(...).
        final TableBasicDO tableBasicDO = new TableBasicDO();
        tableBasicDO.setGuid("guid");
        tableBasicDO.setStoreGuid("storeGuid");
        tableBasicDO.setAreaGuid("areaGuid");
        tableBasicDO.setAreaName("areaName");
        tableBasicDO.setTableCode("tableCode");
        tableBasicDO.setSeats(0);
        tableBasicDO.setSort(0);
        tableBasicDO.setEnable(0);
        tableBasicDO.setDeleted(0);
        when(mockTableBasicMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableBasicDO);

        when(mockTableBasicMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        final String result = tableBasicServiceImplUnderTest.update(tableBasicDTO);

        // Verify the results
        assertThat(result).isEqualTo("SUCCESS");

        // Confirm TableBasicMapper.update(...).
        final TableBasicDO entity = new TableBasicDO();
        entity.setGuid("guid");
        entity.setStoreGuid("storeGuid");
        entity.setAreaGuid("areaGuid");
        entity.setAreaName("areaName");
        entity.setTableCode("tableCode");
        entity.setSeats(0);
        entity.setSort(0);
        entity.setEnable(0);
        entity.setDeleted(0);
        verify(mockTableBasicMapper).update(eq(entity), any(LambdaQueryWrapper.class));
        verify(mockTableTagRelationService).removeTagByTableIds("guid");
        verify(mockTableTagRelationService).createTags(Arrays.asList("value"), "guid");
    }

    @Test
    public void testBatchDelete() {
        // Setup
        when(mockTableOrderService.listTableOccupied(Arrays.asList("value"))).thenReturn(Arrays.asList("value"));

        // Configure TableBasicMapper.selectList(...).
        final TableBasicDO tableBasicDO = new TableBasicDO();
        tableBasicDO.setGuid("guid");
        tableBasicDO.setStoreGuid("storeGuid");
        tableBasicDO.setAreaGuid("areaGuid");
        tableBasicDO.setAreaName("areaName");
        tableBasicDO.setTableCode("tableCode");
        tableBasicDO.setSeats(0);
        tableBasicDO.setSort(0);
        tableBasicDO.setEnable(0);
        tableBasicDO.setDeleted(0);
        final List<TableBasicDO> tableBasicDOS = Arrays.asList(tableBasicDO);
        when(mockTableBasicMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableBasicDOS);

        // Run the test
        final List<String> result = tableBasicServiceImplUnderTest.batchDelete(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
        verify(mockTableBasicMapper).deleteAll(Arrays.asList("value"));
        verify(mockTableTagRelationService).removeTagByTableIds(Arrays.asList("value"));
    }

    @Test
    public void testBatchDelete_TableOrderServiceReturnsNoItems() {
        // Setup
        when(mockTableOrderService.listTableOccupied(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = tableBasicServiceImplUnderTest.batchDelete(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
        verify(mockTableBasicMapper).deleteAll(Arrays.asList("value"));
        verify(mockTableTagRelationService).removeTagByTableIds(Arrays.asList("value"));
    }

    @Test
    public void testBatchDelete_TableBasicMapperSelectListReturnsNoItems() {
        // Setup
        when(mockTableOrderService.listTableOccupied(Arrays.asList("value"))).thenReturn(Arrays.asList("value"));
        when(mockTableBasicMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = tableBasicServiceImplUnderTest.batchDelete(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
        verify(mockTableBasicMapper).deleteAll(Arrays.asList("value"));
        verify(mockTableTagRelationService).removeTagByTableIds(Arrays.asList("value"));
    }

    @Test
    public void testListTable() {
        // Setup
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));

        final TableBasicDTO tableBasicDTO = new TableBasicDTO();
        tableBasicDTO.setGuid("guid");
        tableBasicDTO.setStoreGuid("storeGuid");
        tableBasicDTO.setAreaGuid("areaGuid");
        tableBasicDTO.setTableCode("tableCode");
        tableBasicDTO.setSort(0);
        tableBasicDTO.setTagList(Arrays.asList("value"));
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableBasicDTO.setTableTagDTOS(Arrays.asList(tableTagDTO));
        final List<TableBasicDTO> expectedResult = Arrays.asList(tableBasicDTO);

        // Configure TableBasicMapper.selectList(...).
        final TableBasicDO tableBasicDO = new TableBasicDO();
        tableBasicDO.setGuid("guid");
        tableBasicDO.setStoreGuid("storeGuid");
        tableBasicDO.setAreaGuid("areaGuid");
        tableBasicDO.setAreaName("areaName");
        tableBasicDO.setTableCode("tableCode");
        tableBasicDO.setSeats(0);
        tableBasicDO.setSort(0);
        tableBasicDO.setEnable(0);
        tableBasicDO.setDeleted(0);
        final List<TableBasicDO> tableBasicDOS = Arrays.asList(tableBasicDO);
        when(mockTableBasicMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableBasicDOS);

        when(mockTableTagRelationService.getTagInfoByTableInfos(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Run the test
        final List<TableBasicDTO> result = tableBasicServiceImplUnderTest.listTable(tableBasicQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListTable_TableBasicMapperReturnsNoItems() {
        // Setup
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));

        when(mockTableBasicMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockTableTagRelationService.getTagInfoByTableInfos(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Run the test
        final List<TableBasicDTO> result = tableBasicServiceImplUnderTest.listTable(tableBasicQueryDTO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testBatchCreate() {
        // Setup
        final TableBatchCreateDTO tableBatchCreateDTO = new TableBatchCreateDTO();
        tableBatchCreateDTO.setStoreGuid("storeGuid");
        tableBatchCreateDTO.setAreaGuid("areaGuid");
        tableBatchCreateDTO.setFixedFirstWord("fixedFirstWord");
        tableBatchCreateDTO.setBatchTableEnum(BatchTableEnum.FIXED_2);
        tableBatchCreateDTO.setStartNum(0);
        tableBatchCreateDTO.setTotal(0);
        tableBatchCreateDTO.setSeatsPerTable(0);
        tableBatchCreateDTO.setTagList(Arrays.asList("value"));

        // Configure TableBasicMapper.selectList(...).
        final TableBasicDO tableBasicDO = new TableBasicDO();
        tableBasicDO.setGuid("guid");
        tableBasicDO.setStoreGuid("storeGuid");
        tableBasicDO.setAreaGuid("areaGuid");
        tableBasicDO.setAreaName("areaName");
        tableBasicDO.setTableCode("tableCode");
        tableBasicDO.setSeats(0);
        tableBasicDO.setSort(0);
        tableBasicDO.setEnable(0);
        tableBasicDO.setDeleted(0);
        final List<TableBasicDO> tableBasicDOS = Arrays.asList(tableBasicDO);
        when(mockTableBasicMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableBasicDOS);

        when(mockRedisService.batchGuid(0, "hst_table_basic")).thenReturn(Arrays.asList("value"));
        when(mockTableBasicMapper.maxSort("storeGuid", "areaGuid")).thenReturn(0);

        // Run the test
        final String result = tableBasicServiceImplUnderTest.batchCreate(tableBatchCreateDTO);

        // Verify the results
        assertThat(result).isEqualTo("SUCCESS");

        // Confirm TableOrderService.saveBatch(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setGuid("c67e6dfb-0a81-4deb-a3b3-e2a83ad3bbc0");
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("orderGuid");
        final List<TableOrderDO> entityList = Arrays.asList(tableOrderDO);
        verify(mockTableOrderService).saveBatch(entityList, 0);
        verify(mockTableTagRelationService).createTags(Arrays.asList("value"), Arrays.asList("value"));
    }

    @Test
    public void testBatchCreate_TableBasicMapperSelectListReturnsNoItems() {
        // Setup
        final TableBatchCreateDTO tableBatchCreateDTO = new TableBatchCreateDTO();
        tableBatchCreateDTO.setStoreGuid("storeGuid");
        tableBatchCreateDTO.setAreaGuid("areaGuid");
        tableBatchCreateDTO.setFixedFirstWord("fixedFirstWord");
        tableBatchCreateDTO.setBatchTableEnum(BatchTableEnum.FIXED_2);
        tableBatchCreateDTO.setStartNum(0);
        tableBatchCreateDTO.setTotal(0);
        tableBatchCreateDTO.setSeatsPerTable(0);
        tableBatchCreateDTO.setTagList(Arrays.asList("value"));

        when(mockTableBasicMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockRedisService.batchGuid(0, "hst_table_basic")).thenReturn(Arrays.asList("value"));
        when(mockTableBasicMapper.maxSort("storeGuid", "areaGuid")).thenReturn(0);

        // Run the test
        final String result = tableBasicServiceImplUnderTest.batchCreate(tableBatchCreateDTO);

        // Verify the results
        assertThat(result).isEqualTo("SUCCESS");

        // Confirm TableOrderService.saveBatch(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setGuid("c67e6dfb-0a81-4deb-a3b3-e2a83ad3bbc0");
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("orderGuid");
        final List<TableOrderDO> entityList = Arrays.asList(tableOrderDO);
        verify(mockTableOrderService).saveBatch(entityList, 0);
        verify(mockTableTagRelationService).createTags(Arrays.asList("value"), Arrays.asList("value"));
    }

    @Test
    public void testBatchCreate_RedisServiceReturnsNoItems() {
        // Setup
        final TableBatchCreateDTO tableBatchCreateDTO = new TableBatchCreateDTO();
        tableBatchCreateDTO.setStoreGuid("storeGuid");
        tableBatchCreateDTO.setAreaGuid("areaGuid");
        tableBatchCreateDTO.setFixedFirstWord("fixedFirstWord");
        tableBatchCreateDTO.setBatchTableEnum(BatchTableEnum.FIXED_2);
        tableBatchCreateDTO.setStartNum(0);
        tableBatchCreateDTO.setTotal(0);
        tableBatchCreateDTO.setSeatsPerTable(0);
        tableBatchCreateDTO.setTagList(Arrays.asList("value"));

        // Configure TableBasicMapper.selectList(...).
        final TableBasicDO tableBasicDO = new TableBasicDO();
        tableBasicDO.setGuid("guid");
        tableBasicDO.setStoreGuid("storeGuid");
        tableBasicDO.setAreaGuid("areaGuid");
        tableBasicDO.setAreaName("areaName");
        tableBasicDO.setTableCode("tableCode");
        tableBasicDO.setSeats(0);
        tableBasicDO.setSort(0);
        tableBasicDO.setEnable(0);
        tableBasicDO.setDeleted(0);
        final List<TableBasicDO> tableBasicDOS = Arrays.asList(tableBasicDO);
        when(mockTableBasicMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableBasicDOS);

        when(mockRedisService.batchGuid(0, "hst_table_basic")).thenReturn(Collections.emptyList());
        when(mockTableBasicMapper.maxSort("storeGuid", "areaGuid")).thenReturn(0);

        // Run the test
        final String result = tableBasicServiceImplUnderTest.batchCreate(tableBatchCreateDTO);

        // Verify the results
        assertThat(result).isEqualTo("SUCCESS");

        // Confirm TableOrderService.saveBatch(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setGuid("c67e6dfb-0a81-4deb-a3b3-e2a83ad3bbc0");
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("orderGuid");
        final List<TableOrderDO> entityList = Arrays.asList(tableOrderDO);
        verify(mockTableOrderService).saveBatch(entityList, 0);
        verify(mockTableTagRelationService).createTags(Arrays.asList("value"), Arrays.asList("value"));
    }

    @Test
    public void testBatchCreate_TableBasicMapperMaxSortReturnsNull() {
        // Setup
        final TableBatchCreateDTO tableBatchCreateDTO = new TableBatchCreateDTO();
        tableBatchCreateDTO.setStoreGuid("storeGuid");
        tableBatchCreateDTO.setAreaGuid("areaGuid");
        tableBatchCreateDTO.setFixedFirstWord("fixedFirstWord");
        tableBatchCreateDTO.setBatchTableEnum(BatchTableEnum.FIXED_2);
        tableBatchCreateDTO.setStartNum(0);
        tableBatchCreateDTO.setTotal(0);
        tableBatchCreateDTO.setSeatsPerTable(0);
        tableBatchCreateDTO.setTagList(Arrays.asList("value"));

        // Configure TableBasicMapper.selectList(...).
        final TableBasicDO tableBasicDO = new TableBasicDO();
        tableBasicDO.setGuid("guid");
        tableBasicDO.setStoreGuid("storeGuid");
        tableBasicDO.setAreaGuid("areaGuid");
        tableBasicDO.setAreaName("areaName");
        tableBasicDO.setTableCode("tableCode");
        tableBasicDO.setSeats(0);
        tableBasicDO.setSort(0);
        tableBasicDO.setEnable(0);
        tableBasicDO.setDeleted(0);
        final List<TableBasicDO> tableBasicDOS = Arrays.asList(tableBasicDO);
        when(mockTableBasicMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableBasicDOS);

        when(mockRedisService.batchGuid(0, "hst_table_basic")).thenReturn(Arrays.asList("value"));
        when(mockTableBasicMapper.maxSort("storeGuid", "areaGuid")).thenReturn(null);

        // Run the test
        final String result = tableBasicServiceImplUnderTest.batchCreate(tableBatchCreateDTO);

        // Verify the results
        assertThat(result).isEqualTo("SUCCESS");

        // Confirm TableOrderService.saveBatch(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setGuid("c67e6dfb-0a81-4deb-a3b3-e2a83ad3bbc0");
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("orderGuid");
        final List<TableOrderDO> entityList = Arrays.asList(tableOrderDO);
        verify(mockTableOrderService).saveBatch(entityList, 0);
        verify(mockTableTagRelationService).createTags(Arrays.asList("value"), Arrays.asList("value"));
    }

    @Test
    public void testIsTableOccupied() {
        // Setup
        when(mockTableBasicMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final boolean result = tableBasicServiceImplUnderTest.isTableOccupied("areaGuid");

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testIsTableOccupied_TableBasicMapperReturnsNull() {
        // Setup
        when(mockTableBasicMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        final boolean result = tableBasicServiceImplUnderTest.isTableOccupied("areaGuid");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testQueryTableInfo() {
        // Setup
        final TableBasicDTO expectedResult = new TableBasicDTO();
        expectedResult.setGuid("guid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setAreaGuid("areaGuid");
        expectedResult.setTableCode("tableCode");
        expectedResult.setSort(0);
        expectedResult.setTagList(Arrays.asList("value"));
        final TableTagDTO tableTagDTO = new TableTagDTO();
        expectedResult.setTableTagDTOS(Arrays.asList(tableTagDTO));

        // Run the test
        final TableBasicDTO result = tableBasicServiceImplUnderTest.queryTableInfo("tableGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryUnBindingTableInfo() {
        // Setup
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setTableGuid("guid");
        tableInfoDTO.setTableName("tableCode");
        tableInfoDTO.setAreaGuid("areaGuid");
        tableInfoDTO.setAreaName("areaName");
        tableInfoDTO.setSort(0);
        final List<PadAreaDTO> expectedResult = Arrays.asList(
                new PadAreaDTO("areaGuid", "areaName", Arrays.asList(tableInfoDTO)));

        // Configure TableBasicMapper.selectList(...).
        final TableBasicDO tableBasicDO = new TableBasicDO();
        tableBasicDO.setGuid("guid");
        tableBasicDO.setStoreGuid("storeGuid");
        tableBasicDO.setAreaGuid("areaGuid");
        tableBasicDO.setAreaName("areaName");
        tableBasicDO.setTableCode("tableCode");
        tableBasicDO.setSeats(0);
        tableBasicDO.setSort(0);
        tableBasicDO.setEnable(0);
        tableBasicDO.setDeleted(0);
        final List<TableBasicDO> tableBasicDOS = Arrays.asList(tableBasicDO);
        when(mockTableBasicMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(tableBasicDOS);

        // Run the test
        final List<PadAreaDTO> result = tableBasicServiceImplUnderTest.queryUnBindingTableInfo(Arrays.asList("value"),
                "storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryUnBindingTableInfo_TableBasicMapperReturnsNoItems() {
        // Setup
        when(mockTableBasicMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<PadAreaDTO> result = tableBasicServiceImplUnderTest.queryUnBindingTableInfo(Arrays.asList("value"),
                "storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testListTableByStoreGuid() {
        // Setup
        final SingleDataDTO singleDataDTO = new SingleDataDTO("data", Arrays.asList("value"));
        final StoreAndTableDTO storeAndTableDTO = new StoreAndTableDTO();
        storeAndTableDTO.setStoreGuid("storeGuid");
        storeAndTableDTO.setTableGuidList(Arrays.asList("value"));
        final List<StoreAndTableDTO> expectedResult = Arrays.asList(storeAndTableDTO);

        // Run the test
        final List<StoreAndTableDTO> result = tableBasicServiceImplUnderTest.listTableByStoreGuid(singleDataDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
