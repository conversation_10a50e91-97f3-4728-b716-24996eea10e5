package com.holderzone.holder.saas.store.deposit.service.impl;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.holderzone.holder.saas.store.deposit.entity.bo.OperationDO;
import com.holderzone.holder.saas.store.deposit.mapper.HsdOperationMapper;
import com.holderzone.holder.saas.store.deposit.mapstruct.OperationMapstruct;
import com.holderzone.holder.saas.store.deposit.service.DistributedIdService;
import com.holderzone.holder.saas.store.deposit.service.IHsdOperationGoodsService;
import com.holderzone.saas.store.dto.deposit.req.OperationCreateReqDTO;

import java.time.LocalDate;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@ContextConfiguration(classes = {HsdOperationServiceImpl.class})
@RunWith(SpringJUnit4ClassRunner.class)
public class HsdOperationServiceImplDiffblueTest {
    @MockBean
    private DistributedIdService distributedIdService;

    @MockBean
    private HsdOperationMapper hsdOperationMapper;

    @Autowired
    private HsdOperationServiceImpl hsdOperationServiceImpl;

    @MockBean
    private IHsdOperationGoodsService iHsdOperationGoodsService;

    @MockBean
    private OperationMapstruct operationMapstruct;

    /**
     * Method under test:
     * {@link HsdOperationServiceImpl#createOperationRecord(OperationCreateReqDTO, String, String)}
     */
    @Test
    public void testCreateOperationRecord() {
        when(hsdOperationMapper.insert(Mockito.<OperationDO>any())).thenReturn(1);

        OperationDO operationDO = new OperationDO();
        operationDO.setDepositGuid("1234");
        operationDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationDO.setGuid("1234");
        operationDO.setId(1L);
        operationDO.setOperationWay(1);
        operationDO.setOperator("Operator");
        operationDO.setRemark("Remark");
        operationDO.setUserId("42");
        when(operationMapstruct.fromOperationDTO(Mockito.<OperationCreateReqDTO>any())).thenReturn(operationDO);

        OperationCreateReqDTO operationCreateReqDTO = new OperationCreateReqDTO();
        operationCreateReqDTO.setOperationWay(1);
        operationCreateReqDTO.setOperator("Operator");
        operationCreateReqDTO.setRemark("Remark");
        operationCreateReqDTO.setUserId("42");
        Boolean actualCreateOperationRecordResult = hsdOperationServiceImpl.createOperationRecord(operationCreateReqDTO,
                "1234", "1234");
        verify(hsdOperationMapper).insert(Mockito.<OperationDO>any());
        verify(operationMapstruct).fromOperationDTO(Mockito.<OperationCreateReqDTO>any());
        assertTrue(actualCreateOperationRecordResult);
    }

    /**
     * Method under test:
     * {@link HsdOperationServiceImpl#createOperationRecord(OperationCreateReqDTO, String, String)}
     */
    @Test
    public void testCreateOperationRecord2() {
        when(hsdOperationMapper.insert(Mockito.<OperationDO>any())).thenReturn(0);

        OperationDO operationDO = new OperationDO();
        operationDO.setDepositGuid("1234");
        operationDO.setGmtCreate(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationDO.setGmtModified(LocalDate.of(1970, 1, 1).atStartOfDay());
        operationDO.setGuid("1234");
        operationDO.setId(1L);
        operationDO.setOperationWay(1);
        operationDO.setOperator("Operator");
        operationDO.setRemark("Remark");
        operationDO.setUserId("42");
        when(operationMapstruct.fromOperationDTO(Mockito.<OperationCreateReqDTO>any())).thenReturn(operationDO);

        OperationCreateReqDTO operationCreateReqDTO = new OperationCreateReqDTO();
        operationCreateReqDTO.setOperationWay(1);
        operationCreateReqDTO.setOperator("Operator");
        operationCreateReqDTO.setRemark("Remark");
        operationCreateReqDTO.setUserId("42");
        Boolean actualCreateOperationRecordResult = hsdOperationServiceImpl.createOperationRecord(operationCreateReqDTO,
                "1234", "1234");
        verify(hsdOperationMapper).insert(Mockito.<OperationDO>any());
        verify(operationMapstruct).fromOperationDTO(Mockito.<OperationCreateReqDTO>any());
        assertFalse(actualCreateOperationRecordResult);
    }
}
