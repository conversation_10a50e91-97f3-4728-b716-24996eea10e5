package com.holderzone.holder.saas.store.report.service.impl;

import com.google.common.util.concurrent.MoreExecutors;
import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.holder.saas.store.report.mapper.TradeReturnMapper;
import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.ReturnDetailItemDTO;
import com.holderzone.saas.store.dto.report.resp.ReturnItemDTO;
import com.holderzone.saas.store.dto.report.resp.TotalStatisticsDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TradeReturnServiceImplTest {

    @Mock
    private TradeReturnMapper mockTradeReturnMapper;
    @Mock
    private OssClient mockOssClient;

    private TradeReturnServiceImpl tradeReturnServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        tradeReturnServiceImplUnderTest = new TradeReturnServiceImpl(mockTradeReturnMapper, mockOssClient,
                MoreExecutors.newDirectExecutorService());
    }

    @Test
    public void testList() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeReturnMapper.statistics(...).
        final TotalStatisticsDTO totalStatisticsDTO = new TotalStatisticsDTO("brandGuid", "storeGuid", "goodsGuid",
                new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"));
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeReturnMapper.statistics(query1)).thenReturn(totalStatisticsDTO);

        // Configure TradeReturnMapper.count(...).
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeReturnMapper.count(query2)).thenReturn(0);

        // Configure TradeReturnMapper.pageInfo(...).
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        returnItemDTO.setBrandName("brandName");
        returnItemDTO.setBrandGuid("brandGuid");
        returnItemDTO.setStoreName("storeName");
        returnItemDTO.setReturnQuantity(new BigDecimal("0.00"));
        returnItemDTO.setRefundRate("refundRate");
        final List<ReturnItemDTO> list = Arrays.asList(returnItemDTO);
        final ReportQueryVO query3 = new ReportQueryVO();
        query3.setCurrentPage(0);
        query3.setPageSize(0);
        query3.setStartTime(LocalDate.of(2020, 1, 1));
        query3.setEndTime(LocalDate.of(2020, 1, 1));
        query3.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeReturnMapper.pageInfo(query3)).thenReturn(list);

        // Run the test
        final Message<ReturnItemDTO> result = tradeReturnServiceImplUnderTest.list(query);

        // Verify the results
    }

    @Test
    public void testList_TradeReturnMapperPageInfoReturnsNoItems() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeReturnMapper.statistics(...).
        final TotalStatisticsDTO totalStatisticsDTO = new TotalStatisticsDTO("brandGuid", "storeGuid", "goodsGuid",
                new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"));
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeReturnMapper.statistics(query1)).thenReturn(totalStatisticsDTO);

        // Configure TradeReturnMapper.count(...).
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeReturnMapper.count(query2)).thenReturn(0);

        // Configure TradeReturnMapper.pageInfo(...).
        final ReportQueryVO query3 = new ReportQueryVO();
        query3.setCurrentPage(0);
        query3.setPageSize(0);
        query3.setStartTime(LocalDate.of(2020, 1, 1));
        query3.setEndTime(LocalDate.of(2020, 1, 1));
        query3.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeReturnMapper.pageInfo(query3)).thenReturn(Collections.emptyList());

        // Run the test
        final Message<ReturnItemDTO> result = tradeReturnServiceImplUnderTest.list(query);

        // Verify the results
    }

    @Test
    public void testListDetail() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeReturnMapper.countReturnDetail(...).
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeReturnMapper.countReturnDetail(query1)).thenReturn(0);

        // Configure TradeReturnMapper.statistics(...).
        final TotalStatisticsDTO totalStatisticsDTO = new TotalStatisticsDTO("brandGuid", "storeGuid", "goodsGuid",
                new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"));
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeReturnMapper.statistics(query2)).thenReturn(totalStatisticsDTO);

        // Configure TradeReturnMapper.listReturnDetail(...).
        final ReturnDetailItemDTO returnDetailItemDTO = new ReturnDetailItemDTO();
        returnDetailItemDTO.setStoreName("storeName");
        returnDetailItemDTO.setOrderNo("orderNo");
        returnDetailItemDTO.setCateringType("cateringType");
        returnDetailItemDTO.setSkuGuid("skuGuid");
        returnDetailItemDTO.setGoodsName("goodsName");
        final List<ReturnDetailItemDTO> returnDetailItemDTOS = Arrays.asList(returnDetailItemDTO);
        final ReportQueryVO query3 = new ReportQueryVO();
        query3.setCurrentPage(0);
        query3.setPageSize(0);
        query3.setStartTime(LocalDate.of(2020, 1, 1));
        query3.setEndTime(LocalDate.of(2020, 1, 1));
        query3.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeReturnMapper.listReturnDetail(query3)).thenReturn(returnDetailItemDTOS);

        // Run the test
        final Message<ReturnDetailItemDTO> result = tradeReturnServiceImplUnderTest.listDetail(query);

        // Verify the results
    }

    @Test
    public void testListDetail_TradeReturnMapperListReturnDetailReturnsNoItems() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeReturnMapper.countReturnDetail(...).
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeReturnMapper.countReturnDetail(query1)).thenReturn(0);

        // Configure TradeReturnMapper.statistics(...).
        final TotalStatisticsDTO totalStatisticsDTO = new TotalStatisticsDTO("brandGuid", "storeGuid", "goodsGuid",
                new BigDecimal("0.00"), new BigDecimal("0.00"), new BigDecimal("0.00"));
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeReturnMapper.statistics(query2)).thenReturn(totalStatisticsDTO);

        // Configure TradeReturnMapper.listReturnDetail(...).
        final ReportQueryVO query3 = new ReportQueryVO();
        query3.setCurrentPage(0);
        query3.setPageSize(0);
        query3.setStartTime(LocalDate.of(2020, 1, 1));
        query3.setEndTime(LocalDate.of(2020, 1, 1));
        query3.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeReturnMapper.listReturnDetail(query3)).thenReturn(Collections.emptyList());

        // Run the test
        final Message<ReturnDetailItemDTO> result = tradeReturnServiceImplUnderTest.listDetail(query);

        // Verify the results
    }

    @Test
    public void testExportReturnDetail() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeReturnMapper.countReturnDetail(...).
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeReturnMapper.countReturnDetail(query1)).thenReturn(0);

        // Configure TradeReturnMapper.listReturnDetail(...).
        final ReturnDetailItemDTO returnDetailItemDTO = new ReturnDetailItemDTO();
        returnDetailItemDTO.setStoreName("storeName");
        returnDetailItemDTO.setOrderNo("orderNo");
        returnDetailItemDTO.setCateringType("cateringType");
        returnDetailItemDTO.setSkuGuid("skuGuid");
        returnDetailItemDTO.setGoodsName("goodsName");
        final List<ReturnDetailItemDTO> returnDetailItemDTOS = Arrays.asList(returnDetailItemDTO);
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeReturnMapper.listReturnDetail(query2)).thenReturn(returnDetailItemDTOS);

        // Run the test
        final String result = tradeReturnServiceImplUnderTest.exportReturnDetail(query);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testExportReturnDetail_TradeReturnMapperListReturnDetailReturnsNoItems() {
        // Setup
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");

        // Configure TradeReturnMapper.countReturnDetail(...).
        final ReportQueryVO query1 = new ReportQueryVO();
        query1.setCurrentPage(0);
        query1.setPageSize(0);
        query1.setStartTime(LocalDate.of(2020, 1, 1));
        query1.setEndTime(LocalDate.of(2020, 1, 1));
        query1.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeReturnMapper.countReturnDetail(query1)).thenReturn(0);

        // Configure TradeReturnMapper.listReturnDetail(...).
        final ReportQueryVO query2 = new ReportQueryVO();
        query2.setCurrentPage(0);
        query2.setPageSize(0);
        query2.setStartTime(LocalDate.of(2020, 1, 1));
        query2.setEndTime(LocalDate.of(2020, 1, 1));
        query2.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeReturnMapper.listReturnDetail(query2)).thenReturn(Collections.emptyList());

        // Run the test
        final String result = tradeReturnServiceImplUnderTest.exportReturnDetail(query);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }
}
