package com.holderzone.holder.saas.store.pay.service.rpc.impl;

import com.holderzone.framework.response.LogicResponse;
import com.holderzone.holder.saas.store.pay.config.AggPayConfig;
import com.holderzone.holder.saas.store.pay.entity.AggPayReserveVO;
import com.holderzone.saas.store.dto.pay.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AggPayRpcServiceImplTest {

    @Mock
    private AggPayConfig mockAggPayConfig;
    @Mock
    private RestTemplate mockRemoteRestTemplate;

    private AggPayRpcServiceImpl aggPayRpcServiceImplUnderTest;

    private HttpClient customHttpClient;

    @Before
    public void setUp() {
        aggPayRpcServiceImplUnderTest = new AggPayRpcServiceImpl(mockAggPayConfig, mockRemoteRestTemplate, customHttpClient);
    }

    @Test
    public void testPreTrading() {
        // Setup
        final AggPayPreTradingReqDTO payPreTradingReqDTO = new AggPayPreTradingReqDTO();
        payPreTradingReqDTO.setAmount(new BigDecimal("0.00"));
        payPreTradingReqDTO.setOrderGUID("b1030eec-8b38-4cc4-aa67-3bca2f8fa348");
        payPreTradingReqDTO.setPayGUID("599e0112-3325-4eb2-8642-3def3080b169");
        payPreTradingReqDTO.setGoodsName("goodsName");
        payPreTradingReqDTO.setBody("body");

        final AggPayRespDTO expectedResult = new AggPayRespDTO();
        expectedResult.setCode("code");
        expectedResult.setMsg("msg");
        expectedResult.setResult("result");
        expectedResult.setPayGuid("payGuid");
        expectedResult.setOrderGuid("orderGuid");

        when(mockAggPayConfig.getPay()).thenReturn("pay");

        // Configure RestTemplate.exchange(...).
        final AggPayRespDTO aggPayRespDTO = new AggPayRespDTO();
        aggPayRespDTO.setCode("code");
        aggPayRespDTO.setMsg("msg");
        aggPayRespDTO.setResult("result");
        aggPayRespDTO.setPayGuid("payGuid");
        aggPayRespDTO.setOrderGuid("orderGuid");
        final ResponseEntity<LogicResponse<AggPayRespDTO>> logicResponseEntity = new ResponseEntity<>(
                LogicResponse.success(aggPayRespDTO), HttpStatus.OK);
        final AggPayPreTradingReqDTO aggPayPreTradingReqDTO = new AggPayPreTradingReqDTO();
        aggPayPreTradingReqDTO.setAmount(new BigDecimal("0.00"));
        aggPayPreTradingReqDTO.setOrderGUID("b1030eec-8b38-4cc4-aa67-3bca2f8fa348");
        aggPayPreTradingReqDTO.setPayGUID("599e0112-3325-4eb2-8642-3def3080b169");
        aggPayPreTradingReqDTO.setGoodsName("goodsName");
        aggPayPreTradingReqDTO.setBody("body");
        final HttpEntity<AggPayPreTradingReqDTO> requestEntity = new HttpEntity<>(aggPayPreTradingReqDTO,
                new HttpHeaders());
        when(mockRemoteRestTemplate.exchange("pay", HttpMethod.POST, requestEntity,
                new ParameterizedTypeReference<LogicResponse<AggPayRespDTO>>() {})).thenReturn(logicResponseEntity);

        // Run the test
        final AggPayRespDTO result = aggPayRpcServiceImplUnderTest.preTrading(payPreTradingReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testPreTrading_RestTemplateThrowsRestClientException() {
        // Setup
        final AggPayPreTradingReqDTO payPreTradingReqDTO = new AggPayPreTradingReqDTO();
        payPreTradingReqDTO.setAmount(new BigDecimal("0.00"));
        payPreTradingReqDTO.setOrderGUID("b1030eec-8b38-4cc4-aa67-3bca2f8fa348");
        payPreTradingReqDTO.setPayGUID("599e0112-3325-4eb2-8642-3def3080b169");
        payPreTradingReqDTO.setGoodsName("goodsName");
        payPreTradingReqDTO.setBody("body");

        final AggPayRespDTO expectedResult = new AggPayRespDTO();
        expectedResult.setCode("code");
        expectedResult.setMsg("msg");
        expectedResult.setResult("result");
        expectedResult.setPayGuid("payGuid");
        expectedResult.setOrderGuid("orderGuid");

        when(mockAggPayConfig.getPay()).thenReturn("pay");

        // Configure RestTemplate.exchange(...).
        final AggPayPreTradingReqDTO aggPayPreTradingReqDTO = new AggPayPreTradingReqDTO();
        aggPayPreTradingReqDTO.setAmount(new BigDecimal("0.00"));
        aggPayPreTradingReqDTO.setOrderGUID("b1030eec-8b38-4cc4-aa67-3bca2f8fa348");
        aggPayPreTradingReqDTO.setPayGUID("599e0112-3325-4eb2-8642-3def3080b169");
        aggPayPreTradingReqDTO.setGoodsName("goodsName");
        aggPayPreTradingReqDTO.setBody("body");
        final HttpEntity<AggPayPreTradingReqDTO> requestEntity = new HttpEntity<>(aggPayPreTradingReqDTO,
                new HttpHeaders());
        when(mockRemoteRestTemplate.exchange("pay", HttpMethod.POST, requestEntity,
                new ParameterizedTypeReference<RestClientException>() {})).thenThrow(RestClientException.class);

        // Run the test
        final AggPayRespDTO result = aggPayRpcServiceImplUnderTest.preTrading(payPreTradingReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testPreTradingAsync() {
        // Setup
        final AggPayPreTradingReqDTO aggPayPreTradingReqDTO = new AggPayPreTradingReqDTO();
        aggPayPreTradingReqDTO.setAmount(new BigDecimal("0.00"));
        aggPayPreTradingReqDTO.setOrderGUID("b1030eec-8b38-4cc4-aa67-3bca2f8fa348");
        aggPayPreTradingReqDTO.setPayGUID("599e0112-3325-4eb2-8642-3def3080b169");
        aggPayPreTradingReqDTO.setGoodsName("goodsName");
        aggPayPreTradingReqDTO.setBody("body");

        when(mockAggPayConfig.getPay()).thenReturn("result");

        // Run the test
        final Mono<AggPayRespDTO> result = aggPayRpcServiceImplUnderTest.preTradingAsync(aggPayPreTradingReqDTO);

        // Verify the results
    }

    @Test
    public void testDoPolling() {
        // Setup
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("df38fe1a-7762-40e6-83d0-e04cbef4c6e9");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("3f30534f-ce1c-459a-8530-5827e2f07e33");
        pollingJHPayDTO.setAppId("appId");

        final AggPayPollingRespDTO expectedResult = new AggPayPollingRespDTO();
        expectedResult.setCode("code");
        expectedResult.setMsg("msg");
        expectedResult.setAmount(new BigDecimal("0.00"));
        expectedResult.setSubject("subject");
        expectedResult.setBody("body");

        when(mockAggPayConfig.getPolling()).thenReturn("polling");

        // Configure RestTemplate.exchange(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setMsg("msg");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setSubject("subject");
        aggPayPollingRespDTO.setBody("body");
        final ResponseEntity<LogicResponse<AggPayPollingRespDTO>> logicResponseEntity = new ResponseEntity<>(
                LogicResponse.success(aggPayPollingRespDTO), HttpStatus.OK);
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("df38fe1a-7762-40e6-83d0-e04cbef4c6e9");
        aggPayPollingDTO.setAttachData("attachData");
        aggPayPollingDTO.setRefundNo("refundNo");
        aggPayPollingDTO.setPayGUID("3f30534f-ce1c-459a-8530-5827e2f07e33");
        aggPayPollingDTO.setAppId("appId");
        final HttpEntity<AggPayPollingDTO> requestEntity = new HttpEntity<>(aggPayPollingDTO, new HttpHeaders());
        when(mockRemoteRestTemplate.exchange("polling", HttpMethod.POST, requestEntity,
                new ParameterizedTypeReference<LogicResponse<AggPayPollingRespDTO>>() {})).thenReturn(logicResponseEntity);

        // Run the test
        final AggPayPollingRespDTO result = aggPayRpcServiceImplUnderTest.doPolling(pollingJHPayDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testDoPolling_RestTemplateThrowsRestClientException() {
        // Setup
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("df38fe1a-7762-40e6-83d0-e04cbef4c6e9");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("3f30534f-ce1c-459a-8530-5827e2f07e33");
        pollingJHPayDTO.setAppId("appId");

        final AggPayPollingRespDTO expectedResult = new AggPayPollingRespDTO();
        expectedResult.setCode("code");
        expectedResult.setMsg("msg");
        expectedResult.setAmount(new BigDecimal("0.00"));
        expectedResult.setSubject("subject");
        expectedResult.setBody("body");

        when(mockAggPayConfig.getPolling()).thenReturn("polling");

        // Configure RestTemplate.exchange(...).
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("df38fe1a-7762-40e6-83d0-e04cbef4c6e9");
        aggPayPollingDTO.setAttachData("attachData");
        aggPayPollingDTO.setRefundNo("refundNo");
        aggPayPollingDTO.setPayGUID("3f30534f-ce1c-459a-8530-5827e2f07e33");
        aggPayPollingDTO.setAppId("appId");
        final HttpEntity<AggPayPollingDTO> requestEntity = new HttpEntity<>(aggPayPollingDTO, new HttpHeaders());
        when(mockRemoteRestTemplate.exchange("polling", HttpMethod.POST, requestEntity,
                new ParameterizedTypeReference<RestClientException>() {})).thenThrow(RestClientException.class);

        // Run the test
        final AggPayPollingRespDTO result = aggPayRpcServiceImplUnderTest.doPolling(pollingJHPayDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testDoPollingAsync() {
        // Setup
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("df38fe1a-7762-40e6-83d0-e04cbef4c6e9");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("3f30534f-ce1c-459a-8530-5827e2f07e33");
        pollingJHPayDTO.setAppId("appId");

        when(mockAggPayConfig.getPolling()).thenReturn("result");

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayRpcServiceImplUnderTest.doPollingAsync(pollingJHPayDTO);

        // Verify the results
    }

    @Test
    public void testPrePayQueryBank() {
        // Setup
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("df38fe1a-7762-40e6-83d0-e04cbef4c6e9");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("3f30534f-ce1c-459a-8530-5827e2f07e33");
        pollingJHPayDTO.setAppId("appId");

        final AggPayPollingRespDTO expectedResult = new AggPayPollingRespDTO();
        expectedResult.setCode("code");
        expectedResult.setMsg("msg");
        expectedResult.setAmount(new BigDecimal("0.00"));
        expectedResult.setSubject("subject");
        expectedResult.setBody("body");

        when(mockAggPayConfig.getQuery()).thenReturn("query");

        // Configure RestTemplate.exchange(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setMsg("msg");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setSubject("subject");
        aggPayPollingRespDTO.setBody("body");
        final ResponseEntity<LogicResponse<AggPayPollingRespDTO>> logicResponseEntity = new ResponseEntity<>(
                LogicResponse.success(aggPayPollingRespDTO), HttpStatus.OK);
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("df38fe1a-7762-40e6-83d0-e04cbef4c6e9");
        aggPayPollingDTO.setAttachData("attachData");
        aggPayPollingDTO.setRefundNo("refundNo");
        aggPayPollingDTO.setPayGUID("3f30534f-ce1c-459a-8530-5827e2f07e33");
        aggPayPollingDTO.setAppId("appId");
        final HttpEntity<AggPayPollingDTO> requestEntity = new HttpEntity<>(aggPayPollingDTO, new HttpHeaders());
        when(mockRemoteRestTemplate.exchange("query", HttpMethod.POST, requestEntity,
                new ParameterizedTypeReference<LogicResponse<AggPayPollingRespDTO>>() {})).thenReturn(logicResponseEntity);

        // Run the test
        final AggPayPollingRespDTO result = aggPayRpcServiceImplUnderTest.prePayQueryBank(pollingJHPayDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testPrePayQueryBank_RestTemplateThrowsRestClientException() {
        // Setup
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("df38fe1a-7762-40e6-83d0-e04cbef4c6e9");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("3f30534f-ce1c-459a-8530-5827e2f07e33");
        pollingJHPayDTO.setAppId("appId");

        final AggPayPollingRespDTO expectedResult = new AggPayPollingRespDTO();
        expectedResult.setCode("code");
        expectedResult.setMsg("msg");
        expectedResult.setAmount(new BigDecimal("0.00"));
        expectedResult.setSubject("subject");
        expectedResult.setBody("body");

        when(mockAggPayConfig.getQuery()).thenReturn("query");

        // Configure RestTemplate.exchange(...).
        final AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();
        aggPayPollingDTO.setOrderGUID("df38fe1a-7762-40e6-83d0-e04cbef4c6e9");
        aggPayPollingDTO.setAttachData("attachData");
        aggPayPollingDTO.setRefundNo("refundNo");
        aggPayPollingDTO.setPayGUID("3f30534f-ce1c-459a-8530-5827e2f07e33");
        aggPayPollingDTO.setAppId("appId");
        final HttpEntity<AggPayPollingDTO> requestEntity = new HttpEntity<>(aggPayPollingDTO, new HttpHeaders());
        when(mockRemoteRestTemplate.exchange("query", HttpMethod.POST, requestEntity,
                new ParameterizedTypeReference<RestClientException>() {})).thenThrow(RestClientException.class);

        // Run the test
        final AggPayPollingRespDTO result = aggPayRpcServiceImplUnderTest.prePayQueryBank(pollingJHPayDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testPrePayQueryBankAsync() {
        // Setup
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("df38fe1a-7762-40e6-83d0-e04cbef4c6e9");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("3f30534f-ce1c-459a-8530-5827e2f07e33");
        pollingJHPayDTO.setAppId("appId");

        when(mockAggPayConfig.getQuery()).thenReturn("result");

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayRpcServiceImplUnderTest.prePayQueryBankAsync(pollingJHPayDTO);

        // Verify the results
    }

    @Test
    public void testPayPayQueryBankAsync() {
        // Setup
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("df38fe1a-7762-40e6-83d0-e04cbef4c6e9");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("3f30534f-ce1c-459a-8530-5827e2f07e33");
        pollingJHPayDTO.setAppId("appId");

        when(mockAggPayConfig.getQueryPaySt()).thenReturn("result");

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayRpcServiceImplUnderTest.payPayQueryBankAsync(pollingJHPayDTO);

        // Verify the results
    }

    @Test
    public void testRefund() {
        // Setup
        final AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
        aggRefundReqDTO.setPayGUID("412e7a5f-680e-401b-aee3-1eb75307bea7");
        aggRefundReqDTO.setOrderGUID("d4ef7f2d-ace3-4ee5-8bc6-92aaf267a2cd");
        aggRefundReqDTO.setRefundType(0);
        aggRefundReqDTO.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO.setReason("reason");

        final AggRefundRespDTO expectedResult = new AggRefundRespDTO();
        expectedResult.setCode("code");
        expectedResult.setMsg("msg");
        expectedResult.setMchntOrderNo("mchntOrderNo");
        expectedResult.setRefOrderNo("refOrderNo");
        expectedResult.setRefundFee("refundFee");

        when(mockAggPayConfig.getRefund()).thenReturn("refund");

        // Configure RestTemplate.exchange(...).
        final AggRefundRespDTO aggRefundRespDTO = new AggRefundRespDTO();
        aggRefundRespDTO.setCode("code");
        aggRefundRespDTO.setMsg("msg");
        aggRefundRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundRespDTO.setRefOrderNo("refOrderNo");
        aggRefundRespDTO.setRefundFee("refundFee");
        final ResponseEntity<LogicResponse<AggRefundRespDTO>> logicResponseEntity = new ResponseEntity<>(
                LogicResponse.success(aggRefundRespDTO), HttpStatus.OK);
        final AggRefundReqDTO aggRefundReqDTO1 = new AggRefundReqDTO();
        aggRefundReqDTO1.setPayGUID("412e7a5f-680e-401b-aee3-1eb75307bea7");
        aggRefundReqDTO1.setOrderGUID("d4ef7f2d-ace3-4ee5-8bc6-92aaf267a2cd");
        aggRefundReqDTO1.setRefundType(0);
        aggRefundReqDTO1.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO1.setReason("reason");
        final HttpEntity<AggRefundReqDTO> requestEntity = new HttpEntity<>(aggRefundReqDTO1, new HttpHeaders());
        when(mockRemoteRestTemplate.exchange("refund", HttpMethod.POST, requestEntity,
                new ParameterizedTypeReference<LogicResponse<AggRefundRespDTO>>() {})).thenReturn(logicResponseEntity);

        // Run the test
        final AggRefundRespDTO result = aggPayRpcServiceImplUnderTest.refund(aggRefundReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testRefund_RestTemplateThrowsRestClientException() {
        // Setup
        final AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
        aggRefundReqDTO.setPayGUID("412e7a5f-680e-401b-aee3-1eb75307bea7");
        aggRefundReqDTO.setOrderGUID("d4ef7f2d-ace3-4ee5-8bc6-92aaf267a2cd");
        aggRefundReqDTO.setRefundType(0);
        aggRefundReqDTO.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO.setReason("reason");

        final AggRefundRespDTO expectedResult = new AggRefundRespDTO();
        expectedResult.setCode("code");
        expectedResult.setMsg("msg");
        expectedResult.setMchntOrderNo("mchntOrderNo");
        expectedResult.setRefOrderNo("refOrderNo");
        expectedResult.setRefundFee("refundFee");

        when(mockAggPayConfig.getRefund()).thenReturn("refund");

        // Configure RestTemplate.exchange(...).
        final AggRefundReqDTO aggRefundReqDTO1 = new AggRefundReqDTO();
        aggRefundReqDTO1.setPayGUID("412e7a5f-680e-401b-aee3-1eb75307bea7");
        aggRefundReqDTO1.setOrderGUID("d4ef7f2d-ace3-4ee5-8bc6-92aaf267a2cd");
        aggRefundReqDTO1.setRefundType(0);
        aggRefundReqDTO1.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO1.setReason("reason");
        final HttpEntity<AggRefundReqDTO> requestEntity = new HttpEntity<>(aggRefundReqDTO1, new HttpHeaders());
        when(mockRemoteRestTemplate.exchange("refund", HttpMethod.POST, requestEntity,
                new ParameterizedTypeReference<RestClientException>() {})).thenThrow(RestClientException.class);

        // Run the test
        final AggRefundRespDTO result = aggPayRpcServiceImplUnderTest.refund(aggRefundReqDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testRefundAsync() {
        // Setup
        final AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
        aggRefundReqDTO.setPayGUID("412e7a5f-680e-401b-aee3-1eb75307bea7");
        aggRefundReqDTO.setOrderGUID("d4ef7f2d-ace3-4ee5-8bc6-92aaf267a2cd");
        aggRefundReqDTO.setRefundType(0);
        aggRefundReqDTO.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO.setReason("reason");

        when(mockAggPayConfig.getRefund()).thenReturn("result");

        // Run the test
        final Mono<AggRefundRespDTO> result = aggPayRpcServiceImplUnderTest.refundAsync(aggRefundReqDTO);

        // Verify the results
    }

    @Test
    public void testDoRefundPolling() {
        // Setup
        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("d96ff6b9-03c4-48be-ba69-3bf573cc757b");
        aggRefundPollingDTO.setAttachData("attachData");
        aggRefundPollingDTO.setRefundNo("refundNo");
        aggRefundPollingDTO.setPayGUID("2df6ec1b-d5d9-4c72-a274-826a4ace52a1");
        aggRefundPollingDTO.setAppId("appId");

        final AggRefundPollingRespDTO expectedResult = new AggRefundPollingRespDTO();
        expectedResult.setCode("code");
        expectedResult.setMsg("msg");
        expectedResult.setMchntOrderNo("mchntOrderNo");
        expectedResult.setOrderNo("orderNo");
        expectedResult.setMchntId("mchntId");

        when(mockAggPayConfig.getRefundPolling()).thenReturn("refundPolling");

        // Configure RestTemplate.exchange(...).
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode("code");
        aggRefundPollingRespDTO.setMsg("msg");
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setOrderNo("orderNo");
        aggRefundPollingRespDTO.setMchntId("mchntId");
        final ResponseEntity<LogicResponse<AggRefundPollingRespDTO>> logicResponseEntity = new ResponseEntity<>(
                LogicResponse.success(aggRefundPollingRespDTO), HttpStatus.OK);
        final AggRefundPollingDTO aggRefundPollingDTO1 = new AggRefundPollingDTO();
        aggRefundPollingDTO1.setOrderGUID("d96ff6b9-03c4-48be-ba69-3bf573cc757b");
        aggRefundPollingDTO1.setAttachData("attachData");
        aggRefundPollingDTO1.setRefundNo("refundNo");
        aggRefundPollingDTO1.setPayGUID("2df6ec1b-d5d9-4c72-a274-826a4ace52a1");
        aggRefundPollingDTO1.setAppId("appId");
        final HttpEntity<AggRefundPollingDTO> requestEntity = new HttpEntity<>(aggRefundPollingDTO1, new HttpHeaders());
        when(mockRemoteRestTemplate.exchange("refundPolling", HttpMethod.POST, requestEntity,
                new ParameterizedTypeReference<LogicResponse<AggRefundPollingRespDTO>>() {})).thenReturn(logicResponseEntity);

        // Run the test
        final AggRefundPollingRespDTO result = aggPayRpcServiceImplUnderTest.doRefundPolling(aggRefundPollingDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testDoRefundPolling_RestTemplateThrowsRestClientException() {
        // Setup
        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("d96ff6b9-03c4-48be-ba69-3bf573cc757b");
        aggRefundPollingDTO.setAttachData("attachData");
        aggRefundPollingDTO.setRefundNo("refundNo");
        aggRefundPollingDTO.setPayGUID("2df6ec1b-d5d9-4c72-a274-826a4ace52a1");
        aggRefundPollingDTO.setAppId("appId");

        final AggRefundPollingRespDTO expectedResult = new AggRefundPollingRespDTO();
        expectedResult.setCode("code");
        expectedResult.setMsg("msg");
        expectedResult.setMchntOrderNo("mchntOrderNo");
        expectedResult.setOrderNo("orderNo");
        expectedResult.setMchntId("mchntId");

        when(mockAggPayConfig.getRefundPolling()).thenReturn("refundPolling");

        // Configure RestTemplate.exchange(...).
        final AggRefundPollingDTO aggRefundPollingDTO1 = new AggRefundPollingDTO();
        aggRefundPollingDTO1.setOrderGUID("d96ff6b9-03c4-48be-ba69-3bf573cc757b");
        aggRefundPollingDTO1.setAttachData("attachData");
        aggRefundPollingDTO1.setRefundNo("refundNo");
        aggRefundPollingDTO1.setPayGUID("2df6ec1b-d5d9-4c72-a274-826a4ace52a1");
        aggRefundPollingDTO1.setAppId("appId");
        final HttpEntity<AggRefundPollingDTO> requestEntity = new HttpEntity<>(aggRefundPollingDTO1, new HttpHeaders());
        when(mockRemoteRestTemplate.exchange("refundPolling", HttpMethod.POST, requestEntity,
                new ParameterizedTypeReference<RestClientException>() {})).thenThrow(RestClientException.class);

        // Run the test
        final AggRefundPollingRespDTO result = aggPayRpcServiceImplUnderTest.doRefundPolling(aggRefundPollingDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testDoRefundPollingAsync() {
        // Setup
        final AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setOrderGUID("d96ff6b9-03c4-48be-ba69-3bf573cc757b");
        aggRefundPollingDTO.setAttachData("attachData");
        aggRefundPollingDTO.setRefundNo("refundNo");
        aggRefundPollingDTO.setPayGUID("2df6ec1b-d5d9-4c72-a274-826a4ace52a1");
        aggRefundPollingDTO.setAppId("appId");

        when(mockAggPayConfig.getRefundPolling()).thenReturn("result");

        // Run the test
        final Mono<AggRefundPollingRespDTO> result = aggPayRpcServiceImplUnderTest.doRefundPollingAsync(
                aggRefundPollingDTO);

        // Verify the results
    }

    @Test
    public void testWeChatPublicAccountPay() {
        // Setup
        final AggWeChatPublicAccountPayDTO accountPayDTO = new AggWeChatPublicAccountPayDTO();
        accountPayDTO.setAmount(new BigDecimal("0.00"));
        accountPayDTO.setOrderGUID("7eee4e96-996a-41f4-8f13-7ce6efc0a543");
        accountPayDTO.setPayGUID("d654d7d1-b18c-4193-bab7-082e1f7b5a8f");
        accountPayDTO.setGoodsName("goodsName");
        accountPayDTO.setBody("body");

        when(mockAggPayConfig.getWechath5pay()).thenReturn("wechath5pay");

        // Configure RestTemplate.exchange(...).
        final AggWeChatPublicAccountPayDTO aggWeChatPublicAccountPayDTO = new AggWeChatPublicAccountPayDTO();
        aggWeChatPublicAccountPayDTO.setAmount(new BigDecimal("0.00"));
        aggWeChatPublicAccountPayDTO.setOrderGUID("7eee4e96-996a-41f4-8f13-7ce6efc0a543");
        aggWeChatPublicAccountPayDTO.setPayGUID("d654d7d1-b18c-4193-bab7-082e1f7b5a8f");
        aggWeChatPublicAccountPayDTO.setGoodsName("goodsName");
        aggWeChatPublicAccountPayDTO.setBody("body");
        final HttpEntity<AggWeChatPublicAccountPayDTO> requestEntity = new HttpEntity<>(aggWeChatPublicAccountPayDTO,
                new HttpHeaders());
        when(mockRemoteRestTemplate.exchange("wechath5pay", HttpMethod.POST, requestEntity,
                new ParameterizedTypeReference() {})).thenReturn(new ResponseEntity<>("body", HttpStatus.OK));

        // Run the test
        final String result = aggPayRpcServiceImplUnderTest.weChatPublicAccountPay(accountPayDTO);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testWeChatPublicAccountPay_RestTemplateThrowsRestClientException() {
        // Setup
        final AggWeChatPublicAccountPayDTO accountPayDTO = new AggWeChatPublicAccountPayDTO();
        accountPayDTO.setAmount(new BigDecimal("0.00"));
        accountPayDTO.setOrderGUID("7eee4e96-996a-41f4-8f13-7ce6efc0a543");
        accountPayDTO.setPayGUID("d654d7d1-b18c-4193-bab7-082e1f7b5a8f");
        accountPayDTO.setGoodsName("goodsName");
        accountPayDTO.setBody("body");

        when(mockAggPayConfig.getWechath5pay()).thenReturn("wechath5pay");

        // Configure RestTemplate.exchange(...).
        final AggWeChatPublicAccountPayDTO aggWeChatPublicAccountPayDTO = new AggWeChatPublicAccountPayDTO();
        aggWeChatPublicAccountPayDTO.setAmount(new BigDecimal("0.00"));
        aggWeChatPublicAccountPayDTO.setOrderGUID("7eee4e96-996a-41f4-8f13-7ce6efc0a543");
        aggWeChatPublicAccountPayDTO.setPayGUID("d654d7d1-b18c-4193-bab7-082e1f7b5a8f");
        aggWeChatPublicAccountPayDTO.setGoodsName("goodsName");
        aggWeChatPublicAccountPayDTO.setBody("body");
        final HttpEntity<AggWeChatPublicAccountPayDTO> requestEntity = new HttpEntity<>(aggWeChatPublicAccountPayDTO,
                new HttpHeaders());
        when(mockRemoteRestTemplate.exchange("wechath5pay", HttpMethod.POST, requestEntity,
                new ParameterizedTypeReference<RestClientException>() {})).thenThrow(RestClientException.class);

        // Run the test
        final String result = aggPayRpcServiceImplUnderTest.weChatPublicAccountPay(accountPayDTO);

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testWeChatPublicAccountPayAsync() {
        // Setup
        final AggWeChatPublicAccountPayDTO accountPayDTO = new AggWeChatPublicAccountPayDTO();
        accountPayDTO.setAmount(new BigDecimal("0.00"));
        accountPayDTO.setOrderGUID("7eee4e96-996a-41f4-8f13-7ce6efc0a543");
        accountPayDTO.setPayGUID("d654d7d1-b18c-4193-bab7-082e1f7b5a8f");
        accountPayDTO.setGoodsName("goodsName");
        accountPayDTO.setBody("body");

        when(mockAggPayConfig.getWechath5pay()).thenReturn("result");

        // Run the test
        final Mono<String> result = aggPayRpcServiceImplUnderTest.weChatPublicAccountPayAsync(accountPayDTO);

        // Verify the results
    }

    @Test
    public void testDoWeChatPublicAccountPolling() {
        // Setup
        final AggPayPollingRespDTO expectedResult = new AggPayPollingRespDTO();
        expectedResult.setCode("code");
        expectedResult.setMsg("msg");
        expectedResult.setAmount(new BigDecimal("0.00"));
        expectedResult.setSubject("subject");
        expectedResult.setBody("body");

        when(mockAggPayConfig.getWechath5polling()).thenReturn("result");

        // Configure RestTemplate.exchange(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setMsg("msg");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setSubject("subject");
        aggPayPollingRespDTO.setBody("body");
        final ResponseEntity<LogicResponse<AggPayPollingRespDTO>> logicResponseEntity = new ResponseEntity<>(
                LogicResponse.success(aggPayPollingRespDTO), HttpStatus.OK);
        when(mockRemoteRestTemplate.exchange("url", HttpMethod.GET, new HttpEntity<>("value", new HttpHeaders()),
                new ParameterizedTypeReference<LogicResponse<AggPayPollingRespDTO>>() {})).thenReturn(logicResponseEntity);

        // Run the test
        final AggPayPollingRespDTO result = aggPayRpcServiceImplUnderTest.doWeChatPublicAccountPolling("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testDoWeChatPublicAccountPolling_RestTemplateThrowsRestClientException() {
        // Setup
        final AggPayPollingRespDTO expectedResult = new AggPayPollingRespDTO();
        expectedResult.setCode("code");
        expectedResult.setMsg("msg");
        expectedResult.setAmount(new BigDecimal("0.00"));
        expectedResult.setSubject("subject");
        expectedResult.setBody("body");

        when(mockAggPayConfig.getWechath5polling()).thenReturn("result");
        when(mockRemoteRestTemplate.exchange("url", HttpMethod.GET, new HttpEntity<>("value", new HttpHeaders()),
                new ParameterizedTypeReference<RestClientException>() {})).thenThrow(RestClientException.class);

        // Run the test
        final AggPayPollingRespDTO result = aggPayRpcServiceImplUnderTest.doWeChatPublicAccountPolling("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testDoWeChatPublicAccountPollingAsync() {
        // Setup
        when(mockAggPayConfig.getWechath5polling()).thenReturn("result");

        // Run the test
        final Mono<AggPayPollingRespDTO> result = aggPayRpcServiceImplUnderTest.doWeChatPublicAccountPollingAsync(
                "8e0b3d5d-ef02-4260-9f63-41541858b281");

        // Verify the results
    }

    @Test
    public void testDoAggPayReserve() {
        // Setup
        final AggPayReserveVO accountPayDTO = new AggPayReserveVO();
        accountPayDTO.setAppId("appId");
        accountPayDTO.setAttachData("attachData");
        accountPayDTO.setDeveloperId("developerId");
        accountPayDTO.setOrderGUID("be5bfaea-acbf-449d-b920-8e417bf19ad2");
        accountPayDTO.setPayGUID("5f74214b-796f-42ba-ac8c-78ffaad1ebc5");

        when(mockAggPayConfig.getReserve()).thenReturn("result");

        // Run the test
        final Mono<AggPayReserveResultDTO> result = aggPayRpcServiceImplUnderTest.doAggPayReserve(accountPayDTO);

        // Verify the results
    }
}
