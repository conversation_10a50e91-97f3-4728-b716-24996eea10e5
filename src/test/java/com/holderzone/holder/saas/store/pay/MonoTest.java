package com.holderzone.holder.saas.store.pay;

import reactor.core.publisher.Mono;

/**
 * {@link MonoTest}
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/3/3 下午2:05
 */
public class MonoTest {

    public static void main(String[] args) {
        Mono<Integer> integerMono = Mono.just(Integer.valueOf(6))
                .filter(a -> a > 5)
                .doOnNext(System.out::print)
                .flatMap(a -> Mono.just(5));
        System.out.printf(integerMono.block().toString());
    }


}
