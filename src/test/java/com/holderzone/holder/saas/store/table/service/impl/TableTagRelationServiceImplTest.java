package com.holderzone.holder.saas.store.table.service.impl;

import com.holderzone.holder.saas.store.table.service.RedisService;
import com.holderzone.holder.saas.store.table.service.TableTagService;
import com.holderzone.saas.store.dto.table.TableTagDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TableTagRelationServiceImplTest {

    @Mock
    private RedisService mockRedisService;
    @Mock
    private TableTagService mockTableTagService;

    private TableTagRelationServiceImpl tableTagRelationServiceImplUnderTest;

    @Before
    public void setUp() {
        tableTagRelationServiceImplUnderTest = new TableTagRelationServiceImpl(mockRedisService, mockTableTagService);
    }

    @Test
    public void testRemoveTagByTableIds1() {
        // Setup
        // Run the test
        tableTagRelationServiceImplUnderTest.removeTagByTableIds("tableids");

        // Verify the results
    }

    @Test
    public void testRemoveTagByTableIds2() {
        // Setup
        // Run the test
        tableTagRelationServiceImplUnderTest.removeTagByTableIds(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testRemoveTagByTagGuid() {
        assertThat(tableTagRelationServiceImplUnderTest.removeTagByTagGuid("tagGuid")).isFalse();
    }

    @Test
    public void testCreateTags1() {
        // Setup
        when(mockRedisService.batchGuid(0, "hst_table_tag_relation")).thenReturn(Arrays.asList("value"));

        // Run the test
        tableTagRelationServiceImplUnderTest.createTags(Arrays.asList("value"), "tableGuid");

        // Verify the results
    }

    @Test
    public void testCreateTags1_RedisServiceReturnsNoItems() {
        // Setup
        when(mockRedisService.batchGuid(0, "hst_table_tag_relation")).thenReturn(Collections.emptyList());

        // Run the test
        tableTagRelationServiceImplUnderTest.createTags(Arrays.asList("value"), "tableGuid");

        // Verify the results
    }

    @Test
    public void testCreateTags2() {
        // Setup
        when(mockRedisService.batchGuid(0, "hst_table_tag_relation")).thenReturn(Arrays.asList("value"));

        // Run the test
        tableTagRelationServiceImplUnderTest.createTags(Arrays.asList("value"), Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testCreateTags2_RedisServiceReturnsNoItems() {
        // Setup
        when(mockRedisService.batchGuid(0, "hst_table_tag_relation")).thenReturn(Collections.emptyList());

        // Run the test
        tableTagRelationServiceImplUnderTest.createTags(Arrays.asList("value"), Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testGetTagInfoByTableInfos() {
        // Setup
        final Map<String, List<TableTagDTO>> expectedResult = new HashMap<>();
        when(mockTableTagService.getTagWithGuidMap(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Run the test
        final Map<String, List<TableTagDTO>> result = tableTagRelationServiceImplUnderTest.getTagInfoByTableInfos(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
