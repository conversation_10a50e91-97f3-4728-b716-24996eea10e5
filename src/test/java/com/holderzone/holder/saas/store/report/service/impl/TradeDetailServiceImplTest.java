package com.holderzone.holder.saas.store.report.service.impl;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.report.mapper.TradeDetailMapper;
import com.holderzone.holder.saas.store.report.service.rpc.organization.OrganizationService;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.report.query.PaymentConstituteQueryDTO;
import com.holderzone.saas.store.dto.report.query.TradeDetailQueryDTO;
import com.holderzone.saas.store.dto.report.resp.GrouponTradeDetailRespDTO;
import com.holderzone.saas.store.dto.report.resp.PaymentConstituteDTO;
import com.holderzone.saas.store.dto.report.resp.PaymentConstituteRespDTO;
import com.holderzone.saas.store.dto.report.resp.TakeawayTradeDetailRespDTO;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TradeDetailServiceImplTest {

    public static final String RESULT = "result";
    public static final String VALUE = "value";
    public static final String STORE_NAME = "storeName";
    public static final String BRAND_GUID = "brandGuid";
    @Mock
    private TradeDetailMapper mockTradeDetailMapper;
    @Mock
    private OssClient mockOssClient;
    @Mock
    private OrganizationService mockOrganizationService;

    @InjectMocks
    private TradeDetailServiceImpl tradeDetailServiceImplUnderTest;

    @Rule
    public ExpectedException expectedException = ExpectedException.none();

    @Before
    public void setUp() {
        UserContext userContext = new UserContext();
        userContext.setOperSubjectGuid("2209191214425540006");
        userContext.setEnterpriseGuid("4895");
        UserContextUtils.put(userContext);

        tradeDetailServiceImplUnderTest = new TradeDetailServiceImpl(mockTradeDetailMapper, mockOssClient,
                mockOrganizationService);
    }

    @Test
    public void testPageGroupon() {
        // Setup
        final TradeDetailQueryDTO query = getTradeDetailQueryDTO();

        // Configure TradeDetailMapper.countGroupon(...).
        final TradeDetailQueryDTO query1 = getTradeDetailQueryDTO();
        when(mockTradeDetailMapper.countGroupon(query1)).thenReturn(1L);

        // Configure TradeDetailMapper.pageGroupon(...).
        final GrouponTradeDetailRespDTO grouponTradeDetailRespDTO = new GrouponTradeDetailRespDTO();
        grouponTradeDetailRespDTO.setSource(0);
        grouponTradeDetailRespDTO.setCouponCode("couponCode");
        grouponTradeDetailRespDTO.setCouponUseTime("couponUseTime");
        grouponTradeDetailRespDTO.setStoreName(STORE_NAME);
        grouponTradeDetailRespDTO.setThirdStoreGuid("thirdStoreGuid");
        List<GrouponTradeDetailRespDTO> grouponTradeDetailRespDTOS = Collections.singletonList(grouponTradeDetailRespDTO);

        // Run the test
        final Page<GrouponTradeDetailRespDTO> result = tradeDetailServiceImplUnderTest.pageGroupon(query);

        // Verify the results
        assertEquals(1, result.getTotalCount());
    }

    @Test
    public void testPageGroupon_TradeDetailMapperPageGrouponReturnsNoItems() {
        // Setup
        final TradeDetailQueryDTO query = getTradeDetailQueryDTO();

        // Configure TradeDetailMapper.countGroupon(...).
        final TradeDetailQueryDTO query1 = getTradeDetailQueryDTO();
        when(mockTradeDetailMapper.countGroupon(query1)).thenReturn(0L);

        // Configure TradeDetailMapper.pageGroupon(...).

        // Run the test
        final Page<GrouponTradeDetailRespDTO> result = tradeDetailServiceImplUnderTest.pageGroupon(query);

        // Verify the results
        assertEquals(0, result.getTotalCount());
    }

    @Test
    public void testPageTakeaway() {
        // Setup
        final TradeDetailQueryDTO query = getTradeDetailQueryDTO();

        // Configure TradeDetailMapper.countTakeaway(...).
        final TradeDetailQueryDTO query1 = getTradeDetailQueryDTO();
        when(mockTradeDetailMapper.countTakeaway(query1)).thenReturn(1L);

        // Configure TradeDetailMapper.pageTakeaway(...).
        final List<TakeawayTradeDetailRespDTO> takeawayTradeDetailRespDTOS = getTakeawayTradeDetailRespDTOS();
        final TradeDetailQueryDTO query2 = getTradeDetailQueryDTO();
        when(mockTradeDetailMapper.pageTakeaway(query2)).thenReturn(takeawayTradeDetailRespDTOS);

        // Run the test
        final Page<TakeawayTradeDetailRespDTO> result = tradeDetailServiceImplUnderTest.pageTakeaway(query);

        // Verify the results
        assertEquals(1, result.getTotalCount());
    }

    @NotNull
    private static List<TakeawayTradeDetailRespDTO> getTakeawayTradeDetailRespDTOS() {
        final TakeawayTradeDetailRespDTO takeawayTradeDetailRespDTO = new TakeawayTradeDetailRespDTO();
        takeawayTradeDetailRespDTO.setStoreGuid("storeGuid");
        takeawayTradeDetailRespDTO.setStoreName(STORE_NAME);
        takeawayTradeDetailRespDTO.setOrderCreateDate("orderCreateDate");
        takeawayTradeDetailRespDTO.setTakeoutOrderType(0);
        takeawayTradeDetailRespDTO.setStatus(0);
        return Collections.singletonList(takeawayTradeDetailRespDTO);
    }

    @Test
    public void testPageTakeaway_TradeDetailMapperPageTakeawayReturnsNoItems() {
        // Setup
        final TradeDetailQueryDTO query = getTradeDetailQueryDTO();

        // Configure TradeDetailMapper.countTakeaway(...).
        final TradeDetailQueryDTO query1 = getTradeDetailQueryDTO();
        when(mockTradeDetailMapper.countTakeaway(query1)).thenReturn(0L);

        // Run the test
        final Page<TakeawayTradeDetailRespDTO> result = tradeDetailServiceImplUnderTest.pageTakeaway(query);

        // Verify the results
        assertEquals(0, result.getTotalCount());
    }

    @NotNull
    private static TradeDetailQueryDTO getTradeDetailQueryDTO() {
        final TradeDetailQueryDTO query = new TradeDetailQueryDTO();
        query.setCurrentPage(0L);
        query.setPageSize(0L);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setBrandGuid(BRAND_GUID);
        return query;
    }

    @Test
    public void testExportGroupon() {
        // Setup
        final TradeDetailQueryDTO query = getTradeDetailQueryDTO();

        // Configure TradeDetailMapper.countTakeaway(...).

        // Configure TradeDetailMapper.listGroupon(...).
        final GrouponTradeDetailRespDTO grouponTradeDetailRespDTO = new GrouponTradeDetailRespDTO();
        grouponTradeDetailRespDTO.setSource(0);
        grouponTradeDetailRespDTO.setCouponCode("couponCode");
        grouponTradeDetailRespDTO.setCouponUseTime("couponUseTime");
        grouponTradeDetailRespDTO.setStoreName(STORE_NAME);
        grouponTradeDetailRespDTO.setThirdStoreGuid("thirdStoreGuid");
        final List<GrouponTradeDetailRespDTO> grouponTradeDetailRespDTOS = Arrays.asList(grouponTradeDetailRespDTO);
        final TradeDetailQueryDTO query2 = getTradeDetailQueryDTO();
        when(mockTradeDetailMapper.listGroupon(query2)).thenReturn(grouponTradeDetailRespDTOS);

        // Run the test
        final String result = tradeDetailServiceImplUnderTest.exportGroupon(query);

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testExportGroupon_TradeDetailMapperListGrouponReturnsNoItems() {
        // Setup
        final TradeDetailQueryDTO query = getTradeDetailQueryDTO();

        // Configure TradeDetailMapper.listGroupon(...).
        final TradeDetailQueryDTO query2 = getTradeDetailQueryDTO();
        when(mockTradeDetailMapper.listGroupon(query2)).thenReturn(Collections.emptyList());
        when(mockOssClient.upload(anyString(), any(byte[].class))).thenReturn(RESULT);

        // Run the test
        final String result = tradeDetailServiceImplUnderTest.exportGroupon(query);

        // Verify the results
        assertThat(result).isEqualTo(RESULT);
    }

    @Test
    public void testExportTakeaway() {
        // Setup
        final TradeDetailQueryDTO query = getTradeDetailQueryDTO();

        // Configure TradeDetailMapper.countTakeaway(...).
        final TradeDetailQueryDTO query1 = getTradeDetailQueryDTO();
        when(mockTradeDetailMapper.countTakeaway(query1)).thenReturn(0L);

        // Configure TradeDetailMapper.listTakeaway(...).
        final List<TakeawayTradeDetailRespDTO> takeawayTradeDetailRespDTOS = getTakeawayTradeDetailRespDTOS();
        final TradeDetailQueryDTO query2 = getTradeDetailQueryDTO();
        when(mockTradeDetailMapper.listTakeaway(query2)).thenReturn(takeawayTradeDetailRespDTOS);
        when(mockOssClient.upload(anyString(), any(byte[].class))).thenReturn(RESULT);

        // Run the test
        final String result = tradeDetailServiceImplUnderTest.exportTakeaway(query);

        // Verify the results
        assertThat(result).isEqualTo(RESULT);
    }

    @Test
    public void testExportTakeaway_TradeDetailMapperListTakeawayReturnsNoItems() {
        // Setup
        final TradeDetailQueryDTO query = getTradeDetailQueryDTO();

        // Configure TradeDetailMapper.countTakeaway(...).
        final TradeDetailQueryDTO query1 = getTradeDetailQueryDTO();
        when(mockTradeDetailMapper.countTakeaway(query1)).thenReturn(20001L);

        // 执行期望抛出异常
        expectedException.expect(BusinessException.class);
        // Run the test
        final String result = tradeDetailServiceImplUnderTest.exportTakeaway(query);
    }

    @Test
    public void testPaymentConstitute() {
        // Setup
        final PaymentConstituteQueryDTO query = getPaymentConstituteQueryDTO();

        // Configure TradeDetailMapper.summarizingPaymentConstitute(...).
        final List<PaymentConstituteDTO> paymentConstituteDTOS = getPaymentConstituteDTOS();
        final PaymentConstituteQueryDTO query1 = getPaymentConstituteQueryDTO();
        when(mockTradeDetailMapper.summarizingPaymentConstitute(query1)).thenReturn(paymentConstituteDTOS);

        // Configure TradeDetailMapper.singlePaymentConstitute(...).
        final List<PaymentConstituteDTO> paymentConstituteDTOS1 = getPaymentConstituteDTOS();

        // Run the test
        final Page<PaymentConstituteRespDTO> result = tradeDetailServiceImplUnderTest.paymentConstitute(query);

        // Verify the results
        assertEquals(1, result.getTotalCount());

        query.setShowType(2);
        // Run the test
        final Page<PaymentConstituteRespDTO> resultSingle = tradeDetailServiceImplUnderTest.paymentConstitute(query);

        // Verify the results
        assertEquals(0, resultSingle.getTotalCount());
    }

    @Test
    public void testPaymentConstitute_SingleDaySinglePaymentConstitutePage() {
        final PaymentConstituteQueryDTO query = getSingleDayConstituteQueryDTO();
        List<StoreDTO> singleDayStoreDTOList = getSingleDayStoreDTOList();
        when(mockOrganizationService.queryStoreAndBrandByIdList(anyList())).thenReturn(singleDayStoreDTOList);
        List<PaymentConstituteDTO> singleDayPaymentConstituteDTOS = getSingleDayPaymentConstituteDTOS();
        when(mockTradeDetailMapper.singleDaySinglePaymentConstitute(any())).thenReturn(singleDayPaymentConstituteDTOS);
        Page<PaymentConstituteRespDTO> paymentConstituteRespDTOPage = tradeDetailServiceImplUnderTest.paymentConstitute(query);
        assertNotNull(paymentConstituteRespDTOPage);
        assertEquals(4, paymentConstituteRespDTOPage.getTotalCount());
    }

    @NotNull
    private static  PaymentConstituteQueryDTO getSingleDayConstituteQueryDTO() {
        final PaymentConstituteQueryDTO query = new PaymentConstituteQueryDTO();
        query.setCurrentPage(1L);
        query.setPageSize(5L);
        query.setEnterpriseGuid("enterpriseGuid");
        query.setStartTime(LocalDate.of(2024, 5, 13));
        query.setEndTime(LocalDate.of(2024, 5, 14));
        query.setBrandGuid(BRAND_GUID);
        query.setShowType(3);
        query.setStoreGuidList(Arrays.asList("4896", "4897"));
        return query;
    }

    @NotNull
    private static List<StoreDTO> getSingleDayStoreDTOList() {
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("4896");
        storeDTO1.setCode("code4896");
        storeDTO1.setName("name4896");
        storeDTO1.setBelongBrandGuid("belongBrandGuid4896");
        storeDTO1.setBelongBrandName("belongBrandName4896");

        final StoreDTO storeDTO2 = new StoreDTO();
        storeDTO2.setGuid("4897");
        storeDTO2.setCode("code4897");
        storeDTO2.setName("name4897");
        storeDTO2.setBelongBrandGuid("belongBrandGuid4897");
        storeDTO2.setBelongBrandName("belongBrandName4897");
        return Arrays.asList(storeDTO1, storeDTO2);
    }

    @NotNull
    private static List<PaymentConstituteDTO> getSingleDayPaymentConstituteDTOS() {
        final PaymentConstituteDTO paymentConstituteDTO1 = new PaymentConstituteDTO();
        paymentConstituteDTO1.setBusinessDate(LocalDate.of(2024, 5, 13));
        paymentConstituteDTO1.setStoreGuid("4896");
        paymentConstituteDTO1.setPaymentType(4);
        paymentConstituteDTO1.setPayMethod("会员余额支付");
        paymentConstituteDTO1.setSalesRevenue(new BigDecimal("163.15"));
        paymentConstituteDTO1.setMemberRecharge(new BigDecimal("0.00"));
        paymentConstituteDTO1.setDeposit(new BigDecimal("0.00"));
        paymentConstituteDTO1.setServiceCharge(new BigDecimal("0.00"));
        paymentConstituteDTO1.setActualIncome(new BigDecimal("163.15"));

        final PaymentConstituteDTO paymentConstituteDTO2 = new PaymentConstituteDTO();
        paymentConstituteDTO2.setBusinessDate(LocalDate.of(2024, 5, 13));
        paymentConstituteDTO2.setStoreGuid("4896");
        paymentConstituteDTO2.setPaymentType(2);
        paymentConstituteDTO2.setPayMethod("聚合支付");
        paymentConstituteDTO2.setSalesRevenue(new BigDecimal("0.01"));
        paymentConstituteDTO2.setMemberRecharge(new BigDecimal("0.00"));
        paymentConstituteDTO2.setDeposit(new BigDecimal("0.00"));
        paymentConstituteDTO2.setServiceCharge(new BigDecimal("0.00"));
        paymentConstituteDTO2.setActualIncome(new BigDecimal("0.01"));

        final PaymentConstituteDTO paymentConstituteDTO3 = new PaymentConstituteDTO();
        paymentConstituteDTO3.setBusinessDate(LocalDate.of(2024, 5, 14));
        paymentConstituteDTO3.setStoreGuid("4896");
        paymentConstituteDTO3.setPaymentType(4);
        paymentConstituteDTO3.setPayMethod("会员余额支付");
        paymentConstituteDTO3.setSalesRevenue(new BigDecimal("1.5"));
        paymentConstituteDTO3.setMemberRecharge(new BigDecimal("0.00"));
        paymentConstituteDTO3.setDeposit(new BigDecimal("0.00"));
        paymentConstituteDTO3.setServiceCharge(new BigDecimal("0.00"));
        paymentConstituteDTO3.setActualIncome(new BigDecimal("1.5"));

        final PaymentConstituteDTO paymentConstituteDTO4 = new PaymentConstituteDTO();
        paymentConstituteDTO4.setBusinessDate(LocalDate.of(2024, 5, 14));
        paymentConstituteDTO4.setStoreGuid("4897");
        paymentConstituteDTO4.setPaymentType(4);
        paymentConstituteDTO4.setPayMethod("会员余额支付");
        paymentConstituteDTO4.setSalesRevenue(new BigDecimal("10.5"));
        paymentConstituteDTO4.setMemberRecharge(new BigDecimal("0.00"));
        paymentConstituteDTO4.setDeposit(new BigDecimal("0.00"));
        paymentConstituteDTO4.setServiceCharge(new BigDecimal("0.00"));
        paymentConstituteDTO4.setActualIncome(new BigDecimal("10.5"));
        return Arrays.asList(paymentConstituteDTO1, paymentConstituteDTO2, paymentConstituteDTO3, paymentConstituteDTO4);
    }

    @NotNull
    private static List<PaymentConstituteDTO> getPaymentConstituteDTOS() {
        final PaymentConstituteDTO paymentConstituteDTO = new PaymentConstituteDTO();
        paymentConstituteDTO.setBusinessDate(LocalDate.of(2020, 1, 1));
        paymentConstituteDTO.setStoreGuid("storeGuid");
        paymentConstituteDTO.setPaymentType(0);
        paymentConstituteDTO.setPayMethod("");
        paymentConstituteDTO.setSalesRevenue(new BigDecimal("0.00"));
        paymentConstituteDTO.setMemberRecharge(new BigDecimal("0.00"));
        paymentConstituteDTO.setDeposit(new BigDecimal("0.00"));
        return Collections.singletonList(paymentConstituteDTO);
    }

    @NotNull
    private static PaymentConstituteQueryDTO getPaymentConstituteQueryDTO() {
        final PaymentConstituteQueryDTO query = new PaymentConstituteQueryDTO();
        query.setCurrentPage(1L);
        query.setPageSize(20L);
        query.setEnterpriseGuid("enterpriseGuid");
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setBrandGuid(BRAND_GUID);
        query.setShowType(1);
        query.setStoreGuidList(Collections.singletonList(VALUE));
        return query;
    }

    @Test
    public void testPaymentConstitute_TradeDetailMapperSummarizingPaymentConstituteReturnsNoItems() {
        // Setup
        final PaymentConstituteQueryDTO query = getPaymentConstituteQueryDTO();

        // Configure TradeDetailMapper.summarizingPaymentConstitute(...).
        final PaymentConstituteQueryDTO query1 = getPaymentConstituteQueryDTO();
        when(mockTradeDetailMapper.summarizingPaymentConstitute(query1)).thenReturn(Collections.emptyList());

        // Run the test
        final Page<PaymentConstituteRespDTO> result = tradeDetailServiceImplUnderTest.paymentConstitute(query);

        // Verify the results
        assertEquals(1, result.getTotalCount());
    }

    @Test
    public void testPaymentConstitute_OrganizationServiceReturnsNoItems() {
        // Setup
        final PaymentConstituteQueryDTO query = getPaymentConstituteQueryDTO();

        when(mockOrganizationService.queryStoreAndBrandByIdList(Collections.singletonList(VALUE)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Page<PaymentConstituteRespDTO> result = tradeDetailServiceImplUnderTest.paymentConstitute(query);

        // Verify the results
        assertEquals(1, result.getTotalCount());

        query.setShowType(3);
        final Page<PaymentConstituteRespDTO> resultSingleDay = tradeDetailServiceImplUnderTest.paymentConstitute(query);
        assertEquals(0, resultSingleDay.getTotalCount());
    }

    @Test
    public void testPaymentConstitute_TradeDetailMapperSinglePaymentConstituteReturnsNoItems() {
        // Setup
        final PaymentConstituteQueryDTO query = getPaymentConstituteQueryDTO();

        // Run the test
        final Page<PaymentConstituteRespDTO> result = tradeDetailServiceImplUnderTest.paymentConstitute(query);

        // Verify the results
        assertEquals(1, result.getTotalCount());
    }

    @Test
    public void testPaymentConstitute_TradeDetailMapperSingleDaySinglePaymentConstituteReturnsNoItems() {
        final PaymentConstituteQueryDTO query = getSingleDayConstituteQueryDTO();
        query.setShowType(3);
        // Configure OrganizationService.queryStoreAndBrandByIdList(...).
        final List<StoreDTO> storeDTOS = getStoreDTOList();
        when(mockOrganizationService.queryStoreAndBrandByIdList(anyList())).thenReturn(storeDTOS);
        List<PaymentConstituteDTO> singleDayPaymentConstituteDTOS = getSingleDayPaymentConstituteDTOS();
        when(mockTradeDetailMapper.singleDaySinglePaymentConstitute(query)).thenReturn(singleDayPaymentConstituteDTOS);
        final Page<PaymentConstituteRespDTO> resultSingleDay = tradeDetailServiceImplUnderTest.paymentConstitute(query);
        assertNotNull(resultSingleDay);
    }

    @Test
    public void testExportPaymentConstitute() {
        // Setup
        final PaymentConstituteQueryDTO query = getPaymentConstituteQueryDTO();

        // Configure TradeDetailMapper.summarizingPaymentConstitute(...).
        final List<PaymentConstituteDTO> paymentConstituteDTOS = getPaymentConstituteDTOS();
        final PaymentConstituteQueryDTO query1 = getPaymentConstituteQueryDTO();
        when(mockTradeDetailMapper.summarizingPaymentConstitute(query1)).thenReturn(paymentConstituteDTOS);

        // Configure TradeDetailMapper.singlePaymentConstitute(...).
        final List<PaymentConstituteDTO> paymentConstituteDTOS1 = getPaymentConstituteDTOS();

        when(mockOssClient.upload(anyString(), any(byte[].class))).thenReturn(RESULT);

        // Run the test
        final String result = tradeDetailServiceImplUnderTest.exportPaymentConstitute(query);

        // Verify the results
        assertThat(result).isEqualTo(RESULT);

        query.setShowType(2);
        // Run the test
        final String resultSingle = tradeDetailServiceImplUnderTest.exportPaymentConstitute(query);

        // Verify the results
        assertThat(resultSingle).isEqualTo(RESULT);

        query.setShowType(3);
        // Run the test
        final String resultSingleDay = tradeDetailServiceImplUnderTest.exportPaymentConstitute(query);

        // Verify the results
        assertThat(resultSingleDay).isEqualTo(RESULT);
    }

    @Test
    public void testExportPaymentConstitute_TradeDetailMapperSummarizingPaymentConstituteReturnsNoItems() {
        // Setup
        final PaymentConstituteQueryDTO query = getPaymentConstituteQueryDTO();

        // Configure TradeDetailMapper.summarizingPaymentConstitute(...).
        final PaymentConstituteQueryDTO query1 = getPaymentConstituteQueryDTO();
        when(mockTradeDetailMapper.summarizingPaymentConstitute(query1)).thenReturn(Collections.emptyList());
        when(mockOssClient.upload(anyString(), any(byte[].class))).thenReturn(RESULT);
        // Run the test
        final String result = tradeDetailServiceImplUnderTest.exportPaymentConstitute(query);

        // Verify the results
        assertThat(result).isEqualTo(RESULT);
    }

    @Test
    public void testExportPaymentConstitute_OrganizationServiceReturnsNoItems() {
        // Setup
        final PaymentConstituteQueryDTO query = getPaymentConstituteQueryDTO();

        when(mockOssClient.upload(anyString(), any(byte[].class))).thenReturn(RESULT);

        // Run the test
        final String result = tradeDetailServiceImplUnderTest.exportPaymentConstitute(query);

        // Verify the results
        assertThat(result).isEqualTo(RESULT);
    }

    @Test
    public void testExportPaymentConstitute_TradeDetailMapperSinglePaymentConstituteReturnsNoItems() {
        // Setup
        final PaymentConstituteQueryDTO query = getPaymentConstituteQueryDTO();

        // Configure OrganizationService.queryStoreAndBrandByIdList(...).

        // Configure TradeDetailMapper.singlePaymentConstitute(...).
        when(mockOssClient.upload(anyString(), any(byte[].class))).thenReturn(RESULT);

        // Run the test
        final String result = tradeDetailServiceImplUnderTest.exportPaymentConstitute(query);

        // Verify the results
        assertThat(result).isEqualTo(RESULT);
    }

    @NotNull
    private static List<StoreDTO> getStoreDTOList() {
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("f4b97042-428a-4efa-aa67-263a3c8d6dd8");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        return Collections.singletonList(storeDTO);
    }
}
