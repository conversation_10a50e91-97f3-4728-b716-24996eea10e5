package com.holderzone.holder.saas.store.config;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.config.utils.SpringContextUtils;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReqDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReqQueryDTO;
import com.holderzone.saas.store.enums.common.ConfigEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.net.URLEncoder;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
public class ConfigTests {
    @Autowired
    private WebApplicationContext wac;
    private MockMvc mockMvc;

    @Autowired
    private ApplicationContext applicationContext;

    @Before
    public void setup() {
        SpringContextUtils.getInstance().setCfgContext((ConfigurableApplicationContext) applicationContext);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();   //构造MockMvc
    }

    @Test
    public void configSave() throws Exception {
        ConfigReqDTO configReqDTO = ConfigReqDTO.builder().storeGuid("6506453252643487745")
                .dictValue("08:00")
                .dicCode(ConfigEnum.ESTIMATE_RECOVERY_TIME.getCode())
                .dicName(ConfigEnum.ESTIMATE_RECOVERY_TIME.getDesc())
                .guid("6534636857789288449").build();
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userContext), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/config/save_config").header("userInfo", encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(configReqDTO)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }

    @Test
    public void getConfig() throws Exception {
        ConfigReqQueryDTO configReqQueryDTO = new ConfigReqQueryDTO();
        configReqQueryDTO.setStoreGuid("6506453252643487745");
        configReqQueryDTO.setDicCode(ConfigEnum.ESTIMATE_RECOVERY_TIME.getCode());
        /*configReqDTO.setGuid("6534257531524372481");*/
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userContext), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/config/get_config_by_code").header("userInfo", encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(configReqQueryDTO)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }


    @Test
    public void getConfigs() throws Exception {
        ConfigReqDTO configReqDTO = new ConfigReqDTO();
        configReqDTO.setDicCode(ConfigEnum.ESTIMATE_RECOVERY_TIME.getCode());
        configReqDTO.setDictValue("13:00");
        configReqDTO.setIsEnable(0);
        /*configReqDTO.setGuid("6534257531524372481");*/
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid("6506431195651982337");
        String encode = URLEncoder.encode(JacksonUtils.writeValueAsString(userContext), "utf-8");
        MvcResult mvcResult = mockMvc.perform(post("/config/get_config").header("userInfo", encode).accept(MediaType.APPLICATION_JSON_VALUE)
                .contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(configReqDTO)))
                .andExpect(status().is4xxClientError()).andDo(print()).andReturn();
        String contentAsString = mvcResult.getResponse().getContentAsString();
        System.out.println("sout:" + contentAsString);
    }


}
