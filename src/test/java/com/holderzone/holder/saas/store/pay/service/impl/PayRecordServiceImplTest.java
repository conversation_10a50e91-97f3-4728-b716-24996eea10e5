package com.holderzone.holder.saas.store.pay.service.impl;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;

public class PayRecordServiceImplTest {

    private PayRecordServiceImpl payRecordServiceImplUnderTest;

    @Before
    public void setUp() {
        payRecordServiceImplUnderTest = new PayRecordServiceImpl();
    }

    @Test
    public void testGetStatisticsTotalAmount() {
        // Setup
        final BasePageDTO basePageDTO = new BasePageDTO();
        basePageDTO.setCurrentPage(0);
        basePageDTO.setPageSize(0);
        basePageDTO.setMaxId(0L);

        // Run the test
        final BigDecimal result = payRecordServiceImplUnderTest.getStatisticsTotalAmount(basePageDTO);

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
    }
}
