package com.holderzone.holder.saas.store.report.helper;

import static org.junit.Assert.assertEquals;

import org.junit.Test;

public class ExceptionHelperDiffblueTest {
    /**
     * Method under test:
     * {@link ExceptionHelper#throwException(Exception, String, String)}
     */
    @Test
    public void testThrowException() {
        assertEquals("foo", ExceptionHelper.throwException(new Exception("foo"), "Table", "Query"));
        assertEquals("请联系管理员开通", ExceptionHelper.throwException(new Exception("does not exist"), "Table", "Query"));
        assertEquals("foo", ExceptionHelper.throwException(new Exception("foo"), "foo", "Query"));
    }

    /**
     * Method under test:
     * {@link ExceptionHelper#throwException(Exception, String, String)}
     */
    @Test
    public void testThrowException2() {
        Exception e = new Exception("does not exist");
        e.addSuppressed(new Throwable());
        assertEquals("请联系管理员开通", ExceptionHelper.throwException(e, "Table", "Query"));
    }
}
