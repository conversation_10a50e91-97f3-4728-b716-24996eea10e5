package com.holderzone.holder.saas.store.table.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.store.table.client.BizMsgRpcClient;
import com.holderzone.holder.saas.store.table.client.OrganizationClientService;
import com.holderzone.holder.saas.store.table.client.TradeRpcClient;
import com.holderzone.holder.saas.store.table.domain.TableBasicDO;
import com.holderzone.holder.saas.store.table.domain.TableOrderDO;
import com.holderzone.holder.saas.store.table.mapper.TableBasicMapper;
import com.holderzone.holder.saas.store.table.mapper.TableOrderMapper;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.organization.PadOrderTypeReqDTO;
import com.holderzone.saas.store.dto.table.TableCombineDTO;
import com.holderzone.saas.store.dto.table.TableInfoDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.TurnTableDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BizMsgServiceImplTest {

    @Mock
    private BizMsgRpcClient mockBizMsgRpcClient;
    @Mock
    private OrganizationClientService mockOrganizationClientService;
    @Mock
    private TableBasicMapper mockTableBasicMapper;
    @Mock
    private TableOrderMapper mockTableOrderMapper;
    @Mock
    private TradeRpcClient mockTradeRpcClient;

    private BizMsgServiceImpl bizMsgServiceImplUnderTest;

    @Before
    public void setUp() {
        bizMsgServiceImplUnderTest = new BizMsgServiceImpl(mockBizMsgRpcClient, mockOrganizationClientService,
                mockTableBasicMapper, mockTableOrderMapper, mockTradeRpcClient);
    }

    @Test
    public void testSendMsg1() {
        // Setup
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setStoreGuid("storeGuid");
        baseDTO.setStoreName("storeName");

        // Configure BizMsgRpcClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setSubject("name");
        businessMessageDTO.setContent("一体机拆台消息");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        businessMessageDTO.setPlatform("2");
        businessMessageDTO.setStoreGuid("storeGuid");
        businessMessageDTO.setStoreName("storeName");
        businessMessageDTO.setMessageTypeStr("messageTypeStr");
        when(mockBizMsgRpcClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final String result = bizMsgServiceImplUnderTest.sendMsg(baseDTO, "content");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testSendMsg2() {
        // Setup
        final BaseDTO openTableDTO = new BaseDTO();
        openTableDTO.setDeviceType(0);
        openTableDTO.setDeviceId("deviceId");
        openTableDTO.setEnterpriseGuid("enterpriseGuid");
        openTableDTO.setStoreGuid("storeGuid");
        openTableDTO.setStoreName("storeName");

        // Configure BizMsgRpcClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setSubject("name");
        businessMessageDTO.setContent("一体机拆台消息");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        businessMessageDTO.setPlatform("2");
        businessMessageDTO.setStoreGuid("storeGuid");
        businessMessageDTO.setStoreName("storeName");
        businessMessageDTO.setMessageTypeStr("messageTypeStr");
        when(mockBizMsgRpcClient.sendMsg(businessMessageDTO)).thenReturn("result");

        // Run the test
        final String result = bizMsgServiceImplUnderTest.sendMsg(openTableDTO, Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testSendMsg3() {
        // Setup
        final TableCombineDTO tableCombineDTO = new TableCombineDTO();
        tableCombineDTO.setStoreGuid("storeGuid");
        tableCombineDTO.setStoreName("storeName");
        tableCombineDTO.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO.setCombineTimes(0);
        tableCombineDTO.setMainTableGuid("tableGuid");

        // Configure TableBasicMapper.selectOne(...).
        final TableBasicDO tableBasicDO = new TableBasicDO();
        tableBasicDO.setGuid("88229112-5e37-494c-803e-6c82dc9db9b9");
        tableBasicDO.setAreaGuid("areaGuid");
        tableBasicDO.setAreaName("areaName");
        tableBasicDO.setTableCode("tableCode");
        tableBasicDO.setSeats(0);
        when(mockTableBasicMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableBasicDO);

        // Configure OrganizationClientService.listDeviceByStoreTable(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setTableGuid("tableGuid");
        final List<StoreDeviceDTO> storeDeviceDTOS = Arrays.asList(storeDeviceDTO);
        when(mockOrganizationClientService.listDeviceByStoreTable(any(PadOrderTypeReqDTO.class)))
                .thenReturn(storeDeviceDTOS);

        // Configure OrganizationClientService.queryDeviceByStoreTable(...).
        final StoreDeviceDTO storeDeviceDTO1 = new StoreDeviceDTO();
        storeDeviceDTO1.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO1.setStoreNo("storeNo");
        storeDeviceDTO1.setStoreGuid("storeGuid");
        storeDeviceDTO1.setDeviceNo("deviceNo");
        storeDeviceDTO1.setTableGuid("tableGuid");
        when(mockOrganizationClientService.queryDeviceByStoreTable(any(PadOrderTypeReqDTO.class)))
                .thenReturn(storeDeviceDTO1);

        // Run the test
        bizMsgServiceImplUnderTest.sendMsg(tableCombineDTO);

        // Verify the results
        // Confirm BizMsgRpcClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setSubject("name");
        businessMessageDTO.setContent("一体机拆台消息");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        businessMessageDTO.setPlatform("2");
        businessMessageDTO.setStoreGuid("storeGuid");
        businessMessageDTO.setStoreName("storeName");
        businessMessageDTO.setMessageTypeStr("messageTypeStr");
        verify(mockBizMsgRpcClient).sendMsg(businessMessageDTO);
    }

    @Test
    public void testSendMsg3_OrganizationClientServiceListDeviceByStoreTableReturnsNoItems() {
        // Setup
        final TableCombineDTO tableCombineDTO = new TableCombineDTO();
        tableCombineDTO.setStoreGuid("storeGuid");
        tableCombineDTO.setStoreName("storeName");
        tableCombineDTO.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO.setCombineTimes(0);
        tableCombineDTO.setMainTableGuid("tableGuid");

        // Configure TableBasicMapper.selectOne(...).
        final TableBasicDO tableBasicDO = new TableBasicDO();
        tableBasicDO.setGuid("88229112-5e37-494c-803e-6c82dc9db9b9");
        tableBasicDO.setAreaGuid("areaGuid");
        tableBasicDO.setAreaName("areaName");
        tableBasicDO.setTableCode("tableCode");
        tableBasicDO.setSeats(0);
        when(mockTableBasicMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableBasicDO);

        when(mockOrganizationClientService.listDeviceByStoreTable(any(PadOrderTypeReqDTO.class)))
                .thenReturn(Collections.emptyList());

        // Configure OrganizationClientService.queryDeviceByStoreTable(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setTableGuid("tableGuid");
        when(mockOrganizationClientService.queryDeviceByStoreTable(any(PadOrderTypeReqDTO.class)))
                .thenReturn(storeDeviceDTO);

        // Run the test
        bizMsgServiceImplUnderTest.sendMsg(tableCombineDTO);

        // Verify the results
        // Confirm BizMsgRpcClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setSubject("name");
        businessMessageDTO.setContent("一体机拆台消息");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        businessMessageDTO.setPlatform("2");
        businessMessageDTO.setStoreGuid("storeGuid");
        businessMessageDTO.setStoreName("storeName");
        businessMessageDTO.setMessageTypeStr("messageTypeStr");
        verify(mockBizMsgRpcClient).sendMsg(businessMessageDTO);
    }

    @Test
    public void testSendMsg4() {
        // Setup
        final TurnTableDTO turnTableDTO = new TurnTableDTO();
        turnTableDTO.setStoreGuid("storeGuid");
        turnTableDTO.setStoreName("storeName");
        turnTableDTO.setOriginTableGuid("originTableGuid");
        turnTableDTO.setOriginTableCode("originTableCode");
        turnTableDTO.setNewTableGuid("newTableGuid");

        // Configure TableOrderMapper.selectOne(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setGuid("9268bc97-5eb9-4650-9a08-496314cff50d");
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("orderGuid");
        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDO);

        // Configure OrganizationClientService.listDeviceByStoreTable(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setTableGuid("tableGuid");
        final List<StoreDeviceDTO> storeDeviceDTOS = Arrays.asList(storeDeviceDTO);
        when(mockOrganizationClientService.listDeviceByStoreTable(any(PadOrderTypeReqDTO.class)))
                .thenReturn(storeDeviceDTOS);

        // Configure TableBasicMapper.selectOne(...).
        final TableBasicDO tableBasicDO = new TableBasicDO();
        tableBasicDO.setGuid("88229112-5e37-494c-803e-6c82dc9db9b9");
        tableBasicDO.setAreaGuid("areaGuid");
        tableBasicDO.setAreaName("areaName");
        tableBasicDO.setTableCode("tableCode");
        tableBasicDO.setSeats(0);
        when(mockTableBasicMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableBasicDO);

        // Configure TradeRpcClient.findByOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setGuid("26d8feb5-b1c7-4150-b261-151a57655758");
        orderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDTO.setGuestCount(0);
        orderDTO.setDiningTableGuid("diningTableGuid");
        when(mockTradeRpcClient.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        // Run the test
        bizMsgServiceImplUnderTest.sendMsg(turnTableDTO);

        // Verify the results
        // Confirm BizMsgRpcClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setSubject("name");
        businessMessageDTO.setContent("一体机拆台消息");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        businessMessageDTO.setPlatform("2");
        businessMessageDTO.setStoreGuid("storeGuid");
        businessMessageDTO.setStoreName("storeName");
        businessMessageDTO.setMessageTypeStr("messageTypeStr");
        verify(mockBizMsgRpcClient).sendMsg(businessMessageDTO);
    }

    @Test
    public void testSendMsg4_TableOrderMapperReturnsNull() {
        // Setup
        final TurnTableDTO turnTableDTO = new TurnTableDTO();
        turnTableDTO.setStoreGuid("storeGuid");
        turnTableDTO.setStoreName("storeName");
        turnTableDTO.setOriginTableGuid("originTableGuid");
        turnTableDTO.setOriginTableCode("originTableCode");
        turnTableDTO.setNewTableGuid("newTableGuid");

        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure OrganizationClientService.listDeviceByStoreTable(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setTableGuid("tableGuid");
        final List<StoreDeviceDTO> storeDeviceDTOS = Arrays.asList(storeDeviceDTO);
        when(mockOrganizationClientService.listDeviceByStoreTable(any(PadOrderTypeReqDTO.class)))
                .thenReturn(storeDeviceDTOS);

        // Configure TableBasicMapper.selectOne(...).
        final TableBasicDO tableBasicDO = new TableBasicDO();
        tableBasicDO.setGuid("88229112-5e37-494c-803e-6c82dc9db9b9");
        tableBasicDO.setAreaGuid("areaGuid");
        tableBasicDO.setAreaName("areaName");
        tableBasicDO.setTableCode("tableCode");
        tableBasicDO.setSeats(0);
        when(mockTableBasicMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableBasicDO);

        // Configure TradeRpcClient.findByOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setGuid("26d8feb5-b1c7-4150-b261-151a57655758");
        orderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDTO.setGuestCount(0);
        orderDTO.setDiningTableGuid("diningTableGuid");
        when(mockTradeRpcClient.findByOrderGuid("orderGuid")).thenReturn(orderDTO);

        // Run the test
        bizMsgServiceImplUnderTest.sendMsg(turnTableDTO);

        // Verify the results
        // Confirm BizMsgRpcClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setSubject("name");
        businessMessageDTO.setContent("一体机拆台消息");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        businessMessageDTO.setPlatform("2");
        businessMessageDTO.setStoreGuid("storeGuid");
        businessMessageDTO.setStoreName("storeName");
        businessMessageDTO.setMessageTypeStr("messageTypeStr");
        verify(mockBizMsgRpcClient).sendMsg(businessMessageDTO);
    }

    @Test
    public void testSendMsg4_OrganizationClientServiceReturnsNoItems() {
        // Setup
        final TurnTableDTO turnTableDTO = new TurnTableDTO();
        turnTableDTO.setStoreGuid("storeGuid");
        turnTableDTO.setStoreName("storeName");
        turnTableDTO.setOriginTableGuid("originTableGuid");
        turnTableDTO.setOriginTableCode("originTableCode");
        turnTableDTO.setNewTableGuid("newTableGuid");

        // Configure TableOrderMapper.selectOne(...).
        final TableOrderDO tableOrderDO = new TableOrderDO();
        tableOrderDO.setId(0L);
        tableOrderDO.setGuid("9268bc97-5eb9-4650-9a08-496314cff50d");
        tableOrderDO.setTableGuid("tableGuid");
        tableOrderDO.setMainOrderGuid("mainOrderGuid");
        tableOrderDO.setOrderGuid("orderGuid");
        when(mockTableOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(tableOrderDO);

        when(mockOrganizationClientService.listDeviceByStoreTable(any(PadOrderTypeReqDTO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        bizMsgServiceImplUnderTest.sendMsg(turnTableDTO);

        // Verify the results
    }

    @Test
    public void testSendCloseMsg() {
        // Setup
        // Configure OrganizationClientService.queryDeviceByStoreTable(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setTableGuid("tableGuid");
        when(mockOrganizationClientService.queryDeviceByStoreTable(any(PadOrderTypeReqDTO.class)))
                .thenReturn(storeDeviceDTO);

        // Run the test
        bizMsgServiceImplUnderTest.sendCloseMsg("storeGuid", "storeName", "tableGuid");

        // Verify the results
        // Confirm BizMsgRpcClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setSubject("name");
        businessMessageDTO.setContent("一体机拆台消息");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        businessMessageDTO.setPlatform("2");
        businessMessageDTO.setStoreGuid("storeGuid");
        businessMessageDTO.setStoreName("storeName");
        businessMessageDTO.setMessageTypeStr("messageTypeStr");
        verify(mockBizMsgRpcClient).sendMsg(businessMessageDTO);
    }

    @Test
    public void testSendMsg5() {
        // Setup
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setStoreGuid("storeGuid");
        tableInfoDTO.setStoreName("storeName");
        tableInfoDTO.setTableName("tableName");
        tableInfoDTO.setAreaGuid("areaGuid");
        tableInfoDTO.setAreaName("areaName");
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);

        // Configure TradeRpcClient.listOrderByCombineOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setGuid("26d8feb5-b1c7-4150-b261-151a57655758");
        orderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDTO.setGuestCount(0);
        orderDTO.setDiningTableGuid("diningTableGuid");
        final List<OrderDTO> orderDTOS = Arrays.asList(orderDTO);
        when(mockTradeRpcClient.listOrderByCombineOrderGuid("mainOrderGuid")).thenReturn(orderDTOS);

        // Configure OrganizationClientService.listDeviceByStoreTable(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
        storeDeviceDTO.setEnterpriseGuid("enterpriseGuid");
        storeDeviceDTO.setStoreNo("storeNo");
        storeDeviceDTO.setStoreGuid("storeGuid");
        storeDeviceDTO.setDeviceNo("deviceNo");
        storeDeviceDTO.setTableGuid("tableGuid");
        final List<StoreDeviceDTO> storeDeviceDTOS = Arrays.asList(storeDeviceDTO);
        when(mockOrganizationClientService.listDeviceByStoreTable(any(PadOrderTypeReqDTO.class)))
                .thenReturn(storeDeviceDTOS);

        // Run the test
        bizMsgServiceImplUnderTest.sendMsg(tableOrderCombineDTO, Lists.newArrayList("1"));

        // Verify the results
        // Confirm BizMsgRpcClient.sendMsg(...).
        final BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setSubject("name");
        businessMessageDTO.setContent("一体机拆台消息");
        businessMessageDTO.setMessageType(0);
        businessMessageDTO.setDetailMessageType(0);
        businessMessageDTO.setPlatform("2");
        businessMessageDTO.setStoreGuid("storeGuid");
        businessMessageDTO.setStoreName("storeName");
        businessMessageDTO.setMessageTypeStr("messageTypeStr");
        verify(mockBizMsgRpcClient).sendMsg(businessMessageDTO);
    }

    @Test
    public void testSendMsg5_TradeRpcClientReturnsNoItems() {
        // Setup
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setStoreGuid("storeGuid");
        tableInfoDTO.setStoreName("storeName");
        tableInfoDTO.setTableName("tableName");
        tableInfoDTO.setAreaGuid("areaGuid");
        tableInfoDTO.setAreaName("areaName");
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);
        when(mockTradeRpcClient.listOrderByCombineOrderGuid("mainOrderGuid")).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> bizMsgServiceImplUnderTest.sendMsg(tableOrderCombineDTO, Lists.newArrayList("1")))
                .isInstanceOf(BusinessException.class);
    }

    @Test
    public void testSendMsg5_OrganizationClientServiceReturnsNoItems() {
        // Setup
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setStoreGuid("storeGuid");
        tableInfoDTO.setStoreName("storeName");
        tableInfoDTO.setTableName("tableName");
        tableInfoDTO.setAreaGuid("areaGuid");
        tableInfoDTO.setAreaName("areaName");
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);

        // Configure TradeRpcClient.listOrderByCombineOrderGuid(...).
        final OrderDTO orderDTO = new OrderDTO();
        orderDTO.setGuid("26d8feb5-b1c7-4150-b261-151a57655758");
        orderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDTO.setGuestCount(0);
        orderDTO.setDiningTableGuid("diningTableGuid");
        final List<OrderDTO> orderDTOS = Arrays.asList(orderDTO);
        when(mockTradeRpcClient.listOrderByCombineOrderGuid("mainOrderGuid")).thenReturn(orderDTOS);

        when(mockOrganizationClientService.listDeviceByStoreTable(any(PadOrderTypeReqDTO.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        bizMsgServiceImplUnderTest.sendMsg(tableOrderCombineDTO, Lists.newArrayList("1"));

        // Verify the results
    }
}
