package com.holderzone.holder.saas.store.deposit.service.impl;

import com.holderzone.holder.saas.store.deposit.entity.bo.RemindDO;
import com.holderzone.holder.saas.store.deposit.mapstruct.RemindMapstruct;
import com.holderzone.holder.saas.store.deposit.service.DistributedIdService;
import com.holderzone.holder.saas.store.deposit.service.rpc.StoreRpcService;
import com.holderzone.saas.store.dto.deposit.req.MessageRemindReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsdRemindServiceImplTest {

    @Mock
    private DistributedIdService mockDistributedIdService;
    @Mock
    private StoreRpcService mockStoreRpcService;
    @Mock
    private RemindMapstruct mockRemindMapstruct;

    private HsdRemindServiceImpl hsdRemindServiceImplUnderTest;

    @Before
    public void setUp() {
        hsdRemindServiceImplUnderTest = new HsdRemindServiceImpl(mockDistributedIdService, mockStoreRpcService,
                mockRemindMapstruct);
    }

    @Test
    public void testCreateRemindRecord() {
        // Setup
        final MessageRemindReqDTO messageRemindReqDTO = new MessageRemindReqDTO();
        messageRemindReqDTO.setStoreGuid("storeGuid");
        messageRemindReqDTO.setStoreName("storeName");
        messageRemindReqDTO.setDepositRemind(0);
        messageRemindReqDTO.setGetRemind(0);
        messageRemindReqDTO.setExpireRemind(0);

        // Configure RemindMapstruct.fromRemindDTO(...).
        final RemindDO remindDO = new RemindDO();
        remindDO.setId(0L);
        remindDO.setGuid("70e7a53e-20cf-42dd-9e23-015b0735da0f");
        remindDO.setStoreGuid("storeGuid");
        remindDO.setStoreName("name");
        remindDO.setAdvanceDays(0);
        final MessageRemindReqDTO messageRemindReqDTO1 = new MessageRemindReqDTO();
        messageRemindReqDTO1.setStoreGuid("storeGuid");
        messageRemindReqDTO1.setStoreName("storeName");
        messageRemindReqDTO1.setDepositRemind(0);
        messageRemindReqDTO1.setGetRemind(0);
        messageRemindReqDTO1.setExpireRemind(0);
        when(mockRemindMapstruct.fromRemindDTO(messageRemindReqDTO1)).thenReturn(remindDO);

        when(mockDistributedIdService.nextRemindGuid()).thenReturn("70e7a53e-20cf-42dd-9e23-015b0735da0f");

        // Run the test
        final Boolean result = hsdRemindServiceImplUnderTest.createRemindRecord(messageRemindReqDTO);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testQueryRemindRecord() {
        // Setup
        final MessageRemindReqDTO expectedResult = new MessageRemindReqDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setDepositRemind(0);
        expectedResult.setGetRemind(0);
        expectedResult.setExpireRemind(0);

        // Configure StoreRpcService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("c0ddbca5-0ca8-41f5-9a8d-931c73726348");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockStoreRpcService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure RemindMapstruct.fromRemindDO(...).
        final MessageRemindReqDTO messageRemindReqDTO = new MessageRemindReqDTO();
        messageRemindReqDTO.setStoreGuid("storeGuid");
        messageRemindReqDTO.setStoreName("storeName");
        messageRemindReqDTO.setDepositRemind(0);
        messageRemindReqDTO.setGetRemind(0);
        messageRemindReqDTO.setExpireRemind(0);
        final RemindDO remindDO = new RemindDO();
        remindDO.setId(0L);
        remindDO.setGuid("70e7a53e-20cf-42dd-9e23-015b0735da0f");
        remindDO.setStoreGuid("storeGuid");
        remindDO.setStoreName("name");
        remindDO.setAdvanceDays(0);
        when(mockRemindMapstruct.fromRemindDO(remindDO)).thenReturn(messageRemindReqDTO);

        // Run the test
        final MessageRemindReqDTO result = hsdRemindServiceImplUnderTest.queryRemindRecord("storeGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
