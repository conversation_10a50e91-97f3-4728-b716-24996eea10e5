package com.holderzone.holder.saas.store.report.controller;

import com.holderzone.holder.saas.store.report.service.TradeFreeService;
import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.base.Pager;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.FreeItemDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashMap;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(TradeFreeController.class)
public class TradeFreeControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private TradeFreeService mockTradeFreeService;

    @Test
    public void testList() throws Exception {
        // Setup
        // Configure TradeFreeService.list(...).
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        freeItemDTO.setBrandName("brandName");
        freeItemDTO.setStoreName("storeName");
        freeItemDTO.setGoodsName("goodsName");
        freeItemDTO.setSkuGuid("skuGuid");
        freeItemDTO.setGoodsGuid("goodsGuid");
        final Pager pager = new Pager();
        pager.setPageNo(0);
        pager.setPageSize(0);
        pager.setTotalCount(0);
        pager.setTotalPages(0);
        final Message<FreeItemDTO> freeItemDTOMessage = new Message<>(Arrays.asList(freeItemDTO), new HashMap<>(),
                pager);
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeFreeService.list(query)).thenReturn(freeItemDTOMessage);

        // Run the test and verify the results
        mockMvc.perform(post("/trade/free/list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testList_TradeFreeServiceReturnsNoItem() throws Exception {
        // Setup
        // Configure TradeFreeService.list(...).
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeFreeService.list(query)).thenReturn(Message.getInstance());

        // Run the test and verify the results
        mockMvc.perform(post("/trade/free/list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("", true));
    }

    @Test
    public void testExport() throws Exception {
        // Setup
        // Configure TradeFreeService.export(...).
        final ReportQueryVO query = new ReportQueryVO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setStartTime(LocalDate.of(2020, 1, 1));
        query.setEndTime(LocalDate.of(2020, 1, 1));
        query.setEnterpriseGuid("enterpriseGuid");
        when(mockTradeFreeService.export(query)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/trade/free/export")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
