package com.holderzone.holder.saas.store.pay.service.impl;

import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.holder.saas.store.pay.config.AggPayConfig;
import com.holderzone.holder.saas.store.pay.entity.HandlerPayBO;
import com.holderzone.saas.store.dto.pay.AggPayPollingDTO;
import org.apache.rocketmq.common.message.Message;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RocketServiceImplTest {

    @Mock
    private DefaultRocketMqProducer mockDefaultRocketMqProducer;
    @Mock
    private AggPayConfig mockAggPayConfig;

    private RocketServiceImpl rocketServiceImplUnderTest;

    @Before
    public void setUp() {
        rocketServiceImplUnderTest = new RocketServiceImpl(mockDefaultRocketMqProducer, mockAggPayConfig);
    }

    @Test
    public void testPolling() {
        // Setup
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("72f5bfde-858e-4b57-8f5d-ab1f2be2e4a3");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("56069537-545d-4a9f-81e6-671e2ed542df");
        pollingJHPayDTO.setAppId("appId");

        final HandlerPayBO handlerPayBO = HandlerPayBO.builder()
                .times(0)
                .build();
        when(mockAggPayConfig.getPollingTimes()).thenReturn(0);
        when(mockDefaultRocketMqProducer.sendMessage(any(Message.class))).thenReturn(false);

        // Run the test
        final boolean result = rocketServiceImplUnderTest.polling(pollingJHPayDTO, handlerPayBO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testPolling_DefaultRocketMqProducerReturnsTrue() {
        // Setup
        final AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setOrderGUID("72f5bfde-858e-4b57-8f5d-ab1f2be2e4a3");
        pollingJHPayDTO.setAttachData("attachData");
        pollingJHPayDTO.setRefundNo("refundNo");
        pollingJHPayDTO.setPayGUID("56069537-545d-4a9f-81e6-671e2ed542df");
        pollingJHPayDTO.setAppId("appId");

        final HandlerPayBO handlerPayBO = HandlerPayBO.builder()
                .times(0)
                .build();
        when(mockAggPayConfig.getPollingTimes()).thenReturn(0);
        when(mockDefaultRocketMqProducer.sendMessage(any(Message.class))).thenReturn(true);

        // Run the test
        final boolean result = rocketServiceImplUnderTest.polling(pollingJHPayDTO, handlerPayBO);

        // Verify the results
        assertThat(result).isTrue();
    }
}
