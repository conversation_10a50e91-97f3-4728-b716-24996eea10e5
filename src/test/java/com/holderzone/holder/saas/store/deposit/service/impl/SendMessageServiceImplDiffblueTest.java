package com.holderzone.holder.saas.store.deposit.service.impl;

import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.holderzone.framework.base.dto.message.BusinessMessage;
import com.holderzone.framework.base.dto.message.DeviceMessage;
import com.holderzone.framework.base.dto.message.MailMessageDTO;
import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.framework.base.dto.message.MessageType;
import com.holderzone.framework.base.dto.message.PushMessageDTO;
import com.holderzone.framework.base.dto.message.ShortMessageDTO;
import com.holderzone.framework.base.dto.message.ShortMessageType;
import com.holderzone.framework.base.dto.message.SysMessage;
import com.holderzone.framework.base.dto.message.TopicType;
import com.holderzone.holder.saas.store.deposit.service.rpc.EntServiceClient;
import com.holderzone.holder.saas.store.deposit.service.rpc.MsgClientService;
import com.holderzone.resource.common.dto.enterprise.DeductShortMessageDTO;
import com.holderzone.resource.common.dto.enterprise.MessageConfigDTO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@ContextConfiguration(classes = {SendMessageServiceImpl.class})
@RunWith(SpringJUnit4ClassRunner.class)
public class SendMessageServiceImplDiffblueTest {
    @MockBean
    private EntServiceClient entServiceClient;

    @MockBean
    private MsgClientService msgClientService;

    @Autowired
    private SendMessageServiceImpl sendMessageServiceImpl;

    /**
     * Method under test:
     * {@link SendMessageServiceImpl#sendMessage(MessageDTO, String)}
     */
    @Test
    public void testSendMessage() {
        MessageConfigDTO messageConfigDTO = new MessageConfigDTO();
        messageConfigDTO.setAfterCharge(1);
        messageConfigDTO.setAfterConsume(1);
        messageConfigDTO.setAppreciateGuid("1234");
        messageConfigDTO.setCreateTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        messageConfigDTO.setForeignKey("Foreign Key");
        messageConfigDTO.setId(1);
        messageConfigDTO.setResidueCount(1);
        messageConfigDTO.setUpdateTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        when(entServiceClient.deductShortMessage(Mockito.<List<DeductShortMessageDTO>>any())).thenReturn(true);
        when(entServiceClient.getMessageInfo(Mockito.<String>any())).thenReturn(messageConfigDTO);
        doNothing().when(msgClientService).sendMessage(Mockito.<MessageDTO>any());

        MailMessageDTO mailMessage = new MailMessageDTO();
        mailMessage.setContext("Context");
        mailMessage.setReceivers(new ArrayList<>());
        mailMessage.setSubject("Hello from the Dreaming Spires");

        BusinessMessage businessMessage = new BusinessMessage();
        businessMessage.setBusinessType("Business Type");
        businessMessage.setEnterpriseGuid("1234");
        businessMessage.setStoreGuid("1234");

        DeviceMessage deviceMessage = new DeviceMessage();
        deviceMessage.setDeviceNumber("42");
        deviceMessage.setEnterpriseGuid("1234");

        SysMessage sysMessage = new SysMessage();
        sysMessage.setTopic("Topic");

        PushMessageDTO pushMessage = new PushMessageDTO();
        pushMessage.setBusinessMessage(businessMessage);
        pushMessage.setData("Data");
        pushMessage.setDeviceMessage(deviceMessage);
        pushMessage.setSysMessage(sysMessage);
        pushMessage.setTopicType(TopicType.SYSTEM);

        ShortMessageDTO shortMessage = new ShortMessageDTO();
        shortMessage.setContent("Not all who wander are lost");
        shortMessage.setParams(new HashMap<>());
        shortMessage.setPhoneNumber("6625550144");
        shortMessage.setShortMessageType(ShortMessageType.BOOK);

        MessageDTO m1 = new MessageDTO();
        m1.setMailMessage(mailMessage);
        m1.setMessageType(MessageType.SHORT_MESSAGE);
        m1.setPushMessage(pushMessage);
        m1.setShortMessage(shortMessage);
        sendMessageServiceImpl.sendMessage(m1, "1234");
        verify(entServiceClient).deductShortMessage(Mockito.<List<DeductShortMessageDTO>>any());
        verify(entServiceClient).getMessageInfo(Mockito.<String>any());
        verify(msgClientService).sendMessage(Mockito.<MessageDTO>any());
    }

    /**
     * Method under test:
     * {@link SendMessageServiceImpl#sendMessage(MessageDTO, String)}
     */
    @Test
    public void testSendMessage2() {
        MessageConfigDTO messageConfigDTO = new MessageConfigDTO();
        messageConfigDTO.setAfterCharge(1);
        messageConfigDTO.setAfterConsume(1);
        messageConfigDTO.setAppreciateGuid("1234");
        messageConfigDTO.setCreateTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        messageConfigDTO.setForeignKey("Foreign Key");
        messageConfigDTO.setId(1);
        messageConfigDTO.setResidueCount(1);
        messageConfigDTO.setUpdateTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        when(entServiceClient.deductShortMessage(Mockito.<List<DeductShortMessageDTO>>any())).thenReturn(false);
        when(entServiceClient.getMessageInfo(Mockito.<String>any())).thenReturn(messageConfigDTO);

        MailMessageDTO mailMessage = new MailMessageDTO();
        mailMessage.setContext("Context");
        mailMessage.setReceivers(new ArrayList<>());
        mailMessage.setSubject("Hello from the Dreaming Spires");

        BusinessMessage businessMessage = new BusinessMessage();
        businessMessage.setBusinessType("Business Type");
        businessMessage.setEnterpriseGuid("1234");
        businessMessage.setStoreGuid("1234");

        DeviceMessage deviceMessage = new DeviceMessage();
        deviceMessage.setDeviceNumber("42");
        deviceMessage.setEnterpriseGuid("1234");

        SysMessage sysMessage = new SysMessage();
        sysMessage.setTopic("Topic");

        PushMessageDTO pushMessage = new PushMessageDTO();
        pushMessage.setBusinessMessage(businessMessage);
        pushMessage.setData("Data");
        pushMessage.setDeviceMessage(deviceMessage);
        pushMessage.setSysMessage(sysMessage);
        pushMessage.setTopicType(TopicType.SYSTEM);

        ShortMessageDTO shortMessage = new ShortMessageDTO();
        shortMessage.setContent("Not all who wander are lost");
        shortMessage.setParams(new HashMap<>());
        shortMessage.setPhoneNumber("6625550144");
        shortMessage.setShortMessageType(ShortMessageType.BOOK);

        MessageDTO m1 = new MessageDTO();
        m1.setMailMessage(mailMessage);
        m1.setMessageType(MessageType.SHORT_MESSAGE);
        m1.setPushMessage(pushMessage);
        m1.setShortMessage(shortMessage);
        sendMessageServiceImpl.sendMessage(m1, "1234");
        verify(entServiceClient).deductShortMessage(Mockito.<List<DeductShortMessageDTO>>any());
        verify(entServiceClient).getMessageInfo(Mockito.<String>any());
    }

    /**
     * Method under test:
     * {@link SendMessageServiceImpl#sendMessage(MessageDTO, String)}
     */
    @Test
    public void testSendMessage3() {
        MessageConfigDTO messageConfigDTO = mock(MessageConfigDTO.class);
        when(messageConfigDTO.getResidueCount()).thenReturn(-1);
        doNothing().when(messageConfigDTO).setAfterCharge(anyInt());
        doNothing().when(messageConfigDTO).setAfterConsume(anyInt());
        doNothing().when(messageConfigDTO).setAppreciateGuid(Mockito.<String>any());
        doNothing().when(messageConfigDTO).setCreateTime(Mockito.<LocalDateTime>any());
        doNothing().when(messageConfigDTO).setForeignKey(Mockito.<String>any());
        doNothing().when(messageConfigDTO).setId(anyInt());
        doNothing().when(messageConfigDTO).setResidueCount(anyInt());
        doNothing().when(messageConfigDTO).setUpdateTime(Mockito.<LocalDateTime>any());
        messageConfigDTO.setAfterCharge(1);
        messageConfigDTO.setAfterConsume(1);
        messageConfigDTO.setAppreciateGuid("1234");
        messageConfigDTO.setCreateTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        messageConfigDTO.setForeignKey("Foreign Key");
        messageConfigDTO.setId(1);
        messageConfigDTO.setResidueCount(1);
        messageConfigDTO.setUpdateTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        when(entServiceClient.getMessageInfo(Mockito.<String>any())).thenReturn(messageConfigDTO);

        MailMessageDTO mailMessage = new MailMessageDTO();
        mailMessage.setContext("Context");
        mailMessage.setReceivers(new ArrayList<>());
        mailMessage.setSubject("Hello from the Dreaming Spires");

        BusinessMessage businessMessage = new BusinessMessage();
        businessMessage.setBusinessType("Business Type");
        businessMessage.setEnterpriseGuid("1234");
        businessMessage.setStoreGuid("1234");

        DeviceMessage deviceMessage = new DeviceMessage();
        deviceMessage.setDeviceNumber("42");
        deviceMessage.setEnterpriseGuid("1234");

        SysMessage sysMessage = new SysMessage();
        sysMessage.setTopic("Topic");

        PushMessageDTO pushMessage = new PushMessageDTO();
        pushMessage.setBusinessMessage(businessMessage);
        pushMessage.setData("Data");
        pushMessage.setDeviceMessage(deviceMessage);
        pushMessage.setSysMessage(sysMessage);
        pushMessage.setTopicType(TopicType.SYSTEM);

        ShortMessageDTO shortMessage = new ShortMessageDTO();
        shortMessage.setContent("Not all who wander are lost");
        shortMessage.setParams(new HashMap<>());
        shortMessage.setPhoneNumber("6625550144");
        shortMessage.setShortMessageType(ShortMessageType.BOOK);

        MessageDTO m1 = new MessageDTO();
        m1.setMailMessage(mailMessage);
        m1.setMessageType(MessageType.SHORT_MESSAGE);
        m1.setPushMessage(pushMessage);
        m1.setShortMessage(shortMessage);
        sendMessageServiceImpl.sendMessage(m1, "1234");
        verify(entServiceClient).getMessageInfo(Mockito.<String>any());
        verify(messageConfigDTO, atLeast(1)).getResidueCount();
        verify(messageConfigDTO).setAfterCharge(anyInt());
        verify(messageConfigDTO).setAfterConsume(anyInt());
        verify(messageConfigDTO).setAppreciateGuid(Mockito.<String>any());
        verify(messageConfigDTO).setCreateTime(Mockito.<LocalDateTime>any());
        verify(messageConfigDTO).setForeignKey(Mockito.<String>any());
        verify(messageConfigDTO).setId(anyInt());
        verify(messageConfigDTO, atLeast(1)).setResidueCount(anyInt());
        verify(messageConfigDTO).setUpdateTime(Mockito.<LocalDateTime>any());
    }
}
