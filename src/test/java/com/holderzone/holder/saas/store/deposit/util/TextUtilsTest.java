package com.holderzone.holder.saas.store.deposit.util;

import org.junit.Test;

import java.io.UnsupportedEncodingException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

public class TextUtilsTest {

    @Test
    public void testFilterIllegalCharacter() {
        assertThat(TextUtils.filterIllegalCharacter("source", "replaceStr")).isEqualTo("result");
    }

    @Test
    public void testIsDecodeText() {
        assertThat(TextUtils.isDecodeText('a')).isFalse();
    }

    @Test
    public void testDecodeEmoji() throws Exception {
        assertThat(TextUtils.decodeEmoji("source")).isEqualTo("result");
        assertThatThrownBy(() -> TextUtils.decodeEmoji("source")).isInstanceOf(UnsupportedEncodingException.class);
    }

    @Test
    public void testMain() {
        // Setup
        // Run the test
        TextUtils.main(new String[]{"args"});

        // Verify the results
    }
}
