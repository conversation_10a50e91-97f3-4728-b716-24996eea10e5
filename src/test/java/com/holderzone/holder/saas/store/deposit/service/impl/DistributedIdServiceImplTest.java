package com.holderzone.holder.saas.store.deposit.service.impl;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import static org.assertj.core.api.Assertions.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class DistributedIdServiceImplTest {

    @Mock
    private StringRedisTemplate mockRedisTemplate;

    private DistributedIdServiceImpl distributedIdServiceImplUnderTest;

    @Before
    public void setUp() {
        distributedIdServiceImplUnderTest = new DistributedIdServiceImpl(mockRedisTemplate);
    }

    @Test
    public void testRawId() {
        assertThat(distributedIdServiceImplUnderTest.rawId("tag")).isEqualTo(0L);
    }

    @Test
    public void testNextId() {
        assertThat(distributedIdServiceImplUnderTest.nextId("tag")).isEqualTo("result");
    }

    @Test
    public void testNextDepositItemGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextDepositItemGuid()).isEqualTo("result");
    }

    @Test
    public void testNextGoodsGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextGoodsGuid()).isEqualTo("result");
    }

    @Test
    public void testNextOperationGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextOperationGuid()).isEqualTo("result");
    }

    @Test
    public void testNextOperationGoodsGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextOperationGoodsGuid()).isEqualTo("result");
    }

    @Test
    public void testNextRemindGuid() {
        assertThat(distributedIdServiceImplUnderTest.nextRemindGuid()).isEqualTo("result");
    }
}
