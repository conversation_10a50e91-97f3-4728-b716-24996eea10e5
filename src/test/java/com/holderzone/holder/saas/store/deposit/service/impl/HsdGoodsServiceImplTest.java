package com.holderzone.holder.saas.store.deposit.service.impl;

import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.deposit.entity.bo.GoodsDO;
import com.holderzone.holder.saas.store.deposit.mapstruct.GoodsMapstruct;
import com.holderzone.holder.saas.store.deposit.service.DistributedIdService;
import com.holderzone.holder.saas.store.deposit.service.rpc.ItemRpcService;
import com.holderzone.saas.store.dto.deposit.req.DepositQueryReqForWebDTO;
import com.holderzone.saas.store.dto.deposit.resp.GoodsSummaryRespDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class HsdGoodsServiceImplTest {

    @Mock
    private ItemRpcService mockItemRpcService;
    @Mock
    private DistributedIdService mockDistributedIdService;
    @Mock
    private GoodsMapstruct mockGoodsMapstruct;

    private HsdGoodsServiceImpl hsdGoodsServiceImplUnderTest;

    @Before
    public void setUp() {
        hsdGoodsServiceImplUnderTest = new HsdGoodsServiceImpl(mockItemRpcService, mockDistributedIdService,
                mockGoodsMapstruct);
    }

    @Test
    public void testCreateGoodsItem() {
        // Setup
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setGuid("99115280-79ce-4a70-96f5-200b5a27c25e");
        goodsDO.setStoreGuid("storeGuid");
        goodsDO.setDepositGuid("depositGuid");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setResidueNum(0);

        // Run the test
        final Boolean result = hsdGoodsServiceImplUnderTest.createGoodsItem(goodsDO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testQueryGoodsList() {
        // Setup
        final GoodsDO goodsDO = new GoodsDO();
        goodsDO.setGuid("99115280-79ce-4a70-96f5-200b5a27c25e");
        goodsDO.setStoreGuid("storeGuid");
        goodsDO.setDepositGuid("depositGuid");
        goodsDO.setGoodsName("goodsName");
        goodsDO.setResidueNum(0);
        final List<GoodsDO> expectedResult = Arrays.asList(goodsDO);

        // Run the test
        final List<GoodsDO> result = hsdGoodsServiceImplUnderTest.queryGoodsList("depositGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryGoodsItem() {
        // Setup
        final GoodsDO expectedResult = new GoodsDO();
        expectedResult.setGuid("99115280-79ce-4a70-96f5-200b5a27c25e");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setDepositGuid("depositGuid");
        expectedResult.setGoodsName("goodsName");
        expectedResult.setResidueNum(0);

        // Run the test
        final GoodsDO result = hsdGoodsServiceImplUnderTest.queryGoodsItem("depositGuid", "goodsGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryGoodsSummary() {
        // Setup
        final DepositQueryReqForWebDTO depositQueryReqDTO = new DepositQueryReqForWebDTO();
        depositQueryReqDTO.setStoreGuid("storeGuid");
        depositQueryReqDTO.setCondition("condition");

        // Run the test
        final Page<GoodsSummaryRespDTO> result = hsdGoodsServiceImplUnderTest.queryGoodsSummary(depositQueryReqDTO);

        // Verify the results
    }

    @Test
    public void testQueryExpireGoods() {
        assertThat(hsdGoodsServiceImplUnderTest.queryExpireGoods("depositGuid")).isNull();
    }
}
