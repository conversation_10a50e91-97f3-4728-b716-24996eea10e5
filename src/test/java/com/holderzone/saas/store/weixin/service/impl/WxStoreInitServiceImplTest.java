package com.holderzone.saas.store.weixin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderConfigDO;
import com.holderzone.saas.store.weixin.entity.domain.WxQueueConfigDO;
import com.holderzone.saas.store.weixin.service.WxQueueConfigService;
import com.holderzone.saas.store.weixin.service.WxReserveConfigService;
import com.holderzone.saas.store.weixin.service.WxStoreOrderConfigService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStoreInitServiceImplTest {

    @Mock
    private WxStoreOrderConfigService mockWxStoreOrderConfigService;
    @Mock
    private WxQueueConfigService mockWxQueueConfigService;
    @Mock
    private WxReserveConfigService mockWxReserveConfigService;
    @Mock
    private RedisUtils mockRedisUtils;

    private WxStoreInitServiceImpl wxStoreInitServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxStoreInitServiceImplUnderTest = new WxStoreInitServiceImpl(mockWxStoreOrderConfigService,
                mockWxQueueConfigService, mockWxReserveConfigService, mockRedisUtils);
    }

    @Test
    public void testInitWxStore() {
        // Setup
        // Configure WxStoreOrderConfigService.list(...).
        final WxOrderConfigDO wxOrderConfigDO = new WxOrderConfigDO();
        wxOrderConfigDO.setGuid("1f430a35-fa15-49b7-b5d0-d5f578674f38");
        wxOrderConfigDO.setStoreGuid("storeGuid");
        wxOrderConfigDO.setMenuType(0);
        wxOrderConfigDO.setOrderModel(0);
        wxOrderConfigDO.setTakingModel(0);
        wxOrderConfigDO.setIsOnlinePayed(0);
        wxOrderConfigDO.setIsWeighingOrdered(0);
        wxOrderConfigDO.setIsRemarked(0);
        wxOrderConfigDO.setUrlType(0);
        wxOrderConfigDO.setTagNames("tagNames");
        wxOrderConfigDO.setIsOrderOpen(0);
        final List<WxOrderConfigDO> wxOrderConfigDOS = Arrays.asList(wxOrderConfigDO);
        when(mockWxStoreOrderConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(wxOrderConfigDOS);

        when(mockRedisUtils.keyGenerate(BusinessName.WX)).thenReturn("redisKey");
        when(mockRedisUtils.generateGuid("redisKey")).thenReturn("1f430a35-fa15-49b7-b5d0-d5f578674f38");

        // Configure WxQueueConfigService.list(...).
        final List<WxQueueConfigDO> wxQueueConfigDOS = Arrays.asList(
                new WxQueueConfigDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        "e376e66c-c804-41ff-9706-1889a9f9afef", "storeGuid", 0, 0, 0, new BigDecimal("0.00"), 0));
        when(mockWxQueueConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(wxQueueConfigDOS);

        // Run the test
        wxStoreInitServiceImplUnderTest.initWxStore(Arrays.asList("value"));

        // Verify the results
        // Confirm WxStoreOrderConfigService.saveBatch(...).
        final WxOrderConfigDO wxOrderConfigDO1 = new WxOrderConfigDO();
        wxOrderConfigDO1.setGuid("1f430a35-fa15-49b7-b5d0-d5f578674f38");
        wxOrderConfigDO1.setStoreGuid("storeGuid");
        wxOrderConfigDO1.setMenuType(0);
        wxOrderConfigDO1.setOrderModel(0);
        wxOrderConfigDO1.setTakingModel(0);
        wxOrderConfigDO1.setIsOnlinePayed(0);
        wxOrderConfigDO1.setIsWeighingOrdered(0);
        wxOrderConfigDO1.setIsRemarked(0);
        wxOrderConfigDO1.setUrlType(0);
        wxOrderConfigDO1.setTagNames("tagNames");
        wxOrderConfigDO1.setIsOrderOpen(0);
        final List<WxOrderConfigDO> entityList = Arrays.asList(wxOrderConfigDO1);
        verify(mockWxStoreOrderConfigService).saveBatch(entityList);
        verify(mockWxQueueConfigService).saveBatch(Arrays.asList(
                new WxQueueConfigDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        "e376e66c-c804-41ff-9706-1889a9f9afef", "storeGuid", 0, 0, 0, new BigDecimal("0.00"), 0)));
        verify(mockWxReserveConfigService).initWxReserveConfig(Arrays.asList("value"));
    }

    @Test
    public void testInitWxStore_WxStoreOrderConfigServiceListReturnsNoItems() {
        // Setup
        when(mockWxStoreOrderConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockRedisUtils.keyGenerate(BusinessName.WX)).thenReturn("redisKey");
        when(mockRedisUtils.generateGuid("redisKey")).thenReturn("1f430a35-fa15-49b7-b5d0-d5f578674f38");

        // Configure WxQueueConfigService.list(...).
        final List<WxQueueConfigDO> wxQueueConfigDOS = Arrays.asList(
                new WxQueueConfigDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        "e376e66c-c804-41ff-9706-1889a9f9afef", "storeGuid", 0, 0, 0, new BigDecimal("0.00"), 0));
        when(mockWxQueueConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(wxQueueConfigDOS);

        // Run the test
        wxStoreInitServiceImplUnderTest.initWxStore(Arrays.asList("value"));

        // Verify the results
        // Confirm WxStoreOrderConfigService.saveBatch(...).
        final WxOrderConfigDO wxOrderConfigDO = new WxOrderConfigDO();
        wxOrderConfigDO.setGuid("1f430a35-fa15-49b7-b5d0-d5f578674f38");
        wxOrderConfigDO.setStoreGuid("storeGuid");
        wxOrderConfigDO.setMenuType(0);
        wxOrderConfigDO.setOrderModel(0);
        wxOrderConfigDO.setTakingModel(0);
        wxOrderConfigDO.setIsOnlinePayed(0);
        wxOrderConfigDO.setIsWeighingOrdered(0);
        wxOrderConfigDO.setIsRemarked(0);
        wxOrderConfigDO.setUrlType(0);
        wxOrderConfigDO.setTagNames("tagNames");
        wxOrderConfigDO.setIsOrderOpen(0);
        final List<WxOrderConfigDO> entityList = Arrays.asList(wxOrderConfigDO);
        verify(mockWxStoreOrderConfigService).saveBatch(entityList);
        verify(mockWxQueueConfigService).saveBatch(Arrays.asList(
                new WxQueueConfigDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        "e376e66c-c804-41ff-9706-1889a9f9afef", "storeGuid", 0, 0, 0, new BigDecimal("0.00"), 0)));
        verify(mockWxReserveConfigService).initWxReserveConfig(Arrays.asList("value"));
    }

    @Test
    public void testInitWxStore_WxQueueConfigServiceListReturnsNoItems() {
        // Setup
        // Configure WxStoreOrderConfigService.list(...).
        final WxOrderConfigDO wxOrderConfigDO = new WxOrderConfigDO();
        wxOrderConfigDO.setGuid("1f430a35-fa15-49b7-b5d0-d5f578674f38");
        wxOrderConfigDO.setStoreGuid("storeGuid");
        wxOrderConfigDO.setMenuType(0);
        wxOrderConfigDO.setOrderModel(0);
        wxOrderConfigDO.setTakingModel(0);
        wxOrderConfigDO.setIsOnlinePayed(0);
        wxOrderConfigDO.setIsWeighingOrdered(0);
        wxOrderConfigDO.setIsRemarked(0);
        wxOrderConfigDO.setUrlType(0);
        wxOrderConfigDO.setTagNames("tagNames");
        wxOrderConfigDO.setIsOrderOpen(0);
        final List<WxOrderConfigDO> wxOrderConfigDOS = Arrays.asList(wxOrderConfigDO);
        when(mockWxStoreOrderConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(wxOrderConfigDOS);

        when(mockRedisUtils.keyGenerate(BusinessName.WX)).thenReturn("redisKey");
        when(mockRedisUtils.generateGuid("redisKey")).thenReturn("1f430a35-fa15-49b7-b5d0-d5f578674f38");
        when(mockWxQueueConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        wxStoreInitServiceImplUnderTest.initWxStore(Arrays.asList("value"));

        // Verify the results
        // Confirm WxStoreOrderConfigService.saveBatch(...).
        final WxOrderConfigDO wxOrderConfigDO1 = new WxOrderConfigDO();
        wxOrderConfigDO1.setGuid("1f430a35-fa15-49b7-b5d0-d5f578674f38");
        wxOrderConfigDO1.setStoreGuid("storeGuid");
        wxOrderConfigDO1.setMenuType(0);
        wxOrderConfigDO1.setOrderModel(0);
        wxOrderConfigDO1.setTakingModel(0);
        wxOrderConfigDO1.setIsOnlinePayed(0);
        wxOrderConfigDO1.setIsWeighingOrdered(0);
        wxOrderConfigDO1.setIsRemarked(0);
        wxOrderConfigDO1.setUrlType(0);
        wxOrderConfigDO1.setTagNames("tagNames");
        wxOrderConfigDO1.setIsOrderOpen(0);
        final List<WxOrderConfigDO> entityList = Arrays.asList(wxOrderConfigDO1);
        verify(mockWxStoreOrderConfigService).saveBatch(entityList);
        verify(mockWxQueueConfigService).saveBatch(Arrays.asList(
                new WxQueueConfigDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        "e376e66c-c804-41ff-9706-1889a9f9afef", "storeGuid", 0, 0, 0, new BigDecimal("0.00"), 0)));
        verify(mockWxReserveConfigService).initWxReserveConfig(Arrays.asList("value"));
    }

    @Test
    public void testInitWxStoreByMQ() {
        // Setup
        // Configure WxStoreOrderConfigService.getOne(...).
        final WxOrderConfigDO wxOrderConfigDO = new WxOrderConfigDO();
        wxOrderConfigDO.setGuid("1f430a35-fa15-49b7-b5d0-d5f578674f38");
        wxOrderConfigDO.setStoreGuid("storeGuid");
        wxOrderConfigDO.setMenuType(0);
        wxOrderConfigDO.setOrderModel(0);
        wxOrderConfigDO.setTakingModel(0);
        wxOrderConfigDO.setIsOnlinePayed(0);
        wxOrderConfigDO.setIsWeighingOrdered(0);
        wxOrderConfigDO.setIsRemarked(0);
        wxOrderConfigDO.setUrlType(0);
        wxOrderConfigDO.setTagNames("tagNames");
        wxOrderConfigDO.setIsOrderOpen(0);
        when(mockWxStoreOrderConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(wxOrderConfigDO);

        // Configure WxQueueConfigService.getOne(...).
        final WxQueueConfigDO wxQueueConfigDO = new WxQueueConfigDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "e376e66c-c804-41ff-9706-1889a9f9afef", "storeGuid", 0, 0, 0,
                new BigDecimal("0.00"), 0);
        when(mockWxQueueConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(wxQueueConfigDO);

        // Run the test
        wxStoreInitServiceImplUnderTest.initWxStoreByMQ("storeGuid");

        // Verify the results
        verify(mockWxReserveConfigService).initWxReserveConfig(Arrays.asList("value"));
    }

    @Test
    public void testInitWxStoreByMQ_WxStoreOrderConfigServiceGetOneReturnsNull() {
        // Setup
        when(mockWxStoreOrderConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(mockRedisUtils.keyGenerate(BusinessName.WX)).thenReturn("redisKey");
        when(mockRedisUtils.generateGuid("redisKey")).thenReturn("1f430a35-fa15-49b7-b5d0-d5f578674f38");

        // Configure WxStoreOrderConfigService.save(...).
        final WxOrderConfigDO entity = new WxOrderConfigDO();
        entity.setGuid("1f430a35-fa15-49b7-b5d0-d5f578674f38");
        entity.setStoreGuid("storeGuid");
        entity.setMenuType(0);
        entity.setOrderModel(0);
        entity.setTakingModel(0);
        entity.setIsOnlinePayed(0);
        entity.setIsWeighingOrdered(0);
        entity.setIsRemarked(0);
        entity.setUrlType(0);
        entity.setTagNames("tagNames");
        entity.setIsOrderOpen(0);
        when(mockWxStoreOrderConfigService.save(entity)).thenReturn(true);

        // Configure WxQueueConfigService.getOne(...).
        final WxQueueConfigDO wxQueueConfigDO = new WxQueueConfigDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "e376e66c-c804-41ff-9706-1889a9f9afef", "storeGuid", 0, 0, 0,
                new BigDecimal("0.00"), 0);
        when(mockWxQueueConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(wxQueueConfigDO);

        // Run the test
        wxStoreInitServiceImplUnderTest.initWxStoreByMQ("storeGuid");

        // Verify the results
        verify(mockWxReserveConfigService).initWxReserveConfig(Arrays.asList("value"));
    }

    @Test(expected = BusinessException.class)
    public void testInitWxStoreByMQ_WxStoreOrderConfigServiceSaveReturnsFalse() {
        // Setup
        when(mockWxStoreOrderConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(mockRedisUtils.keyGenerate(BusinessName.WX)).thenReturn("redisKey");
        when(mockRedisUtils.generateGuid("redisKey")).thenReturn("1f430a35-fa15-49b7-b5d0-d5f578674f38");

        // Configure WxStoreOrderConfigService.save(...).
        final WxOrderConfigDO entity = new WxOrderConfigDO();
        entity.setGuid("1f430a35-fa15-49b7-b5d0-d5f578674f38");
        entity.setStoreGuid("storeGuid");
        entity.setMenuType(0);
        entity.setOrderModel(0);
        entity.setTakingModel(0);
        entity.setIsOnlinePayed(0);
        entity.setIsWeighingOrdered(0);
        entity.setIsRemarked(0);
        entity.setUrlType(0);
        entity.setTagNames("tagNames");
        entity.setIsOrderOpen(0);
        when(mockWxStoreOrderConfigService.save(entity)).thenReturn(false);

        // Run the test
        wxStoreInitServiceImplUnderTest.initWxStoreByMQ("storeGuid");
    }

    @Test
    public void testInitWxStoreByMQ_WxQueueConfigServiceGetOneReturnsNull() {
        // Setup
        // Configure WxStoreOrderConfigService.getOne(...).
        final WxOrderConfigDO wxOrderConfigDO = new WxOrderConfigDO();
        wxOrderConfigDO.setGuid("1f430a35-fa15-49b7-b5d0-d5f578674f38");
        wxOrderConfigDO.setStoreGuid("storeGuid");
        wxOrderConfigDO.setMenuType(0);
        wxOrderConfigDO.setOrderModel(0);
        wxOrderConfigDO.setTakingModel(0);
        wxOrderConfigDO.setIsOnlinePayed(0);
        wxOrderConfigDO.setIsWeighingOrdered(0);
        wxOrderConfigDO.setIsRemarked(0);
        wxOrderConfigDO.setUrlType(0);
        wxOrderConfigDO.setTagNames("tagNames");
        wxOrderConfigDO.setIsOrderOpen(0);
        when(mockWxStoreOrderConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(wxOrderConfigDO);

        when(mockWxQueueConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(mockRedisUtils.keyGenerate(BusinessName.WX)).thenReturn("redisKey");
        when(mockRedisUtils.generateGuid("redisKey")).thenReturn("1f430a35-fa15-49b7-b5d0-d5f578674f38");
        when(mockWxQueueConfigService.save(
                new WxQueueConfigDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        "e376e66c-c804-41ff-9706-1889a9f9afef", "storeGuid", 0, 0, 0, new BigDecimal("0.00"),
                        0))).thenReturn(true);

        // Run the test
        wxStoreInitServiceImplUnderTest.initWxStoreByMQ("storeGuid");

        // Verify the results
        verify(mockWxReserveConfigService).initWxReserveConfig(Arrays.asList("value"));
    }

    @Test(expected = BusinessException.class)
    public void testInitWxStoreByMQ_WxQueueConfigServiceSaveReturnsFalse() {
        // Setup
        // Configure WxStoreOrderConfigService.getOne(...).
        final WxOrderConfigDO wxOrderConfigDO = new WxOrderConfigDO();
        wxOrderConfigDO.setGuid("1f430a35-fa15-49b7-b5d0-d5f578674f38");
        wxOrderConfigDO.setStoreGuid("storeGuid");
        wxOrderConfigDO.setMenuType(0);
        wxOrderConfigDO.setOrderModel(0);
        wxOrderConfigDO.setTakingModel(0);
        wxOrderConfigDO.setIsOnlinePayed(0);
        wxOrderConfigDO.setIsWeighingOrdered(0);
        wxOrderConfigDO.setIsRemarked(0);
        wxOrderConfigDO.setUrlType(0);
        wxOrderConfigDO.setTagNames("tagNames");
        wxOrderConfigDO.setIsOrderOpen(0);
        when(mockWxStoreOrderConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(wxOrderConfigDO);

        when(mockRedisUtils.keyGenerate(BusinessName.WX)).thenReturn("redisKey");
        when(mockRedisUtils.generateGuid("redisKey")).thenReturn("1f430a35-fa15-49b7-b5d0-d5f578674f38");
        when(mockWxQueueConfigService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(mockWxQueueConfigService.save(
                new WxQueueConfigDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        "e376e66c-c804-41ff-9706-1889a9f9afef", "storeGuid", 0, 0, 0, new BigDecimal("0.00"),
                        0))).thenReturn(false);

        // Run the test
        wxStoreInitServiceImplUnderTest.initWxStoreByMQ("storeGuid");
    }
}
