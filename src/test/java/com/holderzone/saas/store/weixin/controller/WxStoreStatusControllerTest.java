package com.holderzone.saas.store.weixin.controller;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreStatusUpdateReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxCouldEditStoreDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreStatusRespDTO;
import com.holderzone.saas.store.weixin.service.WxConfigOverviewService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxStoreStatusController.class)
public class WxStoreStatusControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxConfigOverviewService mockWxConfigOverviewService;

    @Test
    public void testGetWxStoreStatus() throws Exception {
        // Setup
        // Configure WxConfigOverviewService.pageWxStoreStatus(...).
        final WxStoreStatusRespDTO wxStoreStatusRespDTO = new WxStoreStatusRespDTO();
        wxStoreStatusRespDTO.setGuid("aa41021a-0b86-4acf-b336-89c78e9c9796");
        wxStoreStatusRespDTO.setStoreGuid("storeGuid");
        wxStoreStatusRespDTO.setStoreName("storeName");
        wxStoreStatusRespDTO.setBrandNameList(Arrays.asList("value"));
        wxStoreStatusRespDTO.setIsOpened(0);
        final Page<WxStoreStatusRespDTO> wxStoreStatusRespDTOPage = new Page<>(0L, 0L,
                Arrays.asList(wxStoreStatusRespDTO));
        when(mockWxConfigOverviewService.pageWxStoreStatus(WxStorePageReqDTO.builder().build()))
                .thenReturn(wxStoreStatusRespDTOPage);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_status/page_wx_store_status")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateStatusByGuid() throws Exception {
        // Setup
        when(mockWxConfigOverviewService.updateWxStoreStatus(
                new WxStoreStatusUpdateReqDTO("fd78bab0-04f9-468d-b687-e6ebbb6b781e", 0, 0, 0, 0, 0)))
                .thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_status/update_status_by_guid")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateStatusByGuid_WxConfigOverviewServiceReturnsTrue() throws Exception {
        // Setup
        when(mockWxConfigOverviewService.updateWxStoreStatus(
                new WxStoreStatusUpdateReqDTO("fd78bab0-04f9-468d-b687-e6ebbb6b781e", 0, 0, 0, 0, 0))).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_status/update_status_by_guid")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListCouldEditStore() throws Exception {
        // Setup
        // Configure WxConfigOverviewService.listCouldEditStore(...).
        final List<WxCouldEditStoreDTO> wxCouldEditStoreDTOS = Arrays.asList(
                new WxCouldEditStoreDTO("storeGuid", "storeName", Arrays.asList("value")));
        when(mockWxConfigOverviewService.listCouldEditStore(WxStoreReqDTO.builder().build()))
                .thenReturn(wxCouldEditStoreDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_status/list_could_edit_store")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListCouldEditStore_WxConfigOverviewServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockWxConfigOverviewService.listCouldEditStore(WxStoreReqDTO.builder().build()))
                .thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_status/list_could_edit_store")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }
}
