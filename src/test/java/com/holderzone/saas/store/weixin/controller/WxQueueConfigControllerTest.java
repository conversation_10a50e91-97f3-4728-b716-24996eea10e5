package com.holderzone.saas.store.weixin.controller;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.queue.HolderQueueDTO;
import com.holderzone.saas.store.dto.queue.ItemGuidDTO;
import com.holderzone.saas.store.dto.weixin.WxInQueueReqDTO;
import com.holderzone.saas.store.dto.weixin.WxQueueDetailDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxQueueConfigUpdateBatchReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxQueueInfoReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxInQueueRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxQueueConfigDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxTotalQueueDTO;
import com.holderzone.saas.store.weixin.service.WxQueueConfigService;
import com.holderzone.saas.store.weixin.service.WxQueueService;
import com.holderzone.saas.store.weixin.service.WxStoreAuthorizerInfoService;
import me.chanjar.weixin.common.error.WxErrorException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxQueueConfigController.class)
public class WxQueueConfigControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxQueueConfigService mockWxQueueConfigService;
    @MockBean
    private WxQueueService mockWxQueueService;
    @MockBean
    private WxStoreAuthorizerInfoService mockWxStoreAuthorizerInfoService;

    @Test
    public void testPageConfig() throws Exception {
        // Setup
        // Configure WxQueueConfigService.pageQueueConfig(...).
        final Page<WxQueueConfigDTO> wxQueueConfigDTOPage = new Page<>(0L, 0L, Arrays.asList(
                new WxQueueConfigDTO("4aba4c2c-cefc-48c6-aa07-a86e52e6b1e3", 0, new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), 0, false)));
        when(mockWxQueueConfigService.pageQueueConfig(WxStorePageReqDTO.builder().build()))
                .thenReturn(wxQueueConfigDTOPage);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_queue/page_config")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetByGuid() throws Exception {
        // Setup
        // Configure WxQueueConfigService.getQueueConfig(...).
        final WxQueueConfigDTO wxQueueConfigDTO = new WxQueueConfigDTO("4aba4c2c-cefc-48c6-aa07-a86e52e6b1e3", 0,
                new BigDecimal("0.00"), 0, new BigDecimal("0.00"), 0, false);
        when(mockWxQueueConfigService.getQueueConfig(
                new WxQueueConfigDTO("4aba4c2c-cefc-48c6-aa07-a86e52e6b1e3", 0, new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), 0, false))).thenReturn(wxQueueConfigDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_queue/get_by_guid")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateByGuid() throws Exception {
        // Setup
        when(mockWxQueueConfigService.updateQueueConfig(
                new WxQueueConfigDTO("4aba4c2c-cefc-48c6-aa07-a86e52e6b1e3", 0, new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), 0, false))).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_queue/update_by_guid")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateByGuid_WxQueueConfigServiceReturnsTrue() throws Exception {
        // Setup
        when(mockWxQueueConfigService.updateQueueConfig(
                new WxQueueConfigDTO("4aba4c2c-cefc-48c6-aa07-a86e52e6b1e3", 0, new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), 0, false))).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_queue/update_by_guid")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testBatchUpdate() throws Exception {
        // Setup
        when(mockWxQueueConfigService.updateQueueConfigBatch(new WxQueueConfigUpdateBatchReqDTO(Arrays.asList("value"),
                new WxQueueConfigDTO("4aba4c2c-cefc-48c6-aa07-a86e52e6b1e3", 0, new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), 0, false)))).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_queue/batch_update")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testBatchUpdate_WxQueueConfigServiceReturnsTrue() throws Exception {
        // Setup
        when(mockWxQueueConfigService.updateQueueConfigBatch(new WxQueueConfigUpdateBatchReqDTO(Arrays.asList("value"),
                new WxQueueConfigDTO("4aba4c2c-cefc-48c6-aa07-a86e52e6b1e3", 0, new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), 0, false)))).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_queue/batch_update")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetQueueQrCode() throws Exception {
        // Setup
        when(mockWxStoreAuthorizerInfoService.getQueueQrCodeUrl(new WxQueueInfoReqDTO("queueGuid")))
                .thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/wx_queue/get_queue_qr_code")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetQueueQrCode_WxStoreAuthorizerInfoServiceThrowsWxErrorException() throws Exception {
        // Setup
        when(mockWxStoreAuthorizerInfoService.getQueueQrCodeUrl(new WxQueueInfoReqDTO("queueGuid")))
                .thenThrow(WxErrorException.class);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_queue/get_queue_qr_code")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is5xxServerError())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testDetailDTO() throws Exception {
        // Setup
        when(mockWxQueueService.getDetail(WxPortalReqDTO.builder().build()))
                .thenReturn(WxQueueDetailDTO.builder().build());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_queue/detail")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testInQueue() throws Exception {
        // Setup
        when(mockWxQueueService.inQueueByWx(
                new WxInQueueReqDTO(WxStoreConsumerDTO.builder().build(), (byte) 0b0, "storeGuid")))
                .thenReturn(new WxInQueueRespDTO(0, "respMsg"));

        // Run the test and verify the results
        mockMvc.perform(post("/wx_queue/in_queue")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testCallUpNotify() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/wx_queue/call_up_notify")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm WxQueueService.callUpNotify(...).
        final ItemGuidDTO itemGuidDTO = new ItemGuidDTO();
        itemGuidDTO.setItemGuid("itemGuid");
        itemGuidDTO.setBrandGuid("brandGuid");
        itemGuidDTO.setEnterpriseGuid("enterpriseGuid");
        final List<ItemGuidDTO> itemGuidDTOS = Arrays.asList(itemGuidDTO);
        verify(mockWxQueueService).callUpNotify(itemGuidDTOS);
    }

    @Test
    public void testGetTotalDetail() throws Exception {
        // Setup
        // Configure WxQueueService.getTotalDetail(...).
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("409712f2-cd2f-402d-86dc-f875326cdadc");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setLogoUrl("logoUrl");
        holderQueueDTO.setIsEnableRecovery(false);
        final WxTotalQueueDTO wxTotalQueueDTO = new WxTotalQueueDTO("storeGuid", "storeName", "brandGuid", "brandName",
                "brandLogo", Arrays.asList(holderQueueDTO));
        when(mockWxQueueService.getTotalDetail(WxPortalReqDTO.builder().build())).thenReturn(wxTotalQueueDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_queue/total_detail")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
