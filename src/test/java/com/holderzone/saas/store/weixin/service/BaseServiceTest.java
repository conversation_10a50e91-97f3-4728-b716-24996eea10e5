package com.holderzone.saas.store.weixin.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.saas.store.weixin.entity.domain.WxQrRedirectDo;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.mapper.WxQrRedirectMapper;
import me.chanjar.weixin.common.error.WxErrorException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;

@RunWith(SpringJUnit4ClassRunner.class)
//指定测试环境使用的ApplicationContext是WebApplicationContext类型的
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BaseServiceTest {
    @Autowired
    WxUserRecordService wxUserRecordService;

    @Autowired
    WxStoreMpService storeMpService;

    @Test
    public  void testGetOneByOpenId(){
        String openId = "oUiAL02UiL2xBR_d-wwPaesgKGRc_test_1";
        EnterpriseIdentifier.setEnterpriseGuid(ENTERPRISE_GUID);
        WxUserRecordDO oneByOpenId = wxUserRecordService.getOneByOpenId(openId);
        assertEquals(openId,oneByOpenId.getOpenId());

    }

    private final static String  ENTERPRISE_GUID = "6506431195651982337";
    @Test
    public void testInList(){
        List<String> ids = new ArrayList<>();
        EnterpriseIdentifier.setEnterpriseGuid(ENTERPRISE_GUID);
        LambdaQueryWrapper<WxUserRecordDO> in = new LambdaQueryWrapper<WxUserRecordDO>().in(WxUserRecordDO::getGuid, ids);
        List<WxUserRecordDO> list = wxUserRecordService.list(in);
    }

    @Test
    public void test() throws WxErrorException {
        EnterpriseIdentifier.setEnterpriseGuid(ENTERPRISE_GUID);
        String longUrl = "https://cydl1000.oss-cn-hangzhou.aliyuncs.com/cydl-distributor/447/ordering?brandGuid=6730020513080410112&storeGuid=2106281027102330005&areaGuid=6815103237905973248&diningTableGuid=6815103237952110592&tag=1";
        storeMpService.shortenUrl(longUrl,"123",2);
    }

    public static void main(String[] args) {
        String url = "https://cydl1000.oss-cn-hangzhou.aliyuncs.com/cydl-distributor/447/ordering?brandGuid=6730020513080410112&storeGuid=2106281027102330005&areaGuid=6815103237905973248&diningTableGuid=6815103237952110592&tag=1";
        JSONObject json = new JSONObject();
        String paramsSting = url.substring(url.indexOf("?") + 1);
        String[] split = paramsSting.split("&");
        for(String param : split){
            String[] keyValue = param.split("=");
            json.put(keyValue[0],keyValue[1]);
        }
    }
}
