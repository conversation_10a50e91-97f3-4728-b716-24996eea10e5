package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.saas.store.dto.weixin.WxMemberOverviewModelDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.service.WxOrderRecordService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class DiningMemberModelItemServiceImplTest {

    @Mock
    private WxOrderRecordService mockWxOrderRecordService;

    @InjectMocks
    private DiningMemberModelItemServiceImpl diningMemberModelItemServiceImplUnderTest;

    @Test
    public void testGetWxMemberOverviewModel() {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder().build();
        final WxMemberOverviewModelDTO expectedResult = WxMemberOverviewModelDTO.builder()
                .modelId(0)
                .modelName("我的就餐")
                .modelCount(0)
                .build();

        // Run the test
        final WxMemberOverviewModelDTO result = diningMemberModelItemServiceImplUnderTest.getWxMemberOverviewModel(
                wxStoreConsumerDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
