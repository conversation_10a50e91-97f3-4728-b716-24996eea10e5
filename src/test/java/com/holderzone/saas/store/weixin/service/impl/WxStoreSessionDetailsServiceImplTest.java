package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberInfoDTO;
import com.holderzone.holder.saas.member.wechat.dto.member.*;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.face.WeChatPayReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.BrandStoreDetailDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.req.WxH5PayReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPrepayReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.weixin.config.ResponseModel;
import com.holderzone.saas.store.weixin.execption.CorrectResult;
import com.holderzone.saas.store.weixin.service.WxStoreOrderConfigService;
import com.holderzone.saas.store.weixin.service.rpc.*;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberTerminalClientService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStoreSessionDetailsServiceImplTest {

    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private WxStoreTableClientService mockWxStoreTableClientService;
    @Mock
    private MemberTerminalClientService mockMemberTerminalClientService;
    @Mock
    private WxStoreDineInOrderClientService mockWxStoreDineInOrderClientService;
    @Mock
    private WxStoreDineInBillClientService mockWxStoreDineInBillClientService;
    @Mock
    private WxStoreOrderConfigService mockWxStoreOrderConfigService;
    @Mock
    private OrganizationClientService mockOrganizationClientService;
    @Mock
    private EnterpriseClientService mockEnterpriseClientService;
    @Mock
    private HsaBaseClientService mockHsaBaseClientService;

    private WxStoreSessionDetailsServiceImpl wxStoreSessionDetailsServiceImplUnderTest;

    @Before
    public void setUp() {
        wxStoreSessionDetailsServiceImplUnderTest = new WxStoreSessionDetailsServiceImpl(mockRedisUtils,
                mockWxStoreTableClientService, mockMemberTerminalClientService, mockWxStoreDineInOrderClientService,
                mockWxStoreDineInBillClientService, mockWxStoreOrderConfigService, mockOrganizationClientService,
                mockEnterpriseClientService, mockHsaBaseClientService);
    }

    @Test
    public void testSaveOrderGuid() {
        // Setup
        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.saveOrderGuid("key", "orderGuid");

        // Verify the results
        verify(mockRedisUtils).hPut("orderGuid", "key", "orderGuid");
    }

    @Test
    public void testDelOrderGuid() {
        // Setup
        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.delOrderGuid("key");

        // Verify the results
        verify(mockRedisUtils).hDelete("orderGuid", "key");
    }

    @Test
    public void testGetOrderGuid1() {
        // Setup
        when(mockRedisUtils.hGet("orderGuid", "key")).thenReturn("result");

        // Run the test
        final String result = wxStoreSessionDetailsServiceImplUnderTest.getOrderGuid("key");

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testSaveFastOrderGuid() {
        // Setup
        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.saveFastOrderGuid("storeGuid", "openId", "orderGuid");

        // Verify the results
        verify(mockRedisUtils).hPut("key", "openId", "orderGuid");
    }

    @Test
    public void testGetFastOrderGuid() {
        // Setup
        when(mockRedisUtils.hGet("key", "openId")).thenReturn("result");

        // Run the test
        final String result = wxStoreSessionDetailsServiceImplUnderTest.getFastOrderGuid("storeGuid", "openId");

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testSaveOrderState() {
        // Setup
        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.saveOrderState("tableGuid", 0);

        // Verify the results
        verify(mockRedisUtils).hPut("orderState", "tableGuid", 0);
        verify(mockRedisUtils).persist("orderState");
    }

    @Test
    public void testGetOrderState() {
        // Setup
        when(mockRedisUtils.hGet("orderState", "tableGuid")).thenReturn("result");

        // Run the test
        final Integer result = wxStoreSessionDetailsServiceImplUnderTest.getOrderState("tableGuid");

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testDelOrderState() {
        // Setup
        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.delOrderState("tableGuid");

        // Verify the results
        verify(mockRedisUtils).hDelete("orderState", "tableGuid");
    }

    @Test
    public void testUpdateMerchantBatchGuid() {
        // Setup
        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.updateMerchantBatchGuid("key", "merchatBatchGuid");

        // Verify the results
        verify(mockRedisUtils).hPut("merchantOrderGuid", "key", "merchatBatchGuid");
        verify(mockRedisUtils).persist("merchantOrderGuid");
    }

    @Test
    public void testGetMerchantBatchGuid() {
        // Setup
        when(mockRedisUtils.hGet("merchantOrderGuid", "key")).thenReturn("result");

        // Run the test
        final String result = wxStoreSessionDetailsServiceImplUnderTest.getMerchantBatchGuid("key");

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testDelMerchantBatchGuid() {
        // Setup
        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.delMerchantBatchGuid("key");

        // Verify the results
        verify(mockRedisUtils).hDelete("merchantOrderGuid", "key");
    }

    @Test
    public void testDelDinnerGuestsCount() {
        // Setup
        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.delDinnerGuestsCount("key");

        // Verify the results
        verify(mockRedisUtils).hDelete("guestsCount", "key");
    }

    @Test
    public void testUpdateDinnerGuestsCount() {
        // Setup
        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.updateDinnerGuestsCount("tableGuid", 0);

        // Verify the results
        verify(mockRedisUtils).hPut("guestsCount", "tableGuid", 0);
    }

    @Test
    public void testGetDinnerGuestsCount() {
        // Setup
        when(mockRedisUtils.hGet("guestsCount", "key")).thenReturn("result");

        // Run the test
        final Integer result = wxStoreSessionDetailsServiceImplUnderTest.getDinnerGuestsCount("key");

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testSavePaidUser() {
        // Setup
        final WxH5PayReqDTO wxH5PayReqDTO = WxH5PayReqDTO.builder().build();

        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.savePaidUser("storeGuid", "key", wxH5PayReqDTO);

        // Verify the results
        verify(mockRedisUtils).hPut("key", "key", WxH5PayReqDTO.builder().build());
    }

    @Test
    public void testDelPaidUser() {
        // Setup
        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.delPaidUser("storeGuid", "key");

        // Verify the results
        verify(mockRedisUtils).hDelete("key", "key");
    }

    @Test
    public void testGetPaidUser() {
        // Setup
        final WxH5PayReqDTO expectedResult = WxH5PayReqDTO.builder().build();
        when(mockRedisUtils.hGet("key", "key")).thenReturn("result");

        // Run the test
        final WxH5PayReqDTO result = wxStoreSessionDetailsServiceImplUnderTest.getPaidUser("storeGuid", "key");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testSaveTablePaidUser() {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .diningTableGuid("diningTableGuid")
                .build();

        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.saveTablePaidUser(wxStoreConsumerDTO);

        // Verify the results
        verify(mockRedisUtils).hPut("tablePaidDetails:", "diningTableGuid", WxStoreConsumerDTO.builder()
                .openId("openId")
                .diningTableGuid("diningTableGuid")
                .build());
    }

    @Test
    public void testDelTablePaidUser() {
        // Setup
        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.delTablePaidUser("tableGuid");

        // Verify the results
        verify(mockRedisUtils).hDelete("tablePaidUser", "tableGuid");
    }

    @Test
    public void testGetTablePaidUser() {
        // Setup
        final WxStoreConsumerDTO expectedResult = WxStoreConsumerDTO.builder()
                .openId("openId")
                .diningTableGuid("diningTableGuid")
                .build();
        when(mockRedisUtils.hGet("tablePaidDetails:", "tableGuid")).thenReturn("result");

        // Run the test
        final WxStoreConsumerDTO result = wxStoreSessionDetailsServiceImplUnderTest.getTablePaidUser("tableGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetBrandInfo() {
        // Setup
        final WxStoreConsumerDTO expectedResult = WxStoreConsumerDTO.builder()
                .openId("openId")
                .diningTableGuid("diningTableGuid")
                .build();
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WxStoreConsumerDTO result = wxStoreSessionDetailsServiceImplUnderTest.getBrandInfo("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testSaveFirstPerson() {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .diningTableGuid("diningTableGuid")
                .build();

        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.saveFirstPerson(wxStoreConsumerDTO);

        // Verify the results
        verify(mockRedisUtils).set("key", WxStoreConsumerDTO.builder()
                .openId("openId")
                .diningTableGuid("diningTableGuid")
                .build());
    }

    @Test
    public void testDelFirstPerson() {
        // Setup
        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.delFirstPerson("openId");

        // Verify the results
        verify(mockRedisUtils).delete("key");
    }

    @Test
    public void testGetFirstPerson() {
        // Setup
        final WxStoreConsumerDTO expectedResult = WxStoreConsumerDTO.builder()
                .openId("openId")
                .diningTableGuid("diningTableGuid")
                .build();
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WxStoreConsumerDTO result = wxStoreSessionDetailsServiceImplUnderTest.getFirstPerson("opendId");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testSaveTableToken() {
        // Setup
        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.saveTableToken("tableGuid");

        // Verify the results
        verify(mockRedisUtils).hPut("tableToken:", "tableGuid", "tableGuid");
    }

    @Test
    public void testDelTableToken() {
        // Setup
        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.delTableToken("tableGuid");

        // Verify the results
        verify(mockRedisUtils).hDelete("tableToken:", "tableGuid");
    }

    @Test
    public void testGetTableToken() {
        // Setup
        when(mockRedisUtils.hGet("tableToken:", "tableGuid")).thenReturn("result");

        // Run the test
        final String result = wxStoreSessionDetailsServiceImplUnderTest.getTableToken("tableGuid");

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testSavePrepay() {
        // Setup
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .storeGuid("storeGuid")
                .openId("openId")
                .build();

        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.savePrepay(wxPrepayReqDTO);

        // Verify the results
        verify(mockRedisUtils).hPut("key", "openId", WxPrepayReqDTO.builder()
                .storeGuid("storeGuid")
                .openId("openId")
                .build());
    }

    @Test
    public void testDelPrepay() {
        // Setup
        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.delPrepay("storeGuid", "openId");

        // Verify the results
        verify(mockRedisUtils).hDelete("key", "openId");
    }

    @Test
    public void testGetPrepay() {
        // Setup
        final WxPrepayReqDTO expectedResult = WxPrepayReqDTO.builder()
                .storeGuid("storeGuid")
                .openId("openId")
                .build();
        when(mockRedisUtils.hGet("key", "openId")).thenReturn("result");

        // Run the test
        final WxPrepayReqDTO result = wxStoreSessionDetailsServiceImplUnderTest.getPrepay("storeGuid", "openId");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetMemberInfoAndCardList() {
        // Setup
        final ResponseMemberAndCardInfoDTO expectedResult = new ResponseMemberAndCardInfoDTO();
        final ResponseMemberInfoDTO memberInfoDTO = new ResponseMemberInfoDTO();
        memberInfoDTO.setMemberQRCode("memberQRCode");
        memberInfoDTO.setMemberInfoGuid("memberInfoGuid");
        memberInfoDTO.setPhoneNum("phoneNum");
        memberInfoDTO.setNickName("nickName");
        expectedResult.setMemberInfoDTO(memberInfoDTO);

        // Configure MemberTerminalClientService.getMemberInfoAndCard(...).
        final ResponseMemberAndCardInfoDTO responseMemberAndCardInfoDTO = new ResponseMemberAndCardInfoDTO();
        final ResponseMemberInfoDTO memberInfoDTO1 = new ResponseMemberInfoDTO();
        memberInfoDTO1.setMemberQRCode("memberQRCode");
        memberInfoDTO1.setMemberInfoGuid("memberInfoGuid");
        memberInfoDTO1.setPhoneNum("phoneNum");
        memberInfoDTO1.setNickName("nickName");
        responseMemberAndCardInfoDTO.setMemberInfoDTO(memberInfoDTO1);
        final RequestQueryStoreAndMemberAndCard requestQueryStoreAndMemberAndCard = new RequestQueryStoreAndMemberAndCard();
        requestQueryStoreAndMemberAndCard.setPhoneNumOrCardNum("openId");
        requestQueryStoreAndMemberAndCard.setStoreGuid("storeGuid");
        requestQueryStoreAndMemberAndCard.setEnterpriseGuid("enterpriseGuid");
        requestQueryStoreAndMemberAndCard.setIsCurrentStoreCard(0);
        requestQueryStoreAndMemberAndCard.setCheckExpireGuid("checkExpireGuid");
        when(mockMemberTerminalClientService.getMemberInfoAndCard(requestQueryStoreAndMemberAndCard))
                .thenReturn(responseMemberAndCardInfoDTO);

        // Run the test
        final ResponseMemberAndCardInfoDTO result = wxStoreSessionDetailsServiceImplUnderTest.getMemberInfoAndCardList(
                "enterpriseGuid", "storeGuid", "openId");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testOrderDetails() {
        // Setup
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("2ccd6e2f-1c2a-47dc-ba69-4f93190ed731");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("4c72a50f-9221-4e99-a419-dd36b65f318e");
        dineinOrderDetailRespDTO.setReserveItems(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setState(0);
        final CorrectResult<DineinOrderDetailRespDTO> expectedResult = new CorrectResult<>("message", 0,
                dineinOrderDetailRespDTO);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("2ccd6e2f-1c2a-47dc-ba69-4f93190ed731");
        dineinOrderDetailRespDTO1.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("4c72a50f-9221-4e99-a419-dd36b65f318e");
        dineinOrderDetailRespDTO1.setReserveItems(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO1.setState(0);
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("orderGuid")
                .build())).thenReturn(dineinOrderDetailRespDTO1);

        // Run the test
        final CorrectResult<DineinOrderDetailRespDTO> result = wxStoreSessionDetailsServiceImplUnderTest.orderDetails(
                "orderGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCalculateDetails() {
        // Setup
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("2ccd6e2f-1c2a-47dc-ba69-4f93190ed731");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("4c72a50f-9221-4e99-a419-dd36b65f318e");
        dineinOrderDetailRespDTO.setReserveItems(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setState(0);
        final CorrectResult<DineinOrderDetailRespDTO> expectedResult = new CorrectResult<>("message", 0,
                dineinOrderDetailRespDTO);

        // Configure WxStoreDineInBillClientService.calculate(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("2ccd6e2f-1c2a-47dc-ba69-4f93190ed731");
        dineinOrderDetailRespDTO1.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("4c72a50f-9221-4e99-a419-dd36b65f318e");
        dineinOrderDetailRespDTO1.setReserveItems(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO1.setState(0);
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setDeviceId("openId");
        billCalculateReqDTO.setEnterpriseGuid("enterpriseGuid");
        billCalculateReqDTO.setStoreGuid("storeGuid");
        billCalculateReqDTO.setOrderGuid("orderGuid");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setMemberIntegral(0);
        billCalculateReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO.setMemberPhone("memberPhone");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVerify(0);
        when(mockWxStoreDineInBillClientService.calculate(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO1);

        // Run the test
        final CorrectResult<DineinOrderDetailRespDTO> result = wxStoreSessionDetailsServiceImplUnderTest.calculateDetails(
                "orderGuid", "openId", "memberInfoCardGuid", 0, "volumeCode");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCalculateDetails2() {
        // Setup
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("2ccd6e2f-1c2a-47dc-ba69-4f93190ed731");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("4c72a50f-9221-4e99-a419-dd36b65f318e");
        dineinOrderDetailRespDTO.setReserveItems(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setState(0);
        final CorrectResult<DineinOrderDetailRespDTO> expectedResult = new CorrectResult<>("message", 0,
                dineinOrderDetailRespDTO);

        // Configure WxStoreDineInBillClientService.calculate(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("2ccd6e2f-1c2a-47dc-ba69-4f93190ed731");
        dineinOrderDetailRespDTO1.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("4c72a50f-9221-4e99-a419-dd36b65f318e");
        dineinOrderDetailRespDTO1.setReserveItems(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO1.setState(0);
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setDeviceId("openId");
        billCalculateReqDTO.setEnterpriseGuid("enterpriseGuid");
        billCalculateReqDTO.setStoreGuid("storeGuid");
        billCalculateReqDTO.setOrderGuid("orderGuid");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setMemberIntegral(0);
        billCalculateReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO.setMemberPhone("memberPhone");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVerify(0);
        when(mockWxStoreDineInBillClientService.calculate(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO1);

        // Run the test
        final CorrectResult<DineinOrderDetailRespDTO> result = wxStoreSessionDetailsServiceImplUnderTest.calculateDetails2(
                "orderGuid", "openId", "memberInfoCardGuid", 0, "volumeCode");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetOrderGuid2() {
        // Setup
        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("cac5be34-7808-4bd2-9e7f-73165a72b5bc");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        when(mockWxStoreTableClientService.getOrderGuid("key")).thenReturn("result");
        when(mockRedisUtils.hGet("orderGuid", "key")).thenReturn("result");

        // Run the test
        final String result = wxStoreSessionDetailsServiceImplUnderTest.getOrderGuid("storeGuid", "key", "openId");

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testGetMemberInfo() {
        // Setup
        final ResponseMemberInfo expectedResult = new ResponseMemberInfo();
        expectedResult.setQrcode("qrcode");
        expectedResult.setMemberInfoGuid("memberInfoGuid");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setPhoneNum("phoneNum");

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setMemberInfoGuid("memberInfoGuid");
        responseMemberInfo.setOperSubjectGuid("operSubjectGuid");
        responseMemberInfo.setEnterpriseGuid("enterpriseGuid");
        responseMemberInfo.setPhoneNum("phoneNum");
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Run the test
        final ResponseMemberInfo result = wxStoreSessionDetailsServiceImplUnderTest.getMemberInfo("enterpriseGuid",
                "openId");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetMemberCard() {
        // Setup
        final ResponseMemberCardAll expectedResult = new ResponseMemberCardAll();
        final ResponseMemberCardListOwned responseMemberCardListOwned = new ResponseMemberCardListOwned();
        responseMemberCardListOwned.setCardQRCode("cardQRCode");
        responseMemberCardListOwned.setCardGuid("cardGuid");
        responseMemberCardListOwned.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCardListOwned.setSystemManagementGuid("systemManagementGuid");
        expectedResult.setOpenedCardList(Arrays.asList(responseMemberCardListOwned));

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setMemberInfoGuid("memberInfoGuid");
        responseMemberInfo.setOperSubjectGuid("operSubjectGuid");
        responseMemberInfo.setEnterpriseGuid("enterpriseGuid");
        responseMemberInfo.setPhoneNum("phoneNum");
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Configure HsaBaseClientService.getMemberCard(...).
        final ResponseMemberCardAll responseMemberCardAll = new ResponseMemberCardAll();
        final ResponseMemberCardListOwned responseMemberCardListOwned1 = new ResponseMemberCardListOwned();
        responseMemberCardListOwned1.setCardQRCode("cardQRCode");
        responseMemberCardListOwned1.setCardGuid("cardGuid");
        responseMemberCardListOwned1.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCardListOwned1.setSystemManagementGuid("systemManagementGuid");
        responseMemberCardAll.setOpenedCardList(Arrays.asList(responseMemberCardListOwned1));
        final ResponseModel<ResponseMemberCardAll> responseMemberCardAllResponseModel = new ResponseModel<>(
                responseMemberCardAll);
        final RequestQueryMemberCardList memberCardListQueryReqDTO = new RequestQueryMemberCardList();
        memberCardListQueryReqDTO.setMemberInfoGuid("memberInfoGuid");
        memberCardListQueryReqDTO.setEnterpriseGuid("enterpriseGuid");
        memberCardListQueryReqDTO.setBrandGuid("brandGuid");
        memberCardListQueryReqDTO.setStoreGuid("storeGuid");
        when(mockHsaBaseClientService.getMemberCard(memberCardListQueryReqDTO))
                .thenReturn(responseMemberCardAllResponseModel);

        // Run the test
        final ResponseMemberCardAll result = wxStoreSessionDetailsServiceImplUnderTest.getMemberCard("enterpriseGuid",
                "brandGuid", "openId");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetMemberValidVolumeNumber() {
        // Setup
        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setMemberInfoGuid("memberInfoGuid");
        responseMemberInfo.setOperSubjectGuid("operSubjectGuid");
        responseMemberInfo.setEnterpriseGuid("enterpriseGuid");
        responseMemberInfo.setPhoneNum("phoneNum");
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Configure HsaBaseClientService.getMemberValidVolumeNumber(...).
        final RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO = new RequestMemberInfoVolumeQuery();
        memberInfoVolumeQueryReqDTO.setMemberInfoGuid("memberInfoGuid");
        memberInfoVolumeQueryReqDTO.setVolumeType(0);
        memberInfoVolumeQueryReqDTO.setStoreGuid("");
        memberInfoVolumeQueryReqDTO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVolumeQueryReqDTO.setBrandGuid("brandGuid");
        memberInfoVolumeQueryReqDTO.setMayUseVolume(0);
        when(mockHsaBaseClientService.getMemberValidVolumeNumber(memberInfoVolumeQueryReqDTO))
                .thenReturn(new ResponseModel<>(0));

        // Run the test
        final Integer result = wxStoreSessionDetailsServiceImplUnderTest.getMemberValidVolumeNumber("enterpriseGuid",
                "brandGuid", "openId");

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testGetBrandDetail() {
        // Setup
        // Configure OrganizationClientService.queryBrandByGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("a3745145-6656-4cc6-b0c9-d44150ebd3ed");
        brandDTO.setUuid("1e87f0bb-0f04-48dd-b125-5e05047029fa");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockOrganizationClientService.queryBrandByGuid("brandGuid")).thenReturn(brandDTO);

        // Run the test
        final BrandDTO result = wxStoreSessionDetailsServiceImplUnderTest.getBrandDetail("brandGuid");

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testGetBrandDetail_OrganizationClientServiceReturnsNull() {
        // Setup
        when(mockOrganizationClientService.queryBrandByGuid("brandGuid")).thenReturn(null);

        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.getBrandDetail("brandGuid");
    }

    @Test
    public void testGetTableDetail() {
        // Setup
        final TableDTO expectedResult = TableDTO.builder().build();
        when(mockWxStoreTableClientService.getTableByGuid("tableGuid")).thenReturn(TableDTO.builder().build());

        // Run the test
        final TableDTO result = wxStoreSessionDetailsServiceImplUnderTest.getTableDetail("tableGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = BusinessException.class)
    public void testGetTableDetail_WxStoreTableClientServiceReturnsNull() {
        // Setup
        when(mockWxStoreTableClientService.getTableByGuid("tableGuid")).thenReturn(null);

        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.getTableDetail("tableGuid");
    }

    @Test
    public void testGetStoreDetail() {
        // Setup
        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("6075d890-4004-4a0a-995c-79a8b7c53ae0");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Run the test
        final StoreDTO result = wxStoreSessionDetailsServiceImplUnderTest.getStoreDetail("storeGuid");

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testGetStoreDetail_OrganizationClientServiceReturnsNull() {
        // Setup
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(null);

        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.getStoreDetail("storeGuid");
    }

    @Test
    public void testGetBrandInfoDetails() {
        // Setup
        // Configure OrganizationClientService.queryBrandByStoreGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("a3745145-6656-4cc6-b0c9-d44150ebd3ed");
        brandDTO.setUuid("1e87f0bb-0f04-48dd-b125-5e05047029fa");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockOrganizationClientService.queryBrandByStoreGuid("storeGuid")).thenReturn(brandDTO);

        // Run the test
        final BrandDTO result = wxStoreSessionDetailsServiceImplUnderTest.getBrandInfoDetails("storeGuid");

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testGetBrandInfoDetails_OrganizationClientServiceReturnsNull() {
        // Setup
        when(mockOrganizationClientService.queryBrandByStoreGuid("storeGuid")).thenReturn(null);

        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.getBrandInfoDetails("storeGuid");
    }

    @Test
    public void testGetEnterpriseDetail() {
        // Setup
        final EnterpriseDTO expectedResult = new EnterpriseDTO();
        expectedResult.setId(0L);
        expectedResult.setUid("uid");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setName("name");
        expectedResult.setDbServer(0);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure EnterpriseClientService.findEnterprise(...).
        final EnterpriseDTO enterpriseDTO = new EnterpriseDTO();
        enterpriseDTO.setId(0L);
        enterpriseDTO.setUid("uid");
        enterpriseDTO.setEnterpriseGuid("enterpriseGuid");
        enterpriseDTO.setName("name");
        enterpriseDTO.setDbServer(0);
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("openId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockEnterpriseClientService.findEnterprise(baseDTO)).thenReturn(enterpriseDTO);

        // Run the test
        final EnterpriseDTO result = wxStoreSessionDetailsServiceImplUnderTest.getEnterpriseDetail();

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm RedisUtils.set(...).
        final EnterpriseDTO value = new EnterpriseDTO();
        value.setId(0L);
        value.setUid("uid");
        value.setEnterpriseGuid("enterpriseGuid");
        value.setName("name");
        value.setDbServer(0);
        verify(mockRedisUtils).set("key", value);
    }

    @Test
    public void testSavePayCallBack() {
        // Setup
        final WeChatPayReqDTO weChatPayReqDTO = new WeChatPayReqDTO();
        weChatPayReqDTO.setDeviceId("openId");
        weChatPayReqDTO.setEnterpriseGuid("enterpriseGuid");
        weChatPayReqDTO.setStoreGuid("storeGuid");
        weChatPayReqDTO.setOrderGuid("orderGuid");
        weChatPayReqDTO.setPayGuid("payGuid");

        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.savePayCallBack("orderGuid", weChatPayReqDTO);

        // Verify the results
        // Confirm RedisUtils.set(...).
        final WeChatPayReqDTO value = new WeChatPayReqDTO();
        value.setDeviceId("openId");
        value.setEnterpriseGuid("enterpriseGuid");
        value.setStoreGuid("storeGuid");
        value.setOrderGuid("orderGuid");
        value.setPayGuid("payGuid");
        verify(mockRedisUtils).set("key", value);
    }

    @Test
    public void testDelPayCallBack() {
        // Setup
        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.delPayCallBack("orderGuid");

        // Verify the results
        verify(mockRedisUtils).delete("key");
    }

    @Test
    public void testGetPayCallBack() {
        // Setup
        final WeChatPayReqDTO expectedResult = new WeChatPayReqDTO();
        expectedResult.setDeviceId("openId");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setPayGuid("payGuid");

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WeChatPayReqDTO result = wxStoreSessionDetailsServiceImplUnderTest.getPayCallBack("orderGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetStoreBrandDetail() {
        // Setup
        final BrandStoreDetailDTO expectedResult = new BrandStoreDetailDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setBrandGuid("brandGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setBrandName("brandName");
        expectedResult.setBrandLogoUrl("brandLogoUrl");

        // Configure OrganizationClientService.queryStoreBrandDetail(...).
        final BrandStoreDetailDTO brandStoreDetailDTO = new BrandStoreDetailDTO();
        brandStoreDetailDTO.setStoreGuid("storeGuid");
        brandStoreDetailDTO.setBrandGuid("brandGuid");
        brandStoreDetailDTO.setStoreName("storeName");
        brandStoreDetailDTO.setBrandName("brandName");
        brandStoreDetailDTO.setBrandLogoUrl("brandLogoUrl");
        when(mockOrganizationClientService.queryStoreBrandDetail("storeGuid", "brandGuid"))
                .thenReturn(brandStoreDetailDTO);

        // Run the test
        final BrandStoreDetailDTO result = wxStoreSessionDetailsServiceImplUnderTest.getStoreBrandDetail("storeGuid",
                "brandGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = BusinessException.class)
    public void testGetStoreBrandDetail_OrganizationClientServiceReturnsNull() {
        // Setup
        when(mockOrganizationClientService.queryStoreBrandDetail("storeGuid", "brandGuid")).thenReturn(null);

        // Run the test
        wxStoreSessionDetailsServiceImplUnderTest.getStoreBrandDetail("storeGuid", "brandGuid");
    }
}
