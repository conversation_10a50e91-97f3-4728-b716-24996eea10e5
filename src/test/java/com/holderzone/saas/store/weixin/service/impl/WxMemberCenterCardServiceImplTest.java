package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberCardAll;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberCardListOwned;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberCardListUnowned;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.weixin.WxMemberCenterCardRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.config.ResponseModel;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxMemberCenterCardServiceImplTest {

    @Mock
    private MemberClientService mockMemberClientService;
    @Mock
    private RedisTemplate mockRedisTemplate;
    @Mock
    private WxStoreSessionDetailsService mockWxStoreSessionDetailsService;
    @Mock
    private HsaBaseClientService mockHsaBaseClientService;

    private WxMemberCenterCardServiceImpl wxMemberCenterCardServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxMemberCenterCardServiceImplUnderTest = new WxMemberCenterCardServiceImpl(mockMemberClientService,
                mockRedisTemplate, mockWxStoreSessionDetailsService, mockHsaBaseClientService);
    }

    @Test
    public void testCardList() {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .isLogin(false)
                .memberInfoGuid("memberInfoGuid")
                .build();
        final WxMemberCenterCardRespDTO expectedResult = WxMemberCenterCardRespDTO.builder()
                .memberCardList(Arrays.asList(new ResponseMemberCardListOwned()))
                .ownCard(false)
                .build();

        // Configure WxStoreSessionDetailsService.getMemberCard(...).
        final ResponseMemberCardAll responseMemberCardAll = new ResponseMemberCardAll();
        final ResponseMemberCardListOwned responseMemberCardListOwned = new ResponseMemberCardListOwned();
        responseMemberCardListOwned.setCardGuid("cardGuid");
        responseMemberCardListOwned.setCardName("cardName");
        responseMemberCardListOwned.setCardLogo("logoUrl");
        responseMemberCardListOwned.setCardIcon("cardIcon");
        responseMemberCardListOwned.setCardLevelName("");
        responseMemberCardListOwned.setCardLevelNum(0);
        responseMemberCardAll.setOpenedCardList(Arrays.asList(responseMemberCardListOwned));
        final ResponseMemberCardListUnowned responseMemberCardListUnowned = new ResponseMemberCardListUnowned();
        responseMemberCardListUnowned.setCardGuid("cardGuid");
        responseMemberCardListUnowned.setCardName("cardName");
        responseMemberCardAll.setNonactivatedCardList(Arrays.asList(responseMemberCardListUnowned));
        when(mockWxStoreSessionDetailsService.getMemberCard("enterpriseGuid", "brandGuid", "openId"))
                .thenReturn(responseMemberCardAll);

        // Configure WxStoreSessionDetailsService.getBrandDetail(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("b73392da-1fb8-49ed-a91b-2db8096a8e4c");
        brandDTO.setUuid("1339e5c2-334c-4bee-8bae-b74eab6bcaee");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockWxStoreSessionDetailsService.getBrandDetail("brandGuid")).thenReturn(brandDTO);

        // Configure HsaBaseClientService.getDefaultCard(...).
        final ResponseMemberCardListOwned responseMemberCardListOwned1 = new ResponseMemberCardListOwned();
        responseMemberCardListOwned1.setCardGuid("cardGuid");
        responseMemberCardListOwned1.setCardName("cardName");
        responseMemberCardListOwned1.setCardLogo("logoUrl");
        responseMemberCardListOwned1.setCardIcon("cardIcon");
        responseMemberCardListOwned1.setCardLevelName("");
        responseMemberCardListOwned1.setCardLevelNum(0);
        final ResponseModel<ResponseMemberCardListOwned> responseMemberCardListOwnedResponseModel = new ResponseModel<>(
                responseMemberCardListOwned1);
        when(mockHsaBaseClientService.getDefaultCard("memberInfoGuid"))
                .thenReturn(responseMemberCardListOwnedResponseModel);

        // Run the test
        final WxMemberCenterCardRespDTO result = wxMemberCenterCardServiceImplUnderTest.cardList(wxStoreConsumerDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCardList_WxStoreSessionDetailsServiceGetMemberCardReturnsNull() {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .isLogin(false)
                .memberInfoGuid("memberInfoGuid")
                .build();
        final WxMemberCenterCardRespDTO expectedResult = WxMemberCenterCardRespDTO.builder()
                .memberCardList(Arrays.asList(new ResponseMemberCardListOwned()))
                .ownCard(false)
                .build();
        when(mockWxStoreSessionDetailsService.getMemberCard("enterpriseGuid", "brandGuid", "openId")).thenReturn(null);

        // Run the test
        final WxMemberCenterCardRespDTO result = wxMemberCenterCardServiceImplUnderTest.cardList(wxStoreConsumerDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
