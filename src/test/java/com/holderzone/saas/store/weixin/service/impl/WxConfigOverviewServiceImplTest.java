package com.holderzone.saas.store.weixin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.weixin.common.BusinessName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.weixin.common.WxStoreInfoDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreStatusUpdateReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxCouldEditStoreDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreStatusRespDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxConfigOverviewDO;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderConfigDO;
import com.holderzone.saas.store.weixin.entity.domain.WxQueueConfigDO;
import com.holderzone.saas.store.weixin.mapstruct.WxConfigOverviewMapstruct;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreConfigMapstruct;
import com.holderzone.saas.store.weixin.service.WxOrganizationService;
import com.holderzone.saas.store.weixin.service.WxQueueConfigService;
import com.holderzone.saas.store.weixin.service.WxStoreInitService;
import com.holderzone.saas.store.weixin.service.WxStoreOrderConfigService;
import com.holderzone.saas.store.weixin.service.rpc.OrganizationClientService;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxConfigOverviewServiceImplTest {

    @Mock
    private WxOrganizationService mockWxOrganizationService;
    @Mock
    private WxConfigOverviewMapstruct mockWxConfigOverviewMapstruct;
    @Mock
    private WxStoreInitService mockWxStoreInitService;
    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private OrganizationClientService mockOrganizationClientService;
    @Mock
    private WxQueueConfigService mockWxQueueConfigService;
    @Mock
    private WxStoreConfigMapstruct mockWxStoreConfigMapstruct;
    @Mock
    private WxStoreOrderConfigService mockWxStoreOrderConfigService;

    @InjectMocks
    private WxConfigOverviewServiceImpl wxConfigOverviewServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxConfigOverviewServiceImplUnderTest.wxOrganizationService = mockWxOrganizationService;
        wxConfigOverviewServiceImplUnderTest.wxConfigOverviewMapstruct = mockWxConfigOverviewMapstruct;
        wxConfigOverviewServiceImplUnderTest.wxStoreInitService = mockWxStoreInitService;
        wxConfigOverviewServiceImplUnderTest.redisUtils = mockRedisUtils;
        wxConfigOverviewServiceImplUnderTest.organizationClientService = mockOrganizationClientService;
        wxConfigOverviewServiceImplUnderTest.wxQueueConfigService = mockWxQueueConfigService;
        wxConfigOverviewServiceImplUnderTest.wxStoreConfigMapstruct = mockWxStoreConfigMapstruct;
    }

    @Test
    public void testPageWxStoreStatus() {
        // Setup
        final WxStorePageReqDTO wxStorePageReqDTO = WxStorePageReqDTO.builder()
                .storeName("storeName")
                .build();

        // Configure WxOrganizationService.getStoreConfig(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("916c3803-2491-4633-abde-992d7c6c1c6c");
        brandDTO.setName("name");
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("storeGuid");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("916c3803-2491-4633-abde-992d7c6c1c6c");
        brandDTO1.setName("name");
        storeDTO1.setBrandDTOList(Arrays.asList(brandDTO1));
        final Triple<Page<StoreDTO>, List<StoreDTO>, List<String>> pageListListTriple = Triple.of(
                new Page<>(0L, 0L, Arrays.asList(storeDTO)), Arrays.asList(storeDTO1), Arrays.asList("value"));
        when(mockWxOrganizationService.getStoreConfig(WxStorePageReqDTO.builder()
                .storeName("storeName")
                .build())).thenReturn(pageListListTriple);

        when(mockRedisUtils.keyGenerate(BusinessName.WX)).thenReturn("redisKey");
        when(mockRedisUtils.generateGuid("redisKey")).thenReturn("7b878ebe-ba0b-45c3-8045-ba7bb0510750");

        // Configure WxConfigOverviewMapstruct.wxConfigOverViewDO2DTO(...).
        final WxStoreStatusRespDTO wxStoreStatusRespDTO = new WxStoreStatusRespDTO();
        wxStoreStatusRespDTO.setGuid("f19309ee-bfb3-4211-abbc-673cd9be4567");
        wxStoreStatusRespDTO.setStoreGuid("storeGuid");
        wxStoreStatusRespDTO.setStoreName("name");
        wxStoreStatusRespDTO.setBrandNameList(Arrays.asList("value"));
        wxStoreStatusRespDTO.setIsOpened(0);
        when(mockWxConfigOverviewMapstruct.wxConfigOverViewDO2DTO(
                new WxConfigOverviewDO(0L, "7b878ebe-ba0b-45c3-8045-ba7bb0510750",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid", 0, 0,
                        0, 0, 0))).thenReturn(wxStoreStatusRespDTO);

        // Run the test
        final Page<WxStoreStatusRespDTO> result = wxConfigOverviewServiceImplUnderTest.pageWxStoreStatus(
                wxStorePageReqDTO);

        // Verify the results
        verify(mockWxStoreInitService).initWxStore(Arrays.asList("value"));
    }

    @Test
    public void testUpdateWxStoreStatus() {
        // Setup
        final WxStoreStatusUpdateReqDTO wxStoreStatusUpdateReqDTO = new WxStoreStatusUpdateReqDTO(
                "f8b22bf0-90eb-40d0-b3e3-394d93574918", 0, 0, 0, 0, 0);

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("916c3803-2491-4633-abde-992d7c6c1c6c");
        brandDTO.setName("name");
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure WxConfigOverviewMapstruct.wxConfigOverviewDTO2DO(...).
        final WxConfigOverviewDO wxConfigOverviewDO = new WxConfigOverviewDO(0L, "7b878ebe-ba0b-45c3-8045-ba7bb0510750",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid", 0, 0, 0, 0,
                0);
        when(mockWxConfigOverviewMapstruct.wxConfigOverviewDTO2DO(
                new WxStoreStatusUpdateReqDTO("f8b22bf0-90eb-40d0-b3e3-394d93574918", 0, 0, 0, 0, 0)))
                .thenReturn(wxConfigOverviewDO);

        // Configure WxStoreOrderConfigService.getOne(...).
        final WxOrderConfigDO wxOrderConfigDO = new WxOrderConfigDO();
        wxOrderConfigDO.setId(0L);
        wxOrderConfigDO.setGuid("894e5c8b-4771-4d49-935c-75d536ab15b1");
        wxOrderConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderConfigDO.setStoreGuid("storeGuid");
        when(mockWxStoreOrderConfigService.getOne(any(LambdaQueryWrapper.class), eq(false)))
                .thenReturn(wxOrderConfigDO);

        // Configure WxStoreConfigMapstruct.orderConfigDO2OrderConfigRespDTO(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setStoreGuid("storeGuid");
        wxOrderConfigDTO.setStoreName("name");
        wxOrderConfigDTO.setIsOpened(false);
        wxOrderConfigDTO.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO.setGuid("08f6c479-a1c3-4c36-a72e-525b7251fd5c");
        final WxOrderConfigDO wxOrderConfigDO1 = new WxOrderConfigDO();
        wxOrderConfigDO1.setId(0L);
        wxOrderConfigDO1.setGuid("894e5c8b-4771-4d49-935c-75d536ab15b1");
        wxOrderConfigDO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderConfigDO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderConfigDO1.setStoreGuid("storeGuid");
        when(mockWxStoreConfigMapstruct.orderConfigDO2OrderConfigRespDTO(wxOrderConfigDO1))
                .thenReturn(wxOrderConfigDTO);

        when(mockRedisUtils.keyGenerate(BusinessName.WX_STORE_CONFIG)).thenReturn("key");

        // Run the test
        final boolean result = wxConfigOverviewServiceImplUnderTest.updateWxStoreStatus(wxStoreStatusUpdateReqDTO);

        // Verify the results
        assertFalse(result);

        // Confirm RedisUtils.setEx(...).
        final WxOrderConfigDTO value = new WxOrderConfigDTO();
        value.setStoreGuid("storeGuid");
        value.setStoreName("name");
        value.setIsOpened(false);
        value.setBrandNameList(Arrays.asList("value"));
        value.setGuid("08f6c479-a1c3-4c36-a72e-525b7251fd5c");
        verify(mockRedisUtils).setEx("key", value, 24L, TimeUnit.HOURS);
    }

    @Test(expected = BusinessException.class)
    public void testUpdateWxStoreStatus_OrganizationClientServiceReturnsNull() {
        // Setup
        final WxStoreStatusUpdateReqDTO wxStoreStatusUpdateReqDTO = new WxStoreStatusUpdateReqDTO(
                "f8b22bf0-90eb-40d0-b3e3-394d93574918", 0, 0, 0, 0, 0);
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(null);

        // Run the test
        wxConfigOverviewServiceImplUnderTest.updateWxStoreStatus(wxStoreStatusUpdateReqDTO);
    }

    @Test
    public void testCouldEdit() {
        // Setup
        // Configure WxOrganizationService.getStoreDTOByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("916c3803-2491-4633-abde-992d7c6c1c6c");
        brandDTO.setName("name");
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        when(mockWxOrganizationService.getStoreDTOByGuid("storeGuid")).thenReturn(storeDTO);

        // Run the test
        wxConfigOverviewServiceImplUnderTest.couldEdit(Arrays.asList("value"), 0);

        // Verify the results
    }

    @Test
    public void testListByStoreGuidList() {
        // Setup
        final WxStoreStatusRespDTO wxStoreStatusRespDTO = new WxStoreStatusRespDTO();
        wxStoreStatusRespDTO.setGuid("f19309ee-bfb3-4211-abbc-673cd9be4567");
        wxStoreStatusRespDTO.setStoreGuid("storeGuid");
        wxStoreStatusRespDTO.setStoreName("name");
        wxStoreStatusRespDTO.setBrandNameList(Arrays.asList("value"));
        wxStoreStatusRespDTO.setIsOpened(0);
        final List<WxStoreStatusRespDTO> expectedResult = Arrays.asList(wxStoreStatusRespDTO);

        // Configure OrganizationClientService.queryStoreByIdList(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("916c3803-2491-4633-abde-992d7c6c1c6c");
        brandDTO.setName("name");
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        final List<StoreDTO> storeDTOS = Arrays.asList(storeDTO);
        when(mockOrganizationClientService.queryStoreByIdList(Arrays.asList("value"))).thenReturn(storeDTOS);

        when(mockRedisUtils.keyGenerate(BusinessName.WX)).thenReturn("redisKey");
        when(mockRedisUtils.generateGuid("redisKey")).thenReturn("7b878ebe-ba0b-45c3-8045-ba7bb0510750");

        // Configure WxConfigOverviewMapstruct.wxConfigOverViewDO2DTO(...).
        final WxStoreStatusRespDTO wxStoreStatusRespDTO1 = new WxStoreStatusRespDTO();
        wxStoreStatusRespDTO1.setGuid("f19309ee-bfb3-4211-abbc-673cd9be4567");
        wxStoreStatusRespDTO1.setStoreGuid("storeGuid");
        wxStoreStatusRespDTO1.setStoreName("name");
        wxStoreStatusRespDTO1.setBrandNameList(Arrays.asList("value"));
        wxStoreStatusRespDTO1.setIsOpened(0);
        when(mockWxConfigOverviewMapstruct.wxConfigOverViewDO2DTO(
                new WxConfigOverviewDO(0L, "7b878ebe-ba0b-45c3-8045-ba7bb0510750",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid", 0, 0,
                        0, 0, 0))).thenReturn(wxStoreStatusRespDTO1);

        // Run the test
        final List<WxStoreStatusRespDTO> result = wxConfigOverviewServiceImplUnderTest.listByStoreGuidList(
                Arrays.asList("value"));

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreInitService).initWxStore(Arrays.asList("value"));
    }

    @Test
    public void testListByStoreGuidList_OrganizationClientServiceReturnsNoItems() {
        // Setup
        when(mockOrganizationClientService.queryStoreByIdList(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());
        when(mockRedisUtils.keyGenerate(BusinessName.WX)).thenReturn("redisKey");
        when(mockRedisUtils.generateGuid("redisKey")).thenReturn("7b878ebe-ba0b-45c3-8045-ba7bb0510750");

        // Configure WxConfigOverviewMapstruct.wxConfigOverViewDO2DTO(...).
        final WxStoreStatusRespDTO wxStoreStatusRespDTO = new WxStoreStatusRespDTO();
        wxStoreStatusRespDTO.setGuid("f19309ee-bfb3-4211-abbc-673cd9be4567");
        wxStoreStatusRespDTO.setStoreGuid("storeGuid");
        wxStoreStatusRespDTO.setStoreName("name");
        wxStoreStatusRespDTO.setBrandNameList(Arrays.asList("value"));
        wxStoreStatusRespDTO.setIsOpened(0);
        when(mockWxConfigOverviewMapstruct.wxConfigOverViewDO2DTO(
                new WxConfigOverviewDO(0L, "7b878ebe-ba0b-45c3-8045-ba7bb0510750",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid", 0, 0,
                        0, 0, 0))).thenReturn(wxStoreStatusRespDTO);

        // Run the test
        final List<WxStoreStatusRespDTO> result = wxConfigOverviewServiceImplUnderTest.listByStoreGuidList(
                Arrays.asList("value"));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
        verify(mockWxStoreInitService).initWxStore(Arrays.asList("value"));
    }

    @Test
    public void testListCouldEditStore() {
        // Setup
        final WxStoreReqDTO wxStoreReqDTO = WxStoreReqDTO.builder()
                .storeName("storeName")
                .type(0)
                .build();
        final List<WxCouldEditStoreDTO> expectedResult = Arrays.asList(
                new WxCouldEditStoreDTO("storeGuid", "name", Arrays.asList("value")));

        // Configure WxOrganizationService.getStoreConfig(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("916c3803-2491-4633-abde-992d7c6c1c6c");
        brandDTO.setName("name");
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("storeGuid");
        storeDTO1.setName("name");
        storeDTO1.setBelongBrandGuid("belongBrandGuid");
        storeDTO1.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO1.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setGuid("916c3803-2491-4633-abde-992d7c6c1c6c");
        brandDTO1.setName("name");
        storeDTO1.setBrandDTOList(Arrays.asList(brandDTO1));
        final Triple<Page<StoreDTO>, List<StoreDTO>, List<String>> pageListListTriple = Triple.of(
                new Page<>(0L, 0L, Arrays.asList(storeDTO)), Arrays.asList(storeDTO1), Arrays.asList("value"));
        when(mockWxOrganizationService.getStoreConfig(WxStorePageReqDTO.builder()
                .storeName("storeName")
                .build())).thenReturn(pageListListTriple);

        // Run the test
        final List<WxCouldEditStoreDTO> result = wxConfigOverviewServiceImplUnderTest.listCouldEditStore(wxStoreReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testIsStoreOpen() {
        // Setup
        final Pair<WxStoreInfoDTO, Boolean> expectedResult = Pair.of(
                new WxStoreInfoDTO("storeGuid", "name", "tel", "address", false, 0, new BigDecimal("0.00"),
                        new BigDecimal("0.00"), Arrays.asList("value")), false);

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("storeGuid");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBusinessStart(LocalTime.of(0, 0, 0));
        storeDTO.setBusinessEnd(LocalTime.of(0, 0, 0));
        storeDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("916c3803-2491-4633-abde-992d7c6c1c6c");
        brandDTO.setName("name");
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Run the test
        final Pair<WxStoreInfoDTO, Boolean> result = wxConfigOverviewServiceImplUnderTest.isStoreOpen(0, "storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testInitWxStore() {
        // Setup
        when(mockRedisUtils.keyGenerate(BusinessName.WX)).thenReturn("redisKey");
        when(mockRedisUtils.generateGuid("redisKey")).thenReturn("7b878ebe-ba0b-45c3-8045-ba7bb0510750");

        // Run the test
        wxConfigOverviewServiceImplUnderTest.initWxStore("storeGuid");

        // Verify the results
        verify(mockWxStoreInitService).initWxStoreByMQ("storeGuid");
    }

    @Test
    public void testWhetherQueueConfig() {
        // Setup
        // Configure WxQueueConfigService.getOne(...).
        final WxQueueConfigDO wxQueueConfigDO = new WxQueueConfigDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "bfad460d-d92d-4630-9d33-f3b95b909f65", "storeGuid", 0, 0, 0,
                new BigDecimal("0.00"), 0);
        when(mockWxQueueConfigService.getOne("storeGuid")).thenReturn(wxQueueConfigDO);

        // Run the test
        final boolean result = wxConfigOverviewServiceImplUnderTest.whetherQueueConfig("storeGuid");

        // Verify the results
        assertFalse(result);
    }
}
