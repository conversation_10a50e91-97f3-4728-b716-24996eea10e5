package com.holderzone.saas.store.weixin.service.impl;

import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import org.junit.Before;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;

public class WxMpMessageHandlerImplTest {

    private WxMpMessageHandlerImpl wxMpMessageHandlerImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxMpMessageHandlerImplUnderTest = new WxMpMessageHandlerImpl();
    }


    @Test
    public void testHandle() throws Exception {
        // Setup
        final WxMpXmlMessage wxMpXmlMessage = new WxMpXmlMessage();
        wxMpXmlMessage.setMsgType("msgType");
        wxMpXmlMessage.setToUser("fromUserName");
        wxMpXmlMessage.setFromUser("fromUser");
        wxMpXmlMessage.setContent("感谢您的关注！");
        wxMpXmlMessage.setMediaId("mediaId");
        wxMpXmlMessage.setEvent("event");
        wxMpXmlMessage.setLatitude(0.0);
        wxMpXmlMessage.setLongitude(0.0);

        final Map<String, Object> map = new HashMap<>();
        final WxMpXmlOutMessage expectedResult = null;

        // Run the test
        final WxMpXmlOutMessage result = wxMpMessageHandlerImplUnderTest.handle(wxMpXmlMessage, map, null, null);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test(expected = WxErrorException.class)
    public void testHandle_ThrowsWxErrorException() throws Exception {
        // Setup
        final WxMpXmlMessage wxMpXmlMessage = new WxMpXmlMessage();
        wxMpXmlMessage.setMsgType("msgType");
        wxMpXmlMessage.setToUser("fromUserName");
        wxMpXmlMessage.setFromUser("fromUser");
        wxMpXmlMessage.setContent("感谢您的关注！");
        wxMpXmlMessage.setMediaId("mediaId");
        wxMpXmlMessage.setEvent("event");
        wxMpXmlMessage.setLatitude(0.0);
        wxMpXmlMessage.setLongitude(0.0);

        final Map<String, Object> map = new HashMap<>();

        // Run the test
        wxMpMessageHandlerImplUnderTest.handle(wxMpXmlMessage, map, null, null);
    }
}
