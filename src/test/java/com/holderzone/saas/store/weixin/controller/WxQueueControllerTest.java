package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.queue.*;
import com.holderzone.saas.store.weixin.service.WxQueueService;
import com.holderzone.saas.store.weixin.service.rpc.QueueClientService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxQueueController.class)
public class WxQueueControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxQueueService mockWxQueueService;
    @MockBean
    private QueueClientService mockQueueClientService;

    @Test
    public void testQueryByGuid() throws Exception {
        // Setup
        // Configure QueueClientService.queryByUser(...).
        final List<WxQueueListDTO> wxQueueListDTOS = Arrays.asList(WxQueueListDTO.builder()
                .time("time")
                .build());
        when(mockQueueClientService.queryByUser(QueueWechatDTO.builder().build())).thenReturn(wxQueueListDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_queue/query_by_guid")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryByGuid_QueueClientServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockQueueClientService.queryByUser(QueueWechatDTO.builder().build())).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_queue/query_by_guid")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testGetQueueDetails() throws Exception {
        // Setup
        // Configure QueueClientService.getQueueDetails(...).
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("a898b242-6587-4748-aea5-fd8a789bfcbb");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setIsEnableRecovery(false);
        holderQueueDTO.setRecoveryNum(0);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setQueueGuid("queueGuid");
        holderQueueItemDetailDTO.setQueueCode("queueCode");
        holderQueueItemDetailDTO.setSort(0);
        holderQueueItemDetailDTO.setCode("code");
        holderQueueItemDetailDTO.setStatus((byte) 0b0);
        final HolderQueueQueueRecordDTO holderQueueQueueRecordDTO = new HolderQueueQueueRecordDTO(holderQueueDTO,
                holderQueueItemDetailDTO);
        final ItemGuidDTO itemGuidDTO = new ItemGuidDTO();
        itemGuidDTO.setItemGuid("itemGuid");
        itemGuidDTO.setBrandGuid("brandGuid");
        itemGuidDTO.setEnterpriseGuid("enterpriseGuid");
        when(mockQueueClientService.getQueueDetails(itemGuidDTO)).thenReturn(holderQueueQueueRecordDTO);

        // Configure QueueClientService.query(...).
        final StoreConfigDTO storeConfigDTO = new StoreConfigDTO();
        storeConfigDTO.setGuid("236e5cd9-063d-4064-8e71-14b337616eea");
        storeConfigDTO.setStoreGuid("storeGuid");
        storeConfigDTO.setIsEnableEat(false);
        storeConfigDTO.setIsEnableRecovery(false);
        storeConfigDTO.setRecoveryNum("recoveryNum");
        when(mockQueueClientService.query("storeGuid")).thenReturn(storeConfigDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_queue/query_detail")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetQueueDetails_QueueClientServiceGetQueueDetailsReturnsNull() throws Exception {
        // Setup
        // Configure QueueClientService.getQueueDetails(...).
        final ItemGuidDTO itemGuidDTO = new ItemGuidDTO();
        itemGuidDTO.setItemGuid("itemGuid");
        itemGuidDTO.setBrandGuid("brandGuid");
        itemGuidDTO.setEnterpriseGuid("enterpriseGuid");
        when(mockQueueClientService.getQueueDetails(itemGuidDTO)).thenReturn(null);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_queue/query_detail")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("", true));
    }

    @Test
    public void testCancelQueue() throws Exception {
        // Setup
        when(mockQueueClientService.cancel("queueGuid", "enterpriseGuid")).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(get("/wx_queue/update_cancel_queue")
                        .param("queueGuid", "queueGuid")
                        .param("enterpriseGuid", "enterpriseGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testCancelQueue_QueueClientServiceReturnsTrue() throws Exception {
        // Setup
        when(mockQueueClientService.cancel("queueGuid", "enterpriseGuid")).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(get("/wx_queue/update_cancel_queue")
                        .param("queueGuid", "queueGuid")
                        .param("enterpriseGuid", "enterpriseGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryQueueStatusNum() throws Exception {
        // Setup
        when(mockWxQueueService.queryQueueStatusNum(QueueWechatDTO.builder().build())).thenReturn(new HashMap<>());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_queue/groupingByQueueStatus")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
