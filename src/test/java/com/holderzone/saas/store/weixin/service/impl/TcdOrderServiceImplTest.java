package com.holderzone.saas.store.weixin.service.impl;

import com.google.common.util.concurrent.MoreExecutors;
import com.holderzone.saas.store.dto.order.inside.OrderGuidsDTO;
import com.holderzone.saas.store.dto.trade.OrderDetailPushMqDTO;
import com.holderzone.saas.store.weixin.config.OverallConfig;
import com.holderzone.saas.store.weixin.service.rpc.TradeClientService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TcdOrderServiceImplTest {

    @Mock
    private TradeClientService mockTradeClientService;
    @Mock
    private OverallConfig mockOverallConfig;

    private TcdOrderServiceImpl tcdOrderServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        tcdOrderServiceImplUnderTest = new TcdOrderServiceImpl(MoreExecutors.directExecutor(), mockTradeClientService,
                mockOverallConfig);
    }

    @Test
    public void testAsyncOrder() {
        // Setup
        // Configure TradeClientService.findByWxOrderGuid(...).
        final OrderDetailPushMqDTO orderDetailPushMqDTO = new OrderDetailPushMqDTO();
        orderDetailPushMqDTO.setEnterpriseGuid("enterpriseGuid");
        orderDetailPushMqDTO.setPhone("phone");
        orderDetailPushMqDTO.setOpenId("openId");
        orderDetailPushMqDTO.setOperSubjectGuid("operSubjectGuid");
        orderDetailPushMqDTO.setOrderRecordGuid("orderRecordGuid");
        when(mockTradeClientService.findByWxOrderGuid(new OrderGuidsDTO(Arrays.asList("value"))))
                .thenReturn(orderDetailPushMqDTO);

        when(mockOverallConfig.getPushOrderDetailUrl()).thenReturn("result");

        // Run the test
        tcdOrderServiceImplUnderTest.asyncOrder("orderGuid");

        // Verify the results
    }
}
