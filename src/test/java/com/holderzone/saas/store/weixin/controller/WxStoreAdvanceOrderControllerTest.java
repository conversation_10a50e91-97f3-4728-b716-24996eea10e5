package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceEstimateDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceOrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceOrderPriceDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreItemRespDTO;
import com.holderzone.saas.store.weixin.service.WxStoreAdvanceOrderService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxStoreAdvanceOrderController.class)
public class WxStoreAdvanceOrderControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStoreAdvanceOrderService mockWxStoreAdvanceOrderService;

    @Test
    public void testCreateAdvanceOrder() throws Exception {
        // Setup
        // Configure WxStoreAdvanceOrderService.createAdvanceOrder(...).
        final WxStoreAdvanceEstimateDTO wxStoreAdvanceEstimateDTO = WxStoreAdvanceEstimateDTO.builder().build();
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder().build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreItemRespDTO.setItemGuid("itemGuid");
        wxStoreItemRespDTO.setTypeGuid("typeGuid");
        wxStoreItemRespDTO.setTypeName("typeName");
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));
        when(mockWxStoreAdvanceOrderService.createAdvanceOrder(wxStoreAdvanceOrderDTO))
                .thenReturn(wxStoreAdvanceEstimateDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_advance_order_provide/add")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateAdvanceOrderRemark() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_advance_order_provide/update_remark")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockWxStoreAdvanceOrderService).updateAdvanceOrderRemark(WxStoreAdvanceConsumerReqDTO.builder().build());
    }

    @Test
    public void testDelAdvanceOrder() throws Exception {
        // Setup
        when(mockWxStoreAdvanceOrderService.delAdvanceOrder(WxStoreAdvanceConsumerReqDTO.builder().build()))
                .thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_advance_order_provide/del")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testDelAdvanceOrder_WxStoreAdvanceOrderServiceReturnsTrue() throws Exception {
        // Setup
        when(mockWxStoreAdvanceOrderService.delAdvanceOrder(WxStoreAdvanceConsumerReqDTO.builder().build()))
                .thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_advance_order_provide/del")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetPersonWxStoreAdvanceOrder() throws Exception {
        // Setup
        // Configure WxStoreAdvanceOrderService.getPersonWxStoreAdvanceOrder(...).
        final WxStoreAdvanceOrderPriceDTO wxStoreAdvanceOrderPriceDTO = new WxStoreAdvanceOrderPriceDTO();
        wxStoreAdvanceOrderPriceDTO.setAdvanceGuid("advanceGuid");
        wxStoreAdvanceOrderPriceDTO.setTradeOrderGuid("tradeOrderGuid");
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder().build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));
        wxStoreAdvanceOrderPriceDTO.setWxStoreAdvanceOrderDTOS(Arrays.asList(wxStoreAdvanceOrderDTO));
        when(mockWxStoreAdvanceOrderService.getPersonWxStoreAdvanceOrder(
                WxStoreAdvanceConsumerReqDTO.builder().build())).thenReturn(wxStoreAdvanceOrderPriceDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_advance_order_provide/advance")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetTableWxStoreAdvanceOrder() throws Exception {
        // Setup
        // Configure WxStoreAdvanceOrderService.getTableWxStoreAdvanceOrder(...).
        final WxStoreAdvanceOrderPriceDTO wxStoreAdvanceOrderPriceDTO = new WxStoreAdvanceOrderPriceDTO();
        wxStoreAdvanceOrderPriceDTO.setAdvanceGuid("advanceGuid");
        wxStoreAdvanceOrderPriceDTO.setTradeOrderGuid("tradeOrderGuid");
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        wxStoreAdvanceOrderDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder().build());
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));
        wxStoreAdvanceOrderPriceDTO.setWxStoreAdvanceOrderDTOS(Arrays.asList(wxStoreAdvanceOrderDTO));
        when(mockWxStoreAdvanceOrderService.getTableWxStoreAdvanceOrder(
                WxStoreAdvanceConsumerReqDTO.builder().build())).thenReturn(wxStoreAdvanceOrderPriceDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_advance_order_provide/table")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
