package com.holderzone.saas.store.weixin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.base.dto.file.FileDto;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.table.TableTagDTO;
import com.holderzone.saas.store.dto.weixin.MultiMemberDTO;
import com.holderzone.saas.store.dto.weixin.WxStickDownloadTableDTO;
import com.holderzone.saas.store.dto.weixin.WxTableStickDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickDownloadReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickIsModelDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickModelReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickDownloadRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickModelRespDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxConfigOverviewDO;
import com.holderzone.saas.store.weixin.entity.domain.WxQrRedirectDo;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreTableStickDO;
import com.holderzone.saas.store.weixin.entity.query.WxQrCodeUrlQuery;
import com.holderzone.saas.store.weixin.mapper.WxQrRedirectMapper;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreTableStickMapstruct;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.rpc.BaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.EnterpriseClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStickModelClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreTableClientService;
import com.holderzone.saas.store.weixin.utils.PdfUtil;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.io.input.BrokenInputStream;
import org.apache.commons.io.input.NullInputStream;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class WxStoreTableStickServiceImplTest {

    @Mock
    private WxStoreTableStickMapstruct mockWxStoreTableStickMapstruct;
    @Mock
    private WxStickModelClientService mockWxStickModelClientService;
    @Mock
    private WxStoreAuthorizerInfoService mockWxStoreAuthorizerInfoService;
    @Mock
    private BaseClientService mockBaseClientService;
    @Mock
    private EnterpriseClientService mockEnterpriseClientService;
    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private PdfUtil mockPdfUtil;
    @Mock
    private WxStoreMpService mockWxStoreMpService;
    @Mock
    private WxStoreOrderConfigService mockWxStoreOrderConfigService;
    @Mock
    private WxConfigOverviewService mockWxConfigOverviewService;
    @Mock
    private WxSaasMpService mockWxSaasMpService;
    @Mock
    private WxStoreTableClientService mockWxStoreTableClientService;
    @Mock
    private OssClient mockOssClient;
    @Mock
    private WxQrRedirectMapper mockWxQrRedirectMapper;

    @InjectMocks
    private WxStoreTableStickServiceImpl wxStoreTableStickServiceImplUnderTest;

    @Before
    public void setUp() {
        wxStoreTableStickServiceImplUnderTest.wxStoreTableStickMapstruct = mockWxStoreTableStickMapstruct;
        wxStoreTableStickServiceImplUnderTest.wxStickModelClientService = mockWxStickModelClientService;
        wxStoreTableStickServiceImplUnderTest.wxStoreAuthorizerInfoService = mockWxStoreAuthorizerInfoService;
        wxStoreTableStickServiceImplUnderTest.baseClientService = mockBaseClientService;
        wxStoreTableStickServiceImplUnderTest.enterpriseClientService = mockEnterpriseClientService;
        wxStoreTableStickServiceImplUnderTest.redisUtils = mockRedisUtils;
        wxStoreTableStickServiceImplUnderTest.pdfUtil = mockPdfUtil;
        wxStoreTableStickServiceImplUnderTest.wxStoreMpService = mockWxStoreMpService;
        wxStoreTableStickServiceImplUnderTest.wxStoreOrderConfigService = mockWxStoreOrderConfigService;
        wxStoreTableStickServiceImplUnderTest.wxConfigOverviewService = mockWxConfigOverviewService;
        wxStoreTableStickServiceImplUnderTest.wxSaasMpService = mockWxSaasMpService;
        wxStoreTableStickServiceImplUnderTest.wxStoreTableClientService = mockWxStoreTableClientService;
        wxStoreTableStickServiceImplUnderTest.wxQrRedirectMapper = mockWxQrRedirectMapper;
    }

    @Test
    public void testHandleStickMessage() {
        // Setup
        final List<WxTableStickDTO> wxTableStickDTOList = Arrays.asList(
                new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                        "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                        "isDelete", 0L, 0L, "categoryName", "previewImg", 0));

        // Configure WxStoreTableStickMapstruct.tableStickDTO2DO(...).
        final WxStoreTableStickDO wxStoreTableStickDO = new WxStoreTableStickDO(0L,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                "6b363ff4-53f1-4878-890a-c301f873048c", "6b363ff4-53f1-4878-890a-c301f873048c", "name", "categoryGuid",
                new BigDecimal("0.00"), new BigDecimal("0.00"), "isRecommended", "backColor", 0.0, 0.0, "unit", "bgUrl",
                "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                "sponsorsTextColor", "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow",
                "tableNumberText", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                0, "categoryName", "previewImg", 0);
        when(mockWxStoreTableStickMapstruct.tableStickDTO2DO(
                new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                        "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                        "isDelete", 0L, 0L, "categoryName", "previewImg", 0))).thenReturn(wxStoreTableStickDO);

        when(mockRedisUtils.generateGuid("redisKey")).thenReturn("6b363ff4-53f1-4878-890a-c301f873048c");

        // Configure WxStoreTableStickMapstruct.tableStickDTOList2DOList(...).
        final List<WxStoreTableStickDO> wxStoreTableStickDOS = Arrays.asList(
                new WxStoreTableStickDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), "6b363ff4-53f1-4878-890a-c301f873048c",
                        "6b363ff4-53f1-4878-890a-c301f873048c", "name", "categoryGuid", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "backColor", 0.0, 0.0, "unit", "bgUrl", "backImage",
                        "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape", "storeNameText",
                        "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText", "qrCodeTextColor",
                        "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor", "sponsorsTextColor",
                        "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow", "tableNumberText",
                        "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable", 0,
                        "categoryName", "previewImg", 0));
        when(mockWxStoreTableStickMapstruct.tableStickDTOList2DOList(Arrays.asList(
                new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                        "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                        "isDelete", 0L, 0L, "categoryName", "previewImg", 0)))).thenReturn(wxStoreTableStickDOS);

        // Run the test
        wxStoreTableStickServiceImplUnderTest.handleStickMessage(wxTableStickDTOList, "messageType");

        // Verify the results
    }

    @Test
    public void testHandleStickMessage_WxStoreTableStickMapstructTableStickDTOList2DOListReturnsNoItems() {
        // Setup
        final List<WxTableStickDTO> wxTableStickDTOList = Arrays.asList(
                new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                        "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                        "isDelete", 0L, 0L, "categoryName", "previewImg", 0));
        when(mockWxStoreTableStickMapstruct.tableStickDTOList2DOList(Arrays.asList(
                new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                        "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                        "isDelete", 0L, 0L, "categoryName", "previewImg", 0)))).thenReturn(Collections.emptyList());

        // Run the test
        wxStoreTableStickServiceImplUnderTest.handleStickMessage(wxTableStickDTOList, "messageType");

        // Verify the results
    }

    @Test
    public void testListTableStick() {
        // Setup
        final List<WxTableStickDTO> expectedResult = Arrays.asList(
                new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                        "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                        "isDelete", 0L, 0L, "categoryName", "previewImg", 0));

        // Configure WxStoreTableStickMapstruct.stickDOList2DTOList(...).
        final List<WxTableStickDTO> wxTableStickDTOS = Arrays.asList(
                new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                        "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                        "isDelete", 0L, 0L, "categoryName", "previewImg", 0));
        when(mockWxStoreTableStickMapstruct.stickDOList2DTOList(Arrays.asList(
                new WxStoreTableStickDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), "6b363ff4-53f1-4878-890a-c301f873048c",
                        "6b363ff4-53f1-4878-890a-c301f873048c", "name", "categoryGuid", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "backColor", 0.0, 0.0, "unit", "bgUrl", "backImage",
                        "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape", "storeNameText",
                        "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText", "qrCodeTextColor",
                        "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor", "sponsorsTextColor",
                        "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow", "tableNumberText",
                        "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable", 0,
                        "categoryName", "previewImg", 0)))).thenReturn(wxTableStickDTOS);

        // Run the test
        final List<WxTableStickDTO> result = wxStoreTableStickServiceImplUnderTest.listTableStick(0);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testListTableStick_WxStoreTableStickMapstructReturnsNoItems() {
        // Setup
        when(mockWxStoreTableStickMapstruct.stickDOList2DTOList(Arrays.asList(
                new WxStoreTableStickDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), "6b363ff4-53f1-4878-890a-c301f873048c",
                        "6b363ff4-53f1-4878-890a-c301f873048c", "name", "categoryGuid", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "backColor", 0.0, 0.0, "unit", "bgUrl", "backImage",
                        "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape", "storeNameText",
                        "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText", "qrCodeTextColor",
                        "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor", "sponsorsTextColor",
                        "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow", "tableNumberText",
                        "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable", 0,
                        "categoryName", "previewImg", 0)))).thenReturn(Collections.emptyList());

        // Run the test
        final List<WxTableStickDTO> result = wxStoreTableStickServiceImplUnderTest.listTableStick(0);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testAddOrUpdateStick() {
        // Setup
        final WxTableStickDTO wxTableStickDTO = new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category",
                new BigDecimal("0.00"), new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit",
                "bgUrl", "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                "isDelete", 0L, 0L, "categoryName", "previewImg", 0);

        // Configure WxStoreTableStickMapstruct.tableStickDTO2DO(...).
        final WxStoreTableStickDO wxStoreTableStickDO = new WxStoreTableStickDO(0L,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                "6b363ff4-53f1-4878-890a-c301f873048c", "6b363ff4-53f1-4878-890a-c301f873048c", "name", "categoryGuid",
                new BigDecimal("0.00"), new BigDecimal("0.00"), "isRecommended", "backColor", 0.0, 0.0, "unit", "bgUrl",
                "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                "sponsorsTextColor", "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow",
                "tableNumberText", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                0, "categoryName", "previewImg", 0);
        when(mockWxStoreTableStickMapstruct.tableStickDTO2DO(
                new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                        "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                        "isDelete", 0L, 0L, "categoryName", "previewImg", 0))).thenReturn(wxStoreTableStickDO);

        when(mockRedisUtils.generateGuid("redisKey")).thenReturn("6b363ff4-53f1-4878-890a-c301f873048c");
        when(mockPdfUtil.createPreviewImage(
                new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                        "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                        "isDelete", 0L, 0L, "categoryName", "previewImg", 0), "imageName")).thenReturn("fileContent");
        when(mockBaseClientService.upload(any(FileDto.class))).thenReturn("previewImg");

        // Run the test
        final boolean result = wxStoreTableStickServiceImplUnderTest.addOrUpdateStick(wxTableStickDTO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testFindByGuid() {
        // Setup
        final WxTableStickDTO expectedResult = new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category",
                new BigDecimal("0.00"), new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit",
                "bgUrl", "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                "isDelete", 0L, 0L, "categoryName", "previewImg", 0);

        // Configure WxStoreTableStickMapstruct.stickDO2stickDTO(...).
        final WxTableStickDTO wxTableStickDTO = new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category",
                new BigDecimal("0.00"), new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit",
                "bgUrl", "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                "isDelete", 0L, 0L, "categoryName", "previewImg", 0);
        when(mockWxStoreTableStickMapstruct.stickDO2stickDTO(
                new WxStoreTableStickDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), "6b363ff4-53f1-4878-890a-c301f873048c",
                        "6b363ff4-53f1-4878-890a-c301f873048c", "name", "categoryGuid", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "backColor", 0.0, 0.0, "unit", "bgUrl", "backImage",
                        "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape", "storeNameText",
                        "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText", "qrCodeTextColor",
                        "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor", "sponsorsTextColor",
                        "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow", "tableNumberText",
                        "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable", 0,
                        "categoryName", "previewImg", 0))).thenReturn(wxTableStickDTO);

        // Run the test
        final WxTableStickDTO result = wxStoreTableStickServiceImplUnderTest.findByGuid(
                "5746127d-480d-42a5-8bbe-ac1157579ba2");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testListStickModel() {
        // Setup
        final WxStickModelReqDTO wxStickModelReqDTO = new WxStickModelReqDTO(Arrays.asList("value"), 0, "category");
        final List<WxStickModelRespDTO> expectedResult = Arrays.asList(
                new WxStickModelRespDTO("a45b1a51-e578-49c3-aacf-23e397f2c707", "name", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "previewImg", 0));

        // Configure WxStickModelClientService.getTableStickList(...).
        final List<WxTableStickDTO> wxTableStickDTOS = Arrays.asList(
                new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                        "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                        "isDelete", 0L, 0L, "categoryName", "previewImg", 0));
        when(mockWxStickModelClientService.getTableStickList(
                new WxStickModelReqDTO(Arrays.asList("value"), 0, "category"))).thenReturn(wxTableStickDTOS);

        // Run the test
        final List<WxStickModelRespDTO> result = wxStoreTableStickServiceImplUnderTest.listStickModel(
                wxStickModelReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testListStickModel_WxStickModelClientServiceReturnsNoItems() {
        // Setup
        final WxStickModelReqDTO wxStickModelReqDTO = new WxStickModelReqDTO(Arrays.asList("value"), 0, "category");
        when(mockWxStickModelClientService.getTableStickList(
                new WxStickModelReqDTO(Arrays.asList("value"), 0, "category"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<WxStickModelRespDTO> result = wxStoreTableStickServiceImplUnderTest.listStickModel(
                wxStickModelReqDTO);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testDownloadOssStickZip() {
        // Setup
        when(mockRedisUtils.get("downloadKey")).thenReturn("result");

        // Run the test
        final String result = wxStoreTableStickServiceImplUnderTest.downloadOssStickZip("downloadKey");

        // Verify the results
        assertEquals("result", result);
        verify(mockRedisUtils).delete("downloadKey");
    }

    @Test
    public void testDownloadStickZip() {
        // Setup
        final WxStickDownloadRespDTO expectedResult = new WxStickDownloadRespDTO("storeName", "content".getBytes(),
                "exception");
        when(mockRedisUtils.get("downloadKey")).thenReturn("result");

        // Run the test
        final WxStickDownloadRespDTO result = wxStoreTableStickServiceImplUnderTest.downloadStickZip("downloadKey");

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockRedisUtils).delete("downloadKey");
    }

    @Test
    public void testCreateStickZip() throws Exception {
        // Setup
        final WxStickDownloadReqDTO wxStickDownloadReqDTO = new WxStickDownloadReqDTO("downloadKey", 0, "stickGuid",
                "storeName", "storeGuid", "brandGuid",
                Arrays.asList(new WxStickDownloadTableDTO("areaGuid", "areaName", "guid", "tableCode")));
        final JSONObject content = new JSONObject(0, false);

        // Configure WxStoreTableClientService.queryTableByWeb(...).
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableTagDTO.setGuid("2256253b-e899-49dc-89cf-9b2492270f38");
        tableTagDTO.setTagName("tagName");
        tableTagDTO.setSort(0);
        final List<TableBasicDTO> tableBasicDTOS = Arrays.asList(
                new TableBasicDTO("guid", "storeGuid", "storeName", "areaGuid", "areaName", "tableCode", 0, 0, 0, 0,
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        Arrays.asList("value"), Arrays.asList(tableTagDTO)));
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockWxStoreTableClientService.queryTableByWeb(tableBasicQueryDTO)).thenReturn(tableBasicDTOS);

        // Configure WxStoreTableStickMapstruct.stickDO2stickDTO(...).
        final WxTableStickDTO wxTableStickDTO = new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category",
                new BigDecimal("0.00"), new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit",
                "bgUrl", "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                "isDelete", 0L, 0L, "categoryName", "previewImg", 0);
        when(mockWxStoreTableStickMapstruct.stickDO2stickDTO(
                new WxStoreTableStickDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), "6b363ff4-53f1-4878-890a-c301f873048c",
                        "6b363ff4-53f1-4878-890a-c301f873048c", "name", "categoryGuid", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "backColor", 0.0, 0.0, "unit", "bgUrl", "backImage",
                        "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape", "storeNameText",
                        "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText", "qrCodeTextColor",
                        "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor", "sponsorsTextColor",
                        "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow", "tableNumberText",
                        "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable", 0,
                        "categoryName", "previewImg", 0))).thenReturn(wxTableStickDTO);

        when(mockWxStoreAuthorizerInfoService.getQrCodeUrl(
                new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName", "areaGuid", "areaName", "guid",
                        "tableCode", 0, "brandGuid", "appId", false, false, "myselfOpenId"))).thenReturn("qrCode");
        when(mockWxStoreMpService.getQrCodeUrl(
                new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName", "areaGuid", "areaName", "guid",
                        "tableCode", 0, "brandGuid", "appId", false, false, "myselfOpenId"))).thenReturn("longUrl");
        when(mockWxStoreMpService.shortenUrl("longUrl", "guid", 0)).thenReturn("qrCode");

        // Configure PdfUtil.createStickImage(...).
        final InputStream spyInputStream = spy(new ByteArrayInputStream("content".getBytes()));
        when(mockPdfUtil.createStickImage(
                new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                        "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                        "isDelete", 0L, 0L, "categoryName", "previewImg", 0))).thenReturn(spyInputStream);

        // Run the test
        wxStoreTableStickServiceImplUnderTest.createStickZip(wxStickDownloadReqDTO, "enterpriseGuid", content);

        // Verify the results
        verify(mockRedisUtils).set("downloadKey",
                new WxStickDownloadRespDTO("storeName", "content".getBytes(), "exception"));
        verify(spyInputStream).close();
    }

    @Test
    public void testCreateStickZip_WxStoreTableClientServiceReturnsNoItems() {
        // Setup
        final WxStickDownloadReqDTO wxStickDownloadReqDTO = new WxStickDownloadReqDTO("downloadKey", 0, "stickGuid",
                "storeName", "storeGuid", "brandGuid",
                Arrays.asList(new WxStickDownloadTableDTO("areaGuid", "areaName", "guid", "tableCode")));
        final JSONObject content = new JSONObject(0, false);

        // Configure WxStoreTableClientService.queryTableByWeb(...).
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockWxStoreTableClientService.queryTableByWeb(tableBasicQueryDTO)).thenReturn(Collections.emptyList());

        // Run the test
        wxStoreTableStickServiceImplUnderTest.createStickZip(wxStickDownloadReqDTO, "enterpriseGuid", content);

        // Verify the results
    }

    @Test(expected = BusinessException.class)
    public void testCreateStickZip_WxStoreAuthorizerInfoServiceThrowsWxErrorException() throws Exception {
        // Setup
        final WxStickDownloadReqDTO wxStickDownloadReqDTO = new WxStickDownloadReqDTO("downloadKey", 0, "stickGuid",
                "storeName", "storeGuid", "brandGuid",
                Arrays.asList(new WxStickDownloadTableDTO("areaGuid", "areaName", "guid", "tableCode")));
        final JSONObject content = new JSONObject(0, false);

        // Configure WxStoreTableClientService.queryTableByWeb(...).
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableTagDTO.setGuid("2256253b-e899-49dc-89cf-9b2492270f38");
        tableTagDTO.setTagName("tagName");
        tableTagDTO.setSort(0);
        final List<TableBasicDTO> tableBasicDTOS = Arrays.asList(
                new TableBasicDTO("guid", "storeGuid", "storeName", "areaGuid", "areaName", "tableCode", 0, 0, 0, 0,
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        Arrays.asList("value"), Arrays.asList(tableTagDTO)));
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockWxStoreTableClientService.queryTableByWeb(tableBasicQueryDTO)).thenReturn(tableBasicDTOS);

        // Configure WxStoreTableStickMapstruct.stickDO2stickDTO(...).
        final WxTableStickDTO wxTableStickDTO = new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category",
                new BigDecimal("0.00"), new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit",
                "bgUrl", "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                "isDelete", 0L, 0L, "categoryName", "previewImg", 0);
        when(mockWxStoreTableStickMapstruct.stickDO2stickDTO(
                new WxStoreTableStickDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), "6b363ff4-53f1-4878-890a-c301f873048c",
                        "6b363ff4-53f1-4878-890a-c301f873048c", "name", "categoryGuid", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "backColor", 0.0, 0.0, "unit", "bgUrl", "backImage",
                        "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape", "storeNameText",
                        "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText", "qrCodeTextColor",
                        "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor", "sponsorsTextColor",
                        "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow", "tableNumberText",
                        "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable", 0,
                        "categoryName", "previewImg", 0))).thenReturn(wxTableStickDTO);

        when(mockWxStoreAuthorizerInfoService.getQrCodeUrl(
                new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName", "areaGuid", "areaName", "guid",
                        "tableCode", 0, "brandGuid", "appId", false, false, "myselfOpenId")))
                .thenThrow(WxErrorException.class);

        // Run the test
        wxStoreTableStickServiceImplUnderTest.createStickZip(wxStickDownloadReqDTO, "enterpriseGuid", content);
    }

    @Test(expected = BusinessException.class)
    public void testCreateStickZip_WxStoreMpServiceShortenUrlThrowsWxErrorException() throws Exception {
        // Setup
        final WxStickDownloadReqDTO wxStickDownloadReqDTO = new WxStickDownloadReqDTO("downloadKey", 0, "stickGuid",
                "storeName", "storeGuid", "brandGuid",
                Arrays.asList(new WxStickDownloadTableDTO("areaGuid", "areaName", "guid", "tableCode")));
        final JSONObject content = new JSONObject(0, false);

        // Configure WxStoreTableClientService.queryTableByWeb(...).
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableTagDTO.setGuid("2256253b-e899-49dc-89cf-9b2492270f38");
        tableTagDTO.setTagName("tagName");
        tableTagDTO.setSort(0);
        final List<TableBasicDTO> tableBasicDTOS = Arrays.asList(
                new TableBasicDTO("guid", "storeGuid", "storeName", "areaGuid", "areaName", "tableCode", 0, 0, 0, 0,
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        Arrays.asList("value"), Arrays.asList(tableTagDTO)));
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockWxStoreTableClientService.queryTableByWeb(tableBasicQueryDTO)).thenReturn(tableBasicDTOS);

        // Configure WxStoreTableStickMapstruct.stickDO2stickDTO(...).
        final WxTableStickDTO wxTableStickDTO = new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category",
                new BigDecimal("0.00"), new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit",
                "bgUrl", "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                "isDelete", 0L, 0L, "categoryName", "previewImg", 0);
        when(mockWxStoreTableStickMapstruct.stickDO2stickDTO(
                new WxStoreTableStickDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), "6b363ff4-53f1-4878-890a-c301f873048c",
                        "6b363ff4-53f1-4878-890a-c301f873048c", "name", "categoryGuid", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "backColor", 0.0, 0.0, "unit", "bgUrl", "backImage",
                        "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape", "storeNameText",
                        "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText", "qrCodeTextColor",
                        "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor", "sponsorsTextColor",
                        "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow", "tableNumberText",
                        "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable", 0,
                        "categoryName", "previewImg", 0))).thenReturn(wxTableStickDTO);

        when(mockWxStoreMpService.getQrCodeUrl(
                new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName", "areaGuid", "areaName", "guid",
                        "tableCode", 0, "brandGuid", "appId", false, false, "myselfOpenId"))).thenReturn("longUrl");
        when(mockWxStoreMpService.shortenUrl("longUrl", "guid", 0)).thenThrow(WxErrorException.class);

        // Run the test
        wxStoreTableStickServiceImplUnderTest.createStickZip(wxStickDownloadReqDTO, "enterpriseGuid", content);
    }

    @Test
    public void testCreateStickZip_PdfUtilReturnsNoContent() throws Exception {
        // Setup
        final WxStickDownloadReqDTO wxStickDownloadReqDTO = new WxStickDownloadReqDTO("downloadKey", 0, "stickGuid",
                "storeName", "storeGuid", "brandGuid",
                Arrays.asList(new WxStickDownloadTableDTO("areaGuid", "areaName", "guid", "tableCode")));
        final JSONObject content = new JSONObject(0, false);

        // Configure WxStoreTableClientService.queryTableByWeb(...).
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableTagDTO.setGuid("2256253b-e899-49dc-89cf-9b2492270f38");
        tableTagDTO.setTagName("tagName");
        tableTagDTO.setSort(0);
        final List<TableBasicDTO> tableBasicDTOS = Arrays.asList(
                new TableBasicDTO("guid", "storeGuid", "storeName", "areaGuid", "areaName", "tableCode", 0, 0, 0, 0,
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        Arrays.asList("value"), Arrays.asList(tableTagDTO)));
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockWxStoreTableClientService.queryTableByWeb(tableBasicQueryDTO)).thenReturn(tableBasicDTOS);

        // Configure WxStoreTableStickMapstruct.stickDO2stickDTO(...).
        final WxTableStickDTO wxTableStickDTO = new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category",
                new BigDecimal("0.00"), new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit",
                "bgUrl", "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                "isDelete", 0L, 0L, "categoryName", "previewImg", 0);
        when(mockWxStoreTableStickMapstruct.stickDO2stickDTO(
                new WxStoreTableStickDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), "6b363ff4-53f1-4878-890a-c301f873048c",
                        "6b363ff4-53f1-4878-890a-c301f873048c", "name", "categoryGuid", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "backColor", 0.0, 0.0, "unit", "bgUrl", "backImage",
                        "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape", "storeNameText",
                        "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText", "qrCodeTextColor",
                        "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor", "sponsorsTextColor",
                        "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow", "tableNumberText",
                        "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable", 0,
                        "categoryName", "previewImg", 0))).thenReturn(wxTableStickDTO);

        when(mockWxStoreAuthorizerInfoService.getQrCodeUrl(
                new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName", "areaGuid", "areaName", "guid",
                        "tableCode", 0, "brandGuid", "appId", false, false, "myselfOpenId"))).thenReturn("qrCode");

        // Configure PdfUtil.createStickImage(...).
        final InputStream spyInputStream = spy(new NullInputStream(0L));
        when(mockPdfUtil.createStickImage(
                new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                        "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                        "isDelete", 0L, 0L, "categoryName", "previewImg", 0))).thenReturn(spyInputStream);

        // Run the test
        wxStoreTableStickServiceImplUnderTest.createStickZip(wxStickDownloadReqDTO, "enterpriseGuid", content);

        // Verify the results
        verify(spyInputStream).close();
        verify(mockRedisUtils).set("downloadKey",
                new WxStickDownloadRespDTO("storeName", "content".getBytes(), "exception"));
    }

    @Test
    public void testCreateStickZip_PdfUtilReturnsBrokenIo() throws Exception {
        // Setup
        final WxStickDownloadReqDTO wxStickDownloadReqDTO = new WxStickDownloadReqDTO("downloadKey", 0, "stickGuid",
                "storeName", "storeGuid", "brandGuid",
                Arrays.asList(new WxStickDownloadTableDTO("areaGuid", "areaName", "guid", "tableCode")));
        final JSONObject content = new JSONObject(0, false);

        // Configure WxStoreTableClientService.queryTableByWeb(...).
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableTagDTO.setGuid("2256253b-e899-49dc-89cf-9b2492270f38");
        tableTagDTO.setTagName("tagName");
        tableTagDTO.setSort(0);
        final List<TableBasicDTO> tableBasicDTOS = Arrays.asList(
                new TableBasicDTO("guid", "storeGuid", "storeName", "areaGuid", "areaName", "tableCode", 0, 0, 0, 0,
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        Arrays.asList("value"), Arrays.asList(tableTagDTO)));
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockWxStoreTableClientService.queryTableByWeb(tableBasicQueryDTO)).thenReturn(tableBasicDTOS);

        // Configure WxStoreTableStickMapstruct.stickDO2stickDTO(...).
        final WxTableStickDTO wxTableStickDTO = new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category",
                new BigDecimal("0.00"), new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit",
                "bgUrl", "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                "isDelete", 0L, 0L, "categoryName", "previewImg", 0);
        when(mockWxStoreTableStickMapstruct.stickDO2stickDTO(
                new WxStoreTableStickDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), "6b363ff4-53f1-4878-890a-c301f873048c",
                        "6b363ff4-53f1-4878-890a-c301f873048c", "name", "categoryGuid", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "backColor", 0.0, 0.0, "unit", "bgUrl", "backImage",
                        "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape", "storeNameText",
                        "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText", "qrCodeTextColor",
                        "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor", "sponsorsTextColor",
                        "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow", "tableNumberText",
                        "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable", 0,
                        "categoryName", "previewImg", 0))).thenReturn(wxTableStickDTO);

        when(mockWxStoreAuthorizerInfoService.getQrCodeUrl(
                new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName", "areaGuid", "areaName", "guid",
                        "tableCode", 0, "brandGuid", "appId", false, false, "myselfOpenId"))).thenReturn("qrCode");

        // Configure PdfUtil.createStickImage(...).
        final InputStream spyInputStream = spy(new BrokenInputStream());
        when(mockPdfUtil.createStickImage(
                new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                        "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                        "isDelete", 0L, 0L, "categoryName", "previewImg", 0))).thenReturn(spyInputStream);

        // Run the test
        wxStoreTableStickServiceImplUnderTest.createStickZip(wxStickDownloadReqDTO, "enterpriseGuid", content);

        // Verify the results
        verify(spyInputStream).close();
        verify(mockRedisUtils).set("downloadKey",
                new WxStickDownloadRespDTO("storeName", "content".getBytes(), "exception"));
    }

    @Test
    public void testIsWeatherConfig() {
        // Setup
        // Configure WxConfigOverviewService.getOne(...).
        final WxConfigOverviewDO wxConfigOverviewDO = new WxConfigOverviewDO(0L, "c07e20e7-71e5-44dc-8a3f-a450913b85a1",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid", 0, 0, 0, 0,
                0);
        when(mockWxConfigOverviewService.getOne(any(LambdaQueryWrapper.class))).thenReturn(wxConfigOverviewDO);

        // Run the test
        final boolean result = wxStoreTableStickServiceImplUnderTest.isWeatherConfig("storeGuid");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testIsWeatherConfig_WxConfigOverviewServiceReturnsNull() {
        // Setup
        when(mockWxConfigOverviewService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        final boolean result = wxStoreTableStickServiceImplUnderTest.isWeatherConfig("storeGuid");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testDeleteMyStick() {
        // Setup
        final WxStickIsModelDTO wxStickIsModelDTO = new WxStickIsModelDTO("8e81ae25-99a7-4a67-b6c2-ed92acdc5a15", 0);

        // Run the test
        final Boolean result = wxStoreTableStickServiceImplUnderTest.deleteMyStick(wxStickIsModelDTO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testCheckIsBought() {
        assertFalse(wxStoreTableStickServiceImplUnderTest.checkIsBought(Arrays.asList("value")));
    }

    @Test
    public void testSaveOrUpdateModels() {
        // Setup
        final List<WxTableStickDTO> wxTableStickDTOList = Arrays.asList(
                new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                        "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                        "isDelete", 0L, 0L, "categoryName", "previewImg", 0));

        // Configure WxStoreTableStickMapstruct.tableStickDTO2DO(...).
        final WxStoreTableStickDO wxStoreTableStickDO = new WxStoreTableStickDO(0L,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                "6b363ff4-53f1-4878-890a-c301f873048c", "6b363ff4-53f1-4878-890a-c301f873048c", "name", "categoryGuid",
                new BigDecimal("0.00"), new BigDecimal("0.00"), "isRecommended", "backColor", 0.0, 0.0, "unit", "bgUrl",
                "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                "sponsorsTextColor", "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow",
                "tableNumberText", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                0, "categoryName", "previewImg", 0);
        when(mockWxStoreTableStickMapstruct.tableStickDTO2DO(
                new WxTableStickDTO("9ee631fa-bd2b-4599-8245-7c9d050e1b9f", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaName", "areaTextColor", "tableNumberShow",
                        "tableCode", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                        "isDelete", 0L, 0L, "categoryName", "previewImg", 0))).thenReturn(wxStoreTableStickDO);

        when(mockRedisUtils.generateGuid("redisKey")).thenReturn("6b363ff4-53f1-4878-890a-c301f873048c");

        // Run the test
        wxStoreTableStickServiceImplUnderTest.saveOrUpdateModels(wxTableStickDTOList);

        // Verify the results
    }

    @Test
    public void testGetRedirectUrl() {
        // Setup
        when(mockRedisUtils.hGet("key", "field")).thenReturn("result");

        // Configure WxQrRedirectMapper.selectOne(...).
        final WxQrRedirectDo wxQrRedirectDo = new WxQrRedirectDo(0L, "url", "tableGuid", 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "staffGuid", "staffName");
        when(mockWxQrRedirectMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(wxQrRedirectDo);

        // Configure WxQrRedirectMapper.selectList(...).
        final List<WxQrRedirectDo> wxQrRedirectDos = Arrays.asList(
                new WxQrRedirectDo(0L, "url", "tableGuid", 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0), "staffGuid",
                        "staffName"));
        when(mockWxQrRedirectMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(wxQrRedirectDos);

        // Configure EnterpriseClientService.findMemberInfoByOrganizationGuid(...).
        final MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
        multiMemberDTO.setId(0L);
        multiMemberDTO.setEnterpriseGuid("enterpriseGuid");
        multiMemberDTO.setMultiMemberGuid("multiMemberGuid");
        multiMemberDTO.setMultiMemberName("multiMemberName");
        multiMemberDTO.setEnabled(false);
        when(mockEnterpriseClientService.findMemberInfoByOrganizationGuid("organizationGuid"))
                .thenReturn(multiMemberDTO);

        // Run the test
        final String result = wxStoreTableStickServiceImplUnderTest.getRedirectUrl("enterpriseTable", "lang");

        // Verify the results
        assertEquals("result", result);
        verify(mockRedisUtils).hPut("key", "hashKey", "value");
    }

    @Test(expected = BusinessException.class)
    public void testGetRedirectUrl_WxQrRedirectMapperSelectListReturnsNoItems() {
        // Setup
        when(mockRedisUtils.hGet("key", "field")).thenReturn("result");

        // Configure WxQrRedirectMapper.selectOne(...).
        final WxQrRedirectDo wxQrRedirectDo = new WxQrRedirectDo(0L, "url", "tableGuid", 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "staffGuid", "staffName");
        when(mockWxQrRedirectMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(wxQrRedirectDo);

        when(mockWxQrRedirectMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        wxStoreTableStickServiceImplUnderTest.getRedirectUrl("enterpriseTable", "lang");
    }
}
