package com.holderzone.saas.store.weixin.controller;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.weixin.req.WxReserveConfigDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.weixin.service.WxReserveConfigService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxReserveConfigController.class)
public class WxReserveConfigControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxReserveConfigService mockWxReserveConfigService;

    @Test
    public void testListConfig() throws Exception {
        // Setup
        // Configure WxReserveConfigService.listConfig(...).
        final WxReserveConfigDTO wxReserveConfigDTO = new WxReserveConfigDTO();
        wxReserveConfigDTO.setGuid("ff403f7a-fb81-4743-a372-42e12f678cbb");
        wxReserveConfigDTO.setStoreGuid("storeGuid");
        wxReserveConfigDTO.setStoreName("storeName");
        wxReserveConfigDTO.setStatus(false);
        wxReserveConfigDTO.setReserveType(0);
        final Page<WxReserveConfigDTO> wxReserveConfigDTOPage = new Page<>(0L, 0L, Arrays.asList(wxReserveConfigDTO));
        when(mockWxReserveConfigService.listConfig(WxStorePageReqDTO.builder().build()))
                .thenReturn(wxReserveConfigDTOPage);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_reserve/list_config")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateConfig() throws Exception {
        // Setup
        // Configure WxReserveConfigService.updateConfig(...).
        final WxReserveConfigDTO wxReserveConfigDTO = new WxReserveConfigDTO();
        wxReserveConfigDTO.setGuid("ff403f7a-fb81-4743-a372-42e12f678cbb");
        wxReserveConfigDTO.setStoreGuid("storeGuid");
        wxReserveConfigDTO.setStoreName("storeName");
        wxReserveConfigDTO.setStatus(false);
        wxReserveConfigDTO.setReserveType(0);
        when(mockWxReserveConfigService.updateConfig(wxReserveConfigDTO)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_reserve/update_config")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateConfig_WxReserveConfigServiceReturnsTrue() throws Exception {
        // Setup
        // Configure WxReserveConfigService.updateConfig(...).
        final WxReserveConfigDTO wxReserveConfigDTO = new WxReserveConfigDTO();
        wxReserveConfigDTO.setGuid("ff403f7a-fb81-4743-a372-42e12f678cbb");
        wxReserveConfigDTO.setStoreGuid("storeGuid");
        wxReserveConfigDTO.setStoreName("storeName");
        wxReserveConfigDTO.setStatus(false);
        wxReserveConfigDTO.setReserveType(0);
        when(mockWxReserveConfigService.updateConfig(wxReserveConfigDTO)).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_reserve/update_config")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetConfig() throws Exception {
        // Setup
        // Configure WxReserveConfigService.getConfig(...).
        final WxReserveConfigDTO wxReserveConfigDTO = new WxReserveConfigDTO();
        wxReserveConfigDTO.setGuid("ff403f7a-fb81-4743-a372-42e12f678cbb");
        wxReserveConfigDTO.setStoreGuid("storeGuid");
        wxReserveConfigDTO.setStoreName("storeName");
        wxReserveConfigDTO.setStatus(false);
        wxReserveConfigDTO.setReserveType(0);
        when(mockWxReserveConfigService.getConfig("data")).thenReturn(wxReserveConfigDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_reserve/get_config")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testObtainByStore() throws Exception {
        // Setup
        // Configure WxReserveConfigService.store(...).
        final WxReserveConfigDTO wxReserveConfigDTO = new WxReserveConfigDTO();
        wxReserveConfigDTO.setGuid("ff403f7a-fb81-4743-a372-42e12f678cbb");
        wxReserveConfigDTO.setStoreGuid("storeGuid");
        wxReserveConfigDTO.setStoreName("storeName");
        wxReserveConfigDTO.setStatus(false);
        wxReserveConfigDTO.setReserveType(0);
        when(mockWxReserveConfigService.store("storeGuid")).thenReturn(wxReserveConfigDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_reserve/store")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testStoreList() throws Exception {
        // Setup
        // Configure WxReserveConfigService.storeList(...).
        final WxReserveConfigDTO wxReserveConfigDTO = new WxReserveConfigDTO();
        wxReserveConfigDTO.setGuid("ff403f7a-fb81-4743-a372-42e12f678cbb");
        wxReserveConfigDTO.setStoreGuid("storeGuid");
        wxReserveConfigDTO.setStoreName("storeName");
        wxReserveConfigDTO.setStatus(false);
        wxReserveConfigDTO.setReserveType(0);
        final List<WxReserveConfigDTO> wxReserveConfigDTOS = Arrays.asList(wxReserveConfigDTO);
        when(mockWxReserveConfigService.storeList(Arrays.asList("value"))).thenReturn(wxReserveConfigDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_reserve/store_list")
                        .param("storeGuidList", "storeGuidList")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testStoreList_WxReserveConfigServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockWxReserveConfigService.storeList(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_reserve/store_list")
                        .param("storeGuidList", "storeGuidList")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }
}
