package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.weixin.open.WxMpTemplateDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerInfoDO;
import com.holderzone.saas.store.weixin.service.WxSaasMpService;
import me.chanjar.weixin.common.error.WxErrorException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxMpTemplateServiceImplTest {

    @Mock
    private WxSaasMpService mockWxSaasMpService;

    private WxMpTemplateServiceImpl wxMpTemplateServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxMpTemplateServiceImplUnderTest = new WxMpTemplateServiceImpl(mockWxSaasMpService);
    }

    @Test
    public void testCreateMsgTemplate() throws Exception {
        // Setup
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "e1b85ee3-fe36-4c6e-b5e9-8ad14ed55887", "BrandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateId", false, "operSubjectGuid");
        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "e1b85ee3-fe36-4c6e-b5e9-8ad14ed55887", "BrandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateId",
                        false, "operSubjectGuid"))).thenReturn(null);

        // Run the test
        final String result = wxMpTemplateServiceImplUnderTest.createMsgTemplate(wxStoreAuthorizerInfoDO, 0);

        // Verify the results
        assertEquals("success", result);
    }

    @Test(expected = BusinessException.class)
    public void testCreateMsgTemplate_WxSaasMpServiceThrowsWxErrorException() throws Exception {
        // Setup
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "e1b85ee3-fe36-4c6e-b5e9-8ad14ed55887", "BrandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateId", false, "operSubjectGuid");
        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "e1b85ee3-fe36-4c6e-b5e9-8ad14ed55887", "BrandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateId",
                        false, "operSubjectGuid"))).thenThrow(WxErrorException.class);

        // Run the test
        wxMpTemplateServiceImplUnderTest.createMsgTemplate(wxStoreAuthorizerInfoDO, 0);
    }

    @Test
    public void testAddMsgTemplate() {
        // Setup
        final WxMpTemplateDTO wxMpTemplateDTO = new WxMpTemplateDTO();
        wxMpTemplateDTO.setShortId("shortId");
        wxMpTemplateDTO.setAppId("appId");
        wxMpTemplateDTO.setTemplateId("templateId");
        wxMpTemplateDTO.setBrandGuid("BrandGuid");
        wxMpTemplateDTO.setType(0);

        // Run the test
        wxMpTemplateServiceImplUnderTest.addMsgTemplate(wxMpTemplateDTO);

        // Verify the results
    }
}
