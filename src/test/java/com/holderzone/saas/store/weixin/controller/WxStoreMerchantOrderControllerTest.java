package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.weixin.utils.MockHttpUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.context.WebApplicationContext;

@RunWith(SpringRunner.class)
@SpringBootTest
public class WxStoreMerchantOrderControllerTest {

    private static final String WX_STORE_MERCHANT_ORDER = "/wx_store_merchant_order";

    private static final String userInfo = "{\"operSubjectGuid\": \"2008141005594470007\",\"enterpriseGuid\":" +
            " \"6506431195651982337\",\"enterpriseName\": \"企业0227\",\"enterpriseNo\": \"********\",\"storeGuid\":" +
            " \"6619160595813892096\",\"storeName\": \"听雨阁\",\"storeNo\": \"6148139\",\"userGuid\": " +
            "\"6653489337230950401\",\"account\": \"200003\",\"tel\": \"***********\",\"name\": \"赵亮\"}\n";

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setUp() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void testGetDetailByOrderRecordGuid() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("orderRecordGuid", "6831515853117194240");
        String content = MockHttpUtil.get(WX_STORE_MERCHANT_ORDER + "/get_detail_by_order_record_guid", params, mockMvc);
        System.out.println(content);
    }

    @Test
    public void testGetWechatOrderFeeByOrderGuid() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("orderGuid", "6610772994555904000");
        String content = MockHttpUtil.get(WX_STORE_MERCHANT_ORDER + "/get_wechat_order_fee_by_order_guid", params, mockMvc);
        System.out.println(content);
    }

    @Test
    public void testGetWechatOrderInfoByOrderGuid() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("orderGuid", "6610772994555904000");
        String content = MockHttpUtil.get(WX_STORE_MERCHANT_ORDER + "/get_wechat_order_info_by_order_guid", params, mockMvc);
        System.out.println(content);
    }

    @Test
    public void testGetWechatOrderInfoByGuid() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("guid", "6610772994555904000");
        String content = MockHttpUtil.get(WX_STORE_MERCHANT_ORDER + "/get_wechat_order_info_by_guid", params, mockMvc);
        System.out.println(content);
    }
}