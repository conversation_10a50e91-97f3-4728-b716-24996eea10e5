package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.weixin.WxStickOrderCallBackDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickModelOrderDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickOrderRespDTO;
import com.holderzone.saas.store.weixin.service.WxStickModelOrderService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxStickModelOrderController.class)
public class WxStickModelOrderControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStickModelOrderService mockWxStickModelOrderService;

    @Test
    public void testOrder() throws Exception {
        // Setup
        // Configure WxStickModelOrderService.order(...).
        final WxStickOrderRespDTO wxStickOrderRespDTO = new WxStickOrderRespDTO();
        wxStickOrderRespDTO.setCode("code");
        wxStickOrderRespDTO.setMsg("msg");
        wxStickOrderRespDTO.setPayGuid("payGuid");
        wxStickOrderRespDTO.setPaymentGuid("paymentGuid");
        wxStickOrderRespDTO.setCodeUrl("codeUrl");
        when(mockWxStickModelOrderService.order(
                new WxStickModelOrderDTO("enterpriseGuid", Arrays.asList("value"), 0, "terminal", "payType",
                        "staffAccount", "staffGuid", "staffName", "lastPayGuid"))).thenReturn(wxStickOrderRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_stick_order/order")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testPolling() throws Exception {
        // Setup
        // Configure WxStickModelOrderService.polling(...).
        final WxStickOrderRespDTO wxStickOrderRespDTO = new WxStickOrderRespDTO();
        wxStickOrderRespDTO.setCode("code");
        wxStickOrderRespDTO.setMsg("msg");
        wxStickOrderRespDTO.setPayGuid("payGuid");
        wxStickOrderRespDTO.setPaymentGuid("paymentGuid");
        wxStickOrderRespDTO.setCodeUrl("codeUrl");
        final WxStickOrderRespDTO wxStickOrderRespDTO1 = new WxStickOrderRespDTO();
        wxStickOrderRespDTO1.setCode("code");
        wxStickOrderRespDTO1.setMsg("msg");
        wxStickOrderRespDTO1.setPayGuid("payGuid");
        wxStickOrderRespDTO1.setPaymentGuid("paymentGuid");
        wxStickOrderRespDTO1.setCodeUrl("codeUrl");
        when(mockWxStickModelOrderService.polling(wxStickOrderRespDTO1)).thenReturn(wxStickOrderRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_stick_order/polling")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testCallBack() throws Exception {
        // Setup
        when(mockWxStickModelOrderService.callBack(
                new WxStickOrderCallBackDTO("enterpriseGuid", "payGuid", "paySt", Arrays.asList("value"))))
                .thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/wx_stick_order/call_back")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
