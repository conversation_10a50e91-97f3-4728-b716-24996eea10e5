package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.holder.saas.member.wechat.dto.coupon.RequestVolumeStoreAndCard;
import com.holderzone.holder.saas.member.wechat.dto.coupon.ResponseHsmProductDetail;
import com.holderzone.holder.saas.member.wechat.dto.coupon.ResponseVolumeStoreAndProduct;
import com.holderzone.holder.saas.member.wechat.dto.coupon.VolumeInStore;
import com.holderzone.holder.saas.member.wechat.dto.member.MemberInfoVolume;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestMemberInfoVolumeQuery;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolume;
import com.holderzone.saas.store.dto.weixin.member.WxApplicableProductStores;
import com.holderzone.saas.store.dto.weixin.member.WxMemberInfoVolumeDetailsRespDTO;
import com.holderzone.saas.store.dto.weixin.member.WxMemberVolumeInfoListReqDTO;
import com.holderzone.saas.store.dto.weixin.member.WxVolumeDetailReqDTO;
import com.holderzone.saas.store.weixin.config.ResponseModel;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxMemberCenterVolumeServiceImplTest {

    @Mock
    private MemberClientService mockMemberClientService;
    @Mock
    private HsaBaseClientService mockHsaBaseClientService;

    private WxMemberCenterVolumeServiceImpl wxMemberCenterVolumeServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxMemberCenterVolumeServiceImplUnderTest = new WxMemberCenterVolumeServiceImpl(mockMemberClientService,
                mockHsaBaseClientService);
    }

    @Test
    public void testVolumeInfoList() {
        // Setup
        final WxMemberVolumeInfoListReqDTO wxMemberVolumeInfoListReqDTO = WxMemberVolumeInfoListReqDTO.builder()
                .memberInfoGuid("memberInfoGuid")
                .volumeType(0)
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .mayUseVolume(0)
                .storeGuid("storeGuid")
                .build();
        final ResponseMemberInfoVolume expectedResult = new ResponseMemberInfoVolume();
        expectedResult.setMayUseVolumeNum(0);
        expectedResult.setNotUseVolumeNum(0);
        final MemberInfoVolume memberInfoVolume = new MemberInfoVolume();
        memberInfoVolume.setMayUseVolume(0);
        memberInfoVolume.setVolumeEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setMemberVolumeList(Arrays.asList(memberInfoVolume));

        // Configure HsaBaseClientService.getMemberVolume(...).
        final ResponseMemberInfoVolume responseMemberInfoVolume = new ResponseMemberInfoVolume();
        responseMemberInfoVolume.setMayUseVolumeNum(0);
        responseMemberInfoVolume.setNotUseVolumeNum(0);
        final MemberInfoVolume memberInfoVolume1 = new MemberInfoVolume();
        memberInfoVolume1.setMayUseVolume(0);
        memberInfoVolume1.setVolumeEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        responseMemberInfoVolume.setMemberVolumeList(Arrays.asList(memberInfoVolume1));
        final ResponseModel<ResponseMemberInfoVolume> responseMemberInfoVolumeResponseModel = new ResponseModel<>(
                responseMemberInfoVolume);
        final RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO = new RequestMemberInfoVolumeQuery();
        memberInfoVolumeQueryReqDTO.setMemberInfoGuid("memberInfoGuid");
        memberInfoVolumeQueryReqDTO.setVolumeType(0);
        memberInfoVolumeQueryReqDTO.setStoreGuid("storeGuid");
        memberInfoVolumeQueryReqDTO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVolumeQueryReqDTO.setBrandGuid("brandGuid");
        memberInfoVolumeQueryReqDTO.setMayUseVolume(0);
        when(mockHsaBaseClientService.getMemberVolume(memberInfoVolumeQueryReqDTO))
                .thenReturn(responseMemberInfoVolumeResponseModel);

        // Run the test
        final ResponseMemberInfoVolume result = wxMemberCenterVolumeServiceImplUnderTest.volumeInfoList(
                wxMemberVolumeInfoListReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testVolumeCodeDetails() {
        // Setup
        final WxVolumeDetailReqDTO wxVolumeDetailReqDTO = new WxVolumeDetailReqDTO("enterpriseGuid", "memberVolumeGuid",
                "volumeInfoGuid");
        final WxMemberInfoVolumeDetailsRespDTO expectedResult = WxMemberInfoVolumeDetailsRespDTO.builder()
                .wxApplicableProductStores(Arrays.asList(WxApplicableProductStores.builder()
                        .storeName("storeName")
                        .build()))
                .supportAll(0)
                .productDetailRespDTOS(Arrays.asList(new ResponseHsmProductDetail()))
                .build();

        // Configure HsaBaseClientService.getSpecialMemberVolumeDetails(...).
        final ResponseModel<WxMemberInfoVolumeDetailsRespDTO> wxMemberInfoVolumeDetailsRespDTOResponseModel = new ResponseModel<>(
                WxMemberInfoVolumeDetailsRespDTO.builder()
                        .wxApplicableProductStores(Arrays.asList(WxApplicableProductStores.builder()
                                .storeName("storeName")
                                .build()))
                        .supportAll(0)
                        .productDetailRespDTOS(Arrays.asList(new ResponseHsmProductDetail()))
                        .build());
        when(mockHsaBaseClientService.getSpecialMemberVolumeDetails("memberVolumeGuid"))
                .thenReturn(wxMemberInfoVolumeDetailsRespDTOResponseModel);

        // Configure HsaBaseClientService.getVolumeStoreAndProduct(...).
        final ResponseVolumeStoreAndProduct responseVolumeStoreAndProduct = new ResponseVolumeStoreAndProduct();
        responseVolumeStoreAndProduct.setVolumeGuid("volumeGuid");
        final VolumeInStore volumeInStore = new VolumeInStore();
        volumeInStore.setStoreName("storeName");
        responseVolumeStoreAndProduct.setVolumeInStoreList(Arrays.asList(volumeInStore));
        responseVolumeStoreAndProduct.setSupportAll(0);
        final ResponseHsmProductDetail responseHsmProductDetail = new ResponseHsmProductDetail();
        responseVolumeStoreAndProduct.setProductDetailRespDTOS(Arrays.asList(responseHsmProductDetail));
        final ResponseModel<List<ResponseVolumeStoreAndProduct>> listResponseModel = new ResponseModel<>(
                Arrays.asList(responseVolumeStoreAndProduct));
        final RequestVolumeStoreAndCard volumeGuids = new RequestVolumeStoreAndCard();
        volumeGuids.setVolumeGuids(Arrays.asList("value"));
        volumeGuids.setEnterpriseGuid("enterpriseGuid");
        when(mockHsaBaseClientService.getVolumeStoreAndProduct(volumeGuids)).thenReturn(listResponseModel);

        // Run the test
        final WxMemberInfoVolumeDetailsRespDTO result = wxMemberCenterVolumeServiceImplUnderTest.volumeCodeDetails(
                wxVolumeDetailReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testVolumeCodeDetails_HsaBaseClientServiceGetVolumeStoreAndProductReturnsNoItems() {
        // Setup
        final WxVolumeDetailReqDTO wxVolumeDetailReqDTO = new WxVolumeDetailReqDTO("enterpriseGuid", "memberVolumeGuid",
                "volumeInfoGuid");
        final WxMemberInfoVolumeDetailsRespDTO expectedResult = WxMemberInfoVolumeDetailsRespDTO.builder()
                .wxApplicableProductStores(Arrays.asList(WxApplicableProductStores.builder()
                        .storeName("storeName")
                        .build()))
                .supportAll(0)
                .productDetailRespDTOS(Arrays.asList(new ResponseHsmProductDetail()))
                .build();

        // Configure HsaBaseClientService.getSpecialMemberVolumeDetails(...).
        final ResponseModel<WxMemberInfoVolumeDetailsRespDTO> wxMemberInfoVolumeDetailsRespDTOResponseModel = new ResponseModel<>(
                WxMemberInfoVolumeDetailsRespDTO.builder()
                        .wxApplicableProductStores(Arrays.asList(WxApplicableProductStores.builder()
                                .storeName("storeName")
                                .build()))
                        .supportAll(0)
                        .productDetailRespDTOS(Arrays.asList(new ResponseHsmProductDetail()))
                        .build());
        when(mockHsaBaseClientService.getSpecialMemberVolumeDetails("memberVolumeGuid"))
                .thenReturn(wxMemberInfoVolumeDetailsRespDTOResponseModel);

        // Configure HsaBaseClientService.getVolumeStoreAndProduct(...).
        final ResponseModel<List<ResponseVolumeStoreAndProduct>> listResponseModel = new ResponseModel<>(
                Collections.emptyList());
        final RequestVolumeStoreAndCard volumeGuids = new RequestVolumeStoreAndCard();
        volumeGuids.setVolumeGuids(Arrays.asList("value"));
        volumeGuids.setEnterpriseGuid("enterpriseGuid");
        when(mockHsaBaseClientService.getVolumeStoreAndProduct(volumeGuids)).thenReturn(listResponseModel);

        // Run the test
        final WxMemberInfoVolumeDetailsRespDTO result = wxMemberCenterVolumeServiceImplUnderTest.volumeCodeDetails(
                wxVolumeDetailReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
