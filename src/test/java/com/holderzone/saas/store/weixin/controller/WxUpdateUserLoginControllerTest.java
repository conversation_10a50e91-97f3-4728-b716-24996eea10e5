package com.holderzone.saas.store.weixin.controller;

import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.service.WxUserRecordService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxUpdateUserLoginController.class)
public class WxUpdateUserLoginControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxUserRecordService mockWxUserRecordService;
    @MockBean
    private RedisUtils mockRedisUtils;

    @Test
    public void testUpdateUserLogin() throws Exception {
        // Setup
        when(mockWxUserRecordService.updateUserLogin(WxStoreConsumerDTO.builder()
                .openId("openId")
                .isLogin(false)
                .build())).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_open/update_login")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateUserLogin_WxUserRecordServiceReturnsTrue() throws Exception {
        // Setup
        when(mockWxUserRecordService.updateUserLogin(WxStoreConsumerDTO.builder()
                .openId("openId")
                .isLogin(false)
                .build())).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_open/update_login")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
