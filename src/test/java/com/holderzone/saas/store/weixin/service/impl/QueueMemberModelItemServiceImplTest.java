package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.saas.store.dto.queue.QueueWechatDTO;
import com.holderzone.saas.store.dto.queue.WxQueueListDTO;
import com.holderzone.saas.store.dto.weixin.WxMemberOverviewModelDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.service.WxConfigOverviewService;
import com.holderzone.saas.store.weixin.service.rpc.WxQueueClientService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueueMemberModelItemServiceImplTest {

    @Mock
    private WxConfigOverviewService mockWxConfigOverviewService;
    @Mock
    private WxQueueClientService mockWxQueueClientService;

    @InjectMocks
    private QueueMemberModelItemServiceImpl queueMemberModelItemServiceImplUnderTest;

    @Test
    public void testGetWxMemberOverviewModel() {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .build();
        final WxMemberOverviewModelDTO expectedResult = WxMemberOverviewModelDTO.builder()
                .modelId(0)
                .modelName("我的排队")
                .modelCount(0)
                .build();

        // Configure WxQueueClientService.queryByUser(...).
        final List<WxQueueListDTO> wxQueueListDTOS = Arrays.asList(WxQueueListDTO.builder()
                .status((byte) 0b0)
                .build());
        when(mockWxQueueClientService.queryByUser(QueueWechatDTO.builder()
                .brandGuid("brandGuid")
                .userGuid("openId")
                .enterpriseGuid("enterpriseGuid")
                .build())).thenReturn(wxQueueListDTOS);

        // Run the test
        final WxMemberOverviewModelDTO result = queueMemberModelItemServiceImplUnderTest.getWxMemberOverviewModel(
                wxStoreConsumerDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetWxMemberOverviewModel_WxQueueClientServiceReturnsNoItems() {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .build();
        final WxMemberOverviewModelDTO expectedResult = WxMemberOverviewModelDTO.builder()
                .modelId(0)
                .modelName("我的排队")
                .modelCount(0)
                .build();
        when(mockWxQueueClientService.queryByUser(QueueWechatDTO.builder()
                .brandGuid("brandGuid")
                .userGuid("openId")
                .enterpriseGuid("enterpriseGuid")
                .build())).thenReturn(Collections.emptyList());

        // Run the test
        final WxMemberOverviewModelDTO result = queueMemberModelItemServiceImplUnderTest.getWxMemberOverviewModel(
                wxStoreConsumerDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
