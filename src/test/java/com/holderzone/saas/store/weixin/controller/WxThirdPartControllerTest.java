package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.weixin.req.WxQueryThirdPartUserInfoReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxThirdPartUserInfoReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxThirdPartUserInfoRespDTO;
import com.holderzone.saas.store.weixin.service.WxThirdPartUserInfoService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxThirdPartController.class)
public class WxThirdPartControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxThirdPartUserInfoService mockWxThirdPartUserInfoService;

    @Test
    public void testSaveOrUpdateThirdPartUserInfo() throws Exception {
        // Setup
        // Configure WxThirdPartUserInfoService.saveOrUpdateThirdPartUserInfo(...).
        final WxThirdPartUserInfoReqDTO wxThirdPartUserInfoReqDTO = new WxThirdPartUserInfoReqDTO();
        wxThirdPartUserInfoReqDTO.setNickName("nickName");
        when(mockWxThirdPartUserInfoService.saveOrUpdateThirdPartUserInfo(wxThirdPartUserInfoReqDTO)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_third_part/save_or_update_third_part_user_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testSaveOrUpdateThirdPartUserInfo_WxThirdPartUserInfoServiceReturnsTrue() throws Exception {
        // Setup
        // Configure WxThirdPartUserInfoService.saveOrUpdateThirdPartUserInfo(...).
        final WxThirdPartUserInfoReqDTO wxThirdPartUserInfoReqDTO = new WxThirdPartUserInfoReqDTO();
        wxThirdPartUserInfoReqDTO.setNickName("nickName");
        when(mockWxThirdPartUserInfoService.saveOrUpdateThirdPartUserInfo(wxThirdPartUserInfoReqDTO)).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_third_part/save_or_update_third_part_user_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testCheckThirdPartUserInfo() throws Exception {
        // Setup
        // Configure WxThirdPartUserInfoService.checkThirdPartUserInfo(...).
        final WxThirdPartUserInfoRespDTO wxThirdPartUserInfoRespDTO = new WxThirdPartUserInfoRespDTO();
        wxThirdPartUserInfoRespDTO.setGuid("3a9f7ceb-eb25-4e81-82f8-063c53088ec0");
        wxThirdPartUserInfoRespDTO.setNickName("nickName");
        wxThirdPartUserInfoRespDTO.setOpenId("openId");
        wxThirdPartUserInfoRespDTO.setPhone("phone");
        wxThirdPartUserInfoRespDTO.setSource(0);
        final WxQueryThirdPartUserInfoReqDTO wxQueryThirdPartUserInfoReqDTO = new WxQueryThirdPartUserInfoReqDTO();
        wxQueryThirdPartUserInfoReqDTO.setOpenId("openId");
        wxQueryThirdPartUserInfoReqDTO.setTel("tel");
        wxQueryThirdPartUserInfoReqDTO.setSource(0);
        wxQueryThirdPartUserInfoReqDTO.setPassword("password");
        when(mockWxThirdPartUserInfoService.checkThirdPartUserInfo(wxQueryThirdPartUserInfoReqDTO))
                .thenReturn(wxThirdPartUserInfoRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_third_part/check_third_part_user_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryThirdPartUserInfo() throws Exception {
        // Setup
        // Configure WxThirdPartUserInfoService.queryThirdPartUserInfo(...).
        final WxThirdPartUserInfoRespDTO wxThirdPartUserInfoRespDTO = new WxThirdPartUserInfoRespDTO();
        wxThirdPartUserInfoRespDTO.setGuid("3a9f7ceb-eb25-4e81-82f8-063c53088ec0");
        wxThirdPartUserInfoRespDTO.setNickName("nickName");
        wxThirdPartUserInfoRespDTO.setOpenId("openId");
        wxThirdPartUserInfoRespDTO.setPhone("phone");
        wxThirdPartUserInfoRespDTO.setSource(0);
        final WxQueryThirdPartUserInfoReqDTO wxQueryThirdPartUserInfoReqDTO = new WxQueryThirdPartUserInfoReqDTO();
        wxQueryThirdPartUserInfoReqDTO.setOpenId("openId");
        wxQueryThirdPartUserInfoReqDTO.setTel("tel");
        wxQueryThirdPartUserInfoReqDTO.setSource(0);
        wxQueryThirdPartUserInfoReqDTO.setPassword("password");
        when(mockWxThirdPartUserInfoService.queryThirdPartUserInfo(wxQueryThirdPartUserInfoReqDTO))
                .thenReturn(wxThirdPartUserInfoRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_third_part/query_third_part_user_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
