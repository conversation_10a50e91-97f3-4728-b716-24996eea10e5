package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.weixin.WxMemberOverviewModelDTO;
import com.holderzone.saas.store.dto.weixin.WxMemberOverviewRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.service.WxMemberOverviewService;
import com.holderzone.saas.store.weixin.service.impl.QueueMemberModelItemServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxMemberCenterOverviewController.class)
public class WxMemberCenterOverviewControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxMemberOverviewService mockWxMemberOverviewService;
    @MockBean
    private QueueMemberModelItemServiceImpl mockQueueMemberModelItemService;

    @Test
    public void testAllModel() throws Exception {
        // Setup
        // Configure WxMemberOverviewService.allModel(...).
        final WxMemberOverviewRespDTO wxMemberOverviewRespDTO = WxMemberOverviewRespDTO.builder().build();
        when(mockWxMemberOverviewService.allModel(WxStoreConsumerDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .build())).thenReturn(wxMemberOverviewRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_member_center_overview/all_model")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueueConfig() throws Exception {
        // Setup
        // Configure QueueMemberModelItemServiceImpl.getWxMemberOverviewModel(...).
        final WxMemberOverviewModelDTO wxMemberOverviewModelDTO = WxMemberOverviewModelDTO.builder().build();
        when(mockQueueMemberModelItemService.getWxMemberOverviewModel(WxStoreConsumerDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .build())).thenReturn(wxMemberOverviewModelDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_member_center_overview/queue_config")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
