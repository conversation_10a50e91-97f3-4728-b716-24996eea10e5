package com.holderzone.saas.store.weixin.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseAccountStatus;
import com.holderzone.holder.saas.weixin.entry.dto.OrderDetailDTO;
import com.holderzone.holder.saas.weixin.entry.dto.req.WxOrderDetailReqDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeCalculateDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.table.TableTagDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreTableCombineDTO;
import com.holderzone.saas.store.dto.weixin.resp.ItemImgDTO;
import com.holderzone.saas.store.weixin.config.ResponseModel;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderItemDO;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderRecordDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.helper.OrderDetailHelper;
import com.holderzone.saas.store.weixin.service.WxOrderRecordService;
import com.holderzone.saas.store.weixin.service.WxStoreMerchantOrderService;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import com.holderzone.saas.store.weixin.service.WxUserRecordService;
import com.holderzone.saas.store.weixin.service.deal.BusinessClientService;
import com.holderzone.saas.store.weixin.service.deal.ItemClientService;
import com.holderzone.saas.store.weixin.service.OrderItemService;
import com.holderzone.saas.store.weixin.service.deal.TableClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreDineInOrderClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreTableClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxOrderManagerTest {

    @Mock
    private WxStoreTableClientService mockWxStoreTableClientService;
    @Mock
    private WxOrderRecordService mockWxOrderRecordService;
    @Mock
    private OrderItemService mockOrderItemService;
    @Mock
    private WxStoreMerchantOrderService mockWxStoreMerchantOrderService;
    @Mock
    private WxStoreDineInOrderClientService mockWxStoreDineInOrderClientService;
    @Mock
    private ItemClientService mockItemClientService;
    @Mock
    private WxStoreSessionDetailsService mockWxStoreSessionDetailsService;
    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private WxUserRecordService mockWxUserRecordService;
    @Mock
    private TableClientService mockTableClientService;
    @Mock
    private HsaBaseClientService mockHsaBaseClientService;
    @Mock
    private BusinessClientService mockBusinessClientService;

    @InjectMocks
    private WxOrderManager wxOrderManagerUnderTest;

    @Before
    public void setUp() throws Exception {
        wxOrderManagerUnderTest.wxStoreTableClientService = mockWxStoreTableClientService;
        wxOrderManagerUnderTest.wxOrderRecordService = mockWxOrderRecordService;
        wxOrderManagerUnderTest.orderItemService = mockOrderItemService;
        wxOrderManagerUnderTest.wxStoreMerchantOrderService = mockWxStoreMerchantOrderService;
        wxOrderManagerUnderTest.wxStoreDineInOrderClientService = mockWxStoreDineInOrderClientService;
        wxOrderManagerUnderTest.itemClientService = mockItemClientService;
        wxOrderManagerUnderTest.wxStoreSessionDetailsService = mockWxStoreSessionDetailsService;
        wxOrderManagerUnderTest.redisUtils = mockRedisUtils;
        wxOrderManagerUnderTest.wxUserRecordService = mockWxUserRecordService;
        wxOrderManagerUnderTest.tableClientService = mockTableClientService;
    }

    @Test
    public void testDetail() {
        // Setup
        final WxOrderDetailReqDTO req = new WxOrderDetailReqDTO("2bef721b-09a0-4d36-96cb-ab4fe055bd2d", 0, 0,
                "memberInfoCardGuid", "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                false);
        final OrderDetailDTO expectedResult = new OrderDetailDTO();
        expectedResult.setMergeInfo("mergeInfo");
        expectedResult.setBrandName("brandName");
        expectedResult.setDiningTableCode("code");
        expectedResult.setAreaGuid("areaGuid");
        expectedResult.setAreaName("areaName");
        expectedResult.setGuestCount(0);
        expectedResult.setOrderState(0);
        expectedResult.setMemberCardGuid("memberCardGuid");
        expectedResult.setLoginStatus(false);

        // Configure WxOrderRecordService.getById(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265",
                "orderGuid", "merchantGuid", "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid",
                "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getById("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415")).thenReturn(wxOrderRecordDO);

        // Configure WxStoreSessionDetailsService.getTableDetail(...).
        final TableDTO tableDTO = TableDTO.builder()
                .storeGuid("storeGuid")
                .storeName("name")
                .areaName("areaName")
                .tableGuid("tableGuid")
                .code("code")
                .build();
        when(mockWxStoreSessionDetailsService.getTableDetail("tableGuid")).thenReturn(tableDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("617502ad-8d54-4801-989d-343e336d8425");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreTableClientService.tableCombineList(...).
        final List<WxStoreTableCombineDTO> wxStoreTableCombineDTOS = Arrays.asList(WxStoreTableCombineDTO.builder()
                .tableGuid("tableGuid")
                .areaName("areaName")
                .tableCode("tableCode")
                .mainOrderGuid("orderGuid")
                .orderGuid("orderGuid")
                .build());
        when(mockWxStoreTableClientService.tableCombineList("tableGuid", "mainOrderGuid"))
                .thenReturn(wxStoreTableCombineDTOS);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        actuallyPayFeeDetailDTO.setPaymentType(0);
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("desc");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setDiningTableGuid("diningTableGuid");
        dineinOrderDetailRespDTO.setGuestCount(0);
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberCardGuid");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure BusinessClientService.calculateSurcharge(...).
        final List<SurchargeLinkDTO> surchargeLinkDTOS = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        final SurchargeCalculateDTO surchargeDTO = new SurchargeCalculateDTO();
        surchargeDTO.setGuestCount(0);
        surchargeDTO.setTableGuid("diningTableGuid");
        surchargeDTO.setAreaGuid("areaGuid");
        surchargeDTO.setEnterpriseGuid("enterpriseGuid");
        surchargeDTO.setTradeMode(0);
        when(mockBusinessClientService.calculateSurcharge(surchargeDTO)).thenReturn(surchargeLinkDTOS);

        // Configure ItemClientService.getItemPictureUrls(...).
        final List<ItemImgDTO> itemImgDTOS = Arrays.asList(ItemImgDTO.builder().build());
        when(mockItemClientService.getItemPictureUrls(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(itemImgDTOS);

        // Configure WxUserRecordService.getById(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "8274c005-27d6-45b9-900a-8bfc6d676374", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        when(mockWxUserRecordService.getById("userRecordGuid")).thenReturn(wxUserRecordDO);

        // Configure TableClientService.listByWeb(...).
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableTagDTO.setGuid("f6a4e494-4e2e-4b56-a262-6d60e7bce658");
        tableTagDTO.setTagName("tagName");
        tableTagDTO.setSort(0);
        final List<TableBasicDTO> tableBasicDTOS = Arrays.asList(
                new TableBasicDTO("58c00a25-8768-49d0-a868-6f9c8b123e20", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableCode", 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), Arrays.asList("value"), Arrays.asList(tableTagDTO)));
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableClientService.listByWeb(tableBasicQueryDTO)).thenReturn(tableBasicDTOS);

        // Configure WxStoreSessionDetailsService.getBrandInfoDetails(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("guid");
        brandDTO.setUuid("c01c9044-16a9-4615-8df1-6d5aa53f1021");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockWxStoreSessionDetailsService.getBrandInfoDetails("storeGuid")).thenReturn(brandDTO);

        // Configure WxStoreMerchantOrderService.selectByOrderRecordGuid(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("batchCode");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setOpenId("openid");
        wxStoreMerchantOrderDO.setNickName("nickname");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setIsLogin(0);
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setHeadImgUrl("headImgUrl");
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderService.selectByOrderRecordGuid("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415",
                Arrays.asList("value"))).thenReturn(wxStoreMerchantOrderDOS);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure OrderItemService.list(...).
        final WxOrderItemDO wxOrderItemDO = new WxOrderItemDO();
        wxOrderItemDO.setGuid("55696e1c-5509-4e21-83d0-b8cc7a6f19ce");
        wxOrderItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderItemDO.setIsDel(false);
        wxOrderItemDO.setMerchantGuid("merchantGuid");
        final List<WxOrderItemDO> wxOrderItemDOS = Arrays.asList(wxOrderItemDO);
        when(mockOrderItemService.list(any(LambdaQueryWrapper.class))).thenReturn(wxOrderItemDOS);

        // Run the test
        final OrderDetailDTO result = wxOrderManagerUnderTest.detail(req);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxOrderRecordService).dealFastOrderTimeOut("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415");
        verify(mockWxOrderRecordService).updateById(
                new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
    }

    @Test
    public void testDetail_WxOrderRecordServiceGetByIdReturnsNull() {
        // Setup
        final WxOrderDetailReqDTO req = new WxOrderDetailReqDTO("2bef721b-09a0-4d36-96cb-ab4fe055bd2d", 0, 0,
                "memberInfoCardGuid", "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                false);
        final OrderDetailDTO expectedResult = new OrderDetailDTO();
        expectedResult.setMergeInfo("mergeInfo");
        expectedResult.setBrandName("brandName");
        expectedResult.setDiningTableCode("code");
        expectedResult.setAreaGuid("areaGuid");
        expectedResult.setAreaName("areaName");
        expectedResult.setGuestCount(0);
        expectedResult.setOrderState(0);
        expectedResult.setMemberCardGuid("memberCardGuid");
        expectedResult.setLoginStatus(false);

        when(mockWxOrderRecordService.getById("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415")).thenReturn(null);

        // Configure WxOrderRecordService.getByOrderHolderNo(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265",
                "orderGuid", "merchantGuid", "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid",
                "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getByOrderHolderNo("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415"))
                .thenReturn(wxOrderRecordDO);

        // Configure WxStoreSessionDetailsService.getTableDetail(...).
        final TableDTO tableDTO = TableDTO.builder()
                .storeGuid("storeGuid")
                .storeName("name")
                .areaName("areaName")
                .tableGuid("tableGuid")
                .code("code")
                .build();
        when(mockWxStoreSessionDetailsService.getTableDetail("tableGuid")).thenReturn(tableDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("617502ad-8d54-4801-989d-343e336d8425");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreTableClientService.tableCombineList(...).
        final List<WxStoreTableCombineDTO> wxStoreTableCombineDTOS = Arrays.asList(WxStoreTableCombineDTO.builder()
                .tableGuid("tableGuid")
                .areaName("areaName")
                .tableCode("tableCode")
                .mainOrderGuid("orderGuid")
                .orderGuid("orderGuid")
                .build());
        when(mockWxStoreTableClientService.tableCombineList("tableGuid", "mainOrderGuid"))
                .thenReturn(wxStoreTableCombineDTOS);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        actuallyPayFeeDetailDTO.setPaymentType(0);
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("desc");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setDiningTableGuid("diningTableGuid");
        dineinOrderDetailRespDTO.setGuestCount(0);
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberCardGuid");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure BusinessClientService.calculateSurcharge(...).
        final List<SurchargeLinkDTO> surchargeLinkDTOS = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        final SurchargeCalculateDTO surchargeDTO = new SurchargeCalculateDTO();
        surchargeDTO.setGuestCount(0);
        surchargeDTO.setTableGuid("diningTableGuid");
        surchargeDTO.setAreaGuid("areaGuid");
        surchargeDTO.setEnterpriseGuid("enterpriseGuid");
        surchargeDTO.setTradeMode(0);
        when(mockBusinessClientService.calculateSurcharge(surchargeDTO)).thenReturn(surchargeLinkDTOS);

        // Configure ItemClientService.getItemPictureUrls(...).
        final List<ItemImgDTO> itemImgDTOS = Arrays.asList(ItemImgDTO.builder().build());
        when(mockItemClientService.getItemPictureUrls(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(itemImgDTOS);

        // Configure WxUserRecordService.getById(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "8274c005-27d6-45b9-900a-8bfc6d676374", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        when(mockWxUserRecordService.getById("userRecordGuid")).thenReturn(wxUserRecordDO);

        // Configure TableClientService.listByWeb(...).
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableTagDTO.setGuid("f6a4e494-4e2e-4b56-a262-6d60e7bce658");
        tableTagDTO.setTagName("tagName");
        tableTagDTO.setSort(0);
        final List<TableBasicDTO> tableBasicDTOS = Arrays.asList(
                new TableBasicDTO("58c00a25-8768-49d0-a868-6f9c8b123e20", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableCode", 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), Arrays.asList("value"), Arrays.asList(tableTagDTO)));
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableClientService.listByWeb(tableBasicQueryDTO)).thenReturn(tableBasicDTOS);

        // Configure WxStoreSessionDetailsService.getBrandInfoDetails(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("guid");
        brandDTO.setUuid("c01c9044-16a9-4615-8df1-6d5aa53f1021");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockWxStoreSessionDetailsService.getBrandInfoDetails("storeGuid")).thenReturn(brandDTO);

        // Configure WxStoreMerchantOrderService.selectByOrderRecordGuid(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("batchCode");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setOpenId("openid");
        wxStoreMerchantOrderDO.setNickName("nickname");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setIsLogin(0);
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setHeadImgUrl("headImgUrl");
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderService.selectByOrderRecordGuid("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415",
                Arrays.asList("value"))).thenReturn(wxStoreMerchantOrderDOS);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure OrderItemService.list(...).
        final WxOrderItemDO wxOrderItemDO = new WxOrderItemDO();
        wxOrderItemDO.setGuid("55696e1c-5509-4e21-83d0-b8cc7a6f19ce");
        wxOrderItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderItemDO.setIsDel(false);
        wxOrderItemDO.setMerchantGuid("merchantGuid");
        final List<WxOrderItemDO> wxOrderItemDOS = Arrays.asList(wxOrderItemDO);
        when(mockOrderItemService.list(any(LambdaQueryWrapper.class))).thenReturn(wxOrderItemDOS);

        // Run the test
        final OrderDetailDTO result = wxOrderManagerUnderTest.detail(req);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxOrderRecordService).dealFastOrderTimeOut("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415");
        verify(mockWxOrderRecordService).updateById(
                new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
    }

    @Test
    public void testDetail_WxStoreTableClientServiceReturnsNoItems() {
        // Setup
        final WxOrderDetailReqDTO req = new WxOrderDetailReqDTO("2bef721b-09a0-4d36-96cb-ab4fe055bd2d", 0, 0,
                "memberInfoCardGuid", "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                false);
        final OrderDetailDTO expectedResult = new OrderDetailDTO();
        expectedResult.setMergeInfo("mergeInfo");
        expectedResult.setBrandName("brandName");
        expectedResult.setDiningTableCode("code");
        expectedResult.setAreaGuid("areaGuid");
        expectedResult.setAreaName("areaName");
        expectedResult.setGuestCount(0);
        expectedResult.setOrderState(0);
        expectedResult.setMemberCardGuid("memberCardGuid");
        expectedResult.setLoginStatus(false);

        // Configure WxOrderRecordService.getById(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265",
                "orderGuid", "merchantGuid", "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid",
                "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getById("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415")).thenReturn(wxOrderRecordDO);

        // Configure WxStoreSessionDetailsService.getTableDetail(...).
        final TableDTO tableDTO = TableDTO.builder()
                .storeGuid("storeGuid")
                .storeName("name")
                .areaName("areaName")
                .tableGuid("tableGuid")
                .code("code")
                .build();
        when(mockWxStoreSessionDetailsService.getTableDetail("tableGuid")).thenReturn(tableDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("617502ad-8d54-4801-989d-343e336d8425");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        when(mockWxStoreTableClientService.tableCombineList("tableGuid", "mainOrderGuid"))
                .thenReturn(Collections.emptyList());

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        actuallyPayFeeDetailDTO.setPaymentType(0);
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("desc");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setDiningTableGuid("diningTableGuid");
        dineinOrderDetailRespDTO.setGuestCount(0);
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberCardGuid");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure BusinessClientService.calculateSurcharge(...).
        final List<SurchargeLinkDTO> surchargeLinkDTOS = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        final SurchargeCalculateDTO surchargeDTO = new SurchargeCalculateDTO();
        surchargeDTO.setGuestCount(0);
        surchargeDTO.setTableGuid("diningTableGuid");
        surchargeDTO.setAreaGuid("areaGuid");
        surchargeDTO.setEnterpriseGuid("enterpriseGuid");
        surchargeDTO.setTradeMode(0);
        when(mockBusinessClientService.calculateSurcharge(surchargeDTO)).thenReturn(surchargeLinkDTOS);

        // Configure ItemClientService.getItemPictureUrls(...).
        final List<ItemImgDTO> itemImgDTOS = Arrays.asList(ItemImgDTO.builder().build());
        when(mockItemClientService.getItemPictureUrls(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(itemImgDTOS);

        // Configure WxUserRecordService.getById(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "8274c005-27d6-45b9-900a-8bfc6d676374", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        when(mockWxUserRecordService.getById("userRecordGuid")).thenReturn(wxUserRecordDO);

        // Configure TableClientService.listByWeb(...).
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableTagDTO.setGuid("f6a4e494-4e2e-4b56-a262-6d60e7bce658");
        tableTagDTO.setTagName("tagName");
        tableTagDTO.setSort(0);
        final List<TableBasicDTO> tableBasicDTOS = Arrays.asList(
                new TableBasicDTO("58c00a25-8768-49d0-a868-6f9c8b123e20", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableCode", 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), Arrays.asList("value"), Arrays.asList(tableTagDTO)));
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableClientService.listByWeb(tableBasicQueryDTO)).thenReturn(tableBasicDTOS);

        // Configure WxStoreSessionDetailsService.getBrandInfoDetails(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("guid");
        brandDTO.setUuid("c01c9044-16a9-4615-8df1-6d5aa53f1021");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockWxStoreSessionDetailsService.getBrandInfoDetails("storeGuid")).thenReturn(brandDTO);

        // Configure WxStoreMerchantOrderService.selectByOrderRecordGuid(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("batchCode");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setOpenId("openid");
        wxStoreMerchantOrderDO.setNickName("nickname");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setIsLogin(0);
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setHeadImgUrl("headImgUrl");
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderService.selectByOrderRecordGuid("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415",
                Arrays.asList("value"))).thenReturn(wxStoreMerchantOrderDOS);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure OrderItemService.list(...).
        final WxOrderItemDO wxOrderItemDO = new WxOrderItemDO();
        wxOrderItemDO.setGuid("55696e1c-5509-4e21-83d0-b8cc7a6f19ce");
        wxOrderItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderItemDO.setIsDel(false);
        wxOrderItemDO.setMerchantGuid("merchantGuid");
        final List<WxOrderItemDO> wxOrderItemDOS = Arrays.asList(wxOrderItemDO);
        when(mockOrderItemService.list(any(LambdaQueryWrapper.class))).thenReturn(wxOrderItemDOS);

        // Run the test
        final OrderDetailDTO result = wxOrderManagerUnderTest.detail(req);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxOrderRecordService).dealFastOrderTimeOut("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415");
    }

    @Test
    public void testDetail_BusinessClientServiceReturnsNoItems() {
        // Setup
        final WxOrderDetailReqDTO req = new WxOrderDetailReqDTO("2bef721b-09a0-4d36-96cb-ab4fe055bd2d", 0, 0,
                "memberInfoCardGuid", "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                false);
        final OrderDetailDTO expectedResult = new OrderDetailDTO();
        expectedResult.setMergeInfo("mergeInfo");
        expectedResult.setBrandName("brandName");
        expectedResult.setDiningTableCode("code");
        expectedResult.setAreaGuid("areaGuid");
        expectedResult.setAreaName("areaName");
        expectedResult.setGuestCount(0);
        expectedResult.setOrderState(0);
        expectedResult.setMemberCardGuid("memberCardGuid");
        expectedResult.setLoginStatus(false);

        // Configure WxOrderRecordService.getById(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265",
                "orderGuid", "merchantGuid", "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid",
                "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getById("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415")).thenReturn(wxOrderRecordDO);

        // Configure WxStoreSessionDetailsService.getTableDetail(...).
        final TableDTO tableDTO = TableDTO.builder()
                .storeGuid("storeGuid")
                .storeName("name")
                .areaName("areaName")
                .tableGuid("tableGuid")
                .code("code")
                .build();
        when(mockWxStoreSessionDetailsService.getTableDetail("tableGuid")).thenReturn(tableDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("617502ad-8d54-4801-989d-343e336d8425");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreTableClientService.tableCombineList(...).
        final List<WxStoreTableCombineDTO> wxStoreTableCombineDTOS = Arrays.asList(WxStoreTableCombineDTO.builder()
                .tableGuid("tableGuid")
                .areaName("areaName")
                .tableCode("tableCode")
                .mainOrderGuid("orderGuid")
                .orderGuid("orderGuid")
                .build());
        when(mockWxStoreTableClientService.tableCombineList("tableGuid", "mainOrderGuid"))
                .thenReturn(wxStoreTableCombineDTOS);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        actuallyPayFeeDetailDTO.setPaymentType(0);
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("desc");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setDiningTableGuid("diningTableGuid");
        dineinOrderDetailRespDTO.setGuestCount(0);
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberCardGuid");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure BusinessClientService.calculateSurcharge(...).
        final SurchargeCalculateDTO surchargeDTO = new SurchargeCalculateDTO();
        surchargeDTO.setGuestCount(0);
        surchargeDTO.setTableGuid("diningTableGuid");
        surchargeDTO.setAreaGuid("areaGuid");
        surchargeDTO.setEnterpriseGuid("enterpriseGuid");
        surchargeDTO.setTradeMode(0);
        when(mockBusinessClientService.calculateSurcharge(surchargeDTO)).thenReturn(Collections.emptyList());

        // Configure ItemClientService.getItemPictureUrls(...).
        final List<ItemImgDTO> itemImgDTOS = Arrays.asList(ItemImgDTO.builder().build());
        when(mockItemClientService.getItemPictureUrls(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(itemImgDTOS);

        // Configure WxUserRecordService.getById(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "8274c005-27d6-45b9-900a-8bfc6d676374", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        when(mockWxUserRecordService.getById("userRecordGuid")).thenReturn(wxUserRecordDO);

        // Configure TableClientService.listByWeb(...).
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableTagDTO.setGuid("f6a4e494-4e2e-4b56-a262-6d60e7bce658");
        tableTagDTO.setTagName("tagName");
        tableTagDTO.setSort(0);
        final List<TableBasicDTO> tableBasicDTOS = Arrays.asList(
                new TableBasicDTO("58c00a25-8768-49d0-a868-6f9c8b123e20", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableCode", 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), Arrays.asList("value"), Arrays.asList(tableTagDTO)));
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableClientService.listByWeb(tableBasicQueryDTO)).thenReturn(tableBasicDTOS);

        // Configure WxStoreSessionDetailsService.getBrandInfoDetails(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("guid");
        brandDTO.setUuid("c01c9044-16a9-4615-8df1-6d5aa53f1021");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockWxStoreSessionDetailsService.getBrandInfoDetails("storeGuid")).thenReturn(brandDTO);

        // Configure WxStoreMerchantOrderService.selectByOrderRecordGuid(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("batchCode");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setOpenId("openid");
        wxStoreMerchantOrderDO.setNickName("nickname");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setIsLogin(0);
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setHeadImgUrl("headImgUrl");
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderService.selectByOrderRecordGuid("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415",
                Arrays.asList("value"))).thenReturn(wxStoreMerchantOrderDOS);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure OrderItemService.list(...).
        final WxOrderItemDO wxOrderItemDO = new WxOrderItemDO();
        wxOrderItemDO.setGuid("55696e1c-5509-4e21-83d0-b8cc7a6f19ce");
        wxOrderItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderItemDO.setIsDel(false);
        wxOrderItemDO.setMerchantGuid("merchantGuid");
        final List<WxOrderItemDO> wxOrderItemDOS = Arrays.asList(wxOrderItemDO);
        when(mockOrderItemService.list(any(LambdaQueryWrapper.class))).thenReturn(wxOrderItemDOS);

        // Run the test
        final OrderDetailDTO result = wxOrderManagerUnderTest.detail(req);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxOrderRecordService).dealFastOrderTimeOut("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415");
        verify(mockWxOrderRecordService).updateById(
                new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
    }

    @Test
    public void testDetail_ItemClientServiceReturnsNoItems() {
        // Setup
        final WxOrderDetailReqDTO req = new WxOrderDetailReqDTO("2bef721b-09a0-4d36-96cb-ab4fe055bd2d", 0, 0,
                "memberInfoCardGuid", "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                false);
        final OrderDetailDTO expectedResult = new OrderDetailDTO();
        expectedResult.setMergeInfo("mergeInfo");
        expectedResult.setBrandName("brandName");
        expectedResult.setDiningTableCode("code");
        expectedResult.setAreaGuid("areaGuid");
        expectedResult.setAreaName("areaName");
        expectedResult.setGuestCount(0);
        expectedResult.setOrderState(0);
        expectedResult.setMemberCardGuid("memberCardGuid");
        expectedResult.setLoginStatus(false);

        // Configure WxOrderRecordService.getById(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265",
                "orderGuid", "merchantGuid", "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid",
                "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getById("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415")).thenReturn(wxOrderRecordDO);

        // Configure WxStoreSessionDetailsService.getTableDetail(...).
        final TableDTO tableDTO = TableDTO.builder()
                .storeGuid("storeGuid")
                .storeName("name")
                .areaName("areaName")
                .tableGuid("tableGuid")
                .code("code")
                .build();
        when(mockWxStoreSessionDetailsService.getTableDetail("tableGuid")).thenReturn(tableDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("617502ad-8d54-4801-989d-343e336d8425");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreTableClientService.tableCombineList(...).
        final List<WxStoreTableCombineDTO> wxStoreTableCombineDTOS = Arrays.asList(WxStoreTableCombineDTO.builder()
                .tableGuid("tableGuid")
                .areaName("areaName")
                .tableCode("tableCode")
                .mainOrderGuid("orderGuid")
                .orderGuid("orderGuid")
                .build());
        when(mockWxStoreTableClientService.tableCombineList("tableGuid", "mainOrderGuid"))
                .thenReturn(wxStoreTableCombineDTOS);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        actuallyPayFeeDetailDTO.setPaymentType(0);
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("desc");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setDiningTableGuid("diningTableGuid");
        dineinOrderDetailRespDTO.setGuestCount(0);
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberCardGuid");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure BusinessClientService.calculateSurcharge(...).
        final List<SurchargeLinkDTO> surchargeLinkDTOS = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        final SurchargeCalculateDTO surchargeDTO = new SurchargeCalculateDTO();
        surchargeDTO.setGuestCount(0);
        surchargeDTO.setTableGuid("diningTableGuid");
        surchargeDTO.setAreaGuid("areaGuid");
        surchargeDTO.setEnterpriseGuid("enterpriseGuid");
        surchargeDTO.setTradeMode(0);
        when(mockBusinessClientService.calculateSurcharge(surchargeDTO)).thenReturn(surchargeLinkDTOS);

        when(mockItemClientService.getItemPictureUrls(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(Collections.emptyList());

        // Run the test
        final OrderDetailDTO result = wxOrderManagerUnderTest.detail(req);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxOrderRecordService).dealFastOrderTimeOut("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415");
        verify(mockWxOrderRecordService).updateById(
                new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
    }

    @Test
    public void testDetail_TableClientServiceReturnsNoItems() {
        // Setup
        final WxOrderDetailReqDTO req = new WxOrderDetailReqDTO("2bef721b-09a0-4d36-96cb-ab4fe055bd2d", 0, 0,
                "memberInfoCardGuid", "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                false);
        final OrderDetailDTO expectedResult = new OrderDetailDTO();
        expectedResult.setMergeInfo("mergeInfo");
        expectedResult.setBrandName("brandName");
        expectedResult.setDiningTableCode("code");
        expectedResult.setAreaGuid("areaGuid");
        expectedResult.setAreaName("areaName");
        expectedResult.setGuestCount(0);
        expectedResult.setOrderState(0);
        expectedResult.setMemberCardGuid("memberCardGuid");
        expectedResult.setLoginStatus(false);

        // Configure WxOrderRecordService.getById(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265",
                "orderGuid", "merchantGuid", "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid",
                "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getById("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415")).thenReturn(wxOrderRecordDO);

        // Configure WxStoreSessionDetailsService.getTableDetail(...).
        final TableDTO tableDTO = TableDTO.builder()
                .storeGuid("storeGuid")
                .storeName("name")
                .areaName("areaName")
                .tableGuid("tableGuid")
                .code("code")
                .build();
        when(mockWxStoreSessionDetailsService.getTableDetail("tableGuid")).thenReturn(tableDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("617502ad-8d54-4801-989d-343e336d8425");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreTableClientService.tableCombineList(...).
        final List<WxStoreTableCombineDTO> wxStoreTableCombineDTOS = Arrays.asList(WxStoreTableCombineDTO.builder()
                .tableGuid("tableGuid")
                .areaName("areaName")
                .tableCode("tableCode")
                .mainOrderGuid("orderGuid")
                .orderGuid("orderGuid")
                .build());
        when(mockWxStoreTableClientService.tableCombineList("tableGuid", "mainOrderGuid"))
                .thenReturn(wxStoreTableCombineDTOS);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        actuallyPayFeeDetailDTO.setPaymentType(0);
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("desc");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setDiningTableGuid("diningTableGuid");
        dineinOrderDetailRespDTO.setGuestCount(0);
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberCardGuid");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure BusinessClientService.calculateSurcharge(...).
        final List<SurchargeLinkDTO> surchargeLinkDTOS = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        final SurchargeCalculateDTO surchargeDTO = new SurchargeCalculateDTO();
        surchargeDTO.setGuestCount(0);
        surchargeDTO.setTableGuid("diningTableGuid");
        surchargeDTO.setAreaGuid("areaGuid");
        surchargeDTO.setEnterpriseGuid("enterpriseGuid");
        surchargeDTO.setTradeMode(0);
        when(mockBusinessClientService.calculateSurcharge(surchargeDTO)).thenReturn(surchargeLinkDTOS);

        // Configure ItemClientService.getItemPictureUrls(...).
        final List<ItemImgDTO> itemImgDTOS = Arrays.asList(ItemImgDTO.builder().build());
        when(mockItemClientService.getItemPictureUrls(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(itemImgDTOS);

        // Configure WxUserRecordService.getById(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "8274c005-27d6-45b9-900a-8bfc6d676374", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        when(mockWxUserRecordService.getById("userRecordGuid")).thenReturn(wxUserRecordDO);

        // Configure TableClientService.listByWeb(...).
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableClientService.listByWeb(tableBasicQueryDTO)).thenReturn(Collections.emptyList());

        // Run the test
        final OrderDetailDTO result = wxOrderManagerUnderTest.detail(req);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxOrderRecordService).dealFastOrderTimeOut("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415");
        verify(mockWxOrderRecordService).updateById(
                new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
    }

    @Test
    public void testDetail_WxStoreSessionDetailsServiceGetBrandInfoDetailsReturnsNull() {
        // Setup
        final WxOrderDetailReqDTO req = new WxOrderDetailReqDTO("2bef721b-09a0-4d36-96cb-ab4fe055bd2d", 0, 0,
                "memberInfoCardGuid", "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                false);
        final OrderDetailDTO expectedResult = new OrderDetailDTO();
        expectedResult.setMergeInfo("mergeInfo");
        expectedResult.setBrandName("brandName");
        expectedResult.setDiningTableCode("code");
        expectedResult.setAreaGuid("areaGuid");
        expectedResult.setAreaName("areaName");
        expectedResult.setGuestCount(0);
        expectedResult.setOrderState(0);
        expectedResult.setMemberCardGuid("memberCardGuid");
        expectedResult.setLoginStatus(false);

        // Configure WxOrderRecordService.getById(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265",
                "orderGuid", "merchantGuid", "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid",
                "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getById("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415")).thenReturn(wxOrderRecordDO);

        // Configure WxStoreSessionDetailsService.getTableDetail(...).
        final TableDTO tableDTO = TableDTO.builder()
                .storeGuid("storeGuid")
                .storeName("name")
                .areaName("areaName")
                .tableGuid("tableGuid")
                .code("code")
                .build();
        when(mockWxStoreSessionDetailsService.getTableDetail("tableGuid")).thenReturn(tableDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("617502ad-8d54-4801-989d-343e336d8425");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreTableClientService.tableCombineList(...).
        final List<WxStoreTableCombineDTO> wxStoreTableCombineDTOS = Arrays.asList(WxStoreTableCombineDTO.builder()
                .tableGuid("tableGuid")
                .areaName("areaName")
                .tableCode("tableCode")
                .mainOrderGuid("orderGuid")
                .orderGuid("orderGuid")
                .build());
        when(mockWxStoreTableClientService.tableCombineList("tableGuid", "mainOrderGuid"))
                .thenReturn(wxStoreTableCombineDTOS);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        actuallyPayFeeDetailDTO.setPaymentType(0);
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("desc");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setDiningTableGuid("diningTableGuid");
        dineinOrderDetailRespDTO.setGuestCount(0);
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberCardGuid");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure BusinessClientService.calculateSurcharge(...).
        final List<SurchargeLinkDTO> surchargeLinkDTOS = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        final SurchargeCalculateDTO surchargeDTO = new SurchargeCalculateDTO();
        surchargeDTO.setGuestCount(0);
        surchargeDTO.setTableGuid("diningTableGuid");
        surchargeDTO.setAreaGuid("areaGuid");
        surchargeDTO.setEnterpriseGuid("enterpriseGuid");
        surchargeDTO.setTradeMode(0);
        when(mockBusinessClientService.calculateSurcharge(surchargeDTO)).thenReturn(surchargeLinkDTOS);

        // Configure TableClientService.listByWeb(...).
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableTagDTO.setGuid("f6a4e494-4e2e-4b56-a262-6d60e7bce658");
        tableTagDTO.setTagName("tagName");
        tableTagDTO.setSort(0);
        final List<TableBasicDTO> tableBasicDTOS = Arrays.asList(
                new TableBasicDTO("58c00a25-8768-49d0-a868-6f9c8b123e20", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableCode", 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), Arrays.asList("value"), Arrays.asList(tableTagDTO)));
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableClientService.listByWeb(tableBasicQueryDTO)).thenReturn(tableBasicDTOS);

        when(mockWxStoreSessionDetailsService.getBrandInfoDetails("storeGuid")).thenReturn(null);

        // Configure ItemClientService.getItemPictureUrls(...).
        final List<ItemImgDTO> itemImgDTOS = Arrays.asList(ItemImgDTO.builder().build());
        when(mockItemClientService.getItemPictureUrls(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(itemImgDTOS);

        // Configure WxStoreMerchantOrderService.selectByOrderRecordGuid(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("batchCode");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setOpenId("openid");
        wxStoreMerchantOrderDO.setNickName("nickname");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setIsLogin(0);
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setHeadImgUrl("headImgUrl");
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderService.selectByOrderRecordGuid("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415",
                Arrays.asList("value"))).thenReturn(wxStoreMerchantOrderDOS);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure OrderItemService.list(...).
        final WxOrderItemDO wxOrderItemDO = new WxOrderItemDO();
        wxOrderItemDO.setGuid("55696e1c-5509-4e21-83d0-b8cc7a6f19ce");
        wxOrderItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderItemDO.setIsDel(false);
        wxOrderItemDO.setMerchantGuid("merchantGuid");
        final List<WxOrderItemDO> wxOrderItemDOS = Arrays.asList(wxOrderItemDO);
        when(mockOrderItemService.list(any(LambdaQueryWrapper.class))).thenReturn(wxOrderItemDOS);

        // Run the test
        final OrderDetailDTO result = wxOrderManagerUnderTest.detail(req);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxOrderRecordService).dealFastOrderTimeOut("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415");
        verify(mockWxOrderRecordService).updateById(
                new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
    }

    @Test
    public void testDetail_WxStoreMerchantOrderServiceReturnsNoItems() {
        // Setup
        final WxOrderDetailReqDTO req = new WxOrderDetailReqDTO("2bef721b-09a0-4d36-96cb-ab4fe055bd2d", 0, 0,
                "memberInfoCardGuid", "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                false);
        final OrderDetailDTO expectedResult = new OrderDetailDTO();
        expectedResult.setMergeInfo("mergeInfo");
        expectedResult.setBrandName("brandName");
        expectedResult.setDiningTableCode("code");
        expectedResult.setAreaGuid("areaGuid");
        expectedResult.setAreaName("areaName");
        expectedResult.setGuestCount(0);
        expectedResult.setOrderState(0);
        expectedResult.setMemberCardGuid("memberCardGuid");
        expectedResult.setLoginStatus(false);

        // Configure WxOrderRecordService.getById(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265",
                "orderGuid", "merchantGuid", "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid",
                "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getById("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415")).thenReturn(wxOrderRecordDO);

        // Configure WxStoreSessionDetailsService.getTableDetail(...).
        final TableDTO tableDTO = TableDTO.builder()
                .storeGuid("storeGuid")
                .storeName("name")
                .areaName("areaName")
                .tableGuid("tableGuid")
                .code("code")
                .build();
        when(mockWxStoreSessionDetailsService.getTableDetail("tableGuid")).thenReturn(tableDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("617502ad-8d54-4801-989d-343e336d8425");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreTableClientService.tableCombineList(...).
        final List<WxStoreTableCombineDTO> wxStoreTableCombineDTOS = Arrays.asList(WxStoreTableCombineDTO.builder()
                .tableGuid("tableGuid")
                .areaName("areaName")
                .tableCode("tableCode")
                .mainOrderGuid("orderGuid")
                .orderGuid("orderGuid")
                .build());
        when(mockWxStoreTableClientService.tableCombineList("tableGuid", "mainOrderGuid"))
                .thenReturn(wxStoreTableCombineDTOS);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        actuallyPayFeeDetailDTO.setPaymentType(0);
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("desc");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setDiningTableGuid("diningTableGuid");
        dineinOrderDetailRespDTO.setGuestCount(0);
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberCardGuid");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure BusinessClientService.calculateSurcharge(...).
        final List<SurchargeLinkDTO> surchargeLinkDTOS = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        final SurchargeCalculateDTO surchargeDTO = new SurchargeCalculateDTO();
        surchargeDTO.setGuestCount(0);
        surchargeDTO.setTableGuid("diningTableGuid");
        surchargeDTO.setAreaGuid("areaGuid");
        surchargeDTO.setEnterpriseGuid("enterpriseGuid");
        surchargeDTO.setTradeMode(0);
        when(mockBusinessClientService.calculateSurcharge(surchargeDTO)).thenReturn(surchargeLinkDTOS);

        // Configure TableClientService.listByWeb(...).
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableTagDTO.setGuid("f6a4e494-4e2e-4b56-a262-6d60e7bce658");
        tableTagDTO.setTagName("tagName");
        tableTagDTO.setSort(0);
        final List<TableBasicDTO> tableBasicDTOS = Arrays.asList(
                new TableBasicDTO("58c00a25-8768-49d0-a868-6f9c8b123e20", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableCode", 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), Arrays.asList("value"), Arrays.asList(tableTagDTO)));
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableClientService.listByWeb(tableBasicQueryDTO)).thenReturn(tableBasicDTOS);

        // Configure WxStoreSessionDetailsService.getBrandInfoDetails(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("guid");
        brandDTO.setUuid("c01c9044-16a9-4615-8df1-6d5aa53f1021");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockWxStoreSessionDetailsService.getBrandInfoDetails("storeGuid")).thenReturn(brandDTO);

        when(mockWxStoreMerchantOrderService.selectByOrderRecordGuid("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415",
                Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure ItemClientService.getItemPictureUrls(...).
        final List<ItemImgDTO> itemImgDTOS = Arrays.asList(ItemImgDTO.builder().build());
        when(mockItemClientService.getItemPictureUrls(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(itemImgDTOS);

        // Run the test
        final OrderDetailDTO result = wxOrderManagerUnderTest.detail(req);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxOrderRecordService).dealFastOrderTimeOut("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415");
        verify(mockWxOrderRecordService).updateById(
                new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
    }

    @Test
    public void testDetail_RedisUtilsGetReturnsNull() {
        // Setup
        final WxOrderDetailReqDTO req = new WxOrderDetailReqDTO("2bef721b-09a0-4d36-96cb-ab4fe055bd2d", 0, 0,
                "memberInfoCardGuid", "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                false);
        final OrderDetailDTO expectedResult = new OrderDetailDTO();
        expectedResult.setMergeInfo("mergeInfo");
        expectedResult.setBrandName("brandName");
        expectedResult.setDiningTableCode("code");
        expectedResult.setAreaGuid("areaGuid");
        expectedResult.setAreaName("areaName");
        expectedResult.setGuestCount(0);
        expectedResult.setOrderState(0);
        expectedResult.setMemberCardGuid("memberCardGuid");
        expectedResult.setLoginStatus(false);

        // Configure WxOrderRecordService.getById(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265",
                "orderGuid", "merchantGuid", "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid",
                "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getById("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415")).thenReturn(wxOrderRecordDO);

        // Configure WxStoreSessionDetailsService.getTableDetail(...).
        final TableDTO tableDTO = TableDTO.builder()
                .storeGuid("storeGuid")
                .storeName("name")
                .areaName("areaName")
                .tableGuid("tableGuid")
                .code("code")
                .build();
        when(mockWxStoreSessionDetailsService.getTableDetail("tableGuid")).thenReturn(tableDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("617502ad-8d54-4801-989d-343e336d8425");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreTableClientService.tableCombineList(...).
        final List<WxStoreTableCombineDTO> wxStoreTableCombineDTOS = Arrays.asList(WxStoreTableCombineDTO.builder()
                .tableGuid("tableGuid")
                .areaName("areaName")
                .tableCode("tableCode")
                .mainOrderGuid("orderGuid")
                .orderGuid("orderGuid")
                .build());
        when(mockWxStoreTableClientService.tableCombineList("tableGuid", "mainOrderGuid"))
                .thenReturn(wxStoreTableCombineDTOS);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        actuallyPayFeeDetailDTO.setPaymentType(0);
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("desc");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setDiningTableGuid("diningTableGuid");
        dineinOrderDetailRespDTO.setGuestCount(0);
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberCardGuid");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure BusinessClientService.calculateSurcharge(...).
        final List<SurchargeLinkDTO> surchargeLinkDTOS = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        final SurchargeCalculateDTO surchargeDTO = new SurchargeCalculateDTO();
        surchargeDTO.setGuestCount(0);
        surchargeDTO.setTableGuid("diningTableGuid");
        surchargeDTO.setAreaGuid("areaGuid");
        surchargeDTO.setEnterpriseGuid("enterpriseGuid");
        surchargeDTO.setTradeMode(0);
        when(mockBusinessClientService.calculateSurcharge(surchargeDTO)).thenReturn(surchargeLinkDTOS);

        // Configure TableClientService.listByWeb(...).
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableTagDTO.setGuid("f6a4e494-4e2e-4b56-a262-6d60e7bce658");
        tableTagDTO.setTagName("tagName");
        tableTagDTO.setSort(0);
        final List<TableBasicDTO> tableBasicDTOS = Arrays.asList(
                new TableBasicDTO("58c00a25-8768-49d0-a868-6f9c8b123e20", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableCode", 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), Arrays.asList("value"), Arrays.asList(tableTagDTO)));
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableClientService.listByWeb(tableBasicQueryDTO)).thenReturn(tableBasicDTOS);

        // Configure WxStoreSessionDetailsService.getBrandInfoDetails(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("guid");
        brandDTO.setUuid("c01c9044-16a9-4615-8df1-6d5aa53f1021");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockWxStoreSessionDetailsService.getBrandInfoDetails("storeGuid")).thenReturn(brandDTO);

        // Configure WxStoreMerchantOrderService.selectByOrderRecordGuid(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("batchCode");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setOpenId("openid");
        wxStoreMerchantOrderDO.setNickName("nickname");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setIsLogin(0);
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setHeadImgUrl("headImgUrl");
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderService.selectByOrderRecordGuid("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415",
                Arrays.asList("value"))).thenReturn(wxStoreMerchantOrderDOS);

        when(mockRedisUtils.get("key")).thenReturn(null);

        // Configure ItemClientService.getItemPictureUrls(...).
        final List<ItemImgDTO> itemImgDTOS = Arrays.asList(ItemImgDTO.builder().build());
        when(mockItemClientService.getItemPictureUrls(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(itemImgDTOS);

        // Configure HsaBaseClientService.getMemberState(...).
        final ResponseAccountStatus responseAccountStatus = new ResponseAccountStatus();
        responseAccountStatus.setMemberInfoGuid("memberInfoGuid");
        responseAccountStatus.setOperSubjectGuid("operSubjectGuid");
        responseAccountStatus.setPhoneNum("phoneNum");
        responseAccountStatus.setNickName("nickName");
        responseAccountStatus.setAccountState(0);
        final ResponseModel<ResponseAccountStatus> responseAccountStatusResponseModel = new ResponseModel<>(
                responseAccountStatus);
        when(mockHsaBaseClientService.getMemberState("openid", "enterpriseGuid"))
                .thenReturn(responseAccountStatusResponseModel);

        // Configure OrderItemService.list(...).
        final WxOrderItemDO wxOrderItemDO = new WxOrderItemDO();
        wxOrderItemDO.setGuid("55696e1c-5509-4e21-83d0-b8cc7a6f19ce");
        wxOrderItemDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderItemDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxOrderItemDO.setIsDel(false);
        wxOrderItemDO.setMerchantGuid("merchantGuid");
        final List<WxOrderItemDO> wxOrderItemDOS = Arrays.asList(wxOrderItemDO);
        when(mockOrderItemService.list(any(LambdaQueryWrapper.class))).thenReturn(wxOrderItemDOS);

        // Run the test
        final OrderDetailDTO result = wxOrderManagerUnderTest.detail(req);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxOrderRecordService).dealFastOrderTimeOut("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415");
        verify(mockWxOrderRecordService).updateById(
                new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        verify(mockRedisUtils).setNx("key", "accountState", 300L);
    }

    @Test
    public void testDetail_OrderItemServiceReturnsNoItems() {
        // Setup
        final WxOrderDetailReqDTO req = new WxOrderDetailReqDTO("2bef721b-09a0-4d36-96cb-ab4fe055bd2d", 0, 0,
                "memberInfoCardGuid", "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                false);
        final OrderDetailDTO expectedResult = new OrderDetailDTO();
        expectedResult.setMergeInfo("mergeInfo");
        expectedResult.setBrandName("brandName");
        expectedResult.setDiningTableCode("code");
        expectedResult.setAreaGuid("areaGuid");
        expectedResult.setAreaName("areaName");
        expectedResult.setGuestCount(0);
        expectedResult.setOrderState(0);
        expectedResult.setMemberCardGuid("memberCardGuid");
        expectedResult.setLoginStatus(false);

        // Configure WxOrderRecordService.getById(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265",
                "orderGuid", "merchantGuid", "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid",
                "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName",
                new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getById("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415")).thenReturn(wxOrderRecordDO);

        // Configure WxStoreSessionDetailsService.getTableDetail(...).
        final TableDTO tableDTO = TableDTO.builder()
                .storeGuid("storeGuid")
                .storeName("name")
                .areaName("areaName")
                .tableGuid("tableGuid")
                .code("code")
                .build();
        when(mockWxStoreSessionDetailsService.getTableDetail("tableGuid")).thenReturn(tableDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("617502ad-8d54-4801-989d-343e336d8425");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreTableClientService.tableCombineList(...).
        final List<WxStoreTableCombineDTO> wxStoreTableCombineDTOS = Arrays.asList(WxStoreTableCombineDTO.builder()
                .tableGuid("tableGuid")
                .areaName("areaName")
                .tableCode("tableCode")
                .mainOrderGuid("orderGuid")
                .orderGuid("orderGuid")
                .build());
        when(mockWxStoreTableClientService.tableCombineList("tableGuid", "mainOrderGuid"))
                .thenReturn(wxStoreTableCombineDTOS);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        actuallyPayFeeDetailDTO.setPaymentType(0);
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("desc");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setDiningTableGuid("diningTableGuid");
        dineinOrderDetailRespDTO.setGuestCount(0);
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberCardGuid");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure BusinessClientService.calculateSurcharge(...).
        final List<SurchargeLinkDTO> surchargeLinkDTOS = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        final SurchargeCalculateDTO surchargeDTO = new SurchargeCalculateDTO();
        surchargeDTO.setGuestCount(0);
        surchargeDTO.setTableGuid("diningTableGuid");
        surchargeDTO.setAreaGuid("areaGuid");
        surchargeDTO.setEnterpriseGuid("enterpriseGuid");
        surchargeDTO.setTradeMode(0);
        when(mockBusinessClientService.calculateSurcharge(surchargeDTO)).thenReturn(surchargeLinkDTOS);

        // Configure ItemClientService.getItemPictureUrls(...).
        final List<ItemImgDTO> itemImgDTOS = Arrays.asList(ItemImgDTO.builder().build());
        when(mockItemClientService.getItemPictureUrls(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(itemImgDTOS);

        // Configure TableClientService.listByWeb(...).
        final TableTagDTO tableTagDTO = new TableTagDTO();
        tableTagDTO.setGuid("f6a4e494-4e2e-4b56-a262-6d60e7bce658");
        tableTagDTO.setTagName("tagName");
        tableTagDTO.setSort(0);
        final List<TableBasicDTO> tableBasicDTOS = Arrays.asList(
                new TableBasicDTO("58c00a25-8768-49d0-a868-6f9c8b123e20", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableCode", 0, 0, 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), Arrays.asList("value"), Arrays.asList(tableTagDTO)));
        final TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
        tableBasicQueryDTO.setStoreGuid("storeGuid");
        tableBasicQueryDTO.setAreaGuid("areaGuid");
        tableBasicQueryDTO.setTableGuidList(Arrays.asList("value"));
        when(mockTableClientService.listByWeb(tableBasicQueryDTO)).thenReturn(tableBasicDTOS);

        // Configure WxStoreSessionDetailsService.getBrandInfoDetails(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("guid");
        brandDTO.setUuid("c01c9044-16a9-4615-8df1-6d5aa53f1021");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockWxStoreSessionDetailsService.getBrandInfoDetails("storeGuid")).thenReturn(brandDTO);

        // Configure WxStoreMerchantOrderService.selectByOrderRecordGuid(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("batchCode");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setOpenId("openid");
        wxStoreMerchantOrderDO.setNickName("nickname");
        wxStoreMerchantOrderDO.setActualGuestsNo(0);
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setRemark("remark");
        wxStoreMerchantOrderDO.setIsLogin(0);
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setHeadImgUrl("headImgUrl");
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderService.selectByOrderRecordGuid("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415",
                Arrays.asList("value"))).thenReturn(wxStoreMerchantOrderDOS);

        when(mockRedisUtils.get("key")).thenReturn("result");
        when(mockOrderItemService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final OrderDetailDTO result = wxOrderManagerUnderTest.detail(req);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxOrderRecordService).dealFastOrderTimeOut("5f1a6cff-cc45-4e5e-a576-d20aa9cc6415");
        verify(mockWxOrderRecordService).updateById(
                new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
    }

    @Test
    public void testInitOrderItemImg() {
        // Setup
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        actuallyPayFeeDetailDTO.setPaymentType(0);
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("desc");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setDiningTableGuid("diningTableGuid");
        dineinOrderDetailRespDTO.setGuestCount(0);
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberCardGuid");
        final OrderDetailHelper detailBuilder = new OrderDetailHelper(
                new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0),
                dineinOrderDetailRespDTO, false, false, "memberInfoCardGuid");

        // Configure ItemClientService.getItemPictureUrls(...).
        final List<ItemImgDTO> itemImgDTOS = Arrays.asList(ItemImgDTO.builder().build());
        when(mockItemClientService.getItemPictureUrls(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(itemImgDTOS);

        // Run the test
        wxOrderManagerUnderTest.initOrderItemImg(detailBuilder);

        // Verify the results
    }

    @Test
    public void testInitOrderItemImg_ItemClientServiceReturnsNoItems() {
        // Setup
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        actuallyPayFeeDetailDTO.setPaymentType(0);
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("desc");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setDiningTableGuid("diningTableGuid");
        dineinOrderDetailRespDTO.setGuestCount(0);
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberCardGuid");
        final OrderDetailHelper detailBuilder = new OrderDetailHelper(
                new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0),
                dineinOrderDetailRespDTO, false, false, "memberInfoCardGuid");
        when(mockItemClientService.getItemPictureUrls(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(Collections.emptyList());

        // Run the test
        wxOrderManagerUnderTest.initOrderItemImg(detailBuilder);

        // Verify the results
    }

    @Test
    public void testGetmemberStateByCache() {
        // Setup
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final int result = wxOrderManagerUnderTest.getmemberStateByCache("openid", "enterpriseGuid");

        // Verify the results
        assertEquals(0, result);
    }

    @Test
    public void testGetmemberStateByCache_RedisUtilsGetReturnsNull() {
        // Setup
        when(mockRedisUtils.get("key")).thenReturn(null);

        // Configure HsaBaseClientService.getMemberState(...).
        final ResponseAccountStatus responseAccountStatus = new ResponseAccountStatus();
        responseAccountStatus.setMemberInfoGuid("memberInfoGuid");
        responseAccountStatus.setOperSubjectGuid("operSubjectGuid");
        responseAccountStatus.setPhoneNum("phoneNum");
        responseAccountStatus.setNickName("nickName");
        responseAccountStatus.setAccountState(0);
        final ResponseModel<ResponseAccountStatus> responseAccountStatusResponseModel = new ResponseModel<>(
                responseAccountStatus);
        when(mockHsaBaseClientService.getMemberState("openid", "enterpriseGuid"))
                .thenReturn(responseAccountStatusResponseModel);

        // Run the test
        final int result = wxOrderManagerUnderTest.getmemberStateByCache("openid", "enterpriseGuid");

        // Verify the results
        assertEquals(0, result);
        verify(mockRedisUtils).setNx("key", "accountState", 300L);
    }

    @Test
    public void testInitAreaMap() {
        // Setup
        final WxOrderRecordDO orderRecord = new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265", "orderGuid",
                "merchantGuid", "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName",
                "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        actuallyPayFeeDetailDTO.setPaymentType(0);
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("desc");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setDiningTableGuid("diningTableGuid");
        dineinOrderDetailRespDTO.setGuestCount(0);
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberCardGuid");
        final OrderDetailHelper detailBuilder = new OrderDetailHelper(
                new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0),
                dineinOrderDetailRespDTO, false, false, "memberInfoCardGuid");

        // Configure WxStoreTableClientService.tableCombineList(...).
        final List<WxStoreTableCombineDTO> wxStoreTableCombineDTOS = Arrays.asList(WxStoreTableCombineDTO.builder()
                .tableGuid("tableGuid")
                .areaName("areaName")
                .tableCode("tableCode")
                .mainOrderGuid("orderGuid")
                .orderGuid("orderGuid")
                .build());
        when(mockWxStoreTableClientService.tableCombineList("tableGuid", "mainOrderGuid"))
                .thenReturn(wxStoreTableCombineDTOS);

        // Run the test
        wxOrderManagerUnderTest.initAreaMap(orderRecord, detailBuilder);

        // Verify the results
        verify(mockWxOrderRecordService).updateById(
                new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
    }

    @Test
    public void testInitAreaMap_WxStoreTableClientServiceReturnsNoItems() {
        // Setup
        final WxOrderRecordDO orderRecord = new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265", "orderGuid",
                "merchantGuid", "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName",
                "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        actuallyPayFeeDetailDTO.setPaymentType(0);
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("desc");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setDiningTableGuid("diningTableGuid");
        dineinOrderDetailRespDTO.setGuestCount(0);
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberCardGuid");
        final OrderDetailHelper detailBuilder = new OrderDetailHelper(
                new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0),
                dineinOrderDetailRespDTO, false, false, "memberInfoCardGuid");
        when(mockWxStoreTableClientService.tableCombineList("tableGuid", "mainOrderGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        wxOrderManagerUnderTest.initAreaMap(orderRecord, detailBuilder);

        // Verify the results
    }

    @Test
    public void testGetTradeOrderDetail() {
        // Setup
        final DineinOrderDetailRespDTO expectedResult = new DineinOrderDetailRespDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setUpperState(0);
        expectedResult.setMainOrderGuid("orderGuid");
        expectedResult.setTradeMode(0);
        expectedResult.setAppendFee(new BigDecimal("0.00"));
        expectedResult.setActuallyPayFee(new BigDecimal("0.00"));
        expectedResult.setDiscountFee(new BigDecimal("0.00"));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setAmount(new BigDecimal("0.00"));
        actuallyPayFeeDetailDTO.setPaymentType(0);
        expectedResult.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("desc");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        expectedResult.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        expectedResult.setState(0);
        expectedResult.setDiningTableGuid("diningTableGuid");
        expectedResult.setGuestCount(0);
        expectedResult.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        expectedResult.setMemberCardGuid("memberCardGuid");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setAmount(new BigDecimal("0.00"));
        actuallyPayFeeDetailDTO1.setPaymentType(0);
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("desc");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setDiningTableGuid("diningTableGuid");
        dineinOrderDetailRespDTO.setGuestCount(0);
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberCardGuid");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("orderGuid")
                .datas(Arrays.asList("value"))
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Run the test
        final DineinOrderDetailRespDTO result = wxOrderManagerUnderTest.getTradeOrderDetail("orderGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUpdateOrderRecord() {
        // Setup
        // Run the test
        wxOrderManagerUnderTest.updateOrderRecord("1a86d901-94df-4078-9d56-320598ab5265", "orderGuid");

        // Verify the results
        verify(mockWxOrderRecordService).updateById(
                new WxOrderRecordDO(0L, "1a86d901-94df-4078-9d56-320598ab5265", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
    }
}
