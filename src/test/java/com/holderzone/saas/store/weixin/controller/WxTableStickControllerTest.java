package com.holderzone.saas.store.weixin.controller;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.weixin.WxCategoryDTO;
import com.holderzone.saas.store.dto.weixin.WxStickDownloadTableDTO;
import com.holderzone.saas.store.dto.weixin.WxTableStickDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickDownloadReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickIsModelDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickModelReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxBrandAuthRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickDownloadRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickModelRespDTO;
import com.holderzone.saas.store.weixin.config.WeChatConfig;
import com.holderzone.saas.store.weixin.service.WxStoreAuthorizerInfoService;
import com.holderzone.saas.store.weixin.service.WxStoreTableStickService;
import com.holderzone.saas.store.weixin.service.rpc.WxStickModelClientService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxTableStickController.class)
public class WxTableStickControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStoreTableStickService mockWxStoreTableStickService;
    @MockBean
    private WxStickModelClientService mockWxStickModelClientService;
    @MockBean
    private WxStoreAuthorizerInfoService mockWxStoreAuthorizerInfoService;
    @MockBean
    private RedisUtils mockRedisUtils;
    @MockBean
    private WeChatConfig mockWeChatConfig;

    @Test
    public void testListModelAndTicket() throws Exception {
        // Setup
        // Configure WxStoreTableStickService.listTableStick(...).
        final List<WxTableStickDTO> wxTableStickDTOS = Arrays.asList(
                new WxTableStickDTO("9147021d-5deb-4ca9-9bd4-6019244e6bdd", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow",
                        "tableNumberText", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid",
                        "isEnable", "isDelete", 0L, 0L, "categoryName", "previewImg", 0));
        when(mockWxStoreTableStickService.listTableStick(0)).thenReturn(wxTableStickDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_table_stick/list_model_and_stick")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListModelAndTicket_WxStoreTableStickServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockWxStoreTableStickService.listTableStick(0)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_table_stick/list_model_and_stick")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testSaveOrUpdate() throws Exception {
        // Setup
        when(mockWxStoreTableStickService.addOrUpdateStick(
                new WxTableStickDTO("9147021d-5deb-4ca9-9bd4-6019244e6bdd", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow",
                        "tableNumberText", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid",
                        "isEnable", "isDelete", 0L, 0L, "categoryName", "previewImg", 0))).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_table_stick/save_or_update_stick")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testSaveOrUpdate_WxStoreTableStickServiceReturnsTrue() throws Exception {
        // Setup
        when(mockWxStoreTableStickService.addOrUpdateStick(
                new WxTableStickDTO("9147021d-5deb-4ca9-9bd4-6019244e6bdd", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow",
                        "tableNumberText", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid",
                        "isEnable", "isDelete", 0L, 0L, "categoryName", "previewImg", 0))).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_table_stick/save_or_update_stick")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testFindByGuid() throws Exception {
        // Setup
        // Configure WxStoreTableStickService.findByGuid(...).
        final WxTableStickDTO wxTableStickDTO = new WxTableStickDTO("9147021d-5deb-4ca9-9bd4-6019244e6bdd", "category",
                new BigDecimal("0.00"), new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit",
                "bgUrl", "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                "sponsorsTextColor", "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow",
                "tableNumberText", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid", "isEnable",
                "isDelete", 0L, 0L, "categoryName", "previewImg", 0);
        when(mockWxStoreTableStickService.findByGuid("73bb7918-1e10-4904-b499-6179a8a9cc64"))
                .thenReturn(wxTableStickDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_table_stick/find_by_guid")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListWxTableStick() throws Exception {
        // Setup
        // Configure WxStoreTableStickService.listStickModel(...).
        final List<WxStickModelRespDTO> wxStickModelRespDTOS = Arrays.asList(
                new WxStickModelRespDTO("e299d885-9a70-4064-9fcf-e72bcfa8f7e7", "name", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "previewImg", 0));
        when(mockWxStoreTableStickService.listStickModel(
                new WxStickModelReqDTO(Arrays.asList("value"), 0, "category"))).thenReturn(wxStickModelRespDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_table_stick/list_stick_model")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListWxTableStick_WxStoreTableStickServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockWxStoreTableStickService.listStickModel(
                new WxStickModelReqDTO(Arrays.asList("value"), 0, "category"))).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_table_stick/list_stick_model")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testListStickCategory() throws Exception {
        // Setup
        // Configure WxStickModelClientService.listCategory(...).
        final List<WxCategoryDTO> wxCategoryDTOS = Arrays.asList(
                new WxCategoryDTO("storeGuid", "enterpriseGuid", "messageType", "eefb24b0-12f6-4419-86e3-9aaad0713b69",
                        "categoryName", 0, 0L, "isDelete"));
        when(mockWxStickModelClientService.listCategory()).thenReturn(wxCategoryDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_table_stick/list_stick_category")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListStickCategory_WxStickModelClientServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockWxStickModelClientService.listCategory()).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_table_stick/list_stick_category")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testDeleteMyStick() throws Exception {
        // Setup
        when(mockWxStoreTableStickService.deleteMyStick(
                new WxStickIsModelDTO("ae4d31d0-4015-4410-9983-d04fbb723a1f", 0))).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_table_stick/delete_my_stick")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testDeleteMyStick_WxStoreTableStickServiceReturnsTrue() throws Exception {
        // Setup
        when(mockWxStoreTableStickService.deleteMyStick(
                new WxStickIsModelDTO("ae4d31d0-4015-4410-9983-d04fbb723a1f", 0))).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_table_stick/delete_my_stick")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testDownloadStickZip() throws Exception {
        // Setup
        // Configure WxStoreTableStickService.downloadStickZip(...).
        final WxStickDownloadRespDTO wxStickDownloadRespDTO = new WxStickDownloadRespDTO("fileName",
                "content".getBytes(), "exception");
        when(mockWxStoreTableStickService.downloadStickZip("downloadKey")).thenReturn(wxStickDownloadRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_table_stick/download_stick_zip")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testCreateStickZip() throws Exception {
        // Setup
        // Configure WxStoreAuthorizerInfoService.getByBrandGuid(...).
        final WxBrandAuthRespDTO wxBrandAuthRespDTO = new WxBrandAuthRespDTO("brandGuid", "brandName", "nickName",
                "mpType", "mpAppId", Arrays.asList(0), "qrcodeUrl", 0, "multiMemberGuid", "multiMemberName");
        when(mockWxStoreAuthorizerInfoService.getByBrandGuid("brandGuid")).thenReturn(wxBrandAuthRespDTO);

        when(mockWeChatConfig.getTongChiDaoTableTicketInfo()).thenReturn("result");
        when(mockWxStoreTableStickService.isWeatherConfig("storeGuid")).thenReturn(true);
        when(mockRedisUtils.generateGuid("redisKey")).thenReturn("downloadKey");

        // Run the test and verify the results
        mockMvc.perform(post("/wx_table_stick/create_stick_zip")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockWxStoreTableStickService).createStickZip(
                new WxStickDownloadReqDTO("downloadKey", 0, "stickGuid", "storeName", "storeGuid", "brandGuid",
                        Arrays.asList(new WxStickDownloadTableDTO("areaGuid", "areaName", "tableGuid", "tableName"))),
                "enterpriseGuid", new JSONObject(0, false));
    }

    @Test
    public void testCreateStickZip_WxStoreTableStickServiceIsWeatherConfigReturnsFalse() throws Exception {
        // Setup
        // Configure WxStoreAuthorizerInfoService.getByBrandGuid(...).
        final WxBrandAuthRespDTO wxBrandAuthRespDTO = new WxBrandAuthRespDTO("brandGuid", "brandName", "nickName",
                "mpType", "mpAppId", Arrays.asList(0), "qrcodeUrl", 0, "multiMemberGuid", "multiMemberName");
        when(mockWxStoreAuthorizerInfoService.getByBrandGuid("brandGuid")).thenReturn(wxBrandAuthRespDTO);

        when(mockWeChatConfig.getTongChiDaoTableTicketInfo()).thenReturn("result");
        when(mockWxStoreTableStickService.isWeatherConfig("storeGuid")).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_table_stick/create_stick_zip")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is5xxServerError())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQrRedirect() throws Exception {
        // Setup
        when(mockWxStoreTableStickService.getRedirectUrl("enterpriseTable", "lang")).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(get("/wx_table_stick/qr_redirect")
                        .param("enterpriseTable", "enterpriseTable")
                        .param("lang", "lang")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
