package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.organization.QueryStoreDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.weixin.service.rpc.OrganizationClientService;
import org.apache.commons.lang3.tuple.Triple;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxOrganizationServiceImplTest {

    @Mock
    private OrganizationClientService mockOrganizationClientService;

    private WxOrganizationServiceImpl wxOrganizationServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxOrganizationServiceImplUnderTest = new WxOrganizationServiceImpl();
        wxOrganizationServiceImplUnderTest.organizationClientService = mockOrganizationClientService;
    }

    @Test
    public void testGetStoreConfig() {
        // Setup
        final WxStorePageReqDTO wxStorePageReqDTO = WxStorePageReqDTO.builder()
                .storeName("storeName")
                .build();

        // Configure OrganizationClientService.queryStoreByCondition(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("2d140092-b857-47b7-880f-3fd81599a1be");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        final Page<StoreDTO> storeDTOPage = new Page<>(0L, 0L, Arrays.asList(storeDTO));
        final QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setIsEnable(0);
        queryStoreDTO.setStoreName("storeName");
        when(mockOrganizationClientService.queryStoreByCondition(queryStoreDTO)).thenReturn(storeDTOPage);

        // Run the test
        final Triple<Page<StoreDTO>, List<StoreDTO>, List<String>> result = wxOrganizationServiceImplUnderTest.getStoreConfig(
                wxStorePageReqDTO);

        // Verify the results
    }

    @Test
    public void testGetStoreDTOByGuid() {
        // Setup
        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("2d140092-b857-47b7-880f-3fd81599a1be");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Run the test
        final StoreDTO result = wxOrganizationServiceImplUnderTest.getStoreDTOByGuid("storeGuid");

        // Verify the results
    }

    @Test
    public void testGetStorePage() {
        // Setup
        final WxStorePageReqDTO wxStorePageReqDTO = WxStorePageReqDTO.builder()
                .storeName("storeName")
                .build();

        // Configure OrganizationClientService.queryStoreByCondition(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("2d140092-b857-47b7-880f-3fd81599a1be");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        final Page<StoreDTO> storeDTOPage = new Page<>(0L, 0L, Arrays.asList(storeDTO));
        final QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setIsEnable(0);
        queryStoreDTO.setStoreName("storeName");
        when(mockOrganizationClientService.queryStoreByCondition(queryStoreDTO)).thenReturn(storeDTOPage);

        // Run the test
        final Page<StoreDTO> result = wxOrganizationServiceImplUnderTest.getStorePage(wxStorePageReqDTO);

        // Verify the results
    }
}
