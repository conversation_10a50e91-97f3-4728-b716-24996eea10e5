package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.weixin.open.WxOpenAuthDTO;
import com.holderzone.saas.store.dto.weixin.req.WxCommonReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPreCodReqDTO;
import com.holderzone.saas.store.weixin.service.WxStoreAuthorizerInfoService;
import com.holderzone.saas.store.weixin.service.WxStoreComponentConfigService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import me.chanjar.weixin.common.error.WxErrorException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.io.UnsupportedEncodingException;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxThirdOpenController.class)
public class WxThirdOpenControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStoreComponentConfigService mockWxThirdOpenService;
    @MockBean
    private WxStoreAuthorizerInfoService mockWxStoreAuthorizerInfoService;
    @MockBean
    private DynamicHelper mockDynamicHelper;

    @Test
    public void testReceiveVerifyTicket() throws Exception {
        // Setup
        // Configure WxStoreComponentConfigService.receiveTicket(...).
        final WxCommonReqDTO wxCommonReqDTO = new WxCommonReqDTO();
        wxCommonReqDTO.setSignature("signature");
        wxCommonReqDTO.setEchostr("echostr");
        wxCommonReqDTO.setTimestamp("timestamp");
        wxCommonReqDTO.setNonce("nonce");
        wxCommonReqDTO.setEncrypt_type("encrypt_type");
        when(mockWxThirdOpenService.receiveTicket(wxCommonReqDTO)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/wx_open/receive_ticket")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetPreCode() throws Exception {
        // Setup
        when(mockWxStoreAuthorizerInfoService.getPreAuthCode(
                new WxPreCodReqDTO("brandGuid", "enterpriseGuid", "appId", 0,"",""))).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/wx_open/get_pre_code")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetPreCode_WxStoreAuthorizerInfoServiceThrowsWxErrorException() throws Exception {
        // Setup
        when(mockWxStoreAuthorizerInfoService.getPreAuthCode(
                new WxPreCodReqDTO("brandGuid", "enterpriseGuid", "appId", 0,"",""))).thenThrow(WxErrorException.class);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_open/get_pre_code")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is5xxServerError())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryAuth() throws Exception {
        // Setup
        when(mockWxStoreAuthorizerInfoService.queryAuth(
                new WxOpenAuthDTO("auth_code", 0L, "brandGuid", "enterpriseGuid"))).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/wx_open/query_auth")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testQueryAuth_WxStoreAuthorizerInfoServiceThrowsWxErrorException() throws Exception {
        // Setup
        when(mockWxStoreAuthorizerInfoService.queryAuth(
                new WxOpenAuthDTO("auth_code", 0L, "brandGuid", "enterpriseGuid"))).thenThrow(WxErrorException.class);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_open/query_auth")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is5xxServerError())
                .andExpect(content().json("{}", true));
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testQueryAuth_WxStoreAuthorizerInfoServiceThrowsUnsupportedEncodingException() throws Exception {
        // Setup
        when(mockWxStoreAuthorizerInfoService.queryAuth(
                new WxOpenAuthDTO("auth_code", 0L, "brandGuid", "enterpriseGuid")))
                .thenThrow(UnsupportedEncodingException.class);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_open/query_auth")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is5xxServerError())
                .andExpect(content().json("{}", true));
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }
}
