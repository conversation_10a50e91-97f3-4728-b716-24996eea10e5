package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.queue.*;
import com.holderzone.saas.store.dto.weixin.WxInQueueReqDTO;
import com.holderzone.saas.store.dto.weixin.WxQueueDetailDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxQueueInfoReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxInQueueRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxTotalQueueDTO;
import com.holderzone.saas.store.weixin.service.WxOpenMessageService;
import com.holderzone.saas.store.weixin.service.rpc.OrganizationClientService;
import com.holderzone.saas.store.weixin.service.rpc.QueueClientService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import me.chanjar.weixin.common.error.WxErrorException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class WxQueueServiceImplTest {

    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private QueueClientService mockQueueClientService;
    @Mock
    private OrganizationClientService mockOrganizationClientService;
    @Mock
    private WxOpenMessageService mockWxOpenMessageService;

    private WxQueueServiceImpl wxQueueServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxQueueServiceImplUnderTest = new WxQueueServiceImpl();
        wxQueueServiceImplUnderTest.dynamicHelper = mockDynamicHelper;
        wxQueueServiceImplUnderTest.redisUtils = mockRedisUtils;
        wxQueueServiceImplUnderTest.queueClientService = mockQueueClientService;
        wxQueueServiceImplUnderTest.organizationClientService = mockOrganizationClientService;
        wxQueueServiceImplUnderTest.wxOpenMessageService = mockWxOpenMessageService;
    }

    @Test
    public void testGetDetail() {
        // Setup
        final WxPortalReqDTO wxPortalReqDTO = WxPortalReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .msgKey("msgKey")
                .build();
        final WxQueueDetailDTO expectedResult = WxQueueDetailDTO.builder()
                .brandGuid("brandGuid")
                .brandName("brandName")
                .brandLogo("brandLogo")
                .storeConfigDTO(new StoreConfigDTO())
                .code(0)
                .build();
        when(mockRedisUtils.get("msgKey")).thenReturn("result");

        // Configure QueueClientService.obtain(...).
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("878c3e69-237b-4767-a935-7ae3e1a73fbe");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setLogoUrl("logoUrl");
        holderQueueDTO.setIsEnableRecovery(false);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("openId");
        holderQueueItemDetailDTO.setEnterpriseGuid("enterpriseGuid");
        holderQueueItemDetailDTO.setStoreName("name");
        holderQueueItemDetailDTO.setGuid("msgKey");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setContact("nickName");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setGender(false);
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        final HolderQueueQueueRecordDTO holderQueueQueueRecordDTO = new HolderQueueQueueRecordDTO(holderQueueDTO,
                holderQueueItemDetailDTO);
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("openId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("name");
        dto.setItemGuid("msgKey");
        dto.setEnterpriseGuid("enterpriseGuid");
        when(mockQueueClientService.obtain(dto)).thenReturn(holderQueueQueueRecordDTO);

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure OrganizationClientService.queryBrandByGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setUuid("9fd72e5d-9be5-4894-b089-41ec03bdc638");
        brandDTO.setName("brandName");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("brandLogo");
        when(mockOrganizationClientService.queryBrandByGuid("belongBrandGuid")).thenReturn(brandDTO);

        // Configure QueueClientService.query(...).
        final StoreConfigDTO storeConfigDTO = new StoreConfigDTO();
        storeConfigDTO.setGuid("ab7fe3b3-c69d-4cd4-89d1-e319ac2acaaf");
        storeConfigDTO.setStoreGuid("storeGuid");
        storeConfigDTO.setIsEnableEat(false);
        storeConfigDTO.setIsEnableRecovery(false);
        storeConfigDTO.setRecoveryNum("recoveryNum");
        when(mockQueueClientService.query("guid")).thenReturn(storeConfigDTO);

        // Run the test
        final WxQueueDetailDTO result = wxQueueServiceImplUnderTest.getDetail(wxPortalReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testInQueueByWx() throws Exception {
        // Setup
        final WxInQueueReqDTO wxInQueueReqDTO = new WxInQueueReqDTO(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .sex(0)
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("belongBrandGuid")
                .build(), (byte) 0b0, "storeGuid");
        final WxInQueueRespDTO expectedResult = new WxInQueueRespDTO(0, "respMsg");
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure QueueClientService.inQueue(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("openId");
        holderQueueItemDetailDTO.setEnterpriseGuid("enterpriseGuid");
        holderQueueItemDetailDTO.setStoreName("name");
        holderQueueItemDetailDTO.setGuid("msgKey");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setContact("nickName");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setGender(false);
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        final HolderQueueItemDTO holderQueueItemDTO = new HolderQueueItemDTO();
        holderQueueItemDTO.setDeviceType(0);
        holderQueueItemDTO.setDeviceId("openId");
        holderQueueItemDTO.setEnterpriseGuid("enterpriseGuid");
        holderQueueItemDTO.setStoreName("name");
        holderQueueItemDTO.setGuid("msgKey");
        holderQueueItemDTO.setStoreGuid("storeGuid");
        holderQueueItemDTO.setContact("nickName");
        holderQueueItemDTO.setPhone("phone");
        holderQueueItemDTO.setGender(false);
        holderQueueItemDTO.setPeopleNumber((byte) 0b0);
        when(mockQueueClientService.inQueue(holderQueueItemDTO)).thenReturn(holderQueueItemDetailDTO);

        // Configure QueueClientService.obtain(...).
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("878c3e69-237b-4767-a935-7ae3e1a73fbe");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setLogoUrl("logoUrl");
        holderQueueDTO.setIsEnableRecovery(false);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO1 = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO1.setDeviceType(0);
        holderQueueItemDetailDTO1.setDeviceId("openId");
        holderQueueItemDetailDTO1.setEnterpriseGuid("enterpriseGuid");
        holderQueueItemDetailDTO1.setStoreName("name");
        holderQueueItemDetailDTO1.setGuid("msgKey");
        holderQueueItemDetailDTO1.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO1.setContact("nickName");
        holderQueueItemDetailDTO1.setPhone("phone");
        holderQueueItemDetailDTO1.setGender(false);
        holderQueueItemDetailDTO1.setPeopleNumber((byte) 0b0);
        final HolderQueueQueueRecordDTO holderQueueQueueRecordDTO = new HolderQueueQueueRecordDTO(holderQueueDTO,
                holderQueueItemDetailDTO1);
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("openId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("name");
        dto.setItemGuid("msgKey");
        dto.setEnterpriseGuid("enterpriseGuid");
        when(mockQueueClientService.obtain(dto)).thenReturn(holderQueueQueueRecordDTO);

        // Run the test
        final WxInQueueRespDTO result = wxQueueServiceImplUnderTest.inQueueByWx(wxInQueueReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
        verify(mockRedisUtils).setEx("msgKey", new WxQueueInfoReqDTO("msgKey"), 43200L, TimeUnit.SECONDS);

        // Confirm WxOpenMessageService.sendQueueMsg(...).
        final HolderQueueDTO holderQueueDTO1 = new HolderQueueDTO();
        holderQueueDTO1.setGuid("878c3e69-237b-4767-a935-7ae3e1a73fbe");
        holderQueueDTO1.setStoreGuid("storeGuid");
        holderQueueDTO1.setBrandName("brandName");
        holderQueueDTO1.setLogoUrl("logoUrl");
        holderQueueDTO1.setIsEnableRecovery(false);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO2 = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO2.setDeviceType(0);
        holderQueueItemDetailDTO2.setDeviceId("openId");
        holderQueueItemDetailDTO2.setEnterpriseGuid("enterpriseGuid");
        holderQueueItemDetailDTO2.setStoreName("name");
        holderQueueItemDetailDTO2.setGuid("msgKey");
        holderQueueItemDetailDTO2.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO2.setContact("nickName");
        holderQueueItemDetailDTO2.setPhone("phone");
        holderQueueItemDetailDTO2.setGender(false);
        holderQueueItemDetailDTO2.setPeopleNumber((byte) 0b0);
        final HolderQueueQueueRecordDTO obtain = new HolderQueueQueueRecordDTO(holderQueueDTO1,
                holderQueueItemDetailDTO2);
        verify(mockWxOpenMessageService).sendQueueMsg(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .sex(0)
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("belongBrandGuid")
                .build(), obtain, WxPortalReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .msgKey("msgKey")
                .build());
    }

    @Test
    public void testInQueueByWx_WxOpenMessageServiceThrowsWxErrorException() throws Exception {
        // Setup
        final WxInQueueReqDTO wxInQueueReqDTO = new WxInQueueReqDTO(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .sex(0)
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("belongBrandGuid")
                .build(), (byte) 0b0, "storeGuid");
        final WxInQueueRespDTO expectedResult = new WxInQueueRespDTO(0, "respMsg");
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure QueueClientService.inQueue(...).
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("openId");
        holderQueueItemDetailDTO.setEnterpriseGuid("enterpriseGuid");
        holderQueueItemDetailDTO.setStoreName("name");
        holderQueueItemDetailDTO.setGuid("msgKey");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setContact("nickName");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setGender(false);
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        final HolderQueueItemDTO holderQueueItemDTO = new HolderQueueItemDTO();
        holderQueueItemDTO.setDeviceType(0);
        holderQueueItemDTO.setDeviceId("openId");
        holderQueueItemDTO.setEnterpriseGuid("enterpriseGuid");
        holderQueueItemDTO.setStoreName("name");
        holderQueueItemDTO.setGuid("msgKey");
        holderQueueItemDTO.setStoreGuid("storeGuid");
        holderQueueItemDTO.setContact("nickName");
        holderQueueItemDTO.setPhone("phone");
        holderQueueItemDTO.setGender(false);
        holderQueueItemDTO.setPeopleNumber((byte) 0b0);
        when(mockQueueClientService.inQueue(holderQueueItemDTO)).thenReturn(holderQueueItemDetailDTO);

        // Configure QueueClientService.obtain(...).
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("878c3e69-237b-4767-a935-7ae3e1a73fbe");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setLogoUrl("logoUrl");
        holderQueueDTO.setIsEnableRecovery(false);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO1 = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO1.setDeviceType(0);
        holderQueueItemDetailDTO1.setDeviceId("openId");
        holderQueueItemDetailDTO1.setEnterpriseGuid("enterpriseGuid");
        holderQueueItemDetailDTO1.setStoreName("name");
        holderQueueItemDetailDTO1.setGuid("msgKey");
        holderQueueItemDetailDTO1.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO1.setContact("nickName");
        holderQueueItemDetailDTO1.setPhone("phone");
        holderQueueItemDetailDTO1.setGender(false);
        holderQueueItemDetailDTO1.setPeopleNumber((byte) 0b0);
        final HolderQueueQueueRecordDTO holderQueueQueueRecordDTO = new HolderQueueQueueRecordDTO(holderQueueDTO,
                holderQueueItemDetailDTO1);
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("openId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("name");
        dto.setItemGuid("msgKey");
        dto.setEnterpriseGuid("enterpriseGuid");
        when(mockQueueClientService.obtain(dto)).thenReturn(holderQueueQueueRecordDTO);

        // Configure WxOpenMessageService.sendQueueMsg(...).
        final HolderQueueDTO holderQueueDTO1 = new HolderQueueDTO();
        holderQueueDTO1.setGuid("878c3e69-237b-4767-a935-7ae3e1a73fbe");
        holderQueueDTO1.setStoreGuid("storeGuid");
        holderQueueDTO1.setBrandName("brandName");
        holderQueueDTO1.setLogoUrl("logoUrl");
        holderQueueDTO1.setIsEnableRecovery(false);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO2 = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO2.setDeviceType(0);
        holderQueueItemDetailDTO2.setDeviceId("openId");
        holderQueueItemDetailDTO2.setEnterpriseGuid("enterpriseGuid");
        holderQueueItemDetailDTO2.setStoreName("name");
        holderQueueItemDetailDTO2.setGuid("msgKey");
        holderQueueItemDetailDTO2.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO2.setContact("nickName");
        holderQueueItemDetailDTO2.setPhone("phone");
        holderQueueItemDetailDTO2.setGender(false);
        holderQueueItemDetailDTO2.setPeopleNumber((byte) 0b0);
        final HolderQueueQueueRecordDTO obtain = new HolderQueueQueueRecordDTO(holderQueueDTO1,
                holderQueueItemDetailDTO2);
        doThrow(WxErrorException.class).when(mockWxOpenMessageService).sendQueueMsg(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .sex(0)
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("belongBrandGuid")
                .build(), obtain, WxPortalReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .msgKey("msgKey")
                .build());

        // Run the test
        final WxInQueueRespDTO result = wxQueueServiceImplUnderTest.inQueueByWx(wxInQueueReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
        verify(mockRedisUtils).setEx("msgKey", new WxQueueInfoReqDTO("msgKey"), 43200L, TimeUnit.SECONDS);
    }

    @Test
    public void testCallUpNotify() throws Exception {
        // Setup
        final ItemGuidDTO itemGuidDTO = new ItemGuidDTO();
        itemGuidDTO.setDeviceType(0);
        itemGuidDTO.setDeviceId("openId");
        itemGuidDTO.setStoreGuid("storeGuid");
        itemGuidDTO.setStoreName("name");
        itemGuidDTO.setItemGuid("msgKey");
        itemGuidDTO.setEnterpriseGuid("enterpriseGuid");
        final List<ItemGuidDTO> itemGuidDTOS = Arrays.asList(itemGuidDTO);

        // Configure QueueClientService.obtain(...).
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("878c3e69-237b-4767-a935-7ae3e1a73fbe");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setLogoUrl("logoUrl");
        holderQueueDTO.setIsEnableRecovery(false);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("openId");
        holderQueueItemDetailDTO.setEnterpriseGuid("enterpriseGuid");
        holderQueueItemDetailDTO.setStoreName("name");
        holderQueueItemDetailDTO.setGuid("msgKey");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setContact("nickName");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setGender(false);
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        final HolderQueueQueueRecordDTO holderQueueQueueRecordDTO = new HolderQueueQueueRecordDTO(holderQueueDTO,
                holderQueueItemDetailDTO);
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("openId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("name");
        dto.setItemGuid("msgKey");
        dto.setEnterpriseGuid("enterpriseGuid");
        when(mockQueueClientService.obtain(dto)).thenReturn(holderQueueQueueRecordDTO);

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Run the test
        wxQueueServiceImplUnderTest.callUpNotify(itemGuidDTOS);

        // Verify the results
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");

        // Confirm WxOpenMessageService.sendQueueMsg(...).
        final HolderQueueDTO holderQueueDTO1 = new HolderQueueDTO();
        holderQueueDTO1.setGuid("878c3e69-237b-4767-a935-7ae3e1a73fbe");
        holderQueueDTO1.setStoreGuid("storeGuid");
        holderQueueDTO1.setBrandName("brandName");
        holderQueueDTO1.setLogoUrl("logoUrl");
        holderQueueDTO1.setIsEnableRecovery(false);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO1 = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO1.setDeviceType(0);
        holderQueueItemDetailDTO1.setDeviceId("openId");
        holderQueueItemDetailDTO1.setEnterpriseGuid("enterpriseGuid");
        holderQueueItemDetailDTO1.setStoreName("name");
        holderQueueItemDetailDTO1.setGuid("msgKey");
        holderQueueItemDetailDTO1.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO1.setContact("nickName");
        holderQueueItemDetailDTO1.setPhone("phone");
        holderQueueItemDetailDTO1.setGender(false);
        holderQueueItemDetailDTO1.setPeopleNumber((byte) 0b0);
        final HolderQueueQueueRecordDTO obtain = new HolderQueueQueueRecordDTO(holderQueueDTO1,
                holderQueueItemDetailDTO1);
        verify(mockWxOpenMessageService).sendQueueMsg(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .sex(0)
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("belongBrandGuid")
                .build(), obtain, WxPortalReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .msgKey("msgKey")
                .build());
    }

    @Test
    public void testCallUpNotify_WxOpenMessageServiceThrowsWxErrorException() throws Exception {
        // Setup
        final ItemGuidDTO itemGuidDTO = new ItemGuidDTO();
        itemGuidDTO.setDeviceType(0);
        itemGuidDTO.setDeviceId("openId");
        itemGuidDTO.setStoreGuid("storeGuid");
        itemGuidDTO.setStoreName("name");
        itemGuidDTO.setItemGuid("msgKey");
        itemGuidDTO.setEnterpriseGuid("enterpriseGuid");
        final List<ItemGuidDTO> itemGuidDTOS = Arrays.asList(itemGuidDTO);

        // Configure QueueClientService.obtain(...).
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("878c3e69-237b-4767-a935-7ae3e1a73fbe");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setLogoUrl("logoUrl");
        holderQueueDTO.setIsEnableRecovery(false);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO.setDeviceType(0);
        holderQueueItemDetailDTO.setDeviceId("openId");
        holderQueueItemDetailDTO.setEnterpriseGuid("enterpriseGuid");
        holderQueueItemDetailDTO.setStoreName("name");
        holderQueueItemDetailDTO.setGuid("msgKey");
        holderQueueItemDetailDTO.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO.setContact("nickName");
        holderQueueItemDetailDTO.setPhone("phone");
        holderQueueItemDetailDTO.setGender(false);
        holderQueueItemDetailDTO.setPeopleNumber((byte) 0b0);
        final HolderQueueQueueRecordDTO holderQueueQueueRecordDTO = new HolderQueueQueueRecordDTO(holderQueueDTO,
                holderQueueItemDetailDTO);
        final ItemGuidDTO dto = new ItemGuidDTO();
        dto.setDeviceType(0);
        dto.setDeviceId("openId");
        dto.setStoreGuid("storeGuid");
        dto.setStoreName("name");
        dto.setItemGuid("msgKey");
        dto.setEnterpriseGuid("enterpriseGuid");
        when(mockQueueClientService.obtain(dto)).thenReturn(holderQueueQueueRecordDTO);

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure WxOpenMessageService.sendQueueMsg(...).
        final HolderQueueDTO holderQueueDTO1 = new HolderQueueDTO();
        holderQueueDTO1.setGuid("878c3e69-237b-4767-a935-7ae3e1a73fbe");
        holderQueueDTO1.setStoreGuid("storeGuid");
        holderQueueDTO1.setBrandName("brandName");
        holderQueueDTO1.setLogoUrl("logoUrl");
        holderQueueDTO1.setIsEnableRecovery(false);
        final HolderQueueItemDetailDTO holderQueueItemDetailDTO1 = new HolderQueueItemDetailDTO();
        holderQueueItemDetailDTO1.setDeviceType(0);
        holderQueueItemDetailDTO1.setDeviceId("openId");
        holderQueueItemDetailDTO1.setEnterpriseGuid("enterpriseGuid");
        holderQueueItemDetailDTO1.setStoreName("name");
        holderQueueItemDetailDTO1.setGuid("msgKey");
        holderQueueItemDetailDTO1.setStoreGuid("storeGuid");
        holderQueueItemDetailDTO1.setContact("nickName");
        holderQueueItemDetailDTO1.setPhone("phone");
        holderQueueItemDetailDTO1.setGender(false);
        holderQueueItemDetailDTO1.setPeopleNumber((byte) 0b0);
        final HolderQueueQueueRecordDTO obtain = new HolderQueueQueueRecordDTO(holderQueueDTO1,
                holderQueueItemDetailDTO1);
        doThrow(WxErrorException.class).when(mockWxOpenMessageService).sendQueueMsg(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .sex(0)
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("belongBrandGuid")
                .build(), obtain, WxPortalReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .msgKey("msgKey")
                .build());

        // Run the test
        wxQueueServiceImplUnderTest.callUpNotify(itemGuidDTOS);

        // Verify the results
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testGetTotalDetail() {
        // Setup
        final WxPortalReqDTO wxPortalReqDTO = WxPortalReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .msgKey("msgKey")
                .build();
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("878c3e69-237b-4767-a935-7ae3e1a73fbe");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setLogoUrl("logoUrl");
        holderQueueDTO.setIsEnableRecovery(false);
        final WxTotalQueueDTO expectedResult = new WxTotalQueueDTO("guid", "name", "brandGuid", "brandName",
                "brandLogo", Arrays.asList(holderQueueDTO));

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure OrganizationClientService.queryBrandByGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setUuid("9fd72e5d-9be5-4894-b089-41ec03bdc638");
        brandDTO.setName("brandName");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("brandLogo");
        when(mockOrganizationClientService.queryBrandByGuid("belongBrandGuid")).thenReturn(brandDTO);

        // Configure QueueClientService.listByStore(...).
        final HolderQueueDTO holderQueueDTO1 = new HolderQueueDTO();
        holderQueueDTO1.setGuid("878c3e69-237b-4767-a935-7ae3e1a73fbe");
        holderQueueDTO1.setStoreGuid("storeGuid");
        holderQueueDTO1.setBrandName("brandName");
        holderQueueDTO1.setLogoUrl("logoUrl");
        holderQueueDTO1.setIsEnableRecovery(false);
        final List<HolderQueueDTO> holderQueueDTOS = Arrays.asList(holderQueueDTO1);
        when(mockQueueClientService.listByStore(new StoreGuidDTO("guid"))).thenReturn(holderQueueDTOS);

        // Run the test
        final WxTotalQueueDTO result = wxQueueServiceImplUnderTest.getTotalDetail(wxPortalReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testGetTotalDetail_QueueClientServiceReturnsNoItems() {
        // Setup
        final WxPortalReqDTO wxPortalReqDTO = WxPortalReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .msgKey("msgKey")
                .build();
        final HolderQueueDTO holderQueueDTO = new HolderQueueDTO();
        holderQueueDTO.setGuid("878c3e69-237b-4767-a935-7ae3e1a73fbe");
        holderQueueDTO.setStoreGuid("storeGuid");
        holderQueueDTO.setBrandName("brandName");
        holderQueueDTO.setLogoUrl("logoUrl");
        holderQueueDTO.setIsEnableRecovery(false);
        final WxTotalQueueDTO expectedResult = new WxTotalQueueDTO("guid", "name", "brandGuid", "brandName",
                "brandLogo", Arrays.asList(holderQueueDTO));

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        // Configure OrganizationClientService.queryBrandByGuid(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setUuid("9fd72e5d-9be5-4894-b089-41ec03bdc638");
        brandDTO.setName("brandName");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("brandLogo");
        when(mockOrganizationClientService.queryBrandByGuid("belongBrandGuid")).thenReturn(brandDTO);

        when(mockQueueClientService.listByStore(new StoreGuidDTO("guid"))).thenReturn(Collections.emptyList());

        // Run the test
        final WxTotalQueueDTO result = wxQueueServiceImplUnderTest.getTotalDetail(wxPortalReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testQueryQueueStatusNum() {
        // Setup
        final QueueWechatDTO queueWechatDTO = QueueWechatDTO.builder().build();
        final Map<Integer, Integer> expectedResult = new HashMap<>();

        // Configure QueueClientService.queryByUser(...).
        final List<WxQueueListDTO> wxQueueListDTOS = Arrays.asList(WxQueueListDTO.builder()
                .status((byte) 0b0)
                .build());
        when(mockQueueClientService.queryByUser(QueueWechatDTO.builder().build())).thenReturn(wxQueueListDTOS);

        // Run the test
        final Map<Integer, Integer> result = wxQueueServiceImplUnderTest.queryQueueStatusNum(queueWechatDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQueryQueueStatusNum_QueueClientServiceReturnsNoItems() {
        // Setup
        final QueueWechatDTO queueWechatDTO = QueueWechatDTO.builder().build();
        final Map<Integer, Integer> expectedResult = new HashMap<>();
        when(mockQueueClientService.queryByUser(QueueWechatDTO.builder().build())).thenReturn(Collections.emptyList());

        // Run the test
        final Map<Integer, Integer> result = wxQueueServiceImplUnderTest.queryQueueStatusNum(queueWechatDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
