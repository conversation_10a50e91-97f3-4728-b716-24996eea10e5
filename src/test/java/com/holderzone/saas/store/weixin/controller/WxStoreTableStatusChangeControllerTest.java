package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.table.TableCombineDTO;
import com.holderzone.saas.store.dto.table.TableInfoDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.TurnTableDTO;
import com.holderzone.saas.store.weixin.service.WxStoreTableStatusChangeService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxStoreTableStatusChangeController.class)
public class WxStoreTableStatusChangeControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStoreTableStatusChangeService mockWxStoreTableStatusChangeService;
    @MockBean
    private DynamicHelper mockDynamicHelper;

    @Test
    public void testCombine() throws Exception {
        // Setup
        // Configure WxStoreTableStatusChangeService.combine(...).
        final TableCombineDTO tableCombineDTO = new TableCombineDTO();
        tableCombineDTO.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO.setCombineTimes(0);
        tableCombineDTO.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO.setMainTableGuid("mainTableGuid");
        when(mockWxStoreTableStatusChangeService.combine(tableCombineDTO)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_table_status_change/combine")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testCombine_WxStoreTableStatusChangeServiceReturnsTrue() throws Exception {
        // Setup
        // Configure WxStoreTableStatusChangeService.combine(...).
        final TableCombineDTO tableCombineDTO = new TableCombineDTO();
        tableCombineDTO.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO.setCombineTimes(0);
        tableCombineDTO.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO.setMainTableGuid("mainTableGuid");
        when(mockWxStoreTableStatusChangeService.combine(tableCombineDTO)).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_table_status_change/combine")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testTurn() throws Exception {
        // Setup
        // Configure WxStoreTableStatusChangeService.turn(...).
        final TurnTableDTO tableStatus = new TurnTableDTO();
        tableStatus.setEnterpriseGuid("enterpriseGuid");
        tableStatus.setOriginTableGuid("originTableGuid");
        tableStatus.setOriginTableCode("originTableCode");
        tableStatus.setOriginTableAreaGuid("originTableAreaGuid");
        tableStatus.setOriginTableAreaName("originTableAreaName");
        when(mockWxStoreTableStatusChangeService.turn(tableStatus)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_table_status_change/turn")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testTurn_WxStoreTableStatusChangeServiceReturnsTrue() throws Exception {
        // Setup
        // Configure WxStoreTableStatusChangeService.turn(...).
        final TurnTableDTO tableStatus = new TurnTableDTO();
        tableStatus.setEnterpriseGuid("enterpriseGuid");
        tableStatus.setOriginTableGuid("originTableGuid");
        tableStatus.setOriginTableCode("originTableCode");
        tableStatus.setOriginTableAreaGuid("originTableAreaGuid");
        tableStatus.setOriginTableAreaName("originTableAreaName");
        when(mockWxStoreTableStatusChangeService.turn(tableStatus)).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_table_status_change/turn")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testSeparate() throws Exception {
        // Setup
        // Configure WxStoreTableStatusChangeService.separate(...).
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setEnterpriseGuid("enterpriseGuid");
        tableInfoDTO.setTableName("tableName");
        tableInfoDTO.setAreaGuid("areaGuid");
        tableInfoDTO.setAreaName("areaName");
        tableInfoDTO.setSort(0);
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);
        when(mockWxStoreTableStatusChangeService.separate(tableOrderCombineDTO)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_table_status_change/separate")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testSeparate_WxStoreTableStatusChangeServiceReturnsTrue() throws Exception {
        // Setup
        // Configure WxStoreTableStatusChangeService.separate(...).
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setEnterpriseGuid("enterpriseGuid");
        tableInfoDTO.setTableName("tableName");
        tableInfoDTO.setAreaGuid("areaGuid");
        tableInfoDTO.setAreaName("areaName");
        tableInfoDTO.setSort(0);
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);
        when(mockWxStoreTableStatusChangeService.separate(tableOrderCombineDTO)).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_table_status_change/separate")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }
}
