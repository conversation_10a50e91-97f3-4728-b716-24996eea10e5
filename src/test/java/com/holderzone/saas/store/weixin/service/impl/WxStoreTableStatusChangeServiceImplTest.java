package com.holderzone.saas.store.weixin.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.table.TableCombineDTO;
import com.holderzone.saas.store.dto.table.TableInfoDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.TurnTableDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderRecordDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.helper.WebsocketMessageHelper;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStoreTableStatusChangeServiceImplTest {

    @Mock
    private WxStoreMerchantOrderService mockWxStoreMerchantOrderService;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private WxStoreTradeOrderService mockWxStoreTradeOrderService;
    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private WxOrderRecordService mockWxOrderRecordService;
    @Mock
    private WxUserRecordService mockWxUserRecordService;
    @Mock
    private WebsocketMessageHelper mockWebsocketMessageHelper;

    private WxStoreTableStatusChangeServiceImpl wxStoreTableStatusChangeServiceImplUnderTest;

    @Before
    public void setUp() {
        wxStoreTableStatusChangeServiceImplUnderTest = new WxStoreTableStatusChangeServiceImpl(
                mockWxStoreMerchantOrderService, mockDynamicHelper,
                mockRedisUtils, mockWxOrderRecordService, mockWxUserRecordService,
                mockWebsocketMessageHelper);
    }

    @Test
    public void testCombine() {
        // Setup
        final TableCombineDTO tableCombineDTO = new TableCombineDTO();
        tableCombineDTO.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO.setCombineTimes(0);
        tableCombineDTO.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO.setMainTableGuid("mainTableGuid");

        when(mockRedisUtils.hKeyList("key")).thenReturn(new HashSet<>(Arrays.asList("value")));

        // Run the test
        final boolean result = wxStoreTableStatusChangeServiceImplUnderTest.combine(tableCombineDTO);

        // Verify the results
        assertTrue(result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
        verify(mockWebsocketMessageHelper).sendOrderEmqMessage("tableGuid", new HashSet<>(Arrays.asList("value")));
    }

    @Test
    public void testCombine_RedisUtilsReturnsNoItems() {
        // Setup
        final TableCombineDTO tableCombineDTO = new TableCombineDTO();
        tableCombineDTO.setEnterpriseGuid("enterpriseGuid");
        tableCombineDTO.setTableGuidList(Arrays.asList("value"));
        tableCombineDTO.setCombineTimes(0);
        tableCombineDTO.setMainOrderGuid("mainOrderGuid");
        tableCombineDTO.setMainTableGuid("mainTableGuid");

        when(mockRedisUtils.hKeyList("key")).thenReturn(Collections.emptySet());

        // Run the test
        final boolean result = wxStoreTableStatusChangeServiceImplUnderTest.combine(tableCombineDTO);

        // Verify the results
        assertTrue(result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testSeparate() {
        // Setup
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setEnterpriseGuid("enterpriseGuid");
        tableInfoDTO.setTableGuid("tableGuid");
        tableInfoDTO.setTableName("tableName");
        tableInfoDTO.setAreaGuid("areaGuid");
        tableInfoDTO.setAreaName("areaName");
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);
        when(mockRedisUtils.hKeyList("key")).thenReturn(new HashSet<>(Arrays.asList("value")));

        // Run the test
        final boolean result = wxStoreTableStatusChangeServiceImplUnderTest.separate(tableOrderCombineDTO);

        // Verify the results
        assertTrue(result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
        verify(mockWebsocketMessageHelper).sendOrderEmqMessage("tableGuid", new HashSet<>(Arrays.asList("value")));
    }

    @Test
    public void testSeparate_RedisUtilsReturnsNoItems() {
        // Setup
        final TableInfoDTO tableInfoDTO = new TableInfoDTO();
        tableInfoDTO.setEnterpriseGuid("enterpriseGuid");
        tableInfoDTO.setTableGuid("tableGuid");
        tableInfoDTO.setTableName("tableName");
        tableInfoDTO.setAreaGuid("areaGuid");
        tableInfoDTO.setAreaName("areaName");
        final TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO(Arrays.asList(tableInfoDTO),
                "mainOrderGuid", "mainTableGuid", true);
        when(mockRedisUtils.hKeyList("key")).thenReturn(Collections.emptySet());

        // Run the test
        final boolean result = wxStoreTableStatusChangeServiceImplUnderTest.separate(tableOrderCombineDTO);

        // Verify the results
        assertTrue(result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testTurn() {
        // Setup
        final TurnTableDTO turnTableDTO = new TurnTableDTO();
        turnTableDTO.setEnterpriseGuid("enterpriseGuid");
        turnTableDTO.setOriginTableGuid("newTableGuid");
        turnTableDTO.setOriginTableCode("code");
        turnTableDTO.setNewTableAreaName("areaName");
        turnTableDTO.setNewTableAreaGuid("areaGuid");
        turnTableDTO.setNewTableGuid("newTableGuid");
        turnTableDTO.setNewTableCode("code");

        when(mockRedisUtils.hKeyList("key")).thenReturn(new HashSet<>(Arrays.asList("value")));

        // Configure WxOrderRecordService.getOutStandingOrders(...).
        final List<WxOrderRecordDO> wxOrderRecordDOS = Arrays.asList(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "newTableGuid", "code", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        when(mockWxOrderRecordService.getOutStandingOrders("newTableGuid")).thenReturn(wxOrderRecordDOS);

        when(mockWxStoreMerchantOrderService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        when(mockWxOrderRecordService.updateById(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "newTableGuid", "code", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0))).thenReturn(false);
        when(mockWxStoreMerchantOrderService.update(any(LambdaUpdateWrapper.class))).thenReturn(false);

        // Run the test
        final boolean result = wxStoreTableStatusChangeServiceImplUnderTest.turn(turnTableDTO);

        // Verify the results
        assertTrue(result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
        verify(mockRedisUtils).delete("key");

        // Confirm WebsocketMessageHelper.sendTableEmqMessage(...).
        final TurnTableDTO turnTableDTO1 = new TurnTableDTO();
        turnTableDTO1.setEnterpriseGuid("enterpriseGuid");
        turnTableDTO1.setOriginTableGuid("newTableGuid");
        turnTableDTO1.setOriginTableCode("code");
        turnTableDTO1.setNewTableAreaName("areaName");
        turnTableDTO1.setNewTableAreaGuid("areaGuid");
        turnTableDTO1.setNewTableGuid("newTableGuid");
        turnTableDTO1.setNewTableCode("code");
        verify(mockWebsocketMessageHelper).sendTableEmqMessage("newTableGuid", turnTableDTO1, true, "msg",
                new HashSet<>(Arrays.asList("value")));
        verify(mockRedisUtils).rename("oldKey", "newKey");

        // Confirm WxStoreMerchantOrderService.updateBatchById(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> entityList = Arrays.asList(wxStoreMerchantOrderDO);
        verify(mockWxStoreMerchantOrderService).updateBatchById(entityList);
        verify(mockWxOrderRecordService).removeById("guid");
    }

    @Test
    public void testTurn_RedisUtilsHKeyListReturnsNoItems() {
        // Setup
        final TurnTableDTO turnTableDTO = new TurnTableDTO();
        turnTableDTO.setEnterpriseGuid("enterpriseGuid");
        turnTableDTO.setOriginTableGuid("newTableGuid");
        turnTableDTO.setOriginTableCode("code");
        turnTableDTO.setNewTableAreaName("areaName");
        turnTableDTO.setNewTableAreaGuid("areaGuid");
        turnTableDTO.setNewTableGuid("newTableGuid");
        turnTableDTO.setNewTableCode("code");

        when(mockRedisUtils.hKeyList("key")).thenReturn(Collections.emptySet());

        // Configure WxOrderRecordService.getOutStandingOrders(...).
        final List<WxOrderRecordDO> wxOrderRecordDOS = Arrays.asList(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "newTableGuid", "code", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        when(mockWxOrderRecordService.getOutStandingOrders("newTableGuid")).thenReturn(wxOrderRecordDOS);

        when(mockWxStoreMerchantOrderService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        when(mockWxOrderRecordService.updateById(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "newTableGuid", "code", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0))).thenReturn(false);
        when(mockWxStoreMerchantOrderService.update(any(LambdaUpdateWrapper.class))).thenReturn(false);

        // Run the test
        final boolean result = wxStoreTableStatusChangeServiceImplUnderTest.turn(turnTableDTO);

        // Verify the results
        assertTrue(result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
        verify(mockRedisUtils).delete("key");

        // Confirm WebsocketMessageHelper.sendTableEmqMessage(...).
        final TurnTableDTO turnTableDTO1 = new TurnTableDTO();
        turnTableDTO1.setEnterpriseGuid("enterpriseGuid");
        turnTableDTO1.setOriginTableGuid("newTableGuid");
        turnTableDTO1.setOriginTableCode("code");
        turnTableDTO1.setNewTableAreaName("areaName");
        turnTableDTO1.setNewTableAreaGuid("areaGuid");
        turnTableDTO1.setNewTableGuid("newTableGuid");
        turnTableDTO1.setNewTableCode("code");
        verify(mockWebsocketMessageHelper).sendTableEmqMessage("newTableGuid", turnTableDTO1, true, "msg",
                new HashSet<>(Arrays.asList("value")));

        // Confirm WxStoreMerchantOrderService.updateBatchById(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> entityList = Arrays.asList(wxStoreMerchantOrderDO);
        verify(mockWxStoreMerchantOrderService).updateBatchById(entityList);
        verify(mockWxOrderRecordService).removeById("guid");
    }

    @Test
    public void testTurn_WxOrderRecordServiceGetOutStandingOrdersReturnsNoItems() {
        // Setup
        final TurnTableDTO turnTableDTO = new TurnTableDTO();
        turnTableDTO.setEnterpriseGuid("enterpriseGuid");
        turnTableDTO.setOriginTableGuid("newTableGuid");
        turnTableDTO.setOriginTableCode("code");
        turnTableDTO.setNewTableAreaName("areaName");
        turnTableDTO.setNewTableAreaGuid("areaGuid");
        turnTableDTO.setNewTableGuid("newTableGuid");
        turnTableDTO.setNewTableCode("code");

        when(mockRedisUtils.hKeyList("key")).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockWxOrderRecordService.getOutStandingOrders("newTableGuid")).thenReturn(Collections.emptyList());
        when(mockWxStoreMerchantOrderService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        when(mockWxOrderRecordService.updateById(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "newTableGuid", "code", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0))).thenReturn(false);
        when(mockWxStoreMerchantOrderService.update(any(LambdaUpdateWrapper.class))).thenReturn(false);

        // Run the test
        final boolean result = wxStoreTableStatusChangeServiceImplUnderTest.turn(turnTableDTO);

        // Verify the results
        assertTrue(result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
        verify(mockRedisUtils).delete("key");

        // Confirm WebsocketMessageHelper.sendTableEmqMessage(...).
        final TurnTableDTO turnTableDTO1 = new TurnTableDTO();
        turnTableDTO1.setEnterpriseGuid("enterpriseGuid");
        turnTableDTO1.setOriginTableGuid("newTableGuid");
        turnTableDTO1.setOriginTableCode("code");
        turnTableDTO1.setNewTableAreaName("areaName");
        turnTableDTO1.setNewTableAreaGuid("areaGuid");
        turnTableDTO1.setNewTableGuid("newTableGuid");
        turnTableDTO1.setNewTableCode("code");
        verify(mockWebsocketMessageHelper).sendTableEmqMessage("newTableGuid", turnTableDTO1, true, "msg",
                new HashSet<>(Arrays.asList("value")));
        verify(mockRedisUtils).rename("oldKey", "newKey");

        // Confirm WxStoreMerchantOrderService.updateBatchById(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDO.setAreaName("areaName");
        wxStoreMerchantOrderDO.setDiningTableGuid("diningTableGuid");
        wxStoreMerchantOrderDO.setTableCode("tableCode");
        wxStoreMerchantOrderDO.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> entityList = Arrays.asList(wxStoreMerchantOrderDO);
        verify(mockWxStoreMerchantOrderService).updateBatchById(entityList);
        verify(mockWxOrderRecordService).removeById("guid");
    }

    @Test
    public void testTurn_WxUserRecordServiceReturnsNull() {
        // Setup
        final TurnTableDTO turnTableDTO = new TurnTableDTO();
        turnTableDTO.setEnterpriseGuid("enterpriseGuid");
        turnTableDTO.setOriginTableGuid("newTableGuid");
        turnTableDTO.setOriginTableCode("code");
        turnTableDTO.setNewTableAreaName("areaName");
        turnTableDTO.setNewTableAreaGuid("areaGuid");
        turnTableDTO.setNewTableGuid("newTableGuid");
        turnTableDTO.setNewTableCode("code");

        when(mockRedisUtils.hKeyList("key")).thenReturn(new HashSet<>(Arrays.asList("value")));

        // Configure WxOrderRecordService.getOutStandingOrders(...).
        final List<WxOrderRecordDO> wxOrderRecordDOS = Arrays.asList(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "newTableGuid", "code", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        when(mockWxOrderRecordService.getOutStandingOrders("newTableGuid")).thenReturn(wxOrderRecordDOS);

        when(mockWxStoreMerchantOrderService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(null);
        when(mockWxOrderRecordService.updateById(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "newTableGuid", "code", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0))).thenReturn(false);
        when(mockWxStoreMerchantOrderService.update(any(LambdaUpdateWrapper.class))).thenReturn(false);

        // Run the test
        final boolean result = wxStoreTableStatusChangeServiceImplUnderTest.turn(turnTableDTO);

        // Verify the results
        assertTrue(result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
        verify(mockRedisUtils).delete("key");

        // Confirm WebsocketMessageHelper.sendTableEmqMessage(...).
        final TurnTableDTO turnTableDTO1 = new TurnTableDTO();
        turnTableDTO1.setEnterpriseGuid("enterpriseGuid");
        turnTableDTO1.setOriginTableGuid("newTableGuid");
        turnTableDTO1.setOriginTableCode("code");
        turnTableDTO1.setNewTableAreaName("areaName");
        turnTableDTO1.setNewTableAreaGuid("areaGuid");
        turnTableDTO1.setNewTableGuid("newTableGuid");
        turnTableDTO1.setNewTableCode("code");
        verify(mockWebsocketMessageHelper).sendTableEmqMessage("newTableGuid", turnTableDTO1, true, "msg",
                new HashSet<>(Arrays.asList("value")));
        verify(mockRedisUtils).rename("oldKey", "newKey");
    }
}
