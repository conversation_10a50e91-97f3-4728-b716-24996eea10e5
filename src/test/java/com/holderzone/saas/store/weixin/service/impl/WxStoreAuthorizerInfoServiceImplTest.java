package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.framework.base.dto.file.FileDto;
import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.weixin.open.WxOpenAuthDTO;
import com.holderzone.saas.store.dto.weixin.req.*;
import com.holderzone.saas.store.dto.weixin.resp.WxBrandAuthRespDTO;
import com.holderzone.saas.store.weixin.config.WeChatConfig;
import com.holderzone.saas.store.weixin.config.WxThirdOpenConfig;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerInfoDO;
import com.holderzone.saas.store.weixin.entity.query.WxQrCodeUrlQuery;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreOpenMapstruct;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.rpc.BaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.OrganizationClientService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.open.api.WxOpenComponentService;
import me.chanjar.weixin.open.bean.auth.WxOpenAuthorizationInfo;
import me.chanjar.weixin.open.bean.auth.WxOpenAuthorizerInfo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStoreAuthorizerInfoServiceImplTest {

    @Mock
    private WxOpenComponentService mockWxOpenComponentService;
    @Mock
    private WxStoreComponentConfigService mockWxStoreComponentConfigService;
    @Mock
    private WxThirdOpenConfig mockWxThirdOpenConfig;
    @Mock
    private WxStoreOpenMapstruct mockWxStoreOpenMapstruct;
    @Mock
    private WxStoreAuthorizerBusiInfoService mockWxStoreAuthorizerBusiInfoService;
    @Mock
    private OrganizationClientService mockOrganizationClientService;
    @Mock
    private BaseClientService mockBaseClientService;
    @Mock
    private WxSaasMpService mockWxSaasMpService;
    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private WxQrCodeInfoService mockWxQrCodeInfoService;
    @Mock
    private WxStoreMpService mockWxStoreMpService;
    @Mock
    private WxMpTemplateService mockWxMpTemplateService;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private WeChatConfig mockWeChatConfig;

    private WxStoreAuthorizerInfoServiceImpl wxStoreAuthorizerInfoServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxStoreAuthorizerInfoServiceImplUnderTest = new WxStoreAuthorizerInfoServiceImpl();
        wxStoreAuthorizerInfoServiceImplUnderTest.wxOpenComponentService = mockWxOpenComponentService;
        wxStoreAuthorizerInfoServiceImplUnderTest.wxStoreComponentConfigService = mockWxStoreComponentConfigService;
        wxStoreAuthorizerInfoServiceImplUnderTest.wxThirdOpenConfig = mockWxThirdOpenConfig;
        wxStoreAuthorizerInfoServiceImplUnderTest.wxStoreOpenMapstruct = mockWxStoreOpenMapstruct;
        wxStoreAuthorizerInfoServiceImplUnderTest.wxStoreAuthorizerBusiInfoService = mockWxStoreAuthorizerBusiInfoService;
        wxStoreAuthorizerInfoServiceImplUnderTest.organizationClientService = mockOrganizationClientService;
        wxStoreAuthorizerInfoServiceImplUnderTest.baseClientService = mockBaseClientService;
        wxStoreAuthorizerInfoServiceImplUnderTest.wxSaasMpService = mockWxSaasMpService;
        wxStoreAuthorizerInfoServiceImplUnderTest.redisUtils = mockRedisUtils;
        wxStoreAuthorizerInfoServiceImplUnderTest.wxQrCodeInfoService = mockWxQrCodeInfoService;
        wxStoreAuthorizerInfoServiceImplUnderTest.wxStoreMpService = mockWxStoreMpService;
        wxStoreAuthorizerInfoServiceImplUnderTest.wxMpTemplateService = mockWxMpTemplateService;
        wxStoreAuthorizerInfoServiceImplUnderTest.dynamicHelper = mockDynamicHelper;
        wxStoreAuthorizerInfoServiceImplUnderTest.weChatConfig = mockWeChatConfig;
    }

    @Test
    public void testGetPreAuthCode() throws Exception {
        // Setup
        final WxPreCodReqDTO wxPreCodReqDTO = new WxPreCodReqDTO("brandGuid", "enterpriseGuid", "appId", 0,"","");
        when(mockWeChatConfig.getREDIRECT_URI()).thenReturn("result");
        when(mockWxThirdOpenConfig.getWxOpenComponentService()).thenReturn(null);

        // Run the test
        final String result = wxStoreAuthorizerInfoServiceImplUnderTest.getPreAuthCode(wxPreCodReqDTO);

        // Verify the results
        assertEquals("result", result);
        verify(mockWxStoreComponentConfigService).getAccessToken();
    }

    @Test(expected = WxErrorException.class)
    public void testGetPreAuthCode_WxStoreComponentConfigServiceThrowsWxErrorException() throws Exception {
        // Setup
        final WxPreCodReqDTO wxPreCodReqDTO = new WxPreCodReqDTO("brandGuid", "enterpriseGuid", "appId", 0,"","");
        when(mockWxStoreComponentConfigService.getAccessToken()).thenThrow(WxErrorException.class);

        // Run the test
        wxStoreAuthorizerInfoServiceImplUnderTest.getPreAuthCode(wxPreCodReqDTO);
    }

    @Test
    public void testQueryAuth() throws Exception {
        // Setup
        final WxOpenAuthDTO wxOpenAuthDTO = new WxOpenAuthDTO("auth_code", 0L, "brandGuid", "enterpriseGuid");
        when(mockWxThirdOpenConfig.getWxOpenComponentService()).thenReturn(null);
        when(mockRedisUtils.generateGuid("redisKey")).thenReturn("4e62d5fc-2e23-4ec5-b36d-1f269c7f68e8");

        // Configure WxStoreOpenMapstruct.authorizerInfo2DO(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "4e62d5fc-2e23-4ec5-b36d-1f269c7f68e8", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "authorizerAppid", "authorizerAccessToken", 0L,
                "authorizerRefreshToken", "funcInfo", "nickName", "originalUrl", 0, 0, "userName", "principalName",
                "alias", "originalUrl", "signature", "unBandUserGuid", "unBandUserName", "unBandUserTel",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateId", false, "multiMemberGuid");
        final WxOpenAuthorizerInfo wxOpenAuthorizerInfo = new WxOpenAuthorizerInfo();
        wxOpenAuthorizerInfo.setNickName("nickName");
        wxOpenAuthorizerInfo.setHeadImg("headImg");
        wxOpenAuthorizerInfo.setServiceTypeInfo(0);
        wxOpenAuthorizerInfo.setVerifyTypeInfo(0);
        wxOpenAuthorizerInfo.setBusinessInfo(new HashMap<>());
        final WxOpenAuthorizationInfo wxOpenAuthorizationInfo = new WxOpenAuthorizationInfo();
        wxOpenAuthorizationInfo.setAuthorizerAppid("authorizerAppid");
        wxOpenAuthorizationInfo.setAuthorizerAccessToken("authorizerAccessToken");
        wxOpenAuthorizationInfo.setExpiresIn(0);
        wxOpenAuthorizationInfo.setAuthorizerRefreshToken("authorizerRefreshToken");
        wxOpenAuthorizationInfo.setFuncInfo(Arrays.asList(0));
        when(mockWxStoreOpenMapstruct.authorizerInfo2DO(wxOpenAuthorizerInfo, wxOpenAuthorizationInfo))
                .thenReturn(wxStoreAuthorizerInfoDO);

        when(mockBaseClientService.upload(any(FileDto.class))).thenReturn("result");
        when(mockWeChatConfig.getCALL_BACK_ERROR_PAGE()).thenReturn("result");
        when(mockWeChatConfig.getCALL_BACK_SUCCESS_PAGE()).thenReturn("CALL_BACK_SUCCESS_PAGE");

        // Run the test
        final String result = wxStoreAuthorizerInfoServiceImplUnderTest.queryAuth(wxOpenAuthDTO);

        // Verify the results
        assertEquals("CALL_BACK_SUCCESS_PAGE", result);
        verify(mockWxStoreComponentConfigService).getAccessToken();
        verify(mockWxMpTemplateService).createMsgTemplate(
                new WxStoreAuthorizerInfoDO(0L, "4e62d5fc-2e23-4ec5-b36d-1f269c7f68e8", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "authorizerAppid",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "originalUrl", 0,
                        0, "userName", "principalName", "alias", "originalUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateId",
                        false, "multiMemberGuid"), 0);
        verify(mockWxStoreAuthorizerBusiInfoService).saveOrUpdateStoreAuthorizeBusiInfo("authorizerAppid",
                new HashMap<>());
    }

    @Test(expected = WxErrorException.class)
    public void testQueryAuth_WxStoreComponentConfigServiceThrowsWxErrorException() throws Exception {
        // Setup
        final WxOpenAuthDTO wxOpenAuthDTO = new WxOpenAuthDTO("auth_code", 0L, "brandGuid", "enterpriseGuid");
        when(mockWxStoreComponentConfigService.getAccessToken()).thenThrow(WxErrorException.class);

        // Run the test
        wxStoreAuthorizerInfoServiceImplUnderTest.queryAuth(wxOpenAuthDTO);
    }

    @Test
    public void testGetBrandAuthList() {
        // Setup
        final List<WxBrandAuthRespDTO> expectedResult = Arrays.asList(
                new WxBrandAuthRespDTO("brandGuid", "brandName", "nickName", "mpType", "authorizerAppid",
                        Arrays.asList(0), "originalUrl", 0, "multiMemberGuid", "multiMemberName"));

        // Configure OrganizationClientService.queryBrandList(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("ef77a046-8194-4460-95fe-23207395b9d9");
        brandDTO.setUuid("177278c8-d1c1-4212-affb-94279f229d88");
        brandDTO.setName("brandName");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        final List<BrandDTO> brandDTOS = Arrays.asList(brandDTO);
        when(mockOrganizationClientService.queryBrandList()).thenReturn(brandDTOS);

        // Run the test
        final List<WxBrandAuthRespDTO> result = wxStoreAuthorizerInfoServiceImplUnderTest.getBrandAuthList();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetBrandAuthList_OrganizationClientServiceReturnsNoItems() {
        // Setup
        when(mockOrganizationClientService.queryBrandList()).thenReturn(Collections.emptyList());

        // Run the test
        final List<WxBrandAuthRespDTO> result = wxStoreAuthorizerInfoServiceImplUnderTest.getBrandAuthList();

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testSendShortMessage() {
        // Setup
        final WxSendShortMsgReqDTO wxSendShortMsgReqDTO = new WxSendShortMsgReqDTO("userGuid", "userName", "tel");
        when(mockBaseClientService.sendMessage(any(MessageDTO.class))).thenReturn(true);

        // Run the test
        final String result = wxStoreAuthorizerInfoServiceImplUnderTest.sendShortMessage(wxSendShortMsgReqDTO);

        // Verify the results
        assertEquals("result", result);
        verify(mockRedisUtils).setEx("key", "value", 300L, TimeUnit.SECONDS);
    }

    @Test(expected = BusinessException.class)
    public void testSendShortMessage_BaseClientServiceReturnsFalse() {
        // Setup
        final WxSendShortMsgReqDTO wxSendShortMsgReqDTO = new WxSendShortMsgReqDTO("userGuid", "userName", "tel");
        when(mockBaseClientService.sendMessage(any(MessageDTO.class))).thenReturn(false);

        // Run the test
        wxStoreAuthorizerInfoServiceImplUnderTest.sendShortMessage(wxSendShortMsgReqDTO);
    }

    @Test
    public void testUnBandBrand() {
        // Setup
        final WxUnBandReqDTO wxUnBandReqDTO = new WxUnBandReqDTO("brandGuid", "messageKey", "code");
        when(mockRedisUtils.get("messageKey")).thenReturn("result");

        // Run the test
        final Integer result = wxStoreAuthorizerInfoServiceImplUnderTest.unBandBrand(wxUnBandReqDTO);

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
        verify(mockRedisUtils).delete("messageKey");
    }

    @Test
    public void testGetByBrandGuid() {
        // Setup
        final WxBrandAuthRespDTO expectedResult = new WxBrandAuthRespDTO("brandGuid", "brandName", "nickName", "mpType",
                "authorizerAppid", Arrays.asList(0), "originalUrl", 0, "multiMemberGuid", "multiMemberName");

        // Run the test
        final WxBrandAuthRespDTO result = wxStoreAuthorizerInfoServiceImplUnderTest.getByBrandGuid("brandGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetQrCodeUrl() throws Exception {
        // Setup
        final WxQrCodeUrlQuery wxQrCodeUrlQuery = new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName",
                "areaGuid", "areaName", "tableGuid", "tableName", 0, "brandGuid", "appId", false, false,
                "myselfOpenId");
        when(mockWxQrCodeInfoService.getSceneStr(
                new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid",
                        "tableName", 0, "brandGuid", "appId", false, false, "myselfOpenId"))).thenReturn("result");
        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "4e62d5fc-2e23-4ec5-b36d-1f269c7f68e8", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "authorizerAppid",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "originalUrl", 0,
                        0, "userName", "principalName", "alias", "originalUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateId",
                        false, "multiMemberGuid"))).thenReturn(null);

        // Run the test
        final String result = wxStoreAuthorizerInfoServiceImplUnderTest.getQrCodeUrl(wxQrCodeUrlQuery);

        // Verify the results
        assertEquals("url", result);
    }

    @Test(expected = WxErrorException.class)
    public void testGetQrCodeUrl_WxSaasMpServiceThrowsWxErrorException() throws Exception {
        // Setup
        final WxQrCodeUrlQuery wxQrCodeUrlQuery = new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName",
                "areaGuid", "areaName", "tableGuid", "tableName", 0, "brandGuid", "appId", false, false,
                "myselfOpenId");
        when(mockWxQrCodeInfoService.getSceneStr(
                new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid",
                        "tableName", 0, "brandGuid", "appId", false, false, "myselfOpenId"))).thenReturn("result");
        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "4e62d5fc-2e23-4ec5-b36d-1f269c7f68e8", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "authorizerAppid",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "originalUrl", 0,
                        0, "userName", "principalName", "alias", "originalUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateId",
                        false, "multiMemberGuid"))).thenThrow(WxErrorException.class);

        // Run the test
        wxStoreAuthorizerInfoServiceImplUnderTest.getQrCodeUrl(wxQrCodeUrlQuery);
    }

    @Test
    public void testGetQueueQrCodeUrl() throws Exception {
        // Setup
        final WxQueueInfoReqDTO wxQueueInfoReqDTO = new WxQueueInfoReqDTO("queueGuid");

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("f4d99c7c-8af8-40ab-a277-95059c034783");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        when(mockWeChatConfig.getQUEUE_DETAIL_PAGE()).thenReturn("result");
        when(mockWxStoreMpService.shortenUrl("longUrl", "tableGuid", 0)).thenReturn("url");
        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "4e62d5fc-2e23-4ec5-b36d-1f269c7f68e8", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "authorizerAppid",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "originalUrl", 0,
                        0, "userName", "principalName", "alias", "originalUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateId",
                        false, "multiMemberGuid"))).thenReturn(null);

        // Run the test
        final String result = wxStoreAuthorizerInfoServiceImplUnderTest.getQueueQrCodeUrl(wxQueueInfoReqDTO);

        // Verify the results
        assertEquals("url", result);
        verify(mockRedisUtils).setEx("queueGuid", new WxQueueInfoReqDTO("queueGuid"), 43210L, TimeUnit.SECONDS);
    }

    @Test(expected = WxErrorException.class)
    public void testGetQueueQrCodeUrl_WxStoreMpServiceThrowsWxErrorException() throws Exception {
        // Setup
        final WxQueueInfoReqDTO wxQueueInfoReqDTO = new WxQueueInfoReqDTO("queueGuid");

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("f4d99c7c-8af8-40ab-a277-95059c034783");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        when(mockWeChatConfig.getQUEUE_DETAIL_PAGE()).thenReturn("result");
        when(mockWxStoreMpService.shortenUrl("longUrl", "tableGuid", 0)).thenThrow(WxErrorException.class);

        // Run the test
        wxStoreAuthorizerInfoServiceImplUnderTest.getQueueQrCodeUrl(wxQueueInfoReqDTO);
    }

    @Test(expected = WxErrorException.class)
    public void testGetQueueQrCodeUrl_WxSaasMpServiceThrowsWxErrorException() throws Exception {
        // Setup
        final WxQueueInfoReqDTO wxQueueInfoReqDTO = new WxQueueInfoReqDTO("queueGuid");

        // Configure OrganizationClientService.queryStoreByGuid(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("f4d99c7c-8af8-40ab-a277-95059c034783");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockOrganizationClientService.queryStoreByGuid("storeGuid")).thenReturn(storeDTO);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "4e62d5fc-2e23-4ec5-b36d-1f269c7f68e8", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "authorizerAppid",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "originalUrl", 0,
                        0, "userName", "principalName", "alias", "originalUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateId",
                        false, "multiMemberGuid"))).thenThrow(WxErrorException.class);

        // Run the test
        wxStoreAuthorizerInfoServiceImplUnderTest.getQueueQrCodeUrl(wxQueueInfoReqDTO);
    }

    @Test
    public void testUpdateWxAuthorizeInfo() {
        // Setup
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "4e62d5fc-2e23-4ec5-b36d-1f269c7f68e8", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "authorizerAppid", "authorizerAccessToken", 0L,
                "authorizerRefreshToken", "funcInfo", "nickName", "originalUrl", 0, 0, "userName", "principalName",
                "alias", "originalUrl", "signature", "unBandUserGuid", "unBandUserName", "unBandUserTel",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateId", false, "multiMemberGuid");

        // Run the test
        final boolean result = wxStoreAuthorizerInfoServiceImplUnderTest.updateWxAuthorizeInfo(wxStoreAuthorizerInfoDO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testGetByAppId() {
        // Setup
        final WxStoreAuthorizerInfoDO expectedResult = new WxStoreAuthorizerInfoDO(0L,
                "4e62d5fc-2e23-4ec5-b36d-1f269c7f68e8", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "authorizerAppid", "authorizerAccessToken", 0L,
                "authorizerRefreshToken", "funcInfo", "nickName", "originalUrl", 0, 0, "userName", "principalName",
                "alias", "originalUrl", "signature", "unBandUserGuid", "unBandUserName", "unBandUserTel",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateId", false, "multiMemberGuid");

        // Run the test
        final WxStoreAuthorizerInfoDO result = wxStoreAuthorizerInfoServiceImplUnderTest.getByAppId("appId");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetByBrandId() {
        // Setup
        final WxStoreAuthorizerInfoDO expectedResult = new WxStoreAuthorizerInfoDO(0L,
                "4e62d5fc-2e23-4ec5-b36d-1f269c7f68e8", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "authorizerAppid", "authorizerAccessToken", 0L,
                "authorizerRefreshToken", "funcInfo", "nickName", "originalUrl", 0, 0, "userName", "principalName",
                "alias", "originalUrl", "signature", "unBandUserGuid", "unBandUserName", "unBandUserTel",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateId", false, "multiMemberGuid");

        // Run the test
        final WxStoreAuthorizerInfoDO result = wxStoreAuthorizerInfoServiceImplUnderTest.getByBrandId("brandId");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testBindSubject() {
        // Setup
        final WxOperSubjectBrandReqDTO wxOperSubjectBrandReqDTO = new WxOperSubjectBrandReqDTO();
        wxOperSubjectBrandReqDTO.setBrandGuid("brandGuid");
        wxOperSubjectBrandReqDTO.setIsAlliance(false);
        wxOperSubjectBrandReqDTO.setOperSubjectGuid("multiMemberGuid");

        // Run the test
        final Boolean result = wxStoreAuthorizerInfoServiceImplUnderTest.bindSubject(wxOperSubjectBrandReqDTO);

        // Verify the results
        assertFalse(result);
    }
}
