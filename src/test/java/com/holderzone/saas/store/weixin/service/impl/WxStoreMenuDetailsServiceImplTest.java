package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.holder.saas.member.wechat.dto.member.RequestQueryMemberInfo;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfo;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.weixin.MultiMemberDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreWebSocketUserDTO;
import com.holderzone.saas.store.dto.weixin.common.WxStoreInfoDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreMenuReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.*;
import com.holderzone.saas.store.weixin.config.ResponseModel;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerInfoDO;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreConfigRespDTOMapstruct;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreItemRespMapstruct;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.rpc.*;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStoreMenuDetailsServiceImplTest {

    @Mock
    private WxStoreMenuItemClientService mockWxStoreMenuItemClientService;
    @Mock
    private WxStoreOrderConfigService mockWxStoreOrderConfigService;
    @Mock
    private WxStoreConfigRespDTOMapstruct mockWxStoreConfigRespDTOMapstruct;
    @Mock
    private WxStoreItemRespMapstruct mockWxStoreItemRespMapstruct;
    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private WxStoreDineInOrderClientService mockWxStoreDineInOrderClientService;
    @Mock
    private WxStoreTableClientService mockWxStoreTableClientService;
    @Mock
    private WxStoreSessionDetailsService mockWxStoreSessionDetailsService;
    @Mock
    private WxConfigOverviewService mockWxConfigOverviewService;
    @Mock
    private WxStoreEstimateClientService mockWxStoreEstimateClientService;
    @Mock
    private WxUserRecordService mockWxUserRecordService;
    @Mock
    private WxStoreAuthorizerInfoService mockWxStoreAuthorizerInfoService;
    @Mock
    private EnterpriseClientService mockEnterpriseClientService;
    @Mock
    private WxStoreSocketSessionService mockWxStoreSocketSessionService;
    @Mock
    private MemberClientService mockMemberClientService;
    @Mock
    private HsaBaseClientService mockHsaBaseClientService;

    private WxStoreMenuDetailsServiceImpl wxStoreMenuDetailsServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxStoreMenuDetailsServiceImplUnderTest = new WxStoreMenuDetailsServiceImpl(mockWxStoreMenuItemClientService,
                mockWxStoreOrderConfigService, mockWxStoreConfigRespDTOMapstruct, mockWxStoreItemRespMapstruct,
                mockRedisUtils, mockDynamicHelper, mockWxStoreDineInOrderClientService, mockWxStoreTableClientService,
                mockWxStoreSessionDetailsService, mockWxConfigOverviewService, mockWxStoreEstimateClientService,
                mockWxUserRecordService, mockWxStoreAuthorizerInfoService, mockEnterpriseClientService);
        ReflectionTestUtils.setField(wxStoreMenuDetailsServiceImplUnderTest, "wxStoreSocketSessionService",
                mockWxStoreSocketSessionService);
        ReflectionTestUtils.setField(wxStoreMenuDetailsServiceImplUnderTest, "memberClientService",
                mockMemberClientService);
        ReflectionTestUtils.setField(wxStoreMenuDetailsServiceImplUnderTest, "hsaBaseClientService",
                mockHsaBaseClientService);
    }

    @Test
    public void testGetWxMenuDetails() {
        // Setup
        final WxStoreMenuReqDTO wxStoreMenuReqDTO = new WxStoreMenuReqDTO("enterpriseGuid", "storeGuid", 0,
                WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandName("brandName")
                        .brandLogo("brandLogo")
                        .isLogin(false)
                        .phoneNum("phoneNum")
                        .isAlliance(false)
                        .operSubjectGuid("operSubjectGuid")
                        .multiMemberStatus(false)
                        .build());
        final WxMenuDetailsDTO expectedResult = WxMenuDetailsDTO.builder()
                .itemList(Arrays.asList(new WxStoreItemRespDTO()))
                .typeList(Arrays.asList(new WxTypeAndTagDTO()))
                .wxStoreConfigRespDTO(new WxStoreConfigRespDTO())
                .isJump(0)
                .first(0)
                .build();
        when(mockWxStoreSessionDetailsService.getOrderState("diningTableGuid")).thenReturn(0);

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setStoreGuid("storeGuid");
        wxOrderConfigDTO.setStoreName("storeName");
        wxOrderConfigDTO.setIsOpened(false);
        wxOrderConfigDTO.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setMenuType(0);
        wxOrderConfigDTO.setTagNames("tagNames");
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        // Configure WxStoreSocketSessionService.getSingleSession(...).
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandName("brandName")
                        .brandLogo("brandLogo")
                        .isLogin(false)
                        .phoneNum("phoneNum")
                        .isAlliance(false)
                        .operSubjectGuid("operSubjectGuid")
                        .multiMemberStatus(false)
                        .build())
                .build();
        when(mockWxStoreSocketSessionService.getSingleSession(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandName("brandName")
                        .brandLogo("brandLogo")
                        .isLogin(false)
                        .phoneNum("phoneNum")
                        .isAlliance(false)
                        .operSubjectGuid("operSubjectGuid")
                        .multiMemberStatus(false)
                        .build())
                .build())).thenReturn(wxStoreAdvanceConsumerReqDTO);

        when(mockWxStoreTableClientService.getOrderGuid("diningTableGuid")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("fd0ff024-4feb-477e-8d57-fe30fa7f9cbc");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("17d2bb3d-eabe-461f-a30e-81c9792bf363");
        dineinOrderDetailRespDTO.setReserveItems(Arrays.asList(dineInItemDTO));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("17d2bb3d-eabe-461f-a30e-81c9792bf363");
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreMenuItemClientService.getItemsForWeixin(...).
        final ItemAndTypeForAndroidRespDTO itemAndTypeForAndroidRespDTO = new ItemAndTypeForAndroidRespDTO();
        final ItemSynRespDTO itemSynRespDTO = new ItemSynRespDTO();
        itemSynRespDTO.setItemType(0);
        itemSynRespDTO.setIsFixPkg(0);
        final SkuSynRespDTO skuSynRespDTO = new SkuSynRespDTO();
        skuSynRespDTO.setIsJoinWeChat(0);
        itemSynRespDTO.setSkuList(Arrays.asList(skuSynRespDTO));
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setIsMultiChoice(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        itemSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        final AttrGroupSynRespDTO attrGroupSynRespDTO1 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO1.setIsRequired(0);
        attrGroupSynRespDTO1.setIsMultiChoice(0);
        final AttrSynRespDTO attrSynRespDTO1 = new AttrSynRespDTO();
        attrSynRespDTO1.setIsDefault(0);
        attrGroupSynRespDTO1.setAttrList(Arrays.asList(attrSynRespDTO1));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO1));
        subItemSkuSynRespDTO.setIsSoldOut(0);
        subItemSkuSynRespDTO.setIsJoinWeChat(0);
        subItemSkuSynRespDTO.setResidueQuantity(new BigDecimal("0.00"));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        itemSynRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        itemAndTypeForAndroidRespDTO.setItemList(Arrays.asList(itemSynRespDTO));
        final TypeSynRespDTO typeSynRespDTO = new TypeSynRespDTO();
        typeSynRespDTO.setTypeGuid("typeGuid");
        typeSynRespDTO.setName("name");
        typeSynRespDTO.setSort(0);
        itemAndTypeForAndroidRespDTO.setTypeList(Arrays.asList(typeSynRespDTO));
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockWxStoreMenuItemClientService.getItemsForWeixin(baseDTO)).thenReturn(itemAndTypeForAndroidRespDTO);

        // Configure WxConfigOverviewService.isStoreOpen(...).
        final Pair<WxStoreInfoDTO, Boolean> wxStoreInfoDTOBooleanPair = Pair.of(
                new WxStoreInfoDTO("storeGuid", "storeName", "tel", "address", false, 0, new BigDecimal("0.00"),
                        new BigDecimal("0.00"), Arrays.asList("value")), false);
        when(mockWxConfigOverviewService.isStoreOpen(0, "storeGuid")).thenReturn(wxStoreInfoDTOBooleanPair);

        // Configure WxStoreItemRespMapstruct.getWxStoreItem(...).
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        final TagRespDTO tagRespDTO = new TagRespDTO();
        tagRespDTO.setName("id");
        tagRespDTO.setId("id");
        wxStoreItemRespDTO.setTagList(Arrays.asList(tagRespDTO));
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setMinOrderNum(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreSkuRespDTO.setIsSoldOut(0);
        wxStoreSkuRespDTO.setResidueQuantity(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO1 = new SubgroupSynRespDTO();
        subgroupSynRespDTO1.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO1 = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO1.setSkuGuid("skuGuid");
        final AttrGroupSynRespDTO attrGroupSynRespDTO2 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO2.setIsRequired(0);
        attrGroupSynRespDTO2.setIsMultiChoice(0);
        final AttrSynRespDTO attrSynRespDTO2 = new AttrSynRespDTO();
        attrSynRespDTO2.setIsDefault(0);
        attrGroupSynRespDTO2.setAttrList(Arrays.asList(attrSynRespDTO2));
        subItemSkuSynRespDTO1.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO2));
        subItemSkuSynRespDTO1.setIsSoldOut(0);
        subItemSkuSynRespDTO1.setIsJoinWeChat(0);
        subItemSkuSynRespDTO1.setResidueQuantity(new BigDecimal("0.00"));
        subgroupSynRespDTO1.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO1));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO1));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        final List<WxStoreItemRespDTO> wxStoreItemRespDTOS = Arrays.asList(wxStoreItemRespDTO);
        final ItemSynRespDTO itemSynRespDTO2 = new ItemSynRespDTO();
        itemSynRespDTO2.setItemType(0);
        itemSynRespDTO2.setIsFixPkg(0);
        final SkuSynRespDTO skuSynRespDTO1 = new SkuSynRespDTO();
        skuSynRespDTO1.setIsJoinWeChat(0);
        itemSynRespDTO2.setSkuList(Arrays.asList(skuSynRespDTO1));
        final AttrGroupSynRespDTO attrGroupSynRespDTO3 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO3.setIsRequired(0);
        attrGroupSynRespDTO3.setIsMultiChoice(0);
        final AttrSynRespDTO attrSynRespDTO3 = new AttrSynRespDTO();
        attrSynRespDTO3.setIsDefault(0);
        attrGroupSynRespDTO3.setAttrList(Arrays.asList(attrSynRespDTO3));
        itemSynRespDTO2.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO3));
        final SubgroupSynRespDTO subgroupSynRespDTO2 = new SubgroupSynRespDTO();
        subgroupSynRespDTO2.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO2 = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO2.setSkuGuid("skuGuid");
        final AttrGroupSynRespDTO attrGroupSynRespDTO4 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO4.setIsRequired(0);
        attrGroupSynRespDTO4.setIsMultiChoice(0);
        final AttrSynRespDTO attrSynRespDTO4 = new AttrSynRespDTO();
        attrSynRespDTO4.setIsDefault(0);
        attrGroupSynRespDTO4.setAttrList(Arrays.asList(attrSynRespDTO4));
        subItemSkuSynRespDTO2.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO4));
        subItemSkuSynRespDTO2.setIsSoldOut(0);
        subItemSkuSynRespDTO2.setIsJoinWeChat(0);
        subItemSkuSynRespDTO2.setResidueQuantity(new BigDecimal("0.00"));
        subgroupSynRespDTO2.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO2));
        itemSynRespDTO2.setSubgroupList(Arrays.asList(subgroupSynRespDTO2));
        final List<ItemSynRespDTO> itemSynRespDTO1 = Arrays.asList(itemSynRespDTO2);
        when(mockWxStoreItemRespMapstruct.getWxStoreItem(itemSynRespDTO1)).thenReturn(wxStoreItemRespDTOS);

        // Configure WxStoreConfigRespDTOMapstruct.wxStoreOrderConfigRespDTO2WxStoreConfigRespDTO(...).
        final WxStoreConfigRespDTO wxStoreConfigRespDTO = new WxStoreConfigRespDTO();
        wxStoreConfigRespDTO.setStoreGuid("storeGuid");
        wxStoreConfigRespDTO.setStoreName("storeName");
        wxStoreConfigRespDTO.setIsOpened(false);
        wxStoreConfigRespDTO.setBrandNameList(Arrays.asList("value"));
        wxStoreConfigRespDTO.setIsOrderOpen(0);
        final WxOrderConfigDTO wxOrderConfigDTO1 = new WxOrderConfigDTO();
        wxOrderConfigDTO1.setStoreGuid("storeGuid");
        wxOrderConfigDTO1.setStoreName("storeName");
        wxOrderConfigDTO1.setIsOpened(false);
        wxOrderConfigDTO1.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO1.setOrderModel(0);
        wxOrderConfigDTO1.setTakingModel(0);
        wxOrderConfigDTO1.setMenuType(0);
        wxOrderConfigDTO1.setTagNames("tagNames");
        when(mockWxStoreConfigRespDTOMapstruct.wxStoreOrderConfigRespDTO2WxStoreConfigRespDTO(
                wxOrderConfigDTO1)).thenReturn(wxStoreConfigRespDTO);

        // Configure WxStoreEstimateClientService.queryEstimateForSyn(...).
        final List<ItemEstimateForAndroidRespDTO> itemEstimateForAndroidRespDTOS = Arrays.asList(
                ItemEstimateForAndroidRespDTO.builder()
                        .skuGuid("skuGuid")
                        .isSoldOut(0)
                        .residueQuantity(new BigDecimal("0.00"))
                        .build());
        final BaseDTO baseDTO1 = new BaseDTO();
        baseDTO1.setDeviceType(0);
        baseDTO1.setDeviceId("deviceId");
        baseDTO1.setEnterpriseGuid("enterpriseGuid");
        baseDTO1.setEnterpriseName("enterpriseName");
        baseDTO1.setStoreGuid("storeGuid");
        when(mockWxStoreEstimateClientService.queryEstimateForSyn(baseDTO1)).thenReturn(itemEstimateForAndroidRespDTOS);

        // Configure WxStoreSessionDetailsService.getFirstPerson(...).
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .diningTableGuid("diningTableGuid")
                .brandName("brandName")
                .brandLogo("brandLogo")
                .isLogin(false)
                .phoneNum("phoneNum")
                .isAlliance(false)
                .operSubjectGuid("operSubjectGuid")
                .multiMemberStatus(false)
                .build();
        when(mockWxStoreSessionDetailsService.getFirstPerson("openId")).thenReturn(wxStoreConsumerDTO);

        // Run the test
        final WxMenuDetailsDTO result = wxStoreMenuDetailsServiceImplUnderTest.getWxMenuDetails(wxStoreMenuReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
        verify(mockWxStoreSessionDetailsService).delOrderState("diningTableGuid");
        verify(mockWxStoreSessionDetailsService).delOrderGuid("diningTableGuid");
        verify(mockWxStoreSessionDetailsService).delMerchantBatchGuid("diningTableGuid");
        verify(mockWxStoreSessionDetailsService).delDinnerGuestsCount("diningTableGuid");
        verify(mockWxStoreSessionDetailsService).delTablePaidUser("diningTableGuid");
        verify(mockWxStoreSessionDetailsService).delFirstPerson("openId");
        verify(mockWxStoreSessionDetailsService).saveOrderState("diningTableGuid", 0);
        verify(mockWxStoreSocketSessionService).delTableSession("diningTableGuid");
        verify(mockWxStoreSocketSessionService).createSession(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandName("brandName")
                        .brandLogo("brandLogo")
                        .isLogin(false)
                        .phoneNum("phoneNum")
                        .isAlliance(false)
                        .operSubjectGuid("operSubjectGuid")
                        .multiMemberStatus(false)
                        .build())
                .build());
        verify(mockWxStoreSessionDetailsService).saveFirstPerson(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .diningTableGuid("diningTableGuid")
                .brandName("brandName")
                .brandLogo("brandLogo")
                .isLogin(false)
                .phoneNum("phoneNum")
                .isAlliance(false)
                .operSubjectGuid("operSubjectGuid")
                .multiMemberStatus(false)
                .build());
    }

    @Test
    public void testGetWxMenuDetails_WxStoreItemRespMapstructReturnsNoItems() {
        // Setup
        final WxStoreMenuReqDTO wxStoreMenuReqDTO = new WxStoreMenuReqDTO("enterpriseGuid", "storeGuid", 0,
                WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandName("brandName")
                        .brandLogo("brandLogo")
                        .isLogin(false)
                        .phoneNum("phoneNum")
                        .isAlliance(false)
                        .operSubjectGuid("operSubjectGuid")
                        .multiMemberStatus(false)
                        .build());
        final WxMenuDetailsDTO expectedResult = WxMenuDetailsDTO.builder()
                .itemList(Arrays.asList(new WxStoreItemRespDTO()))
                .typeList(Arrays.asList(new WxTypeAndTagDTO()))
                .wxStoreConfigRespDTO(new WxStoreConfigRespDTO())
                .isJump(0)
                .first(0)
                .build();
        when(mockWxStoreSessionDetailsService.getOrderState("diningTableGuid")).thenReturn(0);

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setStoreGuid("storeGuid");
        wxOrderConfigDTO.setStoreName("storeName");
        wxOrderConfigDTO.setIsOpened(false);
        wxOrderConfigDTO.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setMenuType(0);
        wxOrderConfigDTO.setTagNames("tagNames");
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        // Configure WxStoreSocketSessionService.getSingleSession(...).
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandName("brandName")
                        .brandLogo("brandLogo")
                        .isLogin(false)
                        .phoneNum("phoneNum")
                        .isAlliance(false)
                        .operSubjectGuid("operSubjectGuid")
                        .multiMemberStatus(false)
                        .build())
                .build();
        when(mockWxStoreSocketSessionService.getSingleSession(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandName("brandName")
                        .brandLogo("brandLogo")
                        .isLogin(false)
                        .phoneNum("phoneNum")
                        .isAlliance(false)
                        .operSubjectGuid("operSubjectGuid")
                        .multiMemberStatus(false)
                        .build())
                .build())).thenReturn(wxStoreAdvanceConsumerReqDTO);

        when(mockWxStoreTableClientService.getOrderGuid("diningTableGuid")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("fd0ff024-4feb-477e-8d57-fe30fa7f9cbc");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("17d2bb3d-eabe-461f-a30e-81c9792bf363");
        dineinOrderDetailRespDTO.setReserveItems(Arrays.asList(dineInItemDTO));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("17d2bb3d-eabe-461f-a30e-81c9792bf363");
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreMenuItemClientService.getItemsForWeixin(...).
        final ItemAndTypeForAndroidRespDTO itemAndTypeForAndroidRespDTO = new ItemAndTypeForAndroidRespDTO();
        final ItemSynRespDTO itemSynRespDTO = new ItemSynRespDTO();
        itemSynRespDTO.setItemType(0);
        itemSynRespDTO.setIsFixPkg(0);
        final SkuSynRespDTO skuSynRespDTO = new SkuSynRespDTO();
        skuSynRespDTO.setIsJoinWeChat(0);
        itemSynRespDTO.setSkuList(Arrays.asList(skuSynRespDTO));
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setIsMultiChoice(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        itemSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        final AttrGroupSynRespDTO attrGroupSynRespDTO1 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO1.setIsRequired(0);
        attrGroupSynRespDTO1.setIsMultiChoice(0);
        final AttrSynRespDTO attrSynRespDTO1 = new AttrSynRespDTO();
        attrSynRespDTO1.setIsDefault(0);
        attrGroupSynRespDTO1.setAttrList(Arrays.asList(attrSynRespDTO1));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO1));
        subItemSkuSynRespDTO.setIsSoldOut(0);
        subItemSkuSynRespDTO.setIsJoinWeChat(0);
        subItemSkuSynRespDTO.setResidueQuantity(new BigDecimal("0.00"));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        itemSynRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        itemAndTypeForAndroidRespDTO.setItemList(Arrays.asList(itemSynRespDTO));
        final TypeSynRespDTO typeSynRespDTO = new TypeSynRespDTO();
        typeSynRespDTO.setTypeGuid("typeGuid");
        typeSynRespDTO.setName("name");
        typeSynRespDTO.setSort(0);
        itemAndTypeForAndroidRespDTO.setTypeList(Arrays.asList(typeSynRespDTO));
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockWxStoreMenuItemClientService.getItemsForWeixin(baseDTO)).thenReturn(itemAndTypeForAndroidRespDTO);

        // Configure WxConfigOverviewService.isStoreOpen(...).
        final Pair<WxStoreInfoDTO, Boolean> wxStoreInfoDTOBooleanPair = Pair.of(
                new WxStoreInfoDTO("storeGuid", "storeName", "tel", "address", false, 0, new BigDecimal("0.00"),
                        new BigDecimal("0.00"), Arrays.asList("value")), false);
        when(mockWxConfigOverviewService.isStoreOpen(0, "storeGuid")).thenReturn(wxStoreInfoDTOBooleanPair);

        // Configure WxStoreItemRespMapstruct.getWxStoreItem(...).
        final ItemSynRespDTO itemSynRespDTO2 = new ItemSynRespDTO();
        itemSynRespDTO2.setItemType(0);
        itemSynRespDTO2.setIsFixPkg(0);
        final SkuSynRespDTO skuSynRespDTO1 = new SkuSynRespDTO();
        skuSynRespDTO1.setIsJoinWeChat(0);
        itemSynRespDTO2.setSkuList(Arrays.asList(skuSynRespDTO1));
        final AttrGroupSynRespDTO attrGroupSynRespDTO2 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO2.setIsRequired(0);
        attrGroupSynRespDTO2.setIsMultiChoice(0);
        final AttrSynRespDTO attrSynRespDTO2 = new AttrSynRespDTO();
        attrSynRespDTO2.setIsDefault(0);
        attrGroupSynRespDTO2.setAttrList(Arrays.asList(attrSynRespDTO2));
        itemSynRespDTO2.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO2));
        final SubgroupSynRespDTO subgroupSynRespDTO1 = new SubgroupSynRespDTO();
        subgroupSynRespDTO1.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO1 = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO1.setSkuGuid("skuGuid");
        final AttrGroupSynRespDTO attrGroupSynRespDTO3 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO3.setIsRequired(0);
        attrGroupSynRespDTO3.setIsMultiChoice(0);
        final AttrSynRespDTO attrSynRespDTO3 = new AttrSynRespDTO();
        attrSynRespDTO3.setIsDefault(0);
        attrGroupSynRespDTO3.setAttrList(Arrays.asList(attrSynRespDTO3));
        subItemSkuSynRespDTO1.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO3));
        subItemSkuSynRespDTO1.setIsSoldOut(0);
        subItemSkuSynRespDTO1.setIsJoinWeChat(0);
        subItemSkuSynRespDTO1.setResidueQuantity(new BigDecimal("0.00"));
        subgroupSynRespDTO1.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO1));
        itemSynRespDTO2.setSubgroupList(Arrays.asList(subgroupSynRespDTO1));
        final List<ItemSynRespDTO> itemSynRespDTO1 = Arrays.asList(itemSynRespDTO2);
        when(mockWxStoreItemRespMapstruct.getWxStoreItem(itemSynRespDTO1)).thenReturn(Collections.emptyList());

        // Configure WxStoreConfigRespDTOMapstruct.wxStoreOrderConfigRespDTO2WxStoreConfigRespDTO(...).
        final WxStoreConfigRespDTO wxStoreConfigRespDTO = new WxStoreConfigRespDTO();
        wxStoreConfigRespDTO.setStoreGuid("storeGuid");
        wxStoreConfigRespDTO.setStoreName("storeName");
        wxStoreConfigRespDTO.setIsOpened(false);
        wxStoreConfigRespDTO.setBrandNameList(Arrays.asList("value"));
        wxStoreConfigRespDTO.setIsOrderOpen(0);
        final WxOrderConfigDTO wxOrderConfigDTO1 = new WxOrderConfigDTO();
        wxOrderConfigDTO1.setStoreGuid("storeGuid");
        wxOrderConfigDTO1.setStoreName("storeName");
        wxOrderConfigDTO1.setIsOpened(false);
        wxOrderConfigDTO1.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO1.setOrderModel(0);
        wxOrderConfigDTO1.setTakingModel(0);
        wxOrderConfigDTO1.setMenuType(0);
        wxOrderConfigDTO1.setTagNames("tagNames");
        when(mockWxStoreConfigRespDTOMapstruct.wxStoreOrderConfigRespDTO2WxStoreConfigRespDTO(
                wxOrderConfigDTO1)).thenReturn(wxStoreConfigRespDTO);

        // Configure WxStoreEstimateClientService.queryEstimateForSyn(...).
        final List<ItemEstimateForAndroidRespDTO> itemEstimateForAndroidRespDTOS = Arrays.asList(
                ItemEstimateForAndroidRespDTO.builder()
                        .skuGuid("skuGuid")
                        .isSoldOut(0)
                        .residueQuantity(new BigDecimal("0.00"))
                        .build());
        final BaseDTO baseDTO1 = new BaseDTO();
        baseDTO1.setDeviceType(0);
        baseDTO1.setDeviceId("deviceId");
        baseDTO1.setEnterpriseGuid("enterpriseGuid");
        baseDTO1.setEnterpriseName("enterpriseName");
        baseDTO1.setStoreGuid("storeGuid");
        when(mockWxStoreEstimateClientService.queryEstimateForSyn(baseDTO1)).thenReturn(itemEstimateForAndroidRespDTOS);

        // Configure WxStoreSessionDetailsService.getFirstPerson(...).
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .diningTableGuid("diningTableGuid")
                .brandName("brandName")
                .brandLogo("brandLogo")
                .isLogin(false)
                .phoneNum("phoneNum")
                .isAlliance(false)
                .operSubjectGuid("operSubjectGuid")
                .multiMemberStatus(false)
                .build();
        when(mockWxStoreSessionDetailsService.getFirstPerson("openId")).thenReturn(wxStoreConsumerDTO);

        // Run the test
        final WxMenuDetailsDTO result = wxStoreMenuDetailsServiceImplUnderTest.getWxMenuDetails(wxStoreMenuReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
        verify(mockWxStoreSessionDetailsService).delOrderState("diningTableGuid");
        verify(mockWxStoreSessionDetailsService).delOrderGuid("diningTableGuid");
        verify(mockWxStoreSessionDetailsService).delMerchantBatchGuid("diningTableGuid");
        verify(mockWxStoreSessionDetailsService).delDinnerGuestsCount("diningTableGuid");
        verify(mockWxStoreSessionDetailsService).delTablePaidUser("diningTableGuid");
        verify(mockWxStoreSessionDetailsService).delFirstPerson("openId");
        verify(mockWxStoreSessionDetailsService).saveOrderState("diningTableGuid", 0);
        verify(mockWxStoreSocketSessionService).delTableSession("diningTableGuid");
        verify(mockWxStoreSocketSessionService).createSession(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandName("brandName")
                        .brandLogo("brandLogo")
                        .isLogin(false)
                        .phoneNum("phoneNum")
                        .isAlliance(false)
                        .operSubjectGuid("operSubjectGuid")
                        .multiMemberStatus(false)
                        .build())
                .build());
        verify(mockWxStoreSessionDetailsService).saveFirstPerson(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .diningTableGuid("diningTableGuid")
                .brandName("brandName")
                .brandLogo("brandLogo")
                .isLogin(false)
                .phoneNum("phoneNum")
                .isAlliance(false)
                .operSubjectGuid("operSubjectGuid")
                .multiMemberStatus(false)
                .build());
    }

    @Test
    public void testGetWxMenuDetails_WxStoreEstimateClientServiceReturnsNoItems() {
        // Setup
        final WxStoreMenuReqDTO wxStoreMenuReqDTO = new WxStoreMenuReqDTO("enterpriseGuid", "storeGuid", 0,
                WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandName("brandName")
                        .brandLogo("brandLogo")
                        .isLogin(false)
                        .phoneNum("phoneNum")
                        .isAlliance(false)
                        .operSubjectGuid("operSubjectGuid")
                        .multiMemberStatus(false)
                        .build());
        final WxMenuDetailsDTO expectedResult = WxMenuDetailsDTO.builder()
                .itemList(Arrays.asList(new WxStoreItemRespDTO()))
                .typeList(Arrays.asList(new WxTypeAndTagDTO()))
                .wxStoreConfigRespDTO(new WxStoreConfigRespDTO())
                .isJump(0)
                .first(0)
                .build();
        when(mockWxStoreSessionDetailsService.getOrderState("diningTableGuid")).thenReturn(0);

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setStoreGuid("storeGuid");
        wxOrderConfigDTO.setStoreName("storeName");
        wxOrderConfigDTO.setIsOpened(false);
        wxOrderConfigDTO.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setMenuType(0);
        wxOrderConfigDTO.setTagNames("tagNames");
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        // Configure WxStoreSocketSessionService.getSingleSession(...).
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandName("brandName")
                        .brandLogo("brandLogo")
                        .isLogin(false)
                        .phoneNum("phoneNum")
                        .isAlliance(false)
                        .operSubjectGuid("operSubjectGuid")
                        .multiMemberStatus(false)
                        .build())
                .build();
        when(mockWxStoreSocketSessionService.getSingleSession(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandName("brandName")
                        .brandLogo("brandLogo")
                        .isLogin(false)
                        .phoneNum("phoneNum")
                        .isAlliance(false)
                        .operSubjectGuid("operSubjectGuid")
                        .multiMemberStatus(false)
                        .build())
                .build())).thenReturn(wxStoreAdvanceConsumerReqDTO);

        when(mockWxStoreTableClientService.getOrderGuid("diningTableGuid")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("fd0ff024-4feb-477e-8d57-fe30fa7f9cbc");
        dineinOrderDetailRespDTO.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("17d2bb3d-eabe-461f-a30e-81c9792bf363");
        dineinOrderDetailRespDTO.setReserveItems(Arrays.asList(dineInItemDTO));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("17d2bb3d-eabe-461f-a30e-81c9792bf363");
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreMenuItemClientService.getItemsForWeixin(...).
        final ItemAndTypeForAndroidRespDTO itemAndTypeForAndroidRespDTO = new ItemAndTypeForAndroidRespDTO();
        final ItemSynRespDTO itemSynRespDTO = new ItemSynRespDTO();
        itemSynRespDTO.setItemType(0);
        itemSynRespDTO.setIsFixPkg(0);
        final SkuSynRespDTO skuSynRespDTO = new SkuSynRespDTO();
        skuSynRespDTO.setIsJoinWeChat(0);
        itemSynRespDTO.setSkuList(Arrays.asList(skuSynRespDTO));
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setIsMultiChoice(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        itemSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        final AttrGroupSynRespDTO attrGroupSynRespDTO1 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO1.setIsRequired(0);
        attrGroupSynRespDTO1.setIsMultiChoice(0);
        final AttrSynRespDTO attrSynRespDTO1 = new AttrSynRespDTO();
        attrSynRespDTO1.setIsDefault(0);
        attrGroupSynRespDTO1.setAttrList(Arrays.asList(attrSynRespDTO1));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO1));
        subItemSkuSynRespDTO.setIsSoldOut(0);
        subItemSkuSynRespDTO.setIsJoinWeChat(0);
        subItemSkuSynRespDTO.setResidueQuantity(new BigDecimal("0.00"));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        itemSynRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        itemAndTypeForAndroidRespDTO.setItemList(Arrays.asList(itemSynRespDTO));
        final TypeSynRespDTO typeSynRespDTO = new TypeSynRespDTO();
        typeSynRespDTO.setTypeGuid("typeGuid");
        typeSynRespDTO.setName("name");
        typeSynRespDTO.setSort(0);
        itemAndTypeForAndroidRespDTO.setTypeList(Arrays.asList(typeSynRespDTO));
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockWxStoreMenuItemClientService.getItemsForWeixin(baseDTO)).thenReturn(itemAndTypeForAndroidRespDTO);

        // Configure WxConfigOverviewService.isStoreOpen(...).
        final Pair<WxStoreInfoDTO, Boolean> wxStoreInfoDTOBooleanPair = Pair.of(
                new WxStoreInfoDTO("storeGuid", "storeName", "tel", "address", false, 0, new BigDecimal("0.00"),
                        new BigDecimal("0.00"), Arrays.asList("value")), false);
        when(mockWxConfigOverviewService.isStoreOpen(0, "storeGuid")).thenReturn(wxStoreInfoDTOBooleanPair);

        // Configure WxStoreItemRespMapstruct.getWxStoreItem(...).
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        final TagRespDTO tagRespDTO = new TagRespDTO();
        tagRespDTO.setName("id");
        tagRespDTO.setId("id");
        wxStoreItemRespDTO.setTagList(Arrays.asList(tagRespDTO));
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setMinOrderNum(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreSkuRespDTO.setIsSoldOut(0);
        wxStoreSkuRespDTO.setResidueQuantity(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO1 = new SubgroupSynRespDTO();
        subgroupSynRespDTO1.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO1 = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO1.setSkuGuid("skuGuid");
        final AttrGroupSynRespDTO attrGroupSynRespDTO2 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO2.setIsRequired(0);
        attrGroupSynRespDTO2.setIsMultiChoice(0);
        final AttrSynRespDTO attrSynRespDTO2 = new AttrSynRespDTO();
        attrSynRespDTO2.setIsDefault(0);
        attrGroupSynRespDTO2.setAttrList(Arrays.asList(attrSynRespDTO2));
        subItemSkuSynRespDTO1.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO2));
        subItemSkuSynRespDTO1.setIsSoldOut(0);
        subItemSkuSynRespDTO1.setIsJoinWeChat(0);
        subItemSkuSynRespDTO1.setResidueQuantity(new BigDecimal("0.00"));
        subgroupSynRespDTO1.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO1));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO1));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        final List<WxStoreItemRespDTO> wxStoreItemRespDTOS = Arrays.asList(wxStoreItemRespDTO);
        final ItemSynRespDTO itemSynRespDTO2 = new ItemSynRespDTO();
        itemSynRespDTO2.setItemType(0);
        itemSynRespDTO2.setIsFixPkg(0);
        final SkuSynRespDTO skuSynRespDTO1 = new SkuSynRespDTO();
        skuSynRespDTO1.setIsJoinWeChat(0);
        itemSynRespDTO2.setSkuList(Arrays.asList(skuSynRespDTO1));
        final AttrGroupSynRespDTO attrGroupSynRespDTO3 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO3.setIsRequired(0);
        attrGroupSynRespDTO3.setIsMultiChoice(0);
        final AttrSynRespDTO attrSynRespDTO3 = new AttrSynRespDTO();
        attrSynRespDTO3.setIsDefault(0);
        attrGroupSynRespDTO3.setAttrList(Arrays.asList(attrSynRespDTO3));
        itemSynRespDTO2.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO3));
        final SubgroupSynRespDTO subgroupSynRespDTO2 = new SubgroupSynRespDTO();
        subgroupSynRespDTO2.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO2 = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO2.setSkuGuid("skuGuid");
        final AttrGroupSynRespDTO attrGroupSynRespDTO4 = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO4.setIsRequired(0);
        attrGroupSynRespDTO4.setIsMultiChoice(0);
        final AttrSynRespDTO attrSynRespDTO4 = new AttrSynRespDTO();
        attrSynRespDTO4.setIsDefault(0);
        attrGroupSynRespDTO4.setAttrList(Arrays.asList(attrSynRespDTO4));
        subItemSkuSynRespDTO2.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO4));
        subItemSkuSynRespDTO2.setIsSoldOut(0);
        subItemSkuSynRespDTO2.setIsJoinWeChat(0);
        subItemSkuSynRespDTO2.setResidueQuantity(new BigDecimal("0.00"));
        subgroupSynRespDTO2.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO2));
        itemSynRespDTO2.setSubgroupList(Arrays.asList(subgroupSynRespDTO2));
        final List<ItemSynRespDTO> itemSynRespDTO1 = Arrays.asList(itemSynRespDTO2);
        when(mockWxStoreItemRespMapstruct.getWxStoreItem(itemSynRespDTO1)).thenReturn(wxStoreItemRespDTOS);

        // Configure WxStoreConfigRespDTOMapstruct.wxStoreOrderConfigRespDTO2WxStoreConfigRespDTO(...).
        final WxStoreConfigRespDTO wxStoreConfigRespDTO = new WxStoreConfigRespDTO();
        wxStoreConfigRespDTO.setStoreGuid("storeGuid");
        wxStoreConfigRespDTO.setStoreName("storeName");
        wxStoreConfigRespDTO.setIsOpened(false);
        wxStoreConfigRespDTO.setBrandNameList(Arrays.asList("value"));
        wxStoreConfigRespDTO.setIsOrderOpen(0);
        final WxOrderConfigDTO wxOrderConfigDTO1 = new WxOrderConfigDTO();
        wxOrderConfigDTO1.setStoreGuid("storeGuid");
        wxOrderConfigDTO1.setStoreName("storeName");
        wxOrderConfigDTO1.setIsOpened(false);
        wxOrderConfigDTO1.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO1.setOrderModel(0);
        wxOrderConfigDTO1.setTakingModel(0);
        wxOrderConfigDTO1.setMenuType(0);
        wxOrderConfigDTO1.setTagNames("tagNames");
        when(mockWxStoreConfigRespDTOMapstruct.wxStoreOrderConfigRespDTO2WxStoreConfigRespDTO(
                wxOrderConfigDTO1)).thenReturn(wxStoreConfigRespDTO);

        // Configure WxStoreEstimateClientService.queryEstimateForSyn(...).
        final BaseDTO baseDTO1 = new BaseDTO();
        baseDTO1.setDeviceType(0);
        baseDTO1.setDeviceId("deviceId");
        baseDTO1.setEnterpriseGuid("enterpriseGuid");
        baseDTO1.setEnterpriseName("enterpriseName");
        baseDTO1.setStoreGuid("storeGuid");
        when(mockWxStoreEstimateClientService.queryEstimateForSyn(baseDTO1)).thenReturn(Collections.emptyList());

        // Configure WxStoreSessionDetailsService.getFirstPerson(...).
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .diningTableGuid("diningTableGuid")
                .brandName("brandName")
                .brandLogo("brandLogo")
                .isLogin(false)
                .phoneNum("phoneNum")
                .isAlliance(false)
                .operSubjectGuid("operSubjectGuid")
                .multiMemberStatus(false)
                .build();
        when(mockWxStoreSessionDetailsService.getFirstPerson("openId")).thenReturn(wxStoreConsumerDTO);

        // Run the test
        final WxMenuDetailsDTO result = wxStoreMenuDetailsServiceImplUnderTest.getWxMenuDetails(wxStoreMenuReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
        verify(mockWxStoreSessionDetailsService).delOrderState("diningTableGuid");
        verify(mockWxStoreSessionDetailsService).delOrderGuid("diningTableGuid");
        verify(mockWxStoreSessionDetailsService).delMerchantBatchGuid("diningTableGuid");
        verify(mockWxStoreSessionDetailsService).delDinnerGuestsCount("diningTableGuid");
        verify(mockWxStoreSessionDetailsService).delTablePaidUser("diningTableGuid");
        verify(mockWxStoreSessionDetailsService).delFirstPerson("openId");
        verify(mockWxStoreSessionDetailsService).saveOrderState("diningTableGuid", 0);
        verify(mockWxStoreSocketSessionService).delTableSession("diningTableGuid");
        verify(mockWxStoreSocketSessionService).createSession(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandName("brandName")
                        .brandLogo("brandLogo")
                        .isLogin(false)
                        .phoneNum("phoneNum")
                        .isAlliance(false)
                        .operSubjectGuid("operSubjectGuid")
                        .multiMemberStatus(false)
                        .build())
                .build());
        verify(mockWxStoreSessionDetailsService).saveFirstPerson(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .diningTableGuid("diningTableGuid")
                .brandName("brandName")
                .brandLogo("brandLogo")
                .isLogin(false)
                .phoneNum("phoneNum")
                .isAlliance(false)
                .operSubjectGuid("operSubjectGuid")
                .multiMemberStatus(false)
                .build());
    }

    @Test
    public void testGetStoreConfiguration1() {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .diningTableGuid("diningTableGuid")
                .brandName("brandName")
                .brandLogo("brandLogo")
                .isLogin(false)
                .phoneNum("phoneNum")
                .isAlliance(false)
                .operSubjectGuid("operSubjectGuid")
                .multiMemberStatus(false)
                .build();
        final WxOrderConfigDTO expectedResult = new WxOrderConfigDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setIsOpened(false);
        expectedResult.setBrandNameList(Arrays.asList("value"));
        expectedResult.setOrderModel(0);
        expectedResult.setTakingModel(0);
        expectedResult.setMenuType(0);
        expectedResult.setTagNames("tagNames");

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setStoreGuid("storeGuid");
        wxOrderConfigDTO.setStoreName("storeName");
        wxOrderConfigDTO.setIsOpened(false);
        wxOrderConfigDTO.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setMenuType(0);
        wxOrderConfigDTO.setTagNames("tagNames");
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        // Run the test
        final WxOrderConfigDTO result = wxStoreMenuDetailsServiceImplUnderTest.getStoreConfiguration(
                wxStoreConsumerDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetStoreConfiguration2() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandName("brandName")
                        .brandLogo("brandLogo")
                        .isLogin(false)
                        .phoneNum("phoneNum")
                        .isAlliance(false)
                        .operSubjectGuid("operSubjectGuid")
                        .multiMemberStatus(false)
                        .build())
                .build();
        final WxOrderConfigDTO expectedResult = new WxOrderConfigDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setIsOpened(false);
        expectedResult.setBrandNameList(Arrays.asList("value"));
        expectedResult.setOrderModel(0);
        expectedResult.setTakingModel(0);
        expectedResult.setMenuType(0);
        expectedResult.setTagNames("tagNames");

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setStoreGuid("storeGuid");
        wxOrderConfigDTO.setStoreName("storeName");
        wxOrderConfigDTO.setIsOpened(false);
        wxOrderConfigDTO.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setMenuType(0);
        wxOrderConfigDTO.setTagNames("tagNames");
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        // Run the test
        final WxOrderConfigDTO result = wxStoreMenuDetailsServiceImplUnderTest.getStoreConfiguration(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testJudgeOrderType1() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandName("brandName")
                        .brandLogo("brandLogo")
                        .isLogin(false)
                        .phoneNum("phoneNum")
                        .isAlliance(false)
                        .operSubjectGuid("operSubjectGuid")
                        .multiMemberStatus(false)
                        .build())
                .build();

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setStoreGuid("storeGuid");
        wxOrderConfigDTO.setStoreName("storeName");
        wxOrderConfigDTO.setIsOpened(false);
        wxOrderConfigDTO.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setMenuType(0);
        wxOrderConfigDTO.setTagNames("tagNames");
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        // Run the test
        final Boolean result = wxStoreMenuDetailsServiceImplUnderTest.judgeOrderType(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testJudgeOrderType2() {
        // Setup
        final WxStoreWebSocketUserDTO wxStoreWebSocketUserDTO = new WxStoreWebSocketUserDTO();
        wxStoreWebSocketUserDTO.setSid("sid");
        wxStoreWebSocketUserDTO.setWxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .diningTableGuid("diningTableGuid")
                .brandName("brandName")
                .brandLogo("brandLogo")
                .isLogin(false)
                .phoneNum("phoneNum")
                .isAlliance(false)
                .operSubjectGuid("operSubjectGuid")
                .multiMemberStatus(false)
                .build());

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setStoreGuid("storeGuid");
        wxOrderConfigDTO.setStoreName("storeName");
        wxOrderConfigDTO.setIsOpened(false);
        wxOrderConfigDTO.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setMenuType(0);
        wxOrderConfigDTO.setTagNames("tagNames");
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        // Run the test
        final Boolean result = wxStoreMenuDetailsServiceImplUnderTest.judgeOrderType(wxStoreWebSocketUserDTO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testJudgeOrderType3() {
        // Setup
        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setStoreGuid("storeGuid");
        wxOrderConfigDTO.setStoreName("storeName");
        wxOrderConfigDTO.setIsOpened(false);
        wxOrderConfigDTO.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setMenuType(0);
        wxOrderConfigDTO.setTagNames("tagNames");
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        // Run the test
        final Boolean result = wxStoreMenuDetailsServiceImplUnderTest.judgeOrderType("storeGuid");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testGetDetailConfig() {
        // Setup
        final WxOrderConfigDTO expectedResult = new WxOrderConfigDTO();
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setIsOpened(false);
        expectedResult.setBrandNameList(Arrays.asList("value"));
        expectedResult.setOrderModel(0);
        expectedResult.setTakingModel(0);
        expectedResult.setMenuType(0);
        expectedResult.setTagNames("tagNames");

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setStoreGuid("storeGuid");
        wxOrderConfigDTO.setStoreName("storeName");
        wxOrderConfigDTO.setIsOpened(false);
        wxOrderConfigDTO.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setMenuType(0);
        wxOrderConfigDTO.setTagNames("tagNames");
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        // Run the test
        final WxOrderConfigDTO result = wxStoreMenuDetailsServiceImplUnderTest.getDetailConfig("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetTakingModel1() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandName("brandName")
                        .brandLogo("brandLogo")
                        .isLogin(false)
                        .phoneNum("phoneNum")
                        .isAlliance(false)
                        .operSubjectGuid("operSubjectGuid")
                        .multiMemberStatus(false)
                        .build())
                .build();

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setStoreGuid("storeGuid");
        wxOrderConfigDTO.setStoreName("storeName");
        wxOrderConfigDTO.setIsOpened(false);
        wxOrderConfigDTO.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setMenuType(0);
        wxOrderConfigDTO.setTagNames("tagNames");
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        // Run the test
        final Integer result = wxStoreMenuDetailsServiceImplUnderTest.getTakingModel(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testGetTakingModel2() {
        // Setup
        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setStoreGuid("storeGuid");
        wxOrderConfigDTO.setStoreName("storeName");
        wxOrderConfigDTO.setIsOpened(false);
        wxOrderConfigDTO.setBrandNameList(Arrays.asList("value"));
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setMenuType(0);
        wxOrderConfigDTO.setTagNames("tagNames");
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        // Run the test
        final Integer result = wxStoreMenuDetailsServiceImplUnderTest.getTakingModel("storeGuid");

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testGetConsumerInfo() {
        // Setup
        final WxPortalReqDTO wxPortalReqDTO = WxPortalReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .brandGuid("brandGuid")
                .msgKey("msgKey")
                .build();
        final WxStoreConsumerDTO expectedResult = WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .diningTableGuid("diningTableGuid")
                .brandName("brandName")
                .brandLogo("brandLogo")
                .isLogin(false)
                .phoneNum("phoneNum")
                .isAlliance(false)
                .operSubjectGuid("operSubjectGuid")
                .multiMemberStatus(false)
                .build();
        when(mockRedisUtils.get("msgKey")).thenReturn("result");

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setMemberInfoGuid("memberInfoGuid");
        responseMemberInfo.setOperSubjectGuid("operSubjectGuid");
        responseMemberInfo.setEnterpriseGuid("enterpriseGuid");
        responseMemberInfo.setPhoneNum("phoneNum");
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Configure WxStoreAuthorizerInfoService.getByBrandId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "da932772-5d3a-423b-9be9-7d49d66586b4", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "authorizerAppid", "authorizerAccessToken", 0L,
                "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias",
                "qrcodeUrl", "signature", "unBandUserGuid", "unBandUserName", "unBandUserTel",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByBrandId("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        // Configure EnterpriseClientService.list(...).
        final MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
        multiMemberDTO.setId(0L);
        multiMemberDTO.setEnterpriseGuid("enterpriseGuid");
        multiMemberDTO.setMultiMemberGuid("multiMemberGuid");
        multiMemberDTO.setMultiMemberName("multiMemberName");
        multiMemberDTO.setEnabled(false);
        final List<MultiMemberDTO> multiMemberDTOS = Arrays.asList(multiMemberDTO);
        when(mockEnterpriseClientService.list(new HashSet<>(Arrays.asList("value")))).thenReturn(multiMemberDTOS);

        // Run the test
        final WxStoreConsumerDTO result = wxStoreMenuDetailsServiceImplUnderTest.getConsumerInfo(wxPortalReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testGetConsumerInfo_EnterpriseClientServiceReturnsNoItems() {
        // Setup
        final WxPortalReqDTO wxPortalReqDTO = WxPortalReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .brandGuid("brandGuid")
                .msgKey("msgKey")
                .build();
        final WxStoreConsumerDTO expectedResult = WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .diningTableGuid("diningTableGuid")
                .brandName("brandName")
                .brandLogo("brandLogo")
                .isLogin(false)
                .phoneNum("phoneNum")
                .isAlliance(false)
                .operSubjectGuid("operSubjectGuid")
                .multiMemberStatus(false)
                .build();
        when(mockRedisUtils.get("msgKey")).thenReturn("result");

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setMemberInfoGuid("memberInfoGuid");
        responseMemberInfo.setOperSubjectGuid("operSubjectGuid");
        responseMemberInfo.setEnterpriseGuid("enterpriseGuid");
        responseMemberInfo.setPhoneNum("phoneNum");
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Configure WxStoreAuthorizerInfoService.getByBrandId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "da932772-5d3a-423b-9be9-7d49d66586b4", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "authorizerAppid", "authorizerAccessToken", 0L,
                "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias",
                "qrcodeUrl", "signature", "unBandUserGuid", "unBandUserName", "unBandUserTel",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByBrandId("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockEnterpriseClientService.list(new HashSet<>(Arrays.asList("value"))))
                .thenReturn(Collections.emptyList());

        // Run the test
        final WxStoreConsumerDTO result = wxStoreMenuDetailsServiceImplUnderTest.getConsumerInfo(wxPortalReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testEstimateItem() {
        // Setup
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        final TagRespDTO tagRespDTO = new TagRespDTO();
        tagRespDTO.setName("id");
        tagRespDTO.setId("id");
        wxStoreItemRespDTO.setTagList(Arrays.asList(tagRespDTO));
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setMinOrderNum(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreSkuRespDTO.setIsSoldOut(0);
        wxStoreSkuRespDTO.setResidueQuantity(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setIsMultiChoice(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        subItemSkuSynRespDTO.setIsSoldOut(0);
        subItemSkuSynRespDTO.setIsJoinWeChat(0);
        subItemSkuSynRespDTO.setResidueQuantity(new BigDecimal("0.00"));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        final List<WxStoreItemRespDTO> wxStoreItem = Arrays.asList(wxStoreItemRespDTO);
        final UserInfoDTO userInfoDTO = UserInfoDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .build();

        // Configure WxStoreEstimateClientService.queryEstimateForSyn(...).
        final List<ItemEstimateForAndroidRespDTO> itemEstimateForAndroidRespDTOS = Arrays.asList(
                ItemEstimateForAndroidRespDTO.builder()
                        .skuGuid("skuGuid")
                        .isSoldOut(0)
                        .residueQuantity(new BigDecimal("0.00"))
                        .build());
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockWxStoreEstimateClientService.queryEstimateForSyn(baseDTO)).thenReturn(itemEstimateForAndroidRespDTOS);

        // Run the test
        wxStoreMenuDetailsServiceImplUnderTest.estimateItem(wxStoreItem, userInfoDTO);

        // Verify the results
    }

    @Test
    public void testEstimateItem_WxStoreEstimateClientServiceReturnsNoItems() {
        // Setup
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        final TagRespDTO tagRespDTO = new TagRespDTO();
        tagRespDTO.setName("id");
        tagRespDTO.setId("id");
        wxStoreItemRespDTO.setTagList(Arrays.asList(tagRespDTO));
        wxStoreItemRespDTO.setIsFixPkg(0);
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setMinOrderNum(new BigDecimal("0.00"));
        wxStoreSkuRespDTO.setUserck(0);
        wxStoreSkuRespDTO.setIsSoldOut(0);
        wxStoreSkuRespDTO.setResidueQuantity(new BigDecimal("0.00"));
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        final SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
        subgroupSynRespDTO.setPickNum(0);
        final SubItemSkuSynRespDTO subItemSkuSynRespDTO = new SubItemSkuSynRespDTO();
        subItemSkuSynRespDTO.setSkuGuid("skuGuid");
        final AttrGroupSynRespDTO attrGroupSynRespDTO = new AttrGroupSynRespDTO();
        attrGroupSynRespDTO.setIsRequired(0);
        attrGroupSynRespDTO.setIsMultiChoice(0);
        final AttrSynRespDTO attrSynRespDTO = new AttrSynRespDTO();
        attrSynRespDTO.setIsDefault(0);
        attrGroupSynRespDTO.setAttrList(Arrays.asList(attrSynRespDTO));
        subItemSkuSynRespDTO.setAttrGroupList(Arrays.asList(attrGroupSynRespDTO));
        subItemSkuSynRespDTO.setIsSoldOut(0);
        subItemSkuSynRespDTO.setIsJoinWeChat(0);
        subItemSkuSynRespDTO.setResidueQuantity(new BigDecimal("0.00"));
        subgroupSynRespDTO.setSubItemSkuList(Arrays.asList(subItemSkuSynRespDTO));
        wxStoreItemRespDTO.setSubgroupList(Arrays.asList(subgroupSynRespDTO));
        final WxStoreAttrGroupRespDTO wxStoreAttrGroupRespDTO = new WxStoreAttrGroupRespDTO();
        wxStoreItemRespDTO.setAttrGroupList(Arrays.asList(wxStoreAttrGroupRespDTO));
        wxStoreItemRespDTO.setCurrentCount(new BigDecimal("0.00"));
        final List<WxStoreItemRespDTO> wxStoreItem = Arrays.asList(wxStoreItemRespDTO);
        final UserInfoDTO userInfoDTO = UserInfoDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .build();

        // Configure WxStoreEstimateClientService.queryEstimateForSyn(...).
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockWxStoreEstimateClientService.queryEstimateForSyn(baseDTO)).thenReturn(Collections.emptyList());

        // Run the test
        wxStoreMenuDetailsServiceImplUnderTest.estimateItem(wxStoreItem, userInfoDTO);

        // Verify the results
    }
}
