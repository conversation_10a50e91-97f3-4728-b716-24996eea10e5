package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.weixin.req.WxQueryThirdPartUserInfoReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxThirdPartUserInfoReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxThirdPartUserInfoRespDTO;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxThirdPartUserInfoServiceImplTest {

    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private DynamicHelper mockDynamicHelper;

    @InjectMocks
    private WxThirdPartUserInfoServiceImpl wxThirdPartUserInfoServiceImplUnderTest;

    @Before
    public void setUp() {
        wxThirdPartUserInfoServiceImplUnderTest.redisUtils = mockRedisUtils;
    }

    @Test
    public void testSaveOrUpdateThirdPartUserInfo() {
        // Setup
        final WxThirdPartUserInfoReqDTO wxThirdPartUserInfoReqDTO = new WxThirdPartUserInfoReqDTO();
        wxThirdPartUserInfoReqDTO.setOpenId("openId");
        wxThirdPartUserInfoReqDTO.setTel("phone");
        wxThirdPartUserInfoReqDTO.setSource(0);
        wxThirdPartUserInfoReqDTO.setNickName("nickName");

        when(mockRedisUtils.generateGuid("redisKey")).thenReturn("2db3832f-7f5a-4f2c-a63e-c446fe90ee69");

        // Run the test
        final Boolean result = wxThirdPartUserInfoServiceImplUnderTest.saveOrUpdateThirdPartUserInfo(
                wxThirdPartUserInfoReqDTO);

        // Verify the results
        assertFalse(result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testCheckThirdPartUserInfo() {
        // Setup
        final WxQueryThirdPartUserInfoReqDTO wxQueryThirdPartUserInfoReqDTO = new WxQueryThirdPartUserInfoReqDTO();
        wxQueryThirdPartUserInfoReqDTO.setOpenId("openId");
        wxQueryThirdPartUserInfoReqDTO.setTel("phone");
        wxQueryThirdPartUserInfoReqDTO.setSource(0);
        wxQueryThirdPartUserInfoReqDTO.setPassword("password");

        final WxThirdPartUserInfoRespDTO expectedResult = new WxThirdPartUserInfoRespDTO();
        expectedResult.setGuid("9b61cb1f-9773-4a6c-8d3b-fc0641a829d7");
        expectedResult.setNickName("nickName");
        expectedResult.setOpenId("openId");
        expectedResult.setPhone("phone");
        expectedResult.setSource(0);

        // Run the test
        final WxThirdPartUserInfoRespDTO result = wxThirdPartUserInfoServiceImplUnderTest.checkThirdPartUserInfo(
                wxQueryThirdPartUserInfoReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testQueryThirdPartUserInfo() {
        // Setup
        final WxQueryThirdPartUserInfoReqDTO wxQueryThirdPartUserInfoReqDTO = new WxQueryThirdPartUserInfoReqDTO();
        wxQueryThirdPartUserInfoReqDTO.setOpenId("openId");
        wxQueryThirdPartUserInfoReqDTO.setTel("phone");
        wxQueryThirdPartUserInfoReqDTO.setSource(0);
        wxQueryThirdPartUserInfoReqDTO.setPassword("password");

        final WxThirdPartUserInfoRespDTO expectedResult = new WxThirdPartUserInfoRespDTO();
        expectedResult.setGuid("9b61cb1f-9773-4a6c-8d3b-fc0641a829d7");
        expectedResult.setNickName("nickName");
        expectedResult.setOpenId("openId");
        expectedResult.setPhone("phone");
        expectedResult.setSource(0);

        // Run the test
        final WxThirdPartUserInfoRespDTO result = wxThirdPartUserInfoServiceImplUnderTest.queryThirdPartUserInfo(
                wxQueryThirdPartUserInfoReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }
}
