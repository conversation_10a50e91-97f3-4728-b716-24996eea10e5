package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.weixin.req.WxOperSubjectBrandReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxSendShortMsgReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxUnBandReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxBrandAuthRespDTO;
import com.holderzone.saas.store.weixin.service.WxStoreAuthorizerInfoService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxStoreBrandAuthController.class)
public class WxStoreBrandAuthControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStoreAuthorizerInfoService mockWxStoreAuthorizerInfoService;

    @Test
    public void testGetBrandList() throws Exception {
        // Setup
        // Configure WxStoreAuthorizerInfoService.getBrandAuthList(...).
        final List<WxBrandAuthRespDTO> wxBrandAuthRespDTOS = Arrays.asList(
                new WxBrandAuthRespDTO("brandGuid", "brandName", "nickName", "mpType", "mpAppId", Arrays.asList(0),
                        "qrcodeUrl", 0, "multiMemberGuid", "multiMemberName"));
        when(mockWxStoreAuthorizerInfoService.getBrandAuthList()).thenReturn(wxBrandAuthRespDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_brand_auth/list_brand_auth")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetBrandList_WxStoreAuthorizerInfoServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockWxStoreAuthorizerInfoService.getBrandAuthList()).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_brand_auth/list_brand_auth")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testSendShortMessage() throws Exception {
        // Setup
        when(mockWxStoreAuthorizerInfoService.sendShortMessage(
                new WxSendShortMsgReqDTO("userGuid", "userName", "tel"))).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/wx_brand_auth/send_message")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testDeleteBrandBand() throws Exception {
        // Setup
        when(mockWxStoreAuthorizerInfoService.unBandBrand(
                new WxUnBandReqDTO("brandGuid", "messageKey", "code"))).thenReturn(0);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_brand_auth/un_band_brand")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetByBrandGuid() throws Exception {
        // Setup
        // Configure WxStoreAuthorizerInfoService.getByBrandGuid(...).
        final WxBrandAuthRespDTO wxBrandAuthRespDTO = new WxBrandAuthRespDTO("brandGuid", "brandName", "nickName",
                "mpType", "mpAppId", Arrays.asList(0), "qrcodeUrl", 0, "multiMemberGuid", "multiMemberName");
        when(mockWxStoreAuthorizerInfoService.getByBrandGuid("brandGuid")).thenReturn(wxBrandAuthRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_brand_auth/get_by_brand_guid")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testBindSubject() throws Exception {
        // Setup
        // Configure WxStoreAuthorizerInfoService.bindSubject(...).
        final WxOperSubjectBrandReqDTO wxOperSubjectBrandReqDTO = new WxOperSubjectBrandReqDTO();
        wxOperSubjectBrandReqDTO.setBrandGuid("brandGuid");
        wxOperSubjectBrandReqDTO.setIsAlliance(false);
        wxOperSubjectBrandReqDTO.setOperSubjectGuid("operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.bindSubject(wxOperSubjectBrandReqDTO)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_brand_auth/bind_subject")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testBindSubject_WxStoreAuthorizerInfoServiceReturnsTrue() throws Exception {
        // Setup
        // Configure WxStoreAuthorizerInfoService.bindSubject(...).
        final WxOperSubjectBrandReqDTO wxOperSubjectBrandReqDTO = new WxOperSubjectBrandReqDTO();
        wxOperSubjectBrandReqDTO.setBrandGuid("brandGuid");
        wxOperSubjectBrandReqDTO.setIsAlliance(false);
        wxOperSubjectBrandReqDTO.setOperSubjectGuid("operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.bindSubject(wxOperSubjectBrandReqDTO)).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_brand_auth/bind_subject")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
