package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.saas.store.dto.organization.StoreParserBaseDTO;
import com.holderzone.saas.store.dto.organization.StoreParserBasePageDTO;
import com.holderzone.saas.store.dto.organization.StoreParserDTO;
import com.holderzone.saas.store.dto.organization.StoreParserPageDTO;
import com.holderzone.saas.store.weixin.service.rpc.OrganizationClientService;
import com.holderzone.saas.store.weixin.service.rpc.user.UserFeignService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class StoreParserServiceImplTest {

    @Mock
    private OrganizationClientService mockOrganizationService;
    @Mock
    private UserFeignService mockUserFeignService;

    private StoreParserServiceImpl storeParserServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        storeParserServiceImplUnderTest = new StoreParserServiceImpl(mockOrganizationService, mockUserFeignService);
    }

    @Test
    public void testParseByCondition1() {
        // Setup
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));

        when(mockUserFeignService.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));

        // Configure OrganizationClientService.parseByCondition(...).
        final StoreParserDTO storeParserDTO1 = new StoreParserDTO();
        storeParserDTO1.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO1.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO1.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO1.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO1.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrganizationService.parseByCondition(storeParserDTO1)).thenReturn(Arrays.asList("value"));

        // Run the test
        storeParserServiceImplUnderTest.parseByCondition(storeParserDTO);

        // Verify the results
    }

    @Test
    public void testParseByCondition1_UserFeignServiceReturnsNoItems() {
        // Setup
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));

        when(mockUserFeignService.queryAllStoreGuid()).thenReturn(Collections.emptyList());

        // Run the test
        storeParserServiceImplUnderTest.parseByCondition(storeParserDTO);

        // Verify the results
    }

    @Test
    public void testParseByCondition1_OrganizationClientServiceReturnsNoItems() {
        // Setup
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));

        when(mockUserFeignService.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));

        // Configure OrganizationClientService.parseByCondition(...).
        final StoreParserDTO storeParserDTO1 = new StoreParserDTO();
        storeParserDTO1.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO1.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO1.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO1.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO1.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrganizationService.parseByCondition(storeParserDTO1)).thenReturn(Collections.emptyList());

        // Run the test
        storeParserServiceImplUnderTest.parseByCondition(storeParserDTO);

        // Verify the results
    }

    @Test
    public void testParseByCondition2() {
        // Setup
        final StoreParserBaseDTO storeParserBaseDTO = new StoreParserBaseDTO();
        storeParserBaseDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserBaseDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserBaseDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserBaseDTO.setOrganizationGuidList(Arrays.asList("value"));

        when(mockUserFeignService.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));

        // Configure OrganizationClientService.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrganizationService.parseByCondition(storeParserDTO)).thenReturn(Arrays.asList("value"));

        // Run the test
        storeParserServiceImplUnderTest.parseByCondition(storeParserBaseDTO);

        // Verify the results
    }

    @Test
    public void testParseByCondition2_UserFeignServiceReturnsNoItems() {
        // Setup
        final StoreParserBaseDTO storeParserBaseDTO = new StoreParserBaseDTO();
        storeParserBaseDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserBaseDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserBaseDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserBaseDTO.setOrganizationGuidList(Arrays.asList("value"));

        when(mockUserFeignService.queryAllStoreGuid()).thenReturn(Collections.emptyList());

        // Run the test
        storeParserServiceImplUnderTest.parseByCondition(storeParserBaseDTO);

        // Verify the results
    }

    @Test
    public void testParseByCondition2_OrganizationClientServiceReturnsNoItems() {
        // Setup
        final StoreParserBaseDTO storeParserBaseDTO = new StoreParserBaseDTO();
        storeParserBaseDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserBaseDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserBaseDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserBaseDTO.setOrganizationGuidList(Arrays.asList("value"));

        when(mockUserFeignService.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));

        // Configure OrganizationClientService.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrganizationService.parseByCondition(storeParserDTO)).thenReturn(Collections.emptyList());

        // Run the test
        storeParserServiceImplUnderTest.parseByCondition(storeParserBaseDTO);

        // Verify the results
    }

    @Test
    public void testParseByCondition3() {
        // Setup
        final StoreParserPageDTO storeParserPageDTO = new StoreParserPageDTO();
        storeParserPageDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserPageDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserPageDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserPageDTO.setOrganizationGuidList(Arrays.asList("value"));
        storeParserPageDTO.setRuleType(0);

        when(mockUserFeignService.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));

        // Configure OrganizationClientService.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrganizationService.parseByCondition(storeParserDTO)).thenReturn(Arrays.asList("value"));

        // Run the test
        storeParserServiceImplUnderTest.parseByCondition(storeParserPageDTO);

        // Verify the results
    }

    @Test
    public void testParseByCondition3_UserFeignServiceReturnsNoItems() {
        // Setup
        final StoreParserPageDTO storeParserPageDTO = new StoreParserPageDTO();
        storeParserPageDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserPageDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserPageDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserPageDTO.setOrganizationGuidList(Arrays.asList("value"));
        storeParserPageDTO.setRuleType(0);

        when(mockUserFeignService.queryAllStoreGuid()).thenReturn(Collections.emptyList());

        // Run the test
        storeParserServiceImplUnderTest.parseByCondition(storeParserPageDTO);

        // Verify the results
    }

    @Test
    public void testParseByCondition3_OrganizationClientServiceReturnsNoItems() {
        // Setup
        final StoreParserPageDTO storeParserPageDTO = new StoreParserPageDTO();
        storeParserPageDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserPageDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserPageDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserPageDTO.setOrganizationGuidList(Arrays.asList("value"));
        storeParserPageDTO.setRuleType(0);

        when(mockUserFeignService.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));

        // Configure OrganizationClientService.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrganizationService.parseByCondition(storeParserDTO)).thenReturn(Collections.emptyList());

        // Run the test
        storeParserServiceImplUnderTest.parseByCondition(storeParserPageDTO);

        // Verify the results
    }

    @Test
    public void testParseByCondition4() {
        // Setup
        final StoreParserBasePageDTO storeParserBasePageDTO = new StoreParserBasePageDTO();
        storeParserBasePageDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserBasePageDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserBasePageDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserBasePageDTO.setOrganizationGuidList(Arrays.asList("value"));

        when(mockUserFeignService.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));

        // Configure OrganizationClientService.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrganizationService.parseByCondition(storeParserDTO)).thenReturn(Arrays.asList("value"));

        // Run the test
        storeParserServiceImplUnderTest.parseByCondition(storeParserBasePageDTO);

        // Verify the results
    }

    @Test
    public void testParseByCondition4_UserFeignServiceReturnsNoItems() {
        // Setup
        final StoreParserBasePageDTO storeParserBasePageDTO = new StoreParserBasePageDTO();
        storeParserBasePageDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserBasePageDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserBasePageDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserBasePageDTO.setOrganizationGuidList(Arrays.asList("value"));

        when(mockUserFeignService.queryAllStoreGuid()).thenReturn(Collections.emptyList());

        // Run the test
        storeParserServiceImplUnderTest.parseByCondition(storeParserBasePageDTO);

        // Verify the results
    }

    @Test
    public void testParseByCondition4_OrganizationClientServiceReturnsNoItems() {
        // Setup
        final StoreParserBasePageDTO storeParserBasePageDTO = new StoreParserBasePageDTO();
        storeParserBasePageDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserBasePageDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserBasePageDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserBasePageDTO.setOrganizationGuidList(Arrays.asList("value"));

        when(mockUserFeignService.queryAllStoreGuid()).thenReturn(Arrays.asList("value"));

        // Configure OrganizationClientService.parseByCondition(...).
        final StoreParserDTO storeParserDTO = new StoreParserDTO();
        storeParserDTO.setAllStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setStoreGuidList(Arrays.asList("value"));
        storeParserDTO.setRegionCodeList(Arrays.asList("value"));
        storeParserDTO.setBrandGuidList(Arrays.asList("value"));
        storeParserDTO.setOrganizationGuidList(Arrays.asList("value"));
        when(mockOrganizationService.parseByCondition(storeParserDTO)).thenReturn(Collections.emptyList());

        // Run the test
        storeParserServiceImplUnderTest.parseByCondition(storeParserBasePageDTO);

        // Verify the results
    }
}
