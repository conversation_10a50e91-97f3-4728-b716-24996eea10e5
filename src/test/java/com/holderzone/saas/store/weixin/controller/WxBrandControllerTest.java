package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxBrandController.class)
public class WxBrandControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStoreSessionDetailsService mockWxStoreSessionDetailsService;
    @MockBean
    private DynamicHelper mockDynamicHelper;

    @Test
    public void testGetBrandDetail() throws Exception {
        // Setup
        // Configure WxStoreSessionDetailsService.getBrandDetail(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("5b5b1347-40ec-4aa4-8bcf-e31e92bd7f89");
        brandDTO.setUuid("2b61e83a-db23-45de-8973-7fb9f298b61c");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockWxStoreSessionDetailsService.getBrandDetail("brandGuid")).thenReturn(brandDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_brand/get_by_brand_guid")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }
}
