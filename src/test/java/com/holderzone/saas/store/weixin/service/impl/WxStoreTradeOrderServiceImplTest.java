package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberCard;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberInfoDTO;
import com.holderzone.holder.saas.member.wechat.dto.card.RequestCardRightDetails;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseCardRight;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseCardRightDetail;
import com.holderzone.holder.saas.member.wechat.dto.coupon.ResponseVolumeList;
import com.holderzone.holder.saas.member.wechat.dto.member.MemberInfoVolume;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestMemberInfoVolumeQuery;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolume;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolumeDetails;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.WxStoreMerchantDineInItemDTO;
import com.holderzone.saas.store.dto.business.manage.SurchargeConditionQuery;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.FreeItemDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateFastFoodReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.OrderFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnItemDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.weixin.*;
import com.holderzone.saas.store.dto.weixin.member.*;
import com.holderzone.saas.store.dto.weixin.req.WxPaidOrderDetailsReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPrepayReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreItemRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreSkuRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreTradeDetailsGroupDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreTradeOrderDetailsRespDTO;
import com.holderzone.saas.store.weixin.config.ResponseModel;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.entity.query.WxStorePendingOrdersQuery;
import com.holderzone.saas.store.weixin.execption.CorrectResult;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreCreateDineInOrderReqDTOMapStruct;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreCreateFastFoodReqDTOMapstruct;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.rpc.*;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStoreTradeOrderServiceImplTest {

    @Mock
    private WxStoreDineInOrderClientService mockWxStoreDineInOrderClientService;
    @Mock
    private WxStoreTableClientService mockWxStoreTableClientService;
    @Mock
    private WxStoreCreateDineInOrderReqDTOMapStruct mockWxStoreCreateDineInOrderReqDTOMapStruct;
    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private WxStoreAdvanceOrderService mockWxStoreAdvanceOrderService;
    @Mock
    private WxStoreDineInBillClientService mockWxStoreDineInBillClientService;
    @Mock
    private WxStoreCreateFastFoodReqDTOMapstruct mockWxStoreCreateFastFoodReqDTOMapstruct;
    @Mock
    private WxStoreMenuDetailsService mockWxStoreMenuDetailsService;
    @Mock
    private WxStoreMerchantDineInItemService mockWxStoreMerchantDineInItemService;
    @Mock
    private WxStoreMerchantOrderService mockWxStoreMerchantOrderService;
    @Mock
    private WxStoreSessionDetailsService mockWxStoreSessionDetailsService;
    @Mock
    private WxSocketMsgService mockWxSocketMsgService;
    @Mock
    private WxOrderRecordService mockWxOrderRecordService;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private WxStoreEstimateClientService mockWxStoreEstimateClientService;
    @Mock
    private WxStorePersonOrderDetailsService mockWxStorePersonOrderDetailsService;
    @Mock
    private WxStoreOrderPayService mockWxStoreOrderPayService;
    @Mock
    private OrganizationClientService mockOrganizationClientService;
    @Mock
    private MemberClientService mockMemberClientService;
    @Mock
    private WxUserRecordService mockWxUserRecordService;
    @Mock
    private HsaBaseClientService mockHsaBaseClientService;
    @Mock
    private BusinessClientService mockBusinessClientService;
    @Mock
    private WxStoreWeChatOrderClientService mockWxStoreWeChatOrderClientService;

    @InjectMocks
    private WxStoreTradeOrderServiceImpl wxStoreTradeOrderServiceImplUnderTest;

    @Test
    public void testGetTableOrderDetails() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build();

        // Configure WxStoreOrderPayService.orderPay(...).
        final WxStoreTradeOrderDetailsRespDTO wxStoreTradeOrderDetailsRespDTO = WxStoreTradeOrderDetailsRespDTO.builder()
                .combine(0)
                .wxStoreTradeDetailsGroupDTOS(Arrays.asList(WxStoreTradeDetailsGroupDTO.builder()
                        .tableGuid("tableGuid")
                        .tableCode("tableCode")
                        .areaName("areaName")
                        .mainTable(0)
                        .wxStoreTradeOrderDetailsDTOS(Arrays.asList(new WxStoreTradeOrderDetailsDTO()))
                        .build()))
                .orderNo("orderNo")
                .tableCode("code")
                .tradeMode(0)
                .totalPrice(new BigDecimal("0.00"))
                .orderFeeDetailDTOS(Arrays.asList(new OrderFeeDetailDTO()))
                .actuallyPayFeeDetailDTOS(Arrays.asList(new ActuallyPayFeeDetailDTO()))
                .discountFeeDetailDTOS(Arrays.asList(new DiscountFeeDetailDTO()))
                .gmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .checkoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .guestCount(0)
                .orderState(0)
                .areaName("areaName")
                .storeName("storeName")
                .brandName("brandName")
                .payWay("payWay")
                .errorMsg("errorMsg")
                .build();
        when(mockWxStoreOrderPayService.orderPay(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(wxStoreTradeOrderDetailsRespDTO);

        // Configure WxStorePersonOrderDetailsService.getPersonOrderDetails(...).
        final WxStoreTradeOrderDetailsRespDTO wxStoreTradeOrderDetailsRespDTO1 = WxStoreTradeOrderDetailsRespDTO.builder()
                .combine(0)
                .wxStoreTradeDetailsGroupDTOS(Arrays.asList(WxStoreTradeDetailsGroupDTO.builder()
                        .tableGuid("tableGuid")
                        .tableCode("tableCode")
                        .areaName("areaName")
                        .mainTable(0)
                        .wxStoreTradeOrderDetailsDTOS(Arrays.asList(new WxStoreTradeOrderDetailsDTO()))
                        .build()))
                .orderNo("orderNo")
                .tableCode("code")
                .tradeMode(0)
                .totalPrice(new BigDecimal("0.00"))
                .orderFeeDetailDTOS(Arrays.asList(new OrderFeeDetailDTO()))
                .actuallyPayFeeDetailDTOS(Arrays.asList(new ActuallyPayFeeDetailDTO()))
                .discountFeeDetailDTOS(Arrays.asList(new DiscountFeeDetailDTO()))
                .gmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .checkoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .guestCount(0)
                .orderState(0)
                .areaName("areaName")
                .storeName("storeName")
                .brandName("brandName")
                .payWay("payWay")
                .errorMsg("errorMsg")
                .build();
        when(mockWxStorePersonOrderDetailsService.getPersonOrderDetails(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(wxStoreTradeOrderDetailsRespDTO1);

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(false);
        when(mockWxStoreSessionDetailsService.getOrderState("tableGuid")).thenReturn(0);
        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("tableGuid")).thenReturn("result");
        when(mockRedisUtils.generatdDTOGuid(WxStoreMerchantOperationDTO.class)).thenReturn("merchatBatchGuid");
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn("result");
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(false);
        when(mockWxStoreSessionDetailsService.getOrderGuid("tableGuid")).thenReturn("data");
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreMerchantOrderService.getPendingOrders(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setNickName("nickName");
        wxStoreMerchantOrderDTO.setDiningTableGuid("tableGuid");
        wxStoreMerchantOrderDTO.setTableCode("code");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setItemCount(0);
        wxStoreMerchantOrderDTO.setActualGuestsNo(0);
        wxStoreMerchantOrderDTO.setTradeGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        wxStoreMerchantOrderDTO.setRemark("remark");
        wxStoreMerchantOrderDTO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDTO.setAreaName("areaName");
        wxStoreMerchantOrderDTO.setOperationName("nickName");
        wxStoreMerchantOrderDTO.setHeadImgUrl("headImgUrl");
        wxStoreMerchantOrderDTO.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDTO.setCombine("data");
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS = Arrays.asList(wxStoreMerchantOrderDTO);
        when(mockWxStoreMerchantOrderService.getPendingOrders(WxStorePendingOrdersQuery.builder()
                .orderStates(Arrays.asList(0))
                .orderGuid("orderGuid")
                .combine("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .build())).thenReturn(wxStoreMerchantOrderDTOS);

        // Configure WxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem(...).
        final WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItemDTO = new WxStoreMerchantDineInItemDTO();
        wxStoreMerchantDineInItemDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        wxStoreMerchantDineInItemDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantDineInItemDTO.setItemCount(0);
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        wxStoreMerchantDineInItemDTO.setOrderFeeDetailDTOS(Arrays.asList(orderFeeDetailDTO1));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        wxStoreMerchantDineInItemDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("volumeName");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        wxStoreMerchantDineInItemDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        wxStoreMerchantDineInItemDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        when(mockWxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem("batchId"))
                .thenReturn(wxStoreMerchantDineInItemDTO);

        // Configure WxStoreMerchantOrderService.getOneByGuid(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("guid");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setOrderGuid("batchId");
        wxStoreMerchantOrderDO.setHeadImgUrl("headImgUrl");
        when(mockWxStoreMerchantOrderService.getOneByGuid("orderGuid")).thenReturn(wxStoreMerchantOrderDO);

        when(mockWxStoreSessionDetailsService.getDinnerGuestsCount("tableGuid")).thenReturn(0);

        // Configure WxStoreSessionDetailsService.getTablePaidUser(...).
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .sex(0)
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .diningTableGuid("tableGuid")
                .tableCode("code")
                .diningTableName("code")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .brandName("brandName")
                .brandGuid("brandGuid")
                .build();
        when(mockWxStoreSessionDetailsService.getTablePaidUser("tableGuid")).thenReturn(wxStoreConsumerDTO);

        // Run the test
        final WebSocketMessageDTO result = wxStoreTradeOrderServiceImplUnderTest.getTableOrderDetails(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
        verify(mockWxStoreSessionDetailsService).updateMerchantBatchGuid("tableGuid", "merchatBatchGuid");
        verify(mockWxStoreSessionDetailsService).saveOrderGuid("tableGuid", "data");
        verify(mockWxStoreSessionDetailsService).saveOrderState("tableGuid", 0);
    }

    @Test
    public void testGetTableOrderDetails_WxStoreMenuDetailsServiceJudgeOrderType1ReturnsTrue() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(true);
        when(mockWxStoreSessionDetailsService.getOrderState("tableGuid")).thenReturn(0);
        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("tableGuid")).thenReturn("result");
        when(mockRedisUtils.generatdDTOGuid(WxStoreMerchantOperationDTO.class)).thenReturn("merchatBatchGuid");
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn("result");
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(false);
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreMerchantOrderService.getPendingOrders(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setNickName("nickName");
        wxStoreMerchantOrderDTO.setDiningTableGuid("tableGuid");
        wxStoreMerchantOrderDTO.setTableCode("code");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setItemCount(0);
        wxStoreMerchantOrderDTO.setActualGuestsNo(0);
        wxStoreMerchantOrderDTO.setTradeGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        wxStoreMerchantOrderDTO.setRemark("remark");
        wxStoreMerchantOrderDTO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDTO.setAreaName("areaName");
        wxStoreMerchantOrderDTO.setOperationName("nickName");
        wxStoreMerchantOrderDTO.setHeadImgUrl("headImgUrl");
        wxStoreMerchantOrderDTO.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDTO.setCombine("data");
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS = Arrays.asList(wxStoreMerchantOrderDTO);
        when(mockWxStoreMerchantOrderService.getPendingOrders(WxStorePendingOrdersQuery.builder()
                .orderStates(Arrays.asList(0))
                .orderGuid("orderGuid")
                .combine("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .build())).thenReturn(wxStoreMerchantOrderDTOS);

        // Configure WxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem(...).
        final WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItemDTO = new WxStoreMerchantDineInItemDTO();
        wxStoreMerchantDineInItemDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        wxStoreMerchantDineInItemDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantDineInItemDTO.setItemCount(0);
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        wxStoreMerchantDineInItemDTO.setOrderFeeDetailDTOS(Arrays.asList(orderFeeDetailDTO1));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        wxStoreMerchantDineInItemDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("volumeName");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        wxStoreMerchantDineInItemDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        wxStoreMerchantDineInItemDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        when(mockWxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem("batchId"))
                .thenReturn(wxStoreMerchantDineInItemDTO);

        // Configure WxStoreMerchantOrderService.getOneByGuid(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("guid");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setOrderGuid("batchId");
        wxStoreMerchantOrderDO.setHeadImgUrl("headImgUrl");
        when(mockWxStoreMerchantOrderService.getOneByGuid("orderGuid")).thenReturn(wxStoreMerchantOrderDO);

        // Run the test
        final WebSocketMessageDTO result = wxStoreTradeOrderServiceImplUnderTest.getTableOrderDetails(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
        verify(mockWxStoreSessionDetailsService).updateMerchantBatchGuid("tableGuid", "merchatBatchGuid");
        verify(mockWxStoreSessionDetailsService).saveOrderGuid("tableGuid", "data");
        verify(mockWxStoreSessionDetailsService).saveOrderState("tableGuid", 0);
    }

    @Test
    public void testGetTableOrderDetails_WxStoreTableClientServiceReturnsNull() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(true);
        when(mockWxStoreSessionDetailsService.getOrderState("tableGuid")).thenReturn(0);
        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("tableGuid")).thenReturn("result");
        when(mockRedisUtils.generatdDTOGuid(WxStoreMerchantOperationDTO.class)).thenReturn("merchatBatchGuid");
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn(null);

        // Configure WxStorePersonOrderDetailsService.getPersonOrderDetails(...).
        final WxStoreTradeOrderDetailsRespDTO wxStoreTradeOrderDetailsRespDTO = WxStoreTradeOrderDetailsRespDTO.builder()
                .combine(0)
                .wxStoreTradeDetailsGroupDTOS(Arrays.asList(WxStoreTradeDetailsGroupDTO.builder()
                        .tableGuid("tableGuid")
                        .tableCode("tableCode")
                        .areaName("areaName")
                        .mainTable(0)
                        .wxStoreTradeOrderDetailsDTOS(Arrays.asList(new WxStoreTradeOrderDetailsDTO()))
                        .build()))
                .orderNo("orderNo")
                .tableCode("code")
                .tradeMode(0)
                .totalPrice(new BigDecimal("0.00"))
                .orderFeeDetailDTOS(Arrays.asList(new OrderFeeDetailDTO()))
                .actuallyPayFeeDetailDTOS(Arrays.asList(new ActuallyPayFeeDetailDTO()))
                .discountFeeDetailDTOS(Arrays.asList(new DiscountFeeDetailDTO()))
                .gmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .checkoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .guestCount(0)
                .orderState(0)
                .areaName("areaName")
                .storeName("storeName")
                .brandName("brandName")
                .payWay("payWay")
                .errorMsg("errorMsg")
                .build();
        when(mockWxStorePersonOrderDetailsService.getPersonOrderDetails(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(wxStoreTradeOrderDetailsRespDTO);

        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(false);
        when(mockWxStoreSessionDetailsService.getOrderGuid("tableGuid")).thenReturn("data");
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreMerchantOrderService.getPendingOrders(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setNickName("nickName");
        wxStoreMerchantOrderDTO.setDiningTableGuid("tableGuid");
        wxStoreMerchantOrderDTO.setTableCode("code");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setItemCount(0);
        wxStoreMerchantOrderDTO.setActualGuestsNo(0);
        wxStoreMerchantOrderDTO.setTradeGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        wxStoreMerchantOrderDTO.setRemark("remark");
        wxStoreMerchantOrderDTO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDTO.setAreaName("areaName");
        wxStoreMerchantOrderDTO.setOperationName("nickName");
        wxStoreMerchantOrderDTO.setHeadImgUrl("headImgUrl");
        wxStoreMerchantOrderDTO.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDTO.setCombine("data");
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS = Arrays.asList(wxStoreMerchantOrderDTO);
        when(mockWxStoreMerchantOrderService.getPendingOrders(WxStorePendingOrdersQuery.builder()
                .orderStates(Arrays.asList(0))
                .orderGuid("orderGuid")
                .combine("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .build())).thenReturn(wxStoreMerchantOrderDTOS);

        // Configure WxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem(...).
        final WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItemDTO = new WxStoreMerchantDineInItemDTO();
        wxStoreMerchantDineInItemDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        wxStoreMerchantDineInItemDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantDineInItemDTO.setItemCount(0);
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        wxStoreMerchantDineInItemDTO.setOrderFeeDetailDTOS(Arrays.asList(orderFeeDetailDTO1));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        wxStoreMerchantDineInItemDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("volumeName");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        wxStoreMerchantDineInItemDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        wxStoreMerchantDineInItemDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        when(mockWxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem("batchId"))
                .thenReturn(wxStoreMerchantDineInItemDTO);

        when(mockWxStoreSessionDetailsService.getDinnerGuestsCount("tableGuid")).thenReturn(0);

        // Configure WxStoreSessionDetailsService.getTablePaidUser(...).
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .sex(0)
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .diningTableGuid("tableGuid")
                .tableCode("code")
                .diningTableName("code")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .brandName("brandName")
                .brandGuid("brandGuid")
                .build();
        when(mockWxStoreSessionDetailsService.getTablePaidUser("tableGuid")).thenReturn(wxStoreConsumerDTO);

        // Run the test
        final WebSocketMessageDTO result = wxStoreTradeOrderServiceImplUnderTest.getTableOrderDetails(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
        verify(mockWxStoreSessionDetailsService).updateMerchantBatchGuid("tableGuid", "merchatBatchGuid");
        verify(mockWxStoreSessionDetailsService).saveOrderGuid("tableGuid", "data");
    }

    @Test
    public void testGetTableOrderDetails_WxStoreMenuDetailsServiceJudgeOrderType2ReturnsTrue() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(true);
        when(mockWxStoreSessionDetailsService.getOrderState("tableGuid")).thenReturn(0);
        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("tableGuid")).thenReturn("result");
        when(mockRedisUtils.generatdDTOGuid(WxStoreMerchantOperationDTO.class)).thenReturn("merchatBatchGuid");
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn("result");
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(true);
        when(mockWxStoreSessionDetailsService.getOrderGuid("tableGuid")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreMerchantOrderService.getPendingOrders(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setNickName("nickName");
        wxStoreMerchantOrderDTO.setDiningTableGuid("tableGuid");
        wxStoreMerchantOrderDTO.setTableCode("code");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setItemCount(0);
        wxStoreMerchantOrderDTO.setActualGuestsNo(0);
        wxStoreMerchantOrderDTO.setTradeGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        wxStoreMerchantOrderDTO.setRemark("remark");
        wxStoreMerchantOrderDTO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDTO.setAreaName("areaName");
        wxStoreMerchantOrderDTO.setOperationName("nickName");
        wxStoreMerchantOrderDTO.setHeadImgUrl("headImgUrl");
        wxStoreMerchantOrderDTO.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDTO.setCombine("data");
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS = Arrays.asList(wxStoreMerchantOrderDTO);
        when(mockWxStoreMerchantOrderService.getPendingOrders(WxStorePendingOrdersQuery.builder()
                .orderStates(Arrays.asList(0))
                .orderGuid("orderGuid")
                .combine("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .build())).thenReturn(wxStoreMerchantOrderDTOS);

        // Configure WxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem(...).
        final WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItemDTO = new WxStoreMerchantDineInItemDTO();
        wxStoreMerchantDineInItemDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        wxStoreMerchantDineInItemDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantDineInItemDTO.setItemCount(0);
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        wxStoreMerchantDineInItemDTO.setOrderFeeDetailDTOS(Arrays.asList(orderFeeDetailDTO1));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        wxStoreMerchantDineInItemDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("volumeName");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        wxStoreMerchantDineInItemDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        wxStoreMerchantDineInItemDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        when(mockWxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem("batchId"))
                .thenReturn(wxStoreMerchantDineInItemDTO);

        // Configure WxStoreMerchantOrderService.getOneByGuid(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("guid");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setOrderGuid("batchId");
        wxStoreMerchantOrderDO.setHeadImgUrl("headImgUrl");
        when(mockWxStoreMerchantOrderService.getOneByGuid("orderGuid")).thenReturn(wxStoreMerchantOrderDO);

        // Run the test
        final WebSocketMessageDTO result = wxStoreTradeOrderServiceImplUnderTest.getTableOrderDetails(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
        verify(mockWxStoreSessionDetailsService).updateMerchantBatchGuid("tableGuid", "merchatBatchGuid");
        verify(mockWxStoreSessionDetailsService).saveOrderGuid("tableGuid", "data");
        verify(mockWxStoreSessionDetailsService).saveOrderState("tableGuid", 0);
    }

    @Test
    public void testGetTableOrderDetails_WxStoreMerchantOrderServiceGetPendingOrdersReturnsNull() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(true);
        when(mockWxStoreSessionDetailsService.getOrderState("tableGuid")).thenReturn(0);
        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("tableGuid")).thenReturn("result");
        when(mockRedisUtils.generatdDTOGuid(WxStoreMerchantOperationDTO.class)).thenReturn("merchatBatchGuid");
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn("result");
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(false);
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        when(mockWxStoreMerchantOrderService.getPendingOrders(WxStorePendingOrdersQuery.builder()
                .orderStates(Arrays.asList(0))
                .orderGuid("orderGuid")
                .combine("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .build())).thenReturn(null);

        // Configure WxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem(...).
        final WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItemDTO = new WxStoreMerchantDineInItemDTO();
        wxStoreMerchantDineInItemDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        wxStoreMerchantDineInItemDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantDineInItemDTO.setItemCount(0);
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        wxStoreMerchantDineInItemDTO.setOrderFeeDetailDTOS(Arrays.asList(orderFeeDetailDTO1));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        wxStoreMerchantDineInItemDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("volumeName");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        wxStoreMerchantDineInItemDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        wxStoreMerchantDineInItemDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        when(mockWxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem("batchId"))
                .thenReturn(wxStoreMerchantDineInItemDTO);

        // Configure WxStoreMerchantOrderService.getOneByGuid(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("guid");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setOrderGuid("batchId");
        wxStoreMerchantOrderDO.setHeadImgUrl("headImgUrl");
        when(mockWxStoreMerchantOrderService.getOneByGuid("orderGuid")).thenReturn(wxStoreMerchantOrderDO);

        // Run the test
        final WebSocketMessageDTO result = wxStoreTradeOrderServiceImplUnderTest.getTableOrderDetails(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
        verify(mockWxStoreSessionDetailsService).updateMerchantBatchGuid("tableGuid", "merchatBatchGuid");
        verify(mockWxStoreSessionDetailsService).saveOrderGuid("tableGuid", "data");
        verify(mockWxStoreSessionDetailsService).saveOrderState("tableGuid", 0);
    }

    @Test
    public void testGetTableOrderDetails_WxStoreMerchantOrderServiceGetPendingOrdersReturnsNoItems() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(true);
        when(mockWxStoreSessionDetailsService.getOrderState("tableGuid")).thenReturn(0);
        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("tableGuid")).thenReturn("result");
        when(mockRedisUtils.generatdDTOGuid(WxStoreMerchantOperationDTO.class)).thenReturn("merchatBatchGuid");
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn("result");
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(false);
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        when(mockWxStoreMerchantOrderService.getPendingOrders(WxStorePendingOrdersQuery.builder()
                .orderStates(Arrays.asList(0))
                .orderGuid("orderGuid")
                .combine("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .build())).thenReturn(Collections.emptyList());

        // Configure WxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem(...).
        final WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItemDTO = new WxStoreMerchantDineInItemDTO();
        wxStoreMerchantDineInItemDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        wxStoreMerchantDineInItemDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantDineInItemDTO.setItemCount(0);
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        wxStoreMerchantDineInItemDTO.setOrderFeeDetailDTOS(Arrays.asList(orderFeeDetailDTO1));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        wxStoreMerchantDineInItemDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("volumeName");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        wxStoreMerchantDineInItemDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        wxStoreMerchantDineInItemDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        when(mockWxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem("batchId"))
                .thenReturn(wxStoreMerchantDineInItemDTO);

        // Configure WxStoreMerchantOrderService.getOneByGuid(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("guid");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setOrderGuid("batchId");
        wxStoreMerchantOrderDO.setHeadImgUrl("headImgUrl");
        when(mockWxStoreMerchantOrderService.getOneByGuid("orderGuid")).thenReturn(wxStoreMerchantOrderDO);

        // Run the test
        final WebSocketMessageDTO result = wxStoreTradeOrderServiceImplUnderTest.getTableOrderDetails(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
        verify(mockWxStoreSessionDetailsService).updateMerchantBatchGuid("tableGuid", "merchatBatchGuid");
        verify(mockWxStoreSessionDetailsService).saveOrderGuid("tableGuid", "data");
        verify(mockWxStoreSessionDetailsService).saveOrderState("tableGuid", 0);
    }

    @Test
    public void testReleaseTableLock() {
        wxStoreTradeOrderServiceImplUnderTest.releaseTableLock(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build());
    }

    @Test
    public void testCalculate() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build();
        final DineinOrderDetailRespDTO expectedResult = new DineinOrderDetailRespDTO();
        expectedResult.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        expectedResult.setUpperState(0);
        expectedResult.setMainOrderNo("mainOrderNo");
        expectedResult.setMainOrderGuid("data");
        expectedResult.setTradeMode(0);
        expectedResult.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        expectedResult.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        expectedResult.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        expectedResult.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        expectedResult.setOrderNo("orderNo");
        expectedResult.setState(0);
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRemark("remark");
        expectedResult.setDiningTableGuid("tableGuid");
        expectedResult.setDiningTableName("tableCode");
        expectedResult.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        expectedResult.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        expectedResult.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        expectedResult.setTip("tip");

        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(false);
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn("data");
        when(mockWxStoreSessionDetailsService.getOrderGuid("tableGuid")).thenReturn("data");
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO1);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("volumeName");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreDineInBillClientService.calculate(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO1.setUpperState(0);
        dineinOrderDetailRespDTO1.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO1.setMainOrderGuid("data");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO2 = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO1.setOrderFeeDetailDTO(orderFeeDetailDTO2);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO2 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO2.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO1.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO2));
        final DiscountFeeDetailDTO discountFeeDetailDTO2 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO2.setDiscountName("volumeName");
        discountFeeDetailDTO2.setDiscountType(0);
        discountFeeDetailDTO2.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO2));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        dineinOrderDetailRespDTO1.setState(0);
        dineinOrderDetailRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setRemark("remark");
        dineinOrderDetailRespDTO1.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO1.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO1.setGuestCount(0);
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setUserWxPublicOpenId("openId");
        dineInItemDTO2.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO2.setItemType(0);
        dineInItemDTO2.setItemState(0);
        dineInItemDTO2.setSkuGuid("skuGuid");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO2.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO2.setIsPay(0);
        dineInItemDTO2.setIsMemberDiscount(0);
        dineInItemDTO2.setIsWholeDiscount(0);
        dineInItemDTO2.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO2 = new FreeItemDTO();
        dineInItemDTO2.setFreeItemDTOS(Arrays.asList(freeItemDTO2));
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        dineinOrderDetailRespDTO1.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO1.setTip("tip");
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setDeviceId("openId");
        billCalculateReqDTO.setEnterpriseGuid("enterpriseGuid");
        billCalculateReqDTO.setEnterpriseName("enterpriseName");
        billCalculateReqDTO.setStoreGuid("storeGuid");
        billCalculateReqDTO.setOrderGuid("data");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setMemberPhone("openId");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVerify(0);
        when(mockWxStoreDineInBillClientService.calculate(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO1);

        // Run the test
        final DineinOrderDetailRespDTO result = wxStoreTradeOrderServiceImplUnderTest.calculate(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).saveOrderGuid("tableGuid", "data");
    }

    @Test
    public void testCalculate_WxStoreMenuDetailsServiceReturnsTrue() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build();
        final DineinOrderDetailRespDTO expectedResult = new DineinOrderDetailRespDTO();
        expectedResult.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        expectedResult.setUpperState(0);
        expectedResult.setMainOrderNo("mainOrderNo");
        expectedResult.setMainOrderGuid("data");
        expectedResult.setTradeMode(0);
        expectedResult.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        expectedResult.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        expectedResult.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        expectedResult.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        expectedResult.setOrderNo("orderNo");
        expectedResult.setState(0);
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRemark("remark");
        expectedResult.setDiningTableGuid("tableGuid");
        expectedResult.setDiningTableName("tableCode");
        expectedResult.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        expectedResult.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        expectedResult.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        expectedResult.setTip("tip");

        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(true);
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn("data");
        when(mockWxStoreSessionDetailsService.getOrderGuid("tableGuid")).thenReturn("data");
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO1);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("volumeName");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreDineInBillClientService.calculate(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO1.setUpperState(0);
        dineinOrderDetailRespDTO1.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO1.setMainOrderGuid("data");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO2 = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO1.setOrderFeeDetailDTO(orderFeeDetailDTO2);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO2 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO2.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO1.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO2));
        final DiscountFeeDetailDTO discountFeeDetailDTO2 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO2.setDiscountName("volumeName");
        discountFeeDetailDTO2.setDiscountType(0);
        discountFeeDetailDTO2.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO2));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        dineinOrderDetailRespDTO1.setState(0);
        dineinOrderDetailRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setRemark("remark");
        dineinOrderDetailRespDTO1.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO1.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO1.setGuestCount(0);
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setUserWxPublicOpenId("openId");
        dineInItemDTO2.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO2.setItemType(0);
        dineInItemDTO2.setItemState(0);
        dineInItemDTO2.setSkuGuid("skuGuid");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO2.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO2.setIsPay(0);
        dineInItemDTO2.setIsMemberDiscount(0);
        dineInItemDTO2.setIsWholeDiscount(0);
        dineInItemDTO2.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO2 = new FreeItemDTO();
        dineInItemDTO2.setFreeItemDTOS(Arrays.asList(freeItemDTO2));
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        dineinOrderDetailRespDTO1.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO1.setTip("tip");
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setDeviceId("openId");
        billCalculateReqDTO.setEnterpriseGuid("enterpriseGuid");
        billCalculateReqDTO.setEnterpriseName("enterpriseName");
        billCalculateReqDTO.setStoreGuid("storeGuid");
        billCalculateReqDTO.setOrderGuid("data");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setMemberPhone("openId");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVerify(0);
        when(mockWxStoreDineInBillClientService.calculate(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO1);

        // Run the test
        final DineinOrderDetailRespDTO result = wxStoreTradeOrderServiceImplUnderTest.calculate(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).saveOrderGuid("tableGuid", "data");
    }

    @Test
    public void testSubmitOrder() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build();
        final SubmitReturnDTO expectedResult = SubmitReturnDTO.builder()
                .errorCode(0)
                .errorMsg("errorMsg")
                .orderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .build();

        // Configure OrganizationClientService.findMasterDevice(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO("enterpriseGuid", "storeNo", "storeGuid", "storeName",
                "deviceNo", "deviceGuid", false, false, 0, "deviceName", 0, "createUserGuid", "createUserName",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "tableGuid", 0);
        when(mockOrganizationClientService.findMasterDevice("storeGuid")).thenReturn(storeDeviceDTO);

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(false);
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn("result");
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(false);
        when(mockWxStoreSessionDetailsService.getOrderGuid("tableGuid")).thenReturn("data");
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        when(mockRedisUtils.generatdDTOGuid(WxStoreMerchantOrderDTO.class)).thenReturn("merchatBatchGuid");

        // Configure WxStoreAdvanceOrderService.getSingleWxStoreAdvanceOrder(...).
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setIsWholeDiscount(0);
        wxStoreSkuRespDTO.setIsMemberDiscount(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));
        when(mockWxStoreAdvanceOrderService.getSingleWxStoreAdvanceOrder(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(wxStoreAdvanceOrderDTO);

        // Configure WxStoreCreateFastFoodReqDTOMapstruct.getCreateFastFoodReq(...).
        final CreateFastFoodReqDTO createFastFoodReqDTO = new CreateFastFoodReqDTO();
        createFastFoodReqDTO.setDeviceId("openId");
        createFastFoodReqDTO.setEnterpriseGuid("enterpriseGuid");
        createFastFoodReqDTO.setEnterpriseName("enterpriseName");
        createFastFoodReqDTO.setStoreGuid("storeGuid");
        createFastFoodReqDTO.setRemark("orderRemark");
        createFastFoodReqDTO.setGuestCount(0);
        createFastFoodReqDTO.setUserWxPublicOpenId("openId");
        createFastFoodReqDTO.setWeixinTableCode("code");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        createFastFoodReqDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO1 = new WxStoreAdvanceOrderDTO();
        final WxStoreItemRespDTO wxStoreItemRespDTO1 = new WxStoreItemRespDTO();
        final WxStoreSkuRespDTO wxStoreSkuRespDTO1 = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO1.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO1.setIsWholeDiscount(0);
        wxStoreSkuRespDTO1.setIsMemberDiscount(0);
        wxStoreItemRespDTO1.setSkuList(Arrays.asList(wxStoreSkuRespDTO1));
        wxStoreAdvanceOrderDTO1.setItemList(Arrays.asList(wxStoreItemRespDTO1));
        when(mockWxStoreCreateFastFoodReqDTOMapstruct.getCreateFastFoodReq(wxStoreAdvanceOrderDTO1))
                .thenReturn(createFastFoodReqDTO);

        // Configure WxStoreAdvanceOrderService.checkEstimate(...).
        final WxStoreAdvanceEstimateDTO wxStoreAdvanceEstimateDTO = WxStoreAdvanceEstimateDTO.builder()
                .estimateResult(false)
                .wxStoreEstimateItemList(Arrays.asList(WxStoreEstimateItem.builder().build()))
                .build();
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO2 = new WxStoreAdvanceOrderDTO();
        final WxStoreItemRespDTO wxStoreItemRespDTO2 = new WxStoreItemRespDTO();
        final WxStoreSkuRespDTO wxStoreSkuRespDTO2 = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO2.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO2.setIsWholeDiscount(0);
        wxStoreSkuRespDTO2.setIsMemberDiscount(0);
        wxStoreItemRespDTO2.setSkuList(Arrays.asList(wxStoreSkuRespDTO2));
        wxStoreAdvanceOrderDTO2.setItemList(Arrays.asList(wxStoreItemRespDTO2));
        when(mockWxStoreAdvanceOrderService.checkEstimate(wxStoreAdvanceOrderDTO2))
                .thenReturn(wxStoreAdvanceEstimateDTO);

        // Configure WxStoreDineInOrderClientService.addItem(...).
        final EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(false);
        estimateItemRespDTO.setEstimate(false);
        estimateItemRespDTO.setErrorMsg("errorMsg");
        estimateItemRespDTO.setEstimateInfo("estimateInfo");
        estimateItemRespDTO.setOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        final CreateFastFoodReqDTO createFastFoodReqDTO1 = new CreateFastFoodReqDTO();
        createFastFoodReqDTO1.setDeviceId("openId");
        createFastFoodReqDTO1.setEnterpriseGuid("enterpriseGuid");
        createFastFoodReqDTO1.setEnterpriseName("enterpriseName");
        createFastFoodReqDTO1.setStoreGuid("storeGuid");
        createFastFoodReqDTO1.setRemark("orderRemark");
        createFastFoodReqDTO1.setGuestCount(0);
        createFastFoodReqDTO1.setUserWxPublicOpenId("openId");
        createFastFoodReqDTO1.setWeixinTableCode("code");
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setUserWxPublicOpenId("openId");
        dineInItemDTO2.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO2.setItemType(0);
        dineInItemDTO2.setItemState(0);
        dineInItemDTO2.setSkuGuid("skuGuid");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO2.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO2.setIsPay(0);
        dineInItemDTO2.setIsMemberDiscount(0);
        dineInItemDTO2.setIsWholeDiscount(0);
        dineInItemDTO2.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO2 = new FreeItemDTO();
        dineInItemDTO2.setFreeItemDTOS(Arrays.asList(freeItemDTO2));
        createFastFoodReqDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        when(mockWxStoreDineInOrderClientService.addItem(createFastFoodReqDTO1)).thenReturn(estimateItemRespDTO);

        // Run the test
        final SubmitReturnDTO result = wxStoreTradeOrderServiceImplUnderTest.submitOrder(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).saveOrderGuid("tableGuid", "data");
        verify(mockWxStoreAdvanceOrderService).delAdvanceTableOrder(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build());
        verify(mockWxStoreAdvanceOrderService).delAdvanceOrderRemark(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build());
        verify(mockWxStoreSessionDetailsService).saveFastOrderGuid("storeGuid", "openId",
                "574e403c-d09c-49b4-98fc-953a183a6fb6");

        // Confirm WxStoreMerchantOrderService.updateFastOrder(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setNickName("nickName");
        wxStoreMerchantOrderDTO.setDiningTableGuid("tableGuid");
        wxStoreMerchantOrderDTO.setTableCode("code");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setItemCount(0);
        wxStoreMerchantOrderDTO.setActualGuestsNo(0);
        wxStoreMerchantOrderDTO.setTradeGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        wxStoreMerchantOrderDTO.setRemark("remark");
        wxStoreMerchantOrderDTO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDTO.setAreaName("areaName");
        wxStoreMerchantOrderDTO.setOperationName("nickName");
        wxStoreMerchantOrderDTO.setHeadImgUrl("headImgUrl");
        wxStoreMerchantOrderDTO.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDTO.setCombine("data");
        verify(mockWxStoreMerchantOrderService).updateFastOrder(wxStoreMerchantOrderDTO);

        // Confirm WxOrderRecordService.update(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO1.setUpperState(0);
        dineinOrderDetailRespDTO1.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO1.setMainOrderGuid("data");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO1.setOrderFeeDetailDTO(orderFeeDetailDTO1);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO1.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("volumeName");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        dineinOrderDetailRespDTO1.setState(0);
        dineinOrderDetailRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setRemark("remark");
        dineinOrderDetailRespDTO1.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO1.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO1.setGuestCount(0);
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setUserWxPublicOpenId("openId");
        dineInItemDTO3.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO3.setItemType(0);
        dineInItemDTO3.setItemState(0);
        dineInItemDTO3.setSkuGuid("skuGuid");
        dineInItemDTO3.setPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO3.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO3.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO3.setIsPay(0);
        dineInItemDTO3.setIsMemberDiscount(0);
        dineInItemDTO3.setIsWholeDiscount(0);
        dineInItemDTO3.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO3.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO3 = new FreeItemDTO();
        dineInItemDTO3.setFreeItemDTOS(Arrays.asList(freeItemDTO3));
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO3));
        dineinOrderDetailRespDTO1.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO1.setTip("tip");
        final WxStoreAccountDTO wxStoreAccountDTO = new WxStoreAccountDTO(dineinOrderDetailRespDTO1,
                WxStoreAdvanceConsumerReqDTO.builder()
                        .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                        .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                                .openId("openId")
                                .nickName("nickName")
                                .headImgUrl("headImgUrl")
                                .sex(0)
                                .enterpriseGuid("enterpriseGuid")
                                .enterpriseName("enterpriseName")
                                .storeGuid("storeGuid")
                                .storeName("storeName")
                                .diningTableGuid("tableGuid")
                                .tableCode("code")
                                .diningTableName("code")
                                .areaGuid("areaGuid")
                                .areaName("areaName")
                                .brandName("brandName")
                                .brandGuid("brandGuid")
                                .build())
                        .userCount(0)
                        .orderRemark("orderRemark")
                        .pageNum(0)
                        .build());
        verify(mockWxOrderRecordService).update(wxStoreAccountDTO);

        // Confirm RedisUtils.set(...).
        final WxStoreAdvanceOrderDTO value = new WxStoreAdvanceOrderDTO();
        final WxStoreItemRespDTO wxStoreItemRespDTO3 = new WxStoreItemRespDTO();
        final WxStoreSkuRespDTO wxStoreSkuRespDTO3 = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO3.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO3.setIsWholeDiscount(0);
        wxStoreSkuRespDTO3.setIsMemberDiscount(0);
        wxStoreItemRespDTO3.setSkuList(Arrays.asList(wxStoreSkuRespDTO3));
        value.setItemList(Arrays.asList(wxStoreItemRespDTO3));
        verify(mockRedisUtils).set("key", value);
    }

    @Test
    public void testSubmitOrder_WxStoreMenuDetailsServiceJudgeOrderType1ReturnsTrue() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build();
        final SubmitReturnDTO expectedResult = SubmitReturnDTO.builder()
                .errorCode(0)
                .errorMsg("errorMsg")
                .orderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .build();

        // Configure OrganizationClientService.findMasterDevice(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO("enterpriseGuid", "storeNo", "storeGuid", "storeName",
                "deviceNo", "deviceGuid", false, false, 0, "deviceName", 0, "createUserGuid", "createUserName",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "tableGuid", 0);
        when(mockOrganizationClientService.findMasterDevice("storeGuid")).thenReturn(storeDeviceDTO);

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(true);
        when(mockWxStoreSessionDetailsService.getDinnerGuestsCount("tableGuid")).thenReturn(0);
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn("result");
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(false);
        when(mockWxStoreSessionDetailsService.getOrderGuid("tableGuid")).thenReturn("data");
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        when(mockWxStoreSessionDetailsService.getOrderState("tableGuid")).thenReturn(0);
        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("tableGuid")).thenReturn("result");
        when(mockRedisUtils.generatdDTOGuid(WxStoreMerchantOrderDTO.class)).thenReturn("merchatBatchGuid");

        // Configure WxStoreAdvanceOrderService.getTableWxStoreAdvanceOrder(...).
        final WxStoreAdvanceOrderPriceDTO wxStoreAdvanceOrderPriceDTO = new WxStoreAdvanceOrderPriceDTO();
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setIsWholeDiscount(0);
        wxStoreSkuRespDTO.setIsMemberDiscount(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));
        wxStoreAdvanceOrderPriceDTO.setWxStoreAdvanceOrderDTOS(Arrays.asList(wxStoreAdvanceOrderDTO));
        when(mockWxStoreAdvanceOrderService.getTableWxStoreAdvanceOrder(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(wxStoreAdvanceOrderPriceDTO);

        // Configure WxStoreCreateDineInOrderReqDTOMapStruct.getCreateDineInOrderReq(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceId("openId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        createDineInOrderReqDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        final WxStoreItemRespDTO wxStoreItemRespDTO1 = new WxStoreItemRespDTO();
        wxStoreItemRespDTO1.setItemGuid("itemGuid");
        final WxStoreSkuRespDTO wxStoreSkuRespDTO1 = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO1.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO1.setIsWholeDiscount(0);
        wxStoreSkuRespDTO1.setIsMemberDiscount(0);
        wxStoreItemRespDTO1.setSkuList(Arrays.asList(wxStoreSkuRespDTO1));
        final List<WxStoreItemRespDTO> wxStoreItemRespDTOS = Arrays.asList(wxStoreItemRespDTO1);
        when(mockWxStoreCreateDineInOrderReqDTOMapStruct.getCreateDineInOrderReq(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build(), wxStoreItemRespDTOS)).thenReturn(createDineInOrderReqDTO);

        when(mockWxStoreAdvanceOrderService.getAdvanceOrderRemark(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn("remark");
        when(mockWxStoreAdvanceOrderService.getTotalPrice(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(new BigDecimal("0.00"));

        // Run the test
        final SubmitReturnDTO result = wxStoreTradeOrderServiceImplUnderTest.submitOrder(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).saveOrderGuid("tableGuid", "data");
        verify(mockWxStoreSessionDetailsService).updateDinnerGuestsCount("tableGuid", 0);
        verify(mockWxStoreSessionDetailsService).saveOrderState("tableGuid", 0);
        verify(mockWxStoreSessionDetailsService).updateMerchantBatchGuid("tableGuid", "merchatBatchGuid");
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");

        // Confirm WxStoreMerchantOrderService.updateMerchantOrder(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setNickName("nickName");
        wxStoreMerchantOrderDTO.setDiningTableGuid("tableGuid");
        wxStoreMerchantOrderDTO.setTableCode("code");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setItemCount(0);
        wxStoreMerchantOrderDTO.setActualGuestsNo(0);
        wxStoreMerchantOrderDTO.setTradeGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        wxStoreMerchantOrderDTO.setRemark("remark");
        wxStoreMerchantOrderDTO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDTO.setAreaName("areaName");
        wxStoreMerchantOrderDTO.setOperationName("nickName");
        wxStoreMerchantOrderDTO.setHeadImgUrl("headImgUrl");
        wxStoreMerchantOrderDTO.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDTO.setCombine("data");
        verify(mockWxStoreMerchantOrderService).updateMerchantOrder(wxStoreMerchantOrderDTO);

        // Confirm WxStoreMerchantDineInItemService.update(...).
        final WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItemDTO = new WxStoreMerchantDineInItemDTO();
        wxStoreMerchantDineInItemDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setUserWxPublicOpenId("openId");
        dineInItemDTO2.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO2.setItemType(0);
        dineInItemDTO2.setItemState(0);
        dineInItemDTO2.setSkuGuid("skuGuid");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO2.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO2.setIsPay(0);
        dineInItemDTO2.setIsMemberDiscount(0);
        dineInItemDTO2.setIsWholeDiscount(0);
        dineInItemDTO2.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO2 = new FreeItemDTO();
        dineInItemDTO2.setFreeItemDTOS(Arrays.asList(freeItemDTO2));
        wxStoreMerchantDineInItemDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        wxStoreMerchantDineInItemDTO.setItemCount(0);
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        wxStoreMerchantDineInItemDTO.setOrderFeeDetailDTOS(Arrays.asList(orderFeeDetailDTO1));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        wxStoreMerchantDineInItemDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("volumeName");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        wxStoreMerchantDineInItemDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        wxStoreMerchantDineInItemDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        verify(mockWxStoreMerchantDineInItemService).update(wxStoreMerchantDineInItemDTO);
        verify(mockWxStoreAdvanceOrderService).delAdvanceTableOrder(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build());
        verify(mockWxStoreAdvanceOrderService).delAdvanceOrderRemark(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build());
        verify(mockWxSocketMsgService).distribute(WxSocketDistributionDTO.builder()
                .distribution(0)
                .tableGuid("tableGuid")
                .openId("openId")
                .content("content")
                .build());
    }

    @Test
    public void testSubmitOrder_WxStoreTableClientServiceReturnsNull() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build();
        final SubmitReturnDTO expectedResult = SubmitReturnDTO.builder()
                .errorCode(0)
                .errorMsg("errorMsg")
                .orderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .build();

        // Configure OrganizationClientService.findMasterDevice(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO("enterpriseGuid", "storeNo", "storeGuid", "storeName",
                "deviceNo", "deviceGuid", false, false, 0, "deviceName", 0, "createUserGuid", "createUserName",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "tableGuid", 0);
        when(mockOrganizationClientService.findMasterDevice("storeGuid")).thenReturn(storeDeviceDTO);

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(true);
        when(mockWxStoreSessionDetailsService.getDinnerGuestsCount("tableGuid")).thenReturn(0);
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn(null);
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(false);
        when(mockWxStoreSessionDetailsService.getOrderGuid("tableGuid")).thenReturn("data");
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        when(mockWxStoreSessionDetailsService.getOrderState("tableGuid")).thenReturn(0);
        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("tableGuid")).thenReturn("result");
        when(mockRedisUtils.generatdDTOGuid(WxStoreMerchantOrderDTO.class)).thenReturn("merchatBatchGuid");

        // Configure WxStoreAdvanceOrderService.getTableWxStoreAdvanceOrder(...).
        final WxStoreAdvanceOrderPriceDTO wxStoreAdvanceOrderPriceDTO = new WxStoreAdvanceOrderPriceDTO();
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setIsWholeDiscount(0);
        wxStoreSkuRespDTO.setIsMemberDiscount(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));
        wxStoreAdvanceOrderPriceDTO.setWxStoreAdvanceOrderDTOS(Arrays.asList(wxStoreAdvanceOrderDTO));
        when(mockWxStoreAdvanceOrderService.getTableWxStoreAdvanceOrder(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(wxStoreAdvanceOrderPriceDTO);

        // Configure WxStoreCreateDineInOrderReqDTOMapStruct.getCreateDineInOrderReq(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceId("openId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        createDineInOrderReqDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        final WxStoreItemRespDTO wxStoreItemRespDTO1 = new WxStoreItemRespDTO();
        wxStoreItemRespDTO1.setItemGuid("itemGuid");
        final WxStoreSkuRespDTO wxStoreSkuRespDTO1 = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO1.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO1.setIsWholeDiscount(0);
        wxStoreSkuRespDTO1.setIsMemberDiscount(0);
        wxStoreItemRespDTO1.setSkuList(Arrays.asList(wxStoreSkuRespDTO1));
        final List<WxStoreItemRespDTO> wxStoreItemRespDTOS = Arrays.asList(wxStoreItemRespDTO1);
        when(mockWxStoreCreateDineInOrderReqDTOMapStruct.getCreateDineInOrderReq(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build(), wxStoreItemRespDTOS)).thenReturn(createDineInOrderReqDTO);

        when(mockWxStoreAdvanceOrderService.getAdvanceOrderRemark(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn("remark");
        when(mockWxStoreAdvanceOrderService.getTotalPrice(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(new BigDecimal("0.00"));

        // Run the test
        final SubmitReturnDTO result = wxStoreTradeOrderServiceImplUnderTest.submitOrder(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).saveOrderGuid("tableGuid", "data");
        verify(mockWxStoreSessionDetailsService).saveOrderState("tableGuid", 0);
        verify(mockWxStoreSessionDetailsService).updateMerchantBatchGuid("tableGuid", "merchatBatchGuid");
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");

        // Confirm WxStoreMerchantOrderService.updateMerchantOrder(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setNickName("nickName");
        wxStoreMerchantOrderDTO.setDiningTableGuid("tableGuid");
        wxStoreMerchantOrderDTO.setTableCode("code");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setItemCount(0);
        wxStoreMerchantOrderDTO.setActualGuestsNo(0);
        wxStoreMerchantOrderDTO.setTradeGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        wxStoreMerchantOrderDTO.setRemark("remark");
        wxStoreMerchantOrderDTO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDTO.setAreaName("areaName");
        wxStoreMerchantOrderDTO.setOperationName("nickName");
        wxStoreMerchantOrderDTO.setHeadImgUrl("headImgUrl");
        wxStoreMerchantOrderDTO.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDTO.setCombine("data");
        verify(mockWxStoreMerchantOrderService).updateMerchantOrder(wxStoreMerchantOrderDTO);

        // Confirm WxStoreMerchantDineInItemService.update(...).
        final WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItemDTO = new WxStoreMerchantDineInItemDTO();
        wxStoreMerchantDineInItemDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setUserWxPublicOpenId("openId");
        dineInItemDTO2.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO2.setItemType(0);
        dineInItemDTO2.setItemState(0);
        dineInItemDTO2.setSkuGuid("skuGuid");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO2.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO2.setIsPay(0);
        dineInItemDTO2.setIsMemberDiscount(0);
        dineInItemDTO2.setIsWholeDiscount(0);
        dineInItemDTO2.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO2 = new FreeItemDTO();
        dineInItemDTO2.setFreeItemDTOS(Arrays.asList(freeItemDTO2));
        wxStoreMerchantDineInItemDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        wxStoreMerchantDineInItemDTO.setItemCount(0);
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        wxStoreMerchantDineInItemDTO.setOrderFeeDetailDTOS(Arrays.asList(orderFeeDetailDTO1));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        wxStoreMerchantDineInItemDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("volumeName");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        wxStoreMerchantDineInItemDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        wxStoreMerchantDineInItemDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        verify(mockWxStoreMerchantDineInItemService).update(wxStoreMerchantDineInItemDTO);
        verify(mockWxStoreAdvanceOrderService).delAdvanceTableOrder(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build());
        verify(mockWxStoreAdvanceOrderService).delAdvanceOrderRemark(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build());
        verify(mockWxSocketMsgService).distribute(WxSocketDistributionDTO.builder()
                .distribution(0)
                .tableGuid("tableGuid")
                .openId("openId")
                .content("content")
                .build());
    }

    @Test
    public void testSubmitOrder_WxStoreMenuDetailsServiceJudgeOrderType2ReturnsTrue() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build();
        final SubmitReturnDTO expectedResult = SubmitReturnDTO.builder()
                .errorCode(0)
                .errorMsg("errorMsg")
                .orderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .build();

        // Configure OrganizationClientService.findMasterDevice(...).
        final StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO("enterpriseGuid", "storeNo", "storeGuid", "storeName",
                "deviceNo", "deviceGuid", false, false, 0, "deviceName", 0, "createUserGuid", "createUserName",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "tableGuid", 0);
        when(mockOrganizationClientService.findMasterDevice("storeGuid")).thenReturn(storeDeviceDTO);

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(true);
        when(mockWxStoreSessionDetailsService.getDinnerGuestsCount("tableGuid")).thenReturn(0);
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn("result");
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(true);
        when(mockWxStoreSessionDetailsService.getOrderGuid("tableGuid")).thenReturn("data");
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        when(mockWxStoreSessionDetailsService.getOrderState("tableGuid")).thenReturn(0);
        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("tableGuid")).thenReturn("result");
        when(mockRedisUtils.generatdDTOGuid(WxStoreMerchantOrderDTO.class)).thenReturn("merchatBatchGuid");

        // Configure WxStoreAdvanceOrderService.getTableWxStoreAdvanceOrder(...).
        final WxStoreAdvanceOrderPriceDTO wxStoreAdvanceOrderPriceDTO = new WxStoreAdvanceOrderPriceDTO();
        final WxStoreAdvanceOrderDTO wxStoreAdvanceOrderDTO = new WxStoreAdvanceOrderDTO();
        final WxStoreItemRespDTO wxStoreItemRespDTO = new WxStoreItemRespDTO();
        final WxStoreSkuRespDTO wxStoreSkuRespDTO = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO.setIsWholeDiscount(0);
        wxStoreSkuRespDTO.setIsMemberDiscount(0);
        wxStoreItemRespDTO.setSkuList(Arrays.asList(wxStoreSkuRespDTO));
        wxStoreAdvanceOrderDTO.setItemList(Arrays.asList(wxStoreItemRespDTO));
        wxStoreAdvanceOrderPriceDTO.setWxStoreAdvanceOrderDTOS(Arrays.asList(wxStoreAdvanceOrderDTO));
        when(mockWxStoreAdvanceOrderService.getTableWxStoreAdvanceOrder(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(wxStoreAdvanceOrderPriceDTO);

        // Configure WxStoreCreateDineInOrderReqDTOMapStruct.getCreateDineInOrderReq(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceId("openId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        createDineInOrderReqDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        final WxStoreItemRespDTO wxStoreItemRespDTO1 = new WxStoreItemRespDTO();
        wxStoreItemRespDTO1.setItemGuid("itemGuid");
        final WxStoreSkuRespDTO wxStoreSkuRespDTO1 = new WxStoreSkuRespDTO();
        wxStoreSkuRespDTO1.setSkuGuid("skuGuid");
        wxStoreSkuRespDTO1.setIsWholeDiscount(0);
        wxStoreSkuRespDTO1.setIsMemberDiscount(0);
        wxStoreItemRespDTO1.setSkuList(Arrays.asList(wxStoreSkuRespDTO1));
        final List<WxStoreItemRespDTO> wxStoreItemRespDTOS = Arrays.asList(wxStoreItemRespDTO1);
        when(mockWxStoreCreateDineInOrderReqDTOMapStruct.getCreateDineInOrderReq(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build(), wxStoreItemRespDTOS)).thenReturn(createDineInOrderReqDTO);

        when(mockWxStoreAdvanceOrderService.getAdvanceOrderRemark(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn("remark");
        when(mockWxStoreAdvanceOrderService.getTotalPrice(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(new BigDecimal("0.00"));

        // Run the test
        final SubmitReturnDTO result = wxStoreTradeOrderServiceImplUnderTest.submitOrder(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).saveOrderGuid("tableGuid", "data");
        verify(mockWxStoreSessionDetailsService).saveOrderState("tableGuid", 0);
        verify(mockWxStoreSessionDetailsService).updateMerchantBatchGuid("tableGuid", "merchatBatchGuid");
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");

        // Confirm WxStoreMerchantOrderService.updateMerchantOrder(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setNickName("nickName");
        wxStoreMerchantOrderDTO.setDiningTableGuid("tableGuid");
        wxStoreMerchantOrderDTO.setTableCode("code");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setItemCount(0);
        wxStoreMerchantOrderDTO.setActualGuestsNo(0);
        wxStoreMerchantOrderDTO.setTradeGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        wxStoreMerchantOrderDTO.setRemark("remark");
        wxStoreMerchantOrderDTO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDTO.setAreaName("areaName");
        wxStoreMerchantOrderDTO.setOperationName("nickName");
        wxStoreMerchantOrderDTO.setHeadImgUrl("headImgUrl");
        wxStoreMerchantOrderDTO.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDTO.setCombine("data");
        verify(mockWxStoreMerchantOrderService).updateMerchantOrder(wxStoreMerchantOrderDTO);

        // Confirm WxStoreMerchantDineInItemService.update(...).
        final WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItemDTO = new WxStoreMerchantDineInItemDTO();
        wxStoreMerchantDineInItemDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setUserWxPublicOpenId("openId");
        dineInItemDTO2.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO2.setItemType(0);
        dineInItemDTO2.setItemState(0);
        dineInItemDTO2.setSkuGuid("skuGuid");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO2.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO2.setIsPay(0);
        dineInItemDTO2.setIsMemberDiscount(0);
        dineInItemDTO2.setIsWholeDiscount(0);
        dineInItemDTO2.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO2 = new FreeItemDTO();
        dineInItemDTO2.setFreeItemDTOS(Arrays.asList(freeItemDTO2));
        wxStoreMerchantDineInItemDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        wxStoreMerchantDineInItemDTO.setItemCount(0);
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        wxStoreMerchantDineInItemDTO.setOrderFeeDetailDTOS(Arrays.asList(orderFeeDetailDTO1));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        wxStoreMerchantDineInItemDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("volumeName");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        wxStoreMerchantDineInItemDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        wxStoreMerchantDineInItemDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        verify(mockWxStoreMerchantDineInItemService).update(wxStoreMerchantDineInItemDTO);
        verify(mockWxStoreAdvanceOrderService).delAdvanceTableOrder(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build());
        verify(mockWxStoreAdvanceOrderService).delAdvanceOrderRemark(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build());
        verify(mockWxSocketMsgService).distribute(WxSocketDistributionDTO.builder()
                .distribution(0)
                .tableGuid("tableGuid")
                .openId("openId")
                .content("content")
                .build());
    }

    @Test
    public void testClearAdvanceDetails() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build();

        // Run the test
        wxStoreTradeOrderServiceImplUnderTest.clearAdvanceDetails(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        verify(mockWxStoreAdvanceOrderService).delAdvanceTableOrder(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build());
        verify(mockWxStoreAdvanceOrderService).delAdvanceOrderRemark(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build());
    }

    @Test
    public void testUpdateRemark() {
        // Setup
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceId("openId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        createDineInOrderReqDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));

        // Configure WxStoreDineInOrderClientService.updateRemark(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO1 = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO1.setDeviceId("openId");
        createDineInOrderReqDTO1.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO1.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO1.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        createDineInOrderReqDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        when(mockWxStoreDineInOrderClientService.updateRemark(createDineInOrderReqDTO1)).thenReturn(false);

        // Run the test
        final Boolean result = wxStoreTradeOrderServiceImplUnderTest.updateRemark(createDineInOrderReqDTO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testUpdateRemark_WxStoreDineInOrderClientServiceReturnsTrue() {
        // Setup
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceId("openId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        createDineInOrderReqDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));

        // Configure WxStoreDineInOrderClientService.updateRemark(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO1 = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO1.setDeviceId("openId");
        createDineInOrderReqDTO1.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO1.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO1.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        createDineInOrderReqDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        when(mockWxStoreDineInOrderClientService.updateRemark(createDineInOrderReqDTO1)).thenReturn(true);

        // Run the test
        final Boolean result = wxStoreTradeOrderServiceImplUnderTest.updateRemark(createDineInOrderReqDTO);

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testUpdateGuestCount() {
        // Setup
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceId("openId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        createDineInOrderReqDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));

        // Configure WxStoreDineInOrderClientService.updateGuestCount(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO1 = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO1.setDeviceId("openId");
        createDineInOrderReqDTO1.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO1.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO1.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        createDineInOrderReqDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        when(mockWxStoreDineInOrderClientService.updateGuestCount(createDineInOrderReqDTO1)).thenReturn(false);

        // Run the test
        final Boolean result = wxStoreTradeOrderServiceImplUnderTest.updateGuestCount(createDineInOrderReqDTO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testUpdateGuestCount_WxStoreDineInOrderClientServiceReturnsTrue() {
        // Setup
        final CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO.setDeviceId("openId");
        createDineInOrderReqDTO.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        createDineInOrderReqDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));

        // Configure WxStoreDineInOrderClientService.updateGuestCount(...).
        final CreateDineInOrderReqDTO createDineInOrderReqDTO1 = new CreateDineInOrderReqDTO();
        createDineInOrderReqDTO1.setDeviceId("openId");
        createDineInOrderReqDTO1.setEnterpriseGuid("enterpriseGuid");
        createDineInOrderReqDTO1.setEnterpriseName("enterpriseName");
        createDineInOrderReqDTO1.setStoreGuid("storeGuid");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        createDineInOrderReqDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        when(mockWxStoreDineInOrderClientService.updateGuestCount(createDineInOrderReqDTO1)).thenReturn(true);

        // Run the test
        final Boolean result = wxStoreTradeOrderServiceImplUnderTest.updateGuestCount(createDineInOrderReqDTO);

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testGetDineinOrderDetailResp() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build();
        final DineinOrderDetailRespDTO expectedResult = new DineinOrderDetailRespDTO();
        expectedResult.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        expectedResult.setUpperState(0);
        expectedResult.setMainOrderNo("mainOrderNo");
        expectedResult.setMainOrderGuid("data");
        expectedResult.setTradeMode(0);
        expectedResult.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        expectedResult.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        expectedResult.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        expectedResult.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        expectedResult.setOrderNo("orderNo");
        expectedResult.setState(0);
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRemark("remark");
        expectedResult.setDiningTableGuid("tableGuid");
        expectedResult.setDiningTableName("tableCode");
        expectedResult.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        expectedResult.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        expectedResult.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        expectedResult.setTip("tip");

        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(false);
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO1);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("volumeName");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Run the test
        final DineinOrderDetailRespDTO result = wxStoreTradeOrderServiceImplUnderTest.getDineinOrderDetailResp(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetDineinOrderDetailResp_WxStoreMenuDetailsServiceReturnsTrue() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build();
        final DineinOrderDetailRespDTO expectedResult = new DineinOrderDetailRespDTO();
        expectedResult.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        expectedResult.setUpperState(0);
        expectedResult.setMainOrderNo("mainOrderNo");
        expectedResult.setMainOrderGuid("data");
        expectedResult.setTradeMode(0);
        expectedResult.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        expectedResult.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        expectedResult.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        expectedResult.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        expectedResult.setOrderNo("orderNo");
        expectedResult.setState(0);
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRemark("remark");
        expectedResult.setDiningTableGuid("tableGuid");
        expectedResult.setDiningTableName("tableCode");
        expectedResult.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        expectedResult.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        expectedResult.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        expectedResult.setTip("tip");

        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(true);
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn("data");
        when(mockWxStoreSessionDetailsService.getOrderGuid("tableGuid")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO1);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("volumeName");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Run the test
        final DineinOrderDetailRespDTO result = wxStoreTradeOrderServiceImplUnderTest.getDineinOrderDetailResp(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).saveOrderGuid("tableGuid", "data");
    }

    @Test
    public void testGetOrderGuid() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(false);
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId")).thenReturn("data");

        // Run the test
        final String result = wxStoreTradeOrderServiceImplUnderTest.getOrderGuid(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals("data", result);
    }

    @Test
    public void testGetOrderGuid_WxStoreMenuDetailsServiceReturnsTrue() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(true);
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn("data");
        when(mockWxStoreSessionDetailsService.getOrderGuid("tableGuid")).thenReturn("data");

        // Run the test
        final String result = wxStoreTradeOrderServiceImplUnderTest.getOrderGuid(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals("data", result);
        verify(mockWxStoreSessionDetailsService).saveOrderGuid("tableGuid", "data");
    }

    @Test
    public void testSetUpOrderState1() {
        // Setup
        final WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO = new WxStoreTradeOrderDetailsDTO();
        wxStoreTradeOrderDetailsDTO.setMainTable(0);
        wxStoreTradeOrderDetailsDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        wxStoreTradeOrderDetailsDTO.setWxGuid("guid");
        wxStoreTradeOrderDetailsDTO.setBatchId("batchId");
        wxStoreTradeOrderDetailsDTO.setTableGuid("tableGuid");
        wxStoreTradeOrderDetailsDTO.setOrderNo("orderNo");
        wxStoreTradeOrderDetailsDTO.setTableCode("code");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        wxStoreTradeOrderDetailsDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        wxStoreTradeOrderDetailsDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        wxStoreTradeOrderDetailsDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreTradeOrderDetailsDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreTradeOrderDetailsDTO.setOrderModel(0);
        wxStoreTradeOrderDetailsDTO.setState(0);
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        wxStoreTradeOrderDetailsDTO.setOrderFeeDetailDTOS(Arrays.asList(orderFeeDetailDTO));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        wxStoreTradeOrderDetailsDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        wxStoreTradeOrderDetailsDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        wxStoreTradeOrderDetailsDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build());
        final List<WxStoreTradeOrderDetailsDTO> wxStoreTradeOrderDetailsDTOS = Arrays.asList(
                wxStoreTradeOrderDetailsDTO);

        // Run the test
        final Integer result = wxStoreTradeOrderServiceImplUnderTest.setUpOrderState(wxStoreTradeOrderDetailsDTOS);

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testSetUpOrderState2() {
        // Setup
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");

        final WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO = new WxStoreTradeOrderDetailsDTO();
        wxStoreTradeOrderDetailsDTO.setMainTable(0);
        wxStoreTradeOrderDetailsDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        wxStoreTradeOrderDetailsDTO.setWxGuid("guid");
        wxStoreTradeOrderDetailsDTO.setBatchId("batchId");
        wxStoreTradeOrderDetailsDTO.setTableGuid("tableGuid");
        wxStoreTradeOrderDetailsDTO.setOrderNo("orderNo");
        wxStoreTradeOrderDetailsDTO.setTableCode("code");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        wxStoreTradeOrderDetailsDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        wxStoreTradeOrderDetailsDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        wxStoreTradeOrderDetailsDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreTradeOrderDetailsDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreTradeOrderDetailsDTO.setOrderModel(0);
        wxStoreTradeOrderDetailsDTO.setState(0);
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        wxStoreTradeOrderDetailsDTO.setOrderFeeDetailDTOS(Arrays.asList(orderFeeDetailDTO1));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        wxStoreTradeOrderDetailsDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("volumeName");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        wxStoreTradeOrderDetailsDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        wxStoreTradeOrderDetailsDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build());

        // Run the test
        wxStoreTradeOrderServiceImplUnderTest.setUpOrderState(dineinOrderDetailRespDTO, wxStoreTradeOrderDetailsDTO);

        // Verify the results
    }

    @Test
    public void testRefreshTable1() {
        // Setup
        // Run the test
        wxStoreTradeOrderServiceImplUnderTest.refreshTable("tableGuid");

        // Verify the results
        verify(mockWxSocketMsgService).distribute(WxSocketDistributionDTO.builder()
                .distribution(0)
                .tableGuid("tableGuid")
                .openId("openId")
                .content("content")
                .build());
    }

    @Test
    public void testRefreshTable2() {
        // Setup
        // Run the test
        wxStoreTradeOrderServiceImplUnderTest.refreshTable("tableGuid", "openId", 0);

        // Verify the results
        verify(mockWxSocketMsgService).distribute(WxSocketDistributionDTO.builder()
                .distribution(0)
                .tableGuid("tableGuid")
                .openId("openId")
                .content("content")
                .build());
    }

    @Test
    public void testGetMainOrderDetails() {
        // Setup
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");

        final DineinOrderDetailRespDTO expectedResult = new DineinOrderDetailRespDTO();
        expectedResult.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        expectedResult.setUpperState(0);
        expectedResult.setMainOrderNo("mainOrderNo");
        expectedResult.setMainOrderGuid("data");
        expectedResult.setTradeMode(0);
        expectedResult.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        expectedResult.setOrderFeeDetailDTO(orderFeeDetailDTO1);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        expectedResult.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("volumeName");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        expectedResult.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        expectedResult.setOrderNo("orderNo");
        expectedResult.setState(0);
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRemark("remark");
        expectedResult.setDiningTableGuid("tableGuid");
        expectedResult.setDiningTableName("tableCode");
        expectedResult.setGuestCount(0);
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        expectedResult.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        expectedResult.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        expectedResult.setTip("tip");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO1.setUpperState(0);
        dineinOrderDetailRespDTO1.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO1.setMainOrderGuid("data");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO2 = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO1.setOrderFeeDetailDTO(orderFeeDetailDTO2);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO2 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO2.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO1.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO2));
        final DiscountFeeDetailDTO discountFeeDetailDTO2 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO2.setDiscountName("volumeName");
        discountFeeDetailDTO2.setDiscountType(0);
        discountFeeDetailDTO2.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO2));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        dineinOrderDetailRespDTO1.setState(0);
        dineinOrderDetailRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setRemark("remark");
        dineinOrderDetailRespDTO1.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO1.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO1.setGuestCount(0);
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setUserWxPublicOpenId("openId");
        dineInItemDTO2.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO2.setItemType(0);
        dineInItemDTO2.setItemState(0);
        dineInItemDTO2.setSkuGuid("skuGuid");
        dineInItemDTO2.setPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO2.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO2.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO2.setIsPay(0);
        dineInItemDTO2.setIsMemberDiscount(0);
        dineInItemDTO2.setIsWholeDiscount(0);
        dineInItemDTO2.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO2.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO2 = new FreeItemDTO();
        dineInItemDTO2.setFreeItemDTOS(Arrays.asList(freeItemDTO2));
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        dineinOrderDetailRespDTO1.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO1.setTip("tip");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO1);

        // Run the test
        final DineinOrderDetailRespDTO result = wxStoreTradeOrderServiceImplUnderTest.getMainOrderDetails(
                dineinOrderDetailRespDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testValidateCardAndVolume() {
        // Setup
        final CardAndVolumeDiscountReqDTO cardAndVolumeDiscountReqDTO = CardAndVolumeDiscountReqDTO.builder()
                .storeGuid("storeGuid")
                .tableGuid("tableGuid")
                .openId("openId")
                .volumeCode("volumeCode")
                .memberInfoCardGuid("memberInfoCardGuid")
                .volumeName("volumeName")
                .discountType(0)
                .build();
        final CardAndVolumeDTO expectedResult = CardAndVolumeDTO.builder()
                .memberDiscountDTOS(Arrays.asList(MemberDiscountDTO.builder().build()))
                .discountAmount(new BigDecimal("0.00"))
                .result(0)
                .errorMsg("errorMsg")
                .build();
        when(mockWxStoreSessionDetailsService.getOrderGuid("storeGuid", "tableGuid", "openId")).thenReturn("orderGuid");

        // Configure WxStoreSessionDetailsService.calculateDetails(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        final CorrectResult<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOCorrectResult = new CorrectResult<>(
                "errorMsg", 0, dineinOrderDetailRespDTO);
        when(mockWxStoreSessionDetailsService.calculateDetails("orderGuid", "openId", "memberInfoCardGuid", 2,
                "volumeCode")).thenReturn(dineinOrderDetailRespDTOCorrectResult);

        // Run the test
        final CardAndVolumeDTO result = wxStoreTradeOrderServiceImplUnderTest.validateCardAndVolume(
                cardAndVolumeDiscountReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
    }

    @Test
    public void testValidateCardAndVolume_WxStoreSessionDetailsServiceCalculateDetailsReturnsNoItem() {
        // Setup
        final CardAndVolumeDiscountReqDTO cardAndVolumeDiscountReqDTO = CardAndVolumeDiscountReqDTO.builder()
                .storeGuid("storeGuid")
                .tableGuid("tableGuid")
                .openId("openId")
                .volumeCode("volumeCode")
                .memberInfoCardGuid("memberInfoCardGuid")
                .volumeName("volumeName")
                .discountType(0)
                .build();
        final CardAndVolumeDTO expectedResult = CardAndVolumeDTO.builder()
                .memberDiscountDTOS(Arrays.asList(MemberDiscountDTO.builder().build()))
                .discountAmount(new BigDecimal("0.00"))
                .result(0)
                .errorMsg("errorMsg")
                .build();
        when(mockWxStoreSessionDetailsService.getOrderGuid("storeGuid", "tableGuid", "openId")).thenReturn("orderGuid");
        when(mockWxStoreSessionDetailsService.calculateDetails("orderGuid", "openId", "memberInfoCardGuid", 2,
                "volumeCode")).thenReturn(CorrectResult.changeFailed());

        // Run the test
        final CardAndVolumeDTO result = wxStoreTradeOrderServiceImplUnderTest.validateCardAndVolume(
                cardAndVolumeDiscountReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
    }

    @Test
    public void testValidateCardAndVolume_WxStoreSessionDetailsServiceCalculateDetailsReturnsFailure() {
        // Setup
        final CardAndVolumeDiscountReqDTO cardAndVolumeDiscountReqDTO = CardAndVolumeDiscountReqDTO.builder()
                .storeGuid("storeGuid")
                .tableGuid("tableGuid")
                .openId("openId")
                .volumeCode("volumeCode")
                .memberInfoCardGuid("memberInfoCardGuid")
                .volumeName("volumeName")
                .discountType(0)
                .build();
        final CardAndVolumeDTO expectedResult = CardAndVolumeDTO.builder()
                .memberDiscountDTOS(Arrays.asList(MemberDiscountDTO.builder().build()))
                .discountAmount(new BigDecimal("0.00"))
                .result(0)
                .errorMsg("errorMsg")
                .build();
        when(mockWxStoreSessionDetailsService.getOrderGuid("storeGuid", "tableGuid", "openId")).thenReturn("orderGuid");

        // Configure WxStoreSessionDetailsService.calculateDetails(...).
        final CorrectResult<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOCorrectResult = CorrectResult.changeFailed();
        when(mockWxStoreSessionDetailsService.calculateDetails("orderGuid", "openId", "memberInfoCardGuid", 2,
                "volumeCode")).thenReturn(dineinOrderDetailRespDTOCorrectResult);

        // Run the test
        final CardAndVolumeDTO result = wxStoreTradeOrderServiceImplUnderTest.validateCardAndVolume(
                cardAndVolumeDiscountReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
    }

    @Test
    public void testCardList() {
        // Setup
        final WxMemberCardListReqDTO wxMemberCardListReqDTO = WxMemberCardListReqDTO.builder()
                .openId("openId")
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .build();
        final WxMemberCardRespDTO expectedResult = WxMemberCardRespDTO.builder()
                .result(0)
                .wxMemberCardDTOS(Arrays.asList(WxMemberCardDTO.builder()
                        .memberInfoCardGuid("memberInfoCardGuid")
                        .cardRightDetailsRespDTOS(Arrays.asList(new ResponseCardRight()))
                        .uck(0)
                        .build()))
                .errorMsg("errorMsg")
                .build();

        // Configure WxStoreSessionDetailsService.getMemberInfoAndCardList(...).
        final ResponseMemberAndCardInfoDTO responseMemberAndCardInfoDTO = new ResponseMemberAndCardInfoDTO();
        final ResponseMemberInfoDTO memberInfoDTO = new ResponseMemberInfoDTO();
        memberInfoDTO.setMemberQRCode("memberQRCode");
        memberInfoDTO.setMemberInfoGuid("memberInfoGuid");
        memberInfoDTO.setStateCode(0);
        responseMemberAndCardInfoDTO.setMemberInfoDTO(memberInfoDTO);
        final ResponseMemberCard responseMemberCard = new ResponseMemberCard();
        responseMemberAndCardInfoDTO.setMemberCardListRespDTOs(Arrays.asList(responseMemberCard));
        when(mockWxStoreSessionDetailsService.getMemberInfoAndCardList("enterpriseGuid", "storeGuid",
                "openId")).thenReturn(responseMemberAndCardInfoDTO);

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .memberCardGuid("memberCardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .volumeCode("volumeCode")
                .orderGuid("data")
                .memberInfoGuid("memberInfoGuid")
                .purchaseGroupFee(new BigDecimal("0.00"))
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        // Configure HsaBaseClientService.getCardRightDetails(...).
        final ResponseCardRight responseCardRight = new ResponseCardRight();
        responseCardRight.setName("name");
        final ResponseCardRightDetail responseCardRightDetail = new ResponseCardRightDetail();
        responseCardRightDetail.setRightsGuid("rightsGuid");
        responseCardRightDetail.setRightsName("rightsName");
        responseCardRightDetail.setGainCondition("gainCondition");
        responseCardRight.setList(Arrays.asList(responseCardRightDetail));
        final ResponseModel<List<ResponseCardRight>> listResponseModel = new ResponseModel<>(
                Arrays.asList(responseCardRight));
        final RequestCardRightDetails requestCardRightDetails = new RequestCardRightDetails();
        requestCardRightDetails.setMemberInfoCardGuid("memberInfoCardGuid");
        requestCardRightDetails.setCardGuid("cardGuid");
        requestCardRightDetails.setCardType(0);
        requestCardRightDetails.setCardLevelGuid("cardLevelGuid");
        when(mockHsaBaseClientService.getCardRightDetails(requestCardRightDetails)).thenReturn(listResponseModel);

        // Run the test
        final WxMemberCardRespDTO result = wxStoreTradeOrderServiceImplUnderTest.cardList(wxMemberCardListReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCardList_HsaBaseClientServiceReturnsNoItems() {
        // Setup
        final WxMemberCardListReqDTO wxMemberCardListReqDTO = WxMemberCardListReqDTO.builder()
                .openId("openId")
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .build();
        final WxMemberCardRespDTO expectedResult = WxMemberCardRespDTO.builder()
                .result(0)
                .wxMemberCardDTOS(Arrays.asList(WxMemberCardDTO.builder()
                        .memberInfoCardGuid("memberInfoCardGuid")
                        .cardRightDetailsRespDTOS(Arrays.asList(new ResponseCardRight()))
                        .uck(0)
                        .build()))
                .errorMsg("errorMsg")
                .build();

        // Configure WxStoreSessionDetailsService.getMemberInfoAndCardList(...).
        final ResponseMemberAndCardInfoDTO responseMemberAndCardInfoDTO = new ResponseMemberAndCardInfoDTO();
        final ResponseMemberInfoDTO memberInfoDTO = new ResponseMemberInfoDTO();
        memberInfoDTO.setMemberQRCode("memberQRCode");
        memberInfoDTO.setMemberInfoGuid("memberInfoGuid");
        memberInfoDTO.setStateCode(0);
        responseMemberAndCardInfoDTO.setMemberInfoDTO(memberInfoDTO);
        final ResponseMemberCard responseMemberCard = new ResponseMemberCard();
        responseMemberAndCardInfoDTO.setMemberCardListRespDTOs(Arrays.asList(responseMemberCard));
        when(mockWxStoreSessionDetailsService.getMemberInfoAndCardList("enterpriseGuid", "storeGuid",
                "openId")).thenReturn(responseMemberAndCardInfoDTO);

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .memberCardGuid("memberCardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .volumeCode("volumeCode")
                .orderGuid("data")
                .memberInfoGuid("memberInfoGuid")
                .purchaseGroupFee(new BigDecimal("0.00"))
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        // Configure HsaBaseClientService.getCardRightDetails(...).
        final ResponseModel<List<ResponseCardRight>> listResponseModel = new ResponseModel<>(Collections.emptyList());
        final RequestCardRightDetails requestCardRightDetails = new RequestCardRightDetails();
        requestCardRightDetails.setMemberInfoCardGuid("memberInfoCardGuid");
        requestCardRightDetails.setCardGuid("cardGuid");
        requestCardRightDetails.setCardType(0);
        requestCardRightDetails.setCardLevelGuid("cardLevelGuid");
        when(mockHsaBaseClientService.getCardRightDetails(requestCardRightDetails)).thenReturn(listResponseModel);

        // Run the test
        final WxMemberCardRespDTO result = wxStoreTradeOrderServiceImplUnderTest.cardList(wxMemberCardListReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testVolumeCodeList() {
        // Setup
        final WxVolumeCodeReqDTO wxVolumeCodeReqDTO = WxVolumeCodeReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .openId("openId")
                .build();
        final WxVolumeCodeRespDTO expectedResult = WxVolumeCodeRespDTO.builder()
                .memberVolumeList(Arrays.asList(WxVolumeCodeDTO.builder()
                        .memberVolumeGuid("memberVolumeGuid")
                        .MemberInfoVolumeDetailsRespDTO(new ResponseMemberInfoVolumeDetails())
                        .volumeState(0)
                        .volumeInfoState(0)
                        .uck(0)
                        .volumeCode("volumeCode")
                        .build()))
                .result(0)
                .errorMsg("errorMsg")
                .build();

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .memberCardGuid("memberCardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .volumeCode("volumeCode")
                .orderGuid("data")
                .memberInfoGuid("memberInfoGuid")
                .purchaseGroupFee(new BigDecimal("0.00"))
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        // Configure HsaBaseClientService.getMemberVolume(...).
        final ResponseMemberInfoVolume responseMemberInfoVolume = new ResponseMemberInfoVolume();
        responseMemberInfoVolume.setMayUseVolumeNum(0);
        responseMemberInfoVolume.setNotUseVolumeNum(0);
        final MemberInfoVolume memberInfoVolume = new MemberInfoVolume();
        memberInfoVolume.setMayUseVolume(0);
        memberInfoVolume.setVolumeType(0);
        responseMemberInfoVolume.setMemberVolumeList(Arrays.asList(memberInfoVolume));
        final ResponseModel<ResponseMemberInfoVolume> responseMemberInfoVolumeResponseModel = new ResponseModel<>(
                responseMemberInfoVolume);
        final RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO = new RequestMemberInfoVolumeQuery();
        memberInfoVolumeQueryReqDTO.setMemberInfoGuid("memberInfoGuid");
        memberInfoVolumeQueryReqDTO.setVolumeType(0);
        memberInfoVolumeQueryReqDTO.setStoreGuid("storeGuid");
        memberInfoVolumeQueryReqDTO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVolumeQueryReqDTO.setBrandGuid("brandGuid");
        memberInfoVolumeQueryReqDTO.setMayUseVolume(0);
        memberInfoVolumeQueryReqDTO.setExcludeBind(0);
        when(mockHsaBaseClientService.getMemberVolume(memberInfoVolumeQueryReqDTO))
                .thenReturn(responseMemberInfoVolumeResponseModel);

        // Configure HsaBaseClientService.getMemberVolumeDetails(...).
        final ResponseMemberInfoVolumeDetails responseMemberInfoVolumeDetails = new ResponseMemberInfoVolumeDetails();
        responseMemberInfoVolumeDetails.setMemberVolumeGuid("memberVolumeGuid");
        responseMemberInfoVolumeDetails.setVolumeInfoGuid("volumeInfoGuid");
        responseMemberInfoVolumeDetails.setVolumeInfoName("volumeInfoName");
        responseMemberInfoVolumeDetails.setVolumeSimpleDesc("volumeSimpleDesc");
        responseMemberInfoVolumeDetails.setVolumeStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ResponseModel<ResponseMemberInfoVolumeDetails> responseMemberInfoVolumeDetailsResponseModel = new ResponseModel<>(
                responseMemberInfoVolumeDetails);
        when(mockHsaBaseClientService.getMemberVolumeDetails("memberVolumeGuid"))
                .thenReturn(responseMemberInfoVolumeDetailsResponseModel);

        // Configure HsaBaseClientService.consumeVolumeList(...).
        final ResponseVolumeList responseVolumeList = new ResponseVolumeList();
        responseVolumeList.setVolumeCode("volumeCode");
        responseVolumeList.setVolumeInfoGuid("volumeInfoGuid");
        responseVolumeList.setMemberVolumeGuid("memberVolumeGuid");
        responseVolumeList.setVolumeInfoName("volumeInfoName");
        responseVolumeList.setVolumeStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ResponseModel<List<ResponseVolumeList>> listResponseModel = new ResponseModel<>(
                Arrays.asList(responseVolumeList));
        when(mockHsaBaseClientService.consumeVolumeList("data")).thenReturn(listResponseModel);

        // Configure WxStoreWeChatOrderClientService.checkVolume(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setDeviceId("openId");
        billCalculateReqDTO.setEnterpriseGuid("enterpriseGuid");
        billCalculateReqDTO.setEnterpriseName("enterpriseName");
        billCalculateReqDTO.setStoreGuid("storeGuid");
        billCalculateReqDTO.setOrderGuid("data");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setMemberPhone("openId");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVerify(0);
        when(mockWxStoreWeChatOrderClientService.checkVolume(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO);

        // Run the test
        final WxVolumeCodeRespDTO result = wxStoreTradeOrderServiceImplUnderTest.volumeCodeList(wxVolumeCodeReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testVolumeCodeList_HsaBaseClientServiceConsumeVolumeListReturnsNoItems() {
        // Setup
        final WxVolumeCodeReqDTO wxVolumeCodeReqDTO = WxVolumeCodeReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .openId("openId")
                .build();
        final WxVolumeCodeRespDTO expectedResult = WxVolumeCodeRespDTO.builder()
                .memberVolumeList(Arrays.asList(WxVolumeCodeDTO.builder()
                        .memberVolumeGuid("memberVolumeGuid")
                        .MemberInfoVolumeDetailsRespDTO(new ResponseMemberInfoVolumeDetails())
                        .volumeState(0)
                        .volumeInfoState(0)
                        .uck(0)
                        .volumeCode("volumeCode")
                        .build()))
                .result(0)
                .errorMsg("errorMsg")
                .build();

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .memberCardGuid("memberCardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .volumeCode("volumeCode")
                .orderGuid("data")
                .memberInfoGuid("memberInfoGuid")
                .purchaseGroupFee(new BigDecimal("0.00"))
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        // Configure HsaBaseClientService.getMemberVolume(...).
        final ResponseMemberInfoVolume responseMemberInfoVolume = new ResponseMemberInfoVolume();
        responseMemberInfoVolume.setMayUseVolumeNum(0);
        responseMemberInfoVolume.setNotUseVolumeNum(0);
        final MemberInfoVolume memberInfoVolume = new MemberInfoVolume();
        memberInfoVolume.setMayUseVolume(0);
        memberInfoVolume.setVolumeType(0);
        responseMemberInfoVolume.setMemberVolumeList(Arrays.asList(memberInfoVolume));
        final ResponseModel<ResponseMemberInfoVolume> responseMemberInfoVolumeResponseModel = new ResponseModel<>(
                responseMemberInfoVolume);
        final RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO = new RequestMemberInfoVolumeQuery();
        memberInfoVolumeQueryReqDTO.setMemberInfoGuid("memberInfoGuid");
        memberInfoVolumeQueryReqDTO.setVolumeType(0);
        memberInfoVolumeQueryReqDTO.setStoreGuid("storeGuid");
        memberInfoVolumeQueryReqDTO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVolumeQueryReqDTO.setBrandGuid("brandGuid");
        memberInfoVolumeQueryReqDTO.setMayUseVolume(0);
        memberInfoVolumeQueryReqDTO.setExcludeBind(0);
        when(mockHsaBaseClientService.getMemberVolume(memberInfoVolumeQueryReqDTO))
                .thenReturn(responseMemberInfoVolumeResponseModel);

        // Configure HsaBaseClientService.getMemberVolumeDetails(...).
        final ResponseMemberInfoVolumeDetails responseMemberInfoVolumeDetails = new ResponseMemberInfoVolumeDetails();
        responseMemberInfoVolumeDetails.setMemberVolumeGuid("memberVolumeGuid");
        responseMemberInfoVolumeDetails.setVolumeInfoGuid("volumeInfoGuid");
        responseMemberInfoVolumeDetails.setVolumeInfoName("volumeInfoName");
        responseMemberInfoVolumeDetails.setVolumeSimpleDesc("volumeSimpleDesc");
        responseMemberInfoVolumeDetails.setVolumeStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ResponseModel<ResponseMemberInfoVolumeDetails> responseMemberInfoVolumeDetailsResponseModel = new ResponseModel<>(
                responseMemberInfoVolumeDetails);
        when(mockHsaBaseClientService.getMemberVolumeDetails("memberVolumeGuid"))
                .thenReturn(responseMemberInfoVolumeDetailsResponseModel);

        // Configure HsaBaseClientService.consumeVolumeList(...).
        final ResponseModel<List<ResponseVolumeList>> listResponseModel = new ResponseModel<>(Collections.emptyList());
        when(mockHsaBaseClientService.consumeVolumeList("data")).thenReturn(listResponseModel);

        // Configure WxStoreWeChatOrderClientService.checkVolume(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setDeviceId("openId");
        billCalculateReqDTO.setEnterpriseGuid("enterpriseGuid");
        billCalculateReqDTO.setEnterpriseName("enterpriseName");
        billCalculateReqDTO.setStoreGuid("storeGuid");
        billCalculateReqDTO.setOrderGuid("data");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setMemberPhone("openId");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVerify(0);
        when(mockWxStoreWeChatOrderClientService.checkVolume(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO);

        // Run the test
        final WxVolumeCodeRespDTO result = wxStoreTradeOrderServiceImplUnderTest.volumeCodeList(wxVolumeCodeReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testVolumeCodeDetails() {
        // Setup
        final WxVolumeCodeDetailsReqDTO wxVolumeCodeDetailsReqDTO = WxVolumeCodeDetailsReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .storeGuid("storeGuid")
                .memberVolumeGuid("memberVolumeGuid")
                .build();
        final ResponseMemberInfoVolumeDetails expectedResult = new ResponseMemberInfoVolumeDetails();
        expectedResult.setMemberVolumeGuid("memberVolumeGuid");
        expectedResult.setVolumeInfoGuid("volumeInfoGuid");
        expectedResult.setVolumeInfoName("volumeInfoName");
        expectedResult.setVolumeSimpleDesc("volumeSimpleDesc");
        expectedResult.setVolumeStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure HsaBaseClientService.getMemberVolumeDetails(...).
        final ResponseMemberInfoVolumeDetails responseMemberInfoVolumeDetails = new ResponseMemberInfoVolumeDetails();
        responseMemberInfoVolumeDetails.setMemberVolumeGuid("memberVolumeGuid");
        responseMemberInfoVolumeDetails.setVolumeInfoGuid("volumeInfoGuid");
        responseMemberInfoVolumeDetails.setVolumeInfoName("volumeInfoName");
        responseMemberInfoVolumeDetails.setVolumeSimpleDesc("volumeSimpleDesc");
        responseMemberInfoVolumeDetails.setVolumeStartDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ResponseModel<ResponseMemberInfoVolumeDetails> responseMemberInfoVolumeDetailsResponseModel = new ResponseModel<>(
                responseMemberInfoVolumeDetails);
        when(mockHsaBaseClientService.getMemberVolumeDetails("memberVolumeGuid"))
                .thenReturn(responseMemberInfoVolumeDetailsResponseModel);

        // Run the test
        final ResponseMemberInfoVolumeDetails result = wxStoreTradeOrderServiceImplUnderTest.volumeCodeDetails(
                wxVolumeCodeDetailsReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testOrderDetails() {
        // Setup
        final WxPaidOrderDetailsReqDTO wxPaidOrderDetailsReqDTO = WxPaidOrderDetailsReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .openId("openId")
                .orderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .build();

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "42dfba7f-1212-4e7d-8d5c-5754838c1735", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        // Configure WxOrderRecordService.getOrderRecord(...).
        final WxOrderRecordDTO wxOrderRecordDTO = WxOrderRecordDTO.builder()
                .brandGuid("brandGuid")
                .tableGuid("tableGuid")
                .build();
        when(mockWxOrderRecordService.getOrderRecord("574e403c-d09c-49b4-98fc-953a183a6fb6"))
                .thenReturn(wxOrderRecordDTO);

        // Configure WxStoreTableClientService.getTableByGuid(...).
        final TableDTO tableDTO = TableDTO.builder()
                .storeGuid("storeGuid")
                .storeName("storeName")
                .areaName("areaName")
                .code("code")
                .build();
        when(mockWxStoreTableClientService.getTableByGuid("tableGuid")).thenReturn(tableDTO);

        // Configure WxStoreOrderPayService.orderPay(...).
        final WxStoreTradeOrderDetailsRespDTO wxStoreTradeOrderDetailsRespDTO = WxStoreTradeOrderDetailsRespDTO.builder()
                .combine(0)
                .wxStoreTradeDetailsGroupDTOS(Arrays.asList(WxStoreTradeDetailsGroupDTO.builder()
                        .tableGuid("tableGuid")
                        .tableCode("tableCode")
                        .areaName("areaName")
                        .mainTable(0)
                        .wxStoreTradeOrderDetailsDTOS(Arrays.asList(new WxStoreTradeOrderDetailsDTO()))
                        .build()))
                .orderNo("orderNo")
                .tableCode("code")
                .tradeMode(0)
                .totalPrice(new BigDecimal("0.00"))
                .orderFeeDetailDTOS(Arrays.asList(new OrderFeeDetailDTO()))
                .actuallyPayFeeDetailDTOS(Arrays.asList(new ActuallyPayFeeDetailDTO()))
                .discountFeeDetailDTOS(Arrays.asList(new DiscountFeeDetailDTO()))
                .gmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .checkoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .guestCount(0)
                .orderState(0)
                .areaName("areaName")
                .storeName("storeName")
                .brandName("brandName")
                .payWay("payWay")
                .errorMsg("errorMsg")
                .build();
        when(mockWxStoreOrderPayService.orderPay(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(wxStoreTradeOrderDetailsRespDTO);

        // Configure WxStorePersonOrderDetailsService.getPersonOrderDetails(...).
        final WxStoreTradeOrderDetailsRespDTO wxStoreTradeOrderDetailsRespDTO1 = WxStoreTradeOrderDetailsRespDTO.builder()
                .combine(0)
                .wxStoreTradeDetailsGroupDTOS(Arrays.asList(WxStoreTradeDetailsGroupDTO.builder()
                        .tableGuid("tableGuid")
                        .tableCode("tableCode")
                        .areaName("areaName")
                        .mainTable(0)
                        .wxStoreTradeOrderDetailsDTOS(Arrays.asList(new WxStoreTradeOrderDetailsDTO()))
                        .build()))
                .orderNo("orderNo")
                .tableCode("code")
                .tradeMode(0)
                .totalPrice(new BigDecimal("0.00"))
                .orderFeeDetailDTOS(Arrays.asList(new OrderFeeDetailDTO()))
                .actuallyPayFeeDetailDTOS(Arrays.asList(new ActuallyPayFeeDetailDTO()))
                .discountFeeDetailDTOS(Arrays.asList(new DiscountFeeDetailDTO()))
                .gmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .checkoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .guestCount(0)
                .orderState(0)
                .areaName("areaName")
                .storeName("storeName")
                .brandName("brandName")
                .payWay("payWay")
                .errorMsg("errorMsg")
                .build();
        when(mockWxStorePersonOrderDetailsService.getPersonOrderDetails(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(wxStoreTradeOrderDetailsRespDTO1);

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(false);
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Run the test
        final WebSocketMessageDTO result = wxStoreTradeOrderServiceImplUnderTest.orderDetails(wxPaidOrderDetailsReqDTO);

        // Verify the results
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
    }

    @Test
    public void testOrderDetails_WxStoreMenuDetailsServiceJudgeOrderType1ReturnsTrue() {
        // Setup
        final WxPaidOrderDetailsReqDTO wxPaidOrderDetailsReqDTO = WxPaidOrderDetailsReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .openId("openId")
                .orderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .build();

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "42dfba7f-1212-4e7d-8d5c-5754838c1735", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        // Configure WxOrderRecordService.getOrderRecord(...).
        final WxOrderRecordDTO wxOrderRecordDTO = WxOrderRecordDTO.builder()
                .brandGuid("brandGuid")
                .tableGuid("tableGuid")
                .build();
        when(mockWxOrderRecordService.getOrderRecord("574e403c-d09c-49b4-98fc-953a183a6fb6"))
                .thenReturn(wxOrderRecordDTO);

        // Configure WxStoreTableClientService.getTableByGuid(...).
        final TableDTO tableDTO = TableDTO.builder()
                .storeGuid("storeGuid")
                .storeName("storeName")
                .areaName("areaName")
                .code("code")
                .build();
        when(mockWxStoreTableClientService.getTableByGuid("tableGuid")).thenReturn(tableDTO);

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(true);
        when(mockWxStoreSessionDetailsService.getOrderState("tableGuid")).thenReturn(0);
        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("tableGuid")).thenReturn("result");
        when(mockRedisUtils.generatdDTOGuid(WxStoreMerchantOperationDTO.class)).thenReturn("merchatBatchGuid");
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn("result");
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(false);
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreMerchantOrderService.getPendingOrders(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setNickName("nickName");
        wxStoreMerchantOrderDTO.setDiningTableGuid("tableGuid");
        wxStoreMerchantOrderDTO.setTableCode("code");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setItemCount(0);
        wxStoreMerchantOrderDTO.setActualGuestsNo(0);
        wxStoreMerchantOrderDTO.setTradeGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        wxStoreMerchantOrderDTO.setRemark("remark");
        wxStoreMerchantOrderDTO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDTO.setAreaName("areaName");
        wxStoreMerchantOrderDTO.setOperationName("nickName");
        wxStoreMerchantOrderDTO.setHeadImgUrl("headImgUrl");
        wxStoreMerchantOrderDTO.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDTO.setCombine("data");
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS = Arrays.asList(wxStoreMerchantOrderDTO);
        when(mockWxStoreMerchantOrderService.getPendingOrders(WxStorePendingOrdersQuery.builder()
                .orderStates(Arrays.asList(0))
                .orderGuid("orderGuid")
                .combine("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .build())).thenReturn(wxStoreMerchantOrderDTOS);

        // Configure WxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem(...).
        final WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItemDTO = new WxStoreMerchantDineInItemDTO();
        wxStoreMerchantDineInItemDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        wxStoreMerchantDineInItemDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantDineInItemDTO.setItemCount(0);
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        wxStoreMerchantDineInItemDTO.setOrderFeeDetailDTOS(Arrays.asList(orderFeeDetailDTO1));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        wxStoreMerchantDineInItemDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("volumeName");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        wxStoreMerchantDineInItemDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        wxStoreMerchantDineInItemDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        when(mockWxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem("batchId"))
                .thenReturn(wxStoreMerchantDineInItemDTO);

        // Configure WxStoreMerchantOrderService.getOneByGuid(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("guid");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setOrderGuid("batchId");
        wxStoreMerchantOrderDO.setHeadImgUrl("headImgUrl");
        when(mockWxStoreMerchantOrderService.getOneByGuid("orderGuid")).thenReturn(wxStoreMerchantOrderDO);

        // Run the test
        final WebSocketMessageDTO result = wxStoreTradeOrderServiceImplUnderTest.orderDetails(wxPaidOrderDetailsReqDTO);

        // Verify the results
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
        verify(mockWxStoreSessionDetailsService).updateMerchantBatchGuid("tableGuid", "merchatBatchGuid");
        verify(mockWxStoreSessionDetailsService).saveOrderGuid("tableGuid", "data");
        verify(mockWxStoreSessionDetailsService).saveOrderState("tableGuid", 0);
    }

    @Test
    public void testOrderDetails_WxStoreTableClientServiceGetOrderGuidReturnsNull() {
        // Setup
        final WxPaidOrderDetailsReqDTO wxPaidOrderDetailsReqDTO = WxPaidOrderDetailsReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .openId("openId")
                .orderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .build();

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "42dfba7f-1212-4e7d-8d5c-5754838c1735", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        // Configure WxOrderRecordService.getOrderRecord(...).
        final WxOrderRecordDTO wxOrderRecordDTO = WxOrderRecordDTO.builder()
                .brandGuid("brandGuid")
                .tableGuid("tableGuid")
                .build();
        when(mockWxOrderRecordService.getOrderRecord("574e403c-d09c-49b4-98fc-953a183a6fb6"))
                .thenReturn(wxOrderRecordDTO);

        // Configure WxStoreTableClientService.getTableByGuid(...).
        final TableDTO tableDTO = TableDTO.builder()
                .storeGuid("storeGuid")
                .storeName("storeName")
                .areaName("areaName")
                .code("code")
                .build();
        when(mockWxStoreTableClientService.getTableByGuid("tableGuid")).thenReturn(tableDTO);

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(true);
        when(mockWxStoreSessionDetailsService.getOrderState("tableGuid")).thenReturn(0);
        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("tableGuid")).thenReturn("result");
        when(mockRedisUtils.generatdDTOGuid(WxStoreMerchantOperationDTO.class)).thenReturn("merchatBatchGuid");
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn(null);

        // Configure WxStorePersonOrderDetailsService.getPersonOrderDetails(...).
        final WxStoreTradeOrderDetailsRespDTO wxStoreTradeOrderDetailsRespDTO = WxStoreTradeOrderDetailsRespDTO.builder()
                .combine(0)
                .wxStoreTradeDetailsGroupDTOS(Arrays.asList(WxStoreTradeDetailsGroupDTO.builder()
                        .tableGuid("tableGuid")
                        .tableCode("tableCode")
                        .areaName("areaName")
                        .mainTable(0)
                        .wxStoreTradeOrderDetailsDTOS(Arrays.asList(new WxStoreTradeOrderDetailsDTO()))
                        .build()))
                .orderNo("orderNo")
                .tableCode("code")
                .tradeMode(0)
                .totalPrice(new BigDecimal("0.00"))
                .orderFeeDetailDTOS(Arrays.asList(new OrderFeeDetailDTO()))
                .actuallyPayFeeDetailDTOS(Arrays.asList(new ActuallyPayFeeDetailDTO()))
                .discountFeeDetailDTOS(Arrays.asList(new DiscountFeeDetailDTO()))
                .gmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .checkoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .guestCount(0)
                .orderState(0)
                .areaName("areaName")
                .storeName("storeName")
                .brandName("brandName")
                .payWay("payWay")
                .errorMsg("errorMsg")
                .build();
        when(mockWxStorePersonOrderDetailsService.getPersonOrderDetails(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(wxStoreTradeOrderDetailsRespDTO);

        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(false);
        when(mockWxStoreSessionDetailsService.getOrderGuid("tableGuid")).thenReturn("data");
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreMerchantOrderService.getPendingOrders(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setNickName("nickName");
        wxStoreMerchantOrderDTO.setDiningTableGuid("tableGuid");
        wxStoreMerchantOrderDTO.setTableCode("code");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setItemCount(0);
        wxStoreMerchantOrderDTO.setActualGuestsNo(0);
        wxStoreMerchantOrderDTO.setTradeGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        wxStoreMerchantOrderDTO.setRemark("remark");
        wxStoreMerchantOrderDTO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDTO.setAreaName("areaName");
        wxStoreMerchantOrderDTO.setOperationName("nickName");
        wxStoreMerchantOrderDTO.setHeadImgUrl("headImgUrl");
        wxStoreMerchantOrderDTO.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDTO.setCombine("data");
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS = Arrays.asList(wxStoreMerchantOrderDTO);
        when(mockWxStoreMerchantOrderService.getPendingOrders(WxStorePendingOrdersQuery.builder()
                .orderStates(Arrays.asList(0))
                .orderGuid("orderGuid")
                .combine("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .build())).thenReturn(wxStoreMerchantOrderDTOS);

        // Configure WxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem(...).
        final WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItemDTO = new WxStoreMerchantDineInItemDTO();
        wxStoreMerchantDineInItemDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        wxStoreMerchantDineInItemDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantDineInItemDTO.setItemCount(0);
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        wxStoreMerchantDineInItemDTO.setOrderFeeDetailDTOS(Arrays.asList(orderFeeDetailDTO1));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        wxStoreMerchantDineInItemDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("volumeName");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        wxStoreMerchantDineInItemDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        wxStoreMerchantDineInItemDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        when(mockWxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem("batchId"))
                .thenReturn(wxStoreMerchantDineInItemDTO);

        when(mockWxStoreSessionDetailsService.getDinnerGuestsCount("tableGuid")).thenReturn(0);

        // Configure WxStoreSessionDetailsService.getTablePaidUser(...).
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .sex(0)
                .enterpriseGuid("enterpriseGuid")
                .enterpriseName("enterpriseName")
                .storeGuid("storeGuid")
                .storeName("storeName")
                .diningTableGuid("tableGuid")
                .tableCode("code")
                .diningTableName("code")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .brandName("brandName")
                .brandGuid("brandGuid")
                .build();
        when(mockWxStoreSessionDetailsService.getTablePaidUser("tableGuid")).thenReturn(wxStoreConsumerDTO);

        // Run the test
        final WebSocketMessageDTO result = wxStoreTradeOrderServiceImplUnderTest.orderDetails(wxPaidOrderDetailsReqDTO);

        // Verify the results
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
        verify(mockWxStoreSessionDetailsService).updateMerchantBatchGuid("tableGuid", "merchatBatchGuid");
        verify(mockWxStoreSessionDetailsService).saveOrderGuid("tableGuid", "data");
    }

    @Test
    public void testOrderDetails_WxStoreMenuDetailsServiceJudgeOrderType2ReturnsTrue() {
        // Setup
        final WxPaidOrderDetailsReqDTO wxPaidOrderDetailsReqDTO = WxPaidOrderDetailsReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .openId("openId")
                .orderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .build();

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "42dfba7f-1212-4e7d-8d5c-5754838c1735", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        // Configure WxOrderRecordService.getOrderRecord(...).
        final WxOrderRecordDTO wxOrderRecordDTO = WxOrderRecordDTO.builder()
                .brandGuid("brandGuid")
                .tableGuid("tableGuid")
                .build();
        when(mockWxOrderRecordService.getOrderRecord("574e403c-d09c-49b4-98fc-953a183a6fb6"))
                .thenReturn(wxOrderRecordDTO);

        // Configure WxStoreTableClientService.getTableByGuid(...).
        final TableDTO tableDTO = TableDTO.builder()
                .storeGuid("storeGuid")
                .storeName("storeName")
                .areaName("areaName")
                .code("code")
                .build();
        when(mockWxStoreTableClientService.getTableByGuid("tableGuid")).thenReturn(tableDTO);

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(true);
        when(mockWxStoreSessionDetailsService.getOrderState("tableGuid")).thenReturn(0);
        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("tableGuid")).thenReturn("result");
        when(mockRedisUtils.generatdDTOGuid(WxStoreMerchantOperationDTO.class)).thenReturn("merchatBatchGuid");
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn("result");
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(true);
        when(mockWxStoreSessionDetailsService.getOrderGuid("tableGuid")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreMerchantOrderService.getPendingOrders(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        wxStoreMerchantOrderDTO.setNickName("nickName");
        wxStoreMerchantOrderDTO.setDiningTableGuid("tableGuid");
        wxStoreMerchantOrderDTO.setTableCode("code");
        wxStoreMerchantOrderDTO.setStoreGuid("storeGuid");
        wxStoreMerchantOrderDTO.setOrderState(0);
        wxStoreMerchantOrderDTO.setTradeMode(0);
        wxStoreMerchantOrderDTO.setItemCount(0);
        wxStoreMerchantOrderDTO.setActualGuestsNo(0);
        wxStoreMerchantOrderDTO.setTradeGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        wxStoreMerchantOrderDTO.setRemark("remark");
        wxStoreMerchantOrderDTO.setAreaGuid("areaGuid");
        wxStoreMerchantOrderDTO.setAreaName("areaName");
        wxStoreMerchantOrderDTO.setOperationName("nickName");
        wxStoreMerchantOrderDTO.setHeadImgUrl("headImgUrl");
        wxStoreMerchantOrderDTO.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDTO.setCombine("data");
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS = Arrays.asList(wxStoreMerchantOrderDTO);
        when(mockWxStoreMerchantOrderService.getPendingOrders(WxStorePendingOrdersQuery.builder()
                .orderStates(Arrays.asList(0))
                .orderGuid("orderGuid")
                .combine("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .build())).thenReturn(wxStoreMerchantOrderDTOS);

        // Configure WxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem(...).
        final WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItemDTO = new WxStoreMerchantDineInItemDTO();
        wxStoreMerchantDineInItemDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        wxStoreMerchantDineInItemDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantDineInItemDTO.setItemCount(0);
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        wxStoreMerchantDineInItemDTO.setOrderFeeDetailDTOS(Arrays.asList(orderFeeDetailDTO1));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        wxStoreMerchantDineInItemDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("volumeName");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        wxStoreMerchantDineInItemDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        wxStoreMerchantDineInItemDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        when(mockWxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem("batchId"))
                .thenReturn(wxStoreMerchantDineInItemDTO);

        // Configure WxStoreMerchantOrderService.getOneByGuid(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("guid");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setOrderGuid("batchId");
        wxStoreMerchantOrderDO.setHeadImgUrl("headImgUrl");
        when(mockWxStoreMerchantOrderService.getOneByGuid("orderGuid")).thenReturn(wxStoreMerchantOrderDO);

        // Run the test
        final WebSocketMessageDTO result = wxStoreTradeOrderServiceImplUnderTest.orderDetails(wxPaidOrderDetailsReqDTO);

        // Verify the results
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
        verify(mockWxStoreSessionDetailsService).updateMerchantBatchGuid("tableGuid", "merchatBatchGuid");
        verify(mockWxStoreSessionDetailsService).saveOrderGuid("tableGuid", "data");
        verify(mockWxStoreSessionDetailsService).saveOrderState("tableGuid", 0);
    }

    @Test
    public void testOrderDetails_WxStoreMerchantOrderServiceGetPendingOrdersReturnsNull() {
        // Setup
        final WxPaidOrderDetailsReqDTO wxPaidOrderDetailsReqDTO = WxPaidOrderDetailsReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .openId("openId")
                .orderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .build();

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "42dfba7f-1212-4e7d-8d5c-5754838c1735", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        // Configure WxOrderRecordService.getOrderRecord(...).
        final WxOrderRecordDTO wxOrderRecordDTO = WxOrderRecordDTO.builder()
                .brandGuid("brandGuid")
                .tableGuid("tableGuid")
                .build();
        when(mockWxOrderRecordService.getOrderRecord("574e403c-d09c-49b4-98fc-953a183a6fb6"))
                .thenReturn(wxOrderRecordDTO);

        // Configure WxStoreTableClientService.getTableByGuid(...).
        final TableDTO tableDTO = TableDTO.builder()
                .storeGuid("storeGuid")
                .storeName("storeName")
                .areaName("areaName")
                .code("code")
                .build();
        when(mockWxStoreTableClientService.getTableByGuid("tableGuid")).thenReturn(tableDTO);

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(true);
        when(mockWxStoreSessionDetailsService.getOrderState("tableGuid")).thenReturn(0);
        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("tableGuid")).thenReturn("result");
        when(mockRedisUtils.generatdDTOGuid(WxStoreMerchantOperationDTO.class)).thenReturn("merchatBatchGuid");
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn("result");
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(false);
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        when(mockWxStoreMerchantOrderService.getPendingOrders(WxStorePendingOrdersQuery.builder()
                .orderStates(Arrays.asList(0))
                .orderGuid("orderGuid")
                .combine("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .build())).thenReturn(null);

        // Configure WxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem(...).
        final WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItemDTO = new WxStoreMerchantDineInItemDTO();
        wxStoreMerchantDineInItemDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        wxStoreMerchantDineInItemDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantDineInItemDTO.setItemCount(0);
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        wxStoreMerchantDineInItemDTO.setOrderFeeDetailDTOS(Arrays.asList(orderFeeDetailDTO1));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        wxStoreMerchantDineInItemDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("volumeName");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        wxStoreMerchantDineInItemDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        wxStoreMerchantDineInItemDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        when(mockWxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem("batchId"))
                .thenReturn(wxStoreMerchantDineInItemDTO);

        // Configure WxStoreMerchantOrderService.getOneByGuid(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("guid");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setOrderGuid("batchId");
        wxStoreMerchantOrderDO.setHeadImgUrl("headImgUrl");
        when(mockWxStoreMerchantOrderService.getOneByGuid("orderGuid")).thenReturn(wxStoreMerchantOrderDO);

        // Run the test
        final WebSocketMessageDTO result = wxStoreTradeOrderServiceImplUnderTest.orderDetails(wxPaidOrderDetailsReqDTO);

        // Verify the results
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
        verify(mockWxStoreSessionDetailsService).updateMerchantBatchGuid("tableGuid", "merchatBatchGuid");
        verify(mockWxStoreSessionDetailsService).saveOrderGuid("tableGuid", "data");
        verify(mockWxStoreSessionDetailsService).saveOrderState("tableGuid", 0);
    }

    @Test
    public void testOrderDetails_WxStoreMerchantOrderServiceGetPendingOrdersReturnsNoItems() {
        // Setup
        final WxPaidOrderDetailsReqDTO wxPaidOrderDetailsReqDTO = WxPaidOrderDetailsReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .openId("openId")
                .orderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .build();

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "42dfba7f-1212-4e7d-8d5c-5754838c1735", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        // Configure WxOrderRecordService.getOrderRecord(...).
        final WxOrderRecordDTO wxOrderRecordDTO = WxOrderRecordDTO.builder()
                .brandGuid("brandGuid")
                .tableGuid("tableGuid")
                .build();
        when(mockWxOrderRecordService.getOrderRecord("574e403c-d09c-49b4-98fc-953a183a6fb6"))
                .thenReturn(wxOrderRecordDTO);

        // Configure WxStoreTableClientService.getTableByGuid(...).
        final TableDTO tableDTO = TableDTO.builder()
                .storeGuid("storeGuid")
                .storeName("storeName")
                .areaName("areaName")
                .code("code")
                .build();
        when(mockWxStoreTableClientService.getTableByGuid("tableGuid")).thenReturn(tableDTO);

        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .sex(0)
                        .enterpriseGuid("enterpriseGuid")
                        .enterpriseName("enterpriseName")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .diningTableGuid("tableGuid")
                        .tableCode("code")
                        .diningTableName("code")
                        .areaGuid("areaGuid")
                        .areaName("areaName")
                        .brandName("brandName")
                        .brandGuid("brandGuid")
                        .build())
                .userCount(0)
                .orderRemark("orderRemark")
                .pageNum(0)
                .build())).thenReturn(true);
        when(mockWxStoreSessionDetailsService.getOrderState("tableGuid")).thenReturn(0);
        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("tableGuid")).thenReturn("result");
        when(mockRedisUtils.generatdDTOGuid(WxStoreMerchantOperationDTO.class)).thenReturn("merchatBatchGuid");
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn("result");
        when(mockWxStoreMenuDetailsService.judgeOrderType("storeGuid")).thenReturn(false);
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId")).thenReturn("data");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("574e403c-d09c-49b4-98fc-953a183a6fb6");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderNo("mainOrderNo");
        dineinOrderDetailRespDTO.setMainOrderGuid("data");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountName("volumeName");
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("tableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setUserWxPublicOpenId("openId");
        dineInItemDTO.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO.setItemType(0);
        dineInItemDTO.setItemState(0);
        dineInItemDTO.setSkuGuid("skuGuid");
        dineInItemDTO.setPrice(new BigDecimal("0.00"));
        dineInItemDTO.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO.setIsPay(0);
        dineInItemDTO.setIsMemberDiscount(0);
        dineInItemDTO.setIsWholeDiscount(0);
        dineInItemDTO.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO = new FreeItemDTO();
        dineInItemDTO.setFreeItemDTOS(Arrays.asList(freeItemDTO));
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        when(mockWxStoreMerchantOrderService.getPendingOrders(WxStorePendingOrdersQuery.builder()
                .orderStates(Arrays.asList(0))
                .orderGuid("orderGuid")
                .combine("574e403c-d09c-49b4-98fc-953a183a6fb6")
                .build())).thenReturn(Collections.emptyList());

        // Configure WxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem(...).
        final WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItemDTO = new WxStoreMerchantDineInItemDTO();
        wxStoreMerchantDineInItemDTO.setGuid("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setUserWxPublicOpenId("openId");
        dineInItemDTO1.setWxBatch("4d2940e6-b710-43a7-88cb-7a0ab22f7d7c");
        dineInItemDTO1.setItemType(0);
        dineInItemDTO1.setItemState(0);
        dineInItemDTO1.setSkuGuid("skuGuid");
        dineInItemDTO1.setPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setItemPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setCurrentCount(new BigDecimal("0.00"));
        dineInItemDTO1.setFreeCount(new BigDecimal("0.00"));
        dineInItemDTO1.setReturnCount(new BigDecimal("0.00"));
        dineInItemDTO1.setIsPay(0);
        dineInItemDTO1.setIsMemberDiscount(0);
        dineInItemDTO1.setIsWholeDiscount(0);
        dineInItemDTO1.setOriginalPrice(new BigDecimal("0.00"));
        dineInItemDTO1.setMemberDiscount(new BigDecimal("0.00"));
        final FreeItemDTO freeItemDTO1 = new FreeItemDTO();
        dineInItemDTO1.setFreeItemDTOS(Arrays.asList(freeItemDTO1));
        wxStoreMerchantDineInItemDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        wxStoreMerchantDineInItemDTO.setItemCount(0);
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        wxStoreMerchantDineInItemDTO.setOrderFeeDetailDTOS(Arrays.asList(orderFeeDetailDTO1));
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        wxStoreMerchantDineInItemDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountName("volumeName");
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        wxStoreMerchantDineInItemDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        wxStoreMerchantDineInItemDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        when(mockWxStoreMerchantDineInItemService.getWxStoreMerchantDineInItem("batchId"))
                .thenReturn(wxStoreMerchantDineInItemDTO);

        // Configure WxStoreMerchantOrderService.getOneByGuid(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setGuid("guid");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setNickName("nickName");
        wxStoreMerchantOrderDO.setOrderGuid("batchId");
        wxStoreMerchantOrderDO.setHeadImgUrl("headImgUrl");
        when(mockWxStoreMerchantOrderService.getOneByGuid("orderGuid")).thenReturn(wxStoreMerchantOrderDO);

        // Run the test
        final WebSocketMessageDTO result = wxStoreTradeOrderServiceImplUnderTest.orderDetails(wxPaidOrderDetailsReqDTO);

        // Verify the results
        verify(mockWxStoreSessionDetailsService).delPrepay("storeGuid", "openId");
        verify(mockWxStoreSessionDetailsService).updateMerchantBatchGuid("tableGuid", "merchatBatchGuid");
        verify(mockWxStoreSessionDetailsService).saveOrderGuid("tableGuid", "data");
        verify(mockWxStoreSessionDetailsService).saveOrderState("tableGuid", 0);
    }

    @Test
    public void testRemoveFastGuestCount() {
        // Setup
        // Run the test
        wxStoreTradeOrderServiceImplUnderTest.removeFastGuestCount("diningTableGuid", "openId");

        // Verify the results
        verify(mockRedisUtils).delete("key");
    }

    @Test
    public void testTransformSurchargeCache() {
        // Setup
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        wxStoreTradeOrderServiceImplUnderTest.transformSurchargeCache("tableGuid", "orderGuid");

        // Verify the results
        verify(mockRedisUtils).setNx("key", "value", 0L);
    }

    @Test
    public void testFilterTimeLimitTableSurchargeList() {
        // Setup
        final List<SurchargeLinkDTO> surchargeLinkList = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        final List<SurchargeLinkDTO> expectedResult = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        when(mockRedisUtils.hasKey("key")).thenReturn(false);

        // Run the test
        final List<SurchargeLinkDTO> result = wxStoreTradeOrderServiceImplUnderTest.filterTimeLimitTableSurchargeList(
                "tableGuid", surchargeLinkList);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testFilterTimeLimitTableSurchargeList_RedisUtilsReturnsTrue() {
        // Setup
        final List<SurchargeLinkDTO> surchargeLinkList = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        when(mockRedisUtils.hasKey("key")).thenReturn(true);

        // Run the test
        final List<SurchargeLinkDTO> result = wxStoreTradeOrderServiceImplUnderTest.filterTimeLimitTableSurchargeList(
                "tableGuid", surchargeLinkList);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQuerySurchargeList() {
        // Setup
        final List<SurchargeLinkDTO> expectedResult = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));

        // Configure BusinessClientService.listSurchargeByCondition(...).
        final List<SurchargeLinkDTO> surchargeLinkDTOS = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        final SurchargeConditionQuery query = new SurchargeConditionQuery();
        query.setStoreGuid("storeGuid");
        query.setType(0);
        query.setTradeMode(0);
        query.setAreaGuid("areaGuid");
        when(mockBusinessClientService.listSurchargeByCondition(query)).thenReturn(surchargeLinkDTOS);

        when(mockRedisUtils.hasKey("key")).thenReturn(false);

        // Run the test
        final List<SurchargeLinkDTO> result = wxStoreTradeOrderServiceImplUnderTest.querySurchargeList(0, "areaGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQuerySurchargeList_BusinessClientServiceReturnsNoItems() {
        // Setup
        final List<SurchargeLinkDTO> expectedResult = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));

        // Configure BusinessClientService.listSurchargeByCondition(...).
        final SurchargeConditionQuery query = new SurchargeConditionQuery();
        query.setStoreGuid("storeGuid");
        query.setType(0);
        query.setTradeMode(0);
        query.setAreaGuid("areaGuid");
        when(mockBusinessClientService.listSurchargeByCondition(query)).thenReturn(Collections.emptyList());

        when(mockRedisUtils.hasKey("key")).thenReturn(false);

        // Run the test
        final List<SurchargeLinkDTO> result = wxStoreTradeOrderServiceImplUnderTest.querySurchargeList(0, "areaGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQuerySurchargeList_RedisUtilsReturnsTrue() {
        // Setup
        // Configure BusinessClientService.listSurchargeByCondition(...).
        final List<SurchargeLinkDTO> surchargeLinkDTOS = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));
        final SurchargeConditionQuery query = new SurchargeConditionQuery();
        query.setStoreGuid("storeGuid");
        query.setType(0);
        query.setTradeMode(0);
        query.setAreaGuid("areaGuid");
        when(mockBusinessClientService.listSurchargeByCondition(query)).thenReturn(surchargeLinkDTOS);

        when(mockRedisUtils.hasKey("key")).thenReturn(true);

        // Run the test
        final List<SurchargeLinkDTO> result = wxStoreTradeOrderServiceImplUnderTest.querySurchargeList(0, "areaGuid");

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testSetOrderSurchargeCache() {
        // Setup
        final List<SurchargeLinkDTO> surchargeLinkList = Arrays.asList(
                new SurchargeLinkDTO("surchargeGuid", "orderGuid", "areaGuid", "name", new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), "tradeMode", 0));

        // Run the test
        wxStoreTradeOrderServiceImplUnderTest.setOrderSurchargeCache("orderGuid", surchargeLinkList);

        // Verify the results
        verify(mockRedisUtils).setEx("key", "value", 30L, TimeUnit.MINUTES);
    }
}
