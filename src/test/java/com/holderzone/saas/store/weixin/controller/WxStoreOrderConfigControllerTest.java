package com.holderzone.saas.store.weixin.controller;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.weixin.req.WxOrderConfigUpdateBatchReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.weixin.service.WxStoreOrderConfigService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxStoreOrderConfigController.class)
public class WxStoreOrderConfigControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStoreOrderConfigService mockWxStoreOrderConfigService;

    @Test
    public void testGetOrderConfigList() throws Exception {
        // Setup
        when(mockWxStoreOrderConfigService.pageOrderConfig(WxStorePageReqDTO.builder()
                .storeGuid("storeGuid")
                .storeName("storeName")
                .build())).thenReturn(new Page<>(0L, 0L, Arrays.asList()));

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_config/list_order_config")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetDetailConfig() throws Exception {
        // Setup
        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("bd501ac0-5a36-4ef5-a054-dcb70107293f");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder().build()))
                .thenReturn(wxOrderConfigDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_config/get_detail_config")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testCreateStoreConfig() throws Exception {
        // Setup
        when(mockWxStoreOrderConfigService.saveStoreConfig(WxStoreReqDTO.builder().build())).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_config/create_store_config")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testCreateStoreConfig_WxStoreOrderConfigServiceReturnsTrue() throws Exception {
        // Setup
        when(mockWxStoreOrderConfigService.saveStoreConfig(WxStoreReqDTO.builder().build())).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_config/create_store_config")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateStoreConfig() throws Exception {
        // Setup
        // Configure WxStoreOrderConfigService.updateStoreConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("bd501ac0-5a36-4ef5-a054-dcb70107293f");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreOrderConfigService.updateStoreConfig(wxOrderConfigDTO)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_config/update_store_config")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateStoreConfig_WxStoreOrderConfigServiceReturnsTrue() throws Exception {
        // Setup
        // Configure WxStoreOrderConfigService.updateStoreConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("bd501ac0-5a36-4ef5-a054-dcb70107293f");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreOrderConfigService.updateStoreConfig(wxOrderConfigDTO)).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_config/update_store_config")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetPadBackgroundUrl() throws Exception {
        // Setup
        when(mockWxStoreOrderConfigService.getPadBackgroundUrl("storeGuid")).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_config/get_pad_background_url")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateStoreConfigBatch() throws Exception {
        // Setup
        // Configure WxStoreOrderConfigService.updateBatchStoreConfig(...).
        final WxOrderConfigUpdateBatchReqDTO wxOrderConfigUpdateBatchReqDTO = new WxOrderConfigUpdateBatchReqDTO();
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("bd501ac0-5a36-4ef5-a054-dcb70107293f");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigUpdateBatchReqDTO.setWxOrderConfigDTO(wxOrderConfigDTO);
        when(mockWxStoreOrderConfigService.updateBatchStoreConfig(wxOrderConfigUpdateBatchReqDTO)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_config/update_batch_store_config")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateStoreConfigBatch_WxStoreOrderConfigServiceReturnsTrue() throws Exception {
        // Setup
        // Configure WxStoreOrderConfigService.updateBatchStoreConfig(...).
        final WxOrderConfigUpdateBatchReqDTO wxOrderConfigUpdateBatchReqDTO = new WxOrderConfigUpdateBatchReqDTO();
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("bd501ac0-5a36-4ef5-a054-dcb70107293f");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigUpdateBatchReqDTO.setWxOrderConfigDTO(wxOrderConfigDTO);
        when(mockWxStoreOrderConfigService.updateBatchStoreConfig(wxOrderConfigUpdateBatchReqDTO)).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_config/update_batch_store_config")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetStoreConfig() throws Exception {
        // Setup
        // Configure WxStoreOrderConfigService.getStoreConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("bd501ac0-5a36-4ef5-a054-dcb70107293f");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreOrderConfigService.getStoreConfig("storeGuid")).thenReturn(wxOrderConfigDTO);

        // Run the test and verify the results
        mockMvc.perform(get("/wx_store_order_config/store_config")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
