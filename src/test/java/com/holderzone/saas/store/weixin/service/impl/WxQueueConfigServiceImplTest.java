package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.weixin.req.WxQueueConfigUpdateBatchReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxQueueConfigDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxQueueConfigDO;
import com.holderzone.saas.store.weixin.mapstruct.WxQueueConfigMapstruct;
import com.holderzone.saas.store.weixin.service.WxConfigOverviewService;
import com.holderzone.saas.store.weixin.service.WxOrganizationService;
import org.apache.commons.lang3.tuple.Triple;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxQueueConfigServiceImplTest {

    @Mock
    private WxOrganizationService mockWxOrganizationService;
    @Mock
    private WxQueueConfigMapstruct mockWxQueueConfigMapstruct;
    @Mock
    private WxConfigOverviewService mockWxConfigOverviewService;

    private WxQueueConfigServiceImpl wxQueueConfigServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxQueueConfigServiceImplUnderTest = new WxQueueConfigServiceImpl();
        wxQueueConfigServiceImplUnderTest.wxOrganizationService = mockWxOrganizationService;
        wxQueueConfigServiceImplUnderTest.wxQueueConfigMapstruct = mockWxQueueConfigMapstruct;
        wxQueueConfigServiceImplUnderTest.wxConfigOverviewService = mockWxConfigOverviewService;
    }

    @Test
    public void testPageQueueConfig() {
        // Setup
        final WxStorePageReqDTO wxStorePageReqDTO = WxStorePageReqDTO.builder().build();

        // Configure WxOrganizationService.getStoreConfig(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("32faeaa9-0df8-42cc-b854-064421e7d94a");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setName("name");
        storeDTO.setBrandDTOList(Arrays.asList(brandDTO));
        final StoreDTO storeDTO1 = new StoreDTO();
        storeDTO1.setGuid("32faeaa9-0df8-42cc-b854-064421e7d94a");
        storeDTO1.setCode("code");
        storeDTO1.setName("name");
        final BrandDTO brandDTO1 = new BrandDTO();
        brandDTO1.setName("name");
        storeDTO1.setBrandDTOList(Arrays.asList(brandDTO1));
        final Triple<Page<StoreDTO>, List<StoreDTO>, List<String>> pageListListTriple = Triple.of(
                new Page<>(0L, 0L, Arrays.asList(storeDTO)), Arrays.asList(storeDTO1), Arrays.asList("value"));
        when(mockWxOrganizationService.getStoreConfig(WxStorePageReqDTO.builder().build()))
                .thenReturn(pageListListTriple);

        // Configure WxQueueConfigMapstruct.queueConfigDO2DTO(...).
        final WxQueueConfigDTO wxQueueConfigDTO = new WxQueueConfigDTO("ec7176c2-b670-4b9f-931f-5b5954ea378e", 0,
                new BigDecimal("0.00"), 0, new BigDecimal("0.00"), 0, false);
        when(mockWxQueueConfigMapstruct.queueConfigDO2DTO(
                new WxQueueConfigDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        "7e949a37-d0ab-4f7a-bb2e-dd9654f5e72d", "storeGuid", 0, 0, 0, new BigDecimal("0.00"),
                        0))).thenReturn(wxQueueConfigDTO);

        // Run the test
        final Page<WxQueueConfigDTO> result = wxQueueConfigServiceImplUnderTest.pageQueueConfig(wxStorePageReqDTO);

        // Verify the results
    }

    @Test
    public void testUpdateQueueConfig() {
        // Setup
        final WxQueueConfigDTO wxQueueConfigDTO = new WxQueueConfigDTO("ec7176c2-b670-4b9f-931f-5b5954ea378e", 0,
                new BigDecimal("0.00"), 0, new BigDecimal("0.00"), 0, false);

        // Configure WxQueueConfigMapstruct.queueConfigDTO2DO(...).
        final WxQueueConfigDO wxQueueConfigDO = new WxQueueConfigDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "7e949a37-d0ab-4f7a-bb2e-dd9654f5e72d", "storeGuid", 0, 0, 0,
                new BigDecimal("0.00"), 0);
        when(mockWxQueueConfigMapstruct.queueConfigDTO2DO(
                new WxQueueConfigDTO("ec7176c2-b670-4b9f-931f-5b5954ea378e", 0, new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), 0, false))).thenReturn(wxQueueConfigDO);

        // Run the test
        final Boolean result = wxQueueConfigServiceImplUnderTest.updateQueueConfig(wxQueueConfigDTO);

        // Verify the results
        assertFalse(result);
        verify(mockWxConfigOverviewService).couldEdit(Arrays.asList("value"), 1);
    }

    @Test
    public void testUpdateQueueConfigBatch() {
        // Setup
        final WxQueueConfigUpdateBatchReqDTO wxQueueConfigUpdateBatchReqDTO = new WxQueueConfigUpdateBatchReqDTO(
                Arrays.asList("value"),
                new WxQueueConfigDTO("ec7176c2-b670-4b9f-931f-5b5954ea378e", 0, new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), 0, false));

        // Configure WxQueueConfigMapstruct.queueConfigDTO2DO(...).
        final WxQueueConfigDO wxQueueConfigDO = new WxQueueConfigDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "7e949a37-d0ab-4f7a-bb2e-dd9654f5e72d", "storeGuid", 0, 0, 0,
                new BigDecimal("0.00"), 0);
        when(mockWxQueueConfigMapstruct.queueConfigDTO2DO(
                new WxQueueConfigDTO("ec7176c2-b670-4b9f-931f-5b5954ea378e", 0, new BigDecimal("0.00"), 0,
                        new BigDecimal("0.00"), 0, false))).thenReturn(wxQueueConfigDO);

        // Run the test
        final Boolean result = wxQueueConfigServiceImplUnderTest.updateQueueConfigBatch(wxQueueConfigUpdateBatchReqDTO);

        // Verify the results
        assertFalse(result);
        verify(mockWxConfigOverviewService).couldEdit(Arrays.asList("value"), 1);
    }

    @Test
    public void testGetQueueConfig() {
        // Setup
        final WxQueueConfigDTO wxQueueConfigDTO = new WxQueueConfigDTO("ec7176c2-b670-4b9f-931f-5b5954ea378e", 0,
                new BigDecimal("0.00"), 0, new BigDecimal("0.00"), 0, false);
        final WxQueueConfigDTO expectedResult = new WxQueueConfigDTO("ec7176c2-b670-4b9f-931f-5b5954ea378e", 0,
                new BigDecimal("0.00"), 0, new BigDecimal("0.00"), 0, false);

        // Configure WxQueueConfigMapstruct.queueConfigDO2DTO(...).
        final WxQueueConfigDTO wxQueueConfigDTO1 = new WxQueueConfigDTO("ec7176c2-b670-4b9f-931f-5b5954ea378e", 0,
                new BigDecimal("0.00"), 0, new BigDecimal("0.00"), 0, false);
        when(mockWxQueueConfigMapstruct.queueConfigDO2DTO(
                new WxQueueConfigDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        "7e949a37-d0ab-4f7a-bb2e-dd9654f5e72d", "storeGuid", 0, 0, 0, new BigDecimal("0.00"),
                        0))).thenReturn(wxQueueConfigDTO1);

        // Run the test
        final WxQueueConfigDTO result = wxQueueConfigServiceImplUnderTest.getQueueConfig(wxQueueConfigDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetOne() {
        // Setup
        final WxQueueConfigDO expectedResult = new WxQueueConfigDO(0L, LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "7e949a37-d0ab-4f7a-bb2e-dd9654f5e72d", "storeGuid", 0, 0, 0,
                new BigDecimal("0.00"), 0);

        // Run the test
        final WxQueueConfigDO result = wxQueueConfigServiceImplUnderTest.getOne("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
