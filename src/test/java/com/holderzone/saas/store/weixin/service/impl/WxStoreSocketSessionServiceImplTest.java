package com.holderzone.saas.store.weixin.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.weixin.WxSocketDistributionDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.service.WxSocketMsgService;
import com.holderzone.saas.store.weixin.service.WxStoreMenuDetailsService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStoreSocketSessionServiceImplTest {

    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private WxStoreMenuDetailsService mockWxStoreMenuDetailsService;
    @Mock
    private WxSocketMsgService mockWxSocketMsgService;

    private WxStoreSocketSessionServiceImpl wxStoreSocketSessionServiceImplUnderTest;

    @Before
    public void setUp() {
        wxStoreSocketSessionServiceImplUnderTest = new WxStoreSocketSessionServiceImpl(mockRedisUtils,
                mockWxStoreMenuDetailsService, mockWxSocketMsgService);
    }

    @Test
    public void testCreateSession() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .build())).thenReturn(false);
        when(mockRedisUtils.hGet("key", "openId")).thenReturn("result");

        // Run the test
        wxStoreSocketSessionServiceImplUnderTest.createSession(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        verify(mockWxSocketMsgService).distribute(WxSocketDistributionDTO.builder()
                .distribution(0)
                .tableGuid("diningTableGuid")
                .openId("openId")
                .content("content")
                .build());
        verify(mockRedisUtils).hPut("key", "openId", WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .build());
        verify(mockRedisUtils).set("key", WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .build());
    }

    @Test
    public void testGetSession1() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .build();
        final List<WxStoreAdvanceConsumerReqDTO> expectedResult = Arrays.asList(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .build());
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .build())).thenReturn(false);

        // Configure RedisUtils.hValues(...).
        WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO1 = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .build();
        when(mockRedisUtils.hValues("key")).thenReturn(Collections.singletonList(wxStoreAdvanceConsumerReqDTO1));

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final List<WxStoreAdvanceConsumerReqDTO> result = wxStoreSocketSessionServiceImplUnderTest.getSession(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetSession1_RedisUtilsHValuesReturnsNoItems() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .build())).thenReturn(false);
        when(mockRedisUtils.hValues("key")).thenReturn(Collections.emptyList());

        // Run the test
        final List<WxStoreAdvanceConsumerReqDTO> result = wxStoreSocketSessionServiceImplUnderTest.getSession(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetAllConsumer() {
        assertNull(wxStoreSocketSessionServiceImplUnderTest.getAllConsumer(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .build()));
    }

    @Test
    public void testDelSession() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .build())).thenReturn(false);

        // Run the test
        wxStoreSocketSessionServiceImplUnderTest.delSession(wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        verify(mockRedisUtils).delete("key");
    }

    @Test
    public void testDelTableSession() {
        // Setup
        // Run the test
        wxStoreSocketSessionServiceImplUnderTest.delTableSession("tableGuid");

        // Verify the results
        verify(mockRedisUtils).delete("key");
    }

    @Test
    public void testGetSingleSession1() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .build())).thenReturn(false);
        when(mockRedisUtils.hGet("key", "openId")).thenReturn("result");
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WxStoreAdvanceConsumerReqDTO result = wxStoreSocketSessionServiceImplUnderTest.getSingleSession(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testGetSession2() {
        // Setup
        final List<WxStoreAdvanceConsumerReqDTO> expectedResult = Arrays.asList(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .build());

        WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .build();
        when(mockRedisUtils.hValues("key")).thenReturn(Lists.newArrayList(wxStoreAdvanceConsumerReqDTO));

        // Run the test
        final List<WxStoreAdvanceConsumerReqDTO> result = wxStoreSocketSessionServiceImplUnderTest.getSession(
                "tableGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetSession2_RedisUtilsReturnsNoItems() {
        // Setup
        when(mockRedisUtils.hValues("key")).thenReturn(Collections.emptyList());

        // Run the test
        final List<WxStoreAdvanceConsumerReqDTO> result = wxStoreSocketSessionServiceImplUnderTest.getSession(
                "tableGuid");

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetSingleSession2() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO expectedResult = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .nickName("nickName")
                        .headImgUrl("headImgUrl")
                        .enterpriseGuid("enterpriseGuid")
                        .diningTableGuid("diningTableGuid")
                        .build())
                .build();
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WxStoreAdvanceConsumerReqDTO result = wxStoreSocketSessionServiceImplUnderTest.getSingleSession("openId");

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
