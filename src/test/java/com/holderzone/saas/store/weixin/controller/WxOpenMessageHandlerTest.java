package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.weixin.menu.WxMenuUrlDTO;
import com.holderzone.saas.store.dto.weixin.open.*;
import com.holderzone.saas.store.dto.weixin.req.*;
import com.holderzone.saas.store.dto.weixin.resp.WxConfigRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxSubjectRespDTO;
import com.holderzone.saas.store.weixin.service.WxMpTemplateService;
import com.holderzone.saas.store.weixin.service.WxOpenMessageService;
import me.chanjar.weixin.common.error.WxErrorException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxOpenMessageHandler.class)
public class WxOpenMessageHandlerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxOpenMessageService mockWxOpenMessageService;
    @MockBean
    private WxMpTemplateService mockWxMpTemplateService;

    @Test
    public void testHandleMessage() throws Exception {
        // Setup
        // Configure WxOpenMessageService.getWxMpXmlMessage(...).
        final WxCommonReqDTO wxCommonReqDTO = new WxCommonReqDTO();
        wxCommonReqDTO.setSignature("signature");
        wxCommonReqDTO.setEchostr("echostr");
        wxCommonReqDTO.setTimestamp("timestamp");
        wxCommonReqDTO.setNonce("nonce");
        wxCommonReqDTO.setEncrypt_type("encrypt_type");
        final WxMessageHandleReqDTO wxMessageHandleReqDTO = new WxMessageHandleReqDTO("body", "appId", wxCommonReqDTO);
        when(mockWxOpenMessageService.getWxMpXmlMessage(wxMessageHandleReqDTO)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/wx_handler/handle_message")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testHandleMessage_WxOpenMessageServiceThrowsWxErrorException() throws Exception {
        // Setup
        // Configure WxOpenMessageService.getWxMpXmlMessage(...).
        final WxCommonReqDTO wxCommonReqDTO = new WxCommonReqDTO();
        wxCommonReqDTO.setSignature("signature");
        wxCommonReqDTO.setEchostr("echostr");
        wxCommonReqDTO.setTimestamp("timestamp");
        wxCommonReqDTO.setNonce("nonce");
        wxCommonReqDTO.setEncrypt_type("encrypt_type");
        final WxMessageHandleReqDTO wxMessageHandleReqDTO = new WxMessageHandleReqDTO("body", "appId", wxCommonReqDTO);
        when(mockWxOpenMessageService.getWxMpXmlMessage(wxMessageHandleReqDTO)).thenThrow(WxErrorException.class);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_handler/handle_message")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is5xxServerError())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetUserInfo() throws Exception {
        // Setup
        // Configure WxOpenMessageService.getUserInfo(...).
        final WxAuthorizeReqDTO wxAuthorizeReqDTO = new WxAuthorizeReqDTO();
        wxAuthorizeReqDTO.setCode("code");
        wxAuthorizeReqDTO.setState("state");
        wxAuthorizeReqDTO.setEventKey("eventKey");
        wxAuthorizeReqDTO.setAppId("appId");
        wxAuthorizeReqDTO.setWeixinToken("weixinToken");
        when(mockWxOpenMessageService.getUserInfo(wxAuthorizeReqDTO)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/wx_handler/get_user_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetUserInfo_WxOpenMessageServiceThrowsWxErrorException() throws Exception {
        // Setup
        // Configure WxOpenMessageService.getUserInfo(...).
        final WxAuthorizeReqDTO wxAuthorizeReqDTO = new WxAuthorizeReqDTO();
        wxAuthorizeReqDTO.setCode("code");
        wxAuthorizeReqDTO.setState("state");
        wxAuthorizeReqDTO.setEventKey("eventKey");
        wxAuthorizeReqDTO.setAppId("appId");
        wxAuthorizeReqDTO.setWeixinToken("weixinToken");
        when(mockWxOpenMessageService.getUserInfo(wxAuthorizeReqDTO)).thenThrow(WxErrorException.class);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_handler/get_user_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is5xxServerError())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testCreateMsgTemp() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/wx_handler/create_msg_temp")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockWxOpenMessageService).createQueueMsgTemp(new TempMsgCreateDTO("appId"));
    }

    @Test
    public void testCreateMsgTemp_WxOpenMessageServiceThrowsWxErrorException() throws Exception {
        // Setup
        doThrow(WxErrorException.class).when(mockWxOpenMessageService).createQueueMsgTemp(
                new TempMsgCreateDTO("appId"));

        // Run the test and verify the results
        mockMvc.perform(post("/wx_handler/create_msg_temp")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is5xxServerError())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testShopList() throws Exception {
        // Setup
        when(mockWxOpenMessageService.shopList(WxPortalReqDTO.builder().build())).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/wx_handler/shop_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testShopList_WxOpenMessageServiceThrowsWxErrorException() throws Exception {
        // Setup
        when(mockWxOpenMessageService.shopList(WxPortalReqDTO.builder().build())).thenThrow(WxErrorException.class);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_handler/shop_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is5xxServerError())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetWxConfig() throws Exception {
        // Setup
        // Configure WxOpenMessageService.generateDTO(...).
        final WxCommonReqDTO wxCommonReqDTO = new WxCommonReqDTO();
        wxCommonReqDTO.setSignature("signature");
        wxCommonReqDTO.setEchostr("echostr");
        wxCommonReqDTO.setTimestamp("timestamp");
        wxCommonReqDTO.setNonce("nonce");
        wxCommonReqDTO.setEncrypt_type("encrypt_type");
        final WxConfigRespDTO wxConfigRespDTO = new WxConfigRespDTO(wxCommonReqDTO, "brandName", "appId",
                "enterpriseGuid");
        when(mockWxOpenMessageService.generateDTO(WxPortalReqDTO.builder().build())).thenReturn(wxConfigRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_handler/wx_config")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetWxConfig_WxOpenMessageServiceThrowsWxErrorException() throws Exception {
        // Setup
        when(mockWxOpenMessageService.generateDTO(WxPortalReqDTO.builder().build())).thenThrow(WxErrorException.class);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_handler/wx_config")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is5xxServerError())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testMemberLogin() throws Exception {
        // Setup
        when(mockWxOpenMessageService.memberLogin(WxPortalReqDTO.builder().build())).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/wx_handler/login")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testNewMemberLogin() throws Exception {
        // Setup
        when(mockWxOpenMessageService.newMemberLogin(WxPortalReqDTO.builder().build())).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/wx_handler/new_member_login")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetWxSubject() throws Exception {
        // Setup
        // Configure WxOpenMessageService.getWxSubject(...).
        final WxSubjectRespDTO wxSubjectRespDTO = new WxSubjectRespDTO(false, "operSubjectGuid", false);
        when(mockWxOpenMessageService.getWxSubject(WxSubjectReqDTO.builder().build())).thenReturn(wxSubjectRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_handler/get_wx_subject")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetBossAuthorizeUrl() throws Exception {
        // Setup
        // Configure WxOpenMessageService.getBossAuthorizeUrl(...).
        final WxMenuUrlDTO wxMenuUrlDTO = new WxMenuUrlDTO();
        wxMenuUrlDTO.setEnterpriseGuid("enterpriseGuid");
        wxMenuUrlDTO.setBrandGuid("brandGuid");
        wxMenuUrlDTO.setMenuType("menuType");
        wxMenuUrlDTO.setAppId("appId");
        wxMenuUrlDTO.setToken("token");
        when(mockWxOpenMessageService.getBossAuthorizeUrl(wxMenuUrlDTO)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/wx_handler/menu_authorize_url/boss")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetBossRedirectUrl() throws Exception {
        // Setup
        // Configure WxOpenMessageService.getBossRedirectUrl(...).
        final WxAuthorizeReqDTO wxAuthorizeReqDTO = new WxAuthorizeReqDTO();
        wxAuthorizeReqDTO.setCode("code");
        wxAuthorizeReqDTO.setState("state");
        wxAuthorizeReqDTO.setEventKey("eventKey");
        wxAuthorizeReqDTO.setAppId("appId");
        wxAuthorizeReqDTO.setWeixinToken("weixinToken");
        when(mockWxOpenMessageService.getBossRedirectUrl(wxAuthorizeReqDTO)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/wx_handler/menu_redirect_url/boss")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testSaveBossAuthToken() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/wx_handler/save/boss/token")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm WxOpenMessageService.saveBossAuthToken(...).
        final WxMenuUrlDTO wxMenuUrlDTO = new WxMenuUrlDTO();
        wxMenuUrlDTO.setEnterpriseGuid("enterpriseGuid");
        wxMenuUrlDTO.setBrandGuid("brandGuid");
        wxMenuUrlDTO.setMenuType("menuType");
        wxMenuUrlDTO.setAppId("appId");
        wxMenuUrlDTO.setToken("token");
        verify(mockWxOpenMessageService).saveBossAuthToken(wxMenuUrlDTO);
    }

    @Test
    public void testCleanBossAuthToken() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/wx_handler/clean/boss/token")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm WxOpenMessageService.cleanBossAuthToken(...).
        final WxMenuUrlDTO wxMenuUrlDTO = new WxMenuUrlDTO();
        wxMenuUrlDTO.setEnterpriseGuid("enterpriseGuid");
        wxMenuUrlDTO.setBrandGuid("brandGuid");
        wxMenuUrlDTO.setMenuType("menuType");
        wxMenuUrlDTO.setAppId("appId");
        wxMenuUrlDTO.setToken("token");
        verify(mockWxOpenMessageService).cleanBossAuthToken(wxMenuUrlDTO);
    }

    @Test
    public void testSendCallMessage() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/wx_handler/send_call_message")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm WxOpenMessageService.sendCallMessage(...).
        final WxSendMessageReqDTO sendMessageReqDTO = new WxSendMessageReqDTO();
        final WxOrderReqDTO wxOrderReqDTO = new WxOrderReqDTO();
        wxOrderReqDTO.setOrderGuid("orderGuid");
        final WxOrderItemReqDTO wxOrderItemReqDTO = new WxOrderItemReqDTO();
        wxOrderItemReqDTO.setItemGuid("itemGuid");
        wxOrderItemReqDTO.setItemName("itemName");
        wxOrderReqDTO.setOrderItemList(Arrays.asList(wxOrderItemReqDTO));
        sendMessageReqDTO.setOrderList(Arrays.asList(wxOrderReqDTO));
        verify(mockWxOpenMessageService).sendCallMessage(sendMessageReqDTO);
    }

    @Test
    public void testAddMsgTemplate() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/wx_handler/add_msg_template")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm WxMpTemplateService.addMsgTemplate(...).
        final WxMpTemplateDTO wxMpTemplateDTO = new WxMpTemplateDTO();
        wxMpTemplateDTO.setShortId("shortId");
        wxMpTemplateDTO.setAppId("appId");
        wxMpTemplateDTO.setTemplateId("templateId");
        wxMpTemplateDTO.setBrandGuid("brandGuid");
        wxMpTemplateDTO.setType(0);
        verify(mockWxMpTemplateService).addMsgTemplate(wxMpTemplateDTO);
    }
}
