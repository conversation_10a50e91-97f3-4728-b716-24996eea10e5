package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberCard;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberInfoDTO;
import com.holderzone.holder.saas.member.wechat.dto.member.MemberInfoVolume;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestMemberInfoVolumeQuery;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolume;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreTradeOrderDetailsDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPrepayReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreTradeOrderDetailsRespDTO;
import com.holderzone.saas.store.weixin.config.ResponseModel;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import com.holderzone.saas.store.weixin.entity.query.WxStorePendingOrdersQuery;
import com.holderzone.saas.store.weixin.execption.CorrectResult;
import com.holderzone.saas.store.weixin.service.WxStoreMenuDetailsService;
import com.holderzone.saas.store.weixin.service.WxStoreMerchantOrderService;
import com.holderzone.saas.store.weixin.service.WxStorePersonOrderDetailsService;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreDineInBillClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreDineInOrderClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreTableClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStoreOrderPayServiceImplTest {

    @Mock
    private WxStoreSessionDetailsService mockWxStoreSessionDetailsService;
    @Mock
    private WxStoreMenuDetailsService mockWxStoreMenuDetailsService;
    @Mock
    private WxStoreTableClientService mockWxStoreTableClientService;
    @Mock
    private WxStorePersonOrderDetailsService mockWxStorePersonOrderDetailsService;
    @Mock
    private WxStoreDineInBillClientService mockWxStoreDineInBillClientService;
    @Mock
    private WxStoreMerchantOrderService mockWxStoreMerchantOrderService;
    @Mock
    private MemberClientService mockMemberClientService;
    @Mock
    private WxStoreDineInOrderClientService mockWxStoreDineInOrderClientService;
    @Mock
    private HsaBaseClientService mockHsaBaseClientService;

    @InjectMocks
    private WxStoreOrderPayServiceImpl wxStoreOrderPayServiceImplUnderTest;

    @Test
    public void testOrderPay1() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .memberIntegral(0)
                .build();
        final WxStoreTradeOrderDetailsRespDTO expectedResult = WxStoreTradeOrderDetailsRespDTO.builder()
                .discountFee(new BigDecimal("0.00"))
                .discountFeeDetailDTOS(Arrays.asList(new DiscountFeeDetailDTO()))
                .errorMsg("errorMsg")
                .unMemberTotalAmount(new BigDecimal("0.00"))
                .saveZero(new BigDecimal("0.00"))
                .memberCardStatus(0)
                .memberVolumeStatus(0)
                .memberCardFee(new BigDecimal("0.00"))
                .memberVolumeFee(new BigDecimal("0.00"))
                .cardGuid("cardGuid")
                .cardName("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .systemManagementGuid("systemManagementGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .memberIntegral(0)
                .build())).thenReturn(false);
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId")).thenReturn("combine");

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .systemManagementGuid("systemManagementGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("combine")
                .mode(0)
                .orderFee(new BigDecimal("0.00"))
                .memberInfoGuid("memberInfoGuid")
                .purchaseGroupFee(new BigDecimal("0.00"))
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        // Configure WxStoreSessionDetailsService.getMemberInfoAndCardList(...).
        final ResponseMemberAndCardInfoDTO responseMemberAndCardInfoDTO = new ResponseMemberAndCardInfoDTO();
        final ResponseMemberInfoDTO memberInfoDTO = new ResponseMemberInfoDTO();
        memberInfoDTO.setMemberInfoGuid("memberInfoGuid");
        memberInfoDTO.setStateCode(0);
        responseMemberAndCardInfoDTO.setMemberInfoDTO(memberInfoDTO);
        final ResponseMemberCard responseMemberCard = new ResponseMemberCard();
        responseMemberCard.setCardGuid("cardGuid");
        responseMemberCard.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCard.setSystemManagementGuid("systemManagementGuid");
        responseMemberAndCardInfoDTO.setMemberCardListRespDTOs(Arrays.asList(responseMemberCard));
        when(mockWxStoreSessionDetailsService.getMemberInfoAndCardList("enterpriseGuid", "storeGuid",
                "openId")).thenReturn(responseMemberAndCardInfoDTO);

        // Configure HsaBaseClientService.getMemberVolume(...).
        final ResponseMemberInfoVolume responseMemberInfoVolume = new ResponseMemberInfoVolume();
        responseMemberInfoVolume.setMayUseVolumeNum(0);
        final MemberInfoVolume memberInfoVolume = new MemberInfoVolume();
        memberInfoVolume.setMayUseVolume(0);
        memberInfoVolume.setVolumeState(0);
        memberInfoVolume.setVolumeType(0);
        memberInfoVolume.setVolumeCode("volumeCode");
        responseMemberInfoVolume.setMemberVolumeList(Arrays.asList(memberInfoVolume));
        final ResponseModel<ResponseMemberInfoVolume> responseMemberInfoVolumeResponseModel = new ResponseModel<>(
                responseMemberInfoVolume);
        final RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO = new RequestMemberInfoVolumeQuery();
        memberInfoVolumeQueryReqDTO.setMemberInfoGuid("memberInfoGuid");
        memberInfoVolumeQueryReqDTO.setVolumeType(0);
        memberInfoVolumeQueryReqDTO.setStoreGuid("storeGuid");
        memberInfoVolumeQueryReqDTO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVolumeQueryReqDTO.setBrandGuid("brandGuid");
        memberInfoVolumeQueryReqDTO.setMayUseVolume(0);
        memberInfoVolumeQueryReqDTO.setExcludeBind(0);
        when(mockHsaBaseClientService.getMemberVolume(memberInfoVolumeQueryReqDTO))
                .thenReturn(responseMemberInfoVolumeResponseModel);

        // Configure WxStoreSessionDetailsService.calculateDetails(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("combine");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        dineinOrderDetailRespDTO.setVersion(0);
        final CorrectResult<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOCorrectResult = new CorrectResult<>(
                "errorMsg", 0, dineinOrderDetailRespDTO);
        when(mockWxStoreSessionDetailsService.calculateDetails("combine", "openId", "memberInfoCardGuid", 0,
                "volumeCode")).thenReturn(dineinOrderDetailRespDTOCorrectResult);

        // Configure WxStorePersonOrderDetailsService.getWxStoreTradeOrderDetails(...).
        final WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO = new WxStoreTradeOrderDetailsDTO();
        wxStoreTradeOrderDetailsDTO.setMainTable(0);
        wxStoreTradeOrderDetailsDTO.setGuid("4365c531-1931-4dce-984d-0a0678cdfbef");
        wxStoreTradeOrderDetailsDTO.setWxGuid("wxGuid");
        wxStoreTradeOrderDetailsDTO.setBatchId("batchId");
        wxStoreTradeOrderDetailsDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .memberIntegral(0)
                .build());
        final DineinOrderDetailRespDTO mainOrderDetails = new DineinOrderDetailRespDTO();
        mainOrderDetails.setGuid("combine");
        mainOrderDetails.setTradeMode(0);
        mainOrderDetails.setOrderFee(new BigDecimal("0.00"));
        mainOrderDetails.setActuallyPayFee(new BigDecimal("0.00"));
        mainOrderDetails.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        mainOrderDetails.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        mainOrderDetails.setOrderNo("orderNo");
        mainOrderDetails.setState(0);
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        mainOrderDetails.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        mainOrderDetails.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        mainOrderDetails.setTip("tip");
        mainOrderDetails.setVersion(0);
        when(mockWxStorePersonOrderDetailsService.getWxStoreTradeOrderDetails(mainOrderDetails))
                .thenReturn(wxStoreTradeOrderDetailsDTO);

        // Configure WxStorePersonOrderDetailsService.getWxStoreTradeOrderDetailsResp(...).
        final WxStoreTradeOrderDetailsRespDTO wxStoreTradeOrderDetailsRespDTO = WxStoreTradeOrderDetailsRespDTO.builder()
                .discountFee(new BigDecimal("0.00"))
                .discountFeeDetailDTOS(Arrays.asList(new DiscountFeeDetailDTO()))
                .errorMsg("errorMsg")
                .unMemberTotalAmount(new BigDecimal("0.00"))
                .saveZero(new BigDecimal("0.00"))
                .memberCardStatus(0)
                .memberVolumeStatus(0)
                .memberCardFee(new BigDecimal("0.00"))
                .memberVolumeFee(new BigDecimal("0.00"))
                .cardGuid("cardGuid")
                .cardName("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .systemManagementGuid("systemManagementGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .build();
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("combine");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO2 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO2.setDiscountType(0);
        discountFeeDetailDTO2.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO2));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        dineinOrderDetailRespDTO1.setState(0);
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        dineinOrderDetailRespDTO1.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO1.setTip("tip");
        dineinOrderDetailRespDTO1.setVersion(0);
        final WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO1 = new WxStoreTradeOrderDetailsDTO();
        wxStoreTradeOrderDetailsDTO1.setMainTable(0);
        wxStoreTradeOrderDetailsDTO1.setGuid("4365c531-1931-4dce-984d-0a0678cdfbef");
        wxStoreTradeOrderDetailsDTO1.setWxGuid("wxGuid");
        wxStoreTradeOrderDetailsDTO1.setBatchId("batchId");
        wxStoreTradeOrderDetailsDTO1.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .memberIntegral(0)
                .build());
        when(mockWxStorePersonOrderDetailsService.getWxStoreTradeOrderDetailsResp(dineinOrderDetailRespDTO1,
                wxStoreTradeOrderDetailsDTO1, WxStoreAdvanceConsumerReqDTO.builder()
                        .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                                .openId("openId")
                                .enterpriseGuid("enterpriseGuid")
                                .storeGuid("storeGuid")
                                .diningTableGuid("diningTableGuid")
                                .brandGuid("brandGuid")
                                .build())
                        .memberIntegral(0)
                        .build())).thenReturn(wxStoreTradeOrderDetailsRespDTO);

        // Run the test
        final WxStoreTradeOrderDetailsRespDTO result = wxStoreOrderPayServiceImplUnderTest.orderPay(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).savePrepay(WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .systemManagementGuid("systemManagementGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("combine")
                .mode(0)
                .orderFee(new BigDecimal("0.00"))
                .memberInfoGuid("memberInfoGuid")
                .purchaseGroupFee(new BigDecimal("0.00"))
                .version(0)
                .build());
    }

    @Test
    public void testOrderPay1_WxStoreMenuDetailsServiceReturnsTrue() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .memberIntegral(0)
                .build();
        final WxStoreTradeOrderDetailsRespDTO expectedResult = WxStoreTradeOrderDetailsRespDTO.builder()
                .discountFee(new BigDecimal("0.00"))
                .discountFeeDetailDTOS(Arrays.asList(new DiscountFeeDetailDTO()))
                .errorMsg("errorMsg")
                .unMemberTotalAmount(new BigDecimal("0.00"))
                .saveZero(new BigDecimal("0.00"))
                .memberCardStatus(0)
                .memberVolumeStatus(0)
                .memberCardFee(new BigDecimal("0.00"))
                .memberVolumeFee(new BigDecimal("0.00"))
                .cardGuid("cardGuid")
                .cardName("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .systemManagementGuid("systemManagementGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .memberIntegral(0)
                .build())).thenReturn(true);
        when(mockWxStoreTableClientService.getOrderGuid("diningTableGuid")).thenReturn("combine");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("combine");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        dineinOrderDetailRespDTO.setVersion(0);
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("combine")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("diningTableGuid")).thenReturn("orderGuid");

        // Configure WxStoreMerchantOrderService.tableListByMerchantGuidOrOrderGuid(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setId(0L);
        wxStoreMerchantOrderDO.setGuid("fe4e403e-903a-48a3-a99c-dff57275db80");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setOrderState(0);
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderService.tableListByMerchantGuidOrOrderGuid(WxStorePendingOrdersQuery.builder()
                .orderStates(Arrays.asList(0))
                .orderGuid("orderGuid")
                .combine("combine")
                .build())).thenReturn(wxStoreMerchantOrderDOS);

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .systemManagementGuid("systemManagementGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("combine")
                .mode(0)
                .orderFee(new BigDecimal("0.00"))
                .memberInfoGuid("memberInfoGuid")
                .purchaseGroupFee(new BigDecimal("0.00"))
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        // Configure WxStoreSessionDetailsService.getMemberInfoAndCardList(...).
        final ResponseMemberAndCardInfoDTO responseMemberAndCardInfoDTO = new ResponseMemberAndCardInfoDTO();
        final ResponseMemberInfoDTO memberInfoDTO = new ResponseMemberInfoDTO();
        memberInfoDTO.setMemberInfoGuid("memberInfoGuid");
        memberInfoDTO.setStateCode(0);
        responseMemberAndCardInfoDTO.setMemberInfoDTO(memberInfoDTO);
        final ResponseMemberCard responseMemberCard = new ResponseMemberCard();
        responseMemberCard.setCardGuid("cardGuid");
        responseMemberCard.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCard.setSystemManagementGuid("systemManagementGuid");
        responseMemberAndCardInfoDTO.setMemberCardListRespDTOs(Arrays.asList(responseMemberCard));
        when(mockWxStoreSessionDetailsService.getMemberInfoAndCardList("enterpriseGuid", "storeGuid",
                "openId")).thenReturn(responseMemberAndCardInfoDTO);

        // Configure HsaBaseClientService.getMemberVolume(...).
        final ResponseMemberInfoVolume responseMemberInfoVolume = new ResponseMemberInfoVolume();
        responseMemberInfoVolume.setMayUseVolumeNum(0);
        final MemberInfoVolume memberInfoVolume = new MemberInfoVolume();
        memberInfoVolume.setMayUseVolume(0);
        memberInfoVolume.setVolumeState(0);
        memberInfoVolume.setVolumeType(0);
        memberInfoVolume.setVolumeCode("volumeCode");
        responseMemberInfoVolume.setMemberVolumeList(Arrays.asList(memberInfoVolume));
        final ResponseModel<ResponseMemberInfoVolume> responseMemberInfoVolumeResponseModel = new ResponseModel<>(
                responseMemberInfoVolume);
        final RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO = new RequestMemberInfoVolumeQuery();
        memberInfoVolumeQueryReqDTO.setMemberInfoGuid("memberInfoGuid");
        memberInfoVolumeQueryReqDTO.setVolumeType(0);
        memberInfoVolumeQueryReqDTO.setStoreGuid("storeGuid");
        memberInfoVolumeQueryReqDTO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVolumeQueryReqDTO.setBrandGuid("brandGuid");
        memberInfoVolumeQueryReqDTO.setMayUseVolume(0);
        memberInfoVolumeQueryReqDTO.setExcludeBind(0);
        when(mockHsaBaseClientService.getMemberVolume(memberInfoVolumeQueryReqDTO))
                .thenReturn(responseMemberInfoVolumeResponseModel);

        // Configure WxStoreSessionDetailsService.calculateDetails(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("combine");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        dineinOrderDetailRespDTO1.setState(0);
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO1.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO1.setTip("tip");
        dineinOrderDetailRespDTO1.setVersion(0);
        final CorrectResult<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOCorrectResult = new CorrectResult<>(
                "errorMsg", 0, dineinOrderDetailRespDTO1);
        when(mockWxStoreSessionDetailsService.calculateDetails("combine", "openId", "memberInfoCardGuid", 0,
                "volumeCode")).thenReturn(dineinOrderDetailRespDTOCorrectResult);

        // Configure WxStorePersonOrderDetailsService.getWxStoreTradeOrderDetails(...).
        final WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO = new WxStoreTradeOrderDetailsDTO();
        wxStoreTradeOrderDetailsDTO.setMainTable(0);
        wxStoreTradeOrderDetailsDTO.setGuid("4365c531-1931-4dce-984d-0a0678cdfbef");
        wxStoreTradeOrderDetailsDTO.setWxGuid("wxGuid");
        wxStoreTradeOrderDetailsDTO.setBatchId("batchId");
        wxStoreTradeOrderDetailsDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .memberIntegral(0)
                .build());
        final DineinOrderDetailRespDTO mainOrderDetails = new DineinOrderDetailRespDTO();
        mainOrderDetails.setGuid("combine");
        mainOrderDetails.setTradeMode(0);
        mainOrderDetails.setOrderFee(new BigDecimal("0.00"));
        mainOrderDetails.setActuallyPayFee(new BigDecimal("0.00"));
        mainOrderDetails.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO2 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO2.setDiscountType(0);
        discountFeeDetailDTO2.setDiscountFee(new BigDecimal("0.00"));
        mainOrderDetails.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO2));
        mainOrderDetails.setOrderNo("orderNo");
        mainOrderDetails.setState(0);
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        mainOrderDetails.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        mainOrderDetails.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        mainOrderDetails.setTip("tip");
        mainOrderDetails.setVersion(0);
        when(mockWxStorePersonOrderDetailsService.getWxStoreTradeOrderDetails(mainOrderDetails))
                .thenReturn(wxStoreTradeOrderDetailsDTO);

        // Configure WxStorePersonOrderDetailsService.getWxStoreTradeOrderDetailsResp(...).
        final WxStoreTradeOrderDetailsRespDTO wxStoreTradeOrderDetailsRespDTO = WxStoreTradeOrderDetailsRespDTO.builder()
                .discountFee(new BigDecimal("0.00"))
                .discountFeeDetailDTOS(Arrays.asList(new DiscountFeeDetailDTO()))
                .errorMsg("errorMsg")
                .unMemberTotalAmount(new BigDecimal("0.00"))
                .saveZero(new BigDecimal("0.00"))
                .memberCardStatus(0)
                .memberVolumeStatus(0)
                .memberCardFee(new BigDecimal("0.00"))
                .memberVolumeFee(new BigDecimal("0.00"))
                .cardGuid("cardGuid")
                .cardName("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .systemManagementGuid("systemManagementGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .build();
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO2 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO2.setGuid("combine");
        dineinOrderDetailRespDTO2.setTradeMode(0);
        dineinOrderDetailRespDTO2.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO3 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO3.setDiscountType(0);
        discountFeeDetailDTO3.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO3));
        dineinOrderDetailRespDTO2.setOrderNo("orderNo");
        dineinOrderDetailRespDTO2.setState(0);
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineinOrderDetailRespDTO2.setDineInItemDTOS(Arrays.asList(dineInItemDTO3));
        dineinOrderDetailRespDTO2.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO2.setTip("tip");
        dineinOrderDetailRespDTO2.setVersion(0);
        final WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO1 = new WxStoreTradeOrderDetailsDTO();
        wxStoreTradeOrderDetailsDTO1.setMainTable(0);
        wxStoreTradeOrderDetailsDTO1.setGuid("4365c531-1931-4dce-984d-0a0678cdfbef");
        wxStoreTradeOrderDetailsDTO1.setWxGuid("wxGuid");
        wxStoreTradeOrderDetailsDTO1.setBatchId("batchId");
        wxStoreTradeOrderDetailsDTO1.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .memberIntegral(0)
                .build());
        when(mockWxStorePersonOrderDetailsService.getWxStoreTradeOrderDetailsResp(dineinOrderDetailRespDTO2,
                wxStoreTradeOrderDetailsDTO1, WxStoreAdvanceConsumerReqDTO.builder()
                        .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                                .openId("openId")
                                .enterpriseGuid("enterpriseGuid")
                                .storeGuid("storeGuid")
                                .diningTableGuid("diningTableGuid")
                                .brandGuid("brandGuid")
                                .build())
                        .memberIntegral(0)
                        .build())).thenReturn(wxStoreTradeOrderDetailsRespDTO);

        // Run the test
        final WxStoreTradeOrderDetailsRespDTO result = wxStoreOrderPayServiceImplUnderTest.orderPay(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).saveOrderGuid("diningTableGuid", "combine");
        verify(mockWxStoreSessionDetailsService).savePrepay(WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .systemManagementGuid("systemManagementGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("combine")
                .mode(0)
                .orderFee(new BigDecimal("0.00"))
                .memberInfoGuid("memberInfoGuid")
                .purchaseGroupFee(new BigDecimal("0.00"))
                .version(0)
                .build());
    }

    @Test
    public void testOrderPay1_WxStoreMerchantOrderServiceReturnsNoItems() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .memberIntegral(0)
                .build();
        final WxStoreTradeOrderDetailsRespDTO expectedResult = WxStoreTradeOrderDetailsRespDTO.builder()
                .discountFee(new BigDecimal("0.00"))
                .discountFeeDetailDTOS(Arrays.asList(new DiscountFeeDetailDTO()))
                .errorMsg("errorMsg")
                .unMemberTotalAmount(new BigDecimal("0.00"))
                .saveZero(new BigDecimal("0.00"))
                .memberCardStatus(0)
                .memberVolumeStatus(0)
                .memberCardFee(new BigDecimal("0.00"))
                .memberVolumeFee(new BigDecimal("0.00"))
                .cardGuid("cardGuid")
                .cardName("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .systemManagementGuid("systemManagementGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .memberIntegral(0)
                .build())).thenReturn(true);
        when(mockWxStoreTableClientService.getOrderGuid("diningTableGuid")).thenReturn("combine");

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("combine");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        dineinOrderDetailRespDTO.setVersion(0);
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("combine")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("diningTableGuid")).thenReturn("orderGuid");
        when(mockWxStoreMerchantOrderService.tableListByMerchantGuidOrOrderGuid(WxStorePendingOrdersQuery.builder()
                .orderStates(Arrays.asList(0))
                .orderGuid("orderGuid")
                .combine("combine")
                .build())).thenReturn(Collections.emptyList());

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .systemManagementGuid("systemManagementGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("combine")
                .mode(0)
                .orderFee(new BigDecimal("0.00"))
                .memberInfoGuid("memberInfoGuid")
                .purchaseGroupFee(new BigDecimal("0.00"))
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        // Configure WxStoreSessionDetailsService.getMemberInfoAndCardList(...).
        final ResponseMemberAndCardInfoDTO responseMemberAndCardInfoDTO = new ResponseMemberAndCardInfoDTO();
        final ResponseMemberInfoDTO memberInfoDTO = new ResponseMemberInfoDTO();
        memberInfoDTO.setMemberInfoGuid("memberInfoGuid");
        memberInfoDTO.setStateCode(0);
        responseMemberAndCardInfoDTO.setMemberInfoDTO(memberInfoDTO);
        final ResponseMemberCard responseMemberCard = new ResponseMemberCard();
        responseMemberCard.setCardGuid("cardGuid");
        responseMemberCard.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCard.setSystemManagementGuid("systemManagementGuid");
        responseMemberAndCardInfoDTO.setMemberCardListRespDTOs(Arrays.asList(responseMemberCard));
        when(mockWxStoreSessionDetailsService.getMemberInfoAndCardList("enterpriseGuid", "storeGuid",
                "openId")).thenReturn(responseMemberAndCardInfoDTO);

        // Configure HsaBaseClientService.getMemberVolume(...).
        final ResponseMemberInfoVolume responseMemberInfoVolume = new ResponseMemberInfoVolume();
        responseMemberInfoVolume.setMayUseVolumeNum(0);
        final MemberInfoVolume memberInfoVolume = new MemberInfoVolume();
        memberInfoVolume.setMayUseVolume(0);
        memberInfoVolume.setVolumeState(0);
        memberInfoVolume.setVolumeType(0);
        memberInfoVolume.setVolumeCode("volumeCode");
        responseMemberInfoVolume.setMemberVolumeList(Arrays.asList(memberInfoVolume));
        final ResponseModel<ResponseMemberInfoVolume> responseMemberInfoVolumeResponseModel = new ResponseModel<>(
                responseMemberInfoVolume);
        final RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO = new RequestMemberInfoVolumeQuery();
        memberInfoVolumeQueryReqDTO.setMemberInfoGuid("memberInfoGuid");
        memberInfoVolumeQueryReqDTO.setVolumeType(0);
        memberInfoVolumeQueryReqDTO.setStoreGuid("storeGuid");
        memberInfoVolumeQueryReqDTO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVolumeQueryReqDTO.setBrandGuid("brandGuid");
        memberInfoVolumeQueryReqDTO.setMayUseVolume(0);
        memberInfoVolumeQueryReqDTO.setExcludeBind(0);
        when(mockHsaBaseClientService.getMemberVolume(memberInfoVolumeQueryReqDTO))
                .thenReturn(responseMemberInfoVolumeResponseModel);

        // Configure WxStoreSessionDetailsService.calculateDetails(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("combine");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        dineinOrderDetailRespDTO1.setState(0);
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO1.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO1.setTip("tip");
        dineinOrderDetailRespDTO1.setVersion(0);
        final CorrectResult<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOCorrectResult = new CorrectResult<>(
                "errorMsg", 0, dineinOrderDetailRespDTO1);
        when(mockWxStoreSessionDetailsService.calculateDetails("combine", "openId", "memberInfoCardGuid", 0,
                "volumeCode")).thenReturn(dineinOrderDetailRespDTOCorrectResult);

        // Configure WxStorePersonOrderDetailsService.getWxStoreTradeOrderDetails(...).
        final WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO = new WxStoreTradeOrderDetailsDTO();
        wxStoreTradeOrderDetailsDTO.setMainTable(0);
        wxStoreTradeOrderDetailsDTO.setGuid("4365c531-1931-4dce-984d-0a0678cdfbef");
        wxStoreTradeOrderDetailsDTO.setWxGuid("wxGuid");
        wxStoreTradeOrderDetailsDTO.setBatchId("batchId");
        wxStoreTradeOrderDetailsDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .memberIntegral(0)
                .build());
        final DineinOrderDetailRespDTO mainOrderDetails = new DineinOrderDetailRespDTO();
        mainOrderDetails.setGuid("combine");
        mainOrderDetails.setTradeMode(0);
        mainOrderDetails.setOrderFee(new BigDecimal("0.00"));
        mainOrderDetails.setActuallyPayFee(new BigDecimal("0.00"));
        mainOrderDetails.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO2 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO2.setDiscountType(0);
        discountFeeDetailDTO2.setDiscountFee(new BigDecimal("0.00"));
        mainOrderDetails.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO2));
        mainOrderDetails.setOrderNo("orderNo");
        mainOrderDetails.setState(0);
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        mainOrderDetails.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        mainOrderDetails.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        mainOrderDetails.setTip("tip");
        mainOrderDetails.setVersion(0);
        when(mockWxStorePersonOrderDetailsService.getWxStoreTradeOrderDetails(mainOrderDetails))
                .thenReturn(wxStoreTradeOrderDetailsDTO);

        // Configure WxStorePersonOrderDetailsService.getWxStoreTradeOrderDetailsResp(...).
        final WxStoreTradeOrderDetailsRespDTO wxStoreTradeOrderDetailsRespDTO = WxStoreTradeOrderDetailsRespDTO.builder()
                .discountFee(new BigDecimal("0.00"))
                .discountFeeDetailDTOS(Arrays.asList(new DiscountFeeDetailDTO()))
                .errorMsg("errorMsg")
                .unMemberTotalAmount(new BigDecimal("0.00"))
                .saveZero(new BigDecimal("0.00"))
                .memberCardStatus(0)
                .memberVolumeStatus(0)
                .memberCardFee(new BigDecimal("0.00"))
                .memberVolumeFee(new BigDecimal("0.00"))
                .cardGuid("cardGuid")
                .cardName("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .systemManagementGuid("systemManagementGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .build();
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO2 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO2.setGuid("combine");
        dineinOrderDetailRespDTO2.setTradeMode(0);
        dineinOrderDetailRespDTO2.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO3 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO3.setDiscountType(0);
        discountFeeDetailDTO3.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO3));
        dineinOrderDetailRespDTO2.setOrderNo("orderNo");
        dineinOrderDetailRespDTO2.setState(0);
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineinOrderDetailRespDTO2.setDineInItemDTOS(Arrays.asList(dineInItemDTO3));
        dineinOrderDetailRespDTO2.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO2.setTip("tip");
        dineinOrderDetailRespDTO2.setVersion(0);
        final WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO1 = new WxStoreTradeOrderDetailsDTO();
        wxStoreTradeOrderDetailsDTO1.setMainTable(0);
        wxStoreTradeOrderDetailsDTO1.setGuid("4365c531-1931-4dce-984d-0a0678cdfbef");
        wxStoreTradeOrderDetailsDTO1.setWxGuid("wxGuid");
        wxStoreTradeOrderDetailsDTO1.setBatchId("batchId");
        wxStoreTradeOrderDetailsDTO1.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .memberIntegral(0)
                .build());
        when(mockWxStorePersonOrderDetailsService.getWxStoreTradeOrderDetailsResp(dineinOrderDetailRespDTO2,
                wxStoreTradeOrderDetailsDTO1, WxStoreAdvanceConsumerReqDTO.builder()
                        .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                                .openId("openId")
                                .enterpriseGuid("enterpriseGuid")
                                .storeGuid("storeGuid")
                                .diningTableGuid("diningTableGuid")
                                .brandGuid("brandGuid")
                                .build())
                        .memberIntegral(0)
                        .build())).thenReturn(wxStoreTradeOrderDetailsRespDTO);

        // Run the test
        final WxStoreTradeOrderDetailsRespDTO result = wxStoreOrderPayServiceImplUnderTest.orderPay(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).saveOrderGuid("diningTableGuid", "combine");
        verify(mockWxStoreSessionDetailsService).savePrepay(WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .systemManagementGuid("systemManagementGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("combine")
                .mode(0)
                .orderFee(new BigDecimal("0.00"))
                .memberInfoGuid("memberInfoGuid")
                .purchaseGroupFee(new BigDecimal("0.00"))
                .version(0)
                .build());
    }

    @Test
    public void testOrderPay1_WxStoreSessionDetailsServiceCalculateDetailsReturnsNoItem() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .memberIntegral(0)
                .build();
        final WxStoreTradeOrderDetailsRespDTO expectedResult = WxStoreTradeOrderDetailsRespDTO.builder()
                .discountFee(new BigDecimal("0.00"))
                .discountFeeDetailDTOS(Arrays.asList(new DiscountFeeDetailDTO()))
                .errorMsg("errorMsg")
                .unMemberTotalAmount(new BigDecimal("0.00"))
                .saveZero(new BigDecimal("0.00"))
                .memberCardStatus(0)
                .memberVolumeStatus(0)
                .memberCardFee(new BigDecimal("0.00"))
                .memberVolumeFee(new BigDecimal("0.00"))
                .cardGuid("cardGuid")
                .cardName("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .systemManagementGuid("systemManagementGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .memberIntegral(0)
                .build())).thenReturn(false);
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId")).thenReturn("combine");

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .systemManagementGuid("systemManagementGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("combine")
                .mode(0)
                .orderFee(new BigDecimal("0.00"))
                .memberInfoGuid("memberInfoGuid")
                .purchaseGroupFee(new BigDecimal("0.00"))
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        // Configure WxStoreSessionDetailsService.getMemberInfoAndCardList(...).
        final ResponseMemberAndCardInfoDTO responseMemberAndCardInfoDTO = new ResponseMemberAndCardInfoDTO();
        final ResponseMemberInfoDTO memberInfoDTO = new ResponseMemberInfoDTO();
        memberInfoDTO.setMemberInfoGuid("memberInfoGuid");
        memberInfoDTO.setStateCode(0);
        responseMemberAndCardInfoDTO.setMemberInfoDTO(memberInfoDTO);
        final ResponseMemberCard responseMemberCard = new ResponseMemberCard();
        responseMemberCard.setCardGuid("cardGuid");
        responseMemberCard.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCard.setSystemManagementGuid("systemManagementGuid");
        responseMemberAndCardInfoDTO.setMemberCardListRespDTOs(Arrays.asList(responseMemberCard));
        when(mockWxStoreSessionDetailsService.getMemberInfoAndCardList("enterpriseGuid", "storeGuid",
                "openId")).thenReturn(responseMemberAndCardInfoDTO);

        // Configure HsaBaseClientService.getMemberVolume(...).
        final ResponseMemberInfoVolume responseMemberInfoVolume = new ResponseMemberInfoVolume();
        responseMemberInfoVolume.setMayUseVolumeNum(0);
        final MemberInfoVolume memberInfoVolume = new MemberInfoVolume();
        memberInfoVolume.setMayUseVolume(0);
        memberInfoVolume.setVolumeState(0);
        memberInfoVolume.setVolumeType(0);
        memberInfoVolume.setVolumeCode("volumeCode");
        responseMemberInfoVolume.setMemberVolumeList(Arrays.asList(memberInfoVolume));
        final ResponseModel<ResponseMemberInfoVolume> responseMemberInfoVolumeResponseModel = new ResponseModel<>(
                responseMemberInfoVolume);
        final RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO = new RequestMemberInfoVolumeQuery();
        memberInfoVolumeQueryReqDTO.setMemberInfoGuid("memberInfoGuid");
        memberInfoVolumeQueryReqDTO.setVolumeType(0);
        memberInfoVolumeQueryReqDTO.setStoreGuid("storeGuid");
        memberInfoVolumeQueryReqDTO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVolumeQueryReqDTO.setBrandGuid("brandGuid");
        memberInfoVolumeQueryReqDTO.setMayUseVolume(0);
        memberInfoVolumeQueryReqDTO.setExcludeBind(0);
        when(mockHsaBaseClientService.getMemberVolume(memberInfoVolumeQueryReqDTO))
                .thenReturn(responseMemberInfoVolumeResponseModel);

        when(mockWxStoreSessionDetailsService.calculateDetails("combine", "openId", "memberInfoCardGuid", 0,
                "volumeCode")).thenReturn(CorrectResult.changeFailed());

        // Configure WxStorePersonOrderDetailsService.getWxStoreTradeOrderDetails(...).
        final WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO = new WxStoreTradeOrderDetailsDTO();
        wxStoreTradeOrderDetailsDTO.setMainTable(0);
        wxStoreTradeOrderDetailsDTO.setGuid("4365c531-1931-4dce-984d-0a0678cdfbef");
        wxStoreTradeOrderDetailsDTO.setWxGuid("wxGuid");
        wxStoreTradeOrderDetailsDTO.setBatchId("batchId");
        wxStoreTradeOrderDetailsDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .memberIntegral(0)
                .build());
        final DineinOrderDetailRespDTO mainOrderDetails = new DineinOrderDetailRespDTO();
        mainOrderDetails.setGuid("combine");
        mainOrderDetails.setTradeMode(0);
        mainOrderDetails.setOrderFee(new BigDecimal("0.00"));
        mainOrderDetails.setActuallyPayFee(new BigDecimal("0.00"));
        mainOrderDetails.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        mainOrderDetails.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        mainOrderDetails.setOrderNo("orderNo");
        mainOrderDetails.setState(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        mainOrderDetails.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        mainOrderDetails.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        mainOrderDetails.setTip("tip");
        mainOrderDetails.setVersion(0);
        when(mockWxStorePersonOrderDetailsService.getWxStoreTradeOrderDetails(mainOrderDetails))
                .thenReturn(wxStoreTradeOrderDetailsDTO);

        // Configure WxStorePersonOrderDetailsService.getWxStoreTradeOrderDetailsResp(...).
        final WxStoreTradeOrderDetailsRespDTO wxStoreTradeOrderDetailsRespDTO = WxStoreTradeOrderDetailsRespDTO.builder()
                .discountFee(new BigDecimal("0.00"))
                .discountFeeDetailDTOS(Arrays.asList(new DiscountFeeDetailDTO()))
                .errorMsg("errorMsg")
                .unMemberTotalAmount(new BigDecimal("0.00"))
                .saveZero(new BigDecimal("0.00"))
                .memberCardStatus(0)
                .memberVolumeStatus(0)
                .memberCardFee(new BigDecimal("0.00"))
                .memberVolumeFee(new BigDecimal("0.00"))
                .cardGuid("cardGuid")
                .cardName("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .systemManagementGuid("systemManagementGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .build();
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("combine");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        dineinOrderDetailRespDTO.setVersion(0);
        final WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO1 = new WxStoreTradeOrderDetailsDTO();
        wxStoreTradeOrderDetailsDTO1.setMainTable(0);
        wxStoreTradeOrderDetailsDTO1.setGuid("4365c531-1931-4dce-984d-0a0678cdfbef");
        wxStoreTradeOrderDetailsDTO1.setWxGuid("wxGuid");
        wxStoreTradeOrderDetailsDTO1.setBatchId("batchId");
        wxStoreTradeOrderDetailsDTO1.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .memberIntegral(0)
                .build());
        when(mockWxStorePersonOrderDetailsService.getWxStoreTradeOrderDetailsResp(dineinOrderDetailRespDTO,
                wxStoreTradeOrderDetailsDTO1, WxStoreAdvanceConsumerReqDTO.builder()
                        .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                                .openId("openId")
                                .enterpriseGuid("enterpriseGuid")
                                .storeGuid("storeGuid")
                                .diningTableGuid("diningTableGuid")
                                .brandGuid("brandGuid")
                                .build())
                        .memberIntegral(0)
                        .build())).thenReturn(wxStoreTradeOrderDetailsRespDTO);

        // Run the test
        final WxStoreTradeOrderDetailsRespDTO result = wxStoreOrderPayServiceImplUnderTest.orderPay(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).savePrepay(WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .systemManagementGuid("systemManagementGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("combine")
                .mode(0)
                .orderFee(new BigDecimal("0.00"))
                .memberInfoGuid("memberInfoGuid")
                .purchaseGroupFee(new BigDecimal("0.00"))
                .version(0)
                .build());
    }

    @Test
    public void testOrderPay1_WxStoreSessionDetailsServiceCalculateDetailsReturnsFailure() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .memberIntegral(0)
                .build();
        final WxStoreTradeOrderDetailsRespDTO expectedResult = WxStoreTradeOrderDetailsRespDTO.builder()
                .discountFee(new BigDecimal("0.00"))
                .discountFeeDetailDTOS(Arrays.asList(new DiscountFeeDetailDTO()))
                .errorMsg("errorMsg")
                .unMemberTotalAmount(new BigDecimal("0.00"))
                .saveZero(new BigDecimal("0.00"))
                .memberCardStatus(0)
                .memberVolumeStatus(0)
                .memberCardFee(new BigDecimal("0.00"))
                .memberVolumeFee(new BigDecimal("0.00"))
                .cardGuid("cardGuid")
                .cardName("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .systemManagementGuid("systemManagementGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .build();
        when(mockWxStoreMenuDetailsService.judgeOrderType(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .memberIntegral(0)
                .build())).thenReturn(false);
        when(mockWxStoreSessionDetailsService.getFastOrderGuid("storeGuid", "openId")).thenReturn("combine");

        // Configure WxStoreSessionDetailsService.getPrepay(...).
        final WxPrepayReqDTO wxPrepayReqDTO = WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .systemManagementGuid("systemManagementGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("combine")
                .mode(0)
                .orderFee(new BigDecimal("0.00"))
                .memberInfoGuid("memberInfoGuid")
                .purchaseGroupFee(new BigDecimal("0.00"))
                .version(0)
                .build();
        when(mockWxStoreSessionDetailsService.getPrepay("storeGuid", "openId")).thenReturn(wxPrepayReqDTO);

        // Configure WxStoreSessionDetailsService.getMemberInfoAndCardList(...).
        final ResponseMemberAndCardInfoDTO responseMemberAndCardInfoDTO = new ResponseMemberAndCardInfoDTO();
        final ResponseMemberInfoDTO memberInfoDTO = new ResponseMemberInfoDTO();
        memberInfoDTO.setMemberInfoGuid("memberInfoGuid");
        memberInfoDTO.setStateCode(0);
        responseMemberAndCardInfoDTO.setMemberInfoDTO(memberInfoDTO);
        final ResponseMemberCard responseMemberCard = new ResponseMemberCard();
        responseMemberCard.setCardGuid("cardGuid");
        responseMemberCard.setMemberInfoCardGuid("memberInfoCardGuid");
        responseMemberCard.setSystemManagementGuid("systemManagementGuid");
        responseMemberAndCardInfoDTO.setMemberCardListRespDTOs(Arrays.asList(responseMemberCard));
        when(mockWxStoreSessionDetailsService.getMemberInfoAndCardList("enterpriseGuid", "storeGuid",
                "openId")).thenReturn(responseMemberAndCardInfoDTO);

        // Configure HsaBaseClientService.getMemberVolume(...).
        final ResponseMemberInfoVolume responseMemberInfoVolume = new ResponseMemberInfoVolume();
        responseMemberInfoVolume.setMayUseVolumeNum(0);
        final MemberInfoVolume memberInfoVolume = new MemberInfoVolume();
        memberInfoVolume.setMayUseVolume(0);
        memberInfoVolume.setVolumeState(0);
        memberInfoVolume.setVolumeType(0);
        memberInfoVolume.setVolumeCode("volumeCode");
        responseMemberInfoVolume.setMemberVolumeList(Arrays.asList(memberInfoVolume));
        final ResponseModel<ResponseMemberInfoVolume> responseMemberInfoVolumeResponseModel = new ResponseModel<>(
                responseMemberInfoVolume);
        final RequestMemberInfoVolumeQuery memberInfoVolumeQueryReqDTO = new RequestMemberInfoVolumeQuery();
        memberInfoVolumeQueryReqDTO.setMemberInfoGuid("memberInfoGuid");
        memberInfoVolumeQueryReqDTO.setVolumeType(0);
        memberInfoVolumeQueryReqDTO.setStoreGuid("storeGuid");
        memberInfoVolumeQueryReqDTO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVolumeQueryReqDTO.setBrandGuid("brandGuid");
        memberInfoVolumeQueryReqDTO.setMayUseVolume(0);
        memberInfoVolumeQueryReqDTO.setExcludeBind(0);
        when(mockHsaBaseClientService.getMemberVolume(memberInfoVolumeQueryReqDTO))
                .thenReturn(responseMemberInfoVolumeResponseModel);

        // Configure WxStoreSessionDetailsService.calculateDetails(...).
        final CorrectResult<DineinOrderDetailRespDTO> dineinOrderDetailRespDTOCorrectResult = CorrectResult.changeFailed();
        when(mockWxStoreSessionDetailsService.calculateDetails("combine", "openId", "memberInfoCardGuid", 0,
                "volumeCode")).thenReturn(dineinOrderDetailRespDTOCorrectResult);

        // Configure WxStorePersonOrderDetailsService.getWxStoreTradeOrderDetails(...).
        final WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO = new WxStoreTradeOrderDetailsDTO();
        wxStoreTradeOrderDetailsDTO.setMainTable(0);
        wxStoreTradeOrderDetailsDTO.setGuid("4365c531-1931-4dce-984d-0a0678cdfbef");
        wxStoreTradeOrderDetailsDTO.setWxGuid("wxGuid");
        wxStoreTradeOrderDetailsDTO.setBatchId("batchId");
        wxStoreTradeOrderDetailsDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .memberIntegral(0)
                .build());
        final DineinOrderDetailRespDTO mainOrderDetails = new DineinOrderDetailRespDTO();
        mainOrderDetails.setGuid("combine");
        mainOrderDetails.setTradeMode(0);
        mainOrderDetails.setOrderFee(new BigDecimal("0.00"));
        mainOrderDetails.setActuallyPayFee(new BigDecimal("0.00"));
        mainOrderDetails.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(0);
        discountFeeDetailDTO.setDiscountFee(new BigDecimal("0.00"));
        mainOrderDetails.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        mainOrderDetails.setOrderNo("orderNo");
        mainOrderDetails.setState(0);
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        mainOrderDetails.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        mainOrderDetails.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        mainOrderDetails.setTip("tip");
        mainOrderDetails.setVersion(0);
        when(mockWxStorePersonOrderDetailsService.getWxStoreTradeOrderDetails(mainOrderDetails))
                .thenReturn(wxStoreTradeOrderDetailsDTO);

        // Configure WxStorePersonOrderDetailsService.getWxStoreTradeOrderDetailsResp(...).
        final WxStoreTradeOrderDetailsRespDTO wxStoreTradeOrderDetailsRespDTO = WxStoreTradeOrderDetailsRespDTO.builder()
                .discountFee(new BigDecimal("0.00"))
                .discountFeeDetailDTOS(Arrays.asList(new DiscountFeeDetailDTO()))
                .errorMsg("errorMsg")
                .unMemberTotalAmount(new BigDecimal("0.00"))
                .saveZero(new BigDecimal("0.00"))
                .memberCardStatus(0)
                .memberVolumeStatus(0)
                .memberCardFee(new BigDecimal("0.00"))
                .memberVolumeFee(new BigDecimal("0.00"))
                .cardGuid("cardGuid")
                .cardName("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .systemManagementGuid("systemManagementGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .build();
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("combine");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        discountFeeDetailDTO1.setDiscountType(0);
        discountFeeDetailDTO1.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setTip("tip");
        dineinOrderDetailRespDTO.setVersion(0);
        final WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO1 = new WxStoreTradeOrderDetailsDTO();
        wxStoreTradeOrderDetailsDTO1.setMainTable(0);
        wxStoreTradeOrderDetailsDTO1.setGuid("4365c531-1931-4dce-984d-0a0678cdfbef");
        wxStoreTradeOrderDetailsDTO1.setWxGuid("wxGuid");
        wxStoreTradeOrderDetailsDTO1.setBatchId("batchId");
        wxStoreTradeOrderDetailsDTO1.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .openId("openId")
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .diningTableGuid("diningTableGuid")
                        .brandGuid("brandGuid")
                        .build())
                .memberIntegral(0)
                .build());
        when(mockWxStorePersonOrderDetailsService.getWxStoreTradeOrderDetailsResp(dineinOrderDetailRespDTO,
                wxStoreTradeOrderDetailsDTO1, WxStoreAdvanceConsumerReqDTO.builder()
                        .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                                .openId("openId")
                                .enterpriseGuid("enterpriseGuid")
                                .storeGuid("storeGuid")
                                .diningTableGuid("diningTableGuid")
                                .brandGuid("brandGuid")
                                .build())
                        .memberIntegral(0)
                        .build())).thenReturn(wxStoreTradeOrderDetailsRespDTO);

        // Run the test
        final WxStoreTradeOrderDetailsRespDTO result = wxStoreOrderPayServiceImplUnderTest.orderPay(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreSessionDetailsService).savePrepay(WxPrepayReqDTO.builder()
                .enterpriseGuid("enterpriseGuid")
                .brandGuid("brandGuid")
                .storeGuid("storeGuid")
                .tableGuid("diningTableGuid")
                .openId("openId")
                .payAmount(new BigDecimal("0.00"))
                .memberCardGuid("cardGuid")
                .cardGuid("cardGuid")
                .memberInfoCardGuid("memberInfoCardGuid")
                .systemManagementGuid("systemManagementGuid")
                .memberIntegral(0)
                .volumeCode("volumeCode")
                .orderGuid("combine")
                .mode(0)
                .orderFee(new BigDecimal("0.00"))
                .memberInfoGuid("memberInfoGuid")
                .purchaseGroupFee(new BigDecimal("0.00"))
                .version(0)
                .build());
    }
}
