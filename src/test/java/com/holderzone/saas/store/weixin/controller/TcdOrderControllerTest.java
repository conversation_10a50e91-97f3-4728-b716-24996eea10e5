package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.weixin.service.TcdOrderService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(TcdOrderController.class)
public class TcdOrderControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private TcdOrderService mockOrderService;

    @Test
    public void testAsyncTcdOrder() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(get("/deal/tcd/order/async/{orderGuid}", "orderGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockOrderService).asyncOrder("orderGuid");
    }
}
