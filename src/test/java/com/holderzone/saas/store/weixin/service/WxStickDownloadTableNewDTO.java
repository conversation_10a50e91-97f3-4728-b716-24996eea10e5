package com.holderzone.saas.store.weixin.service;

import com.holderzone.saas.store.dto.weixin.WxStickDownloadTableDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/7/6 16:12
 * @description
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("微信桌贴下载桌位DTO new")
public class WxStickDownloadTableNewDTO extends WxStickDownloadTableDTO {
    @ApiModelProperty("门店id")
    private String storeGuid;

    @ApiModelProperty("门店名称")
    private String storeName;
}
