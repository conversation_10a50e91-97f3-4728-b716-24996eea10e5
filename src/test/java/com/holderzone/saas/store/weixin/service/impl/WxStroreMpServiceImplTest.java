package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.saas.store.dto.weixin.req.WxAuthorizeReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxCommonReqDTO;
import com.holderzone.saas.store.weixin.config.WeChatConfig;
import com.holderzone.saas.store.weixin.config.WxH5AccountConfig;
import com.holderzone.saas.store.weixin.config.WxH5OpenIdRelationConfig;
import com.holderzone.saas.store.weixin.config.WxOpenAccountConfig;
import com.holderzone.saas.store.weixin.entity.domain.WxQrRedirectDo;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerInfoDO;
import com.holderzone.saas.store.weixin.entity.query.WxQrCodeUrlQuery;
import com.holderzone.saas.store.weixin.mapper.WxQrRedirectMapper;
import com.holderzone.saas.store.weixin.service.WxQrCodeInfoService;
import com.holderzone.saas.store.weixin.service.WxSaasMpService;
import com.holderzone.saas.store.weixin.service.WxStoreAuthorizerInfoService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import com.holderzone.saas.store.weixin.utils.WxConsumerParsUtil;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import me.chanjar.weixin.open.api.WxOpenComponentService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStroreMpServiceImplTest {

    @Mock
    private WxMpService mockWxMpService;
    @Mock
    private WxMpService mockWorkBenchService;
    @Mock
    private WxQrCodeInfoService mockWxQrCodeInfoService;
    @Mock
    private WxConsumerParsUtil mockWxConsumerParsUtil;
    @Mock
    private DynamicHelper mockDynamicHelper;
    @Mock
    private WxStoreAuthorizerInfoService mockWxStoreAuthorizerInfoService;
    @Mock
    private WxSaasMpService mockWxSaasMpService;
    @Mock
    private WxOpenComponentService mockWxOpenComponentService;
    @Mock
    private WeChatConfig mockWeChatConfig;
    @Mock
    private WxOpenAccountConfig mockWxOpenAccountConfig;
    @Mock
    private WxH5AccountConfig mockWxH5AccountConfig;
    @Mock
    private WxH5OpenIdRelationConfig mockWxH5OpenIdRelationConfig;
    @Mock
    private WxQrRedirectMapper mockWxQrRedirectMapper;

    private WxStroreMpServiceImpl wxStroreMpServiceImplUnderTest;

    @Before
    public void setUp() {
        wxStroreMpServiceImplUnderTest = new WxStroreMpServiceImpl();
        wxStroreMpServiceImplUnderTest.wxMpService = mockWxMpService;
        wxStroreMpServiceImplUnderTest.workBenchService = mockWorkBenchService;
        wxStroreMpServiceImplUnderTest.wxQrCodeInfoService = mockWxQrCodeInfoService;
        wxStroreMpServiceImplUnderTest.wxConsumerParsUtil = mockWxConsumerParsUtil;
        wxStroreMpServiceImplUnderTest.dynamicHelper = mockDynamicHelper;
        wxStroreMpServiceImplUnderTest.wxStoreAuthorizerInfoService = mockWxStoreAuthorizerInfoService;
        wxStroreMpServiceImplUnderTest.wxSaasMpService = mockWxSaasMpService;
        wxStroreMpServiceImplUnderTest.wxOpenComponentService = mockWxOpenComponentService;
        wxStroreMpServiceImplUnderTest.weChatConfig = mockWeChatConfig;
        wxStroreMpServiceImplUnderTest.wxOpenAccountConfig = mockWxOpenAccountConfig;
        wxStroreMpServiceImplUnderTest.wxH5AccountConfig = mockWxH5AccountConfig;
        wxStroreMpServiceImplUnderTest.wxH5OpenIdRelationConfig = mockWxH5OpenIdRelationConfig;
        wxStroreMpServiceImplUnderTest.wxQrRedirectMapper = mockWxQrRedirectMapper;
        wxStroreMpServiceImplUnderTest.qrRedirectUrl = "qrRedirectUrl";
        wxStroreMpServiceImplUnderTest.componentAuthorizerFlag = false;
    }

    @Test
    public void testGetQrCodeUrl() {
        // Setup
        final WxQrCodeUrlQuery wxQrCodeUrlQuery = new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName",
                "areaGuid", "areaName", "tableGuid", "tableName", 0, "brandGuid", "appId", false, false,
                "myselfOpenId");

        // Configure WxStoreAuthorizerInfoService.getByBrandId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "f4172d59-8c85-4801-867f-db516a703501", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByBrandId("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWeChatConfig.getInternalAppId()).thenReturn("appId");
        when(mockWxQrCodeInfoService.getSceneStr(
                new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid",
                        "tableName", 0, "brandGuid", "appId", false, false, "myselfOpenId"))).thenReturn("result");
        when(mockWeChatConfig.getBaseQrCodeUrl()).thenReturn("result");
        when(mockWxOpenComponentService.oauth2buildAuthorizationUrl("appId", "s1", WxConsts.OAuth2Scope.SNSAPI_USERINFO,
                "holder")).thenReturn("result");

        // Run the test
        final String result = wxStroreMpServiceImplUnderTest.getQrCodeUrl(wxQrCodeUrlQuery);

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testGetAuthorizeUrl() {
        // Setup
        final WxQrCodeUrlQuery wxQrCodeUrlQuery = new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName",
                "areaGuid", "areaName", "tableGuid", "tableName", 0, "brandGuid", "appId", false, false,
                "myselfOpenId");
        when(mockWxQrCodeInfoService.getSceneStr(
                new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid",
                        "tableName", 0, "brandGuid", "appId", false, false, "myselfOpenId"))).thenReturn("result");
        when(mockWeChatConfig.getBaseQrCodeUrl()).thenReturn("result");
        when(mockWxH5OpenIdRelationConfig.getBrandGuids()).thenReturn(Arrays.asList("value"));
        when(mockWxOpenAccountConfig.getComponentAppId()).thenReturn("result");

        // Run the test
        final String result = wxStroreMpServiceImplUnderTest.getAuthorizeUrl(wxQrCodeUrlQuery, "","","");

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testGetAuthorizeUrl_WxH5OpenIdRelationConfigReturnsNoItems() {
        // Setup
        final WxQrCodeUrlQuery wxQrCodeUrlQuery = new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName",
                "areaGuid", "areaName", "tableGuid", "tableName", 0, "brandGuid", "appId", false, false,
                "myselfOpenId");
        when(mockWxQrCodeInfoService.getSceneStr(
                new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid",
                        "tableName", 0, "brandGuid", "appId", false, false, "myselfOpenId"))).thenReturn("result");
        when(mockWeChatConfig.getBaseQrCodeUrl()).thenReturn("result");
        when(mockWxH5OpenIdRelationConfig.getBrandGuids()).thenReturn(Collections.emptyList());

        // Run the test
        final String result = wxStroreMpServiceImplUnderTest.getAuthorizeUrl(wxQrCodeUrlQuery, "","","");

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testGetUserInfo() throws Exception {
        // Setup
        final WxAuthorizeReqDTO wxAuthorizeReqDTO = new WxAuthorizeReqDTO();
        wxAuthorizeReqDTO.setCode("code");
        wxAuthorizeReqDTO.setState("state");
        wxAuthorizeReqDTO.setEventKey("eventKey");
        wxAuthorizeReqDTO.setAppId("appId");
        wxAuthorizeReqDTO.setWeixinToken("weixinToken");

        when(mockWorkBenchService.getOAuth2Service()).thenReturn(null);
        when(mockWeChatConfig.getWorkbenchUrl()).thenReturn("result");
        when(mockWxConsumerParsUtil.changeDateSourceByEventKey("eventKey")).thenReturn("eventKey");

        // Configure WxStoreAuthorizerInfoService.getByAppId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "f4172d59-8c85-4801-867f-db516a703501", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByAppId("appId")).thenReturn(wxStoreAuthorizerInfoDO);

        // Configure WxConsumerParsUtil.parse2Consumer(...).
        final WxMpUser wxMpUser = new WxMpUser();
        wxMpUser.setSubscribe(false);
        wxMpUser.setOpenId("openId");
        wxMpUser.setNickname("nickname");
        wxMpUser.setSexDesc("sexDesc");
        wxMpUser.setSex(0);
        when(mockWxConsumerParsUtil.parse2Consumer("eventKey", wxMpUser,
                new WxStoreAuthorizerInfoDO(0L, "f4172d59-8c85-4801-867f-db516a703501", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn("result");

        // Run the test
        final String result = wxStroreMpServiceImplUnderTest.getUserInfo(wxAuthorizeReqDTO);

        // Verify the results
        assertEquals("result", result);
    }

    @Test
    public void testVerifyMessage() {
        // Setup
        final WxCommonReqDTO wxCommonReqDTO = new WxCommonReqDTO();
        wxCommonReqDTO.setSignature("signature");
        wxCommonReqDTO.setEchostr("非法请求");
        wxCommonReqDTO.setTimestamp("timestamp");
        wxCommonReqDTO.setNonce("nonce");
        wxCommonReqDTO.setEncrypt_type("encrypt_type");

        when(mockWxMpService.checkSignature("timestamp", "nonce", "signature")).thenReturn(false);

        // Run the test
        final String result = wxStroreMpServiceImplUnderTest.verifyMessage(wxCommonReqDTO);

        // Verify the results
        assertEquals("非法请求", result);
    }

    @Test
    public void testShortenUrl() throws Exception {
        // Setup
        // Run the test
        final String result = wxStroreMpServiceImplUnderTest.shortenUrl("longUrl", "tableGuid", 0);

        // Verify the results
        assertEquals("longUrl", result);
        verify(mockWxQrRedirectMapper).insertMaster(
                new WxQrRedirectDo(0L, "longUrl", "tableGuid", 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0), "staffGuid",
                        "staffName"));
    }
}
