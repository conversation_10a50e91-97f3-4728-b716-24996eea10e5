package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.weixin.*;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreCityListRespDTO;
import com.holderzone.saas.store.weixin.service.WxStoreListService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Arrays;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxStoreListController.class)
public class WxStoreListControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStoreListService mockWxStoreListService;

    @Test
    public void testListStoreConfig() throws Exception {
        // Setup
        // Configure WxStoreListService.listStoreConfig(...).
        final WxStoreListDTO wxStoreListDTO = new WxStoreListDTO("enterpriseGuid", "brandGuid", "brandName",
                "brandLogoUrl",
                Arrays.asList(new WxStoreConfigDTO(new WxStoreQueueConfigDTO(false, false, false, 0, false))), 0);
        when(mockWxStoreListService.listStoreConfig(
                new WxPositionDTO(new BigDecimal("0.00"), new BigDecimal("0.00"), "cityCode", "cityName")))
                .thenReturn(wxStoreListDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store/list_store_config")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListStoreCity() throws Exception {
        // Setup
        // Configure WxStoreListService.listStoreCity(...).
        final WxStoreCityListRespDTO wxStoreCityListRespDTO = new WxStoreCityListRespDTO();
        final WxStoreCityDTO wxStoreCityDTO = new WxStoreCityDTO();
        wxStoreCityDTO.setCityName("cityName");
        wxStoreCityDTO.setCode("code");
        wxStoreCityDTO.setCount(0);
        wxStoreCityListRespDTO.setCityList(Arrays.asList(wxStoreCityDTO));
        final WxStoreCityDTO wxStoreCityDTO1 = new WxStoreCityDTO();
        wxStoreCityDTO1.setCityName("cityName");
        wxStoreCityDTO1.setCode("code");
        wxStoreCityDTO1.setCount(0);
        wxStoreCityListRespDTO.setOtherCities(Arrays.asList(wxStoreCityDTO1));
        when(mockWxStoreListService.listStoreCity(
                new WxPositionDTO(new BigDecimal("0.00"), new BigDecimal("0.00"), "cityCode", "cityName")))
                .thenReturn(wxStoreCityListRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store/list_store_city")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
