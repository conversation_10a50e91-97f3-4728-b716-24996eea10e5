package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.weixin.req.WxReserveConfigDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxReserveConfigDO;
import com.holderzone.saas.store.weixin.service.WxOrganizationService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxReserveConfigServiceImplTest {

    @Mock
    private WxOrganizationService mockWxOrganizationService;
    @Mock
    private RedisTemplate mockRedisTemplate;

    private WxReserveConfigServiceImpl wxReserveConfigServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxReserveConfigServiceImplUnderTest = new WxReserveConfigServiceImpl(mockWxOrganizationService,
                mockRedisTemplate);
    }

    @Test
    public void testListConfig() {
        // Setup
        final WxStorePageReqDTO wxStorePageReqDTO = WxStorePageReqDTO.builder().build();

        // Configure WxOrganizationService.getStorePage(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("guid");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        final Page<StoreDTO> storeDTOPage = new Page<>(0L, 0L, Arrays.asList(storeDTO));
        when(mockWxOrganizationService.getStorePage(WxStorePageReqDTO.builder().build())).thenReturn(storeDTOPage);

        // Run the test
        final Page<WxReserveConfigDTO> result = wxReserveConfigServiceImplUnderTest.listConfig(wxStorePageReqDTO);

        // Verify the results
    }

    @Test
    public void testListConfig_WxOrganizationServiceReturnsNull() {
        // Setup
        final WxStorePageReqDTO wxStorePageReqDTO = WxStorePageReqDTO.builder().build();
        when(mockWxOrganizationService.getStorePage(WxStorePageReqDTO.builder().build())).thenReturn(null);

        // Run the test
        final Page<WxReserveConfigDTO> result = wxReserveConfigServiceImplUnderTest.listConfig(wxStorePageReqDTO);

        // Verify the results
    }

    @Test
    public void testUpdateConfig() {
        // Setup
        final WxReserveConfigDTO wxReserveConfigDTO = new WxReserveConfigDTO();
        wxReserveConfigDTO.setGuid("a3fc9b5b-5a63-4f7e-bf10-04bed3dd660c");
        wxReserveConfigDTO.setStoreGuid("guid");
        wxReserveConfigDTO.setStoreName("name");
        wxReserveConfigDTO.setStatus(false);
        wxReserveConfigDTO.setReserveType(0);

        // Run the test
        final Boolean result = wxReserveConfigServiceImplUnderTest.updateConfig(wxReserveConfigDTO);

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testGetConfig() {
        // Setup
        final WxReserveConfigDTO expectedResult = new WxReserveConfigDTO();
        expectedResult.setGuid("a3fc9b5b-5a63-4f7e-bf10-04bed3dd660c");
        expectedResult.setStoreGuid("guid");
        expectedResult.setStoreName("name");
        expectedResult.setStatus(false);
        expectedResult.setReserveType(0);

        // Run the test
        final WxReserveConfigDTO result = wxReserveConfigServiceImplUnderTest.getConfig(
                "2fe65525-24e9-4fbf-9b65-3891058d57d1");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testInitWxReserveConfig() {
        // Setup
        final WxReserveConfigDO wxReserveConfigDO = new WxReserveConfigDO();
        wxReserveConfigDO.setGuid("118230eb-d7b1-44e2-99f2-7d6ebb41a71d");
        wxReserveConfigDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxReserveConfigDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxReserveConfigDO.setIsDelete(false);
        wxReserveConfigDO.setStoreGuid("storeGuid");
        wxReserveConfigDO.setStatus(false);
        wxReserveConfigDO.setReserveType(0);
        wxReserveConfigDO.setDaysBefore(0);
        wxReserveConfigDO.setHoursBefore(0.0);
        wxReserveConfigDO.setReserveArea(0);
        wxReserveConfigDO.setDaysRange(0);
        final List<WxReserveConfigDO> expectedResult = Arrays.asList(wxReserveConfigDO);

        // Run the test
        final List<WxReserveConfigDO> result = wxReserveConfigServiceImplUnderTest.initWxReserveConfig(
                Arrays.asList("value"));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testStore() {
        // Setup
        final WxReserveConfigDTO expectedResult = new WxReserveConfigDTO();
        expectedResult.setGuid("a3fc9b5b-5a63-4f7e-bf10-04bed3dd660c");
        expectedResult.setStoreGuid("guid");
        expectedResult.setStoreName("name");
        expectedResult.setStatus(false);
        expectedResult.setReserveType(0);

        // Run the test
        final WxReserveConfigDTO result = wxReserveConfigServiceImplUnderTest.store("storeGuid");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testStoreList() {
        // Setup
        final WxReserveConfigDTO wxReserveConfigDTO = new WxReserveConfigDTO();
        wxReserveConfigDTO.setGuid("a3fc9b5b-5a63-4f7e-bf10-04bed3dd660c");
        wxReserveConfigDTO.setStoreGuid("guid");
        wxReserveConfigDTO.setStoreName("name");
        wxReserveConfigDTO.setStatus(false);
        wxReserveConfigDTO.setReserveType(0);
        final List<WxReserveConfigDTO> expectedResult = Arrays.asList(wxReserveConfigDTO);

        // Run the test
        final List<WxReserveConfigDTO> result = wxReserveConfigServiceImplUnderTest.storeList(Arrays.asList("value"));

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
