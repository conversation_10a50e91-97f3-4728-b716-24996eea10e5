package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.saas.store.weixin.config.WxThirdOpenConfig;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerInfoDO;
import com.holderzone.saas.store.weixin.service.WxStoreAuthorizerInfoService;
import com.holderzone.saas.store.weixin.service.WxStoreComponentConfigService;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxSaasMpServiceImplTest {

    @Mock
    private WxThirdOpenConfig mockWxThirdOpenConfig;
    @Mock
    private WxStoreAuthorizerInfoService mockWxStoreAuthorizerInfoService;
    @Mock
    private WxStoreComponentConfigService mockWxStoreComponentConfigService;

    private WxSaasMpServiceImpl wxSaasMpServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxSaasMpServiceImplUnderTest = new WxSaasMpServiceImpl();
        wxSaasMpServiceImplUnderTest.wxThirdOpenConfig = mockWxThirdOpenConfig;
        wxSaasMpServiceImplUnderTest.wxStoreAuthorizerInfoService = mockWxStoreAuthorizerInfoService;
        wxSaasMpServiceImplUnderTest.wxStoreComponentConfigService = mockWxStoreComponentConfigService;
    }

    @Test
    public void testGetWxMpService() throws Exception {
        // Setup
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "6272dd30-c430-4906-a57f-f49a20eee4fc", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "authorizerAppid", "authorizerAccessToken", 0L,
                "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias",
                "qrcodeUrl", "signature", "unBandUserGuid", "unBandUserName", "unBandUserTel",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId", false, "operSubjectGuid");
        when(mockWxThirdOpenConfig.getWxOpenConfigStorage()).thenReturn(null);
        when(mockWxThirdOpenConfig.getWxOpenComponentService()).thenReturn(null);

        // Run the test
        final WxMpService result = wxSaasMpServiceImplUnderTest.getWxMpService(wxStoreAuthorizerInfoDO);

        // Verify the results
        verify(mockWxStoreComponentConfigService).getAccessToken();
        verify(mockWxStoreAuthorizerInfoService).updateWxAuthorizeInfo(
                new WxStoreAuthorizerInfoDO(0L, "6272dd30-c430-4906-a57f-f49a20eee4fc", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "authorizerAppid",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"));
    }

    @Test(expected = WxErrorException.class)
    public void testGetWxMpService_WxStoreComponentConfigServiceThrowsWxErrorException() throws Exception {
        // Setup
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "6272dd30-c430-4906-a57f-f49a20eee4fc", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "authorizerAppid", "authorizerAccessToken", 0L,
                "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias",
                "qrcodeUrl", "signature", "unBandUserGuid", "unBandUserName", "unBandUserTel",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreComponentConfigService.getAccessToken()).thenThrow(WxErrorException.class);

        // Run the test
        wxSaasMpServiceImplUnderTest.getWxMpService(wxStoreAuthorizerInfoDO);
    }
}
