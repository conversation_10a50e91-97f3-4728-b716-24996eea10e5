package com.holderzone.saas.store.weixin.controller;

import com.holderzone.holder.saas.member.wechat.dto.member.MemberInfoVolume;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolume;
import com.holderzone.saas.store.dto.weixin.member.WxMemberInfoVolumeDetailsRespDTO;
import com.holderzone.saas.store.dto.weixin.member.WxMemberVolumeInfoListReqDTO;
import com.holderzone.saas.store.dto.weixin.member.WxVolumeDetailReqDTO;
import com.holderzone.saas.store.weixin.service.WxMemberCenterVolumeService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxMemberCenterVolumeController.class)
public class WxMemberCenterVolumeControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxMemberCenterVolumeService mockWxMemberCenterVolumeService;

    @Test
    public void testVolumeInfoList() throws Exception {
        // Setup
        // Configure WxMemberCenterVolumeService.volumeInfoList(...).
        final ResponseMemberInfoVolume responseMemberInfoVolume = new ResponseMemberInfoVolume();
        responseMemberInfoVolume.setMayUseVolumeNum(0);
        responseMemberInfoVolume.setNotUseVolumeNum(0);
        final MemberInfoVolume memberInfoVolume = new MemberInfoVolume();
        memberInfoVolume.setMemberVolumeGuid("memberVolumeGuid");
        memberInfoVolume.setVolumeInfoGuid("volumeInfoGuid");
        responseMemberInfoVolume.setMemberVolumeList(Arrays.asList(memberInfoVolume));
        when(mockWxMemberCenterVolumeService.volumeInfoList(WxMemberVolumeInfoListReqDTO.builder().build()))
                .thenReturn(responseMemberInfoVolume);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_member_center_volume/volume_list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testVolumeCodeDetails() throws Exception {
        // Setup
        // Configure WxMemberCenterVolumeService.volumeCodeDetails(...).
        final WxMemberInfoVolumeDetailsRespDTO wxMemberInfoVolumeDetailsRespDTO = WxMemberInfoVolumeDetailsRespDTO.builder().build();
        when(mockWxMemberCenterVolumeService.volumeCodeDetails(
                new WxVolumeDetailReqDTO("enterpriseGuid", "memberVolumeGuid", "volumeInfoGuid")))
                .thenReturn(wxMemberInfoVolumeDetailsRespDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_member_center_volume/volume_details")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
