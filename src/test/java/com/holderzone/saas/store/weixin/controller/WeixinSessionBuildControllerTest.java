package com.holderzone.saas.store.weixin.controller;

import com.holderzone.holder.saas.member.wechat.dto.member.RequestQueryMemberInfo;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfo;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.entry.dto.WxUserInfoDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.item.resp.ItemAndTypeForAndroidRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemSynRespDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.weixin.MultiMemberDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.weixin.config.ResponseModel;
import com.holderzone.saas.store.weixin.config.WxH5AccountConfig;
import com.holderzone.saas.store.weixin.config.WxH5OpenIdRelationConfig;
import com.holderzone.saas.store.weixin.entity.domain.WxQrCodeInfoDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreAuthorizerInfoDO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.entity.query.WxQrCodeUrlQuery;
import com.holderzone.saas.store.weixin.manager.WeixinSdkManager;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreConsumerMapstruct;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.deal.ItemClientService;
import com.holderzone.saas.store.weixin.service.deal.TableClientService;
import com.holderzone.saas.store.weixin.service.rpc.EnterpriseClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import com.holderzone.saas.store.weixin.utils.UserMemberSessionUtils;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.bean.result.WxMpOAuth2AccessToken;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import me.chanjar.weixin.open.api.WxOpenComponentService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.concurrent.TimeUnit;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WeixinSessionBuildController.class)
public class WeixinSessionBuildControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStoreSessionDetailsService mockWxStoreSessionDetailsService;
    @MockBean
    private WxSaasMpService mockWxSaasMpService;
    @MockBean
    private WxStoreAuthorizerInfoService mockWxStoreAuthorizerInfoService;
    @MockBean
    private WeixinSdkManager mockWeixinSdkManager;
    @MockBean
    private WxQrCodeInfoService mockWxQrCodeInfoService;
    @MockBean
    private RedisUtils mockRedisUtils;
    @MockBean
    private WxStoreConsumerMapstruct mockWxStoreConsumerMapstruct;
    @MockBean
    private WxOpenComponentService mockWxOpenComponentService;
    @MockBean
    private WxUserRecordService mockWxUserRecordService;
    @MockBean
    private WxStoreMpService mockWxStoreMpService;
    @MockBean
    private MemberClientService mockMemberClientService;
    @MockBean
    private ItemClientService mockItemClientService;
    @MockBean
    private WxOrderRecordService mockWxOrderRecordService;
    @MockBean
    private WxStoreOrderConfigService mockWxStoreOrderConfigService;
    @MockBean
    private UserMemberSessionUtils mockUserMemberSessionUtils;
    @MockBean
    private TableClientService mockTableClientService;
    @MockBean
    private HsaBaseClientService mockHsaBaseClientService;
    @MockBean
    private EnterpriseClientService mockEnterpriseClientService;
    @MockBean
    private WxH5AccountConfig mockWxH5AccountConfig;
    @MockBean
    private WxH5OpenIdRelationConfig mockWxH5OpenIdRelationConfig;

    @Test
    public void testGetToken() throws Exception {
        // Setup
        when(mockRedisUtils.setNx("key", "1", 60L)).thenReturn(false);
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure TableClientService.getTableByGuid(...).
        final TableDTO tableDTO = TableDTO.builder()
                .storeGuid("storeGuid")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .code("code")
                .build();
        when(mockTableClientService.getTableByGuid("diningTableGuid")).thenReturn(tableDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("d4d99bba-c4e8-4f30-841b-7b9e84049a3c");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("460b5927-1a70-4554-969f-93a22569f7fc");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        when(mockRedisUtils.generateGuid("wx-token")).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/session/getToken")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockWxUserRecordService).asyncMemberInfo(new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0), "openId");
        verify(mockRedisUtils).setEx("key", "value", 5L, TimeUnit.MINUTES);
        verify(mockWxOrderRecordService).initialRecord(new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0));
        verify(mockWxUserRecordService).saveOrUpdateUserInfo(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .sex(0)
                .country("country")
                .province("province")
                .city("city")
                .isLogin(false)
                .phoneNum("phoneNum")
                .operSubjectGuid("operSubjectGuid")
                .build());
    }

    @Test
    public void testGetToken_RedisUtilsSetNxReturnsTrue() throws Exception {
        // Setup
        when(mockRedisUtils.setNx("key", "1", 60L)).thenReturn(true);

        // Configure ItemClientService.getItemsForWeixin(...).
        final ItemAndTypeForAndroidRespDTO itemAndTypeForAndroidRespDTO = new ItemAndTypeForAndroidRespDTO();
        final ItemSynRespDTO itemSynRespDTO = new ItemSynRespDTO();
        itemSynRespDTO.setItemGuid("itemGuid");
        itemSynRespDTO.setParentGuid("parentGuid");
        itemSynRespDTO.setTypeGuid("typeGuid");
        itemSynRespDTO.setTypeName("typeName");
        itemAndTypeForAndroidRespDTO.setItemList(Arrays.asList(itemSynRespDTO));
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockItemClientService.getItemsForWeixin(baseDTO)).thenReturn(itemAndTypeForAndroidRespDTO);

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Configure TableClientService.getTableByGuid(...).
        final TableDTO tableDTO = TableDTO.builder()
                .storeGuid("storeGuid")
                .areaGuid("areaGuid")
                .areaName("areaName")
                .code("code")
                .build();
        when(mockTableClientService.getTableByGuid("diningTableGuid")).thenReturn(tableDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("d4d99bba-c4e8-4f30-841b-7b9e84049a3c");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("460b5927-1a70-4554-969f-93a22569f7fc");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        when(mockRedisUtils.generateGuid("wx-token")).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/session/getToken")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm RedisUtils.set(...).
        final ItemAndTypeForAndroidRespDTO value = new ItemAndTypeForAndroidRespDTO();
        final ItemSynRespDTO itemSynRespDTO1 = new ItemSynRespDTO();
        itemSynRespDTO1.setItemGuid("itemGuid");
        itemSynRespDTO1.setParentGuid("parentGuid");
        itemSynRespDTO1.setTypeGuid("typeGuid");
        itemSynRespDTO1.setTypeName("typeName");
        value.setItemList(Arrays.asList(itemSynRespDTO1));
        verify(mockRedisUtils).set("key", value);
        verify(mockWxUserRecordService).asyncMemberInfo(new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0), "openId");
        verify(mockRedisUtils).setEx("key", "value", 5L, TimeUnit.MINUTES);
        verify(mockWxOrderRecordService).initialRecord(new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0));
        verify(mockWxUserRecordService).saveOrUpdateUserInfo(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .sex(0)
                .country("country")
                .province("province")
                .city("city")
                .isLogin(false)
                .phoneNum("phoneNum")
                .operSubjectGuid("operSubjectGuid")
                .build());
    }

    @Test
    public void testGetToken_WxStoreOrderConfigServiceReturnsNull() throws Exception {
        // Setup
        when(mockRedisUtils.setNx("key", "1", 60L)).thenReturn(false);
        when(mockRedisUtils.get("key")).thenReturn("result");
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(null);
        when(mockRedisUtils.generateGuid("wx-token")).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/session/getToken")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockWxUserRecordService).asyncMemberInfo(new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0), "openId");
        verify(mockWxOrderRecordService).initialRecord(new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0));
        verify(mockWxUserRecordService).saveOrUpdateUserInfo(WxStoreConsumerDTO.builder()
                .openId("openId")
                .nickName("nickName")
                .headImgUrl("headImgUrl")
                .sex(0)
                .country("country")
                .province("province")
                .city("city")
                .isLogin(false)
                .phoneNum("phoneNum")
                .operSubjectGuid("operSubjectGuid")
                .build());
    }

    @Test
    public void testBuildSession() throws Exception {
        // Setup
        // Configure WxQrCodeInfoService.getCacheWxQrCodeInfoByGuid(...).
        final WxQrCodeInfoDO wxQrCodeInfoDO = new WxQrCodeInfoDO(0L, "a695dd18-86b6-4318-9290-86841787ab34",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid", "storeName",
                "areaGuid", "areaName", "diningTableGuid", "code", "brandGuid", 0, "appId");
        when(mockWxQrCodeInfoService.getCacheWxQrCodeInfoByGuid("wxqrCodeGuid")).thenReturn(wxQrCodeInfoDO);

        when(mockRedisUtils.setNx("key", "1", 60L)).thenReturn(false);

        // Configure WxStoreAuthorizerInfoService.getByBrandId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByBrandId("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxH5OpenIdRelationConfig.getBrandGuids()).thenReturn(Arrays.asList("value"));

        // Configure WxStoreAuthorizerInfoService.getByAppId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO1 = new WxStoreAuthorizerInfoDO(0L,
                "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByAppId("appId")).thenReturn(wxStoreAuthorizerInfoDO1);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn(null);

        // Configure WxOpenComponentService.oauth2getAccessToken(...).
        final WxMpOAuth2AccessToken wxMpOAuth2AccessToken = new WxMpOAuth2AccessToken();
        wxMpOAuth2AccessToken.setAccessToken("accessToken");
        wxMpOAuth2AccessToken.setExpiresIn(0);
        wxMpOAuth2AccessToken.setRefreshToken("refreshToken");
        wxMpOAuth2AccessToken.setOpenId("openId");
        wxMpOAuth2AccessToken.setScope("scope");
        when(mockWxOpenComponentService.oauth2getAccessToken("appId", "code")).thenReturn(wxMpOAuth2AccessToken);

        when(mockWxH5AccountConfig.getAppId()).thenReturn("appId");
        when(mockWxStoreMpService.getAuthorizeUrl(
                new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName", "areaGuid", "areaName",
                        "diningTableGuid", "code", 0, "brandGuid",
                        "appId", false, false, ""), "", "","")).thenReturn("result");
        when(mockWeixinSdkManager.buildWxMpService(3)).thenReturn(null);

        // Configure WxStoreSessionDetailsService.getBrandInfoDetails(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setUuid("7dc9174b-ba64-4401-97fb-93850425c8ae");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockWxStoreSessionDetailsService.getBrandInfoDetails("storeGuid")).thenReturn(brandDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("d4d99bba-c4e8-4f30-841b-7b9e84049a3c");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("460b5927-1a70-4554-969f-93a22569f7fc");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        // Configure WxStoreConsumerMapstruct.wxMpUser2WeixinUserDTO(...).
        final WxUserInfoDTO wxUserInfoDTO = new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city",
                "province", "country", "qrScene", false, "", "");
        final WxMpUser wxMpUser = new WxMpUser();
        wxMpUser.setSubscribe(false);
        wxMpUser.setOpenId("openId");
        wxMpUser.setNickname("微信用户");
        wxMpUser.setSexDesc("sexDesc");
        wxMpUser.setSex(0);
        when(mockWxStoreConsumerMapstruct.wxMpUser2WeixinUserDTO(wxMpUser)).thenReturn(wxUserInfoDTO);

        // Configure EnterpriseClientService.findMemberInfoByOrganizationGuid(...).
        final MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
        multiMemberDTO.setId(0L);
        multiMemberDTO.setEnterpriseGuid("enterpriseGuid");
        multiMemberDTO.setMultiMemberGuid("operSubjectGuid");
        multiMemberDTO.setMultiMemberName("multiMemberName");
        multiMemberDTO.setEnabled(false);
        when(mockEnterpriseClientService.findMemberInfoByOrganizationGuid("storeGuid")).thenReturn(multiMemberDTO);

        // Configure WxStoreConsumerMapstruct.wxMpUser2WxuserRecord(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        final WxMpUser wxMpUser1 = new WxMpUser();
        wxMpUser1.setSubscribe(false);
        wxMpUser1.setOpenId("openId");
        wxMpUser1.setNickname("微信用户");
        wxMpUser1.setSexDesc("sexDesc");
        wxMpUser1.setSex(0);
        when(mockWxStoreConsumerMapstruct.wxMpUser2WxuserRecord(wxMpUser1)).thenReturn(wxUserRecordDO);

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setMemberInfoGuid("memberInfoGuid");
        responseMemberInfo.setPhoneNum("phoneNum");
        responseMemberInfo.setNickName("nickName");
        responseMemberInfo.setHeadImgUrl("headImgUrl");
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO1 = new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO1);

        // Run the test and verify the results
        mockMvc.perform(post("/session/build")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockRedisUtils).setEx("key", "value", 7L, TimeUnit.DAYS);
        verify(mockWxOrderRecordService).initialRecord(new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0));
        verify(mockWxUserRecordService).asyncMemberInfo(new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0), "openId");
        verify(mockWxUserRecordService).updateById(
                new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId", "nickName", "headImgUrl", 0,
                        "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0));
    }

    @Test
    public void testBuildSession_RedisUtilsSetNxReturnsTrue() throws Exception {
        // Setup
        // Configure WxQrCodeInfoService.getCacheWxQrCodeInfoByGuid(...).
        final WxQrCodeInfoDO wxQrCodeInfoDO = new WxQrCodeInfoDO(0L, "a695dd18-86b6-4318-9290-86841787ab34",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid", "storeName",
                "areaGuid", "areaName", "diningTableGuid", "code", "brandGuid", 0, "appId");
        when(mockWxQrCodeInfoService.getCacheWxQrCodeInfoByGuid("wxqrCodeGuid")).thenReturn(wxQrCodeInfoDO);

        when(mockRedisUtils.setNx("key", "1", 60L)).thenReturn(true);

        // Configure ItemClientService.getItemsForWeixin(...).
        final ItemAndTypeForAndroidRespDTO itemAndTypeForAndroidRespDTO = new ItemAndTypeForAndroidRespDTO();
        final ItemSynRespDTO itemSynRespDTO = new ItemSynRespDTO();
        itemSynRespDTO.setItemGuid("itemGuid");
        itemSynRespDTO.setParentGuid("parentGuid");
        itemSynRespDTO.setTypeGuid("typeGuid");
        itemSynRespDTO.setTypeName("typeName");
        itemAndTypeForAndroidRespDTO.setItemList(Arrays.asList(itemSynRespDTO));
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setDeviceType(0);
        baseDTO.setDeviceId("deviceId");
        baseDTO.setEnterpriseGuid("enterpriseGuid");
        baseDTO.setEnterpriseName("enterpriseName");
        baseDTO.setStoreGuid("storeGuid");
        when(mockItemClientService.getItemsForWeixin(baseDTO)).thenReturn(itemAndTypeForAndroidRespDTO);

        // Configure WxStoreAuthorizerInfoService.getByBrandId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByBrandId("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxH5OpenIdRelationConfig.getBrandGuids()).thenReturn(Arrays.asList("value"));

        // Configure WxStoreAuthorizerInfoService.getByAppId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO1 = new WxStoreAuthorizerInfoDO(0L,
                "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByAppId("appId")).thenReturn(wxStoreAuthorizerInfoDO1);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn(null);

        // Configure WxOpenComponentService.oauth2getAccessToken(...).
        final WxMpOAuth2AccessToken wxMpOAuth2AccessToken = new WxMpOAuth2AccessToken();
        wxMpOAuth2AccessToken.setAccessToken("accessToken");
        wxMpOAuth2AccessToken.setExpiresIn(0);
        wxMpOAuth2AccessToken.setRefreshToken("refreshToken");
        wxMpOAuth2AccessToken.setOpenId("openId");
        wxMpOAuth2AccessToken.setScope("scope");
        when(mockWxOpenComponentService.oauth2getAccessToken("appId", "code")).thenReturn(wxMpOAuth2AccessToken);

        when(mockWxH5AccountConfig.getAppId()).thenReturn("appId");
        when(mockWxStoreMpService.getAuthorizeUrl(
                new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName", "areaGuid", "areaName",
                        "diningTableGuid", "code", 0, "brandGuid",
                        "appId", false, false, ""),"" ,"","")).thenReturn("result");
        when(mockWeixinSdkManager.buildWxMpService(3)).thenReturn(null);

        // Configure WxStoreSessionDetailsService.getBrandInfoDetails(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setUuid("7dc9174b-ba64-4401-97fb-93850425c8ae");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockWxStoreSessionDetailsService.getBrandInfoDetails("storeGuid")).thenReturn(brandDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("d4d99bba-c4e8-4f30-841b-7b9e84049a3c");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("460b5927-1a70-4554-969f-93a22569f7fc");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        // Configure WxStoreConsumerMapstruct.wxMpUser2WeixinUserDTO(...).
        final WxUserInfoDTO wxUserInfoDTO = new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city",
                "province", "country", "qrScene", false, "", "");
        final WxMpUser wxMpUser = new WxMpUser();
        wxMpUser.setSubscribe(false);
        wxMpUser.setOpenId("openId");
        wxMpUser.setNickname("微信用户");
        wxMpUser.setSexDesc("sexDesc");
        wxMpUser.setSex(0);
        when(mockWxStoreConsumerMapstruct.wxMpUser2WeixinUserDTO(wxMpUser)).thenReturn(wxUserInfoDTO);

        // Configure EnterpriseClientService.findMemberInfoByOrganizationGuid(...).
        final MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
        multiMemberDTO.setId(0L);
        multiMemberDTO.setEnterpriseGuid("enterpriseGuid");
        multiMemberDTO.setMultiMemberGuid("operSubjectGuid");
        multiMemberDTO.setMultiMemberName("multiMemberName");
        multiMemberDTO.setEnabled(false);
        when(mockEnterpriseClientService.findMemberInfoByOrganizationGuid("storeGuid")).thenReturn(multiMemberDTO);

        // Configure WxStoreConsumerMapstruct.wxMpUser2WxuserRecord(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        final WxMpUser wxMpUser1 = new WxMpUser();
        wxMpUser1.setSubscribe(false);
        wxMpUser1.setOpenId("openId");
        wxMpUser1.setNickname("微信用户");
        wxMpUser1.setSexDesc("sexDesc");
        wxMpUser1.setSex(0);
        when(mockWxStoreConsumerMapstruct.wxMpUser2WxuserRecord(wxMpUser1)).thenReturn(wxUserRecordDO);

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setMemberInfoGuid("memberInfoGuid");
        responseMemberInfo.setPhoneNum("phoneNum");
        responseMemberInfo.setNickName("nickName");
        responseMemberInfo.setHeadImgUrl("headImgUrl");
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO1 = new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO1);

        // Run the test and verify the results
        mockMvc.perform(post("/session/build")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm RedisUtils.set(...).
        final ItemAndTypeForAndroidRespDTO value = new ItemAndTypeForAndroidRespDTO();
        final ItemSynRespDTO itemSynRespDTO1 = new ItemSynRespDTO();
        itemSynRespDTO1.setItemGuid("itemGuid");
        itemSynRespDTO1.setParentGuid("parentGuid");
        itemSynRespDTO1.setTypeGuid("typeGuid");
        itemSynRespDTO1.setTypeName("typeName");
        value.setItemList(Arrays.asList(itemSynRespDTO1));
        verify(mockRedisUtils).set("key", value);
        verify(mockRedisUtils).setEx("key", "value", 7L, TimeUnit.DAYS);
        verify(mockWxOrderRecordService).initialRecord(new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0));
        verify(mockWxUserRecordService).asyncMemberInfo(new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0), "openId");
        verify(mockWxUserRecordService).updateById(
                new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId", "nickName", "headImgUrl", 0,
                        "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0));
    }

    @Test
    public void testBuildSession_WxStoreAuthorizerInfoServiceGetByBrandIdReturnsNull() throws Exception {
        // Setup
        // Configure WxQrCodeInfoService.getCacheWxQrCodeInfoByGuid(...).
        final WxQrCodeInfoDO wxQrCodeInfoDO = new WxQrCodeInfoDO(0L, "a695dd18-86b6-4318-9290-86841787ab34",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid", "storeName",
                "areaGuid", "areaName", "diningTableGuid", "code", "brandGuid", 0, "appId");
        when(mockWxQrCodeInfoService.getCacheWxQrCodeInfoByGuid("wxqrCodeGuid")).thenReturn(wxQrCodeInfoDO);

        when(mockRedisUtils.setNx("key", "1", 60L)).thenReturn(false);
        when(mockWxStoreAuthorizerInfoService.getByBrandId("brandGuid")).thenReturn(null);
        when(mockWxH5OpenIdRelationConfig.getBrandGuids()).thenReturn(Arrays.asList("value"));

        // Configure WxStoreAuthorizerInfoService.getByAppId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByAppId("appId")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn(null);

        // Configure WxOpenComponentService.oauth2getAccessToken(...).
        final WxMpOAuth2AccessToken wxMpOAuth2AccessToken = new WxMpOAuth2AccessToken();
        wxMpOAuth2AccessToken.setAccessToken("accessToken");
        wxMpOAuth2AccessToken.setExpiresIn(0);
        wxMpOAuth2AccessToken.setRefreshToken("refreshToken");
        wxMpOAuth2AccessToken.setOpenId("openId");
        wxMpOAuth2AccessToken.setScope("scope");
        when(mockWxOpenComponentService.oauth2getAccessToken("appId", "code")).thenReturn(wxMpOAuth2AccessToken);

        when(mockWxH5AccountConfig.getAppId()).thenReturn("appId");
        when(mockWxStoreMpService.getAuthorizeUrl(
                new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName", "areaGuid", "areaName",
                        "diningTableGuid", "code", 0, "brandGuid",
                        "appId", false, false, ""), "","","")).thenReturn("result");
        when(mockWeixinSdkManager.buildWxMpService(3)).thenReturn(null);

        // Configure WxStoreSessionDetailsService.getBrandInfoDetails(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setUuid("7dc9174b-ba64-4401-97fb-93850425c8ae");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockWxStoreSessionDetailsService.getBrandInfoDetails("storeGuid")).thenReturn(brandDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("d4d99bba-c4e8-4f30-841b-7b9e84049a3c");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("460b5927-1a70-4554-969f-93a22569f7fc");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        // Configure WxStoreConsumerMapstruct.wxMpUser2WeixinUserDTO(...).
        final WxUserInfoDTO wxUserInfoDTO = new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city",
                "province", "country", "qrScene", false, "", "");
        final WxMpUser wxMpUser = new WxMpUser();
        wxMpUser.setSubscribe(false);
        wxMpUser.setOpenId("openId");
        wxMpUser.setNickname("微信用户");
        wxMpUser.setSexDesc("sexDesc");
        wxMpUser.setSex(0);
        when(mockWxStoreConsumerMapstruct.wxMpUser2WeixinUserDTO(wxMpUser)).thenReturn(wxUserInfoDTO);

        // Configure EnterpriseClientService.findMemberInfoByOrganizationGuid(...).
        final MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
        multiMemberDTO.setId(0L);
        multiMemberDTO.setEnterpriseGuid("enterpriseGuid");
        multiMemberDTO.setMultiMemberGuid("operSubjectGuid");
        multiMemberDTO.setMultiMemberName("multiMemberName");
        multiMemberDTO.setEnabled(false);
        when(mockEnterpriseClientService.findMemberInfoByOrganizationGuid("storeGuid")).thenReturn(multiMemberDTO);

        // Configure WxStoreConsumerMapstruct.wxMpUser2WxuserRecord(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        final WxMpUser wxMpUser1 = new WxMpUser();
        wxMpUser1.setSubscribe(false);
        wxMpUser1.setOpenId("openId");
        wxMpUser1.setNickname("微信用户");
        wxMpUser1.setSexDesc("sexDesc");
        wxMpUser1.setSex(0);
        when(mockWxStoreConsumerMapstruct.wxMpUser2WxuserRecord(wxMpUser1)).thenReturn(wxUserRecordDO);

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setMemberInfoGuid("memberInfoGuid");
        responseMemberInfo.setPhoneNum("phoneNum");
        responseMemberInfo.setNickName("nickName");
        responseMemberInfo.setHeadImgUrl("headImgUrl");
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO1 = new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO1);

        // Run the test and verify the results
        mockMvc.perform(post("/session/build")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockRedisUtils).setEx("key", "value", 7L, TimeUnit.DAYS);
        verify(mockWxOrderRecordService).initialRecord(new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0));
        verify(mockWxUserRecordService).asyncMemberInfo(new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0), "openId");
        verify(mockWxUserRecordService).updateById(
                new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId", "nickName", "headImgUrl", 0,
                        "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0));
    }

    @Test
    public void testBuildSession_WxH5OpenIdRelationConfigReturnsNoItems() throws Exception {
        // Setup
        // Configure WxQrCodeInfoService.getCacheWxQrCodeInfoByGuid(...).
        final WxQrCodeInfoDO wxQrCodeInfoDO = new WxQrCodeInfoDO(0L, "a695dd18-86b6-4318-9290-86841787ab34",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid", "storeName",
                "areaGuid", "areaName", "diningTableGuid", "code", "brandGuid", 0, "appId");
        when(mockWxQrCodeInfoService.getCacheWxQrCodeInfoByGuid("wxqrCodeGuid")).thenReturn(wxQrCodeInfoDO);

        when(mockRedisUtils.setNx("key", "1", 60L)).thenReturn(false);

        // Configure WxStoreAuthorizerInfoService.getByBrandId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByBrandId("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxH5OpenIdRelationConfig.getBrandGuids()).thenReturn(Collections.emptyList());

        // Configure WxStoreAuthorizerInfoService.getByAppId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO1 = new WxStoreAuthorizerInfoDO(0L,
                "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByAppId("appId")).thenReturn(wxStoreAuthorizerInfoDO1);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn(null);

        // Configure WxOpenComponentService.oauth2getAccessToken(...).
        final WxMpOAuth2AccessToken wxMpOAuth2AccessToken = new WxMpOAuth2AccessToken();
        wxMpOAuth2AccessToken.setAccessToken("accessToken");
        wxMpOAuth2AccessToken.setExpiresIn(0);
        wxMpOAuth2AccessToken.setRefreshToken("refreshToken");
        wxMpOAuth2AccessToken.setOpenId("openId");
        wxMpOAuth2AccessToken.setScope("scope");
        when(mockWxOpenComponentService.oauth2getAccessToken("appId", "code")).thenReturn(wxMpOAuth2AccessToken);

        when(mockWxH5AccountConfig.getAppId()).thenReturn("appId");
        when(mockWxStoreMpService.getAuthorizeUrl(
                new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName", "areaGuid", "areaName",
                        "diningTableGuid", "code", 0, "brandGuid",
                        "appId", false, false, ""), "","","")).thenReturn("result");
        when(mockWeixinSdkManager.buildWxMpService(3)).thenReturn(null);

        // Configure WxStoreSessionDetailsService.getBrandInfoDetails(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setUuid("7dc9174b-ba64-4401-97fb-93850425c8ae");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockWxStoreSessionDetailsService.getBrandInfoDetails("storeGuid")).thenReturn(brandDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("d4d99bba-c4e8-4f30-841b-7b9e84049a3c");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("460b5927-1a70-4554-969f-93a22569f7fc");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        // Configure WxStoreConsumerMapstruct.wxMpUser2WeixinUserDTO(...).
        final WxUserInfoDTO wxUserInfoDTO = new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city",
                "province", "country", "qrScene", false, "", "");
        final WxMpUser wxMpUser = new WxMpUser();
        wxMpUser.setSubscribe(false);
        wxMpUser.setOpenId("openId");
        wxMpUser.setNickname("微信用户");
        wxMpUser.setSexDesc("sexDesc");
        wxMpUser.setSex(0);
        when(mockWxStoreConsumerMapstruct.wxMpUser2WeixinUserDTO(wxMpUser)).thenReturn(wxUserInfoDTO);

        // Configure EnterpriseClientService.findMemberInfoByOrganizationGuid(...).
        final MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
        multiMemberDTO.setId(0L);
        multiMemberDTO.setEnterpriseGuid("enterpriseGuid");
        multiMemberDTO.setMultiMemberGuid("operSubjectGuid");
        multiMemberDTO.setMultiMemberName("multiMemberName");
        multiMemberDTO.setEnabled(false);
        when(mockEnterpriseClientService.findMemberInfoByOrganizationGuid("storeGuid")).thenReturn(multiMemberDTO);

        // Configure WxStoreConsumerMapstruct.wxMpUser2WxuserRecord(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        final WxMpUser wxMpUser1 = new WxMpUser();
        wxMpUser1.setSubscribe(false);
        wxMpUser1.setOpenId("openId");
        wxMpUser1.setNickname("微信用户");
        wxMpUser1.setSexDesc("sexDesc");
        wxMpUser1.setSex(0);
        when(mockWxStoreConsumerMapstruct.wxMpUser2WxuserRecord(wxMpUser1)).thenReturn(wxUserRecordDO);

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setMemberInfoGuid("memberInfoGuid");
        responseMemberInfo.setPhoneNum("phoneNum");
        responseMemberInfo.setNickName("nickName");
        responseMemberInfo.setHeadImgUrl("headImgUrl");
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO1 = new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO1);

        // Run the test and verify the results
        mockMvc.perform(post("/session/build")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockRedisUtils).setEx("key", "value", 7L, TimeUnit.DAYS);
        verify(mockWxOrderRecordService).initialRecord(new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0));
        verify(mockWxUserRecordService).asyncMemberInfo(new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0), "openId");
        verify(mockWxUserRecordService).updateById(
                new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId", "nickName", "headImgUrl", 0,
                        "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0));
    }

    @Test
    public void testBuildSession_WxSaasMpServiceThrowsWxErrorException() throws Exception {
        // Setup
        // Configure WxQrCodeInfoService.getCacheWxQrCodeInfoByGuid(...).
        final WxQrCodeInfoDO wxQrCodeInfoDO = new WxQrCodeInfoDO(0L, "a695dd18-86b6-4318-9290-86841787ab34",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid", "storeName",
                "areaGuid", "areaName", "diningTableGuid", "code", "brandGuid", 0, "appId");
        when(mockWxQrCodeInfoService.getCacheWxQrCodeInfoByGuid("wxqrCodeGuid")).thenReturn(wxQrCodeInfoDO);

        when(mockRedisUtils.setNx("key", "1", 60L)).thenReturn(false);

        // Configure WxStoreAuthorizerInfoService.getByBrandId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByBrandId("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxH5OpenIdRelationConfig.getBrandGuids()).thenReturn(Arrays.asList("value"));

        // Configure WxStoreAuthorizerInfoService.getByAppId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO1 = new WxStoreAuthorizerInfoDO(0L,
                "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByAppId("appId")).thenReturn(wxStoreAuthorizerInfoDO1);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenThrow(WxErrorException.class);

        // Configure WxStoreConsumerMapstruct.wxMpUser2WxuserRecord(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        final WxMpUser wxMpUser = new WxMpUser();
        wxMpUser.setSubscribe(false);
        wxMpUser.setOpenId("openId");
        wxMpUser.setNickname("微信用户");
        wxMpUser.setSexDesc("sexDesc");
        wxMpUser.setSex(0);
        when(mockWxStoreConsumerMapstruct.wxMpUser2WxuserRecord(wxMpUser)).thenReturn(wxUserRecordDO);

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setMemberInfoGuid("memberInfoGuid");
        responseMemberInfo.setPhoneNum("phoneNum");
        responseMemberInfo.setNickName("nickName");
        responseMemberInfo.setHeadImgUrl("headImgUrl");
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO1 = new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO1);

        // Run the test and verify the results
        mockMvc.perform(post("/session/build")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockWxUserRecordService).updateById(
                new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId", "nickName", "headImgUrl", 0,
                        "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0));
    }

    @Test
    public void testBuildSession_WxOpenComponentServiceThrowsWxErrorException() throws Exception {
        // Setup
        // Configure WxQrCodeInfoService.getCacheWxQrCodeInfoByGuid(...).
        final WxQrCodeInfoDO wxQrCodeInfoDO = new WxQrCodeInfoDO(0L, "a695dd18-86b6-4318-9290-86841787ab34",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid", "storeName",
                "areaGuid", "areaName", "diningTableGuid", "code", "brandGuid", 0, "appId");
        when(mockWxQrCodeInfoService.getCacheWxQrCodeInfoByGuid("wxqrCodeGuid")).thenReturn(wxQrCodeInfoDO);

        when(mockRedisUtils.setNx("key", "1", 60L)).thenReturn(false);

        // Configure WxStoreAuthorizerInfoService.getByBrandId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByBrandId("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxH5OpenIdRelationConfig.getBrandGuids()).thenReturn(Arrays.asList("value"));

        // Configure WxStoreAuthorizerInfoService.getByAppId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO1 = new WxStoreAuthorizerInfoDO(0L,
                "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByAppId("appId")).thenReturn(wxStoreAuthorizerInfoDO1);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn(null);
        when(mockWxOpenComponentService.oauth2getAccessToken("appId", "code")).thenThrow(WxErrorException.class);

        // Configure WxStoreConsumerMapstruct.wxMpUser2WxuserRecord(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        final WxMpUser wxMpUser = new WxMpUser();
        wxMpUser.setSubscribe(false);
        wxMpUser.setOpenId("openId");
        wxMpUser.setNickname("微信用户");
        wxMpUser.setSexDesc("sexDesc");
        wxMpUser.setSex(0);
        when(mockWxStoreConsumerMapstruct.wxMpUser2WxuserRecord(wxMpUser)).thenReturn(wxUserRecordDO);

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setMemberInfoGuid("memberInfoGuid");
        responseMemberInfo.setPhoneNum("phoneNum");
        responseMemberInfo.setNickName("nickName");
        responseMemberInfo.setHeadImgUrl("headImgUrl");
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO1 = new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO1);

        // Run the test and verify the results
        mockMvc.perform(post("/session/build")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockWxUserRecordService).updateById(
                new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId", "nickName", "headImgUrl", 0,
                        "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0));
    }

    @Test
    public void testBuildSession_WxStoreOrderConfigServiceReturnsNull() throws Exception {
        // Setup
        // Configure WxQrCodeInfoService.getCacheWxQrCodeInfoByGuid(...).
        final WxQrCodeInfoDO wxQrCodeInfoDO = new WxQrCodeInfoDO(0L, "a695dd18-86b6-4318-9290-86841787ab34",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid", "storeName",
                "areaGuid", "areaName", "diningTableGuid", "code", "brandGuid", 0, "appId");
        when(mockWxQrCodeInfoService.getCacheWxQrCodeInfoByGuid("wxqrCodeGuid")).thenReturn(wxQrCodeInfoDO);

        when(mockRedisUtils.setNx("key", "1", 60L)).thenReturn(false);

        // Configure WxStoreAuthorizerInfoService.getByBrandId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByBrandId("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxH5OpenIdRelationConfig.getBrandGuids()).thenReturn(Arrays.asList("value"));

        // Configure WxStoreAuthorizerInfoService.getByAppId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO1 = new WxStoreAuthorizerInfoDO(0L,
                "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByAppId("appId")).thenReturn(wxStoreAuthorizerInfoDO1);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn(null);

        // Configure WxOpenComponentService.oauth2getAccessToken(...).
        final WxMpOAuth2AccessToken wxMpOAuth2AccessToken = new WxMpOAuth2AccessToken();
        wxMpOAuth2AccessToken.setAccessToken("accessToken");
        wxMpOAuth2AccessToken.setExpiresIn(0);
        wxMpOAuth2AccessToken.setRefreshToken("refreshToken");
        wxMpOAuth2AccessToken.setOpenId("openId");
        wxMpOAuth2AccessToken.setScope("scope");
        when(mockWxOpenComponentService.oauth2getAccessToken("appId", "code")).thenReturn(wxMpOAuth2AccessToken);

        when(mockWxH5AccountConfig.getAppId()).thenReturn("appId");
        when(mockWxStoreMpService.getAuthorizeUrl(
                new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName", "areaGuid", "areaName",
                        "diningTableGuid", "code", 0, "brandGuid",
                        "appId", false, false, ""), "","","")).thenReturn("result");
        when(mockWeixinSdkManager.buildWxMpService(3)).thenReturn(null);

        // Configure WxStoreSessionDetailsService.getBrandInfoDetails(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setUuid("7dc9174b-ba64-4401-97fb-93850425c8ae");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockWxStoreSessionDetailsService.getBrandInfoDetails("storeGuid")).thenReturn(brandDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("d4d99bba-c4e8-4f30-841b-7b9e84049a3c");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(null);

        // Configure WxStoreConsumerMapstruct.wxMpUser2WeixinUserDTO(...).
        final WxUserInfoDTO wxUserInfoDTO = new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city",
                "province", "country", "qrScene", false, "", "");
        final WxMpUser wxMpUser = new WxMpUser();
        wxMpUser.setSubscribe(false);
        wxMpUser.setOpenId("openId");
        wxMpUser.setNickname("微信用户");
        wxMpUser.setSexDesc("sexDesc");
        wxMpUser.setSex(0);
        when(mockWxStoreConsumerMapstruct.wxMpUser2WeixinUserDTO(wxMpUser)).thenReturn(wxUserInfoDTO);

        // Configure EnterpriseClientService.findMemberInfoByOrganizationGuid(...).
        final MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
        multiMemberDTO.setId(0L);
        multiMemberDTO.setEnterpriseGuid("enterpriseGuid");
        multiMemberDTO.setMultiMemberGuid("operSubjectGuid");
        multiMemberDTO.setMultiMemberName("multiMemberName");
        multiMemberDTO.setEnabled(false);
        when(mockEnterpriseClientService.findMemberInfoByOrganizationGuid("storeGuid")).thenReturn(multiMemberDTO);

        // Configure WxStoreConsumerMapstruct.wxMpUser2WxuserRecord(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        final WxMpUser wxMpUser1 = new WxMpUser();
        wxMpUser1.setSubscribe(false);
        wxMpUser1.setOpenId("openId");
        wxMpUser1.setNickname("微信用户");
        wxMpUser1.setSexDesc("sexDesc");
        wxMpUser1.setSex(0);
        when(mockWxStoreConsumerMapstruct.wxMpUser2WxuserRecord(wxMpUser1)).thenReturn(wxUserRecordDO);

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setMemberInfoGuid("memberInfoGuid");
        responseMemberInfo.setPhoneNum("phoneNum");
        responseMemberInfo.setNickName("nickName");
        responseMemberInfo.setHeadImgUrl("headImgUrl");
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO1 = new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO1);

        // Run the test and verify the results
        mockMvc.perform(post("/session/build")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockRedisUtils).setEx("key", "value", 7L, TimeUnit.DAYS);
        verify(mockWxUserRecordService).asyncMemberInfo(new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0), "openId");
        verify(mockWxUserRecordService).updateById(
                new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId", "nickName", "headImgUrl", 0,
                        "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0));
    }

    @Test
    public void testBuildSession_WxUserRecordServiceGetOneByOpenIdReturnsNull() throws Exception {
        // Setup
        // Configure WxQrCodeInfoService.getCacheWxQrCodeInfoByGuid(...).
        final WxQrCodeInfoDO wxQrCodeInfoDO = new WxQrCodeInfoDO(0L, "a695dd18-86b6-4318-9290-86841787ab34",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "storeGuid", "storeName",
                "areaGuid", "areaName", "diningTableGuid", "code", "brandGuid", 0, "appId");
        when(mockWxQrCodeInfoService.getCacheWxQrCodeInfoByGuid("wxqrCodeGuid")).thenReturn(wxQrCodeInfoDO);

        when(mockRedisUtils.setNx("key", "1", 60L)).thenReturn(false);

        // Configure WxStoreAuthorizerInfoService.getByBrandId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByBrandId("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        when(mockWxH5OpenIdRelationConfig.getBrandGuids()).thenReturn(Arrays.asList("value"));

        // Configure WxStoreAuthorizerInfoService.getByAppId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO1 = new WxStoreAuthorizerInfoDO(0L,
                "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByAppId("appId")).thenReturn(wxStoreAuthorizerInfoDO1);

        when(mockWxSaasMpService.getWxMpService(
                new WxStoreAuthorizerInfoDO(0L, "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId",
                        "authorizerAccessToken", 0L, "authorizerRefreshToken", "funcInfo", "nickName", "headImg", 0, 0,
                        "userName", "principalName", "alias", "qrcodeUrl", "signature", "unBandUserGuid",
                        "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0, "templateMsgId",
                        false, "operSubjectGuid"))).thenReturn(null);

        // Configure WxOpenComponentService.oauth2getAccessToken(...).
        final WxMpOAuth2AccessToken wxMpOAuth2AccessToken = new WxMpOAuth2AccessToken();
        wxMpOAuth2AccessToken.setAccessToken("accessToken");
        wxMpOAuth2AccessToken.setExpiresIn(0);
        wxMpOAuth2AccessToken.setRefreshToken("refreshToken");
        wxMpOAuth2AccessToken.setOpenId("openId");
        wxMpOAuth2AccessToken.setScope("scope");
        when(mockWxOpenComponentService.oauth2getAccessToken("appId", "code")).thenReturn(wxMpOAuth2AccessToken);

        when(mockWxH5AccountConfig.getAppId()).thenReturn("appId");
        when(mockWxStoreMpService.getAuthorizeUrl(
                new WxQrCodeUrlQuery("enterpriseGuid", "storeGuid", "storeName", "areaGuid", "areaName",
                        "diningTableGuid", "code", 0, "brandGuid",
                        "appId", false, false, ""), "", "", "")).thenReturn("result");
        when(mockWeixinSdkManager.buildWxMpService(3)).thenReturn(null);

        // Configure WxStoreSessionDetailsService.getBrandInfoDetails(...).
        final BrandDTO brandDTO = new BrandDTO();
        brandDTO.setGuid("brandGuid");
        brandDTO.setUuid("7dc9174b-ba64-4401-97fb-93850425c8ae");
        brandDTO.setName("name");
        brandDTO.setDescription("description");
        brandDTO.setLogoUrl("logoUrl");
        when(mockWxStoreSessionDetailsService.getBrandInfoDetails("storeGuid")).thenReturn(brandDTO);

        // Configure WxStoreSessionDetailsService.getStoreDetail(...).
        final StoreDTO storeDTO = new StoreDTO();
        storeDTO.setGuid("d4d99bba-c4e8-4f30-841b-7b9e84049a3c");
        storeDTO.setCode("code");
        storeDTO.setName("name");
        storeDTO.setBelongBrandGuid("belongBrandGuid");
        storeDTO.setBelongBrandName("belongBrandName");
        when(mockWxStoreSessionDetailsService.getStoreDetail("storeGuid")).thenReturn(storeDTO);

        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("460b5927-1a70-4554-969f-93a22569f7fc");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        // Configure WxStoreConsumerMapstruct.wxMpUser2WeixinUserDTO(...).
        final WxUserInfoDTO wxUserInfoDTO = new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city",
                "province", "country", "qrScene", false, "", "");
        final WxMpUser wxMpUser = new WxMpUser();
        wxMpUser.setSubscribe(false);
        wxMpUser.setOpenId("openId");
        wxMpUser.setNickname("微信用户");
        wxMpUser.setSexDesc("sexDesc");
        wxMpUser.setSex(0);
        when(mockWxStoreConsumerMapstruct.wxMpUser2WeixinUserDTO(wxMpUser)).thenReturn(wxUserInfoDTO);

        // Configure EnterpriseClientService.findMemberInfoByOrganizationGuid(...).
        final MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
        multiMemberDTO.setId(0L);
        multiMemberDTO.setEnterpriseGuid("enterpriseGuid");
        multiMemberDTO.setMultiMemberGuid("operSubjectGuid");
        multiMemberDTO.setMultiMemberName("multiMemberName");
        multiMemberDTO.setEnabled(false);
        when(mockEnterpriseClientService.findMemberInfoByOrganizationGuid("storeGuid")).thenReturn(multiMemberDTO);

        // Configure WxStoreConsumerMapstruct.wxMpUser2WxuserRecord(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        final WxMpUser wxMpUser1 = new WxMpUser();
        wxMpUser1.setSubscribe(false);
        wxMpUser1.setOpenId("openId");
        wxMpUser1.setNickname("微信用户");
        wxMpUser1.setSexDesc("sexDesc");
        wxMpUser1.setSex(0);
        when(mockWxStoreConsumerMapstruct.wxMpUser2WxuserRecord(wxMpUser1)).thenReturn(wxUserRecordDO);

        // Configure HsaBaseClientService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setMemberInfoGuid("memberInfoGuid");
        responseMemberInfo.setPhoneNum("phoneNum");
        responseMemberInfo.setNickName("nickName");
        responseMemberInfo.setHeadImgUrl("headImgUrl");
        final ResponseModel<ResponseMemberInfo> responseMemberInfoResponseModel = new ResponseModel<>(
                responseMemberInfo);
        final RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
        requestQueryMemberInfo.setMemberInfoGuid("memberInfoGuid");
        requestQueryMemberInfo.setUnionId("unionId");
        requestQueryMemberInfo.setOpenId("openId");
        requestQueryMemberInfo.setPhoneNum("phoneNum");
        requestQueryMemberInfo.setOperSubjectGuid("operSubjectGuid");
        when(mockHsaBaseClientService.getMemberInfo(requestQueryMemberInfo))
                .thenReturn(responseMemberInfoResponseModel);

        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(null);
        when(mockRedisUtils.generateGuid("WxUserRecord")).thenReturn("91b0262d-77e7-4e63-9a2c-044812ab7d7e");

        // Run the test and verify the results
        mockMvc.perform(post("/session/build")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockRedisUtils).setEx("key", "value", 7L, TimeUnit.DAYS);
        verify(mockWxOrderRecordService).initialRecord(new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0));
        verify(mockWxUserRecordService).asyncMemberInfo(new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0), "openId");
        verify(mockWxUserRecordService).save(
                new WxUserRecordDO(0L, "91b0262d-77e7-4e63-9a2c-044812ab7d7e", "openId", "nickName", "headImgUrl", 0,
                        "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0));
    }

    @Test
    public void testGetWxAuthInfo() throws Exception {
        // Setup
        // Configure WxStoreAuthorizerInfoService.getByBrandId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByBrandId("brandGuid")).thenReturn(wxStoreAuthorizerInfoDO);

        // Run the test and verify the results
        mockMvc.perform(post("/session/get_wx_auth_info")
                        .param("brandGuid", "brandGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testFindWxAuthInfo() throws Exception {
        // Setup
        // Configure WxStoreAuthorizerInfoService.getByAppId(...).
        final WxStoreAuthorizerInfoDO wxStoreAuthorizerInfoDO = new WxStoreAuthorizerInfoDO(0L,
                "420a3455-00f6-4eab-9411-68cd73fc04ab", "brandGuid", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), "appId", "authorizerAccessToken", 0L, "authorizerRefreshToken",
                "funcInfo", "nickName", "headImg", 0, 0, "userName", "principalName", "alias", "qrcodeUrl", "signature",
                "unBandUserGuid", "unBandUserName", "unBandUserTel", LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0, 0,
                "templateMsgId", false, "operSubjectGuid");
        when(mockWxStoreAuthorizerInfoService.getByAppId("appId")).thenReturn(wxStoreAuthorizerInfoDO);

        // Run the test and verify the results
        mockMvc.perform(post("/session/find_wx_auth_info")
                        .param("appId", "appId")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
