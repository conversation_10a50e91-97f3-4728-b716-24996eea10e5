package com.holderzone.saas.store.weixin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseAccountStatus;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnItemDTO;
import com.holderzone.saas.store.dto.table.TableStatusChangeDTO;
import com.holderzone.saas.store.dto.weixin.MultiMemberDTO;
import com.holderzone.saas.store.dto.weixin.req.WxMemberModeNotifyReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxMemberTradeNotifyReqDTO;
import com.holderzone.saas.store.weixin.config.ResponseModel;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderRecordDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.event.NotifyMessageQueue;
import com.holderzone.saas.store.weixin.helper.WebsocketMessageHelper;
import com.holderzone.saas.store.weixin.service.*;
import com.holderzone.saas.store.weixin.service.OrderItemService;
import com.holderzone.saas.store.weixin.service.rpc.EnterpriseClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreDineInOrderClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.HsaBaseClientService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import com.holderzone.saas.store.weixin.utils.UserMemberSessionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStoreOrderStatusChangeServiceImplTest {

    @Mock
    private WxStoreSessionDetailsService mockWxStoreSessionDetailsService;
    @Mock
    private WxStoreMerchantOrderService mockWxStoreMerchantOrderService;
    @Mock
    private WxStoreDineInOrderClientService mockWxStoreDineInOrderClientService;
    @Mock
    private WxOrderRecordService mockWxOrderRecordService;
    @Mock
    private WxStoreTradeOrderService mockWxStoreTradeOrderService;
    @Mock
    private MemberClientService mockMemberClientService;
    @Mock
    private OrderItemService mockOrderItemService;
    @Mock
    private WxUserRecordService mockWxUserRecordService;
    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private UserMemberSessionUtils mockUserMemberSessionUtils;
    @Mock
    private WebsocketMessageHelper mockWebsocketMessageHelper;
    @Mock
    private NotifyMessageQueue mockNotifyMessageQueue;
    @Mock
    private HsaBaseClientService mockHsaBaseClientService;
    @Mock
    private EnterpriseClientService mockEnterpriseClientService;

    @InjectMocks
    private WxStoreOrderStatusChangeServiceImpl wxStoreOrderStatusChangeServiceImplUnderTest;

    @Test
    public void testComplete() {
        // Setup
        final TableStatusChangeDTO tableStatusChangeDTO = new TableStatusChangeDTO();
        tableStatusChangeDTO.setDeviceType(0);
        tableStatusChangeDTO.setEnterpriseGuid("enterpriseGuid");
        tableStatusChangeDTO.setStoreGuid("storeGuid");
        tableStatusChangeDTO.setUserGuid("operationGuid");
        tableStatusChangeDTO.setUserName("operationName");
        tableStatusChangeDTO.setOrderGuid("data");
        tableStatusChangeDTO.setTableGuid("tableGuid");

        // Configure EnterpriseClientService.findMemberInfoByOrganizationGuid(...).
        final MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
        multiMemberDTO.setId(0L);
        multiMemberDTO.setEnterpriseGuid("enterpriseGuid");
        multiMemberDTO.setMultiMemberGuid("");
        multiMemberDTO.setMultiMemberName("multiMemberName");
        multiMemberDTO.setEnabled(false);
        when(mockEnterpriseClientService.findMemberInfoByOrganizationGuid("storeGuid")).thenReturn(multiMemberDTO);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        returnItemDTO.setDineInItemDTO(dineInItemDTO);
        dineinOrderDetailRespDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setItemName("itemName");
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberInfoCardGuid");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreTradeOrderService.getMainOrderDetails(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("orderGuid");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        dineinOrderDetailRespDTO1.setState(0);
        dineinOrderDetailRespDTO1.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO1 = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setItemName("itemName");
        returnItemDTO1.setDineInItemDTO(dineInItemDTO2);
        dineinOrderDetailRespDTO1.setReturnItemDTOS(Arrays.asList(returnItemDTO1));
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setItemName("itemName");
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO3));
        dineinOrderDetailRespDTO1.setMemberCardGuid("memberInfoCardGuid");
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO2 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO2.setGuid("orderGuid");
        dineinOrderDetailRespDTO2.setTradeMode(0);
        dineinOrderDetailRespDTO2.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setOrderNo("orderNo");
        dineinOrderDetailRespDTO2.setState(0);
        dineinOrderDetailRespDTO2.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO2 = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO4 = new DineInItemDTO();
        dineInItemDTO4.setItemName("itemName");
        returnItemDTO2.setDineInItemDTO(dineInItemDTO4);
        dineinOrderDetailRespDTO2.setReturnItemDTOS(Arrays.asList(returnItemDTO2));
        final DineInItemDTO dineInItemDTO5 = new DineInItemDTO();
        dineInItemDTO5.setItemName("itemName");
        dineinOrderDetailRespDTO2.setDineInItemDTOS(Arrays.asList(dineInItemDTO5));
        dineinOrderDetailRespDTO2.setMemberCardGuid("memberInfoCardGuid");
        when(mockWxStoreTradeOrderService.getMainOrderDetails(dineinOrderDetailRespDTO2))
                .thenReturn(dineinOrderDetailRespDTO1);

        // Configure WxStoreMerchantOrderService.list(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setIsDel(0);
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("关台拒单");
        wxStoreMerchantOrderDO.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDO.setOperationGuid("operationGuid");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("operationName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(wxStoreMerchantOrderDOS);

        // Configure WxOrderRecordService.listByIds(...).
        final Collection<WxOrderRecordDO> wxOrderRecordDOS = Arrays.asList(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        when(mockWxOrderRecordService.listByIds(Arrays.asList("value"))).thenReturn(wxOrderRecordDOS);

        when(mockOrderItemService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));
        when(mockWxOrderRecordService.updateById(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0))).thenReturn(false);

        // Configure WxOrderRecordService.getOutStandingOrders(...).
        final List<WxOrderRecordDO> wxOrderRecordDOS1 = Arrays.asList(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        when(mockWxOrderRecordService.getOutStandingOrders("tableGuid")).thenReturn(wxOrderRecordDOS1);

        // Configure WxStoreMerchantOrderService.firstSubmit(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO1 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO1.setIsDel(0);
        wxStoreMerchantOrderDO1.setOpenId("openId");
        wxStoreMerchantOrderDO1.setOrderState(0);
        wxStoreMerchantOrderDO1.setDenialReason("关台拒单");
        wxStoreMerchantOrderDO1.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDO1.setOperationGuid("operationGuid");
        wxStoreMerchantOrderDO1.setOperationSource(0);
        wxStoreMerchantOrderDO1.setOperationName("operationName");
        wxStoreMerchantOrderDO1.setOrderRecordGuid("orderRecordGuid");
        when(mockWxStoreMerchantOrderService.firstSubmit("guid")).thenReturn(wxStoreMerchantOrderDO1);

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        when(mockWxStoreMerchantOrderService.update(any(LambdaUpdateWrapper.class))).thenReturn(false);
        when(mockRedisUtils.hKeyList("key")).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockHsaBaseClientService.findOpenIDByMemberGuid("memberInfoCardGuid"))
                .thenReturn(new ResponseModel<>("data"));

        // Configure HsaBaseClientService.getMemberState(...).
        final ResponseAccountStatus responseAccountStatus = new ResponseAccountStatus();
        responseAccountStatus.setMemberInfoGuid("memberInfoGuid");
        responseAccountStatus.setOperSubjectGuid("operSubjectGuid");
        responseAccountStatus.setPhoneNum("phoneNum");
        responseAccountStatus.setNickName("nickName");
        responseAccountStatus.setAccountState(0);
        final ResponseModel<ResponseAccountStatus> responseAccountStatusResponseModel = new ResponseModel<>(
                responseAccountStatus);
        when(mockHsaBaseClientService.getMemberState("data", "enterpriseGuid"))
                .thenReturn(responseAccountStatusResponseModel);

        // Run the test
        wxStoreOrderStatusChangeServiceImplUnderTest.complete(tableStatusChangeDTO);

        // Verify the results
        verify(mockRedisUtils).delete("key");

        // Confirm WxStoreMerchantOrderService.updateBatchById(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO2 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO2.setIsDel(0);
        wxStoreMerchantOrderDO2.setOpenId("openId");
        wxStoreMerchantOrderDO2.setOrderState(0);
        wxStoreMerchantOrderDO2.setDenialReason("关台拒单");
        wxStoreMerchantOrderDO2.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDO2.setOperationGuid("operationGuid");
        wxStoreMerchantOrderDO2.setOperationSource(0);
        wxStoreMerchantOrderDO2.setOperationName("operationName");
        wxStoreMerchantOrderDO2.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> entityList = Arrays.asList(wxStoreMerchantOrderDO2);
        verify(mockWxStoreMerchantOrderService).updateBatchById(entityList);
        verify(mockWebsocketMessageHelper).sendOrderEmqMessage("tableGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delTableCardList("storeGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockNotifyMessageQueue).add(WxMemberModeNotifyReqDTO.builder()
                .remainingTime(0L)
                .userInfoDTO(UserInfoDTO.builder()
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .allianceId("allianceId")
                        .source("source")
                        .build())
                .wxMemberTradeNotifyReqDTO(WxMemberTradeNotifyReqDTO.builder()
                        .enterpriseGuid("enterpriseGuid")
                        .openId("data")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .actuallyPayFee(new BigDecimal("0.00"))
                        .orderGuid("guid")
                        .memberInfoCardGuid("memberInfoCardGuid")
                        .brandGuid("brandGuid")
                        .build())
                .build());
    }

    @Test
    public void testComplete_WxStoreTradeOrderServiceReturnsNull() {
        // Setup
        final TableStatusChangeDTO tableStatusChangeDTO = new TableStatusChangeDTO();
        tableStatusChangeDTO.setDeviceType(0);
        tableStatusChangeDTO.setEnterpriseGuid("enterpriseGuid");
        tableStatusChangeDTO.setStoreGuid("storeGuid");
        tableStatusChangeDTO.setUserGuid("operationGuid");
        tableStatusChangeDTO.setUserName("operationName");
        tableStatusChangeDTO.setOrderGuid("data");
        tableStatusChangeDTO.setTableGuid("tableGuid");

        // Configure EnterpriseClientService.findMemberInfoByOrganizationGuid(...).
        final MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
        multiMemberDTO.setId(0L);
        multiMemberDTO.setEnterpriseGuid("enterpriseGuid");
        multiMemberDTO.setMultiMemberGuid("");
        multiMemberDTO.setMultiMemberName("multiMemberName");
        multiMemberDTO.setEnabled(false);
        when(mockEnterpriseClientService.findMemberInfoByOrganizationGuid("storeGuid")).thenReturn(multiMemberDTO);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        returnItemDTO.setDineInItemDTO(dineInItemDTO);
        dineinOrderDetailRespDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setItemName("itemName");
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberInfoCardGuid");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreTradeOrderService.getMainOrderDetails(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("orderGuid");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        dineinOrderDetailRespDTO1.setState(0);
        dineinOrderDetailRespDTO1.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO1 = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setItemName("itemName");
        returnItemDTO1.setDineInItemDTO(dineInItemDTO2);
        dineinOrderDetailRespDTO1.setReturnItemDTOS(Arrays.asList(returnItemDTO1));
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setItemName("itemName");
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO3));
        dineinOrderDetailRespDTO1.setMemberCardGuid("memberInfoCardGuid");
        when(mockWxStoreTradeOrderService.getMainOrderDetails(dineinOrderDetailRespDTO1)).thenReturn(null);

        // Run the test
        wxStoreOrderStatusChangeServiceImplUnderTest.complete(tableStatusChangeDTO);

        // Verify the results
    }

    @Test
    public void testComplete_WxStoreMerchantOrderServiceListReturnsNoItems() {
        // Setup
        final TableStatusChangeDTO tableStatusChangeDTO = new TableStatusChangeDTO();
        tableStatusChangeDTO.setDeviceType(0);
        tableStatusChangeDTO.setEnterpriseGuid("enterpriseGuid");
        tableStatusChangeDTO.setStoreGuid("storeGuid");
        tableStatusChangeDTO.setUserGuid("operationGuid");
        tableStatusChangeDTO.setUserName("operationName");
        tableStatusChangeDTO.setOrderGuid("data");
        tableStatusChangeDTO.setTableGuid("tableGuid");

        // Configure EnterpriseClientService.findMemberInfoByOrganizationGuid(...).
        final MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
        multiMemberDTO.setId(0L);
        multiMemberDTO.setEnterpriseGuid("enterpriseGuid");
        multiMemberDTO.setMultiMemberGuid("");
        multiMemberDTO.setMultiMemberName("multiMemberName");
        multiMemberDTO.setEnabled(false);
        when(mockEnterpriseClientService.findMemberInfoByOrganizationGuid("storeGuid")).thenReturn(multiMemberDTO);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        returnItemDTO.setDineInItemDTO(dineInItemDTO);
        dineinOrderDetailRespDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setItemName("itemName");
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberInfoCardGuid");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreTradeOrderService.getMainOrderDetails(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("orderGuid");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        dineinOrderDetailRespDTO1.setState(0);
        dineinOrderDetailRespDTO1.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO1 = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setItemName("itemName");
        returnItemDTO1.setDineInItemDTO(dineInItemDTO2);
        dineinOrderDetailRespDTO1.setReturnItemDTOS(Arrays.asList(returnItemDTO1));
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setItemName("itemName");
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO3));
        dineinOrderDetailRespDTO1.setMemberCardGuid("memberInfoCardGuid");
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO2 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO2.setGuid("orderGuid");
        dineinOrderDetailRespDTO2.setTradeMode(0);
        dineinOrderDetailRespDTO2.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setOrderNo("orderNo");
        dineinOrderDetailRespDTO2.setState(0);
        dineinOrderDetailRespDTO2.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO2 = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO4 = new DineInItemDTO();
        dineInItemDTO4.setItemName("itemName");
        returnItemDTO2.setDineInItemDTO(dineInItemDTO4);
        dineinOrderDetailRespDTO2.setReturnItemDTOS(Arrays.asList(returnItemDTO2));
        final DineInItemDTO dineInItemDTO5 = new DineInItemDTO();
        dineInItemDTO5.setItemName("itemName");
        dineinOrderDetailRespDTO2.setDineInItemDTOS(Arrays.asList(dineInItemDTO5));
        dineinOrderDetailRespDTO2.setMemberCardGuid("memberInfoCardGuid");
        when(mockWxStoreTradeOrderService.getMainOrderDetails(dineinOrderDetailRespDTO2))
                .thenReturn(dineinOrderDetailRespDTO1);

        when(mockWxStoreMerchantOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockWxOrderRecordService.updateById(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0))).thenReturn(false);

        // Configure WxOrderRecordService.getOutStandingOrders(...).
        final List<WxOrderRecordDO> wxOrderRecordDOS = Arrays.asList(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        when(mockWxOrderRecordService.getOutStandingOrders("tableGuid")).thenReturn(wxOrderRecordDOS);

        // Configure WxStoreMerchantOrderService.firstSubmit(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setIsDel(0);
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("关台拒单");
        wxStoreMerchantOrderDO.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDO.setOperationGuid("operationGuid");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("operationName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("orderRecordGuid");
        when(mockWxStoreMerchantOrderService.firstSubmit("guid")).thenReturn(wxStoreMerchantOrderDO);

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        when(mockWxStoreMerchantOrderService.update(any(LambdaUpdateWrapper.class))).thenReturn(false);
        when(mockRedisUtils.hKeyList("key")).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockHsaBaseClientService.findOpenIDByMemberGuid("memberInfoCardGuid"))
                .thenReturn(new ResponseModel<>("data"));

        // Configure HsaBaseClientService.getMemberState(...).
        final ResponseAccountStatus responseAccountStatus = new ResponseAccountStatus();
        responseAccountStatus.setMemberInfoGuid("memberInfoGuid");
        responseAccountStatus.setOperSubjectGuid("operSubjectGuid");
        responseAccountStatus.setPhoneNum("phoneNum");
        responseAccountStatus.setNickName("nickName");
        responseAccountStatus.setAccountState(0);
        final ResponseModel<ResponseAccountStatus> responseAccountStatusResponseModel = new ResponseModel<>(
                responseAccountStatus);
        when(mockHsaBaseClientService.getMemberState("data", "enterpriseGuid"))
                .thenReturn(responseAccountStatusResponseModel);

        // Run the test
        wxStoreOrderStatusChangeServiceImplUnderTest.complete(tableStatusChangeDTO);

        // Verify the results
        verify(mockRedisUtils).delete("key");
        verify(mockWebsocketMessageHelper).sendOrderEmqMessage("tableGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delTableCardList("storeGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockNotifyMessageQueue).add(WxMemberModeNotifyReqDTO.builder()
                .remainingTime(0L)
                .userInfoDTO(UserInfoDTO.builder()
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .allianceId("allianceId")
                        .source("source")
                        .build())
                .wxMemberTradeNotifyReqDTO(WxMemberTradeNotifyReqDTO.builder()
                        .enterpriseGuid("enterpriseGuid")
                        .openId("data")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .actuallyPayFee(new BigDecimal("0.00"))
                        .orderGuid("guid")
                        .memberInfoCardGuid("memberInfoCardGuid")
                        .brandGuid("brandGuid")
                        .build())
                .build());
    }

    @Test
    public void testComplete_WxOrderRecordServiceListByIdsReturnsNoItems() {
        // Setup
        final TableStatusChangeDTO tableStatusChangeDTO = new TableStatusChangeDTO();
        tableStatusChangeDTO.setDeviceType(0);
        tableStatusChangeDTO.setEnterpriseGuid("enterpriseGuid");
        tableStatusChangeDTO.setStoreGuid("storeGuid");
        tableStatusChangeDTO.setUserGuid("operationGuid");
        tableStatusChangeDTO.setUserName("operationName");
        tableStatusChangeDTO.setOrderGuid("data");
        tableStatusChangeDTO.setTableGuid("tableGuid");

        // Configure EnterpriseClientService.findMemberInfoByOrganizationGuid(...).
        final MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
        multiMemberDTO.setId(0L);
        multiMemberDTO.setEnterpriseGuid("enterpriseGuid");
        multiMemberDTO.setMultiMemberGuid("");
        multiMemberDTO.setMultiMemberName("multiMemberName");
        multiMemberDTO.setEnabled(false);
        when(mockEnterpriseClientService.findMemberInfoByOrganizationGuid("storeGuid")).thenReturn(multiMemberDTO);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        returnItemDTO.setDineInItemDTO(dineInItemDTO);
        dineinOrderDetailRespDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setItemName("itemName");
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberInfoCardGuid");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreTradeOrderService.getMainOrderDetails(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("orderGuid");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        dineinOrderDetailRespDTO1.setState(0);
        dineinOrderDetailRespDTO1.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO1 = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setItemName("itemName");
        returnItemDTO1.setDineInItemDTO(dineInItemDTO2);
        dineinOrderDetailRespDTO1.setReturnItemDTOS(Arrays.asList(returnItemDTO1));
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setItemName("itemName");
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO3));
        dineinOrderDetailRespDTO1.setMemberCardGuid("memberInfoCardGuid");
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO2 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO2.setGuid("orderGuid");
        dineinOrderDetailRespDTO2.setTradeMode(0);
        dineinOrderDetailRespDTO2.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setOrderNo("orderNo");
        dineinOrderDetailRespDTO2.setState(0);
        dineinOrderDetailRespDTO2.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO2 = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO4 = new DineInItemDTO();
        dineInItemDTO4.setItemName("itemName");
        returnItemDTO2.setDineInItemDTO(dineInItemDTO4);
        dineinOrderDetailRespDTO2.setReturnItemDTOS(Arrays.asList(returnItemDTO2));
        final DineInItemDTO dineInItemDTO5 = new DineInItemDTO();
        dineInItemDTO5.setItemName("itemName");
        dineinOrderDetailRespDTO2.setDineInItemDTOS(Arrays.asList(dineInItemDTO5));
        dineinOrderDetailRespDTO2.setMemberCardGuid("memberInfoCardGuid");
        when(mockWxStoreTradeOrderService.getMainOrderDetails(dineinOrderDetailRespDTO2))
                .thenReturn(dineinOrderDetailRespDTO1);

        // Configure WxStoreMerchantOrderService.list(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setIsDel(0);
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("关台拒单");
        wxStoreMerchantOrderDO.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDO.setOperationGuid("operationGuid");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("operationName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(wxStoreMerchantOrderDOS);

        when(mockWxOrderRecordService.listByIds(Arrays.asList("value"))).thenReturn(Collections.emptyList());
        when(mockWxOrderRecordService.updateById(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0))).thenReturn(false);

        // Configure WxOrderRecordService.getOutStandingOrders(...).
        final List<WxOrderRecordDO> wxOrderRecordDOS = Arrays.asList(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        when(mockWxOrderRecordService.getOutStandingOrders("tableGuid")).thenReturn(wxOrderRecordDOS);

        // Configure WxStoreMerchantOrderService.firstSubmit(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO1 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO1.setIsDel(0);
        wxStoreMerchantOrderDO1.setOpenId("openId");
        wxStoreMerchantOrderDO1.setOrderState(0);
        wxStoreMerchantOrderDO1.setDenialReason("关台拒单");
        wxStoreMerchantOrderDO1.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDO1.setOperationGuid("operationGuid");
        wxStoreMerchantOrderDO1.setOperationSource(0);
        wxStoreMerchantOrderDO1.setOperationName("operationName");
        wxStoreMerchantOrderDO1.setOrderRecordGuid("orderRecordGuid");
        when(mockWxStoreMerchantOrderService.firstSubmit("guid")).thenReturn(wxStoreMerchantOrderDO1);

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        when(mockWxStoreMerchantOrderService.update(any(LambdaUpdateWrapper.class))).thenReturn(false);
        when(mockRedisUtils.hKeyList("key")).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockHsaBaseClientService.findOpenIDByMemberGuid("memberInfoCardGuid"))
                .thenReturn(new ResponseModel<>("data"));

        // Configure HsaBaseClientService.getMemberState(...).
        final ResponseAccountStatus responseAccountStatus = new ResponseAccountStatus();
        responseAccountStatus.setMemberInfoGuid("memberInfoGuid");
        responseAccountStatus.setOperSubjectGuid("operSubjectGuid");
        responseAccountStatus.setPhoneNum("phoneNum");
        responseAccountStatus.setNickName("nickName");
        responseAccountStatus.setAccountState(0);
        final ResponseModel<ResponseAccountStatus> responseAccountStatusResponseModel = new ResponseModel<>(
                responseAccountStatus);
        when(mockHsaBaseClientService.getMemberState("data", "enterpriseGuid"))
                .thenReturn(responseAccountStatusResponseModel);

        // Run the test
        wxStoreOrderStatusChangeServiceImplUnderTest.complete(tableStatusChangeDTO);

        // Verify the results
        verify(mockRedisUtils).delete("key");

        // Confirm WxStoreMerchantOrderService.updateBatchById(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO2 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO2.setIsDel(0);
        wxStoreMerchantOrderDO2.setOpenId("openId");
        wxStoreMerchantOrderDO2.setOrderState(0);
        wxStoreMerchantOrderDO2.setDenialReason("关台拒单");
        wxStoreMerchantOrderDO2.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDO2.setOperationGuid("operationGuid");
        wxStoreMerchantOrderDO2.setOperationSource(0);
        wxStoreMerchantOrderDO2.setOperationName("operationName");
        wxStoreMerchantOrderDO2.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> entityList = Arrays.asList(wxStoreMerchantOrderDO2);
        verify(mockWxStoreMerchantOrderService).updateBatchById(entityList);
        verify(mockWebsocketMessageHelper).sendOrderEmqMessage("tableGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delTableCardList("storeGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockNotifyMessageQueue).add(WxMemberModeNotifyReqDTO.builder()
                .remainingTime(0L)
                .userInfoDTO(UserInfoDTO.builder()
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .allianceId("allianceId")
                        .source("source")
                        .build())
                .wxMemberTradeNotifyReqDTO(WxMemberTradeNotifyReqDTO.builder()
                        .enterpriseGuid("enterpriseGuid")
                        .openId("data")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .actuallyPayFee(new BigDecimal("0.00"))
                        .orderGuid("guid")
                        .memberInfoCardGuid("memberInfoCardGuid")
                        .brandGuid("brandGuid")
                        .build())
                .build());
    }

    @Test
    public void testComplete_WxOrderRecordServiceGetOutStandingOrdersReturnsNoItems() {
        // Setup
        final TableStatusChangeDTO tableStatusChangeDTO = new TableStatusChangeDTO();
        tableStatusChangeDTO.setDeviceType(0);
        tableStatusChangeDTO.setEnterpriseGuid("enterpriseGuid");
        tableStatusChangeDTO.setStoreGuid("storeGuid");
        tableStatusChangeDTO.setUserGuid("operationGuid");
        tableStatusChangeDTO.setUserName("operationName");
        tableStatusChangeDTO.setOrderGuid("data");
        tableStatusChangeDTO.setTableGuid("tableGuid");

        // Configure EnterpriseClientService.findMemberInfoByOrganizationGuid(...).
        final MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
        multiMemberDTO.setId(0L);
        multiMemberDTO.setEnterpriseGuid("enterpriseGuid");
        multiMemberDTO.setMultiMemberGuid("");
        multiMemberDTO.setMultiMemberName("multiMemberName");
        multiMemberDTO.setEnabled(false);
        when(mockEnterpriseClientService.findMemberInfoByOrganizationGuid("storeGuid")).thenReturn(multiMemberDTO);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        returnItemDTO.setDineInItemDTO(dineInItemDTO);
        dineinOrderDetailRespDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setItemName("itemName");
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberInfoCardGuid");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreTradeOrderService.getMainOrderDetails(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("orderGuid");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        dineinOrderDetailRespDTO1.setState(0);
        dineinOrderDetailRespDTO1.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO1 = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setItemName("itemName");
        returnItemDTO1.setDineInItemDTO(dineInItemDTO2);
        dineinOrderDetailRespDTO1.setReturnItemDTOS(Arrays.asList(returnItemDTO1));
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setItemName("itemName");
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO3));
        dineinOrderDetailRespDTO1.setMemberCardGuid("memberInfoCardGuid");
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO2 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO2.setGuid("orderGuid");
        dineinOrderDetailRespDTO2.setTradeMode(0);
        dineinOrderDetailRespDTO2.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setOrderNo("orderNo");
        dineinOrderDetailRespDTO2.setState(0);
        dineinOrderDetailRespDTO2.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO2 = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO4 = new DineInItemDTO();
        dineInItemDTO4.setItemName("itemName");
        returnItemDTO2.setDineInItemDTO(dineInItemDTO4);
        dineinOrderDetailRespDTO2.setReturnItemDTOS(Arrays.asList(returnItemDTO2));
        final DineInItemDTO dineInItemDTO5 = new DineInItemDTO();
        dineInItemDTO5.setItemName("itemName");
        dineinOrderDetailRespDTO2.setDineInItemDTOS(Arrays.asList(dineInItemDTO5));
        dineinOrderDetailRespDTO2.setMemberCardGuid("memberInfoCardGuid");
        when(mockWxStoreTradeOrderService.getMainOrderDetails(dineinOrderDetailRespDTO2))
                .thenReturn(dineinOrderDetailRespDTO1);

        // Configure WxStoreMerchantOrderService.list(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setIsDel(0);
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("关台拒单");
        wxStoreMerchantOrderDO.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDO.setOperationGuid("operationGuid");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("operationName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(wxStoreMerchantOrderDOS);

        // Configure WxOrderRecordService.listByIds(...).
        final Collection<WxOrderRecordDO> wxOrderRecordDOS = Arrays.asList(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        when(mockWxOrderRecordService.listByIds(Arrays.asList("value"))).thenReturn(wxOrderRecordDOS);

        when(mockOrderItemService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));
        when(mockWxOrderRecordService.updateById(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0))).thenReturn(false);
        when(mockWxOrderRecordService.getOutStandingOrders("tableGuid")).thenReturn(Collections.emptyList());

        // Configure WxStoreMerchantOrderService.firstSubmit(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO1 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO1.setIsDel(0);
        wxStoreMerchantOrderDO1.setOpenId("openId");
        wxStoreMerchantOrderDO1.setOrderState(0);
        wxStoreMerchantOrderDO1.setDenialReason("关台拒单");
        wxStoreMerchantOrderDO1.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDO1.setOperationGuid("operationGuid");
        wxStoreMerchantOrderDO1.setOperationSource(0);
        wxStoreMerchantOrderDO1.setOperationName("operationName");
        wxStoreMerchantOrderDO1.setOrderRecordGuid("orderRecordGuid");
        when(mockWxStoreMerchantOrderService.firstSubmit("guid")).thenReturn(wxStoreMerchantOrderDO1);

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        when(mockWxStoreMerchantOrderService.update(any(LambdaUpdateWrapper.class))).thenReturn(false);
        when(mockRedisUtils.hKeyList("key")).thenReturn(new HashSet<>(Arrays.asList("value")));
        when(mockHsaBaseClientService.findOpenIDByMemberGuid("memberInfoCardGuid"))
                .thenReturn(new ResponseModel<>("data"));

        // Configure HsaBaseClientService.getMemberState(...).
        final ResponseAccountStatus responseAccountStatus = new ResponseAccountStatus();
        responseAccountStatus.setMemberInfoGuid("memberInfoGuid");
        responseAccountStatus.setOperSubjectGuid("operSubjectGuid");
        responseAccountStatus.setPhoneNum("phoneNum");
        responseAccountStatus.setNickName("nickName");
        responseAccountStatus.setAccountState(0);
        final ResponseModel<ResponseAccountStatus> responseAccountStatusResponseModel = new ResponseModel<>(
                responseAccountStatus);
        when(mockHsaBaseClientService.getMemberState("data", "enterpriseGuid"))
                .thenReturn(responseAccountStatusResponseModel);

        // Run the test
        wxStoreOrderStatusChangeServiceImplUnderTest.complete(tableStatusChangeDTO);

        // Verify the results
        verify(mockRedisUtils).delete("key");

        // Confirm WxStoreMerchantOrderService.updateBatchById(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO2 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO2.setIsDel(0);
        wxStoreMerchantOrderDO2.setOpenId("openId");
        wxStoreMerchantOrderDO2.setOrderState(0);
        wxStoreMerchantOrderDO2.setDenialReason("关台拒单");
        wxStoreMerchantOrderDO2.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDO2.setOperationGuid("operationGuid");
        wxStoreMerchantOrderDO2.setOperationSource(0);
        wxStoreMerchantOrderDO2.setOperationName("operationName");
        wxStoreMerchantOrderDO2.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> entityList = Arrays.asList(wxStoreMerchantOrderDO2);
        verify(mockWxStoreMerchantOrderService).updateBatchById(entityList);
        verify(mockWebsocketMessageHelper).sendOrderEmqMessage("tableGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delTableCardList("storeGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockNotifyMessageQueue).add(WxMemberModeNotifyReqDTO.builder()
                .remainingTime(0L)
                .userInfoDTO(UserInfoDTO.builder()
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .allianceId("allianceId")
                        .source("source")
                        .build())
                .wxMemberTradeNotifyReqDTO(WxMemberTradeNotifyReqDTO.builder()
                        .enterpriseGuid("enterpriseGuid")
                        .openId("data")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .actuallyPayFee(new BigDecimal("0.00"))
                        .orderGuid("guid")
                        .memberInfoCardGuid("memberInfoCardGuid")
                        .brandGuid("brandGuid")
                        .build())
                .build());
    }

    @Test
    public void testComplete_WxStoreMerchantOrderServiceFirstSubmitReturnsNull() {
        // Setup
        final TableStatusChangeDTO tableStatusChangeDTO = new TableStatusChangeDTO();
        tableStatusChangeDTO.setDeviceType(0);
        tableStatusChangeDTO.setEnterpriseGuid("enterpriseGuid");
        tableStatusChangeDTO.setStoreGuid("storeGuid");
        tableStatusChangeDTO.setUserGuid("operationGuid");
        tableStatusChangeDTO.setUserName("operationName");
        tableStatusChangeDTO.setOrderGuid("data");
        tableStatusChangeDTO.setTableGuid("tableGuid");

        // Configure EnterpriseClientService.findMemberInfoByOrganizationGuid(...).
        final MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
        multiMemberDTO.setId(0L);
        multiMemberDTO.setEnterpriseGuid("enterpriseGuid");
        multiMemberDTO.setMultiMemberGuid("");
        multiMemberDTO.setMultiMemberName("multiMemberName");
        multiMemberDTO.setEnabled(false);
        when(mockEnterpriseClientService.findMemberInfoByOrganizationGuid("storeGuid")).thenReturn(multiMemberDTO);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        returnItemDTO.setDineInItemDTO(dineInItemDTO);
        dineinOrderDetailRespDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setItemName("itemName");
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberInfoCardGuid");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreTradeOrderService.getMainOrderDetails(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("orderGuid");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        dineinOrderDetailRespDTO1.setState(0);
        dineinOrderDetailRespDTO1.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO1 = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setItemName("itemName");
        returnItemDTO1.setDineInItemDTO(dineInItemDTO2);
        dineinOrderDetailRespDTO1.setReturnItemDTOS(Arrays.asList(returnItemDTO1));
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setItemName("itemName");
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO3));
        dineinOrderDetailRespDTO1.setMemberCardGuid("memberInfoCardGuid");
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO2 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO2.setGuid("orderGuid");
        dineinOrderDetailRespDTO2.setTradeMode(0);
        dineinOrderDetailRespDTO2.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setOrderNo("orderNo");
        dineinOrderDetailRespDTO2.setState(0);
        dineinOrderDetailRespDTO2.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO2 = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO4 = new DineInItemDTO();
        dineInItemDTO4.setItemName("itemName");
        returnItemDTO2.setDineInItemDTO(dineInItemDTO4);
        dineinOrderDetailRespDTO2.setReturnItemDTOS(Arrays.asList(returnItemDTO2));
        final DineInItemDTO dineInItemDTO5 = new DineInItemDTO();
        dineInItemDTO5.setItemName("itemName");
        dineinOrderDetailRespDTO2.setDineInItemDTOS(Arrays.asList(dineInItemDTO5));
        dineinOrderDetailRespDTO2.setMemberCardGuid("memberInfoCardGuid");
        when(mockWxStoreTradeOrderService.getMainOrderDetails(dineinOrderDetailRespDTO2))
                .thenReturn(dineinOrderDetailRespDTO1);

        // Configure WxStoreMerchantOrderService.list(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setIsDel(0);
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("关台拒单");
        wxStoreMerchantOrderDO.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDO.setOperationGuid("operationGuid");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("operationName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(wxStoreMerchantOrderDOS);

        // Configure WxOrderRecordService.listByIds(...).
        final Collection<WxOrderRecordDO> wxOrderRecordDOS = Arrays.asList(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        when(mockWxOrderRecordService.listByIds(Arrays.asList("value"))).thenReturn(wxOrderRecordDOS);

        when(mockOrderItemService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));
        when(mockWxOrderRecordService.updateById(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0))).thenReturn(false);

        // Configure WxOrderRecordService.getOutStandingOrders(...).
        final List<WxOrderRecordDO> wxOrderRecordDOS1 = Arrays.asList(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        when(mockWxOrderRecordService.getOutStandingOrders("tableGuid")).thenReturn(wxOrderRecordDOS1);

        when(mockWxStoreMerchantOrderService.firstSubmit("guid")).thenReturn(null);

        // Run the test
        wxStoreOrderStatusChangeServiceImplUnderTest.complete(tableStatusChangeDTO);

        // Verify the results
        verify(mockRedisUtils).delete("key");

        // Confirm WxStoreMerchantOrderService.updateBatchById(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO1 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO1.setIsDel(0);
        wxStoreMerchantOrderDO1.setOpenId("openId");
        wxStoreMerchantOrderDO1.setOrderState(0);
        wxStoreMerchantOrderDO1.setDenialReason("关台拒单");
        wxStoreMerchantOrderDO1.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDO1.setOperationGuid("operationGuid");
        wxStoreMerchantOrderDO1.setOperationSource(0);
        wxStoreMerchantOrderDO1.setOperationName("operationName");
        wxStoreMerchantOrderDO1.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> entityList = Arrays.asList(wxStoreMerchantOrderDO1);
        verify(mockWxStoreMerchantOrderService).updateBatchById(entityList);
    }

    @Test
    public void testComplete_WxUserRecordServiceReturnsNull() {
        // Setup
        final TableStatusChangeDTO tableStatusChangeDTO = new TableStatusChangeDTO();
        tableStatusChangeDTO.setDeviceType(0);
        tableStatusChangeDTO.setEnterpriseGuid("enterpriseGuid");
        tableStatusChangeDTO.setStoreGuid("storeGuid");
        tableStatusChangeDTO.setUserGuid("operationGuid");
        tableStatusChangeDTO.setUserName("operationName");
        tableStatusChangeDTO.setOrderGuid("data");
        tableStatusChangeDTO.setTableGuid("tableGuid");

        // Configure EnterpriseClientService.findMemberInfoByOrganizationGuid(...).
        final MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
        multiMemberDTO.setId(0L);
        multiMemberDTO.setEnterpriseGuid("enterpriseGuid");
        multiMemberDTO.setMultiMemberGuid("");
        multiMemberDTO.setMultiMemberName("multiMemberName");
        multiMemberDTO.setEnabled(false);
        when(mockEnterpriseClientService.findMemberInfoByOrganizationGuid("storeGuid")).thenReturn(multiMemberDTO);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        returnItemDTO.setDineInItemDTO(dineInItemDTO);
        dineinOrderDetailRespDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setItemName("itemName");
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberInfoCardGuid");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreTradeOrderService.getMainOrderDetails(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("orderGuid");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        dineinOrderDetailRespDTO1.setState(0);
        dineinOrderDetailRespDTO1.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO1 = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setItemName("itemName");
        returnItemDTO1.setDineInItemDTO(dineInItemDTO2);
        dineinOrderDetailRespDTO1.setReturnItemDTOS(Arrays.asList(returnItemDTO1));
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setItemName("itemName");
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO3));
        dineinOrderDetailRespDTO1.setMemberCardGuid("memberInfoCardGuid");
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO2 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO2.setGuid("orderGuid");
        dineinOrderDetailRespDTO2.setTradeMode(0);
        dineinOrderDetailRespDTO2.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setOrderNo("orderNo");
        dineinOrderDetailRespDTO2.setState(0);
        dineinOrderDetailRespDTO2.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO2 = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO4 = new DineInItemDTO();
        dineInItemDTO4.setItemName("itemName");
        returnItemDTO2.setDineInItemDTO(dineInItemDTO4);
        dineinOrderDetailRespDTO2.setReturnItemDTOS(Arrays.asList(returnItemDTO2));
        final DineInItemDTO dineInItemDTO5 = new DineInItemDTO();
        dineInItemDTO5.setItemName("itemName");
        dineinOrderDetailRespDTO2.setDineInItemDTOS(Arrays.asList(dineInItemDTO5));
        dineinOrderDetailRespDTO2.setMemberCardGuid("memberInfoCardGuid");
        when(mockWxStoreTradeOrderService.getMainOrderDetails(dineinOrderDetailRespDTO2))
                .thenReturn(dineinOrderDetailRespDTO1);

        // Configure WxStoreMerchantOrderService.list(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setIsDel(0);
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("关台拒单");
        wxStoreMerchantOrderDO.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDO.setOperationGuid("operationGuid");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("operationName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(wxStoreMerchantOrderDOS);

        // Configure WxOrderRecordService.listByIds(...).
        final Collection<WxOrderRecordDO> wxOrderRecordDOS = Arrays.asList(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        when(mockWxOrderRecordService.listByIds(Arrays.asList("value"))).thenReturn(wxOrderRecordDOS);

        when(mockOrderItemService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));
        when(mockWxOrderRecordService.updateById(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0))).thenReturn(false);

        // Configure WxOrderRecordService.getOutStandingOrders(...).
        final List<WxOrderRecordDO> wxOrderRecordDOS1 = Arrays.asList(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        when(mockWxOrderRecordService.getOutStandingOrders("tableGuid")).thenReturn(wxOrderRecordDOS1);

        // Configure WxStoreMerchantOrderService.firstSubmit(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO1 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO1.setIsDel(0);
        wxStoreMerchantOrderDO1.setOpenId("openId");
        wxStoreMerchantOrderDO1.setOrderState(0);
        wxStoreMerchantOrderDO1.setDenialReason("关台拒单");
        wxStoreMerchantOrderDO1.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDO1.setOperationGuid("operationGuid");
        wxStoreMerchantOrderDO1.setOperationSource(0);
        wxStoreMerchantOrderDO1.setOperationName("operationName");
        wxStoreMerchantOrderDO1.setOrderRecordGuid("orderRecordGuid");
        when(mockWxStoreMerchantOrderService.firstSubmit("guid")).thenReturn(wxStoreMerchantOrderDO1);

        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(null);

        // Run the test
        wxStoreOrderStatusChangeServiceImplUnderTest.complete(tableStatusChangeDTO);

        // Verify the results
        verify(mockRedisUtils).delete("key");

        // Confirm WxStoreMerchantOrderService.updateBatchById(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO2 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO2.setIsDel(0);
        wxStoreMerchantOrderDO2.setOpenId("openId");
        wxStoreMerchantOrderDO2.setOrderState(0);
        wxStoreMerchantOrderDO2.setDenialReason("关台拒单");
        wxStoreMerchantOrderDO2.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDO2.setOperationGuid("operationGuid");
        wxStoreMerchantOrderDO2.setOperationSource(0);
        wxStoreMerchantOrderDO2.setOperationName("operationName");
        wxStoreMerchantOrderDO2.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> entityList = Arrays.asList(wxStoreMerchantOrderDO2);
        verify(mockWxStoreMerchantOrderService).updateBatchById(entityList);
    }

    @Test
    public void testComplete_RedisUtilsHKeyListReturnsNoItems() {
        // Setup
        final TableStatusChangeDTO tableStatusChangeDTO = new TableStatusChangeDTO();
        tableStatusChangeDTO.setDeviceType(0);
        tableStatusChangeDTO.setEnterpriseGuid("enterpriseGuid");
        tableStatusChangeDTO.setStoreGuid("storeGuid");
        tableStatusChangeDTO.setUserGuid("operationGuid");
        tableStatusChangeDTO.setUserName("operationName");
        tableStatusChangeDTO.setOrderGuid("data");
        tableStatusChangeDTO.setTableGuid("tableGuid");

        // Configure EnterpriseClientService.findMemberInfoByOrganizationGuid(...).
        final MultiMemberDTO multiMemberDTO = new MultiMemberDTO();
        multiMemberDTO.setId(0L);
        multiMemberDTO.setEnterpriseGuid("enterpriseGuid");
        multiMemberDTO.setMultiMemberGuid("");
        multiMemberDTO.setMultiMemberName("multiMemberName");
        multiMemberDTO.setEnabled(false);
        when(mockEnterpriseClientService.findMemberInfoByOrganizationGuid("storeGuid")).thenReturn(multiMemberDTO);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setItemName("itemName");
        returnItemDTO.setDineInItemDTO(dineInItemDTO);
        dineinOrderDetailRespDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setItemName("itemName");
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO.setMemberCardGuid("memberInfoCardGuid");
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("data")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxStoreTradeOrderService.getMainOrderDetails(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("orderGuid");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        dineinOrderDetailRespDTO1.setState(0);
        dineinOrderDetailRespDTO1.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO1 = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineInItemDTO2.setItemName("itemName");
        returnItemDTO1.setDineInItemDTO(dineInItemDTO2);
        dineinOrderDetailRespDTO1.setReturnItemDTOS(Arrays.asList(returnItemDTO1));
        final DineInItemDTO dineInItemDTO3 = new DineInItemDTO();
        dineInItemDTO3.setItemName("itemName");
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO3));
        dineinOrderDetailRespDTO1.setMemberCardGuid("memberInfoCardGuid");
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO2 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO2.setGuid("orderGuid");
        dineinOrderDetailRespDTO2.setTradeMode(0);
        dineinOrderDetailRespDTO2.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setOrderNo("orderNo");
        dineinOrderDetailRespDTO2.setState(0);
        dineinOrderDetailRespDTO2.setMemberGuid("memberInfoCardGuid");
        final ReturnItemDTO returnItemDTO2 = new ReturnItemDTO();
        final DineInItemDTO dineInItemDTO4 = new DineInItemDTO();
        dineInItemDTO4.setItemName("itemName");
        returnItemDTO2.setDineInItemDTO(dineInItemDTO4);
        dineinOrderDetailRespDTO2.setReturnItemDTOS(Arrays.asList(returnItemDTO2));
        final DineInItemDTO dineInItemDTO5 = new DineInItemDTO();
        dineInItemDTO5.setItemName("itemName");
        dineinOrderDetailRespDTO2.setDineInItemDTOS(Arrays.asList(dineInItemDTO5));
        dineinOrderDetailRespDTO2.setMemberCardGuid("memberInfoCardGuid");
        when(mockWxStoreTradeOrderService.getMainOrderDetails(dineinOrderDetailRespDTO2))
                .thenReturn(dineinOrderDetailRespDTO1);

        // Configure WxStoreMerchantOrderService.list(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setIsDel(0);
        wxStoreMerchantOrderDO.setOpenId("openId");
        wxStoreMerchantOrderDO.setOrderState(0);
        wxStoreMerchantOrderDO.setDenialReason("关台拒单");
        wxStoreMerchantOrderDO.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDO.setOperationGuid("operationGuid");
        wxStoreMerchantOrderDO.setOperationSource(0);
        wxStoreMerchantOrderDO.setOperationName("operationName");
        wxStoreMerchantOrderDO.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderService.list(any(LambdaQueryWrapper.class))).thenReturn(wxStoreMerchantOrderDOS);

        // Configure WxOrderRecordService.listByIds(...).
        final Collection<WxOrderRecordDO> wxOrderRecordDOS = Arrays.asList(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        when(mockWxOrderRecordService.listByIds(Arrays.asList("value"))).thenReturn(wxOrderRecordDOS);

        when(mockOrderItemService.lambdaQuery()).thenReturn(new LambdaQueryChainWrapper<>(null));
        when(mockWxOrderRecordService.updateById(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0))).thenReturn(false);

        // Configure WxOrderRecordService.getOutStandingOrders(...).
        final List<WxOrderRecordDO> wxOrderRecordDOS1 = Arrays.asList(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "guid", "brandGuid", "brandName", "logUrl",
                        "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        when(mockWxOrderRecordService.getOutStandingOrders("tableGuid")).thenReturn(wxOrderRecordDOS1);

        // Configure WxStoreMerchantOrderService.firstSubmit(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO1 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO1.setIsDel(0);
        wxStoreMerchantOrderDO1.setOpenId("openId");
        wxStoreMerchantOrderDO1.setOrderState(0);
        wxStoreMerchantOrderDO1.setDenialReason("关台拒单");
        wxStoreMerchantOrderDO1.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDO1.setOperationGuid("operationGuid");
        wxStoreMerchantOrderDO1.setOperationSource(0);
        wxStoreMerchantOrderDO1.setOperationName("operationName");
        wxStoreMerchantOrderDO1.setOrderRecordGuid("orderRecordGuid");
        when(mockWxStoreMerchantOrderService.firstSubmit("guid")).thenReturn(wxStoreMerchantOrderDO1);

        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "guid", "openId", "nickName", "headImgUrl", 0,
                "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        when(mockWxStoreMerchantOrderService.update(any(LambdaUpdateWrapper.class))).thenReturn(false);
        when(mockRedisUtils.hKeyList("key")).thenReturn(Collections.emptySet());
        when(mockHsaBaseClientService.findOpenIDByMemberGuid("memberInfoCardGuid"))
                .thenReturn(new ResponseModel<>("data"));

        // Configure HsaBaseClientService.getMemberState(...).
        final ResponseAccountStatus responseAccountStatus = new ResponseAccountStatus();
        responseAccountStatus.setMemberInfoGuid("memberInfoGuid");
        responseAccountStatus.setOperSubjectGuid("operSubjectGuid");
        responseAccountStatus.setPhoneNum("phoneNum");
        responseAccountStatus.setNickName("nickName");
        responseAccountStatus.setAccountState(0);
        final ResponseModel<ResponseAccountStatus> responseAccountStatusResponseModel = new ResponseModel<>(
                responseAccountStatus);
        when(mockHsaBaseClientService.getMemberState("data", "enterpriseGuid"))
                .thenReturn(responseAccountStatusResponseModel);

        // Run the test
        wxStoreOrderStatusChangeServiceImplUnderTest.complete(tableStatusChangeDTO);

        // Verify the results
        verify(mockRedisUtils).delete("key");

        // Confirm WxStoreMerchantOrderService.updateBatchById(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO2 = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO2.setIsDel(0);
        wxStoreMerchantOrderDO2.setOpenId("openId");
        wxStoreMerchantOrderDO2.setOrderState(0);
        wxStoreMerchantOrderDO2.setDenialReason("关台拒单");
        wxStoreMerchantOrderDO2.setOrderGuid("orderGuid");
        wxStoreMerchantOrderDO2.setOperationGuid("operationGuid");
        wxStoreMerchantOrderDO2.setOperationSource(0);
        wxStoreMerchantOrderDO2.setOperationName("operationName");
        wxStoreMerchantOrderDO2.setOrderRecordGuid("orderRecordGuid");
        final List<WxStoreMerchantOrderDO> entityList = Arrays.asList(wxStoreMerchantOrderDO2);
        verify(mockWxStoreMerchantOrderService).updateBatchById(entityList);
        verify(mockWebsocketMessageHelper).sendOrderEmqMessage("tableGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockUserMemberSessionUtils).delTableCardList("storeGuid", new HashSet<>(Arrays.asList("value")));
        verify(mockNotifyMessageQueue).add(WxMemberModeNotifyReqDTO.builder()
                .remainingTime(0L)
                .userInfoDTO(UserInfoDTO.builder()
                        .enterpriseGuid("enterpriseGuid")
                        .storeGuid("storeGuid")
                        .allianceId("allianceId")
                        .source("source")
                        .build())
                .wxMemberTradeNotifyReqDTO(WxMemberTradeNotifyReqDTO.builder()
                        .enterpriseGuid("enterpriseGuid")
                        .openId("data")
                        .storeGuid("storeGuid")
                        .storeName("storeName")
                        .actuallyPayFee(new BigDecimal("0.00"))
                        .orderGuid("guid")
                        .memberInfoCardGuid("memberInfoCardGuid")
                        .brandGuid("brandGuid")
                        .build())
                .build());
    }

    @Test
    public void testCancel() {
        // Setup
        final TableStatusChangeDTO tableStatusChangeDTO = new TableStatusChangeDTO();
        tableStatusChangeDTO.setDeviceType(0);
        tableStatusChangeDTO.setEnterpriseGuid("enterpriseGuid");
        tableStatusChangeDTO.setStoreGuid("storeGuid");
        tableStatusChangeDTO.setUserGuid("operationGuid");
        tableStatusChangeDTO.setUserName("operationName");
        tableStatusChangeDTO.setOrderGuid("data");
        tableStatusChangeDTO.setTableGuid("tableGuid");

        when(mockWxStoreSessionDetailsService.getMerchantBatchGuid("tableGuid")).thenReturn("result");

        // Run the test
        wxStoreOrderStatusChangeServiceImplUnderTest.cancel(tableStatusChangeDTO);

        // Verify the results
    }
}
