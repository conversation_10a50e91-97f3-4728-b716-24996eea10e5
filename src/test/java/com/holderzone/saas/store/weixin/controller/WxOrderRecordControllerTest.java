package com.holderzone.saas.store.weixin.controller;

import com.holderzone.holder.saas.weixin.common.ErrorCoderEnum;
import com.holderzone.holder.saas.weixin.entry.dto.*;
import com.holderzone.holder.saas.weixin.entry.dto.req.WxOrderDetailReqDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderDTO;
import com.holderzone.saas.store.dto.weixin.deal.PayBackOrderRecordDTO;
import com.holderzone.saas.store.dto.weixin.deal.UnMemberMessageDTO;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberSessionDTO;
import com.holderzone.saas.store.dto.weixin.deal.WxStoreUserOrderItemDTO;
import com.holderzone.saas.store.dto.weixin.req.WxBrandUserOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.*;
import com.holderzone.saas.store.weixin.entity.domain.WxOrderRecordDO;
import com.holderzone.saas.store.weixin.entity.domain.WxStoreMerchantOrderDO;
import com.holderzone.saas.store.weixin.helper.OrderDetailHelper;
import com.holderzone.saas.store.weixin.manager.WxOrderManager;
import com.holderzone.saas.store.weixin.service.WxOrderRecordService;
import com.holderzone.saas.store.weixin.service.WxStoreMerchantOrderService;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreDineInBillClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreTableClientService;
import com.holderzone.saas.store.weixin.utils.UserMemberSessionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxOrderRecordController.class)
public class WxOrderRecordControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxOrderRecordService mockWxOrderRecordService;
    @MockBean
    private WxStoreMerchantOrderService mockWxStoreMerchantOrderService;
    @MockBean
    private WxStoreSessionDetailsService mockWxStoreSessionDetailsService;
    @MockBean
    private WxStoreTableClientService mockWxStoreTableClientService;
    @MockBean
    private UserMemberSessionUtils mockUserMemberSessionUtils;
    @MockBean
    private WxStoreDineInBillClientService mockWxStoreDineInBillClientService;
    @MockBean
    private WxOrderManager mockWxOrderManager;

    @Test
    public void testGetWxOrderRecordByCondition() throws Exception {
        // Setup
        // Configure WxOrderRecordService.getWxOrderRecordByCondition(...).
        final List<WxOrderRecordDO> wxOrderRecordDOS = Arrays.asList(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "userRecordGuid", "brandGuid", "brandName",
                        "logUrl", "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0));
        when(mockWxOrderRecordService.getWxOrderRecordByCondition(
                WxStoreAdvanceConsumerReqDTO.builder().build())).thenReturn(wxOrderRecordDOS);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_record/get_all")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetWxOrderRecordByCondition_WxOrderRecordServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockWxOrderRecordService.getWxOrderRecordByCondition(
                WxStoreAdvanceConsumerReqDTO.builder().build())).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_record/get_all")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testDelWxOrderRecord() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_record/del")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockWxOrderRecordService).delWxOrderRecord(WxStoreAdvanceConsumerReqDTO.builder().build());
    }

    @Test
    public void testGetWxStoreUserOrder() throws Exception {
        // Setup
        // Configure WxOrderRecordService.getWxStoreUserOrder(...).
        final WxStoreUserOrderDTO wxStoreUserOrderDTO = new WxStoreUserOrderDTO();
        final WxStoreDineinOrderDetailsRespDTO wxStoreDineinOrderDetailsRespDTO = new WxStoreDineinOrderDetailsRespDTO();
        wxStoreDineinOrderDetailsRespDTO.setGuid("017012c3-a5ff-42ce-839d-e3055412aafa");
        wxStoreDineinOrderDetailsRespDTO.setLogUrl("logUrl");
        wxStoreDineinOrderDetailsRespDTO.setStoreName("storeName");
        wxStoreDineinOrderDetailsRespDTO.setTradeMode(0);
        wxStoreUserOrderDTO.setOrders(Arrays.asList(wxStoreDineinOrderDetailsRespDTO));
        when(mockWxOrderRecordService.getWxStoreUserOrder(WxStoreAdvanceConsumerReqDTO.builder().build()))
                .thenReturn(wxStoreUserOrderDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_record/user_order")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetWxBrandUserOrder() throws Exception {
        // Setup
        // Configure WxOrderRecordService.getWxBrandUserOrder(...).
        final WxBrandUserOrderDTO wxBrandUserOrderDTO = new WxBrandUserOrderDTO(
                Arrays.asList(new WxBrandUserOrderTitleGroup(0, "orderName", 0)), Arrays.asList(
                new WxBrandUserOrderItemDTO("tradeOrderGuid", "logUrl", WxStoreConsumerDTO.builder().build(), 0,
                        new BigDecimal("0.00"), 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0), Arrays.asList("value"))));
        when(mockWxOrderRecordService.getWxBrandUserOrder(WxBrandUserOrderReqDTO.builder().build()))
                .thenReturn(wxBrandUserOrderDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_record/brand_user_order")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testDetailCalculate() throws Exception {
        // Setup
        // Configure WxOrderRecordService.getById(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid",
                "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid", "areaName",
                "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false,
                "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getById("guid")).thenReturn(wxOrderRecordDO);

        when(mockWxStoreMerchantOrderService.countByStatus("guid", 0, 0)).thenReturn(0);
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn("orderGuid");

        // Configure WxStoreDineInBillClientService.calculate(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setReserveFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setSingleItemUsedMemeberPrice(0);
        dineinOrderDetailRespDTO.setTip("tip");
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setDeviceType(0);
        billCalculateReqDTO.setDeviceId("deviceId");
        billCalculateReqDTO.setEnterpriseGuid("enterpriseGuid");
        billCalculateReqDTO.setStoreGuid("storeGuid");
        billCalculateReqDTO.setOrderGuid("orderGuid");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setMemberIntegral(0);
        billCalculateReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO.setMemberPhone("memberPhone");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVerify(0);
        when(mockWxStoreDineInBillClientService.calculate(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO);

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure WxStoreSessionDetailsService.getTableDetail(...).
        final TableDTO tableDTO = TableDTO.builder()
                .code("code")
                .build();
        when(mockWxStoreSessionDetailsService.getTableDetail("tableGuid")).thenReturn(tableDTO);

        // Configure WxStoreMerchantOrderService.selectByOrderRecordGuid(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setId(0L);
        wxStoreMerchantOrderDO.setGuid("460c34e9-54f1-4c23-9eae-5bacf7e9f3e3");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setOrderState(0);
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderService.selectByOrderRecordGuid("guid", Arrays.asList("value")))
                .thenReturn(wxStoreMerchantOrderDOS);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_record/detail/calculate")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockWxOrderRecordService).dealFastOrderTimeOut("guid");
        verify(mockUserMemberSessionUtils).addUserMemberSession(
                new UserMemberSessionDTO("storeGuid", "openId", "userGuid", false, "nickName", "memberInfoGuid",
                        "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false, "volumeCode", false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid"));

        // Confirm WxOrderManager.initAreaMap(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setUpperState(0);
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setReserveFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO1.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO1.setSingleItemUsedMemeberPrice(0);
        dineinOrderDetailRespDTO1.setTip("tip");
        final OrderDetailHelper detailBuilder = new OrderDetailHelper(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "userRecordGuid", "brandGuid", "brandName",
                        "logUrl", "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0), dineinOrderDetailRespDTO1, false, false,
                "memberInfoCardGuid");
        verify(mockWxOrderManager).initAreaMap(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "userRecordGuid", "brandGuid", "brandName",
                        "logUrl", "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0), detailBuilder);
    }

    @Test
    public void testDetailCalculate_WxOrderRecordServiceGetByIdReturnsNull() throws Exception {
        // Setup
        when(mockWxOrderRecordService.getById("guid")).thenReturn(null);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_record/detail/calculate")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("", true));
    }

    @Test
    public void testDetailCalculate_UserMemberSessionUtilsGetUserMemberSessionReturnsNull() throws Exception {
        // Setup
        // Configure WxOrderRecordService.getById(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid",
                "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid", "areaName",
                "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false,
                "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getById("guid")).thenReturn(wxOrderRecordDO);

        when(mockWxStoreMerchantOrderService.countByStatus("guid", 0, 0)).thenReturn(0);
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn("orderGuid");

        // Configure WxStoreDineInBillClientService.calculate(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setReserveFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setSingleItemUsedMemeberPrice(0);
        dineinOrderDetailRespDTO.setTip("tip");
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setDeviceType(0);
        billCalculateReqDTO.setDeviceId("deviceId");
        billCalculateReqDTO.setEnterpriseGuid("enterpriseGuid");
        billCalculateReqDTO.setStoreGuid("storeGuid");
        billCalculateReqDTO.setOrderGuid("orderGuid");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setMemberIntegral(0);
        billCalculateReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO.setMemberPhone("memberPhone");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVerify(0);
        when(mockWxStoreDineInBillClientService.calculate(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO);

        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(null);

        // Configure WxStoreSessionDetailsService.getTableDetail(...).
        final TableDTO tableDTO = TableDTO.builder()
                .code("code")
                .build();
        when(mockWxStoreSessionDetailsService.getTableDetail("tableGuid")).thenReturn(tableDTO);

        // Configure WxStoreMerchantOrderService.selectByOrderRecordGuid(...).
        final WxStoreMerchantOrderDO wxStoreMerchantOrderDO = new WxStoreMerchantOrderDO();
        wxStoreMerchantOrderDO.setId(0L);
        wxStoreMerchantOrderDO.setGuid("460c34e9-54f1-4c23-9eae-5bacf7e9f3e3");
        wxStoreMerchantOrderDO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDO.setOrderState(0);
        final List<WxStoreMerchantOrderDO> wxStoreMerchantOrderDOS = Arrays.asList(wxStoreMerchantOrderDO);
        when(mockWxStoreMerchantOrderService.selectByOrderRecordGuid("guid", Arrays.asList("value")))
                .thenReturn(wxStoreMerchantOrderDOS);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_record/detail/calculate")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockUserMemberSessionUtils).addUserMemberSession(
                new UserMemberSessionDTO("storeGuid", "openId", "userGuid", false, "nickName", "memberInfoGuid",
                        "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false, "volumeCode", false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid"));

        // Confirm WxOrderManager.initAreaMap(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setUpperState(0);
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setReserveFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO1.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO1.setSingleItemUsedMemeberPrice(0);
        dineinOrderDetailRespDTO1.setTip("tip");
        final OrderDetailHelper detailBuilder = new OrderDetailHelper(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "userRecordGuid", "brandGuid", "brandName",
                        "logUrl", "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0), dineinOrderDetailRespDTO1, false, false,
                "memberInfoCardGuid");
        verify(mockWxOrderManager).initAreaMap(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "userRecordGuid", "brandGuid", "brandName",
                        "logUrl", "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0), detailBuilder);
    }

    @Test
    public void testDetailCalculate_WxStoreMerchantOrderServiceSelectByOrderRecordGuidReturnsNoItems() throws Exception {
        // Setup
        // Configure WxOrderRecordService.getById(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid",
                "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid", "areaName",
                "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false,
                "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getById("guid")).thenReturn(wxOrderRecordDO);

        when(mockWxStoreMerchantOrderService.countByStatus("guid", 0, 0)).thenReturn(0);
        when(mockWxStoreTableClientService.getOrderGuid("tableGuid")).thenReturn("orderGuid");

        // Configure WxStoreDineInBillClientService.calculate(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setReserveFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setSingleItemUsedMemeberPrice(0);
        dineinOrderDetailRespDTO.setTip("tip");
        final BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setDeviceType(0);
        billCalculateReqDTO.setDeviceId("deviceId");
        billCalculateReqDTO.setEnterpriseGuid("enterpriseGuid");
        billCalculateReqDTO.setStoreGuid("storeGuid");
        billCalculateReqDTO.setOrderGuid("orderGuid");
        billCalculateReqDTO.setMemberLogin(0);
        billCalculateReqDTO.setMemberIntegral(0);
        billCalculateReqDTO.setMemberInfoCardGuid("memberInfoCardGuid");
        billCalculateReqDTO.setMemberPhone("memberPhone");
        billCalculateReqDTO.setVolumeCode("volumeCode");
        billCalculateReqDTO.setVerify(0);
        when(mockWxStoreDineInBillClientService.calculate(billCalculateReqDTO)).thenReturn(dineinOrderDetailRespDTO);

        // Configure UserMemberSessionUtils.getUserMemberSession(...).
        final UserMemberSessionDTO userMemberSessionDTO = new UserMemberSessionDTO("storeGuid", "openId", "userGuid",
                false, "nickName", "memberInfoGuid", "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false,
                "volumeCode", false, new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid");
        when(mockUserMemberSessionUtils.getUserMemberSession("openId")).thenReturn(userMemberSessionDTO);

        // Configure WxStoreSessionDetailsService.getTableDetail(...).
        final TableDTO tableDTO = TableDTO.builder()
                .code("code")
                .build();
        when(mockWxStoreSessionDetailsService.getTableDetail("tableGuid")).thenReturn(tableDTO);

        when(mockWxStoreMerchantOrderService.selectByOrderRecordGuid("guid", Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_record/detail/calculate")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockUserMemberSessionUtils).addUserMemberSession(
                new UserMemberSessionDTO("storeGuid", "openId", "userGuid", false, "nickName", "memberInfoGuid",
                        "cardGuid", "memberInfoCardGuid", "systemManagementGuid", 0, false, "volumeCode", false,
                        new BigDecimal("0.00"), new BigDecimal("0.00"), "operSubjectGuid"));

        // Confirm WxOrderManager.initAreaMap(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setUpperState(0);
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setReserveFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO1.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO1.setSingleItemUsedMemeberPrice(0);
        dineinOrderDetailRespDTO1.setTip("tip");
        final OrderDetailHelper detailBuilder = new OrderDetailHelper(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "userRecordGuid", "brandGuid", "brandName",
                        "logUrl", "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0), dineinOrderDetailRespDTO1, false, false,
                "memberInfoCardGuid");
        verify(mockWxOrderManager).initAreaMap(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "userRecordGuid", "brandGuid", "brandName",
                        "logUrl", "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0), detailBuilder);

        // Confirm WxOrderManager.initOrderItemImg(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO2 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO2.setUpperState(0);
        dineinOrderDetailRespDTO2.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setReserveFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO2.setOrderNo("orderNo");
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineinOrderDetailRespDTO2.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        dineinOrderDetailRespDTO2.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO2.setSingleItemUsedMemeberPrice(0);
        dineinOrderDetailRespDTO2.setTip("tip");
        final OrderDetailHelper detailBuilder1 = new OrderDetailHelper(
                new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid", "userRecordGuid", "brandGuid", "brandName",
                        "logUrl", "storeGuid", "storeName", "areaGuid", "areaName", "tableGuid", "tableCode", 0, 0,
                        "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false, "memberInfoCardGuid",
                        "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0), dineinOrderDetailRespDTO2, false, false,
                "memberInfoCardGuid");
        verify(mockWxOrderManager).initOrderItemImg(detailBuilder1);
        verify(mockWxOrderManager).updateOrderRecord("guid", "orderGuid");
    }

    @Test
    public void testDetailCalculateCheck() throws Exception {
        // Setup
        when(mockWxStoreMerchantOrderService.countByStatus("guid", 0, 0)).thenReturn(0);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_record/calculate/check")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testDetail() throws Exception {
        // Setup
        // Configure WxOrderManager.detail(...).
        final OrderDetailDTO orderDetailDTO = new OrderDetailDTO();
        orderDetailDTO.setCode(0);
        orderDetailDTO.setMessage("会员不可用");
        orderDetailDTO.setCombine(0);
        orderDetailDTO.setBrandName("brandName");
        orderDetailDTO.setDiningTableCode("code");
        orderDetailDTO.setAreaName("areaName");
        orderDetailDTO.setOrderState(0);
        final OrderTableItemDTO orderTableItemDTO = new OrderTableItemDTO();
        final OrderBatchItemDTO orderBatchItemDTO = new OrderBatchItemDTO();
        orderBatchItemDTO.setLoginStatus(false);
        orderBatchItemDTO.setState(0);
        orderTableItemDTO.setOrderBatchDTOs(Arrays.asList(orderBatchItemDTO));
        orderDetailDTO.setTableOrderDetailDTO(Arrays.asList(orderTableItemDTO));
        orderDetailDTO.setCouponSelect(false);
        orderDetailDTO.setEnableMemberPrice(false);
        orderDetailDTO.setSavingZero(new BigDecimal("0.00"));
        orderDetailDTO.setSavingZeroType(0);
        orderDetailDTO.setTotlePrice(new BigDecimal("0.00"));
        orderDetailDTO.setSaleTotlePrice(new BigDecimal("0.00"));
        orderDetailDTO.setPayAmount(new BigDecimal("0.00"));
        orderDetailDTO.setDeprecPayAmount(new BigDecimal("0.00"));
        orderDetailDTO.setLoginStatus(false);
        when(mockWxOrderManager.detail(
                new WxOrderDetailReqDTO("b1e1f442-6962-48f1-8d06-1e18ff96be3a", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(orderDetailDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_record/detail")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testDetail_WxOrderManagerReturnsError() throws Exception {
        // Setup
        // Configure WxOrderManager.detail(...).
        final OrderDetailDTO orderDetailDTO = OrderDetailDTO.buildByError(ErrorCoderEnum.ORDER_CALCULATE_ERROR);
        when(mockWxOrderManager.detail(
                new WxOrderDetailReqDTO("b1e1f442-6962-48f1-8d06-1e18ff96be3a", 0, 0, "memberInfoCardGuid",
                        "memberPhone", new BigDecimal("0.00"), new BigDecimal("0.00"), "volumeCode",
                        false))).thenReturn(orderDetailDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_record/detail")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetByOrderHolderNo() throws Exception {
        // Setup
        // Configure WxOrderRecordService.getByOrderHolderNo(...).
        final WxOrderRecordDO wxOrderRecordDO = new WxOrderRecordDO(0L, "guid", "orderGuid", "merchantGuid",
                "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid", "areaName",
                "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"), new BigDecimal("0.00"), false,
                "memberInfoCardGuid", "volumeCode", "itemName", "orderHolderNo", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), 0);
        when(mockWxOrderRecordService.getByOrderHolderNo("orderHolderNo")).thenReturn(wxOrderRecordDO);

        // Run the test and verify the results
        mockMvc.perform(get("/wx_store_order_record/get_by_order_holder_no")
                        .param("orderHolderNo", "orderHolderNo")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUserStoreList() throws Exception {
        // Setup
        // Configure WxOrderRecordService.userStoreList(...).
        final List<WxStoreUserOrderItemDTO> wxStoreUserOrderItemDTOS = Arrays.asList(
                WxStoreUserOrderItemDTO.builder().build());
        when(mockWxOrderRecordService.userStoreList()).thenReturn(wxStoreUserOrderItemDTOS);

        // Run the test and verify the results
        mockMvc.perform(get("/wx_store_order_record/store_list")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUserStoreList_WxOrderRecordServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockWxOrderRecordService.userStoreList()).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(get("/wx_store_order_record/store_list")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testGetUnFinishStoreUserOrderList() throws Exception {
        // Setup
        // Configure WxOrderRecordService.getUnFinishStoreUserOrderList(...).
        final List<WxStoreUserOrderItemDTO> wxStoreUserOrderItemDTOS = Arrays.asList(
                WxStoreUserOrderItemDTO.builder().build());
        when(mockWxOrderRecordService.getUnFinishStoreUserOrderList()).thenReturn(wxStoreUserOrderItemDTOS);

        // Run the test and verify the results
        mockMvc.perform(get("/wx_store_order_record/un_finish_order_list")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetUnFinishStoreUserOrderList_WxOrderRecordServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockWxOrderRecordService.getUnFinishStoreUserOrderList()).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(get("/wx_store_order_record/un_finish_order_list")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testGetMerchantOrderGuid() throws Exception {
        // Setup
        when(mockWxOrderRecordService.getMerchantOrderGuid("orderRecordGuid")).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_record/order_guid")
                        .param("orderGuid", "orderRecordGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUserStorePage() throws Exception {
        // Setup
        when(mockWxOrderRecordService.userStorePage(0, 0)).thenReturn(null);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_record/store_page")
                        .param("pageNo", "0")
                        .param("pageSize", "0")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testPayBackOrder() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_record/member_back")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockWxOrderRecordService).payBackOrder(
                new PayBackOrderRecordDTO("4c9f954c-1c5b-45ef-8a56-dcd31a24f22c", "orderGuid", "merchantGuid",
                        "userRecordGuid", "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "areaGuid",
                        "areaName", "tableGuid", "tableCode", 0, 0, "orderStateName", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), false, "memberInfoCardGuid", "volumeCode", "itemName",
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
    }

    @Test
    public void testInitialDineOrder() throws Exception {
        // Setup
        when(mockWxOrderRecordService.initialRecord(new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false, "",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0))).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(get("/wx_store_order_record/dine_initial")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testSendUnMemberMessage() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(get("/wx_store_order_record/sendUnMemberMessage")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm WxOrderRecordService.sendUnMemberMessage(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setReserveFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        dineinOrderDetailRespDTO.setSingleItemUsedMemeberPrice(0);
        dineinOrderDetailRespDTO.setTip("tip");
        final UnMemberMessageDTO unMemberMessageDTO = new UnMemberMessageDTO(dineinOrderDetailRespDTO,
                "orderRecordGuid", "openId");
        verify(mockWxOrderRecordService).sendUnMemberMessage(unMemberMessageDTO);
    }

    @Test
    public void testGetMerchantOrderPhone() throws Exception {
        // Setup
        // Configure WxOrderRecordService.getMerchantOrderPhone(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("d9f86f0b-42ba-44e8-a211-286f6279046d");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        when(mockWxOrderRecordService.getMerchantOrderPhone("orderGuid")).thenReturn(wxStoreMerchantOrderDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_record/get_merchant_order_phone")
                        .param("orderGuid", "orderGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListByOrderGuid() throws Exception {
        // Setup
        // Configure WxOrderRecordService.listByOrderGuid(...).
        final WxStoreMerchantOrderDTO wxStoreMerchantOrderDTO = new WxStoreMerchantOrderDTO();
        wxStoreMerchantOrderDTO.setGuid("d9f86f0b-42ba-44e8-a211-286f6279046d");
        wxStoreMerchantOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreMerchantOrderDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreMerchantOrderDTO.setOpenId("openId");
        final List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS = Arrays.asList(wxStoreMerchantOrderDTO);
        when(mockWxOrderRecordService.listByOrderGuid(SingleDataDTO.builder().build()))
                .thenReturn(wxStoreMerchantOrderDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_record/list_by_order_guid")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListByOrderGuid_WxOrderRecordServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockWxOrderRecordService.listByOrderGuid(SingleDataDTO.builder().build()))
                .thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_order_record/list_by_order_guid")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }
}
