package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.WxStoreMerchantDineInItemDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStoreMerchantDineInItemServiceImplTest {

    @Mock
    private RedisUtils mockRedisUtils;

    private WxStoreMerchantDineInItemServiceImpl wxStoreMerchantDineInItemServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxStoreMerchantDineInItemServiceImplUnderTest = new WxStoreMerchantDineInItemServiceImpl(mockRedisUtils);
    }

    @Test
    public void testUpdate() {
        // Setup
        final WxStoreMerchantDineInItemDTO wxStoreMerchantDineInItemDTO = new WxStoreMerchantDineInItemDTO();
        wxStoreMerchantDineInItemDTO.setGuid("27a9d159-fc36-4594-b860-bcf551540b0d");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("13f6359a-82bf-40c1-b884-7e3974baf279");
        dineInItemDTO.setOrderGuid("orderGuid");
        dineInItemDTO.setOriginalOrderItemGuid(0L);
        wxStoreMerchantDineInItemDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));

        // Run the test
        wxStoreMerchantDineInItemServiceImplUnderTest.update(wxStoreMerchantDineInItemDTO);

        // Verify the results
        // Confirm RedisUtils.setEx(...).
        final WxStoreMerchantDineInItemDTO value = new WxStoreMerchantDineInItemDTO();
        value.setGuid("27a9d159-fc36-4594-b860-bcf551540b0d");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineInItemDTO1.setGuid("13f6359a-82bf-40c1-b884-7e3974baf279");
        dineInItemDTO1.setOrderGuid("orderGuid");
        dineInItemDTO1.setOriginalOrderItemGuid(0L);
        value.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        verify(mockRedisUtils).setEx("key", value, 24L, TimeUnit.HOURS);
    }

    @Test
    public void testDel() {
        // Setup
        when(mockRedisUtils.delete("key")).thenReturn(false);

        // Run the test
        final Boolean result = wxStoreMerchantDineInItemServiceImplUnderTest.del(
                "ed479e3d-a3cd-4f48-b5a0-6c77213511c4");

        // Verify the results
        assertFalse(result);
    }

    @Test
    public void testDel_RedisUtilsReturnsTrue() {
        // Setup
        when(mockRedisUtils.delete("key")).thenReturn(true);

        // Run the test
        final Boolean result = wxStoreMerchantDineInItemServiceImplUnderTest.del(
                "ed479e3d-a3cd-4f48-b5a0-6c77213511c4");

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testGetWxStoreMerchantDineInItem() {
        // Setup
        final WxStoreMerchantDineInItemDTO expectedResult = new WxStoreMerchantDineInItemDTO();
        expectedResult.setGuid("27a9d159-fc36-4594-b860-bcf551540b0d");
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineInItemDTO.setGuid("13f6359a-82bf-40c1-b884-7e3974baf279");
        dineInItemDTO.setOrderGuid("orderGuid");
        dineInItemDTO.setOriginalOrderItemGuid(0L);
        expectedResult.setDineInItemDTOS(Arrays.asList(dineInItemDTO));

        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WxStoreMerchantDineInItemDTO result = wxStoreMerchantDineInItemServiceImplUnderTest.getWxStoreMerchantDineInItem(
                "42aaacb6-222b-40ea-8590-650da1ea6d0f");

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
