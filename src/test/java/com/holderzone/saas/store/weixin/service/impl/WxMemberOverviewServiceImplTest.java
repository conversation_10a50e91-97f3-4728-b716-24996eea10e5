package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfo;
import com.holderzone.saas.store.dto.weixin.WxMemberCenterCardRespDTO;
import com.holderzone.saas.store.dto.weixin.WxMemberOverviewModelDTO;
import com.holderzone.saas.store.dto.weixin.WxMemberOverviewRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.weixin.service.WxMemberCenterCardService;
import com.holderzone.saas.store.weixin.service.WxStoreSessionDetailsService;
import com.holderzone.saas.store.weixin.service.WxUserRecordService;
import com.holderzone.saas.store.weixin.service.rpc.member.MemberClientService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxMemberOverviewServiceImplTest {

    @Mock
    private WxMemberCenterCardService mockWxMemberCenterCardService;
    @Mock
    private WxStoreSessionDetailsService mockWxStoreSessionDetailsService;
    @Mock
    private QueueMemberModelItemServiceImpl mockQueueMemberModelItemService;
    @Mock
    private VolumeMemberModelItemServiceImpl mockVolumeMemberModelItemService;
    @Mock
    private DiningMemberModelItemServiceImpl mockDiningMemberModelItemService;
    @Mock
    private MemberClientService mockMemberClientService;
    @Mock
    private WxUserRecordService mockWxUserRecordService;

    private WxMemberOverviewServiceImpl wxMemberOverviewServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxMemberOverviewServiceImplUnderTest = new WxMemberOverviewServiceImpl(mockWxMemberCenterCardService,
                mockWxStoreSessionDetailsService, mockQueueMemberModelItemService, mockVolumeMemberModelItemService,
                mockDiningMemberModelItemService, mockMemberClientService, mockWxUserRecordService);
    }

    @Test
    public void testAllModel() {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .enterpriseGuid("enterpriseGuid")
                .isLogin(false)
                .build();
        final WxMemberOverviewRespDTO expectedResult = WxMemberOverviewRespDTO.builder()
                .bindingPhoneNum(false)
                .phoneNum("")
                .wxMemberOverviewModelDTOS(Arrays.asList(WxMemberOverviewModelDTO.builder()
                        .modelId(0)
                        .modelName("error")
                        .modelCount(0)
                        .build()))
                .wxMemberCenterCardRespDTO(WxMemberCenterCardRespDTO.builder().build())
                .memberInfoGuid("")
                .build();

        // Configure WxMemberCenterCardService.cardList(...).
        final WxMemberCenterCardRespDTO wxMemberCenterCardRespDTO = WxMemberCenterCardRespDTO.builder().build();
        when(mockWxMemberCenterCardService.cardList(WxStoreConsumerDTO.builder()
                .openId("openId")
                .enterpriseGuid("enterpriseGuid")
                .isLogin(false)
                .build())).thenReturn(wxMemberCenterCardRespDTO);

        // Configure WxStoreSessionDetailsService.getMemberInfo(...).
        final ResponseMemberInfo responseMemberInfo = new ResponseMemberInfo();
        responseMemberInfo.setQrcode("qrcode");
        responseMemberInfo.setMemberInfoGuid("");
        responseMemberInfo.setOperSubjectGuid("operSubjectGuid");
        responseMemberInfo.setEnterpriseGuid("enterpriseGuid");
        responseMemberInfo.setPhoneNum("");
        when(mockWxStoreSessionDetailsService.getMemberInfo("enterpriseGuid", "openId")).thenReturn(responseMemberInfo);

        // Run the test
        final WxMemberOverviewRespDTO result = wxMemberOverviewServiceImplUnderTest.allModel(wxStoreConsumerDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxUserRecordService).updateUserInfo("openId");
    }
}
