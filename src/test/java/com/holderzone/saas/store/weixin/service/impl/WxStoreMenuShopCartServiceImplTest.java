package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.weixin.WxStoreCartItemDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerShoppingCartDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreShoppingCartDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreConsumerCartItemReqDTO;
import com.holderzone.saas.store.weixin.mapstruct.WxStoreShoppingCartMapStruct;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStoreMenuShopCartServiceImplTest {

    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private WxStoreShoppingCartMapStruct mockWxStoreShoppingCartMapStruct;

    private WxStoreMenuShopCartServiceImpl wxStoreMenuShopCartServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxStoreMenuShopCartServiceImplUnderTest = new WxStoreMenuShopCartServiceImpl(mockRedisUtils,
                mockWxStoreShoppingCartMapStruct);
    }

    @Test
    public void testCreateShoppingCart() {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .storeGuid("storeGuid")
                .build();
        final WxStoreShoppingCartDTO expectedResult = new WxStoreShoppingCartDTO();
        expectedResult.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO = new WxStoreCartItemDTO();
        wxStoreCartItemDTO.setItemGuid("itemGuid");
        expectedResult.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO));
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockRedisUtils.generatdDTOGuid(WxStoreShoppingCartDTO.class))
                .thenReturn("4e8c164a-f348-4c62-b326-c41371d18eba");

        // Configure WxStoreShoppingCartMapStruct.wxStoreConsumerDTO2WxStoreShoppingCartDTO(...).
        final WxStoreShoppingCartDTO wxStoreShoppingCartDTO = new WxStoreShoppingCartDTO();
        wxStoreShoppingCartDTO.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO1 = new WxStoreCartItemDTO();
        wxStoreCartItemDTO1.setItemGuid("itemGuid");
        wxStoreShoppingCartDTO.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO1));
        wxStoreShoppingCartDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreShoppingCartDTO.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockWxStoreShoppingCartMapStruct.wxStoreConsumerDTO2WxStoreShoppingCartDTO(WxStoreConsumerDTO.builder()
                .openId("openId")
                .storeGuid("storeGuid")
                .build())).thenReturn(wxStoreShoppingCartDTO);

        when(mockRedisUtils.keyGenerate("shoppingCart")).thenReturn("key");

        // Run the test
        final WxStoreShoppingCartDTO result = wxStoreMenuShopCartServiceImplUnderTest.createShoppingCart(
                wxStoreConsumerDTO);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm RedisUtils.set(...).
        final WxStoreShoppingCartDTO value = new WxStoreShoppingCartDTO();
        value.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO2 = new WxStoreCartItemDTO();
        wxStoreCartItemDTO2.setItemGuid("itemGuid");
        value.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO2));
        value.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        value.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockRedisUtils).set("key", value);
    }

    @Test
    public void testSave() {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .storeGuid("storeGuid")
                .build();
        final WxStoreShoppingCartDTO wxStoreShoppingCartDTO = new WxStoreShoppingCartDTO();
        wxStoreShoppingCartDTO.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO = new WxStoreCartItemDTO();
        wxStoreCartItemDTO.setItemGuid("itemGuid");
        wxStoreShoppingCartDTO.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO));
        wxStoreShoppingCartDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreShoppingCartDTO.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockRedisUtils.keyGenerate("shoppingCart")).thenReturn("key");

        // Run the test
        wxStoreMenuShopCartServiceImplUnderTest.save(wxStoreConsumerDTO, wxStoreShoppingCartDTO);

        // Verify the results
        // Confirm RedisUtils.set(...).
        final WxStoreShoppingCartDTO value = new WxStoreShoppingCartDTO();
        value.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO1 = new WxStoreCartItemDTO();
        wxStoreCartItemDTO1.setItemGuid("itemGuid");
        value.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO1));
        value.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        value.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockRedisUtils).set("key", value);
    }

    @Test
    public void testGetWxStoreShoppingCart() {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .storeGuid("storeGuid")
                .build();
        final WxStoreShoppingCartDTO expectedResult = new WxStoreShoppingCartDTO();
        expectedResult.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO = new WxStoreCartItemDTO();
        wxStoreCartItemDTO.setItemGuid("itemGuid");
        expectedResult.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO));
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockRedisUtils.keyGenerate("shoppingCart")).thenReturn("key");
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WxStoreShoppingCartDTO result = wxStoreMenuShopCartServiceImplUnderTest.getWxStoreShoppingCart(
                wxStoreConsumerDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetWxStoreShoppingCart_RedisUtilsGetReturnsNull() {
        // Setup
        final WxStoreConsumerDTO wxStoreConsumerDTO = WxStoreConsumerDTO.builder()
                .openId("openId")
                .storeGuid("storeGuid")
                .build();
        final WxStoreShoppingCartDTO expectedResult = new WxStoreShoppingCartDTO();
        expectedResult.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO = new WxStoreCartItemDTO();
        wxStoreCartItemDTO.setItemGuid("itemGuid");
        expectedResult.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO));
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockRedisUtils.keyGenerate("shoppingCart")).thenReturn("key");
        when(mockRedisUtils.get("key")).thenReturn(null);
        when(mockRedisUtils.generatdDTOGuid(WxStoreShoppingCartDTO.class))
                .thenReturn("4e8c164a-f348-4c62-b326-c41371d18eba");

        // Configure WxStoreShoppingCartMapStruct.wxStoreConsumerDTO2WxStoreShoppingCartDTO(...).
        final WxStoreShoppingCartDTO wxStoreShoppingCartDTO = new WxStoreShoppingCartDTO();
        wxStoreShoppingCartDTO.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO1 = new WxStoreCartItemDTO();
        wxStoreCartItemDTO1.setItemGuid("itemGuid");
        wxStoreShoppingCartDTO.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO1));
        wxStoreShoppingCartDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreShoppingCartDTO.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockWxStoreShoppingCartMapStruct.wxStoreConsumerDTO2WxStoreShoppingCartDTO(WxStoreConsumerDTO.builder()
                .openId("openId")
                .storeGuid("storeGuid")
                .build())).thenReturn(wxStoreShoppingCartDTO);

        // Run the test
        final WxStoreShoppingCartDTO result = wxStoreMenuShopCartServiceImplUnderTest.getWxStoreShoppingCart(
                wxStoreConsumerDTO);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm RedisUtils.set(...).
        final WxStoreShoppingCartDTO value = new WxStoreShoppingCartDTO();
        value.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO2 = new WxStoreCartItemDTO();
        wxStoreCartItemDTO2.setItemGuid("itemGuid");
        value.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO2));
        value.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        value.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockRedisUtils).set("key", value);
    }

    @Test
    public void testUpdateWxStoreShoppingCart() {
        // Setup
        final WxStoreConsumerShoppingCartDTO wxStoreConsumerShoppingCartDTO = new WxStoreConsumerShoppingCartDTO();
        final WxStoreShoppingCartDTO wxStoreShoppingCartDTO = new WxStoreShoppingCartDTO();
        wxStoreShoppingCartDTO.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO = new WxStoreCartItemDTO();
        wxStoreCartItemDTO.setItemGuid("itemGuid");
        wxStoreShoppingCartDTO.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO));
        wxStoreShoppingCartDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreShoppingCartDTO.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreConsumerShoppingCartDTO.setWxStoreShoppingCartDTO(wxStoreShoppingCartDTO);
        wxStoreConsumerShoppingCartDTO.setWxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                .openId("openId")
                .storeGuid("storeGuid")
                .build());

        when(mockRedisUtils.keyGenerate("shoppingCart")).thenReturn("key");

        // Run the test
        final WxStoreShoppingCartDTO result = wxStoreMenuShopCartServiceImplUnderTest.updateWxStoreShoppingCart(
                wxStoreConsumerShoppingCartDTO);

        // Verify the results
        assertNull(result);

        // Confirm RedisUtils.set(...).
        final WxStoreShoppingCartDTO value = new WxStoreShoppingCartDTO();
        value.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO1 = new WxStoreCartItemDTO();
        wxStoreCartItemDTO1.setItemGuid("itemGuid");
        value.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO1));
        value.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        value.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockRedisUtils).set("key", value);
    }

    @Test
    public void testCreateCartItem() {
        // Setup
        final WxStoreConsumerCartItemReqDTO wxStoreConsumerCartItemReqDTO = new WxStoreConsumerCartItemReqDTO();
        wxStoreConsumerCartItemReqDTO.setWxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                .openId("openId")
                .storeGuid("storeGuid")
                .build());
        final WxStoreCartItemDTO wxStoreCartItemDTO = new WxStoreCartItemDTO();
        wxStoreCartItemDTO.setItemGuid("itemGuid");
        wxStoreCartItemDTO.setItemName("itemName");
        wxStoreCartItemDTO.setCode("code");
        wxStoreConsumerCartItemReqDTO.setWxStoreCartItemDTO(wxStoreCartItemDTO);

        final WxStoreShoppingCartDTO expectedResult = new WxStoreShoppingCartDTO();
        expectedResult.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO1 = new WxStoreCartItemDTO();
        wxStoreCartItemDTO1.setItemGuid("itemGuid");
        expectedResult.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO1));
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockRedisUtils.keyGenerate("shoppingCart")).thenReturn("key");
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WxStoreShoppingCartDTO result = wxStoreMenuShopCartServiceImplUnderTest.createCartItem(
                wxStoreConsumerCartItemReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm RedisUtils.set(...).
        final WxStoreShoppingCartDTO value = new WxStoreShoppingCartDTO();
        value.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO2 = new WxStoreCartItemDTO();
        wxStoreCartItemDTO2.setItemGuid("itemGuid");
        value.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO2));
        value.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        value.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockRedisUtils).set("key", value);
    }

    @Test
    public void testCreateCartItem_RedisUtilsGetReturnsNull() {
        // Setup
        final WxStoreConsumerCartItemReqDTO wxStoreConsumerCartItemReqDTO = new WxStoreConsumerCartItemReqDTO();
        wxStoreConsumerCartItemReqDTO.setWxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                .openId("openId")
                .storeGuid("storeGuid")
                .build());
        final WxStoreCartItemDTO wxStoreCartItemDTO = new WxStoreCartItemDTO();
        wxStoreCartItemDTO.setItemGuid("itemGuid");
        wxStoreCartItemDTO.setItemName("itemName");
        wxStoreCartItemDTO.setCode("code");
        wxStoreConsumerCartItemReqDTO.setWxStoreCartItemDTO(wxStoreCartItemDTO);

        final WxStoreShoppingCartDTO expectedResult = new WxStoreShoppingCartDTO();
        expectedResult.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO1 = new WxStoreCartItemDTO();
        wxStoreCartItemDTO1.setItemGuid("itemGuid");
        expectedResult.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO1));
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockRedisUtils.keyGenerate("shoppingCart")).thenReturn("key");
        when(mockRedisUtils.get("key")).thenReturn(null);
        when(mockRedisUtils.generatdDTOGuid(WxStoreShoppingCartDTO.class))
                .thenReturn("4e8c164a-f348-4c62-b326-c41371d18eba");

        // Configure WxStoreShoppingCartMapStruct.wxStoreConsumerDTO2WxStoreShoppingCartDTO(...).
        final WxStoreShoppingCartDTO wxStoreShoppingCartDTO = new WxStoreShoppingCartDTO();
        wxStoreShoppingCartDTO.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO2 = new WxStoreCartItemDTO();
        wxStoreCartItemDTO2.setItemGuid("itemGuid");
        wxStoreShoppingCartDTO.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO2));
        wxStoreShoppingCartDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreShoppingCartDTO.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockWxStoreShoppingCartMapStruct.wxStoreConsumerDTO2WxStoreShoppingCartDTO(WxStoreConsumerDTO.builder()
                .openId("openId")
                .storeGuid("storeGuid")
                .build())).thenReturn(wxStoreShoppingCartDTO);

        // Run the test
        final WxStoreShoppingCartDTO result = wxStoreMenuShopCartServiceImplUnderTest.createCartItem(
                wxStoreConsumerCartItemReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm RedisUtils.set(...).
        final WxStoreShoppingCartDTO value = new WxStoreShoppingCartDTO();
        value.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO3 = new WxStoreCartItemDTO();
        wxStoreCartItemDTO3.setItemGuid("itemGuid");
        value.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO3));
        value.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        value.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockRedisUtils).set("key", value);
    }

    @Test
    public void testUpdateCartItem() {
        // Setup
        final WxStoreConsumerCartItemReqDTO wxStoreConsumerCartItemReqDTO = new WxStoreConsumerCartItemReqDTO();
        wxStoreConsumerCartItemReqDTO.setWxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                .openId("openId")
                .storeGuid("storeGuid")
                .build());
        final WxStoreCartItemDTO wxStoreCartItemDTO = new WxStoreCartItemDTO();
        wxStoreCartItemDTO.setItemGuid("itemGuid");
        wxStoreCartItemDTO.setItemName("itemName");
        wxStoreCartItemDTO.setCode("code");
        wxStoreConsumerCartItemReqDTO.setWxStoreCartItemDTO(wxStoreCartItemDTO);

        final WxStoreShoppingCartDTO expectedResult = new WxStoreShoppingCartDTO();
        expectedResult.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO1 = new WxStoreCartItemDTO();
        wxStoreCartItemDTO1.setItemGuid("itemGuid");
        expectedResult.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO1));
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockRedisUtils.keyGenerate("shoppingCart")).thenReturn("key");
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WxStoreShoppingCartDTO result = wxStoreMenuShopCartServiceImplUnderTest.updateCartItem(
                wxStoreConsumerCartItemReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm RedisUtils.set(...).
        final WxStoreShoppingCartDTO value = new WxStoreShoppingCartDTO();
        value.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO2 = new WxStoreCartItemDTO();
        wxStoreCartItemDTO2.setItemGuid("itemGuid");
        value.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO2));
        value.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        value.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockRedisUtils).set("key", value);
    }

    @Test
    public void testUpdateCartItem_RedisUtilsGetReturnsNull() {
        // Setup
        final WxStoreConsumerCartItemReqDTO wxStoreConsumerCartItemReqDTO = new WxStoreConsumerCartItemReqDTO();
        wxStoreConsumerCartItemReqDTO.setWxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                .openId("openId")
                .storeGuid("storeGuid")
                .build());
        final WxStoreCartItemDTO wxStoreCartItemDTO = new WxStoreCartItemDTO();
        wxStoreCartItemDTO.setItemGuid("itemGuid");
        wxStoreCartItemDTO.setItemName("itemName");
        wxStoreCartItemDTO.setCode("code");
        wxStoreConsumerCartItemReqDTO.setWxStoreCartItemDTO(wxStoreCartItemDTO);

        final WxStoreShoppingCartDTO expectedResult = new WxStoreShoppingCartDTO();
        expectedResult.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO1 = new WxStoreCartItemDTO();
        wxStoreCartItemDTO1.setItemGuid("itemGuid");
        expectedResult.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO1));
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockRedisUtils.keyGenerate("shoppingCart")).thenReturn("key");
        when(mockRedisUtils.get("key")).thenReturn(null);
        when(mockRedisUtils.generatdDTOGuid(WxStoreShoppingCartDTO.class))
                .thenReturn("4e8c164a-f348-4c62-b326-c41371d18eba");

        // Configure WxStoreShoppingCartMapStruct.wxStoreConsumerDTO2WxStoreShoppingCartDTO(...).
        final WxStoreShoppingCartDTO wxStoreShoppingCartDTO = new WxStoreShoppingCartDTO();
        wxStoreShoppingCartDTO.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO2 = new WxStoreCartItemDTO();
        wxStoreCartItemDTO2.setItemGuid("itemGuid");
        wxStoreShoppingCartDTO.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO2));
        wxStoreShoppingCartDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreShoppingCartDTO.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockWxStoreShoppingCartMapStruct.wxStoreConsumerDTO2WxStoreShoppingCartDTO(WxStoreConsumerDTO.builder()
                .openId("openId")
                .storeGuid("storeGuid")
                .build())).thenReturn(wxStoreShoppingCartDTO);

        // Run the test
        final WxStoreShoppingCartDTO result = wxStoreMenuShopCartServiceImplUnderTest.updateCartItem(
                wxStoreConsumerCartItemReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm RedisUtils.set(...).
        final WxStoreShoppingCartDTO value = new WxStoreShoppingCartDTO();
        value.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO3 = new WxStoreCartItemDTO();
        wxStoreCartItemDTO3.setItemGuid("itemGuid");
        value.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO3));
        value.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        value.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockRedisUtils).set("key", value);
    }

    @Test
    public void testDelCartItem() {
        // Setup
        final WxStoreConsumerCartItemReqDTO wxStoreConsumerCartItemReqDTO = new WxStoreConsumerCartItemReqDTO();
        wxStoreConsumerCartItemReqDTO.setWxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                .openId("openId")
                .storeGuid("storeGuid")
                .build());
        final WxStoreCartItemDTO wxStoreCartItemDTO = new WxStoreCartItemDTO();
        wxStoreCartItemDTO.setItemGuid("itemGuid");
        wxStoreCartItemDTO.setItemName("itemName");
        wxStoreCartItemDTO.setCode("code");
        wxStoreConsumerCartItemReqDTO.setWxStoreCartItemDTO(wxStoreCartItemDTO);

        final WxStoreShoppingCartDTO expectedResult = new WxStoreShoppingCartDTO();
        expectedResult.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO1 = new WxStoreCartItemDTO();
        wxStoreCartItemDTO1.setItemGuid("itemGuid");
        expectedResult.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO1));
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockRedisUtils.keyGenerate("shoppingCart")).thenReturn("key");
        when(mockRedisUtils.get("key")).thenReturn("result");

        // Run the test
        final WxStoreShoppingCartDTO result = wxStoreMenuShopCartServiceImplUnderTest.delCartItem(
                wxStoreConsumerCartItemReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm RedisUtils.set(...).
        final WxStoreShoppingCartDTO value = new WxStoreShoppingCartDTO();
        value.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO2 = new WxStoreCartItemDTO();
        wxStoreCartItemDTO2.setItemGuid("itemGuid");
        value.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO2));
        value.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        value.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockRedisUtils).set("key", value);
    }

    @Test
    public void testDelCartItem_RedisUtilsGetReturnsNull() {
        // Setup
        final WxStoreConsumerCartItemReqDTO wxStoreConsumerCartItemReqDTO = new WxStoreConsumerCartItemReqDTO();
        wxStoreConsumerCartItemReqDTO.setWxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                .openId("openId")
                .storeGuid("storeGuid")
                .build());
        final WxStoreCartItemDTO wxStoreCartItemDTO = new WxStoreCartItemDTO();
        wxStoreCartItemDTO.setItemGuid("itemGuid");
        wxStoreCartItemDTO.setItemName("itemName");
        wxStoreCartItemDTO.setCode("code");
        wxStoreConsumerCartItemReqDTO.setWxStoreCartItemDTO(wxStoreCartItemDTO);

        final WxStoreShoppingCartDTO expectedResult = new WxStoreShoppingCartDTO();
        expectedResult.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO1 = new WxStoreCartItemDTO();
        wxStoreCartItemDTO1.setItemGuid("itemGuid");
        expectedResult.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO1));
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockRedisUtils.keyGenerate("shoppingCart")).thenReturn("key");
        when(mockRedisUtils.get("key")).thenReturn(null);
        when(mockRedisUtils.generatdDTOGuid(WxStoreShoppingCartDTO.class))
                .thenReturn("4e8c164a-f348-4c62-b326-c41371d18eba");

        // Configure WxStoreShoppingCartMapStruct.wxStoreConsumerDTO2WxStoreShoppingCartDTO(...).
        final WxStoreShoppingCartDTO wxStoreShoppingCartDTO = new WxStoreShoppingCartDTO();
        wxStoreShoppingCartDTO.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO2 = new WxStoreCartItemDTO();
        wxStoreCartItemDTO2.setItemGuid("itemGuid");
        wxStoreShoppingCartDTO.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO2));
        wxStoreShoppingCartDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreShoppingCartDTO.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockWxStoreShoppingCartMapStruct.wxStoreConsumerDTO2WxStoreShoppingCartDTO(WxStoreConsumerDTO.builder()
                .openId("openId")
                .storeGuid("storeGuid")
                .build())).thenReturn(wxStoreShoppingCartDTO);

        // Run the test
        final WxStoreShoppingCartDTO result = wxStoreMenuShopCartServiceImplUnderTest.delCartItem(
                wxStoreConsumerCartItemReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);

        // Confirm RedisUtils.set(...).
        final WxStoreShoppingCartDTO value = new WxStoreShoppingCartDTO();
        value.setGuid("4e8c164a-f348-4c62-b326-c41371d18eba");
        final WxStoreCartItemDTO wxStoreCartItemDTO3 = new WxStoreCartItemDTO();
        wxStoreCartItemDTO3.setItemGuid("itemGuid");
        value.setWxStoreCartItemDTOS(Arrays.asList(wxStoreCartItemDTO3));
        value.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        value.setGmtUpdate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockRedisUtils).set("key", value);
    }
}
