package com.holderzone.saas.store.weixin.controller;

import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.entry.dto.WxUserInfoDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.weixin.WxUserRecordDTO;
import com.holderzone.saas.store.weixin.entity.domain.WxUserRecordDO;
import com.holderzone.saas.store.weixin.mapstruct.WxUserRecordMapstruct;
import com.holderzone.saas.store.weixin.service.WxUserRecordService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxUserRecordController.class)
public class WxUserRecordControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxUserRecordService mockWxUserRecordService;
    @MockBean
    private WxUserRecordMapstruct mockWxUserRecordMapstruct;
    @MockBean
    private RedisUtils mockRedisUtils;

    @Test
    public void testGetUserByOpenId() throws Exception {
        // Setup
        // Configure WxUserRecordService.getOneByOpenId(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "6f7ed7e8-57bb-4ee6-873e-5d2ad93279b0", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(wxUserRecordDO);

        when(mockWxUserRecordMapstruct.getWxUserRecordDTO(
                new WxUserRecordDO(0L, "6f7ed7e8-57bb-4ee6-873e-5d2ad93279b0", "openId", "nickName", "headImgUrl", 0,
                        "country", "province", "city", "phone", LocalDateTime.of(2020, 1, 1, 0, 0, 0),
                        LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid", 0)))
                .thenReturn(WxUserRecordDTO.builder().build());

        // Run the test and verify the results
        mockMvc.perform(get("/wx_user_record/getUserByOpenId/{openId}", "openId")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetUserByOpenId_WxUserRecordServiceReturnsNull() throws Exception {
        // Setup
        when(mockWxUserRecordService.getOneByOpenId("openId")).thenReturn(null);

        // Run the test and verify the results
        mockMvc.perform(get("/wx_user_record/getUserByOpenId/{openId}", "openId")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("", true));
    }

    @Test
    public void testBindPhoneNum() throws Exception {
        // Setup
        when(mockWxUserRecordService.bindPhoneNum("phoneNum")).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_user_record/bind_phone")
                        .param("phoneNum", "phoneNum")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testBindPhoneNum_WxUserRecordServiceReturnsTrue() throws Exception {
        // Setup
        when(mockWxUserRecordService.bindPhoneNum("phoneNum")).thenReturn(true);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_user_record/bind_phone")
                        .param("phoneNum", "phoneNum")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testObtainByPhone() throws Exception {
        // Setup
        // Configure WxUserRecordService.obtainByPhone(...).
        final WxUserRecordDO wxUserRecordDO = new WxUserRecordDO(0L, "6f7ed7e8-57bb-4ee6-873e-5d2ad93279b0", "openId",
                "nickName", "headImgUrl", 0, "country", "province", "city", "phone",
                LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0), false, "operSubjectGuid",
                0);
        when(mockWxUserRecordService.obtainByPhone("phone")).thenReturn(wxUserRecordDO);

        // Run the test and verify the results
        mockMvc.perform(get("/wx_user_record/getByPhone")
                        .param("phone", "phone")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testLoginWxUserRecordAndFlushCard() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(get("/wx_user_record/loginWxUserRecordAndFlushCard")
                        .param("enterpriseGuid", "enterpriseGuid")
                        .param("phoneNum", "phoneNum")
                        .param("openId", "openId")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockWxUserRecordService).asyncMemberInfo(new WxMemberSessionDTO(
                new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province", "country",
                        "qrScene", false, "",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName", "brandGuid",
                "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid", "diningTableCode", "areaGuid",
                "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken", "message", 0), "openId");
        verify(mockWxUserRecordService).updateUserLoginByOpenId("enterpriseGuid", "openId", true,
                new WxMemberSessionDTO(
                        new WxUserInfoDTO("openId", "nickname", "headImgUrl", "unionId", 0, "city", "province",
                                "country", "qrScene", false,"",""), "operSubjectGuid", "enterpriseGuid", "enterpriseName",
                        "brandGuid", "brandName", "logUrl", "storeGuid", "storeName", "diningTableGuid",
                        "diningTableCode", "areaGuid", "areaName", "memberInfoGuid", "phoneNum", false, 0, "wxtoken",
                        "message", 0));
    }
}
