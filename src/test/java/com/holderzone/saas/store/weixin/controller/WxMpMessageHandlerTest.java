package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.weixin.req.WxAuthorizeReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxCommonReqDTO;
import com.holderzone.saas.store.weixin.service.WxStoreMpService;
import me.chanjar.weixin.common.error.WxErrorException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxMpMessageHandler.class)
public class WxMpMessageHandlerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStoreMpService mockWxStoreMpService;

    @Test
    public void testVerify() throws Exception {
        // Setup
        // Configure WxStoreMpService.verifyMessage(...).
        final WxCommonReqDTO wxCommonReqDTO = new WxCommonReqDTO();
        wxCommonReqDTO.setSignature("signature");
        wxCommonReqDTO.setEchostr("echostr");
        wxCommonReqDTO.setTimestamp("timestamp");
        wxCommonReqDTO.setNonce("nonce");
        wxCommonReqDTO.setEncrypt_type("encrypt_type");
        when(mockWxStoreMpService.verifyMessage(wxCommonReqDTO)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/wx_mp/verify")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetUserInfo() throws Exception {
        // Setup
        // Configure WxStoreMpService.getUserInfo(...).
        final WxAuthorizeReqDTO wxAuthorizeReqDTO = new WxAuthorizeReqDTO();
        wxAuthorizeReqDTO.setCode("code");
        wxAuthorizeReqDTO.setState("state");
        wxAuthorizeReqDTO.setEventKey("eventKey");
        wxAuthorizeReqDTO.setAppId("appId");
        wxAuthorizeReqDTO.setWeixinToken("weixinToken");
        when(mockWxStoreMpService.getUserInfo(wxAuthorizeReqDTO)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/wx_mp/get_user_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetUserInfo_WxStoreMpServiceThrowsWxErrorException() throws Exception {
        // Setup
        // Configure WxStoreMpService.getUserInfo(...).
        final WxAuthorizeReqDTO wxAuthorizeReqDTO = new WxAuthorizeReqDTO();
        wxAuthorizeReqDTO.setCode("code");
        wxAuthorizeReqDTO.setState("state");
        wxAuthorizeReqDTO.setEventKey("eventKey");
        wxAuthorizeReqDTO.setAppId("appId");
        wxAuthorizeReqDTO.setWeixinToken("weixinToken");
        when(mockWxStoreMpService.getUserInfo(wxAuthorizeReqDTO)).thenThrow(WxErrorException.class);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_mp/get_user_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is5xxServerError())
                .andExpect(content().json("{}", true));
    }
}
