package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.weixin.WxStickShopCartDTO;
import com.holderzone.saas.store.dto.weixin.WxTableStickDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickModelReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickShopCartRemoveDTO;
import com.holderzone.saas.store.weixin.mapstruct.WxStickShopCartMapstruct;
import com.holderzone.saas.store.weixin.service.rpc.WxStickModelClientService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStickShopCartServiceImplTest {

    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private WxStickShopCartMapstruct mockWxStickShopCartMapstruct;
    @Mock
    private WxStickModelClientService mockWxStickModelClientService;

    private WxStickShopCartServiceImpl wxStickShopCartServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxStickShopCartServiceImplUnderTest = new WxStickShopCartServiceImpl();
        wxStickShopCartServiceImplUnderTest.redisUtils = mockRedisUtils;
        wxStickShopCartServiceImplUnderTest.wxStickShopCartMapstruct = mockWxStickShopCartMapstruct;
        wxStickShopCartServiceImplUnderTest.wxStickModelClientService = mockWxStickModelClientService;
    }

    @Test
    public void testListShopCart() {
        // Setup
        final List<WxStickShopCartDTO> expectedResult = Arrays.asList(
                new WxStickShopCartDTO("modelGuid", "modelName", "previewImg", new BigDecimal("0.00")));

        // Configure WxStickModelClientService.getTableStickList(...).
        final List<WxTableStickDTO> wxTableStickDTOS = Arrays.asList(
                new WxTableStickDTO("e1438f6f-4627-4a66-a82b-59d2f013a03e", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "modelName", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow",
                        "tableNumberText", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid",
                        "isEnable", "isDelete", 0L, 0L, "categoryName", "previewImg", 0));
        when(mockWxStickModelClientService.getTableStickList(
                new WxStickModelReqDTO(Arrays.asList("value"), 0, "category"))).thenReturn(wxTableStickDTOS);

        // Run the test
        final List<WxStickShopCartDTO> result = wxStickShopCartServiceImplUnderTest.listShopCart();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testListShopCart_WxStickModelClientServiceReturnsNoItems() {
        // Setup
        when(mockWxStickModelClientService.getTableStickList(
                new WxStickModelReqDTO(Arrays.asList("value"), 0, "category"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<WxStickShopCartDTO> result = wxStickShopCartServiceImplUnderTest.listShopCart();

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testAddModels() {
        // Setup
        final List<WxStickShopCartDTO> wxStickShopCartDTOList = Arrays.asList(
                new WxStickShopCartDTO("modelGuid", "modelName", "previewImg", new BigDecimal("0.00")));
        when(mockRedisUtils.generateGuid("redisKey")).thenReturn("3e4aa077-68bb-4f00-8105-f8f152828281");

        // Run the test
        wxStickShopCartServiceImplUnderTest.addModels(wxStickShopCartDTOList);

        // Verify the results
    }

    @Test
    public void testRemoveModels() {
        // Setup
        final WxStickShopCartRemoveDTO wxStickShopCartRemoveDTO = new WxStickShopCartRemoveDTO(Arrays.asList("value"),
                0);

        // Run the test
        wxStickShopCartServiceImplUnderTest.removeModels(wxStickShopCartRemoveDTO);

        // Verify the results
    }
}
