package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.weixin.service.WxStoreSocketSessionService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxStoreSocketSessionController.class)
public class WxStoreSocketSessionControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStoreSocketSessionService mockWxStoreSocketSessionService;
    @MockBean
    private DynamicHelper mockDynamicHelper;

    @Test
    public void testCreateSession() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_socket_session/create")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockWxStoreSocketSessionService).createSession(WxStoreAdvanceConsumerReqDTO.builder().build());
    }

    @Test
    public void testDelSession() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_socket_session/del_session")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockWxStoreSocketSessionService).delSession(WxStoreAdvanceConsumerReqDTO.builder().build());
    }

    @Test
    public void testGetWebSocketUser1() throws Exception {
        // Setup
        // Configure WxStoreSocketSessionService.getSession(...).
        final List<WxStoreAdvanceConsumerReqDTO> wxStoreAdvanceConsumerReqDTOS = Arrays.asList(
                WxStoreAdvanceConsumerReqDTO.builder().build());
        when(mockWxStoreSocketSessionService.getSession(WxStoreAdvanceConsumerReqDTO.builder().build()))
                .thenReturn(wxStoreAdvanceConsumerReqDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_socket_session/find_session")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetWebSocketUser1_WxStoreSocketSessionServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockWxStoreSocketSessionService.getSession(WxStoreAdvanceConsumerReqDTO.builder().build()))
                .thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_socket_session/find_session")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testGetPersonSocketUser1() throws Exception {
        // Setup
        // Configure WxStoreSocketSessionService.getSingleSession(...).
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder().build();
        when(mockWxStoreSocketSessionService.getSingleSession(
                WxStoreAdvanceConsumerReqDTO.builder().build())).thenReturn(wxStoreAdvanceConsumerReqDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_socket_session/single_session")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetWebSocketUser2() throws Exception {
        // Setup
        // Configure WxStoreSocketSessionService.getSession(...).
        final List<WxStoreAdvanceConsumerReqDTO> wxStoreAdvanceConsumerReqDTOS = Arrays.asList(
                WxStoreAdvanceConsumerReqDTO.builder().build());
        when(mockWxStoreSocketSessionService.getSession("tableGuid")).thenReturn(wxStoreAdvanceConsumerReqDTOS);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_socket_session/table_user")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testGetWebSocketUser2_WxStoreSocketSessionServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockWxStoreSocketSessionService.getSession("tableGuid")).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_socket_session/table_user")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test
    public void testGetPersonSocketUser2() throws Exception {
        // Setup
        // Configure WxStoreSocketSessionService.getSingleSession(...).
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder().build();
        when(mockWxStoreSocketSessionService.getSingleSession("openId")).thenReturn(wxStoreAdvanceConsumerReqDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx_store_socket_session/single_user")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }
}
