package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.saas.store.dto.business.manage.ShortMsgPollingRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStickOrderCallBackDTO;
import com.holderzone.saas.store.dto.weixin.WxTableStickDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickModelOrderDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickModelReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStickShopCartRemoveDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickJHPayRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStickOrderRespDTO;
import com.holderzone.saas.store.weixin.service.WxStickShopCartService;
import com.holderzone.saas.store.weixin.service.WxStoreTableStickService;
import com.holderzone.saas.store.weixin.service.rpc.WxStickModelClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStickOrderClientService;
import com.holderzone.saas.store.weixin.utils.DynamicHelper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStickModelOrderServiceImplTest {

    @Mock
    private WxStickOrderClientService mockWxStickOrderClientService;
    @Mock
    private WxStickModelClientService mockWxStickModelClientService;
    @Mock
    private WxStickShopCartService mockWxStickShopCartService;
    @Mock
    private WxStoreTableStickService mockWxStoreTableStickService;
    @Mock
    private RedisUtils mockRedisUtils;
    @Mock
    private DynamicHelper mockDynamicHelper;

    private WxStickModelOrderServiceImpl wxStickModelOrderServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        wxStickModelOrderServiceImplUnderTest = new WxStickModelOrderServiceImpl();
        wxStickModelOrderServiceImplUnderTest.wxStickOrderClientService = mockWxStickOrderClientService;
        wxStickModelOrderServiceImplUnderTest.wxStickModelClientService = mockWxStickModelClientService;
        wxStickModelOrderServiceImplUnderTest.wxStickShopCartService = mockWxStickShopCartService;
        wxStickModelOrderServiceImplUnderTest.wxStoreTableStickService = mockWxStoreTableStickService;
        wxStickModelOrderServiceImplUnderTest.redisUtils = mockRedisUtils;
        wxStickModelOrderServiceImplUnderTest.dynamicHelper = mockDynamicHelper;
    }

    @Test
    public void testOrder() {
        // Setup
        final WxStickModelOrderDTO wxStickModelOrderDTO = new WxStickModelOrderDTO("enterpriseGuid",
                Arrays.asList("value"), 0, "terminal", "payType", "staffAccount", "staffGuid", "staffName",
                "lastPayGuid");
        final WxStickOrderRespDTO expectedResult = new WxStickOrderRespDTO();
        expectedResult.setCode("code");
        expectedResult.setMsg("msg");
        expectedResult.setPayGuid("payGuid");
        expectedResult.setPaymentGuid("paymentGuid");
        expectedResult.setCodeUrl("codeUrl");
        expectedResult.setPaySt("paySt");
        expectedResult.setBuyStatus(0);
        expectedResult.setGuidList(Arrays.asList("value"));

        when(mockWxStoreTableStickService.checkIsBought(Arrays.asList("value"))).thenReturn(true);

        // Configure WxStickOrderClientService.order(...).
        final WxStickJHPayRespDTO wxStickJHPayRespDTO = new WxStickJHPayRespDTO("attachData", "code", "msg", "result");
        when(mockWxStickOrderClientService.order(
                new WxStickModelOrderDTO("enterpriseGuid", Arrays.asList("value"), 0, "terminal", "payType",
                        "staffAccount", "staffGuid", "staffName", "lastPayGuid"))).thenReturn(wxStickJHPayRespDTO);

        // Configure WxStickModelClientService.getTableStickList(...).
        final List<WxTableStickDTO> wxTableStickDTOS = Arrays.asList(
                new WxTableStickDTO("5b830c03-ee9e-4c33-b1a9-4813fa121b6a", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow",
                        "tableNumberText", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid",
                        "isEnable", "isDelete", 0L, 0L, "categoryName", "previewImg", 0));
        when(mockWxStickModelClientService.getTableStickList(
                new WxStickModelReqDTO(Arrays.asList("value"), 0, "category"))).thenReturn(wxTableStickDTOS);

        // Configure WxStickOrderClientService.polling(...).
        final ShortMsgPollingRespDTO shortMsgPollingRespDTO = new ShortMsgPollingRespDTO();
        shortMsgPollingRespDTO.setCode("code");
        shortMsgPollingRespDTO.setMsg("msg");
        shortMsgPollingRespDTO.setBody("body");
        shortMsgPollingRespDTO.setPaySt("paySt");
        shortMsgPollingRespDTO.setCodeUrl("codeUrl");
        final WxStickOrderRespDTO wxStickOrderRespDTO = new WxStickOrderRespDTO();
        wxStickOrderRespDTO.setCode("code");
        wxStickOrderRespDTO.setMsg("msg");
        wxStickOrderRespDTO.setPayGuid("payGuid");
        wxStickOrderRespDTO.setPaymentGuid("paymentGuid");
        wxStickOrderRespDTO.setCodeUrl("codeUrl");
        wxStickOrderRespDTO.setPaySt("paySt");
        wxStickOrderRespDTO.setBuyStatus(0);
        wxStickOrderRespDTO.setGuidList(Arrays.asList("value"));
        when(mockWxStickOrderClientService.polling(wxStickOrderRespDTO)).thenReturn(shortMsgPollingRespDTO);

        // Run the test
        final WxStickOrderRespDTO result = wxStickModelOrderServiceImplUnderTest.order(wxStickModelOrderDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreTableStickService).saveOrUpdateModels(Arrays.asList(
                new WxTableStickDTO("5b830c03-ee9e-4c33-b1a9-4813fa121b6a", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow",
                        "tableNumberText", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid",
                        "isEnable", "isDelete", 0L, 0L, "categoryName", "previewImg", 0)));
        verify(mockWxStickShopCartService).removeModels(new WxStickShopCartRemoveDTO(Arrays.asList("value"), 0));
        verify(mockDynamicHelper).changeDatasource("enterpriseGuid");
    }

    @Test(expected = BusinessException.class)
    public void testOrder_WxStoreTableStickServiceCheckIsBoughtReturnsFalse() {
        // Setup
        final WxStickModelOrderDTO wxStickModelOrderDTO = new WxStickModelOrderDTO("enterpriseGuid",
                Arrays.asList("value"), 0, "terminal", "payType", "staffAccount", "staffGuid", "staffName",
                "lastPayGuid");
        when(mockWxStoreTableStickService.checkIsBought(Arrays.asList("value"))).thenReturn(false);

        // Run the test
        wxStickModelOrderServiceImplUnderTest.order(wxStickModelOrderDTO);
    }

    @Test
    public void testOrder_WxStickModelClientServiceReturnsNoItems() {
        // Setup
        final WxStickModelOrderDTO wxStickModelOrderDTO = new WxStickModelOrderDTO("enterpriseGuid",
                Arrays.asList("value"), 0, "terminal", "payType", "staffAccount", "staffGuid", "staffName",
                "lastPayGuid");
        final WxStickOrderRespDTO expectedResult = new WxStickOrderRespDTO();
        expectedResult.setCode("code");
        expectedResult.setMsg("msg");
        expectedResult.setPayGuid("payGuid");
        expectedResult.setPaymentGuid("paymentGuid");
        expectedResult.setCodeUrl("codeUrl");
        expectedResult.setPaySt("paySt");
        expectedResult.setBuyStatus(0);
        expectedResult.setGuidList(Arrays.asList("value"));

        when(mockWxStoreTableStickService.checkIsBought(Arrays.asList("value"))).thenReturn(true);

        // Configure WxStickOrderClientService.order(...).
        final WxStickJHPayRespDTO wxStickJHPayRespDTO = new WxStickJHPayRespDTO("attachData", "code", "msg", "result");
        when(mockWxStickOrderClientService.order(
                new WxStickModelOrderDTO("enterpriseGuid", Arrays.asList("value"), 0, "terminal", "payType",
                        "staffAccount", "staffGuid", "staffName", "lastPayGuid"))).thenReturn(wxStickJHPayRespDTO);

        when(mockWxStickModelClientService.getTableStickList(
                new WxStickModelReqDTO(Arrays.asList("value"), 0, "category"))).thenReturn(Collections.emptyList());

        // Run the test
        final WxStickOrderRespDTO result = wxStickModelOrderServiceImplUnderTest.order(wxStickModelOrderDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockWxStoreTableStickService).saveOrUpdateModels(Arrays.asList(
                new WxTableStickDTO("5b830c03-ee9e-4c33-b1a9-4813fa121b6a", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow",
                        "tableNumberText", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid",
                        "isEnable", "isDelete", 0L, 0L, "categoryName", "previewImg", 0)));
        verify(mockWxStickShopCartService).removeModels(new WxStickShopCartRemoveDTO(Arrays.asList("value"), 0));
    }

    @Test
    public void testPolling() {
        // Setup
        final WxStickOrderRespDTO wxStickOrderRespDTO = new WxStickOrderRespDTO();
        wxStickOrderRespDTO.setCode("code");
        wxStickOrderRespDTO.setMsg("msg");
        wxStickOrderRespDTO.setPayGuid("payGuid");
        wxStickOrderRespDTO.setPaymentGuid("paymentGuid");
        wxStickOrderRespDTO.setCodeUrl("codeUrl");
        wxStickOrderRespDTO.setPaySt("paySt");
        wxStickOrderRespDTO.setBuyStatus(0);
        wxStickOrderRespDTO.setGuidList(Arrays.asList("value"));

        final WxStickOrderRespDTO expectedResult = new WxStickOrderRespDTO();
        expectedResult.setCode("code");
        expectedResult.setMsg("msg");
        expectedResult.setPayGuid("payGuid");
        expectedResult.setPaymentGuid("paymentGuid");
        expectedResult.setCodeUrl("codeUrl");
        expectedResult.setPaySt("paySt");
        expectedResult.setBuyStatus(0);
        expectedResult.setGuidList(Arrays.asList("value"));

        // Configure WxStickOrderClientService.polling(...).
        final ShortMsgPollingRespDTO shortMsgPollingRespDTO = new ShortMsgPollingRespDTO();
        shortMsgPollingRespDTO.setCode("code");
        shortMsgPollingRespDTO.setMsg("msg");
        shortMsgPollingRespDTO.setBody("body");
        shortMsgPollingRespDTO.setPaySt("paySt");
        shortMsgPollingRespDTO.setCodeUrl("codeUrl");
        final WxStickOrderRespDTO wxStickOrderRespDTO1 = new WxStickOrderRespDTO();
        wxStickOrderRespDTO1.setCode("code");
        wxStickOrderRespDTO1.setMsg("msg");
        wxStickOrderRespDTO1.setPayGuid("payGuid");
        wxStickOrderRespDTO1.setPaymentGuid("paymentGuid");
        wxStickOrderRespDTO1.setCodeUrl("codeUrl");
        wxStickOrderRespDTO1.setPaySt("paySt");
        wxStickOrderRespDTO1.setBuyStatus(0);
        wxStickOrderRespDTO1.setGuidList(Arrays.asList("value"));
        when(mockWxStickOrderClientService.polling(wxStickOrderRespDTO1)).thenReturn(shortMsgPollingRespDTO);

        // Run the test
        final WxStickOrderRespDTO result = wxStickModelOrderServiceImplUnderTest.polling(wxStickOrderRespDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testPolling_WxStickOrderClientServiceReturnsNull() {
        // Setup
        final WxStickOrderRespDTO wxStickOrderRespDTO = new WxStickOrderRespDTO();
        wxStickOrderRespDTO.setCode("code");
        wxStickOrderRespDTO.setMsg("msg");
        wxStickOrderRespDTO.setPayGuid("payGuid");
        wxStickOrderRespDTO.setPaymentGuid("paymentGuid");
        wxStickOrderRespDTO.setCodeUrl("codeUrl");
        wxStickOrderRespDTO.setPaySt("paySt");
        wxStickOrderRespDTO.setBuyStatus(0);
        wxStickOrderRespDTO.setGuidList(Arrays.asList("value"));

        // Configure WxStickOrderClientService.polling(...).
        final WxStickOrderRespDTO wxStickOrderRespDTO1 = new WxStickOrderRespDTO();
        wxStickOrderRespDTO1.setCode("code");
        wxStickOrderRespDTO1.setMsg("msg");
        wxStickOrderRespDTO1.setPayGuid("payGuid");
        wxStickOrderRespDTO1.setPaymentGuid("paymentGuid");
        wxStickOrderRespDTO1.setCodeUrl("codeUrl");
        wxStickOrderRespDTO1.setPaySt("paySt");
        wxStickOrderRespDTO1.setBuyStatus(0);
        wxStickOrderRespDTO1.setGuidList(Arrays.asList("value"));
        when(mockWxStickOrderClientService.polling(wxStickOrderRespDTO1)).thenReturn(null);

        // Run the test
        final WxStickOrderRespDTO result = wxStickModelOrderServiceImplUnderTest.polling(wxStickOrderRespDTO);

        // Verify the results
        assertNull(result);
    }

    @Test
    public void testCallBack() {
        // Setup
        final WxStickOrderCallBackDTO wxStickOrderCallBackDTO = new WxStickOrderCallBackDTO("enterpriseGuid", "payGuid",
                "paySt", Arrays.asList("value"));

        // Configure WxStickModelClientService.getTableStickList(...).
        final List<WxTableStickDTO> wxTableStickDTOS = Arrays.asList(
                new WxTableStickDTO("5b830c03-ee9e-4c33-b1a9-4813fa121b6a", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow",
                        "tableNumberText", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid",
                        "isEnable", "isDelete", 0L, 0L, "categoryName", "previewImg", 0));
        when(mockWxStickModelClientService.getTableStickList(
                new WxStickModelReqDTO(Arrays.asList("value"), 0, "category"))).thenReturn(wxTableStickDTOS);

        // Run the test
        final String result = wxStickModelOrderServiceImplUnderTest.callBack(wxStickOrderCallBackDTO);

        // Verify the results
        assertEquals("SUCCESS", result);
        verify(mockWxStickShopCartService).removeModels(new WxStickShopCartRemoveDTO(Arrays.asList("value"), 0));
        verify(mockWxStoreTableStickService).saveOrUpdateModels(Arrays.asList(
                new WxTableStickDTO("5b830c03-ee9e-4c33-b1a9-4813fa121b6a", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow",
                        "tableNumberText", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid",
                        "isEnable", "isDelete", 0L, 0L, "categoryName", "previewImg", 0)));
    }

    @Test
    public void testCallBack_WxStickModelClientServiceReturnsNoItems() {
        // Setup
        final WxStickOrderCallBackDTO wxStickOrderCallBackDTO = new WxStickOrderCallBackDTO("enterpriseGuid", "payGuid",
                "paySt", Arrays.asList("value"));
        when(mockWxStickModelClientService.getTableStickList(
                new WxStickModelReqDTO(Arrays.asList("value"), 0, "category"))).thenReturn(Collections.emptyList());

        // Run the test
        final String result = wxStickModelOrderServiceImplUnderTest.callBack(wxStickOrderCallBackDTO);

        // Verify the results
        assertEquals("SUCCESS", result);
        verify(mockWxStickShopCartService).removeModels(new WxStickShopCartRemoveDTO(Arrays.asList("value"), 0));
        verify(mockWxStoreTableStickService).saveOrUpdateModels(Arrays.asList(
                new WxTableStickDTO("5b830c03-ee9e-4c33-b1a9-4813fa121b6a", "category", new BigDecimal("0.00"),
                        new BigDecimal("0.00"), "isRecommended", "name", "backColor", 0.0, 0.0, "unit", "bgUrl",
                        "backImage", "backStrategy", "elementImage", "elementLogo", "logoSwitch", 0, "elementShape",
                        "storeNameText", "storeNameTextColor", "storeDescText", "storeDescTextColor", "qrCodeText",
                        "qrCodeTextColor", "wifiText", "wifiTextColor", "wifiPassword", "wifiPasswordColor",
                        "sponsorsTextColor", "sponsorsText", "areaShow", "areaText", "areaTextColor", "tableNumberShow",
                        "tableNumberText", "tableNumberTextColor", "qrCode", "createStaffGuid", "updateStaffGuid",
                        "isEnable", "isDelete", 0L, 0L, "categoryName", "previewImg", 0)));
    }
}
