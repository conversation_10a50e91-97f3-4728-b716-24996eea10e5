package com.holderzone.saas.store.weixin.service.impl;

import com.holderzone.holder.saas.member.terminal.dto.order.ResponseIntegralOffset;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.OrderFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnItemDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreTradeOrderDetailsDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreTradeDetailsGroupDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreTradeOrderDetailsRespDTO;
import com.holderzone.saas.store.weixin.service.WxOrderRecordService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreDineInOrderClientService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreTableClientService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxStorePersonOrderDetailsServiceImplTest {

    @Mock
    private WxStoreDineInOrderClientService mockWxStoreDineInOrderClientService;
    @Mock
    private WxStoreTableClientService mockWxStoreTableClientService;
    @Mock
    private WxOrderRecordService mockWxOrderRecordService;

    private WxStorePersonOrderDetailsServiceImpl wxStorePersonOrderDetailsServiceImplUnderTest;

    @Before
    public void setUp() {
        wxStorePersonOrderDetailsServiceImplUnderTest = new WxStorePersonOrderDetailsServiceImpl(
                mockWxStoreDineInOrderClientService, mockWxStoreTableClientService, mockWxOrderRecordService);
    }

    @Test
    public void testGetPersonOrderDetails() {
        // Setup
        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("orderGuid")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .storeName("storeName")
                        .diningTableGuid("diningTableGuid")
                        .tableCode("tableCode")
                        .areaName("areaName")
                        .brandName("brandName")
                        .build())
                .build();
        final WxStoreTradeOrderDetailsRespDTO expectedResult = WxStoreTradeOrderDetailsRespDTO.builder()
                .wxStoreTradeDetailsGroupDTOS(Arrays.asList(WxStoreTradeDetailsGroupDTO.builder()
                        .tableCode("tableCode")
                        .areaName("areaName")
                        .wxStoreTradeOrderDetailsDTOS(Arrays.asList(new WxStoreTradeOrderDetailsDTO()))
                        .build()))
                .orderNo("orderNo")
                .mark("mark")
                .tableCode("tableCode")
                .tradeMode(0)
                .totalPrice(new BigDecimal("0.00"))
                .orderFee(new BigDecimal("0.00"))
                .changeFee(new BigDecimal("0.00"))
                .discountFee(new BigDecimal("0.00"))
                .orderFeeDetailDTOS(Arrays.asList(new OrderFeeDetailDTO()))
                .actuallyPayFeeDetailDTOS(Arrays.asList(new ActuallyPayFeeDetailDTO()))
                .discountFeeDetailDTOS(Arrays.asList(new DiscountFeeDetailDTO()))
                .gmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .checkoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .guestCount(0)
                .orderState(0)
                .areaName("areaName")
                .storeName("storeName")
                .brandName("brandName")
                .payWay("payWay")
                .appendFee(new BigDecimal("0.00"))
                .payableAmount(new BigDecimal("0.00"))
                .totalConsumption(new BigDecimal("0.00"))
                .actuallyPayFee(new BigDecimal("0.00"))
                .cardIntegral(0)
                .integralFee(new BigDecimal("0.00"))
                .build();

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("f0f87808-a394-4e4d-8734-2847901c59c3");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMark("mark");
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("diningTableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        dineinOrderDetailRespDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("orderGuid")
                .build())).thenReturn(dineinOrderDetailRespDTO);

        // Configure WxOrderRecordService.cancelFastOrder(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("f0f87808-a394-4e4d-8734-2847901c59c3");
        dineinOrderDetailRespDTO1.setUpperState(0);
        dineinOrderDetailRespDTO1.setMainOrderGuid("orderGuid");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO1.setOrderFeeDetailDTO(orderFeeDetailDTO1);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO1.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        dineinOrderDetailRespDTO1.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        dineinOrderDetailRespDTO1.setState(0);
        dineinOrderDetailRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setMark("mark");
        dineinOrderDetailRespDTO1.setRemark("remark");
        dineinOrderDetailRespDTO1.setDiningTableGuid("diningTableGuid");
        dineinOrderDetailRespDTO1.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO1.setGuestCount(0);
        final ReturnItemDTO returnItemDTO1 = new ReturnItemDTO();
        dineinOrderDetailRespDTO1.setReturnItemDTOS(Arrays.asList(returnItemDTO1));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        dineinOrderDetailRespDTO1.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        final ResponseIntegralOffset integralOffsetResultRespDTO1 = new ResponseIntegralOffset();
        integralOffsetResultRespDTO1.setUseIntegral(0);
        integralOffsetResultRespDTO1.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO1);
        when(mockWxOrderRecordService.cancelFastOrder(dineinOrderDetailRespDTO1, WxStoreConsumerDTO.builder()
                .storeName("storeName")
                .diningTableGuid("diningTableGuid")
                .tableCode("tableCode")
                .areaName("areaName")
                .brandName("brandName")
                .build())).thenReturn(false);

        // Configure WxStoreTableClientService.getTableByGuid(...).
        final TableDTO tableDTO = TableDTO.builder()
                .areaName("areaName")
                .build();
        when(mockWxStoreTableClientService.getTableByGuid("diningTableGuid")).thenReturn(tableDTO);

        // Run the test
        final WxStoreTradeOrderDetailsRespDTO result = wxStorePersonOrderDetailsServiceImplUnderTest.getPersonOrderDetails(
                wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetWxStoreTradeOrderDetails() {
        // Setup
        final DineinOrderDetailRespDTO mainOrderDetails = new DineinOrderDetailRespDTO();
        mainOrderDetails.setGuid("f0f87808-a394-4e4d-8734-2847901c59c3");
        mainOrderDetails.setUpperState(0);
        mainOrderDetails.setMainOrderGuid("orderGuid");
        mainOrderDetails.setTradeMode(0);
        mainOrderDetails.setOrderFee(new BigDecimal("0.00"));
        mainOrderDetails.setAppendFee(new BigDecimal("0.00"));
        mainOrderDetails.setActuallyPayFee(new BigDecimal("0.00"));
        mainOrderDetails.setChangeFee(new BigDecimal("0.00"));
        mainOrderDetails.setDiscountFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        mainOrderDetails.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        mainOrderDetails.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        mainOrderDetails.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        mainOrderDetails.setOrderNo("orderNo");
        mainOrderDetails.setState(0);
        mainOrderDetails.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mainOrderDetails.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mainOrderDetails.setMark("mark");
        mainOrderDetails.setRemark("remark");
        mainOrderDetails.setDiningTableGuid("diningTableGuid");
        mainOrderDetails.setDiningTableName("tableCode");
        mainOrderDetails.setGuestCount(0);
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        mainOrderDetails.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        mainOrderDetails.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        mainOrderDetails.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        mainOrderDetails.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);

        final WxStoreTradeOrderDetailsDTO expectedResult = new WxStoreTradeOrderDetailsDTO();
        expectedResult.setGuid("f0f87808-a394-4e4d-8734-2847901c59c3");
        expectedResult.setTableGuid("diningTableGuid");
        expectedResult.setOrderNo("orderNo");
        expectedResult.setMark("mark");
        expectedResult.setRemark("remark");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        expectedResult.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        final ReturnItemDTO returnItemDTO1 = new ReturnItemDTO();
        expectedResult.setReturnItemDTOS(Arrays.asList(returnItemDTO1));
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setTotalPrice(new BigDecimal("0.00"));
        expectedResult.setOrderModel(0);
        expectedResult.setState(0);
        expectedResult.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("orderGuid")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .storeName("storeName")
                        .diningTableGuid("diningTableGuid")
                        .tableCode("tableCode")
                        .areaName("areaName")
                        .brandName("brandName")
                        .build())
                .build());

        // Run the test
        final WxStoreTradeOrderDetailsDTO result = wxStorePersonOrderDetailsServiceImplUnderTest.getWxStoreTradeOrderDetails(
                mainOrderDetails);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetWxStoreTradeOrderDetailsResp() {
        // Setup
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("f0f87808-a394-4e4d-8734-2847901c59c3");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMark("mark");
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("diningTableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        dineinOrderDetailRespDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);

        final WxStoreTradeOrderDetailsDTO wxStoreTradeOrderDetailsDTO = new WxStoreTradeOrderDetailsDTO();
        wxStoreTradeOrderDetailsDTO.setGuid("f0f87808-a394-4e4d-8734-2847901c59c3");
        wxStoreTradeOrderDetailsDTO.setTableGuid("diningTableGuid");
        wxStoreTradeOrderDetailsDTO.setOrderNo("orderNo");
        wxStoreTradeOrderDetailsDTO.setMark("mark");
        wxStoreTradeOrderDetailsDTO.setRemark("remark");
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        wxStoreTradeOrderDetailsDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        final ReturnItemDTO returnItemDTO1 = new ReturnItemDTO();
        wxStoreTradeOrderDetailsDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO1));
        wxStoreTradeOrderDetailsDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreTradeOrderDetailsDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        wxStoreTradeOrderDetailsDTO.setTotalPrice(new BigDecimal("0.00"));
        wxStoreTradeOrderDetailsDTO.setOrderModel(0);
        wxStoreTradeOrderDetailsDTO.setState(0);
        wxStoreTradeOrderDetailsDTO.setWxStoreAdvanceConsumerReqDTO(WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("orderGuid")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .storeName("storeName")
                        .diningTableGuid("diningTableGuid")
                        .tableCode("tableCode")
                        .areaName("areaName")
                        .brandName("brandName")
                        .build())
                .build());

        final WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO = WxStoreAdvanceConsumerReqDTO.builder()
                .tradeOrderGuid("orderGuid")
                .wxStoreConsumerDTO(WxStoreConsumerDTO.builder()
                        .storeName("storeName")
                        .diningTableGuid("diningTableGuid")
                        .tableCode("tableCode")
                        .areaName("areaName")
                        .brandName("brandName")
                        .build())
                .build();
        final WxStoreTradeOrderDetailsRespDTO expectedResult = WxStoreTradeOrderDetailsRespDTO.builder()
                .wxStoreTradeDetailsGroupDTOS(Arrays.asList(WxStoreTradeDetailsGroupDTO.builder()
                        .tableCode("tableCode")
                        .areaName("areaName")
                        .wxStoreTradeOrderDetailsDTOS(Arrays.asList(new WxStoreTradeOrderDetailsDTO()))
                        .build()))
                .orderNo("orderNo")
                .mark("mark")
                .tableCode("tableCode")
                .tradeMode(0)
                .totalPrice(new BigDecimal("0.00"))
                .orderFee(new BigDecimal("0.00"))
                .changeFee(new BigDecimal("0.00"))
                .discountFee(new BigDecimal("0.00"))
                .orderFeeDetailDTOS(Arrays.asList(new OrderFeeDetailDTO()))
                .actuallyPayFeeDetailDTOS(Arrays.asList(new ActuallyPayFeeDetailDTO()))
                .discountFeeDetailDTOS(Arrays.asList(new DiscountFeeDetailDTO()))
                .gmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .checkoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .guestCount(0)
                .orderState(0)
                .areaName("areaName")
                .storeName("storeName")
                .brandName("brandName")
                .payWay("payWay")
                .appendFee(new BigDecimal("0.00"))
                .payableAmount(new BigDecimal("0.00"))
                .totalConsumption(new BigDecimal("0.00"))
                .actuallyPayFee(new BigDecimal("0.00"))
                .cardIntegral(0)
                .integralFee(new BigDecimal("0.00"))
                .build();

        // Configure WxOrderRecordService.cancelFastOrder(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("f0f87808-a394-4e4d-8734-2847901c59c3");
        dineinOrderDetailRespDTO1.setUpperState(0);
        dineinOrderDetailRespDTO1.setMainOrderGuid("orderGuid");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO1.setOrderFeeDetailDTO(orderFeeDetailDTO1);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO1.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        dineinOrderDetailRespDTO1.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        dineinOrderDetailRespDTO1.setState(0);
        dineinOrderDetailRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setMark("mark");
        dineinOrderDetailRespDTO1.setRemark("remark");
        dineinOrderDetailRespDTO1.setDiningTableGuid("diningTableGuid");
        dineinOrderDetailRespDTO1.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO1.setGuestCount(0);
        final ReturnItemDTO returnItemDTO2 = new ReturnItemDTO();
        dineinOrderDetailRespDTO1.setReturnItemDTOS(Arrays.asList(returnItemDTO2));
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        dineinOrderDetailRespDTO1.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        final ResponseIntegralOffset integralOffsetResultRespDTO1 = new ResponseIntegralOffset();
        integralOffsetResultRespDTO1.setUseIntegral(0);
        integralOffsetResultRespDTO1.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO1);
        when(mockWxOrderRecordService.cancelFastOrder(dineinOrderDetailRespDTO1, WxStoreConsumerDTO.builder()
                .storeName("storeName")
                .diningTableGuid("diningTableGuid")
                .tableCode("tableCode")
                .areaName("areaName")
                .brandName("brandName")
                .build())).thenReturn(false);

        // Configure WxStoreTableClientService.getTableByGuid(...).
        final TableDTO tableDTO = TableDTO.builder()
                .areaName("areaName")
                .build();
        when(mockWxStoreTableClientService.getTableByGuid("diningTableGuid")).thenReturn(tableDTO);

        // Run the test
        final WxStoreTradeOrderDetailsRespDTO result = wxStorePersonOrderDetailsServiceImplUnderTest.getWxStoreTradeOrderDetailsResp(
                dineinOrderDetailRespDTO, wxStoreTradeOrderDetailsDTO, wxStoreAdvanceConsumerReqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetMainOrderDetails() {
        // Setup
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO.setGuid("f0f87808-a394-4e4d-8734-2847901c59c3");
        dineinOrderDetailRespDTO.setUpperState(0);
        dineinOrderDetailRespDTO.setMainOrderGuid("orderGuid");
        dineinOrderDetailRespDTO.setTradeMode(0);
        dineinOrderDetailRespDTO.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setDiscountFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO.setOrderFeeDetailDTO(orderFeeDetailDTO);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO));
        final DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        dineinOrderDetailRespDTO.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO));
        dineinOrderDetailRespDTO.setOrderNo("orderNo");
        dineinOrderDetailRespDTO.setState(0);
        dineinOrderDetailRespDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO.setMark("mark");
        dineinOrderDetailRespDTO.setRemark("remark");
        dineinOrderDetailRespDTO.setDiningTableGuid("diningTableGuid");
        dineinOrderDetailRespDTO.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO.setGuestCount(0);
        final ReturnItemDTO returnItemDTO = new ReturnItemDTO();
        dineinOrderDetailRespDTO.setReturnItemDTOS(Arrays.asList(returnItemDTO));
        final DineInItemDTO dineInItemDTO = new DineInItemDTO();
        dineinOrderDetailRespDTO.setDineInItemDTOS(Arrays.asList(dineInItemDTO));
        dineinOrderDetailRespDTO.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        final ResponseIntegralOffset integralOffsetResultRespDTO = new ResponseIntegralOffset();
        integralOffsetResultRespDTO.setUseIntegral(0);
        integralOffsetResultRespDTO.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO);

        final DineinOrderDetailRespDTO expectedResult = new DineinOrderDetailRespDTO();
        expectedResult.setGuid("f0f87808-a394-4e4d-8734-2847901c59c3");
        expectedResult.setUpperState(0);
        expectedResult.setMainOrderGuid("orderGuid");
        expectedResult.setTradeMode(0);
        expectedResult.setOrderFee(new BigDecimal("0.00"));
        expectedResult.setAppendFee(new BigDecimal("0.00"));
        expectedResult.setActuallyPayFee(new BigDecimal("0.00"));
        expectedResult.setChangeFee(new BigDecimal("0.00"));
        expectedResult.setDiscountFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO1 = new OrderFeeDetailDTO();
        expectedResult.setOrderFeeDetailDTO(orderFeeDetailDTO1);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO1 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO1.setPaymentTypeName("payWay");
        expectedResult.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO1));
        final DiscountFeeDetailDTO discountFeeDetailDTO1 = new DiscountFeeDetailDTO();
        expectedResult.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO1));
        expectedResult.setOrderNo("orderNo");
        expectedResult.setState(0);
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setMark("mark");
        expectedResult.setRemark("remark");
        expectedResult.setDiningTableGuid("diningTableGuid");
        expectedResult.setDiningTableName("tableCode");
        expectedResult.setGuestCount(0);
        final ReturnItemDTO returnItemDTO1 = new ReturnItemDTO();
        expectedResult.setReturnItemDTOS(Arrays.asList(returnItemDTO1));
        final DineInItemDTO dineInItemDTO1 = new DineInItemDTO();
        expectedResult.setDineInItemDTOS(Arrays.asList(dineInItemDTO1));
        expectedResult.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        final ResponseIntegralOffset integralOffsetResultRespDTO1 = new ResponseIntegralOffset();
        integralOffsetResultRespDTO1.setUseIntegral(0);
        integralOffsetResultRespDTO1.setDeductionMoney(new BigDecimal("0.00"));
        expectedResult.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO1);

        // Configure WxStoreDineInOrderClientService.getOrderDetail(...).
        final DineinOrderDetailRespDTO dineinOrderDetailRespDTO1 = new DineinOrderDetailRespDTO();
        dineinOrderDetailRespDTO1.setGuid("f0f87808-a394-4e4d-8734-2847901c59c3");
        dineinOrderDetailRespDTO1.setUpperState(0);
        dineinOrderDetailRespDTO1.setMainOrderGuid("orderGuid");
        dineinOrderDetailRespDTO1.setTradeMode(0);
        dineinOrderDetailRespDTO1.setOrderFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setAppendFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setActuallyPayFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setChangeFee(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setDiscountFee(new BigDecimal("0.00"));
        final OrderFeeDetailDTO orderFeeDetailDTO2 = new OrderFeeDetailDTO();
        dineinOrderDetailRespDTO1.setOrderFeeDetailDTO(orderFeeDetailDTO2);
        final ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO2 = new ActuallyPayFeeDetailDTO();
        actuallyPayFeeDetailDTO2.setPaymentTypeName("payWay");
        dineinOrderDetailRespDTO1.setActuallyPayFeeDetailDTOS(Arrays.asList(actuallyPayFeeDetailDTO2));
        final DiscountFeeDetailDTO discountFeeDetailDTO2 = new DiscountFeeDetailDTO();
        dineinOrderDetailRespDTO1.setDiscountFeeDetailDTOS(Arrays.asList(discountFeeDetailDTO2));
        dineinOrderDetailRespDTO1.setOrderNo("orderNo");
        dineinOrderDetailRespDTO1.setState(0);
        dineinOrderDetailRespDTO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        dineinOrderDetailRespDTO1.setMark("mark");
        dineinOrderDetailRespDTO1.setRemark("remark");
        dineinOrderDetailRespDTO1.setDiningTableGuid("diningTableGuid");
        dineinOrderDetailRespDTO1.setDiningTableName("tableCode");
        dineinOrderDetailRespDTO1.setGuestCount(0);
        final ReturnItemDTO returnItemDTO2 = new ReturnItemDTO();
        dineinOrderDetailRespDTO1.setReturnItemDTOS(Arrays.asList(returnItemDTO2));
        final DineInItemDTO dineInItemDTO2 = new DineInItemDTO();
        dineinOrderDetailRespDTO1.setDineInItemDTOS(Arrays.asList(dineInItemDTO2));
        dineinOrderDetailRespDTO1.setSubOrderDetails(Arrays.asList(new DineinOrderDetailRespDTO()));
        final ResponseIntegralOffset integralOffsetResultRespDTO2 = new ResponseIntegralOffset();
        integralOffsetResultRespDTO2.setUseIntegral(0);
        integralOffsetResultRespDTO2.setDeductionMoney(new BigDecimal("0.00"));
        dineinOrderDetailRespDTO1.setIntegralOffsetResultRespDTO(integralOffsetResultRespDTO2);
        when(mockWxStoreDineInOrderClientService.getOrderDetail(SingleDataDTO.builder()
                .data("orderGuid")
                .build())).thenReturn(dineinOrderDetailRespDTO1);

        // Run the test
        final DineinOrderDetailRespDTO result = wxStorePersonOrderDetailsServiceImplUnderTest.getMainOrderDetails(
                dineinOrderDetailRespDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }
}
