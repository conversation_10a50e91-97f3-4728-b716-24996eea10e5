package com.holderzone.saas.store.weixin.controller;

import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreMenuReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxMenuDetailsDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.weixin.service.WxStoreMenuDetailsService;
import com.holderzone.saas.store.weixin.service.WxStoreOrderConfigService;
import com.holderzone.saas.store.weixin.service.rpc.WxStoreMenuItemClientService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(WxStoreMenuDetailsController.class)
public class WxStoreMenuDetailsControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WxStoreMenuItemClientService mockWxStoreMenuItemClientService;
    @MockBean
    private WxStoreMenuDetailsService mockWxStoreMenuDetailsService;
    @MockBean
    private WxStoreOrderConfigService mockWxStoreOrderConfigService;

    @Test
    public void testGetWxMenuDetails() throws Exception {
        // Setup
        when(mockWxStoreMenuDetailsService.getWxMenuDetails(new WxStoreMenuReqDTO("enterpriseGuid", "storeGuid", 0,
                WxStoreConsumerDTO.builder().build()))).thenReturn(WxMenuDetailsDTO.builder().build());

        // Run the test and verify the results
        mockMvc.perform(post("/wx-store-menu-provide/details")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testTest() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/wx-store-menu-provide/test")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetwxOrderConfig() throws Exception {
        // Setup
        // Configure WxStoreMenuDetailsService.getStoreConfiguration(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("3dfdb8e6-8906-472b-af20-747a8fda5d33");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreMenuDetailsService.getStoreConfiguration(WxStoreConsumerDTO.builder().build()))
                .thenReturn(wxOrderConfigDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/wx-store-menu-provide/configuration")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetConsumerInfo() throws Exception {
        // Setup
        when(mockWxStoreMenuDetailsService.getConsumerInfo(WxPortalReqDTO.builder().build()))
                .thenReturn(WxStoreConsumerDTO.builder().build());

        // Run the test and verify the results
        mockMvc.perform(post("/wx-store-menu-provide/get_consumer_info")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetStoreConfig() throws Exception {
        // Setup
        // Configure WxStoreOrderConfigService.getDetailConfig(...).
        final WxOrderConfigDTO wxOrderConfigDTO = new WxOrderConfigDTO();
        wxOrderConfigDTO.setGuid("3dfdb8e6-8906-472b-af20-747a8fda5d33");
        wxOrderConfigDTO.setIsOrderOpen(0);
        wxOrderConfigDTO.setOrderModel(0);
        wxOrderConfigDTO.setTakingModel(0);
        wxOrderConfigDTO.setIsOnlinePayed(0);
        when(mockWxStoreOrderConfigService.getDetailConfig(WxStoreReqDTO.builder()
                .storeGuid("storeGuid")
                .build())).thenReturn(wxOrderConfigDTO);

        // Run the test and verify the results
        mockMvc.perform(get("/wx-store-menu-provide/store_config")
                        .param("storeGuid", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
